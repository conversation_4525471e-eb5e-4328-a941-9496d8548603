package com.youibot.tms.biz.service.impl;

import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.enums.AgvUsageStatus;
import com.youibot.tms.biz.forest.dto.fleet5.RunningStatus;
import com.youibot.tms.biz.forest.dto.fleet5.TaskDetailApiDTO;
import com.youibot.tms.biz.forest.enums.VehicleStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Method;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Park任务Hash稳定性测试
 * 验证Park任务从IDLE变为RUN时，hash值应该保持不变
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@SpringBootTest
@ActiveProfiles("test")
class ParkTaskHashStabilityTest {

    @Test
    @DisplayName("测试Park任务IDLE到RUN状态hash稳定性")
    void testParkTask_IdleToRunHashStability() throws Exception {
        // 创建AgvStatusProcessingServiceImpl实例
        AgvStatusProcessingServiceImpl service = new AgvStatusProcessingServiceImpl();
        
        // 场景1：Park任务，AGV处于IDLE状态
        AgvDTO agvIdle = createParkTaskAgv("1", VehicleStatus.IDLE, "Free", "Auto", "Normal");
        
        // 场景2：Park任务，AGV处于RUN状态，工作状态变为Work
        AgvDTO agvRun = createParkTaskAgv("1", VehicleStatus.RUN, "Work", "Auto", "Normal");

        // 使用反射调用私有方法
        Method calculateHashMethod = AgvStatusProcessingServiceImpl.class.getDeclaredMethod(
            "calculateAgvStatusHash", String.class, java.util.List.class);
        calculateHashMethod.setAccessible(true);
        
        System.out.println("=== Park任务IDLE状态Hash计算 ===");
        String idleHash = (String) calculateHashMethod.invoke(service, "PARK_TEST", Arrays.asList(agvIdle));
        
        System.out.println("\n=== Park任务RUN状态Hash计算 ===");
        String runHash = (String) calculateHashMethod.invoke(service, "PARK_TEST", Arrays.asList(agvRun));
        
        // 验证hash值相同
        assertEquals(idleHash, runHash, "Park任务从IDLE变为RUN时，hash值应该保持不变");
        
        System.out.println("IDLE状态Hash: " + idleHash);
        System.out.println("RUN状态Hash: " + runHash);
        System.out.println("Hash相同: " + idleHash.equals(runHash));
        System.out.println("\n预期日志内容:");
        System.out.println("- [INFO] Fleet状态: IDLE -> RUN");
        System.out.println("- [INFO] 工作状态: Free -> Work (Park任务固定为Park_Idle)");
        System.out.println("- S66F1状态都应该是1（完全待机，无在荷）");
        System.out.println("- Hash对比结果: 相同，不会触发S66F1发送");
    }

    @Test
    @DisplayName("测试Park任务多个运行状态字段变化的hash稳定性")
    void testParkTask_MultipleRunningStatusChangesHashStability() throws Exception {
        // 创建AgvStatusProcessingServiceImpl实例
        AgvStatusProcessingServiceImpl service = new AgvStatusProcessingServiceImpl();
        
        // 场景1：Park任务，所有运行状态都是初始值
        AgvDTO agv1 = createParkTaskAgv("1", VehicleStatus.IDLE, "Free", "Auto", "Normal");
        
        // 场景2：Park任务，Fleet状态变为RUN，所有运行状态都变化
        AgvDTO agv2 = createParkTaskAgv("1", VehicleStatus.RUN, "Work", "Manual", "Abnormal");

        // 使用反射调用私有方法
        Method calculateHashMethod = AgvStatusProcessingServiceImpl.class.getDeclaredMethod(
            "calculateAgvStatusHash", String.class, java.util.List.class);
        calculateHashMethod.setAccessible(true);
        
        System.out.println("=== Park任务初始状态Hash计算 ===");
        String firstHash = (String) calculateHashMethod.invoke(service, "PARK_MULTI", Arrays.asList(agv1));
        
        System.out.println("\n=== Park任务多字段变化Hash计算 ===");
        String secondHash = (String) calculateHashMethod.invoke(service, "PARK_MULTI", Arrays.asList(agv2));
        
        // 验证hash值相同
        assertEquals(firstHash, secondHash, "Park任务多个运行状态字段变化时，hash值应该保持不变");
        
        System.out.println("初始状态Hash: " + firstHash);
        System.out.println("多字段变化Hash: " + secondHash);
        System.out.println("Hash相同: " + firstHash.equals(secondHash));
        System.out.println("\n预期日志内容:");
        System.out.println("- [INFO] Fleet状态: IDLE -> RUN");
        System.out.println("- [INFO] 工作状态: Free -> Work (Park任务固定为Park_Idle)");
        System.out.println("- [INFO] 控制状态: Auto -> Manual (Park任务固定为Park_Idle)");
        System.out.println("- [INFO] 运行异常状态: Normal -> Abnormal (Park任务固定为Park_Idle)");
        System.out.println("- 所有运行状态字段都使用Park_Idle固定值参与hash计算");
    }

    @Test
    @DisplayName("测试Park任务与Api任务的hash对比")
    void testParkTask_VsApiTaskHashComparison() throws Exception {
        // 创建AgvStatusProcessingServiceImpl实例
        AgvStatusProcessingServiceImpl service = new AgvStatusProcessingServiceImpl();
        
        // 场景1：Park任务，RUN状态
        AgvDTO parkAgv = createParkTaskAgv("1", VehicleStatus.RUN, "Work", "Auto", "Normal");
        
        // 场景2：Api任务，RUN状态，相同的运行状态
        AgvDTO apiAgv = createApiTaskAgv("1", VehicleStatus.RUN, "Work", "Auto", "Normal");

        // 使用反射调用私有方法
        Method calculateHashMethod = AgvStatusProcessingServiceImpl.class.getDeclaredMethod(
            "calculateAgvStatusHash", String.class, java.util.List.class);
        calculateHashMethod.setAccessible(true);
        
        System.out.println("=== Park任务RUN状态Hash计算 ===");
        String parkHash = (String) calculateHashMethod.invoke(service, "PARK_VS_API", Arrays.asList(parkAgv));
        
        System.out.println("\n=== Api任务RUN状态Hash计算 ===");
        String apiHash = (String) calculateHashMethod.invoke(service, "PARK_VS_API", Arrays.asList(apiAgv));
        
        // 验证hash值不同
        assertNotEquals(parkHash, apiHash, "Park任务和Api任务的hash值应该不同");
        
        System.out.println("Park任务Hash: " + parkHash);
        System.out.println("Api任务Hash: " + apiHash);
        System.out.println("Hash不同: " + !parkHash.equals(apiHash));
        System.out.println("\n说明:");
        System.out.println("- Park任务: S66F1状态1，运行状态使用Park_Idle固定值");
        System.out.println("- Api任务: S66F1状态4，运行状态使用实际值Work,Auto,Normal");
        System.out.println("- 两者hash值不同，体现了不同的业务逻辑");
    }

    @Test
    @DisplayName("测试Park任务异常通知变化的hash影响")
    void testParkTask_AbnormalNoticeChangeHashImpact() throws Exception {
        // 创建AgvStatusProcessingServiceImpl实例
        AgvStatusProcessingServiceImpl service = new AgvStatusProcessingServiceImpl();
        
        // 场景1：Park任务，正常状态
        AgvDTO parkNormal = createParkTaskAgv("1", VehicleStatus.RUN, "Work", "Auto", "Normal");
        parkNormal.setAbnormalNotice(false);
        
        // 场景2：Park任务，异常通知
        AgvDTO parkAbnormal = createParkTaskAgv("1", VehicleStatus.RUN, "Work", "Auto", "Normal");
        parkAbnormal.setAbnormalNotice(true);

        // 使用反射调用私有方法
        Method calculateHashMethod = AgvStatusProcessingServiceImpl.class.getDeclaredMethod(
            "calculateAgvStatusHash", String.class, java.util.List.class);
        calculateHashMethod.setAccessible(true);
        
        System.out.println("=== Park任务正常状态Hash计算 ===");
        String normalHash = (String) calculateHashMethod.invoke(service, "PARK_ABNORMAL", Arrays.asList(parkNormal));
        
        System.out.println("\n=== Park任务异常通知Hash计算 ===");
        String abnormalHash = (String) calculateHashMethod.invoke(service, "PARK_ABNORMAL", Arrays.asList(parkAbnormal));
        
        // 验证hash值不同（异常通知优先级最高）
        assertNotEquals(normalHash, abnormalHash, "Park任务异常通知变化应该影响hash值");
        
        System.out.println("正常状态Hash: " + normalHash);
        System.out.println("异常通知Hash: " + abnormalHash);
        System.out.println("Hash不同: " + !normalHash.equals(abnormalHash));
        System.out.println("\n说明:");
        System.out.println("- 异常通知优先级最高，即使是Park任务也会影响S66F1状态");
        System.out.println("- 正常状态: S66F1状态1");
        System.out.println("- 异常通知: S66F1状态100");
    }

    // 辅助方法：创建Park任务AGV
    private AgvDTO createParkTaskAgv(String agvCode, VehicleStatus status, String workStatus, String controlStatus, String abnormalStatus) {
        AgvDTO agv = new AgvDTO();
        agv.setAgvCode(agvCode);
        agv.setName("BAY1_" + agvCode);
        agv.setConnectStatus("connect");
        agv.setStatus(status);
        agv.setAbnormalNotice(false);
        agv.setUsageStatus(AgvUsageStatus.FREE);
        
        // 设置Park任务
        TaskDetailApiDTO parkTask = new TaskDetailApiDTO();
        parkTask.setSource("Park");
        parkTask.setTaskNo("T20250803000039");
        agv.setTaskInfo(parkTask);
        
        // 设置运行状态
        RunningStatus runningStatus = new RunningStatus();
        runningStatus.setWorkStatus(workStatus);
        runningStatus.setControlStatus(controlStatus);
        runningStatus.setAbnormalStatus(abnormalStatus);
        agv.setRunningStatus(runningStatus);
        
        return agv;
    }

    // 辅助方法：创建Api任务AGV
    private AgvDTO createApiTaskAgv(String agvCode, VehicleStatus status, String workStatus, String controlStatus, String abnormalStatus) {
        AgvDTO agv = new AgvDTO();
        agv.setAgvCode(agvCode);
        agv.setName("BAY1_" + agvCode);
        agv.setConnectStatus("connect");
        agv.setStatus(status);
        agv.setAbnormalNotice(false);
        agv.setUsageStatus(AgvUsageStatus.FREE);
        
        // 设置Api任务
        TaskDetailApiDTO apiTask = new TaskDetailApiDTO();
        apiTask.setSource("Api");
        apiTask.setTaskNo("API_TASK_001");
        agv.setTaskInfo(apiTask);
        
        // 设置运行状态
        RunningStatus runningStatus = new RunningStatus();
        runningStatus.setWorkStatus(workStatus);
        runningStatus.setControlStatus(controlStatus);
        runningStatus.setAbnormalStatus(abnormalStatus);
        agv.setRunningStatus(runningStatus);
        
        return agv;
    }
}
