<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youibot.tms.biz.mapper.TaskStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youibot.tms.biz.entity.TaskStatistics">
        <id column="id" property="id"/>
        <result column="success_num" property="successNum"/>
        <result column="fail_num" property="failNum"/>
        <result column="total" property="total"/>
        <result column="agv_code" property="agvCode"/>
        <result column="attribution_date" property="attributionDate"/>
        <result column="date_hour" property="dateHour"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, success_num, fail_num, total, agv_code, date_hour,attribution_date, create_time
    </sql>


    <!--  根据获取实体List，根据小时   -->
    <select id="findListByHour" resultMap="BaseResultMap">
        SELECT
        SUM(a.success_num) AS success_num ,
        SUM(a.fail_num) AS fail_num ,
        SUM(a.total) AS total ,
        a.agv_code AS agv_code ,
        CONCAT(date_format(ANY_VALUE(a.create_time),'%Y-%m-%d %H'),':00:00') AS create_time ,
        ANY_VALUE(attribution_date) AS attribution_date ,
        ANY_VALUE(date_hour) AS date_hour
        FROM biz_task_statistics a
        <where>
            <if test="taskStatisticsRequest.agvCode != null  and taskStatisticsRequest.agvCode != ''">AND a.agv_code =
                #{taskStatisticsRequest.agvCode}
            </if>
            <if test="agvCodes != null">
                AND a.agv_code in
                <foreach collection="agvCodes" open="(" separator="," close=")" item="agvCodeItem">
                    #{agvCodeItem}
                </foreach>
            </if>
            <if test="taskStatisticsRequest.beginDate != null and taskStatisticsRequest.beginDate != ''">
                AND date_format(a.date_hour,'%Y-%m-%d %H') <![CDATA[ >= ]]>
                date_format(#{taskStatisticsRequest.beginDate},'%Y-%m-%d %H')
            </if>
            <if test="taskStatisticsRequest.endDate != null and taskStatisticsRequest.endDate != ''">
                AND date_format(a.date_hour,'%Y-%m-%d %H') <![CDATA[ <= ]]>
                date_format(#{taskStatisticsRequest.endDate},'%Y-%m-%d %H')
            </if>
        </where>
        group by date_format(date_hour,'%Y-%m-%d %H'),a.agv_code
    </select>


    <!--  根据获取实体List ，根据天  -->
    <select id="findListByHourToDay" resultMap="BaseResultMap">
        SELECT
        SUM(a.success_num) AS success_num ,
        SUM(a.fail_num) AS fail_num ,
        SUM(a.total) AS total ,
        a.agv_code AS agv_code ,
        date_format(a.create_time,'%Y-%m-%d') AS create_time ,
        attribution_date AS attribution_date ,
        date_hour AS date_hour
        FROM biz_task_statistics a
        <where>
            <if test="taskStatisticsRequest.agvCode != null  and taskStatisticsRequest.agvCode != ''">AND a.agv_code =
                #{taskStatisticsRequest.agvCode}
            </if>
            <if test="agvCodes != null">
                AND a.agv_code in
                <foreach collection="agvCodes" open="(" separator="," close=")" item="agvCodeItem">
                    #{agvCodeItem}
                </foreach>
            </if>
            <if test="taskStatisticsRequest.beginDate != null and taskStatisticsRequest.beginDate != ''">
                AND date_format(a.date_hour,'%Y-%m-%d %H') <![CDATA[ >= ]]>
                date_format(#{taskStatisticsRequest.beginDate},'%Y-%m-%d %H')
            </if>
            <if test="taskStatisticsRequest.endDate != null and taskStatisticsRequest.endDate != ''">
                AND date_format(a.date_hour,'%Y-%m-%d %H') <![CDATA[ <= ]]>
                date_format(#{taskStatisticsRequest.endDate},'%Y-%m-%d %H')
            </if>
        </where>
        group by date_format(attribution_date,'%Y-%m-%d'),a.agv_code
    </select>


    <!--  根据获取实体List ，根据天  -->
    <select id="findListByHourToMonth" resultMap="BaseResultMap">
        SELECT
        SUM(a.success_num) AS success_num ,
        SUM(a.fail_num) AS fail_num ,
        SUM(a.total) AS total ,
        a.agv_code AS agv_code ,
        date_format(a.create_time,'%Y-%m-%d') AS date_format ,
        attribution_date AS attribution_date ,
        date_hour AS date_hour
        FROM biz_task_statistics a
        <where>
            <if test="taskStatisticsRequest.agvCode != null  and taskStatisticsRequest.agvCode != ''">AND a.agv_code =
                #{taskStatisticsRequest.agvCode}
            </if>
            <if test="agvCodes != null">
                AND a.agv_code in
                <foreach collection="agvCodes" open="(" separator="," close=")" item="agvCodeItem">
                    #{agvCodeItem}
                </foreach>
            </if>
            <if test="taskStatisticsRequest.beginDate != null and taskStatisticsRequest.beginDate != ''">
                AND date_format(a.date_hour,'%Y-%m-%d %H') <![CDATA[ >= ]]>
                date_format(#{taskStatisticsRequest.beginDate},'%Y-%m-%d %H')
            </if>
            <if test="taskStatisticsRequest.endDate != null and taskStatisticsRequest.endDate != ''">
                AND date_format(a.date_hour,'%Y-%m-%d %H') <![CDATA[ <= ]]>
                date_format(#{taskStatisticsRequest.endDate},'%Y-%m-%d %H')
            </if>
        </where>
        group by date_format(attribution_date,'%Y-%m'),a.agv_code
    </select>

    <select id="findTaskStatisticsById" resultType="com.youibot.tms.biz.entity.TaskStatistics">
        <!-- 根据联合主键查询数据 page -->
        SELECT
        <include refid="Base_Column_List"/>
        FROM biz_task_statistics a
        where a.agv_code = #{agvCode} and a.date_hour = #{dateHour}
    </select>


    <!-- 根据联合主键查询数据 page -->
    <update id="updateByAgvCodeAndDateHour" parameterType="com.youibot.tms.biz.entity.TaskStatistics">
        update biz_task_statistics set
        <if test="taskStatistics.successNum!=null">
            success_num = #{taskStatistics.successNum},
        </if>
        <if test="taskStatistics.failNum!=null">
            fail_num = #{taskStatistics.failNum},
        </if>
        <if test="taskStatistics.total!=null">
            total = #{taskStatistics.total},
        </if>
        <if test="taskStatistics.attributionDate!=null">
            attribution_date = #{taskStatistics.attributionDate}
        </if>
        where agv_Code=#{taskStatistics.agvCode} and date_hour=#{taskStatistics.dateHour}
    </update>


    <insert id="saveBatchByMultiId" parameterType="java.util.List">
        insert into biz_task_statistics(
        id,
        success_num,
        fail_num,
        total,
        agv_code,
        create_time,
        update_time,
        attribution_date,
        date_hour
        )VALUES
        <foreach collection="list" item="taskStatistics" index="index" separator=",">
            (
            #{taskStatistics.id},
            #{taskStatistics.successNum},
            #{taskStatistics.failNum},
            #{taskStatistics.total},
            #{taskStatistics.agvCode},
            #{taskStatistics.createTime},
            #{taskStatistics.updateTime},
            #{taskStatistics.attributionDate},
            #{taskStatistics.dateHour}
            )
        </foreach>
    </insert>


    <!-- 通过归属日期批量删除 -->
    <delete id="batchDeleteByAttributionDate" parameterType="java.util.List">
        delete from biz_task_statistics
        where attribution_date in
        <foreach collection="list" open="(" separator="," close=")" item="attributionDate">
            #{attributionDate}
        </foreach>
    </delete>

    <select id="today" resultType="com.youibot.tms.biz.entity.TaskStatistics">
        SELECT COUNT(id)                            total,
               SUM(IF(a.status = 'SUCCESS', 1, 0))  successNum,
               SUM(if(a.status != 'SUCCESS', 1, 0)) failNum,
               ANY_VALUE(a.agv_code)                agvCode
        FROM biz_task a
        WHERE TO_DAYS(a.create_time) = TO_DAYS(NOW())

    </select>
    <!--  根据获取实体List，根据月   -->
    <select id="findListByMonth" resultType="com.youibot.tms.biz.entity.TaskStatistics">
        SELECT
        SUM(a.success_num) AS success_num ,
        SUM(a.fail_num) AS fail_num ,
        SUM(a.total) AS "total" ,
        a.agv_code AS "agv_code" ,
        CONCAT(date_format(a.create_time,'%Y-%m'),'-01 00:00:00') AS "createTime" ,
        a.attribution_date AS attribution_date ,
        a.date_hour AS date_hour
        FROM biz_task_statistics a
        <where>
            <if test="taskStatisticsRequest.agvCodes != null and taskStatisticsRequest.agvCodes.size() > 0">
                AND a.agv_id in
                <foreach collection="taskStatisticsRequest.agvCodes" index="index" open="(" close=")" item="agvCode"
                         separator=",">
                    #{agvCode}
                </foreach>
            </if>
            <if test="taskStatisticsRequest.beginDate != null and taskStatisticsRequest.beginDate != ''">
                AND date_format(a.attribution_date,'%Y-%m-%d') <![CDATA[ >= ]]>
                date_format(#{taskStatisticsRequest.beginDate},'%Y-%m-%d')
            </if>
            <if test="taskStatisticsRequest.endDate != null and taskStatisticsRequest.endDate != ''">
                AND date_format(a.attribution_date,'%Y-%m-%d') <![CDATA[ <= ]]>
                date_format(#{taskStatisticsRequest.endDate},'%Y-%m-%d')
            </if>
        </where>
        group by date_format(a.attribution_date,'%Y-%m'),a.agv_code
    </select>


    <!--  根据获取实体List ，根据天  -->
    <select id="findListByDay" resultType="com.youibot.tms.biz.entity.TaskStatistics">
        SELECT
        SUM(a.success_num) AS success_num ,
        SUM(a.fail_num) AS fail_num ,
        SUM(a.total) AS "total" ,
        a.agv_code AS "agv_code" ,
        date_format(a.create_time,'%Y-%m-%d') AS "createTime" ,
        a.attribution_date AS attribution_date ,
        a.date_hour AS date_hour
        FROM biz_task_statistics a
        <where>
            <if test="taskStatisticsRequest.agvCodes != null and taskStatisticsRequest.agvCodes.size() > 0">
                AND a.agv_id in
                <foreach collection="taskStatisticsRequest.agvCodes" index="index" open="(" close=")" item="agvCode"
                         separator=",">
                    #{agvCode}
                </foreach>
            </if>
            <if test="taskStatisticsRequest.beginDate != null and taskStatisticsRequest.beginDate != ''">
                AND date_format(a.attribution_date,'%Y-%m-%d') <![CDATA[ >= ]]>
                date_format(#{taskStatisticsRequest.beginDate},'%Y-%m-%d')
            </if>
            <if test="taskStatisticsRequest.endDate != null and taskStatisticsRequest.endDate != ''">
                AND date_format(a.attribution_date,'%Y-%m-%d') <![CDATA[ <= ]]>
                date_format(#{taskStatisticsRequest.endDate},'%Y-%m-%d')
            </if>
        </where>
        group by date_format(a.attribution_date,'%Y-%m-%d'),a.agv_code
    </select>

</mapper>
