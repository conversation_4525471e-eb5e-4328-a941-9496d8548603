package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.entity.PortEq;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * eq料口表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-29
 */
public interface PortEqMapper extends BaseMapper<PortEq> {

    int updateBaseById(PortEq base);

    int updateSettingById(PortEq setting);

    @Update("update biz_port_eq set enabled = #{enabled} where id=#{id}")
    void updateEnabledById(PortEq entity);

    @Select("select a.id, a.code, a.name, a.zone_area_id, a.priority, a.device_id,a.device_iot_id, a.safety_flag, a.enabled,a.device_iot_signal_id,a.create_time,a.update_time " +
            "from biz_port_eq a " +
            "${ew.customSqlSegment}")
    List<PortEq> selectList(@Param("ew") Wrapper<PortEq> queryWrapper);

    List<PortEq> selectListByCode(String code);
}
