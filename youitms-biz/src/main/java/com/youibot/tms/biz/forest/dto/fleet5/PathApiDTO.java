package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "PathApiDTO",description = "路径详情")
public class PathApiDTO {

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "地图编码", position = 2)
    private String vehicleMapCode;

    @ApiModelProperty(value = "开始标记点", position = 3)
    private String startMarkerCode;

    @ApiModelProperty(value = "结束标记点", position = 4)
    private String endMarkerCode;

    @ApiModelProperty(value="禁用的机器人类型，多个用逗号隔开: Lift 顶升、Compose 复合",position = 5)
    private String disableAgvTypes;

    @ApiModelProperty(value = "车头朝向: none 自适应,0,90,180,-90,-180", position = 6)
    private Integer agvDirection;

    @ApiModelProperty(value = "路径类型，Common、普通路径，QR_Down、二维码对接路径，Shelflegs、货架腿对接，Symbol_V、V型板对接，Reflector、反光板对接，LeaveDocking、脱离对接，Pallet、托盘对接", position = 7)
    private String pathType;

    @ApiModelProperty(value = "线类型 1、直线 2、曲线（只要不为1,目前都默认为曲线）", position = 8)
    private Integer lineType;

    @ApiModelProperty(value = "工位编码", position = 9)
    private Integer workStationCode;

    @ApiModelProperty(value = "偏差X", position = 10)
    private Double offsetX;

    @ApiModelProperty(value = "偏差Y", position = 11)
    private Double offsetY;

    @ApiModelProperty(value = "偏差角度", position = 12)
    private Double offsetAngle;

    @ApiModelProperty(value = "模板编号", position = 13)
    private Integer templateNo;

    @ApiModelProperty(value = "相机避障: 1开启 0关闭 默认0", position = 14)
    private Integer cameraObstacle;

    @ApiModelProperty(value = "路径权重系数", position = 15)
    private Double weightRatio;

    @ApiModelProperty(value = "平移速度 m/s >0", position = 16)
    private Double movingSpeed;

    @ApiModelProperty(value = "旋转速度 rad/s >0", position = 17)
    private Double rotationSpeed;

    @ApiModelProperty(value = "移动避障区域，值1-16代表第1-16组区域，可以传空", position = 18)
    private Integer moveObstacleRegion;

    @ApiModelProperty(value = "旋转避障区域，值1-16代表第1-16组区域，可以传空", position = 19)
    private Integer rotationObstacleRegion;

    @ApiModelProperty(value = "融合特征 1、开启 0、关闭",position = 20)
    private Integer featureFusion;

    @ApiModelProperty(value = "避障 1、打开避障  2、关闭避障", position = 21)
    private Integer safety;

    @ApiModelProperty(value = "拓展布尔",position = 22)
    private String extendBit;

    @ApiModelProperty(value = "拓展字符串",position = 23)
    private String extendString;

    @ApiModelProperty(value = "自主绕障: 1开启 0关闭 默认0",position = 24)
    private Integer obstacleAvoidance;

    @ApiModelProperty(value = "路径信息",position = 25)
    private List<PathInfoApiDTO> pathInfos;

}
