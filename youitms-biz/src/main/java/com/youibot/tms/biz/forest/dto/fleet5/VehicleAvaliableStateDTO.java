package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 机器人总状态：可用列表、不可用列表及原因
 * 类名称：VehicleTotalStateDTO
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@EqualsAndHashCode
@Builder
@ApiModel(value = "VehicleAvaliableStateDTO", description = "机器人总状态")
@NoArgsConstructor
@AllArgsConstructor
public class VehicleAvaliableStateDTO implements Serializable {

    @ApiModelProperty(value = "可用机器人编码集合", position = 1)
    private List<String> avaliableVehicleList;

    @ApiModelProperty(value = "不可用机器人详情集合", position = 2)
    private List<UnavaliableVehicleInfo> unavaliableVehicleList;

    @Data
    @Builder
    @ApiModel(value = "UnavaliableVehicleInfo", description = "不可用机器人详情")
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class UnavaliableVehicleInfo{
        @ApiModelProperty(value = "不可用机器人编码", position = 1)
        private String code;
        @ApiModelProperty(value = "不可用原因", position = 2)
        private String reason;
    }

}
