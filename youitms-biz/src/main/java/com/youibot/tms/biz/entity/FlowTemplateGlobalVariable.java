package com.youibot.tms.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.tms.biz.enums.TaskGlobalVariableType;
import com.youibot.tms.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 任务全局变量
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Getter
@Setter
@TableName("biz_flow_template_global_variable")
@ToString
@ApiModel(value = "任务全局变量", description = "任务全局变量")
@EqualsAndHashCode(callSuper = true)
public class FlowTemplateGlobalVariable extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 流程模板ID
     */
    @TableField("flow_template_id")
    @ApiModelProperty(value = "流程模板ID")
    private String flowTemplateId;

    /**
     * 组件参数的全局变量KEY
     */
    @TableField("variable_key")
    @ApiModelProperty(value = "全局变量KEY")
    private String variableKey;

    /**
     * 全局变量默认值
     */
    @TableField("variable_value")
    @ApiModelProperty(value = "全局变量默认值")
    private String variableValue;

    /**
     * 全局变量说明
     */
    @TableField("remark")
    @ApiModelProperty(value = "变量说明")
    private String remark;

    /**
     * 全局变量类型: system：系统内置, custom：用户自定义
     */
    @TableField("type")
    @ApiModelProperty(value = "全局变量类型: system：系统内置, custom：用户自定义")
    private TaskGlobalVariableType type;


    /**
     * 删除标志（0代表存在 1代表删除）
     * 需要真删除，在实体类中重写此字段，并去掉TableLogic的注解
     */
    @TableField("del_flag")
    private Boolean delFlag = false;

}
