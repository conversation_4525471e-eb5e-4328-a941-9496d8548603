package com.youibot.tms.biz.forest.dto.fleet5;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 储位信息DTO
 * 对应Fleet系统中的MosStorageInfo类
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "MosStorageInfo", description = "储位信息")
public class MosStorageInfo {

    @ApiModelProperty(value = "料箱号码")
    private String id;

    @ApiModelProperty(value = "储位状态, FULL:有, EMPTY:无, ERROR:检测错误")
    private String state;

    @ApiModelProperty(value = "物料编码, UNDEFINE:未定义, NONE:读取失败, 其他值为正常读取到的数据")
    private String code;

    @ApiModelProperty(value = "错误编码")
    private Integer error_code;

    @ApiModelProperty(value = "信息")
    private String message;

    @ApiModelProperty(value = "扩展属性")
    private JSONObject property;

    @ApiModelProperty(value = "料箱序号")
    private int index;
}
