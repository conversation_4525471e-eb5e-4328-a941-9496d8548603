package com.youibot.tms.biz.crontab;

import com.youibot.tms.biz.service.TaskStatisticsService;
import com.youibot.tms.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <NAME_EMAIL> on 2023/2/21.
 *
 * <AUTHOR>
 * @date 2023/2/21 11:16
 */
@Slf4j
@Component
public class TaskCountCrontab {
    @Resource
    private TaskStatisticsService taskStatisticsService;

    /**
     * 定时执行统计任务数据，每个小时执行一次
     */
    @Scheduled(fixedRate = 60 * 60000)
//    @Scheduled(fixedRate = 2  * 60000)
    public void run() {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String specifiedDay = simpleDateFormat.format(date);
        List<String> lastWeekDayList = DateUtils.getLastWeekDayList(specifiedDay, 3);
        log.info("统计前三天的数据，要统计的时间列表：[{}]",lastWeekDayList);
        taskStatisticsService.batchDeleteByAttributionDate(lastWeekDayList);
        taskStatisticsService.countTaskHistoryData(3);
    }
}
