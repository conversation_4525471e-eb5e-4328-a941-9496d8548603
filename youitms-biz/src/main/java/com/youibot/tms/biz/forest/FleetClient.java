package com.youibot.tms.biz.forest;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.*;
import com.dtflys.forest.http.ForestResponse;
import com.youibot.tms.biz.api.request.TaskStatisticsRequest;
import com.youibot.tms.biz.forest.dto.*;
import com.youibot.tms.biz.forest.request.*;

import java.util.List;
import java.util.Map;

/**
 * 为了便于开发调试，外部调用不要直接调用FleetClient <br/>
 * 而是调用FleetProxyService，该接口有模拟实现类，开发时可以不必真正调用Fleet
 */
@BaseRequest(baseURL = "${fleetHost}", interceptor = FleetForestInterceptor.class)
public interface FleetClient {

    @GetRequest(value = "/api/v3/logo", timeout = 2000)
    FleetLogoDTO logo();


    @GetRequest(value = "${host}/api/v3/logo", interceptor = {}, retryCount = 0, timeout = 2000)
    FleetLogoDTO logo(@DataVariable("host") String host);

    /**
     * 查询所有AGV，包括不在线的
     *
     * @return
     */
    @LogEnabled(false)
    @GetRequest(value = "/api/v3/agvs", timeout = 5 * 1000)
    List<FleetAgvDTO> listAllAgvs();

    /**
     * 查询所有在线AGV
     *
     * @return
     */
    @GetRequest(value = "/api/v3/vehicles")
    List<VehicleDTO> listVehicles();

    /**
     * 查询所有在线并且可用的AGV
     *
     * @return
     */
    @GetRequest(value = "/api/v3/vehicles/free")
    List<VehicleDTO> listFreeVehicles();

    /**
     * 查询所有可用的机器人列表和不可用车辆及不可用原因
     *
     * @Title: freeAndNotFreeReason
     * @Description: 查询所有可用的机器人列表和不可用车辆及不可用原因
     * <AUTHOR>
     * @date 2021-05-08 06:07:37
     */
    @GetRequest(value = "/api/v3/vehicles/freeAndNotFreeReason")
    FreeAgvAndNotFreeReasonVO freeAndNotFreeReason();

    /**
     * 查询单台AGV
     *
     * @param agvCode
     * @return
     */
    @GetRequest(value = "/api/v3/vehicles/${agvCode}")
    VehicleDTO getVehicle(@DataVariable("agvCode") String agvCode);

    /**
     * @param markerId
     * @return
     * @Title: getAgvCodesSequence
     * @Description: 查询导航点附近的所有机器人列表按照距离远近返回
     */
    @GetRequest(value = "/api/v3/bestWayWitchMarkId/getAgvCodesSequence/${markerId}", timeout = 10 * 1000)
    Map<String, Integer> getAgvCodesSequence(@DataVariable("markerId") String markerId);


    /**
     * 查询所有导航点
     *
     * @param queryRequest
     * @return
     */
    @GetRequest(value = "/api/v3/markers")
    List<MarkerDTO> getAllMarker(@Query MarkerQueryRequest queryRequest);


    /**
     * @param missionWorkParam 创建任务所需要的参数
     * @return
     * @Title: createMissionWork
     * @Description: 通过missionid创建missinWork
     * <AUTHOR>
     * @date 2021-03-02 01:52:52
     */
    @PostRequest(value = "/api/v3/missionWorks")
    MissionWorkDTO createMissionWork(@JSONBody MissionWorkParam missionWorkParam);

    /**
     * 分页查询导航点
     *
     * @param queryPageRequest
     * @return
     */
    @GetRequest(value = "/api/v3/markers/page")
    FleetPageDataDTO<MarkerDTO> getPageMarker(@Query MarkerQueryPageRequest queryPageRequest);

    /**
     * 查询所有任务
     *
     * @param queryRequest
     * @return
     */
    @GetRequest(value = "/api/v3/missions", timeout = 5 * 1000)
    List<MissionDTO> getAllMission(@Query MissionQueryRequest queryRequest);

    /**
     * 分页查询任务
     *
     * @param queryPageRequest
     * @return
     */
    @GetRequest(value = "/api/v3/missions/page")
    FleetPageDataDTO<MissionDTO> getPageMission(@Query MissionQueryPageRequest queryPageRequest);


    /**
     * @param id
     * @return
     * @Title: getMissionWork
     * @Description: 查询missionwork状态
     * <AUTHOR>
     * @date 2021-03-03 09:01:39
     */
    @GetRequest(value = "/api/v3/missionWorks/${id}", timeout = 5 * 1000)
    MissionWorkDTO getMissionWork(@DataVariable("id") String id);


    /**
     * 查询地图列表
     *
     * @param request
     * @return
     */
    @GetRequest(value = "/api/v3/AGVMaps")
    List<AgvMapDTO> getAllAGVMap(@Query AgvMapRequest request);

    /**
     * 查询地图列表
     *
     * @param request
     * @return
     */
    @GetRequest(value = "/api/v3/AGVMaps")
    List<AGVMapVO> getAllAGVMap4(@Query AgvMapRequest request);


    @GetRequest(value = "/api/v3/agvTypes")
    List<AgvTypeDTO> getAllAgvTypes();

    /**
     * 开启自动充电和泊车 4.8之前的版本
     *
     * @param agvCode agv编号
     * @return
     */
    @PostRequest("/api/v3/vehicles/${agvCode}/openAutoParkOrCharge")
    ForestResponse<String> openAutoParkOrCharge(@DataVariable("agvCode") String agvCode);

    /**
     * 关闭自动充电和泊车 4.8之前的版本
     *
     * @param agvCode agv编号
     * @return
     */
    @PostRequest("/api/v3/vehicles/${agvCode}/closeAutoParkOrChage")
    ForestResponse<String> closeAutoParkOrCharge(@DataVariable("agvCode") String agvCode);


    /**
     * 开启自动充电 4.8之后的版本
     * 07-23  周武提供新的版本
     *
     * @param agvCode agv编号
     * @return
     */
    @PostRequest("/api/v3/vehicles/${agvCode}/openAutoCharge")
    ForestResponse<String> openAutoCharge(@DataVariable("agvCode") String agvCode);

    /**
     * 关闭自动充电4.8之后的版本
     *
     * @param agvCode agv编号
     * @return
     */
    @PostRequest("/api/v3/vehicles/${agvCode}/closeAutoCharge")
    ForestResponse<String> closeAutoCharge(@DataVariable("agvCode") String agvCode);


    /**
     * 关闭自动泊车4.8之后的版本
     *
     * @param agvCode
     * @return
     */
    @PostRequest("/api/v3/vehicles/${agvCode}/closeAutoPark")
    ForestResponse<String> closeAutoPark(@DataVariable("agvCode") String agvCode);


    /**
     * 开启自动泊车4.8之后的版本
     *
     * @param agvCode
     * @return
     */
    @PostRequest("/api/v3/vehicles/${agvCode}/openAutoPark")
    ForestResponse<String> openAutoPark(@DataVariable("agvCode") String agvCode);


    /**
     * 返回任务动作在某个时间段的统计信息,传入agvCode,查询单个，否则查询所有
     *
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @description //TODO
     * @date 2022/3/29 13:48
     **/
    @GetRequest("/api/v3/statistic/getMissionWorkActionStatistic")
    JSONObject getMissionWorkActionStatistic(@Query TaskStatisticsRequest agvDataStatisticRequest);

    /**
     * 返回机器人在某个时间段的作业统计信息
     *
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @description //TODO
     * @date 2022/3/29 13:48
     **/
    @GetRequest("/api/v3/statistic/getMissionWorkStatistic")
    JSONObject getMissionWorkStatistic(@Query MissionWorkStatisticQueryRequest agvDataStatisticRequest);


    /**
     * 继续执行作业
     *
     * @param missionWorkId 任务作业编号
     */
    @PostRequest("/api/v3/missionWorks/${id}/controls/continue")
    void continueMissionWork(@DataVariable("id") String missionWorkId);

    /**
     * 停止调度系统正在执行的任务作业
     *
     * @param missionWorkId 任务作业编号
     * @param param         参数
     */
    @PostRequest("/api/v3/missionWorks/${id}/controls/stop")
    void stopMissionWork(@DataVariable("id") String missionWorkId, @JSONBody StopMissionWorkRequest param);

    @PostRequest("/api/v3/bestWayWitchMarkId/getMarkerIdsSequence")
    Map<String, Integer> getMarkerIdsSequence(@JSONBody TMSMarkerIdSequenceDTO request);

    /**
     * 获取调度配置
     */
    @GetRequest("/api/v3/schedulerConfig")
    SchedulerConfig getSchedulerConfig();

    /**
     * AGV一键停止(停止充电动作、归位动作、作业)
     *
     * @param agvCode 机器人编号
     */
    @PostRequest("/api/v3/vehicles/${agvCode}/oneKeyStop")
    void oneKeyStop(@DataVariable("agvCode") String agvCode);


    /**
     * AGV一键重置(青措充电异常、归为异常、作业异常)
     *
     * @param agvCode 机器人编号
     */
    @PostRequest("/api/v3/vehicles/${agvCode}/oneKeyReset")
    void oneKeyReset(@DataVariable("agvCode") String agvCode);

    /**
     * 查询任务动作
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @GetRequest("/api/v3/missionWorkActions/page")
    FleetPageDataDTO<MissionWorkActionDTO> getMissionAction(@Query String startTime, @Query String endTime, @Query String sort);
}
