package com.youibot.tms.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.api.dto.WorkFlowTemplateDTO;
import com.youibot.tms.biz.api.dto.WorkFlowTemplateExportDTO;
import com.youibot.tms.biz.api.request.FlowTemplateSaveGraphDataRequest;
import com.youibot.tms.biz.entity.*;
import com.youibot.tms.biz.enums.AgvUsageStatus;
import com.youibot.tms.biz.enums.FlowNodeTemplateGraphDataAttrTypeEnum;
import com.youibot.tms.biz.enums.FlowTemplateComponentTypeEnum;
import com.youibot.tms.biz.enums.TaskStatus;
import com.youibot.tms.biz.flow.common.CommonWorkFlowVariable;
import com.youibot.tms.biz.flow.common.listener.CommonWorkFlowListener;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.common.core.dto.ResultDTO;
import com.youibot.tms.common.exception.ServiceException;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.exception.base.CommonErrorCode;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.workflow.core.enums.FlowStatusEnum;
import com.youibot.tms.workflow.core.executor.WorkFlowExecutor;
import com.youibot.tms.workflow.core.flow.WorkFlow;
import com.youibot.tms.workflow.core.listener.WorkFlowExecuteListener;
import com.youibot.tms.workflow.entity.WorkFlowEntity;
import com.youibot.tms.workflow.entity.WorkFlowNodeEntity;
import com.youibot.tms.workflow.entity.WorkFlowNodeTemplateEntity;
import com.youibot.tms.workflow.entity.WorkFlowTemplateEntity;
import com.youibot.tms.workflow.enums.WorkFlowTemplateStatus;
import com.youibot.tms.workflow.request.WorkFlowRequest;
import com.youibot.tms.workflow.service.WorkFlowNodeTemplateService;
import com.youibot.tms.workflow.service.WorkFlowService;
import com.youibot.tms.workflow.service.WorkFlowTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FlowTemplateAssistServiceImpl implements FlowTemplateAssistService {
    @Autowired
    private WorkFlowExecutor workFlowExecutor;
    @Autowired
    private WorkFlowTemplateService workFlowTemplateService;

    @Autowired
    private WorkFlowNodeTemplateService workFlowNodeTemplateService;

    @Autowired
    private CommonWorkFlowListener commonWorkFlowListener;

    @Autowired
    private FlowNodeTemplateGraphDataService flowNodeTemplateGraphDataService;
    @Autowired
    private FlowTemplateComponentGlobalVariableService flowTemplateComponentGlobalVariableService;
    @Autowired
    private FlowTemplateGlobalVariableService flowTemplateGlobalVariableService;
    @Autowired
    private FlowTemplateComponentOutputParamsService flowTemplateComponentOutputParamsService;
    @Autowired
    private WorkFlowService workFlowService;
    @Autowired
    private TaskService taskService;
    @Resource
    private AgvService agvService;
    @Autowired
    private FleetProxyService fleetProxyService;
    @Resource
    protected TaskLogService taskLogService;
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 不分页查询工作流流程模板列表
     *
     * @param workFlowRequest 参数
     * @return
     */
    @Override
    public List<WorkFlowTemplateEntity> list(WorkFlowRequest workFlowRequest) {
        LambdaQueryWrapper<WorkFlowTemplateEntity> queryWrapper = getLambdaQueryWrapper(workFlowRequest);
        queryWrapper.orderByDesc(WorkFlowTemplateEntity::getCreateTime);
        return workFlowTemplateService.list(queryWrapper);
    }

    /**
     * 页面表格分页查询
     *
     * @param workFlowRequest 参数
     * @return
     */
    @Override
    public Page<WorkFlowTemplateEntity> pageList(WorkFlowRequest workFlowRequest) {
        LambdaQueryWrapper<WorkFlowTemplateEntity> queryWrapper = getLambdaQueryWrapper(workFlowRequest);
        queryWrapper.orderByDesc(WorkFlowTemplateEntity::getCreateTime);
        Page<WorkFlowTemplateEntity> page = new Page<>(workFlowRequest.getPageNum(), workFlowRequest.getPageSize());
        return workFlowTemplateService.page(page, queryWrapper);
    }

    private LambdaQueryWrapper<WorkFlowTemplateEntity> getLambdaQueryWrapper(WorkFlowRequest workFlowRequest) {
        LambdaQueryWrapper<WorkFlowTemplateEntity> queryWrapper = Wrappers.lambdaQuery();
        if (ToolUtil.isNotEmpty(workFlowRequest.getCode())) {
            queryWrapper.eq(WorkFlowTemplateEntity::getCode, workFlowRequest.getCode());
        }
        if (ToolUtil.isNotEmpty(workFlowRequest.getName())) {
            queryWrapper.eq(WorkFlowTemplateEntity::getName, workFlowRequest.getName());
        }
        if (ToolUtil.isNotEmpty(workFlowRequest.getQueryCreateTimeStart())) {
            queryWrapper.gt(WorkFlowTemplateEntity::getCreateTime, workFlowRequest.getQueryCreateTimeStart());
        }
        if (ToolUtil.isNotEmpty(workFlowRequest.getQueryCreateTimeEnd())) {
            queryWrapper.lt(WorkFlowTemplateEntity::getCreateTime, workFlowRequest.getQueryCreateTimeEnd());
        }
        return queryWrapper;
    }


    /**
     * 根据ID查询详细的流程模板信息
     *
     * @param id 主键
     * @return
     */
    @Override
    public WorkFlowTemplateDTO getById(String id) {
        WorkFlowTemplateEntity workFlowTemplateById = workFlowTemplateService.getWorkFlowTemplateById(id);
        if (workFlowTemplateById == null) {
            return null;
        }
        return getWorkFlowTemplateDTO(workFlowTemplateById);
    }


    /**
     * 根据ID查询详细的流程模板信息
     *
     * @param code 主键
     * @return
     */
    @Override
    public WorkFlowTemplateDTO getByCode(String code) {
        WorkFlowTemplateEntity workFlowTemplateById = workFlowTemplateService.getWorkFlowTemplateByCode(code);
        return getWorkFlowTemplateDTO(workFlowTemplateById);
    }

    private WorkFlowTemplateDTO getWorkFlowTemplateDTO(WorkFlowTemplateEntity workFlowTemplateById) {
        List<FlowNodeTemplateGraphData> list = flowNodeTemplateGraphDataService.list(Wrappers.<FlowNodeTemplateGraphData>lambdaQuery().eq(FlowNodeTemplateGraphData::getFlowTemplateId, workFlowTemplateById.getId()));
        List<FlowTemplateComponentOutputParams> componentOutputParamsList = flowTemplateComponentOutputParamsService.list(Wrappers.<FlowTemplateComponentOutputParams>lambdaQuery()
                .eq(FlowTemplateComponentOutputParams::getFlowTemplateId, workFlowTemplateById.getId()));
        List<FlowNodeTemplateGraphData> edgeList = list.stream().filter(item -> FlowNodeTemplateGraphDataAttrTypeEnum.edge.equals(item.getAttrType())).collect(Collectors.toList());
        List<FlowNodeTemplateGraphData> nodeList = list.stream().filter(item -> FlowNodeTemplateGraphDataAttrTypeEnum.node.equals(item.getAttrType())).collect(Collectors.toList());
        WorkFlowTemplateDTO flowNodeTemplateGraphDataDTO = new WorkFlowTemplateDTO();
        flowNodeTemplateGraphDataDTO.setWorkFlowTemplateEntity(workFlowTemplateById);
        flowNodeTemplateGraphDataDTO.setNodes(nodeList);
        flowNodeTemplateGraphDataDTO.setEdges(edgeList);
        flowNodeTemplateGraphDataDTO.setComponentOutputParamsList(componentOutputParamsList);
        return flowNodeTemplateGraphDataDTO;
    }


    /**
     * 根据ID删除流程模板
     *
     * @param id 主键
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        boolean b = workFlowTemplateService.removeById(id);
        if (b) {
            workFlowNodeTemplateService.remove(Wrappers.<WorkFlowNodeTemplateEntity>lambdaQuery().eq(WorkFlowNodeTemplateEntity::getFlowTemplateId, id));
            flowTemplateGlobalVariableService.remove(Wrappers.<FlowTemplateGlobalVariable>lambdaQuery().eq(FlowTemplateGlobalVariable::getFlowTemplateId, id));
            flowNodeTemplateGraphDataService.remove(Wrappers.<FlowNodeTemplateGraphData>lambdaQuery().eq(FlowNodeTemplateGraphData::getFlowTemplateId, id));
            flowTemplateComponentGlobalVariableService.remove(Wrappers.<FlowTemplateComponentGlobalVariable>lambdaQuery().eq(FlowTemplateComponentGlobalVariable::getFlowTemplateId, id));
            flowTemplateComponentOutputParamsService.remove(Wrappers.<FlowTemplateComponentOutputParams>lambdaQuery().eq(FlowTemplateComponentOutputParams::getFlowTemplateId, id));
        }
        return b;
    }


    /**
     * 根据ID列表批量删除流程模板
     *
     * @param ids 主键
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<String> ids) {
        boolean b = workFlowTemplateService.removeByIds(ids);
        if (b) {
            workFlowNodeTemplateService.remove(Wrappers.<WorkFlowNodeTemplateEntity>lambdaQuery().in(WorkFlowNodeTemplateEntity::getFlowTemplateId, ids));
            //删除全局变量
            flowTemplateGlobalVariableService.remove(Wrappers.<FlowTemplateGlobalVariable>lambdaQuery().in(FlowTemplateGlobalVariable::getFlowTemplateId, ids));
            flowNodeTemplateGraphDataService.remove(Wrappers.<FlowNodeTemplateGraphData>lambdaQuery().in(FlowNodeTemplateGraphData::getFlowTemplateId, ids));
            flowTemplateComponentGlobalVariableService.remove(Wrappers.<FlowTemplateComponentGlobalVariable>lambdaQuery().in(FlowTemplateComponentGlobalVariable::getFlowTemplateId, ids));
            flowTemplateComponentOutputParamsService.remove(Wrappers.<FlowTemplateComponentOutputParams>lambdaQuery().in(FlowTemplateComponentOutputParams::getFlowTemplateId, ids));
        }
        return b;
    }

    /**
     * 根据ID更新流程模板
     *
     * @param workFlowTemplateEntity 流程模板信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(WorkFlowTemplateEntity workFlowTemplateEntity) {
        ConcurrentHashMap<String, WorkFlowNodeTemplateEntity> nodeList = workFlowTemplateEntity.getNodeList();
        if (nodeList != null && nodeList.size() > 0) {
            workFlowNodeTemplateService.remove(Wrappers.<WorkFlowNodeTemplateEntity>lambdaQuery().eq(WorkFlowNodeTemplateEntity::getFlowTemplateId, workFlowTemplateEntity.getId()));
            nodeList.forEach((key, value) -> workFlowNodeTemplateService.saveOrUpdateWorkFlowNodeTemplateById(value));
        }
        return workFlowTemplateService.updateById(workFlowTemplateEntity);
    }

    @Override
    public void saveFlowGraphData(FlowTemplateSaveGraphDataRequest flowTemplateSaveGraphDataRequest) {
        List<FlowNodeTemplateGraphData> nodeList1 = flowTemplateSaveGraphDataRequest.getNodeList();
        List<FlowNodeTemplateGraphData> edgeList = flowTemplateSaveGraphDataRequest.getEdgeList();
        for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : nodeList1) {
            flowNodeTemplateGraphData.setAttrType(FlowNodeTemplateGraphDataAttrTypeEnum.node);
            flowNodeTemplateGraphData.setFlowTemplateId(flowTemplateSaveGraphDataRequest.getFlowTemplateId());
        }
        for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : edgeList) {
            flowNodeTemplateGraphData.setAttrType(FlowNodeTemplateGraphDataAttrTypeEnum.edge);
            flowNodeTemplateGraphData.setFlowTemplateId(flowTemplateSaveGraphDataRequest.getFlowTemplateId());
        }
        //保存流程节点前端需要的数据信息
        flowNodeTemplateGraphDataService.remove(Wrappers.<FlowNodeTemplateGraphData>lambdaQuery().eq(FlowNodeTemplateGraphData::getFlowTemplateId, flowTemplateSaveGraphDataRequest.getFlowTemplateId()));
        ArrayList<FlowNodeTemplateGraphData> flowNodeTemplateGraphData1 = new ArrayList<>(nodeList1);
        flowNodeTemplateGraphData1.addAll(edgeList);
        flowNodeTemplateGraphDataService.saveBatch(flowNodeTemplateGraphData1);
        //更新流程模板信息
        workFlowTemplateService.update(Wrappers.<WorkFlowTemplateEntity>lambdaUpdate()
                .eq(WorkFlowTemplateEntity::getId, flowTemplateSaveGraphDataRequest.getFlowTemplateId()).set(WorkFlowTemplateEntity::getStatus, WorkFlowTemplateStatus.unPublish));
    }

    /**
     * 保存流程模板和流程图数据
     *
     * @param flowTemplateSaveGraphDataRequest 请求信息
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveFlowTemplateGraphData(FlowTemplateSaveGraphDataRequest flowTemplateSaveGraphDataRequest) {
        List<FlowNodeTemplateGraphData> nodeList1 = flowTemplateSaveGraphDataRequest.getNodeList();
        List<FlowNodeTemplateGraphData> startNodes = new ArrayList<>();
        List<FlowNodeTemplateGraphData> endNodes = new ArrayList<>();
        for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : nodeList1) {
            if (FlowTemplateComponentTypeEnum.StartComponent.equals(FlowTemplateComponentTypeEnum.valueOf(flowNodeTemplateGraphData.getType()))) {
                startNodes.add(flowNodeTemplateGraphData);
            }
            if (FlowTemplateComponentTypeEnum.EndComponent.equals(FlowTemplateComponentTypeEnum.valueOf(flowNodeTemplateGraphData.getType()))) {
                endNodes.add(flowNodeTemplateGraphData);
            }
        }
        if (startNodes.size() != 1) {
            throw new ServiceException("没有或有多个开始节点！", 50008);
        }
        if (endNodes.size() != 1) {
            throw new ServiceException("没有或有多个结束节点！", 50009);
        }
        List<FlowNodeTemplateGraphData> edgeList = flowTemplateSaveGraphDataRequest.getEdgeList();
        for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : edgeList) {
            flowNodeTemplateGraphData.setFlowTemplateId(flowTemplateSaveGraphDataRequest.getFlowTemplateId());
            flowNodeTemplateGraphData.setAttrType(FlowNodeTemplateGraphDataAttrTypeEnum.edge);
        }
        //获取开始节点和节数节点
        WorkFlowNodeTemplateEntity lastNode = null;
        WorkFlowNodeTemplateEntity firstNode = null;
        //将前端的数据模型转化成流程模板节点信息
        ConcurrentHashMap<String, WorkFlowNodeTemplateEntity> nodeList = new ConcurrentHashMap<>();
        for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : nodeList1) {
            flowNodeTemplateGraphData.setFlowTemplateId(flowTemplateSaveGraphDataRequest.getFlowTemplateId());
            flowNodeTemplateGraphData.setAttrType(FlowNodeTemplateGraphDataAttrTypeEnum.node);
            WorkFlowNodeTemplateEntity workFlowNodeTemplateEntity = flowNodeTemplateGraphData.toWorkFlowNodeTemplateEntity(edgeList,nodeList1);
            workFlowNodeTemplateEntity.setFlowTemplateId(flowTemplateSaveGraphDataRequest.getFlowTemplateId());
            if (FlowTemplateComponentTypeEnum.StartComponent.equals(FlowTemplateComponentTypeEnum.valueOf(flowNodeTemplateGraphData.getType()))) {
                firstNode = workFlowNodeTemplateEntity;
            }
            if (FlowTemplateComponentTypeEnum.EndComponent.equals(FlowTemplateComponentTypeEnum.valueOf(flowNodeTemplateGraphData.getType()))) {
                lastNode = workFlowNodeTemplateEntity;
            }
            nodeList.put(workFlowNodeTemplateEntity.getId(), workFlowNodeTemplateEntity);
        }


        //保存流程节点前端需要的数据信息
        flowNodeTemplateGraphDataService.remove(Wrappers.<FlowNodeTemplateGraphData>lambdaQuery().eq(FlowNodeTemplateGraphData::getFlowTemplateId, flowTemplateSaveGraphDataRequest.getFlowTemplateId()));
        ArrayList<FlowNodeTemplateGraphData> flowNodeTemplateGraphData1 = new ArrayList<>(nodeList1);
        flowNodeTemplateGraphData1.addAll(edgeList);
        for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : flowNodeTemplateGraphData1) {
            flowNodeTemplateGraphDataService.save(flowNodeTemplateGraphData);
        }


        //更新流程模板信息
        WorkFlowTemplateEntity byId = workFlowTemplateService.getById(flowTemplateSaveGraphDataRequest.getFlowTemplateId());
        assert firstNode != null;
        byId.setFirstNodeId(firstNode.getId());
        assert lastNode != null;
        byId.setLastNodeId(lastNode.getId());
        byId.setStatus(WorkFlowTemplateStatus.published);
        workFlowTemplateService.updateById(byId);
        if (nodeList.size() > 0) {
            workFlowNodeTemplateService.remove(Wrappers.<WorkFlowNodeTemplateEntity>lambdaQuery().eq(WorkFlowNodeTemplateEntity::getFlowTemplateId, flowTemplateSaveGraphDataRequest.getFlowTemplateId()));
            nodeList.forEach((key, value) -> workFlowNodeTemplateService.save(value));
        }
    }


    /**
     * 创建工作流流程模板
     *
     * @param workFlowTemplateEntity 流程模板信息
     * @return
     */
    @Override
    public WorkFlowTemplateEntity save(WorkFlowTemplateEntity workFlowTemplateEntity) {
        int count = workFlowTemplateService.count(Wrappers.<WorkFlowTemplateEntity>lambdaQuery()
                .eq(WorkFlowTemplateEntity::getCode, workFlowTemplateEntity.getCode()));
        if (count > 0) {
            throw new BusinessException(CommonErrorCode.CODE_EXISTED);
        }
        ConcurrentHashMap<String, WorkFlowNodeTemplateEntity> nodeList = workFlowTemplateEntity.getNodeList();
        if (nodeList != null && nodeList.size() > 0) {
            nodeList.forEach((key, value) -> workFlowNodeTemplateService.saveOrUpdateWorkFlowNodeTemplateById(value));
        }
        workFlowTemplateService.saveOrUpdateWorkFlowTemplateById(workFlowTemplateEntity);
        //初始化每个流程模板都有的系统内置全局变量
        flowTemplateGlobalVariableService.initSystemVariables(workFlowTemplateEntity.getId());
        return workFlowTemplateEntity;
    }


    /**
     * 根据流程模板ID生成工作流流程
     *
     * @param workFlowTemplateId 流程模板信息
     * @param taskGlobalVariable 全局变量
     * @return
     */
    @Override
    public WorkFlow initAndExecuteWorkflow(String workFlowTemplateId, JSONObject taskGlobalVariable) {
        log.info("准备开始执行");
        WorkFlow objectWorkFlow = initWorkflow(workFlowTemplateId, taskGlobalVariable, commonWorkFlowListener);
        workFlowExecutor.executor(objectWorkFlow);
        log.info("开始执行");
        return objectWorkFlow;
    }

    @Override
    public WorkFlow initWorkflow(String workFlowTemplateId, JSONObject taskGlobalVariable, WorkFlowExecuteListener workFlowExecuteListener) {
        WorkFlowTemplateEntity workFlowTemplateById = workFlowTemplateService.getWorkFlowTemplateById(workFlowTemplateId);
        if (workFlowTemplateById == null) {
            throw new ServiceException("没有找到此流程,请传入正确的任务流程编号！流程编号：" + workFlowTemplateId);
        }
        List<FlowTemplateComponentGlobalVariable> list = flowTemplateComponentGlobalVariableService.list(Wrappers.<FlowTemplateComponentGlobalVariable>lambdaQuery().eq(FlowTemplateComponentGlobalVariable::getFlowTemplateId, workFlowTemplateId));
        List<FlowTemplateGlobalVariable> flowTemplateGlobalVariables = flowTemplateGlobalVariableService.list(Wrappers.<FlowTemplateGlobalVariable>lambdaQuery().eq(FlowTemplateGlobalVariable::getFlowTemplateId, workFlowTemplateId));
        CommonWorkFlowVariable commonWorkFlowVariable = new CommonWorkFlowVariable();
        HashMap<FlowTemplateComponentTypeEnum, List<FlowTemplateComponentGlobalVariable>> flowTemplateComponentTypeEnumListHashMap = new HashMap<FlowTemplateComponentTypeEnum, List<FlowTemplateComponentGlobalVariable>>();
        for (FlowTemplateComponentGlobalVariable flowTemplateComponentGlobalVariable : list) {
            FlowTemplateComponentTypeEnum flowTemplateComponentTypeEnum = FlowTemplateComponentTypeEnum.valueOf(flowTemplateComponentGlobalVariable.getComponentCode());
            List<FlowTemplateComponentGlobalVariable> flowTemplateComponentGlobalVariables1 = flowTemplateComponentTypeEnumListHashMap.get(flowTemplateComponentTypeEnum);
            if (flowTemplateComponentGlobalVariables1 == null) {
                ArrayList<FlowTemplateComponentGlobalVariable> flowTemplateComponentGlobalVariables = new ArrayList<>();
                flowTemplateComponentGlobalVariables.add(flowTemplateComponentGlobalVariable);
                flowTemplateComponentTypeEnumListHashMap.put(flowTemplateComponentTypeEnum, flowTemplateComponentGlobalVariables);
            } else {
                flowTemplateComponentGlobalVariables1.add(flowTemplateComponentGlobalVariable);
            }
        }
        commonWorkFlowVariable.setTaskGlobalVariable(taskGlobalVariable);
        commonWorkFlowVariable.setTaskComponentParams(flowTemplateComponentTypeEnumListHashMap);
        commonWorkFlowVariable.setFlowTemplateGlobalVariables(flowTemplateGlobalVariables);
        WorkFlow objectWorkFlow = workFlowTemplateService.initWorkFlow(workFlowTemplateById, workFlowExecuteListener);
        objectWorkFlow.setVariables(commonWorkFlowVariable);
        return objectWorkFlow;
    }

    @Override
    public void setDefaultFlowTemplate(String flowTemplateId) {
        //先将其他的默认流程设置为非默认
        workFlowTemplateService.update(Wrappers.<WorkFlowTemplateEntity>lambdaUpdate()
                .eq(WorkFlowTemplateEntity::getIsDefault, true).set(WorkFlowTemplateEntity::getIsDefault, false));
        //再将当前流程设置为默认流程
        workFlowTemplateService.update(Wrappers.<WorkFlowTemplateEntity>lambdaUpdate()
                .eq(WorkFlowTemplateEntity::getId, flowTemplateId).set(WorkFlowTemplateEntity::getIsDefault, true));
    }

    @Override
    public WorkFlowTemplateEntity getDefaultFlowTemplate() {
        return workFlowTemplateService.getOne(Wrappers.<WorkFlowTemplateEntity>lambdaQuery().eq(WorkFlowTemplateEntity::getIsDefault, true).last(" limit 1"));
    }


    @Override
    public WorkFlowTemplateDTO getFlowTemplateInfo(String flowId) {
        WorkFlowEntity workFlowById = workFlowService.getWorkFlowById(flowId);
        if (workFlowById == null) {
            return  null;
//            throw new ServiceException("请传入正确的任务流程编号！");
        }

        ConcurrentHashMap<String, WorkFlowNodeEntity> nodeList = workFlowById.getNodeList();
        Collection<WorkFlowNodeEntity> values = nodeList.values();
        String flowTemplateId = workFlowById.getFlowTemplateId();
        WorkFlowTemplateDTO workFlowTemplateDTO = getById(flowTemplateId);
        if (workFlowTemplateDTO == null) {
            throw new ServiceException("没有查询到此流程，请传入正确的任务流程编号！");
        }
        List<FlowNodeTemplateGraphData> nodes = workFlowTemplateDTO.getNodes();
        for (FlowNodeTemplateGraphData node : nodes) {
            Optional<WorkFlowNodeEntity> any = values.stream().filter(item -> item.getFlowNodeTemplateId().equals(node.getId())).findAny();
            if (any.isPresent()) {
                WorkFlowNodeEntity workFlowNodeEntity = any.get();
                GraphProperties graphProperties = new GraphProperties();
                graphProperties.setFlowTemplateId(flowTemplateId);
                graphProperties.setComponentCode(node.getType());
                graphProperties.setFlowNodeTemplateId(node.getFlowTemplateId());
                graphProperties.setWorkFlowNodeEntity(workFlowNodeEntity);
                node.setProperties(graphProperties);
            } else {
                log.warn("查询到流程发生了变动，变动的节点展示的节点状态可能会对应不上！变动的节点：{}", node);
//                throw new ServiceException("查询到此流程发生了变更，请传入正确的任务流程编号！");
            }
        }
        workFlowTemplateDTO.setWorkFlowEntity(workFlowById);
        return workFlowTemplateDTO;
    }

    @Override
    public ResultDTO flowOperation(String flowId, String operation) {
        if (flowId == null) {
            throw new ServiceException("参数缺失，请传入正确的流程ID！");
        }
        if (operation == null) {
            throw new ServiceException("参数缺失，请传入正确的操作类型！");
        }
        log.info("手动操作：" + operation);
        List<Task> list = taskService.list(Wrappers.<Task>lambdaQuery().eq(Task::getFlowId, flowId));
        WorkFlow workFlowById = workFlowExecutor.getWorkFlowById(flowId);
        if (workFlowById == null) {
            throw new ServiceException("未找到此流程！流程编号：" + flowId);
        }
        if (list.size() == 1) {
            Task task = list.get(0);
            String agvCode = task.getAgvCode();
            if ("stop".equals(operation)) {
                if (!FlowStatusEnum.COMPLETED.equals(workFlowById.getStatus())
                        && !FlowStatusEnum.STOPPED.equals(workFlowById.getStatus())) {
                    if (agvCode != null) {
                        Agv agv = agvService.selectByAgvCode(agvCode);
                        if (agv != null) {
                            //一键停止AGV当前车身上的任务
                            fleetProxyService.restartTaskPrevOneKeyStopAllAgvRunningMission(agvCode);
                        }
                    }
                    workFlowExecutor.stop(flowId);
                    return ResultDTO.success();
                } else {
                    throw new ServiceException("当前任务已经停止或者已经完成无法停止");
                }
            } else {
                if (agvCode != null) {
                    Agv agv = agvService.selectByAgvCode(agvCode);
                    if (agv == null) {
                        if ("restart".equals(operation)) {
                            log.info("由于此任务使用的机器人已经被清理掉了，没有找到此任务使用的机器人，因此重新执行此任务时会重新对任务进行派车申请！");
                            recordTaskLog(task.getCode(), flowId);
                            checkAndStopFlow(flowId, workFlowById, agvCode);
                            workFlowExecutor.executor(flowId);
                            return ResultDTO.success("操作成功，由于此任务使用的机器人已经被清理掉了，没有找到此任务使用的机器人，因此重新执行此任务时会重新对任务进行派车申请！");
                        } else {
                            throw new ServiceException("由于此任务使用的机器人已经被清理掉了，没有找到此任务使用的机器人，不允许其他操作，只能重新执行此任务，请重新派车并且重新从第一个节点开始执行此流程！");
                        }
                    } else {
                        if (AgvUsageStatus.OCCUPIED.equals(agv.getUsageStatus())) {
                            String workFlowId = agv.getWorkFlowId();
                            if (flowId.equals(workFlowId)) {
                                if ("suspend".equals(operation)) {
                                    if (FlowStatusEnum.RUNNING.equals(workFlowById.getStatus())
                                            || FlowStatusEnum.NOT_START.equals(workFlowById.getStatus())
                                            || FlowStatusEnum.ERROR.equals(workFlowById.getStatus())
                                            || FlowStatusEnum.WAIT.equals(workFlowById.getStatus())) {
                                        //一键停止AGV当前车身上的任务
//                                        fleetProxyService.restartTaskPrevOneKeyStopAllAgvRunningMission(agvCode);
                                        workFlowExecutor.suspend(flowId);
                                        return ResultDTO.success();
                                    } else {
                                        throw new ServiceException("当前任务不处于执行中状态，无法挂起！");
                                    }
                                } else if ("recovery".equals(operation)) {
                                    if (FlowStatusEnum.ERROR.equals(workFlowById.getStatus())
                                            || FlowStatusEnum.SUSPEND.equals(workFlowById.getStatus())) {
                                        //一键停止AGV当前车身上的任务
                                        fleetProxyService.restartTaskPrevOneKeyStopAllAgvRunningMission(agvCode);
                                        workFlowExecutor.recovery(flowId);
                                        taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getId, task.getId()).set(Task::getStatus, TaskStatus.EXECUTING));
                                        return ResultDTO.success();
                                    } else {
                                        throw new ServiceException("当前任务不处于挂起或者异常状态，无法恢复！");
                                    }

                                } else if ("skipCurrentNodeRecovery".equals(operation)) {
                                    if (FlowStatusEnum.ERROR.equals(workFlowById.getStatus())
                                            || FlowStatusEnum.SUSPEND.equals(workFlowById.getStatus())) {
                                        //一键停止AGV当前车身上的任务
                                        fleetProxyService.restartTaskPrevOneKeyStopAllAgvRunningMission(agvCode);
                                        workFlowExecutor.skipCurrentNodeRecovery(flowId);
                                        taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getId, task.getId()).set(Task::getStatus, TaskStatus.EXECUTING));
                                        return ResultDTO.success();
                                    } else {
                                        throw new ServiceException("当前任务不处于挂起或者异常状态，无法恢复！");
                                    }
                                } else if ("restart".equals(operation)) {
                                    log.info("由于机器人未被释放，并且锁定此机器人的就是此任务，因此直接重新执行任务！");
                                    //一键停止AGV当前车身上的任务
                                    recordTaskLog(task.getCode(), flowId);
                                    fleetProxyService.restartTaskPrevOneKeyStopAllAgvRunningMission(agvCode);
                                    workFlowExecutor.restart(flowId);
                                    return ResultDTO.success("操作成功，即将从第一个节点重新开始执行此流程！");
                                } else {
                                    throw new ServiceException("没有这个操作：" + operation);
                                }
                            } else {
                                if ("restart".equals(operation)) {
                                    log.info("由于机器人已经被其他机器人占用，因此重新执行此任务时会重新对任务进行派车申请！");
                                    recordTaskLog(task.getCode(), flowId);
                                    checkAndStopFlow(flowId, workFlowById, agvCode);
                                    taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getId, task.getId()).set(Task::getStatus, TaskStatus.NOT_START));
                                    workFlowExecutor.executor(flowId);
                                    return ResultDTO.success("操作成功，由于机器人已经被其他机器人占用，因此重新执行此任务时会重新对任务进行派车申请！");
                                } else {
                                    throw new ServiceException("机器人已经被其他机器人锁定，不允许其他操作，只能重新执行此任务，请重新派车并且重新从第一个节点开始执行此流程！");
                                }
                            }
                        } else {
                            if ("restart".equals(operation)) {
                                log.info("由于机器人已经被释放，因此重新执行此任务时会重新对任务进行派车申请！");
                                recordTaskLog(task.getCode(), flowId);
                                checkAndStopFlow(flowId, workFlowById, agvCode);
                                taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getId, task.getId()).set(Task::getStatus, TaskStatus.NOT_START));
                                workFlowExecutor.executor(flowId);
                                return ResultDTO.success("操作成功，由于机器人已经被释放，因此重新执行此任务时会重新对任务进行派车申请！");
                            } else {
                                throw new ServiceException("机器人已经被释放，不允许其他操作，只能重新执行此任务，请重新派车并且重新从第一个节点开始执行此流程！");
                            }
                        }
                    }

                } else {
                    if ("restart".equals(operation)) {
                        log.info("由于此任务还未派车成功，因此重新执行此任务时会重新对任务进行派车申请！");
                        workFlowExecutor.executor(flowId);
                        return ResultDTO.success("操作成功，由于此任务还未派车成功，因此重新执行此任务时会重新对任务进行派车申请！");
                    } else {
                        throw new ServiceException("此任务还未派车成功，不允许其他操作，只能重新执行此任务，请重新派车并且重新从第一个节点开始执行此流程！");
                    }
                }
            }
        } else {
            throw new ServiceException("系统异常！没有获取到此流程的任务信息！");
        }
    }

    /**
     * 重新执行任务时记录日志
     *
     * @param taskCode 任务编号
     * @param flowId   流程ID
     */
    private void recordTaskLog(String taskCode, String flowId) {
        TaskLog taskLog = new TaskLog();
        taskLog.setTaskCode(taskCode);
        taskLog.setFlowId(flowId);
        taskLog.setTitle("未开始");
        taskLog.setTaskStatus(TaskStatus.NOT_START.name());
        taskLog.setRemark("重新执行");
        taskLogService.save(taskLog);
    }

    private void checkAndStopFlow(String flowId, WorkFlow workFlowById, String agvCode) {
        if (!FlowStatusEnum.NOT_START.equals(workFlowById.getStatus())
                && !FlowStatusEnum.STOPPED.equals(workFlowById.getStatus())
                && !FlowStatusEnum.COMPLETED.equals(workFlowById.getStatus())) {
            //一键停止AGV当前车身上的任务
            fleetProxyService.restartTaskPrevOneKeyStopAllAgvRunningMission(agvCode);
            workFlowExecutor.stop(flowId);
        }
    }

    @Override
    public WorkFlowTemplateEntity getFirst() {
        return workFlowTemplateService.getOne(Wrappers.<WorkFlowTemplateEntity>lambdaQuery().last(" limit 1 "));
    }

    @Override
    public String export(String[] ids) {
        ArrayList<WorkFlowTemplateExportDTO> workFlowTemplateExportDTOS = new ArrayList<>();
        for (String id : ids) {
            WorkFlowTemplateEntity workFlowTemplateEntity = workFlowTemplateService.getById(id);
            List<WorkFlowNodeTemplateEntity> workFlowNodeTemplateEntities = workFlowNodeTemplateService.list(Wrappers.<WorkFlowNodeTemplateEntity>lambdaQuery().eq(WorkFlowNodeTemplateEntity::getFlowTemplateId, id));
            List<FlowTemplateGlobalVariable> flowTemplateGlobalVariables = flowTemplateGlobalVariableService.list(Wrappers.<FlowTemplateGlobalVariable>lambdaQuery().eq(FlowTemplateGlobalVariable::getFlowTemplateId, id));
            List<FlowNodeTemplateGraphData> flowNodeTemplateGraphDataList = flowNodeTemplateGraphDataService.list(Wrappers.<FlowNodeTemplateGraphData>lambdaQuery().eq(FlowNodeTemplateGraphData::getFlowTemplateId, id));
            List<FlowTemplateComponentGlobalVariable> flowTemplateComponentGlobalVariables = flowTemplateComponentGlobalVariableService.list(Wrappers.<FlowTemplateComponentGlobalVariable>lambdaQuery().eq(FlowTemplateComponentGlobalVariable::getFlowTemplateId, id));
            List<FlowTemplateComponentOutputParams> flowTemplateComponentOutputParams = flowTemplateComponentOutputParamsService.list(Wrappers.<FlowTemplateComponentOutputParams>lambdaQuery().eq(FlowTemplateComponentOutputParams::getFlowTemplateId, id));

            WorkFlowTemplateExportDTO workFlowTemplateExportDTO = new WorkFlowTemplateExportDTO();
            workFlowTemplateExportDTO.setWorkFlowTemplateEntity(workFlowTemplateEntity);
            workFlowTemplateExportDTO.setWorkFlowNodeTemplateEntities(workFlowNodeTemplateEntities);
            workFlowTemplateExportDTO.setFlowTemplateGlobalVariables(flowTemplateGlobalVariables);
            workFlowTemplateExportDTO.setFlowNodeTemplateGraphDataList(flowNodeTemplateGraphDataList);
            workFlowTemplateExportDTO.setFlowTemplateComponentGlobalVariables(flowTemplateComponentGlobalVariables);
            workFlowTemplateExportDTO.setFlowTemplateComponentOutputParams(flowTemplateComponentOutputParams);

            workFlowTemplateExportDTOS.add(workFlowTemplateExportDTO);
        }
        String s = JSON.toJSONString(workFlowTemplateExportDTOS);

        return s;
    }

    @Override
    public void importFlow(MultipartFile file) {
        try {
            InputStream inputStream = file.getInputStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            StringBuilder stringBuilder = new StringBuilder();
            int data = inputStream.read();
            while (data != -1) {
                byteArrayOutputStream.write(data);
                data = inputStream.read();
            }
            stringBuilder.append(byteArrayOutputStream);
            String text = stringBuilder.toString();
            log.info("导入的文本内容:{}", text);
            List<WorkFlowTemplateExportDTO> workFlowTemplateExportDTOS = JSONArray.parseArray(text, WorkFlowTemplateExportDTO.class);
            for (WorkFlowTemplateExportDTO workFlowTemplateExportDTO : workFlowTemplateExportDTOS) {
                WorkFlowTemplateEntity workFlowTemplateEntity = workFlowTemplateExportDTO.getWorkFlowTemplateEntity();
                String code = workFlowTemplateEntity.getCode();
                int count = this.workFlowTemplateService.count(Wrappers.<WorkFlowTemplateEntity>lambdaQuery().eq(WorkFlowTemplateEntity::getCode, code));
                if (count > 0) {
                    log.warn("流程编号为：{}的流程已经存在，无法再重新导入！", code);
                    continue;
                }
                boolean save = this.workFlowTemplateService.save(workFlowTemplateEntity);
                if (save) {
                    List<WorkFlowNodeTemplateEntity> workFlowNodeTemplateEntities = workFlowTemplateExportDTO.getWorkFlowNodeTemplateEntities();
                    for (WorkFlowNodeTemplateEntity workFlowNodeTemplateEntity : workFlowNodeTemplateEntities) {
                        workFlowNodeTemplateEntity.setFlowTemplateId(workFlowTemplateEntity.getId());
                    }
                    workFlowNodeTemplateService.saveBatch(workFlowNodeTemplateEntities);
                    List<FlowTemplateGlobalVariable> flowTemplateGlobalVariables = workFlowTemplateExportDTO.getFlowTemplateGlobalVariables();
                    for (FlowTemplateGlobalVariable flowTemplateGlobalVariable : flowTemplateGlobalVariables) {
                        flowTemplateGlobalVariable.setFlowTemplateId(workFlowTemplateEntity.getId());
                    }
                    flowTemplateGlobalVariableService.saveBatch(flowTemplateGlobalVariables);
                    List<FlowNodeTemplateGraphData> flowNodeTemplateGraphDataList = workFlowTemplateExportDTO.getFlowNodeTemplateGraphDataList();
                    for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : flowNodeTemplateGraphDataList) {
                        flowNodeTemplateGraphData.setFlowTemplateId(workFlowTemplateEntity.getId());
                    }
                    flowNodeTemplateGraphDataService.saveBatch(flowNodeTemplateGraphDataList);
                    List<FlowTemplateComponentGlobalVariable> flowTemplateComponentGlobalVariables = workFlowTemplateExportDTO.getFlowTemplateComponentGlobalVariables();
                    for (FlowTemplateComponentGlobalVariable flowTemplateComponentGlobalVariable : flowTemplateComponentGlobalVariables) {
                        flowTemplateComponentGlobalVariable.setFlowTemplateId(workFlowTemplateEntity.getId());
                    }
                    flowTemplateComponentGlobalVariableService.saveBatch(flowTemplateComponentGlobalVariables);
                    List<FlowTemplateComponentOutputParams> flowTemplateComponentOutputParams = workFlowTemplateExportDTO.getFlowTemplateComponentOutputParams();
                    for (FlowTemplateComponentOutputParams flowTemplateComponentOutputParam : flowTemplateComponentOutputParams) {
                        flowTemplateComponentOutputParam.setFlowTemplateId(workFlowTemplateEntity.getId());
                    }
                    flowTemplateComponentOutputParamsService.saveBatch(flowTemplateComponentOutputParams);
                }
            }
        } catch (Exception e) {
            log.error("导入失败:{}", Throwables.getStackTraceAsString(e));
        }
    }
}
