package com.youibot.tms.biz.forest.dto.fleet5;

import com.youibot.tms.biz.forest.enums.VehicleStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;



/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "VehicleDetailApiDTO",description = "机器人详情")
public class VehicleDetailApiDTO extends VehicleApiDTO {

    @ApiModelProperty(value = "机器人类型编码", position = 1)
    private String vehicleTypeCode;

    @ApiModelProperty(value = "机器人组编码", position = 2)
    private String vehicleGroupCode;

    @ApiModelProperty(value = "连接状态 未连接=disconnect 已连接=connect", position = 3)
    protected String connectStatus;

    @ApiModelProperty(value = "调度模式 手动=ManualSchedule 自动=AutoSchedule", position = 4)
    protected String scheduleMode;

    @ApiModelProperty(value = "位置状态", position = 5)
    private PositionStatus positionStatus;

    @ApiModelProperty(value = "运行状态", position = 6)
    private RunningStatus runningStatus;

    @ApiModelProperty(value = "是否有异常通知", position = 6)
    private Boolean abnormalNotice;

    @ApiModelProperty(value = "异常信息", dataType = "List", position = 7)
    private List<ErrorInfo> errorInfoList;

    @ApiModelProperty(value = "电池信息", position = 8)
    private List<Battery> batteryList;

    @ApiModelProperty(value = "工控机运行数据", position = 9)
    private MachineStatus machineStatus;

    @ApiModelProperty(value = "储位信息", position = 9)
    private List<MosStorageInfo> storageInfoList;

    @ApiModelProperty(value = "已规划路径", position = 10)
    private List<VehicleDetailPathApiDTO> planedPaths;

    @ApiModelProperty(value = "已执行路径", position = 11)
    private List<VehicleDetailPathApiDTO> executedPaths;

    @ApiModelProperty(value = "占用路径", position = 12)
    private List<VehicleDetailPathApiDTO> occupyPaths;

    @ApiModelProperty(value = "任务信息", position = 13)
    private TaskDetailApiDTO taskInfo;


    public VehicleStatus getVehicleStatus() {
        VehicleStatus agvStatusEnum = null;
        // 是否故障或离线（忽略大小写）
        if ("disconnect".equalsIgnoreCase(this.connectStatus)) {
            agvStatusEnum = VehicleStatus.OFFLINE;
        } else if (this.getRunningStatus()!=null && "Abnormal".equals(this.getRunningStatus().getAbnormalStatus())) {
            agvStatusEnum = VehicleStatus.ERROR;
        }
        // 是否充电中
        if(this.getBatteryList()!=null && this.getBatteryList().size()>0 ){
            Battery battery = this.getBatteryList().get(0);
            if(battery.getCharging()){
                agvStatusEnum = VehicleStatus.CHARGE;
            }
        }

        // 运行状态
        if (agvStatusEnum == null && this.getRunningStatus()!=null) {
            String workStatus = this.getRunningStatus().getWorkStatus();
            if("Free".equals(workStatus)){
                agvStatusEnum = VehicleStatus.IDLE;
            }else{
                agvStatusEnum = VehicleStatus.RUN;
            }

        }
        // null 表示未知状态
        return agvStatusEnum;
    }

    public Double getBatterNumber(){
        if(this.getBatteryList()!=null && this.getBatteryList().size()>0){
            //取所有Battery的rate平均值
            return this.getBatteryList().stream().mapToDouble(Battery::getRate).average().orElse(0);
        }
        return 0.0;
    }


}
