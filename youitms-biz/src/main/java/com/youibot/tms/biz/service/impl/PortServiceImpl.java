package com.youibot.tms.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.ActionParam;
import com.youibot.tms.biz.entity.Port;
import com.youibot.tms.biz.entity.PortEq;
import com.youibot.tms.biz.entity.PortStocker;
import com.youibot.tms.biz.enums.PortType;
import com.youibot.tms.biz.mapper.PortEqMapper;
import com.youibot.tms.biz.mapper.PortStockerMapper;
import com.youibot.tms.biz.service.PortService;
import com.youibot.tms.common.core.domain.entity.SysRole;
import com.youibot.tms.common.core.domain.entity.SysUser;
import com.youibot.tms.common.core.domain.model.LoginUser;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.SecurityUtils;
import com.youibot.tms.common.utils.StringUtils;
import com.youibot.tms.common.utils.ToolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PortServiceImpl implements PortService {

    @Resource
    private PortEqMapper portEqMapper;

    @Resource
    private PortStockerMapper portStockerMapper;

    @Override
    public Port getByTypeAndId(PortType type, Long portId) {
        if (PortType.PORT_EQ.equals(type)) {
            return portEqMapper.selectById(portId);
        } else if (PortType.PORT_STOCKER.equals(type)) {
            return portStockerMapper.selectById(portId);
        } else {
            return null;
        }
    }

    public Long getZoneId(PortType type, Long portId){
        if (PortType.PORT_EQ.equals(type)) {
            PortEq portEq = portEqMapper.selectById(portId);
            if(portEq!=null){
                return portEq.getZoneAreaId();
            }
        } else if (PortType.PORT_STOCKER.equals(type)) {
            PortStocker portStocker = portStockerMapper.selectById(portId);
            if(portStocker!=null){
                return portStocker.getZoneStockerId();
            }
        }
        return null;
    }

    @Override
    public ActionParam getFirstActionParam(Port port) {
        List<ActionParam> actionParams = port.getActionParam();
        if (ToolUtil.isEmpty(actionParams)) {
            //Port中的作业点参数为空，有可能是因为列表查询没有查出actionParams字段
            //此时再selectById即可
            port = this.getByTypeAndId(port.getPortType(), port.getId());
            if (ToolUtil.isNotEmpty(port.getActionParam())) {
                actionParams = port.getActionParam();
            }
        }

        if (ToolUtil.isEmpty(actionParams)) {
            throw new BusinessException(BizErrorCode.TASK_PORT_NO_ACTION_PARAM, port.getFullName());
        }else{
            return actionParams.get(0);
        }
    }

    @Override
    public List<PortStocker> getAllPortStocker() {
        QueryWrapper<PortStocker> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("priority");
        return portStockerMapper.selectList(queryWrapper);

    }

    public Port getByCode(String code){

        List<PortEq> eqList = portEqMapper.selectListByCode(code);

        List<PortStocker> stockerList = portStockerMapper.selectListByCode(code);

        if(ToolUtil.isEmpty(eqList) && ToolUtil.isEmpty(stockerList)){
            return null;
        }else if(ToolUtil.isNotEmpty(eqList) && ToolUtil.isNotEmpty(stockerList)){
            throw new BusinessException(BizErrorCode.PORT_CODE_CONFLICT,code);
        }else if(eqList.size()>1 || stockerList.size()>1){
            throw new BusinessException(BizErrorCode.PORT_CODE_CONFLICT,code);
        }else if(eqList.size() == 1){
            return eqList.get(0);
        }else if(stockerList.size() ==1){
            return stockerList.get(0);
        }
        return null;

    }

    public void checkCodeUnique(Port entity){
        if(entity!=null && StringUtils.isNotEmpty(entity.getCode())) {
            Port port = this.getByCode(entity.getCode());
            if (port != null && !port.getId().equals(entity.getId())) {
                throw new BusinessException(BizErrorCode.PORT_CODE_EXISTED);
            }
        }
    }

    public List<Port> list(QueryWrapper eqWrapper,QueryWrapper stockerWrapper){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser))
        {
            SysUser currentUser = loginUser.getUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin())
            {
                List<SysRole> role = currentUser.getRoles();
                if(ToolUtil.isNotEmpty(role)){
                    List<Long> roleIds = role.stream().map(SysRole::getId).collect(Collectors.toList());
                    String roleIdsStr = StringUtils.join(roleIds, ",");
                    eqWrapper.apply(StringUtils.format( " zone_area_id in (SELECT zone_id FROM sys_role_zone WHERE role_id in ({})) " ,roleIdsStr));
                    stockerWrapper.apply(StringUtils.format(" zone_stocker_id in ( SELECT zone_id FROM sys_role_zone WHERE role_id in ({}) ) ",roleIdsStr));
                }
            }
        }
        List<PortEq> eqList = portEqMapper.selectList(eqWrapper);
        List<PortStocker> stockerList = portStockerMapper.selectList(stockerWrapper);
        List<Port> result = new ArrayList<>();
        if(ToolUtil.isNotEmpty(eqList) ){
            result.addAll(eqList);
        }
        if(ToolUtil.isNotEmpty(stockerList)){
            result.addAll(stockerList);
        }
        return result;
    }
}
