package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "TaskTypeApiDTO",description = "任务流程")
public class TaskTypeApiDTO {

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    @ApiModelProperty(value = "优先级 最高=5 较高=4 高=3 中=2 低=1, 默认低", position = 3)
    private Integer priority;

}
