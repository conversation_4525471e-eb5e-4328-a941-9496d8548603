package com.youibot.tms.biz.forest.dto.fleet5;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2022/12/14/14:16
 * @Description: 异常信息
 */
@Data
@ApiModel(value = "ErrorInfo", description = "异常信息")
public class ErrorInfo {
    /**
     * 自定义异常码，机器人急停作为异常码推送
     */
    @ApiModelProperty(value = "编码")
    private Integer code;
    /**
     * 执行任务出现的异常，对应的任务ID
     */
    @JsonIgnore
    private String taskId;
    /**
     * 执行任务出现的异常，对应的指令ID
     */
    @JsonIgnore
    private String instructId;
    /**
     * 等级 1：普通 2：警告 3：错误
     */
    @ApiModelProperty(value = "等级 1：普通 2：警告 3：错误", position = 3)
    private Integer level;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型", position = 4)
    private String type;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", position = 5)
    private String description;
    /**
     * 解决措施
     */
    @ApiModelProperty(value = "解决措施", position = 6)
    private String solution;
    /**
     * 失效时间
     */
    @JsonIgnore
    private Integer invalidTime;
    /**
     * 系统来源
     */
    @ApiModelProperty(value = "系统来源", position = 8)
    private String sourceSystem;

    /**
     * 异常通知ID - 关联AbnormalApiDTO#id，用于RTMS系统去重
     */
    @ApiModelProperty(value = "异常通知ID", position = 9)
    private String abnormalNoticeId;
}
