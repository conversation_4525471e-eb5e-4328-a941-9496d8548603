package com.youibot.tms.biz.forest;

import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.DataVariable;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.PostRequest;
import com.youibot.tms.biz.api.dto.TaskFleetCallbackDTO;

@BaseRequest()
public interface TmsClient {

    @PostRequest(value = "${callbackUrl}", contentType = "application/json"  ,interceptor = {},retryCount = 0)
    void missionWorkCallback(@DataVariable("callbackUrl") String callbackUrl,@JSONBody TaskFleetCallbackDTO params);
}
