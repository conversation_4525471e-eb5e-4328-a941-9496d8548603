package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.api.request.TaskStatisticsRequest;
import com.youibot.tms.biz.entity.TaskStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <NAME_EMAIL> on 2023/2/17.
 *
 * <AUTHOR>
 * @date 2023/2/17 17:35
 */
public interface TaskStatisticsMapper extends BaseMapper<TaskStatistics> {

    /**
     * 根据小时分组
     *
     * @param taskStatisticsRequest 实体类
     */
    List<TaskStatistics> findListByHour(@Param("taskStatisticsRequest") TaskStatisticsRequest taskStatisticsRequest,@Param("agvCodes") List<String> agvCodes);

    /**
     * 根据天和小时分组，但是返回归属日期
     *
     * @param taskStatisticsRequest 实体类
     */
    List<TaskStatistics> findListByHourToDay(@Param("taskStatisticsRequest") TaskStatisticsRequest taskStatisticsRequest,@Param("agvCodes") List<String> agvCodes);

    /**
     * 根据日期和时间分组查询，返回月的数据
     *
     * @param taskStatisticsRequest 請求參數
     */
    List<TaskStatistics> findListByHourToMonth(@Param("taskStatisticsRequest") TaskStatisticsRequest taskStatisticsRequest,@Param("agvCodes") List<String> agvCodes);

    /**
     * 根据日期和时间分组查询，返回月的数据
     *
     * @param agvCode  机器人编号
     * @param dateHour 时间
     */
    TaskStatistics findTaskStatisticsById(@Param("agvCode") String agvCode, @Param("dateHour") String dateHour);

    /**
     * 根据机器人编号和时间来更新统计的数据
     *
     * @param taskStatistics 统计的数据
     */
    void updateByAgvCodeAndDateHour(@Param("taskStatistics") TaskStatistics taskStatistics);

    /**
     * 批量新增统计数据
     *
     * @param taskStatisticsList 统计的数据
     */
    void saveBatchByMultiId(@Param("list") List<TaskStatistics> taskStatisticsList);

    /**
     * 根据归属日期批量删除数据
     *
     * @param attributionDate 归属日期
     */
    void batchDeleteByAttributionDate(@Param("list") List<String> attributionDate);

    TaskStatistics today();

    List<TaskStatistics> findListByMonth(@Param("taskStatisticsRequest") TaskStatisticsRequest taskStatisticsRequest);

    List<TaskStatistics> findListByDay(@Param("taskStatisticsRequest") TaskStatisticsRequest taskStatisticsRequest);
}
