package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yaoh<PERSON><PERSON>
 * @Date: 2022/12/14/16:03
 * @Description: 电池状态信息
 */
@Data
public class Battery {
    /**
     * 电池序列号
     */
    @ApiModelProperty(value = "电池序列号")
    private String batteryId;
    /**
     * 充电=true
     * 放电=false
     */
    @ApiModelProperty(value = "是否充电 充电=true, 放电=false", position = 1)
    private Boolean charging;
    /**
     * 电量百分比 单位：%
     */
    @ApiModelProperty(value = "电量百分比 单位：%", position = 2)
    private Double rate;
    /**
     * 电池电压 单位：V
     */
    @ApiModelProperty(value = "电池电压 单位：V", position = 3)
    private Double voltage;
    /**
     * 电池电流 单位：A
     */
    @ApiModelProperty(value = "电池电流 单位：A", position = 4)
    private Double current;
    /**
     * 电池充电次数
     */
    @ApiModelProperty(value = "电池充电次数", position = 5)
    private Integer chargingTimes;
    /**
     * 电池温度 单位：摄氏度
     */
    @ApiModelProperty(value = "电池温度 单位：摄氏度", position = 6)
    private Double temperature;
    /**
     * 电池健康度 当前健康状态，寿命百分比[0,100]
     */
    @ApiModelProperty(value = "电池健康度 当前健康状态，寿命百分比[0,100]", position = 7)
    private Double health;
}
