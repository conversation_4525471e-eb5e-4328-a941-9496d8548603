package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VehicleApiDTO",description = "机器人信息")
public class VehicleApiDTO {

    @ApiModelProperty(value = "机器人编号")
    protected String vehicleCode;

    @ApiModelProperty(value = "机器人IP地址", position = 1)
    protected String ip;

    @ApiModelProperty(value = "机器人名称")
    protected String name;

    @ApiModelProperty(value = "机器人MAC地址", position = 2)
    protected String mac;

    @ApiModelProperty(value = "Pilot版本", position = 3)
    protected String pilotVersion;

    @ApiModelProperty(value = "Mos版本", position = 4)
    protected String mosVersion;
}
