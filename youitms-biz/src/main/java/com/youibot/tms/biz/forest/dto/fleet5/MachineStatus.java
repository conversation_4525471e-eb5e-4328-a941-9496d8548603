package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2022/12/14/16:03
 * @Description: 工控机运行数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "MachineStatus",description = "工控机运行数据")
public class MachineStatus {

    @ApiModelProperty(value = "CPU占用率 单位：%")
    private Double cpuOcp;

    @ApiModelProperty(value = "内存占用率 单位：%")
    private Double memOcp;

    @ApiModelProperty(value = "剩余硬盘空间 单位：MB")
    private Double spaceFree;

    @ApiModelProperty(value = "CPU温度 单位：摄氏度")
    private Double temperature;
}
