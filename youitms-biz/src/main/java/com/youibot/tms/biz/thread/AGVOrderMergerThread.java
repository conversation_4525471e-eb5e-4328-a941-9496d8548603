package com.youibot.tms.biz.thread;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.entity.*;
import com.youibot.tms.biz.enums.AgvStoragePortStatus;
import com.youibot.tms.biz.enums.TaskNodeAction;
import com.youibot.tms.biz.flow.common.listener.CommonWorkFlowListener;
import com.youibot.tms.biz.secs.handler.S66F3CommandHandler;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.system.service.ISysConfigService;
import com.youibot.tms.workflow.core.executor.WorkFlowExecutor;
import com.youibot.tms.workflow.core.flow.WorkFlow;
import com.youibot.tms.workflow.entity.WorkFlowTemplateEntity;
import com.youibot.tms.workflow.service.WorkFlowService;
import com.youibot.tms.workflow.service.WorkFlowTemplateService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


import static com.youibot.tms.common.constant.SysConfigKeyConstants.AGV_ORDER_MERGE_RULE;

/**
 * AGV订单合并线程
 * 该线程会从数据库中捞取进行中的FromToRecord和AgvStoragePort，
 * 使用AGVOrderMerger来生成workFlow
 *
 * <AUTHOR>
 */
@Component
public class AGVOrderMergerThread extends Thread {
    private static final Logger logger = LoggerFactory.getLogger(AGVOrderMergerThread.class);

    @Lazy
    @Autowired
    private FromToRecordService fromToRecordService;

    @Autowired
    private AgvStoragePortService agvStoragePortService;

    @Autowired
    private WorkFlowService workFlowService;

    @Autowired
    private PortService portService;

    @Autowired
    private WorkFlowTemplateService workFlowTemplateService;

    @Autowired
    private FlowTemplateAssistService flowTemplateAssistService;

    @Autowired
    private WorkFlowExecutor workFlowExecutor;

    @Autowired
    private CommonWorkFlowListener commonWorkFlowListener;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private FleetProxyService fleetProxyService;

    /**
     * 线程休眠时间（毫秒），默认180秒（3分钟）
     */
    @Value("${agv.order.merger.thread.sleep:180000}")
    private long sleepTime;

    /**
     * 线程休眠时间最小值（毫秒），默认60秒（1分钟）
     */
    @Value("${agv.order.merger.thread.min.sleep:60000}")
    private long minSleepTime;

    /**
     * 线程休眠时间最大值（毫秒），默认300秒（5分钟）
     */
    @Value("${agv.order.merger.thread.max.sleep:300000}")
    private long maxSleepTime;

    /**
     * 车厢容量
     */
    private int carriageCapacity;

    /**
     * 是否启用线程
     */
    @Value("${agv.order.merger.thread.enabled:true}")
    private boolean enabled;

    /**
     * 线程运行状态
     */
    private final AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 线程等待对象，用于唤醒线程
     */
    private final Object waitLock = new Object();

    /**
     * 旧版本号，用于版本号机制
     */
    private final AtomicLong oldVersion = new AtomicLong(-2);

    /**
     * 新版本号，用于版本号机制
     */
    private final AtomicLong newVersion = new AtomicLong(0);



    @Async
    @EventListener(ContextRefreshedEvent.class)
    public void contextRefreshedEvent() {
        // 在这里执行你的方法
        // 所有的 bean 都已经初始化完成
//        init();
    }

    /**
     * 初始化并启动线程
     */
    public void init() {
        if (enabled) {
            this.setName("agv-order-merger-thread");
            this.setDaemon(true);
            this.start();
            logger.info("AGV订单合并线程已启动，休眠时间：{}毫秒", sleepTime);
        } else {
            logger.info("AGV订单合并线程未启用");
        }
    }

    @Override
    public void run() {
        running.set(true);
        logger.info("AGV订单合并线程开始运行");

        while (running.get()) {
            try {
                // 处理订单合并逻辑
                processOrderMerge();

                // 线程等待指定时间或被唤醒
                synchronized (waitLock) {
                    try {
                        if( isVersionsEqual() ) {
                            logger.debug("AGV订单合并线程进入休眠状态，休眠时间：{}毫秒", sleepTime);
                            waitLock.wait(sleepTime);
                            logger.debug("AGV订单合并线程被唤醒或休眠时间到期");
                        }else {
                            logger.debug("AGV订单合并线程继续处理：{}毫秒", minSleepTime);
                        }


                    } catch (InterruptedException e) {
                        logger.warn("AGV订单合并线程等待被中断", e);
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } catch (Exception e) {
                logger.error("AGV订单合并线程执行过程中发生异常", e);
                // 发生异常后继续执行，不中断线程
                try {
                    // 发生异常时，等待一小段时间再继续
                    Thread.sleep(minSleepTime);
                } catch (InterruptedException ie) {
                    logger.warn("AGV订单合并线程异常后等待被中断", ie);
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        logger.info("AGV订单合并线程已停止");
    }

    /**
     * 处理订单合并逻辑
     *
     */
    private void processOrderMerge() {
        // 获取当前的新版本号
        long currentNewVersion = newVersion.get();
        // 获取当前的旧版本号
        long currentOldVersion = oldVersion.get();

        boolean isNeedWait = false;
        // 如果新版本号和旧版本号相同，则不需要处理
        if (isVersionsEqual()) {
            logger.debug("版本号相同，不需要处理订单合并逻辑: version={}", currentNewVersion);
            return;
        }

        logger.info("开始处理订单合并逻辑: oldVersion={}, newVersion={}", currentOldVersion, currentNewVersion);
        try {
            // 1. 获取进行中的FromToRecord记录
            List<FromToRecord> currentRecords = fromToRecordService.current();
            if (currentRecords.isEmpty()) {
                logger.debug("没有进行中的FromToRecord记录");
                return;
            }

            // 过滤出没有关联到工作流的记录，并且过滤掉已经使用空规则合并的订单
            List<FromToRecord> unassignedRecords = currentRecords.stream()
                    .filter(record -> (StringUtils.isBlank(record.getFlowId()))
                            && (record.getEmptyRuleMerged() == null || !record.getEmptyRuleMerged()))
                    .collect(Collectors.toList());

            if (unassignedRecords.isEmpty()) {
                isNeedWait = true;
                logger.debug("没有可以处理的FromToRecord记录");
                return;
            }

            logger.info("获取到{}条未分配的FromToRecord记录", unassignedRecords.size());

            // 2. 获取在线AGV列表
            List<String> agvCodes = getOnlineAgvCodes();

            logger.info("获取到的AGV编码列表: {}", agvCodes);

            // 如果agvCodes为空，则使用默认值
            if (agvCodes.isEmpty()) {
                logger.info("没有获取到在线AGV，使用默认值");
                return;
            }
            List<FromToRecord> assignedRecords = Lists.newArrayList();
            for (String agvCode : agvCodes) {
                unassignedRecords.removeAll(assignedRecords);
                if(CollectionUtils.isEmpty(unassignedRecords)){
                    logger.debug("没有可以处理的FromToRecord记录");
                    break;
                }

                List<AgvStoragePort> storagePorts = agvStoragePortService.getAllList(agvCode);
                List<AgvStoragePort> availablePorts = filterAvailablePorts(storagePorts);

                if (availablePorts.isEmpty()) {
                    logger.debug("AGV[{}]没有可用的车厢储位", agvCode);
                    continue;
                }

                logger.info("AGV[{}]有{}个可用的车厢储位", agvCode, availablePorts.size());

                // 设置车厢容量为可用储位的数量
                carriageCapacity = availablePorts.size();

                // 3. 使用AGVOrderMerger合并订单
                // 从SysConfig中获取合并模式
                Set<AGVOrderMerger.MergeMode> mergeModes = getMergeModes();

                logger.info("使用的合并模式: {}, 车厢容量: {}", mergeModes, carriageCapacity);

                AGVOrderMerger orderMerger = new AGVOrderMerger(carriageCapacity, mergeModes);
                List<List<FromToRecord>> mergedGroups = orderMerger.mergeOrders(unassignedRecords);

                if (mergedGroups.isEmpty()) {
                    logger.debug("没有可合并的订单组");

                    // 如果没有合并组，则将每个记录作为单独的组
                    for (FromToRecord record : unassignedRecords) {
                        List<FromToRecord> singleGroup = new ArrayList<>();
                        singleGroup.add(record);
                        List<FromToRecord> assignedGroup = generateWorkflow(singleGroup, agvCode);
                        if (assignedGroup != null) {
                            assignedRecords.addAll(assignedGroup);
                        }
                    }
                    continue;
                }

                logger.info("合并后生成{}个订单组", mergedGroups.size());

                // 4. 为每个合并组生成工作流
                for (List<FromToRecord> group : mergedGroups) {
                    List<FromToRecord> assignedGroup = generateWorkflow(group, agvCode);
                    if (assignedGroup != null) {
                        assignedRecords.addAll(assignedGroup);
                    }
                }
                isNeedWait = true ;
            }
        } catch (Exception e) {
            logger.error("处理订单合并逻辑时发生异常", e);
            throw e;
        } finally {
            // 处理完成后更新旧版本号
            // 如果新版本号和旧版本号相差为1，则直接将旧版本号设置为新版本号
            // 否则，将旧版本号增加1，以便下次继续处理
            long newVersionValue = newVersion.get();
            long oldVersionValue = oldVersion.get();

            if (newVersionValue - oldVersionValue == 1) {
                // 如果相差为1，则直接将旧版本号设置为新版本号
                oldVersion.set(newVersionValue);
                logger.info("处理完成，旧版本号更新为新版本号: oldVersion={}, newVersion={}", newVersionValue, newVersionValue);
            } else {
                // 如果相差大于1，则将旧版本号增加1，以便下次继续处理
                if(isNeedWait){
                    oldVersion.incrementAndGet();
                    logger.info("处理完成，旧版本号增加1: oldVersion={}, newVersion={}", oldVersion.get(), newVersionValue);
                }

            }
        }
    }



    /**
     * 从SysConfig中获取合并模式
     *
     * @return 合并模式集合
     */
    private Set<AGVOrderMerger.MergeMode> getMergeModes() {
        Set<AGVOrderMerger.MergeMode> mergeModes = new HashSet<>();
        String mergeModesStr = sysConfigService.selectConfigValueByKey(AGV_ORDER_MERGE_RULE);
        logger.info("从SysConfig中获取合并模式: {}", mergeModesStr);

        if (StringUtils.isNotEmpty(mergeModesStr)) {
            try {
                // 解析合并模式字符串，格式为逗号分隔的枚举序数
                String[] modeOrdinals = mergeModesStr.split(",");
                AGVOrderMerger.MergeMode[] allModes = AGVOrderMerger.MergeMode.values();

                for (String ordinalStr : modeOrdinals) {
                    if (StringUtils.isNotEmpty(ordinalStr)) {
                        try {
                            int ordinal = Integer.parseInt(ordinalStr.trim());
                            if (ordinal >= 0 && ordinal < allModes.length) {
                                mergeModes.add(allModes[ordinal]);
                            } else {
                                logger.warn("无效的合并模式序数: {}", ordinal);
                            }
                        } catch (NumberFormatException e) {
                            logger.warn("无效的合并模式序数字符串: {}", ordinalStr, e);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("解析合并模式字符串时发生异常: {}", mergeModesStr, e);
            }
        }

        // 如果没有配置合并模式，则使用空集合
        // 空集合表示使用规则1，同一commandId只能合并到一组

        return mergeModes;
    }

    /**
     * 获取在线AGV编码列表
     *
     * @return 在线AGV编码列表
     */
    private List<String> getOnlineAgvCodes() {
        List<String> agvCodes = new ArrayList<>();
        try {
            List<AgvDTO> onlineAgvs = fleetProxyService.listOnlineAgvs();
            if (onlineAgvs != null && !onlineAgvs.isEmpty()) {
                for (AgvDTO agv : onlineAgvs) {
                    // 使用getAgvCode或getId获取AGV编码
                    String agvCode = agv.getAgvCode();
                    if (StringUtils.isEmpty(agvCode)) {
                        // 如果getAgvCode返回空，则尝试使用getId
                        agvCode = agv.getId();
                    }

                    if (StringUtils.isNotEmpty(agvCode)) {
                        agvCodes.add(agvCode);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取在线AGV列表时发生异常", e);
        }
        return agvCodes;
    }

    /**
     * 过滤出可用的车厢储位
     */
    private List<AgvStoragePort> filterAvailablePorts(List<AgvStoragePort> storagePorts) {
        return storagePorts.stream()
                .filter(port -> port.getStatus() == AgvStoragePortStatus.FREE)
                .collect(Collectors.toList());
    }

    /**
     * 为订单组生成工作流
     */
    private List<FromToRecord> generateWorkflow(List<FromToRecord> group, String agvCode) {
        try {
            if (group.isEmpty()) {
                logger.warn("订单组为空，无法生成工作流");
                return null;
            }
            List<S66F3CommandHandler.Location> froms = new ArrayList<>();
            List<S66F3CommandHandler.Location> tos = new ArrayList<>();
            List<FromToRecordWrapper> wrappers = new ArrayList<>();
            // 生成命令ID
            String commandId = "CMD_" + System.currentTimeMillis();
//            AGVOrderMerger.MergeMode mergeMode = group.get(0).getMergeMode();
            for (FromToRecord record : group){


            // 获取第一条记录的起点和终点
            FromToRecord firstRecord = record;
            String fromCode = firstRecord.getFromCode();
            String toCode = firstRecord.getToCode();

            // 获取起点和终点的Port信息
            Port fromPort = portService.getByCode(fromCode);
            Port toPort = portService.getByCode(toCode);

            if (fromPort == null || toPort == null) {
                logger.error("无法找到起点[{}]或终点[{}]的Port信息", fromCode, toCode);
                return null;
            }


            String lotId = "LOT_" + System.currentTimeMillis();

            // 创建任务全局变量

            // 创建起点位置信息
            S66F3CommandHandler.Location fromLocation = new S66F3CommandHandler.Location(fromPort.getMapCode(), fromPort.getActionParam().get(0).getMarkerCode(), false, true, commandId, commandId, fromPort.getId(), fromPort.getPortType(), fromPort.getCode(), lotId, TaskNodeAction.PICK_UP, fromPort.getCode(), fromPort.getCode(), record.getFormToSeq());


            S66F3CommandHandler.Location toLocation = new S66F3CommandHandler.Location(toPort.getMapCode(), toPort.getActionParam().get(0).getMarkerCode(), false, true, commandId, commandId, toPort.getId(), toPort.getPortType(), toPort.getCode(), lotId, TaskNodeAction.PUT_DOWN, toPort.getCode(), toPort.getCode(), record.getFormToSeq());


            FromToRecordWrapper wrapper = FromToRecordWrapper.create(
                        record,
                        fromLocation ,
                        toLocation
                );

            // 设置任务全局变量
            froms.add(fromLocation);
            tos.add(toLocation);
            wrappers.add( wrapper ) ;



            }

         String tempCode= "mergeMode.getModeName()" ;

            // 获取工作流模板
          WorkFlowTemplateEntity workFlowTemplate = workFlowTemplateService.getWorkFlowTemplateByCode( tempCode );
            if (workFlowTemplate == null) {
                logger.error("找不到{}工作流模板", tempCode );
                return null;
            }
            JSONObject taskGlobalVariable = new JSONObject( );
            if("TMP_ID_CONTINUOUS_ROUTE".equals(tempCode)){
                taskGlobalVariable.put("fromTos", JSON.parseArray(JSON.toJSONString(froms)));

            }else{
                taskGlobalVariable.put("froms", JSON.parseArray(JSON.toJSONString(froms)));
                taskGlobalVariable.put("tos",  JSON.parseArray(JSON.toJSONString(tos)));
            }


            // 创建任务
            Task task = taskService.createTaskByFlowTemplateId(workFlowTemplate.getId(), taskGlobalVariable);

            // 初始化工作流
            WorkFlow workFlow = flowTemplateAssistService.initWorkflow(workFlowTemplate.getId(), taskGlobalVariable, commonWorkFlowListener);
            String flowId = workFlow.getId();

            // 更新任务信息
            task.setFlowId(flowId);
            task.setCustomTaskId(commandId);
            task.setPriority(1);
            List<String> agvCodes = new ArrayList<>();
            agvCodes.add(agvCode);
            task.setNeedAgvCodes(agvCodes);
            taskService.updateById(task);

            // 记录FromToRecord与流程的关联
            fromToRecordService.record(group, flowId);

            // 执行工作流
            workFlowExecutor.executor(flowId);

            logger.info("为AGV[{}]生成工作流[{}]，包含{}条记录", agvCode, flowId, group.size());
        } catch (Exception e) {
            logger.error("为订单组生成工作流时发生异常", e);
            throw e;
        }
        return group;
    }

    /**
     * 检查版本号是否相等
     * @return true if versions are equal, false otherwise
     */
    private boolean isVersionsEqual() {
        return newVersion.get() == oldVersion.get();
    }

    /**
     * 停止线程
     */
    public void stopThread() {
        running.set(false);
        this.interrupt();
        logger.info("AGV订单合并线程停止命令已发送");
    }

    /**
     * 检查线程是否正在运行
     */
    public boolean isRunning() {
        return running.get();
    }

    /**
     * 唤醒线程，立即处理订单合并逻辑
     */
    public void wakeUp() {
        synchronized (waitLock) {
            logger.info("AGV订单合并线程被唤醒");
            waitLock.notify();
        }
    }

    /**
     * 增加版本号并唤醒线程
     * 如果新版本号比旧版本号大，则唤醒线程
     *
     * @return 新的版本号
     */
    public long incrementVersionAndWakeUp() {
        long newVersionValue = newVersion.incrementAndGet();
        long oldVersionValue = oldVersion.get();

        if (newVersionValue > oldVersionValue) {
            logger.info("AGV订单合并线程版本号更新: old={}, new={}", oldVersionValue, newVersionValue);
            wakeUp();
        }

        return newVersionValue;
    }
}
