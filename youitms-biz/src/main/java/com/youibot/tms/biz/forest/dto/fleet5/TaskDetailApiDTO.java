package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务详情
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "TaskDetailApiDTO",description = "任务详情")
public class TaskDetailApiDTO {

    @ApiModelProperty(value = "任务类型编码")
    private String taskTypeCode;

    @ApiModelProperty(value = "任务编号", position = 1)
    private String taskNo;

    @ApiModelProperty(value = "外部任务编码", position = 2)
    private String externalTaskNo;

    @ApiModelProperty(value = "任务名称", position = 3)
    private String name;

    @ApiModelProperty(value = "任务状态 创建=Create 执行中=Running 已完成=Finished 取消=Cancel", position = 4)
    private String status;

    @ApiModelProperty(value = "优先级 最高=5 较高=4 高=3 中=2 低=1, 默认低", position = 5)
    private Integer priority;

    @ApiModelProperty(value = "上游回调地址", position = 6)
    private String callbackUrl;

    @ApiModelProperty(value = "输入参数", position = 7)
    private Map<String, Object> paramIn;

    @ApiModelProperty(value = "输出参数", position = 8)
    private Map<String, Object> paramOut;

    @ApiModelProperty(value = "节点详情", position = 9)
    private List<TaskNodeDetailApiDTO> taskNodeDetails;

    @ApiModelProperty(value = "开始时间", position = 10)
    private Date startTime;

    @ApiModelProperty(value = "结束时间", position = 11)
    private Date endTime;

    @ApiModelProperty(value = "创建任务来源", position = 12)
    private String source;

    @ApiModelProperty(value = "提示信息，由结束节点设置", position = 13)
    private String shutdownMsg;

}
