package com.youibot.tms.biz.service.impl.fleet;

import com.youibot.tms.biz.forest.FleetClient;
import com.youibot.tms.biz.forest.dto.AgvMapDTO;
import com.youibot.tms.biz.forest.request.AgvMapRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: Fleet48ProxyServiceImpl
 * @Description: fleet请求相关辅助类
 * @date 2021-03-03 11:47:13
 */
@Slf4j
@Component("fleetProxyService")
@ConditionalOnExpression(value = "(!'${fleet.version:}'.equals('simulation') )&& ${fleet.version:4.8}<4.8")
public class Fleet47ProxyServiceImpl extends Fleet48ProxyServiceImpl {

    @Autowired
    private FleetClient fleetClient;

    @Value("${fleet.version:}")
    private String version;

    @Override
    @PostConstruct
    public void init() {
        log.info("fleet.version={},启用Fleet47Proxy", version);
    }

    @Override
    public List<AgvMapDTO> getAllAGVMap(AgvMapRequest request) {
        return fleetClient.getAllAGVMap(request);
    }


    @Override
    public void openAutoParkAndCharge(String agvCode) {
        fleetClient.openAutoParkOrCharge(agvCode);
    }

    @Override
    public void closeAutoParkAndCharge(String agvCode) {
        fleetClient.closeAutoParkOrCharge(agvCode);
    }

}
