package com.youibot.tms.biz.forest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/10/17 11:23
 */
@Data
@ApiModel(value = "Path", description = "路径")
public class Path implements Serializable{


    /**  
	 * @Fields serialVersionUID : TODO(描述)
	 * <AUTHOR>
	 * @date 2021-03-09 06:12:37 
	 */  
	
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "ID")
    private String id;


    @ApiModelProperty(value = "地图ID", position = 1)
    private String agvMapId;


    @ApiModelProperty(value = "开始标记点", position = 2)
    private String startMarkerId;


    @ApiModelProperty(value = "结束标记点", position = 3)
    private String endMarkerId;


    @ApiModelProperty(value = "开始点的控制点xy坐标", position = 4)
    private String startControl;


    @ApiModelProperty(value = "结束点的控制点xy坐标", position = 5)
    private String endControl;


    @ApiModelProperty(value = "长度", position = 6)
    private Double length;


    @ApiModelProperty(value = "线类型 1、直线 2、曲线（只要不为1,目前都默认为曲线）", position = 7)
    private Integer lineType;


    @ApiModelProperty(value = "方向 0、双向 1、正向 2、反向", position = 8)
    private Integer direction;


    @ApiModelProperty(value = "正向行驶时agv方向 1、正向 2、反向", position = 9)
    private Integer forwardAgvDirection;


    @ApiModelProperty(value = "反向行驶时agv方向 1、正向 2、反向", position = 10)
    private Integer reverseAgvDirection;


    @ApiModelProperty(value = "使用状态 ENABLE:启用  DISABLE:禁用", position = 11)
    private String usageStatus;


    @ApiModelProperty(value = "创建时间", position = 12)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @ApiModelProperty(value = "更新时间", position = 13)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
