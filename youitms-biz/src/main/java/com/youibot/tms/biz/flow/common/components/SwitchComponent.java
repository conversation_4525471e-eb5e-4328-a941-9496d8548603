package com.youibot.tms.biz.flow.common.components;

import com.alibaba.fastjson.JSONObject;
import com.youibot.tms.biz.entity.FlowComponent;
import com.youibot.tms.biz.entity.FlowTemplateComponentGlobalVariable;
import com.youibot.tms.biz.entity.FlowTemplateGlobalVariable;
import com.youibot.tms.biz.enums.ConditionComputeSignEnum;
import com.youibot.tms.biz.enums.ConditionGroupComputeSignEnum;
import com.youibot.tms.biz.enums.FlowTemplateComponentTypeEnum;
import com.youibot.tms.biz.enums.TaskComponentGlobalVariableType;
import com.youibot.tms.biz.flow.common.AbstractBizCommonComponent;
import com.youibot.tms.biz.flow.common.enums.CommonComponentParamsTypeEnums;
import com.youibot.tms.biz.flow.common.enums.ParamsTypeEnum;
import com.youibot.tms.biz.flow.constants.GlobalVariableKeys;
import com.youibot.tms.biz.flow.dto.OutputParams;
import com.youibot.tms.biz.flow.dto.ParamsType;
import com.youibot.tms.common.exception.ServiceException;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.workflow.core.flow.FlowNode;
import com.youibot.tms.workflow.core.flow.WorkFlow;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 条件组件，可根据判断条件选择执行下一步
 *
 * <AUTHOR>
 * @since 2023年9月8日10:35:31
 */
@Slf4j
@Order(4)
@Component
public class SwitchComponent extends AbstractBizCommonComponent {
    private static final String name = "条件判断";
    private static final String conditionGroupKey = "conditionGroup";
    private static final String elseConditionKey = "elseCondition";
    private static final List<FlowComponent.ComponentParam<List<ConditionGroupAndResult>>> params = new ArrayList<>();

    static {
        ArrayList<ConditionGroupAndResult> conditionGroupAndResults = new ArrayList<>();
        ParamsType paramsType = new ParamsType(ParamsTypeEnum.Array, ConditionGroupAndResult.class);
        paramsTypeMap.put(conditionGroupKey, paramsType);
        params.add(new FlowComponent.ComponentParam<>("条件参数", "conditionGroup", conditionGroupAndResults, CommonComponentParamsTypeEnums.conditionGroup.name()));

        ParamsType paramsType2 = new ParamsType(ParamsTypeEnum.String, String.class);
        paramsTypeMap.put(elseConditionKey, paramsType2);
        params.add(new FlowComponent.ComponentParam<>("否则", "elseCondition", conditionGroupAndResults, CommonComponentParamsTypeEnums.conditionGroupElseSelect.name()));
    }

    /**
     * 组件输出参数列表
     */
    public static class OutputParamsKeyConstant {
    }

    @Override
    protected String run(WorkFlow workFlow, FlowNode flowNode, JSONObject taskGlobalVariable,
                         List<FlowTemplateGlobalVariable> flowTemplateGlobalVariables,
                         List<FlowTemplateComponentGlobalVariable> flowTemplateComponentGlobalVariables) {
        log.info("进入条件节点");
        List<ConditionGroupAndResult> conditionGroupAndResults = checkAndGetComponentParams(flowNode, taskGlobalVariable,
                flowTemplateGlobalVariables, flowTemplateComponentGlobalVariables, conditionGroupKey);
        String conditionGroupAndResult = checkAndGetComponentParams(flowNode, taskGlobalVariable,
                flowTemplateGlobalVariables, flowTemplateComponentGlobalVariables, elseConditionKey);
        String s = computeConditionGroupAndResult(conditionGroupAndResults, flowNode, taskGlobalVariable, flowTemplateGlobalVariables);
        log.info("获取到参数：{} 的值为：{}", conditionGroupKey, conditionGroupAndResults);
        log.info("获取到计算比较结果:{},默认下一个节点：{}", s, conditionGroupAndResult);
        String agvCode = taskGlobalVariable.getString(GlobalVariableKeys.agvCodeKey);
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        String resultNextNodeId;
        if (s == null) {
            resultNextNodeId = conditionGroupAndResult;
        } else {
            resultNextNodeId = s;
        }
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put(conditionGroupKey, conditionGroupAndResults);
        jsonObject1.put(elseConditionKey, conditionGroupAndResult);
        jsonObject1.put("result", resultNextNodeId);
        String remark = "AGV[" + agvCode + "]正在执行条件判断！判断结果：" + (resultNextNodeId == null ? "判断失败，没有获取到判断后的结果!" : "[" + resultNextNodeId + "]");
        flowNode.setVariables(jsonObject1);
        recordTaskLog(workFlow, flowNode, flowNode.getName(), jsonObject1.toJSONString(), taskCode, remark);
        if (resultNextNodeId != null) {
            FlowNode nodeByNodeTemplateId = workFlow.getNodeByNodeTemplateId(resultNextNodeId);
            if (nodeByNodeTemplateId != null) {
                remark = remark + " 下一步节点组件名称：" + nodeByNodeTemplateId.getName();
                flowNode.setVariables(jsonObject1);
                recordTaskLog(workFlow, flowNode, flowNode.getName(), jsonObject1.toJSONString(), taskCode, remark);
                return nodeByNodeTemplateId.getId();
            }
        }
        throw new ServiceException("请检查条件参数是否正确配置，没有获取到条件组的结果！");
    }


    /**
     * 获取比较的结果
     *
     * @param conditionGroupAndResults    条件组列表
     * @param abstractFlowNode            当前节点信息
     * @param taskGlobalVariable          外部传入的全局变量
     * @param flowTemplateGlobalVariables 全局变量KEY信息列表
     * @return
     */
    private String computeConditionGroupAndResult(List<ConditionGroupAndResult> conditionGroupAndResults, FlowNode abstractFlowNode,
                                                  JSONObject taskGlobalVariable, List<FlowTemplateGlobalVariable> flowTemplateGlobalVariables) {

        for (ConditionGroupAndResult conditionGroupAndResult : conditionGroupAndResults) {
            List<ConditionGroup> conditionGroups = conditionGroupAndResult.getConditionGroups();
            ConditionGroupComputeSignEnum computeSign1 = conditionGroupAndResult.getComputeSign();
            if (ToolUtil.isNotEmpty(conditionGroups)) {
                if (computeSign1 == null && conditionGroups.size() > 1) {
                    throw new ServiceException("请检查条件参数是否正确配置，没有获取到条件组的计算符号！");
                }
                boolean conditionGroupFinalResult = true;
                String result = conditionGroupAndResult.getResult();
                if (result == null) {
                    throw new ServiceException("请检查条件参数是否正确配置,检查连线是否有连好！");
                }
                for (ConditionGroup conditionGroup : conditionGroups) {
                    boolean conditionGroupComputeResult = true;
                    List<Condition> conditions = conditionGroup.getConditions();
                    ConditionGroupComputeSignEnum computeSign = conditionGroup.getComputeSign();
                    for (Condition condition : conditions) {
                        boolean b = computeCondition(condition, abstractFlowNode, taskGlobalVariable, flowTemplateGlobalVariables);
                        if (ConditionGroupComputeSignEnum.and.equals(computeSign)) {
                            conditionGroupComputeResult = b;
                            if (!conditionGroupComputeResult) {
                                break;
                            }
                        } else {
                            conditionGroupComputeResult = b;
                            if (conditionGroupComputeResult) {
                                break;
                            }
                        }
                    }
                    if (ConditionGroupComputeSignEnum.and.equals(computeSign1)) {
                        conditionGroupFinalResult = conditionGroupComputeResult;
                        if (!conditionGroupFinalResult) {
                            break;
                        }
                    } else {
                        conditionGroupFinalResult = conditionGroupComputeResult;
                        if (conditionGroupFinalResult) {
                            break;
                        }
                    }

                }
                if (conditionGroupFinalResult) {
                    return result;
                }
            } else {
                throw new ServiceException("请检查条件参数是否正确配置，没有获取到条件组信息！");
            }
        }

        return null;
    }

    /**
     * 将比较用的计算符号转化为代码实现，对两个目标值进行比较
     *
     * @param condition                   条件信息
     * @param abstractFlowNode            当前节点
     * @param taskGlobalVariable          外部传入的全局变量
     * @param flowTemplateGlobalVariables 全局变量KEY信息列表
     * @return
     */
    private boolean computeCondition(Condition condition, FlowNode abstractFlowNode, JSONObject taskGlobalVariable,
                                     List<FlowTemplateGlobalVariable> flowTemplateGlobalVariables) {
        TaskComponentGlobalVariableType paramType = condition.getParamType();
        TaskComponentGlobalVariableType targetParamType = condition.getTargetParamType();
        String param = condition.getParam();
        String paramResult = extractedConditionParam(abstractFlowNode, taskGlobalVariable, flowTemplateGlobalVariables, paramType, param);
        String targetParam = condition.getTargetParam();
        String targetParamResult = extractedConditionParam(abstractFlowNode, taskGlobalVariable, flowTemplateGlobalVariables, targetParamType, targetParam);
        ConditionComputeSignEnum computeSign = condition.getComputeSign();
        if (ConditionComputeSignEnum.eq.equals(computeSign)) {
            return paramResult.equals(targetParamResult);
        } else if (ConditionComputeSignEnum.ge.equals(computeSign)) {
            double v = Double.parseDouble(paramResult);
            double v2 = Double.parseDouble(targetParamResult);
            return v >= v2;
        } else if (ConditionComputeSignEnum.gt.equals(computeSign)) {
            double v = Double.parseDouble(paramResult);
            double v2 = Double.parseDouble(targetParamResult);
            return v > v2;
        } else if (ConditionComputeSignEnum.lt.equals(computeSign)) {
            double v = Double.parseDouble(paramResult);
            double v2 = Double.parseDouble(targetParamResult);
            return v < v2;
        } else if (ConditionComputeSignEnum.le.equals(computeSign)) {
            double v = Double.parseDouble(paramResult);
            double v2 = Double.parseDouble(targetParamResult);
            return v <= v2;
        } else if (ConditionComputeSignEnum.notEq.equals(computeSign)) {
            return !paramResult.equals(targetParamResult);
        }
        throw new ServiceException("请检查条件参数是否正确配置！");
    }

    /**
     * 提取参数
     *
     * @param abstractFlowNode            当前流程节点
     * @param taskGlobalVariable          外部传入的全局变量
     * @param flowTemplateGlobalVariables 配置的全局变量key列表
     * @param paramType                   参数类型
     * @param param1                      参数值
     * @return
     */
    private String extractedConditionParam(FlowNode abstractFlowNode, JSONObject taskGlobalVariable, List<FlowTemplateGlobalVariable> flowTemplateGlobalVariables,
                                           TaskComponentGlobalVariableType paramType, String param1) {
        String param;
        if (TaskComponentGlobalVariableType.variable.equals(paramType)) {
            Object o = super.extractGlobalVariable(abstractFlowNode, taskGlobalVariable, flowTemplateGlobalVariables, conditionGroupKey, param1);
            param = String.valueOf(o);
        } else if (TaskComponentGlobalVariableType.output.equals(paramType)) {
            Object o = super.extractOutputValue(abstractFlowNode, conditionGroupKey, param1);
            param = String.valueOf(o);
        } else {
            param = param1;
        }
        return param;
    }


    @Override
    public <T> List<FlowComponent.ComponentParam<T>> getParams() {
        List<FlowComponent.ComponentParam<T>> componentParams = new ArrayList<>();
        for (FlowComponent.ComponentParam<List<ConditionGroupAndResult>> param : params) {
            componentParams.add((FlowComponent.ComponentParam<T>) param);
        }
        return componentParams;
    }

    @Override
    public List<OutputParams> getOutputParams() {
        return new ArrayList<>();
    }

    @Override
    public String getName() {
        return name;
    }


    /**
     * 判断条件
     */
    @Data
    private static class Condition {
        private String id;
        private String param;
        private String targetParam;
        private TaskComponentGlobalVariableType paramType;
        private TaskComponentGlobalVariableType targetParamType;
        private ConditionComputeSignEnum computeSign;
    }

    /**
     * 条件组之间是以  || 连接
     */
    @Data
    private static class ConditionGroup {
        private String id;
        /**
         * 这里面的条件都是以  && 连接
         */
        private List<Condition> conditions = new ArrayList<>();
        private ConditionGroupComputeSignEnum computeSign;

    }

    /**
     * 条件组和结果
     */
    @Data
    private static class ConditionGroupAndResult {
        /**
         * 这里面的条件都是以  || 连接
         */
        private List<ConditionGroup> conditionGroups = new ArrayList<>();
        private ConditionGroupComputeSignEnum computeSign;
        private String result;

    }

    @Override
    public FlowTemplateComponentTypeEnum getFlowTemplateComponentType() {
        return FlowTemplateComponentTypeEnum.SwitchComponent;
    }
    @Override
    public String getSvg() {
        return "switch.svg";
    }
    @Override
    public String getRemark() {
        return "条件组件，此组件可以使用条件组和条件进行任意组合，灵活的实现各种判断条件";
    }
}
