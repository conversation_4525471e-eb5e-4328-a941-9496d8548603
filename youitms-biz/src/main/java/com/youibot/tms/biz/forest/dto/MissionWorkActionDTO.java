package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "MissionWorkActionDTO", description = "作业动作")
public class MissionWorkActionDTO {

    
    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "agv的编号", position = 1)
    private String agvCode;

    @ApiModelProperty(value = "任务的ID", position = 2)
    private String missionActionId;

    @ApiModelProperty(value = "作业ID", position = 3)
    private String missionWorkId;

    @ApiModelProperty(value = "动作类型", position = 4)
    private String actionType;

    
    @ApiModelProperty(value = "名称", position = 5)
    private String name;

    @ApiModelProperty(value = "顺序编号", position = 6)
    private Integer sequence;

    @ApiModelProperty(value = "执行结果描述", position = 7)
    private String message;

    @ApiModelProperty(value = "参数", position = 8)
    private String parameters;

    /**
     * 状态：START,RUNNING,SUCCESS,FAULT
     */
    @ApiModelProperty(value = "状态 START:开始执行 RUNNING:执行中 SUCCESS:执行成功 FAULT:执行错误", position = 9)
    private String status;

    @ApiModelProperty(value = "返回编码 1001:正确编码 其他为错误编码", position = 11)
    private Integer resultCode;

    @ApiModelProperty(value = "返回提示信息", position = 12)
    private String resultMessage;

    @ApiModelProperty(value = "返回数据区数据类型", position = 13)
    private String resultType;

    @ApiModelProperty(value = "返回数据区数据", position = 14)
    private String resultData;

    @ApiModelProperty(value = "开始时间", position = 15)
    private Date startTime;

    @ApiModelProperty(value = "结束时间", position = 16)
    private Date endTime;

    @ApiModelProperty(value = "创建时间", position = 17)
    private Date createTime;

    @ApiModelProperty(value = "更新时间", position = 18)
    private Date updateTime;

    @ApiModelProperty(value = "执行顺序编号", position = 19)
    private Integer actionSequence;

    @ApiModelProperty(value = "当该action属于循环action的子action时，记录该action所在的本次循环执行是否完成", position = 20)
    private Boolean thisLoopCompletes;

    @ApiModelProperty(value = "异常错误代码", position = 20)
    private Integer errorCode;

    private  transient List<MqMessage> mqMessages;

    @ApiModelProperty(value = "动作参数JSON")
    private Object actionParams;
}
