package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "MarkerInfoApiDTO",description = "点位信息")
public class MarkerInfoApiDTO {

    @ApiModelProperty(value = "定位图编码", position = 1)
    private String locatingCode;

    @ApiModelProperty(value = "x坐标", position = 2)
    private Double x;

    @ApiModelProperty(value = "y坐标", position = 3)
    private Double y;

}
