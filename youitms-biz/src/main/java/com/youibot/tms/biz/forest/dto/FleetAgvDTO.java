package com.youibot.tms.biz.forest.dto;

import com.youibot.tms.biz.forest.enums.VehicleStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/11 11:58
 */
@Data
@ApiModel(value = "Agv", description = "Agv")
public class FleetAgvDTO {


    @ApiModelProperty(value = "Id", position = 0)
    private String id;

    @ApiModelProperty
    private String ip;

    /**
     * 对应vehicleDTO中的agvId
     */
    @ApiModelProperty(value = "agvCode", position = 1)
    private String agvCode;

    @ApiModelProperty(value = "agvId", position = 2)
    private String agvId;
    
    @ApiModelProperty(value = "名称", position = 3)
    private String agvName;
    
    @ApiModelProperty(value = "导航类型 LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航", position = 4)
    private String navigationType;
    
    @ApiModelProperty(value = "机器人型号", position = 5)
    protected String agvType;
    
    @ApiModelProperty(value = "机器人所属组", position = 6)
    protected String agvGroupId;
    
    @ApiModelProperty(value = "外型信息", position = 7)
    private String shapeInfo;
    
    @ApiModelProperty(value = "启用状态 0：未启用 1：启用", position = 8)
    private Integer status;

    
    @ApiModelProperty(value = "在线状态 0：断线 1：在线 2：离线", position = 9)
    private Integer onlineStatus;

    @ApiModelProperty(value = "控制模式,1自动模式、2手动模式")
    
    private Integer controlMode;

    @ApiModelProperty(value = "地图指定状态,0未指定、1已指定")
    private Integer appointStatus;

    @ApiModelProperty(value = "异常状态,1无异常、2工作异常、3充电异常、4归位异常")
    private Integer abnormalStatus;

    
    @ApiModelProperty(value = "任务状态,1空闲、2任务、3充电、4归位")
    private Integer workStatus;

    @ApiModelProperty(value = "地图同步状态,0未同步、1同步")
    private Integer mapStatus;

    @ApiModelProperty(value = "机器人颜色")
    private String agvColor;

    
    @ApiModelProperty(value = "是否自动充电，0:关闭，1:开启，2:默认")
    private Integer autoCharge;

    
    @ApiModelProperty(value = "是否自动泊车，0:关闭，1:开启，2:默认")
    private Integer autoPark;

    
    @ApiModelProperty(value = "是否自动分配任务，0:关闭，1:开启")
    private Integer autoAllocation;

    
    @ApiModelProperty(value = "绑定的泊车点, 运行时参数(json格式) 如：[{\"mapId\":\"1001\",\"markerCode\":\"123456\"}]")
    private String bindParkMarkers;

    
    @ApiModelProperty(value = "绑定的充电点, 运行时参数(json格式) 如：[{\"mapId\":\"1001\",\"markerCode\":\"123456\"}]")
    private String bindChargeMarkers;

    
    @ApiModelProperty(value = "绑定的泊车点配置")
    private boolean bindParkConfig;

    
    @ApiModelProperty(value = "绑定的充电点配置")
    private boolean bindChargeConfig;

    
    @ApiModelProperty(value = "创建时间", position = 10)
    private Date createTime;

    
    @ApiModelProperty(value = "更新时间", position = 11)
    private Date updateTime;


    public VehicleStatus getVehicleStatus(){
        VehicleStatus agvStatusEnum = null;
        Integer workStatus = this.getWorkStatus();
        // 是否故障或离线
        if (!AGVConstant.ONLINE.equals(this.getOnlineStatus())) {
            agvStatusEnum = VehicleStatus.OFFLINE;
        } else if (!AGVConstant.ABNORMAL_STATUS_NO.equals(this.getAbnormalStatus())) {
            agvStatusEnum = VehicleStatus.ERROR;
        }
        if (agvStatusEnum == null) {
            switch (workStatus) {
                case 1:
                    agvStatusEnum = VehicleStatus.IDLE;
                    break;
                case 2:
                case 4:
                    agvStatusEnum = VehicleStatus.RUN;
                    break;
                case 3:
                    agvStatusEnum = VehicleStatus.CHARGE;
                    break;
                default:
                    break;
            }
        }
        // null 表示未知状态
        return agvStatusEnum;
    }

}
