package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 充电桩DTO
 * 用于充电桩信息的传输对象
 */
@Data
@ApiModel(value = "ChargeStationDTO", description = "充电桩信息传输对象")
public class ChargeStationDTO {

    /**
     * 充电桩ID
     */
    @ApiModelProperty(value = "充电桩ID", example = "1", position = 1, required = true)
    private Long id;

    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID", example = "CS001", position = 2, notes = "充电桩的唯一标识符")
    private String deviceId;

    /**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码", example = "CHARGE-001", position = 3, notes = "充电桩的编码，用于外部系统集成")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", example = "一号充电桩", position = 4, notes = "充电桩的显示名称")
    private String deviceName;

    /**
     * 启用状态 Enable/Disable
     */
    @ApiModelProperty(value = "启用状态", example = "Enable", position = 5, allowableValues = "Enable,Disable", notes = "充电桩的启用状态，Enable表示启用，Disable表示禁用")
    private String status;

    /**
     * 网络状态 online/offline
     */
    @ApiModelProperty(value = "网络状态", example = "online", position = 6, allowableValues = "online,offline", notes = "充电桩的网络连接状态，online表示在线，offline表示离线")
    private String networkStatus;

    /**
     * 运行状态 normal/abnormal
     */
    @ApiModelProperty(value = "运行状态", example = "normal", position = 7, allowableValues = "normal,abnormal", notes = "充电桩的运行状态，normal表示正常，abnormal表示异常")
    private String workStatus;

    /**
     * 控制模式 auto/manual
     */
    @ApiModelProperty(value = "控制模式", example = "auto", position = 8, allowableValues = "auto,manual", notes = "充电桩的控制模式，auto表示自动模式，manual表示手动模式")
    private String controlMode;

    /**
     * 放电状态 discharging/no_discharge
     */
    @ApiModelProperty(value = "放电状态", example = "no_discharge", position = 9, allowableValues = "discharging,no_discharge", notes = "充电桩的放电状态，discharging表示放电中，no_discharge表示未放电")
    private String dischargeStatus;

    /**
     * 复位状态 pending/completed
     */
    @ApiModelProperty(value = "复位状态", example = "completed", position = 10, allowableValues = "pending,completed", notes = "充电桩的复位状态，pending表示复位中，completed表示复位完成")
    private String resetStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2023-05-01 12:00:00", position = 11, notes = "充电桩记录的创建时间，格式为 yyyy-MM-dd HH:mm:ss")
    private String createDate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2023-05-02 14:30:00", position = 12, notes = "充电桩记录的最后更新时间，格式为 yyyy-MM-dd HH:mm:ss")
    private String updateDate;
}
