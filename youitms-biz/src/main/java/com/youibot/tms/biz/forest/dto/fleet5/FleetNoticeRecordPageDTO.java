package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Fleet通知记录分页数据DTO（TMS专用）
 * Created by Augment Agent on 2024/12/19.
 */
@Data
@ApiModel(value = "FleetNoticeRecordPageDTO", description = "Fleet通知记录分页数据（TMS专用）")
public class FleetNoticeRecordPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前页码")
    private Integer page;

    @ApiModelProperty(value = "每页大小")
    private Integer limit;

    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "总页数")
    private Integer pages;

    @ApiModelProperty(value = "通知记录列表")
    private List<FleetNoticeRecord> list;

    /**
     * Fleet通知记录
     */
    @Data
    @ApiModel(value = "FleetNoticeRecord", description = "Fleet通知记录")
    public static class FleetNoticeRecord implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "记录ID")
        private Long id;

        @ApiModelProperty(value = "异常编码")
        private Integer code;

        @ApiModelProperty(value = "异常描述")
        private String description;

        @ApiModelProperty(value = "机器人编码")
        private String vehicleCode;

        @ApiModelProperty(value = "机器人名称")
        private String vehicleName;

        @ApiModelProperty(value = "异常级别")
        private Integer level;

        @ApiModelProperty(value = "状态")
        private Integer status;

        @ApiModelProperty(value = "数据内容")
        private String data;

        @ApiModelProperty(value = "解决方案")
        private String solution;

        @ApiModelProperty(value = "地图名称")
        private String mapName;

        @ApiModelProperty(value = "任务工作ID")
        private String missionWorkId;

        @ApiModelProperty(value = "设备ID")
        private String deviceId;

        @ApiModelProperty(value = "来源系统")
        private String sourceSystem;

        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "失效时间")
        private Integer invalidTime;

        @ApiModelProperty(value = "创建时间")
        private Date createDate;

        @ApiModelProperty(value = "更新时间")
        private Date updateDate;

        @ApiModelProperty(value = "关闭时间")
        private Date closeTime;

        @ApiModelProperty(value = "最后推送时间")
        private Date lastPushTime;

        @ApiModelProperty(value = "X坐标")
        private Double positionX;

        @ApiModelProperty(value = "Y坐标")
        private Double positionY;

        /**
         * 获取异常级别描述
         */
        public String getLevelDescription() {
            if (level == null) {
                return "未知";
            }
            switch (level) {
                case 1:
                    return "普通";
                case 2:
                    return "警告";
                case 3:
                    return "错误";
                default:
                    return "未知";
            }
        }

        /**
         * 获取状态描述
         */
        public String getStatusDescription() {
            if (status == null) {
                return "未知";
            }
            switch (status) {
                case 1:
                    return "激活";
                case 2:
                    return "忽略";
                case 3:
                    return "关闭";
                default:
                    return "未知";
            }
        }

        /**
         * 判断是否需要弹窗提醒
         */
        public boolean needPopup() {
            return level != null && level == 3 && status != null && status == 1;
        }

        /**
         * 获取位置信息
         */
        public String getLocationInfo() {
            if (positionX != null && positionY != null) {
                return String.format("X:%.2f, Y:%.2f", positionX, positionY);
            }
            return "位置信息不可用";
        }
    }

    /**
     * 获取分页信息摘要
     */
    public String getPageSummary() {
        return String.format("第%d页，共%d页，总计%d条记录",
                page != null ? page : 0,
                pages != null ? pages : 0,
                total != null ? total : 0);
    }

    /**
     * 判断是否有数据
     */
    public boolean hasData() {
        return list != null && !list.isEmpty();
    }

    /**
     * 获取当前页记录数
     */
    public int getCurrentPageSize() {
        return list != null ? list.size() : 0;
    }

    /**
     * 判断是否是第一页
     */
    public boolean isFirstPage() {
        return page != null && page == 1;
    }

    /**
     * 判断是否是最后一页
     */
    public boolean isLastPage() {
        return page != null && pages != null && page.equals(pages);
    }

    /**
     * 获取下一页页码
     */
    public Integer getNextPageNum() {
        if (page != null && pages != null && page < pages) {
            return page + 1;
        }
        return null;
    }

    /**
     * 获取上一页页码
     */
    public Integer getPrevPageNum() {
        if (page != null && page > 1) {
            return page - 1;
        }
        return null;
    }
}
