package com.youibot.tms.biz.events.listener;

import com.youibot.tms.biz.entity.PortStocker;
import com.youibot.tms.biz.service.PortStockerService;
import com.youibot.tms.common.core.domain.entity.ZoneStocker;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.system.events.ZoneStockerUpdateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Component
public class ZoneStockerUpdateListener implements ApplicationListener<ZoneStockerUpdateEvent> {

    @Resource
    private PortStockerService portStockerService;
    

    /**
     * 分隔符
     */
    private final static String SPLIT = "-";

    @Override
    public void onApplicationEvent(ZoneStockerUpdateEvent event) {
        ZoneStocker zoneStocker = event.getEntity();
        List<PortStocker> existed = portStockerService.selectByZoneStockerId(zoneStocker.getId());
        if (ToolUtil.isNotEmpty(zoneStocker)) {
            log.info("Get ZoneStockerUpdateEvent,entity.id:{}", event.getEntity().getId());
            Long numX = zoneStocker.getNumX();
            Long numY = zoneStocker.getNumY();
            Long numZ = zoneStocker.getNumZ();
            if (numX != null && numY != null && numZ != null
                    && numX > 0 && numY > 0 && numZ > 0) {

                for (long x = 1L; x <= numX; x++) {
                    for (long y = 1L; y <= numY; y++) {
                        for (long z = 1L; z <= numZ; z++) {
                            String autoName = zoneStocker.getCode() + SPLIT + x + SPLIT + y+ SPLIT + z;
                            PortStocker port = fetchFromExisted(existed, autoName);
                            if(port == null) {
                                port = new PortStocker();
                                port.setCode(autoName);
                                port.setName(autoName);
                                port.setEnabled(true);
                                port.setZoneStockerId(zoneStocker.getId());

                                portStockerService.save(port);
                                log.info("生成库位并保存成功：{}", autoName);
                            }else if(port.getDelFlag()){
                                log.info("恢复已删除的库位：{}", autoName);
                                port.setDelFlag(false);
                                portStockerService.updateBaseById(port);
                            }else{
                                log.info("库位已存在：{}", autoName);

                            }
                        }
                    }
                }


            }
            //将existed中剩余的库位删除
            if(ToolUtil.isNotEmpty(existed)){
                log.info("多余的库位，将其删除：{}", existed.stream().map(PortStocker::getName).collect(java.util.stream.Collectors.toList()));
                List<Long> ids = existed.stream().map(PortStocker::getId).collect(java.util.stream.Collectors.toList());
                portStockerService.removeByIds(ids);
            }

        }
    }

    private PortStocker fetchFromExisted(List<PortStocker> existed, String autoName) {
        PortStocker foundElement = null;

        Iterator<PortStocker> iterator = existed.iterator();
        while (iterator.hasNext()) {
            PortStocker element = iterator.next();
            if (element.getName().equals(autoName)) {  // 假设 PortStocker 类中有一个 getName 方法用于获取名称
                foundElement = element;
                iterator.remove();  // 从列表中删除找到的元素
                break;
            }
        }

        return foundElement;
    }
}
