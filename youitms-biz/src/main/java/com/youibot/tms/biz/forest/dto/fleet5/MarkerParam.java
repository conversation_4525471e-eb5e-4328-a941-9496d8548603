package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
@ApiModel(value = "MarkerParam", description = "点位扩展参数")
public class MarkerParam implements Cloneable {

    @ApiModelProperty(value = "文本1")
    private String text1;

    @ApiModelProperty(value = "文本2")
    private String text2;

    @ApiModelProperty(value = "文本3")
    private String text3;

    @ApiModelProperty(value = "数字1")
    private Double number1;

    @ApiModelProperty(value = "数字2")
    private Double number2;

    @ApiModelProperty(value = "数字3")
    private Double number3;
}
