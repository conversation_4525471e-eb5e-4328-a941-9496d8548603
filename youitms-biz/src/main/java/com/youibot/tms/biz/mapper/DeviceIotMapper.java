package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.entity.DeviceIot;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */

@CacheNamespace
public interface DeviceIotMapper extends BaseMapper<DeviceIot> {

    @Update("update biz_device_iot set enabled = #{enabled} where id = #{id}")
    void updateEnabledById(DeviceIot entity);

    @Select("select a.* " +
            "from biz_device_iot a " +
            "left join biz_device b on a.device_id = b.id " +
            "${ew.customSqlSegment}")
    List<DeviceIot> selectList(@Param("ew") Wrapper<DeviceIot> queryWrapper);
}
