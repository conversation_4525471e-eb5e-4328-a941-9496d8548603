package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "DefaultVehicleStatusDTO", description = "机器人状态")
public class DefaultVehicleStatusDTO implements Serializable {

	/**  
	 * @Fields serialVersionUID : TODO(描述)
	 * <AUTHOR>
	 * @date 2021-03-09 06:11:38 
	 */  
	
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "运行")
	private RuntimeStatus runtime;

	@ApiModelProperty(value = "地图")
	private MapStatus map;

	@ApiModelProperty(value = "位置")
	private PositionStatus position;

	@ApiModelProperty(value = "速度")
	private SpeedStatus speed;

	@ApiModelProperty(value = "电池")
	private BatteryStatus battery;

	@ApiModelProperty(value = "电机")
	private EmecStatus emec;

	@ApiModelProperty(value = "电机")
	private RfidJY rfidJY;

	@Data
	@ApiModel(value = "RuntimeStatus", description = "运行")
	public static class RuntimeStatus implements Serializable  {
		/**  
		 * @Fields serialVersionUID : TODO(描述)
		 * <AUTHOR>
		 * @date 2021-04-08 06:46:57 
		 */  
		
		private static final long serialVersionUID = 1L;
		@ApiModelProperty(value = "当前状态 1:空闲中,2:动作中 3,被阻档,4冲电中,5异常中,6急停7:正在进行重定位")
		private Integer agv_status;//当前状态1:空闲中,2:动作中3,被阻档,4冲电中,5异常中,6急停7:正在进行重定位
		@ApiModelProperty(value = "今天开机运行时间,单位:s")
		private Long time;//今天开机运行时间,s
		@ApiModelProperty(value = "累计运行时间, 单位:s")
		private Long total_time;//累计运行时间, 单位 s
		@ApiModelProperty(value = "累计行驶里程, 单位:km")
		private Double odom;//累计行驶里程, 单位 km
		@ApiModelProperty(value = "控制器当前温度")
		private Double controller_temp;//控制器当前温度
		@ApiModelProperty(value = "电机当前温度")
		private Double motor_temp;//电机当前温度
	}

	@Data
	@ApiModel(value = "MapStatus", description = "地图")
	public static class MapStatus implements Serializable {
		/**  
		 * @Fields serialVersionUID : TODO(描述)
		 * <AUTHOR>
		 * @date 2021-04-08 06:48:21 
		 */  
		
		private static final long serialVersionUID = 1L;
		@ApiModelProperty(value = "地图ID")
		private String current_map_id;//当前地图名(mapId+mapVersion)
		@ApiModelProperty(value = "地图类型 1:激光地图  2:虚拟地图  3:CAD地图")
		private Integer current_map_type;//地图类型 1:激光地图  2:虚拟地图  3:CAD地图
		@ApiModelProperty(value = "扫图状态  1：正在扫图  2：表示暂停中  3：表示建图完成")
		private Integer scan_status;//扫图状态  1：正在扫图  2：表示暂停中  3：表示建图完成
	}

	@Data
	@ApiModel(value = "PositionStatus", description = "位置")
	public static class PositionStatus implements Serializable {
		/**  
		 * @Fields serialVersionUID : TODO(描述)
		 * <AUTHOR>
		 * @date 2021-04-08 06:48:14 
		 */  
		
		private static final long serialVersionUID = 1L;
		@ApiModelProperty(value = "X位置")
		private Double pos_x;// X坐标
		@ApiModelProperty(value = "Y位置")
		private Double pos_y;// y坐标
		@ApiModelProperty(value = "角度 单位:rad")
		private Double pos_angle;// angle 坐标, 单位 rad
		@ApiModelProperty(value = "机器人激光定位的置信度, 范围 [0, 1]")
		private Double pos_confidence;//机器人激光定位的置信度, 范围 [0, 1]
		@ApiModelProperty(value = "当前站点ID")
		private String pos_current_station;// 机器人当前所在站点的 ID
		@ApiModelProperty(value = "机器人在曲线上的位置")
		private Double t;//曲线上的t值
		@ApiModelProperty(value = "机器人在那条曲线上")
		private String segment_id;//曲线Id
		@ApiModelProperty(value = "协方差矩阵")
		private Double[] covariance;

        private Long update_time_millis;//数据更新时间，单位ms
	}

    @Data
    @ApiModel(value = "SpeedStatus", description = "速度")
    public static class SpeedStatus implements Serializable {
        /**  
		 * @Fields serialVersionUID : TODO(描述)
		 * <AUTHOR>
		 * @date 2021-04-08 06:47:18 
		 */  
		
		private static final long serialVersionUID = 1L;
		@ApiModelProperty(value = "X方向实际的速度,单位 m/s")
        private Double speed_vx;// 机器人在机器人坐标系的 x 方向实际的速度, 单位 m/s
        @ApiModelProperty(value = "Y方向实际的速度,单位 m/s")
        private Double speed_vy;// 机器人在机器人坐标系的 y 方向实际的速度, 单位 m/s
        @ApiModelProperty(value = "实际的角速度,单位 rad/s")
        private Double speed_w;// 机器人在机器人坐标系的实际的角速度(即顺时针转为负, 逆时针转为正), 单位 rad/s
        @ApiModelProperty(value = "X方向接收到的速度,单位 m/s")
        private Double speed_r_vx;// 机器人在机器人坐标系的 x 方向接收到的速度, 单位 m/s
        @ApiModelProperty(value = "Y方向收到的速度,单位 m/s")
        private Double speed_r_vy;// 机器人在机器人坐标系的 y 方向收到的速度, 单位 m/s
        @ApiModelProperty(value = "收到的角速度,单位 rad/s")
        private Double speed_r_w;// 机器人在机器人坐标系的收到的角速度(即顺时针转为负, 逆时针转为正), 单位 rad/s
    }

	@Data
	@ApiModel(value = "BatteryStatus", description = "电池")
	public static class BatteryStatus implements Serializable {
		/**  
		 * @Fields serialVersionUID : TODO(描述)
		 * <AUTHOR>
		 * @date 2021-04-08 06:47:30 
		 */  
		
		private static final long serialVersionUID = 1L;
		@ApiModelProperty(value = "电池是否正在充电,1：充电，0：未充电")
		private Integer battery_status;//电池是否正在充电,1：充电，0：未充电
		@ApiModelProperty(value = "机器人电池电量, 范围 [0, 100]")
		private Double battery_value;// 机器人电池电量, 范围 [0, 100]
		@ApiModelProperty(value = "机器人电池电芯最高温度, 单位 ℃")
		private Double battery_temp;//机器人电池电芯最高温度, 单位 ℃
		@ApiModelProperty(value = "充电电流, 单位 A")
		private Double battery_charge;// 充电电流, 单位 A
		@ApiModelProperty(value = "放电电流，单位A")
		private Double battery_discharge;// 放电电流，单位A
		@ApiModelProperty(value = "电压, 单位 V")
		private Double battery_voltage;// 电压, 单位 V
		@ApiModelProperty(value = "电池充满的电池电量 单位 mah")
		private Double battery_fullcapacity;//电池充满的电池电量 mah
		@ApiModelProperty(value = "电池剩余的电池电量 单位 mah")
		private Double battery_leftcapacity;//电池剩余的电池电量 mah
	}

	@Data
	@ApiModel(value = "EmecStatus", description = "电机")
	public static class EmecStatus implements Serializable {
		/**  
		 * @Fields serialVersionUID : TODO(描述)
		 * <AUTHOR>
		 * @date 2021-04-08 06:48:05 
		 */  
		
		private static final long serialVersionUID = 1L;
		@ApiModelProperty(value = "急停状态 0:未急停, 1:急停")
		private Integer emc_status;// 0 未急停, 1表示急停
		@ApiModelProperty(value = "急停原因  1:表示急停按钮处于激活状态(按下) 2:安全激光雷达触发导致的急停 3:碰撞开类导致的急停")
		private Integer[] emc_reason;// 1 表示急停按钮处于激活状态(按下),2 安全激光雷达触发导致的急停，3 碰撞开类导致的急停
	}

    @Data
    @ApiModel(value = "RfidJY", description = "rfid")
    public static class RfidJY implements Serializable {
        /**  
		 * @Fields serialVersionUID : TODO(描述)
		 * <AUTHOR>
		 * @date 2021-04-08 06:47:56 
		 */  
		
		private static final long serialVersionUID = 1L;
		@ApiModelProperty(value = "rfid 盒子ID ,全为0表示没有读取到rfid")
        private String rfid_1; // String 格式 长度为16 盒子ID ,全为0表示没有读取到rfid
        private String rfid_2;
        private String rfid_3;
        private String rfid_4;
        private String rfid_5;
        private String rfid_6;
        private String rfid_7;
        private String rfid_8;

    }
}
