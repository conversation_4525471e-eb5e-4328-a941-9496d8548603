package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "LocatingInfo",description = "定位图信息")
public class LocatingInfo {

    @ApiModelProperty(value = "定位图编码")
    private String locatingCode;

    @ApiModelProperty(value = "是否是默认定位图 1：是 0：不是")
    private Integer isDefault = 0;

    @ApiModelProperty(value = "绑定的机器人类型编码,按逗号,分割")
    private String vehicleTypeCodes;

    @ApiModelProperty(value = "绑定的机器人类型名称,按逗号,分割")
    private String vehicleTypeNames;

    @ApiModelProperty(value = "关联的地图编码")
    private String vehicleMapCode;

    @ApiModelProperty(value = "类型:LaserMap")
    private String type;

    @ApiModelProperty(value = "栅格图片存储路径")
    private String image;

    @ApiModelProperty(value = "像素宽度")
    private Double width;

    @ApiModelProperty(value = "像素高度")
    private Double height;

    @ApiModelProperty(value = "旋转角度")
    private Double angle;

    @ApiModelProperty(value = "中心x坐标")
    private Double originX;

    @ApiModelProperty(value = "中心y坐标")
    private Double originY;

    @ApiModelProperty(value = "角度")
    private Double originYaw;

    @ApiModelProperty(value = "分辨率")
    private Double resolution;

}
