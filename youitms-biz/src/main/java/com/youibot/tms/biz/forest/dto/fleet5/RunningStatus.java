package com.youibot.tms.biz.forest.dto.fleet5;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2022/12/14/14:00
 * @Description: 运行状态信息
 */
@Data
public class RunningStatus {
    /**
     * AGV合速度
     * @return
     */
    @JSONField(name = "speed")
    public Double getSpeed() {
        return Math.sqrt(Math.pow(xSpeed, 2) + Math.pow(ySpeed, 2));
    }

    @JSONField(name = "xSpeed")
    public Double getxSpeed() {
        return xSpeed;
    }

    @JSONField(name = "ySpeed")
    public Double getySpeed() {
        return ySpeed;
    }

    /**
     * AGV的实时x速度，单位：m/s
     */
    @ApiModelProperty(value = "AGV的实时x速度, 单位:m/s")
    private Double xSpeed;

    /**
     * AGV的实时y速度，单位：m/s
     */
    @ApiModelProperty(value = "AGV的实时y速度, 单位:m/s", position = 1)
    private Double ySpeed;

    /**
     * AGV的实时角速度，单位：rad/s
     */
    @ApiModelProperty(value = "AGV的实时角速度, 单位:m/s", position = 2)
    private Double angleSpeed;

    /**
     * 工作状态-业务处理
     * 离线=Offline
     * 空闲=Free
     * 工作=Work
     */
    @ApiModelProperty(value = "工作状态 空闲=Free 工作=Work", position = 3)
    private String workStatus;

    /**
     * 控制状态-业务处理
     * 手动=Manual
     * 半自动=SemiAuto
     * 自动=Auto
     */
    @ApiModelProperty(value = "控制模式 手动=Manual 自动=Auto", position = 4)
    private String controlStatus;

    /**
     * 异常状态
     * 异常=Abnormal
     * 无异常=Normal
     */
    @ApiModelProperty(value = "异常状态 异常=Abnormal 无异常=Normal", position = 5)
    private String abnormalStatus;

    /**
     * 暂停开关-业务处理
     * 关闭=Close
     * 开启=Open
     */
    @ApiModelProperty(value = "暂停状态 关闭=Close 开启=Open", position = 6)
    private String softStopSwitch;

    /**
     * 对接状态
     * 未对接=NoDocking
     * 对接中=Docking
     * 已对接=Docked
     * 脱离中=Disengage
     * 异常=Abnormal
     */
    @ApiModelProperty(value = "对接状态, 未对接=NoDocking, 对接中=Docking, 已对接=Docked, 脱离中=Disengage, 异常=Abnormal", position = 7)
    private String dockingStatus;

    /**
     * 扫图状态
     * 未扫图=NotScanned
     * 扫图中=Scanning
     */
    @ApiModelProperty(value = "扫图状态, 未扫图=NotScanned, 扫图中=Scanning", position = 8)
    private String mapScanStatus;

    /**
     * 抱闸状态
     * 已抱闸=HoldingBrake
     * 未抱闸=UnblockingBrake
     */
    @ApiModelProperty(value = "抱闸状态, 已抱闸=HoldingBrake, 未抱闸=UnblockingBrake", position = 9)
    private String bankBrakeStatus;

    /**
     * 路径导航急停
     */
    @ApiModelProperty(value = "是否路径导航急停, 是=true, 否=false", position = 10)
    private Boolean navigationEmerStop;

    /**
     * 按钮急停
     */
    @ApiModelProperty(value = "是否按钮急停, 是=true, 否=false", position = 11)
    private Boolean buttonEmerStop;

    /**
     * 电池是否正在充电,true：充电，false：未充电
     */
    @JsonIgnore
    private Boolean charging;

    /**
     * 叉臂高度或顶升高度,单位：mm
     */
    @ApiModelProperty(value = "叉臂高度或顶升高度,单位：mm", position = 12)
    private Double position = 0.0;

    @ApiModelProperty(value = "运行总里程，单位：m。", position = 14)
    private Double odom;
}
