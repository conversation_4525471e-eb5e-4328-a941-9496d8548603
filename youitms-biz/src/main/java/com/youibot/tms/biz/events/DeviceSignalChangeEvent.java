package com.youibot.tms.biz.events;

import lombok.ToString;
import org.springframework.context.ApplicationEvent;


@ToString
public class DeviceSignalChangeEvent extends ApplicationEvent {



    /**
     * 设备通讯ID
     */
    private Long deviceIotId;

    /**
     * 信号位置（寄存器位置）
     */
    private String address;

    private Integer oldValue;
    /**
     * 最新值
     */
    private Integer newValue;

    public DeviceSignalChangeEvent(Object source) {
        super(source);
    }

    public Long getDeviceIotId() {
        return deviceIotId;
    }

    public void setDeviceIotId(Long deviceIotId) {
        this.deviceIotId = deviceIotId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getNewValue() {
        return newValue;
    }

    public void setNewValue(Integer newValue) {
        this.newValue = newValue;
    }

    public Integer getOldValue() {
        return oldValue;
    }

    public void setOldValue(Integer oldValue) {
        this.oldValue = oldValue;
    }
}
