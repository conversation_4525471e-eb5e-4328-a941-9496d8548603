package com.youibot.tms.biz.forest.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @version V1.0
 * @Title:
 * @ClassName: com.youibot.agv.scheduler.controller.parameterEntity
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/9/29 17:01
 */
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Data
@ApiModel(value = "MissionWorkParam", description = "任务创建参数")
public class MissionWorkParam implements Serializable {

    /**
     * @Fields serialVersionUID : TODO(描述)
     * <AUTHOR>
     * @date 2021-03-09 06:12:24
     */

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务的ID", position = 1)
    private String missionId;

    @ApiModelProperty(value = "指定执行任务的AGV, AGV分配规则会覆盖任务原本的分配规则; missionQueueId有值时该字段不生效", position = 2)
    private String agvCode;

    @ApiModelProperty(value = "回调接口", position = 3)
    private String callbackUrl;

    @ApiModelProperty(value = "运行时参数(json格式) 如：{\"marker1\":\"1001\"}", position = 4)
    private String runtimeParam;

    private JSONObject runtimeParamObject;

    @ApiModelProperty(value = "任务链ID; 如果使用该值, 任务链的AGV分配规则会覆盖任务原本的分配规则, 且本接口的agvCode字段不生效, " +
            "建议调用/api/v3/missionWorks/create/check接口检测AGV分配规则是否会被覆盖做相应提示", position = 5)
    private String missionWorkChainId;

    @ApiModelProperty(hidden = true)
    private String missionCallId;

    @ApiModelProperty(hidden = true)
    private String missionWorkId;

    @ApiModelProperty(value = "状态 CREATE:创建(未执行) ASSIGNED:已分配 WAIT:等待(继续)执行 RUNNING:执行中 SUCCESS:执行成功 FAULT:执行错误 PAUSE:暂停 BEING_PAUSE:暂停中 BEING_RESUME:恢复中 SHUTDOWN:已停止 BEING_SHUTDOWN:停止中", position = 8)
    private String status;

    public MissionWorkParam() {
        super();
    }

    public void setRuntimeParamObject(JSONObject jsonObject){
        this.runtimeParamObject = jsonObject;
        this.runtimeParam = jsonObject.toJSONString();
    }


}
