package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * mq接收信息
 * <AUTHOR>
 * @date 2020/8/31 17:30
 */

@Data
public class MqMessage {

    
    @ApiModelProperty(value = "ID", position = 0)
    private String id;

    @ApiModelProperty(value = "消息主题", position = 1)
    private String topic;

    @ApiModelProperty(value = "消息内容信息", position = 2)
    private String message;

    @ApiModelProperty(value = "是否消费 0:未消费 1:已消费", position = 3)
    private Integer consume;

    @ApiModelProperty(value = "创建时间", position = 4)
    private Date createTime;

    @ApiModelProperty(value = "更新时间", position = 5)
    private Date updateTime;

    public MqMessage(){}

    public MqMessage(String topic, String message) {
        this.topic = topic;
        this.message = message;
    }
}
