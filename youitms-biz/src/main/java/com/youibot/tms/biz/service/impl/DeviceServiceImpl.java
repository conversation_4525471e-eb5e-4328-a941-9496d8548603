package com.youibot.tms.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.Device;
import com.youibot.tms.biz.entity.DeviceIot;
import com.youibot.tms.biz.entity.DeviceStatus;
import com.youibot.tms.biz.mapper.DeviceIotMapper;
import com.youibot.tms.biz.mapper.DeviceMapper;
import com.youibot.tms.biz.service.DeviceService;
import com.youibot.tms.common.exception.base.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    @Resource
    private DeviceIotMapper deviceIotMapper;

    @Override
    public Device findByCode(String deviceCode) {
        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Device::getCode, deviceCode);
        return this.baseMapper.selectOne(wrapper);
    }

    @Override
    public Page<DeviceStatus> pageForStatus(Page<Device> page, QueryWrapper<Device> query) {
        return this.getBaseMapper().selectPageForStatus(page, query);
    }

    public List<DeviceStatus> listForStatus(QueryWrapper<Device> query) {
        return this.getBaseMapper().selectListForStatus(query);
    }

    @Override
    public boolean removeByIds(List<Long> ids) {
        int bindNum = 0;
        for (Long id : ids) {
            //未被关联 直接删除
            if (checkBind(id)) {
                this.getBaseMapper().deleteById(id);
            } else {
                bindNum++;
            }
        }
        if (bindNum > 0) {
            throw new BusinessException(BizErrorCode.DEVICE_HAS_DELETE_NO_BIND);
        }
        return true;
    }

    public boolean removeById(Long id) {
        if (checkBind(id)) {
            //未被关联
            return this.getBaseMapper().deleteById(id) > 0;
        } else {
            throw new BusinessException(BizErrorCode.DEVICE_HAS_BIND_IOT);
        }
    }

    /**
     * 检查是否被关联
     *
     * @param id
     * @return true - 未被关联
     * false - 已被关联
     */
    private boolean checkBind(Long id) {
        QueryWrapper<DeviceIot> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", id);
        wrapper.eq("del_flag", 0);
        return deviceIotMapper.selectCount(wrapper) == 0;
    }


}
