package com.youibot.tms.biz.forest.dto.fleet5;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2022/12/14/13:50
 * @Description: 位置状态信息
 */
@Data
public class PositionStatus {
    /**
     * 定位状态
     * 未定位=NotLocated  已定位=Located
     */
    @ApiModelProperty(value = "定位状态 未定位=NotLocated 已定位=Located")
    private String status;

    /**
     * 地图Code
     */
    @ApiModelProperty(value = "AGV当前所在地图Code", position = 1)
    private String vehicleMapCode;

    /**
     * x坐标， 单位：m
     */
    @ApiModelProperty(value = "AGV当前轨迹点位置的x坐标, 单位:m", position = 2)
    private Double x;

    /**
     * y坐标， 单位：m
     */
    @ApiModelProperty(value = "AGV当前轨迹点位置的y坐标, 单位:m", position = 3)
    private Double y;

    /**
     * AGV当前轨迹点位置的绝对车体方向
     * 范围：[-180,180]
     */
    @ApiModelProperty(value = "AGV当前轨迹点位置的绝对车体方向, 范围:[-180,180]", position = 4)
    private Double direction;

    /**
     * 置信度
     */
    @ApiModelProperty(value = "定位置信度, 异常=0, 低=1, 中=2, 高=3", position = 5)
    private Double confidence;

    /**
     * 定位图编码Code
     */
    @ApiModelProperty(value = "AGV当前所在地图 定位图编码", position = 6)
    private String locatingCode;

    /**
     * 当前点位编码
     */
    @ApiModelProperty(value = "当前点位编码", position = 7)
    private String currentMarkerCode;

    /**
     * 当前路径编码
     */
    @ApiModelProperty(value = "当前路径编码", position = 8)
    private String currentPathCode;

    /**
     * 最后通过点位编码
     */
    @JsonIgnore
    private String lastMarkerCode;

    /**
     * 从路径起点到当前位置AGV走过的距离百分比
     */
    @JsonIgnore
    private Double progressRate;

    /**
     * 最后推送时间戳
     */
    @JsonIgnore
    private Long lastPushTime;
}
