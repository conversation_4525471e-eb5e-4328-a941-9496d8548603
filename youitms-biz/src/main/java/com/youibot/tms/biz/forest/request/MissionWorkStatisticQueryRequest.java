package com.youibot.tms.biz.forest.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <NAME_EMAIL> on 2023/2/28.
 *
 * <AUTHOR>
 * @date 2023/2/28 15:37
 */
@Data
@ApiModel("任务查询DTO")
public class MissionWorkStatisticQueryRequest {
    @ApiModelProperty("机器人编号")
    private String agvCode;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("枚举值：NONE、HOUR、DAY、MONTH")
    private String type;

}
