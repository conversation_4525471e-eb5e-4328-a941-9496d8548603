package com.youibot.tms.biz.service.impl;

import com.google.common.base.Throwables;
import com.shimizukenta.secs.SecsException;
import com.shimizukenta.secs.gem.Clock;
import com.shimizukenta.secs.secs1ontcpip.ext.multiclient.ClientConnection;
import com.shimizukenta.secs.secs2.Secs2;
import com.shimizukenta.secs.secs2.Secs2Exception;

import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.entity.FromToRecord;
import com.youibot.tms.biz.enums.AgvUsageStatus;
import com.youibot.tms.biz.forest.enums.VehicleStatus;
import com.youibot.tms.biz.entity.CommunicationDevice;
import com.youibot.tms.biz.secs.DeviceControlStateManager;
import com.youibot.tms.biz.secs.SxFyCommandHandlerHolder;
import com.youibot.tms.biz.secs.Util;
import com.youibot.tms.biz.secs.communicator.secs1OnTcpIp.Secs1OnTcpIpReceiverCommunicator;
import com.youibot.tms.biz.secs.enums.model.ControlStateEnum;
import com.youibot.tms.biz.utils.BayNameUtils;
import com.youibot.tms.biz.service.CommunicationDeviceService;
import com.youibot.tms.biz.service.AgvService;
import com.youibot.tms.biz.service.AgvStatusProcessingService;
import com.youibot.tms.biz.service.FleetProxyService;
import com.youibot.tms.biz.service.SecsService;
import com.youibot.tms.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.SocketAddress;
import java.util.Set;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
// 已移除定时任务相关的import
// import java.util.concurrent.Executors;
// import java.util.concurrent.ScheduledExecutorService;
// import java.util.concurrent.TimeUnit;

/**
 * secs服务
 *
 * <AUTHOR>
 * @date 2024-05-08 13:41
 */
@Slf4j
@Service
public class SecsServiceImpl implements SecsService, ApplicationListener<ApplicationReadyEvent> {
    // 使用agvCode作为键，因为agvCode是全局唯一的，而robotNumber只在bayName内唯一
    private static final Map<String, VehicleStatus> AGV_STATUS_CACHE = new HashMap<>();

    // 记录最后一次系统报告发送时间，避免重复发送
    private static volatile long lastSysReportTime = 0;
    private static final long SYS_REPORT_INTERVAL_MS = 1000; // 最小间隔1秒

    @Lazy
    @Resource
    private Secs1OnTcpIpReceiverCommunicator communicator;

    @Lazy
    @Resource
    private SxFyCommandHandlerHolder sxFyCommandHandlerHolder;

    @Autowired
    private DeviceControlStateManager deviceControlStateManager;

    @Autowired
    private CommunicationDeviceService communicationDeviceService;

    @Resource
    private FleetProxyService fleetProxyService;

    @Resource
    private AgvService agvService;

    @Resource
    private AgvStatusProcessingService agvStatusProcessingService;

    @Autowired
    private SecsReportDiagnosticService diagnosticService;

    // 已移除定时任务，不再需要ScheduledExecutorService
    // private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE = Executors.newSingleThreadScheduledExecutor();

    @Override
    public void onlineCheck(ClientConnection targetConnection) throws InterruptedException, SecsException {
        try {
            log.info("在线确认");
           targetConnection.getCommunicator().gem().s1f1();

            /*   secsMessage.ifPresent(msg -> sxFyCommandHandlerHolder.handle(msg));*/
        } catch (Exception e) {
            log.error("在线确认失败", e);
            throw e;
        }
    }

    /**
     * 每天0点自动同步一次
     */
//    @Scheduled(cron = "0 0 0 * * ?")
//    public synchronized void timeGet0() throws InterruptedException, SecsException  {
//        if (CachePool.getControl() != ControlStateEnum.ONLINE_REMOTE) {
//            return;
//        }
////        timeGet(targetConnection);
//    }


    @Override
    public synchronized void timeGet(ClientConnection targetConnection) throws InterruptedException, SecsException {
        try {
            log.info("开始时钟同步: {}", targetConnection.getRemoteAddress());

            // 发送S2F17时间请求
            Clock clock = targetConnection.getCommunicator().gem().s2f17();

            if (clock != null) {
                log.info("时钟同步成功: {}, 设备时间: {}",
                         targetConnection.getRemoteAddress(),
                         clock.toLocalDateTime());
            } else {
                log.warn("时钟同步返回空值: {}", targetConnection.getRemoteAddress());
            }

        } catch (Secs2Exception e) {
            // 处理SECS2解析异常，通常是设备返回了无效的时间格式
            log.error("时钟同步失败 - SECS2解析错误: {}, 错误信息: {}",
                      targetConnection.getRemoteAddress(), e.getMessage());

            // 检查是否是空字符串解析错误
            if (e.getMessage() != null && e.getMessage().contains("Parse Failed \"\"")) {
                log.warn("设备返回空的时间字符串: {}, 可能设备不支持时间同步或时间未设置",
                         targetConnection.getRemoteAddress());
            }

            // 不重新抛出异常，避免影响其他操作
        } catch (SecsException e) {
            log.error("时钟同步失败 - SECS通信错误: {}, 错误信息: {}",
                      targetConnection.getRemoteAddress(), e.getMessage());
            // 不重新抛出异常，避免影响其他操作
        } catch (Exception e) {
            log.error("时钟同步失败 - 未知错误: {}, 错误信息: {}",
                      targetConnection.getRemoteAddress(), e.getMessage(), e);
        }
    }



    @Override
    public void sysReport(SocketAddress sourceAddress, List<AgvDTO> agvDTOList, ControlStateEnum controlStateEnum) throws InterruptedException, SecsException {
        // 提取发送目标的IP和端口信息
        String targetInfo = extractSocketAddressInfo(sourceAddress);

        // 【诊断日志】记录调用栈信息，用于追踪重复发送源头
        String callStack = getSimplifiedCallStack();
        log.info("【SECS诊断】sysReport调用 - 目标: {}, 控制状态: {}, AGV数量: {}, 调用栈: {}",
                targetInfo, controlStateEnum, agvDTOList.size(), callStack);

        for (AgvDTO agvDTO : agvDTOList) {
            // 使用BayNameUtils验证AGV代码格式并提取robotNumber
            if (!BayNameUtils.isValidAgvCode(agvDTO.getName())) {
                log.debug("AGV name[{}]格式无效，跳过处理: agvCode={}",
                        agvDTO.getName(), agvDTO.getAgvCode());
                continue;
            }

            int robotNumber = BayNameUtils.extractRobotNumber(agvDTO.getName());

            // 使用BayNameUtils判断旧格式的有效性，或者检查新格式的范围
            boolean isValidRobotNumber = false;
            if (BayNameUtils.isLegacyAgvCode(agvDTO.getName())) {
                // 旧格式：1-10
                isValidRobotNumber = true;
            } else if (robotNumber >= 1 && robotNumber <= 10) {
                // 新格式：robotNumber在1-10范围内
                isValidRobotNumber = true;
            }

            if (isValidRobotNumber) {
                Agv agv = agvService.selectByAgvCode(agvDTO.getAgvCode());
                if (agv != null) {
                    boolean isTmsUsed = agv.getUsageStatus() == AgvUsageStatus.OCCUPIED && agvDTO.getStatus() == VehicleStatus.IDLE;
                    VehicleStatus finalStatus = isTmsUsed ? VehicleStatus.RUN : agvDTO.getStatus();

                    // 使用agvCode作为缓存键，因为agvCode是全局唯一的，而robotNumber只在bayName内唯一
                    AGV_STATUS_CACHE.put(agvDTO.getAgvCode(), finalStatus);
                    log.debug("更新AGV状态缓存: name={}, agvCode={}, robotNumber={}, status={}",
                            agvDTO.getName(), agvDTO.getAgvCode(), robotNumber, finalStatus);
                } else {
                    log.warn("未找到AGV记录: agvCode={}, name={}", agvDTO.getAgvCode(), agvDTO.getName());
                }
            } else {
                log.warn("AGV robotNumber[{}]超出SECS支持范围(1-10): name={}, agvCode={}",
                        robotNumber, agvDTO.getName(), agvDTO.getAgvCode());
            }
        }

        try {
            // 根据当前bayName判断是否有空闲机器人来设置transportError
//            boolean transportError = calculateTransportError(agvDTOList);
            boolean transportError = false;

            log.info("【SECS诊断】准备发送系统报告到 {} (控制状态: {}, AGV数量: {}, 搬送队列错误: {}, 调用栈: {})",
                    targetInfo, controlStateEnum, agvDTOList.size(), transportError, callStack);

            // 记录发送时间戳用于重复发送检测
            long sendTimestamp = System.currentTimeMillis();
            recordSysReportSend(targetInfo, sendTimestamp, callStack);

            // 记录到诊断服务
            String agvInfo = String.format("AGV数量:%d,状态:%s", agvDTOList.size(), controlStateEnum);
            diagnosticService.recordSysReportSend(targetInfo, callStack, agvInfo);

            communicator.send(sourceAddress, 66, 1, true,
                    Util.s66f1(controlStateEnum == ControlStateEnum.ONLINE_REMOTE, false, transportError, agvDTOList));
            log.info("【SECS诊断】系统报告发送成功到 {} - 耗时: {}ms", targetInfo, System.currentTimeMillis() - sendTimestamp);

        } catch (Exception e) {
            log.error("系统报告发送失败到 {}: {}", targetInfo, e.getMessage(), e);
            throw e;
        }
    }

    /**
     *
     * 根据当前bayName判断是否有空闲机器人来计算transportError
     * 1. 根据车辆 可以直接
     *   指令队列最大长度: online agv 数量* 4*K
     * @param agvDTOList 当前bayName的AGV列表
     * @return true表示没有空闲机器人（搬送指示队列已满），false表示有空闲机器人
     */
    private boolean calculateTransportError(List<AgvDTO> agvDTOList) {
        if (agvDTOList == null || agvDTOList.isEmpty()) {
            // 如果没有AGV，认为没有空闲机器人
            return true;
        }

        // 从AGV列表中提取bayName（所有AGV应该属于同一个bayName）
        String bayName = null;
        for (AgvDTO agv : agvDTOList) {
            String agvBayName = BayNameUtils.extractBayNameFromAgvName(agv.getName());
            if (agvBayName != null) {
                bayName = agvBayName;
                break;
            }
        }

        if (bayName == null) {
            log.warn("无法从AGV列表中提取bayName，默认认为没有空闲机器人");
            return true;
        }

        // 检查该bayName是否有空闲机器人
        boolean hasIdleRobots = hasIdleRobotsInBayName(bayName, agvDTOList);

        // transportError: true表示搬送指示队列已满（没有空闲机器人），false表示有空闲机器人
        boolean transportError = !hasIdleRobots;

        log.debug("bayName[{}]空闲机器人检查: 有空闲={}, transportError={}", bayName, hasIdleRobots, transportError);
        return transportError;
    }

    /**
     * 检查指定bayName是否有空闲机器人
     *
     * @param bayName 目标bayName
     * @param agvDTOList 该bayName的AGV列表
     * @return true表示有空闲机器人，false表示没有空闲机器人
     */
    private boolean hasIdleRobotsInBayName(String bayName, List<AgvDTO> agvDTOList) {
        // 检查AGV列表中是否有空闲状态的机器人
        long idleCount = agvDTOList.stream()
                .filter(agv -> agv.getStatus() == VehicleStatus.IDLE)
                .filter(agv -> agv.getUsageStatus() != AgvUsageStatus.OCCUPIED) // 排除被TMS占用的
                .count();

        log.debug("bayName[{}]空闲AGV数量: {}/{}", bayName, idleCount, agvDTOList.size());
        return idleCount > 0;
    }

    @Override
    public void cancel(List<FromToRecord> records) {
        if (records == null || records.isEmpty()) {
            log.warn("取消指令记录列表为空，跳过处理");
            return;
        }

        // 按设备分组处理记录，因为不同设备的记录需要发送到不同的客户端
        Map<String, List<FromToRecord>> recordsByDevice = groupRecordsByDevice(records);

        for (Map.Entry<String, List<FromToRecord>> entry : recordsByDevice.entrySet()) {
            String deviceKey = entry.getKey();
            List<FromToRecord> deviceRecords = entry.getValue();

            try {
                sendCancelCommandToDevice(deviceKey, deviceRecords);
            } catch (Exception e) {
                log.error("向设备[{}]发送取消指令失败: {}", deviceKey, e.getMessage(), e);
                // 继续处理其他设备的记录
            }
        }
    }

    /**
     * 按设备分组FromToRecord记录
     * 优先使用deviceIp，如果没有则使用deviceId
     */
    private Map<String, List<FromToRecord>> groupRecordsByDevice(List<FromToRecord> records) {
        Map<String, List<FromToRecord>> groupedRecords = new HashMap<>();

        for (FromToRecord record : records) {
            String deviceKey;
            if (record.getDeviceIp() != null) {
                deviceKey = "IP:" + record.getDeviceIp();
            } else if (record.getDeviceId() != null) {
                deviceKey = "ID:" + record.getDeviceId();
            } else {
                log.warn("记录[{}]缺少设备标识，跳过处理", record.getId());
                continue;
            }

            groupedRecords.computeIfAbsent(deviceKey, k -> new ArrayList<>()).add(record);
        }

        return groupedRecords;
    }

    /**
     * 向指定设备发送取消指令
     * 注意：这里发送的是同一个设备的多个FromToRecord记录，这是合理的，
     * 因为一个commandId可能包含多个搬运任务，都需要一起取消
     */
    private void sendCancelCommandToDevice(String deviceKey, List<FromToRecord> deviceRecords) {
        FromToRecord firstRecord = deviceRecords.get(0);
        String commandId = firstRecord.getCommandId();

        log.info("CommandId: {}, 准备向设备[{}]发送取消指令，包含{}条记录", commandId, deviceKey, deviceRecords.size());

        // 构建取消指令数据 - S66F45格式
        List<Secs2> root = new ArrayList<>();
        Secs2 statusL = Secs2.list(Secs2.binary((byte) 5)); // 状态：5=取消完成
        Secs2 agvL = Secs2.list(Secs2.binary((byte) 0));    // AGV编号：0表示所有
        Secs2 numL = Secs2.list(Secs2.binary((byte) deviceRecords.size())); // 记录数量
        root.add(statusL);
        root.add(agvL);
        root.add(numL);

        // 构建每个搬运记录的数据
        List<Secs2> nodes = new ArrayList<>();
        for (FromToRecord record : deviceRecords) {
            Secs2 node = Secs2.list(
                    Secs2.ascii(record.getCarrierId()),  // 载具ID
                    Secs2.binary((byte) 1),              // 优先级
                    Secs2.list(
                            Secs2.ascii(record.getFromCode()), // 起点
                            Secs2.ascii(record.getToCode())    // 终点
                    )
            );
            nodes.add(node);
            log.debug("CommandId: {}, 添加取消记录: 载具[{}] 从[{}]到[{}]", commandId, record.getCarrierId(), record.getFromCode(), record.getToCode());
        }
        root.add(Secs2.list(nodes));

        Secs2 data = Secs2.list(root);

        // 检查对应设备的控制状态
        String deviceIp = firstRecord.getDeviceIp();
        if (deviceIp != null) {
            // 使用设备IP进行精确的状态检查
            if (!deviceControlStateManager.isDeviceOnlineByIp(deviceIp)) {
                log.info("CommandId: {}, 设备[IP:{}]offline状态不发送取消指令", commandId, deviceIp);
                throw new RuntimeException("设备offline状态不发送取消指令");
            }
        } else {
            // 兼容旧数据，使用deviceId检查（可能不准确）
            if (!deviceControlStateManager.isDeviceOnline(firstRecord.getDeviceId())) {
                log.info("CommandId: {}, 设备[ID:{}]offline状态不发送取消指令", commandId, firstRecord.getDeviceId());
                throw new RuntimeException("设备offline状态不发送取消指令");
            }
        }

        try {
            // 获取目标地址
            Set<SocketAddress> targetAddresses =null;
            if (deviceIp != null) {
                // 优先使用设备IP精确查找
                targetAddresses = Util.getAddressesByIp(deviceIp);
                log.debug("CommandId: {}, 使用设备IP[{}]查找目标地址", commandId, deviceIp);
            }

            if (targetAddresses.isEmpty()) {
                log.error("CommandId: {}, 未找到设备[{}]对应的客户端连接", commandId, deviceKey);
                throw new RuntimeException("未找到对应的客户端连接");
            }

            // 向所有目标地址发送取消指令
            int successCount = 0;
            for (SocketAddress address : targetAddresses) {
                try {
                    this.communicator.send(address, 66, 45, true, data);
                    successCount++;
                    log.info("CommandId: {}, 成功向设备[{}]发送S66F45取消指令，包含{}条记录", commandId, deviceKey, deviceRecords.size());
                } catch (Exception e) {
                    log.error("CommandId: {}, 向设备[{}]发送S66F45取消指令失败: {}", commandId, deviceKey, e.getMessage());
                }
            }

            if (successCount == 0) {
                throw new RuntimeException("所有目标地址发送取消指令都失败");
            }

            log.info("CommandId: {}, 设备[{}]S66F45取消指令发送完成: 成功{}/总共{}个地址", commandId, deviceKey, successCount, targetAddresses.size());

        } catch (Exception e) {
            log.error("CommandId: {}, 设备[{}]SECS S66F45取消指令发送异常：{}", commandId, deviceKey, Throwables.getStackTraceAsString(e));

            // 设置对应设备为离线状态
//            if (deviceIp != null) {
//                deviceControlStateManager.setControlStateByIp(deviceIp, ControlStateEnum.OFFLINE_EQ_OFFLINE);
//            } else {
//                deviceControlStateManager.setControlState(firstRecord.getDeviceId(), ControlStateEnum.OFFLINE_EQ_OFFLINE);
//            }

            throw new ServiceException("SECS S66F45取消指令发送异常：" + e.getMessage());
        }
    }

    @Override
    public void sysReport(ClientConnection targetConnection, List<AgvDTO> agvDTOList, ControlStateEnum controlStateEnum) {
        try {
            sysReport( targetConnection.getRemoteAddress(), agvDTOList, controlStateEnum);
        } catch (Exception e) {
            log.error("error:{}" , Throwables.getStackTraceAsString(e));
        }
    }













    /**
     * 获取与指定设备地址相关的AGV列表
     * 只返回与设备bayName匹配的AGV
     *
     * @param address 设备地址
     * @param allAgvs 所有AGV列表
     * @return 相关的AGV列表
     */
    private List<AgvDTO> getRelevantAgvsForAddress(SocketAddress address, List<AgvDTO> allAgvs) {
        try {
            // 从SocketAddress提取IP地址
            String deviceIp = extractIpFromSocketAddress(address);
            if (deviceIp == null) {
                log.warn("无法从地址[{}]提取IP，返回所有AGV", address);
                return allAgvs;
            }

            // 根据IP地址查找设备
            CommunicationDevice device = communicationDeviceService.getByIpAddress(deviceIp);
            if (device == null || device.getBayName() == null) {
                log.warn("设备[IP:{}]不存在或未设置bayName，返回所有AGV", deviceIp);
                return allAgvs;
            }

            String bayName = device.getBayName();
            log.debug("设备[IP:{}]的bayName为[{}]，过滤相关AGV", deviceIp, bayName);

            // 过滤出与bayName相关的AGV
            List<AgvDTO> relevantAgvs = new ArrayList<>();
            for (AgvDTO agv : allAgvs) {
                String agvCode = agv.getAgvCode();

                // 检查AGV代码是否以bayName开头（新格式：bayName_robotNumber）
                if (agvCode != null && agvCode.startsWith(bayName + "_")) {
                    relevantAgvs.add(agv);
                    log.debug("AGV[{}]属于设备[{}]", agvCode, bayName);
                }
                // 兼容旧格式：如果AGV代码是纯数字，则包含所有（向后兼容）
                else if (agvCode != null && agvCode.matches("^[1-9]|10$")) {
                    // 对于纯数字格式的AGV代码，暂时包含所有，后续可以根据具体需求调整
                    relevantAgvs.add(agv);
                    log.debug("AGV[{}]使用旧格式，包含在设备[{}]的报告中", agvCode, bayName);
                }
            }

            log.debug("设备[IP:{}, bayName:{}]相关的AGV数量: {}/{}", deviceIp, bayName, relevantAgvs.size(), allAgvs.size());
            return relevantAgvs;

        } catch (Exception e) {
            log.error("获取设备相关AGV时发生异常: {}", e.getMessage(), e);
            // 发生异常时返回所有AGV，确保系统正常运行
            return allAgvs;
        }
    }

    /**
     * 从SocketAddress中提取IP地址
     *
     * @param address SocketAddress
     * @return IP地址字符串
     */
    private String extractIpFromSocketAddress(SocketAddress address) {
        if (address == null) {
            return null;
        }

        String addressStr = address.toString();
        // 格式通常是 /IP:PORT，需要提取IP部分
        if (addressStr.startsWith("/")) {
            addressStr = addressStr.substring(1);
        }

        int colonIndex = addressStr.lastIndexOf(':');
        if (colonIndex > 0) {
            return addressStr.substring(0, colonIndex);
        }

        return addressStr;
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("SECS服务启动完成 - 已移除定时AGV状态监控，改为Fleet主动推送模式");
        // 注释掉定时任务，避免与Fleet推送造成S5F1重复发送
        // SCHEDULED_EXECUTOR_SERVICE.scheduleAtFixedRate(() -> {
        //     try {
        //         agvStatusMonitor();
        //     } catch (Exception e) {
        //         e.printStackTrace();
        //     }
        // }, 0, 100, TimeUnit.MILLISECONDS);
    }

    // ======================== 诊断辅助方法 ========================

    /**
     * 系统报告发送记录缓存 - 用于检测重复发送
     * Key: targetInfo, Value: List<SendRecord>
     */
    private static final Map<String, List<SendRecord>> SYS_REPORT_SEND_HISTORY = new ConcurrentHashMap<>();

    /**
     * 发送记录内部类
     */
    private static class SendRecord {
        private final long timestamp;
        private final String callStack;

        public SendRecord(long timestamp, String callStack) {
            this.timestamp = timestamp;
            this.callStack = callStack;
        }

        public long getTimestamp() { return timestamp; }
        public String getCallStack() { return callStack; }
    }

    /**
     * 记录系统报告发送信息
     */
    private void recordSysReportSend(String targetInfo, long timestamp, String callStack) {
        SYS_REPORT_SEND_HISTORY.computeIfAbsent(targetInfo, k -> new ArrayList<>()).add(new SendRecord(timestamp, callStack));

        // 检测短时间内的重复发送（5秒内）
        List<SendRecord> records = SYS_REPORT_SEND_HISTORY.get(targetInfo);
        long currentTime = System.currentTimeMillis();

        // 清理5分钟前的记录
        records.removeIf(record -> currentTime - record.getTimestamp() > 300000);

        // 检测5秒内的重复发送
        long recentCount = records.stream()
                .filter(record -> currentTime - record.getTimestamp() <= 5000)
                .count();

        if (recentCount > 1) {
            log.warn("【SECS诊断】检测到可能的重复发送！目标: {}, 5秒内发送次数: {}", targetInfo, recentCount);

            // 输出最近的调用栈信息
            records.stream()
                    .filter(record -> currentTime - record.getTimestamp() <= 5000)
                    .forEach(record -> log.warn("【SECS诊断】重复发送记录 - 时间: {}, 调用栈: {}",
                            new Date(record.getTimestamp()), record.getCallStack()));
        }
    }

    /**
     * 获取简化的调用栈信息
     */
    private String getSimplifiedCallStack() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        StringBuilder sb = new StringBuilder();

        // 跳过前几个系统调用，找到业务相关的调用
        for (int i = 3; i < Math.min(stackTrace.length, 8); i++) {
            StackTraceElement element = stackTrace[i];
            String className = element.getClassName();

            // 只记录业务相关的类
            if (className.contains("youibot.tms") &&
                !className.contains("SecsServiceImpl") &&
                !className.contains("$$")) {

                String simpleClassName = className.substring(className.lastIndexOf('.') + 1);
                sb.append(simpleClassName).append(".").append(element.getMethodName());

                if (i < Math.min(stackTrace.length - 1, 7)) {
                    sb.append(" -> ");
                }
            }
        }

        return sb.length() > 0 ? sb.toString() : "Unknown";
    }

    /**
     * 向指定bayName对应的设备发送系统报告
     * @param bayName 目标bayName
     * @param agvs 该bayName的AGV列表
     */
    @Override
    public void sendSysReportToBayName(String bayName, List<AgvDTO> agvs) {
        try {
            // 根据bayName找到对应的设备地址
            Set<SocketAddress> bayNameAddresses = getBayNameAddresses(bayName);

            if (bayNameAddresses.isEmpty()) {
                log.warn("bayName[{}]没有找到对应的在线设备地址", bayName);
                return;
            }

            // 向该bayName的所有在线设备发送系统报告
            for (SocketAddress address : bayNameAddresses) {
                try {
                    ControlStateEnum deviceState = deviceControlStateManager.getControlStateByAddress(address);
                    log.debug("向bayName[{}]的设备[{}]发送系统报告，设备状态: {}，AGV数量: {}",
                            bayName, address, deviceState, agvs.size());
                    sysReport(address, agvs, deviceState);
                } catch (Exception e) {
                    log.error("向bayName[{}]的设备[{}]发送系统报告失败: {}", bayName, address, e.getMessage(), e);
                    // 发送失败时，将对应设备设置为离线状态
//                    deviceControlStateManager.setControlStateByAddress(address, ControlStateEnum.OFFLINE_EQ_OFFLINE);
                }
            }
        } catch (Exception e) {
            log.error("向bayName[{}]发送系统报告时发生异常: {}", bayName, e.getMessage(), e);
        }
    }

    /**
     * 根据bayName获取对应的在线设备地址
     * @param bayName 目标bayName
     * @return 该bayName对应的在线设备地址集合
     */
    private Set<SocketAddress> getBayNameAddresses(String bayName) {
        try {
            // 首先尝试使用现有的工具方法
            Set<SocketAddress> addresses = Util.getClientAddressesByBayName(bayName);

            if (!addresses.isEmpty()) {
                // 过滤出在线的设备地址
                Set<SocketAddress> onlineAddresses = new HashSet<>();
                for (SocketAddress address : addresses) {
                    if (deviceControlStateManager.isDeviceOnlineByAddress(address)) {
                        onlineAddresses.add(address);
                        log.debug("找到bayName[{}]对应的在线设备: {}", bayName, address);
                    } else {
                        log.debug("设备[{}]不在线，跳过", address);
                    }
                }

                if (!onlineAddresses.isEmpty()) {
                    log.debug("bayName[{}]通过工具方法找到{}个在线设备地址", bayName, onlineAddresses.size());
                    return onlineAddresses;
                }
            }

            // 如果工具方法没有找到结果，回退到直接查询TCP连接的方式
            log.debug("bayName[{}]工具方法未找到结果，回退到直接查询TCP连接", bayName);
            return getBayNameAddressesFallback(bayName);

        } catch (Exception e) {
            log.error("获取bayName[{}]对应的设备地址时发生异常: {}", bayName, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 回退方法：直接查询TCP连接来查找bayName对应的设备地址
     */
    private Set<SocketAddress> getBayNameAddressesFallback(String bayName) {
        Set<SocketAddress> addresses = new HashSet<>();

        try {
            Collection<ClientConnection> connections = communicator.getConnections();
            log.debug("回退查找bayName[{}]对应的设备地址，当前TCP连接数: {}", bayName, connections.size());

            for (ClientConnection connection : connections) {
                SocketAddress address = connection.getRemoteAddress();

                // 检查设备是否在线
                if (!deviceControlStateManager.isDeviceOnlineByAddress(address)) {
                    log.debug("设备[{}]不在线，跳过", address);
                    continue;
                }

                // 根据地址获取设备信息
                String deviceIp = extractIpFromSocketAddress(address);
                if (deviceIp != null) {
                    CommunicationDevice device = communicationDeviceService.getByIpAddress(deviceIp);
                    if (device != null && bayName.equals(device.getBayName())) {
                        addresses.add(address);
                        log.debug("回退方法找到bayName[{}]对应的在线设备: {} (IP: {})", bayName, address, deviceIp);

                        // 同时更新bayName到IP的映射，以便下次能通过工具方法找到
                        Util.setBayNameToIpMapping(bayName, deviceIp);
                    } else {
                        log.debug("设备[IP: {}]的bayName[{}]与目标bayName[{}]不匹配",
                                deviceIp, device != null ? device.getBayName() : "null", bayName);
                    }
                } else {
                    log.warn("无法从地址[{}]提取IP", address);
                }
            }

            log.debug("回退方法为bayName[{}]找到{}个匹配的在线设备地址", bayName, addresses.size());
        } catch (Exception e) {
            log.error("回退查找bayName[{}]对应的设备地址时发生异常: {}", bayName, e.getMessage(), e);
        }

        return addresses;
    }

    /**
     * 提取SocketAddress的IP和端口信息用于日志记录
     * @param socketAddress 套接字地址
     * @return 格式化的地址信息字符串
     */
    private String extractSocketAddressInfo(SocketAddress socketAddress) {
        if (socketAddress == null) {
            return "unknown";
        }

        String addressStr = socketAddress.toString();
        // 处理InetSocketAddress格式: /*************:5000 或 hostname/*************:5000
        if (addressStr.startsWith("/")) {
            return addressStr.substring(1); // 移除开头的斜杠
        } else if (addressStr.contains("/")) {
            // 提取IP:PORT部分
            int slashIndex = addressStr.lastIndexOf("/");
            return addressStr.substring(slashIndex + 1);
        }

        return addressStr;
    }




    /**
     * 向特定的ClientConnection发送S5F1报警消息
     * 每个AGV只向其对应的ClientConnection发送报警消息
     * 注意：现在使用vehicleName（从name字段解析）作为key
     */
    public void sendS5F1ToSpecificClients(Map<String, Util.AlarmInfo> s5f1AlarmMap) {
        for (Map.Entry<String, Util.AlarmInfo> entry : s5f1AlarmMap.entrySet()) {
            String vehicleName = entry.getKey(); // 现在key是vehicleName而不是agvCode
            Util.AlarmInfo alarmInfo = entry.getValue();

            try {

                // 根据vehicleName获取对应的客户端连接地址
                Set<SocketAddress> clientAddresses = Util.getClientAddressesByAgvName( vehicleName);
                if (clientAddresses.isEmpty()) {
                    log.warn("未找到AGV[{}]对应的客户端连接，跳过S5F1发送", vehicleName);
                    continue;
                }

                // 构建S5F1消息
                Secs2 s5f1Data = Util.buildS5F1AlarmNotification(
                        alarmInfo.getAlarmCode(),
                        alarmInfo.getAlarmType(),
                        alarmInfo.getRobotNumber(),
                        alarmInfo.getAlarmText()
                );

                // 向AGV对应的所有客户端连接发送S5F1消息
                int successCount = 0;
                for (SocketAddress clientAddress : clientAddresses) {
                    try {
                        communicator.send(clientAddress, 5, 1, true, s5f1Data);
                        successCount++;
                        log.debug("成功向AGV[{}]的客户端[{}]发送S5F1消息", vehicleName, clientAddress);
                    } catch (Exception e) {
                        log.error("【S5F1发送失败】向AGV[{}]的客户端[{}]发送S5F1消息失败: {}", vehicleName, clientAddress, Throwables.getStackTraceAsString(e));
                        // 发送失败时，将对应设备设置为离线状态
                        log.warn("【OFFLINE_EQ_OFFLINE】S5F1通信失败自动离线 - AGV[{}]客户端[{}]S5F1发送失败，自动设置为OFFLINE_EQ_OFFLINE状态", vehicleName, clientAddress);
//                        deviceControlStateManager.setControlStateByAddress(clientAddress, ControlStateEnum.OFFLINE_EQ_OFFLINE);
                    }
                }

                log.info("AGV[{}]的S5F1报警消息发送完成: 成功{}/总共{}个客户端, 报警内容: {}",
                        vehicleName, successCount, clientAddresses.size(), alarmInfo.getAlarmText());

            } catch (Exception e) {
                log.error("处理AGV[{}]的S5F1报警消息失败: {}", vehicleName, e.getMessage(), e);
            }
        }
    }


}
