package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VehicleDetailPathApiDTO",description = "路径详情")
public class VehicleDetailPathApiDTO {

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "开始点", position = 2)
    private String startMarkerCode;

    @ApiModelProperty(value = "结束点", position = 3)
    private String endMarkerCode;

}
