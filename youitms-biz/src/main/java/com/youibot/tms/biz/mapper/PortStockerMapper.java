package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.entity.PortStocker;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * stocker储位表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-29
 */
public interface PortStockerMapper extends BaseMapper<PortStocker> {

    @Update("update biz_port_stocker set enabled = #{enabled} where id = #{id}")
    void updateEnabledById(PortStocker entity);

    @Select(" select a.id, a.code, a.name, a.zone_stocker_id, a.device_iot_signal_id, a.device_id, a.enabled, a.pre_action_param, a.agv_type, a.action_param,a.create_time,a.update_time " +
            " from biz_port_stocker a " +
            " ${ew.customSqlSegment} ")
    List<PortStocker> selectList(@Param("ew") Wrapper<PortStocker> queryWrapper);

    void updateBaseById(PortStocker entity);

    List<PortStocker> selectListByCode(String code);
}
