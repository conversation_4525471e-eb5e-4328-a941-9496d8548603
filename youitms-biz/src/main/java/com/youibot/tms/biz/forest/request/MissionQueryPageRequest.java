package com.youibot.tms.biz.forest.request;

import com.youibot.tms.common.core.request.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("任务查询DTO")
public class MissionQueryPageRequest extends PageRequest {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "任务组ID", position = 1)
    private String missionGroupId;


    @ApiModelProperty(value = "任务名称", example = "自动运输任务", position = 2)
    private String name;


    @ApiModelProperty(value = "任务编号", example = "M001", position = 4)
    private String code;


    @ApiModelProperty(value = "任务描述", example = "自动运输任务", position = 3)
    private String description;


    @ApiModelProperty(value = "机器人的ID,任务指定执行的机器人ID.", position = 4)
    private String agvCode;


    @ApiModelProperty(value = "机器人组的ID,任务只会分配给组内的机器人", position = 5)
    private String agvGroupId;


    @ApiModelProperty(value = "机器人类型,任务只会分配给类型内的机器人", position = 6)
    private String agvType;
}
