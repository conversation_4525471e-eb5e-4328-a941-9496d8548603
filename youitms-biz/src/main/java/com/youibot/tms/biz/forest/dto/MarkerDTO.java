package com.youibot.tms.biz.forest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 标记点
 * 类名称：Marker
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@ApiModel(value = "导航点DTO", description = "标记点")
public class MarkerDTO  {


    @ApiModelProperty(value="ID", position = 0)
    private String id;

    
    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    
    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    
    @ApiModelProperty(value = "x坐标", position = 3)
    private Double x;

    
    @ApiModelProperty(value = "y坐标", position = 4)
    private Double y;

    
    @ApiModelProperty(value = "描述", position = 5)
    private String description;

    
    @ApiModelProperty(value = "地图的ID", position = 6)
    private String agvMapId;

    
    @ApiModelProperty(value = "角度", position = 7)
    private Double angle;

    
    @ApiModelProperty(value = "类型 INITIAL_MARKER:初始点, CHARGING_MARKER:充电点, NAVIGATION_MARKER:导航点, QRCODE_MARKER:二维码点, WAIT_MARKER:待机点, WORK_MARKER:工作点, ADJUSTMENT_MARKER:调整点, ELEVATOR_MARKER:电梯点", position = 8)
    private String type;

    
    @ApiModelProperty(value = "协方差矩阵", position = 9)
    private String covariance;

    
    @ApiModelProperty(value = "使用状态 ENABLE:启用  DISABLE:禁用", position = 10)
    private String usageStatus;

    
    @ApiModelProperty(value = "创建时间", position = 11)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    
    @ApiModelProperty(value = "创建时间", position = 12)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

}
