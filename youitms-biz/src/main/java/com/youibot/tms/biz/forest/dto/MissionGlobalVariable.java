package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务全局变量
 * <AUTHOR>  E-mail:shis<PERSON><EMAIL>
 * @version CreateTime: 2020/4/22 19:44
 */
@Data
@ApiModel(value = "MissionGlobalVariable", description = "调度系统任务全局变量")
public class MissionGlobalVariable implements Serializable {

 
    /**  
	 * @Fields serialVersionUID : TODO(描述)
	 * <AUTHOR>
	 * @date 2021-03-09 06:11:52 
	 */  
	
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "ID")
    private String id;

 
    @ApiModelProperty(value = "任务的ID", position = 1)
    private String missionId;


    @ApiModelProperty(value = "变量键", position = 2)
    private String variableKey;

  
    @ApiModelProperty(value = "变量值", position = 3)
    private String variableValue;

    
    @ApiModelProperty(value = "创建时间", position = 4)
    private Date createTime;

   
    @ApiModelProperty(value = "更新时间", position = 5)
    private Date updateTime;

}
