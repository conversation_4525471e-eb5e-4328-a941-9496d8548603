package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "MarkerSortQueryApiDTO",description = "点位排序查询对象")
public class MarkerSortQueryApiDTO {

    @ApiModelProperty(value = "机器人编号", required = true)
    private String vehicleCode;

    @ApiModelProperty(value = "点位编号列表", required = true)
    private List<String> markerCodes;

}
