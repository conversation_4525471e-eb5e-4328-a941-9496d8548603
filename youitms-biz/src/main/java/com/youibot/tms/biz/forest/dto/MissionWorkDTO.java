package com.youibot.tms.biz.forest.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper=false)
@Data
@ApiModel(value = "MissionWorkDTO", description = "任务工作")
public class MissionWorkDTO  implements Serializable {

	public final static String CALL_BACK_URL_SUFFIX = "/outlet/tms/callback" ;
	/**
	 * @Fields serialVersionUID : TODO(描述)
	 * <AUTHOR>
	 * @date 2021-03-09 06:11:16
	 */

	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "ID", position = 0)
	private String id;

	@ApiModelProperty(value = "agv的编号", position = 1)
	private String agvCode;

	@ApiModelProperty(value = "任务的ID", position = 2)
	private String missionId;

	@ApiModelProperty(value = "调度计划的ID", position = 3)
	private String schedulePlanId;

	@ApiModelProperty(value = "回调接口", position = 4)
	private String callbackUrl;

	@ApiModelProperty(value = "名称", position = 5)
	private String name;

	@ApiModelProperty(value = "优先级,1:低，2：普通，3：高，4：最高. 默认是2", position = 6)
	private Integer sequence;

	@ApiModelProperty(value = "描述", position = 7)
	private String description;

	@ApiModelProperty(value = "状态 CREATE:创建(未执行) ASSIGNED:已分配 START:开始执行 WAIT:等待(继续)执行 RUNNING:执行中 SUCCESS:执行成功 FAULT:执行错误 PAUSE:暂停 BEING_PAUSE:暂停中 BEING_RESUME:恢复中 SHUTDOWN:已停止 BEING_SHUTDOWN:停止中 WAITINPUT:等待输入", position = 8)
	private MissionWorkStatus status;


	@ApiModelProperty(value = "上一个状态", position = 8)
	private String preStatus;

    @ApiModelProperty(value = "异常信息", position = 9)
	private String message;

	@ApiModelProperty(value = "是否可中断", position = 10)
	private Boolean interrupt;

	@ApiModelProperty(value = "运行时参数(json格式) 如：{\"marker1\":\"1001\"}", position = 11)
	private String runtimeParam;

//    @JsonFormat(timezone="GMT+8")

	@ApiModelProperty(value = "开始时间", position = 12)
	private Date startTime;

	@ApiModelProperty(value = "结束时间", position = 13)
	private Date endTime;

	@ApiModelProperty(value = "任务链ID", position = 14)
	private String missionWorkChainId;

	@ApiModelProperty(value = "机器人组的ID,任务只会分配给组内的机器人", position = 15)
	private String agvGroupId;

	@ApiModelProperty(value = "机器人类型,任务只会分配给类型内的机器人", position = 16)
	private String agvType;

	@ApiModelProperty(value = "创建时间", position = 17)
	private Date createTime;

	@ApiModelProperty(value = "更新时间", position = 18)
	private Date updateTime;

	@ApiModelProperty(value = "当前任务序号", position = 19)
	private Integer currentActionSequence;

	@ApiModelProperty(value = "异常错误代码", position = 20)
	private Integer errorCode;

	@ApiModelProperty(value = "机器人名称", position = 21)
	private String agvName;

	@ApiModelProperty(value = "任务组ID", position = 22)
	private String missionGroupId;

	@ApiModelProperty(value = " 任务呼叫id", position = 23)
	private String missionCallId;

	
	/**
	 * @Title: checkSuccess
	 * @Description: 检查是否成功
	 * @return
	 * <AUTHOR>
	 * @date 2021-04-21 05:20:43
	 */
	public boolean checkSuccess() {

		return this.status.equals(MissionWorkStatus.SUCCESS);
	}
	public boolean isShutDown() {
		
		return this.status.equals(MissionWorkStatus.SHUTDOWN);
	}

	/**
	 * @Title: checkFault
	 * @Description: 检查是否是错误状态
	 * @return
	 * <AUTHOR>
	 * @date 2021-04-21 05:23:01
	 */
	public boolean checkFault() {

		return this.status.equals(MissionWorkStatus.FAULT);
	}

	
	
}
