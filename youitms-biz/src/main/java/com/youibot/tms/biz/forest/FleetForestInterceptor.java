package com.youibot.tms.biz.forest;

import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestRequestBody;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.common.utils.spring.SpringUtils;
import com.youibot.tms.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.youibot.tms.common.constant.SysConfigKeyConstants.SYS_FLEET_HOST;

/**
 * fleet接口请求异常处理
 * <NAME_EMAIL> on 2022/12/7.
 *
 * <AUTHOR>
 * @date 2022/12/7 10:28
 */
@Slf4j
public class FleetForestInterceptor implements Interceptor<String> {


    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
        String fleetHost = configService.selectConfigValueByKey(SYS_FLEET_HOST);
        if (ToolUtil.isEmpty(fleetHost)) {
            log.error("Fleet接口请求地址未配置");
            throw new BusinessException(BizErrorCode.FLEET_NOT_CONFIG);
        } else {
            String content = "";
            if (response != null && response.getContent() != null) {
                content = response.getContent();
            }
            log.error("请求fleet接口发生异常,Fleet接口地址:{},响应信息：{}，异常信息:{}", request.getUrl(), content, Throwables.getStackTraceAsString(ex));
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED, request.getUrl());
        }

    }
}
