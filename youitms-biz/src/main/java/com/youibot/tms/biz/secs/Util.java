package com.youibot.tms.biz.secs;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.shimizukenta.secs.SecsException;

import com.shimizukenta.secs.secs1ontcpip.ext.multiclient.ClientConnection;
import com.shimizukenta.secs.secs2.Secs2;

import com.shimizukenta.secs.secs2.impl.Secs2Binary;
import com.shimizukenta.secs.secs2.impl.Secs2List;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.cache.AgvDTOCache;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.entity.Task;
import com.youibot.tms.biz.enums.AgvUsageStatus;
import com.youibot.tms.biz.enums.DeviceConnectionStatusEnum;
import com.youibot.tms.biz.enums.TaskStatus;
import com.youibot.tms.biz.forest.dto.fleet5.AbnormalApiDTO;
import com.youibot.tms.biz.forest.dto.fleet5.MosStorageInfo;
import com.youibot.tms.biz.forest.enums.VehicleStatus;
import com.youibot.tms.biz.secs.communicator.secs1OnTcpIp.Secs1OnTcpIpReceiverCommunicator;
import com.youibot.tms.biz.secs.enums.TaskSourceEnum;
import com.youibot.tms.biz.service.AgvService;
import com.youibot.tms.biz.service.CommunicationDeviceService;
import com.youibot.tms.biz.service.FleetProxyService;
import com.youibot.tms.biz.service.TaskService;
import com.youibot.tms.biz.utils.BayNameUtils;
import com.youibot.tms.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Tms业务定制的Gem协议
 *
 * <AUTHOR>
 * @date 2024-05-10 14:44
 */
@Slf4j
public class Util  {

    /**
     * AGV执行中任务信息
     */
    public static class AgvTaskInfo {
        private final boolean hasExecutingTask;
        private final String taskSource;
        private final String taskCode;

        public AgvTaskInfo(boolean hasExecutingTask, String taskSource, String taskCode) {
            this.hasExecutingTask = hasExecutingTask;
            this.taskSource = taskSource;
            this.taskCode = taskCode;
        }

        public boolean hasExecutingTask() {
            return hasExecutingTask;
        }

        public String getTaskSource() {
            return taskSource;
        }

        public String getTaskCode() {
            return taskCode;
        }

        public static AgvTaskInfo noTask() {
            return new AgvTaskInfo(false, null, null);
        }
    }

    /**
     * 线程安全的双向映射类
     * 支持 SocketAddress <-> Integer 的双向查找
     */
    public static class ThreadSafeBiMap<K, V> {
        private final ConcurrentHashMap<K, V> forwardMap = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<V, K> reverseMap = new ConcurrentHashMap<>();
        private final ReadWriteLock lock = new ReentrantReadWriteLock();

        /**
         * 添加映射关系
         * @param key 键
         * @param value 值
         * @return 之前关联的值，如果没有则返回null
         */
        public V put(K key, V value) {
            lock.writeLock().lock();
            try {
                // 移除旧的映射关系
                V oldValue = forwardMap.get(key);
                if (oldValue != null) {
                    reverseMap.remove(oldValue);
                }
                K oldKey = reverseMap.get(value);
                if (oldKey != null) {
                    forwardMap.remove(oldKey);
                }

                // 添加新的映射关系
                forwardMap.put(key, value);
                reverseMap.put(value, key);
                return oldValue;
            } finally {
                lock.writeLock().unlock();
            }
        }

        /**
         * 根据键获取值
         * @param key 键
         * @return 值，如果不存在则返回null
         */
        public V get(K key) {
            lock.readLock().lock();
            try {
                return forwardMap.get(key);
            } finally {
                lock.readLock().unlock();
            }
        }

        /**
         * 根据值获取键（反向查找）
         * @param value 值
         * @return 键，如果不存在则返回null
         */
        public K getByValue(V value) {
            lock.readLock().lock();
            try {
                return reverseMap.get(value);
            } finally {
                lock.readLock().unlock();
            }
        }

        /**
         * 根据键移除映射关系
         * @param key 键
         * @return 被移除的值，如果不存在则返回null
         */
        public V remove(K key) {
            lock.writeLock().lock();
            try {
                V value = forwardMap.remove(key);
                if (value != null) {
                    reverseMap.remove(value);
                }
                return value;
            } finally {
                lock.writeLock().unlock();
            }
        }

        /**
         * 根据值移除映射关系（反向移除）
         * @param value 值
         * @return 被移除的键，如果不存在则返回null
         */
        public K removeByValue(V value) {
            lock.writeLock().lock();
            try {
                K key = reverseMap.remove(value);
                if (key != null) {
                    forwardMap.remove(key);
                }
                return key;
            } finally {
                lock.writeLock().unlock();
            }
        }

        /**
         * 检查是否包含指定的键
         * @param key 键
         * @return 如果包含则返回true
         */
        public boolean containsKey(K key) {
            lock.readLock().lock();
            try {
                return forwardMap.containsKey(key);
            } finally {
                lock.readLock().unlock();
            }
        }

        /**
         * 检查是否包含指定的值
         * @param value 值
         * @return 如果包含则返回true
         */
        public boolean containsValue(V value) {
            lock.readLock().lock();
            try {
                return reverseMap.containsKey(value);
            } finally {
                lock.readLock().unlock();
            }
        }

        /**
         * 获取映射的大小
         * @return 映射的大小
         */
        public int size() {
            lock.readLock().lock();
            try {
                return forwardMap.size();
            } finally {
                lock.readLock().unlock();
            }
        }

        /**
         * 检查映射是否为空
         * @return 如果为空则返回true
         */
        public boolean isEmpty() {
            lock.readLock().lock();
            try {
                return forwardMap.isEmpty();
            } finally {
                lock.readLock().unlock();
            }
        }

        /**
         * 清空所有映射关系
         */
        public void clear() {
            lock.writeLock().lock();
            try {
                forwardMap.clear();
                reverseMap.clear();
            } finally {
                lock.writeLock().unlock();
            }
        }

        /**
         * 获取正向映射的副本（只读）
         * @return 正向映射的副本
         */
        public Map<K, V> getForwardMap() {
            lock.readLock().lock();
            try {
                return new ConcurrentHashMap<>(forwardMap);
            } finally {
                lock.readLock().unlock();
            }
        }

        /**
         * 获取反向映射的副本（只读）
         * @return 反向映射的副本
         */
        public Map<V, K> getReverseMap() {
            lock.readLock().lock();
            try {
                return new ConcurrentHashMap<>(reverseMap);
            } finally {
                lock.readLock().unlock();
            }
        }
    }

    // SocketAddress -> deviceId 的映射（一对一）
    public static final ConcurrentHashMap<SocketAddress, Integer> addressToDeviceIdMap = new ConcurrentHashMap<>();

    // deviceId -> Set<SocketAddress> 的映射（一对多，支持deviceId重复）
    public static final ConcurrentHashMap<Integer, Set<SocketAddress>> deviceIdToAddressesMap = new ConcurrentHashMap<>();

    // 添加一个缓存，用于记录设备ID的连接状态
    public static final ConcurrentHashMap<Integer, DeviceConnectionStatusEnum> deviceStatusCache = new ConcurrentHashMap<>();

    // AGV代码到设备ID的映射（用于S5F1报警消息路由）
    public static final ConcurrentHashMap<String, Integer> agvCodeToDeviceIdMap = new ConcurrentHashMap<>();

    // bayName到IP地址的映射（用于通过bayName查找设备IP）
    public static final ConcurrentHashMap<String, String> bayNameToIpMap = new ConcurrentHashMap<>();

    // IP地址到bayName的映射（反向映射）
    public static final ConcurrentHashMap<String, String> ipToBayNameMap = new ConcurrentHashMap<>();

    /**
     * 根据远程地址获取设备ID
     *
     * @param remoteAddress 远程地址
     * @return 设备ID，如果找不到则返回null
     */
    public static final Integer getDeviceIdByAddress(SocketAddress remoteAddress) {
        return addressToDeviceIdMap.get(remoteAddress);
    }

    /**
     * 根据设备ID获取所有远程地址（支持一个deviceId对应多个地址）
     *
     * @param deviceId 设备ID
     * @return 远程地址集合，如果找不到则返回空集合
     */
    public static final Set<SocketAddress> getAddressesByDeviceId(Integer deviceId) {
        Set<SocketAddress> addresses = deviceIdToAddressesMap.get(deviceId);
        return addresses != null ? new HashSet<>(addresses) : new HashSet<>();
    }

    /**
     * 根据设备ID获取第一个远程地址（兼容旧接口）
     *
     * @param deviceId 设备ID
     * @return 第一个远程地址，如果找不到则返回null
     */
    public static final SocketAddress getAddressByDeviceId(Integer deviceId) {
        Set<SocketAddress> addresses = deviceIdToAddressesMap.get(deviceId);
        return addresses != null && !addresses.isEmpty() ? addresses.iterator().next() : null;
    }

    /**
     * 根据IP地址获取第一个远程地址（优化单连接场景）
     * 优先从地址映射中查找，如果没找到则从实时连接中查找
     *
     * @param ipAddress IP地址
     * @return 第一个远程地址，如果找不到则返回null
     */
    public static final SocketAddress getAddressByIp(String ipAddress) {
        // 首先从地址映射中查找（现有逻辑）
        for (Map.Entry<SocketAddress, Integer> entry : addressToDeviceIdMap.entrySet()) {
            SocketAddress address = entry.getKey();
            String addressIp = extractIpFromSocketAddress(address);
            if (ipAddress.equals(addressIp)) {
                return address;
            }
        }

        // 如果从地址映射中没找到，再从实时连接中查找
        // 这样可以处理设备刚连接但还没建立映射的情况
        try {
            Secs1OnTcpIpReceiverCommunicator communicator = SpringUtils.getBean(Secs1OnTcpIpReceiverCommunicator.class);
            if (communicator != null) {
                Collection<ClientConnection> connections = communicator.getConnections();
                for (ClientConnection connection : connections) {
                    SocketAddress address = connection.getRemoteAddress();
                    String addressIp = extractIpFromSocketAddress(address);
                    if (ipAddress.equals(addressIp)) {
                        log.debug("从实时连接中找到IP[{}]对应的地址: {}", ipAddress, address);
                        return address;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("从实时连接中查找IP[{}]对应的地址时发生异常: {}", ipAddress, e.getMessage());
        }

        return null;
    }

    /**
     * 添加地址到设备ID的映射关系
     *
     * @param remoteAddress 远程地址
     * @param deviceId 设备ID
     * @return 之前关联的设备ID，如果没有则返回null
     */
    public static final Integer putAddressDeviceMapping(SocketAddress remoteAddress, Integer deviceId) {
        // 获取旧的deviceId
        Integer oldDeviceId = addressToDeviceIdMap.get(remoteAddress);

        // 如果有旧的映射，先从旧的deviceId的地址集合中移除
        if (oldDeviceId != null) {
            Set<SocketAddress> oldAddresses = deviceIdToAddressesMap.get(oldDeviceId);
            if (oldAddresses != null) {
                oldAddresses.remove(remoteAddress);
                if (oldAddresses.isEmpty()) {
                    deviceIdToAddressesMap.remove(oldDeviceId);
                }
            }
        }

        // 添加新的映射
        addressToDeviceIdMap.put(remoteAddress, deviceId);
        deviceIdToAddressesMap.computeIfAbsent(deviceId, k -> ConcurrentHashMap.newKeySet()).add(remoteAddress);

        return oldDeviceId;
    }

    /**
     * 根据远程地址移除映射关系
     *
     * @param remoteAddress 远程地址
     * @return 被移除的设备ID，如果不存在则返回null
     */
    public static final Integer removeAddressDeviceMapping(SocketAddress remoteAddress) {
        Integer deviceId = addressToDeviceIdMap.remove(remoteAddress);
        if (deviceId != null) {
            Set<SocketAddress> addresses = deviceIdToAddressesMap.get(deviceId);
            if (addresses != null) {
                addresses.remove(remoteAddress);
                if (addresses.isEmpty()) {
                    deviceIdToAddressesMap.remove(deviceId);
                }
            }
        }
        return deviceId;
    }

    /**
     * 根据设备ID移除所有映射关系
     *
     * @param deviceId 设备ID
     * @return 被移除的远程地址集合，如果不存在则返回空集合
     */
    public static final Set<SocketAddress> removeDeviceAddressMapping(Integer deviceId) {
        Set<SocketAddress> addresses = deviceIdToAddressesMap.remove(deviceId);
        if (addresses != null) {
            // 从反向映射中移除所有相关地址
            for (SocketAddress address : addresses) {
                addressToDeviceIdMap.remove(address);
            }
            return new HashSet<>(addresses);
        }
        return new HashSet<>();
    }

    /**
     * 检查是否包含指定的远程地址
     *
     * @param remoteAddress 远程地址
     * @return 如果包含则返回true
     */
    public static final boolean containsAddress(SocketAddress remoteAddress) {
        return addressToDeviceIdMap.containsKey(remoteAddress);
    }

    /**
     * 检查是否包含指定的设备ID
     *
     * @param deviceId 设备ID
     * @return 如果包含则返回true
     */
    public static final boolean containsDeviceId(Integer deviceId) {
        return deviceIdToAddressesMap.containsKey(deviceId);
    }

    /**
     * 获取地址到设备ID的映射（只读副本）
     *
     * @return 地址到设备ID的映射副本
     */
    public static final Map<SocketAddress, Integer> getAddressToDeviceIdMap() {
        return new ConcurrentHashMap<>(addressToDeviceIdMap);
    }

    /**
     * 获取设备ID到地址集合的映射（只读副本）
     *
     * @return 设备ID到地址集合的映射副本
     */
    public static final Map<Integer, Set<SocketAddress>> getDeviceIdToAddressesMap() {
        Map<Integer, Set<SocketAddress>> result = new ConcurrentHashMap<>();
        for (Map.Entry<Integer, Set<SocketAddress>> entry : deviceIdToAddressesMap.entrySet()) {
            result.put(entry.getKey(), new HashSet<>(entry.getValue()));
        }
        return result;
    }

    /**
     * 获取设备ID到第一个地址的映射（兼容旧接口）
     *
     * @return 设备ID到第一个地址的映射副本
     */
    public static final Map<Integer, SocketAddress> getDeviceIdToAddressMap() {
        Map<Integer, SocketAddress> result = new ConcurrentHashMap<>();
        for (Map.Entry<Integer, Set<SocketAddress>> entry : deviceIdToAddressesMap.entrySet()) {
            Set<SocketAddress> addresses = entry.getValue();
            if (!addresses.isEmpty()) {
                result.put(entry.getKey(), addresses.iterator().next());
            }
        }
        return result;
    }

    /**
     * 向指定设备ID的所有客户端发送消息
     *
     * @param deviceId 设备ID
     * @param communicator SECS通信器
     * @param stream 流号
     * @param function 功能号
     * @param wbit 是否需要回复
     * @param secs2 消息数据
     * @return 成功发送的客户端数量
     */
    public static final int sendToAllClientsOfDevice(Integer deviceId,
                                                   Secs1OnTcpIpReceiverCommunicator communicator,
                                                   int stream, int function, boolean wbit, Secs2 secs2) {
        Set<SocketAddress> addresses = getAddressesByDeviceId(deviceId);
        int successCount = 0;

        for (SocketAddress address : addresses) {
            try {
                communicator.send(address, stream, function, wbit, secs2);
                successCount++;
                log.debug("成功向设备[{}]的客户端[{}]发送S{}F{}消息", deviceId, address, stream, function);
            } catch (Exception e) {
                log.error("向设备[{}]的客户端[{}]发送S{}F{}消息失败: {}", deviceId, address, stream, function, e.getMessage());
            }
        }

        return successCount;
    }

    /**
     * 获取指定设备ID的连接数量
     *
     * @param deviceId 设备ID
     * @return 连接数量
     */
    public static final int getConnectionCountByDeviceId(Integer deviceId) {
        Set<SocketAddress> addresses = deviceIdToAddressesMap.get(deviceId);
        return addresses != null ? addresses.size() : 0;
    }

    /**
     * 获取所有设备ID及其连接数量的统计信息
     *
     * @return 设备ID到连接数量的映射
     */
    public static final Map<Integer, Integer> getDeviceConnectionStats() {
        Map<Integer, Integer> stats = new ConcurrentHashMap<>();
        for (Map.Entry<Integer, Set<SocketAddress>> entry : deviceIdToAddressesMap.entrySet()) {
            stats.put(entry.getKey(), entry.getValue().size());
        }
        return stats;
    }

    /**
     * 获取地址映射的大小
     *
     * @return 映射大小
     */
    public static final int getMappingSize() {
        return addressToDeviceIdMap.size();
    }

    /**
     * 清理指定设备ID的所有映射关系
     *
     * @param deviceId 设备ID
     */
    public static final void clearDeviceMappings(Integer deviceId) {
        Set<SocketAddress> addresses = removeDeviceAddressMapping(deviceId);
        log.info("清理设备[{}]的映射关系，移除了{}个连接", deviceId, addresses.size());
    }

    /**
     * 设置AGV代码到设备ID的映射
     *
     * @param agvCode AGV代码
     * @param deviceId 设备ID
     */
    public static final void setAgvCodeToDeviceIdMapping(String agvCode, Integer deviceId) {
        agvCodeToDeviceIdMap.put(agvCode, deviceId);
        log.debug("设置AGV[{}]到设备ID[{}]的映射", agvCode, deviceId);
    }

    /**
     * 根据AGV代码获取设备ID
     *
     * @param agvCode AGV代码
     * @return 设备ID，如果找不到则返回agvCode本身（假设agvCode就是deviceId）
     */
    public static final Integer getDeviceIdByAgvCode(String agvCode) {
        Integer deviceId = agvCodeToDeviceIdMap.get(agvCode);
        if (deviceId != null) {
            return deviceId;
        }

        // 如果没有显式映射，假设agvCode就是deviceId
        log.debug("未找到AGV[{}]的设备ID映射，使用AGV代码作为设备ID", agvCode);
        return null;
    }

    /**
     * 根据AGV代码获取对应的客户端连接地址
     * 优先使用新的bayName机制，如果失败则回退到旧的deviceId机制
     *
     * @param agvName AGV代码
     * @return 客户端连接地址集合
     */
    public static final Set<SocketAddress> getClientAddressesByAgvName(String agvName) {
        String bayNameByAgvName = getBayNameByAgvName(agvName);
        // 优先尝试新的bayName机制
        Set<SocketAddress> addresses = getClientAddressesByBayName(bayNameByAgvName);
        if (!addresses.isEmpty()) {
            return addresses;
        }

        // 如果新机制失败，回退到旧的deviceId机制（向后兼容）
        Integer deviceId = getDeviceIdByAgvCode(agvName);
        if (deviceId != null) {
            return getAddressesByDeviceId(deviceId);
        }

        return new HashSet<>();
    }

    /**
     * 移除AGV代码到设备ID的映射
     *
     * @param agvCode AGV代码
     * @return 被移除的设备ID
     */
    public static final Integer removeAgvCodeToDeviceIdMapping(Integer agvCode) {
        Integer deviceId = agvCodeToDeviceIdMap.remove(agvCode);
        log.debug("移除AGV[{}]到设备ID[{}]的映射", agvCode, deviceId);
        return deviceId;
    }

    /**
     * 获取所有AGV代码到设备ID的映射
     *
     * @return AGV代码到设备ID的映射副本
     */
    public static final Map<String, Integer> getAgvCodeToDeviceIdMap() {
        return new ConcurrentHashMap<>(agvCodeToDeviceIdMap);
    }



    /**
     * 设置bayName到IP地址的映射
     *
     * @param bayName Bay名称
     * @param ipAddress IP地址
     */
    public static final void setBayNameToIpMapping(String bayName, String ipAddress) {
        if (bayName != null && ipAddress != null) {
            bayNameToIpMap.put(bayName, ipAddress);
            ipToBayNameMap.put(ipAddress, bayName);
            log.debug("设置Bay[{}]到IP[{}]的映射", bayName, ipAddress);
        }
    }

    /**
     * 根据bayName获取IP地址
     *
     * @param bayName Bay名称
     * @return IP地址，如果找不到则返回null
     */
    public static final String getIpByBayName(String bayName) {
        return bayNameToIpMap.get(bayName);
    }

    /**
     * 根据IP地址获取bayName
     *
     * @param ipAddress IP地址
     * @return Bay名称，如果找不到则返回null
     */
    public static final String getBayNameByIp(String ipAddress) {
        return ipToBayNameMap.get(ipAddress);
    }

    /**
     * 根据AGV代码解析出bayName和robotNumber
     * AGV代码格式：bayName_robotNumber
     *
     * @param agvName AGV代码
     * @return 包含bayName和robotNumber的数组，[0]为bayName，[1]为robotNumber
     */
    public static final String[] parseAgvName(String agvName) {
        if (agvName == null || !agvName.contains("_")) {
            return null;
        }
        String[] parts = agvName.split("_", 2);
        if (parts.length == 2) {
            return parts; // [bayName, robotNumber]
        }
        return null;
    }

    /**
     * 根据agvName获取对应的bayName
     *
     * @param agvName agvName
     * @return Bay名称，如果解析失败则返回null
     */
    public static final String getBayNameByAgvName(String agvName) {
        String[] parts = parseAgvName(agvName);
        return parts != null ? parts[0] : null;
    }

    /**
     * 根据agvName获取对应的robotNumber
     *
     * @param agvName agvName
     * @return 机器人编号，如果解析失败则返回null
     */
    public static final String getRobotNumberByAgvName(String agvName) {
        String[] parts = parseAgvName(agvName);
        return parts != null ? parts[1] : null;
    }

    /**
     * 根据AGV代码获取对应的ClientConnection地址
     * 通过agvCode解析出bayName，再通过bayName找到对应的IP地址和ClientConnection
     *
     * @param bayName AGV代码
     * @return 客户端连接地址集合
     */
    public static final Set<SocketAddress> getClientAddressesByBayName(String bayName) {

        if (bayName != null) {
            String ipAddress = getIpByBayName(bayName);
            if (ipAddress != null) {
                // 根据IP地址找到对应的SocketAddress
                return getAddressesByIp(ipAddress);
            }
        }
        return new HashSet<>();
    }



    /**
     * 获取与指定设备相关的AGV列表
     * 只返回与设备bayName匹配的AGV
     * 统一的AGV过滤逻辑，供SecsManualController和S1F17CommandHandler等使用
     *
     * @param sourceAddress 设备地址
     * @param communicationDeviceService 通信设备服务
     * @param fleetProxyService 车队代理服务
     * @return 相关的AGV列表
     */
    public static final List<com.youibot.tms.biz.api.dto.AgvDTO> getRelevantAgvsForDevice(
            SocketAddress sourceAddress,
            CommunicationDeviceService communicationDeviceService,
          FleetProxyService fleetProxyService) {

        try {
            // 从SocketAddress提取IP地址
            String deviceIp = extractIpFromSocketAddress(sourceAddress);
            if (deviceIp == null) {
                log.warn("无法从地址[{}]提取IP，返回所有AGV", sourceAddress);
                return fleetProxyService.listAllAgvs();
            }

            // 根据IP地址查找设备
            com.youibot.tms.biz.entity.CommunicationDevice device = communicationDeviceService.getByIpAddress(deviceIp);
            if (device == null || device.getBayName() == null) {
                log.warn("设备[IP:{}]不存在或未设置bayName，返回所有AGV", deviceIp);
                return fleetProxyService.listAllAgvs();
            }

            String bayName = device.getBayName();
            log.debug("设备[IP:{}]的bayName为[{}]，过滤相关AGV", deviceIp, bayName);

            // 获取所有AGV并过滤出与bayName相关的
            List<com.youibot.tms.biz.api.dto.AgvDTO> allAgvs = fleetProxyService.listAllAgvs();
            List<com.youibot.tms.biz.api.dto.AgvDTO> relevantAgvs = new ArrayList<>();

            for (com.youibot.tms.biz.api.dto.AgvDTO agv : allAgvs) {
                String agvCode = agv.getAgvCode();
                String vehicleName = agv.getName();

                // 使用BayNameUtils封装方法检查AGV是否属于指定bayName
                if (com.youibot.tms.biz.utils.BayNameUtils.isAgvBelongsToBay(vehicleName, bayName)) {
                    relevantAgvs.add(agv);
                    log.debug("AGV[{}]属于设备[{}]", agvCode, bayName);
                }
                // 兼容旧格式：使用BayNameUtils检查是否为旧格式
                else if (com.youibot.tms.biz.utils.BayNameUtils.isLegacyAgvCode(vehicleName)) {
                    // 对于纯数字格式的AGV代码，暂时包含所有，后续可以根据具体需求调整
                    relevantAgvs.add(agv);
                    log.debug("AGV[{}]使用旧格式，包含在设备[{}]的报告中", agvCode, bayName);
                }
            }

            log.info("设备[IP:{}, bayName:{}]相关的AGV数量: {}/{}", deviceIp, bayName, relevantAgvs.size(), allAgvs.size());
            return relevantAgvs;

        } catch (Exception e) {
            log.error("获取设备相关AGV时发生异常: {}", e.getMessage(), e);
            // 发生异常时返回所有AGV，确保系统正常运行
            return fleetProxyService.listAllAgvs();
        }
    }

    /**
     * 根据IP地址获取对应的SocketAddress集合
     * 优先从地址映射中查找，如果没找到则从实时连接中查找
     *
     * @param ipAddress IP地址
     * @return SocketAddress集合
     */
    public static final Set<SocketAddress> getAddressesByIp(String ipAddress) {
        Set<SocketAddress> result = new HashSet<>();

        // 首先从地址映射中查找（现有逻辑）
        for (Map.Entry<SocketAddress, Integer> entry : addressToDeviceIdMap.entrySet()) {
            SocketAddress address = entry.getKey();
            String addressIp = extractIpFromSocketAddress(address);
            if (ipAddress.equals(addressIp)) {
                result.add(address);
            }
        }

        // 如果从地址映射中没找到，再从实时连接中查找
        // 这样可以处理设备刚连接但还没建立映射的情况
        if (result.isEmpty()) {
            try {
                Secs1OnTcpIpReceiverCommunicator communicator = SpringUtils.getBean(Secs1OnTcpIpReceiverCommunicator.class);
                if (communicator != null) {
                    Collection<ClientConnection> connections = communicator.getConnections();
                    for (ClientConnection connection : connections) {
                        SocketAddress address = connection.getRemoteAddress();
                        String addressIp = extractIpFromSocketAddress(address);
                        if (ipAddress.equals(addressIp)) {
                            result.add(address);
                            log.debug("从实时连接中找到IP[{}]对应的地址: {}", ipAddress, address);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("从实时连接中查找IP[{}]对应的地址时发生异常: {}", ipAddress, e.getMessage());
            }
        }

        return result;
    }

    /**
     * 从SocketAddress中提取IP地址
     *
     * @param address SocketAddress
     * @return IP地址字符串
     */
    public static final String extractIpFromSocketAddress(SocketAddress address) {
        if (address == null) {
            return null;
        }

        String addressStr = address.toString();
        // 格式通常是 /IP:PORT，需要提取IP部分
        if (addressStr.startsWith("/")) {
            addressStr = addressStr.substring(1);
        }

        int colonIndex = addressStr.lastIndexOf(':');
        if (colonIndex > 0) {
            return addressStr.substring(0, colonIndex);
        }

        return addressStr;
    }

    /**
     * 移除bayName到IP地址的映射
     *
     * @param bayName Bay名称
     * @return 被移除的IP地址
     */
    public static final String removeBayNameToIpMapping(String bayName) {
        String ipAddress = bayNameToIpMap.remove(bayName);
        if (ipAddress != null) {
            ipToBayNameMap.remove(ipAddress);
            log.debug("移除Bay[{}]到IP[{}]的映射", bayName, ipAddress);
        }
        return ipAddress;
    }

    /**
     * 检查AGV是否有对应的客户端连接
     *
     * @param agvCode AGV代码
     * @return true if AGV has client connections
     */
    public static final boolean hasClientConnectionsForAgv(String agvCode) {
        Set<SocketAddress> addresses = getClientAddressesByAgvName(agvCode);
        return !addresses.isEmpty();
    }



    /**
     * 清空所有映射关系
     */
    public static final void clearAllMappings() {
        addressToDeviceIdMap.clear();
        deviceIdToAddressesMap.clear();
    }

    /**
     * 获取地址映射的调试信息
     */
    public static final String getAddressMappingDebugInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("地址映射状态:\n");
        sb.append("- 映射数量: ").append(addressToDeviceIdMap.size()).append("\n");
        sb.append("- 设备映射数量: ").append(deviceIdToAddressesMap.size()).append("\n");

        if (!addressToDeviceIdMap.isEmpty()) {
            sb.append("- 地址到设备ID映射:\n");
            for (Map.Entry<SocketAddress, Integer> entry : addressToDeviceIdMap.entrySet()) {
                sb.append("  ").append(entry.getKey()).append(" -> 设备[").append(entry.getValue()).append("]\n");
            }
        }

        if (!deviceIdToAddressesMap.isEmpty()) {
            sb.append("- 设备ID到地址映射:\n");
            for (Map.Entry<Integer, Set<SocketAddress>> entry : deviceIdToAddressesMap.entrySet()) {
                sb.append("  设备[").append(entry.getKey()).append("] -> ").append(entry.getValue()).append("\n");
            }
        }

        return sb.toString();
    }
    /**
     * 系统状态报告 (S66F1)
     *
     * 根据AGVC-HOST通信仕様書实现系统状态报告消息
     *
     * 系统状态位字段 (SYSSUT):
     * - BIT8: 系统模式 (0=离线, 1=在线)
     * - BIT7: 系统错误 (0=无错误, 1=系统错误发生中)
     * - BIT6: 搬送指示队列 (0=无错误, 1=搬送指示队列已满)
     * - BIT5-BIT1: 未定义
     *
     * AGV状态值 (AGVnSUT):
     * - 0: 未注册
     * - 1: 完全待机，无在荷
     * - 2: 完全待机，有在荷
     * - 4: 搬送指令执行中
     * - 5: 充电中
     * - 6: 手动
     * - 100: 异常
     * - 200: 离线
     *
     * @param online 系统在线状态
     * @param sysError 系统错误状态
     * @param transportError 搬送指示队列错误状态  堆积过多处理不过来
     * @param agvDTOList AGV列表
     * @return S66F1消息数据
     */
    public static Secs2 s66f1(boolean online, boolean sysError, boolean transportError, List<AgvDTO> agvDTOList) throws InterruptedException, SecsException {
        log.info("【S66F1构建】开始构建S66F1消息 - online={}, sysError={}, transportError={}, AGV数量={}",
                online, sysError, transportError, agvDTOList != null ? agvDTOList.size() : 0);

        if (agvDTOList != null) {
            for (AgvDTO agv : agvDTOList) {
                log.debug("【S66F1构建】输入AGV数据 - code={}, name={}, connectStatus={}, status={}",
                         agv.getAgvCode(), agv.getName(), agv.getConnectStatus(), agv.getStatus());
            }
        }

        // 系统状态
        int sysStatus = BooleanUtil.toInt(online) << 7 | BooleanUtil.toInt(sysError) << 6 | BooleanUtil.toInt(transportError) << 5;
        Secs2Binary sysStatusSecs = new Secs2Binary((byte) sysStatus);

        // agv数量, 固定10
        Secs2Binary agvNumSecs = new Secs2Binary((byte) 10);

        // agv状态
        List<Secs2Binary> agvStatusList = new ArrayList<>();
        // 使用robotNumber作为映射键，同一bayName内robotNumber不会重复
        Map<Integer, AgvDTO> agvDTOMap = agvDTOList.stream()
                .filter(agv -> BayNameUtils.isValidAgvCode(agv.getName())) // 只处理有效格式的AGV
                .collect(Collectors.toMap(
                        agv -> BayNameUtils.extractRobotNumber(agv.getName()),
                        Function.identity()
                ));

        // 批量查询处于EXECUTING和SUSPENDED状态的任务，用于验证AGV是否真的在执行任务
        Map<String, AgvTaskInfo> agvExecutingTaskMap = getAgvExecutingTaskMap(agvDTOMap.values());

        for (int i = 1; i <= 10; i++) {
            AgvDTO agvDTO = agvDTOMap.get(i);
            int agvStatus = getAgvStatus(agvDTO, agvExecutingTaskMap);
            if (agvDTO != null) {
                log.info("agv[{}]状态[{}] (name: {}, agvCode: {})", i, agvStatus, agvDTO.getName(), agvDTO.getAgvCode());
            } else {
                log.debug("agv[{}]状态[{}] (未注册)", i, agvStatus);
            }
            agvStatusList.add(new Secs2Binary((byte) agvStatus));
        }
        Secs2 tx = new Secs2List(sysStatusSecs, agvNumSecs, new Secs2List(agvStatusList));
        return tx;
    }

    /**
     * 批量查询AGV是否有处于EXECUTING或SUSPENDED状态的任务
     * 用于验证AGV状态为RUN时是否真的在执行任务
     *
     * @param agvDTOs AGV列表
     * @return Map<agvCode, AgvTaskInfo> AGV编码到任务信息的映射
     */
    /**
     * 获取AGV执行中任务映射（包可见，供AgvStatusProcessingServiceImpl复用）
     */
    public static Map<String, AgvTaskInfo> getAgvExecutingTaskMap(Collection<AgvDTO> agvDTOs) {
        Map<String, AgvTaskInfo> result = new HashMap<>();

        if (agvDTOs == null || agvDTOs.isEmpty()) {
            return result;
        }

        try {
            // 获取TaskService
            TaskService taskService = SpringUtils.getBean(TaskService.class);

            // 提取所有agvCode
            Set<String> agvCodes = agvDTOs.stream()
                    .map(AgvDTO::getAgvCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (agvCodes.isEmpty()) {
                return result;
            }

            log.debug("【S66F1任务查询】开始查询AGV执行中任务 - agvCodes: {}", agvCodes);

            // 批量查询处于EXECUTING和SUSPENDED状态的任务
            List<Task> executingTasks = taskService.list(
                Wrappers.<Task>lambdaQuery()
                    .in(Task::getStatus, Arrays.asList(TaskStatus.EXECUTING, TaskStatus.SUSPENDED))
                    .in(Task::getAgvCode, agvCodes)
                    .isNotNull(Task::getAgvCode)
                    .ne(Task::getAgvCode, "")
                    .eq(Task::getDelFlag, false)
            );

            // 初始化所有AGV为无执行中任务
            for (String agvCode : agvCodes) {
                result.put(agvCode, AgvTaskInfo.noTask());
            }

            // 标记有执行中任务的AGV，并记录任务信息
            for (Task task : executingTasks) {
                String agvCode = task.getAgvCode();
                if (agvCode != null) {
                    String taskCode = task.getCode();
                    // 任务来源将从AgvDTO.taskInfo.source获取，这里不需要存储
                    result.put(agvCode, new AgvTaskInfo(true, null, taskCode));
                    log.debug("【S66F1任务查询】AGV[{}]有执行中任务 - 任务编号: {}, 状态: {}",
                            agvCode, taskCode, task.getStatus());
                }
            }

            long executingTaskCount = result.values().stream().mapToLong(info -> info.hasExecutingTask() ? 1 : 0).sum();
            log.info("【S66F1任务查询】任务查询完成 - 查询AGV数量: {}, 有执行中任务的AGV数量: {}",
                    agvCodes.size(), executingTaskCount);

        } catch (Exception e) {
            log.error("【S66F1任务查询】查询AGV执行中任务时发生异常", e);
            // 异常情况下，为了安全起见，假设所有AGV都没有执行中任务
            for (AgvDTO agvDTO : agvDTOs) {
                if (agvDTO.getAgvCode() != null) {
                    result.put(agvDTO.getAgvCode(), AgvTaskInfo.noTask());
                }
            }
        }

        return result;
    }

    /**
     * 获取AGV的S66F1状态映射结果（包可见，供AgvStatusProcessingServiceImpl复用）
     *
     * @param agvDTO AGV数据传输对象
     * @return S66F1状态值
     */
    static int getAgvStatus(AgvDTO agvDTO) {
        return getAgvStatus(agvDTO, new HashMap<>());
    }

    /**
     * 获取AGV的S66F1状态映射结果（包可见，供AgvStatusProcessingServiceImpl复用）
     *
     * @param agvDTO AGV数据传输对象
     * @param agvExecutingTaskMap AGV执行中任务映射
     * @return S66F1状态值
     */
    public static int getAgvStatus(AgvDTO agvDTO, Map<String, AgvTaskInfo> agvExecutingTaskMap) {
        if (agvDTO == null) {
            log.warn("【S66F1状态映射】AGV数据为null，返回未注册状态0");
            return 0;
        }

        String agvCode = agvDTO.getAgvCode();
        String agvName = agvDTO.getName();
        log.debug("getAgvStatus_data:{}", JSON.toJSONString( agvDTO ));
        log.debug("【S66F1状态映射】开始处理AGV[{}({})] - connectStatus={}, status={}, abnormalNotice={}, data={}",
                 agvName, agvCode, agvDTO.getConnectStatus(), agvDTO.getStatus(), agvDTO.getAbnormalNotice(), JSON.toJSONString( agvDTO ));

        // 优先检查连接状态（直接检查原始字段，避免状态计算错误）
        String connectStatus = agvDTO.getConnectStatus();
        if ("disconnect".equalsIgnoreCase(connectStatus)) {
            log.info("【S66F1状态映射】AGV[{}({})]连接状态为Disconnect，返回离线状态200", agvName, agvCode);
            return 200; // 离线
        }

        // 优先检查abnormalNotice字段判断异常状态
        Boolean abnormalNotice = agvDTO.getAbnormalNotice();
        if (abnormalNotice != null && abnormalNotice) {
            log.info("【S66F1状态映射】AGV[{}({})]abnormalNotice=true，返回异常状态100", agvName, agvCode);
            return 100; // 异常
        }

        // 检查是否为手动模式
        if (isManualMode(agvDTO)) {
            // 手动模式
            return 6;
        }

        VehicleStatus status = agvDTO.getStatus();
        int resultStatus;

        switch (status) {
            case RUN:
                // 搬送指令执行中 - 需要验证是否真的有EXECUTING或SUSPENDED状态的任务
                AgvTaskInfo taskInfo = agvExecutingTaskMap.get(agvCode);
                if (taskInfo != null && taskInfo.hasExecutingTask()) {
                    String taskCode = taskInfo.getTaskCode();

                    // 从AgvDTO的taskInfo中获取任务来源
                    String taskSource = null;
                    if (agvDTO.getTaskInfo() != null) {
                        taskSource = agvDTO.getTaskInfo().getSource();
                        log.info("【S66F1状态映射调试】AGV[{}({})] - taskInfo存在，taskNo: [{}], source: [{}]",
                                agvName, agvCode, agvDTO.getTaskInfo().getTaskNo(), taskSource);
                    } else {
                        log.info("【S66F1状态映射调试】AGV[{}({})] - taskInfo为null", agvName, agvCode);
                    }

                    // 检查任务来源，如果是系统策略任务（如Park），按IDLE处理
                    log.info("【S66F1状态映射调试】AGV[{}({})] - taskSource: [{}], taskInfo: [{}], isSystemPolicyTask: [{}]",
                            agvName, agvCode, taskSource,
                            agvDTO.getTaskInfo() != null ? "存在" : "null",
                            taskSource != null ? TaskSourceEnum.isSystemPolicyTask(taskSource) : "taskSource为null");

                    if (taskSource != null && TaskSourceEnum.isSystemPolicyTask(taskSource)) {
                        log.info("【S66F1状态映射】AGV[{}({})]状态为RUN但任务来源为系统策略[{}]，按IDLE状态处理 - 任务: {}",
                                agvName, agvCode, taskSource, taskCode);
                        if (hasLoad(agvDTO)) {
                            resultStatus = 2; // 完全待机，有在荷
                        } else {
                            resultStatus = 1; // 完全待机，无在荷
                        }
                    } else {
                        // 正常业务任务或无法判断来源的任务，返回搬送指令执行中状态
                        log.info("【S66F1状态映射】AGV[{}({})]状态为RUN且有执行中任务，返回搬送指令执行中状态4 - 任务: {}, 来源: {}",
                                agvName, agvCode, taskCode, taskSource != null ? taskSource : "未知");
                        resultStatus = 4;
                    }
                } else {
                    // AGV状态为RUN但没有EXECUTING或SUSPENDED状态的任务，按IDLE处理
                    log.warn("【S66F1状态映射】AGV[{}({})]状态为RUN但没有执行中任务，按IDLE状态处理", agvName, agvCode);
                    if (hasLoad(agvDTO)) {
                        resultStatus = 2; // 完全待机，有在荷
                    } else {
                        resultStatus = 1; // 完全待机，无在荷
                    }
                }
                break;
            case IDLE:
                // IDLE状态也需要检查是否有执行中任务
                AgvTaskInfo idleTaskInfo = agvExecutingTaskMap.get(agvCode);
                if (idleTaskInfo != null && idleTaskInfo.hasExecutingTask()) {
                    String taskCode = idleTaskInfo.getTaskCode();

                    resultStatus = 4;

                } else {
                    // 没有执行中任务，完全待机 - 需要区分是否有在荷
                    if (hasLoad(agvDTO)) {
                        // 完全待机，有在荷
                        resultStatus = 2;
                    } else {
                        // 完全待机，无在荷
                        resultStatus = 1;
                    }
                }
                break;
            case CHARGE:
                // 充电中
                resultStatus = 5;
                break;
            case ERROR:
                // 异常
                resultStatus = 100;
                break;
            case OFFLINE:
                // 离线
                resultStatus = 200;
                break;
            default:
                log.error("【S66F1状态映射】AGV[{}({})]状态[{}]未定义", agvName, agvCode, status);
                throw new RuntimeException(StrUtil.format("状态[{}]未定义", status));
        }

        log.info("【S66F1状态映射】AGV[{}({})] 最终状态映射: connectStatus={}, status={} -> S66F1值={}",
                agvName, agvCode, connectStatus, status, resultStatus);

        return resultStatus;
    }

    /**
     * 检查AGV是否处于手动模式
     * 综合检查控制状态和调度模式
     *
     * @param agvDTO AGV数据传输对象
     * @return true表示手动模式，false表示自动模式
     */
    private static boolean isManualMode(AgvDTO agvDTO) {
        if (agvDTO == null) {
            return false;
        }

        // 1. 优先检查运行状态中的控制状态（最准确的判断）
        if (agvDTO.getRunningStatus() != null) {
            String controlStatus = agvDTO.getRunningStatus().getControlStatus();
            if ("Manual".equals(controlStatus)) {
                log.debug("AGV[{}]控制状态为Manual，判定为手动模式", agvDTO.getAgvName());
                return true;
            }
            if ("Repair".equals(controlStatus)) {
                log.debug("AGV[{}]控制状态为Repair，判定为手动模式", agvDTO.getAgvName());
                return true;
            }
            if ("SemiAuto".equals(controlStatus)) {
                log.debug("AGV[{}]控制状态为SemiAuto，判定为手动模式", agvDTO.getAgvName());
                return true;
            }
        }

        // 2. 检查调度模式（辅助判断）
        String scheduleMode = agvDTO.getScheduleMode();
//        if ("ManualSchedule".equals(scheduleMode)) {
//            log.debug("AGV[{}]调度模式为ManualSchedule，判定为手动模式", agvDTO.getAgvName());
//            return true;
//        }

        log.debug("AGV[{}]判定为自动模式 - 控制状态: {}, 调度模式: {}",
                 agvDTO.getAgvName(),
                 agvDTO.getRunningStatus() != null ? agvDTO.getRunningStatus().getControlStatus() : "null",
                 scheduleMode);

        return false;
    }

    /**
     * 检查AGV是否有在荷（货物）（包可见，供AgvStatusProcessingServiceImpl复用）
     *
     * @param agvDTO AGV数据传输对象
     * @return true表示有在荷，false表示无在荷
     */
    public static boolean hasLoad(AgvDTO agvDTO) {
        // 检查任务使用状态
        if (agvDTO.getUsageStatus() != null) {
            // 如果AGV被占用，可能表示有货物
            return agvDTO.getUsageStatus() == AgvUsageStatus.OCCUPIED;
        }

        // 检查是否有正在执行的任务
        if (agvDTO.getTaskCodes() != null && !agvDTO.getTaskCodes().isEmpty()) {
            // 如果有任务在执行，可能表示有货物
            return true;
        }

        List<MosStorageInfo> storageInfoList = agvDTO.getStorageInfoList();
        if(CollectionUtils.isNotEmpty( storageInfoList) ) {
            Optional<MosStorageInfo> first = storageInfoList.stream().filter(p -> Objects.nonNull(p) && Objects.equals("FULL", p.getState())).findFirst();
            return first.isPresent();
        }
        // 如果AgvDTO继承自VehicleDetailApiDTO，可以检查储位信息
        // 但由于AgvDTO没有直接暴露getStorageInfoList方法，
        // 这里使用其他可用的字段来判断

        // 默认认为无在荷
        return false;
    }

    /**
     * 报警代码枚举 - ALCD字段的计算
     * ALCD为BIN,1 (1字节二进制数据)
     * bit8: 报警状态 (1=发生, 0=解除)
     * bit7-1: 报警分类代码，通常为5 (不可恢复的错误)
     */
    public enum AlarmCode {
        // ALCD = (状态位 << 7) | 报警分类代码
        // 报警分类代码 = 5 (不可恢复的错误)
        ALARM_OCCURRED((byte) ((1 << 7) | 5)),   // 1000 0101 = 0x85 = 133
        ALARM_RELEASED((byte) ((0 << 7) | 5));   // 0000 0101 = 0x05 = 5

        private final byte value;

        AlarmCode(byte value) {
            this.value = value;
        }

        public byte getValue() {
            return value;
        }

        /**
         * 获取报警状态位 (bit8)
         * @return true=发生, false=解除
         */
        public boolean isOccurred() {
            return (value & 0x80) != 0; // 检查最高位
        }

        /**
         * 获取报警分类代码 (bit7-1)
         * @return 报警分类代码
         */
        public int getAlarmCategory() {
            return value & 0x7F; // 获取低7位
        }

        /**
         * 根据分类代码和发生/解除标志位创建AlarmCode
         * 注意：只能返回预定义的枚举值，如果需要其他分类代码，请使用 CustomAlarmCode 类
         *
         * @param categoryCode 报警分类代码 (bit7-1, 范围0-127)
         * @param isOccurred 是否发生 (true=发生, false=解除)
         * @return 对应的AlarmCode实例，如果不匹配预定义值则抛出异常
         * @throws IllegalArgumentException 如果分类代码超出范围或不匹配预定义值
         */
        public static AlarmCode create(int categoryCode, boolean isOccurred) {
            // 验证分类代码范围
            if (categoryCode < 0 || categoryCode > 127) {
                throw new IllegalArgumentException("报警分类代码必须在0-127范围内: " + categoryCode);
            }

            // 对于标准的分类代码5
            if (categoryCode == 5) {
                return isOccurred ? ALARM_OCCURRED : ALARM_RELEASED;
            } else {
                throw new IllegalArgumentException("不支持的报警分类代码: " + categoryCode +
                    ", 当前只支持分类代码5。如需其他分类代码，请使用 CustomAlarmCode.create()");
            }
        }

        /**
         * 根据字节值获取AlarmCode
         *
         * @param alcdValue ALCD字节值
         * @return 对应的AlarmCode实例，如果不匹配预定义值则抛出异常
         * @throws IllegalArgumentException 如果字节值不匹配预定义的枚举值
         */
        public static AlarmCode fromByte(byte alcdValue) {
            // 检查是否匹配预定义的枚举值
            if (alcdValue == ALARM_OCCURRED.value) {
                return ALARM_OCCURRED;
            } else if (alcdValue == ALARM_RELEASED.value) {
                return ALARM_RELEASED;
            } else {
                throw new IllegalArgumentException("不支持的ALCD值: 0x" +
                    String.format("%02X", alcdValue & 0xFF) +
                    ", 当前只支持标准的报警分类代码5。如需其他值，请使用 CustomAlarmCode.fromByte()");
            }
        }

        /**
         * 便利方法：创建报警发生的AlarmCode
         *
         * @param categoryCode 报警分类代码
         * @return 报警发生的AlarmCode
         */
        public static AlarmCode occurred(int categoryCode) {
            return create(categoryCode, true);
        }

        /**
         * 便利方法：创建报警解除的AlarmCode
         *
         * @param categoryCode 报警分类代码
         * @return 报警解除的AlarmCode
         */
        public static AlarmCode released(int categoryCode) {
            return create(categoryCode, false);
        }
    }

    /**
     * 自定义报警代码类 - 用于处理非标准分类代码的ALCD
     * 当需要使用分类代码5以外的值时使用此类
     */
    public static class CustomAlarmCode {
        private final byte value;

        private CustomAlarmCode(byte value) {
            this.value = value;
        }

        public byte getValue() {
            return value;
        }

        /**
         * 获取报警状态位 (bit8)
         * @return true=发生, false=解除
         */
        public boolean isOccurred() {
            return (value & 0x80) != 0; // 检查最高位
        }

        /**
         * 获取报警分类代码 (bit7-1)
         * @return 报警分类代码
         */
        public int getAlarmCategory() {
            return value & 0x7F; // 获取低7位
        }

        /**
         * 根据分类代码和发生/解除标志位创建CustomAlarmCode
         *
         * @param categoryCode 报警分类代码 (bit7-1, 范围0-127)
         * @param isOccurred 是否发生 (true=发生, false=解除)
         * @return CustomAlarmCode实例
         * @throws IllegalArgumentException 如果分类代码超出范围
         */
        public static CustomAlarmCode create(int categoryCode, boolean isOccurred) {
            // 验证分类代码范围
            if (categoryCode < 0 || categoryCode > 127) {
                throw new IllegalArgumentException("报警分类代码必须在0-127范围内: " + categoryCode);
            }

            // 计算ALCD值: (状态位 << 7) | 分类代码
            byte alcdValue = (byte) ((isOccurred ? 1 : 0) << 7 | categoryCode);
            return new CustomAlarmCode(alcdValue);
        }

        /**
         * 根据字节值创建CustomAlarmCode
         *
         * @param alcdValue ALCD字节值
         * @return CustomAlarmCode实例
         */
        public static CustomAlarmCode fromByte(byte alcdValue) {
            return new CustomAlarmCode(alcdValue);
        }

        /**
         * 便利方法：创建报警发生的CustomAlarmCode
         *
         * @param categoryCode 报警分类代码
         * @return 报警发生的CustomAlarmCode
         */
        public static CustomAlarmCode occurred(int categoryCode) {
            return create(categoryCode, true);
        }

        /**
         * 便利方法：创建报警解除的CustomAlarmCode
         *
         * @param categoryCode 报警分类代码
         * @return 报警解除的CustomAlarmCode
         */
        public static CustomAlarmCode released(int categoryCode) {
            return create(categoryCode, false);
        }

        /**
         * 转换为标准AlarmCode（如果可能）
         *
         * @return 对应的AlarmCode，如果不匹配则返回null
         */
        public AlarmCode toAlarmCode() {
            if (value == AlarmCode.ALARM_OCCURRED.getValue()) {
                return AlarmCode.ALARM_OCCURRED;
            } else if (value == AlarmCode.ALARM_RELEASED.getValue()) {
                return AlarmCode.ALARM_RELEASED;
            }
            return null;
        }

        @Override
        public String toString() {
            return String.format("CustomAlarmCode{value=0x%02X, occurred=%s, category=%d}",
                value & 0xFF, isOccurred(), getAlarmCategory());
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            CustomAlarmCode that = (CustomAlarmCode) obj;
            return value == that.value;
        }

        @Override
        public int hashCode() {
            return Byte.hashCode(value);
        }
    }

    /**
     * 报警类别枚举
     * bit16-13: 报警类别 (Alarm Type)
     * 0001 = 重異常 (Severe abnormality)
     * 0010 = 中異常 (Medium abnormality)
     * 0011 = 軽異常 (Minor abnormality)
     */
    public enum AlarmType {
        SEVERE_ABNORMALITY(0b0001),   // 重異常 (Severe abnormality) - 二进制 0001
        MEDIUM_ABNORMALITY(0b0010),   // 中異常 (Medium abnormality) - 二进制 0010
        MINOR_ABNORMALITY(0b0011);    // 軽異常 (Minor abnormality) - 二进制 0011

        private final int value;

        AlarmType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 构建标准的S5F1报警通知消息
     *
     * @param alarmCode 报警代码 (ALCD字段: bit8=状态, bit7-1=分类代码5)
     * @param alarmType 报警类别 (1=重异常, 2=中异常, 3=轻异常)
     * @param robotNumber 机器人号 (1-10)
     * @param alarmText 报警文本 (最大6字符)
     * @return S5F1消息数据
     *
     * S5F1消息格式:
     * L,3
     *   BIN,1  <ALCD>  ; 报警代码 (bit8=状态, bit7-1=分类代码5)
     *   U2,1   <ALID>  ; 报警ID (bit16-13=类别, bit12-5=0, bit4-1=机器人号)
     *   ASC,6  <ALTX>  ; 报警文本
     */
    public static Secs2 buildS5F1AlarmNotification(AlarmCode alarmCode, AlarmType alarmType,
                                                  int robotNumber, String alarmText) {
        // 验证参数
        if (robotNumber < 1 || robotNumber > 10) {
            throw new IllegalArgumentException("机器人号必须在1-10范围内: " + robotNumber);
        }

        if (alarmText == null) {
            alarmText = "";
        }

        // 确保报警文本不超过6个字符
        if (alarmText.length() > 6) {
            alarmText = alarmText.substring(0, 6);
        } else {
            // 不足6位用空格补齐
            alarmText = String.format("%-6s", alarmText);
        }

        // 构建报警ID (ALID) - 16位无符号整数
        // bit16-13 (4位): 报警类别 (Alarm Type)
        // bit12-5  (8位): 常数0 (Constant 0)
        // bit4-1   (4位): 机器人号 (Robot Number) 1-10
        //
        // 位布局: [AlarmType(4bit)][Constant0(8bit)][RobotNumber(4bit)]
        //         15-12           11-4              3-0
        int alarmId = (alarmType.getValue() << 12) | (robotNumber & 0x0F);

        // 构建S5F1消息体
        // L,3
        //   BIN,1  <ALCD>  ; 报警代码
        //   U2,1   <ALID>  ; 报警ID
        //   ASC,6  <ALTX>  ; 报警文本
        return Secs2.list(
            Secs2.binary(alarmCode.getValue()),    // ALCD: 报警代码
            Secs2.uint2(alarmId),                  // ALID: 报警ID
            Secs2.ascii(alarmText)                 // ALTX: 报警文本
        );
    }

    /**
     * 构建S5F1报警发生消息
     *
     * @param alarmType 报警类别
     * @param robotNumber 机器人号 (1-10)
     * @param alarmText 报警文本
     * @return S5F1消息数据
     */
    public static Secs2 buildS5F1AlarmOccurred(AlarmType alarmType, int robotNumber, String alarmText) {
        return buildS5F1AlarmNotification(AlarmCode.ALARM_OCCURRED, alarmType, robotNumber, alarmText);
    }

    /**
     * 构建S5F1报警解除消息
     *
     * @param alarmType 报警类别
     * @param robotNumber 机器人号 (1-10)
     * @param alarmText 报警文本
     * @return S5F1消息数据
     */
    public static Secs2 buildS5F1AlarmReleased(AlarmType alarmType, int robotNumber, String alarmText) {
        return buildS5F1AlarmNotification(AlarmCode.ALARM_RELEASED, alarmType, robotNumber, alarmText);
    }

    /**
     * 兼容旧版本的s5f1方法
     *
     * @deprecated 请使用 buildS5F1AlarmNotification 方法
     */
    @Deprecated
    public Secs2 s5f1(Integer agvCode, Integer status) throws InterruptedException, SecsException {
        AlarmCode alarmCode = status == 1 ? AlarmCode.ALARM_OCCURRED : AlarmCode.ALARM_RELEASED;
        AlarmType alarmType = AlarmType.MEDIUM_ABNORMALITY; // 默认中异常
        String alarmText = "ALM" + String.format("%03d", agvCode);

        return buildS5F1AlarmNotification(alarmCode, alarmType, agvCode, alarmText);
    }

    /**
     * 构建机器人异常报警消息
     *
     * @param robotNumber 机器人号 (1-10)
     * @param abnormalityNumber 异常编号 (6位数字)
     * @return S5F1消息数据
     */
    public static Secs2 buildS5F1RobotAbnormality(int robotNumber, int abnormalityNumber) {
        String alarmText = String.format("%06d", abnormalityNumber);
        return buildS5F1AlarmOccurred(AlarmType.MEDIUM_ABNORMALITY, robotNumber, alarmText);
    }

    /**
     * 构建机器人异常恢复消息
     *
     * @param robotNumber 机器人号 (1-10)
     * @param abnormalityNumber 异常编号 (6位数字)
     * @return S5F1消息数据
     */
    public static Secs2 buildS5F1RobotRecovery(int robotNumber, int abnormalityNumber) {
        String alarmText = String.format("%06d", abnormalityNumber);
        return buildS5F1AlarmReleased(AlarmType.MEDIUM_ABNORMALITY, robotNumber, alarmText);
    }

    /**
     * 构建严重异常报警消息
     *
     * @param robotNumber 机器人号 (1-10)
     * @param errorCode 错误代码
     * @return S5F1消息数据
     */
    public static Secs2 buildS5F1SevereError(int robotNumber, String errorCode) {
        return buildS5F1AlarmOccurred(AlarmType.SEVERE_ABNORMALITY, robotNumber, errorCode);
    }

    /**
     * 构建轻微异常报警消息
     *
     * @param robotNumber 机器人号 (1-10)
     * @param warningCode 警告代码
     * @return S5F1消息数据
     */
    public static Secs2 buildS5F1MinorWarning(int robotNumber, String warningCode) {
        return buildS5F1AlarmOccurred(AlarmType.MINOR_ABNORMALITY, robotNumber, warningCode);
    }

    /**
     * 构建S5F1报警通知消息 - 基于AbnormalApiDTO
     *
     * @param abnormalDto 异常消息DTO
     * @return S5F1消息数据
     * @throws IllegalArgumentException 当DTO参数无效时
     *
     * 映射规则:
     * - level字段: 1=轻异常, 2=中异常, 3=重异常
     * - status字段: 0=报警发生, 2=报警解除 (忽略status=1的情况)
     * - vehicleCode字段: 提取机器人号 (支持格式: "AGV1", "Robot3", "3"等)
     * - code字段: 格式化为6位报警文本
     */
    public static Secs2 buildS5F1AlarmNotification(AbnormalApiDTO abnormalDto) {
        if (abnormalDto == null) {
            throw new IllegalArgumentException("AbnormalApiDTO不能为null");
        }

        // 映射报警类别 (level字段)
        AlarmType alarmType;
        Integer level = abnormalDto.getLevel();
        if (level == null) {
            throw new IllegalArgumentException("异常等级(level)不能为null");
        }
        switch (level) {
            case 1:
                alarmType = AlarmType.MINOR_ABNORMALITY;   // 普通 -> 轻异常
                break;
            case 2:
                alarmType = AlarmType.MEDIUM_ABNORMALITY;  // 警告 -> 中异常
                break;
            case 3:
                alarmType = AlarmType.SEVERE_ABNORMALITY;  // 错误 -> 重异常
                break;
            default:
                throw new IllegalArgumentException("不支持的异常等级: " + level + ", 支持的值: 1(普通), 2(警告), 3(错误)");
        }

        // 映射报警代码 (status字段)
        AlarmCode alarmCode;
        Integer status = abnormalDto.getStatus();
        if (status == null) {
            throw new IllegalArgumentException("异常状态(status)不能为null");
        }
        switch (status) {
            case 0:
                alarmCode = AlarmCode.ALARM_OCCURRED;  // 激活 -> 报警发生
                break;
            case 2:
                alarmCode = AlarmCode.ALARM_RELEASED;  // 关闭 -> 报警解除
                break;
            case 1:
                throw new IllegalArgumentException("忽略状态(status=1)不支持生成SECS消息");
            default:
                throw new IllegalArgumentException("不支持的异常状态: " + status + ", 支持的值: 0(激活), 2(关闭)");
        }

        // 提取机器人号 (vehicleCode字段) - 注意：这个方法有问题，应该使用vehicleName
        // TODO: 这个方法存在问题，vehicleCode不携带robotNumber，应该从vehicleName提取
        // 建议在调用处直接传入正确的robotNumber，而不是使用这个有问题的方法
        int robotNumber = extractRobotNumber(abnormalDto.getVehicleCode());

        // 格式化报警文本 (code字段)
        String alarmText = formatAlarmText(abnormalDto.getCode());

        // 调用原有方法构建S5F1消息
        return buildS5F1AlarmNotification(alarmCode, alarmType, robotNumber, alarmText);
    }

    public static AgvDTO getAgvDTO(String vehicleCode){
        return getAgvDTO(vehicleCode, null);
    }

    public static AgvDTO getAgvDTO(String vehicleCode, Integer level) {
        log.debug("【getAgvDTO】开始获取车辆[{}]的AgvDTO数据，level={}", vehicleCode, level);

        AgvDTO agvByCode = AgvDTOCache.getAgvByCode(vehicleCode);

        if (agvByCode == null || StringUtils.isBlank( BayNameUtils.extractBayNameFromAgvName(agvByCode.getAgvName()))) {
            log.warn("【getAgvDTO】无法从缓存找到车辆[{}]的详细信息，尝试从数据库查询", vehicleCode);

            // 如果缓存中没有，尝试从数据库查询
            Agv agv = SpringUtils.getBean(AgvService.class).selectByAgvCode(vehicleCode);
            if (agv == null) {
                log.error("【getAgvDTO】车辆[{}]在数据库中不存在，返回null", vehicleCode);
                return null;
            }

            log.info("【getAgvDTO】从数据库查询到车辆[{}]，创建基本AgvDTO: agvName={}", vehicleCode, agv.getAgvName());
            // 创建基本的AgvDTO用于处理
            agvByCode = createBasicAgvDTO(agv, level);
        } else {
            log.debug("【getAgvDTO】从缓存成功获取车辆[{}]数据: connectStatus={}, status={}",
                     vehicleCode, agvByCode.getConnectStatus(), agvByCode.getStatus());
        }

        return agvByCode;
    }

    /**
     * 创建基本的AgvDTO用于异常处理
     */
    private static  AgvDTO createBasicAgvDTO(Agv agv, Integer errorLevel) {
        AgvDTO agvDTO = new AgvDTO();
        agvDTO.setAgvCode(agv.getAgvCode());
        agvDTO.setName(agv.getAgvName());
        agvDTO.setAgvName( agv.getAgvName()) ;

        // 根据异常等级推断AGV状态
        if ( errorLevel!= null && errorLevel  >= 3) {
            // 错误级别的异常，设置为ERROR状态
            agvDTO.setStatus(VehicleStatus.ERROR);
        } else {
            // 其他情况保持当前状态或设置为IDLE
            agvDTO.setStatus(VehicleStatus.IDLE);
        }

        return agvDTO;
    }
    /**
     * 从vehicleCode中提取机器人号
     *
     * @param vehicleCode 机器人编码 (支持格式: "AGV1", "Robot3", "3", "agv05"等)
     * @return 机器人号 (1-10)
     * @throws IllegalArgumentException 当无法提取有效机器人号时
     */
    private static int extractRobotNumber(String vehicleCode) {
        if (vehicleCode == null || vehicleCode.trim().isEmpty()) {
            throw new IllegalArgumentException("机器人编码(vehicleCode)不能为空");
        }

        String code = vehicleCode.trim();

        // 尝试直接解析为数字
        try {
            int number = Integer.parseInt(code);
            if (number >= 1 && number <= 10) {
                return number;
            }
        } catch (NumberFormatException e) {
            // 继续尝试其他格式
        }

        // 提取字符串中的数字部分
        StringBuilder numberPart = new StringBuilder();
        for (char c : code.toCharArray()) {
            if (Character.isDigit(c)) {
                numberPart.append(c);
            }
        }

        if (numberPart.length() == 0) {
            throw new IllegalArgumentException("无法从机器人编码中提取数字: " + vehicleCode);
        }

        try {
            int robotNumber = Integer.parseInt(numberPart.toString());
            if (robotNumber < 1 || robotNumber > 10) {
                throw new IllegalArgumentException("机器人号必须在1-10范围内, 提取到的值: " + robotNumber + ", 来源: " + vehicleCode);
            }
            return robotNumber;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无法解析机器人编码中的数字部分: " + numberPart + ", 来源: " + vehicleCode);
        }
    }

    /**
     * 格式化异常代码为报警文本
     *
     * @param code 异常代码
     * @return 格式化后的6位报警文本
     */
    private static String formatAlarmText(Integer code) {
        if (code == null) {
            return "000000";  // 默认值
        }

        // 格式化为6位数字，不足补0
        return String.format("%06d", Math.abs(code));
    }

    /**
     * 构建S5F1异常发生消息 - 基于AbnormalApiDTO
     *
     * @param abnormalDto 异常消息DTO (status必须为0-激活)
     * @return S5F1消息数据
     * @throws IllegalArgumentException 当DTO状态不是激活状态时
     */
    public static Secs2 buildS5F1AlarmOccurred(AbnormalApiDTO abnormalDto) {
        if (abnormalDto == null) {
            throw new IllegalArgumentException("AbnormalApiDTO不能为null");
        }

        if (abnormalDto.getStatus() == null || abnormalDto.getStatus() != 0) {
            throw new IllegalArgumentException("构建异常发生消息时，status必须为0(激活状态), 当前值: " + abnormalDto.getStatus());
        }

        return buildS5F1AlarmNotification(abnormalDto);
    }

    /**
     * 构建S5F1异常解除消息 - 基于AbnormalApiDTO
     *
     * @param abnormalDto 异常消息DTO (status必须为2-关闭)
     * @return S5F1消息数据
     * @throws IllegalArgumentException 当DTO状态不是关闭状态时
     */
    public static Secs2 buildS5F1AlarmReleased(AbnormalApiDTO abnormalDto) {
        if (abnormalDto == null) {
            throw new IllegalArgumentException("AbnormalApiDTO不能为null");
        }

        if (abnormalDto.getStatus() == null || abnormalDto.getStatus() != 2) {
            throw new IllegalArgumentException("构建异常解除消息时，status必须为2(关闭状态), 当前值: " + abnormalDto.getStatus());
        }

        return buildS5F1AlarmNotification(abnormalDto);
    }

    /**
     * 解析S5F1消息内容
     *
     * @param secs2Data S5F1消息数据
     * @return 解析后的报警信息
     */
    public static AlarmInfo parseS5F1(Secs2 secs2Data) {
        try {
            if (secs2Data.size() != 3) {
                throw new IllegalArgumentException("S5F1消息格式错误: 应包含3个元素");
            }

            // 解析ALCD (报警代码)
            byte alarmCodeValue = secs2Data.getByte(0,0);;
            AlarmCode alarmCode = AlarmCode.fromByte(alarmCodeValue);

            // 可选：验证报警分类代码是否为预期值（如果需要严格验证）
            int alarmCategory = alarmCode.getAlarmCategory();
            // 注释掉严格验证，允许不同的分类代码
            // if (alarmCategory != 5) {
            //     throw new IllegalArgumentException("无效的报警分类代码: " + alarmCategory + ", 期望值: 5");
            // }

            // 解析ALID (报警ID)
            int alarmId = secs2Data.getInt(1,0  );
            // bit16-13: 报警类别
            int alarmTypeValue = (alarmId >> 12) & 0x0F;
            // bit4-1: 机器人号
            int robotNumber = alarmId & 0x0F;

            AlarmType alarmType;
            switch (alarmTypeValue) {
                case 0b0001: alarmType = AlarmType.SEVERE_ABNORMALITY; break;  // 0001
                case 0b0010: alarmType = AlarmType.MEDIUM_ABNORMALITY; break; // 0010
                case 0b0011: alarmType = AlarmType.MINOR_ABNORMALITY; break;  // 0011
                default: throw new IllegalArgumentException("未知的报警类别: " + alarmTypeValue + " (二进制: " + Integer.toBinaryString(alarmTypeValue) + ")");
            }

            // 解析ALTX (报警文本)
            String alarmText = secs2Data.get(2).getAscii().trim();

            return new AlarmInfo(alarmCode, alarmType, robotNumber, alarmText);

        } catch (Exception e) {
            e.printStackTrace();
            throw new IllegalArgumentException("解析S5F1消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 报警信息数据类
     */
    public static class AlarmInfo {
        private final AlarmCode alarmCode;
        private final AlarmType alarmType;
        private final int robotNumber;
        private final String alarmText;

        public AlarmInfo(AlarmCode alarmCode, AlarmType alarmType, int robotNumber, String alarmText) {
            this.alarmCode = alarmCode;
            this.alarmType = alarmType;
            this.robotNumber = robotNumber;
            this.alarmText = alarmText;
        }

        public AlarmCode getAlarmCode() { return alarmCode; }
        public AlarmType getAlarmType() { return alarmType; }
        public int getRobotNumber() { return robotNumber; }
        public String getAlarmText() { return alarmText; }

        public boolean isAlarmOccurred() { return alarmCode == AlarmCode.ALARM_OCCURRED; }
        public boolean isAlarmReleased() { return alarmCode == AlarmCode.ALARM_RELEASED; }

        @Override
        public String toString() {
            return String.format("AlarmInfo{code=%s, type=%s, robot=%d, text='%s'}",
                alarmCode, alarmType, robotNumber, alarmText);
        }
    }

    /**
     * 根据IP地址查找客户端连接
     */
    public static ClientConnection findConnectionByIP(String targetIP, Integer targetPort) {
        Secs1OnTcpIpReceiverCommunicator  secs1OnTcpIpReceiverCommunicator =   SpringUtils.getBean(Secs1OnTcpIpReceiverCommunicator.class);
        Collection<ClientConnection> connections = secs1OnTcpIpReceiverCommunicator.getConnections();

        for (ClientConnection connection : connections) {
            SocketAddress remoteAddress = connection.getRemoteAddress();

            if (remoteAddress instanceof InetSocketAddress) {
                InetSocketAddress inetAddress = (InetSocketAddress) remoteAddress;
                String connectionIP = inetAddress.getAddress().getHostAddress();
                int connectionPort = inetAddress.getPort();

                // 匹配IP地址
                if (connectionIP.equals(targetIP)) {
                    // 如果指定了端口，则同时匹配端口
                    if (targetPort == null || connectionPort == targetPort) {
                        return connection;
                    }
                }
            }
        }

        return null;
    }
}
