package com.youibot.tms.biz.forest.dto;


/**
 * @ClassName: MissionWorkStatus
 * @Description: 对应的missionWork状态
 * <AUTHOR>
 * @date 2020-07-30 02:23:06 
*/

/**  
 * @ClassName: MissionWorkStatus
 * @Description:  调度系统missionWork状态
 * <AUTHOR>
 * @date 2020-07-30 02:24:41 
*/  
public enum MissionWorkStatus  {

	/**
	 * 创建(未执行)
	 */
	CREATE,
	/**
	 * 已分配
	 */
	ASSIGNED,
	/**
	 * 开始执行
	 */
	START,
	/**
	 * 等待(继续)执行
	 */
	WAIT,
	/**
	 * 执行中
	 */
	RUNNING,
	/**
	 * 执行成功
	 */
	SUCCESS,
	/**
	 * 执行错误
	 */
	FAULT,
	/**
	 * 暂停
	 */
	PAUSE,
	/**
	 * 暂停中
	 */
	BEING_PAUSE,
	/**
	 * 恢复中
	 */
	BEING_RESUME,
	/**
	 * 已停止
	 */
	SHUTDOWN,
	/**
	 * 停止中
	 */
	BEING_SHUTDOWN,
	/**
	 * 等待输入
	 */
	WAITINPUT

}
