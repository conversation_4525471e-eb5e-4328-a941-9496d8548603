package com.youibot.tms.biz.flow.common.listener;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.entity.FromToRecord;
import com.youibot.tms.biz.entity.Task;
import com.youibot.tms.biz.entity.TaskLog;
import com.youibot.tms.biz.enums.FromToRecordStatusEnum;
import com.youibot.tms.biz.enums.TaskStatus;
import com.youibot.tms.biz.flow.common.AbstractBizCommonComponent;
import com.youibot.tms.biz.flow.common.CommonWorkFlowVariable;
import com.youibot.tms.biz.flow.common.components.ForBeginComponent;
import com.youibot.tms.biz.flow.common.components.PickupComponent;
import com.youibot.tms.biz.flow.common.components.PutDownComponent;
import com.youibot.tms.biz.flow.constants.GlobalVariableKeys;
import com.youibot.tms.biz.flow.dto.PortAndPortType;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.workflow.core.annotation.FlowListener;
import com.youibot.tms.workflow.core.enums.FlowNodeStatusEnum;
import com.youibot.tms.workflow.core.enums.FlowNodeTypeEnum;
import com.youibot.tms.workflow.core.flow.FlowNode;
import com.youibot.tms.workflow.core.flow.WorkFlow;
import com.youibot.tms.workflow.core.listener.WorkFlowExecuteListener;
import com.youibot.tms.workflow.entity.WorkFlowTemplateEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.analysis.solvers.BaseUnivariateSolver;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import com.youibot.tms.biz.flow.common.components.MoveComponent;


/**
 * 工作流流程的生命周期
 * <NAME_EMAIL> on 2023/1/31.
 *
 * <AUTHOR>
 * @date 2023/1/31 15:50
 */
@Slf4j
@FlowListener("commonWorkFlowListener")
public class CommonWorkFlowListener implements WorkFlowExecuteListener {
    @Autowired
    protected FlowTemplateGlobalVariableService flowTemplateGlobalVariableService;
    @Autowired
    protected FleetProxyService fleetProxyService;
    @Autowired
    private AgvService agvService;
    @Resource
    protected TaskLogService taskLogService;
    @Resource
    protected TaskService taskService;
    @Resource
    protected TaskDispatchAgvService taskDispatchAgvService;
    @Resource
    private FlowTemplateAssistService flowTemplateAssistService;

    /**
     * 全局变量KEY
     */
    public final static String locationListKey = "locationList";
    @Autowired
    private FromToRecordService fromToRecordService;

    /**
     * 处理任务取消
     * @param workFlow 流程信息
     * @param flowNode 当前流程节点信息
     */
    @Override
    public void onFlowNodeBeforeStarting(WorkFlow workFlow, FlowNode flowNode) {
        log.info("onBeforeStarting 校验MES任务是否已经被取消了");
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        AbstractBizCommonComponent component = (AbstractBizCommonComponent) flowNode.getComponent();
        String flowTemplateId = workFlow.getFlowTemplateId();
        WorkFlowTemplateEntity defaultFlowTemplate = flowTemplateAssistService.getDefaultFlowTemplate();
        if (!defaultFlowTemplate.getId().equals(flowTemplateId)) {
            log.info("当前流程时取消流程，不需要判断任务是否被取消！");
            return;
        }
        boolean isForBeginNode = false;
        FlowNode forNode = flowNode.getForNode();
        if (ToolUtil.isNotEmpty(forNode)) {
            String beginNodeId = forNode.getBeginNodeId();
            log.info("循环组件：{}，ID：{},循环开始节点ID：{}", forNode.getName(), forNode.getId(), beginNodeId);
            isForBeginNode = beginNodeId.equals(flowNode.getId());
        }
        //假如处于循环当中，那么设置当前点位信息
        AtomicInteger forCurrentNum = flowNode.getForCurrentNum();
        if (forCurrentNum != null && isForBeginNode) {
            Object portAndPortTypesString = taskGlobalVariable.get(locationListKey);
            if (ToolUtil.isNotEmpty(portAndPortTypesString)) {
                String portAndPortTypesString1 = "";
                if (portAndPortTypesString instanceof String) {
                    portAndPortTypesString1 = portAndPortTypesString.toString();
                } else {
                    portAndPortTypesString1 = JSON.toJSONString(portAndPortTypesString);
                }
                List<PortAndPortType> portAndPortTypes = JSON.parseArray(portAndPortTypesString1, PortAndPortType.class);
                int o = forCurrentNum.get();
                if (o >= portAndPortTypes.size()) {
                    o = 0;
                }
                component.setCurrentPortInfo(taskGlobalVariable, portAndPortTypes.get(o), AbstractBizCommonComponent.getPortInfo(portAndPortTypes.get(o)));
            }
        }
        String currentMesTaskCode = taskGlobalVariable.getString(flowTemplateGlobalVariableService.getCurrentPortCustomerTaskCodeKey());
        String taskCode = taskGlobalVariable.getString(flowTemplateGlobalVariableService.getTaskCodeKey());
        String customerTaskCode = taskGlobalVariable.getString(flowTemplateGlobalVariableService.getCurrentPortCustomerTaskCodeKey());
        if (currentMesTaskCode == null) {
            currentMesTaskCode = taskGlobalVariable.getString("currentMesTaskCode");
        }
        log.info("onBeforeStarting 当前流程的流程ID：{}，MES任务编号：{}", workFlow.getId(), currentMesTaskCode);
        if (ToolUtil.isNotEmpty(currentMesTaskCode)) {
            FromToRecord mesTask = fromToRecordService.getById(currentMesTaskCode);
            if (ToolUtil.isNotEmpty(mesTask)) {
                boolean isMove = component instanceof MoveComponent;
                boolean isPickup = component instanceof PickupComponent;
                boolean isPutDown = component instanceof PutDownComponent;
                boolean isForNode = component instanceof ForBeginComponent;
       /*         if (isMove) {
                    mesTask.setActionType(ActionType.MOVE);
                } else if (isPickup || isPutDown) {
                    mesTask.setActionType(ActionType.AMR);
                } else if (isForNode) {
                    mesTask.setActionType(ActionType.OTHER);
                }*/
                fromToRecordService.updateById(mesTask);
                if (FromToRecordStatusEnum.CANCEL.name().equals(mesTask.getStatus())) {
                    log.info("onBeforeStarting MES任务已经被取消了或者异常了，判断当前流程节点是否需要跳过！");
//                    taskLog(taskCode, workFlow, flowNode, "任务失败", "由于此任务已经被取消或者异常了，因此跳过此任务之后的所有节点！上层系统任务编号：【" + customerTaskCode + "】");

                    if (!FlowNodeTypeEnum.FOR.equals(flowNode.getType()) && !flowNode.isLast() && !isForBeginNode) {
                        log.info("onBeforeStarting MES任务已经被取消了或者异常了，将流程节点状态设置为跳过状态，将不再执行此节点！");
                        flowNode.setStatus(FlowNodeStatusEnum.SKIP);
                        workFlow.setContinue(true);
                    }
                }
            }
        }
    }

    @Override
    public void onFlowNodeStart(WorkFlow workFlow, FlowNode flowNode) {
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        String agvCode = taskGlobalVariable.getString(GlobalVariableKeys.agvCodeKey);
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        log.info("流程节点开始，当前节点信息：流程[{}]，任务[{}]，AGV[{}],全局变量：{}，输出参数：{}", workFlow.getId(), taskCode, agvCode, JSON.toJSONString(taskGlobalVariable), flowNode.getPrevNode() == null ? "无" : JSON.toJSONString(flowNode.getPrevNode().getOutputParams()));
    }

    @Override
    public void onFlowNodeSuccess(WorkFlow workFlow, FlowNode flowNode) {
        // 这里插单
    }

    @Override
    public void onFlowNodeSkipped(WorkFlow workFlow, FlowNode flowNode) {

    }

    @Override
    public void onFlowStart(WorkFlow workFlow, FlowNode flowNode) {
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        log.info("流程开始，全局变量：{}", JSONObject.toJSONString(taskGlobalVariable));
        Task task = taskService.selectByTaskCode(taskCode);
        task.setStartTime(new Date());
        taskService.updateById(task);
    }


    @Override
    public void onError(WorkFlow workFlow, FlowNode flowNode, Exception exception) {
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        String agvCode = taskGlobalVariable.getString(GlobalVariableKeys.agvCodeKey);
        log.info("生命周期监听器监听流程发生异常!流程[{}]，任务[{}]，AGV[{}]，异常信息：{}", workFlow.getId(), taskCode, agvCode, Throwables.getStackTraceAsString(exception));
        String currentPortCode = taskGlobalVariable.getString(GlobalVariableKeys.currentPortCodeKey);
        String currentCarrierCode = taskGlobalVariable.getString(GlobalVariableKeys.currentCarrierCodeKey);
        if (ToolUtil.isEmpty(currentPortCode)) {
            currentPortCode = "暂无";
        }
        if (ToolUtil.isEmpty(currentCarrierCode)) {
            currentCarrierCode = "暂无";
        }
        String message = exception.getMessage();
        if( exception instanceof BusinessException && StringUtils.isBlank(exception.getMessage())) {
            message =((BusinessException) exception).getMsgs();
        }
        String remark = "AGV[" + agvCode + "]执行任务期间发生异常！目标点[" + currentPortCode + "] 目标载具[" + currentCarrierCode + "]，异常信息：" + message;

        TaskStatus errorTaskStatus = TaskStatus.ERROR;
        taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getCode, taskCode).set(Task::getStatus, errorTaskStatus).set(Task::getErrorMessage, remark));
        TaskLog taskLog = new TaskLog();
        taskLog.setTaskCode(taskCode);
        taskLog.setFlowId(workFlow.getId());
        taskLog.setTitle("流程异常，已挂起");
        taskLog.setTaskStatus(errorTaskStatus.name());
        taskLog.setFlowNodeId(flowNode.getId());
        taskLog.setRemark(remark);
        WorkFlow parentFlow = workFlow.getParentFlow();
        if (parentFlow != null) {
            taskLog.setParentFlowId(parentFlow.getId());
            taskLog.setFlowNodeId(parentFlow.getCurrentNode().getId());
        }
        Object variables = flowNode.getVariables();
        if (variables == null) {
            WorkFlow childrenFlow = workFlow.getChildrenFlow();
            if (childrenFlow != null) {
                FlowNode currentNode = childrenFlow.getCurrentNode();
                if (currentNode != null) {
                    variables = currentNode.getVariables();
                }
            }
        }
        taskLog.setNodeParams(variables == null ? null : variables.toString());
        taskLogService.save(taskLog);
    }

    @Override
    public void onStop(WorkFlow workFlow, FlowNode flowNode) {
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        String agvCode = taskGlobalVariable.getString(GlobalVariableKeys.agvCodeKey);
        taskDispatchAgvService.checkAndCancelDispatchAgv(taskCode);
        log.info("生命周期监听器监听到流程触发停止事件啦!流程[{}]，任务[{}]，AGV[{}]", workFlow.getId(), taskCode, agvCode);
        String currentPortCode = taskGlobalVariable.getString(GlobalVariableKeys.currentPortCodeKey);
        String currentCarrierCode = taskGlobalVariable.getString(GlobalVariableKeys.currentCarrierCodeKey);
        if (ToolUtil.isEmpty(currentPortCode)) {
            currentPortCode = "暂无";
        }
        if (ToolUtil.isEmpty(currentCarrierCode)) {
            currentCarrierCode = "暂无";
        }
        String remark = "AGV[" + agvCode + "]执行任务期间被[停止]！目标点[" + currentPortCode + "] 目标载具[" + currentCarrierCode + "]";
        taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getCode, taskCode).set(Task::getStatus, TaskStatus.CANCEL));
        TaskLog taskLog = new TaskLog();
        taskLog.setTaskCode(taskCode);
        taskLog.setFlowId(workFlow.getId());
        taskLog.setTitle("取消");
        taskLog.setTaskStatus(TaskStatus.CANCEL.name());
        if (flowNode != null) {
            taskLog.setFlowNodeId(flowNode.getId());
            Object variables = flowNode.getVariables();
            if (variables == null) {
                WorkFlow childrenFlow = workFlow.getChildrenFlow();
                if (childrenFlow != null) {
                    FlowNode currentNode = childrenFlow.getCurrentNode();
                    if (currentNode != null) {
                        variables = currentNode.getVariables();
                    }
                }
            }
            taskLog.setNodeParams(variables == null ? null : variables.toString());
        }
        taskLog.setRemark(remark);
        WorkFlow parentFlow = workFlow.getParentFlow();
        if (parentFlow != null) {
            taskLog.setParentFlowId(parentFlow.getId());
            taskLog.setFlowNodeId(parentFlow.getCurrentNode().getId());
        }
        taskLogService.save(taskLog);
    }

    @Override
    public void onSuspend(WorkFlow workFlow, FlowNode flowNode) {
        log.debug("生命周期监听器监听到流程挂起啦！流程编号：{}", workFlow.getId());
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        String agvCode = taskGlobalVariable.getString(GlobalVariableKeys.agvCodeKey);
        taskDispatchAgvService.checkAndSuspendDispatchAgv(taskCode);
        log.info("生命周期监听器监听到流程触发挂起事件!流程[{}]，任务[{}]，AGV[{}]", workFlow.getId(), taskCode, agvCode);
        String currentPortCode = taskGlobalVariable.getString(GlobalVariableKeys.currentPortCodeKey);
        String currentCarrierCode = taskGlobalVariable.getString(GlobalVariableKeys.currentCarrierCodeKey);
        if (ToolUtil.isEmpty(currentPortCode)) {
            currentPortCode = "暂无";
        }
        if (ToolUtil.isEmpty(currentCarrierCode)) {
            currentCarrierCode = "暂无";
        }
        Task task = taskService.selectByTaskCode(taskCode);
        if (!TaskStatus.ERROR.equals(task.getStatus()) && !TaskStatus.CANCEL.equals(task.getStatus()) &&
                !TaskStatus.SUCCESS.equals(task.getStatus()) && !TaskStatus.SUSPENDED.equals(task.getStatus())) {
            task.setStatus(TaskStatus.SUSPENDED);
            taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getId, task.getId()).set(Task::getStatus, TaskStatus.SUSPENDED));
            String remark = "AGV[" + agvCode + "]执行任务期间被[挂起]！目标点[" + currentPortCode + "] 目标载具[" + currentCarrierCode + "]";
            saveTaskLog(workFlow, flowNode, "暂停任务", taskCode, TaskStatus.SUSPENDED, remark);
        }
    }

    private void saveTaskLog(WorkFlow workFlow, FlowNode flowNode, String title, String taskCode, TaskStatus taskStatus, String remark) {
        TaskLog taskLog = new TaskLog();
        taskLog.setTaskCode(taskCode);
        taskLog.setFlowId(workFlow.getId());
        taskLog.setTitle(title);
        taskLog.setTaskStatus(taskStatus.name());
        if (flowNode != null) {
            taskLog.setFlowNodeId(flowNode.getId());
            Object variables = flowNode.getVariables();
            if (variables == null) {
                WorkFlow childrenFlow = workFlow.getChildrenFlow();
                if (childrenFlow != null) {
                    FlowNode currentNode = childrenFlow.getCurrentNode();
                    if (currentNode != null) {
                        variables = currentNode.getVariables();
                    }
                }
            }
            taskLog.setNodeParams(variables == null ? null : variables.toString());
        }
        taskLog.setRemark(remark);
        WorkFlow parentFlow = workFlow.getParentFlow();
        if (parentFlow != null) {
            taskLog.setParentFlowId(parentFlow.getId());
            taskLog.setFlowNodeId(parentFlow.getCurrentNode().getId());
        }
        taskLogService.save(taskLog);
    }

    @Override
    public void onWaiting(WorkFlow workFlow, FlowNode flowNode) {
        log.debug("生命周期监听器监听到流程暂停等待啦！");
    }

    @Override
    public void onShutdown(WorkFlow workFlow, FlowNode flowNode) {
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        String agvCode = taskGlobalVariable.getString(GlobalVariableKeys.agvCodeKey);
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        log.info("生命周期监听器监听到流程非正常结束!流程[{}]，任务[{}]，AGV[{}]", workFlow.getId(), taskCode, agvCode);
        //更新任务的结束时间
        taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getCode, taskCode).set(Task::getEndTime, new Date()));
        if (agvCode != null) {
            Agv agv = agvService.selectByAgvCode(agvCode);
            if (agv != null) {
                //假如机器人时被当前流程锁定的，那么就释放机器人，否则不是释放机器人，避免把机器人当前执行的其他任务给释放掉了
                if (workFlow.getId().equals(agv.getWorkFlowId())) {
                    //开启自动充电和泊车
                    fleetProxyService.openAutoParkAndCharge(agvCode);
                    //释放机器人
                    agvService.releaseAgvAndReleaseAgvStoragePort(agvCode);
                }
            }
        }

        String currentPortCode = taskGlobalVariable.getString(GlobalVariableKeys.currentPortCodeKey);
        String currentCarrierCode = taskGlobalVariable.getString(GlobalVariableKeys.currentCarrierCodeKey);
        if (ToolUtil.isEmpty(currentPortCode)) {
            currentPortCode = "暂无";
        }
        if (ToolUtil.isEmpty(currentCarrierCode)) {
            currentCarrierCode = "暂无";
        }
        String remark = "机器人[" + agvCode + "]执行任务异常结束！目标点[" + currentPortCode + "] 目标载具[" + currentCarrierCode + "]";
        TaskLog taskLog = new TaskLog();
        taskLog.setTaskCode(taskCode);
        taskLog.setFlowId(workFlow.getId());
        taskLog.setTitle("取消");
        taskLog.setTaskStatus(TaskStatus.CANCEL.name());
        if (flowNode != null) {
            taskLog.setFlowNodeId(flowNode.getId());
        }
        taskLog.setRemark(remark);
        taskLogService.save(taskLog);
    }

    @Override
    public void onFlowCompleted(WorkFlow workFlow, FlowNode flowNode) {
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        String agvCode = taskGlobalVariable.getString(GlobalVariableKeys.agvCodeKey);
        log.info("生命周期监听器监听到流程正常结束!流程[{}]，任务[{}]，AGV[{}]", workFlow.getId(), taskCode, agvCode);
        taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getCode, taskCode).set(Task::getStatus, TaskStatus.SUCCESS).set(Task::getEndTime, new Date()));

        if (agvCode != null) {
            Agv agv = agvService.selectByAgvCode(agvCode);
            //假如机器人时被当前流程锁定的，那么就释放机器人，否则不是释放机器人，避免把机器人当前执行的其他任务给释放掉了
            if (workFlow.getId().equals(agv.getWorkFlowId())) {
                //开启自动充电和泊车
                fleetProxyService.openAutoParkAndCharge(agvCode);
                //释放机器人
                agvService.releaseAgvAndReleaseAgvStoragePort(agvCode);
            }
        }
        String remark = "AGV[" + agvCode + "]执行任务成功，释放机器人！任务[" + taskCode + "]";
        TaskLog taskLog = new TaskLog();
        taskLog.setTaskCode(taskCode);
        taskLog.setFlowId(workFlow.getId());
        taskLog.setTitle("完成");
        taskLog.setTaskStatus(TaskStatus.SUCCESS.name());
        if (flowNode != null) {
            taskLog.setFlowNodeId(flowNode.getId());
        }
        taskLog.setRemark(remark);
        taskLogService.save(taskLog);
    }

    @Override
    public void onRecovery(WorkFlow workFlow, FlowNode currentNode) {
        CommonWorkFlowVariable commonWorkFlowVariable = workFlow.getVariables();
        JSONObject taskGlobalVariable = commonWorkFlowVariable.getTaskGlobalVariable();
        String taskCode = taskGlobalVariable.getString(GlobalVariableKeys.taskCodeKey);
        String agvCode = taskGlobalVariable.getString(GlobalVariableKeys.agvCodeKey);
        taskDispatchAgvService.checkAndSuspendDispatchAgv(taskCode);
        log.info("生命周期监听器监听到流程恢复事件!流程[{}]，任务[{}]，AGV[{}]", workFlow.getId(), taskCode, agvCode);
        String currentPortCode = taskGlobalVariable.getString(GlobalVariableKeys.currentPortCodeKey);
        String currentCarrierCode = taskGlobalVariable.getString(GlobalVariableKeys.currentCarrierCodeKey);
        if (ToolUtil.isEmpty(currentPortCode)) {
            currentPortCode = "暂无";
        }
        if (ToolUtil.isEmpty(currentCarrierCode)) {
            currentCarrierCode = "暂无";
        }
        Task task = taskService.selectByTaskCode(taskCode);
        task.setStatus(TaskStatus.EXECUTING);
        taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getId, task.getId()).set(Task::getStatus, TaskStatus.EXECUTING));
        String remark = "恢复任务，AGV[" + agvCode + "]继续执行任务！目标点[" + currentPortCode + "] 目标载具[" + currentCarrierCode + "]";
        saveTaskLog(workFlow, currentNode, "恢复任务", task.getCode(), TaskStatus.EXECUTING, remark);

    }
}
