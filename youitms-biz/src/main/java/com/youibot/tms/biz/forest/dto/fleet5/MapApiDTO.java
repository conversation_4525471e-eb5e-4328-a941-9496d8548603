package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "MapApiDTO",description = "地图信息")
public class MapApiDTO {

    @ApiModelProperty(value = "编码", position = 2)
    private String code;

    @ApiModelProperty(value = "名称", position = 3)
    private String name;

    @ApiModelProperty(value = "创建时间", position = 15)
    private Date createTime;

    @ApiModelProperty(value = "发布时间", position = 16)
    private Date publishTime;

}
