package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * @Author：yangpeilin
 * @Date: 2020/5/21 16:46
 */
@Data
public class AGVStatisticsDTO implements Serializable {
  
	
    /**  
	 * @Fields serialVersionUID : TODO(描述)
	 * <AUTHOR>
	 * @date 2021-03-09 03:55:28 
	 */  
	
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "ID", position = 0)
    private String id;


    @ApiModelProperty(value = "工作时间 单位：秒", position = 1)
    private long workTime;


    @ApiModelProperty(value = "空闲时间 单位：秒", position = 2)
    private long freeTime;


    @ApiModelProperty(value = "总在线时间 单位：秒", position = 3)
    private long onTime;


    @ApiModelProperty(value = "总任务数", position = 4)
    private Integer missionCount;


    @ApiModelProperty(value = "已完成任务数", position = 5)
    private Integer missionFinishCount;

 
    @ApiModelProperty(value = "取消任务数", position = 6)
    private Integer missionCancelCount;

 
    @ApiModelProperty(value = "充电总次数", position = 7)
    private Integer chargeCount;

  
    @ApiModelProperty(value = "充电总时间 单位：秒", position = 8)
    private long chargeTime;

 
    @ApiModelProperty(value = "数据类型： 1:day,7:week,30:month,0:all", position = 9)
    private Integer dataType;

  
    @ApiModelProperty(value = "创建时间", position = 10)
    private Date createTime;

  
    @ApiModelProperty(value = "更新时间", position = 11)
    private Date updateTime;

  
    @ApiModelProperty(value = "机器人编号", position = 12)
    private String agvCode;

  
    @ApiModelProperty(value = "数据归属时间（年月日），日：当前日期；周：当前周最后一天，月：当前月最后一天；所有：空字符串", position = 13)
    private long belongTime;

    public AGVStatisticsDTO(String agvCode, Integer dataType){
        this.agvCode = agvCode;
        this.dataType = dataType;
    }

    public AGVStatisticsDTO(){}

    public void addTodayData(AGVStatisticsDTO day) {
        this.workTime = this.workTime + day.getWorkTime();
        this.freeTime = this.freeTime + day.getFreeTime();
        this.onTime = this.onTime + day.getOnTime();
        if (missionCount == null){
            setMissionCount(day.getMissionCount());
        }else {
            this.missionCount += day.getMissionCount();
        }
        if (missionFinishCount == null){
            setMissionFinishCount(day.getMissionFinishCount());
        }else {
            this.missionFinishCount += day.getMissionFinishCount();
        }
        if (missionCancelCount == null){
            setMissionCancelCount(day.getMissionCancelCount());
        }else {
            this.missionCancelCount += day.getMissionCancelCount();
        }
        if (chargeCount == null){
            setChargeCount(day.getChargeCount());
        }else {
            this.chargeCount += day.getChargeCount();
        }
        this.chargeTime = this.chargeTime + day.getChargeTime();
    }

    //设置默认值
    public void setDefaultValue() {
        if (StringUtils.isEmpty(this.id)){
            this.id = UUID.randomUUID().toString();
        }
        if (this.missionCount == null){
            this.missionCount = 0;
        }
        if (this.missionFinishCount == null){
            this.missionFinishCount = 0;
        }
        if (this.missionCancelCount == null){
            this.missionCancelCount = 0;
        }
        if (this.chargeCount == null){
            this.chargeCount = 0;
        }
        if (this.dataType == null){
            this.dataType = 0;
        }
    }
}
