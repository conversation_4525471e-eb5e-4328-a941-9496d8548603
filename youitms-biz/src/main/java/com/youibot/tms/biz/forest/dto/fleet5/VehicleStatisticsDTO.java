package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Fleet机器人统计数据DTO - 与实际Fleet接口返回数据结构匹配
 * Created by Augment Agent on 2024/12/19.
 */
@Data
@ApiModel(value = "VehicleStatisticsDTO", description = "Fleet机器人统计数据")
public class VehicleStatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态饼图数据")
    private StatusPieChart statusPieChart;

    @ApiModelProperty(value = "工作状态饼图数据")
    private WorkStatusPieChart workStatusPieChart;

    @ApiModelProperty(value = "状态折线图数据")
    private StatusLineChart statusLineChart;

    @ApiModelProperty(value = "稼动率折线图数据")
    private UtilizeRateLineChart utilizeRateLineChart;

    /**
     * 状态饼图数据
     */
    @Data
    @ApiModel(value = "StatusPieChart", description = "状态饼图数据")
    public static class StatusPieChart implements Serializable {
        @ApiModelProperty(value = "饼图数据列表")
        private List<PieData> pies;
    }

    /**
     * 工作状态饼图数据
     */
    @Data
    @ApiModel(value = "WorkStatusPieChart", description = "工作状态饼图数据")
    public static class WorkStatusPieChart implements Serializable {
        @ApiModelProperty(value = "饼图数据列表")
        private List<PieData> pies;
    }

    /**
     * 饼图数据项
     */
    @Data
    @ApiModel(value = "PieData", description = "饼图数据项")
    public static class PieData implements Serializable {
        @ApiModelProperty(value = "状态名称")
        private String name;

        @ApiModelProperty(value = "数值")
        private Double value;

        @ApiModelProperty(value = "显示单位")
        private String displayUnit;
    }

    /**
     * 状态折线图数据
     */
    @Data
    @ApiModel(value = "StatusLineChart", description = "状态折线图数据")
    public static class StatusLineChart implements Serializable {
        @ApiModelProperty(value = "系列数据列表")
        private List<SeriesData> seriesList;

        @ApiModelProperty(value = "X轴标签")
        private List<String> xAxisNames;
    }

    /**
     * 稼动率折线图数据
     */
    @Data
    @ApiModel(value = "UtilizeRateLineChart", description = "稼动率折线图数据")
    public static class UtilizeRateLineChart implements Serializable {
        @ApiModelProperty(value = "系列数据列表")
        private List<SeriesData> seriesList;

        @ApiModelProperty(value = "X轴标签")
        private List<String> xAxisNames;
    }

    /**
     * 系列数据
     */
    @Data
    @ApiModel(value = "SeriesData", description = "系列数据")
    public static class SeriesData implements Serializable {
        @ApiModelProperty(value = "系列名称")
        private String name;

        @ApiModelProperty(value = "数值列表")
        private List<Double> values;

        @ApiModelProperty(value = "显示单位列表")
        private List<String> displayUnits;
    }
}
