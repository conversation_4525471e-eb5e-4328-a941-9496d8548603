package com.youibot.tms.biz.events.listener;

import com.youibot.tms.biz.events.DeviceSignalChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DeviceSignalChangeListener implements ApplicationListener<DeviceSignalChangeEvent> {
    @Override
    public void onApplicationEvent(DeviceSignalChangeEvent event) {
        log.info("接收到信号变更事件:{}",event);
        //TODO 执行信号变更逻辑，预警或触发任务
    }
}
