package com.youibot.tms.biz.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.enums.FlowNodeTemplateGraphDataAttrTypeEnum;
import com.youibot.tms.biz.enums.FlowTemplateComponentTypeEnum;
import com.youibot.tms.common.core.domain.BaseEntity;
import com.youibot.tms.common.exception.ServiceException;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.workflow.core.enums.FlowNodeTypeEnum;
import com.youibot.tms.workflow.entity.WorkFlowNodeTemplateEntity;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Setter
@TableName(value = "biz_flow_node_template_graph_data", autoResultMap = true)
@ToString
@ApiModel(value = "流程模板图形数据", description = "流程模板图形数据")
@EqualsAndHashCode(callSuper = true)
public class FlowNodeTemplateGraphData extends BaseEntity {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    @TableField("type")
    private String type;
    @TableField("attr_type")
    private FlowNodeTemplateGraphDataAttrTypeEnum attrType;
    @TableField("x")
    private Double x;
    @TableField("y")
    private Double y;
    @TableField(value = "text", typeHandler = FastjsonTypeHandler.class)
    private JSONObject text;
    @TableField("source_node_id")
    private String sourceNodeId;
    @TableField("target_node_id")
    private String targetNodeId;
    @TableField("flow_template_id")
    private String flowTemplateId;
    @TableField(value = "start_point", typeHandler = FastjsonTypeHandler.class)
    private GraphCoordinate startPoint;
    @TableField(value = "end_point", typeHandler = FastjsonTypeHandler.class)
    private GraphCoordinate endPoint;
    @TableField(value = "points_list", typeHandler = FastjsonTypeHandler.class)
    private List<GraphCoordinate> pointsList;
    @TableField(value = "properties", typeHandler = FastjsonTypeHandler.class)
    private GraphProperties properties;
    @TableField(value = "params_map", typeHandler = FastjsonTypeHandler.class)
//    @TableField("params_map")
    private JSONObject paramsMap;

    /**
     * 删除标志（0代表存在 1代表删除）
     * 需要真删除，在实体类中重写此字段，并去掉TableLogic的注解
     */
    @TableField("del_flag")
    private Boolean delFlag = false;


    /**
     * 直接用前端传过来的ID作为节点ID，节点ID必须是UUID
     *
     * @param allEdgeList 边列表（连线列表）
     * @return
     */
    public WorkFlowNodeTemplateEntity toWorkFlowNodeTemplateEntity(List<FlowNodeTemplateGraphData> allEdgeList, List<FlowNodeTemplateGraphData> allNodeList) {
        String thisId = this.getId();
        WorkFlowNodeTemplateEntity workFlowNodeTemplateEntity = new WorkFlowNodeTemplateEntity();
        try {
            JSONObject text = this.getText();
            if (text != null) {
                workFlowNodeTemplateEntity.setName(text.getString("value"));
            }
        } catch (Exception e) {
            log.error("设置流程节点名称发生异常：{}", Throwables.getStackTraceAsString(e));
        }
        workFlowNodeTemplateEntity.setId(thisId);
        FlowTemplateComponentTypeEnum flowTemplateComponentTypeEnum = FlowTemplateComponentTypeEnum.valueOf(this.getType());
        workFlowNodeTemplateEntity.setComponentClazz(flowTemplateComponentTypeEnum.getClazz().getTypeName());
        workFlowNodeTemplateEntity.setFlowTemplateId(this.getFlowTemplateId());
        workFlowNodeTemplateEntity.setType(flowTemplateComponentTypeEnum.getNodeType());
        if (FlowNodeTypeEnum.FOR.equals(flowTemplateComponentTypeEnum.getNodeType())) {
            /**
             * 筛选出循环节点的两个目标点，其中一个是循环开始节点，一个是循环结束之后的下一个节点
             * 第一步先筛选出当前循环组件身上的连线
             * 第二步再从当前循环组件身上的连线中筛选出当前循环组件的循环开始节点
             */
            List<FlowNodeTemplateGraphData> currentForComponentEdgeList = allEdgeList.stream().filter(item -> item.getSourceNodeId().equals(thisId)).collect(Collectors.toList());
            if (currentForComponentEdgeList.size() < 2) {
                throw new ServiceException("前端传入的边列表参数中，缺少循环组件的循环开始节点连线！", 50003);
            }

            //经过筛选获取当前循环组件的循环开始节点
            FlowNodeTemplateGraphData nextNode;
//            Optional<FlowNodeTemplateGraphData> any = currentForComponentEdgeList.stream().filter(item -> checkForStartNode(item, allEdgeList, thisId)).findAny();
            Optional<FlowNodeTemplateGraphData> any = currentForComponentEdgeList.stream().filter(item -> {
                        for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : allNodeList) {
                            String targetNodeId1 = item.getTargetNodeId();
                            if (targetNodeId1.equals(flowNodeTemplateGraphData.getId())) {
                                boolean equals = FlowTemplateComponentTypeEnum.valueOf(flowNodeTemplateGraphData.getType()).equals(FlowTemplateComponentTypeEnum.ForBeginComponent);
                                if (equals) {
                                    return true;
                                }
                            }
                        }
                        return false;
                    }

            ).findAny();
            if (any.isPresent()) {
                nextNode = any.get();
            } else {
                throw new ServiceException("前端传入的边列表参数中，缺少循环组件的循环开始节点连线或同一个循环组件有多个循环开始节点连线！", 50003);
            }
            //设置当前循环组件的开始节点
            workFlowNodeTemplateEntity.setForBeginNodeId(nextNode.getTargetNodeId());
            //从当前组件身上的连线中排出掉循环开始节点的连线，就剩下下一个节点的连线了
            Optional<FlowNodeTemplateGraphData> any1 = currentForComponentEdgeList.stream().filter(item -> !item.getId().equals(nextNode.getId())).findAny();
            if (any1.isPresent()) {
                FlowNodeTemplateGraphData flowNodeTemplateGraphData = any1.get();
                workFlowNodeTemplateEntity.setNextId(flowNodeTemplateGraphData.getTargetNodeId());
            }
        } else if (FlowNodeTypeEnum.SWITCH.equals(flowTemplateComponentTypeEnum.getNodeType())) {
            /**
             * 条件节点，先筛选出当前条件节点身上的连线
             */
            List<FlowNodeTemplateGraphData> currentForComponentEdgeList = allEdgeList.stream().filter(item -> thisId.equals(item.getSourceNodeId())).collect(Collectors.toList());
            HashMap<String, String> nextNodeMap = new HashMap<>();
            for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : currentForComponentEdgeList) {
                nextNodeMap.put(flowNodeTemplateGraphData.getTargetNodeId(), flowNodeTemplateGraphData.getTargetNodeId());
            }
            workFlowNodeTemplateEntity.setNextNodes(JSON.toJSONString(nextNodeMap));
        } else if (FlowNodeTypeEnum.COMMON.equals(flowTemplateComponentTypeEnum.getNodeType()) || FlowNodeTypeEnum.SYSTEM.equals(flowTemplateComponentTypeEnum.getNodeType())) {
            List<FlowNodeTemplateGraphData> currentForComponentEdgeList = allEdgeList.stream().filter(item -> thisId.equals(item.getSourceNodeId())).collect(Collectors.toList());
            if (currentForComponentEdgeList.size() > 1) {
                throw new ServiceException("当前组件非循环组件也非条件组件，不可连接多个节点！", 50004);
            } else if (currentForComponentEdgeList.size() == 1) {
                workFlowNodeTemplateEntity.setNextId(currentForComponentEdgeList.get(0).getTargetNodeId());
            }
        }
        return workFlowNodeTemplateEntity;
    }

    /**
     * 直接用前端传过来的ID作为节点ID，节点ID必须是UUID
     *
     * @return
     */
    public WorkFlowNodeTemplateEntity toWorkFlowNodeTemplateEntity() {
        String thisId = this.getId();
        WorkFlowNodeTemplateEntity workFlowNodeTemplateEntity = new WorkFlowNodeTemplateEntity();
        try {
            JSONObject text = this.getText();
            if (text != null) {
                workFlowNodeTemplateEntity.setName(text.getString("value"));
            }
        } catch (Exception e) {
            log.error("设置流程节点名称发生异常：{}", Throwables.getStackTraceAsString(e));
        }
        workFlowNodeTemplateEntity.setId(thisId);
        FlowTemplateComponentTypeEnum flowTemplateComponentTypeEnum = FlowTemplateComponentTypeEnum.valueOf(this.getType());
        workFlowNodeTemplateEntity.setComponentClazz(flowTemplateComponentTypeEnum.getClazz().getTypeName());
        workFlowNodeTemplateEntity.setFlowTemplateId(this.getFlowTemplateId());
        workFlowNodeTemplateEntity.setType(flowTemplateComponentTypeEnum.getNodeType());
        return workFlowNodeTemplateEntity;
    }

    /**
     * 校验当前这个连线是否就是连往循环开始节点的连线
     * 逻辑走向  1、当前连线的目标节点是循环开始节点，直接返回true
     * 2、当前连线的目标节点不是循环开始节点，那么就需要继续向下递归查找，直到找到循环开始节点为止
     * 地柜逻辑：1、遍历当前连线的目标节点的下一级连线列表，假如找到循环开始节点，则返回true
     * 2、假如没有找到那么就将下一级所有的连线存储起来，然后继续向下递归查找，直到找到循环开始节点为止
     * 3、当把所有的连线都遍历完了，还是没有找到循环开始节点，那么就返回false
     *
     * @param currentForComponentEdge 需要校验的连线
     * @param allEdgeList             所有连线
     * @param forNodeId               循环节点ID
     * @return
     */
    private boolean checkForStartNode(FlowNodeTemplateGraphData currentForComponentEdge, List<FlowNodeTemplateGraphData> allEdgeList, String forNodeId) {
        String targetNodeId = currentForComponentEdge.getTargetNodeId();
        if (targetNodeId.equals(forNodeId)) {
            return true;
        }
        List<FlowNodeTemplateGraphData> collect = allEdgeList.stream().filter(item -> item.getSourceNodeId().equals(targetNodeId)).collect(Collectors.toList());
        HashSet<String> hashSet = new HashSet<>();
        while (true) {
            ArrayList<FlowNodeTemplateGraphData> flowNodeTemplateGraphData1 = new ArrayList<>();
            for (FlowNodeTemplateGraphData flowNodeTemplateGraphData : collect) {
                if (flowNodeTemplateGraphData.getTargetNodeId().equals(forNodeId)) {
                    return true;
                }
                if (!hashSet.contains(flowNodeTemplateGraphData.getId())) {
                    hashSet.add(flowNodeTemplateGraphData.getId());
                    flowNodeTemplateGraphData1.addAll(allEdgeList.stream().filter(item -> item.getSourceNodeId().equals(flowNodeTemplateGraphData.getTargetNodeId())).collect(Collectors.toList()));
                }
            }
            collect.clear();
            collect.addAll(flowNodeTemplateGraphData1);
            if (ToolUtil.isEmpty(collect)) {
                break;
            }
        }
        return false;
    }
}
