package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "MapDetailApiDTO",description = "地图详情")
public class MapDetailApiDTO extends MapApiDTO {

    @ApiModelProperty(value = "定位图集合", position = 3)
    private List<LocatingInfo> locatingInfos;

    @ApiModelProperty(value = "路径", position = 4)
    private List<PathApiDTO> paths;

    @ApiModelProperty(value = "点位", position = 5)
    private List<MarkerApiDTO> markers;

    @ApiModelProperty(value = "区域", position = 6)
    private List<MapAreaDTO> mapAreas;

}
