package com.youibot.tms.biz.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 设备Modbus实时信号
 */
@Data
@ApiModel("设备Modbus实时信号")
public class DeviceModbusRealSignal {

    @ApiModelProperty("信号地址")
    private String address;

    @ApiModelProperty("信号描述")
    private String description;

    @ApiModelProperty("值")
    private Integer value;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @ApiModelProperty("状态更新时间")
    private Date updateTime;
}
