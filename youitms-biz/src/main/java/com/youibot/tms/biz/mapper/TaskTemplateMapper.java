package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youibot.tms.biz.entity.TaskTemplate;
import com.youibot.tms.system.dto.DataScopeParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 任务模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
@Mapper
public interface TaskTemplateMapper extends BaseMapper<TaskTemplate> {

    Page<TaskTemplate> selectPage(Page<TaskTemplate> page, @Param("ew") Wrapper<TaskTemplate> queryWrapper, @Param("dataScope") DataScopeParam dataScopeParam);
    /**
     * 查询当前置顶的模板的排序号
     *
     * @return 排序号
     */
    @Select("SELECT MAX(sort_num) FROM biz_task_template WHERE sort_num IS NOT NULL")
    Long selectTopSortNum();

    /**
     * 查询当前置顶的模板
     *
     * @return 置顶的模板
     */
    @Select("SELECT * FROM biz_task_template WHERE sort_num IS NOT NULL ORDER BY sort_num DESC LIMIT 1")
    TaskTemplate selectTopTemplate();


}
