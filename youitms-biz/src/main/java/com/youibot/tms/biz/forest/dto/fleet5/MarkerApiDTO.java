package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@ApiModel(value = "MarkerApiDTO",description = "点位详情")
public class MarkerApiDTO {

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    @ApiModelProperty(value = "角度", position = 5)
    @Range(min = -180,max = 180)
    private Double angle;

    @ApiModelProperty(value = "类型 ChargingMarker:充电点, NavigationMarker:导航点, WorkMarker:工作点", position = 6)
    private String type;

    @ApiModelProperty(value = "地图编码", position = 7)
    private String vehicleMapCode;

    @ApiModelProperty(value = "是否可泊车, 0:不可泊车，1：可泊车", position = 8)
    private Integer isPark;

    @ApiModelProperty(value = "是否可避让 1：可避让 0：不可避让", position = 8)
    private Integer isAvoid;

    @ApiModelProperty(value = "路网点类型, 0:交叉路网点，1：普通路网点", position = 9)
    private Integer networkMarkerType;

    @ApiModelProperty(value = "充电点对接类型，ReflectorPoint:反光条特征对接点, Vpoint:V型特征对接点", position = 10)
    private String dockingType;

    @ApiModelProperty(value = "对接方式 1、车头对接  2、车尾对接", position = 11)
    private Integer dockingDirection;

    @ApiModelProperty(value = "点位信息", position = 12)
    private List<MarkerInfoApiDTO> markInfos;

    @ApiModelProperty(value = "点位扩展参数", position = 13)
    private MarkerParam params;
}
