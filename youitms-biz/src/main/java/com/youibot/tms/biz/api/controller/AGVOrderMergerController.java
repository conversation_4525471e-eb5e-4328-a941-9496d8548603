package com.youibot.tms.biz.api.controller;

import com.youibot.tms.biz.thread.AGVOrderMergerThread;

import com.youibot.tms.common.core.dto.ResultDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * AGV订单合并控制器
 *
 * <AUTHOR>
 */
@Api(tags = "AGV订单合并相关接口")
@RestController
@RequestMapping("/api/v1/biz/agvOrderMerger")
public class AGVOrderMergerController {

    @Lazy
    @Autowired
    private AGVOrderMergerThread agvOrderMergerThread;

    @GetMapping("/status")
    @ApiOperation(value = "获取AGV订单合并线程状态")
    public ResultDTO<Boolean> getStatus() {
        return ResultDTO.success(agvOrderMergerThread.isRunning());
    }

    @GetMapping("/start")
    @ApiOperation(value = "启动AGV订单合并线程")
    public ResultDTO<String> start() {
        if (agvOrderMergerThread.isRunning()) {
            return ResultDTO.success("AGV订单合并线程已经在运行中");
        }

        agvOrderMergerThread.init();
        return ResultDTO.success("AGV订单合并线程启动成功");
    }

    @GetMapping("/stop")
    @ApiOperation(value = "停止AGV订单合并线程")
    public ResultDTO<String> stop() {
        if (!agvOrderMergerThread.isRunning()) {
            return ResultDTO.success("AGV订单合并线程已经停止");
        }

        agvOrderMergerThread.stopThread();
        return ResultDTO.success("AGV订单合并线程停止命令已发送");
    }
}
