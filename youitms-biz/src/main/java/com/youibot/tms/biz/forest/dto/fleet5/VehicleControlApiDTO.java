package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "VehicleControlApiDTO",description = "机器人控制")
public class VehicleControlApiDTO {

    /**
     * 机器人编码
     */
    @ApiModelProperty(value = "机器人编号", required = true)
    private String vehicleCode;

    /**
     * 调度模式
     * 手动调度：ManualSchedule
     * 自动调度：AutoSchedule
     */
    @ApiModelProperty(value = "调度模式 手动调度：ManualSchedule 自动调度：AutoSchedule", position = 1)
    private String scheduleMode;

    /**
     * 机器人自动充电,0关闭,1开启
     */
    @ApiModelProperty(value = "机器人自动充电,0关闭,1开启", position = 2)
    private Integer autoCharge;

    /**
     * 机器人自动泊车,0关闭,1开启
     */
    @ApiModelProperty(value = "机器人自动泊车,0关闭,1开启", position = 3)
    private Integer autoPark;

    /**
     * 暂停
     * 开启：Open
     * 关闭：Close
     */
    @ApiModelProperty(value = "暂停 开启：Open 关闭：Close", position = 4)
    private String softStop;

    /**
     * 重启：Restart
     * 关机：Shutdown
     * 离场：Departure
     */
    @ApiModelProperty(value = "重启：Restart 关机：Shutdown 离场：Departure", position = 5)
    private String machineOperate;

    /**
     * 控制模式
     * 自动：Auto
     * 手动：Manual
     */
    @ApiModelProperty(value = "控制模式 自动：Auto 手动：Manual", position = 6)
    private String controlMode;



}
