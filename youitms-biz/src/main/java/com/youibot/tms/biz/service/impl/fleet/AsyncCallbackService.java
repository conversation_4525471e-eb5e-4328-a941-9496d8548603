package com.youibot.tms.biz.service.impl.fleet;

import com.youibot.tms.biz.api.dto.TaskFleetCallbackDTO;
import com.youibot.tms.biz.forest.TmsClient;
import com.youibot.tms.biz.forest.dto.MissionWorkParam;
import com.youibot.tms.biz.forest.dto.MissionWorkStatus;
import com.youibot.tms.common.utils.uuid.UUID;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AsyncCallbackService {

    @Resource
    private TmsClient tmsClient;

    @Async
    public void missionCallback(MissionWorkParam param){
        try {
            //1秒后调回调接口，模拟fleet运行时间
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        TaskFleetCallbackDTO data = new TaskFleetCallbackDTO();
        data.setStatus(MissionWorkStatus.SUCCESS);
        data.setMissionWorkId(param.getMissionWorkId());
        tmsClient.missionWorkCallback(param.getCallbackUrl(),data);
    }
}
