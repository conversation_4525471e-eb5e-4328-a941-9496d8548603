package com.youibot.tms.biz.forest.dto;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "MissionWorkDTO 扩展类 ", description = "任务工作")
public class MissionWorkExDTO extends MissionWorkDTO implements Serializable {

	/**
	 * 调度系统不支持根据传入队列id
	 */
	private String tmsGroupId;
	
	private boolean inQuenue;
	/**
	 * @Fields serialVersionUID : mission 扩展类
	 * <AUTHOR>
	 * @date 2021-03-09 06:11:16
	 */

	private static final long serialVersionUID = 1L;

	protected MissionWorkParam missionWorkParam;

	/**
	 * 返回任务结果的状态列表
	 */
	private List<String> endStatusList = new ArrayList<>();


	public void clearEndStatusList(){
		this.endStatusList = null;
	}

	public MissionWorkExDTO(MissionWorkParam missionWorkParam) {
		this();
		
		this.missionWorkParam = missionWorkParam;

	}
	public MissionWorkExDTO(MissionWorkParam missionWorkParam , String tmsGroupId) {
		this(missionWorkParam);
		
		this.setTmsGroupId(tmsGroupId);
		if(Objects.nonNull(missionWorkParam) ) {
			if(StringUtils.isNotBlank(this.getTmsGroupId()) ) {
				if(StringUtils.isNotBlank(this.getId())) {
					missionWorkParam.setMissionWorkId(this.getTmsGroupId()+"@" +this.getId());
				}
				
			}
		}
		if( Objects.nonNull(missionWorkParam) && !StringUtils.contains(missionWorkParam.getMissionWorkId(), this.getId())) {
			missionWorkParam.setMissionWorkId(this.getId());
		}
		this.setId(missionWorkParam.getMissionWorkId());
	}
	public MissionWorkExDTO(MissionWorkDTO missionWorkDTO) {
		this();
		BeanUtils.copyProperties(missionWorkDTO, this);

	}

	public MissionWorkExDTO() {
		super();
		if(StringUtils.isBlank(this.getId())) {
			this.setId(IdUtil.fastUUID());
		}
		
	}



	/**
	 * @Fields autoNext : 是否自动下一个
	 * <AUTHOR>
	 * @date 2021-05-08 02:30:28
	 */
	protected boolean autoNext = true;

	/**
	 * @Fields isLast : 是否是最后一个, 最后一个将删除队列执行下一个请求序列
	 * <AUTHOR>
	 * @date 2021-05-08 02:30:41
	 */
	protected boolean isLast = false;


	/**
	 * @Fields callBackBeanName : 队列实现回调接口
	 * <AUTHOR>
	 * @date 2021-05-18 02:01:46
	 */
	protected String callBackBeanName;

	/**  
	 * @Title: isEnd
	 * @Description: 是否终止状态
	 * @return
	 * <AUTHOR>
	 * @date 2021-05-19 02:48:10 
	 */
	@JsonIgnore
	public boolean isEnd() {
		if(CollectionUtils.isNotEmpty(endStatusList)){
			return endStatusList.contains(this.getStatus());
		}
		return MissionWorkStatus.FAULT.equals(this.getStatus()) ||
				MissionWorkStatus.SUCCESS.equals(this.getStatus())||
				MissionWorkStatus.SHUTDOWN.equals(this.getStatus());
	}
	
	/** 是否完成创建
	 * @return
	 */
	@JsonIgnore
	public boolean isValid() {

		return StringUtils.isNotBlank(this.getId()) && this.getStatus()!=null;
	}
	/**  
	 * @Title: isError
	 * @Description: 任务执行出错
	 * @return
	 * <AUTHOR>
	 * @date 2021-05-19 07:55:53 
	 */
	@JsonIgnore
	public boolean isError() {

		return MissionWorkStatus.FAULT.equals(this.getStatus()) ||
				this.getStatus() == null
				;
	}
	
	/**  
	 * @Title: isSuccess
	 * @Description: 任务执行成功
	 * @return
	 * <AUTHOR>
	 * @date 2021-05-19 07:56:05 
	 */
	@JsonIgnore
	public boolean isSuccess() {
		
		return MissionWorkStatus.SUCCESS.equals(this.getStatus()) && StringUtils.isNotBlank(this.getId())
				;
	}

	@JsonIgnore
	public boolean isWait(){
		return MissionWorkStatus.WAIT.equals(this.getStatus()) && StringUtils.isNotBlank(this.getId());
	}
	
	/**  
	 * @Title: isShutDown
	 * @Description: 任务执行被强行停止
	 * @return
	 * <AUTHOR>
	 * @date 2021-05-19 07:56:16 
	 */
	@JsonIgnore
	public boolean isShutDown() {
		
		return MissionWorkStatus.SHUTDOWN.equals(this.getStatus())
				;
	}



}
