package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "ShapeInfo", description = "外型")
public class ShapeInfo implements Serializable {

    /**  
	 * @Fields serialVersionUID : TODO(描述)
	 * <AUTHOR>
	 * @date 2021-03-09 06:12:44 
	 */  
	
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "机器人长度 单位：mm")
    private Integer length;
    @ApiModelProperty(value = "机器人宽度 单位：mm")
    private Integer width;
    @ApiModelProperty(value = "机器人半径 单位：mm")
    private Integer radius;
}
