package com.youibot.tms.biz.entity;

import com.youibot.tms.biz.enums.TaskNodeAction;
import com.youibot.tms.biz.secs.handler.S66F3CommandHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * FromToRecord的包装类，包含取料位置和放料位置
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class FromToRecordWrapper implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 原始的FromToRecord记录
     */
    private FromToRecord record;
    
    /**
     * 取料位置信息
     */
    private S66F3CommandHandler.Location pickupLocation;
    
    /**
     * 放料位置信息
     */
    private S66F3CommandHandler.Location putdownLocation;
    
    /**
     * 根据FromToRecord和两个位置创建包装类
     * 
     * @param record FromToRecord记录
     * @param pickupLocation 取料位置
     * @param putdownLocation 放料位置
     */
    public FromToRecordWrapper(FromToRecord record, S66F3CommandHandler.Location pickupLocation, S66F3CommandHandler.Location putdownLocation) {
        this.record = record;
        this.pickupLocation = pickupLocation;
        this.putdownLocation = putdownLocation;
    }
    
    /**
     * 根据FromToRecord创建包装类，自动生成取料和放料位置
     * 
     * @param record FromToRecord记录
     * @return 包装类实例
     */
    public static FromToRecordWrapper create(
            FromToRecord record,
            S66F3CommandHandler.Location pickupLocation ,
            S66F3CommandHandler.Location putdownLocation) {
        
        if (record == null) {
            return null;
        }
        

        
        return new FromToRecordWrapper(record, pickupLocation, putdownLocation);
    }
    
    /**
     * 获取记录ID
     * 
     * @return 记录ID
     */
    public Long getRecordId() {
        return record != null ? record.getId() : null;
    }
    
    /**
     * 获取命令ID
     * 
     * @return 命令ID
     */
    public String getCommandId() {
        return record != null ? record.getCommandId() : null;
    }
    
    /**
     * 获取载体ID
     * 
     * @return 载体ID
     */
    public String getCarrierId() {
        return record != null ? record.getCarrierId() : null;
    }
    
    /**
     * 获取起点代码
     * 
     * @return 起点代码
     */
    public String getFromCode() {
        return record != null ? record.getFromCode() : null;
    }
    
    /**
     * 获取终点代码
     * 
     * @return 终点代码
     */
    public String getToCode() {
        return record != null ? record.getToCode() : null;
    }
    
    /**
     * 获取合并模式
     * 
     * @return 合并模式
     */
    public String getMergeModeName() {
        if (record == null || record.getMergeMode() == null) {
            return null;
        }
        return "record.getMergeMode().getModeName()";
    }
    
    /**
     * 获取前一条记录ID
     * 
     * @return 前一条记录ID
     */
    public Long getPrevRecordId() {
        return record != null ? record.getPrevFromRecordId() : null;
    }
    
    /**
     * 获取后一条记录ID
     * 
     * @return 后一条记录ID
     */
    public Long getNextRecordId() {
        return record != null ? record.getNextFromRecordId() : null;
    }
    
    /**
     * 判断是否在合并链中
     * 
     * @return 如果在合并链中返回true，否则返回false
     */
    public boolean isInMergeChain() {
        return record != null && (record.getPrevFromRecordId() != null || record.getNextFromRecordId() != null);
    }
    
    /**
     * 更新取料位置信息
     * 
     * @param mapCode 地图代码
     * @param markerCode 标记代码
     * @param isEnd 是否为结束节点
     * @param isTail 是否为尾部节点
     */
    public void updatePickupLocation(String mapCode, String markerCode, boolean isEnd, boolean isTail) {
        if (pickupLocation != null) {
            pickupLocation.setMapCode(mapCode);
            pickupLocation.setMarkerCode(markerCode);
            pickupLocation.setEnd(isEnd);
            pickupLocation.setTail(isTail);
        }
    }
    
    /**
     * 更新放料位置信息
     * 
     * @param mapCode 地图代码
     * @param markerCode 标记代码
     * @param isEnd 是否为结束节点
     * @param isTail 是否为尾部节点
     */
    public void updatePutdownLocation(String mapCode, String markerCode, boolean isEnd, boolean isTail) {
        if (putdownLocation != null) {
            putdownLocation.setMapCode(mapCode);
            putdownLocation.setMarkerCode(markerCode);
            putdownLocation.setEnd(isEnd);
            putdownLocation.setTail(isTail);
        }
    }
}
