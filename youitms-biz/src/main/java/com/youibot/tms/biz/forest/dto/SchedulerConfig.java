package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "SchedulerConfig", description = "调度阈值配置")
public class SchedulerConfig {

    @ApiModelProperty(value = "ID", position = 0)
    private Long id;

    @ApiModelProperty(value = "低电量值", position = 1)
    private Double lowBatterValue;

    
    @ApiModelProperty(value = "可取消充电电量值", position = 2)
    private Double cancelBatteryValue;

    
    @ApiModelProperty(value = "充电最高电量值", position = 3)
    private Double highBatteryValue;

    
    @ApiModelProperty(value = "空闲充电点分数", position = 4)
    private Double freeChargeScope;

    
    @ApiModelProperty(value = "距离基数占比", position = 5)
    private Double distanceRatio;

    
    @ApiModelProperty(value = "电量基数占比", position = 6)
    private Double batteryValueRatio;

    
    @ApiModelProperty(value = "是否启用泊车调度，0是禁用，1是启用", position = 7)
    private Integer parkSchedulerEnable;

    
    @ApiModelProperty(value = "是否启用充电调度，0是禁用，1是启用", position = 8)
    private Integer chargeSchedulerEnable;

    
    @ApiModelProperty(value = "泊车调度间隔时间，单位是秒", position = 9)
    private Integer parkSchedulerInterval;

    
    @ApiModelProperty(value = "充电调度间隔时间，单位是秒。", position = 10)
    private Integer chargeSchedulerInterval;

    
    @ApiModelProperty(value = "是否启用绕障功能，0是禁用，1是启用", position = 11)
    private Integer blockCheckEnable;

    
    @ApiModelProperty(value = "障碍识别时间，单位是秒", position = 12)
    private Integer blockCheckInterval;

    
    @ApiModelProperty(value = "默认移除障碍（路径权重）时间，单位是秒", position = 13)
    private Integer removeBlockInterval;

    
    @ApiModelProperty(value = "最短充电时间, 单位是秒", position = 14)
    private Integer minimumChargeTime;

    
    @ApiModelProperty(value = "校正充电间隔, 单位是小时", position = 15)
    private Integer correctChargeInterval;

    
    @ApiModelProperty(value = "是否启用作业预分配，0:不启用，1:启用", position = 16)
    private Integer preMissionWorkEnable;

    
    @ApiModelProperty(value = "同时进行校正充电机器人的最大数量", position = 17)
    private Integer maximumCorrectChargeNum;

    
    @ApiModelProperty(value = "作业执行花费时长", position = 18)
    private Integer missionWorkSpendTime;

    
    @ApiModelProperty(value = "任务执行时间基数占比", position = 19)
    private Double timeRatio;

    
    @ApiModelProperty(value = "是否开启跨地图充电，0不启用，1启用", position = 20)
    private Integer acrossChargeEnable;

    
    @ApiModelProperty(value = "是否开启跨地图泊车，0不启用，1启用", position = 21)
    private Integer acrossParkEnable;

    
    @ApiModelProperty(value = "创建时间", position = 22)
    private Date createTime;

    
    @ApiModelProperty(value="更新时间",position = 23)
    private Date updateTime;

}
