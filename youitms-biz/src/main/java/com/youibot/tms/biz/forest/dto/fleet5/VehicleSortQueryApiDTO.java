package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "VehicleSortQueryApiDTO",description = "机器人排序查询对象")
public class VehicleSortQueryApiDTO {

    @ApiModelProperty(value = "点位编号", required = true)
    private String markerCode;

    @ApiModelProperty(value = "机器人编号列表", required = true)
    private List<String> vehicleCodes;

}
