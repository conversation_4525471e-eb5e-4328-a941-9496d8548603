package com.youibot.tms.biz.crontab;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.entity.CommunicationLog;
import com.youibot.tms.biz.entity.Message;
import com.youibot.tms.biz.entity.Task;
import com.youibot.tms.biz.entity.TaskLog;
import com.youibot.tms.biz.entity.TaskNode;
import com.youibot.tms.biz.service.CommunicationLogService;
import com.youibot.tms.biz.service.MessageService;
import com.youibot.tms.biz.service.TaskLogService;
import com.youibot.tms.biz.service.TaskNodeService;
import com.youibot.tms.biz.service.TaskService;
import com.youibot.tms.common.constant.SysConfigKeyConstants;
import com.youibot.tms.common.utils.DateUtils;
import com.youibot.tms.quartz.service.ISysJobLogService;
import com.youibot.tms.system.service.ISysConfigService;
import com.youibot.tms.system.service.ISysLoginLogService;
import com.youibot.tms.system.service.ISysOperLogService;
import com.youibot.tms.workflow.entity.WorkFlowEntity;
import com.youibot.tms.workflow.entity.WorkFlowNodeEntity;
import com.youibot.tms.workflow.service.WorkFlowNodeService;
import com.youibot.tms.workflow.service.WorkFlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 日志类数据清理定时任务
 */
@Slf4j
@Component
public class LogClearCrontab {

    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ISysLoginLogService sysLoginLogService;
    @Resource
    private ISysOperLogService sysOperLogService;
    @Resource
    private ISysJobLogService sysJobLogService;
    @Resource
    private MessageService messageService;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private TaskService taskService;
    @Resource
    private TaskNodeService taskNodeService;
    @Resource
    private CommunicationLogService communicationLogService;


    /**
     * 定时清理日志类数据，每天执行一次
     */
    @Scheduled(fixedRate = 24 * 60 * 60000)
    public void clearLogData() {
        //获取数据保留时长，默认60天
        String dataSaveDaysKey = SysConfigKeyConstants.DATA_SAVE_DAYS;
        String dataSaveDays = sysConfigService.getConfigDefaultValueByKey(dataSaveDaysKey, "60");
        int i = Integer.parseInt(dataSaveDays);
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = dateFormat.format(date);
        String fetureDate = DateUtils.getPastDate(format, i);
        log.info("开始清理：{} 天前的日志数据！", fetureDate);
        try {
            int i1 = sysLoginLogService.removeLoginLog(fetureDate);
            int i2 = sysOperLogService.removeOperLog(fetureDate);
            int i3 = sysJobLogService.removeJobLog(fetureDate);
            log.info("已经清理：{} 条登录日志！{} 条操作日志！{} 条定时任务日志！", i1, i2, i3);
            messageService.remove(Wrappers.<Message>lambdaQuery().lt(Message::getCreateTime, fetureDate));
            taskLogService.remove(Wrappers.<TaskLog>lambdaQuery().lt(TaskLog::getCreateTime, fetureDate));
            taskService.remove(Wrappers.<Task>lambdaQuery().lt(Task::getCreateTime, fetureDate));
            taskNodeService.remove(Wrappers.<TaskNode>lambdaQuery().lt(TaskNode::getCreateTime, fetureDate));
            // 清理通讯日志
            int i4 = communicationLogService.remove(Wrappers.<CommunicationLog>lambdaQuery().lt(CommunicationLog::getCreateTime, fetureDate)) ? 1 : 0;
            log.info("已经清理：{} 条通讯日志！", i4);
            clearWorkFlowHistoryData(fetureDate);
        } catch (Exception e) {
            log.error("清理数据库历史数据发生异常:{}", Throwables.getStackTraceAsString(e));
        }
    }


    @Autowired
    private WorkFlowService workFlowService;
    @Autowired
    private WorkFlowNodeService workFlowNodeService;


    public void clearWorkFlowHistoryData(String pastDate) {
        log.info("清理数据库 {} 前的工作流流程历史数据!", pastDate);
        List<WorkFlowEntity> list = workFlowService.list(Wrappers.<WorkFlowEntity>lambdaQuery().lt(WorkFlowEntity::getCreateTime, pastDate));
        if (list != null && list.size() > 0) {
            List<String> collect = list.parallelStream().map(WorkFlowEntity::getId).collect(Collectors.toList());
            if (collect.size() > 0) {
                workFlowService.remove(Wrappers.<WorkFlowEntity>lambdaQuery().in(WorkFlowEntity::getId, collect));
                workFlowNodeService.remove(Wrappers.<WorkFlowNodeEntity>lambdaQuery().in(WorkFlowNodeEntity::getFlowId, collect));
            }
        }
    }
}
