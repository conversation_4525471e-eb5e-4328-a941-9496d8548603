package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VehicleSimpleStatusApiDTO",description = "机器人精简状态")
public class VehicleSimpleStatusApiDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "机器人编号")
    private String vehicleCode;

    @ApiModelProperty(value = "自动充电,0关闭,1开启")
    private Integer autoCharge;

    @ApiModelProperty(value = "自动泊车,0关闭,1开启")
    private Integer autoPark;

    @ApiModelProperty(value = "连接状态 未连接=disconnect 已连接=connect")
    protected String connectStatus;

    @ApiModelProperty(value = "调度模式 手动=ManualSchedule 自动=AutoSchedule")
    protected String scheduleMode;

}
