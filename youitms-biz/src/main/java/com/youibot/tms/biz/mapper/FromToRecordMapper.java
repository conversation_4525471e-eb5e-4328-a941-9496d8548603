package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.entity.FromToRecord;
import com.youibot.tms.biz.api.dto.TransportEfficiencyDetailDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * from/to搬运记录
 *
 * <AUTHOR>
 * @date 2024-08-30 16:04
 */
@Mapper
public interface FromToRecordMapper extends BaseMapper<FromToRecord> {

    /**
     * 直接在数据库层面统计搬运效率数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param agvCode 机器人编码（可选）
     * @param agvCodes 机器人编码列表（可选）
     * @return 按机器人分组的搬运效率统计数据
     */
    List<TransportEfficiencyDetailDTO> getTransportEfficiencyStatistics(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("agvCode") String agvCode,
            @Param("agvCodes") List<String> agvCodes);
}
