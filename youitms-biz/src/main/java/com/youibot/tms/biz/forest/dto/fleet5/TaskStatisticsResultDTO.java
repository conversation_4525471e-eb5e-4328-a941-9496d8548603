package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 作业统计结果
 * 类名称：TaskStatisticsResultDTO
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@EqualsAndHashCode
@Builder
@ApiModel(value = "TaskStatisticsResultDTO", description = "作业统计结果")
@NoArgsConstructor
@AllArgsConstructor
public class TaskStatisticsResultDTO implements Serializable {

    @ApiModelProperty(value = "机器人编号", position = 1)
    private String agvCode;

    @ApiModelProperty(value = "作业总数量", position = 2)
    private Long workNum;

    @ApiModelProperty(value = "成功作业数量", position = 3)
    private Long successNum;

    @ApiModelProperty(value = "工作时长，单位：秒", position = 4)
    private Long workTime;

    @ApiModelProperty(value = "充电次数", position = 5)
    private Long chargeNum;

    @ApiModelProperty(value = "充电时长，单位：秒", position = 6)
    private Long chargeTime;

//    @ApiModelProperty(value = "泊车次数", position = 7)
//    private Long parkNum;
//
//    @ApiModelProperty(value = "泊车时长，单位：秒", position = 8)
//    private Long parkTime;

    @ApiModelProperty(value = "异常次数", position = 9)
    private Long errorNum;

    @ApiModelProperty(value = "异常时长，单位：秒", position = 10)
    private Long errorTime;

    @ApiModelProperty(value = "空闲时长，单位：秒", position = 11)
    private Long freeTime;

    @ApiModelProperty(value = "在线时长，单位：秒", position = 12)
    private Long onlineTime;

    @ApiModelProperty(value = "断线时长，单位：秒")
    private Long disconnectTime;

}
