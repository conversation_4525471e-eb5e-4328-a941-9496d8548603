package com.youibot.tms.biz.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.youibot.tms.biz.api.dto.*;
import com.youibot.tms.biz.api.request.TaskStatisticsRequest;
import com.youibot.tms.biz.api.request.YearMonthWeek;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.entity.TaskStatistics;
import com.youibot.tms.biz.forest.dto.MissionWorkStatisticDTO;
import com.youibot.tms.biz.mapper.TaskStatisticsMapper;
import com.youibot.tms.biz.service.AgvService;
import com.youibot.tms.biz.service.FleetProxyService;
import com.youibot.tms.biz.service.TaskService;
import com.youibot.tms.biz.service.TaskStatisticsService;
import com.youibot.tms.common.enums.ZoneType;
import com.youibot.tms.common.exception.ServiceException;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.exception.base.CommonErrorCode;
import com.youibot.tms.common.utils.DateUtils;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.common.utils.bean.BeanUtils;
import com.youibot.tms.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.tms.common.constant.SysConfigKeyConstants.SYS_TASK_ATTRIBUTION_DATE_LIMIT;

/**
 * <NAME_EMAIL> on 2023/2/17.
 *
 * <AUTHOR>
 * @date 2023/2/17 17:33
 */
@Slf4j
@Service
public class TaskStatisticsServiceImpl extends ServiceImpl<TaskStatisticsMapper, TaskStatistics> implements TaskStatisticsService {

    @Resource
    private TaskStatisticsMapper taskStatisticsMapper;

    @Resource
    private TaskService taskService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private ISysConfigService sysConfigService;


    @Autowired
    private FleetProxyService fleetProxyService;
    @Resource
    private AgvService agvService;
    /**
     * 要保留的小数点的位数
     */
    private final int num = 1;

    @Override
    public List<TaskStatistics> findListByHour(TaskStatisticsRequest taskStatisticsRequest) {
        if (ToolUtil.isEmpty(taskStatisticsRequest.getAgvCode())) {
            Long zoneId = taskStatisticsRequest.getZoneAreaId();
            ZoneType zoneType = taskStatisticsRequest.getZoneType();
            if (ToolUtil.isNotEmpty(zoneId) && ToolUtil.isNotEmpty(zoneType)) {
                if (ZoneType.AREA.equals(zoneType)) {
                    List<Agv> agvs = agvService.listByZoneAreaId(zoneId);
                    if (agvs != null) {
                        List<String> agvCodes = agvs.stream().map(Agv::getAgvCode).collect(Collectors.toList());
                        return taskStatisticsMapper.findListByHour(taskStatisticsRequest, agvCodes);
                    }
                } else {
                    List<Agv> agvs = agvService.listByZoneStockerId(zoneId);
                    if (agvs != null) {
                        List<String> agvCodes = agvs.stream().map(Agv::getAgvCode).collect(Collectors.toList());
                        return taskStatisticsMapper.findListByHour(taskStatisticsRequest, agvCodes);
                    }
                }
            }
        }
        return taskStatisticsMapper.findListByHour(taskStatisticsRequest, null);
    }

    /**
     * 给数据设置一下归属日期
     *
     * @param taskStatisticsList 任务统计数据查询请求参数
     */
    private void setTaskStatisticsAttributionDate(List<TaskStatistics> taskStatisticsList) {
        String configDefaultValueByKey = sysConfigService.getConfigDefaultValueByKey(SYS_TASK_ATTRIBUTION_DATE_LIMIT, "8");
        int limitTime = Integer.parseInt(configDefaultValueByKey);
        for (TaskStatistics taskStatistics : taskStatisticsList) {
            String dateHour = taskStatistics.getDateHour();
            String[] s = dateHour.split(" ");
            int hour = Integer.parseInt(s[1]);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date;
            try {
                date = simpleDateFormat.parse(s[0]);
            } catch (ParseException e) {
                date = null;
                log.info("异常信息：{}", e.getLocalizedMessage());
            }
            Date parse;
            //limitTime点钟之前的数据是属于昨天的数据
            if (hour < limitTime) {
                try {
                    String pastDate = DateUtils.getPastDate(s[0], 1);
                    parse = simpleDateFormat.parse(pastDate);
                } catch (ParseException e) {
                    parse = null;
                    log.info("异常信息：{}", e.getLocalizedMessage());
                }
            } else {
                parse = date;
            }
            taskStatistics.setAttributionDate(parse);
        }
    }


    @Override
    public List<TaskStatistics> findListByHourToDay(TaskStatisticsRequest taskStatisticsRequest) {
        if (ToolUtil.isEmpty(taskStatisticsRequest.getAgvCode())) {
            Long zoneId = taskStatisticsRequest.getZoneAreaId();
            ZoneType zoneType = taskStatisticsRequest.getZoneType();
            if (ToolUtil.isNotEmpty(zoneId) && ToolUtil.isNotEmpty(zoneType)) {
                if (ZoneType.AREA.equals(zoneType)) {
                    List<Agv> agvs = agvService.listByZoneAreaId(zoneId);
                    if (agvs != null) {
                        List<String> agvCodes = agvs.stream().map(Agv::getAgvCode).collect(Collectors.toList());
                        return taskStatisticsMapper.findListByHourToDay(taskStatisticsRequest, agvCodes);
                    }
                } else {
                    List<Agv> agvs = agvService.listByZoneStockerId(zoneId);
                    if (agvs != null) {
                        List<String> agvCodes = agvs.stream().map(Agv::getAgvCode).collect(Collectors.toList());
                        return taskStatisticsMapper.findListByHourToDay(taskStatisticsRequest, agvCodes);
                    }
                }
            }
        }
        return taskStatisticsMapper.findListByHourToDay(taskStatisticsRequest, null);
    }

    @Override
    public List<TaskStatistics> findListByHourToMonth(TaskStatisticsRequest taskStatisticsRequest) {
        if (ToolUtil.isEmpty(taskStatisticsRequest.getAgvCode())) {
            Long zoneId = taskStatisticsRequest.getZoneAreaId();
            ZoneType zoneType = taskStatisticsRequest.getZoneType();
            if (ToolUtil.isNotEmpty(zoneId) && ToolUtil.isNotEmpty(zoneType)) {
                if (ZoneType.AREA.equals(zoneType)) {
                    List<Agv> agvs = agvService.listByZoneAreaId(zoneId);
                    if (agvs != null) {
                        List<String> agvCodes = agvs.stream().map(Agv::getAgvCode).collect(Collectors.toList());
                        return taskStatisticsMapper.findListByHourToMonth(taskStatisticsRequest, agvCodes);
                    }
                } else {
                    List<Agv> agvs = agvService.listByZoneStockerId(zoneId);
                    if (agvs != null) {
                        List<String> agvCodes = agvs.stream().map(Agv::getAgvCode).collect(Collectors.toList());
                        return taskStatisticsMapper.findListByHourToMonth(taskStatisticsRequest, agvCodes);
                    }
                }
            }
        }
        return taskStatisticsMapper.findListByHourToMonth(taskStatisticsRequest, null);
    }

    /**
     * 异步批量统计历史数据
     *
     * @param days
     */
    @Override
    public void countTaskHistoryData(int days) {
        List<Date> dateList = getDateList(days);
        threadPoolTaskExecutor.submit(() -> {
            for (Date date : dateList) {
                countOrderHistoryByDate(date);
            }
        });
    }

    public List<Date> getDateList(int days) {
        LinkedList<Date> objects = new LinkedList<>();
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format1 = format.format(date);
        List<String> pastDate = DateUtils.getLastWeekDayList(format1, days);
        LinkedList<String> objects1 = new LinkedList<>();
        for (String s : pastDate) {
            for (int i = 0; i < 24; i++) {
                if (i < 10) {
                    objects1.add(s + " 0" + i + ":00:00");
                } else {
                    objects1.add(s + " " + i + ":00:00");
                }
            }
        }
        for (String s : objects1) {
            try {
                Date parse = format.parse(s);
                objects.add(parse);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return objects;
    }

    /**
     * 统计订单数据
     *
     * @param date
     */
    @Override
    public void countOrderHistoryByDate(Date date) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format1 = format.format(date);
            List<TaskStatistics> taskStatisticss = taskService.countTaskByHour(format1);
            for (TaskStatistics taskStatistics : taskStatisticss) {
                if (taskStatistics.getAgvCode() == null) {
                    taskStatistics.setAgvCode("void");
                }
            }
            setTaskStatisticsAttributionDate(taskStatisticss);
            try {
                if (taskStatisticss.size() > 0) {
                    this.saveOrUpdateBatchByMultiId(taskStatisticss);
                }
            } catch (Exception e2) {
                log.error("插入数据发生异常，异常数据：{}，异常信息：{}", taskStatisticss, Throwables.getStackTraceAsString(e2));
            }

        } catch (Exception e) {
            log.error("统计任务数据发生异常，异常信息：{}", Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateBatchByMultiId(List<TaskStatistics> taskStatisticsList) {
        List<TaskStatistics> updates = new ArrayList<>();
        List<TaskStatistics> adds = new ArrayList<>();
        for (TaskStatistics taskStatistics : taskStatisticsList) {
            TaskStatistics taskStatisticsById = taskStatisticsMapper.findTaskStatisticsById(taskStatistics.getAgvCode(), taskStatistics.getDateHour());
            if (ToolUtil.isEmpty(taskStatisticsById)) {
                taskStatistics.setId(IdUtil.getSnowflakeNextId());//生成ID
                adds.add(taskStatistics);
            } else {
                updates.add(taskStatistics);
            }
        }
        log.info("修改条数：{}", updates.size());
        for (TaskStatistics update : updates) {
            if (!ToolUtil.isEmpty(update.getAgvCode()) && !ToolUtil.isEmpty(update.getDateHour())) {
                taskStatisticsMapper.updateByAgvCodeAndDateHour(update);
            } else {
                throw new BusinessException(CommonErrorCode.PARAM_ERROR);
            }
        }
        log.info("新增条数：{}", adds.size());
        if (adds.size() > 0) {
            taskStatisticsMapper.saveBatchByMultiId(adds);
        }
    }

    @Override
    public void batchDeleteByAttributionDate(List<String> attributionDate) {
        taskStatisticsMapper.batchDeleteByAttributionDate(attributionDate);
    }

    public TaskStatistics today() {
        return taskStatisticsMapper.today();
    }

    @Override
    public List<TaskStatistics> findListByMonth(TaskStatisticsRequest taskStatisticsRequest) {
        return taskStatisticsMapper.findListByMonth(taskStatisticsRequest);
    }

    @Override
    public List<TaskStatistics> findListByDay(TaskStatisticsRequest taskStatisticsRequest) {
        return taskStatisticsMapper.findListByDay(taskStatisticsRequest);
    }

    @Override
    public LinkedHashMap<String, Map<String, Integer>> monthlyDataReport(YearMonthWeek yearWeek) {
        LinkedHashMap<String, List<TaskStatistics>> resultMap = getTaskStatisticsByMonthData(yearWeek);
        LinkedHashMap<String, Map<String, Integer>> result = new LinkedHashMap<>();
        Set<Map.Entry<String, List<TaskStatistics>>> entries = resultMap.entrySet();
        for (Map.Entry<String, List<TaskStatistics>> entry : entries) {
            String key = entry.getKey();
            List<TaskStatistics> value = entry.getValue();
            LinkedHashMap<String, Integer> linkedHashMap = new LinkedHashMap<>();
            for (TaskStatistics orderMonthCountVO : value) {
                linkedHashMap.put(orderMonthCountVO.getAgvCode(), orderMonthCountVO.getSuccessNum());
            }
            result.put(key, linkedHashMap);
        }
        return result;
    }

    public LinkedHashMap<String, List<TaskStatistics>> getTaskStatisticsByMonthData(YearMonthWeek yearWeek) {
        if (ToolUtil.isEmpty(yearWeek)) {
            throw new ServiceException("请传入年份和月份！");
        }
        if (ToolUtil.isEmpty(yearWeek.getYear())) {
            throw new ServiceException("请传入年份！");
        }
        if (ToolUtil.isEmpty(yearWeek.getMonth())) {
            throw new ServiceException("请传入月份！");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String month = yearWeek.getMonth() < 10 ? "0" + yearWeek.getMonth() : yearWeek.getMonth() + "";
        String dateStr = yearWeek.getYear() + "-" + month + "-" + "01";
        Date date;
        try {
            date = simpleDateFormat.parse(dateStr);
        } catch (ParseException e) {
            throw new ServiceException("请传入正确的年份和月份！");
        }
        String pastMonthDate = DateUtils.getPastMonthDate(date, 5);
        Date date2;
        try {
            date2 = simpleDateFormat.parse(pastMonthDate);
        } catch (ParseException e) {
            throw new ServiceException("请传入正确的年份和月份！");
        }
        List<Agv> agvList = agvService.list();
        Agv agv1 = new Agv();
        agv1.setAgvCode("total");
        agv1.setAgvName("total");
        agvList.add(agv1);
        LinkedHashMap<String, List<TaskStatistics>> resultMap = new LinkedHashMap<>();
        for (int i = 0; i < 6; i++) {
            String pastMonthDate1 = DateUtils.getFetureMonthDate(date2, i, "yyyy-MM");
            List<TaskStatistics> orderMonthCountVOS1 = new ArrayList<>();
            for (Agv agv : agvList) {
                TaskStatistics orderMonthCountVO = new TaskStatistics();
                orderMonthCountVO.setAgvCode(agv.getAgvCode());
                orderMonthCountVO.setTotal(0);
                orderMonthCountVO.setSuccessNum(0);
                orderMonthCountVO.setAttributionDateStr(pastMonthDate1);
                orderMonthCountVOS1.add(orderMonthCountVO);
            }
            resultMap.put(pastMonthDate1, orderMonthCountVOS1);
        }

        TaskStatisticsRequest taskStatisticsRequest = new TaskStatisticsRequest();
        taskStatisticsRequest.setBeginDate(pastMonthDate);
        String month2 = (yearWeek.getMonth() + 1) < 10 ? "0" + (yearWeek.getMonth() + 1) : (yearWeek.getMonth() + 1) + "";
        String dateStr2 = yearWeek.getYear() + "-" + month2 + "-" + "01";
        taskStatisticsRequest.setEndDate(dateStr2);
        List<TaskStatistics> listByMonth = this.findListByMonth(taskStatisticsRequest);
        for (TaskStatistics count : listByMonth) {
            SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM");
            count.setAttributionDateStr(simpleDateFormat1.format(count.getAttributionDate()));
            List<TaskStatistics> orderCounts = resultMap.get(count.getAttributionDateStr());
            if (ToolUtil.isNotEmpty(orderCounts)) {
                for (TaskStatistics orderCount1 : orderCounts) {
                    if (orderCount1.getAgvCode().equals(count.getAgvCode())) {
                        orderCount1.setSuccessNum(count.getSuccessNum());
                    }
                }
            }
        }

        Set<Map.Entry<String, List<TaskStatistics>>> entries = resultMap.entrySet();
        for (Map.Entry<String, List<TaskStatistics>> entry : entries) {
            List<TaskStatistics> value = entry.getValue();
            Integer totalCount = 0;
            TaskStatistics totalCountVO = null;
            for (TaskStatistics orderMonthCountVO : value) {
                if (!"total".equals(orderMonthCountVO.getAgvCode())) {
                    totalCount = totalCount + (orderMonthCountVO.getSuccessNum() == null ? 0 : orderMonthCountVO.getSuccessNum());
                } else {
                    totalCountVO = orderMonthCountVO;
                }
            }
            if (totalCountVO != null) {
                totalCountVO.setTotal(totalCount);
                totalCountVO.setSuccessNum(totalCount);
            }
        }

        return resultMap;
    }

    @Override
    public void exportMonthlyDataReport(YearMonthWeek yearWeek, HttpServletResponse response) {
        LinkedHashMap<String, List<TaskStatistics>> orderCountByMonthData = getTaskStatisticsByMonthData(yearWeek);
        exportOrderByMonth(orderCountByMonthData, response);
    }


    public void exportOrderByMonth(LinkedHashMap<String, List<TaskStatistics>> dataList, HttpServletResponse response) {
        //设置模板名称
        String name1 = "数据报表导出", fileName = null;
        Set<String> strings = dataList.keySet();
        int size = strings.size();
        String[] strings1 = new String[size + 1];
        String[] strings3 = new String[size];
        String[] strings2 = strings.toArray(strings3);
        strings1[0] = "机器人编号";
        for (int i = 0; i < strings2.length; i++) {
            strings1[i + 1] = strings2[i];
        }
        List<List<String>> lists = setExcelHead(strings1);
        List<List<Object>> lists1 = setExportData(dataList);
        //防止中文乱码
        try {
            fileName = URLEncoder.encode(name1, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        setExcelData(lists, lists1, response);

    }

    /**
     * 自定义 Excel 头部栏，表头
     *
     * @return
     */
    public static List<List<String>> setExcelHead(String[] titleNames) {
        List<List<String>> lists = new ArrayList<>();
        for (String name : titleNames) {
            List<String> list = new ArrayList<>();
            list.add(name);
            lists.add(list);
        }
        return lists;
    }


    public List<List<Object>> setExportData(LinkedHashMap<String, List<TaskStatistics>> dataList) {
        Set<String> strings = dataList.keySet();

        Collection<List<TaskStatistics>> values = dataList.values();

        List<TaskStatistics> orderMonthCountVOS1 = values.stream().findFirst().get();

        List<List<Object>> result = new ArrayList<>();
        for (TaskStatistics orderMonthCountVO : orderMonthCountVOS1) {
            List<Object> list = new ArrayList<>();
            list.add(orderMonthCountVO.getAgvCode());
            result.add(list);
        }
        for (List<Object> objects : result) {
            for (String string : strings) {
                List<TaskStatistics> orderMonthCountVOS = dataList.get(string);
                for (TaskStatistics orderMonthCountVO : orderMonthCountVOS) {
                    if (string.equals(orderMonthCountVO.getAttributionDateStr()) && objects.get(0).equals(orderMonthCountVO.getAgvCode())) {
                        objects.add(orderMonthCountVO.getSuccessNum());
                    }
                }
            }
        }
        return result;
    }

    /**
     * 自定义设置导出Excel数据
     *
     * @param head
     * @param dataList
     * @param response
     */
    public static void setExcelData(List<List<String>> head, List<List<Object>> dataList, HttpServletResponse response) {
        //设置响应头
        response.setContentType("applicatio/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {
            EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .head(head)
                    .sheet()
                    .doWrite(dataList);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Override
    public JSONObject getWeekAgvTaskCountBarChart(YearMonthWeek yearWeek) {
        LinkedHashMap<String, Object> jsonArray = new LinkedHashMap<>();
        DateUtils.WeekDate lastWeekDays;
        DateUtils.WeekDate lastWeekDays1;
        DateUtils.WeekDate lastWeekDays2;
        DateUtils.WeekDate lastWeekDays3;
        if (ToolUtil.isEmpty(yearWeek.getWeek()) || ToolUtil.isEmpty(yearWeek.getYear())) {
            //上周的周日和周一
            lastWeekDays = DateUtils.getLastWeekDays();
            //上上周的周日和周一
            lastWeekDays1 = DateUtils.getLastWeekDays(2);
            //上上上周的周日和周一
            lastWeekDays2 = DateUtils.getLastWeekDays(3);
            //上上上上周的周日和周一
            lastWeekDays3 = DateUtils.getLastWeekDays(4);
        } else {
            //传入年的第 getWeek 周
            lastWeekDays = DateUtils.getLastWeekDays(yearWeek.getYear(), yearWeek.getWeek());
            //传入年的第 getWeek 周的上一周
            lastWeekDays1 = DateUtils.getLastWeekDays(yearWeek.getYear(), yearWeek.getWeek() - 1);
            //传入年的第 getWeek 周上上周
            lastWeekDays2 = DateUtils.getLastWeekDays(yearWeek.getYear(), yearWeek.getWeek() - 2);
            //传入年的第 getWeek 周上上上周
            lastWeekDays3 = DateUtils.getLastWeekDays(yearWeek.getYear(), yearWeek.getWeek() - 3);
        }
        String monday = lastWeekDays.getMonday();
        String sunday = lastWeekDays.getSunday();
        String monday1 = lastWeekDays1.getMonday();
        String sunday1 = lastWeekDays1.getSunday();
        String monday2 = lastWeekDays2.getMonday();
        String sunday2 = lastWeekDays2.getSunday();
        String monday3 = lastWeekDays3.getMonday();
        String sunday3 = lastWeekDays3.getSunday();

        int whatWeek = DateUtils.getWhatWeek(sunday);
        int whatWeek1 = DateUtils.getWhatWeek(sunday1);
        int whatWeek2 = DateUtils.getWhatWeek(sunday2);
        //获取往前推四周是本年的第几周
        int whatWeek3 = DateUtils.getWhatWeek(sunday3);
        //////////////////////////////////////////////////////////////////////////////////////////////////////////
        TaskStatisticsRequest taskStatisticsRequest = new TaskStatisticsRequest();
        taskStatisticsRequest.setBeginDate(monday3);
        //由于统计的数据是从8点到第二天8点统计的，那么此时得把截止时间推后一天
        taskStatisticsRequest.setEndDate(sunday);
        taskStatisticsRequest.setType(TaskStatisticsRequest.DateType.DAY);
        List<Agv> agvList = agvService.list();//查询所有的机器人
        log.info("传入的开始时间：{}，结束时间：{}", taskStatisticsRequest.getBeginDate(), taskStatisticsRequest.getEndDate());
        List<TaskStatistics> listByDay = this.findListByDay(taskStatisticsRequest);
        HashMap<String, List<TaskStatistics>> missionWorkStatistic = new HashMap<>();
        for (TaskStatistics orderCount : listByDay) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String format = simpleDateFormat.format(orderCount.getAttributionDate());
            Object o = missionWorkStatistic.get(format);
            if (o == null) {
                missionWorkStatistic.put(format, new ArrayList<>());
            }
        }
        log.info("数据列表：{}", JSON.toJSONString(missionWorkStatistic));
        for (TaskStatistics taskStatistics : listByDay) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String format = simpleDateFormat.format(taskStatistics.getAttributionDate());
            List<TaskStatistics> taskStatisticsList = missionWorkStatistic.get(format);
            if (taskStatisticsList != null) {
                TaskStatistics taskStatistics1 = new TaskStatistics();
                taskStatistics1.setAgvCode(getAgvNameByCode(agvList, taskStatistics.getAgvCode()));
                taskStatistics1.setTotal(taskStatistics.getSuccessNum());
//                taskStatistics1.setTotal(taskStatistics.getTotal());
                taskStatisticsList.add(taskStatistics1);
            }
        }
        /////////////////////////////////////////////////////////////////////////////////////////////////////////
        //前一周的所有日期
        List<String> lastWeekDayList = DateUtils.getLastWeekDayList(monday);
        //前两周的所有日期
        List<String> lastWeekDayList1 = DateUtils.getLastWeekDayList(monday1);
        //前三周的所有日期
        List<String> lastWeekDayList2 = DateUtils.getLastWeekDayList(monday2);
        //前四周的所有日期
        List<String> lastWeekDayList3 = DateUtils.getLastWeekDayList(monday3);
        log.info("前一周的所有日期：{}", JSON.toJSONString(lastWeekDayList));
        log.info("前两周的所有日期：{}", JSON.toJSONString(lastWeekDayList1));
        log.info("前三周的所有日期：{}", JSON.toJSONString(lastWeekDayList2));
        log.info("前四周的所有日期：{}", JSON.toJSONString(lastWeekDayList3));
        //前一周每辆车的每天的任务总据
        TreeMap<String, Object> objects = new TreeMap<>();
        //前一周的每辆车的一周的任务总数
        Map<String, Integer> map = new LinkedHashMap<>();
        //前两周的每辆车的一周的任务总数
        Map<String, Integer> map1 = new LinkedHashMap<>();
        //前三周的每辆车的一周的任务总数
        Map<String, Integer> map2 = new LinkedHashMap<>();
        //前四周的每辆车的一周的任务总数
        Map<String, Integer> map3 = new LinkedHashMap<>();

        List<TaskStatistics> listSort = sortAgvList(agvList);
        for (TaskStatistics taskStatistics : listSort) {
            String agvCode = taskStatistics.getAgvCode();
            map.put(agvCode, 0);
            map1.put(agvCode, 0);
            map2.put(agvCode, 0);
            map3.put(agvCode, 0);
        }
        for (String s : lastWeekDayList) {
            //获取每天的机器人的任务数量
            LinkedHashMap<String, Object> jsonObject = new LinkedHashMap<>();
            for (TaskStatistics taskStatistics : listSort) {
                String agvCode = taskStatistics.getAgvCode();
                jsonObject.put(agvCode, 0);
            }
            objects.put(s, jsonObject);
        }
        //将从数据库拿到的数据进行遍历和计算
        Set<Map.Entry<String, List<TaskStatistics>>> entrySet = missionWorkStatistic.entrySet();
        for (Map.Entry<String, List<TaskStatistics>> stringObjectEntry : entrySet) {
            String key = stringObjectEntry.getKey();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = "";
            try {
                Date parse = sdf.parse(key);
                dateStr = sdf.format(parse);
            } catch (ParseException e) {
                throw new ServiceException("将fleet返回的日期转换成年月日 yyyy-MM-dd 格式发生异常!异常信息：" + Throwables.getStackTraceAsString(e));
            }
            //获取上周每天每辆机器人的任务数量
            List<TaskStatistics> values = stringObjectEntry.getValue();
            List<TaskStatistics> list = sortAgvList(agvList);
            for (TaskStatistics value : values) {
                String agvCode = value.getAgvCode();
                for (TaskStatistics taskStatistics : list) {
                    String agvCode1 = taskStatistics.getAgvCode();
                    if (agvCode1.equals(agvCode)) {
                        BeanUtils.copyBeanProp(value, taskStatistics);
                    }
                }
            }
            stringObjectEntry.setValue(list);
            //获取每天的机器人的任务数量
            LinkedHashMap<String, Object> jsonObject = new LinkedHashMap<>();
            for (TaskStatistics taskStatistics : list) {
                String agvCode = taskStatistics.getAgvCode();
                jsonObject.put(agvCode, 0);
            }
            if (lastWeekDayList.contains(dateStr)) {
                for (TaskStatistics taskStatistics : list) {
                    String agvCode = taskStatistics.getAgvCode();
                    Integer integer = map.get(agvCode);
                    int workNum = integer + taskStatistics.getTotal();
                    map.put(agvCode, workNum);
                    jsonObject.put(agvCode, taskStatistics.getTotal());
                    objects.put(dateStr, jsonObject);
                }
            }
            setAgvTaskData(lastWeekDayList1, dateStr, list, map1);
            setAgvTaskData(lastWeekDayList2, dateStr, list, map2);
            setAgvTaskData(lastWeekDayList3, dateStr, list, map3);
        }
        //往前推四周的数据，用于排序，需求要求一定要把总数放在最前面
        LinkedHashMap<String, Object> linkedHashMap3 = new LinkedHashMap<>();
        //往前推三周的数据，用于排序，需求要求一定要把总数放在最前面
        LinkedHashMap<String, Object> linkedHashMap2 = new LinkedHashMap<>();
        //往前推二周的数据，用于排序，需求要求一定要把总数放在最前面
        LinkedHashMap<String, Object> linkedHashMap1 = new LinkedHashMap<>();
        //往前推一周的数据，用于排序，需求要求一定要把总数放在最前面
        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
        sortDateAgvTask(map3, linkedHashMap3);
        sortDateAgvTask(map2, linkedHashMap2);
        sortDateAgvTask(map1, linkedHashMap1);
        sortDateAgvTask(map, linkedHashMap);

        LinkedHashMap<String, Object> resObjects1 = new LinkedHashMap<>();

        jsonArray.put("W" + whatWeek3, linkedHashMap3);
        jsonArray.put("W" + whatWeek2, linkedHashMap2);
        jsonArray.put("W" + whatWeek1, linkedHashMap1);
        Set<Map.Entry<String, Object>> entries = objects.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            LinkedHashMap<String, Object> value = (LinkedHashMap<String, Object>) entry.getValue();
            Set<Map.Entry<String, Object>> entrySet1 = value.entrySet();
            int dayTotal = 0;
            for (Map.Entry<String, Object> stringObjectEntry : entrySet1) {
                dayTotal = dayTotal + Integer.parseInt(stringObjectEntry.getValue().toString());
            }
            value.put("total", dayTotal);
            LinkedHashMap<String, Object> objectObjectLinkedHashMap = new LinkedHashMap<>();
            objectObjectLinkedHashMap.put("total", dayTotal);
            for (Map.Entry<String, Object> stringObjectEntry : entrySet1) {
                objectObjectLinkedHashMap.put(stringObjectEntry.getKey(), stringObjectEntry.getValue());
            }
            jsonArray.put(entry.getKey(), objectObjectLinkedHashMap);
            resObjects1.put(entry.getKey(), objectObjectLinkedHashMap);
        }
        jsonArray.put("W" + whatWeek, linkedHashMap);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", "00000");
        jsonObject.put("msg", "SUCCESS");
        jsonObject.put("data", jsonArray);

        JSONObject res1 = new JSONObject();
        res1.put("week", "W" + whatWeek);
        res1.put("data", resObjects1);
        jsonObject.put("data1", res1);
        LinkedHashMap<String, Object> res2 = new LinkedHashMap<>();
        res2.put("W" + whatWeek3, linkedHashMap3);
        res2.put("W" + whatWeek2, linkedHashMap2);
        res2.put("W" + whatWeek1, linkedHashMap1);
        res2.put("W" + whatWeek, linkedHashMap);
        jsonObject.put("data2", res2);
        jsonObject.put("agvSort", listSort);
        return jsonObject;
    }

    /**
     * 获取机器人机器人名称，将机器人编号转换成机器人名称
     *
     * @param agvList 机器人列表
     * @param agvCode 机器人编号
     * @return String 返回机器人的序列号
     */
    public String getAgvNameByCode(List<Agv> agvList, String agvCode) {
        String serialNumber = "";
        for (Agv agv : agvList) {
            if (agvCode.equals(agv.getAgvCode())) {
                if (!ToolUtil.isEmpty(agv.getAgvName())) {
                    serialNumber = agv.getAgvName();
                } else {
                    serialNumber = agv.getAgvCode();
                }
            }
        }
        return serialNumber;
    }

    /**
     * 给机器人排序
     *
     * @param agvList 机器人列表
     * @return
     */
    public List<TaskStatistics> sortAgvList(List<Agv> agvList) {
        //获取机器人顺序
        List<TaskStatistics> listSort = new LinkedList<>();
        for (Agv agv : agvList) {
            TaskStatistics taskStatistics = new TaskStatistics();
            taskStatistics.setAgvCode(agv.getAgvName());
            listSort.add(taskStatistics);
        }
        return listSort;
    }

    /**
     * 统计机器人的任务总数
     *
     * @param lastWeekDayList 当前周的日期列表
     * @param dateStr         当前的日期
     * @param list            所有机器人任务的数据列表
     * @param map             当前周任务数量总数结果
     */
    public void setAgvTaskData(List<String> lastWeekDayList, String dateStr, List<TaskStatistics> list, Map<String, Integer> map) {
        if (lastWeekDayList.contains(dateStr)) {
            for (TaskStatistics taskStatistics : list) {
                String agvCode = taskStatistics.getAgvCode();
                Integer integer = map.get(agvCode);
                int workNum = integer + taskStatistics.getTotal();
                map.put(agvCode, workNum);
            }
        }
    }

    /**
     * 统计每个周期的所有机器人的任务总数，并且给最终的结果进行排序，将所有机器人的任务总数放在最前面
     *
     * @param map           每台机器人的任务数量
     * @param linkedHashMap 排序后的结果
     */
    public void sortDateAgvTask(Map<String, Integer> map, LinkedHashMap<String, Object> linkedHashMap) {
        Set<Map.Entry<String, Integer>> mapEntries = map.entrySet();
        // 往前推一周的数据
        //用于计算总任务数
        int mapTotal = 0;
        for (Map.Entry<String, Integer> stringIntegerEntry : mapEntries) {
            mapTotal = mapTotal + stringIntegerEntry.getValue();
        }
        map.put("total", mapTotal);
        linkedHashMap.put("total", mapTotal);
        for (Map.Entry<String, Integer> stringIntegerEntry : mapEntries) {
            linkedHashMap.put(stringIntegerEntry.getKey(), stringIntegerEntry.getValue());
        }
    }


    @Override
    public void exportWeekAgvTaskCountBarChart(YearMonthWeek yearWeek, HttpServletResponse response) {
        JSONObject weekAgvTaskCountBarChart = getWeekAgvTaskCountBarChart(yearWeek);
        JSONObject data1 = (JSONObject) weekAgvTaskCountBarChart.get("data1");
        LinkedHashMap<String, Object> data2 = (LinkedHashMap) data1.get("data");
        LinkedHashMap<String, List<TaskStatistics>> dataMap = new LinkedHashMap<>();
        Set<Map.Entry<String, Object>> entries = data2.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            LinkedHashMap<String, Object> value = (LinkedHashMap<String, Object>) entry.getValue();
            ArrayList<TaskStatistics> orderWeekExcelDTOS = new ArrayList<>();
            for (Map.Entry<String, Object> stringObjectEntry : value.entrySet()) {
                TaskStatistics taskStatistics = new TaskStatistics();
                taskStatistics.setAgvCode(stringObjectEntry.getKey());
                taskStatistics.setTotal(Integer.parseInt(stringObjectEntry.getValue().toString()));
                taskStatistics.setAttributionDateStr(entry.getKey());
                orderWeekExcelDTOS.add(taskStatistics);
            }
            dataMap.put(entry.getKey(), orderWeekExcelDTOS);
        }
        try {
            exportOrderByMonth(dataMap, response);
        } catch (Exception e) {
            log.error("导出发生异常：{}", Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public Map<String, Object> getTaskStatisticsLineChart(TaskStatisticsRequest agvDataStatisticRequest) {
        LinkedHashMap<String, Object> result = new LinkedHashMap<>();
        try {
            List<TaskStatistics> listByDay = null;
            List<String> keyList = getKeyList(agvDataStatisticRequest);
            for (String s : keyList) {
                JSONObject resultData = new JSONObject();
                resultData.put("successNum", 0);
                resultData.put("workNum", 0);
                resultData.put("failNum", 0);
                resultData.put("notCompleteNum", 0);
                result.put(s, resultData);
            }
//            log.info("接收到的参数为：{}", JSON.toJSON(agvDataStatisticRequest));
            String formatStr = "yyyy-MM-dd 00:00:00";
            if (TaskStatisticsRequest.DateType.HOUR.equals(agvDataStatisticRequest.getType())) {
                listByDay = this.findListByHour(agvDataStatisticRequest);
                formatStr = "yyyy-MM-dd HH:00:00";
            } else if (TaskStatisticsRequest.DateType.DAY.equals(agvDataStatisticRequest.getType())) {
                listByDay = this.findListByHourToDay(agvDataStatisticRequest);
                formatStr = "yyyy-MM-dd";
            } else if (TaskStatisticsRequest.DateType.MONTH.equals(agvDataStatisticRequest.getType())) {
                listByDay = this.findListByHourToMonth(agvDataStatisticRequest);
                formatStr = "yyyy-MM-01";
            }
            if (listByDay == null) {
                throw new BusinessException(CommonErrorCode.PARAM_ERROR);
            }

            Map<String, List<TaskStatistics>> map = new LinkedHashMap<>();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formatStr);
            for (TaskStatistics taskStatistics : listByDay) {
                if (taskStatistics.getAttributionDate() == null) {
                    continue;
                }
                String format = "";
                if (TaskStatisticsRequest.DateType.HOUR.equals(agvDataStatisticRequest.getType())) {
                    format = taskStatistics.getDateHour() + DateUtils.HOUR_SUFFIX;
                } else {
                    format = simpleDateFormat.format(taskStatistics.getAttributionDate());
                }
                List<TaskStatistics> o = map.get(format);
                if (o == null) {
                    o = new ArrayList<>();
                }
                o.add(taskStatistics);
                map.put(format, o);
            }

            Set<Map.Entry<String, List<TaskStatistics>>> entrySet = map.entrySet();

            //匹配对应的时间段的数据
            for (Map.Entry<String, List<TaskStatistics>> stringObjectEntry : entrySet) {
                List<TaskStatistics> value = stringObjectEntry.getValue();
                //任务成功数量
                int successNum = 0;
                //任务总数
                int workNum = 0;
                //任务总数
                int failNum = 0;
                //未完成任务
                int notCompleteNum = 0;
                //单位内的时长统计
                for (TaskStatistics taskStatistics : value) {
                    //成功总数量
                    successNum = successNum + taskStatistics.getSuccessNum();
                    //任务总数亮
                    workNum = workNum + taskStatistics.getTotal();
                    //失败的任务数量
                    failNum = failNum + taskStatistics.getFailNum();
                    //未完成任务数量
                    notCompleteNum = notCompleteNum + taskStatistics.getNotCompleteNum();
                }
                JSONObject resultData = new JSONObject();
                resultData.put("successNum", successNum);
                resultData.put("workNum", workNum);
                resultData.put("failNum", failNum);
                resultData.put("notCompleteNum", notCompleteNum);
                result.put(getDateKeyByType(stringObjectEntry.getKey(), agvDataStatisticRequest.getType()), resultData);
            }
        } catch (Exception e) {
            log.error("发生异常！异常信息：{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(CommonErrorCode.SYSTEM_ERROR);
        }
        return result;
    }


    private String getDateKeyByType(String dateTime, TaskStatisticsRequest.DateType type) {
        if (com.youibot.tms.common.utils.StringUtils.isNotEmpty(dateTime)) {
            if (TaskStatisticsRequest.DateType.DAY.equals(type) || TaskStatisticsRequest.DateType.MONTH.equals(type)) {
                return dateTime.substring(0, 10);//只截取前10位，包含"yyyy-MM-dd"，不要后面的时分秒
            }
        }
        return dateTime;
    }

    /**
     * 获取任务折线图的X轴
     *
     * @param agvDataStatisticRequest 请求参数
     * @return
     */
    public List<String> getKeyList(TaskStatisticsRequest agvDataStatisticRequest) {
        List<String> keyList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat simpleDateFormatMonthDay = new SimpleDateFormat("yyyy-MM-dd");
        String endTime = agvDataStatisticRequest.getEndDate();
        String startTime = agvDataStatisticRequest.getBeginDate();
        Date startDate = null;
        Date endDate = null;
        try {
            endDate = sdf.parse(endTime);
            startDate = sdf.parse(startTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (startDate == null || endDate == null) {
            throw new BusinessException(CommonErrorCode.DATE_FORMAT_ERROR);
        }
        if (TaskStatisticsRequest.DateType.HOUR.equals(agvDataStatisticRequest.getType())) {
            String formatEndDateDate = simpleDateFormatMonthDay.format(endDate);
            String yearMonthDate = simpleDateFormatMonthDay.format(startDate);
            long endTimeLong = endDate.getTime();
            long startTimeLong = startDate.getTime();
            long dayCount = (endTimeLong - startTimeLong) / (1000 * 60 * 60);

            for (int i = 0; i <= dayCount; i++) {
                int i1 = 8 + i;
                if (i1 >= 24) {
                    yearMonthDate = formatEndDateDate;
                    i1 = i1 - 24;
                }
                String s = "";
                if (i1 < 10) {
                    s = yearMonthDate + " 0" + i1 + ":00:00";
                } else {
                    s = yearMonthDate + " " + i1 + ":00:00";
                }
                keyList.add(s);
            }
        } else if (TaskStatisticsRequest.DateType.DAY.equals(agvDataStatisticRequest.getType())) {
            List<Date> datesBetweenUsingJava7 = DateUtils.getDatesBetweenUsing(startDate, endDate);
            for (Date date : datesBetweenUsingJava7) {
                String format1 = simpleDateFormatMonthDay.format(date);
                keyList.add(format1);
            }
        } else if (TaskStatisticsRequest.DateType.MONTH.equals(agvDataStatisticRequest.getType())) {
            List<String> monthBetween = DateUtils.getMonthBetween(startDate, endDate);
            for (String s : monthBetween) {
                s = s + "-01";
                keyList.add(s);
            }
        }
        log.info("返回的列：{}", keyList);
        return keyList;
    }


    @Override
    public Map<String, Object> getUpTimeLineChart(TaskStatisticsRequest agvDataStatisticRequest) {
        Map<String, Object> result = new LinkedHashMap<>();
        try {

            List<String> keyList = getKeyList(agvDataStatisticRequest);
            for (String s : keyList) {
                MissionWorkStatisticDTO resultData = new MissionWorkStatisticDTO();
                result.put(s, resultData);
            }
            //查询调度系统统计的机器人任务状态
            List<MissionWorkStatisticDTO> missionWorkStatisticDTOList = getMissionStatisticAfter(agvDataStatisticRequest);
            for (MissionWorkStatisticDTO missionWorkStatisticDTO : missionWorkStatisticDTOList) {
                MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatisticVO = countTime(missionWorkStatisticDTO.getMissionWorkStatistics(), agvDataStatisticRequest, missionWorkStatisticDTO.getDateTime());

                result.put(missionWorkStatisticDTO.getDateTimeByType(agvDataStatisticRequest.getType()), missionWorkStatisticVO);
            }
        } catch (Exception e) {
            log.error("发生异常！异常信息：{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED, "/api/v3/statistic/getMissionWorkStatistic");
        }
        return result;
    }


    /**
     * 根据传入的统计维度类型，获取对应的总时间长度
     *
     * @param agvDataStatisticRequest
     * @param date
     * @return
     * @throws ParseException
     */
    public long getTotalTime(TaskStatisticsRequest agvDataStatisticRequest, String date) throws ParseException {
        TaskStatisticsRequest.DateType type = agvDataStatisticRequest.getType();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date queryEndDate = dateFormat.parse(agvDataStatisticRequest.getEndDate());
        Date dataBelongDate = dateFormat.parse(date);
        long res = 0L;
        if (TaskStatisticsRequest.DateType.HOUR.equals(type)) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(dataBelongDate);
            Date dateBelongStartTime = cal.getTime();
            cal.add(Calendar.HOUR_OF_DAY, 1);
            Date dateBelongEndTime = cal.getTime();
            //如果查询的结束时间 小于 统计区间时间
            //如果查询的结束时间是 08:40 ,而统计区间为08:00-09:00
            //则总的时间只能算40分钟，不能算为60分钟
            if (queryEndDate.getTime() < dateBelongEndTime.getTime()) {
                res = (queryEndDate.getTime() - dateBelongStartTime.getTime()) / 1000;
            } else {
                res = 60 * 60;
            }

        } else if (TaskStatisticsRequest.DateType.DAY.equals(type) || TaskStatisticsRequest.DateType.NONE.equals(type)) {
            res = 24 * 60 * 60;
        } else if (TaskStatisticsRequest.DateType.MONTH.equals(type)) {
            res = DateUtils.getMonthDays(date) * 24 * 60 * 60;
        }
        return res;
    }

    /**
     * 获取fleet的机器人统计数据
     *
     * @param agvDataStatisticRequest 请求参数
     * @return
     */
    public List<MissionWorkStatisticDTO> getMissionStatisticAfter(TaskStatisticsRequest agvDataStatisticRequest) {
        //查询调度系统统计的机器人任务状态
        return fleetProxyService.getMissionWorkStatistic(agvDataStatisticRequest);
    }

    public MissionWorkStatisticDTO.MissionWorkStatistic countTime(List<MissionWorkStatisticDTO.MissionWorkStatistic> missionWorkStatistics, TaskStatisticsRequest agvDataStatisticRequest, String date) {
        //最终的返回结果
        MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatisticVO = new MissionWorkStatisticDTO.MissionWorkStatistic();
        //单位内的时长统计
        //充电时长
        long chargeTime = 0;
        //异常时长（fault）
        long errorTime = 0;
        //空闲时长
        long freeTime = 0;
        //任务总时长
        long workTime = 0;
        //泊车时长
        long parkTime = 0;
        //离线时间
        long offlineTime = 0;
        long totalTime = 0;
        try {
            totalTime = getTotalTime(agvDataStatisticRequest, date);
        } catch (ParseException e) {
            log.info("时间格式化异常，异常参数：{}", date);
            e.printStackTrace();
        }
        for (MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatistic : missionWorkStatistics) {
            //充电时长
            long chargeTime2 = missionWorkStatistic.getChargeTime();
            //异常时长（fault）
            long errorTime2 = missionWorkStatistic.getErrorTime();
            //空闲时长
            long freeTime2 = missionWorkStatistic.getFreeTime();
            //任务总时长
            long workTime2 = missionWorkStatistic.getWorkTime();
            //泊车时长
            long parkTime2 = missionWorkStatistic.getParkTime();
            //充电时长
            chargeTime = chargeTime + chargeTime2;
            //异常时长（fault）
            errorTime = errorTime + errorTime2;
            //空闲时长
            freeTime = freeTime + freeTime2;
            //任务总时长
            workTime = workTime + workTime2;
            //泊车时长
            parkTime = parkTime + parkTime2;
            long onlineTime2 = chargeTime2 + errorTime2 + freeTime2 + workTime2 + parkTime2;
            //离线时间
            offlineTime = offlineTime + (totalTime - onlineTime2);
        }
        missionWorkStatisticVO.setOfflineTime(offlineTime);
        missionWorkStatisticVO.setWorkTime(workTime);
        missionWorkStatisticVO.setParkTime(parkTime);
        missionWorkStatisticVO.setChargeTime(chargeTime);
        missionWorkStatisticVO.setFreeTime(freeTime);
        missionWorkStatisticVO.setErrorTime(errorTime);
        return missionWorkStatisticVO;
    }

    /**
     * 获取总共有多少台在线的机器人
     *
     * @return
     */
    public int getAgvNum() {
        List<AgvDTO> fleetAgvDTOS = fleetProxyService.listAllAgvs();
        if (fleetAgvDTOS != null) {
            log.info("获取到fleet查询到的所有机器人数量：{}", fleetAgvDTOS.size());
            return fleetAgvDTOS.size();
        } else {
            return 0;
        }
    }


    public Map getUtilizationLineChart(TaskStatisticsRequest agvDataStatisticRequest) {
        LinkedHashMap<String, Object> result = new LinkedHashMap<>();
        try {
            long agvNum = 1;
            if (ToolUtil.isEmpty(agvDataStatisticRequest.getAgvCode())) {
                agvNum = getAgvNum();
            }
            List<String> keyList = getKeyList(agvDataStatisticRequest);
            for (String s : keyList) {
                JSONObject resultData = new JSONObject();
                resultData.put("uu", 0);
                resultData.put("au", 0);
                resultData.put("uu-1", 0);
                result.put(s, resultData);
            }
            //查询调度系统统计的机器人任务状态
            List<MissionWorkStatisticDTO> missionWorkStatisticDTOList = getMissionStatisticAfter(agvDataStatisticRequest);
            for (MissionWorkStatisticDTO missionWorkStatisticDTO : missionWorkStatisticDTOList) {
                MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatisticVO = countTime(missionWorkStatisticDTO.getMissionWorkStatistics(), agvDataStatisticRequest, missionWorkStatisticDTO.getDateTime());
                //计算公式 Down = offline + fault + Alarm;
                //计算公式 Idle 空闲时间（freeTime） +泊车时间;
                //最新算法「利用率」UU % = Run / (Total - 充电)
                //最新算法「可利用率」AU % = (Run + Idle) / (Total - 充电)
                //最新算法「可利用率」UU_1 % = Run / （Run+Idle）
                LinkedHashMap<String, Object> resultData = new LinkedHashMap<>();
                long totalTime = getTotalTime(agvDataStatisticRequest, missionWorkStatisticDTO.getDateTime());
                //乘以机器人数量获取总时间
                totalTime = totalTime * (agvNum == 0 ? 1 : agvNum);
//                long offlineTime = totalTime - onlineTime;//总时间减去在线时间得到离线时间
                double a = 0;
                if ((totalTime - missionWorkStatisticVO.getChargeTime()) > 0) {
                    a = ToolUtil.formatDouble(((double) missionWorkStatisticVO.getWorkTime() / (totalTime - missionWorkStatisticVO.getChargeTime())) * 100, num);
                }
                double b = 0;
                if ((totalTime - missionWorkStatisticVO.getChargeTime()) > 0) {
                    b = ToolUtil.formatDouble((((double) missionWorkStatisticVO.getWorkTime() + missionWorkStatisticVO.getParkTime() + missionWorkStatisticVO.getFreeTime()) / (totalTime - missionWorkStatisticVO.getChargeTime())) * 100, num);
                }
                double c = 0;
                if (totalTime - missionWorkStatisticVO.getChargeTime() > 0) {
                    c = ToolUtil.formatDouble((double) missionWorkStatisticVO.getWorkTime() / ((double) (missionWorkStatisticVO.getWorkTime()) + (double) (missionWorkStatisticVO.getParkTime()) + (double) (missionWorkStatisticVO.getFreeTime())) * 100, num);
                }
                resultData.put("uu", a);
                resultData.put("au", b);
                resultData.put("uu-1", c);
                result.put(missionWorkStatisticDTO.getDateTimeByType(agvDataStatisticRequest.getType()), resultData);
            }
        } catch (Exception e) {
            log.error("发生异常！异常信息：{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED, "/api/v3/statistic/getMissionWorkStatistic");
        }
        return result;
    }


    public MissionWorkStatisticDTO.MissionWorkStatistic getPieChartData(TaskStatisticsRequest agvDataStatisticRequest) {
        //最终的返回结果
        MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatisticVO = new MissionWorkStatisticDTO.MissionWorkStatistic();
        missionWorkStatisticVO.setAgvCode(agvDataStatisticRequest.getAgvCode());
        missionWorkStatisticVO.setChargeTime(0L);
        missionWorkStatisticVO.setErrorTime(0L);
        missionWorkStatisticVO.setFreeTime(0L);
        missionWorkStatisticVO.setWorkTime(0L);
        missionWorkStatisticVO.setParkTime(0L);
        missionWorkStatisticVO.setOfflineTime(0L);
        //查询调度系统统计的机器人任务状态
        List<MissionWorkStatisticDTO> missionWorkStatisticDTOList = getMissionStatisticAfter(agvDataStatisticRequest);
        for (MissionWorkStatisticDTO missionWorkStatisticDTO : missionWorkStatisticDTOList) {
            MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatisticVO1 = countTime(missionWorkStatisticDTO.getMissionWorkStatistics(), agvDataStatisticRequest, missionWorkStatisticDTO.getDateTime());
            missionWorkStatisticVO.setChargeTime(missionWorkStatisticVO.getChargeTime() + missionWorkStatisticVO1.getChargeTime());
            missionWorkStatisticVO.setErrorTime(missionWorkStatisticVO.getErrorTime() + missionWorkStatisticVO1.getErrorTime());
            missionWorkStatisticVO.setFreeTime(missionWorkStatisticVO.getFreeTime() + missionWorkStatisticVO1.getFreeTime());
            missionWorkStatisticVO.setWorkTime(missionWorkStatisticVO.getWorkTime() + missionWorkStatisticVO1.getWorkTime());
            missionWorkStatisticVO.setParkTime(missionWorkStatisticVO.getParkTime() + missionWorkStatisticVO1.getParkTime());
            missionWorkStatisticVO.setOfflineTime(missionWorkStatisticVO.getOfflineTime() + missionWorkStatisticVO1.getOfflineTime());
        }
        return missionWorkStatisticVO;
    }




    @Override
    public void reportExport(TaskStatisticsRequest taskStatisticsRequest, HttpServletResponse response) {

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        // 运行时间统计数据
        RunTimeExcelDTO runTimeExcelDTO = new RunTimeExcelDTO();
        MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatistic = this.getPieChartData(taskStatisticsRequest);
        if (missionWorkStatistic != null) {
            runTimeExcelDTO = RunTimeExcelDTO.buildRunTimeExcel(taskStatisticsRequest.getBeginDate(), taskStatisticsRequest.getEndDate(), taskStatisticsRequest.getUnit(), missionWorkStatistic);
        }

        // UU&AU统计
        List<UuAuExcelDTO> auExcelList = Lists.newArrayList();
        Map uuResponse = this.getUtilizationLineChart(taskStatisticsRequest);
        LinkedHashMap<String, Object> jsonData = (LinkedHashMap<String, Object>) uuResponse;
        if (jsonData != null) {
            auExcelList = UuAuExcelDTO.buildUuAuExcel(jsonData);
        }

        // 任务统计
        List<OrderExcelDTO> orderExcelList = Lists.newArrayList();
        Map<String, Object> orderResponse = this.getTaskStatisticsLineChart(taskStatisticsRequest);
        LinkedHashMap<String, Object> orderJson = (LinkedHashMap<String, Object>) orderResponse;
        if (orderJson != null) {
            orderExcelList = OrderExcelDTO.buildOrderExcel(orderJson);
        }

        // up统计
        List<UpTimeExcelDTO> upTimeExcelList = Lists.newArrayList();
        Map<String, Object> upResponse = this.getUpTimeLineChart(taskStatisticsRequest);
        LinkedHashMap<String, Object> upTimeJson = (LinkedHashMap<String, Object>) upResponse;
        if (upTimeJson != null) {
            upTimeExcelList = UpTimeExcelDTO.buildUpTimeExcel(upTimeJson);
        }

        try {
            String fileName = URLEncoder.encode("报表导出数据", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
//            // 向sheet0写数据
            WriteSheet runTimeSheet = EasyExcel.writerSheet(0, "运行时间统计").head(RunTimeExcelDTO.class).build();
            excelWriter.write(Lists.newArrayList(runTimeExcelDTO), runTimeSheet);
//            excelWriter.write(Lists.newArrayList(new RunTimeExcelVO()), runTimeSheet);

            exportUuAndAu(taskStatisticsRequest, excelWriter);
//            excelWriter.write(Lists.newArrayList(new UuAuExcelVO()), uuSheet);
//
            WriteSheet orderSheet = EasyExcel.writerSheet(2, "任务统计").head(OrderExcelDTO.class).build();
            excelWriter.write(orderExcelList, orderSheet);
//
            exportUptime(taskStatisticsRequest, excelWriter);
//            excelWriter.write(Lists.newArrayList(new UpTimeExcelVO()), upTimeSheet);


            excelWriter.finish();
        } catch (Exception e) {
            log.error("报表导出异常:{}", e);
        }
    }

    private void exportUuAndAu(TaskStatisticsRequest fleetWorkActionStatisticDto, ExcelWriter excelWriter) {
        try {
            //查询调度系统统计的机器人任务状态
            List<MissionWorkStatisticDTO> missionWorkStatisticDTOS = getMissionStatisticAfter(fleetWorkActionStatisticDto);
            ArrayList<UuAuExcelDTO> uuAuExcelDTOS = new ArrayList<>();
            HashMap<String, Double> stringDoubleHashMap = new HashMap<>();
            HashMap<String, Double> stringDoubleHashMap2 = new HashMap<>();
            for (MissionWorkStatisticDTO missionWorkStatisticDTO : missionWorkStatisticDTOS) {
                long totalTime = getTotalTime(fleetWorkActionStatisticDto, missionWorkStatisticDTO.getDateTime());
                List<MissionWorkStatisticDTO.MissionWorkStatistic> missionWorkStatistics = missionWorkStatisticDTO.getMissionWorkStatistics();
                for (MissionWorkStatisticDTO.MissionWorkStatistic workStatistic : missionWorkStatistics) {
                    UuAuExcelDTO uuAuExcelDTO = new UuAuExcelDTO();
                    uuAuExcelDTO.setTime(missionWorkStatisticDTO.getDateTime());
                    uuAuExcelDTO.setAgvCode(workStatistic.getAgvCode());
                    //uu
                    double a = 0;
                    if ((totalTime - workStatistic.getChargeTime()) > 0) {
                        a = ToolUtil.formatDouble(((double) workStatistic.getWorkTime() / (totalTime - workStatistic.getChargeTime())) * 100, num);
                    }
                    uuAuExcelDTO.setUu(String.valueOf(a));
                    Double aDouble = stringDoubleHashMap.get(workStatistic.getAgvCode());
                    if (aDouble == null) {
                        stringDoubleHashMap.put(workStatistic.getAgvCode(), a);
                    } else {
                        aDouble = aDouble + a;
                        stringDoubleHashMap.put(workStatistic.getAgvCode(), aDouble);
                    }

                    //au
                    double b = 0;
                    if ((totalTime - workStatistic.getChargeTime()) > 0) {
                        b = ToolUtil.formatDouble((((double) workStatistic.getWorkTime() + workStatistic.getParkTime() + workStatistic.getFreeTime()) / (totalTime - workStatistic.getChargeTime())) * 100, num);
                    }

                    Double aDouble2 = stringDoubleHashMap2.get(workStatistic.getAgvCode());
                    if (aDouble2 == null) {
                        stringDoubleHashMap2.put(workStatistic.getAgvCode(), b);
                    } else {
                        aDouble2 = aDouble2 + b;
                        stringDoubleHashMap2.put(workStatistic.getAgvCode(), aDouble2);
                    }
                    uuAuExcelDTO.setAu(String.valueOf(b));
                    uuAuExcelDTOS.add(uuAuExcelDTO);
                }
            }
            for (UuAuExcelDTO uuAuExcelDTO : uuAuExcelDTOS) {
                Double aDouble = stringDoubleHashMap.get(uuAuExcelDTO.getAgvCode());
                double v = ToolUtil.formatDouble(aDouble / missionWorkStatisticDTOS.size(), num);
                Double aDouble2 = stringDoubleHashMap2.get(uuAuExcelDTO.getAgvCode());
                double v2 = ToolUtil.formatDouble(aDouble2 / missionWorkStatisticDTOS.size(), num);
                uuAuExcelDTO.setUuMmeanValue(String.valueOf(v2));
                uuAuExcelDTO.setAuMeanValue(String.valueOf(v));
            }

            // 首先按照time属性排序
            // time相同，按照name排序
            uuAuExcelDTOS.sort(Comparator.comparing((UuAuExcelDTO o) -> parseTime(o.getTime())).thenComparing(UuAuExcelDTO::getAgvCode));
            WriteSheet uuSheet = EasyExcel.writerSheet(1, "UU&AU").head(UuAuExcelDTO.class).build();
            excelWriter.write(uuAuExcelDTOS, uuSheet);
        } catch (Exception e) {
            log.error("发生异常！异常信息：{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("发生异常！！");
        }
    }


    private void exportUptime(TaskStatisticsRequest fleetWorkActionStatisticDto, ExcelWriter excelWriter) {
        try {
            //查询调度系统统计的机器人任务状态
            List<MissionWorkStatisticDTO>  missionWorkStatisticDTOS = getMissionStatisticAfter(fleetWorkActionStatisticDto);
            ArrayList<UpTimeExcelDTO> upTimeExcelDTOS = new ArrayList<>();
            HashMap<String, Double> stringDoubleHashMap = new HashMap<>();
            for (MissionWorkStatisticDTO missionWorkStatisticDTO : missionWorkStatisticDTOS) {
                long totalTime = getTotalTime(fleetWorkActionStatisticDto, missionWorkStatisticDTO.getDateTime());
                List<MissionWorkStatisticDTO.MissionWorkStatistic> missionWorkStatistics = missionWorkStatisticDTO.getMissionWorkStatistics();
                for (MissionWorkStatisticDTO.MissionWorkStatistic workStatistic : missionWorkStatistics) {
                    UpTimeExcelDTO upTimeExcelDTO = new UpTimeExcelDTO();
                    upTimeExcelDTO.setTime(missionWorkStatisticDTO.getDateTime());
                    double v = ToolUtil.formatDouble((((double) workStatistic.getWorkTime() + workStatistic.getParkTime() + workStatistic.getFreeTime()) / totalTime) * 100, num);
                    upTimeExcelDTO.setUpTime(String.valueOf(v));
                    Double aDouble = stringDoubleHashMap.get(workStatistic.getAgvCode());
                    if (aDouble == null) {
                        stringDoubleHashMap.put(workStatistic.getAgvCode(), v);
                    } else {
                        aDouble = aDouble + v;
                        stringDoubleHashMap.put(workStatistic.getAgvCode(), aDouble);
                    }
                    upTimeExcelDTO.setAgvCode(workStatistic.getAgvCode());
                    upTimeExcelDTOS.add(upTimeExcelDTO);
                }
            }
            for (UpTimeExcelDTO upTimeExcelDTO : upTimeExcelDTOS) {
                Double aDouble = stringDoubleHashMap.get(upTimeExcelDTO.getAgvCode());
                double v = ToolUtil.formatDouble(aDouble / missionWorkStatisticDTOS.size(), num);
                upTimeExcelDTO.setMeanValue(String.valueOf(v));
            }
            // 首先按照time属性排序
            // time相同，按照name排序
            upTimeExcelDTOS.sort(Comparator.comparing((UpTimeExcelDTO o) -> parseTime(o.getTime())).thenComparing(UpTimeExcelDTO::getAgvCode));

            WriteSheet upTimeSheet = EasyExcel.writerSheet(3, "Up time").head(UpTimeExcelDTO.class).build();
            excelWriter.write(upTimeExcelDTOS, upTimeSheet);
        } catch (Exception e) {
            log.error("发生异常！异常信息：{}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("发生异常！！");
        }
    }
    private static Date parseTime(String time) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return format.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

}
