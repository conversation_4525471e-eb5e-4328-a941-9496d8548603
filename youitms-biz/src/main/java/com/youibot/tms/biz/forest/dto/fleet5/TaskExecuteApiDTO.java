package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "TaskExecuteApiDTO",description = "创建任务")
public class TaskExecuteApiDTO {

    @ApiModelProperty(value = "任务类型编号", required = true)
    private String taskTypeCode;

    @ApiModelProperty(value = "外部任务编号", position = 1)
    private String externalTaskNo;

    @ApiModelProperty(value = "回调地址, 当任务执行成功/取消时会调用该地址", position = 2)
    private String callbackUrl;

    @ApiModelProperty(value = "优先级 最高=5 较高=4 高=3 中=2 低=1, 默认低", position = 3)
    private Integer priority;

    @ApiModelProperty(value = "任务执行参数", position = 4)
    private Map<String, Object> params;

}
