package com.youibot.tms.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.AgvType;
import com.youibot.tms.biz.forest.dto.AgvTypeDTO;
import com.youibot.tms.biz.mapper.AgvTypeMapper;
import com.youibot.tms.biz.service.AgvTypeService;
import com.youibot.tms.biz.service.FleetProxyService;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.youibot.tms.common.constant.SysConfigKeyConstants.SYS_FLEET_HOST;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Slf4j
@Service
public class AgvTypeServiceImpl extends ServiceImpl<AgvTypeMapper, AgvType> implements AgvTypeService {
    @Resource
    private FleetProxyService fleetProxyService;
    @Resource
    private ISysConfigService sysConfigService;

    @Override
    public AgvType selectByType(String agvType) {
        return this.baseMapper.selectByType(agvType, false);//默认查找未删除的
    }

    @Override
    public boolean sync() throws BusinessException {
        //先尝试获取fleet查询到的数据
        List<AgvTypeDTO> data = null;
        try {
            data = fleetProxyService.getAllAgvTypes();
        } catch (Exception e) {
            String host = sysConfigService.selectConfigValueByKey(SYS_FLEET_HOST);
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED, host);
        }

        //再删除所有,逻辑删除
        this.baseMapper.delete(null);

        if (data != null && data.size() > 0) {
            data.forEach(type -> {
                AgvType old = this.baseMapper.selectByType(type.getCode(), null);//查找范围包括已删除
                if (old != null) {
                    old.setDelFlag(false);
                    old.setId(type.getId());
                    this.baseMapper.updateByType(old);
                } else {
                    AgvType entity = new AgvType();
                    entity.setId(type.getId());
                    entity.setAgvType(type.getCode());
                    this.save(entity);
                }
            });
        }


        return true;
    }

}
