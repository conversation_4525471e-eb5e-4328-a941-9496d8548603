package com.youibot.tms.biz.events.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.PortStocker;
import com.youibot.tms.biz.mapper.PortStockerMapper;
import com.youibot.tms.common.core.domain.entity.ZoneStocker;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.system.events.ZoneStockerRemoveEvent;
import com.youibot.tms.system.mapper.ZoneStockerMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ZoneStockerRemoveListener implements ApplicationListener<ZoneStockerRemoveEvent> {


    @Resource
    private PortStockerMapper portStockerMapper;

    @Resource
    private ZoneStockerMapper zoneStockerMapper;

    @Override
    public void onApplicationEvent(ZoneStockerRemoveEvent event) {
        List<Long> ids = event.getIds();
        if (ToolUtil.isNotEmpty(ids)) {
            log.info("Get ZoneStockerRemoveEvent,zoneStocker ids:{}", ids);
            for (Long zoneStockerId : ids) {
                LambdaQueryWrapper<PortStocker> wrapper = new LambdaQueryWrapper();
                wrapper.eq(PortStocker::getZoneStockerId,zoneStockerId);
                long count = portStockerMapper.selectCount(wrapper);
                if(count >0 ){
                    ZoneStocker entity = zoneStockerMapper.selectById(zoneStockerId);
                    String zoneStockerName = entity!=null?entity.getName():"";
                    throw new BusinessException(BizErrorCode.ZONE_STOCKER_BIND_PORT,zoneStockerName);
                }
            }

        }
    }
}
