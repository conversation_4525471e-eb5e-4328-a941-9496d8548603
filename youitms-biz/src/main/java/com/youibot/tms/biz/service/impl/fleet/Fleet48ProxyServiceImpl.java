package com.youibot.tms.biz.service.impl.fleet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.api.request.TaskStatisticsRequest;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.flow.dto.FleetAndTmsTaskDTO;
import com.youibot.tms.biz.forest.FleetClient;
import com.youibot.tms.biz.forest.dto.*;
import com.youibot.tms.biz.forest.request.*;
import com.youibot.tms.biz.service.AgvService;
import com.youibot.tms.biz.service.FleetMissionWorkExecutingQueue;
import com.youibot.tms.biz.service.FleetProxyService;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.StringUtils;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.common.utils.spring.SpringUtils;
import com.youibot.tms.system.service.ISysConfigService;
import com.youibot.tms.workflow.utils.LambadaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @ClassName: Fleet48ProxyServiceImpl
 * @Description: fleet请求相关辅助类
 * @date 2021-03-03 11:47:13
 */
@Primary
@Slf4j
@Component("fleetProxyService")
@ConditionalOnExpression(value = "(!'${fleet.version}'.equals('simulation') )&& ${fleet.version}==4.8")
public class Fleet48ProxyServiceImpl implements FleetProxyService {

    @Resource
    private FleetClient fleetClient;

    @Value("${fleet.version:}")
    private String version;

    @Resource
    private FleetMissionWorkExecutingQueue fleetMissionWorkExecutingQueue;

    @Resource
    private ISysConfigService sysConfigService;

    @PostConstruct
    public void init() {
        log.info("fleet.version={},启用FleetDefaultProxy", version);
    }

    @Override
    public String version() {
        FleetLogoDTO logo = fleetClient.logo();
        if (logo != null) {
            return logo.getVersion();
        } else {
            return null;
        }
    }

    @Override
    public String testVersion(String host) {
        try {
            FleetLogoDTO logo = fleetClient.logo(host);
            if (logo != null) {
                return logo.getVersion();
            } else {
                return null;
            }
        } catch (Exception e) {
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED, host);
        }
    }

    @Override
    public List<AgvDTO> listFreeVehicles() {
        return Collections.emptyList();
    }

    @Override
    public List<AgvDTO> listAllAgvs() {
        List<FleetAgvDTO> result =  fleetClient.listAllAgvs();
        return result.stream().map(agv -> {
            AgvDTO dto = new AgvDTO();
            dto.setAgvId(agv.getAgvId());
            dto.setAgvCode(agv.getAgvCode());
            dto.setAgvName(agv.getAgvName());
            dto.setAgvType(agv.getAgvType());
            dto.setStatus(agv.getVehicleStatus());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AgvDTO> listOnlineAgvs() {
        List<VehicleDTO> result = fleetClient.listVehicles();
        if(result!=null){
            return result.stream().map(this::getAgvDTO).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<AgvDTO> listFreeAgvs() {
        List<VehicleDTO> result = fleetClient.listFreeVehicles();
        if(result!=null){
            return result.stream().map(this::getAgvDTO).collect(Collectors.toList());
        }
        return null;
    }

    private AgvDTO getAgvDTO(VehicleDTO vehicleDTO) {
        AgvDTO dto = new AgvDTO();
        dto.setId(vehicleDTO.getId());
        dto.setAgvId(vehicleDTO.getId());
        dto.setAgvCode(vehicleDTO.getId());
        dto.setAgvName(vehicleDTO.getName());
        dto.setStatus(vehicleDTO.getVehicleStatus());
        dto.setBattery(vehicleDTO.getBattery());
        return dto;
    }

    @Override
    public FreeAgvAndNotFreeReasonRO freeAndNotFreeReason() {
        FreeAgvAndNotFreeReasonVO result = fleetClient.freeAndNotFreeReason();
        if(result!=null){
            FreeAgvAndNotFreeReasonRO response= new FreeAgvAndNotFreeReasonRO();
            response.setNotFreeReasonMap(result.getNotFreeReasonMap());
            if(result.getFreeVehicles()!=null) {
                response.setFreeVehicles(result.getFreeVehicles().stream().map(this::getAgvDTO).collect(Collectors.toList()));
            }
            return response;
        }
        return null;
    }


    /**
     * 传入一个导航点编号和机器人列表,从传入的机器人列表中获取距离这个导航点最近的机器人
     */
    @Override
    public String getAgvCodesSequence(String mapName, List<String> agvCodes, String markerCode) {
        if (agvCodes != null && agvCodes.size() == 1) {
            //如果只有一个AGV，则不用排序，直接返回
            return agvCodes.get(0);
        }
        MarkerQueryRequest markerQueryRequest = new MarkerQueryRequest();
        markerQueryRequest.setAgvMapName(mapName);
        List<MarkerDTO> markers = this.getAllMarker(markerQueryRequest);
        String markerId = null;
        if (ToolUtil.isNotEmpty(markers)) {
            MarkerDTO marker = markers.stream().filter(m -> {
                return m.getCode().equals(markerCode);
            }).findFirst().orElse(null);
            if (marker != null) {
                markerId = marker.getId();
            }
        }
        if (StringUtils.isEmpty(markerId)) {
            throw new BusinessException(BizErrorCode.PORT_ACTION_PARAM_CONFIG_ERROR);
        }
        log.info("根据传入的导航点查询距离最近的机器人！导航点ID：{},传入的可用机器人列表：{}", markerId, agvCodes);
        Map<String, Integer> sequence = fleetClient.getAgvCodesSequence(markerId);

        List<String> sortedAgvCodes = agvCodes.stream().sorted(new Comparator<String>() {
            @Override
            public int compare(String t0, String t1) {
                Integer i1 = sequence.get(t0);
                if (i1 == null) {
                    i1 = Integer.MAX_VALUE;
                }
                Integer i2 = sequence.get(t1);
                if (i2 == null) {
                    i2 = Integer.MAX_VALUE;
                }
                return i1 - i2;
            }
        }).collect(Collectors.toList());
        log.info("根据传入的导航点查询距离最近的机器人！fleet返回的结果：{}", sortedAgvCodes);
        return sortedAgvCodes.get(0);
    }

    @Override
    public List<MarkerDTO> getAllMarker(MarkerQueryRequest queryRequest) {
        return fleetClient.getAllMarker(queryRequest);
    }


    @Override
    public MissionWorkDTO createMissionWork(FleetAndTmsTaskDTO fleetAndTmsTaskDTO) {
        log.info("创建Fleet任务,任务参数：{}，流程节点ID：{}", fleetAndTmsTaskDTO.getMissionWorkParam(), fleetAndTmsTaskDTO.getFlowNodeId());
        MissionWorkDTO missionWork = fleetClient.createMissionWork(fleetAndTmsTaskDTO.getMissionWorkParam());
        fleetAndTmsTaskDTO.getMissionWorkParam().setMissionWorkId(missionWork.getId());
        fleetMissionWorkExecutingQueue.put(fleetAndTmsTaskDTO.getFlowNodeId(), fleetAndTmsTaskDTO);
        return missionWork;
    }

    @Override
    public List<MissionDTO> getAllMission(MissionQueryRequest queryRequest) {
        return fleetClient.getAllMission(queryRequest);
    }

    @Override
    public FleetPageDataDTO<MissionDTO> getPageMission(MissionQueryPageRequest queryPageRequest) {
        return fleetClient.getPageMission(queryPageRequest);
    }

    @Override
    public MissionWorkDTO getMissionWork(String id) {
        return fleetClient.getMissionWork(id);
    }

    @Override
    public MissionWorkDTO pollGetMissionWork(String missionWorkId, MissionWorkStatus... status) {
        MissionWorkDTO missionWork = this.getMissionWork(missionWorkId);
        List<MissionWorkStatus> collect = Arrays.stream(status).collect(Collectors.toList());
        while (missionWork == null || !collect.contains(missionWork.getStatus())) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.info("轮询fleet任务信息时线程被中断！");
            }
            missionWork = this.getMissionWork(missionWorkId);
            if (missionWork != null) {
                log.info("查询到fleet任务的信息：{}", JSON.toJSONString(missionWork));
            } else {
                log.info("从fleet没有查询到MissionWorkId为：{}的任务，请检查fleet系统任务是否被删除！", missionWorkId);
            }
        }
        return missionWork;
    }

    /**
     * 获取地图列表
     *
     * @param request AgvMapRequest
     * @return 返回地图列表
     */
    @Override
    public List<AgvMapDTO> getAllAGVMap(AgvMapRequest request) {
        List<AGVMapVO> allAGVMap4 = fleetClient.getAllAGVMap4(request);
        List<AgvMapDTO> agvMapDTOS = new LinkedList<>();
        allAGVMap4.forEach(item -> {
            agvMapDTOS.add(item.getAgvMap());
        });
        return agvMapDTOS;
    }

    @Override
    public List<AgvTypeDTO> getAllAgvTypes() {
        return fleetClient.getAllAgvTypes();
    }



    @Override
    public void openAutoParkAndCharge(String agvCode) {
        fleetClient.openAutoPark(agvCode);
        fleetClient.openAutoCharge(agvCode);
    }

    @Override
    public void closeAutoParkAndCharge(String agvCode) {
        fleetClient.closeAutoPark(agvCode);
        fleetClient.closeAutoCharge(agvCode);
    }

    @Override
    public List<MissionWorkStatisticDTO> getMissionWorkStatistic(TaskStatisticsRequest agvDataStatisticRequest) {
        try {
            MissionWorkStatisticQueryRequest missionWorkStatisticQueryRequest = new MissionWorkStatisticQueryRequest();
            missionWorkStatisticQueryRequest.setAgvCode(agvDataStatisticRequest.getAgvCode());
            missionWorkStatisticQueryRequest.setStartTime(agvDataStatisticRequest.getBeginDate());
            missionWorkStatisticQueryRequest.setType(agvDataStatisticRequest.getType().name());
            missionWorkStatisticQueryRequest.setEndTime(agvDataStatisticRequest.getEndDate());
            JSONObject missionWorkActionStatisticJson = fleetClient.getMissionWorkStatistic(missionWorkStatisticQueryRequest);
            Set<Map.Entry<String, Object>> entries = missionWorkActionStatisticJson.entrySet();
            List<MissionWorkStatisticDTO> dataList = new ArrayList<>();
            for (Map.Entry<String, Object> item : entries) {
                String key = item.getKey();
                Object value1 = item.getValue();
                if (ToolUtil.isNotEmpty(value1)) {
                    MissionWorkStatisticDTO missionWorkActionStatisticDTO = new MissionWorkStatisticDTO();
                    JSONObject value = JSONObject.parseObject(JSON.toJSONString(value1));
                    missionWorkActionStatisticDTO.setDateTime(key);
                    ArrayList<MissionWorkStatisticDTO.MissionWorkStatistic> missionWorkActionStatistics = new ArrayList<>();
                    missionWorkActionStatisticDTO.setMissionWorkStatistics(missionWorkActionStatistics);
                    Set<Map.Entry<String, Object>> entries1 = value.entrySet();
                    entries1.forEach((data) -> {
                        JSONObject jsonObject2 = (JSONObject) data.getValue();
                        if (ToolUtil.isNotEmpty(jsonObject2)) {
                            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(jsonObject2));
                            MissionWorkStatisticDTO.MissionWorkStatistic missionWorkActionStatistic = new MissionWorkStatisticDTO.MissionWorkStatistic();
                            missionWorkActionStatistic.setWorkNum(jsonObject.getInteger(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getWorkNum)));
                            missionWorkActionStatistic.setWorkTime(jsonObject.getLong(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getWorkTime)));
                            missionWorkActionStatistic.setAgvCode(jsonObject.getString(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getAgvCode)));
                            missionWorkActionStatistic.setChargeNum(jsonObject.getInteger(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getChargeNum)));
                            missionWorkActionStatistic.setChargeTime(jsonObject.getLong(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getChargeTime)));
                            missionWorkActionStatistic.setErrorNum(jsonObject.getInteger(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getErrorNum)));
                            missionWorkActionStatistic.setErrorTime(jsonObject.getLong(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getErrorTime)));
                            missionWorkActionStatistic.setParkNum(jsonObject.getInteger(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getParkNum)));
                            missionWorkActionStatistic.setParkTime(jsonObject.getLong(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getParkTime)));
                            missionWorkActionStatistic.setFreeTime(jsonObject.getLong(LambadaUtil.resolveColumn(MissionWorkStatisticDTO.MissionWorkStatistic::getFreeTime)));
                            missionWorkActionStatistic.setOfflineTime(0L);
                            missionWorkActionStatistics.add(missionWorkActionStatistic);
                        }
                    });
                    dataList.add(missionWorkActionStatisticDTO);
                }
            }
            return dataList;
        } catch (Exception e) {
            log.error("请求Fleet获取机器人任务统计数据发生异常：{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED);
        }
    }

    public List<String> sortMarkerCodesByDistance(String mapName, String agvCode, List<String> markerCodes) {
        log.info("准备给导航点排序:{}", markerCodes);
        MarkerQueryRequest markerQueryRequest = new MarkerQueryRequest();
        markerQueryRequest.setAgvMapName(mapName);
        List<MarkerDTO> markers = this.getAllMarker(markerQueryRequest);
        if (ToolUtil.isNotEmpty(markers)) {
            //把markerCode转成markerId
            List<MarkerDTO> filterMarkers = markers.stream().filter(m -> markerCodes.contains(m.getCode())).collect(Collectors.toList());
            Set<String> filterMarkerIds = filterMarkers.stream().map(MarkerDTO::getId).collect(Collectors.toSet());
            if (ToolUtil.isNotEmpty(filterMarkerIds)) {
                TMSMarkerIdSequenceDTO sortRequest = new TMSMarkerIdSequenceDTO();
                sortRequest.setAgvCode(agvCode);
                sortRequest.setMarkerIds(filterMarkerIds);
                //result的Map,key是markerId,value是排序值 ，值越小，离小车越近
                Map<String, Integer> result = fleetClient.getMarkerIdsSequence(sortRequest);
                List<String> sortedMarkerCodes = filterMarkers.stream().sorted(new Comparator<MarkerDTO>() {
                    @Override
                    public int compare(MarkerDTO t0, MarkerDTO t1) {
                        return result.get(t0.getId()) - result.get(t1.getId());
                    }
                }).map(MarkerDTO::getCode).collect(Collectors.toList());
                log.info("排序后的导航点:{}", sortedMarkerCodes);
                return sortedMarkerCodes;
            }
        }
        //如果没有查到导航点，则原样返回
        return markerCodes;
    }

    @Override
    public SchedulerConfig getSchedulerConfig() {
        return fleetClient.getSchedulerConfig();
    }

    @Override
    public boolean oneKeyStop(String agvCode) {
        try {
            fleetClient.oneKeyStop(agvCode);
            return true;
        } catch (Exception e) {
            log.error("调用fleet一键停止功能接口报错，AGV[{}]，异常信息：{}", (agvCode == null ? "null" : agvCode), Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    @Override
    public boolean oneKeyReset(String agvCode) {
        try {
            fleetClient.oneKeyReset(agvCode);
            return true;
        } catch (Exception e) {
            log.error("调用fleet AGV一键重置功能接口报错，AGV[{}]，异常信息：{}", (agvCode == null ? "null" : agvCode), Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    @Override
    public boolean restartTaskPrevOneKeyStopAllAgvRunningMission(String agvCode) {
        try {
            //由于要停止机器人正在执行中的作业任务，那么就需要先将Fleet回调和回调超时轮询给屏蔽掉，否则停止任务时fleet会u回调我们TMS，
            // 然后通知流程说Fleet任务异常了，导致重新执行流程发生异常！
            log.info("先将Fleet回调和回调超时轮询给屏蔽掉！AGV[{}]", agvCode);
            List<FleetAndTmsTaskDTO> fleetAndTmsTaskDTOS = fleetMissionWorkExecutingQueue.getAll();
            AgvService agvService = SpringUtils.getBean(AgvService.class);
            Agv agv = agvService.selectByAgvCode(agvCode);
            String workFlowId = agv.getWorkFlowId();
            if (workFlowId != null) {
                for (FleetAndTmsTaskDTO fleetAndTmsTaskDTO : fleetAndTmsTaskDTOS) {
                    if (workFlowId.equals(fleetAndTmsTaskDTO.getFlowId())) {
                        fleetMissionWorkExecutingQueue.remove(fleetAndTmsTaskDTO.getFlowNodeId());
                    }
                }
            }
            log.info("准备停止当前机器人身上所有执行中和创建中的任务！AGV[{}]", agvCode);
            try {
                //先一键重置清错
                this.oneKeyReset(agvCode);
            } catch (Exception e) {
                log.error("一键重置清错失败！AGV[{}],异常信息：{}", agvCode, Throwables.getStackTraceAsString(e));
            }
            //再一键停止
            boolean b = this.oneKeyStop(agvCode);
            log.info("停止当前机器人身上所有执行中和创建中的任务成功！AGV[{}]", agvCode);
            return b;
        } catch (Exception e) {
            log.error("调用fleet AGV一键重启任务功能接口报错，AGV[{}]，异常信息：{}", (agvCode == null ? "null" : agvCode), Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    public String getCallbackUrl(String flowId, String flowNodeId) {
        String tmsUrl = sysConfigService.selectConfigValueByKey("sys.url");
        if (!tmsUrl.endsWith("/")) {
            tmsUrl = tmsUrl + "/";
        }
        String path = "api/v1/open/workflow/callback/fleet/task/callback/" + flowId + "/" + flowNodeId;
        return tmsUrl + path;
    }

    @Override
    public List<MissionWorkActionDTO> getMissionActions(String startTime, String endTime) {
        return fleetClient.getMissionAction(startTime, endTime, "create_time asc").getList();
    }
}
