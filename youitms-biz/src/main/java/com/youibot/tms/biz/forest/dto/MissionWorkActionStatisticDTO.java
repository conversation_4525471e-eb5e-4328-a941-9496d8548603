package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Fleet机器人任务数据统计报表接口返回的数据结构
 * <NAME_EMAIL> on 2023/2/17.
 *
 * <AUTHOR>
 * @date 2023/2/17 14:01
 */
@ApiModel(value = "MissionWorkActionStatisticDTO", description = "Fleet任务动作数据统计")
@Data
public class MissionWorkActionStatisticDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "统计时间")
    private String dateTime;
    @ApiModelProperty(value = "统计时间内的数据列表")
    private List<MissionWorkActionStatistic> missionWorkActionStatistics;

    @Data
    @ApiModel(value = "MissionWorkActionStatistic", description = "Fleet任务动作")
    public static class MissionWorkActionStatistic implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 机器人编号
         */
        @ApiModelProperty(value = "机器人编号")
        private String agvCode;
        /**
         * 任务动作的ID
         */
        @ApiModelProperty(value = "任务动作的ID")
        private String missionActionId;
        /**
         * 任务动作的名称
         */
        @ApiModelProperty(value = "任务动作的名称")
        private String missionActionName;
        /**
         * 执行次数
         */
        @ApiModelProperty(value = "执行次数")
        private Integer totalNum;
        /**
         * 执行时长
         */
        @ApiModelProperty(value = "执行时长")
        private Long totalTime;
    }
}
