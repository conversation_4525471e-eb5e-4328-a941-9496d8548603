package com.youibot.tms.biz.forest.dto.fleet5;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "AbnormalApiDTO",description = "异常消息")
public class AbnormalApiDTO {

    @ApiModelProperty(value = "通知ID")
    private String id;

    @ApiModelProperty(value = "异常编码")
    private Integer code;

    @ApiModelProperty(value = "等级(1-普通,2-警告,3-错误)")
    private Integer level;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "描述", position = 1)
    private String description;

    @ApiModelProperty(value = "数据内容")
    private String data;

    @ApiModelProperty(value = "状态(0-激活,1-忽略,2-关闭)")
    private Integer status;

    @ApiModelProperty(value = "解决措施", position = 2)
    private String solution;

    @ApiModelProperty(value = "机器人编码", position = 4)
    private String vehicleCode;

    @JsonIgnore
    private String mapName;

    @ApiModelProperty(value = "地图编码", position = 5)
    private String vehicleMapCode;

    @ApiModelProperty(value = "地图编码")
    private String mapCode;

    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "创建时间", position = 8)
    private Date createDate;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "关闭时间")
    private Date closeTime;

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "失效时间")
    private Long invalidTime;

    @ApiModelProperty(value = "最后推送时间")
    private Date lastPushTime;

    @ApiModelProperty(value = "位置X坐标")
    private BigDecimal positionX;

    @ApiModelProperty(value = "位置Y坐标")
    private BigDecimal positionY;

    public String getVehicleMapCode() {
        return mapName;
    }

    // 兼容性方法
    public Date getCreateTime() {
        return createTime != null ? createTime : createDate;
    }

    public String getMapCode() {
        return mapCode != null ? mapCode : vehicleMapCode;
    }
}
