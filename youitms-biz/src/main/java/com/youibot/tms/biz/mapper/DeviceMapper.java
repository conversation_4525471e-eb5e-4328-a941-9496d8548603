package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youibot.tms.biz.entity.Device;
import com.youibot.tms.biz.entity.DeviceStatus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设备表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
public interface DeviceMapper extends BaseMapper<Device> {

    Page<DeviceStatus> selectPageForStatus(Page<Device> page, @Param("ew")QueryWrapper<Device> query);

    List<DeviceStatus> selectListForStatus(@Param("ew")QueryWrapper<Device> query);

    DeviceStatus selectForStatus(@Param("ew") QueryWrapper<Device> query);
}
