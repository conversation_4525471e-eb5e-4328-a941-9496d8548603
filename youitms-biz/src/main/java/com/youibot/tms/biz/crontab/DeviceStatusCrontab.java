package com.youibot.tms.biz.crontab;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.serotonin.modbus4j.BatchResults;
import com.youibot.tms.biz.entity.DeviceIot;
import com.youibot.tms.biz.entity.DeviceIotSignal;
import com.youibot.tms.biz.entity.DeviceModbusRealSignal;
import com.youibot.tms.biz.enums.DeviceConnectStatus;
import com.youibot.tms.biz.service.DeviceIotService;
import com.youibot.tms.biz.service.DeviceIotSignalService;
import com.youibot.tms.biz.service.DeviceStatusService;
import com.youibot.tms.common.iot.modbus.ModbusDriver;
import com.youibot.tms.common.iot.modbus.ModbusPoint;
import com.youibot.tms.common.iot.modbus.ModbusTcpUtil;
import com.youibot.tms.common.utils.ToolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class DeviceStatusCrontab {

    @Resource
    private DeviceIotService deviceIotService;

    @Resource
    private DeviceIotSignalService deviceIotSignalService;

    @Resource
    private DeviceStatusService deviceStatusService;

    /**
     * 每5秒扫描一次设备通讯
     */
//    @Scheduled(fixedRate = 5000)
    public void scanDeviceIot(){
        List<DeviceIot> iotList = getAllIot();
        iotList.forEach(deviceIot -> {
            scanOneDevice(deviceIot);
        });
    }

    private void scanOneDevice(DeviceIot iot){
        List<DeviceIotSignal> signals = getSignals(iot.getId());
        if(ToolUtil.isNotEmpty(signals)){
            ModbusDriver driver = iot.getModbusDriver();
            List<ModbusPoint> points = new ArrayList<>();
            //keySet，批量读取到结果时，再按keySet遍历结果
            Set<String> keySet = new HashSet<>();
            for(DeviceIotSignal s :signals){
                if(StringUtils.isNumeric(s.getAddress())){
                    int offset = Integer.parseInt(s.getAddress());
                    ModbusPoint p = new ModbusPoint();
                    p.setKey(s.getAddress());
                    keySet.add(s.getAddress());//记录key
                    p.setFunctionCode(3);
                    p.setSlaveId(1);
                    p.setOffset(offset);
                    points.add(p);
                }
            }

            try {
                BatchResults<String> result = ModbusTcpUtil.readBatch(driver,points);
                log.debug("iot:[{}:{}],信号:{}",iot.getIp(),iot.getPort(),result);
                //遍历结果，并保存到map中
                Map<String, DeviceModbusRealSignal> data = new HashMap<>();
                Date currentTime = new Date();
                keySet.forEach(k->{
                    DeviceIotSignal signal = getIotSignalByAddress(signals,k);
                    Object value = result.getValue(k);
                    DeviceModbusRealSignal realSignal = new DeviceModbusRealSignal();
                    realSignal.setAddress(k);
                    realSignal.setDescription(signal.getDescription());
                    realSignal.setValue(Integer.parseInt(value.toString()));
                    realSignal.setUpdateTime(currentTime);
                    data.put(k,realSignal);
                });
                deviceStatusService.putSignals(iot.getId(),data);
                deviceStatusService.setConnectStatus(iot.getId(), DeviceConnectStatus.SUCCESS);
            } catch (Exception e) {
                log.error("iot:[{}:{}]读取信号失败,{}",iot.getIp(),iot.getPort(),e.getMessage());
                //设备连接状态改为异常
                deviceStatusService.setConnectStatus(iot.getId(), DeviceConnectStatus.ERROR);
            }
        }
    }

    private DeviceIotSignal getIotSignalByAddress(List<DeviceIotSignal> signals,String address){
        return signals.stream().filter(signal->{
            return signal.getAddress().equals(address);
        }).findFirst().orElse(null);
    }


    private List<DeviceIot> getAllIot(){
        QueryWrapper<DeviceIot> queryWrapper =new QueryWrapper<>();
        queryWrapper.eq("a.del_flag",0); //未删除的
        queryWrapper.eq("b.del_flag",0); //设备未删除
        queryWrapper.eq("a.enabled", true);//启用状态的
        return deviceIotService.list(queryWrapper);
    }

    private List<DeviceIotSignal> getSignals(Long deviceIotId){
        LambdaQueryWrapper<DeviceIotSignal> queryWrapper = Wrappers.lambdaQuery(DeviceIotSignal.class)
                .eq(DeviceIotSignal::getDeviceIotId,deviceIotId)
                .eq(DeviceIotSignal::getDelFlag,0);
        return deviceIotSignalService.list(queryWrapper);
    }
}
