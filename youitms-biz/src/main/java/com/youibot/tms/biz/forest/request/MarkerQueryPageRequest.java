package com.youibot.tms.biz.forest.request;

import com.youibot.tms.common.core.request.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel("导航点分页查询DTO")
public class MarkerQueryPageRequest extends PageRequest {
    @ApiModelProperty(value = "地图名称，fleet4.8及以上版本有效")
    private String agvMapName;

    @ApiModelProperty(value = "是否草稿，默认false")
    private Boolean isDraft = false;
}
