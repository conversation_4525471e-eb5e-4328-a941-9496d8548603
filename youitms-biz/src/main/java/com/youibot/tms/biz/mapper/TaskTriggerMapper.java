package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.entity.TaskTrigger;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务触发器 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Mapper
public interface TaskTriggerMapper extends BaseMapper<TaskTrigger> {


    List<TaskTrigger> findTriggerByIotId(@Param("deviceIotId") Long deviceIotId);
}
