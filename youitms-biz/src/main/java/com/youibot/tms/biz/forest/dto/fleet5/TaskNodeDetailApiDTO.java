package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "TaskNodeDetailApiDTO",description = "任务节点详情")
public class TaskNodeDetailApiDTO {

    @ApiModelProperty(value = "节点编码")
    private String code;

    @ApiModelProperty(value = "节点名称", position = 1)
    private String name;

    @ApiModelProperty(value = "节点类型", position = 2)
    private String type;

    @ApiModelProperty(value = "节点状态 创建=Create 执行中=Running 已完成=Finished 取消=Cancel", position = 3)
    private String status;

    @ApiModelProperty(value = "输入参数", position = 4)
    private Map<String, Object> paramIn;

    @ApiModelProperty(value = "输出参数", position = 5)
    private Map<String, Object> paramOut;

    @ApiModelProperty(value = "开始时间", position = 6)
    private Date startTime;

    @ApiModelProperty(value = "结束时间", position = 7)
    private Date endTime;

    @ApiModelProperty(value = "总距离(m)，执行机器人移动节点该属性有值，表示行驶到目标点的总距离", position = 8)
    private Double totalDistance;

    @ApiModelProperty(value = "剩余距离(m)，执行机器人移动节点该属性有值，表示行驶到目标点的还有多少距离，单位米", position = 9)
    private Double remainDistance;

    @ApiModelProperty(value = "剩余时长(s)，执行机器人移动节点该属性有值，表示行驶到目标点的还需多少时长，单位秒", position = 10)
    private Double remainDuration;

}
