package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.util.Date;

/**
 * 任务类型
 * <AUTHOR>
 */
@Data
@ApiModel(value = "TaskTypeDTO",description = "任务类型")
public class TaskTypeDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "编码code")
    private String code;

    @ApiModelProperty(value = "名称name")
    private String name;

    @ApiModelProperty(value = "priority 优先级 最高=5 较高=4 高=3 中=2 低=1, 默认低")
    private Integer priority;

    @ApiModelProperty(value = "发布状态 已发布:Published 未发布: Unpublished")
    private String publishStatus;

    @ApiModelProperty(value = "编排数据")
    private String formatData;

    @Transient
    private Boolean hidden;

    @ApiModelProperty(value = "是否开启快捷入口，默认关闭")
    private Boolean isQuickVisit;

    @ApiModelProperty(value = "是否开启PDA入口，默认关闭")
    private Boolean isPdaVisit;

    @ApiModelProperty(value = "节点参数数据，提供给前端方便前端解析")
    private String nodeParam;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    private String creatorName;

    public String getCreator() {
        return creatorName;
    }

    public Long getCreatorId() {
        return creator;
    }

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "修改人")
    private Long updater;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "事件类型 接口事件：Interface 定时事件：FixedTime 按钮事件：Button 寄存器事件：Plc 机器人寄存器事件：VehiclePlc 机器人异常事件：VehicleAbnormal 任务取消事件：TaskCancel 任务完成事件：TaskFinished")
    private String eventType;

}
