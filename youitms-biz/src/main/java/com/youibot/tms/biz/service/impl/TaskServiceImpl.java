package com.youibot.tms.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.*;
import com.youibot.tms.biz.enums.*;
import com.youibot.tms.biz.flow.common.AbstractBizCommonComponent;
import com.youibot.tms.biz.flow.common.components.ForBeginComponent;
import com.youibot.tms.biz.flow.common.components.PickupComponent;
import com.youibot.tms.biz.flow.common.components.PutDownComponent;
import com.youibot.tms.biz.flow.common.components.SubFlowComponent;
import com.youibot.tms.biz.flow.common.listener.CommonWorkFlowListener;
import com.youibot.tms.biz.flow.constants.GlobalVariableKeys;
import com.youibot.tms.biz.flow.dto.PortAndPortType;
import com.youibot.tms.biz.mapper.*;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.common.core.domain.entity.ZoneArea;
import com.youibot.tms.common.core.domain.entity.ZoneStocker;
import com.youibot.tms.common.core.redis.RedisCache;
import com.youibot.tms.common.exception.ServiceException;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.Pair;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.common.utils.bean.BeanUtils;
import com.youibot.tms.system.dto.DataScopeParam;
import com.youibot.tms.system.service.ISysConfigService;
import com.youibot.tms.system.service.ZoneAreaService;
import com.youibot.tms.system.service.ZoneStockerService;
import com.youibot.tms.workflow.core.executor.WorkFlowExecutor;
import com.youibot.tms.workflow.core.flow.WorkFlow;
import com.youibot.tms.workflow.entity.WorkFlowNodeTemplateEntity;
import com.youibot.tms.workflow.entity.WorkFlowTemplateEntity;
import com.youibot.tms.workflow.event.WorkFlowStopEvent;
import com.youibot.tms.workflow.service.WorkFlowTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Slf4j
@Service
public class TaskServiceImpl extends ServiceImpl<TaskMapper, Task> implements TaskService {

    @Resource
    private TaskMapper taskMapper;
    @Resource
    private TaskNodeService taskNodeService;

    @Lazy
    @Resource
    private TaskLogService taskLogService;

    @Resource
    private AgvMapper agvMapper;

    @Lazy
    @Resource
    private AgvTypeService agvTypeService;

    @Lazy
    @Resource
    private TaskTemplateMapper taskTemplateMapper;

    @Resource
    private PortEqMapper portEqMapper;

    @Resource
    private PortStockerMapper portStockerMapper;
    @Lazy
    @Resource
    private ZoneAreaService zoneAreaService;
    @Resource
    private ZoneStockerService zoneStockerService;
    @Resource
    private TaskZoneMapper taskZoneMapper;

    @Resource
    private PortService portService;
    @Autowired
    protected FlowTemplateGlobalVariableService flowTemplateGlobalVariableService;
    @Resource
    private RedisCache redisCache;
    @Autowired
    private FlowTemplateComponentGlobalVariableService flowTemplateComponentGlobalVariableService;
    @Autowired
    private TaskDispatchAgvService taskDispatchAgvService;

    @Resource
    private ISysConfigService sysConfigService;

    @Autowired
    private WorkFlowExecutor workFlowExecutor;

    @Lazy
    @Resource
    private FlowTemplateAssistService flowTemplateAssistService;

    @Lazy
    @Resource
    private CommonWorkFlowListener commonWorkFlowListener;

    @Lazy
    @Resource
    private DeviceIotService deviceIotService;

    @Autowired
    private WorkFlowTemplateService workFlowTemplateService;
    private final static String ORDER_CODE_INCREMENT_KEY = "order_code_increment";

    /**
     * 不足4位的数字，前面补0
     */
    private final static String FORMAT = "%04d";

    private final static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    @Override
    public Page<Task> page(Page<Task> page, QueryWrapper<Task> queryWrapper) {

        DataScopeParam dataScopeParam = DataScopeParam.buildDataScopeParam("biz_task_zone", "task_id");
        if (dataScopeParam != null) {
            queryWrapper.apply(dataScopeParam.getFilterSql());
        }
        return this.baseMapper.selectPage(page, queryWrapper, dataScopeParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Task createTaskByFlowTemplateId(String workFlowTemplateId, JSONObject taskGlobalVariable) {
        Task task = new Task();
        task.setType(TaskType.NORMAL);
        task.setCode(incrementCode());
        task.setFlowTemplateId(workFlowTemplateId);
        taskGlobalVariable.put(GlobalVariableKeys.taskCodeKey, task.getCode());
        task.setGlobalVariable(taskGlobalVariable);
        task.setSource(TaskSource.TEMPLATE);
        task.setStatus(TaskStatus.NOT_START);
        initTaskPortInfo(task);
        this.saveOrUpdate(task);
        return task;
    }


    /**
     * 根据全局变量初始化任务节点信息
     *
     * @param task 任务信息
     */
    public void initTaskPortInfo(Task task) {
        String flowTemplateId = task.getFlowTemplateId();
        //获取流程模板信息
        WorkFlowTemplateEntity workFlowTemplateById = workFlowTemplateService.getWorkFlowTemplateById(flowTemplateId);
        ConcurrentHashMap<String, WorkFlowNodeTemplateEntity> nodeList = workFlowTemplateById.getNodeList();
        Collection<WorkFlowNodeTemplateEntity> values = nodeList.values();
        List<WorkFlowNodeTemplateEntity> pickupNodes = values.stream().filter(item -> item.getComponentClazz().equals(PickupComponent.class.getTypeName())).collect(Collectors.toList());
        List<WorkFlowNodeTemplateEntity> putDownNodes = values.stream().filter(item -> item.getComponentClazz().equals(PutDownComponent.class.getTypeName())).collect(Collectors.toList());
        List<WorkFlowNodeTemplateEntity> subFlowNodes = values.stream().filter(item -> item.getComponentClazz().equals(SubFlowComponent.class.getTypeName())).collect(Collectors.toList());
        List<WorkFlowNodeTemplateEntity> forBeginNodes = values.stream().filter(item -> item.getComponentClazz().equals(ForBeginComponent.class.getTypeName())).collect(Collectors.toList());
        Collection<WorkFlowNodeTemplateEntity> subFlowNodes1 = getSubFlowNodes(subFlowNodes, task.getGlobalVariable());

        List<TaskNode> taskNodes = new ArrayList<>();
        while (ToolUtil.isNotEmpty(subFlowNodes)) {
            List<WorkFlowNodeTemplateEntity> collect = subFlowNodes1.stream().filter(item -> item.getComponentClazz().equals(PickupComponent.class.getTypeName())).collect(Collectors.toList());
            pickupNodes.addAll(collect);
            List<WorkFlowNodeTemplateEntity> collect1 = subFlowNodes1.stream().filter(item -> item.getComponentClazz().equals(PutDownComponent.class.getTypeName())).collect(Collectors.toList());
            putDownNodes.addAll(collect1);
            subFlowNodes = subFlowNodes1.stream().filter(item -> item.getComponentClazz().equals(SubFlowComponent.class.getTypeName())).collect(Collectors.toList());
            if (ToolUtil.isNotEmpty(subFlowNodes)) {
                subFlowNodes1 = getSubFlowNodes(subFlowNodes, task.getGlobalVariable());
            }
        }
        if (ToolUtil.isNotEmpty(pickupNodes)) {
            WorkFlowNodeTemplateEntity workFlowNodeTemplateEntity = pickupNodes.get(0);
            List<Pair<Port, PortAndPortType>> nodePorts = getNodePorts(workFlowNodeTemplateEntity, task.getGlobalVariable(), forBeginNodes);
            if (ToolUtil.isNotEmpty(nodePorts)) {
                Port port = nodePorts.get(0).getKey();
                task.setStartCode(port.getCode());
                task.setStartId(port.getId());
                task.setStartName(port.getFullName());
                task.setStartType(port.getPortType());
                task.setCarrierCode(nodePorts.get(0).getValue().getCarrierCode());
                for (WorkFlowNodeTemplateEntity pickupNode : pickupNodes) {
                    List<TaskNode> taskNode = createTaskNode(task, pickupNode, forBeginNodes);
                    for (TaskNode node : taskNode) {
                        if (!taskNodes.contains(node)) {
                            taskNodes.add(node);
                        }
                    }
                }
            }
        }
        if (ToolUtil.isNotEmpty(putDownNodes)) {
            WorkFlowNodeTemplateEntity workFlowNodeTemplateEntity = putDownNodes.get(putDownNodes.size() - 1);
            List<Pair<Port, PortAndPortType>> nodePorts = getNodePorts(workFlowNodeTemplateEntity, task.getGlobalVariable(), forBeginNodes);
            if (ToolUtil.isNotEmpty(nodePorts)) {
                Port port = nodePorts.get(0).getKey();
                task.setDestinationCode(port.getCode());
                task.setDestinationId(port.getId());
                task.setDestinationName(port.getFullName());
                task.setDestinationType(port.getPortType());
                task.setCarrierCode(nodePorts.get(0).getValue().getCarrierCode());
                for (WorkFlowNodeTemplateEntity putDownNode : putDownNodes) {
                    List<TaskNode> taskNode = createTaskNode(task, putDownNode, forBeginNodes);
                    for (TaskNode node : taskNode) {
                        if (!taskNodes.contains(node)) {
                            taskNodes.add(node);
                        }
                    }
                }
            }
        }
        taskNodeService.saveBatch(taskNodes);
    }

    /**
     * 获取子节点所有的节点
     *
     * @param subFlowNodes   子节点列表
     * @param globalVariable 全局变量
     * @return
     */
    public Collection<WorkFlowNodeTemplateEntity> getSubFlowNodes(List<WorkFlowNodeTemplateEntity> subFlowNodes, JSONObject globalVariable) {
        ArrayList<WorkFlowNodeTemplateEntity> workFlowNodeTemplateEntities = new ArrayList<>();
        for (WorkFlowNodeTemplateEntity subFlowNode : subFlowNodes) {
            List<FlowTemplateComponentGlobalVariable> list = flowTemplateComponentGlobalVariableService.list(Wrappers.<FlowTemplateComponentGlobalVariable>lambdaQuery().eq(FlowTemplateComponentGlobalVariable::getFlowNodeTemplateId, subFlowNode.getId()));
            if (ToolUtil.isEmpty(list)) {
                throw new ServiceException("子流程节点没有设置参数，请检查流程节点的参数！节点ID编号：" + subFlowNode.getId() + "，节点名称：" + subFlowNode.getName());
            }
            FlowTemplateComponentGlobalVariable flowTemplateComponentGlobalVariable = list.get(0);
            TaskComponentGlobalVariableType type = flowTemplateComponentGlobalVariable.getType();
            String componentParamsValue = flowTemplateComponentGlobalVariable.getComponentParamsValue();
            if (componentParamsValue == null) {
                throw new ServiceException("子流程节点没有设置参数，请检查流程节点的参数！节点ID编号：" + subFlowNode.getId() + "，节点名称：" + subFlowNode.getName());
            }
            if (TaskComponentGlobalVariableType.fixed.equals(type)) {
                WorkFlowTemplateEntity workFlowTemplateById = workFlowTemplateService.getWorkFlowTemplateById(componentParamsValue);
                ConcurrentHashMap<String, WorkFlowNodeTemplateEntity> nodeList = workFlowTemplateById.getNodeList();
                workFlowNodeTemplateEntities.addAll(nodeList.values());
            } else if (TaskComponentGlobalVariableType.variable.equals(type)) {
                FlowTemplateGlobalVariable byId = flowTemplateGlobalVariableService.getById(componentParamsValue);
                String variableKey = byId.getVariableKey();
                String subFlowId = globalVariable.getString(variableKey);
                WorkFlowTemplateEntity workFlowTemplateById = workFlowTemplateService.getWorkFlowTemplateById(subFlowId);
                ConcurrentHashMap<String, WorkFlowNodeTemplateEntity> nodeList = workFlowTemplateById.getNodeList();
                workFlowNodeTemplateEntities.addAll(nodeList.values());
            } else {
                throw new ServiceException("由于您的流程中使用了子流程节点调用了其他流程，但是没有选择固定的子流程编号使用的是节点输出变量，因此无法获取到子流程信息导致无法推断出流程的具体的点位信息！节点ID编号：" + subFlowNode.getId() + "，节点名称：" + subFlowNode.getName());
            }
        }
        return workFlowNodeTemplateEntities;
    }

    /**
     * 获取取料或者放料节点的料口列表
     *
     * @param flowNodeTemplateEntity 取料或者放料节点
     * @param globalVariable         全局变量
     * @return
     */
    public List<Pair<Port, PortAndPortType>> getNodePorts(WorkFlowNodeTemplateEntity flowNodeTemplateEntity, JSONObject globalVariable, List<WorkFlowNodeTemplateEntity> forBeginNodes) {
        String portCodeListKey = PickupComponent.portCodeKey;
        List<FlowTemplateComponentGlobalVariable> list = flowTemplateComponentGlobalVariableService.list(Wrappers.<FlowTemplateComponentGlobalVariable>lambdaQuery()
                .eq(FlowTemplateComponentGlobalVariable::getFlowNodeTemplateId, flowNodeTemplateEntity.getId()).eq(FlowTemplateComponentGlobalVariable::getComponentParamsKey, portCodeListKey));
        if (ToolUtil.isEmpty(list)) {
            throw new ServiceException("取料节点没有设置参数，请检查取料节点的参数！节点ID编号：" + flowNodeTemplateEntity.getId() + "，节点名称：" + flowNodeTemplateEntity.getName());
        }
        FlowTemplateComponentGlobalVariable flowTemplateComponentGlobalVariable = list.get(0);
        TaskComponentGlobalVariableType type = flowTemplateComponentGlobalVariable.getType();
        String componentParamsValue = flowTemplateComponentGlobalVariable.getComponentParamsValue();
        ArrayList<Pair<Port, PortAndPortType>> ports = new ArrayList<>();
        if (TaskComponentGlobalVariableType.fixed.equals(type)) {
            getTaskPorts(ports, componentParamsValue);
            return ports;
        } else if (TaskComponentGlobalVariableType.variable.equals(type)) {
            FlowTemplateGlobalVariable byId = flowTemplateGlobalVariableService.getById(flowTemplateComponentGlobalVariable.getFlowTemplateGlobalVariableId());
            String variableKey = byId.getVariableKey();
            Object o = globalVariable.get(variableKey);
            if (o == null) {
                if (ToolUtil.isNotEmpty(forBeginNodes)) {
                    log.info("流程使用了循环组件，点位信息只能从循环的列表中获取！循环节点数量:{}", forBeginNodes.size());
                    for (WorkFlowNodeTemplateEntity forBeginNode : forBeginNodes) {
                        FlowTemplateComponentGlobalVariable flowTemplateComponentGlobalVariable1 = flowTemplateComponentGlobalVariableService.getOne(Wrappers.<FlowTemplateComponentGlobalVariable>lambdaQuery()
                                .eq(FlowTemplateComponentGlobalVariable::getFlowNodeTemplateId, forBeginNode.getId())
                                .eq(FlowTemplateComponentGlobalVariable::getComponentParamsKey, ForBeginComponent.InParamsKeyConstant.forParamsKey).last("limit 1"));
                        log.info("获取到的循环节点的变量信息：{}", JSON.toJSONString(flowTemplateComponentGlobalVariable1));
                        if (flowTemplateComponentGlobalVariable1 == null) {
                            throw new ServiceException("由于此流程使用了全局变量，但是创建任务时并没有传入该变量的值，并且没有给此全局变量设置默认值，因此无法执行此流程！节点ID编号：" + flowNodeTemplateEntity.getId() + "，节点名称：" + flowNodeTemplateEntity.getName() + "全局变量KEY：" + byId.getVariableKey());
                        }
                        String componentParamsValue1 = flowTemplateComponentGlobalVariable1.getComponentParamsValue();
                        log.info("获取到循环的参数列表:{}", componentParamsValue1);
                        ForBeginComponent.ForParams forParams = JSONObject.parseObject(componentParamsValue1, ForBeginComponent.ForParams.class);
                        String forItemKey = forParams.getForItemKey();
                        if (ToolUtil.isNotEmpty(forItemKey) && forItemKey.equals(variableKey)) {
                            String forNumType = forParams.getForType();
                            log.info("获取循环类型：{}", forNumType);
                            if (ForBeginComponent.ForTypeEnums.forEach.getCode().equals(forNumType)) {
                                log.info("确认是数组循环，可以获取到点位信息；");
                                TaskComponentGlobalVariableType forVariableValueType = forParams.getForVariableValueType();
                                log.info("确认是数组循环，获取到循环值类型：{}；", forVariableValueType);
                                if (TaskComponentGlobalVariableType.variable.equals(forVariableValueType)) {
                                    String forVariable = forParams.getForVariable();
                                    JSONArray jsonArray = globalVariable.getJSONArray(forVariable);
                                    log.info("循环节点的循环变量类型为变量，变量名称：{},获取到变量值：{}", forVariable, JSON.toJSONString(jsonArray));
                                    List<PortAndPortType> javaList = jsonArray.toJavaList(PortAndPortType.class);
                                    for (PortAndPortType portAndPortType : javaList) {
                                        Pair<Port, PortAndPortType> portStringPair = new Pair<>(AbstractBizCommonComponent.getPortInfo(portAndPortType), portAndPortType);
                                        ports.add(portStringPair);
                                    }
                                } else if (TaskComponentGlobalVariableType.fixed.equals(forVariableValueType)) {
                                    String forVariable = forParams.getForVariable();
                                    log.info("循环节点的循环变量类型为固定值：{}", forVariable);
                                    List<PortAndPortType> portAndPortTypes = JSONArray.parseArray(forVariable, PortAndPortType.class);
                                    for (PortAndPortType portAndPortType : portAndPortTypes) {
                                        Pair<Port, PortAndPortType> portStringPair = new Pair<>(AbstractBizCommonComponent.getPortInfo(portAndPortType), portAndPortType);
                                        ports.add(portStringPair);
                                    }
                                }
                            }
                        }
                    }
                }
                String variableValue = byId.getVariableValue();
                if (StrUtil.isBlank(variableValue)) {
                    log.error("由于此流程使用了全局变量，但是创建任务时并没有传入该变量的值，并且没有给此全局变量设置默认值，因此无法执行此流程！节点ID编号：" + flowNodeTemplateEntity.getId() + "，节点名称：" + flowNodeTemplateEntity.getName() + "全局变量KEY：" + byId.getVariableKey());
                    return ports;
                }
                try {
                    PortAndPortType taskPort = getTaskPorts(ports, variableValue);
                    globalVariable.put(variableKey, taskPort);
                } catch (Exception e) {
                    log.error("获取料口信息发生异常，异常信息：{}", Throwables.getStackTraceAsString(e));
//                    throw new ServiceException("没有从接口传入的全局变量中获取到此节点的参数，当前从流程模板配置的全局变量默认值中获取到的参数格式不正确，请按照文档给出的参数结构进行传参！获取到的料口列表参数：" + variableValue);
                }
                return ports;
//                }
            } else {
                if (o instanceof String) {
                    String variableValue = (String) o;
                    try {
                        getTaskPorts(ports, variableValue);
                    } catch (Exception e) {
                        log.error("获取料口信息发生异常，异常信息：{}", Throwables.getStackTraceAsString(e));
                        throw new ServiceException("从接口传入的全局变量中获取到的参数格式不正确，请按照文档给出的参数结构进行传参！当前获取到的料口列表参数：" + variableValue);
                    }
                }
                if (o instanceof JSONObject) {
                    JSONObject variableString = (JSONObject) o;
                    try {
                        PortAndPortType portAndPortType = JSON.parseObject(JSON.toJSONString(variableString), PortAndPortType.class);
                        Pair<Port, PortAndPortType> portStringPair = new Pair<>(AbstractBizCommonComponent.getPortInfo(portAndPortType), portAndPortType);
                        ports.add(portStringPair);
                    } catch (Exception e) {
                        log.error("获取料口信息发生异常，异常信息：{}", Throwables.getStackTraceAsString(e));
                        throw new ServiceException("获取到的参数格式不正确，请按照文档给出的参数结构进行传参！当前获取到的料口列表参数：" + variableString);
                    }
                }
                if (o instanceof List) {
                    try {
                        for (Object o1 : (List) o){
                            getTaskPorts(ports, JSON.toJSONString(o1));
                        }

                    } catch (Exception e) {
                        log.error("获取到的参数格式不正确，请按照文档给出的参数结构进行传参！当前获取到的料口列表参数：{},异常信息：{}", JSON.toJSONString(o), Throwables.getStackTraceAsString(e));
                        throw new ServiceException("获取到的参数格式不正确，请按照文档给出的参数结构进行传参！当前获取到的料口列表参数：" + JSON.toJSONString(o));
                    }
                }
                if (o instanceof PortAndPortType) {
                    try {
                        Pair<Port, PortAndPortType> portStringPair = new Pair<>(AbstractBizCommonComponent.getPortInfo((PortAndPortType)o), (PortAndPortType)o);
                        ports.add(portStringPair);;
                    } catch (Exception e) {
                        log.error("获取到的参数格式不正确，请按照文档给出的参数结构进行传参！当前获取到的料口列表参数：{},异常信息：{}", JSON.toJSONString(o), Throwables.getStackTraceAsString(e));
                        throw new ServiceException("获取到的参数格式不正确，请按照文档给出的参数结构进行传参！当前获取到的料口列表参数：" + JSON.toJSONString(o));
                    }
                }
            }
            return ports;
        } else {
            throw new ServiceException("取料节点没有选择固定的子流程编号和全局变量使用的是节点输出变量，因此无法获取到子流程信息导致无法推断出流程的具体的点位信息！节点ID编号：" + flowNodeTemplateEntity.getId() + "，节点名称：" + flowNodeTemplateEntity.getName());
        }
    }

    /**
     * 获取任务的料口数组信息
     *
     * @param ports
     * @param variableValue
     */
    private PortAndPortType getTaskPorts(ArrayList<Pair<Port, PortAndPortType>> ports, String variableValue) {
        PortAndPortType portAndPortType = JSON.parseObject(variableValue, PortAndPortType.class);
        Pair<Port, PortAndPortType> portStringPair = new Pair<>(AbstractBizCommonComponent.getPortInfo(portAndPortType), portAndPortType);
        ports.add(portStringPair);
        return portAndPortType;
    }

    /**
     * 创建任务节点
     *
     * @param task                       任务信息
     * @param workFlowNodeTemplateEntity 流程节点模板
     * @return
     */
    private List<TaskNode> createTaskNode(Task task, WorkFlowNodeTemplateEntity workFlowNodeTemplateEntity, List<WorkFlowNodeTemplateEntity> forBeginNodes) {
        List<TaskNode> taskNodes = new ArrayList<>();
        List<Pair<Port, PortAndPortType>> nodePorts = getNodePorts(workFlowNodeTemplateEntity, task.getGlobalVariable(), forBeginNodes);
        for (Pair<Port, PortAndPortType> nodePort : nodePorts) {
            TaskNode taskNode = new TaskNode();
            taskNode.setTaskId(task.getId());
            taskNode.setTaskCode(task.getCode());
            taskNode.setPortType(nodePort.getKey().getPortType());
            taskNode.setPortId(nodePort.getKey().getId());
            taskNode.setPortCode(nodePort.getKey().getCode());
            taskNode.setCarrierCode(nodePort.getValue().getCarrierCode());
            taskNode.setCustomTaskCode(nodePort.getValue().getCustomerTaskCode());
            taskNode.setActionType(nodePort.getValue().getActionType());
            taskNode.setStatus(TaskNodeStatus.NOT_START);
            taskNode.setRecordSeq( nodePort.getValue().getIndex());
            taskNodes.add(taskNode);
        }
        return taskNodes;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(Task task) {
        //查询起点、终点、交换点信息
        bindPortName(task);
        boolean result = super.save(task);
        recordDataScope(task);
//        addVariable(task);
//        workFlowExecutor.executor(task.getFlowId());
        return result;
    }

    private void addVariable(Task task) {
        WorkFlowTemplateEntity template = flowTemplateAssistService.getDefaultFlowTemplate();
        if (template == null) {
            throw new BusinessException(BizErrorCode.FLOW_TEMPLATE_DEFAULT_NOT_EXIST);
        }
        JSONObject taskGlobalVariable = new JSONObject();
//        taskGlobalVariable.put("pickUpPortIds", Arrays.asList(new PortAndPortType(task.getStartId().toString(), task.getStartType().name())));
//        taskGlobalVariable.put("putDownPortIds", Arrays.asList(new PortAndPortType(task.getDestinationId().toString(), task.getDestinationType().name())));
        taskGlobalVariable.put(GlobalVariableKeys.taskCodeKey, task.getCode());
        WorkFlow workFlow = flowTemplateAssistService.initWorkflow(template.getId(), taskGlobalVariable, commonWorkFlowListener);
        task.setFlowId(workFlow.getId());
        task.setFlowTemplateId(template.getId());
        task.setGlobalVariable(taskGlobalVariable);
        this.updateById(task);
    }


    public Task createTaskFromTemplate(long templateId, TaskSource taskSource) {
        TaskTemplate template = taskTemplateMapper.selectById(templateId);
        if (template == null) {
            log.error("无效的任务模板[{}]", templateId);
            return null;
        }
        return createTaskFromTemplate(template, taskSource);
    }

    public Task createTaskFromTemplate(TaskTemplate template, TaskSource taskSource) {
        Task existed = findExecutingTaskByTemplate(template.getId());
        if (existed != null) {
            log.warn("任务模板[{}],已生成任务[{}]正在执行中，不再重复生成", template.getName(), existed.getCode());
            return null;
        }

        Task task = new Task();
        BeanUtils.copyBeanProp(template, task);
        task.setId(null);//上面的BeanUtils.copyBeanProp会把模板的ID复制到任务的ID，任务ID置null后，在save时会自动创建
        task.setCode(this.incrementCode());
        //默认未开始状态
        task.setSource(taskSource);
        task.setTaskTemplateId(template.getId());//记录模板ID
        this.save(task);
        return task;
    }


    private Task findExecutingTaskByTemplate(Long taskTemplateId) {
        return taskMapper.selectOne(new LambdaQueryWrapper<Task>()
                .eq(Task::getTaskTemplateId, taskTemplateId)
                .eq(Task::getDelFlag, 0)
                .in(Task::getStatus, TaskStatus.NOT_START, TaskStatus.EXECUTING, TaskStatus.ALLOTTING));
    }


    /**
     * 关联查询起点、终点、交换点的名称，并赋值到task中去
     *
     * @param task
     */
    private void bindPortName(Task task) {
        Port start;
        if (task.getStartId() == null && task.getStartCode() == null) {
            start = null;
        } else {
            if (task.getStartId() == null || task.getStartId() == 0L) {
                start = portService.getByCode(task.getStartCode());
            } else {
                start = portService.getByTypeAndId(task.getStartType(), task.getStartId());
            }
        }

        if (start != null) {
            task.setStartId(start.getId());
            task.setStartType(start.getPortType());
            task.setStartName(start.getFullName());
        }

        Port destination;
        if (task.getDestinationId() == null && task.getDestinationCode() == null) {
            destination = null;
        } else {
            if (task.getDestinationId() == null || task.getDestinationId() == 0L) {
                destination = portService.getByCode(task.getDestinationCode());
            } else {
                destination = portService.getByTypeAndId(task.getDestinationType(), task.getDestinationId());
            }
        }

        if (destination != null) {
            task.setDestinationId(destination.getId());
            task.setDestinationType(destination.getPortType());
            task.setDestinationName(destination.getFullName());
        }

        if (TaskType.SWAP.equals(task.getType())) {
            Port swap = null;
            if (task.getSwapId() == null || task.getSwapId() == 0L) {
                swap = portService.getByCode(task.getSwapCode());
            } else {
                swap = portService.getByTypeAndId(task.getSwapType(), task.getSwapId());
            }

            if (swap == null) {
                throw new BusinessException(BizErrorCode.TASK_SWAP_INVALID);
            } else {
                task.setSwapId(swap.getId());
                task.setSwapType(swap.getPortType());
                task.setSwapName(swap.getFullName());
            }
        }

    }


    /**
     * 记录任务所属的数据权限
     *
     * @param task
     */
    private void recordDataScope(Task task) {
        Set<Long> zoneIds = new HashSet<>();
        if (ToolUtil.isNotEmpty(task.getStartId())) {
            Long startZoneId = portService.getZoneId(task.getStartType(), task.getStartId());
            zoneIds.add(startZoneId);
        }
        if (ToolUtil.isNotEmpty(task.getDestinationId())) {
            Long destinationZoneId = portService.getZoneId(task.getDestinationType(), task.getDestinationId());
            zoneIds.add(destinationZoneId);
        }
        if (ToolUtil.isNotEmpty(zoneIds)) {
            taskZoneMapper.removeByTaskId(task.getId());
            //遍历并保存
            for (Long zoneId : zoneIds) {
                TaskZone taskZone = new TaskZone();
                taskZone.setTaskId(task.getId());
                taskZone.setZoneId(zoneId);
                taskZoneMapper.insert(taskZone);
            }
        }
    }


    @Override
    public synchronized String incrementCode() {
        Date now = new Date();
        long sn = redisCache.increment(ORDER_CODE_INCREMENT_KEY);
        return "T" + DATE_FORMAT.format(now) + String.format(FORMAT, sn);
    }

    @Override
    public Task selectByTaskCode(String taskCode) {
        return taskMapper.selectByTaskCode(taskCode);
    }

    @Override
    public List<TaskStatistics> countTaskByHour(String date) {
        return taskMapper.countTaskByHour(date);
    }

    /**
     * 给任务分配空闲的机器人
     *
     * @param task         任务信息
     * @param freeVehicles 空闲的机器人列表
     * @return
     */
    @Override
    public String distributionVehicle(Task task, List<String> freeVehicles) {
        if (ToolUtil.isEmpty(freeVehicles)) {
            log.warn("Fleet没有空闲的机器人！");
            return null;
        }
        TaskStatus status = task.getStatus();
        if (status.equals(TaskStatus.NOT_START)) {
            task.setStatus(TaskStatus.ALLOTTING);
            this.updateById(task);
        }
        List<String> agvCodes = task.getAgvCodes();
        if (ToolUtil.isNotEmpty(agvCodes)) {
            for (String freeVehicle : freeVehicles) {
                if (agvCodes.contains(freeVehicle)) {
                    return freeVehicle;
                }
            }
        } else {
            Long startId = task.getStartId();
            PortType startType = task.getStartType();
            if (PortType.PORT_EQ.equals(startType)) {
                return getVehicleByPortEq(startId, task, freeVehicles);
            } else {
                return getVehicleByPortStocker(startId, task, freeVehicles);
            }
        }
        log.warn("没有匹配到空闲的机器人！");
        return null;
    }

    /**
     * 根据料口信息获取关联可用的机器人列表，假如获取不到，那么更新任务状态为派车失败，并且记录失败信息
     *
     * @param startId
     * @param task
     * @param freeVehicles
     * @return
     */
    private String getVehicleByPortEq(Long startId, Task task, List<String> freeVehicles) {
        PortEq portEq = getPortEq(startId, task);
        if (ToolUtil.isNotEmpty(portEq)) {
            Long zoneAreaId = portEq.getZoneAreaId();
            ZoneArea zoneArea = getZoneArea(zoneAreaId, task);
            if (ToolUtil.isNotEmpty(zoneArea)) {
                List<String> agvCodes = getAgvCodes(zoneArea, task);
                if (ToolUtil.isNotEmpty(agvCodes)) {
                    for (String freeVehicle : freeVehicles) {
                        if (agvCodes.contains(freeVehicle)) {
                            return freeVehicle;
                        }
                    }
                    task.setErrorMessage("没有匹配到对应的机器人，任务起点关联的机器人列表为：" + JSON.toJSONString(agvCodes) + "空闲的机器人列表为:" + JSON.toJSONString(freeVehicles));
                    this.updateById(task);
                }
            }
        }

        return null;
    }

    /**
     * 根据库位信息获取关联可用的机器人列表，假如获取不到，那么更新任务状态为派车失败，并且记录失败信息
     *
     * @param startId
     * @param task
     * @param freeVehicles
     * @return
     */
    private String getVehicleByPortStocker(Long startId, Task task, List<String> freeVehicles) {
        PortStocker portStocker = getPortStocker(startId, task);
        if (ToolUtil.isNotEmpty(portStocker)) {
            Long zoneStockerId = portStocker.getZoneStockerId();
            ZoneStocker zoneStocker = getZoneStocker(zoneStockerId, task);
            if (ToolUtil.isNotEmpty(zoneStocker)) {
                List<String> agvCodes = getAgvCodes(zoneStocker, task);
                if (ToolUtil.isNotEmpty(agvCodes)) {
                    for (String freeVehicle : freeVehicles) {
                        if (agvCodes.contains(freeVehicle)) {
                            return freeVehicle;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取料口信息，假如获取不到，那么更新任务状态为派车失败，并且记录失败信息
     *
     * @param id
     * @param task
     * @return
     */
    private PortEq getPortEq(Long id, Task task) {
        PortEq portEq = portEqMapper.selectById(id);
        if (ToolUtil.isEmpty(portEq)) {
            task.setStatus(TaskStatus.ERROR);
            task.setErrorMessage("根据任务的起点ID查询不到料口信息!");
            this.updateById(task);
            log.error("根据任务的起点ID查询不到料口信息，任务编号:{}", task.getCode());
        }
        return portEq;
    }

    /**
     * 获取区域信息，假如获取不到，那么更新任务状态为派车失败，并且记录失败信息
     *
     * @param id
     * @param task
     * @return
     */
    private ZoneArea getZoneArea(Long id, Task task) {
        ZoneArea zoneArea = zoneAreaService.getById(id);
        if (ToolUtil.isEmpty(zoneArea)) {
            task.setStatus(TaskStatus.ERROR);
            task.setErrorMessage("根据起点的料口信息查询不到区域信息!");
            this.updateById(task);
            log.error("根据起点的料口信息查询不到区域信息，任务编号:{}", task.getCode());
        }
        return zoneArea;
    }

    /**
     * 获取区域关联的机器人列表信息，假如获取不到，那么更新任务状态为派车失败，并且记录失败信息
     *
     * @param zoneArea
     * @param task
     * @return
     */
    private List<String> getAgvCodes(ZoneArea zoneArea, Task task) {
        List<String> agvCodes = zoneArea.getAgvCodes();
        if (ToolUtil.isEmpty(agvCodes)) {
            task.setStatus(TaskStatus.ERROR);
            task.setErrorMessage("根据任务起点对应的区域信息没有关联机器人!");
            this.updateById(task);
            log.error("根据任务起点对应的区域信息没有关联机器人，不予分配，任务编号:{}", task.getCode());
        }
        return agvCodes;
    }

    /**
     * 获取库位信息，假如获取不到，那么更新任务状态为派车失败，并且记录失败信息
     *
     * @param id
     * @param task
     * @return
     */
    private PortStocker getPortStocker(Long id, Task task) {
        PortStocker portStocker = portStockerMapper.selectById(id);
        if (ToolUtil.isEmpty(portStocker)) {
            task.setStatus(TaskStatus.ERROR);
            task.setErrorMessage("根据任务的起点ID查询不到库位信息!");
            this.updateById(task);
            log.error("根据任务的起点ID查询不到库位信息，任务编号:{}", task.getCode());
        }
        return portStocker;
    }

    /**
     * 获取库区信息，假如获取不到，那么更新任务状态为派车失败，并且记录失败信息
     *
     * @param id
     * @param task
     * @return
     */
    private ZoneStocker getZoneStocker(Long id, Task task) {
        ZoneStocker zoneStocker = zoneStockerService.getById(id);
        if (ToolUtil.isEmpty(zoneStocker)) {
            task.setStatus(TaskStatus.ERROR);
            task.setErrorMessage("根据起点的料口信息查询不到库区信息!");
            this.updateById(task);
            log.error("根据起点的料口信息查询不到库区信息，任务编号:{}", task.getCode());
        }
        return zoneStocker;
    }

    /**
     * 获取库区关联的机器人列表，假如获取不到，那么更新任务状态为派车失败，并且记录失败信息
     *
     * @param zoneStocker
     * @param task
     * @return
     */
    private List<String> getAgvCodes(ZoneStocker zoneStocker, Task task) {
        List<String> agvCodes = zoneStocker.getAgvCodes();
        if (ToolUtil.isEmpty(agvCodes)) {
            task.setStatus(TaskStatus.ERROR);
            task.setErrorMessage("根据任务起点对应的库区信息没有关联机器人!");
            this.updateById(task);
            log.error("根据任务起点对应的库区信息没有关联机器人，不予分配，任务编号:{}", task.getCode());
        }
        return agvCodes;
    }

    @Override
    public long countNotStart() {
        LambdaQueryWrapper<Task> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Task::getStatus, new TaskStatus[]{TaskStatus.NOT_START, TaskStatus.ALLOTTING});
        queryWrapper.eq(Task::getDelFlag, 0);
        return this.baseMapper.selectCount(queryWrapper);
    }

    public List<Task> listNotStart() {
        LambdaQueryWrapper<Task> taskLambdaQueryWrapper = Wrappers.<Task>lambdaQuery()
                .in(Task::getStatus, Arrays.asList(TaskStatus.NOT_START, TaskStatus.ALLOTTING))
                .orderByAsc(Task::getPriority)
                .orderByAsc(Task::getCreateTime);
        return this.list(taskLambdaQueryWrapper);
    }


    public Port getStartPort(Task task) {
        Long portId = task.getStartId();
        if (portId == null) {
            return null;
        }
        PortType portType = task.getStartType();
        if (PortType.PORT_EQ.equals(portType)) {
            return portEqMapper.selectById(portId);
        } else {
            return portStockerMapper.selectById(portId);
        }
    }

    public Port getDestinationPort(Task task) {
        Long portId = task.getDestinationId();
        if (portId == null) {
            return null;
        }
        PortType portType = task.getDestinationType();
        if (PortType.PORT_EQ.equals(portType)) {
            return portEqMapper.selectById(portId);
        } else {
            return portStockerMapper.selectById(portId);
        }
    }

    public Port getSwapPort(Task task) {
        Long portId = task.getSwapId();
        if (portId == null) {
            return null;
        }
        PortType portType = task.getSwapType();
        if (PortType.PORT_EQ.equals(portType)) {
            return portEqMapper.selectById(portId);
        } else {
            return portStockerMapper.selectById(portId);
        }
    }


    @Override
    public List<String> getNeedAgv(Task task) {
        List<TaskNode> taskNodes = taskNodeService.list(Wrappers.<TaskNode>lambdaQuery().eq(TaskNode::getTaskCode, task.getCode()));
        HashSet<String> agvTypes = new HashSet<>();
        List<String> agvCodes = new ArrayList<>();
        for (int i = 0; i < taskNodes.size(); i++) {
            TaskNode taskNode = taskNodes.get(i);
            Long portId = taskNode.getPortId();
            PortType portType = taskNode.getPortType();
            Port port;
            if (PortType.PORT_EQ.equals(portType)) {
                port = portEqMapper.selectById(portId);
            } else {
                port = portStockerMapper.selectById(portId);
            }
            if (port != null) {
                String agvType = port.getAgvType();
                agvTypes.add(agvType);
                List<String> needAgvByPort = getNeedAgvByPort(port);
                if (i == 0) {
                    agvCodes = needAgvByPort;
                } else {
                    agvCodes.retainAll(needAgvByPort);
                }
            }
        }
        //查询任务中已经指定的AGV
        List<String> needAgvCodes = task.getNeedAgvCodes();
        if (ToolUtil.isNotEmpty(needAgvCodes)) {
            //获取交集
            agvCodes.retainAll(needAgvCodes);
        }
        if (agvTypes.size() == 1) {
            return agvCodes;
        } else {
            return null;
        }
    }

    @Override
    public List<String> getNeedAgvByPort(Port port) {
        PortType portType = port.getPortType();
        if (PortType.PORT_EQ.equals(portType)) {
            PortEq startPortEq = (PortEq) port;
            Long zoneAreaId = startPortEq.getZoneAreaId();
            ZoneArea zoneArea = zoneAreaService.getById(zoneAreaId);
            return zoneArea.getAgvCodes();
        } else {
            PortStocker startPortStocker = (PortStocker) port;
            Long zoneStockerId = startPortStocker.getZoneStockerId();
            ZoneStocker zoneStocker = zoneStockerService.getById(zoneStockerId);
            return zoneStocker.getAgvCodes();
        }
    }

    @Override
    public String getStartMarkerId(Task task) {
        Port port = getStartPort(task);
        ActionParam actionParam = port.getActionParam().get(0);
        return actionParam.getMarkerId();
    }

    @Autowired
    private FleetProxyService fleetProxyService;
    @Resource
    private AgvService agvService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;




    @Override
    public void stopTask(String taskId) {
        log.info("开始停止任务");
        Task task = taskMapper.selectById(taskId);
        if (task == null) {
            task = taskMapper.selectByTaskCode(taskId);
            if (task == null) {
                throw new BusinessException(BizErrorCode.TASK_ID_INVALID);
            }
        }

        applicationEventPublisher.publishEvent(new WorkFlowStopEvent(task.getFlowId()));

        String agvCode = task.getAgvCode();
        TaskStatus status = task.getStatus();
        if (TaskStatus.CANCEL.equals(status) || TaskStatus.SUCCESS.equals(status)) {
            throw new ServiceException("任务已经结束，无法停止任务！");
        }
        String taskCode = task.getCode();
        if (TaskStatus.ALLOTTING.equals(status) || TaskStatus.NOT_START.equals(status)) {
            log.info("任务处于派车中，取消派车！");
            taskDispatchAgvService.checkAndCancelDispatchAgv(taskCode);
            workFlowExecutor.clearCache(task.getFlowId());
        } else {
            if (ToolUtil.isNotEmpty(agvCode)) {
                Agv agv = agvService.selectByAgvCode(agvCode);
                //判断当前锁定此机器人的是不是此任务，假如是此任务则先停止该机器人车身上的所有任务，再开启自动泊车和自动充电，然后再释放机器人
                if (ToolUtil.isNotEmpty(agv.getTaskCodes()) && agv.getTaskCodes().contains(taskCode)) {
                    log.info("开始停止AGV身上所有的任务！");
                    //一键停止AGV当前车身上的任务
                    fleetProxyService.restartTaskPrevOneKeyStopAllAgvRunningMission(agvCode);
                    log.info("停止AGV身上所有的任务完成！");
                    //开启自动充电和泊车
                    fleetProxyService.openAutoParkAndCharge(agvCode);
                    //释放机器人
                    agvService.releaseAgvAndReleaseAgvStoragePort(agvCode);
                }
            }
            try {
                workFlowExecutor.stop(task.getFlowId());
            } catch (Exception e) {
                log.error("取消任务停止流程时发生异常，异常信息：{}", Throwables.getStackTraceAsString(e));
            }
        }
        //将任务状态 改为 已取消
        task.setStatus(TaskStatus.CANCEL);
        this.updateById(task);
    }

}
