package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "FleetMission", description = "任务")
public class MissionDTO {

 
    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "任务组ID", position = 1)
    private String missionGroupId;

 
    @ApiModelProperty(value = "任务名称", example = "自动运输任务", position = 2)
    private String name;


    @ApiModelProperty(value = "任务编号", example = "M001", position = 4)
    private String code;


    @ApiModelProperty(value = "任务描述", example = "自动运输任务", position = 3)
    private String description;


    @ApiModelProperty(value = "机器人的ID,任务指定执行的机器人ID.", position = 4)
    private String agvCode;

  
    @ApiModelProperty(value = "机器人组的ID,任务只会分配给组内的机器人", position = 5)
    private String agvGroupId;

   
    @ApiModelProperty(value = "机器人类型,任务只会分配给类型内的机器人", position = 6)
    private String agvType;

    @ApiModelProperty(value = "优先级,1:低，2：普通，3：高，4：最高. 默认是2", example = "2", position = 7)
    private Integer sequence = 4;

   
    @ApiModelProperty(value = "是否可中断,True:任务可以被更高优级的新任务中断，False:必须执行完成后才能执行其他的任务。", example = "false", position = 8)
    private Boolean interrupt = false ;

    
    @ApiModelProperty(value = "创建时间", position = 9)
    private Date createTime;


    @ApiModelProperty(value = "更新时间", position = 10)
    private Date updateTime;

   
    @ApiModelProperty(value = "任务版本号", position = 11)
    private Integer version;


    
    @ApiModelProperty(value = "0:未删除、1删除", position = 12)
    private Integer isDeleted;

   
    @ApiModelProperty(value = "任务排序字段", position = 13)
    private Integer orderBy;

}
