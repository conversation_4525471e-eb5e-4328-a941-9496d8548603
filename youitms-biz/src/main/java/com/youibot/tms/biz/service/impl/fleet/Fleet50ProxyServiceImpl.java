package com.youibot.tms.biz.service.impl.fleet;

import com.github.pagehelper.PageInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.api.dto.*;
import com.youibot.tms.biz.api.request.TaskStatisticsRequest;
import com.youibot.tms.biz.api.request.VehicleUtilizationRequest;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.flow.dto.FleetAndTmsTaskDTO;
import com.youibot.tms.biz.forest.Fleet5Client;
import com.youibot.tms.biz.forest.dto.*;
import com.youibot.tms.biz.forest.dto.MissionWorkStatisticDTO;
import com.youibot.tms.biz.forest.dto.AgvTypeDTO;
import com.youibot.tms.biz.forest.dto.fleet5.*;
import com.youibot.tms.biz.forest.enums.VehicleStatus;
import com.youibot.tms.biz.forest.request.*;
import com.youibot.tms.biz.mapper.FromToRecordMapper;
import com.youibot.tms.biz.service.FleetMissionWorkExecutingQueue;
import com.youibot.tms.biz.service.FleetProxyService;
import com.youibot.tms.biz.service.TaskStatisticsService;
import com.youibot.tms.biz.service.FromToRecordService;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.Map;
import com.youibot.tms.common.constant.StatisticsConstants;


/**
 * <AUTHOR>
 * @ClassName: Fleet48ProxyServiceImpl
 * @Description: fleet请求相关辅助类
 * @date 2021-03-03 11:47:13
 */
@Slf4j
@Component("fleetProxyService")
@ConditionalOnExpression(value = "(!'${fleet.version}'.equals('simulation') )&& ${fleet.version}==5.0")
public class Fleet50ProxyServiceImpl implements FleetProxyService {

    @Resource
    private Fleet5Client fleet5Client;

    @Resource
    private TaskStatisticsService taskStatisticsService;

    @Value("${fleet.version:}")
    private String version;

    @Resource
    private FleetMissionWorkExecutingQueue fleetMissionWorkExecutingQueue;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private FromToRecordService fromToRecordService;

    @Resource
    private FromToRecordMapper fromToRecordMapper;

    /**
     * 并发调用Fleet接口的线程池
     */
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @PostConstruct
    public void init() {
        log.info("fleet.version={},启用Fleet50ProxyServiceImpl", version);
    }

    @Override
    public String version() {
        FleetResult<SystemVersionDTO> result = fleet5Client.version();
        if (result != null && result.getData() != null && result.getData().getSystemVersion()!=null) {
            return result.getData().getSystemVersion();
        }
        return null;
    }

    @Override
    public String testVersion(String host) {
        try {
            FleetResult<SystemVersionDTO> result = fleet5Client.version(host);
            if (result != null && result.getData() != null && result.getData().getSystemVersion()!=null) {
                return result.getData().getSystemVersion();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("请求fleet版本失败", e);
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED, host);
        }
    }

    @Override
    public String getReqUrl(String flowId, String flowNodeId) {
        String tmsUrl = sysConfigService.selectConfigValueByKey("sys.url");
        if (!tmsUrl.endsWith("/")) {
            tmsUrl = tmsUrl + "/";
        }
        String path = "api/v1/open/workflow/callback/fleet5/task/midReq/" + flowId + "/" + flowNodeId;

        return tmsUrl + path;
    }

    @Override
    public List<AgvDTO> listAllAgvs() {
        List<VehicleDetailApiDTO> vehicleDetails = null;
        try {
            vehicleDetails = getAllVehicleDetails(null);
            if(vehicleDetails == null || vehicleDetails.size() == 0){
                return Lists.newArrayList();
            }
        } catch (Exception e) {
            log.error("error:{}" , Throwables.getStackTraceAsString(e));
            return Lists.newArrayList();
        }
        return vehicleDetails.stream().map(this::fleetDetailToAgvDTO).collect(Collectors.toList());
    }

    /**
     * 将VehicleDetailApiDTO转换为AgvDTO，确保所有属性都被正确复制
     *
     * @param detail VehicleDetailApiDTO对象
     * @return 包含所有VehicleDetailApiDTO属性的AgvDTO对象
     */
    private AgvDTO fleetDetailToAgvDTO(VehicleDetailApiDTO detail) {
        if (detail == null) {
            log.warn("转换为AgvDTO时源对象detail为null");
            return new AgvDTO();
        }

        // 使用新的构造方法创建AgvDTO对象
        // 这个构造方法会自动复制VehicleDetailApiDTO的所有属性
        return new AgvDTO(detail);
    }

    @Override
    public List<AgvDTO> listOnlineAgvs() {

        List<AgvDTO> allAgv = listAllAgvs();
        return allAgv.stream().filter(agv -> agv.getStatus() != VehicleStatus.OFFLINE).collect(Collectors.toList());
    }


    private List<VehicleDetailApiDTO> getAllVehicleDetails(List<String> agvCodes) {
        List<String> vehicleCodes = null;
        //如果传入的agvCodes为空，则先获取所有agvCode
        if(agvCodes == null || agvCodes.size() == 0){
            FleetResult<List<VehicleApiDTO>> result = fleet5Client.listVehicles();
            if(result.getCode() != 0){
                log.error("获取机器人列表失败,code={},msg={}", result.getCode(), result.getMsg());
                return null;
            }
            List<VehicleApiDTO> vehicleApiDTOS = result.getData();
            vehicleCodes = vehicleApiDTOS.stream().map(VehicleApiDTO::getVehicleCode).collect(Collectors.toList());
        }else{
            vehicleCodes = agvCodes;
        }

        return getVehicleDetailApiDTOS(vehicleCodes);
    }

    @Override
    public List<VehicleDetailApiDTO> getVehicleDetailApiDTOS(List<String> vehicleCodes) {
        VehicleQueryApiDTO query = new VehicleQueryApiDTO();
        query.setVehicleCodes(vehicleCodes);
        FleetResult<List<VehicleDetailApiDTO>> detailResult = fleet5Client.vehicleDetailList(query);
        if(detailResult.getCode() != 0){
            log.error("获取机器人详情列表失败,code={},msg={}", detailResult.getCode(), detailResult.getMsg());
            return null;
        }
        return detailResult.getData();
    }

    @Override
    public List<AgvDTO> listFreeAgvs() {
        List<AgvDTO> allAgvs = listAllAgvs();
        return allAgvs.stream().filter(agv -> {
            return VehicleStatus.IDLE.equals(agv.getStatus());
        }).collect(Collectors.toList());
    }
    @Override
    public List<AgvDTO> listFreeAgvs2() {
        List<AgvDTO> allAgvs = listAllAgvs();
        return  allAgvs ;
    }


    @Override
    public FreeAgvAndNotFreeReasonRO freeAndNotFreeReason() {
        FleetResult<VehicleAvaliableStateDTO> fleetResult = fleet5Client.getVehicleAvailableState();
        FreeAgvAndNotFreeReasonRO result = new FreeAgvAndNotFreeReasonRO();
        if(fleetResult.getCode()==0){

            List<String> agvCodes = fleetResult.getData().getAvaliableVehicleList();
            if(agvCodes!=null && agvCodes.size()>0) {
                List<VehicleDetailApiDTO> agvDetails = getAllVehicleDetails(agvCodes);
                if(agvDetails!=null && agvDetails.size()>0){
                    result.setFreeVehicles(agvDetails.stream().map(this::fleetDetailToAgvDTO).collect(Collectors.toList()));
                }
            }
            if(fleetResult.getData().getUnavaliableVehicleList()!=null && fleetResult.getData().getUnavaliableVehicleList().size()>0){
                Map<String, String> notFreeReasonMap = fleetResult.getData().getUnavaliableVehicleList().stream().collect(Collectors.toMap(VehicleAvaliableStateDTO.UnavaliableVehicleInfo::getCode, VehicleAvaliableStateDTO.UnavaliableVehicleInfo::getReason));
                result.setNotFreeReasonMap(notFreeReasonMap);
            }
        }
        return result;
    }


    /**
     * 传入一个导航点编号和机器人列表,从传入的机器人列表中获取距离这个导航点最近的机器人
     */
    @Override
    public String getAgvCodesSequence(String mapName, List<String> agvCodes, String markerCode) {
        log.info("查找最近的机器人,agvCodes={},markerCode={}",  agvCodes, markerCode);
        if(ToolUtil.isEmpty(agvCodes)){
            return null;
        }else if(agvCodes.size()==1){
            //只有一个机器人,不用排序，直接返回
            return agvCodes.get(0);
        }
        VehicleSortQueryApiDTO request = new VehicleSortQueryApiDTO();
        request.setVehicleCodes(agvCodes);
        request.setMarkerCode(markerCode);//fleet5的导航点编号包含了地图编号，不需要再传地图编号
        FleetResult<List<String>> result = fleet5Client.getSortedVehicleListByDistance(request);

        if(result.getCode() == 0 && result.getData()!=null && result.getData().size()>0){
            log.info("排序后的agvCodes={}", result.getData());
            return result.getData().get(0);
        }else{
            return null;
        }
    }

    @Override
    public List<MarkerDTO> getAllMarker(MarkerQueryRequest queryRequest) {

        MarkerListRequest query = new MarkerListRequest();
        query.setVehicleMapCode(queryRequest.getAgvMapName());
        FleetResult<List<MarkerForTmsDTO>> result = fleet5Client.getMarkerList(query);
        if(result.getCode()==0){
            List<MarkerForTmsDTO> markers = result.getData();
            if(markers==null || markers.size()==0){
                return null;
            }
            return markers.stream().map(markerApiDTO -> {
                MarkerDTO markerDTO = new MarkerDTO();
                markerDTO.setCode(markerApiDTO.getCode());
                markerDTO.setName(markerApiDTO.getCode());
                markerDTO.setId(markerApiDTO.getCode());
                return markerDTO;
            }).collect(Collectors.toList());
        }

        return null;
    }



    @Override
    public MissionWorkDTO createMissionWork(FleetAndTmsTaskDTO fleetAndTmsTaskDTO) {
        log.info("创建Fleet任务,任务参数：{}，流程节点ID：{}", fleetAndTmsTaskDTO.getMissionWorkParam(), fleetAndTmsTaskDTO.getFlowNodeId());

        MissionWorkParam missionWorkParam = fleetAndTmsTaskDTO.getMissionWorkParam();
        TaskExecuteApiDTO request = new TaskExecuteApiDTO();
        request.setTaskTypeCode(missionWorkParam.getMissionId());
        request.setCallbackUrl(missionWorkParam.getCallbackUrl());
        JSONObject runtimeParamObject = missionWorkParam.getRuntimeParamObject();
        if(runtimeParamObject==null){
            runtimeParamObject = new JSONObject();
        }
        runtimeParamObject.put("agvCode", missionWorkParam.getAgvCode());
        request.setParams(runtimeParamObject);

        FleetResult<TaskDetailApiDTO> result = fleet5Client.createTask(request);

        if(result.getCode() != 0){
            log.error("创建任务失败,code={},msg={}", result.getCode(), result.getMsg());

            BusinessException businessException = new BusinessException(BizErrorCode.FLEET_API_ERROR, result.getCode() + "", result.getMsg());
            businessException.setMsgs( result.getMsg());
            businessException.setFleetAndTmsTaskDTO(fleetAndTmsTaskDTO);
            throw businessException;
        }
        TaskDetailApiDTO task = result.getData();

        fleetAndTmsTaskDTO.getMissionWorkParam().setMissionWorkId(task.getTaskNo());
        fleetMissionWorkExecutingQueue.put(fleetAndTmsTaskDTO.getFlowNodeId(), fleetAndTmsTaskDTO);

        MissionWorkDTO response = new MissionWorkDTO();
        response.setMissionId(missionWorkParam.getMissionId());
        response.setRuntimeParam(missionWorkParam.getRuntimeParam());
        return response;
    }

    @Override
    public List<MissionDTO> getAllMission(MissionQueryRequest queryRequest) {
        TaskTypeDTO search = null;
        if(ToolUtil.isNotEmpty(queryRequest)) {
            search = new TaskTypeDTO();
            search.setCode(queryRequest.getCode());
            search.setName(queryRequest.getName());

        }
        FleetResult<List<TaskTypeDTO>> result = fleet5Client.getAllMission(search);
        if(result.getCode() == 0){
            List<TaskTypeDTO> data = result.getData();
            if(data!=null && data.size()>0){
                return data.stream().map(taskTypeDTO -> {
                    MissionDTO missionDTO = new MissionDTO();
                    missionDTO.setId(taskTypeDTO.getCode());
                    missionDTO.setCode(taskTypeDTO.getCode());
                    missionDTO.setName(taskTypeDTO.getName());
                    return missionDTO;
                }).collect(Collectors.toList());
            }
        }
        return null;
    }

    @Override
    public FleetPageDataDTO<MissionDTO> getPageMission(MissionQueryPageRequest queryPageRequest) {
        return null;
    }

    @Override
    public MissionWorkDTO getMissionWork(String id) {
        TaskQueryApiDTO request = new TaskQueryApiDTO();
        request.setTaskNo(id);
        FleetResult<TaskDetailApiDTO> result = fleet5Client.queryTask(request);
        if(result.getCode() == 0){
            TaskDetailApiDTO task = result.getData();
            //
            MissionWorkDTO missionWorkDTO = new MissionWorkDTO();
            missionWorkDTO.setId(task.getTaskNo());
            MissionWorkStatus status = null;
            if(task.getStatus().equals("Finished")){
                status = MissionWorkStatus.SUCCESS;
            }else if(task.getStatus().equals("Running")){
                status = MissionWorkStatus.RUNNING;
            }else if(task.getStatus().equals("Create")){
                status = MissionWorkStatus.CREATE;
            }else if(task.getStatus().equals("Cancel")){
                status = MissionWorkStatus.SHUTDOWN;
            }
            missionWorkDTO.setStatus(status);
            return missionWorkDTO;
        }
        return null;
    }

    @Override
    public MissionWorkDTO pollGetMissionWork(String missionWorkId, MissionWorkStatus... status) {
        return null;
    }

    /**
     * 获取地图列表
     *
     * @param request AgvMapRequest
     * @return 返回地图列表
     */
    @Override
    public List<AgvMapDTO> getAllAGVMap(AgvMapRequest request) {
        VehicleMapDetailDTO query = new VehicleMapDetailDTO();
        if(ToolUtil.isNotEmpty(request.getName())){
            query.setName(request.getName());
        }
        FleetResult<List<VehicleMapDetailDTO>> result = fleet5Client.getVehicleMapList(query);
        if(result.getCode()==0){
            List<VehicleMapDetailDTO> vehicleMapDetails = result.getData();
            if(ToolUtil.isNotEmpty(vehicleMapDetails)){
                return vehicleMapDetails.stream().map(vehicleMapDetailDTO -> {
                    AgvMapDTO agvMapDTO = new AgvMapDTO();
                    agvMapDTO.setId(vehicleMapDetailDTO.getCode());
                    agvMapDTO.setName(vehicleMapDetailDTO.getName());
                    return agvMapDTO;
                }).collect(Collectors.toList());
            }
        }
        return null;
    }

    @Override
    public List<AgvTypeDTO> getAllAgvTypes() {

        FleetResult<List<VehicleTypeDTO>> result =fleet5Client.getVehicleTypeList();
        if(result.getCode()==0 ){
            List<VehicleTypeDTO> vehicleTypes = result.getData();
            if(ToolUtil.isNotEmpty(vehicleTypes)){
                return vehicleTypes.stream().map(vehicleTypeDTO -> {
                    AgvTypeDTO agvTypeDTO = new AgvTypeDTO();
                    agvTypeDTO.setCode(vehicleTypeDTO.getCode());
                    agvTypeDTO.setName(vehicleTypeDTO.getName());
                    agvTypeDTO.setId(vehicleTypeDTO.getId());
                    return agvTypeDTO;
                }).collect(Collectors.toList());
            }
        }

        return null;
    }



    @Override
    public void openAutoParkAndCharge(String agvCode) {
        log.info("AGV[{}]开启自动泊车和自动充电", agvCode);
        VehicleControlApiDTO request = new VehicleControlApiDTO();
        request.setVehicleCode(agvCode);
        request.setAutoCharge(1);
        request.setAutoPark(1);
        fleet5Client.operation(request);
    }

    @Override
    public void closeAutoParkAndCharge(String agvCode) {
        log.info("AGV[{}]关闭自动泊车和自动充电", agvCode);
        VehicleControlApiDTO request = new VehicleControlApiDTO();
        request.setVehicleCode(agvCode);
        request.setAutoCharge(0);
        request.setAutoPark(0);
        fleet5Client.operation(request);
    }

    @Override
    public List<MissionWorkStatisticDTO> getMissionWorkStatistic(TaskStatisticsRequest request) {
        try {
            MissionWorkStatisticQueryRequest requestParams = new MissionWorkStatisticQueryRequest();
            requestParams.setAgvCode(request.getAgvCode());
            requestParams.setStartTime(request.getBeginDate());
            requestParams.setType(request.getType().name());
            requestParams.setEndTime(request.getEndDate());

            log.info("调用Fleet接口getMissionWorkStatistic，参数：agvCode={}, startTime={}, endTime={}, type={}",
                    requestParams.getAgvCode(), requestParams.getStartTime(), requestParams.getEndTime(), requestParams.getType());

            FleetResult<Map<String, List<TaskStatisticsResultDTO>>> result = fleet5Client.getMissionWorkStatistic(requestParams);

            if(result.getCode()!=0){
                log.error("获取任务统计数据失败，返回数据为：{}", JSON.toJSONString(result));
                return null;
            }
            List<MissionWorkStatisticDTO> resultList = new ArrayList<>();
            Map<String, List<TaskStatisticsResultDTO>> missionWorkActionStatisticJson = result.getData();

            //result.data是一个以时间为key,结果集为value的map
            //遍历result.data
            missionWorkActionStatisticJson.forEach((dateTime, value)->{
                MissionWorkStatisticDTO resultData = new MissionWorkStatisticDTO();
                resultData.setDateTime(dateTime);
                resultData.setMissionWorkStatistics(value.stream().map(taskStatisticsResultDTO -> {
                    MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatistic = new MissionWorkStatisticDTO.MissionWorkStatistic();
                    missionWorkStatistic.setWorkNum(taskStatisticsResultDTO.getWorkNum().intValue());
                    missionWorkStatistic.setWorkTime(taskStatisticsResultDTO.getWorkTime());
                    missionWorkStatistic.setAgvCode(taskStatisticsResultDTO.getAgvCode());
                    missionWorkStatistic.setChargeNum(taskStatisticsResultDTO.getChargeNum().intValue());
                    missionWorkStatistic.setChargeTime(taskStatisticsResultDTO.getChargeTime());
                    missionWorkStatistic.setErrorNum(taskStatisticsResultDTO.getErrorNum().intValue());
                    missionWorkStatistic.setErrorTime(taskStatisticsResultDTO.getErrorTime());
                    missionWorkStatistic.setParkNum(0);
                    missionWorkStatistic.setParkTime(0L);
                    missionWorkStatistic.setFreeTime(taskStatisticsResultDTO.getFreeTime());
                    missionWorkStatistic.setOfflineTime(taskStatisticsResultDTO.getDisconnectTime());
                    return missionWorkStatistic;
                }).collect(Collectors.toList()));
                resultList.add(resultData);
            });

            return resultList;
        } catch (Exception e) {
            log.error("请求Fleet获取机器人任务统计数据发生异常：{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED);
        }
    }

    public List<String> sortMarkerCodesByDistance(String mapName, String agvCode, List<String> markerCodes) {
        log.info("AGV[{}],准备给导航点排序:{}", agvCode,markerCodes);
        if (markerCodes == null || markerCodes.size() == 0) {
            return null;
        }else if(markerCodes.size()==1){
            //如果只有一个导航点，则直接返回
            return markerCodes;
        }
        MarkerSortQueryApiDTO request = new MarkerSortQueryApiDTO();
        request.setVehicleCode(agvCode);
        request.setMarkerCodes(markerCodes);
        FleetResult<List<String>> result = fleet5Client.getSortedMarkerListByDistance(request);
        if(result.getCode() ==0){
            log.info("排序后的导航点:{}", result.getData());
            return result.getData();
        }else{
            return null;
        }
    }

    @Override
    public SchedulerConfig getSchedulerConfig() {
        return null;
    }

    @Override
    public boolean oneKeyStop(String agvCode) {
        List<VehicleDetailApiDTO> vehicleDetails = getAllVehicleDetails(Collections.singletonList(agvCode));
        if(ToolUtil.isEmpty(vehicleDetails)){
            return true;
        }
        try {
            VehicleDetailApiDTO vehicleDetail = vehicleDetails.get(0);
            if(vehicleDetail.getTaskInfo() != null && vehicleDetail.getTaskInfo().getTaskNo() != null) {
                String taskNo = vehicleDetail.getTaskInfo().getTaskNo();
                TaskOperateApiDTO request = new TaskOperateApiDTO();
                request.setTaskNo(taskNo);
                log.info("AGV[{}]正在执行任务[{}]，准备取消任务", agvCode, taskNo);
                fleet5Client.cancelTask(request);
            }else{
                log.info("AGV[{}]没有在执行的任务，无需停止", agvCode);
            }
            return true;
        } catch (Exception e) {
            log.error("调用fleet取消任务报错，AGV[{}]，异常信息：{}", (agvCode == null ? "null" : agvCode), Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    @Override
    public boolean oneKeyReset(String agvCode) {
        return this.oneKeyStop(agvCode);
    }

    @Override
    public boolean restartTaskPrevOneKeyStopAllAgvRunningMission(String agvCode) {
        return this.oneKeyStop(agvCode);
    }

    public String getCallbackUrl(String flowId, String flowNodeId) {
        String tmsUrl = sysConfigService.selectConfigValueByKey("sys.url");
        if (!tmsUrl.endsWith("/")) {
            tmsUrl = tmsUrl + "/";
        }
        String path = "api/v1/open/workflow/callback/fleet5/task/callback/" + flowId + "/" + flowNodeId;

        return tmsUrl + path;
    }

    @Override
    public List<AgvDTO> listFreeVehicles() {
        return fleet5Client.listFreeVehicles();
    }

    @Override
    public CancelTaskResult cancelTask(Collection<String> taskNos) {
        try {
          return  cancelTask(taskNos, false, 30);
        } catch (Exception e) {
            log.error("调用fleet取消任务报错，异常信息：{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 并发取消任务（优化版本）
     * 支持同步和异步模式
     *
     * @param taskNos 任务编号集合
     * @param waitForCompletion 是否等待所有任务取消完成
     * @param timeoutSeconds 超时时间（秒），仅在waitForCompletion=true时有效
     * @return 取消结果
     */
    public CancelTaskResult cancelTask(Collection<String> taskNos, boolean waitForCompletion, int timeoutSeconds) {
        if (taskNos == null || taskNos.isEmpty()) {
            log.warn("取消任务失败：任务编号列表为空");
            CancelTaskResult result = new CancelTaskResult();
            result.setSuccessCount(0);
            result.setFailureCount(0);
            result.setMessage("任务编号列表为空");
            return result;
        }

        log.info("开始并发取消{}个任务：{}，等待完成：{}，超时：{}秒",
                taskNos.size(), taskNos, waitForCompletion, timeoutSeconds);
        long startTime = System.currentTimeMillis();

        CancelTaskResult result = new CancelTaskResult();
        List<String> successTaskIds = Collections.synchronizedList(new ArrayList<>());
        List<String> failureTaskIds = Collections.synchronizedList(new ArrayList<>());

        // 使用并发方式取消任务
        List<CompletableFuture<Void>> futures = taskNos.stream()
                .map(taskNo -> CompletableFuture.runAsync(() -> {
                    try {
                        log.debug("开始取消任务：{}", taskNo);
                        TaskOperateApiDTO request = new TaskOperateApiDTO();
                        request.setTaskNo(taskNo);

                        FleetResult<Void> fleetResult = fleet5Client.cancelTask(request);

                        if (fleetResult != null && fleetResult.getCode() == 0) {
                            successTaskIds.add(taskNo);
                            log.info("成功取消任务：{}", taskNo);
                        } else {
                            failureTaskIds.add(taskNo);
                            log.warn("取消任务失败：{}，code={}，msg={}", taskNo,
                                    fleetResult != null ? fleetResult.getCode() : "null",
                                    fleetResult != null ? fleetResult.getMsg() : "result is null");
                        }
                    } catch (Exception e) {
                        failureTaskIds.add(taskNo);
                        log.error("取消任务异常：{}", taskNo, e);
                    }
                }, executorService))
                .collect(Collectors.toList());

        if (waitForCompletion) {
            // 同步模式：等待所有任务完成
            try {
                CompletableFuture<Void> allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

                if (timeoutSeconds > 0) {
                    // 带超时的等待
                    allTasks.get(timeoutSeconds, java.util.concurrent.TimeUnit.SECONDS);
                } else {
                    // 无限等待
                    allTasks.get();
                }

                log.info("所有任务取消操作已完成");
            } catch (java.util.concurrent.TimeoutException e) {
                log.warn("等待任务取消完成超时（{}秒），部分任务可能仍在处理中", timeoutSeconds);
                result.setMessage("部分任务取消超时，可能仍在处理中");
            } catch (Exception e) {
                log.error("等待任务取消完成时发生异常", e);
                result.setMessage("等待任务取消时发生异常：" + e.getMessage());
            }

            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;

            // 构建同步结果
            result.setSuccessCount(successTaskIds.size());
            result.setFailureCount(failureTaskIds.size());
            result.setSuccessTaskIds(new ArrayList<>(successTaskIds));
            result.setFailureTaskIds(new ArrayList<>(failureTaskIds));
            result.setTotalTime(totalTime);

            if (result.getMessage() == null) {
                result.setMessage(String.format("同步取消任务完成，成功%d个，失败%d个，耗时%dms",
                        successTaskIds.size(), failureTaskIds.size(), totalTime));
            }

            log.info("同步取消任务完成：{}", result);
        } else {
            // 异步模式：立即返回，任务在后台继续执行
            long submitTime = System.currentTimeMillis() - startTime;

            result.setSuccessCount(0);
            result.setFailureCount(0);
            result.setSuccessTaskIds(new ArrayList<>());
            result.setFailureTaskIds(new ArrayList<>());
            result.setTotalTime(submitTime);
            result.setMessage(String.format("异步取消任务已提交，共%d个任务，提交耗时%dms",
                    taskNos.size(), submitTime));

            // 异步记录最终结果
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .whenComplete((unused, throwable) -> {
                        long finalTime = System.currentTimeMillis() - startTime;
                        log.info("异步取消任务最终完成，成功{}个，失败{}个，总耗时{}ms",
                                successTaskIds.size(), failureTaskIds.size(), finalTime);
                    });

            log.info("异步取消任务已提交：{}", result);
        }

        return result;
    }

    @Override
    public CancelTaskResult cancelTaskAdvanced(List<String> taskIds, List<String> agvCodes, Integer timeout) {
        log.info("开始高级并发取消任务，taskIds={}，agvCodes={}，timeout={}s", taskIds, agvCodes, timeout);
        long startTime = System.currentTimeMillis();

        // 确定要取消的任务列表
        Set<String> targetTaskNos = new HashSet<>();

        try {
            // 如果指定了任务ID，直接使用
            if (taskIds != null && !taskIds.isEmpty()) {
                targetTaskNos.addAll(taskIds);
                log.info("指定取消任务：{}", taskIds);
            }

            // 如果指定了机器人编码，获取这些机器人正在执行的任务
            if (agvCodes != null && !agvCodes.isEmpty()) {
                for (String agvCode : agvCodes) {
                    try {
                        List<VehicleDetailApiDTO> vehicleDetails = getAllVehicleDetails(Collections.singletonList(agvCode));
                        if (vehicleDetails != null && !vehicleDetails.isEmpty()) {
                            VehicleDetailApiDTO vehicleDetail = vehicleDetails.get(0);
                            if (vehicleDetail.getTaskInfo() != null && vehicleDetail.getTaskInfo().getTaskNo() != null) {
                                String taskNo = vehicleDetail.getTaskInfo().getTaskNo();
                                targetTaskNos.add(taskNo);
                                log.info("机器人[{}]正在执行任务[{}]，加入取消列表", agvCode, taskNo);
                            } else {
                                log.info("机器人[{}]没有正在执行的任务", agvCode);
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取机器人[{}]任务信息失败", agvCode, e);
                    }
                }
            }

            // 如果没有指定任务ID和机器人编码，获取所有正在运行的任务
            if ((taskIds == null || taskIds.isEmpty()) && (agvCodes == null || agvCodes.isEmpty())) {
                log.info("未指定具体任务或机器人，获取所有正在运行的任务");
                List<VehicleDetailApiDTO> allVehicles = getAllVehicleDetails(null);
                if (allVehicles != null) {
                    for (VehicleDetailApiDTO vehicle : allVehicles) {
                        if (vehicle.getTaskInfo() != null && vehicle.getTaskInfo().getTaskNo() != null) {
                            targetTaskNos.add(vehicle.getTaskInfo().getTaskNo());
                        }
                    }
                }
                log.info("找到{}个正在运行的任务", targetTaskNos.size());
            }

            if (targetTaskNos.isEmpty()) {
                CancelTaskResult result = new CancelTaskResult();
                result.setSuccessCount(0);
                result.setFailureCount(0);
                result.setMessage("没有找到需要取消的任务");
                result.setTotalTime(System.currentTimeMillis() - startTime);
                return result;
            }

            // 调用基础的并发取消方法
            return cancelTask(targetTaskNos);

        } catch (Exception e) {
            log.error("高级并发取消任务失败", e);
            CancelTaskResult result = new CancelTaskResult();
            result.setSuccessCount(0);
            result.setFailureCount(targetTaskNos.size());
            result.setFailureTaskIds(new ArrayList<>(targetTaskNos));
            result.setMessage("高级并发取消任务失败：" + e.getMessage());
            result.setTotalTime(System.currentTimeMillis() - startTime);
            return result;
        }
    }


    @Override
    public List<ChargeStationDTO> listChargeStations(Map<String, Object> params) {
        try {
            FleetResult<List<ChargeStationDTO>> result = fleet5Client.getChargeStationList(params);
            if (result.getCode() == 0 && result.getData() != null) {
                return result.getData();
            } else {
                log.error("获取充电桩列表失败, code={}, msg={}", result.getCode(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("获取充电桩列表异常", e);
        }
        return Collections.emptyList();
    }

    @Override
    public ChargeStationDTO getChargeStationInfo(Long id) {
        try {
            FleetResult<ChargeStationDTO> result = fleet5Client.getChargeStationInfo(id);
            if (result.getCode() == 0 && result.getData() != null) {
                return result.getData();
            } else {
                log.error("获取充电桩详情失败, code={}, msg={}", result.getCode(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("获取充电桩详情异常, id={}", id, e);
        }
        return null;
    }

    // ==================== 四大功能实现 ====================

    @Override
    public List<VehicleUtilizationDTO> getVehicleUtilizationStatistics(VehicleUtilizationRequest request) {
        try {
            log.info("获取机器人稼动率统计数据，请求参数：{}", request);

            // 验证日期参数
            if (!validateDateRange(request.getStartDate(), request.getEndDate())) {
                log.error("日期参数错误：开始日期[{}]不能晚于结束日期[{}]", request.getStartDate(), request.getEndDate());
                return Collections.emptyList();
            }

            // 获取机器人列表
            List<String> vehicleCodes = getVehicleCodesList(request);
            if (vehicleCodes == null || vehicleCodes.isEmpty()) {
                log.warn("未获取到机器人列表");
                return Collections.emptyList();
            }

            log.info("获取到{}台机器人，准备并发调用Fleet统计接口", vehicleCodes.size());

            // 根据时间范围自动选择时间维度，而不是依赖请求的timeDimension
            long daysBetween = request.getDaysBetween();
            String autoSelectedTimeDimension;

            if (daysBetween <= 1) {
                autoSelectedTimeDimension = StatisticsConstants.MISSION_STATISTIC_TYPE_HOUR;
                log.info("时间范围{}天 ≤ 1天，自动选择HOUR维度", daysBetween);
            } else if (daysBetween <= 60) {
                autoSelectedTimeDimension = StatisticsConstants.MISSION_STATISTIC_TYPE_DAY;
                log.info("时间范围{}天 ≤ 60天，自动选择DAY维度", daysBetween);
            } else {
                autoSelectedTimeDimension = StatisticsConstants.MISSION_STATISTIC_TYPE_MONTH;
                log.info("时间范围{}天 > 60天，自动选择MONTH维度", daysBetween);
            }

            // 对于长时间范围（>60天），优先使用汇总数据以确保数据完整性
            boolean useAggregatedData = (daysBetween > 60);
            log.info("使用自动选择的时间维度[{}]获取机器人统计数据，是否使用汇总数据：{}", autoSelectedTimeDimension, useAggregatedData);

            if (useAggregatedData) {
                // 汇总数据：使用原有的单条数据方式
                List<CompletableFuture<VehicleUtilizationDTO>> futures = vehicleCodes.stream()
                        .filter(vehicleCode -> vehicleCode != null && !vehicleCode.trim().isEmpty() && !"null".equals(vehicleCode))
                        .map(vehicleCode -> CompletableFuture.supplyAsync(() ->
                                getVehicleStatisticsFromFleet(vehicleCode, request.getStartDate(), request.getEndDate()),
                                executorService))
                        .collect(Collectors.toList());

                // 等待所有异步调用完成并收集结果
                List<VehicleUtilizationDTO> result = futures.stream()
                        .map(CompletableFuture::join)
                        .filter(dto -> dto != null)
                        .collect(Collectors.toList());

                log.info("成功获取{}台机器人的汇总稼动率统计数据", result.size());
                return result;
            } else {
                // 时间维度数据：使用自动选择的时间维度
                // 创建一个临时请求对象，设置自动选择的时间维度
                VehicleUtilizationRequest timeDimensionRequest = new VehicleUtilizationRequest();
                timeDimensionRequest.setStartDate(request.getStartDate());
                timeDimensionRequest.setEndDate(request.getEndDate());
                timeDimensionRequest.setAgvCode(request.getAgvCode());
                timeDimensionRequest.setAgvCodes(request.getAgvCodes());
                timeDimensionRequest.setStatisticsType(request.getStatisticsType());
                timeDimensionRequest.setTimeDimension(autoSelectedTimeDimension); // 使用自动选择的时间维度

                List<CompletableFuture<List<VehicleUtilizationDTO>>> futures = vehicleCodes.stream()
                        .filter(vehicleCode -> vehicleCode != null && !vehicleCode.trim().isEmpty() && !"null".equals(vehicleCode))
                        .map(vehicleCode -> CompletableFuture.supplyAsync(() ->
                                getVehicleStatisticsTimeDimensionFromFleet(vehicleCode, timeDimensionRequest),
                                executorService))
                        .collect(Collectors.toList());

                // 等待所有异步调用完成并收集结果
                List<VehicleUtilizationDTO> result = futures.stream()
                        .map(CompletableFuture::join)
                        .filter(list -> list != null && !list.isEmpty())
                        .flatMap(List::stream)
                        .collect(Collectors.toList());

                log.info("成功获取{}台机器人的{}维度稼动率统计数据，共{}条记录", vehicleCodes.size(), autoSelectedTimeDimension, result.size());

                // 如果时间维度数据为空，回退到汇总数据模式
                if (result.isEmpty()) {
                    log.warn("{}维度数据为空，回退到汇总数据模式获取稼动率统计数据", autoSelectedTimeDimension);
                    List<CompletableFuture<VehicleUtilizationDTO>> summaryFutures = vehicleCodes.stream()
                            .filter(vehicleCode -> vehicleCode != null && !vehicleCode.trim().isEmpty() && !"null".equals(vehicleCode))
                            .map(vehicleCode -> CompletableFuture.supplyAsync(() ->
                                    getVehicleStatisticsFromFleet(vehicleCode, request.getStartDate(), request.getEndDate()),
                                    executorService))
                            .collect(Collectors.toList());

                    // 等待所有异步调用完成并收集结果
                    List<VehicleUtilizationDTO> summaryResult = summaryFutures.stream()
                            .map(CompletableFuture::join)
                            .filter(dto -> dto != null)
                            .collect(Collectors.toList());

                    log.info("回退模式成功获取{}台机器人的汇总稼动率统计数据", summaryResult.size());
                    return summaryResult;
                }

                return result;
            }



        } catch (Exception e) {
            log.error("获取机器人稼动率统计数据失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<AbnormalEventCountDTO> getAbnormalEventStatistics(VehicleUtilizationRequest request) {
        try {
            log.info("获取异常事件统计数据，请求参数：{}", request);

            // 调用Fleet异常统计接口
            FleetResult<AbnormalStatisticsDTO> fleetResult =
                    fleet5Client.getAbnormalStatistics(request.getStartDate(), request.getEndDate());

            if (fleetResult == null || fleetResult.getCode() != 0 || fleetResult.getData() == null) {
                log.warn("Fleet异常统计接口返回空数据，code: {}", fleetResult != null ? fleetResult.getCode() : "null");
                return Collections.emptyList();
            }

            AbnormalStatisticsDTO abnormalStatistics = fleetResult.getData();
            List<AbnormalEventCountDTO> result = convertFleetAbnormalStatistics(abnormalStatistics);

            // 计算PARETO数据
            calculateParetoData(result);

            return result;
        } catch (Exception e) {
            log.error("获取异常事件统计数据失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public FleetPageResult<AbnormalEventDetailDTO> getAbnormalEventDetails(VehicleUtilizationRequest request) {
        try {
            log.info("获取异常事件详情数据，异常编码：{}，请求参数：{}", request.getAbnormalCode(), request);

            // 使用Fleet的TMS专用分页接口：/fleet/tms/notice/record/page（无需登录）
            String code = request.getAbnormalCode() != null ? request.getAbnormalCode().toString() : null;
            Integer pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 50;

            // 构建时间范围参数
            String createDate = null;
            if (request.getStartDate() != null && request.getEndDate() != null) {
                createDate = request.getStartDate() + "," + request.getEndDate();
            }

            log.info("调用Fleet TMS专用分页接口查询异常事件详情：code={}, pageNum={}, pageSize={}, createDate={}",
                    code, pageNum, pageSize, createDate);

            // 调用Fleet TMS专用分页接口（无需登录）
            FleetResult<FleetNoticeRecordPageDTO> fleetResult = fleet5Client.getTmsNoticeRecordPage(
                    code,           // 异常编码
                    null,           // 描述
                    null,           // 级别
                    pageNum,        // 页码
                    pageSize,       // 页大小
                    request.getAgvCode(), // 机器人编码
                    null,           // 状态
                    createDate      // 创建时间范围
            );

            if (fleetResult == null || fleetResult.getCode() != 0 || fleetResult.getData() == null) {
                log.warn("Fleet TMS专用异常详情分页接口返回空数据，code: {}", fleetResult != null ? fleetResult.getCode() : "null");
                return FleetPageResult.empty(pageNum, pageSize);
            }

            FleetNoticeRecordPageDTO pageData = fleetResult.getData();
            if (pageData.getList() == null || pageData.getList().isEmpty()) {
                log.info("Fleet返回的异常事件详情数据为空，{}", pageData.getPageSummary());
                return FleetPageResult.empty(pageData.getPage(), pageData.getLimit());
            }

            // 转换Fleet通知记录为TMS异常事件详情DTO
            List<AbnormalEventDetailDTO> result = pageData.getList().stream()
                    .map(this::convertFleetNoticeRecordToAbnormalEventDetail)
                    .collect(Collectors.toList());

            // 构建分页结果，保留Fleet的分页信息
            FleetPageResult<AbnormalEventDetailDTO> pageResult = FleetPageResult.of(
                    pageData.getPage(),
                    pageData.getLimit(),
                    pageData.getTotal(),
                    pageData.getPages(),
                    result
            );

            log.info("成功获取异常事件详情数据，共{}条，{}", result.size(), pageData.getPageSummary());
            return pageResult;

        } catch (Exception e) {
            log.error("获取异常事件详情数据失败", e);
            // 使用前端传递的分页参数
            Integer pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 50;
            return FleetPageResult.empty(pageNum, pageSize);
        }
    }

    @Override
    public FleetPageResult<FleetNotificationDTO> getFleetNotifications(VehicleUtilizationRequest request) {
        try {
            log.info("获取Fleet通知信息，请求参数：{}", request);

            // 获取前端传递的分页参数
            Integer pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 50;

            // 构建时间范围参数
            String createDate = (request.getStartDate() != null && request.getEndDate() != null)
                    ? request.getStartDate() + "," + request.getEndDate() : null;

            log.info("调用Fleet通知接口：pageNum={}, pageSize={}, createDate={}", pageNum, pageSize, createDate);

            // 调用Fleet TMS专用分页接口（无需登录）
            FleetResult<FleetNoticeRecordPageDTO> fleetResult = fleet5Client.getTmsNoticeRecordPage(
                    null, null, request.getNotificationLevel(),
                    pageNum, pageSize,
                    request.getAgvCode(), request.getNotificationStatus(), createDate);

            if (fleetResult == null || fleetResult.getCode() != 0 || fleetResult.getData() == null) {
                log.warn("Fleet TMS专用通知接口返回空数据，code: {}", fleetResult != null ? fleetResult.getCode() : "null");
                return FleetPageResult.empty(pageNum, pageSize);
            }

            FleetNoticeRecordPageDTO pageData = fleetResult.getData();
            log.info("Fleet返回的分页信息：page={}, limit={}, total={}, 实际数据条数={}",
                    pageData.getPage(), pageData.getLimit(), pageData.getTotal(),
                    pageData.getList() != null ? pageData.getList().size() : 0);

            if (pageData.getList() == null || pageData.getList().isEmpty()) {
                log.info("Fleet返回的通知信息数据为空，{}", pageData.getPageSummary());
                return FleetPageResult.empty(pageData.getPage(), pageData.getLimit());
            }

            // 转换并设置弹窗提醒标识
            List<FleetNotificationDTO> result = pageData.getList().stream()
                    .map(this::convertFleetNoticeRecordToNotificationDTO)
                    .peek(FleetNotificationDTO::checkNeedPopup)
                    .collect(Collectors.toList());

            // 构建分页结果，使用前端传递的分页参数，但保留Fleet的总数和总页数信息
            FleetPageResult<FleetNotificationDTO> pageResult = FleetPageResult.of(
                    pageNum,  // 使用前端传递的页码
                    pageSize, // 使用前端传递的页大小
                    pageData.getTotal(),
                    pageData.getPages(),
                    result
            );

            log.info("成功获取Fleet通知信息，共{}条，{}", result.size(), pageData.getPageSummary());
            return pageResult;

        } catch (Exception e) {
            log.error("获取Fleet通知信息失败", e);
            // 使用前端传递的分页参数
            Integer pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 50;
            return FleetPageResult.empty(pageNum, pageSize);
        }
    }

    @Override
    public StatisticsReportDTO getStatisticsReport(VehicleUtilizationRequest request) {
        try {
            log.info("获取统计报表数据，请求参数：{}", request);

            StatisticsReportDTO report = new StatisticsReportDTO();
            report.setStartDate(request.getStartDate());
            report.setEndDate(request.getEndDate());
            report.setAgvCode(request.getAgvCode());

            // 获取稼动率数据（这里返回的是多日数据，每天一条记录）
            List<VehicleUtilizationDTO> utilizationData = getVehicleUtilizationStatistics(request);

            if (!utilizationData.isEmpty()) {
                // 根据时间范围自动选择数据处理方式，而不是依赖请求的statisticsType
                long daysBetween = request.getDaysBetween();
                List<StatisticsReportDTO.DailyStatisticsData> dailyDataList;

                log.info("时间范围：{}天，自动选择数据聚合方式", daysBetween);

                if (daysBetween <= 1) {
                    // 1天以内：按小时维度处理，不聚合
                    log.info("HOUR维度：按小时分组处理{}条数据", utilizationData.size());
                    dailyDataList = utilizationData.stream()
                            .collect(Collectors.groupingBy(VehicleUtilizationDTO::getStatisticsDate))
                            .entrySet().stream()
                            .sorted(Map.Entry.comparingByKey()) // 按时间排序
                            .map(entry -> {
                                String timeKey = entry.getKey();
                                List<VehicleUtilizationDTO> hourData = entry.getValue();
                                VehicleUtilizationDTO aggregatedHourData = aggregateDayData(hourData, request);
                                return buildDailyStatisticsData(timeKey, aggregatedHourData);
                            })
                            .collect(Collectors.toList());
                } else if (daysBetween <= 60) {
                    // 1-60天：按天维度处理，可能需要聚合小时数据为天数据
                    log.info("DAY维度：按天分组处理{}条数据", utilizationData.size());
                    dailyDataList = utilizationData.stream()
                            .collect(Collectors.groupingBy(data -> {
                                // 将小时数据聚合到天级别
                                String statisticsDate = data.getStatisticsDate();
                                if (statisticsDate != null && statisticsDate.length() >= 10) {
                                    return statisticsDate.substring(0, 10); // 提取日期部分 YYYY-MM-DD
                                }
                                return statisticsDate;
                            }))
                            .entrySet().stream()
                            .sorted(Map.Entry.comparingByKey()) // 按日期排序
                            .map(entry -> {
                                String dateKey = entry.getKey();
                                List<VehicleUtilizationDTO> dayData = entry.getValue();
                                VehicleUtilizationDTO aggregatedDayData = aggregateAllDataForDay(dayData, request);
                                aggregatedDayData.setStatisticsDate(dateKey); // 设置为日期格式
                                return buildDailyStatisticsData(dateKey, aggregatedDayData);
                            })
                            .collect(Collectors.toList());
                } else {
                    // 超过60天：按月维度处理，聚合数据为月数据
                    log.info("MONTH维度：按月分组处理{}条数据", utilizationData.size());
                    dailyDataList = utilizationData.stream()
                            .collect(Collectors.groupingBy(data -> {
                                // 将数据聚合到月级别
                                String statisticsDate = data.getStatisticsDate();
                                if (statisticsDate != null && statisticsDate.length() >= 7) {
                                    return statisticsDate.substring(0, 7); // 提取月份部分 YYYY-MM
                                }
                                return statisticsDate;
                            }))
                            .entrySet().stream()
                            .sorted(Map.Entry.comparingByKey()) // 按月份排序
                            .map(entry -> {
                                String monthKey = entry.getKey();
                                List<VehicleUtilizationDTO> monthData = entry.getValue();
                                VehicleUtilizationDTO aggregatedMonthData = aggregateAllDataForDay(monthData, request);
                                aggregatedMonthData.setStatisticsDate(monthKey); // 设置为月份格式
                                return buildDailyStatisticsData(monthKey, aggregatedMonthData);
                            })
                            .collect(Collectors.toList());
                }

                report.setDailyDataList(dailyDataList);

                // 设置汇总数据（只有图表1的稼动率饼图数据）
                VehicleUtilizationDTO totalAggregatedData = aggregateUtilizationData(utilizationData, request);
                report.setUtilizationData(buildUtilizationData(totalAggregatedData, request));
            }

            // 获取异常事件数据用于PARETO图
            List<AbnormalEventCountDTO> abnormalData = getAbnormalEventStatistics(request);
            List<StatisticsReportDTO.ParetoData> paretoData = abnormalData.stream()
                    .map(this::convertToParetoData)
                    .collect(Collectors.toList());
            report.setParetoData(paretoData);

            log.info("成功构建统计报表，包含{}天的数据", report.getDailyDataList() != null ? report.getDailyDataList().size() : 0);
            return report;
        } catch (Exception e) {
            log.error("获取统计报表数据失败", e);
            return new StatisticsReportDTO();
        }
    }

    @Override
    public PageInfo<AbnormalEventDetailDTO> getVehicleAbnormalLogs(VehicleUtilizationRequest request) {
        try {
            log.info("获取机器人异常日志详情，请求参数：{}", request);

            // 设置默认分页参数
            int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;

            // 确定要查询的机器人编码列表
            List<String> vehicleCodes = determineVehicleCodesList(request);
            log.info("查询异常日志的机器人编码: {}", vehicleCodes);

            // 构建参数
            String vehicleCodesStr = String.join(",", vehicleCodes);
            String createDate = request.getStartDate() + "," + request.getEndDate(); // Fleet接口要求的时间格式

            log.info("调用TMS接口查询异常日志详情: vehicleCodes={}, createDate={}, page={}, limit={}",
                    vehicleCodesStr, createDate, pageNum, pageSize);

            // 调用TMS接口查询Error类型的日志
            FleetResult<FleetLogPageDTO> result = fleet5Client.getFleetLogsByTms(
                    vehicleCodesStr,
                    "Error",
                    createDate,
                    pageNum,
                    pageSize
            );

            if (result.getCode() != 0) {
                log.error("查询机器人异常日志失败, code={}, msg={}", result.getCode(), result.getMsg());
                return createEmptyPageInfo(pageNum, pageSize);
            }

            FleetLogPageDTO fleetLogPage = result.getData();
            if (fleetLogPage == null) {
                log.warn("Fleet返回的异常日志数据为空");
                return createEmptyPageInfo(pageNum, pageSize);
            }

            // 转换Fleet日志数据为TMS异常事件详情DTO
            List<AbnormalEventDetailDTO> abnormalDetails = convertFleetLogsToAbnormalDetails(fleetLogPage.getList());

            // 构建分页结果
            PageInfo<AbnormalEventDetailDTO> pageInfo = new PageInfo<>();
            pageInfo.setList(abnormalDetails);
            pageInfo.setTotal(fleetLogPage.getTotal() != null ? fleetLogPage.getTotal().longValue() : 0L);
            pageInfo.setPageSize(fleetLogPage.getLimit() != null ? fleetLogPage.getLimit() : pageSize);
            pageInfo.setPageNum(fleetLogPage.getPage() != null ? fleetLogPage.getPage() : pageNum);

            // 计算总页数
            int totalPages = 0;
            if (fleetLogPage.getTotal() != null && fleetLogPage.getLimit() != null && fleetLogPage.getLimit() > 0) {
                totalPages = (int) Math.ceil((double) fleetLogPage.getTotal() / fleetLogPage.getLimit());
            }
            pageInfo.setPages(totalPages);

            log.info("获取机器人异常日志详情成功，共{}条数据", pageInfo.getTotal());
            return pageInfo;

        } catch (Exception e) {
            log.error("获取机器人异常日志详情失败", e);
            return createEmptyPageInfo(
                    request.getPageNum() != null ? request.getPageNum() : 1,
                    request.getPageSize() != null ? request.getPageSize() : 10
            );
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 聚合多台机器人的稼动率数据
     *
     * @param utilizationData 多台机器人的稼动率数据列表
     * @param request 请求参数
     * @return 聚合后的稼动率数据
     */
    private VehicleUtilizationDTO aggregateUtilizationData(List<VehicleUtilizationDTO> utilizationData, VehicleUtilizationRequest request) {
        if (utilizationData == null || utilizationData.isEmpty()) {
            return new VehicleUtilizationDTO();
        }

        // 如果只有一台机器人或者指定了单个机器人，直接返回
        if (utilizationData.size() == 1 || (request.getAgvCode() != null && !request.getAgvCode().trim().isEmpty())) {
            VehicleUtilizationDTO singleData = utilizationData.get(0);
            log.info("返回单台机器人[{}]的统计数据", singleData.getAgvCode());
            return singleData;
        }

        // 多台机器人数据聚合
        log.info("聚合{}台机器人的统计数据", utilizationData.size());

        VehicleUtilizationDTO aggregated = new VehicleUtilizationDTO();
        aggregated.setAgvCode("ALL"); // 表示所有机器人
        aggregated.setStatisticsDate(request.getStartDate());

        // 聚合时间数据（累加）
        long totalWorkTime = 0L;
        long totalIdleTime = 0L;
        long totalChargeTime = 0L;
        long totalAbnormalTime = 0L;
        long totalOfflineTime = 0L;
        long totalParkTime = 0L;
        long totalOnlineTime = 0L;

        // 聚合计数数据（累加）
        int totalCompletedTaskCount = 0;
        int totalChargeCount = 0;
        int totalAbnormalCount = 0;

        for (VehicleUtilizationDTO data : utilizationData) {
            if (data != null) {
                totalWorkTime += (data.getWorkTime() != null ? data.getWorkTime() : 0L);
                totalIdleTime += (data.getIdleTime() != null ? data.getIdleTime() : 0L);
                totalChargeTime += (data.getChargeTime() != null ? data.getChargeTime() : 0L);
                totalAbnormalTime += (data.getAbnormalTime() != null ? data.getAbnormalTime() : 0L);
                totalOfflineTime += (data.getOfflineTime() != null ? data.getOfflineTime() : 0L);
                totalParkTime += (data.getParkTime() != null ? data.getParkTime() : 0L);
                totalOnlineTime += (data.getTotalOnlineTime() != null ? data.getTotalOnlineTime() : 0L);

                totalCompletedTaskCount += (data.getCompletedTaskCount() != null ? data.getCompletedTaskCount() : 0);
                totalChargeCount += (data.getChargeCount() != null ? data.getChargeCount() : 0);
                totalAbnormalCount += (data.getAbnormalCount() != null ? data.getAbnormalCount() : 0);
            }
        }

        // 设置聚合后的数据
        aggregated.setWorkTime(totalWorkTime);
        aggregated.setIdleTime(totalIdleTime);
        aggregated.setChargeTime(totalChargeTime);
        aggregated.setAbnormalTime(totalAbnormalTime);
        aggregated.setOfflineTime(totalOfflineTime);
        aggregated.setParkTime(totalParkTime);
        aggregated.setTotalOnlineTime(totalOnlineTime);

        aggregated.setCompletedTaskCount(totalCompletedTaskCount);
        aggregated.setChargeCount(totalChargeCount);
        aggregated.setAbnormalCount(totalAbnormalCount);

        // 计算聚合后的比率和指标
        aggregated.calculateAllRatios();

        log.info("聚合完成 - 总工作时间:{}s, 总空闲时间:{}s, 总异常时间:{}s, 稼动率:{}%, 完成任务数:{}, 异常次数:{}",
                totalWorkTime, totalIdleTime, totalAbnormalTime, aggregated.getUtilizationRate(),
                totalCompletedTaskCount, totalAbnormalCount);

        return aggregated;
    }

    /**
     * 聚合所有数据为单天数据（用于DAY模式）
     */
    private VehicleUtilizationDTO aggregateAllDataForDay(List<VehicleUtilizationDTO> allData, VehicleUtilizationRequest request) {
        if (allData == null || allData.isEmpty()) {
            return new VehicleUtilizationDTO();
        }

        log.info("聚合{}条时间维度数据为单天数据", allData.size());

        VehicleUtilizationDTO aggregated = new VehicleUtilizationDTO();
        aggregated.setAgvCode(request.getAgvCode() != null ? request.getAgvCode() : "ALL");
        aggregated.setStatisticsDate(request.getStartDate()); // 使用请求的日期

        // 聚合所有时间段的数据
        long totalWorkTime = allData.stream().mapToLong(d -> d.getWorkTime() != null ? d.getWorkTime() : 0L).sum();
        long totalIdleTime = allData.stream().mapToLong(d -> d.getIdleTime() != null ? d.getIdleTime() : 0L).sum();
        long totalChargeTime = allData.stream().mapToLong(d -> d.getChargeTime() != null ? d.getChargeTime() : 0L).sum();
        long totalAbnormalTime = allData.stream().mapToLong(d -> d.getAbnormalTime() != null ? d.getAbnormalTime() : 0L).sum();
        long totalOfflineTime = allData.stream().mapToLong(d -> d.getOfflineTime() != null ? d.getOfflineTime() : 0L).sum();
        long totalParkTime = allData.stream().mapToLong(d -> d.getParkTime() != null ? d.getParkTime() : 0L).sum();

        int totalCompletedTaskCount = allData.stream().mapToInt(d -> d.getCompletedTaskCount() != null ? d.getCompletedTaskCount() : 0).sum();
        int totalChargeCount = allData.stream().mapToInt(d -> d.getChargeCount() != null ? d.getChargeCount() : 0).sum();
        int totalAbnormalCount = allData.stream().mapToInt(d -> d.getAbnormalCount() != null ? d.getAbnormalCount() : 0).sum();

        aggregated.setWorkTime(totalWorkTime);
        aggregated.setIdleTime(totalIdleTime);
        aggregated.setChargeTime(totalChargeTime);
        aggregated.setAbnormalTime(totalAbnormalTime);
        aggregated.setOfflineTime(totalOfflineTime);
        aggregated.setParkTime(totalParkTime);
        aggregated.setTotalOnlineTime(totalWorkTime + totalIdleTime + totalChargeTime + totalAbnormalTime + totalParkTime);

        aggregated.setCompletedTaskCount(totalCompletedTaskCount);
        aggregated.setChargeCount(totalChargeCount);
        aggregated.setAbnormalCount(totalAbnormalCount);

        // 重新计算比率
        aggregated.calculateAllRatios();

        log.info("DAY模式聚合完成 - 日期:{}, 总工作时间:{}s, 总空闲时间:{}s, 总异常时间:{}s, 稼动率:{}%",
                aggregated.getStatisticsDate(), totalWorkTime, totalIdleTime, totalAbnormalTime, aggregated.getUtilizationRate());

        return aggregated;
    }

    /**
     * 聚合单日多台机器人的数据
     */
    private VehicleUtilizationDTO aggregateDayData(List<VehicleUtilizationDTO> dayData, VehicleUtilizationRequest request) {
        if (dayData == null || dayData.isEmpty()) {
            return new VehicleUtilizationDTO();
        }

        if (dayData.size() == 1) {
            return dayData.get(0);
        }

        // 多台机器人数据聚合
        VehicleUtilizationDTO aggregated = new VehicleUtilizationDTO();
        aggregated.setAgvCode(request.getAgvCode() != null ? request.getAgvCode() : "ALL");
        aggregated.setStatisticsDate(dayData.get(0).getStatisticsDate());

        // 聚合时间和计数数据
        long totalWorkTime = dayData.stream().mapToLong(d -> d.getWorkTime() != null ? d.getWorkTime() : 0L).sum();
        long totalIdleTime = dayData.stream().mapToLong(d -> d.getIdleTime() != null ? d.getIdleTime() : 0L).sum();
        long totalChargeTime = dayData.stream().mapToLong(d -> d.getChargeTime() != null ? d.getChargeTime() : 0L).sum();
        long totalAbnormalTime = dayData.stream().mapToLong(d -> d.getAbnormalTime() != null ? d.getAbnormalTime() : 0L).sum();
        long totalOfflineTime = dayData.stream().mapToLong(d -> d.getOfflineTime() != null ? d.getOfflineTime() : 0L).sum();
        long totalParkTime = dayData.stream().mapToLong(d -> d.getParkTime() != null ? d.getParkTime() : 0L).sum();

        int totalCompletedTaskCount = dayData.stream().mapToInt(d -> d.getCompletedTaskCount() != null ? d.getCompletedTaskCount() : 0).sum();
        int totalChargeCount = dayData.stream().mapToInt(d -> d.getChargeCount() != null ? d.getChargeCount() : 0).sum();
        int totalAbnormalCount = dayData.stream().mapToInt(d -> d.getAbnormalCount() != null ? d.getAbnormalCount() : 0).sum();

        aggregated.setWorkTime(totalWorkTime);
        aggregated.setIdleTime(totalIdleTime);
        aggregated.setChargeTime(totalChargeTime);
        aggregated.setAbnormalTime(totalAbnormalTime);
        aggregated.setOfflineTime(totalOfflineTime);
        aggregated.setParkTime(totalParkTime);
        aggregated.setTotalOnlineTime(totalWorkTime + totalIdleTime + totalChargeTime + totalAbnormalTime + totalParkTime);

        aggregated.setCompletedTaskCount(totalCompletedTaskCount);
        aggregated.setChargeCount(totalChargeCount);
        aggregated.setAbnormalCount(totalAbnormalCount);

        // 重新计算比率
        aggregated.calculateAllRatios();

        return aggregated;
    }

    /**
     * 构建每日统计数据（包含6个日期维度图表的数据）
     */
    private StatisticsReportDTO.DailyStatisticsData buildDailyStatisticsData(String date, VehicleUtilizationDTO data) {
        StatisticsReportDTO.DailyStatisticsData dailyData = new StatisticsReportDTO.DailyStatisticsData();
        dailyData.setStatisticsDate(date);

        // 图表2：UU/AU数据
        dailyData.setUuAuData(buildUuAuData(data));

        // 图表3：任务数量统计数据（6个指标）
        dailyData.setTaskQuantityData(buildTaskQuantityData(data, date));

        // 图表4：任务完成率/异常率数据（3个指标）
        dailyData.setTaskCompletionRateData(buildTaskCompletionRateData(data, date));

        // 图表5：任务时长统计数据（6个指标）
        dailyData.setTaskDurationData(buildTaskDurationData(data, date));

        // 图表6：机器人时间统计数据（8个指标）
        dailyData.setRobotTimeData(buildRobotTimeData(data, date));

        // 图表7：MTBF/MTTR数据
        dailyData.setMtbfMttrData(buildMtbfMttrData(data));

        return dailyData;
    }

    /**
     * 构建稼动率数据（图表1：与MonitorConsoleController#getPieChartData保持一致）
     */
    private StatisticsReportDTO.UtilizationData buildUtilizationData(VehicleUtilizationDTO data, VehicleUtilizationRequest originalRequest) {
        log.info("=== 开始构建图表1稼动率数据（新版本-使用TaskStatistics） ===");
        StatisticsReportDTO.UtilizationData utilization = new StatisticsReportDTO.UtilizationData();

        try {
            // 使用与MonitorConsoleController相同的数据源和参数
            TaskStatisticsRequest taskRequest = new TaskStatisticsRequest();
            // 自动为开始时间添加 00:00:00，为结束时间添加 23:59:59
            String beginDateWithTime = originalRequest.getStartDate() + " 00:00:00";
            String endDateWithTime = originalRequest.getEndDate() + " 23:59:59";
            taskRequest.setBeginDate(beginDateWithTime);
            taskRequest.setEndDate(endDateWithTime);
            // 处理agvCode：如果为空或null，传递空字符串（查询所有机器人汇总数据）
            String agvCode = originalRequest.getAgvCode();
            if (agvCode == null || agvCode.trim().isEmpty() || "null".equals(agvCode)) {
                taskRequest.setAgvCode(""); // 空字符串表示查询所有机器人
            } else {
                taskRequest.setAgvCode(agvCode.trim());
            }

            // 根据时间范围自动选择时间维度，与旧API保持一致
            long daysBetween = originalRequest.getDaysBetween();
            TaskStatisticsRequest.DateType dateType;
            if (daysBetween <= 1) {
                dateType = TaskStatisticsRequest.DateType.HOUR;
            } else if (daysBetween <= 60) {
                dateType = TaskStatisticsRequest.DateType.DAY;
            } else {
                dateType = TaskStatisticsRequest.DateType.MONTH; // 超过60天使用月维度
            }
            taskRequest.setType(dateType);

            log.info("调用TaskStatisticsService.getPieChartData，参数详情：");
            log.info("  - 原始startDate: {} -> 转换后beginDate: {}", originalRequest.getStartDate(), taskRequest.getBeginDate());
            log.info("  - 原始endDate: {} -> 转换后endDate: {}", originalRequest.getEndDate(), taskRequest.getEndDate());
            log.info("  - 原始agvCode: [{}] -> 转换后agvCode: [{}]", originalRequest.getAgvCode(), taskRequest.getAgvCode());
            log.info("  - 时间范围天数: {} -> 自动选择type: {}", daysBetween, taskRequest.getType());
            log.info("  - 完整请求对象: {}", taskRequest);

            // 调用与MonitorConsoleController相同的方法
            log.info("准备调用taskStatisticsService.getPieChartData，taskStatisticsService是否为null: {}", taskStatisticsService == null);
            MissionWorkStatisticDTO.MissionWorkStatistic missionWorkStatistic = taskStatisticsService.getPieChartData(taskRequest);
            log.info("taskStatisticsService.getPieChartData返回结果是否为null: {}", missionWorkStatistic == null);

            if (missionWorkStatistic != null) {
                log.info("TaskStatistics数据获取成功：workTime={}, freeTime={}, parkTime={}, errorTime={}, offlineTime={}, chargeTime={}",
                        missionWorkStatistic.getWorkTime(), missionWorkStatistic.getFreeTime(), missionWorkStatistic.getParkTime(),
                        missionWorkStatistic.getErrorTime(), missionWorkStatistic.getOfflineTime(), missionWorkStatistic.getChargeTime());

                // 映射到图表1的五个指标（转换为小时）
                utilization.setOperatingTime(convertSecondsToHours(missionWorkStatistic.getWorkTime()));      // Operating Time
                utilization.setChargingTime(convertSecondsToHours(                                             // Charging Time
                    missionWorkStatistic.getChargeTime() != null ? missionWorkStatistic.getChargeTime() : 0L
                ));
                utilization.setFaultTime(convertSecondsToHours(                                                // Fault Time (errorTime)
                    missionWorkStatistic.getErrorTime() != null ? missionWorkStatistic.getErrorTime() : 0L
                ));
                utilization.setIdleTime(convertSecondsToHours(                                                 // Idle Time (freeTime + parkTime)
                    (missionWorkStatistic.getFreeTime() != null ? missionWorkStatistic.getFreeTime() : 0L) +
                    (missionWorkStatistic.getParkTime() != null ? missionWorkStatistic.getParkTime() : 0L)
                ));
                utilization.setOfflineTime(convertSecondsToHours(                                              // Offline Time
                    missionWorkStatistic.getOfflineTime() != null ? missionWorkStatistic.getOfflineTime() : 0L
                ));

                // 保持向后兼容性
                utilization.setWorkTime(utilization.getOperatingTime());                                       // 兼容旧字段
                utilization.setAbnormalTime(utilization.getFaultTime());                                       // 兼容旧字段

                // 计算稼动率（与MonitorConsole一致的计算方式）
                Long totalTime = (missionWorkStatistic.getWorkTime() != null ? missionWorkStatistic.getWorkTime() : 0L) +
                               (missionWorkStatistic.getFreeTime() != null ? missionWorkStatistic.getFreeTime() : 0L) +
                               (missionWorkStatistic.getParkTime() != null ? missionWorkStatistic.getParkTime() : 0L) +
                               (missionWorkStatistic.getErrorTime() != null ? missionWorkStatistic.getErrorTime() : 0L) +
                               (missionWorkStatistic.getOfflineTime() != null ? missionWorkStatistic.getOfflineTime() : 0L) +
                               (missionWorkStatistic.getChargeTime() != null ? missionWorkStatistic.getChargeTime() : 0L);

                if (totalTime > 0) {
                    BigDecimal utilizationRate = BigDecimal.valueOf(
                        (missionWorkStatistic.getWorkTime() != null ? missionWorkStatistic.getWorkTime() : 0L) * 100.0 / totalTime)
                            .setScale(2, BigDecimal.ROUND_HALF_UP);
                    utilization.setUtilizationRate(utilizationRate);
                } else {
                    utilization.setUtilizationRate(BigDecimal.ZERO);
                }

                log.info("图表1稼动率数据（五个指标）：Operating Time={}h, Charging Time={}h, Fault Time={}h, Idle Time={}h, Offline Time={}h, 稼动率={}%",
                        utilization.getOperatingTime(), utilization.getChargingTime(), utilization.getFaultTime(),
                        utilization.getIdleTime(), utilization.getOfflineTime(), utilization.getUtilizationRate());
            } else {
                // 如果TaskStatistics数据为空，回退到Fleet数据
                log.warn("TaskStatistics数据为空，回退到Fleet数据：workTime={}, idleTime={}, abnormalTime={}, utilizationRate={}",
                        data.getWorkTime(), data.getIdleTime(), data.getAbnormalTime(), data.getUtilizationRate());
                utilization.setUtilizationRate(data.getUtilizationRate());
                utilization.setWorkTime(convertSecondsToHours(data.getWorkTime()));
                utilization.setIdleTime(convertSecondsToHours(data.getIdleTime()));
                utilization.setAbnormalTime(convertSecondsToHours(data.getAbnormalTime()));
            }
        } catch (Exception e) {
            log.error("构建稼动率数据失败，回退到Fleet数据：workTime={}, idleTime={}, abnormalTime={}, utilizationRate={}",
                    data.getWorkTime(), data.getIdleTime(), data.getAbnormalTime(), data.getUtilizationRate(), e);
            utilization.setUtilizationRate(data.getUtilizationRate());
            utilization.setWorkTime(convertSecondsToHours(data.getWorkTime()));
            utilization.setIdleTime(convertSecondsToHours(data.getIdleTime()));
            utilization.setAbnormalTime(convertSecondsToHours(data.getAbnormalTime()));
        }

        return utilization;
    }

    /**
     * 构建UU/AU数据
     */
    private StatisticsReportDTO.UuAuData buildUuAuData(VehicleUtilizationDTO data) {
        StatisticsReportDTO.UuAuData uuAu = new StatisticsReportDTO.UuAuData();
        uuAu.setUu(data.getUtilizationRate()); // UU = 稼动率

        // AU = (总时间 - 离线时间) / 总时间
        Long totalTime = data.getTotalOnlineTime() + (data.getOfflineTime() != null ? data.getOfflineTime() : 0);
        if (totalTime > 0) {
            BigDecimal au = BigDecimal.valueOf(data.getTotalOnlineTime())
                    .divide(BigDecimal.valueOf(totalTime), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            uuAu.setAu(au);
        }
        uuAu.setOnlineTime(convertSecondsToHours(data.getTotalOnlineTime()));
        uuAu.setTotalTime(convertSecondsToHours(totalTime));
        return uuAu;
    }

    /**
     * 构建MTBF/MTTR数据
     */
    private StatisticsReportDTO.MtbfMttrData buildMtbfMttrData(VehicleUtilizationDTO data) {
        StatisticsReportDTO.MtbfMttrData mtbfMttr = new StatisticsReportDTO.MtbfMttrData();
        mtbfMttr.setMtbf(data.getMtbf());
        mtbfMttr.setMttr(data.getMttr());
        mtbfMttr.setFailureCount(data.getAbnormalCount());
        mtbfMttr.setTotalRunTime(convertSecondsToHours(data.getWorkTime()));
        return mtbfMttr;
    }

    /**
     * 构建每日任务数据
     */
    private StatisticsReportDTO.DailyTaskData buildDailyTaskData(VehicleUtilizationDTO data, String date) {
        StatisticsReportDTO.DailyTaskData taskData = new StatisticsReportDTO.DailyTaskData();
        taskData.setDate(date);
        taskData.setCompletedCount(data.getCompletedTaskCount() != null ? data.getCompletedTaskCount() : 0);
        taskData.setFailedCount(0); // 暂时设为0，需要从其他数据源获取
        taskData.setTotalCount(taskData.getCompletedCount() + taskData.getFailedCount());

        // 计算完成率
        if (taskData.getTotalCount() > 0) {
            BigDecimal completionRate = BigDecimal.valueOf(taskData.getCompletedCount())
                    .divide(BigDecimal.valueOf(taskData.getTotalCount()), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            taskData.setCompletionRate(completionRate);
        } else {
            taskData.setCompletionRate(BigDecimal.ZERO);
        }

        return taskData;
    }

    /**
     * 构建任务状态堆叠面积图数据
     */
    private StatisticsReportDTO.TaskStatusAreaData buildTaskStatusAreaData(VehicleUtilizationDTO data, String date) {
        StatisticsReportDTO.TaskStatusAreaData statusData = new StatisticsReportDTO.TaskStatusAreaData();
        statusData.setTimePoint(date);
        statusData.setCompletedTasks(data.getCompletedTaskCount() != null ? data.getCompletedTaskCount() : 0);
        statusData.setFailedTasks(0); // 暂时设为0
        statusData.setRunningTasks(0); // 暂时设为0
        statusData.setWaitingTasks(0); // 暂时设为0
        return statusData;
    }

    /**
     * 构建多指标趋势数据
     */
    private StatisticsReportDTO.MultiMetricTrendData buildMultiMetricTrendData(VehicleUtilizationDTO data, String date) {
        StatisticsReportDTO.MultiMetricTrendData metricData = new StatisticsReportDTO.MultiMetricTrendData();
        metricData.setTimePoint(date);
        metricData.setUtilizationRate(data.getUtilizationRate());

        // 计算UU和AU
        if (data.getTotalOnlineTime() != null && data.getTotalOnlineTime() > 0) {
            // UU = 稼动率
            metricData.setUu(data.getUtilizationRate());

            // AU = 在线时间 / 总时间
            Long totalTime = data.getTotalOnlineTime() + (data.getOfflineTime() != null ? data.getOfflineTime() : 0);
            if (totalTime > 0) {
                BigDecimal au = BigDecimal.valueOf(data.getTotalOnlineTime())
                        .divide(BigDecimal.valueOf(totalTime), 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                metricData.setAu(au);
            }
        }

        metricData.setTaskCompletionRate(BigDecimal.valueOf(100)); // 暂时设为100%
        return metricData;
    }

    /**
     * 构建任务数量统计数据（图表3：6个指标）
     */
    private StatisticsReportDTO.TaskQuantityData buildTaskQuantityData(VehicleUtilizationDTO data, String date) {
        StatisticsReportDTO.TaskQuantityData quantityData = new StatisticsReportDTO.TaskQuantityData();

        // 从Fleet数据获取基础任务数量
        Integer totalTasks = data.getCompletedTaskCount() != null ? data.getCompletedTaskCount() : 0;
        quantityData.setTotalTasks(totalTasks);

        // TODO: 需要从TMS的FromToRecord表中查询具体的异常分类数据
        // 暂时使用模拟数据，实际应该调用TMS接口获取
        quantityData.setNormalTasks((int) (totalTasks * 0.85)); // 85%正常
        quantityData.setPickupAbnormalTasks((int) (totalTasks * 0.05)); // 5%去料异常
        quantityData.setDropoffAbnormalTasks((int) (totalTasks * 0.04)); // 4%放料异常
        quantityData.setNavigationAbnormalTasks((int) (totalTasks * 0.03)); // 3%导航异常
        quantityData.setCancelledTasks((int) (totalTasks * 0.03)); // 3%取消

        return quantityData;
    }

    /**
     * 构建任务完成率/异常率数据（图表4：3个指标）
     */
    private StatisticsReportDTO.TaskCompletionRateData buildTaskCompletionRateData(VehicleUtilizationDTO data, String date) {
        StatisticsReportDTO.TaskCompletionRateData rateData = new StatisticsReportDTO.TaskCompletionRateData();

        // 基于任务数量计算比率
        rateData.setCompletionRate(new BigDecimal("85.0")); // 85%完成率
        rateData.setAbnormalRate(new BigDecimal("12.0")); // 12%异常率
        rateData.setCancellationRate(new BigDecimal("3.0")); // 3%取消率

        return rateData;
    }

    /**
     * 构建任务时长统计数据（图表5：6个指标）
     */
    private StatisticsReportDTO.TaskDurationData buildTaskDurationData(VehicleUtilizationDTO data, String date) {
        StatisticsReportDTO.TaskDurationData durationData = new StatisticsReportDTO.TaskDurationData();

        // TODO: 需要从TMS的FromToRecord表中查询具体的时长数据
        // 暂时使用模拟数据，实际应该调用TMS接口获取
        durationData.setAvgExecutionDuration(new BigDecimal("15.5")); // 平均执行时长15.5分钟
        durationData.setAvgDispatchDuration(new BigDecimal("2.3")); // 派车时长2.3分钟
        durationData.setAvgPickupNavigationDuration(new BigDecimal("3.2")); // 取料导航3.2分钟
        durationData.setAvgPickupDuration(new BigDecimal("4.1")); // 取料时长4.1分钟
        durationData.setAvgDropoffNavigationDuration(new BigDecimal("3.8")); // 放料导航3.8分钟
        durationData.setAvgDropoffDuration(new BigDecimal("2.1")); // 放料时长2.1分钟

        return durationData;
    }

    /**
     * 构建机器人时间统计数据（图表6：8个指标）
     */
    private StatisticsReportDTO.RobotTimeData buildRobotTimeData(VehicleUtilizationDTO data, String date) {
        StatisticsReportDTO.RobotTimeData timeData = new StatisticsReportDTO.RobotTimeData();

        // 从Fleet数据获取时间信息（转换为分钟）
        Long runningTime = data.getWorkTime() != null ? data.getWorkTime() / 60 : 0L; // 秒转分钟
        Long idleTime = data.getIdleTime() != null ? data.getIdleTime() / 60 : 0L;
        Long abnormalTime = data.getAbnormalTime() != null ? data.getAbnormalTime() / 60 : 0L;
        Long taskTime = runningTime; // 任务时间等于运行时间

        Long totalTime = runningTime + idleTime + abnormalTime;

        // 设置时间值（分钟）
        timeData.setRunningTime(runningTime);
        timeData.setTaskTime(taskTime);
        timeData.setIdleTime(idleTime);
        timeData.setAbnormalTime(abnormalTime);

        // 计算占比
        if (totalTime > 0) {
            timeData.setRunningTimeRatio(BigDecimal.valueOf(runningTime * 100.0 / totalTime).setScale(2, BigDecimal.ROUND_HALF_UP));
            timeData.setTaskTimeRatio(BigDecimal.valueOf(taskTime * 100.0 / totalTime).setScale(2, BigDecimal.ROUND_HALF_UP));
            timeData.setIdleTimeRatio(BigDecimal.valueOf(idleTime * 100.0 / totalTime).setScale(2, BigDecimal.ROUND_HALF_UP));
            timeData.setAbnormalTimeRatio(BigDecimal.valueOf(abnormalTime * 100.0 / totalTime).setScale(2, BigDecimal.ROUND_HALF_UP));
        } else {
            timeData.setRunningTimeRatio(BigDecimal.ZERO);
            timeData.setTaskTimeRatio(BigDecimal.ZERO);
            timeData.setIdleTimeRatio(BigDecimal.ZERO);
            timeData.setAbnormalTimeRatio(BigDecimal.ZERO);
        }

        return timeData;
    }

    /**
     * 从Fleet日志接口查询异常次数（支持多个机器人）
     * 使用TMS接口：/tms/page
     */
    private int getAbnormalCountFromFleetLogs(List<String> vehicleCodes, String startDate, String endDate) {
        try {
            log.debug("查询机器人{}在时间范围[{} - {}]的异常日志", vehicleCodes, startDate, endDate);

            // 构建参数
            String vehicleCodesStr = String.join(",", vehicleCodes);
            String createDate = startDate + "," + endDate; // Fleet接口要求的时间格式

            log.debug("调用TMS接口查询异常日志: vehicleCodes={}, createDate={}", vehicleCodesStr, createDate);

            // 调用TMS接口查询Error类型的日志
            FleetResult<FleetLogPageDTO> result = fleet5Client.getFleetLogsByTms(
                    vehicleCodesStr,
                    "Error",
                    createDate,
                    1,
                    1 // 只需要获取总数，不需要具体数据
            );

            if (result.getCode() != 0) {
                log.warn("查询机器人{}异常日志失败, code={}, msg={}", vehicleCodes, result.getCode(), result.getMsg());
                return 0;
            }

            FleetLogPageDTO pageData = result.getData();
            if (pageData != null && pageData.getTotal() != null) {
                int abnormalCount = pageData.getTotal();
                log.debug("机器人{}异常日志总数: {}", vehicleCodes, abnormalCount);
                return abnormalCount;
            }

            return 0;
        } catch (Exception e) {
            log.error("查询机器人{}异常日志异常", vehicleCodes, e);
            return 0;
        }
    }

    /**
     * 确定要查询的机器人编码列表（用于日志查询）
     * 返回机器人编码列表
     */
    private List<String> determineVehicleCodesList(VehicleUtilizationRequest request) {
        // 优先级1：单个机器人
        if (StringUtils.isNotBlank(request.getAgvCode())) {
            return Collections.singletonList(request.getAgvCode());
        }

        // 优先级2：多个机器人
        if (request.getAgvCodes() != null && !request.getAgvCodes().isEmpty()) {
            return request.getAgvCodes();
        }

        // 优先级3：获取所有机器人
        return getVehicleCodesList(request);
    }

    /**
     * 获取机器人编码列表
     * 支持三种模式：
     * 1. 指定单个机器人（agvCode）
     * 2. 指定多个机器人（agvCodes）
     * 3. 获取所有机器人（agvCode和agvCodes都为空）
     */
    private List<String> getVehicleCodesList(VehicleUtilizationRequest request) {
        try {
            // 优先级1：如果请求中指定了特定机器人，则只获取该机器人
            if (request.getAgvCode() != null && !request.getAgvCode().trim().isEmpty() && !"null".equals(request.getAgvCode())) {
                String agvCode = request.getAgvCode().trim();
                log.info("查询指定单个机器人：{}", agvCode);
                return Collections.singletonList(agvCode);
            }

            // 优先级2：如果请求中指定了机器人列表，则使用该列表
            if (request.getAgvCodes() != null && !request.getAgvCodes().isEmpty()) {
                // 过滤掉空字符串、null值和"null"字符串
                List<String> filteredCodes = request.getAgvCodes().stream()
                        .filter(code -> code != null && !code.trim().isEmpty() && !"null".equals(code))
                        .map(String::trim)
                        .collect(Collectors.toList());

                if (!filteredCodes.isEmpty()) {
                    log.info("查询指定多个机器人：{}", filteredCodes);
                    return filteredCodes;
                }
            }

            // 优先级3：获取所有机器人列表
            log.info("未指定机器人编号，获取所有机器人列表");
            FleetResult<List<VehicleApiDTO>> result = fleet5Client.listVehicles();
            if (result.getCode() != 0) {
                log.error("获取机器人列表失败, code={}, msg={}", result.getCode(), result.getMsg());
                return Collections.emptyList();
            }

            List<VehicleApiDTO> vehicles = result.getData();
            if (vehicles == null || vehicles.isEmpty()) {
                log.warn("Fleet返回的机器人列表为空");
                return Collections.emptyList();
            }

            List<String> allVehicleCodes = vehicles.stream()
                    .map(VehicleApiDTO::getVehicleCode)
                    .filter(code -> code != null && !code.trim().isEmpty() && !"null".equals(code))
                    .map(String::trim)
                    .collect(Collectors.toList());

            log.info("获取到所有机器人列表，共{}台：{}", allVehicleCodes.size(), allVehicleCodes);
            return allVehicleCodes;

        } catch (Exception e) {
            log.error("获取机器人列表异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 从Fleet获取单台机器人的统计数据
     */
    @Override
    public VehicleUtilizationDTO getVehicleStatisticsFromFleet(String vehicleCode, String startDate, String endDate) {
        try {
            // 参数验证：vehicleCode不能为null或空字符串
            if (vehicleCode == null || vehicleCode.trim().isEmpty() || "null".equals(vehicleCode)) {
                log.warn("vehicleCode参数无效：[{}]，跳过该机器人的统计数据获取", vehicleCode);
                return null;
            }

            log.info("调用Fleet接口获取机器人[{}]统计数据，时间范围：{} - {}，预期URL：/fleet/statistics/vehicle/{}",
                    vehicleCode, startDate, endDate, vehicleCode);

            FleetResult<VehicleStatisticsDTO> result = getVehicleStatisticsDTOFleetResult(vehicleCode, startDate, endDate);
            if (result == null) return null;

            VehicleStatisticsDTO fleetData = result.getData();
            if (fleetData == null) {
                log.warn("机器人[{}]统计数据为空", vehicleCode);
                return null;
            }

            // 转换Fleet数据为TMS DTO
            return convertFleetStatisticsToUtilizationDTO(fleetData, vehicleCode, startDate, endDate);

        } catch (Exception e) {
            log.error("获取机器人[{}]统计数据异常", vehicleCode, e);
            return null;
        }
    }

    /**
     * 从Fleet获取单台机器人的时间维度统计数据
     */
    private List<VehicleUtilizationDTO> getVehicleStatisticsTimeDimensionFromFleet(String vehicleCode, VehicleUtilizationRequest request) {
        try {
            // 参数验证：vehicleCode不能为null或空字符串
            if (vehicleCode == null || vehicleCode.trim().isEmpty() || "null".equals(vehicleCode)) {
                log.warn("vehicleCode参数无效：[{}]，跳过该机器人的时间维度统计数据获取", vehicleCode);
                return Collections.emptyList();
            }

            log.info("调用Fleet接口获取机器人[{}]时间维度统计数据，时间范围：{} - {}，维度：{}",
                    vehicleCode, request.getStartDate(), request.getEndDate(), request.getTimeDimension());

            FleetResult<VehicleStatisticsDTO> result = getVehicleStatisticsDTOFleetResult(vehicleCode, request.getStartDate(), request.getEndDate());
            if (result == null) return Collections.emptyList();

            VehicleStatisticsDTO fleetData = result.getData();
            if (fleetData == null) {
                log.warn("机器人[{}]时间维度统计数据为空", vehicleCode);
                return Collections.emptyList();
            }

            // 转换Fleet数据为时间维度数据列表
            return convertFleetStatisticsToTimeDimensionList(fleetData, vehicleCode, request);

        } catch (Exception e) {
            log.error("获取机器人[{}]时间维度统计数据异常", vehicleCode, e);
            return Collections.emptyList();
        }
    }

    @Override
    public FleetResult<VehicleStatisticsDTO> getVehicleStatisticsDTOFleetResult(String vehicleCode, String startDate, String endDate) {
        FleetResult<VehicleStatisticsDTO> result = fleet5Client.getVehicleDetailStatistics(vehicleCode, startDate, endDate);

        if (result == null || result.getCode() != 0) {
            log.warn("获取机器人[{}]统计数据失败，code={}, msg={}", vehicleCode,
                    result != null ? result.getCode() : "null",
                    result != null ? result.getMsg() : "result is null");
            return null;
        }
        return result;
    }

    /**
     * 验证日期范围
     */
    private boolean validateDateRange(String startDate, String endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        try {
            // 简单的字符串比较，假设日期格式为 yyyy-MM-dd
            return startDate.compareTo(endDate) <= 0;
        } catch (Exception e) {
            log.error("日期格式验证失败", e);
            return false;
        }
    }

    /**
     * 构建Fleet任务统计请求参数
     */
    private TaskStatisticsRequest buildTaskStatisticsRequest(VehicleUtilizationRequest request) {
        TaskStatisticsRequest taskRequest = new TaskStatisticsRequest();
        taskRequest.setAgvCode(request.getAgvCode());
        taskRequest.setBeginDate(request.getStartDate());
        taskRequest.setEndDate(request.getEndDate());

        // 根据统计类型设置type
        if (request.getStatisticsType() != null) {
            switch (request.getStatisticsType()) {
                case DAY:
                    taskRequest.setType(TaskStatisticsRequest.DateType.DAY);
                    break;
                case HOUR:
                    taskRequest.setType(TaskStatisticsRequest.DateType.HOUR);
                    break;
                default:
                    taskRequest.setType(TaskStatisticsRequest.DateType.NONE);
                    break;
            }
        } else {
            taskRequest.setType(TaskStatisticsRequest.DateType.DAY);
        }

        return taskRequest;
    }

    /**
     * 转换Fleet统计数据为TMS稼动率DTO（单条汇总数据）
     */
    private VehicleUtilizationDTO convertFleetStatisticsToUtilizationDTO(VehicleStatisticsDTO fleetData, String vehicleCode, String startDate, String endDate) {
        VehicleUtilizationDTO dto = new VehicleUtilizationDTO();

        dto.setAgvCode(vehicleCode);
        dto.setStatisticsDate(startDate);

        // 从Fleet的statusPieChart中提取时间数据
        if (fleetData.getStatusPieChart() != null && fleetData.getStatusPieChart().getPies() != null) {
            for (VehicleStatisticsDTO.PieData pie : fleetData.getStatusPieChart().getPies()) {
                String name = pie.getName();
                // Fleet返回的value都是秒数，不管displayUnit显示什么
                Long timeInSeconds = pie.getValue() != null ? pie.getValue().longValue() : 0L;

                switch (name) {
                    case "忙碌":
                    case "工作":
                    case "Working":
                        dto.setWorkTime(timeInSeconds);
                        break;
                    case "空闲":
                    case "Idle":
                        dto.setIdleTime(timeInSeconds);
                        break;
                    case "充电":
                    case "Charging":
                        dto.setChargeTime(timeInSeconds);
                        break;
                    case "异常":
                    case "Error":
                        dto.setAbnormalTime(timeInSeconds);
                        break;
                    case "未连接":
                    case "离线":
                    case "Offline":
                        dto.setOfflineTime(timeInSeconds);
                        break;
                    case "泊车":
                    case "Parking":
                        dto.setParkTime(timeInSeconds);
                        break;
                }
            }
        }

        // 设置默认值
        if (dto.getWorkTime() == null) dto.setWorkTime(0L);
        if (dto.getIdleTime() == null) dto.setIdleTime(0L);
        if (dto.getChargeTime() == null) dto.setChargeTime(0L);
        if (dto.getAbnormalTime() == null) dto.setAbnormalTime(0L);
        if (dto.getOfflineTime() == null) dto.setOfflineTime(0L);
        if (dto.getParkTime() == null) dto.setParkTime(0L);

        // 从Fleet的utilizeRateLineChart中提取稼动率数据（如果有的话）
        if (fleetData.getUtilizeRateLineChart() != null &&
            fleetData.getUtilizeRateLineChart().getSeriesList() != null) {

            for (VehicleStatisticsDTO.SeriesData series : fleetData.getUtilizeRateLineChart().getSeriesList()) {
                if ("实际稼动率".equals(series.getName()) &&
                    series.getValues() != null && !series.getValues().isEmpty()) {
                    // 取第一个值作为稼动率（通常是最新的数据）
                    Double utilizationRate = series.getValues().get(0);
                    if (utilizationRate != null) {
                        // 转换为百分比
                        dto.setUtilizationRate(BigDecimal.valueOf(utilizationRate * 100)
                                .setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    break;
                }
            }
        }

        // 从workStatusPieChart中提取任务和充电数据
        if (fleetData.getWorkStatusPieChart() != null &&
            fleetData.getWorkStatusPieChart().getPies() != null) {

            log.debug("机器人[{}]的workStatusPieChart数据：", vehicleCode);
            for (VehicleStatisticsDTO.PieData pie : fleetData.getWorkStatusPieChart().getPies()) {
                String name = pie.getName();
                log.debug("  - {}: {}", name, pie.getValue());
                if (pie.getValue() != null) {
                    switch (name) {
                        case "作业":
                            dto.setCompletedTaskCount(pie.getValue().intValue());
                            log.debug("设置完成任务数: {}", pie.getValue().intValue());
                            break;
                        case "充电":
                            dto.setChargeCount(pie.getValue().intValue());
                            log.debug("设置充电次数: {}", pie.getValue().intValue());
                            break;
                        case "泊车":
                            // 泊车次数（如果需要的话）
                            break;
                    }
                }
            }
        } else {
            log.warn("机器人[{}]的workStatusPieChart数据为空", vehicleCode);
        }

        // 通过Fleet日志接口查询异常次数
        List<String> vehicleCodeList = Collections.singletonList(vehicleCode);
        int abnormalCount = getAbnormalCountFromFleetLogs(vehicleCodeList, startDate, endDate);
        dto.setAbnormalCount(abnormalCount);
        log.debug("机器人[{}]从Fleet日志查询到异常次数: {}", vehicleCode, abnormalCount);

        // 计算总在线时间
        Long totalOnlineTime = dto.getWorkTime() + dto.getIdleTime() + dto.getChargeTime() +
                              dto.getAbnormalTime() + dto.getParkTime();
        dto.setTotalOnlineTime(totalOnlineTime);

        // 设置默认的计数值（只有在没有从Fleet API获取到数据时才设置默认值）
        if (dto.getChargeCount() == null) {
            dto.setChargeCount(0);
        }
        if (dto.getAbnormalCount() == null) {
            dto.setAbnormalCount(0);
        }
        if (dto.getCompletedTaskCount() == null) {
            dto.setCompletedTaskCount(0);
        }

        // 如果Fleet没有提供稼动率，则计算所有比率和指标
        if (dto.getUtilizationRate() == null) {
            dto.calculateAllRatios();
        } else {
            // Fleet提供了稼动率，但仍需要计算其他比率指标
            dto.calculateWorkTimeRatio();
            dto.calculateRunTimeRatio();
            dto.calculateAbnormalTimeRatio();
            dto.calculateMtbf();
            dto.calculateMttr();
        }

        log.info("转换机器人[{}]统计数据完成：", vehicleCode);
        log.info("  基础时间数据 - 忙碌:{}s, 空闲:{}s, 充电:{}s, 异常:{}s, 未连接:{}s, 泊车:{}s",
                dto.getWorkTime(), dto.getIdleTime(), dto.getChargeTime(),
                dto.getAbnormalTime(), dto.getOfflineTime(), dto.getParkTime());
        log.info("  比率指标 - 稼动率:{}%, 作业时间比:{}%, 运行时间比:{}%, 异常时间比:{}%",
                dto.getUtilizationRate(), dto.getWorkTimeRatio(), dto.getRunTimeRatio(), dto.getAbnormalTimeRatio());
        log.info("  计数指标 - 完成任务数:{}, 充电次数:{}, 异常次数:{}",
                dto.getCompletedTaskCount(), dto.getChargeCount(), dto.getAbnormalCount());
        log.info("  可靠性指标 - MTBF:{}h, MTTR:{}h", dto.getMtbf(), dto.getMttr());

        return dto;
    }

    /**
     * 转换Fleet统计数据为TMS稼动率DTO列表（按时间维度分组）
     *
     * @param fleetData Fleet统计数据
     * @param vehicleCode 机器人编码
     * @param request 请求参数（包含时间维度信息）
     * @return 按时间维度分组的稼动率数据列表
     */
    private List<VehicleUtilizationDTO> convertFleetStatisticsToTimeDimensionList(
            VehicleStatisticsDTO fleetData, String vehicleCode, VehicleUtilizationRequest request) {

        List<VehicleUtilizationDTO> result = new ArrayList<>();
        String timeDimension = request.getTimeDimension();

        log.info("转换机器人[{}]的Fleet数据为时间维度[{}]的数据列表", vehicleCode, timeDimension);

        // 从utilizeRateLineChart中提取时间序列数据
        if (fleetData.getUtilizeRateLineChart() != null &&
            fleetData.getUtilizeRateLineChart().getXAxisNames() != null &&
            !fleetData.getUtilizeRateLineChart().getXAxisNames().isEmpty()) {

            List<String> timeLabels = fleetData.getUtilizeRateLineChart().getXAxisNames();

            // 查找稼动率数据系列
            VehicleStatisticsDTO.SeriesData utilizationSeries = null;
            if (fleetData.getUtilizeRateLineChart().getSeriesList() != null) {
                for (VehicleStatisticsDTO.SeriesData series : fleetData.getUtilizeRateLineChart().getSeriesList()) {
                    if ("实际稼动率".equals(series.getName()) || "稼动率".equals(series.getName())) {
                        utilizationSeries = series;
                        break;
                    }
                }
            }

            // 为每个时间点创建一条记录
            for (int i = 0; i < timeLabels.size(); i++) {
                VehicleUtilizationDTO dto = new VehicleUtilizationDTO();
                dto.setAgvCode(vehicleCode);

                // 根据统计类型格式化statisticsDate
                String originalTimeLabel = timeLabels.get(i);
                String formattedDate = formatStatisticsDate(originalTimeLabel, request.getStatisticsType());
                dto.setStatisticsDate(formattedDate);

                // 设置稼动率数据
                if (utilizationSeries != null &&
                    utilizationSeries.getValues() != null &&
                    i < utilizationSeries.getValues().size()) {
                    Double utilizationRate = utilizationSeries.getValues().get(i);
                    if (utilizationRate != null) {
                        dto.setUtilizationRate(BigDecimal.valueOf(utilizationRate * 100)
                                .setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }

                // 从statusLineChart中提取各状态的时间数据
                extractTimeSeriesDataFromStatusLineChart(fleetData, dto, i);

                // 计算其他指标
                calculateAdditionalMetrics(dto, vehicleCode, timeLabels.get(i));

                result.add(dto);
            }

            log.info("成功转换机器人[{}]的{}个时间点数据", vehicleCode, result.size());
        } else {
            // 如果没有时间序列数据，回退到单条汇总数据
            log.warn("机器人[{}]没有时间序列数据，使用汇总数据", vehicleCode);
            VehicleUtilizationDTO summaryDto = convertFleetStatisticsToUtilizationDTO(
                fleetData, vehicleCode, request.getStartDate(), request.getEndDate());
            if (summaryDto != null) {
                result.add(summaryDto);
            }
        }

        return result;
    }

    /**
     * 从statusLineChart中提取时间序列数据
     */
    private void extractTimeSeriesDataFromStatusLineChart(VehicleStatisticsDTO fleetData,
                                                         VehicleUtilizationDTO dto, int timeIndex) {
        if (fleetData.getStatusLineChart() == null ||
            fleetData.getStatusLineChart().getSeriesList() == null) {
            return;
        }

        for (VehicleStatisticsDTO.SeriesData series : fleetData.getStatusLineChart().getSeriesList()) {
            if (series.getValues() == null || timeIndex >= series.getValues().size()) {
                continue;
            }

            Double value = series.getValues().get(timeIndex);
            if (value == null) continue;

            Long timeInSeconds = value.longValue();
            String seriesName = series.getName();

            // 根据系列名称设置对应的时间字段
            switch (seriesName) {
                case "忙碌":
                case "工作":
                    dto.setWorkTime(timeInSeconds);
                    break;
                case "空闲":
                    dto.setIdleTime(timeInSeconds);
                    break;
                case "充电":
                    dto.setChargeTime(timeInSeconds);
                    break;
                case "异常":
                    dto.setAbnormalTime(timeInSeconds);
                    break;
                case "未连接":
                case "离线":
                    dto.setOfflineTime(timeInSeconds);
                    break;
                case "泊车":
                    dto.setParkTime(timeInSeconds);
                    break;
            }
        }
    }

    /**
     * 计算额外的指标（MTBF/MTTR等）
     */
    private void calculateAdditionalMetrics(VehicleUtilizationDTO dto, String vehicleCode, String date) {
        // 计算总在线时间
        Long totalOnlineTime = (dto.getWorkTime() != null ? dto.getWorkTime() : 0L) +
                              (dto.getIdleTime() != null ? dto.getIdleTime() : 0L) +
                              (dto.getChargeTime() != null ? dto.getChargeTime() : 0L) +
                              (dto.getAbnormalTime() != null ? dto.getAbnormalTime() : 0L) +
                              (dto.getParkTime() != null ? dto.getParkTime() : 0L);
        dto.setTotalOnlineTime(totalOnlineTime);

        // 设置默认计数值（时间序列数据中通常没有计数信息）
        dto.setChargeCount(0);
        dto.setAbnormalCount(0);
        dto.setCompletedTaskCount(0);

        // 计算所有比率和指标
        dto.calculateAllRatios();
    }

    /**
     * 转换Fleet统计数据为TMS稼动率DTO（旧方法，保留兼容性）
     */
    private VehicleUtilizationDTO convertToUtilizationDTO(MissionWorkStatisticDTO.MissionWorkStatistic stat, String dateTime) {
        VehicleUtilizationDTO dto = new VehicleUtilizationDTO();

        dto.setAgvCode(stat.getAgvCode());
        dto.setStatisticsDate(dateTime);
        dto.setWorkTime(stat.getWorkTime());
        dto.setIdleTime(stat.getFreeTime());
        dto.setParkTime(stat.getParkTime());
        dto.setAbnormalTime(stat.getErrorTime());
        dto.setOfflineTime(stat.getOfflineTime());
        dto.setChargeTime(stat.getChargeTime());
        dto.setChargeCount(stat.getChargeNum());
        dto.setAbnormalCount(stat.getErrorNum());
        dto.setCompletedTaskCount(stat.getWorkNum());

        // 计算总在线时间
        Long totalOnlineTime = (stat.getWorkTime() != null ? stat.getWorkTime() : 0) +
                              (stat.getFreeTime() != null ? stat.getFreeTime() : 0) +
                              (stat.getParkTime() != null ? stat.getParkTime() : 0) +
                              (stat.getErrorTime() != null ? stat.getErrorTime() : 0) +
                              (stat.getChargeTime() != null ? stat.getChargeTime() : 0);
        dto.setTotalOnlineTime(totalOnlineTime);

        // 计算所有比率和指标
        dto.calculateAllRatios();

        return dto;
    }

    /**
     * 计算PARETO数据
     */
    private void calculateParetoData(List<AbnormalEventCountDTO> abnormalList) {
        if (abnormalList == null || abnormalList.isEmpty()) {
            return;
        }

        // 1. 按发生次数降序排序
        abnormalList.sort(Comparator.comparing(AbnormalEventCountDTO::getOccurrenceCount).reversed());

        // 2. 计算总数
        int totalCount = abnormalList.stream()
                .mapToInt(AbnormalEventCountDTO::getOccurrenceCount)
                .sum();

        // 3. 计算累计百分比
        int cumulativeCount = 0;
        for (AbnormalEventCountDTO data : abnormalList) {
            cumulativeCount += data.getOccurrenceCount();

            // 单项百分比
            data.calculateItemPercentage(totalCount);

            // 累计百分比
            BigDecimal cumulativePercentage = BigDecimal.valueOf(cumulativeCount)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
            data.setCumulativePercentage(cumulativePercentage);
        }
    }

    /**
     * 转换为PARETO数据
     */
    private StatisticsReportDTO.ParetoData convertToParetoData(AbnormalEventCountDTO abnormalData) {
        StatisticsReportDTO.ParetoData paretoData = new StatisticsReportDTO.ParetoData();
        paretoData.setAbnormalCode(abnormalData.getAbnormalCode());
        paretoData.setAbnormalDescription(abnormalData.getAbnormalDescription());
        paretoData.setOccurrenceCount(abnormalData.getOccurrenceCount());
        paretoData.setCumulativePercentage(abnormalData.getCumulativePercentage());
        paretoData.setItemPercentage(abnormalData.getItemPercentage());
        return paretoData;
    }

    /**
     * 根据统计类型格式化statisticsDate
     *
     * @param originalTimeLabel Fleet返回的原始时间标签
     * @param statisticsType 统计类型
     * @return 格式化后的时间字符串
     */
    private String formatStatisticsDate(String originalTimeLabel, VehicleUtilizationRequest.StatisticsType statisticsType) {
        log.debug("格式化statisticsDate - 原始值：[{}]，统计类型：[{}]", originalTimeLabel, statisticsType);

        if (originalTimeLabel == null) {
            log.warn("原始时间标签为null，返回null");
            return null;
        }

        if (statisticsType == null) {
            log.warn("统计类型为null，返回原始时间标签：{}", originalTimeLabel);
            return originalTimeLabel;
        }

        try {
            String result;
            switch (statisticsType) {
                case HOUR:
                    // HOUR: 返回小时格式，如 "2025-06-20 14"
                    if (originalTimeLabel.length() >= 13) {
                        result = originalTimeLabel.substring(0, 13); // "2025-06-20 14"
                    } else {
                        result = originalTimeLabel;
                    }
                    log.debug("HOUR格式化：{} -> {}", originalTimeLabel, result);
                    return result;

                case DAY:
                    // DAY: 返回日期格式，如 "2025-06-20"
                    if (originalTimeLabel.length() >= 10) {
                        result = originalTimeLabel.substring(0, 10); // "2025-06-20"
                    } else {
                        result = originalTimeLabel;
                    }
                    log.debug("DAY格式化：{} -> {}", originalTimeLabel, result);
                    return result;

                case MONTH:
                    // MONTH: 返回月份格式，如 "2025-06"
                    if (originalTimeLabel.length() >= 7) {
                        result = originalTimeLabel.substring(0, 7); // "2025-06"
                    } else {
                        result = originalTimeLabel;
                    }
                    log.debug("MONTH格式化：{} -> {}", originalTimeLabel, result);
                    return result;

                case NONE:
                default:
                    log.debug("NONE或其他类型，返回原始值：{}", originalTimeLabel);
                    return originalTimeLabel;
            }
        } catch (Exception e) {
            log.error("格式化statisticsDate失败，原始值：{}，统计类型：{}", originalTimeLabel, statisticsType, e);
            return originalTimeLabel;
        }
    }

    /**
     * 秒转小时
     */
    private BigDecimal convertSecondsToHours(Long seconds) {
        if (seconds == null || seconds == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(seconds)
                .divide(BigDecimal.valueOf(3600), 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 转换Fleet异常统计数据为TMS异常事件统计DTO
     */
    private List<AbnormalEventCountDTO> convertFleetAbnormalStatistics(AbnormalStatisticsDTO fleetData) {

        List<AbnormalEventCountDTO> result = new ArrayList<>();

        if (fleetData == null) {
            log.warn("Fleet异常统计数据为空");
            return result;
        }

        // 关注abnormalDetailPieChart部分
        if (fleetData.getAbnormalDetailPieChart() == null ||
            fleetData.getAbnormalDetailPieChart().getPies() == null ||
            fleetData.getAbnormalDetailPieChart().getPies().isEmpty()) {
            log.warn("Fleet异常统计数据中abnormalDetailPieChart为空");
            return result;
        }

        log.info("开始转换Fleet异常统计数据，abnormalDetailPieChart包含{}条记录",
                fleetData.getAbnormalDetailPieChart().getPies().size());

        // 转换Fleet的abnormalDetailPieChart数据
        for (AbnormalStatisticsDTO.PieChart.Pie pie :
             fleetData.getAbnormalDetailPieChart().getPies()) {

            AbnormalEventCountDTO dto = new AbnormalEventCountDTO();

            // 从name中提取异常码和描述，格式如："300013-CPU资源占用过高"
            String name = pie.getName();
            if (name != null && name.contains("-")) {
                String[] parts = name.split("-", 2);
                try {
                    dto.setAbnormalCode(Integer.parseInt(parts[0]));
                    dto.setAbnormalDescription(parts.length > 1 ? parts[1] : name);
                } catch (NumberFormatException e) {
                    log.warn("无法解析异常码：{}，使用默认值", name);
                    dto.setAbnormalCode(0);
                    dto.setAbnormalDescription(name);
                }
            } else {
                dto.setAbnormalCode(0);
                dto.setAbnormalDescription(name != null ? name : "未知异常");
            }

            // 设置发生次数
            dto.setOccurrenceCount(pie.getValue() != null ? pie.getValue().intValue() : 0);

            // 设置默认值
            dto.setAffectedVehicleCount(1); // Fleet数据中可能没有这个字段，暂时设为1
            dto.setTotalDuration(0L); // 这里是次数统计，不是时长
            dto.setAbnormalLevel(2); // 默认警告级别
            dto.setAbnormalType("系统"); // 默认类型

            // 计算平均持续时间（这里主要是次数统计，时长设为0）
            dto.setAvgDuration(0L);

            log.debug("转换异常事件：编码={}, 描述={}, 次数={}",
                    dto.getAbnormalCode(), dto.getAbnormalDescription(), dto.getOccurrenceCount());

            result.add(dto);
        }

        log.info("成功转换{}条Fleet异常统计数据", result.size());
        return result;
    }

    /**
     * 转换Fleet通知数据为TMS通知DTO
     */
    private List<FleetNotificationDTO> convertFleetNotifications(List<AbnormalApiDTO> fleetNotifications) {

        List<FleetNotificationDTO> result = new ArrayList<>();

        if (fleetNotifications == null || fleetNotifications.isEmpty()) {
            return result;
        }

        for (AbnormalApiDTO fleetNotice : fleetNotifications) {
            FleetNotificationDTO dto = new FleetNotificationDTO();

            dto.setNotificationId(fleetNotice.getId());
            dto.setAbnormalCode(fleetNotice.getCode());
            dto.setLevel(fleetNotice.getLevel());
            dto.setSource(fleetNotice.getSource());
            dto.setDescription(fleetNotice.getDescription());
            dto.setData(fleetNotice.getData());
            dto.setStatus(fleetNotice.getStatus());
            dto.setAgvCode(fleetNotice.getVehicleCode());
            dto.setMapCode(fleetNotice.getMapCode());
            dto.setTaskId(fleetNotice.getTaskNo());
            dto.setSolution(fleetNotice.getSolution());
            dto.setCreateTime(fleetNotice.getCreateTime());
            dto.setUpdateTime(fleetNotice.getUpdateTime());
            dto.setCloseTime(fleetNotice.getCloseTime());
            dto.setDeviceId(fleetNotice.getDeviceId());
            dto.setInvalidTime(fleetNotice.getInvalidTime());
            dto.setLastPushTime(fleetNotice.getLastPushTime());

            // 设置位置信息
            if (fleetNotice.getPositionX() != null) {
                dto.setPositionX(fleetNotice.getPositionX().doubleValue());
            }
            if (fleetNotice.getPositionY() != null) {
                dto.setPositionY(fleetNotice.getPositionY().doubleValue());
            }

            result.add(dto);
        }

        return result;
    }

    /**
     * 转换Fleet通知数据为TMS异常事件详情DTO
     */
    private AbnormalEventDetailDTO convertToAbnormalEventDetail(AbnormalApiDTO fleetNotice) {

        AbnormalEventDetailDTO dto = new AbnormalEventDetailDTO();

        dto.setEventId(fleetNotice.getId());
        dto.setAbnormalCode(fleetNotice.getCode());
        dto.setAbnormalDescription(fleetNotice.getDescription());
        dto.setAgvCode(fleetNotice.getVehicleCode());
        dto.setAbnormalLevel(fleetNotice.getLevel());
        dto.setOccurrenceTime(fleetNotice.getCreateTime());
        dto.setEndTime(fleetNotice.getCloseTime());
        dto.setTaskId(fleetNotice.getTaskNo());
        dto.setMapCode(fleetNotice.getMapCode());
        dto.setSolution(fleetNotice.getSolution());
        dto.setHandleStatus(fleetNotice.getStatus());
        dto.setCreateTime(fleetNotice.getCreateTime());
        dto.setData(fleetNotice.getData());
        dto.setSourceSystem("Fleet");

        // 设置位置信息
        if (fleetNotice.getPositionX() != null && fleetNotice.getPositionY() != null) {
            dto.setLocation(String.format("X:%.2f, Y:%.2f",
                    fleetNotice.getPositionX().doubleValue(),
                    fleetNotice.getPositionY().doubleValue()));
        }

        // 计算持续时间
        dto.calculateDuration();

        return dto;
    }

    /**
     * 转换Fleet通知记录为TMS异常事件详情DTO
     */
    private AbnormalEventDetailDTO convertNoticeRecordToAbnormalEventDetail(NoticeRecordPageDTO.NoticeRecord record) {
        AbnormalEventDetailDTO dto = new AbnormalEventDetailDTO();

        dto.setEventId(record.getId() != null ? record.getId().toString() : null);
        dto.setAbnormalCode(record.getCode());
        dto.setAbnormalDescription(record.getDescription());
        dto.setAgvCode(record.getVehicleCode());
        dto.setAgvName(record.getVehicleName()); // 使用正确的字段名
        dto.setOccurrenceTime(record.getLastPushTime() != null ? record.getLastPushTime() : record.getCreateTime());
        dto.setAbnormalLevel(record.getLevel());
        dto.setData(record.getData());
        dto.setSolution(record.getSolution());
        dto.setHandleStatus(record.getStatus());

        // 设置额外信息
        dto.setMapCode(record.getMapName());
        dto.setTaskId(record.getMissionWorkId());
        dto.setDeviceId(record.getDeviceId());
        dto.setSourceSystem(record.getSourceSystem() != null ? record.getSourceSystem() : "Fleet");
        dto.setAbnormalType(record.getType());

        // 设置时间信息
        dto.setCreateTime(record.getCreateTime());
        dto.setUpdateTime(record.getUpdateTime());
        dto.setEndTime(record.getCloseTime());

        // 设置状态描述
        dto.setStatus(record.getStatusDescription());

        // 设置是否需要弹窗提醒
        dto.setNeedPopup(record.needPopup());

        // 计算持续时间（如果异常已关闭）
        if (record.getCreateTime() != null && record.getCloseTime() != null) {
            long durationMs = record.getCloseTime().getTime() - record.getCreateTime().getTime();
            dto.setDuration(durationMs / 1000); // 转换为秒
        } else {
            dto.setDuration(0L);
        }

        log.debug("转换Fleet通知记录：ID={}, 编码={}, 描述={}, 机器人={}, 状态={}",
                record.getId(), record.getCode(), record.getDescription(),
                record.getVehicleCode(), record.getStatusDescription());

        return dto;
    }

    /**
     * 转换Fleet TMS专用通知记录为TMS异常事件详情DTO
     */
    private AbnormalEventDetailDTO convertFleetNoticeRecordToAbnormalEventDetail(FleetNoticeRecordPageDTO.FleetNoticeRecord record) {
        AbnormalEventDetailDTO dto = new AbnormalEventDetailDTO();

        dto.setEventId(record.getId() != null ? record.getId().toString() : null);
        dto.setAbnormalCode(record.getCode());
        dto.setAbnormalDescription(record.getDescription());
        dto.setAgvCode(record.getVehicleCode());
        dto.setAgvName(record.getVehicleName());
        dto.setOccurrenceTime(record.getLastPushTime() != null ? record.getLastPushTime() : record.getCreateDate());
        dto.setAbnormalLevel(record.getLevel());
        dto.setData(record.getData());
        dto.setSolution(record.getSolution());
        dto.setHandleStatus(record.getStatus());

        // 设置额外信息
        dto.setMapCode(record.getMapName());
        dto.setTaskId(record.getMissionWorkId());
        dto.setDeviceId(record.getDeviceId());
        dto.setSourceSystem(record.getSourceSystem() != null ? record.getSourceSystem() : "Fleet");
        dto.setAbnormalType(record.getType());

        // 设置时间信息
        dto.setCreateTime(record.getCreateDate());
        dto.setUpdateTime(record.getUpdateDate());
        dto.setEndTime(record.getCloseTime());

        // 设置状态描述
        dto.setStatus(record.getStatusDescription());

        // 设置是否需要弹窗提醒
        dto.setNeedPopup(record.needPopup());

        // 设置位置信息
        dto.setLocation(record.getLocationInfo());

        // 计算持续时间（如果异常已关闭）
        if (record.getCreateDate() != null && record.getCloseTime() != null) {
            long durationMs = record.getCloseTime().getTime() - record.getCreateDate().getTime();
            dto.setDuration(durationMs / 1000); // 转换为秒
        } else {
            dto.setDuration(0L);
        }

        log.debug("转换Fleet TMS专用通知记录：ID={}, 编码={}, 描述={}, 机器人={}, 状态={}",
                record.getId(), record.getCode(), record.getDescription(),
                record.getVehicleCode(), record.getStatusDescription());

        return dto;
    }

    /**
     * 转换Fleet TMS专用通知记录为TMS通知DTO
     */
    private FleetNotificationDTO convertFleetNoticeRecordToNotificationDTO(FleetNoticeRecordPageDTO.FleetNoticeRecord record) {
        FleetNotificationDTO dto = new FleetNotificationDTO();

        dto.setNotificationId(record.getId() != null ? record.getId().toString() : null);
        dto.setAbnormalCode(record.getCode());
        dto.setAbnormalDescription(record.getDescription());
        dto.setAgvCode(record.getVehicleCode());
        dto.setAgvName(record.getVehicleName());
        dto.setNotificationLevel(record.getLevel());
        dto.setNotificationStatus(record.getStatus());
        dto.setNotificationData(record.getData());
        dto.setSolution(record.getSolution());

        // 设置额外信息
        dto.setMapCode(record.getMapName());
        dto.setTaskId(record.getMissionWorkId());
        dto.setDeviceId(record.getDeviceId());
        dto.setSourceSystem(record.getSourceSystem() != null ? record.getSourceSystem() : "Fleet");
        dto.setNotificationType(record.getType());

        // 设置时间信息
        dto.setCreateTime(record.getCreateDate());
        dto.setUpdateTime(record.getUpdateDate());
        dto.setCloseTime(record.getCloseTime());
        dto.setLastPushTime(record.getLastPushTime());

        // 设置位置信息
        dto.setLocation(record.getLocationInfo());

        // 设置状态描述
        dto.setStatusDescription(record.getStatusDescription());

        // 设置级别描述
        dto.setLevelDescription(record.getLevelDescription());

        // 设置是否需要弹窗提醒
        dto.setNeedPopup(record.needPopup());

        // 计算持续时间（如果通知已关闭）
        if (record.getCreateDate() != null && record.getCloseTime() != null) {
            long durationMs = record.getCloseTime().getTime() - record.getCreateDate().getTime();
            dto.setDuration(durationMs / 1000); // 转换为秒
        } else {
            dto.setDuration(0L);
        }

        log.debug("转换Fleet TMS专用通知记录：ID={}, 编码={}, 描述={}, 机器人={}, 级别={}, 状态={}",
                record.getId(), record.getCode(), record.getDescription(),
                record.getVehicleCode(), record.getLevelDescription(), record.getStatusDescription());

        return dto;
    }

    @Override
    public List<TransportEfficiencyDetailDTO> getTransportEfficiencyStatistics(VehicleUtilizationRequest request) {
        try {
            log.info("获取任务搬运效率详情统计，请求参数：{}", request);

            // 解析时间参数
            LocalDateTime startTime = null;
            LocalDateTime endTime = null;
            if (request.getStartDate() != null && request.getEndDate() != null) {
                startTime = LocalDate.parse(request.getStartDate()).atStartOfDay();
                endTime = LocalDate.parse(request.getEndDate()).atTime(23, 59, 59);
            }

            // 直接调用数据库层面的统计方法（真正的高性能实现）
            List<TransportEfficiencyDetailDTO> resultList = fromToRecordMapper
                    .getTransportEfficiencyStatistics(startTime, endTime, request.getAgvCode(), request.getAgvCodes());

            if (resultList == null || resultList.isEmpty()) {
                log.warn("未统计到搬运效率数据");
                return Collections.emptyList();
            }

            log.info("成功获取{}台机器人的搬运效率详情统计", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("获取任务搬运效率详情统计失败", e);
            return Collections.emptyList();
        }
    }

























    /**
     * 创建空的分页结果
     */
    private PageInfo<AbnormalEventDetailDTO> createEmptyPageInfo(int pageNum, int pageSize) {
        PageInfo<AbnormalEventDetailDTO> pageInfo = new PageInfo<>();
        pageInfo.setList(Collections.emptyList());
        pageInfo.setTotal(0L);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPages(0);
        return pageInfo;
    }

    /**
     * 转换Fleet日志数据为TMS异常事件详情DTO列表
     */
    private List<AbnormalEventDetailDTO> convertFleetLogsToAbnormalDetails(List<FleetLogPageDTO.FleetLogDTO> fleetLogs) {
        if (fleetLogs == null || fleetLogs.isEmpty()) {
            return Collections.emptyList();
        }

        return fleetLogs.stream()
                .map(this::convertFleetLogToAbnormalDetail)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个Fleet日志为TMS异常事件详情DTO
     */
    private AbnormalEventDetailDTO convertFleetLogToAbnormalDetail(FleetLogPageDTO.FleetLogDTO fleetLog) {
        AbnormalEventDetailDTO dto = new AbnormalEventDetailDTO();

        dto.setEventId(fleetLog.getId() != null ? fleetLog.getId().toString() : null);
        dto.setAbnormalCode(fleetLog.getCode()); // 可能为null
        dto.setAbnormalDescription(fleetLog.getDescription() != null ? fleetLog.getDescription() : fleetLog.getMessage());
        dto.setAgvCode(fleetLog.getVehicleCode());
        dto.setAbnormalLevel(3); // Error级别设为3-错误
        dto.setAbnormalType(fleetLog.getModule() != null ? fleetLog.getModule() : "系统");

        // 转换时间字符串为Date对象
        dto.setOccurrenceTime(parseFleetTimeString(fleetLog.getCreateTime()));
        dto.setEndTime(parseFleetTimeString(fleetLog.getUpdateTime()));
        dto.setCreateTime(parseFleetTimeString(fleetLog.getCreateTime()));

        dto.setTaskId(fleetLog.getTaskNo());
        dto.setMapCode(fleetLog.getMapCode()); // 可能为null
        dto.setSolution(fleetLog.getSolution()); // 可能为null
        dto.setHandleStatus(fleetLog.getStatus()); // 可能为null
        dto.setData(fleetLog.getData());
        dto.setSourceSystem("Fleet");

        // Fleet日志中没有位置信息，设置为空
        if (fleetLog.getPositionX() != null && fleetLog.getPositionY() != null) {
            dto.setLocation(String.format("X:%.2f, Y:%.2f",
                    fleetLog.getPositionX(), fleetLog.getPositionY()));
        } else {
            dto.setLocation("位置信息不可用");
        }

        // 计算持续时间
        dto.calculateDuration();

        return dto;
    }

    /**
     * 解析Fleet时间字符串为Date对象
     */
    private Date parseFleetTimeString(String timeString) {
        if (timeString == null || timeString.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析常见的时间格式
            // 格式1: yyyy-MM-dd HH:mm:ss
            if (timeString.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return java.sql.Timestamp.valueOf(timeString);
            }

            // 格式2: yyyy-MM-dd'T'HH:mm:ss
            if (timeString.contains("T")) {
                return java.sql.Timestamp.valueOf(timeString.replace("T", " "));
            }

            // 格式3: 时间戳（毫秒）
            if (timeString.matches("\\d{13}")) {
                return new Date(Long.parseLong(timeString));
            }

            // 格式4: 时间戳（秒）
            if (timeString.matches("\\d{10}")) {
                return new Date(Long.parseLong(timeString) * 1000);
            }

            log.warn("无法解析Fleet时间字符串: {}", timeString);
            return new Date(); // 返回当前时间作为默认值

        } catch (Exception e) {
            log.error("解析Fleet时间字符串失败: {}", timeString, e);
            return new Date(); // 返回当前时间作为默认值
        }
    }

    /**
     * 导出统计报表到Excel文件（8个图表多Sheet）
     */
    @Override
    public void exportStatisticsReportToExcel(StatisticsReportDTO report, VehicleUtilizationRequest request,
                                             HttpServletResponse response) {
        try {
            log.info("开始导出8个图表的统计报表Excel文件");

            // 设置响应头
            String fileName = String.format("统计报表_%s_%s.xlsx", request.getStartDate(), request.getEndDate());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    java.net.URLEncoder.encode(fileName, "UTF-8"));

            // 创建工作簿
            org.apache.poi.xssf.usermodel.XSSFWorkbook workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook();

            // Sheet1: 图表1-稼动率饼图（五个指标）
            createUtilizationSheet(workbook, report.getUtilizationData());

            // Sheet2-7: 日期维度图表
            createDailyDataSheets(workbook, report.getDailyDataList());

            // Sheet8: PARETO分析
            createParetoSheet(workbook, report.getParetoData());

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("成功导出8个图表的统计报表Excel文件：{}", fileName);

        } catch (Exception e) {
            log.error("导出统计报表Excel文件失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("导出失败：" + e.getMessage());
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 创建稼动率饼图Sheet（图表1）
     */
    private void createUtilizationSheet(org.apache.poi.xssf.usermodel.XSSFWorkbook workbook,
                                       StatisticsReportDTO.UtilizationData utilizationData) {
        org.apache.poi.ss.usermodel.Sheet sheet = workbook.createSheet("图表1-稼动率饼图");

        // 创建标题行
        org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("指标名称");
        headerRow.createCell(1).setCellValue("时长(小时)");
        headerRow.createCell(2).setCellValue("说明");

        // 创建数据行
        int rowNum = 1;
        if (utilizationData != null) {
            createDataRow(sheet, rowNum++, "Operating Time", utilizationData.getOperatingTime(), "作业时间");
            createDataRow(sheet, rowNum++, "Charging Time", utilizationData.getChargingTime(), "充电时间");
            createDataRow(sheet, rowNum++, "Fault Time", utilizationData.getFaultTime(), "故障时间");
            createDataRow(sheet, rowNum++, "Idle Time", utilizationData.getIdleTime(), "空闲时间");
            createDataRow(sheet, rowNum++, "Offline Time", utilizationData.getOfflineTime(), "离线时间");

            // 添加稼动率
            org.apache.poi.ss.usermodel.Row rateRow = sheet.createRow(rowNum);
            rateRow.createCell(0).setCellValue("稼动率");
            rateRow.createCell(1).setCellValue(utilizationData.getUtilizationRate() != null ?
                    utilizationData.getUtilizationRate().doubleValue() + "%" : "0%");
            rateRow.createCell(2).setCellValue("Operating Time / 总时间 * 100%");
        }

        // 自动调整列宽
        for (int i = 0; i < 3; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建日期维度图表Sheets（图表2-7）
     */
    private void createDailyDataSheets(org.apache.poi.xssf.usermodel.XSSFWorkbook workbook,
                                      List<StatisticsReportDTO.DailyStatisticsData> dailyDataList) {
        if (dailyDataList == null || dailyDataList.isEmpty()) {
            return;
        }

        // 这里可以根据需要创建多个Sheet，暂时创建一个汇总Sheet
        org.apache.poi.ss.usermodel.Sheet sheet = workbook.createSheet("图表2-7-日期维度数据");

        // 创建标题行
        org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("日期");
        headerRow.createCell(1).setCellValue("UU(%)");
        headerRow.createCell(2).setCellValue("AU(%)");
        headerRow.createCell(3).setCellValue("总任务数");
        headerRow.createCell(4).setCellValue("完成率(%)");

        // 创建数据行
        int rowNum = 1;
        for (StatisticsReportDTO.DailyStatisticsData dailyData : dailyDataList) {
            org.apache.poi.ss.usermodel.Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(dailyData.getStatisticsDate());

            if (dailyData.getUuAuData() != null) {
                row.createCell(1).setCellValue(dailyData.getUuAuData().getUu() != null ?
                        dailyData.getUuAuData().getUu().doubleValue() : 0.0);
                row.createCell(2).setCellValue(dailyData.getUuAuData().getAu() != null ?
                        dailyData.getUuAuData().getAu().doubleValue() : 0.0);
            }

            if (dailyData.getTaskQuantityData() != null) {
                row.createCell(3).setCellValue(dailyData.getTaskQuantityData().getTotalTasks() != null ?
                        dailyData.getTaskQuantityData().getTotalTasks() : 0);
            }

            if (dailyData.getTaskCompletionRateData() != null) {
                row.createCell(4).setCellValue(dailyData.getTaskCompletionRateData().getCompletionRate() != null ?
                        dailyData.getTaskCompletionRateData().getCompletionRate().doubleValue() : 0.0);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < 5; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建PARETO分析Sheet（图表8）
     */
    private void createParetoSheet(org.apache.poi.xssf.usermodel.XSSFWorkbook workbook,
                                  List<StatisticsReportDTO.ParetoData> paretoData) {
        org.apache.poi.ss.usermodel.Sheet sheet = workbook.createSheet("图表8-PARETO异常分析");

        // 创建标题行
        org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("异常编码");
        headerRow.createCell(1).setCellValue("异常描述");
        headerRow.createCell(2).setCellValue("发生次数");
        headerRow.createCell(3).setCellValue("单项百分比(%)");
        headerRow.createCell(4).setCellValue("累计百分比(%)");

        // 创建数据行
        if (paretoData != null) {
            int rowNum = 1;
            for (StatisticsReportDTO.ParetoData data : paretoData) {
                org.apache.poi.ss.usermodel.Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(data.getAbnormalCode() != null ? data.getAbnormalCode() : 0);
                row.createCell(1).setCellValue(data.getAbnormalDescription() != null ? data.getAbnormalDescription() : "");
                row.createCell(2).setCellValue(data.getOccurrenceCount() != null ? data.getOccurrenceCount() : 0);
                row.createCell(3).setCellValue(data.getItemPercentage() != null ? data.getItemPercentage().doubleValue() : 0.0);
                row.createCell(4).setCellValue(data.getCumulativePercentage() != null ? data.getCumulativePercentage().doubleValue() : 0.0);
            }
        }

        // 自动调整列宽
        for (int i = 0; i < 5; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建数据行的辅助方法
     */
    private void createDataRow(org.apache.poi.ss.usermodel.Sheet sheet, int rowNum, String name,
                              BigDecimal value, String description) {
        org.apache.poi.ss.usermodel.Row row = sheet.createRow(rowNum);
        row.createCell(0).setCellValue(name);
        row.createCell(1).setCellValue(value != null ? value.doubleValue() : 0.0);
        row.createCell(2).setCellValue(description);
    }


}
