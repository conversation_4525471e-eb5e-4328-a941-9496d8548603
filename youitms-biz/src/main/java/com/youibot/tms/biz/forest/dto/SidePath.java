package com.youibot.tms.biz.forest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * 边
 * 类名称：Edge
 * 创建时间：2019年4月2日 下午3:50:30
 */
@ApiModel(value = "SidePath", description = "边路径")
public class SidePath  implements Serializable {
   

  
    /**  
	 * @Fields serialVersionUID : TODO(描述)
	 * <AUTHOR>
	 * @date 2021-03-09 06:12:57 
	 */  
	
	private static final long serialVersionUID = 1L;


	private String id;

    
    @ApiModelProperty(value = "地图ID", position = 1)
    private String agvMapId;

    
    @ApiModelProperty(value = "路径ID", position = 2)
    private String pathId;

    
    @ApiModelProperty(value = "开始标记点", position = 3)
    private String startMarkerId;

    
    @ApiModelProperty(value = "结束标记点", position = 4)
    private String endMarkerId;

    
    @ApiModelProperty(value = "开始点的控制点xy坐标", position = 5)
    private String startControl;

    
    @ApiModelProperty(value = "结束点的控制点xy坐标", position = 6)
    private String endControl;

    
    @ApiModelProperty(value = "长度", position = 7)
    private Double length;

    
   
    @ApiModelProperty(value = "进入路径角度", position = 8)
    private Double inAngle;

    
  
    @ApiModelProperty(value = "走出路径角度", position = 9)
    private Double outAngle;

    
    @ApiModelProperty(value = "线类型 1、直线 2、曲线（只要不为1,目前都默认为曲线）", position = 10)
    private Integer lineType;

    
    @ApiModelProperty(value = "agv方向: 0、双向 1、正向 2、反向", position = 11)
    private Integer agvDirection;

    
 
    @ApiModelProperty(value = "使用状态 ENABLE:启用  DISABLE:禁用", position = 12)
    private String usageStatus;

    
    @ApiModelProperty(value = "创建时间", position = 13)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    
    @ApiModelProperty(value = "更新时间", position = 14)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    //三阶贝塞尔曲线的起始端点
 
    private Double t0;

    //三阶贝塞尔曲线的结束端点
    
    private Double t1;

    
    private Integer navigationType;//导航类型  0、正常导航 1、方向调整+导航 2、进入电梯 3、出来电梯 4、乘坐电梯（包含切换地图）

    public SidePath() {
    }

    public SidePath(String startMarkerId, String endMarkerId, String agvMapId, Double length) {
        this.id = UUID.randomUUID().toString();
        this.startMarkerId = startMarkerId;
        this.endMarkerId = endMarkerId;
        this.agvMapId = agvMapId;
        this.length = length;
        this.inAngle = 0D;
        this.outAngle = 0D;
    }

   
}
