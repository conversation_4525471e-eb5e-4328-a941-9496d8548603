package com.youibot.tms.biz.forest;


import com.dtflys.forest.annotation.*;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.forest.dto.ChargeStationDTO;
import com.youibot.tms.biz.forest.dto.VehicleDTO;
import com.youibot.tms.biz.forest.dto.fleet5.*;
import com.youibot.tms.biz.forest.request.MissionWorkStatisticQueryRequest;

import java.util.List;
import java.util.Map;

/**
 * 为了便于开发调试，外部调用不要直接调用FleetClient <br/>
 * 而是调用FleetProxyService，该接口有模拟实现类，开发时可以不必真正调用Fleet
 */
@BaseRequest(baseURL = "${fleetHost}", interceptor = FleetForestInterceptor.class)
public interface Fleet5Client {


    @GetRequest(value = "/fleet/tms/getSystemVersion", dataType = "json",timeout = 2000)
    FleetResult<SystemVersionDTO> version();


    @GetRequest(value = "${host}/fleet/tms/getSystemVersion", dataType = "json",interceptor = {}, retryCount = 0, timeout = 2000)
    FleetResult<SystemVersionDTO> version(@DataVariable("host") String host);

    /**
     * 查询机器人列表
     *
     * @return
     */
    @PostRequest(value = "/fleet/api/v1/vehicles")
    FleetResult<List<VehicleApiDTO>> listVehicles();

    @GetRequest(value = "/fleet/tms/getVehicleTypeList")
    FleetResult<List<VehicleTypeDTO>> getVehicleTypeList();

    /**
     * 查询机器人信息（精简）
     *
     * @return
     */
    @PostRequest(value = "/fleet/api/v1/vehicles/simple")
    FleetResult<List<VehicleSimpleStatusApiDTO>> vehicleSimpleList(@JSONBody VehicleSimpleQueryApiDTO vehicleQueryApiDTO);


    /**
     * 查询机器人信息（详情）
     * @param vehicleQueryApiDTO
     * @return
     */
    @PostRequest(value = "/fleet/api/v1/vehicles/detail")
    FleetResult<List<VehicleDetailApiDTO>> vehicleDetailList(@JSONBody VehicleQueryApiDTO vehicleQueryApiDTO);

    @PostRequest(value = "/fleet/api/v1/vehicleMaps/query")
    FleetResult<MapDetailApiDTO> mapQuery(@JSONBody MapQueryApiDTO mapQueryApiDTO);


    /**
     * 操作机器人
     * @param control
     * @return
     */
    @PostRequest(value = "/fleet/api/v1/vehicles/operation")
    FleetResult<Void> operation(@JSONBody VehicleControlApiDTO control);

    /**
     * 按照目标导航点距离，排序机器人
     * @param sortQueryApiDTO
     * @return
     */
    @PostRequest(value = "/fleet/api/v1/vehicles/getSortedVehicleListByDistance")
    FleetResult<List<String>> getSortedVehicleListByDistance(@JSONBody VehicleSortQueryApiDTO sortQueryApiDTO);

    /**
     * 按照离机器人的距离，排序导航点
     * @param sortQueryApiDTO
     * @return
     */
    @PostRequest(value = "/fleet/api/v1/vehicles/getSortedMarkerListByDistance")
    FleetResult<List<String>> getSortedMarkerListByDistance(@JSONBody MarkerSortQueryApiDTO sortQueryApiDTO);


    @LogEnabled(logResponseContent = true)
    @PostRequest(value = "/fleet/api/v1/tasks/create")
    FleetResult<TaskDetailApiDTO> createTask(@JSONBody TaskExecuteApiDTO executeDTO);


    @PostRequest(value = "/fleet/api/v1/tasks/cancel")
    FleetResult<Void> cancelTask(@JSONBody TaskOperateApiDTO taskCancelApiDTO);


    @PostRequest(value = "/fleet/api/v1/tasks/query")
    FleetResult<TaskDetailApiDTO> queryTask(@JSONBody TaskQueryApiDTO taskQueryApiDTO);

    @GetRequest(value = "/fleet/tms/getVehicleMapList" , dataType = "json")
    FleetResult<List<VehicleMapDetailDTO>> getVehicleMapList(@Query VehicleMapDetailDTO search);

    @GetRequest(value = "/fleet/tms/getTaskTypeList" , dataType = "json")
    FleetResult<List<TaskTypeDTO>> getAllMission(@Query TaskTypeDTO search);

    @GetRequest(value = "/fleet/tms/statistic/getMissionWorkStatistic",dataType = "json")
    FleetResult<Map<String, List<TaskStatisticsResultDTO>>> getMissionWorkStatistic(@Query MissionWorkStatisticQueryRequest agvDataStatisticRequest);

    @GetRequest(value = "/fleet/tms/getVehicleAvaliableState",dataType = "json")
    FleetResult<VehicleAvaliableStateDTO> getVehicleAvailableState();

    @GetRequest(value = "/fleet/tms/getMarkerList",dataType = "json")
    FleetResult<List<MarkerForTmsDTO>> getMarkerList(@Query MarkerListRequest request);

    /**
     * 获取充电桩列表（通过 TMS 接口避免权限控制）
     * @param params 查询参数
     * @return 充电桩列表
     */
    @GetRequest(value = "/fleet/tms/chargeStation/list")
    FleetResult<List<ChargeStationDTO>> getChargeStationList(@Query Map<String, Object> params);

    /**
     * 获取充电桩详情（通过 TMS 接口避免权限控制）
     * @param id 充电桩ID
     * @return 充电桩详情
     */
    @GetRequest(value = "/fleet/tms/chargeStation/info")
    FleetResult<ChargeStationDTO> getChargeStationInfo(@Query("id") Long id);

    /**
     * 查询所有在线并且可用的AGV
     *
     * @return
     */
    @GetRequest(value = "/api/v3/vehicles/free")
    List<AgvDTO> listFreeVehicles();

    // ==================== 四大功能相关接口 ====================

    /**
     * 获取多台机器人统计数据（汇总）
     * 对应Fleet系统的 /fleet/statistics/vehicle 接口
     */
    @GetRequest(value = "/fleet/statistics/vehicle", dataType = "json")
    FleetResult<VehicleStatisticsDTO> getVehicleStatistics(@Query("startTime") String startTime,
                                                           @Query("endTime") String endTime);

    /**
     * 获取单台机器人详细统计数据
     * 对应Fleet系统的 /fleet/statistics/vehicle/{vehicleCode} 接口
     * 注意：这个接口返回的是单台机器人的统计数据，结构与VehicleStatisticsDTO相同
     *
     * 正确的调用示例：
     * GET /fleet/statistics/vehicle/1?startTime=2025-05-01&endTime=2025-05-29
     */
    @GetRequest(value = "/fleet/statistics/vehicle/${vehicleCode}", dataType = "json")
    FleetResult<VehicleStatisticsDTO> getVehicleDetailStatistics(@DataVariable("vehicleCode") String vehicleCode,
                                                                 @Query("startTime") String startTime,
                                                                 @Query("endTime") String endTime);

    /**
     * 获取异常统计数据
     * 对应Fleet系统的 /fleet/statistics/abnormal 接口
     */
    @LogEnabled(logResponseContent = true)
    @GetRequest(value = "/fleet/statistics/abnormal", dataType = "json")
    FleetResult<AbnormalStatisticsDTO> getAbnormalStatistics(@Query("startTime") String startTime,
                                                            @Query("endTime") String endTime);

    /**
     * 获取Fleet系统日志（分页）- 通过TMS接口
     * 对应Fleet系统的 /fleet/tms/page 接口
     * 用于查询异常日志数据
     */
    @GetRequest(value = "/fleet/tms/page", dataType = "json")
    FleetResult<FleetLogPageDTO> getFleetLogsByTms(@Query("vehicleCodes") String vehicleCodes,
                                                   @Query("type") String type,
                                                   @Query("createDate") String createDate,
                                                   @Query("pageNum") Integer page,
                                                   @Query("pageSize") Integer limit);

    /**
     * 获取通知记录数据
     * 对应Fleet系统的 /fleet/api/v1/abnormalNotices 接口
     */
    @LogEnabled(logResponseContent = true)
    @PostRequest(value = "/fleet/api/v1/abnormalNotices", dataType = "json")
    FleetResult<List<AbnormalApiDTO>> getAbnormalNotices(@JSONBody AbnormalNoticesApiDTO request);

    /**
     * 分页查询通知记录数据
     * 对应Fleet系统的 /fleet/notice/record/page 接口（需要登录）
     */
    @LogEnabled(logResponseContent = true)
    @GetRequest(value = "/fleet/notice/record/page", dataType = "json")
    FleetResult<NoticeRecordPageDTO> getNoticeRecordPage(@Query("code") String code,
                                                         @Query("description") String description,
                                                         @Query("level") Integer level,
                                                         @Query("pageNum") Integer pageNum,
                                                         @Query("pageSize") Integer pageSize,
                                                         @Query("vehicleCode") String vehicleCode,
                                                         @Query("status") Integer status,
                                                         @Query("createDate") String createDate);

    /**
     * 分页查询通知记录数据（TMS专用接口，无需登录）
     * 对应Fleet系统的 /fleet/tms/notice/record/page 接口
     */
    @LogEnabled(logResponseContent = true)
    @GetRequest(value = "/fleet/tms/notice/record/page", dataType = "json")
    FleetResult<FleetNoticeRecordPageDTO> getTmsNoticeRecordPage(@Query("code") String code,
                                                                 @Query("description") String description,
                                                                 @Query("level") Integer level,
                                                                 @Query("pageNum") Integer pageNum,
                                                                 @Query("pageSize") Integer pageSize,
                                                                 @Query("vehicleCode") String vehicleCode,
                                                                 @Query("status") Integer status,
                                                                 @Query("createDate") String createDate);
}
