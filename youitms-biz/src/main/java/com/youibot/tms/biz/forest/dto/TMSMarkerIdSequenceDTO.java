package com.youibot.tms.biz.forest.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR>
 * @Date :Created in 上午10:52 2021/1/8
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
@ApiModel(value = "TMSMarkerIdSequence", description = "TMS对同一个机器人和一组markerId按照距离进行排序")
public class TMSMarkerIdSequenceDTO {

    @ApiModelProperty(value = "要排序的agvCode")
    private String agvCode;

    @ApiModelProperty(value = "一组待排序的MarkerId")
    private Collection<String> markerIds;
}
