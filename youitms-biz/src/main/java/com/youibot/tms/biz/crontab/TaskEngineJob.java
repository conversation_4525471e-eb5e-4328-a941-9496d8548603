package com.youibot.tms.biz.crontab;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.google.common.collect.Sets;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.api.dto.MCSAgvInfoDTO;
import com.youibot.tms.biz.common.exception.BizConstants;
import com.youibot.tms.biz.entity.FromToRecord;
import com.youibot.tms.biz.entity.Port;
import com.youibot.tms.biz.enums.TaskNodeAction;
import com.youibot.tms.biz.forest.dto.FreeAgvAndNotFreeReasonRO;
import com.youibot.tms.biz.forest.enums.VehicleStatus;
import com.youibot.tms.biz.secs.handler.S66F3CommandHandler;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.common.utils.StringUtils;
import com.youibot.tms.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@DisallowConcurrentExecution
public class TaskEngineJob implements Job {



    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try {
//            TaskDispatchAgvService taskDispatchAgvService = SpringUtils.getBean(TaskDispatchAgvService.class);
//            taskDispatchAgvService.loopAgvReqDataQueue();
            AgvStatusReport bean = SpringUtils.getBean(AgvStatusReport.class);

            bean.triggerAgvInfoReport() ;
        }catch (Exception e) {
            log.error("任务调度异常:{}",Throwables.getStackTraceAsString(e));
        }
    }


}
