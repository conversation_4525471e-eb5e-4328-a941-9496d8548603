package com.youibot.tms.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.api.dto.FromToPageRequest;
import com.youibot.tms.biz.api.dto.LocalFromToVO;
import com.youibot.tms.biz.cache.AgvDTOCache;
import com.youibot.tms.biz.utils.BayNameUtils;
import com.youibot.tms.biz.common.exception.BizConstants;
import com.youibot.tms.biz.entity.*;

import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

import com.youibot.tms.biz.enums.*;
import com.youibot.tms.biz.flow.common.CommonWorkFlowVariable;
import com.youibot.tms.biz.flow.common.enums.MergeMode;
import com.youibot.tms.biz.flow.dto.InsertTaskParams;
import com.youibot.tms.biz.flow.handel.WorkFlowTaskHandler;
import com.youibot.tms.biz.mapper.FromToRecordMapper;
import com.youibot.tms.biz.secs.CachePool;
import com.youibot.tms.biz.secs.DeviceControlStateManager;
import com.youibot.tms.common.core.dto.ResultDTO;
import lombok.extern.slf4j.Slf4j;
import com.youibot.tms.biz.secs.enums.model.ControlStateEnum;
import com.youibot.tms.biz.secs.handler.S66F3CommandHandler;
import com.youibot.tms.biz.secs.communicator.secs1OnTcpIp.Secs1OnTcpIpReceiverCommunicator;
import com.shimizukenta.secs.secs2.Secs2;
import com.youibot.tms.biz.secs.Util;
import java.net.SocketAddress;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.biz.service.handler.StatusHandler;
import com.youibot.tms.biz.service.handler.StatusHandlerFactory;
import com.youibot.tms.workflow.core.executor.WorkFlowExecutor;
import com.youibot.tms.workflow.core.flow.WorkFlow;
import com.youibot.tms.workflow.entity.WorkFlowEntity;
import org.apache.commons.collections4.CollectionUtils;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.stream.Collectors;

import static com.youibot.tms.biz.common.exception.BizConstants.CANCEL_COMMANDS;

/**
 * <AUTHOR>
 * @date 2024-08-30 16:08
 */
@Slf4j
@Service
public class FromToRecordServiceImpl extends ServiceImpl<FromToRecordMapper, FromToRecord> implements FromToRecordService {
    @Resource
    private WorkFlowTaskHandler workFlowTaskHandler;

    @Resource
    private SecsService secsService;

    @Resource
    private Secs1OnTcpIpReceiverCommunicator communicator;

    @Resource
    private DeviceControlStateManager deviceControlStateManager;

    @Resource
    private PortService portService;

    @Resource
    private InsertTaskService insertTaskService;

    @Resource
    private CommunicationDeviceService communicationDeviceService;

    @Resource
    private TaskNodeService taskNodeService;

    @Resource
    protected TaskLogService taskLogService;

    @Autowired
    private WorkFlowExecutor workFlowExecutor;

    @Autowired
    private TaskService taskService;

    @Autowired
    private FleetProxyService fleetProxyService;

    @Resource
    private AgvService agvService;

    @Resource
    private ZoneAgvMappingService zoneAgvMappingService;

    @Resource
    private TaskResourceLockManager taskResourceLockManager;

    @Override
    public void cancel(Long recordId) {
        FromToRecord fromToRecord = this.getById(recordId);
        Assert.notNull(fromToRecord, "任务不存在");

        if (fromToRecord.getSource() == FromToRecordSourceEnum.HOST) {
            // 检查对应设备的控制状态
            String deviceIp = fromToRecord.getDeviceIp();
            if (deviceIp != null) {
                // 使用设备特定的控制状态检查
                if (!deviceControlStateManager.isDeviceOnlineByIp(deviceIp)) {
                    throw new RuntimeException("设备[IP:" + deviceIp + "]offline状态无法取消");
                }
            } else {
                // 兼容旧数据，使用全局状态检查
                if (CachePool.getControl() != ControlStateEnum.ONLINE_REMOTE) {
                    throw new RuntimeException("offline状态无法取消");
                }
            }
        }

        List<FromToRecord> records = this.list(new LambdaQueryWrapper<FromToRecord>()
                .eq(FromToRecord::getCommandId, fromToRecord.getCommandId()));

        Assert.isTrue(records.stream().map(FromToRecord::getStatus).allMatch(FromToRecordStatusEnum::cancellable), "任务已开始，无法取消");


        try {
            // 命令未开始则报取消，命令异常报异常终了
            this.syncRun(() -> {
                WorkFlow workFlow = null;
                if(StringUtils.isNotBlank(fromToRecord.getFlowId())){
                     workFlow = workFlowTaskHandler.getWorkFlowById(fromToRecord.getFlowId());
                    // 标记指令取消
                    CommonWorkFlowVariable variables = workFlow.getVariables(CommonWorkFlowVariable.class);
                    JSONObject taskGlobalVariable = variables.getTaskGlobalVariable();
                    Set<String> cancelCommands = taskGlobalVariable.getObject("cancel_commands", new TypeReference<HashSet<String>>() {
                    });
                    cancelCommands = cancelCommands == null ? new HashSet<>() : cancelCommands;
                    cancelCommands.add(fromToRecord.getCommandId());
                    taskGlobalVariable.put( CANCEL_COMMANDS, cancelCommands);

                    workFlow.lambadaUpdateWorkFlowById(com.youibot.tms.workflow.core.lambada.LambdaQueryWrapper.<WorkFlowEntity>lambdaQuery()
                            .set(WorkFlowEntity::getId, workFlow.getId())
                            .set(WorkFlowEntity::getVariables, variables)
                            .set(WorkFlowEntity::getVariablesClazz, workFlow.getVariablesClazz()));
                    List<String> status = TaskNodeStatus.cancellableStatus().parallelStream().map(TaskNodeStatus::name).collect(Collectors.toList());
                    LambdaQueryWrapper<TaskNode> select = Wrappers.lambdaQuery(TaskNode.class).eq(TaskNode::getCustomTaskCode, fromToRecord.getCommandId()).in(TaskNode::getStatus, status).select(TaskNode::getFleetTaskNo);
                    List<TaskNode> list = taskNodeService.list(select);
                  if(CollectionUtils.isEmpty(list)){
                        return;
                    }
                    List<String>  taskNo = list.stream().filter(p ->Objects.nonNull(p) && StringUtils.isNotBlank(p.getFleetTaskNo())).map(TaskNode::getFleetTaskNo).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(taskNo)){
                        return;
                    }else{
                        FleetProxyService.CancelTaskResult cancelTaskResult = fleetProxyService.cancelTask(taskNo);//fleetProxyService.
                        log.info("cancelTaskResult:{}", JSON.toJSONString(cancelTaskResult));
                    }


                }


                if (records.stream().map(FromToRecord::getStatus).allMatch(status -> status == FromToRecordStatusEnum.NOT_START)) {
                    // 未开始，报取消
                    if (fromToRecord.getSource() == FromToRecordSourceEnum.HOST) {
                        log.info("指令[{}]取消", fromToRecord.getCommandId());
                        // 发送规范的S66F45取消消息
                        sendCancelCompletionReport(fromToRecord);
                    }
                } else {
                    if(Objects.nonNull( workFlow)){
                        // 异常，报异常终了
                        if (fromToRecord.getSource() == FromToRecordSourceEnum.HOST) {
                            log.info("指令[{}]异常终了", fromToRecord.getCommandId());
                            workFlowTaskHandler.skipCurrentNodeRecovery(workFlow.getId());
                        } else {
                            workFlowTaskHandler.stop(workFlow.getId());
                        }
                    }

                }
            });

            // 标记为已取消
            this.update(new LambdaUpdateWrapper<FromToRecord>()
                    .set(FromToRecord::getStatus, FromToRecordStatusEnum.CANCEL)
                    .in(FromToRecord::getStatus, FromToRecordStatusEnum.cancellableStatus())
                    .eq(FromToRecord::getFlowId, fromToRecord.getFlowId())
                    .eq(FromToRecord::getCommandId, fromToRecord.getCommandId()));
        } finally {
            if(CollectionUtils.isNotEmpty( records)){
                List<Long> collect = records.parallelStream().map(FromToRecord::getId).collect(Collectors.toList());
                updateRecordStatus(collect, FromToRecordStatusEnum.CANCEL);
                taskNodeService.update(Wrappers.<TaskNode>lambdaUpdate().eq(TaskNode::getCustomTaskCode, fromToRecord.getCommandId()).set(TaskNode::getStatus, TaskNodeStatus.CANCEL));

                // 当agvCode不为空时，更新相关的待合并记录
//                clearPendingMergeRecords(fromToRecord.getAgvCode());
            }

        }
    }

    @Override
    public List<FromToRecord> current() {
        return this.list(new LambdaQueryWrapper<FromToRecord>().in(FromToRecord::getStatus, FromToRecordStatusEnum.currents()));
    }

    @Override
    public void record(List<FromToRecord> records, String flowId) {
        for (FromToRecord record : records) {
            record.setFlowId(flowId);
        }
        this.saveOrUpdateBatch(records);
    }

    @Override
    public List<String> getTopNRecords(int n, Collection<String> agvCodes , Set<String> processedCommandIds) {
        LambdaQueryWrapper<FromToRecord> queryWrapper = new LambdaQueryWrapper<FromToRecord>()
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .isNull(FromToRecord::getFlowId);

        // AGV code 条件逻辑
        if (CollUtil.isNotEmpty(agvCodes)) {
            List<String> trimmedAgvCodes = agvCodes.stream()
                    .filter(StrUtil::isNotBlank)
                    .map(String::trim)
                    .collect(Collectors.toList());

            queryWrapper.and(wrapper -> {
                // 条件：agv_code为空 或 agv_code在给定列表中
                wrapper.apply("LENGTH(TRIM(agv_code)) = 0");
                if (!trimmedAgvCodes.isEmpty()) {
                    wrapper.or().in(FromToRecord::getAgvCode, trimmedAgvCodes);
                }
            });
        } else {
            // 如果 agvCodes 为空，只匹配 agv_code 为空的记录
            queryWrapper.and(wrapper -> wrapper.apply("LENGTH(TRIM(agv_code)) = 0"));
        }
        if(CollUtil.isNotEmpty(processedCommandIds)){
            queryWrapper.notIn(FromToRecord::getCommandId, processedCommandIds);
        }

        queryWrapper.orderByDesc(FromToRecord::getPriority)
                .orderByAsc(FromToRecord::getCreateTime);

        List<FromToRecord> allCandidates = this.list(queryWrapper);

        if (CollUtil.isEmpty(allCandidates)) {
            log.info("没有找到符合条件的记录");
            return null;
        }

        // 按 commandId 去重，只保留第一条

        Map<String, FromToRecord> uniqueMap = new LinkedHashMap<>();
       List<String> res = Lists.newArrayList();
        for (FromToRecord record : allCandidates) {
            String commandId = record.getCommandId();
            if (!uniqueMap.containsKey(commandId)) {
                uniqueMap.put(commandId, record);
                res.add(commandId);
            }
            if (uniqueMap.size() >= n) {
                break;
            }
        }

        List<FromToRecord> result = new ArrayList<>(uniqueMap.values());
        log.info("找到 {} 个不同 commandId 的任务记录，返回前 {} 条", result.size(), n);

        return res;
    }

    @Override
    public List<String> getTopNRecordsByBayName(int n, String bayName, Collection<String> agvCodes, Set<String> processedCommandIds) {
        if (StringUtils.isBlank(bayName)) {
            log.warn("bayName为空，返回空列表");
            return new ArrayList<>();
        }

        log.info("开始获取bayName[{}]的前{}条任务记录，可用AGV: {}, 已处理命令: {}",
                bayName, n, agvCodes, processedCommandIds.size());

        LambdaQueryWrapper<FromToRecord> queryWrapper = new LambdaQueryWrapper<FromToRecord>()
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .isNull(FromToRecord::getFlowId);

        // 过滤已处理的命令ID
        if (CollUtil.isNotEmpty(processedCommandIds)) {
            queryWrapper.notIn(FromToRecord::getCommandId, processedCommandIds);
        }

        // 根据deviceIp过滤，只获取属于指定bayName的任务
        List<String> bayIpAddresses = getBayIpAddresses(bayName);
        if (CollUtil.isEmpty(bayIpAddresses)) {
            log.warn("bayName[{}]没有对应的IP地址，返回空列表", bayName);
            return new ArrayList<>();
        }
        queryWrapper.in(FromToRecord::getDeviceIp, bayIpAddresses);

        // AGV过滤逻辑
        if (CollUtil.isNotEmpty(agvCodes)) {
            queryWrapper.and(wrapper -> {
                wrapper.in(FromToRecord::getAgvCode, agvCodes)
                       .or()
                       .apply("LENGTH(TRIM(agv_code)) = 0");
            });
        }

        // 排序：优先级降序，创建时间升序
        queryWrapper.orderByDesc(FromToRecord::getPriority)
                   .orderByAsc(FromToRecord::getCreateTime);

        List<FromToRecord> allCandidates = this.list(queryWrapper);

        if (CollUtil.isEmpty(allCandidates)) {
            log.info("bayName[{}]没有找到符合条件的记录", bayName);
            return new ArrayList<>();
        }

        // 按 commandId 去重，只保留第一条
        Map<String, FromToRecord> uniqueMap = new LinkedHashMap<>();
        List<String> res = new ArrayList<>();
        for (FromToRecord record : allCandidates) {
            String commandId = record.getCommandId();
            if (!uniqueMap.containsKey(commandId)) {
                uniqueMap.put(commandId, record);
                res.add(commandId);
            }
            if (uniqueMap.size() >= n) {
                break;
            }
        }

        log.info("bayName[{}]找到 {} 个不同 commandId 的任务记录，返回前 {} 条",
                bayName, uniqueMap.size(), n);

        return res;
    }

    @Override
    public Map<String, List<String>> getTopNRecordsGroupedByBayName(int n, Collection<String> agvCodes, Set<String> processedCommandIds) {
        log.info("开始按bayName分组获取任务记录，每组{}条，可用AGV: {}, 已处理命令: {}",
                n, agvCodes, processedCommandIds.size());

        Map<String, List<String>> result = new HashMap<>();

        // 获取所有活跃的bayName
        List<String> activeBayNames = communicationDeviceService.getActiveBayNames();
        if (CollUtil.isEmpty(activeBayNames)) {
            log.warn("没有找到活跃的bayName");
            return result;
        }

        // 为每个bayName获取任务
        for (String bayName : activeBayNames) {
            List<String> bayRecords = getTopNRecordsByBayName(n, bayName, agvCodes, processedCommandIds);
            if (CollUtil.isNotEmpty(bayRecords)) {
                result.put(bayName, bayRecords);
            }
        }

        log.info("按bayName分组完成，共{}个bay有任务: {}", result.size(), result.keySet());
        return result;
    }

    /**
     * 获取指定bayName对应的IP地址列表
     */
    private List<String> getBayIpAddresses(String bayName) {
        if (StringUtils.isBlank(bayName)) {
            return new ArrayList<>();
        }

        // 通过CommunicationDevice查找该bayName对应的所有IP地址
        LambdaQueryWrapper<CommunicationDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommunicationDevice::getBayName, bayName)
                   .isNotNull(CommunicationDevice::getIpAddress);

        List<CommunicationDevice> devices = communicationDeviceService.list(queryWrapper);
        return devices.stream()
                     .map(CommunicationDevice::getIpAddress)
                     .filter(StringUtils::isNotBlank)
                     .collect(Collectors.toList());
    }


    @Override
    public void pickUp(String flowId, String carrierId, String agvCode ,  JSONObject forItem) {
        S66F3CommandHandler.Location location = forItem.toJavaObject(S66F3CommandHandler.Location.class);
        FromToRecord record = this.getOne(new LambdaQueryWrapper<FromToRecord>()
                .eq(FromToRecord::getFlowId, flowId)
                .eq(FromToRecord::getCommandId, location.getCommandId())
                .eq(FromToRecord::getCarrierId, carrierId)
                .ne(FromToRecord::getStatus, FromToRecordStatusEnum.CANCEL)
                .eq(FromToRecord::getFormToSeq, location.getFormToSeq())

        )

                ;

        if (record != null) {
//            updateStatus(record, FromToRecordStatusEnum.PICK_UP_ING);
            // 更新AGV编号
            record.setAgvCode(agvCode);
            this.updateById(record);
        }
    }

    @Override
    public void putDown(String flowId, String carrierId ,  JSONObject forItem) {
      /*  S66F3CommandHandler.Location location = forItem.toJavaObject(S66F3CommandHandler.Location.class);
        FromToRecord record = this.getOne(new LambdaQueryWrapper<FromToRecord>()
                .eq(FromToRecord::getFlowId, flowId)
                .eq(FromToRecord::getCommandId, location.getCommandId())
                .eq(FromToRecord::getCarrierId, carrierId)
                .eq(FromToRecord::getFormToSeq, location.getFormToSeq())
                .ne(FromToRecord::getStatus, FromToRecordStatusEnum.CANCEL))

                ;

        if (record != null) {
            updateStatus(record, FromToRecordStatusEnum.PUT_DOWN_ING);
        }*/
    }

    @Override
    public void error(String flowId, String carrierId) {
        FromToRecord record = this.getOne(new LambdaQueryWrapper<FromToRecord>()
                .eq(FromToRecord::getFlowId, flowId)
                .eq(FromToRecord::getCarrierId, carrierId));

        Assert.notNull(record, "记录不存在");
        updateStatus(record, FromToRecordStatusEnum.ERROR);

        // 更新同一指令的其他记录
        this.update(new LambdaUpdateWrapper<FromToRecord>()
                .set(FromToRecord::getStatus, FromToRecordStatusEnum.ERROR)
                .eq(FromToRecord::getFlowId, flowId)
                .eq(FromToRecord::getCommandId, record.getCommandId())
                .ne(FromToRecord::getId, record.getId()));
    }

    @Override
    public synchronized void syncRun(Runnable runnable) {
        runnable.run();
    }

    @Override
    public void localCreate(LocalFromToVO local) {
        Port from = portService.getByCode(local.getFromCode());
        Assert.notNull(from, "点位[{}]不存在", local.getFromCode());
        Port to = portService.getByCode(local.getToCode());
        Assert.notNull(to, "点位[{}]不存在", local.getToCode());

        String commandId = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_MS_PATTERN);
        // 使用传入的carrierId，如果为空则生成随机字符串
        String lotId = StrUtil.isNotBlank(local.getCarrierId()) ? local.getCarrierId() : "";
  /*
        S66F3CommandHandler.Location locationFrom = new S66F3CommandHandler.Location(from.getMapCode(), from.getActionParam().get(0).getMarkerCode(), true, true, commandId, commandId, from.getId(), from.getPortType(), from.getCode(), lotId, TaskNodeAction.PICK_UP, from.getCode(), to.getCode());
        S66F3CommandHandler.Location locationTo = new S66F3CommandHandler.Location(to.getMapCode(), to.getActionParam().get(0).getMarkerCode(), true, true, commandId, commandId, to.getId(), to.getPortType(), to.getCode(), lotId, TaskNodeAction.PUT_DOWN, from.getCode(), to.getCode());

     JSONObject taskGlobalVariable = new JSONObject();
        taskGlobalVariable.put("from", JSON.parseObject(JSON.toJSONString(locationFrom)));
        taskGlobalVariable.put("to", JSON.parseObject(JSON.toJSONString(locationTo)));

        WorkFlowTemplateEntity workFlowTemplateEntity = workFlowTemplateService.getWorkFlowTemplateByCode("local");
        Assert.notNull(workFlowTemplateEntity, () -> new RuntimeException("local流程不存在"));

        Task task = taskService.createTaskByFlowTemplateId(workFlowTemplateEntity.getId(), taskGlobalVariable);
        WorkFlow workFlow = flowTemplateAssistService.initWorkflow(workFlowTemplateEntity.getId(), taskGlobalVariable, commonWorkFlowListener);

        task.setFlowId(workFlow.getId());
        task.setCustomTaskId(commandId);
        // 使用传入的优先级，如果为空则使用默认值1
        task.setPriority(local.getPriority() != null ? local.getPriority() : 1);
        taskService.updateById(task);
        workFlowExecutor.executor(workFlow.getId());*/

        FromToRecord record = FromToRecord.record(FromToRecordSourceEnum.LOCAL, commandId, lotId, from.getCode(), to.getCode(), null, from.getMergeCode(), to.getMergeCode(), true);
//        record.setFlowId(workFlow.getId());
        // 设置 AGV 编码
        record.setAgvCode(local.getAgvCode());
        // 设置优先级
        record.setPriority(local.getPriority() != null ? local.getPriority() : 0);
        this.save(record);
    }

    @Override
    public synchronized void localCreateBatch(List<LocalFromToVO> locals) {
        if (CollUtil.isEmpty(locals)) {
            return;
        }
        Assert.isTrue( locals.size()<=4, "指令大小不能超过4个", locals.size());
        String commandId = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_MS_PATTERN);

        // 批量验证点位并创建记录
        List<FromToRecord> records = new ArrayList<>(locals.size());
        int fromToSeq = 0;
        for (LocalFromToVO local : locals) {

            Port from = portService.getByCode(local.getFromCode());
            Assert.notNull(from, "点位[{}]不存在", local.getFromCode());
            Port to = portService.getByCode(local.getToCode());
            Assert.notNull(to, "点位[{}]不存在", local.getToCode());
            // 使用传入的carrierId，如果为空则生成随机字符串
            String lotId = StrUtil.isNotBlank(local.getCarrierId()) ? local.getCarrierId() : "";

            // tail 只有在最后一个 FromToRecord 时才为 true（或者列表大小为1时）
            boolean isLastRecord = (fromToSeq == locals.size() - 1);


            FromToRecord record = FromToRecord.record(FromToRecordSourceEnum.LOCAL, commandId, lotId, from.getCode(), to.getCode(), null, from.getMergeCode(), to.getMergeCode(), isLastRecord, fromToSeq);
//        record.setFlowId(workFlow.getId());
            // 设置 AGV 编码
            record.setAgvCode(local.getAgvCode());
            // 设置优先级
            record.setPriority(local.getPriority() != null ? local.getPriority() : 0);

            log.info("LOCAL批量创建搬运指令，料盒ID={}，起点={}，终点={}，索引={}/{}，tail={}",
                    lotId, from.getCode(), to.getCode(), fromToSeq, locals.size() - 1, isLastRecord);

            fromToSeq = fromToSeq +1 ;
            records.add(record);
        }

        // 批量保存记录
        boolean saveResult = this.saveBatch(records);

        // 如果保存成功，唤醒AGVOrderMergerThread线程处理订单
        if (saveResult) {
            // 增加版本号并唤醒线程
            try {
                insertTaskService.insert(records, commandId);
            } catch (Exception e) {
                log.error("创建任务失败:{}", Throwables.getStackTraceAsString(e));
            }

        }
    }

    @Override
    public List<FromToRecord> getByCommandId(String commandId) {
        return list(new LambdaQueryWrapper<FromToRecord>().eq(FromToRecord::getCommandId, commandId));
    }

    @Override
    public Page<FromToRecord> history(FromToPageRequest request) {
        // 使用PageHelper进行分页
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        LambdaQueryWrapper<FromToRecord> queryWrapper = new LambdaQueryWrapper<FromToRecord>()
                // 载具ID查询
                .eq(StrUtil.isNotBlank(request.getCarrierId()), FromToRecord::getCarrierId, request.getCarrierId())
                // 任务状态查询，只查询当前状态
                .in(FromToRecord::getStatus, FromToRecordStatusEnum.currents());

        // 按创建时间降序排序
        queryWrapper.orderByDesc(FromToRecord::getCreateTime);

        List<FromToRecord> list = list(queryWrapper);

        // 创建MyBatis-Plus Page对象以保持接口兼容性
        Page<FromToRecord> page = new Page<>(request.getPageNum(), request.getPageSize());
        page.setRecords(list);

        // 从PageHelper获取分页信息
        PageInfo<FromToRecord> pageInfo = new PageInfo<>(list);
        page.setTotal(pageInfo.getTotal());
        page.setCurrent(pageInfo.getPageNum());
        page.setSize(pageInfo.getPageSize());

        return page;
    }

    @Override
    public Page<FromToRecord> pageQuery(FromToPageRequest request) {
        // 使用PageHelper进行分页
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        LambdaQueryWrapper<FromToRecord> queryWrapper = buildQueryWrapper(request);

        List<FromToRecord> list = list(queryWrapper);

        // 创建MyBatis-Plus Page对象以保持接口兼容性
        Page<FromToRecord> page = new Page<>(request.getPageNum(), request.getPageSize());
        page.setRecords(list);

        // 从PageHelper获取分页信息
        PageInfo<FromToRecord> pageInfo = new PageInfo<>(list);
        page.setTotal(pageInfo.getTotal());
        page.setCurrent(pageInfo.getPageNum());
        page.setSize(pageInfo.getPageSize());

        return page;
    }

    @Override
    public List<FromToRecord> export(FromToPageRequest request) {
        LambdaQueryWrapper<FromToRecord> queryWrapper = buildQueryWrapper(request);

        return list(queryWrapper);
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 检查是否有正在进行中的任务
        List<FromToRecord> records = listByIds(ids);
        for (FromToRecord record : records) {
            // 如果任务状态不是COMPLETED或END，则不允许删除
            if (record.getStatus() != FromToRecordStatusEnum.COMPLETED) {
                log.warn("任务[{}]状态为[{}]，不允许删除", record.getId(), record.getStatus());
                return false;
            }
        }

        return removeByIds(ids);
    }

    @Override
    public boolean batchUpdateStatus(List<Long> ids, FromToRecordStatusEnum status) {
        if (ids == null || ids.isEmpty() || status == null) {
            return false;
        }

        // 批量更新状态
        LambdaUpdateWrapper<FromToRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(FromToRecord::getId, ids)
                .set(FromToRecord::getStatus, status);

        return update(updateWrapper);
    }

    /**
     * 构建查询条件
     *
     * @param request 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<FromToRecord> buildQueryWrapper(FromToPageRequest request) {
        LambdaQueryWrapper<FromToRecord> queryWrapper = new LambdaQueryWrapper<FromToRecord>();

        // 精确查询
        if (StrUtil.isNotBlank(request.getCarrierId())) {
            queryWrapper.eq(FromToRecord::getCarrierId, request.getCarrierId());
        }
        if (StrUtil.isNotBlank(request.getFromCode())) {
            queryWrapper.eq(FromToRecord::getFromCode, request.getFromCode());
        }
        if (StrUtil.isNotBlank(request.getToCode())) {
            queryWrapper.eq(FromToRecord::getToCode, request.getToCode());
        }
        if (StrUtil.isNotBlank(request.getAgvCode())) {
            queryWrapper.eq(FromToRecord::getAgvCode, request.getAgvCode());
        }

        // 任务状态查询
        // 模糊查询 - 使用优化的方式
        if (StrUtil.isNotBlank(request.getCarrierIdLike())) {
            // 添加%前缀优化，如果用户输入的是完整的ID，则使用精确查询
            if (StrUtil.isNotBlank(request.getCarrierId()) && request.getCarrierIdLike().equals(request.getCarrierId())) {
                queryWrapper.eq(FromToRecord::getCarrierId, request.getCarrierIdLike());
            } else {
                queryWrapper.like(FromToRecord::getCarrierId, request.getCarrierIdLike());
            }
        }
        if (StrUtil.isNotBlank(request.getFromCodeLike())) {
            if (StrUtil.isNotBlank(request.getFromCode()) && request.getFromCodeLike().equals(request.getFromCode())) {
                queryWrapper.eq(FromToRecord::getFromCode, request.getFromCodeLike());
            } else {
                queryWrapper.like(FromToRecord::getFromCode, request.getFromCodeLike());
            }
        }
        if (StrUtil.isNotBlank(request.getToCodeLike())) {
            if (StrUtil.isNotBlank(request.getToCode()) && request.getToCodeLike().equals(request.getToCode())) {
                queryWrapper.eq(FromToRecord::getToCode, request.getToCodeLike());
            } else {
                queryWrapper.like(FromToRecord::getToCode, request.getToCodeLike());
            }
        }
        if (StrUtil.isNotBlank(request.getAgvCodeLike())) {
            if (StrUtil.isNotBlank(request.getAgvCode()) && request.getAgvCodeLike().equals(request.getAgvCode())) {
                queryWrapper.eq(FromToRecord::getAgvCode, request.getAgvCodeLike());
            } else {
                queryWrapper.like(FromToRecord::getAgvCode, request.getAgvCodeLike());
            }
        }

        // 任务状态查询 - 单个状态
        if (request.getStatus() != null) {
            queryWrapper.eq(FromToRecord::getStatus, request.getStatus());
        }

        // 任务状态查询 - 多个状态
        if (request.getStatusList() != null) {
            queryWrapper.in(FromToRecord::getStatus, FromToRecordStatusEnum.parseEnumList( request.getStatusList()));
        }

        // 创建时间范围查询
        if (request.getCreateTimeStart() != null) {
            queryWrapper.ge(FromToRecord::getCreateTime, request.getCreateTimeStart());
        }
        // 创建时间结束
        if (request.getCreateTimeEnd() != null) {
            queryWrapper.le(FromToRecord::getCreateTime, request.getCreateTimeEnd());
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(FromToRecord::getCreateTime);

        return queryWrapper;
    }

    /**
     * 检查状态转换是否有效
     *
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     * @return 是否有效
     */
    private boolean isValidStatusTransition(FromToRecordStatusEnum oldStatus, FromToRecordStatusEnum newStatus) {
        // 允许转换到取消状态
        if (newStatus == FromToRecordStatusEnum.CANCEL) {
            return FromToRecordStatusEnum.cancellable(oldStatus);
        }

        // 允许转换到错误状态
        if (newStatus == FromToRecordStatusEnum.ERROR) {
            return true;
        }

        // 对于其他状态，根据状态流转图定义有效的转换
        switch (oldStatus) {
            case NOT_START:
                return newStatus == FromToRecordStatusEnum.DISPATCHING;

            case DISPATCHING:
                return newStatus == FromToRecordStatusEnum.PICKUP_NAVIGATING;
            case PICKUP_NAVIGATING:
                return newStatus == FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION;
            case ARRIVE_PICKUP_POSITION:
                return newStatus == FromToRecordStatusEnum.PICK_UP_ING;

            case PICK_UP_ING:
                return newStatus == FromToRecordStatusEnum.PICK_UP_FINISHED;

            case PICK_UP_FINISHED:
                return newStatus == FromToRecordStatusEnum.PUT_DOWN_NAVIGATING;

            case PUT_DOWN_NAVIGATING:
                return newStatus == FromToRecordStatusEnum.ARRIVE_DROP_POSITION;

            case ARRIVE_DROP_POSITION:
                return newStatus == FromToRecordStatusEnum.PUT_DOWN_ING;

            case PUT_DOWN_ING:
                return newStatus == FromToRecordStatusEnum.PUT_DOWN_FINISHED;

            case PUT_DOWN_FINISHED:
                return newStatus == FromToRecordStatusEnum.COMPLETED;

            default:
                return false;
        }
    }

    /**
     * 更新时间戳并计算时长
     */
    private void updateTimestampsAndDurations(FromToRecord record,
                                              FromToRecordStatusEnum oldStatus,
                                              FromToRecordStatusEnum newStatus) {
        LocalDateTime now = LocalDateTime.now();

        // 使用状态处理器工厂获取对应的处理器
        StatusHandler handler = StatusHandlerFactory.getHandler(newStatus);
        if (handler != null) {
            handler.handle(record, now);
        }
    }

    /**
     * 记录状态变化日志
     */
    private void logStatusChange(FromToRecord record,
                                 FromToRecordStatusEnum oldStatus,
                                 FromToRecordStatusEnum newStatus) {
        log.info("记录状态变化: recordId={}, carrierId={}, oldStatus={}, newStatus={}",
                record.getId(), record.getCarrierId(), oldStatus, newStatus);
    }

    @Override
    public boolean updateRecordStatus(Long recordId, FromToRecordStatusEnum newStatus) {
        FromToRecord record = this.getById(recordId);
        if (record == null) {
            log.warn("记录不存在: {}", recordId);
            return false;
        }
        return updateStatus(record, newStatus);
    }

    /**
     * 更新记录状态并自动发送S66F45搬送完成报告（如果需要）
     * 这个方法专门用于需要发送SECS消息的状态更新场景
     *
     * @param recordId 记录ID
     * @param newStatus 新状态
     * @param location 位置信息（用于SECS消息）
     * @param task 任务信息（用于SECS消息）
     * @return 是否更新成功
     */
    public boolean updateRecordStatusWithSecsReport(Long recordId, FromToRecordStatusEnum newStatus,
                                                   S66F3CommandHandler.Location location, Task task) {
        // 先更新状态
        boolean updateResult = updateRecordStatus(recordId, newStatus);

        if (updateResult) {
            // 状态更新成功后，发送SECS报告
            FromToRecord record = this.getById(recordId);
            if (record != null) {
                sendTransportCompletionReport(record, newStatus, location, task);
            }
        }

        return updateResult;
    }

    @Override
    public boolean updateRecordStatus(Collection<Long> recordId, FromToRecordStatusEnum status) {
    for (Long id : recordId) {
        updateRecordStatus(id, status);
    }
        return  true;
    }

    /**
     * 更新记录状态
     *
     * @param record    记录
     * @param newStatus 新状态
     * @return 是否更新成功
     */
    private boolean updateStatus(FromToRecord record, FromToRecordStatusEnum newStatus) {
        FromToRecordStatusEnum oldStatus = record.getStatus();

        // 检查状态转换是否有效
   /*     if (!isValidStatusTransition(oldStatus, newStatus)) {
            log.warn("无效的状态转换: {} -> {}", oldStatus, newStatus);
            return false;
        }*/

        // 更新时间戳并计算时长
        updateTimestampsAndDurations(record, oldStatus, newStatus);

        // 更新状态
        record.setStatus(newStatus);

        // 记录日志
        logStatusChange(record, oldStatus, newStatus);

        // 保存到数据库
        boolean update = this.updateById(record);


        return  update;
    }

    @Override
    public boolean changePriority(Long recordId, Integer priority) {
        if (recordId == null || priority == null) {
            return false;
        }

        FromToRecord record = this.getById(recordId);
        if (record == null) {
            log.warn("记录不存在: {}", recordId);
            return false;
        }

        // 检查任务状态，只有NOT_START状态的任务才能修改优先级
        if (record.getStatus() != FromToRecordStatusEnum.NOT_START) {
            log.warn("只有未开始的任务才能修改优先级，当前状态: {}", record.getStatus());
            return false;
        }

        // 更新优先级
        record.setPriority(priority);

        // 保存到数据库
        return this.updateById(record);
    }

    @Override
    public boolean skipCurrentNode(Long recordId) {
        if (recordId == null) {
            return false;
        }

        FromToRecord record = this.getById(recordId);
        if (record == null) {
            log.warn("记录不存在: {}", recordId);
            return false;
        }

        // 检查任务状态，只有进行中的任务才能跳过当前节点
        if (record.getStatus() == FromToRecordStatusEnum.NOT_START ||
                record.getStatus() == FromToRecordStatusEnum.COMPLETED) {
            log.warn("只有进行中的任务才能跳过当前节点，当前状态: {}", record.getStatus());
            return false;
        }

        // 根据当前状态确定下一个状态
        FromToRecordStatusEnum nextStatus;
        switch (record.getStatus()) {
            case DISPATCHING:
                nextStatus = FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION;
                break;
            case ARRIVE_PICKUP_POSITION:
                nextStatus = FromToRecordStatusEnum.PICK_UP_ING;
                break;
            case PICK_UP_ING:
                nextStatus = FromToRecordStatusEnum.PICK_UP_FINISHED;
                break;
            case PICK_UP_FINISHED:
                nextStatus = FromToRecordStatusEnum.PICKUP_NAVIGATING;
                break;
            case PICKUP_NAVIGATING:
                nextStatus = FromToRecordStatusEnum.ARRIVE_DROP_POSITION;
                break;
            case ARRIVE_DROP_POSITION:
                nextStatus = FromToRecordStatusEnum.PUT_DOWN_ING;
                break;
            case PUT_DOWN_ING:
                nextStatus = FromToRecordStatusEnum.PUT_DOWN_FINISHED;
                break;
            case PUT_DOWN_FINISHED:
                nextStatus = FromToRecordStatusEnum.COMPLETED;
                break;
            default:
                log.warn("当前状态无法跳过: {}", record.getStatus());
                return false;
        }

        // 更新状态
        return updateStatus(record, nextStatus);
    }

    @Override
    public boolean restart(Long recordId) {
        if (recordId == null) {
            return false;
        }

        FromToRecord record = this.getById(recordId);
        if (record == null) {
            log.warn("记录不存在: {}", recordId);
            return false;
        }

        // 检查任务状态，只有COMPLETED状态的任务才能重新执行
        if (record.getStatus() != FromToRecordStatusEnum.COMPLETED) {
            log.warn("只有已完成的任务才能重新执行，当前状态: {}", record.getStatus());
            return false;
        }

        LambdaUpdateWrapper<FromToRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FromToRecord::getId, recordId)
                .set(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .set(FromToRecord::getArrivePickupPositionTime, null)
                .set(FromToRecord::getStartPickupTime, null)
                .set(FromToRecord::getFinishPickupTime, null)
                .set(FromToRecord::getArriveDropPositionTime, null)
                .set(FromToRecord::getStartDropTime, null)
                .set(FromToRecord::getFinishDropTime, null)
                .set(FromToRecord::getTotalDuration, null)
                .set(FromToRecord::getDispatchDuration, null)
                .set(FromToRecord::getPickupNavigationDuration, null)
                .set(FromToRecord::getPickupDuration, null)
                .set(FromToRecord::getDropNavigationDuration, null)
                .set(FromToRecord::getDropDuration, null)
                .set(FromToRecord::getMergeMode, null)

                .set(FromToRecord::getFlowId, null);

        // 保存到数据库
        return this.update( updateWrapper);
    }

    @Override
    public List<FromToRecord> exportByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据ID列表查询记录
        return listByIds(ids);
    }

    /**
     * 锁定并获取可用的记录
     * 根据端口编号和AGV编号获取状态为NOT_START、flowId为空的记录
     * 按照优先级降序、创建时间升序排序
     * 使用同步方式避免并发捕获问题
     *
     * @param fromMergeKey  取料合并标标识符
     * @param toMergeKey     放料合并标标识符
     * @return 符合条件的记录列表
     */
    @Override
    public synchronized List<FromToRecord> lockAndFetchAvailable(String fromMergeKey, String toMergeKey, InsertTaskParams params) {
        log.info("开始锁定并获取可用的记录，端口编号: {}, AGV编号: {}", fromMergeKey, toMergeKey, params.getAgvCode());

        Set<MergeMode> configuredModes = params.getConfiguredModes();
        boolean from = false;
        boolean to = false;
        for (MergeMode mode :configuredModes) {
            if(!from && mode.isFrom()){
                from = true;
            }
            if(!to && mode.isTo()){
                to = true;
            }
            if(from && to){
                break;
            }
        }

        // 使用final变量来避免lambda表达式中的编译问题
        String finalFromMergeKey = fromMergeKey;
        String finalToMergeKey = toMergeKey;

        if(!params.isFroms()){
            boolean contains = configuredModes.contains(MergeMode.CONTINUOUS_ROUTE);
            if(contains){

                finalFromMergeKey = params.getCurrentMergeCode();
                from = true;
                to = false;
            }else{
                return new ArrayList<>();
            }
        }

        // 构建查询条件
        LambdaQueryWrapper<FromToRecord> queryWrapper = new LambdaQueryWrapper<FromToRecord>()
                // 状态为未开始
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                // 流程ID为空（表示还未被分配到流程）
                .isNull(FromToRecord::getFlowId);

        // 如果指定了端口编号，添加端口编号条件（OR关系）
        boolean hasFromCondition = StrUtil.isNotBlank(finalFromMergeKey) && from;
        boolean hasToCondition = StrUtil.isNotBlank(finalToMergeKey) && to;

        final String finalFinalFromMergeKey = finalFromMergeKey;
        if (hasFromCondition || hasToCondition) {
            queryWrapper.and(wrapper -> {
                if (hasFromCondition && hasToCondition) {
                    // 两个条件都存在时，使用OR关系
                    wrapper.eq(FromToRecord::getFromMergeKey, finalFinalFromMergeKey)
                           .or()
                           .eq(FromToRecord::getToMergeKey, finalToMergeKey);
                } else if (hasFromCondition) {
                    // 只有from条件
                    wrapper.eq(FromToRecord::getFromMergeKey, finalFinalFromMergeKey);
                } else {
                    // 只有to条件
                    wrapper.eq(FromToRecord::getToMergeKey, finalToMergeKey);
                }

            });
        }

        String agvCode = params.getAgvCode();
        // 如果指定了AGV编号，添加AGV编号条件
        // 如果没有指定AGV编号，则获取agvCode为空的记录
        if (StrUtil.isNotBlank(agvCode)) {
            String trimmedAgvCode = agvCode.trim();
            if (StrUtil.isNotEmpty(trimmedAgvCode)) {
                queryWrapper.and(wrapper -> wrapper
                        .apply("(agv_code = {0} OR LENGTH(TRIM(agv_code)) = 0 or agv_code is null)", trimmedAgvCode));
            } else {
                queryWrapper.and(wrapper -> wrapper
                        .apply("LENGTH(TRIM(agv_code)) = 0 or agv_code is null "));
            }
        } else {
            queryWrapper.and(wrapper -> wrapper
                    .apply("LENGTH(TRIM(agv_code)) = 0  or agv_code is null "));
        }

        // 添加deviceIp过滤条件
        // 使用传入的deviceIp，只能与相同deviceIp的任务合并
        String currentDeviceIp = params.getDeviceIp();
        if (StrUtil.isNotBlank(currentDeviceIp)) {
            // 如果当前任务有deviceIp，只查询相同deviceIp的记录
            queryWrapper.eq(FromToRecord::getDeviceIp, currentDeviceIp);
            log.info("添加deviceIp过滤条件: {}", currentDeviceIp);
        } else {
            // 如果当前任务没有deviceIp，只查询deviceIp为空的记录
            queryWrapper.and(wrapper -> wrapper
                    .apply("(device_ip IS NULL OR LENGTH(TRIM(device_ip)) = 0)"));
            log.info("添加deviceIp过滤条件: 只查询deviceIp为空的记录");
        }

        // 按照优先级降序、创建时间升序排序
        queryWrapper.orderByDesc(FromToRecord::getPriority)
                .orderByAsc(FromToRecord::getCreateTime);

        // 查询符合条件的记录
        List<FromToRecord> records = this.list(queryWrapper);

        if (records.isEmpty()) {
            log.info("没有找到符合条件的记录");
            return records;
        }

        log.info("找到{}条符合条件的记录", records.size());

        // 对这些记录进行锁定处理，防止并发捕获
        // 这里可以根据实际需求进行处理，例如标记这些记录为正在处理状态
        // 或者在数据库中添加锁定标记

        // 返回符合条件的记录
        return records;
    }



    @Override
    public void updateRecordStatus(List<String> commandIds, FromToRecordStatusEnum fromToRecordStatusEnum) {

        List<FromToRecord> list = super.list(Wrappers.lambdaQuery(FromToRecord.class).in(FromToRecord::getCommandId, commandIds).select(FromToRecord::getId));

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> collect = list.parallelStream().map(FromToRecord::getId).collect(Collectors.toList());
        updateRecordStatus(collect, fromToRecordStatusEnum);
    }

    @Override
    public ResultDTO updatebyMissionWorkHttpReq(String flowId ,String flowNodeId , JSONObject data, S66F3CommandHandler.Location location) {
        String status = data.getString(BizConstants.FROM_TO_RECORDSTATUS);
        FromToRecordStatusEnum fromToRecordStatusEnum = EnumUtils.getEnum(FromToRecordStatusEnum.class, status);
      if(Objects.nonNull( fromToRecordStatusEnum)){
          TaskNodeStatus taskNodeStatus = TaskNodeStatus.fromSubStatus(fromToRecordStatusEnum);
          FromToRecord fromToRecord = this.getOne(Wrappers.lambdaQuery(FromToRecord.class).eq(FromToRecord::getFlowId, flowId).eq(FromToRecord::getCommandId, location.getCommandId()).eq(FromToRecord::getFormToSeq, location.getFormToSeq()));
         if(Objects.isNull( fromToRecord)){
             return ResultDTO.success();
         }
          FromToRecordStatusEnum currentStatus = fromToRecord.getStatus();

          if(Objects.nonNull(taskNodeStatus) ){

              /**
               * 如果当前的状态已经更新过就跳过
               */
              String msgs = data.getString(BizConstants.FLEET_ERROR_MSG);
              if(fromToRecordStatusEnum.ordinal() <= currentStatus.ordinal()){
                  return ResultDTO.success();
              }
              Task task = taskService.getOne(Wrappers.lambdaQuery(Task.class).eq(Task::getFlowId, flowId));
              Integer seq = task.getSeq();
              String taskNo = data.getString(BizConstants.TASK_NO);
              TaskNode one = taskNodeService.getOne(Wrappers.lambdaQuery(TaskNode.class).eq(TaskNode::getCustomTaskCode, location.getCommandId()).eq(TaskNode::getRecordSeq, location.getIndex()).eq(TaskNode::getActionType, location.getActionType()).eq(TaskNode::getTaskCode, task.getCode()));
              taskNodeService.update(Wrappers.lambdaUpdate(TaskNode.class).eq(TaskNode::getId, one.getId()).set(TaskNode::getStatus, taskNodeStatus)
                      .set(TaskNode::getFlowSeq, seq)
                      .set(TaskNode::getFleetTaskNo, taskNo )
                      .set (TaskNode::getRemark,msgs)
              );
              seq = seq +  1;
              taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getId, task.getId()).set(Task::getSeq, seq));

              ; ;
              // 更新状态并自动发送S66F45搬送完成报告
              this.updateRecordStatusWithSecsReport(fromToRecord.getId(), fromToRecordStatusEnum, location, task);
              recordTaskLog(one.getTaskCode(),fromToRecordStatusEnum,flowId , flowNodeId );

          }

      }


        return ResultDTO.success();
    }

    /**
     * 记录任务派车日志
     *
     * @param taskCode 任务编号
     */
    @Override
    public void recordTaskLog(String taskCode, FromToRecordStatusEnum fromToRecordStatusEnum, String flowId, String flowNodeId) {
        TaskNodeStatus taskNodeStatus = TaskNodeStatus.fromSubStatus(fromToRecordStatusEnum);
        String title ="状态变更";
        log.info("记录派车日志，任务[{}]，日志说明：{}", taskCode, taskNodeStatus.name());

        TaskLog taskLog = new TaskLog();
        taskLog.setTaskCode(taskCode);
        taskLog.setTitle(title);
        taskLog.setTaskStatus(taskNodeStatus.name() );
        taskLog.setRemark( FromToRecordStatusEnum.describeContext(fromToRecordStatusEnum) );
        taskLog.setFlowId(flowId);
        taskLog.setFlowNodeId( flowNodeId );
        taskLogService.save(taskLog);
    }

    /**
     * 获取CommandId所需的绑定AGV列表
     * @param fromToRecordList 任务记录列表
     * @return 所需的绑定AGV集合
     */
    @Override
    public Set<String> getRequiredAgvsForCommand(List<FromToRecord> fromToRecordList) {
        Set<String> requiredAgvs = new HashSet<>();

        for (FromToRecord record : fromToRecordList) {
            // 检查起点
            Port fromPort = portService.getByCode(record.getFromCode());
            addBoundAgvsFromPort(fromPort, requiredAgvs);

            // 检查终点
            Port toPort = portService.getByCode(record.getToCode());
            addBoundAgvsFromPort(toPort, requiredAgvs);
        }

        return requiredAgvs;
    }

    /**
     * 从Port中获取绑定的AGV并添加到集合中
     * @param port Port对象
     * @param requiredAgvs 绑定AGV集合
     */
    private void addBoundAgvsFromPort(Port port, Set<String> requiredAgvs) {
        if (port instanceof PortStocker) {
            PortStocker portStocker = (PortStocker) port;
            if (portStocker.getZoneStockerId() != null) {
                List<Agv> boundAgvs = agvService.listByZoneStockerId(portStocker.getZoneStockerId());
                if (CollectionUtils.isNotEmpty(boundAgvs)) {
                    boundAgvs.forEach(agv -> requiredAgvs.add(agv.getAgvCode()));
                }
            }
        } else if (port instanceof PortEq) {
            PortEq portEq = (PortEq) port;
            if (portEq.getZoneAreaId() != null) {
                List<Agv> boundAgvs = agvService.listByZoneAreaId(portEq.getZoneAreaId());
                if (CollectionUtils.isNotEmpty(boundAgvs)) {
                    boundAgvs.forEach(agv -> requiredAgvs.add(agv.getAgvCode()));
                }
            }
        }
    }

    /**
     * 发送S66F45取消完成报告
     * 使用规范的S66F45消息格式发送取消完成通知
     *
     * @param fromToRecord 搬送记录
     */
    private void sendCancelCompletionReport(FromToRecord fromToRecord) {
        try {
            log.info("发送S66F45取消完成报告: CommandId={}, Source={}, DeviceId={}",
                    fromToRecord.getCommandId(), fromToRecord.getSource(), fromToRecord.getDeviceId());

            // 构建取消状态的S66F45消息数据
            Secs2 messageData = buildS66F45MessageData(fromToRecord, FromToRecordStatusEnum.CANCEL, null, null);

            // 根据FromToRecord的来源发送SECS消息
            sendMessageToAppropriateClient(fromToRecord, messageData);

            log.info("发送S66F45取消完成报告成功: CommandId={}, ActionFlag=4(取消完成), Source={}, DeviceId={}",
                    fromToRecord.getCommandId(), fromToRecord.getSource(), fromToRecord.getDeviceId());

        } catch (Exception e) {
            log.error("发送S66F45取消完成报告失败: CommandId={}, Error={}",
                    fromToRecord.getCommandId(), e.getMessage(), e);
        }
    }

    /**
     * 发送S66F45搬送完成报告
     *
     * @param fromToRecord 搬送记录
     * @param status 当前状态
     * @param location 位置信息
     * @param task 任务信息
     */
    private void sendTransportCompletionReport(FromToRecord fromToRecord, FromToRecordStatusEnum status,
                                             S66F3CommandHandler.Location location, Task task) {
        try {
            // 判断是否需要发送搬送完成报告
            if (!shouldSendCompletionReport(status)) {
                return;
            }

            // 检查是否为同一commandId下的最后一个需要发送报告的FromToRecord
            if (!isLastRecordToSendReport(fromToRecord, status)) {
                log.info("跳过S66F45报告发送: CommandId={}, RecordId={}, 不是最后一个需要发送报告的记录",
                    fromToRecord.getCommandId(), fromToRecord.getId());
                return;
            }

            // 检查设备状态，只有在线远程模式才发送
            String deviceIp = fromToRecord.getDeviceIp();
            if (deviceIp != null) {
                // 使用设备特定的控制状态检查
                if (!deviceControlStateManager.isDeviceOnlineByIp(deviceIp)) {
                    log.info("设备[IP:{}]不在在线远程模式，跳过S66F45搬送完成报告发送", deviceIp);
                    return;
                }
            } else {
                // 兼容旧数据，使用全局状态检查
                log.info("手动下发无法确定 host  ，跳过S66F45搬送完成报告发送: Status={}", CachePool.getControl());

            }

            // 构建S66F45消息数据
            Secs2 messageData = buildS66F45MessageData(fromToRecord, status, location, task);

            // 根据FromToRecord的来源发送SECS消息
            sendMessageToAppropriateClient(fromToRecord, messageData);

            log.info("发送S66F45搬送完成报告成功: TaskCode={}, Status={}, CommandId={}, ActionFlag={}, Source={}, DeviceId={}",
                    task.getCode(), status, location.getCommandId(), getActionFlag(status, fromToRecord),
                    fromToRecord.getSource(), fromToRecord.getDeviceId());

        } catch (Exception e) {
            log.error("发送S66F45搬送完成报告失败: TaskCode={}, Status={}, Error={}",
                    task.getCode(), status, e.getMessage(), e);
        }
    }

    /**
     * 判断是否需要发送搬送完成报告
     * 只有完成动作、异常和取消状态才需要发送S66F45消息
     *
     * @param status 当前状态
     * @return true if should send completion report
     */
    private boolean shouldSendCompletionReport(FromToRecordStatusEnum status) {
        switch (status) {
            // 完成状态 - 需要报告
            case PICK_UP_FINISHED:      // 取料完成
            case PUT_DOWN_FINISHED:     // 放料完成
            case COMPLETED:             // 整体搬送完成

            // 异常状态 - 需要报告
            case ERROR:                 // 异常

            // 取消状态 - 需要报告
            case CANCEL:                // 取消完成
                return true;

            // 进行中状态 - 不需要报告
            case NOT_START:             // 未开始
            case DISPATCHING:           // 派车中
            case PICKUP_NAVIGATING:     // 取料导航中
            case ARRIVE_PICKUP_POSITION: // 到达取料位置
            case PICK_UP_ING:           // 取料中
            case PUT_DOWN_NAVIGATING:   // 放料导航中
            case ARRIVE_DROP_POSITION:  // 到达放料位置
            case PUT_DOWN_ING:          // 放料中
            default:
                return false;
        }
    }

    /**
     * 构建S66F45消息数据
     * 根据AGVC-HOST通信仕様書规范构建S66F45"搬送完了報告"消息
     *
     * @param fromToRecord 搬送记录
     * @param status 当前状态
     * @param location 位置信息
     * @param task 任务信息
     * @return SECS2 message data
     */
    private Secs2 buildS66F45MessageData(FromToRecord fromToRecord, FromToRecordStatusEnum status,
                                        S66F3CommandHandler.Location location, Task task) {

        // 获取动作状态标志
        byte actionFlag = getActionFlag(status, fromToRecord);

        // 获取AGV号 (1~10)，如果未确定则为0
        byte agvNo = getAgvNumber(task);

        // 获取同一commandId下的所有FromToRecord来构建批次信息
        List<FromToRecord> allRecords = getAllRecordsForCommand(fromToRecord.getCommandId());

        // 批次数量 (LNUM) 最大4
        byte batchCount = (byte) Math.min(allRecords.size(), 4);

        // 构建批次信息列表
        List<Secs2> batchList = new ArrayList<>();
        for (int i = 0; i < batchCount; i++) {
            FromToRecord record = allRecords.get(i);

            // 卡盒ID (最大10字符)
            String palletId = formatToFixedLength(getPalletId(record), 10);

            // 卡盒数量 (将来用)
            byte palletCount = 1; // 默认为1

            // 搬送源 (FROM) 和目的地 (TO) (各最大10字符)
            String source = formatToFixedLength(getLocationCode(record.getFromCode()), 10);
            String destination = formatToFixedLength(getLocationCode(record.getToCode()), 10);

            // 构建单个批次信息 L,3
            Secs2 batchInfo = Secs2.list(
                Secs2.ascii(palletId),               // <ID1> 卡盒ID (固定10字符)
                Secs2.binary(palletCount),           // <CNUM> 卡盒数量 (将来用)
                Secs2.list(                          // 搬送源和目标信息 L,2
                    Secs2.ascii(source),             // <SRC1> 搬送源 (固定10字符)
                    Secs2.ascii(destination)         // <DST1> 搬送目标 (固定10字符)
                )
            );
            batchList.add(batchInfo);
        }

        Secs2 list = Secs2.list(batchList);

        // 构建SECS2消息格式 - 根据规范 L,4
        // L,4
        //   BIN,1 <FLG>: 动作状态
        //   BIN,1 <AGVNO>: AGV号 (直接是BIN,1，不是L,1)
        //   BIN,1 <LNUM>: 批次数量 (直接是BIN,1，不是L,1)
        //   每个批次信息 (重复LNUM次) L,3:
        //     ASC,10 <ID1>: 卡盒ID
        //     BIN,1 <CNUM>: 卡盒数量
        //     L,2:
        //       ASC,10 <SRC1>: 搬送源
        //       ASC,10 <DST1>: 搬送目标

        List<Secs2> messageElements = new ArrayList<>();
        messageElements.add( Secs2.list( Secs2.binary(actionFlag)) );    // <FLG> 动作状态
        messageElements.add( Secs2.list(Secs2.binary(agvNo)) );         // <AGVNO> AGV号 (直接BIN,1)
        messageElements.add( Secs2.list(Secs2.binary(batchCount)) );    // <LNUM> 批次数量 (直接BIN,1)
        messageElements.add( list );                // 批次信息列表

        return Secs2.list(messageElements);
    }

    /**
     * 获取动作状态标志
     * 根据AGVC-HOST通信仕様書S66F45消息的FLG参数定义：
     * 1: 積み動作完了 (装载操作完成/取料完成)
     * 2: 降ろし動作完了 (卸载操作完成/放料完成)
     * 3: 積ろし動作異常完了 (装载/卸载操作异常完成)
     * 4: 降ろし動作異常完了 (卸载操作异常完成)
     * 5: キャンセル完了 (取消完成)
     * その他: 其他异常
     *
     * @param status 当前状态
     * @param fromToRecord 搬送记录，用于判断ERROR状态的具体阶段
     * @return action flag
     */
    private byte getActionFlag(FromToRecordStatusEnum status, FromToRecord fromToRecord) {
        switch (status) {
            // 取料相关状态 (積み動作)
            case PICK_UP_FINISHED:
                return 1; // 積み動作完了 (装载操作完成/取料完成)

            // 放料相关状态 (降ろし動作)
            case PUT_DOWN_FINISHED:
                return 2; // 降ろし動作完了 (卸载操作完成/放料完成)
            case COMPLETED:
                return 2; // 整个搬送任务完成，视为降ろし動作完了

            // 异常状态 - 根据任务进度判断是取料异常还是放料异常
            case ERROR:
                return getErrorActionFlag(fromToRecord);

            // 取消状态
            case CANCEL:
                return 5; // キャンセル完了 (取消完成)

            // 进行中的状态和其他状态 - 这些状态不应该发送S66F45消息
            case NOT_START:
            case DISPATCHING:
            case PICKUP_NAVIGATING:
            case ARRIVE_PICKUP_POSITION:
            case PICK_UP_ING:
            case PUT_DOWN_NAVIGATING:
            case ARRIVE_DROP_POSITION:
            case PUT_DOWN_ING:
            default:
                // 这些状态不应该触发S66F45消息发送，但如果意外调用，返回其他异常
                return 3; // 默认为積ろし動作異常完了
        }
    }

    /**
     * 根据任务进度判断ERROR状态对应的动作标志
     * 通过分析任务的时间戳来判断错误发生在取料阶段还是放料阶段
     *
     * @param fromToRecord 搬送记录
     * @return error action flag
     */
    private byte getErrorActionFlag(FromToRecord fromToRecord) {
        if (fromToRecord == null) {
            return 3; // 默认为積ろし動作異常完了
        }

        // 如果已经完成取料，说明错误发生在放料阶段
        if (fromToRecord.getFinishPickupTime() != null) {
            return 4; // 降ろし動作異常完了 (卸载操作异常完成)
        }

        // 如果已经开始取料但未完成，说明错误发生在取料阶段
        if (fromToRecord.getStartPickupTime() != null) {
            return 3; // 積ろし動作異常完了 (装载/卸载操作异常完成)
        }

        // 如果还没开始取料，默认为積ろし動作異常完了
        return 3; // 積ろし動作異常完了 (装载/卸载操作异常完成)
    }

    /**
     * 获取AGV号
     * 根据AGVC-HOST通信仕様書规范，AGV号应该从AgvDTO的name字段中提取
     *
     * @param task 任务信息
     * @return AGV number (1-10, 0 if undetermined)
     */
    private byte getAgvNumber(Task task) {
        try {
            // 从任务中获取AGV编号
            String agvCode = null;
            if (Objects.nonNull( task) && StringUtils.isNotBlank(task.getAgvCode())) {
                agvCode = task.getAgvCode();
                // 通过FleetProxyService获取AgvDTO，从name字段提取AGV号
                AgvDTO agvDTO = getAgvDTOByCode(agvCode);
                if (agvDTO != null && StringUtils.isNotBlank(agvDTO.getName())) {
                    String agvName = agvDTO.getName();

                    // 使用BayNameUtils工具类提取机器人编号
                    int robotNumber = BayNameUtils.extractRobotNumber(agvName);
                    if (robotNumber > 0 && robotNumber <= 10) {
                        log.debug("从AgvDTO.name[{}]中提取AGV号: {}", agvName, robotNumber);
                        return (byte) robotNumber;
                    } else if (robotNumber > 10) {
                        // 如果数字超过10，映射到1-10范围内
                        int mappedNumber = (robotNumber % 10);
                        if (mappedNumber == 0) {
                            mappedNumber = 10;
                        }
                        log.info("AGV名称[{}]中的机器人编号[{}]超出范围，映射为[{}]", agvName, robotNumber, mappedNumber);
                        return (byte) mappedNumber;
                    }
                }

                // 如果无法从AgvDTO.name获取，尝试从agvCode中提取（兼容旧逻辑）
                log.warn("无法从AgvDTO.name获取AGV号，尝试从agvCode[{}]中提取", agvCode);
                String numberPart = agvCode.replaceAll("[^0-9]", "");
                if (StringUtils.isNotBlank(numberPart)) {
                    int agvNum = Integer.parseInt(numberPart);
                    if (agvNum >= 1 && agvNum <= 10) {
                        return (byte) agvNum;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析AGV号失败: agvCode={}, error={}", task.getAgvCode(), e.getMessage());
        }
        return 0; // 未确定时返回0
    }

    /**
     * 根据agvCode获取对应的AgvDTO
     * 优先从AgvStatusReport的缓存中获取，如果没有则查询Fleet系统
     *
     * @param agvCode AGV编号
     * @return AgvDTO对象，如果未找到则返回null
     */
    private AgvDTO getAgvDTOByCode(String agvCode) {
        try {
            // 优先从AgvStatusReport的全局缓存中获取
            AgvDTO cachedAgv = AgvDTOCache.getAgvByCode(agvCode);
            if (cachedAgv != null) {
                log.debug("从AgvStatusReport缓存中获取到AGV[{}]", agvCode);
                return cachedAgv;
            }

            // 如果缓存中没有，则查询Fleet系统（兜底方案）
            log.debug("缓存中未找到AGV[{}]，查询Fleet系统", agvCode);
            List<AgvDTO> allAgvs = fleetProxyService.listAllAgvs();
            if (allAgvs != null) {
                return allAgvs.stream()
                    .filter(agv -> agvCode.equals(agv.getAgvCode()))
                    .findFirst()
                    .orElse(null);
            }

        } catch (Exception e) {
            log.warn("获取AgvDTO失败: agvCode={}, error={}", agvCode, e.getMessage());
        }
        return null;
    }



    /**
     * 获取托盘ID
     *
     * @param fromToRecord 搬送记录
     * @return pallet ID (max 10 characters)
     */
    private String getPalletId(FromToRecord fromToRecord) {
        String palletId = fromToRecord.getCarrierId();
        if (StringUtils.isBlank(palletId)) {
            palletId = "UNKNOWN";
        }
        // 确保不超过10个字符
        return StringUtils.left(palletId, 10);
    }

    /**
     * 获取位置代码
     *
     * @param location 位置
     * @return location code (max 10 characters)
     */
    private String getLocationCode(String location) {
        if (StringUtils.isBlank(location)) {
            return "UNKNOWN";
        }
        // 确保不超过10个字符
        return StringUtils.left(location, 10);
    }

    /**
     * 获取同一commandId下的所有FromToRecord记录
     *
     * @param commandId 命令ID
     * @return 同一命令下的所有记录列表
     */
    private List<FromToRecord> getAllRecordsForCommand(String commandId) {
        if (StringUtils.isBlank(commandId)) {
            return new ArrayList<>();
        }

        return this.list(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getCommandId, commandId)
                .orderByAsc(FromToRecord::getFormToSeq)
        );
    }

    /**
     * 格式化字符串为固定长度
     * 如果字符串长度不足，则用空格填充到指定长度
     * 如果字符串长度超过，则截取到指定长度
     *
     * @param str 原始字符串
     * @param length 目标长度
     * @return 格式化后的固定长度字符串
     */
    private String formatToFixedLength(String str, int length) {
        if (str == null) {
            str = "";
        }

        if (str.length() > length) {
            return str.substring(0, length);
        } else if (str.length() < length) {
            // 用空格填充到指定长度
            return String.format("%-" + length + "s", str);
        } else {
            return str;
        }
    }

    /**
     * 根据FromToRecord的来源发送消息到合适的客户端
     * 只有HOST来源且有deviceId的记录才发送SECS消息，其他情况不发送
     * 支持一个deviceId对应多个客户端连接的情况
     *
     * @param fromToRecord 搬送记录
     * @param messageData 消息数据
     */
    private void sendMessageToAppropriateClient(FromToRecord fromToRecord, Secs2 messageData) {
        try {
            FromToRecordSourceEnum source = fromToRecord.getSource();
            Integer deviceId = fromToRecord.getDeviceId();
            String deviceIp = fromToRecord.getDeviceIp();
            String commandId = fromToRecord.getCommandId();
            FromToRecordStatusEnum status = fromToRecord.getStatus();

            if (source == FromToRecordSourceEnum.HOST && deviceId != null) {
                // 只有HOST来源且有deviceId的记录才发送给对应的设备客户端
                Set<SocketAddress> clientAddresses = getClientAddressesForDevice(deviceId, deviceIp, commandId, status);

                if (!clientAddresses.isEmpty()) {
                    sendMessageToClientAddresses(clientAddresses, deviceId, deviceIp, commandId, status, messageData);
                } else {
                    log.warn("未找到设备[ID:{}, IP:{}]对应的客户端连接，无法发送S66F45消息，CommandId: {}, Status: {}", deviceId, deviceIp, commandId, status);
                }
            } else {
                // LOCAL来源或其他情况，不发送SECS消息
                log.info("跳过发送S66F45消息: CommandId={}, Status={}, Source={}, DeviceId={}, DeviceIp={} (只有HOST来源且有deviceId才发送)", commandId, status, source, deviceId, deviceIp);
            }
        } catch (Exception e) {
            log.error("发送S66F45消息失败，CommandId: {}, Status: {}, 错误: {}", fromToRecord.getCommandId(), fromToRecord.getStatus(), e.getMessage(), e);
        }
    }

    /**
     * 获取设备对应的客户端地址集合
     * 优先使用设备IP，如果没有则使用deviceId（兼容旧数据）
     *
     * @param deviceId 设备ID
     * @param deviceIp 设备IP
     * @param commandId 命令ID
     * @param status 记录状态
     * @return 客户端地址集合
     */
    private Set<SocketAddress> getClientAddressesForDevice(Integer deviceId, String deviceIp, String commandId, FromToRecordStatusEnum status) {
        Set<SocketAddress> clientAddresses;

        if (deviceIp != null) {
            // 优先使用设备IP精确查找ClientConnection
            clientAddresses = Util.getAddressesByIp(deviceIp);
            log.debug("CommandId: {}, Status: {}, 使用设备IP[{}]查找客户端连接，找到{}个连接", commandId, status, deviceIp, clientAddresses.size());
        } else {
            // 兼容旧数据，使用deviceId查找
            clientAddresses = Util.getAddressesByDeviceId(deviceId);
            log.warn("CommandId: {}, Status: {}, 设备IP为空，使用deviceId[{}]查找客户端连接（可能不准确），找到{}个连接", commandId, status, deviceId, clientAddresses.size());
        }

        return clientAddresses;
    }

    /**
     * 向客户端地址集合发送消息
     * 针对单连接和多连接场景进行优化
     *
     * @param clientAddresses 客户端地址集合
     * @param deviceId 设备ID
     * @param deviceIp 设备IP
     * @param commandId 命令ID
     * @param status 记录状态
     * @param messageData 消息数据
     */
    private void sendMessageToClientAddresses(Set<SocketAddress> clientAddresses, Integer deviceId, String deviceIp, String commandId, FromToRecordStatusEnum status, Secs2 messageData) {
        int totalClients = clientAddresses.size();
        int successCount = 0;

        // 针对单连接场景优化日志
        if (totalClients == 1) {
            SocketAddress clientAddress = clientAddresses.iterator().next();
            try {
                log.info("CommandId: {}, Status: {}, 发送S66F45消息到HOST设备[ID:{}, IP:{}]客户端: {}", commandId, status, deviceId, deviceIp, clientAddress);
                communicator.send(clientAddress, 66, 45, true, messageData);
                successCount = 1;
                log.info("CommandId: {}, Status: {}, 设备[ID:{}, IP:{}]的S66F45消息发送成功", commandId, status, deviceId, deviceIp);
            } catch (Exception e) {
                log.error("CommandId: {}, Status: {}, 向设备[ID:{}, IP:{}]的客户端[{}]发送S66F45消息失败: {}", commandId, status, deviceId, deviceIp, clientAddress, e.getMessage());
            }
        } else {
            // 多连接场景：详细记录每个连接的发送结果
            log.info("CommandId: {}, Status: {}, 设备[ID:{}, IP:{}]有{}个客户端连接，开始批量发送S66F45消息", commandId, status, deviceId, deviceIp, totalClients);

            for (SocketAddress clientAddress : clientAddresses) {
                try {
                    log.debug("CommandId: {}, Status: {}, 发送S66F45消息到HOST设备[ID:{}, IP:{}]客户端: {}", commandId, status, deviceId, deviceIp, clientAddress);
                    communicator.send(clientAddress, 66, 45, true, messageData);
                    successCount++;
                } catch (Exception e) {
                    log.error("CommandId: {}, Status: {}, 向设备[ID:{}, IP:{}]的客户端[{}]发送S66F45消息失败: {}", commandId, status, deviceId, deviceIp, clientAddress, e.getMessage());
                }
            }

            log.info("CommandId: {}, Status: {}, 设备[ID:{}, IP:{}]的S66F45消息批量发送完成: 成功{}/总共{}个客户端", commandId, status, deviceId, deviceIp, successCount, totalClients);
        }
    }

    /**
     * 检查是否为同一commandId下的最后一个需要发送报告的FromToRecord
     *
     * @param currentRecord 当前记录
     * @param currentStatus 当前状态
     * @return true if this is the last record to send report
     */
    private boolean isLastRecordToSendReport(FromToRecord currentRecord, FromToRecordStatusEnum currentStatus) {
        try {
            String commandId = currentRecord.getCommandId();
            if (StringUtils.isBlank(commandId)) {
                log.warn("CommandId为空，无法判断是否为最后一个记录: RecordId={}", currentRecord.getId());
                return true; // 如果commandId为空，默认发送
            }

            // 查询同一commandId下的所有记录
            LambdaQueryWrapper<FromToRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FromToRecord::getCommandId, commandId)
                       .orderBy(true, true, FromToRecord::getFormToSeq); // 按formToSeq排序

            List<FromToRecord> allRecords = this.list(queryWrapper);

            if (CollectionUtils.isEmpty(allRecords)) {
                log.warn("未找到CommandId={}的记录", commandId);
                return true;
            }

            // 找到当前记录在列表中的位置
            int currentIndex = -1;
            for (int i = 0; i < allRecords.size(); i++) {
                if (Objects.equals(allRecords.get(i).getId(), currentRecord.getId())) {
                    currentIndex = i;
                    break;
                }
            }

            if (currentIndex == -1) {
                log.warn("在CommandId={}的记录列表中未找到当前记录: RecordId={}", commandId, currentRecord.getId());
                return true;
            }

            // 检查当前记录之后是否还有需要发送报告的记录
            for (int i = currentIndex + 1; i < allRecords.size(); i++) {
                FromToRecord laterRecord = allRecords.get(i);
                FromToRecordStatusEnum laterStatus = laterRecord.getStatus();

                // 如果后面的记录还没有达到需要发送报告的状态，说明当前不是最后一个
                if (laterStatus != null && !isCompletedStatus(laterStatus)) {
                    log.debug("CommandId={}还有未完成的记录: RecordId={}, Status={}, 当前记录不是最后一个",
                        commandId, laterRecord.getId(), laterStatus);
                    return false;
                }
            }

            log.info("CommandId={}的当前记录是最后一个需要发送报告的记录: RecordId={}, Status={}",
                commandId, currentRecord.getId(), currentStatus);
            return true;

        } catch (Exception e) {
            log.error("检查是否为最后一个记录时发生异常: RecordId={}, Error={}",
                currentRecord.getId(), e.getMessage(), e);
            return true; // 发生异常时默认发送，避免丢失重要消息
        }
    }

    /**
     * 判断状态是否为已完成状态（不再需要等待的状态）
     *
     * @param status 状态
     * @return true if status is completed
     */
    private boolean isCompletedStatus(FromToRecordStatusEnum status) {
        switch (status) {
            case COMPLETED:         // 已完成
            case CANCEL:            // 已取消
            case ERROR:             // 异常（也算完成）
                return true;
            default:
                return false;
        }
    }

    // ==========================================
    // 新增的派车优化相关方法实现
    // ==========================================

    @Override
    public List<FromToRecord> getValidPendingTasks() {
        // 🔥 修复：查询真正待分配的任务，排除已分配给AGV的任务
        List<FromToRecord> allTasks = list(
            Wrappers.<FromToRecord>lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .isNull(FromToRecord::getFlowId)
                // 🔥 简化：直接排除已分配给AGV的任务
                .ne(FromToRecord::getIsPendingMerge, true)
                .orderByDesc(FromToRecord::getPriority)
                .orderByAsc(FromToRecord::getCreateTime)
        );

        if (allTasks.isEmpty()) {
            log.debug("未找到任何待分配任务");
            return new ArrayList<>();
        }

        log.debug("查询到{}个待分配任务（排除已分配给AGV的任务）", allTasks.size());

        // 2. 过滤有效任务
        List<FromToRecord> validTasks = allTasks.stream()
            .filter(this::isTaskValid)
            .collect(Collectors.toList());

        log.debug("过滤后有效任务{}个", validTasks.size());
        return validTasks;
    }

    @Override
    public Map<com.youibot.tms.biz.lock.TaskLockContext, List<FromToRecord>> groupTasksByLockContext(List<FromToRecord> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return new HashMap<>();
        }

        return tasks.stream()
            .collect(Collectors.groupingBy(task ->
                taskResourceLockManager.getLockContextForTask(task)
            ));
    }

    @Override
    public boolean isTaskValid(FromToRecord record) {
        try {
            // 1. HOST来源：检查设备状态（但允许合单）
            if (record.getSource() == FromToRecordSourceEnum.HOST) {
                if (StringUtils.isBlank(record.getDeviceIp())) {
                    log.debug("HOST来源任务{}缺少设备IP", record.getCommandId());
                    return false;
                }

                if (!zoneAgvMappingService.isDeviceOnline(record.getDeviceIp())) {
                    log.debug("HOST来源任务{}的设备{}不在线", record.getCommandId(), record.getDeviceIp());
                    return false;
                }
            }

            // 2. 如果指定了AGV，检查该AGV是否可用
            if (StringUtils.isNotBlank(record.getAgvCode())) {
                if (!agvService.isAgvAvailable(record.getAgvCode())) {
                    log.debug("任务{}指定的AGV{}不可用", record.getCommandId(), record.getAgvCode());
                    return false;
                }
            }

            // 3. 所有来源的任务都可以通过验证（HOST和LOCAL都支持合单）
            return true;

        } catch (Exception e) {
            log.error("验证任务{}有效性时发生异常", record.getCommandId(), e);
            return false;
        }
    }

    /**
     * 清理待合并记录状态
     * 当AGV任务完成或取消时，清理所有以该AGV为合并目标的待合并记录
     *
     * @param agvCode AGV编号
     */
    public void clearPendingMergeRecords(String agvCode) {
        if (StringUtils.isNotBlank(agvCode)) {
            this.update(new LambdaUpdateWrapper<FromToRecord>()
                    .set(FromToRecord::getIsPendingMerge, false)
                    .set(FromToRecord::getPendingMergeTargetAgv, null)
                    .eq(FromToRecord::getPendingMergeTargetAgv, agvCode)
                    .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                    .eq(FromToRecord::getIsPendingMerge, true));
        }
    }

    @Override
    public List<FromToRecord> getLongPendingTasks(java.time.Duration threshold) {
        java.time.LocalDateTime cutoffTime = java.time.LocalDateTime.now().minus(threshold);

        return list(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .isNull(FromToRecord::getFlowId)
                .lt(FromToRecord::getCreateTime, cutoffTime)
                .orderByAsc(FromToRecord::getCreateTime)
        );
    }

    @Override
    public int getTaskCountByDeviceIp(String deviceIp) {
        if (StringUtils.isBlank(deviceIp)) {
            return 0;
        }

        return count(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getDeviceIp, deviceIp)
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .isNull(FromToRecord::getFlowId)
        );
    }

    @Override
    public List<FromToRecord> getPendingTasks() {
        return list(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .isNull(FromToRecord::getFlowId)
                .orderByDesc(FromToRecord::getPriority)
                .orderByAsc(FromToRecord::getCreateTime)
        );
    }

    @Override
    public List<FromToRecord> getTasksByDeviceIp(String deviceIp) {
        if (StringUtils.isBlank(deviceIp)) {
            return new ArrayList<>();
        }

        return list(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getDeviceIp, deviceIp)
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .isNull(FromToRecord::getFlowId)
                .orderByDesc(FromToRecord::getPriority)
                .orderByAsc(FromToRecord::getCreateTime)
        );
    }

    @Override
    public List<FromToRecord> getTasksByZone(Long zoneId) {
        if (zoneId == null) {
            return new ArrayList<>();
        }

        // TODO: 实现根据Zone ID查询任务的逻辑
        // 这需要通过Port关联到Zone，然后查询相关任务
        // 暂时返回空列表
        return new ArrayList<>();
    }

    @Override
    public int getPendingTaskCount() {
        return count(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                .isNull(FromToRecord::getFlowId)
        );
    }

    @Override
    public int getProcessingTaskCount() {
        return count(
            Wrappers.lambdaQuery(FromToRecord.class)
                .in(FromToRecord::getStatus,
                    FromToRecordStatusEnum.DISPATCHING,
                    FromToRecordStatusEnum.PICKUP_NAVIGATING,
                    FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION,
                    FromToRecordStatusEnum.PICK_UP_ING,
                    FromToRecordStatusEnum.PUT_DOWN_NAVIGATING,
                    FromToRecordStatusEnum.ARRIVE_DROP_POSITION,
                    FromToRecordStatusEnum.PUT_DOWN_ING)
        );
    }

    @Override
    public int getTodayCompletedTaskCount() {
        java.time.LocalDateTime startOfDay = java.time.LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        java.time.LocalDateTime endOfDay = startOfDay.plusDays(1);

        return count(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.COMPLETED)
                .ge(FromToRecord::getUpdateTime, startOfDay)
                .lt(FromToRecord::getUpdateTime, endOfDay)
        );
    }

    @Override
    public FromToRecord getByCommandIdAndFormToSeq(String commandId, Integer formToSeq) {
        if (StringUtils.isBlank(commandId) || formToSeq == null) {
            return null;
        }

        return getOne(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getCommandId, commandId)
                .eq(FromToRecord::getFormToSeq, formToSeq)
        );
    }

    @Override
    public List<FromToRecord> getProcessingTasksByAgv(String agvCode) {
        if (StringUtils.isBlank(agvCode)) {
            return new ArrayList<>();
        }

        return list(
            Wrappers.lambdaQuery(FromToRecord.class)
                .eq(FromToRecord::getAgvCode, agvCode)
                .in(FromToRecord::getStatus,
                    FromToRecordStatusEnum.DISPATCHING,
                    FromToRecordStatusEnum.PICKUP_NAVIGATING,
                    FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION,
                    FromToRecordStatusEnum.PICK_UP_ING,
                    FromToRecordStatusEnum.PUT_DOWN_NAVIGATING,
                    FromToRecordStatusEnum.ARRIVE_DROP_POSITION,
                    FromToRecordStatusEnum.PUT_DOWN_ING)
                .orderByDesc(FromToRecord::getUpdateTime)
        );
    }



}
