package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "PathInfoApiDTO",description = "路径信息")
public class PathInfoApiDTO {

    @ApiModelProperty(value = "定位图编码", position = 1)
    private String locatingCode;

    @ApiModelProperty(value = "开始点的控制点xy坐标", position = 2)
    private String startControl;

    @ApiModelProperty(value = "结束点的控制点xy坐标", position = 3)
    private String endControl;

    @ApiModelProperty(value = "长度", position = 4)
    private Double length;

}
