package com.youibot.tms.biz.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youibot.tms.biz.api.dto.AgvTypeDTO;
import com.youibot.tms.biz.api.request.AgvTypePageRequest;
import com.youibot.tms.biz.entity.AgvType;
import com.youibot.tms.biz.service.AgvTypeService;
import com.youibot.tms.common.annotation.Log;
import com.youibot.tms.common.core.controller.BaseController;
import com.youibot.tms.common.core.dto.ResultDTO;
import com.youibot.tms.common.core.page.TableDataInfo;
import com.youibot.tms.common.enums.BusinessType;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022-11-28
 */
@Api(tags = "AGV型号相关接口")
@RestController
@RequestMapping("/api/v1/biz/agvType")
public class AgvTypeController extends BaseController {
    @Resource
    private AgvTypeService agvTypeService;


    @GetMapping("/list")
    @ApiOperation(value = "分页查询")
    public ResultDTO<TableDataInfo<AgvTypeDTO>> list(@ApiParam AgvTypePageRequest request) {
        Page<AgvType> page = new Page<>(request.getPageNum(), request.getPageSize());
        QueryWrapper<AgvType> query = getQueryWrapper(request);
        Page<AgvType> list = agvTypeService.page(page, query);
        return getPageData(list, toDTO(list.getRecords()));
    }

    private List<AgvTypeDTO> toDTO(List<AgvType> origin) {
        if (origin != null && origin.size() > 0) {
            return origin.stream().map(AgvTypeDTO::new).collect(Collectors.toList());
        }
        return null;
    }

    @Log(title = "机器人型号同步", businessType = BusinessType.UPDATE)
    @PostMapping("/sync")
    @ApiOperation(value = "同步机器人类型")
    public ResultDTO sync() {
        try {
            agvTypeService.sync();
        } catch (BusinessException e) {
            logger.error("同步机器人型号异常", e);
            return ResultDTO.error(e.getMessage());
        }
        return ResultDTO.success();
    }


    @GetMapping("/{id}")
    @ApiOperation(value = "详情")
    public ResultDTO info(@PathVariable Long id) {
        AgvType agvType = agvTypeService.getById(id);
        return ResultDTO.success(agvType);
    }

    @Log(title = "", businessType = BusinessType.INSERT)
    @PostMapping("")
    @ApiOperation(value = "创建")
    public ResultDTO add(@RequestBody AgvTypeDTO agvModel) {
        agvTypeService.save(agvModel.parseEntity());
        return ResultDTO.success();
    }

    @Log(title = "机器人型号", businessType = BusinessType.UPDATE)
    @PutMapping("")
    @ApiOperation(value = "更新")
    public ResultDTO update(@RequestBody AgvTypeDTO agvModel) {
        //如果fleetMissionName为空，则取fleetMissionId
        if (agvModel.getFleetMoveMissionWorkName() == null) {
            agvModel.setFleetMoveMissionWorkName(agvModel.getFleetMoveMissionWorkId());
        }
        //如果前置任务名称为空，则取id
        if (agvModel.getFleetFrontMissionWorkName() == null) {
            agvModel.setFleetFrontMissionWorkName(agvModel.getFleetFrontMissionWorkId());
        }

        if(agvModel.getFleetPickUpMissionWorkName() == null){
            agvModel.setFleetPickUpMissionWorkName(agvModel.getFleetPickUpMissionWorkId());
        }

        if(agvModel.getFleetPutDownMissionWorkName() == null){
            agvModel.setFleetPutDownMissionWorkName(agvModel.getFleetPutDownMissionWorkId());
        }

        if(agvModel.getFleetSwapMissionWorkName() == null){
            agvModel.setFleetSwapMissionWorkName(agvModel.getFleetSwapMissionWorkId());
        }

        agvTypeService.updateById(agvModel.parseEntity());
        return ResultDTO.success();
    }

    @Log(title = "", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除")
    public ResultDTO deleteBatch(@PathVariable Long[] ids) {
        agvTypeService.removeByIds(Arrays.asList(ids));
        return ResultDTO.success();
    }


    @Log(title = "", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    @ApiOperation(value = "导出", notes = "导出列表", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void export(HttpServletResponse response, AgvTypePageRequest request) {
        QueryWrapper<AgvType> query = getQueryWrapper(request);
        //默认按添加时间倒序
        query.orderByDesc("create_time");
        List<AgvType> list = agvTypeService.list(query);

        ExcelUtil<AgvType> util = new ExcelUtil<>(AgvType.class);
        util.exportExcel(response, list, "");
    }

    @GetMapping("/template")
    @ApiOperation(value = "导入模板下载", notes = "模板下载", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void excel(HttpServletResponse response) {
        //导出一个空表格作为模板
        List<AgvType> list = new ArrayList<>();
        ExcelUtil<AgvType> util = new ExcelUtil<>(AgvType.class);
        util.exportExcel(response, list, "");
    }

    @Log(title = "", businessType = BusinessType.IMPORT)
    @PutMapping("/import")
    @ApiOperation(value = "导入", notes = "导入列表")
    public ResultDTO importExcel(MultipartFile file) throws Exception {

        ExcelUtil<AgvType> util = new ExcelUtil<>(AgvType.class);
        List<AgvType> list = util.importExcel(file.getInputStream());
        agvTypeService.saveBatch(list);
        return ResultDTO.success();
    }

}
