package com.youibot.tms.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youibot.tms.biz.api.request.AgvUpdateRequest;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.enums.PortType;
import com.youibot.tms.common.exception.base.BusinessException;

import java.util.List;
import java.util.Map;
import com.youibot.tms.biz.api.dto.AgvDTO;

/**
 * <p>
 * AGV配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
public interface AgvService extends IService<Agv> {
    /**
     * 分页查询机器人列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<Agv> page(Page<Agv> page, QueryWrapper<Agv> query);
    /**
     * 从Fleet系统同步机器人基本信息
     */
    void syncAgv() throws BusinessException;

    /**
     * 更新机器人，主要是更新机器人关联的区域和库区
     *
     * @param agv 机器人信息
     */
    void updateAgv(AgvUpdateRequest agv);

    /**
     * 根据机器人编号查找机器人
     *
     * @param agvCode 机器人编号
     * @return 返回机器人信息
     */
    Agv selectByAgvCode(String agvCode);

    /**
     * 根据机器人编号锁定机器人
     *
     * @param agvCode 机器人编号
     * @return 返回成功与否
     */
    boolean lock(String agvCode, List<String> taskCodes, String workFlowId);

    /**
     * 根据机器人编号释放机器人并且释放机器人车身上的所有车厢
     *
     * @param agvCode 机器人编号
     * @return 返回成功与否
     */
    boolean releaseAgvAndReleaseAgvStoragePort(String agvCode);

    /**
     * 根据机器人编号释放机器人，但是不会释放车身上的车厢
     *
     * @param agvCode 机器人编号
     * @return 返回成功与否
     */
    boolean releaseAgv(String agvCode);

    /**
     * 根据机器人编号释放机器人并且结束正在执行的工作流流程任务
     *
     * @param agvCode 机器人编号
     * @return 返回成功与否
     */
    boolean releaseAndStopTask(String agvCode);

    /**
     * 获取TMS中空闲的机器人
     *
     * @return 空闲的机器人列表
     */
    List<Agv> listFree();

    /**
     * 获取可用的机器人，Fleet中空闲的AGV和TMS中空闲的机器人交集
     * 为什么不能用Fleet中空闲的AGV-TMS中占用的机器人？
     * 因为Fleet中空闲的机器人可能还没同步到TMS中，相关配置还没设置，无法使用
     *
     * @return
     */
    List<Agv> listAvailableAgv();




    List<String> getAvailableAgvCodes();

    /**
     * 根据区域ID查询机器人列表
     */
    List<Agv> listByZoneAreaId(Long zoneAreaId);

    /**
     * 根据库区ID查询机器人列表
     */
    List<Agv> listByZoneStockerId(Long zoneStockerId);

    List<Agv> listByPort(PortType portType, Long id, String excludeAgvCode);

    void clearAgvLocation(Agv agv, String markerCode);

    void shutdownTask(String agvCode);

    /**
     * 根据bayName过滤AGV代码
     *
     * @param bayName bay名称
     * @return 属于该bayName的可用AGV代码列表
     */
    List<String> getAvailableAgvCodesByBayName(String bayName);

    /**
     * 按bayName分组AGV
     *
     * @param agvs AGV列表
     * @return 按bayName分组的AGV映射
     */
    Map<String, List<AgvDTO>> groupAgvsByBayName(List<AgvDTO> agvs);

    // ==========================================
    // 新增的派车优化相关方法
    // ==========================================

    /**
     * 检查指定AGV是否可用
     *
     * @param agvCode AGV代码
     * @return 是否可用
     */
    boolean isAgvAvailable(String agvCode);

    /**
     * 获取过滤后的空闲AGV
     *
     * @return 空闲AGV列表
     */
    List<AgvDTO> getFilteredFreeAgvs();

    /**
     * 获取空闲AGV代码
     *
     * @return 空闲AGV代码列表
     */
    List<String> getIdleAgvCodes();

    /**
     * 获取长时间空闲的AGV
     *
     * @param threshold 时间阈值
     * @return 长时间空闲的AGV代码列表
     */
    List<String> getLongIdleAgvs(java.time.Duration threshold);

    /**
     * 获取所有AGV代码
     *
     * @return 所有AGV代码列表
     */
    List<String> getAllAgvCodes();

    /**
     * 获取空闲AGV数量
     *
     * @return 空闲AGV数量
     */
    int getIdleAgvCount();

    /**
     * 获取忙碌AGV数量
     *
     * @return 忙碌AGV数量
     */
    int getBusyAgvCount();

    /**
     * 获取离线AGV数量
     *
     * @return 离线AGV数量
     */
    int getOfflineAgvCount();

    /**
     * 获取AGV总数量
     *
     * @return AGV总数量
     */
    int getTotalAgvCount();

}
