package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.entity.AgvType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Mapper
public interface AgvTypeMapper extends BaseMapper<AgvType> {

    /**
     * 更新删除状态
     * @param entity
     */
    void updateDelFlagById(AgvType entity);

    void updateByType(AgvType entity);

    AgvType selectByType(@Param("agvType") String agvType,@Param("delFlag")Boolean delFlag);
}
