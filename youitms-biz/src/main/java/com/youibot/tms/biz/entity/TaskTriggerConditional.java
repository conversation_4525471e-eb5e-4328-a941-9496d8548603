package com.youibot.tms.biz.entity;

import com.youibot.tms.biz.enums.TriggerConditionalMatchEnum;
import lombok.Data;

/**
 * 任务触发器条件
 */
@Data
public class TaskTriggerConditional {
    /**
     * 设备通讯ID
     * mysql json_search 只能对字符串类型查询 ，所以此处保存为字符串格式
     */
    private String deviceIotId;
    /**
     * 设备通讯信号ID
     */
    private String deviceIotSignalId;
    /**
     * 匹配方式
     */
    private TriggerConditionalMatchEnum matchEnum;
    /**
     * 目标值
     */
    private String targetValue;
}
