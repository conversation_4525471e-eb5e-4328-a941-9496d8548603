package com.youibot.tms.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.tms.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 流程模板节点组件的输出参数
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Getter
@Setter
@TableName("biz_flow_template_component_output_params")
@ToString
@ApiModel(value = "任务组件输出结果", description = "任务组件输出结果")
@EqualsAndHashCode(callSuper = true)
public class FlowTemplateComponentOutputParams extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 流程模板ID
     */
    @TableField("flow_template_id")
    @ApiModelProperty(value = "流程模板ID")
    private String flowTemplateId;

    /**
     * 流程模板节点ID
     */
    @TableField("flow_template_node_id")
    @ApiModelProperty(value = "流程模板节点ID")
    private String flowTemplateNodeId;

    /**
     * 流程模板节点组件编号
     */
    @TableField("component_code")
    @ApiModelProperty(value = "流程模板节点组件编号")
    private String componentCode;

    /**
     * 组件参数的组件输出参数KEY
     */
    @TableField("variable_key")
    @ApiModelProperty(value = "变量KEY")
    private String variableKey;

    /**
     * 组件输出参数默认值
     */
    @TableField("variable_value")
    @ApiModelProperty(value = "变量默认值")
    private String variableValue;

    /**
     * 组件输出参数说明
     */
    @TableField("remark")
    @ApiModelProperty(value = "变量说明")
    private String remark;

    /**
     * 输出参数类型：system：系统内置，custom：客户自定义
     */
    @TableField("type")
    @ApiModelProperty(value = "输出参数类型：system：系统内置，custom：客户自定义")
    private String type;

    /**
     * 删除标志（0代表存在 1代表删除）
     * 需要真删除，在实体类中重写此字段，并去掉TableLogic的注解
     */
    @TableField("del_flag")
    private Boolean delFlag = false;
}
