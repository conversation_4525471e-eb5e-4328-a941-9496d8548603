package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Fleet异常统计数据DTO
 * Created by Augment Agent on 2024/12/19.
 */
@Data
@ApiModel(value = "AbnormalStatisticsDTO", description = "Fleet异常统计数据")
public class AbnormalStatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机器人异常比例（饼状图）")
    private PieChart vehicleAbnormalPieChart;

    @ApiModelProperty(value = "异常分类比例（饼状图）", position = 1)
    private PieChart abnormalDetailPieChart;

    @ApiModelProperty(value = "异常平均处理时间（饼状图）", position = 2)
    private PieChart avgHandleDurationPieChart;

    @ApiModelProperty(value = "新增异常数量（折线图）", position = 3)
    private LineChart newCountLineChart;

    @ApiModelProperty(value = "异常平均处理时间（折线图）", position = 4)
    private LineChart avgHandleDurationLineChart;

    @ApiModelProperty(value = "统计开始时间")
    private Date statisticsStartTime;

    @ApiModelProperty(value = "统计结束时间")
    private Date statisticsEndTime;

    @ApiModelProperty(value = "异常详情列表")
    private List<AbnormalDetail> abnormalDetails;

    @ApiModelProperty(value = "总异常数量")
    private Integer totalAbnormalCount;

    @ApiModelProperty(value = "影响机器人数量")
    private Integer affectedVehicleCount;

    /**
     * 异常详情内部类
     */
    @Data
    @ApiModel(value = "AbnormalDetail", description = "异常详情")
    public static class AbnormalDetail implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "异常编码")
        private Integer abnormalCode;

        @ApiModelProperty(value = "异常描述")
        private String abnormalDescription;

        @ApiModelProperty(value = "异常类型")
        private String abnormalType;

        @ApiModelProperty(value = "新增数量")
        private Integer newCount;

        @ApiModelProperty(value = "关闭数量")
        private Integer closeCount;

        @ApiModelProperty(value = "平均处理时长(秒)")
        private BigDecimal avgHandleTime;

        @ApiModelProperty(value = "最大处理时长(秒)")
        private BigDecimal maxHandleTime;

        @ApiModelProperty(value = "最小处理时长(秒)")
        private BigDecimal minHandleTime;

        @ApiModelProperty(value = "总处理时长(秒)")
        private BigDecimal totalHandleTime;

        @ApiModelProperty(value = "异常级别")
        private Integer level;

        @ApiModelProperty(value = "影响机器人数量")
        private Integer affectedVehicleCount;

        @ApiModelProperty(value = "最近发生时间")
        private Date lastOccurrenceTime;

        @ApiModelProperty(value = "发生频率(次/小时)")
        private BigDecimal frequency;
    }

    /**
     * 饼状图
     */
    @Data
    @ApiModel(value = "PieChart", description = "饼状图")
    public static class PieChart implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "大饼", dataType = "List")
        private List<Pie> pies;

        @Data
        @ApiModel(value = "Pie", description = "小饼")
        public static class Pie implements Serializable {
            private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "名称")
            private String name;

            @ApiModelProperty(value = "值", position = 1)
            private Double value;

            @ApiModelProperty(value = "显示单位", position = 2)
            private String displayUnit;
        }
    }

    /**
     * 折线图
     */
    @Data
    @ApiModel(value = "LineChart", description = "折线图")
    public static class LineChart implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "x轴名称", dataType = "List")
        private List<String> xAxisNames;

        @ApiModelProperty(value = "系列", dataType = "List", position = 1)
        private List<Series> seriesList;

        @Data
        @ApiModel(value = "Series", description = "系列")
        public static class Series implements Serializable {
            private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "名称")
            private String name;

            @ApiModelProperty(value = "值", dataType = "List", position = 1)
            private List<Double> values;

            @ApiModelProperty(value = "显示单位", dataType = "List", position = 2)
            private List<String> displayUnits;
        }
    }
}
