package com.youibot.tms.biz.forest.dto.fleet5;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * agv_type(AgvType)表实体类
 *
 * <AUTHOR>
 * @since 2022-12-12 16:06:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VehicleTypeDTO {
    @ExcelIgnore
    @ApiModelProperty(value = "类型ID")
    private String id;

    @ExcelProperty(value = "编码", index = 0)
    @ApiModelProperty(value = "编码")
    private String code;

    @ExcelProperty(value = "名称", index = 1)
    @ApiModelProperty(value = "机器人类型名称")
    private String name;

    @ExcelProperty(value = "允许旋转", index = 2)
    @ApiModelProperty(value = "允许旋转")
    private Boolean canRotate;

    @ExcelIgnore
    @ApiModelProperty(value = "创建人ID", position = 3)
    private Long creator;

    @ExcelProperty(value = "创建人", index = 4)
    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ExcelProperty(value = "创建时间", index = 5)
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ExcelProperty(value = "修改时间", index = 6)
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;
}

