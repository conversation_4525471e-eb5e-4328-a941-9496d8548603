package com.youibot.tms.biz.service.impl.fleet;

import cn.hutool.core.util.RandomUtil;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.api.request.TaskStatisticsRequest;
import com.youibot.tms.biz.flow.dto.FleetAndTmsTaskDTO;
import com.youibot.tms.biz.forest.dto.*;
import com.youibot.tms.biz.forest.enums.VehicleStatus;
import com.youibot.tms.biz.forest.request.AgvMapRequest;
import com.youibot.tms.biz.forest.request.MarkerQueryRequest;
import com.youibot.tms.biz.forest.request.MissionQueryPageRequest;
import com.youibot.tms.biz.forest.request.MissionQueryRequest;
import com.youibot.tms.biz.service.FleetMissionWorkExecutingQueue;
import com.youibot.tms.biz.service.FleetProxyService;
import com.youibot.tms.common.utils.ThreadsUtils;
import com.youibot.tms.common.utils.uuid.UUID;
import com.youibot.tms.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 仿真Fleet
 * 不向fleet发送请求，而是虚构一些数据，供业务流程流转
 * 在配置文件中增加 fleet.simulation:true ，则启用该service
 * 配置为false或者不配置该项，则启用默认的 Fleet48ProxyServiceImpl
 */
@Slf4j
@Component("simulationFleetProxyService")
public class FleetSimulationProxyServiceImpl implements FleetProxyService {


    /**
     * 小车集合
     */
    private static final List<AgvDTO> vehiclePool;

    /**
     * 小车型号
     */
    private static final List<AgvTypeDTO> vehicleTypePool;

    /**
     * 导航点集合
     */
    private static final List<MarkerDTO> markerPool;

    @Resource
    private ISysConfigService sysConfigService;


    static {
        log.info("仿真Fleet-初始化数据");
        vehiclePool = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            AgvDTO vehicle = new AgvDTO();
            String id = i + "";
            vehicle.setAgvName(id);
            vehicle.setAgvCode(id);
            vehicle.setAgvId(id);
            vehicle.setId(id);
            vehicle.setAgvType("AMR");
            vehicle.setBattery(80D);
            vehicle.setStatus(VehicleStatus.IDLE);
            vehiclePool.add(vehicle);
        }

        vehicleTypePool = new ArrayList<>();
        AgvTypeDTO typeDTO = new AgvTypeDTO();
        typeDTO.setCode("AMR");
        typeDTO.setId("AMR");
        typeDTO.setName("AMR");
        vehicleTypePool.add(typeDTO);

        markerPool = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            for (int j = 1; j <= 10; j++) {
                MarkerDTO marker = new MarkerDTO();
                int id = i * 10 + j;
                marker.setId(id + "");
                marker.setCode(id + "");
                marker.setX(i + 0.0);
                marker.setY(j + 0.0);
                markerPool.add(marker);
            }

        }

//        new Thread(() -> {
//            AgvDTO agvDTO = vehiclePool.get(0);
//            while (true) {
//                if (RandomUtil.randomBoolean()) {
//                    agvDTO.setStatus(VehicleStatus.IDLE);
//                } else {
//                    agvDTO.setStatus(VehicleStatus.ERROR);
//                }
//                try {
//                    TimeUnit.SECONDS.sleep(3);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//        }).start();
    }

    @Value("${fleet.version:}")
    private String version;

    @Resource
    private FleetMissionWorkExecutingQueue fleetMissionWorkExecutingQueue;

    @Resource
    private AsyncCallbackService asyncCallbackService;

    @PostConstruct
    public void init() {
        log.info("fleet.version={},启用仿真调度", version);
    }

    @Override
    public String version() {
        return this.version;
    }

    @Override
    public String testVersion(String host) {
        return this.version;
    }

    @Override
    public List<AgvDTO> listFreeVehicles() {
        return Collections.emptyList();
    }


    @Override
    public void openAutoParkAndCharge(String agvCode) {

    }

    @Override
    public void closeAutoParkAndCharge(String agvCode) {
        log.info("关闭自动泊车和自动充电");
    }


    @Override
    public List<AgvDTO> listAllAgvs() {
        return vehiclePool;
    }


    public List<AgvDTO> listFreeAgvs() {
        log.info("查询可用机器人，返回仿真数据");
        ThreadsUtils.sleep(200);//模拟网络请求时间
        return vehiclePool;
    }

    @Override
    public FreeAgvAndNotFreeReasonRO freeAndNotFreeReason() {
        FreeAgvAndNotFreeReasonRO freeAgvAndNotFreeReasonRO = new FreeAgvAndNotFreeReasonRO();
        freeAgvAndNotFreeReasonRO.setFreeVehicles(vehiclePool);
        freeAgvAndNotFreeReasonRO.setNotFreeReasonMap(Collections.emptyMap());
        return freeAgvAndNotFreeReasonRO;
    }

    @Override
    public List<AgvDTO> listOnlineAgvs() {
        return this.listFreeAgvs();
    }


    @Override
    public String getAgvCodesSequence(String mapName, List<String> agvCodes, String markerCode) {
        return agvCodes.get(0);
    }

    @Override
    public List<MarkerDTO> getAllMarker(MarkerQueryRequest queryRequest) {
        return markerPool;
    }


    @Override
    public MissionWorkDTO createMissionWork(FleetAndTmsTaskDTO fleetAndTmsTaskDTO) {
        log.info("仿真创建任务:{}", fleetAndTmsTaskDTO.getMissionWorkParam());
        String id = UUID.fastUUID().toString();
        fleetAndTmsTaskDTO.getMissionWorkParam().setMissionWorkId(id);
        fleetMissionWorkExecutingQueue.put(fleetAndTmsTaskDTO.getFlowNodeId(), fleetAndTmsTaskDTO);
        asyncCallbackService.missionCallback(fleetAndTmsTaskDTO.getMissionWorkParam());
        return new MissionWorkDTO();
    }

    @Override
    public List<MissionDTO> getAllMission(MissionQueryRequest queryRequest) {
        return null;
    }

    @Override
    public MissionWorkDTO pollGetMissionWork(String missionWorkId, MissionWorkStatus... status) {
        return null;
    }

    @Override
    public FleetPageDataDTO<MissionDTO> getPageMission(MissionQueryPageRequest queryPageRequest) {
        return null;
    }

    @Override
    public MissionWorkDTO getMissionWork(String id) {
        return null;
    }

    @Override
    public List<AgvMapDTO> getAllAGVMap(AgvMapRequest request) {
        return null;
    }

    @Override
    public List<AgvTypeDTO> getAllAgvTypes() {
        return vehicleTypePool;
    }


    @Override
    public List<MissionWorkStatisticDTO> getMissionWorkStatistic(TaskStatisticsRequest agvDataStatisticRequest) {
        return null;
    }

    @Override
    public List<String> sortMarkerCodesByDistance(String mapName, String agvCode, List<String> markerCodes) {
        log.info("准备给导航点排序:{}", markerCodes);
        markerCodes.sort(new Comparator<String>() {
            @Override
            public int compare(String s, String t1) {
                return Integer.parseInt(s) - Integer.parseInt(t1);
            }
        });
        log.info("排序后的导航点:{}", markerCodes);
        return markerCodes;
    }

    @Override
    public SchedulerConfig getSchedulerConfig() {
        return null;
    }

    @Override
    public boolean oneKeyReset(String agvCode) {
        return true;
    }

    @Override
    public boolean oneKeyStop(String agvCode) {
        return true;
    }

    @Override
    public boolean restartTaskPrevOneKeyStopAllAgvRunningMission(String agvCode) {
        return true;
    }

    public String getCallbackUrl(String flowId, String flowNodeId) {
        String tmsUrl = sysConfigService.selectConfigValueByKey("sys.url");
        if (!tmsUrl.endsWith("/")) {
            tmsUrl = tmsUrl + "/";
        }
        String path = "api/v1/open/workflow/callback/fleet/task/callback/" + flowId + "/" + flowNodeId;
        return tmsUrl + path;
    }

    @Override
    public List<MissionWorkActionDTO> getMissionActions(String startTime, String endTime) {
        return null;
    }
}
