package com.youibot.tms.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.PortEq;
import com.youibot.tms.biz.entity.TaskTemplate;
import com.youibot.tms.biz.mapper.PortEqMapper;
import com.youibot.tms.biz.mapper.TaskTemplateMapper;
import com.youibot.tms.biz.service.PortEqService;
import com.youibot.tms.biz.service.PortService;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.ToolUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * eq料口表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-29
 */
@Service
public class PortEqServiceImpl extends ServiceImpl<PortEqMapper, PortEq> implements PortEqService {

    @Resource
    private TaskTemplateMapper taskTemplateMapper;

    @Resource
    private PortService portService;

    @Override
    public List<PortEq> selectByAreaId(long areaId) {
        LambdaQueryWrapper<PortEq> queryWrapper = Wrappers.<PortEq>lambdaQuery()
                .eq(PortEq::getZoneAreaId, areaId);
        return list(queryWrapper);
    }

    public boolean save(PortEq entity){
        portService.checkCodeUnique(entity);
        return super.save(entity);
    }


    @Override
    public int updateBaseById(PortEq entity) {
        portService.checkCodeUnique(entity);
        return this.baseMapper.updateBaseById(entity);
    }

    @Override
    public boolean removeByIds(List<Long> ids) {
        LambdaQueryWrapper<TaskTemplate> queryWrapper = Wrappers.<TaskTemplate>lambdaQuery()
                .in(TaskTemplate::getStartId, ids).or()
                .in(TaskTemplate::getDestinationId, ids).or()
                .in(TaskTemplate::getSwapId, ids);
        List<TaskTemplate> list = taskTemplateMapper.selectList(queryWrapper);
        Set<Long> startSet = list.stream().filter(a -> ToolUtil.isNotEmpty(a.getStartId())).map(TaskTemplate::getStartId).collect(Collectors.toSet());
        Set<Long> destSet = list.stream().filter(a -> ToolUtil.isNotEmpty(a.getDestinationId())).map(TaskTemplate::getDestinationId).collect(Collectors.toSet());
        Set<Long> swapSet = list.stream().filter(a -> ToolUtil.isNotEmpty(a.getSwapId())).map(TaskTemplate::getSwapId).collect(Collectors.toSet());
        destSet.addAll(swapSet);
        startSet.addAll(destSet);
        ids.removeAll(startSet);
        if (ids.size() > 0) {
            this.getBaseMapper().deleteBatchIds(ids);
        }
        if (list.size() > 0) {
            throw new BusinessException(BizErrorCode.TASK_TEMPLATE_BIND_PORT);
        }
        return true;
    }

    @Override
    public int updateSettingById(PortEq entity) {
        return this.baseMapper.updateSettingById(entity);
    }

    @Override
    public void updateEnabledById(PortEq entity) {
        this.baseMapper.updateEnabledById(entity);
    }
}
