package com.youibot.tms.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.tms.biz.enums.TaskComponentGlobalVariableType;
import com.youibot.tms.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 流程模板全局变量
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Getter
@Setter
@TableName("biz_flow_template_component_global_variable")
@ToString
@ApiModel(value = "流程模板组件参数和全局变量关联信息", description = "流程模板组件参数和全局变量关联信息")
@EqualsAndHashCode(callSuper = true)
public class FlowTemplateComponentGlobalVariable extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id")
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 流程模板ID
     */
    @TableField("flow_template_id")
    @ApiModelProperty(value = "流程模板ID")
    private String flowTemplateId;
    /**
     * 流程模板的全局变量ID
     */
    @TableField("flow_template_global_variable_id")
    @ApiModelProperty(value = "流程模板的全局变量ID")
    private String flowTemplateGlobalVariableId;
    /**
     * 组件的编号
     */
    @TableField("component_code")
    @ApiModelProperty(value = "组件的编号")
    private String componentCode;
    /**
     * 流程模板节点ID
     */
    @TableField("flow_node_template_id")
    @ApiModelProperty(value = "组件的编号")
    private String flowNodeTemplateId;

    /**
     * 组件参数的KEY
     */
    @TableField("component_params_key")
    @ApiModelProperty(value = "组件参数的KEY")
    private String componentParamsKey;
    /**
     * 组件参数的类型: fixed：固定值, variable：全局变量
     */
    @TableField("type")
    @ApiModelProperty(value = "组件参数的类型: fixed：固定值, variable：全局变量, output：组件输出参数")
    private TaskComponentGlobalVariableType type;
    /**
     * 组件参数的value,固定值时不为空，变量时为空
     */
    @TableField("component_params_value")
    @ApiModelProperty(value = "组件参数的value,固定值时不为空，变量时为空")
    private String componentParamsValue;

    /**
     * 删除标志（0代表存在 1代表删除）
     * 需要真删除，在实体类中重写此字段，并去掉TableLogic的注解
     */
    @TableField("del_flag")
    private Boolean delFlag = false;

}
