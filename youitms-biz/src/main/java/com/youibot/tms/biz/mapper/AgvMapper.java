package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.enums.AgvUsageStatus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * AGV配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
public interface AgvMapper extends BaseMapper<Agv> {

    /**
     * 根据机器人编号查询机器人
     *
     * @param agvCode 机器人编号
     * @param delFlag 是否查询已删除的
     * @return 机器人信息
     */
    Agv selectByAgvCode(@Param("agvCode") String agvCode, @Param("delFlag") Boolean delFlag);

    boolean updateUsageStatusByAgvCode(@Param("agvCode") String agvCode, @Param("usageStatus") AgvUsageStatus usageStatus, @Param("taskCodes") List<String> taskCodes, @Param("workFlowId") String workFlowId);
}
