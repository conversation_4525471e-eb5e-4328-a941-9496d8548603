package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youibot.tms.biz.entity.Task;
import com.youibot.tms.biz.entity.TaskStatistics;
import com.youibot.tms.system.dto.DataScopeParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Mapper
public interface TaskMapper extends BaseMapper<Task> {


    Page<Task> selectPage(Page<Task> page, @Param("ew") Wrapper<Task> queryWrapper, @Param("dataScope") DataScopeParam dataScopeParam);

    Task selectByTaskCode(@Param("taskCode") String taskCode);
    /**
     * 根据日期统计任务
     *
     * @param date 日期
     * @return
     */
    List<TaskStatistics> countTaskByHour(@Param("date") String date);
}
