package com.youibot.tms.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.api.request.AgvUpdateRequest;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.entity.Task;
import com.youibot.tms.biz.entity.TaskLog;
import com.youibot.tms.biz.enums.AgvUsageStatus;
import com.youibot.tms.biz.enums.PortType;
import com.youibot.tms.biz.enums.TaskStatus;
import com.youibot.tms.biz.flow.handel.WorkFlowTaskHandler;
import com.youibot.tms.biz.mapper.AgvMapper;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.common.constant.SysConfigKeyConstants;
import com.youibot.tms.common.core.domain.entity.ZoneArea;
import com.youibot.tms.common.core.domain.entity.ZoneStocker;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.common.utils.StringUtils;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.system.domain.AgvZoneArea;
import com.youibot.tms.system.domain.AgvZoneStocker;
import com.youibot.tms.system.mapper.AgvZoneAreaMapper;
import com.youibot.tms.system.mapper.AgvZoneStockerMapper;
import com.youibot.tms.system.mapper.ZoneAreaMapper;
import com.youibot.tms.system.mapper.ZoneStockerMapper;
import com.youibot.tms.system.service.ISysConfigService;
import com.youibot.tms.workflow.core.flow.WorkFlow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import com.youibot.tms.biz.utils.BayNameUtils;

import static com.youibot.tms.common.constant.SysConfigKeyConstants.SYS_FLEET_HOST;

/**
 * <p>
 * AGV配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Slf4j
@Service
public class AgvServiceImpl extends ServiceImpl<AgvMapper, Agv> implements AgvService {

    @Autowired
    private FleetProxyService fleetProxyService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private AgvZoneAreaMapper agvZoneAreaMapper;
    @Resource
    private AgvZoneStockerMapper agvZoneStockerMapper;

    @Resource
    private AgvMapper agvMapper;

    @Autowired
    private TaskService taskService;
    @Autowired
    private WorkFlowTaskHandler workFlowTaskHandler;
    @Resource
    public AgvStoragePortService agvStoragePortService;

    @Resource
    public ZoneAreaMapper zoneAreaMapper;
    @Resource
    public ZoneStockerMapper zoneStockerMapper;
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public Page<Agv> page(Page<Agv> page, QueryWrapper<Agv> query) {
        Page<Agv> list = super.page(page, query);
        ConcurrentHashMap<String, String> concurrentHashMap = new ConcurrentHashMap<>();
        ConcurrentHashMap<String, String> concurrentHashMap2 = new ConcurrentHashMap<>();
        for (Agv agv : list.getRecords()) {
            if(StringUtils.isBlank( agv.getAgvCode())){
                continue;
            }
            concurrentHashMap.put(agv.getAgvCode(), agv.getAgvCode());
            concurrentHashMap2.put(agv.getAgvCode(), agv.getAgvCode());
        }
        for (Agv agv : list.getRecords()) {
            threadPoolTaskExecutor.execute(() -> {
                List<AgvZoneArea> agvZoneAreas = agvZoneAreaMapper.selectList(Wrappers.<AgvZoneArea>lambdaQuery().eq(AgvZoneArea::getAgvCode, agv.getAgvCode()));
                Set<Long> zoneAreaIds = agvZoneAreas.stream().map(AgvZoneArea::getZoneAreaId).collect(Collectors.toSet());
                List<ZoneArea> zoneAreas = new ArrayList<>();
                if (ToolUtil.isNotEmpty(zoneAreaIds)) {
                    zoneAreas = zoneAreaMapper.selectList(Wrappers.<ZoneArea>lambdaQuery().in(ZoneArea::getId, zoneAreaIds));
                }
                agv.setZoneAreas(zoneAreas);
                agv.setZoneAreaIds(new ArrayList<>(zoneAreaIds));
                concurrentHashMap.remove(agv.getAgvCode());
            });
            threadPoolTaskExecutor.execute(() -> {
                List<AgvZoneStocker> agvZoneStockers = agvZoneStockerMapper.selectList(Wrappers.<AgvZoneStocker>lambdaQuery().eq(AgvZoneStocker::getAgvCode, agv.getAgvCode()));
                Set<Long> zoneStockerIds = agvZoneStockers.stream().map(AgvZoneStocker::getZoneStockerId).collect(Collectors.toSet());
                List<ZoneStocker> zoneStockers = new ArrayList<>();
                if (ToolUtil.isNotEmpty(zoneStockerIds)) {
                    zoneStockers = zoneStockerMapper.selectList(Wrappers.<ZoneStocker>lambdaQuery().in(ZoneStocker::getId, zoneStockerIds));
                }
                agv.setZoneStockers(zoneStockers);
                agv.setZoneStockerIds(new ArrayList<>(zoneStockerIds));
                concurrentHashMap2.remove(agv.getAgvCode());
            });
        }
        while (!concurrentHashMap.isEmpty() || !concurrentHashMap2.isEmpty()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.error("线程被中断", e);
            }
        }
        return list;
    }

    @Override
    public void syncAgv() throws BusinessException {
        log.info(">>>>同步fleet机器人操作");
        //先尝试从fleet中查询
        List<AgvDTO> data = null;
        try {
            data = fleetProxyService.listAllAgvs();
        } catch (Exception e) {
            String host = sysConfigService.selectConfigValueByKey(SYS_FLEET_HOST);
            throw new BusinessException(BizErrorCode.FLEET_REQUEST_API_FAILED, host);
        }
        //再全部删除
        this.baseMapper.delete(null);
        if (data != null && data.size() > 0) {
            data.forEach(agv -> {
                Agv old = this.getById(agv.getId());
                if (old != null) {
                    old.setAgvName(agv.getName());
                    old.setAgvId(agv.getAgvId());
                    old.setAgvCode(agv.getAgvCode());
                    old.setAgvType(agv.getAgvType());
                    old.setDelFlag(false);
                    this.updateById(old);
                } else {
                    Agv entity = new Agv();
                    entity.setId(agv.getId());
                    entity.setAgvId(agv.getAgvId());
                    entity.setAgvCode(agv.getAgvCode());
                    entity.setAgvName(agv.getName());
                    entity.setAgvType(agv.getAgvType());
                    entity.setUsageStatus(AgvUsageStatus.FREE);
                    this.save(entity);
                }
            });
        }
    }

    @Override
    public void updateAgv(AgvUpdateRequest agv) {
        agvZoneAreaMapper.delete(Wrappers.<AgvZoneArea>lambdaQuery().eq(AgvZoneArea::getAgvCode, agv.getAgvCode()));
        agvZoneStockerMapper.delete(Wrappers.<AgvZoneStocker>lambdaQuery().eq(AgvZoneStocker::getAgvCode, agv.getAgvCode()));
        List<Long> zoneAreaIds = agv.getZoneAreaIds();
        for (Long zoneAreaId : zoneAreaIds) {
            AgvZoneArea agvZoneArea = new AgvZoneArea();
            agvZoneArea.setZoneAreaId(zoneAreaId);
            agvZoneArea.setAgvCode(agv.getAgvCode());
            agvZoneAreaMapper.insert(agvZoneArea);
        }
        List<Long> zoneStockerIds = agv.getZoneStockerIds();
        for (Long zoneStockerId : zoneStockerIds) {
            AgvZoneStocker agvZoneStocker = new AgvZoneStocker();
            agvZoneStocker.setZoneStockerId(zoneStockerId);
            agvZoneStocker.setAgvCode(agv.getAgvCode());
            agvZoneStockerMapper.insert(agvZoneStocker);
        }
        Agv entity = this.baseMapper.selectById(agv.getId());
        if (entity != null) {
            entity.setUpdateTime(new Date());
            entity.setModbusIp(agv.getModbusIp());
            entity.setModbusPort(agv.getModbusPort());
            this.baseMapper.updateById(entity);
        }
    }

    @Override
    public Agv selectByAgvCode(String agvCode) {
        return agvMapper.selectByAgvCode(agvCode, false);
    }

    @Override
    public synchronized boolean lock(String agvCode, List<String> taskCodes, String workFlowId) {
        log.info("锁定AGV[{}],WorkFlowId:[{}]", agvCode, workFlowId);
        Agv agv = agvMapper.selectByAgvCode(agvCode, false);
        if (agv != null && AgvUsageStatus.FREE.equals(agv.getUsageStatus())) {
            return agvMapper.updateUsageStatusByAgvCode(agvCode, AgvUsageStatus.OCCUPIED, taskCodes, workFlowId);
        } else {
            return false;
        }
    }

    @Override
    public boolean releaseAgvAndReleaseAgvStoragePort(String agvCode) {
        log.info("释放AGV[{}]", agvCode);
        Agv agv = agvMapper.selectByAgvCode(agvCode, false);
        agv.setPortType(null);
        agv.setPortId(null);
        agv.setTaskCodes(null);
        agv.setWorkFlowId(null);
        agv.setUsageStatus(AgvUsageStatus.FREE);
        agvStoragePortService.freeAllPort(agvCode);
        return agvMapper.updateById(agv) > 0;
    }

    @Override
    public boolean releaseAgv(String agvCode) {
        log.info("释放AGV[{}]", agvCode);
        Agv agv = agvMapper.selectByAgvCode(agvCode, false);
        agv.setPortType(null);
        agv.setPortId(null);
        agv.setTaskCodes(null);
        agv.setWorkFlowId(null);
        agv.setUsageStatus(AgvUsageStatus.FREE);
        return agvMapper.updateById(agv) > 0;
    }

    @Override
    public boolean releaseAndStopTask(String agvCode) {
        Agv agv = agvMapper.selectByAgvCode(agvCode, false);
        AgvUsageStatus usageStatus = agv.getUsageStatus();
        if (ToolUtil.isNotEmpty(agv) && AgvUsageStatus.OCCUPIED.equals(usageStatus) && ToolUtil.isNotEmpty(agv.getTaskCodes())) {
            for (String taskCode : agv.getTaskCodes()) {
                Task task = taskService.selectByTaskCode(taskCode);
                if (ToolUtil.isNotEmpty(agv)) {
                    workFlowTaskHandler.stop(task.getId() + "");
                }
            }

            return releaseAgvAndReleaseAgvStoragePort(agvCode);
        }
        return false;
    }

    @Override
    public List<Agv> listFree() {
        return this.list(Wrappers.<Agv>lambdaQuery().eq(Agv::getUsageStatus, AgvUsageStatus.FREE));
    }

    @Override
    public List<Agv> listAvailableAgv() {
        List<AgvDTO> fleetIdleVehicles = fleetProxyService.listFreeAgvs();
        if (ToolUtil.isEmpty(fleetIdleVehicles)) {
            log.debug("Fleet没有空闲的机器人");
            return null;
        }
        //筛选电量
        try {
            String lowestBatteryStr = sysConfigService.getConfigDefaultValueByKey(SysConfigKeyConstants.TASK_BATTERY_LOWEST, "30");
            Double lowestBattery = Double.parseDouble(lowestBatteryStr);
            fleetIdleVehicles = fleetIdleVehicles.stream().filter(vehicleDTO -> {
                return vehicleDTO.getBattery() > lowestBattery;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("按电量筛选AGV时错误", e);
        }
        if (ToolUtil.isEmpty(fleetIdleVehicles)) {
            log.debug("没有符合电量要求的机器人");
            return null;
        }
        //查询TMS中空闲的机器人
        List<Agv> tmsIdleAgvs = this.listFree();
        if (ToolUtil.isEmpty(tmsIdleAgvs)) {
            log.debug("TMS没有空闲的机器人");
            return null;
        }
        // 筛选出TMS中空闲并且电量符合要求的AGV
        List<Agv> list = new ArrayList<>();
        for (Agv agv : tmsIdleAgvs) {
            if (vehiclesContains(fleetIdleVehicles, agv.getAgvCode())) {
                list.add(agv);
            }
        }
        return list;
    }

    @Override
    public List<String> getAvailableAgvCodes(){
        List<Agv> agvs = this.listAvailableAgv();
        if(CollectionUtils.isEmpty(agvs)){
            return Lists.newArrayList();
        }
        return agvs.stream().map(Agv::getAgvCode).collect(Collectors.toList());
    }
    /**
     * VehicleDTO中的id==TMS.Agv.agvCode
     *
     * @param vehicles
     * @param agvCode
     * @return
     */
    private boolean vehiclesContains(List<AgvDTO> vehicles, String agvCode) {
        for (AgvDTO vehicle : vehicles) {
            if (vehicle.getAgvCode().equals(agvCode)) {
                return true;
            }
        }
        return false;
    }


    @Override
    public List<Agv> listByZoneAreaId(Long zoneAreaId) {
        List<AgvZoneArea> agvZoneAreas = agvZoneAreaMapper.selectList(Wrappers.<AgvZoneArea>lambdaQuery().eq(AgvZoneArea::getZoneAreaId, zoneAreaId));
        List<String> collect = agvZoneAreas.stream().map(AgvZoneArea::getAgvCode).collect(Collectors.toList());
        return agvMapper.selectList(Wrappers.<Agv>lambdaQuery().in(Agv::getAgvCode, collect));
    }

    @Override
    public List<Agv> listByZoneStockerId(Long zoneStockerId) {
        List<AgvZoneStocker> agvZoneStockers = agvZoneStockerMapper.selectList(Wrappers.<AgvZoneStocker>lambdaQuery().eq(AgvZoneStocker::getZoneStockerId, zoneStockerId));
        List<String> collect = agvZoneStockers.stream().map(AgvZoneStocker::getAgvCode).collect(Collectors.toList());
        return agvMapper.selectList(Wrappers.<Agv>lambdaQuery().in(Agv::getAgvCode, collect));
    }

    public List<Agv> listByPort(PortType portType, Long portId, String excludeAgvCode) {
        QueryWrapper<Agv> query = new QueryWrapper<>();
        query.eq("port_type", portType);
        query.eq("port_id", portId);
        if (ToolUtil.isNotEmpty(excludeAgvCode)) {
            query.ne("agv_code", excludeAgvCode);//需要排除的agvCode
        }
        return agvMapper.selectList(query);
    }

    @Override
    public void clearAgvLocation(Agv agv, String markerCode) {
        agv.setPortId(null);
        agv.setPortType(null);
        this.updateById(agv);
        log.info("AGV[{}]离开[{}]", agv.getAgvCode(), markerCode);
    }

    @Resource
    protected TaskLogService taskLogService;

    public void shutdownTask(String agvCode) {
        Agv agv = this.selectByAgvCode(agvCode);
        if (agv != null) {
            String currentNodeId = null;
            WorkFlow parentFlow = null;
            String workFlowId = agv.getWorkFlowId();
            //一键停止AGV当前车身上的任务
            fleetProxyService.restartTaskPrevOneKeyStopAllAgvRunningMission(agvCode);
            if (StringUtils.isNotEmpty(workFlowId)) {
                log.info("结束流程[{}]", workFlowId);
                WorkFlow workFlow = workFlowTaskHandler.getWorkFlowById(workFlowId);
                if (workFlow != null) {
                    if (workFlow.getCurrentNode() == null) {
                        workFlow.setCurrentNode(workFlow.getFirstNode());
                    }
                    currentNodeId = workFlow.getCurrentNode() == null ? null : workFlow.getCurrentNode().getId();
                    parentFlow = workFlow.getParentFlow();
                    workFlowTaskHandler.stop(workFlowId);
                }
            }
            //开启自动充电和泊车
            fleetProxyService.openAutoParkAndCharge(agvCode);
            //释放机器人
            this.releaseAgvAndReleaseAgvStoragePort(agvCode);
            //改变任务状态，记录任务日志
            List<String> taskCodes = agv.getTaskCodes();
            if (ToolUtil.isNotEmpty(taskCodes)) {
                for (String taskCode : taskCodes) {
                    TaskLog taskLog = new TaskLog();
                    taskLog.setTaskCode(taskCode);
                    taskLog.setFlowId(workFlowId);
                    taskLog.setTitle("异常");
                    taskLog.setTaskStatus(TaskStatus.ERROR.name());
                    taskLog.setFlowNodeId(currentNodeId);
                    taskLog.setRemark("人工在机器人状态列表中结束任务，将任务状态设置为异常状态");
                    if (parentFlow != null) {
                        taskLog.setParentFlowId(parentFlow.getId());
                        taskLog.setFlowNodeId(parentFlow.getCurrentNode().getId());
                    }
                    taskLogService.save(taskLog);
                    taskService.update(Wrappers.<Task>lambdaUpdate().eq(Task::getCode, taskCode).set(Task::getStatus, TaskStatus.CANCEL));
                }
            }
        }
    }

    @Override
    public List<String> getAvailableAgvCodesByBayName(String bayName) {
        if (StringUtils.isBlank(bayName)) {
            log.warn("bayName为空，返回空列表");
            return new ArrayList<>();
        }

        log.info("获取bayName[{}]的可用AGV代码", bayName);

        // 获取所有可用的AGV对象
        List<AgvDTO> fleetIdleVehicles = fleetProxyService.listFreeAgvs();
        if (ToolUtil.isEmpty(fleetIdleVehicles)) {
            log.info("没有可用的AGV");
            return new ArrayList<>();
        }

        // 过滤出属于指定bayName的AGV
        List<String> bayAgvCodes = fleetIdleVehicles.stream()
                .filter(agv -> {
                    String vehicleName = agv.getName();
                    if (StringUtils.isBlank(vehicleName)) {
                        return false;
                    }
                    // 检查新格式：bayName_robotNumber
                    if (BayNameUtils.isAgvBelongsToBay(vehicleName, bayName)) {
                        return true;
                    }
                    // 兼容旧格式：纯数字AGV代码暂时包含在所有bayName中
                    return BayNameUtils.isLegacyAgvCode(vehicleName);
                })
                .map(AgvDTO::getAgvCode)
                .collect(Collectors.toList());

        log.info("bayName[{}]的可用AGV数量: {}/{}", bayName, bayAgvCodes.size(), fleetIdleVehicles.size());
        return bayAgvCodes;
    }

    @Override
    public Map<String, List<AgvDTO>> groupAgvsByBayName(List<AgvDTO> agvs) {
        if (CollectionUtils.isEmpty(agvs)) {
            log.warn("AGV列表为空，返回空映射");
            return new HashMap<>();
        }

        log.info("开始按bayName分组{}个AGV", agvs.size());

        Map<String, List<AgvDTO>> groupedAgvs = new HashMap<>();

        for (AgvDTO agv : agvs) {
            String agvCode = agv.getAgvCode();
            String vehicleName = agv.getName();
            if (StringUtils.isBlank(vehicleName)) {
                log.warn("AGV名称为空，跳过: {}", agv);
                continue;
            }

            String bayName = BayNameUtils.extractBayNameFromAgvName(vehicleName);

            if (bayName != null) {
                // 新格式：按提取的bayName分组
                groupedAgvs.computeIfAbsent(bayName, k -> new ArrayList<>()).add(agv);
                log.debug("AGV[{}]分配到bayName[{}]", agvCode, bayName);
            } else if (BayNameUtils.isLegacyAgvCode(vehicleName)) {
                // 旧格式：分配到特殊的"LEGACY"组，或者分配到所有活跃的bayName
                // 这里选择分配到"LEGACY"组，便于统一管理
                groupedAgvs.computeIfAbsent("LEGACY", k -> new ArrayList<>()).add(agv);
                log.debug("AGV[{}]使用旧格式，分配到LEGACY组", agvCode);
            } else {
                log.warn("AGV名称格式无效，跳过: {}", vehicleName);
            }
        }

        log.info("AGV分组完成，共{}个组: {}", groupedAgvs.size(),
                groupedAgvs.entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().size())));

        return groupedAgvs;
    }

    // ==========================================
    // 新增的派车优化相关方法实现
    // ==========================================

    @Override
    public boolean isAgvAvailable(String agvCode) {
        if (StringUtils.isBlank(agvCode)) {
            return false;
        }

        try {
            Agv agv = getOne(Wrappers.lambdaQuery(Agv.class).eq(Agv::getAgvCode, agvCode));
            if (agv == null) {
                log.debug("AGV不存在: {}", agvCode);
                return false;
            }

            // 检查AGV状态
            return agv.getUsageStatus() == AgvUsageStatus.FREE;

        } catch (Exception e) {
            log.error("检查AGV{}可用性时发生异常", agvCode, e);
            return false;
        }
    }

    @Override
    public List<AgvDTO> getFilteredFreeAgvs() {
        try {
            // 获取Fleet中的空闲AGV
            List<AgvDTO> fleetFreeAgvs = fleetProxyService.listFreeAgvs();
            if (CollectionUtils.isEmpty(fleetFreeAgvs)) {
                log.debug("Fleet中没有空闲的AGV");
                return new ArrayList<>();
            }

            // 获取TMS中空闲的AGV代码
            List<String> tmsFreeAgvCodes = getIdleAgvCodes();
            if (CollectionUtils.isEmpty(tmsFreeAgvCodes)) {
                log.debug("TMS中没有空闲的AGV");
                return new ArrayList<>();
            }

            // 过滤出既在Fleet中空闲又在TMS中空闲的AGV
            return fleetFreeAgvs.stream()
                .filter(agvDTO -> tmsFreeAgvCodes.contains(agvDTO.getAgvCode()))
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取过滤后的空闲AGV时发生异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getIdleAgvCodes() {
        List<Agv> idleAgvs = list(
            Wrappers.lambdaQuery(Agv.class)
                .eq(Agv::getUsageStatus, AgvUsageStatus.FREE)
        );

        return idleAgvs.stream()
            .map(Agv::getAgvCode)
            .collect(Collectors.toList());
    }

    @Override
    public List<String> getLongIdleAgvs(java.time.Duration threshold) {
        try {
            // 获取所有空闲的AGV
            List<String> idleAgvCodes = getIdleAgvCodes();
            if (CollectionUtils.isEmpty(idleAgvCodes)) {
                return new ArrayList<>();
            }

            // 计算阈值时间点
            java.time.LocalDateTime thresholdTime = java.time.LocalDateTime.now().minus(threshold);
            java.util.Date thresholdDate = java.sql.Timestamp.valueOf(thresholdTime);

            // 查询长时间空闲的AGV（updateTime早于阈值时间且状态为FREE）
            List<Agv> longIdleAgvs = list(
                Wrappers.lambdaQuery(Agv.class)
                    .eq(Agv::getUsageStatus, AgvUsageStatus.FREE)
                    .le(Agv::getUpdateTime, thresholdDate)
                    .in(Agv::getAgvCode, idleAgvCodes)
            );

            return longIdleAgvs.stream()
                .map(Agv::getAgvCode)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取长时间空闲AGV时发生异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getAllAgvCodes() {
        List<Agv> allAgvs = list();
        return allAgvs.stream()
            .map(Agv::getAgvCode)
            .collect(Collectors.toList());
    }

    @Override
    public int getIdleAgvCount() {
        return count(
            Wrappers.lambdaQuery(Agv.class)
                .eq(Agv::getUsageStatus, AgvUsageStatus.FREE)
        );
    }

    @Override
    public int getBusyAgvCount() {
        return count(
            Wrappers.lambdaQuery(Agv.class)
                .eq(Agv::getUsageStatus, AgvUsageStatus.OCCUPIED)
        );
    }

    @Override
    public int getOfflineAgvCount() {
        // 由于AgvUsageStatus只有FREE和OCCUPIED，离线AGV需要通过其他方式判断
        // 这里暂时返回0，实际应该通过Fleet状态或其他字段判断
        // TODO: 实现离线AGV统计逻辑，可能需要查询Fleet状态
        return 0;
    }

    @Override
    public int getTotalAgvCount() {
        return count();
    }

}
