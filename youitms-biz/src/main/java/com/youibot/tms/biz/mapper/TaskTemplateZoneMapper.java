package com.youibot.tms.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youibot.tms.biz.entity.TaskTemplateZone;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;


@Mapper
public interface TaskTemplateZoneMapper extends BaseMapper<TaskTemplateZone> {


    @Delete("delete from biz_task_template_zone where task_template_id = #{id}")
    void removeByTaskTemplateId(Long id);
}
