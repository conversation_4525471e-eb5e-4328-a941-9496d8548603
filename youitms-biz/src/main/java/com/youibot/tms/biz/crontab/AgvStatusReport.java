package com.youibot.tms.biz.crontab;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.api.dto.AgvDTO;
import com.youibot.tms.biz.api.dto.MCSAgvInfoDTO;
import com.youibot.tms.biz.common.exception.BizConstants;
import com.youibot.tms.biz.entity.*;
import com.youibot.tms.biz.enums.*;
import com.youibot.tms.biz.forest.dto.FreeAgvAndNotFreeReasonRO;
import com.youibot.tms.biz.forest.enums.VehicleStatus;
import com.youibot.tms.biz.secs.DeviceControlStateManager;
import com.youibot.tms.biz.secs.Util;
import com.youibot.tms.biz.secs.handler.S66F3CommandHandler;
import com.youibot.tms.biz.utils.AgvNameUtils;
import com.youibot.tms.common.core.domain.entity.ZoneArea;
import com.youibot.tms.common.core.domain.entity.ZoneStocker;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.biz.utils.BayNameUtils;
import com.youibot.tms.common.utils.Pair;
import com.youibot.tms.common.utils.StringUtils;
import com.youibot.tms.system.service.ZoneAreaService;
import com.youibot.tms.system.service.ZoneStockerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.EnumSet;

import com.youibot.tms.biz.config.BayNameDispatchConfig;
import com.youibot.tms.biz.cache.AgvDTOCache;
import com.youibot.tms.biz.entity.AgvStoragePort;
import com.youibot.tms.biz.service.AgvStoragePortService;
import com.youibot.tms.biz.flow.common.enums.MergeMode;
import com.youibot.tms.system.service.ISysConfigService;
import com.youibot.tms.biz.entity.TaskNode;
import com.youibot.tms.biz.service.TaskNodeService;
import com.youibot.tms.biz.entity.Task;
import com.youibot.tms.biz.service.TaskService;
import com.youibot.tms.biz.enums.TaskNodeAction;
import com.youibot.tms.biz.flow.dto.InsertTaskParams;
import com.youibot.tms.biz.utils.BayNameUtils;
import com.youibot.tms.biz.utils.AgvNameUtils;
import com.youibot.tms.biz.secs.Util;
import com.youibot.tms.biz.api.dto.AgvDTO;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Comparator;
import java.util.Map;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import static com.youibot.tms.common.constant.SysConfigKeyConstants.AGV_ORDER_MERGE_RULE;

/**
 * AGV状态上报
 */
@Slf4j
@Component
public class AgvStatusReport {

    @Resource
    private FleetProxyService fleetProxyService;

    @Resource
    private AgvService agvService;

    @Resource
    private FromToRecordService fromToRecordService;
    @Resource
    private PortService portService;

    @Resource
    private InsertTaskService insertTaskService;

    @Resource
    private BayNameDispatchConfig dispatchConfig;

    @Resource
    private AgvAllocationService agvAllocationService;

    @Resource
    private TaskResourceLockManager taskResourceLockManager;

    @Resource
    private CommunicationDeviceService communicationDeviceService;

    @Resource
    private AgvStoragePortService agvStoragePortService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private TaskNodeService taskNodeService;

    @Resource
    private DeviceControlStateManager deviceControlStateManager;

    @Lazy
    @Resource
    private ZoneAreaService zoneAreaService;

    @Lazy
    @Resource
    private ZoneStockerService zoneStockerService;

    @Resource
    private TaskService  taskService;

    public static final Map<String, String> agvInfoMap = new ConcurrentHashMap<>();

    // 防止并发执行的锁
    private final ReentrantLock triggerLock = new ReentrantLock();

    // bayName并发处理线程池（延迟初始化）
    private ExecutorService bayNameExecutor;

    // 每个bayName的独立锁
    private final Map<String, ReentrantLock> bayLocks = new ConcurrentHashMap<>();

//    @Scheduled(fixedRate = 10000)//每10秒执行一次
    public void reportAgvInfo() {
        log.info("上报AGV状态");
        List<AgvDTO> vehicleDataList;
        try {
            vehicleDataList = fleetProxyService.listOnlineAgvs();
        }catch (Exception e){
            log.error("查询机器人列表报错:{}", e.getMessage());
            return;
        }
        if(CollectionUtils.isEmpty(vehicleDataList)){
            log.debug("没有agv信息需要上报");
            return;
        }
        for (AgvDTO vehicleData : vehicleDataList) {
            MCSAgvInfoDTO mcsAgvInfoDTO = this.getAgvInfoByAgvCode(vehicleData);
            if(mcsAgvInfoDTO!=null) {
                String agvCode = vehicleData.getId();
                //校验是否与上一次提交的信息相等
                String preInfo = agvInfoMap.get(agvCode);
                String info = JSON.toJSONString(mcsAgvInfoDTO);
                if (info.equals(preInfo)) {
                    continue;
                }
                // 发送到上层业务系统
                agvInfoMap.put(agvCode, info);
            }
        }

    }



    public MCSAgvInfoDTO getAgvInfoByAgvCode(AgvDTO vehicleData) {
        MCSAgvInfoDTO mcsAgvInfoDTO = new MCSAgvInfoDTO();
        String agvCode = vehicleData.getId();
        Agv agv = agvService.selectByAgvCode(agvCode);
        if(agv != null) {

            mcsAgvInfoDTO.setAgvCode(agvCode);

            mcsAgvInfoDTO.setOnlineStatus( VehicleStatus.OFFLINE.equals(vehicleData.getStatus())?0:1);
            mcsAgvInfoDTO.setWorkStatus(vehicleData.getStatus());
            return mcsAgvInfoDTO;
        }else{
            return null;
        }

    }


    /**
     * 任务触发 - 基于统一AGV分配策略的优化派车机制
     */
    @SuppressWarnings("ReassignedVariable")
    public void triggerAgvInfoReport() {
        // 尝试获取锁，如果获取不到则直接返回，避免并发执行
        if (!triggerLock.tryLock()) {
            log.warn("triggerAgvInfoReport正在执行中，跳过本次执行");
            return;
        }

        try {
            log.info("开始执行优化的派车流程");


            // 1. 清理阶段：先清理无效的 pending 任务，确保能捞取到有效任务
//            cleanupExpiredPendingMerge();

            // 1.1. 检查并修复 pending merge 数据一致性问题
//            checkAndFixPendingMergeConsistency();

            // 1.2. 清理已完成任务的 pending 标识
//            cleanupCompletedTasksPendingFlags();

            // 2. 任务预筛选阶段：获取有效任务
            List<FromToRecord> validTasks = fromToRecordService.getValidPendingTasks();
            if (CollectionUtils.isEmpty(validTasks)) {
                log.info("没有有效的待处理任务，跳过派车");
                return;
            }

            log.info("找到{}个有效待处理任务", validTasks.size());

            // 1.6. 合单候选检查阶段：识别可能被合单的任务，延迟分配
            if (dispatchConfig.isEnablePendingMergeDelay()) {
                List<PendingMergeCandidate> pendingMergeCandidates = identifyPendingMergeCandidates(validTasks);

                if (!pendingMergeCandidates.isEmpty()) {
                    markTasksAsPendingMerge(pendingMergeCandidates);
                    // 从有效任务中移除被标记的任务
                    List<FromToRecord> markedTasks = pendingMergeCandidates.stream()
                        .map(PendingMergeCandidate::getTask)
                        .collect(Collectors.toList());
                    validTasks.removeAll(markedTasks);

                    log.info("标记{}个任务为合单候选延迟分配，剩余{}个任务立即分配",
                            pendingMergeCandidates.size(), validTasks.size());
                }

                // 如果所有任务都被标记为延迟分配，直接返回
                if (CollectionUtils.isEmpty(validTasks)) {
                    log.info("所有任务都被标记为合单候选，等待InsertTaskComponent处理");
                    return;
                }
            }

            // 2. 按commandId分组任务（保证原子性）
            Map<String, List<FromToRecord>> commandGroups = validTasks.stream()
                .collect(Collectors.groupingBy(FromToRecord::getCommandId));

            if (commandGroups.isEmpty()) {
                log.warn("按commandId分组后为空，跳过处理");
                return;
            }

            // 对每个commandId组内的记录按formToSeq排序（处理null值和重复值）
            commandGroups.forEach((commandId, records) -> {
                records.sort(Comparator.comparing(
                    FromToRecord::getFormToSeq,
                    Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(FromToRecord::getId)); // 使用ID作为次要排序条件确保稳定排序

                // 记录排序后的顺序用于调试
                if (log.isDebugEnabled()) {
                    String sortedSeqs = records.stream()
                        .map(r -> String.valueOf(r.getFormToSeq()))
                        .collect(Collectors.joining(","));
                    log.debug("commandId[{}]任务排序完成，formToSeq顺序: [{}]", commandId, sortedSeqs);
                }
            });

            log.info("任务按commandId分组完成，共{}个命令组", commandGroups.size());

            // 3. 分离Local和Host任务
            Map<String, List<FromToRecord>> localCommandGroups = new HashMap<>();
            Map<String, List<FromToRecord>> hostCommandGroups = new HashMap<>();

            for (Map.Entry<String, List<FromToRecord>> entry : commandGroups.entrySet()) {
                String commandId = entry.getKey();
                List<FromToRecord> tasks = entry.getValue();
                FromToRecord firstTask = tasks.get(0);

                if (isLocalTask(firstTask)) {
                    localCommandGroups.put(commandId, tasks);
                } else {
                    hostCommandGroups.put(commandId, tasks);
                }
            }

            log.info("任务分离完成：Local任务{}个命令组，Host任务{}个命令组",
                    localCommandGroups.size(), hostCommandGroups.size());

            // 4. 按bayName分组Host任务（支持并发处理）
            Map<String, List<String>> bayNameCommandGroups = groupCommandsByBayName(hostCommandGroups);

            log.info("Host任务按bayName分组完成，共{}个bayName组", bayNameCommandGroups.size());

            // 3. AGV状态查询阶段：只有在有任务时才查询AGV
            FreeAgvAndNotFreeReasonRO freeAgvAndNotFreeReasonRO = fleetProxyService.freeAndNotFreeReason();
            if (Objects.isNull(freeAgvAndNotFreeReasonRO)) {
                log.error("【派车失败】Fleet系统返回空数据，无法获取AGV状态信息");
                return;
            }

            List<AgvDTO> freeAgvs = freeAgvAndNotFreeReasonRO.getFreeVehicles();
//            Map<String, String> notFreeReasonMap = freeAgvAndNotFreeReasonRO.getNotFreeReasonMap();

            if (CollectionUtils.isEmpty(freeAgvs)) {
                log.warn("【派车失败】Fleet系统中没有空闲AGV，所有AGV都处于忙碌状态");
                return;
            }

            // 过滤TMS中可用的AGV
            List<String> tmsFree = agvService.getAvailableAgvCodes();
            if (CollectionUtils.isEmpty(tmsFree)) {
                log.warn("【派车失败】TMS中没有可用AGV，可能所有AGV都被禁用或处于维护状态");
                return;
            }

            List<AgvDTO> fleetNotInTms = freeAgvs.stream()
                    .filter(p -> !tmsFree.contains(p.getAgvCode()))
                    .collect(Collectors.toList());

            freeAgvs = freeAgvs.parallelStream()
                    .filter(p -> tmsFree.contains(p.getAgvCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(freeAgvs)) {
                log.warn("【派车失败】Fleet空闲AGV与TMS可用AGV无交集，无法分配任务");
                return;
            }

            // 过滤掉已经被其他工作流占用的AGV
            Set<String> occupiedAgvs = insertTaskService.getOccupiedAgvs();
            List<AgvDTO> workflowOccupiedAgvs = freeAgvs.stream()
                    .filter(agv -> occupiedAgvs.contains(agv.getAgvCode()))
                    .collect(Collectors.toList());

            freeAgvs = freeAgvs.stream()
                    .filter(agv -> !occupiedAgvs.contains(agv.getAgvCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(freeAgvs)) {
                log.warn("【派车失败】所有可用AGV都已被其他工作流占用，无法分配新任务");
                return;
            }

            // 输出统一的派车状态报告
            logDispatchStatusReport(bayNameCommandGroups, freeAgvAndNotFreeReasonRO, tmsFree,
                    fleetNotInTms, workflowOccupiedAgvs, freeAgvs);

            // 4. AGV按bayName分组阶段：确保AGV不跨bayName分配
            Map<String, List<AgvDTO>> agvsByBayName;
            if (dispatchConfig.isEnableBayNameGrouping() && !dispatchConfig.isAllowCrossBayTransport()) {
                // 启用bayName分组且不允许跨bayName运输时，按bayName分组AGV
                agvsByBayName = agvService.groupAgvsByBayName(freeAgvs);
                log.info("AGV按bayName分组完成，共{}个bayName组: {}",
                        agvsByBayName.size(),
                        agvsByBayName.entrySet().stream()
                                .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        entry -> entry.getValue().size())));
            } else {
                // 兼容模式：所有AGV可用于所有bayName
                agvsByBayName = new HashMap<>();
                for (String bayName : bayNameCommandGroups.keySet()) {
                    agvsByBayName.put(bayName, new ArrayList<>(freeAgvs));
                }
                log.info("使用兼容模式，所有{}个AGV可用于所有bayName", freeAgvs.size());
            }

            // 5. 并发处理阶段：按bayName并发处理Host任务，每个bayName只使用分配给它的AGV
            if (!bayNameCommandGroups.isEmpty()) {
                boolean hasAnyDeviceOnline = deviceControlStateManager.hasAnyDeviceOnline();
                if(!hasAnyDeviceOnline){
                    log.info("没有任何设备在线<<=====>>，跳过派车");
                    return;
                }
                processByBayNameGroups(bayNameCommandGroups, hostCommandGroups, agvsByBayName);
            }

            // 6. 处理Local任务（不受bayName限制，可以使用任何空闲AGV）
            if (!localCommandGroups.isEmpty()) {
                processLocalTasks(localCommandGroups, freeAgvs);
            }

        } catch (Exception e) {
            log.error("triggerAgvInfoReport执行时发生异常", e);
        } finally {
            triggerLock.unlock();
        }
    }

    /**
     * 输出统一的派车状态报告，减少日志零散输出
     */
    private void logDispatchStatusReport(Map<String, List<String>> bayNameCommandGroups,
                                       FreeAgvAndNotFreeReasonRO freeAgvAndNotFreeReasonRO,
                                       List<String> tmsFree,
                                       List<AgvDTO> fleetNotInTms,
                                       List<AgvDTO> workflowOccupiedAgvs,
                                       List<AgvDTO> finalAvailableAgvs) {

        StringBuilder report = new StringBuilder();
        report.append("\n").append(StringUtils.repeat("=", 80)).append("\n");
        report.append("【派车状态报告】").append("\n");
        report.append(StringUtils.repeat("=", 80)).append("\n");

        // 1. 任务概况
        report.append("📋 任务概况:\n");
        report.append(String.format("   待处理bayName: %d个 %s\n",
                bayNameCommandGroups.size(), bayNameCommandGroups.keySet()));
        int totalCommands = bayNameCommandGroups.values().stream().mapToInt(List::size).sum();
        report.append(String.format("   待处理commandId: %d个\n", totalCommands));

        // 2. Fleet系统状态
        List<AgvDTO> freeAgvs = freeAgvAndNotFreeReasonRO.getFreeVehicles();
        Map<String, String> notFreeReasonMap = freeAgvAndNotFreeReasonRO.getNotFreeReasonMap();
        report.append("\n🚗 Fleet系统状态:\n");
        report.append(String.format("   空闲AGV: %d个 %s\n",
                freeAgvs != null ? freeAgvs.size() : 0,
                freeAgvs != null ? freeAgvs.stream().map(AgvDTO::getAgvCode).collect(Collectors.toList()) : "[]"));
        report.append(String.format("   非空闲AGV: %d个",
                notFreeReasonMap != null ? notFreeReasonMap.size() : 0));
        if (notFreeReasonMap != null && !notFreeReasonMap.isEmpty()) {
            report.append(" (原因: ").append(notFreeReasonMap).append(")");
        }
        report.append("\n");

        // 3. TMS过滤结果
        report.append("\n🔧 TMS过滤结果:\n");
        report.append(String.format("   TMS可用AGV: %d个 %s\n", tmsFree.size(), tmsFree));
        if (!fleetNotInTms.isEmpty()) {
            report.append(String.format("   Fleet空闲但TMS不可用: %d个 %s\n",
                    fleetNotInTms.size(),
                    fleetNotInTms.stream().map(AgvDTO::getAgvCode).collect(Collectors.toList())));
        }

        // 4. 工作流占用情况
        report.append("\n⚙️ 工作流占用情况:\n");
        if (!workflowOccupiedAgvs.isEmpty()) {
            report.append(String.format("   被占用AGV: %d个 %s\n",
                    workflowOccupiedAgvs.size(),
                    workflowOccupiedAgvs.stream().map(AgvDTO::getAgvCode).collect(Collectors.toList())));
        } else {
            report.append("   无AGV被工作流占用\n");
        }

        // 5. 最终结果
        report.append("\n✅ 最终可派车AGV:\n");
        report.append(String.format("   数量: %d个\n", finalAvailableAgvs.size()));
        report.append(String.format("   列表: %s\n",
                finalAvailableAgvs.stream().map(AgvDTO::getAgvCode).collect(Collectors.toList())));

        report.append(StringUtils.repeat("=", 80));

        log.info(report.toString());
    }

    /**
     * 按bayName分组commandId
     * 注意：只处理Host任务，Local任务需要单独处理
     */
    private Map<String, List<String>> groupCommandsByBayName(Map<String, List<FromToRecord>> commandGroups) {
        Map<String, List<String>> bayNameGroups = new HashMap<>();

        for (Map.Entry<String, List<FromToRecord>> entry : commandGroups.entrySet()) {
            String commandId = entry.getKey();
            List<FromToRecord> tasks = entry.getValue();

            // 使用第一个任务来确定来源和bayName
            FromToRecord firstTask = tasks.get(0);

            // 跳过Local任务，它们需要单独处理
            if (isLocalTask(firstTask)) {
                log.debug("跳过Local任务 commandId[{}]，将在单独的流程中处理", commandId);
                continue;
            }

            // 只处理Host任务：按bayName分组
            String bayName = getTaskBayName(firstTask);
            if (StringUtils.isNotBlank(bayName)) {
                bayNameGroups.computeIfAbsent(bayName, k -> new ArrayList<>()).add(commandId);
                log.debug("Host任务 commandId[{}] 分组到 bayName: {}", commandId, bayName);
            } else {
                // 无法确定bayName的Host任务，单独分组
                String unknownGroupKey = "UNKNOWN_" + commandId;
                bayNameGroups.computeIfAbsent(unknownGroupKey, k -> new ArrayList<>()).add(commandId);
                log.debug("无法确定bayName的Host任务 commandId[{}] 单独分组为: {}", commandId, unknownGroupKey);
            }
        }

        log.debug("Host任务按bayName分组结果: {}", bayNameGroups);
        return bayNameGroups;
    }

    /**
     * 判断任务是否为Local任务
     * Local任务的特征：source为LOCAL或者没有deviceIp
     */
    private boolean isLocalTask(FromToRecord task) {
        try {
            // 方法1：通过source字段判断
            if (FromToRecordSourceEnum.LOCAL.equals(task.getSource())) {
                return true;
            }

            // 方法2：通过deviceIp判断（Local任务通常没有deviceIp）
            if (StringUtils.isBlank(task.getDeviceIp()) &&
                FromToRecordSourceEnum.HOST.equals(task.getSource())) {
                // Host任务但没有deviceIp，可能是异常情况，保守处理为Local
                log.debug("Host任务[{}]没有deviceIp，保守处理为Local任务", task.getCommandId());
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("判断任务[{}]是否为Local时发生异常", task.getCommandId(), e);
            return true; // 异常时保守处理为Local
        }
    }



    /**
     * 通过Port代码获取bayName
     */
    private String getBayNameFromToCode(String portCode) {
        try {
            if (StringUtils.isBlank(portCode)) {
                return null;
            }

            // 通过portCode获取Port对象
            Port port = portService.getByCode(portCode);
            if (port == null) {
                log.debug("找不到Port[{}]", portCode);
                return null;
            }

            // 根据Port类型获取Zone ID
            Long zoneId = portService.getZoneId(port.getPortType(), port.getId());
            if (zoneId == null) {
                log.debug("Port[{}]没有关联的Zone", portCode);
                return null;
            }

            // 根据Port类型查询对应的Zone并获取code
            return getZoneCodeById(port.getPortType(), zoneId);

        } catch (Exception e) {
            log.warn("通过Port代码[{}]获取bayName时发生异常: {}", portCode, e.getMessage());
            return null;
        }
    }

    /**
     * 根据Zone ID和类型获取Zone的code
     */
    private String getZoneCodeById(PortType portType, Long zoneId) {
        try {
            if (PortType.PORT_EQ.equals(portType)) {
                // 查询ZoneArea
                ZoneArea zoneArea = zoneAreaService.getById(zoneId);
                if (zoneArea != null) {
                    return zoneArea.getCode();
                }
            } else if (PortType.PORT_STOCKER.equals(portType)) {
                // 查询ZoneStocker
                ZoneStocker zoneStocker = zoneStockerService.getById(zoneId);
                if (zoneStocker != null) {
                    return zoneStocker.getCode();
                }
            }

            log.debug("找不到Zone[{}]，类型: {}", zoneId, portType);
            return null;
        } catch (Exception e) {
            log.warn("查询Zone[{}]的code时发生异常，类型: {}", zoneId, portType, e);
            return null;
        }
    }

    /**
     * 按bayName并发处理任务组（每个bayName只使用自己的AGV）
     */
    private void processByBayNameGroups(Map<String, List<String>> bayNameCommandGroups,
                                       Map<String, List<FromToRecord>> commandGroups,
                                       Map<String, List<AgvDTO>> agvsByBayName) {
        log.info("开始并发处理{}个bayName组", bayNameCommandGroups.size());

        // 并发处理每个bayName组，每个bayName只使用属于自己的AGV
        List<CompletableFuture<Void>> futures = bayNameCommandGroups.entrySet()
                .stream()
                .map(entry -> {
                    String bayName = entry.getKey();
                    List<String> commandIds = entry.getValue();
                    List<AgvDTO> bayAgvs = agvsByBayName.getOrDefault(bayName, new ArrayList<>());

                    // 缓存这个bayName下的所有AGV信息（用于后续S66F45消息构建）
                    if (CollectionUtils.isNotEmpty(bayAgvs)) {
                        log.info("【AgvStatusReport】准备缓存bayName[{}]的{}个AGV信息", bayName, bayAgvs.size());
                        for (AgvDTO agv : bayAgvs) {
                            log.debug("【AgvStatusReport】缓存AGV数据: code={}, name={}, connectStatus={}, status={}",
                                     agv.getAgvCode(), agv.getName(), agv.getConnectStatus(), agv.getStatus());
                        }
                        AgvDTOCache.updateCache(bayAgvs);
                        log.info("【AgvStatusReport】已缓存bayName[{}]的{}个AGV信息", bayName, bayAgvs.size());
                    } else {
                        log.warn("【AgvStatusReport】bayName[{}]没有AGV数据需要缓存", bayName);
                    }

                    log.debug("bayName[{}]分配到{}个AGV，待处理{}个commandId",
                            bayName, bayAgvs.size(), commandIds.size());

                    return CompletableFuture.runAsync(() ->
                            processBayNameTasks(bayName, commandIds, commandGroups, bayAgvs));
                })
                .collect(Collectors.toList());

        try {
            // 等待所有bayName组处理完成，设置60秒超时
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(60, TimeUnit.SECONDS);
            log.info("所有bayName组处理完成");
        } catch (Exception e) {
            log.error("等待bayName组完成时发生异常", e);
            // 取消未完成的任务
            futures.forEach(future -> future.cancel(true));
        }
    }

    /**
     * 处理单个bayName的任务（支持优先级排序，只使用属于该bayName的AGV）
     */
    private void processBayNameTasks(String bayName, List<String> commandIds,
                                   Map<String, List<FromToRecord>> commandGroups,
                                   List<AgvDTO> bayAgvs) {
        log.info("开始处理bayName[{}]的{}个commandId，可用AGV: {}",
                bayName, commandIds.size(),
                bayAgvs.stream().map(AgvDTO::getAgvCode).collect(Collectors.toList()));

        // 检查设备是否在线，如果不在线则跳过派车处理
        if (!deviceControlStateManager.isDeviceOnlineByBayName(bayName)) {
            log.warn("【派车失败】bayName[{}]设备离线，跳过{}个commandId的派车处理",
                    bayName, commandIds.size());
            return;
        }

        try {
            // 按优先级和创建时间排序commandId
            List<String> sortedCommandIds = sortCommandIdsByPriority(commandIds, commandGroups);

            log.info("bayName[{}]任务排序完成，处理顺序: {}", bayName, sortedCommandIds);

            // 按排序后的顺序处理每个commandId
            for (String commandId : sortedCommandIds) {
                List<FromToRecord> commandTasks = commandGroups.get(commandId);
                if (commandTasks != null && !commandTasks.isEmpty()) {

                    // 检查该 commandId 是否应该被 pending 住（延迟分配）
                    if (shouldPendCommandId(commandId, commandTasks)) {
                        log.info("【延迟分配】commandId[{}] 符合 pending 条件，暂不分配AGV", commandId);
                        continue; // 跳过当前 commandId，不进行 AGV 分配
                    }

                    processCommandGroup(commandId, commandTasks, bayAgvs);
                }
            }

        } catch (Exception e) {
            log.error("处理bayName[{}]时发生异常", bayName, e);
        }
    }

    /**
     * 检查 commandId 是否应该被 pending 住（延迟分配）
     * 实时检测该 commandId 是否能够合并进入当前正在运行的任务
     */
    private boolean shouldPendCommandId(String commandId, List<FromToRecord> commandTasks) {
        try {
            // 1. 首先检查是否已经被之前标记为 pending
            boolean hasPendingMergeTask = commandTasks.stream()
                .anyMatch(task -> Boolean.TRUE.equals(task.getIsPendingMerge()));

            if (hasPendingMergeTask) {
                log.debug("commandId[{}] 已被标记为 pending merge，应该延迟分配", commandId);
                return true;
            }

            // 2. 🔥 优先检查：是否有任务已指定目标AGV
            List<FromToRecord> tasksWithTargetAgv = commandTasks.stream()
                .filter(task -> StringUtils.isNotBlank(task.getPendingMergeTargetAgv()))
                .collect(Collectors.toList());

            if (!tasksWithTargetAgv.isEmpty()) {
                // 检查指定的目标AGV是否仍然可用
                boolean canContinueWithTargetAgv = checkSpecifiedTargetAgv(commandId, tasksWithTargetAgv);
                if (canContinueWithTargetAgv) {
                    return true;
                }
                // 如果指定的目标AGV不可用，继续检查其他AGV
                log.info("【目标AGV检查】commandId[{}] 指定的目标AGV不可用，尝试寻找其他AGV", commandId);
            }

            // 3. 实时检测：该 commandId 是否能够合并进入当前正在运行的任务
            boolean canMergeWithRunningTasks = canMergeWithCurrentlyRunningTasks(commandId, commandTasks);

            if (canMergeWithRunningTasks) {
                // 获取详细的分配信息用于日志
                AgvNearingCompletion targetAgv = findBestAgvForMerging(commandId, commandTasks,
                    getAgvsNearingCommandCompletion(), commandTasks.size());
                if (targetAgv != null) {
                    log.info("【实时合单检测】commandId[{}] 可以合并进入AGV[{}]，延迟分配等待合单 - AGV状态：总容量={}, 已占用={}, 已pending={}, 可用={}, 需要={}",
                             commandId, targetAgv.getAgvCode(), targetAgv.getTotalCapacity(),
                             targetAgv.getOccupiedStorageCount(), targetAgv.getPendingTaskCount(),
                             targetAgv.getAvailableCapacity(), commandTasks.size());
                } else {
                    log.info("【实时合单检测】commandId[{}] 可以合并进入正在运行的任务，延迟分配等待合单", commandId);
                }
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("检查 commandId[{}] 是否应该 pending 时发生异常", commandId, e);
            return false; // 异常时不 pending，正常分配
        }
    }

    /**
     * 检查指定的目标AGV是否仍然可用于合单
     * 优先保持已有的目标AGV分配
     */
    private boolean checkSpecifiedTargetAgv(String commandId, List<FromToRecord> tasksWithTargetAgv) {
        try {
            // 获取所有指定的目标AGV（可能有多个任务指定不同的AGV）
            Set<String> specifiedAgvCodes = tasksWithTargetAgv.stream()
                .map(FromToRecord::getPendingMergeTargetAgv)
                .collect(Collectors.toSet());

            // 获取当前可用的AGV列表
            List<AgvNearingCompletion> availableAgvs = getAgvsNearingCommandCompletion();
            Map<String, AgvNearingCompletion> agvMap = availableAgvs.stream()
                .collect(Collectors.toMap(AgvNearingCompletion::getAgvCode, agv -> agv));

            for (String specifiedAgvCode : specifiedAgvCodes) {
                AgvNearingCompletion specifiedAgv = agvMap.get(specifiedAgvCode);

                if (specifiedAgv == null) {
                    log.warn("【目标AGV检查】commandId[{}] 指定的目标AGV[{}]不在可用列表中", commandId, specifiedAgvCode);
                    continue;
                }

                // 检查指定的AGV是否有容量
                if (!checkAgvCapacityForMerge(specifiedAgv)) {
                    log.warn("【目标AGV检查】commandId[{}] 指定的目标AGV[{}]容量不足", commandId, specifiedAgvCode);
                    continue;
                }

                // 获取指定给该AGV的任务
                List<FromToRecord> tasksForThisAgv = tasksWithTargetAgv.stream()
                    .filter(task -> specifiedAgvCode.equals(task.getPendingMergeTargetAgv()))
                    .collect(Collectors.toList());

                // 检查这些任务是否能与指定的AGV合单
                if (canCommandMergeWithAgv(commandId, tasksForThisAgv, specifiedAgv)) {
                    log.info("【目标AGV检查】commandId[{}] 可以继续使用指定的目标AGV[{}]，延迟分配 - AGV状态：总容量={}, 已占用={}, 已pending={}, 可用={}, 需要={}",
                             commandId, specifiedAgvCode, specifiedAgv.getTotalCapacity(),
                             specifiedAgv.getOccupiedStorageCount(), specifiedAgv.getPendingTaskCount(),
                             specifiedAgv.getAvailableCapacity(), tasksForThisAgv.size());
                    return true;
                } else {
                    log.warn("【目标AGV检查】commandId[{}] 无法与指定的目标AGV[{}]合单（规则不匹配）", commandId, specifiedAgvCode);
                }
            }

            return false;

        } catch (Exception e) {
            log.error("检查指定目标AGV时发生异常，commandId[{}]", commandId, e);
            return false;
        }
    }

    /**
     * 检查 commandId 是否能够合并进入当前正在运行的任务
     * 考虑容量限制和负载均衡，避免单个 AGV pending 过多任务
     */
    private boolean canMergeWithCurrentlyRunningTasks(String commandId, List<FromToRecord> commandTasks) {
        try {
            // 1. 获取当前正在执行任务的 AGV（已包含容量检查）
            List<AgvNearingCompletion> nearingCompletionAgvs = getAgvsNearingCommandCompletion();

            if (nearingCompletionAgvs.isEmpty()) {
                log.debug("当前没有可用于合单的 AGV，commandId[{}] 无法合单", commandId);
                return false;
            }

            // 2. 计算当前 commandId 需要的储位数量
            int requiredCapacity = commandTasks.size(); // 每个 FromToRecord 占用一个储位

            // 3. 寻找最优的 AGV 进行合单（考虑容量和负载均衡）
            AgvNearingCompletion bestAgv = findBestAgvForMerging(commandId, commandTasks, nearingCompletionAgvs, requiredCapacity);

            if (bestAgv != null) {
                log.debug("commandId[{}] 可以与最优 AGV[{}] 合单 - 容量匹配：总容量={}, 已占用={}, 已pending={}, 可用={}, 需要={}",
                         commandId, bestAgv.getAgvCode(), bestAgv.getTotalCapacity(),
                         bestAgv.getOccupiedStorageCount(), bestAgv.getPendingTaskCount(),
                         bestAgv.getAvailableCapacity(), requiredCapacity);
                return true;
            }

            log.debug("commandId[{}] 无法找到合适的 AGV 进行合单，需要储位={}", commandId, requiredCapacity);
            return false;

        } catch (Exception e) {
            log.error("检查 commandId[{}] 是否能合并进入正在运行任务时发生异常", commandId, e);
            return false;
        }
    }

    /**
     * 为合单寻找可用的 AGV
     * 优先合单原则：只要能合单且有足够容量就立即选择，不考虑负载均衡
     */
    private AgvNearingCompletion findBestAgvForMerging(String commandId, List<FromToRecord> commandTasks,
                                                      List<AgvNearingCompletion> candidateAgvs, int requiredCapacity) {
        try {
            // 遍历所有候选 AGV，找到第一个能够合单且有足够容量的
            for (AgvNearingCompletion agvInfo : candidateAgvs) {
                // 1. 检查容量是否足够
                if (agvInfo.getAvailableCapacity() < requiredCapacity) {
                    log.debug("AGV[{}] 容量不足：需要={}, 可用={}",
                             agvInfo.getAgvCode(), requiredCapacity, agvInfo.getAvailableCapacity());
                    continue;
                }

                // 2. 检查是否能通过合单规则
                if (canCommandMergeWithAgv(commandId, commandTasks, agvInfo)) {
                    log.debug("AGV[{}] 通过合单检查，选择进行合单 - 容量详情：总容量={}, 已占用={}, 已pending={}, 可用={}, 需要={}",
                             agvInfo.getAgvCode(), agvInfo.getTotalCapacity(), agvInfo.getOccupiedStorageCount(),
                             agvInfo.getPendingTaskCount(), agvInfo.getAvailableCapacity(), requiredCapacity);
                    return agvInfo; // 立即返回第一个符合条件的 AGV
                } else {
                    log.debug("AGV[{}] 未通过合单规则检查 - 容量详情：总容量={}, 已占用={}, 已pending={}, 可用={}, 需要={}",
                             agvInfo.getAgvCode(), agvInfo.getTotalCapacity(), agvInfo.getOccupiedStorageCount(),
                             agvInfo.getPendingTaskCount(), agvInfo.getAvailableCapacity(), requiredCapacity);
                }
            }

            log.debug("没有找到能够合单 commandId[{}] 的有效 AGV", commandId);
            return null;

        } catch (Exception e) {
            log.error("为 commandId[{}] 寻找可用 AGV 时发生异常", commandId, e);
            return null;
        }
    }



    /**
     * 检查指定 commandId 是否能与特定 AGV 合单
     */
    private boolean canCommandMergeWithAgv(String commandId, List<FromToRecord> commandTasks, AgvNearingCompletion agvInfo) {
        try {
            // 1. bayName 过滤：只有相同 bayName 的任务才能合单
            List<FromToRecord> sameBayNameTasks = filterTasksBySameBayName(agvInfo, commandTasks);
            if (sameBayNameTasks.isEmpty()) {
                log.debug("commandId[{}] 与 AGV[{}] bayName 不匹配", commandId, agvInfo.getAgvCode());
                return false;
            }

            // 2. 合单规则和容量检查：使用与 InsertTaskComponent 相同的逻辑
            List<FromToRecord> mergeableTasks = findMergeableCandidatesForAgv(agvInfo, sameBayNameTasks);

            // 3. 检查是否包含当前 commandId 的所有任务
            Set<String> mergeableCommandIds = mergeableTasks.stream()
                .map(FromToRecord::getCommandId)
                .collect(Collectors.toSet());

            boolean canMerge = mergeableCommandIds.contains(commandId);

            if (canMerge) {
                log.debug("commandId[{}] 通过合单规则检查，可以与 AGV[{}] 合单", commandId, agvInfo.getAgvCode());
            } else {
                log.debug("commandId[{}] 未通过合单规则检查，无法与 AGV[{}] 合单", commandId, agvInfo.getAgvCode());
            }

            return canMerge;

        } catch (Exception e) {
            log.error("检查 commandId[{}] 与 AGV[{}] 合单可能性时发生异常", commandId, agvInfo.getAgvCode(), e);
            return false;
        }
    }

    /**
     * 按优先级和创建时间排序commandId
     */
    private List<String> sortCommandIdsByPriority(List<String> commandIds,
                                                 Map<String, List<FromToRecord>> commandGroups) {
        return commandIds.stream()
                .sorted((commandId1, commandId2) -> {
                    List<FromToRecord> tasks1 = commandGroups.get(commandId1);
                    List<FromToRecord> tasks2 = commandGroups.get(commandId2);

                    if (tasks1 == null || tasks1.isEmpty()) return 1;
                    if (tasks2 == null || tasks2.isEmpty()) return -1;

                    FromToRecord task1 = tasks1.get(0); // 使用第一个任务代表整个commandId
                    FromToRecord task2 = tasks2.get(0);

                    // 1. 按priority排序（越小越优先）
                    int priorityCompare = Integer.compare(task1.getPriority(), task2.getPriority());
                    if (priorityCompare != 0) {
                        return priorityCompare;
                    }

                    // 2. priority相同时，按创建时间排序（越早越优先）
                    if (task1.getCreateTime() != null && task2.getCreateTime() != null) {
                        return task1.getCreateTime().compareTo(task2.getCreateTime());
                    }

                    // 3. 如果创建时间为空，按commandId字典序排序
                    return commandId1.compareTo(commandId2);
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理单个commandId组的任务（保证原子性，只使用属于该bayName的AGV）
     */
    private void processCommandGroup(String commandId, List<FromToRecord> commandTasks, List<AgvDTO> bayAgvs) {
        log.info("开始处理commandId[{}]的{}个任务", commandId, commandTasks.size());

        try {
            // 获取第一个任务来确定AGV分配策略（同一commandId下的任务应该有相同的分配策略）
            FromToRecord firstTask = commandTasks.get(0);
            List<String> taskAvailableAgvs = agvAllocationService.getAvailableAgvsForTask(firstTask);

            // 与当前bayName的空闲AGV求交集
            List<AgvDTO> availableAgvs = bayAgvs.stream()
                    .filter(agv -> taskAvailableAgvs.isEmpty() || taskAvailableAgvs.contains(agv.getAgvCode()))
                    .collect(Collectors.toList());

            if (!availableAgvs.isEmpty()) {
                // 将整个commandId组作为原子单位处理
                log.info("【派车成功】commandId[{}]分配到{}个AGV", commandId, availableAgvs.size());
                processCommandWithAgvs(commandId, commandTasks, availableAgvs);
            } else {
                // 记录派车失败，详细原因在统一报告中已显示
                log.warn("【派车失败】commandId[{}]无可用AGV - 任务指定AGV: {}, 可用范围: {}",
                        commandId, firstTask.getAgvCode(),
                        taskAvailableAgvs.isEmpty() ? "无限制" : taskAvailableAgvs);
            }

        } catch (Exception e) {
            log.error("处理commandId[{}]时发生异常", commandId, e);
        }
    }

    /**
     * 处理单个commandId组与可用AGV（保证原子性）
     */
    private void processCommandWithAgvs(String commandId, List<FromToRecord> commandTasks, List<AgvDTO> availableAgvs) {
        try {
            log.info("开始为commandId[{}]创建工作流，包含{}个任务", commandId, commandTasks.size());

            // 批量验证点位并创建记录
            List<S66F3CommandHandler.Location> froms = new ArrayList<>();
            List<S66F3CommandHandler.Location> tos = new ArrayList<>();
            int index = 0;

            // 按formToSeq顺序处理每个任务
            for (FromToRecord task : commandTasks) {
                Port from = portService.getByCode(task.getFromCode());
                Assert.notNull(from, "点位[{}]不存在", task.getFromCode());
                Port to = portService.getByCode(task.getToCode());
                Assert.notNull(to, "点位[{}]不存在", task.getToCode());

                // 使用传入的carrierId，如果为空则生成随机字符串
                String lotId = StrUtil.isNotBlank(task.getCarrierId()) ? task.getCarrierId() : "";

                S66F3CommandHandler.Location locationFrom = new S66F3CommandHandler.Location(
                        from.getMapCode(),
                        from.getActionParam().get(0).getMarkerCode(),
                        true, task.getIsLastInCommand() != null ? task.getIsLastInCommand() : true, commandId, commandId, from.getId(), from.getPortType(),
                        from.getCode(), lotId, TaskNodeAction.PICK_UP, from.getCode(),
                        to.getCode(), task.getFormToSeq());
                locationFrom.setIndex(index++);

                S66F3CommandHandler.Location locationTo = new S66F3CommandHandler.Location(
                        to.getMapCode(),
                        to.getActionParam().get(0).getMarkerCode(),
                        true, task.getIsLastInCommand() != null ? task.getIsLastInCommand() : true, commandId, commandId, to.getId(), to.getPortType(),
                        to.getCode(), lotId, TaskNodeAction.PUT_DOWN, from.getCode(),
                        to.getCode(), task.getFormToSeq());
                locationTo.setIndex(index++);

                froms.add(locationFrom);
                tos.add(locationTo);
            }

            // 转换AGV列表为代码集合
            Set<String> availableAgvCodes = availableAgvs.stream()
                    .map(AgvDTO::getAgvCode)
                    .collect(Collectors.toSet());

            // 创建工作流 - 传入完整的commandTasks列表
            Pair<String, String> workFlow = insertTaskService.createWorkFlow(commandTasks, commandId, froms, tos, availableAgvCodes);
            String workFlowId = workFlow.getKey();
            String allocatedAgv = workFlow.getValue();

            if (StringUtils.isNotBlank(workFlowId)) {
                // 🔥 修复：派车成功时清理 pending 标识，避免统计不准确
                // 检查是否有任务原本是 pending 状态
                boolean hasPendingTasks = commandTasks.stream()
                    .anyMatch(task -> Boolean.TRUE.equals(task.getIsPendingMerge()) ||
                             StringUtils.isNotBlank(task.getPendingMergeTargetAgv()) ||
                             task.getPendingMergeTime() != null);

                // 成功创建工作流 - 更新整个commandId下的所有任务
                fromToRecordService.update(Wrappers.lambdaUpdate(FromToRecord.class)
                        .eq(FromToRecord::getCommandId, commandId)
                        .set(FromToRecord::getFlowId, workFlowId)
                        .set(FromToRecord::getAgvCode, allocatedAgv)
                        .set(FromToRecord::getStatus, FromToRecordStatusEnum.DISPATCHING)
                        // 🔥 修复：设置 pending 标识为 true，确保统计准确
                        .set(FromToRecord::getIsPendingMerge, true)
                        .set(FromToRecord::getPendingMergeTime, LocalDateTime.now())
                        .set(FromToRecord::getPendingMergeTargetAgv, allocatedAgv));

                // 将分配的AGV代码放入缓存
                BizConstants.AGV_CODE_CACHE.put(allocatedAgv, commandId);

                if (hasPendingTasks) {
                    log.info("【派车成功】CommandId[{}], WorkFlowId[{}], 分配AGV[{}], 包含{}个任务（已更新pending标识）",
                            commandId, workFlowId, allocatedAgv, commandTasks.size());
                } else {
                    log.info("【派车成功】CommandId[{}], WorkFlowId[{}], 分配AGV[{}], 包含{}个任务（已设置pending标识）",
                            commandId, workFlowId, allocatedAgv, commandTasks.size());
                }
            } else {
                log.warn("创建工作流失败，CommandId[{}]", commandId);
            }

        } catch (Exception e) {
            log.error("处理commandId[{}]时发生异常", commandId, e);
        }
    }









    /**
     * 处理特定bayName的任务
     */
    private void processBayNameTasks(String bayName, List<AgvDTO> bayAgvs) {
        // 使用独立的锁，避免不同bayName之间的阻塞
        ReentrantLock bayLock = getBayLock(bayName);
        if (!bayLock.tryLock()) {
            log.warn("bayName[{}]正在处理中，跳过本次执行", bayName);
            return;
        }

        try {
            log.info("开始处理bayName[{}]的任务，可用AGV数量: {}", bayName, bayAgvs.size());

            // 显示当前可用于合单的AGV状态总览
            List<AgvNearingCompletion> runningAgvs = getAgvsNearingCommandCompletion();
            if (!runningAgvs.isEmpty()) {
                log.info("bayName[{}] 当前可用于合单的AGV总览：", bayName);
                for (AgvNearingCompletion agv : runningAgvs) {
                    log.info("  AGV[{}]: 总容量={}, 已占用={}, 已pending={}, 可用={}",
                             agv.getAgvCode(), agv.getTotalCapacity(), agv.getOccupiedStorageCount(),
                             agv.getPendingTaskCount(), agv.getAvailableCapacity());
                }
            } else {
                log.info("bayName[{}] 当前没有可用于合单的AGV", bayName);
            }

            // 获取该bayName下的AGV代码
            Set<String> agvCodes = bayAgvs.stream()
                    .map(AgvDTO::getAgvCode)
                    .collect(Collectors.toSet());

            Set<String> processedCommandIds = new HashSet<>();
            int targetCount = bayAgvs.size();
            int successCount = 0;

            // 容错控制（使用配置值）
            int maxRounds = dispatchConfig.getMaxRoundsPerBay();
            int currentRound = 0;
            int consecutiveEmptyRounds = 0;
            int maxConsecutiveEmptyRounds = dispatchConfig.getMaxConsecutiveEmptyRoundsPerBay();
            long startTime = System.currentTimeMillis();
            long maxProcessTime = dispatchConfig.getMaxProcessTimePerBay();

            // 持续获取和处理任务
            while ((successCount < targetCount) && (currentRound < maxRounds) &&
                    (consecutiveEmptyRounds < maxConsecutiveEmptyRounds) &&
                    ((System.currentTimeMillis() - startTime) < maxProcessTime)) {

                if (CollectionUtils.isEmpty(agvCodes)) {
                    log.warn("bayName[{}]可用车辆为空，退出", bayName);
                    break;
                }

                currentRound++;
                int batchSize = Math.max(1, targetCount - successCount);

                // 获取该bayName的任务
                List<String> topNRecords = fromToRecordService.getTopNRecordsByBayName(
                        batchSize, bayName, agvCodes, processedCommandIds);

                if (Objects.isNull(topNRecords) || CollectionUtils.isEmpty(topNRecords)) {
                    consecutiveEmptyRounds++;
                    log.warn("bayName[{}]第{}轮未获取到可用任务，连续空轮次:{}/{}, 已成功创建{}个工作流，目标{}个",
                            bayName, currentRound, consecutiveEmptyRounds, maxConsecutiveEmptyRounds, successCount, targetCount);
                    if (consecutiveEmptyRounds >= maxConsecutiveEmptyRounds) {
                        log.warn("bayName[{}]连续{}轮未获取到数据，停止处理", bayName, maxConsecutiveEmptyRounds);
                        break;
                    }
                    // 短暂等待后继续
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("bayName[{}]线程被中断", bayName, e);
                        break;
                    }
                    continue;
                }

                // 重置连续空轮次计数
                consecutiveEmptyRounds = 0;

                log.info("bayName[{}]第{}轮获取到{}个任务: {}", bayName, currentRound, topNRecords.size(), topNRecords);

                // 处理任务
                int roundSuccessCount = processBayNameTaskBatch(bayName, topNRecords, agvCodes, processedCommandIds);
                successCount += roundSuccessCount;

                log.info("bayName[{}]第{}轮处理完成，本轮成功创建{}个工作流，累计成功{}个，目标{}个",
                        bayName, currentRound, roundSuccessCount, successCount, targetCount);
            }

            // 输出最终结果
            long totalTime = System.currentTimeMillis() - startTime;
            String stopReason = getStopReason(successCount, targetCount, currentRound, maxRounds,
                    consecutiveEmptyRounds, maxConsecutiveEmptyRounds, totalTime, maxProcessTime);

            log.info("bayName[{}]最终处理完成，目标创建{}个工作流，实际成功创建{}个，处理轮次:{}/{}, 耗时:{}ms, 停止原因:{}",
                    bayName, targetCount, successCount, currentRound, maxRounds, totalTime, stopReason);

        } catch (Exception e) {
            log.error("bayName[{}]处理任务时发生异常", bayName, e);
        } finally {
            bayLock.unlock();
        }
    }

    /**
     * 处理一批任务
     */
    private int processBayNameTaskBatch(String bayName, List<String> commandIds, Set<String> agvCodes, Set<String> processedCommandIds) {
        int successCount = 0;
        Set<String> reservedAgvs = new HashSet<>();

        for (String commandId : commandIds) {
            if (CollectionUtils.isEmpty(agvCodes)) {
                log.warn("bayName[{}]可用车辆为空，退出", bayName);
                break;
            }

            try {
                List<FromToRecord> list = fromToRecordService.list(
                        Wrappers.lambdaQuery(FromToRecord.class)
                                .eq(FromToRecord::getCommandId, commandId)
                                .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                                .isNull(FromToRecord::getFlowId));

                if (CollectionUtils.isEmpty(list)) {
                    log.warn("bayName[{}]CommandId[{}]没有找到对应的FromToRecord", bayName, commandId);
                    continue;
                }

                // 构建位置信息
                List<S66F3CommandHandler.Location> froms = new ArrayList<>(list.size());
                List<S66F3CommandHandler.Location> tos = new ArrayList<>(list.size());
                int index = 0;

                for (FromToRecord local : list) {
                    Port from = portService.getByCode(local.getFromCode());
                    Assert.notNull(from, "点位[{}]不存在", local.getFromCode());
                    Port to = portService.getByCode(local.getToCode());
                    Assert.notNull(to, "点位[{}]不存在", local.getToCode());

                    String lotId = StrUtil.isNotBlank(local.getCarrierId()) ? local.getCarrierId() : "";

                    S66F3CommandHandler.Location locationFrom = new S66F3CommandHandler.Location(
                            from.getMapCode(),
                            from.getActionParam().get(0).getMarkerCode(),
                            true, local.getIsLastInCommand() != null ? local.getIsLastInCommand() : true, commandId, commandId, from.getId(), from.getPortType(),
                            from.getCode(), lotId, TaskNodeAction.PICK_UP, from.getCode(),
                            to.getCode(), local.getFormToSeq());
                    locationFrom.setIndex(index++);

                    S66F3CommandHandler.Location locationTo = new S66F3CommandHandler.Location(
                            to.getMapCode(),
                            to.getActionParam().get(0).getMarkerCode(),
                            true, local.getIsLastInCommand() != null ? local.getIsLastInCommand() : true, commandId, commandId, to.getId(), to.getPortType(),
                            to.getCode(), lotId, TaskNodeAction.PUT_DOWN, from.getCode(),
                            to.getCode(), local.getFormToSeq());
                    locationTo.setIndex(index++);

                    froms.add(locationFrom);
                    tos.add(locationTo);
                }

                // 检查是否需要特定的AGV
                Set<String> requiredAgvs = fromToRecordService.getRequiredAgvsForCommand(list);
                Set<String> availableAgvs = agvCodes;

                if (CollectionUtils.isNotEmpty(requiredAgvs)) {
                    availableAgvs = agvCodes.stream()
                            .filter(requiredAgvs::contains)
                            .collect(Collectors.toSet());

                    if (CollectionUtils.isEmpty(availableAgvs)) {
                        log.warn("bayName[{}]CommandId[{}]需要特定AGV{}，但当前可用AGV{}中没有匹配的，跳过",
                                bayName, commandId, requiredAgvs, agvCodes);
                        continue;
                    }
                    log.info("bayName[{}]CommandId[{}]需要特定AGV{}，当前可用的匹配AGV：{}",
                            bayName, commandId, requiredAgvs, availableAgvs);
                }

                if (CollectionUtils.isEmpty(availableAgvs)) {
                    log.warn("bayName[{}]CommandId[{}]没有可用的AGV，跳过创建工作流", bayName, commandId);
                    continue;
                }

                // 创建工作流
                Pair<String, String> workFlow = insertTaskService.createWorkFlow(list, commandId, froms, tos, availableAgvs);
                String workFlowId = workFlow.getKey();
                String allocatedAgv = workFlow.getValue();

                if (StringUtils.isNotBlank(workFlowId)) {
                    // 成功创建工作流
                    processedCommandIds.add(commandId);
                    fromToRecordService.update(Wrappers.lambdaUpdate(FromToRecord.class)
                            .eq(FromToRecord::getCommandId, commandId)
                            .set(FromToRecord::getFlowId, workFlowId)
                            .set(FromToRecord::getAgvCode, allocatedAgv)
                            .set(FromToRecord::getStatus, FromToRecordStatusEnum.DISPATCHING));
                    successCount++;
                    agvCodes.remove(allocatedAgv);
                    BizConstants.AGV_CODE_CACHE.put(allocatedAgv, commandId);

                    log.info("bayName[{}]成功创建工作流，CommandId[{}], WorkFlowId[{}], 分配AGV[{}]",
                            bayName, commandId, workFlowId, allocatedAgv);
                } else {
                    if (CollectionUtils.isNotEmpty(requiredAgvs)) {
                        // 为高优先级任务预留AGV
                        for (String requiredAgv : requiredAgvs) {
                            if (agvCodes.contains(requiredAgv)) {
                                agvCodes.remove(requiredAgv);
                                reservedAgvs.add(requiredAgv);
                                log.info("bayName[{}]为高优先级失败任务CommandId[{}]临时移除AGV[{}]",
                                        bayName, commandId, requiredAgv);
                            }
                        }
                    }
                    log.warn("bayName[{}]创建工作流失败，CommandId[{}]", bayName, commandId);
                }

            } catch (Exception e) {
                log.error("bayName[{}]创建工作流异常，CommandId[{}], 异常信息：{}",
                        bayName, commandId, Throwables.getStackTraceAsString(e));
            }
        }

        // 恢复预留的AGV
        if (CollectionUtils.isNotEmpty(reservedAgvs)) {
            agvCodes.addAll(reservedAgvs);
            log.info("bayName[{}]恢复预留的AGV：{}", bayName, reservedAgvs);
        }

        return successCount;
    }

    /**
     * 获取bayName对应的锁
     */
    private ReentrantLock getBayLock(String bayName) {
        return bayLocks.computeIfAbsent(bayName, k -> new ReentrantLock());
    }



    /**
     * 获取停止原因
     */
    private String getStopReason(int successCount, int targetCount, int currentRound, int maxRounds,
                                int consecutiveEmptyRounds, int maxConsecutiveEmptyRounds,
                                long totalTime, long maxProcessTime) {
        if (successCount >= targetCount) {
            return "达到目标数量";
        } else if (currentRound >= maxRounds) {
            return String.format("达到最大轮次数(%d)", maxRounds);
        } else if (consecutiveEmptyRounds >= maxConsecutiveEmptyRounds) {
            return String.format("连续%d轮无数据", maxConsecutiveEmptyRounds);
        } else if (totalTime >= maxProcessTime) {
            return String.format("处理超时(%dms)", maxProcessTime);
        } else {
            return "未知原因";
        }
    }

    // ==========================================
    // 延迟分配机制相关方法
    // ==========================================

    /**
     * 识别合单候选任务
     *
     * 完整的延迟分配管理：
     * 1. 清理不再需要的延迟分配（AGV完成任务、bayName不匹配等）
     * 2. 为当前正在执行任务的AGV寻找新的合单候选
     * 3. 确保延迟分配的准确性和时效性
     *
     * @param validTasks 待分配的有效任务列表
     * @return 合单候选任务列表，包含任务和目标AGV信息
     */
    private List<PendingMergeCandidate> identifyPendingMergeCandidates(List<FromToRecord> validTasks) {
        List<PendingMergeCandidate> candidates = new ArrayList<>();

        try {
            // 1. 获取当前正在执行任务的AGV
            List<AgvNearingCompletion> nearingCompletionAgvs = getAgvsNearingCommandCompletion();
//            Set<String> currentExecutingAgvCodes = nearingCompletionAgvs.stream()
//                .map(AgvNearingCompletion::getAgvCode)
//                .collect(Collectors.toSet());

            // 2. 清理不再需要的延迟分配
//            cleanupObsoletePendingMerge(currentExecutingAgvCodes, validTasks);

            // 3. 如果没有正在执行的AGV，直接返回
            if (nearingCompletionAgvs.isEmpty()) {
                log.debug("当前没有正在执行任务的AGV，无需新增延迟分配");
                return candidates;
            }

            // 🔥 重构：优先处理已有 pendingMergeTargetAgv 的任务，然后处理新任务

            // 4.1 第一阶段：优先处理已指定目标AGV的任务
            List<FromToRecord> tasksWithTargetAgv = validTasks.stream()
                .filter(task -> StringUtils.isNotBlank(task.getPendingMergeTargetAgv()))
                .collect(Collectors.toList());

            List<FromToRecord> remainingTasks = new ArrayList<>(validTasks);
            remainingTasks.removeAll(tasksWithTargetAgv);

            log.info("任务分类：已指定目标AGV的任务{}个，待分配任务{}个",
                     tasksWithTargetAgv.size(), remainingTasks.size());

            // 处理已指定目标AGV的任务
            candidates.addAll(processPriorityTargetAgvTasks(tasksWithTargetAgv, nearingCompletionAgvs));

            // 4.2 第二阶段：为剩余任务分配AGV（避免重复分配）
            Set<Long> assignedTaskIds = candidates.stream()
                .map(candidate -> candidate.getTask().getId())
                .collect(Collectors.toSet());

            List<FromToRecord> unassignedTasks = remainingTasks.stream()
                .filter(task -> !assignedTaskIds.contains(task.getId()))
                .collect(Collectors.toList());

            candidates.addAll(processRemainingTasks(unassignedTasks, nearingCompletionAgvs));

            // 去重：按任务ID去重，保留第一个目标AGV
            candidates = candidates.stream()
                .collect(Collectors.toMap(
                    candidate -> candidate.getTask().getId(),
                    candidate -> candidate,
                    (existing, replacement) -> existing)) // 保留第一个
                .values()
                .stream()
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("识别合单候选任务时发生异常", e);
        }

        return candidates;
    }

    /**
     * 清理已完成任务的 pending 标识
     * 当任务状态不再是 NOT_START 或 DISPATCHING 时，应该清理 pending 标识
     */
    private void cleanupCompletedTasksPendingFlags() {
        try {
            // 查找已完成但仍有 pending 标识的任务
            List<FromToRecord> completedTasksWithPending = fromToRecordService.list(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getIsPendingMerge, true)
                    .notIn(FromToRecord::getStatus, Arrays.asList(
                        FromToRecordStatusEnum.NOT_START,
                        FromToRecordStatusEnum.DISPATCHING
                    ))
            );

            if (!completedTasksWithPending.isEmpty()) {
                for (FromToRecord task : completedTasksWithPending) {
                    task.setIsPendingMerge(false);
                    task.setPendingMergeTime(null);
                    task.setPendingMergeTargetAgv(null);
                }
                fromToRecordService.updateBatchById(completedTasksWithPending);

                log.info("清理了{}个已完成任务的pending标识", completedTasksWithPending.size());
            }

        } catch (Exception e) {
            log.error("清理已完成任务的pending标识时发生异常", e);
        }
    }

    /**
     * 检查并修复 pending merge 数据一致性问题
     * 修复场景：pendingMergeTime 有值但 isPendingMerge 为 false 的情况
     */
    private void checkAndFixPendingMergeConsistency() {
        try {
            // 查找数据不一致的记录：有 pendingMergeTime 但 isPendingMerge 为 false
            List<FromToRecord> inconsistentTasks = fromToRecordService.list(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                    .isNull(FromToRecord::getFlowId)
                    .isNotNull(FromToRecord::getPendingMergeTime)
                    .and(wrapper -> wrapper
                        .isNull(FromToRecord::getIsPendingMerge)
                        .or()
                        .eq(FromToRecord::getIsPendingMerge, false)
                    )
            );

            if (inconsistentTasks.isEmpty()) {
                log.debug("未发现 pending merge 数据不一致问题");
                return;
            }

            log.warn("发现{}个 pending merge 数据不一致的任务，开始修复", inconsistentTasks.size());

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = now.minus(dispatchConfig.getPendingMergeTimeoutSeconds(), ChronoUnit.SECONDS);

            List<FromToRecord> tasksToFix = new ArrayList<>();
            List<FromToRecord> tasksToCleanup = new ArrayList<>();

            for (FromToRecord task : inconsistentTasks) {
                LocalDateTime pendingTime = task.getPendingMergeTime();

                if (pendingTime.isBefore(expireTime)) {
                    // 已过期，清理所有 pending 相关字段
                    task.setPendingMergeTime(null);
                    task.setIsPendingMerge(false);
                    task.setPendingMergeTargetAgv(null);
                    tasksToCleanup.add(task);
                    log.info("清理过期的不一致任务：commandId[{}], pendingTime[{}]",
                             task.getCommandId(), pendingTime);
                } else {
                    // 未过期，修复 isPendingMerge 为 true
                    task.setIsPendingMerge(true);
                    tasksToFix.add(task);
                    log.info("修复不一致任务：commandId[{}], 设置 isPendingMerge=true", task.getCommandId());
                }
            }

            // 批量更新
            List<FromToRecord> allTasksToUpdate = new ArrayList<>();
            allTasksToUpdate.addAll(tasksToFix);
            allTasksToUpdate.addAll(tasksToCleanup);

            if (!allTasksToUpdate.isEmpty()) {
                fromToRecordService.updateBatchById(allTasksToUpdate);
                log.info("修复完成：修复{}个任务，清理{}个过期任务", tasksToFix.size(), tasksToCleanup.size());
            }

        } catch (Exception e) {
            log.error("检查和修复 pending merge 数据一致性时发生异常", e);
        }
    }

    /**
     * 优先处理已指定目标AGV的任务
     * 只有当指定的AGV容量不足时，才会被排除
     */
    private List<PendingMergeCandidate> processPriorityTargetAgvTasks(List<FromToRecord> tasksWithTargetAgv,
                                                                     List<AgvNearingCompletion> nearingCompletionAgvs) {
        List<PendingMergeCandidate> candidates = new ArrayList<>();

        // 按目标AGV分组
        Map<String, List<FromToRecord>> tasksByTargetAgv = tasksWithTargetAgv.stream()
            .collect(Collectors.groupingBy(FromToRecord::getPendingMergeTargetAgv));

        // 创建AGV映射以便快速查找
        Map<String, AgvNearingCompletion> agvMap = nearingCompletionAgvs.stream()
            .collect(Collectors.toMap(AgvNearingCompletion::getAgvCode, agv -> agv));

        for (Map.Entry<String, List<FromToRecord>> entry : tasksByTargetAgv.entrySet()) {
            String targetAgvCode = entry.getKey();
            List<FromToRecord> tasks = entry.getValue();

            AgvNearingCompletion targetAgv = agvMap.get(targetAgvCode);
            if (targetAgv == null) {
                log.warn("指定的目标AGV[{}]不在可用列表中，跳过{}个任务", targetAgvCode, tasks.size());
                continue;
            }

            // 检查目标AGV是否有容量
            if (!checkAgvCapacityForMerge(targetAgv)) {
                log.warn("指定的目标AGV[{}]容量不足，跳过{}个任务", targetAgvCode, tasks.size());
                continue;
            }

            // 过滤出与目标AGV相同bayName的任务
            List<FromToRecord> sameBayNameTasks = filterTasksBySameBayName(targetAgv, tasks);
            if (sameBayNameTasks.isEmpty()) {
                log.warn("指定的目标AGV[{}]与任务bayName不匹配，跳过{}个任务", targetAgvCode, tasks.size());
                continue;
            }

            // 查找可以与目标AGV合单的任务
            List<FromToRecord> mergeableTasks = findMergeableCandidatesForAgv(targetAgv, sameBayNameTasks);

            // 根据AGV实际可用容量限制任务数量
            int maxAllowedTasks = targetAgv.getAvailableCapacity();
            List<FromToRecord> limitedTasks = mergeableTasks.stream()
                .limit(maxAllowedTasks)
                .collect(Collectors.toList());

            // 为限制后的任务创建候选对象
            for (FromToRecord task : limitedTasks) {
                candidates.add(new PendingMergeCandidate(task, targetAgvCode));
            }

            log.info("优先分配：AGV[{}] 指定任务{}个，可合单{}个，实际分配{}个（容量限制={}）",
                     targetAgvCode, tasks.size(), mergeableTasks.size(), limitedTasks.size(), maxAllowedTasks);
        }

        return candidates;
    }

    /**
     * 处理剩余任务，为其分配合适的AGV
     */
    private List<PendingMergeCandidate> processRemainingTasks(List<FromToRecord> remainingTasks,
                                                             List<AgvNearingCompletion> nearingCompletionAgvs) {
        List<PendingMergeCandidate> candidates = new ArrayList<>();

        for (AgvNearingCompletion agvInfo : nearingCompletionAgvs) {
            // 检查该AGV是否有容量进行合单
            if (!checkAgvCapacityForMerge(agvInfo)) {
                continue;
            }

            // 过滤出与该AGV相同bayName的任务
            List<FromToRecord> sameBayNameTasks = filterTasksBySameBayName(agvInfo, remainingTasks);

            // 查找可以与该AGV合单的任务
            List<FromToRecord> mergeableTasks = findMergeableCandidatesForAgv(agvInfo, sameBayNameTasks);

            // 根据AGV实际可用容量限制任务数量
            int maxAllowedTasks = agvInfo.getAvailableCapacity();
            List<FromToRecord> limitedTasks = mergeableTasks.stream()
                .limit(maxAllowedTasks)
                .collect(Collectors.toList());

            // 为限制后的任务创建候选对象
            for (FromToRecord task : limitedTasks) {
                candidates.add(new PendingMergeCandidate(task, agvInfo.getAgvCode()));
            }

            // 记录容量控制日志
            if (mergeableTasks.size() > limitedTasks.size()) {
                log.info("剩余任务分配：AGV[{}] 找到{}个可合单任务，实际分配{}个（可用容量={}），避免超过容量",
                         agvInfo.getAgvCode(), mergeableTasks.size(), limitedTasks.size(), maxAllowedTasks);
            } else {
                log.debug("剩余任务分配：AGV[{}] 分配{}个pending任务，可用容量={}",
                         agvInfo.getAgvCode(), limitedTasks.size(), maxAllowedTasks);
            }
        }

        return candidates;
    }

    /**
     * 清理过时的延迟分配
     *
     * 清理场景：
     * 1. 目标AGV不再执行任务
     * 2. 任务的bayName与目标AGV不匹配
     * 3. 任务已经不在有效任务列表中
     */
    private void cleanupObsoletePendingMerge(Set<String> currentExecutingAgvCodes, List<FromToRecord> validTasks) {
        try {
            // 获取所有当前的延迟分配任务
            List<FromToRecord> currentPendingTasks = fromToRecordService.list(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getIsPendingMerge, true)
                    .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
                    .isNotNull(FromToRecord::getPendingMergeTargetAgv));

            if (currentPendingTasks.isEmpty()) {
                log.debug("当前没有延迟分配任务需要检查");
                return;
            }

            List<FromToRecord> tasksToCleanup = new ArrayList<>();
            Set<Long> validTaskIds = validTasks.stream()
                .map(FromToRecord::getId)
                .collect(Collectors.toSet());

            for (FromToRecord pendingTask : currentPendingTasks) {
                String targetAgvCode = pendingTask.getPendingMergeTargetAgv();
                boolean shouldCleanup = false;
                String cleanupReason = "";

                // 检查1：目标AGV是否还在执行任务
                if (!currentExecutingAgvCodes.contains(targetAgvCode)) {
                    shouldCleanup = true;
                    cleanupReason = "目标AGV不再执行任务";
                }
                // 检查2：任务是否还在有效任务列表中
                else if (!validTaskIds.contains(pendingTask.getId())) {
                    shouldCleanup = true;
                    cleanupReason = "任务不在有效任务列表中";
                }
                // 检查3：bayName是否还匹配
                else if (!isBayNameMatching(pendingTask, targetAgvCode)) {
                    shouldCleanup = true;
                    cleanupReason = "bayName不匹配";
                }

                if (shouldCleanup) {
                    tasksToCleanup.add(pendingTask);
                    log.debug("标记清理延迟分配任务[{}]，目标AGV[{}]，原因：{}",
                             pendingTask.getCommandId(), targetAgvCode, cleanupReason);
                }
            }

            if (!tasksToCleanup.isEmpty()) {
                clearPendingMergeFlags(tasksToCleanup, "过时清理");
            }

        } catch (Exception e) {
            log.error("清理过时延迟分配时发生异常", e);
        }
    }

    /**
     * 检查任务的bayName是否与目标AGV匹配
     */
    private boolean isBayNameMatching(FromToRecord task, String targetAgvCode) {
        try {
            String taskBayName = getTaskBayName(task);
            String agvBayName = getAgvBayName(targetAgvCode);

            if (StringUtils.isBlank(taskBayName) || StringUtils.isBlank(agvBayName)) {
                log.debug("任务[{}]或AGV[{}]的bayName为空，跳过匹配检查",
                         task.getCommandId(), targetAgvCode);
                return true; // 如果无法获取bayName，保守处理
            }

            boolean isMatching = taskBayName.equals(agvBayName);
            if (!isMatching) {
                log.debug("bayName不匹配：任务[{}] bayName[{}] vs AGV[{}] bayName[{}]",
                         task.getCommandId(), taskBayName, targetAgvCode, agvBayName);
            }

            return isMatching;

        } catch (Exception e) {
            log.error("检查bayName匹配时发生异常，任务[{}]，AGV[{}]",
                     task.getCommandId(), targetAgvCode, e);
            return true; // 异常时保守处理
        }
    }

    /**
     * 过滤出与指定AGV相同bayName的任务
     * 只有相同bayName的任务才能被该AGV合单
     */
    private List<FromToRecord> filterTasksBySameBayName(AgvNearingCompletion agvInfo, List<FromToRecord> tasks) {
        try {
            // 获取AGV的bayName
            String agvBayName = getAgvBayName(agvInfo.getAgvCode());
            if (StringUtils.isBlank(agvBayName)) {
                log.debug("AGV[{}] 无法获取bayName，跳过bayName过滤", agvInfo.getAgvCode());
                return new ArrayList<>();
            }

            // 过滤出相同bayName的任务
            List<FromToRecord> sameBayNameTasks = tasks.stream()
                .filter(task -> {
                    String taskBayName = getTaskBayName(task);
                    boolean isSameBayName = agvBayName.equals(taskBayName);

                    if (!isSameBayName && StringUtils.isNotBlank(taskBayName)) {
                        log.debug("任务[{}] bayName[{}] 与 AGV[{}] bayName[{}] 不匹配，过滤掉",
                                 task.getCommandId(), taskBayName, agvInfo.getAgvCode(), agvBayName);
                    }

                    return isSameBayName;
                })
                .collect(Collectors.toList());

            log.debug("AGV[{}] bayName[{}] 过滤结果: 原始任务数={}, 匹配任务数={}",
                     agvInfo.getAgvCode(), agvBayName, tasks.size(), sameBayNameTasks.size());

            return sameBayNameTasks;

        } catch (Exception e) {
            log.error("过滤AGV[{}]的相同bayName任务时发生异常", agvInfo.getAgvCode(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取AGV的bayName
     */
    private String getAgvBayName(String agvCode) {
        try {

            String bayName = null;
            // 方法2：如果方法1失败，尝试通过数据库查询AGV信息获取 agvName，再提取 bayName
            AgvDTO agvDTO = Util.getAgvDTO(agvCode);
            if (agvDTO != null && StringUtils.isNotBlank(agvDTO.getAgvName())) {
                bayName = BayNameUtils.extractBayNameFromAgvName(agvDTO.getAgvName());
                if (StringUtils.isNotBlank(bayName)) {
                    log.debug("通过AGV代码[{}]查询到agvName[{}]，提取到bayName: {}",
                             agvCode, agvDTO.getAgvName(), bayName);
                    return bayName;
                }
            }

            log.debug("无法从AGV代码[{}]获取bayName", agvCode);
            return null;

        } catch (Exception e) {
            log.error("获取AGV[{}]的bayName时发生异常", agvCode, e);
            return null;
        }
    }

    /**
     * 获取任务的bayName
     */
    private String getTaskBayName(FromToRecord task) {
        try {
            // 优先从deviceIp获取bayName
            if (StringUtils.isNotBlank(task.getDeviceIp())) {
                String bayName = communicationDeviceService.getBayNameByIpAddress(task.getDeviceIp());
                if (StringUtils.isNotBlank(bayName)) {
                    log.debug("任务[{}]通过deviceIp[{}]获取到bayName: {}",
                             task.getCommandId(), task.getDeviceIp(), bayName);
                    return bayName;
                }
            }

            // 兜底：通过AGV代码推断（如果任务已分配给AGV）
            if (StringUtils.isNotBlank(task.getAgvCode())) {
                String bayName = getAgvBayName(task.getAgvCode());
                if (StringUtils.isNotBlank(bayName)) {
                    log.debug("任务[{}]通过agvCode[{}]推断到bayName: {}",
                             task.getCommandId(), task.getAgvCode(), bayName);
                    return bayName;
                }
            }

            log.debug("任务[{}]无法获取bayName", task.getCommandId());
            return null;

        } catch (Exception e) {
            log.error("获取任务[{}]的bayName时发生异常", task.getCommandId(), e);
            return null;
        }
    }

    /**
     * 获取即将完成当前COMMAND_ID最后一个任务的AGV
     */
    private List<AgvNearingCompletion> getAgvsNearingCommandCompletion() {
        List<AgvNearingCompletion> result = new ArrayList<>();

        // 获取所有正在执行任务的AGV（包含导航状态和完成状态）
        List<String> busyAgvCodes = fromToRecordService.list(
            Wrappers.lambdaQuery(FromToRecord.class)
                .isNotNull(FromToRecord::getAgvCode)
                .in(FromToRecord::getStatus,
                    FromToRecordStatusEnum.DISPATCHING,          // 派车中 - 合单关键时机
                    FromToRecordStatusEnum.PICKUP_NAVIGATING,    // 取料导航中
                    FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION, // 到达取料位置
                    FromToRecordStatusEnum.PICK_UP_ING,          // 取料中
                    FromToRecordStatusEnum.PICK_UP_FINISHED )   // 取料完成 - 合单关键时机
//                    FromToRecordStatusEnum.PUT_DOWN_NAVIGATING,  // 放料导航中
//                    FromToRecordStatusEnum.ARRIVE_DROP_POSITION, // 到达放料位置
//                    FromToRecordStatusEnum.PUT_DOWN_ING,         // 放料中
//                    FromToRecordStatusEnum.PUT_DOWN_FINISHED)    // 放料完成 - 合单关键时机
                .select(FromToRecord::getAgvCode))
            .stream()
            .map(FromToRecord::getAgvCode)
            .distinct()
            .collect(Collectors.toList());

        for (String agvCode : busyAgvCodes) {
            // 获取该AGV当前正在执行的任务
            FromToRecord currentTask = fromToRecordService.getOne(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getAgvCode, agvCode)
                    .in(FromToRecord::getStatus,
                        FromToRecordStatusEnum.DISPATCHING,          // 派车中 - 合单关键时机
                        FromToRecordStatusEnum.PICKUP_NAVIGATING,    // 取料导航中
                        FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION, // 到达取料位置
                        FromToRecordStatusEnum.PICK_UP_ING,          // 取料中
                        FromToRecordStatusEnum.PICK_UP_FINISHED )    // 取料完成 - 合单关键时机
//                        FromToRecordStatusEnum.PUT_DOWN_NAVIGATING,  // 放料导航中
//                        FromToRecordStatusEnum.ARRIVE_DROP_POSITION, // 到达放料位置
//                        FromToRecordStatusEnum.PUT_DOWN_ING,         // 放料中
//                        FromToRecordStatusEnum.PUT_DOWN_FINISHED)    // 放料完成 - 合单关键时机
                    .last("limit 1"));

            if (currentTask != null) {
                // 获取AGV的容量信息
                List<AgvStoragePort> storageInfo = agvStoragePortService.getAllList(agvCode);
                int totalCapacity = storageInfo.size();

                // 🔥 修复：使用更精确的容量计算方法，考虑当前任务的储位占用情况
                CapacityCalculationResult preciseCapacity = calculateAgvCapacityLikeInsertTask(agvCode, currentTask);
                int physicalAvailableCapacity;
                int occupiedCount;

                if (preciseCapacity != null) {
                    // 🔥 简化：直接使用 freeSize，因为已经包含了所有 pending 任务的占用
                    physicalAvailableCapacity = preciseCapacity.getFreeSize();
                    occupiedCount = totalCapacity - physicalAvailableCapacity;
                    log.debug("AGV[{}] 使用简化容量计算：总储位={}, pending任务占用={}, 可用储位={}",
                             agvCode, totalCapacity, preciseCapacity.getPendingCount(), physicalAvailableCapacity);
                } else {
                    // 回退到简单计算方法
                    occupiedCount = getOccupiedStorageCount(agvCode);
                    physicalAvailableCapacity = totalCapacity - occupiedCount;
                    log.debug("AGV[{}] 回退到简单容量计算：总容量={}, 占用={}, 可用={}",
                             agvCode, totalCapacity, occupiedCount, physicalAvailableCapacity);
                }

                // 🔥 优化：直接使用已计算的结果，避免重复查询
                int pendingTaskCount = preciseCapacity != null ? preciseCapacity.getPendingCount() : 0;

                // physicalAvailableCapacity 已经是扣除 pending 任务后的可用容量
                int actualAvailableCapacity = physicalAvailableCapacity;

                // 只有具备实际可用储位的AGV才能进行合单
                if (actualAvailableCapacity > 0) {
                    AgvNearingCompletion agvInfo = new AgvNearingCompletion();
                    agvInfo.setAgvCode(agvCode);
                    agvInfo.setCurrentTask(currentTask);
                    agvInfo.setTotalCapacity(totalCapacity);
                    agvInfo.setOccupiedStorageCount(occupiedCount);
                    agvInfo.setAvailableCapacity(actualAvailableCapacity);
                    agvInfo.setPendingTaskCount(pendingTaskCount);

                    result.add(agvInfo);

                    String capacityMethod = preciseCapacity != null ? "精确计算" : "简单计算";
                    log.info("AGV[{}] 可用于合单 - 执行任务[{}]，容量状态({})：总储位={}, 已占用={}, 物理可用={}, 已pending={}, 实际可用={}",
                             agvCode, currentTask.getCommandId(), capacityMethod, totalCapacity, occupiedCount,
                             physicalAvailableCapacity, pendingTaskCount, actualAvailableCapacity);
                } else {
                    String capacityMethod = preciseCapacity != null ? "精确计算" : "简单计算";
                    log.info("AGV[{}] 容量已满 - 执行任务[{}]，容量状态({})：总储位={}, 已占用={}, 已pending={}, 实际可用=0，无法合单",
                             agvCode, currentTask.getCommandId(), capacityMethod, totalCapacity, occupiedCount, pendingTaskCount);
                }
            }
        }

        return result;
    }

    /**
     * 统计已经 pending 给指定 AGV 的任务数量
     * 每个 FromToRecord 占用一个储位
     */
    private int getPendingTaskCountForAgv(String agvCode) {
        try {
            int pendingCount = fromToRecordService.count(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getIsPendingMerge, true)
                    .eq(FromToRecord::getPendingMergeTargetAgv, agvCode)
                    .notIn(FromToRecord::getStatus,
                            FromToRecordStatusEnum.COMPLETED,
                            FromToRecordStatusEnum.CANCEL));

            log.debug("AGV[{}] 已 pending 任务数量: {}", agvCode, pendingCount);
            return pendingCount;

        } catch (Exception e) {
            log.error("统计 AGV[{}] pending 任务数量时发生异常", agvCode, e);
            return 0; // 异常时返回 0，保守处理
        }
    }

    /**
     * 获取AGV当前已占用储位数量
     * 注意：这个方法返回的是储位占用数量，不是任务数量
     * 用于计算 freeStorageSlotsCount = totalCapacity - occupiedStorageCount
     */
    private int getOccupiedStorageCount(String agvCode) {
        try {
            // 获取AGV的所有储位信息
            List<AgvStoragePort> allStoragePorts = agvStoragePortService.getAllList(agvCode);

            // 计算已占用的储位数量（USED 或 LOCKING 状态）
            int occupiedCount = (int) allStoragePorts.stream()
                .filter(AgvStoragePort::isUsed)
                .count();

            log.debug("AGV[{}] 储位统计: 总储位={}, 已占用={}", agvCode, allStoragePorts.size(), occupiedCount);
            return occupiedCount;

        } catch (Exception e) {
            log.error("获取AGV[{}]储位占用情况时发生异常", agvCode, e);
            // 异常时回退到基于任务状态的估算
            int taskBasedEstimate = fromToRecordService.count(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getAgvCode, agvCode)
                    .in(FromToRecord::getStatus,
                        FromToRecordStatusEnum.DISPATCHING,          // 派车中
                        FromToRecordStatusEnum.PICKUP_NAVIGATING,    // 取料导航中
                        FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION, // 到达取料位置
                        FromToRecordStatusEnum.PICK_UP_ING,          // 取料中
                        FromToRecordStatusEnum.PICK_UP_FINISHED,     // 取料完成（货物在车上）
                        FromToRecordStatusEnum.PUT_DOWN_NAVIGATING,  // 放料导航中
                        FromToRecordStatusEnum.ARRIVE_DROP_POSITION, // 到达放料位置
                        FromToRecordStatusEnum.PUT_DOWN_ING));       // 放料中

            log.debug("AGV[{}] 使用任务状态估算储位占用: {}", agvCode, taskBasedEstimate);
            return taskBasedEstimate;
        }
    }

    /**
     * 检查AGV是否有容量进行合单
     * 与 InsertTaskComponent 保持一致的容量计算逻辑
     */
    private boolean checkAgvCapacityForMerge(AgvNearingCompletion agvInfo) {
        try {
            String agvCode = agvInfo.getAgvCode();
            FromToRecord currentTask = agvInfo.getCurrentTask();

            // 获取AGV当前工作流的全局变量
            CapacityCalculationResult result = calculateAgvCapacityLikeInsertTask(agvCode, currentTask);

            if (result == null) {
                log.debug("AGV[{}] 无法获取容量计算结果", agvCode);
                return false;
            }

            // 🔥 简化：直接使用 freeSize，因为已经包含了所有 pending 任务的占用
            int freeSize = result.getFreeSize();
            boolean hasCapacity = freeSize > 0;

            log.debug("AGV[{}] 容量检查: 可用储位={}, pending任务占用={}, totalSize={}, 有空余容量={}",
                     agvCode, freeSize, -result.getOperatorVal(), result.getTotalSize(), hasCapacity);

            return hasCapacity;
        } catch (Exception e) {
            log.error("检查AGV[{}]容量时发生异常", agvInfo.getAgvCode(), e);
            return false;
        }
    }

    /**
     * 🔥 重构：直接统计 FromToRecord 中 pending 且车辆编码匹配的个数作为储位占用量
     * 简化容量计算逻辑，避免复杂的 TaskNode 分析
     */
    private CapacityCalculationResult calculateAgvCapacityLikeInsertTask(String agvCode, FromToRecord currentTask) {
        try {
            // 1. 获取 AGV 的总储位数量
            List<AgvStoragePort> agvStoragePortList = agvStoragePortService.getAllList(agvCode);
            int totalSize = agvStoragePortList.size();

            // 2. 统计该 AGV 的所有 pending 任务数量（包括正在执行和等待合单的）
            int pendingTaskCount = (int) fromToRecordService.count(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getIsPendingMerge, true)
                    .eq(FromToRecord::getPendingMergeTargetAgv, agvCode)
                        .notIn(FromToRecord::getStatus,
                                FromToRecordStatusEnum.COMPLETED,
                                FromToRecordStatusEnum.CANCEL// 派车中
                              )     // 放料中

            );

            // 3. 计算可用储位：总储位 - pending 任务占用的储位
            int freeSize = totalSize - pendingTaskCount;

            // 4. 对于正在执行的任务，pickUpCount 设为 0（因为已经在 pending 统计中）
            // 这里不需要复杂的 TaskNode 分析，直接基于 FromToRecord 的 pending 状态
            int pickUpCount = 0;

            // 5. operatorVal 表示已完成的储位变化，这里设为负的 pending 数量
            int operatorVal = -pendingTaskCount;

            log.debug("AGV[{}] 简化容量计算：总储位={}, pending任务={}, 可用储位={}",
                     agvCode, totalSize, pendingTaskCount, freeSize);

            return new CapacityCalculationResult(freeSize, pickUpCount, totalSize, operatorVal, pendingTaskCount);

        } catch (Exception e) {
            log.error("计算AGV[{}]容量时发生异常", agvCode, e);
            return null;
        }
    }








    /**
     * 判断当前任务是否处于取料阶段
     * 包含正在执行中的状态和关键的完成状态（合单触发时机）
     */
    private boolean isInPickupPhase(FromToRecordStatusEnum status) {
        switch (status) {
            case PICKUP_NAVIGATING:      // 取料导航中
            case ARRIVE_PICKUP_POSITION: // 到达取料位置
            case PICK_UP_ING:            // 取料中
            case PICK_UP_FINISHED:       // 取料完成 - 重要：合单的关键时机
                return true; // 取料阶段
            case PUT_DOWN_NAVIGATING:    // 放料导航中
            case ARRIVE_DROP_POSITION:   // 到达放料位置
            case PUT_DOWN_ING:           // 放料中
            case PUT_DOWN_FINISHED:      // 放料完成 - 重要：合单的关键时机
                return false; // 放料阶段
            default:
                // 对于其他状态（如 NOT_START, DISPATCHING, COMPLETED），保守处理
                log.debug("处理非核心状态: {}", status);
                return true; // 保守默认为取料阶段
        }
    }

    /**
     * 容量计算结果
     */
    private static class CapacityCalculationResult {
        private final int freeSize;
        private final int pickUpCount;
        private final int totalSize;
        private final int operatorVal;
        private final int pendingCount;  // 🔥 新增：直接封装 pending 任务数量

        public CapacityCalculationResult(int freeSize, int pickUpCount, int totalSize, int operatorVal, int pendingCount) {
            this.freeSize = freeSize;
            this.pickUpCount = pickUpCount;
            this.totalSize = totalSize;
            this.operatorVal = operatorVal;
            this.pendingCount = pendingCount;
        }

        public int getFreeSize() {
            return freeSize;
        }

        public int getPickUpCount() {
            return pickUpCount;
        }

        public int getTotalSize() {
            return totalSize;
        }

        public int getOperatorVal() {
            return operatorVal;
        }

        public int getPendingCount() {
            return pendingCount;
        }
    }

    /**
     * 查找可以与指定AGV合单的候选任务
     * 按 commandId 分组，使用与 InsertTaskComponent 一致的选择逻辑
     */
    private List<FromToRecord> findMergeableCandidatesForAgv(AgvNearingCompletion agvInfo,
                                                            List<FromToRecord> candidates) {
        FromToRecord currentTask = agvInfo.getCurrentTask();

        try {
            // 1. 获取系统配置的合并模式
            Set<MergeMode> configuredModes = getConfiguredMergeModes();
            if (configuredModes.isEmpty()) {
                log.debug("没有配置合并模式");
                return new ArrayList<>();
            }

            // 2. 设置合并标识
            for (FromToRecord candidate : candidates) {
                setMergeKeys(candidate);
            }
            setMergeKeys(currentTask);

            // 3. 创建模拟的 InsertTaskParams
            InsertTaskParams mockParams = createMockInsertTaskParams(agvInfo, currentTask, configuredModes);

            // 4. 使用 MergeMode.groupRecords 进行分组（与 InsertTaskComponent 完全一致）
            MergeMode.GroupResult result = MergeMode.groupRecords(candidates, configuredModes, mockParams);

            // 5. 选择优先级最高的分组（模拟 selectHighestPriorityGroup 逻辑）
            List<FromToRecord> selectedGroup = selectHighestPriorityGroup(result, mockParams, agvInfo);

            if (!selectedGroup.isEmpty()) {
                log.debug("AGV[{}] 选择了包含 {} 个任务的合单分组",
                         agvInfo.getAgvCode(), selectedGroup.size());

                // 按 commandId 分组显示详细信息
                Map<String, Long> commandIdCounts = selectedGroup.stream()
                    .collect(Collectors.groupingBy(FromToRecord::getCommandId, Collectors.counting()));
                log.debug("选中的分组详情: {}", commandIdCounts);
            }

            return selectedGroup;

        } catch (Exception e) {
            log.error("查找 AGV[{}] 的合单候选任务时发生异常", agvInfo.getAgvCode(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 选择优先级最高的分组
     * 与 InsertTaskComponent 完全一致：按 MergeMode 优先级选择，然后在该模式内选择任务优先级最高的分组
     */
    private List<FromToRecord> selectHighestPriorityGroup(MergeMode.GroupResult result, InsertTaskParams mockParams, AgvNearingCompletion agvInfo) {
        Map<MergeMode, List<List<FromToRecord>>> grouped = result.getGrouped();

        if (grouped.isEmpty()) {
            return new ArrayList<>();
        }

        // 按照MergeMode的ordinal()从小到大进行迭代（与 InsertTaskService 完全一致）
        List<Map.Entry<MergeMode, List<List<FromToRecord>>>> sortedEntries = grouped.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.comparingInt(Enum::ordinal)))
                .collect(Collectors.toList());

        for (Map.Entry<MergeMode, List<List<FromToRecord>>> entry : sortedEntries) {
            MergeMode mode = entry.getKey();
            List<List<FromToRecord>> groups = entry.getValue();

            if (CollectionUtils.isEmpty(groups)) {
                continue;
            }

            // 检查取料阶段的模式限制（与 InsertTaskService 一致）
            int ordinal = mode.ordinal();
            boolean isSame = ordinal <= 2;
            if (!isSame && mockParams.isFroms()) {
                continue; // 取料只适用前三种合并模式
            }

            // 在该 MergeMode 内选择包含优先级最高的FromToRecord的分组
            List<FromToRecord> selectedGroup = selectHighestPriorityGroupLikeInsertTask(groups);

            if (CollectionUtils.isNotEmpty(selectedGroup)) {
                // 验证选中分组的容量是否足够
                if (validateGroupCapacity(selectedGroup, mockParams, agvInfo)) {
                    log.debug("选择了 MergeMode[{}] 中的优先级最高分组，包含{}个任务",
                             mode, selectedGroup.size());
                    return selectedGroup;
                } else {
                    log.debug("MergeMode[{}] 的分组容量不足，尝试下一个模式", mode);
                }
            }
        }

        log.debug("所有 MergeMode 分组都不满足条件，返回空列表");
        return new ArrayList<>();
    }

    /**
     * 验证分组的容量是否足够
     * 使用与 InsertTaskComponent 完全一致的容量计算逻辑
     */
    private boolean validateGroupCapacity(List<FromToRecord> group, InsertTaskParams mockParams, AgvNearingCompletion agvInfo) {
        try {
            // 获取 AGV 的真实容量信息
            CapacityCalculationResult currentCapacity = calculateAgvCapacityLikeInsertTask(
                agvInfo.getAgvCode(), agvInfo.getCurrentTask());

            if (currentCapacity == null) {
                log.debug("无法获取 AGV[{}] 的容量信息", agvInfo.getAgvCode());
                return false;
            }

            // 🔥 简化：直接使用分组任务数量，因为每个 FromToRecord 占用一个储位
            int groupTaskCount = group.size();

            // 直接使用 freeSize，因为已经包含了所有 pending 任务的占用
            int availableCapacity = currentCapacity.getFreeSize();

            // 验证分组是否能放入可用容量
            boolean hasEnoughCapacity = availableCapacity > 0 && groupTaskCount <= availableCapacity;

            log.debug("分组容量验证: AGV[{}] 可用储位={}, pending任务占用={}, 分组需求={}, 总容量={}, 结果={}",
                     agvInfo.getAgvCode(), availableCapacity, -currentCapacity.getOperatorVal(),
                     groupTaskCount, currentCapacity.getTotalSize(), hasEnoughCapacity);

            return hasEnoughCapacity;

        } catch (Exception e) {
            log.error("验证分组容量时发生异常", e);
            return false;
        }
    }

    /**
     * 计算分组中实际的取料任务数量
     * 通过 TaskNode 获取准确的取料操作数量
     */
    private int calculateGroupPickUpCount(List<FromToRecord> group) {
        int totalPickUpCount = 0;

        // 按 commandId 分组，避免重复计算
        Map<String, List<FromToRecord>> groupByCommandId = group.stream()
            .collect(Collectors.groupingBy(FromToRecord::getCommandId));

        for (Map.Entry<String, List<FromToRecord>> entry : groupByCommandId.entrySet()) {
            String commandId = entry.getKey();

            try {
                // 获取该 commandId 的 TaskNode 信息
                List<TaskNode> taskNodes = taskNodeService.list(
                    Wrappers.lambdaQuery(TaskNode.class)
                        .eq(TaskNode::getTaskCode, commandId)
                        .orderByAsc(TaskNode::getRecordSeq));

                // 统计取料操作数量
                int commandPickUpCount = (int) taskNodes.stream()
                    .filter(node -> TaskNodeAction.PICK_UP.equals(node.getActionType()))
                    .count();

                totalPickUpCount += commandPickUpCount;

                log.debug("CommandId[{}] 取料任务数量: {}", commandId, commandPickUpCount);

            } catch (Exception e) {
                log.error("计算 commandId[{}] 取料数量时发生异常", commandId, e);
                // 异常时使用保守估计：假设每个 FromToRecord 对应一个取料操作
                totalPickUpCount += entry.getValue().size();
            }
        }

        log.debug("分组总取料任务数量: {}", totalPickUpCount);
        return totalPickUpCount;
    }



    /**
     * 创建模拟的 InsertTaskParams 用于测试合单
     */
    private InsertTaskParams createMockInsertTaskParams(AgvNearingCompletion agvInfo, FromToRecord currentTask,
                                                       Set<MergeMode> configuredModes) {
        InsertTaskParams params = new InsertTaskParams();

        // 获取当前 AGV 的容量状态
        CapacityCalculationResult capacity = calculateAgvCapacityLikeInsertTask(
            agvInfo.getAgvCode(), currentTask);

        if (capacity != null) {
            // 🔥 简化：直接使用 freeSize，因为已经包含了所有 pending 任务的占用
            params.setFreeStorageSlotsCount(Math.max(0, capacity.getFreeSize()));
            log.debug("AGV[{}] 模拟参数容量：可用储位={}, pending任务占用={}",
                     agvInfo.getAgvCode(), capacity.getFreeSize(), -capacity.getOperatorVal());
        } else {
            // 回退到简单的容量计算
            params.setFreeStorageSlotsCount(agvInfo.getTotalCapacity() - agvInfo.getOccupiedStorageCount());
            log.debug("AGV[{}] 模拟参数容量（简单计算）：总容量={}, 占用={}, 可用={}",
                     agvInfo.getAgvCode(), agvInfo.getTotalCapacity(), agvInfo.getOccupiedStorageCount(),
                     agvInfo.getTotalCapacity() - agvInfo.getOccupiedStorageCount());
        }

        params.setAgvCode(agvInfo.getAgvCode());
        params.setConfiguredModes(configuredModes);

        // 设置合并标识
        if (currentTask.getFromMergeKey() != null) {
            params.setFromMergeCode(currentTask.getFromMergeKey());
        }
        if (currentTask.getToMergeKey() != null) {
            params.setToMergeCode(currentTask.getToMergeKey());
        }

        // 根据当前任务状态判断是取料还是放料阶段，设置对应的 actionType
        boolean isFroms = isInPickupPhase(currentTask.getStatus());
        if (isFroms) {
            params.setActionType(TaskNodeAction.PICK_UP.name());
        } else {
            params.setActionType(TaskNodeAction.PUT_DOWN.name());
        }

        return params;
    }



    /**
     * 检查两个任务是否可以根据合单规则进行合并
     * 与 InsertTaskComponent 保持一致，使用系统配置的合并模式
     */
    private boolean canMergeByRules(FromToRecord existingTask, FromToRecord candidateTask) {
        try {
            // 获取系统配置的合并模式
            Set<MergeMode> configuredModes = getConfiguredMergeModes();
            if (configuredModes.isEmpty()) {
                log.debug("没有配置合并模式，跳过合单规则检查");
                return false;
            }

            // 设置合并标识
            setMergeKeys(existingTask);
            setMergeKeys(candidateTask);

            // 检查每个配置的合并模式
            for (MergeMode mode : configuredModes) {
                if (mode.isMatch(existingTask, candidateTask)) {
                    log.debug("任务[{}]和[{}]匹配合并模式[{}]",
                             existingTask.getCommandId(), candidateTask.getCommandId(), mode);
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查合单规则时发生异常", e);
            return false;
        }
    }

    /**
     * 获取系统配置的合并模式
     * 与 InsertTaskServiceImpl.getMergeModes() 保持一致
     */
    private Set<MergeMode> getConfiguredMergeModes() {
        Set<MergeMode> mergeModes = new HashSet<>();
        String mergeModesStr = sysConfigService.selectConfigValueByKey(AGV_ORDER_MERGE_RULE);
        log.debug("从SysConfig中获取合并模式: {}", mergeModesStr);

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(mergeModesStr)) {
            try {
                // 解析合并模式字符串，格式为逗号分隔的枚举序数
                String[] modeOrdinals = mergeModesStr.split(",");
                MergeMode[] allModes = MergeMode.values();

                for (String ordinalStr : modeOrdinals) {
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(ordinalStr)) {
                        try {
                            int ordinal = Integer.parseInt(ordinalStr.trim());
                            if (ordinal >= 0 && ordinal < allModes.length) {
                                mergeModes.add(allModes[ordinal]);
                            } else {
                                log.warn("无效的合并模式序数: {}", ordinal);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无效的合并模式序数字符串: {}", ordinalStr, e);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析合并模式字符串时发生异常: {}", mergeModesStr, e);
            }
        }

        return mergeModes;
    }

    /**
     * 为 FromToRecord 设置合并标识
     */
    private void setMergeKeys(FromToRecord record) {
        if (record.getFromMergeKey() == null) {
            Port fromPort = portService.getByCode(record.getFromCode());
            if (fromPort != null) {
                record.setFromMergeKey(fromPort.getMergeCode());
            }
        }

        if (record.getToMergeKey() == null) {
            Port toPort = portService.getByCode(record.getToCode());
            if (toPort != null) {
                record.setToMergeKey(toPort.getMergeCode());
            }
        }
    }

    /**
     * 标记任务为合单候选
     */
    private void markTasksAsPendingMerge(List<PendingMergeCandidate> candidates) {
        LocalDateTime now = LocalDateTime.now();
        List<FromToRecord> tasksToUpdate = new ArrayList<>();

        for (PendingMergeCandidate candidate : candidates) {
            FromToRecord task = candidate.getTask();
            task.setPendingMergeTime(now);
            task.setIsPendingMerge(true);
            task.setPendingMergeTargetAgv(candidate.getTargetAgvCode());
            tasksToUpdate.add(task);
        }

        fromToRecordService.updateBatchById(tasksToUpdate);

        // 按目标AGV分组显示日志
        Map<String, List<String>> tasksByAgv = candidates.stream()
            .collect(Collectors.groupingBy(
                PendingMergeCandidate::getTargetAgvCode,
                Collectors.mapping(
                    candidate -> candidate.getTask().getCommandId(),
                    Collectors.toList())));

        log.info("标记{}个任务为合单候选，按目标AGV分组：{}", candidates.size(), tasksByAgv);
    }

    /**
     * 清理过期的延迟分配
     */
    private void cleanupExpiredPendingMerge() {
        try {
//            LocalDateTime expireTime = LocalDateTime.now().minus(
//                dispatchConfig.getPendingMergeTimeoutSeconds(), ChronoUnit.SECONDS);
//
//            // 🔥 修复：只清理真正过期的待分配任务，不清理已分配的任务
//            List<FromToRecord> expiredTasks = fromToRecordService.list(
//                Wrappers.lambdaQuery(FromToRecord.class)
//                    .eq(FromToRecord::getIsPendingMerge, true)
//                    .lt(FromToRecord::getPendingMergeTime, expireTime)
//                    .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START)
//                    .isNull(FromToRecord::getFlowId));
//
//            if (!expiredTasks.isEmpty()) {
//                for (FromToRecord task : expiredTasks) {
//                    task.setIsPendingMerge(false);
//                    task.setPendingMergeTime(null);
//                    task.setPendingMergeTargetAgv(null); // 清理目标AGV字段
//                }
//                fromToRecordService.updateBatchById(expiredTasks);
//
//                log.info("清理了{}个过期的合单候选任务", expiredTasks.size());
//            }

            // 清理主任务被取消的延迟分配
            cleanupCancelledTaskPendingMerge();

            // 清理目标AGV离线的延迟分配
            cleanupOfflineAgvPendingMerge();

        } catch (Exception e) {
            log.error("清理过期合单候选任务时发生异常", e);
        }
    }

    /**
     * 清理主任务被取消的延迟分配
     */
    private void cleanupCancelledTaskPendingMerge() {
        try {
            // 查找目标AGV的主任务已被取消或完成的延迟分配任务
            List<FromToRecord> pendingTasks = fromToRecordService.list(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getIsPendingMerge, true)
                    .isNotNull(FromToRecord::getPendingMergeTargetAgv)
                    .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START));

            List<FromToRecord> tasksToCleanup = new ArrayList<>();

            for (FromToRecord pendingTask : pendingTasks) {
                String targetAgvCode = pendingTask.getPendingMergeTargetAgv();

                // 检查目标AGV是否还有正在执行的任务
                boolean hasActiveTask = fromToRecordService.count(
                    Wrappers.lambdaQuery(FromToRecord.class)
                        .eq(FromToRecord::getAgvCode, targetAgvCode)
                        .in(FromToRecord::getStatus,
                            FromToRecordStatusEnum.PICKUP_NAVIGATING,
                            FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION,
                            FromToRecordStatusEnum.PICK_UP_ING,
                            FromToRecordStatusEnum.PICK_UP_FINISHED,
                            FromToRecordStatusEnum.PUT_DOWN_NAVIGATING,
                            FromToRecordStatusEnum.ARRIVE_DROP_POSITION,
                            FromToRecordStatusEnum.PUT_DOWN_ING,
                            FromToRecordStatusEnum.PUT_DOWN_FINISHED)) > 0;

                if (!hasActiveTask) {
                    tasksToCleanup.add(pendingTask);
                    log.debug("目标AGV[{}]没有活跃任务，清理相关延迟分配任务[{}]",
                             targetAgvCode, pendingTask.getCommandId());
                }
            }

            if (!tasksToCleanup.isEmpty()) {
                clearPendingMergeFlags(tasksToCleanup, "目标AGV无活跃任务");
            }

        } catch (Exception e) {
            log.error("清理已取消任务的延迟分配时发生异常", e);
        }
    }

    /**
     * 清理目标AGV离线的延迟分配
     */
    private void cleanupOfflineAgvPendingMerge() {
        try {
            // 获取所有有延迟分配任务的目标AGV
            List<String> targetAgvCodes = fromToRecordService.list(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getIsPendingMerge, true)
                    .isNotNull(FromToRecord::getPendingMergeTargetAgv)
                    .select(FromToRecord::getPendingMergeTargetAgv))
                .stream()
                .map(FromToRecord::getPendingMergeTargetAgv)
                .distinct()
                .collect(Collectors.toList());

            List<FromToRecord> tasksToCleanup = new ArrayList<>();

            for (String agvCode : targetAgvCodes) {
                // 检查AGV是否在线
                boolean isAgvOnline = checkAgvOnlineStatus(agvCode);

                if (!isAgvOnline) {
                    // 获取该离线AGV的所有延迟分配任务
                    List<FromToRecord> offlineAgvTasks = fromToRecordService.list(
                        Wrappers.lambdaQuery(FromToRecord.class)
                            .eq(FromToRecord::getIsPendingMerge, true)
                            .eq(FromToRecord::getPendingMergeTargetAgv, agvCode)
                            .eq(FromToRecord::getStatus, FromToRecordStatusEnum.NOT_START));

                    tasksToCleanup.addAll(offlineAgvTasks);
                    log.debug("目标AGV[{}]离线，清理{}个相关延迟分配任务", agvCode, offlineAgvTasks.size());
                }
            }

            if (!tasksToCleanup.isEmpty()) {
                clearPendingMergeFlags(tasksToCleanup, "目标AGV离线");
            }

        } catch (Exception e) {
            log.error("清理离线AGV的延迟分配时发生异常", e);
        }
    }

    /**
     * 检查AGV在线状态
     */
    private boolean checkAgvOnlineStatus(String agvCode) {
        try {
            // 简化实现：如果AGV有活跃任务，认为在线
            return fromToRecordService.count(
                Wrappers.lambdaQuery(FromToRecord.class)
                    .eq(FromToRecord::getAgvCode, agvCode)
                    .in(FromToRecord::getStatus,
                        FromToRecordStatusEnum.PICKUP_NAVIGATING,
                        FromToRecordStatusEnum.ARRIVE_PICKUP_POSITION,
                        FromToRecordStatusEnum.PICK_UP_ING,
                        FromToRecordStatusEnum.PICK_UP_FINISHED,
                        FromToRecordStatusEnum.PUT_DOWN_NAVIGATING,
                        FromToRecordStatusEnum.ARRIVE_DROP_POSITION,
                        FromToRecordStatusEnum.PUT_DOWN_ING,
                        FromToRecordStatusEnum.PUT_DOWN_FINISHED)) > 0;
        } catch (Exception e) {
            log.error("检查AGV[{}]在线状态时发生异常", agvCode, e);
            return true; // 异常时保守处理，认为在线
        }
    }

    /**
     * 清理延迟分配标记
     */
    private void clearPendingMergeFlags(List<FromToRecord> tasks, String reason) {
        for (FromToRecord task : tasks) {
            task.setIsPendingMerge(false);
            task.setPendingMergeTime(null);
            task.setPendingMergeTargetAgv(null);
        }
        fromToRecordService.updateBatchById(tasks);

        log.info("清理{}个延迟分配任务（原因：{}）", tasks.size(), reason);
    }

    /**
     * 选择包含优先级最高的FromToRecord的分组
     * 与 InsertTaskServiceImpl.selectHighestPriorityGroup 完全一致的逻辑
     */
    private List<FromToRecord> selectHighestPriorityGroupLikeInsertTask(List<List<FromToRecord>> groups) {
        if (CollectionUtils.isEmpty(groups)) {
            return new ArrayList<>();
        }

        // 如果只有一个分组，直接返回
        if (groups.size() == 1) {
            return groups.get(0);
        }

        // 收集所有已分组的记录，并找到其中优先级最高的
        FromToRecord highestPriorityInGroups = null;
        List<FromToRecord> targetGroup = null;

        for (List<FromToRecord> group : groups) {
            for (FromToRecord record : group) {
                if (highestPriorityInGroups == null || isHigherPriority(record, highestPriorityInGroups)) {
                    highestPriorityInGroups = record;
                    targetGroup = group;
                }
            }
        }

        if (targetGroup != null) {
            log.debug("找到已分组中优先级最高的记录: commandId={}, priority={}, createTime={}, 所在分组大小: {}",
                    highestPriorityInGroups.getCommandId(),
                    highestPriorityInGroups.getPriority(),
                    highestPriorityInGroups.getCreateTime(),
                    targetGroup.size());
            return targetGroup;
        }

        // 如果没有找到，返回第0个分组
        log.debug("未找到已分组中的优先级最高记录，使用第0个分组，分组大小: {}", groups.get(0).size());
        return groups.get(0);
    }

    /**
     * 判断record1的优先级是否高于record2
     * 与 InsertTaskServiceImpl.isHigherPriority 完全一致的优先级规则
     * 优先级规则：priority降序，createTime升序
     */
    private boolean isHigherPriority(FromToRecord record1, FromToRecord record2) {
        if (record1 == null) return false;
        if (record2 == null) return true;

        // 先比较优先级（数值越大优先级越高）
        int priorityCompare = Integer.compare(record1.getPriority(), record2.getPriority());
        if (priorityCompare != 0) {
            return priorityCompare > 0; // record1优先级更高
        }

        // 优先级相同时，比较创建时间（时间越早优先级越高）
        if (record1.getCreateTime() != null && record2.getCreateTime() != null) {
            return record1.getCreateTime().before(record2.getCreateTime());
        }

        return false;
    }

    /**
     * AGV即将完成信息
     */
    private static class AgvNearingCompletion {
        private String agvCode;
        private FromToRecord currentTask;
        private int totalCapacity;
        private int occupiedStorageCount; // 已占用储位数量，不是任务数量
        private int availableCapacity; // 实际可用储位数量，已扣除 pending 任务
        private int pendingTaskCount; // 已 pending 给该 AGV 的任务数量

        public String getAgvCode() {
            return agvCode;
        }

        public void setAgvCode(String agvCode) {
            this.agvCode = agvCode;
        }

        public FromToRecord getCurrentTask() {
            return currentTask;
        }

        public void setCurrentTask(FromToRecord currentTask) {
            this.currentTask = currentTask;
        }

        public int getTotalCapacity() {
            return totalCapacity;
        }

        public void setTotalCapacity(int totalCapacity) {
            this.totalCapacity = totalCapacity;
        }

        public int getOccupiedStorageCount() {
            return occupiedStorageCount;
        }

        public void setOccupiedStorageCount(int occupiedStorageCount) {
            this.occupiedStorageCount = occupiedStorageCount;
        }

        // 兼容性方法：保持原有接口，但名称更清晰
        @Deprecated
        public int getCurrentTaskCount() {
            return occupiedStorageCount;
        }

        @Deprecated
        public void setCurrentTaskCount(int currentTaskCount) {
            this.occupiedStorageCount = currentTaskCount;
        }

        public int getAvailableCapacity() {
            return availableCapacity;
        }

        public void setAvailableCapacity(int availableCapacity) {
            this.availableCapacity = availableCapacity;
        }

        public int getPendingTaskCount() {
            return pendingTaskCount;
        }

        public void setPendingTaskCount(int pendingTaskCount) {
            this.pendingTaskCount = pendingTaskCount;
        }
    }

    /**
     * 合单候选信息
     * 包含任务和目标AGV信息
     */
    private static class PendingMergeCandidate {
        private final FromToRecord task;
        private final String targetAgvCode;

        public PendingMergeCandidate(FromToRecord task, String targetAgvCode) {
            this.task = task;
            this.targetAgvCode = targetAgvCode;
        }

        public FromToRecord getTask() {
            return task;
        }

        public String getTargetAgvCode() {
            return targetAgvCode;
        }
    }

    /**
     * 处理Local任务
     * 参考HOST任务的分配逻辑，按commandId分组处理，使用工作流创建
     */
    private void processLocalTasks(Map<String, List<FromToRecord>> localCommandGroups, List<AgvDTO> freeAgvs) {
        try {
            log.info("开始处理{}个Local任务命令组", localCommandGroups.size());

            // 按优先级排序commandId（参考HOST任务的排序逻辑）
            List<String> sortedCommandIds = sortCommandIdsByPriority(
                new ArrayList<>(localCommandGroups.keySet()), localCommandGroups);

            log.info("Local任务排序完成，处理顺序: {}", sortedCommandIds);

            // 按排序后的顺序处理每个commandId
            for (String commandId : sortedCommandIds) {
                List<FromToRecord> commandTasks = localCommandGroups.get(commandId);
                if (commandTasks != null && !commandTasks.isEmpty()) {
                    // 检查该 commandId 是否应该被 pending 住（延迟分配）
                    if (shouldPendCommandId(commandId, commandTasks)) {
                        log.info("【延迟分配】commandId[{}] 符合 pending 条件，暂不分配AGV", commandId);
                        continue; // 跳过当前 commandId，不进行 AGV 分配
                    }
                    processLocalCommandGroup(commandId, commandTasks, freeAgvs);
                }
            }

        } catch (Exception e) {
            log.error("处理Local任务时发生异常", e);
        }
    }

    /**
     * 处理单个Local commandId组的任务
     * 参考HOST任务的processCommandGroup逻辑
     */
    private void processLocalCommandGroup(String commandId, List<FromToRecord> commandTasks, List<AgvDTO> freeAgvs) {
        log.info("开始处理Local commandId[{}]的{}个任务", commandId, commandTasks.size());

        try {
            // 获取第一个任务来确定AGV分配策略
            FromToRecord firstTask = commandTasks.get(0);
            List<String> taskAvailableAgvs = agvAllocationService.getAvailableAgvsForTask(firstTask);

            // 与空闲AGV求交集
            List<AgvDTO> availableAgvs = freeAgvs.stream()
                    .filter(agv -> taskAvailableAgvs.isEmpty() || taskAvailableAgvs.contains(agv.getAgvCode()))
                    .collect(Collectors.toList());

            if (!availableAgvs.isEmpty()) {
                // 将整个commandId组作为原子单位处理（与HOST任务一致）
                log.info("【Local派车成功】commandId[{}]分配到{}个AGV", commandId, availableAgvs.size());
                processLocalCommandWithAgvs(commandId, commandTasks, availableAgvs);
            } else {
                // 记录派车失败
                log.warn("【Local派车失败】commandId[{}]无可用AGV - 任务指定AGV: {}, 可用范围: {}",
                        commandId, firstTask.getAgvCode(),
                        taskAvailableAgvs.isEmpty() ? "无限制" : taskAvailableAgvs);
            }

        } catch (Exception e) {
            log.error("处理Local commandId[{}]时发生异常", commandId, e);
        }
    }

    /**
     * 处理单个Local commandId组与可用AGV
     * 参考HOST任务的processCommandWithAgvs逻辑，使用相同的工作流创建机制
     */
    private void processLocalCommandWithAgvs(String commandId, List<FromToRecord> commandTasks, List<AgvDTO> availableAgvs) {
        try {
            log.info("开始为Local commandId[{}]创建工作流，包含{}个任务", commandId, commandTasks.size());

            // 批量验证点位并创建记录（与HOST任务完全一致的逻辑）
            List<S66F3CommandHandler.Location> froms = new ArrayList<>();
            List<S66F3CommandHandler.Location> tos = new ArrayList<>();
            int index = 0;

            // 按formToSeq顺序处理每个任务
            for (FromToRecord task : commandTasks) {
                Port from = portService.getByCode(task.getFromCode());
                Assert.notNull(from, "点位[{}]不存在", task.getFromCode());
                Port to = portService.getByCode(task.getToCode());
                Assert.notNull(to, "点位[{}]不存在", task.getToCode());

                // 使用传入的carrierId，如果为空则生成随机字符串
                String lotId = StrUtil.isNotBlank(task.getCarrierId()) ? task.getCarrierId() : "";

                S66F3CommandHandler.Location locationFrom = new S66F3CommandHandler.Location(
                        from.getMapCode(),
                        from.getActionParam().get(0).getMarkerCode(),
                        true, task.getIsLastInCommand() != null ? task.getIsLastInCommand() : true, commandId, commandId, from.getId(), from.getPortType(),
                        from.getCode(), lotId, TaskNodeAction.PICK_UP, from.getCode(),
                        to.getCode(), task.getFormToSeq());
                locationFrom.setIndex(index++);

                S66F3CommandHandler.Location locationTo = new S66F3CommandHandler.Location(
                        to.getMapCode(),
                        to.getActionParam().get(0).getMarkerCode(),
                        true, task.getIsLastInCommand() != null ? task.getIsLastInCommand() : true, commandId, commandId, to.getId(), to.getPortType(),
                        to.getCode(), lotId, TaskNodeAction.PUT_DOWN, from.getCode(),
                        to.getCode(), task.getFormToSeq());
                locationTo.setIndex(index++);

                froms.add(locationFrom);
                tos.add(locationTo);
            }

            // 转换AGV列表为代码集合
            Set<String> availableAgvCodes = availableAgvs.stream()
                    .map(AgvDTO::getAgvCode)
                    .collect(Collectors.toSet());

            // 创建工作流 - 使用与HOST任务相同的服务
            Pair<String, String> workFlow = insertTaskService.createWorkFlow(commandTasks, commandId, froms, tos, availableAgvCodes);
            String workFlowId = workFlow.getKey();
            String allocatedAgv = workFlow.getValue();

            if (StringUtils.isNotBlank(workFlowId)) {
                // 成功创建工作流 - 更新整个commandId下的所有任务
                fromToRecordService.update(Wrappers.lambdaUpdate(FromToRecord.class)
                        .eq(FromToRecord::getCommandId, commandId)
                        .set(FromToRecord::getFlowId, workFlowId)
                        .set(FromToRecord::getAgvCode, allocatedAgv)
                        .set(FromToRecord::getStatus, FromToRecordStatusEnum.DISPATCHING));

                // 将分配的AGV代码放入缓存
                BizConstants.AGV_CODE_CACHE.put(allocatedAgv, commandId);

                log.info("成功创建Local工作流，CommandId[{}], WorkFlowId[{}], 分配AGV[{}], 包含{}个任务",
                        commandId, workFlowId, allocatedAgv, commandTasks.size());
            } else {
                log.error("创建Local工作流失败，CommandId[{}]", commandId);
            }

        } catch (Exception e) {
            log.error("为Local commandId[{}]创建工作流时发生异常", commandId, e);
        }
    }


}
