package com.youibot.tms.biz.forest.dto;

import com.youibot.tms.biz.api.request.TaskStatisticsRequest;
import com.youibot.tms.common.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <NAME_EMAIL> on 2023/2/17.
 *
 * <AUTHOR>
 * @date 2023/2/17 14:37
 */
@ApiModel(value = "MissionWorkStatistic", description = "Fleet任务数据统计")
@Data
public class MissionWorkStatisticDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "统计时间")
    private String dateTime;
    @ApiModelProperty(value = "统计时间内的数据列表")
    private List<MissionWorkStatistic> missionWorkStatistics;

    public String getDateTimeByType(TaskStatisticsRequest.DateType type) {
        if(StringUtils.isNotEmpty(dateTime)) {
            if (TaskStatisticsRequest.DateType.DAY.equals(type) || TaskStatisticsRequest.DateType.MONTH.equals(type)) {
                return dateTime.substring(0,10);//只截取前10位，包含"yyyy-MM-dd"，不要后面的时分秒
            }
        }
        return dateTime;
    }

    @Data
    @ApiModel(value = "MissionWorkStatistic", description = "Fleet任务数据")
    public static class MissionWorkStatistic implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 机器人编号
         */
        @ApiModelProperty(value = "机器人编号")
        private String agvCode;
        /**
         * 任务次数
         */
        @ApiModelProperty(value = "任务次数")
        private Integer workNum;
        /**
         * 任务时长
         */
        @ApiModelProperty(value = "任务时长")
        private Long workTime;
        /**
         * 充电次数
         */
        @ApiModelProperty(value = "充电次数")
        private Integer chargeNum;
        /**
         * 充电时长
         */
        @ApiModelProperty(value = "充电时长")
        private Long chargeTime;
        /**
         * 泊车次数
         */
        @ApiModelProperty(value = "泊车次数")
        private Integer parkNum;
        /**
         * 泊车时长
         */
        @ApiModelProperty(value = "泊车时长")
        private Long parkTime;
        /**
         * 异常次数
         */
        @ApiModelProperty(value = "异常次数")
        private Integer errorNum;
        /**
         * 异常时长
         */
        @ApiModelProperty(value = "异常时长")
        private Long errorTime;
        /**
         * 空闲时长
         */
        @ApiModelProperty(value = "空闲时长")
        private Long freeTime;
        /**
         * 离线时长
         */
        @ApiModelProperty(value = "离线时长")
        private Long offlineTime;
        /**
         * 未激活时间
         */
        @ApiModelProperty("未激活时间")
        private Long notActiveTime;

    }
}
