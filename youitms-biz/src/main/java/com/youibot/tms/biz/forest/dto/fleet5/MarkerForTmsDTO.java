package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 标记点
 * 类名称：Marker
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "MarkerForTmsDTO", description = "标记点")
@NoArgsConstructor
@AllArgsConstructor
public class MarkerForTmsDTO extends MapElement{

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    @ApiModelProperty(value = "类型 ChargingMarker:充电点, NavigationMarker:导航点, WorkMarker:工作点", position = 3)
    private String type;

    @ApiModelProperty(value = "地图编码", position = 4)
    private String vehicleMapCode;

}
