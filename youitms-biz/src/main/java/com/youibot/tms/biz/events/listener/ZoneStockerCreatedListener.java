package com.youibot.tms.biz.events.listener;

import com.youibot.tms.biz.entity.PortStocker;
import com.youibot.tms.biz.service.PortStockerService;
import com.youibot.tms.common.core.domain.entity.ZoneStocker;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.system.events.ZoneStockerCreatedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ZoneStockerCreatedListener implements ApplicationListener<ZoneStockerCreatedEvent> {

    @Resource
    private PortStockerService portStockerService;


    /**
     * 分隔符
     */
    private final static String SPLIT = "-";

    @Override
    public void onApplicationEvent(ZoneStockerCreatedEvent event) {
        ZoneStocker zoneStocker = event.getEntity();
        if (ToolUtil.isNotEmpty(zoneStocker)) {
            log.info("Get ZoneStockerCreatedEvent,entity.id:{}", event.getEntity().getId());
            Long numX = zoneStocker.getNumX();
            Long numY = zoneStocker.getNumY();
            Long numZ = zoneStocker.getNumZ();
            if (numX != null && numY != null && numZ != null
                    && numX > 0 && numY > 0 && numZ > 0) {



                for (long x = 1L; x <= numX; x++) {
                    for (long y = 1L; y <= numY; y++) {
                        for (long z = 1L; z <= numZ; z++) {
                            String autoName = zoneStocker.getCode() + SPLIT + x + SPLIT + y+ SPLIT + z;
                            PortStocker port = new PortStocker();
                            port.setCode(autoName);
                            port.setName(autoName);
                            port.setEnabled(true);
                            port.setZoneStockerId(zoneStocker.getId());
                            portStockerService.save(port);
                            log.info("生成库位并保存成功：{}", autoName);
                        }
                    }
                }


            }

        }
    }
}
