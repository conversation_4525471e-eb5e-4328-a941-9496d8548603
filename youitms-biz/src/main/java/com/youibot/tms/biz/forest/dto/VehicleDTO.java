package com.youibot.tms.biz.forest.dto;

import com.youibot.tms.biz.forest.enums.VehicleStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Data
@ApiModel("在线机器人")
public class VehicleDTO {

    private static final double POWER_FULL = 100;


    /**
     * 对应FleetAgvDTO中的agvCode
     */
    @ApiModelProperty(value = "Agv ID")
    private String id;


//    @ApiModelProperty(value = "agvCode")
//    private String agvCode;

    @ApiModelProperty(value = "名称", position = 1)
    private String name;

    @ApiModelProperty(value = "AGV地图ID", position = 2)
    private String agvMapId;

    @ApiModelProperty(value = "连接时间", position = 3)
    private Date connectedTime;

    @ApiModelProperty(value = "地图状态  0、未同步  1、已同步", position = 4)
    protected Integer mapStatus;

    @ApiModelProperty(value = "地图状态  0、未定位  1、已定位", position = 5)
    protected Integer locationStatus;

    @ApiModelProperty(value = "任务状态 1、空闲 2、任务 3、充电 4、归位", position = 7)
    protected Integer workStatus;

    @ApiModelProperty(value = "异常状态 1、无异常 2、任务异常 3、充电异常 4、归位异常", position = 8)
    protected Integer abnormalStatus;

    @ApiModelProperty(value = "控制模式 1、自动模式 2、手动模式 3、录制模式", position = 9)
    private Integer controlMode;

    @ApiModelProperty(value = "导航类型 LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航", position = 10)
    private String navigationType;

//    @ApiModelProperty(value = "外型信息", position = 11)
//    private transient ShapeInfo shapeInfo;
//
//    @ApiModelProperty(value = "状态信息", position = 13)
//    private DefaultVehicleStatusDTO defaultVehicleStatus;

    @ApiModelProperty(value = "错误信息(目前只提示智能充电/归位的报错信息)", position = 14)
    private String errorMessage;


    @ApiModelProperty(value = "在线状态：0:离线 1:在线", position = 19)
    private Integer onlineStatus;


    @ApiModelProperty(value = "机器人类型", position = 20)
    protected String agvType;


    @ApiModelProperty(value = "机器人IP地址", position = 20)
    protected String agvIp;

    @ApiModelProperty(value = "机器人端口号", position = 20)
    protected String agvPort;

    @ApiModelProperty(value = "启用状态 0：未启用 1：启用", position = 21)
    private Integer status;


    @ApiModelProperty(value = "分配状态 0：未分配 1：已分配", position = 22)
    private Integer allocateStatus;

    @ApiModelProperty(value = "tms 对该机器人的分配状态: 0 或者null ,未分配 ,1 : 已分配", position = 22)
    protected Integer tmsAssiginStatus;

    @ApiModelProperty(value = "距离某个markId 的顺序,距离越小值,距离越近", position = 22)
    protected Integer sortIndexToMarkId;

    @ApiModelProperty(value = "厢位数量")
    protected Integer locNum;

    @Getter(value = AccessLevel.NONE)
    @ApiModelProperty(value = "当前机器人的电量", position = 22)
    private Double currentBattery;

    @Setter
    @Getter
    @ApiModelProperty(value = "机器人分组ID", position = 22)
    private String agvGroupId;

    @Setter
    @Getter
    @ApiModelProperty(value = "机器人分组名称", position = 22)
    private String agvGroupName;

    @Setter
    @Getter
    @ApiModelProperty(value = "机器人状态信息", position = 22)
    protected DefaultVehicleStatus defaultVehicleStatus;


    public VehicleStatus getVehicleStatus(){
        VehicleStatus agvStatusEnum = null;
        Integer workStatus = this.getWorkStatus();
        // 是否故障或离线
        if (!AGVConstant.ONLINE.equals(this.getOnlineStatus())) {
            agvStatusEnum = VehicleStatus.OFFLINE;
        } else if (!AGVConstant.ABNORMAL_STATUS_NO.equals(this.getAbnormalStatus())) {
            agvStatusEnum = VehicleStatus.ERROR;
        }
        if (agvStatusEnum == null) {
            switch (workStatus) {
                case 1:
                    agvStatusEnum = VehicleStatus.IDLE;
                    break;
                case 2:
                case 4:
                    agvStatusEnum = VehicleStatus.RUN;
                    break;
                case 3:
                    agvStatusEnum = VehicleStatus.CHARGE;
                    break;
                default:
                    break;
            }
        }
        // null 表示未知状态
        return agvStatusEnum;
    }

    public Double getBattery() {
        if(this.getDefaultVehicleStatus()!=null && this.getDefaultVehicleStatus().getBattery()!=null){
            return this.getDefaultVehicleStatus().getBattery().getBattery_value();
        }else{
            return 0.0;
        }
    }
}
