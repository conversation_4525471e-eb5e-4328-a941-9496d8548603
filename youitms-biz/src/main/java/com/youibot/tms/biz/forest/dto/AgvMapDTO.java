package com.youibot.tms.biz.forest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 地图
 * 类名称：Map
 * 创建时间：2019年4月2日 下午3:35:52
 */
@Data
@ApiModel(value = "AGVMap", description = "地图")
public class AgvMapDTO {

    @ApiModelProperty(value = "ID", position = 0)
    private String id;

    
    @ApiModelProperty(value = "类型：LASER_MAP：激光地图，VIRTUAL_MAP：虚拟地图，CAD_MAP：CAD地图", position = 1)
    private String type;

    
    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    
    @ApiModelProperty(value = "描述", position = 3)
    private String description;

    
    @ApiModelProperty(value = "地图图片数据", position = 4)
    private String mapData;

    
    @ApiModelProperty(value = "中心x坐标", position = 5)
    private Double originX;

    
    @ApiModelProperty(value = "中心y坐标", position = 6)
    private Double originY;

    
    @ApiModelProperty(value = "分辨率", position = 7)
    private Double resolution;

    
    @ApiModelProperty(value = "像素高度", position = 8)
    private Double height;//像素高度

    
    @ApiModelProperty(value = "像素宽度", position = 9)
    private Double width;//像素宽度

    
    @ApiModelProperty(value = "物理高度 单位：m", position = 10)
    private Double physicsHeight;

    
    @ApiModelProperty(value = "物理宽度   单位：m", position = 11)
    private Double physicsWidth;

    
    @ApiModelProperty(value = "角度", position = 12)
    private Double originYaw;//角度

    
    @ApiModelProperty(value = "版本号", position = 13)
    private Integer version;

    
    @ApiModelProperty(value = "使用状态 ENABLE:启用  DISABLE:禁用", position = 14)
    private String usageStatus;

    
    @ApiModelProperty(value = "所在楼层数, 默认是1楼", position = 15)
    private Integer floor;

    
    @ApiModelProperty(value = "创建时间", position = 16)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    
    @ApiModelProperty(value = "更新时间", position = 17)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

   
    @ApiModelProperty(value = "是否叠加版本号", position = 18)
    private transient Boolean versionIncrease = true;

    @ApiModelProperty(value = "图层版本号", position = 19)
    private Integer layerVersion;

    @ApiModelProperty(value = "特征点中心点数据", position = 20)
    private String featureMapOrigin;

    @ApiModelProperty(value = "特征地图数据", position = 21)
    private String featureMapData;

}
