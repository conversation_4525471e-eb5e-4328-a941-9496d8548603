package com.youibot.tms.biz.forest.dto.fleet5;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Fleet系统日志分页查询结果DTO
 * 对应Fleet系统 /tms/page 接口返回数据
 * Created by Augment Agent on 2024/12/19.
 */
@Data
@ApiModel(value = "FleetLogPageDTO", description = "Fleet系统日志分页查询结果")
public class FleetLogPageDTO implements Serializable {

    @ApiModelProperty(value = "日志记录列表")
    private List<FleetLogDTO> list;

    @ApiModelProperty(value = "总记录数")
    private Integer total;

    @ApiModelProperty(value = "当前页码")
    private Integer page;

    @ApiModelProperty(value = "每页大小")
    private Integer limit;

    /**
     * Fleet系统日志记录DTO
     * 对应Fleet的SysLogDTO结构
     */
    @Data
    @ApiModel(value = "FleetLogDTO", description = "Fleet系统日志记录")
    public static class FleetLogDTO implements Serializable {

        @ApiModelProperty(value = "日志ID")
        private Long id;

        @ApiModelProperty(value = "创建者")
        private Long creator;

        @ApiModelProperty(value = "创建时间")
        private String createDate;

        @ApiModelProperty(value = "类型 运行日志：Running/告警日志：Warning/错误日志：Error")
        private String type;

        @ApiModelProperty(value = "功能模块")
        private String module;

        @ApiModelProperty(value = "日志内容")
        private String content;

        @ApiModelProperty(value = "日志数据")
        private String data;

        @ApiModelProperty(value = "报文")
        private String message;

        @ApiModelProperty(value = "机器人，多个之间用英文逗号分隔")
        private String vehicleCodes;

        @ApiModelProperty(value = "任务编号，多个之间用英文逗号分隔")
        private String taskNos;

        @ApiModelProperty(value = "节点编码")
        private String nodeCode;

        @ApiModelProperty(value = "同样的日志最后一次更新的时间")
        private String lastTime;

        // 为了兼容性，保留一些常用字段的别名
        @ApiModelProperty(value = "机器人编号（从vehicleCodes中提取第一个）")
        public String getVehicleCode() {
            if (vehicleCodes != null && !vehicleCodes.isEmpty()) {
                return vehicleCodes.split(",")[0];
            }
            return null;
        }

        @ApiModelProperty(value = "任务编号（从taskNos中提取第一个）")
        public String getTaskNo() {
            if (taskNos != null && !taskNos.isEmpty()) {
                return taskNos.split(",")[0];
            }
            return null;
        }

        @ApiModelProperty(value = "创建时间（别名）")
        public String getCreateTime() {
            return createDate;
        }

        @ApiModelProperty(value = "更新时间（别名）")
        public String getUpdateTime() {
            return lastTime;
        }

        @ApiModelProperty(value = "异常描述（从content获取）")
        public String getDescription() {
            return content;
        }

        @ApiModelProperty(value = "异常编码（暂时返回null，Fleet日志中没有此字段）")
        public Integer getCode() {
            return null;
        }

        @ApiModelProperty(value = "位置X坐标（暂时返回null，Fleet日志中没有此字段）")
        public Double getPositionX() {
            return null;
        }

        @ApiModelProperty(value = "位置Y坐标（暂时返回null，Fleet日志中没有此字段）")
        public Double getPositionY() {
            return null;
        }

        @ApiModelProperty(value = "状态（暂时返回null，Fleet日志中没有此字段）")
        public Integer getStatus() {
            return null;
        }

        @ApiModelProperty(value = "解决方案（暂时返回null，Fleet日志中没有此字段）")
        public String getSolution() {
            return null;
        }

        @ApiModelProperty(value = "地图编码（暂时返回null，Fleet日志中没有此字段）")
        public String getMapCode() {
            return null;
        }
    }
}
