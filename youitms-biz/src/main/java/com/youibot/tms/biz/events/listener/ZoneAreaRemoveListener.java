package com.youibot.tms.biz.events.listener;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youibot.tms.biz.common.exception.BizErrorCode;
import com.youibot.tms.biz.entity.PortEq;
import com.youibot.tms.biz.mapper.PortEqMapper;
import com.youibot.tms.common.core.domain.entity.ZoneArea;
import com.youibot.tms.common.exception.base.BusinessException;
import com.youibot.tms.system.events.ZoneAreaRemoveEvent;
import com.youibot.tms.system.mapper.ZoneAreaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ZoneAreaRemoveListener implements ApplicationListener<ZoneAreaRemoveEvent> {

    @Resource
    private PortEqMapper portEqMapper;

    @Resource
    private ZoneAreaMapper zoneAreaMapper;

    @Override
    public void onApplicationEvent(ZoneAreaRemoveEvent event) {
        ZoneArea entity = event.getEntity();
        if (entity != null) {
            QueryWrapper<PortEq> wrapper = new QueryWrapper<>();
            wrapper.eq("zone_area_id",entity.getId());
            long count = portEqMapper.selectCount(wrapper);
            if(count >0){
                throw new BusinessException(BizErrorCode.ZONE_AREA_BIND_PORT,entity.getName());
            }else{
                zoneAreaMapper.deleteById(entity.getId());
            }
        }
    }
}
