package com.youibot.agv.scheduler.mapper;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AGVMapperTest {

	@Autowired
	private AGVMapper agvMapper;
	
	@Test
	public void test() {
		
		System.out.println(" agv: "  + agvMapper );
		System.out.println(" agv: "  + agvMapper );
		System.out.println(" agv: "  + agvMapper );
		System.out.println(" agv: "  + agvMapper );
		int updateEnable = agvMapper.updateEnable( 1 );
		
		System.out.println(" agv: "  + updateEnable );
		System.out.println(" agv: "  + updateEnable );
		System.out.println(" agv: "  + updateEnable );
	}

}
