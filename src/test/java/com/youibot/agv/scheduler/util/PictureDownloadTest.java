package com.youibot.agv.scheduler.util;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/8/5 19:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PictureDownloadTest {

    @Test
    public void test() throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault(); //1、创建实例
        HttpGet httpGet = new HttpGet("http://localhost:8080/api/v1/pictures?url=/image/1.jpeg"); //2、创建请求
        CloseableHttpResponse response = httpClient.execute(httpGet); //3、执行
        HttpEntity httpEntity = response.getEntity(); //4、获取实体
        if (httpEntity != null) {
            InputStream in = httpEntity.getContent();
            OutputStream out = new FileOutputStream("D://image//success.png");
            int len;
            byte[] buffer = new byte[10240];
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        }
        //关闭资源
        response.close();
        httpClient.close();
    }

}
