package com.youibot.agv.scheduler.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;

/**
 * @version V1.0
 * @Title:
 * @ClassName: PACKAGE_NAME
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/4/24 11:46
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class JsonTest {

    @Test
    public void test() {

        String jsonStr = "{\"data\":{\"header\":{\"stamp\":14328999,\"frameId\":\"base_laser_scan\"},\"grid_phits\":[{\"x\":111,\"y\":111}]},\"return_code\":123,\"error_message\":\"aaa\"}";
        HashMap map = JSON.parseObject(jsonStr, HashMap.class);
        for (Object key : map.keySet()) {
            System.out.println(key + " class:" + key.getClass());
            System.out.println(map.get(key) + " class:" + map.get(key).getClass());
        }

        JSONObject jsonObject = (JSONObject)map.get("data");
        HashMap newMap = JSON.parseObject(jsonObject.toJSONString(),HashMap.class);
        System.out.println(newMap);
        System.out.println(newMap.get("header").getClass());


        System.out.println(map);
        Object obj = JSON.parse(jsonStr);

    }
}
