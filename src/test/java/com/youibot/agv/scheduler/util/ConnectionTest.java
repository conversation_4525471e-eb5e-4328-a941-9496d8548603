package com.youibot.agv.scheduler.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.service.MissionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Random;

/**
 * @version V1.0
 * @Title:
 * @ClassName: PACKAGE_NAME
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/4/24 11:46
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class ConnectionTest {

    @Autowired
    private MissionService missionService;

    @Test
    public void test() throws InterruptedException {
        int threadCount = 200;
        for (int i = 0; i <= threadCount; i++) {
            Thread thread = new ConnectionThread();
            thread.start();
        }
        Thread.sleep(10000000);
    }

    class ConnectionThread extends Thread {
        @Override
        public void run() {
//            super.start();
            int i = 0;
            int count = new Random().nextInt(100);
            while (i < count) {
                System.out.println(missionService.findAll());
                try {
                    Thread.sleep(1000);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                i++;
            }
        }
    }
}
