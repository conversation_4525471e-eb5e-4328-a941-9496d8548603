package com.youibot.agv.scheduler.util;

import static org.junit.Assert.*;

import java.io.IOException;

import org.apache.commons.net.ftp.FTPClient;
import org.junit.Test;

public class FtpUtilsTest {

	@Test
	public void testGetConnectClientStringIntStringString() {

		String ftpUrl = "172.31.173.143";
		int ftpPort = 21;
		String userName = "ftp";
		String pwd = "123456";

		try {
			FTPClient client = FtpUtils.getConnectClient( ftpUrl, ftpPort, userName , pwd);
			int list = client.list();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

}
