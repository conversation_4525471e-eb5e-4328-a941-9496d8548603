package com.youibot.agv.scheduler.util;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/6/22 14:02
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class Base64UtilsTest {

    @Test
    public void base64ToImageTest() throws IOException {
        String imgFile = "D:\\image\\1.jpg";
        String base64Str = Base64Utils.imageToBase64(imgFile);
        System.out.println("-------->" + base64Str);
        Base64Utils.base64ToImage(base64Str, new File("").getCanonicalPath() + File.separator + AGVPropertiesUtils.getString("IMAGE_SAVE_PATH.CAMERA_FOLDER"), "2.jpg");
    }

    @Test
    public void imageToBase64Test() throws IOException {
//        String imgFile = "D:\\image\\1.jpg";
//        String base64Str = Base64Utils.imageToBase64(imgFile);
//        System.out.println("-------->" + base64Str);

        File file = new File("");
        String filePath = file.getCanonicalPath();
        System.out.println(filePath);

        String property = System.getProperty("user.dir");
        System.out.println(property);
    }
}
