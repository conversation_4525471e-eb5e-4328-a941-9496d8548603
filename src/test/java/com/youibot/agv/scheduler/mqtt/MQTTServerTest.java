//package com.youibot.agv.scheduler.mqtt;
//
//import org.eclipse.paho.client.mqttv3.*;
//import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
//
///**
// * @Author：yangpeilin
// * @Date: 2020/7/17 21:21
// */
//public class MQTTServerTest {
//
//    public static final String HOST = "tcp://localhost:11883";
//
//    public static final String TOPIC = "/YOUIFleet/+";
//    public static final String RECIVE_TOPIC = "YOUICompass";
//    private static final String clientid = "YOUIFleet_server";
//
//    private MqttClient client;
//    private MqttTopic topic;
//    private String userName = "admin";
//    private String passWord = "public";
//
//    private MqttMessage message;
//
//    public MQTTServerTest() throws MqttException {
//        //MemoryPersistence设置clientid的保存形式，默认为以内存保存
//        client = new MqttClient(HOST, clientid, new MemoryPersistence());
//        connect();
//    }
//
//    private void connect() {
//        MqttConnectOptions options = new MqttConnectOptions();
//        options.setCleanSession(false);
//        options.setUserName(userName);
//        options.setPassword(passWord.toCharArray());
//        // 设置超时时间
//        options.setConnectionTimeout(10);
//        // 设置会话心跳时间
//        options.setKeepAliveInterval(20);
//        try {
//            // 订阅
//            client.subscribe(RECIVE_TOPIC);
//            client.setCallback(new MessageStatusCallback());
//            client.connect(options);
//            topic = client.getTopic(TOPIC);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    public void publish(MqttMessage message) throws MqttPersistenceException, MqttException {
//        MqttDeliveryToken token = topic.publish(message);
//        token.waitForCompletion();
//        System.out.println(token.isComplete() + "========");
//    }
//
//    private static void sendMsg() throws MqttException {
//        MQTTServerTest server = new MQTTServerTest();
//        server.message = new MqttMessage();
//        server.message.setQos(2);
//        server.message.setRetained(true);
//        server.message.setPayload("YOUIFleet start server push message...5".getBytes());
//        server.publish(server.message);
//        System.out.println(server.message.isRetained() + "------ratained状态");
//    }
//
//
//}
