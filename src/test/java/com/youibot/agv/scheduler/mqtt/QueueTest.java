//package com.youibot.agv.scheduler.mqtt;
//
//import com.youibot.agv.scheduler.mqtt.thread.MqVehicleStatusThread;
//import com.youibot.agv.scheduler.service.*;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class QueueTest {
//
//    @Autowired
//    private AGVMapService agvMapService;
//
//    @Autowired
//    private MarkerService markerService;
//
//    @Autowired
//    private SidePathService sidePathService;
//
//    @Autowired
//    private MapAreaService mapAreaService;
//
//    @Autowired
//    private MarkerAndDockingPointService markerAndDockingPointService;
//
//    @Autowired
//    private PathService pathService;
//
//    @Autowired
//    private DockingPointService dockingPointService;
//
//    @Autowired
//    private DataCodeMatrixService dataCodeMatrixService;
//
//    @Autowired
//    private RecordPathService recordPathService;
//
//    @Autowired
//    private MqVehicleStatusThread mqVehicleStatusThread;
//
//    @Test
//    public void Test(){
//        for (int i = 1; i <= 5; i++) {
//            MyThread myThread = new MyThread(i+".线程");
//            myThread.start();
//        }
//        mqVehicleStatusThread.start();
//        while (true){
//
//        }
//    }
//
//    class MyThread extends Thread {
//
//        public MyThread(String threadName) {
//            this.setName(threadName);
//        }
//
//        @Override
//        public void run() {
////            for (int i = 0; i < 10000; i++) {
////                MqMessageConstant.linkedList.offer(this.getName()+"---"+i);
////                System.out.println("---------size:" + MqMessageConstant.linkedList.size());
////            }
//        }
//    }
//}
