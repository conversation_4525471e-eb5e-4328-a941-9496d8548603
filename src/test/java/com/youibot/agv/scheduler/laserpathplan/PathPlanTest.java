package com.youibot.agv.scheduler.laserpathplan;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedEdge;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DStarLiteAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DStartLiteManager;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DijkstraAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.engine.pathplan.util.SidePathUtils;
import com.youibot.agv.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.service.SidePathService;
import lombok.Data;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.zip.DeflaterOutputStream;

import static com.youibot.agv.scheduler.constant.MapConstant.*;
import static com.youibot.agv.scheduler.constant.PathPlanConstant.MINUS_WEIGHT;
import static com.youibot.agv.scheduler.constant.PathPlanConstant.PLUS_WEIGHT;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 下午1:38 2020/8/14
 * @Description :
 * @Modified By :
 * @Version :
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PathPlanTest {
    private static final Logger logger = LoggerFactory.getLogger(PathPlanTest.class);

    //private static MarkerService markerService = (MarkerService) ApplicationUtils.getBean("markerServiceImpl");
    @Autowired
    private MarkerService markerService;
    @Autowired
    private PathService pathService;
    @Autowired
    private SidePathService sidePathService;
    @Autowired
    private AGVMapService agvMapService;
    private Random random = new Random(System.currentTimeMillis());

    @Value("${PATH_PLAN.MAX_DERAILMENT_DISTANCE:10}")
    private Double maxDerailmentDistance;

    private DStartLiteManager pathPlanManager = DStartLiteManager.getInstance();
    
    @Test
    public void dijkStraAlgorithmTest() throws Exception {
        String agvMapId1 = "56a7372a-c6b7-11ea-b5a6-0242ac110002";
        MapGraphUtil.addAGVMap(agvMapId1);
        DirectedGraph dGraph = MapGraphUtil.getCloneDirectedGraph();
        DijkstraAlgorithm dijkstraAlgorithm = new DijkstraAlgorithm("Test", dGraph);
        String startMarkerId = "83";
        String goalMarkerId = "61";
        long startTime = System.currentTimeMillis();
        MarkerPathResult path = dijkstraAlgorithm.plan(startMarkerId, goalMarkerId);
        long costTime = System.currentTimeMillis() - startTime;
        printPath(path);
        System.out.println("路径规划计算耗时[" + costTime + "]ms");
    }

    @Test
    public void dijkStraAlgorithmTest22() throws Exception {
        String agvMapId1 = "FAB_10";
        MapGraphUtil.addAGVMap(agvMapId1);
//        DirectedGraph dGraph = MapGraphUtil.getCloneDirectedGraph();
        DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();
        String startMarkerId = "dbffbbcf-acd5-485d-8d90-b9b748a81aa9";
        String goalMarkerId = "ca235344-e340-4b43-baa7-e05e09df5c4e";
        long startTime = System.currentTimeMillis();
        String agvCode ="AAGVA9";
//        pathPlanManager.plan(agvCode, dGraph, startMarkerId, goalMarkerId, false);
        MarkerPathResult path = pathPlanManager.plan(agvCode, originDirectedGraph, startMarkerId, goalMarkerId, false);
        long costTime = System.currentTimeMillis() - startTime;
        printPath(path);
        System.out.println("路径规划计算耗时[" + costTime + "]ms");
    }
    
    
    class ModifyWeight extends Thread {
        private DirectedGraph graph;
        private List<Marker> activeMarkers;
        private List<SidePath> sidePaths;

        public ModifyWeight(DirectedGraph graph, List<Marker> activeMarkers, List<SidePath> sidePaths) {
            this.graph = graph;
            this.activeMarkers = activeMarkers;
            this.sidePaths = sidePaths;
        }

        @Override
        public void run() {
            try {
                Map<DirectedEdge, Double> directedEdgeDoubleHashMap = Maps.newHashMap();
                while (true) {
                    Thread.sleep(1);
                    for (Map.Entry<DirectedEdge, Double> edgeDoubleEntry : directedEdgeDoubleHashMap.entrySet()) {
                        DirectedEdge edge = edgeDoubleEntry.getKey();
                        Double value = edgeDoubleEntry.getValue();
                        if (edge != null) {
                            edge.modifyAutoWeight(MINUS_WEIGHT, value);
                        }
                    }

                    directedEdgeDoubleHashMap.clear();
                    int count = random.nextInt(65535) % 300;
                    for (int i = 0; i < count; i++) {
                        int index = random.nextInt(65535) % sidePaths.size();
                        double value = random.nextInt(65535) % 100;
                        SidePath sidePath = sidePaths.get(index);
                        DirectedEdge edge = graph.getEdgeBySidePath(sidePath);
                        if (edge != null) {
                            logger.debug(edge.toString());
                            edge.modifyAutoWeight(PLUS_WEIGHT, value);
                            directedEdgeDoubleHashMap.put(edge, value);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error(LogExceptionStackUtil.LogExceptionStack(e));
            }
        }
    }

    @Test
    public void test() throws Exception {
        String agvMapId1 = "1c7f01a0-dae6-11ea-8f9c-b00cd1656a83";
        //String agvMapId2 = "b31db7cf-0b83-11eb-b005-0242ac110002";
        MapGraphUtil.addAGVMap(agvMapId1);
        //MapGraphUtil.addAGVMap(agvMapId2);
        //MapGraphUtil.removeAGVMap(agvMapId);
        DirectedGraph cloneDirectedGraph = MapGraphUtil.getCloneDirectedGraph();
        DStarLiteAlgorithm DStarLiteAlgorithm = new DStarLiteAlgorithm("Test", cloneDirectedGraph);
        //String startMarkerId = markerService.selectMarkerByCode(startMarkerCode).getId();
        //String goalMarkerId = markerService.selectMarkerByCode(goalMarkerCode).getId();
        List<Marker> activeMarkers = new ArrayList<>(MapGraphUtil.getActiveMarkers());
        List<SidePath> activeSidePaths = new ArrayList<>(MapGraphUtil.getActiveSidePaths());
        ModifyWeight modifyWeightThread = new ModifyWeight(cloneDirectedGraph, activeMarkers, activeSidePaths);
        modifyWeightThread.start();

        int size = activeMarkers.size();
        while (true) {
            Thread.sleep(1);
            int i = random.nextInt(65535) % size;
            int j = random.nextInt(65535) % size;
            MarkerPathResult plan = DStarLiteAlgorithm.plan(activeMarkers.get(i).getId(), activeMarkers.get(j).getId());
            if (!plan.isSuccess()) {
                logger.error("error of path plan");
            } else {
                logger.debug(plan.toString());
            }
        }

        //String startMarkerId = "16";
        //long startTime = System.currentTimeMillis();
        //MarkerPathResult path = DStarLiteAlgorithm.plan(startMarkerId, goalMarkerId);
        //printPath(path);
        //long costTime = System.currentTimeMillis() - startTime;
        //System.out.println("路径规划计算耗时[" + costTime + "]ms");
    }

    private void printPath(MarkerPathResult markerPathResult) {
        List<String> temp = new LinkedList<>();
        for (String markerId : markerPathResult.markerPath) {
//            String code = markerService.selectById(markerId).getCode();
//            temp.add(code);
        }
        System.out.println(temp.toString());
    }

    //@Test
    public AGVMap createAgvMap(Double height, Double width) throws Exception {
        AGVMap agvMap = new AGVMap();
//        agvMap.setOriginX(0D);
//        agvMap.setOriginY(0D);
//        agvMap.setOriginYaw(0D);
//        agvMap.setPhysicsHeight(height);
//        agvMap.setPhysicsWidth(width);
//        agvMap.setType(MAP_TYPE_LASER);
//        agvMap.setName("ThomasTest1");
//        agvMap.setResolution(0.03D);
//        agvMap.setUsageStatus(MAP_USAGE_STATUS_DISABLE);
//        agvMap.setFloor(1);
//        agvMapService.insert(agvMap);
        return agvMap;
    }

    @Test
    public void createAGVMapAndData() throws Exception {
        int height = 15;
        int width = 20;
        //AGVMap agvMap = createAgvMap((double) (height + 2), (double) (width + 2));
        //generateMapData(agvMap.getId(), width, height);
        String agvMapId = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
        generateMapData(agvMapId, width, height);
    }

    //@Test
    public void generateMapData(String agvMapId, Integer m, Integer n) throws Exception {
        //String agvMapId = "16fdb199-36a4-11eb-a0a1-e0d55ef82298";
        //Integer m = 20;
        //Integer n = 20;
        Integer first_marker_code = null;
        Set<Marker> markers = new HashSet<>();
        for (int j = 1; j <= n; j++) {
            for (int i = 1; i <= m; i++) {
                Marker marker = new Marker();
                //Integer startIndex = (j-1)*m+i;
                marker.setAngle(0D);
                marker.setAgvMapName(agvMapId);
                marker.setType(MARKER_TYPE_NAVIGATION);
                marker.setUsageStatus(MARKER_USAGE_STATUS_ENABLE);
                marker.setX((double) i);
                marker.setY((double) j);
                markerService.insert(marker);
                if (first_marker_code == null) {
                    first_marker_code = Integer.valueOf(marker.getCode());
                }
                markers.add(marker);
            }
        }
        for (int j = 1; j <= n; j++) {
            for (int i = 1; i <= m; i++) {
                Integer start = first_marker_code + (j - 1) * m + i - 1;
                Integer end = start + m;
                insertSidePath(start, end, agvMapId, markers);
                if ((start - first_marker_code + 1) % m != 0) {
                    end = start + 1;
                    insertSidePath(start, end, agvMapId, markers);
                }
            }
        }


    }

    public void insertSidePath(Integer start, Integer end, String agvMapId, Set<Marker> markers) {
        //Marker startMarker = markerService.selectMarkerByCode(start.toString());
        //Marker endMarker = markerService.selectMarkerByCode(end.toString());
        Marker startMarker = null;
        Marker endMarker = null;
        for (Marker marker : markers) {
            if (marker.getCode().equals(start.toString())) {
                startMarker = marker;
            }
            if (marker.getCode().equals(end.toString())) {
                endMarker = marker;
            }
        }
        if (startMarker == null || endMarker == null) {
            return;
        }
        double x1 = startMarker.getX();
        double y1 = startMarker.getY();
        double x2 = endMarker.getX();
        double y2 = endMarker.getY();
        double r = 1.0 / 3;
        double x = (1 - r) * x1 + r * x2;
        double y = (1 - r) * y1 + r * y2;
        MyPosition myPosition = new MyPosition(x, y);
        String startControl = JSON.toJSONString(myPosition);
        r = 2.0 / 3;
        x = (1 - r) * x1 + r * x2;
        y = (1 - r) * y1 + r * y2;
        myPosition.setX(x);
        myPosition.setY(y);
        String endControl = JSON.toJSONString(myPosition);
        Path path = new Path();
        path.setStartMarkerId(startMarker.getId());
        path.setEndMarkerId(endMarker.getId());
        path.setStartControl(startControl);
        path.setEndControl(endControl);
        path.setAgvMapName(agvMapId);
        path.setDirection(PATH_DIRECTION_TWO_WAY);
        path.setUsageStatus(PATH_USAGE_STATUS_ENABLE);
        path.setLineType(PATH_LINE_TYPE_CURVE);
        path.setLength(1D);
        path.setForwardAgvDirection(1);
        path.setReverseAgvDirection(1);
        pathService.insert(path);
    }

    @Data
    public class MyPosition {
        private Double x;
        private Double y;

        MyPosition(Double x, Double y) {
            this.x = x;
            this.y = y;
        }
    }

    @Test
    public void generateSidePathAngle() {
        String agvMapId = "56a7372a-c6b7-11ea-b5a6-0242ac110002";
        List<SidePath> sidePaths = sidePathService.selectByAGVMapId(agvMapId,false);
        for (SidePath sidePath : sidePaths) {
            Double[] inOutAngle = SidePathUtils.getInOutAngle(sidePath,false);
            sidePath.setInAngle(inOutAngle[0]);
            sidePath.setOutAngle(inOutAngle[1]);
//            sidePathService.update(sidePath);
        }
    }

    @Test
    public void checkMarkerDistance() {
        String agvMapId = "119c850d-b8d5-11e9-9cef-7cd30a6810d3";

//        List<SidePath> sidePathList = sidePathService.selectByAGVMapId(agvMapId);
//        for (SidePath sidePath : sidePathList) {
//            Marker startMarker = markerService.selectById(sidePath.getStartMarkerId());
//            Marker endMarker = markerService.selectById(sidePath.getEndMarkerId());
//            Point startPoint = new Point(startMarker.getX(), startMarker.getY());
//            Point endPoint = new Point(endMarker.getX(), endMarker.getY());
//            Double distance = startPoint.minus(endPoint).getNorm();
//            if (distance <= 2 * maxDerailmentDistance) {
//                logger.debug("startMarkerCode:{},endMarkerCode:{}", startMarker.getCode(), endMarker.getCode());
//            }
//        }
    }

    @Test
    public void changePathDirection() {
        String agvMapId = "ab77df42-36a5-11eb-a0a1-e0d55ef82298";
//        List<Path> paths = pathService.selectByAGVMapId(agvMapId);
//        for (Path path : paths) {
//            path.setDirection(PATH_DIRECTION_TWO_WAY);
//            path.setLineType(PATH_LINE_TYPE_STRAIGHT);
//            pathService.update(path);
//        }

    }
}
