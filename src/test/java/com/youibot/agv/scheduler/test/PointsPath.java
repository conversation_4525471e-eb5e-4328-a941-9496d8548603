package com.youibot.agv.scheduler.test;

import java.util.ArrayList;
import java.util.List;

import com.google.common.collect.Lists;

/**
 * @Annotion: 从 一个点 到另一个点所有路径走法 ,并输出环
 * @ClassName: TwoPointsPath
 * @Author:
 * @Date: 2019/9/29 9:54
 * @Version: 1.0
 */
public class PointsPath {
	/** 当前路径 */
	private  List<String> nowPath = new ArrayList<>();

	/**
	 * 环
	 */
	private  List<List<String>> cycle = new ArrayList<>();
	
	/**
	 * 通路
	 */
	private  List<List<String>> path = new ArrayList<>();

	/**
	 *
	 * @param nodeList
	 * @param source   起始节点
	 * @param target   目标节点
	 */
	public   void findAllPath(List< Node> nodeList, String source, String target) {
		if (nowPath.contains(source)) {
//			System.out.println("这是一个环:" + nowPath);
			cycle.add( Lists.newArrayList( nowPath )) ;
			nowPath.remove(nowPath.size() - 1);
			return;
		}
		for (int i = 0; i < nodeList.size(); i++) {
			Node node = (Node) nodeList.get(i);
			if (node.getSource().equals(source)) {
				nowPath.add(node.getSource());
				if (node.getTarget().equals(target)) {
					nowPath.add(node.getTarget());
//					System.out.println("这是一条路径:" + nowPath);
					path.add(  Lists.newArrayList( nowPath )) ;
					/* 因为添加了终点路径,所以要返回两次 */
					nowPath.remove(nowPath.size() - 1);
					nowPath.remove(nowPath.size() - 1);
					/* 已经找到路径,返回上层找其他路径 */
					continue;
				}
				findAllPath(nodeList, node.getTarget(), target);
			}
		}
		/* 如果找不到下个节点,返回上层 */
		if (nowPath.size() > 0) {
			nowPath.remove(nowPath.size() - 1);
		}

	}

	/**
	 * 测试
	 */
	public static void main(String[] args) {
		
		PointsPath pointsPath = new PointsPath();
		List<Node> list = new ArrayList<>();
		list.add(new Node("1", "2"));
		list.add(new Node("1", "3"));
		list.add(new Node("2", "4"));
		list.add(new Node("4", "6"));
		list.add(new Node("4", "3"));
		list.add(new Node("3", "2"));
		list.add(new Node("3", "5"));
		list.add(new Node("5", "6"));

		pointsPath.findAllPath(list, "1", "5");
		
		System.out.println("cycle:");
		System.out.println(pointsPath.cycle);
		System.out.println("path:");
		System.out.println(pointsPath.path );
	}

}
