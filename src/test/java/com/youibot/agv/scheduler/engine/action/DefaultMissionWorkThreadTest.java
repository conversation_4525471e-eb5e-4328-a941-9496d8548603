package com.youibot.agv.scheduler.engine.action;

import com.youibot.agv.scheduler.listener.event.MissionWorkStartEvent;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

/**
* <AUTHOR>  E-mail:<EMAIL>
* @version CreateTime:  
*/
@RunWith(SpringRunner.class)
@SpringBootTest
public class DefaultMissionWorkThreadTest {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void test() throws InterruptedException {
        Thread.sleep(10000);
//        DefaultMissionWorkThread thread = new DefaultMissionWorkThread();
//        MissionWork missionWork = missionWorkService.selectById("0012bd5c-8288-11e9-a8cc-b0416f03e8c3");
//        DefaultVehicle vehicle = (DefaultVehicle) this.defaultVehiclePool.getVehicle("b0c1d9ec-03b9-43ed-b7a7-6ca05c367250");
//        vehicle.setMissionWork(missionWork);
//        vehicle.setWorkStatus(WORK_STATUS_RUNNING);
//        thread.start(vehicle);
        applicationContext.publishEvent(new MissionWorkStartEvent("Agv status observer push"));


        Thread.sleep(10000000);
    }
}
