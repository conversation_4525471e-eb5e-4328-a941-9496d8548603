package com.youibot.agv.scheduler.engine.thread;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeansException;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-04-11 19:52
 */


@RunWith(SpringRunner.class)
@SpringBootTest
public class AgvManagerThreadExecutorTest implements ApplicationContextAware {

    private ApplicationContext context = null;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
    }

    @Test
    public void test() throws InterruptedException {
        int threadNumber = 1;
        while (threadNumber < 10) {
            /*AGVManageThreadExecutor thread = context.getBean(AGVManageThreadExecutor.class);
            thread.start();
            threadNumber++;*/
        }
        Thread.sleep(100000);
    }
}
