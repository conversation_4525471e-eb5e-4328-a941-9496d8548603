package com.youibot.agv.scheduler.engine.action;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.util.MissionActionSortUtils;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mapper.MissionWorkLogMapper;
import com.youibot.agv.scheduler.mapper.MissionWorkMapper;
import com.youibot.agv.scheduler.service.MissionActionParameterService;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.service.MissionWorkGlobalVariableService;
import com.youibot.agv.scheduler.service.impl.MissionWorkServiceImpl;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-08-01 14:10
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FirstMarkerTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkServiceImpl.class);

    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionActionParameterService missionActionParameterService;

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

//    @Autowired
//    private MissionWorkMapper missionWorkMapper;
//
//    @Autowired
//    private MissionWorkLogMapper missionWorkLogMapper;

    @Autowired
    private MissionActionService missionActionService;

    @Autowired
    private DefaultVehiclePool vehiclePool;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Test
    public void test(){
        String missionWorkId = "1b3ad9e3-9cd6-4755-8dda-5acb664d5398";
        MissionWork missionWork = null;//missionWorkMapper.selectByPrimaryKey(missionWorkId);
        // get action by mission id .
        List<MissionAction> missionActions = missionActionService.selectByMissionId(missionWork.getMissionId());
        if (CollectionUtils.isEmpty(missionActions)) {
            return ;
        }
        // filter action
        missionActions = missionActions.stream().filter(x -> StringUtils.isEmpty(x.getParentActionId()) && (x.getActionType().equals("MOVE_SIDE_PATH") || x.getActionType().equals("MOVE_DOCKING"))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(missionActions)) {
            return ;
        }
        MissionActionSortUtils.sortMissionActionListBySequence(missionActions);//排序
        MissionAction missionAction = missionActions.get(0);
        // get marker by action.
        List<MissionActionParameter> missionActionParameters = missionActionParameterService.selectByMissionActionId(missionAction.getId());
        // get marker code.
        List<MissionActionParameter> markerActionParameters = missionActionParameters.stream().filter(x -> x.getParameterKey().equals("markerCode")).collect(Collectors.toList());
        List<MissionActionParameter> mapActionParameters = missionActionParameters.stream().filter(x -> x.getParameterKey().equals("agvMapId")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(markerActionParameters) || CollectionUtils.isEmpty(mapActionParameters)) {
            return ;
        }
        String markerCode = "";
        String mapId = "";
        if (markerActionParameters != null && mapActionParameters != null) {
            final String markerCodeKey = markerActionParameters.get(0).getParameterValue();
            final String mapIdKey = mapActionParameters.get(0).getParameterValue();
            // check global parameter.
            List<MissionWorkGlobalVariable> missionWorkGlobalVariables = missionWorkGlobalVariableService.selectByMissionWorkId(missionWorkId);
            List<MissionWorkGlobalVariable> markerGlobals = missionWorkGlobalVariables.stream().filter(mg -> mg.getVariableKey().equals(markerCodeKey)).collect(Collectors.toList());
            List<MissionWorkGlobalVariable> mapGlobals = missionWorkGlobalVariables.stream().filter(mg -> mg.getVariableKey().equals(mapIdKey)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(markerGlobals)){
                markerCode = markerGlobals.get(0).getVariableValue();
            }else {
                markerCode = markerCodeKey;
            }
            if (!CollectionUtils.isEmpty(mapGlobals)){
                mapId = mapGlobals.get(0).getVariableValue();
            }else {
                mapId = mapIdKey;
            }
        }
        // check marker code info.
        if (StringUtils.isEmpty(markerCode) || StringUtils.isEmpty(mapId)) {
            return ;
        }
        final String markerCodeFinal = markerCode;
        final String mapIdFinal = mapId;
        Set<Marker> markers = MapGraphUtil.getEnableMarkers();
//        markers = markers.stream().filter((Marker m) -> (m.getCode().equals(markerCodeFinal) && m.getAgvMapId().equals(mapIdFinal))).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(markers)) {
//            LOGGER.error("getMissionFirstMakerId missionWorkId：{}", missionWork == null ? null : missionWork.getId());
            throw new ExecuteException("marker=" + markerCode + MessageUtils.getMessage("action.marker_is_null_or_disable"));
        }
    }
}
