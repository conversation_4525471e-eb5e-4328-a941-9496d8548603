package com.youibot.agv.scheduler.engine.action.attribute;

import java.util.Date;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FreeTimeAttributeTest {

	@Test
	public void test() throws InterruptedException {
		Date freeStartTime = new Date();
		Thread.sleep(1500);
		Double value = (double)(new Date().getTime() - freeStartTime.getTime()) / 1000;
		System.out.println("----------->" + value);
	}
	
}
