package com.youibot.agv.scheduler.engine.action;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.service.SidePathService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/14 19:57
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SidePathActionTest {

    @Autowired
    private SidePathService sidePathService;

    @Test
    public void pathNavigationTest() {

    }

}
