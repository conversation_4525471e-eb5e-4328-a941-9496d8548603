package com.youibot.agv.scheduler.engine.util;

import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/7/24 20:47
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HttpClientServiceTest {

    @Autowired
    private HttpClientService httpClientService;

    @Test
    public void doGet() throws IOException {
        HttpResult httpResult = httpClientService.doGet("http://localhost:8080/api/v1/AGVMaps");
        System.out.println("------>" + httpResult.getCode());
    }

    @Test
    public void doPost() throws IOException {
        HttpResult httpResult = httpClientService.doPost("http://localhost:8080/api/v1/AGVMaps", "name\":\"测试地图1\"}");
        System.out.println("------>" + httpResult.getCode());
    }

}
