package com.youibot.agv.scheduler.engine.pathplan.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DStarLiteAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.thread.MapUpdateThread;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class VehicleScopeServiceTest {

	@Autowired
	private VehicleScopeService vehicleScopeService;
	@Autowired
	DefaultVehiclePool  defaultVehiclePool;
	 @Autowired
	 private AGVService agvService;
	 
	 @Autowired
	 private MapUpdateThread mapUpdateThread;
	 
	 String goalMarkerId = "ca235344-e340-4b43-baa7-e05e09df5c4e";
	 
	 @Test
	 public void testAim() {
		  String agvMapId1 = "FAB_10";
//	        MapGraphUtil.addAGVMap(agvMapId1);
//	        
//		 boolean checkAimMarker = MapGraphUtil.checkAimMarker(goalMarkerId );
//		 
//		 System.out.println("checkAim:{}" + checkAimMarker );
		 try {
			mapUpdateThread.transMapDataFromCurrentToMemory();
			boolean checkAimMarker = MapGraphUtil.checkAimMarker(goalMarkerId );
			 
			 System.out.println("checkAim:{}" + checkAimMarker );
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	 }
	@Test
	public void testScopeByDistanceStringVehicle() {
		  String agvMapId1 = "FAB_10";
	        MapGraphUtil.addAGVMap(agvMapId1);
//	        DirectedGraph dGraph = MapGraphUtil.getCloneDirectedGraph();
	        DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();
	        String startMarkerId = "dbffbbcf-acd5-485d-8d90-b9b748a81aa9";
	       
	        long startTime = System.currentTimeMillis();
	        String agvCode ="AAGVA9";
//	        pathPlanManager.plan(agvCode, dGraph, startMarkerId, goalMarkerId, false);
	        Agv agvDto = agvService.selectByAgvCode(agvCode);
	        defaultVehiclePool.attachAGV(agvDto);
	        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
	        Double distance = vehicleScopeService.scopeByDistance( goalMarkerId , vehicle) ;
	        
	        long costTime = System.currentTimeMillis() - startTime;
	   
	        System.out.println("路径规划计算耗时[" + costTime + "]ms" +",距离double:" + distance );
	        
	}

	
	@Test
	public void test3() {
		
		    DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();
	        DStarLiteAlgorithm dStarLiteAlgorithm = new DStarLiteAlgorithm(originDirectedGraph);
	        String startMarkerId = "dbffbbcf-acd5-485d-8d90-b9b748a81aa9";
	        MarkerPathResult markerPathResult = dStarLiteAlgorithm.plan(startMarkerId, goalMarkerId);
	        
	}
}
