package com.youibot.agv.scheduler.engine.action.device;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

/**
 * 红外热像仪拍照测试
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/7/15 16:10
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ThermalImagerPhotographyActionTest {

    @Test
    public void test() throws InterruptedException, IOException {
        //ThermalImagerPhotographyAction thermalImagerPhotographyAction = (ThermalImagerPhotographyAction) ApplicationUtils.getBean("thermalImagerPhotographyAction");
        //MissionWorkAction missionWorkAction = new MissionWorkAction();
        //missionWorkAction.setId("thermal-test");
        //missionWorkAction.setParameters("{\"count\":5}");
        //thermalImagerPhotographyAction.init(missionWorkAction);
        //QrCodeVehicle vehicle = (QrCodeVehicle) ApplicationUtils.getBean("corgiQR22Vehicle");
        //vehicle.setIp("**************");
        //Map<String, Object> resultMap = thermalImagerPhotographyAction.execute(vehicle);
        //System.out.println("-------->resultMap:" + resultMap);
    }
}
