package com.youibot.agv.scheduler.FTP;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.map.entity.AGVMapMd5;
import com.youibot.agv.scheduler.map.entity.PathMd5;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MD5Utils;
import com.youibot.agv.scheduler.util.Md5Util;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.DigestUtils;

import java.io.InputStream;

import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.MD5_SUFFIX;
import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.ROAD_NETWORK_SUFFIX;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class UploadMapTest {

    @Test
    public void generateMd5() {
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            String roadNetWorkPath = "/home/<USER>/youibot_map/map1001/path/current/";
            String locationPath = "/home/<USER>/youibot_map/map1001/locating/current/";
            ftpClient.changeWorkingDirectory(roadNetWorkPath);
            System.out.println(ftpClient.printWorkingDirectory());
            FTPFile[] ftpFiles = ftpClient.listFiles();
            PathMd5 pathMd5 = new PathMd5();
            for (FTPFile ftpFile : ftpFiles) {
                if (ftpFile.isFile()) {
                    String suffix = ftpFile.getName().substring(ftpFile.getName().lastIndexOf(".") + 1);
                    if (suffix.equals(ROAD_NETWORK_SUFFIX)) {
                        InputStream inputStream = ftpClient.retrieveFileStream(roadNetWorkPath + ftpFile.getName());
                        if (inputStream != null) {
                            String md5ByFilePath = DigestUtils.md5DigestAsHex(inputStream);
                            pathMd5.setPathMd5(md5ByFilePath);
                            inputStream.close();
                            ftpClient.completePendingCommand();
                        }
                    }
                }
            }

            ftpClient.changeToParentDirectory();
            ftpClient.changeToParentDirectory();
            ftpClient.changeWorkingDirectory(locationPath);
            System.out.println(ftpClient.printWorkingDirectory());
            ftpFiles = ftpClient.listFiles();
            JSONObject jsonObject = new JSONObject();
            for (FTPFile ftpFile : ftpFiles) {
                if (ftpFile.isFile()) {
                    String suffix = ftpFile.getName().substring(ftpFile.getName().lastIndexOf(".") + 1);
                    if (!suffix.equals(MD5_SUFFIX)) {
                        InputStream inputStream = ftpClient.retrieveFileStream(locationPath + ftpFile.getName());
                        if (inputStream != null) {
                            String md5ByFilePath = DigestUtils.md5DigestAsHex(inputStream);
                            jsonObject.put(suffix + "Md5", md5ByFilePath);
                            inputStream.close();
                            ftpClient.completePendingCommand();
                        }

                    }
                }
            }
            AGVMapMd5 agvMapMd5 = JSONObject.parseObject(JSON.toJSONString(jsonObject), AGVMapMd5.class);

            MapFileUtils.writeValue(JSONObject.toJSONString(pathMd5), roadNetWorkPath + "path.md5", ftpClient);
            MapFileUtils.writeValue(JSONObject.toJSONString(agvMapMd5), locationPath + "map.md5", ftpClient);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ftpClient != null) {
                FtpUtils.disconnect(ftpClient);
            }
        }
    }
}
