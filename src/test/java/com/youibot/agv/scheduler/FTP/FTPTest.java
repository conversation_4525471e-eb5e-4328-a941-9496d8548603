package com.youibot.agv.scheduler.FTP;

import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.MapGraphInfo;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FTPTest {

    @Test
    public void FTPFileTest() throws IOException {
        FTPClient ftpClient = FtpUtils.getConnectClient("172.20.29.18", 21, "ftp", "123456");
        System.out.println(ftpClient.printWorkingDirectory());
        String path = "/home/<USER>/";
        boolean b = ftpClient.changeWorkingDirectory(path);
        System.out.println(b);
        System.out.println(ftpClient.printWorkingDirectory());
        FTPFile[] ftpFiles = ftpClient.listFiles();
        System.out.println(ftpFiles.length);
        //MapFileUtils.getMapGraphs(ftpClient, path);
        FtpUtils.disconnect(ftpClient);
//        MapGraphInfo map1001 = AGVMapInfoCache.getCache("map1001");
//        System.out.println(map1001.getAgvMap().toString());
    }
    
    @Test
    public void FTPFileTest2() throws IOException {
    	
        FTPClient ftpClient = FtpUtils.getConnectClient("172.20.29.18", 21, "ftp", "123456");
        System.out.println(ftpClient.printWorkingDirectory());
        String path = "/home/<USER>/";
        boolean b = ftpClient.changeWorkingDirectory(path);
        System.out.println(b);
        System.out.println(ftpClient.printWorkingDirectory());
        FTPFile[] ftpFiles = ftpClient.listFiles();
        System.out.println(ftpFiles.length);
        //MapFileUtils.getMapGraphs(ftpClient, path);
        FtpUtils.disconnect(ftpClient);
//        MapGraphInfo map1001 = AGVMapInfoCache.getCache("map1001");
//        System.out.println(map1001.getAgvMap().toString());
    }
    

    public static void main(String[] args) throws IOException {

    }


}
