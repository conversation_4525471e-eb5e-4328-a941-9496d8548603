use agv_scheduler;

INSERT INTO `agv_group`(id,name,agv_type_id,description)
VALUES ('55e0cb87-38c6-4229-afc7-65a6c2216bb6', '仓库B小组', 'TRANS200', '0');
INSERT INTO `agv_group`(id,name,agv_type_id,description)
VALUES ('99d39b1b-35c1-42b9-ac1e-a4c266e723f0', '仓库A小组', 'TRANS200', '0');

INSERT INTO `agv`(id,agv_device_id,agv_group_id,agv_type_id,map_id,name,description,ip,agv_connection_status,agv_connection_mode)
VALUES ('b0c1d9ec-03b9-43ed-b7a7-6ca05c367250', NULL, '99d39b1b-35c1-42b9-ac1e-a4c266e723f0', 'TRANS200','b5f57291-bcb9-4dab-8bea-7b9485ab4d48', '测试AGV-A', '测试AGV-A', '*************', 'CONNECTION_NOT', 'AUTO_CONNECTION');
INSERT INTO `agv`(id,agv_device_id,agv_group_id,agv_type_id,map_id,name,description,ip,agv_connection_status,agv_connection_mode)
VALUES ('cfe0e198-e880-46dc-8c03-8fc70fffaf01', NULL, '99d39b1b-35c1-42b9-ac1e-a4c266e723f0', 'TRANS200','b5f57291-bcb9-4dab-8bea-7b9485ab4d48', '测试AGV-B', '测试AGV-B', '*************', 'CONNECTION_NOT', 'AUTO_CONNECTION');

-- 2.	初始化地图数据
INSERT INTO `agv_map`(`id`, `type`, `name`, `description`, `map_data`, `origin_x`, `origin_y`, `resolution`, `height`, `width`, `physics_height`, `physics_width`, `origin_yaw`, `occupied_thresh`, `free_thresh`, `negate`, `version`, `is_current_map`)
VALUES ('b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 'VIRTUAL_MAP', 'AGV和ADS联合测试地图', 'AGV和ADS联合测试地图', '', 0, 0, 0.05, 120, 72, 6, 3.6, 0, 0, 0, 0, 1, true);

INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('1', '1', 3.5, 0, '二维码1', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('2', '2', 3.5, 1, '二维码2', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('5', '5', 3.5, 3.965, '二维码5', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('12', '12', 0, 3.84, '二维码12', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('13', '13', 1.75, 0, '二维码13', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('14', '14', 1.75, 5, '二维码14', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('15', '15', 0, 0.88, '二维码15', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('16', '16', 0, 0, '二维码16', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('17', '17', 1, 0, '二维码17', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('19', '19', 1, 4, '二维码19', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('20', '20', 1, 5, '二维码20', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('21', '21', 0, 5, '二维码21', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('22', '22', 1, 1, '二维码22', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('23', '23', 1, 2, '二维码23', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('24', '24', 1, 3, '二维码24', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('25', '25', 2.5, 0, '二维码25', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('26', '26', 2.5, 1, '二维码26', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('27', '27', 2.5, 2, '二维码27', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('28', '28', 2.5, 5, '二维码28', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('29', '29', 2.5, 4, '二维码29', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('30', '30', 2.5, 3, '二维码30', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('11', '11', 3.5, 5, '二维码11', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('94', '94', 1.08, 5, '二维码30', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('95', '95', 1.08, 0, '二维码11', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);


INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('06754b5f-9e61-4c0d-83b0-7fb9f4692c51', '15->16', NULL, '15', '16', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('0d875753-067a-41c3-be14-43df3d520d10', '16->15', NULL, '16', '15', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('0e2428f2-dae6-4c28-94e7-24318d290c25', '16->17', NULL, '16', '17', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('10ada817-17a7-4061-933e-f686b864099c', '17->16', NULL, '17', '16', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('10c9e1e4-f068-4a87-92f7-6b760f379539', '17->95', NULL, '17', '95', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('95->17', '95->17', NULL, '95', '17', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('1661b45c-8e78-4ddb-9a2b-6aebd56b9902', '13->95', NULL, '13', '95', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('95->13', '95->13', NULL, '95', '13', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('16cf648c-3f79-4e80-8680-f83c92c09829', '13->25', NULL, '13', '25', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('1849f6fb-c7d0-4704-a45c-367f4901aa3f', '25->13', NULL, '25', '13', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('18c800bc-2ef8-44c8-8a86-7cc3cb26028d', '25->1', NULL, '25', '1', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('197beaf4-bc95-42d0-af5c-33bdee6b9911', '1->25', NULL, '1', '25', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('1cd60a06-f518-4e07-b69a-8e4541d7f124', '1->2', NULL, '1', '2', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('1fada04d-e8c0-49d6-93d0-d82e8f32abdc', '2->1', NULL, '2', '1', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('1ffb3a2e-720a-453f-bb71-21cc3bda0907', '12->21', NULL, '12', '21', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('1ffbq2e-720a-337f-bb71-21cc3bda0907', '21->12', NULL, '21', '12', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('22187828-fd9a-4f47-a9fd-989d36782a0c', '21->20', NULL, '21', '20', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('222f03d4-4c97-422d-b247-b17fe4370f7d', '20->21', NULL, '20', '21', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('25b9713b-2dd4-4a62-af45-45bc2e66c882', '20->94', NULL, '20', '94', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('94->20', '94->20', NULL, '94', '20', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('26d2c6aa-462a-430a-bbae-fae2799c728e', '14->94', NULL, '14', '94', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('94->14', '94->14', NULL, '94', '14', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('2accc8c6-bf9d-4129-91f1-3af9599415e9', '14->28', NULL, '14', '28', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('2cb7e32d-fd14-41d7-b541-9e655119b328', '28->14', NULL, '28', '14', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('39adfbf8-1ce9-42b2-9741-82cf86df107d', '28->11', NULL, '28', '11', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('39fa2f57-834d-4e02-a48f-f270e9715bad', '11->28', NULL, '11', '28', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('3c0ff20f-4b59-44e0-9804-a95725a45ca0', '11->5', NULL, '11', '5', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('3cb94d05-46d0-472c-b79f-a557e00ed10d', '5->11', NULL, '5', '11', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('3e984a9e-3232-4a36-abc9-eea9839dec73', '20->19', NULL, '20', '19', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('19-20', '19->20', NULL, '19', '20', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('409df0ff-5ee6-4ad8-876a-8747dcdeb230', '19->24', NULL, '19', '24', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('24->19', '24->19', NULL, '24', '19', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('440bce3b-f8a0-4b8b-ba1e-a83ba60554a9', '24->23', NULL, '24', '23', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('23->24', '23->24', NULL, '23', '24', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('48ab9fd2-35f5-440a-9bc5-45c63f4d730d', '23->22', NULL, '23', '22', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('22->23', '22->23', NULL, '22', '23', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('48c6fc57-0538-4c90-bb92-881e54e871a0', '22->17', NULL, '22', '17', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('17->22', '17->22', NULL, '17', '22', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('4ba07e41-56eb-4c0c-b52d-42f3f09719f2', '25->26', NULL, '25', '26', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('26->25', '26->25', NULL, '26', '25', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('4e0a0fff-edc0-4381-85cf-998c689d3759', '26->27', NULL, '26', '27', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('27->26', '27->26', NULL, '27', '26', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('4fb96cf2-dfdf-41da-8bb0-ba6df83fe309', '27->30', NULL, '27', '30', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('30->27', '30->27', NULL, '30', '27', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('50d82f8d-4e09-403e-863a-6c86049243c5', '30->29', NULL, '30', '29', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('29->30', '29->30', NULL, '29', '30', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('5b69dbda-b2f2-4be8-a55c-ca6f56dda2c7', '29->28', NULL, '29', '28', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `agv_map_id`)
VALUES ('28->29', '28->29', NULL, '28', '29', NULL, 0, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');


-- mission_group
INSERT INTO `mission_group` (id,name,description)
 VALUES ('0098eee1-689e-11e9-8b84-000c29998a57', '', '第二次AGV和ADS联合测试文档-二维码导航');

-- create mission.
INSERT INTO `mission` (id,agv_map_id,agv_id,agv_group_id,name,description,mission_group_id,sequence)
 VALUES ('pls-mission-1', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', null, null, 'PTL测试任务产线1', '移动到A点后上货到B点下货', null, 2);

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-1', 'pls-mission-1', '移动到19二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '1');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap1', 'pls-mission-action-1', 'marker_id', '19', 'String');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-2', 'pls-mission-1', '关闭避障传感器', 'NORMAL_PARAMETER', 'OBSTACLE_AVOIDANCE', '2');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap17', 'pls-mission-action-2', 'open', '0', 'Integer');


INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-3', 'pls-mission-1', '移动到20二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '3');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap3', 'pls-mission-action-3', 'marker_id', '20', 'String');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-4', 'pls-mission-1', '后电上升', 'NORMAL_PARAMETER', 'TRANSFORMER_REAR_RISE', '4');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-41', 'pls-mission-1', '前滚筒上货滚动', 'NORMAL_PARAMETER', 'ROLLER_FRONT_FEEDING', '5');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-42', 'pls-mission-1', '前电上升', 'NORMAL_PARAMETER', 'TRANSFORMER_FRONT_RISE', '6');


INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-5', 'pls-mission-1', '倒退移动到19二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '7');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap5', 'pls-mission-action-5', 'marker_id', '19', 'String');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-6', 'pls-mission-1', '打开避障传感器', 'NORMAL_PARAMETER', 'OBSTACLE_AVOIDANCE', '8');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap18', 'pls-mission-action-6', 'open', '1', 'Integer');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-7', 'pls-mission-1', '移动到22二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '9');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap7', 'pls-mission-action-7', 'marker_id', '22', 'String');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-8', 'pls-mission-1', '关闭避障传感器', 'NORMAL_PARAMETER', 'OBSTACLE_AVOIDANCE', '10');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap19', 'pls-mission-action-8', 'open', '0', 'Integer');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-9', 'pls-mission-1', '移动到17二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '11');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap9', 'pls-mission-action-9', 'marker_id', '17', 'String');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-10', 'pls-mission-1', '等待继续', 'NORMAL_PARAMETER', 'PAUSE_AGV_ACTION', '12');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-12', 'pls-mission-1', '前电下降', 'NORMAL_PARAMETER', 'TRANSFORMER_FRONT_DOWN', '15');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-13', 'pls-mission-1', '前滚筒下货滚动', 'NORMAL_PARAMETER', 'ROLLER_FRONT_BLANKING', '16');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-14', 'pls-mission-1', '后电下降', 'NORMAL_PARAMETER', 'TRANSFORMER_REAR_DOWN', '17');


INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-15', 'pls-mission-1', '倒退移动到22二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '18');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap13', 'pls-mission-action-15', 'marker_id', '22', 'String');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('pls-mission-action-16', 'pls-mission-1', '打开避障传感器', 'NORMAL_PARAMETER', 'OBSTACLE_AVOIDANCE', '19');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('ap20', 'pls-mission-action-16', 'open', '1', 'Integer');


select * from mission_action_parameter as ap left JOIN mission_action as ma on ma.id = ap.mission_action_id
where ma.mission_id = 'pls-mission-1' order by ma.sequence;
select * from mission_action as ma left JOIN mission_action_parameter as ap on ma.id = ap.mission_action_id
where ma.mission_id = 'pls-mission-1' order by ma.sequence;