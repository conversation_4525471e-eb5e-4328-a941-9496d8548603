use agv_scheduler;

-- 1. 初始化agv数据
-- agv_group
INSERT INTO `agv_group`(id,name,agv_type_id,description)
VALUES ('55e0cb87-38c6-4229-afc7-65a6c2216bb6', '仓库B小组', 'TRANS200', '0');
INSERT INTO `agv_group`(id,name,agv_type_id,description)
VALUES ('99d39b1b-35c1-42b9-ac1e-a4c266e723f0', '仓库A小组', 'TRANS200', '0');

-- agv
INSERT INTO `agv`(id,agv_device_id,agv_group_id,agv_type_id,map_id,name,description,ip,agv_connection_status,agv_connection_mode)
VALUES ('b0c1d9ec-03b9-43ed-b7a7-6ca05c367250', NULL, '99d39b1b-35c1-42b9-ac1e-a4c266e723f0', 'TRANS200','b5f57291-bcb9-4dab-8bea-7b9485ab4d48', '测试AGV-A', '测试AGV-A', '*************', 'CONNECTION_NOT', 'AUTO_CONNECTION');

INSERT INTO `agv`(id,agv_device_id,agv_group_id,agv_type_id,map_id,name,description,ip,agv_connection_status,agv_connection_mode)
VALUES ('cfe0e198-e880-46dc-8c03-8fc70fffaf01', NULL, '99d39b1b-35c1-42b9-ac1e-a4c266e723f0', 'TRANS200','b5f57291-bcb9-4dab-8bea-7b9485ab4d48', '测试AGV-B', '测试AGV-B', '*************', 'CONNECTION_NOT', 'AUTO_CONNECTION');

-- 2.	初始化地图数据
-- agv_map
INSERT INTO `agv_map`(`id`, `type`, `name`, `description`, `map_data`, `origin_x`, `origin_y`, `resolution`, `height`, `width`, `physics_height`, `physics_width`, `origin_yaw`, `occupied_thresh`, `free_thresh`, `negate`, `version`, `is_current_map`)
VALUES ('b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 'VIRTUAL_MAP', 'AGV和ADS联合测试地图', 'AGV和ADS联合测试地图', '', 0, 0, 0.05, 140, 140, 7, 7, 0, 0, 0, 0, 1, true);

-- marker
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('242', '242', 0, 0, '二维码242', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('224', '224', 0, 1, '二维码224', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('228', '228', 0, 2, '二维码228', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('250', '250', 0, 3, '二维码250', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('222', '222', 0, 4, '二维码222', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('221', '221', 0, 5, '二维码221', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('220', '220', 0, 6, '二维码220', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('200', '200', 0, 7, '二维码200', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('256', '256', 1, 0, '二维码256', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('225', '225', 1, 1, '二维码225', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('229', '229', 1, 2, '二维码229', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('223', '223', 1, 3, '二维码223', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('218', '218', 1, 4, '二维码218', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('217', '217', 1, 5, '二维码217', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('216', '216', 1, 6, '二维码216', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('201', '201', 1, 7, '二维码201', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('255', '255', 2, 0, '二维码255', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('241', '241', 2, 1, '二维码241', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('230', '230', 2, 2, '二维码230', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('215', '215', 2, 3, '二维码215', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('214', '214', 2, 4, '二维码214', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('213', '213', 2, 5, '二维码213', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('212', '212', 2, 6, '二维码212', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('208', '208', 2, 7, '二维码208', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('254', '254', 3, 0, '二维码254', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('226', '226', 3, 1, '二维码226', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('251', '251', 3, 2, '二维码251', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('211', '211', 3, 3, '二维码211', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('210', '210', 3, 4, '二维码210', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('209', '209', 3, 5, '二维码209', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('207', '207', 3, 6, '二维码207', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('205', '205', 3, 7, '二维码205', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('243', '243', 4, 0, '二维码243', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('227', '227', 4, 1, '二维码227', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('249', '249', 4, 2, '二维码249', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('253', '253', 4, 3, '二维码253', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('270', '270', 4, 4, '二维码270', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('271', '271', 4, 5, '二维码271', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('266', '266', 4, 6, '二维码266', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('204', '204', 4, 7, '二维码204', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('245', '245', 5, 0, '二维码245', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('239', '239', 5, 1, '二维码239', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('244', '244', 5, 2, '二维码244', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('252', '252', 5, 3, '二维码252', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('280', '280', 5, 4, '二维码280', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('267', '267', 5, 5, '二维码267', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('265', '265', 5, 6, '二维码265', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('203', '203', 5, 7, '二维码203', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('246', '246', 6, 0, '二维码246', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('240', '240', 6, 1, '二维码240', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('248', '248', 6, 2, '二维码248', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('247', '247', 6, 3, '二维码247', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('269', '269', 6, 4, '二维码269', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('268', '268', 6, 5, '二维码268', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('264', '264', 6, 6, '二维码264', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('202', '202', 6, 7, '二维码202', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('257', '257', 7, 0, '二维码257', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('258', '258', 7, 1, '二维码258', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('259', '259', 7, 2, '二维码259', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('260', '260', 7, 3, '二维码260', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('261', '261', 7, 4, '二维码261', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('262', '262', 7, 5, '二维码262', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('263', '263', 7, 6, '二维码263', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);
INSERT INTO `marker`(`id`, `name`, `x`, `y`, `description`, `agv_map_id`, `angle`, `type`, `icon`)
VALUES ('206', '206', 7, 7, '二维码206', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 0, 'QRCODE_MARKER', NULL);

-- side_path
-- y轴方向
-- x=0
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('200->220', '200->220', NULL, '200', '220', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('220->200', '220->200', NULL, '220', '200', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('220->221', '220->221', NULL, '220', '221', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('221->220', '221->220', NULL, '221', '220', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('221->222', '221->222', NULL, '221', '222', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('222->221', '222->221', NULL, '222', '221', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('222->250', '222->250', NULL, '222', '250', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('250->222', '250->222', NULL, '250', '222', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('250->228', '250->228', NULL, '250', '228', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('228->250', '228->250', NULL, '228', '250', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('228->224', '228->224', NULL, '228', '224', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('224->228', '224->228', NULL, '224', '228', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('224->242', '224->242', NULL, '224', '242', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('242->224', '242->224', NULL, '242', '224', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- x=1
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('201->216', '201->216', NULL, '201', '216', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('216->201', '216->201', NULL, '216', '201', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('216->217', '216->217', NULL, '216', '217', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('217->216', '217->216', NULL, '217', '216', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('217->218', '217->218', NULL, '217', '218', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('218->217', '218->217', NULL, '218', '217', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('217->223', '217->223', NULL, '217', '223', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('223->217', '223->217', NULL, '223', '217', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('223->229', '223->229', NULL, '223', '229', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('229->223', '229->223', NULL, '229', '223', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('229->225', '229->225', NULL, '229', '225', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('225->229', '225->229', NULL, '225', '229', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('225->256', '225->256', NULL, '225', '256', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('256->225', '256->225', NULL, '256', '225', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- x=2
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('208->212', '208->212', NULL, '208', '212', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('212->208', '212->208', NULL, '212', '208', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('212->213', '212->213', NULL, '212', '213', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('213->212', '213->212', NULL, '213', '212', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('213->214', '213->214', NULL, '213', '214', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('214->213', '214->213', NULL, '214', '213', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('214->215', '214->215', NULL, '214', '215', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('215->214', '215->214', NULL, '215', '214', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('215->230', '215->230', NULL, '215', '230', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('230->215', '230->215', NULL, '230', '215', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('230->241', '230->241', NULL, '230', '241', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('241->230', '241->230', NULL, '241', '230', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('241->255', '241->255', NULL, '241', '255', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('255->241', '255->241', NULL, '255', '241', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- x=3
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('205->207', '205->207', NULL, '205', '207', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('207->205', '207->205', NULL, '207', '205', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('207->209', '207->209', NULL, '207', '209', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('209->207', '209->207', NULL, '209', '207', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('209->210', '209->210', NULL, '209', '210', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('210->209', '210->209', NULL, '210', '209', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('210->211', '210->211', NULL, '210', '211', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('211->210', '211->210', NULL, '211', '210', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('211->251', '211->251', NULL, '211', '251', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('251->211', '251->211', NULL, '251', '211', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('251->226', '251->226', NULL, '251', '226', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('226->251', '226->251', NULL, '226', '251', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('226->254', '226->254', NULL, '226', '254', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('254->226', '254->226', NULL, '254', '226', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- x=4
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('204->266', '204->266', NULL, '204', '266', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('266->204', '266->204', NULL, '266', '204', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('266->271', '266->271', NULL, '266', '271', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('271->266', '271->266', NULL, '271', '266', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('271->270', '271->270', NULL, '271', '270', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('270->271', '270->271', NULL, '270', '271', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('270->253', '270->253', NULL, '270', '253', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('253->270', '253->270', NULL, '253', '270', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('253->249', '253->249', NULL, '253', '249', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('249->253', '249->253', NULL, '249', '253', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('249->227', '249->227', NULL, '249', '227', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('227->249', '227->249', NULL, '227', '249', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('227->243', '227->243', NULL, '227', '243', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('243->227', '243->227', NULL, '243', '227', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- x=5
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('203->265', '203->265', NULL, '203', '265', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('265->203', '265->203', NULL, '265', '203', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('265->267', '265->267', NULL, '265', '267', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('267->265', '267->265', NULL, '267', '265', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('267->280', '267->280', NULL, '267', '280', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('280->267', '280->267', NULL, '280', '267', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('280->252', '280->252', NULL, '280', '252', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('252->280', '252->280', NULL, '252', '280', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('252->244', '252->244', NULL, '252', '244', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('244->252', '244->252', NULL, '244', '252', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('244->239', '244->239', NULL, '244', '239', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('239->244', '239->244', NULL, '239', '244', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('239->245', '239->245', NULL, '239', '245', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('245->239', '245->239', NULL, '245', '239', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- x=6
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('202->264', '202->264', NULL, '202', '264', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('264->202', '264->202', NULL, '264', '202', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('264->268', '264->268', NULL, '264', '268', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('268->264', '268->264', NULL, '268', '264', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('268->269', '268->269', NULL, '268', '269', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('269->268', '269->268', NULL, '269', '268', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('269->247', '269->247', NULL, '269', '247', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('247->269', '247->269', NULL, '247', '269', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('247->248', '247->248', NULL, '247', '248', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('248->247', '248->247', NULL, '248', '247', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('248->240', '248->240', NULL, '248', '240', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('240->248', '240->248', NULL, '240', '248', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('240->246', '240->246', NULL, '240', '246', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('246->240', '246->240', NULL, '246', '240', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- x=7
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('206->263', '206->263', NULL, '206', '263', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('263->206', '263->206', NULL, '263', '206', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('263->262', '263->262', NULL, '263', '262', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('262->263', '262->263', NULL, '262', '263', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('262->261', '262->261', NULL, '262', '261', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('261->262', '261->262', NULL, '261', '262', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('261->260', '261->260', NULL, '261', '260', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('260->261', '260->261', NULL, '260', '261', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('260->259', '260->259', NULL, '260', '259', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('259->260', '259->260', NULL, '259', '260', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('259->258', '259->258', NULL, '259', '258', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('258->259', '258->259', NULL, '258', '259', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('258->257', '258->257', NULL, '258', '257', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('257->258', '257->258', NULL, '257', '258', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- x轴方向
-- y=7
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('200->201', '200->201', NULL, '200', '201', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('201->200', '201->200', NULL, '201', '200', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('201->208', '201->208', NULL, '201', '208', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('208->201', '208->201', NULL, '208', '201', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('208->205', '208->205', NULL, '208', '205', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('205->208', '205->208', NULL, '205', '208', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('205->204', '205->204', NULL, '205', '204', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('204->205', '204->205', NULL, '204', '205', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('204->203', '204->203', NULL, '204', '203', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('203->204', '203->204', NULL, '203', '204', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('203->202', '203->202', NULL, '203', '202', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('202->203', '202->203', NULL, '202', '203', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('202->206', '202->206', NULL, '202', '206', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('206->202', '206->202', NULL, '206', '202', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- y=6
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('220->216', '220->216', NULL, '220', '216', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('216->220', '216->220', NULL, '216', '220', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('216->212', '216->212', NULL, '216', '212', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('212->216', '212->216', NULL, '212', '216', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('212->207', '212->207', NULL, '212', '207', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('207->212', '207->212', NULL, '207', '212', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('207->266', '207->266', NULL, '207', '266', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('266->207', '266->207', NULL, '266', '207', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('266->265', '266->265', NULL, '266', '265', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('265->266', '265->266', NULL, '265', '266', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('265->264', '265->264', NULL, '265', '264', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('264->265', '264->265', NULL, '264', '265', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('264->263', '264->263', NULL, '264', '263', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('263->264', '263->264', NULL, '263', '264', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- y=5
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('221->217', '221->217', NULL, '221', '217', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('217->221', '217->221', NULL, '217', '221', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('217->213', '217->213', NULL, '217', '213', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('213->217', '213->217', NULL, '213', '217', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('213->209', '213->209', NULL, '213', '209', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('209->213', '209->213', NULL, '209', '213', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('209->271', '209->271', NULL, '209', '271', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('271->209', '271->209', NULL, '271', '209', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('271->267', '271->267', NULL, '271', '267', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('267->271', '267->271', NULL, '267', '271', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('267->268', '267->268', NULL, '267', '268', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('268->267', '268->267', NULL, '268', '267', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('268->262', '268->262', NULL, '268', '262', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('262->268', '262->268', NULL, '262', '268', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- y=4
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('222->218', '222->218', NULL, '222', '218', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('218->222', '218->222', NULL, '218', '222', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('218->214', '218->214', NULL, '218', '214', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('214->218', '214->218', NULL, '214', '218', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('214->210', '214->210', NULL, '214', '210', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('210->214', '210->214', NULL, '210', '214', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('210->270', '210->270', NULL, '210', '270', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('270->210', '270->210', NULL, '270', '210', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('270->280', '270->280', NULL, '270', '280', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('280->270', '280->270', NULL, '280', '270', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('280->269', '280->269', NULL, '280', '269', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('269->280', '269->280', NULL, '269', '280', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('269->261', '269->261', NULL, '269', '261', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('261->269', '261->269', NULL, '261', '269', NULL, 0, 1, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- y=3
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('250->223', '250->223', NULL, '250', '223', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('223->250', '223->250', NULL, '223', '250', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('223->215', '223->215', NULL, '223', '215', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('215->223', '215->223', NULL, '215', '223', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('215->211', '215->211', NULL, '215', '211', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('211->215', '211->215', NULL, '211', '215', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('211->253', '211->253', NULL, '211', '253', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('253->211', '253->211', NULL, '253', '211', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('253->252', '253->252', NULL, '253', '252', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('252->253', '252->253', NULL, '252', '253', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('252->247', '252->247', NULL, '252', '247', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('247->252', '247->252', NULL, '247', '252', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('247->260', '247->260', NULL, '247', '260', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('260->247', '260->247', NULL, '260', '247', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- y=2
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('228->229', '228->229', NULL, '228', '229', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('229->228', '229->228', NULL, '229', '228', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('229->230', '229->230', NULL, '229', '230', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('230->229', '230->229', NULL, '230', '229', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('230->251', '230->251', NULL, '230', '251', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('251->230', '251->230', NULL, '251', '230', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('251->249', '251->249', NULL, '251', '249', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('249->251', '249->251', NULL, '249', '251', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('249->244', '249->244', NULL, '249', '244', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('244->249', '244->249', NULL, '244', '249', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('244->248', '244->248', NULL, '244', '248', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('248->244', '248->244', NULL, '248', '244', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('248->259', '248->259', NULL, '248', '259', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('259->248', '259->248', NULL, '259', '248', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- y=1
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('224->225', '224->225', NULL, '224', '225', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('225->224', '225->224', NULL, '225', '224', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('225->241', '225->241', NULL, '225', '241', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('241->225', '241->225', NULL, '241', '225', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('241->226', '241->226', NULL, '241', '226', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('226->241', '226->241', NULL, '226', '241', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('226->227', '226->227', NULL, '226', '227', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('227->226', '227->226', NULL, '227', '226', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('227->239', '227->239', NULL, '227', '239', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('239->227', '239->227', NULL, '239', '227', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('239->240', '239->240', NULL, '239', '240', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('240->239', '240->239', NULL, '240', '239', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('240->258', '240->258', NULL, '240', '258', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('258->240', '258->240', NULL, '258', '240', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- y=0
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('242->256', '242->256', NULL, '242', '256', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('256->242', '256->242', NULL, '256', '242', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('256->255', '256->255', NULL, '256', '255', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('255->256', '255->256', NULL, '255', '256', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('255->254', '255->254', NULL, '255', '254', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('254->255', '254->255', NULL, '254', '255', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('254->243', '254->243', NULL, '254', '243', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('243->254', '243->254', NULL, '243', '254', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('243->245', '243->245', NULL, '243', '245', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('245->243', '245->243', NULL, '245', '243', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('245->246', '245->246', NULL, '245', '246', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('246->245', '246->245', NULL, '246', '245', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('246->257', '246->257', NULL, '246', '257', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `side_path`(`id`, `name`, `description`, `start_marker_id`, `end_marker_id`, `length`, `curvature`, `move_direction`, `agv_map_id`)
VALUES ('257->246', '257->246', NULL, '257', '246', NULL, 0, NULL, 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');


-- AVOIDANCE_OFF_AREA
INSERT INTO `map_area`(`id`, `area_type_id`, `name`, `remark`, `polygon`, `agv_map_id`)
VALUES ('8d572138-0873-49d1-b6df-35d13cf916c6', 'AVOIDANCE_OFF_AREA', '避障关闭区', NULL, '[{"x":1.5,"y":-0.5},{"x":3.5,"y":-0.5},{"x":3.5,"y":0.5},{"x":1.5,"y":0.5}]', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `map_area`(`id`, `area_type_id`, `name`, `remark`, `polygon`, `agv_map_id`)
VALUES ('4d1c1f9a-e014-4d2b-b343-5b1a395bded1', 'AVOIDANCE_OFF_AREA', '避障关闭区', NULL, '[{"x":7.5,"y":-0.5},{"x":9.5,"y":-0.5},{"x":9.5,"y":0.5},{"x":7.5,"y":0.5}]', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');
INSERT INTO `map_area`(`id`, `area_type_id`, `name`, `remark`, `polygon`, `agv_map_id`)
VALUES ('63d4ec39-36f9-4409-94ee-34dc7a8725d7', 'AVOIDANCE_OFF_AREA', '避障关闭区', NULL, '[{"x":-0.5,"y":2.5},{"x":0.5,"y":2.5},{"x":0.5,"y":4.5},{"x":-0.5,"y":4.5}]', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');

-- SINGLE_AGV_AREA
INSERT INTO `map_area`(`id`, `area_type_id`, `name`, `remark`, `polygon`, `agv_map_id`)
VALUES ('cee75733-e5b0-4ca1-9ffe-43587bf4a42f', 'SINGLE_AGV_AREA', '单机通过区域', NULL, '[{"x":-1.0,"y":8.0},{"x":-1.0,"y":4.5},{"x":2.5,"y":4.5},{"x":2.5,"y":8.0}]', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48');


-- create mission
INSERT INTO `mission` (id,agv_map_id,agv_id,agv_group_id,name,description,mission_group_id,sequence)
 VALUES ('test-mission-1', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 'b0c1d9ec-03b9-43ed-b7a7-6ca05c367250', null, '产线1运输任务', '移动到A点后上货到B点下货', null, 1);

INSERT INTO `mission` (id,agv_map_id,agv_id,agv_group_id,name,description,mission_group_id,sequence)
 VALUES ('test-mission-2', 'b5f57291-bcb9-4dab-8bea-7b9485ab4d48', 'cfe0e198-e880-46dc-8c03-8fc70fffaf01', null, '产线1运输任务', '移动到A点后上货到B点下货', null, 1);

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('test-mission-action-1', 'test-mission-1', '移动到218二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '1');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('plsap1', 'test-mission-action-1', 'marker_id', '218', 'String');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('test-mission-action-2', 'test-mission-1', '移动到201二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '2');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('plsap2', 'test-mission-action-2', 'marker_id', '201', 'String');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('test-mission-action-3', 'test-mission-1', '移动到202二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '3');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('plsap3', 'test-mission-action-3', 'marker_id', '202', 'String');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('test-mission-action-4', 'test-mission-1', '移动到269二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '4');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('plsap4', 'test-mission-action-4', 'marker_id', '269', 'String');

INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('test-mission-action-5', 'test-mission-2', '移动到269二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '1');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('plsap5', 'test-mission-action-5', 'marker_id', '269', 'String');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('test-mission-action-6', 'test-mission-2', '移动到202二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '2');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('plsap6', 'test-mission-action-6', 'marker_id', '202', 'String');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('test-mission-action-7', 'test-mission-2', '移动到201二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '3');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('plsap7', 'test-mission-action-7', 'marker_id', '201', 'String');
INSERT INTO `mission_action`  (id,mission_id,name,parameter_type,action_definition_Id,sequence)
VALUES ('test-mission-action-8', 'test-mission-2', '移动到218二维码点', 'NORMAL_PARAMETER', 'MOVE_NAV_QRCODES', '4');
INSERT INTO `mission_action_parameter`   (id,mission_action_id,parameter_key,parameter_value,parameter_value_type)
VALUES ('plsap8', 'test-mission-action-8', 'marker_id', '218', 'String');
