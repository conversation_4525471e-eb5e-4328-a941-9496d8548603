# TcpIpSerialConverter.py

## Introduction

This library is TCP/IP <-> RS232C convert on Raspberry Pi.

## How to use

1. Buy and set [Raspberry Pi](https://www.raspberrypi.org)
1. Copy "tcpip-serial-converter.py" to "/home/<USER>/TcpIpSerialConverter/tcpip-serial-converter.py"
1. Copy "run.sh" and `sh run.sh`

## Recommend

Use USB-Serial-Cable. e.g. [Buffalo](https://www.buffalo.jp/product/detail/bsusrc0605bs.html)  
Edit to "--device /dev/ttyUSB0" of "run.sh"
