AGV_SOCKET:
  #socket连接超时时间(30秒)
  SOTIMEOUT: 30000
  #心跳socket连接超时时间(5秒)
  HEARTBEAT_SOTIMEOUT: 5000
  #地图操作socket连接超时时间(600秒)
  MAP_SOTIMEOUT: 60000
  PORT:
    HEARTBEAT: 18203
    STATUS: 18204
    SENSOR: 18205
    CONFIG: 18206
    CONTROL: 18207
    ACTION: 18208
    MAP: 18209
    DEVICE: 18210
    TIME_DOMAIN: 19997
    FREQUENCY_DOMAIN: 1998

WEB_SOCKET:
  MAX_IDLE_TIMEOUT: 40000
  LOG_INFO_TIMEOUT: 3600000

#AGV SOCKET API CODE Table.
AGV_API_CODE:
  #对齐到指定位置
  MOVE_DOCKING: 40001
  #移动到指定坐标-自由导航
  MOVE_FREE_NAVIGATION: 40002
  #移动到指定位置-路径导航
  MOVE_SIDE_PATH: 40005
  #相对移动到指定位置
  MOVE_RELATIVE: 40008
  #原地旋转固定角度
  MOVE_ROTATE: 40009
  #电池冲电
  BATTERY_CHARGE: 40014
  #脱离对接位至原来位置
  LEAVE_DOCKING: 40025
  #辊筒电缸
  ROLLER_AND_TRANSFORMER: 40026
  #顶升机构
  JACKING_MECHANISM: 40027
  #货架旋转
  SHELF_ROTATION: 40028
  #打开货架随动
  SHELF_FOLLOW_UP_OPEN: 40029
  #关闭货架随动
  SHELF_FOLLOW_UP_CLOSE: 40030
  #夹持器（夹紧电机）
  GRIPPER: 11028
  #人体检测消毒灯控制
  HUMAN_DISINFECT: 40031
  #视觉抓取
  VISUAL_GRABBING: 40032
  #托盘升降
  TRAY_LIFTING: 40033
  #托盘前后
  TRAY_FORWARD: 40034
  #plc控制
  PLC: 40040
  #货架二维码中心对准
  SHELF_ALIGNMENT: 40041
  #机器人和货架旋转
  AGV_SHELF_ANGLE: 40042
  #夹抱左右侧进出料指令
  CLAMPS_OPERATE: 40050
  #日月光接驳架
  CONNECTING_FRAME: 40077
  #动作暂停
  ACTION_PAUSE: 40600
  #动作恢复
  ACTION_RESUME: 40601
  #动作停止
  ACTION_STOP: 40602
  #动作重置
  ACTION_RESET: 40603
  #读取设备噪音数据
  READ_EQUIPMENT_NOISE: 11001
  #升降平台控制指令
  LIFTING_PLATFORM: 11006
  #获取摄像头参数指令
  QUERY_CAMERA_PARAMETER: 11018
  #设置摄像头参数
  SETTING_CAMERA_PARAMETER: 11002
  #设置摄像头预置点
  SETTING_CAMERA_PRESET: 11019
  #摄像头拍照
  CAMERA_PHOTOGRAPHY: 11003
  #获取热像仪参数指令
  QUERY_INFRARED_PARAMETER: 11021
  #设置热像仪参数
  SETTING_INFRARED_PARAMETER: 11004
  #设置热像仪预置点
  SETTING_INFRARED_PRESET: 11022
  #红外热像仪拍照
  INFRARED_PHOTOGRAPHY: 11005
  #红外摄像头测温指令
  QUERY_INFRARED_TEMPERATURE: 11024
  #补光灯控制指令
  FILL_LIGHT: 11025
  #机械臂关节控制指令
  MANIPULATOR_ARM: 11011
  #机械臂状态操作
  MANIPULATOR_ARM_STATUS_OPERATION: 11033
  #视觉插孔控制
  VISUAL_JACK: 11026
  #档案抓取控制
  FILL_CAPTURE_CONTROL: 11029
  #紫外线消毒灯控制
  STERILIZING_LAMP: 11032
  #滚筒左侧进料指令
  ROLLER_LEFT_IN: 11038
  #滚筒左侧出料指令
  ROLLER_LEFT_OUT: 11039
  #滚筒右侧进料指令
  ROLLER_RIGHT_IN: 11040
  #滚筒右侧出料指令
  ROLLER_RIGHT_OUT: 11041
  #滚筒升降位置指令
  ROLLER_UP_DOWN: 11046
  #滚筒宽度指令
  ROLLER_WIDTH: 11050
  #六氟化硫检测
  SIX_SULFUR_FLUORIDE_DETECTION: 11034
  #插取电机位置控制
  PLUG_IN_MOTOR: 11035
  #气体检测
  GAS_DETECTION: 20004
  #device
  #机械臂示教控制指令
  MANIPULATOR_ARM_DEMO: 11031
  #查询机械臂关节控制状态指令
  MANIPULATOR_ARM_QUERY_STATUS: 11012
  #map
  #同步地图指令
  SYNC_MAP: 50001
  #指定当前地图
  APPOINT_CURRENT_MAP: 50002
  #保存地图图层数据指令
  SAVE_MAP_LAYER: 50003
  #查询AGV地图列表
  QUERY_AGVMAP_LIST: 50004
  #开始录制地图
  RECORDING_MAP: 50005
  #结束录制地图
  STOP_RECORDING_MAP: 50006
  #暂停录制地图
  PAUSE_RECORDING_MAP: 50007
  #恢复录制地图
  RESUME_RECORDING_MAP: 50008
  #查询录制地图信息
  QUERY_RECORDING_MAP_INFO: 50009
  #查询AGV地图信息
  QUERY_AGVMAP_INFO: 50010
  #查询地图图层数据
  QUERY_AGVMAP_LAYER_DATA: 50011
  #追加特征地图数据
  APPEND_FEATURE_MAP: 50012
  #status
  #查询AGV的所有状态
  QUERY_AGV_ALL_STATUS: 10010
  #查询AGV的基本信息
  QUERY_AGV_INFO: 10001
  #查询AGV设备列表信息
  QUERY_AGV_DEVICE_INFO: 10012
  #心跳连接
  HEARTBEAT_CONNECTION: 10014
  #任务状态
  ACTION_STATUS: 10015
  #control
  #AGV 移动控制
  AGV_MOVE_CONTROLLER: 30001
  #AGV 手动重定位
  MANUAL_RELOCATION: 30002
  #AGV 导航点插入系统
  INSERT_SYSTEM: 30003
  #AGV 自动重定位
  AUTO_RELOCATION: 30004
  #AGV 检测对接点
  CHECK_DOCKING_POINT: 30005
  #AGV 导航点插入系统状态查询
  INSERT_SYSTEM_STATUS: 30006
  #AGV 切换到手动控制模式
  SWITCH_MANUAL_CONTROL_MODE: 30007
  #AGV 避障控制开关
  OBSTACLE_AVOIDANCE: 30008
  #AGV 生成快捷路径
  GENERATE_SHORTCUT_PATHS: 30009
  #AGV 录制初始位置
  HOME_MARKER_RECORD: 30010
  #AMR电机软急停
  EMERGENCY_STOP: 30011
  #AGV 重启
  AGV_RESTART: 30012
  #AGV 解除碰撞(短暂)急停
  SHORT_STOP_RECOVERY: 30014
  #config
  #查询AGV的配置信息
  QUERY_AGV_CONFIG_INFO: 60001
  #保存AGV的配置信息
  SAVE_AGV_CONFIG_INFO: 60002
  #传感器
  #查询激光数据
  QUERY_LASER_DATA: 20001
  #温湿度传感器
  TEMPERATURE_AND_HUMIDITY: 20005
  #声纹传感器
  #时域数据
  TIME_DOMAIN_DATA: 19997
  #频域数据
  FREQUENCY_DOMAIN_DATA: 19998
  #防跌落状态
  FALL_PREVENT_STATUS: 30077
AGV_THREAD_CONFIG:
  WAIT_TIME:
    #AGV获取不到可走路径时，需要等待重新请求路径规划。等待时间配置(单位是ms)
    AGV_RETRY_PATH_PLAN: 1000
    #在AGV执行ACTION，需要不断的查询AGV状态时，查询间隔时间配置(单位是ms)
    AGV_RETRY_GET_STATUS: 1000
    #循环获取agvStatus每次间隔时间(单位是ms)
    GET_AGV_STATUS_INTERVAL: 50
    #agv状态连接失败后重新连接等待时间(单位是ms)
    AGV_STATUS_RECONNECT: 10000
    #在AGV执行Relocation，需要不断的查询AGV relocation状态时，查询间隔时间配置(单位是ms)
    AGV_RETRY_GET_INSERT_SYSTEM: 1000
    #missionWorkAction参数类型为运行时参数时，missionWorkAction会被置为等待运行参数状态，需要不断查询missionWorkAction的状态是否被置为运行状态（运行参数填入），每次间隔时间（单位是ms）
    GET_AGV_MISSION_WORK_ACTION_STATUS: 1000
    #agv在运行过程中，如果前方出现障碍物，停止移动后，需要等待时间后重新请求路径规划。等待时间配置(单位是ms)
    AGV_CONFLICT_RETRY_PATH_PLAN: 3000
    #每次发送心跳连接间隔时间(单位是ms)
    HEARTBEAT_CONNECTION_INTERVAL: 100
    #智能充电每个指令发送的时间间隔
    SMART_CHARGING_COMMAND_INTERVAL: 1000
    #设置摄像头参数后等待完成时间
    AFTER_SETTING_CAMERA_PARAM: 5000

PATH_PLAN:
  #安全路径发送给AGV的最大marker点数量
  MAX_MARKERS_SEND_TO_AGV: 20
  #AGV安全距离（1为相隔一个二维码）暂时只支持间隔一个二维码
  AGV_SAFE_DISTANCE: 0
  #AGV安全刹车距离（单位m）
  AGV_BRAKE_DISTANCE: 0.5
  #在AGV运行中检测频率（间隔毫秒）
  AGV_DETECTION_FREQUENCY: 100
  #AGV最大刹车距离（单位为二维码点个数）
  AGV_BRAKE_QR_DISTANCE: 5
  #判断脱轨的最大距离(单位m)
  MAX_DERAILMENT_DISTANCE: 0.2
  #纯路径跟随AGV的安全检测距离(单位m)
  AGV_SAFE_DISTANCE_PURE_PURSUIT: 3.0

# agv拍摄图片的存储路径
IMAGE_SAVE_PATH:
  # 摄像头拍照
  CAMERA_FOLDER: /server/data/image/camera
  # 红外热像仪
  INFRARED_FOLDER: /server/data/image/infrared

# 调用agv api的配置
CALL_AGV_API_CONFIG:
  # agv执行指令失败时重新调用的次数
  FAULT_RESEND_COUNT: 1
  CHARGING_FAULT_RESEND_COUNT: 10

LICENSE:
  LICENSE_TYPE: YOUIFleet
  SUBJECT: license
  STOREPASS: ypl123
  PUBLICKEYSSTOREPATH: /secure/publicCerts.store
  PUBLICALIAS: publicCert

LOG_INFO:
  DIRECTOR_PATH: /server/ads/logs
  LOG_FIX: .log

FILE:
   DOWNLOAD_FILE_PATH:
       USER_MANUAL: /server/data/file/YOUICompass-v4.1.0.docx
       API_MANUAL: /server/data/file/YOUICompassAPI-v4.1.0.docx

 #上传文件目录
UPLOAD_FILE:
  TIME_DOMAIN: /service/data/time_domain/
  FREQUENCY_DOMAIN: /service/data/frequercy_domain/

MODBUS:
  SLAVE_ID: 1
  NUMBER_OF_BITS: 1

AGV:
  IP: **********