

DROP TABLE IF EXISTS `work_cycle_fire_path`;
CREATE TABLE `work_cycle_fire_path`  (
  `path_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '·��id',
  `enabled` bit(1) NULL DEFAULT NULL COMMENT '�Ƿ�����',
  `create_time` timestamp(0) NULL DEFAULT NULL COMMENT '����ʱ��',
  `update_time` timestamp(0) NULL DEFAULT NULL COMMENT '����ʱ��',
  PRIMARY KEY (`path_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '̨������Ŀ���ֻ�״·������' ROW_FORMAT = Dynamic;

