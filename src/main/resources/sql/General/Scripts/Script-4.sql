


CREATE DATABASE IF NOT EXISTS agv_scheduler_v4.8.2 default charset utf8 COLLATE utf8_general_ci;
use agv_scheduler_v4.8.2


CREATE TABLE IF NOT EXISTS `map_area_type` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(256) COMMENT '����',
  `description` varchar(2048) COMMENT '����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='�������ͱ�';


CREATE TABLE IF NOT EXISTS `notification` (
  `id` varchar(256) NOT NULL,
  `description` varchar(2048) COMMENT '����',
  `type` varchar(256) COMMENT '����',
  `agv_code` varchar(256) COMMENT '�����˱��',
  `agv_name` varchar(256) COMMENT '����������',
  `mission_work_id` varchar(256) COMMENT '����Id',
  `mission_work_name` varchar(256) COMMENT '��������',
  `have_read` varchar(256) COMMENT '�Ѷ�',
  `scale` int COMMENT '��Ϣ����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  KEY `have_read` (`have_read`) USING BTREE,
  KEY `agv_code` (`agv_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='֪ͨ��Ϣ';

CREATE TABLE IF NOT EXISTS `elevator` (
  `id` varchar(256) NOT NULL,
  `name` varchar(256) COMMENT '����',
  `description` varchar(2048) COMMENT '����',
  `customer` varchar(256) COMMENT '�ͻ� TAI_CHI:̫��',
  `ip` varchar(2048) COMMENT '�������ݵ�ip��ַ',
  `port` int(11) COMMENT '�������ݵĶ˿ں�',
  `apply_timeout` int(11) default 180 COMMENT '������ݳ�ʱʱ�� ��λ����',
  `control_timeout` int(11) default 120 COMMENT '���Ƶ��ݳ�ʱʱ�� ��λ����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����';

CREATE TABLE IF NOT EXISTS `floor` (
  `id` varchar(256) NOT NULL,
  `elevator_id` varchar(256) COMMENT '����ID',
  `agv_map_id` varchar(256) COMMENT '��ͼID',
  `marker_id` varchar(2048) COMMENT '��ǵ�ID, type=ELEVATOR_MARKER�ı�ǵ�ſ����',
  `marker_code` varchar(255) COMMENT '��ǵ�CODE' ,
  `number` int(11) COMMENT '¥����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='¥��';

CREATE TABLE IF NOT EXISTS `record_path` (
  `id` varchar(256) NOT NULL,
  `agv_map_id` varchar(256) NOT NULL COMMENT '��ͼid',
  `name` varchar(256) COMMENT '����',
  `positions` varchar(1024) NOT NULL COMMENT '·���������',
  `length` double NOT NULL COMMENT '����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='·����';

CREATE TABLE IF NOT EXISTS `agv`  (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) NOT NULL DEFAULT '' COMMENT '�����˱��',
  `agv_id` varchar(256) DEFAULT '' COMMENT '������ID����¼���룩',
  `agv_name` varchar(256) NOT NULL DEFAULT '' COMMENT '����',
  `navigation_type` varchar(50) NOT NULL DEFAULT '' COMMENT '�������ͣ�LASER:���⵼�� QR_CODE:��ά�뵼�� BLEND:��ϵ���',
  `agv_type` varchar(256) DEFAULT '' COMMENT '����������',
  `agv_group_id` varchar(256) NOT NULL DEFAULT '' COMMENT '������������',
  `status` tinyint (1) NOT NULL DEFAULT 0 COMMENT 'agv����״̬ 0:δ����,1:����',
  `online_status` tinyint (1) DEFAULT 0 COMMENT 'agv����״̬ 0:����,1:����,2:����',
  `shape_info` longtext COMMENT '������Ϣ',
  `agv_color` varchar(256) COMMENT '��������ɫ',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  KEY `agv_code` (`agv_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Agv';

CREATE TABLE IF NOT EXISTS `agv_group`  (
  `id` varchar(256)  NOT NULL COMMENT '��������ID',
  `name` varchar(256) COMMENT '������������',
  `description` varchar(1024)  COMMENT '����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='agv_group';

CREATE TABLE IF NOT EXISTS `agv_type`  (
  `id` varchar(256)  NOT NULL COMMENT 'AGV����ID',
  `code` varchar(256) COMMENT 'AGV����',
  `name` varchar(256) COMMENT '��������',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  UNIQUE(`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='agv_type';

CREATE TABLE IF NOT EXISTS `mission_work` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) COMMENT 'agv�ı��',
  `agv_name` varchar(256) COMMENT 'agv������',
  `mission_id` varchar(256) NOT NULL COMMENT '����ID',
  `trigger_selector_id` varchar(256) COMMENT '������ID',
  `schedule_plan_id` varchar(256) COMMENT '���ȼƻ�id',
  `callback_url` varchar(256) COMMENT '�ص�url',
  `name` varchar(256) NOT NULL COMMENT '����',
  `sequence` int DEFAULT '1' COMMENT '˳��༭',
  `description` varchar(2048) COMMENT '����',
  `status` varchar(256) COMMENT '״̬��WAIT,START,RUNNING,SUCCESS,FAULT,PAUSE,SHUTDOWN',
  `message` text COMMENT '�쳣��Ϣ',
  `runtime_param` varchar(1024) COMMENT '����ʱ����(json��ʽ), ��{"marker1":"1001"}',
--  `mission_call_id` varchar(256) COMMENT '�������id',
  `agv_group_id` varchar(256) COMMENT 'AGV��ID',
  `mission_group_id` varchar(256) COMMENT '������ID',
  `agv_type` varchar(256) COMMENT 'AGV����',
  `error_code` varchar(256) COMMENT '������',
--   `allocation_status` varchar(256) DEFAULT 'UNASSIGNED' COMMENT '����״̬ �ѷ��䣺ASSIGNED  δ���䣺UNASSIGNED',
  `start_time` datetime COMMENT '��ʼʱ��',
  `end_time` datetime COMMENT '����ʱ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����ִ��';

CREATE TABLE IF NOT EXISTS `mission_work_log` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_work_id` varchar(256) NOT NULL COMMENT '��ҵID',
  `agv_code` varchar(256) COMMENT 'agv�ı��',
  `status` varchar(256) COMMENT '״̬��WAIT,START,RUNNING,SUCCESS,FAULT,PAUSE,SHUTDOWN',
  `start_time` datetime COMMENT '��ʼʱ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����״̬�����־';


CREATE TABLE IF NOT EXISTS `mission_work_action` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) COMMENT 'agv�ı��',
  `mission_action_id` varchar(256) COMMENT '������id',
  `mission_work_id` varchar(256) COMMENT '��������ID',
  `action_type` varchar(256) COMMENT '��������',
  `name` varchar(128) COMMENT '����',
  `message` varchar(1024) COMMENT 'ִ�н������',
  `sequence` int COMMENT '˳����',
  `parameters` varchar(4096) COMMENT '����',
  `status` varchar(128) COMMENT '״̬��START,RUNNING,SUCCESS,FAULT,WAIT_RUNTIME_PARAM',
  `parameter_type` varchar(128) DEFAULT 'NORMAL_PARAMETER' COMMENT '�������(NORMAL_PARAMETER,RUNTIME_PARAMETER)',
  `result_code` varchar(128) DEFAULT NULL COMMENT '���ر���',
  `result_message` varchar(2048) DEFAULT NULL COMMENT '������ʾ��Ϣ',
  `result_type` varchar(128) DEFAULT NULL COMMENT '������������������(MESSAGE,IMAGE)',
  `result_data` longtext COMMENT '���ص�����������',
  `error_code` varchar(256) COMMENT '������',
  `start_time` datetime COMMENT '��ʼʱ��',
  `end_time` datetime COMMENT '����ʱ��',
  `action_sequence` int COMMENT 'ִ��˳����',
  `this_loop_completes` boolean DEFAULT NULL COMMENT '��Ϊѭ������actionʱ����¼����ѭ���Ƿ����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����ִ�ж���';


CREATE TABLE  IF NOT EXISTS `dashboard` (
  `id` varchar(256) NOT NULL COMMENT 'id',
  `agv_map_id` varchar(256) COMMENT '��ͼid',
  `name` varchar(128) COMMENT '����',
  `description` varchar(2048) COMMENT '����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='dashboard';

CREATE TABLE IF NOT EXISTS  `user` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(128) COMMENT '����',
  `user_name` varchar(128) COMMENT '�û���',
  `password` varchar(128) COMMENT '��½����',
  `email` varchar(128) COMMENT '����',
  `phone` varchar(128) COMMENT '�绰',
  `admin` tinyint(1) DEFAULT 0 COMMENT '�Ƿ�Ϊ����Ա 0:�ǹ���Ա��1:����Ա',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='�û�';

CREATE TABLE IF NOT EXISTS  `mission_group` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(128) COMMENT '����',
  `description` varchar(2048) COMMENT '����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='������';

CREATE TABLE IF NOT EXISTS  `mission` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) COMMENT 'agv�ı��',
  `agv_group_id` varchar(256) COMMENT 'agv���id',
  `agv_type` varchar(256) COMMENT 'agv����',
  `name` varchar(128) COMMENT '����',
  `code` varchar(255) COMMENT '������',
  `description` varchar(2048) COMMENT '����',
  `mission_group_id` varchar(128) COMMENT '����������',
  `sequence` int DEFAULT '2' COMMENT '���ȼ�,1:�ͣ�2����ͨ��3���ߣ�4�����. Ĭ����2',
  `version` bigint(20) DEFAULT 0 COMMENT '�汾��',
  `order_by` int DEFAULT 1 COMMENT '��������',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����';

CREATE TABLE  IF NOT EXISTS `mission_action` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_id` varchar(256) COMMENT '��������id',
  `name` varchar(128) COMMENT '����',
  `action_type` varchar(256) COMMENT '��������',
  `sequence` int COMMENT '˳����',
  `description` varchar(2048) COMMENT '����',
  `is_sub_action` boolean DEFAULT false COMMENT '�Ƿ����Ӷ���',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='������';

CREATE TABLE IF NOT EXISTS  `mission_group` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(128) COMMENT '����',
  `description` varchar(2048) COMMENT '����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='������';

CREATE TABLE IF NOT EXISTS `schedule_plan` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_id` varchar(256) COMMENT '����id',
  `agv_code` varchar(256) COMMENT 'Agv ���',
  `name` varchar(256) COMMENT '���ȼƻ�����',
  `cron` varchar(256) COMMENT 'ʱ����ʽ',
  `description` varchar(2048) COMMENT '����',
  `complete_frequency` int(11) COMMENT '�Ѿ���ɴ���',
  `frequency` int(11) COMMENT 'simpleTrigger �ظ�����',
  `execute_interval` int  DEFAULT '1' COMMENT 'simpleTrigger ִ�м��(��λ:��)',
  `status` varchar(128) COMMENT '״̬',
  `callback_url` varchar(128) COMMENT '�ص��ӿ�',
  `execute_over_create_new` tinyint(1) DEFAULT '0' COMMENT 'ִ�з�ʽ 0:һ����ȫ������missionWork 1:�𲽴�������һ��missionWorkִ����ɺ��ٴ�����һ����',
  `execute_time` datetime COMMENT 'ִ��ʱ��',
  `start_time` datetime COMMENT '��ʼʱ��',
  `end_time` datetime COMMENT '����ʱ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ִ�мƻ�';

CREATE TABLE IF NOT EXISTS `mission_action_parameter` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_action_id` varchar(256) COMMENT '������id',
  `parameter_key` varchar(256) COMMENT '����key',
  `parameter_value` varchar(10240) COMMENT '����ֵ',
  `parameter_value_type` varchar(32) COMMENT '����ֵ����',
  `parameter_type` varchar(32) COMMENT '����ֵ����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='������������';


CREATE TABLE IF NOT EXISTS `schedule_plan_run_parameter` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_action_id` varchar(256) COMMENT '������id',
  `schedule_plan_id` varchar(256) COMMENT '���ȼƻ�id',
  `parameter_key` varchar(256) COMMENT '����key',
  `parameter_value` varchar(10240) COMMENT '����ֵ',
  `parameter_value_type` varchar(32) COMMENT '����ֵ����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����Ķ�̬������';

CREATE TABLE IF NOT EXISTS `mission_work_action_parameter` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_work_action_id` varchar(256) COMMENT '������id',
  `parameter_key` varchar(256) COMMENT '����key',
  `parameter_value` varchar(10240) COMMENT '����ֵ',
  `type` varchar(256) COMMENT '��������',
  `parameter_type` varchar(256) COMMENT '��������',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='��ҵ��������';

CREATE TABLE IF NOT EXISTS `runtime_parameter` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_work_id` varchar(256) COMMENT '������id',
  `parameter_key` varchar(256) COMMENT '����key',
  `parameter_value` varchar(10240) COMMENT '����ֵ',
  `type` varchar(256) COMMENT '��������',
  `parameter_type` varchar(256) COMMENT '��������',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='��������ʱ����';

CREATE TABLE IF NOT EXISTS `camera_preset_param` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(256) COMMENT '����',
  `osd_str` varchar(256) COMMENT 'OSD�ַ�����(ͼƬˮӡ)',
  `p_bright_value` int(11) COMMENT '����ָ��,ȡֵ��Χ[1,10]',
  `p_contrast_value` int(11) COMMENT '�Աȶ�ָ��,ȡֵ��Χ[1,10]',
  `p_saturation_value` int(11) COMMENT '���Ͷ�ָ��,ȡֵ��Χ[1,10]',
  `by_exposure_mode_set` int(11) COMMENT '������ع�ģʽ��0-�ֶ�ģʽ��1-�Զ��ع⣬2-��Ȧ���ȣ�3-�������ȣ�4-��������',
  `by_shutter_set` int(11) COMMENT '���ŵȼ�',
  `by_max_shutter_set` int(11) COMMENT '������ֵ',
  `by_min_shutter_set` int(11) COMMENT '��С����ֵ',
  `by_max_iris_set` int(11) COMMENT '����Ȧ����ֵ(���Զ��ع�ģʽ����Ч)��ȡֵ��Χ��[0,100]',
  `by_min_iris_set` int(11) COMMENT '��С��Ȧ����ֵ(���Զ��ع�ģʽ����Ч)��ȡֵ��Χ��[0,100]',
  `by_focus_mode` int(11) COMMENT '�۽�ģʽ��0-�Զ���1-�ֶ���2-���Զ�',
  `w_zoom_pos` int(11) COMMENT 'Z�������䱶������ ȡֵ��Χ10-666',
  `i_iris_set` int(11) COMMENT '��Ȧ��Ϊʵ��ȡֵ*100��ֵ��0��ʾ�رչ�Ȧ',
  `xml_focus` int(11) COMMENT '�ɼ��ⵥĿ����ͷ��������',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����ͷԤ�ò���';

CREATE TABLE IF NOT EXISTS `manipulator_arm_preset_param` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(256) COMMENT '����',
  `joints1` double COMMENT '��һ���ؽڽǶ�',
  `joints2` double COMMENT '�ڶ����ؽڽǶ�',
  `joints3` double COMMENT '�������ؽڽǶ�',
  `joints4` double COMMENT '���ĸ��ؽڽǶ�',
  `joints5` double COMMENT '������ؽڽǶ�',
  `joints6` double COMMENT '�������ؽڽǶ�',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����ͷԤ�ò���';

CREATE TABLE IF NOT EXISTS `sys_sequence` (
  `seq_name` varchar(50) CHARACTER SET latin1 COLLATE latin1_bin NOT NULL,
  `min_value` int(11) NOT NULL,
  `max_value` int(11) NOT NULL,
  `current_value` int(11) NOT NULL,
  `increment_value` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`seq_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS `mission_global_variable` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_id` varchar(256) COMMENT '����ID',
  `variable_key` varchar(256) COMMENT '������',
  `variable_value` varchar(1024) COMMENT '����ֵ',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����ȫ�ֱ���';

CREATE TABLE IF NOT EXISTS `mission_work_global_variable` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_id` varchar(256) COMMENT '����ID',
  `mission_work_id` varchar(256) COMMENT '����ID',
  `variable_key` varchar(256) COMMENT '������',
  `variable_value` varchar(1024) COMMENT '����ֵ',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='����ȫ�ֱ���';

CREATE TABLE IF NOT EXISTS `license` (
  `id` varchar(256) NOT NULL,
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '��Ч��ʼʱ��',
  `end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '��Ч����ʱ��',
  `server_type` varchar(50) NOT NULL COMMENT '�����������ͣ�YOUIFleet��YOUIDrive��YOUIINS��YOUITMS',
  `company_name` varchar(256) NOT NULL COMMENT '��˾����',
  `file_id` varchar(50) NOT NULL UNIQUE COMMENT '�ļ�����/�ļ�Ψһid',
  `expire` int(2) default 0 COMMENT '�Ƿ���� 0:δ����, 1:����',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  KEY `license_file_id` (`file_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='license��Ȩ��Ϣ��';

CREATE TABLE IF NOT EXISTS `language` (
  `id` varchar(256) NOT NULL,
  `current_use` varchar(256) default 'CHINESE' COMMENT '��ǰʹ������, ����:CHINESE  Ӣ��:ENGLISH',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ʹ������';

CREATE TABLE IF NOT EXISTS `operation_record` (
  `id` varchar(256) NOT NULL,
  `agv_code` varchar(256) COMMENT '�����˱��',
  `agv_name` varchar(256) COMMENT '����������',
  `operation_type` int(11) NOT NULL COMMENT '��������: 0������1ά��',
  `description` text COMMENT '������Ϣ',
  `operation_name` varchar(256) NOT NULL COMMENT '����/������Ա',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����/����ʱ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  KEY `agv_code` (`agv_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='������ά��������¼';

CREATE TABLE IF NOT EXISTS `logo_info` (
  `id` varchar(256) NOT NULL,
  `company_name` varchar(256) COMMENT '��˾����',
  `version` varchar(256) COMMENT 'ϵͳ�汾',
  `login_title` varchar(256) DEFAULT 'YOUIFleet' COMMENT '��ҳ����',
  `login_title_font_size` int DEFAULT 36 COMMENT '��¼ҳ���������С',
  `enterprise_url` varchar(256) DEFAULT 'https://www.youibot.com/' COMMENT '��˾��ַ',
  `version_owner` varchar(256) COMMENT '��Ȩ����',
  `logo_image` longtext COMMENT 'logoͼƬ��ͼƬbase64��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='logo��Ϣ��';

CREATE TABLE IF NOT EXISTS `system_config` (
  `id` varchar(256) NOT NULL,
  `mission_work` int(11) COMMENT '������־ ��λ:��',
  `log_file` int(11) COMMENT 'ϵͳ��־�ļ� ��λ:��',
  `system_notify` int(11) COMMENT 'ϵͳ֪ͨ ��λ:��',
  `map_file` int(11) COMMENT '��ͼ�ļ� ��λ:��',
  `time_zone` varchar(256) COMMENT 'ʱ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ϵͳ����';

CREATE TABLE IF NOT EXISTS `agv_statistics` (
  `id` varchar(256) NOT NULL,
  `agv_code` varchar(256) DEFAULT NULL COMMENT 'agv�ı��',
  `work_time` bigint(20) DEFAULT '0' COMMENT '����ʱ�� ��λ:s',
  `free_time` bigint(20) DEFAULT '0' COMMENT '����ʱ�� ��λ:s',
  `on_time` bigint(20) DEFAULT '0' COMMENT '������ʱ�� ��λ:s',
  `mission_count` int(11) DEFAULT '0' COMMENT '��������',
  `mission_finish_count` int(11) DEFAULT '0' COMMENT '���������',
  `mission_cancel_count` int(11) DEFAULT '0' COMMENT 'ȡ��������',
--   `charge_count` int(11) DEFAULT '0' COMMENT '����ܴ���',
--   `charge_time` bigint(20) DEFAULT '0' COMMENT '�����ʱ�� ��λ:s',
  `data_type` int(11) DEFAULT '0' COMMENT '��λ: 1:day,7:week,30:month',
  `belong_time` bigint(20) DEFAULT '0' COMMENT '���ݹ���ʱ��59:59ʱ������գ���ǰ���ڣ��ܣ���ǰ�ܵ�һ�죬�£���ǰ�µ�һ�죻���У�0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  KEY `create_time` (`create_time`),
  KEY `agv_code` (`agv_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='avgͳ�Ʊ�';

CREATE TABLE IF NOT EXISTS `agv_log` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) COMMENT 'agv�ı��',
  `type` int(11) COMMENT '����: 1:����,2:����',
  `mission_work_id` varchar(256) COMMENT '����typeΪ2ʱ:�ò�����ֵ',
  `start_time` bigint(20) COMMENT '��ʼʱ��',
  `end_time` bigint(20) COMMENT '����ʱ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  KEY `create_time` (`create_time`),
  KEY `agv_code` (`agv_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='��������־��Ϣ';


CREATE TABLE IF NOT EXISTS `mq_operate_result_info` (
  `id` varchar(256) NOT NULL,
  `agv_code` varchar(256) NOT NULL COMMENT 'agv�ı��',
  `operate_type` varchar(50) NOT NULL COMMENT '������������',
  `result` tinyint DEFAULT '0' COMMENT '�Ƿ�ɹ���0���ɹ���1��ʧ��',
  `message` varchar(1024) DEFAULT '' COMMENT '������Ϣ',
  `have_read` tinyint DEFAULT '0' COMMENT '�Ƿ��Ѷ���0��δ����1���Ѷ�',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  KEY `agv_operate_id`(`agv_code`, `operate_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='mq���������Ϣ';

CREATE TABLE IF NOT EXISTS `mq_message` (
  `id` varchar(256) NOT NULL,
  `topic` varchar(128) NOT NULL COMMENT '���}����',
  `message` longtext NOT NULL COMMENT 'mq��Ϣ',
  `consume` tinyint DEFAULT '0' COMMENT '�Ƿ����ѣ�0��δ���ѣ�1��������',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='mq������Ϣ';

CREATE TABLE IF NOT EXISTS `work_scheduler` (
  `id` INT(20) not null AUTO_INCREMENT COMMENT 'ID',
  `vehicle_id` varchar(256) COMMENT 'agv id',
  `work_id` varchar(256) COMMENT '����ID',
  `status` varchar(256) DEFAULT 'CREATE' COMMENT  '״̬��CREATE,START,CANCEL,SUCCESS,FAULT',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `start_time` datetime COMMENT '��ʼʱ��',
  `finish_time` datetime COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='��ҵ�������';

CREATE TABLE IF NOT EXISTS `charge_scheduler` (
  `id` INT(20) not null AUTO_INCREMENT COMMENT 'ID',
  `vehicle_id` varchar(256) COMMENT 'agv id',
  `charge_id` varchar(256) COMMENT '����ID',
  `status` varchar(256) DEFAULT 'CREATE' COMMENT  '״̬��CREATE,START,CANCEL,SUCCESS,FAULT',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `start_time` datetime COMMENT '��ʼʱ��',
  `finish_time` datetime COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='���������';

CREATE TABLE IF NOT EXISTS `park_scheduler` (
  `id` INT(20) not null AUTO_INCREMENT COMMENT 'ID',
  `vehicle_id` varchar(256) COMMENT 'vehicle id',
  `park_id` varchar(256) COMMENT '������ID',
  `status` varchar(256) DEFAULT 'CREATE' COMMENT  '״̬��CREATE,START,CANCEL,SUCCESS,FAULT',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `start_time` datetime COMMENT '��ʼʱ��',
  `finish_time` datetime COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='�����ֲ�����';


CREATE TABLE IF NOT EXISTS `scheduler_config` (
  `id` INT(20) not null AUTO_INCREMENT COMMENT 'ID',
  `low_batter_value` double DEFAULT '0' COMMENT '�͵���ֵ',
  `cancel_battery_value` double DEFAULT '0' COMMENT '��ȡ��������ֵ',
  `high_battery_value` double DEFAULT '0' COMMENT  '�����ߵ���ֵ',
  `free_charge_scope` double DEFAULT '0' COMMENT  '���г������',
  `distance_ratio` double DEFAULT '0' COMMENT  '�������ռ��',
  `battery_value_ratio` double DEFAULT '0' COMMENT  '��������ռ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='������ֵ����';

CREATE TABLE IF NOT EXISTS`auto_door` (
  `id` varchar(256) NOT NULL,
  `name` varchar(64) NOT NULL COMMENT '����',
  `ip` varchar(256) NOT NULL COMMENT '�Զ���ip',
  `port` int(11) NOT NULL COMMENT '�Զ��Ŷ˿�',
  `current_status` varchar(256) DEFAULT NULL COMMENT '���ѿ�:OPEN ���ѹ�:CLOSE ������:OPERATING ͨѶ�쳣:ERROR δ��·��:UNBOUND_PATH',
  `type` varchar(256) DEFAULT NULL COMMENT '���� AUTO_DOOR:�Զ��� AIR_SHOWER_DOOR:������',
  `position` varchar(256) DEFAULT NULL COMMENT 'λ�� ��type=AIR_SHOWER_DOORʱ��ֵ  FRONT:ǰ��  BACK:����',
  `relation_door_id` varchar(256) DEFAULT NULL COMMENT '������ID ��type=AIR_SHOWER_DOORʱ��ֵ',
  `residence_time` varchar(256) DEFAULT NULL COMMENT 'ͣ��ʱ�� ��λ:��  ��������Ҫ�����������ż�ͣ��һ��ʱ��',
  `last_close_door_time` datetime DEFAULT NULL COMMENT '���һ�ι������ʱ��',
  `control_mode` varchar(256) DEFAULT 'SCHEDULER' COMMENT '����ģʽ LOCAL:���ؿ��� SCHEDULER:���ȿ���',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='�Զ���';

CREATE TABLE IF NOT EXISTS`path_agv_type` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `path_id` varchar(256)  COMMENT '·��ID',
  `agv_type_ids` varchar(256)  COMMENT '����ID,����ö��Ÿ���',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`),
  UNIQUE(`path_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='·�����ͱ�';

CREATE TABLE IF NOT EXISTS `trigger_selector` (
  `id` varchar(128) NOT NULL COMMENT '����ID',
  `code` varchar(128) NOT NULL COMMENT '����',
  `name` varchar(32) NOT NULL COMMENT '����',
  `mission_id` varchar(128) NOT NULL COMMENT '����id',
  `type` int(11) NOT NULL COMMENT '�������� 1:��ʱ������2������������3����������',
  `pager_id` varchar(128) DEFAULT NULL COMMENT '������ID',
  `start_type` int(11) DEFAULT NULL COMMENT '��ʼ���� 1:��ʱ��2:��ʱ',
  `start_time` datetime DEFAULT NULL COMMENT '��ʼʱ��',
  `period` int(11) DEFAULT NULL COMMENT '�������',
  `unit` varchar(32) DEFAULT NULL COMMENT '���������ʱ�䵥λ SENCOND��DAY��WEEK��MONTH',
  `sensor_json` text COMMENT '������JSON,[{"sensorId":"xxxx","value":1}]',
  `device_address` int(11) DEFAULT NULL COMMENT '�豸��ַ',
  `button_address` int(11) DEFAULT NULL COMMENT '��ť��ַ',
  `execute_times` int(11) DEFAULT '1' COMMENT 'ִ�д���',
  `completed_times` int(11) DEFAULT NULL COMMENT '����ɴ���',
  `is_disabled` int(11) NOT NULL DEFAULT '0' COMMENT '�Ƿ����',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '�޸�ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='��������';


CREATE TABLE IF NOT EXISTS `sensor` (
  `id` varchar(256) NOT NULL COMMENT '����ID',
  `code` varchar(255) NOT NULL COMMENT '���',
  `name` varchar(255) NOT NULL COMMENT '����',
  `ip` varchar(255) NOT NULL COMMENT 'IP��ַ',
  `port` varchar(255) NOT NULL COMMENT '�˿�',
  `function_code` varchar(255) NOT NULL COMMENT '������',
  `slave_id` varchar(255) NOT NULL COMMENT '��վID',
  `start_address` varchar(255) NOT NULL COMMENT '��ʼ��ַ',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '״̬,0δ������1�Ѵ���',
  `is_disabled` int unsigned NOT NULL DEFAULT '0' COMMENT '�Ƿ����,0δ���á�1�ѽ���',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='��������';

CREATE TABLE IF NOT EXISTS `abnormal_prompt`(
  `id`  VARCHAR(256) NOT NULL,
  `abnormal_level` int  COMMENT '�ȼ� 1����ͨ 2������ 3������',
  `abnormal_type` VARCHAR(256)  COMMENT '����',
  `abnormal_code` int  COMMENT '�쳣����',
  `abnormal_description` VARCHAR(1024)  COMMENT '����',
  `help` VARCHAR(2048) COMMENT '������',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
)	ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='�쳣��ʾ';


CREATE TABLE IF NOT EXISTS `agv_map_update_queue` (
  `id` varchar(256) NOT NULL,
  `status` varchar(256) NOT NULL COMMENT '���״̬��create��running��finish��failed',
  `map_name` varchar(256) DEFAULT NULL COMMENT '��ͼ����',
  `type` varchar(256) NOT NULL COMMENT '����:current_2_memory��tmp_2_current��draft_2_current',
  `level` varchar(256) NOT NULL COMMENT '���ȼ���1���ͣ���2����ͨ����3���ߣ�',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='��ͼ���¶���';

CREATE TABLE IF NOT EXISTS `foreign_action` (
  `id` varchar(256) NOT NULL,
  `agv_code` varchar(256)  COMMENT 'agv�ı��',
  `action_type` varchar(256)  COMMENT '��������',
  `parameters` varchar(2048) COMMENT '��������',
  `status` varchar(256) COMMENT '״̬ START:��ʼִ�� RUNNING:ִ���� SUCCESS:ִ�гɹ� FAULT:ִ�д���',
  `result_code` int COMMENT '���ر��� 1001:��ȷ���� ����Ϊ�������',
  `result_data` varchar(2048) COMMENT '��������������',
  `call_back_url` varchar(256) COMMENT '�ص�Url',
  `start_time` datetime COMMENT '��ʼʱ��',
  `end_time` datetime COMMENT '����ʱ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='�ⲿ�������ñ�';

CREATE TABLE IF NOT EXISTS `map_element_config` (
  `id` varchar(256) NOT NULL,
  `map_name` varchar(256)  COMMENT '��ͼ����',
  `m_width_height` double COMMENT '��ǵ���',
  `line_Width_Height` double COMMENT '·���Ŀ��',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='��ͼԪ�����ñ�';

alter table `scheduler_config` add park_scheduler_enable TINYINT(1) DEFAULT 1 COMMENT '�Ƿ����ò������ȣ�0�ǽ��ã�1������' after `battery_value_ratio`;
alter table `scheduler_config` add charge_scheduler_enable TINYINT(1) DEFAULT 1 COMMENT '�Ƿ����ó����ȣ�0�ǽ��ã�1������' after `park_scheduler_enable`;
alter table `scheduler_config` add park_scheduler_interval int DEFAULT 0 COMMENT '�������ȼ��ʱ�䣬��λ���롣' after `charge_scheduler_enable`;
alter table `scheduler_config` add charge_scheduler_interval int DEFAULT 0 COMMENT '�����ȼ��ʱ�䣬��λ���롣' after `park_scheduler_interval`;
alter table `scheduler_config` add block_check_enable TINYINT(1) DEFAULT 1 COMMENT '�Ƿ��������Ϲ��ܣ�0�ǽ��ã�1������' after `charge_scheduler_interval`;
alter table `scheduler_config` add block_check_interval int DEFAULT 30 COMMENT '�ϰ�ʶ��ʱ�䣬��λ���롣' after `block_check_enable`;
alter table `scheduler_config` add remove_block_interval int DEFAULT 1800 COMMENT 'Ĭ���Ƴ��ϰ���·��Ȩ�أ�ʱ�䣬��λ���롣' after `block_check_interval`;
alter table `scheduler_config` add minimum_charge_time int DEFAULT 600 COMMENT '��̳��ʱ��, ��λ���롣' after `remove_block_interval`;
alter table `scheduler_config` add correct_charge_interval int DEFAULT 168 COMMENT 'У�������, ��λ��Сʱ��' after `minimum_charge_time`;
alter table `scheduler_config` add maximum_correct_charge_num int DEFAULT 1 COMMENT 'ͬʱ����У���������˵����������' after `correct_charge_interval`;
alter table `scheduler_config` add time_ratio double DEFAULT -1 COMMENT '����ִ��ʱ�����ֵĻ���ռ��' after `maximum_correct_charge_num`;
alter table `scheduler_config` add mission_work_spend_time int DEFAULT 400 COMMENT '����ִ��ƽ��ʱ��, ��λ����' after `time_ratio`;
alter table `scheduler_config` add pre_mission_work_enable TINYINT(1) DEFAULT 1 COMMENT '�Ƿ�������ҵԤ���䣬0:�����ã�1:����' after `mission_work_spend_time`;
alter table `scheduler_config` add across_charge_enable TINYINT(1) DEFAULT 0 COMMENT '�Ƿ���ͼ��磬0:�����ã�1:����' after `pre_mission_work_enable`;
alter table `scheduler_config` add across_park_enable TINYINT(1) DEFAULT 0 COMMENT '�Ƿ���ͼ������0:�����ã�1:����' after `across_charge_enable`;

alter table `work_scheduler` add work_name varchar(128) COMMENT '��������' after `work_id`;
alter table `work_scheduler` add fault_message text COMMENT '�쳣��Ϣ' after `status`;
alter table `charge_scheduler` add charge_point_code varchar(256) COMMENT '���վ����' after `charge_id`;
alter table `charge_scheduler` add fault_message text COMMENT '�쳣��Ϣ' after `status`;
alter table `charge_scheduler` add charge_type TINYINT(1) DEFAULT 0 COMMENT '������ͣ�0:��ͨ��磬1:У����' after `charge_point_code`;
alter table `charge_scheduler` add  charge_point_map_name varchar(256) COMMENT '���վ���ͼ����' after `charge_type`;
alter table `charge_scheduler` add  trigger_type int  COMMENT '�������ͣ�0���͵������� 2:���д��� 3:ǿ�Ƴ�紥��' after `charge_point_map_name`;
alter table `park_scheduler` add park_point_code varchar(256) COMMENT '����վ����' after `park_id`;
alter table `park_scheduler` add fault_message text COMMENT '�쳣��Ϣ' after `status`;

alter table `mission` add is_deleted TINYINT(1) DEFAULT 0 COMMENT '0δɾ��,1ɾ��';
alter table `mission_action` add is_deleted TINYINT(1) DEFAULT 0 COMMENT '0δɾ��,1ɾ��';
alter table `mission` add warning_battery DOUBLE(5,2) COMMENT 'Ԥ������,���ڸ�ֵ�����������';

alter table `agv` add map_status tinyint(1) unsigned DEFAULT NULL DEFAULT '0' COMMENT '��ͼͬ��״̬,0δͬ����1ͬ��';
alter table `agv` add control_mode tinyint(1) unsigned DEFAULT NULL COMMENT '����ģʽ,1�Զ�ģʽ��2�ֶ�ģʽ';
alter table `agv` add appoint_status tinyint(1) unsigned DEFAULT NULL COMMENT '��ͼָ��״̬,0δָ����1��ָ��';
alter table `agv` add abnormal_status tinyint(1) DEFAULT NULL COMMENT '�쳣״̬,1���쳣��2�����쳣��3����쳣��4��λ�쳣';
alter table `agv` add work_status tinyint(1) DEFAULT NULL COMMENT '����״̬,1���С�2����3��硢4��λ';
alter table `agv` add auto_charge tinyint(1) DEFAULT 2 COMMENT '�������Զ����,0�ر�,1����,2Ĭ��';
alter table `agv` add auto_park tinyint(1) DEFAULT 2 COMMENT '�������Զ�����,0�ر�,1����,2Ĭ��';
alter table `agv` add ip varchar(256) COMMENT '������IP��ַ';

alter table `agv` add auto_allocation tinyint(1) DEFAULT  1 COMMENT '�Ƿ��Զ���������0:�رգ�1:����';
alter table `agv` add bind_charge_config boolean DEFAULT false COMMENT '�Ƿ����󶨳������� 0���ر� 1:����' after `auto_allocation`;
alter table `agv` add bind_charge_markers varchar(1024) default null COMMENT '�󶨵ĳ���, ����Զ��Ÿ���' after `bind_charge_config`;
alter table `agv` add bind_park_config boolean DEFAULT false COMMENT '�Ƿ����󶨲��������� 0���ر� 1:����' after `bind_charge_markers`;
alter table `agv` add bind_park_markers varchar(1024) default null COMMENT '�󶨵Ĳ�����, ����Զ��Ÿ���'  after `bind_park_config`;

alter table `mq_message` add message_id bigint(20) DEFAULT NULL COMMENT '��Ϣid' after id;
alter table `system_config` add mq_message int(11) DEFAULT NULL COMMENT 'mq��Ϣ' after system_notify;

-- index
ALTER TABLE `mission_work` ADD INDEX status_index (`status`);
ALTER TABLE `mission_work` ADD INDEX create_time_index (`create_time`);
ALTER TABLE `mission_work` ADD INDEX sequence_index (`sequence`);
ALTER TABLE `mission_action` ADD INDEX mission_id_index (`mission_id`);
ALTER TABLE `mission_action_parameter` ADD INDEX mission_action_id_index (`mission_action_id`);
ALTER TABLE `mission_global_variable` ADD INDEX mission_id_index (`mission_id`);
ALTER TABLE `mission_work_global_variable` ADD INDEX mission_work_id_index (`mission_work_id`);
ALTER TABLE `work_scheduler` ADD INDEX work_id_status_index(`work_id`,`status`);
ALTER TABLE `work_scheduler` ADD INDEX vehicle_id_status_index(`vehicle_id`,`status`);
ALTER TABLE `work_scheduler` ADD INDEX status_index(`status`);
ALTER TABLE `work_scheduler` ADD INDEX work_id_index(`work_id`);
ALTER TABLE `work_scheduler` ADD INDEX vehicle_id_index(`vehicle_id`);
ALTER TABLE `charge_scheduler` ADD INDEX vehicle_id_index(`vehicle_id`);
ALTER TABLE `charge_scheduler` ADD INDEX status_index(`status`);
ALTER TABLE `charge_scheduler` ADD INDEX vehicle_id_status_index(`vehicle_id`,`status`);
ALTER TABLE `charge_scheduler` ADD INDEX charge_type_status_index(`charge_type`,`status`);
ALTER TABLE `park_scheduler` ADD INDEX vehicle_id_index(`vehicle_id`);
ALTER TABLE `park_scheduler` ADD INDEX status_index(`status`);
ALTER TABLE `park_scheduler` ADD INDEX vehicle_id_status_index(`vehicle_id`,`status`);
ALTER TABLE `mq_message` ADD INDEX consume_index(`consume`);
ALTER TABLE `mq_message` ADD COLUMN unique_flag varchar(256) unique COMMENT 'Ψһ��־, ����mqtt��Ϣȥ��, UUID' after `consume`;

ALTER TABLE `floor`
add COLUMN `status_address`  tinyint(5)  DEFAULT 0 COMMENT '��״̬��ַ' AFTER `number`,
add COLUMN `operate_address`  tinyint(5)  DEFAULT 0 COMMENT '�Ų�����ַ' AFTER `status_address`,
ADD COLUMN `read_function_code`  char(4) NULL COMMENT '��modbus������' AFTER `number`,
ADD COLUMN `open_status_value`  int DEFAULT 1  COMMENT '����״ֵ̬' AFTER `status_address`,
ADD COLUMN `close_status_value`  int DEFAULT 0  COMMENT '����״ֵ̬' AFTER `open_status_value`,
ADD COLUMN `write_function_code`  char(4) NULL COMMENT 'дmodbus������' AFTER `close_status_value`,
ADD COLUMN `operate_out_open_value`  int  DEFAULT 1 COMMENT '�����������ֵ' AFTER `operate_address`,
ADD COLUMN `operate_in_open_value`  int  DEFAULT 2 COMMENT '�����ں�����ֵ' AFTER `operate_out_open_value`,
ADD COLUMN `operate_close_value`  int DEFAULT 0 COMMENT '��������ֵ' AFTER `operate_in_open_value`,
ADD COLUMN `elevator_arrive_address` int NULL COMMENT '���ݵ�λ��ַ' AFTER `operate_close_value`;

alter table `notification` add help varchar(2048) DEFAULT NULL COMMENT '������' after `scale`;
alter table `notification` add error_code varchar(2048) DEFAULT NULL COMMENT '�������' after `help`;



alter table mission_action
add column `pre_Action` varchar (256) COMMENT '��һ������' after `action_type`,
add column `next_Action` varchar (256) COMMENT '��һ������' after `pre_Action`,
add column `parent_action_id` varchar (256) COMMENT '���ද��Id' after `action_type`,
add column `child_type` varchar (256) COMMENT '�Ӷ������� 1��IF 2��ELSE 3��WHILE' after `next_action`;

alter table mission_work_action
add column `parent_action_id` varchar (256) COMMENT '���ද��Id' after `error_code`,
add column `child_type` varchar (256) COMMENT '�Ӷ������� 1��IF 2��ELSE 3��WHILE' after `parent_action_id`;

alter table system_config
   add column `ftp_url` varchar (256) COMMENT 'ftp������ַ' after `time_zone`;
alter table `auto_door` ADD COLUMN `open_address` int(11) NOT NULL COMMENT '���ŵ�ַ' after `port`;
alter table `auto_door` ADD COLUMN `open_status_address` int(11) NOT NULL COMMENT '����״̬��ַ' after `open_address`;
alter table `auto_door` ADD COLUMN `close_address` int(11) COMMENT '���ŵ�ַ' after `open_status_address`;
alter table `auto_door` ADD COLUMN `close_status_address` int(11)  COMMENT '����״̬��ַ' after `close_address`;



alter table `abnormal_prompt` ADD COLUMN `en_desc` varchar(1024) COMMENT 'Ӣ������'  after `update_time`;

alter table `abnormal_prompt` ADD COLUMN `en_slolution` varchar(1024) COMMENT 'Ӣ������'  after `update_time`;


alter table `scheduler_config` add enable_cycle bit(1) DEFAULT b'1' COMMENT '�Ƿ���������Ѳ�ߣ�0:�����ã�1:����' after `across_charge_enable`;

alter table `scheduler_config` add workers_count int(8) DEFAULT 1 COMMENT '��׼Ѳ������' after `across_charge_enable`;


alter table `agv` add line_patrol_mode bit(1) DEFAULT b'1' COMMENT '�Ƿ�Ѳ��' ;

alter table `agv` add line_patro_weight int(8) DEFAULT 0 COMMENT 'Ѳ��ƫ��' ;


DROP TABLE IF EXISTS `work_cycle_config`;
CREATE TABLE `work_cycle_config`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'վ��id',
  `station_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'վ������',
  `agv_map_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '��ͼid',
  `mark_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '��ͼ�ϵĵ�����',
  `seq` double(8, 2) NULL DEFAULT NULL COMMENT '��������',
  `reel` bit(1) NULL DEFAULT b'1' COMMENT '�Ƿ�����',
  `enabled` bit(1) NULL DEFAULT NULL COMMENT '�Ƿ�����',
  `create_time` timestamp(0) NULL DEFAULT NULL COMMENT '����ʱ��',
  `update_time` timestamp(0) NULL DEFAULT NULL COMMENT '����ʱ��',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


