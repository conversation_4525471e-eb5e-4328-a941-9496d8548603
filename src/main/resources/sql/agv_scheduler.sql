
CREATE DATABASE IF NOT EXISTS `agv_scheduler_v4.8.2` default charset utf8 COLLATE utf8_general_ci;
use `agv_scheduler_v4.8.2`;


CREATE TABLE IF NOT EXISTS `map_area_type` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(256) COMMENT '名称',
  `description` varchar(2048) COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='区域类型表';


CREATE TABLE IF NOT EXISTS `notification` (
  `id` varchar(256) NOT NULL,
  `description` varchar(2048) COMMENT '描述',
  `type` varchar(256) COMMENT '类型',
  `agv_code` varchar(256) COMMENT '机器人编号',
  `agv_name` varchar(256) COMMENT '机器人名称',
  `mission_work_id` varchar(256) COMMENT '任务Id',
  `mission_work_name` varchar(256) COMMENT '任务名称',
  `have_read` varchar(256) COMMENT '已读',
  `scale` int COMMENT '消息级别',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `have_read` (`have_read`) USING BTREE,
  KEY `agv_code` (`agv_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通知消息';

CREATE TABLE IF NOT EXISTS `elevator` (
  `id` varchar(256) NOT NULL,
  `name` varchar(256) COMMENT '名称',
  `description` varchar(2048) COMMENT '描述',
  `customer` varchar(256) COMMENT '客户 TAI_CHI:太极',
  `ip` varchar(2048) COMMENT '操作电梯的ip地址',
  `port` int(11) COMMENT '操作电梯的端口号',
  `apply_timeout` int(11) default 180 COMMENT '申请电梯超时时间 单位：秒',
  `control_timeout` int(11) default 120 COMMENT '控制电梯超时时间 单位：秒',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='电梯';

CREATE TABLE IF NOT EXISTS `floor` (
  `id` varchar(256) NOT NULL,
  `elevator_id` varchar(256) COMMENT '电梯ID',
  `agv_map_id` varchar(256) COMMENT '地图ID',
  `marker_id` varchar(2048) COMMENT '标记点ID, type=ELEVATOR_MARKER的标记点才可添加',
  `marker_code` varchar(255) COMMENT '标记点CODE' ,
  `number` int(11) COMMENT '楼层数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='楼层';

CREATE TABLE IF NOT EXISTS `record_path` (
  `id` varchar(256) NOT NULL,
  `agv_map_id` varchar(256) NOT NULL COMMENT '地图id',
  `name` varchar(256) COMMENT '名称',
  `positions` varchar(1024) NOT NULL COMMENT '路径坐标队列',
  `length` double NOT NULL COMMENT '长度',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='路径表';

CREATE TABLE IF NOT EXISTS `agv`  (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) NOT NULL DEFAULT '' COMMENT '机器人编号',
  `agv_id` varchar(256) DEFAULT '' COMMENT '机器人ID（登录密码）',
  `agv_name` varchar(256) NOT NULL DEFAULT '' COMMENT '名称',
  `navigation_type` varchar(50) NOT NULL DEFAULT '' COMMENT '导航类型：LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航',
  `agv_type` varchar(256) DEFAULT '' COMMENT '机器人类型',
  `agv_group_id` varchar(256) NOT NULL DEFAULT '' COMMENT '机器人所属组',
  `status` tinyint (1) NOT NULL DEFAULT 0 COMMENT 'agv启用状态 0:未启用,1:启用',
  `online_status` tinyint (1) DEFAULT 0 COMMENT 'agv上线状态 0:离线,1:在线,2:断线',
  `shape_info` longtext COMMENT '外型信息',
  `agv_color` varchar(256) COMMENT '机器人颜色',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `agv_code` (`agv_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Agv';

CREATE TABLE IF NOT EXISTS `agv_group`  (
  `id` varchar(256)  NOT NULL COMMENT '机器人组ID',
  `name` varchar(256) COMMENT '机器人组名称',
  `description` varchar(1024)  COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='agv_group';

CREATE TABLE IF NOT EXISTS `agv_type`  (
  `id` varchar(256)  NOT NULL COMMENT 'AGV类型ID',
  `code` varchar(256) COMMENT 'AGV类型',
  `name` varchar(256) COMMENT '类型名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE(`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='agv_type';

CREATE TABLE IF NOT EXISTS `mission_work` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) COMMENT 'agv的编号',
  `agv_name` varchar(256) COMMENT 'agv的名称',
  `mission_id` varchar(256) NOT NULL COMMENT '任务ID',
  `trigger_selector_id` varchar(256) COMMENT '触发器ID',
  `schedule_plan_id` varchar(256) COMMENT '调度计划id',
  `callback_url` varchar(256) COMMENT '回调url',
  `name` varchar(256) NOT NULL COMMENT '名称',
  `sequence` int DEFAULT '1' COMMENT '顺序编辑',
  `description` varchar(2048) COMMENT '描述',
  `status` varchar(256) COMMENT '状态：WAIT,START,RUNNING,SUCCESS,FAULT,PAUSE,SHUTDOWN',
  `message` text COMMENT '异常信息',
  `runtime_param` varchar(1024) COMMENT '运行时参数(json格式), 如{"marker1":"1001"}',
--  `mission_call_id` varchar(256) COMMENT '任务呼叫id',
  `agv_group_id` varchar(256) COMMENT 'AGV组ID',
  `mission_group_id` varchar(256) COMMENT '任务组ID',
  `agv_type` varchar(256) COMMENT 'AGV类型',
  `error_code` varchar(256) COMMENT '错误码',
--   `allocation_status` varchar(256) DEFAULT 'UNASSIGNED' COMMENT '分配状态 已分配：ASSIGNED  未分配：UNASSIGNED',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务执行';

CREATE TABLE IF NOT EXISTS `mission_work_log` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_work_id` varchar(256) NOT NULL COMMENT '作业ID',
  `agv_code` varchar(256) COMMENT 'agv的编号',
  `status` varchar(256) COMMENT '状态：WAIT,START,RUNNING,SUCCESS,FAULT,PAUSE,SHUTDOWN',
  `start_time` datetime COMMENT '开始时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务状态变更日志';


CREATE TABLE IF NOT EXISTS `mission_work_action` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) COMMENT 'agv的编号',
  `mission_action_id` varchar(256) COMMENT '任务动作id',
  `mission_work_id` varchar(256) COMMENT '所属任务ID',
  `action_type` varchar(256) COMMENT '动作类型',
  `name` varchar(128) COMMENT '名称',
  `message` varchar(1024) COMMENT '执行结果描述',
  `sequence` int COMMENT '顺序编号',
  `parameters` varchar(4096) COMMENT '参数',
  `status` varchar(128) COMMENT '状态：START,RUNNING,SUCCESS,FAULT,WAIT_RUNTIME_PARAM',
  `parameter_type` varchar(128) DEFAULT 'NORMAL_PARAMETER' COMMENT '参数类别(NORMAL_PARAMETER,RUNTIME_PARAMETER)',
  `result_code` varchar(128) DEFAULT NULL COMMENT '返回编码',
  `result_message` varchar(2048) DEFAULT NULL COMMENT '返回提示信息',
  `result_type` varchar(128) DEFAULT NULL COMMENT '返回数据区数据类型(MESSAGE,IMAGE)',
  `result_data` longtext COMMENT '返回的数据区内容',
  `error_code` varchar(256) COMMENT '错误码',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `action_sequence` int COMMENT '执行顺序编号',
  `this_loop_completes` boolean DEFAULT NULL COMMENT '作为循环的子action时，记录本次循环是否完成',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务执行动作';


CREATE TABLE  IF NOT EXISTS `dashboard` (
  `id` varchar(256) NOT NULL COMMENT 'id',
  `agv_map_id` varchar(256) COMMENT '地图id',
  `name` varchar(128) COMMENT '名称',
  `description` varchar(2048) COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='dashboard';

CREATE TABLE IF NOT EXISTS  `user` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(128) COMMENT '姓名',
  `user_name` varchar(128) COMMENT '用户名',
  `password` varchar(128) COMMENT '登陆密码',
  `email` varchar(128) COMMENT '邮箱',
  `phone` varchar(128) COMMENT '电话',
  `admin` tinyint(1) DEFAULT 0 COMMENT '是否为管理员 0:非管理员，1:管理员',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户';

CREATE TABLE IF NOT EXISTS  `mission_group` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(128) COMMENT '名称',
  `description` varchar(2048) COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务组';

CREATE TABLE IF NOT EXISTS  `mission` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) COMMENT 'agv的编号',
  `agv_group_id` varchar(256) COMMENT 'agv组的id',
  `agv_type` varchar(256) COMMENT 'agv类型',
  `name` varchar(128) COMMENT '名称',
  `code` varchar(255) COMMENT '任务编号',
  `description` varchar(2048) COMMENT '描述',
  `mission_group_id` varchar(128) COMMENT '所属任务组',
  `sequence` int DEFAULT '2' COMMENT '优先级,1:低，2：普通，3：高，4：最高. 默认是2',
  `version` bigint(20) DEFAULT 0 COMMENT '版本号',
  `order_by` int DEFAULT 1 COMMENT '任务排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务';

CREATE TABLE  IF NOT EXISTS `mission_action` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_id` varchar(256) COMMENT '所属任务id',
  `name` varchar(128) COMMENT '名称',
  `action_type` varchar(256) COMMENT '动作类型',
  `sequence` int COMMENT '顺序编号',
  `description` varchar(2048) COMMENT '描述',
  `is_sub_action` boolean DEFAULT false COMMENT '是否是子动作',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务动作';

CREATE TABLE IF NOT EXISTS  `mission_group` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(128) COMMENT '名称',
  `description` varchar(2048) COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务组';

CREATE TABLE IF NOT EXISTS `schedule_plan` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_id` varchar(256) COMMENT '任务id',
  `agv_code` varchar(256) COMMENT 'Agv 编号',
  `name` varchar(256) COMMENT '调度计划名称',
  `cron` varchar(256) COMMENT '时间表达式',
  `description` varchar(2048) COMMENT '描述',
  `complete_frequency` int(11) COMMENT '已经完成次数',
  `frequency` int(11) COMMENT 'simpleTrigger 重复次数',
  `execute_interval` int  DEFAULT '1' COMMENT 'simpleTrigger 执行间隔(单位:秒)',
  `status` varchar(128) COMMENT '状态',
  `callback_url` varchar(128) COMMENT '回调接口',
  `execute_over_create_new` tinyint(1) DEFAULT '0' COMMENT '执行方式 0:一次性全部创建missionWork 1:逐步创建（上一个missionWork执行完成后再创建下一条）',
  `execute_time` datetime COMMENT '执行时间',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='执行计划';

CREATE TABLE IF NOT EXISTS `mission_action_parameter` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_action_id` varchar(256) COMMENT '任务动作id',
  `parameter_key` varchar(256) COMMENT '参数key',
  `parameter_value` varchar(10240) COMMENT '参数值',
  `parameter_value_type` varchar(32) COMMENT '参数值类型',
  `parameter_type` varchar(32) COMMENT '参数值类型',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务动作参数表';


CREATE TABLE IF NOT EXISTS `schedule_plan_run_parameter` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_action_id` varchar(256) COMMENT '任务动作id',
  `schedule_plan_id` varchar(256) COMMENT '调度计划id',
  `parameter_key` varchar(256) COMMENT '参数key',
  `parameter_value` varchar(10240) COMMENT '参数值',
  `parameter_value_type` varchar(32) COMMENT '参数值类型',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务的动态参数表';

CREATE TABLE IF NOT EXISTS `mission_work_action_parameter` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_work_action_id` varchar(256) COMMENT '任务动作id',
  `parameter_key` varchar(256) COMMENT '参数key',
  `parameter_value` varchar(10240) COMMENT '参数值',
  `type` varchar(256) COMMENT '数据类型',
  `parameter_type` varchar(256) COMMENT '参数类型',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作业动作参数';

CREATE TABLE IF NOT EXISTS `runtime_parameter` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_work_id` varchar(256) COMMENT '任务动作id',
  `parameter_key` varchar(256) COMMENT '参数key',
  `parameter_value` varchar(10240) COMMENT '参数值',
  `type` varchar(256) COMMENT '数据类型',
  `parameter_type` varchar(256) COMMENT '参数类型',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务运行时参数';

CREATE TABLE IF NOT EXISTS `camera_preset_param` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(256) COMMENT '名称',
  `osd_str` varchar(256) COMMENT 'OSD字符叠加(图片水印)',
  `p_bright_value` int(11) COMMENT '亮度指针,取值范围[1,10]',
  `p_contrast_value` int(11) COMMENT '对比度指针,取值范围[1,10]',
  `p_saturation_value` int(11) COMMENT '饱和度指针,取值范围[1,10]',
  `by_exposure_mode_set` int(11) COMMENT '球机的曝光模式：0-手动模式，1-自动曝光，2-光圈优先，3-快门优先，4-增益优先',
  `by_shutter_set` int(11) COMMENT '快门等级',
  `by_max_shutter_set` int(11) COMMENT '最大快门值',
  `by_min_shutter_set` int(11) COMMENT '最小快门值',
  `by_max_iris_set` int(11) COMMENT '最大光圈限制值(在自动曝光模式下生效)，取值范围：[0,100]',
  `by_min_iris_set` int(11) COMMENT '最小光圈限制值(在自动曝光模式下生效)，取值范围：[0,100]',
  `by_focus_mode` int(11) COMMENT '聚焦模式：0-自动，1-手动，2-半自动',
  `w_zoom_pos` int(11) COMMENT 'Z参数（变倍参数） 取值范围10-666',
  `i_iris_set` int(11) COMMENT '光圈，为实际取值*100的值，0表示关闭光圈',
  `xml_focus` int(11) COMMENT '可见光单目摄像头调整焦距',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='摄像头预置参数';

CREATE TABLE IF NOT EXISTS `manipulator_arm_preset_param` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `name` varchar(256) COMMENT '名称',
  `joints1` double COMMENT '第一个关节角度',
  `joints2` double COMMENT '第二个关节角度',
  `joints3` double COMMENT '第三个关节角度',
  `joints4` double COMMENT '第四个关节角度',
  `joints5` double COMMENT '第五个关节角度',
  `joints6` double COMMENT '第六个关节角度',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='摄像头预置参数';

CREATE TABLE IF NOT EXISTS `sys_sequence` (
  `seq_name` varchar(50) CHARACTER SET latin1 COLLATE latin1_bin NOT NULL,
  `min_value` int(11) NOT NULL,
  `max_value` int(11) NOT NULL,
  `current_value` int(11) NOT NULL,
  `increment_value` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`seq_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS `mission_global_variable` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_id` varchar(256) COMMENT '任务ID',
  `variable_key` varchar(256) COMMENT '变量键',
  `variable_value` varchar(1024) COMMENT '变量值',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务全局变量';

CREATE TABLE IF NOT EXISTS `mission_work_global_variable` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `mission_id` varchar(256) COMMENT '任务ID',
  `mission_work_id` varchar(256) COMMENT '工作ID',
  `variable_key` varchar(256) COMMENT '变量键',
  `variable_value` varchar(1024) COMMENT '变量值',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工作全局变量';

CREATE TABLE IF NOT EXISTS `license` (
  `id` varchar(256) NOT NULL,
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效开始时间',
  `end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效结束时间',
  `server_type` varchar(50) NOT NULL COMMENT '所属服务类型：YOUIFleet、YOUIDrive、YOUIINS、YOUITMS',
  `company_name` varchar(256) NOT NULL COMMENT '公司名称',
  `file_id` varchar(50) NOT NULL UNIQUE COMMENT '文件名称/文件唯一id',
  `expire` int(2) default 0 COMMENT '是否过期 0:未过期, 1:过期',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `license_file_id` (`file_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='license授权信息表';

CREATE TABLE IF NOT EXISTS `language` (
  `id` varchar(256) NOT NULL,
  `current_use` varchar(256) default 'CHINESE' COMMENT '当前使用语言, 中文:CHINESE  英文:ENGLISH',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='使用语言';

CREATE TABLE IF NOT EXISTS `operation_record` (
  `id` varchar(256) NOT NULL,
  `agv_code` varchar(256) COMMENT '机器人编号',
  `agv_name` varchar(256) COMMENT '机器人名称',
  `operation_type` int(11) NOT NULL COMMENT '操作类型: 0保养、1维修',
  `description` text COMMENT '描述信息',
  `operation_name` varchar(256) NOT NULL COMMENT '操作/保养人员',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作/保养时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `agv_code` (`agv_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='保养和维护操作记录';

CREATE TABLE IF NOT EXISTS `logo_info` (
  `id` varchar(256) NOT NULL,
  `company_name` varchar(256) COMMENT '公司名称',
  `version` varchar(256) COMMENT '系统版本',
  `login_title` varchar(256) DEFAULT 'YOUIFleet' COMMENT '网页标题',
  `login_title_font_size` int DEFAULT 36 COMMENT '登录页标题字体大小',
  `enterprise_url` varchar(256) DEFAULT 'https://www.youibot.com/' COMMENT '公司网址',
  `version_owner` varchar(256) COMMENT '版权所有',
  `logo_image` longtext COMMENT 'logo图片（图片base64）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='logo信息表';

CREATE TABLE IF NOT EXISTS `system_config` (
  `id` varchar(256) NOT NULL,
  `mission_work` int(11) COMMENT '任务日志 单位:天',
  `log_file` int(11) COMMENT '系统日志文件 单位:天',
  `system_notify` int(11) COMMENT '系统通知 单位:天',
  `map_file` int(11) COMMENT '地图文件 单位:天',
  `time_zone` varchar(256) COMMENT '时区',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统配置';

CREATE TABLE IF NOT EXISTS `agv_statistics` (
  `id` varchar(256) NOT NULL,
  `agv_code` varchar(256) DEFAULT NULL COMMENT 'agv的编号',
  `work_time` bigint(20) DEFAULT '0' COMMENT '工作时间 单位:s',
  `free_time` bigint(20) DEFAULT '0' COMMENT '空闲时间 单位:s',
  `on_time` bigint(20) DEFAULT '0' COMMENT '总在线时间 单位:s',
  `mission_count` int(11) DEFAULT '0' COMMENT '总任务数',
  `mission_finish_count` int(11) DEFAULT '0' COMMENT '完成任务数',
  `mission_cancel_count` int(11) DEFAULT '0' COMMENT '取消任务数',
--   `charge_count` int(11) DEFAULT '0' COMMENT '充电总次数',
--   `charge_time` bigint(20) DEFAULT '0' COMMENT '充电总时间 单位:s',
  `data_type` int(11) DEFAULT '0' COMMENT '单位: 1:day,7:week,30:month',
  `belong_time` bigint(20) DEFAULT '0' COMMENT '数据归属时间59:59时间戳，日：当前日期；周：当前周第一天，月：当前月第一天；所有：0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `create_time` (`create_time`),
  KEY `agv_code` (`agv_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='avg统计表';

CREATE TABLE IF NOT EXISTS `agv_log` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `agv_code` varchar(256) COMMENT 'agv的编号',
  `type` int(11) COMMENT '类型: 1:在线,2:工作',
  `mission_work_id` varchar(256) COMMENT '类型type为2时:该参数有值',
  `start_time` bigint(20) COMMENT '开始时间',
  `end_time` bigint(20) COMMENT '结束时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `create_time` (`create_time`),
  KEY `agv_code` (`agv_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='机器人日志信息';


CREATE TABLE IF NOT EXISTS `mq_operate_result_info` (
  `id` varchar(256) NOT NULL,
  `agv_code` varchar(256) NOT NULL COMMENT 'agv的编号',
  `operate_type` varchar(50) NOT NULL COMMENT '操作返回类型',
  `result` tinyint DEFAULT '0' COMMENT '是否成功：0：成功，1：失败',
  `message` varchar(1024) DEFAULT '' COMMENT '错误信息',
  `have_read` tinyint DEFAULT '0' COMMENT '是否已读：0：未读，1：已读',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `agv_operate_id`(`agv_code`, `operate_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='mq操作结果信息';

CREATE TABLE IF NOT EXISTS `mq_message` (
  `id` varchar(256) NOT NULL,
  `topic` varchar(128) NOT NULL COMMENT '主題名称',
  `message` longtext NOT NULL COMMENT 'mq消息',
  `consume` tinyint DEFAULT '0' COMMENT '是否消费：0：未消费，1：已消费',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='mq接收信息';

CREATE TABLE IF NOT EXISTS `work_scheduler` (
  `id` INT(20) not null AUTO_INCREMENT COMMENT 'ID',
  `vehicle_id` varchar(256) COMMENT 'agv id',
  `work_id` varchar(256) COMMENT '任务ID',
  `status` varchar(256) DEFAULT 'CREATE' COMMENT  '状态：CREATE,START,CANCEL,SUCCESS,FAULT',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `start_time` datetime COMMENT '开始时间',
  `finish_time` datetime COMMENT '结束时间',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作业分配队列';

CREATE TABLE IF NOT EXISTS `charge_scheduler` (
  `id` INT(20) not null AUTO_INCREMENT COMMENT 'ID',
  `vehicle_id` varchar(256) COMMENT 'agv id',
  `charge_id` varchar(256) COMMENT '充电点ID',
  `status` varchar(256) DEFAULT 'CREATE' COMMENT  '状态：CREATE,START,CANCEL,SUCCESS,FAULT',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `start_time` datetime COMMENT '开始时间',
  `finish_time` datetime COMMENT '结束时间',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='充电分配队列';

CREATE TABLE IF NOT EXISTS `park_scheduler` (
  `id` INT(20) not null AUTO_INCREMENT COMMENT 'ID',
  `vehicle_id` varchar(256) COMMENT 'vehicle id',
  `park_id` varchar(256) COMMENT '泊车点ID',
  `status` varchar(256) DEFAULT 'CREATE' COMMENT  '状态：CREATE,START,CANCEL,SUCCESS,FAULT',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `start_time` datetime COMMENT '开始时间',
  `finish_time` datetime COMMENT '结束时间',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='泊车分泊队列';


CREATE TABLE IF NOT EXISTS `scheduler_config` (
  `id` INT(20) not null AUTO_INCREMENT COMMENT 'ID',
  `low_batter_value` double DEFAULT '0' COMMENT '低电量值',
  `cancel_battery_value` double DEFAULT '0' COMMENT '可取消充电电量值',
  `high_battery_value` double DEFAULT '0' COMMENT  '充电最高电量值',
  `free_charge_scope` double DEFAULT '0' COMMENT  '空闲充电点分数',
  `distance_ratio` double DEFAULT '0' COMMENT  '距离基数占比',
  `battery_value_ratio` double DEFAULT '0' COMMENT  '电量基数占比',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='调度阈值配置';

CREATE TABLE IF NOT EXISTS`auto_door` (
  `id` varchar(256) NOT NULL,
  `name` varchar(64) NOT NULL COMMENT '名称',
  `ip` varchar(256) NOT NULL COMMENT '自动门ip',
  `port` int(11) NOT NULL COMMENT '自动门端口',
  `current_status` varchar(256) DEFAULT NULL COMMENT '门已开:OPEN 门已关:CLOSE 操作中:OPERATING 通讯异常:ERROR 未绑路径:UNBOUND_PATH',
  `type` varchar(256) DEFAULT NULL COMMENT '类型 AUTO_DOOR:自动门 AIR_SHOWER_DOOR:风淋门',
  `position` varchar(256) DEFAULT NULL COMMENT '位置 当type=AIR_SHOWER_DOOR时有值  FRONT:前门  BACK:后门',
  `relation_door_id` varchar(256) DEFAULT NULL COMMENT '关联门ID 当type=AIR_SHOWER_DOOR时有值',
  `residence_time` varchar(256) DEFAULT NULL COMMENT '停留时间 单位:秒  机器人需要在两道风淋门间停留一段时间',
  `last_close_door_time` datetime DEFAULT NULL COMMENT '最后一次关门完成时间',
  `control_mode` varchar(256) DEFAULT 'SCHEDULER' COMMENT '控制模式 LOCAL:本地控制 SCHEDULER:调度控制',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='自动门';

CREATE TABLE IF NOT EXISTS`path_agv_type` (
  `id` varchar(256) NOT NULL COMMENT 'ID',
  `path_id` varchar(256)  COMMENT '路径ID',
  `agv_type_ids` varchar(256)  COMMENT '车型ID,多个用逗号隔开',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE(`path_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='路径车型表';

CREATE TABLE IF NOT EXISTS `trigger_selector` (
  `id` varchar(128) NOT NULL COMMENT '主键ID',
  `code` varchar(128) NOT NULL COMMENT '编码',
  `name` varchar(32) NOT NULL COMMENT '名称',
  `mission_id` varchar(128) NOT NULL COMMENT '任务id',
  `type` int(11) NOT NULL COMMENT '触发类型 1:定时触发、2呼叫器触发、3传感器触发',
  `pager_id` varchar(128) DEFAULT NULL COMMENT '呼叫器ID',
  `start_type` int(11) DEFAULT NULL COMMENT '开始类型 1:即时、2:定时',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `period` int(11) DEFAULT NULL COMMENT '触发间隔',
  `unit` varchar(32) DEFAULT NULL COMMENT '触发间隔的时间单位 SENCOND、DAY、WEEK、MONTH',
  `sensor_json` text COMMENT '传感器JSON,[{"sensorId":"xxxx","value":1}]',
  `device_address` int(11) DEFAULT NULL COMMENT '设备地址',
  `button_address` int(11) DEFAULT NULL COMMENT '按钮地址',
  `execute_times` int(11) DEFAULT '1' COMMENT '执行次数',
  `completed_times` int(11) DEFAULT NULL COMMENT '已完成次数',
  `is_disabled` int(11) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='触发器表';


CREATE TABLE IF NOT EXISTS `sensor` (
  `id` varchar(256) NOT NULL COMMENT '主键ID',
  `code` varchar(255) NOT NULL COMMENT '编号',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `ip` varchar(255) NOT NULL COMMENT 'IP地址',
  `port` varchar(255) NOT NULL COMMENT '端口',
  `function_code` varchar(255) NOT NULL COMMENT '功能码',
  `slave_id` varchar(255) NOT NULL COMMENT '从站ID',
  `start_address` varchar(255) NOT NULL COMMENT '起始地址',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态,0未触发、1已触发',
  `is_disabled` int unsigned NOT NULL DEFAULT '0' COMMENT '是否禁用,0未禁用、1已禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='传感器表';

CREATE TABLE IF NOT EXISTS `abnormal_prompt`(
  `id`  VARCHAR(256) NOT NULL,
  `abnormal_level` int  COMMENT '等级 1：普通 2：警告 3：错误',
  `abnormal_type` VARCHAR(256)  COMMENT '类型',
  `abnormal_code` int  COMMENT '异常编码',
  `abnormal_description` VARCHAR(1024)  COMMENT '描述',
  `help` VARCHAR(2048) COMMENT '处理建议',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
)	ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='异常提示';


CREATE TABLE IF NOT EXISTS `agv_map_update_queue` (
  `id` varchar(256) NOT NULL,
  `status` varchar(256) NOT NULL COMMENT '完成状态：create、running、finish、failed',
  `map_name` varchar(256) DEFAULT NULL COMMENT '地图名称',
  `type` varchar(256) NOT NULL COMMENT '类型:current_2_memory、tmp_2_current、draft_2_current',
  `level` varchar(256) NOT NULL COMMENT '优先级：1（低）、2（普通）、3（高）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='地图更新队列';

CREATE TABLE IF NOT EXISTS `foreign_action` (
  `id` varchar(256) NOT NULL,
  `agv_code` varchar(256)  COMMENT 'agv的编号',
  `action_type` varchar(256)  COMMENT '动作类型',
  `parameters` varchar(2048) COMMENT '动作参数',
  `status` varchar(256) COMMENT '状态 START:开始执行 RUNNING:执行中 SUCCESS:执行成功 FAULT:执行错误',
  `result_code` int COMMENT '返回编码 1001:正确编码 其他为错误编码',
  `result_data` varchar(2048) COMMENT '返回数据区数据',
  `call_back_url` varchar(256) COMMENT '回调Url',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='外部动作调用表';

CREATE TABLE IF NOT EXISTS `map_element_config` (
  `id` varchar(256) NOT NULL,
  `map_name` varchar(256)  COMMENT '地图名称',
  `m_width_height` double COMMENT '标记点宽高',
  `line_Width_Height` double COMMENT '路径的宽高',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='地图元素配置表';

alter table `scheduler_config` add park_scheduler_enable TINYINT(1) DEFAULT 1 COMMENT '是否启用泊车调度，0是禁用，1是启用' after `battery_value_ratio`;
alter table `scheduler_config` add charge_scheduler_enable TINYINT(1) DEFAULT 1 COMMENT '是否启用充电调度，0是禁用，1是启用' after `park_scheduler_enable`;
alter table `scheduler_config` add park_scheduler_interval int DEFAULT 0 COMMENT '泊车调度间隔时间，单位是秒。' after `charge_scheduler_enable`;
alter table `scheduler_config` add charge_scheduler_interval int DEFAULT 0 COMMENT '充电调度间隔时间，单位是秒。' after `park_scheduler_interval`;
alter table `scheduler_config` add block_check_enable TINYINT(1) DEFAULT 1 COMMENT '是否启用绕障功能，0是禁用，1是启用' after `charge_scheduler_interval`;
alter table `scheduler_config` add block_check_interval int DEFAULT 30 COMMENT '障碍识别时间，单位是秒。' after `block_check_enable`;
alter table `scheduler_config` add remove_block_interval int DEFAULT 1800 COMMENT '默认移除障碍（路径权重）时间，单位是秒。' after `block_check_interval`;
alter table `scheduler_config` add minimum_charge_time int DEFAULT 600 COMMENT '最短充电时间, 单位是秒。' after `remove_block_interval`;
alter table `scheduler_config` add correct_charge_interval int DEFAULT 168 COMMENT '校正充电间隔, 单位是小时。' after `minimum_charge_time`;
alter table `scheduler_config` add maximum_correct_charge_num int DEFAULT 1 COMMENT '同时进行校正充电机器人的最大数量。' after `correct_charge_interval`;
alter table `scheduler_config` add time_ratio double DEFAULT -1 COMMENT '任务执行时间评分的基数占比' after `maximum_correct_charge_num`;
alter table `scheduler_config` add mission_work_spend_time int DEFAULT 400 COMMENT '任务执行平均时长, 单位是秒' after `time_ratio`;
alter table `scheduler_config` add pre_mission_work_enable TINYINT(1) DEFAULT 1 COMMENT '是否启用作业预分配，0:不启用，1:启用' after `mission_work_spend_time`;
alter table `scheduler_config` add across_charge_enable TINYINT(1) DEFAULT 0 COMMENT '是否跨地图充电，0:不启用，1:启用' after `pre_mission_work_enable`;
alter table `scheduler_config` add across_park_enable TINYINT(1) DEFAULT 0 COMMENT '是否跨地图泊车，0:不启用，1:启用' after `across_charge_enable`;

alter table `work_scheduler` add work_name varchar(128) COMMENT '工作名称' after `work_id`;
alter table `work_scheduler` add fault_message text COMMENT '异常信息' after `status`;
alter table `charge_scheduler` add charge_point_code varchar(256) COMMENT '充电站点编号' after `charge_id`;
alter table `charge_scheduler` add fault_message text COMMENT '异常信息' after `status`;
alter table `charge_scheduler` add charge_type TINYINT(1) DEFAULT 0 COMMENT '充电类型，0:普通充电，1:校验充电' after `charge_point_code`;
alter table `charge_scheduler` add  charge_point_map_name varchar(256) COMMENT '充电站点地图名称' after `charge_type`;
alter table `charge_scheduler` add  trigger_type int  COMMENT '触发类型，0：低电量触发 2:空闲触发 3:强制充电触发' after `charge_point_map_name`;
alter table `park_scheduler` add park_point_code varchar(256) COMMENT '泊车站点编号' after `park_id`;
alter table `park_scheduler` add fault_message text COMMENT '异常信息' after `status`;

alter table `mission` add is_deleted TINYINT(1) DEFAULT 0 COMMENT '0未删除,1删除';
alter table `mission_action` add is_deleted TINYINT(1) DEFAULT 0 COMMENT '0未删除,1删除';
alter table `mission` add warning_battery DOUBLE(5,2) COMMENT '预警电量,低于该值不可任务分配';

alter table `agv` add map_status tinyint(1) unsigned DEFAULT NULL DEFAULT '0' COMMENT '地图同步状态,0未同步、1同步';
alter table `agv` add control_mode tinyint(1) unsigned DEFAULT NULL COMMENT '控制模式,1自动模式、2手动模式';
alter table `agv` add appoint_status tinyint(1) unsigned DEFAULT NULL COMMENT '地图指定状态,0未指定、1已指定';
alter table `agv` add abnormal_status tinyint(1) DEFAULT NULL COMMENT '异常状态,1无异常、2工作异常、3充电异常、4归位异常';
alter table `agv` add work_status tinyint(1) DEFAULT NULL COMMENT '任务状态,1空闲、2任务、3充电、4归位';
alter table `agv` add auto_charge tinyint(1) DEFAULT 2 COMMENT '机器人自动充电,0关闭,1开启,2默认';
alter table `agv` add auto_park tinyint(1) DEFAULT 2 COMMENT '机器人自动泊车,0关闭,1开启,2默认';
alter table `agv` add ip varchar(256) COMMENT '机器人IP地址';

alter table `agv` add auto_allocation tinyint(1) DEFAULT  1 COMMENT '是否自动分配任务，0:关闭，1:开启';
alter table `agv` add bind_charge_config boolean DEFAULT false COMMENT '是否开启绑定充电点配置 0：关闭 1:开启' after `auto_allocation`;
alter table `agv` add bind_charge_markers varchar(1024) default null COMMENT '绑定的充电点, 多个以逗号隔开' after `bind_charge_config`;
alter table `agv` add bind_park_config boolean DEFAULT false COMMENT '是否开启绑定泊车点配置 0：关闭 1:开启' after `bind_charge_markers`;
alter table `agv` add bind_park_markers varchar(1024) default null COMMENT '绑定的泊车点, 多个以逗号隔开'  after `bind_park_config`;

alter table `mq_message` add message_id bigint(20) DEFAULT NULL COMMENT '消息id' after id;
alter table `system_config` add mq_message int(11) DEFAULT NULL COMMENT 'mq消息' after system_notify;

-- index
ALTER TABLE `mission_work` ADD INDEX status_index (`status`);
ALTER TABLE `mission_work` ADD INDEX create_time_index (`create_time`);
ALTER TABLE `mission_work` ADD INDEX sequence_index (`sequence`);
ALTER TABLE `mission_action` ADD INDEX mission_id_index (`mission_id`);
ALTER TABLE `mission_action_parameter` ADD INDEX mission_action_id_index (`mission_action_id`);
ALTER TABLE `mission_global_variable` ADD INDEX mission_id_index (`mission_id`);
ALTER TABLE `mission_work_global_variable` ADD INDEX mission_work_id_index (`mission_work_id`);
ALTER TABLE `work_scheduler` ADD INDEX work_id_status_index(`work_id`,`status`);
ALTER TABLE `work_scheduler` ADD INDEX vehicle_id_status_index(`vehicle_id`,`status`);
ALTER TABLE `work_scheduler` ADD INDEX status_index(`status`);
ALTER TABLE `work_scheduler` ADD INDEX work_id_index(`work_id`);
ALTER TABLE `work_scheduler` ADD INDEX vehicle_id_index(`vehicle_id`);
ALTER TABLE `charge_scheduler` ADD INDEX vehicle_id_index(`vehicle_id`);
ALTER TABLE `charge_scheduler` ADD INDEX status_index(`status`);
ALTER TABLE `charge_scheduler` ADD INDEX vehicle_id_status_index(`vehicle_id`,`status`);
ALTER TABLE `charge_scheduler` ADD INDEX charge_type_status_index(`charge_type`,`status`);
ALTER TABLE `park_scheduler` ADD INDEX vehicle_id_index(`vehicle_id`);
ALTER TABLE `park_scheduler` ADD INDEX status_index(`status`);
ALTER TABLE `park_scheduler` ADD INDEX vehicle_id_status_index(`vehicle_id`,`status`);
ALTER TABLE `mq_message` ADD INDEX consume_index(`consume`);
ALTER TABLE `mq_message` ADD COLUMN unique_flag varchar(256) unique COMMENT '唯一标志, 用于mqtt消息去重, UUID' after `consume`;

ALTER TABLE `floor`
add COLUMN `status_address`  tinyint(5)  DEFAULT 0 COMMENT '门状态地址' AFTER `number`,
add COLUMN `operate_address`  tinyint(5)  DEFAULT 0 COMMENT '门操作地址' AFTER `status_address`,
ADD COLUMN `read_function_code`  char(4) NULL COMMENT '读modbus功能码' AFTER `number`,
ADD COLUMN `open_status_value`  int DEFAULT 1  COMMENT '开门状态值' AFTER `status_address`,
ADD COLUMN `close_status_value`  int DEFAULT 0  COMMENT '关门状态值' AFTER `open_status_value`,
ADD COLUMN `write_function_code`  char(4) NULL COMMENT '写modbus功能码' AFTER `close_status_value`,
ADD COLUMN `operate_out_open_value`  int  DEFAULT 1 COMMENT '操作外呼开门值' AFTER `operate_address`,
ADD COLUMN `operate_in_open_value`  int  DEFAULT 2 COMMENT '操作内呼开门值' AFTER `operate_out_open_value`,
ADD COLUMN `operate_close_value`  int DEFAULT 0 COMMENT '操作关门值' AFTER `operate_in_open_value`,
ADD COLUMN `elevator_arrive_address` int NULL COMMENT '电梯到位地址' AFTER `operate_close_value`;

alter table `notification` add help varchar(2048) DEFAULT NULL COMMENT '处理建议' after `scale`;
alter table `notification` add error_code varchar(2048) DEFAULT NULL COMMENT '错误编码' after `help`;



alter table mission_action
add column `pre_Action` varchar (256) COMMENT '上一个动作' after `action_type`,
add column `next_Action` varchar (256) COMMENT '下一个动作' after `pre_Action`,
add column `parent_action_id` varchar (256) COMMENT '父类动作Id' after `action_type`,
add column `child_type` varchar (256) COMMENT '子动作类型 1、IF 2、ELSE 3、WHILE' after `next_action`;

alter table mission_work_action
add column `parent_action_id` varchar (256) COMMENT '父类动作Id' after `error_code`,
add column `child_type` varchar (256) COMMENT '子动作类型 1、IF 2、ELSE 3、WHILE' after `parent_action_id`;

alter table system_config
   add column `ftp_url` varchar (256) COMMENT 'ftp主机地址' after `time_zone`;
alter table `auto_door` ADD COLUMN `open_address` int(11) NOT NULL COMMENT '开门地址' after `port`;
alter table `auto_door` ADD COLUMN `open_status_address` int(11) NOT NULL COMMENT '开门状态地址' after `open_address`;
alter table `auto_door` ADD COLUMN `close_address` int(11) COMMENT '关门地址' after `open_status_address`;
alter table `auto_door` ADD COLUMN `close_status_address` int(11)  COMMENT '关门状态地址' after `close_address`;



alter table `abnormal_prompt` ADD COLUMN `en_desc` varchar(1024) COMMENT '英文描述'  after `update_time`;

alter table `abnormal_prompt` ADD COLUMN `en_slolution` varchar(1024) COMMENT '英文描述'  after `update_time`;


alter table `scheduler_config` add enable_cycle bit(1) DEFAULT b'1' COMMENT '是否启用智能巡线，0:不启用，1:启用' after `across_charge_enable`;

alter table `scheduler_config` add workers_count int(8) DEFAULT 1 COMMENT '标准巡线数量' after `across_charge_enable`;


alter table `agv` add line_patrol_mode bit(1) DEFAULT b'1' COMMENT '是否巡线' ;

alter table `agv` add line_patro_weight int(8) DEFAULT 0 COMMENT '巡线偏好' ;


DROP TABLE IF EXISTS `work_cycle_config`;
CREATE TABLE `work_cycle_config`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '站点id',
  `station_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '站点名称',
  `agv_map_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地图id',
  `mark_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地图上的导航点',
  `seq` double(8, 2) NULL DEFAULT NULL COMMENT '排序序列',
  `reel` bit(1) NULL DEFAULT b'1' COMMENT '是否绕行',
  `enabled` bit(1) NULL DEFAULT NULL COMMENT '是否启用',
  `create_time` timestamp(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` timestamp(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


ALTER TABLE agv_group ADD worker_count TINYINT(3) SIGNED default -1;

ALTER TABLE scheduler_config ADD line_patrol_scheduler_interval  INT(4)  default 60 NOT NULL COMMENT '巡线标准调度间隔' ;



alter table `work_cycle_config` ADD COLUMN `agv_group_id` varchar(128) DEFAULT null COMMENT '机器人组的ID,任务只会分配给组内的机器人';

alter table `work_cycle_config`  ADD COLUMN `enter_work_mode`  int  DEFAULT 0 COMMENT '进站工作模式: 0 自动,1 伪巡线,2 离线' AFTER `agv_group_id`;

alter table `agv_group` ADD COLUMN `light_by_battery` bit(1) DEFAULT b'0' COMMENT '灯随电量亮';



alter table `floor` ADD COLUMN  `call_elevator_address` int  DEFAULT 40011 COMMENT '呼梯地址',
                    ADD COLUMN  `call_elevator_value` int  DEFAULT 40011 COMMENT '呼梯值';
alter table `floor` ADD COLUMN `created_by` varchar(128) DEFAULT 'YOUIFleet' COMMENT '创建者 多机：YOUIFleet 单机：YOUICompass';
alter table `elevator` ADD COLUMN `created_by` varchar(128) DEFAULT 'YOUIFleet' COMMENT '创建者 多机：YOUIFleet 单机：YOUICompass';

alter table `elevator`
    ADD COLUMN  `elevator_mode_address` int  DEFAULT 40093 COMMENT '电梯模式地址',
    ADD COLUMN  `elevator_running_status_address` int  DEFAULT 40002 COMMENT '电梯运行状态地址',
    ADD COLUMN  `elevator_agv_mode` int  DEFAULT 2 COMMENT '电梯AGV模式值',
    ADD COLUMN  `elevator_manual_mode` int  DEFAULT 1 COMMENT '电梯人工模式值';

alter table `elevator` ADD COLUMN `face_recognition_address` varchar(300) DEFAULT null COMMENT '电梯人脸识别地址';

alter table `work_cycle_config` ADD COLUMN `alt_agv_group_id` varchar(128) DEFAULT null COMMENT '机器人组的ID,任务只会分配给组内的机器人';

alter table `work_cycle_config`  ADD COLUMN `release_mode`  int  DEFAULT 0 COMMENT '放行模式: 0 自动(人工+系统(fleet&mes)),1系统放行(系统(fleet&mes)' AFTER `agv_group_id`;

alter table `scheduler_config` add fault_mode bit(1) DEFAULT b'0' COMMENT '是否故障模式，0:不启用，1:启用' after `across_charge_enable`;



alter table `scheduler_config` add number_vehicles  int(8) DEFAULT 4 COMMENT '工作站agv数量限制' after `across_charge_enable`;


alter table `scheduler_config` add voice_playback varchar(128) DEFAULT null after `across_charge_enable`;

ALTER TABLE `mission_work_action` ADD INDEX mission_work_id_index (`mission_work_id`) ;

alter table `work_cycle_config` add number_vehicles  int(8) DEFAULT 4 COMMENT '工作站agv数量限制' ;
