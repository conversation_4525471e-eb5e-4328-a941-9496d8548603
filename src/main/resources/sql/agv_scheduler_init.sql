-- 数据库时区设置 UTC
SET GLOBAL time_zone = '+08:00';
SET time_zone = '+08:00';

flush privileges;
-- 查询数据库时区：SELECT @@global.time_zone, @@session.time_zone;

use `agv_scheduler_v4.8.2`;

-- ----------------------------
-- are_type 初始化数据
-- ----------------------------
INSERT INTO `map_area_type` (id,name,description)
VALUES ('AVOIDANCE_OFF_AREA', '避障关闭区载', NULL);
INSERT INTO `map_area_type` (id,name,description)
VALUES ('SINGLE_AGV_AREA', '单机工作区域', NULL);

-- 创建id自增函数
DELIMITER $$

DROP FUNCTION IF EXISTS `next_value`$$
CREATE FUNCTION `next_value`(name varchar(50)) RETURNS int(11)
begin
declare _cur int;
declare _maxvalue int;  -- 接收最大值
declare _increment int; -- 接收增长步数
set _increment = (select increment_value from sys_sequence where seq_name = name);
set _maxvalue = (select max_value from sys_sequence where seq_name = name);
set _cur = (select current_value from sys_sequence where seq_name = name);
update sys_sequence                      -- 更新当前值
set current_value = _cur + increment_value
where seq_name = name ;
if(_cur + _increment >= _maxvalue) then  -- 判断是都达到最大值
     update sys_sequence
       set current_value = min_value
       where seq_name = name ;
end if;
return _cur;

end$$
DELIMITER ;

-- ----------------------------------
-- sys_sequence（用于id自增） 初始化数据
-- ----------------------------------
INSERT INTO `sys_sequence` (`seq_name`, `min_value`, `max_value`, `current_value`, `increment_value`)
VALUES ('common_key', 1, 99999999, 1, 1);


INSERT INTO `language` (`id`, `current_use`)
VALUES ('common', 'CHINESE');


-- 初始化系统默认配置
insert into system_config values(replace(uuid(),"-",""), 10, 30, 30, 1, 1, 'GMT+8', null,sysdate(), sysdate());

-- 初始化admin用户
INSERT INTO `user`(`id`, `name`, `user_name`, `password`, `admin`) VALUES ('ce41ce331ff3-4027afbce35ab17e508d', 'admin', 'admin', 'admin', 1);

-- 初始化Logo信息
insert into logo_info values(replace(uuid(),"-",""), '深圳优艾智合机器人科技有限公司', 'YOUIFleet v4.8.2-tjd', 'YOUIFleet', 36, 'https://www.youibot.com/' ,'深圳优艾智合机器人科技有限公司', '/static/images/logo.png', sysdate(), sysdate());

-- 初始化调度配置参数信息
INSERT INTO scheduler_config(`id`, `low_batter_value`, `cancel_battery_value`, `high_battery_value`, `free_charge_scope`, `distance_ratio`, `battery_value_ratio`, `park_scheduler_enable`, `charge_scheduler_enable`, `park_scheduler_interval`, `charge_scheduler_interval`, `block_check_enable`, `block_check_interval`, `remove_block_interval`, `minimum_charge_time`, `correct_charge_interval`, `maximum_correct_charge_num`, `time_ratio`, `mission_work_spend_time`, `pre_mission_work_enable`, `across_charge_enable`, `across_park_enable`, `create_time`, `update_time`, `enable_cycle`, `workers_count`) VALUES (1, 5, 40, 80, 500, -1, 1, 0, 0, 10, 10, 0, 30, 1800, 600, 168, 1, -1, 500, 0, 0, 0, sysdate(), sysdate(), b'1', 1);


-- 初始化异常信息

-- ----------------------------
-- Records of abnormal_prompt
-- ----------------------------
INSERT INTO `abnormal_prompt` VALUES ('81cff324-a124-11ec-aa26-8c8caa7e2ae0', 3, '地图', 202013, '使用的地图不存在', '请重新配置任务的动作', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The map used does not exist', 'Please reconfigure the action of the task');
INSERT INTO `abnormal_prompt` VALUES ('81d090f5-a124-11ec-aa26-8c8caa7e2ae0', 3, '地图', 202014, '使用的地图未启用', '请启用该任务使用的地图', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The map used is not enabled', 'Please enable the map used by this quest');
INSERT INTO `abnormal_prompt` VALUES ('81d11de3-a124-11ec-aa26-8c8caa7e2ae0', 3, '地图', 202028, '机器人当前站点不存在', '请检查地图中该站点的配置数据', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The robot\'s current site does not exist', 'Please check the configuration data for this site in the map');
INSERT INTO `abnormal_prompt` VALUES ('81d17b12-a124-11ec-aa26-8c8caa7e2ae0', 3, '地图', 302102, '目标点不可达', '请检查地图该路径导航的起点和终点之间是否可达', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'target point unreachable', 'Please check if the map is reachable between the start and end of the route navigation');
INSERT INTO `abnormal_prompt` VALUES ('81d1d757-a124-11ec-aa26-8c8caa7e2ae0', 3, '机器人', 201000, '机器人底盘异常', '请查看该机器人上报的异常信息', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The robot chassis is abnormal', 'Please check the abnormal information reported by the robot');
INSERT INTO `abnormal_prompt` VALUES ('81d23fb0-a124-11ec-aa26-8c8caa7e2ae0', 3, '机器人', 202021, '机器人脱轨', '请将机器人移动到地图点位后执行重定位', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot derails', 'Please move the robot to the map point and perform relocation');
INSERT INTO `abnormal_prompt` VALUES ('81d2bc5b-a124-11ec-aa26-8c8caa7e2ae0', 3, '机器人', 202035, '机器人前方持续有障碍物', '请移除机器人前方障碍物并复位机器人', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Obstacles persist in front of the robot', 'Please remove obstacles in front of the robot and reset the robot');
INSERT INTO `abnormal_prompt` VALUES ('81d32b54-a124-11ec-aa26-8c8caa7e2ae0', 3, '机器人', 203000, '脚本机械臂异常', '请联系技术支持排查', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script robot arm exception', 'Please contact technical support for troubleshooting');
INSERT INTO `abnormal_prompt` VALUES ('81d3a508-a124-11ec-aa26-8c8caa7e2ae0', 3, '机器人', 204001, '机器人状态不满足充电', '请检查机器人状态', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The robot state is not satisfied with charging', 'Please check the robot status');
INSERT INTO `abnormal_prompt` VALUES ('81d4119a-a124-11ec-aa26-8c8caa7e2ae0', 3, '机器人', 205001, '机器人状态不满足泊车', '请检查机器人状态', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The robot state is not sufficient for parking', 'Please check the robot status');
INSERT INTO `abnormal_prompt` VALUES ('81d49d2c-a124-11ec-aa26-8c8caa7e2ae0', 2, '机器人', 206001, '电池电量低', '请充电', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'battery is low', 'please charge');
INSERT INTO `abnormal_prompt` VALUES ('81d53f4c-a124-11ec-aa26-8c8caa7e2ae0', 2, '机器人', 206002, '按钮急停', '请查看机器人', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'button emergency stop', 'Please check the robot');
INSERT INTO `abnormal_prompt` VALUES ('81d5abd5-a124-11ec-aa26-8c8caa7e2ae0', 2, '机器人', 206003, '安全设备急停', '请查看机器人', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Emergency stop of safety equipment', 'Please check the robot');
INSERT INTO `abnormal_prompt` VALUES ('81d64568-a124-11ec-aa26-8c8caa7e2ae0', 2, '机器人', 206004, '碰撞急停', '请查看机器人', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Collision emergency stop', 'Please check the robot');
INSERT INTO `abnormal_prompt` VALUES ('81d6b3f2-a124-11ec-aa26-8c8caa7e2ae0', 2, '机器人', 206005, '升降电机急停', '请查看机器人', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Elevator motor emergency stop', 'Please check the robot');
INSERT INTO `abnormal_prompt` VALUES ('81d70b64-a124-11ec-aa26-8c8caa7e2ae0', 2, '机器人', 206007, '辊筒急停', '请查看机器人', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Roller emergency stop', 'Please check the robot');
INSERT INTO `abnormal_prompt` VALUES ('81d7a2b8-a124-11ec-aa26-8c8caa7e2ae0', 3, '机器人', 302101, '机器人脱轨', '请将机器人移动到地图点位后重定位', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot derails', 'Please move the robot to the map point and relocate');
INSERT INTO `abnormal_prompt` VALUES ('81d82d74-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202004, '路径导航起点不可在电梯点', '机器人当前在电梯点, 请先将机器人先移出电梯', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The starting point of route navigation cannot be at the elevator point', 'The robot is currently at the elevator point, please move the robot out of the elevator first');
INSERT INTO `abnormal_prompt` VALUES ('81d8c61e-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202005, '目标站点不可用', '请检查目标站点是否已被禁用或删除', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'target site unavailable', 'Please check if the target site has been disabled or deleted');
INSERT INTO `abnormal_prompt` VALUES ('81d961dc-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202006, '路径导航起点不可在设备范围内', '请先将机器人移到空旷位置', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The route navigation start point cannot be within the range of the device', 'Please move the robot to an open space first');
INSERT INTO `abnormal_prompt` VALUES ('81d9db58-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202020, '自动门数据为空', '请检查路径导航中自动门数据是否存在', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Automatic door data is empty', 'Please check whether the automatic door data exists in the route navigation');
INSERT INTO `abnormal_prompt` VALUES ('81da48c5-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202022, '路径导航起点不可在设备范围内', '请先将机器人移到空旷位置', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The route navigation start point cannot be within the range of the device', 'Please move the robot to an open space first');
INSERT INTO `abnormal_prompt` VALUES ('81dab134-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202023, '切换手动模式导致任务异常', '请重新执行该任务', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Switching to manual mode causes task exceptions', 'Please perform this task again');
INSERT INTO `abnormal_prompt` VALUES ('81db48b7-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202024, '读到错误码', '读取寄存器时设备上报错误', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'read error code', 'The device reports an error when reading the register');
INSERT INTO `abnormal_prompt` VALUES ('81dbae91-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202025, '读寄存器超时', '请检查与该寄存器的IP地址和端口网络是否连通', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'read register timeout', 'Please check the IP address and port network connectivity with this register');
INSERT INTO `abnormal_prompt` VALUES ('81dc19b7-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202026, '动作缺少参数', '请检查该任务的动作配置', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Action is missing a parameter', 'Please check the action configuration for this task');
INSERT INTO `abnormal_prompt` VALUES ('81dc8bc2-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202027, '读寄存器失败', '请检查该寄存器是否支持当前功能码', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to read register', 'Please check whether the register supports the current function code');
INSERT INTO `abnormal_prompt` VALUES ('81dcf73d-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202029, 'HTTP请求异常', '请检查网络连接是否连通', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'HTTP request exception', 'Please check if the network connection is connected');
INSERT INTO `abnormal_prompt` VALUES ('81dd519f-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202030, 'URL地址为空', '请检查Http Post动作的配置参数', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'URL address is empty', 'Please check the configuration parameters of the Http Post action');
INSERT INTO `abnormal_prompt` VALUES ('81ddc5ac-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202031, '当前作业不存在', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The current job does not exist', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81de3edf-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202032, '当前任务不存在', '该任务未创建或已被删除，请停止当前作业', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The current task does not exist', 'The task was not created or has been deleted, please stop the current job');
INSERT INTO `abnormal_prompt` VALUES ('81decab1-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202033, '上传文件出错', '请检查文件格式是否符合要求', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Error uploading file', 'Please check if the file format meets the requirements');
INSERT INTO `abnormal_prompt` VALUES ('81df5020-a124-11ec-aa26-8c8caa7e2ae0', 3, '任务', 202034, '楼层数据为空', '请检查路径导航中楼层数据是否存在', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Floor data is empty', 'Please check whether the floor data exists in the route navigation');
INSERT INTO `abnormal_prompt` VALUES ('81dfb816-a124-11ec-aa26-8c8caa7e2ae0', 3, '网络', 202007, '下载电梯数据失败', '请检查与Fleet系统之间的网络是否联通', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to download elevator data', 'Please check whether the network is connected with the Fleet system');
INSERT INTO `abnormal_prompt` VALUES ('81e03847-a124-11ec-aa26-8c8caa7e2ae0', 3, '网络', 202016, 'mobus通讯失败', '请检查modbus通讯是否连通', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'mobus communication failed', 'Please check whether the modbus communication is connected');
INSERT INTO `abnormal_prompt` VALUES ('81e0b1e7-a124-11ec-aa26-8c8caa7e2ae0', 3, '网络', 202017, '申请电梯超时', '请检查电梯通讯是否连通', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Request for elevator timeout', 'Please check if the elevator communication is connected');
INSERT INTO `abnormal_prompt` VALUES ('81e1247e-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 200000, '系统异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e17b1b-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202000, '路径导航异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Path navigation exception', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e1cdb4-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202001, '路径规划异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'abnormal path planning', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e232b2-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202002, '路径规划超时', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Path planning timed out', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e2a225-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202003, '系统异常', 'Compass系统异常，标记点不存在', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'Compass system exception, the marker does not exist');
INSERT INTO `abnormal_prompt` VALUES ('81e31ad2-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202008, '系统异常', '请联系Compass开发人员，使用电梯时导航类型异常', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'Please contact the Compass developer, the navigation type is abnormal when using the elevator');
INSERT INTO `abnormal_prompt` VALUES ('81e3ad8b-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202009, '系统异常', '请联系Compass开发人员，电梯路径上的标记点起点或终点不存在', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'Please contact the Compass developer, the marked point start or end point on the elevator path does not exist');
INSERT INTO `abnormal_prompt` VALUES ('81e421d7-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202010, '系统异常', '请联系Compass开发人员，申请电梯时内部数据错误', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'Please contact Compass developer, internal data error when requesting elevator');
INSERT INTO `abnormal_prompt` VALUES ('81e4a7eb-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202011, '电梯移动超时', '请检查电梯是否出现故障', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Elevator movement timed out', 'Please check if the elevator is malfunctioning');
INSERT INTO `abnormal_prompt` VALUES ('81e53335-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202012, '系统异常', '请联系Compass开发人员，目标地图Id为空', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'Please contact Compass developer, target map Id is empty');
INSERT INTO `abnormal_prompt` VALUES ('81e5be27-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202015, '系统异常', '请联系Compass开发人员，找不到电梯数据', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'Please contact Compass developer, elevator data not found');
INSERT INTO `abnormal_prompt` VALUES ('81e63c29-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 202019, '系统异常', 'Compass系统异常，该动作数据为空', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'The Compass system is abnormal, the action data is empty');
INSERT INTO `abnormal_prompt` VALUES ('81e6a0cd-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 204002, '充电执行异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Abnormal charging execution', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e7100f-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 204003, '充电取消异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Charge Cancellation Abnormal', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e773e8-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 205002, '泊车执行异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Parking execution abnormal', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e7f2e8-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 205003, '泊车取消异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Parking Cancellation Abnormal', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e86517-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 300000, '系统异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'System exception', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e8d07f-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 302000, '作业执行异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Job execution exception', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e93b8c-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 302001, '作业分配异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Job assignment exception', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81e996e6-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 302002, '作业下发异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Job delivery exception', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81ea150d-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 302003, '停止作业失败', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to stop job', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81eaa0bd-a124-11ec-aa26-8c8caa7e2ae0', 3, '系统', 302100, '路径规划异常', '请提交当前地图、Compass、Fleet日志给到售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'abnormal path planning', 'Please submit the current map, Compass, Fleet logs to the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('81eafd59-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570001, '机械臂通用失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'General failure of robotic arm', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81eb550f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570002, '机械臂接口参数错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot arm interface parameter error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81eba61f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570003, '未兼容的指令接口', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Incompatible command interface', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ebfc07-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570004, '机器人连接失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot connection failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ec5438-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570005, '机械臂socket通讯消息收发异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The socket communication message of the robotic arm is abnormally sent and received', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ecb41a-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570006, 'Socket断开连接', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Socket disconnected', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ed33fd-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570007, '创建请求失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Create request failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ed9bde-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570008, '请求相关的内部变量出错', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Request related internal variable error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ee0d07-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570009, '请求超时', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Request timed out', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ee7710-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570010, '发送请求信息失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to send request information', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81eedebe-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570011, '响应信息为空', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The response information is empty', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ef6533-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570012, '响应信息header不符', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The response header does not match', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81efe7d5-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570013, '解析响应失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to parse response', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f04a9d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570014, '正解出错', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Correct solution is wrong', 'Terminate the current task, remove the material on the gripper of the robotic arm, manually return the robotic arm to the origin, and resume the task.');
INSERT INTO `abnormal_prompt` VALUES ('81f0a155-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570015, '逆解出错', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Inverse solution error', 'Terminate the current task, remove the material on the gripper of the robotic arm, manually return the robotic arm to the origin, and resume the task.');
INSERT INTO `abnormal_prompt` VALUES ('81f10f77-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570016, '工具标定出错', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Tool calibration error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f15f77-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570017, '工具标定参数有错', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Tool calibration parameters are wrong', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f1c3b4-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570018, '坐标系标定失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Coordinate system calibration failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f24728-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570019, '基坐标系转用户座标失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to convert base coordinate system to user coordinate', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f2cac6-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570020, '用户坐标系转基座标失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to rotate the base coordinate of the user coordinate system', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f335cb-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570021, '机器人上电失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot failed to power on', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f3a53c-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570022, '机器人断电失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot power failure', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f416d2-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570023, '机器人使能失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot enable failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f48986-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570024, '机器人下使能失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to enable the robot', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f4eca1-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570025, '机器人复位失败', '再次点击复位按钮进行复位，如果还失败，请取出示教器，清除报错', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot reset failed', 'Click the reset button again to reset, if it still fails, please take out the teaching device and clear the error');
INSERT INTO `abnormal_prompt` VALUES ('81f56d98-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570026, '机器人暂停失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot pause failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f5df35-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570027, '机器人停止失败', '拍下急停按钮，取下机械臂抓手上的物料，上电，使能，手动将机械臂回到原点后，恢复任务', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot stop failed', 'Press the emergency stop button, remove the material on the gripper of the robotic arm, power on, enable, and manually return the robotic arm to the origin, then resume the task');
INSERT INTO `abnormal_prompt` VALUES ('81f642ad-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570028, '机器人状态获取失败', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to get robot status', 'Terminate the current task, remove the material on the gripper of the robotic arm, and manually return the robotic arm to the origin to resume the task');
INSERT INTO `abnormal_prompt` VALUES ('81f69bdb-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570041, 'move指令阻塞等待超时', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，检查超时原因。1.如因为路径点轨迹融合半径太大，导致运动不到位，可适当将对应步骤的速度降低。然后单机测试运行该任务，如果不会再报超时，即可恢复任务；2.如因为触发防护性停止导致机械臂暂停时间超过1分钟，可以直接恢复任务。', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The move instruction blocks waiting for a timeout', 'Terminate the current task, remove the material on the gripper of the robotic arm, and manually return the robotic arm to the origin, then check the reason for the timeout. 1. If the motion is not in place because the fusion radius of the path point trajectory is too large, the speed of the corresponding step can be appropriately reduced. Then run the task in a stand-alone test. If no timeout is reported, the task can be resumed; 2. If the robotic arm is suspended for more than 1 minute due to triggering a protective stop, the task can be resumed directly.');
INSERT INTO `abnormal_prompt` VALUES ('81f71cb7-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570042, '运动相关的内部变量出错', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motion related internal variable error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f77e69-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570043, '运动请求失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'motion request failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f7f539-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570044, '生成运动请求失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to generate motion request', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f85bba-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570045, '运动被事件中断', '终止当前任务，取下机械臂抓手上的物料，点击复位按钮进行复位，如果无法复位，请使用示教器清除错误。清错后重新上电、使能，手动将机械臂回到原点后，恢复任务', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Movement interrupted by event', 'Terminate the current task, remove the material on the gripper of the robotic arm, and click the reset button to reset. If it cannot be reset, please use the teach pendant to clear the error. After clearing the error, power on and enable it again, and manually return the robotic arm to the origin to resume the task.');
INSERT INTO `abnormal_prompt` VALUES ('81f8c7a6-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570046, '运动相关的路点容器的长度不符合规定', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Movement-related waypoint container length is not compliant', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f940aa-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570047, '服务器响应返回错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The server responded with an error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81f9c445-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570048, '真实机械臂不存在', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The real robotic arm does not exist', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fa6947-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570049, '调用缓停接口失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to call slow stop interface', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fad482-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570050, '调用急停接口失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to call emergency stop interface', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fb35dd-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570051, '调用暂停接口失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to call the pause interface', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fba8c7-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570052, '调用继续接口失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to call continue interface', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fc4184-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 570053, '运动被条件中断', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，检查失效条件。如果是非传感器自身原图导致的条件失效，请恢复任务。如果是传感器问题，请联系售后人员解决', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Movement interrupted by condition', 'Terminate the current task, remove the material on the gripper of the robotic arm, manually return the robotic arm to the origin, and check the failure condition. If the condition is not caused by the original image of the sensor itself, please resume the task. If it is a sensor problem, please contact the after-sales personnel to solve');
INSERT INTO `abnormal_prompt` VALUES ('81fca161-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571001, '关节运动属性配置错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Incorrect configuration of joint motion properties', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fd024b-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571002, '直线运动属性配置错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Linear motion properties are misconfigured', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fd5908-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571003, '轨迹运动属性配置错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Incorrect configuration of trajectory motion properties', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fda39b-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571004, '无效的运动属性配置', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid motion attribute configuration', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fe0095-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 571005, '等待机器人停止', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Wait for the robot to stop', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fe6566-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 571006, '超出关节运动范围', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'beyond the range of motion of the joint', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81fed620-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571007, '请正确设置MODEP第一个路点', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Please set the MODEP first waypoint correctly', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ff3117-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571008, '传送带跟踪配置错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Conveyor Tracking Configuration Error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('81ff9609-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571009, '传送带轨迹类型错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'conveyor track type error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820013f3-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571010, '相对坐标变换逆解失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Relative coordinate transformation inverse solution failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82006ee0-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 571011, '示教模式发生碰撞', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Collision in teach mode', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8200cf07-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571012, '运动属性配置错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motion property misconfigured', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82014302-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571101, '轨迹异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Trajectory abnormality', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8201a477-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571102, '轨迹规划错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Trajectory planning error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82020158-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571103, '二型在线轨迹规划失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Type II online trajectory planning failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82026d72-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571104, '逆解失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Inverse solution failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8202d988-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 571105, '动力学限制保护', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Kinetic limit protection', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820337ce-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571106, '传送带跟踪失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Conveyor tracking failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8203b9e6-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 571107, '超出传送带工作范围', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'beyond the working range of the conveyor belt', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82042c31-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 571108, '关节超出范围', '终止当前任务，取下机械臂抓手上的物料。检查MOS错误日志，查看是哪个点导致的关节超限，修改该点位后，将机械臂回到原点，重新开始任务', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Joint out of range', 'Terminate the current task and remove the material on the gripper of the robotic arm. Check the MOS error log to see which point caused the joint to exceed the limit. After modifying the point, return the robotic arm to the origin and restart the task');
INSERT INTO `abnormal_prompt` VALUES ('82048938-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 571109, '关节超速', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'joint overspeed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8204f40a-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571110, '离线轨迹规划失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Offline trajectory planning failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820563bd-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571200, '控制器异常，逆解失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The controller is abnormal and the inverse solution fails', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8205ca08-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571201, '控制器异常，状态异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The controller is abnormal, the state is abnormal', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820628d9-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571300, '运动进入到stop阶段', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The movement enters the stop stage', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82069013-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571301, '运动被事件中断', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Movement interrupted by event', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8206fa3c-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 571401, '机械臂未定义的失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot arm undefined failure', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82074d5f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572100, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8207a189-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572101, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820806a5-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572102, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8208764d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572103, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8208eef5-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572104, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820948be-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572105, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82099b8b-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572106, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820a0e47-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572107, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820a7b21-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572108, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820ae07d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572109, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820b6542-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572110, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820bd3ba-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572111, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820c3767-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572112, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820ca5bb-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572113, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820d068f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572114, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820d60d3-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572115, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820dc7ff-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572116, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820e29ed-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572117, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820eb851-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572118, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820f3e0f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572119, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820f9a5f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572120, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('820fec38-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572121, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821048dd-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572122, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8210b396-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572123, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821111d3-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572124, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82116dd1-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572125, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8211e49d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572126, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82125d2f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572127, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8212c5dd-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572128, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8213406a-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572129, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8213bc18-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572130, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821419d2-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572131, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82146e0f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572132, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8214ccb2-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572133, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82151d19-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572134, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82157bf8-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572135, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8215e607-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572136, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82164934-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572137, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8216b461-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572138, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82171a0c-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 572139, 'PLC客户端异常', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'PLC client exception', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821777dc-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 573100, 'E84 消息等待超时', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'E84 message wait timed out', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8217d276-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 573101, 'E84不支持的操作类型', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Operation type not supported by E84', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8218374d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 573102, 'E84不支持的机器人上下料状态', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot loading and unloading status not supported by E84', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8218a564-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574100, '夹爪打开失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Gripper opening failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82190c35-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574101, '夹爪关闭失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Gripper closing failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821959c7-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574102, '夹爪复位失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Gripper reset failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8219a5c3-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574200, '距离传感器检测超时', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Proximity sensor detection timeout', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8219f456-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574201, '抓手有无料检测传感器检测超时', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Whether the gripper has material or not, the sensor detects the timeout', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821a4f78-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574300, '相机未连接', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Camera not connected', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821ac0bf-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574301, '相机内部错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Internal camera error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821b16cd-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574302, '超时', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'time out', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821b6cd7-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574303, '相机未知命令', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Camera unknown command', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821bd3df-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574304, '索引超出范围', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'index out of range', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821c376f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574305, '自变量太少', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'too few independent variables', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821c8a2f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574306, '无效自变量类型', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'invalid argument type', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821ce326-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574307, '无效自变量', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'invalid argument', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821d57b2-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574308, '不允许的命令', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'command not allowed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821db0e0-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574309, '不允许的组合', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Combinations not allowed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821e0fd7-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574310, '相机忙', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'camera busy', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821e81ec-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574311, '未完全实施', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'not fully implemented', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821ece35-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574312, '不支持', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'not support', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821f1e81-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574313, '结果字符串过⻓', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The resulting string is too long', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821f710d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574314, '⽆效相机 ID', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid camera ID', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('821fea05-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574315, '⽆效相机特征 ID', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid Camera Feature ID', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82203cc3-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574316, '不同的配⽅名称', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'different recipe names', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82208c2a-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574317, '不同版本', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'different versions', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8220d878-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574318, '没有标定', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Not calibrated', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82212334-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574319, '标定失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Calibration failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82216ea1-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574320, '⽆效标定数据', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid calibration data', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8221bed1-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574321, '未达到给定的标定位置', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The given calibration position has not been reached', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82221fe3-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574322, '⽆启动命令', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'no startup command', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8222ac33-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574323, '特征未经过训练', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Features are not trained', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822316b8-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574324, '特征未找到', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Feature not found', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82236af0-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574325, '特征未映射', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Features are not mapped', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8223cd53-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574326, '部件位置未经过训练', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Part positions are not trained', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82242231-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574327, '机器⼈位置未经过训练', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Robot positions are not trained', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82247d7b-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574328, '⽆效部件 ID', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid Part ID', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8224ebe5-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574329, '未定位此部件的所有特征', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'All features of this assembly are not positioned', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822549c7-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574330, '部件⽆有效夹持纠正', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Parts are not clamped effectively', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8225c79d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574350, '相机读取socket错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Camera read socket error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82261edd-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574351, '相机响应信息header不符', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Camera response information header does not match', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822678ba-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574352, '解析相机响应失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to parse camera response', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8226d4a9-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 574390, '相机拍照失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The camera failed to take a picture', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82274805-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575100, '无效的储位ID', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid slot ID', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8227c51a-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575101, '储位检测超时', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Storage position detection timeout', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82282dc5-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575300, '无物料信息检测sensor', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'No material information detection sensor', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822896d2-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575301, 'smart tag 传感器未连接', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'smart tag sensor not connected', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8228f6df-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575302, 'smart tag 读取失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'smart tag read failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8229507b-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575303, 'smart tag 读取超时', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'smart tag read timeout', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8229c480-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575304, 'smart tag 数据无效', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'smart tag data is invalid', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822a442d-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 575900, 'smart tag sensor 故障', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'smart tag sensor fault', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822aa93e-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575401, 'RFID 传感器未连接', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'RFID sensor not connected', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822b0066-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575402, 'RFID 读取失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'RFID read failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822b6112-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575403, 'RFID 读取超时', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'RFID read timeout', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822bbd5b-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 575404, 'RFID 数据无效', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid RFID data', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822c124c-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 575901, 'RFID sensor 故障', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'RFID sensor failure', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822cb062-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 576100, '升降柱超出运动范围', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Lifting column out of range of motion', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822d1d5e-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 576101, '无效的控制指令', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'invalid control command', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822d7ece-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579000, '取消任务失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to cancel task', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822ddbe5-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579101, '暂停任务失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Failed to suspend task', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822e3488-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579102, '恢复任务失败', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Recovery task failed', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822e891d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579103, 'buffer解析错误', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'buffer parsing error', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822f022b-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579104, '未找到任务', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Mission not found', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822f6180-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579105, '任务列表未更新', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Task list not updated', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('822fcf8e-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579106, '存在未完成的任务', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'There are unfinished tasks', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82302c31-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579201, '无效步骤类型', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid step type', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8230863d-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579202, '未找到pose value', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'pose value not found', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8230de83-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579203, '未找到joint value', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'joint value not found', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82313abe-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579204, '未找到偏移量', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Offset not found', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8231b06a-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579205, '无效的feature ID', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid feature ID', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('82323c8f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 579206, '无效的条件类型', '请联系mos系统开发人员处理', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Invalid condition type', 'Please contact the mos system developer for processing');
INSERT INTO `abnormal_prompt` VALUES ('8232d242-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110001, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823344d0-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110002, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8233a8f6-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110003, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82343b14-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110004, '左驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82348b09-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110005, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8234e4be-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110006, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82353df0-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110007, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82359ba9-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110008, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8235ebe2-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110009, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82363e67-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110010, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82369e58-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110011, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8236f4dd-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110012, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8237519e-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110013, '左驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8237f1a0-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110014, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823840cc-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110015, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82388b39-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110016, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8238e7b1-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110017, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82394fc6-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110018, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8239aa9e-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110019, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823a050e-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110020, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823a4e5d-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110021, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823a978c-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110022, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823ae066-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110023, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823b28f8-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110024, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823b78f8-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110025, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823bde94-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110026, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823c2639-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110027, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823c76b7-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110028, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823cd1c5-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110029, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823d2bb5-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110030, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823d722c-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110031, '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Left Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823dbce7-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110032, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823e1147-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110033, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823e73af-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110034, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823ecd2c-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110035, '右驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be turned off and left for an hour before use. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823f2910-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110036, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823f7221-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110037, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('823fbd08-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110038, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82400877-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110039, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82405238-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110040, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8240b4e8-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110041, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8241150a-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110042, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82416383-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110043, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8241c088-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110044, '右驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be turned off and left for an hour before use. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82421626-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110045, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82425ef8-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110046, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8242b580-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110047, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824301e5-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110048, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824371c6-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110049, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8243bd55-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110050, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82440b2f-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110051, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82445a0f-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110052, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8244af00-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110053, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8244f662-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110054, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824541a2-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110055, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8245a26a-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110056, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82460abf-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110057, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82465f6a-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110058, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8246bda1-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110059, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82472066-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110060, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82478b5e-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110061, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8247e1ed-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110062, '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Right Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82484091-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110063, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8248a253-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110064, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8248f6a9-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110065, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82494e65-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110066, '顶升驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be turned off and left for an hour before use. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8249b267-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110067, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824a0a54-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110068, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824a5b6b-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110069, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824aa7fc-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110070, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824b0b22-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110071, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824b54a4-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110072, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824ba27c-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110073, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824bf5a3-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110074, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824c52bd-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110075, '顶升驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be turned off and left for an hour before use. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824c9bda-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110076, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824cef6f-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110077, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824d4108-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110078, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824dad35-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110079, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824dfaf1-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110080, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824e5151-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110081, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824e9964-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110082, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824ee245-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110083, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824f2947-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110084, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824f8412-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110085, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('824fee5f-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110086, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82504b82-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110087, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82509360-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110088, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8250e837-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110089, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82513dbd-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110090, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82518a9b-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110091, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8251d400-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110092, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82521a1b-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110093, '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Jacking Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82526b6b-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110094, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8252e074-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110095, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82533d38-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110096, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8253b437-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110097, '旋转驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be turned off and left for an hour before use. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8254050d-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110098, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82545812-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110099, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8254b368-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110100, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825520e9-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110101, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82558648-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110102, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8255e597-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110103, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82563f00-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110104, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82569082-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110105, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8256df89-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110106, '旋转驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be turned off and left for an hour before use. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82572f27-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110107, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8257921b-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110108, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8257fc37-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110109, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82584f4a-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110110, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82589445-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110111, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8258de14-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110112, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8259305c-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110113, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825981e9-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110114, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8259d440-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110115, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825a451b-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110116, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825aa2fb-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110117, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825aef61-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110118, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825b3784-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110119, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825b8809-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110120, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825bd328-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110121, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825c1b4d-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110122, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825c697b-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110123, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825cbc21-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110124, '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Spinning Drive - Abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825d0375-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110200, '电机初始化-异常', '行走电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor initialization - exception', 'The transmission of the travel motor message fails. It may be because the travel motor is not configured with correct parameters or is not connected. If it cannot be restored after restarting, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825d49ab-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110201, '电机初始化-异常', '升降电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor initialization - exception', 'The elevating motor message fails to be sent, possibly because the walking motor is not configured with correct parameters or is not connected. If it cannot be restored after restarting, please contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('825d8e94-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110202, '电机初始化-异常', '插取电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor initialization - exception', 'The message sending of the plug-in motor fails. It may be because the walking motor is not configured with correct parameters or is not connected. If it cannot be restored after restarting, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825dd403-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110203, '电机初始化-异常', '旋转电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor initialization - exception', 'The message sending of the rotating motor fails, possibly because the walking motor is not configured with correct parameters or is not connected. If it cannot be restored after restarting, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825e18e3-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110204, '电机初始化-异常', '夹取电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor initialization - exception', 'The transmission of the gripping motor message fails, possibly because the walking motor is not configured with correct parameters or is not connected. If it cannot be restored after restarting, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825e6708-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110205, '电机初始化-异常', '森创升降电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor initialization - exception', 'Sentron lift motor message failed to send, it may be because the walking motor is not configured with correct parameters or is not connected. If it cannot be restored after restarting, please contact after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('825eb31e-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110206, '电机初始化-异常', '森创旋转电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor initialization - exception', 'Sentron rotating motor message failed to send, maybe because the walking motor is not configured with correct parameters or is not connected, please contact the after-sales service if it cannot be restored after restarting');
INSERT INTO `abnormal_prompt` VALUES ('825f0afd-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110207, '电机初始化-异常', 'SR电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor initialization - exception', 'The SR motor message failed to be sent. It may be because the walking motor is not configured with correct parameters or is not connected. If it cannot be restored after restarting, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('825f548f-a124-11ec-aa26-8c8caa7e2ae0', 1, '软件', 110300, '电机控制-异常', '下发的旋转指令超过范围：-180~180，清除错误后重新下发即可', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor Control - Abnormal', 'The issued rotation command exceeds the range: -180~180, after clearing the error, it can be issued again');
INSERT INTO `abnormal_prompt` VALUES ('825fac70-a124-11ec-aa26-8c8caa7e2ae0', 1, '软件', 110301, '电机控制-异常', '下发的旋转速度指令超过范围，清除错误后重新下发即可，速度最大8转每分钟，', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor Control - Abnormal', 'If the issued rotation speed command exceeds the range, it can be issued again after clearing the error. The maximum speed is 8 revolutions per minute.');
INSERT INTO `abnormal_prompt` VALUES ('825ff7d8-a124-11ec-aa26-8c8caa7e2ae0', 1, '软件', 110302, '电机控制-异常', '下发的升降指令超过范围，清除错误后重新下发即可，若在合理范围内一直不能成功，请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor Control - Abnormal', 'The issued lift command exceeds the range. After clearing the error, you can send it again. If it has not been successful within a reasonable range, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8260475a-a124-11ec-aa26-8c8caa7e2ae0', 1, '软件', 110303, '电机控制-异常', '下发的升降速度指令超过范围，清除错误后重新下发即可，速度10mm/s', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor Control - Abnormal', 'The issued lift speed command exceeds the range, clear the error and then send it again, the speed is 10mm/s');
INSERT INTO `abnormal_prompt` VALUES ('82608d70-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110400, '电机运行-异常', '触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor running - abnormal', 'Trigger the emergency stop and then release it, or restart the robot to see if it recovers, otherwise contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('8260d9e7-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110401, '电机运行-异常', '触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor running - abnormal', 'Trigger the emergency stop and then release it, or restart the robot to see if it recovers, otherwise contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('826125fa-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110402, '电机运行-异常', '触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor running - abnormal', 'Trigger the emergency stop and then release it, or restart the robot to see if it recovers, otherwise contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('82616dae-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110403, '电机运行-异常', '触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Motor running - abnormal', 'Trigger the emergency stop and then release it, or restart the robot to see if it recovers, otherwise contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('8261b855-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110500, '声光系统-异常', '重启后若依旧无法解决，则联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Sound and Light System - Anomaly', 'If the problem still cannot be solved after restarting, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82620b74-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110501, '声光系统-异常', '重启后若依旧无法解决，则联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Sound and Light System - Anomaly', 'If the problem still cannot be solved after restarting, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82625550-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110502, '声光系统-异常', '确认音频名字无误后重新下发指令', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Sound and Light System - Anomaly', 'Re-issue the command after confirming that the audio name is correct');
INSERT INTO `abnormal_prompt` VALUES ('82629d96-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 110503, '声光系统-异常', '确认音频名字无误后重新下发指令', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Sound and Light System - Anomaly', 'Re-issue the command after confirming that the audio name is correct');
INSERT INTO `abnormal_prompt` VALUES ('8262eea4-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110504, '声光系统-异常', '重启后若依旧无法解决，则联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Sound and Light System - Anomaly', 'If the problem still cannot be solved after restarting, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82633d0b-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 110505, '声光系统-异常', '检查socketIP和端口是否配置正确，如果正确则联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Sound and Light System - Anomaly', 'Check whether the socketIP and port are configured correctly, if so, contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('8263899f-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 120100, 'bms-异常', '检查串口是否正常', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'bms - exception', 'Check whether the serial port is normal');
INSERT INTO `abnormal_prompt` VALUES ('8263d31f-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 120101, 'bms-异常', '检查端口号是否正确，检查电池和品牌是否有问题', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'bms - exception', 'Check if the port number is correct, check if there is a problem with the battery and brand');
INSERT INTO `abnormal_prompt` VALUES ('826419a9-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 120001, '充电-异常', '可能电池的通信线连接不良，或者间歇性通信失败，重新执行任务，若依旧出错请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'charge-abnormal', 'Maybe the communication cable of the battery is not connected properly, or the communication fails intermittently. Re-execute the task. If the error still occurs, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8264603c-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 120002, '充电-异常', '重启重新尝试后若依旧报错请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'charge-abnormal', 'If the error is still reported after restarting and trying again, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8264a8c7-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 120003, '充电-异常', '重启重新尝试后若依旧报错请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'charge-abnormal', 'If the error is still reported after restarting and trying again, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8264f107-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 120004, '充电-异常', '重启重新尝试后若依旧报错请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'charge-abnormal', 'If the error is still reported after restarting and trying again, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82653a6d-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 120005, '充电-异常', '可能环境因素导致对接信息采集失败，调整环境至良好无干扰的情况再重新尝试任务，若调整后依旧失败请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'charge-abnormal', 'The connection information collection may fail due to environmental factors. Adjust the environment to a good and no interference situation and try the task again. If the adjustment still fails, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8265839b-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 120006, '充电-异常', '可能环境因素导致对接信息采集失败，调整环境至良好无干扰的情况再重新尝试任务，若调整后依旧失败请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'charge-abnormal', 'The connection information collection may fail due to environmental factors. Adjust the environment to a good and no interference situation and try the task again. If the adjustment still fails, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8265ca24-a124-11ec-aa26-8c8caa7e2ae0', 2, '硬件', 120007, '充电-异常', '若没良好对接充电桩，请调整对接参数，确保充电桩处于自动模式，', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'charge-abnormal', 'If the charging pile is not properly connected, please adjust the docking parameters to ensure that the charging pile is in automatic mode.');
INSERT INTO `abnormal_prompt` VALUES ('826611ec-a124-11ec-aa26-8c8caa7e2ae0', 2, '硬件', 120008, '充电-异常', '降低充满百分比，默认为97%，若降低到89%依旧还有问题，请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'charge-abnormal', 'Reduce the full percentage, the default is 97%, if there is still a problem if it is reduced to 89%, please contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('82665895-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 121001, '音频-异常', '确认所指定播放的音频名字存在与AGV内，注意音频名字末尾不要加后缀，如.mp3，确保音频为mp3格式，', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'audio - exception', 'Confirm that the specified audio name exists in the AGV, pay attention not to add a suffix at the end of the audio name, such as .mp3, make sure the audio is in mp3 format,');
INSERT INTO `abnormal_prompt` VALUES ('8266a0e9-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113001, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8266e61a-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113002, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82672dc0-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113003, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8267729e-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113004, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8267c3b4-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113005, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826822a9-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113006, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82688574-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113007, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8268d260-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113008, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82691a56-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113009, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82696736-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113010, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8269c540-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113011, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826a1eb6-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113012, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826a786e-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113013, 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826ac864-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113015, 'Can卡-异常', '可能CAN卡未连接或者连接线断开，若重启无法解决，请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'Maybe the CAN card is not connected or the connection line is disconnected. If restarting cannot solve the problem, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826b18ff-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113016, 'Can卡-异常', '可能CAN卡设备异常，若多次重启未恢复，请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'The CAN card device may be abnormal. If it is not recovered after restarting several times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826b6986-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 113017, 'Can卡-异常', '可能CAN卡设备异常，若多次重启未恢复，请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Can card - exception', 'The CAN card device may be abnormal. If it is not recovered after restarting several times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826bbb2b-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 123004, 'Socket-异常', '重新确认API端口号与接口号是否正确', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Socket-Exception', 'Re-confirm whether the API port number and interface number are correct');
INSERT INTO `abnormal_prompt` VALUES ('826c0974-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 123005, 'Socket-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Socket-Exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826c636f-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 123006, 'Socket-异常', '无法获取配置信息，请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Socket-Exception', 'Unable to obtain configuration information, please contact after-sales');
INSERT INTO `abnormal_prompt` VALUES ('826cc9b6-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 115001, 'IMU-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'IMU-abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826d589f-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 115002, 'IMU-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'IMU-abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826daba1-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 115003, 'IMU-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'IMU-abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826e06e0-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 115004, 'IMU-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'IMU-abnormal', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826e6542-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127001, '导航执行异常', '需要停止当前任务，并联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'Need to stop the current task and contact after sales');
INSERT INTO `abnormal_prompt` VALUES ('826ebb4f-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127002, '导航执行异常', '需要停止当前任务，并且人工判断机器人是否脱轨，如果脱轨，请将机器人移动到路径上，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'It is necessary to stop the current task and manually judge whether the robot is derailed. If it is derailed, please move the robot to the path. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826f153e-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127003, '导航执行异常', '需要停止当前任务，并且人工判断定位数据是否存在，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'It is necessary to stop the current task and manually determine whether the positioning data exists. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826f62f0-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127004, '导航执行异常', '需要停止当前任务，并且人工判断货架下方标记物是否识别正常，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'It is necessary to stop the current task, and manually judge whether the marker under the shelf is recognized normally. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('826fbba1-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127005, '导航执行异常', '需要停止当前任务，并且判断是否存在雷达以及pcl相关报错，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'It is necessary to stop the current task, and judge whether there is a radar and PCl related error. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('827012e0-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127006, '导航执行异常', '需要停止当前任务，并且判断是否存在电机相关报错，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'It is necessary to stop the current task and judge whether there is a motor-related error. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82706f32-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127007, '导航执行异常', '需要停止当前任务，并且判断是否存在定位相关报错，如果没有，激光定位情况下请判断是否存在雷达相关报错，二维码定位判断是否存在二维码传感器通讯异常，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'It is necessary to stop the current task and judge whether there is a positioning-related error. If not, in the case of laser positioning, please judge whether there is a radar-related error. QR code positioning judge whether there is an abnormal communication between the QR code sensor, if it cannot be recovered or the abnormality occurs many times. Please contact after sales');
INSERT INTO `abnormal_prompt` VALUES ('8270bf05-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127009, '导航执行异常', '需要停止当前任务，当前路径周遭人工特征是否存在被遮挡的情况，如果存在，请避免遮挡，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'It is necessary to stop the current task. Check whether the artificial features around the current path are blocked. If so, please avoid blocking. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('827126f1-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 127010, '导航执行异常', '需要停止当前任务，并且判断当前激光定位数据是否正常，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'It is necessary to stop the current task and judge whether the current laser positioning data is normal. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82717e0a-a124-11ec-aa26-8c8caa7e2ae0', 1, '软件', 127011, '导航执行异常', '请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Navigation execution exception', 'Please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('8271db6a-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128001, '对接执行异常', '需要清除错误状态，并且判断当前机器人是否在执行对接，或者是否在之前执行对接指令后未执行脱离对接，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'It is necessary to clear the error state, and determine whether the current robot is performing docking, or whether it has not performed detachment docking after executing the docking command before. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82724b5a-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128002, '对接执行异常', '需要清除错误状态，并且判断当前指定的对接目标是否合理，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'It is necessary to clear the error state and judge whether the currently specified docking target is reasonable. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8272a704-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128003, '对接执行异常', '需要清除错误状态，并且判断特征检测模块运行状态，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'It is necessary to clear the error status and judge the running status of the feature detection module. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('827311dd-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128004, '对接执行异常', '需要清除错误状态，并且判断特征检测模块运行状态，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'It is necessary to clear the error status and judge the running status of the feature detection module. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82738b59-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128005, '对接执行异常', '需要清除错误状态，并且判断特征检测模块运行状态，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'It is necessary to clear the error status and judge the running status of the feature detection module. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8273f440-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128006, '对接执行异常', '需要清除错误状态，并请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'Need to clear the error state, and please contact after-sales');
INSERT INTO `abnormal_prompt` VALUES ('827449fd-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128007, '对接执行异常', '需要清除错误状态，并且判断特征检测模块运行状态，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'It is necessary to clear the error status and judge the running status of the feature detection module. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8274a76f-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128008, '对接执行异常', '需要清除错误状态，需要清除错误状态，并且判断当前机器人是否在执行脱离对接，或者是否在之前未执行对接动作，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'The error state needs to be cleared, the error state needs to be cleared, and it is necessary to determine whether the current robot is performing detachment, or whether it has not performed the docking action before. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82750ca5-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 128009, '对接执行异常', '需要清除错误状态，并且判断是否存在定位相关报错，如果没有，请判断是否存在雷达相关报错，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Connection execution exception', 'It is necessary to clear the error status and judge whether there is a positioning-related error. If not, please judge whether there is a radar-related error. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82755d67-a124-11ec-aa26-8c8caa7e2ae0', 1, '软件', 130001, '定位异常', '请尝试重定位，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Positioning exception', 'Please try to relocate, if it cannot be recovered or the abnormality occurs many times, please contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('8275b804-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 133001, '特征检测执行异常', '人工判断机器人是否在对接范围内，特征是否与机器人雷达高度一致，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Feature Detection Execution Abnormal', 'Manually judge whether the robot is within the docking range and whether the features are highly consistent with the robot radar. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82760ec5-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 133002, '特征检测执行异常', '人工判断是否有相似特征，调整机器人对接距离或方向，如果无法回复或异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Feature Detection Execution Abnormal', 'Manually judge whether there are similar features, and adjust the docking distance or direction of the robot. If you cannot reply or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82768377-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 133003, '特征检测执行异常', '判断是否存在激光雷达相关报错，如果无法恢复或异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Feature Detection Execution Abnormal', 'Determine whether there is an error related to lidar. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('8276e540-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 133004, '特征检测执行异常', '判断是否存在定位相关报错，如果无法恢复或异常出现多次请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Feature Detection Execution Abnormal', 'Determine whether there is a positioning-related error, if it cannot be recovered or the abnormality occurs many times, please contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('82773c34-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 135001, '建图执行异常', '判断是否存在激光雷达相关报错，如果无法恢复或异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Mapping execution exception', 'Determine whether there is an error related to lidar. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales service');
INSERT INTO `abnormal_prompt` VALUES ('827795cc-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 135002, '建图执行异常', '人工判断绘图过程是否形成回环，如果一直没有回环请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Mapping execution exception', 'Manually judge whether a loop is formed during the drawing process. If there is no loop, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8277ef1f-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 119001, '节点异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Node exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82786118-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 119002, '节点异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Node exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('8278cf35-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 119003, '节点异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Node exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82793c48-a124-11ec-aa26-8c8caa7e2ae0', 3, '软件', 119004, '节点异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Node exception', 'It needs to be restarted and restored. If it cannot be restored or the abnormality occurs many times, please contact the after-sales service.');
INSERT INTO `abnormal_prompt` VALUES ('82799f05-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140000, '脚本异常', '请检查socket或者lua指令的参数，例如：长度、类型等', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please check the parameters of socket or lua command, such as: length, type, etc.');
INSERT INTO `abnormal_prompt` VALUES ('827a0e2e-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140001, '脚本异常', '指定控制的脚本不存在，请检查脚本的名字或者ID', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The specified control script does not exist, please check the name or ID of the script');
INSERT INTO `abnormal_prompt` VALUES ('827a7c45-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140002, '脚本异常', '脚本正在运行中，不运行用户当前的控制指令', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The script is running, not running the user\'s current control command');
INSERT INTO `abnormal_prompt` VALUES ('827ad841-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140003, '脚本异常', '脚本子线程没有正常退出，这种情况属于致命BUG，请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The script sub-thread did not exit normally, this is a fatal bug, please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('827b3512-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140004, '脚本异常', '启动脚本超时，检测脚本存放路径是否正确，如检测无误，请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The startup script times out. Check whether the script storage path is correct. If the detection is correct, please contact the developer.');
INSERT INTO `abnormal_prompt` VALUES ('827b8ed4-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140005, '脚本异常', '停止脚本超时，检测脚本是否已经退出，如检测无误，请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Stop the script timeout, check whether the script has exited, if the detection is correct, please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('827bf1fd-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140006, '脚本异常', '下发的控制指令不存在，请检查json指令的command', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The issued control command does not exist, please check the command of the json command');
INSERT INTO `abnormal_prompt` VALUES ('827c686e-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140007, '脚本异常', '指定的脚本变量地址错误，请检查下发的脚本变量的地址是否不在指定范围内【0，31】', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The specified script variable address is wrong, please check whether the address of the script variable delivered is not within the specified range [0, 31]');
INSERT INTO `abnormal_prompt` VALUES ('827cd1a9-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140008, '脚本异常', '请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('827d3242-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140009, '脚本异常', '请检查lua脚本保存的后缀名是否正确，并联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please check whether the suffix saved in the lua script is correct and contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('827dad1b-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140010, '脚本异常', '配置的用户lua脚本保存路径错误，请检查配置文件', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The configured user lua script save path is wrong, please check the configuration file');
INSERT INTO `abnormal_prompt` VALUES ('827e11d9-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140011, '脚本异常', '请检查json格式是否正确，或者socket协议的包头包尾是否已经添加', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please check whether the json format is correct, or whether the header and footer of the socket protocol have been added');
INSERT INTO `abnormal_prompt` VALUES ('827e8130-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140012, '脚本异常', 'JSON字段中缺少“command”字段，请联系前端开发人员进行核对', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The \"command\" field is missing in the JSON field, please contact the front-end developer to check');
INSERT INTO `abnormal_prompt` VALUES ('827eedfc-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140013, '脚本异常', '变量类型错误，请选择string/double/int', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The variable type is wrong, please select string/double/int');
INSERT INTO `abnormal_prompt` VALUES ('827f494d-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140014, '脚本异常', '脚本向compass请求路径导航中，compass返回的状态错误', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'When the script requests path navigation to compass, the status returned by compass is incorrect');
INSERT INTO `abnormal_prompt` VALUES ('827f9fdf-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140015, '脚本异常', '脚本向compass请求路径导航中，compass反馈异常', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'When the script requests path navigation to compass, the compass feedback is abnormal');
INSERT INTO `abnormal_prompt` VALUES ('828002a7-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140016, '脚本异常', '脚本向compass请求路径导航中，compass的json错误', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'When the script requests path navigation to compass, the json of compass is wrong');
INSERT INTO `abnormal_prompt` VALUES ('8280661d-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140017, '脚本异常', '脚本向compass请求路径导航过程中，compass的socket服务断开', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'During the process of the script requesting path navigation to compass, the socket service of compass is disconnected');
INSERT INTO `abnormal_prompt` VALUES ('8280d755-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140018, '脚本异常', 'moveLine接口发生的错误，请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'An error occurred in the moveLine interface, please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('82813fb9-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140019, '脚本异常', '对接出错，请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Docking error, please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('8281a1e1-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140020, '脚本异常', '等待小车运动超时，请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Wait for the trolley movement to time out, please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('82820563-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 140021, '脚本异常', '运行中的脚本数量到达阈值，请停止继续启动脚本或者停止当前运行中的脚本', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The number of running scripts has reached the threshold, please stop continuing to start the script or stop the currently running script');
INSERT INTO `abnormal_prompt` VALUES ('828258a1-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 145000, '脚本异常', '连接机械臂超时，请检测机械臂是否已经上电、网线是否正常', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The connection to the robotic arm has timed out. Please check whether the robotic arm is powered on and whether the network cable is normal.');
INSERT INTO `abnormal_prompt` VALUES ('8282b715-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 145001, '脚本异常', '机械臂没有建立连接，请检测机械臂是否已经上电、网线是否正常', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The robotic arm is not connected, please check whether the robotic arm is powered on and the network cable is normal');
INSERT INTO `abnormal_prompt` VALUES ('82830709-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 145002, '脚本异常', '请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('8283599d-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 145003, '脚本异常', '控制机械臂输入的参数错误，请根据协议检查参数', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The parameter input to control the robot arm is wrong, please check the parameter according to the agreement');
INSERT INTO `abnormal_prompt` VALUES ('8283a9c6-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 145004, '脚本异常', '机械臂返回的消息错误，请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The message returned by the robot arm is wrong, please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('8283fb0b-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 145005, '脚本异常', '下发控制机械臂的指令错误，请根据协议检查下发的指令', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'The command issued to control the robotic arm is incorrect, please check the issued command according to the protocol');
INSERT INTO `abnormal_prompt` VALUES ('82844ab0-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 145006, '脚本异常', '请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('82849b91-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 145007, '脚本异常', '请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('828503c4-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 146000, '脚本异常', '请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('82856ced-a124-11ec-aa26-8c8caa7e2ae0', 2, '软件', 146001, '脚本异常', '请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Script exception', 'Please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('8285d4d0-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 170001, '上集成异常', '请联系开发人员', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Integration exception', 'Please contact the developer');
INSERT INTO `abnormal_prompt` VALUES ('828623b9-a124-11ec-aa26-8c8caa7e2ae0', 2, '硬件', 170002, '上集成异常', '请按照上集成操作手册复位', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Integration exception', 'Please follow the reset on the integrated operation manual');
INSERT INTO `abnormal_prompt` VALUES ('82867a85-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 170003, '上集成异常', '请复位驱动器清除故障，或者断电重启', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Integration exception', 'Please reset the drive to clear the fault, or power off and restart');
INSERT INTO `abnormal_prompt` VALUES ('8286d0f0-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 170004, '上集成异常', '请复位驱动器清除故障，或者断电重启', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Integration exception', 'Please reset the drive to clear the fault, or power off and restart');
INSERT INTO `abnormal_prompt` VALUES ('828725f7-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 170005, '上集成异常', '请复位驱动器清除故障，或者断电重启', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Integration exception', 'Please reset the drive to clear the fault, or power off and restart');
INSERT INTO `abnormal_prompt` VALUES ('82877e54-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 170006, '上集成异常', '请复位驱动器清除故障，或者断电重启', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Integration exception', 'Please reset the drive to clear the fault, or power off and restart');
INSERT INTO `abnormal_prompt` VALUES ('8288944f-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 170007, '上集成异常', '请复位驱动器清除故障，或者断电重启', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Integration exception', 'Please reset the drive to clear the fault, or power off and restart');
INSERT INTO `abnormal_prompt` VALUES ('8288e79e-a124-11ec-aa26-8c8caa7e2ae0', 3, '硬件', 170008, '上集成异常', '松开急停可恢复', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'Integration exception', 'Release the emergency stop to resume');
INSERT INTO `abnormal_prompt` VALUES('81d11de3-a124-11ec-aa26-8c8caa7e2ae1', 3, '机器人', 202121, '机器人无故停止运行超过15秒', '请检查检查机器人是否出故障', '2022-06-19 06:40:25', '2022-06-20 18:50:58', 'The robot can not run in 15 seconds', 'Please check the robot');


alter table `abnormal_prompt` add push_up bit(1) not null  DEFAULT b'1' COMMENT '是否推送到上游系统，0:不推送，1:推送' after `en_desc`;

INSERT INTO `abnormal_prompt` (`id`, `abnormal_level`, `abnormal_type`, `abnormal_code`, `abnormal_description`, `help`, `create_time`, `update_time`, `en_slolution`, `en_desc`, `push_up`) VALUES ('01c2eeca-c107-11ef-8b6d-0242ac110002', 3, '软件', 2080016, '机器人安全开关未开启', '请确保机器人安全开关处于开启状态', '2024-12-12 06:58:08', '2024-12-25 15:19:41', 'The safe switches are not  all opened', 'Please check safe switches ', b'1');
