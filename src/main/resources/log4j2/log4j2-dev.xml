<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="OFF">
    <Properties>
        <Property name="App">fleet</Property>
        <Property name="logDir">logs</Property>
        <Property name="splitSize">40 MB</Property>
    </Properties>
    <Appenders>

        <!-- 打印出所有debug级别以及以上的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
        <RollingRandomAccessFile name="debugLog" fileName="${logDir}/${App}-debug.log" immediateFlush="true"
                                 filePattern="${logDir}/$${date:yyyy-MM}/${App}-debug-%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} [%t] %-5level %logger{36} %L %M - %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${splitSize}"/>
            </Policies>
            <Filters>
                <!-- 只记录info和warn级别信息 -->
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <!-- 指定每天的最大压缩包个数，默认7个，超过了会覆盖之前的 -->
            <DefaultRolloverStrategy max="100000"/>
        </RollingRandomAccessFile>

        <!-- 打印出所有info级别以及以上的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
<!--        <RollingRandomAccessFile name="infoLog" fileName="${logDir}/${App}-info.log" immediateFlush="true"-->
<!--                                 filePattern="${logDir}/$${date:yyyy-MM}/${App}-info-%d{MM-dd-yyyy}-%i.log.gz"-->
<!--                                 append="true">-->
<!--            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} [%t] %-5level %logger{36} %L %M - %msg%xEx%n"/>-->
<!--            <Policies>-->
<!--                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>-->
<!--                <SizeBasedTriggeringPolicy size="${splitSize}"/>-->
<!--            </Policies>-->
<!--            <Filters>-->
<!--                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>-->
<!--            </Filters>-->
<!--            <DefaultRolloverStrategy max="1000"/>-->
<!--        </RollingRandomAccessFile>-->

        <!-- 打印出所有error级别以及以上的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
        <RollingRandomAccessFile name="errorLog" fileName="${logDir}/${App}-error.log" immediateFlush="true"
                                 filePattern="${logDir}/$${date:yyyy-MM}/${App}-error-%d{yyyy-MM-dd}-%i.log.gz"
                                 append="false">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} [%t] %-5level %logger{36} %L %M - %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${splitSize}"/>
            </Policies>
            <Filters>
                <!-- 只记录error级别信息 -->
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <!-- 指定每天的最大压缩包个数，默认7个，超过了会覆盖之前的 -->
            <DefaultRolloverStrategy max="1000"/>
        </RollingRandomAccessFile>

        <!-- 打印controller接口日志 -->
        <RollingRandomAccessFile name="apiLog" fileName="${logDir}/${App}-api.log" immediateFlush="true"
                                 filePattern="${logDir}/$${date:yyyy-MM}/${App}-api-%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout pattern="%d{yyyy-MM-dd 'at' HH:mm:ss z} [%t] %-5level %logger{36} %L %M - %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${splitSize}"/>
            </Policies>
            <Filters>
                <!-- 只记录info和warn级别信息 -->
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <!-- 指定每天的最大压缩包个数，默认7个，超过了会覆盖之前的 -->
            <DefaultRolloverStrategy max="100000"/>
        </RollingRandomAccessFile>

        <Routing name="Routing">
        	
        		<InjectAgvCodePolicy />
        		
            <Routes pattern="$${ctx:ROUTINGKEY}">
                <!-- This route is chosen if ThreadContext has value 'special' for key ROUTINGKEY. -->
                <Route key="special">
                    <RollingFile name="Rolling-${ctx:ROUTINGKEY}" fileName="${logDir}/special-${ctx:ROUTINGKEY}.log"
                                 filePattern="${logDir}/${date:yyyy-MM}/special-${ctx:ROUTINGKEY}-%d{yyyy-MM-dd}-%i.log.gz">
                        <PatternLayout>
                            <Pattern>%d{ISO8601} [%t] %p %c{3} %L - %m%n</Pattern>
                        </PatternLayout>
                        <Policies>
                            <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                            <SizeBasedTriggeringPolicy size="${splitSize}"/>
                        </Policies>
                    </RollingFile>
                </Route>
                <!-- This route is chosen if ThreadContext has no value for key ROUTINGKEY. -->
                <Route key="$${ctx:ROUTINGKEY}">
                    <RollingFile name="Rolling-default" fileName="${logDir}/${App}-default.log"
                                 filePattern="${logDir}/${date:yyyy-MM}/${App}-default-%d{yyyy-MM-dd}-%i.log.gz">
                        <PatternLayout>
                            <pattern>%d{ISO8601} [%t] %p %c{3} %L- %m%n</pattern>
                        </PatternLayout>
                        <Policies>
                            <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                            <SizeBasedTriggeringPolicy size="${splitSize}"/>
                        </Policies>
                    </RollingFile>
                </Route>
                <Route>
                    <RollingFile name="Rolling-${ctx:ROUTINGKEY}" fileName="${logDir}/agv-code-${ctx:ROUTINGKEY}.log"
                                 filePattern="${logDir}/${date:yyyy-MM}/agv-code-${ctx:ROUTINGKEY}-%d{yyyy-MM-dd}-%i.log.gz">
                        <PatternLayout>
                            <pattern>%d{ISO8601} [%t] %p %c{3} %L- %m%n</pattern>
                        </PatternLayout>
                        <Policies>
                            <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                            <SizeBasedTriggeringPolicy size="${splitSize}"/>
                        </Policies>
                    </RollingFile>
                </Route>
            </Routes>
        </Routing>
        <!--Console指定了结果输出到控制台-->
        <Console name="ConsolePrint" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy.MM.dd HH:mm:ss z} %t %-5level %class{36} %L %M - %msg%xEx%n"/>
        </Console>
    </Appenders>

    <Loggers>

        <logger name="com.youibot.agv.scheduler.config.LogOperationAspect" level="debug" additivity="false">
            <AppenderRef ref="apiLog"/>
        </logger>


		<!-- secs msg -->
   <logger name="com.shimizukenta.secs" level="debug" additivity="false">
            <AppenderRef ref="infoLog"/>
            <AppenderRef ref="debugLog"/>
            <AppenderRef ref="errorLog"/>
            <AppenderRef ref="console"/>
        </logger>
        
        <!--第三方的软件日志级别 -->
        <logger name="com.youibot.agv.scheduler" level="debug" additivity="false">
            <AppenderRef ref="infoLog"/>
            <AppenderRef ref="debugLog"/>
            <AppenderRef ref="errorLog"/>
            <AppenderRef ref="console"/>
        </logger>

        <logger name="com.youibot.agv.scheduler.mapper" level="debug" additivity="false">

            <AppenderRef ref="sqlLog"/>
        </logger>
        <logger name="com.youibot.agv.scheduler" level="debug" additivity="false">
            <AppenderRef ref="Routing"/>
            <AppenderRef ref="ConsolePrint"/>
            <AppenderRef ref="debugLog"/>
<!--            <AppenderRef ref="infoLog"/>-->
            <AppenderRef ref="errorLog"/>
        </logger>
        <Root includeLocation="true" additivity="false">
            <AppenderRef ref="Routing"/>
            <AppenderRef ref="ConsolePrint"/>
            <AppenderRef ref="apiLog"/>
            <AppenderRef ref="debugLog"/>
<!--            <AppenderRef ref="infoLog"/>-->
            <AppenderRef ref="errorLog"/>
        </Root>
    </Loggers>
</Configuration>