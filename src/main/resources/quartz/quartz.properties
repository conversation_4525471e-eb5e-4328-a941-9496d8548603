#IDÉèÖÃÎª×Ô¶¯»ñÈ¡ Ã¿Ò»¸ö±ØÐë²»Í¬ £¨ËùÓÐµ÷¶ÈÆ÷ÊµÀýÖÐÊÇÎ¨Ò»µÄ£©
org.quartz.scheduler.instanceId=AUTO
org.quartz.scheduler.instanceName=clusteredScheduler
#Ö¸¶¨µ÷¶È³ÌÐòµÄÖ÷Ïß³ÌÊÇ·ñÓ¦¸ÃÊÇÊØ»¤Ïß³Ì
#org.quartz.scheduler.makeSchedulerThreadDaemon=true
#ThreadPoolÊµÏÖµÄÀàÃû
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true
#Ïß³ÌÊýÁ¿
org.quartz.threadPool.threadCount=20
#Ïß³ÌÓÅÏÈ¼¶
org.quartz.threadPool.threadPriority=5
#Êý¾Ý±£´æ·½Ê½Îª³Ö¾Ã»¯
org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
#StdJDBCDelegateËµÃ÷Ö§³Ö¼¯Èº
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
#quartzÄÚ²¿±íµÄÇ°×º
org.quartz.jobStore.tablePrefix=QRTZ_
#ÊÇ·ñ¼ÓÈë¼¯Èº
org.quartz.jobStore.isClustered=false
#ÈÝÐíµÄ×î´ó×÷ÒµÑÓ³¤Ê±¼ä
org.quartz.jobStore.misfireThreshold=60000