spring:
  profiles:
    include: test-agv
  datasource:
    default:
      platform: mysql
      jdbc-url: **********************************************************************************************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: root
      type: com.zaxxer.hikari.HikariDataSource #使用Hikari连接池
      minimum-idle: 5 #空闲连接数据
      maximum-pool-size: 140 #最大连接池数量，根据并发适当增加。不能超过MYSQL的连接数，mysql默认最大151.
      auto-commit: true
      idle-timeout: 30000 #空闲30S自动释放
      connection-timeout: 30000 #连接超时30秒
      connection-test-query: SELECT 1 #连接状态测试语句
      max-lifetime: 1800000 #连接最大存活时间，如果连接一直在用，超过这个时间会重新创建连接。

    quartz:
      platform: mysql
      jdbc-url: *****************************************************************************************************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: root
      type: com.zaxxer.hikari.HikariDataSource #使用Hikari连接池
      minimum-idle: 5 #空闲连接数据
      maximum-pool-size: 30 #最大连接池数量，根据并发适当增加。不能超过MYSQL的连接数，mysql默认最大151.
      auto-commit: true
      idle-timeout: 30000 #空闲30S自动释放
      connection-timeout: 30000 #连接超时30秒
      connection-test-query: SELECT 1 #连接状态测试语句
      max-lifetime: 1800000 #连接最大存活时间，如果连接一直在用，超过这个时间会重新创建连接。

  quartz:
    #相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            instanceName: clusteredScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: false
            clusterCheckinInterval: 10000
            useProperties: false
            #错失触发策略时间
            misfireThreshold: 60000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
    #数据库方式
    job-store-type: jdbc

  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB #单个文件最大大小
      max-request-size: 1024MB #上传数据总大小

#server
server:
  port: 8080
  servlet:
    session:
      timeout: 30


# agv总管理线程池核心配置
#agv连接线程池核心配置
agv:
  manage:
    async:
      executor:
        thread:
          core_pool_size: 10
          max_pool_size: 300
          queue_capacity: 2
          keep_alive_time: 60
          name_prefix: Async-agv-manage-
  connection:
    async:
      executor:
        thread:
          core_pool_size: 100
          max_pool_size: 300
          queue_capacity: 2
          keep_alive_time: 10
          name_prefix: Async-agv-connection-
          status_thread_priority: 10

#任务呼叫盒连接线程池核心配置
mission_call:
  manage:
    executor:
      thread:
        core_pool_size: 10
        max_pool_size: 300
        queue_capacity: 2
        keep_alive_time: 10
        name_prefix: mission-call-manage-

logging:
  config: classpath:log4j2/log4j2-test.xml

#httpClient
http:
  maxTotal: 100
  defaultMaxPerRoute: 20
  connectTimeout: 3000
  connectionRequestTimeout: 3000
  socketTimeout: 10000
  staleConnectionCheckEnabled: true

LICENSE:
  LICENSE_TYPE: YOUIFleet
  SUBJECT: license
  STOREPASS: eXBsMTIz
  PUBLICKEYSSTOREPATH: /secure/publicCerts.store
  PUBLICALIAS: publicCert

# 调用agv api的配置
CALL_AGV_API_CONFIG:
  # agv执行指令失败时重新调用的次数
  FAULT_RESEND_COUNT: 5

LOG_INFO:
  DIRECTOR_PATH: /server/ads/logs/
  LOG_FIX: .log

mqtt:
  url: tcp://localhost:1883
  username: admin
  password: public
  cleanSession: false
  automaticReconnect: true
  completionTimeout: 2000
  keepAliveInterval: 90
  maxInflight: 1000 #同时发布topic的最大数量
  clientPre: fleet_
  retainTimeout: 120 #自定义保留消息超时时间 120s

scheduler:
  time:
    diff: 10
