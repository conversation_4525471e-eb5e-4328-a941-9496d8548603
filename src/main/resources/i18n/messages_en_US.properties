http.agv_execute_task_now=AGV is on the mission/charging/standby/mission queue, please stop it!
http.agv_has_no_execute_work=AGV is not performing the mission!
http.agv_not_login=AGV not logged in, please login first!
http.agv_not_have_this_map=AGV map does not sync, loading failed!
http.license_certificate_failure=Certificate is invalid!
http.license_certificate_not_uploaded=Certificate is not uploaded!
http.license_certificate_validate_failed=Certificate verification failed!
http.license_certificate_authentication_failed=Certificate validation failed!
http.certificate_expired=Certificate has expired!
http.license_certificate_has_been_uploaded=This certificate has been uploaded!
http.marker_type_is_not_Initial=The type of marker point is not initial point!
http.missing_parameter=Missing parameter\uFF01
http.mission_work_already_execute=This mission is in progress, can not be modified!
http.mission_work_status_is_not_pause_or_fault=Operation failed, the mission status is not pause or fail!
http.mission_work_status_is_not_running=Operation failed, the mission status is not in progress!
http.mission_work_status_is_not_wait_or_wait_input=Operation failed, the mission status is not in waiting!
http.image_does_not_exist=This image does not exist!
http.image_acquisition_failed=Fail to get the image!
http.schedule_plan_get_trigger_state_error=Fail to trigger dispatching plan!
http.schedule_plan_trigger_state_error=The trigger status of dispatching plan error!
http.user_password_error=Password error!
http.token_is_null=Token error! Please log in!
http.token_format_error=Token format validation failed! Please log in!
http.token_authentication_failed=Token authentication failed! Please log in!
http.token_expired=Login expired\uFF0Cplease login first!
http.have_no_enable_map=There is no map to enable, please enable it first!
http.no_mission_can_stop=There are no tasks to stop
http.params_error=The parameter is wrong!
http.marker_is_not_exist=Current point or target point does not exist!
http.marker_is_not_adjust=The current point is not an adjustment point!
http.dest_marker_is_not_worker=Target point is not work point!
http.params_data_is_empty=The parameter is empty!

service.agv_path_param_common_is_null=The default configuration parameters of navigation is empty!
service.docking_point_is_null=The docking point is not exist!
service.marker_already_binding_docking=The docking point is bound!
service.docking_can_only_binding_work_charge=Fail to bind! The docking point can only be charging point or work point!
service.docking_point_type_error=The type of docking point error!
service.marker_is_null=The marker point does not exist!
service.marker_code_already_exists=The marker point code already exists!
service.agv_map_is_null=The map does not exist!
service.agv_map_name_is_invalid=The map name is invalid!
service.agv_map_name_is_null=The map name is null!
service.agv_map_data_is_invalid=The map data is invalid!
service.no_usable_agv=The usable agv list is empty!

service.agv_map_already_enable=The map is enabled. Please disable it first!
service.mission_work_is_null=The work does not exist!
service.schedule_plan_is_null=The dispatch plan does not exist!
service.user_is_null=The user does not exist!
service.mission_variable_already_exists=The global variable already exist!
service.mission_work_variable_value_is_null=The global variable is not assigned!
service.mission_work_variable_is_null=The global variable does not exist!
service.mission_work_status_is_not_wait_or_wait_input_input=Mission status is not in waiting!
service.mission_is_null=This mission does not exist!
service.schedule_plan_add_quartz_error=Fail to add the dispatch plan!
service.schedule_plan_trigger_state_none=Fail to trigger the dispatch plan!
service.schedule_plan_status_is_not_pause=Fail to recovery the dispatch plan!
service.path_is_null=The path does not exist!
service.map_area_is_null=The area does not exist!
service.data_code_matrix_is_null=The QR code does not exist!
service.mission_action_list_is_null=The mission has no action, please create the action first!
service.directory_not_exist=Directory does not exist\uFF01
service.file_read_error=Failed to read file\uFF01
service.file_is_directory_or_not_exist=The file is a directory or the file does not exist\uFF01
service.file_download_error=File download failed\uFF01
service.floor_number_already_exist=service.floor_number_already_exist=Floor number {0} already exists in the elevator!
service.elevator_already_binding_floor=service.elevator_already_binding_floor=Marker {0} has been bound to other floors!
service.marker_binding_floor_not_delete=service.marker_binding_floor_not_delete=The elevator marker has been bound to the elevator floor, please untie it first!
service.mission_code_is_exist=mission code is exist\uFF01
service.mission_code_is_null=mission code is null!
service.close_auto_door_failed=Failed to close the auto door!
service.open_auto_door_failed=Failed to open the auto door!
service.auto_door_is_use=The robot is in the automatic (air shower) door. This operation is not allowed!
service.auto_door_is_error=Abnormal automatic door!
service.abnormal_code_is_exists=Service abnormal_code is exists!
service.file_missing_parameter=Service file missing parameter!
service.must_upload_excel_file_format=Service must upload excel file format!
service.read_excel_file_fail=Service read excel file fail!
service.add_map_fail=Failed to add map!
service.update_map_fail=Failed to update map!
service.delete_map_fail=Failed to delete map!
service.read_map_data_fail=Failed to read map data!
service.logout_FTP_Fail=FTP logout failed!
service.read_map_file_fail=Failed to read map file!
service.update_marker_fail=Failed to update marker!
service.update_path_fail=Failed to update path!
service.add_path_fail=Failed to add path!
service.delete_path_fail=Failed to delete path!
service.read_path_file_fail=Failed to read path file!
service.read_draft_file_fail=Failed to read draft file!
service.delete_marker_file_fail=Failed to delete marker!

service.change_ftp_folder_fail=Failed to change folder!
service.create_ftp_folder_fail=Failed to create folder!
service.save_ftp_file_fail=Failed to save ftp file!
service.ftp_connect_error=Failed to connect to ftp!

service.system_config_error=system config info error!
service.ftp_host_is_null=FTP host is null?

vehicle.mode_is_not_manual=Operation failed! AGV is not in manual mode\uFF01
vehicle.pause_mission_fault=Fail to mission action\uFF01
vehicle.resume_mission_fault=Fail to recovery mission!
vehicle.continue_mission_fault=Fail to continue mission!
vehicle.status_is_abnormal=Operation failed! AGV abnormal state\uFF01
vehicle.map_not_sync=Unsynchronized map\uFF01
vehicle.map_not_appoint=Map not specified!
vehicle.not_exception=The robot is not abnormal at present!
vehicle.current_control_model_is_manual=The current control mode is manual mode!
vehicle.exist_excute_mission_agv=There are robots performing tasks. Operation forbidden!
vehicle.not_enable=Robot not enabled\uFF01
vehicle.outline=Robot offline!
vehicle.already_sync=vehicle already sync,do not repeat operate!
notification.mission_work_error=Fail to execute the work, name:

socket.return_code_error=The communication returns an error code!

action.aim_marker_is_not_exist_or_disable=Target site is not exist or disable!
action.missing_path=Invalid path!
action.agv_is_off_track=AGV derailed!
action.marker_is_null_or_disable=does not exist or is disabled in this map!

user.login_name_or_phone_or_email_exist=User name, mobile phone number or email already exist!
user.admin_not_allow_delete=Admin users are not allowed to delete!
user.user_is_not_admin=Sorry, you are not an administrator\uFF01
user.username_or_password_is_empty=The user name or password is empty!
user.username_or_password_error=user or password error\uFF01
vehicle.current_control_model_is_auto=current control model is auto!
user.old_password_or_password_is_empty=The original password or confirmation password is empty
user.old_password_error=Old password error\uFF01
service.name_is_exists=Name {0}, already exists!
http.agv_disconnection_can_not_delete=AGV cannot be deleted by disconnection state!
service.mission_select_export_is_null=mission select export is null!
service.mission_code_is_no_exist=mission code is no exist!
service.mission_call_ip_and_port_exists=mission call ip or device address already_exists!
service.mission_call_exist_un_finished_work=mission call exist unfinished work\uFF0Cplease stop or wait successfully!
service.mission_export_data_is_null=mission export data is null!
service.agv_map_is_empty=Import file, no map data!
service.agv_map_data_error=Wrong map data!
vehicle.map_not_sync_or_map_syncing=Map not synchronized or in sync, please try later!
service.find_same_agv_map_and_status_is_enable=The system has a map with the same flag as the imported map and has been enabled. Please disable the system map before importing!
http.path_already_exists=Path already exists!
service.find_error_agvcode=Wrong robot code!
service.exist_manual_or_exception_agv=Robot with auto mode or abnormal state!
service.air_shower_door_already_exist=The air shower door already exists!
service.auto_door_not_exist=Automatic door does not exist!
service.air_shower_relation_not_exist=Associated door not found!
service.auto_door_position_error=Air shower door position data error!

path_plan.angle_weight_negative=Negative angle weight\uFF01
path_plan.auto_weight_negative=Negative auto weight\uFF01
service.agv_map_modify_fail=Map modification failed\uFF01
http.adjust_action_exist=Adjust action is exist!
http.agv_have_mission_work=The robot is performing a task, please stop the task first
service.the_status_do_not_cancel=The scheduling plan has been completed / cancelled. It cannot be cancelled!
service.the_scheduler_is_empty=The scheduling data does not exist!
service.the_status_is_do_not_delete=This status cannot be deleted!
service.agv_type_code_or_name_is_exist=Robot type code or name already exists!

trigger_period_range_out=trigger_period_range_out!
trigger_type_can_not_modify=trigger_type_can_not_modify!
trigger_missing_parameter=trigger_missing_parameter!
trigger.had.done=trigger had done,can not modify!
device_address_already_bind_mission=device address already bind mission!
sensor_code_already_exist=sensor code already exist!
sensor_code_is_empty=sensor code is empty!
trigger_selector_time_error=The start time of trigger timing must not be less than the current time!


logic.and=and
logic.or=or

#\u53F0\u79EF\u7535\u76F8\u5173\u914D\u7F6E
service.id_param_cant_be_null=\u5DE5\u4F5C\u7AD9\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
service.update_name_seq_duplicate=The stationNo,stationName and seq can't be dublicate in the same map
service.id_station_seq_cant_be_null=The stationNo, stationName and seq can't be null
_occupied=is existed
_same_map_station_name_occupied=station name already existed in the map
_same_map_seq_occupied=seq already existed in the map 