http.agv_execute_task_now=\u673A\u5668\u4EBA\u6B63\u5728\u4EFB\u52A1\u4E2D/\u5145\u7535\u4E2D/\u5F85\u673A\u4E2D/\u6267\u884C\u4EFB\u52A1\u94FE, \u8BF7\u5148\u505C\u6B62!
http.agv_has_no_execute_work=\u673A\u5668\u4EBA\u672A\u6267\u884C\u4EFB\u52A1!
http.agv_not_login=\u673A\u5668\u4EBA\u672A\u767B\u5F55,\u8BF7\u5148\u767B\u5F55\uFF01
http.agv_not_have_this_map=\u5730\u56FE\u672A\u540C\u6B65,\u8BF7\u5148\u540C\u6B65\u5730\u56FE\uFF01
http.license_certificate_failure=\u8BC1\u4E66\u5931\u6548!
http.license_certificate_not_uploaded=\u8BC1\u4E66\u672A\u4E0A\u4F20!
http.license_certificate_validate_failed=\u8BC1\u4E66\u6821\u9A8C\u5931\u8D25!
http.license_certificate_authentication_failed=\u8BC1\u4E66\u8BA4\u8BC1\u5931\u8D25\uFF01
http.certificate_expired=\u8BC1\u4E66\u5DF2\u8FC7\u671F\uFF01
http.license_certificate_has_been_uploaded=\u8BE5\u8BC1\u4E66\u5DF2\u4E0A\u4F20!
http.marker_type_is_not_Initial=\u8BE5\u6807\u8BB0\u70B9\u7C7B\u578B\u4E0D\u662F\u521D\u59CB\u70B9!
http.missing_parameter=\u7F3A\u5C11\u53C2\u6570!
http.mission_work_already_execute=\u4EFB\u52A1\u6267\u884C\u4E2D,\u4E0D\u80FD\u4FEE\u6539!
http.mission_work_status_is_not_pause_or_fault=\u64CD\u4F5C\u5931\u8D25,\u4EFB\u52A1\u7684\u72B6\u6001\u4E0D\u662F\u6682\u505C\u6216\u8005\u5931\u8D25!
http.mission_work_status_is_not_running=\u64CD\u4F5C\u5931\u8D25,\u8BE5\u4EFB\u52A1\u7684\u72B6\u6001\u4E0D\u662F\u6267\u884C\u4E2D!
http.mission_work_status_is_not_wait_or_wait_input=\u64CD\u4F5C\u5931\u8D25,\u8BE5\u4EFB\u52A1\u7684\u72B6\u6001\u4E0D\u662F\u7B49\u5F85!
http.image_does_not_exist=\u56FE\u7247\u4E0D\u5B58\u5728!
http.image_acquisition_failed=\u83B7\u53D6\u56FE\u7247\u5931\u8D25\uFF01
http.schedule_plan_get_trigger_state_error=\u8C03\u5EA6\u8BA1\u5212\u89E6\u53D1\u5931\u8D25!
http.schedule_plan_trigger_state_error=\u8C03\u5EA6\u8BA1\u5212\u89E6\u53D1\u72B6\u6001\u5F02\u5E38!
http.user_password_error=\u5BC6\u7801\u9519\u8BEF!
http.token_is_null=Token\u65E0\u6548\uFF0C\u8BF7\u5148\u767B\u5F55!
http.token_format_error=Token\u683C\u5F0F\u9A8C\u8BC1\u5931\u8D25\uFF0C\u8BF7\u6C42\u767B\u5F55!
http.token_authentication_failed=\u8EAB\u4EFD\u9A8C\u8BC1\u5931\u8D25\uFF0C\u8BF7\u5148\u767B\u5F55!
http.token_expired=\u767B\u5F55\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55!
http.have_no_enable_map=\u6682\u65E0\u542F\u7528\u5730\u56FE, \u8BF7\u5148\u542F\u7528!
http.no_mission_can_stop=\u6682\u65E0\u53EF\u505C\u6B62\u7684\u4EFB\u52A1!
http.params_error=\u53C2\u6570\u6709\u8BEF!
http.agv_disconnection_can_not_delete=\u975E\u79BB\u7EBF\u72B6\u6001\u4E0D\u80FD\u5220\u9664!
http.path_already_exists=\u8DEF\u5F84\u5DF2\u5B58\u5728\uFF01
http.marker_is_not_exist=\u5F53\u524D\u70B9\u6216\u76EE\u6807\u70B9\u4E0D\u5B58\u5728\uFF01
http.marker_is_not_adjust=\u5F53\u524D\u70B9\u4E0D\u662F\u8C03\u6574\u70B9\uFF01
http.dest_marker_is_not_worker=\u76EE\u6807\u70B9\u4E0D\u662F\u5DE5\u4F5C\u70B9\uFF01
http.params_data_is_empty=\u53C2\u6570\u4E3A\u7A7A!

service.agv_path_param_common_is_null=\u8DEF\u5F84\u5BFC\u822A\u53C2\u6570\u9ED8\u8BA4\u914D\u7F6E\u4E3A\u7A7A\uFF01
service.docking_point_is_null=\u5BF9\u63A5\u70B9\u4E0D\u5B58\u5728!
service.marker_already_binding_docking=\u5BF9\u63A5\u70B9\u5DF2\u7ED1\u5B9A!
service.docking_can_only_binding_work_charge=\u7ED1\u5B9A\u5931\u8D25\uFF01\u5BF9\u63A5\u70B9\u53EA\u80FD\u7ED1\u5B9A\u5145\u7535\u70B9\u6216\u8005\u5DE5\u4F5C\u70B9!
service.docking_point_type_error=\u5BF9\u63A5\u70B9\u7C7B\u578B\u9519\u8BEF\uFF01
service.marker_is_null=\u6807\u8BB0\u70B9\u4E0D\u5B58\u5728!
service.agv_map_name_is_invalid=\u5730\u56FE\u540D\u79F0\u4E0D\u5408\u6CD5!
service.agv_map_name_is_null=\u5730\u56FE\u540D\u79F0\u4E3A\u7A7A!
service.agv_map_data_is_invalid=\u5730\u56FE\u6570\u636E\u4E0D\u5408\u6CD5!
service.no_usable_agv=\u6CA1\u6709\u53EF\u7528\u5C0F\u8F66!

service.marker_code_already_exists=\u6807\u8BB0\u70B9\u7F16\u7801\u5DF2\u5B58\u5728!
service.agv_map_is_null=\u5730\u56FE\u4E0D\u5B58\u5728!
service.agv_map_already_enable=\u8BE5\u5730\u56FE\u5DF2\u7ECF\u542F\u7528, \u8BF7\u5148\u7981\u7528\uFF01
service.mission_work_is_null=\u4F5C\u4E1A\u4E0D\u5B58\u5728!
service.schedule_plan_is_null=\u8C03\u5EA6\u8BA1\u5212\u4E0D\u5B58\u5728!
service.user_is_null=\u7528\u6237\u4E0D\u5B58\u5728!
service.mission_variable_already_exists=\u5168\u5C40\u53D8\u91CF\u5DF2\u5B58\u5728!
service.mission_work_variable_value_is_null=\u5168\u5C40\u53D8\u91CF\u672A\u8D4B\u503C!
service.mission_work_variable_is_null=\u5168\u5C40\u53D8\u91CF\u4E0D\u5B58\u5728!
service.mission_work_status_is_not_wait_or_wait_input_input=\u4EFB\u52A1\u7684\u72B6\u6001\u4E0D\u662F\u7B49\u5F85!
service.mission_is_null=\u8BE5\u4EFB\u52A1\u4E0D\u5B58\u5728!
service.schedule_plan_add_quartz_error=\u8C03\u5EA6\u8BA1\u5212\u6DFB\u52A0\u5931\u8D25!
service.schedule_plan_trigger_state_none=\u8C03\u5EA6\u8BA1\u5212\u89E6\u53D1\u5931\u8D25!
service.schedule_plan_status_is_not_pause=\u8C03\u5EA6\u8BA1\u5212\u7684\u6062\u590D\u5931\u8D25!
service.path_is_null=\u8DEF\u5F84\u4E0D\u5B58\u5728!
service.map_area_is_null=\u533A\u57DF\u4E0D\u5B58\u5728\uFF01
service.data_code_matrix_is_null=\u4E8C\u7EF4\u7801\u70B9\u4E0D\u5B58\u5728\uFF01
service.mission_action_list_is_null=\u8BE5\u4EFB\u52A1\u6CA1\u6709\u52A8\u4F5C, \u8BF7\u5148\u65B0\u5EFA\u52A8\u4F5C\uFF01
service.directory_not_exist=\u76EE\u5F55\u4E0D\u5B58\u5728\uFF01
service.file_read_error=\u8BFB\u53D6\u6587\u4EF6\u5931\u8D25\uFF01
service.file_is_directory_or_not_exist=\u6587\u4EF6\u4E3A\u76EE\u5F55\u6216\u6587\u4EF6\u4E0D\u5B58\u5728\uFF01
service.file_download_error=\u6587\u4EF6\u4E0B\u8F7D\u5931\u8D25\uFF01
service.floor_number_already_exist=\u697C\u5C42{0}\u5728\u8BE5\u7535\u68AF\u5DF2\u5B58\u5728\uFF01
service.elevator_already_binding_floor=\u7535\u68AF\u70B9{0}\u7ED1\u5B9A\u4E86\u5176\u4ED6\u697C\u5C42\uFF01
service.marker_binding_floor_not_delete=\u8BE5\u7535\u68AF\u70B9\u5DF2\u7ECF\u7ED1\u5B9A\u7535\u68AF\u697C\u5C42, \u8BF7\u5148\u89E3\u9664\u4E0E\u7535\u68AF\u697C\u5C42\u7684\u7ED1\u5B9A\uFF01
service.mission_code_is_exist=\u4EFB\u52A1\u7F16\u7801\u5DF2\u5B58\u5728!
service.mission_code_is_null=\u4EFB\u52A1\u7F16\u7801\u4E3A\u7A7A!
service.name_is_exists=\u540D\u79F0 {0},\u5DF2\u7ECF\u5B58\u5728!
service.close_auto_door_failed=\u5173\u95ED\u81EA\u52A8\u95E8\u5931\u8D25!
service.open_auto_door_failed=\u5F00\u542F\u81EA\u52A8\u95E8\u5931\u8D25!
service.air_shower_door_already_exist=\u98CE\u6DCB\u95E8\u5DF2\u5B58\u5728\uFF01
service.auto_door_not_exist=\u81EA\u52A8\u95E8\u4E0D\u5B58\u5728!
service.air_shower_relation_not_exist=\u672A\u627E\u5230\u5173\u8054\u95E8\uFF01
service.auto_door_position_error=\u98CE\u6DCB\u95E8\u4F4D\u7F6E\u6570\u636E\u9519\u8BEF\uFF01
service.auto_door_is_use=\u673A\u5668\u4EBA\u4F7F\u7528\u81EA\u52A8(\u98CE\u6DCB)\u95E8\u4E2D, \u4E0D\u53EF\u8FDB\u884C\u8BE5\u64CD\u4F5C\uFF01
service.auto_door_is_error=\u81EA\u52A8\u95E8\u5F02\u5E38\uFF01
service.abnormal_code_is_exists=\u5F02\u5E38\u7801\u5DF2\u5B58\u5728\uFF0C\u4E0D\u53EF\u91CD\u590D\u6DFB\u52A0\uFF01
service.file_missing_parameter=\u6587\u4EF6\u7F3A\u5C11\u53C2\u6570\u6216\u53C2\u6570\u503C\u4E3A\u7A7A\uFF01
service.must_upload_excel_file_format=\u53EA\u80FD\u4E0A\u4F20excel\u6587\u4EF6\u683C\u5F0F\uFF01
service.read_excel_file_fail=\u8BFB\u53D6excel\u6587\u4EF6\u5931\u8D25\uFF01
service.add_map_fail=\u6DFB\u52A0\u5730\u56FE\u5931\u8D25\uFF01
service.update_map_fail=\u4FEE\u6539\u5730\u56FE\u5931\u8D25\uFF01
service.delete_map_fail=\u5220\u9664\u5730\u56FE\u5931\u8D25\uFF01
service.read_map_data_fail=\u8BFB\u53D6\u5730\u56FE\u6570\u636E\u5931\u8D25\uFF01
service.logout_FTP_Fail=FTP\u767B\u51FA\u5931\u8D25\uFF01
service.read_map_file_fail=\u8BFB\u53D6\u5730\u56FE\u6587\u4EF6\u5931\u8D25\uFF01
service.update_marker_fail=\u4FEE\u6539\u6807\u8BB0\u70B9\u5931\u8D25\uFF01
service.update_path_fail=\u4FEE\u6539\u8DEF\u5F84\u5931\u8D25\uFF01
service.add_path_fail=\u6DFB\u52A0\u8DEF\u5F84\u5931\u8D25\uFF01
service.delete_path_fail=\u5220\u9664\u8DEF\u5F84\u5931\u8D25\uFF01
service.read_path_file_fail=\u8BFB\u53D6\u8DEF\u7F51\u6587\u4EF6\u5931\u8D25!
service.read_draft_file_fail=\u67E5\u8BE2\u8349\u7A3F\u6587\u4EF6\u5931\u8D25\uFF01
service.delete_marker_file_fail=\u5220\u9664\u6807\u8BB0\u70B9\u5931\u8D25\uFF01

service.change_ftp_folder_fail=\u5207\u6362\u76EE\u5F55\u5931\u8D25!
service.create_ftp_folder_fail=\u521B\u5EFA\u76EE\u5F55\u5931\u8D25!
service.save_ftp_file_fail=\u4FDD\u5B58ftp\u6587\u4EF6\u5931\u8D25!
service.ftp_connect_error=ftp\u8FDE\u63A5\u5931\u8D25!

service.system_config_error=\u7CFB\u7EDF\u914D\u7F6E\u5F02\u5E38\uFF01!
service.ftp_host_is_null=ftp host \u4E3A\u7A7A\uFF0C\u8BF7\u5148\u8FDB\u884C\u914D\u7F6E?

vehicle.mode_is_not_manual=\u64CD\u4F5C\u5931\u8D25\u3002\u673A\u5668\u4EBA\u4E0D\u662F\u624B\u5DE5\u6A21\u5F0F\uFF01
vehicle.pause_mission_fault=\u6682\u505C\u4EFB\u52A1\u5931\u8D25\uFF01
vehicle.resume_mission_fault=\u6062\u590D\u4EFB\u52A1\u5931\u8D25\uFF01
vehicle.continue_mission_fault=\u7EE7\u7EED\u4EFB\u52A1\u5931\u8D25\uFF01
vehicle.status_is_abnormal=\u64CD\u4F5C\u5931\u8D25, \u673A\u5668\u4EBA\u5F02\u5E38\u72B6\u6001\u4E2D\uFF01
vehicle.map_not_sync=\u5730\u56FE\u672A\u540C\u6B65\uFF01
vehicle.map_not_sync_or_map_syncing=\u5730\u56FE\u672A\u540C\u6B65\u6216\u6B63\u5728\u540C\u6B65\u4E2D\uFF0C\u8BF7\u7A0D\u540E\u5C1D\u8BD5\uFF01
vehicle.map_not_appoint=\u5730\u56FE\u672A\u6307\u5B9A\uFF01
vehicle.not_exception=\u8BE5\u673A\u5668\u4EBA\u76EE\u524D\u65E0\u5F02\u5E38\uFF01
vehicle.current_control_model_is_manual=\u5F53\u524D\u63A7\u5236\u6A21\u5F0F\u4E3A\u624B\u52A8\u6A21\u5F0F\uFF01
vehicle.current_control_model_is_auto=\u5F53\u524D\u63A7\u5236\u6A21\u5F0F\u4E3A\u81EA\u52A8\u6A21\u5F0F\uFF01
vehicle.exist_excute_mission_agv=\u5B58\u5728\u6267\u884C\u4EFB\u52A1\u7684\u673A\u5668\u4EBA\uFF0C\u7981\u6B62\u64CD\u4F5C\uFF01
vehicle.not_enable=\u673A\u5668\u4EBA\u672A\u542F\u7528\uFF01
vehicle.outline=\u673A\u5668\u4EBA\u5DF2\u65AD\u7EBF!
vehicle.already_sync=\u673A\u5668\u4EBA\u5DF2\u540C\u6B65\u5730\u56FE,\u8BF7\u52FF\u91CD\u590D\u64CD\u4F5C!
notification.mission_work_error=\u4F5C\u4E1A\u6267\u884C\u5931\u8D25, \u540D\u79F0:

socket.return_code_error=\u901A\u4FE1\u8FD4\u56DE\u9519\u8BEF\u7801\uFF01

action.aim_marker_is_not_exist_or_disable=\u76EE\u6807\u7AD9\u70B9\u4E3A\u7A7A\u6216\u8005\u88AB\u7981\u7528\uFF01
action.missing_path=\u65E0\u6548\u7684\u8DEF\u5F84\uFF01
action.agv_is_off_track=\u673A\u5668\u4EBA\u8131\u8F68\uFF01
action.marker_is_null_or_disable=\u5728\u8BE5\u5730\u56FE\u4E2D\u4E0D\u5B58\u5728\u6216\u5DF2\u88AB\u7981\u7528\uFF01

user.login_name_or_phone_or_email_exist=\u7528\u6237\u540D\u3001\u624B\u673A\u53F7\u7801\u6216\u90AE\u7BB1\u5DF2\u5B58\u5728\uFF01
user.admin_not_allow_delete=admin\u7528\u6237\u4E0D\u5141\u8BB8\u5220\u9664\uFF01
user.user_is_not_admin=\u5BF9\u4E0D\u8D77\uFF0C\u60A8\u4E0D\u662F\u7BA1\u7406\u5458\uFF01
user.username_or_password_is_empty=\u7528\u6237\u540D\u6216\u5BC6\u7801\u4E3A\u7A7A\uFF01
user.username_or_password_error=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF\uFF01
user.old_password_or_password_is_empty=\u539F\u59CB\u5BC6\u7801\u6216\u786E\u8BA4\u5BC6\u7801\u4E3A\u7A7A!
user.old_password_error=\u65E7\u5BC6\u7801\u9519\u8BEF!
service.mission_code_is_no_exist=\u4EFB\u52A1\u7F16\u7801\u4E0D\u5B58\u5728\uFF01
service.mission_call_ip_and_port_exists=\u4EFB\u52A1\u547C\u53EBip\u6216\u8005\u8BBE\u5907\u5730\u5740\u5DF2\u5B58\u5728!
service.mission_call_exist_un_finished_work=\u547C\u53EB\u76D2\u5B58\u5728\u672A\u5B8C\u6210\u7684\u4EFB\u52A1\uFF0C\u8BF7\u5148\u505C\u6B62\u6216\u8005\u4EFB\u52A1\u5B8C\u6210\uFF01
service.mission_select_export_is_null=\u4EFB\u52A1\u5BFC\u51FA\u4E3A\u7A7A!
service.mission_export_data_is_null=\u4EFB\u52A1\u5BFC\u5165\u4E3A\u7A7A!
service.agv_map_is_empty=\u5BFC\u5165\u6587\u4EF6\uFF0C\u65E0\u5730\u56FE\u6570\u636E\uFF01
service.agv_map_data_error=\u9519\u8BEF\u7684\u5730\u56FE\u6570\u636E\uFF01
service.find_same_agv_map_and_status_is_enable=\u7CFB\u7EDF\u5B58\u5728\u4E0E\u5BFC\u5165\u5730\u56FE\u6709\u76F8\u540C\u6807\u5FD7\u4E14\u5DF2\u542F\u7528\u7684\u5730\u56FE\uFF0C\u8BF7\u7981\u7528\u7CFB\u7EDF\u5730\u56FE\u540E\u5BFC\u5165\uFF01
service.find_error_agvcode=\u5B58\u5728\u9519\u8BEF\u7684\u673A\u5668\u4EBA\u7F16\u7801\uFF01
service.exist_manual_or_exception_agv=\u5B58\u5728\u81EA\u52A8\u6A21\u5F0F\u6216\u5F02\u5E38\u72B6\u6001\u7684\u673A\u5668\u4EBA\uFF01
service.the_status_do_not_cancel=\u8C03\u5EA6\u8BA1\u5212\u5DF2\u7ECF\u6267\u884C\u5B8C\u6210/\u53D6\u6D88, \u4E0D\u53EF\u53D6\u6D88\uFF01
service.the_scheduler_is_empty=\u8BE5\u8C03\u5EA6\u6570\u636E\u4E0D\u5B58\u5728\uFF01
service.the_status_is_do_not_delete=\u8BE5\u72B6\u6001\u4E0D\u80FD\u5220\u9664\uFF01

path_plan.angle_weight_negative=\u89D2\u5EA6\u6743\u91CD\u51FA\u73B0\u8D1F\u503C\uFF01
path_plan.auto_weight_negative=\u81EA\u52A8\u6743\u91CD\u51FA\u73B0\u8D1F\u503C\uFF01
service.agv_map_modify_fail=\u6279\u91CF\u4FEE\u6539\u9519\u8BEF
http.adjust_action_exist=\u8C03\u6574\u52A8\u4F5C\u5DF2\u5B58\u5728\uFF01
http.agv_have_mission_work=\u673A\u5668\u4EBA\u6B63\u5728\u6267\u884C\u4EFB\u52A1\u4E2D\uFF0C\u8BF7\u5148\u505C\u6B62\u4EFB\u52A1\uFF01
service.agv_type_code_or_name_is_exist=\u673A\u5668\u4EBA\u7C7B\u578B\u7F16\u7801\u6216\u540D\u79F0\u5DF2\u5B58\u5728\uFF01


trigger_period_range_out=\u89E6\u53D1\u5668\u95F4\u9694\u8D85\u51FA\u6700\u5927\u503C
trigger_type_can_not_modify=\u89E6\u53D1\u5668\u7C7B\u578B\u4E0D\u53EF\u4FEE\u6539!
trigger_missing_parameter=\u89E6\u53D1\u5668\u7F3A\u5C11\u76F8\u5173\u53C2\u6570!
trigger.had.done=\u89E6\u53D1\u5668\u5DF2\u7ECF\u5B8C\u6210,\u4E0D\u53EF\u4FEE\u6539!
device_address_already_bind_mission=\u8BBE\u5907\u5730\u5740\u5DF2\u7ED1\u5B9A\u4EFB\u52A1!
sensor_code_already_exist=\u4F20\u611F\u5668\u7F16\u7801\u5DF2\u5B58\u5728!
sensor_code_is_empty=\u4F20\u611F\u5668\u7F16\u7801\u4E3A\u7A7A!
trigger_selector_time_error=\u89E6\u53D1\u5668\u5B9A\u65F6\u7684\u5F00\u59CB\u65F6\u95F4\u4E0D\u80FD\u5C0F\u4E8E\u5F53\u524D\u65F6\u95F4!

logic.and=\u5E76\u4E14
logic.or=\u6216\u8005
#\u53F0\u79EF\u7535\u76F8\u5173\u914D\u7F6E
service.id_param_cant_be_null=\u66F4\u65B0\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
service.update_name_seq_duplicate=\u66F4\u65B0\u53C2\u6570\u8FDD\u53CD\u5DE5\u4F5C\u7AD9\u540D\u79F0,\u5E8F\u5217\u53F7,\u6807\u8BB0\u70B9\u552F\u4E00\u6027\u7EA6\u675F
service.id_station_seq_cant_be_null=\u5DE5\u4F5C\u7AD9\u7684\u7F16\u53F7,\u540D\u79F0\u53CA\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
_occupied=\u88AB\u5360\u7528
_same_map_station_name_occupied=\u540C\u4E00\u5F20\u5730\u56FE\u5DE5\u4F5C\u7AD9\u540D\u79F0\u91CD\u590D
_same_map_seq_occupied=\u540C\u4E00\u5F20\u5730\u56FE\u5DE5\u4F5C\u987A\u5E8F\u91CD\u590D