http.agv_execute_task_now=æºå¨äººæ­£å¨ä»»å¡ä¸­/åçµä¸­/å½ä½ä¸­/æ§è¡ä»»å¡é¾, è¯·ååæ­¢!
http.agv_has_no_execute_work=æºå¨äººæªæ§è¡ä»»å¡!
http.agv_not_have_this_map=è¯»åå¤±è´¥ï¼å°å¾ä¸å­å¨!
http.agv_return_failed=æºå¨äººè¿åå¤±è´¥!
http.license_certificate_failure=è¯ä¹¦å¤±æ!
http.license_certificate_not_uploaded=è¯ä¹¦æªä¸ä¼ !
http.license_certificate_validate_failed=è¯ä¹¦æ ¡éªå¤±è´¥!
http.license_certificate_authentication_failed=è¯ä¹¦è®¤è¯å¤±è´¥ï¼
http.certificate_expired=è¯ä¹¦å·²è¿æï¼
http.license_certificate_has_been_uploaded=è¯¥è¯ä¹¦å·²ä¸ä¼ !
http.marker_type_is_not_Initial=è¯¥æ è®°ç¹ç±»åä¸æ¯åå§ç¹!
http.marker_start_or_end_not_exist=æ è®°ç¹èµ·ç¹æç»ç¹ä¸å­å¨!
http.missing_parameter=ç¼ºå°åæ°!
http.mission_work_already_execute=ä»»å¡æ§è¡ä¸­,ä¸è½ä¿®æ¹!
http.mission_work_status_is_not_pause_or_fault=æä½å¤±è´¥,ä»»å¡çç¶æä¸æ¯æåæèå¤±è´¥!
http.mission_work_status_is_not_running=æä½å¤±è´¥,è¯¥ä»»å¡çç¶æä¸æ¯æ§è¡ä¸­!
http.mission_work_status_is_not_wait_or_wait_input=æä½å¤±è´¥,è¯¥ä»»å¡çç¶æä¸æ¯ç­å¾!
http.mission_work_status_is_success=ä»»å¡æ§è¡æå!
http.mission_work_chain_status_is_success=ä»»å¡é¾å·²æ§è¡æåï¼
http.image_does_not_exist=å¾çä¸å­å¨!
http.image_acquisition_failed=è·åå¾çå¤±è´¥ï¼
http.schedule_plan_get_trigger_state_error=è°åº¦è®¡åè§¦åå¤±è´¥!
http.schedule_plan_trigger_state_error=è°åº¦è®¡åè§¦åç¶æå¼å¸¸!
http.user_password_error=å¯ç éè¯¯!
http.old_password_error=åå§å¯ç éè¯¯!
http.token_is_null=Tokenæ æï¼è¯·åç»å½!
http.token_format_error=Tokenæ ¼å¼éªè¯å¤±è´¥ï¼è¯·æ±ç»å½!
http.token_authentication_failed=èº«ä»½éªè¯å¤±è´¥ï¼è¯·åç»å½!
http.params_error=åæ°æè¯¯!
http.action_request_timeout=httpè¯·æ±è¶æ¶ï¼
http.adjust_action_exist=åå»ºå¤±è´¥ï¼æå¤ä¸æ¡æ°æ®ï¼
http.path_already_exists=è·¯å¾å·²å­å¨ï¼
http.marker_is_not_exist=å½åç¹æç®æ ç¹ä¸å­å¨ï¼
http.marker_is_not_adjust=å½åç¹ä¸æ¯è°æ´ç¹ï¼
http.dest_marker_is_not_worker=ç®æ ç¹ä¸æ¯å·¥ä½ç¹ï¼
http.error_loading_map_data=å è½½å°å¾æ°æ®åºéï¼
http.error_cleaning_map_data=æ¸çå°å¾æ°æ®åºéï¼
service.agv_code_is_exist=AGVç¼ç å·²å­å¨ï¼

service.The_path_has_bind_elevator_marker_or_adjust_marker=å½åè·¯å¾ä¸å·²ç»ç»å®çµæ¢¯ç¹æèæ¯è°æ´ç¹ï¼æ æ³æ·»å èªå¨é¨ã
service.The_path_has_bind_elevator_marker=å½åè·¯å¾å·²ç»ç»å®çµæ¢¯ç¹ï¼æ æ³æ·»å èªå¨é¨ã
service.The_path_has_bind_adjust_marker=å½åè·¯å¾å·²ç»ç»å®è°æ´ç¹ï¼æ æ³æ·»å èªå¨é¨ã
service.cancel_path_plan_fail=åæ¶è·¯å¾è§åå¤±è´¥ï¼
service.mission_work_status_is_not_wait_or_wait_input=è¯¥ä»»å¡æ­£å¨è¿è¡æå·²ç»æ§è¡æåï¼
service.mqtt_cmd_is_not_exist=mqttä¸»é¢ä¸­çæä»¤ä¸å­å¨ï¼
service.wait_marker_is_null=å°å¾ä¸æ²¡æå¾æºç¹ï¼è¯·å¨å°å¾ä¸åå»ºå¾æºç¹ï¼
service.charging_marker_is_null=å°å¾ä¸æ²¡æåçµç¹ï¼è¯·å¨å°å¾ä¸åå»ºåçµç¹ï¼
service.docking_point_is_null=å¯¹æ¥ç¹ä¸å­å¨!
service.marker_already_binding_docking=å¯¹æ¥ç¹å·²ç»å®!
service.docking_can_only_binding_work_charge=ç»å®å¤±è´¥ï¼å¯¹æ¥ç¹åªè½ç»å®åçµç¹æèå·¥ä½ç¹!
service.docking_point_type_error=å¯¹æ¥ç¹ç±»åéè¯¯ï¼
service.marker_is_null=æ è®°ç¹ä¸å­å¨!
service.marker_code_already_exists=æ è®°ç¹ç¼ç å·²å­å¨!
service.marker_type_is_not_elevator=æ è®°ç¹ç±»åä¸æ¯çµæ¢¯ç¹ï¼
service.agv_map_is_null=å°å¾ä¸å­å¨!
service.agv_map_type_error=å°å¾ç±»åéè¯¯!
service.agv_map_already_enable=è¯¥å°å¾å·²ç»å¯ç¨, è¯·åç¦ç¨ï¼
service.agv_map_not_enable=ç®æ å°å¾æªå¯ç¨ï¼
service.mission_work_is_null=å·¥ä½ä¸å­å¨!
service.schedule_plan_is_null=è°åº¦è®¡åä¸å­å¨!
service.user_is_null=ç¨æ·ä¸å­å¨!
service.mission_variable_already_exists=å¨å±åéå·²å­å¨!
service.mission_work_variable_value_is_null=å¨å±åéæªèµå¼!
service.mission_work_variable_is_null=å¨å±åéä¸å­å¨!
service.mission_work_status_is_not_wait_or_wait_input_input=ä»»å¡çç¶æä¸æ¯ç­å¾!
service.mission_is_null=è¯¥é¢è®¾ä»»å¡ä¸å­å¨!
service.mission_is_null_by_execute_chain=ä»»å¡ID={0}çä»»å¡ä¸å­å¨ï¼
service.mission_chain_is_null=è¯¥é¢è®¾ä»»å¡é¾ä¸å­å¨ï¼
service.mission_ids_is_null=è¯¥é¢è®¾ä»»å¡é¾ä¸­æ²¡æä»»å¡ï¼
service.schedule_plan_add_quartz_error=è°åº¦è®¡åæ·»å å¤±è´¥!
service.schedule_plan_trigger_state_none=è°åº¦è®¡åè§¦åå¤±è´¥!
service.schedule_plan_status_is_not_pause=è°åº¦è®¡åçæ¢å¤å¤±è´¥!
service.path_is_null=è·¯å¾ä¸å­å¨!
service.map_area_is_null=åºåä¸å­å¨ï¼
service.data_code_matrix_is_null=äºç»´ç ç¹ä¸å­å¨ï¼
service.mission_action_list_is_null=è¯¥ä»»å¡æ²¡æå¨ä½, è¯·åæ°å»ºå¨ä½ï¼
service.file_read_error=è¯»åæä»¶å¤±è´¥ï¼
service.directory_not_exist=ç®å½ä¸å­å¨ï¼
service.file_download_error=æä»¶ä¸è½½å¤±è´¥ï¼
service.elevator_is_null=çµæ¢¯ä¸ºç©ºï¼
service.adjust_action_is_null=è°æ´æ°æ®ä¸ºç©ºï¼
service.mission_work_chain_is_null=ä»»å¡é¾ä¸å­å¨ï¼
service.mission_work_chain_is_not_create=æä½å¤±è´¥, ä»»å¡é¾å·²ç»æ§è¡ï¼
service.floor_number_already_exist=æ¥¼å±{0}å¨è¯¥çµæ¢¯å·²å­å¨ï¼
service.elevator_already_binding_floor=æ è®°ç¹{0}å·²ç»ç»å®å¶ä»æ¥¼å±ï¼
service.marker_binding_floor_not_delete=è¯¥çµæ¢¯ç¹å·²ç»ç»å®çµæ¢¯æ¥¼å±, è¯·åè§£é¤ä¸çµæ¢¯æ¥¼å±çç»å®ï¼
service.mqtt_public_error=æ¨éæ°æ®å°mqttå¤±è´¥, topic{0},errorMsg:{1}ï¼
service.mqtt_disconnection=mqttéä¿¡æ­å¼, è¯·ç¨åéè¯ï¼
service.mqtt_connection_fault=mqttéä¿¡å¤±è´¥!
service.mqtt_params_agv_code_is_not_exist=mqttåæ°AGVç¼ç ä¸å­å¨ï¼
service.system_work_mode_is_null=ç³»ç»å·¥ä½æ¨¡å¼æ°æ®ä¸å­å¨ï¼
service.scheduler_scheduler_url_is_null=è°åº¦å°åä¸ºç©º!
service.mission_code_is_exist=ä»»å¡ç¼ç å·²å­å¨!
service.mission_code_is_null=ä»»å¡ç¼ç ä¸ºç©º!
service.mission_repeat_submission=ä»»å¡å·²åå»ºï¼æ ééå¤æäº¤!
service.mission_call_ip_and_port_exists=ä»»å¡å¼å«ipåç«¯å£å·²å­å¨
service.mission_code_is_no_exist=ä»»å¡ç¼ç ä¸å­å¨ï¼
service.mission_work_stop_or_executed_successfully=è¯·ååæ­¢ä»»å¡æèä»»å¡æ§è¡æå!
service.mission_modbus_write_error=åå¥modbusæ°æ®å¤±è´¥ï¼
service.mission_select_export_is_null=ä»»å¡å¯¼åºä¸ºç©º!
service.mission_export_data_is_null=ä»»å¡å¯¼å¥ä¸ºç©º!
service.mission_import_already_exists=å¯¼å¥ä»»å¡å·²å­å¨ï¼
service.mqtt_download_elevator_error=mqttä¸è½½çµæ¢¯åæ¥¼å±æ°æ®è¶æ¶/åºéï¼
service.scheduler_login_fault=ç»å½å¤±è´¥ï¼
service.scheduler_login_timeout=ç»å½è¶æ¶ï¼
service.mission_work_fault_by_reboot=ç³»ç»éå¯, ä»»å¡æ§è¡å¤±è´¥ï¼

vehicle.map_not_sync_or_disabled=å°å¾æªåæ­¥æè¢«ç¦ç¨!
vehicle.agv_disconnected=æºå¨äººæªè¿æ¥æå, è¯·ç¨ç­ï¼
vehicle.smart_charging_fault=åçµå¤±è´¥ï¼
vehicle.charging_marker_is_null=åçµç¹ä¸å­å¨æå·²è¢«ç¦ç¨ï¼
vehicle.move_to_marker_fault=ç§»å¨å¤±è´¥ï¼
vehicle.move_rotate_fault=åå°æè½¬å¤±è´¥ï¼
vehicle.docking_fault=å¯¹æ¥å¤±è´¥ï¼
vehicle.charging_fault=åçµå¤±è´¥ï¼
vehicle.leave_docking_fault=è±ç¦»å¯¹æ¥å¤±è´¥ï¼
vehicle.smart_waiting_fault=å½ä½å¤±è´¥ï¼
vehicle.qr_map_not_support_operation=äºç»´ç å°å¾ä¸æ¯æè¯¥æä½ï¼
vehicle.qr_navigation_not_support_operation=äºç»´ç å¯¼èªç±»åçæºå¨äººä¸æ¯æè¯¥æä½ï¼
vehicle.recording_map=æºå¨äººæ­£å¨å½å¶å°å¾ï¼
vehicle.mode_is_not_manual=æä½å¤±è´¥ãæºå¨äººä¸æ¯æå·¥æ¨¡å¼ï¼
vehicle.mode_is_not_record=æä½å¤±è´¥, æºå¨äººä¸æ¯å½å¶æ¨¡å¼ï¼
vehicle.status_is_abnormal=æä½å¤±è´¥, æºå¨äººå¼å¸¸ç¶æä¸­ï¼
vehicle.start_record_map_fault=æºå¨äººå¼å¯å½å¶å°å¾å¤±è´¥ï¼
vehicle.query_record_map_data_fault=æ¥è¯¢å½å¶å°å¾å®æ¶æ°æ®å¤±è´¥ï¼
vehicle.record_map_being_start=æºå¨äººåå¤å½å¶å°å¾ä¸­, è¯·ç¨ç­ï¼
vehicle.no_recording_map=æºå¨äººæªå½å¶å°å¾ï¼
vehicle.stop_record_map_fault=æºå¨äººåæ­¢å½å¶å°å¾å¤±è´¥ï¼
vehicle.no_normal_status_or_auto_mode=æä½å¤±è´¥ï¼æºå¨äººä¸æ¯èªå¨æ¨¡å¼ï¼
vehicle.pause_action_fault=æåå¨ä½å¤±è´¥ï¼
vehicle.resume_action_fault=æ¢å¤å¨ä½å¤±è´¥ï¼
vehicle.stop_action_fault=åæ­¢å¨ä½å¤±è´¥ï¼
vehicle.reset_action_fault=éç½®å¨ä½å¤±è´¥ï¼
vehicle.stop_smart_task_now=æºå¨äººåæ­¢åçµä¸­æå½ä½ä¸­ï¼
vehicle.no_execute_smart_task=æºå¨äººæªæ§è¡åçµæå½ä½ï¼
vehicle.execute_action_now=æºå¨äººå¨ä½ä¸­æå¨ä½å¼å¸¸, è¯·ç¨åæéç½®ï¼
vehicle.open_manual_mode_fault=æå¼æå¨æ¨¡å¼å¤±è´¥ï¼
vehicle.map_not_sync=å°å¾æªåæ­¥æèæªå¯ç¨ï¼
vehicle.exception_cannot_recover=è¯¥å¼å¸¸æ æ³æ¢å¤!
vehicle.not_exception=è¯¥æºå¨äººç®åæ å¼å¸¸ï¼
vehicle.smart_charging=æºå¨äººåçµä¸­, è¯·ååæ­¢ï¼
vehicle.map_type_fault=å½å¶ç±»åæè¯¯ï¼
vehicle.recording_grid_map=æºå¨äººæ­£å¨å½å¶æ æ ¼å°å¾ï¼
vehicle.recording_feature_map=æºå¨äººæ­£å¨å½å¶ç¹å¾å°å¾ï¼
vehicle.not_ready_for_the_mission=æºå¨äººæªåå¤å°±ç»ª, è¯·åå°æºå¨äººåæ¢ä¸ºèªå¨æ¨¡å¼ï¼
vehicle.status_is_abnormal_now=æºå¨äººæ§è¡åºé, è¯·åæ¸çéè¯¯!
vehicle.is_working_now=æºå¨äººæ­£å¨æ§è¡ä»»å¡/åçµ/å½ä½/ä»»å¡é¾, è¯·ååæ­¢!
vehicle.recording_map_now=æºå¨äººæ­£å¨å½å¶å°å¾ä¸­, è¯·ååæ­¢!
vehicle.mode_is_not_manual_now=è¯·åå°æºå¨äººçæ§å¶æ¨¡å¼åä¸ºæå¨æ¨¡å¼!
vehicle.agv_code_is_null=è¯·åç¼è¾æºå¨äººä¿¡æ¯ï¼
vehicle.scheduler_mode_can_not_update_agv_code=è°åº¦æ¨¡å¼ä¸ä¸è½ä¿®æ¹!
vehicle.scheduler_mode_can_not_disable_map=è°åº¦æ¨¡å¼ä¸ä¸è½ç¦ç¨å°å¾ï¼
vehicle.path_plan_exception=æºå¨äººè·¯å¾è§åå¼å¸¸

notification.low_power=æºå¨äººçµéè¾ä½ï¼è¯·åæ¶åçµï¼
notification.emergency_stop_by_button=æºå¨äººæ¥åï¼æ¥åæé®è§¦å!
notification.emergency_stop_by_safe=æºå¨äººæ¥åï¼å®å¨è®¾å¤è§¦å!
notification.emergency_stop_by_collision=æºå¨äººæ¥åï¼ç¢°æå¼å³è§¦å!
notification.lifting_motor_emergency_stop=æºå¨äººåéçµæºæ¥å!
notification.lifting_motor_drive_error=æºå¨äººåéçµæºé©±å¨æ¥é!
notification.roller_emergency_stop=æºå¨äººè¾ç­æ¥åï¼
notification.mission_work_error=ä»»å¡å·¥ä½æ§è¡å¤±è´¥, åç§°:

socket.connection_failed=éä¿¡è¿æ¥å¤±è´¥ï¼
socket.return_empty=éä¿¡è¿åä¸ºç©º!
socket.return_packet_header_error=éä¿¡åå¤´éè¯¯!
socket.return_packet_end_error=éä¿¡åå°¾éè¯¯!
socket.return_data_empty=éä¿¡åå®¹ä¸ºç©º!
socket.return_code_error=éä¿¡è¿åéè¯¯ç ï¼

action.thread_interrupt=æ§è¡è¢«ä¸­æ­ï¼
action.action_type_error=å¨ä½ç±»åéè¯¯ï¼
action.current_station_is_empty=å½åç«ç¹ä¸ºç©ºï¼
action.http_url_is_empty=URLå°åä¸ºç©ºï¼
action.http_post_return_error=HTTPè¯·æ±å¼å¸¸ï¼
action.internal_storage_data_error=åé¨å­å¨æ°æ®éè¯¯ï¼
action.reset_agv_error=æºå¨äººéç½®æä»¤åºéï¼
action.pause_agv_error=æºå¨äººæåæä»¤åºéï¼
action.stop_agv_error=æºå¨äººåæ­¢å½ä»¤åºéï¼
action.read_modbus_result_null=MODBUSè¯»åä¸ºç©ºï¼
action.write_modbus_fail=MODBUSåå¥å¤±è´¥ï¼
action.modbus_communication_failure=MODBUSéè®¯å¤±è´¥ï¼
action.wrong_value=æ°å¼å¡«åéè¯¯ï¼
action.aim_marker_is_empty=ç®æ ç«ç¹ä¸ºç©ºï¼
action.aim_marker_is_not_exist_or_disable=ç®æ ç«ç¹ä¸ºç©ºæèè¢«ç¦ç¨ï¼
action.agv_return_failed=æºå¨äººæ§è¡å¤±è´¥ï¼
action.agv_all_status_is_null=æºå¨äººå®æ¶æ»ç¶æä¸ºç©ºï¼
action.agv_runtime_status_is_null=æºå¨äººè¿è¡ç¶æä¸ºç©ºï¼
action.vehicle_is_null=æºå¨äººç¼å­æ°æ®ä¸ºç©ºï¼
action.agv_not_at_marker=æºå¨äººä¸å¨ç«ç¹ï¼
action.agv_current_marker_not_work_or_charge=æºå¨äººç¹ä½ä¸æ¯å·¥ä½ç¹æèåçµç¹ï¼
action.docking_point_is_null=æ æçå¯¹æ¥ç¹ï¼å¯¹æ¥ç¹ä¸ºç©ºï¼
action.agv_is_off_track=æºå¨äººè±è½¨ï¼
action.missing_path=æ æçè·¯å¾ï¼
action.hazards_path=å½åè·¯å¾å­å¨å®å¨éæ£ï¼è¯·ç§»å¨å°å¶å®è·¯å¾å¼å§è§åï¼
action.marker_is_null_or_disable=å¨è¯¥å°å¾ä¸­ä¸å­å¨æå·²è¢«ç¦ç¨ï¼
action.up_down_out_size=æ»ç­åéèå´è¶çï¼
action.condition_does_not_hold=å¨{0}ç§å, æ¡ä»¶å¤æ­æ æ³æ»¡è¶³, {1}(è¯»åå¼) {2}(æ¡ä»¶) {3}(æ¯è¾å¼)
action.elevator_apply_time_out=ç³è¯·çµæ¢¯è¶æ¶ï¼
action.elevator_move_time_out=ç§»å¨çµæ¢¯è¶æ¶ï¼
action.elevator_abnormal=çµæ¢¯å¼å¸¸ï¼
action.side_path_elevator_navigation_discontinuity=åé¨æ°æ®éè¯¯, ä½¿ç¨çµæ¢¯æ¶å¯¼èªç±»åä¸åç¡®ï¼
action.floor_is_null=åé¨æ°æ®éè¯¯, æ¥¼å±æ°æ®ä¸ºç©ºï¼
action.obstacle_avoidance_timeout=å¯éè¡è·¯å¾é¿æ¶é´å­å¨éç¢ç©, æºå¨äººæ æ³éè¡ï¼
action.stop_fail_by_id_different=è½¯ä»¶è®°å½çä»»å¡ID({0})ä¸åºçæ§è¡çID({1})ä¸ä¸è´ï¼
action.path_planning_error=è·¯å¾è§åéè¯¯ï¼
action.agv_at_elevator=æºå¨äººå½åå¨çµæ¢¯ç¹, è¯·åå°æºå¨äººç§»å¨åºæ¥ï¼
action.aim_marker_not_at_elevator=ç®æ ç¹ä¸è½æ¯çµæ¢¯ç¹ï¼
action.path_planning_time_out=è·¯å¾è§åè¶æ¶ï¼