WEB_SOCKET:
  MAX_IDLE_TIMEOUT: 10000

#Agv SOCKET API CODE Table.
AGV_API_CODE:
  #动作暂停
  ACTION_PAUSE: 40600
  #动作恢复
  ACTION_RESUME: 40601
  #动作停止
  ACTION_STOP: 40602
  #动作重置
  ACTION_RESET: 40603
  #status
  #任务状态
  ACTION_STATUS: 10015
  #control
  #Agv 检测对接点
  CHECK_DOCKING_POINT: 30005
  #Agv 避障控制开关
  OBSTACLE_AVOIDANCE: 30008
  #Agv 生成快捷路径
  GENERATE_SHORTCUT_PATHS: 30009
  #Agv 解除碰撞(短暂)急停
  SHORT_STOP_RECOVERY: 30014

PATH_PLAN:
  #是否打印修改路径权重的日志
  PRINT_LOG_OF_MODIFY_PATH_WEIGHT: true
  #修改日志文件路径
  LOG_FILE_OF_MODIFY_PATH_WEIGHT: /server/ads/logs/modify_sidePath_weight.txt
  #是否打印冲突处理的日志
  PRINT_LOG_OF_CONFLICT_PROCESS: true
  #修改冲突处理日志文件路径
  LOG_FILE_OF_CONFLICT_PROCESS: /server/ads/logs/conflict_process.txt
  #是否打印冲突处理的日志
  PRINT_LOG_OF_SEND_SIDE_PATH: true
  #修改冲突处理日志文件路径
  LOG_FILE_OF_SEND_SIDE_PATH: /server/ads/logs/send_side_path.txt
  #判断非工作点的最大脱轨距离(单位m)
  NON_WORK_POINT_MAX_DERAILMENT_DISTANCE: 0.2
  #判断工作点的最大脱轨距离(单位m)
  WORK_POINT_MAX_DERAILMENT_DISTANCE: 0.05
  #纯路径跟随AGV的安全检测距离(单位m)
  AGV_SAFE_DISTANCE_PURE_PURSUIT: 0.5
  #AGV运行中的路径阈值，以时间代价表示(单位s)
  AGV_SAFE_TIME_PURE_PURSUIT: 3.0
  #路径规划AGV的定位增加的权重(临时)
  LOCATION_PLUS_AUTO_WEIGHT: 0.0
  #路径规划已规划路径增加权重值(正向)
  PLANNED_PLUS_AUTO_WEIGHT_OBVERSE: 0.0
  #路径规划已规划路径增加权重值(反向)
  PLANNED_PLUS_AUTO_WEIGHT_REVERSE: 20.0
  #路径规划运行中的路径增加权重值(正向)
  RUNNING_PLUS_AUTO_WEIGHT_OBVERSE: 0.0
  #路径规划运行中的路径增加权重值(反向)
  RUNNING_PLUS_AUTO_WEIGHT_REVERSE: 12.0
  #路径规划AGV占用的单机区域加权重值
  SINGLE_AREA_PLUS_AUTO_WEIGHT: 0.0
  #机器人报错添加的权重
  AGV_ABNORMAL_STATUS_USER_WEIGHT: 2000.0
  #发送的最短路径长度
  SEND_SHORTEST_SIDE_PATH: 0.01
  #机器人处于避让点路径规划时, 检测前N段路径上是否存在机器人
  CHECK_FRONT_PATH_BY_AVOID_MARKER_PLAN: 4
  #开启路径导航阻挡绕路功能（被静止的机器人阻挡）
  OPEN_OBSTRUCT_DETOUR: false

# agv拍摄图片的存储路径
IMAGE_SAVE_PATH:
  # 摄像头拍照
  CAMERA_FOLDER: /server/data/image/camera

# 查询机器人是否可执行任务，获取结果超时时间（单位s）
MISSION_WORK:
  CAN_EXECUTE:
    TIME_OUT: 120
    #机器人在拒绝作业后间隔一定时间，可再分配 (单位s)
    REJECT_INTERVAL: 200

#呼叫盒服务端口
MISSION:
  CALL:
    PORT: 9080

AUTO_DOOR:
  #自动门控制线程睡眠时间 毫秒
  CONTROL_THREAD_SLEEP_TIME: 1000
  #modbus超时时间 毫秒
  MODBUS_TIME_OUT: 500
  #提前开门时间 秒
  OPEN_ADVANCE_TIME: 20