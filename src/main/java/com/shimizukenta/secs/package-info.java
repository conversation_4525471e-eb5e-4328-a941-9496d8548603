/**
 * SecsCommunicator.
 * 
 * <ul>
 * <li>To get new HSMS-SS communicator instance, ...</li>
 * </ul>
 * <ul>
 * <li>To open communicator, {@link SecsCommunicator#open()}.</li>
 * <li> To close communicator {@link SecsCommunicator#close()}.</li>
 * </ul>
 * <ul>
 * <li>To receive primary SECS-Message, {@link SecsCommunicator#addSecsMessageReceiveListener(SecsMessageReceiveListener)}.</li>
 * </ul>
 * 
 * <AUTHOR>
 *
 */
package com.shimizukenta.secs;
