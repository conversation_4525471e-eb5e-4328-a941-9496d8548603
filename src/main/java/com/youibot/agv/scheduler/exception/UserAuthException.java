package com.youibot.agv.scheduler.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * @Author：yangpeilin
 * @Date: 2020/4/21 19:16
 */
@ResponseStatus(code= HttpStatus.UNPROCESSABLE_ENTITY,reason="user auth fail")
public class UserAuthException extends  RuntimeException {

    public UserAuthException() {
        super();
    }

    public UserAuthException(String message) {
        super(message);
    }

    private String msg;
    private int code;

    public UserAuthException(String msg, int code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }
    public UserAuthException(String msg, int code, Throwable e) {
        super(msg, e);
        this.code = code;
    }
    public String getMsg() {
        return msg;
    }
    public void setMsg(String msg) {
        this.msg = msg;
    }
    public int getCode() {
        return code;
    }
    public void setCode(int code) {
        this.code = code;
    }
}
