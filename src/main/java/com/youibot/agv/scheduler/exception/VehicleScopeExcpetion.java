package com.youibot.agv.scheduler.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(code = HttpStatus.BAD_REQUEST, reason = "vehicle scope failed.")
public class VehicleScopeExcpetion extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public VehicleScopeExcpetion() {
        super();
    }

    public VehicleScopeExcpetion(String message) {
        super(message);
    }
}
