package com.youibot.agv.scheduler.exception;

import lombok.Data;
import org.springframework.boot.context.properties.bind.BindException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.security.InvalidParameterException;
import java.util.Date;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年6月22日 下午4:16:29
 */
@RestControllerAdvice
public class GobalExceptionHandler {

	@ResponseBody
	@ExceptionHandler
	// 返回的 HTTP 状态码为 HttpStatus.BAD_REQUEST
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	// 处理某个具体的异常
	public RespCode handleBindException(BindException ex) {
		return new RespCode(ex.getMessage(), HttpStatus.BAD_REQUEST.value());
	}

	@ResponseBody
	@ExceptionHandler
	// 返回的 HTTP 状态码为 HttpStatus.BAD_REQUEST
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	// 处理某个具体的异常
	public RespCode handleBindException(InvalidParameterException ex) {
		return new RespCode(ex.getMessage(), HttpStatus.BAD_REQUEST.value());
	}

	@ResponseBody
	@ExceptionHandler(value = LicenseAuthException.class)
	// 返回的 HTTP 状态码为 HttpStatus.LOCKED
	@ResponseStatus(HttpStatus.LOCKED)
	public RespCode handleAuthException(LicenseAuthException ex) {
		return new RespCode(ex.getMessage(), ex.getCode());
	}

	//自定义系统统一异常
	@ResponseBody
	@ExceptionHandler(value = YOUIFleetException.class)
	// 返回的 HTTP 状态码为 HttpStatus.INTERNAL_SERVER_ERROR
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public RespCode handleYOUIFleetException(YOUIFleetException ex) {
		return new RespCode(ex.getMessage(), ex.getCode());
	}

	@ResponseBody
	@ExceptionHandler
	// 返回的 HTTP 状态码为 HttpStatus.INTERNAL_SERVER_ERROR
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	// 处理全局异常
	public RespCode handleException(Exception ex) {
		return new RespCode(ex.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value());
	}

	@Data
	class RespCode {
		private Date timestamp = new Date();
		private String message;
		private Integer status;

		private RespCode(String message) {
			this.message = message;
		}

		private RespCode(String message, Integer status) {
			this.message = message;
			this.status = status;
		}
	}
}
