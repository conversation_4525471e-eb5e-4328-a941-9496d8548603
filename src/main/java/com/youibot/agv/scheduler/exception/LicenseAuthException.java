package com.youibot.agv.scheduler.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * @Author：yangpeilin
 * @Date: 2020/4/21 19:16
 */
@ResponseStatus(code= HttpStatus.BAD_REQUEST,reason="auth fail")
public class LicenseAuthException extends  RuntimeException {

    public LicenseAuthException() {
        super();
    }

    public LicenseAuthException(String message) {
        super(message);
    }

    private String msg;
    private int code;

    public LicenseAuthException(String msg, int code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }
    public LicenseAuthException(String msg, int code, Throwable e) {
        super(msg, e);
        this.code = code;
    }
    public String getMsg() {
        return msg;
    }
    public void setMsg(String msg) {
        this.msg = msg;
    }
    public int getCode() {
        return code;
    }
    public void setCode(int code) {
        this.code = code;
    }
}
