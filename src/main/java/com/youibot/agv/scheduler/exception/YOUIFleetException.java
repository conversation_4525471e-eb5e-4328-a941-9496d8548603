package com.youibot.agv.scheduler.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * @Author：yangpeilin
 * @Date: 2020/4/21 19:16
 */
@ResponseStatus(code= HttpStatus.INTERNAL_SERVER_ERROR,reason="youiFleet fail")
public class YOUIFleetException extends  RuntimeException {

    public YOUIFleetException() {
        super();
    }

    public YOUIFleetException(String message) {
        super(message);
    }

    private String msg;
    private int code;



    public YOUIFleetException(int code, String msg) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public YOUIFleetException(String msg, Throwable e) {
        super(msg, e);
    }

    public YOUIFleetException(int code, String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }
    public String getMsg() {
        return msg;
    }
    public void setMsg(String msg) {
        this.msg = msg;
    }
    public int getCode() {
        return code;
    }
    public void setCode(int code) {
        this.code = code;
    }
}
