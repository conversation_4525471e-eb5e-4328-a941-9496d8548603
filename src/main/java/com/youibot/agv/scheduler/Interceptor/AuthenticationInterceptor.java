package com.youibot.agv.scheduler.Interceptor;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.youibot.agv.scheduler.entity.User;
import com.youibot.agv.scheduler.exception.UserAuthException;
import com.youibot.agv.scheduler.service.UserService;
import com.youibot.agv.scheduler.util.JWTUtil;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.util.UserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class AuthenticationInterceptor implements HandlerInterceptor {

    @Autowired
    private UserService userService;
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthenticationInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 获取token
        String token = UserUtil.getToken(request);
        if (StringUtils.isEmpty(token)) {
            throw new UserAuthException(MessageUtils.getMessage("http.token_is_null"));
//            return requestDispatcher(request, response, ExceptionInfoEnum.NOT_LOGIN.getMessage());
        }
        String userId;
        try {
            userId = JWT.decode(token).getAudience().get(0);
        } catch (JWTDecodeException exception) {
            LOGGER.error("Token format error: token" + token);
            throw new UserAuthException(MessageUtils.getMessage("http.token_format_error"));
        }
        User user = userService.selectById(userId);
        if (StringUtils.isEmpty(user)) {
            throw new UserAuthException(MessageUtils.getMessage("service.user_is_null"));
        }
        // 验证token
        DecodedJWT jwt;
        try {
            Algorithm algorithm = Algorithm.HMAC256(user.getPassword());
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(JWTUtil.SIGN_ISSUER)
                    .withAudience(userId)
                    .build(); //Reusable verifier instance
            jwt = verifier.verify(token);


        } catch (JWTVerificationException exception) {
            throw new UserAuthException(MessageUtils.getMessage("http.token_authentication_failed"));
        }
        long time = jwt.getExpiresAt().getTime();
        if (time < 0 && time < System.currentTimeMillis()){
            LOGGER.debug("login expired...");
            throw new UserAuthException(MessageUtils.getMessage("http.token_expired"));
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {

    }

}
