package com.youibot.agv.scheduler.Interceptor;

import com.youibot.agv.scheduler.annotation.LicenseVerifys;
import com.youibot.agv.scheduler.constant.LicenseConstant;
import com.youibot.agv.scheduler.exception.LicenseAuthException;
import com.youibot.agv.scheduler.license.License;
import com.youibot.agv.scheduler.service.LicenseService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;

/**
 * license拦截器
 * @Author：yangpeilin
 * @Date: 2020/4/20 17:44
 */
@Component
public class LicenseInterceptor implements HandlerInterceptor {

    @Autowired
    private LicenseService licenseService;

    private static Logger logger = LoggerFactory.getLogger(LicenseInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //option 请求 排除
        if (request.getMethod().equals(RequestMethod.OPTIONS.name())) {
            response.setStatus(HttpStatus.OK.value());
            return true;
        }
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            LicenseVerifys annotation = method.getAnnotation(LicenseVerifys.class);
            //有该注解 即放行
            if (annotation == null) {
                License license = null;
                //将license信息设置到内存，防止每次都去数据库中取
                if (CollectionUtils.isEmpty(LicenseConstant.licenseMap) || LicenseConstant.licenseMap.get(LicenseConstant.LICENSE_TYPE) == null){
                    //查库
                    List<License> licenseList = licenseService.findAll();
                    if (!CollectionUtils.isEmpty(licenseList)){
                        license = licenseList.get(0);
                        LicenseConstant.licenseMap.put(LicenseConstant.LICENSE_TYPE, license);
                    }
                }else {
                    license = LicenseConstant.licenseMap.get(LicenseConstant.LICENSE_TYPE);
                }
                if (license == null){
                    logger.info("license is null");
//                    return  true;
                    throw new LicenseAuthException(MessageUtils.getMessage("http.license_certificate_authentication_failed"), 423);
//                    return verifyLicenseFailInfo(response);
                }
                long startTime = license.getStartTime().getTime();
                long endTime = license.getEndTime().getTime();
                long now = System.currentTimeMillis();
                //防止用户修改系统时间:如果系统当前时间小于数据更新时间，表明时间被修改，将更新时间值赋给now
                long time = license.getUpdateTime().getTime();
                if (time > now + LicenseConstant.TIME_ERROR){//允许误差范围
                    now = time;
                }
                //1.有效结束时间小于当前时间，认证失败 2.有效开始时间大于当前时间，认证失败 3.类型判断
                if (license.getExpire() == 1 || startTime > now || endTime < now || !license.getServerType().equals(LicenseConstant.LICENSE_TYPE)){
                    if (1 != license.getExpire()){//修改过期状态
                        license.setExpire(1);
                        licenseService.updateByPrimaryKeySelective(license);
                    }
                    logger.info("license is expired");
                    throw new LicenseAuthException(MessageUtils.getMessage("http.certificate_expired"), 423);
                }
            }
        }
        return true;
    }

}
