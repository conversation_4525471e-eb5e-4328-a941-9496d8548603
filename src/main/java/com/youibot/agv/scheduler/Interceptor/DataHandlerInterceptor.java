package com.youibot.agv.scheduler.Interceptor;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import org.apache.ibatis.binding.MapperMethod.ParamMap;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.defaults.DefaultSqlSession.StrictMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年6月4日 下午6:32:19
 * @param <E>
 */

@Component
@Intercepts({ @Signature(type = Executor.class, method = "update", args = { MappedStatement.class, Object.class }) })
public class DataHandlerInterceptor<E> implements Interceptor {
	private static final Logger LOGGER = LoggerFactory.getLogger(DataHandlerInterceptor.class);

	@Override
	public Object intercept(Invocation invocation) throws Throwable {
		MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
		SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
		if (SqlCommandType.DELETE.equals(sqlCommandType)) {
			return invocation.proceed();
		}
		Object object = invocation.getArgs()[1];
		if (object instanceof ParamMap) {
			ParamMap<?> map = (ParamMap<?>) object;
			if (!map.containsKey("record")){
				return invocation.proceed();
			}
			object = map.get("record");
			dataHandler(mappedStatement, object);
			// 批量操作
		} else if (object instanceof StrictMap) {
			StrictMap<?> map = (StrictMap<?>) object;
			List<E> list = (List<E>) map.get("collection");
			for (E e : list) {
				dataHandler(mappedStatement, e);
			}
		} else {
			dataHandler(mappedStatement, object);
		}
		return invocation.proceed();
	}

	// 时间处理
	private void dataHandler(MappedStatement mappedStatement, Object object) throws Throwable {
		SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
		// insert
		if (SqlCommandType.INSERT.equals(sqlCommandType)) {
			try {

				//如果为空的话，给默认值，不为空的话，用我们自己设置的值
				Field fieldCreate = object.getClass().getDeclaredField("createTime");
				fieldCreate.setAccessible(true);
				Object result = fieldCreate.get(object);
				if(result==null){
					fieldCreate.set(object, new Date());
				}

				Field fieldUpdate = object.getClass().getDeclaredField("updateTime");
				fieldUpdate.setAccessible(true);
				fieldUpdate.set(object, new Date());
			} catch (NoSuchFieldException e) {
				LOGGER.error("Error in field attribute", e);
			}
		} else {
			// update
			if (SqlCommandType.UPDATE.equals(sqlCommandType)) {
				try {
					Field fieldUpdate = object.getClass().getDeclaredField("updateTime");
					fieldUpdate.setAccessible(true);
					fieldUpdate.set(object, new Date());
				} catch (NoSuchFieldException e) {
					LOGGER.error("Error in field attribute", e);
				}

			}
		}

	}

	@Override
	public Object plugin(Object target) {
		return Plugin.wrap(target, this);
	}

	@Override
	public void setProperties(Properties properties) {

	}


}