package com.youibot.agv.scheduler.constant.enums;

public enum ErrorEnum {
    AGV_SYSTEM_ERROR(200000, "机器人系统异常"),
    AGV_PILOT_ERROR(201000, "机器人底盘异常"),
    PATH_NAVIGATION_ERROR(202000, "机器人路径导航异常"),
    PATH_PLAN_ERROR(202001, "机器人路径规划异常"),
    PATH_PLAN_TIME_OUT(202002, "机器人路径规划超时"),
    MARKER_IS_NOT_EXIST(202003, "路径导航中，目标点不存在"),
    ACTION_AGV_AT_ELEVATOR(202004, "机器人当前在电梯点, 请先将机器人移动出来"),
    ACTION_MARKER_IS_NULL_OR_DISABLE(202005, "目标站点为空或者被禁用"),
    ACTION_AIM_MARKER_NOT_AT_ELEVATOR(202006, "目标点不能是电梯点"),
    SERVICE_MQTT_DOWNLOAD_ELEVATOR_ERROR(202007, "mqtt下载电梯和楼层数据超时/出错"),
    SIDE_PATH_ELEVATOR_NAVIGATION_DISCONTINUITY(202008, "内部数据错误, 使用电梯时导航类型不准确"),
    ELEVATOR_MARKER_START_OR_END_NOT_EXIST(202009, "电梯路径上的标记点起点或终点不存在"),
    APPLY_ELEVATOR_INTERNAL_STORAGE_DATA_ERROR(202010, "申请电梯时，内部数据存储错误"),
    ELEVATOR_MOVE_TIME_OUT(202011, "移动电梯超时"),
    AGV_MAP_ID_IS_NULL(202012, "获取目标地图Id为空"),
    AGV_MAP_IS_NULL(202013, "地图不存在"),
    AGV_MAP_NOT_ENABLE(202014, "目标地图未启用"),
    ELEVATOR_IS_NULL(202015, "电梯为空"),
    MODBUS_COMMUNICATION_FAIL(202016, "MODBUS通讯失败"),
    ELEVATOR_APPLY_TIME_OUT(202017, "申请电梯超时"),
    ELEVATOR_ABNORMAL(202018, "电梯异常"),
    ADJUST_ACTION_IS_NULL(202019, "调整动作数据为空"),
    AUTO_DOOR_DATA_IS_NULL(202020, "自动门数据为空"),
    ACTION_AGV_IS_OFF_TRACK(202021, "机器人脱轨"),
    ACTION_HAZARDS_PATH(202022, "路径导航起点不可在设备范围内"),
    ROBOT_SWITCHES_TO_MANUAL_MODE_ERROR(202023, "切手动模式使任务异常"),
    MODBUS_READ_DATA_EQUAL_OR_CONTAIN_ERROR_CODE(202024, "从寄存器中读到错误码"),
    MODBUS_READ_NOT_EQUAL_TIME_OUT(202025, "读寄存器超时"),
    MISSING_PARAMETER(202026, "动作缺少参数"),
    READ_MODBUS_RESULT_NULL(202027, "读寄存器失败"),
    CURRENT_STATION_IS_EMPTY(202028, "机器人当前所在站点的位置不存在"),
    HTTP_POST_RETURN_ERROR(202029, "HTTP请求异常"),
    HTTP_URL_IS_EMPTY(202030, "请求URL地址为空"),
    MISSION_WORK_IS_NULL(202031, "当前任务不存在"),
    MISSION_IS_NULL(202032, "当前预设任务不存在"),
    FILE_UPLOAD_ERROR(202033, "上传文件出错"),
    FLOOR_IS_NULL(202034, "楼层数据为空"),
    OBSTACLE_AVOIDANCE_TIMEOUT(202035, "可通行路径长时间存在障碍物, 机器人无法通行"),
    ROBOT_SCRIPT_ACTION_ERROR(203000, "脚本机械臂异常"),
    CHARGE_START_ERROR(204001, "充电分配异常"),
    CHARGE_RUNNING_ERROR(204002, "充电执行异常"),
    CHARGE_STOP_ERROR(204003, "充电取消异常"),
    PARK_START_ERROR(205001, "泊车分配异常"),
    PARK_RUNNING_ERROR(205002, "泊车执行异常"),
    PARK_STOP_ERROR(205003, "泊车取消异常"),
    BATTERY_VALUE_LOW(206001, "电池电量低"),
    BUTTON_EMERGENCY_STOP(206002, "按钮急停"),
    EQUIPMENT_EMERGENCY_STOP(206003, "安全设备急停"),
    COLLECTION_EMERGENCY_STOP(206004, "碰撞急停"),
    LIFT_EMERGENCY_STOP(206006, "升降电机急停"),
    LIFT_ERROR(206007, "升降电机报错"),
    ABNORMAL_STOP_CHECK(202121, "机器人无故停止运行超过15秒"),
    ROLLER_EMERGENCY_STOP(206008, "辊筒急停");

    private Integer errorCode;
    private String errorMessage;

    private ErrorEnum(Integer errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public Integer code() {
        return this.errorCode;
    }

    public String msg() {
        return this.errorMessage;
    }
}
