package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/4 14:57
 */
public class ActionConstant {

    //电池充电是否需要对接
    public static final Integer BATTERY_CHARGE_NEED_DOCKING_YES = 0;//需要对接
    public static final Integer BATTERY_CHARGE_NEED_DOCKING_NO = 1;//不需要对接

    //是否设置摄像头参数
    public static final Integer CAMERA_PARAM_SETTING_NO = 0;//不设置
    public static final Integer CAMERA_PARAM_SETTING_YES = 1;//设置

    //是否设置摄像头预置点参数
    public static final Integer CAMERA_PRESET_POINT_SETTING_NO = 0;//不设置
    public static final Integer CAMERA_PRESET_POINT_SETTING_YES = 1;//设置

    //摄像头是否打开补光灯
    public static final Integer CAMERA_FILL_LIGHT_CLOSE = 0;//不打开
    public static final Integer CAMERA_FILL_LIGHT_OPEN = 1;//打开

    //是否设置红外热像仪参数
    public static final Integer INFRARED_PARAM_SETTING_NO = 0;//不设置
    public static final Integer INFRARED_PARAM_SETTING_YES = 1;//设置

    //是否设置红外热像仪预置点参数
    public static final Integer INFRARED_PRESET_POINT_SETTING_NO = 0;//不设置
    public static final Integer INFRARED_PRESET_POINT_SETTING_YES = 1;//设置

    //是否红外热像仪测温
    public static final Integer INFRARED_THERMOMETER_NO = 0;//不测温
    public static final Integer INFRARED_THERMOMETER_YES = 1;//测温

    //移动对接
    public static final Integer LEAVE_DOCKING = 0;//脱离对接
    public static final Integer MOVE_DOCKING = 1;//对接

    //是否在action中设置路径参数
    public static final Integer PATH_PARAM_ACTION_SETTING_NO = 0;//不设置
    public static final Integer PATH_PARAM_ACTION_SETTING_YES = 1;//设置

    //plc操作
    public static final Integer PLC_MODE_READ = 1;//读
    public static final Integer PLC_MODE_WRITE = 2;//写

    //remotePlc操作
    public static final String REMOTE_PLC_FUNCTION_READ_01 = "01";//  读值仅能是0/1
    public static final String REMOTE_PLC_FUNCTION_WRITE_05 = "05";// 写值仅能是0/1
    public static final String REMOTE_PLC_FUNCTION_READ_03 = "03";//  读值 结果为Integer值
    public static final String REMOTE_PLC_FUNCTION_WRITE_06 = "06";// 写值 器值为Integer值

    //滚筒左右上下料操作
    public static final int ROLLER_LEFT_IN = 1;//左侧进料
    public static final int ROLLER_LEFT_OUT = 2;//左侧出料
    public static final int ROLLER_RIGHT_IN = 3;//右侧进料
    public static final int ROLLER_RIGHT_OUT = 4;//右侧出料

    //货架随动操作
    public static final Integer SHELF_FOLLOW_UP_OPEN = 1;//打开随动
    public static final Integer SHELF_FOLLOW_UP_CLOSE = 0;//关闭随动

    //路径导航中路径的导航类型
    public static final int SIDE_PATH_NAVIGATION_TYPE_NORMAL = 0;//正常导航
    public static final int SIDE_PATH_NAVIGATION_TYPE_ADJUSTMENT = 1;//1、方向调整+导航
    public static final int SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR = 2;//进入电梯
    public static final int SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR = 3;//出来电梯
    public static final int SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR = 4;//乘坐电梯（包含切换地图）

    //路径导航中与切换地图点相邻的点的类型
    public static final Integer MARKER_NAVIGATION_TYPE_NORMAL = 0;//正常点
    public static final Integer MARKER_NAVIGATION_TYPE_IN_OUT = 1;//进出电梯点
    public static final Integer MARKER_NAVIGATION_TYPE_ELEVATOR = 2;//电梯点
    public static final Integer MARKER_NAVIGATION_TYPE_ADJUST = 3;//调整点
    public static final Integer MARKER_NAVIGATION_TYPE_ADJUST_DEST = 4;//调整目标点

    public static final Integer CAMERA_IS_RETURN_YES = 1;//摄像头执行完动作归位
    public static final Integer CAMERA_IS_RETURN_NO = 2;//摄像头执行完动作不归位

    public static final Integer PATH_OBSTACLE_AVOIDANCE_OPEN = 1;//开启绕障
    public static final Integer PATH_OBSTACLE_AVOIDANCE_CLOSE = 2;//关闭绕障
}
