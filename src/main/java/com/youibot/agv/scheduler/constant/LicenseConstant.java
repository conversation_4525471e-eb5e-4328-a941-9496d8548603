package com.youibot.agv.scheduler.constant;

import com.youibot.agv.scheduler.license.License;
import org.apache.commons.collections4.map.HashedMap;

import java.util.Map;

/**
 * @Author：yang<PERSON>ilin
 * @Date: 2020/4/20 17:55
 */
public class LicenseConstant {

    public static final String LICENSE_TYPE = "YOUICompass";

    public static Map<String, License> licenseMap = new HashedMap<>(2);

    //允许误差范围，此处配置根据定时任务中的执行间隔来设定
    public static final Long TIME_ERROR = 1000*60*60l;

    public static final Integer EXPIRE = 1;

}
