package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR>  E-mail:shishao<PERSON>@youibot.com
 * @version CreateTime: 2020/9/26 10:37
 */
public class SystemWorkModeConstant {

    //调度模式下的登录状态
    public static final String SCHEDULER_LOGIN_STATUS_LOGGED_IN = "LOGGED_IN";//已登录
    public static final String SCHEDULER_LOGIN_STATUS_NO_LOGGED_IN = "NO_LOGGED_IN";//未登录
    public static final String SCHEDULER_LOGIN_STATUS_LOGGED_FAULT = "LOGGED_FAULT";//登录失败

    /**
     * 调度模式下的在线状态
     */
    public static final String SCHEDULER_ONLINE_STATUS_ONLINE = "ONLINE";//在线
    public static final String SCHEDULER_ONLINE_STATUS_OFFLINE = "OFFLINE";//离线-就是连接断开
    public static final String SCHEDULER_ONLINE_STATUS_DIS_CONNECTION = "DISCONNECTION";//断开-本地


    //工作模式
    public static final String SYSTEM_WORK_MODE_LOCAL = "LOCAL";//本地模式
    public static final String SYSTEM_WORK_MODE_SCHEDULER = "SCHEDULER";//调度模式

}
