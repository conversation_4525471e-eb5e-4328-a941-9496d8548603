package com.youibot.agv.scheduler.constant.vo;

import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.ShapeInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


@Data
public class VehicleStatisticVO {

    @ApiModelProperty(value = "机器人名称",position = 0)
    protected String name;

    @ApiModelProperty(value = "地图id",position = 1)
    protected String agvMapId;

    @ApiModelProperty(value = "机器人分组",position = 2)
    protected String agvGroupId;

    @ApiModelProperty(value = "机器人类型",position = 3)
    protected String agvType;

    @ApiModelProperty(value = "连接时间",position = 4)
    protected Date connectedTime;

    @ApiModelProperty(value = "地图状态(自行更新) 0、未同步  1、已同步",position = 5)
    protected Integer mapStatus;// 0、未同步  1、已同步

    @ApiModelProperty(value = "地图指定状态(自行更新) 0:未指定; 1:已指定",position = 6)
    protected Integer appointStatus;//0:未指定; 1:已指定

    @ApiModelProperty(value = "任务状态",position = 7)
    protected Integer workStatus;

    @ApiModelProperty(value = "异常状态",position = 8)
    protected Integer abnormalStatus;

    @ApiModelProperty(value = "控制模式",position = 9)
    protected Integer controlMode;

    @ApiModelProperty(value = "导航类型 LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航",position = 10)
    protected String navigationType;//导航类型 LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航

    @ApiModelProperty(value = "机器人编号",position = 11)
    protected Boolean initialized;

    @ApiModelProperty(value = "设备列表信息",position = 12)
    protected ShapeInfo shapeInfo;

    @ApiModelProperty(value = "状态信息",position = 13)
    protected DefaultVehicleStatus defaultVehicleStatus;

    //错误提示信息，目前只提示智能充电(归位)报错信息
    @ApiModelProperty(value = "错误提示信息",position = 14)
    protected String errorMessage;

    @ApiModelProperty(value = "是否已经调用terminate标记",position = 15)
    protected boolean terminate;//是否已经调用terminate标记

    @ApiModelProperty(value = "启用状态 0：未启用 1：启用",position = 16)
    protected Integer status;//启用状态 0：未启用 1：启用

    @ApiModelProperty(value = "在线状态 0：离线 1：在线 2：断线",position = 17)
    private Integer onlineStatus;//在线状态 0：离线 1：在线 2：断线

    @ApiModelProperty(value = "智能充电必须充电电量阈值",position = 18)
    private Integer mustChargeBatteryValue;//智能充电必须充电电量阈值

    @ApiModelProperty(value = "执行的任务名称",position = 19)
    private String missionName;//执行的任务名称

    @ApiModelProperty(value = "执行的作业ID",position = 20)
    private String missionWorkId;//执行的作业ID

    @ApiModelProperty(value = "最后一次所在标记点ID",position = 21)
    private String lastMarkerId;//最后一次所在标记点ID

    @ApiModelProperty(value = "路径导航急停的开始时间",position = 21)
    private Long emc_jam_start_time;//路径导航急停的开始时间

}
