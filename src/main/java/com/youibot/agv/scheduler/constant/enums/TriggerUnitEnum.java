package com.youibot.agv.scheduler.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/1/7 19:27
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TriggerUnitEnum {


    SECOND("秒"),

    DAY("天"),

    WEEK("周"),

    MONTH("月");

    private String tips;

    /**
     * 转换成秒数
     *
     * @param period 触发间隔
     * @param unit   时间单位
     * @return
     */
    public static long toSeconds(Long period, String unit) {
        TriggerUnitEnum triggerUnitEnum = Arrays.stream(TriggerUnitEnum.values())
                .filter(x -> x.name().equals(unit)).findFirst().orElse(null);
        switch (triggerUnitEnum) {
            case SECOND:
                return period;
            case DAY:
                return TimeUnit.DAYS.toSeconds(period);

            case WEEK:
                return TimeUnit.SECONDS.convert(period * 7, TimeUnit.DAYS);
            case MONTH:
                return TimeUnit.SECONDS.convert(period * 30, TimeUnit.DAYS);
            default:
                return Long.parseLong("3600");
        }
    }


}
