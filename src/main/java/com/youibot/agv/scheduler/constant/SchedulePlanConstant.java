package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月17日 下午7:39:42
 */
public class SchedulePlanConstant {
    //调度计划的执行状态
    //准备
    public static final String SCHEDULE_STATUS_READY = "READY";
    //停止
    public static final String SCHEDULE_STATUS_SHUTDOWN = "SHUTDOWN";
    //错误
    public static final String SCHEDULE_STATUS_ERROR = "ERROR";
    //运行
    public static final String SCHEDULE_STATUS_RUNNING = "RUNNING";
    //暂停
    public static final String SCHEDULE_STATUS_PAUSE = "PAUSE";
    //删除
    public static final String SCHEDULE_STATUS_DELETE = "DELETE";
    //成功
    public static final String SCHEDULE_STATUS_SUCCESS = "SUCCESS";

    //JobKey的属性
    public static final String SCHEDULE_JOB_KEY_JOB_GROUP = "DEFAULT";
    public static final String SCHEDULE_JOB_KEY_TRIGGER_GROUP = "DEFAULT";

    //TriggerState    NONE, NORMAL, PAUSED, COMPLETE, ERROR, BLOCKED  STATE
    //无
    public static final String SCHEDULE_TRIGGER_STATUS_NONE = "NONE";
    //正常状态
    public static final String SCHEDULE_TRIGGER_STATUS_NORMAL = "NORMAL";
    //暂停状态
    public static final String SCHEDULE_TRIGGER_STATUS_PAUSED = "PAUSED";
    //完成状态
    public static final String SCHEDULE_TRIGGER_STATUS_COMPLETE = "COMPLETE";
    //阻塞
    public static final String SCHEDULE_TRIGGER_STATUS_BLOCKED = "BLOCKED";
}
