package com.youibot.agv.scheduler.constant.vo;

import java.util.List;

import com.alibaba.excel.util.StringUtils;
import com.google.common.collect.Lists;

import lombok.Data;

/**
 * http: 
 *  url: *************:8998/api/server/getdata
 *  methond: post
 *  host :货架ip
 *  data:
 *  {"ACC": "0.10","smkdataList": [{"SMK": "20.27","T": "16.23","H": "28.84"},{"SMK": "0.00","T": "0.00","H": "0.00"},{"SMK": "0.00","T": "0.00","H": "0.00"}]}
 *  
 * <AUTHOR>
 *报加速度温湿度参数
 */
@Data
public class TjdSensorDto {
	
	
	/**
	 * 台积电温湿度传感器数据
	 */
	private  static final TjdSensorDto INSTANCE = new TjdSensorDto();
	
	
	/** 清空 同时返回一个
	 * @return
	 */
	public  synchronized static final TjdSensorDto clear() {
		 
		INSTANCE.X = StringUtils.EMPTY ;
		INSTANCE.Y = StringUtils.EMPTY ;
		INSTANCE.Z = StringUtils.EMPTY ;
		INSTANCE.smkdataList.clear();
		
		return INSTANCE;
		
	}
	/**
	 * 加速度
	 */
	private String X;
	/**
	 * 加速度
	 */
	private String Y;
	/**
	 * 加速度
	 */
	private String Z;
	
	/**
	 * 1 ,2 ,3 号传感器数据
	 */
	private List<SmokeSensor> smkdataList = Lists.newArrayList();
	

	
	/**
	 * <AUTHOR>
	 *  烟感温湿度传感器 数据结构
	 */
	@Data
	public static final  class SmokeSensor {
		
		/**
		 * 烟感参数
		 */
		private String SMK;
		
		/**
		 * 温度参数>
		 */
		private String T;
		
		
		/**
		 * 湿度参数
		 */
		private String H;
	}
}
