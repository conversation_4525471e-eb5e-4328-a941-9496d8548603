package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR>
 * @Date :Created in 下午3:42 2020/1/2
 * @Description :
 * @Modified By :
 * @Version :
 */
public class NotificationConstant {

    public static final String READ = "READ";                                               //已读消息
    public static final String UNREAD = "UNREAD";                                           //未读消息

    //工作模式
    public static final String LOCAL_MODE = "LOCAL";//本地模式
    public static final String SCHEDULER_MODE = "SCHEDULER";//调度模式

    public static final Integer INFO_SCALE = 1;                                             //普通消息
    public static final Integer WARN_SCALE = 2;                                             //警告消息
    public static final Integer ERROR_SCALE = 3;                                            //错误消息

    public static final String BATTERY_VALUE_LOW = "BATTERY_VALUE_LOW";                     //电池电量低
    public static final String BUTTON_EMERGENCY_STOP = "BUTTON_EMERGENCY_STOP";             //按钮急停
    public static final String EQUIPMENT_EMERGENCY_STOP = "EQUIPMENT_EMERGENCY_STOP";       //安全设备急停
    public static final String COLLECTION_EMERGENCY_STOP = "COLLECTION_EMERGENCY_STOP";     //碰撞急停
    public static final String MISSION_ERROR = "MISSION_ERROR";                             //任务错误
    public static final String LIFT_EMERGENCY_STOP = "LIFT_EMERGENCY_STOP";                 //升降电机急停
    public static final String LIFT_ERROR = "LIFT_ERROR";                                   //升降电机报错
    public static final String ROLLER_EMERGENCY_STOP = "ROLLER_EMERGENCY_STOP";             //辊筒急停

}
