package com.youibot.agv.scheduler.constant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class AGVMapVO {

    @ApiModelProperty(value = "ID", position = 0)
    private String id;

    @ApiModelProperty(value = "类型：LASER_MAP：激光地图，VIRTUAL_MAP：虚拟地图，CAD_MAP：CAD地图", position = 1)
    private String type;

    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    @ApiModelProperty(value = "描述", position = 3)
    private String description;

    @ApiModelProperty(value = "分辨率", position = 7)
    private Double resolution;

    @ApiModelProperty(value = "物理高度 单位：m", position = 10)
    private Double physicsHeight;

    @ApiModelProperty(value = "物理宽度   单位：m", position = 11)
    private Double physicsWidth;

    @ApiModelProperty(value = "版本号", position = 13)
    private Integer version;

    @ApiModelProperty(value = "使用状态 ENABLE:启用  DISABLE:禁用", position = 14)
    private String usageStatus;



}
