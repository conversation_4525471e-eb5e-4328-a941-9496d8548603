package com.youibot.agv.scheduler.constant.enums;

import com.youibot.agv.scheduler.entity.WorkCycleConfig;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public enum ReleaseMode {

    /**
     * 人工
     */
    AUTO {
        @Override
        public String getAgvGroupId( Vehicle vehicle  ,WorkCycleConfig config) {
            return config.getAgvGroupId();
        }
    },
    /**
     * 系统放行
     */
    SYSTEM {
        @Override
        public String getAgvGroupId(Vehicle vehicle , WorkCycleConfig config) {
            return config.getAgvGroupId();
        }
    },

    /**
     * 系统切换地图同时放行
      */
    SYSTEM_TOOGLE {
        @Override
        public String getAgvGroupId(Vehicle vehicle ,WorkCycleConfig config) {
            String targetAgvGroupId = StringUtils.equals( vehicle.getAgvGroupId(), config.getAgvGroupId()) ? config.getAltAgvGroupId() : config.getAgvGroupId();
            return  targetAgvGroupId ;
        }
    },
    MANUAL {

    },

    /**
     *   联动模式,当前工作站放行自动放行下一个工作站的车辆
     */
    LINKAGE {

    },

    ;

  public   boolean isThis( Object releaseMode){
       return Objects.equals(Objects.toString(this.ordinal()), Objects.toString(releaseMode));

    }

    /**
     * 获得放行模式
     * @param releaseMode
     * @return
     */
    public  static   ReleaseMode getReleaseMode( Object releaseMode){
        for (ReleaseMode mode :
                ReleaseMode.values()) {
            if(mode.isThis( releaseMode)){
                return  mode;
            }

        }
        return  null;
    }
   public  String getTargetAgvGroupId( Vehicle vehicle ,WorkCycleConfig config){
      if(Objects.nonNull( config) &&  isThis( config.getReleaseMode())){
          return getAgvGroupId(vehicle ,config);
      }
      return  vehicle.getAgvGroupId();
   }

    public  String getAgvGroupId(Vehicle vehicle , WorkCycleConfig config){
        return  config.getAgvGroupId();
    };
}
