package com.youibot.agv.scheduler.constant.vo;

import com.youibot.agv.scheduler.util.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


@Data
public class MissionStatisticVO {

    @ApiModelProperty(value = "机器人编号",position = 0)
    private String agvCode;

    @ApiModelProperty(value = "任务次数", position = 1)
    private Integer workNum;

    @ApiModelProperty(value = "任务时间", position = 2)
    private Long workTime;

    @ApiModelProperty(value = "充电次数", position = 3)
    private Integer chargeNum;

    @ApiModelProperty(value = "充电总时间 单位: 秒", position = 4)
    private Long chargeTime;

    @ApiModelProperty(value = "泊车次数", position = 5)
    private Integer parkNum;

    @ApiModelProperty(value = "泊车总时间 单位: 秒", position = 6)
    private Long parkTime;

    @ApiModelProperty(value = "异常次数", position = 7)
    private Integer errorNum;

    @ApiModelProperty(value = "异常总时间 单位: 秒", position = 8)
    private Long errorTime;

    @ApiModelProperty(value = "空闲总时间 单位: 秒", position = 9)
    private Long freeTime;

    public MissionStatisticVO(){
        this.workNum = 0;
        this.workTime = 0L;

        this.chargeNum = 0;
        this.chargeTime = 0L;

        this.parkNum = 0;
        this.parkTime = 0L;

        this.errorNum = 0;
        this.errorTime = 0L;

        this.freeTime = 0L;
    }

    public void computeFreeTime(Date start , Date end){
        if(start==null || end==null || !start.before(end)){
            return;
        }
        long seconds = DateUtils.getSeconds(start, end);
        this.freeTime = seconds - this.workTime - this.chargeTime - this.parkTime - this.errorTime;
        this.freeTime  = this.freeTime < 0 ? - this.freeTime : this.freeTime;
    }

}
