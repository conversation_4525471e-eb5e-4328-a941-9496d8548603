package com.youibot.agv.scheduler.constant.vo;

import com.youibot.agv.scheduler.entity.TriggerSelector;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TriggerSelectorVO extends TriggerSelector {

    @ApiModelProperty(value = "触发器关联的任务编码")
    private String missionCode;

    @ApiModelProperty(value = "触发器关联的任务名称")
    private String missionName;
}
