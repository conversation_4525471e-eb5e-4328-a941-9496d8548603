package com.youibot.agv.scheduler.constant.vo;

import java.util.List;

import lombok.Data;

/**
 * http: 
 *  url: *************:8998/api/server/getdata
 *  methond: post
 *  host :货架ip
 *  data:
{"event": "reply","dataList": [{"name":"charge01","u": "0.0","i": "0.0","t": "36.0","oc": "0"},{"name":"charge02","u": "0.0","i": "0.0","t": "43.0","oc": "0"},{"name":"charge03","u": "0.0","i": "0.0","t": "41.0","oc": "0"},{"name":"charge04","u": "55.5","i": "60.0","t": "28.0","oc": "0"}]}
 *  
 * <AUTHOR>
 *充电桩本体的数据
 */
@Data
public class TjdChargerDto {
	
	
	private  String	event ;
   
   
	private List<TjdChargerItem>  dataList ;
   
   @Data
   public final static class TjdChargerItem {
	   
	 private  String name ;
	   
	 private  String u ;
	   
	 private String i ;
	   
	 private  String t ;
	   
	 private String oc ;
	   
   }
}
