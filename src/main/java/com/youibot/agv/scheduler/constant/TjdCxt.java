package com.youibot.agv.scheduler.constant;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.google.common.collect.*;
import com.google.common.collect.Table.Cell;
import com.shimizukenta.secs.secs2.Secs2;
import com.shimizukenta.secs.secs2.Secs2List;
import com.youibot.agv.scheduler.constant.enums.ErrorEnum;
import com.youibot.agv.scheduler.constant.enums.ReleaseMode;
import com.youibot.agv.scheduler.controller.v3.CompassController;
import com.youibot.agv.scheduler.controller.v3.GetBestWayWithMarkIdController;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.*;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.path.NodeCodeList;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.BlockCheckService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.SidePathResourcePool;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.listener.event.NotifyMessageEvent;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage.ReportType;
import com.youibot.agv.scheduler.param.MissionWorkParam;
import com.youibot.agv.scheduler.param.TMSMarkerIdSequence;
import com.youibot.agv.scheduler.param.TjdScreenItemDto;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.TjdHelper;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.BatteryStatus;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.PositionStatus;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.SpeedStatus;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.BidiMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;
import org.apache.commons.collections4.map.ListOrderedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;

/**
 * <AUTHOR> 台积电实体
 */
@Slf4j
@Data
public class TjdCxt {

	public static final String INVALID_READ = "Invalid Read";
	/**
	 * @Fields MARKER_CODE : 台积电项目移动到导航点参数对应的key值
	 */
	public static final String MARKER_CODE = "markerCode";

	public static final String AGV_MAPID = "agvMapId";

	/**
	 * @Fields AGV : 台积电查询station列表标识字段
	 */
	public static final String STATION = "STATION";

	/**
	 * 任务的名称
	 */
	public static final String MISSION_WORK_NAME_M26 = "m26";

	/**
	 * 仅仅下料
	 */
	public static final String UNLOADONLY = "UNLOADONLY";

	/**
	 * @Fields TRANFER_DOWN : 小车下料标识
	 */
	public static final String TRANFER_DOWN = "TransferDown";

	/**
	 * @Fields TRANFER_UP :小车上料标识
	 */
	public static final String TRANFER_UP = "TransferUp";

	/**
	 * @Fields AGV : 台积电查询agv列表标识字段
	 */
	public static final String AGV = "AGV";
	/**
	 * 站点AGV数量超限 语音文件名称
	 */
	public  static final String KEY_MUSIC_FILE_NAME ="musicName";
	/**
	 * 站点AGV数量超限 语音播放次数
	 */
	public  static final String KEY_MUSIC_TIMES = "playTime";
	public  static final String KEY_PLAY_TRIGGER = "playTrigger";
	public  static final int KEY_PLAY_TRIGGER_VALUE =1;
	public  static final int KEY_MUSIC_TIMES_VALUE = 5;
	/**
	 * @Fields HANDLE_VEHICLES : 正在分配任务的车辆, 避免并发的情况下 同时从内存和数据库查询导致不一致
	 */
	public static final Set<String> HANDLING_VEHICLES = Sets.newConcurrentHashSet();

	/**
	 * @Fields CYCLE_MAP : 环状线
	 */
	public static final ListOrderedMap<String, WorkCycleConfig> CYCLE_MAP = new ListOrderedMap<>();

	/**
	 * @Fields STATION_MARK_CODE_MAP : 工作站和导航点互相对应的map
	 */
	public static final DualHashBidiMap<String, String> STATION_MARK_CODE_MAP = new DualHashBidiMap<>();

	/**
	 * 一键离线
	 */
	public static final String ON_KEY_OFF_LINE = "on_key_off_line";
	/**
	 * 一键掉头
	 */
	public static final String ON_KEY_U_TURN = "on_key_u_turn";
	/**
	 * 是否巡线
	 */
	public static final String LINEPATROL = "linePatrol";

	/**
	 * 放行模式
	 */
	public static final String RELEASE_MODE = "releaseMode";
	/**
	 * @Fields WAIT_MES_COMMAND_MINUTES : 到站之后等待mes下达下货指令的时间 等待分钟
	 */
	public static final Integer WAIT_MES_COMMAND_MINUTES = 3;

	public static final String SEQ = "seq";

	/**
	 * AGV 是否异常停止检测次数： 每两秒检测一次
	 */
	public static final Integer AGV_STOP_CHECK_TIMES = 8;

	public static final String AGV_STOP_CHECK_TIMES_MAP_KEY = "count";
	/**
	 * @Fields WAIT_MES_COMMAND_SECONDS : 到站之后等待mes下达下货指令的时间 3 分钟
	 */
	public static final Integer WAIT_MES_COMMAND_SECONDS = WAIT_MES_COMMAND_MINUTES * 60;

	/**
	 * @Fields CYCLE_FIRE_PATH : 火灾环状路线配置
	 */
	public static final Set<String> CYCLE_FIRE_PATH = Sets.newConcurrentHashSet();
	
	/**
	 * 禁用alert 
	 */
	public static final Set<Integer> DISABLE_ALERT = Sets.newConcurrentHashSet();

	/**
	 * 控制消息上报频率
	 */
	public static final Cache<Object, Object> cache = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES)
			.maximumSize(300).build();
	

	/**
	 * 离站缓存
	 */
	public static final ConcurrentMap<String , AgvArrivedErackInfoMessage> LEAVE_NOTIFY = Maps.newConcurrentMap();
	
	/**
	 * 工作站与掉头点缓存
	 */
	public static final Map<String, String> UTURN_CACHE = Maps.newHashMap();

	/**
	 * 车辆导航点最近的工作总站
	 */
	public static final Map<String, String> MARKCODE_NEAREST_STATION = Maps.newHashMap();
	
	
	/**
	 * 重发作业计划
	 */
	public static final Map<String, WorkScheduler> RESEND = Maps.newHashMap();

	/**
	 * 台积帮助类
	 */
	public static TjdHelper tjdHelper;

	/**
	 * 任务
	 */
	public static MissionWorkService missionWorkService;
	/**
	 * 车辆速度
	 */
	private static CheckAndSendPathService checkAndSendPathService;
	private static SidePathResourcePool sidePathResourcePool;

	/**
	 * 容灾开发 相关 start
	 */

	/**
	 * 停止充电调度的key
	 */
	public static final String STOP_SCHEDULER_KEY = "stop_scheduler_key";
	/**
	 * 暂停log 打印
	 */
	public static final String STOP_SCHEDULER_LOG = "stop_scheduler_log";
	
	/**
	 * 充电桩data
	 */
	public final static String CHARGE_URL ="http://10.101.125.94:8088/api/server/charge/data";
	/**
	 * 关闭充电桩的data
	 */
	public final static String CHARGE_STOP_URL ="http://10.101.125.94:8088/api/server/charge/stop";

	/**
	 * 从暂停中恢复运行
	 */
	public static volatile boolean STOP_SCHEDULER_RESUME = false;
	
	
	/**
	 * 存储: 离站的agvCode 及 对应的工作站id
	 * 用处: 离站记录, 进站清空
	 */
	public static volatile Map<String,String>  LEAVING_STATION = Maps.newConcurrentMap();
	/**
	 * 是否有agv 着火
	 */
	public static final  String IS_AGV_FIRING_KEY = "IS_AGV_FIRING";
	public static  boolean IS_AGV_FIRING =  false ;

	/**
	 * 用以监视 暂停恢复
	 */
	public static RemovalListener<String, Boolean> STOP_SCHEDULER_lISTENER = new RemovalListener<String, Boolean>() {
		public void onRemoval(RemovalNotification<String, Boolean> notification) {
			// notification.getKey(); // 当前删除对象的 key
			// notification.getValue(); // 当前删除对象的 value
//			log.debug("key:{}, value:{}", notification.getKey(), notification.getValue());
//			log.debug("key:{}, value:{}", notification.getKey(), notification.getValue());
//			log.debug("key:{}, value:{}", notification.getKey(), notification.getValue());
			STOP_SCHEDULER_RESUME = true;
		}
	};
	
	/*********************************
	 * some agv firing   other run away start 
	 */
	/**
	 * 用以监视 agv车辆着火，之后恢复
	 */
	public static RemovalListener<String, Boolean> AGV_FIRING_lISTENER = new RemovalListener<String, Boolean>() {
		public void onRemoval(RemovalNotification<String, Boolean> notification) {
			// notification.getKey(); // 当前删除对象的 key
			// notification.getValue(); // 当前删除对象的 value

			IS_AGV_FIRING = false ;
			TjdCxt.FIRE_RETAKE_AGVS.clear();
		}
	};
	
	/**
	 * 单个车辆着火，其它车辆退出
	 */
	public static final Cache<String, Boolean> FIRE_RETAKE = CacheBuilder.newBuilder().expireAfterWrite( 30, TimeUnit.MINUTES)
			.removalListener( AGV_FIRING_lISTENER ).maximumSize(1).build();
	/**
	 * 当前正在着火的agv 
	 */
	public static final Set<String> FIRE_RETAKE_AGVS = Sets.newConcurrentHashSet(); 
	/***********************************************************************
	 * some agv firing   other run away end 
	 */

	/**
	 * 在一定时间内暂时停止重新上下线调度 start
	 */
	public static  Cache<String, Integer> REGROUP = CacheBuilder.newBuilder().expireAfterWrite( 60, TimeUnit.SECONDS)
			.build() ;
	
	public static  final String REGROUP_KEY= "REGROUP_KEY" ;
	
	/**
	 * 在一定时间内暂时停止重新上下线调度 end
	 */
	
	/**
	 * 清理失效的记录
	 */
	public static final void cleanUp() {

		if (Objects.nonNull(STOP_SCHEDULER)) {

			STOP_SCHEDULER.cleanUp();
		}
		
		if(Objects.nonNull( TjdCxt.FIRE_RETAKE) ) {
			
			TjdCxt.FIRE_RETAKE.cleanUp(); 
		}
		
		if(Objects.nonNull( TjdCxt.REGROUP) ) {
					
				TjdCxt.REGROUP.cleanUp(); 
			}
	}

	/**
	 * 停止所有的任务调度
	 */
	public static Cache<String, Boolean> STOP_SCHEDULER = setStopScheduler( 40 );
	
	/**
	 * 热备处理中
	 */
	public static AtomicBoolean HANDLING = new AtomicBoolean( false );

	/**
	 * 工作站单个agv数量限制
	 */
	public final static  Integer STATION_COUNT_LIMIT = 4 ;
	/**
	 * 工作站agv
	 */
  public final static 	MultiValuedMap<String, String> STATION_COUNT = new ArrayListValuedHashMap<String, String>();

  public  final  static  String ACTION_TYPE_AUDIO_PLAYBACK ="AUDIO_PLAYBACK";
	/**
	 * 下发进工作站任务时进入
	 * @param station
	 * @param agv
	 */
  public  static  synchronized  void putStationCount(String station ,String agv){

	  CompletableFuture.runAsync( () ->{
		  removeStationCountByAgv( agv);
		  STATION_COUNT.put(station, agv) ;
		  checkAndBrocat( station);
	  });

  }

	private static void checkAndBrocat(String station) {

		Collection<String> agvs = STATION_COUNT.get(station);
		List<String> list = Lists.newArrayList( agvs);
		SchedulerConfigService configService = ApplicationUtils.getBean(SchedulerConfigService.class);
		DefaultVehiclePool defaultVehiclePool = ApplicationUtils.getBean(DefaultVehiclePool.class);

		SchedulerConfig schedulerConfig = configService.selectSchedulerConfig();
		WorkCycleConfig config = getCycle().get(station);
		if(Objects.nonNull(config) &&Objects.nonNull(config.getNumberVehicles()) && config.getNumberVehicles()>0 && list.size() - config.getNumberVehicles()> 0){
			List<String> toBrocatAgvs = list.subList(0, list.size() - config.getNumberVehicles());
			if(CollectionUtils.isNotEmpty( toBrocatAgvs)){
				for (String agvCode :
						toBrocatAgvs) {
					Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
					if(Objects.nonNull( vehicle)){
						if( StringUtils.isBlank( vehicle.getAgvGroupId())){
							vehicle.setPlayStationMusic( true);
							log.debug("agvCode:{}数量超过工作站限制:{},播报语音:{},暂存结果" , agvCode, config.getNumberVehicles(), schedulerConfig.getVoicePlayback());
						}


					}

				}

			}


		}
	}

	public  static  synchronized  void removeStationCountByStation(String station ){

		STATION_COUNT.remove( station);
	}
	public  static  synchronized  void removeStationCountByAgv(String agvId ){

		boolean b = STATION_COUNT.containsValue(agvId);
		if(b){
			List<Map.Entry<String , String>> entries = Lists.newArrayList();
			for (Map.Entry<String ,String> entry:
					STATION_COUNT.entries()) {
				if( entry.getValue().equals( agvId)){
					entries.add( entry);
				}
			}
			entries.forEach(i ->{
				STATION_COUNT.removeMapping( i.getKey(), i.getValue()) ;
			});

		}

	}
	private static Cache<String, Boolean> setStopScheduler(int expireTime) {
		return CacheBuilder.newBuilder().expireAfterWrite(expireTime, TimeUnit.SECONDS )
				.removalListener(STOP_SCHEDULER_lISTENER).maximumSize( 2 ).build();
	}

	/**
	 * 重新设置暂停计时器
	 * 
	 * @param expireTime
	 */
	public static void resetStopScheduler(int expireTime) {
		Cache<String, Boolean> stopScheduler = setStopScheduler(expireTime);
		STOP_SCHEDULER = stopScheduler;

	}

	/**
	 * 停止所有的任务调度
	 */
	public final synchronized static boolean pauseAllScheduler() {
		if (!isPause()) {
			STOP_SCHEDULER.put(STOP_SCHEDULER_KEY, true);
			return true;
		}

		return false;
	}

	/**
	 * 判断任务分配是否处于暂停中
	 * 
	 * @return
	 */
	public final synchronized static boolean isPause() {

		Boolean pause = STOP_SCHEDULER.getIfPresent(STOP_SCHEDULER_KEY);

		boolean isPause = BooleanUtils.toBoolean(pause);
		return isPause || HANDLING.get() ;
	}

	/**
	 * 容灾开发 相关 end
	 */

	/**
	 * @ClassName: EventId
	 * @Description: 台积电事件id 相关配置项
	 * <AUTHOR>
	 * @date 2022年1月5日 下午1:43:28
	 * @see ReportType s 6, f 11
	 * @see Alert s 6, f 13
	 */
	public static final class EventId implements Serializable {

		/**
		 * @Fields serialVersionUID : TODO（用一句话描述这个变量表示什么）
		 */

		private static final long serialVersionUID = 1L;

		/**
		 * @Fields AGV_STATION_ARRIVED_ALIAS : agv 预到达工作站报表别名）
		 */
		public final static String AGV_STATION_PRE_ARRIVED_ALIAS = "AGV_PRE_REACH_STATION";

		/**
		 * @Fields AGV_STATION_ARRIVED_EVENT_ID : agv预到达工作站报表Id
		 */
		public final static String AGV_STATION_PRE_ARRIVED_EVENT_ID = "5000";
		/**
		 * @Fields AGV_STATION_ARRIVED_ALIAS : agv 到达工作站报表别名）
		 */
		public final static String AGV_REACH_STATION_ALIAS = "AGV_REACH_STATION";

		/**
		 * @Fields AGV_STATION_ARRIVED_EVENT_ID : agv到达工作站报表Id
		 */
		public final static String AGV_REACH_STATION_EVENT_ID = "5001";

		/**
		 * @Fields AGV_STATION_LEAVE_ALIAS : agv 离开站点别名
		 */
		public final static String AGV_STATION_LEAVE_ALIAS = "AGV_STATION_DEPARTURE";
		
		/**
		 * 真实离站，速度大于0
		 */
		public final static String AGV_STATION_LEAVE_ALIAS2 = "AGV_STATION_DEPARTURE2";

		/**
		 * @Fields AGV_STATION_LEAVED_EVENT_ID :agv 离开站点事件id
		 */
		public final static String AGV_STATION_LEAVED_EVENT_ID = "5002";
		
		/**
		 * 真实离站，速度大于0
		 */
		public final static String AGV_STATION_LEAVED_EVENT_ID2 = "5003";

		/**
		 * @Fields AGV_BIN_CASSETTE_IN : 物料移入
		 */
		public final static String AGV_BIN_CASSETTE_IN_ALIAS = "CassetteIn";

		public final static String AGV_BIN_CASSETTE_IN_EVENT_ID = "6001";

		/**
		 * @Fields AGV_BIN_CASSETTE_OUT : 物料移除
		 */
		public final static String AGV_BIN_CASSETTE_OUT_ALIAS = "CassetteOut";

		/**
		 * @Fields AGV_BIN_CASSETTE_OUT_EVENT_ID : 物料移除id
		 */
		public final static String AGV_BIN_CASSETTE_OUT_EVENT_ID = "6002";

		/**
		 * @Fields AGV_TASK_ERROR : 任务执行失败 对应的alert Id
		 */
		public final static Integer AGV_TASK_ERROR = 1001;

		/**
		 * @Fields ABNORMAL_STOP_ALIAS : agv异常停止
		 */
		public static final String ABNORMAL_STOP_ALIAS = "abnormal stop";

		/**
		 * @Fields ABNORMAL_STOP_EVENT_ID : agv异常停止
		 */
		public static final String ABNORMAL_STOP_EVENT_ID = "7000";

		/**
		 * @Fields CONTINUE_RUN_ALIAS : 从异常恢复运行
		 */
		public static final String CONTINUE_RUN_ALIAS = "continueRun";

		/**
		 * @Fields CONTINUE_RUN_EVENT_ID : 从异常恢复运行
		 */
		public static final String CONTINUE_RUN_EVENT_ID = "7001";

		public static final String ONlINE_READY_ALIAS = "agv online ready";

		public static final String ONlINE_READY_EVENT_ID = "0000";

		/**
		 * agv网络联线成功
		 */
		public static final String NET_ONlINE_READY_ALIAS = "agv online ready(network)";

		public static final String NET_ONlINE_READY_EVENT_ID = "1111";

		public static final String OFFlINE_DIE_ALIAS = "agv offline ready(network)";

		public static final String OFFlINE_DIE_EVENT_ID = "9999";
		/**
		 * agv 被人为下线
		 */
		public static final String BIZ_OFFlINE_DIE_ALIAS = "agv mannal offline to charge or park";

		public static final String BIZ_OFFlINE_DIE_EVENT_ID = "8888";
		/**
		 * AGV 异常消除
		 */
		public static final String CLEAR_ALL_ERROR_ALIAS = "agv recocery from abnormal";

		public static final String CLEAR_ALL_ERROR_EVENT_ID = "5555";
		/**
		 * @Fields OFFlINE_ALIAS : agv 离线前往充电或者泊车
		 */
		public static final String CHARGEING_ALIAS = "agv offline for charge or park";

		/**
		 * @Fields OFFlINE_EVENT_ID : : agv 离线前往充电或者泊车
		 */
		public static final String CHARGEING_EVENT_ID = "8000";
		
		public static final int CHARGEING_EVENT_PHRASE_PREFIX = 8030 ;
		/**
		 * @Fields OFFlINE_ALIAS : agv 离线前往充电或者泊车
		 */
		public static final String CHARGEING_READY_ALIAS = "charging ready";

		/**
		 * @Fields OFFlINE_EVENT_ID : : agv 离线前往充电或者泊车
		 */
		public static final String CHARGEING_READY_EVENT_ID = "8000";
		/**
		 * 充电清洗
		 */
		public static final String CHARGEING_WASH_ALIAS = "charging wash";
		/**
		 * 充电清洗
		 */
		public static final String CHARGEING_WASH_EVENT_ID = "8001";
		/**
		 * 手环放行别名
		 */
		public static final String BRACELET_ALIAS = "te bracelet release";
		/**
		 * 手环放行事件id
		 */
		public static final String BRACELET_EVENT_ID = "8002";
		/**
		 * agv 的电量
		 */
		public static final String BATTERY_ALIAS = "battery per 10 minutes";

		/**
		 * @Fields agv 电量编码 : : agv 离线前往充电或者泊车
		 */
		public static final String BATTERY_EVENT_ID = "8005";
		/**
		 * agv 异常停止检测
		 */
		public static final String ABNORMAL_STOP_CHECK_ALIAS = "abnormal stop 15 seconds";

		/**
		 * @Fields agv 异常停止检测
		 */
		public static final String ABNORMAL_STOP_CHECK_EVENT_ID = "8006";

		/**
		 * agv 的电量
		 */
		public static final String INNER_SENSOR_ALIAS = "inner sensor data per 10 minutes";

		/**
		 * @Fields agv 电量编码 : : agv 离线前往充电或者泊车
		 */
		public static final String INNER_SENSOR_EVENT_ID = "8007";
		/**
		 * agv 的电量
		 */
		public static final String CHARGER_STACK_ALIAS = "Charger Data per 10 minutes";
		
		/**
		 * @Fields agv 电量编码 : : agv 离线前往充电或者泊车
		 */
		public static final String CHARGER_STACK_EVENT_ID = "100010";

		/**
		 * it指令类型
		 */
		public  static enum  CMD{

			TRANSFER,

			RELEASE,

			ALT ,

			RELEASE_ALT ,

			PRIMARY,

			RELEASE_PRIMARY,
			;

			public  static  CMD getCmd( String cmd){
				for (CMD type :
						CMD.values()) {
					if(StringUtils.equalsIgnoreCase( type.name(), cmd)){
						return  type ;
					}
				}
				return null;
			}

		}
		/**
		 * @ClassName: Alert
		 * @Description: 报警信息上报
		 * <AUTHOR>
		 * @date 2022年1月20日 下午5:45:30
		 *
		 */
		@Getter
		@AllArgsConstructor
		public enum Alert {
			/**
			 * 到达站点仍然未接收到mes 的上下料指令
			 */
			NO_UPLOAD_UNlOAD_CMD_RECEIVED("12300", "1", "no s2f49 received when reached station"),

			/**
			 * 与电子货架通讯异常
			 */
			SHELF_COMM_ERROR("12302", "1", "communicate with erack error"),
			/**
			 * 任务执行异常
			 */
			TASK_RUN_ERROR("12602", "1", "mission run error"),

			/**
			 * 与组态屏通讯异常
			 */
			SCREEN_COMM_ERROR("12301", "1", "communicate with screen error"),

			/**
			 * 发生火灾
			 */
			FIRE_HAPPENED("13000", "1", "fire happened ");

			String alertId;

			String alertStatus;

			String desc;

			/**
			 * @Title: msg
			 * @Description: 报警转化为secs 消息
			 * @param @return 设定文件
			 * @return Secs2List 返回类型
			 * @throws
			 */
			/**
			 * @Title: msg
			 * @Description: 报警转化为secs 消息
			 * @param @return 设定文件
			 * @return Secs2List 返回类型
			 * @throws
			 */
			public Secs2List msg(Vehicle vehicle, String solution) {

				return Secs2.list(

						/**
						 * 报警id
						 */
						Secs2.ascii(this.getAlertId()),
						/**
						 * 设置报警
						 */
						Secs2.ascii(this.getAlertStatus()),

						Secs2.ascii(vehicle.getId()),

						Secs2.ascii(getPosition(vehicle)),
						/**
						 * 报警描述信息
						 */
						Secs2.ascii(this.getDesc()),
						/**
						 * 
						 */
						Secs2.ascii(StringUtils.isNotBlank(solution) ? solution : "pls check agv"),

						/**
						 * 当前时间
						 */
						Secs2.uint8(System.currentTimeMillis())

				);
			}

			/**
			 * 根据消息上报异常
			 * 
			 * @param notification
			 * @return
			 */
			public static Secs2List custom(Notification notification, AbnormalPrompt abnormalPrompt, String position) {
				return Secs2.list(

						/**
						 * 报警id
						 */
						Secs2.ascii("" + notification.getErrorCode()),
						/**
						 * 设置报警: 1 表示发生
						 */
						Secs2.ascii("1"),

						Secs2.ascii("" + notification.getAgvCode()),
						/**
						 * 车辆位置
						 */
						Secs2.ascii(position),
						/**
						 * 报警描述信息
						 */
						Secs2.ascii(Objects.toString(abnormalPrompt.getEnDesc())),

						Secs2.ascii(Objects.toString(abnormalPrompt.getEnSlolution())),
						/**
						 * 异常类型
						 */
						Secs2.ascii(Objects.toString(abnormalPrompt.getAbnormalLevel())),
						/**
						 * 时间戳
						 */
						Secs2.uint8(System.currentTimeMillis())

				);

			};
		}

	};

	/**
	 * @Title: getCycle
	 * @Description: 台积电环状线
	 * @return ListOrderedMap<String,WorkCycleConfig> 返回类型
	 * @throws
	 */

	public static final ListOrderedMap<String, WorkCycleConfig> getCycle() {

		return CYCLE_MAP;
	}

	/**
	 * @Title: getStationMakCodeCycle
	 * @Description: 获得工作站和导航点的对应关系
	 * @param @return 设定文件
	 * @return DualHashBidiMap<String,String> 返回类型
	 * @throws
	 */

	public static final DualHashBidiMap<String, String> getStationMakCodeCycle() {

		return STATION_MARK_CODE_MAP;
	}

	/**
	 * @Title: addPointtToCycle
	 * @Description: 向环状线添加工作站
	 * @param @param  key
	 * @param @param  config
	 * @param @return 设定文件
	 * @return ListOrderedMap<String,WorkCycleConfig> 返回类型
	 * @throws
	 */

	public synchronized static final ListOrderedMap<String, WorkCycleConfig> addPointtToCycle(String key,
			WorkCycleConfig config) {
		CYCLE_MAP.put(key, config);
		STATION_MARK_CODE_MAP.put(key, config.getMarkCode());
		return CYCLE_MAP;
	}

	/**
	 * @Title: deletePointtToCycle
	 * @Description: 从点位中删除数据
	 * @param @param  key 工作站的 id
	 * @param @return 设定文件
	 * @return ListOrderedMap<String,WorkCycleConfig> 返回类型
	 * @throws
	 */
	public synchronized static final ListOrderedMap<String, WorkCycleConfig> deletePointtToCycle(String key) {
		CYCLE_MAP.remove(key);
		STATION_MARK_CODE_MAP.remove(key);
		return CYCLE_MAP;
	}

	/**
	 * @Title: getNextMarkCode
	 * @Description: 通过站点key 获得对应的markCode
	 * @param @param  key
	 * @param @return 设定文件
	 * @return String 返回类型
	 * @throws
	 */

	public synchronized static final String getNextMarkCode(String key) {

		String markCode = STATION_MARK_CODE_MAP.get(key);

		return markCode;
	}

	/**
	 * @Title: getStation
	 * @Description: 通过导航点获取到工作站
	 * @param @param  markCodeq
	 * @param @return 设定文件
	 * @return String 返回类型
	 * @throws
	 */
	public synchronized static final String getStation(String markCode) {

		String key = STATION_MARK_CODE_MAP.getKey(markCode);

		return key;
	}

	/**
	 * 获得执行任务所需的变量
	 * 
	 * @Title: getMissionWorkParam
	 * @Description: 获得执行任务所需的变量
	 * @param @param  p
	 * @param @param  mission
	 * @param @return 设定文件
	 * @return MissionWorkParam 返回类型
	 * @throws
	 */
	public final static Triple<Boolean, MissionWorkParam , WorkCycleConfig> getMissionWorkParam(Vehicle vehicle, Mission mission) {
		Map<String, Object> data = Maps.newHashMap();

		WorkCycleConfig cycleConfig = getNextMarkCodeByAgvCode(vehicle.getId());
		defaultParam(vehicle, data, cycleConfig);

		/**
		 * 构建创建任务所需的变量
		 */
		MissionWorkParam missionWorkParam = new MissionWorkParam();
		missionWorkParam.setAgvCode(vehicle.getId());
		missionWorkParam.setMissionId(mission.getId());
		missionWorkParam.setRuntimeParam(JSON.toJSONString(data));
		return Triple.of(BooleanUtils.toBoolean(  StringUtils.isNotBlank(cycleConfig.getMarkCode())), missionWorkParam , cycleConfig);
	}

	public static void defaultParam(Vehicle vehicle, Map<String, Object> data, WorkCycleConfig cycleConfig ) {
		data.put(TjdCxt.AGV_MAPID, cycleConfig.getAgvMapId());
		data.put(TjdCxt.MARKER_CODE, cycleConfig.getMarkCode());
		data.put(TjdCxt.STATION, Objects.toString( vehicle.getCurrentStation()));
		data.put(TjdCxt.ON_KEY_U_TURN, vehicle.isUturn());
		data.put(TjdCxt.LINEPATROL, vehicle.getLinePatrolMode());
		data.put(TjdCxt.RELEASE_MODE, cycleConfig.getReleaseMode());
		data.put(TjdCxt.SEQ, cycleConfig.getSeq());
		vehicle.setUturn(false);
		boolean unloadOnly = BooleanUtils.toBoolean(vehicle.getUnLoadOnly());
		if (!vehicle.getLinePatrolMode()) {
			unloadOnly = true;
		}
		data.put(TjdCxt.UNLOADONLY, unloadOnly);
	}

	/**
	 * @param @param  vehicle
	 * @param @return 设定文件
	 * @return String 返回类型
	 * @throws
	 * @Title: getNextMarkCode
	 * @Description: 获得下一个导航点的位置
	 */
	public final static WorkCycleConfig getNextMarkCodeByAgvCode(String agvCode) {
		WorkCycleConfig cycleConfig = null;
		VehiclePool vehiclePool = ApplicationUtils.getBean(VehiclePool.class);
		Vehicle vehicle = vehiclePool.getVehicle(agvCode);
		/**
		 * 下一个工作站
		 */
		String nextStation = vehicle.nextStation();
		try {
			
			if (StringUtils.isBlank(nextStation)) {
				log.error("pls_config_station_list");
				return cycleConfig;
			}
			/**
			 * 超车修改站点
			 */
			// nextStation = overTake(vehicle, nextStation);
			ListOrderedMap<String, WorkCycleConfig> cycle = vehicle.getCycle();
			if (CollectionUtils.size(cycle) == 1) {
				 cycleConfig = cycle.getValue(0);
//				markCode = cycleConfig.getMarkCode();
				vehicle.setPreStation(vehicle.getCurrentStation());
				vehicle.setCurrentStation(cycleConfig.getId());
				return cycleConfig;
			} else {
				cycleConfig = cycle.get(nextStation);


				vehicle.setPreStation(vehicle.getCurrentStation());
				vehicle.setCurrentStation(nextStation);
				return cycleConfig;
			}
		} catch (Exception e) {
			e.printStackTrace();
			vehicle.setPreStation(vehicle.getCurrentStation());
			vehicle.setCurrentStation(nextStation);
			return cycleConfig;
		}

	}

	

	/** 工作站进离站的配置
	 * @param v
	 * @return
	 */
	public static  boolean stationConfig(Vehicle v) {
		boolean containsKey = TjdCxt.LEAVING_STATION.containsKey(v.getId());
		String preStation = v.getCurrentStation();
		if(containsKey || StringUtils.isNotBlank( preStation)) {
			WorkCycleConfig config = TjdCxt.CYCLE_MAP.get( TjdCxt.LEAVING_STATION.get ( v.getId()) ) ;
			if(Objects.isNull(config)) {
				config = TjdCxt.CYCLE_MAP.get( preStation) ;
			}
			 // 离站伪巡线 ,离线本就离线就不管了
			if(config.getEnterWorkMode() == 1) {
				v.setDummy( true );
				return true ;
			}
		}
		
		return false ;
	}
	/**
	 * haveMaerial( 车辆实体)
	 * 
	 * @Title: haveMaerial
	 * @Description: 判断机器人上有无物料
	 * @param @param  vehicle
	 * @param @return 设定文件
	 * @return boolean 返回类型
	 * @throws
	 */
	public final static boolean haveMaterial(Vehicle vehicle) {

		try {
			/**
			 * 离站伪巡线
			 */
			if(  TjdCxt.stationConfig(vehicle ) ) {
				return true ;
			}
			Collection<TjdScreenItemDto> currentShelfData = vehicle.getShelfData().values();
			/**
			 * 所有的工作站
			 */
			Collection<String> lotIds = vehicle.lotIds();
			if (CollectionUtils.isNotEmpty(currentShelfData) && CollectionUtils.isNotEmpty(lotIds)) {

				Set<String> lotIdStations = currentShelfData.parallelStream()
						.filter(p -> StringUtils.isNotBlank(p.getToStation())||StringUtils.isNotBlank(p.getLotId())).map(TjdScreenItemDto::getToStation)
						.collect(Collectors.toSet());
				vehicle.setCycleMaterialPoints(lotIdStations);
				return CollectionUtils.isNotEmpty(lotIdStations);
			}
		} catch (Exception e) {
			log.error("error:{}", Throwables.getStackTraceAsString(e));
		}

		return false;
	}

	/**
	 * @Title: haveMaterialNextStation
	 * @Description: 判断车辆再某个站点是否有料
	 * @param @param  vehicle
	 * @param @param  stationId
	 * @param @return 设定文件
	 * @return boolean 返回类型
	 * @throws
	 */
	public final static boolean haveMaterialNextStation(Vehicle vehicle, String stationId) {

		return haveMaterial(vehicle) && CollectionUtils.isNotEmpty(vehicle.getCycleMaterialPoints())
				&& vehicle.getCycleMaterialPoints().contains(stationId);
	}

	/**
	 * 获得下一个工作站:即当前需应前往的工作站
	 * 
	 * @param vehicle
	 * @return
	 */
	public final static String getNextStation(Vehicle vehicle) {
		
		/**
		 * 设置小车要循环的环
		 */
		setVehicleCycle(vehicle);
		ListOrderedMap<String, WorkCycleConfig> cycle = vehicle.getCycle();
		
		/**
		 * 离站伪巡线
		 */
		String dummpyStation = TjdCxt.LEAVING_STATION.get( vehicle.getId());
		boolean stationConfig = TjdCxt.stationConfig(vehicle );
		if(  stationConfig ) {
			if(StringUtils.isBlank(dummpyStation)) {
				dummpyStation = vehicle.getCurrentStation();
			}

			String nextKey = 	getNextKey(cycle, dummpyStation);;
			log.debug("dumpy_agv:{},dumpyStation:{},nextStation:{}",vehicle.getId(), dummpyStation, nextKey);
			
			return nextKey;
		}
		/**
		 * 是否 一键掉头 获得一种新的车型
		 */
//		boolean uTurn = StringUtils.isBlank(vehicle.getAgvType());
		String currentStation = vehicle.getCurrentStation();
//		String lastMarkerId = vehicle.getLastMarkerId();
//		if (uTurn ) {
//			String uturnStation = getUturnStation(vehicle, lastMarkerId);
//			if (StringUtils.isBlank(uturnStation)) {
//				log.error("agvCode:[{}],event:[调头无法找到掉头点], content:{}", vehicle.getId(), vehicle.getAgvType());
//				vehicle.setAgvType(TjdCxt.MISSION_WORK_NAME_M26);
//			}
//			vehicle.setPreStation( null );
//			vehicle.setCurrentStation( uturnStation );
//			return uturnStation;
//
//		}

		boolean offline = !BooleanUtils.toBoolean(vehicle.getLinePatrolMode());
		if (StringUtils.isBlank(currentStation) || offline) {
			try {
			
				/**
					 * 沿着环找下一个
					 */
					if( offline) {
						
						String nextKey = getMaterialNext(vehicle, currentStation);
						if(StringUtils.isBlank(nextKey)) {
							return getNearStation(vehicle, cycle);
						}
						return nextKey ;
					}
					/**
					 * 找最近的一个
					 */
					return getNearStation(vehicle, cycle);
				
			
			} catch (Exception e) {
				log.error("agv_code:{} ,error:{}", vehicle.getId(), Throwables.getStackTraceAsString(e));
				notifyException(vehicle, ErrorEnum.ACTION_AGV_IS_OFF_TRACK);
			}
		}

		String nextKey = getNextKey(vehicle.getCycle(), currentStation);
		log.debug("currentKey:{}, nextKey:{}", currentStation, nextKey);
		/**
		 * 同组所有车辆全部都是联动, 但是前站已经有车,不执行放行,仍旧原地等待,直接返回当前站
		 */
		boolean overLoad = isOverLoad(vehicle, nextKey);
		if(overLoad &&  BooleanUtils.isTrue( vehicle.getLinePatrolMode())){
			log.debug("agvCode:{},overLoad:{},currentStation:{},nextKey:{},linePatrolMode:{}", vehicle.getId(), true,	currentStation,nextKey, true);
			return  currentStation;
		}
		return nextKey;
	}

	/** 获取下一个物料点
	 * @param vehicle
	 * @param currentStation
	 * @return
	 * @throws Exception
	 */
	public static String getMaterialNext(Vehicle vehicle, String currentStation) throws Exception {
		
		ListOrderedMap<String, WorkCycleConfig> cycle = vehicle.getCycle();
//		String temp = currentStation  ;
		 boolean haveMaerial = TjdCxt.haveMaterial(vehicle);
		 log.debug("agvCode:{},haveMaerial:{}", vehicle.getId(), haveMaerial);
		 Set<String> cycleMaterialPoints = vehicle.getCycleMaterialPoints();
		 if(CollectionUtils.isEmpty(cycleMaterialPoints) || !haveMaerial) {
			 log.debug("agvCode:{},haveMaerial:{},__dumy:{}", vehicle.getId(), haveMaerial, vehicle.getDummy());
			 return getNextKey(cycle, currentStation) ;
		 }
		 
//		while (Objects.nonNull(  getNextKey( cycle, currentStation))  ) {
//			currentStation = getNextKey(cycle, currentStation);
//		
//			if( cycleMaterialPoints.contains( currentStation )  ) {
//				return currentStation ;
//			}
//			if(StringUtils.equals(temp, currentStation)) {
//				break ;
//			}
//		}
	
	
		
		return getNearStation(vehicle, cycle);
	}



	/**
	 * 获得掉头要去的工作站
	 * 
	 * @param vehicle
	 * @param lastMarkerId
	 * @return
	 */
	private static String getUturnStation(Vehicle vehicle, String lastMarkerId) {
		String agvLoc = getAgvLoc(vehicle);
		Marker marker = MapGraphUtil.getMarkerByMarkerId(agvLoc);
		
		ListOrderedMap<String, WorkCycleConfig> cycle = vehicle.getCycle();
		if (Objects.nonNull(marker)) {
			try {
				DualHashBidiMap<String, String> convertBidiMap = convertBidiMap( cycle) ;

				String turnStationMarkCode = TjdCxt.getUTurnStationMarkCode(vehicle.getAgvMapId(), marker.getCode() , vehicle.getAgvGroupId() );
				
				String uTurnStation = convertBidiMap.inverseBidiMap().get(turnStationMarkCode);
				vehicle.setUturn(true);
				log.debug("agvCode:{},uturn_successstation:{},机器人掉头：{}", vehicle.getId(), uTurnStation,
						turnStationMarkCode);
				return uTurnStation;
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				notifyException(vehicle, ErrorEnum.ACTION_AGV_IS_OFF_TRACK);
			}
			return null;
		} else {
			log.error("agvCode:{},uturn_failed,机器人脱轨", vehicle.getId());
			notifyException(vehicle, ErrorEnum.ACTION_AGV_IS_OFF_TRACK);
			return null;
		}
	}

	/**转化双向表
	 * @param cycle
	 * @return
	 */
	private static DualHashBidiMap<String, String> convertBidiMap(ListOrderedMap<String, WorkCycleConfig> cycle) {
		 DualHashBidiMap<String, String> map = new  DualHashBidiMap<String, String>();
		 cycle.forEach((key, value) -> map.put(key, value.getMarkCode()));
		return map;
		
	}

	public static String getAgvLoc(Vehicle vehicle) {
		String agvLoc = getGetBestWayWithMarkIdController().getAgvLocationByAgvCode(vehicle.getId());
		return agvLoc;
	}

	private static String getNearStation(Vehicle vehicle, ListOrderedMap<String, WorkCycleConfig> cycle)
			throws Exception {
		GetBestWayWithMarkIdController bestWayWithMarkIdController = ApplicationUtils
				.getBean(GetBestWayWithMarkIdController.class);
		MarkerService markerService = ApplicationUtils.getBean(MarkerService.class);

		Set<String> markCodes = cycle.valueList().parallelStream().filter( p ->{
					if (isOverLoad(vehicle, p.getId())) return false;

					return  p.getEnabled() ;
					})
					.map(WorkCycleConfig::getMarkCode).collect(Collectors.toSet());
			/**
			 * codes: agvCode -> agvId 对应关系
			 */
			/**
			 * 离线
			 */

//			vehicle.setDummy(null);
			HashBiMap<String, String> codesBidi = markerService.selectMarkerByCodes(vehicle.getAgvMapId(), markCodes);

			TMSMarkerIdSequence tmsMarkerIdSequence = new TMSMarkerIdSequence();
			String agvCode = vehicle.getId();
			tmsMarkerIdSequence.setAgvCode(agvCode);
			tmsMarkerIdSequence.setMarkerIds(Lists.newArrayList(codesBidi.values()));

			Map<String, Integer> markerIdsSequence = Maps.newConcurrentMap();
			try {
				markerIdsSequence = bestWayWithMarkIdController.getNearCodes2(tmsMarkerIdSequence);
				vehicle.setOutline(false);
				cache.invalidate(agvCode);
			} catch (Exception e) {
				notifyException(vehicle, ErrorEnum.ACTION_AGV_IS_OFF_TRACK);
				throw e;

			}
			HashBiMap<String, Integer> markerIdsBidi = HashBiMap.create(markerIdsSequence);
			Integer mini = markerIdsBidi.values().parallelStream().reduce(Integer::min).get();
			String firstMarkId = markerIdsBidi.inverse().get(mini);
			Marker marker = markerService.selectById(firstMarkId);
			WorkCycleConfig firstStation = cycle.valueList().parallelStream()
					.filter(p -> StringUtils.equals(marker.getCode(), p.getMarkCode())).findFirst().get();

			return firstStation.getId();


	}

	/**
	 * 车辆是否找出工作站的用量限制
	 * @param vehicle
	 * @param p
	 * @return
	 */
	public static boolean isOverLoad(Vehicle vehicle, String stationId) {
		boolean allLinkAge = isAllLinkAge( vehicle);
		/**
		 * 全部是联动模式,且工作站已经有车辆在进行中
		 */
		WorkCycleConfig workCycleConfig = vehicle.getCycle().get(stationId);
		if(allLinkAge && Integer.valueOf(1).equals(workCycleConfig.getNumberVehicles())){
			Collection<String> already = STATION_COUNT.get(workCycleConfig.getId());
			if(CollectionUtils.isNotEmpty(already)) {
				already = Lists.newArrayList( already) ;
				already.remove(vehicle.getId());
				if (CollectionUtils.isNotEmpty(already)) {
					// 刚上线的车辆 联动放行最近的站
				   boolean linkSuccess =	handlinkCurrentStation(vehicle, workCycleConfig.getId());
					log.debug("agvCode:{},already:{},skip:{},linkSuccess:{}", vehicle.getId(), JSON.toJSONString(already), stationId, linkSuccess);
					return  !linkSuccess;
				}
			}
		}
		return false;
	}

	private static boolean isAllLinkAge(Vehicle vehicle ) {
		Set<Integer> releaseModes = vehicle.getCycle().valueList().parallelStream().filter(WorkCycleConfig::getEnabled)
					.map(WorkCycleConfig::getReleaseMode).collect(Collectors.toSet());
		boolean allLinkAge = releaseModes.parallelStream().allMatch(ReleaseMode.LINKAGE::isThis);
		return allLinkAge;
	}

	/**
	 * 1 分钟每次的频率推送报错信息
	 * 
	 * @param vehicle
	 * @param error
	 */
	public static void notifyException(Vehicle vehicle, ErrorEnum error) {
		NotificationService notificationService = ApplicationUtils.getBean(NotificationService.class);
		String agvCode = vehicle.getId();
		String key = agvCode + error.name();
		if (Objects.isNull(cache.getIfPresent(key))) {
			vehicle.setOutline(true);
			cache.put(key, true);
			Notification notification = notificationService.sendMessage(error.code(), agvCode);
			NotifyMessageEvent.publishAlert(notification);
		}
	}

	/**
	 * 设置小车环状路线: 分两种 巡线模式就是全局的环, 非巡线模式就是物料需要放下的环
	 * 
	 * @Title: setVehicleCycle
	 * @Description: TODO
	 * @param @param vehicle 设定文件
	 * @return void 返回类型
	 * @throws
	 */
	private static void setVehicleCycle(Vehicle vehicle) {
		/**
		 * 巡线及伪巡线
		 */
		if ((vehicle.getLinePatrolMode() || vehicle.getDummy())) {
			/**
			 * 根据车辆所属分组来获取车辆的环状路线
			 */
			getVehicleStation(vehicle);

		} else {
			boolean haveMaerial = TjdCxt.haveMaterial(vehicle);
			log.debug("agvCode:{},haveMaterial:{}", vehicle.getId(), haveMaerial);
			Set<String> points = vehicle.getCycleMaterialPoints();

			List<WorkCycleConfig> list = Lists.newArrayList();

			points.stream().forEach(item -> {
				WorkCycleConfig config = CYCLE_MAP.get(item);
				if (Objects.nonNull(config) && config.getEnabled()) {
					list.add(config);
				}
			});
			/**
			 * 根据小车上的物料点来设置环状线
			 */
			if (CollectionUtils.isNotEmpty(list)) {
				List<WorkCycleConfig> orderList = list.parallelStream()
						.sorted(Comparator.comparing(WorkCycleConfig::getSeq)).collect(Collectors.toList());

				ListOrderedMap<String, WorkCycleConfig> materialCycle = new ListOrderedMap<String, WorkCycleConfig>();
				orderList.stream().forEach(item -> {
					materialCycle.put(item.getId(), item);
				});

				vehicle.setCycle(materialCycle);
			}else {
				getVehicleStation(vehicle);
			}

		}
	}

	/**
	 * 根据车辆所属分组来获取车辆所属的工作站和环
	 * 
	 * @param vehicle
	 */
	private static void getVehicleStation(Vehicle vehicle) {

		List<WorkCycleConfig> list = getWorkCycleConfigsByAgvGroupId(vehicle.getAgvGroupId());
		ListOrderedMap<String, WorkCycleConfig> data = new ListOrderedMap<>();
		list.forEach(item -> {
			data.put(item.getId(), item);
		});
		vehicle.setCycle(data);

	}

	public static List<WorkCycleConfig> getWorkCycleConfigsByAgvGroupId(String agvGroupId) {
		List<WorkCycleConfig> list = CYCLE_MAP.valueList().parallelStream()
				.filter(StringUtils.isBlank(agvGroupId)
						? p -> StringUtils.isBlank(p.getAgvGroupId()) && p.getEnabled()
						: p -> StringUtils.equals(p.getAgvGroupId(), agvGroupId) && p.getEnabled())
				.sorted(Comparator.comparing(WorkCycleConfig::getSeq)).collect(Collectors.toList());
		return list;
	}

	public static List<Pair<WorkCycleConfig, WorkCycleConfig>> getWorkCycleConfigsByAgvGroupId(String agvGroupId , String altAgvGroupId) {

		List<WorkCycleConfig> src = getWorkCycleConfigsByAgvGroupId(agvGroupId);
		List<WorkCycleConfig> altConfigs = getWorkCycleConfigsByAgvGroupId(altAgvGroupId);
		List<Pair<WorkCycleConfig, WorkCycleConfig>> res = Lists.newArrayList();
		for (WorkCycleConfig conf :
				src) {
			for (WorkCycleConfig temp :
					altConfigs) {
				if (StringUtils.equalsIgnoreCase(conf.getAgvMapId(), temp.getAgvMapId()) && StringUtils.equalsIgnoreCase(conf.getMarkCode(), temp.getMarkCode())) {
					res.add(Pair.of(conf, temp));
				}
			}

		}
		return res;
	}
	/**
	 * 获得环线第一个站点
	 * 
	 * @return
	 */
	public final static String getFirstKey(ListOrderedMap<String, WorkCycleConfig> cycle) {
		String key = getKey(cycle, cycle.firstKey(), false);
		if (StringUtils.isBlank(key)) {
			return getKey(cycle, cycle.firstKey(), true);
		}
		return key;
	}

	/**
	 * getNextKey
	 * 
	 * @Title: getNextKey
	 * @Description: 返回当前key 的下一个key
	 * @param @param  cycle
	 * @param @param  currentKey
	 * @param @return 设定文件
	 * @return String 返回类型
	 * @throws
	 */
	public final static String getNextKey(ListOrderedMap<String, WorkCycleConfig> cycle, String currentKey) {
		if( Objects.isNull(cycle) || cycle.isEmpty()) {
			log.error("cycle_is_empyt");
			return StringUtils.EMPTY; 
		}

		if (CollectionUtils.size(cycle) == 1 && StringUtils.isNotBlank(currentKey)) {
			return currentKey;
		}
		String next = getKey(cycle, cycle.nextKey(currentKey), true, currentKey);
		if (StringUtils.isBlank(next)) {
			return getKey(cycle, cycle.firstKey(), true, currentKey);
		}
		return next;

	}

	/**
	 * 获得环线最后一个站点
	 * 
	 * @return
	 */
	public final static String getLastKey(ListOrderedMap<String, WorkCycleConfig> cycle) {
		String key = getKey(cycle, cycle.lastKey(), false);
		if (StringUtils.isBlank(key)) {
			return getKey(cycle, cycle.lastKey(), true);
		}
		return key;

	}

	/**
	 * 根据传入的key 值向前或者向后获取有效的配置项( 包括传入项 ) 用于实现 获得环状线 第一个key , 最后一个key, 以及当前key
	 * 的上一个key 和下一个key
	 * 
	 * @param key         传入的key 值
	 * @param backFoward  方向
	 * @param includeSelf 是否包括转入项 只支持一个key
	 * @return
	 */
	public final static String getKey(ListOrderedMap<String, WorkCycleConfig> cycle, String key, boolean backFoward,
			String... excludeKey) {

		if (StringUtils.isBlank(key)) {

			return null;
		}
		WorkCycleConfig cycleConfig = cycle.get(key);

		if (Objects.isNull(cycleConfig)) {

			return null;
		} else {
			boolean shouldSkip = ArrayUtils.isNotEmpty(excludeKey) && ArrayUtils.contains(excludeKey, key);

			if (cycleConfig.getEnabled() && !shouldSkip) {

				return key;
			} else {
				if (backFoward) {
					return getKey(cycle, cycle.nextKey(key), backFoward, excludeKey);
				} else {
					return getKey(cycle, cycle.previousKey(key), backFoward, excludeKey);
				}
			}
		}

	}

	/**
	 * @Title: shouldNextStation
	 * @Description: 是否应该开启下一个工作站任务的创建
	 * @param @param  missionWork
	 * @param @return 设定文件
	 * @return boolean 返回类型
	 * @throws
	 */
	public final static boolean shouldBeginNextStation(Vehicle vehicle, MissionWork missionWork, String missionId) {

		List<String> carefullStatus = Lists.newArrayList(MISSION_WORK_STATUS_SUCCESS, MISSION_WORK_STATUS_SHUTDOWN);
		boolean taskRequire = carefullStatus.contains(missionWork.getStatus())
				&& StringUtils.equals(missionWork.getMissionId(), missionId);

		return taskRequire && (vehicleContinue(vehicle));

	}

	/**
	 * @Title: vehicleContinue
	 * @Description: 车辆继续
	 * @param @param  vehicle
	 * @param @return 设定文件
	 * @return boolean 返回类型
	 * @throws
	 */
	public final static boolean vehicleContinue(Vehicle vehicle) {
		Boolean linePatrolMode = vehicle.getLinePatrolMode();
		boolean haveMaerial = TjdCxt.haveMaterial(vehicle);

		return
		/**
		 * 有料
		 */
		haveMaerial ||
		/**
		 * 巡线且电量够
		 */
				(linePatrolMode && !vehicle.powerLow());
	}

	/**
	 * @Title: isAgv
	 * @Description: 判断是否agv
	 * @param @param  str
	 * @param @return 设定文件
	 * @return boolean 返回类型
	 * @throws
	 */
	public final static boolean isAgv(String str) {

		return StringUtils.equalsIgnoreCase(AGV, str) || StringUtils.isBlank(str);
	}

	/**
	 * @Title: isStation
	 * @Description: 判断是否是工作站
	 * @param @param  str
	 * @param @return 设定文件
	 * @return boolean 返回类型
	 * @throws
	 */
	public final static boolean isStation(String str) {

		return StringUtils.equalsIgnoreCase(STATION, str) || StringUtils.isBlank(str);
	}

	/**
	 * @Title: isTranferDown
	 * @Description: 当前是下料
	 * @param @param  str
	 * @param @return 设定文件
	 * @return boolean 返回类型
	 * @throws
	 */

	public final static boolean isTranferDown(String str) {

		return StringUtils.equalsIgnoreCase(TRANFER_DOWN, str);
	}

	/**
	 * @Title: isTranferUp
	 * @Description: 当前是上料
	 * @param @param  str
	 * @param @return 设定文件
	 * @return boolean 返回类型
	 * @throws
	 */
	public final static boolean isTranferUp(String str) {

		return StringUtils.equalsIgnoreCase(TRANFER_UP, str);
	}

	/**
	 * @Title: pathIdAgvs
	 * @Description: 路径id 和车辆的对应关系
	 * @param @return 设定文件
	 * @return MultiValueMap<String,String> 返回类型
	 * @throws
	 */
	public final static MultiValueMap<String, String> pathIdAgvs() {
		MultiValueMap<String, String> result = new LinkedMultiValueMap<String, String>();
		DirectedGraph dGraph = MapGraphUtil.getOriginDirectedGraph();
		Set<DirectedEdge> allEdges = dGraph.getAllEdges();
		allEdges.stream().forEach(edge -> {
			String pathId = edge.getPathId();
			ConcurrentHashMap<String, Double> agvPosition = edge.getAgvPosition();

			if (StringUtils.isNotBlank(pathId) && MapUtils.isNotEmpty(agvPosition)) {

				result.addAll(edge.getPathId(), new ArrayList<String>(agvPosition.keySet()));
			}

		});
		return result;
	}

	public static final String getPosition(Vehicle vehicle) {

		String agvCode = vehicle.getId();
		Double x = 0d;
		Double y = 0d;
		String markCode = "";
		DefaultVehicleStatus defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
		if (Objects.nonNull(defaultVehicleStatus) && Objects.nonNull(defaultVehicleStatus.getPosition())) {
			PositionStatus position = defaultVehicleStatus.getPosition();
			x = position.getPos_x();
			y = position.getPos_y();
			markCode = position.getPos_current_station_code();
		}
		;
		TjdHelper tjdHelper = getHelper();
		if (StringUtils.isBlank(markCode)) {
			markCode = tjdHelper.getOnMarkCode(vehicle.getId());
		}
		String position = "X=" + String.format("%.2f", x) + " Y=" + String.format("%.2f", y) + " CODE=" + markCode
				+ " POSITION=" + tjdHelper.getPosition(agvCode);
		return position;
	}

	/**
	 * 获得帮助类
	 * 
	 * @return
	 */
	public static TjdHelper getHelper() {
		if (Objects.isNull(tjdHelper)) {
			tjdHelper = ApplicationUtils.getBean(TjdHelper.class);
		}

		return tjdHelper;
	}

	/**
	 * 获得帮助类
	 * 
	 * @return
	 */
	public static MissionWorkService getMissionWorkSerive() {
		if (Objects.isNull(missionWorkService)) {
			missionWorkService = ApplicationUtils.getBean(MissionWorkService.class);
		}

		return missionWorkService;
	}

	/**
	 * 获得帮助类
	 * 
	 * @return
	 */
	public static CheckAndSendPathService getCheckAndSendPathService() {
		if (Objects.isNull(checkAndSendPathService)) {
			checkAndSendPathService = ApplicationUtils.getBean(CheckAndSendPathService.class);
		}

		return checkAndSendPathService;
	}

	public static SidePathResourcePool getSidePathResourcePool() {
		if (Objects.isNull(sidePathResourcePool)) {
			sidePathResourcePool = ApplicationUtils.getBean(SidePathResourcePool.class);
		}

		return sidePathResourcePool;
	}

	/**
	 * 递归查找最近的拐弯点
	 * 
	 * @param agvMapId
	 * @param currentStation
	 * @return
	 */
	public static String getUTurnStationMarkCodeOrg(String agvMapId, String curretMarkCode, Set<String> dealedMarkCode,
			Integer typeTimes, String agvGroupId) {

		if (dealedMarkCode.contains(curretMarkCode)) {
			return null;
		}
		dealedMarkCode.add(curretMarkCode);

		MarkerService markerService = ApplicationUtils.getBean(MarkerService.class);

		Marker currentMarker = markerService.selectMarkerByCode(agvMapId, curretMarkCode);
		if (Objects.isNull(currentMarker)) {
			log.error("curretMarkCode：{}，not_exist", curretMarkCode);

			return null;
		}
		Set<String> outSidePathId = MapGraphUtil.getOutSidePathId(currentMarker.getId());
		if (CollectionUtils.isEmpty(outSidePathId)) {
			outSidePathId = MapGraphUtil.getInSidePathId(currentMarker.getId());
		}
		/**
		 * 多个出度
		 */
		Set<String> typeSidePath = Sets.newLinkedHashSet();
		Set<String> noReverseSidePath = Sets.newLinkedHashSet();
		BidiMap<String, Double> badimaap = new DualHashBidiMap<>();
		if (CollectionUtils.size(outSidePathId) > 1) {
			typeTimes = getMax(outSidePathId, typeSidePath, noReverseSidePath, badimaap, typeTimes);
		}
		/**
		 * 只是保留有工作站的路径
		 */
	
        
		for (String sidePathId : outSidePathId) {
			SidePath sidePathItem = MapGraphUtil.getSidePathBySidePathId(sidePathId);
			
			
			/*
			 * String log3 = getLog( sidePathId); System.out.println("log3:" + log3);
			 */
			 
			  
			String endMarkerId = sidePathItem.getEndMarkerId();
//			 SidePath reverseSidePath = MapGraphUtil.getReverseSidePath( sidePathId);
//			 if( Objects.nonNull(reverseSidePath) ) {
//				 continue ;
//			 }
			Marker endMarker = MapGraphUtil.getMarkerByMarkerId(endMarkerId);
			String code = endMarker.getCode();
			Path path = MapGraphUtil.getPathByPathId(sidePathItem.getPathId());
			PathAgvType agvType = pathAgvType(path.getId());
			if (Objects.nonNull(agvType) && StringUtils.isNotBlank(agvType.getAgvTypeIds())) {

				String[] strings = StringUtils.split(agvType.getAgvTypeIds(), ",");
				HashSet<String> typeIds = Sets.newHashSet(strings);

				if (typeIds.contains(MISSION_WORK_NAME_M26)) {

					if (getStationCode(agvGroupId ).contains(code)) {

						return code;
					} else {

						return getUTurnStationMarkCodeOrg(agvMapId, code, dealedMarkCode, typeTimes , agvGroupId);
					}

				} else {

					return getUTurnStationMarkCodeOrg(agvMapId, code, dealedMarkCode, typeTimes , agvGroupId);
				}

			} else {
				if (getStationCode( agvGroupId).contains(code)) {

					return code;
				}

				return getUTurnStationMarkCodeOrg(agvMapId, code, dealedMarkCode, typeTimes , agvGroupId);
			}

		}

		return null;

	}

	/** 获得和分组匹配的工作站
	 * @param agvGroupId
	 * @return
	 */
	public static Set<String> getStationCode(String agvGroupId ) {
		STATION_MARK_CODE_MAP.values() ;
		Map<String, List<WorkCycleConfig>> map = CYCLE_MAP.valueList().parallelStream().filter(i -> BooleanUtils.toBoolean( i.getEnabled() )).collect(Collectors.groupingBy( i ->{
		   
			 	return  Objects.toString(StringUtils.isBlank(i.getAgvGroupId()) ?   null : i.getAgvGroupId() );

		}));
		  List<WorkCycleConfig> list = map.getOrDefault( Objects.toString(StringUtils.isBlank( agvGroupId )?   null : agvGroupId ), Lists.newArrayList());
		  
		  return list.parallelStream().map( WorkCycleConfig::getMarkCode).collect(Collectors.toSet());
	}



	/**
	 * 获得优先级最大的出度
	 * 
	 * @param outSidePathId
	 * @param typeSidePath
	 * @param noReverseSidePath
	 * @param badimaap
	 */
	public static Integer getMax(Set<String> outSidePathId, Set<String> typeSidePath, Set<String> noReverseSidePath,
			BidiMap<String, Double> badimaap, Integer typeTimes) {
		outSidePathId.parallelStream().forEach(p -> {
			SidePath sidePathItem = MapGraphUtil.getSidePathBySidePathId(p);
			SidePath reverseSidePath = MapGraphUtil.getReverseSidePath(p);
			PathAgvType agvType = pathAgvType(sidePathItem.getPathId());
			
			/*
			 * String log2 = getLog(p); System.out.println("log2:" + log2);
			 */
			 
			 

			if (Objects.nonNull(agvType) && StringUtils.isNotBlank(agvType.getAgvTypeIds())) {
				String[] strings = StringUtils.split(agvType.getAgvTypeIds(), ",");
				HashSet<String> typeIds = Sets.newHashSet(strings);
				if (typeIds.contains(MISSION_WORK_NAME_M26)) {
					typeSidePath.add(p);
				}

			} else if (Objects.isNull(reverseSidePath)) {
				noReverseSidePath.add(p);
			}

		});

		outSidePathId.addAll(typeSidePath);
		outSidePathId.addAll(noReverseSidePath);

		HashSetValuedHashMap<Double, String> result = new HashSetValuedHashMap<>();

		/**
		 * 求权重最优先的
		 */
		for (String path : outSidePathId) {

			SidePath sidePathItem = MapGraphUtil.getSidePathBySidePathId(path);
			Double weight = Optional.ofNullable( sidePathItem.getWeightRatio()).orElse( -1d );
			if (typeSidePath.contains(path)) {
				weight = weight + 110 * typeTimes;
				typeTimes = typeTimes - 1;
				if (typeTimes == 0) {
					typeTimes = -1;
				}
			}
			/*
			 * String log = getLog( path ); System.out.println("log: " + log );
			 */
			  
			badimaap.put(path, weight);
			result.put(weight, path);

		}
		
		Double max = Collections.max(badimaap.values());

		Set<String> set = result.get(max);

		
		/*
		 * for (String key : set) { String log = getLog( key);
		 * System.out.println("log: " + log );
		 * 
		 * }
		 */
		 

		outSidePathId.clear();
		outSidePathId.addAll(set);
		return typeTimes;
	}

	public static String getLog(String string) {
		SidePath sidePathItem = MapGraphUtil.getSidePathBySidePathId(string);
		String startMarkerId = sidePathItem.getStartMarkerId();
		String endMarkerId = sidePathItem.getEndMarkerId();
		Marker startMarker = MapGraphUtil.getMarkerByMarkerId(startMarkerId);
		Marker endMarker = MapGraphUtil.getMarkerByMarkerId(endMarkerId);
		String log = startMarker.getCode() + "->" + endMarker.getCode();
		return log;
	}

	/**
	 * 获取路径上的车型
	 * 
	 * @param pathId
	 * @return
	 */
	public final static PathAgvType pathAgvType(String pathId) {
		PathAgvTypeService pathAgvTypeService = ApplicationUtils.getBean(PathAgvTypeService.class);
		Path path = MapGraphUtil.getPathByPathId(pathId);
		PathAgvType agvType = pathAgvTypeService.selectByPathId(path.getId());
		return agvType;
	};

	/**
	 * 判断机器人是否在移动
	 * 
	 * @param vehicle
	 * @return
	 */
	public final static Boolean isMoving(Vehicle vehicle) {
		DefaultVehicleStatus defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
		if (defaultVehicleStatus == null) {
			return false;
		}
		SpeedStatus speed = defaultVehicleStatus.getSpeed();
		if ( speed == null) {
			return false;
		}
		Double vx = speed.getSpeed_vx();
		Double w = speed.getSpeed_w();
		Double r_vx = speed.getSpeed_r_vx();
		Double r_w = speed.getSpeed_r_w();

		if (!isZero(vx) || !isZero(w) || !isZero(r_vx) || !isZero(r_w)) {
			return true;
		}

		return false;
	}

	/**
	 * 正在等待
	 * 
	 * @param vehicle
	 * @return
	 */
	public final static Boolean isWaiting(Vehicle vehicle) {

		String missionWorkId = vehicle.getMissionWorkId();
		if (StringUtils.isBlank(missionWorkId)) {
			return false;
		}
		MissionWork missionWork = getMissionWorkSerive().selectById(missionWorkId);
		if (Objects.isNull(missionWork)) {
			return false;
		}
		return MISSION_WORK_STATUS_WAIT.equals(missionWork.getStatus());
	}
	public final static Boolean isWaiting(String agvId){

		DefaultVehiclePool bean = ApplicationUtils.getBean(DefaultVehiclePool.class);

		Vehicle vehicle = bean.getVehicle(agvId);
		if(Objects.isNull( vehicle)){
			return  false ;
		}
		return  isWaiting( vehicle);
	}

	/**
	 * 在工作站等待或者正在申请资源 忽略停止检测
	 * 
	 * @param vehicle
	 * @return
	 */
	public final static Boolean isBlock(Vehicle vehicle) {

		BlockCheckService blockCheckService = ApplicationUtils.getBean(BlockCheckService.class);
		List<String> sharedPath = blockCheckService.checkSharedPathAgvId(vehicle.getId());
		if (CollectionUtils.isNotEmpty(sharedPath)) {
			log.debug("vehicleId:{},blockBy:{}", vehicle.getId(), StringUtils.join(sharedPath, "---"));
		}
		// 跟当前车辆无共道情形
		return !CollectionUtils.isEmpty(sharedPath);
	}

	// 判断一个Double是否为零
	private static Boolean isZero(Double d) {
		// 判断是否停止阈值
		return Math.abs(d) <= 0.01;
	}

	/**
	 * @param agvMapId
	 * @param curretMarkCode
	 * @return
	 */
	@SuppressWarnings("finally")
	public static String getUTurnStationMarkCode(String agvMapId, String curretMarkCode , String agvGroupId) {
			String res = null;
		try {
			res  = getUTurnStationMarkCodeOrg(agvMapId, curretMarkCode, Sets.newHashSet(), 1 , agvGroupId);
		} finally {
			if( StringUtils.isBlank(res)) {
				NodeCodeList<String> workCode = getCompassController().getNearWorkCode(agvMapId, curretMarkCode, getStationCode ( agvGroupId ));
				  if(Objects.nonNull(workCode) && CollectionUtils.isNotEmpty(workCode)){
					  return (String) workCode.getLast();
				  }
			}
			return res;
		
		}
	}

	/**
	 * 清空缓存数据
	 */
	public static FullLocationService getFullLocationService() {

		FullLocationService locationService = ApplicationUtils.getBean(FullLocationService.class);
		return locationService;

	}

	public static GetBestWayWithMarkIdController getGetBestWayWithMarkIdController() {

		GetBestWayWithMarkIdController locationService = ApplicationUtils.getBean(GetBestWayWithMarkIdController.class);
		return locationService;

	}
	public static CompassController getCompassController() {
		
		CompassController locationService = ApplicationUtils.getBean(CompassController.class);
		return locationService;
		
	}

	/**
	 * 清空缓存数据
	 */
	public static void clearCacheData() {

		UTURN_CACHE.clear();

		MARKCODE_NEAREST_STATION.clear();

	}

	/**
	 * 是否正在充电
	 * 
	 * @return
	 */
	public static final boolean isCharge(Vehicle vehicle) {

		if (Objects.isNull(vehicle)) {
			return false;

		}

		DefaultVehicleStatus defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
		if (Objects.isNull(defaultVehicleStatus)) {
			return false;
		}
		BatteryStatus battery = defaultVehicleStatus.getBattery();
		if (Objects.isNull(battery)) {
			return false;
		}
		Double battery_charge = battery.getBattery_charge();
		if (VehicleConstant.TASK_STATUS_CHARGE.equals(vehicle.getWorkStatus()) || battery_charge > 0) {
			return true;

		}
		return false;
	}

	/**
	 * 正在泊车
	 * 
	 * @param vehicle
	 * @return
	 */
	public static final boolean isPark(Vehicle vehicle) {

		FullLocationService fullLocationService = ApplicationUtils.getBean(FullLocationService.class);
		VehicleLocation vehicleLocation = fullLocationService.getVehicleLocation(vehicle.getId());
		if (Objects.isNull(vehicleLocation)) {
			return false;
		}
		Marker marker = vehicleLocation.getMarker();
		if (Objects.isNull(marker)) {
			return false;
		}
		return BooleanUtils.toBoolean(marker.getIsPark());
	}

	/**
	 * 是否应该上报
	 * 
	 * @param item
	 * @return
	 */
	public final static boolean shoudlReport(Vehicle item) {
		/**
		 * 正在移动
		 */
		Boolean moving = TjdCxt.isMoving(item);

		Boolean isBlock = TjdCxt.isBlock(item);
		Boolean waiting = TjdCxt.isWaiting(item);
		/**
		 * 非正常原因没有移动
		 */
		boolean careFull = !moving && !waiting && !isBlock;
		/*
		 * log.
		 * debug("agvCode:[{}],event:[异常上报],，careful:[{}] ,moving：[{}],wait:[{}],isBlock:[{}]"
		 * , item.getId(), careFull, moving, waiting, isBlock);
		 */
		return careFull;

	}

	/**
	 * 获得已经排好序的车辆
	 * 
	 * @param table
	 * @return
	 */
	public final static LinkedHashSet<String> getLinkedVehicles(TreeBasedTable<String, String, Double> table) {
		String rowKey = getMaxValueRowKey(table);
		if(StringUtils.isBlank(rowKey)) {
			return new LinkedHashSet<String>();
		}
		SortedSet<String> rowKeySet = table.rowKeySet();

		SortedSet<String> tailSet = rowKeySet.tailSet(rowKey);

		SortedSet<String> headSet = rowKeySet.headSet(rowKey);

		LinkedHashSet<String> data = new LinkedHashSet<>(tailSet);
		data.addAll(headSet);

		return data;
	}

	/**
	 * @param res
	 * @param max
	 * @return
	 */
	public final static String getMaxValueRowKey(TreeBasedTable<String, String, Double> res) {
		if( Objects.isNull(res) || res.isEmpty()) {
			return StringUtils.EMPTY ;
		}
		Double max = Collections.max(res.values());
		for (Cell<String, String, Double> item : res.cellSet()) {
			if (Double.toString(item.getValue()).equals(Double.toString(max))) {
				return item.getRowKey();
			}

		}
		return null;
	}

	
	/** 获得两点所有的路径
	 * @param src
	 * @param target
	 * @return
	 */
	  public final static   Map< DirectedNode, LinkedList<DirectedNode>> pathAll(DirectedNode start,DirectedNode end,  DirectedGraph dGraph) {
	        Collection<DirectedNode> nodes = dGraph.getNodeMap().values();
	        Set<DirectedEdge> allEdges = dGraph.getAllEdges();

	        LinkedBlockingDeque<DirectedNode> minHeap = new LinkedBlockingDeque<DirectedNode>();//节点的优先队列
	        Map<DirectedNode, DirectedNodeProperty> nodePropertyMap = new HashMap<>();//节点及其属性映射表
	        Map<DirectedEdge, DirectedEdgeProperty> edgePropertyMap = new HashMap<>();//边及其属性映射表

	        //init data
	        for (DirectedNode node : nodes) {
	            nodePropertyMap.put(node, new DirectedNodeProperty());
	        }
	        for (DirectedEdge edge : allEdges) {
	            edgePropertyMap.put(edge, new DirectedEdgeProperty(edge));
	        }

	        //computeShortestPath
	        Queue<DirectedNode> S = new LinkedList<>();
	        Map< DirectedNode, LinkedList<DirectedNode>>  path = Maps.newHashMap();
	        minHeap.add( start);

	        while (minHeap.size() > 0) {
	            DirectedNode nodeV = minHeap.poll();
	           
	            if (S.contains(nodeV)) continue;
	            else {
	                S.add(nodeV);
	                Set<DirectedNode> adjacentNodes = dGraph.getSuccessors(nodeV);
	                for (DirectedNode nodeT : adjacentNodes) {
	                	 if (S.contains( nodeT)) continue;
	                    DirectedEdge edge = dGraph.getEdgeByStartEndNode(nodeV, nodeT);
	                    
	                    if (edge == null) continue;
	                   LinkedList<DirectedNode> list = path.getOrDefault( nodeV , new LinkedList<>());
	                   if(CollectionUtils.isEmpty(list)) {
	                	   list.add(nodeV);
	                   }
	                   list.addLast(nodeT);
	                   
	                   path.put(nodeT, list);
	                   if(!nodeT.equals(end)) {
	                	   minHeap.add( nodeT);
	                   }
                    
                     if (getParent(nodePropertyMap, nodeV) != null && getParent(nodePropertyMap, nodeV).equals(nodeT)) {
                         setParent(nodePropertyMap, nodeV, null);
                     }
                     setParent(nodePropertyMap, nodeT, nodeV);
	                }
	                path.remove(nodeV);
	            }
	        }
			return path ;
	     
	    }

	  

	    private final static  DirectedNode getParent(Map<DirectedNode, DirectedNodeProperty> nodePropertyMap, DirectedNode node) {
	        return nodePropertyMap.get(node).parent;
	    }

	    private final static  void setParent(Map<DirectedNode, DirectedNodeProperty> nodePropertyMap, DirectedNode node, DirectedNode value) {
	        nodePropertyMap.get(node).parent = value;
	    }


	/**
	 * 处理车辆联动模式
	 * @param vehicle
	 * @param currentStation
	 */
	public static void handleLinkStation(Vehicle vehicle, String currentStation) {

		try {
			String nextStation = getNextStation(vehicle, currentStation);
			log.info("vehicle:{},station:{} next_station:{}", vehicle.getId() , currentStation , nextStation);
			WorkCycleConfig workCycleConfig = vehicle.getCycle().get(nextStation);
			WorkCycleConfig currentConfig = vehicle.getCycle().get(currentStation);
			if(Objects.isNull( workCycleConfig)){
				return;
			}
			boolean linkageThis = ReleaseMode.LINKAGE.isThis(currentConfig.getReleaseMode());
			if( !linkageThis){
				// 当前工作站非联动下一站,直接退出
				return;
			}
			DefaultVehiclePool defaultVehiclePool = ApplicationUtils.getBean(DefaultVehiclePool.class);
			String finalNextStation = nextStation;
			Optional<Vehicle> nextVehicle = defaultVehiclePool.getAll().parallelStream().filter(p -> TjdCxt.isWaiting(p)&& !p.getId().equals( vehicle.getId()) && finalNextStation.equals(p.getCurrentStation())).findFirst();
			if(nextVehicle.isPresent()){
				Vehicle vehicle1 = nextVehicle.get();
				vehicle1.continueMissionWork( vehicle1.getMissionWorkId());
				log.info("vehicle:{},station:{}联动工作站:{} ve2:{} continue_mission_work:{}", vehicle.getId() , currentStation , nextStation, vehicle1, vehicle1.getMissionWorkId());
			}
		} catch (Exception e) {

		}

	}

	/**
	 * 当前站联动
	 * @param vehicle
	 * @param currentStation
	 */
	public static boolean handlinkCurrentStation(Vehicle vehicle, String currentStation){

		try {
			DefaultVehiclePool defaultVehiclePool = ApplicationUtils.getBean(DefaultVehiclePool.class);
			Optional<Vehicle> nextVehicle = defaultVehiclePool.getAll().parallelStream().filter(p -> TjdCxt.isWaiting(p)&& !p.getId().equals( vehicle.getId()) && currentStation.equals(p.getCurrentStation())).findFirst();
			if(nextVehicle.isPresent()){
				Vehicle vehicle1 = nextVehicle.get();
				vehicle1.continueMissionWork( vehicle1.getMissionWorkId());
				log.info("vehicle:{},station:{}联动工作站:{} ve2:{} continue_mission_work:{}", vehicle.getId() , currentStation , currentStation, vehicle1, vehicle1.getMissionWorkId());
				return true;
			}
		} catch (Exception e) {

		}
		return false;
	}

	/**
	 * 获得下一个工作站
	 * @param vehicle
	 * @param currentStation
	 * @return
	 */
	public static String getNextStation(Vehicle vehicle, String currentStation) {
		String nextStation = vehicle.getCycle().nextKey(currentStation);
		if(StringUtils.isBlank( nextStation)){
			nextStation = vehicle.getCycle().firstKey();
		}
		return nextStation;
	}
}
