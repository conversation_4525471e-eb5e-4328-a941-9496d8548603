package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 14:25
 */
public class AGVConstant {

    //连接时同步地图方式
    public static final String INIT_MAP_MODE_AUTO_SYNC = "AUTO_SYNC";//自动同步
    public static final String INIT_MAP_MODE_MANUAL_SYNC = "MANUAL_SYNC";//手动同步

    //导航类型
    public static final String NAVIGATION_TYPE_LASER = "LASER";//激光导航
    public static final String NAVIGATION_TYPE_QR_CODE = "QR_CODE";//二维码导航
    public static final String NAVIGATION_TYPE_BLEND = "BLEND";//混合导航（激光和二维码都支持）

    //地图状态
    public static final Integer MAP_STATUS_NORMAL = 1;//正常(已同步且启用)
    public static final Integer MAP_STATUS_NO = 2;//无地图
    public static final Integer MAP_STATUS_NOT_SYNC = 3;//未同步
    public static final Integer MAP_STATUS_SYNC_RUNNING = 4;//同步中
    public static final Integer MAP_STATUS_SYNC_FAULT = 5;//同步失败
    public static final Integer MAP_STATUS_NOT_ENABLE = 6;//未启用

    //任务状态
    public static final Integer TASK_STATUS_FREE = 1;//空闲
    public static final Integer TASK_STATUS_WORK = 2;//任务
    public static final Integer TASK_STATUS_CHARGING = 3;//充电
    public static final Integer TASK_STATUS_HOMING = 4;//归位
    public static final Integer TASK_STATUS_ERROR = 5;//任务异常

    //异常状态
    public static final Integer ABNORMAL_STATUS_NO = 1;//无异常
    public static final Integer ABNORMAL_STATUS_WORK = 2;//工作异常
    public static final Integer ABNORMAL_STATUS_CHARGING = 3;//充电异常
    public static final Integer ABNORMAL_STATUS_HOMING = 4;//归位异常

    //录制状态
    public static final Integer RECORD_MAP_STATUS_NO = 1;//未录制
    public static final Integer RECORD_MAP_STATUS_BEING_START = 2;//开始中
    public static final Integer RECORD_MAP_STATUS_GRID = 3;//录制栅格
    public static final Integer RECORD_MAP_STATUS_FEATURE = 4;//录制特征

    //连接状态
    public static final Integer CONNECT_STATUS_SUCCESS = 1;//已连接
    public static final Integer CONNECT_STATUS_RUNNING = 2;//连接中
    public static final Integer CONNECT_STATUS_FAULT = 3;//未连接

    //手动状态
    public static final Integer MANUAL_ACTION_STATUS_NO = 1;//无动作
    public static final Integer MANUAL_ACTION_STATUS_RUNNING = 2;//动作中
    public static final Integer MANUAL_ACTION_STATUS_ABNORMAL = 3;//动作异常

    //控制模式
    public static final Integer AUTO_CONTROL_MODE = 1;//自动模式
    public static final Integer MANUAL_CONTROL_MODE = 2;//手动模式
    public static final Integer RECORD_CONTROL_MODE = 3;//录制模式

    //功能配置类型
    public static final String FUNCTION_TYPE_CHARGE = "SMART_CHARGE";//智能充电
    public static final String FUNCTION_TYPE_WAIT = "SMART_WAIT";//智能归位

    //智能充电是否对接
    public static final Integer SMART_CHARGE_DOCKING_YES = 0;//对接
    public static final Integer SMART_CHARGE_DOCKING_NO = 1;//不对接

    //AGV功能配置是否启用
    public static final Integer SMART_TASK_IS_ENABLE = 0;//启用
    public static final Integer SMART_TASK_IS_UNABLE = 1;//不启用

    //AGV统计
    public static final Integer ALL = 0;//所有
    public static final Integer DAY = 1;//天
    public static final Integer WEEK = 7;//周
    public static final Integer MONTH = 30;//月

    //AGV日志类型
    public static final Integer AGV_LOG_TYPE_ON_LINE = 1;//在线
    public static final Integer AGV_LOG_TYPE_CHARGE = 2;//充电


    // AGV状态
    public static final String QUERY_WORK_STATUS = "workStatus";//任务中
    public static final String QUERY_FREE_STATUS = "freeStatus";//空闲中
    public static final String QUERY_CHARGER_STATUS = "chargeStatus";//充电
    public static final String QUERY_ONLINE_STATUS = "offlineStatus";//未连接
    public static final String QUERY_ABNORMAL_STATUS = "abnormalStatus";//异常

}
