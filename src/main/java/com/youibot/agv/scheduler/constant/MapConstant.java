package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 15:33
 */
public class MapConstant {

    public static final String AGV_MAP_TYPE_LASER = "LASER_MAP";//激光地图
    public static final String AGV_MAP_TYPE_VIRTUAL = "VIRTUAL_MAP";//虚拟地图

    public static final String MARKER_TYPE_INITIAL = "INITIAL_MARKER";//初始点
    public static final String MARKER_TYPE_CHARGING = "CHARGING_MARKER";//充电点
    public static final String MARKER_TYPE_NAVIGATION = "NAVIGATION_MARKER";//导航点
    public static final String MARKER_TYPE_QRCODE = "QRCODE_MARKER";//二维码点
    public static final String MARKER_TYPE_WAIT = "WAIT_MARKER";// 待机点
    public static final String MARKER_TYPE_WORK = "WORK_MARKER";//工作点
    public static final String MARKER_TYPE_ADJUSTMENT = "ADJUST_MARKER";//调整点
    public static final String MARKER_TYPE_ELEVATOR = "ELEVATOR_MARKER";//电梯点

    public static final String DOCKING_POINT_TYPE_REFLECTOR = "REFLECTOR_POINT";//反光条特征对接点
    public static final String DOCKING_POINT_TYPE_V = "V_POINT";//V型特征对接点
    public static final String DOCKING_POINT_TYPE_CHARGE_STATION = "CHARGE_STATION_POINT";//充电桩对接点

    //path的方向
    public static final Integer PATH_DIRECTION_TWO_WAY = 0;//双向
    public static final Integer PATH_DIRECTION_FORWARD = 1;//正向
    public static final Integer PATH_DIRECTION_REVERSE = 2;//反向

    //path线类型
    public static final int PATH_LINE_TYPE_STRAIGHT = 1;//直线
    public static final int PATH_LINE_TYPE_CURVE = 2;//曲线

    //是否是充电桩对接点
    public static final Integer DOCKING_POINT_IS_CHARGE_STATION = 1;//是充电桩
    public static final Integer DOCKING_POINT_IS_NOT_CHARGE_STATION = 0;//不是充电桩

    //地图使用状态
    public static final String MAP_USAGE_STATUS_ENABLE = "ENABLE";//启用
    public static final String MAP_USAGE_STATUS_DISABLE = "DISABLE";//禁用

    //标记点使用状态
    public static final String MARKER_USAGE_STATUS_ENABLE = "ENABLE";//启用
    public static final String MARKER_USAGE_STATUS_DISABLE = "DISABLE";//禁用

    //路径使用状态
    public static final String PATH_USAGE_STATUS_ENABLE = "ENABLE";//启用
    public static final String PATH_USAGE_STATUS_DISABLE = "DISABLE";//禁用

}
