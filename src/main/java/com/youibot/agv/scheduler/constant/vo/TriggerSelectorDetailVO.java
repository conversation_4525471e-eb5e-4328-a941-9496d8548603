package com.youibot.agv.scheduler.constant.vo;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionWork;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/1/12 9:44
 * @Description: 已完成
 */
@Data
public class TriggerSelectorDetailVO extends PageInfo<MissionWork> implements Serializable {

    @ApiModelProperty(value = "已触发")
    private Integer alreadyTrigger;

    @ApiModelProperty(value = "已完成")
    private Integer completed;

    @ApiModelProperty(value = "执行中")
    private Integer running;

    @ApiModelProperty(value = "已取消")
    private Integer cancel;

}
