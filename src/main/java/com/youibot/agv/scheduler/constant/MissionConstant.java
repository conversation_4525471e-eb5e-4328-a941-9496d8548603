package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 15:34
 */
public class MissionConstant {

    // mission action type  RuntimeParameter
    public static final String MISSION_ACTION_PARAM_TYPE_NORMAL = "NORMAL_PARAMETER";//正常参数
    public static final String MISSION_ACTION_PARAM_TYPE_RUNTIME = "RUNTIME_PARAMETER";//运行时参数

    //mission action attribute type
    public static final String MISSION_ACTION_ATTRIBUTE_BATTERY_VALUE = "BATTERY_VALUE";//电量
    public static final String MISSION_ACTION_ATTRIBUTE_FREE_TIME = "FREE_TIME";//空闲时间
    public static final Integer RUNTIME_ACTION_TIME = 60;// 运行一个动作预计需要60s


    // mission work status
    public static final String MISSION_WORK_STATUS_CREATE = "CREATE";//创建(未执行)
    public static final String MISSION_WORK_STATUS_ASSIGNED = "ASSIGNED";//已分配状态(多机的状态)
    public static final String MISSION_WORK_STATUS_WAIT = "WAIT";//等待(继续)执行
    public static final String MISSION_WORK_STATUS_RUNNING = "RUNNING";//执行中
    public static final String MISSION_WORK_STATUS_SUCCESS = "SUCCESS";//执行成功
    public static final String MISSION_WORK_STATUS_FAULT = "FAULT";//执行错误
    public static final String MISSION_WORK_STATUS_PAUSE = "PAUSE";//已暂停
    public static final String MISSION_WORK_STATUS_BEING_PAUSE = "BEING_PAUSE";//暂停中
    public static final String MISSION_WORK_STATUS_BEING_RESUME = "BEING_RESUME";//恢复中
    public static final String MISSION_WORK_STATUS_SHUTDOWN = "SHUTDOWN";//已停止
    public static final String MISSION_WORK_STATUS_BEING_SHUTDOWN = "BEING_SHUTDOWN";//停止中
    public static final String MISSION_WORK_STATUS_WAIT_INPUT = "WAITINPUT";//等待输入

    // mission work action status.
    public static final String MISSION_WORK_ACTION_STATUS_CREATE = "CREATE";//创建
    public static final String MISSION_WORK_ACTION_STATUS_START = "START";//开始执行
    public static final String MISSION_WORK_ACTION_STATUS_RUNNING = "RUNNING";//执行中
    public static final String MISSION_WORK_ACTION_STATUS_SUCCESS = "SUCCESS";//执行成功
    public static final String MISSION_WORK_ACTION_STATUS_FAULT = "FAULT";//执行错误
    public static final String MISSION_WORK_ACTION_STATUS_SHUTDOWN = "SHUTDOWN";

    //任务链状态
    public static final String MISSION_WORK_CHAIN_STATUS_CREATE = "CREATE";//创建(未执行)
    public static final String MISSION_WORK_CHAIN_STATUS_RUNNING = "RUNNING";//执行中
    public static final String MISSION_WORK_CHAIN_STATUS_SUCCESS = "SUCCESS";//执行成功
    public static final String MISSION_WORK_CHAIN_STATUS_FAULT = "FAULT";//执行错误
    public static final String MISSION_WORK_CHAIN_STATUS_SHUTDOWN = "SHUTDOWN";//已停止

    //工作分配状态
    public static final String MISSION_WORK_ALLOCATION_STATUS_ASSIGNED = "ASSIGNED";//已分配
    public static final String MISSION_WORK_ALLOCATION_STATUS_UNASSIGNED = "UNASSIGNED";//未分配

    //创建者
    public static final String MISSION_WORK_CREATED_BY_YOUIFLEET="YOUIFleet";
    public static final String MISSION_WORK_CREATED_BY_YOUICOMPASS="YOUICompass";

    //任务呼叫状态
    public static final String MISSION_CALL_NORMAL_STATUS = "NORMAL";//正常
    public static final String MISSION_CALL_ABNORMAL_STATUS = "ABNORMAL";//异常

}
