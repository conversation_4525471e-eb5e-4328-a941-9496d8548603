package com.youibot.agv.scheduler.constant.vo;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.youibot.agv.scheduler.entity.MissionWork;

import lombok.Data;

@Data
public class MissionWorkRecordVO {

    @ExcelProperty(value = "名称", index = 0)
    private String name;

    @ExcelProperty(value = "状态", index = 1)
    private String status;

    @ExcelProperty(value = "优先级", index = 2)
    private String sequence;

    @ExcelProperty(value = "机器人", index = 3)
    private String agvCode;

    @ExcelProperty(value = "指令序号", index = 4)
    private Integer currentActionSequence;

    @ExcelProperty(value = "指令名称", index = 5)
    private String currentActionName;

    @ExcelProperty(value = "异常消息", index = 6)
    private String message;

    @ExcelProperty(value = "创建时间", index = 7)
    private Date createTime;

    @ExcelProperty(value = "开始时间", index = 8)
    private Date startTime;

    @ExcelProperty(value = "结束时间", index = 10)
    private Date endTime;

    @ExcelProperty(value = "更新时间", index = 9)
    private Date updateTime;

    public MissionWorkRecordVO(MissionWork missionWork) {
        this.name = missionWork.getName();
        this.status = missionWork.getStatus();
        if (missionWork.getSequence() == null) {
            this.sequence = null;
        } else if (missionWork.getSequence() == 1) {
            this.sequence = "低";
        } else if (missionWork.getSequence() == 2) {
            this.sequence = "普通";
        } else if (missionWork.getSequence() == 3) {
            this.sequence = "高";
        } else {
            this.sequence = "最高";
        }
        this.agvCode = missionWork.getAgvCode();
        this.currentActionName = missionWork.getCurrentActionName();
        this.currentActionSequence = missionWork.getCurrentActionSequence();
        this.message = missionWork.getMessage();
        this.createTime = missionWork.getCreateTime();
        this.updateTime = missionWork.getUpdateTime();
        this.endTime = missionWork.getEndTime();
        this.startTime = missionWork.getStartTime();
    }

}
