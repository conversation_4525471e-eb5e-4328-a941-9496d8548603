package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:21 2020/11/13
 * @Description :
 * @Modified By :
 * @Version :
 */
public class PathSimulationConstant {
    public static final int ROTATE_TYPE_ABSOLUTE = 1;   // 绝对旋转
    public static final int ROTATE_TYPE_RELATIVE = 2;   // 相对旋转

    public static final int AGV_DIRECTION_NORMAL = 1;   // AGV正向移动
    public static final int AGV_DIRECTION_REVERSE = 2;  // AGV反向移动

    public static final int PATH_SIMULATION_FINISHED = 1;   //路径仿真完成
    public static final int PATH_SIMULATION_RUNNING = 2;    //路径仿真运行中
    public static final int PATH_SIMULATION_STANDBY = 3;    //路径仿真待命中
}
