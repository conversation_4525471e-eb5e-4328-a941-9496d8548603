package com.youibot.agv.scheduler.constant;

/**
 * @author: <PERSON><PERSON><PERSON>.CHU
 * @Date: 2021/1/12 11:49
 * @Description:
 */
public final class RegexConstant {

    /**
     * 英文、数字、下划线组合的正则
     */
    public static final String CODE_REGEX = "^[0-9a-zA-Z_]{1,}$";

    public static final String CODE_ERROR_MSG = "只能是英文，数字，下划线";


    public static final String IP_REGEX = "((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)";


    //地图名称命名规则：字母或数字、下划线，字母开头
    public static final String MAP_NAME_REG = "^[a-zA-Z0-9-]{1,}[\\w]*";

}
