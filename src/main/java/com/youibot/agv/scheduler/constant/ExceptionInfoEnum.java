package com.youibot.agv.scheduler.constant;

import lombok.Getter;

/**
 * @Author：yangpeilin
 * @Date: 2020/4/27 19:32
 */
@Getter
public enum ExceptionInfoEnum {

    AGV_RETURN_CODE(40001, "AGV编码返回错误!"),
    MISSIONWORK_PAUSE(40002, "暂停任务失败!"),
    MISSIONWORK_RESUME(40003, "恢复任务失败!"),
    MISSIONWORK_STOP(40004, "停止任务失败!"),
    EXCUTE_ACTIOM(40005, "执行动作失败!"),
    PARAMS_EMPTY(40006, "非法参数"),
    VEHICLE_NOT_FREE(40007, "机器人状态不满足执行任务！");

    private Integer errorCode;

    private String message;

    ExceptionInfoEnum(Integer errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }



}
