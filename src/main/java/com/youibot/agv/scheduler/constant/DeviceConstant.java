package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR>  E-mail:shishao<PERSON>@youibot.com
 * @version CreateTime: 2021/11/4 14:39
 */
public class DeviceConstant {

    //自动门状态
    public static String AUTO_DOOR_STATUS_OPEN = "OPEN";//开启
    public static String AUTO_DOOR_STATUS_CLOSE = "CLOSE";//关闭
    public static String AUTO_DOOR_STATUS_OPERATING = "OPERATING";//操作中
    public static String AUTO_DOOR_STATUS_UNBOUND_PATH = "UNBOUND_PATH";//未绑路径
    public static String AUTO_DOOR_STATUS_ERROR = "ERROR";//通讯异常

    //门类型
    public static String DOOR_TYPE_AUTO = "AUTO_DOOR";//自动门
    public static String DOOR_TYPE_AIR_SHOWER = "AIR_SHOWER_DOOR";//风淋门

    //风淋门位置
    public static String AIR_SHOWER_DOOR_POSITION_FRONT = "FRONT";//前门
    public static String AIR_SHOWER_DOOR_POSITION_BACK = "BACK";//后门

}
