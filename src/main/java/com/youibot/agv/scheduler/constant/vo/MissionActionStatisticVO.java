package com.youibot.agv.scheduler.constant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class MissionActionStatisticVO {


    @ApiModelProperty(value = "机器人编号",position = 0)
    private String agvCode;

    @ApiModelProperty(value = "动作编号", position = 1)
    private String missionActionId;

    @ApiModelProperty(value = "动作名称", position = 2)
    private String missionActionName;

    @ApiModelProperty(value = "动作执行次数", position = 3)
    private Integer totalNum;

    @ApiModelProperty(value = "动作执行总时间 单位: 秒", position = 4)
    private Long totalTime;


}
