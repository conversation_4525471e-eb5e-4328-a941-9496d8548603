package com.youibot.agv.scheduler.listener.biz;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;
import static com.youibot.agv.scheduler.device.callbox.CallBoxCache.clientSocketMap;

import java.net.Socket;
import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.device.callbox.CallBoxUtils;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.TriggerSelector;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.TriggerSelectorService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/1/14 16:29
 * @Description: mission_work 状态变更进行业务处理
 */
@Slf4j
@Component
public class MissionWorkStatusChangeBizHandler {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private TriggerSelectorService triggerSelectorService;

	@Autowired
	private MissionService missionService;
	
    private static final String EXECUTION_RATE_100 = "100";

    /**
     * missionWork 状态为 SUCCESS、SHUTDOWN ,业务处理逻辑
     *
     * @param missionWork
     */
    public void onCompleted(MissionWork missionWork) {
        log.debug("作业完成进入业务处理逻辑>>>>{}", JSON.toJSONString(missionWork));
        try {
            //增加mission的完成次数 and missionWork的结束时间 仿真传过来的mission_id会拼上AGV编号,这里会空指针
            missionWork.setEndTime(new Date());
            missionWorkService.update(missionWork);
            //前端需要vehicle的missionWork信息
            if (defaultVehiclePool.getVehicle(missionWork.getAgvCode()) != null) {
                Vehicle vehicle = defaultVehiclePool.getVehicle(missionWork.getAgvCode());
                if (vehicle != null) {
                    vehicle.setMissionWorkId(null);
                    vehicle.setMissionName(null);
                }
            }
        } catch (Exception e) {
            log.debug("作业完成处理监听器出错:{}", e);
        }finally {
        	resetCurrent( missionWork) ;
		}

        this.triggerSelectorLogic(missionWork.getId(), missionWork.getTriggerSelectorId());


    }


	/**重置当前agv 所在的工作站指针
	 * @param missionWork
	 */
	private void resetCurrent(MissionWork missionWork) {
		Mission mission = missionService.getTjdDefaultMission();
		
		Vehicle vehicle = defaultVehiclePool.getVehicle(missionWork.getAgvCode());
		if(Objects.isNull(vehicle) || Objects.isNull(mission)) {
			log.error("currentAgv_isNULL_mission_not_configured");
			return ;
		}
		if(StringUtils.equals(mission.getName(), missionWork.getName()) && MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {
			if(StringUtils.isNotBlank(vehicle.getPreStation())) {
				vehicle.setCurrentStation(vehicle.getPreStation() );
			}else {
				/**
				 * 清空當前工作站
				 */
				vehicle.setCurrentStation( null );
			}
		}
		
	}
    private void triggerSelectorLogic(String missionWorkId, String triggerSelectorId) {
        if (StringUtils.isEmpty(triggerSelectorId)) {
            return;
        }
        TriggerSelector triggerSelector = triggerSelectorService.selectById(triggerSelectorId);
        if (triggerSelector == null) {
            return;
        }
        Integer deviceAddress = triggerSelector.getDeviceAddress();
        Integer buttonAddress = triggerSelector.getButtonAddress();

        try {
            Socket socket = clientSocketMap.get(deviceAddress);
            if (socket != null && !socket.isClosed()) {
                // 当前灯关闭
                byte[] message = CallBoxUtils.getCloseLightMessage(deviceAddress, triggerSelector.getButtonAddress());
                CallBoxUtils.write2Stream(socket.getOutputStream(), message);
                log.debug("呼叫盒创建的任务执行完成,关闭灯 设备地址=[{}],按钮地址:{},  missionWorkId=[{}]", deviceAddress, buttonAddress, missionWorkId);
            }
        } catch (Exception e) {
            log.debug("呼叫盒创建的任务执行完成,关闭灯出错:{}", e.getMessage());
        }

    }

    /**
     * MissionWork状态为 RUNNING 处理逻辑
     *
     * @param missionWork
     */
    public void onRunning(MissionWork missionWork) {

        //呼叫盒去掉已响应字段逻辑
//        try {
//            Example example = new Example(MissionCall.class);
//            example.createCriteria().andEqualTo("missionCode", missionWork.getMissionCode());
//            List<MissionCall> missionCalls = missionCallService.selectByExample(example);
//            if (!CollectionUtils.isEmpty(missionCalls)) {
//                List<String> pagerIds = missionCalls.stream().map(MissionCall::getPagerId).collect(Collectors.toList());
//                List<Pager> pagers = pagerService.selectByIds(pagerIds);
//                pagers.stream().peek(x -> x.setCallStatus(CommonConstant.HAD_RESPONSE));
//                pagerService.batchUpdate(pagers);
//            }
//        } catch (Exception ignore) {
//
//        }


    }
}
