package com.youibot.agv.scheduler.listener;

import java.util.Date;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkLog;
import com.youibot.agv.scheduler.listener.event.MissionWorkLogEvent;
import com.youibot.agv.scheduler.mapper.MissionWorkLogMapper;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/3/3 9:55
 * @Description: 监听 {@link com.youibot.agv.scheduler.listener.event.MissionWorkLogEvent} 事件
 * 记录任务状态变更事件,用于性能统计
 */
@Component
public class MissionWorkLogEventListener {

    @Autowired
    private MissionWorkLogMapper missionWorkLogMapper;

    @EventListener
    public void eventHandler(MissionWorkLogEvent missionWorkLogEvent) {
        try {
            MissionWork missionWork = missionWorkLogEvent.getMissionWork();
            MissionWorkLog missionWorkLog = buildMissionWorkLog(missionWork);
            missionWorkLogMapper.insert(missionWorkLog);
        } catch (Exception ignore) {

        }
    }

    private MissionWorkLog buildMissionWorkLog(MissionWork missionWork) {
        String agvCode = missionWork.getAgvCode();
        String status = missionWork.getStatus();
        String missionWorkId = missionWork.getId();
        MissionWorkLog missionWorkLog = new MissionWorkLog();
        missionWorkLog.setId(UUID.randomUUID().toString());
        missionWorkLog.setMissionWorkId(missionWorkId);
        missionWorkLog.setAgvCode(agvCode);
        missionWorkLog.setStartTime(new Date());
        missionWorkLog.setStatus(status);
        return missionWorkLog;
    }


}
