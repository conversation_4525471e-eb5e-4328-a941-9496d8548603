package com.youibot.agv.scheduler.listener.event;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月23日 上午11:08:42
 */
public class MissionWorkStartEvent extends ApplicationEvent {

    private static final long serialVersionUID = 2133217782269797801L;

    private MissionWork missionWork;

    public MissionWorkStartEvent(Vehicle vehicle, MissionWork missionWork) {
        super(vehicle);
        this.missionWork = missionWork;
    }

    public MissionWork getMissionWork() {
        return missionWork;
    }
}
