package com.youibot.agv.scheduler.listener;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.listener.event.MissionWorkActionStateEvent;
import com.youibot.agv.scheduler.mqtt.bean.send.ActionMessage;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_CREATED_BY_YOUIFLEET;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_ONLINE;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_MISSION_WORK_ACTION;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/9/7 11:04
 */
@Component
public class WorkActionStateEventListenerPushMqtt {

    @Autowired
    private MissionWorkService missionWorkService;

    /**
     * missionWorkAction状态变更推送对象到mqtt
     *
     * @param event
     */
    @EventListener
    public void pushMqtt(MissionWorkActionStateEvent event) throws InterruptedException {
        if (event.getSource() != null && event.getSource() instanceof MissionWorkAction) {
            MissionWorkAction missionWorkAction = (MissionWorkAction) event.getSource();
            MissionWork missionWork = missionWorkService.selectById(missionWorkAction.getMissionWorkId());
            if (missionWork != null && MISSION_WORK_CREATED_BY_YOUIFLEET.equals(missionWork.getCreatedBy())) {
                Vehicle vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
                if (SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus())) {
                    String agvCode = vehicle.getDeviceNumber();
                    String missionActionId = missionWorkAction.getMissionActionId();
                    if (!StringUtils.isEmpty(missionActionId)) {
                        if (missionActionId.contains(agvCode)) {
                            missionWorkAction.setMissionActionId(missionActionId.replace("_" + agvCode, ""));
                        }
                    }
                    ActionMessage actionMessage = new ActionMessage(missionWorkAction, agvCode);
                    MqttUtils.pushMessage(MQTT_PUBLISH_MISSION_WORK_ACTION, actionMessage, Integer.MAX_VALUE);
                }
            }
        }
    }
}
