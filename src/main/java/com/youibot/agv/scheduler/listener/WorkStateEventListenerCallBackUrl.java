package com.youibot.agv.scheduler.listener;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月23日 上午11:20:11
 */
@Component
public class WorkStateEventListenerCallBackUrl {
    private static final Logger LOGGER = LoggerFactory.getLogger(WorkStateEventListenerCallBackUrl.class);

    @Autowired
    private HttpClientService httpClientService;

    //@EventListener
    public void reCallbackUrl(MissionWorkStateEvent event) {
        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
            MissionWork missionWork = (MissionWork) event.getSource();
            try {
                Map<String, Object> param = new HashMap<>();
                param.put("missionWorkId", missionWork.getId());
                param.put("schedulePlanId", missionWork.getSchedulePlanId());
                param.put("status", missionWork.getStatus());
                httpClientService.doPost(missionWork.getCallbackUrl(), param);
            } catch (Exception e) {
                LOGGER.error("Mission work update status ,call back url error. url:", e);
            }
        }
    }

}
