package com.youibot.agv.scheduler.listener;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月23日 上午11:20:11
 */
@Component
public class WorkStateEventListenerCallBackUrl {
    private static final Logger logger = LoggerFactory.getLogger(WorkStateEventListenerCallBackUrl.class);

    @Autowired
    private HttpClientService httpClientService;

    @SuppressWarnings("unchecked")
	@EventListener
    public void reCallbackUrl(MissionWorkStateEvent event) {
        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
            if (event.getSource() != null && event.getSource() instanceof MissionWork) {
                MissionWork missionWork = (MissionWork) event.getSource();
                try {

            
                    Map<String, Object> param = new HashMap<>();
                    String runtimeParam = missionWork.getRuntimeParam();
                    if (!StringUtils.isEmpty(runtimeParam)) {
                        Map map = JSONObject.parseObject(runtimeParam, Map.class);
                        param.putAll(map);
                    }
                    param.put("missionWorkId", missionWork.getId());
                    param.put("schedulePlanId", missionWork.getSchedulePlanId());
                    param.put("agvCode", missionWork.getAgvCode());
                    param.put("status", missionWork.getStatus());
                    param.put("errorCode",missionWork.getErrorCode());
                    param.put("errorMessage",missionWork.getMessage());
                    HttpResult httpResult = httpClientService.doPost(missionWork.getCallbackUrl(), param);

                    @SuppressWarnings("rawtypes")
					HashMap map = new HashMap();
                    map.put("missionWork",missionWork);
                    map.put("param",param);
                    map.put("response",httpResult);
                    logger.debug("agvCode:[{}],event:[作业完成接口回调],回调相关数据：[{}]",missionWork.getAgvCode(),JSONObject.toJSONString(map));
                } catch (Exception e) {
                    logger.error("agvCode:[{}],event:[作业完成接口回调异常],作业数据：[{}]，异常原因：[{}]",missionWork.getAgvCode(),JSONObject.toJSONString(missionWork),e);
                }
            }
        }
    }

//    @EventListener
//    public void reCallbackUrl(MissionWorkActionStateEvent event) {
//        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
//            MissionWorkAction missionWorkAction = (MissionWorkAction) event.getSource();
//            if (LOGGER.isDebugEnabled()) {
//                LOGGER.debug(missionWorkAction.toString());
//            }
//            MissionWork missionWork = missionWorkService.selectById(missionWorkAction.getMissionWorkId());
//            try {
//                Map<String, Object> param = new HashMap<>();
//                param.put("missionWorkId", missionWork.getMissionId());
//                param.put("schedulePlanId", missionWork.getSchedulePlanId());
//                param.put("agvId", missionWork.getAgvId());
//                param.put("status", missionWork.getStatus());
//                param.put("missionWorkActionId", missionWorkAction.getId());
//                param.put("missionWorkActionType", missionWorkAction.getActionType());
//                param.put("missionActionId", missionWorkAction.getMissionActionId());
//                param.put("missionActionStatus", missionWorkAction.getStatus());
//                httpClientService.doPut(missionWork.getCallbackUrl(), param);
//            } catch (IOException e) {
//                LOGGER.error("Mission work update status ,call back url error. url:" + missionWork.getCallbackUrl());
//            }
//        }
//    }
}
