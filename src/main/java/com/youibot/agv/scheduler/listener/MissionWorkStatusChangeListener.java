package com.youibot.agv.scheduler.listener;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_RUNNING;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SUCCESS;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.listener.biz.MissionWorkStatusChangeBizHandler;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/1/14 16:14
 * @Description: mission_work 状态变更进行业务处理
 */

@Component
public class MissionWorkStatusChangeListener {

    @Autowired
    private MissionWorkStatusChangeBizHandler statusChangeBizHandler;
    
    @Order(value = Ordered.HIGHEST_PRECEDENCE )
    @Async
    @EventListener
    public void handlerBiz(MissionWorkStateEvent event) {
        MissionWork missionWork = (MissionWork) event.getSource();
        if (missionWork == null) {
            return;
        }

        String status = missionWork.getStatus();
        switch (status) {
            case MISSION_WORK_STATUS_RUNNING:
                statusChangeBizHandler.onRunning(missionWork);
                break;

            case MISSION_WORK_STATUS_SUCCESS:
            case MISSION_WORK_STATUS_SHUTDOWN:
                statusChangeBizHandler.onCompleted(missionWork);
                break;
            default:
                break;
        }
    }

}
