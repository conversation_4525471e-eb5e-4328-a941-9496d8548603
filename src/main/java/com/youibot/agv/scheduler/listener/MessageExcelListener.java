package com.youibot.agv.scheduler.listener;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.entity.MessageExcelProperty;
import com.youibot.agv.scheduler.service.AbnormalPromptService;
import com.youibot.agv.scheduler.util.ApplicationUtils;

import lombok.SneakyThrows;


public class MessageExcelListener extends AnalysisEventListener<MessageExcelProperty> {

    private AbnormalPromptService abnormalPromptService = (AbnormalPromptService) ApplicationUtils.getBean("abnormalPromptServiceImpl");

    /**
     * 自定义用于暂时存储data
     * 可以通过实例获取该值
     */
    private LinkedList<MessageExcelProperty> messageExcelProperties = new LinkedList<>();

    public MessageExcelListener(MessageExcelProperty messageExcelProperty) {
    }

    public MessageExcelListener() {
    }


    /**
     * 这个每一条数据解析都会来调用
     */
    @Override
    public void invoke(MessageExcelProperty messageExcelProperty, AnalysisContext analysisContext) {
        //校验属性是否为空
        validAbnormalParameter(messageExcelProperty);
        messageExcelProperties.add(messageExcelProperty);
    }

    private void validAbnormalParameter(MessageExcelProperty messageExcelProperty) {
        Optional.ofNullable(messageExcelProperty.getAbnormalLevel()).orElseThrow(ExcelAnalysisException::new);
        Optional.ofNullable(messageExcelProperty.getAbnormalType()).orElseThrow(ExcelAnalysisException::new);
        Optional.ofNullable(messageExcelProperty.getAbnormalCode()).orElseThrow(ExcelAnalysisException::new);
        Optional.ofNullable(messageExcelProperty.getAbnormalDescription()).orElseThrow(ExcelAnalysisException::new);
        Optional.ofNullable(messageExcelProperty.getHelp()).orElseThrow(ExcelAnalysisException::new);
    }

    /**
     * 校验表头是否与模板一致
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if (headMap != null && headMap.size() == 5) {
            if (!"等级".equals(headMap.get(0)) || !"类型".equals(headMap.get(1))
                    || !"异常码".equals(headMap.get(2)) || !"描述".equals(headMap.get(3)) || !"处理建议".equals(headMap.get(4))) {
                throw new ExcelAnalysisException();
            }
        } else {
            throw new ExcelAnalysisException();
        }

    }

    /**
     * 异常统一处理
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) {
        throw new ExcelAnalysisException("导入excel格式不符合，请下载标准模板后再重新导入！（注意：内容均不能为空，异常码类型仅支持整型！）");
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param analysisContext
     */
    @SneakyThrows
    @Override
    @Transactional
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        List<MessageExcelProperty> messageExcelPropertyList = new ArrayList<>();
        List<Integer> codeListFromExcel = new ArrayList<>();
        messageExcelProperties.forEach(obj -> {
            //Excel表中所有异常信息
            messageExcelPropertyList.add(obj);
            //Excel表中所有异常编码
            codeListFromExcel.add(obj.getAbnormalCode());
        });
        //获取数据库中所有异常编码
        List<Integer> codeListFromBase = abnormalPromptService.getAbnormalCodeList();
        //比较是否有重复的异常编码
        compareAbnormalCode(codeListFromExcel, codeListFromBase);
        //新增异常信息
        insertAbnormal(messageExcelPropertyList);
    }

    private void insertAbnormal(List<MessageExcelProperty> messageExcelPropertyList) {
        messageExcelPropertyList.forEach(obj -> {
            AbnormalPrompt abnormalPrompt = new AbnormalPrompt();
            if ("普通".equals(obj.getAbnormalLevel())) {
                abnormalPrompt.setAbnormalLevel(1);
            } else if ("警告".equals(obj.getAbnormalLevel())) {
                abnormalPrompt.setAbnormalLevel(2);
            } else {
                abnormalPrompt.setAbnormalLevel(3);
            }
            abnormalPrompt.setAbnormalCode(obj.getAbnormalCode());
            abnormalPrompt.setAbnormalType(obj.getAbnormalType());
            abnormalPrompt.setAbnormalDescription(obj.getAbnormalDescription());
            abnormalPrompt.setHelp(obj.getHelp());
            abnormalPromptService.insert(abnormalPrompt);
        });
    }

    private void compareAbnormalCode(List<Integer> codeListForExcel, List<Integer> codeListFromBase) {
        //1.比较excel中异常码是否有重复
        List<Integer> duplicateList = codeListForExcel.stream()
                .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(duplicateList) && duplicateList.size() > 0) {
            throw new ExcelAnalysisException("Excel中异常码不可重复，重复的异常码为：" + duplicateList.toString());
        }
        //2.比较excel和数据库的异常码是否有重复
        codeListForExcel.forEach(code -> {
            if (codeListFromBase.contains(code)) {
                duplicateList.add(code);
            }
        });
        if (!CollectionUtils.isEmpty(duplicateList) && duplicateList.size() > 0) {
            throw new ExcelAnalysisException("Excel中异常码与数据库中异常码有重复，重复的异常码为：" + duplicateList.toString());
        }
    }
}
