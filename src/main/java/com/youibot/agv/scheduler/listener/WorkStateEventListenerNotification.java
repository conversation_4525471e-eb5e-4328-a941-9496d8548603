package com.youibot.agv.scheduler.listener;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;
import static com.youibot.agv.scheduler.constant.NotificationConstant.UNREAD;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.Notification;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.AbnormalPromptService;
import com.youibot.agv.scheduler.service.NotificationService;

/**
 * <AUTHOR>
 * @Date :Created in 上午11:30 2020/1/3
 * @Description :
 * @Modified By :
 * @Version :
 */
@Component
public class WorkStateEventListenerNotification {
    private static final Logger logger = LoggerFactory.getLogger(WorkStateEventListenerNotification.class);

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private AGVService agvService;

    @Autowired
    private AbnormalPromptService abnormalPromptService;

    //@EventListener
    public void eventNotification(MissionWorkStateEvent event) {
        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
            MissionWork missionWork = (MissionWork) event.getSource();
            try {

           

                if (missionWork.getStatus().equals(MISSION_WORK_STATUS_FAULT)) {
                    AbnormalPrompt abnormalByCode;
                    Notification notification = new Notification();
                    //根据异常码获取异常信息
                    if (null != missionWork.getErrorCode()) {
                        abnormalByCode = abnormalPromptService.getAbnormalByCode(missionWork.getErrorCode());
                        if (abnormalByCode != null) {
                            notification.setScale(abnormalByCode.getAbnormalLevel());
                            notification.setType(abnormalByCode.getAbnormalType());
                            notification.setDescription(abnormalByCode.getAbnormalDescription());
                            notification.setHelp(abnormalByCode.getHelp());
                        } else {
                            notification.setDescription("消息管理中找不到异常码" + missionWork.getErrorCode() + "的配置信息");
                        }
                    }
                    notification.setErrorCode(missionWork.getErrorCode());
                    notification.setAgvCode(missionWork.getAgvCode());
                    notification.setAgvName(agvService.getAGVName(missionWork.getAgvCode()));
                    notification.setMissionWorkId(missionWork.getId());
                    notification.setMissionWorkName(missionWork.getName());
                    notification.setHaveRead(UNREAD);
                    notificationService.sendMessage(notification);

                    logger.debug("agvCode:[{}],event:[作业状态异常],content:[{}]",missionWork.getAgvCode(), JSONObject.toJSONString(notification));
                }
            } catch (Exception e) {
                logger.error("agvCode:[{}],event:[作业状态异常发送提醒异常],异常原因:[{}]",missionWork.getAgvCode(), e);
            }
        }
    }
}
