package com.youibot.agv.scheduler.listener;

import com.youibot.agv.scheduler.constant.NotificationConstant;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.Notification;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.service.NotificationService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;
import static com.youibot.agv.scheduler.constant.NotificationConstant.*;

/**
 * <AUTHOR>
 * @Date :Created in 上午11:30 2020/1/3
 * @Description :
 * @Modified By :
 * @Version :
 */
@Component
public class WorkStateEventListenerNotification {
    private static final Logger LOGGER = LoggerFactory.getLogger(WorkStateEventListenerNotification.class);

    @Autowired
    private NotificationService notificationService;

    @EventListener
    public void eventNotification(MissionWorkStateEvent event) {
        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
            MissionWork missionWork = (MissionWork) event.getSource();
            try {
                if (missionWork.getStatus().equals(MISSION_WORK_STATUS_FAULT)) {
                    Notification notification = new Notification();
                    notification.setMissionWorkId(missionWork.getId());
                    notification.setMissionWorkName(missionWork.getName());
                    notification.setHaveRead(UNREAD);
                    notification.setScale(ERROR_SCALE);
                    notification.setType(MISSION_ERROR);
                    notification.setDescription(MessageUtils.getMessage("notification.mission_work_error") + missionWork.getName());
                    notification.setMode(NotificationConstant.LOCAL_MODE);
                    notificationService.sendMessage(notification);
                }
            } catch (Exception e) {
                LOGGER.error("mission work status notification error.", e);
            }
        }
    }
}
