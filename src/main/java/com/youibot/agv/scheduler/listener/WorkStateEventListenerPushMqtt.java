package com.youibot.agv.scheduler.listener;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.mqtt.bean.send.MissionWorkMessage;
import com.youibot.agv.scheduler.mqtt.bean.send.PositionMessage;
import com.youibot.agv.scheduler.mqtt.listener.PositionMessagePushListener;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_ASSIGNED;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_ONLINE;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_MISSION_WORK;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_POSITION;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/9/7 11:04
 */
@Component
public class WorkStateEventListenerPushMqtt {

    @Autowired
    private PositionMessagePushListener positionMessagePushListener;

    /**
     * missionWork状态变更推送对象到mqtt
     *
     * @param event
     */
    @EventListener
    public void pushMqtt(MissionWorkStateEvent event) throws InterruptedException {
        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
            MissionWork missionWork = (MissionWork) event.getSource();
            if (!StringUtils.isEmpty(missionWork.getAgvId())) {
                Vehicle vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
                if (SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus()) && !MISSION_WORK_STATUS_ASSIGNED.equals(missionWork.getStatus())) {
                    //状态变更前先推送一次位置数据
                    //PositionMessage positionMessage = new PositionMessage();
                    //positionMessage.setValue(vehicle);
                    //MqttUtils.pushMessage(MQTT_PUBLISH_POSITION, positionMessage);
                    positionMessagePushListener.positionMessagePush(vehicle.getDeviceNumber());
                    String agvCode = vehicle.getDeviceNumber();
                    String missionId = missionWork.getMissionId();
                    if (!StringUtils.isEmpty(missionId)) {
                        if (missionId.contains(agvCode)) {
                            missionWork.setMissionId(missionId.replace("_" + agvCode, ""));
                        }
                    }
                    MissionWorkMessage missionWorkMessage = new MissionWorkMessage(missionWork, vehicle.getDeviceNumber());
                    MqttUtils.pushMessage(MQTT_PUBLISH_MISSION_WORK, missionWorkMessage);
                }
            }
        }
    }

}
