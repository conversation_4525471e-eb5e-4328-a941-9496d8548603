package com.youibot.agv.scheduler.listener.event;

import com.google.common.collect.Maps;
import com.youibot.agv.scheduler.entity.Notification;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage.ReportType;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.BraceletMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.ChargeEventsMsg;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.ChargeWashMessage;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Map;
import java.util.Objects;


/**
  * @ClassName: NotifyMessageEvent
  * @Description: 消息产生事件
  * <AUTHOR>
  * @date 2022年3月31日 下午3:49:48
  *
  */
@EqualsAndHashCode(callSuper=false)
@Data
public class NotifyMessageEvent extends ApplicationEvent {

   public static ApplicationEventPublisher  applicationEventPublisher;
   
	   /**
	 * 当前是正常状态: 0 
	 */
    public  static Map<String, Integer>  isNoraml= Maps.newConcurrentMap();
	/**
	  * @Fields serialVersionUID : 序列化
	  */
	private static final long serialVersionUID = 1L;

	/**
	 * true: alert ,false : event 
	 */
	private boolean alert = true;
	/**
	 * 上报event类型
	 */
	private ReportType reportType;
	
	 /**
     * 充电events 时有值
     */
	private ChargeEventsMsg chargeEventsMsg;
	/**
	 * 充电清洗
	 */
	private ChargeWashMessage chargeWashMessage;

	private BraceletMessage braceletMessage;
	
	public NotifyMessageEvent(Notification source) {
		super(source);
	}

	/**构造事件源
	 * @return
	 */
	public static NotifyMessageEvent getInstance( String agvCode) {
		Notification item = new Notification();
		item.setAgvCode(agvCode);
		NotifyMessageEvent result = new NotifyMessageEvent(item) ;
		result.setAlert(false);
		return result ;
		
	}
	
	/**发布alert
	 * @param source
	 */
	public static void publishAlert(Notification source) {
		 get().publishEvent(  new NotifyMessageEvent( source ) );
	}
	
	/**上报事件
	 * @param agvCode
	 * @param reportType
	 */
	public static void publish(String agvCode, ReportType reportType ) {
	    /**
         * 离线事件上报
         */
        NotifyMessageEvent instance = getReportNotify(agvCode, reportType);
        get().publishEvent( instance );
	}

	/**获得上报事件
	 * @param agvCode
	 * @param reportType
	 * @return
	 */
	public static NotifyMessageEvent getReportNotify(String agvCode, ReportType reportType) {
		NotifyMessageEvent instance = NotifyMessageEvent.getInstance( agvCode);
        instance.setReportType( reportType );
		return instance;
	}
	
	/**获得事件发布器
	 * @return
	 */
	public static ApplicationEventPublisher get() {
		
		if(Objects.isNull(applicationEventPublisher)) {
			
			applicationEventPublisher = ApplicationUtils.getBean( ApplicationEventPublisher.class);
		}
		return applicationEventPublisher;
	}
	
	
}
