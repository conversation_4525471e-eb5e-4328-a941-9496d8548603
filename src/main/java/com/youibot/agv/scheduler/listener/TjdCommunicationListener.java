package com.youibot.agv.scheduler.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Uninterruptibles;
import com.shimizukenta.secs.SecsException;
import com.shimizukenta.secs.SecsMessage;
import com.shimizukenta.secs.SecsSendMessageException;
import com.shimizukenta.secs.SecsWaitReplyMessageException;
import com.shimizukenta.secs.ext.annotation.SecsMsgListener;
import com.shimizukenta.secs.ext.annotation.Sf;
import com.shimizukenta.secs.ext.config.AbstractSecsMsgListener;
import com.shimizukenta.secs.hsms.HsmsCommunicator;
import com.shimizukenta.secs.secs2.Secs2;
import com.shimizukenta.secs.secs2.Secs2Exception;
import com.shimizukenta.secs.secs2.Secs2List;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.constant.TjdCxt.EventId;
import com.youibot.agv.scheduler.constant.TjdCxt.EventId.Alert;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.listener.event.NotifyMessageEvent;
import com.youibot.agv.scheduler.listener.event.ReportSidePathDataEvent;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage.ReportType;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.BraceletMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.ChargeEventsMsg;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.ChargeEventsMsg.Phrase;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.ChargeWashMessage;
import com.youibot.agv.scheduler.mqtt.service.VehicleCommandService;
import com.youibot.agv.scheduler.param.TjdScreenItemDto;
import com.youibot.agv.scheduler.param.vo.TjdBinChangeDTO;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.ListOrderedMap;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;

/**
 * 台积电项目mes 系统通讯处理类
 *
 * <AUTHOR>
 */
@EnableAspectJAutoProxy(proxyTargetClass = true)
@Slf4j
@SecsMsgListener
public class TjdCommunicationListener extends AbstractSecsMsgListener {

    private static final String BIN_NO = "binNo";

    private static final String SEP = "_";

    private static final String SUCCESS_STR = "SUCCESS";

    private static final String NOT_FOUND = "2";

    private static final String ON_LIINE = "0";

    private static final String OFF_LIINE = "1";

    private static final String FAIL = "1";

    private static final String SUCCESS = "0";

    private static Cache<Object, AtomicInteger> SCREEN_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(2, TimeUnit.MINUTES).build();

    private static volatile boolean runing = true;


    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private VehiclePoolService vehiclePoolService;


    @Autowired
    private LogoInfoService logoInfoService;

    @Autowired
    private WorkCycleConfigService workCycleConfigService;

    @Autowired
    private VehicleCommandService vehicleCommandService;

    @Autowired
    private HttpClientService httpClientService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private AbnormalPromptService abnormalPromptService;


    @Autowired
    private PathService pathService;

    @Autowired
    private VehicleControlService vehicleControlService;

    @Autowired
    private SchedulerConfigService schedulerConfigService;


    /**
     * @param @param event 设定文件
     * @return void 返回类型
     * @throws
     * @Title: handleInit
     * @Description: MES系统初始握手
     */
    @Sf(s = 1, f = 1)
    public void msgHandleInit(SecsMessage event) {

        try {

            String softName = "youifleet";
            String version = "v1.0.0";

            LogoInfo logoInfo = logoInfoService.getDefaultLogoInfo();

            if (Objects.nonNull(logoInfo)) {
                String versionSoftName = logoInfo.getVersion();
                String[] split = StringUtils.split(versionSoftName, " ");
                softName = split[0];
                if (ArrayUtils.getLength(split) >= 2) {
                    version = split[1];
                }
            }
            reply(event, Secs2.list(
                    /**
                     * SoftName
                     */
                    Secs2.ascii(softName) /* 1 */
                    /**
                     * Software Version
                     */
                    , Secs2.ascii(version)

            ));

        } catch (SecsException | InterruptedException e) {
            log.error("error:{}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * @param @param event 设定文件
     * @return void 返回类型
     * @throws
     * @Title: handleAgvStationQuery
     * @Description: mes 系统查询 agv 和工作站
     */
    @Sf(s = 2, f = 13)
    private void msgHandleAgvStationQuery(SecsMessage event) {

        log.info("handler_get_event:{}", event);

        try {
            Secs2 msg = event.secs2();
            int size = msg.size();
            String markTag = (size < 1) ? StringUtils.EMPTY : msg.getAscii(0);
            List<String> tagIds = getTagIds(msg);
            List<Secs2List> totalList = Lists.newArrayList();
            if (TjdCxt.isAgv(markTag)) {
                List<Secs2List> list = handleAgv(tagIds);
                totalList.addAll(list);
            }
            if (TjdCxt.isStation(markTag)) {
                List<Secs2List> list = handleStation(tagIds);
                totalList.addAll(list);
            }
            log.debug("tag:{}, tagId:{}", markTag, tagIds);

            if (CollectionUtils.isEmpty(totalList)) {
                super.reply(event, false, Secs2.list(Secs2.ascii("no data found")));
            } else {
                super.reply(event, false, Secs2.list(totalList));
            }

        } catch (Exception e) {

            log.error("error:{}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * agv / station status: 0 online 1 offline
     *
     * @param @param event 设定文件
     * @return void 返回类型
     * @throws
     * @Title: msgHandleAgvStationUpdate
     * @Description: 处理agv 或者站点状态更新
     */
    @Sf(s = 2, f = 15)
    private void msgHandleAgvStationUpdate(SecsMessage event) {

        LinkedMultiValueMap<String, String> map = new LinkedMultiValueMap<>();

        Secs2 data = event.secs2();

        data.forEach(item -> {

            try {
                map.add(item.getAscii(1), item.getAscii(0));
            } catch (Secs2Exception e) {
                log.error("error:{}", Throwables.getStackTraceAsString(e));
            }
        });
        try {
            List<String> allIds = map.values().parallelStream().flatMap(i -> i.stream()).collect(Collectors.toList());
            String tipMsg = "";
            if (CollectionUtils.isNotEmpty(allIds)) {
                List<String> offLineIds = map.get(OFF_LIINE);
                List<Vehicle> vehicles = defaultVehiclePool.getVehicles(allIds);
                vehicles.forEach(agv -> {
                    if (offLineIds.contains(agv.getId())) {

                        vehicleControlService.linecePatro(agv.getId(), false);
                    } else {
                        vehicleControlService.linecePatro(agv.getId(), true);

                    }

                });

                ListOrderedMap<String, WorkCycleConfig> cycle = TjdCxt.getCycle();

                List<WorkCycleConfig> list = Lists.newArrayList();
                for (String id : allIds) {
                    WorkCycleConfig cycleConfig = cycle.get(id);
                    if (Objects.nonNull(cycleConfig)) {
                        if (offLineIds.contains(id)) {
                            cycleConfig.setEnabled(false);
                        } else {
                            cycleConfig.setEnabled(true);
                        }

                        list.add(cycleConfig);
                    }

                }
                if (CollectionUtils.isNotEmpty(list)) {

                    workCycleConfigService.batchUpdate(list);
                }
                if (CollectionUtils.isEmpty(list) && CollectionUtils.isEmpty(vehicles)) {

                    tipMsg = "update agvId or stationId can't be null , agvid and stationId not conrrect";
                }

            } else {
                tipMsg = "update agvId or stationId can't be null";
            }

            reply(event, Secs2.list(Secs2.ascii(StringUtils.isBlank(tipMsg) ? SUCCESS : FAIL),

                    Secs2.ascii(StringUtils.isBlank(tipMsg) ? SUCCESS_STR : tipMsg)));

        } catch (SecsException | InterruptedException e) {
            log.error("error:{}", Throwables.getStackTraceAsString(e));
            try {
                reply(event, Secs2.list(Secs2.ascii(FAIL), Secs2.ascii(Throwables.getStackTraceAsString(e))));
            } catch (SecsException | InterruptedException e1) {
                log.error("error:{}", Throwables.getStackTraceAsString(e));
            }
        }
    }

    /**
     * @param @param event 设定文件
     * @return void 返回类型
     * @throws
     * @Title: releaseGoods
     * @Description: mes系统给货架下货指令
     */
    @Sf(s = 2, f = 49)
    public void msgReleaseGoods(SecsMessage event) {
        String errorMsg = "";
        log.debug(" releaseGoods:{}", event);
        try {
            Secs2 msg = event.secs2();

            String workStationId = msg.getAscii(0);
            String agvId = msg.getAscii(1);
            String cmd = msg.getAscii(2);
            EventId.CMD cmd1 = EventId.CMD.getCmd(cmd);
            if (Objects.nonNull(cmd1) && !EventId.CMD.TRANSFER.equals(cmd1)) {
                log.debug("cmd1:{},agv:{}", cmd1.name(), agvId);
                handleCmd(event, cmd1, agvId);
                return;
            }
            Vehicle vehicle = defaultVehiclePool.getVehicle(agvId);

            if (Objects.isNull(vehicle)) {
                errorMsg = "agvId not exist";
                log.error("agvId not exist, agvId:{}, station:{}, 未做到站数据上报", agvId, workStationId);
                updateMsg(event, errorMsg);
                return;
            }

            Set<TjdScreenItemDto> refreshData = Sets.newConcurrentHashSet();
            Secs2 tranferDown = msg.get(3, 0);

            Secs2 tranferUp = msg.get(3, 1);
            /**
             * 处理下料
             */
            Pair<Set<String>, Set<TjdScreenItemDto>> tranferDownHandle = handleTranfer(vehicle, tranferDown,
                    refreshData, true);
            /**
             * 处理上料
             */
            Pair<Set<String>, Set<TjdScreenItemDto>> tranferUpHandle = handleTranfer(vehicle, tranferUp, refreshData,
                    false);

            refreshData = refreshData.parallelStream().map(item -> {
                /**
                 * 下料的需要闪烁
                 */
                if (tranferDownHandle.getKey().contains(item.getLotId())) {
                    item.setFlag(1);
                }
                return item;
            }).collect(Collectors.toSet());

            /**
             * 电量不足,只能进行下料
             */
            if (vehicle.getUnLoadOnly()) {
                errorMsg = errorMsg + "  power is not full";
                vehicle.setUnLoadOnly(true);
                vehicle.setToUp(Sets.newHashSet());
            }

            updateMsg(event, errorMsg);
            /**
             * 发布厢位上下料信息
             */
            AgvArrivedErackInfoMessage publishMsg = AgvArrivedErackInfoMessage.builder()
                    .toDown(tranferDownHandle.getRight()).toUp(tranferUpHandle.getRight()).currentShelfData(refreshData)
                    .unLoadOnly(vehicle.getUnLoadOnly()).mesReturn(true).currentStation(workStationId)
                    .reportType(ReportType.PRE_ENTER).build();
            vehicleCommandService.publishShelfBins(agvId, publishMsg);
        } catch (Exception e) {
            log.error("error_detail:{}", Throwables.getStackTraceAsString(e));
            errorMsg = "handled_failed";
            try {
                updateMsg(event, errorMsg);
            } catch (SecsException | InterruptedException e1) {
                log.error("error:{}", e);
            }
        }
    }

    private void handleCmd(SecsMessage event, EventId.CMD cmd1, String agvId) {
        String errorMsg = null;
        try {

            if (EventId.CMD.ALT.equals(cmd1)) {
                vehicleCommandService.uTurn(agvId, false, false, true, true);
            }

            if (EventId.CMD.PRIMARY.equals(cmd1)) {
                vehicleCommandService.uTurn(agvId, false, false, true, false);
            }
            if (EventId.CMD.RELEASE.equals(cmd1)) {
                vehicleCommandService.uTurn(agvId, true, false, true, false);
            }
            if (EventId.CMD.RELEASE_ALT.equals(cmd1)) {
                vehicleCommandService.uTurn(agvId, true, false, true, true);
            }

            if (EventId.CMD.RELEASE_PRIMARY.equals(cmd1)) {
                vehicleCommandService.uTurn(agvId, true, false, true, false);
            }

        } catch (Exception e) {

            errorMsg = Throwables.getStackTraceAsString(e);
            log.error("error:{}", errorMsg);
            errorMsg = "Pls check currentStation";
        }
        try {
            updateMsg(event, errorMsg);
        } catch (Exception e) {
            log.error("errorMsg:{}", Throwables.getStackTraceAsString(e));
        }
    }

    public void updateMsg(SecsMessage event, String errorMsg)
            throws SecsSendMessageException, SecsWaitReplyMessageException, SecsException, InterruptedException {
        super.reply(event, Secs2.list(
                        /**
                         * Retrun Code 0:OK 1:Fail
                         */
                        Secs2.ascii((StringUtils.isBlank(errorMsg) ? SUCCESS : FAIL)) /* 0 */
                        /**
                         * 报错原因
                         */
                        , Secs2.ascii(StringUtils.isBlank(errorMsg) ? "" : errorMsg) /* 1 */

                )

        );
    }

    @Sf(s = 2, f = 41)
    public void casseteIn(SecsMessage event) {

        try {
            log.debug(" casseteIn:{}", event);
            super.reply(event, Secs2.empty());
            TjdScreenItemDto dto = new TjdScreenItemDto();
            Secs2 secs2 = event.secs2();
            Secs2 data = secs2.get(1);
            String agvId = null;
            for (int i = 1; i <= data.size(); i++) {
                Set<Field> set = TjdScreenItemDto.filedMap.get(i);

                if (CollectionUtils.isNotEmpty(set)) {
                    for (Field field : set) {
                        Secs2 item = data.get(i - 1);
                        String itemValue = item.getAscii(1);
                        try {
                            if (StringUtils.equals(itemValue, SEP)) {
                                continue;
                            }
                            if (StringUtils.equals(field.getName(), BIN_NO)) {
                                String[] split = StringUtils.split(itemValue, SEP);
                                if (ArrayUtils.getLength(split) == 2) {
                                    agvId = split[0];
                                    itemValue = dto.getBackBin(split[1]);
                                }
                            }
                            field.set(dto, itemValue);
                        } catch (IllegalArgumentException | IllegalAccessException e) {
                            log.error("error:{}", Throwables.getStackTraceAsString(e));
                        }
                    }

                }

            }

            // 向compass 下发储位刷新指令
            if (StringUtils.isNotBlank(agvId)) {
                Vehicle vehicle = defaultVehiclePool.getVehicle(agvId);
                if (Objects.nonNull(vehicle)) {
                    AgvArrivedErackInfoMessage publishMsg = AgvArrivedErackInfoMessage.builder()
                            .currentShelfData(Sets.newHashSet(dto)).unLoadOnly(vehicle.getUnLoadOnly()).mesReturn(false)
                            .reportType(ReportType.QUERY).build();
                    vehicleCommandService.publishShelfBins(agvId, publishMsg);
                }

            }

        } catch (Secs2Exception | SecsException | InterruptedException e) {
            log.error("error:{}", Throwables.getStackTraceAsString(e));
        }
    }


    /**
     * 关闭所有充电充电桩
     *
     * @param event
     */
    @Sf(s = 1, f = 4)
    public void msgUpdateGoods(SecsMessage event) {
        log.debug("updateData:{}", event.secs2());

        try {
            httpClientService.doGet(TjdCxt.CHARGE_STOP_URL);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }


    /**
     * 关闭所有充电充电桩
     *
     * @param event
     */
    @Sf(s = 1, f = 5)
    public void msgAgvIsFiring(SecsMessage event) {
        log.debug("msgAgvIsFiring:{}", event.secs2());

        Secs2 msg = event.secs2();

        if (Objects.nonNull(msg) && !msg.isEmpty()) {
            Secs2 secs2Item = msg.get();
            if (Objects.isNull(secs2Item) || secs2Item.size() == 0) {
                clearChargeAgvFire(event);
            } else {
                agvFireLogic(event, msg);
            }

        } else {
            clearChargeAgvFire(event);
        }


    }

    /**
     * agv 着火的处理逻辑
     *
     * @param event
     * @param msg
     */
    public void agvFireLogic(SecsMessage event, Secs2 msg) {
        TjdCxt.IS_AGV_FIRING = true;
        TjdCxt.FIRE_RETAKE.put(TjdCxt.IS_AGV_FIRING_KEY, TjdCxt.IS_AGV_FIRING);

        msg.forEach(item -> {
            try {
                String agvCode = item.getAscii();
                TjdCxt.FIRE_RETAKE_AGVS.add(agvCode);
            } catch (Secs2Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        });

        try {
            super.reply(event, Secs2.ascii("stop_charge_handle success"));
        } catch (SecsException | InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * agv 着火清除的处理
     *
     * @param event
     */
    public void clearChargeAgvFire(SecsMessage event) {
        TjdCxt.IS_AGV_FIRING = false;
        TjdCxt.FIRE_RETAKE.invalidate(TjdCxt.IS_AGV_FIRING_KEY);
        TjdCxt.FIRE_RETAKE.cleanUp();
        try {
            super.reply(event, Secs2.ascii("enable_charge_handle success"));
        } catch (SecsException | InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * @param @param  msg
     * @param @return 设定文件
     * @return List<String> 返回类型
     * @throws
     * @Title: getTagIds
     * @Description: 获得工作站或者agvid 列表
     */
    private List<String> getTagIds(Secs2 msg) {
        List<String> list = Lists.newArrayList();
        for (int i = 0; i < msg.size(); i++) {
            if (i != 0) {
                try {
                    list.add(msg.get(i).getAscii());
                } catch (Secs2Exception e) {
                    log.error("error_msg:{}", Throwables.getStackTraceAsString(e));
                }
            }

        }
        return list;
    }

    /**
     * @param @param  tagId
     * @param @return 设定文件
     * @return List<Secs2List> 返回类型
     * @throws
     * @Title: handleStation
     * @Description: 处理站点相关消息
     */
    private List<Secs2List> handleStation(List<String> tagIds) {
        List<Secs2List> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(tagIds)) {
            ListOrderedMap<String, WorkCycleConfig> cycle = TjdCxt.getCycle();
            list = cycle.valueList().stream().map(getStationInfo()).collect(Collectors.toList());
        } else {
            ListOrderedMap<String, WorkCycleConfig> cycle = TjdCxt.getCycle();
            for (String tagId : tagIds) {
                WorkCycleConfig config = cycle.get(tagId);
                if (Objects.nonNull(config)) {
                    Secs2List apply = getStationInfo().apply(config);
                    list.add(apply);
                } else {
                    /**
                     * 无法查询到站点
                     */
                    list.add(notFoundMsg(tagId));
                }
            }

        }
        return list;
    }

    /**
     * @param @param  tagId
     * @param @return 设定文件
     * @return Secs2List 返回类型
     * @throws
     * @Title: notFoundMsg
     * @Description: 查询不到站点或者agv 的返回数据
     */
    private Secs2List notFoundMsg(String tagId) {
        return Secs2.list(
                /**
                 * agvId 或者stationId
                 */
                Secs2.ascii(tagId),
                /**
                 * agvName或者stationName
                 */
                Secs2.ascii(""),
                /**
                 * agvStatus 或者stationStatus AGV Status 0:online 1:offline 2 not exist
                 */
                Secs2.ascii(NOT_FOUND)

        );
    }

    /**
     * @param @param  tagId
     * @param @return 设定文件
     * @return List<Secs2List> 返回类型
     * @throws
     * @Title: handleAgv
     * @Description: agv 数据信息发送出去
     */
    private List<Secs2List> handleAgv(List<String> tagIds) {
        List<Secs2List> list = Lists.newArrayList();

        if (CollectionUtils.isEmpty(tagIds)) {
            List<Vehicle> all = defaultVehiclePool.getAll();
            list = all.parallelStream().map(getVehicleInfo()).collect(Collectors.toList());
        } else {

            for (String tagId : tagIds) {
                Vehicle vehicle = defaultVehiclePool.getVehicle(tagId);
                if (Objects.nonNull(vehicle)) {

                    Secs2List listItem = getVehicleInfo().apply(vehicle);
                    list.add(listItem);
                } else {
                    /**
                     * 无法查询到agv
                     */
                    list.add(notFoundMsg(tagId)

                    );

                }

            }

        }

        return list;

    }

    /**
     * @param @return 设定文件
     * @return Function<? super Vehicle, ? extends Secs2List> 返回类型
     * @throws
     * @Title: getVehicleInfo
     * @Description: 获得车辆lot信息
     */
    private Function<? super WorkCycleConfig, ? extends Secs2List> getStationInfo() {
        return item -> {

            /**
             * 所有的电子货架
             */

            return Secs2.list(
                    /**
                     * AGVID
                     */
                    Secs2.ascii(item.getId()),
                    /**
                     * AGVNAME
                     */
                    Secs2.ascii(item.getStationName()),
                    /**
                     * GV Status 0:online 1:offline 2 not exist
                     */
                    Secs2.ascii(item.getEnabled() ? ON_LIINE : OFF_LIINE)

            );

        };
    }

    /**
     * @param @return 设定文件
     * @return Function<? super Vehicle, ? extends Secs2List> 返回类型
     * @throws
     * @Title: getVehicleInfo
     * @Description: 获得车辆lot信息
     */
    private Function<? super Vehicle, ? extends Secs2List> getVehicleInfo() {
        return item -> {

            Collection<TjdScreenItemDto> shelfData = item.getShelfData().values();
            /**
             * 所有的货物
             */
            List<Secs2List> lotIds = shelfData.parallelStream().map(getShelfData()).collect(Collectors.toList());

            return Secs2.list(
                    /**
                     * AGVID
                     */
                    Secs2.ascii(item.getId()),
                    /**
                     * AGVNAME
                     */
                    Secs2.ascii(item.getName()),
                    /**
                     * AGV Status 0:online 1:offline 2 not exist
                     */
                    Secs2.ascii(item.getLinePatrolMode() ? ON_LIINE : OFF_LIINE),
                    /**
                     * Lot Info
                     */
                    Secs2.list(lotIds)

            );

        };
    }

    /**
     * @param @return 设定文件
     * @return Function<? super TjdScreenItemDto, ? extends Secs2List> 返回类型
     * @throws
     * @Title: getShelfData
     * @Description: 货架上的数据转化为secs 消息
     */
    private Function<? super TjdScreenItemDto, Secs2List> getShelfData() {
        return shelfItem -> {

            return Secs2.list(
                    /**
                     * 厢位编号
                     */
                    Secs2.ascii(TjdScreenItemDto.getIndex(shelfItem.getBinNo())),
                    /**
                     * 物料lot
                     */
                    Secs2.ascii(shelfItem.getLotId())

            );

        };
    }

    /**
     * @param @param  message
     * @param @param  tranfer
     * @param @return
     * @param @throws Secs2Exception 设定文件
     * @return Set<String> 返回类型
     * @throws
     * @Title: handleTranfer
     * @Description: 处理上下料, 同时下料的时候返回对应的下料lotId
     */
    private Pair<Set<String>, Set<TjdScreenItemDto>> handleTranfer(Vehicle vehicle, Secs2 tranfer,
                                                                   Set<TjdScreenItemDto> refreshData, boolean isDown) throws Secs2Exception {
        final Set<TjdScreenItemDto> operateData = Sets.newConcurrentHashSet();

        Set<String> res = Sets.newConcurrentHashSet();
//		String command = tranfer.getAscii(0);
        Secs2 transferItems = tranfer.get(1, 0);
        transferItems.forEach(item -> {

            TjdScreenItemDto itemDto = commandToScreen().apply(item);
            if (isDown) {
                itemDto.setFlag(1);
            }
            res.add(itemDto.getLotId());
            refreshData.add(itemDto);
            operateData.add(itemDto);

        });

        Pair<Set<String>, Set<TjdScreenItemDto>> of = Pair.of(res, operateData);
        return of;
    }

    /**
     * @param @return 设定文件
     * @return Function<Secs2, TjdScreenItemDto> 返回类型
     * @throws
     * @Title: commandToScreen
     * @Description: 上下货指令转化为厢位的物料情况
     */
    private final static Function<Secs2, TjdScreenItemDto> commandToScreen() {

        return shelfItem -> {

            TjdScreenItemDto dto = new TjdScreenItemDto();
            try {
                String position = shelfItem.getAscii(0);
                if (StringUtils.equals(position, SEP)) {
                    return null;
                }
                String[] agvPosition = StringUtils.split(position, SEP);

                dto.setCurPosition(position);
                String binNo = agvPosition[1];
                dto.setBinNo(dto.getBackBin(binNo));

                dto.setLotId(shelfItem.getAscii(1));
                dto.setState(shelfItem.getAscii(2));
                dto.setStage(shelfItem.getAscii(3));
                dto.setPartId(shelfItem.getAscii(4));
                dto.setCurMainQty(shelfItem.getAscii(5));
                dto.setCapability(shelfItem.getAscii(6));
                dto.setPri(shelfItem.getAscii(7));
                dto.setNextLocation(shelfItem.getAscii(8));

            } catch (Secs2Exception e) {
                log.error("get_data:{}", Throwables.getStackTraceAsString(e));
            }
            return dto;

        };

    }

    @Autowired
    private ForeignActionService foreignActionService;

    /**
     * @param 设定文件
     * @return void 返回类型
     * @throws
     * @Title: toMesStation
     * @Description: 工作站到达事件
     */
    public AgvArrivedErackInfoMessage toMesStation(AgvArrivedErackInfoMessage agvArrivedMsg) {

        Vehicle vehicle = defaultVehiclePool.getVehicle(agvArrivedMsg.getAgvCode());

        try {

            ReportType reportType = agvArrivedMsg.getReportType();
            if (ReportType.QUERY.equals(reportType)) {
                /**
                 * 从Mes 系统获得更新之后的data
                 */

            } else if (ReportType.CASSETTE_IN.equals(reportType)
                    || ReportType.CASSETTE_OUT.equals(reportType)) {
                /**
                 * 上报车辆数据给上游mes 系统
                 */
                reportChangeInfoToMes(agvArrivedMsg);

            } else {
                // 離綫模式，不上報事件
                if ((!vehicle.getLinePatrolMode() && ReportType.PRE_ENTER.equals(reportType))) {
                    return agvArrivedMsg;
                }
                submitTotalInfoToMes(agvArrivedMsg);

                if (Objects.nonNull(reportType) && reportType.isLeave()) {
                    TjdCxt.LEAVE_NOTIFY.put(agvArrivedMsg.getAgvCode(), agvArrivedMsg);
                }
                boolean enter = ReportType.ENTER.equals(reportType);
                boolean leave = ReportType.LEAVE.equals(reportType);
                /**
                 * 进站
                 */
                if (enter) {
                    TjdCxt.LEAVING_STATION.remove(agvArrivedMsg.getAgvCode());
                    foreignActionService.broadcastForeignAction(agvArrivedMsg.getAgvCode());
                }
                if (leave) {
                    vehicle.setPlayStationMusic(false);
                }
                /**
                 * 离站
                 */
                String currentStation = agvArrivedMsg.getCurrentStation();

                if (leave && StringUtils.isNotBlank(currentStation)) {
                    TjdCxt.handleLinkStation(vehicle, currentStation);

                    WorkCycleConfig cycleConfig = workCycleConfigService.selectById(currentStation);


                    boolean cycleAble = Objects.nonNull(cycleConfig) && StringUtils.equals(vehicle.getAgvGroupId(), cycleConfig.getAgvGroupId()) && BooleanUtils.toBoolean(cycleConfig.getEnabled());
                    /**
                     * 离站且进站工作模式离站工作模式非自动
                     */
                    if (cycleAble && cycleConfig.getEnterWorkMode() > 0) {
                        TjdCxt.LEAVING_STATION.put(agvArrivedMsg.getAgvCode(), currentStation);

                    }

                }

                if (enter && !agvArrivedMsg.isMesReturn()) {
                    /**
                     * 到达工作站之后仍然未 等待mes 返回结果
                     */
                    alertToMes(Alert.NO_UPLOAD_UNlOAD_CMD_RECEIVED, vehicle, "pls check  IT sever");
                }
            }

        } catch (Exception e) {

            log.error("error:{}", Throwables.getStackTraceAsString(e));
        } finally {
            if (Objects.nonNull(vehicle)) {
                agvArrivedMsg.setUnLoadOnly(vehicle.getUnLoadOnly());
                reportAlive(vehicle);
            }

        }

        return agvArrivedMsg;
    }


    /**
     * 真正离站通知
     *
     * @param agvCode
     */
    public void leaveReportNotify(String agvCode) {
        synchronized (agvCode) {
            CompletableFuture.runAsync(() -> {
                if (TjdCxt.LEAVE_NOTIFY.containsKey(agvCode)) {
                    AgvArrivedErackInfoMessage msg = TjdCxt.LEAVE_NOTIFY.get(agvCode);
                    Vehicle vehicle = defaultVehiclePool.getVehicle(msg.getAgvCode());
                    Boolean moving = TjdCxt.isMoving(vehicle);
                    if (BooleanUtils.toBoolean(moving)) {
                        try {
                            msg.setReportType(ReportType.LEAVE2);
                            vehicle.setPlayStationMusic(false);
                            toMesStation(msg);
                        } finally {
                            TjdCxt.LEAVE_NOTIFY.remove(agvCode);
                        }

                    }
                }
            });

        }

    }

    /**
     * @param @param vehicle 设定文件
     * @return void 返回类型
     * @throws
     * @Title: reportAlive
     * @Description: 上报货架组态屏通讯异常
     */
    public void reportAlive(Vehicle vehicle) {
        try {
            String key = vehicle.getId() + Alert.SHELF_COMM_ERROR;
            if (!vehicle.getShelfAlive()
                    && (Objects.isNull(SCREEN_CACHE.getIfPresent(key)) || SCREEN_CACHE.getIfPresent(key).get() < 50)) {
                alertToMes(Alert.SHELF_COMM_ERROR, vehicle, "pls check agv shelf");
                putAdd(SCREEN_CACHE, key);
            }
            String key2 = vehicle.getId() + Alert.SCREEN_COMM_ERROR;
            if (!vehicle.getScreenAlive() && (Objects.isNull(SCREEN_CACHE.getIfPresent(key2))
                    || SCREEN_CACHE.getIfPresent(key2).get() < 50)) {
                alertToMes(Alert.SCREEN_COMM_ERROR, vehicle, "pls check agv screen");
                putAdd(SCREEN_CACHE, key2);
            }
        } catch (Exception e) {
            log.error("reportAlive_shelf_screen_error:{}", Throwables.getStackTraceAsString(e));
        }

    }

    private void putAdd(Cache<Object, AtomicInteger> cache, String key) {
        AtomicInteger count = null;
        count = cache.getIfPresent(key);
        if (Objects.isNull(count)) {
            count = new AtomicInteger(0);
        }
        count.incrementAndGet();
        cache.put(key, count);

    }

    /**
     * @param @param agvArrivedMsg 设定文件
     * @return void 返回类型
     * @throws
     * @Title: reportChangeInfoToMes
     * @Description: 上报机器人厢位变更信息到mes
     */
    private void reportChangeInfoToMes(AgvArrivedErackInfoMessage agvArrivedMsg) {

        try {
            if (Objects.nonNull(agvArrivedMsg) && Objects.nonNull(agvArrivedMsg.getTjdBinChangeDTO())) {
                TjdBinChangeDTO changeDTO = agvArrivedMsg.getTjdBinChangeDTO();

                Secs2List list = Secs2.list(Secs2.ascii(TjdScreenItemDto.getIndex(changeDTO.getBinNo())),
                        Secs2.ascii(changeDTO.getLotId()), Secs2.ascii(changeDTO.getPcdId()));

                List<Secs2List> newArrayList = Lists.newArrayList();
                newArrayList.add(list);
                /**
                 * 机器人厢位变更推送厢位数据给mes
                 */
                sendMsg(agvArrivedMsg, newArrayList);

            }

        } catch (SecsException | InterruptedException e) {
            log.error("errors:{}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * @param @param  agvArrivedMsg
     * @param @throws SecsSendMessageException
     * @param @throws SecsWaitReplyMessageException
     * @param @throws SecsException
     * @param @throws InterruptedException 设定文件
     * @return void 返回类型
     * @throws
     * @Title: submitInfoToMes
     * @Description: 将数据上报mes系统
     */
    private void submitTotalInfoToMes(final AgvArrivedErackInfoMessage agvArrivedMsg)
            throws SecsSendMessageException, SecsWaitReplyMessageException, SecsException, InterruptedException {
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvArrivedMsg.getAgvCode());

        List<Secs2List> shelfData = vehicle.getShelfData().values().parallelStream().map(getShelfData())
                .collect(Collectors.toList());
//		List<Secs2List> shelfData = agvArrivedMsg.getCurrentShelfData().parallelStream()
//				.map(getShelfData()).collect(Collectors.toList());
        /**
         * 发送数据给上游mes系统
         */
        sendMsg(agvArrivedMsg, shelfData);
    }

    /**
     * @param @param  agvArrivedMsg
     * @param @param  shelfData
     * @param @throws SecsSendMessageException
     * @param @throws SecsWaitReplyMessageException
     * @param @throws SecsException
     * @param @throws InterruptedException 设定文件
     * @return void 返回类型
     * @throws
     * @Title: sendMsg
     * @Description: 向mes系统发送agv 的全量厢位数据
     */
    private void sendMsg(final AgvArrivedErackInfoMessage agvArrivedMsg, List<Secs2List> shelfData)
            throws SecsSendMessageException, SecsWaitReplyMessageException, SecsException, InterruptedException {
        /**
         * 向mes系统上报agv信息
         */
        ReportType reportType = agvArrivedMsg.getReportType();
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvArrivedMsg.getAgvCode());
        if (StringUtils.isBlank(vehicle.getCurrentStation())) {
            log.error("agvCode:{},currentStation:{},当前工作站为空", vehicle.getId(), vehicle.getCurrentStation());

            return;
        }
        ListOrderedMap<String, WorkCycleConfig> cycle = vehicle.getCycle();
        if (Objects.isNull(cycle) || cycle.isEmpty()) {
            log.error("agvCode:{},currentStation:{},当前工作站为空", vehicle.getId(), vehicle.getCurrentStation());

            return;
        }
        WorkCycleConfig config = cycle.get(vehicle.getCurrentStation());
        if (Objects.isNull(config)) {
            log.error("agvCode:{},currentStation:{},当前工作站为空", vehicle.getId(), vehicle.getCurrentStation());
            return;
        }
        super.send(6, 11, false, Secs2.list(
                /**
                 * agv到达工作站事件别名
                 */
                Secs2.ascii(reportType.getAlias()),
                /**
                 * 事件id
                 */
                Secs2.ascii(reportType.getEventId()),
                /**
                 * DATA
                 */
                Secs2.list(
                        /**
                         * agv所对应的code
                         */
                        Secs2.list(Secs2.ascii("AgvID"), Secs2.ascii(agvArrivedMsg.getAgvCode()

                        )),
                        /**
                         * 当前工作站
                         */
                        Secs2.list(Secs2.ascii("currentStation"),
                                Secs2.ascii(Objects.toString(vehicle.getCurrentStation()))

                        ),
                        /**
                         * 对应的电子货架数据
                         */
                        Secs2.list(Secs2.ascii("shelfData"), Secs2.list(shelfData)

                        ),

                        Secs2.list(

                                Secs2.ascii("releaseMode"),
                                Secs2.ascii(Objects.toString(config.getReleaseMode()))),
                        Secs2.list(

                                Secs2.ascii("agvGroupId"),
                                Secs2.ascii(Objects.toString(config.getAgvGroupId()))),
                        Secs2.list(

                                Secs2.ascii("altAgvGroupId"),
                                Secs2.ascii(Objects.toString(config.getAltAgvGroupId()))),
                        Secs2.list(

                                Secs2.ascii("currentAgvGroupId"),
                                Secs2.ascii(Objects.toString(vehicle.getAgvGroupId()))),
                        Secs2.list(

                                Secs2.ascii("faultMode"),
                                Secs2.bool(BooleanUtils.toBoolean(schedulerConfigService.selectSchedulerConfig().getFaultMode()))),
                        Secs2.list(

                                Secs2.ascii("brocadCast"),
                                Secs2.bool(BooleanUtils.toBoolean(ReportType.ENTER.equals(reportType) && foreignActionService.shouldPlay(agvArrivedMsg.getAgvCode())))


                        )


                        // end
                ))

        );

    }

    /**
     * 查询这些物料是否有目标归属地
     *
     * @param @param  currentShelfData
     * @param @return 即将要前往的工作站
     * @return Set<String> 返回类型
     * @throws
     * @Title: haveMaterial
     * @Description: TODO
     */
    public final static boolean haveMaterial(Collection<TjdScreenItemDto> currentShelfData) {
        if (CollectionUtils.isEmpty(currentShelfData)) {
            return false;
        } else {
            List<String> list = currentShelfData.parallelStream().map(TjdScreenItemDto::getNextLocation)
                    .collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(list);
        }

    }

    public static void handleUpdateShelfData(Collection<TjdScreenItemDto> shelfData, SecsMessage message) {

        log.debug("haveMaerial:{}", message.secs2());
        int size = message.secs2().size();
        if (size > 0) {
            message.secs2().forEach(item -> {

                TjdScreenItemDto screenItemDto = commandToScreen().apply(item);
                log.info("shelf:{}, data:{}", JSON.toJSONString(shelfData), screenItemDto);

                if (Objects.nonNull(screenItemDto) && shelfData.contains(screenItemDto)) {
                    /**
                     * 更新操作
                     */
                    /**
                     * 根据hashcode 及 binNo 来删除
                     */
                    shelfData.remove(screenItemDto);
                    /**
                     * 重新放入已一个其它属性的值
                     */
                    shelfData.add(screenItemDto);
                }

            });

        }
    }

    /**
     * @param @param event 设定文件
     * @return void 返回类型
     * @throws IOException
     * @throws
     * @Title: virtualStationArrived
     * @Description: 到达虚拟站点或者红绿灯亮灯点
     */
    @Async
    @EventListener
    public void virtualStationArrived(ReportSidePathDataEvent event) throws Exception {

        log.debug("received_at_virtual_station:{}", JSON.toJSONString(event.getSource()));

        Object source = event.getSource();
        ReportSidePathData data = (ReportSidePathData) source;

        if (Objects.nonNull(data) && Objects.nonNull(data.getSidePath()) && StringUtils.isNotBlank(data.getAgvCode())) {
            SidePath sidePath = data.getSidePath();
            String reportCode = StringUtils.trim(sidePath.getReportCode());
            Vehicle vehicle = defaultVehiclePool.getVehicle(data.getAgvCode());

            boolean containsStationNo = vehicle.getCycle().containsKey(reportCode);
            Integer reportStatus = sidePath.getReportStatus();
            boolean boolean1 = BooleanUtils.toBoolean(reportStatus);
            log.debug("contains_reportNo:{}_{},{}, reportStatus:{}", data.getAgvCode(), sidePath.getReportCode(), containsStationNo, boolean1);
            if (!boolean1) {
                log.debug("disabled_report:{}", boolean1);
                return;
            }
            if (containsStationNo && StringUtils.isBlank(sidePath.getReportUrl())) {
                /**
                 * 预到达上报站点数据
                 */
                if (StringUtils.equals(vehicle.getCurrentStation(), reportCode)) {
                    log.debug("preArriveStationReport_virtual_station:{}_{}", data.getAgvCode(), sidePath.getReportCode());
                    preArriveStationReport(data);
                } else {
                    log.warn("passby_reportcode:{}", sidePath.getReportCode());
                }

            } else {
                /**
                 * 发送红绿灯信号
                 */
                if (!data.isFromPlaning()) {
                    log.debug("redGreenLight_received_at_virtual_station:{}", sidePath.getReportUrl());
                    redGreenLight(sidePath);
                }

            }

        }

    }

    /**
     * @param @param  sidePath
     * @param @throws IOException 设定文件
     * @return void 返回类型
     * @throws
     * @Title: redGreenLight
     * @Description: 红绿灯退避
     */
    public void redGreenLight(SidePath sidePath) {
        Integer repeatAmount = sidePath.getRepeatAmount();
        if (Objects.isNull(sidePath) || Objects.isNull(repeatAmount) || repeatAmount < 3) {
            repeatAmount = 4;
        }
        for (int i = 1; i <= repeatAmount; i++) {

            try {
                HttpResult doGet = httpClientService.doGet(sidePath.getReportUrl());
                if (String.valueOf(doGet.getCode()).startsWith("2")) {
                    log.debug("2xx_response_direct_exit");
                    return;
                }

            } catch (Exception e) {
                Uninterruptibles.sleepUninterruptibly(i * 20 * 1000, TimeUnit.MICROSECONDS);

            }

        }

    }

    /**
     * @param @param data
     * @param @param containsStationNo 设定文件
     * @return void 返回类型
     * @throws
     * @Title: preArriveStationReport
     * @Description: 到达站点预上报
     */
    private void preArriveStationReport(ReportSidePathData data) {

        AgvArrivedErackInfoMessage agvArrivedMsg = new AgvArrivedErackInfoMessage();
        try {
            Vehicle vehicle = defaultVehiclePool.getVehicle(data.getAgvCode());
            /**
             * agv 处于近下货或者离线模式不上报数据
             */
            if (!vehicle.getLinePatrolMode()) {
                log.debug("agv:{},unloadOnly_or_not_in_linePatrolMode_skip_submit:{}", data.getAgvCode(),
                        data.getSidePath().getReportCode());
                return;
            }
            agvArrivedMsg.setAgvCode(data.getAgvCode());
            agvArrivedMsg.setReportType(ReportType.PRE_ENTER);
            agvArrivedMsg.setCurrentStation(data.getSidePath().getReportCode());
            /**
             * 车辆不可能为空&& 车辆的厢位也不可能为空
             */
            Collection<TjdScreenItemDto> values = vehicle.getShelfData().values();
            if (CollectionUtils.isNotEmpty(values)) {
                agvArrivedMsg.setCurrentShelfData(values);
                /**
                 * 将车辆厢位相关的数据上报mes 系统
                 */
                submitTotalInfoToMes(agvArrivedMsg);
            }

        } catch (SecsException | InterruptedException e) {
            log.error("error:{}", Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * @param @param event 设定文件
     * @return void 返回类型
     * @throws
     * @Title: fireAlert
     * @Description: 监听火灾报警
     */
    @Async
    @EventListener(classes = ApplicationReadyEvent.class)
    public void fireAlert(ApplicationReadyEvent event) {

        while (runing) {

            try {

                if (FireAlertNettyStart.getFired()) {

                    Pair<Collection<Vehicle>, Collection<Vehicle>> agvs = groupByPathId(
                            vehiclePoolService.getAvailable());
                    /**
                     * 需要发送mqtt报警信号小车, 小车向modbus中写入数据,关闭避障
                     */
                    Collection<Vehicle> fireAgvs = agvs.getKey();

                    fireAgvs.parallelStream().forEach(agv -> {

                        this.handleFiredAgv(agv);
                    });
                    /**
                     * 不在火灾路径上的agv, 暂停任务即可
                     */
                    Collection<Vehicle> notFireAgvs = agvs.getValue();

                    notFireAgvs.parallelStream().forEach(agv -> {

                        this.handleNotFiredAgv(agv);
                    });

                    FireAlertNettyStart.haveFired = true;
                    /**
                     * 上报mes 系统火警
                     */
//					alertToMes(Alert.FIRE_HAPPENED);
                }
                /**
                 * 火灾消除：恢复任务
                 */
                clearFire();

                Uninterruptibles.sleepUninterruptibly(1, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("error:{}", Throwables.getStackTraceAsString(e));
                Uninterruptibles.sleepUninterruptibly(1, TimeUnit.SECONDS);

            }
        }

    }

    /**
     * @param @param  vehicles
     * @param @param  pathIds
     * @param @return 设定文件
     * @return Pair<Collection < Vehicle>,Collection<Vehicle>> 返回类型
     * @throws
     * @Title: groupByPathId
     * @Description: 将给的车辆分组: 再火灾路线上的车辆, 不在火灾路线上的车辆
     */
    public final Pair<Collection<Vehicle>, Collection<Vehicle>> groupByPathId(Collection<Vehicle> vehicles) {

        ConcurrentMap<String, Boolean> newConcurrentMap = Maps.newConcurrentMap();

        MultiValueMap<String, String> pathIdAgvs = TjdCxt.pathIdAgvs();

        Set<Entry<String, List<String>>> entrySet = pathIdAgvs.entrySet();

        Map<Boolean, List<Vehicle>> map = vehicles.stream().collect(Collectors.groupingBy(p -> {

            for (Entry<String, List<String>> item : entrySet) {

                List<String> agvs = item.getValue();
                if (CollectionUtils.isNotEmpty(agvs) && agvs.contains(p.getId())
                        && !newConcurrentMap.containsKey(p.getId())) {

                    Path path = pathService.selectById(p.getAgvMapId(), item.getKey(), false);
                    if (Objects.nonNull(path) && BooleanUtils.toBoolean(path.getIsFirePath())) {
                        newConcurrentMap.put(p.getId(), true);
                        return true;
                    }

                }
            }

            return false;
        }));
        return Pair.of(MapUtils.getObject(map, true, Lists.newArrayList()),
                MapUtils.getObject(map, false, Lists.newArrayList()));
    }

    /**
     * 火灾消除
     */
    private void clearFire() {

        if (!FireAlertNettyStart.getFired() && FireAlertNettyStart.haveFired) {

            List<Vehicle> available = vehiclePoolService.getAvailable();
            available.parallelStream().forEach(item -> {

                try {

                    vehicleCommandService.pauseMissionWorkFileAlert(item.getMissionWorkId(), item.getId(), false);

                    item.resumeMissionWork(item.getMissionWorkId());

                } catch (Exception e) {
                    log.error("error :{}", Throwables.getStackTraceAsString(e));
                }

            });

            /**
             * 火灾消除
             */
            FireAlertNettyStart.haveFired = false;
        }
    }

    /**
     * @param @param fireAgvs 设定文件
     * @return void 返回类型
     * @throws
     * @Title: handlerFiredAgv
     * @Description: 在火灾路径上的agv
     */
    private void handleFiredAgv(Vehicle fireAgvs) {

        try {
            vehicleCommandService.fireAlert(fireAgvs.getId());
        } catch (Exception e) {
            log.error("InFiredPathAgv:{}, error:{}", fireAgvs.getId(), Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * @param @param notFireAgvs 设定文件
     * @return void 返回类型
     * @throws
     * @Title: handleNotFiredAgv
     * @Description: 处理没有着火的agv
     */
    private void handleNotFiredAgv(Vehicle notFireAgvs) {

        try {
            vehicleCommandService.pauseMissionWorkFileAlert(notFireAgvs.getMissionWorkId(), notFireAgvs.getId(), true);

        } catch (Exception e) {
            log.error("notInFiredPathAgv:{}, error:{}", notFireAgvs.getId(), Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * 监听missionwork 状态变化
     *
     * @param @param event 设定文件
     * @return void 返回类型
     * @throws
     * @Title: missionWorkStateChanged
     * @Description: 台积电项目监听任务状态变化之后, 重新新建一个任务
     */
    @Async
    @EventListener(classes = MissionWorkStateEvent.class)
    public void missionWorkStateChanged(MissionWorkStateEvent event) {

        try {
            if (Objects.isNull(event) || Objects.isNull(event.getSource())) {
                return;
            }
            MissionWork missionWork = (MissionWork) event.getSource();
            /**
             * 创建任务: 这里可以创建,同时在 @see
             * com.youibot.agv.scheduler.engine.scheduler.TjdIntegrityComponent 中也可以创建
             */
            /**
             * 这里不创建任务, 统一到集成发生器里面去创建
             */
            /* createNextMission( missionWork); */
            /**
             * 上报任务执行出错到mes系统
             */
            missionErrorReport(missionWork);
            /**
             * change current point
             */
//			resetCurrent(missionWork);
        } finally {
            // TODO: handle finally clause

        }
    }

    /**
     * 事件及异常上报
     *
     * @param event
     */
    @Async
    @EventListener(classes = NotifyMessageEvent.class)
    public void notifyMessageEventHandler(NotifyMessageEvent event) {

        try {

            Notification source = (Notification) event.getSource();
            String agvCode = source.getAgvCode();

            log.debug("receive:{}", JSON.toJSONString(event.getSource()));
            /**
             * 异常上报
             */
            /**
             * 事件上报
             */
            Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
            if (vehicle == null) {
                log.error("agvCode:{},not_exists", agvCode);
                return;
            }
            ReportType type = event.getReportType();
            String position = TjdCxt.getPosition(vehicle);
            if (event.isAlert()) {
                AbnormalPrompt abnormalPrompt = abnormalPromptService.getAbnormalByCode(source.getErrorCode());
                if (!TjdCxt.DISABLE_ALERT.contains(source.getErrorCode()) || !abnormalPrompt.isPushUp()) {

                    send(5, 1, false, Alert.custom(source, abnormalPrompt, position));
                }

            } else {

                Secs2List list = Secs2.list(Secs2.list(

                                Secs2.ascii("AGVID"), Secs2.ascii(agvCode)),

                        Secs2.list(

                                Secs2.ascii("POSITION"), Secs2.ascii(position))

                );


                boolean chargeType = ReportType.CHARGE.equals(type) && Objects.nonNull(event.getChargeEventsMsg());
                if (chargeType) {

                    list = chargeData(agvCode, position, event);
                }

                if (ReportType.CHARGE_WASH.equals(type) && Objects.nonNull(event.getChargeWashMessage())) {

                    list = chargeWashData(agvCode, position, event);
                }

                if (ReportType.BRACELET.equals(type) && Objects.nonNull(event.getBraceletMessage())) {

                    list = braceletData(agvCode, position, event);
                }
                String alias = type.getAlias();
                String eventId = type.getEventId();
                if (chargeType) {
                    Phrase phrase = event.getChargeEventsMsg().getPhrase();
                    alias = phrase.name();
                    eventId = "" + (EventId.CHARGEING_EVENT_PHRASE_PREFIX + phrase.ordinal());
                }
                super.send(6, 11, false, Secs2.list(
                                /**
                                 * agv到达工作站事件别名
                                 */
                                Secs2.ascii(alias),
                                /**
                                 * 事件id
                                 */
                                Secs2.ascii(eventId),
                                /**
                                 * 额外data
                                 */
                                list

                        )

                );
            }

        } catch (SecsException | InterruptedException e) {
            log.error("submit msg error:{}");
        }

    }

    private Secs2List braceletData(String agvCode, String position, NotifyMessageEvent event) {
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        int releaseMode = 0;
        WorkCycleConfig config = vehicle.getCycle().get(vehicle.getCurrentStation());
        if (Objects.nonNull(config)) {
            releaseMode = config.getReleaseMode();
        }
        BraceletMessage msg = event.getBraceletMessage();
        Secs2List list = Secs2.list(
                Secs2.list(

                        Secs2.ascii("AGVID"), Secs2.ascii(agvCode)),

                Secs2.list(

                        Secs2.ascii("POSITION"), Secs2.ascii(position)),

                Secs2.list(

                        Secs2.ascii("toeNO"), Secs2.ascii(msg.getNo())),

                Secs2.list(

                        Secs2.ascii("filed1"), Secs2.ascii(Objects.toString(msg.getFiled1()))),
                Secs2.list(

                        Secs2.ascii("time"),
                        Secs2.ascii(Objects.toString(msg.getTime()))),
                Secs2.list(

                        Secs2.ascii("stationId"),
                        Secs2.ascii(Objects.toString(vehicle.getCurrentStation()))),
                Secs2.list(

                        Secs2.ascii("releaseMode"),
                        Secs2.ascii(Objects.toString(releaseMode))),
                Secs2.list(

                        Secs2.ascii("agvGroupId"),
                        Secs2.ascii(Objects.toString(config.getAgvGroupId()))),
                Secs2.list(

                        Secs2.ascii("altAgvGroupId"),
                        Secs2.ascii(Objects.toString(config.getAltAgvGroupId()))),
                Secs2.list(

                        Secs2.ascii("currentAgvGroupId"),
                        Secs2.ascii(Objects.toString(vehicle.getAgvGroupId()))),
                Secs2.list(

                        Secs2.ascii("faultMode"),
                        Secs2.bool(BooleanUtils.toBoolean(schedulerConfigService.selectSchedulerConfig().getFaultMode())))

        );
        return list;
    }

    private Secs2List chargeWashData(String agvCode, String position, NotifyMessageEvent event) {
        ChargeWashMessage msg = event.getChargeWashMessage();
        Secs2List list = Secs2.list(Secs2.list(

                        Secs2.ascii("AGVID"), Secs2.ascii(agvCode)),

                Secs2.list(

                        Secs2.ascii("POSITION"), Secs2.ascii(position)),

                Secs2.list(

                        Secs2.ascii("toeNO"), Secs2.ascii(msg.getNo())),

                Secs2.list(

                        Secs2.ascii("filed1"), Secs2.ascii(Objects.toString(msg.getFiled1()))),
                Secs2.list(

                        Secs2.ascii("time"),
                        Secs2.ascii(Objects.toString(msg.getTime())))

        );
        return list;
    }

    private Secs2List chargeData(String agvCode, String position, NotifyMessageEvent event) {
        ChargeEventsMsg msg = event.getChargeEventsMsg();

        Secs2List list = Secs2.list(Secs2.list(

                        Secs2.ascii("AGVID"), Secs2.ascii(agvCode)),

                Secs2.list(

                        Secs2.ascii("POSITION"), Secs2.ascii(position)),

                Secs2.list(

                        Secs2.ascii("markCode"), Secs2.ascii(msg.getMarkCode())),

                Secs2.list(

                        Secs2.ascii("phrase"), Secs2.ascii(Objects.toString(msg.getPhrase()))),
                Secs2.list(

                        Secs2.ascii("batteryValue"),
                        Secs2.ascii(Objects.toString(String.format("%.2f", msg.getBatteryValue()))))

        );
        return list;
    }

    /*
     * private void resetCurrent(MissionWork missionWork) { Mission mission =
     * missionService.getTjdDefaultMission();
     *
     * Vehicle vehicle = defaultVehiclePool.getVehicle(missionWork.getAgvCode());
     * if(Objects.isNull(vehicle) || Objects.isNull(mission)) {
     * log.error("currentAgv_isNULL_mission_not_configured"); return ; }
     * if(StringUtils.equals(mission.getName(), missionWork.getName()) &&
     * MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {
     * if(StringUtils.isNotBlank(vehicle.getPreStation())) {
     * vehicle.setCurrentStation(vehicle.getPreStation() ); }else {
     *//**
     * 清空當前工作站
     *//*
     * vehicle.setCurrentStation( null ); } }
     *
     * }
     */

    /**
     * 这里不创建任务, 统一到集成发生器里面去创建
     *
     * @param @param  missionWork
     * @param @return 设定文件
     * @return MissionWork 返回类型
     * @throws
     * @Title: createNextMission
     * @Description: 创建机器人的下一个任务
     */
    @SuppressWarnings("unused")
    /*
     * private MissionWork createNextMission(MissionWork missionWork) { Mission
     * mission = missionService.getTjdDefaultMission();
     *
     * Vehicle vehicle = defaultVehiclePool.getVehicle(missionWork.getAgvCode());
     *
     * boolean shouldBeginNextStation = TjdCxt.shouldBeginNextStation(vehicle,
     * missionWork, mission.getId()); if (shouldBeginNextStation) {
     *
     * Pair<Boolean, MissionWorkParam> missionWorkParam =
     * TjdCxt.getMissionWorkParam(vehicle, mission);
     *
     * missionWorkService.createByMissionWorkParam(missionWorkParam.getRight());
     *
     * } return missionWork; }
     */

    /**
     *
     * @Title: missionErrorReport
     * @Description: 上报mes 系统任务执行出错
     * @param @param missionWork 设定文件
     * @return void 返回类型
     * @throws
     */
    private void missionErrorReport(MissionWork missionWork) {
        if (MISSION_WORK_STATUS_FAULT.equals(missionWork.getStatus())) {
            Vehicle vehicle = defaultVehiclePool.getVehicle(missionWork.getAgvCode());
            alertToMes(Alert.TASK_RUN_ERROR, vehicle, "");

        }
    }

    /**
     * @param @param alert 设定文件
     * @return void 返回类型
     * @throws
     * @Title: alertToMes
     * @Description: 发送报警信息到mes 系统
     */

    private void alertToMes(Alert alert, Vehicle vehicle, String solution) {
        try {
            if (!TjdCxt.isWaiting(vehicle)) {
                try {
                    boolean isDisable = TjdCxt.DISABLE_ALERT.contains(Integer.valueOf(alert.getAlertId()));
                    if (isDisable) {
                        return;
                    }
                } catch (Exception e) {
                    // TODO: handle exception
                }
                send(5, 1, false, alert.msg(vehicle, solution));
            }

        } catch (Exception e) {
            log.error("report_error");
        }
    }

    @PostConstruct
    public void init() {

        NotifyMessageEvent.applicationEventPublisher = applicationEventPublisher;

    }

    public HsmsCommunicator getHsmsCommunicator() {

        return hsmsSsCommunicator;
    }

    @PreDestroy
    public void destrony() {

        runing = false;
    }
}
