package com.youibot.agv.scheduler.listener;
 
 
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.google.common.base.Throwables;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Sets;
import com.youibot.agv.scheduler.config.TjdFireAlertProperties;

import lombok.extern.slf4j.Slf4j;


/**
  * @ClassName: FireAlertNettyClient
  * @Description: 火灾接收客户端
  * <AUTHOR>
  * @date 2022年2月26日 下午7:30:13
  *
  */
@Component
@Slf4j(topic =  "fireAlertNettyStart")
public class FireAlertNettyStart extends Thread {
	
	public  static boolean haveFired ;
	
	@Autowired
	private TjdFireAlertProperties tjdFireAlertProperties;
	
	static Set<FireAlertNettyClient> clients = Sets.newHashSet();
	
	/**
	 * 缓存避免重复打印日志
	 */
	public static Cache<Object, Object> cache = CacheBuilder.newBuilder().expireAfterWrite(3 , TimeUnit.MINUTES).maximumSize(2).build();

	@Async
	@PostConstruct
	public void init() {
		
		Set<String> hosts = tjdFireAlertProperties.getHost();
		
		for (String ip : hosts) {
			
			try {
				
				FireAlertNettyClient client = new FireAlertNettyClient(ip , tjdFireAlertProperties.getPort());
				client.setDaemon(true);
				client.start();
				clients.add(client);
			} catch (Exception e) {
				
				log.error("error:{}", Throwables.getStackTraceAsString(e));
			}
			
		}
	}
	
	@SuppressWarnings("deprecation")
	@PreDestroy
	public void destrony() {
		
		for ( FireAlertNettyClient item : clients ) {
			 try {
				item.destroy();
				item.stop();
			} catch (Exception e) {
				log.error("error:{}" ,Throwables.getStackTraceAsString(e));
			}
			
		}
		
	}
	
	/** 是否着火
	 * @return
	 */
	public static boolean getFired() {
		
		for (FireAlertNettyClient client : clients) {
			if(Objects.isNull(client.getChannelFuture())) {
				try {
					client.initConnect();
				} catch (Exception e) {
				 log.debug("getFired:reconect");
				}
			}
			String key = client.host + "__"+ client.port;
			if(client.fired) {
			
				Object val = cache.getIfPresent(key);
				if(Objects.isNull(val)) {
					log.error("fire_happened:{}" , key);
					cache.put(key, key);
				}else {
					cache.invalidate(key);
				}
				return true;
			}
			
		}
		return false; 
	}
	

	
	
}


