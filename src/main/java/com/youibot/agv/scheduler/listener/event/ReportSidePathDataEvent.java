package com.youibot.agv.scheduler.listener.event;

import org.springframework.context.ApplicationEvent;

import com.youibot.agv.scheduler.entity.ReportSidePathData;

/**
  * @ClassName: ReportSidePathDataEvent
  * @Description: 上报事件
  * <AUTHOR>
  * @date 2022年1月18日 上午11:36:08
  *
  */
public class ReportSidePathDataEvent extends ApplicationEvent {

	/**
	  * @Fields serialVersionUID : TODO（用一句话描述这个变量表示什么）
	  */
	
	private static final long serialVersionUID = 1L;

	public ReportSidePathDataEvent(ReportSidePathData source) {
		super(source);
	}

}
