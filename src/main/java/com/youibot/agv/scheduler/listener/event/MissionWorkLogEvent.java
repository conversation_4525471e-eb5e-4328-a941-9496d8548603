package com.youibot.agv.scheduler.listener.event;

import com.youibot.agv.scheduler.entity.MissionWork;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/3/3 9:53
 * @Description: 记录MissionWork的状态变更时间,用于性能统计
 */
@Getter
public class MissionWorkLogEvent extends ApplicationEvent {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private MissionWork missionWork;

    public MissionWorkLogEvent(MissionWork missionWork, Object source) {
        super(source);
        this.missionWork = missionWork;
    }
}
