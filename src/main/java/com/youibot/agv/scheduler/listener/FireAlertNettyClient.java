package com.youibot.agv.scheduler.listener;
 
 
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.google.common.base.Splitter;
import com.google.common.base.Throwables;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerAdapter;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoop;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.LineBasedFrameDecoder;
import io.netty.handler.codec.string.StringDecoder;
import lombok.extern.slf4j.Slf4j;


/**
  * @ClassName: FireAlertNettyClient
  * @Description: 火灾接收客户端
  * <AUTHOR>
  * @date 2022年2月26日 下午7:30:13
  *
  */
@Slf4j
public class FireAlertNettyClient extends Thread {
	

	
	public volatile   boolean fired ;

	
//	public static TjdFireAlertProperties tjdFireAlertProperties;
	
	private  volatile  ChannelFuture channelFuture ;
	
	 NioEventLoopGroup group = null;
	 Bootstrap bootstrap = null;
	
	@Override
	public void run() {
		
		init(); 
	}

	

	String host ;
	int port;
	
	

	public FireAlertNettyClient(String host, int port) {
		super();
		this.host = host;
		this.port = port;
		this.setName(host+"_"+port);
	}




	/**
	 * 启动
	 */
	public void init() {
	
		try {
			initConnect(   );
		} catch (Exception e) {
			log.error("error:{}" , Throwables.getStackTraceAsString(e));
		}
		
	}
	
	
	
	
	/**
	 *销毁客户端
	 */
	public void destroy() {
		if(Objects.nonNull(channelFuture)) {
			channelFuture.channel().close();
			channelFuture = null; 
			
		}
		try {
			group.close();
		} catch (Exception e) {
			
		}finally {
			if(Objects.nonNull(group)) {
				group.shutdownGracefully();
			}
			
		}
		
		
	}
	
	/**初始化连接
	 * @throws Exception
	 */
	public synchronized  void initConnect( ) throws Exception{
	
	
		try {
			 if(Objects.isNull(group)) {
				 group = new NioEventLoopGroup();
			 }
			 if(Objects.isNull(bootstrap)) {
				 bootstrap = new Bootstrap();
					bootstrap =  bootstrap.group(group).channel(NioSocketChannel.class)
					.option(ChannelOption.SO_KEEPALIVE, true )
					
					.handler(new ChannelInitializer<SocketChannel>() {
		 
						@Override
						protected void initChannel(SocketChannel ch) throws Exception {
							ch.pipeline().addLast(new LineBasedFrameDecoder(1024));
							ch.pipeline().addLast(new StringDecoder());
							ch.pipeline().addLast( new ChannelHandlerAdapter() {
								
								@Override
								public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
									log.warn("close_fireAlertClient_connection");
									cause.printStackTrace();
								}
								
								@Override
								public void channelInactive(ChannelHandlerContext ctx) throws Exception {
									ctx.channel().eventLoop().schedule(new Runnable() {
							            @Override
							            public void run() {
							            	log.error("reconnect fireAlertServer .....");
							                try {
							                	initConnect();
											} catch (Exception e) {
												e.printStackTrace();
											}
							            }
							        }, 300, TimeUnit.MICROSECONDS);
								}
								
								
								
								@Override
								public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
									String body =  (String) msg;
									
//								 log.debug("host_port:{},receive_fireAlertServer_data：{} ", host+"_"+ port, body);
									Map<String, String> split = Splitter.on(",").withKeyValueSeparator(":").split(body);
									fired = split.containsValue("1");
									
//									log.debug("host_port:{},haveFired:{} ", host+"_"+ port, fired);
								}
							}
							);
							
						}
					});
					
					channelFuture = bootstrap.connect(host , port );
					channelFuture.addListener(new ChannelFutureListener() {

						@Override
						public void operationComplete(ChannelFuture arg0) throws Exception {

					        if (!channelFuture.isSuccess()) {
					            final EventLoop loop = channelFuture.channel().eventLoop();
					            loop.schedule(new Runnable() {
					                @Override
					                public void run() {
					                	log.error("fireAlertServer reconnection again");
					                    try {
											initConnect();
										} catch (Exception e) {
											e.printStackTrace();
										}
					                }
					            },10, TimeUnit.MICROSECONDS);
					        } else {
					        	log.error("服务端链接成功...");
					        }
					    
							
						}
						
					});
					
				
					return ;
			 }
			if(Objects.nonNull(channelFuture)) {
				channelFuture.channel().disconnect();
				channelFuture = null; 
				channelFuture = bootstrap.connect(host ,port );
				channelFuture.addListener(new ChannelFutureListener() {

					@Override
					public void operationComplete(ChannelFuture arg0) throws Exception {
				        if (!channelFuture.isSuccess()) {
				            final EventLoop loop = channelFuture.channel().eventLoop();
				            loop.schedule(new Runnable() {
				                @Override
				                public void run() {
				                	log.error("fireAlertServer reconnection again host:{},port:{}" , host, port );
				                    try {
										initConnect();
									} catch (Exception e) {
										log.error("host:{},port:{}" , host, port ) ;
									}
				                }
				            } , 1 , TimeUnit.SECONDS);
				        } else {
				        	log.error("服务端链接成功...");
				        }
				    }
					
				});
				
			}
		
			
		} finally {
			//group.shutdownGracefully();
		}
	}




	public ChannelFuture getChannelFuture() {
		return channelFuture;
	}






	

	
	



}


