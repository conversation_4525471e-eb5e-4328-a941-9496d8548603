package com.youibot.agv.scheduler.listener;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_PAUSE;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_RUNNING;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_WAIT_INPUT;
import static com.youibot.agv.scheduler.constant.SchedulePlanConstant.SCHEDULE_STATUS_PAUSE;
import static com.youibot.agv.scheduler.constant.SchedulePlanConstant.SCHEDULE_STATUS_READY;
import static com.youibot.agv.scheduler.constant.SchedulePlanConstant.SCHEDULE_STATUS_RUNNING;
import static com.youibot.agv.scheduler.constant.SchedulePlanConstant.SCHEDULE_TRIGGER_STATUS_NONE;

import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.SchedulePlan;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.scheduleplan.SchedulePlanUtil;
import com.youibot.agv.scheduler.service.SchedulePlanService;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月23日 上午11:20:11
 */
@Component
public class WorkStateEventListenerUpdateSchedulingPlan {

    private static final Logger LOGGER = LoggerFactory.getLogger(WorkStateEventListenerUpdateSchedulingPlan.class);

    @Autowired
    private SchedulePlanService schedulePlanService;

    @Autowired
    private SchedulePlanUtil schedulePlanUtil;

    @EventListener
    public void handleMissionWorkEvent(MissionWorkStateEvent event) {
        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
//            MissionWork missionWork = (MissionWork) event.getSource();
//            this.updateSchedulingPlan(missionWork);
        }
    }

	// missionWork 所有执行状态的改变
	private void updateSchedulingPlan(MissionWork missionWork) throws SchedulerException {
		String planId = missionWork.getSchedulePlanId();
		SchedulePlan plan = schedulePlanService.selectById(planId);
		if (plan == null) {//missionWork不一定通过schedulePlan创建
			return;
		}
		SchedulePlan planEntity = new SchedulePlan();
		planEntity.setId(planId);
		Boolean executeModel = plan.getExecuteOverCreateNew();
		// MissionWork start running pause
		if (MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus())
				|| MISSION_WORK_STATUS_PAUSE.equals(missionWork.getStatus())
				|| MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
			// 设置运行状态
			if (SCHEDULE_STATUS_READY.equals(plan.getStatus())) {
				if (executeModel) {
					planEntity.setStatus(SCHEDULE_STATUS_RUNNING);
				}
			}
		} else {
			// 获取执行状态
			String triggerState = schedulePlanUtil.getTriggerState(plan);
			// 判断是否完成
			if (!SCHEDULE_TRIGGER_STATUS_NONE.equals(triggerState)) {
				// missionWork 为成功 失败 停止 都要恢复调度计划
				if (executeModel) {
					String planStatus = plan.getStatus();
					// 判断是否是客户暂停
					if (!SCHEDULE_STATUS_PAUSE.equals(planStatus)) {
						schedulePlanService.scheduleResume(plan);
					}
				}
			}

		}
		schedulePlanService.updateByPrimaryKeySelective(planEntity);
	}
}
