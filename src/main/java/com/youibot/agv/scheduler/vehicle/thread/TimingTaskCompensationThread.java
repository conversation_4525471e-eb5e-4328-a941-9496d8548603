package com.youibot.agv.scheduler.vehicle.thread;

import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.AgvStatisticsUtil;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 定时任务补偿
 * @Author：yangpeilin
 * @Date: 2020/7/24 20:07
 */
@Service
public class TimingTaskCompensationThread extends Thread {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionWorkActionParameterService missionWorkActionParameterService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private NotificationService notificationService;
    @Autowired
    private MqMessageService mqMessageService;

    @Override
    public void run() {
        //agv统计信息
        AgvStatisticsUtil.initData();
        //删除过期任务数据
//        SystemConfigJobUtil.deleteMissionWorkLogs(systemConfigService, missionWorkService, missionWorkActionService, missionWorkActionParameterService, notificationService,mqMessageService);
        //删除过期文件
//        SystemConfigJobUtil.deleteLogFile(systemConfigService);
    }
}
