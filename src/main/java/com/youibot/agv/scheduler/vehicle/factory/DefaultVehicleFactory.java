package com.youibot.agv.scheduler.vehicle.factory;

import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 11:00
 */
@Component
public class DefaultVehicleFactory implements VehicleFactory {

    @Override
    public Vehicle createVehicle(AGV agv) throws Exception {
        return null;
    }

    private void setVehicleInfo(AGV agv, Vehicle vehicle) throws Exception {
        vehicle.setId(agv.getId());
        vehicle.setName(agv.getName());
        vehicle.setIp("**********");
    }
}
