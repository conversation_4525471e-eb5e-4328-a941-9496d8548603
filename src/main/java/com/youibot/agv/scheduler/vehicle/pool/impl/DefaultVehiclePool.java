package com.youibot.agv.scheduler.vehicle.pool.impl;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 13:57
 */

@Component
public class DefaultVehiclePool implements VehiclePool {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultVehiclePool.class);

    @Autowired
    private AGVService agvService;
    @Autowired
    private SchedulerConfigService schedulerConfigService;

    private Map<String, Vehicle> poolEntries = new ConcurrentHashMap<>();

    @Override
    public Vehicle getVehicle(String agvCode) {
        if (poolEntries.containsKey(agvCode)) {
            return poolEntries.get(agvCode);
        }
        return null;
    }

    @Override
    public synchronized void attachAGV(Agv agv) {
        if (poolEntries.containsKey(agv.getAgvCode())) {
            return;
        }
        Vehicle vehicle = createVehicle(agv);
        if (vehicle != null) {
            this.attachVehicle(vehicle);
            LOGGER.debug("attach vehicle, id:{}", vehicle.getId());
        }
    }

    @Override
    public void attachVehicle(Vehicle vehicle) {
        poolEntries.put(vehicle.getId(), vehicle);
    }

    @Override
    public void detachVehicle(String id) {
        if (poolEntries.containsKey(id)) {
            Vehicle vehicle = poolEntries.get(id);
            poolEntries.remove(id);
//            vehicle.terminate();
        }
    }

    @JsonIgnore
    @Override
    public List<Vehicle> getAll() {
        return new ArrayList<Vehicle>(this.poolEntries.values());
    }

    public List<Vehicle> getVehicles(List<String> agvCodeList) {
        if (CollectionUtils.isEmpty(agvCodeList)) {
            return null;
        }
        List<Vehicle> list = new ArrayList<>();
        agvCodeList.forEach(agvCode -> {
            Vehicle vehicle = poolEntries.get(agvCode);
            if (vehicle != null) {
                list.add(vehicle);
            }
        });
        return list;
    }

    public static Vehicle createVehicle(Agv agv) {
        Vehicle vehicle = (Vehicle) ApplicationUtils.getBean("defaultVehicle");
        vehicle.initialize(agv);
        return vehicle;
    }

    @Override
    public void updateVehicleByAgv(String agvCode) {
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (agv != null) {
            Vehicle vehicle = this.getVehicle(agvCode);
            if (vehicle != null) {
                vehicle.setAgvGroupId(agv.getAgvGroupId());
                vehicle.setAgvType(agv.getAgvType());
                vehicle.setStatus(agv.getStatus());
                vehicle.setName(agv.getAgvName());
                vehicle.setNavigationType(agv.getNavigationType());
                vehicle.setAutoCharge(agv.getAutoCharge()==2?schedulerConfig.getChargeSchedulerEnable()==1:agv.getAutoCharge()==1);
                vehicle.setAutoPark(agv.getAutoPark() ==2?schedulerConfig.getParkSchedulerEnable()==1:agv.getAutoPark()==1);
                vehicle.setAutoAllocation(agv.getAutoAllocation() == 1);

                vehicle.setAgvColor(agv.getAgvColor());
            }
        }
    }

	@Override
	public Collection<String> getAllAgvCodes() {

		return poolEntries.keySet();
	}
}
