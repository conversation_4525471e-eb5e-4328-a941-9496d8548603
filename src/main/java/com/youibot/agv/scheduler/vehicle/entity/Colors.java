package com.youibot.agv.scheduler.vehicle.entity;

import com.youibot.agv.scheduler.engine.pathplan.util.ColorUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date :Created in 下午1:54 2020/10/21
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class Colors {
    String executedColor;
    String runningColor;
    String plannedColor;

    public Colors() {
        executedColor = "";
        runningColor = "";
        plannedColor = "";
    }

    public Colors(String executedColor, String runningColor, String plannedColor) {
        this.executedColor = executedColor;
        this.runningColor = runningColor;
        this.plannedColor = plannedColor;
    }
}

