package com.youibot.agv.scheduler.vehicle.module;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 17:12
 */
public interface VehicleRelocation {

    /**
     * 手动重定位
     * @param param
     * @return
     */
    void manualRelocation(Map<String, Object> param) throws IOException, InterruptedException;

    /**
     * 自动重定位
     */
    void autoRelocation() throws IOException, InterruptedException;

    /**
     * 录制初始位置
     * @return
     */
    Map<String, Object> recordHomeMarker(String agvMapId) throws IOException;
}
