package com.youibot.agv.scheduler.vehicle.module;

import java.util.Map;

/**
 * <AUTHOR> E-mail:song<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019-05-13 21:22
 */
public interface VehicleMap {

    // 同步地图
    void syncMap(String agvMapId) throws Exception;

    //获取地图列表
    Map<String, Object> findMaps() throws Exception;

    //获取地图
    Map<String, Object> findMap(String deviceMapId, Integer mapType) throws Exception;

}
