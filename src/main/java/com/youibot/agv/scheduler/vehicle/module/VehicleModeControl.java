package com.youibot.agv.scheduler.vehicle.module;

import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.io.IOException;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-15 14:58
 */
public interface VehicleModeControl {

    /**
     * 切换为手工控制模式
     */
    void switchManualMode();

    /**
     * 强制切换为手工模式
     */
    void forceSwitchManualMode() throws InterruptedException;

    /**
     * 手工控制移动
     * @param vx
     * @param vy
     * @param vtheta
     * @return
     * @throws Exception
     */
    boolean manualMove(Double vx, Double vy, Double vtheta) throws Exception;

    /**
     * 切换到自动控制模式
     */
    void switchAutoMode() throws IOException, InterruptedException;

    /**
     * 切换为录制模式
     */
    void switchRecordMode() throws IOException;

}
