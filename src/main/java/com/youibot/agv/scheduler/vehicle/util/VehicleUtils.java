package com.youibot.agv.scheduler.vehicle.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.manager.execute.action.attribute.AttributeFactory;
import com.youibot.agv.scheduler.engine.manager.execute.action.attribute.BaseAttribute;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThread;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThreadPool;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.entity.AGVFunctionConfig;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AGVFunctionConfigService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.DefaultVehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.MissionConstant.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/10/9 19:16
 */
public class VehicleUtils {

    private final static Logger LOGGER = LoggerFactory.getLogger(VehicleUtils.class);

    private static MissionWorkService missionWorkService = (MissionWorkService) ApplicationUtils.getBean("missionWorkServiceImpl");
    private static MissionWorkThreadPool missionWorkThreadPool = (MissionWorkThreadPool) ApplicationUtils.getBean("missionWorkThreadPool");
    private static AGVFunctionConfigService agvFunctionConfigService = (AGVFunctionConfigService) ApplicationUtils.getBean("AGVFunctionConfigServiceImpl");
    private static Vehicle vehicle = (Vehicle) ApplicationUtils.getBean("defaultVehicle");
    private static DefaultVehiclePool defaultVehiclePool = (DefaultVehiclePool) ApplicationUtils.getBean("defaultVehiclePool");

    /**
     * 获取vehicle
     * @return
     */
    // TODO 该方法去掉
    public static Vehicle getVehicle() {
        return vehicle;
    }

    public static Vehicle getVehicle(String agvId) {
        return defaultVehiclePool.getVehicle(agvId);
    }

    public static Vehicle getVehicleByAgvCode(String agvCode) {
        List<Vehicle> vehicles = defaultVehiclePool.getAll().stream().filter(vehicle -> agvCode.equals(vehicle.getDeviceNumber())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicles)) {
            throw new ExecuteException("不存在该机器人缓存数据, agvCode:" + agvCode);
        }
        return vehicles.get(0);
    }

    /**
     * 获取在线的vehicle
     * @return
     */
    public static Vehicle getOnlineVehicle(String agvId) {
        Vehicle vehicle = getVehicle(agvId);
        if (vehicle == null) {//未连接成功
            throw new ExecuteException("不存在该机器人缓存数据, agvId:" + agvId);
        }
        return vehicle;
    }

    /**
     * 检测vehicle是否有智能任务(智能充电、智能归位)，有返回true，否者返回false
     * @return
     */
    public static boolean checkHasSmartTask() {
        Integer workStatus = vehicle.getWorkStatus();
        return TASK_STATUS_CHARGING.equals(workStatus) || TASK_STATUS_HOMING.equals(workStatus);
    }

    /**
     * 获取当前空闲时间(秒)
     *
     * @return
     */
    public static Double getCurrentFreeTime() {
        BaseAttribute attribute = AttributeFactory.createCompareAttribute(MISSION_ACTION_ATTRIBUTE_FREE_TIME);
        if (attribute == null) {
            throw new ExecuteException("create attribute is null by " + MISSION_ACTION_ATTRIBUTE_FREE_TIME);
        }
         Double attributeValue = attribute.getAttributeValue();
        LOGGER.debug("get current free time: " + attributeValue);
        return attributeValue;
    }

    /**
     * 校验vehicle是否处于空闲状态
     * 1、vehicle任务状态为 空闲
     * 2、vehicle控制模式为 自动模式
     * 3、vehicle录制状态为 未录制
     * 4、vehicle异常状态为 无异常
     * 5、vehicle地图状态为 正常
     * 6、vehicle连接状态为 已连接
     * 7、vehicle没有在执行 任务链
     *
     * @return
     */
    public static boolean checkVehicleFree(Vehicle vehicle) {
        return vehicle !=null && TASK_STATUS_FREE.equals(vehicle.getWorkStatus()) && AUTO_CONTROL_MODE.equals(vehicle.getControlMode())
                && RECORD_MAP_STATUS_NO.equals(vehicle.getRecordStatus())
                && MAP_STATUS_NORMAL.equals(vehicle.getMapStatus()) && CONNECT_STATUS_SUCCESS.equals(vehicle.getConnectStatus())
                && StringUtils.isEmpty(vehicle.getMissionWorkChainId());
    }

    /**
     * 检测vehicle的状态是否满足执行任务
     * @return
     */
    public static boolean checkVehicleStatusOnMissionWork(Vehicle vehicle) {
        return vehicle != null && AUTO_CONTROL_MODE.equals(vehicle.getControlMode()) && MAP_STATUS_NORMAL.equals(vehicle.getMapStatus())
                && TASK_STATUS_WORK.equals(vehicle.getWorkStatus()) && vehicle.getMissionWork() != null;
    }

    public static void recoverQueueAndWork(Vehicle vehicle) {
        /**
         * 检测AGV模式与状态
         */
        if (!VehicleUtils.checkVehicleFree(vehicle)) {
            return;
        }

        // 暂时不做任务链
//        String missionWorkChainId = null;
//        MissionWorkChain missionWorkChain = missionWorkChainService.selectOneByStatus(MISSION_WORK_CHAIN_STATUS_RUNNING);
//        if (missionWorkChain != null) {
//            missionWorkChainId = missionWorkChain.getId();
//            LOGGER.debug("recover mission chain :" + missionWorkChainId + " to vehicle");
//            vehicle.setMissionWorkChainId(missionWorkChainId);
//        }

        /**
         * 查看数据库中未执行完成的工作，该vehicle不绑定任务链或者该工作属于绑定的任务链
         */
        MissionWork missionWork = missionWorkService.selectExecutionIncomplete(vehicle.getId());//查询是否有未完成的任务
        if (missionWork != null && MISSION_WORK_ALLOCATION_STATUS_ASSIGNED.equals(missionWork.getAllocationStatus())
                /*&& (StringUtils.isEmpty(missionWorkChainId) || missionWorkChainId.equals(missionWork.getMissionWorkChainId()))*/) {
            LOGGER.debug("recover mission work :" + missionWork.getId() + " to vehicle");
            vehicle.setMissionWork(missionWork);
            vehicle.setWorkStatus(TASK_STATUS_WORK);
            MissionWorkThread missionWorkThread = (MissionWorkThread) ApplicationUtils.getBean("missionWorkThread");
            missionWorkThread.recoveryWork(missionWork);
        }
    }

    //目前场景一台机器人只在分配任务的时候触发该方法, 先去除synchronized
    public static /*synchronized*/ void interruptTasks(Vehicle vehicle, Integer newMissionSequence) {
        try {
            Integer workStatus = vehicle.getWorkStatus();
            if (TASK_STATUS_WORK.equals(workStatus)) {//任务中
                MissionWork missionWork = vehicle.getMissionWork();//获取执行的missionWork
                if (missionWork == null) {
                    LOGGER.error("内部数据错乱, 机器人状态为任务中却无任务数据！");
                    return;
                }
                if (newMissionSequence > missionWork.getSequence()) {//任务可中断且优先级低于新任务
                    MissionWorkThread missionWorkThread = missionWorkThreadPool.get(missionWork.getId());
                    if (missionWorkThread == null) {
                        // 可能需要停止的线程刚刚启动。任务还没有准备好。就开始新的任务会导致停止失败。
                        Thread.sleep(2000);
                        vehicle.setMissionWork(missionWork);
                        missionWorkThread = (MissionWorkThread) ApplicationUtils.getBean("missionWorkThread");
                        missionWorkThread.recoveryWork(missionWork);
                    }
                    missionWorkThread.stopWork();
                    LOGGER.debug("任务：" + missionWork.getName() + " 被优先级更高的新任务/充电中断!");
                }
            } else if (TASK_STATUS_CHARGING.equals(workStatus)) {//充电中
                //根据功能类型(智能充电)查询对应的agvFunctionConfig
                AGVFunctionConfig agvFunctionConfig = agvFunctionConfigService.selectByType(FUNCTION_TYPE_CHARGE);
                Double battery_value = vehicle.getDefaultVehicleStatus().getBattery().getBattery_value();//当前电量
                if (agvFunctionConfig != null && !StringUtils.isEmpty(agvFunctionConfig.getParameter())) {
                    JSONObject parameterJson = JSON.parseObject(agvFunctionConfig.getParameter());
                    Double interruptBatteryValue = parameterJson.getDouble("interruptBatteryValue");//可中断电量
                    if (battery_value >= interruptBatteryValue) {
                        vehicle.stopSmartTask();//停止智能充电
                        LOGGER.debug("充电被任务中断!");
                    }
                }
            } else if (TASK_STATUS_HOMING.equals(workStatus)) {//归位中
                vehicle.stopSmartTask();//停止智能归位
                LOGGER.debug("归位被任务/充电中断!");
            }
        } catch (Exception e) {
            LOGGER.error("中断任务/充电/归位失败, ", e);
        }
    }

    public static Vehicle createVehicle(AGV agv) {
        Vehicle vehicle = (Vehicle) ApplicationUtils.getBean("defaultVehicle");
        vehicle.initialize(agv);
        return vehicle;
    }

}
