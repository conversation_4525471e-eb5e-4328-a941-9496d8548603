package com.youibot.agv.scheduler.vehicle.module;

import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.engine.manager.execute.action.Action;
import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 21:22
 */
public interface VehicleCommand {

    Map<String, Object> executeCommand(Action action) throws ActionException, InterruptedException, IOException;

    void pauseCommand() throws ActionException;

    /**
     * 任务暂停后恢复执行任务（针对正常的任务或agv暂停情况）
     *
     * @return
     */
    void resumeCommand() throws ActionException;

    void stopCommand() throws ActionException, InterruptedException;

    void resetCommand() throws ActionException;

    /**
     * 停止智能（充电、归位）任务
     * @throws ActionException
     */
    void stopSmartTask() throws ActionException;

    /**
     * 停在在手动模式下发送的任务（页面点击的自由导航、手动充电等）
     */
    void stopManualTask() throws IOException, InterruptedException;

    /**
     * 重置（清理错误）手动任务
     */
    void resetManualTask() throws IOException, InterruptedException;

    /**
     * AGV一键恢复
     */
    void oneKeyResume() throws InterruptedException;

    /**
     * AGV一键停止
     */
    void oneKeyStop() throws InterruptedException;

    /**
     * AGV一键重置
     */
    void oneKeyReset();

}
