package com.youibot.agv.scheduler.vehicle.smart.wait;

import com.youibot.agv.scheduler.abnormal.AbnormalCodeConstant;
import com.youibot.agv.scheduler.abnormal.AbnormalMessageUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.mqtt.bean.callback.ParkMessage;

import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;


import static com.youibot.agv.scheduler.constant.AGVConstant.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/2 18:38
 */
@Service
@Scope("prototype")
public class SmartWaitingExecuteThread extends SmartWaitingThread {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartWaitingExecuteThread.class);

    @Autowired
    private SmartWaitingExecutes smartWaitingExecutes;

    @Override
    public void run() {
        while (true) {
            try {
                Thread.sleep(1000);
                this.allocationByScheduleMode();
            } catch (Exception e) {
                LOGGER.error("智能归位分配出错, " + e);
                try {
                    AbnormalMessageUtils.sendErrorCodeMessage(vehicle.getDeviceNumber(), AbnormalCodeConstant.THREAD_VEHICLE_SMART_WAIT_EXCEPTION.code());
                } catch (InterruptedException e1) {
                    LOGGER.error("智能归位分配出错,{} " + e1);
                }
            }
        }
    }

    private void allocationByScheduleMode() {
        if (TASK_STATUS_HOMING.equals(vehicle.getWorkStatus()) && vehicle.getParkMessage() != null && !vehicle.isSmartTaskStopNow()) {
            LOGGER.debug("检测到调度系统配分机器人去泊车！");
            ParkMessage homingMessage = vehicle.getParkMessage();
            Marker parkMarker = homingMessage.getParkMarker();
            isRunning = true;
            smartWaitingExecutes.smartWaiting(vehicle, parkMarker);
            isRunning = false;
        }
    }
}
