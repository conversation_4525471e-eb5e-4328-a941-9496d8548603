package com.youibot.agv.scheduler.vehicle;

import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.MapConstant.AGV_MAP_TYPE_LASER;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/7/18 15:49
 */
public abstract class MapVehicle extends Vehicle {

    private final static Logger LOGGER = LoggerFactory.getLogger(MapVehicle.class);

    @Value("${AGV_SOCKET.PORT.MAP}")
    private Integer mapPort;

    @Autowired
    private AGVService agvService;
    @Autowired
    private AGVMapService agvMapService;

    @Override
    public void initialize(AGV agv) {
        super.initialize(agv);
    }

    @Override
    public void initOnConnection() {
    }

    @Override
    public void manualRelocation(Map<String, Object> param) {
        if (!AGV_MAP_TYPE_LASER.equals(super.getAGVMapType())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.qr_map_not_support_operation"));
        }
        if (!MANUAL_CONTROL_MODE.equals(controlMode)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
        }
        if (!RECORD_MAP_STATUS_NO.equals(this.getRecordStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.recording_map"));
        }
        if (!MANUAL_ACTION_STATUS_NO.equals(manualStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.execute_action_now"));
        }
//        laserRelocationApiService.manualRelocation(ip, param);//手动重定位
    }

    @Override
    public void autoRelocation() {
        if (!AGV_MAP_TYPE_LASER.equals(super.getAGVMapType())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.qr_map_not_support_operation"));
        }
        if (!MANUAL_CONTROL_MODE.equals(controlMode)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
        }
        if (!RECORD_MAP_STATUS_NO.equals(this.getRecordStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.recording_map"));
        }
        if (!MANUAL_ACTION_STATUS_NO.equals(manualStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.execute_action_now"));
        }
        LOGGER.debug("激光地图自动重定位, ip : " + ip);
//        laserRelocationApiService.autoRelocation(ip);
    }

    @Override
    public Map<String, Object> recordHomeMarker(String agvMapId) {
        AGVMap agvMap = agvMapService.selectById(agvMapId);
        if (agvMap == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
        }
        if (!AGV_MAP_TYPE_LASER.equals(agvMap.getType())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.qr_map_not_support_operation"));
        }
        if (!MANUAL_CONTROL_MODE.equals(controlMode)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
        }
        if (!RECORD_MAP_STATUS_NO.equals(this.getRecordStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.recording_map"));
        }
        if (!MANUAL_ACTION_STATUS_NO.equals(manualStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.execute_action_now"));
        }
        return null;
//        return laserRelocationApiService.recordHomeMarker(ip);
    }

    @Override
    public Map<String, Object> findMaps() {
        return null;
    }

    @Override
    public Map<String, Object> findMap(String deviceMapId, Integer mapType) {
        return null;
    }

    @Override
    public void syncMap(String agvMapId) {
        AGVMap agvMap = agvMapService.selectById(agvMapId);
        if (agvMap == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
        }
        if (!MANUAL_CONTROL_MODE.equals(this.controlMode)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
        }
        if (!RECORD_MAP_STATUS_NO.equals(this.getRecordStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.recording_map"));
        }
        if (!MANUAL_ACTION_STATUS_NO.equals(this.manualStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.execute_action_now"));
        }
        super.agvMap = agvMap;
        agvService.updateAGVMapIdById(id, agvMapId);
//        defaultMapApiService.syncMapByAsync(this, agvMapId);
    }
}
