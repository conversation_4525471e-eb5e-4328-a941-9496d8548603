package com.youibot.agv.scheduler.vehicle.smart;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 智能任务(归位、充电)工具类
 *
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2020/3/2 18:27
 */
public class SmartTaskUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartTaskUtils.class);

    /*public static void stopSmartCharge(Vehicle vehicle) throws IOException, InterruptedException {
        //查询任务状态
        AGVSocketClient client = AGVSocketClient.createAGVClient(vehicle.getIp(), ActionUtils.getStatusPort());
        Map<String, Object> dataMap = ActionUtils.sendInstruction(client, AGVPropertiesUtils.getString("AGV_API_CODE.ACTION_STATUS"), null);
        LOGGER.debug("call agv query action status, ip :" + vehicle.getIp() + ", resultData:" + dataMap);
        if (dataMap == null || dataMap.isEmpty()) {
            return;//AGV刚开机没有执行过任务的情况下data为空
        }

        String id = (String) dataMap.get("id");
        Integer status = (Integer) dataMap.get("status");//执行状态
        String apiCode = (String) dataMap.get("code");//执行任务的apiCode
        if (!"SMART_CHARGE".equals(id)) {
            LOGGER.error("AGV正在执行的动作id不是智能充电, id = " + id);
            return;
        }

        if ((ApiCodeUtils.getMoveDocking().equals(apiCode))) {//对接

            if (ACTION_EXECUTE_RUNNING.equals(status)) {//执行中
                ActionUtils.stopAction(vehicle, apiCode);//停止指令
            } else if (ACTION_EXECUTE_SUCCESS.equals(status)){//执行成功
                SmartTaskUtils.leaveDocking(vehicle.getIp(), id);//脱离对接
            }

        } else if (ApiCodeUtils.getBatteryCharge().equals(apiCode)) {//充电

            if (ACTION_EXECUTE_RUNNING.equals(status)) {//执行中
                ActionUtils.stopAction(vehicle, apiCode);//停止指令
            }
            //获取AGV功能配置信息，查看智能充电是否对接，如果是对接充电，需要发送脱离对接指令
            AGVFunctionConfigService agvFunctionConfigService = (AGVFunctionConfigService) ApplicationUtils.getBean("AGVFunctionConfigServiceImpl");
            AGVFunctionConfig agvFunctionConfig = agvFunctionConfigService.selectByType(FUNCTION_TYPE_CHARGE);
            if (agvFunctionConfig != null && !StringUtils.isEmpty(agvFunctionConfig.getParameter())) {
                Integer chargeMode = JSONObject.parseObject(agvFunctionConfig.getParameter()).getInteger("chargeMode");//获取充电模式
                if (SMART_CHARGE_DOCKING_YES.equals(chargeMode)) {
                    SmartTaskUtils.leaveDocking(vehicle.getIp(), id);
                }
            }

        } else if (ApiCodeUtils.getLeaveDocking().equals(apiCode)) {//脱离对接

            if (ACTION_EXECUTE_RUNNING.equals(status)) {//执行中
                //查询脱离对接状态知道成功
                ActionUtils.getActionStatus(vehicle.getIp());
            } else if (ACTION_EXECUTE_FAULT.equals(status)) {
                SmartTaskUtils.leaveDocking(vehicle.getIp(), id);//再次重试脱离对接指令
            }

        } else if (ApiCodeUtils.getMoveSidePath().equals(apiCode)) {//路径导航

            if (ACTION_EXECUTE_RUNNING.equals(status) || ACTION_EXECUTE_PAUSE.equals(status)) {
                ActionUtils.stopAction(vehicle, apiCode);//停止指令
            }

        }
    }

    public static void stopSmartWait(Vehicle vehicle) throws IOException, InterruptedException {
        //查询任务状态
        AGVSocketClient client = AGVSocketClient.createAGVClient(vehicle.getIp(), ActionUtils.getStatusPort());
        Map<String, Object> dataMap = ActionUtils.sendInstruction(client, AGVPropertiesUtils.getString("AGV_API_CODE.ACTION_STATUS"), null);
        LOGGER.debug("call agv query action status, ip :" + vehicle.getIp() + ", resultData:" + dataMap);
        if (dataMap == null || dataMap.isEmpty()) {
            return;//AGV刚开机没有执行过任务的情况下data为空
        }

        Integer status = (Integer) dataMap.get("status");//执行状态
        if (!ACTION_EXECUTE_PAUSE.equals(status) && !ACTION_EXECUTE_RUNNING.equals(status)) {//AGV非执行动作状态，不发暂停指令
            LOGGER.warn("the action of agv is not pause or running");
            return;
        }

        String id = (String) dataMap.get("id");
        if (!"SMART_WAIT".equals(id)) {
            LOGGER.error("AGV正在执行的动作id不是智能归位, id = " + id);
            return;
        }

        ActionUtils.stopAction(vehicle, (String) dataMap.get("code"));//停止指令
    }

    *//**
     * 脱离对接指令
     *
     * @param ip
     * @param id
     * @throws IOException
     * @throws InterruptedException
     *//*
    private static void leaveDocking(String ip, String id) throws IOException, InterruptedException {
        JSONObject dockingParam = new JSONObject();
        dockingParam.put("id", id);
        LOGGER.debug("ip : " + ip + " send leave docking command. send data : " + dockingParam.toJSONString());
        ActionUtils.sendInstruction(ip, AGVPropertiesUtils.getString("AGV_API_CODE.LEAVE_DOCKING"), dockingParam.toJSONString());
        //查看agv脱离对接状态
        ActionUtils.getActionStatus(ip);
    }*/

}
