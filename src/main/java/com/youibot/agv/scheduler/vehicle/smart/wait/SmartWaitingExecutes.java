package com.youibot.agv.scheduler.vehicle.smart.wait;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.abnormal.AbnormalCodeConstant;
import com.youibot.agv.scheduler.abnormal.AbnormalMessageUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.bean.callback.ParkMessage;
import com.youibot.agv.scheduler.mqtt.bean.send.ParkResultMessage;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.mqtt.constant.MqErrorCode.PARK_RUNNING_ERROR;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.PARK_STATUS_ERROR;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.PARK_STATUS_SUCCESS;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_PARK;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/2 19:18
 */
@Service
public class SmartWaitingExecutes {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartWaitingExecutes.class);

    @Autowired
    MissionWorkService missionWorkService;

    public void smartWaiting(Vehicle vehicle, Marker waitMarker) {
        vehicle.setWorkStatus(TASK_STATUS_HOMING);//设置任务状态为归位中
        try {
            moveToMarker(vehicle, waitMarker);
            pushParkStatus(waitMarker.getId(), vehicle.getDeviceNumber(), PARK_STATUS_SUCCESS, null, null);
            vehicle.setWorkStatus(TASK_STATUS_FREE);//设置任务状态为空闲
        } catch (InterruptedException e) {
            LOGGER.warn("ip : " + vehicle.getIp() + " smart waiting thread is interrupted, ", e);
        } catch (Exception e) {
            LOGGER.error("smart waiting error, ", e);
            retryForever(vehicle);
        }
    }

    private void moveToMarker(Vehicle vehicle, Marker waitMarker) throws InterruptedException {
        try {
            //移动到充电点
            Map<String, Object> param = new HashMap<>();
            param.put("missionWorkActionId", "SMART_WAIT_MOVE_TO_MARKER");
            param.put("id", "SMART_WAIT");
            vehicle.moveToMarker(waitMarker.getId(), "SMART_WAIT", vehicle, param);
        } catch (InterruptedException e) {
            throw e;
        } catch (Exception e) {//智能泊车命令执行失败
            LOGGER.warn("ip : " + vehicle.getId() + " 移动到泊车点指令失败, ", e);
            AbnormalMessageUtils.sendErrorCodeMessage(vehicle.getDeviceNumber(), AbnormalCodeConstant.PILOT_ACTION_RUN_ERROR.code());
            throw new YOUIFleetException(AbnormalCodeConstant.PILOT_ACTION_RUN_ERROR.code(),MessageUtils.getMessage("vehicle.move_to_marker_fault") + " " + e.getMessage());
        }
    }

    private void pushParkStatus(String waitMarkerId, String agvCode, Integer status, Integer errorCode, String errorMessage) {
        try {
            ParkMessage parkMessage = VehicleUtils.getVehicleByAgvCode(agvCode).getParkMessage();
            ParkResultMessage parkResultMessage = new ParkResultMessage(parkMessage.getId(), agvCode, waitMarkerId, status, errorCode, errorMessage);
            MqttUtils.pushMessage(MQTT_PUBLISH_PARK, parkResultMessage);
        } catch (Exception e) {
            LOGGER.error("推送泊车状态出错, ", e);
        }
    }

    //调度模式 无限重试
    private void retryForever(Vehicle vehicle){
        LOGGER.debug("泊车执行异常，已上报异常码，接下来恢复异常状态，重新泊车，泊车信息：{}", JSON.toJSONString(vehicle.getParkMessage()));
        vehicle.setErrorMessage(null);
    }

}
