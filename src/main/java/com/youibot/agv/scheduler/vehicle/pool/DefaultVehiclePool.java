package com.youibot.agv.scheduler.vehicle.pool;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.thread.PushAGVStatusThread;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 13:57
 */

@Component
public class DefaultVehiclePool implements VehiclePool {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultVehiclePool.class);

    private Map<String, Vehicle> poolEntries = new HashMap<>();
    @Autowired
    private PushAGVStatusThread pushAGVStatusThread;

    @Override
    public Vehicle getVehicle(String agvId) {
        if (poolEntries.containsKey(agvId)) {
            return poolEntries.get(agvId);
        }
        return null;
    }

    @Override
    public synchronized void attachVehicle(AGV agv) {
        if (poolEntries.containsKey(agv.getId())) {
            return;
        }
        Vehicle vehicle = VehicleUtils.createVehicle(agv);
        if (vehicle != null) {
            this.attachVehicle(vehicle);
            LOGGER.debug("attach vehicle, id:{}", vehicle.getId());
        }
    }

    @Override
    public void attachVehicle(Vehicle vehicle) {
        poolEntries.put(vehicle.getId(), vehicle);
    }

    @Override
    public void detachVehicle(String id) {
        if (poolEntries.containsKey(id)) {
            Vehicle vehicle = poolEntries.get(id);
            poolEntries.remove(id);
            try {
                pushAGVStatusThread.stopThread(vehicle.getId());
            }catch (Exception e){
                LOGGER.error("stop agv status error {}",e);
            }
        }
    }

    @JsonIgnore
    @Override
    public List<Vehicle> getAll() {
        return new ArrayList<Vehicle>(this.poolEntries.values());
    }
}
