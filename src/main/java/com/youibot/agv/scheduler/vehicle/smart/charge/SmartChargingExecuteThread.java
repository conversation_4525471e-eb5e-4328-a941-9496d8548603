package com.youibot.agv.scheduler.vehicle.smart.charge;

import com.youibot.agv.scheduler.abnormal.AbnormalCodeConstant;
import com.youibot.agv.scheduler.abnormal.AbnormalMessageUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.mqtt.bean.callback.ChargeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/2 17:53
 */
@Service
@Scope("prototype")
public class SmartChargingExecuteThread extends SmartChargingThread {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartChargingExecuteThread.class);

    @Autowired
    private SmartChargingExecutes chargingExecutes;

    @Override
    public void run() {
        while (true) {
            try {
                //循环检测，每次睡眠1秒
                Thread.sleep(1000);
                this.allocationByScheduleMode();
            } catch (Exception e) {
                LOGGER.error("智能充电分配出错, " + e);
                try {
                    AbnormalMessageUtils.sendErrorCodeMessage(vehicle.getDeviceNumber(), AbnormalCodeConstant.THREAD_VEHICLE_SMART_CHARGE_EXCEPTION.code());
                } catch (InterruptedException e1) {
                    LOGGER.error("智能充电分配出错, " + e1);
                }
            }
        }
    }


    private void allocationByScheduleMode() {
        if (TASK_STATUS_CHARGING.equals(vehicle.getWorkStatus()) && vehicle.getChargeMessage() != null && !vehicle.isSmartTaskStopNow()) {
            LOGGER.debug("检测到调度系统配分机器人去充电！");
            ChargeMessage chargeMessage = vehicle.getChargeMessage();
            Marker chargeMarker = chargeMessage.getChargeMarker();
            isRunning = true;
            chargingExecutes.smartCharging(vehicle, chargeMarker);
            isRunning = false;
        }
    }
}
