package com.youibot.agv.scheduler.vehicle.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019/6/17 11:20
 */
@Data
@ApiModel(value = "VehicleDeviceInfo", description = "设备信息")
public class VehicleDeviceInfo {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "型号")
    private String model;

    @ApiModelProperty(value = "参数")
    private Map<String, Object> property;

}
