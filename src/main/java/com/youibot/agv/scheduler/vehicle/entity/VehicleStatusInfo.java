package com.youibot.agv.scheduler.vehicle.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.mqtt.bean.callback.PathPlanResultMessage;
import com.youibot.agv.scheduler.mqtt.bean.callback.ResourceResultMessage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * AGV 状态实体
 *
 * @author: huangguanxin
 * @date: 2020-11-06 17:52
 */
@Data
public class VehicleStatusInfo implements Serializable {

    private String id;

    @ApiModelProperty(value = "AGV名称", position = 1)
    private String name;

    @ApiModelProperty(value = "设备型号", position = 2)
    private String deviceModel;

    @ApiModelProperty(value = "设备编号", position = 3)
    private String agvCode;

    @ApiModelProperty(value = "AGV当前地图信息", position = 4)
    private AGVMap agvMap;

    @ApiModelProperty(value = "导航类型 LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航", position = 5)
    private String navigationType;

    @ApiModelProperty(value = "地图状态 1-正常 2-无地图 3-未同步 4-同步中 5-同步失败 6-未启用 ", position = 7)
    private Integer mapStatus;

    @ApiModelProperty(value = "任务状态 1-空闲 2-任务 3-充电 4-归位 5-任务异常", position = 8)
    private Integer workStatus;

    @ApiModelProperty(value = "AGV当前任务信息", position = 9)
    private MissionWork missionWork;

    @ApiModelProperty(value = "手动状态 1-无动作 2-动作中 3-动作异常", position = 11)
    private Integer manualStatus;

    @ApiModelProperty(value = "控制模式 1-自动模式 2-手动模式 3-录制模式", position = 12)
    private Integer controlMode;//控制模式

    @ApiModelProperty(value = "连接信号状态", position = 13)
    private Integer signalStatus;

    @ApiModelProperty(value = "调度状态 ONLINE-在线 OFFLINE-离线 DISCONNECTION-断开", position = 14)
    private String schedulerStatus;

    @ApiModelProperty(value = "在线时长（/秒） ", position = 15)
    private Long onlineTime;

    @ApiModelProperty(value = "调度地址Id", position = 15)
    private String schedulerSystemsId;

    @ApiModelProperty(value = "调度系统Url ", position = 16)
    private String schedulerSystemsUrl;

    @ApiModelProperty(value = "调度系统名称 ", position = 17)
    private String schedulerSystemsName;

    @JsonIgnore
    private String schedulerLoginStatus;//调度模式的登录状态

    @JsonIgnore
    private Integer syncMapStatus;//地图状态 0、未同步 1、已同步   (计算)

    @JsonIgnore
    private Integer syncLocationStatus;//定位状态 0、未定位 1、已定位    (计算)

    @JsonIgnore
    private List<Path> paths = new LinkedList<>();

    @JsonIgnore
    private LinkedList<SidePath> sidePaths = new LinkedList<>();

    @JsonIgnore
    private VehicleInfo vehicleInfo;// agv基本信息

    private DefaultVehicleStatus defaultVehicleStatus;// agv总状态信息

    private List<VehicleDeviceInfo> vehicleDeviceInfoList;// agv设备列表信息

    @JsonIgnore
    private String laserData;//点云数据

    @JsonIgnore
    private String errorMessage;//错误提示信息，目前只提示智能充电(归位)报错信息

//    private String missionWorkChainId;//任务链ID

    @JsonIgnore
    private boolean isSmartTaskStopNow;//是否正在停止智能任务

    @JsonIgnore
    private AGVLog onLineLog;//在线时间日志信息

    @JsonIgnore
    private AGVLog chargeLog;//充电时间日志信息

    @JsonIgnore
    private boolean resetManualTask;//是否已经重置手动任务错误

    @JsonIgnore
    private PathPlanResultMessage pathPlanResult;// 路径规划状态结果

    @JsonIgnore
    private ResourceResultMessage resourceResultMessage;//资源数据返回结果


}
