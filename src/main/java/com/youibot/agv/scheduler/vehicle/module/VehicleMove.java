package com.youibot.agv.scheduler.vehicle.module;

import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:s<PERSON><PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019/6/28 14:23
 */
public interface VehicleMove {

    /**
     * 移动到指定marker(二维码地图用二维码导航，激光地图用路径导航)
     */
    Map<String, Object> moveToMarker(String markerId, String missionWorkId, Vehicle vehicle, Map<String, Object> param) throws IOException, InterruptedException, PathPlanException;
}
