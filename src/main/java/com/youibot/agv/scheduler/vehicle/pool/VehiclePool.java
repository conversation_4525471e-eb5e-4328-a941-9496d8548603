package com.youibot.agv.scheduler.vehicle.pool;

import java.util.Collection;
import java.util.List;

import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.vehicle.Vehicle;

/**
 * <AUTHOR> E-mail:song<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019-05-14 13:54
 */
public interface VehiclePool {

    Vehicle getVehicle(String agvCode);

    void attachVehicle(Vehicle vehicle);

    void attachAGV(Agv agv);

    void detachVehicle(String agvCode);

    List<Vehicle> getAll();
    
    Collection<String> getAllAgvCodes();

    void updateVehicleByAgv(String agvCode);
}
