package com.youibot.agv.scheduler.vehicle.pool;

import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.util.List;

/**
 * <AUTHOR> E-mail:song<PERSON><EMAIL>
 * @version CreateTime: 2019-05-14 13:54
 */
public interface VehiclePool {

    Vehicle getVehicle(String agvId);

    void attachVehicle(Vehicle vehicle);

    void attachVehicle(AGV agv);

    void detachVehicle(String agvId);

    List<Vehicle> getAll();
}
