package com.youibot.agv.scheduler.vehicle.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Random;

@Data
@ApiModel(value = "DefaultVehicleStatus", description = "机器人状态")
public class DefaultVehicleStatus {

    @ApiModelProperty(value = "运行")
    private RuntimeStatus runtime = new RuntimeStatus();

    @ApiModelProperty(value = "地图")
    private MapStatus map = new MapStatus();

    @ApiModelProperty(value = "位置")
    private PositionStatus position = new PositionStatus();

    @ApiModelProperty(value = "速度")
    private SpeedStatus speed = new SpeedStatus();

    @ApiModelProperty(value = "电池")
    private BatteryStatus battery = new BatteryStatus();

    @ApiModelProperty(value = "电机")
    private EmecStatus emec = new EmecStatus();

    /*@ApiModelProperty(value = "导航")
    private NavigationStatus navigation;*/
    @ApiModelProperty(value = "防跌落")
    private FallPreventStatus fallPrevent;

    @ApiModelProperty(value = "升降平台")
    private LiftStatus lift;

    @JsonIgnore
    private RollerStatus roller;

    @Data
    @ApiModel(value = "RuntimeStatus", description = "运行")
    public class RuntimeStatus {
        @ApiModelProperty(value = "当前状态 1:空闲中,2:动作中 3,被阻档,4冲电中,5异常中,6急停7:正在进行重定位")
        private Integer agv_status;//当前状态1:空闲中,2:动作中3,被阻档,4冲电中,5异常中,6急停7:正在进行重定位
        @ApiModelProperty(value = "随动状态 0、无  1、顶升货架随动")
        private Integer follow_status = 0;
        @ApiModelProperty(value = "今天开机运行时间,单位:s")
        private Long time;//今天开机运行时间,s
        @ApiModelProperty(value = "累计运行时间, 单位:s")
        private Long total_time;//累计运行时间, 单位 s
        @ApiModelProperty(value = "累计行驶里程, 单位:km")
        private Double odom;//累计行驶里程, 单位 km
        @ApiModelProperty(value = "控制器当前温度")
        private Double controller_temp;//控制器当前温度
        @ApiModelProperty(value = "电机当前温度")
        private Double motor_temp;//电机当前温度
    }

    @Data
    @ApiModel(value = "MapStatus", description = "地图")
    public class MapStatus {
        @ApiModelProperty(value = "地图ID")
        private String current_map_id;//当前地图名(mapId+mapVersion)
        @ApiModelProperty(value = "地图类型 1:激光地图  2:虚拟地图  3:CAD地图")
        private Integer current_map_type;//地图类型 1:激光地图  2:虚拟地图  3:CAD地图
        @ApiModelProperty(value = "扫图状态  1：正在扫图  2：表示暂停中  3：表示建图完成")
        private Integer scan_status = 3;//扫图状态  1：正在扫图  2：表示暂停中  3：表示建图完成
        @ApiModelProperty(value = "特征地图状态  1：正在扫图  2：表示暂停中  3：表示建图完成")
        private Integer scan_feature_status = 3;//扫图状态  1：正在扫图  2：表示暂停中  3：表示建图完成
    }

    @Data
    @ApiModel(value = "PositionStatus", description = "位置")
    public static class PositionStatus {
        @ApiModelProperty(value = "X位置")
        private Double pos_x = 0.0;// X坐标
        @ApiModelProperty(value = "Y位置")
        private Double pos_y = 0.0;// y坐标
        @ApiModelProperty(value = "角度 单位:rad")
        private Double pos_angle = 0.0;// angle 坐标, 单位 rad
        @ApiModelProperty(value = "机器人激光定位的置信度, 范围 [0,1,2,3] 0-无 1-低 2-中 3-高")
        private Double pos_confidence = 3.0;//机器人激光定位的置信度, 范围 [0, 1]
        @ApiModelProperty(value = "当前是否有定位，0，未定位，1，已定位")
        private Integer pos_islocated = 1;
        @ApiModelProperty(value = "当前站点ID")
        private String pos_current_station = "-1";// 机器人当前所在站点的 ID
        @ApiModelProperty(value = "机器人在曲线上的位置")
        private Double t = 0.0;//曲线上的t值
        @ApiModelProperty(value = "机器人在那条曲线上")
        private String segment_id = "-1";//曲线上的t值
        @ApiModelProperty(value = "协方差矩阵")
        private Double covariance[] = new Double[]{0.0, 0.0, 0.0};

        public Double getPos_angle() {
            if (this.pos_angle != null) {
                return Math.toDegrees(pos_angle);//将弧度转换成角度
            }
            return null;
        }

        private Long update_time_millis;//数据更新时间，单位ms
    }

    @Data
    @ApiModel(value = "SpeedStatus", description = "速度")
    public static class SpeedStatus {
        @ApiModelProperty(value = "X方向实际的速度,单位 m/s")
        private Double speed_vx = 0.0;// 机器人在机器人坐标系的 x 方向实际的速度, 单位 m/s
        @ApiModelProperty(value = "Y方向实际的速度,单位 m/s")
        private Double speed_vy = 0.0;// 机器人在机器人坐标系的 y 方向实际的速度, 单位 m/s
        @ApiModelProperty(value = "实际的角速度,单位 rad/s")
        private Double speed_w = 0.0;// 机器人在机器人坐标系的实际的角速度(即顺时针转为负, 逆时针转为正), 单位 rad/s
        @ApiModelProperty(value = "X方向接收到的速度,单位 m/s")
        private Double speed_r_vx = 0.0;// 机器人在机器人坐标系的 x 方向接收到的速度, 单位 m/s
        @ApiModelProperty(value = "Y方向收到的速度,单位 m/s")
        private Double speed_r_vy = 0.0;// 机器人在机器人坐标系的 y 方向收到的速度, 单位 m/s
        @ApiModelProperty(value = "收到的角速度,单位 rad/s")
        private Double speed_r_w = 0.0;// 机器人在机器人坐标系的收到的角速度(即顺时针转为负, 逆时针转为正), 单位 rad/s
    }

    @Data
    @ApiModel(value = "BatteryStatus", description = "电池")
    public class BatteryStatus {
        @ApiModelProperty(value = "电池是否正在充电,1：充电，0：未充电")
        private Integer battery_status;//电池是否正在充电,1：充电，0：未充电
        @ApiModelProperty(value = "机器人电池电量, 范围 [0, 100]")
        private Double battery_value = (double) ((int) (new Random().nextDouble() * 80) + 20);// 机器人电池电量, 范围 [0, 100]
        @ApiModelProperty(value = "机器人电池电芯最高温度, 单位 ℃")
        private Double battery_temp;//机器人电池电芯最高温度, 单位 ℃
        @ApiModelProperty(value = "充电电流, 单位 A")
        private Double battery_charge = 0.0;// 充电电流, 单位 A
        @ApiModelProperty(value = "放电电流，单位A")
        private Double battery_discharge = 1.2;// 放电电流，单位A
        @ApiModelProperty(value = "电压, 单位 V")
        private Double battery_voltage = 52.308;// 电压, 单位 V
        @ApiModelProperty(value = "电池充满的电池电量 单位 mah")
        private Double battery_fullcapacity;//电池充满的电池电量 mah
        @ApiModelProperty(value = "电池剩余的电池电量 单位 mah")
        private Double battery_leftcapacity;//电池剩余的电池电量 mah
    }

    @Data
    @ApiModel(value = "EmecStatus", description = "电机")
    public class EmecStatus {
        @ApiModelProperty(value = "急停状态 0:未急停, 1:急停")
        private Integer emc_status = 0;// 0 未急停, 1表示急停
        @ApiModelProperty(value = "急停原因  1:表示急停按钮处于激活状态(按下) 2:安全激光雷达触发导致的急停 3:碰撞开类导致的急停 4:路径导航急停")
        private Integer emc_reason[];// 1 表示急停按钮处于激活状态(按下),2 安全激光雷达触发导致的急停，3 碰撞开类导致的急停
        @ApiModelProperty(value = "路径导航急停时间")
        private Long emc_jam_start_time;
    }

	/*@Data
	@ApiModel(value = "NavigationStatus", description = "导航")
	public class NavigationStatus {
		@ApiModelProperty(value = "AGV当前处于的控制模式，1：自动模式，2：手动模式  0:no mode")
		private Integer mode;//AGV当前处于的控制模式，1：自动模式，2：手动模式  0:no mode
		@ApiModelProperty(value = "AGV当前是否重定位成功，1：成功，0：失败")
		private Integer relocalize;//AGV当前是否重定位成功，1：成功，0：失败
		@ApiModelProperty(value = "机器人激光定位数据是否可用 1:可用 2:不可用")
		private Integer trust_location_flag;//机器人激光定位数据是否可用，1:可用，2：不可用
		@ApiModelProperty(value = "是否打开避障开关 1:开 0:关")
		private Integer obstacle_avoidance_switch;// 是否打开避障开关 1：开， 0：关
	}*/

    @Data
    @ApiModel(value = "LiftStatus", description = "升降平台")
    public class LiftStatus {
        @ApiModelProperty(value = "升降电机状态字")
        private Integer status_word;// 状态字
        @ApiModelProperty(value = "升降平台的当前位置脉冲数")
        private Long position;// 当前位置，脉冲值
        @ApiModelProperty(value = "升降平台是否找到原点  1、找到 0、未找到")
        private Integer origin_point;// 1:已经找到原点，0:正在找原点
        @ApiModelProperty(value = "升降平台是否触发限位 1、触发 0、未触发")
        private Integer limit;// 1:触发正负限位，0:没有触发正负限位
        @ApiModelProperty(value = "升降平台是否到达目标位置  1、到达 0、未到达")
        private Integer goal_reached;// 1:目标位置已经到达，0:目标位置未到达
        @ApiModelProperty(value = "升降平台是否急停  1、急停 0、未急停")
        private Integer emc_stop;// 1:急停触发， 0:未急停
        @ApiModelProperty(value = "升降平台是否报错  1、报错 0、未报错")
        private Integer controller_error;// 1:驱动器报错， 0:驱动器未报错
    }

    @Data
    public class RollerStatus {
        private Integer isForwardInFinished;//前方进料是否完成 1.是  0.否（下同）
        private Integer isForwardOutFinished;//前方出料是否完成
        private Integer isFeverseInFinished;//后方进料是否完成
        private Integer isFeverseOutFinished;//后方出料是否完成
        private Integer isFrontCylinderRiseFinished;//前电缸升起是否完成
        private Integer isFrontCylinderDeclineFinished;//前电缸下降是否完成
        private Integer isRearCylinderRiseFinished;//后电缸升起是否完成
        private Integer isRearCylinderDeclineFinished;//后电缸下降是否完成
        private Integer isFrontSensor;//前传感器是否触发
        private Integer isRearSensor;//后传感器是否触发
        private Integer isForwardPlaceFinished;//前归位是否完成
        private Integer isBackPlaceFinished;//后归位是否完成
        private Integer isEmergencyStop;//急停按钮是否触发
        private Integer ERROR0;//超时报警
        private Integer ERROR1;//超时报警
        private Integer ERROR2;//超时报警
    }

    @Data
    public class FallPreventStatus {
        private String status;
        private String switch_;
    }

}
