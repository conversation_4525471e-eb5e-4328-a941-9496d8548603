package com.youibot.agv.scheduler.vehicle.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "ShapeInfo", description = "外型")
public class ShapeInfo implements Serializable {

    @ApiModelProperty(value = "机器人长度 单位：mm")
    private Integer length;
    @ApiModelProperty(value = "机器人宽度 单位：mm")
    private Integer width;
    @ApiModelProperty(value = "机器人半径 单位：mm")
    private Integer radius;
}
