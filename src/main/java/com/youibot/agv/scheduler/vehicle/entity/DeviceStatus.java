package com.youibot.agv.scheduler.vehicle.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: Tianshu.CHU
 * @Date: 2020/12/22 9:43
 * @Description: 设备状态
 * "device_status":{
 * "motor_status": {
 * "left_status": 1,  // uint16，需按二进制解析每一位进行状态显示
 * "left_error": 1,  // uint16，需按二进制解析每一位进行状态显示
 * "right_status": 1,  // uint16，需按二进制解析每一位进行状态显示
 * "right_error": 1,  // uint16，需按二进制解析每一位进行状态显示
 * "rotate_status": 1,  // uint16，需按二进制解析每一位进行状态显示
 * "rotate_error": 1,  // uint16，需按二进制解析每一位进行状态显示
 * "lift_status": 1,  // uint16，需按二进制解析每一位进行状态显示
 * "lift_error": 1,  // uint16，需按二进制解析每一位进行状态显示
 * }
 * }
 */
@Data
public class DeviceStatus implements Serializable {


    private MotorStatus motor_status;


    @Data
    public static class MotorStatus implements Serializable {

        private int left_status;

        private int left_error;

        private int right_status;

        private int right_error;

        private int rotate_status;

        private int rotate_error;

        private int lift_status;

        private int lift_error;

    }
}
