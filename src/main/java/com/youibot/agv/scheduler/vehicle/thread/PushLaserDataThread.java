package com.youibot.agv.scheduler.vehicle.thread;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import com.youibot.agv.scheduler.webSocket.controller.LaserDataSocketController;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketSendCodeEnum;
import com.youibot.agv.scheduler.webSocket.module.SocketSendModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 推送激光数据线程
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/10/16 15:55
 */
@Service
@Deprecated
//@Scope("prototype")
public class PushLaserDataThread extends Thread {

    private final static Logger LOGGER = LoggerFactory.getLogger(PushLaserDataThread.class);

    private static final ObjectMapper webSocketMapper = new ObjectMapper();

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    private String agvCode;

    private boolean continueThread = true;//是否推送agv激光数据标记

    public void start(String agvCode) {
        this.agvCode = agvCode;
        this.start();
    }

    @Override
    public void run() {
        try {
            while (continueThread) {
                try {
                    Thread.sleep(330);//1s推送3次
                    Vehicle laserVehicle = defaultVehiclePool.getVehicle(agvCode);
                    if (laserVehicle == null || laserVehicle.isTerminate()) {
                        LOGGER.warn("vehicle断开连接！");
                        break;
                    }
                    SocketSendModel sendModel = new SocketSendModel();
                    sendModel.setCode(WebSocketSendCodeEnum.LASERDATA.getCode());
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("agvId", laserVehicle.getId());
//                    LOGGER.debug(laserVehicle.getLaserData());
                    dataMap.put("laserData", JSONObject.parseObject(laserVehicle.getLaserData()));
                    sendModel.setData(dataMap);
                    String message = webSocketMapper.writeValueAsString(sendModel);
                    LaserDataSocketController.sendMessage(message);
                } catch (JsonProcessingException e) {
                    LOGGER.error("Error in json parsing", e);
                } catch (Exception e) {
                    LOGGER.error("query laser data error, ", e);
                    try {
                        Thread.sleep(5000);//如果查询点云数据报错，停止5秒后再查询
                    } catch (InterruptedException e1) {
                        LOGGER.warn("thread sleep error");
                    }
                }
            }
            LOGGER.warn("push laser data thread complete, vehicle id : " + agvCode);
        } catch (Exception e) {
            LOGGER.error("push laser data error", e);
        }
        LOGGER.warn("push laser data thread complete, vehicle id : " + agvCode);
    }

    public void stopThread() {
        this.continueThread = false;
        LOGGER.debug("stop laser data thread, vehicle id : " + agvCode);
    }


}
