package com.youibot.agv.scheduler.vehicle.pool.impl;

import static com.youibot.agv.scheduler.constant.VehicleConstant.ABNORMAL_STATUS_NO;
import static com.youibot.agv.scheduler.constant.VehicleConstant.APPOINT_SUCCESS;
import static com.youibot.agv.scheduler.constant.VehicleConstant.AUTO_CONTROL_MODE;
import static com.youibot.agv.scheduler.constant.VehicleConstant.ENABLE;
import static com.youibot.agv.scheduler.constant.VehicleConstant.ONLINE;
import static com.youibot.agv.scheduler.constant.VehicleConstant.TASK_STATUS_FREE;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.HashBiMap;
import com.google.common.collect.Sets;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.entity.ChargeScheduler;
import com.youibot.agv.scheduler.entity.ParkScheduler;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.entity.WorkScheduler;
import com.youibot.agv.scheduler.service.ChargeSchedulerService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.ParkSchedulerService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.service.WorkSchedulerService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-12 16:26
 */
@Service
public class VehiclePoolServiceImpl implements VehiclePoolService {

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;

    @Autowired
    private ParkSchedulerService parkSchedulerService;

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    @Value("${scheduler.time.diff}")
    private String timeDiff;

    @Autowired
    private MarkerService markerService ;
    
    public List<Vehicle> getWaitWork() {
        List<Vehicle> vehicles = this.getAvailable();
        // 移除工作状态的机器人。
        vehicles = vehicles.stream().filter(x -> !VehicleConstant.TASK_STATUS_WORK.equals(x.getWorkStatus())).collect(Collectors.toList());

        // filter allocation vehicle 过滤已分配的机器人。
        List<WorkScheduler> workSchedulers = workSchedulerService.selectRunning();
        for (WorkScheduler workScheduler : workSchedulers) {
            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        // 移除当前低量小于最低可作业电量的机器人。
        vehicles = vehicles.stream().filter(x -> x.getBatteryValue() > schedulerConfig.getLowBatterValue()).collect(Collectors.toList());
        //过滤正在执行充电, 且已充电时间未达到最短充电时间的机器人
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            long chargeStartTime = chargeScheduler.getStartTime() != null ? chargeScheduler.getStartTime().getTime() : System.currentTimeMillis();
            long chargedTime = (System.currentTimeMillis() - chargeStartTime) / 1000;//已充电时间, 秒
            vehicles = vehicles.stream().filter(x -> !(chargeScheduler.getVehicleId().equals(x.getId())
                    && chargedTime < schedulerConfig.getMinimumChargeTime())).collect(Collectors.toList());
        }
        //过滤掉正在进行校正充电的机器人
        List<ChargeScheduler> correctChargeSchedulers = chargeSchedulerService.selectRunningCorrectCharge();
        if (!CollectionUtils.isEmpty(correctChargeSchedulers)) {
            for (ChargeScheduler correctChargeScheduler : correctChargeSchedulers) {
                vehicles = vehicles.stream().filter(x -> !(correctChargeScheduler.getVehicleId().equals(x.getId())
                        && correctChargeScheduler.getChargeType() == 1)).collect(Collectors.toList());
            }
        }

        return vehicles;
    }

    public List<Vehicle> getWaitWorkAndWorking(SchedulerConfig schedulerConfig) {
        List<Vehicle> vehicles = this.getAvailable();
        //过滤掉有预分配作业的机器人
//        List<WorkScheduler> workSchedulers = workSchedulerService.selectAllPreAllocationMissionWork();
//        for (WorkScheduler workScheduler : workSchedulers) {
//            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
//        }
        // 移除当前低量小于最低可作业电量的机器人。
        vehicles = vehicles.stream().filter(x -> x.getBatteryValue() > schedulerConfig.getLowBatterValue()).collect(Collectors.toList());
        //过滤正在执行充电, 且已充电时间未达到最短充电时间的机器人
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            long chargeStartTime = chargeScheduler.getStartTime() != null ? chargeScheduler.getStartTime().getTime() : System.currentTimeMillis();
            long chargedTime = (System.currentTimeMillis() - chargeStartTime) / 1000;//已充电时间, 秒
            vehicles = vehicles.stream().filter(x -> !(chargeScheduler.getVehicleId().equals(x.getId())
                    && chargedTime < schedulerConfig.getMinimumChargeTime())).collect(Collectors.toList());
        }
        //过滤掉正在进行校正充电的机器人
        List<ChargeScheduler> correctChargeSchedulers = chargeSchedulerService.selectRunningCorrectCharge();
        if (!CollectionUtils.isEmpty(correctChargeSchedulers)) {
            for (ChargeScheduler correctChargeScheduler : correctChargeSchedulers) {
                vehicles = vehicles.stream().filter(x -> !(correctChargeScheduler.getVehicleId().equals(x.getId())
                        && correctChargeScheduler.getChargeType() == 1)).collect(Collectors.toList());
            }
        }

        return vehicles;
    }


    public Vehicle selectById(String vehicleId) {
        return defaultVehiclePool.getVehicle(vehicleId);
    }

    /**
     * 获取所有的可以充电的机器人。
     *
     * @param
     * @return
     */
    public List<Vehicle> getWaitCharge() {
        List<Vehicle> vehicles = this.getAvailable();
        // 移除掉工作状态的车辆
        vehicles = vehicles.stream().filter(x -> !VehicleConstant.TASK_STATUS_WORK.equals(x.getWorkStatus())).collect(Collectors.toList());
        //过滤在执行乘梯任务的机器人
        vehicles = vehicles.stream().filter(vehicle -> !vehicle.isUseElevator()).collect(Collectors.toList());
        // 移除充电状态的车辆
        vehicles = vehicles.stream().filter(x -> !VehicleConstant.TASK_STATUS_CHARGE.equals(x.getWorkStatus())).collect(Collectors.toList());
        //移除掉十秒内取消充电的机器人
        List<String> vehicleIdList = chargeSchedulerService.selectCancelByFinishTimeDiff(timeDiff);
        if (!CollectionUtils.isEmpty(vehicleIdList)) {
            vehicles = vehicles.stream().filter(x -> !vehicleIdList.contains(x.getId())).collect(Collectors.toList());
        }
        // filter allocation vehicle 过滤已分配的机器人。
        List<WorkScheduler> workSchedulers = workSchedulerService.selectPrepareAndRunning();
        for (WorkScheduler workScheduler : workSchedulers) {
            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        // 过滤已分配充电的机器人。
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            vehicles = vehicles.stream().filter(x -> !chargeScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        //过滤掉关掉机器人自动充电的机器人
        vehicles = vehicles.stream().filter(Vehicle::isAutoCharge).collect(Collectors.toList());
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        // 过滤掉电量大于充电电量的。
        vehicles = vehicles.stream().filter(x -> x.getBatteryValue() <= schedulerConfig.getHighBatteryValue()).collect(Collectors.toList());
        // 过滤掉最近一次调度时间间隔小于配置的机器人。（获取机器人最后一次作业调度，充电调度，泊车调度）
        if (!CollectionUtils.isEmpty(vehicles) && schedulerConfig.getChargeSchedulerInterval() > 0) {
            Long currentTime = System.currentTimeMillis();
            Iterator<Vehicle> iterator = vehicles.iterator();
            while (iterator.hasNext()) {
                Vehicle vehicle = iterator.next();
                WorkScheduler workScheduler = workSchedulerService.selectLastOne(vehicle.getId());
                ParkScheduler parkScheduler = parkSchedulerService.selectLastOne(vehicle.getId());
                ChargeScheduler chargeScheduler = chargeSchedulerService.selectLastOne(vehicle.getId());
                Long workFinishedTime = workScheduler != null && workScheduler.getFinishTime() != null ? workScheduler.getFinishTime().getTime() : 0L;
                Long parkFinishedTime = parkScheduler != null && parkScheduler.getFinishTime() != null ? parkScheduler.getFinishTime().getTime() : 0L;
                Long chargeFinishedTime = chargeScheduler != null && chargeScheduler.getFinishTime() != null ? chargeScheduler.getFinishTime().getTime() : 0L;
                Long lastTime = Math.max(workFinishedTime, chargeFinishedTime);
                lastTime = Math.max(parkFinishedTime, lastTime);
                if (currentTime - lastTime < schedulerConfig.getChargeSchedulerInterval() * 1000) {
                    iterator.remove();
                }
            }
        }
        return vehicles;
    }

    /**
     * 获取所有需要泊车的机器人。
     *
     * @param
     * @return
     */
    public List<Vehicle> getWaitPark() {
        List<Vehicle> vehicles = this.getAvailable();
        // 过滤出空闲状态的机器人
        vehicles = vehicles.stream().filter(x -> TASK_STATUS_FREE.equals(x.getWorkStatus())).collect(Collectors.toList());
        // filter allocation vehicle 过滤已分配工作的机器人。
        List<WorkScheduler> workSchedulers = workSchedulerService.selectPrepareAndRunning();
        for (WorkScheduler workScheduler : workSchedulers) {
            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        // 过滤已分配充电的机器人。
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            vehicles = vehicles.stream().filter(x -> !chargeScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        // 过滤已分配泊车的机器人。
        List<ParkScheduler> parkSchedulers = parkSchedulerService.selectRunning();
        for (ParkScheduler parkScheduler : parkSchedulers) {
            vehicles = vehicles.stream().filter(x -> !parkScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        //过滤掉关掉机器人自动充电的机器人
        vehicles = vehicles.stream().filter(Vehicle::isAutoPark).collect(Collectors.toList());
        //过滤掉掉十秒内取消泊车的机器人
        List<String> vehicleIdList = parkSchedulerService.selectCancelByFinishTimeDiff(timeDiff);
        if (!CollectionUtils.isEmpty(vehicleIdList)) {
            vehicles = vehicles.stream().filter(x -> !vehicleIdList.contains(x.getId())).collect(Collectors.toList());
        }
        //过滤在执行乘梯任务的机器人
        vehicles = vehicles.stream().filter(vehicle -> !vehicle.isUseElevator()).collect(Collectors.toList());

        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        // 过滤掉最近一次调度时间间隔小于配置的机器人。（获取机器人最后一次作业调度，充电调度，泊车调度）
        if (!CollectionUtils.isEmpty(vehicles) && schedulerConfig.getParkSchedulerInterval() > 0) {
            Long currentTime = System.currentTimeMillis();
            Iterator<Vehicle> iterator = vehicles.iterator();
            while (iterator.hasNext()) {
                Vehicle vehicle = iterator.next();
                WorkScheduler workScheduler = workSchedulerService.selectLastOne(vehicle.getId());
                ParkScheduler parkScheduler = parkSchedulerService.selectLastOne(vehicle.getId());
                ChargeScheduler chargeScheduler = chargeSchedulerService.selectLastOne(vehicle.getId());
                Long workFinishedTime = workScheduler != null && workScheduler.getFinishTime() != null ? workScheduler.getFinishTime().getTime() : 0L;
                Long parkFinishedTime = parkScheduler != null && parkScheduler.getFinishTime() != null ? parkScheduler.getFinishTime().getTime() : 0L;
                Long chargeFinishedTime = chargeScheduler != null && chargeScheduler.getFinishTime() != null ? chargeScheduler.getFinishTime().getTime() : 0L;
                Long lastTime = Math.max(workFinishedTime, chargeFinishedTime);
                lastTime = Math.max(parkFinishedTime, lastTime);
                if (currentTime - lastTime < schedulerConfig.getParkSchedulerInterval() * 1000) {
                    iterator.remove();
                }
            }
        }
        return vehicles;
    }

    @Override
    public List<Vehicle> getAvailable() {
       
        return this.getAvailable( true );
    }

    @Override
    public List<Vehicle> getAvailable( boolean ignoreAbnormal) {
        List<Vehicle> vehicles = this.defaultVehiclePool.getAll();
        if (CollectionUtils.isEmpty(vehicles)) {
            return vehicles;
        }
        // filter auto module vehicle.
        vehicles = vehicles.stream().filter(x -> AUTO_CONTROL_MODE.equals(x.getControlMode())).collect(Collectors.toList());
        // filter enable status vehicle.
        vehicles = vehicles.stream().filter(x -> ENABLE.equals(x.getStatus())).collect(Collectors.toList());
        // filter normal status  vehicle.
        if( ignoreAbnormal) {
        	 vehicles = vehicles.stream().filter(x -> ABNORMAL_STATUS_NO.equals(x.getAbnormalStatus())).collect(Collectors.toList());
        }
       
        // filter success location vehicle.
        vehicles = vehicles.stream().filter(x -> APPOINT_SUCCESS.equals(x.getAppointStatus())).collect(Collectors.toList());
        // filter online status vehicle.
        vehicles = vehicles.stream().filter(x -> ONLINE.equals(x.getOnlineStatus())).collect(Collectors.toList());
        // 过滤掉电量为空的机器人
        vehicles = vehicles.stream().filter(x -> !Objects.isNull(x.getBatteryValue())).collect(Collectors.toList());
        // return all available vehicle.
        return vehicles;
    }
    
    @Override
    public void updateVehicle(Vehicle vehicle) {
        defaultVehiclePool.attachVehicle(vehicle);
    }

    @Override
    public List<Vehicle> selectAll() {
        return this.defaultVehiclePool.getAll();
    }

	/* 1. 通过传入的markCode 来判断这个工作站是否有车辆
	  * <p>Title: haveVehicleNextStation</p>
	  * <p>Description: </p>
	  * @param vehicle 车辆
	  * @param nextMarkCode 要去的下一个工作站
	  * @return
	  * @see com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService#haveVehicleNextStation(com.youibot.agv.scheduler.vehicle.Vehicle, java.lang.String)
	  */
	@Override
	public boolean haveVehicleNextStation(Vehicle vehicle, String nextMarkCode) {
		
		if(StringUtils.isBlank(nextMarkCode) || StringUtils.isBlank(vehicle.getAgvMapId())) {
			
			return false; 
		}
		HashBiMap<String, String> markerByCodes = markerService.selectMarkerByCodes(vehicle.getAgvMapId(),Sets.newHashSet( nextMarkCode) ) ;
		List<Vehicle> available = this.getAvailable();
		Set<String> allMakerIds = available.parallelStream().filter(p -> StringUtils.isNotBlank(p.getCurrentMarkerId())).map(Vehicle::getCurrentMarkerId).collect(Collectors.toSet());
		for (String markId : markerByCodes.values()) {
			if(allMakerIds.contains(markId)) {
				return true; 
			}
			
		}
		return false;
	}
   
	
}
