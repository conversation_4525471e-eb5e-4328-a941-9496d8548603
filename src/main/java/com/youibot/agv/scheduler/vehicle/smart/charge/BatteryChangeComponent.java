package com.youibot.agv.scheduler.vehicle.smart.charge;

import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.mqtt.bean.callback.ChargeMessage;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.pool.DefaultVehiclePool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;

/**
 * 仿真小车的电量变化处理
 * <AUTHOR>
 */
@Component
@Slf4j
public class BatteryChangeComponent implements CommandLineRunner {

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private LocationService locationService;

    //每4分钟掉百分之一的电
    private static final int BATTERY_LOST_SPEED_MINUTE = 4;

    //充电状态下，每分钟涨百分之一的电
    private static final int BATTERY_CHARGE_SPEED_MINUTE = 1;

    //电量最小值
    private static final int BATTERY_MIN = 1;

    //电量最大值
    private static final int BATTERY_MAX = 100;

    @Override
    public void run(String... args) throws Exception {

        //是否处于连接状态
        Predicate<Vehicle> isConnect = vehicle -> Objects.equals(vehicle.getConnectStatus(), CONNECT_STATUS_SUCCESS);
        //是否处于充电状态
        Predicate<Vehicle> isCharge = vehicle -> Objects.equals(vehicle.getWorkStatus(), TASK_STATUS_CHARGING);
        //是否在充电点上
        Predicate<Vehicle> inChargeMarker = vehicle -> {
            ChargeMessage chargeMessage = vehicle.getChargeMessage();
            if (chargeMessage == null) {
                return false;
            }
            Marker marker = locationService.getCurrentMarker(vehicle.getAGVMapId(), vehicle.getPositionStatus().getPos_x(), vehicle.getPositionStatus().getPos_y());
            if (marker == null) {
                return false;
            }
            return Objects.equals(marker.getId(), chargeMessage.getChargeMarkerId());
        };

        //处理仿真小车掉电
        new Thread(() -> {
            while (true) {
                try {
                    TimeUnit.MINUTES.sleep(BATTERY_LOST_SPEED_MINUTE);
                    List<Vehicle> vehicles = defaultVehiclePool.getAll();
                    vehicles = vehicles.stream().filter(v -> isConnect.and(isCharge.negate()).test(v)).collect(Collectors.toList());
                    vehicles.parallelStream().forEach(vehicle -> {
                        DefaultVehicleStatus.BatteryStatus battery = vehicle.getDefaultVehicleStatus().getBattery();
                        battery.setBattery_value(Math.max(battery.getBattery_value() - 1, BATTERY_MIN));
                    });
                } catch (Exception e) {
                    log.error("掉电处理异常 - ", e);
                }
            }
        }).start();

        //处理仿真小车充电
        new Thread(() -> {
            while (true) {
                try {
                    TimeUnit.MINUTES.sleep(BATTERY_CHARGE_SPEED_MINUTE);
                    List<Vehicle> vehicles = defaultVehiclePool.getAll();
                    vehicles = vehicles.stream().filter(v -> isConnect.and(isCharge).and(inChargeMarker).test(v)).collect(Collectors.toList());
                    vehicles.parallelStream().forEach(vehicle -> {
                        DefaultVehicleStatus.BatteryStatus battery = vehicle.getDefaultVehicleStatus().getBattery();
                        battery.setBattery_value(Math.min(battery.getBattery_value() + 1, BATTERY_MAX));
                        if (battery.getBattery_value() >= BATTERY_MAX) {
                            vehicle.setWorkStatus(TASK_STATUS_FREE);
                        }
                    });
                } catch (Exception e) {
                    log.error("充电处理异常 - ", e);
                }
            }
        }).start();
    }
}
