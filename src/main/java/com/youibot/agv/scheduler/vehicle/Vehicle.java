package com.youibot.agv.scheduler.vehicle;


import com.youibot.agv.scheduler.engine.entity.MapResourceCache;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.mqtt.bean.callback.ChargeMessage;
import com.youibot.agv.scheduler.mqtt.bean.callback.ParkMessage;
import com.youibot.agv.scheduler.mqtt.bean.callback.ResourceResultMessage;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.VehicleDeviceInfo;
import com.youibot.agv.scheduler.vehicle.entity.VehicleInfo;
import com.youibot.agv.scheduler.vehicle.module.*;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_LOGIN_STATUS_NO_LOGGED_IN;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_DIS_CONNECTION;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.MAP_NOT_SYNCHRONIZED;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.VEHICLE_UN_EXECUTABLE;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 20:54
 */
@Data
public abstract class Vehicle implements VehicleCommand, VehicleRelocation, VehicleModeControl, VehicleMap, VehicleMove {

    private final static Logger LOGGER = LoggerFactory.getLogger(Vehicle.class);

    @Autowired
    private PathService pathService;

    @Autowired
    private AGVMapService agvMapService;

    protected String id;

    protected String name;

    protected String deviceModel;//设备型号

    protected String deviceNumber;//设备编号

    protected String ip;

    protected AGVMap agvMap;

    protected String navigationType;//导航类型 LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航

    protected Integer mapStatus = MAP_STATUS_NO;//地图状态

    protected Integer workStatus = TASK_STATUS_FREE;//任务状态

    protected Integer recordStatus = RECORD_MAP_STATUS_NO;//录制状态

    protected Integer abnormalStatus = ABNORMAL_STATUS_NO;//异常状态

    protected Integer connectStatus = CONNECT_STATUS_SUCCESS;//连接状态(心跳，仿真模式已连接)

    protected Integer manualStatus = MANUAL_ACTION_STATUS_NO;//手动状态

    protected Double pos_confidence;//置信度状态

    protected Integer controlMode = MANUAL_CONTROL_MODE;//控制模式

    protected boolean useElevator = false;//是否正在使用电梯, 如果正在使用电梯，则不可自动中断任务/充电/归位

    private Integer signalStatus = 20;// 连接信号状态

    protected MissionWork missionWork;

    protected Date connectedTime = new Date();

    private List<Path> paths = new LinkedList<>();

    private List<SidePath> sidePaths = Collections.synchronizedList(new LinkedList<>());

    protected VehicleInfo vehicleInfo = new VehicleInfo();// agv基本信息

    protected DefaultVehicleStatus defaultVehicleStatus = new DefaultVehicleStatus();// agv总状态信息

    protected List<VehicleDeviceInfo> vehicleDeviceInfoList;// agv设备列表信息

    protected String laserData;//点云数据

    protected String errorMessage;//错误提示信息，目前只提示智能充电(归位)报错信息

    protected String missionWorkChainId;//任务链ID

    protected boolean isSmartTaskStopNow = false;//是否正在停止智能任务

    protected AGVLog onLineLog;//在线时间日志信息

    protected AGVLog chargeLog;//充电时间日志信息

    protected boolean resetManualTask = false;//是否已经重置手动任务错误

    //protected PathPlanResultMessage pathPlanResult;// 路径规划状态结果

    protected ResourceResultMessage resourceResultMessage;//资源数据返回结果

    protected String schedulerSystemsId;//调度地址id

    protected String schedulerStatus = SCHEDULER_ONLINE_STATUS_DIS_CONNECTION;// 调度状态

    protected String schedulerLoginStatus = SCHEDULER_LOGIN_STATUS_NO_LOGGED_IN;//调度模式的登录状态

    protected String schedulerLoginMessage;

    private Integer syncMapStatus = MAP_NOT_SYNCHRONIZED;//地图状态 0、未同步 1、已同步   (计算)

    private Integer syncLocationStatus = MAP_NOT_SYNCHRONIZED;//定位状态 0、未定位 1、已定位    (计算)

    private Integer isExecutable = VEHICLE_UN_EXECUTABLE;//是否可执行任务  0、不可执行  1、可执行   (计算)

    protected boolean cancelPathNavigation = false; //是否需要取消当前路径规划

    protected ParkMessage parkMessage;

    protected ChargeMessage chargeMessage;

    protected Long emc_time;

    protected Map<String,Object> mapUpdateMsg = null;//是否接收到地图发布的消息

//    private List<Path> executedPaths = new LinkedList<>();
//
////    private Colors colors = new Colors();

    public DefaultVehicleStatus.PositionStatus getPositionStatus() {
        if (defaultVehicleStatus != null) {
            return defaultVehicleStatus.getPosition();
        }
        return null;
    }

    public void initialize(AGV agv) {
        this.id = agv.getId();
        this.name = agv.getName();
        this.deviceModel = agv.getDeviceModel();
        this.deviceNumber = agv.getAgvCode();
        this.navigationType = agv.getNavigationType();
        this.schedulerSystemsId = agv.getSchedulerSystemsId();
//        String agvMapId = agv.getAgvMapId();
//        if (!StringUtils.isEmpty(agv.getAgvMapId())) {
//            MQTT_SYNC_MAP.forEach(mapData -> {
//                if (agvMapId.equals(mapData.getId())) {
//                    this.agvMap = mapData;
//                    return;
//                }
//            });
//        }
    }

    public abstract void initOnConnection();

    public void setSidePaths(List<SidePath> sidePaths) {
        List<Path> paths = new LinkedList<>();
        if (!CollectionUtils.isEmpty(sidePaths) && agvMap != null) {
            Set<Path> allPaths = MapResourceCache.getPathsByAGVMapId(agvMap.getId());
            allPaths.forEach(path -> {
                sidePaths.forEach(sidePath -> {
                    if (path.getId().equals(sidePath.getPathId()) && !paths.contains(path)) {
                        paths.add(path);
                    }
                });
            });
        }
        this.paths = paths;
        this.sidePaths = Collections.synchronizedList(sidePaths);
    }

    public String getAGVMapType() {
        if (agvMap == null) {
            return null;
        }
        return agvMap.getType();
    }

    public String getAGVMapId() {
        if (agvMap == null) {
            return null;
        }
        return agvMap.getId();
    }
}
