package com.youibot.agv.scheduler.vehicle.smart.charge;

import com.youibot.agv.scheduler.abnormal.AbnormalCodeConstant;
import com.youibot.agv.scheduler.abnormal.AbnormalMessageUtils;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.bean.callback.ChargeMessage;
import com.youibot.agv.scheduler.mqtt.bean.send.ChargeResultMessage;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.NotificationService;
import com.youibot.agv.scheduler.service.SystemWorkModeService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CopyOnWriteArrayList;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.mqtt.constant.MqErrorCode.CHARGE_STOP_ERROR;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.CHARGE_STATUS_ERROR;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.CHARGE_STATUS_STOP;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_CHARGE;

/**
 * 智能充电线程
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/2/18 10:23
 */

public class SmartChargingThread extends Thread {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartChargingThread.class);

    protected Vehicle vehicle;

    protected boolean isRunning = false;

    public void start(Vehicle vehicle) {
        this.vehicle = vehicle;
        this.start();
    }

    public void stopSmartCharging(Vehicle vehicle) {
        try {
            vehicle.setSmartTaskStopNow(true);
            this.interrupt();
            stopSmartCharge(vehicle);
        } catch (Exception e) {
            LOGGER.debug("stop command or leave docking error, ", e);
            vehicle.setErrorMessage("智能充电错误, " + e.getMessage());
            vehicle.setSmartTaskStopNow(false);
            pushChargeStatus(CHARGE_STATUS_ERROR, CHARGE_STOP_ERROR, e.getMessage());
            try {
                AbnormalMessageUtils.sendErrorCodeMessage(vehicle.getDeviceNumber(), AbnormalCodeConstant.PILOT_REJECT_FAILURE.code());
            } catch (InterruptedException e1) {
                LOGGER.debug("stop command error, ", e1);
            }
            throw new YOUIFleetException(AbnormalCodeConstant.PILOT_REJECT_FAILURE.code(),"停止智能充电失败, " + e.getMessage());
        }
        LOGGER.debug("stop command success");
        this.vehicle.setSidePaths(new CopyOnWriteArrayList<>());//清理路径
        vehicle.setErrorMessage(null);
        vehicle.setWorkStatus(TASK_STATUS_FREE);//设置任务状态为空闲
        vehicle.setSmartTaskStopNow(false);
        pushChargeStatus(CHARGE_STATUS_STOP, null, null);
        vehicle.setChargeMessage(null);
    }

    public void stopSmartCharge(Vehicle vehicle) {
        try {
            if (!TASK_STATUS_CHARGING.equals(vehicle.getWorkStatus())) {
                LOGGER.error("机器人当前状态不为充电！");
            }
            LOGGER.info("机器人：{},停止充电任务", vehicle.getDeviceNumber());
            vehicle.stopCommand();
        } catch (InterruptedException e) {
            throw new ExecuteException("停止充电任务失败!");
        }
    }

    private void pushChargeStatus(Integer status, Integer errorCode, String errorMessage) {
        try {
            ChargeMessage chargeMessage = vehicle.getChargeMessage();
            if (chargeMessage != null) {
                ChargeResultMessage chargeResultMessage = new ChargeResultMessage(vehicle.getDeviceNumber(), chargeMessage.getId(), chargeMessage.getChargeMarkerId(), status, errorCode, errorMessage);
                MqttUtils.pushMessage(MQTT_PUBLISH_CHARGE, chargeResultMessage);
            }
        } catch (Exception e) {
            LOGGER.error("推送充电状态出错, ", e);
        }
    }

    @Override
    public void interrupt() {
        super.interrupt();//线程中断
        long startTime = System.currentTimeMillis();
        //检测对应线程是否已经读取到中断,若线程中已经调用Thread.interrupted()方法，thread.isInterrupted()会重新置换为false
        while (this.isInterrupted() || isRunning) {
            LOGGER.debug("interrupt smart charging thread, ip: " + vehicle.getIp());
            if (System.currentTimeMillis() - startTime > 1500) {
                super.interrupt();
                startTime = System.currentTimeMillis();
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                LOGGER.error("thread sleep error,");
            }
            LOGGER.debug("thread detection uninterrupted. ");
        }
        LOGGER.debug("thread detection interrupted");
        try {
            Thread.sleep(500);//睡眠, 避免刚下发动作指令, 马上查询状态为空闲（pilot下发指令与反馈状态多线程）
        } catch (InterruptedException e) {
            LOGGER.error("thread sleep error");
        }
    }

}
