package com.youibot.agv.scheduler.vehicle;

import com.youibot.agv.scheduler.constant.AGVConstant;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.engine.manager.execute.action.Action;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThread;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThreadPool;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathSimulationService;
import com.youibot.agv.scheduler.engine.service.move.MoveApiService;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.smart.charge.SmartChargingThread;
import com.youibot.agv.scheduler.vehicle.smart.wait.SmartWaitingExecutes;
import com.youibot.agv.scheduler.vehicle.smart.wait.SmartWaitingThread;
import com.youibot.agv.scheduler.vehicle.thread.PushAGVStatusThread;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_PAUSE;
import static com.youibot.agv.scheduler.constant.PathSimulationConstant.PATH_SIMULATION_RUNNING;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_ONLINE;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 21:07
 */
@Data
@Component
@Scope("prototype")
public class DefaultVehicle extends MapVehicle {

    private final static Logger LOGGER = LoggerFactory.getLogger(DefaultVehicle.class);

    private Action action;//机器人当前执行的动作
    private boolean terminate = false;//是否已经调用terminate标记

    private PushAGVStatusThread pushAGVStatusThread;//webSocket推动AGV状态线程
    private SmartChargingThread smartChargingThread;//智能充电线程
    private SmartWaitingThread smartWaitingThread;//智能归位线程

    @Autowired
    private AGVService agvService;
    @Autowired
    private MissionWorkThreadPool missionWorkThreadPool;
    @Autowired
    private MissionWorkService missionWorkService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private MoveApiService moveApiService;
    @Autowired
    private AGVLogService agvLogService;
    @Autowired
    private AGVMapService agvMapService;
    @Autowired
    private MissionWorkChainService missionWorkChainService;
    @Autowired
    private SystemWorkModeService systemWorkModeService;
    @Autowired
    private PathPlanService pathPlanService;
    @Autowired
    private PathSimulationService pathSimulationService;

    @Override
    public void initialize(AGV agv) {
        super.initialize(agv);
        this.initStatus();//初始化AGV状态信息、线程
        this.initSmartCharging();//初始化智能充电线程
        this.initSmartWaiting();//初始化智能归位线程
//        this.initOnLineAGVLog();//初始化在线时间日志
    }

    @Override
    public void initOnConnection() {
        super.initOnConnection();
    }

//    private void initOnLineAGVLog() {
//        super.onLineLog = new AGVLog(AGV_LOG_TYPE_ON_LINE, new Date().getTime() / 1000);
//        agvLogService.insert(onLineLog);
//    }

    private void initSmartWaiting() {
        smartWaitingThread = (SmartWaitingThread) ApplicationUtils.getBean("smartWaitingExecuteThread");
        smartWaitingThread.start(this);
    }

    private void initSmartCharging() {
        smartChargingThread = (SmartChargingThread) ApplicationUtils.getBean("smartChargingExecuteThread");
        smartChargingThread.start(this);
    }

    private void initStatus() {
        pushAGVStatusThread = (PushAGVStatusThread) ApplicationUtils.getBean("pushAGVStatusThread");
        pushAGVStatusThread.start(this.getId());
    }

    @Override
    public Map<String, Object> moveToMarker(String markerId, String missionWorkId, Vehicle vehicle, Map<String, Object> param) throws InterruptedException, IOException {
        return moveApiService.moveToMarker(markerId, missionWorkId, vehicle, param);
    }

    @Override
    public Map<String, Object> executeCommand(Action action) throws InterruptedException, IOException {
        //需要vehicle为自动控制模式、正常状态
        if (!VehicleUtils.checkVehicleStatusOnMissionWork(this)) {
            LOGGER.error("vehicle状态不正确, mapStatus={}, missionWork={}, controlMode={}, workStatus={}",
                    mapStatus, missionWork, controlMode, workStatus);
            throw new ExecuteException(MessageUtils.getMessage("vehicle.no_normal_status_or_auto_mode"));
        }
        this.action = action;
        Map<String, Object> resultDataMap = action.execute(this);
        this.action = null;
        return resultDataMap;
    }

    @Override
    public void pauseCommand() {
        this.stopCommand();
    }

    @Override
    public void resumeCommand() {
    }

    @Override
    public void stopCommand() {
        try {
            pathSimulationService.queryPathSimulationState(deviceNumber);
            pathSimulationService.stopPathSimulation(deviceNumber);
        } catch (Exception e) {
            LOGGER.error("停止指令失败, ", e);
            throw new ExecuteException(MessageUtils.getMessage("vehicle.stop_action_fault") + " " + e.getMessage());
        }
    }

    @Override
    public void resetCommand() throws ActionException {
    }

    /**
     * 停止智能（充电、归位）任务
     */
    @Override
    public void stopSmartTask() {
        if (this.isSmartTaskStopNow) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.stop_smart_task_now"));
        }
        if (TASK_STATUS_CHARGING.equals(this.workStatus)) {//任务状态为充电
            smartChargingThread.stopSmartCharging(this);
        } else if (TASK_STATUS_HOMING.equals(this.workStatus)) {//任务状态为归位
            smartWaitingThread.stopSmartWaiting(this);
        } else {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.no_execute_smart_task"));
        }
    }

    @Override
    public void stopManualTask() throws IOException, InterruptedException {
        if (!MANUAL_CONTROL_MODE.equals(controlMode)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
        }
    }

    @Override
    public void resetManualTask() throws IOException, InterruptedException {
        if (!MANUAL_CONTROL_MODE.equals(controlMode)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
        }
    }

    /**
     * 切换为自动模式
     * 1、地图正常
     * 2、录制状态未开始
     * 3、没有在执行手动发送的动作（页面发送的自由导航等）
     */
    @Override
    public void switchAutoMode() {
        if (!SCHEDULER_ONLINE_STATUS_ONLINE.equals(super.schedulerStatus)) {
            throw new ExecuteException("机器人已断开调度，不可切自动模式！");
        }
        if (AUTO_CONTROL_MODE.equals(controlMode)) {
            return;//已经是自动模式
        }
        if (!MAP_STATUS_NORMAL.equals(super.mapStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.map_not_sync"));
        }
        if (!RECORD_MAP_STATUS_NO.equals(super.getRecordStatus())) {//如果agv正在录制地图，不能切换为自动模式
            throw new ExecuteException(MessageUtils.getMessage("vehicle.recording_map"));
        }
        if (!MANUAL_ACTION_STATUS_NO.equals(manualStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.execute_action_now"));
        }
        super.controlMode = AUTO_CONTROL_MODE;//自动控制模式
    }

    @Override
    public void switchManualMode() {
        /**
         * 检测AGV是否在执行任务且未出现异常
         */
        super.controlMode = MANUAL_CONTROL_MODE;//手动控制模式
    }

    @Override
    public void forceSwitchManualMode() throws InterruptedException {
        if (AUTO_CONTROL_MODE.equals(controlMode)) {//自动模式
            if (TASK_STATUS_WORK.equals(workStatus) && missionWork != null) {//任务中
                MissionWorkThread missionWorkThread = missionWorkThreadPool.get(missionWork.getId());
                if (missionWorkThread == null) {
                    LOGGER.debug("任务线程丢失, 强制切换未手工模式时恢复, missionWorkId:" + missionWork.getId() + ", name: " + missionWork.getName());
                    this.setMissionWork(missionWork);
                    missionWorkThread = (MissionWorkThread) ApplicationUtils.getBean("missionWorkThread");
                    missionWorkThread.recoveryWork(missionWork);
                }
                missionWorkThread.stopWork();
            } else if (VehicleUtils.checkHasSmartTask()) {//充电、归位中
                this.stopSmartTask();
            }
        }
        this.switchManualMode();//切换为手工模式
    }

    @Override
    public void switchRecordMode() {
        if (!TASK_STATUS_FREE.equals(super.workStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("http.agv_execute_task_now"));
        }
        if (!MANUAL_ACTION_STATUS_NO.equals(manualStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.execute_action_now"));
        }
        super.controlMode = RECORD_CONTROL_MODE;//录制模式
    }


    @Override
    public boolean manualMove(Double vx, Double vy, Double vtheta) {
        return true;
    }

    public void updateDefaultVehicleStatus(DefaultVehicleStatus defaultVehicleStatus) {
        if (defaultVehicleStatus == null) {
            return;
        }
        this.defaultVehicleStatus = defaultVehicleStatus;
        if (!MAP_STATUS_SYNC_FAULT.equals(super.mapStatus) && !MAP_STATUS_SYNC_RUNNING.equals(super.mapStatus)) {
            //如果地图状态不是同步失败或者同步中, 则根据查询AGV的总状态实时改变
            String current_map_id = defaultVehicleStatus.getMap().getCurrent_map_id();
            if (StringUtils.isEmpty(current_map_id)) {
                super.mapStatus = MAP_STATUS_NO;//无地图
                return;
            }
            boolean mapEnable = false;//AGV使用的地图在调度系统是否启用
            List<AGVMap> enableAGVMaps = agvMapService.getEnableAGVMaps();//获取所有启用地图
            for (AGVMap enableAGVMap : enableAGVMaps) {
                if (current_map_id.startsWith(enableAGVMap.getId())) {
                    mapEnable = true;
                    break;
                }
            }
            if (!mapEnable) {
                super.mapStatus = MAP_STATUS_NOT_ENABLE;//地图未启用
            } else if (agvMap == null || !current_map_id.equals(agvMap.getIdVersion())) {
                super.mapStatus = MAP_STATUS_NOT_SYNC;//地图未同步
            } else {
                super.mapStatus = MAP_STATUS_NORMAL;//正常
            }
        }
    }

    /**
     * 恢复机器人异常
     * 1、充电异常   恢复失败
     * 2、归位异常   恢复失败
     * 3、任务异常（暂停和异常）   尝试恢复（清除异常+停止任务）
     */
    @Override
    public void oneKeyResume() throws InterruptedException {
        if (!MISSION_WORK_STATUS_PAUSE.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_FAULT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_pause_or_fault"));
        }
        MissionWorkThread missionWorkThread = missionWorkThreadPool.get(missionWork.getId());
        if (missionWorkThread == null || !VehicleUtils.checkVehicleStatusOnMissionWork(this)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.not_ready_for_the_mission"));
        }
        missionWorkThread.resumeWork();
    }

    /**
     * 一键停止
     * 1、充电 --> 停止充电动作，置任务状态为空闲，控制模式切换为手工模式；
     * 2、归位 --> 停止归位动作，置任务状态为空闲，控制模式切换为手工模式；
     * 3、工作 --> 停止绑定的工作，置任务状态为空闲，控制模式切换为手工模式；
     */
    @Override
    public void oneKeyStop() throws InterruptedException {
        //1.异常状态下不允许停止
        if (!StringUtils.isEmpty(missionWorkChainId)) {
            missionWorkChainService.stopMissionWorkChain(missionWorkChainId);
        }
        if (TASK_STATUS_WORK.equals(workStatus)) {
            MissionWorkThread missionWorkThread = missionWorkThreadPool.get(missionWork.getId());
            if (missionWorkThread == null) {
                LOGGER.debug("任务线程丢失, 停止时恢复, missionWorkId:" + missionWork.getId() + ", name: " + missionWork.getName());
                this.setMissionWork(missionWork);
                missionWorkThread = (MissionWorkThread) ApplicationUtils.getBean("missionWorkThread");
                missionWorkThread.recoveryWork(missionWork);
            }
            missionWorkThread.stopWork();
        } else if (TASK_STATUS_CHARGING.equals(workStatus) || TASK_STATUS_HOMING.equals(workStatus)) {
            this.stopSmartTask();
        }
    }

    /**
     * 一键重置
     * 1、充电异常 --> 清理充电异常，置任务状态为空闲，控制模式切换为手工模式；
     * 2、归位异常 --> 清理归位异常，置任务状态为空闲，控制模式切换为手工模式；
     * 3、工作异常 --> 清理工作异常，置任务状态为空闲，置对应工作的状态为停止，控制模式切换为手工模式；
     */
    @Override
    public void oneKeyReset() {
        if (TASK_STATUS_CHARGING.equals(workStatus) || TASK_STATUS_HOMING.equals(workStatus)) {
            this.stopSmartTask();
        }
    }
}
