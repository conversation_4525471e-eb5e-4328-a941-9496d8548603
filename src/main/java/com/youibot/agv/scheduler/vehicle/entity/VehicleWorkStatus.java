package com.youibot.agv.scheduler.vehicle.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * AGV 工作状态统计
 *
 * @author: huangguanxin
 * @date: 2020-11-09 17:34
 */
@Data
public class VehicleWorkStatus implements Serializable {

    @ApiModelProperty(value = "总计", position = 1)
    private Integer total = 0;


    @ApiModelProperty(value = "任务数量", position = 2)
    private Integer workCount = 0;


    @ApiModelProperty(value = "空闲数量", position = 3)
    private Integer freeCount = 0;


    @ApiModelProperty(value = "充电数量", position = 5)
    private Integer chargeCount = 0;

    @ApiModelProperty(value = "未连接", position = 6)
    private Integer offlineCount = 0;

    @ApiModelProperty(value = "异常数量", position = 7)
    private Integer abnormalCount = 0;
}
