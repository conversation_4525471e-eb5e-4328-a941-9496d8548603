package com.youibot.agv.scheduler.vehicle.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/6/17 11:06
 */
@Data
@ApiModel(value = "VehicleDefaultInfo", description = "基本信息")
public class VehicleInfo {

    @ApiModelProperty(value = "AGV")
    private AGVInfo agv;

    @ApiModelProperty(value = "设备")
    private DeviceInfo device;

    @ApiModelProperty(value = "外型")
    private ShapeInfo shape = new ShapeInfo();

    @ApiModelProperty(value = "网络")
    private NetworkInfo network;

    @Data
    @ApiModel(value = "AGVInfo", description = "AGV")
    public class AGVInfo {
        @ApiModelProperty(value = "AGV ID")
        private String agv_id;
        @ApiModelProperty(value = "AGV名称")
        private String agv_name;
        @ApiModelProperty(value = "AGV版本号")
        private String agv_version;
    }

    @Data
    @ApiModel(value = "DeviceInfo", description = "设备")
    public class DeviceInfo {
        @ApiModelProperty(value = "电机型号")
        private String motor;
        @ApiModelProperty(value = "IMU型号")
        private String imu;
        @ApiModelProperty(value = "工控机型号")
        private String pc;
    }

    @Data
    @ApiModel(value = "ShapeInfo", description = "外型")
    public class ShapeInfo {
        @ApiModelProperty(value = "机器人长度 单位：mm")
        private Integer length = 800;
        @ApiModelProperty(value = "机器人宽度 单位：mm")
        private Integer width = 600;
        @ApiModelProperty(value = "机器人半径 单位：mm")
        private Integer radius;
    }

    @Data
    @ApiModel(value = "NetworkInfo", description = "网络")
    public class NetworkInfo {
        @ApiModelProperty(value = "网络协议版本")
        private String net_protocol_version;
        @ApiModelProperty(value = "机器人IP")
        private String net_ip;
        @ApiModelProperty(value = "机器人MAC地址")
        private String net_mac;
        @ApiModelProperty(value = "机器人连接的AP名称")
        private String net_wifi_ssid;
        @ApiModelProperty(value = "机器人当前连接的信号强度")
        private String net_wifi_rssi;
    }

}
