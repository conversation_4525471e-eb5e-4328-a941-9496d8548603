package com.youibot.agv.scheduler.vehicle.smart.charge;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.abnormal.AbnormalCodeConstant;
import com.youibot.agv.scheduler.abnormal.AbnormalMessageUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.bean.callback.ChargeMessage;
import com.youibot.agv.scheduler.mqtt.bean.send.ChargeResultMessage;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.CHARGE_STATUS_STOP;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.CHARGE_STATUS_SUCCESS;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_CHARGE;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/2 18:01
 */
@Service
public class SmartChargingExecutes {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartChargingExecutes.class);

    /**
     * 智能充电执行
     * 1、移动到充电点      包括查询执行状态，完成后才执行下一步，下同
     *
     * @param chargeMarker
     */
    public void smartCharging(Vehicle vehicle, Marker chargeMarker) {
        //设置任务状态为充电中
        vehicle.setWorkStatus(TASK_STATUS_CHARGING);
        try {
            moveToMarker(vehicle, chargeMarker);
            while (true) {
                if (vehicle.getDefaultVehicleStatus().getBattery().getBattery_value() >= 100) {
                    pushChargeStatus(chargeMarker.getId(), vehicle.getDeviceNumber(), CHARGE_STATUS_SUCCESS, null, null);
                    break;
                }
                if (Objects.equals(vehicle.getWorkStatus(), TASK_STATUS_FREE)) {
                    pushChargeStatus(chargeMarker.getId(), vehicle.getDeviceNumber(), CHARGE_STATUS_STOP, null, null);
                    break;
                }
                Thread.sleep(1000);
            }
            //设置任务状态为空闲
            vehicle.setWorkStatus(TASK_STATUS_FREE);
        } catch (InterruptedException e) {
            pushChargeStatus(chargeMarker.getId(), vehicle.getDeviceNumber(), CHARGE_STATUS_STOP, null, null);
            LOGGER.warn("ip : " + vehicle.getIp() + " smart charging thread is interrupted, ", e);
        } catch (Exception e) {
            LOGGER.error("smart charging error, ", e);
            retryForever(vehicle);
        }
    }

    private void moveToMarker(Vehicle vehicle, Marker chargeMarker) throws InterruptedException {
        try {
            //移动到充电点
            Map<String, Object> param = new HashMap<>();
            param.put("missionWorkActionId", "SMART_CHARGE_MOVE_TO_MARKER");
            param.put("id", "SMART_CHARGE");
            vehicle.moveToMarker(chargeMarker.getId(), "SMART_CHARGE", vehicle, param);
        } catch (InterruptedException e) {
            throw e;
        } catch (Exception e) {//智能充电命令执行失败
            LOGGER.warn("ip : " + vehicle.getId() + " 移动到充电点指令失败, ", e);
            AbnormalMessageUtils.sendErrorCodeMessage(vehicle.getDeviceNumber(), AbnormalCodeConstant.PILOT_ACTION_RUN_ERROR.code());
            throw new YOUIFleetException(AbnormalCodeConstant.PILOT_ACTION_RUN_ERROR.code(),MessageUtils.getMessage("vehicle.move_to_marker_fault") + " " + e.getMessage());
        }
    }

    private void pushChargeStatus(String chargeMarkerId, String agvCode, Integer status, Integer errorCode, String errorMessage) {
        try {
            ChargeMessage chargeMessage = VehicleUtils.getVehicleByAgvCode(agvCode).getChargeMessage();
            ChargeResultMessage chargeResultMessage = new ChargeResultMessage(agvCode, chargeMessage.getId(), chargeMarkerId, status, errorCode, errorMessage);
            MqttUtils.pushMessage(MQTT_PUBLISH_CHARGE, chargeResultMessage);
        } catch (Exception e) {
            LOGGER.error("推送充电状态出错, ", e);
        }
    }

    //调度模式 无限重试
    private void retryForever(Vehicle vehicle){
        LOGGER.debug("充电执行异常，恢复异常状态，重新充电，充电信息：{}", JSON.toJSONString(vehicle.getChargeMessage()));
        vehicle.setErrorMessage(null);
    }
}
