package com.youibot.agv.scheduler.vehicle.thread;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.youibot.agv.scheduler.entity.VehicleData;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import com.youibot.agv.scheduler.webSocket.controller.VehicleStatusSocketController;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketSendCodeEnum;
import com.youibot.agv.scheduler.webSocket.module.SocketSendModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.websocket.Session;
import java.util.*;


/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/4/8 15:54
 */
public class PushAGVStatusThread extends Thread {

    private Logger LOGGER = LoggerFactory.getLogger(PushAGVStatusThread.class);

    private ObjectMapper webSocketMapper = new ObjectMapper();

    private DefaultVehiclePool defaultVehiclePool;

    //初始化websocket session
    private Session websocketSession;

    public PushAGVStatusThread(Session websocketSession, DefaultVehiclePool defaultVehiclePool) {
        this.websocketSession = websocketSession;
        this.defaultVehiclePool = defaultVehiclePool;
    }

    @Override
    public void run() {
        if (websocketSession == null) {
            LOGGER.error("机器人websocket session is null");
            return;
        }
        while (websocketSession.isOpen()) {
            try {
                List<Vehicle> vehicles = defaultVehiclePool.getAll();
                if (CollectionUtils.isEmpty(vehicles)) {
                    Thread.sleep(1000);
                    continue;
                }
                SocketSendModel sendModel = new SocketSendModel();
                sendModel.setCode(WebSocketSendCodeEnum.AGVSTATUS.getCode());
                for (Vehicle vehicle : vehicles) {
                    if (vehicle.isTerminate() || vehicle.getDefaultVehicleStatus() == null) {
                        continue;
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("AGVStatus", new VehicleData(vehicle));
                    sendModel.setData(map);
                    String message = webSocketMapper.writeValueAsString(sendModel);
                    VehicleStatusSocketController.sendMessage(websocketSession, message);
                }
                Thread.sleep(500);
            } catch (Exception e) {
                LOGGER.error("push agv status error", e);
            }
        }
    }

}
