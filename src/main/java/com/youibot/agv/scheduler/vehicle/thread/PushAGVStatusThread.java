package com.youibot.agv.scheduler.vehicle.thread;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.youibot.agv.scheduler.engine.entity.PathNavigationParam;
import com.youibot.agv.scheduler.engine.pathplan.service.PathSimulationService;
import com.youibot.agv.scheduler.engine.pathplan.service.impl.PathSimulationServiceImpl;
import com.youibot.agv.scheduler.entity.VehicleData;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.DefaultVehiclePool;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import com.youibot.agv.scheduler.webSocket.controller.VehicleStatusSocketController;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketSendCodeEnum;
import com.youibot.agv.scheduler.webSocket.module.SocketSendModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_ONLINE;


/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/4/8 15:54
 */
@Service
@Scope("prototype")
public class PushAGVStatusThread extends Thread {

    private final static Logger LOGGER = LoggerFactory.getLogger(PushAGVStatusThread.class);

    private static final ObjectMapper webSocketMapper = new ObjectMapper();

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;
    @Autowired
    private PathSimulationService pathSimulationService;

    private String agvId;

    private boolean continueThread = true;//是否推送agv总状态线程

    public void start(String agvId) {
        this.agvId = agvId;
        this.start();
    }

    @Override
    public void run() {
        SocketSendModel sendModel = new SocketSendModel();
        sendModel.setCode(WebSocketSendCodeEnum.AGVSTATUS.getCode());
        while (continueThread) {
            try {
                Thread.sleep(500);
                Vehicle vehicle = VehicleUtils.getVehicle(agvId);
                if (vehicle == null || !SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus()) || vehicle.getDefaultVehicleStatus() == null) {
                    continue;
                }
                List<PathNavigationParam.segment> segmentLinkedList = pathSimulationService.getAgvCodeToSegmentQueue(vehicle.getDeviceNumber());
                Map<String, Object> map = new HashMap<>();
                VehicleData vehicleData = new VehicleData(vehicle, segmentLinkedList);
                vehicleData.setId(vehicle.getDeviceNumber());
                map.put("AGVStatus", vehicleData);
                sendModel.setData(map);
                String message = webSocketMapper.writeValueAsString(sendModel);
                VehicleStatusSocketController.sendMessage(message);
            } catch (Exception e) {
                LOGGER.error("push agv status error", e);
            }
        }
        LOGGER.debug("push agv status thread complete, vehicle id : " + agvId);
    }

    public void stopThread(String agvId) {
        this.continueThread = false;
        LOGGER.debug("stop push agv status thread, vehicle id : " + agvId);
    }

}
