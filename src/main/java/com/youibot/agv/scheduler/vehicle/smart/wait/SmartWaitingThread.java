package com.youibot.agv.scheduler.vehicle.smart.wait;

import com.youibot.agv.scheduler.abnormal.AbnormalCodeConstant;
import com.youibot.agv.scheduler.abnormal.AbnormalMessageUtils;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.bean.callback.ParkMessage;
import com.youibot.agv.scheduler.mqtt.bean.send.ParkResultMessage;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.smart.SmartTaskUtils;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.LinkedList;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.mqtt.constant.MqErrorCode.PARK_STOP_ERROR;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.PARK_STATUS_ERROR;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.PARK_STATUS_STOP;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.PARK_STATUS_SUCCESS;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_PARK;

/**
 * 智能归位线程
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/2/20 17:56
 */
@Service
@Scope("prototype")
public class SmartWaitingThread extends Thread {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartWaitingThread.class);

    protected Vehicle vehicle;
    protected boolean isRunning = false;

    public void start(Vehicle vehicle) {
        this.vehicle = vehicle;
        this.start();
    }

    public void stopSmartWaiting(Vehicle vehicle) {
        try {
            vehicle.setSmartTaskStopNow(true);
            this.interrupt();//线程中断
            LOGGER.debug("stop smart waiting, vehicle id: " + vehicle.getId());
            stopSmartWait(vehicle);//发送停止重置指令

        } catch (Exception e) {
            LOGGER.debug("stop command error, ", e);
            vehicle.setErrorMessage("智能归位错误, " + e.getMessage());
            vehicle.setSmartTaskStopNow(false);
            pushParkStatus(vehicle.getDeviceNumber(),PARK_STATUS_ERROR, AbnormalCodeConstant.PILOT_REJECT_FAILURE.code(), AbnormalCodeConstant.PILOT_REJECT_FAILURE.msg());
            try {
                AbnormalMessageUtils.sendErrorCodeMessage(vehicle.getDeviceNumber(), AbnormalCodeConstant.PILOT_REJECT_FAILURE.code());
            } catch (InterruptedException e1) {
                LOGGER.debug("stop command error, ", e1);
            }
            throw new ExecuteException("停止智能归位失败, " + e.getMessage());
        }
        LOGGER.debug("stop command success");
        vehicle.setSidePaths(new LinkedList<>());//清理路径
        vehicle.setErrorMessage(null);
        vehicle.setSmartTaskStopNow(false);
        pushParkStatus(vehicle.getDeviceNumber(), PARK_STATUS_STOP, null, null);
        vehicle.setWorkStatus(TASK_STATUS_FREE);//设置任务状态为空闲
    }

    private void pushParkStatus( String agvCode,Integer status,Integer errorCode, String errorMessage) {
        try {
            ParkMessage parkMessage = vehicle.getParkMessage();
            if (parkMessage != null) {
                ParkResultMessage parkResultMessage = new ParkResultMessage(parkMessage.getId(),agvCode, parkMessage.getParkMarkerId(), status, errorCode, errorMessage);
                MqttUtils.pushMessage(MQTT_PUBLISH_PARK, parkResultMessage);
            }
        } catch (Exception e) {
            LOGGER.error("推送泊车状态出错, ", e);
        }
    }


    public void stopSmartWait(Vehicle vehicle) {
        try {
            if (!TASK_STATUS_HOMING.equals(vehicle.getWorkStatus())) {
                LOGGER.error("机器人当前状态不为泊车！");
            }
            LOGGER.info("机器人：{},停止泊车任务", vehicle.getId());
            vehicle.stopCommand();
        } catch (InterruptedException e) {
            throw new ExecuteException("停止泊车任务失败!");
        }
    }

    @Override
    public void interrupt() {
        super.interrupt();//线程中断
        long startTime = System.currentTimeMillis();
        //检测对应线程是否已经读取到中断,若线程中已经调用Thread.interrupted()方法，thread.isInterrupted()会重新置换为false
        while (this.isInterrupted() || isRunning) {
            LOGGER.debug("interrupt smart waiting thread, ip: " + vehicle.getIp());
            if (System.currentTimeMillis() - startTime > 1500) {
                super.interrupt();
                startTime = System.currentTimeMillis();
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                LOGGER.error("thread sleep error,");
            }
            LOGGER.debug("thread detection uninterrupted. ");
        }
        LOGGER.debug("thread detection interrupted");
    }
}
