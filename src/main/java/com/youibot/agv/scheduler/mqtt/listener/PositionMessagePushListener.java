package com.youibot.agv.scheduler.mqtt.listener;

import com.youibot.agv.scheduler.constant.SystemWorkModeConstant;
import com.youibot.agv.scheduler.mqtt.bean.send.PositionMessage;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_POSITION;

/**
 * {阐述类的作用}
 *
 * @author: huangguanxin
 * @date: 2020-12-11 14:35
 */
@Component
public class PositionMessagePushListener {
    private Logger logger = LoggerFactory.getLogger(PositionMessagePushListener.class);

    public void positionMessagePush(String agvCode) throws InterruptedException {
        Vehicle vehicle = VehicleUtils.getVehicleByAgvCode(agvCode);
        this.positionMessagePush(vehicle);
    }

    @Async("positionMessagePush")
    public void positionMessagePush(Vehicle vehicle) throws InterruptedException {
        if (vehicle.getSchedulerStatus().equals(SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_ONLINE)) {
            if (MqttUtils.getClient(vehicle.getDeviceNumber()) == null || !MqttUtils.getClient(vehicle.getDeviceNumber()).isConnected()) {
                return;
            }
            PositionMessage positionMessage = new PositionMessage();
            positionMessage.setValue(vehicle);
            MqttUtils.pushMessage(MQTT_PUBLISH_POSITION, positionMessage);
//            logger.debug(">>>>>" + JSON.toJSONString(positionMessage));
        }
    }
}