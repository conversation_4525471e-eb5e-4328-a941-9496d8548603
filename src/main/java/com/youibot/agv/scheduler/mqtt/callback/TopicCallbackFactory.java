package com.youibot.agv.scheduler.mqtt.callback;

import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.callback.impl.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.*;

public class TopicCallbackFactory {

    private static final Logger logger = LoggerFactory.getLogger(TopicCallbackFactory.class);

    private static final Map<String, String> topicsToClassName = new HashMap<String, String>() {
        {
            put(MQTT_CALLBACK_LOGIN, UserLoginCallback.class.getName());
            put(MQTT_CALLBACK_LOGOUT, UserLogoutCallback.class.getName());
            put(MQTT_CALLBACK_COMMAND,CommandCallback.class.getName());
            put(MQTT_CALLBACK_MISSION, MissionCallback.class.getName());
            put(MQTT_CALLBACK_PATH_PLAN, PathPlanCallback.class.getName());
            put(MQTT_CALLBACK_SIDE_PATH, SidePathCallback.class.getName());
            put(MQTT_CALLBACK_MAP_PUSH,MapPushCallback.class.getName());
            put(MQTT_CALLBACK_MAP_CURRENT,MapCurrentCallback.class.getName());
            put(MQTT_CALLBACK_MAP_UPDATE,MapUpdateCallback.class.getName());
            put(MQTT_CALLBACK_RELOCATION,RelocationCallback.class.getName());
            put(MQTT_CALLBACK_MISSION_COMMAND,MissionOperationCallback.class.getName());
            put(MQTT_CALLBACK_RESOURCE, ResourceCallback.class.getName());
            put(MQTT_CALLBACK_MISSION_JUDGE, MissionExecuteJudgeCallback.class.getName());
            put(MQTT_CALLBACK_BATCH_OPERATE,BatchOperateCallback.class.getName());
            put(MQTT_CALLBACK_RE_LOGIN,ReLoginCallback.class.getName());
            put(MQTT_CALLBACK_PARK, ParkCallback.class.getName());
            put(MQTT_CALLBACK_CHARGE, ChargeCallback.class.getName());
            put(MQTT_CALLBACK_MAP_UPDATE_MSG, MapUpdateMsgCallback.class.getName());
        }
    };

    public static TopicCallback createTopicCallback(String topicSuffix) {
        try {
            String className = topicsToClassName.get(topicSuffix);
            if (className == null) {

                logger.error("未找到mqtt回调处理类, topicSuffix:{}", topicSuffix);
             return null;
            }
            Class callback = Class.forName(className);
            return (TopicCallback) callback.newInstance();
        } catch (ClassNotFoundException | InstantiationException | IllegalAccessException e) {
            logger.error("创建mqtt回调处理类出错, topicSuffix:{}", topicSuffix, e);
            throw new ExecuteException("创建mqtt回调处理类出错, topicSuffix:" + topicSuffix);
        }
    }
}
