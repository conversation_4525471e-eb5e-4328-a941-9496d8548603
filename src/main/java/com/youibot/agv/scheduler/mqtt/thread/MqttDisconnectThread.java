package com.youibot.agv.scheduler.mqtt.thread;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.AGVLog;
import com.youibot.agv.scheduler.mqtt.bean.send.LogoutMessage;
import com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.AGVLogService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import static com.youibot.agv.scheduler.constant.AGVConstant.AGV_LOG_TYPE_ON_LINE;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.*;

/**
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2021/2/22 16:42
 */
public class MqttDisconnectThread extends Thread {

    private static final Logger LOGGER = LoggerFactory.getLogger(MqttDisconnectThread.class);

    private AGVLogService agvLogService = (AGVLogService) ApplicationUtils.getBean("AGVLogServiceImpl");

    private Vehicle vehicle;

    public MqttDisconnectThread(Vehicle vehicle) {
        this.vehicle = vehicle;
    }

    @Override
    public void run() {
        //注：断线操作在连接不上调度或者调度返回登出失败的时候, 有强制断线的处理, 因为需要模拟compass系统,
        // 避免机器人在连接不了调度的情况下也使用不了本地模式, 但这样处理可能导致与调度的状态不一致(重新上线后可恢复正常)
        String agvCode = vehicle.getDeviceNumber();
        if (SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus())) {
            try {
                //在线状态, 发送登出请求
                MqttUtils.pushMessage(MqTopicConstant.MQTT_PUBLISH_LOGOUT, new LogoutMessage(agvCode));
            } catch (Exception e) {
                LOGGER.error("发送登出指令失败, ", e);
            }
            //等待调度返回登出结果
            Integer timeOut = AGVPropertiesUtils.getInt("MQTT.LOGOUT_TIME_OUT");
            long startTime = System.currentTimeMillis();
            while (true) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    LOGGER.error("睡眠失败, ", e);
                }
                //如果在配置的时间内没有收到登出结果, 强制置状态为断线
                if (System.currentTimeMillis() - startTime > timeOut) {
                    LOGGER.warn("机器人登出, 在配置时间内未收到登出结果回调, 强制置调度状态为断开, agvCode{}", agvCode);
                    vehicle.setSchedulerStatus(SCHEDULER_ONLINE_STATUS_DIS_CONNECTION);
                    vehicle.setSchedulerLoginStatus(SCHEDULER_LOGIN_STATUS_NO_LOGGED_IN);
                    break;
                }
                if (SCHEDULER_ONLINE_STATUS_DIS_CONNECTION.equals(vehicle.getSchedulerStatus())) {
                    //登出结果返回已经状态置为断线
                    break;
                }
            }
        } else {
            //非在线状态, 不发送登出请求, 强制断线
            LOGGER.warn("机器人在非在线状态下断开连接, 不发送登出指令强制断开, agvCode:{}", agvCode);
            vehicle.setSchedulerStatus(SCHEDULER_ONLINE_STATUS_DIS_CONNECTION);
            vehicle.setSchedulerLoginStatus(SCHEDULER_LOGIN_STATUS_NO_LOGGED_IN);
        }

        MqttUtils.stopConnection(agvCode);
        AGVLog agvLog = agvLogService.selectLastAGVLog(vehicle.getId(), AGV_LOG_TYPE_ON_LINE);
        if (agvLog != null) {
            agvLog.setEndTime(System.currentTimeMillis() / 1000);
            agvLogService.update(agvLog);
            vehicle.setOnLineLog(agvLog);
        }
    }
}
