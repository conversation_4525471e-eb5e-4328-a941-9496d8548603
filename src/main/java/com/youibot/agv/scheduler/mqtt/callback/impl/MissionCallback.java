package com.youibot.agv.scheduler.mqtt.callback.impl;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.bean.callback.MissionMessage;
import com.youibot.agv.scheduler.mqtt.bean.send.MissionWorkMessage;
import com.youibot.agv.scheduler.mqtt.callback.TopicCallback;
import com.youibot.agv.scheduler.mqtt.service.MqMissionService;
import com.youibot.agv.scheduler.mqtt.service.impl.MqMissionServiceImpl;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_MISSION_ALLOCATE_RESULT;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_MISSION_WORK;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/8/29 19:54
 */
public class MissionCallback extends TopicCallback {

    private final static Logger LOGGER = LoggerFactory.getLogger(MissionCallback.class);

    private MqMissionService mqMissionService = (MqMissionServiceImpl) ApplicationUtils.getBean("mqMissionServiceImpl");
    private MissionWorkService missionWorkService = (MissionWorkService) ApplicationUtils.getBean("missionWorkServiceImpl");


    @Override
    public void processMsg(String callBackData, String agvCode) {
        LOGGER.debug(agvCode + "任务分配接收, callBackData:{}", callBackData);
        MissionMessage missionMessage = JSONObject.parseObject(callBackData, MissionMessage.class);
        if (missionMessage == null || missionMessage.getMissionWork() == null) {
            LOGGER.error("任务分配缺少参数!");
            return;
        }
        Vehicle vehicle = VehicleUtils.getVehicleByAgvCode(agvCode);
        entitySetId(missionMessage, agvCode);

        Mission mission = missionMessage.getMission();
        MissionWork missionWork = missionMessage.getMissionWork();
        if (mission == null || missionWork == null) {
            LOGGER.error("任务分配缺少参数!");
            return;
        }
        try {
            MissionWork missionWorkDB = missionWorkService.selectById(missionWork.getId());
            if (missionWorkDB != null) {
                LOGGER.error("该任务已经存在！missionWork:{}", missionWork);
                return;
            }
            //判断Vehicle是否满足执行任务的条件，不满足就抛出错误信息不执行分配任务！
            if (!AUTO_CONTROL_MODE.equals(vehicle.getManualStatus())) {
                throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
            }
            if (!RECORD_MAP_STATUS_NO.equals(vehicle.getRecordStatus())) {
                throw new ExecuteException(MessageUtils.getMessage("vehicle.vehicle.recording_map_now"));
            }
            if (!MAP_STATUS_NORMAL.equals(vehicle.getMapStatus())) {
                throw new ExecuteException(MessageUtils.getMessage("vehicle.map_not_sync_or_disabled"));
            }
            if (!CONNECT_STATUS_SUCCESS.equals(vehicle.getConnectStatus())) {
                throw new ExecuteException(MessageUtils.getMessage("vehicle.agv_disconnected"));
            }
            checkByWorkStatus(missionWork, vehicle);
            missionWork = mqMissionService.allocateMission(missionMessage, agvCode);
        } catch (ExecuteException e) {
            LOGGER.error("状态不符合分配任务, mapStatus={}, connectStatus={}, controlMode={}, workStatus={}",
                    vehicle.getMapStatus(), vehicle.getConnectStatus(), vehicle.getControlMode(), vehicle.getWorkStatus());
            missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
            missionWork.setErrorCode(ExceptionInfoEnum.VEHICLE_NOT_FREE.getErrorCode());//机器人状态不满足执行任务
            try {
                MissionWorkMessage missionWorkMessage = new MissionWorkMessage(missionWork, agvCode);
                MqttUtils.pushMessage(MQTT_PUBLISH_MISSION_ALLOCATE_RESULT, missionWorkMessage);
            } catch (Exception e1) {
                LOGGER.error("推送任务失败数据出错！", e1);
            }
        } catch (Exception e) {
            LOGGER.error("任务分配失败{}", e);
            missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
            missionWork.setMessage(e.getMessage());
            try {
                MissionWorkMessage missionWorkMessage = new MissionWorkMessage(missionWork, agvCode);
                MqttUtils.pushMessage(MQTT_PUBLISH_MISSION_WORK, missionWorkMessage);
            } catch (Exception e1) {
                LOGGER.error("推送任务失败数据出错！", e1);
            }
        }
    }

    /**
     * 校验机器人是否可执行任务
     *
     * @param missionWork
     * @param vehicle
     */
    private void checkByWorkStatus(MissionWork missionWork, Vehicle vehicle) {
        boolean isExecutable = false;
        Integer workStatus = vehicle.getWorkStatus();
        if (TASK_STATUS_FREE.equals(workStatus)) {//空闲
            isExecutable = true;
        } else if (TASK_STATUS_HOMING.equals(workStatus)) {//归位中
            vehicle.stopSmartTask();
            isExecutable = true;
        } else if (TASK_STATUS_CHARGING.equals(workStatus)){//充电中
            vehicle.stopSmartTask();
            isExecutable = true;
        } else if (TASK_STATUS_WORK.equals(workStatus)) {//任务中
            MissionWork missionWorkOld = vehicle.getMissionWork();
            //且新任务的优先级比正在执行的任务优先级高
            if (missionWorkOld != null  && missionWork.getSequence() > missionWorkOld.getSequence()) {
                isExecutable = true;
            }
        }
        if (!isExecutable) {
            throw new ExecuteException(MessageUtils.getMessage("http.agv_execute_task_now"));
        }
    }

    private void entitySetId(MissionMessage missionMessage, String agvCode) {
        Mission mission = missionMessage.getMission();
        String agvId = VehicleUtils.getVehicleByAgvCode(agvCode).getId();
        mission.setId(mission.getId() + "_" + agvCode);
        missionMessage.getMissionActions().forEach(missionAction -> {
            missionAction.setId(missionAction.getId() + "_" + agvCode);
            missionAction.setAgvId(agvId);
            missionAction.setMissionId(missionAction.getMissionId() + "_" + agvCode);
        });
        missionMessage.getMissionActionParameters().forEach(missionActionParameter -> {
            missionActionParameter.setId(missionActionParameter.getId() + "_" + agvCode);
            missionActionParameter.setMissionActionId(missionActionParameter.getMissionActionId() + "_" + agvCode);
            missionActionParameter.setAgvId(agvId);
        });
        missionMessage.getMissionGlobalVariables().forEach(missionGlobalVariable -> {
            missionGlobalVariable.setId(missionGlobalVariable.getId() + "_" + agvCode);
            missionGlobalVariable.setAgvId(agvId);
            missionGlobalVariable.setMissionId(missionGlobalVariable.getMissionId() + "_" + agvCode);
        });
    }

    private void pushMissionWork(MissionWork missionWork, String agvCode) {
        missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
        try {
            MissionWorkMessage missionWorkMessage = new MissionWorkMessage(missionWork, agvCode);
//            missionWorkMessage.setAllocateStatus(RESULT_FAIL);
            MqttUtils.pushMessage(MQTT_PUBLISH_MISSION_WORK, missionWorkMessage);
        } catch (Exception e1) {
            LOGGER.error("推送任务失败数据出错！", e1);
        }
    }


}
