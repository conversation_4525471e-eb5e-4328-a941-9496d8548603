package com.youibot.agv.scheduler.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface DropDownSetField {
    /**
     * 定义固定下拉内容
     * @return a
     */
    String[] source() default {};

    /**
     * 列标号必须和字段下标一致
     * @return 0
     */
    int index() default 0;

    /**
     * 动态下拉内容
     * @return
     */
    Class[] sourceClass() default {};
}
