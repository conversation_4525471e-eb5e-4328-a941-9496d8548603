package com.youibot.agv.scheduler.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.entity.WorkScheduler;
import com.youibot.agv.scheduler.mapper.WorkSchedulerMapper;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.WorkSchedulerService;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import com.youibot.agv.scheduler.vehicle.Vehicle;

import tk.mybatis.mapper.entity.Example;
/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 14:44
 */
@Service
public class WorkSchedulerServiceImpl extends BaseServiceImpl<WorkScheduler> implements WorkSchedulerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WorkSchedulerServiceImpl.class);

    @Autowired
    private WorkSchedulerMapper workSchedulerMapper;

    @Autowired
	protected MissionWorkService missionWorkService;
    
    @Override
    public List<WorkScheduler> selectCreate() {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andEqualTo("status", "CREATE");
        return this.selectByExample(example);
    }

    @Override
    public List<WorkScheduler> selectRunning() {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT"));
        return this.selectByExample(example);
    }

    @Override
    public List<WorkScheduler> selectPrepareAndRunning() {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("PREPARE","CREATE", "START", "FAULT"));
        return this.selectByExample(example);
    }

    @Override
    public WorkScheduler selectRunningByWork(String workId) {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT")).andEqualTo("workId", workId);
        return this.selectOneByExample(example);
    }

    @Override
    public WorkScheduler selectPrepareAndRunningByWork(String workId) {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("PREPARE","CREATE", "START", "FAULT")).andEqualTo("workId", workId);
        return this.selectOneByExample(example);
    }

    @Override
    public void updateCancel(String workId) {
        WorkScheduler workScheduler = this.selectPrepareAndRunningByWork(workId);
        if (workScheduler == null) {
            LOGGER.warn("cancel work scheduler is fault, work scheduler is null.");
            return;
        }
        workScheduler.setStatus(workScheduler.STATUS_CANCEL);
        workScheduler.setFinishTime(new Date());
        this.update(workScheduler);
    }

    @Override
    public void updateCancel() {
    	  Example example = new Example(WorkScheduler.class);
           example.createCriteria().andIn("status", Arrays.asList("PREPARE","CREATE", "START", "FAULT"));
           List<WorkScheduler> list = super.selectByExample(example);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            LOGGER.warn("cancel work scheduler is fault, work scheduler list is null.");
            return;
        }
        List<String> set =  new ArrayList<>();
         list = list.stream().map( workScheduler ->{
        	  workScheduler.setStatus(workScheduler.STATUS_CANCEL);
              workScheduler.setFinishTime(new Date());
              if(StringUtils.isNotBlank(workScheduler.getWorkId())) {
            	  set.add( workScheduler.getWorkId());
              }
             
        	return workScheduler;
        }).collect(Collectors.toList());
         if( !CollectionUtils.isEmpty(set) ) {
        	 missionWorkService.deleteByIds(set) ;
         }
        
        this.batchUpdate(list); 
    }
    @Override
    public void updateSuccess(String workId) {
        WorkScheduler workScheduler = this.selectRunningByWork(workId);
        if (workScheduler == null) {
            LOGGER.warn("success work scheduler is fault, work scheduler is null.");
            return;
        }
        workScheduler.setStatus(workScheduler.STATUS_SUCCESS);
        workScheduler.setFinishTime(new Date());
        this.update(workScheduler);
    }

    @Override
    public void updateStart(String workId) {
        WorkScheduler workScheduler = this.selectRunningByWork(workId);
        if (workScheduler == null) {
            LOGGER.warn("start work scheduler is fault, work scheduler is null.");
            return;
        }
        workScheduler.setStatus(workScheduler.STATUS_START);
        workScheduler.setStartTime(new Date());
        this.update(workScheduler);
    }

    @Override
    public void updateFault(String workId, String faultMessage) {
        WorkScheduler workScheduler = this.selectRunningByWork(workId);
        if (workScheduler == null) {
            LOGGER.warn("fault work scheduler is fault, work scheduler is null.");
            return;
        }
        workScheduler.setStatus(workScheduler.STATUS_FAULT);
        workScheduler.setFaultMessage(faultMessage);
        this.update(workScheduler);
    }

    @Override
    public WorkScheduler selectLastOne(String vehicleId){
//        Example example = new Example(WorkScheduler.class);
//        example.createCriteria().andEqualTo("vehicleId",vehicleId);
//        example.setOrderByClause("create_time desc");
//        return this.selectOneByExample(example);
        WorkScheduler workScheduler = workSchedulerMapper.selectLastOne(vehicleId);
        return workScheduler;
    }

    @Override
    public WorkScheduler selectRunningByVehicleId(String vehicleId){
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT")).andEqualTo("vehicleId",vehicleId);
        return this.selectOneByExample(example);
    }

    @Override
    public WorkScheduler selectPreAllocationWorkByVehicleId(String vehicleId) {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andEqualTo("status", "PREPARE").andEqualTo("vehicleId",vehicleId);
        return this.selectOneByExample(example);
    }

    @Override
    public void deleteExpireDataByTime(Integer missionWork) {
        List<String> ids = workSchedulerMapper.selectExpireDataByTime(missionWork);
        LOGGER.debug("删除work_scheduler数据总大小：{}", CollectionUtils.isEmpty(ids) ? 0 : ids.size());
        SystemConfigJobUtil.batchSplitDelete(ids, this);
    }

    @Override
    public List<WorkScheduler> selectPrepare() {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("PREPARE"));
        return this.selectByExample(example);
    }

    @Override
    public WorkScheduler selectPrepareAndRunningByVehicleId(String vehicleId){
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("PREPARE", "CREATE", "START", "FAULT")).andEqualTo("vehicleId",vehicleId);
        return this.selectOneByExample(example);
    }

    @Override
    public WorkScheduler selectLastCancelSchedulerByWorkId(String workId) {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andEqualTo("workId",workId).andEqualTo("status","CANCEL");
        example.setOrderByClause("create_time desc");
        return this.selectOneByExample(example);
    }

    @Override
    public WorkScheduler selectLastCreateSchedulerByWorkId(String vehicleId) {
    	 Example example = new Example(WorkScheduler.class);
         example.createCriteria().andIn("status", Arrays.asList(  "CREATE" )).andEqualTo("vehicleId",vehicleId);
         example.setOrderByClause("create_time desc");
         return this.selectOneByExample(example);
    }
    
	@Override
	public void resendWork(Vehicle vehicle) {

    	
    	Boolean dummy = BooleanUtils.toBoolean( vehicle.getDummy());
    	boolean linePatrolMode = BooleanUtils.toBoolean( vehicle.getLinePatrolMode());
    	boolean containsKey = TjdCxt.RESEND.containsKey( vehicle.getId());
    	boolean online = VehicleConstant.ONLINE.equals(vehicle.getOnlineStatus());
    	boolean free = VehicleConstant.TASK_STATUS_FREE.equals( vehicle.getWorkStatus());
    	
    	
		if( free && online && containsKey  && (dummy || linePatrolMode )) {
			WorkScheduler runningByVehicleId = selectLastCreateSchedulerByWorkId(  vehicle.getId());
			WorkScheduler workScheduler = TjdCxt.RESEND.get(  vehicle.getId());
			Long id = workScheduler.getId();
			/**
			 * 状态为已创建, 重新触发发送
			 */
			if(Objects.nonNull( runningByVehicleId) && id.equals(runningByVehicleId.getId())) {
				LOGGER.debug("agvId:{},resend_workScheduler:{)" ,  vehicle.getId(), id);
				vehicle.startMissionWork(workScheduler);
			}
			
    	}
    
		
	}
    
 
  
}
