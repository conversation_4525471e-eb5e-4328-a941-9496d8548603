package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.AGVLog;
import com.youibot.agv.scheduler.entity.AGVStatistics;

import java.util.List;

public interface AGVLogService extends BaseService<AGVLog>{

    AGVStatistics getAgvStatistics(String agvCode, long yesterday, long cuurentDay);

    /**
     * 根据agvCode和类型查询出未结束的记录
     * @param agvCode
     * @param agvLogTypeOnLine
     * @return
     */
    AGVLog selectUnCompletedByAgvCodeAndType(String agvCode, Integer agvLogTypeOnLine);

    /**
     * 根据agvCode和类型更新结束时间
     * @param agvCode
     * @param agvLogTypeOnLine
     */
    void updateEndTimeByAgvCodeAndType(String agvCode, Integer agvLogTypeOnLine);

    /**
     * 查询所有未完成的记录信息
     * @return
     * @param startTime
     * @param endTime
     */
    List<AGVLog> listUnCompleted(long startTime, long endTime);

    /**
     * 根据机器人编号，作业id和类型修改结束时间
     * @param agvCode
     * @param missionWorkId
     * @param type
     * @param endTime
     */
    void updateNullEndTime(String agvCode, String missionWorkId, Integer type, long endTime);

    /**
     * 根据无结束时间的记录设置结束时间为当创建时间当天的最后一秒
     * @param agvLog
     * @param now
     */
    void setCurrentEndTimeByCreateTime(AGVLog agvLog, long now);

    void deleteExpireDataByTime(Integer timeInterval);

    AGVLog selectlastestByAgvCodeAndType(String agvCode, Integer agvLogTypeError);
}
