package com.youibot.agv.scheduler.service;

public interface DockingPointService {

//	List<DockingPoint> selectByAGVMapId(String agvMapId,boolean isDraft);
//
//	List<DockingPoint> selectByAGVMapIds(List<String> agvMapIds,boolean isDraft);
//
//	DockingPoint selectByMarkerId(String agvMapName,String markerId,boolean isDraft);
//
//	Map<String, List<DockingPoint>> mapByAgvMapId(List<String> agvMapList,boolean isDraft);
//
//    Integer deleteByAgvMapIds(List<String> agvMapIdList);
//
//
//	PageInfo<DockingPoint> findPage(Map<String, String> searchMap, boolean isDraft);
//
//	List<DockingPoint> searchAll(Map<String, String> searchMap, boolean isDraft);
//
//	DockingPoint insert(DockingPoint dockingPoint);
//
//	DockingPoint selectById(String mapName,String id,boolean isDraft);
//
//	DockingPoint update(DockingPoint dockingPoint);
//
//	void deleteById(String agvMapId,String id);
}
