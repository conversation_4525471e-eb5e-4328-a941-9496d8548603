package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.MqMessage;
import com.youibot.agv.scheduler.mapper.MqMessageMapper;
import com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.MessageHandle;
import com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.MessageHandleFactory;
import com.youibot.agv.scheduler.service.MqMessageService;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

import static com.youibot.agv.scheduler.mqtt.constants.MqMessageConstant.CONSUME;
import static com.youibot.agv.scheduler.mqtt.constants.MqMessageConstant.UN_CONSUME;

/**
 * @Author：yangpeilin
 * @Date: 2020/10/13 15:14
 */
@Slf4j
@Service
public class MqMessageServiceImpl extends BaseServiceImpl<MqMessage> implements MqMessageService {

    @Autowired
    private MqMessageMapper mqMessageMapper;

    @Override
    public List<MqMessage> listUnConsumeMessage() {
        Example example = new Example(MqMessage.class);
        example.createCriteria().andEqualTo("consume", UN_CONSUME);
        example.orderBy("createTime").asc();
        RowBounds rowBounds = new RowBounds(0, 100); //　每次查询100条
        return super.selectByExampleAndRowBounds(example, rowBounds);
    }

    @Async("asyncTaskExecutor")
    @Override
    public void handle(MqMessage mqMessage) {
        try {
            log.debug("开始多线程处理消息");
            MessageHandle messageHandle = MessageHandleFactory.getMessageHandle(mqMessage.getTopic());
            messageHandle.handle(mqMessage.getMessage());
        } catch (Exception e) {
            log.error("消息处理线程，处理消息失败！[{}]", e);
            mqMessage.setConsume(CONSUME);
            super.update(mqMessage);
        }
    }

    @Override
    public void handleVehicleStaus(MqMessage mqMessage) {
        MessageHandle messageHandle = MessageHandleFactory.getMessageHandle(mqMessage.getTopic());
        messageHandle.handle(mqMessage.getMessage());
    }

    @Override
    public void deleteExpireDataByTime(Integer notify) {
        List<String> idList = this.selectExpireDataByTime(notify);
        log.debug("删除mq_message数据总大小：{}", CollectionUtils.isEmpty(idList) ? 0 : idList.size());
        SystemConfigJobUtil.batchSplitDelete(idList, this);
    }

    @Override
    public List<String> selectExpireDataByTime(Integer mqMessage) {
        return mqMessageMapper.selectExpireDataByTime(mqMessage);
    }
}
