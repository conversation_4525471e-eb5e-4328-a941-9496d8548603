package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.service.BaseService;
import com.youibot.agv.scheduler.service.MissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 任务版本号相关修改
 * <AUTHOR>
 * @date 2020/9/30 17:15
 */
@Service
public abstract class BaseMissionServiceImpl<T> extends BaseVersionServiceImpl<T> implements BaseService<T> {

    @Autowired
    private MissionService missionService;

    @Override
    public String getFieldName(T t) {
        String fieldStr = "missionId";
        if (t instanceof Mission){
            fieldStr = "id";
        }
        return fieldStr;
    }

    @Override
    public void setVersionById(String beanId) {
        missionService.updateMissionVersion(beanId);
    }
}
