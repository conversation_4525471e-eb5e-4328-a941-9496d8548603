package com.youibot.agv.scheduler.service.impl;


import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.mapper.MissionActionMapper;
import com.youibot.agv.scheduler.service.MissionActionParameterService;
import com.youibot.agv.scheduler.service.MissionActionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;


@Service
public class MissionActionServiceImpl extends BaseMissionServiceImpl<MissionAction> implements MissionActionService{

    @Autowired
    private MissionActionMapper mapper;

    @Autowired
    private MissionActionParameterService missionActionParameterService;

    @Override
    public List<MissionAction> selectByMissionId(String missionId) {
        Example example = new Example(MissionAction.class);
        example.createCriteria().andEqualTo("missionId",missionId);
        return super.selectByExample(example);
    }

    @Override
    public List<MissionAction> selectByMissionIdBatch(List<String> missionIds) {
        Example example = new Example(MissionAction.class);
        example.createCriteria().andIn("missionId",missionIds);
        return super.selectByExample(example);
    }

    @Override
    public List<MissionAction> selectByMissionIdAndIsNotSubAction(String missionId) {
        Example example = new Example(MissionAction.class);
        example.createCriteria().andEqualTo("missionId",missionId).andIsNull("parentActionId");
        return super.selectByExample(example);
    }

    @Override
    public List<MissionAction> selectByMissionIdAndActionType(String missionId, String actionType) {
        Example example = new Example(MissionAction.class);
        example.createCriteria().andEqualTo("missionId",missionId).andEqualTo("actionType", actionType);
        return super.selectByExample(example);
    }

    @Override
    public MissionAction selectNoPostActionByMissionId(String missionId) {
        Example example = new Example(MissionAction.class);
        example.createCriteria().andEqualTo("missionId", missionId).andIsNull("parentActionId").andIsNull("nextAction");
        List<MissionAction> missionActions = mapper.selectByExample(example);
        if (CollectionUtils.isEmpty(missionActions)) {
            return null;
        }
        missionActions.sort(Comparator.comparing(MissionAction::getCreateTime));
        return missionActions.get(missionActions.size() - 1);
    }

    @Override
    public List<MissionAction> selectByParentActionId(String parentId) {
        Example example = new Example(MissionAction.class);
        example.createCriteria().andEqualTo("parentActionId", parentId);
        return mapper.selectByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(MissionAction missionAction) {
        //判断当前新增动作是否有前置节点，如果没有，从数据库查询是否存在第一层且后节点为空的动作，如果有则设置为当前新增动作的前置节点
        MissionAction preAction = null;
        if (StringUtils.isEmpty(missionAction.getPreAction())) {
            preAction = selectNoPostActionByMissionId(missionAction.getMissionId());
            if (Objects.nonNull(preAction)) {
                missionAction.setPreAction(preAction.getId());
            }
        }
        this.insert(missionAction);
        //将当前新增动作设置为前置节点的后置动作
        if (Objects.nonNull(preAction)) {
            preAction.setNextAction(missionAction.getId());
            update(preAction);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAction(String id) {
        MissionAction action = selectById(id);
        if (Objects.isNull(action)) {
            return;
        }

        //如果删除动作为逻辑动作，则应删除其所有下层动作
        if (cont.test(action.getActionType())) {
            recursionDelete(action.getId());
        }
        missionActionParameterService.deleteByMissionActionId(id);
        deleteById(id);
    }

    Predicate<String> cont = str -> {
        List<String> conditionActionTypes = new ArrayList<String>() {{
            add("IF_ELSE");
            add("WHILE");
        }};
        return conditionActionTypes.contains(str);
    };

    /**
     * 递归删除逻辑动作的下层动作
     */
    private void recursionDelete(String parentId) {
        List<MissionAction> actions = selectByParentActionId(parentId);
        if (CollectionUtils.isEmpty(actions)) {
            return;
        }
        actions.stream().filter(action -> cont.test(action.getActionType())).map(MissionAction::getId).forEach(this::recursionDelete);
        List<String> ids = actions.stream().map(MissionAction::getId).collect(Collectors.toList());
        missionActionParameterService.deleteByMissionActionIds(ids);
        deleteByIds(ids);
    }
}
