package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.MissionGlobalVariable;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mapper.MissionWorkGlobalVariableMapper;
import com.youibot.agv.scheduler.service.MissionGlobalVariableService;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.service.MissionWorkGlobalVariableService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.List;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_ACTION_STATUS_SUCCESS;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_WAIT_INPUT;

@Slf4j
@Service
public class MissionWorkGlobalVariableServiceImpl extends BaseServiceImpl<MissionWorkGlobalVariable> implements MissionWorkGlobalVariableService {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionGlobalVariableService missionGlobalVariableService;

    @Autowired(required = false)
    private MissionWorkGlobalVariableMapper missionWorkGlobalVariableMapper;

    @Override
    public List<MissionWorkGlobalVariable> selectByMissionWorkId(String missionWorkId) {
        Example example = new Example(MissionWorkGlobalVariable.class);
        example.createCriteria().andEqualTo("missionWorkId", missionWorkId);
        return selectByExample(example);
    }

    @Override
    public List<MissionWorkGlobalVariable> selectByMissionWorkIdAndKeys(String missionWorkId, List<String> keys) {
        Example example = new Example(MissionWorkGlobalVariable.class);
        example.createCriteria().andEqualTo("missionWorkId", missionWorkId).andIn("variableKey", keys);
        return selectByExample(example);
    }

    @Override
    public List<MissionWorkGlobalVariable> selectWaitInputByMissionWorkId(String missionWorkId) {
        MissionWork missionWork = missionWorkService.selectById(missionWorkId);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (!MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_status_is_not_wait_or_wait_input_input"));
        }
        List<String> keys = null;
        //获取missionWork下类型为"VARIABLE_RUNTIME_SETTING"且状态不是成功的action，拿到其参数下的variableKeys的value值
        List<MissionWorkAction> missionWorkActions = missionWorkActionService.selectByMissionWorkIdAndType(missionWorkId, "VARIABLE_RUNTIME_SETTING");
        if (missionWorkActions != null && !missionWorkActions.isEmpty()) {
            for (MissionWorkAction missionWorkAction : missionWorkActions) {
                if (!MISSION_WORK_ACTION_STATUS_SUCCESS.equals(missionWorkAction.getStatus()) && !StringUtils.isEmpty(missionWorkAction.getParameters())) {
                    JSONObject paramJson = JSONObject.parseObject(missionWorkAction.getParameters());
                    String variableKeys = paramJson.getString("variable_keys");//variableKeys格式:key,key,key...
                    if (!StringUtils.isEmpty(variableKeys)) {
                        keys = Arrays.asList(variableKeys.split(","));//获取key集合
                        break;
                    }
                }
            }
        }
        if (keys == null) {
            return null;
        }
        return this.selectByMissionWorkIdAndKeys(missionWorkId, keys);
    }

    @Override
    public void insertByMissionIdAndMissionWork(String missionId, MissionWork missionWork) {
        if (StringUtils.isEmpty(missionId) || missionWork == null || StringUtils.isEmpty(missionWork.getId())) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        List<MissionGlobalVariable> missionGlobalVariables = missionGlobalVariableService.selectByMissionId(missionId);
        if (missionGlobalVariables == null || missionGlobalVariables.isEmpty()) {
            return;
        }
        //检测在创建工作时有没覆盖全局变量的值
        JSONObject runtimeParamJson = null;
        String runtimeParam = missionWork.getRuntimeParam();
        if (!StringUtils.isEmpty(runtimeParam)) {
            runtimeParamJson = JSONObject.parseObject(runtimeParam);
        }
        for (MissionGlobalVariable missionGlobalVariable : missionGlobalVariables) {
            MissionWorkGlobalVariable missionWorkGlobalVariable = new MissionWorkGlobalVariable();
            missionWorkGlobalVariable.setMissionId(missionId);
            missionWorkGlobalVariable.setMissionWorkId(missionWork.getId());
            String variableKey = missionGlobalVariable.getVariableKey();
            String variableValue = missionGlobalVariable.getVariableValue();
            missionWorkGlobalVariable.setVariableKey(variableKey);
            if (runtimeParamJson != null) {
                if (runtimeParamJson.containsKey(variableKey)) {
                    //使用创建工作时传递的变量值
                    variableValue = runtimeParamJson.getString(variableKey);
                }
            }
            missionWorkGlobalVariable.setVariableValue(variableValue);
            this.insert(missionWorkGlobalVariable);
        }
    }

    @Override
    public void deleteExpireDataByTime(Integer missionWork) {
        List<String> ids = missionWorkGlobalVariableMapper.selectExpireDataByTime(missionWork);
        log.debug("删除mission_work_action数据总大小：{}", CollectionUtils.isEmpty(ids) ? 0 : ids.size());
        SystemConfigJobUtil.batchSplitDelete(ids, this);
    }
}
