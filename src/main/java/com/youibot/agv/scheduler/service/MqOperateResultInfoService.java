package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.MqOperateResultInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/1 11:45
 */
public interface MqOperateResultInfoService extends BaseService<MqOperateResultInfo> {

    /**
     * 批量查询操作结果
     * @param agvCodes agvCode集合
     * @param operateType 操作类型
     * @return
     */
    List<MqOperateResultInfo> findOperateResultBatch(List<String> agvCodes, String operateType);
}
