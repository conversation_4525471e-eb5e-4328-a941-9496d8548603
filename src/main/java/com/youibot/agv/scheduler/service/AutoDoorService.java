package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.AirShowerDoor;
import com.youibot.agv.scheduler.entity.AutoDoor;

import java.util.List;

public interface AutoDoorService extends BaseService<AutoDoor> {

    void open(String id);

    void close(String id);

    void updateAutoDoor(AutoDoor autoDoor);

    List<AutoDoor> selectByType(String type);

    AirShowerDoor insertAirShower(AirShowerDoor airShowerDoor);

    AirShowerDoor updateAirShower(AirShowerDoor airShowerDoor);

    /**
     * 根据前门ID或后门ID查询一组风淋门
     * @param id
     * @return
     */
    AirShowerDoor selectAirShowerByArbitrarilyId(String id);

    void updateStatus(AutoDoor autoDoor);

    /**
     * 根据前门list查询出前门与后门的组合list
     * @param airShowerDoors
     * @return
     */
    List<AirShowerDoor> getAirShowerDoorListByFront(List<AutoDoor> airShowerDoors);
}
