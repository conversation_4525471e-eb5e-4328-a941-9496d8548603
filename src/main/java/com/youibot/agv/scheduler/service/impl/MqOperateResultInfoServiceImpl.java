package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.MqOperateResultInfo;
import com.youibot.agv.scheduler.service.MqOperateResultInfoService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/1 11:46
 */
@Service
public class MqOperateResultInfoServiceImpl extends BaseServiceImpl<MqOperateResultInfo> implements MqOperateResultInfoService {

    @Override
    public List<MqOperateResultInfo> findOperateResultBatch(List<String> agvCodes, String operateType) {
        Example example = new Example(MqOperateResultInfo.class);
        example.createCriteria().andIn("agvCode",agvCodes);
        example.createCriteria().andEqualTo("operateType",operateType);
        example.createCriteria().andEqualTo("haveRead",0);
        return super.selectByExample(example);
    }
}
