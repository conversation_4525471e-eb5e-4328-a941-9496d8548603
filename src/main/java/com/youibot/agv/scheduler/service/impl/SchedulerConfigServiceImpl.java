package com.youibot.agv.scheduler.service.impl;

import com.google.common.cache.CacheBuilder;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * @Author：yangpeilin
 * @Date: 2021/7/20 14:09
 */

@Service
public class SchedulerConfigServiceImpl extends BaseServiceImpl<SchedulerConfig> implements SchedulerConfigService {

    private volatile SchedulerConfig schedulerConfig;

    @Autowired
    AGVService agvService;

    @Autowired
    DefaultVehiclePool defaultVehiclePool;

    @Override
    public SchedulerConfig selectSchedulerConfig() {
        if (schedulerConfig == null) {
            List<SchedulerConfig> schedulerConfigs = super.findAll();
            this.schedulerConfig = CollectionUtils.isEmpty(schedulerConfigs) ? null : schedulerConfigs.get(0);
        }
        return this.schedulerConfig;
    }

    @Override
    public Integer insert(SchedulerConfig schedulerConfig) {
        this.schedulerConfig = null;
        return super.insert(schedulerConfig);
    }

    @Override
    public Integer update(SchedulerConfig schedulerConfig) {

        if (!this.schedulerConfig.getChargeSchedulerEnable().equals(schedulerConfig.getChargeSchedulerEnable())
                || !schedulerConfig.getParkSchedulerEnable().equals(schedulerConfig.getParkSchedulerEnable())) {
            List<Agv> agvs = agvService.selectAll();
            this.schedulerConfig = null;
            Integer updateRow = super.update(schedulerConfig);
            agvs.stream().filter(agv -> agv.getAutoPark() == 2 || agv.getAutoCharge() == 2).map(Agv::getAgvCode)
                    .forEach(agvCode -> defaultVehiclePool.updateVehicleByAgv(agvCode));
            return updateRow;
        }
        Integer update = super.update(schedulerConfig);
        List<SchedulerConfig> schedulerConfigs = super.findAll();
        this.schedulerConfig = CollectionUtils.isEmpty(schedulerConfigs) ? null : schedulerConfigs.get(0);
        Integer linePatrolSchedulerInterval = schedulerConfig.getLinePatrolSchedulerInterval();
        if( linePatrolSchedulerInterval > 0) {
        	TjdCxt.REGROUP = CacheBuilder.newBuilder().expireAfterWrite( linePatrolSchedulerInterval , TimeUnit.SECONDS)
        			.build() ;
        }
        return update ;
    }

    @Override
    public Integer updateByPrimaryKeySelective(SchedulerConfig schedulerConfig) {
        // 修改调度配置中充电/泊车的启用状态时要,如果机器人的泊车/充电配置为默认则同步修改机器人自动泊车/充电状态
        if (!Objects.toString(this.selectSchedulerConfig().getChargeSchedulerEnable()).equals(schedulerConfig.getChargeSchedulerEnable())
                || !Objects.toString(this.selectSchedulerConfig().getParkSchedulerEnable()).equals(schedulerConfig.getParkSchedulerEnable())) {
            List<Agv> agvs = agvService.selectAll();
            Integer updateRow = super.updateByPrimaryKeySelective(schedulerConfig);
            agvs.stream().filter(agv -> agv.getAutoPark() == 2 || agv.getAutoCharge() == 2).map(Agv::getAgvCode)
                    .forEach(agvCode -> defaultVehiclePool.updateVehicleByAgv(agvCode));
            return updateRow;
        }
         Integer updateByPrimaryKeySelective = super.updateByPrimaryKeySelective(schedulerConfig);
         this.schedulerConfig = this.selectSchedulerConfig();
         return updateByPrimaryKeySelective ;
    }

    @Override
    public Integer deleteById(String id) {
        this.schedulerConfig = null;
        return super.deleteById(id);
    }

    @Override
    public Integer batchInsert(List<SchedulerConfig> list) {
        this.schedulerConfig = null;
        return super.batchInsert(list);
    }

    @Override
    public Integer batchUpdate(List<SchedulerConfig> list) {
        this.schedulerConfig = null;
        return super.batchUpdate(list);
    }

    @Override
    public Integer delete(SchedulerConfig schedulerConfig) {
        this.schedulerConfig = null;
        return super.delete(schedulerConfig);
    }

    @Override
    public Integer deleteByIds(List<String> ids) {
        this.schedulerConfig = null;
        return super.deleteByIds(ids);
    }

    @Override
    public Integer deleteByExample(Example example) {
        this.schedulerConfig = null;
        return super.deleteByExample(example);
    }
}
