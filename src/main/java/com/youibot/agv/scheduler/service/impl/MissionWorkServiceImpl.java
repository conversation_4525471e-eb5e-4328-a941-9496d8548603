package com.youibot.agv.scheduler.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.constant.vo.MissionStatisticVO;
import com.youibot.agv.scheduler.constant.vo.MissionWorkRecordVO;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.util.MissionActionSortUtils;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.listener.event.MissionWorkLogEvent;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.mapper.*;
import com.youibot.agv.scheduler.param.MissionWorkCreateCheck;
import com.youibot.agv.scheduler.param.MissionWorkParam;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;

@Service
public class MissionWorkServiceImpl extends BaseServiceImpl<MissionWork> implements MissionWorkService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkServiceImpl.class);

    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionActionParameterService missionActionParameterService;

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    @Autowired
    private MissionWorkMapper missionWorkMapper;

    @Autowired
    private MissionWorkLogMapper missionWorkLogMapper;

    @Autowired
    private MissionActionService missionActionService;

    @Autowired
    private DefaultVehiclePool vehiclePool;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private MissionWorkLogServiceImpl missionWorkLogService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private ChargeSchedulerMapper chargeSchedulerMapper;

    @Autowired
    private ParkSchedulerMapper parkSchedulerMapper;

    @Autowired
    private AGVLogMapper agvLogMapper;

    @Override
    public Integer insert(MissionWork missionWork) {
        missionWork.setId(UUID.randomUUID().toString());
        //拷贝任务全局变量数据到工作全局变量表
        missionWorkGlobalVariableService.insertByMissionIdAndMissionWork(missionWork.getMissionId(), missionWork);
        return super.insert(missionWork);
    }

    @Override
    public List<MissionWork> selectBySequenceAndStatus(String status) {
        Example example = new Example(MissionWork.class);
        example.createCriteria().andEqualTo("status", status);
        example.setOrderByClause("sequence desc, create_time asc");//排序，根据优先级降序，时间升序
        return super.selectByExample(example);
    }

    @Override
    public List<MissionWork> selectByStatus(String status, Integer size) {
        if (StringUtils.isEmpty(status) || size == null || size <= 0) {
            throw new ADSParameterException("status is null or size less 1");
        }
        Example example = new Example(MissionWork.class);
        example.createCriteria().andEqualTo("status", status);
        example.setOrderByClause("sequence desc, create_time asc");//排序，根据优先级降序，时间升序
        RowBounds rowBounds = new RowBounds(0, size);
        return super.selectByExampleAndRowBounds(example, rowBounds);
    }


    public List<MissionWork> selectByUnAllocation(Integer size) {
        List<MissionWork> missionWorks = this.selectByStatus(MISSION_WORK_STATUS_CREATE, size);
        List<WorkScheduler> workSchedulers = workSchedulerService.selectPrepareAndRunning();
        if (!CollectionUtils.isEmpty(workSchedulers) && !CollectionUtils.isEmpty(missionWorks)) {
            List<String> workIds = new ArrayList<>();
            workSchedulers.forEach(w -> workIds.add(w.getWorkId()));
            missionWorks = missionWorks.stream().filter(m -> !workIds.contains(m.getId())).collect(Collectors.toList());
        }
        return missionWorks;
//        return missionWorkMapper.selectByUnAllocation(size);
    }

    @Override
    public MissionWork selectMissionWorkById(String id) {
        MissionWork missionWork = this.selectById(id);
        if (null != missionWork && !StringUtils.isEmpty(missionWork.getAgvCode())) {
            Vehicle vehicle = vehiclePool.getVehicle(missionWork.getAgvCode());
            if (vehicle != null && vehicle.getDefaultVehicleStatus() != null) {
                String vehicleErrorMessage = vehicle.getDefaultVehicleStatus().getErrorMessages();
                String missionWorkMessage = missionWork.getMessage();
                StringBuffer sb = new StringBuffer();
                if (!StringUtils.isEmpty(vehicleErrorMessage)) {
                    sb.append(vehicleErrorMessage);
                }
                if (!StringUtils.isEmpty(missionWorkMessage)) {
                    sb.append(missionWorkMessage);
                }
                if (sb.length() > 0) {
                    missionWork.setMessage(sb.toString());
                }
            }
        }
        return missionWork;
    }

    @Override
    public int updateStatus(MissionWork missionWork, boolean isConsume, boolean event) {
        String status = missionWork.getStatus();
        if (MISSION_WORK_STATUS_RUNNING.equals(status)) {
            if (isConsume) {
                missionWork.setStartTime(new Date());
                missionWork.setEndTime(null);
            }
            //将任务名称和作业Id赋值到vehicle
            this.setVehicleMissionWorkInfo(missionWork.getAgvCode(), missionWork.getId(), missionWork.getName());
        }
        boolean isSuccess = MISSION_WORK_STATUS_SUCCESS.equals(status);
        if (MISSION_WORK_STATUS_FAULT.equals(status) || isSuccess) {
            //单机会将数据库的错误信息传递给调度,这个字段过长导致数据库update失败,我建议统一规范,单机建议调度自行截取,妥协单机!
            if (!StringUtils.isEmpty(missionWork.getMessage()) && missionWork.getMessage().length() > 1020) {
                String subMsg = missionWork.getMessage().substring(0, 1020);
                missionWork.setMessage(subMsg);
            }
            missionWork.setEndTime(new Date());
            if (isSuccess) {
                missionWork.setMessage(null);
            }
        }
        if (isSuccess || MISSION_WORK_STATUS_SHUTDOWN.equals(status)) {
            //将任务名称和作业Id赋值到vehicle
            this.setVehicleMissionWorkInfo(missionWork.getAgvCode(), null, null);
        }
        //MqMissionServiceImpl#updateMissionWork,单机会传入update时间,
        // 避免单机时间不同步导致错误问题,这个方法自行设置
        missionWork.setUpdateTime(new Date());
        // 校正startTime字段的值
        checkMissionWorkStartTime(missionWork);
        Integer update = this.update(missionWork);
        if (event) {
            //发布MissionWorkLog事件
            applicationEventPublisher.publishEvent(new MissionWorkLogEvent(missionWork, this));
            //触发任务状态回调
            applicationEventPublisher.publishEvent(new MissionWorkStateEvent(missionWork));
        }
        return update;
    }

    /**
     * 用于校正系统的起始时间，预防因compass系统和fleet系统之间的时间存在误差
     * 从而导致startTime值比endTime值还大的问题
     *
     * @param missionWork 要更新到数据库的任务对象，也就是compass上报的对象
     */
    private void checkMissionWorkStartTime(MissionWork missionWork) {
        if (Objects.isNull(missionWork.getId())) {
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        if (MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus())) {
            return;
        }
        Example example = new Example(MissionWork.class);
        example.createCriteria().andEqualTo("id", missionWork.getId());
        MissionWork missionWorkDB = missionWorkMapper.selectOneByExample(example);

        if(missionWorkDB != null){
            missionWork.setStartTime(missionWorkDB.getStartTime());
        }

    }

    private void setVehicleMissionWorkInfo(String agvCode, String missionWorkId, String missionName) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if (vehicle != null) {
            vehicle.setMissionName(missionName);
            vehicle.setMissionWorkId(missionWorkId);
        }
    }

    @Override
    public MissionWork getMissionWorkByTriggerSelectorId(String triggerSelectorId) {
        if (StringUtils.isEmpty(triggerSelectorId)) {
            return null;
        }
        return missionWorkMapper.selectMissionWorkByTriggerSelectorId(triggerSelectorId);
    }

    @Override
    public void updateStatus(MissionWork missionWork, String status) {
        missionWork.setStatus(status);
        this.updateStatus(missionWork, false, true);
    }

    @Override
    public List<MissionWork> selectUnCompleteBySchedulePlanId(String schedulePlanId) {
        Example example = new Example(MissionWork.class);
        List<String> notInStatus = new ArrayList<>();
        notInStatus.add(MISSION_WORK_STATUS_SUCCESS);
        notInStatus.add(MISSION_WORK_STATUS_SHUTDOWN);
        example.createCriteria().andNotIn("status", notInStatus)
                .andEqualTo("schedulePlanId", schedulePlanId);
        return super.selectByExample(example);
    }

    @Override
    public List<MissionWork> selectByStatusList(List<String> status, String missionGroupId) {
        Example example = new Example(MissionWork.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("status", status);
        if (!StringUtils.isEmpty(missionGroupId)) {
            criteria.andEqualTo("missionGroupId", missionGroupId);
        }
        example.orderBy("createTime").asc();
        return super.selectByExample(example);
    }

    @Override
    public MissionWork selectUnCompleteByAgvId(String agvCode) {
        Example example = new Example(MissionWork.class);
        List<String> list = new ArrayList<>();
        list.add(MISSION_WORK_STATUS_CREATE);
        list.add(MISSION_WORK_STATUS_SUCCESS);
        list.add(MISSION_WORK_STATUS_SHUTDOWN);
        example.createCriteria().andEqualTo("agvCode", agvCode).andNotIn("status", list);
        return super.selectOneByExample(example);
    }

    @Override
    public MissionWork createByMissionWorkParam(MissionWorkParam missionWorkParam) {
        String missionId = missionWorkParam.getMissionId();
        String missionCode = missionWorkParam.getMissionCode();
        Mission mission = null;
        if (!StringUtils.isEmpty(missionId)) {
            mission = missionService.selectById(missionId);
        }
        if (mission == null && !StringUtils.isEmpty(missionCode)) {
            mission = missionService.selectByCode(missionCode);
        }

        if (mission == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_is_null"));
        }
        List<MissionAction> missionActionList = missionActionService.selectByMissionIdAndIsNotSubAction(missionId);//获取missionWorkAction列表
        if (missionActionList == null || missionActionList.isEmpty()) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_action_list_is_null"));
        }
        MissionWork missionWork = new MissionWork();
        missionWork.setMissionId(mission.getId());
        missionWork.setCallbackUrl(missionWorkParam.getCallbackUrl());
        missionWork.setRuntimeParam(missionWorkParam.getRuntimeParam());
//        missionWork.setMissionCallId(missionWorkParam.getMissionCallId());
        missionWork.setName(mission.getName());
        missionWork.setStatus(MISSION_WORK_STATUS_CREATE);
//        missionWork.setAllocationStatus(MISSION_WORK_ALLOCATION_STATUS_UNASSIGNED);
        if (null != missionWorkParam.getSequence()) {
            missionWork.setSequence(missionWorkParam.getSequence());
        } else {
            missionWork.setSequence(mission.getSequence());
        }
        missionWork.setMissionGroupId(mission.getMissionGroupId());//设置任务组ID,目的：根据任务组ID筛选任务执行记录
        if (!StringUtils.isEmpty(missionWorkParam.getAgvCode())) {
            missionWork.setAgvCode(missionWorkParam.getAgvCode());
        } else {
            missionWork.setAgvCode(mission.getAgvCode());
        }
        Optional.ofNullable(vehiclePool.getVehicle(missionWork.getAgvCode())).ifPresent(x -> missionWork.setAgvName(x.getName()));
        missionWork.setAgvGroupId(mission.getAgvGroupId());
        missionWork.setAgvType(mission.getAgvType());
        insert(missionWork);
        return missionWork;
    }

    @Override
    public MissionWorkCreateCheck createCheckByMissionWorkParam(MissionWorkParam missionWorkParam) {
        if (StringUtils.isEmpty(missionWorkParam.getMissionId())) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Mission mission = missionService.selectById(missionWorkParam.getMissionId());
        if (mission == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_is_null"));
        }

        //工作不绑定任务链, 无冲突
        return new MissionWorkCreateCheck(MISSION_WORK_CREATE_NO_CONFLICT);
    }

    @Override
    public void deleteExpireDataByTime(Integer missionWork) {
        List<String> mwIdList = missionWorkMapper.selectExpireDataByTime(missionWork);
        LOGGER.debug("删除mission_work数据总大小：{}", CollectionUtils.isEmpty(mwIdList) ? 0 : mwIdList.size());
        SystemConfigJobUtil.batchSplitDelete(mwIdList, this);
        List<String> mwlIdList = missionWorkLogMapper.selectExpireDataByTime(missionWork);
        LOGGER.debug("删除mission_work_log数据总大小：{}", CollectionUtils.isEmpty(mwlIdList) ? 0 : mwlIdList.size());
        SystemConfigJobUtil.batchSplitDelete(mwlIdList, missionWorkLogService);
    }

    @Override
    public AGVStatistics getAgvStatistics(String agvCode, long yesterday, long cuurentDay) {
        return missionWorkMapper.getAgvStatistics(agvCode, yesterday, cuurentDay);
    }

    private Mission checkMission(String missionId) {
        if (StringUtils.isEmpty(missionId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Mission mission = missionService.selectById(missionId);
        if (mission == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_is_null"));
        }
        //获取missionWorkAction列表
        List<MissionAction> missionActionList = missionActionService.selectByMissionId(missionId);
        if (CollectionUtils.isEmpty(missionActionList)) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_action_list_is_null"));
        }
        return mission;
    }

    @Override
    public List<MissionWork> selectUnCompleteMissionWorks() {
        Example example = new Example(MissionWork.class);
        List<String> list = new ArrayList<>();
        list.add(MISSION_WORK_STATUS_SUCCESS);
        list.add(MISSION_WORK_STATUS_SHUTDOWN);
        example.createCriteria().andNotIn("status", list);
        return super.selectByExample(example);
    }

    @Override
    public void stopUnCompleteMissionWorks() {
         List<MissionWork> works = this.selectUnCompleteMissionWorks();
        
         if(org.apache.commons.collections4.CollectionUtils.isEmpty(works)) {
        	 return ;
         }
         works = works.stream().map( item ->{
        	 item.setStatus( MISSION_WORK_STATUS_SHUTDOWN );
        	 item.setEndTime( new Date());
        	 
        	 workSchedulerService.updateCancel(item.getId()) ;
        	 return item;
         }).collect(Collectors.toList());
         
         this.batchUpdate(works);
    }
    
    @Override
    public MissionWork selectMissionWorkByAgvCodeAndWorkingStatus(String agvCode) {
        Example example = new Example(MissionWork.class);
        List<String> list = new ArrayList<>();
        list.add(MISSION_WORK_STATUS_RUNNING);
        list.add(MISSION_WORK_STATUS_PAUSE);
        example.createCriteria().andEqualTo("agvCode", agvCode).andIn("status", list);
        return super.selectOneByExample(example);
    }

    @Override
    public List<MissionWork> selectMissionWorkByAgvCodeAndWorkingStatus(String agvCode, String status) {
        Example example = new Example(MissionWork.class);
        List<String> list = new ArrayList<>();
        list.add(status);
        example.createCriteria().andEqualTo("agvCode", agvCode).andIn("status", list);
        return super.selectByExample(example);
    }
    @Override
    public MissionWork createMissionWorkByCode(String missionId, String missionCallId) {
        Mission mission = missionService.selectById(missionId);
        if (mission == null) {
            return null;
        }
        MissionWorkParam missionWorkParam = new MissionWorkParam();
        missionWorkParam.setMissionId(mission.getId());
        missionWorkParam.setAgvCode(mission.getAgvCode());
        missionWorkParam.setMissionCallId(missionCallId);
        return this.createByMissionWorkParam(missionWorkParam);
    }

    /*@Override
    public MissionWork getMissionWorkByMissionCallId(String missionCallId) {
        if (StringUtils.isEmpty(missionCallId)) {
            return null;
        }
        return missionWorkMapper.selectMissionWorkByMissionCallId(missionCallId);
    }*/

    @Override
    public List<MissionWork> selectByStatusListPage(Map<String, String> searchMap) {
        Example example = new Example(MissionWork.class);
        String missionGroupId = searchMap.get("missionGroupId");
        if (!StringUtils.isEmpty(missionGroupId)) {
            example.createCriteria().andEqualTo("missionGroupId", missionGroupId);
        }
        example.createCriteria().andIn("status", Arrays.asList(searchMap.get("statusList").split(",")));
        example.orderBy("createTime").asc();
        PageInfo<MissionWork> pageInfo = getPageInfo(searchMap, example);
        return pageInfo.getList();
    }

    /**
     * 获取第一个位置点
     *
     * @param
     * @return
     */
    @Override
    public Marker getFirstMarker(MissionWork missionWork) {
        // 查询所有非子动作的action
        List<MissionAction> missionActions = missionActionService.selectByMissionIdAndIsNotSubAction(missionWork.getMissionId());
        if (CollectionUtils.isEmpty(missionActions)) {
            return null;
        }
        //由于数据是以双向链表进行排序，先将数据进行排序后在筛选
        missionActions = MissionActionSortUtils.sortMissionActionListBySequence(missionActions);//排序
        List<String> actionTypes = Arrays.asList("MOVE_SIDE_PATH", "MOVE_DOCKING", "REMOTE_PLC", "IF_ELSE", "WHILE", "VARIABLE_SETTING");
        missionActions = missionActions.stream().filter(x -> actionTypes.contains(x.getActionType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(missionActions)) {
            return null;
        }

        MissionAction missionAction = missionActions.get(0);

        List<String> ignoreActionTypes = Arrays.asList("REMOTE_PLC", "IF_ELSE", "WHILE", "VARIABLE_SETTING");
        if (ignoreActionTypes.contains(missionAction.getActionType())) {
            LOGGER.info("作业的第一个导航动作在全局变量/逻辑判断动作之后, 不获取作业第一个导航点。");
            return null;
        }

        // get marker by action.
        List<MissionActionParameter> missionActionParameters = missionActionParameterService.selectByMissionActionId(missionAction.getId());
        // get marker code.
        List<MissionActionParameter> markerActionParameters = missionActionParameters.stream().filter(x -> x.getParameterKey().equals("markerCode")).collect(Collectors.toList());
        List<MissionActionParameter> mapActionParameters = missionActionParameters.stream().filter(x -> x.getParameterKey().equals("agvMapId")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(markerActionParameters) || CollectionUtils.isEmpty(mapActionParameters)) {
            return null;
        }
        String markerCode;
        String mapId;
        final String markerCodeKey = markerActionParameters.get(0).getParameterValue();
        final String mapIdKey = mapActionParameters.get(0).getParameterValue();
        // check global parameter.
        List<MissionWorkGlobalVariable> missionWorkGlobalVariables = missionWorkGlobalVariableService.selectByMissionWorkId(missionWork.getId());
        List<MissionWorkGlobalVariable> markerGlobals = missionWorkGlobalVariables.stream().filter(mg -> mg.getVariableKey().equals(markerCodeKey)).collect(Collectors.toList());
        List<MissionWorkGlobalVariable> mapGlobals = missionWorkGlobalVariables.stream().filter(mg -> mg.getVariableKey().equals(mapIdKey)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(markerGlobals)) {
            markerCode = markerGlobals.get(0).getVariableValue();
        } else {
            markerCode = markerCodeKey;
        }
        if (!CollectionUtils.isEmpty(mapGlobals)) {
            mapId = mapGlobals.get(0).getVariableValue();
        } else {
            mapId = mapIdKey;
        }
        // check marker code info.
        if (StringUtils.isEmpty(markerCode) || StringUtils.isEmpty(mapId)) {
            return null;
        }
        final String markerCodeFinal = markerCode;
        final String mapIdFinal = mapId;
        Set<Marker> markers = MapGraphUtil.getEnableMarkers();
        markers = markers.stream().filter((Marker m) -> (m.getCode().equals(markerCodeFinal) && m.getAgvMapName().equals(mapIdFinal))).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(markers)) {
            return null;
        }
        return markers.iterator().next();
    }

    @Override
    public MissionWork createByTrigger(TriggerSelector triggerSelector) {
        MissionWork work = new MissionWork();
        String missionId = triggerSelector.getMissionId();
        if (StringUtils.isEmpty(missionId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Mission mission = missionService.selectById(missionId);
        work.setMissionId(missionId);
        work.setName(mission.getName());
        work.setStatus(MISSION_WORK_STATUS_CREATE);
        work.setSequence(mission.getSequence());
        work.setTriggerSelectorId(triggerSelector.getId());
        work.setAgvGroupId(mission.getAgvGroupId());
        work.setAgvType(mission.getAgvType());
        work.setAgvCode(mission.getAgvCode());
        work.setId(UUID.randomUUID().toString());
        super.insert(work);
        LOGGER.debug("Creation by trigger successful " + work.getId());
        return work;
    }

    @Override
    public Marker getLastMarker(MissionWork missionWork) {
        // get action by mission id .
        List<MissionAction> missionActions = missionActionService.selectByMissionIdAndIsNotSubAction(missionWork.getMissionId());
        if (CollectionUtils.isEmpty(missionActions)) {
            return null;
        }
        // filter action
        missionActions = MissionActionSortUtils.sortMissionActionListBySequence(missionActions);//排序
        missionActions = missionActions.stream().filter(x -> (x.getActionType().equals("MOVE_SIDE_PATH") || x.getActionType().equals("MOVE_DOCKING"))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(missionActions)) {
            return null;
        }

        MissionAction missionAction = missionActions.get(missionActions.size() - 1);
        // get marker by action.
        List<MissionActionParameter> missionActionParameters = missionActionParameterService.selectByMissionActionId(missionAction.getId());
        // get marker code.
        List<MissionActionParameter> markerActionParameters = missionActionParameters.stream().filter(x -> x.getParameterKey().equals("markerCode")).collect(Collectors.toList());
        List<MissionActionParameter> mapActionParameters = missionActionParameters.stream().filter(x -> x.getParameterKey().equals("agvMapId")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(markerActionParameters) || CollectionUtils.isEmpty(mapActionParameters)) {
            return null;
        }
        String markerCode;
        String mapId;
        final String markerCodeKey = markerActionParameters.get(0).getParameterValue();
        final String mapIdKey = mapActionParameters.get(0).getParameterValue();
        // check global parameter.
        List<MissionWorkGlobalVariable> missionWorkGlobalVariables = missionWorkGlobalVariableService.selectByMissionWorkId(missionWork.getId());
        List<MissionWorkGlobalVariable> markerGlobals = missionWorkGlobalVariables.stream().filter(mg -> mg.getVariableKey().equals(markerCodeKey)).collect(Collectors.toList());
        List<MissionWorkGlobalVariable> mapGlobals = missionWorkGlobalVariables.stream().filter(mg -> mg.getVariableKey().equals(mapIdKey)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(markerGlobals)) {
            markerCode = markerGlobals.get(0).getVariableValue();
        } else {
            markerCode = markerCodeKey;
        }
        if (!CollectionUtils.isEmpty(mapGlobals)) {
            mapId = mapGlobals.get(0).getVariableValue();
        } else {
            mapId = mapIdKey;
        }
        // check marker code info.
        if (StringUtils.isEmpty(markerCode) || StringUtils.isEmpty(mapId)) {
            return null;
        }
        final String markerCodeFinal = markerCode;
        final String mapIdFinal = mapId;
        Set<Marker> markers = MapGraphUtil.getEnableMarkers();
        markers = markers.stream().filter((Marker m) -> (m.getCode().equals(markerCodeFinal) && m.getAgvMapName().equals(mapIdFinal))).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(markers)) {
            return null;
        }
        return markers.iterator().next();
    }

    @Override
    public PageInfo<MissionWork> findPage(Map<String, String> searchMap) {
        Example example = getExample(searchMap);
        PageInfo<MissionWork> missionWorkPageInfo = getPageInfo(searchMap, example);
        List<MissionWork> missionWorks = missionWorkPageInfo.getList();
        List<String> statusList = Arrays.asList(MISSION_WORK_STATUS_CREATE, MISSION_WORK_STATUS_SUCCESS, MISSION_WORK_STATUS_FAULT, MISSION_WORK_STATUS_SHUTDOWN, MISSION_WORK_STATUS_BEING_SHUTDOWN);
        for (MissionWork missionWork : missionWorks) {
            MissionWorkAction missionWorkAction = missionWorkActionService.selectRunningActionByMissionWorkId(missionWork.getId());
            if (missionWorkAction != null && !statusList.contains(missionWork.getStatus())) {
                missionWork.setCurrentActionName(missionWorkAction.getName());
                missionWork.setCurrentActionSequence(missionWorkActionService.selectByMissionWorkId(missionWork.getId()).size());
            }
        }
        missionWorkPageInfo.setList(missionWorks);
        return missionWorkPageInfo;
    }

    @Override
    public void exportRecord(Map<String, String> searchMap, HttpServletResponse response) {
        try {
            List<MissionWork> missionWorks = selectRecord(searchMap);
            List<MissionWorkRecordVO> recordList = new ArrayList<>();
            List<String> statusList = Arrays.asList(MISSION_WORK_STATUS_CREATE, MISSION_WORK_STATUS_SUCCESS, MISSION_WORK_STATUS_FAULT, MISSION_WORK_STATUS_SHUTDOWN, MISSION_WORK_STATUS_BEING_SHUTDOWN);
            for (MissionWork missionWork : missionWorks) {
                if (!statusList.contains(missionWork.getStatus())) {
                    MissionWorkAction missionWorkAction = missionWorkActionService.selectRunningActionByMissionWorkId(missionWork.getId());
                    if(missionWorkAction != null) {
                        missionWork.setCurrentActionSequence(missionWorkActionService.selectByMissionWorkId(missionWork.getId()).size());
                        missionWork.setCurrentActionName(missionWorkAction.getName());
                    }
                }
                recordList.add(new MissionWorkRecordVO(missionWork));
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH mm ss");
            String fileName = "作业记录" + sdf.format(new Date()) + ".xlsx";
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-type", "application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), "iso8859-1"));
            EasyExcel.write(response.getOutputStream(), MissionWorkRecordVO.class)
            //        .registerWriteHandler(new ProductCellWriteHandler())
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("作业记录")
                    .doWrite(recordList);
        } catch (IOException e) {
            throw new ExecuteException(MessageUtils.getMessage("service.data_export_error"));
        }
    }

    @Override
    public List<MissionWork> selectRecord(Map<String, String> searchMap) {
        Example example = getExample(searchMap);
        return super.selectByExample(example);
    }

    private Example getExample(Map<String, String> searchMap) {
        Example example = new Example(MissionWork.class);
        Example.Criteria criteria = example.createCriteria();
        if (searchMap != null && !searchMap.isEmpty()) {
            if (searchMap.containsKey("startTime") && searchMap.containsKey("endTime")) {
                if (searchMap.get("startTime") != null && searchMap.get("endTime") != null) {
                    criteria.andBetween("createTime", searchMap.get("startTime"), searchMap.get("endTime"));
                }
            }
            for (String key : searchMap.keySet()) {
                if ("pageNum".equals(key) || "pageSize".equals(key) || "startTime".equals(key) || "endTime".equals(key)) {
                    continue;
                }
                if ("sort".equals(key)) {
                    example.setOrderByClause(searchMap.get(key));
                    continue;
                }
                Object value = handlerMapKey(searchMap.get(key));
                if (key.equals("name") || key.equals("message")) {
                    criteria.andLike(key, "%" + ((String) value) + "%");
                    continue;
                }
                criteria.andEqualTo(key, value);

            }
        }
        return example;
    }


    /**
     * 查询某个时间范围内，任务统计信息
     * <p>
     * 如果小车编码agvCode存在，就查询单个小车的
     * 否则，就查询所有小车的
     */
    @Override
    public List<MissionStatisticVO> getMissionWorkStatistic(String agvCode, String startTime, String endTime) {
        Date now = new Date();
        Date startDate = DateUtils.getFormatDate(startTime);
        //如果开始时间晚于当前时间，没有数据，不用查询
        if (startDate.after(now)) {
            return Collections.emptyList();
        }
        Date endDate = DateUtils.getFormatDate(endTime);
        if (now.before(endDate)) {
            endDate = now;
        }

        List<MissionStatisticVO> list = new ArrayList<>();
        Map<String, MissionStatisticVO> map = new HashMap<>();

        //任务统计
        computeMissionWorkStatistic(map, agvCode, now, startTime, endTime);

        //充电统计
        computeChargeStatistic(map, agvCode, now, startTime, endTime);

        //泊车统计
        computeParkStatistic(map, agvCode, now, startTime, endTime);

        //异常统计
        computeErrorStatistic(map, agvCode, now, startTime, endTime);

        list.addAll(map.values());

        //最后算一下空闲时间：时间范围对应的时间 - （任务时间、泊车时间、充电时间、异常时间）
        for (MissionStatisticVO item : list) {
            item.computeFreeTime(startDate, endDate);
        }
        return list;
    }


    //防止时间跨度过大，导致数据量过大，进而占用内存过大
    private void computeMissionWorkStatistic(Map<String, MissionStatisticVO> map, String agvCode, Date now, String startTime, String endTime) {
        int missionWorkStatisticCount = missionWorkMapper.getMissionWorkStatisticCount(agvCode, startTime, endTime);
        if (missionWorkStatisticCount <= 0) {
            return;
        }

        Date startDate = DateUtils.getFormatDate(startTime);
        Date endDate = DateUtils.getFormatDate(endTime);
        if (now.before(endDate)) {
            endDate = now;
        }

        int batchNum = 500;
        int total = missionWorkStatisticCount % batchNum == 0 ? missionWorkStatisticCount / batchNum : missionWorkStatisticCount / batchNum + 1;
        for (int i = 0; i < total; i++) {
            int from = i * batchNum;
            int size = missionWorkStatisticCount - i * batchNum > batchNum ? batchNum : missionWorkStatisticCount - i * batchNum;
            List<MissionWork> missionList = missionWorkMapper.getMissionWorkStatistic(agvCode, startTime, endTime, from, size);
            if (CollectionUtils.isEmpty(missionList)) {
                break;
            }
            doComputeMissionWorkStatistic(map, startDate, endDate, missionList);
        }
    }

    //任务统计
    private void doComputeMissionWorkStatistic(Map<String, MissionStatisticVO> map, Date startDate, Date endDate, List<MissionWork> missionList) {

        //按小车分组
        Map<String, List<MissionWork>> agvMap = missionList.stream().collect(Collectors.groupingBy(MissionWork::getAgvCode));
        if (agvMap == null || agvMap.size() <= 0) {
            return;
        }

        final Date finalDate = endDate;

        for (Map.Entry<String, List<MissionWork>> item : agvMap.entrySet()) {
            String agvCode = item.getKey();
            List<MissionWork> workList = item.getValue();
            MissionStatisticVO missionStatisticVO = map.get(agvCode);
            if (missionStatisticVO == null) {
                missionStatisticVO = new MissionStatisticVO();
                missionStatisticVO.setAgvCode(agvCode);
            }
            missionStatisticVO.setWorkNum(missionStatisticVO.getWorkNum() + workList.size());
            long workTime = 0L;
            if (!CollectionUtils.isEmpty(workList)) {
                workTime = workList.stream().mapToLong((work) -> {
                    if (work.getStartTime() == null) {
                        return 0L;
                    }

                    Date tmpStart = work.getStartTime();
                    if (work.getStartTime().before(startDate)) {
                        tmpStart = startDate;
                    }
                    //此为运行中的动作
                    if (work.getEndTime() == null) {
                        return DateUtils.getSeconds(tmpStart, finalDate);
                    }

                    //此为运行结束的动作
                    Date tmpEnd = work.getEndTime();
                    if (work.getEndTime().after(finalDate)) {
                        tmpEnd = finalDate;
                    }
                    return DateUtils.getSeconds(tmpStart, tmpEnd);
                }).sum();
            }
            missionStatisticVO.setWorkTime(missionStatisticVO.getChargeTime() + workTime);
            map.put(agvCode, missionStatisticVO);
        }
    }


    //充电统计
    private void computeChargeStatistic(Map<String, MissionStatisticVO> map, String agvCode, Date now, String startTime, String endTime) {
        int missionWorkStatisticCount = chargeSchedulerMapper.getChargeStatisticCount(agvCode, startTime, endTime);
        if (missionWorkStatisticCount <= 0) {
            return;
        }

        Date startDate = DateUtils.getFormatDate(startTime);
        Date endDate = DateUtils.getFormatDate(endTime);
        if (now.before(endDate)) {
            endDate = now;
        }

        int batchNum = 500;
        int total = missionWorkStatisticCount % batchNum == 0 ? missionWorkStatisticCount / batchNum : missionWorkStatisticCount / batchNum + 1;
        for (int i = 0; i < total; i++) {
            int from = i * batchNum;
            int size = missionWorkStatisticCount - i * batchNum > batchNum ? batchNum : missionWorkStatisticCount - i * batchNum;
            List<ChargeScheduler> missionList = chargeSchedulerMapper.getChargeStatistic(agvCode, startTime, endTime, from, size);
            if (CollectionUtils.isEmpty(missionList)) {
                break;
            }
            doComputeChargeStatistic(map, startDate, endDate, missionList);
        }
    }

    private void doComputeChargeStatistic(Map<String, MissionStatisticVO> map, Date startDate, Date endDate, List<ChargeScheduler> missionList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(missionList)) {
            return;
        }
        //按小车分组
        Map<String, List<ChargeScheduler>> agvMap = missionList.stream().collect(Collectors.groupingBy(ChargeScheduler::getVehicleId));
        if (agvMap == null || agvMap.size() <= 0) {
            return;
        }

        final Date finalDate = endDate;
        for (Map.Entry<String, List<ChargeScheduler>> item : agvMap.entrySet()) {
            String agvCode = item.getKey();
            List<ChargeScheduler> workList = item.getValue();
            MissionStatisticVO missionStatisticVO = map.get(agvCode);
            if (missionStatisticVO == null) {
                missionStatisticVO = new MissionStatisticVO();
                missionStatisticVO.setAgvCode(agvCode);
            }
            missionStatisticVO.setChargeNum(missionStatisticVO.getChargeNum() + workList.size());
            long chargeTime = 0L;
            if (!CollectionUtils.isEmpty(workList)) {
                chargeTime = workList.stream().mapToLong((work) -> {
                    if (work.getStartTime() == null) {
                        return 0L;
                    }

                    Date tmpStart = work.getStartTime();
                    if (work.getStartTime().before(startDate)) {
                        tmpStart = startDate;
                    }
                    //此为运行中的动作
                    if (work.getFinishTime() == null) {
                        return DateUtils.getSeconds(tmpStart, finalDate);
                    }

                    //此为运行结束的动作
                    Date tmpEnd = work.getFinishTime();
                    if (work.getFinishTime().after(finalDate)) {
                        tmpEnd = finalDate;
                    }
                    return DateUtils.getSeconds(tmpStart, tmpEnd);
                }).sum();
            }
            missionStatisticVO.setChargeTime(missionStatisticVO.getChargeTime() + chargeTime);
            map.put(agvCode, missionStatisticVO);
        }
    }


    //泊车统计
    private void computeParkStatistic(Map<String, MissionStatisticVO> map, String agvCode, Date now, String startTime, String endTime) {
        int missionWorkStatisticCount = parkSchedulerMapper.getParkStatisticCount(agvCode, startTime, endTime);
        if (missionWorkStatisticCount <= 0) {
            return;
        }

        Date startDate = DateUtils.getFormatDate(startTime);
        Date endDate = DateUtils.getFormatDate(endTime);
        if (now.before(endDate)) {
            endDate = now;
        }

        int batchNum = 500;
        int total = missionWorkStatisticCount % batchNum == 0 ? missionWorkStatisticCount / batchNum : missionWorkStatisticCount / batchNum + 1;
        for (int i = 0; i < total; i++) {
            int from = i * batchNum;
            int size = missionWorkStatisticCount - i * batchNum > batchNum ? batchNum : missionWorkStatisticCount - i * batchNum;
            List<ParkScheduler> missionList = parkSchedulerMapper.getParkStatistic(agvCode, startTime, endTime, from, size);
            if (CollectionUtils.isEmpty(missionList)) {
                break;
            }
            doComputeParkStatistic(map, startDate, endDate, missionList);
        }
    }

    private void doComputeParkStatistic(Map<String, MissionStatisticVO> map, Date startDate, Date endDate, List<ParkScheduler> missionList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(missionList)) {
            return;
        }
        //按小车分组
        Map<String, List<ParkScheduler>> agvMap = missionList.stream().collect(Collectors.groupingBy(ParkScheduler::getVehicleId));
        if (agvMap == null || agvMap.size() <= 0) {
            return;
        }

        final Date finalDate = endDate;

        for (Map.Entry<String, List<ParkScheduler>> item : agvMap.entrySet()) {
            String agvCode = item.getKey();
            List<ParkScheduler> workList = item.getValue();
            MissionStatisticVO missionStatisticVO = map.get(agvCode);
            if (missionStatisticVO == null) {
                missionStatisticVO = new MissionStatisticVO();
                missionStatisticVO.setAgvCode(agvCode);
            }
            missionStatisticVO.setParkNum(missionStatisticVO.getParkNum() + workList.size());
            long parkTime = 0L;
            if (!CollectionUtils.isEmpty(workList)) {
                parkTime = workList.stream().mapToLong((work) -> {
                    if (work.getStartTime() == null) {
                        return 0L;
                    }

                    Date tmpStart = work.getStartTime();
                    if (work.getStartTime().before(startDate)) {
                        tmpStart = startDate;
                    }
                    //此为运行中的动作
                    if (work.getFinishTime() == null) {
                        return DateUtils.getSeconds(tmpStart, finalDate);
                    }

                    //此为运行结束的动作
                    Date tmpEnd = work.getFinishTime();
                    if (tmpEnd.after(finalDate)) {
                        tmpEnd = finalDate;
                    }
                    return DateUtils.getSeconds(tmpStart, tmpEnd);
                }).sum();
            }
            missionStatisticVO.setParkTime(missionStatisticVO.getParkTime() + parkTime);
            map.put(agvCode, missionStatisticVO);
        }
    }


    //异常统计
    private void computeErrorStatistic(Map<String, MissionStatisticVO> map, String agvCode, Date now, String startTime, String endTime) {
        int missionWorkStatisticCount = agvLogMapper.getAGVLogStatisticCount(agvCode, startTime, endTime);
        if (missionWorkStatisticCount <= 0) {
            return;
        }

        Date startDate = DateUtils.getFormatDate(startTime);
        Date endDate = DateUtils.getFormatDate(endTime);
        if (now.before(endDate)) {
            endDate = now;
        }

        int batchNum = 500;
        int total = missionWorkStatisticCount % batchNum == 0 ? missionWorkStatisticCount / batchNum : missionWorkStatisticCount / batchNum + 1;
        for (int i = 0; i < total; i++) {
            int from = i * batchNum;
            int size = missionWorkStatisticCount - i * batchNum > batchNum ? batchNum : missionWorkStatisticCount - i * batchNum;
            List<AGVLog> missionList = agvLogMapper.getAGVLogStatistic(agvCode, startTime, endTime, from, size);
            if (CollectionUtils.isEmpty(missionList)) {
                break;
            }
            doComputeErrorStatistic(map, startDate, endDate, missionList);
        }
    }

    private void doComputeErrorStatistic(Map<String, MissionStatisticVO> map, Date startDate, Date endDate, List<AGVLog> missionList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(missionList)) {
            return;
        }
        //按小车分组
        Map<String, List<AGVLog>> agvMap = missionList.stream().collect(Collectors.groupingBy(AGVLog::getAgvCode));
        if (agvMap == null || agvMap.size() <= 0) {
            return;
        }

        final Date finalDate = endDate;

        for (Map.Entry<String, List<AGVLog>> item : agvMap.entrySet()) {
            String agvCode = item.getKey();
            List<AGVLog> workList = item.getValue();
            MissionStatisticVO missionStatisticVO = map.get(agvCode);
            if (missionStatisticVO == null) {
                missionStatisticVO = new MissionStatisticVO();
                missionStatisticVO.setAgvCode(agvCode);
            }
            missionStatisticVO.setErrorNum(missionStatisticVO.getErrorNum() + workList.size());
            long errorTime = 0L;
            if (!CollectionUtils.isEmpty(workList)) {
                errorTime = workList.stream().mapToLong((work) -> {
                    if (work.getStartTime() == null) {
                        return 0L;
                    }
                    Date tmpStart = DateUtils.getFormatDate(work.getStartTime());
                    if (tmpStart == null) {
                        return 0;
                    }

                    if (tmpStart.before(startDate)) {
                        tmpStart = startDate;
                    }
                    //此为运行中的动作
                    if (work.getEndTime() == null) {
                        return DateUtils.getSeconds(tmpStart, finalDate);
                    }
                    Date tmpEnd = DateUtils.getFormatDate(work.getEndTime());
                    if (tmpEnd == null) {
                        return DateUtils.getSeconds(tmpStart, finalDate);
                    }

                    //此为运行结束的动作
                    if (tmpEnd.after(finalDate)) {
                        tmpEnd = finalDate;
                    }
                    return DateUtils.getSeconds(tmpStart, tmpEnd);
                }).sum();
            }
            missionStatisticVO.setErrorTime(missionStatisticVO.getErrorTime() + errorTime);
            map.put(agvCode, missionStatisticVO);
        }
    }

	@Override
	public MissionWork quickRun(MissionWorkParam missionWorkParam, String status) {
        String missionId = missionWorkParam.getMissionId();
        String missionCode = missionWorkParam.getMissionCode();
        Mission mission = null;
        if (!StringUtils.isEmpty(missionId)) {
            mission = missionService.selectById(missionId);
        }
        if (mission == null && !StringUtils.isEmpty(missionCode)) {
            mission = missionService.selectByCode(missionCode);
        }

        if (mission == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_is_null"));
        }
        List<MissionAction> missionActionList = missionActionService.selectByMissionIdAndIsNotSubAction(missionId);//获取missionWorkAction列表
        if (missionActionList == null || missionActionList.isEmpty()) {
            throw new ExecuteException("("+  missionCode +"|" + missionId  + ")"+ MessageUtils.getMessage("service.mission_action_list_is_null"));
        }
        MissionWork missionWork = new MissionWork();
        missionWork.setMissionId(mission.getId());
        missionWork.setCallbackUrl(missionWorkParam.getCallbackUrl());
        missionWork.setRuntimeParam(missionWorkParam.getRuntimeParam());
//        missionWork.setMissionCallId(missionWorkParam.getMissionCallId());
        missionWork.setName(mission.getName());
        missionWork.setStatus(status);
//        missionWork.setAllocationStatus(MISSION_WORK_ALLOCATION_STATUS_UNASSIGNED);
        if (null != missionWorkParam.getSequence()) {
            missionWork.setSequence(missionWorkParam.getSequence());
        } else {
            missionWork.setSequence(mission.getSequence());
        }
        missionWork.setMissionGroupId(mission.getMissionGroupId());//设置任务组ID,目的：根据任务组ID筛选任务执行记录
        if (!StringUtils.isEmpty(missionWorkParam.getAgvCode())) {
            missionWork.setAgvCode(missionWorkParam.getAgvCode());
        } else {
            missionWork.setAgvCode(mission.getAgvCode());
        }
        Optional.ofNullable(vehiclePool.getVehicle(missionWork.getAgvCode())).ifPresent(x -> missionWork.setAgvName(x.getName()));
        missionWork.setAgvGroupId(mission.getAgvGroupId());
        missionWork.setAgvType(mission.getAgvType());
        insert(missionWork);
        return missionWork;
    }


	
	
	@Override
	public void deleteMissionWork(String missionWorkId) {
		
		super.deleteById(missionWorkId );
		
		Example ex = new Example( WorkScheduler.class) ;
		ex.createCriteria().andEqualTo("workId", missionWorkId);
	        
		workSchedulerService.deleteByExample(  ex ) ;
		
	}

    @Override
    public boolean continueMissionWork(String id) {

        MissionWork missionWork = this.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus()) || MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus())) {
            return false;
        }
        if (!MISSION_WORK_STATUS_WAIT.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_wait_or_wait_input"));
        }


        vehiclePool.getVehicle(missionWork.getAgvCode()).continueMissionWork(id);
        //missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_RESUME);//将missionWork状态置为恢复中
//            if (MISSION_WORK_ALLOCATION_STATUS_ASSIGNED.equals(missionWork.getAllocationStatus())) {//已分配到AGV执行
//                this.getVehicle(missionWork.getAgvCode()).resumeCommand(id);
//                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_RESUME);//将missionWork状态置为恢复中
//            } else {//未分配到AGV执行
//                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_CREATE);//修改为创建状态
//            }
        LOGGER.debug("agvCode:[{}],event:[恢复作业],当前恢复的作业数据：[{}]", missionWork.getAgvCode(), JSONObject.toJSONString(missionWork));
       return true  ;
    }

}
