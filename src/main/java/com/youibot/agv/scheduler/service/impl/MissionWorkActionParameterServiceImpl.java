package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mapper.MissionWorkActionParameterMapper;
import com.youibot.agv.scheduler.service.MissionWorkActionParameterService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_ACTION_PARAM_TYPE_RUNTIME;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019年5月15日 上午10:17:15
 */
@Slf4j
@Service
public class MissionWorkActionParameterServiceImpl extends BaseServiceImpl<MissionWorkActionParameter> implements MissionWorkActionParameterService {

    @Autowired
    private MissionWorkActionParameterMapper missionWorkActionParameterMapper;

    @Override
    public List<MissionWorkActionParameter> selectByMissionWorkActionId(String missionWorkActionId) {
        Example example = new Example(MissionWorkActionParameter.class);
        example.createCriteria().andEqualTo("missionWorkActionId", missionWorkActionId);
        return super.selectByExample(example);
    }

    @Override
    public List<MissionWorkActionParameter> selectByMissionWorkActionIdAndKey(String missionWorkActionId,String parameterKey) {
        Example example = new Example(MissionWorkActionParameter.class);
        example.createCriteria().andEqualTo("missionWorkActionId", missionWorkActionId).andEqualTo("parameterKey",parameterKey);
        return super.selectByExample(example);
    }

    @Override
    public void insertByMissionActionParameterList(List<MissionActionParameter> missionActionParameters, String missionWorkActionId) {
        if (missionActionParameters == null || missionActionParameters.isEmpty()) {
            return;
        }
        for (MissionActionParameter missionActionParameter : missionActionParameters) {
            MissionWorkActionParameter missionWorkActionParameter = new MissionWorkActionParameter();
            missionWorkActionParameter.setMissionWorkActionId(missionWorkActionId);
            missionWorkActionParameter.setParameterKey(missionActionParameter.getParameterKey());
            missionWorkActionParameter.setParameterValue(missionActionParameter.getParameterValue());
            missionWorkActionParameter.setParameterType(missionActionParameter.getParameterType());
            missionWorkActionParameter.setType(missionActionParameter.getParameterValueType());
            super.insert(missionWorkActionParameter);
        }
    }

    @Override
    public String getMissionWorkActionParameters(MissionWorkAction missionWorkAction, String missionWorkId, List<MissionWorkGlobalVariable> missionWorkGlobalVariables) {
        List<MissionWorkActionParameter> missionWorkActionParameters = this.selectByMissionWorkActionId(missionWorkAction.getId());
        Map<String, Object> paramMap = new HashMap<>();
        for (MissionWorkActionParameter missionWorkActionParameter : missionWorkActionParameters) {
            String paramKey = missionWorkActionParameter.getParameterKey();
            String paramValue = missionWorkActionParameter.getParameterValue();
            String paramValueType = missionWorkActionParameter.getType();
            if (MISSION_ACTION_PARAM_TYPE_RUNTIME.equals(missionWorkActionParameter.getParameterType())) {//如果时运行时参数
                //获取该missionWork下已有的变量
//                List<MissionWorkGlobalVariable> missionWorkGlobalVariables = missionWorkGlobalVariableService.selectByMissionWorkId(missionWorkId);
                boolean flag = false;//检测在变量库中是否有key为paramValue的变量
                for (MissionWorkGlobalVariable missionWorkGlobalVariable : missionWorkGlobalVariables) {
                    if (missionWorkGlobalVariable.getVariableKey().equals(paramValue)) {
                        String variableValue = missionWorkGlobalVariable.getVariableValue();
                        if (StringUtils.isEmpty(variableValue)) {
                            throw new ExecuteException("key=" + missionWorkGlobalVariable.getVariableKey() + ", " + MessageUtils.getMessage("service.mission_work_variable_value_is_null"));
                        }
                        paramValue = variableValue;
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    throw new ExecuteException("key=" + paramValue + ", " + MessageUtils.getMessage("service.mission_work_variable_is_null"));
                }
            }
            Object sendParamValue = getSendParameterValue(paramValueType, paramValue);
            paramMap.put(paramKey, sendParamValue);
        }
        return JSON.toJSONString(paramMap);
    }

    @Override
    public void deleteExpireDataByTime(Integer missionWork) {
        List<String> idList = missionWorkActionParameterMapper.selectExpireDataByTime(missionWork);
        log.debug("删除mission_work_action数据总大小：{}", CollectionUtils.isEmpty(idList) ? 0 : idList.size());
        SystemConfigJobUtil.batchSplitDelete(idList, this);
    }

    /**
     * 获取真正发给agv类型的值
     *
     * @param type
     * @param parameterValue
     * @return
     */
    private Object getSendParameterValue(String type, String parameterValue) {
        if (StringUtils.isEmpty(parameterValue)) {
            return "";
        }
        if ("Double".equals(type)) {
            return Double.valueOf(parameterValue);
        } else if ("Boolean".equals(type)) {
            return Boolean.valueOf(parameterValue);
        } else if ("Float".equals(type)) {
            return Float.valueOf(parameterValue);
        } else if ("Integer".equals(type)) {
            return Integer.valueOf(parameterValue);
        } else if ("Long".equals(type)) {
            return Long.valueOf(parameterValue);
        } else if ("BigDecimal".equals(type)) {
            return new BigDecimal(parameterValue);
        }
        return parameterValue;
    }
}
