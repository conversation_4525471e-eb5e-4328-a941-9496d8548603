package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.AdjustAction;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.PageInfo;
import com.youibot.agv.scheduler.map.entity.PathResultData;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.service.AdjustActionService;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.ROAD_NETWORK_SUFFIX;

/**
 * @Author：yangpeilin
 * @Date: 2020/7/23 19:00
 */
@Service
public class AdjustActionServiceImpl implements AdjustActionService {


    private static final Logger LOGGER = LoggerFactory.getLogger(AdjustActionServiceImpl.class);

    /**
     * 从目标目录读取文件，如果不存在就从源目录写入到目标目录，再读取
     *
     * @param srcFilePath
     * @param destFilePath
     * @return
     */
    public String getDraftFileInfo(FTPClient ftpClient, String srcFilePath, String destFilePath) throws IOException {

        //查询ftp对应地图的草稿目录的文件 /home/<USER>/youibot_map/mapName/path/draft/mapName.qr
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        boolean retrieveFile = ftpClient.retrieveFile(destFilePath, outputStream);

        //不存在的话，就复制、创建该文件
        if (!retrieveFile) {
            MapFileUtils.copyFolder(srcFilePath, destFilePath, ftpClient);
        }

        //如果仍然不存在的话，就创建一个文件
        retrieveFile = ftpClient.retrieveFile(destFilePath, outputStream);
        if (!retrieveFile) {
            //throw new RuntimeException("get ftp file error");
            String data = "{}";
            ftpClient.storeFile(destFilePath, new ByteArrayInputStream(data.getBytes()));
        }

        byte[] bytes = outputStream.toByteArray();
        return new String(bytes);
    }


    @Override
    public AdjustAction selectByMarkerIdAndDestMarkerId(String agvMapId, String markerId, String destMarkerId, boolean isDraft) {

        if (StringUtil.isBlank(agvMapId) || StringUtil.isBlank(markerId) || StringUtil.isBlank(destMarkerId)) {
            //throw new RuntimeException("param is null");
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        List<AdjustAction> adjustActions = selectByAGVMapId(agvMapId, isDraft);

        if (!CollectionUtils.isEmpty(adjustActions)) {
            return adjustActions.stream().filter(item -> item.getDestMarkerId().equals(destMarkerId) && item.getMarkerId().equals(markerId)).findFirst().orElse(null);
        }
        return null;
    }

    @Override
    public List<AdjustAction> selectByAGVMapId(String agvMapId, boolean isDraft) {
        FTPClient client = null;
        try {
            List<AdjustAction> adjustActions = new ArrayList<>();
            if (isDraft) {
                client = FtpUtils.getConnectClient();
                String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapId, true);
                String result = MapFileUtils.getDraftFileData(filePath, agvMapId, client);
                if (org.springframework.util.StringUtils.isEmpty(result)) {
                    return new ArrayList<>();
                }
                PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
                adjustActions = new ArrayList<>(roadNetwork.getAdjustActions().values());
            } else {
                if (AGVMapInfoCache.getCache(agvMapId) != null && !CollectionUtils.isEmpty(AGVMapInfoCache.getCache(agvMapId).getAdjustActions())) {
                    adjustActions = new ArrayList<>(AGVMapInfoCache.getCache(agvMapId).getAdjustActions().values());
                }
            }
            return adjustActions;
        } catch (Exception e) {
            LOGGER.error("Failed to selectByAGVMapId  e:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_map_file_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public List<AdjustAction> selectByAGVMapIds(List<String> agvMapIds, boolean isDraft) {
        if (CollectionUtils.isEmpty(agvMapIds)) {
            return Collections.emptyList();
        }

        List<AdjustAction> list = new ArrayList<>();
        agvMapIds.forEach(agvMapId -> list.addAll(selectByAGVMapId(agvMapId, isDraft)));

        return list;
    }

    @Override
    public Map<String, List<AdjustAction>> mapByAgvMapId(List<String> agvMapList, boolean isDraft) {
        return BeanUtils.listToMapByFiled(this.selectByAGVMapIds(agvMapList, isDraft), "agvMapId");
    }

    @Override
    public Integer deleteByAgvMapIds(List<String> agvMapIdList) {
        if (CollectionUtils.isEmpty(agvMapIdList)) {
            return 0;
        }
        agvMapIdList.forEach(this::deleteByAgvMapId);
        return agvMapIdList.size();
    }

    public void deleteByAgvMapId(String mapName) {

        String srcFilePath = FtpUtils.server_path + mapName + "/path/current/" + mapName + ".path";
        String destFilePath = FtpUtils.server_path + mapName + "/path/draft/" + mapName + ".path";
        //1、读取文件内容
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);

            JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
            //写入
            mapObj.remove("adjustActions");

            //3、存储到ftp文件
            byte[] bytes = JSONObject.toJSONString(mapObj).getBytes();
            ftpClient.storeFile(destFilePath, new ByteArrayInputStream(bytes));

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (ftpClient != null) {
                FtpUtils.disconnect(ftpClient);
            }
        }
    }


    @Override
    public PageInfo<AdjustAction> findPage(Map<String, String> searchMap, boolean isDraft) {
        //1、从内存地图 数据中，获取
        List<AdjustAction> list = searchAll(searchMap, isDraft);

        Integer pageNum = null;
        Integer pageSize = null;
        String pageNumStr = searchMap.get("pageNum");
        String pageSizeStr = searchMap.get("pageSize");
        if (StringUtils.isNotBlank(pageNumStr)) {
            pageNum = Integer.valueOf(pageNumStr);
        }
        if (StringUtils.isNotBlank(pageSizeStr)) {
            pageSize = Integer.valueOf(pageSizeStr);
        }

        //2、分页查询
        return MapFileUtils.getPageList(list, pageNum, pageSize);
    }

    @Override
    public List<AdjustAction> searchAll(Map<String, String> searchMap, boolean isDraft) {
        if (searchMap == null || searchMap.size() <= 0) {
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        String mapName = searchMap.get("mapName");
        if (StringUtils.isBlank(mapName)) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_name_is_nul"));
        }
        List<AdjustAction> adjustActions = selectByAGVMapId(mapName, isDraft);
        if (searchMap.containsKey("mapName") && searchMap.containsKey("isDraft") && searchMap.size() == 2) {
            return adjustActions;
        }

        Set<AdjustAction> result = new HashSet<>();
        try {
            for (String attribute : searchMap.keySet()) {
                for (AdjustAction adjustAction : adjustActions) {
                    if (MapFileUtils.getGetMethod(adjustAction, attribute, searchMap.get(attribute))) {
                        result.add(adjustAction);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("searchAll AdjustAction failed ：{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
        }
        return new ArrayList<>(result);
    }

    @Override
    public AdjustAction insert(AdjustAction adjustAction) {
        FTPClient client = null;
        try {
            String agvMapName = adjustAction.getAgvMapId();
            if (org.springframework.util.StringUtils.isEmpty(agvMapName)) {
                LOGGER.error("missing agvMapName");
                throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
            }
            adjustAction.setId(UUID.randomUUID().toString());
            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapName, true);
            PathResultData pathResult = new PathResultData();
            Map<String, AdjustAction> adjustActions = new HashMap<>();
            String result = MapFileUtils.getDraftFileData(filePath, agvMapName, client);
            if (!org.springframework.util.StringUtils.isEmpty(result)) {
                pathResult = MapFileUtils.pathFileDataToPathResultData(result);
                adjustActions = pathResult.getAdjustActions();
            }
            adjustActions.put(adjustAction.getId(), adjustAction);
            pathResult.setAdjustActions(adjustActions);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(pathResult), filePath, client);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
        return adjustAction;
    }

    @Override
    public AdjustAction selectById(String mapName, String id, boolean isDraft) {

        List<AdjustAction> list = new ArrayList<>();
        //查询内存
        if (!isDraft) {
            //1、从内存地图 数据中，获取
            Map<String, AdjustAction> markerMap = AGVMapInfoCache.getCache(mapName).getAdjustActions();
            if (markerMap != null && markerMap.size() > 0) {
                list = (List<AdjustAction>) markerMap.values();
            }
        } else {

            String srcFilePath = FtpUtils.server_path + mapName + "/path/current/" + mapName + ".path";
            String destFilePath = FtpUtils.server_path + mapName + "/path/draft/" + mapName + ".path";
            //1、读取文件内容
            FTPClient ftpClient = null;
            try {
                ftpClient = FtpUtils.getConnectClient();
                String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);

                JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
                JSONArray jsonList = mapObj.getJSONArray("adjustActions");
                if (jsonList != null && jsonList.size() > 0) {
                    for (int i = 0; i < jsonList.size(); i++) {
                        AdjustAction tmp = jsonList.getObject(i, AdjustAction.class);
                        list.add(tmp);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (ftpClient != null) {
                    FtpUtils.disconnect(ftpClient);
                }
            }
        }

        AdjustAction target = null;
        if (!CollectionUtils.isEmpty(list)) {
            for (AdjustAction item : list) {
                if (item.getId().equalsIgnoreCase(id)) {
                    target = item;
                    break;
                }
            }
        }
        return target;
    }

    @Override
    public AdjustAction update(AdjustAction adjustAction) {
        String mapName = adjustAction.getAgvMapId();
        if (StringUtils.isEmpty(mapName)) {
            //throw new RuntimeException("params data is null");
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_name_is_nul"));
        }

        String srcFilePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, adjustAction.getAgvMapId(), true);
        //1、读取文件内容
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            String mapInfoStr = MapFileUtils.getDraftFileData(srcFilePath, mapName, ftpClient);
            if (StringUtils.isBlank(mapInfoStr)) {
                LOGGER.error("update adjustAction filed");
                throw new ExecuteException("service.read_map_file_fail");
            }
            PathResultData pathResult = MapFileUtils.pathFileDataToPathResultData(mapInfoStr);
            Map<String, AdjustAction> adjustActions = pathResult.getAdjustActions();
            adjustActions.put(adjustAction.getId(), adjustAction);
            pathResult.setAdjustActions(adjustActions);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(pathResult), srcFilePath, ftpClient);
            return adjustAction;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (ftpClient != null) {
                FtpUtils.disconnect(ftpClient);
            }
        }
        return null;
    }

    @Override
    public void deleteById(String mapName, String id) {

        if (StringUtils.isEmpty(mapName) || StringUtils.isEmpty(id)) {
            //throw new RuntimeException("params data is null");
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        deleteByIds(mapName, Collections.singletonList(id));
    }

    @Override
    public void deleteByIds(String mapName, List<String> ids) {
        if (StringUtils.isEmpty(mapName) || CollectionUtils.isEmpty(ids)) {
            //throw new RuntimeException("params data is null");
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }

        String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, mapName, true);
        //1、读取文件内容
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            PathResultData pathResult = new PathResultData();
            Map<String, AdjustAction> adjustActions = new HashMap<>();
            String result = MapFileUtils.getDraftFileData(filePath, mapName, ftpClient);
            if (!org.springframework.util.StringUtils.isEmpty(result)) {
                pathResult = MapFileUtils.pathFileDataToPathResultData(result);
                adjustActions = pathResult.getAdjustActions();
            }
            if (CollectionUtils.isEmpty(adjustActions)) {
                return;
            }
            for (String adjustId : ids) {
                adjustActions.remove(adjustId);
            }
            pathResult.setAdjustActions(adjustActions);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(pathResult), filePath, ftpClient);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (ftpClient != null) {
                FtpUtils.disconnect(ftpClient);
            }
        }
    }

    @Override
    public List<AdjustAction> selectByMarkerId(String id, String agvMapName, boolean isDraft) {
        List<AdjustAction> adjustActions = selectByAGVMapId(agvMapName, isDraft);
        return adjustActions.stream().filter(adjustAction -> adjustAction.getMarkerId().equals(id)).collect(Collectors.toList());
    }
}
