package com.youibot.agv.scheduler.service;


import com.youibot.agv.scheduler.entity.Floor;

import java.util.List;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/7/30 16:07
 */
public interface FloorService extends BaseService<Floor> {

    List<Floor> selectByElevatorId(String elevatorId);

    Floor selectByMarkerId(String markerId);

    List<Floor> selectByAGVMapId(String agvMapId);

}
