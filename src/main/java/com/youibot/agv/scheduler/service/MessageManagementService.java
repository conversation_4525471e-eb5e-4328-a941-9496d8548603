package com.youibot.agv.scheduler.service;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface MessageManagementService {
    PageInfo<AbnormalPrompt> findPage(Map<String, String> searchMap);

    List<AbnormalPrompt> searchAll(Map<String, String> searchMap);

    void insert(AbnormalPrompt abnormalPrompt);

    AbnormalPrompt selectById(String id);

    void update(AbnormalPrompt abnormalPrompt);

    void deleteById(String id);

    List<String> getAbnormalTypeList();

    void downExcelFile(Integer abnormalLevel, String abnormalType, Integer abnormalCode, String abnormalDescription, HttpServletResponse response);

    void uploadExcelFile(MultipartFile multiPartFile);

}
