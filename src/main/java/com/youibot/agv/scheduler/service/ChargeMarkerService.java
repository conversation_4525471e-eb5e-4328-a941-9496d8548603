package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.entity.Marker;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-12 17:55
 */
public interface ChargeMarkerService {

    /**
     * 查询所有的充电点。
     *
     * @return
     */
    List<Marker> selectAll();

    /**
     * 查询所有未绑定的充电点。
     * @return
     */
    List<Marker> selectByFree();

    /**
     * 查询所有已绑定的充电点。
     *
     * @return
     */
    List<Marker> selectByBound();

    /**
     * 解绑充电点
     *
     * @param markerId
     */
    void freeChargeMarker(Integer markerId);

    /**
     * 绑定充电点
     *
     * @param markerId
     */
    void bindChargeMarker(Integer markerId);
}
