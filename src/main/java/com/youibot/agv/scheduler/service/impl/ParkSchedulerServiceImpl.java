package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.ParkScheduler;
import com.youibot.agv.scheduler.mapper.ParkSchedulerMapper;
import com.youibot.agv.scheduler.service.ParkSchedulerService;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 14:44
 */
@Service
public class ParkSchedulerServiceImpl extends BaseServiceImpl<ParkScheduler> implements ParkSchedulerService {

    private static final Logger logger = LoggerFactory.getLogger(WorkSchedulerServiceImpl.class);

    @Autowired
    private ParkSchedulerMapper parkSchedulerMapper;

    ParkScheduler selectById(Long id) {
        Example example = new Example(ParkScheduler.class);
        example.createCriteria().andEqualTo("id", id);
        return this.selectOneByExample(example);
    }

    @Override
    public List<ParkScheduler> selectCreate() {
        Example example = new Example(ParkScheduler.class);
        example.createCriteria().andEqualTo("status", "CREATE");
        return this.selectByExample(example);
    }

    @Override
    public List<ParkScheduler> selectRunning() {
        Example example = new Example(ParkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT"));
        return this.selectByExample(example);
    }

    @Override
    public List<ParkScheduler> selectCreateAndStart() {
        Example example = new Example(ParkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START"));
        return this.selectByExample(example);
    }


    @Override
    public ParkScheduler selectRunningByVehicle(String vehicleId) {
        Example example = new Example(ParkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT")).andEqualTo("vehicleId", vehicleId);
        return this.selectOneByExample(example);
    }

    @Override
    public void updateCancel(Long id) {
        ParkScheduler parkScheduler = this.selectById(id);
        if (parkScheduler == null) {
            logger.warn("cancel park scheduler is fault, work scheduler is null.");
            return;
        }
        parkScheduler.setStatus(parkScheduler.STATUS_CANCEL);
        parkScheduler.setFinishTime(new Date());
        this.update(parkScheduler);
        logger.debug("agvCode:[{}],event:[取消泊车调度],取消泊车调度完成：{}", parkScheduler.getVehicleId(), JSONObject.toJSONString(parkScheduler));
    }

    @Override
    public void updateCancel( ) {
      List<ParkScheduler> list = selectRunning();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            logger.warn("cancel park scheduler list is fault, work scheduler is null.");
            return;
        }
        list =  list.parallelStream().map( parkScheduler ->{
        	  parkScheduler.setStatus(parkScheduler.STATUS_CANCEL);
              parkScheduler.setFinishTime(new Date());
        	return parkScheduler;
        }).collect(Collectors.toList()) ;
      
        this.batchUpdate(list);
        logger.debug("event:[取消泊车调度],取消所有泊车调度完成");
    }

    @Override
    public void updateSuccess(Long id) {
        ParkScheduler parkScheduler = this.selectById(id);
        if (parkScheduler == null) {
            logger.warn("success park scheduler is fault, work scheduler is null.");
            return;
        }
        parkScheduler.setStatus(parkScheduler.STATUS_SUCCESS);
        parkScheduler.setFinishTime(new Date());
        this.update(parkScheduler);
    }

    @Override
    public void updateStart(Long id) {
        ParkScheduler parkScheduler = this.selectById(id);
        if (parkScheduler == null) {
            logger.warn("start park scheduler is fault, work scheduler is null.");
            return;
        }
        parkScheduler.setStatus(parkScheduler.STATUS_START);
        parkScheduler.setStartTime(new Date());
        this.update(parkScheduler);
    }

    @Override
    public void updateFault(Long id, String faultMessage) {
        ParkScheduler parkScheduler = this.selectById(id);
        if (parkScheduler == null) {
            logger.warn("fault park scheduler is fault, work scheduler is null.");
            return;
        }
        parkScheduler.setStatus(parkScheduler.STATUS_FAULT);
        parkScheduler.setFaultMessage(faultMessage);
        this.update(parkScheduler);
        logger.debug("agvCode:[{}],event:[泊车调度异常],异常原因：{}", parkScheduler.getVehicleId(), faultMessage);
    }

    @Override
    public List<String> selectCancelByFinishTimeDiff(String timeDiff) {
        return parkSchedulerMapper.selectCancelByFinishTimeDiff(timeDiff);
    }

    @Override
    public ParkScheduler selectLastOne(String vehicleId){
        ParkScheduler parkScheduler = parkSchedulerMapper.selectLastOne(vehicleId);
        return parkScheduler;
    }

    @Override
    public void deleteExpireDataByTime(Integer missionWork) {
        List<String> ids = parkSchedulerMapper.selectExpireDataByTime(missionWork);
        logger.debug("删除park_scheduler数据总大小：{}", CollectionUtils.isEmpty(ids) ? 0 : ids.size());
        SystemConfigJobUtil.batchSplitDelete(ids, this);
    }
}
