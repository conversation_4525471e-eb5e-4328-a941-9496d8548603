package com.youibot.agv.scheduler.service;


import com.youibot.agv.scheduler.dto.AgvBatchParam;
import com.youibot.agv.scheduler.entity.SystemWorkMode;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.eclipse.paho.client.mqttv3.MqttException;

import java.io.IOException;
import java.util.List;


public interface SystemWorkModeService extends BaseService<SystemWorkMode> {

    SystemWorkMode getOne(String agvId);

    /**
     * 切换为调度模式
     */
    void switchSchedulerMode(Vehicle vehicle) throws MqttException,InterruptedException;

    /**
     * 切换为本地模式
     */
    void switchLocalMode(Vehicle vehicle);

}
