package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.DataCodeMatrix;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.PageInfo;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.DataCodeMatrixService;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 下午5:50 2019/12/27
 * @Description :
 * @Modified By :
 * @Version :
 */
@Service
public class DataCodeMatrixServiceImpl implements DataCodeMatrixService {

    @Autowired
    private AGVMapService agvMapService;


    /**
     * 从目标目录读取文件，如果不存在就从源目录写入到目标目录，再读取
     * @param srcFilePath
     * @param destFilePath
     * @return
     */
    public String getDraftFileInfo(FTPClient ftpClient,String srcFilePath,String destFilePath) throws IOException {

        //查询ftp对应地图的草稿目录的文件 /home/<USER>/youibot_map/mapName/path/draft/mapName.qr
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        boolean retrieveFile = ftpClient.retrieveFile(destFilePath, outputStream);

        //不存在的话，就复制、创建该文件
        if(!retrieveFile){
            MapFileUtils.copyFolder(srcFilePath,destFilePath,ftpClient);
        }

        //如果仍然不存在的话，就创建一个文件
        retrieveFile = ftpClient.retrieveFile(destFilePath, outputStream);
        if(!retrieveFile){
            //throw new RuntimeException("get ftp file error");
            String data = "{}";
            ftpClient.storeFile(destFilePath,new ByteArrayInputStream(data.getBytes()));
        }

        byte[] bytes = outputStream.toByteArray();
        String str = new String(bytes);
        return str;
    }



    @Override
    public void deleteById(String mapName,String id) {
        if (StringUtils.isEmpty(mapName) || StringUtils.isBlank(id)) {
            //throw new RuntimeException("mapName is null");
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_name_is_null"));
        }
        DataCodeMatrix dataCodeMatrix = this.selectById(mapName,id,true);
        if (dataCodeMatrix == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.data_code_matrix_is_null"));
        }

        String srcFilePath = FtpUtils.server_path + mapName + "/locating/current/" +mapName+ ".qr";
        String destFilePath = FtpUtils.server_path + mapName + "/path/draft/" +mapName+ ".qr";
        //1、读取文件内容
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            String mapInfoStr = getDraftFileInfo(ftpClient,srcFilePath,destFilePath);
            JSONArray list = JSONObject.parseArray(mapInfoStr);
            if(list==null || list.size() <=0){
                list = new JSONArray();
            }

            //2、处理数据
            List<DataCodeMatrix> matrixList = new ArrayList<>();
            for(int i=0;i<list.size();i++){
                JSONObject tmp = list.getJSONObject(i);
                DataCodeMatrix tmpArea = JSONObject.parseObject(tmp.toJSONString(),DataCodeMatrix.class);
                if(!tmpArea.getId().equalsIgnoreCase(id)){
                    matrixList.add(tmpArea);
                }
            }

            //3、存储到ftp文件
            byte[] bytes = JSONObject.toJSONString(matrixList).getBytes();
            ftpClient.storeFile(destFilePath,new ByteArrayInputStream(bytes));

        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(ftpClient!=null){
                FtpUtils.disconnect(ftpClient);
            }
        }
    }

    @Override
    public List<DataCodeMatrix> selectByAGVMapId(String agvMapId,boolean isDraft) {

        List<DataCodeMatrix> matrixList = new ArrayList<>();
        if(!isDraft){
            matrixList = (List<DataCodeMatrix>) AGVMapInfoCache.getCache(agvMapId).getDataCodeMatrices().values();
        }else{

            String srcFilePath = FtpUtils.server_path + agvMapId + "/locating/current/" +agvMapId+ ".qr";
            String destFilePath = FtpUtils.server_path + agvMapId + "/path/draft/" +agvMapId+ ".qr";
            //1、读取文件内容
            FTPClient ftpClient = null;
            try {
                ftpClient = FtpUtils.getConnectClient();
                String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
                JSONArray list = JSONObject.parseArray(mapInfoStr);
                if (list == null || list.size() <= 0) {
                    list = new JSONArray();
                }
                //2、处理数据
                for(int i=0;i<list.size();i++){
                    JSONObject tmp = list.getJSONObject(i);
                    DataCodeMatrix tmpObj = JSONObject.parseObject(tmp.toJSONString(),DataCodeMatrix.class);
                    matrixList.add(tmpObj);
                }

            }catch (Exception e){
                throw new RuntimeException(e);
            }finally {
                if(ftpClient!=null){
                    FtpUtils.disconnect(ftpClient);
                }
            }
        }
        return matrixList;
    }

    @Override
    public List<DataCodeMatrix> selectByAGVMapIds(List<String> agvMapIds,boolean isDraft) {
        if(CollectionUtils.isEmpty(agvMapIds)){
            //throw new RuntimeException("req param is null");
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        List<DataCodeMatrix> list = new ArrayList<>();

        if(!isDraft){
            agvMapIds.stream().forEach(agvMapId ->{
                list.addAll(AGVMapInfoCache.getCache(agvMapId).getDataCodeMatrices().values());
            });
        }else{

            //查询ftp的draft目录，
            // 如果没有的话，需要查询对应地图的current目录，写入到 draft
            agvMapIds.stream().forEach(agvMapId ->{
                List<DataCodeMatrix> tmpList = selectByAGVMapId(agvMapId, isDraft);
                list.addAll(tmpList);
            });
        }
        return list;
    }

    @Override
    public Map<String, List<DataCodeMatrix>> mapByAgvMapId(List<String> agvMapList,boolean isDraft) {
        return BeanUtils.listToMapByFiled(this.selectByAGVMapIds(agvMapList,isDraft), "agvMapId");
    }

    @Override
    public Integer deleteByAgvMapIds(List<String> agvMapIdList) {
        if(CollectionUtils.isEmpty(agvMapIdList)){
            return 0;
        }
        agvMapIdList.stream().forEach(mapName->{
            deleteByAgvMapId(mapName);
        });
        return agvMapIdList.size();
    }
    /**
     * 删除草稿目录中 的数据
     * @return
     */
    public Integer deleteByAgvMapId(String mapName) {
        if(StringUtils.isEmpty(mapName)){
            //throw new RuntimeException("req param is null");
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_name_is_null"));
        }

        String srcFilePath = FtpUtils.server_path + mapName + "/locating/current/" +mapName+ ".qr";
        String destFilePath = FtpUtils.server_path + mapName + "/path/draft/" +mapName+ ".qr";
        //1、读取文件内容
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            String mapInfoStr = getDraftFileInfo(ftpClient,srcFilePath,destFilePath);
            JSONArray list = JSONObject.parseArray(mapInfoStr);
            if(list==null || list.size() <=0){
                list = new JSONArray();
            }

            //2、处理数据
            List<DataCodeMatrix> matrixList = new ArrayList<>();

            //3、存储到ftp文件
            byte[] bytes = JSONObject.toJSONString(matrixList).getBytes();
            boolean storeFile = ftpClient.storeFile(destFilePath, new ByteArrayInputStream(bytes));
            if(!storeFile){
                throw new RuntimeException("save file to ftp error !");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(ftpClient!=null){
                FtpUtils.disconnect(ftpClient);
            }
        }
        return 0;
    }


    @Override
    public PageInfo<DataCodeMatrix> findPage(Map<String, String> searchMap,boolean isDraft) {
        //1、从内存地图 数据中，获取
        List<DataCodeMatrix> list = searchAll(searchMap,isDraft);

        Integer pageNum = null;
        Integer pageSize = null;
        String pageNumStr = searchMap.get("pageNum");
        String pageSizeStr = searchMap.get("pageSize");
        if(StringUtils.isNotBlank(pageNumStr)){
            pageNum = Integer.valueOf(pageNumStr);
        }
        if(StringUtils.isNotBlank(pageSizeStr)){
            pageSize = Integer.valueOf(pageSizeStr);
        }

        //2、分页查询
        return MapFileUtils.getPageList(list, pageNum, pageSize);
    }

    @Override
    public List<DataCodeMatrix> searchAll(Map<String, String> searchMap,boolean isDraft) {
        if(searchMap==null || searchMap.size()<=0){
            //throw new RuntimeException("param is null!");
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        String mapName = searchMap.get("agvMapName");
        if(StringUtils.isBlank(mapName)){
            //throw new RuntimeException("agvMapName param is null!");
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_name_is_null"));
        }
        List<DataCodeMatrix> list = selectByAGVMapId(mapName, isDraft);
        return list;
    }

    @Override
    public void insert(String mapName, DataCodeMatrix dataCodeMatrix) {
        if (StringUtils.isEmpty(mapName)) {
            //throw new RuntimeException("mapName is null");
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_name_is_null"));
        }

        dataCodeMatrix.setId(UUID.randomUUID().toString());

        String srcFilePath = FtpUtils.server_path + mapName + "/locating/current/" +mapName+ ".qr";
        String destFilePath = FtpUtils.server_path + mapName + "/path/draft/" +mapName+ ".qr";
        //1、读取文件内容
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
            JSONArray list = JSONObject.parseArray(mapInfoStr);
            if (list == null || list.size() <= 0) {
                list = new JSONArray();
            }
            //2、写入
            list.add(JSONObject.parseObject(JSONObject.toJSONString(dataCodeMatrix)));

            //3、存储到ftp文件
            byte[] bytes = JSONObject.toJSONString(list).getBytes();
            boolean storeFile = ftpClient.storeFile(destFilePath, new ByteArrayInputStream(bytes));
            if(!storeFile){
                //throw new RuntimeException("save file to ftp error !");
                throw new ExecuteException(MessageUtils.getMessage("service.save_ftp_file_fail"));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public DataCodeMatrix selectById(String mapName, String id, boolean isDraft) {
        if(!isDraft){
            Map<String, DataCodeMatrix> map = AGVMapInfoCache.getCache(mapName).getDataCodeMatrices();
            DataCodeMatrix filter = null;
            if(map!=null && map.size()>0){
                List<DataCodeMatrix> filterList = map.values().stream().filter((item)->item.getId().equalsIgnoreCase(id)).collect(Collectors.toList());
                filter = CollectionUtils.isNotEmpty(filterList) ? null : filterList.get(0);
            }
            return filter;
        }else {

            DataCodeMatrix filter = null;
            String srcFilePath = FtpUtils.server_path + mapName + "/locating/current/" +mapName+ ".qr";
            String destFilePath = FtpUtils.server_path + mapName + "/path/draft/" +mapName+ ".qr";
            //1、读取文件内容
            FTPClient ftpClient = null;
            try {
                ftpClient = FtpUtils.getConnectClient();
                String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
                JSONArray list = JSONObject.parseArray(mapInfoStr);
                if (list == null || list.size() <= 0) {
                    list = new JSONArray();
                }
                for (int i = 0; i < list.size(); i++) {
                    JSONObject tmp = list.getJSONObject(i);
                    DataCodeMatrix tmpArea = JSONObject.parseObject(tmp.toJSONString(), DataCodeMatrix.class);
                    if (tmpArea.getId().endsWith(id)) {
                        filter = tmpArea;
                        break;
                    }
                }
            }catch (IOException e) {
                e.printStackTrace();
            }finally {
                if(ftpClient!=null){
                    FtpUtils.disconnect(ftpClient);
                }
            }
            return filter;
        }
    }

    @Override
    public void update(String mapName, DataCodeMatrix dataCodeMatrix) {
        if (StringUtils.isEmpty(mapName)) {
            //throw new RuntimeException("mapName is null");
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_name_is_null"));
        }

        if (StringUtils.isEmpty(dataCodeMatrix.getId())) {
            dataCodeMatrix.setId(UUID.randomUUID().toString());
        }

        String srcFilePath = FtpUtils.server_path + mapName + "/locating/current/" +mapName+ ".qr";
        String destFilePath = FtpUtils.server_path + mapName + "/path/draft/" +mapName+ ".qr";
        //1、读取文件内容
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
            JSONArray list = JSONObject.parseArray(mapInfoStr);
            if(list==null || list.size() <=0){
                list = new JSONArray();
            }

            List<DataCodeMatrix> matrixList = new ArrayList<>();
            for(int i=0;i<list.size();i++){
                JSONObject tmp = list.getJSONObject(i);
                DataCodeMatrix tmpArea = JSONObject.parseObject(tmp.toJSONString(),DataCodeMatrix.class);
                if(tmpArea.getId().equalsIgnoreCase(dataCodeMatrix.getId())){
                    matrixList.add(dataCodeMatrix);
                }else{
                    matrixList.add(tmpArea);
                }
            }

            //3、存储到ftp文件
            byte[] bytes = JSONObject.toJSONString(list).getBytes();
            boolean storeFile = ftpClient.storeFile(destFilePath, new ByteArrayInputStream(bytes));
            if(!storeFile){
                //throw new RuntimeException("save file to ftp error !");
                throw new ExecuteException(MessageUtils.getMessage("service.save_ftp_file_fail"));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
