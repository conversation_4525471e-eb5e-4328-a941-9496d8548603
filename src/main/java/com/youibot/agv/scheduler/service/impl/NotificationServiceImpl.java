package com.youibot.agv.scheduler.service.impl;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youibot.agv.scheduler.constant.NotificationConstant;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.entity.Notification;
import com.youibot.agv.scheduler.listener.event.NotifyMessageEvent;
import com.youibot.agv.scheduler.mapper.NotificationMapper;
import com.youibot.agv.scheduler.service.AbnormalPromptService;
import com.youibot.agv.scheduler.service.NotificationService;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import com.youibot.agv.scheduler.webSocket.controller.NotificationSocketController;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketSendCodeEnum;
import com.youibot.agv.scheduler.webSocket.module.SocketSendModel;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 下午3:03 2019/12/31
 * @Description :
 * @Modified By :
 * @Version :
 */
@Service
public class NotificationServiceImpl extends BaseServiceImpl<Notification> implements NotificationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationServiceImpl.class);

    private static final ObjectMapper webSocketMapper = new ObjectMapper();

    @Autowired
    private NotificationMapper notificationMapper;
    
    @Autowired
    private AbnormalPromptService abnormalPromptService ;

    //标记每个类型的推送时间
//    private Map<String, Notification> commit = new HashMap<>();

    @Override
    public void sendMessage(Notification notification) {
        LOGGER.debug(notification.toString());
        if (notification == null || notification.getAgvCode() == null) {
            LOGGER.error("未传入有效的Notification");
            return;
        }
        try {
			notification.setId(null);
			//        String agvCode = notification.getAgvCode();
			//首次出现新类型，将数据存到数据库并推送到前端
			this.insert(notification);
			//异常码只有属于错误的等级才需要推送小铃铛
			if (notification.getScale() != null && NotificationConstant.ERROR_SCALE.equals(notification.getScale())) {
				PushDataThread pdt = new PushDataThread(notification);
				pdt.start();
			}
			//        if (commit.get(agvCode + notification.getType()) == null) {
			//            //首次出现新类型，将数据存到数据库并推送到前端
			//            this.insert(notification);
			//            PushDataThread pdt = new PushDataThread(notification);
			//            pdt.start();
			//            commit.put(agvCode + notification.getType(), notification);
			//        } else {
			//            //毫秒
			//            long lastTime = commit.get(agvCode + notification.getType()).getUpdateTime().getTime();
			//            long currentTime = new Date().getTime();
			//            if (currentTime - lastTime > 5 * 1000) {
			//                //大于5秒，重新推送
			//                this.insert(notification);
			//                PushDataThread pdt = new PushDataThread(notification);
			//                pdt.start();
			//                commit.put(agvCode + notification.getType(), notification);
			//            } else {
			//                //小于5秒，更新时间
			//                Notification lastNotification = commit.get(agvCode + notification.getType());
			//                lastNotification.setUpdateTime(new Date());
			//                this.update(lastNotification);
			//                commit.put(agvCode + notification.getType(), lastNotification);
			//            }
			//        }
		} finally {
			/**
	         * 发送消息给台积it系统 
	         */
			if(Objects.nonNull( notification.getErrorCode() ) ) {
				 CompletableFuture.runAsync( () ->{
			        	
			        	NotifyMessageEvent.publishAlert( notification) ;
			        });
			}
		}
    }

    @Override
    public void deleteExpireDataByTime(Integer notify) {
        List<String> idList = notificationMapper.selectExpireDataByTime(notify);
        LOGGER.debug("删除mission_work_action数据总大小：{}", CollectionUtils.isEmpty(idList) ? 0 : idList.size());
        SystemConfigJobUtil.batchSplitDelete(idList, this);
    }

    @Override
    public int updateReadStatus() {
        return notificationMapper.updateReadStatus(NotificationConstant.READ);
    }

    @Override
    public List<String> getNotificationTypeList() {
        return notificationMapper.getNotificationTypeList();
    }

    //推送数据到前端
    private class PushDataThread extends Thread {
        Notification notification;

        public PushDataThread(Notification notification) {
            this.notification = notification;
        }

        @Override
        public void run() {
            try {
                SocketSendModel sendModel = new SocketSendModel();
                sendModel.setCode(WebSocketSendCodeEnum.NOTIFICATION.getCode());
                sendModel.setData(convertBeanToMap(notification));
                String message = webSocketMapper.writeValueAsString(sendModel);
                NotificationSocketController.sendMessage(message);
            } catch (JsonProcessingException e) {
                LOGGER.error("将数据推送到前端出现错误[{}]", e);
            }
        }
    }

    private static Map<String, Object> convertBeanToMap(Object obj) {
        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                // 过滤class属性
                if (!key.equals("class")) {
                    // 得到property对应的getter方法
                    Method getter = property.getReadMethod();
                    Object value = getter.invoke(obj);
                    if (null == value) {
                        map.put(key, "");
                    } else {
                        map.put(key, value);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("convertBean2Map Error {}", e);
        }
        return map;
    }

	@Override
	public Notification sendMessage(Integer errorCode, String agvCode) {
	
		return sendMessage(errorCode, agvCode , null);
	}
	
	@Override
	public Notification sendMessage(Integer errorCode, String agvCode ,String moreHelp) {
		
	      Notification notification = new Notification();
	        notification.setAgvCode(agvCode);
	        //根据异常码获取异常信息
	        AbnormalPrompt abnormalByCode = abnormalPromptService.getAbnormalByCode(errorCode);
	        if (abnormalByCode != null) {
	            notification.setScale(abnormalByCode.getAbnormalLevel());
	            notification.setType(abnormalByCode.getAbnormalType());
	            notification.setDescription(abnormalByCode.getAbnormalDescription());
	            String help = abnormalByCode.getHelp();
	            help = StringUtils.join(help, moreHelp);
				notification.setHelp(help);
	        } else {
	            notification.setDescription("消息管理中找不到异常码" + errorCode + "的配置信息");
	        }
	        notification.setErrorCode(errorCode);
	        notification.setHaveRead(NotificationConstant.UNREAD);
	        this.sendMessage(notification);
	        return notification; 
	}
	
	@Override
	public Notification sendMessage(Integer errorCode, String agvCode ,String moreDesc , String moreHelp) {
		
	      Notification notification = new Notification();
	        notification.setAgvCode(agvCode);
	        //根据异常码获取异常信息
	        AbnormalPrompt abnormalByCode = abnormalPromptService.getAbnormalByCode(errorCode);
	        if (abnormalByCode != null) {
	            notification.setScale(abnormalByCode.getAbnormalLevel());
	            notification.setType(abnormalByCode.getAbnormalType());
	            String abnormalDescription = abnormalByCode.getAbnormalDescription();
	            if( StringUtils.isNotBlank(moreDesc)) {
	            	abnormalDescription = abnormalDescription + ":";
	            	abnormalDescription = StringUtils.join( abnormalDescription , moreDesc) ;
	            }
	          
				notification.setDescription(abnormalDescription);
	            String help = abnormalByCode.getHelp();
	            help = StringUtils.join(help, moreHelp);
				notification.setHelp(help);
	        } else {
	            notification.setDescription("消息管理中找不到异常码" + errorCode + "的配置信息");
	        }
	        notification.setErrorCode(errorCode);
	        notification.setHaveRead(NotificationConstant.UNREAD);
	        this.sendMessage(notification);
	        return notification; 
	}
	
}
