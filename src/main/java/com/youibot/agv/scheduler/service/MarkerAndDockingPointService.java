package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.MarkerAndDockingPoint;

import java.util.List;

public interface MarkerAndDockingPointService{

//    List<MarkerAndDockingPoint> selectByAGVMapId(String agvMapId);
//
//    List<MarkerAndDockingPoint> selectByAGVMapIds(List<String> agvMapIds);
//
//    List<MarkerAndDockingPoint> selectByMarkerId(String markerId);
//
//    List<MarkerAndDockingPoint> selectByDockingPointId(String dockingPointId);

}
