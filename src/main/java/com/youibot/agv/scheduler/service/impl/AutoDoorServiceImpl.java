package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.device.door.util.AutoDoorUtils;
import com.youibot.agv.scheduler.entity.AirShowerDoor;
import com.youibot.agv.scheduler.entity.AutoDoor;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mapper.AutoDoorMapper;
import com.youibot.agv.scheduler.service.AutoDoorService;
import com.youibot.agv.scheduler.util.JLibModbusUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.youibot.agv.scheduler.constant.DeviceConstant.*;

@Service
public class AutoDoorServiceImpl extends BaseServiceImpl<AutoDoor> implements AutoDoorService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoDoorServiceImpl.class);

    private static final Integer SLAVE_ID = 1;

    @Autowired
    private AutoDoorMapper mapper;

    @Override
    public void updateAutoDoor(AutoDoor autoDoor) {
        AutoDoor autoDoorDB = this.selectById(autoDoor.getId());
        if (autoDoorDB != null) {
            this.update(autoDoor);
        } else {
            throw new ExecuteException(MessageUtils.getMessage("service.auto_door_not_exist"));
        }
    }

    @Override
    public List<AutoDoor> selectByType(String type) {
        Example example = new Example(AutoDoor.class);
        example.createCriteria().andEqualTo("type", type);
        return selectByExample(example);
    }


    @Override
    public AirShowerDoor insertAirShower(AirShowerDoor airShowerDoor) {
        AutoDoor frontDoor = airShowerDoor.getFrontDoor();
        AutoDoor backDoor = airShowerDoor.getBackDoor();
        if (frontDoor == null || backDoor == null) {
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        if (!StringUtils.isEmpty(frontDoor.getId())) {
            AutoDoor autoDoorDB = super.selectById(frontDoor.getId());
            if (autoDoorDB != null) {
                throw new ExecuteException(MessageUtils.getMessage("service.air_shower_door_already_exist"));
            }
        }
        if (!StringUtils.isEmpty(backDoor.getId())) {
            AutoDoor autoDoorDB = super.selectById(backDoor.getId());
            if (autoDoorDB != null) {
                throw new ExecuteException(MessageUtils.getMessage("service.air_shower_door_already_exist"));
            }
        }

        //设置前后门与绑定关系
        String frontDoorId = UUID.randomUUID().toString();
        String backDoorId = UUID.randomUUID().toString();
        frontDoor.setId(frontDoorId);
        frontDoor.setRelationDoorId(backDoorId);
        frontDoor.setType(DOOR_TYPE_AIR_SHOWER);
        frontDoor.setPosition(AIR_SHOWER_DOOR_POSITION_FRONT);
        backDoor.setId(backDoorId);
        backDoor.setRelationDoorId(frontDoorId);
        backDoor.setType(DOOR_TYPE_AIR_SHOWER);
        backDoor.setPosition(AIR_SHOWER_DOOR_POSITION_BACK);
        this.insert(frontDoor);
        this.insert(backDoor);
        return new AirShowerDoor(frontDoor, backDoor);
    }

    @Override
    public AirShowerDoor updateAirShower(AirShowerDoor airShowerDoor) {
        AutoDoor frontDoor = airShowerDoor.getFrontDoor();
        AutoDoor backDoor = airShowerDoor.getBackDoor();
        if (frontDoor == null || backDoor == null) {
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        this.update(frontDoor);
        this.update(backDoor);
        return new AirShowerDoor(frontDoor, backDoor);
    }


    @Override
    public AirShowerDoor selectAirShowerByArbitrarilyId(String id) {
        AutoDoor autoDoor = this.selectById(id);
        if (autoDoor == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.auto_door_not_exist"));
        }
        if (StringUtils.isEmpty(autoDoor.getRelationDoorId())) {
            throw new ExecuteException(MessageUtils.getMessage("service.air_shower_relation_not_exist"));
        }
        AutoDoor relationDoor = this.selectById(autoDoor.getRelationDoorId());
        if (relationDoor == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.air_shower_relation_not_exist"));
        }
        if (AIR_SHOWER_DOOR_POSITION_FRONT.equals(autoDoor.getPosition())) {
            return new AirShowerDoor(autoDoor, relationDoor);
        } else if (AIR_SHOWER_DOOR_POSITION_BACK.equals(autoDoor.getPosition())) {
            return new AirShowerDoor(relationDoor, autoDoor);
        } else {
            throw new ExecuteException(MessageUtils.getMessage("service.auto_door_position_error"));
        }
    }

    @Override
    public void updateStatus(AutoDoor autoDoor) {
        mapper.updateStatus(autoDoor.getId(), autoDoor.getCurrentStatus(), autoDoor.getLastCloseDoorTime());
    }

    @Override
    public List<AirShowerDoor> getAirShowerDoorListByFront(List<AutoDoor> airShowerFrontDoors) {
        List<AirShowerDoor> airShowerDoorList = new ArrayList<>();
        airShowerFrontDoors.forEach(frontDoor ->
        {
            AirShowerDoor airShowerDoor = new AirShowerDoor();
            if (AIR_SHOWER_DOOR_POSITION_FRONT.equals(frontDoor.getPosition())) {
                airShowerDoor.setFrontDoor(frontDoor);
                airShowerDoor.setBackDoor(this.selectById(frontDoor.getRelationDoorId()));
                airShowerDoorList.add(airShowerDoor);
            }
        });
        return airShowerDoorList;
    }

    @Override
    public void open(String id) {
        try {
            AutoDoor autoDoor = this.selectById(id);
            if (autoDoor == null) {
                throw new ExecuteException(MessageUtils.getMessage("service.auto_door_not_exist"));
            }
            String status = autoDoor.getCurrentStatus();
            if (AUTO_DOOR_STATUS_ERROR.equals(status)) {
                throw new ExecuteException(MessageUtils.getMessage("service.auto_door_is_error"));
            } else if (AUTO_DOOR_STATUS_OPEN.equals(status)) {
                LOGGER.debug("门已开启, doorName:{}", autoDoor.getName());
                return;
            }
            //重置关门状态值
            if (autoDoor.getCloseAddress() != null) {
                JLibModbusUtils.writeSingleCoil(autoDoor.getIp(), autoDoor.getPort(), SLAVE_ID, autoDoor.getCloseAddress(), false);
            }
            //发送开门指令
            JLibModbusUtils.writeSingleCoil(autoDoor.getIp(), autoDoor.getPort(), SLAVE_ID, autoDoor.getOpenAddress(), true);
            //检测自动门状态, 直到自动门打开
            while (true) {
                Thread.sleep(500);
                boolean open = JLibModbusUtils.readCoil(autoDoor.getIp(), autoDoor.getPort(), SLAVE_ID, autoDoor.getOpenStatusAddress());
                if (open) {
                    LOGGER.debug("自动门已打开, 检测结束");
                    break;
                }
            }
        } catch (ExecuteException e) {
            LOGGER.error("打开自动门失败, {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("打开自动门失败, ", e);
            throw new ExecuteException(MessageUtils.getMessage("service.open_auto_door_failed"));
        }
    }

    @Override
    public void close(String id) {
        try {
            AutoDoor autoDoor = this.selectById(id);
            if (autoDoor == null) {
                throw new ExecuteException(MessageUtils.getMessage("service.auto_door_not_exist"));
            }
            String status = autoDoor.getCurrentStatus();
            if (AUTO_DOOR_STATUS_ERROR.equals(status)) {
                throw new ExecuteException(MessageUtils.getMessage("service.auto_door_is_error"));
            } else if (AUTO_DOOR_STATUS_CLOSE.equals(status)) {
                LOGGER.debug("门已关闭, doorName:{}", autoDoor.getName());
                return;
            }
            if (AutoDoorUtils.isUseAutoDoor(autoDoor)) {
                throw new ExecuteException(MessageUtils.getMessage("service.auto_door_is_use"));
            }
            //重置开门状态值
            JLibModbusUtils.writeSingleCoil(autoDoor.getIp(), autoDoor.getPort(), SLAVE_ID, autoDoor.getOpenAddress(), false);
            //如果关门指令地址为空则不发关门指令
            if (autoDoor.getCloseAddress() == null) {
                return;
            }
            //发送关门指令
            JLibModbusUtils.writeSingleCoil(autoDoor.getIp(), autoDoor.getPort(), SLAVE_ID, autoDoor.getCloseAddress(), true);
            //检测自动门状态, 直到自动门打开
            while (true) {
                Thread.sleep(500);
                boolean close = JLibModbusUtils.readCoil(autoDoor.getIp(), autoDoor.getPort(), SLAVE_ID, autoDoor.getCloseStatusAddress());
                if (close) {
                    LOGGER.debug("自动门已关闭, 检测结束");
                    break;
                }
            }
        } catch (ExecuteException e) {
            LOGGER.error("关闭自动门失败, {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("关闭自动门失败, ", e);
            throw new ExecuteException(MessageUtils.getMessage("service.close_auto_door_failed"));
        }
    }

}
