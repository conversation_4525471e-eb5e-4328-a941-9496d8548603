package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.MissionGroup;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.service.MissonGroupService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.util.List;

@Service
public class MissionGroupServiceImpl extends BaseServiceImpl<MissionGroup> implements MissonGroupService{

    @Override
    public Integer insert(MissionGroup missionGroup) {
        checkName(missionGroup);
        return super.insert(missionGroup);
    }

    @Override
    public Integer update(MissionGroup missionGroup) {
        checkName(missionGroup);
        return super.update(missionGroup);
    }

    private void checkName(MissionGroup missionGroup) {
        List<MissionGroup> missionGroupList = this.findAll();
        for (MissionGroup missionGroupDB : missionGroupList) {
            if (!StringUtils.isEmpty(missionGroup.getName()) && missionGroup.getName().equals(missionGroupDB.getName()) && !missionGroupDB.getId().equals(missionGroup.getId())) {
                throw new ADSParameterException(MessageFormat.format(MessageUtils.getMessage("service.name_is_exists"), missionGroup.getName()));
            }
        }
    }

}
