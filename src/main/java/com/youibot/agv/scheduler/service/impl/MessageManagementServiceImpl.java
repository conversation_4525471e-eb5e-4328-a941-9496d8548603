package com.youibot.agv.scheduler.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.annotation.DropDownSetField;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.entity.MessageExcelProperty;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.listener.MessageExcelListener;
import com.youibot.agv.scheduler.service.AbnormalPromptService;
import com.youibot.agv.scheduler.service.MessageManagementService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.util.ResolveDropAnnotationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class MessageManagementServiceImpl implements MessageManagementService {

    private static final Logger logger = LoggerFactory.getLogger(MessageManagementServiceImpl.class);

    @Autowired
    private AbnormalPromptService abnormalPromptService;

    @Override
    public PageInfo<AbnormalPrompt> findPage(Map<String, String> searchMap) {
        return abnormalPromptService.findPage(searchMap);
    }

    @Override
    public List<AbnormalPrompt> searchAll(Map<String, String> searchMap) {
        return abnormalPromptService.searchAll(searchMap);
    }

    @Override
    public void insert(AbnormalPrompt abnormalPrompt) {
        //校验异常码是否已存在
        validAbnormalCode(abnormalPrompt.getAbnormalCode());
        abnormalPromptService.insert(abnormalPrompt);
    }

    @Override
    public AbnormalPrompt selectById(String id) {
        return abnormalPromptService.selectById(id);
    }

    @Override
    public void update(AbnormalPrompt abnormalPromptNew) {
        AbnormalPrompt abnormalPromptOld = abnormalPromptService.selectById(abnormalPromptNew.getId());
        //判断是否有修改异常码
        if (!abnormalPromptOld.getAbnormalCode().equals(abnormalPromptNew.getAbnormalCode())) {
            //不相同，则校验数据库是否存在该异常码
            validAbnormalCode(abnormalPromptNew.getAbnormalCode());
        }
        abnormalPromptService.update(abnormalPromptNew);
    }

    @Override
    public void deleteById(String id) {
        abnormalPromptService.deleteById(id);
    }

    @Override
    public List<String> getAbnormalTypeList() {
        return abnormalPromptService.getAbnormalTypeList();
    }

    @Override
    public void downExcelFile(Integer abnormalLevel, String abnormalType, Integer abnormalCode, String abnormalDescription, HttpServletResponse response) {
        //根据条件查询需要导出的数据
        List<AbnormalPrompt> abnormalPromptList = queryAbnormalData(abnormalLevel, abnormalType, abnormalCode, abnormalDescription);
        List<MessageExcelProperty> data = new ArrayList<>();
        if (!CollectionUtils.isEmpty(abnormalPromptList) && abnormalPromptList.size() > 0) {
            abnormalPromptList.forEach(dto -> {
                MessageExcelProperty messageExcelProperty = new MessageExcelProperty();
                if (dto.getAbnormalLevel() == 1) {
                    messageExcelProperty.setAbnormalLevel("普通");
                } else if (dto.getAbnormalLevel() == 2) {
                    messageExcelProperty.setAbnormalLevel("警告");
                } else {
                    messageExcelProperty.setAbnormalLevel("错误");
                }
                messageExcelProperty.setAbnormalType(dto.getAbnormalType());
                messageExcelProperty.setAbnormalCode(dto.getAbnormalCode());
                messageExcelProperty.setAbnormalDescription(dto.getAbnormalDescription());
                messageExcelProperty.setHelp(dto.getHelp());
                data.add(messageExcelProperty);
            });
        }
        //获取响应字段对应的下拉集合
        List<Map<String, Object>> list = getDownDropMap();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String fileName = "消息管理" + sdf.format(new Date()) + ".xlsx";
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-type", "application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), "iso8859-1"));
            EasyExcel.write(response.getOutputStream(), MessageExcelProperty.class)
                    .registerWriteHandler(new ProductCellWriteHandler(list))
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("异常信息")
                    .doWrite(data);
        } catch (IOException e) {
            logger.error("导出excel文件失败{}", e.getMessage());
        }
    }

    private List<AbnormalPrompt> queryAbnormalData(Integer abnormalLevel, String abnormalType, Integer abnormalCode, String abnormalDescription) {
        return abnormalPromptService.queryAbnormalData(abnormalLevel, abnormalType, abnormalCode, abnormalDescription);
    }

    /**
     * 获取响应字段对应的下拉集合
     *
     * @return
     */
    private List<Map<String, Object>> getDownDropMap() {
        Field[] fields = MessageExcelProperty.class.getDeclaredFields();
        // 响应字段对应的下拉集合
        List<Map<String, Object>> list = new ArrayList<>();
        Field field = null;
        // 循环判断哪些字段有下拉数据集，并获取
        for (Field value : fields) {
            field = value;
            // 解析注解信息
            DropDownSetField dropDownSetField = field.getAnnotation(DropDownSetField.class);
            if (null != dropDownSetField) {
                Map<String, Object> sources = ResolveDropAnnotationUtil.resolve(dropDownSetField);
                if (sources != null) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("index", sources.get("index"));
                    map.put("source", sources.get("source"));
                    list.add(map);
                }
            }
        }
        return list;
    }

    @Override
    public void uploadExcelFile(MultipartFile multiPartFile) {
        // 获取文件全名
        String allName = multiPartFile.getOriginalFilename();
        // 获取文件名后缀
        String suffixName = allName.substring(allName.lastIndexOf(".") + 1);
        // 判断是否excel文件格式
        if (!checkExtension(suffixName)) {
            throw new ExecuteException(MessageUtils.getMessage("service.must_upload_excel_file_format"));
        }
        try {
            //实例化监听
            ExcelReader excelReader = EasyExcel.read(multiPartFile.getInputStream(),
                    MessageExcelProperty.class, new MessageExcelListener(new MessageExcelProperty())).build();
            //读取excel第一页内容
            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            //读取Excel
            excelReader.read(readSheet);
            //这里千万别忘记关闭，读的时候会创建临时文件，到时磁盘会崩的
            excelReader.finish();
        } catch (IOException e) {
            logger.error("导入excel文件失败{}", e.getMessage());
            throw new ExecuteException(MessageUtils.getMessage("service.read_excel_file_fail"));
        }
    }


    private boolean checkExtension(String suffixName) {
        return Lists.newArrayList("xls", "xlsx", "XLS", "XLSX").contains(suffixName);
    }

    private void validAbnormalCode(Integer abnormalCode) {
        AbnormalPrompt abnormalPrompt = abnormalPromptService.getAbnormalByCode(abnormalCode);
        if (abnormalPrompt != null) {
            throw new ExecuteException(MessageUtils.getMessage("service.abnormal_code_is_exists"));
        }
    }
}
