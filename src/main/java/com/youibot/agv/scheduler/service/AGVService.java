package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.Agv;

import java.util.List;
import java.util.Map;

public interface AGVService extends BaseService<Agv>{

	String getAGVName(String id);

	/**
	 *
	 * 查询所有agv
	 */
	List<Agv> selectAll();

	List<Agv> selectByAgvGroupId(String agvGroupId);

	List<Agv> selectByAgvType(String agvType);

	/**
	 * 根据agvCode查询
	 * @param agvCode
	 * @return
	 */
	Agv selectByAgvCode(String agvCode);

	void updateOnlineStatusByAgvCode(String agvCode, Integer onlineStatus);

	/**
	 * 批量停止任务
	 * @param agvCodes
	 */
    void stopBatch(List<String> agvCodes);

	/**
	 * 根据机器人编号获取机器人正在执行的作业id
	 * @param agvCode
	 */
    String getVehicleMissionWorkId(String agvCode);

	/**
	 * 清理机器人所占资源
	 * @param agvCode
	 */
	void clearResource(String agvCode);

	
	Map<String, List<String>> getAGVBindMarkers(String bindMarkers);

	
	
	/** 持久化linePatrol
	 * @param agvCode
	 */
	public void updateLinePatroline(String agvCode) ;


	void updateLinePatroline(String agvCode, boolean direct);

	/**
	 * 启用 和禁用
	 */
	void updateEnable( );
}
