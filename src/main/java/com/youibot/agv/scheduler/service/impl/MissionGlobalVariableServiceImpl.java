package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.MissionGlobalVariable;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionGlobalVariableService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class MissionGlobalVariableServiceImpl extends BaseMissionServiceImpl<MissionGlobalVariable> implements MissionGlobalVariableService {

    @Override
    public Integer insert(MissionGlobalVariable variable) {
        checkData(variable);
        return super.insert(variable);
    }

    @Override
    public Integer update(MissionGlobalVariable variable) {
        checkData(variable);
        return super.update(variable);
    }

    private void checkData(MissionGlobalVariable variable) {
        String variableKey = variable.getVariableKey();
        String variableValue = variable.getVariableValue();
        String missionId = variable.getMissionId();
        if (StringUtils.isEmpty(variableKey) || StringUtils.isEmpty(variableValue) || StringUtils.isEmpty(missionId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter") + " key=" + variableKey + ", value=" + variableValue + ", missionId=" + missionId);
        }

        List<MissionGlobalVariable> missionGlobalVariables = this.selectByMissionId(missionId);
        for (MissionGlobalVariable variableDB : missionGlobalVariables) {
            if (variable.getVariableKey().equals(variableDB.getVariableKey()) && !variableDB.getId().equals(variable.getId())) {
                throw new ExecuteException("key=" + variable.getVariableKey() + " " + MessageUtils.getMessage("service.mission_variable_already_exists"));
            }
        }
    }

    @Override
    public List<MissionGlobalVariable> selectByMissionId(String missionId) {
        Example example = new Example(MissionGlobalVariable.class);
        example.createCriteria().andEqualTo("missionId", missionId);
        return selectByExample(example);
    }
}
