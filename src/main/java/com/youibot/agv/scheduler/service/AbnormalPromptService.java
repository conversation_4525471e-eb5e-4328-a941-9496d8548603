package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.AbnormalPrompt;

import java.util.List;

public interface AbnormalPromptService extends BaseService<AbnormalPrompt> {

    /**
     * 根据异常编码获取异常信息
     *
     * @param abnormalCode
     * @return
     */
    AbnormalPrompt getAbnormalByCode(Integer abnormalCode);

    String getAbnormalMsgByCode(Integer abnormalCode);

    /**
     * 获取所有去重后的异常类型
     *
     * @return
     */
    List<String> getAbnormalTypeList();

    /**
     * 获取所有异常编码
     *
     * @return
     */
    List<Integer> getAbnormalCodeList();

    List<AbnormalPrompt> queryAbnormalData(Integer abnormalLevel, String abnormalType, Integer abnormalCode, String abnormalDescription);
}
