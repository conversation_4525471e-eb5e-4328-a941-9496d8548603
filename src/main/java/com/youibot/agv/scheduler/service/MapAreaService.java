package com.youibot.agv.scheduler.service;


import com.youibot.agv.scheduler.entity.MapArea;
import com.youibot.agv.scheduler.map.entity.PageInfo;

import java.util.List;
import java.util.Map;

public interface MapAreaService {

    List<MapArea> selectAreaByType(String type,String agvMapId,boolean isDraft);

    List<MapArea> selectByAGVMapId(String agvMapId,boolean isDraft);

    List selectByAGVMapIds(List<String> agvMapIds, boolean isDraft);

    List<MapArea> selectByAGVMapIdAndType(String agvMapId, String areaTypeId,boolean isDraft);

    Map<String, List<MapArea>> mapByAgvMapId(List<String> agvMapList,boolean isDraft);

    Integer deleteByAgvMapIds(List<String> agvMapIdList);


    void deleteByAGVMapId(String agvMapId);

    PageInfo<MapArea> findPage(Map<String, String> searchMap,boolean isDraft);

    List<MapArea> searchAll(Map<String, String> searchMap,boolean isDraft);

    MapArea insert(MapArea mapArea);

    MapArea selectById(String mapName, String id, boolean isDraft);

    MapArea update(MapArea mapArea);

    int deleteById(String mapName,String id);
}
