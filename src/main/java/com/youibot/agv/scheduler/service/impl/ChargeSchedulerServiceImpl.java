package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.ChargeScheduler;
import com.youibot.agv.scheduler.mapper.ChargeSchedulerMapper;
import com.youibot.agv.scheduler.service.ChargeSchedulerService;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 14:44
 */
@Service
public class ChargeSchedulerServiceImpl extends BaseServiceImpl<ChargeScheduler> implements ChargeSchedulerService {

    private static final Logger logger = LoggerFactory.getLogger(ChargeSchedulerServiceImpl.class);

    @Autowired
    private ChargeSchedulerMapper chargeSchedulerMapper;

    ChargeScheduler selectById(Long id) {
        Example example = new Example(ChargeScheduler.class);
        example.createCriteria().andEqualTo("id", id);
        return this.selectOneByExample(example);
    }

    @Override
    public List<ChargeScheduler> selectCreate() {
        Example example = new Example(ChargeScheduler.class);
        example.createCriteria().andEqualTo("status", "CREATE");
        return this.selectByExample(example);
    }

    @Override
    public List<ChargeScheduler> selectRunning() {
        Example example = new Example(ChargeScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT"));
        return this.selectByExample(example);
    }

    @Override
    public ChargeScheduler selectRunningByVehicle(String vehicleId) {
        Example example = new Example(ChargeScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT")).andEqualTo("vehicleId", vehicleId);
        return this.selectOneByExample(example);
    }

    @Override
    public ChargeScheduler selectRunningByChargeId(String chargeId) {
        Example example = new Example(ChargeScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT")).andEqualTo("chargeId", chargeId);
        example.setOrderByClause("create_time desc");//根据创建时间降序
        return this.selectOneByExample(example);
    }

    @Override
    public void updateCancel(Long id) {
        ChargeScheduler chargeScheduler = this.selectById(id);
        if (chargeScheduler == null) {
            logger.warn("agvCode:[{}],event:[取消充电调度],充电调度数据为空：{}", "",id);
            return;
        }
        chargeScheduler.setStatus(chargeScheduler.STATUS_CANCEL);
        chargeScheduler.setFinishTime(new Date());
        this.update(chargeScheduler);
        logger.debug("agvCode:[{}],event:[取消充电调度],取消充电调度完成：{}", chargeScheduler.getVehicleId(), JSONObject.toJSONString(chargeScheduler));
    }
    
    @Override
    public void updateCancel( ) {
         List<ChargeScheduler> list = this.selectRunning();
        if ( CollectionUtils.isEmpty(list)) {
            logger.warn("event:[取消充电调度],充电调度数据为空");
            return;
        }
        list = list.parallelStream().map( chargeScheduler ->{
        	  chargeScheduler.setStatus(chargeScheduler.STATUS_CANCEL);
              chargeScheduler.setFinishTime(new Date());
        	return chargeScheduler;
        }).collect(Collectors.toList());
      
       this.batchUpdate(list);
        logger.debug("event:[取消所有充电调度],取消充电调度完成" );
    }

    @Override
    public void updateSuccess(Long id) {
        ChargeScheduler chargeScheduler = this.selectById(id);
        if (chargeScheduler == null) {
            logger.warn("success charge scheduler is fault, work scheduler is null.");
            return;
        }
        chargeScheduler.setStatus(chargeScheduler.STATUS_SUCCESS);
        chargeScheduler.setFinishTime(new Date());
        this.update(chargeScheduler);
    }

    @Override
    public void updateStart(Long id) {
        ChargeScheduler chargeScheduler = this.selectById(id);
        if (chargeScheduler == null) {
            logger.warn("start charge scheduler is fault, work scheduler is null.");
            return;
        }
        chargeScheduler.setStatus(chargeScheduler.STATUS_START);
        chargeScheduler.setStartTime(new Date());
        this.update(chargeScheduler);
    }

    @Override
    public void updateFault(Long id, String faultMessage) {
        ChargeScheduler chargeScheduler = this.selectById(id);
        if (chargeScheduler == null) {
            logger.warn("fault charge scheduler is fault, work scheduler is null.");
            return;
        }
        chargeScheduler.setStatus(chargeScheduler.STATUS_FAULT);
        chargeScheduler.setFaultMessage(faultMessage);
        this.update(chargeScheduler);
        logger.debug("agvCode:[{}],event:[充电调度异常],异常原因：{}", chargeScheduler.getVehicleId(), faultMessage);
    }

    @Override
    public List<String> selectCancelByFinishTimeDiff(String timeDiff) {
        return chargeSchedulerMapper.selectCancelByFinishTimeDiff(timeDiff);
    }

    @Override
    public ChargeScheduler selectLastOne(String vehicleId){
        ChargeScheduler chargeScheduler = chargeSchedulerMapper.selectLastOne(vehicleId);
        return chargeScheduler;
    }

    @Override
    public ChargeScheduler selectLatestSuccessCorrectCharge(String vehicleId) {
        Example example = new Example(ChargeScheduler.class);
        example.createCriteria().andEqualTo("vehicleId",vehicleId).andEqualTo("chargeType",1).andEqualTo("status","SUCCESS");
        example.setOrderByClause("create_time desc");
        return this.selectOneByExample(example);
    }

    @Override
    public ChargeScheduler selectFirstSuccessCommonCharge(String vehicleId) {
        Example example = new Example(ChargeScheduler.class);
        example.createCriteria().andEqualTo("vehicleId",vehicleId).andEqualTo("chargeType",0).andEqualTo("status","SUCCESS");
        example.setOrderByClause("create_time asc");
        return this.selectOneByExample(example);
    }

    @Override
    public List<ChargeScheduler> selectRunningCorrectCharge() {
        Example example = new Example(ChargeScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT")).andEqualTo("chargeType",1);
        return this.selectByExample(example);
    }

    @Override
    public void deleteExpireDataByTime(Integer timeInterval) {
        List<String> ids = chargeSchedulerMapper.selectExpireDataByTime(timeInterval);
        logger.debug("删除charge_scheduler数据总大小：{}", CollectionUtils.isEmpty(ids) ? 0 : ids.size());
        SystemConfigJobUtil.batchSplitDelete(ids, this);
    }
}
