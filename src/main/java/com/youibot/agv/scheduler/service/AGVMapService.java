package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.map.entity.AGVMapResult;
import com.youibot.agv.scheduler.map.entity.PageInfo;
import com.youibot.agv.scheduler.map.entity.PathResultData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface AGVMapService {

    /**
     * 批量指定地图
     *
     * @param mapId
     * @param agvCodeList
     */
    void batchAppointMap(String mapId, List<String> agvCodeList);

    PageInfo<AGVMap> findPage(Map<String, String> searchMap);

    /**
     * 根据参数列表查询数据
     *
     * @param searchMap
     * @return
     */
    List<AGVMapResult> searchAll(Map<String, String> searchMap);

    List<AGVMap> findAll();

    AGVMap insert(AGVMap agvMap);

    AGVMap selectById(String id);

    AGVMapResult selectAGVMapResultById(String id);

    AGVMap update(AGVMap agvMap);

    void delete(String id);

    /**
     * 查询草稿文件是否存在
     *
     * @param id
     * @return
     */
    boolean selectDraftFile(String id);

    /**
     * 删除草稿文件
     *
     * @param id
     * @return
     */
    void deleteDraftFile(String id);

    void batchUpdatePathInfo(PathResultData pathResult, String agvMapName);

    void batchDelPathInfo(PathResultData pathResult, String agvMapName);

    void pushMapData(String mapName);

    String importMapData(MultipartFile multiPartFile) throws Exception;

    String importBackgroundImgData(MultipartFile multiPartFile,String mapName) throws Exception;

    String delBackgroundImgData(String mapName) throws Exception;

    void exportMapData(HttpServletResponse response, String mapId);

    void refresh(String mapName);

	void syncSimulationAgvMap(AGVMap agvMap);

	void syncMap(String agvCode, List<AGVMap> agvMaps);
}
