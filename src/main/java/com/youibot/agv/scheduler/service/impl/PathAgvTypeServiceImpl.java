package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.PathAgvType;
import com.youibot.agv.scheduler.service.PathAgvTypeService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

@Service
public class PathAgvTypeServiceImpl extends BaseServiceImpl<PathAgvType> implements PathAgvTypeService {

    @Override
    public PathAgvType selectByPathId(String pathId) {
        Example example = new Example(PathAgvType.class);
        example.createCriteria().andEqualTo("pathId", pathId);
        return super.selectOneByExample(example);
    }
}
