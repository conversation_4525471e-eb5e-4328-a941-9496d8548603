package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.AdjustAction;

import java.util.List;
import java.util.Map;

/**
 * @Author：yangpeilin
 * @Date: 2020/7/23 18:59
 */
public interface AdjustActionService extends BaseService<AdjustAction> {

    AdjustAction selectByMarkerIdAndDestMarkerId(String agvMapId, String markerId, String destMarkerId);

    /**
     * 获取调整点中的调整动作
     * @param markerIds
     * @param agvMapId
     * @return
     */
    Map<String, List<AdjustAction>> mapByMakerIds(List<String> markerIds, String agvMapId);

    /**
     * 两点之间是否有调整点数据
     * @param agvMapId
     * @param startMarkerId
     * @param endMarkerId
     * @return
     */
    List<AdjustAction> selectByReplaceMarkerId(String agvMapId, String startMarkerId, String endMarkerId);

    /**
     * 通过关联标记点和目标标记点查询调整信息
     * @param markerId
     * @param destMarkerId
     * @return
     */
    AdjustAction selectByMarkerIdAndDestMarkerId(String markerId, String destMarkerId);

    List<AdjustAction> selectByAGVMapId(String agvMapId);

    void deleteByAGVMapId(String agvMapId,String agvId);
}
