package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.AdjustAction;
import com.youibot.agv.scheduler.map.entity.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * @Author：yangpeilin
 * @Date: 2020/7/23 18:59
 */
public interface AdjustActionService {

    AdjustAction selectByMarkerIdAndDestMarkerId(String agvMapId, String markerId, String destMarkerId,boolean isDraft);

    List<AdjustAction> selectByAGVMapId(String agvMapId,boolean isDraft);

    List<AdjustAction> selectByAGVMapIds(List<String> agvMapIds,boolean isDraft);

    Map<String, List<AdjustAction>> mapByAgvMapId(List<String> agvMapList,boolean isDraft);

    Integer deleteByAgvMapIds(List<String> agvMapIdList);


    PageInfo<AdjustAction> findPage(Map<String, String> searchMap,boolean isDraft);

    List<AdjustAction> searchAll(Map<String, String> searchMap,boolean isDraft);

    AdjustAction insert(AdjustAction dockingPoint);

    AdjustAction selectById(String mapName,String id,boolean isDraft);

    AdjustAction update(AdjustAction dockingPoint);

    void deleteById(String agvMapId,String id);

    void deleteByIds(String agvMapId, List<String> ids);

    List<AdjustAction> selectByMarkerId(String id, String agvMapName, boolean isDraft);
}
