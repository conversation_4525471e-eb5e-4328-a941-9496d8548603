package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.WorkCycleConfig;




/**
  * @ClassName: WorkCycleConfigService
  * @Description: 台积电项目环状线配置
  * <AUTHOR>
  * @date 2021年12月27日 上午11:20:04
  *
  */
public interface WorkCycleConfigService extends BaseService<WorkCycleConfig> {
	
	
	
	/**
	  * @Title: refreshCycleConfig
	  * @Description: 刷新环状线的配置
	  * @param     设定文件
	  * @return void    返回类型
	  * @throws
	  */
	
	
	public void refreshCycleConfig() ;

	/**
	 * 刷新工作站相关配置项
	 */
	void refresh();
	
}
