package com.youibot.agv.scheduler.service;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.dto.AgvBatchParam;
import com.youibot.agv.scheduler.dto.AgvBatchResult;
import com.youibot.agv.scheduler.entity.SchedulerSystems;

import java.util.List;
import java.util.Map;

public interface SchedulerSystemsService extends BaseService<SchedulerSystems>{

    SchedulerSystems save(SchedulerSystems schedulerSystems);

    SchedulerSystems updateById(SchedulerSystems schedulerSystems);

    int delete(String id);

    List<AgvBatchResult> batchScheduler(AgvBatchParam agvBatchParam) throws InterruptedException;

    List<AgvBatchResult> batchLocal(AgvBatchParam agvBatchParam);

    List<AgvBatchResult> batchDelete(AgvBatchParam agvBatchParam);

    PageInfo<SchedulerSystems> selectPage(Map<String, String> searchMap);

    List<AgvBatchResult> batchStop(AgvBatchParam agvBatchParam);

}
