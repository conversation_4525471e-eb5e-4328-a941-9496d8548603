package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.vo.MissionActionStatisticVO;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.mapper.MissionWorkActionMapper;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.util.Base64Utils;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;

@Slf4j
@Service
public class MissionWorkActionServiceImpl extends BaseServiceImpl<MissionWorkAction> implements MissionWorkActionService {

    @Autowired
    private MissionWorkActionMapper missionWorkActionMapper;

    @Override
    public MissionWorkAction selectById(String id) {
        MissionWorkAction missionWorkAction = super.selectById(id);
        if (missionWorkAction == null) {
            return null;
        }
        if (RESULT_TYPE_IMAGE.equals(missionWorkAction.getResultType())) {
            String resultData = missionWorkAction.getResultData();
            if (!StringUtils.isEmpty(resultData)) {
                JSONObject dataJson = JSONObject.parseObject(resultData);
                if (dataJson != null) {
                    JSONArray urlArray = dataJson.getJSONArray("base64");
                    if (urlArray != null && !urlArray.isEmpty()) {
                        JSONArray base64Array = new JSONArray();
                        for (Object o : urlArray) {
                            String base64 = "data:image/jpg;base64," + Base64Utils.imageToBase64(o.toString());
                            base64Array.add(base64);
                        }
                        dataJson.put("base64", base64Array);
                        missionWorkAction.setResultData(dataJson.toJSONString());
                    }
                }
            }
        }
        return missionWorkAction;
    }

    @Override
    public int updateStatus(MissionWorkAction missionWorkAction) {
        if (MISSION_WORK_ACTION_STATUS_FAULT.equals(missionWorkAction.getStatus()) || MISSION_WORK_ACTION_STATUS_SUCCESS.equals(missionWorkAction.getStatus())) {
            missionWorkAction.setEndTime(new Date());
        }
        return this.update(missionWorkAction);
    }

    @Override
    public void updateStatus(MissionWorkAction missionWorkAction, String status) {
        if (missionWorkAction != null) {
            missionWorkAction.setStatus(status);
            this.updateStatus(missionWorkAction);
        }
    }

    @Override
    public MissionWorkAction createMissionWorkAction(MissionAction missionAction, MissionWork missionWork) {
        Integer actionSequence = missionWork.getCurrentActionSequence() != null ? missionWork.getCurrentActionSequence() + 1 : 1;
        missionWork.setCurrentActionSequence(actionSequence);
        missionWork.setCurrentActionName(missionAction.getName());
        MissionWorkAction missionWorkAction = new MissionWorkAction();
        missionWorkAction.setAgvCode(missionWork.getAgvCode());
        missionWorkAction.setMissionActionId(missionAction.getId());
        missionWorkAction.setMissionWorkId(missionWork.getId());
        missionWorkAction.setActionType(missionAction.getActionType());
        missionWorkAction.setName(missionAction.getName());
        missionWorkAction.setChildType(missionAction.getChildType());
        missionWorkAction.setParentActionId(missionAction.getParentActionId());
        missionWorkAction.setStatus(MISSION_WORK_ACTION_STATUS_START);
        missionWorkAction.setStartTime(new Date());
//        this.insert(missionWorkAction);
        return missionWorkAction;
    }

    @Override
    public List<MissionWorkAction> selectByMissionWorkIdAndType(String missionWorkId, String actionType) {
        Example example = new Example(MissionWorkAction.class);
        example.createCriteria().andEqualTo("actionType", actionType)
                .andEqualTo("missionWorkId", missionWorkId)
                .andNotEqualTo("thisLoopCompletes", false);
        return super.selectByExample(example);
    }

    @Override
    public List<MissionWorkAction> selectByMissionWorkId(String missionWorkId) {
        Example example = new Example(MissionWorkAction.class);
        example.createCriteria().andEqualTo("missionWorkId", missionWorkId);
        example.setOrderByClause("update_time asc");
        return super.selectByExample(example);
    }

    @Override
    public MissionWorkAction selectRunningActionByMissionWorkId(String missionWorkId) {
        Example example = new Example(MissionWorkAction.class);
        List<String> statusList = Arrays.asList(MISSION_WORK_ACTION_STATUS_START,MISSION_WORK_ACTION_STATUS_RUNNING,MISSION_WORK_ACTION_STATUS_FAULT);
        example.createCriteria().andEqualTo("missionWorkId", missionWorkId)
                .andIn("status",statusList);
        return super.selectOneByExample(example);
    }

    @Override
    public void deleteExpireDataByTime(Integer missionWork) {
        List<String> idList = missionWorkActionMapper.selectExpireDataByTime(missionWork);
        log.debug("删除mission_work_action数据总大小：{}", CollectionUtils.isEmpty(idList) ? 0 : idList.size());
        SystemConfigJobUtil.batchSplitDelete(idList, this);
    }

    @Override
    public void mergeUpdateAndInsert(MissionWorkAction missionWorkAction) {
        MissionWorkAction missionWorkActionDto = selectById(missionWorkAction.getId());
        if (missionWorkActionDto == null) {
            super.insert(missionWorkAction);
        } else {
            missionWorkAction.setUpdateTime(new Date());
            super.update(missionWorkAction);
        }
    }

    @Override
    public void updateMissionWorkActionStatus(String missionWorkId, String status) {
        missionWorkActionMapper.updateMissionWorkActionStatus(missionWorkId, status);
    }


    /**
     * 查询某个时间范围内，任务动作统计信息
     *
     * 如果小车编码agvCode存在，就查询单个小车的
     * 否则，就查询所有小车的
     */
    @Override
    public List<MissionActionStatisticVO> getMissionActionStatistic(String agvCode, String startTime, String endTime) {
        Date now = new Date();
        Date startDate = DateUtils.getFormatDate(startTime);
        //如果开始时间晚于当前时间，没有数据，不用查询
        if(startDate.after(now)){
            return Collections.emptyList();
        }
        Date endDate = DateUtils.getFormatDate(endTime);
        if(now.before(endDate)){
            endDate = now;
        }

        List<MissionWorkAction> actionList = missionWorkActionMapper.getMissionWorkActionStatistic(agvCode, startTime, endTime);
        if(CollectionUtils.isEmpty(actionList)){
            return Collections.emptyList();
        }
        //按小车分组
        Map<String, List<MissionWorkAction>> agvMap = actionList.stream().collect(Collectors.groupingBy(MissionWorkAction::getAgvCode));
        if(agvMap==null || agvMap.size()<=0){
            return Collections.emptyList();
        }

        List<MissionActionStatisticVO> resultList = new ArrayList<>();
        for(Map.Entry<String, List<MissionWorkAction>> item : agvMap.entrySet()){
            String agv = item.getKey();
            List<MissionWorkAction> tmpList = item.getValue();
            if(CollectionUtils.isEmpty(tmpList)){
                continue;
            }
            //处理单个小车的任务动作统计
            resultList.addAll(computeSingleVehicleActionStatistic(startDate, endDate, agv, tmpList));
        }

        return resultList;
    }

    //单个小车的所有动作统计
    private List<MissionActionStatisticVO> computeSingleVehicleActionStatistic(Date startDate, Date endDate, String agvCode,List<MissionWorkAction> actionList){
        List<MissionActionStatisticVO> resultList = new ArrayList<>();

        //按任务动作id分组
        Map<String, List<MissionWorkAction>> collect = actionList.stream().collect(Collectors.groupingBy(MissionWorkAction::getMissionActionId));
        for(Map.Entry<String, List<MissionWorkAction>> item : collect.entrySet()){
            List<MissionWorkAction> tmpList = item.getValue();
            if(CollectionUtils.isEmpty(tmpList)){
                continue;
            }

            MissionActionStatisticVO vo = new MissionActionStatisticVO();
            vo.setAgvCode(agvCode);
            vo.setMissionActionId(item.getKey());
            vo.setMissionActionName(tmpList.get(0).getName());
            vo.setTotalNum(tmpList.size());

            //计算秒值
            long seconds = tmpList.stream().mapToLong((action)->{
                if(action.getStartTime()==null){
                    return 0L;
                }

                Date tmpStart = action.getStartTime();
                if(action.getStartTime().before(startDate)){
                    tmpStart = startDate;
                }
                //此为运行中的动作
                if(action.getEndTime()==null){
                    return DateUtils.getSeconds(tmpStart, endDate);
                }

                //此为运行结束的动作
                Date tmpEnd = action.getEndTime();
                if(action.getEndTime().after(endDate)){
                    tmpEnd = endDate;
                }
                return DateUtils.getSeconds(tmpStart, tmpEnd);
            }).sum();
            vo.setTotalTime(seconds);

            resultList.add(vo);
        }
        return resultList;
    }


}
