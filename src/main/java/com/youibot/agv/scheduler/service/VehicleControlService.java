package com.youibot.agv.scheduler.service;

import java.io.IOException;
import java.util.LinkedHashSet;

/** 车辆控制
 * <AUTHOR>
 *				
 */
public interface VehicleControlService {

	
	
	    /**切换为手工控制模式
	     * @param agvCode
	     * @throws IOException
	     */
	    public void manualMode( String agvCode) throws IOException ;

	
	    /**切换为自动控制模式
	     * @param agvCode
	     * @throws IOException
	     * @throws InterruptedException
	     */
	    public void autoMode( String agvCode) throws IOException, InterruptedException ;

	    /**批量切换为手工控制模式
	     * @param agvCodes
	     * @throws IOException
	     */
	    public void manualModeBatch( LinkedHashSet<String>  agvCodes) throws IOException ;
	    
	
	    /**批量切换为自动控制模式
	     * @param agvCodes
	     * @throws IOException
	     * @throws InterruptedException
	     */
	    public void autoModeBatch( LinkedHashSet<String>  agvCodes) throws IOException, InterruptedException ;
	    
	    
	    
	 
	    /**台积电项目切换巡线,非巡线
	     * @param agvCode
	     * @param linePatrolMode
	     */
	    public void linecePatro( String agvCode , Boolean linePatrolMode) ;
	    
	    
	    
	
	    /**台积电项目切换充电,取消充电
	     * @param agvCode
	     * @param recharge
	     */
	    public void recharge(String  agvCode, Boolean recharge) ;
	    
	    
	 
	    /**台积电项目切换巡线,非巡线-批量
	     * @param agvCodes
	     * @param linePatrolMode
	     */
	    public void linecePatroBatch( LinkedHashSet<String>  agvCodes ,  Boolean linePatrolMode);
	    
	    
	    
	 
	    /**台积电项目切换充电,取消充电-批量
	     * @param agvCodes
	     * @param recharge
	     */
	    public void rechargeBatch( LinkedHashSet<String>  agvCodes  , Boolean recharge) ;

	    
		
}
