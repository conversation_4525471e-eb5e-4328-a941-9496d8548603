package com.youibot.agv.scheduler.service;


import com.youibot.agv.scheduler.entity.*;

import java.util.List;

/**
* <AUTHOR>  E-mail:<EMAIL>
* @version CreateTime: 2019年5月15日 上午10:16:35
 */
public interface MissionWorkActionParameterService extends BaseService<MissionWorkActionParameter>{

	List<MissionWorkActionParameter> selectByMissionWorkActionId(String missionWorkActionId);

    List<MissionWorkActionParameter> selectByMissionWorkActionIdAndKey(String missionWorkActionId,String parameterKey);

	void insertByMissionActionParameterList(List<MissionActionParameter> missionActionParameters, String missionWorkActionId);

    String getMissionWorkActionParameters(MissionWorkAction missionWorkAction, String missionWorkId, List<MissionWorkGlobalVariable> missionWorkGlobalVariables);

    /**
     * 根据时间设置删除过期数据
     * @param missionWork
     */
    void deleteExpireDataByTime(Integer missionWork);
}
