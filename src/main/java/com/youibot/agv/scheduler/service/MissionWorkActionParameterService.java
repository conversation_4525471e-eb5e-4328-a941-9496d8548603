package com.youibot.agv.scheduler.service;


import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionActionParameter;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.entity.MissionWorkActionParameter;

import java.util.List;

/**
* <AUTHOR>  E-mail:<EMAIL>
* @version CreateTime: 2019年5月15日 上午10:16:35
 */
public interface MissionWorkActionParameterService extends BaseService<MissionWorkActionParameter>{

	List<MissionWorkActionParameter> selectByMissionWorkActionId(String missionWorkActionId);

	void insertByMissionActionParameterList(List<MissionActionParameter> missionActionParameters, String missionWorkActionId,Boolean isRandomPointMaker);

    void deleteByMissionWorkActionId(String missionWorkActionId);

    void addMissionWorkActionParameter(MissionWorkAction missionWorkAction, MissionAction missionAction) throws InterruptedException;

    String getMissionWorkActionParameters(MissionWorkAction missionWorkAction, String missionWorkId);

    /**
     * 根据时间设置删除过期数据
     * @param missionWork
     */
    void deleteExpireDataByTime(Integer missionWork);

    MissionWorkActionParameter selectByMissionWorkParam(String missionWorkActionId, String parameterKey);

}
