package com.youibot.agv.scheduler.service;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.dto.MissionWorkParam;
import com.youibot.agv.scheduler.dto.MissionWorkParams;
import com.youibot.agv.scheduler.entity.AGVStatistics;
import com.youibot.agv.scheduler.entity.MissionWork;

import java.util.List;

public interface MissionWorkService extends BaseService<MissionWork> {

    int updateStatus(MissionWork missionWork) throws InterruptedException;

    void updateStatus(MissionWork missionWork, String status) throws InterruptedException;

    List<MissionWork> selectUnCompleteBySchedulePlanId(String schedulePlanId);

    List<MissionWork> selectByStatusList(List<String> status);

    List<MissionWork> selectByStatus(String status);

    /**
     * 获取所有未执行完成的任务（包括已创建的任务）
     * @return
     */
    List<MissionWork> selectUnComplete();

    /**
     * 获取已经分配执行但未完成的任务（不包括已创建的任务）
     * @return
     */
    MissionWork selectExecutionIncomplete(String agvId);

    MissionWork createByMissionWorkParam(MissionWorkParam missionWorkParam);

    MissionWork createByMissionIdAndChainId(String missionId, String missionWorkChainId);

    /**
     * 根据任务链ID查询所有未完成的MissionWork
     *
     * @param missionWorkChainId
     * @return
     */
    List<MissionWork> selectUnCompleteByWorkChainId(String missionWorkChainId);

    /**
     * 根据时间设置删除过期数据
     * @param missionWork
     */
    void deleteExpireDataByTime(Integer missionWork);

    /**
     * 获取当前日期前一天结束的任务工作
     * @return
     * @param yesterday
     * @param currentDay
     */
    AGVStatistics getAgvStatistics(long yesterday, long currentDay,String agvId);

    /**
     * 停止所有未完成（状态非成功和停止）的工作
     */
    void shutdownByUnComplete() throws InterruptedException;

    List<MissionWork> selectByMissionWorkChainId(String missionWorkChainId);

    /**
     * 根据missionCode 创建任务
     * @param missionCode
     * @param missionCallId
     * @return
     */
    MissionWork createMissionWorkByCode(String missionCode, String missionCallId);

    MissionWork getMissionWorkByMissionCallId(String missionCallId);

    MissionWork selectLastMissionWorkByCreatedBy(String createdBy,String agvCode);

    List<MissionWork> selectMissionWorkByStatus(List<String> status,String agvId);

    PageInfo<MissionWork> selectPage(MissionWorkParams missionWorkParams);

    List<MissionWork> selectUnCompleteWork();


}
