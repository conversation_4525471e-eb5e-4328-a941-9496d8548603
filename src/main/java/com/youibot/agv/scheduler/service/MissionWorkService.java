package com.youibot.agv.scheduler.service;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.constant.vo.MissionStatisticVO;
import com.youibot.agv.scheduler.entity.AGVStatistics;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.TriggerSelector;
import com.youibot.agv.scheduler.param.MissionWorkCreateCheck;
import com.youibot.agv.scheduler.param.MissionWorkParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface MissionWorkService extends BaseService<MissionWork> {

    List<MissionWork> selectBySequenceAndStatus(String status);

    /**
     * 根据状态获取mission。并且只获取需要的数量。
     *
     * @param status 状态。
     * @param size   数量。
     * @return
     */
    List<MissionWork> selectByStatus(String status, Integer size);

    int updateStatus(MissionWork missionWork, boolean isConsume, boolean event);

    void updateStatus(MissionWork missionWork, String status);

    List<MissionWork> selectUnCompleteBySchedulePlanId(String schedulePlanId);

    List<MissionWork> selectByStatusList(List<String> status, String missionGroupId);

    /**
     * 根据触发器id获取作业
     * @param triggerSelectorId
     * @return
     */
    MissionWork getMissionWorkByTriggerSelectorId(String triggerSelectorId);

    /**
     * 根据agvCode查询未完成的missionWork
     *
     * @param agvCode
     * @return
     */
    MissionWork selectUnCompleteByAgvId(String agvCode);

    MissionWork createByMissionWorkParam(MissionWorkParam missionWorkParam);

    MissionWorkCreateCheck createCheckByMissionWorkParam(MissionWorkParam missionWorkParam);

    /**
     * 根据时间设置删除过期数据
     *
     * @param missionWork
     */
    void deleteExpireDataByTime(Integer missionWork);

    /**
     * 获取当前日期前一天结束的作业
     *
     * @param agvCode
     * @param yesterDay
     * @param currentDay
     * @return
     */
    AGVStatistics getAgvStatistics(String agvCode, long yesterDay, long currentDay);

    /**
     * 查询未执行完成的任务
     *
     * @return
     */
    List<MissionWork> selectUnCompleteMissionWorks();

    /**
     * 根据agvCode查询执行中的任务
     *
     * @param agvCode
     * @return
     */
    MissionWork selectMissionWorkByAgvCodeAndWorkingStatus(String agvCode);

    List<MissionWork> selectMissionWorkByAgvCodeAndWorkingStatus(String agvCode, String status);

    /**
     * 根据missionCode 创建任务
     *
     * @param missionCode
     * @param value
     * @return
     */
    MissionWork createMissionWorkByCode(String missionCode, String value);

    /**
     * 分页查询
     *
     * @param searchMap
     * @return
     */
    List<MissionWork> selectByStatusListPage(Map<String, String> searchMap);


    Marker getFirstMarker(MissionWork missionWork);


    /**
     * 根据触发器创建作业
     * @param triggerSelector
     */
    MissionWork createByTrigger(TriggerSelector triggerSelector);

    Marker getLastMarker(MissionWork missionWork);

    /**
     *查询出所有未执行的及未分配的作业。
     */
    List<MissionWork> selectByUnAllocation(Integer size);

    MissionWork selectMissionWorkById(String Id);

    /**
     * 作业记录分页查询
     * @param searchMap
     * @return
     */
    PageInfo<MissionWork> findPage(Map<String,String> searchMap);

    /**
     * 导出任务记录
     * @param searchMap
     */
    void exportRecord(Map<String, String> searchMap, HttpServletResponse response);

    /**
     * 根据条件查询作业记录
     */
    List<MissionWork> selectRecord(Map<String,String> searchMap);

    /**
     * 查询某个时间范围内，任务统计信息
     *
     * 如果小车编码agvCode存在，就查询单个小车的
     * 否则，就查询所有小车的
     */
    List<MissionStatisticVO> getMissionWorkStatistic(String agvCode, String startTime, String endTime);
    
    
	/**
	  * quickRun( 创建Mission即指定状态:适用于直接分配车辆,且车辆可以立即执行任务的情况)
	  * @Title: quickRun
	  * @Description: TODO
	  * @param @param missionWorkParam
	  * @param @param status
	  * @param @return    设定文件
	  * @return MissionWork    返回类型
	  * @throws
	  */
	MissionWork quickRun(MissionWorkParam missionWorkParam, String status);

	/**
	 * 停止所有为完成的任务
	 */
	void stopUnCompleteMissionWorks();

	void deleteMissionWork(String missionWorkId);


    boolean continueMissionWork(String id);

}
