package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.ForeignAction;
import com.youibot.agv.scheduler.mqtt.bean.scribe.foreignAction.ForeignActionResultMessage;
import com.youibot.agv.scheduler.param.ForeignActionRequestParam;

public interface ForeignActionService extends BaseService<ForeignAction> {
    boolean shouldPlay(String agvCode);

    String insertForeignAction(ForeignActionRequestParam foreignActionRequestParam);

    void verifyParamAndAGVStatus(ForeignActionRequestParam foreignActionRequestParam);

    void handleForeignActionResult(ForeignActionResultMessage foreignActionResultMessage);

    void broadcastForeignAction(String agvCode);
}
