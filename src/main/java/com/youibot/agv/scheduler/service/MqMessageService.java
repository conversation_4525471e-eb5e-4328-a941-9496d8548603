package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.MqMessage;

import java.util.List;

/**
 * mq消息
 * @Author：yangpeilin
 * @Date: 2020/10/13 15:13
 */
public interface MqMessageService extends BaseService<MqMessage>{

    /**
     * 查询未消费的100消息列表
     * @return
     */
    List<MqMessage> listUnConsumeMessage();

    /**
     * 普通消息处理
     * @param mqMessage
     */
    void handle(MqMessage mqMessage);

    /**
     * 机器人状态和位置消息处理
     * @param mqMessage
     */
    void handleVehicleStaus(MqMessage mqMessage);

    /**
     * 根据系统设置删除过期emq信息
     * @param notify
     */
    void deleteExpireDataByTime(Integer notify);

    List<String> selectExpireDataByTime(Integer mqMessage);
}
