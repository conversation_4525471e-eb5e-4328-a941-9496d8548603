package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.RequestPathCallback;

import java.util.List;

public interface RequestPathCallbackService extends BaseService<RequestPathCallback> {

    List<Path> selectRequestPathCallback(String missionWorkActionId);

    RequestPathCallback selectNewestByRequestId(String requestPathId);

    List<RequestPathCallback> selectByRequestId(String requestPathId);

    void deleteRequestPathData(Integer day);

}
