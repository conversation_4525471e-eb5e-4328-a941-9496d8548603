package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.SystemConfig;
import com.youibot.agv.scheduler.mapper.SystemConfigMapper;
import com.youibot.agv.scheduler.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Author：yangpeilin
 * @Date: 2020/5/9 16:44
 */
@Service
public class SystemConfigServiceImpl extends BaseServiceImpl<SystemConfig> implements SystemConfigService {

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Override
    public Date getDataBaseCurrentTime() {
        return systemConfigMapper.getDataBaseCurrentTime();
    }
}
