package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.MissionAction;

import java.util.List;

public interface MissionActionService extends BaseService<MissionAction> {

    List<MissionAction> selectByMissionId(String missionId);

    List<MissionAction> selectByMissionIdBatch(List<String> missionIds);

    List<MissionAction> selectByMissionIdAndIsNotSubAction(String missionId);

    List<MissionAction> selectByMissionIdAndActionType(String missionId, String actionType);

    @Override
    Integer insert(MissionAction missionAction);

    @Override
    Integer update(MissionAction missionAction);

    /**
     * 根据任务ID查询为第一层节点和无后节点的动作
     * @param missionId
     * @return
     */
    MissionAction selectNoPostActionByMissionId(String missionId);

    void save(MissionAction missionAction);

    void deleteAction(String id);

    List<MissionAction> selectByParentActionId(String parentId);
}
