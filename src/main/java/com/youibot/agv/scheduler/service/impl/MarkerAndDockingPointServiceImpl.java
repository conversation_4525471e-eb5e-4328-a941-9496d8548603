package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.DockingPoint;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.MarkerAndDockingPoint;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.DockingPointService;
import com.youibot.agv.scheduler.service.MarkerAndDockingPointService;
import com.youibot.agv.scheduler.service.MarkerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

import static com.youibot.agv.scheduler.constant.MapConstant.*;

@Service
public class MarkerAndDockingPointServiceImpl implements MarkerAndDockingPointService {

    @Autowired
    private MarkerService markerService;

    @Autowired
    private DockingPointService dockingPointService;

//    @Override
//    public Integer insert(MarkerAndDockingPoint markerAndDockingPoint) {
//        check(markerAndDockingPoint);
//        return super.insert(markerAndDockingPoint);
//    }
//
//    @Override
//    public Integer update(MarkerAndDockingPoint markerAndDockingPoint) {
//        check(markerAndDockingPoint);
//        return super.update(markerAndDockingPoint);
//    }

//    /**
//     * 添加以及修改校验：
//     * 1.属性判空
//     * 2.marker与dockingPoint的关系目前是限定1对多（以后可能解除限定，改为多对多）
//     * 3.v型特征对接点与反光条特征对接点只能绑定工作标记点
//     * 4.充电桩对接点只能绑定充电标记点
//     *
//     * @param markerAndDockingPoint
//     * @return
//     */
//    private void check(MarkerAndDockingPoint markerAndDockingPoint) {
//        //1.属性判空校验
//        String markerId = markerAndDockingPoint.getMarkerId();
//        String dockingPointId = markerAndDockingPoint.getDockingPointId();
//        if (StringUtils.isEmpty(markerId) || StringUtils.isEmpty(dockingPointId) || StringUtils.isEmpty(markerAndDockingPoint.getAgvMapId())) {
//            throw new ADSParameterException("markerId or dockingPointId or agvMapId is null");
//        }
//        Marker marker = markerService.selectById(markerAndDockingPoint.getAgvMapId(),markerId,true);
//        DockingPoint dockingPoint = dockingPointService.selectById(markerAndDockingPoint.getAgvMapId(),dockingPointId,true);
//        if (marker == null || dockingPoint == null) {
//            throw new ADSParameterException("marker or dockingPoint is null");
//        }
//        //2.marker与dockingPoint的关系目前是限定1对多（以后可能解除限定，改为多对多）校验
//        List<MarkerAndDockingPoint> markerAndDockingPoints = this.selectByDockingPointId(dockingPointId);
//        if (!CollectionUtils.isEmpty(markerAndDockingPoints) && !markerAndDockingPoints.get(0).getId().equals(markerAndDockingPoint.getId())) {
//            throw new ExecuteException("一个对接点只能绑定一个标记点");
//        }
//        //3.v型特征对接点与反光条特征对接点只能绑定工作标记点
//        //4.充电桩对接点只能绑定充电标记点
//        if (DOCKING_POINT_TYPE_REFLECTOR.equals(dockingPoint.getType()) || DOCKING_POINT_TYPE_V.equals(dockingPoint.getType())) {
//            if (!MARKER_TYPE_WORK.equals(marker.getType())) {
//                throw new ExecuteException("v型特征对接点与反光条特征对接点只能绑定工作标记点");
//            }
//        } else if (DOCKING_POINT_TYPE_CHARGE_STATION.equals(dockingPoint.getType())) {
//            if (!MARKER_TYPE_CHARGING.equals(marker.getType())) {
//                throw new ExecuteException("充电桩对接点只能绑定充电标记点");
//            }
//        } else {
//            throw new ExecuteException("对接点类型错误");
//        }
//    }
//
//    @Override
//    public List<MarkerAndDockingPoint> selectByAGVMapId(String agvMapId) {
//        Example example = new Example(MarkerAndDockingPoint.class);
//        example.createCriteria().andEqualTo("agvMapId", agvMapId);
//        return super.selectByExample(example);
//    }
//
//    @Override
//    public List<MarkerAndDockingPoint> selectByAGVMapIds(List<String> agvMapIds) {
//        Example example = new Example(MarkerAndDockingPoint.class);
//        example.createCriteria().andIn("agvMapId", agvMapIds);
//        return super.selectByExample(example);
//    }
//
//    @Override
//    public List<MarkerAndDockingPoint> selectByMarkerId(String markerId) {
//        Example example = new Example(MarkerAndDockingPoint.class);
//        example.createCriteria().andEqualTo("markerId", markerId);
//        return super.selectByExample(example);
//    }
//
//    @Override
//    public List<MarkerAndDockingPoint> selectByDockingPointId(String dockingPointId) {
//        Example example = new Example(MarkerAndDockingPoint.class);
//        example.createCriteria().andEqualTo("dockingPointId", dockingPointId);
//        return super.selectByExample(example);
//    }
}
