package com.youibot.agv.scheduler.service.impl;

import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.entity.WorkCycleConfig;
import com.youibot.agv.scheduler.service.WorkCycleConfigService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;


/**
  * @ClassName: WorkCycleConfigServiceImpl
  * @Description: 台积电项目环状线配置服务实现类
  * <AUTHOR>
  * @date 2021年12月27日 上午11:24:28
  *
  */
@Slf4j
@Service
public class WorkCycleConfigServiceImpl extends BaseServiceImpl<WorkCycleConfig> implements WorkCycleConfigService {


 
   

    @PostConstruct
    public void init() {
    	
    	  tjdCxtInit();
		
    }




	/**
	 *  台积电环状线初始化
	 */
	private void tjdCxtInit() {
		Example example = new Example(WorkCycleConfig.class);
		/**
		 * 不做实际的禁用,用以维护环的顺序
		 */
//		example.createCriteria().andEqualTo("enabled", true);
		example.setOrderByClause("seq asc");
		List<WorkCycleConfig> list = super.selectByExample(example);
		
      list.stream().forEach(item -> {
			
	        TjdCxt.addPointtToCycle(item.getId() , item) ;
		});
      
      log.debug("init_tjd_cycle_success");
	}
	




	@Override
	public void refresh() {
		
		this.refreshCycleConfig();
	}



	@Override
	public void refreshCycleConfig() {
		 
		    tjdCxtInit();
		    TjdCxt.MARKCODE_NEAREST_STATION.clear();
		    TjdCxt.UTURN_CACHE.clear();
		
	}
}
