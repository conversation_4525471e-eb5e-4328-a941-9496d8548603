package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.DataCodeMatrix;
import com.youibot.agv.scheduler.map.entity.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:49 2019/12/27
 * @Description :
 * @Modified By :
 * @Version :
 */
public interface DataCodeMatrixService {

    /**
     * 通过mapId获取当前地图中所有的DataCodeMatrix二维码
     * @param agvMapId
     * @return
     */
    List<DataCodeMatrix> selectByAGVMapId(String agvMapId,boolean isDraft);

    List<DataCodeMatrix> selectByAGVMapIds(List<String> agvMapIds,boolean isDraft);

    Map<String, List<DataCodeMatrix>> mapByAgvMapId(List<String> agvMapList,boolean isDraft);

    Integer deleteByAgvMapIds(List<String> agvMapIdList);


    PageInfo<DataCodeMatrix> findPage(Map<String, String> searchMap,boolean isDraft);

    List<DataCodeMatrix> searchAll(Map<String, String> searchMap,boolean isDraft);

    void insert(String mapName,DataCodeMatrix dataCodeMatrix);

    DataCodeMatrix selectById(String mapName,String id,boolean isDraft);

    void update(String mapName,DataCodeMatrix dataCodeMatrix);

    void deleteById(String mapName,String id);
}