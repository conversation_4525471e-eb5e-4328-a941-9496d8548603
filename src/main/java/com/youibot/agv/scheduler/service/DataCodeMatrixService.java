package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.DataCodeMatrix;

import java.util.List;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:49 2019/12/27
 * @Description :
 * @Modified By :
 * @Version :
 */
public interface DataCodeMatrixService extends BaseService<DataCodeMatrix>{

    /**
     * 通过名字和agvMapId获取DataCodeMatrix二维码
     * @param name
     * @param agvMapId
     * @return
     */
    DataCodeMatrix selectDataCodeMatrixByName(String name, String agvMapId);

    /**
     * 通过DataCodeMatrixId获取名字
     * @param id
     * @return
     */
    String selectNameById(String id);

    /**
     * 通过mapId获取当前地图中所有的DataCodeMatrix二维码
     * @param agvMapId
     * @return
     */
    List<DataCodeMatrix> selectByAGVMapId(String agvMapId);

    void deleteByAGVMapId(String agvMapId,String agvId);
}