package com.youibot.agv.scheduler.service.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.constant.CommonConstant;
import com.youibot.agv.scheduler.constant.enums.TriggerUnitEnum;
import com.youibot.agv.scheduler.constant.vo.TriggerSelectorDetailVO;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.TriggerSelector;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mapper.TriggerSelectorMapper;
import com.youibot.agv.scheduler.scheduleplan.SchedulePlanUtil;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.TriggerSelectorService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/1/7 17:52
 * @Description:
 */
@Slf4j
@Service
public class TriggerSelectorServiceImpl extends BaseServiceImpl<TriggerSelector> implements TriggerSelectorService {

    /**
     * 定时触发
     **/
    private static final Integer TIMER_TYPE = 1;

    /**
     * 呼叫器触发
     **/
    private static final Integer PAGER_TYPE = 2;

    /**
     * 传感器触发
     **/
    private static final Integer SENSOR_TYPE = 3;
    
    /**
     * 本地定时任务
     */
    public  static final Integer TIMER_TYPE_LOCAL = 4;

    /**
     * 立即触发
     **/
    private static final Integer NOW = 1;

    /**
     * 定时触发
     **/
    private static final Integer TIMING = 2;

    @Autowired
    private SchedulePlanUtil schedulePlanUtil;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private TriggerSelectorMapper triggerSelectorMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(TriggerSelector triggerSelector) {
        //step1.参数校验
        this.checkParams(triggerSelector);
        String code = triggerSelector.getCode();
        String name = triggerSelector.getName();
        this.checkCodeAndNameIsRepeat(code, name);
        //如果是传感器触发,直接保存,当传感器状态发生变化时,触发任务
        if (SENSOR_TYPE.equals(triggerSelector.getType())) {
            triggerSelector.setId(UUID.randomUUID().toString());
            triggerSelector.setCompletedTimes(0);
            super.insert(triggerSelector);
            return triggerSelector.getId();
        }

        //step2.初始化触发器
        //获取触发间隔,单位s
        if (TIMER_TYPE.equals(triggerSelector.getType())|| TIMER_TYPE_LOCAL.equals(triggerSelector.getType()) ) {
            long periodOfSends = TriggerUnitEnum.toSeconds(triggerSelector.getPeriod(), triggerSelector.getUnit());
            if (periodOfSends > Integer.MAX_VALUE) {
                throw new YOUIFleetException(MessageUtils.getMessage("trigger_period_range_out"));
            }

            triggerSelector.setSecondsPeriod((int) periodOfSends);
        }
        triggerSelector.setId(UUID.randomUUID().toString());
        triggerSelector.setCompletedTimes(0);
        super.insert(triggerSelector);

        //step3加入到任务器中
        this.postProcess(triggerSelector);

        return triggerSelector.getId();
    }

    private void postProcess(TriggerSelector triggerSelector) {
        if (CommonConstant.ENABLED.equals(triggerSelector.getIsDisabled())) {
        	 boolean isLocal = TIMER_TYPE_LOCAL.equals(triggerSelector.getType());
            if (TIMER_TYPE.equals(triggerSelector.getType()) || isLocal) {
                //定时触发
                try {
                	if(isLocal) {
                		Object beanJob = ApplicationUtils.getBean(triggerSelector.getMissionId());
                		if(Objects.isNull(beanJob)) {
                			 log.warn("触发器加入到任务器中失败: 本地定时任务调用 调用目标不存在");
                             //回滚事务
                             throw new YOUIFleetException("本地定时任务调用 调用目标不存在");
                		}
                	}
                    schedulePlanUtil.addPlan(triggerSelector);
                } catch (Exception e) {
                    log.warn("触发器加入到任务器中失败:{}", e.getMessage());
                    //回滚事务
                    throw new YOUIFleetException(MessageUtils.getMessage("service.server_error"));
                }
            }
        }
    }

    @Override
    public void updateTriggerSelector(String id, TriggerSelector triggerSelector) {
        TriggerSelector triggerSelectorDB = super.selectById(id);
        if (triggerSelectorDB == null) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.missing_parameter"));
        }

        if (!triggerSelector.getType().equals(triggerSelectorDB.getType())) {
            throw new YOUIFleetException(MessageUtils.getMessage("trigger_type_can_not_modify"));
        }

        this.checkPagerRepeat(triggerSelector);


        if (TIMER_TYPE.equals(triggerSelectorDB.getType()) ||  TIMER_TYPE_LOCAL.equals(triggerSelector.getType())) {
            boolean isChange = this.isChange(triggerSelector, triggerSelectorDB);
            //如果变更了开始时间、触发间隔,业务重新处理
            if (isChange) {
                this.checkParams(triggerSelector);
                long periodOfSends = TriggerUnitEnum.toSeconds(triggerSelector.getPeriod(), triggerSelector.getUnit());
                if (periodOfSends > Integer.MAX_VALUE) {
                    throw new YOUIFleetException(MessageUtils.getMessage("trigger_period_range_out"));
                }

                triggerSelector.setSecondsPeriod((int) periodOfSends);
                //先移除Job
                schedulePlanUtil.deleteJob(id);
                //重新添加新的Job
                try {
                    schedulePlanUtil.addPlan(triggerSelector);
                } catch (SchedulerException e) {
                    log.warn("重新添加新Job失败:{}", e.getMessage());
                }
            }
            //如果是修改了启用/禁用
            if (!triggerSelectorDB.getIsDisabled().equals(triggerSelector.getIsDisabled())) {
                //修改为了禁用
                if (CommonConstant.DISABLED.equals(triggerSelector.getIsDisabled())) {
                    //暂停该Job
                    schedulePlanUtil.pauseJob(id);
                }
                //如果是修改为了启用
                if (CommonConstant.ENABLED.equals(triggerSelector.getIsDisabled())) {
                    if (!schedulePlanUtil.checkExists(id)) {
                        //重新添加新的Job
                        try {
                            if (1 == triggerSelector.getStartType()) {
                                triggerSelector.setStartTime(new Date());
                                triggerSelector.setSecondsPeriod((int) TriggerUnitEnum.toSeconds(triggerSelector.getPeriod(), triggerSelector.getUnit()));
                            }
                            schedulePlanUtil.addPlan(triggerSelector);
                        } catch (SchedulerException e) {
                            log.warn("重新添加新Job失败:{}", e);
                        }
                    } else {
                        schedulePlanUtil.resumeJob(id);
                    }
                }
            }
        }
        super.updateByPrimaryKeySelective(triggerSelector);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteTrigger(String id) {
        TriggerSelector triggerSelectorDB = super.selectById(id);
        if (triggerSelectorDB == null) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.missing_parameter"));
        }
        this.deleteById(id);

        if (TIMER_TYPE.equals(triggerSelectorDB.getType())) {
            schedulePlanUtil.deleteJob(id);
        }
    }

    @Override
    public TriggerSelectorDetailVO findMissionWorkById(String id, Map<String, String> searchMap) {
        TriggerSelectorDetailVO result = new TriggerSelectorDetailVO();
        TriggerSelector triggerSelector = super.selectById(id);
        if (triggerSelector == null) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.missing_parameter"));
        }
        //查询missionWorks
        searchMap.put("triggerSelectorId", id);
        PageInfo<MissionWork> page = missionWorkService.findPage(searchMap);

        //统计
        Example example = new Example(MissionWork.class);
        example.createCriteria().andEqualTo("triggerSelectorId", id)
                .andIn("status", Lists.newArrayList("SUCCESS", "SHUTDOWN"));
        Integer completed = missionWorkService.selectCountByExample(example);

        example.clear();
        example.createCriteria().andEqualTo("triggerSelectorId", id)
                .andEqualTo("status", "RUNNING");
        Integer running = missionWorkService.selectCountByExample(example);

        example.clear();
        example.createCriteria().andEqualTo("triggerSelectorId", id)
                .andEqualTo("status", "SHUTDOWN");
        Integer cancel = missionWorkService.selectCountByExample(example);

        BeanUtils.copyPropertiesIgnoreNull(page, result);
        result.setAlreadyTrigger(triggerSelector.getCompletedTimes());
        result.setRunning(running);
        result.setCompleted(completed);
        result.setCancel(cancel);
        return result;

    }

    @Override
    public List<TriggerSelector> selectByDeviceAddress(Integer deviceAddress) {
        Example example = new Example(TriggerSelector.class);
        example.createCriteria().andEqualTo("deviceAddress", deviceAddress);
        return super.selectByExample(example);
    }

    @Override
    public List<TriggerSelector> selectByPagerId(String pagerId) {
        Example example = new Example(TriggerSelector.class);
        example.createCriteria()
                .andEqualTo("pagerId", pagerId);

        return super.selectByExample(example);
    }

    @Override
    public List<TriggerSelector> selectByTriggerType(int type) {
        Example example = new Example(TriggerSelector.class);
        example.createCriteria().andEqualTo("type", 2);
        return this.selectByExample(example);
    }

    @Override
    public List<TriggerSelector> selectByTriggerTypeAndDeviceAddress(int type, Integer deviceAddress) {
        if (Objects.isNull(deviceAddress) || type < 1 || type > 2) {
            return null;
        }
        return triggerSelectorMapper.selectTriggerSelectorsByTypeAndDeviceAddress(type, deviceAddress);
    }

    private boolean isChange(TriggerSelector triggerSelector, TriggerSelector triggerSelectorDB) {
        boolean flag = false;

        // 修改了开始类型
        if (!triggerSelectorDB.getStartType().equals(triggerSelector.getStartType())) {
            flag = true;
        }
        // 修改了开始间隔单位
        if (!triggerSelectorDB.getUnit().equals(triggerSelector.getUnit())) {
            flag = true;
        }

        // 修改触发间隔
        if (!triggerSelector.getPeriod().equals(triggerSelectorDB.getPeriod())) {
            flag = true;
        }

        // 修改了触发次数
        if (!triggerSelector.getExecuteTimes().equals(triggerSelectorDB.getExecuteTimes())) {
            flag = true;
        }

        if (CommonConstant.DISABLED.equals(triggerSelector.getIsDisabled())) {
            flag = false;
        }

        return flag;
    }

    private void checkParams(TriggerSelector triggerSelector) {

        if (NOW.equals(triggerSelector.getStartType())) {
            triggerSelector.setStartTime(new Date());
        }
        if (TIMING.equals(triggerSelector.getStartType())) {
            Date startTime = triggerSelector.getStartTime();
            if (startTime == null || startTime.compareTo(new Date()) < 0) {
                throw new YOUIFleetException(MessageUtils.getMessage("trigger_selector_time_error"));
            }
        }
        this.checkPagerRepeat(triggerSelector);

    }

    private void checkPagerRepeat(TriggerSelector triggerSelector) {
        //如果是呼叫器触发,一个呼叫器地址+按钮只能绑定一次任务
        if (PAGER_TYPE.equals(triggerSelector.getType())) {
            Example example = new Example(TriggerSelector.class);
            Example.Criteria criteria = example.createCriteria();

            if (Objects.isNull(triggerSelector.getDeviceAddress())) {
                criteria.andIsNull("deviceAddress");
            } else {
                criteria.andEqualTo("deviceAddress", triggerSelector.getDeviceAddress());
            }

            if (Objects.isNull(triggerSelector.getButtonAddress())) {
                criteria.andIsNull("buttonAddress");
            } else {
                criteria.andEqualTo("buttonAddress", triggerSelector.getButtonAddress());
            }

            criteria.andEqualTo("type", PAGER_TYPE);
            TriggerSelector triggerSelectorDB = this.selectOneByExample(example);
            if (triggerSelectorDB != null && !triggerSelectorDB.getId().equals(triggerSelector.getId())) {
                throw new YOUIFleetException(MessageUtils.getMessage("device_address_already_bind_mission"));
            }
        }
    }

    private void checkCodeAndNameIsRepeat(String code, String name) {
        Example example = new Example(TriggerSelector.class);
        example.createCriteria()
                .andEqualTo("code", code);
        List<TriggerSelector> triggerSelectors = super.selectByExample(example);
        if (!CollectionUtils.isEmpty(triggerSelectors)) {
            throw new YOUIFleetException("触发器编码已存在!");
        }
        example.clear();
        example.createCriteria()
                .andEqualTo("name", name);
        triggerSelectors = super.selectByExample(example);
        if (!CollectionUtils.isEmpty(triggerSelectors)) {
            throw new YOUIFleetException("触发器名称已存在!");
        }

    }
}
