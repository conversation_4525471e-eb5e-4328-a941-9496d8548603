package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.vehicle.Vehicle;


public interface MissionService extends BaseService<Mission> {

    /**
     * 更新版本
     * @param missionId
     */
    void updateMissionVersion(String missionId);

    /**
     * 作废
     * @param id
     */
    void invalid(String id);

    /**
     * 根据MissionCode查询
     * @param missionCode
     * @return
     */
    Mission selectByCode(String missionCode);

    /**
     * @Title: getTjdDefaultMission
     * @Description:  获得台积电电视默认的任务创建模板
     * @return Mission    返回类型
     * @throws
     */
	Mission getTjdDefaultMission();

	/** 通过agv来查询当前agv 可执行的任务
	 * 只要通过agvCode , 或者 agvType  或 agvGruop 任一 能够查询到 即可
	 * @param vehicle
	 * @return
	 */
	Mission selectByAgv(Vehicle vehicle);

}
