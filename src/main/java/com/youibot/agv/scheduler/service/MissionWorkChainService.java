package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.dto.MissionWorkChainParam;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkChain;

import java.util.List;

public interface MissionWorkChainService extends BaseService<MissionWorkChain>{

    MissionWorkChain createByMissionWorkChainParam(MissionWorkChainParam missionWorkChainParam);

    List<MissionWorkChain> selectByStatus(String status);

    MissionWorkChain selectOneByStatus(String status);

    void stopMissionWorkChain(String id) throws InterruptedException;

    List<MissionWorkChain> selectByStatusList(List<String> status);
}
