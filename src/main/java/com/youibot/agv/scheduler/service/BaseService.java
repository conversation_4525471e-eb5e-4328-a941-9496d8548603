package com.youibot.agv.scheduler.service;

import com.github.pagehelper.PageInfo;
import org.apache.ibatis.session.RowBounds;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;

public interface BaseService<T> {

	T selectById(String id);

	List<T> selectByIds(List<String> ids);

	T selectByEntity(T t);

	T selectOneByExample(Example example);

	List<T> selectByExample(Example example);

	Integer selectCountByExample(Example example);

	Integer insert(T t);

	Integer batchInsert(List<T> list);

	Integer update(T t);

	Integer updateByPrimaryKeySelective(T t);

	Integer batchUpdate(List<T> list);

	Integer deleteById(String id);

	Integer delete(T t);

	Integer deleteByIds(List<String> ids);

	Integer deleteByExample(Example example);

	List<T> findAll();

	List<T> searchAll(Map<String, String> searchMap);

	PageInfo<T> findPage(Map<String, String> searchMap);

	List<T> findByEntity(T t);

	List<T> selectByExampleAndRowBounds(Example example, RowBounds rowBounds);
}
