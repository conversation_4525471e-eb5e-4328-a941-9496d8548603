package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.service.DockingPointService;
import com.youibot.agv.scheduler.service.MarkerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DockingPointServiceImpl implements DockingPointService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DockingPointServiceImpl.class);

    @Autowired
    private MarkerService markerService;


//
//    public String getDraftFileInfo(FTPClient ftpClient, String srcFilePath, String destFilePath) throws IOException {
//
//        //查询ftp对应地图的草稿目录的文件 /home/<USER>/youibot_map/mapName/path/draft/mapName.qr
//        ByteOutputStream outputStream = new ByteOutputStream();
//        boolean retrieveFile = ftpClient.retrieveFile(destFilePath, outputStream);
//
//        //不存在的话，就复制、创建该文件
//        if(!retrieveFile){
//            MapFileUtils.copyFolder(srcFilePath,destFilePath,ftpClient);
//        }
//
//        //如果仍然不存在的话，就创建一个文件
//        retrieveFile = ftpClient.retrieveFile(destFilePath, outputStream);
//        if(!retrieveFile){
//            //throw new RuntimeException("get ftp file error");
//            ftpClient.storeFile(destFilePath,new ByteInputStream());
//        }
//
//        byte[] bytes = outputStream.getBytes();
//        String str = new String(bytes);
//        return str;
//    }
//
//
//
//
//    @Override
//    public DockingPoint insert(DockingPoint dockingPoint) {
//
//        String mapName = dockingPoint.getAgvMapId();
//        String markerId = dockingPoint.getMarkerId();
//        if (StringUtils.isEmpty(mapName) || StringUtils.isEmpty(markerId)) {
//            throw new RuntimeException("params data is null");
//        }
//        if (StringUtils.isEmpty(dockingPoint.getId())) {
//            dockingPoint.setId(UUID.randomUUID().toString());
//        }
//        this.check(dockingPoint);
//
//        if (StringUtils.isEmpty(dockingPoint.getCode())) {
//            List<DockingPoint> dockingPoints = selectByAGVMapId(mapName, true);
//            Integer maxCode = 1;
//            if (!CollectionUtils.isEmpty(dockingPoints)) {
//                for (DockingPoint item : dockingPoints) {
//                    Integer tmpCode = Integer.valueOf(item.getCode());
//                    if (tmpCode > maxCode) {
//                        maxCode = tmpCode;
//                    }
//                }
//            }
//            dockingPoint.setCode(String.valueOf(maxCode));
//        }
//
//        String srcFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/current/" +mapName+ ".path";
//        String destFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/draft/" +mapName+ ".path";
//        //1、读取文件内容
//        FTPClient ftpClient = null;
//        try {
//            ftpClient = FtpUtils.getConnectClient();
//            String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
//
//            JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
//            JSONArray list = mapObj.getJSONArray("Marker");
//            if (list != null && list.size() > 0) {
//                for (int i = 0; i < list.size(); i++) {
//                    Marker tmp = list.getObject(i, Marker.class);
//                    if (markerId.equalsIgnoreCase(tmp.getId()) && (MARKER_TYPE_WORK.equalsIgnoreCase(tmp.getType())
//                            || MARKER_TYPE_CHARGING.equalsIgnoreCase(tmp.getType()))) {
//                        tmp.setDockingPoint(dockingPoint);
//                        list.add(i, JSONObject.parseObject(JSONObject.toJSONString(tmp)));
//                        break;
//                    }
//                }
//            }
//
//            //写入
//            mapObj.put("", list.toJSONString());
//
//            //3、存储到ftp文件
//            byte[] bytes = JSONObject.toJSONString(mapObj).getBytes();
//            ftpClient.storeFile(destFilePath, new ByteInputStream(bytes, bytes.length));
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }finally {
//            if(ftpClient!=null){
//                FtpUtils.disconnect(ftpClient);
//            }
//        }
//        return dockingPoint;
//    }
//
//    @Override
//    public DockingPoint selectById(String mapName, String id, boolean isDraft) {
//
//        List<Marker> markers = new ArrayList<>();
//        //查询内存
//        if (!isDraft) {
//            //1、从内存地图 数据中，获取
//            Map<String, Marker> markerMap = AGVMapInfoCache.getCache(mapName).getMarkers();
//            if(markerMap!=null && markerMap.size()>0){
//                markers = (List<Marker>) markerMap.values();
//            }
//        } else {
//
//            String srcFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/current/" + mapName + ".path";
//            String destFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/draft/" + mapName + ".path";
//            //1、读取文件内容
//            FTPClient ftpClient = null;
//            try {
//                ftpClient = FtpUtils.getConnectClient();
//                String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
//
//                JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
//                JSONArray list = mapObj.getJSONArray("Marker");
//                if (list != null && list.size() > 0) {
//                    for (int i = 0; i < list.size(); i++) {
//                        Marker tmp = list.getObject(i, Marker.class);
//                        markers.add(tmp);
//                    }
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            } finally {
//                if (ftpClient != null) {
//                    FtpUtils.disconnect(ftpClient);
//
//                }
//            }
//        }
//
//        List<Marker> markerList = new ArrayList<>();
//        if (markers != null && markers.size() > 0) {
//            for (Marker item : markers) {
//                if (MARKER_TYPE_WORK.equalsIgnoreCase(item.getType())
//                        || MARKER_TYPE_CHARGING.equalsIgnoreCase(item.getType())) {
//                    markerList.add(item);
//                }
//            }
//        }
//
//        List<DockingPoint> list = null;
//        if (!CollectionUtils.isEmpty(markerList)) {
//            list = markerList.stream().map(Marker::getDockingPoint).collect(Collectors.toList());
//        }
//
//        DockingPoint target = null;
//        if (!CollectionUtils.isEmpty(list)) {
//            for (DockingPoint item : list) {
//                if (item.getId().equalsIgnoreCase(id)) {
//                    target = item;
//                    break;
//                }
//            }
//        }
//        return target;
//    }
//
//    @Override
//    public DockingPoint update(DockingPoint dockingPoint) {
//
//        if(dockingPoint==null || StringUtils.isBlank(dockingPoint.getAgvMapId())){
//            throw new RuntimeException("param is error !");
//        }
//
//        this.check(dockingPoint);
//        String id = dockingPoint.getId();
//        DockingPoint dockingPointDB = this.selectById(dockingPoint.getAgvMapId(),id,true);
//        if (dockingPointDB == null) {
//            throw new ExecuteException(MessageUtils.getMessage("service.docking_point_is_null"));
//        }
//
//        String mapName = dockingPoint.getAgvMapId();
//        String srcFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/current/" +mapName+ ".path";
//        String destFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/draft/" +mapName+ ".path";
//        //1、读取文件内容
//        FTPClient ftpClient = null;
//        try {
//            ftpClient = FtpUtils.getConnectClient();
//            String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
//
//            JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
//            JSONArray list = mapObj.getJSONArray("Marker");
//            if (list != null && list.size() > 0) {
//                for (int i = 0; i < list.size(); i++) {
//                    Marker tmp = list.getObject(i, Marker.class);
//                    if ( tmp !=null && (MARKER_TYPE_WORK.equalsIgnoreCase(tmp.getType())
//                            || MARKER_TYPE_CHARGING.equalsIgnoreCase(tmp.getType()))) {
//                        if(tmp.getDockingPoint()!=null && tmp.getDockingPoint().getId().equals(dockingPoint.getId())){
//                            tmp.setDockingPoint(dockingPoint);
//                        }
//                        list.add(i, JSONObject.parseObject(JSONObject.toJSONString(tmp)));
//                    }
//                }
//            }
//
//            //写入
//            mapObj.put("Marker", list.toJSONString());
//
//            //3、存储到ftp文件
//            byte[] bytes = JSONObject.toJSONString(mapObj).getBytes();
//            ftpClient.storeFile(destFilePath, new ByteInputStream(bytes, bytes.length));
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }finally {
//            if(ftpClient!=null){
//                FtpUtils.disconnect(ftpClient);
//            }
//        }
//        return dockingPoint;
//    }
//
//
//    private void check(DockingPoint dockingPoint) {
//        //1.属性判空校验
//        String markerId = dockingPoint.getMarkerId();
//        if (StringUtils.isEmpty(markerId)) {
//            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
//        }
//        Marker marker = markerService.selectById(dockingPoint.getAgvMapId(),markerId,true);
//        if (marker == null) {
//            throw new ADSParameterException(MessageUtils.getMessage("service.marker_is_null"));
//        }
//        //2.目前marker与dockingPoint只能一对一绑定
//        DockingPoint dockingPointByMarker = this.selectByMarkerId(dockingPoint.getAgvMapId(),markerId,true);
//        if (dockingPointByMarker != null && !dockingPointByMarker.getId().equals(dockingPoint.getId())) {//如果目标markerId已经被其他dockingPoint绑定
//            throw new ExecuteException("markerId：" + markerId + " " + MessageUtils.getMessage("service.marker_already_binding_docking"));
//        }
//
//        if (DOCKING_POINT_TYPE_REFLECTOR.equals(dockingPoint.getType()) || DOCKING_POINT_TYPE_V.equals(dockingPoint.getType())) {
//            //3.v型特征对接点与反光条特征对接点只能绑定工作标记点
//            if (!MARKER_TYPE_WORK.equals(marker.getType()) && !MARKER_TYPE_CHARGING.equals(marker.getType())) {
//                throw new ExecuteException(MessageUtils.getMessage("service.docking_can_only_binding_work_charge"));
//            }
//        } else {
//            throw new ExecuteException(MessageUtils.getMessage("service.docking_point_type_error"));
//        }
//    }
//
//    @Override
//    public List<DockingPoint> selectByAGVMapId(String agvMapId,boolean isDraft) {
//
//        List<DockingPoint> list = null;
//        String mapName = agvMapId;
//        if (!isDraft) {
//            Map<String, Marker> markers = AGVMapInfoCache.getCache(agvMapId).getMarkers();
//            if(markers!=null && markers.size()>0){
//                list = markers.values().stream().map(Marker::getDockingPoint).collect(Collectors.toList());
//            }
//        } else {
//            String srcFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/current/" +mapName+ ".path";
//            String destFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/draft/" +mapName+ ".path";
//            //1、读取文件内容
//            FTPClient ftpClient = null;
//            try {
//                ftpClient = FtpUtils.getConnectClient();
//                String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
//                JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
//                JSONArray objList = mapObj.getJSONArray("Marker");
//                if (objList != null && objList.size() > 0) {
//                    for (int i = 0; i < list.size(); i++) {
//                        Marker tmp = objList.getObject(i, Marker.class);
//                        if(tmp != null){
//                            list.add(tmp.getDockingPoint());
//                        }
//                    }
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }finally {
//                if(ftpClient!=null){
//                    FtpUtils.disconnect(ftpClient);
//                }
//            }
//        }
//        return list;
//    }
//
//    @Override
//    public List<DockingPoint> selectByAGVMapIds(List<String> agvMapIds,boolean isDraft) {
//
//        if(CollectionUtils.isEmpty(agvMapIds)){
//            return Collections.EMPTY_LIST;
//        }
//
//        List<DockingPoint> list = new ArrayList<>();
//        agvMapIds.stream().forEach(agvMapid->{
//            list.addAll(selectByAGVMapId(agvMapid,isDraft));
//        });
//
//        return list;
//    }
//
//    @Override
//    public Map<String, List<DockingPoint>> mapByAgvMapId(List<String> agvMapList,boolean isDraft) {
//        return BeanUtils.listToMapByFiled(this.selectByAGVMapIds(agvMapList,isDraft), "agvMapId");
//    }
//
//
//    @Override
//    public DockingPoint selectByMarkerId(String agvMapName,String markerId, boolean isDraft) {
//
//        List<DockingPoint> list = null;
//        String mapName = agvMapName;
//
//        if (!isDraft) {
//            List<Marker> markers = new ArrayList<>();
//            if(StringUtils.isNotBlank(mapName)){
//                MapGraphInfo cache = AGVMapInfoCache.getCache(agvMapName);
//                if(cache!=null && cache.getMarkers()!=null){
//                    markers = (List<Marker>) cache.getMarkers().values();
//                }
//            }else {
//                Map<String, MapGraphInfo> instance = AGVMapInfoCache.getInstance();
//                if(instance!=null && instance.size()>0){
//                    for(MapGraphInfo item : instance.values()){
//                        Map<String, Marker> tmpMarkers = item.getMarkers();
//                        if(tmpMarkers!=null && tmpMarkers.size()>0){
//                            markers.addAll(tmpMarkers.values());
//                        }
//                    }
//                }
//            }
//            if(markers!=null && markers.size()>0){
//                list = markers.stream().filter(marker->marker.getId().equals(markerId)).map(Marker::getDockingPoint).collect(Collectors.toList());
//            }
//        } else {
//            String srcFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/current/" +mapName+ ".path";
//            String destFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/draft/" +mapName+ ".path";
//            //1、读取文件内容
//            FTPClient ftpClient = null;
//            try {
//                ftpClient = FtpUtils.getConnectClient();
//                String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
//                JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
//                JSONArray objList = mapObj.getJSONArray("Marker");
//                if (objList != null && objList.size() > 0) {
//                    for (int i = 0; i < list.size(); i++) {
//                        Marker tmp = objList.getObject(i, Marker.class);
//                        if(tmp != null && tmp.getId().equals(markerId)){
//                            list.add(tmp.getDockingPoint());
//                        }
//                    }
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }finally {
//                if(ftpClient!=null){
//                    FtpUtils.disconnect(ftpClient);
//                }
//            }
//        }
//        return list!=null ? list.get(0) : null;
//    }
//
//    @Override
//    public void deleteById(String agvMapId, String id) {
//        String mapName = agvMapId;
//        String srcFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/current/" +mapName+ ".path";
//        String destFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/draft/" +mapName+ ".path";
//        //1、读取文件内容
//        FTPClient ftpClient = null;
//        try {
//            ftpClient = FtpUtils.getConnectClient();
//            String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
//
//            JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
//            JSONArray list = mapObj.getJSONArray("Marker");
//            if (list != null && list.size() > 0) {
//                for (int i = 0; i < list.size(); i++) {
//                    Marker tmp = list.getObject(i, Marker.class);
//                    if ( tmp !=null && (MARKER_TYPE_WORK.equalsIgnoreCase(tmp.getType())
//                            || MARKER_TYPE_CHARGING.equalsIgnoreCase(tmp.getType()))) {
//                        if(tmp.getDockingPoint()!=null && tmp.getDockingPoint().getId().equals(id)){
//                            tmp.setDockingPoint(null);
//                        }
//                        list.add(i, JSONObject.parseObject(JSONObject.toJSONString(tmp)));
//                    }
//                }
//            }
//
//            //写入
//            mapObj.put("Marker", list.toJSONString());
//
//            //3、存储到ftp文件
//            byte[] bytes = JSONObject.toJSONString(mapObj).getBytes();
//            ftpClient.storeFile(destFilePath, new ByteInputStream(bytes, bytes.length));
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }finally {
//            if(ftpClient!=null){
//                FtpUtils.disconnect(ftpClient);
//            }
//        }
//    }
//
//    @Override
//    public Integer deleteByAgvMapIds(List<String> agvMapIdList) {
//        if(CollectionUtils.isEmpty(agvMapIdList)){
//            return 0;
//        }
//
//        agvMapIdList.stream().forEach(agvMapid->{
//            deleteByAgvMapId(agvMapid);
//        });
//
//        return agvMapIdList.size();
//    }
//
//    public void deleteByAgvMapId(String agvMapId) {
//
//        if(StringUtils.isBlank(agvMapId)){
//            throw new RuntimeException("param is null!");
//        }
//
//        String mapName = agvMapId;
//        String srcFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/current/" +mapName+ ".path";
//        String destFilePath = "/home/<USER>/youibot_map/" + mapName + "/path/draft/" +mapName+ ".path";
//        //1、读取文件内容
//        FTPClient ftpClient = null;
//        try {
//            ftpClient = FtpUtils.getConnectClient();
//            String mapInfoStr = getDraftFileInfo(ftpClient, srcFilePath, destFilePath);
//
//            JSONObject mapObj = JSONObject.parseObject(mapInfoStr);
//            JSONArray list = mapObj.getJSONArray("Marker");
//            if (list != null && list.size() > 0) {
//                for (int i = 0; i < list.size(); i++) {
//                    Marker tmp = list.getObject(i, Marker.class);
//                    if ( (MARKER_TYPE_WORK.equalsIgnoreCase(tmp.getType())
//                            || MARKER_TYPE_CHARGING.equalsIgnoreCase(tmp.getType()))) {
//                        tmp.setDockingPoint(null);
//                        list.add(i, JSONObject.parseObject(JSONObject.toJSONString(tmp)));
//                    }
//                }
//            }
//
//            //写入
//            mapObj.put("Marker", list.toJSONString());
//
//            //3、存储到ftp文件
//            byte[] bytes = JSONObject.toJSONString(mapObj).getBytes();
//            ftpClient.storeFile(destFilePath, new ByteInputStream(bytes, bytes.length));
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }finally {
//            if(ftpClient!=null){
//                FtpUtils.disconnect(ftpClient);
//            }
//        }
//    }
//
//    @Override
//    public PageInfo<DockingPoint> findPage(Map<String, String> searchMap, boolean isDraft) {
//        //1、从内存地图 数据中，获取
//        List<DockingPoint> list = searchAll(searchMap,isDraft);
//
//        Integer pageNum = null;
//        Integer pageSize = null;
//        String pageNumStr = searchMap.get("pageNum");
//        String pageSizeStr = searchMap.get("pageSize");
//        if(StringUtils.isNotBlank(pageNumStr)){
//            pageNum = Integer.valueOf(pageNumStr);
//        }
//        if(StringUtils.isNotBlank(pageSizeStr)){
//            pageSize = Integer.valueOf(pageSizeStr);
//        }
//
//        //2、分页查询
//        return MapFileUtils.getPageList(list, pageNum, pageSize);
//    }
//
//    @Override
//    public List<DockingPoint> searchAll(Map<String, String> searchMap, boolean isDraft) {
//        if(searchMap==null || searchMap.size()<=0){
//            throw new RuntimeException("param is null!");
//        }
//        String mapName = searchMap.get("agvMapName");
//        if(StringUtils.isBlank(mapName)){
//            throw new RuntimeException("agvMapName param is null!");
//        }
//        List<DockingPoint> dockingPoints = selectByAGVMapId(mapName, isDraft);
//        return dockingPoints;
//    }
//

}
