package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.AGVStatistics;

import java.util.Date;
import java.util.Map;

/**
 * @Author：yangpeilin
 * @Date: 2020/5/21 17:22
 */
public interface AGVStatisticsService extends BaseService<AGVStatistics>{

    AGVStatistics findDataByCreateTimeAndType(Date startDate, Date endDate, int dataType,String agvId);

    AGVStatistics sum(String agvId);

    AGVStatistics findDataByType(int dataType,String agvId);

    AGVStatistics getAGVStatistics(Map<String, String> searchMap);

    Long findDataByRecentDateOfDay(Integer day);
}
