package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.SchedulePlan;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.scheduleplan.SchedulePlanUtil;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.SchedulePlanService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.*;

import static com.youibot.agv.scheduler.constant.SchedulePlanConstant.*;

@Service
public class SchedulePlanServiceImpl extends BaseServiceImpl<SchedulePlan> implements SchedulePlanService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SchedulePlanServiceImpl.class);

    @Autowired
    private SchedulerFactoryBean schedulerFactoryBean;

    @Autowired
    private SchedulePlanUtil schedulePlanUtil;

    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionWorkService missionWorkService;

    private Scheduler scheduler = null;

    @PostConstruct
    private void init() {
        scheduler = schedulerFactoryBean.getScheduler();
    }

    /*
     * 创建任务
     * @Param plan 调度计划
     * @missionActionParameters 任务动作参数
     */
    @Override
    public String createSchedule(SchedulePlan plan) {
        this.checkName(plan);
        SchedulePlan planEntity = new SchedulePlan();
        String missionId = plan.getMissionId();
        if (StringUtils.isEmpty(missionId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Mission mission = missionService.selectById(missionId);
        if (mission == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_is_null"));
        }
        String uuid = UUID.randomUUID().toString();
        planEntity.setId(uuid);
        planEntity.setStatus(SCHEDULE_STATUS_READY);
        planEntity.setDescription(plan.getDescription());
        planEntity.setCallbackUrl(plan.getCallbackUrl());
        planEntity.setName(plan.getName());
        planEntity.setMissionId(missionId);
        planEntity.setAgvCode(plan.getAgvCode());
        planEntity.setExecuteInterval(plan.getExecuteInterval());
        planEntity.setExecuteOverCreateNew(plan.getExecuteOverCreateNew());
        if (StringUtils.isEmpty(plan.getCron())) {
            planEntity.setFrequency(plan.getFrequency());
            planEntity.setExecuteTime(plan.getExecuteTime() != null ? plan.getExecuteTime() : new Date());
            LOGGER.debug("Build SimpleTrigger schedule plan" + planEntity);
        } else {
            planEntity.setCron(plan.getCron());
            LOGGER.debug("Build CronTrigger schedule plan" + planEntity);
        }
        super.insert(planEntity);
        try {
            schedulePlanUtil.addPlan(planEntity);
        } catch (Exception e) {
            super.deleteById(uuid);
            LOGGER.error("添加到quartz失败, ", e);
            throw new ExecuteException(MessageUtils.getMessage("service.schedule_plan_add_quartz_error"));
        }
        return planEntity.getId();
    }

    private void checkName(SchedulePlan schedulePlan) {
        List<SchedulePlan> schedulePlanList = this.findAll();
        for (SchedulePlan schedulePlanDB : schedulePlanList) {
            if (!StringUtils.isEmpty(schedulePlan.getName()) && schedulePlan.getName().equals(schedulePlanDB.getName()) && !schedulePlanDB.getId().equals(schedulePlan.getId())) {
                throw new ADSParameterException(MessageFormat.format(MessageUtils.getMessage("service.name_is_exists"), schedulePlan.getName()));
            }
        }
    }

    @Override
    public void schedulePause(SchedulePlan plan) throws SchedulerException {
        if (SCHEDULE_STATUS_PAUSE.equals(plan.getStatus())) {
            return;
        }
        String triggerState = schedulePlanUtil.getTriggerState(plan);
        if (SCHEDULE_TRIGGER_STATUS_NONE.equals(triggerState)) {
            throw new ExecuteException(MessageUtils.getMessage("service.schedule_plan_trigger_state_none"));
        } else if (SCHEDULE_TRIGGER_STATUS_PAUSED.equals(triggerState)) {
            LOGGER.debug("job already pause, ");
        } else {
            schedulePlanUtil.pauseJob(plan);
        }
        SchedulePlan planEntity = new SchedulePlan();
        planEntity.setId(plan.getId());
        planEntity.setStatus(SCHEDULE_STATUS_PAUSE);
        super.updateByPrimaryKeySelective(planEntity);
    }

    /*
     * 恢复任务
     * @Param plan 调度计划
     * @Param state 是否修改schedule plan 状态
     */
    @Override
    public void scheduleResume(SchedulePlan plan) throws SchedulerException {
        if (!SCHEDULE_STATUS_PAUSE.equals(plan.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("service.schedule_plan_status_is_not_pause"));
        }
        String triggerState = schedulePlanUtil.getTriggerState(plan);
        if (SCHEDULE_TRIGGER_STATUS_NONE.equals(triggerState)) {
            throw new ExecuteException(MessageUtils.getMessage("service.schedule_plan_trigger_state_none"));
        }
        if (plan.getExecuteOverCreateNew()) {//如果调度计划是执行完创建
            //查询是否有未执行完的missionWork
            List<MissionWork> missionWorks = missionWorkService.selectUnCompleteBySchedulePlanId(plan.getId());
            if (missionWorks == null || missionWorks.isEmpty()) {
                schedulePlanUtil.resumeJob(plan);//没有未执行完的工作，恢复quartz调度
            }
        } else {
            schedulePlanUtil.resumeJob(plan);//不是执行完创建，恢复quartz调度
        }
        SchedulePlan planEntity = new SchedulePlan();
        planEntity.setId(plan.getId());
        planEntity.setStatus(SCHEDULE_STATUS_RUNNING);
        super.updateByPrimaryKeySelective(planEntity);
    }

    // 删除任务
    @Override
    public void scheduleDelete(SchedulePlan plan) throws SchedulerException {
        String triggerState = schedulePlanUtil.getTriggerState(plan);
        if (!SCHEDULE_TRIGGER_STATUS_NONE.equals(triggerState)) {
            schedulePlanUtil.deleteJob(plan);
        }
        super.deleteById(plan.getId());
    }

    @Override
    public void scheduleShutDown(SchedulePlan plan) throws SchedulerException {
        if (SCHEDULE_STATUS_SHUTDOWN.equals(plan.getStatus()) || SCHEDULE_STATUS_SUCCESS.equals(plan.getStatus()) || SCHEDULE_STATUS_DELETE.equals(plan.getStatus())) {
            return;
        }
        String triggerState = schedulePlanUtil.getTriggerState(plan);
        if (!SCHEDULE_TRIGGER_STATUS_NONE.equals(triggerState)) {
            schedulePlanUtil.deleteJob(plan);
        }
        SchedulePlan planEntity = new SchedulePlan();
        planEntity.setId(plan.getId());
        planEntity.setStatus(SCHEDULE_STATUS_SHUTDOWN);
        super.updateByPrimaryKeySelective(planEntity);
    }

    //更新
    @Override
    public Integer scheduleUpdate(SchedulePlan plan) {
        this.checkName(plan);
        SchedulePlan planEntity = new SchedulePlan();
        planEntity.setId(plan.getId());
        if (StringUtils.isEmpty(plan.getName()) && StringUtils.isEmpty(plan.getDescription())) {
            LOGGER.warn(" schedulingPlan update failed" + plan);
            return null;
        }
        if (!StringUtils.isEmpty(plan.getName())) {
            planEntity.setName(plan.getName());
        }
        if (!StringUtils.isEmpty(plan.getDescription())) {
            planEntity.setDescription(plan.getDescription());
        }
        return super.updateByPrimaryKeySelective(planEntity);
    }

    @Override
    public void initSchedulePlanBySystemRestart() {
        //获取所有正在执行中的schedulePlan
        List<SchedulePlan> schedulePlans = this.selectByStatus(SCHEDULE_STATUS_RUNNING);
        if (schedulePlans == null || schedulePlans.isEmpty()) {
            return;
        }
        for (SchedulePlan schedulePlan : schedulePlans) {
            try {
                if (schedulePlan.getExecuteOverCreateNew()) {//如果是调度计划执行完创建
                    //查询是否有未执行完的missionWork
                    List<MissionWork> missionWorks = missionWorkService.selectUnCompleteBySchedulePlanId(schedulePlan.getId());
                    if (missionWorks != null && !missionWorks.isEmpty()) {
                        schedulePlanUtil.waitMissionWorkEndAsync(schedulePlan, missionWorks.get(0).getId());
                    } else if (SCHEDULE_TRIGGER_STATUS_PAUSED.equals(schedulePlanUtil.getTriggerState(schedulePlan))) {
                        schedulePlanUtil.resumeJob(schedulePlan);
                    }
                }
            } catch (SchedulerException e) {
                LOGGER.error("resume job error, ", e);
            }
        }
    }

    @Override
    public List<SchedulePlan> selectByStatus(String status) {
        Example example = new Example(SchedulePlan.class);
        example.createCriteria().andEqualTo("status", status);
        return super.selectByExample(example);
    }

    //重写
    protected Example getExample(Map<String, String> searchMap, Class<?> entityClass) {
        Example example = new Example(entityClass);
        Criteria criteria = example.createCriteria();
        List<String> list = new ArrayList<>();
        list.add(SCHEDULE_STATUS_DELETE);
        if (searchMap != null && !searchMap.isEmpty()) {
            for (String key : searchMap.keySet()) {
                if ("pageNum".equals(key) || "pageSize".equals(key)) {
                    continue;
                }
                if ("sort".equals(key)) {
                    example.setOrderByClause(searchMap.get(key));
                    continue;
                }
                criteria.andEqualTo(key, handlerMapKey(searchMap.get(key)));
            }
        }
        criteria.andNotIn("status", list);
        return example;
    }

}
