package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.RequestPath;

public interface RequestPathService extends BaseService<RequestPath> {

    RequestPath save(String agvId, String markerId,String missionWorkId, String missionWorkActionId);

    RequestPath selectByMissionActionId(String vehicleId, String missionWorkActionId);

    void deleteRequestPathData(Integer day);

}
