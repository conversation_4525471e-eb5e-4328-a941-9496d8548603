package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.mapper.AbnormalPromptMapper;
import com.youibot.agv.scheduler.service.AbnormalPromptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class AbnormalPromptServiceImpl extends BaseServiceImpl<AbnormalPrompt> implements AbnormalPromptService {

    @Autowired
    private AbnormalPromptMapper abnormalPromptMapper;

    @Override
    public AbnormalPrompt getAbnormalByCode(Integer abnormalCode) {
        return abnormalPromptMapper.getAbnormalByCode(abnormalCode);
    }

    @Override
    public String getAbnormalMsgByCode(Integer abnormalCode) {
        Example example = new Example(AbnormalPrompt.class);
        example.createCriteria().andEqualTo("abnormalCode", abnormalCode);
        AbnormalPrompt abnormalPrompt = this.selectOneByExample(example);
        if (null != abnormalPrompt) {
            return abnormalPrompt.getAbnormalDescription();
        }
        return null;
    }

    @Override
    public List<String> getAbnormalTypeList() {
        return abnormalPromptMapper.getAbnormalTypeList();
    }

    @Override
    public List<Integer> getAbnormalCodeList() {
        return abnormalPromptMapper.getAbnormalCodeList();
    }

    @Override
    public List<AbnormalPrompt> queryAbnormalData(Integer abnormalLevel, String abnormalType, Integer abnormalCode, String abnormalDescription) {
        return abnormalPromptMapper.queryAbnormalData(abnormalLevel,abnormalType,abnormalCode,abnormalDescription);
    }
}
