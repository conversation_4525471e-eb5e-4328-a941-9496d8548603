package com.youibot.agv.scheduler.service.impl;

import com.google.common.base.Throwables;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.entity.AGVGroup;
import com.youibot.agv.scheduler.listener.event.NotifyMessageEvent;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage.ReportType;
import com.youibot.agv.scheduler.mqtt.service.VehicleCommandService;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;
import java.util.LinkedHashSet;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class VehicleControlServiceImpl implements VehicleControlService {

	@Autowired
	protected VehiclePool vehiclePool;

	@Autowired
	private WorkCycleConfigService workCycleConfigService;

	@Autowired
	private SchedulerConfigService schedulerConfigService;
	
	@Autowired
	private AGVService agvService;

	@Autowired
	private VehicleCommandService vehicleCommandService;
	
	  @Autowired
	  protected AGVGroupService agvGroupService;
	
	/**
	 * 切换为手工控制模式
	 */
	@Override
	public void manualMode(String agvCode) throws IOException {
		vehiclePool.getVehicle(agvCode).switchManualMode();
		/**
		 * agv 下线
		 */
		NotifyMessageEvent.publish(agvCode, ReportType.BIZ_OFFlINE_READY);
		
		vehiclePool.getVehicle(agvCode).reset(   );
		vehicleCommandService.linecePatro(agvCode, false );
	}

	/**
	 * 切换为自动控制模式
	 */
	@Override
	public void autoMode(String agvCode) throws IOException, InterruptedException {
		vehiclePool.getVehicle(agvCode).switchAutoMode();
		/**
		 * agv 上线
		 */
		NotifyMessageEvent.publish(agvCode, ReportType.ONlINE_READY);
		workCycleConfigService.refresh();

		vehiclePool.getVehicle(agvCode).reset(   );
		 vehicleCommandService.linecePatro(agvCode, true );

	}

	/**
	 * 批量切换为手工控制模式
	 */
	@Override
	public void manualModeBatch(@RequestBody LinkedHashSet<String> agvCodes) throws IOException {
		agvCodes.parallelStream().forEach(item -> {
			try {
				this.manualMode(item);
			} catch (AGVResultException | IOException e) {
				log.error("agv:{},error:{}", item, Throwables.getStackTraceAsString(e));
			}
		});

	}

	/**
	 * 批量切换为自动控制模式
	 */
	@Override
	public void autoModeBatch(LinkedHashSet<String> agvCodes) throws IOException, InterruptedException {

		agvCodes.parallelStream().forEach(agvCode -> {
			try {
				this.autoMode(agvCode);
			} catch (InterruptedException | IOException e) {
				log.error("agv:{},error:{}", agvCode, Throwables.getStackTraceAsString(e));
			}
		});
	}

	/**
	 * 台积电项目切换巡线,非巡线
	 */
	@Override
	public void linecePatro(String agvCode, Boolean linePatrolMode) {

		try {
			Vehicle vehicle = vehiclePool.getVehicle(agvCode);
			boolean enableCycle = enableCycle();
			if(StringUtils.isNotBlank(vehicle.getAgvGroupId())) {
				AGVGroup agvGroup = agvGroupService.selectById( vehicle.getAgvGroupId()) ;
				if(Objects.nonNull(agvGroup)) {
					enableCycle =  agvGroup.getWorkerCount() > 0 ;
				}
				
			}
			if (!linePatrolMode) {
				if (enableCycle ) {
					vehicle.getLinePatroWeight().getAndDecrement();

				} else {
					vehicle.setLinePatrolMode(false);
					vehicle.getLinePatroWeight().set(0);
					TjdCxt.removeStationCountByAgv( vehicle.getId());
					
				}
				NotifyMessageEvent.publish(agvCode, ReportType.BIZ_OFFlINE_READY);
			} else {

				if (enableCycle ) {
					vehicle.getLinePatroWeight().getAndIncrement();

				} else {
					vehicle.setLinePatrolMode(true);
					vehicle.getLinePatroWeight().set(0);
				}

				NotifyMessageEvent.publish(agvCode, ReportType.ONlINE_READY);
			} 
		} finally {
			agvService.updateLinePatroline( agvCode);
			
		}
	}

	/**
	 * 台积电项目切换充电,取消充电
	 */
	@Override
	public void recharge(String agvCode, Boolean recharge) {

		try {
			synchronized (agvCode) {
				Vehicle vehicle = vehiclePool.getVehicle(agvCode);
				if (Objects.nonNull(vehicle)) {
					if (recharge) {
						/**
						 * 充电则自动切换为非巡线模式
						 */
						if (enableCycle()) {
							int linePatroWeight = vehicle.getLinePatroWeight().get();
							vehicle.getLinePatroWeight().set(linePatroWeight - 5);
						} else {
							vehicle.setLinePatrolMode(false);
							vehicle.getLinePatroWeight().set(0);
							vehicleCommandService.linecePatro(agvCode, false);
						}

					} else {
						vehicle.cancelCharge();
					}

				}
			} 
		} finally {
			agvService.updateLinePatroline( agvCode);
		}

	}

	/**
	 * 台积电项目切换巡线,非巡线-批量
	 */
	@Override
	public void linecePatroBatch(LinkedHashSet<String> agvCodes, Boolean linePatrolMode) {

		agvCodes.parallelStream().forEach(agvCode -> {

			this.linecePatro(agvCode, linePatrolMode);
		});

	}

	/**
	 * 台积电项目切换充电,取消充电-批量
	 */
	@Override
	public void rechargeBatch(LinkedHashSet<String> agvCodes, Boolean recharge) {
		agvCodes.parallelStream().forEach(agvCode -> {

			this.recharge(agvCode, recharge);
		});

	}

	/**
	 * 获取调度配置是否开启智能巡线
	 * 
	 * @return
	 */
	private boolean enableCycle() {
		return schedulerConfigService.selectSchedulerConfig().getEnableCycle();
	}

}
