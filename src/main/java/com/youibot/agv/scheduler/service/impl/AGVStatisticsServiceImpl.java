package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.AGVStatistics;
import com.youibot.agv.scheduler.entity.DataBaseDate;
import com.youibot.agv.scheduler.mapper.AGVStatisticsMapper;
import com.youibot.agv.scheduler.service.AGVStatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author：yangpeilin
 * @Date: 2020/5/21 17:22
 */
@Service
public class AGVStatisticsServiceImpl extends BaseServiceImpl<AGVStatistics> implements AGVStatisticsService {

    Logger logger = LoggerFactory.getLogger(AGVStatisticsServiceImpl.class);

    @Autowired
    private AGVStatisticsMapper agvStatisticsMapper;

    @Override
    public AGVStatistics findDataByCreateTimeAndType(Date startDate, Date endDate, int dataType, String agvCode) {
        Example example = new Example(AGVStatistics.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dataType", dataType).andEqualTo("agvCode", agvCode);
        if (endDate != null){
            criteria.andBetween("createTime", startDate, endDate);
        }else {
            criteria.orGreaterThanOrEqualTo("createTime", startDate);
        }
        return this.selectOneByExample(example);
    }

    @Override
    public AGVStatistics sum(String agvCode) {
        return agvStatisticsMapper.sum(agvCode);
    }

    @Override
    public AGVStatistics findDataByType(String agvCode, int dataType) {
        Example example = new Example(AGVStatistics.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dataType", dataType).andEqualTo("agvCode", agvCode);
        return this.selectOneByExample(example);
    }

    @Override
    public AGVStatistics getAGVStatistics(Map<String, String> searchMap) {
        Example example = super.getExample(searchMap, AGVStatistics.class,"createTime", searchMap.get("startTime") == null ? 0 : Long.parseLong(searchMap.get("startTime")),
                searchMap.get("endTime") == null ? 0 : (Long.parseLong(searchMap.get("endTime"))));
        List<AGVStatistics> agvStatisticsList = super.selectByExample(example);
        if (CollectionUtils.isEmpty(agvStatisticsList)){
            logger.debug("getAGVStatistics result is empty, searchMap:{}", Arrays.toString(searchMap.entrySet().toArray()));
            return new AGVStatistics();
        }
        AGVStatistics agvStatistics = null;
        for (AGVStatistics bean : agvStatisticsList) {
            if (agvStatistics == null){
                agvStatistics = bean;
            }else {
                agvStatistics.addTodayData(bean);
            }
        }
        agvStatistics.setAgvCode("");
        return agvStatistics;
    }

    @Override
    public Long findDataByRecentDateOfDay(Integer day) {
        return agvStatisticsMapper.findDataByRecentDateOfDay(day);
    }
}
