package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.entity.AGVGroup;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.vo.AGVBindMarkerVo;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.mapper.AGVMapper;
import com.youibot.agv.scheduler.mqtt.bean.push.map.BatchOperateMessage;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.service.MqttService;
import com.youibot.agv.scheduler.mqtt.service.VehicleCommandService;
import com.youibot.agv.scheduler.service.AGVGroupService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.PreDestroy;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_BEING_SHUTDOWN;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;

@Slf4j
@Service
public class AGVServiceImpl extends BaseServiceImpl<Agv> implements AGVService {

    @Autowired
    private AGVMapper agvMapper;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private VehiclePool vehiclePool;

    @Autowired
    private MqttService mqttService;
    
    @Autowired
	private VehicleCommandService vehicleCommandService;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    @Autowired
    private AGVGroupService agvGroupService;

    private void checkName(Agv agv) {
        List<Agv> agvList = this.selectAll();
        for (Agv agvDB : agvList) {
            if (!StringUtils.isEmpty(agv.getAgvName()) && agv.getAgvName().equals(agvDB.getAgvName()) && !agvDB.getAgvCode().equals(agv.getAgvCode())) {
                throw new ADSParameterException(MessageFormat.format(MessageUtils.getMessage("service.name_is_exists"), agv.getAgvName()));
            }
        }
    }

    @Override
    public String getAGVName(String id) {
        Agv agv = super.selectById(id);
        if (agv != null) {
            return agv.getAgvName();
        }
        return null;
    }

    @Override
    public List<Agv> selectAll() {
        return super.findAll();
    }

    @Override
    public List<Agv> selectByAgvGroupId(String agvGroupId) {
        Example example = new Example(Agv.class);
        example.createCriteria().andEqualTo("agvGroupId", agvGroupId);
        return super.selectByExample(example);
    }

    @Override
    public List<Agv> selectByAgvType(String agvType) {
        Example example = new Example(Agv.class);
        example.createCriteria().andEqualTo("agvType", agvType);
        return super.selectByExample(example);
    }

    @Override
    public Agv selectByAgvCode(String agvCode) {
        Example example = new Example(Agv.class);
        example.createCriteria().andEqualTo("agvCode", agvCode);
        return super.selectOneByExample(example);
    }

    @Override
    public void updateOnlineStatusByAgvCode(String agvCode, Integer onlineStatus) {
        Agv agv = this.selectByAgvCode(agvCode);
        if (agv != null) {
            agv.setOnlineStatus(onlineStatus);
            //清空状态信息
            agv.setAbnormalStatus(9);
            agv.setControlMode(9);
            agv.setMapStatus(9);
            agv.setAppointStatus(9);
            agv.setWorkStatus(9);
            // 21/4/9 断线状态机器人需要清理agvId,可以让其他相同agvCode的机器人登录
            agv.setAgvId(null);
            super.update(agv);
        }
//        agvMapper.updateOnlineStatusByAgvCode(agvCode, onlineStatus);
    }

    @Override
    public void stopBatch(List<String> agvCodes) {
        Example example = new Example(MissionWork.class);
        example.createCriteria()
                .andNotIn("status", Lists.newArrayList("CREATE", "SUCCESS", "SHUTDOWN"))
                .andIn("agvCode", agvCodes);
        List<MissionWork> missionWorkList = missionWorkService.selectByExample(example);
        if (!CollectionUtils.isEmpty(missionWorkList)) {
            List<MissionWork> sendMqList = new ArrayList<>();
            List<String> agvCodeList = new ArrayList<>();
            missionWorkList.stream().forEach(missionWork -> {
                String agvCode = missionWork.getAgvCode();
                if (StringUtils.isEmpty(agvCode)) {
                    missionWork.setStatus(MISSION_WORK_STATUS_SHUTDOWN);
                } else {
                    missionWork.setStatus(MISSION_WORK_STATUS_BEING_SHUTDOWN);
                    sendMqList.add(missionWork);
                    agvCodeList.add(agvCode);
                }
            });
            missionWorkService.batchUpdate(missionWorkList);
            mqttService.bathOperate(new BatchOperateMessage(agvCodeList, MqttConstant.BATCH_STOP_MISSIONWORK, null, sendMqList));
        }
    }

    @Override
    public String getVehicleMissionWorkId(String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if (vehicle == null){
            return null;
        }
        return vehicle.getMissionWorkId();
    }

    @Override
    public void clearResource(String agvCode) {
        checkAndSendPathService.clearAgvResource(agvCode);
    }


    @Override
    public Map<String, List<String>> getAGVBindMarkers(String bindMarkers) {
        List<AGVBindMarkerVo> agvBindMarkerVos = JSONObject.parseArray(bindMarkers, AGVBindMarkerVo.class);
        if (!CollectionUtils.isEmpty(agvBindMarkerVos)) {
            Map<String, List<String>> bindMarkerIds = new HashMap<>();
            for (AGVBindMarkerVo agvBindMarkerVo : agvBindMarkerVos) {
                if (bindMarkerIds.containsKey(agvBindMarkerVo.getAgvMapId())) {
                    List<String> stringList = bindMarkerIds.get(agvBindMarkerVo.getAgvMapId());
                    stringList.add(agvBindMarkerVo.getMarkerCode());
                } else {
                    List<String> stringList = new ArrayList<>();
                    stringList.add(agvBindMarkerVo.getMarkerCode());
                    bindMarkerIds.put(agvBindMarkerVo.getAgvMapId(), stringList);
                }
            }
            return bindMarkerIds;
        }
        return null;
    }

    
    @PreDestroy
    public void destrony() {
    	List<Agv> list = selectAll();
    	Collection<String> allAgvCodes = vehiclePool.getAllAgvCodes();
    	list = list.parallelStream().filter( p -> allAgvCodes.contains(p.getAgvCode())).map( agv ->{
    		
    		Vehicle vehicle = vehiclePool.getVehicle( agv.getAgvCode());
    		agv.setLinePatrolMode( vehicle.getLinePatrolMode());
    		agv.setLinePatroWeight(vehicle.getLinePatroWeight().get());
    		
    		return agv;
    	}).collect(Collectors.toList());
    	
    	if(CollectionUtils.isNotEmpty(list)) {
    		super.batchUpdate(list); 
    	}
    }

    
    @Override
    public void updateLinePatroline(String agvCode, boolean direct) {
    	Agv agv = selectByAgvCode(agvCode);
		Vehicle vehicle = vehiclePool.getVehicle( agvCode);
		if(Objects.isNull(vehicle)) {
			return ;
		}
       if( direct){
           agv.setId(agvCode);
           agv.setLinePatrolMode( vehicle.getLinePatrolMode());
           agv.setLinePatroWeight(vehicle.getLinePatroWeight().get());
           updateByPrimaryKeySelective(agv);
           vehicleCommandService.linecePatro(agvCode, vehicle.getLinePatrolMode());
       }

	}
    @Override
    public void updateLinePatroline(String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle( agvCode);
        if(Objects.isNull(vehicle)) {
            return ;
        }
        String agvGroupId = vehicle.getAgvGroupId();
        if(StringUtils.isNotBlank(agvGroupId)){
            AGVGroup group = agvGroupService.selectById(agvGroupId);
            if(Objects.nonNull(group)){
                boolean lightByBattery = group.isLightByBattery();
                this.updateLinePatroline(agvCode , lightByBattery );
            }

        }else{
            this.updateLinePatroline(agvCode , true );
        }


    }

	@Override
	public void updateEnable() {
		
		agvMapper.updateEnable( 1) ;
		
		
		
	}

}
