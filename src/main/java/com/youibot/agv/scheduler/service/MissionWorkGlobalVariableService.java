package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;

import java.util.List;

public interface MissionWorkGlobalVariableService extends BaseService<MissionWorkGlobalVariable>{

    List<MissionWorkGlobalVariable> selectByMissionWorkId(String missionWorkId);

    List<MissionWorkGlobalVariable> selectByMissionWorkIdAndKeys(String missionWorkId, List<String> keys);

    /**
     * 获取等待数据的变量集合
     * @param missionWorkId 工作ID
     * @return
     */
    List<MissionWorkGlobalVariable> selectWaitInputByMissionWorkId(String missionWorkId);

    void insertByMissionIdAndMissionWork(String missionId, MissionWork missionWork);

    /**
     * 根据时间设置删除过期数据
     * @param missionWork
     */
    void deleteExpireDataByTime(Integer missionWork);

}
