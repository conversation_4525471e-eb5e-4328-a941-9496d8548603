package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.ParkScheduler;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 14:43
 */
public interface ParkSchedulerService extends BaseService<ParkScheduler> {

    List<ParkScheduler> selectCreate();

    List<ParkScheduler> selectRunning();

    List<ParkScheduler> selectCreateAndStart();

    ParkScheduler selectRunningByVehicle(String vehicleId);

    void updateCancel(Long id);

    void updateCancel( );
    
    void updateSuccess(Long id);

    void updateStart(Long id);

    void updateFault(Long id, String faultMessage);

    /**
     * 查询出timeDiff秒内取消泊车的机器人
     *
     * @param timeDiff
     * @return
     */
    List<String> selectCancelByFinishTimeDiff(String timeDiff);

    ParkScheduler selectLastOne(String vehicleId);

    void deleteExpireDataByTime(Integer missionWork);
}
