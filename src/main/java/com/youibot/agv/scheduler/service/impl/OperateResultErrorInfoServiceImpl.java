package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.MqOperateResultInfo;
import com.youibot.agv.scheduler.service.OperateResultErrorInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/31 17:54
 */
@Service
public class OperateResultErrorInfoServiceImpl extends BaseServiceImpl<MqOperateResultInfo> implements OperateResultErrorInfoService {

    Logger logger = LoggerFactory.getLogger(OperateResultErrorInfoServiceImpl.class);

    @Override
    public void insetOperateResult(String agvCode, String type, String message, int result) {
        MqOperateResultInfo mqOperateResultInfo = new MqOperateResultInfo();
        mqOperateResultInfo.setAgvCode(agvCode);
        mqOperateResultInfo.setOperateType(type);
        mqOperateResultInfo.setMessage(message);
        mqOperateResultInfo.setResult(result);
        mqOperateResultInfo.setHaveRead(0);
        MqOperateResultInfo mqOperateResultInfoDto = null;
        List<MqOperateResultInfo> byEntity = this.findByEntity(mqOperateResultInfo);
        if (!CollectionUtils.isEmpty(byEntity)){
            logger.debug("接收到重复未读结果消息，agvCode：{}，type：{}, result：{}， message：{}", agvCode, type, result, message);
            return;
        }
        this.insert(mqOperateResultInfo);
    }
}
