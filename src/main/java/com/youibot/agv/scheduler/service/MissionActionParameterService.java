package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.MissionActionParameter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月8日 下午2:48:57
 */
public interface MissionActionParameterService extends BaseService<MissionActionParameter> {

	List<MissionActionParameter> selectByMissionActionId(String missionActionId);

	List<MissionActionParameter> selectByMissionActionIds(List<String> missionActionIds);

	/**
	 *
	 * @param missionActionIds
	 * @return key String为missionActionId
	 */
	Map<String, List<MissionActionParameter>> mapMissionActionParameterByMissionActionIds(List<String> missionActionIds);

	@Override
	Integer update(MissionActionParameter missionActionParameter);

	void deleteByMissionActionId(String missionActionId);

	void deleteByMissionActionIds(List<String> missionActionIds);
}
