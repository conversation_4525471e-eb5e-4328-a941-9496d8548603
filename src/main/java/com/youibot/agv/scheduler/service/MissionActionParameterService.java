package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.MissionActionParameter;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月8日 下午2:48:57
 */
public interface MissionActionParameterService extends BaseService<MissionActionParameter> {

	List<MissionActionParameter> selectByMissionActionId(String missionActionId);

    void deleteByMissionId(String missionId);

    List<MissionActionParameter> selectByMissionId(String missionId);

    MissionActionParameter seleceByMissionWorkParam(String missionActionId, String parameterValue);
}
