package com.youibot.agv.scheduler.service;


import com.youibot.agv.scheduler.entity.SchedulePlan;
import org.quartz.SchedulerException;

import java.util.List;


public interface SchedulePlanService extends BaseService<SchedulePlan> {

    String createSchedule(SchedulePlan plan);

    void schedulePause(SchedulePlan plan) throws SchedulerException;

    void scheduleResume(SchedulePlan plan) throws SchedulerException;

    void scheduleDelete(SchedulePlan plan) throws SchedulerException;

    void scheduleShutDown(SchedulePlan plan) throws SchedulerException;

    Integer scheduleUpdate(SchedulePlan plan);

    void initSchedulePlanBySystemRestart();

    List<SchedulePlan> selectByStatus(String status);
}
