package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

@Service
public abstract class BaseVersionServiceImpl<T> extends BaseServiceImpl<T> implements BaseService<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseVersionServiceImpl.class);

    @Override
    public Integer insert(T t) {
        this.setVersion(t);
        return super.insert(t);
    }

    @Override
    public Integer update(T t) {
        this.setVersion(t);
        return super.update(t);
    }


    @Override
    public Integer updateByPrimaryKeySelective(T t) {
        this.setVersion(t);
        return super.updateByPrimaryKeySelective(t);
    }

    @Override
    public Integer deleteById(String id) {
        this.setVersion(this.selectById(id));
        return super.deleteById(id);
    }

    @Override
    public Integer batchInsert(List<T> list) {
        this.setVersions(list);
        return super.batchInsert(list);
    }

    @Override
    public Integer batchUpdate(List<T> list) {
        this.setVersions(list);
        return super.batchUpdate(list);
    }

    @Override
    public Integer delete(T t) {
        this.setVersion(t);
        return super.delete(t);
    }

    @Override
    public Integer deleteByIds(List<String> ids) {
        this.setVersions(this.selectByIds(ids));
        return super.deleteByIds(ids);
    }


    public String getBeanId(T t){ //抽出来
        if (t == null){
            return null;
        }
        try {
            Class<?> aClass = t.getClass();
            String fieldStr = getFieldName(t);
            Field field = aClass.getDeclaredField(fieldStr);
            if (field != null){
                field.setAccessible(true);
                Object obj = field.get(t);
                if (obj != null){
                    return obj.toString();
                }
            }
        } catch (NoSuchFieldException e) {
            LOGGER.error("当前类没有beanId字段，类：{}，error：{}", t.getClass(), e);
        } catch (IllegalAccessException e) {
            LOGGER.error("获取beanId失败，类：{}，error：{}", t.getClass(), e);
        }
        return null;
    }

    protected void setVersion(T t){
        String beanId = getBeanId(t);
        if (!StringUtils.isEmpty(beanId)){
            this.setVersionById(beanId);
        }
    }

    protected void setVersions(List<T> list){
        if (list.isEmpty()){
            return;
        }
        List<String> beanIdList = new ArrayList<>();
        list.stream().forEach(t -> {
            String beanId = getBeanId(t);
            if (!StringUtils.isEmpty(beanId) && !beanIdList.contains(beanId)){
                beanIdList.add(beanId);
            }
        });
        if (!beanIdList.isEmpty()){
            beanIdList.stream().forEach(beanId -> this.setVersionById(beanId));
        }
    }

    public abstract String getFieldName(T t);

    public abstract void setVersionById(String beanId);

}
