package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.MapConstant;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.entity.ForeignAction;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.bean.push.foreignAction.ForeignActionMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.foreignAction.ForeignActionResultMessage;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.param.ForeignActionCallBackParam;
import com.youibot.agv.scheduler.param.ForeignActionRequestParam;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.service.ForeignActionService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.util.ForeignActionUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.youibot.agv.scheduler.constant.TjdCxt.*;

@Service
public class ForeignActionServiceImpl extends BaseServiceImpl<ForeignAction> implements ForeignActionService {

    private Logger logger = LoggerFactory.getLogger(ForeignActionServiceImpl.class);

    @Autowired
    private DefaultVehiclePool vehiclePool;
    @Autowired
    private HttpClientService httpClientService;

    @Autowired
    private SchedulerConfigService configService ;

    @Override
    public void verifyParamAndAGVStatus(ForeignActionRequestParam foreignActionRequestParam) {
        String agvCode = foreignActionRequestParam.getAgvCode();
        String actionType = foreignActionRequestParam.getActionType();
        Optional.ofNullable(agvCode).orElseThrow(() -> new ExecuteException("机器人编号不能为空"));
        Optional.ofNullable(actionType).orElseThrow(() -> new ExecuteException("动作类型不能为空"));
//        Optional.ofNullable(foreignActionRequestParam.getCallBackUrl()).orElseThrow(() -> new ExecuteException("回传url不能为空"));

        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Optional.ofNullable(vehicle).orElseThrow(() -> new ExecuteException("机器人编号不存在"));

        //校验动作是否可行执行
        List<String> canNotExecuteAction = ForeignActionUtils.getCanNotExecuteAction();
        if (canNotExecuteAction.contains(actionType)) {
            throw new ExecuteException("动作类型不可执行");
        }
        List<String> canExecuteAction = ForeignActionUtils.getCanExecuteAction();
        if (!canExecuteAction.contains(actionType)) {
            throw new ExecuteException("找不到对应动作类型");
        }

        //校验agv状态
        boolean flag = true;
        StringBuilder sb = new StringBuilder();
        //控制模式
        if (VehicleConstant.MANUAL_CONTROL_MODE.equals(vehicle.getControlMode())) {
            flag = false;
            sb.append("机器人为手动模式").append(";");
        }
        //地图是否指定
        if (MapConstant.MAP_STATUS_NOT_APPOINT.equals(vehicle.getAppointStatus())) {
            flag = false;
            sb.append("地图未指定").append(";");
        }
        //机器人是否禁用
        if (VehicleConstant.DISABLE.equals(vehicle.getStatus())) {
            flag = false;
            sb.append("机器人已禁用").append(";");
        }
        //机器人是否异常
        if (!VehicleConstant.ABNORMAL_STATUS_NO.equals(vehicle.getAbnormalStatus())) {
            flag = false;
            sb.append("机器人状态异常").append(";");
        }
        //机器人是否在线
        if (VehicleConstant.OUTLINE.equals(vehicle.getOnlineStatus())) {
            flag = false;
            sb.append("机器人不在线").append(";");
        }
        //机器人是否在充电
        if (VehicleConstant.OUTLINE.equals(vehicle.getWorkStatus())) {
            flag = false;
            sb.append("机器人在充电中").append(";");
        }
        //机器人电量
        if (vehicle.getDefaultVehicleStatus().getBattery().getBattery_value() <= 20) {
            flag = false;
            sb.append("机器人处于低电量状态").append(";");
        }
        if (!flag) {
            throw new ExecuteException("机器人不满足执行动作状态：" + sb.toString());
        }
    }

    @Override
    public void handleForeignActionResult(ForeignActionResultMessage foreignActionResultMessage) {
        ForeignActionCallBackParam foreignActionCallBackParam = new ForeignActionCallBackParam();

        //更新动作执行结果
        String actionId = foreignActionResultMessage.getActionId();
        ForeignAction foreignAction = this.selectById(actionId);
        if (foreignAction == null) {
            logger.error("actionId:{}找不到对应foreignAction", actionId);
            foreignActionCallBackParam.setActionId(actionId);
            foreignActionCallBackParam.setResultCode(1002);
            foreignActionCallBackParam.setResultData("actionId:" + actionId + "找不到对应foreignAction");
        } else {
            foreignAction.setStatus(foreignActionResultMessage.getStatus());
            foreignAction.setResultCode(foreignActionResultMessage.getResultCode());
            foreignAction.setResultData(foreignActionResultMessage.getResultData());
            foreignAction.setStartTime(foreignActionResultMessage.getStartTime());
            foreignAction.setEndTime(foreignActionResultMessage.getEndTime());
            this.update(foreignAction);
            //回调
            foreignActionCallBackParam.setActionId(actionId);
            foreignActionCallBackParam.setAgvCode(foreignAction.getAgvCode());
            foreignActionCallBackParam.setActionType(foreignAction.getActionType());
            foreignActionCallBackParam.setActionParameter(JSON.parseObject(foreignAction.getParameters()));
            foreignActionCallBackParam.setCallBackUrl(foreignAction.getCallBackUrl());
            foreignActionCallBackParam.setResultCode(foreignAction.getResultCode());
            foreignActionCallBackParam.setResultData(foreignAction.getResultData());
            foreignActionCallBackParam.setStartTime(foreignAction.getStartTime());
            foreignActionCallBackParam.setEndTime(foreignAction.getEndTime());
        }
        try {
            httpClientService.doPost(foreignAction.getCallBackUrl(), foreignActionCallBackParam);
        } catch (Exception e) {
            logger.error("foreign action call back url error. url:{},errorMsg:{}", foreignAction.getCallBackUrl(), e.getMessage());
        }
    }

    @Override
    public void broadcastForeignAction(String agvCode) {
        if (!shouldPlay(agvCode)) return;
        SchedulerConfig schedulerConfig = configService.selectSchedulerConfig();
        ForeignActionRequestParam foreignActionRequestParam = new ForeignActionRequestParam();
        foreignActionRequestParam.setActionType( ACTION_TYPE_AUDIO_PLAYBACK);
        foreignActionRequestParam.setAgvCode(agvCode);
        JSONObject obj = new JSONObject();
        obj.put( KEY_MUSIC_FILE_NAME ,schedulerConfig.getVoicePlayback());
        obj.put( KEY_MUSIC_TIMES, KEY_MUSIC_TIMES_VALUE );
        obj.put( KEY_PLAY_TRIGGER , KEY_PLAY_TRIGGER_VALUE );
        obj.put( "audioVolume" , 100 );
        /**
         *   Map<String, Object> dataMap = new HashMap<>();
         *         dataMap.put("id", id);
         *         dataMap.put("playTrigger", playTrigger);
         *         dataMap.put("audioVolume", audioConfiguration.getAudioVolume());
         *         dataMap.put("musicName", audioConfiguration.getMusicName());
         *         dataMap.put("playTime", audioConfiguration.getPlayTime());
         */
        foreignActionRequestParam.setActionParameter( obj);
        this.insertForeignAction(foreignActionRequestParam);
       logger.info("agvCode:{} ,广播音乐播放动作:{}",agvCode, foreignActionRequestParam);
    }

    @Override
    public boolean shouldPlay(String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if(Objects.nonNull( vehicle) && BooleanUtils.isTrue( vehicle.isPlayStationMusic())){
            return true;
        }
        return false;
    }

    @Override
    public String insertForeignAction(ForeignActionRequestParam foreignActionRequestParam) {
        String agvCode = foreignActionRequestParam.getAgvCode();
        String actionType = foreignActionRequestParam.getActionType();
        String actionParameter = foreignActionRequestParam.getActionParameter().toJSONString();
        String callBackUrl = foreignActionRequestParam.getCallBackUrl();

        ForeignAction foreignAction = new ForeignAction();
        foreignAction.setAgvCode(agvCode);
        foreignAction.setActionType(actionType);
        foreignAction.setParameters(actionParameter);
        foreignAction.setCallBackUrl(callBackUrl);
        this.insert(foreignAction);

        ForeignActionMessage foreignActionMessage = new ForeignActionMessage(foreignAction.getId(), actionType, actionParameter, callBackUrl);
        MqttUtils.sendMqttMsg(MqttConstant.FOREIGN_ACTION_TOPIC, agvCode, foreignActionMessage);

        return foreignAction.getId();
    }


}
