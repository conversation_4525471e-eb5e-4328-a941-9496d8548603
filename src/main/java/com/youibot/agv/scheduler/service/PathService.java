package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.entity.vo.PathVo;

import java.util.List;
import java.util.Map;

public interface PathService {

    List<Path> selectByAGVMapId(String agvMapId, boolean isDraft);

    List<Path> selectByAGVMapIds(List<String> agvMapIds, boolean isDraft);

    /**
     * 根据地图id和使用状态查询路径列表
     * @param agvMapId
     * @param usageStatus
     * @param isDraft
     * @return
     */
    List<Path> selectByAGVMapIdAndUsageStatus(String agvMapId, String usageStatus, boolean isDraft);

    Map<String, List<Path>> mapByAgvMapId(List<String> agvMapList, boolean isDraft);

    /**
     * 根据地图id和 结束点和起始点查询路径列表
     * @param startId
     * @param endId
     * @param agvMapId
     * @param isDraft
     * @return
     */
    List<Path> selectByAgvMapIdAndStartIdOrEndId(String startId, String endId, String agvMapId, boolean isDraft);

    void deleteByAgvMapIds(List<String> agvMapIdList);

    List<SidePath> createSidePathsByPathVo(PathVo pathVo,Marker startMarker,Marker endMarker);

    List<Path> selectByAutoDoorIds(List<String> autoDoorIds);

    List<Path> selectByAutoDoorId(String autoDoorId);

    List<SidePath> createSidePathsV2(Path path, Marker startMarker, Marker endMarker);

    /**
     * 根据标记点id查询指定地图中结束点或起始点为id值的路径列表
     * @param agvMapName
     * @param id
     * @param isDraft
     * @return
     */
    List<Path> selectByAgvMapIdMarkerId(String id,String agvMapName,boolean isDraft);

    void deleteByAGVMapId(String agvMapId);

    Path insert(Path path);

    void delete(String agvMapName, String id);

    PathVo update(PathVo pathVo);

    List<PathVo> searchAll(Map<String, String> searchMap, boolean isDraft);

    /**
     * 检查路径导航类型
     * @param path
     * @param isDraft
     */
    void checkPathNavigationType(Path path, boolean isDraft);

    Path selectById(String agvMapName, String id, boolean isDraft);

	Map<String, List<Path>> mapByAgvMapName(List<String> agvMapList, boolean b);
}
