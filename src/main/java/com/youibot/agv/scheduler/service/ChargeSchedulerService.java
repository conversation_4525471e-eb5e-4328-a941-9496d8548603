package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.ChargeScheduler;
import com.youibot.agv.scheduler.entity.WorkScheduler;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 14:43
 */
public interface ChargeSchedulerService extends BaseService<ChargeScheduler> {

    List<ChargeScheduler> selectCreate();

    List<ChargeScheduler> selectRunning();

    ChargeScheduler selectRunningByVehicle(String vehicleId);

    ChargeScheduler selectRunningByChargeId(String chargeId);

    void updateCancel(Long id);

    void updateCancel(  );
    
    void updateSuccess(Long id);

    void updateStart(Long id);

    void updateFault(Long id, String faultMessage);

    /**
     * 查询timeDiff秒内取消充电的机器人
     * @param timeDiff
     * @return
     */
    List<String> selectCancelByFinishTimeDiff(String timeDiff);

    ChargeScheduler selectLastOne(String vehicleId);

    /**
     * 获取agv最近一次成功的校正充电调度
     * 如果第一次进行校正充电，则返回null
     */
    ChargeScheduler selectLatestSuccessCorrectCharge(String vehicleId);

    /**
     * 获取agv第一次成功的普通充电调度
     */
    ChargeScheduler selectFirstSuccessCommonCharge(String vehicleId);

    /**
     * 获取正在进行的校正充电调度
     */
    List<ChargeScheduler> selectRunningCorrectCharge();

    void deleteExpireDataByTime(Integer timeInterval);
}
