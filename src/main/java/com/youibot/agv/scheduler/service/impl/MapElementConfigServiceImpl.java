package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.MapElementConfig;
import com.youibot.agv.scheduler.service.MapElementConfigService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

@Service
public class MapElementConfigServiceImpl extends BaseServiceImpl<MapElementConfig> implements MapElementConfigService {

    @Override
    public MapElementConfig selectByMapName(String mapName) {
        Example example = new Example(MapElementConfig.class);
        example.createCriteria().andEqualTo("mapName",mapName);
        return super.selectOneByExample(example);
    }
}
