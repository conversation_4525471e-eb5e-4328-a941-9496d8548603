package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.Sensor;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/4/13 15:43
 * @Description:
 */
public interface SensorService extends BaseService<Sensor> {
    Sensor detailById(String id);

    String createSensor(Sensor sensor);

    /**
     * 更新传感器详细内容
     *
     * @param sensor 需更新的传感器内容的详细信息
     */
    void updateSensor(Sensor sensor);
}
