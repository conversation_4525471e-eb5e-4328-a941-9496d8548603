package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.AGVMapUpdateQueue;
import com.youibot.agv.scheduler.entity.Floor;
import com.youibot.agv.scheduler.mapper.AGVMapUpdateQueueMapper;
import com.youibot.agv.scheduler.service.AGVMapUpdateQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

@Slf4j
@Service
public class AGVMapUpdateQueueServiceImpl extends BaseServiceImpl<AGVMapUpdateQueue> implements AGVMapUpdateQueueService {



    @Autowired
    private AGVMapUpdateQueueMapper agvMapUpdateQueueMapper;



    @Override
    public AGVMapUpdateQueue selectById(String id) {
        Example example = new Example(Floor.class);
        example.createCriteria().andEqualTo("id", id);
        return super.selectOneByExample(example);
    }

    @Override
    public Integer insert(AGVMapUpdateQueue log) {
        Integer result = super.insert(log);
        return result;
    }

    @Override
    public Integer update(AGVMapUpdateQueue log) {
        Integer result = super.update(log);
        return result;
    }

    @Override
    public AGVMapUpdateQueue selectLastestOne() {
        return agvMapUpdateQueueMapper.selectLastestOne();
    }

    @Override
    public void deleteAllByDay(int day) {
        agvMapUpdateQueueMapper.deleteAllByDay(day);
    }

}
