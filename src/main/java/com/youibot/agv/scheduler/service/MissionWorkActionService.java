package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;

import java.util.List;
import java.util.Map;

public interface MissionWorkActionService extends BaseService<MissionWorkAction> {

    int updateStatus(MissionWorkAction missionWorkAction);

    void updateStatus(MissionWorkAction missionWorkAction, String status);

    MissionWorkAction createMissionWorkAction(String id, MissionAction missionAction, MissionWork missionWork);

    MissionWorkAction selectByMissionWorkIdAndStatus(String missionWorkId,String status);
    /**
     * 获取最后执行完成的任务动作
     *
     * @return
     */
    MissionWorkAction selectLastOne();

    MissionWorkAction selectByMissionActionIdAndMissionWorkId(String missionActionId, String missionWorkId);

    List<MissionWorkAction> selectByMissionWorkIdAndType(String missionWorkId, String actionType);

    /**
     * 保存返回正确的信息
     *
     * @param missionWorkAction
     * @param resultDataMap
     */
    void saveSuccessResult(MissionWorkAction missionWorkAction, Map<String, Object> resultDataMap);

    /**
     * 保存返回正确的信息
     *
     * @param missionWorkAction
     * @param resultType
     * @param resultData
     */
    void saveSuccessResult(MissionWorkAction missionWorkAction, String resultType, String resultData);

    List<MissionWorkAction> selectByMissionWorkId(String missionWorkId);

    /**
     * 根据时间设置删除过期数据
     * @param missionWork
     */
    void deleteExpireDataByTime(Integer missionWork);

    MissionWorkAction selectLastByMissionWorkId(String missionWorkId);
}
