package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.constant.vo.MissionActionStatisticVO;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;

import java.util.List;

public interface MissionWorkActionService extends BaseService<MissionWorkAction> {

    int updateStatus(MissionWorkAction missionWorkAction);

    void updateStatus(MissionWorkAction missionWorkAction, String status);

    MissionWorkAction createMissionWorkAction(MissionAction missionAction, MissionWork missionWork);

    List<MissionWorkAction> selectByMissionWorkIdAndType(String missionWorkId, String actionType);

    List<MissionWorkAction> selectByMissionWorkId(String missionWorkId);

    /**
     * 根据missionWorkId查询运行中的动作
     * @param missionWorkId
     * @return
     */
    MissionWorkAction selectRunningActionByMissionWorkId(String missionWorkId);

    /**
     * 根据时间设置删除过期数据
     * @param missionWork
     */
    void deleteExpireDataByTime(Integer missionWork);

    /**
     * 数据库中存在，则更新。不存在，则新增
     * @param missionWorkAction
     */
    void mergeUpdateAndInsert(MissionWorkAction missionWorkAction);

    /**
     * 根据missionWorkId更新动作列表中未完成的任务状态
     * @param missionWorkId
     * @param status
     */
    void updateMissionWorkActionStatus(String missionWorkId, String status);

    /**
     * 查询某个时间范围内，任务动作统计信息
     *
     * 如果小车编码存在，就查询单个小车的
     * 否则，就查询所有小车的
     */
    List<MissionActionStatisticVO> getMissionActionStatistic(String agvCode, String startTime, String endTime);
}
