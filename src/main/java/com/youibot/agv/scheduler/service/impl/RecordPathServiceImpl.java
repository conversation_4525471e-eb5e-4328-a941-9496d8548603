package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.RecordPath;
import com.youibot.agv.scheduler.service.RecordPathService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class RecordPathServiceImpl extends BaseServiceImpl<RecordPath> implements RecordPathService {

	@Override
	public List<RecordPath> selectByAGVMapId(String agvMapId) {
		Example example = new Example(RecordPath.class);
        example.createCriteria().andEqualTo("agvMapId", agvMapId);
        return super.selectByExample(example);
	}

	@Override
	public List<RecordPath> selectByAGVMapIds(List<String> agvMapIds) {
		Example example = new Example(RecordPath.class);
		example.createCriteria().andIn("agvMapId", agvMapIds);
		return super.selectByExample(example);
	}

}
