package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.constant.vo.TriggerSelectorDetailVO;
import com.youibot.agv.scheduler.entity.TriggerSelector;

import java.util.List;
import java.util.Map;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/1/7 17:49
 * @Description:
 */
public interface TriggerSelectorService extends BaseService<TriggerSelector> {

    /**
     * 创建触发器
     *
     * @param triggerSelector
     * @return triggerSelectorId
     */
    String create(TriggerSelector triggerSelector);

    /**
     * 编辑修改触发器
     *
     * @param id
     * @param triggerSelector
     */
    void updateTriggerSelector(String id, TriggerSelector triggerSelector);

    void deleteTrigger(String id);

    /**
     * 检索触发器下所有的任务列表
     *
     * @param id
     * @return
     */
    TriggerSelectorDetailVO findMissionWorkById(String id, Map<String, String> searchMap);

    List<TriggerSelector> selectByDeviceAddress(Integer deviceAddress);

    /**
     * 根据呼叫器ID查询
     *
     * @param pagerId 呼叫器ID
     * @return
     */
    List<TriggerSelector> selectByPagerId(String pagerId);

    /**
     * 根据触发器类型查询
     *
     * @param type
     * @return
     */
    List<TriggerSelector> selectByTriggerType(int type);


    List<TriggerSelector> selectByTriggerTypeAndDeviceAddress(int type, Integer deviceAddress);
}
