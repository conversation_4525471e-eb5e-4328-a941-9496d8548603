package com.youibot.agv.scheduler.service;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionCall;

import java.util.Map;

public interface MissionCallService extends BaseService<MissionCall>{

    PageInfo<MissionCall> selectPage(Map<String, String> searchMap);

    MissionCall saveMissionCall(MissionCall missionCall);

    MissionCall updateMissionCall(MissionCall missionCall);

    MissionCall reset(String id);


}
