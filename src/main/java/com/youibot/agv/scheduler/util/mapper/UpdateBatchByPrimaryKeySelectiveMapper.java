package com.youibot.agv.scheduler.util.mapper;

import org.apache.ibatis.annotations.UpdateProvider;
import tk.mybatis.mapper.annotation.RegisterMapper;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月23日 下午2:40:13
 */
@RegisterMapper
public interface UpdateBatchByPrimaryKeySelectiveMapper<T> {
	
	/**
     * 根据Example条件批量更新实体`record`包含的不是null的属性值
     *
     * @return
     */
    @UpdateProvider(type = BatchExampleProvider.class, method = "dynamicSQL")
    int updateBatchByPrimaryKeySelective(List<? extends T> recordList);
}
