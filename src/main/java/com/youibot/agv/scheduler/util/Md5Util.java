package com.youibot.agv.scheduler.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月11日 下午1:46:23
 */
public class Md5Util {

	/*
	 * 获取MD5加盐加密后的字符串
	 * 
	 * @param pwd
	 * 
	 * @param salt
	 * 
	 * @Return
	 */
	public static String pwdMD5WithSalt(String pwd, String salt) {
		String pwdMd5 = stringMD5(pwd);
		String pwdMd5Withsalt = stringMD5(pwdMd5 + salt);
		return pwdMd5Withsalt;
	}

	private static String stringMD5(String pwd) {

		try {
			// 拿到一个MD5转换器
			MessageDigest messageDigest = MessageDigest.getInstance("MD5");
			// 输入的字符串转换成字节数组
			byte[] inputByteArray = pwd.getBytes();
			messageDigest.update(inputByteArray);
			byte[] resultByteArray = messageDigest.digest();
			// 字符数组转化为字符串返回
			return byteArrayToHex(resultByteArray).toLowerCase();
		} catch (NoSuchAlgorithmException e) {
			return null;
		}
	}

	private static String byteArrayToHex(byte[] byteArray) {
		// 首先初始化一个字符串，可以用来存放数字+字母，密码组成
		char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
		// 存放最后的密码
		char[] resultCharArray = new char[byteArray.length * 2];
		int index = 0;
		for (byte b : byteArray) {
			resultCharArray[index++] = hexDigits[b >>> 4 & 0xf];
			resultCharArray[index++] = hexDigits[b & 0xf];
		}
		return new String(resultCharArray);
	}

}
