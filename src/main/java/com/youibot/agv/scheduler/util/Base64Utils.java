package com.youibot.agv.scheduler.util;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.*;

public class Base64Utils {

    private static final Logger LOGGER = LoggerFactory.getLogger(Base64Utils.class);

    /**
     * 将图片转换成Base64编码
     *
     * @param imagePath 待处理图片路径
     */
    public static String imageToBase64(String imagePath) {
        if (StringUtils.isEmpty(imagePath)) {
            LOGGER.warn("imagePath is empty");
            return null;
        }
        //将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        InputStream in = null;
        byte[] data;
        //读取图片字节数组
        try {
            in = new FileInputStream(imagePath);
            data = new byte[in.available()];
            in.read(data);
            return new String(Base64.encodeBase64(data));
        } catch (IOException e) {
            LOGGER.error("image to base64 error");
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    LOGGER.error("in close error");
                }
            }
        }
        return null;
    }

    /**
     * 对字节数组字符串进行Base64解码并生成图片
     *
     * @param base64    图片base64数据
     * @param imageFolderPath 图片存储的文件夹路径
     * @param imageName 图片名称
     */
    public static boolean base64ToImage(String base64, String imageFolderPath, String imageName) {
        if (StringUtils.isEmpty(base64) || StringUtils.isEmpty(imageFolderPath) || StringUtils.isEmpty(imageName)) {
            LOGGER.warn("base64 or imageFolderPath or imageName is empty");
            return false;
        }
        OutputStream out = null;
        try {
            File file = new File(imageFolderPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            //Base64解码
            base64 = base64.replace("data:image/png;base64,", "");
            base64 = base64.replace("data:image/jpeg;base64,", "");
            base64 = base64.replace("data:image/jpg;base64,", "");
            byte[] b = Base64.decodeBase64(base64);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {//调整异常数据
                    b[i] += 256;
                }
            }
            //生成图片
            out = new FileOutputStream(imageFolderPath + File.separator + imageName);
            out.write(b);
            out.flush();
            return true;
        } catch (Exception e) {
            LOGGER.error("base64 to image error");
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    LOGGER.error("out close error");
                }
            }
        }
        return false;
    }

}