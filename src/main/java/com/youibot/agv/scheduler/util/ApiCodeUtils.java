package com.youibot.agv.scheduler.util;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/2 19:36
 */
public class ApiCodeUtils {

    public static String getMoveDocking() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.MOVE_DOCKING");
    }

    public static String getMoveSidePath() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.MOVE_SIDE_PATH");
    }

    public static String getMoveFreeNavigation() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.MOVE_FREE_NAVIGATION");
    }

    public static String getMoveRelative() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.MOVE_RELATIVE");
    }

    public static String getMoveRotate() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.MOVE_ROTATE");
    }

    public static String getBatteryCharge() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.BATTERY_CHARGE");
    }

    public static String getLeaveDocking() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.LEAVE_DOCKING");
    }

    public static String getRollerLeftIn() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.ROLLER_LEFT_IN");
    }

    public static String getRollerLeftOut() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.ROLLER_LEFT_OUT");
    }

    public static String getRollerRightIn() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.ROLLER_RIGHT_IN");
    }

    public static String getRollerRightOut() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.ROLLER_RIGHT_OUT");
    }

    public static String getRollerUpOrDown() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.ROLLER_UP_DOWN");
    }

    public static String getClamps() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.CLAMPS_OPERATE");
    }

    public static String getHumanDisinfectControl() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.HUMAN_DISINFECT");
    }

    public static String getAgvShelfAngle() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.AGV_SHELF_ANGLE");
    }

    /**
     * 获取可停止action的apiCode列表
     * @return
     */
    public static List<String> getCanStopActionCode() {
        List<String> list = new ArrayList<>();
        list.add(ApiCodeUtils.getMoveDocking());
        list.add(ApiCodeUtils.getBatteryCharge());
        list.add(ApiCodeUtils.getMoveSidePath());
        list.add(ApiCodeUtils.getMoveRelative());
        list.add(ApiCodeUtils.getMoveRotate());
        list.add(ApiCodeUtils.getMoveFreeNavigation());
        list.add(ApiCodeUtils.getRollerLeftIn());
        list.add(ApiCodeUtils.getRollerLeftOut());
        list.add(ApiCodeUtils.getRollerRightIn());
        list.add(ApiCodeUtils.getRollerRightOut());
        list.add(ApiCodeUtils.getClamps());
        list.add(ApiCodeUtils.getHumanDisinfectControl());
        list.add(ApiCodeUtils.getAgvShelfAngle());
//        list.add(ApiCodeUtils.getRollerUpOrDown());
        return list;
    }

    /**
     * 获取可暂停action的apiCode列表
     * @return
     */
    public static List<String> getCanPauseActionCode() {
        List<String> list = new ArrayList<>();
        list.add(ApiCodeUtils.getMoveSidePath());
        list.add(ApiCodeUtils.getMoveRelative());
        list.add(ApiCodeUtils.getMoveRotate());
        list.add(ApiCodeUtils.getRollerLeftIn());
        list.add(ApiCodeUtils.getRollerLeftOut());
        list.add(ApiCodeUtils.getRollerRightIn());
        list.add(ApiCodeUtils.getRollerRightOut());
        list.add(ApiCodeUtils.getClamps());
        list.add(ApiCodeUtils.getAgvShelfAngle());
        return list;
    }
}
