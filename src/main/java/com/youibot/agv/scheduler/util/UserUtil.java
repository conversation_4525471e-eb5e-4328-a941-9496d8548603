package com.youibot.agv.scheduler.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.youibot.agv.scheduler.entity.User;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

public class UserUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserUtil.class);

    public static User getUser(HttpServletRequest request, UserService userService) {
        String token = getToken(request);
        if (StringUtils.isEmpty(token)) {
            throw new ExecuteException(MessageUtils.getMessage("http.token_is_null"));
        }
        String userId;
        try {
            userId = JWT.decode(token).getAudience().get(0);
        } catch (JWTDecodeException exception) {
            LOGGER.error("Token format error: token" + token);
            throw new ExecuteException(MessageUtils.getMessage("http.token_format_error"));
        }
        User user = userService.selectById(userId);
        if (StringUtils.isEmpty(user)) {
            throw new ExecuteException(MessageUtils.getMessage("service.user_is_null"));
        }
        return user;
    }

    /**
     * 获取token
     *
     * @param request
     * @return
     */
    public static String getToken(HttpServletRequest request) {
        String token = request.getHeader(JWTUtil.HTTP_REQUEST_TOKEN);
//        token = getCookieByName(request, JWTUtil.HTTP_REQUEST_TOKEN);
        if (StringUtils.isEmpty(token)) {
            token = request.getParameter(JWTUtil.HTTP_REQUEST_TOKEN);
        }
        if (StringUtils.isEmpty(token)) {
            LOGGER.error("There is no token, please login first");
            return null;
        }
        return token;
    }
}
