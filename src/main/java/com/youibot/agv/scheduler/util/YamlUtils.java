package com.youibot.agv.scheduler.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.yaml.snakeyaml.Yaml;

import java.util.Map;

/**
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2020/3/3 19:27
 */
public class YamlUtils {

    public static JSONObject yamlToJson(String yamlStr) {
        Yaml yaml = new Yaml();
        Map<String, Object> map = yaml.load(yamlStr);
        return JSONObject.parseObject(JSON.toJSONString(map));
    }

    public static String jsonToYaml(JSONObject jsonObject) {
        Yaml yaml = new Yaml();
        return yaml.dumpAsMap(jsonObject);
    }

}
