package com.youibot.agv.scheduler.util;

import com.youibot.agv.scheduler.entity.Dashboard;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/5 19:36
 */
public class BeanUtils {

    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        org.springframework.beans.BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    public static<T> T setBean(T t, String fieldName, String value){
        if (StringUtils.isEmpty(fieldName) || StringUtils.isEmpty(value)){
            return t;
        }
        try {
            Class<?> aClass = t.getClass();
            Field field = aClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(t, value);
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return t;
    }

    public static void main(String[] args) throws ClassNotFoundException, NoSuchFieldException, IllegalAccessException {
        Dashboard dashboard = new Dashboard();
        Class<?> aClass = dashboard.getClass();
        Field agvMapId = aClass.getDeclaredField("agvMapId");
        agvMapId.setAccessible(true);
        agvMapId.set(dashboard, "1");
        System.out.println(dashboard.getAgvMapId());
    }

}