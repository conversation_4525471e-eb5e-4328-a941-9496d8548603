package com.youibot.agv.scheduler.util;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.SystemConfig;
import com.youibot.agv.scheduler.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class SystemConfigJobUtil {

    static Logger logger = LoggerFactory.getLogger(SystemConfigJobUtil.class);

    private static Integer day;

    private static SimpleDateFormat ymFormat = new SimpleDateFormat("yyyy-MM");

    private static SimpleDateFormat mdyFormat = new SimpleDateFormat("MM-dd-yyyy");

    public static void deleteMissionWorkLogs(SystemConfigService systemConfigService, MissionWorkService missionWorkService,
                                             MissionWorkActionService missionWorkActionService, MissionWorkActionParameterService missionWorkActionParameterService, NotificationService notificationService,MqMessageService mqMessageService){
        logger.info("----------start MissionWorkLogJob job-----------");
        List<SystemConfig> all = systemConfigService.findAll();
        if (CollectionUtils.isEmpty(all)){
            return;
        }
        Integer missionWork = all.get(0).getMissionWork();
        //删除任务信息
        if (missionWork != null || missionWork > 0){
            logger.debug("system config missionWork day is : " + missionWork);
            missionWorkService.deleteExpireDataByTime(missionWork);
            missionWorkActionService.deleteExpireDataByTime(missionWork);
            missionWorkActionParameterService.deleteExpireDataByTime(missionWork);
        }
        //删除通知信息
        Integer notify = all.get(0).getSystemNotify();
        if (notify != null || notify > 0){
            logger.info("system config notify day is : " + notify);
            notificationService.deleteExpireDataByTime(notify);
//            mqMessageService.deleteExpireDataByTime(notify);
        }
        logger.info("----------end MissionWorkLogJob job-----------");
    }

    public static void deleteLogFile(SystemConfigService systemConfigService) {
        String logPath = AGVPropertiesUtils.getString("LOG_INFO.DIRECTOR_PATH");
        File logDir = new File(logPath);
        //设置删除的过期日期
        setExpireDay(systemConfigService);
        logger.debug("expire day is : " + day);
        try {
            //删除过期日志文件
            deleteLogFiles(logDir);
        } catch (ParseException e) {
            logger.error("delete logFile parse date fail");
            e.printStackTrace();
        }
    }

    private static void setExpireDay(SystemConfigService systemConfigService) {
        List<SystemConfig> all = systemConfigService.findAll();
        if (CollectionUtils.isEmpty(all)){
            day = -60;
        }else {
            day = - all.get(0).getLogFile();
        }
    }

    private static void deleteLogFiles(File logDir) throws ParseException {
        if (!logDir.exists() && !logDir.isDirectory()){
            logger.info("logDir is not exists or not director");
            return;
        }
        File[] files = logDir.listFiles();
        if (files.length == 0){
            logger.info("files is empty");
            return;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, day);
        long expireTime = calendar.getTime().getTime();
        for (File log: files) {//logs下的文件
            if (log.isDirectory() && ymFormat.parse(log.getName()).getTime() <= expireTime){
                logger.debug("logs files : " + log.getName());
                File[] logFiles = log.listFiles();
                for (File logFile: logFiles) {
                    String logFileName = logFile.getName();
                    String mdy = logFileName.substring(logFileName.indexOf("-", 4) + 1, logFileName.lastIndexOf("-"));
                    if (mdyFormat.parse(mdy).getTime() <= expireTime){
                        logger.debug("delete fileName : " + logFileName);
                        logFile.delete();
                    }
                }
                if (logFiles.length == 0){
                    logger.debug("delete dirName : " + log.getName());
                    log.delete();
                }
            }
        }
    }

    /**
     *
     * @param requestPathService
     * @param day 过期时间
     */
    public static void deleteRequestPathData(RequestPathService requestPathService,Integer day) {
        requestPathService.deleteRequestPathData(day);



    }
}
