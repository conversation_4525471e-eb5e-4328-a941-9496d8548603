package com.youibot.agv.scheduler.util;

import com.youibot.agv.scheduler.entity.DataBaseDate;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具
 *
 * @Author：yangpeilin
 * @Date: 2020/5/26 20:20
 */
public class DateUtils {

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(DateUtils.class);

    public static void main(String[] args) {
        DataBaseDate dataBaseDate = getDataBaseDate(new Date());
        System.out.println(dataBaseDate);
        System.out.println(new Date(new Date().getTime() + 1000 * 60 * 60 * 24));
    }

    public static final long DAY = 1000 * 60 * 60 * 24;

    public static DataBaseDate getDataBaseDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String format = sdf.format(date);
        DataBaseDate dataBaseDate = null;
        try {
            Date parse = sdf.parse(format);
            dataBaseDate = new DataBaseDate();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(parse.getTime() - 1000));
            calendar.add(Calendar.DAY_OF_MONTH, +1);
            dataBaseDate.setCurrentDay(calendar.getTime());
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - 24);
            dataBaseDate.setYesterday(calendar.getTime());
            dataBaseDate.setStartWeekDay(getFirstDayOfWeek(calendar.getTime()));
            dataBaseDate.setEndWeekDay(getLastDayOfWeek(calendar.getTime()));
            dataBaseDate.setStartMonthDay(getFirstDayDateOfMonth(calendar.getTime()));
            dataBaseDate.setEndMonthDay(getLastDayOfMonth(calendar.getTime()));
        } catch (ParseException e) {
            LOGGER.error("获取数据库日期数据出错, ", e);
        }
        return dataBaseDate;
    }

    //日期所在周的最后一天
    public static Date getLastDayOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_WEEK,
                calendar.getMinimalDaysInFirstWeek()); // +6 Sunday
        //再加一天
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 24);
        return calendar.getTime();
    }

    //日期所在周的第一天
    public static Date getFirstDayOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_WEEK,
                calendar.getFirstDayOfWeek()); // MonSunday
        return calendar.getTime();
    }

    //日期所在月的第一天
    public static Date getFirstDayDateOfMonth(final Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int last = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, last);
        return cal.getTime();
    }

    //日期所在月的最后一天
    public static Date getLastDayOfMonth(final Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int last = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, last);
        //再加一天
        cal.set(Calendar.HOUR, cal.get(Calendar.HOUR) + 24);
        return cal.getTime();

    }


    // 获取当前时间的前后几天
    public static String getBeforeDay(Integer day) {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date date=new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        date = calendar.getTime();
        return sdf.format(date);
    }
}
