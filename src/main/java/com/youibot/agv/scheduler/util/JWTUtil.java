package com.youibot.agv.scheduler.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.youibot.agv.scheduler.entity.User;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


public class JWTUtil {

	// JWT HEADER
	public static final String HEADER_ALGORITHM_NAME = "alg";
	public static final String HEADER_ALGORITHM_VALUE = "HS256";
	public static final String HEADER_TOKEN_NAME = "typ";
	public static final String HEADER_TOKEN_VALUE = "JWT";
	// 签名发行者
	public static final String SIGN_ISSUER = "SERVICE";
	// token 相关
	public static final Integer TOKEN_EXPRIE_DAY = 1;
	public static final Integer TOKEN_EXPRIE_MINUTE = 1;
	public static final String  HTTP_REQUEST_TOKEN="Authorization";
	public static final String TOKEN_PREFIX="Bearer;"; //token的前缀

	// 创建token
	public static String createToken(User user) {

		// 密钥 通过密码获取
		String secret = user.getPassword();
		Algorithm algorithm = Algorithm.HMAC256(secret);
		// 头部消息
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(HEADER_ALGORITHM_NAME, HEADER_ALGORITHM_VALUE);
		map.put(HEADER_TOKEN_NAME, HEADER_TOKEN_VALUE);
		Date expireDate = getAfterDate(null, 0, 0, TOKEN_EXPRIE_DAY, 0, 0, 0);
		return JWT.create()
				.withHeader(map)
				.withIssuer(SIGN_ISSUER)
				//签名的观众 使用用户id
				.withAudience(user.getId())
				//token过期时间
				.withExpiresAt(expireDate)
				.sign(algorithm);

	}

	/*
	 * 返回一定时间后的日期
	 *
	 * @Param data 开始的时间
	 *
	 * @Param year 增加的年
	 *
	 * @Param month 增加的月
	 *
	 * @Param day 增加的天数
	 *
	 * @Param hour 增加的小时
	 *
	 * @Param minute 增加的分钟
	 *
	 * @Param second 增加的秒
	 */
	private static Date getAfterDate(Date date, int year, int month, int day, int hour, int minute, int second) {
		if (date == null) {
			date = new Date();
		}
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		if (year != 0) {
			cal.add(Calendar.YEAR, year);
		}
		if (month != 0) {
			cal.add(Calendar.MONTH, month);
		}
		if (day != 0) {
			cal.add(Calendar.DATE, day);
		}
		if (hour != 0) {
			cal.add(Calendar.HOUR_OF_DAY, hour);
		}
		if (minute != 0) {
			cal.add(Calendar.MINUTE, minute);
		}
		if (second != 0) {
			cal.add(Calendar.SECOND, second);
		}
		return cal.getTime();
	}

}
