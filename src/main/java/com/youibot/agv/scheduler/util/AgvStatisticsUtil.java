package com.youibot.agv.scheduler.util;

import com.youibot.agv.scheduler.constant.AGVConstant;
import com.youibot.agv.scheduler.entity.AGVStatistics;
import com.youibot.agv.scheduler.entity.DataBaseDate;
import com.youibot.agv.scheduler.service.AGVLogService;
import com.youibot.agv.scheduler.service.AGVStatisticsService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.DefaultVehiclePool;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * @Author：yangpeilin
 * @Date: 2020/7/24 16:34
 */
public class AgvStatisticsUtil {

    private static Logger logger = LoggerFactory.getLogger(AgvStatisticsUtil.class);

    private static AGVStatisticsService agvStatisticsService = (AGVStatisticsService) ApplicationUtils.getBean("AGVStatisticsServiceImpl");
    private static MissionWorkService missionWorkService = (MissionWorkService) ApplicationUtils.getBean("missionWorkServiceImpl");
    private static AGVLogService agvLogService = (AGVLogService) ApplicationUtils.getBean("AGVLogServiceImpl");
    private static VehiclePool vehiclePool = (DefaultVehiclePool) ApplicationUtils.getBean("defaultVehiclePool");

    public static void statisticsDataByDate(Date date) {
        //查询 当前日期，当前日期所在周（周一到周一）、月（月初到月初）时间
        List<Vehicle> vehicleList = vehiclePool.getAll();
        DataBaseDate dataBaseDate = DateUtils.getDataBaseDate(date);
        logger.debug(dataBaseDate.toString());
        for(Vehicle vehicle :vehicleList){
            //获取昨天的统计数据
            String agvId =vehicle.getId();
            AGVStatistics day = getDayAgvStatistics(dataBaseDate,agvId);
            day.setAgvId(agvId);
            //插入单位为day的数据1
            agvStatisticsService.insert(day);
            //week:插入单位为week的数据7
            insertDataByType(day, dataBaseDate.getTodayIsEndWeekDay(), dataBaseDate.getStartWeekDay(), dataBaseDate.getEndWeekDay(), AGVConstant.WEEK,agvId);
            //month:插入单位为month的数据30
            insertDataByType(day, dataBaseDate.getTodayIsEndMonthDay(), dataBaseDate.getStartMonthDay(), dataBaseDate.getEndMonthDay(), AGVConstant.MONTH,agvId);
            //统计所有 0
            insertDataToAll(day,agvId);
        }
    }

    /**
     * 所有数据
     *
     * @param day
     */
    private static void insertDataToAll(AGVStatistics day,String agvId) {
        AGVStatistics agvStatistics = agvStatisticsService.findDataByType(AGVConstant.ALL,agvId);
        if (agvStatistics == null) {
            agvStatistics = agvStatisticsService.sum(agvId);
            agvStatistics.setAgvId(agvId);
            agvStatisticsService.insert(agvStatistics);
        } else {
            agvStatistics.addTodayData(day);
            agvStatisticsService.update(agvStatistics);
        }
    }

    private static AGVStatistics getDayAgvStatistics(DataBaseDate dataBaseDate,String agvId) {
        //1.任务统计：总任务数，已完成任务数，取消任务数，工作时间。（根据任务日志进行统计）
        long start = dataBaseDate.getYesterday().getTime() / 1000;
        long end = dataBaseDate.getCurrentDay().getTime() / 1000;
        AGVStatistics day = missionWorkService.getAgvStatistics(start, end,agvId);
        day.setDataType(AGVConstant.DAY);
        //2.充电统计：充电总次数，充电总时间, 总在线时间, 空闲时间
        AGVStatistics agvLog = agvLogService.getAgvStatistics(start, end,agvId);
        day.setChargeCount(agvLog.getChargeCount());
        day.setChargeTime(agvLog.getChargeTime());
        day.setOnTime(agvLog.getOnTime());
        //空闲时间
        day.setFreeTime(day.getOnTime() - day.getWorkTime() - day.getChargeTime());
        day.setBelongTime(dataBaseDate.getCurrentDay().getTime());
        return day;
    }

    /**
     * 每日更新最新周或最新月的统计数据
     *
     * @param day
     * @param todayIsEndDay
     * @param startDay
     * @param endDay
     * @param dataType
     */
    private static void insertDataByType(AGVStatistics day, boolean todayIsEndDay, Date startDay, Date endDay, int dataType,String agvId) {
        //获取一周/月的一条周/月数据  startDay 上(月) 周(月) 第一天 到 本(下) 周(月) 第一天
        AGVStatistics agvStatistics = agvStatisticsService.findDataByCreateTimeAndType(startDay, endDay, dataType,agvId);
        if (todayIsEndDay || agvStatistics == null) {
            agvStatistics = new AGVStatistics(dataType);
            agvStatistics.setAgvId(agvId);
            agvStatisticsService.insert(agvStatistics);
        }
        agvStatistics.addTodayData(day);
        agvStatisticsService.update(agvStatistics);
    }

    /**
     * 初始统计数据
     */
    public static void initData() {
        Long dataByRecentDateOfDay = agvStatisticsService.findDataByRecentDateOfDay(AGVConstant.DAY);
        if (dataByRecentDateOfDay == null || dataByRecentDateOfDay == 0) {//没有数据，统计昨天的数据
            statisticsDataByDate(new Date(System.currentTimeMillis() - DateUtils.DAY));
        } else {//有数据
            long now = System.currentTimeMillis();
            dataByRecentDateOfDay += DateUtils.DAY;
            while (dataByRecentDateOfDay < now) {
                statisticsDataByDate(new Date(dataByRecentDateOfDay));
                dataByRecentDateOfDay += DateUtils.DAY;
            }
        }
    }
}
