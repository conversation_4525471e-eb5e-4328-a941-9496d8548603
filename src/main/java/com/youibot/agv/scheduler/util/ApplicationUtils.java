package com.youibot.agv.scheduler.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class ApplicationUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationUtils.applicationContext = applicationContext;
    }
    
    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }
    
    public static ApplicationContext getApplicationContext() {
    	return applicationContext;
    }
}
