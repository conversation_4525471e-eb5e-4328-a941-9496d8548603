package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionGroup;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.service.MissonGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("missionGroupControllerV3")
@RequestMapping(value = "/api/v3/missionGroups", produces = "application/json")
@Api(value = "任务组", tags = "任务组", description = "任务组管理功能")
public class MissionGroupController extends BaseController {
    @Autowired
    private MissonGroupService missonGroupService;

    @Autowired
    private MissionService missionService;

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionGroupController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionGroup> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missonGroupService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionGroup> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missonGroupService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "missionGroup", value = "任务组", required = true, dataType = "MissionGroup")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionGroup save(@RequestBody @Valid MissionGroup missionGroup) {
        this.missonGroupService.insert(missionGroup);
        LOGGER.debug(missionGroup.toString());
        return missionGroup;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "任务组ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionGroup get(@PathVariable("id") String id) {
        return missonGroupService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "小视图ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionGroup", value = "小视图", required = true, dataType = "MissionGroup")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionGroup update(@PathVariable("id") String id, @RequestBody @Valid MissionGroup missionGroup) {
        missionGroup.setId(id);
        this.missonGroupService.update(missionGroup);
        return this.missonGroupService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "任务组ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.missonGroupService.deleteById(id);
    }

    @ApiOperation(value = "查询组内的任务列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "任务组ID", paramType = "path", required = true, dataType = "String"),
            })
    @GetMapping(value = "/{id}/missions")
    @ResponseStatus(value = HttpStatus.OK)
    public List<Mission> getMissions(@PathVariable("id") String id) {
        Mission mission = new Mission();
        mission.setAgvGroupId(id);
        return missionService.findByEntity(mission);
    }

}
