package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.entity.AGVType;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.AGVTypeService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("agvTypeControllerV3")
@RequestMapping(value = "/api/v3/agvTypes", produces = "application/json")
@Api(value = "AGV类型", tags = "AGV类型", description = "系统初始化时，会自动创建不同的机器人类型，不同的机器人在执行相同的动作时，会有不同的工作机制。所以在创建机器人时需要选择正确的机器人类型。")
public class AGVTypeController extends BaseController {

    @Autowired
    private AGVTypeService agvTypeService;

    @Autowired
    private AGVService agvService;

    private static final Logger LOGGER = LoggerFactory.getLogger(AGVTypeController.class);

    // 获取agv类型列表
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AGVType> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvTypeService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<AGVType> getAll() {
        return agvTypeService.findAll();
    }

    // 创建agv类型信息
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "agvType", value = "机器人类型", required = true, dataType = "AGVType")
    @PostMapping
    @LogOperation
    @ResponseStatus(value = HttpStatus.CREATED)
    public AGVType save(@RequestBody @Valid AGVType agvType) {
        Integer count = agvTypeService.selectCountByCodeOrName(agvType.getCode(), agvType.getName(), "");
        if (count != null && count > 0) {
            throw new YOUIFleetException(MessageUtils.getMessage("service.agv_type_code_or_name_is_exist"));
        }
        this.agvTypeService.insert(agvType);
        return agvType;
    }

    // 获取agv类型信息 ById
    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "机器人类型ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVType get(@PathVariable("id") String id) {
        return agvTypeService.selectById(id);
    }

    @LogOperation
    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "机器人类型ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvType", value = "机器人类型", required = true, dataType = "AGVType")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVType update(@PathVariable("id") String id, @RequestBody @Valid AGVType agvType) {
        agvType.setId(id);
        Integer count = agvTypeService.selectCountByCodeOrName(agvType.getCode(), agvType.getName(), id);
        if (count != null && count > 0) {
            throw new YOUIFleetException(MessageUtils.getMessage("service.agv_type_code_or_name_is_exist"));
        }
        this.agvTypeService.update(agvType);
        return this.agvTypeService.selectById(id);
    }

    // 删除agv类型信息 ById
    @LogOperation
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "机器人类型ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.agvTypeService.deleteById(id);
    }

    // 获取该agv类型下的agv列表
    @ApiOperation(value = "根据类型查询AGV列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "机器人类型ID", paramType = "path", required = true, dataType = "String")})
    @GetMapping(value = "/{id}/agvs")
    @ResponseStatus(value = HttpStatus.OK)
    public List<Agv> list(@PathVariable("id") String id) {
        return agvService.selectByAgvType(id);
    }
}
