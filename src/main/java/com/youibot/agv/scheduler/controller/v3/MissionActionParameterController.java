package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionActionParameter;
import com.youibot.agv.scheduler.service.MissionActionParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月14日 下午6:10:44
 */
@RestController("missionActionParameterControllerV3")
@RequestMapping(value = "/api/v3/missionActionParameters", produces = "application/json")
@Api(value = "预设动作参数", tags = "预设动作参数", description = "预设动作在执行过程中，需要设置不同的参数。机器人会根据不同的参数执行实际动作。")
public class MissionActionParameterController {

    @Autowired
    private MissionActionParameterService missionActionParameterService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionActionParameter> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionActionParameterService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionActionParameter> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionActionParameterService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "missionActionParameter", value = "预设动作参数", required = true, dataType = "MissionActionParameter")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionActionParameter save(@RequestBody @Valid MissionActionParameter missionActionParameter) {
        this.missionActionParameterService.insert(missionActionParameter);
        return missionActionParameter;
    }

    @ApiOperation(value = "批量创建")
    @ApiImplicitParam(name = "missionActionParameters", value = "预设动作参数列表", required = true, dataType = "MissionActionParameter", allowMultiple = true)
    @PostMapping("/batch")
    @ResponseStatus(value = HttpStatus.CREATED)
    public void batchSave(@RequestBody @Valid MissionActionParameter[] missionActionParameters) {
        List<MissionActionParameter> missionActionParameterList = Arrays.asList(missionActionParameters);
        this.missionActionParameterService.batchInsert(missionActionParameterList);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "预设动作参数ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionActionParameter get(@PathVariable("id") String id) {
        return missionActionParameterService.selectById(id);
    }

    @ApiOperation(value = "更新", notes = "根据ID更新动作参数信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "预设动作参数ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionActionParameter", value = "动作参数", required = true, dataType = "MissionActionParameter")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionActionParameter update(@PathVariable("id") String id, @RequestBody @Valid MissionActionParameter missionActionParameter) {
        missionActionParameter.setId(id);
        this.missionActionParameterService.update(missionActionParameter);
        return this.missionActionParameterService.selectById(id);
    }

    @ApiOperation(value = "批量更新")
    @ApiImplicitParam(name = "missionActionParameters", value = "预设动作参数列表", required = true, dataType = "MissionActionParameter", allowMultiple = true)
    @PutMapping(value = "/batch")
    @ResponseStatus(value = HttpStatus.OK)
    public void updateBatch(@RequestBody @Valid MissionActionParameter[] missionActionParameters) {
        List<MissionActionParameter> missionActionParameterList = Arrays.asList(missionActionParameters);
        this.missionActionParameterService.batchUpdate(missionActionParameterList);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "预设动作参数ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.missionActionParameterService.deleteById(id);
    }

    @ApiOperation(value = "删除预设动作参数列表", notes = "根据ID删除预设动作参数列表")
    @ApiImplicitParam(name = "missionActionParameters", value = "预设动作参数列表", required = true, allowMultiple = true, dataType = "MissionActionParameter")
    @PostMapping("/deleteBatch")
    public Integer deleteBatch(@RequestBody @Valid MissionActionParameter[] missionActionParameters) {
        List<String> executeParamList = new ArrayList<>();
        for (MissionActionParameter missionActionParameter : missionActionParameters) {
            executeParamList.add(missionActionParameter.getId());
        }
        return this.missionActionParameterService.deleteByIds(executeParamList);
    }
}
