package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThread;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThreadPool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月29日 下午4:40:23
 */
@ApiIgnore
@RestController("systemControllerV3")
@RequestMapping(value = "/api/v3/systems", produces = "application/json")
@Api(value = "系统", tags = "系统")
public class SystemController {

    @Autowired
    private MissionWorkThreadPool missionWorkThreadPool;

    private static final Logger LOGGER = LoggerFactory.getLogger(SystemController.class);

    @ApiOperation(value = "系统暂停")
    @PostMapping("/controls/pauseAllAGV")
    @ResponseStatus(value = HttpStatus.OK)
    public void systemPause() throws InterruptedException {
        List<MissionWorkThread> missionWorkThreads = missionWorkThreadPool.getAll();
        if (missionWorkThreads == null || missionWorkThreads.isEmpty()) {
            LOGGER.warn("Not mission work thread.");
            return;
        }
        for (MissionWorkThread missionWorkThread : missionWorkThreads) {
            missionWorkThread.pauseWork();
        }
    }

    @ApiOperation(value = "系统恢复")
    @PostMapping("/controls/resumeAllAGV")
    @ResponseStatus(value = HttpStatus.OK)
    public void systemResume() throws InterruptedException {
        List<MissionWorkThread> missionWorkThreads = missionWorkThreadPool.getAll();
        if (missionWorkThreads == null || missionWorkThreads.isEmpty()) {
            LOGGER.warn("Not mission work thread.");
            return;
        }

        for (MissionWorkThread missionWorkThread : missionWorkThreads) {
            missionWorkThread.resumeWork();
        }
    }
}
