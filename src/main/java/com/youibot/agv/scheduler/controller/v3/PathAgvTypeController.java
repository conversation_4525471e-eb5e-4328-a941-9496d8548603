package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.AGVType;
import com.youibot.agv.scheduler.entity.PathAgvType;
import com.youibot.agv.scheduler.param.PathAgvTypeParam;
import com.youibot.agv.scheduler.service.AGVTypeService;
import com.youibot.agv.scheduler.service.PathAgvTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController("pathAgvTypeControllerV3")
@RequestMapping(value = "/api/v3/pathAgvTypes", produces = "application/json")
@Api(value = "路径关联的车辆类型", tags = "路径关联的车辆类型", description = "路径关联机器人类型")
public class PathAgvTypeController extends BaseController {

    @Autowired
    private PathAgvTypeService pathAgvTypeService;

    @Autowired
    private AGVTypeService agvTypeService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<PathAgvType> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return pathAgvTypeService.findPage(searchMap);
    }


    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "pathAgvType", value = "机器人类型", required = true, dataType = "AGVType")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public PathAgvType save(@RequestBody @Valid PathAgvType pathAgvType) {
        this.pathAgvTypeService.insert(pathAgvType);
        return pathAgvType;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "路径机器人类型ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public PathAgvType get(@PathVariable("id") String id) {
        return pathAgvTypeService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "路径机器人类型ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvType", value = "机器人类型", required = true, dataType = "AGVType")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public PathAgvType update(@PathVariable("id") String id, @RequestBody @Valid PathAgvType pathAgvType) {
        pathAgvType.setId(id);
        this.pathAgvTypeService.update(pathAgvType);
        return this.pathAgvTypeService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "路径机器人类型ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.pathAgvTypeService.deleteById(id);
    }

    @ApiOperation(value = "根据pathId类型查询机器人类型列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pathId", value = "路径ID", paramType = "path", required = true, dataType = "PathAgvTypeParam")})
    @GetMapping(value = "/{pathId}/agvTypes")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AGVType> list(@PathVariable("pathId") String pathId) {
        if (!StringUtils.isEmpty(pathId)){
            PathAgvType pathAgvType = pathAgvTypeService.selectByPathId(pathId);
            if (pathAgvType != null && !StringUtils.isEmpty(pathAgvType.getAgvTypeIds())){
                return agvTypeService.selectByIds(Arrays.asList(pathAgvType.getAgvTypeIds().split(",")));
            }
        }
        log.debug("参数pathId为空！");
        return Collections.emptyList();
    }

    @ApiOperation(value = "根据pathId类型修改机器人类型列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pathAgvTypeParam", value = "路径机器人类型参数", required = true, dataType = "PathAgvTypeParam")})
    @PutMapping(value = "/agvTypes")
    @ResponseStatus(value = HttpStatus.OK)
    public void updateAgvTypes(@RequestBody PathAgvTypeParam pathAgvTypeParam) {
        String pathId = pathAgvTypeParam.getPathId();
        if (!StringUtils.isEmpty(pathId)){
            log.debug("更新路径机器人类型参数，pathId:【{}】", pathId);
            PathAgvType pathAgvType = pathAgvTypeService.selectByPathId(pathId);
            if (pathAgvType == null){
                pathAgvType = new PathAgvType();
                pathAgvType.setPathId(pathId);
                pathAgvType.setAgvTypeIds(pathAgvTypeParam.getAgvTypeIds());
                pathAgvTypeService.insert(pathAgvType);
            }else {
                pathAgvType.setAgvTypeIds(pathAgvTypeParam.getAgvTypeIds());
                pathAgvTypeService.update(pathAgvType);
            }
        }
    }
}
