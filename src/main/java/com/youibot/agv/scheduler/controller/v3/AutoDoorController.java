package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.entity.AirShowerDoor;
import com.youibot.agv.scheduler.entity.AutoDoor;
import com.youibot.agv.scheduler.service.AutoDoorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.DeviceConstant.*;

@RestController("AutoDoorControllerV3")
@RequestMapping(value = "/api/v3/autoDoor", produces = "application/json")
@Api(value = "自动门", tags = "自动门", description = "自动门增删改查，手动开关门")
public class AutoDoorController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoDoorController.class);

    @Autowired
    private AutoDoorService autoDoorService;

    @ApiOperation(value = "自动门和风淋门列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<AutoDoor> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return autoDoorService.searchAll(searchMap);
    }

    @ApiOperation(value = "自动门分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/autoDoorPage")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AutoDoor> getAutoDoorPage(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        searchMap.put("type", DOOR_TYPE_AUTO);
        return autoDoorService.findPage(searchMap);
    }

    @ApiOperation(value = "自动门列表")
    @GetMapping("/autoDoorAll")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AutoDoor> getAutoDoorAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        searchMap.put("type", DOOR_TYPE_AUTO);
        return autoDoorService.searchAll(searchMap);
    }

    @ApiOperation(value = "风淋门分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/airShowerDoorPage")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AirShowerDoor> getAirShowerDoorPage(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        searchMap.put("type", DOOR_TYPE_AIR_SHOWER);
        searchMap.put("position", AIR_SHOWER_DOOR_POSITION_FRONT);
        PageInfo<AutoDoor> page = autoDoorService.findPage(searchMap);
        List<AutoDoor> airShowerDoors = page.getList();

        PageInfo<AirShowerDoor> airShowerDoorPageInfo = new PageInfo<>();
        Collection<String> excludes = new ArrayList<>();
        excludes.add("list");
        String[] excludeArr = excludes.toArray(new String[excludes.size()]);
        BeanUtils.copyProperties(airShowerDoors, airShowerDoorPageInfo, excludeArr);
        if (CollectionUtils.isEmpty(airShowerDoors)) {
            return airShowerDoorPageInfo;
        }
        List<AirShowerDoor> airShowerDoorList = autoDoorService.getAirShowerDoorListByFront(airShowerDoors);
        airShowerDoorPageInfo.setList(airShowerDoorList);
        return airShowerDoorPageInfo;
    }

    @ApiOperation(value = "风淋门列表")
    @GetMapping("/airShowerDoorAll")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AirShowerDoor> getAirShowerDoorAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        searchMap.put("type", DOOR_TYPE_AIR_SHOWER);
        searchMap.put("position", AIR_SHOWER_DOOR_POSITION_FRONT);
        List<AutoDoor> airShowerFrontDoors = autoDoorService.searchAll(searchMap);
        if (CollectionUtils.isEmpty(airShowerFrontDoors)) {
            return null;
        }
        List<AirShowerDoor> airShowerDoorList = autoDoorService.getAirShowerDoorListByFront(airShowerFrontDoors);
        return airShowerDoorList;
    }

    @LogOperation
    @ApiOperation(value = "创建自动门")
    @ApiImplicitParam(name = "autoDoor", value = "自动门", required = true, dataType = "AutoDoor")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AutoDoor save(@RequestBody @Valid AutoDoor autoDoor) {
        autoDoor.setType(DOOR_TYPE_AUTO);
        this.autoDoorService.insert(autoDoor);
        LOGGER.debug(autoDoor.toString());
        return autoDoor;
    }

    @ApiOperation(value = "自动门详情")
    @ApiImplicitParam(name = "id", value = "自动门id", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AutoDoor get(@PathVariable("id") String id) {
        return autoDoorService.selectById(id);
    }

    @LogOperation
    @ApiOperation(value = "更新自动门")
    @ApiImplicitParam(name = "autoDoor", value = "自动门", required = true, dataType = "AutoDoor")
    @PutMapping
    @ResponseStatus(value = HttpStatus.OK)
    public AutoDoor update(@RequestBody @Valid AutoDoor autoDoor) {
        this.autoDoorService.updateAutoDoor(autoDoor);
        return autoDoor;
    }

    @LogOperation
    @ApiOperation(value = "删除自动门/风淋门")
    @ApiImplicitParam(name = "id", value = "自动门ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        AutoDoor autoDoor = this.autoDoorService.selectById(id);
        if(DOOR_TYPE_AIR_SHOWER.equals(autoDoor.getType())){
            //删除风淋门
            this.autoDoorService.deleteById(id);
            this.autoDoorService.deleteById(autoDoor.getRelationDoorId());
        }
        //删除自动门
        this.autoDoorService.deleteById(id);
    }

    @LogOperation
    @ApiOperation(value = "开门自动门/风淋门")
    @ApiImplicitParam(name = "id", value = "自动门ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/open")
    @ResponseStatus(value = HttpStatus.OK)
    public void open(@PathVariable("id") String id) {
        this.autoDoorService.open(id);
    }

    @LogOperation
    @ApiOperation(value = "关门自动门/风淋门")
    @ApiImplicitParam(name = "id", value = "自动门ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/close")
    @ResponseStatus(value = HttpStatus.OK)
    public void close(@PathVariable("id") String id) {
        this.autoDoorService.close(id);
    }

    @LogOperation
    @ApiOperation(value = "创建风淋门")
    @ApiImplicitParam(name = "airShowerDoor", value = "风淋门", required = true, dataType = "AirShowerDoor")
    @PostMapping("/airShowerDoor")
    @ResponseStatus(value = HttpStatus.CREATED)
    public AirShowerDoor insertAirShower(@RequestBody @Valid AirShowerDoor airShowerDoor) {
        airShowerDoor = this.autoDoorService.insertAirShower(airShowerDoor);
        LOGGER.debug("创建风淋门数据:" + airShowerDoor.toString());
        return airShowerDoor;
    }

    @ApiOperation(value = "风淋门详情")
    @ApiImplicitParam(name = "id", value = "前门或后门ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/airShowerDoor")
    @ResponseStatus(value = HttpStatus.OK)
    public AirShowerDoor getAirShower(@PathVariable("id") String id) {
        return autoDoorService.selectAirShowerByArbitrarilyId(id);
    }

    @LogOperation
    @ApiOperation(value = "更新风淋门")
    @ApiImplicitParam(name = "airShowerDoor", value = "风淋门", required = true, dataType = "AirShowerDoor")
    @PutMapping("/airShowerDoor")
    @ResponseStatus(value = HttpStatus.OK)
    public AirShowerDoor updateAirShower(@RequestBody @Valid AirShowerDoor airShowerDoor) {
        airShowerDoor = this.autoDoorService.updateAirShower(airShowerDoor);
        LOGGER.debug("更新风淋门数据:" + airShowerDoor.toString());
        return airShowerDoor;
    }

}
