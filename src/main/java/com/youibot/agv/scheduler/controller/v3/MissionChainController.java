package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionChain;
import com.youibot.agv.scheduler.service.MissionChainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("MissionChainControllerV3")
@RequestMapping(value = "/api/v3/missionChains", produces = "application/json")
@Api(value = "预设任务链", tags = "预设任务链", description="预设任务链, 管理一组预设任务, AGV在执行该预设任务链创建的任务链时, 不可执行其他任务。")
public class MissionChainController {

    @Autowired
    private MissionChainService missionChainService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionChain> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionChainService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionChain> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionChainService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "missionChain", value = "动作", required = true, dataType = "MissionChain")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionChain save(@RequestBody @Valid MissionChain missionChain) {
        this.missionChainService.insert(missionChain);
        return missionChain;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "预设任务链ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionChain get(@PathVariable("id") String id) {
        return missionChainService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "预设任务链ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionChain", value = "预设任务链", required = true, dataType = "MissionChain")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionChain update(@PathVariable("id") String id, @RequestBody @Valid MissionChain missionChain) {
        missionChain.setId(id);
        missionChainService.update(missionChain);
        return this.missionChainService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "预设任务链ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.missionChainService.deleteById(id);
    }
}
