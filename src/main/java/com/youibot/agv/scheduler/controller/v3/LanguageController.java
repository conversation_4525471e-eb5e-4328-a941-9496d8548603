package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.entity.Language;
import com.youibot.agv.scheduler.service.LanguageService;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("languageControllerV3")
@RequestMapping(value = "/api/v3/languages", produces = "application/json")
@Api(value = "使用语言", tags = "使用语言", description = "设置系统当前所使用的语言。")
public class LanguageController extends BaseController {

    @Autowired
    private LanguageService languageService;

    @Autowired
    private VehiclePool vehiclePool;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Language> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return languageService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Language> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return languageService.searchAll(searchMap);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "语言ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Language get(@PathVariable("id") String id) {
        return languageService.selectById(id);
    }

    @LogOperation
    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "语言ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "language", value = "语言", required = true, dataType = "Language")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Language update(@PathVariable("id") String id, @RequestBody @Valid Language language) {
        language.setId(id);
        this.languageService.update(language);
        return this.languageService.selectById(id);
    }

}
