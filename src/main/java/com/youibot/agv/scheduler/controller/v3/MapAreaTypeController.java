package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MapArea;
import com.youibot.agv.scheduler.entity.MapAreaType;
import com.youibot.agv.scheduler.service.MapAreaService;
import com.youibot.agv.scheduler.service.MapAreaTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("mapAreaTypeControllerV3")
@RequestMapping(value = "/api/v3/mapAreaTypes", produces = "application/json")
@Api(value = "地图区域类型", tags = "地图区域类型", description = "不同的区域类型实现不同的区域功能，例如避障区域，当车辆通过这个区域时，会关闭避障功能。")
public class MapAreaTypeController extends BaseController {

    @Autowired
    private MapAreaTypeService mapAreaTypeService;

    @Autowired
    private MapAreaService mapAreaService;

    private static final Logger LOGGER = LoggerFactory.getLogger(MapAreaTypeController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MapAreaType> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return mapAreaTypeService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MapAreaType> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return mapAreaTypeService.searchAll(searchMap);
    }

    @ApiIgnore
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "mapAreaType", value = "地图区域类型", required = true, dataType = "MapAreaType")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MapAreaType save(@RequestBody @Valid MapAreaType MapAreaType) {
        this.mapAreaTypeService.insert(MapAreaType);
        LOGGER.debug(MapAreaType.toString());
        return MapAreaType;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "地图区域类型ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MapAreaType get(@PathVariable("id") String id) {
        return mapAreaTypeService.selectById(id);
    }

    @ApiIgnore
    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "地图区域类型ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapAreaType", value = "地图区域类型", required = true, dataType = "MapAreaType")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MapAreaType update(@PathVariable("id") String id, @RequestBody @Valid MapAreaType mapAreaType) {
        mapAreaType.setId(id);
        this.mapAreaTypeService.update(mapAreaType);
        return this.mapAreaTypeService.selectById(id);
    }

    @ApiIgnore
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "地图区域类型ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.mapAreaTypeService.deleteById(id);
    }

    /**
     * 获取该区域类型下的区域列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据类型查询出区域列表")
    @ApiImplicitParam(name = "id", value = "区域类型的ID", paramType = "path", required = true, dataType = "String")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "区域类型的ID", paramType = "path", required = true, dataType = "String")})
    @GetMapping(value = "/{id}/mapAreas")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MapArea> getMapAreas(@PathVariable("id") String id,String agvMapId,Boolean isDraft) {
        return mapAreaService.selectAreaByType(id,agvMapId,isDraft);
    }
}
