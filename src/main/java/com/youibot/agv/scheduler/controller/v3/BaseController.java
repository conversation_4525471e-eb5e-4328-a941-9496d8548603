package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import org.springframework.beans.factory.annotation.Autowired;

import static com.youibot.agv.scheduler.constant.VehicleConstant.DISABLE;
import static com.youibot.agv.scheduler.constant.VehicleConstant.OUTLINE;

public class BaseController {

    @Autowired
    protected VehiclePool vehiclePool;

    public Vehicle getVehicle(String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if (vehicle == null) {
            throw new ExecuteException(MessageUtils.getMessage("http.agv_not_login"));
        }
        if (DISABLE.equals(vehicle.getStatus())){
            throw new ExecuteException(MessageUtils.getMessage("vehicle.not_enable"));
        }
        if (OUTLINE.equals(vehicle.getOnlineStatus())){
            throw new ExecuteException(MessageUtils.getMessage("vehicle.outline"));
        }
        return vehicle;
    }

}
