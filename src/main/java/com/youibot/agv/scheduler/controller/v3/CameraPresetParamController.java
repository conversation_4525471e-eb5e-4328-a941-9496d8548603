package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.CameraPresetParam;
import com.youibot.agv.scheduler.service.CameraPresetParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("cameraPresetParamControllerV3")
@RequestMapping(value = "/api/v3/cameraPresetParams", produces = "application/json")
@Api(value = "摄像头预置参数", tags = "摄像头预置参数", description = "摄像头预置参数用于配置一套摄像头参数模板，编辑好默认参数值后可使用改模板的数据设置agv的摄像头参数")
public class CameraPresetParamController extends BaseController {

    @Autowired
    private CameraPresetParamService cameraPresetParamService;

    private static final Logger LOGGER = LoggerFactory.getLogger(CameraPresetParamController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<CameraPresetParam> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return cameraPresetParamService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<CameraPresetParam> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return cameraPresetParamService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "cameraPresetParam", value = "摄像头预置参数", required = true, dataType = "CameraPresetParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public CameraPresetParam save(@RequestBody @Valid CameraPresetParam cameraPresetParam) {
        this.cameraPresetParamService.insert(cameraPresetParam);
        LOGGER.debug(cameraPresetParam.toString());
        return cameraPresetParam;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "摄像头预置参数ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public CameraPresetParam get(@PathVariable("id") String id) {
        return cameraPresetParamService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "摄像头预置参数ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "cameraPresetParam", value = "摄像头预置参数", required = true, dataType = "CameraPresetParam")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public CameraPresetParam update(@PathVariable("id") String id, @RequestBody @Valid CameraPresetParam cameraPresetParam) {
        cameraPresetParam.setId(id);
        this.cameraPresetParamService.update(cameraPresetParam);
        return this.cameraPresetParamService.selectById(cameraPresetParam.getId());
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "摄像头预置参数ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.cameraPresetParamService.deleteById(id);
    }
}
