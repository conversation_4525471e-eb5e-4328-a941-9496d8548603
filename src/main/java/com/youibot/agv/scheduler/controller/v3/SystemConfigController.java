package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.SystemConfig;
import com.youibot.agv.scheduler.service.SystemConfigService;
import com.youibot.agv.scheduler.util.TimeZoneUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 系统配置
 * @Author：yangpeilin
 * @Date: 2020/5/9 16:42
 */
@RestController("SystemConfigControllerV3")
@RequestMapping("/api/v3/config")
@Api(value = "系统配置", tags = "系统配置", produces = "application/json")
public class SystemConfigController {

    @Autowired
    private SystemConfigService systemConfigService;

    @ApiOperation(value = "查看系统配置")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public SystemConfig getSystemConfig() {
        return systemConfigService.findAll().get(0);
    }

    @ApiOperation(value = "修改系统配置")
    @ApiImplicitParam(name = "systemConfig", value = "系统配置信息", required = true, dataType = "SystemConfig")
    @PutMapping
    @ResponseStatus(value = HttpStatus.OK)
    public void updateSystemConfig(@RequestBody SystemConfig systemConfig) {
        //查看是否有修改时区
        if (!StringUtils.isEmpty(systemConfig.getTimeZone())){
            TimeZoneUtil.setTimeZone(systemConfig.getTimeZone());
        }
        systemConfigService.updateByPrimaryKeySelective(systemConfig);
    }

}
