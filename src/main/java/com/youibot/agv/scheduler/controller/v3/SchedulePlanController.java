package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.SchedulePlan;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.SchedulePlanService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.SchedulePlanConstant.SCHEDULE_STATUS_RUNNING;

@RestController("schedulePlanControllerV3")
@RequestMapping(value = "/api/v3/schedulingPlans", produces = "application/json")
@Api(value = "调度计划", tags = "调度计划", description = "调度计划管理接口，任务可以用调度计划定时执行。")
public class SchedulePlanController extends BaseController {

    @Autowired
    private SchedulePlanService schedulePlanService;

    @Autowired
    private MissionWorkService missionWorkService;

    private static final Logger LOGGER = LoggerFactory.getLogger(SchedulePlanController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<SchedulePlan> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return schedulePlanService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<SchedulePlan> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return schedulePlanService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "schedulePlan", value = "调度计划信息", required = true, dataType = "SchedulePlan")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public SchedulePlan createSchedule(@RequestBody @Valid SchedulePlan schedulePlan) {
        String id = this.schedulePlanService.createSchedule(schedulePlan);
        return schedulePlanService.selectById(id);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "调度计划ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public SchedulePlan get(@PathVariable("id") String id) {
        return schedulePlanService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "调度计划ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "schedulePlan", value = "调度计划信息", required = true, dataType = "SchedulePlan")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public SchedulePlan update(@PathVariable("id") @Required String id, @RequestBody @Valid SchedulePlan schedulePlan) {
        schedulePlan.setId(id);
        this.schedulePlanService.scheduleUpdate(schedulePlan);
        return this.schedulePlanService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "调度计划ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") @Required String id) throws SchedulerException {
        SchedulePlan plan = schedulePlanService.selectById(id);
        if (StringUtils.isEmpty(plan)) {
            return;
        }
        schedulePlanService.scheduleDelete(plan);
    }

    @ApiOperation(value = "根据调度ID查询工作列表")
    @ApiImplicitParam(name = "id", value = "调度计划ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionWorks")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWork> getMissionWorks(@PathVariable("id") String id) {
        MissionWork work = new MissionWork();
        work.setSchedulePlanId(id);
        return missionWorkService.findByEntity(work);
    }

    @ApiOperation(value = "暂停")
    @ApiImplicitParam(name = "id", value = "调度计划ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/pause")
    @ResponseStatus(value = HttpStatus.OK)
    public void schedulePause(@PathVariable("id") @Required String id) throws SchedulerException {
        SchedulePlan plan = schedulePlanService.selectById(id);
        if (plan == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.schedule_plan_is_null"));
        }
        schedulePlanService.schedulePause(plan);
    }

    @ApiOperation(value = "恢复")
    @ApiImplicitParam(name = "id", value = "调度计划ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/resume")
    @ResponseStatus(value = HttpStatus.OK)
    public void scheduleResume(@PathVariable("id") @Required String id) throws SchedulerException {
        SchedulePlan plan = schedulePlanService.selectById(id);
        if (plan == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.schedule_plan_is_null"));
        }
        schedulePlanService.scheduleResume(plan);
    }

    @ApiOperation(value = "停止")
    @ApiImplicitParam(name = "id", value = "调度计划ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/shutDown")
    @ResponseStatus(value = HttpStatus.OK)
    public void scheduleShutDown(@PathVariable("id") @Required String id) throws SchedulerException {
        SchedulePlan plan = schedulePlanService.selectById(id);
        if (plan == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.schedule_plan_is_null"));
        }
        schedulePlanService.scheduleShutDown(plan);
    }


}
