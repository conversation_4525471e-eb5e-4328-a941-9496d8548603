package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.constant.vo.MissionActionStatisticVO;
import com.youibot.agv.scheduler.constant.vo.MissionStatisticVO;
import com.youibot.agv.scheduler.constant.vo.VehicleStatisticVO;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据统计
 * @Author：xyh
 * @Date: 2022/3/3 9:00
 */
@RestController("dataStatisticController")
@RequestMapping("/api/v3/statistic")
@Api(value = "数据统计", tags = "提供给tms,做大屏展示，搜集小车的数据、任务数据", description = "")
@Slf4j
public class DataStatisticController {

    private static Logger logger = LoggerFactory.getLogger(DataStatisticController.class);

    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private MissionWorkActionService missionWorkActionService;
    @Autowired
    private MissionWorkService missionWorkService;



    @ApiOperation(value = "返回机器人状态集合信息", notes = "返回机器人状态集合信息,由调用方决定调用频次")
    @GetMapping("/getVehicleBasicInfo")
    @ResponseStatus(HttpStatus.OK)
    public List<VehicleStatisticVO> getVehicleBasicInfo(@RequestParam(value = "agvCode", required = false) String agvCode) {

        List<VehicleStatisticVO> list = new ArrayList<>();
        //1、如果小车id不为空，查单个
        if(StringUtils.isNotBlank(agvCode)){
            Vehicle vehicle = vehiclePoolService.selectById(agvCode);
            if(vehicle!=null){
                VehicleStatisticVO tmp = new VehicleStatisticVO();
                BeanUtils.copyPropertiesIgnoreNull_new(vehicle,tmp);
                list.add(tmp);
            }
            return list;
        }

        //2、查询所有
        List<Vehicle> vehicles = vehiclePoolService.selectAll();
        if(CollectionUtils.isEmpty(vehicles)){
            return list;
        }
        for(Vehicle vehicle : vehicles){
            VehicleStatisticVO tmp = new VehicleStatisticVO();
            BeanUtils.copyPropertiesIgnoreNull_new(vehicle,tmp);
            list.add(tmp);
        }
        return list;
    }


    @ApiOperation(value = "返回机器人在某个时间段的作业统计信息", notes = "返回机器人在某个时间段的统计信息,传入agvCode,查询单个，否则查询所有")
    @GetMapping("/getMissionWorkStatistic")
    @ResponseStatus(HttpStatus.OK)
    public List<MissionStatisticVO> getMissionWorkStatistic(@RequestParam(value = "agvCode", required = false) String agvCode,
                                                        @RequestParam(value = "startTime", required = true) String startTime,
                                                        @RequestParam(value = "endTime", required = true) String endTime) {

        log.info("TMS 调用作业统计开始:agvCode:{},startTime:{},endTime:{}", agvCode,startTime,endTime);
        if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }

        Date startDate = DateUtils.getFormatDate(startTime);
        Date endDate = DateUtils.getFormatDate(endTime);
        if(startDate==null || endDate==null || startDate.after(endDate)){
            throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
        }
        if(StringUtils.isBlank(agvCode)){
            agvCode = null;
        }
        List<MissionStatisticVO> missionActionStatistic = missionWorkService.getMissionWorkStatistic(agvCode, startTime, endTime);
        log.info("TMS 调用作业统计结束:agvCode:{},startTime:{},endTime:{}", agvCode,startTime,endTime);
        return missionActionStatistic;
    }


    @ApiOperation(value = "返回任务动作在某个时间段的统计信息", notes = "返回任务动作在某个时间段的统计信息,传入agvCode,查询单个，否则查询所有")
    @GetMapping("/getMissionWorkActionStatistic")
    @ResponseStatus(HttpStatus.OK)
    public List<MissionActionStatisticVO> getMissionWorkActionStatistic(@RequestParam(value = "agvCode", required = false) String agvCode,
                                                                        @RequestParam(value = "startTime", required = true) String startTime,
                                                                        @RequestParam(value = "endTime", required = true) String endTime) {

        log.info("TMS 调用动作统计开始:agvCode:{},startTime:{},endTime:{}", agvCode,startTime,endTime);
        if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }

        Date startDate = DateUtils.getFormatDate(startTime);
        Date endDate = DateUtils.getFormatDate(endTime);
        if(startDate==null || endDate==null || startDate.after(endDate)){
            throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
        }
        if(StringUtils.isBlank(agvCode)){
            agvCode = null;
        }
        List<MissionActionStatisticVO> missionActionStatistic = missionWorkActionService.getMissionActionStatistic(agvCode, startTime, endTime);
        log.info("TMS 调用动作统计结束:agvCode:{},startTime:{},endTime:{}", agvCode,startTime,endTime);
        return missionActionStatistic;
    }

}
