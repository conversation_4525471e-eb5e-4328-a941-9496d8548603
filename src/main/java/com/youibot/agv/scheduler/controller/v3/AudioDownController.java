package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.exception.ExecuteException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

@RestController
@RequestMapping("/api/v2/audioFile")
@Api(value = "音频文件", tags = "音频文件(audioFile)")
public class AudioDownController {
    private final static Logger LOGGER = LoggerFactory.getLogger(AudioDownController.class);

    @ApiOperation(value = "下载音频文件(文件流格式)", notes = "根据路径获取对应的音频文件")
    @ApiImplicitParam(name = "url", value = "音频文件url", required = true)
    @GetMapping(value = "/downAudio")
    @ResponseStatus(value = HttpStatus.OK)
    public byte[] downAudioFile(@RequestParam String url) {
        InputStream in = null;
        try {
            File audioFile = new File(url);
            if (!audioFile.exists()) {
                throw new ExecuteException("文件不存在");
            }
            in = new FileInputStream(url);
            byte[] buffer = new byte[(int) audioFile.length()];
            in.read(buffer);
            return buffer;
        } catch (IOException e) {
            throw new ExecuteException("获取文件失败");
        } finally {
            //关闭资源（response获得的流会自动关闭）
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    LOGGER.warn("inputStream close error", e);
                }
            }
        }
    }
}
