package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.DataCodeMatrix;
import com.youibot.agv.scheduler.map.entity.PageInfo;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.DataCodeMatrixService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;



@RestController("dataCodeMatrixV3")
@RequestMapping(value = "/api/v3/dataCodeMatrix", produces = "application/json")
@Api(value = "DataMatrix二维码", tags = "DataMatrix二维码", description = "二维码定位方式地图数据")
@Deprecated
public class DataCodeMatrixController extends BaseController {

    @Autowired
    private DataCodeMatrixService dataCodeMatrixService;

    @Autowired
    private AGVMapService agvMapService;

    private static final Logger LOGGER = LoggerFactory.getLogger(DataCodeMatrixController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<DataCodeMatrix> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap,Boolean isDraft) {
        return dataCodeMatrixService.findPage(searchMap,isDraft);
    }

    @ApiOperation(value = "列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "agvMapName", value = "地图名称", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<DataCodeMatrix> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap,Boolean isDraft) {
        return dataCodeMatrixService.searchAll(searchMap,isDraft);
    }

    @ApiOperation(value = "创建")

    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCodeMatrix", value = "DataMatrix二维码", required = true, dataType = "DataCodeMatrix"),
            @ApiImplicitParam(name = "mapName", value = "地图名称", required = true, dataType = "String", paramType = "query")
    })
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public DataCodeMatrix save(@RequestBody @Valid DataCodeMatrix dataCodeMatrix,String mapName) {
        this.dataCodeMatrixService.insert(mapName,dataCodeMatrix);
        LOGGER.debug(dataCodeMatrix.toString());
        return dataCodeMatrix;
    }

    @ApiOperation(value = "详情", notes = "根据ID获取DataMatrix二维码详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "id", value = "DataMatrix二维码ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapName", value = "地图名称", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public DataCodeMatrix get(@PathVariable("id") String id,String mapName, Boolean isDraft) {
        return dataCodeMatrixService.selectById(mapName,id,isDraft);
    }

    @ApiOperation(value = "更新", notes = "根据ID更新DataMatrix二维码信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "DataMatrix二维码ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "dataCodeMatrix", value = "DataMatrix二维码", required = true, dataType = "DataCodeMatrix"),
            @ApiImplicitParam(name = "mapName", value = "地图名称", required = true, dataType = "String", paramType = "query")
    })
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public DataCodeMatrix update(@PathVariable("id") String id,String mapName, @RequestBody @Valid DataCodeMatrix dataCodeMatrix) {
        dataCodeMatrix.setId(id);
        this.dataCodeMatrixService.update(mapName,dataCodeMatrix);
        return this.dataCodeMatrixService.selectById(mapName,id,true);
    }

    @ApiOperation(value = "删除", notes = "根据ID删除DataMatrix二维码信息")
    @ApiImplicitParam(name = "id", value = "DataMatrix二维码ID", paramType = "path", required = true, dataType = "String")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "DataMatrix二维码ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapName", value = "地图名称", required = true, dataType = "String", paramType = "query")
    })
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id,String mapName) {
        this.dataCodeMatrixService.deleteById(mapName,id);
    }

}
