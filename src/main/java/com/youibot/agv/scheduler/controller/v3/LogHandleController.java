package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.param.LogParam;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 日志处理
 *
 * @Author：yangpeilin
 * @Date: 2020/4/23 18:06
 */

@RestController("LogHandleController")
@RequestMapping("/api/v3/logHandle")
@Api(value = "服务器日志处理", tags = "日志操作,默认操作地址：/server/youifleet/logs/")
public class LogHandleController {

    private static Logger logger = LoggerFactory.getLogger(LogHandleController.class);

    @Value("${LOG_INFO.DIRECTOR_PATH}")
    private String defaultDirector;

    @Value("${LOG_INFO.LOG_FIX}")
    private String logFix;

    @ApiOperation(value = "删除日志文件或文件夹")
    @ApiImplicitParam(name = "filePath", value = "日志参数，必填：filePath(被删除文件名全路径)", required = true, dataType = "filePath")
    @ResponseStatus(value = HttpStatus.OK)
    @DeleteMapping
    public void deleteLogFile(@RequestParam String filePath) {
        File file = new File(defaultDirector + filePath);
        if (!file.exists()) {
            logger.info("deleteLogFile file is not exists !");
            return;
        }
        logger.info("deleteLogFile filePath : " + filePath);
        file.delete();
    }

    @ApiOperation(value = "查询日志文件列表")
    @ApiImplicitParam(name = "logFileDir", value = "日志参数，选填：logFileDir(文件夹，默认为/server/youifleet/logs/)", required = true, dataType = "logFileDir")
    @ResponseStatus(value = HttpStatus.OK)
    @GetMapping
    public List<LogParam> listFiles(String logFileDir) {
        if (StringUtils.isEmpty(logFileDir)) {
            logFileDir = defaultDirector;
        } else {
            logFileDir = defaultDirector + logFileDir;
        }
        logger.info("listFiles logFileDir is : " + logFileDir);
        File logDir = new File(logFileDir);
        if (!logDir.exists() && !logDir.isDirectory()) {
            logger.info("logDir is not exists or not director");
            throw new YOUIFleetException(MessageUtils.getMessage("service.directory_not_exist"));
        }
        File[] files = logDir.listFiles();
        List<LogParam> logParams = new ArrayList<>();
        for (File file : files) {
            LogParam logParam = new LogParam();
            Boolean isFile = true;
            if (file.isDirectory()) {
                isFile = false;
            }
            logParam.setIsFile(isFile);
            logParam.setFileName(file.getName());
            logParam.setLastModified(file.lastModified());
            logParams.add(logParam);
        }
        // log sort by last modify time.
        logParams.sort((log1, log2) -> {
            return Long.compare(log2.getLastModified(), log1.getLastModified()); //修改时间降序
        });

        return logParams;
    }

    @ApiOperation(value = "查看日志文件(可通过关键字查询)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "filePath", value = "filePath(文件名称，全路径)", required = true, dataType = "String"),
            @ApiImplicitParam(name = "keyWord", value = "keyWord(关键字)", required = true, dataType = "String")
    })
    @ResponseStatus(value = HttpStatus.OK)
    @GetMapping(value = "/viewFile", produces = {"application/json;charset=UTF-8"})
    public String viewFile(@RequestParam String filePath, String keyword) {
        File file = new File(defaultDirector + filePath);
        if (!file.exists() || file.isDirectory() || !file.getName().endsWith(logFix)) {
            logger.error("viewFile filePath : " + filePath);
            throw new YOUIFleetException(MessageUtils.getMessage("http.params_error"));
        }
        StringBuffer sb = new StringBuffer();
        try {
            BufferedReader brs = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
            String tempString = null;
            while ((tempString = brs.readLine()) != null) {
                if (StringUtils.isNotBlank(keyword)) {
                    if (tempString.contains(keyword)) {
                        sb.append(tempString).append("\n");
                    }
                } else {
                    sb.append(tempString).append("\n");
                }
            }
        } catch (Exception e) {
            logger.error("read file error, fileName:" + filePath + ", errorMsg:" + e.getMessage());
            throw new YOUIFleetException(MessageUtils.getMessage("service.file_read_error"));
        }
        return sb.toString();
    }

    @ApiOperation(value = "下载日志文件")
    @ApiImplicitParam(name = "filePath", value = "日志参数，必填：filePath(下载文件名称，/文件名)", required = true, dataType = "filePath")
    @ResponseStatus(value = HttpStatus.OK)
    @GetMapping("/downLogFile")
    public void downLogFile(@RequestParam String filePath, HttpServletResponse response) {
        filePath = defaultDirector + filePath;
        File file = new File(filePath);
        if (!file.exists() && file.isDirectory()) {
            logger.info("file is not exists or file is director");
            throw new YOUIFleetException(MessageUtils.getMessage("service.file_is_directory_or_not_exist"));
        }
        try {
            InputStream fis = new BufferedInputStream(new FileInputStream(filePath));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(file.getName().getBytes(StandardCharsets.UTF_8), "iso8859-1"));
            response.addHeader("Content-Length", "" + file.length());
            OutputStream os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            os.write(buffer);// 输出文件
            os.flush();
            os.close();
        } catch (Exception e) {
            logger.error("downLogFile error, fileName:" + filePath + ", errorMsg:" + e.getMessage());
            throw new YOUIFleetException(MessageUtils.getMessage("service.file_download_error"));
        }
    }

}
