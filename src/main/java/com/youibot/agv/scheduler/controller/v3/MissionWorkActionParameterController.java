package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.entity.MissionWorkActionParameter;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.service.MissionWorkActionParameterService;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @version CreateTime: 2019年6月3日 上午10:29:30
 * <AUTHOR>  E-mail:<EMAIL>
 */
@RestController("missionWorkActionParameterControllerV3")
@RequestMapping(value = "/api/v3/missionWorkActionParameters", produces = "application/json")
@Api(value = "动作参数", tags = "动作参数" , description = "动作参数管理接口，动作参数由系统自动创建。")
public class MissionWorkActionParameterController {

    @Autowired
    private MissionWorkActionParameterService missionWorkActionParameterService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkActionParameterController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionWorkActionParameter> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkActionParameterService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkActionParameter> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkActionParameterService.searchAll(searchMap);
    }

    @ApiIgnore
    @ApiOperation(value = "创建", notes = "根据动作参数信息")
    @ApiImplicitParam(name = "missionWorkActionParameter", value = "动作参数", required = true, dataType = "MissionWorkActionParameter")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionWorkActionParameter save(@RequestBody @Valid MissionWorkActionParameter missionWorkActionParameter) {
        this.missionWorkActionParameterService.insert(missionWorkActionParameter);
        LOGGER.debug(missionWorkActionParameter.toString());
        return missionWorkActionParameter;
    }

    @ApiIgnore
    @ApiOperation(value = "批量创建")
    @ApiImplicitParam(name = "missionWorkActionParameters", value = "动作参数列表", required = true, dataType = "MissionWorkActionParameter", allowMultiple = true)
    @PostMapping("/batch")
    @ResponseStatus(value = HttpStatus.CREATED)
    public void batchInsert(@RequestBody @Valid MissionWorkActionParameter[] missionWorkActionParameters) {
        if (missionWorkActionParameters == null || missionWorkActionParameters.length == 0) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        String missionWorkActionId = missionWorkActionParameters[0].getMissionWorkActionId();
        MissionWorkAction missionWorkAction = missionWorkActionService.selectById(missionWorkActionId);
        List<MissionWorkActionParameter> parameterList = Arrays.asList(missionWorkActionParameters);
        this.missionWorkActionParameterService.batchInsert(parameterList);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "动作参数ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWorkActionParameter get(@PathVariable("id") String id) {
        return missionWorkActionParameterService.selectById(id);
    }

    @ApiIgnore
    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "动作参数ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionWorkActionParameter", value = "动作参数", required = true, dataType = "MissionWorkActionParameter")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWorkActionParameter update(@PathVariable("id") String id, @RequestBody @Valid MissionWorkActionParameter missionWorkActionParameter) {
        missionWorkActionParameter.setId(id);
        this.missionWorkActionParameterService.update(missionWorkActionParameter);
        return this.missionWorkActionParameterService.selectById(id);
    }

    @ApiIgnore
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "动作参数ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.missionWorkActionParameterService.deleteById(id);
    }

}
