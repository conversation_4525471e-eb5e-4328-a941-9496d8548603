package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedEdge;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.*;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.*;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.ReportSidePathData;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.service.MapCommandService;
import com.youibot.agv.scheduler.mqtt.service.VehicleCommandService;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.RESET_WEIGHT;
import static com.youibot.agv.scheduler.constant.VehicleConstant.*;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 下午9:12 2019/12/12
 * @Description :
 * @Modified By :
 * @Version :
 */
@RestController("pathPlanTestController")
@RequestMapping(value = "/api/v3/pathPlanTest", produces = "application/json")
@Api(value = "路径规划,交通管制", tags = "路径规划,交通管制", description = "路径规划,交通管制,资源状态")
public class PathPlanTestController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PathPlanTestController.class);

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;
    @Autowired
    private MarkerPathResourcePool markerPathResourcePool;
    @Autowired
    private SidePathResourcePool sidePathResourcePool;
    @Autowired
    private ElevatorPathResourcePool elevatorResourcePool;
    @Autowired
    private SingleAreaPathResourcePool singleAreaResourcePool;
    @Autowired
    private MarkerService markerService;
    @Autowired
    private AGVService agvService;
    @Autowired
    private AGVMapService agvMapService;
    @Autowired
    private VehiclePool vehiclePool;
    @Autowired
    private MapCommandService mapCommandService;
    @Autowired
    public VehicleCommandService vehicleCommandService;
    @Autowired
    private TPathResourcePool tPathResourcePool;
    @Autowired
    private OPathResourcePool oPathResourcePool;
    @Autowired
    private PathPlanService pathPlanService;

    //@ApiIgnore
    @ApiOperation(value = "获取AGV的运行中路径")
    @GetMapping("/agvRunningSidePaths")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvSidePath> getAgvRunningSidePaths() {
        List<AgvSidePath> agvSidePaths = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRuningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
        for (Map.Entry<String, LinkedBlockingDeque<SidePath>> entry : agvToRuningSidePaths.entrySet()) {
            AgvSidePath agvSidePath = new AgvSidePath();
            agvSidePath.setAgvName(entry.getKey());
            LinkedList<TempSidePath> paths = new LinkedList<>();
            for (SidePath sidePath : entry.getValue()) {
                TempSidePath path = new TempSidePath();
                path.setSidePathId(sidePath.getId());
                String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                path.setStartMarkerCode(startCode);
                path.setEndMarkerCode(endCode);
                path.setT0(sidePath.getT0());
                path.setT1(sidePath.getT1());
                paths.add(path);
            }
            agvSidePath.setSidePaths(paths);
            agvSidePaths.add(agvSidePath);
        }
        return agvSidePaths;
    }

    //@ApiIgnore
    @ApiOperation(value = "获取AGV的规划路径")
    @GetMapping("/agvPlannedSidePaths")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvSidePath> getAgvPlannedSidePaths() {
        List<AgvSidePath> agvSidePaths = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanningSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        for (Map.Entry<String, LinkedBlockingDeque<SidePath>> entry : agvToPlanningSidePaths.entrySet()) {
            AgvSidePath agvSidePath = new AgvSidePath();
            agvSidePath.setAgvName(entry.getKey());
            LinkedList<TempSidePath> paths = new LinkedList<>();
            for (SidePath sidePath : entry.getValue()) {
                TempSidePath path = new TempSidePath();
                path.setSidePathId(sidePath.getId());
                String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                path.setStartMarkerCode(startCode);
                path.setEndMarkerCode(endCode);
                path.setT0(sidePath.getT0());
                path.setT1(sidePath.getT1());
                paths.add(path);
            }
            agvSidePath.setSidePaths(paths);
            agvSidePaths.add(agvSidePath);
        }
        return agvSidePaths;
    }

    //@ApiIgnore
    @ApiOperation(value = "获取AGV的已执行路径")
    @GetMapping("/agvExecutedSidePaths")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvSidePath> getAgvExecutedSidePaths() {
        List<AgvSidePath> agvSidePaths = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToExecutedSidePaths = checkAndSendPathService.getAgvToExecutedSidePaths();
        for (Map.Entry<String, LinkedBlockingDeque<SidePath>> entry : agvToExecutedSidePaths.entrySet()) {
            AgvSidePath agvSidePath = new AgvSidePath();
            agvSidePath.setAgvName(entry.getKey());
            LinkedList<TempSidePath> paths = new LinkedList<>();
            for (SidePath sidePath : entry.getValue()) {
                TempSidePath path = new TempSidePath();
                path.setSidePathId(sidePath.getId());
                String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                path.setStartMarkerCode(startCode);
                path.setEndMarkerCode(endCode);
                path.setT0(sidePath.getT0());
                path.setT1(sidePath.getT1());
                paths.add(path);
            }
            agvSidePath.setSidePaths(paths);
            agvSidePaths.add(agvSidePath);
        }
        return agvSidePaths;
    }


    @ApiOperation(value = "获取AGV的运行中和已执行路径--从过去到现在")
    @GetMapping("/agvRuningExecutedSidePaths")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvSidePath> getAgvRuningExecutedSidePaths() {

        List<AgvSidePath> agvSidePaths = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToExecutedSidePaths = checkAndSendPathService.getAgvToExecutedSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRuningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();

        for (Map.Entry<String, LinkedBlockingDeque<SidePath>> entry : agvToExecutedSidePaths.entrySet()) {
            AgvSidePath agvSidePath = new AgvSidePath();
            agvSidePath.setAgvName(entry.getKey());
            LinkedList<TempSidePath> paths = new LinkedList<>();
            LinkedBlockingDeque<SidePath> value = entry.getValue();
            LinkedBlockingDeque<SidePath> runing = agvToRuningSidePaths.get(entry.getKey());
            value.addAll( runing );
            for (SidePath sidePath : value) {
                TempSidePath path = new TempSidePath();
                path.setSidePathId(sidePath.getId());
                String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                path.setStartMarkerCode(startCode);
                path.setEndMarkerCode(endCode);
                path.setT0(sidePath.getT0());
                path.setT1(sidePath.getT1());
                paths.add(path);
            }
            agvSidePath.setSidePaths(paths);
            agvSidePaths.add(agvSidePath);
        }




        return agvSidePaths;
    }

    @ApiOperation(value = "获取AGV的运行中和已执行路径---从现在到过去")
    @GetMapping("/agvRuningExecutedSidePathsFromNowToPast")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvSidePath> getAgvRuningExecutedSidePathsFromNowToPast() {

        List<AgvSidePath> agvSidePaths = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToExecutedSidePaths = checkAndSendPathService.getAgvToExecutedSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRuningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();

        for (Map.Entry<String, LinkedBlockingDeque<SidePath>> entry : agvToExecutedSidePaths.entrySet()) {
            AgvSidePath agvSidePath = new AgvSidePath();
            agvSidePath.setAgvName(entry.getKey());
            LinkedList<TempSidePath> paths = new LinkedList<>();
            LinkedBlockingDeque<SidePath> value = entry.getValue();
            LinkedBlockingDeque<SidePath> runing = agvToRuningSidePaths.get(entry.getKey());
            value.addAll( runing );
            for (SidePath sidePath : value) {
                TempSidePath path = new TempSidePath();
                path.setSidePathId(sidePath.getId());
                String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                path.setStartMarkerCode(startCode);
                path.setEndMarkerCode(endCode);
                path.setT0(sidePath.getT0());
                path.setT1(sidePath.getT1());
                paths.add(path);
            }
            Collections.reverse( paths);
            agvSidePath.setSidePaths(paths);
            agvSidePaths.add(agvSidePath);
        }




        return agvSidePaths;

    }


    @ApiOperation(value = "获取AGV的运行中和已执行路径---从现在到过去--携带上报事件")
    @GetMapping("/agvRuningExecutedSidePathsFromNowToPastReportEvents")
    @ResponseStatus(value = HttpStatus.OK)
    public  Map<String, AgvSidePath > getAgvRuningExecutedSidePathsFromNowToPastReportSidePathData() {
        Map<String, AgvSidePath > res = new HashMap<>( 40 );
        List<AgvSidePath> agvRuningExecutedSidePathsFromNowToPast = getAgvRuningExecutedSidePathsFromNowToPast();

        for (int i = 0; i < agvRuningExecutedSidePathsFromNowToPast.size(); i++) {
            AgvSidePath agvSidePath = agvRuningExecutedSidePathsFromNowToPast.get(i);
            res.put(agvSidePath.getAgvName(), agvSidePath);
            for (int j = 0; j < agvSidePath.getSidePaths().size(); j++) {
                TempSidePath tempSidePath = agvSidePath.getSidePaths().get(j);
                SidePath sidePath = MapGraphUtil.getSidePathBySidePathId(tempSidePath.sidePathId);
                if(Objects.nonNull(sidePath) && StringUtils.isNotBlank( sidePath.getReportType())){
                    ReportSidePathData data = new ReportSidePathData(tempSidePath.endMarkerCode, sidePath, agvSidePath.getAgvName(), false);
                    agvSidePath.setReportSidePathData(data);
                    agvSidePath.setIndex( j );
                    break;
                }
            }
        }

        return res;

    }


    @ApiOperation(value = "获取AGV的运行中和规划将执行路径---从现在到将来--携带上报事件")
    @GetMapping("/agvRuningExecutedSidePathsFromNowToFutureReportEvents")
    @ResponseStatus(value = HttpStatus.OK)
    public  Map<String, AgvSidePath > getAgvRuningFutureSidePathsFromNowToFutureReportSidePathData() {
        Map<String, AgvSidePath > res = new HashMap<>( 40 );
        List<AgvSidePath> agvSidePaths = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRuningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();

        for (Map.Entry<String, LinkedBlockingDeque<SidePath>> entry : agvToRuningSidePaths.entrySet()) {
            AgvSidePath agvSidePath = new AgvSidePath();
            agvSidePath.setAgvName(entry.getKey());
            LinkedList<TempSidePath> paths = new LinkedList<>();
            LinkedBlockingDeque<SidePath> value = entry.getValue();
            LinkedBlockingDeque<SidePath> future = agvToPlanedSidePaths.get(entry.getKey());
            value.addAll( future );
            for (SidePath sidePath : value) {
                TempSidePath path = new TempSidePath();
                path.setSidePathId(sidePath.getId());
                String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                path.setStartMarkerCode(startCode);
                path.setEndMarkerCode(endCode);
                path.setT0(sidePath.getT0());
                path.setT1(sidePath.getT1());
                paths.add(path);
            }
            agvSidePath.setSidePaths(paths);
            agvSidePaths.add(agvSidePath);
        }

        for (int i = 0; i < agvSidePaths.size(); i++) {
            AgvSidePath agvSidePath = agvSidePaths.get(i);
            res.put(agvSidePath.getAgvName(), agvSidePath);
            for (int j = 0; j < agvSidePath.getSidePaths().size(); j++) {
                TempSidePath tempSidePath = agvSidePath.getSidePaths().get(j);
                SidePath sidePath = MapGraphUtil.getSidePathBySidePathId(tempSidePath.sidePathId);
                if(Objects.nonNull(sidePath) && StringUtils.isNotBlank( sidePath.getReportType())){
                    ReportSidePathData data = new ReportSidePathData(tempSidePath.endMarkerCode, sidePath, agvSidePath.getAgvName(), false);
                    agvSidePath.setReportSidePathData(data);
                    agvSidePath.setIndex( j );
                    break;
                }
            }
        }

        return res;

    }

    //@ApiIgnore
    @ApiOperation(value = "获取Marker资源的占用状态")
    @GetMapping("/MarkerResource")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MarkerApplyVehicle> getMarkerResource() {
        Map<String, List<Pair<String, String>>> agvCodeToMarkerIds = new ConcurrentHashMap<>();
        Map<String, MarkerPathResource> poolEntries = markerPathResourcePool.getPoolEntries();
        for (MarkerPathResource markerPathResource : poolEntries.values()) {
            if (markerPathResource.getApplyAgvCode() != null) {
                String agvCode = markerPathResource.getApplyAgvCode();
                agvCodeToMarkerIds.computeIfAbsent(agvCode, k -> new ArrayList<>());
                agvCodeToMarkerIds.get(agvCode).add(new Pair<>(markerPathResource.getMarkerId(), markerPathResource.getMarkerCode()));
            }
        }
        List<MarkerApplyVehicle> markerApplyVehicles = new ArrayList<>();
        for (Map.Entry<String, List<Pair<String, String>>> stringListEntry : agvCodeToMarkerIds.entrySet()) {
            String agvCode = stringListEntry.getKey();
            List<Pair<String, String>> markerIdAndCodes = stringListEntry.getValue();
            MarkerApplyVehicle markerApplyVehicle = new MarkerApplyVehicle();
            markerApplyVehicle.setAgvName(agvCode);
            markerApplyVehicle.setMarkerIds(markerIdAndCodes.stream().map(Pair::first).collect(Collectors.toList()));
            markerApplyVehicle.setMarkerCodes(markerIdAndCodes.stream().map(Pair::second).collect(Collectors.toList()));
            markerApplyVehicles.add(markerApplyVehicle);
        }
        return markerApplyVehicles;
    }


    //@ApiIgnore
    @ApiOperation(value = "获取SidePath资源的占用状态")
    @GetMapping("/SidePathResource")
    @ResponseStatus(value = HttpStatus.OK)
    public List<SidePathApplyVehicle> getSidePathResource() {
        Map<String, List<Pair<String, Pair<String, String>>>> agvCodeToSidePathIds = new ConcurrentHashMap<>();
        Map<String, SidePathResource> poolEntries = sidePathResourcePool.getPoolEntries();
        for (SidePathResource sidePathResource : poolEntries.values()) {
            if (sidePathResource.getApplyAgvCode() != null) {
                String agvCode = sidePathResource.getApplyAgvCode();
                if (agvCodeToSidePathIds.get(agvCode) == null) {
                    List<Pair<String, Pair<String, String>>> list = new ArrayList<>();
                    agvCodeToSidePathIds.put(agvCode, list);
                }
                Pair<String, Pair<String, String>> pair = new Pair<>(sidePathResource.getSidePathId(), new Pair<>(sidePathResource.getNodeU(), sidePathResource.getNodeV()));
                agvCodeToSidePathIds.get(agvCode).add(pair);
            }
        }

        List<SidePathApplyVehicle> sidePathApplyVehicles = new ArrayList<>();
        for (Map.Entry<String, List<Pair<String, Pair<String, String>>>> stringListEntry : agvCodeToSidePathIds.entrySet()) {
            String agvCode = stringListEntry.getKey();
            List<Pair<String, Pair<String, String>>> value = stringListEntry.getValue();
            SidePathApplyVehicle sidePathApplyVehicle = new SidePathApplyVehicle();
            sidePathApplyVehicle.setAgvName(agvCode);
            sidePathApplyVehicle.setSidePathIds(value.stream().map(Pair::first).collect(Collectors.toList()));
            sidePathApplyVehicle.setStartMarkerCodeToEndMarkerCodes(value.stream().map(Pair::second).map(Pair::toString).collect(Collectors.toList()));
            sidePathApplyVehicles.add(sidePathApplyVehicle);
        }
        return sidePathApplyVehicles;
    }

    @ApiOperation(value = "获取单机区域资源的占用状态")
    @GetMapping("/SingleAreaResource")
    @ResponseStatus(value = HttpStatus.OK)
    public List<SingleAreaResourceApplyVehicle> getSingleAreaResource() {
        List<SingleAreaResourceApplyVehicle> singleAreaResourceApplyVehicles = new ArrayList<>();
        Map<String, SingleAreaPathResource> poolEntries = singleAreaResourcePool.getPoolEntries();
        for (SingleAreaPathResource singleAreaResource : poolEntries.values()) {
            if (singleAreaResource.getApplyAgvCode() != null) {
                SingleAreaResourceApplyVehicle singleAreaPathResource = new SingleAreaResourceApplyVehicle();
                singleAreaPathResource.setAgvName(singleAreaResource.getApplyAgvCode());
                singleAreaPathResource.setMarkerIds(singleAreaResource.getZonePathResource());
                Set<String> markerCodes = singleAreaResource.getZonePathResource().stream().map(MapGraphUtil::getMarkerByMarkerId).map(Marker::getCode).collect(Collectors.toSet());
                singleAreaPathResource.setMarkerCodes(markerCodes);
                singleAreaPathResource.setSingleAreaId(singleAreaResource.getId());
                singleAreaResourceApplyVehicles.add(singleAreaPathResource);
            }
        }
        return singleAreaResourceApplyVehicles;
    }

    @ApiOperation(value = "获取T字路资源的占用状态")
    @GetMapping("/TPathResource")
    @ResponseStatus(value = HttpStatus.OK)
    public List<TPathResourceApplyVehicle> getTPathResource(){
        List<TPathResourceApplyVehicle> tPathResourceApplyVehicles = new ArrayList<>();
        Map<String, TPathResource> poolEntries = tPathResourcePool.getPoolEntries();
        for (TPathResource tPathResource : poolEntries.values()){
            if (null != tPathResource.getApplyAgvCode()){
                TPathResourceApplyVehicle tPathResourceApplyVehicle = new TPathResourceApplyVehicle();
                tPathResourceApplyVehicle.setAgvName(tPathResource.getApplyAgvCode());
                tPathResourceApplyVehicle.setMarkerIds(tPathResource.getZonePathResource());
                Set<String> markerCodes = tPathResource.getZonePathResource().stream().map(MapGraphUtil::getMarkerByMarkerId).map(Marker::getCode).collect(Collectors.toSet());
                tPathResourceApplyVehicle.setMarkerCodes(markerCodes);
                tPathResourceApplyVehicle.setTPathId(tPathResource.getId());
                tPathResourceApplyVehicles.add(tPathResourceApplyVehicle);
            }
        }
        return tPathResourceApplyVehicles;
    }

    @ApiOperation(value = "获取一字路资源的占用状态")
    @GetMapping("/OPathResource")
    @ResponseStatus(value = HttpStatus.OK)
    public List<OPathResourceApplyVehicle> getOPathResource(){
        List<OPathResourceApplyVehicle> oPathResourceApplyVehicles = new ArrayList<>();
        Map<String, OPathResource> poolEntries = oPathResourcePool.getPoolEntries();
        for (OPathResource oPathResource : poolEntries.values()){
            if (null != oPathResource.getApplyAgvCode()){
                OPathResourceApplyVehicle oPathResourceApplyVehicle = new OPathResourceApplyVehicle();
                oPathResourceApplyVehicle.setAgvName(oPathResource.getApplyAgvCode());
                oPathResourceApplyVehicle.setMarkerIds(oPathResource.getZonePathResource());
                Set<String> markerCodes = oPathResource.getZonePathResource().stream().map(MapGraphUtil::getMarkerByMarkerId).map(Marker::getCode).collect(Collectors.toSet());
                oPathResourceApplyVehicle.setMarkerCodes(markerCodes);
                oPathResourceApplyVehicle.setOPathId(oPathResource.getId());
                oPathResourceApplyVehicles.add(oPathResourceApplyVehicle);
            }
        }
        return oPathResourceApplyVehicles;
    }

    //@ApiIgnore
    @ApiOperation(value = "获取电梯资源的占用状态")
    @GetMapping("/ElevatorResource")
    @ResponseStatus(value = HttpStatus.OK)
    public List<ElevatorResourceApplyVehicle> getElevatorResource() {
        List<ElevatorResourceApplyVehicle> elevatorResourceApplyVehicles = new ArrayList<>();
        Map<String, ElevatorPathResource> poolEntries = elevatorResourcePool.getPoolEntries();
        for (ElevatorPathResource elevatorResource : poolEntries.values()) {
            if (elevatorResource.getApplyAgvCode() != null) {
                ElevatorResourceApplyVehicle elevatorResourceApplyVehicle = new ElevatorResourceApplyVehicle();
                elevatorResourceApplyVehicle.setAgvName(elevatorResource.getApplyAgvCode());
                elevatorResourceApplyVehicle.setMarkerZone(elevatorResource.getZonePathResource());
                List<String> markerCodes = elevatorResource.getZonePathResource().stream().map(MapGraphUtil::getMarkerByMarkerId).map(Marker::getCode).collect(Collectors.toList());
                elevatorResourceApplyVehicle.setMarkerCodes(markerCodes);
                elevatorResourceApplyVehicle.setElevatorResourceId(elevatorResource.getId());
                elevatorResourceApplyVehicles.add(elevatorResourceApplyVehicle);
            }
        }
        return elevatorResourceApplyVehicles;
    }

    //@ApiIgnore
    @ApiOperation(value = "获取地图路径权重")
    @GetMapping("/DirectEdgeWeight")
    @ResponseStatus(value = HttpStatus.OK)
    public List<DirectEdgeWeight> getDirectEdgeWeight() {
        List<DirectEdgeWeight> directEdgeWeights = new ArrayList<>();
        Set<DirectedEdge> allEdges = MapGraphUtil.getCloneDirectedGraph().getAllEdges();
        for (DirectedEdge edge : allEdges) {
            DirectEdgeWeight directEdgeWeight = new DirectEdgeWeight();
            directEdgeWeight.setACode(edge.getACode());
            directEdgeWeight.setBCode(edge.getBCode());
            directEdgeWeight.setWeightFixed(edge.getOriginWeight());
            directEdgeWeight.setWeightAuto(edge.getWeightAuto());
            directEdgeWeight.setWeightUser(edge.getWeightUser());
            directEdgeWeight.setWeight(edge.getTempWeight());
            directEdgeWeights.add(directEdgeWeight);
        }
        return directEdgeWeights;
    }

    //@ApiIgnore
    @ApiOperation(value = "获取地图不可通行路径权重")
    @GetMapping("/UnreachableDirectEdgeWeight")
    @ResponseStatus(value = HttpStatus.OK)
    public List<DirectEdgeWeight> getUnreachableDirectEdgeWeight() {
        List<DirectEdgeWeight> directEdgeWeights = new ArrayList<>();
        Set<DirectedEdge> allEdges = MapGraphUtil.getCloneDirectedGraph().getAllEdges();
        for (DirectedEdge edge : allEdges) {
            if (edge.getTempWeight() >= Double.MAX_VALUE) {
                DirectEdgeWeight directEdgeWeight = new DirectEdgeWeight();
                directEdgeWeight.setACode(edge.getACode());
                directEdgeWeight.setBCode(edge.getBCode());
                directEdgeWeight.setWeightFixed(edge.getOriginWeight());
                directEdgeWeight.setWeightAuto(edge.getWeightAuto());
                directEdgeWeight.setWeightUser(edge.getWeightUser());
                directEdgeWeight.setWeight(edge.getTempWeight());
                directEdgeWeights.add(directEdgeWeight);
            }
        }
        return directEdgeWeights;
    }

    //@ApiIgnore
    @ApiOperation(value = "重置地图路径权重")
    @GetMapping("/ResetDirectEdgeWeight")
    @ResponseStatus(value = HttpStatus.OK)
    public List<DirectEdgeWeight> resetDirectEdgeWeight() {
        List<DirectEdgeWeight> directEdgeWeights = new ArrayList<>();
        Set<DirectedEdge> allEdges = MapGraphUtil.getCloneDirectedGraph().getAllEdges();
        for (DirectedEdge edge : allEdges) {
            edge.modifyAutoWeight(RESET_WEIGHT, 0d);
            edge.modifyUserWeight(RESET_WEIGHT, 0d);
            DirectEdgeWeight directEdgeWeight = new DirectEdgeWeight();
            directEdgeWeight.setACode(edge.getACode());
            directEdgeWeight.setBCode(edge.getBCode());
            directEdgeWeight.setWeightFixed(edge.getOriginWeight());
            directEdgeWeight.setWeightAuto(edge.getWeightAuto());
            directEdgeWeight.setWeightUser(edge.getWeightUser());
            directEdgeWeight.setWeight(edge.getTempWeight());
            directEdgeWeights.add(directEdgeWeight);
        }
        return directEdgeWeights;
    }

    //@ApiIgnore
    @ApiOperation(value = "获取AGV在路径中的定位数据")
    @GetMapping("/GetLocationInDirectEdge")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AGVPositionInAGVMap> getLocationInDirectEdge() {
        Map<String, StringBuilder> agvCodeToPosition = new HashMap<>();
        Set<DirectedEdge> allEdges = MapGraphUtil.getCloneDirectedGraph().getAllEdges();
        for (DirectedEdge edge : allEdges) {
            ConcurrentHashMap<String, Double> agvPosition = edge.getAgvPosition();
            if (agvPosition.size() > 0) {
                for (Map.Entry<String, Double> stringDoubleEntry : agvPosition.entrySet()) {
                    String agvCode = stringDoubleEntry.getKey();
                    Double t0 = stringDoubleEntry.getValue();
                    if (agvCodeToPosition.get(agvCode) == null) {
                        StringBuilder stringBuilder = new StringBuilder();
                        agvCodeToPosition.put(agvCode, stringBuilder);
                    }
                    agvCodeToPosition.get(agvCode).append(MessageFormat.format("side path:{0}->{1},T0:{2} ", edge.getACode(), edge.getBCode(), t0));
                    //agvCodeToPosition.get(agvCode).append(System.lineSeparator());
                }
            }
        }
        List<AGVPositionInAGVMap> agvPositionInAGVMaps = new ArrayList<>();
        for (Map.Entry<String, StringBuilder> stringStringBuilderEntry : agvCodeToPosition.entrySet()) {
            AGVPositionInAGVMap agvPositionInAGVMap = new AGVPositionInAGVMap();
            agvPositionInAGVMap.setAgvCode(stringStringBuilderEntry.getKey());
            agvPositionInAGVMap.setPosition(stringStringBuilderEntry.getValue().toString());
            agvPositionInAGVMaps.add(agvPositionInAGVMap);
        }
        return agvPositionInAGVMaps;
    }

    //@ApiIgnore
    @ApiOperation(value = "一键同步地图、指定地图")
    @GetMapping("/SyncAndPointMap")
    @ResponseStatus(value = HttpStatus.OK)
    public void syncAndPointMap() {
//        List<Vehicle> vehicles = vehiclePool.getAll().stream().filter(vehicle -> ENABLE.equals(vehicle.getStatus())).collect(Collectors.toList());
//        List<AGVMap> agvMaps = agvMapService.searchAll(new HashMap<>()).stream().filter(agvMap -> MAP_USAGE_STATUS_ENABLE.equals(agvMap.getUsageStatus())).collect(Collectors.toList());
//
//        vehicles.forEach(vehicle -> {
//                    try {
//                        if (MANUAL_CONTROL_MODE.equals(vehicle.getControlMode())) {
//                            return;
//                        }
//                        vehicle.setControlMode(MANUAL_CONTROL_MODE);//手动控制模式
//                        vehicleCommandService.switchManualMode(vehicle.getId());
//                    } catch (Exception e) {
//                        throw new ExecuteException(e.getMessage());
//                    }
//                }
//        );
//
//        vehicles.forEach(vehicle -> {
//            if (MAP_STATUS_SYNC.equals(vehicle.getMapStatus())) {
//                return;
//            }
//            Long start = System.currentTimeMillis();
//            agvMapService.syncMap(vehicle.getId(), agvMaps);
//            try {
//                while (MAP_STATUS_NOT_SYNC.equals(vehicle.getMapStatus()) && Math.abs(System.currentTimeMillis() - start) < 30 * 1000) {
//                    Thread.sleep(20);
//                }
//            } catch (Exception e) {
//                throw new ExecuteException(e.getMessage());
//            }
//        });
//        if (!CollectionUtils.isEmpty(agvMaps)) {
//            AGVMap agvMap = agvMaps.get(0);
//            vehicles.forEach(vehicle -> {
//                mapCommandService.appointCurrentMap(vehicle.getId(), agvMap.getId());
//                try {
//                    Thread.sleep(20);
//                } catch (Exception e) {
//                    throw new ExecuteException(e.getMessage());
//                }
//            });
//        }
//
//        try {
//            Thread.sleep(5000);
//        } catch (Exception e) {
//            throw new ExecuteException(e.getMessage());
//        }
//        List<Marker> markers = new ArrayList<>(MapGraphUtil.getEnableMarkers());
//        int i = 0;
//        for (Vehicle v : vehicles) {
//            if (i >= vehicles.size()) {
//                i = 0;
//            }
//            Marker marker = markers.get(i++);
//            RelocationMessage relocationMessage = new RelocationMessage();
//            relocationMessage.setAgvCode(v.getId());
//            relocationMessage.setType("manualRelocation");
//            relocationMessage.setX(marker.getX());
//            relocationMessage.setY(marker.getY());
//            relocationMessage.setAngle(0d);
//            v.manualRelocation(relocationMessage);
//            try {
//                Thread.sleep(20);
//            } catch (Exception e) {
//                throw new ExecuteException(e.getMessage());
//            }
//        }
    }

    //@ApiIgnore
    @ApiOperation(value = "一键切换自动模式")
    @GetMapping("/switchAutoMode")
    @ResponseStatus(value = HttpStatus.OK)
    public void switchAutoMode() {
        List<Vehicle> vehicles = vehiclePool.getAll().stream().filter(vehicle -> ENABLE.equals(vehicle.getStatus())).collect(Collectors.toList());
        vehicles.forEach(vehicle -> {
                    try {
                        if (AUTO_CONTROL_MODE.equals(vehicle.getControlMode())) {
                            return;
                        }
                        vehicle.setControlMode(AUTO_CONTROL_MODE);//手动控制模式
                        vehicleCommandService.switchAutoMode(vehicle.getId());
                        Thread.sleep(20);
                    } catch (Exception e) {
                        throw new ExecuteException(e.getMessage());
                    }
                }
        );
    }

    //@ApiIgnore
    @ApiOperation(value = "一键重置")
    @GetMapping("/OneKeyReset")
    @ResponseStatus(value = HttpStatus.OK)
    public void oneKeyReset() {
        List<Vehicle> vehicles = vehiclePool.getAll().stream().filter(vehicle -> ENABLE.equals(vehicle.getStatus())).collect(Collectors.toList());
        vehicles.forEach(vehicle -> {
                    try {
                        if (ABNORMAL_STATUS_NO.equals(vehicle.getAbnormalStatus())) {
                            return;
                        }
                        vehicle.oneKeyReset();
                    } catch (Exception e) {
                        throw new ExecuteException(e.getMessage());
                    }
                }
        );
    }

    @Data
    public static class AgvSidePath {
        private String agvName;
        private LinkedList<TempSidePath> sidePaths;
        private ReportSidePathData reportSidePathData;
        private  int index = -1 ;
        public  boolean isValid(){
            return index>=0 && Objects.nonNull( reportSidePathData) && Objects.nonNull( reportSidePathData.getSidePath()) && index <= reportSidePathData.getSidePath().getPreAmount();
        }
    }

    @Data
    public static class TempSidePath {
        private String sidePathId;
        private String startMarkerCode;
        private String endMarkerCode;
        private Double t0;
        private Double t1;
    }

    @Data
    private class MarkerApplyVehicle {
        private String agvName;
        private List<String> markerIds;
        private List<String> markerCodes;
    }

    @Data
    private class SidePathApplyVehicle {
        private String agvName;
        private List<String> SidePathIds;
        private List<String> startMarkerCodeToEndMarkerCodes;
    }

    @Data
    public final static class SingleAreaResourceApplyVehicle {
        private String agvName;
        private Set<String> markerIds;
        private Set<String> markerCodes;
        private String SingleAreaId;
    }

    @Data
    private class TPathResourceApplyVehicle {
        private String agvName;
        private Set<String> markerIds;
        private Set<String> markerCodes;
        private String tPathId;
    }

    @Data
    private class OPathResourceApplyVehicle {
        private String agvName;
        private Set<String> markerIds;
        private Set<String> markerCodes;
        private String oPathId;
    }

    @Data
    private class ElevatorResourceApplyVehicle {
        private String agvName;
        private Set<String> markerZone;
        private String pathResourceId;
        private List<String> markerCodes;
        private String elevatorResourceId;
    }

    @Data
    private class DirectEdgeWeight {
        private String aCode;
        private String bCode;
        private Double weightFixed;
        private Double weightAuto;
        private Double weightUser;
        private Double weight;
    }

    @Data
	public class AGVPositionInAGVMap {
        private String agvCode;
        private String position;
        
        
    }

}
