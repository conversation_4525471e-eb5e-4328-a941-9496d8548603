package com.youibot.agv.scheduler.controller.v3;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/30 15:46
 */
@RestController("aliveControllerV3")
@RequestMapping(value = "/api/v3/alives", produces = "application/json")
@Api(value = "心跳检测", tags = "心跳检测", description = "提供给业务系统调用，从而检测调度系统是否启动成功")
public class AliveController {

    @ApiOperation(value = "心跳检测", notes = "根据该接口完成心跳检测")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public void heartbeat() {

    }

}
