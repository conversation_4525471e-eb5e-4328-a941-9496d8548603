package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.BlockCheckService;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

/**
 * 系统配置
 *
 * @Author：yangpeilin
 * @Date: 2020/5/9 16:42
 */
@RestController("SchedulerConfigController")
@RequestMapping("/api/v3/schedulerConfig")
@Api(value = "调度配置", tags = "调度配置", produces = "application/json")
public class SchedulerConfigController {

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    @Autowired
    private BlockCheckService blockCheckService;

    @ApiOperation(value = "查看调度配置")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public SchedulerConfig getSystemConfig() {
        return schedulerConfigService.findAll().get(0);
    }

    @ApiOperation(value = "修改调度配置")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "调度配置ID", paramType = "path", required = true, dataType = "Long"),
            @ApiImplicitParam(name = "schedulerConfig", value = "=调度配置参数", required = true, dataType = "SchedulerConfig")})
    @PutMapping("/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public void updateSystemConfig(@PathVariable("id") Long id, @RequestBody SchedulerConfig schedulerConfig) {
        schedulerConfig.setId(id);

//        blockCheckService.setBlockCheckInterval(schedulerConfig.getBlockCheckInterval());
//        blockCheckService.setRemoveBlockInterval(schedulerConfig.getRemoveBlockInterval());
        schedulerConfigService.update(schedulerConfig);
        if (schedulerConfig.getBlockCheckEnable().equals(1)) {
            blockCheckService.startBlockCheckThread();
        } else {
            blockCheckService.closeBlockCheckThread();
        }
    }

}
