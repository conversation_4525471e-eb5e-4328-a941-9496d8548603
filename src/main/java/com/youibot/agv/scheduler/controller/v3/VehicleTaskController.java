package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.dto.MoveToMarkerParam;
import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThread;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThreadPool;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_WAIT;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_WAIT_INPUT;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/7/20 15:16
 */
@RestController("VehicleTaskControllerV3")
@RequestMapping(value = "/api/v3/vehicles/{agvId}/task", produces = "application/json")
@Api(value = "机器人动作、任务", tags = "机器人动作、任务", description = "对机器人进行一系列动作、任务操作，如自由导航，任务一键停止、一键恢复等。")
public class VehicleTaskController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleTaskController.class);

    @Autowired
    private MissionWorkThreadPool missionWorkThreadPool;

    @Autowired
    private AGVService agvService;

    @ApiOperation(value = "路径导航到指定标记点")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/moveToMarker")
    @ResponseStatus(value = HttpStatus.OK)
    public void move(@RequestBody MoveToMarkerParam param, @PathVariable("agvId") String agvId) throws InterruptedException, IOException, PathPlanException {
        Vehicle vehicle = VehicleUtils.getOnlineVehicle(agvId);
        String markerId = param.getMarkerId();
        if (StringUtils.isEmpty(markerId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        if (!MANUAL_CONTROL_MODE.equals(vehicle.getControlMode())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
        }
        vehicle.moveToMarker(markerId, "", vehicle, null);
    }

    @ApiOperation(value = "继续执行任务")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/missionWork/continue")
    @ResponseStatus(value = HttpStatus.OK)
    public void workContinue(@PathVariable("agvId") String agvId) throws InterruptedException {
        Vehicle vehicle = VehicleUtils.getOnlineVehicle(agvId);
        MissionWork missionWork = vehicle.getMissionWork();
        if (missionWork == null) {
            LOGGER.error("agv has no execute mission work now");
            throw new ExecuteException(MessageUtils.getMessage("http.agv_has_no_execute_work"));
        }
        if (!MISSION_WORK_STATUS_WAIT.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
            LOGGER.error("mission work status is not wait or wait input");
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_wait_or_wait_input"));
        }
        MissionWorkThread missionWorkThread = missionWorkThreadPool.get(missionWork.getId());
        if (missionWorkThread == null || !AUTO_CONTROL_MODE.equals(vehicle.getControlMode())
                || !MAP_STATUS_NORMAL.equals(vehicle.getMapStatus()) || !TASK_STATUS_WORK.equals(vehicle.getWorkStatus())) {
            LOGGER.error(MessageUtils.getMessage("vehicle.not_ready_for_the_mission"));
            throw new ExecuteException(MessageUtils.getMessage("vehicle.not_ready_for_the_mission"));
        }
        missionWorkThread.continueWork();
    }

    @ApiOperation(value = "AGV一键恢复(充电异常恢复、归位异常恢复、任务异常恢复)")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/oneKeyResume")
    @ResponseStatus(value = HttpStatus.OK)
    public void oneKeyResume(@PathVariable("agvId") String agvId) throws InterruptedException {
        VehicleUtils.getOnlineVehicle(agvId).oneKeyResume();
    }

    @ApiOperation(value = "AGV一键停止(停止充电动作、归位动作、任务工作)")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/oneKeyStop")
    @ResponseStatus(value = HttpStatus.OK)
    public void oneKeyStop(@PathVariable("agvId") String agvId) throws IOException, InterruptedException {
        Vehicle vehicle = VehicleUtils.getOnlineVehicle(agvId);
        if (TASK_STATUS_FREE.equals(vehicle.getWorkStatus()) && MANUAL_CONTROL_MODE.equals(vehicle.getControlMode())) {
            vehicle.stopManualTask();
        } else {
            vehicle.oneKeyStop();
        }
    }

    @ApiOperation(value = "AGV一键重置(清错充电异常、归位异常、工作异常)")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/oneKeyReset")
    @ResponseStatus(value = HttpStatus.OK)
    public void oneKeyReset(@PathVariable("agvId") String agvId) throws IOException, InterruptedException {
        Vehicle vehicle = VehicleUtils.getOnlineVehicle(agvId);
        if (TASK_STATUS_FREE.equals(vehicle.getWorkStatus()) && MANUAL_CONTROL_MODE.equals(vehicle.getControlMode())) {
            vehicle.resetManualTask();
        } else {
            vehicle.oneKeyReset();
        }
    }

}
