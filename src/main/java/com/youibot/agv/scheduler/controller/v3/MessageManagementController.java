package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MessageManagementService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import org.eclipse.jetty.annotations.AbstractDiscoverableAnnotationHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@RestController("messageManagementControllerV3")
@RequestMapping(value = "/api/v3/messageManagement", produces = "application/json")
@Api(value = "消息管理界面", tags = "消息管理界面", description = "异常信息的增删改查及导出、导入功能")
public class MessageManagementController {

    @Autowired
    private MessageManagementService messageManagementService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AbnormalPrompt> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
    	 PageInfo<AbnormalPrompt> info = messageManagementService.findPage(searchMap);
    	 info.setList( convert( info.getList()));
        return info;
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<AbnormalPrompt> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return convert( messageManagementService.searchAll(searchMap));
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "abnormalPrompt", value = "异常信息", required = true, dataType = "AbnormalPrompt")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public void create(@RequestBody @Valid AbnormalPrompt abnormalPrompt) {
        this.messageManagementService.insert(abnormalPrompt);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "异常信息ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AbnormalPrompt get(@PathVariable("id") String id) {
        return messageManagementService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParam(name = "abnormalPrompt", value = "异常信息", required = true, dataType = "AbnormalPrompt")
    @PutMapping
    @ResponseStatus(value = HttpStatus.OK)
    public AbnormalPrompt update(@RequestBody @Valid AbnormalPrompt abnormalPrompt) {
        this.messageManagementService.update(abnormalPrompt);
        return this.messageManagementService.selectById(abnormalPrompt.getId());
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "异常信息ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.messageManagementService.deleteById(id);
    }

    @ApiOperation(value = "获取异常类型列表")
    @GetMapping(value = "/getAbnormalTypeList")
    @ResponseStatus(value = HttpStatus.OK)
    public List<String> getAbnormalTypeList() {
        return messageManagementService.getAbnormalTypeList();
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "/uploadExcelFile")
    @ApiImplicitParam(name = "multiPartFile", value = "异常信息文件", required = true, dataType = "MultiPartFile")
    @ResponseStatus(value = HttpStatus.OK)
    public void uploadExcelFile(@RequestBody MultipartFile multiPartFile) {
        if (multiPartFile == null || multiPartFile.isEmpty()) {
            throw new ExecuteException(MessageUtils.getMessage("service.file_missing_parameter"));
        }
        messageManagementService.uploadExcelFile(multiPartFile);
    }

    @ApiOperation(value = "导出")
    @GetMapping("/downExcelFile")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "abnormalLevel", value = "异常等级",paramType = "path", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "abnormalType", value = "异常类型",paramType = "path", required = false, dataType = "String"),
            @ApiImplicitParam(name = "abnormalCode", value = "异常编码",paramType = "path", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "abnormalDescription", value = "异常描述",paramType = "path", required = false, dataType = "String")
    })
    @ResponseStatus(value = HttpStatus.OK)
    public void downExcelFile(@RequestParam(required = false) Integer abnormalLevel, @RequestParam(required = false) String abnormalType
            , @RequestParam(required = false) Integer abnormalCode, @RequestParam(required = false) String abnormalDescription, HttpServletResponse response) {
        messageManagementService.downExcelFile(abnormalLevel, abnormalType, abnormalCode, abnormalDescription, response);
    }
    
      List<AbnormalPrompt>  convert( List<AbnormalPrompt>  list){
    	boolean isUs = MessageUtils.getLocale() == Locale.US;
    	if(isUs) {
    		list = list.parallelStream().map( item -> {;
    		 item.setHelp( item.getEnSlolution()) ;
    		 item.setAbnormalDescription( item.getEnDesc()) ;
    		return item;
    		}).collect(Collectors.toList());
    	}
    	return list;
    }
}
