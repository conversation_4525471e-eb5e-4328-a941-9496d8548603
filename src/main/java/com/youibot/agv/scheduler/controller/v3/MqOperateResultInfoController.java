package com.youibot.agv.scheduler.controller.v3;

import java.util.List;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.youibot.agv.scheduler.entity.MqOperateResultInfo;
import com.youibot.agv.scheduler.service.MqOperateResultInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * mq返回结果
 *
 * <AUTHOR>
 * @date 2020/9/1 11:37
 */
@RestController
@RequestMapping(value = "/api/v3/mq", produces = "application/json")
@Api(value = "mq操作结果", tags = "mq操作结果", description = "操作结果详情")
public class MqOperateResultInfoController {

    private static final Logger logger = LoggerFactory.getLogger(MqOperateResultInfoController.class);
    private static final long QUERY_TIMEOUT = TimeUnit.SECONDS.toMillis(60);

    @Autowired
    private MqOperateResultInfoService mqOperateResultInfoService;

    @ApiOperation(value = "通过agvCode+操作类型查询结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvCode", value = "机器人的编码", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "operateType", value = "操作类型: 同步地图syncMap，指定地图appointMap，手动重定位manualRelocation，自动重定位autoRelocation，\n" +
                    "    初始点重定位homeRelocation，一键停止oneKeyStop，一键重置oneKeyReset，一键恢复oneKeyResume，自动模式autoMode，手动模式manualMode", paramType = "query", required = true, dataType = "String")
    })
    @GetMapping("/{agvCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public MqOperateResultInfo getOperateResult(@PathVariable("agvCode") String agvCode, @RequestParam("operateType") String operateType) {
     
        MqOperateResultInfo mqOperateResultInfo = new MqOperateResultInfo();
        mqOperateResultInfo.setAgvCode(agvCode);
        mqOperateResultInfo.setOperateType(operateType);
        mqOperateResultInfo.setHaveRead(0);
        List<MqOperateResultInfo> byEntity = mqOperateResultInfoService.findByEntity(mqOperateResultInfo);
        logger.debug("agvCode:[{}],event:[机器人操作结果返回],content:{}", agvCode,  CollectionUtils.isEmpty(byEntity) ? null : byEntity.get(0));
   
        return CollectionUtils.isEmpty(byEntity) ? null : byEntity.get(0);
    }

    @ApiOperation(value = "获取结果后调用")
    @ApiImplicitParam(name = "id", value = "ID", paramType = "path", required = true, dataType = "String")
    @PostMapping("/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MqOperateResultInfo updateReadStatus(@PathVariable("id") String id) {
        MqOperateResultInfo mqOperateResultInfo = new MqOperateResultInfo();
        mqOperateResultInfo.setId(id);
        mqOperateResultInfo.setHaveRead(1);
        mqOperateResultInfoService.updateByPrimaryKeySelective(mqOperateResultInfo);
        return mqOperateResultInfoService.selectById(id);
    }

    @ApiOperation(value = "批量通过agvCode+操作类型查询结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvCodes", value = "机器人的编码列表", required = true, dataType = "String"),
            @ApiImplicitParam(name = "operateType", value = "操作类型: 同步地图syncMap，指定地图appointMap，手动重定位manualRelocation，自动重定位autoRelocation，\n" +
                    "    初始点重定位homeRelocation，一键停止oneKeyStop，一键重置oneKeyReset，一键恢复oneKeyResume，自动模式autoMode，手动模式manualMode", paramType = "query", required = true, dataType = "String")
    })
    @GetMapping("")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MqOperateResultInfo> getOperateBatchResult(@RequestParam("agvCodes") List<String> agvCodes, @RequestParam("operateType") String operateType) {
        long startTime = System.currentTimeMillis();
        List<MqOperateResultInfo> resultList = mqOperateResultInfoService.findOperateResultBatch(agvCodes, operateType);
        while (resultList.size() < agvCodes.size() && System.currentTimeMillis() - startTime < QUERY_TIMEOUT) {
            resultList = mqOperateResultInfoService.findOperateResultBatch(agvCodes, operateType);
        }
        return resultList;
    }


    @ApiOperation(value = "批量获取结果后调用")
    @ApiImplicitParam(name = "ids", value = "结果ID", required = true, dataType = "String")
    @GetMapping("/batch")
    @ResponseStatus(value = HttpStatus.OK)
    public void updateReadStatusBatch(@RequestParam List<String> ids) {
        for (String id : ids) {
            MqOperateResultInfo mqOperateResultInfo = new MqOperateResultInfo();
            mqOperateResultInfo.setId(id);
            mqOperateResultInfo.setHaveRead(1);
            mqOperateResultInfoService.updateByPrimaryKeySelective(mqOperateResultInfo);
        }
    }

}
