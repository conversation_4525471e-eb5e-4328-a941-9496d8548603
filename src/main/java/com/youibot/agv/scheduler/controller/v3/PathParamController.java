package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.PathParam;
import com.youibot.agv.scheduler.service.PathParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("pathParamControllerV3")
@RequestMapping(value = "/api/v3/pathParams", produces = "application/json")
@Api(value = "路径参数", tags = "路径参数", description = "路径参数的管理接口，存储路径的属性参数，如是否打开避障，最大平移速度。用于agv通过路径时的限制。")
public class PathParamController extends BaseController {

    @Autowired
    private PathParamService pathParamService;

    private static final Logger LOGGER = LoggerFactory.getLogger(PathParamController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<PathParam> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return pathParamService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<PathParam> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return pathParamService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "pathParam", value = "路径参数", required = true, dataType = "PathParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public PathParam save(@RequestBody @Valid PathParam pathParam) {
        this.pathParamService.insert(pathParam);
        LOGGER.debug(pathParam.toString());
        return pathParam;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "路径参数ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public PathParam get(@PathVariable("id") String id) {
        return pathParamService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "路径参数ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pathParam", value = "路径参数", required = true, dataType = "PathParam")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public PathParam update(@PathVariable("id") String id, @RequestBody @Valid PathParam pathParam) {
        pathParam.setId(id);
        this.pathParamService.update(pathParam);
        return this.pathParamService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "路径参数ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.pathParamService.deleteById(id);
    }
}
