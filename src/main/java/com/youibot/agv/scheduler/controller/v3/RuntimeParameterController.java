package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.RuntimeParameter;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.service.RuntimeParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime:2019/7/24 14:25
 */
@RestController("runtimeParameterControllerV3")
@RequestMapping(value = "/api/v3/runtimeParameters", produces = "application/json")
@Api(value = "运行时参数", tags = "运行时参数", description = "运行时参数，任务在创建的时候有一些参数不确定，可以在任行运行的过程再输入参数。")
public class RuntimeParameterController extends BaseController {

    @Autowired
    private RuntimeParameterService runtimeParameterService;

    @ApiOperation(value = "批量更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "runtimeParameters", value = "运行时参数", required = true, dataType = "RuntimeParameter", allowMultiple = true)})
    @PutMapping("/batch")
    @ResponseStatus(value = HttpStatus.OK)
    public void batchUpdate(@RequestBody @Valid RuntimeParameter[] runtimeParameters) {
        if (runtimeParameters == null || runtimeParameters.length == 0) {
            throw new ADSParameterException("runtimeParameters is null");
        }
        List<RuntimeParameter> parameterList = Arrays.asList(runtimeParameters);
        runtimeParameterService.batchUpdate(parameterList);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParam(name = "id", value = "动态参数Id", paramType = "path", required = true, dataType = "String")
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public RuntimeParameter update(@PathVariable("id") String id, @RequestBody @Valid RuntimeParameter runtimeParameter) {
        runtimeParameter.setId(id);
        this.runtimeParameterService.update(runtimeParameter);
        return runtimeParameterService.selectById(id);
    }
}
