package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.vo.TriggerSelectorDetailVO;
import com.youibot.agv.scheduler.constant.vo.TriggerSelectorVO;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.TriggerSelector;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.service.TriggerSelectorService;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/1/7 16:59
 * @Description: 触发器
 */
@Api(value = "触发器", tags = "触发器")
@RequestMapping("/api/v3/triggerSelectors")
@RestController
public class TriggerSelectorController extends BaseController {

    @Autowired
    private TriggerSelectorService triggerSelectorService;
    @Autowired
    private MissionService missionService;


    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "code", value = "触发器编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型:1:定时触发、2呼叫器触发、3传感器触发", required = false, dataType = "int", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(HttpStatus.OK)
    public PageInfo<TriggerSelector> findByPage(@RequestParam(required = false) Map<String, String> searchMap) {
        return triggerSelectorService.findPage(searchMap);
    }

    @LogOperation
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "triggerSelector", value = "触发器", required = true, dataType = "TriggerSelector")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public String createTriggerSelector(@RequestBody @Validated TriggerSelector triggerSelector) {
        return triggerSelectorService.create(triggerSelector);
    }

    @ApiOperation(value = "触发器详情")
    @GetMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public TriggerSelectorVO detail(@PathVariable String id) {
        TriggerSelector triggerSelector = triggerSelectorService.selectById(id);
        if (Objects.isNull(triggerSelector.getMissionId())) {
            throw new YOUIFleetException(MessageUtils.getMessage("trigger_missing_parameter"));
        }
        Mission mission = missionService.selectById(triggerSelector.getMissionId());
        TriggerSelectorVO triggerSelectorVO = new TriggerSelectorVO();
        BeanUtils.copyPropertiesIgnoreNull(triggerSelector, triggerSelectorVO);
        if(Objects.nonNull(mission)) {
        	triggerSelectorVO.setMissionCode(mission.getCode());
            triggerSelectorVO.setMissionName(mission.getName());
        }
        
        return triggerSelectorVO;
    }

    @LogOperation
    @ApiOperation(value = "修改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "触发器ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "triggerSelector", value = "TriggerSelector", required = true, dataType = "TriggerSelector")
    })
    @PutMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public void updateTriggerSelector(@PathVariable String id, @RequestBody @Validated TriggerSelector triggerSelector) {
        triggerSelectorService.updateTriggerSelector(id, triggerSelector);
    }

    @LogOperation
    @ApiOperation(value = "删除")
    @DeleteMapping("/{id}")
    public void deleted(@PathVariable String id) {
        triggerSelectorService.deleteTrigger(id);
    }

    @ApiOperation(value = "检索触发器下的所有任务记录")
    @GetMapping("/{id}/missionWorks")
    public TriggerSelectorDetailVO findMissionWorksById(@PathVariable String id, @RequestParam(required = false) Map<String, String> searchMap) {
        return triggerSelectorService.findMissionWorkById(id, searchMap);
    }
}
