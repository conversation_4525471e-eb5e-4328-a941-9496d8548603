package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.constants.dto.ElevatorCallbackNotifyDTO;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: Tianshu.CHU
 * @Date: 2020/10/29 16:05
 * @Description: 申请电梯资源回调
 */
@Slf4j
@RestController
@RequestMapping("/elevatornotice")
public class ElevatorCallbackController {

    @PostMapping
    public void callback(@RequestBody ElevatorCallbackNotifyDTO.ElevatorCallbackDTO callbackBody) {
        log.info("接收到电梯申请回调:{}", JSONObject.toJSONString(callbackBody));
        ElevatorCallbackNotifyDTO callbackNotifyDTO = new ElevatorCallbackNotifyDTO();
        callbackNotifyDTO.setParam(callbackBody);
        MqttUtils.sendMqttMsg(MqttConstant.ELEVATOR_CALLBACK_RESULT, callbackBody.getRequestid(), callbackNotifyDTO);
//        mqttSendService.sendMqttMsg(MqttConstant.ELEVATOR_CALLBACK_RESULT, callbackBody.getRequestid(), callbackNotifyDTO);
        log.info("电梯回调发送消息给单机,topic:{},message:{}", MqttConstant.ELEVATOR_CALLBACK_RESULT.replace("{agvCode}", callbackBody.getRequestid()), JSON.toJSONString(callbackNotifyDTO));
    }

}
