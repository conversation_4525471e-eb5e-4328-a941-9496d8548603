package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.annotation.LicenseVerifys;
import com.youibot.agv.scheduler.constant.LicenseConstant;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.license.License;
import com.youibot.agv.scheduler.license.LicenseDto;
import com.youibot.agv.scheduler.license.VerifyLicense;
import com.youibot.agv.scheduler.service.LicenseService;
import com.youibot.agv.scheduler.util.Base64Utils;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import de.schlichtherle.license.LicenseContent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * license认证
 *
 * @Author：yangpeilin
 * @Date: 2020/4/20 15:47
 */
@RestController("LicenseController")
@Api(value = "证书上传", tags = "证书上传")
@RequestMapping(value = "/api/v3/license", produces = "application/json")
public class LicenseController {

    Logger logger = LoggerFactory.getLogger(LicenseController.class);

    @Autowired
    private LicenseService licenseService;

    @Value("${LICENSE.SUBJECT}")
    private String subject;

    @Value("${LICENSE.PUBLICALIAS}")
    private String publicalias;

    @Value("${LICENSE.PUBLICKEYSSTOREPATH}")
    private String pubPath;

    @Value("${LICENSE.STOREPASS}")
    private String storePass;

    @ApiOperation("上传license文件")
    @ApiImplicitParam(name = "mutiPartFile", value = "文件", required = true, dataType = "MultipartFile")
    @PostMapping(value = "/valiadateLicense")
    @ResponseStatus(value = HttpStatus.OK)
    @LicenseVerifys
    public LicenseDto validateLicense(@RequestBody MultipartFile mutiPartFile) throws Exception {
        VerifyLicense vLicense = initVerifyLicense();
        //生成证书
        LicenseContent licenseContent = null;
        try {
            licenseContent = vLicense.verifyFile(mutiPartFile.getBytes());
            if (licenseContent != null) {
                //将相关信息保存至数据库，并生成一份缓存
                License license = saveLicense(mutiPartFile.getOriginalFilename(), licenseContent);
                return getLicenseParam(license);
            } else {
                throw new ExecuteException(MessageUtils.getMessage("http.license_certificate_validate_failed"));
            }
        } catch (Exception e) {
            logger.error("validateLicense error ：" + e.getMessage());
            String msg;
            if (e instanceof ExecuteException) {
                msg = e.getMessage();
            } else {
                msg = MessageUtils.getMessage("http.license_certificate_failure");
            }
            throw new ExecuteException(msg);
        }
    }

    @ApiOperation("获取证书信息")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    @LicenseVerifys
    public LicenseDto getLicense() {
        List<License> licenseList = licenseService.findAll();
        if (CollectionUtils.isEmpty(licenseList)) {
            throw new ExecuteException(MessageUtils.getMessage("http.license_certificate_not_uploaded"));
        }
        License license = licenseList.get(0);
        return getLicenseParam(license);
    }

    @ApiOperation("删除获取证书信息")
    @DeleteMapping
    @ResponseStatus(value = HttpStatus.OK)
    @LicenseVerifys
    public void deleteLicense() {
        LicenseConstant.licenseMap.clear();
        List<License> all = licenseService.findAll();
        if (!CollectionUtils.isEmpty(all)){
            licenseService.delete(all.get(0));
        }
    }

    private LicenseDto getLicenseParam(License license) {
        LicenseDto licenseParam = new LicenseDto();
        BeanUtils.copyPropertiesIgnoreNull(license, licenseParam);
        long startTime = license.getStartTime().getTime();
        long endTime = license.getEndTime().getTime();
        long now = System.currentTimeMillis();
        Integer type;//0已过期，1生效中，2未生效
        if (startTime > now) {
            type = 2;
        } else if (endTime > now) {
            type = 1;
        } else {
            type = 0;
        }
        licenseParam.setType(type);
        return licenseParam;
    }

    /**
     * 保存license文件相关信息至数据库中
     *
     * @param fileName
     * @param licenseContent
     */
    private License saveLicense(String fileName, LicenseContent licenseContent) {
        Object object = JSON.parse(licenseContent.getInfo());
        if (object != null) {
            Map<String, Object> map = (Map<String, Object>) object;
            if (!CollectionUtils.isEmpty(map)) {
                License license = new License();
                map.forEach((k, v) -> {
                    switch (k) {
                        case "companyName":
                            license.setCompanyName(v.toString());
                            break;
                        case "serverType":
                            license.setServerType(v.toString());
                            break;
                        case "startTime":
                            license.setStartTime(new Date((long) v));
                            break;
                        case "endTime":
                            license.setEndTime(new Date((long) v));
                            break;
                        default:
                            break;
                    }
                });
                license.setFileId(fileName);
                //若原先有证书数据则先清理数据库
                Map<String, License> licenseMap = LicenseConstant.licenseMap;
                if (!CollectionUtils.isEmpty(licenseMap)) {
                    logger.debug("clear LicenseConstant licenseMap");
                    licenseMap.clear();
                }
                List<License> licenseList = licenseService.findAll();
                if (!CollectionUtils.isEmpty(licenseList)) {
                    licenseList.forEach(dto -> {
                        //进行比较，若证书生效时间、结束时间、公司名称、服务类型四者一致 或者 文件id都一致 则表明是同一证书，不给保存。
                        if (compareLicense(license, dto)) {
                            throw new ExecuteException(MessageUtils.getMessage("http.license_certificate_has_been_uploaded"));
                        }
                        licenseService.delete(dto);
                    });
                }
                //保存至数据库
                licenseService.insert(license);
                return license;
            }
        }
        return null;
    }

    private boolean compareLicense(License license, License dto) {
        return (license.getStartTime().equals(dto.getStartTime()) && license.getEndTime().equals(dto.getEndTime()) && license.getServerType().equals(dto.getServerType()) &&
                license.getCompanyName().equals(dto.getCompanyName())) || license.getFileId().equals(dto.getFileId());
    }

    private VerifyLicense initVerifyLicense() throws Exception {
        VerifyLicense verifyLicense = new VerifyLicense();
        verifyLicense.setPublicalias(publicalias);
        verifyLicense.setPubPath(pubPath);
        verifyLicense.setStorepwd(Base64Utils.decryptBASE64(storePass));
        verifyLicense.setSubject(subject);
        return verifyLicense;
    }

}
