package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.dto.AgvAddParam;
import com.youibot.agv.scheduler.engine.entity.MapResourceCache;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.entity.AGVPathParam;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.service.MqVehicleService;
import com.youibot.agv.scheduler.service.AGVPathParamService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.SystemWorkModeService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.util.*;

import static com.youibot.agv.scheduler.constant.MapConstant.MARKER_TYPE_ELEVATOR;

@RestController("agvTestControllerV3")
@RequestMapping(value = "/api/v3/agvsTest", produces = "application/json")
@Api(value = "AGV", tags = "AGV测试", description = "AGV管理接口，用户可以通过接口修改AGV基本信息。")
public class AGVTestController extends BaseController {

    @Autowired
    private AGVService agvService;
    @Autowired
    private MqVehicleService mqVehicleService;
    @Autowired
    private SystemWorkModeService systemWorkModeService;
    @Autowired
    private VehiclePool vehiclePool;
    @Autowired
    private AGVPathParamService agvPathParamService;


    @ApiOperation(value = "批量创建AGV")
    @ApiImplicitParam(name = "agvAddParam", value = "创建", required = true, dataType = "AgvAddParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public List<AGV> save(@RequestBody @Valid AgvAddParam agvAddParam) {
        List<AGV> result =new ArrayList<>();
        if(agvAddParam.getStartCode()==null || agvAddParam.getNumber()==null || StringUtils.isEmpty(agvAddParam.getSchedulerSystemsId())){
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        for (int i = 0; i < agvAddParam.getNumber(); i++) {
            Example example = new Example(AGV.class);
            String agvCode = "Test" + (agvAddParam.getStartCode() + i);
            example.createCriteria().andEqualTo("agvCode", agvCode);
            if (agvService.selectCountByExample(example) > 0) {
                continue;
            }
            AGV agv = new AGV();
            agv.setAgvCode(agvCode);
            agv.setName(agvCode);
            agv.setNavigationType("LASER");
            agv.setDeviceModel("test");
            agv.setSchedulerSystemsId(agvAddParam.getSchedulerSystemsId());
            this.agvService.insert(agv);
            //添加默认值
            AGVPathParam agvPathParam = new AGVPathParam();
            agvPathParam.setAgvId(agv.getId());
            agvPathParam.setMax_translation_speed(0.8);
            agvPathParam.setMax_rotate_speed(0.8);
            agvPathParam.setTranslation_acc(0.5);
            agvPathParam.setRotate_acc(0.6);
            agvPathParamService.insert(agvPathParam);
            vehiclePool.attachVehicle(agv);
            result.add(agv);
        }
        return result;

    }


    @ApiOperation(value = "一键自动重定位", notes = "vehicle自动重定位")
    @PostMapping(value = "/relocation/manual")
    @ResponseStatus(value = HttpStatus.OK)
    public String manualRelocation() {
        List<Vehicle> vehicleList = vehiclePool.getAll();
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("自动重定位的AGV CODE: ");
        Map<String, String> markerMap = new HashMap<>();
        Random random = new Random();
        for (Vehicle vehicle : vehicleList) {
            DefaultVehicleStatus.MapStatus agvMap = vehicle.getDefaultVehicleStatus().getMap();
            String current_map_id = agvMap.getCurrent_map_id();
            if (!StringUtils.isEmpty(current_map_id)) {
                List<Marker> markerList = new ArrayList<>(MapResourceCache.getMarkersByAGVMapId(current_map_id));
                for (int i = 0; i < markerList.size(); i++) {
                    Marker marker = markerList.get(random.nextInt(markerList.size()));
                    if (marker != null) {
                        if (!MARKER_TYPE_ELEVATOR.equals(marker.getType())) {
                            if (StringUtils.isEmpty(markerMap.get(marker.getId()))) {
                                DefaultVehicleStatus.PositionStatus position = vehicle.getDefaultVehicleStatus().getPosition();
                                position.setPos_x(marker.getX());
                                position.setPos_y(marker.getY());
                                position.setPos_angle(Math.toRadians(marker.getAngle()));
                                vehicle.getDefaultVehicleStatus().setPosition(position);
                                markerMap.put(marker.getId(), marker.getId());
                                stringBuffer.append(vehicle.getDeviceNumber() + " ,");
                            }
                        }
                    }
                }
            }
        }
        return stringBuffer.toString();

    }



}
