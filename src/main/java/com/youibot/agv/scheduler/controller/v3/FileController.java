package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.service.DownLoadFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

@RestController("FileControllerV3")
@RequestMapping(value = "/api/v3/file", produces = "application/json")
@Api(value = "系统文档操作", tags = "系统文档操作", description = "对系统文档进行操作, 如下载用户使用手册、api帮助手册等")
public class FileController {

    @Autowired
    private DownLoadFileService downLoadFileService;

    @Value("${FILE.DOWNLOAD_FILE_PATH.USER_MANUAL}")
    private String userManualFilePath;//用户使用手册文件路径

    @Value("${FILE.DOWNLOAD_FILE_PATH.API_MANUAL}")
    private String apiManualFilePath;//api帮助手册文件路径

    @ApiOperation(value = "下载用户操作手册", notes = "用户点击后下载用户操作手册")
    @GetMapping("/userManual/download")
    public void userManualDownLoad(HttpServletResponse response) throws UnsupportedEncodingException {
        downLoadFileService.downloadFile(new File(userManualFilePath), response);
    }

    @ApiOperation(value = "下载api帮助手册", notes = "用户点击后下载用户操作手册")
    @GetMapping("/apiManual/download")
    public void apiManualDownLoad(HttpServletResponse response) throws UnsupportedEncodingException {
        downLoadFileService.downloadFile(new File(apiManualFilePath), response);
    }

}
