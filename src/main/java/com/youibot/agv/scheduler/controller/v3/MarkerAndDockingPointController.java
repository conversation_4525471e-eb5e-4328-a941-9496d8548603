package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MarkerAndDockingPoint;
import com.youibot.agv.scheduler.service.MarkerAndDockingPointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@ApiIgnore
@RestController("MarkerAndDockingPointControllerV3")
@RequestMapping(value = "/api/v3/markerAndDockingPoints", produces = "application/json")
@Api(value = "标志点与对接点中间表", tags = "标志点与对接点中间表", description = "标志点与对接点中间表, 目前只支持标记点与对接点是1对N关系, v型特征对接点与反光条特征对接点只能绑定工作标记点, 充电桩对接点只能绑定充电标记点")
@Deprecated
public class MarkerAndDockingPointController {

	@Autowired
	private MarkerAndDockingPointService markerAndDockingPointService;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(MarkerAndDockingPointController.class);
//
//    @ApiOperation(value = "分页查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
//    })
//    @GetMapping("/page")
//    @ResponseStatus(value = HttpStatus.OK)
//    public PageInfo<MarkerAndDockingPoint> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
//        return markerAndDockingPointService.findPage(searchMap);
//    }
//
//    @ApiOperation(value = "列表")
//    @GetMapping
//    @ResponseStatus(value = HttpStatus.OK)
//    public List<MarkerAndDockingPoint> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
//        return markerAndDockingPointService.searchAll(searchMap);
//    }
//
//    @ApiOperation(value = "创建")
//    @ApiImplicitParam(name = "markerAndDockingPoint", value = "标记", required = true, dataType = "MarkerAndDockingPoint")
//    @PostMapping
//    @ResponseStatus(value = HttpStatus.CREATED)
//    public MarkerAndDockingPoint save(@RequestBody @Valid MarkerAndDockingPoint markerAndDockingPoint) {
//        this.markerAndDockingPointService.insert(markerAndDockingPoint);
//        LOGGER.debug(markerAndDockingPoint.toString());
//        return markerAndDockingPoint;
//    }
//
//    @ApiOperation(value = "详情")
//    @ApiImplicitParam(name = "id", value = "标记ID", paramType = "path", required = true, dataType = "String")
//    @GetMapping(value = "/{id}")
//    @ResponseStatus(value = HttpStatus.OK)
//    public MarkerAndDockingPoint get(@PathVariable("id") String id) {
//        return markerAndDockingPointService.selectById(id);
//    }
//
//    @ApiOperation(value = "更新")
//    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "标记ID", paramType = "path", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "markerAndDockingPoint", value = "标记", required = true, dataType = "DockingPoint")})
//    @PutMapping(value = "/{id}")
//    @ResponseStatus(value = HttpStatus.OK)
//    public MarkerAndDockingPoint update(@PathVariable("id") String id, @RequestBody @Valid MarkerAndDockingPoint markerAndDockingPoint) {
//        markerAndDockingPoint.setId(id);
//        this.markerAndDockingPointService.update(markerAndDockingPoint);
//        return this.markerAndDockingPointService.selectById(id);
//    }
//
//    @ApiOperation(value = "删除")
//    @ApiImplicitParam(name = "id", value = "标记ID", paramType = "path", required = true, dataType = "String")
//    @DeleteMapping(value = "/{id}")
//    @ResponseStatus(value = HttpStatus.NO_CONTENT)
//    public void delete(@PathVariable("id") String id) {
//        this.markerAndDockingPointService.deleteById(id);
//    }
}
