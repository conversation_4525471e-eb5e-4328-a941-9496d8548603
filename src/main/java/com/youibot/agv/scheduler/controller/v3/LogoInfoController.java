package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.LogoInfo;
import com.youibot.agv.scheduler.service.LogoInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * logoInfo
 * @Author：yangpeilin
 * @Date: 2020/5/9 15:16
 */
@RestController("LogoInfoControllerV3")
@RequestMapping("/api/v3/logo")
@Api(value = "logo处理", tags = "logo处理")
public class LogoInfoController {

    private static Logger logger = LoggerFactory.getLogger(LogoInfoController.class);

    @Autowired
    private LogoInfoService logoInfoService;

    @ApiOperation(value = "创建Logo")
    @ApiImplicitParam(name = "logoInfo", value = "logo信息", required = true, dataType = "LogoInfo")
    @ResponseStatus(value = HttpStatus.OK)
    @PostMapping
    public void createLogoInfo(@RequestBody LogoInfo logoInfo) {
        List<LogoInfo> all = logoInfoService.findAll();
        if (!CollectionUtils.isEmpty(all)) logoInfoService.deleteById(all.get(0).getId());
        logoInfoService.insert(logoInfo);
    }

    @ApiOperation(value = "修改Logo")
    @ApiImplicitParam(name = "logoInfo", value = "logo信息", required = true, dataType = "LogoInfo")
    @ResponseStatus(value = HttpStatus.OK)
    @PutMapping
    public void updateLogoInfo(@RequestBody LogoInfo logoInfo) {
        logoInfoService.updateByPrimaryKeySelective(logoInfo);
    }


    @ApiOperation(value = "查询Logo信息")
    @ResponseStatus(value = HttpStatus.OK)
    @GetMapping
    public LogoInfo getLogoInfo() {
        List<LogoInfo> all = logoInfoService.findAll();
        if (!CollectionUtils.isEmpty(all)) return all.get(0);
        return null;
    }



}
