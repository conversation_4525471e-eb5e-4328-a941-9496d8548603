package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.entity.AGVPathParam;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.service.MqVehicleService;
import com.youibot.agv.scheduler.service.AGVPathParamService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.SystemWorkModeService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_DIS_CONNECTION;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_ONLINE;

@RestController("agvControllerV3")
@RequestMapping(value = "/api/v3/agvs", produces = "application/json")
@Api(value = "AGV", tags = "AGV", description = "AGV管理接口，用户可以通过接口修改AGV基本信息。")
public class AGVController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AGVController.class);

    @Autowired
    private AGVService agvService;
    @Autowired
    private MqVehicleService mqVehicleService;
    @Autowired
    private SystemWorkModeService systemWorkModeService;
    @Autowired
    private VehiclePool vehiclePool;
    @Autowired
    private AGVPathParamService agvPathParamService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Vehicle> page(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        PageInfo<AGV> pageList = agvService.findPage(searchMap);
        List<AGV> agvList = pageList.getList();
        List<Vehicle> vehicleList = new ArrayList<>();
        for (AGV agv : agvList) {
            vehicleList.add(vehiclePool.getVehicle(agv.getId()));
        }
        PageInfo<Vehicle> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(pageList, resultPage);
        resultPage.setList(vehicleList);
        return resultPage;
    }


//    private List<Vehicle> setVehicle(List<AGV> agvList) {
//        List<Vehicle> vehicleList = new ArrayList<>();
//        for (AGV agv : agvList) {
//            vehicleList.add(vehiclePool.getVehicle(agv.getId()));
//        }
//        return vehicleList;
//    }

    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGV get(@PathVariable("id") String id) {
        return agvService.selectById(id);
    }


    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "agv", value = "创建", required = true, dataType = "AGV")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AGV save(@RequestBody @Valid AGV agv) {
        Example example = new Example(AGV.class);
        example.createCriteria().andEqualTo("agvCode", agv.getAgvCode());
        if (agvService.selectCountByExample(example) > 0) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_code_is_exist"));
        }
        if(StringUtils.isEmpty(agv.getSchedulerSystemsId())){
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        this.agvService.insert(agv);
        //添加默认值
        AGVPathParam agvPathParam =new AGVPathParam();
        agvPathParam.setAgvId(agv.getId());
        agvPathParam.setMax_translation_speed(0.8);
        agvPathParam.setMax_rotate_speed(0.8);
        agvPathParam.setTranslation_acc(0.5);
        agvPathParam.setRotate_acc(0.6);
        agvPathParamService.insert(agvPathParam);
        vehiclePool.attachVehicle(agv);
        return agv;
    }


    @ApiOperation(value = "更新", notes = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "机器人的ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "AGV", value = "机器人", required = true, dataType = "AGV")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGV update(@PathVariable("id") String id, @RequestBody @Valid AGV agv) {
        agv.setId(id);
        Example example = new Example(AGV.class);
        example.createCriteria().andEqualTo("agvCode", agv.getAgvCode()).andNotEqualTo("id",id);
        if (agvService.selectCountByExample(example) > 0) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_code_is_exist"));
        }
        Vehicle vehicle = VehicleUtils.getOnlineVehicle(id);
        if (!SCHEDULER_ONLINE_STATUS_DIS_CONNECTION.equals(vehicle.getSchedulerStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.scheduler_mode_can_not_update_agv_code"));
        }
        vehicle.setName(agv.getName());
        vehicle.setNavigationType(agv.getNavigationType());
        vehicle.setDeviceModel(agv.getDeviceModel());
        vehicle.setDeviceNumber(agv.getAgvCode());
        vehicle.setSchedulerSystemsId(agv.getSchedulerSystemsId());
        agvService.updateByPrimaryKeySelective(agv);
        try {
            if(SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus())){
                mqVehicleService.sendVehicleInfoMessage(agv.getAgvCode());//登录成功发送机器人信息
            }
        } catch (InterruptedException e) {
            logger.error("推送机器人信息出错,", e);
        }
        return agvService.selectById(id);
    }


}
