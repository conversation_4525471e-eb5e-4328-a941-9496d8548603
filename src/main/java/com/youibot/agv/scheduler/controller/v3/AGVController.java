package com.youibot.agv.scheduler.controller.v3;

import static com.youibot.agv.scheduler.constant.VehicleConstant.DISABLE;
import static com.youibot.agv.scheduler.constant.VehicleConstant.ENABLE;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.service.MqttService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;
import tk.mybatis.mapper.entity.Example;

@RestController("agvController")
@RequestMapping(value = "/api/v3/agvs", produces = "application/json")
@Api(value = "Agv", tags = "Agv", description = "AGV管理接口，用户可以通过接口添加和删除机器人。")
public class AGVController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(AGVController.class);

    @Autowired
    private AGVService agvService;

    @Autowired
    private VehiclePool defaultVehiclePool;

    @Autowired
    private MqttService mqttService;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MarkerService markerService;


    @GetMapping("/test")
    public String test() {
        return "jenkins-restart";
    }


    // 获取AGV列表
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Agv> list(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        searchMap.put("sort", "agv_code asc");
        PageInfo<Agv> pageList = agvService.findPage(searchMap);
        try {
			List<Agv> agvList = pageList.getList();
			setVehicle(agvList);
			pageList.setList(agvList);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        return pageList;
    }
//



    private List<Agv> setVehicle(List<Agv> agvList) {
    	if(CollectionUtils.isEmpty(agvList)) {
    		return agvList ;
    	}
        for (Agv agv : agvList) {
            try {
				String agvCode = agv.getAgvCode();
				Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
				if(Objects.isNull(vehicle)) {
					agv.setLinePatrolMode(false);
					agv.setLinePatroWeight(0);
					continue;
				}
				agv.setVehicle(vehicle);
				AtomicInteger linePatroWeight = vehicle.getLinePatroWeight();
				if(Objects.isNull(linePatroWeight)) {
					linePatroWeight = new AtomicInteger(0);
					vehicle.setLinePatroWeight(linePatroWeight);
				}
				agv.setLinePatroWeight( linePatroWeight.get());
				agv.setLinePatrolMode(vehicle.getLinePatrolMode());
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
			}
        }
        return agvList;
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Agv> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return setVehicle(agvService.searchAll(searchMap));
    }

    // 获取AGV信息 ById
    @ApiOperation(value = "详情", notes = "详情")
    @ApiImplicitParam(name = "id", value = "机器人的ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Agv get(@PathVariable("id") String id) {
        return agvService.selectById(id);
    }

    // 更新AGV信息 ById
    @LogOperation
    @ApiOperation(value = "更新", notes = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "机器人的ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "AGV", value = "机器人 只能更新agvGroupId和status和agvColor", required = true, dataType = "AGV")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Agv update(@PathVariable("id") String id, @RequestBody @Valid Agv agv) {
     
        Agv newAGV = this.agvService.selectById(id);
        newAGV.setAgvGroupId(agv.getAgvGroupId());
        newAGV.setAgvType(agv.getAgvType());
        newAGV.setAgvColor(agv.getAgvColor());
        newAGV.setAutoCharge(agv.getAutoCharge());
        newAGV.setAutoPark(agv.getAutoPark());
        newAGV.setAutoAllocation(agv.getAutoAllocation());
        newAGV.setIp(agv.getIp());

        //更新充电点
        newAGV.setBindChargeConfig(agv.isBindChargeConfig());
        newAGV.setBindChargeMarkers(agv.getBindChargeMarkers());
        newAGV.setBindParkConfig(agv.isBindParkConfig());
        newAGV.setBindParkMarkers(agv.getBindParkMarkers());

        if (!newAGV.getStatus().equals(agv.getStatus())) {
            logger.debug("agvCode:[{}],event:[更新机器人信息],content:{}", agv.getAgvCode(), newAGV.getStatus().equals(ENABLE) ? "启用机器人" : "禁用机器人");
            newAGV.setStatus(agv.getStatus());
        }
        agvService.update(newAGV);
        /**
         * 机器人在任务状态也可以禁用。禁用的机器人执行完当前任务后，不会再分配任务。充电和泊车。
         * 列新机器人的状态后，还需要更新defaultVehiclePoll状态。
         */
        defaultVehiclePool.updateVehicleByAgv(newAGV.getAgvCode());
        logger.debug("agvCode:[{}],event:[更新机器人信息],content:{}", agv.getAgvCode(), "更新完成: " + agv.toString());
       
        return newAGV;
    }

    @LogOperation
    @ApiOperation(value = "批量更新", notes = "批量更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "agvs", value = "机器人信息", dataType = "AGV", required = true, allowMultiple = true)})
    @PutMapping("/batch")
    @ResponseStatus(value = HttpStatus.OK)
    public void batchUpdate(@RequestBody Agv[] agvs) {
        if (agvs == null || agvs.length == 0) {
            throw new YOUIFleetException("http.missing_parameter");
        }
        List<Agv> agvListTmp = Arrays.asList(agvs);
        ArrayList<Agv> agvList = Lists.newArrayList(agvListTmp);
        List<Agv> updateList = new ArrayList<>();
        for (Agv newAGV : agvList) {
            Vehicle vehicle = defaultVehiclePool.getVehicle(newAGV.getAgvCode());
            if (vehicle == null) {
                continue;
            }
            //禁用机器人，需要判断机器人是否有任务
            if (DISABLE.equals(newAGV.getStatus())) {
                MissionWork missionWork = missionWorkService.selectUnCompleteByAgvId(newAGV.getAgvCode());
                if (missionWork != null) {
                    throw new YOUIFleetException(MessageUtils.getMessage("http.agv_have_mission_work"));
                }
            }
            String agvGroupId = newAGV.getAgvGroupId();
            vehicle.setAgvGroupId(agvGroupId);
            vehicle.setStatus(newAGV.getStatus());
            updateList.add(newAGV);
        }
        agvService.batchUpdate(updateList);
    }


    @ApiOperation(value = "查询机器人详情", notes = "根据agvCode查询机器人详情")
    @ApiImplicitParam(name = "agvCode", value = "agvCode机器人编号", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{agvCode}/vehicle")
    @ResponseStatus(value = HttpStatus.OK)
    public Vehicle getAGVBasicInfos(@PathVariable("agvCode") String agvCode) {
        return defaultVehiclePool.getVehicle(agvCode);
    }

    @LogOperation
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "agv的ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        Agv agv = agvService.selectById(id);
        if (agv == null) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.agv_disconnection_can_not_delete"));
        }
        
        mqttService.closeClient(  agv.getAgvCode() );
        this.agvService.deleteById(id);
        vehiclePool.detachVehicle(  agv.getAgvCode()  );
        logger.debug("agvCode:[{}],event:[删除机器人],content:{}", agv.getAgvCode(), agv.toString());
       
    }

    @LogOperation
    @ApiOperation(value = "删除机器人的mqtt连接")
    @ApiImplicitParam(name = "agvCode", value = "agv的agvCode", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/mqtt/{agvCode}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteCloseClient(@PathVariable("agvCode") String agvCode) {
   
        
        mqttService.closeClient( agvCode );
        Example example = new Example(Agv.class) ;
        example.createCriteria().andEqualTo("agvCode", agvCode) ;
        this.agvService.deleteByExample( example);
        vehiclePool.detachVehicle( agvCode );
        logger.debug("agvCode:[{}],event:[删除机器人],content:{}", agvCode, "清空agvCode");
       
    }
    
    @LogOperation
    @ApiOperation(value = "批量停止任务")
    @ApiImplicitParam(name = "agvCodes", value = "agvCodes列表", dataType = "String", required = true)
    @PostMapping("/stop/batch")
    @ResponseStatus(HttpStatus.OK)
    public void stopBatch(@RequestBody List<String> agvCodes) {
        if (CollectionUtils.isEmpty(agvCodes)) {
            throw new YOUIFleetException("http.missing_parameter");
        }
        agvService.stopBatch(agvCodes);
    }


    /**
     * 清理掉机器人占用的所有资源。
     * 包括站点，路径，区域。T字路，一字路。PlanPath,runningPath,ExecutedPath等信息。
     * 包括机器人自身location
     *
     * @param agvCode
     */
    @LogOperation
    @ApiOperation(value = "释放机器人占用的所有资源")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", required = true)
    @PutMapping("/{agvCode}/clear")
    @ResponseStatus(HttpStatus.OK)
    public void clearResource(@PathVariable("agvCode") String agvCode) {
        if (StringUtils.isBlank(agvCode)) {
            throw new YOUIFleetException("http.missing_parameter");
        }
   
        logger.debug("agvCode:[{}],event:[释放机器人占用的所有资源],content:{}", agvCode, "下发清理资源指令");
        agvService.clearResource(agvCode);
       
    }


}
