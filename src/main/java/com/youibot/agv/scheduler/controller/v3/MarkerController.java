package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.map.entity.PageInfo;
import com.youibot.agv.scheduler.service.MarkerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

@RestController("markerControllerV3")
@RequestMapping(value = "/api/v3/markers", produces = "application/json")
@Api(value = "标记点", tags = "标记点", description = "标志管理接口，标志是指地图所有的标志点位资源，例如：导航点，充电点，待机点。")
public class MarkerController extends BaseController {

    @Autowired
    private MarkerService markerService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "agvMapId", value = "地图名称", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Marker> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) throws Exception {
        return markerService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvMapName", value = "地图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Marker> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap, @RequestParam boolean isDraft) {
        return markerService.searchAll(searchMap, isDraft);
    }


    @ApiOperation(value = "列表(查询所有地图的点位)")
    @GetMapping("/all")
    @ResponseStatus(value = HttpStatus.OK)
    public List<Marker> all() {
        return markerService.getAllWithOutCondition();
    }


    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "marker", value = "标记点", required = true, dataType = "Marker")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Marker save(@RequestBody @Valid Marker marker) {
        return this.markerService.insert(marker);
    }

    @ApiOperation(value = "详情")

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "标记点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvMapName", value = "地图名称", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "/{agvMapName}/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Marker get(@PathVariable("id") String id, @PathVariable("agvMapName") String agvMapName, @RequestParam boolean isDraft) {
        return markerService.selectById(agvMapName, id, isDraft);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "标记点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "marker", value = "标记点", required = true, dataType = "Marker")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Marker update(@PathVariable("id") String id, @RequestBody @Valid Marker marker) {
        marker.setId(id);
        return this.markerService.update(marker);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "标记点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvMapName", value = "地图名称", paramType = "path", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/{agvMapName}/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("agvMapName") String agvMapName, @PathVariable("id") String id) {
        this.markerService.deleteById(agvMapName, id);
    }

    @ApiOperation(value = "根据标记点类型查询与当前标记点有路径的所有点位列表", notes = "根据标记点类型查询与当前标记点有路径的所有点位列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "标记ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvMapId", value = "地图id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "标记点类型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "/{id}/listMakerIds")
    @ResponseStatus(value = HttpStatus.OK)
    public List<Marker> listMakerIds(@PathVariable("id") String id, @RequestParam String agvMapId, @RequestParam(required = false) String type, @RequestParam boolean isDraft) {
        return this.markerService.selectRelatePathMarkerIdsByType(id, agvMapId, type, isDraft);
    }
}
