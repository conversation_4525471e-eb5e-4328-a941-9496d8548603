package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.engine.entity.MapResourceCache;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("markerControllerV3")
@RequestMapping(value = "/api/v3/markers", produces = "application/json")
@Api(value = "标记点", tags = "标记点", description = "标志管理接口，标志是指地图所有的标志点位资源，例如：导航点，冲电点，待机点。")
public class MarkerController extends BaseController {

    @Autowired
    private MarkerService markerService;

    @Autowired
    private AGVMapService agvMapService;

    private static final Logger LOGGER = LoggerFactory.getLogger(MarkerController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Marker> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return markerService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Marker> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return markerService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "marker", value = "标记点", required = true, dataType = "Marker")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Marker save(@RequestBody @Valid Marker marker) {
        agvMapService.checkUpdateAllow(marker.getAgvMapName());
        this.markerService.insert(marker);
        LOGGER.debug(marker.toString());
        return this.markerService.selectById(marker.getId());
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "标记点ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Marker get(@PathVariable("id") String id) {
        return MapResourceCache.getMarker( id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "标记点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "marker", value = "标记点", required = true, dataType = "Marker")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Marker update(@PathVariable("id") String id, @RequestBody @Valid Marker marker) {
        agvMapService.checkUpdateAllow(marker.getAgvMapName());
        marker.setId(id);
        Example example = new Example(Marker.class);
        example.createCriteria().andEqualTo("code", marker.getCode()).andEqualTo("agvMapId", marker.getAgvMapName())
                .andNotEqualTo("id",id);
        if (markerService.selectCountByExample(example)> 0) {
            throw new ADSParameterException(MessageUtils.getMessage("service.marker_code_already_exists"));
        }
        this.markerService.update(marker);
        return this.markerService.selectById(marker.getId());
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "标记点ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        Marker marker = markerService.selectById(id);
        if (marker == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.marker_is_null"));
        }
        agvMapService.checkUpdateAllow(marker.getAgvMapName());
        this.markerService.deleteById(id);
    }

    @ApiOperation(value = "根据标记点类型查询与当前标记点有路径的所有点位列表", notes = "根据标记点类型查询与当前标记点有路径的所有点位列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "标记ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvMapId", value = "地图id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "标记点类型", required = false, dataType = "String")
    })
    @GetMapping(value = "/{id}/listMakerIds")
    @ResponseStatus(value = HttpStatus.OK)
    public List<Marker> listMakerIds(@PathVariable("id") String id, @RequestParam String agvMapId, @RequestParam(required = false) String type) {
        return this.markerService.selectRelatePathMarkerIdsByType(id, agvMapId, type);
    }
}
