package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ConflictProcessThread;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date :Created in 下午4:53 2021/1/6
 * @Description :
 * @Modified By :
 * @Version :
 */
@RestController("ConflictProcessTestController")
@RequestMapping(value = "/api/v3/conflictProcessTest", produces = "application/json")
@Api(value = "Conflict Process", tags = "冲突处理", description = "冲突处理")
public class ConflictProcessTestController {

    private static final Logger logger = LoggerFactory.getLogger(ConflictProcessTestController.class);

    @Autowired
    private ConflictProcessThread conflictProcessThread;

    @ApiOperation(value = "获取与传参进来的AGV冲突的AGVList")
    @ApiImplicitParam(name = "id", value = "Vehicle.Id(agvCode)", paramType = "path", required = true, dataType = "String")
    @GetMapping("/getConflictAGVList/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public List<String> getConflictAGVList(@PathVariable("id") String agvCode) {
        Set<String> stringSet = new HashSet<>();
        List<List<Pair<String, String>>> conflictAGVListList = conflictProcessThread.getConflictAGVListList();
        for (List<Pair<String, String>> list : conflictAGVListList) {
            boolean contained = false;
            for (Pair<String, String> pair : list) {
                if (pair.first().equals(agvCode)) {
                    contained = true;
                    break;
                }
            }
            if (contained) {
                List<String> tempList = list.stream().map(Pair::first).collect(Collectors.toList());
                stringSet.addAll(tempList);
            }
        }
        return new ArrayList<>(stringSet);
    }
}
