package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.service.SidePathService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("sidePathControllerV3")
@RequestMapping(value = "/api/v3/sidePaths", produces = "application/json")
@Api(value = "边路径", tags = "边路径", description = "边路径的管理接口，边路径是指两个标志点中间的连接线路。")
public class SidePathController extends BaseController {

    @Autowired
    private SidePathService sidePathService;

    private static final Logger LOGGER = LoggerFactory.getLogger(SidePathController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<SidePath> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return sidePathService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<SidePath> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return sidePathService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "sidePath", value = "边路径", required = true, dataType = "SidePath")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public SidePath save(@RequestBody @Valid SidePath sidePath) {
        this.sidePathService.insert(sidePath);
        LOGGER.debug(sidePath.toString());
        return sidePath;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "边路径ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public SidePath get(@PathVariable("id") String id) {
        return sidePathService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "边路径ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "sidePath", value = "边路径", required = true, dataType = "SidePath")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public SidePath update(@PathVariable("id") String id, @RequestBody @Valid SidePath sidePath) {
        sidePath.setId(id);
        this.sidePathService.update(sidePath);
        return this.sidePathService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "边路径ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.sidePathService.deleteById(id);
    }
}
