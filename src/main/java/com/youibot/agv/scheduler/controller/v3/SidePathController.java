package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.service.SidePathService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import java.util.List;
import java.util.Map;

@Deprecated
@ApiIgnore
@RestController("sidePathControllerV3")
@RequestMapping(value = "/api/v3/sidePaths", produces = "application/json")
@Api(value = "边路径", tags = "边路径", description = "边路径的管理接口，边路径是指两个标志点中间的连接线路。")
public class SidePathController extends BaseController {

    @Autowired
    private SidePathService sidePathService;

    @ApiOperation(value = "列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "agvMapName", value = "标记点ID", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿文件数据", required = true, dataType = "boolean")})
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<SidePath> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap,@RequestParam boolean isDraft) {
        return sidePathService.searchAll(searchMap,isDraft);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "agvMapName", value = "标记点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "id", value = "标记点ID",  required = true, paramType = "path", dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿文件数据", required = true, dataType = "boolean")})
    @GetMapping(value = "/{agvMapName}/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public SidePath get(@PathVariable("agvMapName") String agvMapName,@PathVariable("id") String id,@RequestParam boolean isDraft) {
        return sidePathService.selectById(agvMapName,id,isDraft);
    }

    @ApiOperation(value = "根据pathId查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "agvMapName", value = "标记点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pathId", value = "标记点ID",  required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿文件数据", required = true, dataType = "boolean")})
    @GetMapping(value = "/{agvMapName}/selectByPathId")
    @ResponseStatus(value = HttpStatus.OK)
    public List<SidePath> selectByPathId(@PathVariable("agvMapName") String agvMapName,@RequestParam String pathId, @RequestParam boolean isDraft) {
        return sidePathService.selectByPathId(agvMapName,pathId,isDraft);
    }
}
