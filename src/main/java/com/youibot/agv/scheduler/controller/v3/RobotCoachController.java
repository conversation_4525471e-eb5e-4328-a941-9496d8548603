package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date :Created in 16:50 2021/11/30
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
@RestController("robotCoachController")
@RequestMapping(value = "/api/v3/robotCoach", produces = "application/json")
@Api(value = "查询MOS物料信息状态", tags = "查询MOS物料信息状态", description = "查询MOS物料信息状态")
public class RobotCoachController {

    private static final Map<String, Pair<String, Long>> agvCodeToRobotCoachInfo = new ConcurrentHashMap<>();
    private final Map<String, Long> agvCodeToSendMessageTime = new ConcurrentHashMap<>();

    @ApiOperation("给一个agvCode,根据这个agvCode查询物料信息")
    @GetMapping("/getRobotCoachInfo/{agvCode}")
    public String getRobotCoach(@PathVariable("agvCode") String agvCode) throws InterruptedException {
        if (agvCode == null) {
            throw new ExecuteException("错误的参数");
        }

        MqttUtils.sendMqttMsg(MqttConstant.ROBOT_COACH_TOPIC, agvCode, "robotCoach");
        agvCodeToSendMessageTime.put(agvCode, System.currentTimeMillis());

        long waitTime = 5 * 1000; //设置超时时间

        while (true) {
            if (Math.abs(System.currentTimeMillis() - agvCodeToSendMessageTime.get(agvCode)) > waitTime) {
                throw new ExecuteException("查询等待时间过长");
            }
            Pair<String, Long> pair = agvCodeToRobotCoachInfo.get(agvCode);
            if (pair != null && pair.second() > agvCodeToSendMessageTime.get(agvCode)) {
                return pair.first();
            }
            Thread.sleep(200);
        }
    }

    public void respondMessage(String agvCode, String message) {
        if (agvCode != null) {
            agvCodeToRobotCoachInfo.put(agvCode, new Pair<>(message, System.currentTimeMillis()));
        }
    }
}
