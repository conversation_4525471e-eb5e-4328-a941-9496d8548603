package com.youibot.agv.scheduler.controller.v3;


import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.mqtt.service.VehicleCommandService;
import com.youibot.agv.scheduler.service.VehicleControlService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.LinkedHashSet;
import java.util.Objects;


/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime:2019/5/5 19:52
 */
@Slf4j
@RestController("vehicleControlController")
@RequestMapping(value = "/api/v3/vehicles/", produces = "application/json")
@Api(value = "机器人控制模式", tags = "机器人控制模式", description = "机器人控制模式接口，用户可以通过这个接口切换自动/手工控制模式。")
public class VehicleControlController extends BaseController {


    private static final Logger logger = LoggerFactory.getLogger(VehicleControlController.class);


    @Autowired
    private VehicleControlService vehicleControlService;
    
   
	@Autowired
	private DefaultVehiclePool defaultVehiclePool;
	@Autowired
	private VehicleCommandService vehicleCommandService;
    

    @ApiOperation(value = "切换为手工控制模式")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "{agvCode}/controls/manualMode")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public void manualMode(@PathVariable("agvCode") String agvCode) throws IOException {


      
        Vehicle vehicle = this.getVehicle(agvCode);
        
		vehicle.switchManualMode();
		vehicle.reset( ) ;
        logger.debug("agvCode:[{}],event:[切换为手工控制模式],content:[{}]", agvCode, "指令下发完成");
       
        vehicleControlService.linecePatro(agvCode, false );

    }

    @ApiOperation(value = "切换为自动控制模式")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "{agvCode}/controls/autoMode")
    @ResponseStatus(value = HttpStatus.OK)
    public void autoMode(@PathVariable("agvCode") String agvCode) throws IOException, InterruptedException {
     
  
    Vehicle vehicle = this.getVehicle(agvCode);
    vehicle.reset(   ) ;
	vehicle.switchAutoMode();
    logger.debug("agvCode:[{}],event:[切换为自动控制模式],content:[{}]", agvCode, "指令下发完成");
	vehicleControlService.linecePatro(agvCode, true);

        
    }
    @ApiOperation(value = "批量切换为手工控制模式")
    @ApiImplicitParam(name = "agvCodes", value = "Agv ID",  required = true, dataType = "Set")
    @PostMapping(value = "/manualModeBatch")
    @ResponseStatus(value = HttpStatus.OK)
    public void manualModeBatch(@RequestBody LinkedHashSet<String>  agvCodes) throws IOException {
    	 
    	vehicleControlService.manualModeBatch(agvCodes) ;
    	
    }
    
    @ApiOperation(value = "批量切换为自动控制模式")
    @ApiImplicitParam(name = "agvCodes", value = "Agv ID", required = true, dataType = "Set")
    @PostMapping(value = "/autoModeBatch")
    @ResponseStatus(value = HttpStatus.OK)
    public void autoModeBatch(@RequestBody LinkedHashSet<String>  agvCodes) throws IOException, InterruptedException {
    	
    	vehicleControlService.autoModeBatch(agvCodes) ;

    }

    
    
    
    @ApiOperation(value = "台积电项目切换巡线,非巡线")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", required = true) ,
        @ApiImplicitParam(name = "linePatrolMode", value = "linePatrolMode", dataType = "Boolean", required = true) }
    )
    @PutMapping("{agvCode}/controls/linePatrolMode/{linePatrolMode}")
    @ResponseStatus(HttpStatus.OK)
    public void linecePatro(@PathVariable("agvCode") String agvCode , @PathVariable("linePatrolMode") Boolean linePatrolMode){
    	
    	vehicleControlService.linecePatro(agvCode, linePatrolMode);
    }
    
    
    
    @ApiOperation(value = "台积电项目切换充电,取消充电")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", required = true) ,
    	@ApiImplicitParam(name = "recharge", value = "recharge", dataType = "Boolean", required = true) }
    		)
    @PutMapping("{agvCode}/controls/recharge/{recharge}")
    @ResponseStatus(HttpStatus.OK)
    public void recharge(@PathVariable("agvCode")String  agvCode,@PathVariable("recharge") Boolean recharge){
    	
    	vehicleControlService.recharge(agvCode, recharge);

    	
    }
    
    
    @ApiOperation(value = "台积电项目切换巡线,非巡线-批量")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "agvCodes", value = "agvCodes", dataType = "Set", required = true) ,
        @ApiImplicitParam(name = "linePatrolMode", value = "linePatrolMode", dataType = "Boolean", required = true) }
    )
    @PutMapping("/linePatrolMode/{linePatrolMode}/batch")
    @ResponseStatus(HttpStatus.OK)
    public void linecePatroBatch(@RequestBody LinkedHashSet<String>  agvCodes , @PathVariable("linePatrolMode") Boolean linePatrolMode){
    	

    	vehicleControlService.linecePatroBatch(agvCodes, linePatrolMode);
    	
    }
    
    
    
    @ApiOperation(value = "台积电项目切换充电,取消充电-批量")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "agvCodes", value = "agvCodes", dataType = "Set", required = true) ,
    	@ApiImplicitParam(name = "recharge", value = "recharge", dataType = "Boolean", required = true) }
    		)
    @PutMapping("/recharge/{recharge}/batch")
    @ResponseStatus(HttpStatus.OK)
    public void rechargeBatch( @RequestBody LinkedHashSet<String>  agvCodes  ,@PathVariable("recharge") Boolean recharge){
    	 
    	vehicleControlService.rechargeBatch(agvCodes, recharge);
    	  
    			
    }

    
	
    @ApiOperation(value = "台积电项目--一键掉头&&一键切换工作站工作组")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", required = true) ,
         }
    )
    @PutMapping("{agvCode}/controls/oneKeyUturn")
    @ResponseStatus(HttpStatus.OK)
    public void oneKeyUturn(@PathVariable("agvCode") String agvCode ){
    	
    	 log.debug("agvCode:{}, oneKey_utrun" , agvCode) ;
    	Vehicle vehicle = super.getVehicle(agvCode);
		if (Objects.isNull(agvCode) || Objects.isNull(vehicle)) {
			return ;
		}
		String missionWorkId = vehicle.getMissionWorkId();
		
		if(StringUtils.isNotBlank(missionWorkId)){
			log.debug("agvCode:{},missionWorkId:{}" ,agvCode, missionWorkId);
			vehicleCommandService.uTurn(agvCode, true, true, true ,false) ;
		}
		
		
    }
    
    
}
