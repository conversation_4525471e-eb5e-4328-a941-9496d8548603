package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionGlobalVariable;
import com.youibot.agv.scheduler.service.MissionGlobalVariableService;
import com.youibot.agv.scheduler.service.MissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("missionGlobalVariableControllerV3")
@RequestMapping(value = "/api/v3/missionGlobalVariables", produces = "application/json")
@Api(value = "预设任务全局变量", tags = "预设任务全局变量", description = "预设任务全局变量，在创建预设任务的时候可添加该预设任务的全局变量，供执行预设任务时提供参数动态变化的功能。")
public class MissionGlobalVariableController extends BaseController {

    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionGlobalVariableService missionGlobalVariableService;

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionGlobalVariableController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionGlobalVariable> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionGlobalVariableService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionGlobalVariable> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionGlobalVariableService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "missionGlobalVariable", value = "任务全局变量", required = true, dataType = "MissionGlobalVariable")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionGlobalVariable save(@RequestBody @Valid MissionGlobalVariable missionGlobalVariable) {
        this.missionGlobalVariableService.insert(missionGlobalVariable);
        LOGGER.debug(missionGlobalVariable.toString());
        missionService.updateMissionVersion(missionGlobalVariable.getMissionId());
        return missionGlobalVariable;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "任务全局变量ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionGlobalVariable get(@PathVariable("id") String id) {
        return missionGlobalVariableService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "小视图ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionGlobalVariable", value = "小视图", required = true, dataType = "MissionGlobalVariable")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionGlobalVariable update(@PathVariable("id") String id, @RequestBody @Valid MissionGlobalVariable missionGlobalVariable) {
        missionGlobalVariable.setId(id);
        this.missionGlobalVariableService.update(missionGlobalVariable);
        missionService.updateMissionVersion(missionGlobalVariable.getMissionId());
        return this.missionGlobalVariableService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "任务全局变量ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        MissionGlobalVariable missionGlobalVariable = missionGlobalVariableService.selectById(id);
        if (missionGlobalVariable != null){
            this.missionGlobalVariableService.deleteById(id);
            missionService.updateMissionVersion(missionGlobalVariable.getMissionId());
        }
    }

}
