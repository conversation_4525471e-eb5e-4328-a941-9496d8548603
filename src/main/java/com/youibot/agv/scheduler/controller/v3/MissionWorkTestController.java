package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.engine.exception.MissionWorkException;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.param.MissionWorkCreateCheck;
import com.youibot.agv.scheduler.param.MissionWorkParam;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapConstant.MARKER_TYPE_ELEVATOR;
import static com.youibot.agv.scheduler.constant.MissionConstant.*;

@RestController("missionWorkTestControllerV3")
@RequestMapping(value = "/api/v3/missionWorksTest", produces = "application/json")
@Api(value = "作业", tags = "测试作业", description = "作业的管理接口，作业是AGV实际执行的内容。")
public class MissionWorkTestController extends BaseController {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionActionService missionActionService;
    @Autowired
    private AGVService agvService;
    @Autowired
    private MissionActionParameterService missionActionParameterService;
    @Autowired
    private MarkerService markerService;
    private Random random = new Random(System.currentTimeMillis());

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkTestController.class);
    String oldCode = " ";

    @ApiOperation(value = "创建随机生成目标点任务")
    @ApiImplicitParam(name = "missionWorkParam", value = "创建任务对象", paramType = "body", required = true, dataType = "MissionWorkParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionWork createMissionWork(@RequestBody MissionWorkParam missionWorkParam) {
        return this.createByMissionWorkParam(missionWorkParam);
    }


    private MissionWork createByMissionWorkParam(MissionWorkParam missionWorkParam) {
        String missionId = missionWorkParam.getMissionId();
        if (StringUtils.isEmpty(missionId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Mission mission = missionService.selectById(missionId);
        if (mission == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_is_null"));
        }
        List<MissionAction> missionActionList = missionActionService.selectByMissionIdAndIsNotSubAction(missionId);//获取missionWorkAction列表
        if (missionActionList == null || missionActionList.isEmpty()) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_action_list_is_null"));
        }
        //两个相同的任务marker code 不相同
        for (MissionAction missionAction : missionActionList) {
            if ("MOVE_SIDE_PATH".equals(missionAction.getActionType())) {
                Example example = new Example(MissionActionParameter.class);
                List<String> param = new ArrayList<>(2);
                param.add("agvMapId");
                param.add("markerCode");
                example.createCriteria().andEqualTo("missionActionId", missionAction.getId()).andIn("parameterKey", param);
                List<MissionActionParameter> actionParameterList = missionActionParameterService.selectByExample(example);
                MissionActionParameter markerParameter = null;
                String markerCode = null;
                for (MissionActionParameter actionParameter : actionParameterList) {
                    if ("agvMapId".equals(actionParameter.getParameterKey())) {
                        List<Marker> markerList = markerService.selectByAGVMapId(actionParameter.getParameterValue(),false);
                        String finalOldCode = oldCode;
                        List<Marker> userMarker = markerList.stream().filter(marker -> !MARKER_TYPE_ELEVATOR.equals(marker.getType()) &&
                                !finalOldCode.equals(marker.getCode())).collect(Collectors.toList());
                        markerCode = userMarker.get(random.nextInt(65535) % (markerList.size())).getCode();
                    } else {
                        markerParameter = actionParameter;
                    }
                }
                if (markerParameter != null && !StringUtils.isEmpty(markerCode)) {
                    markerParameter.setParameterValue(markerCode);
                    oldCode = markerCode;
                    missionActionParameterService.update(markerParameter);
                }
            }
        }

        MissionWork missionWork = new MissionWork();
        missionWork.setMissionId(missionId);
        missionWork.setCallbackUrl(missionWorkParam.getCallbackUrl());
        missionWork.setRuntimeParam(missionWorkParam.getRuntimeParam());
//        missionWork.setMissionCallId(missionWorkParam.getMissionCallId());
        missionWork.setName(mission.getName());
        missionWork.setStatus(MISSION_WORK_STATUS_CREATE);
        missionWork.setSequence(mission.getSequence());
        missionWork.setMissionGroupId(mission.getMissionGroupId());//设置任务组ID,目的：根据任务组ID筛选任务执行记录

        this.missionWorkService.insert(missionWork);
        return missionWork;
    }

}
