package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.AGVPathParam;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AGVPathParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;

@RestController("AGVPathParamControllerV3")
@RequestMapping(value = "/api/v3/AGVPathParams", produces = "application/json")
@Api(value = "AGV路径导航参数", tags = "AGV路径导航参数", description = "AGV路径导航参数的管理接口，存储行驶路径的属性参数，如是否打开避障，最大平移速度。用于agv通过路径时的限制。")
public class AGVPathParamController extends BaseController {

    @Autowired
    private AGVPathParamService agvPathParamService;

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{agvId}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVPathParam get(@PathVariable("agvId") String agvId) {
        Example example = new Example(AGVPathParam.class);
        example.createCriteria().andEqualTo("agvId", agvId);
        return agvPathParamService.selectOneByExample(example);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "agvPathParam", value = "AGV路径导航参数", required = true, dataType = "AGVPathParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AGVPathParam save(@RequestBody @Valid AGVPathParam agvPathParam) {
        if(StringUtils.isEmpty(agvPathParam.getAgvId())){
            throw new ExecuteException("AGV ID 不能为空！");
        }
        this.agvPathParamService.insert(agvPathParam);
        return agvPathParam;
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "AGV路径导航参数ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvPathParam", value = "AGV路径导航参数", required = true, dataType = "AGVPathParam")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVPathParam update(@PathVariable("id") String id, @RequestBody @Valid AGVPathParam agvPathParam) {
        if (StringUtils.isEmpty(agvPathParam.getAgvId())) {
            throw new ExecuteException("参数错误！");
        }
        agvPathParam.setId(id);
        this.agvPathParamService.updateByPrimaryKeySelective(agvPathParam);
        return this.agvPathParamService.selectById(id);
    }
}
