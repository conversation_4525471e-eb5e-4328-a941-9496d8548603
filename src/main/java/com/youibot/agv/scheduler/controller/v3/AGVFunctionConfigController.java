package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.AGVFunctionConfig;
import com.youibot.agv.scheduler.service.AGVFunctionConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/02/14 15:15
 */
@RestController("agvFunctionConfigControllerV3")
@RequestMapping(value = "/api/v3/agvFunctionConfigs", produces = "application/json")
@Api(value = "机器人功能配置", tags = "机器人功能配置", description = "可进行机器人功能的智能配置，如配置智能充电、智能归位功能。")
public class AGVFunctionConfigController extends BaseController {

    @Autowired
    private AGVFunctionConfigService agvFunctionConfigService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AGVFunctionConfig> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvFunctionConfigService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<AGVFunctionConfig> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvFunctionConfigService.searchAll(searchMap);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "机器人功能配置ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVFunctionConfig get(@PathVariable("id") String id) {
        return agvFunctionConfigService.selectById(id);
    }

    @ApiOperation(value = "更新", notes = "根据ID更新机器人功能配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "机器人功能配置ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvFunctionConfig", value = "机器人功能配置", required = true, dataType = "AGVFunctionConfig")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVFunctionConfig update(@PathVariable("id") String id, @RequestBody @Valid AGVFunctionConfig agvFunctionConfig) {
        agvFunctionConfig.setId(id);
        this.agvFunctionConfigService.update(agvFunctionConfig);
        return this.agvFunctionConfigService.selectById(id);
    }

}
