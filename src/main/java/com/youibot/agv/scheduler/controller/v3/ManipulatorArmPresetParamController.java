package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.ManipulatorArmPresetParam;
import com.youibot.agv.scheduler.service.ManipulatorArmPresetParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("manipulatorArmPresetParamControllerV3")
@RequestMapping(value = "/api/v3/manipulatorArmPresetParams", produces = "application/json")
@Api(value = "机械臂预置参数", tags = "机械臂预置参数", description = "机械臂预置参数用于配置一套机械臂参数模板，编辑好默认参数值后可使用改模板的数据控制机械臂移动到指定位置")
public class ManipulatorArmPresetParamController extends BaseController {

    @Autowired
    private ManipulatorArmPresetParamService manipulatorArmPresetParamService;

    private static final Logger LOGGER = LoggerFactory.getLogger(ManipulatorArmPresetParamController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<ManipulatorArmPresetParam> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return manipulatorArmPresetParamService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<ManipulatorArmPresetParam> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return manipulatorArmPresetParamService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "manipulatorArmPresetParam", value = "机械臂预置参数", required = true, dataType = "ManipulatorArmPresetParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public ManipulatorArmPresetParam save(@RequestBody @Valid ManipulatorArmPresetParam manipulatorArmPresetParam) {
        this.manipulatorArmPresetParamService.insert(manipulatorArmPresetParam);
        LOGGER.debug(manipulatorArmPresetParam.toString());
        return manipulatorArmPresetParam;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "机械臂预置参数ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public ManipulatorArmPresetParam get(@PathVariable("id") String id) {
        return manipulatorArmPresetParamService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "机械臂预置参数ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "manipulatorArmPresetParam", value = "机械臂预置参数", required = true, dataType = "ManipulatorArmPresetParam")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public ManipulatorArmPresetParam update(@PathVariable("id") String id, @RequestBody @Valid ManipulatorArmPresetParam manipulatorArmPresetParam) {
        manipulatorArmPresetParam.setId(id);
        this.manipulatorArmPresetParamService.update(manipulatorArmPresetParam);
        return this.manipulatorArmPresetParamService.selectById(manipulatorArmPresetParam.getId());
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "机械臂预置参数ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.manipulatorArmPresetParamService.deleteById(id);
    }
}
