package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.youibot.agv.scheduler.config.InitSystem;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapConstant.MAP_USAGE_STATUS_DISABLE;

/**
 * 地图数据导出
 * @Author：yangpeilin
 * @Date: 2020/4/9 15:00
 */
@RestController("DataExportControllerV3")
@RequestMapping("/api/v3/export")
@Api(value = "地图数据导入导出", tags = "地图数据导入导出", description = "将地图相关数据以json文件方式导出到本地进行备份，如需备份可该文件导入")
public class DataExportController {

    private static Logger logger = LoggerFactory.getLogger(DataExportController.class);

    @Autowired
    private AGVMapService agvMapService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private SidePathService sidePathService;

    @Autowired
    private MapAreaService mapAreaService;

    @Autowired
    private PathService pathService;

    @Autowired
    private DockingPointService dockingPointService;

    @Autowired
    private DataCodeMatrixService dataCodeMatrixService;

    private final String AGVMap = "AGVMap";
    private final String Marker = "Marker";
    private final String SidePath = "SidePath";
    private final String MapArea = "MapArea";
    private final String Path = "Path";
    private final String DockingPoint = "DockingPoint";
    private final String DataCodeMatrix = "DataCodeMatrix";

    @ApiOperation(value = "导出地图数据", notes = "导出地图数据JSON文件")
    @ApiImplicitParam(name = "mapId", value = "地图id", dataType = "String")
    @GetMapping("/exportMapData")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void exportMapData(HttpServletResponse response, @RequestParam(value = "mapId", required = false) String mapId) {
        OutputStream os = null;
        try {
            //获取JSON字符串
            String exportFileName = StringUtils.isEmpty(mapId)?"all":agvMapService.selectById(mapId).getName();
            String mapDateJsonString = getMapDateJsonString(mapId);
            byte[] bytes = mapDateJsonString.getBytes();
            // 将格式化后的字符串写入文件
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(exportFileName.getBytes(),"ISO-8859-1") +".json");
//        response.addHeader("Content-Disposition", "attachment;filename=" + fileId+".lic");
            response.addHeader("Content-Length", "" + bytes.length);
            os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            os.write(bytes);// 输出文件
            os.flush();
            os.close();
        } catch (Exception e) {
            logger.error("exportMapData exception : " + e.getMessage());
        } finally {
            if (os != null){
                try {
                    os.close();
                } catch (IOException e) {
                    logger.error("导出地图数据关闭资源出错, ", e);
                }
            }
        }
    }

    @ApiOperation(value = "导入地图数据", notes = "导入地图数据JSON文件")
    @ApiImplicitParam(name = "multiPartFile", value = "文件", required = true, dataType = "file")
    @PostMapping("/importMapData")
    @ResponseStatus(HttpStatus.OK)
    public void importMapData(@RequestBody MultipartFile multiPartFile) {
        Reader reader = null;
        String jsonStr = null;
        try {
            reader = new InputStreamReader(multiPartFile.getInputStream(),"utf-8");
            int ch = 0;
            StringBuffer sb = new StringBuffer();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            jsonStr = sb.toString();
        } catch (Exception e) {
            logger.error("importMapData exception : " + e.getMessage());
        } finally {
            if (reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error("导入地图数据出错关闭资源, ", e);
                }
            }
        }
        if (StringUtils.isEmpty(jsonStr)){
            throw new ExecuteException("文件内容为空");
        }
        //批量新增地图数据到数据库中
        try {
            batchInsertMapData(jsonStr);
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new ExecuteException("导入失败或数据已存在，请确认!");
        }
    }

    /**
     * 批量新增地图相关数据
     * @param jsonStr
     */
    private void batchInsertMapData(String jsonStr){
        Map<String, Object> dataMap = JSON.parseObject(jsonStr, Map.class);
        for (String key : dataMap.keySet()) {
            List obj = (List)dataMap.get(key);
//            List list = JSON.parseObject(JSON.toJSONString(o), List.class);
            logger.debug("----key : " + key + ", value : " + obj.toString());
            if (CollectionUtils.isEmpty(obj)) {
                continue;
            }
            switch (key) {
                case AGVMap:
                    List<com.youibot.agv.scheduler.entity.AGVMap> agvMapList = new ArrayList<>();
                    obj.forEach(object->agvMapList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<com.youibot.agv.scheduler.entity.AGVMap>(){})));
                    agvMapService.deleteByIds(agvMapList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    agvMapList.forEach(agvMap -> agvMap.setUsageStatus(MAP_USAGE_STATUS_DISABLE));
                    agvMapService.batchInsert(agvMapList);
                    break;
                case Marker:
                    List<com.youibot.agv.scheduler.entity.Marker> markerList = new ArrayList<>();
                    obj.forEach(object-> markerList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<com.youibot.agv.scheduler.entity.Marker>(){})));
                    markerService.deleteByIds(markerList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    markerService.batchInsert(markerList);
                    break;
                case SidePath:
                    List<SidePath> sidePathList = new ArrayList<>();
                    obj.forEach(object-> sidePathList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<SidePath>(){})));
                    sidePathService.deleteByIds(sidePathList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    sidePathService.batchInsert(sidePathList);
                    break;
                case MapArea:
                    List<com.youibot.agv.scheduler.entity.MapArea> mapAreaList = new ArrayList<>();
                    obj.forEach(object-> mapAreaList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<com.youibot.agv.scheduler.entity.MapArea>(){})));
                    mapAreaService.deleteByIds(mapAreaList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    mapAreaService.batchInsert(mapAreaList);
                    break;
                case Path:
                    List<com.youibot.agv.scheduler.entity.Path> pathList = new ArrayList<>();
                    obj.forEach(object-> pathList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<com.youibot.agv.scheduler.entity.Path>(){})));
                    pathService.deleteByIds(pathList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    pathService.batchInsert(pathList);
                    break;
                case DockingPoint:
                    List<com.youibot.agv.scheduler.entity.DockingPoint> dockingPointList = new ArrayList<>();
                    obj.forEach(object-> dockingPointList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<com.youibot.agv.scheduler.entity.DockingPoint>(){})));
                    dockingPointService.deleteByIds(dockingPointList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    dockingPointService.batchInsert(dockingPointList);
                    break;
                case DataCodeMatrix:
                    List<com.youibot.agv.scheduler.entity.DataCodeMatrix> dataCodeMatrixList = new ArrayList<>();
                    obj.forEach(object-> dataCodeMatrixList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<com.youibot.agv.scheduler.entity.DataCodeMatrix>(){})));
                    dataCodeMatrixService.deleteByIds(dataCodeMatrixList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    dataCodeMatrixService.batchInsert(dataCodeMatrixList);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 获取数据库地图数据的JSON字符串
     */
    private String getMapDateJsonString(String mapId) {
        logger.debug("getMapDateJsonString mapId : {}", mapId);
        Map<String, Object> dataMap = new HashedMap<>();
        if(StringUtils.isEmpty(mapId)){
            dataMap.put(AGVMap, agvMapService.findAll());
            dataMap.put(Marker, markerService.findAll());
            dataMap.put(SidePath, sidePathService.findAll());
            dataMap.put(MapArea, mapAreaService.findAll());
            dataMap.put(Path, pathService.findAll());
            dataMap.put(DockingPoint, dockingPointService.findAll());
            dataMap.put(DataCodeMatrix, dataCodeMatrixService.findAll());
        }else {
            List<String> ids = new ArrayList<>(2);
            ids.add(mapId);
            dataMap.put(AGVMap, agvMapService.selectByIds(ids));
            dataMap.put(Marker, markerService.selectByAGVMapId(mapId));
            dataMap.put(SidePath, sidePathService.selectByAGVMapId(mapId));
            dataMap.put(MapArea, mapAreaService.selectByAGVMapId(mapId));
            dataMap.put(Path, pathService.selectByAGVMapId(mapId));
            dataMap.put(DockingPoint, dockingPointService.selectByAGVMapId(mapId));
            dataMap.put(DataCodeMatrix, dataCodeMatrixService.selectByAGVMapId(mapId));
        }
        String jsonString = JSON.toJSONString(dataMap);
        logger.debug("getMapDateJsonString jsonString value : " + jsonString);
        return StringUtils.isEmpty(jsonString)?"":jsonString;
    }

    private static<T> List<T> getList(T t) {
        if (t == null){
            return null;
        }
        List<T> list = new ArrayList(2);
        list.add(t);
        return list;
    }

}
