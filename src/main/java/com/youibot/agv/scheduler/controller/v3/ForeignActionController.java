package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.bean.push.foreignAction.ForeignStatusMessage;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.param.ForeignActionRequestParam;
import com.youibot.agv.scheduler.param.ForeignActionResponseParam;
import com.youibot.agv.scheduler.param.ForeignStatusRequestParam;
import com.youibot.agv.scheduler.service.ForeignActionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController("ForeignActionControllerV3")
@RequestMapping(value = "/api/v3/foreignAction", produces = "application/json")
@Api(value = "外部动作调用", tags = "外部动作调用", description = "外部动作调用")
public class ForeignActionController {

    @Autowired
    private ForeignActionService foreignActionService;

    @ApiOperation(value = "外部动作调用执行")
    @ApiImplicitParam(name = "foreignActionRequestParam", value = "外部动作调用执行请求参数", required = true, dataType = "ForeignActionRequestParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public ForeignActionResponseParam save(@RequestBody ForeignActionRequestParam foreignActionRequestParam) {
        ForeignActionResponseParam foreignActionResponseParam = new ForeignActionResponseParam();
        if (foreignActionRequestParam.getAgvCode() != null) {
            foreignActionResponseParam.setAgvCode(foreignActionRequestParam.getAgvCode());
        }
        try {
            //校验参数和机器人状态
            foreignActionService.verifyParamAndAGVStatus(foreignActionRequestParam);
            //保存动作并发送MQ至compass
            String actionId = foreignActionService.insertForeignAction(foreignActionRequestParam);
            foreignActionResponseParam.setActionId(actionId);
            foreignActionResponseParam.setCode(1);
        } catch (ExecuteException e) {
            foreignActionResponseParam.setCode(0);
            foreignActionResponseParam.setMsg(e.getMessage());
        }
        return foreignActionResponseParam;
    }

    @ApiOperation(value = "外部动作状态查询")
    @ApiImplicitParam(name = "foreignStatusRequestParam", value = "外部动作状态查询请求参数", required = true, dataType = "ForeignStatusRequestParam")
    @PostMapping("/getActionStatus")
    @ResponseStatus(value = HttpStatus.CREATED)
    public ForeignActionResponseParam getActionStatus(@RequestBody ForeignStatusRequestParam foreignStatusRequestParam) {
        ForeignActionResponseParam foreignActionResponseParam = new ForeignActionResponseParam();
        String actionId = foreignStatusRequestParam.getActionId();
        String agvCode = foreignStatusRequestParam.getAgvCode();
        String callBackUrl = foreignStatusRequestParam.getCallBackUrl();
        try {
            //校验查询参数
            verifyParam(actionId, agvCode, callBackUrl);
            foreignActionResponseParam.setAgvCode(agvCode);
            foreignActionResponseParam.setActionId(actionId);
            foreignActionResponseParam.setCode(1);
            //发送MQ至compass查询动作状态
            ForeignStatusMessage foreignStatusMessage = new ForeignStatusMessage(agvCode, actionId, callBackUrl);
            MqttUtils.sendMqttMsg(MqttConstant.FOREIGN_STATUS_TOPIC, agvCode, foreignStatusMessage);
        } catch (ExecuteException e) {
            foreignActionResponseParam.setCode(0);
            foreignActionResponseParam.setMsg(e.getMessage());
        }
        return foreignActionResponseParam;
    }

    private void verifyParam(String actionId, String agvCode, String callBackUrl) {
        Optional.ofNullable(actionId).orElseThrow(() -> new ExecuteException("动作actionId不能为空"));
        Optional.ofNullable(agvCode).orElseThrow(() -> new ExecuteException("机器人编号不能为空"));
        Optional.ofNullable(callBackUrl).orElseThrow(() -> new ExecuteException("回调地址不能为空"));

    }

}
