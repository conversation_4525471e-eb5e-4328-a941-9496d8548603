package com.youibot.agv.scheduler.controller.v3;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Table.Cell;
import com.google.common.collect.TreeBasedTable;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.TMSMarkerIdsSequenceAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.TSPGeneticAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.TSPTMS1GeneticAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.TSPTMS2GeneticAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.TSPTMS3GeneticAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.TSPTMS4GeneticAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.TSPTMS5FloorAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.entity.Position;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleScope;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.VehicleScopeService;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.param.TMSFloor;
import com.youibot.agv.scheduler.param.TMSMarkerIdSequence;
import com.youibot.agv.scheduler.param.TMSMaterial;
import com.youibot.agv.scheduler.param.TMSMaterial4;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/** 这个类需要做重新封装
 * <AUTHOR>
 *
 */
@Api(tags = "通过markId 获取路径顺序")
@RequestMapping("/api/v3/bestWayWitchMarkId")
@RestController
public class GetBestWayWithMarkIdController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetBestWayWithMarkIdController.class);

    @Autowired
    private VehicleScopeService vehicleScopeService;
    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private FullLocationService fullLocationService;
    @Autowired
    private MarkerService markerService;
    
    @Autowired
    private VehiclePool defaultVehiclePool;
    
    @Autowired
    private LocationService locationService;
    
    @Autowired
    private CompassController compassController;

    private TSPGeneticAlgorithm tsp = new TSPGeneticAlgorithm();
    private TSPTMS1GeneticAlgorithm tspTms1 = new TSPTMS1GeneticAlgorithm();
    private TSPTMS2GeneticAlgorithm tspTms2 = new TSPTMS2GeneticAlgorithm();
    private TSPTMS3GeneticAlgorithm tspTms3 = new TSPTMS3GeneticAlgorithm();
    private TSPTMS4GeneticAlgorithm tspTms4 = new TSPTMS4GeneticAlgorithm();
    private TSPTMS5FloorAlgorithm tspTms5 = new TSPTMS5FloorAlgorithm();
    private TMSMarkerIdsSequenceAlgorithm tmsSequence1 = new TMSMarkerIdsSequenceAlgorithm();

    @LogOperation
    @ApiOperation("通过站点获取站点路径优先级,有设置起始点")
    @PostMapping("/getBestWay")
    public Map<String, Integer> getBestWay(@RequestBody LinkedHashSet<String> set) {

        LOGGER.info("set:{}", JSON.toJSONString(set));
        List<String> list = new LinkedList<>(set);
        Map<String, Integer> result = Maps.newHashMap();
        if (list.size() == 1) {
            result.put(list.get(0), 0);
        } else {
            String[] path = getWay(list, true);
            //起始站点
            result.put(list.get(0), 0);
            for (int i = 0; i < path.length; i++) {
                result.put(path[i], i + 1);
            }
        }
        return result;
    }

    @LogOperation
    @ApiOperation("通过站点获取站点路径优先级,不设置起始点")
    @PostMapping("/getBestWayNoStart")
    public Map<String, Integer> getBestWayNoStart(@RequestBody Set<String> set) {
        LOGGER.info("set:{}", JSON.toJSONString(set));
        List<String> list = new LinkedList<>(set);
        Map<String, Integer> result = Maps.newHashMap();
        if (list.size() == 1) {
            result.put(list.get(0), 0);
        } else {
            String[] path = getWay(list, false);
            for (int i = 0; i < path.length; i++) {
                result.put(path[i], i);
            }
        }
        return result;
    }

    @LogOperation
    @ApiOperation("上下料路径规划一起完成,同时给一组上料点,一组下料点,一起给出上下料顺序")
    @PostMapping("/getLoadUnloadMaterialSequence1")
    public Map<String, Integer> getLoadUnloadMaterialSequence1(@RequestBody TMSMaterial tmsMaterial) {
        List<String> loadMaterial = tmsMaterial.getLoadMaterial();
        List<String> unloadMaterial = tmsMaterial.getUnloadMaterial();
        tspTms1.GAInit(loadMaterial, unloadMaterial);
        String[] path = tspTms1.run();
        Map<String, Integer> map = new ConcurrentHashMap<>();
        for (int i = 0; i < path.length; i++) {
            map.put(path[i], i);
        }
        return map;
    }

    @LogOperation
    @ApiOperation("同时给出一组上料点,下料点, 按顺序给出依次给出 上-下-上-下 这种顺序")
    @PostMapping("/getLoadUnloadMaterialSequence2")
    public Map<Pair<String, String>, Integer> getLoadUnloadMaterialSequence2(@RequestBody TMSMaterial tmsMaterial) {
        List<String> loadMaterial = tmsMaterial.getLoadMaterial();
        List<String> unloadMaterial = tmsMaterial.getUnloadMaterial();
        tspTms2.GAInit(loadMaterial, unloadMaterial);
        List<Pair<String, String>> path = tspTms2.run();
        Map<Pair<String, String>, Integer> map = new ConcurrentHashMap<>();
        for (int i = 0; i < path.size(); i++) {
            map.put(path.get(i), i);
        }
        return map;
    }

    @LogOperation
    @ApiOperation("同时给出两组缓冲区标记点列表,从两组标记点列表中各取一个，返回距离最近的两个点")
    @PostMapping("/getLoadUnloadMaterialSequence3")
    public Pair<String, String> getLoadUnloadMaterialSequence3(@RequestBody TMSMaterial tmsMaterial) {
        if (tmsMaterial == null) {
            throw new ExecuteException("错误的参数");
        }
        List<String> loadMaterial = tmsMaterial.getLoadMaterial();
        List<String> unloadMaterial = tmsMaterial.getUnloadMaterial();
        tspTms3.GAInit(loadMaterial, unloadMaterial);
        return tspTms3.run();
    }

    @LogOperation
    @ApiOperation("给出一组标记点和一个目标点,从该组标记点中取一个，返回距离最近的那个导航点")
    @PostMapping("/getLoadUnloadMaterialSequence4")
    public String getLoadUnloadMaterialSequence4(@RequestBody TMSMaterial4 tmsMaterial4) {
        if (tmsMaterial4 == null) {
            throw new ExecuteException("错误的参数");
        }
        List<String> loadMaterial = tmsMaterial4.getLoadMaterialList();
        String unloadMaterial = tmsMaterial4.getUnloadMaterial();
        tspTms4.GAInit(loadMaterial, unloadMaterial);
        return tspTms4.run();
    }

    @LogOperation
    @ApiOperation("提升机跨楼层运输,选择最短路径")
    @PostMapping("/getLoadUnloadMaterialSequence5")
    public Pair<String, String> getMarkerIdsSequence1(@RequestBody TMSFloor tmsFloor) {
        if (tmsFloor == null) {
            throw new ExecuteException("错误的参数");
        }
        String startMarkerId = tmsFloor.getStartMarkerId();
        String endMarkerId = tmsFloor.getEndMarkerId();
        List<Pair<String, String>> floorMarkerPairList = tmsFloor.getFloorMarkerPairList();

        Marker startMarker = MapGraphUtil.getMarkerByMarkerId(startMarkerId);
        Marker endMarker = MapGraphUtil.getMarkerByMarkerId(endMarkerId);
        if (startMarker == null || endMarker == null) {
            throw new ExecuteException("点未启用或不存在");
        }
        for (Pair<String, String> stringStringPair : floorMarkerPairList) {
            Marker floorMarkerPairFirst = MapGraphUtil.getMarkerByMarkerId(stringStringPair.first());
            Marker floorMarkerPairSecond = MapGraphUtil.getMarkerByMarkerId(stringStringPair.second());
            if (floorMarkerPairFirst == null || floorMarkerPairSecond == null) {
                throw new ExecuteException("点未启用或不存在");
            }
            if (!startMarker.getAgvMapName().equals(floorMarkerPairFirst.getAgvMapName())) {
                throw new ExecuteException("起始点与提升机起始点不在同一张地图上");
            }
            if (!endMarker.getAgvMapName().equals(floorMarkerPairSecond.getAgvMapName())) {
                throw new ExecuteException("终止点点与提升机终止点不在同一张地图上");
            }
        }
        tspTms5.GAInit(startMarkerId, floorMarkerPairList, endMarkerId);
        return tspTms5.run();
    }

    @LogOperation
    @ApiOperation("给出一个agvCode和一组的markerId, 根据markerId距离agvCode的距离进行排序，距离小的markerId在前")
    @PostMapping("/getMarkerIdsSequence")
    public Map<String, Integer> getMarkerIdsSequence(@RequestBody TMSMarkerIdSequence tmsMarkerIdSequence) {
    	   String agvCode = tmsMarkerIdSequence.getAgvCode();
           List<String> markerIds = tmsMarkerIdSequence.getMarkerIds();
//           VehicleLocation vehicleLocation = locationService.getVehicleLocation(agvCode);
        	  String agvLocation = getAgvLocationByAgvCode(agvCode);
        
           Map<String, Integer> map = new LinkedHashMap<>();
           if(  markerIds.contains( agvLocation)) {
           	map.put( agvLocation ,  -1 ) ;
           	return  map ;
           }
           
         
           tmsMarkerIdSequence.setAgvLocationMarkerId(agvLocation);
           return getNearCodes(markerIds, agvLocation);
    }

    @LogOperation
    @ApiOperation("获取agv当前的位置: 可用于判断车辆是否脱轨")
    @GetMapping("/getAgvLocationByAgvCode/{agvCode}")
	public String getAgvLocationByAgvCode(@PathVariable String agvCode) {
        if (Objects.isNull(agvCode)) {
            throw new ExecuteException("车辆Id 错误:" + agvCode);
        }
		 Vehicle vehicle = vehiclePoolService.selectById( agvCode);
		 if (Objects.isNull(vehicle)) {
	            throw new ExecuteException("车辆Id 错误:" + agvCode);
	        }
         VehicleLocation vehicleLocation = CommonUtils.getValueWithIn(() ->fullLocationService.getVehicleLocation(agvCode), 1500);
       
       
         String agvLocation = null;
         if ( vehicleLocation == null) {
  		   throw new ExecuteException("该机器人已经脱轨，请检查机器人状态");
           }
         
         if (vehicleLocation.getMarker() != null) {
		   agvLocation = vehicleLocation.getMarker().getId();
         } else if (!CollectionUtils.isEmpty(vehicleLocation.getSidePaths())) {
		   agvLocation = vehicleLocation.getSidePaths().get(0).getEndMarkerId();
         }
         if(Objects.isNull(agvLocation) && StringUtils.isNotBlank( vehicle.getLastMarkerId())) {
	   		  vehicleLocation = new VehicleLocation();
	   		  vehicleLocation.setMarker(markerService.selectById( vehicle.getLastMarkerId()));
	   		  vehicleLocation.setUpdateTimeMillis(System.currentTimeMillis());
            }
         if (agvLocation == null) {
		   throw new ExecuteException("该机器人已经脱轨，请检查机器人状态");
         }
		return agvLocation;
	}

    @LogOperation
    @ApiOperation("给出一个agvCode，并指定agvCocation和一组的markerId, 给出agv得到这组markId 的距离排序")
    @PostMapping("/getMarkerIdsSequenceByAgvCode")
    public Map<String, Integer> getNearCodes2(@RequestBody TMSMarkerIdSequence tMSMarkerIdSequence) {
		 String agvCode = tMSMarkerIdSequence.getAgvCode();
		LOGGER.debug("agvCode:{},agvLocationMakerId:{}", agvCode , tMSMarkerIdSequence.getAgvLocationMarkerId());
	        List<VehicleScope> vehicleScopes = new ArrayList<>();
	        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
	        if (Objects.isNull(vehicle)) {
                throw new ExecuteException("车辆Id 错误:" + agvCode);
            }
	        
	        List<String> markerIds = tMSMarkerIdSequence.getMarkerIds();
	        
	        if (CollectionUtils.isEmpty(markerIds)) {
                throw new ExecuteException("目标导航点不能为空" );
            }
	        
	        for (String makerId : markerIds) {
	            Double distanceScope = vehicleScopeService.scopeByDistance(makerId, vehicle);
	            if (distanceScope != null) {
	                vehicleScopes.add(new VehicleScope(makerId, distanceScope));
	            }
	        }
	        //评分排序，降序
	        vehicleScopes.sort(new Comparator<VehicleScope>() {
	            @Override
	            public int compare(VehicleScope o1, VehicleScope o2) {
	                return o2.getScope().compareTo(o1.getScope());
	            }
	        });
	        Map<String, Integer> map = new LinkedHashMap<>();
	        for (int i = 0; i < vehicleScopes.size(); i++) {
	            map.put(vehicleScopes.get(i).getMakerId() , i);
	        }
	        
		return map;
	}

	@LogOperation
    @ApiOperation("给定一个markId, 和一组markIds ,返回距离这组markCode 的距离排序， 跃进越小")
    @PostMapping("/getMarkerIdsSequenceStarCode/{startMarkerId}")
	public Map<String, Integer> getNearCodes(@RequestBody List<String> markerIds,@PathVariable("startMarkerId") String startMarkerId) {
		  Map<String, Integer> map = new LinkedHashMap<>();
		  tmsSequence1.init( startMarkerId, markerIds);

           List<String> sortMarkerIds = tmsSequence1.run(); 
           String temp = null;
           for (int i = 0; i < sortMarkerIds.size(); i++) {
        	   String key = sortMarkerIds.get(i);
        	   if(Objects.isNull(temp)) {
        		   temp = key; 
        	   }
        	   SidePath sidePath = MapGraphUtil.getSidePathByStartEndMarkerId(startMarkerId,  key) ;
        	   boolean equals = StringUtils.equals(temp, startMarkerId);
              if( Objects.nonNull(sidePath) && !equals ) {
            	  map.put(key, -i );
            	  continue;
              }
              map.put(key, i );
           }
           return map;
	}

	@LogOperation
    @ApiOperation("给定一个markCode, 和一组markCode ,返回距离这组markCode 的距离排序， 跃进越小")
    @PostMapping("/getMarkerIdsSequenceStarCodes/{agvMapId}/{startMarkerCode}")
	public Map<String, Integer> getNearCodesByCodes(@RequestBody List<String> markerCodes,@PathVariable("agvMapId") String agvMapId ,@PathVariable("startMarkerCode") String startMarkerCode) {
		Marker startMarker = markerService.selectMarkerByCode(agvMapId, startMarkerCode);
		if (Objects.isNull(startMarker)) {
            throw new ExecuteException("起始markCode不存在:" + startMarkerCode);
        }
		   markerCodes =markerCodes.parallelStream().map(i ->{
			Marker marker = markerService.selectMarkerByCode(agvMapId, i);
			
			return Objects.nonNull(marker) ? marker.getId() :"";
		}).filter(p -> StringUtils.isNotBlank(p)).collect(Collectors.toList());
		  
		    Map<String, Integer> res = new HashedMap<>();
           Map<String, Integer> nearCodes = this.getNearCodes(markerCodes, startMarker.getId());
           nearCodes.entrySet().forEach( item -> {
        	   Marker marker = MapGraphUtil.getMarkerByMarkerId( item.getKey());
        	   res.put(marker.getCode(),  item.getValue()) ;
           });
		return  res;
	}
	
    @LogOperation
    @ApiOperation("给一个markerId,根据这个markerId距离机器人列表的距离进行排序，距离小机器人的在前")
    @GetMapping("/getAgvCodesSequence/{markerId}")
    public Map<String, Integer> getAgvCodesSequence(@PathVariable("markerId") String markerId) {
        if (markerId == null) {
            throw new ExecuteException("错误的参数");
        }
        if (MapGraphUtil.getMarkerByMarkerId(markerId) == null) {
            throw new ExecuteException("错误的参数, 该参数不存在");
        }
        Map<String, Integer> map = new LinkedHashMap<>();
        List<Vehicle> vehicles = vehiclePoolService.getWaitWork();
        if (CollectionUtils.isEmpty(vehicles)) {
            return map;
        }
        // vehicles 评分后排序。
        List<VehicleScope> vehicleScopes = new ArrayList<>();
        for (Vehicle vehicle : vehicles) {
            Double distanceScope = vehicleScopeService.scopeByDistance(markerId, vehicle);
            if (distanceScope != null) {
                vehicleScopes.add(new VehicleScope(vehicle, distanceScope));
            }
        }
        //评分排序，降序
        vehicleScopes.sort(new Comparator<VehicleScope>() {
            @Override
            public int compare(VehicleScope o1, VehicleScope o2) {
                return o2.getScope().compareTo(o1.getScope());
            }
        });
        for (int i = 0; i < vehicleScopes.size(); i++) {
            map.put(vehicleScopes.get(i).getVehicle().getId(), i);
        }
        return map;
    }


   
	@LogOperation
    @ApiOperation("给定地图求最大换中车距离起始点的距离,距离从小到大排序")
    @GetMapping("/getAgvCodesSequenceAgvCode/{agvMapId}")
    public  MutableTriple<String,LinkedHashMap<String , Double > , Double> getAgvCodesSequenceAgvCode(@PathVariable("agvMapId") String agvMapId){
    	
		MutableTriple<String, LinkedHashMap<String, Double>  , Double>  res = new MutableTriple<>();
         Map<String , MutablePair<SidePath, Double> > data = Maps.newConcurrentMap();
         
         LinkedHashMap<String , Double > agvDistance = new LinkedHashMap<>();

         res.setMiddle(agvDistance);
         List<Vehicle> vehicles = vehiclePoolService.getAvailable( false  );
         vehicles =  vehicles.parallelStream().filter(p -> Objects.nonNull(p.getControlMode()) && StringUtils.isNotBlank(p.getAgvMapId())).collect(Collectors.toList());
         if (CollectionUtils.isEmpty(vehicles)) {
             return res;
         }
         List<SidePath> oCycleMaxStationMax = compassController.getContainsSidePathWorkStation( agvMapId);
         
         if(CollectionUtils.isEmpty(oCycleMaxStationMax)) {
        	 LOGGER.debug("agvMapId:{},no cycle found" , agvMapId ) ;
        	 return res;
         }
         SidePath firstSidePath =  oCycleMaxStationMax.get(0);
         Marker firtMarker = markerService.selectById( firstSidePath.getStartMarkerId()) ;
         res.setLeft( firtMarker.getCode()) ;
         
         vehicles.parallelStream().forEach(item ->{
        	 
        	 String id = item.getId();
        	 Position position = locationService.getAgvPosition(id);
        	 MutablePair<SidePath, Double> setCurrentSidePathT0 = locationService.setCurrentSidePathT0(item.getAgvMapId(), position.getPos_x(), position.getPos_y(), oCycleMaxStationMax);
        	 data.put(item.getId(), setCurrentSidePathT0) ;
  
         });
         
          Double length = 0D;
         MultiValueMap<String, String> pathIdAgvs = TjdCxt.pathIdAgvs();
         for (SidePath item :   oCycleMaxStationMax ) {
			 String pathId = item.getPathId();
			 /**
			  * 从起点开始的累计距离
			  */
			 length = length + item.getLength();
			
			 List<String> list = pathIdAgvs.get(pathId) ;
			 if(CollectionUtils.isNotEmpty(list)) {
				 for ( String agvId : list) {
					 MutablePair<SidePath, Double> mutablePair = data.get(agvId);
					 if( Objects.isNull(mutablePair)) {
						 LOGGER.warn("mutablePair_no_data _found:{}" ,agvId);
						 continue;
					 }
					 SidePath key = mutablePair.getKey();
					 if(Objects.isNull(key)) {
						 continue;
					 }
					Double length2 = key.getLength();
					if(Objects.isNull(length2)) {
						length2 = 0D; 
					}
					double d = length2 * mutablePair.getValue();
					 agvDistance.put(agvId, length + d);
					
				}
				
			 }
			
		}
         res.setRight(length);
         return res;
         
    }
    
  
	@LogOperation
    @ApiOperation("给定地图求最大环求相邻辆车的车间距")
    @GetMapping("/getAgvCodesSpan/{agvMapId}")
    public  Set<Cell<String ,String, Double>> getSpanBetweenVehicles(@PathVariable("agvMapId") String agvMapId){
		
		TreeBasedTable<String, String, Double> res = getVehicleTable(agvMapId);
		 
		 return res.cellSet();
	}

	/** 获得车辆的位置排序
	 * @param agvMapId
	 * @return
	 */
	public TreeBasedTable<String, String, Double> getVehicleTable(String agvMapId) {
		TreeBasedTable<String, String, Double > res = TreeBasedTable.create();
		
		 MutableTriple<String,LinkedHashMap<String , Double > , Double> triple = this.getAgvCodesSequenceAgvCode(agvMapId) ;
		 LinkedHashMap<String, Double> value = triple.getMiddle();
		  
		 /**
		  * 总距离
		  */
		 Double total = triple.getRight();
		 
		 String firstKey = null;
		 Double firstValue = 0D;
		 String pkey = null;
		 Double pvalue = 0D;
		 for (Entry<String, Double> entry : value.entrySet()) {
			 String currentKey = entry.getKey();
			 Double currentValue = entry.getValue();
			 if(StringUtils.isBlank(firstKey)) {
				 firstKey = currentKey;
				 firstValue = currentValue ;
			 }
			if( StringUtils.isNotBlank(pkey)) {
				
				res.put(pkey ,  currentKey , currentValue  - pvalue );
			 }

			 pkey= currentKey ;
			 pvalue = currentValue;
			}
		 
		    if(StringUtils.isNotBlank(pkey) && StringUtils.isNotBlank( firstKey) ){
		    	 res.put(  pkey  ,  firstKey , total - ( pvalue  - firstValue ) );
		    }
		
		return res;
	}
    /**
     * @param list
     * @param fromStartPoint true从起始点开始 false不设置固定起始点
     * @return
     */
    private String[] getWay(List<String> list, boolean fromStartPoint) {
        String start = null;
        if (fromStartPoint) {
            //有起始点
            start = list.get(0);
            LOGGER.info("start:{}", start);
            list.remove(0);
        }
        String[] remain = list.toArray(new String[0]);
        tsp.GAInit(start, remain);
        return tsp.run();
    }
}
