package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.service.MissionWorkActionParameterService;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("missionWorkActionControllerV3")
@RequestMapping(value = "/api/v3/missionWorkActions", produces = "application/json")
@Api(value = "工作动作", tags = "工作动作", description = "工作执行的动作列表，由系统在执行任务的过程中自动创建。")
public class MissionWorkActionController extends BaseController {

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionWorkActionParameterService missionWorkActionParameterService;

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkActionController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionWorkAction> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkActionService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkAction> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkActionService.searchAll(searchMap);
    }

    @ApiIgnore
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "missionWorkAction", value = "工作动作", required = true, dataType = "MissionWorkAction")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionWorkAction save(@RequestBody @Valid MissionWorkAction missionWorkAction) {
        this.missionWorkActionService.insert(missionWorkAction);
        LOGGER.debug(missionWorkAction.toString());
        return missionWorkAction;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "工作动作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWorkAction get(@PathVariable("id") String id) {
        return missionWorkActionService.selectById(id);
    }

    @ApiIgnore
    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "工作动作ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionWorkAction", value = "工作动作", required = true, dataType = "MissionWorkAction")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWorkAction update(@PathVariable("id") String id, @RequestBody @Valid MissionWorkAction missionWorkAction) {
        missionWorkAction.setId(id);
        this.missionWorkActionService.update(missionWorkAction);
        return this.missionWorkActionService.selectById(id);
    }

    @ApiIgnore
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "工作动作ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.missionWorkActionService.deleteById(id);
    }

    @ApiOperation(value = "根据任务动作ID查询参数列表")
    @ApiImplicitParam(name = "id", value = "任务动作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionWorkActionParameters")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkActionParameter> getMissionWorkActionParameters(@PathVariable("id") String id) {
        MissionWorkActionParameter missionWorkActionParameter = new MissionWorkActionParameter();
        missionWorkActionParameter.setMissionWorkActionId(id);
        return missionWorkActionParameterService.findByEntity(missionWorkActionParameter);
    }

}
