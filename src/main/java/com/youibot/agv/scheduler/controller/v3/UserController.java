package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.User;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.UserService;
import com.youibot.agv.scheduler.util.JWTUtil;
import com.youibot.agv.scheduler.util.Md5Util;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController("userControllerV3")
@RequestMapping(value = "/api/v3/users", produces = "application/json")
@ApiIgnore
@Api(value = "用户", tags = "用户")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    private static final Logger LOGGER = LoggerFactory.getLogger(UserController.class);

    @ApiOperation(value = "用户分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<User> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return userService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<User> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return userService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "user", value = "用户", required = true, dataType = "User")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public User save(@RequestBody @Valid User user) {
        String uuid = UUID.randomUUID().toString();
        user.setId(uuid);
        // md5密码加密 盐为用户uuid
        String encryptPassWord = Md5Util.pwdMD5WithSalt(user.getPassword(), uuid);
        user.setPassword(encryptPassWord);
        this.userService.insert(user);
        LOGGER.debug(user.toString());
        return user;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "用户ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public User get(@PathVariable("id") String id) {
        return userService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "用户ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "user", value = "用户信息", required = true, dataType = "User")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public User update(@PathVariable("id") String id, @RequestBody @Valid User user) {
        user.setId(id);
        if (!StringUtils.isEmpty(user.getPassword())) {
            String encryptPassword = Md5Util.pwdMD5WithSalt(user.getPassword(), user.getId());
            user.setPassword(encryptPassword);
        }
        this.userService.update(user);
        return userService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "用户ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public int delete(@PathVariable("id") String id) {
        return this.userService.deleteById(id);
    }

    @ApiOperation(value = "登录", notes = "根据用户信息登录")
    @ApiImplicitParam(name = "user", value = "用户", required = true, dataType = "User")
    @PostMapping(value = "/tokens")
    @ResponseStatus(value = HttpStatus.CREATED)
    public String login(@RequestBody @Valid User user) {
        String loginName = user.getLoginName();
        String password = user.getPassword();
        if (StringUtils.isEmpty(loginName) || StringUtils.isEmpty(password)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        User us = new User();
        us.setLoginName(loginName);
        // 通过登录名查询
        List<User> users = this.userService.findByEntity(us);
        if (users == null || users.isEmpty()) {
            throw new ExecuteException(MessageUtils.getMessage("service.user_is_null"));
        } else {
            User userPara = users.get(0);
            String passwordWithSalt = Md5Util.pwdMD5WithSalt(password, userPara.getId());
            if (!userPara.getPassword().equals(passwordWithSalt)) {
                throw new ExecuteException(MessageUtils.getMessage("http.user_password_error"));
            } else {
                String token = JWTUtil.createToken(userPara);
                return JWTUtil.TOKEN_PREFIX + token;
            }
        }

    }

}
