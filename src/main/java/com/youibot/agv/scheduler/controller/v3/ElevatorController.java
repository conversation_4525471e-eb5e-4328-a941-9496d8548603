package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.entity.Elevator;
import com.youibot.agv.scheduler.service.ElevatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("ElevatorControllerV3")
@RequestMapping(value = "/api/v3/elevators", produces = "application/json")
@Api(value = "电梯", tags = "电梯", description = "机器人在执行跨楼层任务时需要电梯辅助完成")
public class ElevatorController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(com.youibot.agv.scheduler.controller.v3.ElevatorController.class);

    @Autowired
    private ElevatorService elevatorService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Elevator> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return elevatorService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Elevator> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return elevatorService.searchAll(searchMap);
    }

    @LogOperation
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "elevator", value = "电梯", required = true, dataType = "Elevator")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Elevator save(@RequestBody @Valid Elevator elevator) {
        this.elevatorService.insert(elevator);
        LOGGER.debug(elevator.toString());
        return elevator;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "电梯ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Elevator get(@PathVariable("id") String id) {
        return elevatorService.selectById(id);
    }

    @LogOperation
    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "电梯ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "elevator", value = "电梯", required = true, dataType = "Elevator")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Elevator update(@PathVariable("id") String id, @RequestBody @Valid Elevator elevator) {
        elevator.setId(id);
        this.elevatorService.update(elevator);
        return this.elevatorService.selectById(elevator.getId());
    }

    @LogOperation
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "电梯ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.elevatorService.deleteById(id);
    }
}
