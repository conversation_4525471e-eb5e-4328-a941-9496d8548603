package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.constant.MapConstant;
import com.youibot.agv.scheduler.entity.AdjustAction;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AdjustActionService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.SidePathService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 调整动作
 * @Author：yangpeilin
 * @Date: 2020/7/23 17:58
 */
@RestController("AdjustActionControllerV3")
@RequestMapping(value = "/api/v3/adjustAction", produces = "application/json")
@Api(value = "调整动作", tags = "调整动作", description = "导航点动作调整，调整：机器人旋转角度、货架旋转角度")
public class AdjustActionController {

    private static final Logger logger = LoggerFactory.getLogger(AdjustActionController.class);

    @Autowired
    private AdjustActionService adjustActionService;

    @Autowired
    private SidePathService sidePathService;

    @Autowired
    private MarkerService markerService;

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "adjustAction", value = "调整动作", required = true, dataType = "AdjustAction")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AdjustAction save(@RequestBody @Valid AdjustAction adjustAction) {
        handleStopMarkerAndPath(adjustAction, true);
        this.adjustActionService.insert(adjustAction);
        return adjustAction;
    }

    /**
     * 停卡点和路径判断
     * @param adjustAction
     * @param isSave
     */
    private void handleStopMarkerAndPath(@RequestBody @Valid AdjustAction adjustAction, boolean isSave) {
        String agvMapId = adjustAction.getAgvMapId();
        String markerId = adjustAction.getMarkerId();
        String destMarkerId = adjustAction.getDestMarkerId();
        if (StringUtils.isEmpty(destMarkerId) || StringUtils.isEmpty(markerId) || StringUtils.isEmpty(agvMapId) || markerId.equals(destMarkerId)) {
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        List<Marker> markers = markerService.selectByIds(Arrays.asList(markerId, destMarkerId));
        if (CollectionUtils.isEmpty(markers) || markers.size() != 2){
            logger.debug("markers size : {}", markers.size());
            throw new ExecuteException(MessageUtils.getMessage("http.marker_is_not_exist"));
        }
        markers.forEach(marker -> {
            String id = marker.getId();
            if (id.equals(markerId)){
                if (!MapConstant.MARKER_TYPE_ADJUSTMENT.equals(marker.getType())){
                    throw new ExecuteException(MessageUtils.getMessage("http.marker_is_not_adjust"));
                }
            }else if (id.equals(destMarkerId)){
                if (!MapConstant.MARKER_TYPE_WORK.equals(marker.getType())){
                    throw new ExecuteException(MessageUtils.getMessage("http.dest_marker_is_not_worker"));
                }
            }

        });
        if (isSave){
            AdjustAction adjustActionDto = adjustActionService.selectByMarkerIdAndDestMarkerId(agvMapId, markerId, destMarkerId);
            if (adjustActionDto != null){
                throw new ExecuteException(MessageUtils.getMessage("http.adjust_action_exist"));
            }
        }
        //判断两点之间是否存在路径
        SidePath sidePath = new SidePath();
        sidePath.setAgvMapName(agvMapId);
        sidePath.setStartMarkerId(markerId);
        sidePath.setEndMarkerId(destMarkerId);
        if (sidePathService.selectByEntity(sidePath) == null){
            logger.error("markerId to destMakerId is not have path, markerId:{},destMakerId:{}", markerId, destMarkerId);
            throw new ExecuteException(MessageUtils.getMessage("service.path_is_null"));
        }
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "调整动作主键id", required = true, dataType = "AdjustAction")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AdjustAction get(@PathVariable("id") String id) {
        return adjustActionService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "调整动作主键id", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adjustAction", value = "标记点", required = true, dataType = "AdjustAction")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AdjustAction update(@PathVariable("id") String id, @RequestBody @Valid AdjustAction adjustAction) {
        adjustAction.setId(id);
        handleStopMarkerAndPath(adjustAction, false);
        this.adjustActionService.update(adjustAction);
        return adjustAction;
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "调整动作主键id", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.adjustActionService.deleteById(id);
    }

    @ApiOperation(value = "调整点列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<AdjustAction> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return adjustActionService.searchAll(searchMap);
    }
}
