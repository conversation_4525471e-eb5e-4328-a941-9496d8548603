package com.youibot.agv.scheduler.controller.v3;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.WorkScheduler;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.service.MissionWorkCommandService;
import com.youibot.agv.scheduler.param.SchedulerStopParam;
import com.youibot.agv.scheduler.param.SchedulerStopResult;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.WorkSchedulerService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@RestController("workSchedulerControllerV3")
@RequestMapping(value = "/api/v3/workSchedulers", produces = "application/json")
@Api(value = "作业调度", tags = "作业调度", description = "作业调度管理接口")
public class WorkSchedulerController extends BaseController {

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private MissionWorkCommandService missionWorkCommandService;

    @Autowired
    private MissionWorkService missionWorkService;

    private static final Logger logger = LoggerFactory.getLogger(WorkSchedulerController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<WorkScheduler> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return workSchedulerService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<WorkScheduler> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return workSchedulerService.searchAll(searchMap);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "删除作业调度记录", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @LogOperation
    public void delete(@PathVariable("id") String id) {
        WorkScheduler workScheduler = workSchedulerService.selectById(id);
        if (WorkScheduler.STATUS_CANCEL.equals(workScheduler.getStatus()) || WorkScheduler.STATUS_SUCCESS.equals(workScheduler.getStatus())) {
            this.workSchedulerService.deleteById(id);
        } else {
            throw new ExecuteException(MessageUtils.getMessage("service.the_status_is_do_not_delete"));
        }
    }

    @ApiOperation(value = "取消")
    @ApiImplicitParam(name = "id", value = "取消作业调度", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/cancel")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public SchedulerStopResult cancel(@PathVariable("id") String id, @RequestBody SchedulerStopParam param) {
        WorkScheduler workScheduler = workSchedulerService.selectById(id);
        if (workScheduler == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.the_scheduler_is_empty"));
        }
        if (WorkScheduler.STATUS_CANCEL.equals(workScheduler.getStatus()) || WorkScheduler.STATUS_SUCCESS.equals(workScheduler.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("service.the_status_do_not_cancel"));
        }
        Vehicle vehicle = vehiclePoolService.selectById(workScheduler.getVehicleId());
        if (!param.isForcedStop() && (vehicle == null || !VehicleConstant.ONLINE.equals(vehicle.getOnlineStatus()))) {
            return new SchedulerStopResult("ERROR", 1001);//非强制取消且机器人不在线
        }

        if (vehicle == null || !VehicleConstant.ONLINE.equals(vehicle.getOnlineStatus())) {
            //如果机器人为离线状态, 不发送取消指令到mqtt, 直接清理相关资源, 目前该做法主要针对机器人关机掉线后, 无法快速清理资源的问题
            MissionWork missionWork = missionWorkService.selectById(workScheduler.getWorkId());
            if (missionWork == null) {
                workScheduler.setStatus(WorkScheduler.STATUS_CANCEL);
                workScheduler.setFinishTime(new Date());
                workScheduler.setFaultMessage("手动取消作业调度,对应作业不存在");
                workSchedulerService.update(workScheduler);
                throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
            }
            missionWork.setStatus(MISSION_WORK_STATUS_SHUTDOWN);
            missionWorkCommandService.updateMissionWork(missionWork);
        } else {
            if (WorkScheduler.STATUS_PREPARE.equals(workScheduler.getStatus())) {
                workScheduler.setStatus(WorkScheduler.STATUS_CANCEL);
                workScheduler.setFinishTime(new Date());
                workScheduler.setFaultMessage("手动取消任务预分配");
                workSchedulerService.update(workScheduler);
            } else {
                vehicle.cancelMissionWork(workScheduler.getWorkId());
            }
        }
        logger.debug("agvCode:{},event:[取消作业调度],当前取消的作业调度数据：[{}]",workScheduler.getVehicleId(),JSONObject.toJSONString(workScheduler));

        return new SchedulerStopResult("SUCCESS", null);
    }
    
 
  
  
}
