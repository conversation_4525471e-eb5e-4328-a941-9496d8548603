package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.engine.entity.MapResourceCache;
import com.youibot.agv.scheduler.engine.pathplan.data.MapMatrixDataUtil;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.thread.AgvMapDelThread;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;

import static com.youibot.agv.scheduler.constant.AGVConstant.TASK_STATUS_FREE;
import static com.youibot.agv.scheduler.constant.MapConstant.MAP_USAGE_STATUS_DISABLE;
import static com.youibot.agv.scheduler.constant.MapConstant.MAP_USAGE_STATUS_ENABLE;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SYSTEM_WORK_MODE_SCHEDULER;

@RestController("AGVMapControllerV3")
@RequestMapping(value = "/api/v3/AGVMaps", produces = "application/json")
@Api(value = "地图", tags = "地图", description = "地图管理功能，用户可以创建和查询所有的地图，并且可能通过地图获取标志，路径，障碍墙，区域等图层信息。")
public class AGVMapController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AGVMapController.class);

    @Autowired
    private AGVMapService agvMapService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private MapAreaService mapAreaService;

    @Autowired
    private PathService pathService;

    @Autowired
    private SystemWorkModeService systemWorkModeService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AGVMap> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvMapService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<AGVMap> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvMapService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "agvMap", value = "地图", required = true, dataType = "AGVMap")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AGVMap save(@RequestBody @Valid AGVMap agvMap) {
        this.agvMapService.insert(agvMap);
        LOGGER.debug(agvMap.toString());
        return agvMap;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVMap get(@PathVariable("id") String id) {
        return MapResourceCache.getAGVMap( id);
    }


    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvMap", value = "地图", required = true, dataType = "AGVMap")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVMap update(@PathVariable("id") String id, @RequestBody @Valid AGVMap agvMap) {
        this.agvMapService.checkUpdateAllow(id);
        agvMap.setId(id);
        this.agvMapService.update(agvMap);
        return this.agvMapService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.agvMapService.checkUpdateAllow(id);
        this.agvMapService.deleteById(id);
        Executors.newSingleThreadExecutor().execute(new AgvMapDelThread(id));
    }

    @ApiOperation(value = "查询标记列表", notes = "根据地图ID获取标记列表")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/markers")
    @ResponseStatus(value = HttpStatus.OK)
    public Set<Marker> getMarkers(@PathVariable("id") String id) {
        return MapResourceCache.getMarkersByAGVMapId( id);
    }

    @ApiOperation(value = "查询区域列表", notes = "根据地图ID获取地图区域列表")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/eventAreas")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MapArea> getEventAreas(@PathVariable("id") String id) {
        return mapAreaService.selectByAGVMapId(id);
    }

    @ApiOperation(value = "查询路径列表", notes = "根据地图ID获取路径列表")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/paths")
    @ResponseStatus(value = HttpStatus.OK)
    public Set<Path> getPaths(@PathVariable("id") String id) {
        return MapResourceCache.getPathsByAGVMapId(id);
    }

    @ApiOperation(value = "启用地图", notes = "启用地图")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @PutMapping(value = "/{id}/enable")
    @ResponseStatus(value = HttpStatus.OK)
    public void enable(@PathVariable("id") String id) {
        AGVMap agvMap = agvMapService.selectById(id);
        if (agvMap == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
        }
        Vehicle vehicle = VehicleUtils.getVehicle();
        if (id.equals(vehicle.getAGVMapId())) {
            vehicle.setAgvMap(agvMap);
        }
        try {
            MapMatrixDataUtil.addAGVMap(id);
        } catch (Exception e) {
            LOGGER.error("启用地图出错, id = " + id, e);
            throw new ExecuteException(MessageUtils.getMessage("http.error_loading_map_data") + " " + e.getMessage());
        }
        agvMap.setUsageStatus(MAP_USAGE_STATUS_ENABLE);//使用状态改为启用
        agvMap.setVersionIncrease(false);
        agvMapService.update(agvMap);
    }

    @ApiOperation(value = "禁用地图", notes = "禁用地图")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @PutMapping(value = "/{id}/disable")
    @ResponseStatus(value = HttpStatus.OK)
    public void disable(@PathVariable("id") String id) {
        AGVMap agvMap = agvMapService.selectById(id);
        if (agvMap == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
        }
        Vehicle vehicle = VehicleUtils.getVehicle();
        //检测地图中是否存在绑定任务、任务链或者异常的AGV
        if (id.equals(vehicle.getAGVMapId()) && (!TASK_STATUS_FREE.equals(vehicle.getWorkStatus()) || !StringUtils.isEmpty(vehicle.getMissionWorkChainId()))) {
            throw new ExecuteException(MessageUtils.getMessage("http.agv_execute_task_now"));
        }
        SystemWorkMode systemWorkMode = systemWorkModeService.getOne(null);
        if (systemWorkMode != null && SYSTEM_WORK_MODE_SCHEDULER.equals(systemWorkMode.getMode())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.scheduler_mode_can_not_disable_map"));
        }
        agvMap.setUsageStatus(MAP_USAGE_STATUS_DISABLE);//使用状态改为禁用
        agvMap.setVersionIncrease(false);
        agvMapService.update(agvMap);
        try {
            MapMatrixDataUtil.removeAGVMap(id);
        } catch (Exception e) {
            LOGGER.error("禁用地图出错, id = " + id, e);
            throw new ExecuteException(MessageUtils.getMessage("http.error_cleaning_map_data") + " " + e.getMessage());
        }
    }

}
