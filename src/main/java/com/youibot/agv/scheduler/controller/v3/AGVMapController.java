package com.youibot.agv.scheduler.controller.v3;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.entity.MapArea;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.AGVMapMd5;
import com.youibot.agv.scheduler.map.entity.AGVMapResult;
import com.youibot.agv.scheduler.map.entity.MapGraphInfo;
import com.youibot.agv.scheduler.map.entity.PageInfo;
import com.youibot.agv.scheduler.map.entity.PathMd5;
import com.youibot.agv.scheduler.map.entity.PathResultData;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.map.utils.ZipUtils;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.AGVMapUpdateQueueService;
import com.youibot.agv.scheduler.service.MapAreaService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.service.SidePathService;
import com.youibot.agv.scheduler.service.WorkCycleConfigService;
import com.youibot.agv.scheduler.util.Base64Utils;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.MD5Utils;
import com.youibot.agv.scheduler.util.MessageUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;


@RestController("agvMapControllerV3")
@RequestMapping(value = "/api/v3/AGVMaps", produces = "application/json")
@Api(value = "地图", tags = "地图", description = "地图管理功能，用户可以创建和查询所有的地图，并且可能通过地图获取标志，路径，障碍墙，区域等图层信息。")
public class AGVMapController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(AGVMapController.class);

    @Autowired
    private AGVMapService agvMapService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private MapAreaService mapAreaService;

    @Autowired
    private SidePathService sidePathService;

    @Autowired
    public AGVMapUpdateQueueService agvMapUpdateQueueService;

    @Autowired
    private PathService pathService;

    @Autowired
    private WorkCycleConfigService workCycleConfigService;
    
    
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AGVMap> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvMapService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<AGVMapResult> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvMapService.searchAll(searchMap);
    }

    @ApiOperation(value = "查询地图文件最后一次修改的时间")
    @GetMapping("/{id}/getLastUpdateTime")
    @ResponseStatus(value = HttpStatus.OK)
    public Long getLastUpdateTime(@PathVariable("id") String id) {
        MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(id);
        if (mapGraphInfo != null) {
            return mapGraphInfo.getLastUpdateTime();
        }
        return null;
    }


    @LogOperation
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "agvMap", value = "地图", required = true, dataType = "AGVMap")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AGVMap save(@RequestBody @Valid AGVMap agvMap) {
        return agvMapService.insert(agvMap);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVMap get(@PathVariable("id") String id) {
        return agvMapService.selectById(id);
    }


    @LogOperation
    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvMap", value = "地图", required = true, dataType = "AGVMap")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVMap update(@PathVariable("id") String id, @RequestBody @Valid AGVMap agvMap) {
        agvMap.setId(id);
        
             try {
				TjdCxt.clearCacheData();
				workCycleConfigService.refresh();
			} catch (Exception e) {
				// TODO: handle exception
			}
		return this.agvMapService.update(agvMap);
    }

    @LogOperation
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        agvMapService.delete(id);
        
        workCycleConfigService.refresh();
    }

    /**
     * 获取该地图下的标志点列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询标记列表", notes = "根据地图ID获取标记列表")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/markers")
    @ResponseStatus(value = HttpStatus.OK)
    public List<Marker> getMarkers(@PathVariable("id") String id, boolean isDraft) {
        return markerService.selectByAGVMapId(id, isDraft);
    }

    /**
     * 获取该地图下的区域列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询区域列表", notes = "根据地图ID获取地图区域列表")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/eventAreas")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MapArea> getEventAreas(@PathVariable("id") String id, Boolean isDraft) {
        return mapAreaService.selectByAGVMapId(id, isDraft);
    }

    /**
     * 获取该地图下的边列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询边路径列表", notes = "根据地图ID获取边路径列表")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/sidePaths")
    @ResponseStatus(value = HttpStatus.OK)
    public List<SidePath> getSidePaths(@PathVariable("id") String id, boolean isDraft) {
        return sidePathService.selectByAGVMapId(id, isDraft);
    }


    @ApiOperation(value = "查询当前地图是否有草稿数据", notes = "查询当前地图是否有草稿数据")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/selectDraftFile")
    @ResponseStatus(value = HttpStatus.OK)
    public boolean selectDraftFile(@PathVariable("id") String id) {
        return agvMapService.selectDraftFile(id);
    }

    @LogOperation
    @ApiOperation(value = "删除草稿数据", notes = "删除当前草稿数据")
    @ApiImplicitParam(name = "id", value = "地图ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}/deleteDraftFile")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteDraftFile(@PathVariable("id") String id) {
        agvMapService.deleteDraftFile(id);
    }

    @LogOperation
    @ApiOperation(value = "修改路网")
    @PutMapping(value = "/{agvMapName}/updatePaths")
    @ApiImplicitParam(name = "agvMapName", value = "地图名称", dataType = "String", paramType = "path")
    @ResponseStatus(value = HttpStatus.OK)
    public void batchUpdate(@PathVariable("agvMapName") String agvMapName, @RequestBody PathResultData pathResult) {
        if (pathResult == null) {
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        agvMapService.batchUpdatePathInfo(pathResult, agvMapName);
    }

    @LogOperation
    @ApiOperation(value = "删除路网")
    @PutMapping(value = "/{agvMapName}/delPaths")
    @ApiImplicitParam(name = "agvMapName", value = "地图名称", dataType = "String", paramType = "path")
    @ResponseStatus(value = HttpStatus.OK)
    public void batchDelete(@PathVariable("agvMapName") String agvMapName, @RequestBody PathResultData pathResult) {
        if (pathResult == null) {
            throw new ExecuteException(MessageUtils.getMessage("missing_parameter"));
        }
        agvMapService.batchDelPathInfo(pathResult, agvMapName);
    }

    @LogOperation
    @ApiOperation(value = "地图发布", notes = "将草稿数据转为正式数据")
    @ApiImplicitParam(name = "mapName", value = "地图名称", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/pushMapData/{mapName}")
    @ResponseStatus(value = HttpStatus.OK)
    public void pushMapData(@PathVariable("mapName") String mapName) {
        agvMapService.pushMapData(mapName);
        workCycleConfigService.refresh();
    }


    @LogOperation
    @ApiOperation(value = "地图刷新", notes = "将正式数据加载到内存")
    @ApiImplicitParam(name = "mapName", value = "地图名称", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/refresh/{mapName}")
    @ResponseStatus(value = HttpStatus.OK)
    public void refresh(@PathVariable("mapName") String mapName) {
        agvMapService.refresh(mapName);
    }


    @ApiOperation(value = "导出地图数据", notes = "导出地图数据zip文件")
    @ApiImplicitParam(name = "mapName", value = "地图名称", required = true, dataType = "String")
    @GetMapping("/exportMapData")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void exportMapData(HttpServletResponse response, @RequestParam(value = "mapName") String mapId) {
        agvMapService.exportMapData(response, mapId);
    }


    @ApiOperation(value = "导入地图数据", notes = "导入地图数据zip文件")
    @ApiImplicitParam(name = "mutiPartFile", value = "文件", required = true, dataType = "file")
    @PostMapping("/importMapData")
    @ResponseStatus(HttpStatus.OK)
    public String importMapData(@RequestBody MultipartFile mutiPartFile) throws Exception {
        return agvMapService.importMapData(mutiPartFile);
    }



    @ApiOperation(value = "更新背景图数据", notes = "更新背景图数据")
    @ApiImplicitParams({@ApiImplicitParam(name = "mapName", value = "地图名称", paramType = "path", required = true, dataType = "String"),
        @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "file")})
    @PostMapping("/importBackgroundImgData/{mapName}")
    @ResponseStatus(HttpStatus.OK)
    public String importBackgroundImgData(@PathVariable(value = "mapName") String mapName,@RequestBody MultipartFile file) throws Exception {
        return agvMapService.importBackgroundImgData(file,mapName);
    }


    @ApiOperation(value = "删除背景图数据", notes = "删除背景图数据")
    @ApiImplicitParams({@ApiImplicitParam(name = "mapName", value = "地图名称", paramType = "path", required = true, dataType = "String")})
    @PostMapping("/delBackgroundImgData/{mapName}")
    @ResponseStatus(HttpStatus.OK)
    public String delBackgroundImgData(@PathVariable(value = "mapName") String mapName) throws Exception {
        return agvMapService.delBackgroundImgData(mapName);
    }


    @ApiOperation(value = "地图数据转换", notes = "地图数据转换")
    @ApiImplicitParam(name = "multipartFile", value = "文件", required = true, dataType = "file")
    @PostMapping("/mapChange")
    @ResponseStatus(HttpStatus.OK)
    public void mapChange(@RequestBody MultipartFile multipartFile, HttpServletResponse response) {
        //将地图文件中的数据解析成字符串
        Reader reader = null;
        String jsonStr = null;
        JSONObject jsonObject = null;
        JSONArray sidePathArray = null;
        JSONArray path = null;
        JSONArray agvMap = null;
        String mapName = null;
        try {
            reader = new InputStreamReader(multipartFile.getInputStream(), "utf-8");
            int ch;
            StringBuilder sb = new StringBuilder();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            jsonStr = sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isEmpty(jsonStr)) {
            throw new ExecuteException("文件内容为空");
        }
        try {
            jsonObject = JSON.parseObject(jsonStr);
            jsonObject.remove("AutoDoor");
            sidePathArray = jsonObject.getJSONArray("SidePath");
            path = jsonObject.getJSONArray("Path");
            //获取地图名称
            agvMap = jsonObject.getJSONArray("AGVMap");
            mapName = agvMap.getJSONObject(0).getString("name");
            //获取MapArea
            List<JSONObject> mapArea = jsonObject.getJSONArray("MapArea").toJavaList(JSONObject.class);
            List<JSONObject> mapAreaResult = mapArea.stream().filter(item -> {
                String polygon = item.getString("polygon");
                int size = JSON.parseArray(polygon).size();
                if (size >= 3) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            jsonObject.put("MapArea", mapAreaResult);
        } catch (Exception e) {
            throw new ExecuteException("json数据转化失败");
        }
        //转换forwardAgvDirection和reverseAgvDirection的值
        for (int i = 0; i < path.size(); i++) {
            JSONObject jsonObject1 = path.getJSONObject(i);
            Integer forwardAgvDirection = jsonObject1.getInteger("forwardAgvDirection");
            Integer reverseAgvDirection = jsonObject1.getInteger("reverseAgvDirection");
            if (forwardAgvDirection == 2) {
                forwardAgvDirection = 180;
            } else if (forwardAgvDirection == 1) {
                forwardAgvDirection = 0;
            }
            if (reverseAgvDirection == 2) {
                reverseAgvDirection = 180;
            } else if (reverseAgvDirection == 1) {
                reverseAgvDirection = 0;
            }
            jsonObject1.put("forwardAgvDirection", forwardAgvDirection);
            jsonObject1.put("reverseAgvDirection", reverseAgvDirection);
            path.set(i, jsonObject1);
        }
        //转换agvDirection的值
        for (int i = 0; i < sidePathArray.size(); i++) {
            JSONObject sidePathArrayJSONObject = sidePathArray.getJSONObject(i);
            Integer agvDirection = sidePathArrayJSONObject.getInteger("agvDirection");
            if (agvDirection == 2) {
                agvDirection = 180;
            } else if (agvDirection == 1) {
                agvDirection = 0;
            } else {
                throw new ExecuteException("地图转换4.7.0版本失败，该地图不是4.7.0以下的地图");
            }
            sidePathArrayJSONObject.put("agvDirection", agvDirection);
            sidePathArray.set(i, sidePathArrayJSONObject);
        }
        jsonObject.put("Path", path);
        jsonObject.put("SidePath", sidePathArray);
        String str = jsonObject.toString();

        //导出地图
        OutputStream os = null;
        try {
            byte[] bytes = str.getBytes();
            // 将格式化后的字符串写入文件
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(mapName.getBytes(), StandardCharsets.ISO_8859_1) + ".json");
            response.addHeader("Content-Length", "" + bytes.length);
            os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            os.write(bytes);// 输出文件
            os.flush();
            os.close();
        } catch (Exception e) {
            logger.error("exportMapData exception : " + e.getMessage());
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    logger.error(e.getMessage());
                }
            }
        }
    }


    @ApiOperation(value = "返回地图数据", notes = "返回地图数据")
    @ApiImplicitParam(name = "multipartFile", value = "文件", required = true, dataType = "file")
    @PostMapping(value = "/mapChangeV2", produces = "application/json;charset=utf-8")
    @ResponseStatus(HttpStatus.OK)
    public String mapChangeV2(@RequestBody MultipartFile multipartFile) {
        //将地图文件中的数据解析成字符串
        Reader reader = null;
        String jsonStr = null;
        try {
            reader = new InputStreamReader(multipartFile.getInputStream(), "utf-8");
            int ch;
            StringBuilder sb = new StringBuilder();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            jsonStr = sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isEmpty(jsonStr)) {
            throw new ExecuteException("文件内容为空");
        }
        return jsonStr;
    }


    @ApiOperation(value = "返回地图文件的压缩包", notes = "返回地图文件的压缩包")
    @ApiImplicitParam(name = "multipartFile", value = "文件内容的JSON数据", required = true, dataType = "JSONObject")
    @PostMapping("/mapSave")
    @ResponseStatus(HttpStatus.OK)
    public void mapSave(@RequestBody JSONObject multipartFile, HttpServletResponse response) {
        if (StringUtils.isEmpty(multipartFile)) {
            throw new ExecuteException("文件内容为空");
        }
        JSONArray agvMap = null;
        String mapName = null;
        List<Path> path = null;
        try {
            //获取地图名称
            agvMap = multipartFile.getJSONArray("AGVMap");
            mapName = agvMap.getJSONObject(0).getString("name");

            path = multipartFile.getJSONArray("Path").toJavaList(Path.class);
            List<Marker> markerList = multipartFile.getJSONArray("Marker").toJavaList(Marker.class);
            Map<String, Marker> markerMap = markerList.stream().collect(Collectors.toMap(Marker::getId, Marker -> Marker));
            ArrayList<SidePath> sidePaths = new ArrayList<>();
            for (Path path1 : path) {
                List<SidePath> sidePaths1 = pathService.createSidePathsV2(path1, markerMap.get(path1.getStartMarkerId()), markerMap.get(path1.getEndMarkerId()));
                sidePaths.addAll(sidePaths1);
            }
            multipartFile.put("SidePath", sidePaths.toArray());
        } catch (Exception e) {
            throw new ExecuteException("json数据转化失败");
        }

        String str = multipartFile.toString();

        //保存地图和md5文件
        String pathParentPath = mapName + "/path/current/";
        String locatingParentPath = mapName + "/locating/current/";
        File pathFile = new File(pathParentPath + mapName + ".path");
        File pathMd5File = new File(pathParentPath + mapName + ".md5");
        File locatingFile = new File(locatingParentPath + mapName + ".info");
        File locatingMd5File = new File(locatingParentPath + mapName + ".md5");
        BufferedOutputStream fb = null;
        BufferedOutputStream md5Fb = null;
        OutputStream os = null;
        try {
            //生成path目录下的文件
            PathResultData pathResultData = MapFileUtils.pathFileDataToPathResultDataV2(str, mapName);
            MapFileUtils.writeValue(MapFileUtils.pathResultDataToString(pathResultData), pathFile);
            String md5String = MD5Utils.fileEncryption(pathParentPath + mapName + ".path");
            PathMd5 pathMd5 = new PathMd5();
            pathMd5.setPathMd5(md5String);
            MapFileUtils.writeValue(JSON.toJSONString(pathMd5), pathMd5File);

            //生成locating目录下的文件
            AGVMap map = new AGVMap();
            String mapData = multipartFile.getJSONArray("AGVMap").getJSONObject(0).getString("mapData");
            Base64Utils.base64ToImage(mapData, locatingParentPath, mapName + ".png");
            AGVMap agvMap1 = multipartFile.getJSONArray("AGVMap").toJavaList(AGVMap.class).get(0);
            BeanUtils.copyPropertiesIgnoreNull(agvMap1, map);
            map.setId(map.getName());
            map.setImage(map.getName()+".png");
            map.setNegate(0.0);
            map.setFree_thresh(0.196);
            map.setOccupied_thresh(0.65);
            if (map.getOriginYaw() == null) {
                map.setOriginYaw(0.0);
            }
            MapFileUtils.writeValue(JSON.toJSONString(map), locatingFile);
            String infoMd5 = MD5Utils.fileEncryption(locatingParentPath + mapName + ".info");
            String pngMd5 = MD5Utils.fileEncryption(locatingParentPath + mapName + ".png");
            AGVMapMd5 agvMapMd5 = new AGVMapMd5();
            agvMapMd5.setInfoMd5(infoMd5);
            agvMapMd5.setPngMd5(pngMd5);
            MapFileUtils.writeValue(JSON.toJSONString(agvMapMd5), locatingMd5File);

            //4、返回前端
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            ZipUtils.toZip(mapName, output, true);
            byte[] data = output.toByteArray();

            response.addHeader("Content-Disposition", "attachment;filename=" + new String(mapName.getBytes(), StandardCharsets.ISO_8859_1) + ".zip");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/zip");

            os = response.getOutputStream();
            os.write(data);// 输出文件
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (fb != null) {
                    fb.close();
                }
                if (md5Fb != null) {
                    md5Fb.close();
                }
                deleteDir(new File(mapName));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }


    /**
     * 递归删除目录下的所有文件及子目录下所有文件
     */
    private static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        // 目录此时为空，可以删除
        return dir.delete();
    }


}
