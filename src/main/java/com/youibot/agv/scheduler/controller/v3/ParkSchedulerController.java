package com.youibot.agv.scheduler.controller.v3;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.entity.ParkScheduler;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.param.SchedulerStopParam;
import com.youibot.agv.scheduler.param.SchedulerStopResult;
import com.youibot.agv.scheduler.service.ParkSchedulerService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@RestController("parkSchedulerControllerV3")
@RequestMapping(value = "/api/v3/parkSchedulers", produces = "application/json")
@Api(value = "泊车调度", tags = "泊车调度", description = "泊车调度管理接口")
public class ParkSchedulerController extends BaseController {

    @Autowired
    private ParkSchedulerService parkSchedulerService;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    private static final Logger logger = LoggerFactory.getLogger(ParkSchedulerController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<ParkScheduler> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return parkSchedulerService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<ParkScheduler> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return parkSchedulerService.searchAll(searchMap);
    }

    @LogOperation
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "删除泊车调度记录", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        ParkScheduler parkScheduler = parkSchedulerService.selectById(id);
        if (ParkScheduler.STATUS_CANCEL.equals(parkScheduler.getStatus()) || ParkScheduler.STATUS_SUCCESS.equals(parkScheduler.getStatus())) {
            this.parkSchedulerService.deleteById(id);
        } else {
            throw new ExecuteException(MessageUtils.getMessage("service.the_status_is_do_not_delete"));
        }
    }

    @LogOperation
    @ApiOperation(value = "取消")
    @ApiImplicitParam(name = "id", value = "取消泊车调度", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/cancel")
    @ResponseStatus(value = HttpStatus.OK)
    public SchedulerStopResult cancel(@PathVariable("id") String id, @RequestBody SchedulerStopParam param) {
        ParkScheduler parkScheduler = parkSchedulerService.selectById(id);
        if (parkScheduler == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.the_scheduler_is_empty"));
        }
        if (ParkScheduler.STATUS_CANCEL.equals(parkScheduler.getStatus()) || ParkScheduler.STATUS_SUCCESS.equals(parkScheduler.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("service.the_status_do_not_cancel"));
        }

        Vehicle vehicle = vehiclePoolService.selectById(parkScheduler.getVehicleId());
        if (!param.isForcedStop() && (vehicle == null || !VehicleConstant.ONLINE.equals(vehicle.getOnlineStatus()))) {
            return new SchedulerStopResult("ERROR", 1001);//非强制取消且机器人不在线
        }

    
        if (vehicle == null || VehicleConstant.OFFLINE.equals(vehicle.getOnlineStatus())) {
            parkSchedulerService.updateCancel(parkScheduler.getId());
            logger.debug("agvCode:[{}],event:[泊车调度],小车不存在或断线，取消泊车：[{}]", vehicle.getId(),JSONObject.toJSONString(parkScheduler));
        } else if (VehicleConstant.OUTLINE.equals(vehicle.getOnlineStatus())){
            parkSchedulerService.updateCancel(parkScheduler.getId());
            //如果机器人为离线状态, 不发送取消指令到mqtt, 直接清理相关资源, 目前该做法主要针对机器人关机掉线后, 无法快速清理资源的问题
            if (!CollectionUtils.isEmpty(vehicle.getPathPlanMessages())) {
                vehicle.getPathPlanMessages().clear();
            }
            checkAndSendPathService.clear(vehicle.getId(), "SMART_WAIT");
            logger.debug("agvCode:[{}],event:[泊车调度],小车离线，取消泊车，并且清理资源：[{}]", vehicle.getId(),JSONObject.toJSONString(parkScheduler));
        } else {
            vehicle.cancelPark();
        }
        logger.debug("agvCode:[{}],event:[泊车调度],当前取消的泊车调度数据：[{}]", vehicle.getId(),JSONObject.toJSONString(parkScheduler));
      
        return new SchedulerStopResult("SUCCESS", null);
    }
}
