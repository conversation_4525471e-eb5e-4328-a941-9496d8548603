package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.OperationRecord;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.service.OperationRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 操作记录
 * @Author：yangpeilin
 * @Date: 2020/4/29 10:40
 */
@RestController("OperationRecordController")
@Api(value = "保养和维护操作记录", tags = "机器人保养和维护操作记录")
@RequestMapping(value = "/api/v3/operationRecord", produces = "application/json")
public class OperationRecordController {

    @Autowired
    private OperationRecordService operationRecordService;

    @ApiOperation("新增保养和维修记录")
    @ApiImplicitParam(name = "operationRecord", value = "操作记录", required = true, dataType = "OperationRecord")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public void saveRecord(@RequestBody OperationRecord operationRecord) {
        if (validate(operationRecord)){
            throw new ADSParameterException("参数有误");
        }
        operationRecordService.insert(operationRecord);
    }

    @ApiOperation("删除保养和维修记录")
    @ApiImplicitParam(name = "id", value = "删除保养和维修记录", paramType = "path", required = true, dataType = "id")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteRecord(@PathVariable String id) {
        operationRecordService.deleteById(id);
    }

    @ApiOperation("修改保养和维修记录")
    @ApiImplicitParam(name = "operationRecord", value = "修改保养和维修记录", paramType = "path", required = true, dataType = "OperationRecord")
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public void updateRecord(@PathVariable String id, @RequestBody @Valid OperationRecord operationRecord) {
        operationRecord.setId(id);
        operationRecordService.updateByPrimaryKeySelective(operationRecord);
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<OperationRecord> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return operationRecordService.findPage(searchMap);
    }

    @ApiOperation("查看单个保养和维修记录")
    @ApiImplicitParam(name = "id", value = "查看单个保养和维修记录", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public OperationRecord updateRecord(@PathVariable String id) {
        OperationRecord operationRecord = new OperationRecord();
        operationRecord.setId(id);
        List<OperationRecord> byEntity = operationRecordService.findByEntity(operationRecord);
        if (CollectionUtils.isEmpty(byEntity)){
            return null;
        }
        return byEntity.get(0);
    }


    private Boolean validate(OperationRecord operationRecord) {
        return StringUtils.isEmpty(operationRecord.getAgvCode()) || StringUtils.isEmpty(operationRecord.getAgvName()) ||
                operationRecord.getOperationType() == null || (operationRecord.getOperationType() != 0 && operationRecord.getOperationType() != 1) ||
                StringUtils.isEmpty(operationRecord.getOperationName()) || operationRecord.getOperationTime() == null;
    }
}
