package com.youibot.agv.scheduler.controller.v3;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_ACTION_TYPE_RUNTIME;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.engine.scheduler.TjdScheduleHelper;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionActionParameter;
import com.youibot.agv.scheduler.entity.MissionGlobalVariable;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.service.MissionActionParameterService;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionGlobalVariableService;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.util.MessageUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;
import tk.mybatis.mapper.entity.Example;

@RestController("missionControllerV3")
@RequestMapping(value = "/api/v3/missions", produces = "application/json")
@Api(value = "任务", tags = "任务", description = "任务管理，用户可以通过接口查询任务，创建任务及编辑任务。")
public class MissionController extends BaseController {

    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionActionService missionActionService;

    @Autowired
    private MissionActionParameterService missionActionParameterService;

    @Autowired
    private MissionGlobalVariableService missionGlobalVariableService;
    
    @Autowired
    private TjdScheduleHelper tjdScheduleHelper;

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Mission> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        searchMap.put("isDeleted", "0");
        return missionService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Mission> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        searchMap.put("isDeleted", "0");
        return missionService.searchAll(searchMap);
    }

    @LogOperation
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "mission", value = "任务", required = true, dataType = "Mission")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Mission save(@RequestBody @Valid Mission mission) {
        if (StringUtils.isEmpty(mission.getCode())) {
            throw new ADSParameterException(MessageUtils.getMessage("service.mission_code_is_null"));
        }
        Example example = new Example(Mission.class);
        example.createCriteria().andEqualTo("code", mission.getCode());
        if (missionService.selectByCode(mission.getCode()) != null) {
            throw new ADSParameterException(MessageUtils.getMessage("service.mission_code_is_exist"));
        }

        this.missionService.insert(mission);
        LOGGER.debug(mission.toString());
        return mission;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Mission get(@PathVariable("id") String id) {
        return missionService.selectById(id);
    }

    @LogOperation
    @ApiOperation(value = "更新", notes = "根据ID更新任务信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mission", value = "任务信息", required = true, dataType = "Mission")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Mission update(@PathVariable("id") String id, @RequestBody @Valid Mission mission) {
        try {
			mission.setId(id);
			if (StringUtils.isEmpty(mission.getCode())) {
				throw new ADSParameterException(MessageUtils.getMessage("service.mission_code_is_null"));
			}
			Example example = new Example(Mission.class);
			example.createCriteria().andEqualTo("code", mission.getCode()).andNotEqualTo("id", id);
			if (this.missionService.selectByExample(example).size() > 0) {
				throw new ADSParameterException(MessageUtils.getMessage("service.mission_code_is_exist"));
			}
			this.missionService.update(mission);
			return this.missionService.selectById(id);
		} finally {
			tjdScheduleHelper.init();
		}
    }

    @ApiOperation(value = "删除", notes = "根据ID删除任务信息")
    @ApiImplicitParam(name = "id", value = "任务ID, 需要提示是否刪除任务链中的任务", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        missionService.invalid(id);
    }

    @ApiOperation(value = "根据任务ID查询任务动作列表")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionActions")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionAction> getMissionActions(@PathVariable("id") String id) {
        MissionAction action = new MissionAction();
        action.setMissionId(id);
        return missionActionService.findByEntity(action);
    }

    /**
     * 查询该Mission的所有动态参数key
     *
     * @return
     * @throws
     * @params id
     */
    @ApiIgnore
    @ApiOperation(value = "获取运行时参数选项(key)列表", notes = "根据ID获取该mission下运行时参数选项(key)列表")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/runtimeParamKeys")
    @ResponseStatus(value = HttpStatus.OK)
    public List<String> runtimeParamKey(@PathVariable("id") String id) {
        List<MissionAction> missionActions = missionActionService.selectByMissionIdAndActionType(id, MISSION_WORK_ACTION_TYPE_RUNTIME);
        if (missionActions == null || missionActions.isEmpty()) {
            return null;
        }
        List<String> keys = new ArrayList<>();
        for (MissionAction missionAction : missionActions) {
            List<MissionActionParameter> missionActionParameters = missionActionParameterService.selectByMissionActionId(missionAction.getId());
            if (missionActionParameters == null) {
                continue;
            }
            for (MissionActionParameter parameter : missionActionParameters) {
                keys.add(parameter.getParameterKey());
            }
        }
        return keys;
    }

    @ApiOperation(value = "根据任务ID查询任务全局变量")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionGlobalVariables")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionGlobalVariable> missionGlobalVariables(@PathVariable("id") String id) {
        return missionGlobalVariableService.selectByMissionId(id);
    }

}
