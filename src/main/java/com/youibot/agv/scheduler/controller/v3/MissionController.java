package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.constant.MissionConstant;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionCall;
import com.youibot.agv.scheduler.entity.MissionGlobalVariable;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionCallService;
import com.youibot.agv.scheduler.service.MissionGlobalVariableService;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("missionControllerV3")
@RequestMapping(value = "/api/v3/missions", produces = "application/json")
@Api(value = "预设任务", tags = "预设任务", description = "预设任务管理，用户可以通过接口查询预设任务，创建任预设务及编辑预设任务。")
public class MissionController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionController.class);

    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionActionService missionActionService;

    @Autowired
    private MissionGlobalVariableService missionGlobalVariableService;

    @Autowired
    private MissionCallService missionCallService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Mission> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Mission> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "mission", value = "预设任务", required = true, dataType = "Mission")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Mission save(@RequestBody @Valid Mission mission) {
        this.missionService.insert(mission);
        LOGGER.debug(mission.toString());
        return mission;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "预设任务ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Mission get(@PathVariable("id") String id) {
        return missionService.selectById(id);
    }

    @ApiOperation(value = "更新", notes = "根据ID更新预设任务信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "预设任务ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mission", value = "预设任务信息", required = true, dataType = "Mission")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Mission update(@PathVariable("id") String id, @RequestBody @Valid Mission mission) {
        mission.setId(id);
        this.missionService.update(mission);
        return this.missionService.selectById(id);
    }

    @ApiOperation(value = "删除", notes = "根据ID删除预设任务信息")
    @ApiImplicitParam(name = "id", value = "预设任务ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        //关联的呼叫任务改为异常
        Mission mission =  missionService.selectById(id);
        if (mission != null && !StringUtils.isEmpty(mission.getCode())) {
            Example example = new Example(MissionCall.class);
            example.createCriteria().andEqualTo("missionCode", mission.getCode());
            List<MissionCall> missionCallList = missionCallService.selectByExample(example);
            missionCallList.forEach(missionCall -> {
                missionCall.setStatus(MissionConstant.MISSION_CALL_ABNORMAL_STATUS);
                missionCall.setMessage("任务编码已删除");
                missionCallService.updateByPrimaryKeySelective(missionCall);
            });
        }
        this.missionService.deleteById(id);
    }

    @ApiOperation(value = "根据预设任务ID查询预设任务动作列表")
    @ApiImplicitParam(name = "id", value = "预设任务ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionActions")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionAction> getMissionActions(@PathVariable("id") String id) {
        return missionActionService.selectByMissionId(id);
    }

    @ApiOperation(value = "根据预设任务ID查询预设任务全局变量")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionGlobalVariables")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionGlobalVariable> missionGlobalVariables(@PathVariable("id") String id) {
        return missionGlobalVariableService.selectByMissionId(id);
    }

}
