package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSON;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.constant.vo.TjdChargerDto;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.path.Node;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.path.NodeCodeList;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.path.PointsPath;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.BlockCheckService;
import com.youibot.agv.scheduler.entity.AGVGroup;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.entity.vo.PauseVo;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage;
import com.youibot.agv.scheduler.mqtt.service.VehicleCommandService;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.param.TjdScreenItemDto;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.youibot.agv.scheduler.mqtt.constants.MqttConstant.RE_LOGIN;

@ApiSort(value = 1)
@Slf4j
@RestController
@RequestMapping(value = "/api/v3/fleet", produces = "application/json")
@Api(value = "Compass", tags = "compass", description = "compass 单机与多机同步通讯接口")
public class CompassController extends BaseController {

//	@Autowired
//	private TjdCommunicationListener tjdCommunicationListener;
	@Autowired
	private DefaultVehiclePool defaultVehiclePool;
	@Autowired
	private VehicleCommandService vehicleCommandService;

	@Autowired
	private WorkCycleConfigService workCycleConfigService;

 
   
   @Autowired
   BlockCheckService blockCheckService ;
    
   @Autowired 
   private MarkerService markerService ;
   
   @Autowired
   private AGVService agvService;
   
   @Autowired
   private CheckAndSendPathService checkAndSendPathService;
   
   
	@Autowired
	protected MissionWorkService missionWorkService;
	
	@Autowired
	private WorkSchedulerService workSchedulerService;
	
	@Autowired
	private ParkSchedulerService parkSchedulerService;
	
	@Autowired
	private ChargeSchedulerService chargeSchedulerService;
	
	@Autowired
    private AGVGroupService agvGroupService;
	
	
	@ApiIgnore
	// 获取AGV列表
	@ApiOperation(value = "compass 上报站点到达事件(由于需要等待fleet 请求mes 返回结果,做同步处理)")
	@PostMapping("/station")
	@ResponseStatus(value = HttpStatus.OK)
	public AgvArrivedErackInfoMessage toMesStation(@RequestBody AgvArrivedErackInfoMessage msg) {

		log.debug("receive_msg:{}", JSON.toJSONString(msg));
		Vehicle vehicle = defaultVehiclePool.getVehicle(msg.getAgvCode());

		if (Objects.nonNull(msg.getReportType()) && Objects.nonNull(vehicle)) {

//			tjdCommunicationListener.toMesStation(msg);

			vehicleCommandService.publishShelfBins(msg.getAgvCode(), msg);
		}

		return msg;
	}

	@ApiOperation(value = "fleet 工作站刷新")
	@GetMapping("/refresh")
	@ResponseStatus(value = HttpStatus.OK)
	public ResponseEntity<Set<String>> refresh() {

		log.debug("刷新fleet工作站");

		workCycleConfigService.refresh();

		return ResponseEntity.ok(Sets.newHashSet("refresh success"));
	}

	@ApiIgnore
	@ApiOperation(value = "fleet 工作站进站提前通知")
	@GetMapping("/markcode")
	@ResponseStatus(value = HttpStatus.OK)
	public ResponseEntity<Set<String>> notice(String markCode) {

		log.debug("fleet_notice_arrive：{}", markCode);

		return ResponseEntity.ok(Sets.newHashSet("refresh success : " + markCode));
	}

	
	@ApiOperation(value = "fleet 通过导航点查询掉头点-- 返回导航点")
	@GetMapping("/getUturn/agvMapId")
	public List<String> getUturn(String agvMapId, String currentMarkCode , String agvGroupName) {


		
		String agvGroupId = null;
		if(StringUtils.isNotBlank(agvGroupName) && !StringUtils.equals(Objects.toString(null), agvGroupName)) {
			AGVGroup agvGroup = agvGroupService.getByAGVGroup( agvGroupName);
			if( Objects.nonNull( agvGroup)){
				agvGroupId = agvGroup.getId() ;
			}
		}
		String markCode = TjdCxt.getUTurnStationMarkCode(agvMapId, currentMarkCode , agvGroupId );
		if (StringUtils.isBlank( markCode)) {
			 Set<String> codes = TjdCxt.getStationCode( agvGroupId );
			 if( codes.contains(currentMarkCode)) {
				 List< List<String>> posablePath = Lists.newArrayList() ;
				 List<String> oPath = getOCycle( agvMapId  ,true ) ;
				 for( String tpath : oPath ) {
					 String[] tmpPath = StringUtils.split( tpath, "->");
					 List<String> collect = Stream.of( tmpPath).map( i -> StringUtils.substringBetween( i , "[", "]")).collect(Collectors.toList());
					//包含所有的工作站 和起始站点
					 boolean containsAllStation = collect.containsAll(codes) && collect.contains( currentMarkCode );
					 if( containsAllStation) {
						 posablePath.add( collect);
					 }
				 }
				 ;
				 
				 List<String> minDistance = getMinDistance(currentMarkCode, posablePath, codes);
				 Pair<Integer, String> pair = getDistanche(currentMarkCode, minDistance, codes);
				 String second = pair.getSecond();
				 if(StringUtils.isNotBlank( second )){
					 return Lists.newArrayList(  pair.getSecond());
				 }
			;
		
			 }
		
			return Lists.newArrayList("当前markCode不正确，请传入绑定工作站的导航点");
		}
		return Lists.newArrayList(markCode );
	
	
	
	}

	private  List<String>  getMinDistance( String currentMarkCode ,  List< List<String>> posablePath,  Set<String> codes) {
		
		 List<String> list = posablePath.parallelStream().min( new Comparator< List<String>>() {

			@Override
			public int compare(List<String> o1, List<String> o2) {
				
				return  Integer.compare( getDistanche( currentMarkCode , o1 , codes).first(), getDistanche( currentMarkCode , o2 , codes).first());
			}
		}).get();
		
		 return list;
	}
	
	private  Pair<Integer, String>  getDistanche( String currentMarkCode , List<String> path, Set<String> codes ) {
		codes.remove(currentMarkCode);
		int indexOf = path.indexOf(currentMarkCode) ;
		if(indexOf == -1) {
			return   new  Pair<Integer, String>(Integer.MAX_VALUE ,null);
		}
		
	    for (int i = indexOf; i <  path.size(); i++) {
			  
			  String station = path.get(i);
				if(codes.contains(station)) {
					
					return new  Pair<Integer, String>(  i +1 , station) ;
				}
		}
	  
		 for (int i = 0; i <  indexOf; i++) {
				String station = path.get(i);
				if(codes.contains(station)) {
					return new  Pair<Integer, String>( i+1 + indexOf , station ) ;
				}
			}
		 
	
		
		  return   new  Pair<Integer, String>(Integer.MAX_VALUE ,null);
	}
	




	
	@ApiOperation(value = "真正触发agv掉头-- 5分钟之内只处理一次！！！！")
	@GetMapping("/uturn/agvCode")
	public List<String> triggerAgvUtrn(String agvCode ) {
		Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
		
		if (Objects.isNull(agvCode) || Objects.isNull(vehicle)) {
			return Lists.newArrayList("agvCode不正确或agv 未上线");
		}
		vehicleCommandService.uTurn(agvCode, true, true, true ,false) ;
		return Lists.newArrayList("trigger_success");
	}
	
	
	@ApiOperation(value = "查询当前agv 被那些车辆阻挡")
	@GetMapping("/block/{agvCode}")
	public List<String> blockAgv(@PathVariable String agvCode ) {
		Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
		
		if ( Objects.isNull(vehicle)) {
			return Lists.newArrayList("agvCode不正确或agv 未上线");
		}
		List<String> checkSharedPath = blockCheckService.checkSharedPathAgvId(agvCode);
		log.debug("agvCode:[{}],event:[查询是否被其他车辆阻挡],content:[{}]", agvCode, StringUtils.join( checkSharedPath, "---"));

		return checkSharedPath;
	}
	
	@ApiOperation(value = "查询车辆是否应该上报阻挡及阻挡计时次数")
	@GetMapping("/block/report/{agvCode}")
	public List<Object> shouldReportAgv(@PathVariable String agvCode ) {
		ArrayList<Object> list = Lists.newArrayList();
		
		Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
		if(Objects.isNull(vehicle)) {
			return list;
		}
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        LinkedBlockingDeque<SidePath> linkedBlockingDeque = agvToPlanedSidePaths.get(agvCode);
	    boolean shoudlReport = TjdCxt.shoudlReport(vehicle);
		list.add(shoudlReport);
		list.add(vehicle.getStopCheckTimes());
		if(CollectionUtils.isNotEmpty(linkedBlockingDeque)) {
			SidePath first = linkedBlockingDeque.getFirst();
			Marker marker = MapGraphUtil.getMarkerByMarkerId( first.getEndMarkerId());
			list.add( marker.getCode());
		}
		return list;
	}
	
	@ApiOperation(value = "查询车辆是否应该上报阻挡及阻挡计时次数 ---(查询车辆拥占有的markCode)")
	@GetMapping("/block/report/own/{agvCode}")
	public Set<String> agvOwnMarker(@PathVariable String agvCode ) {
		Set<String> collect  =  Sets.newHashSet();
		
		Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
		if(Objects.isNull(vehicle)) {
			return collect;
		}
		Set<String> cantApplyMarkerId = blockCheckService.cantApplyMarkerId(agvCode);
		 collect = cantApplyMarkerId.parallelStream().map( i -> MapGraphUtil.getMarkerByMarkerId(i).getCode() ).collect(Collectors.toSet());
		return collect;
	}
	

	@ApiOperation(value = "根据地图 获取当前的O型区域")
	@GetMapping("/getCycle/{agvMapId}/o/{showMarkCode}")
	public List<String> getOCycle(@PathVariable String agvMapId  ,@PathVariable boolean showMarkCode) {
		 return CommonUtils.getAgvMapSortCycle(agvMapId , showMarkCode );
	}


	@ApiOperation(value = "根据地图 获取当前的最大O型区域")
	@GetMapping("/getCycle/{agvMapId}/max/{markCode}")
	public List<String> getOCycleMax(@PathVariable String agvMapId ,@PathVariable boolean markCode) {
		  List<String> mapSortCycle = CommonUtils.getAgvMapSortCycle(agvMapId , markCode );
		  if(CollectionUtils.isEmpty(mapSortCycle)) {
			  return Lists.newArrayList(); 
		  }
		  return Lists.newArrayList( mapSortCycle.get(0)) ;
	}

	@ApiOperation(value = "根据地图 获取当前的最大O型区域 -- 用以计算车间距离")
	@GetMapping("/getCycle/{agvMapId}/maxVehicle/{showMarkCode}")
	public List<String> getOCycleMaxVehicle(@PathVariable String agvMapId ,@PathVariable boolean showMarkCode) {
		  DirectedGraph dGraph = MapGraphUtil.getCloneDirectedGraph();
		  List<String> mapSortCycle = CommonUtils.getTotalMaxCyclesIds(dGraph ,agvMapId , showMarkCode );
		 
		  return Lists.newArrayList(mapSortCycle ) ;
	}
	
	@ApiOperation(value = "根据地图 获得包含所有工作站的环")
	@GetMapping("/getCycle/{agvMapId}/station/{showMarkCode}")
	public List<?> getOCycleMaxStation(@PathVariable String agvMapId ,@PathVariable boolean showMarkCode) {
		  DirectedGraph dGraph = MapGraphUtil.getCloneDirectedGraph();
		  List<List<String>> containsAllStationMarkCodeCycles = CommonUtils.getContainsAllStationMarkCodeCycles(dGraph, agvMapId);
		  if(showMarkCode) {
			  return CommonUtils.getCyclePrint(dGraph, agvMapId, containsAllStationMarkCodeCycles) ;
		  }
		  return containsAllStationMarkCodeCycles ;
	}
	
	@ApiOperation(value = "根据地图 获得包含所有工作站的环且点位最多,")
	@GetMapping("/getCycle/{agvMapId}/station/max/{showMarkCode}")
	public List<?> getOCycleMaxStationMax(@PathVariable String agvMapId ,@PathVariable boolean showMarkCode) {
		  DirectedGraph dGraph = MapGraphUtil.getCloneDirectedGraph();
		  List<List<String>> containsAllStationMarkCodeCycles = CommonUtils.getContainsAllStationMarkCodeCycles(dGraph, agvMapId);
		  if(CollectionUtils.isEmpty(containsAllStationMarkCodeCycles)) {
			  return Lists.newArrayList();
		  }
		  List<String> list = containsAllStationMarkCodeCycles.parallelStream().max(new Comparator<List<String>>() {

				@Override
				public int compare(List<String> o1, List<String> o2) {
					// TODO Auto-generated method stub
					
					return Integer.valueOf(o2.size()).compareTo( Integer.valueOf(o1.size()))  ;
				}
			}).get();
		  List<List<String>> temp = new ArrayList<>();
		  temp.add( list);
		  
		  if( showMarkCode ) {
			  return CommonUtils.getCyclePrint(dGraph, agvMapId,temp) ;
		  }
		  return temp ;
	}
	
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "根据地图 获得包含所有工作站的环且点位最多的路径")
	@GetMapping("/getCycle/{agvMapId}/station/max/path")
	public List<SidePath> getContainsSidePathWorkStation(@PathVariable String agvMapId){
		 DirectedGraph dGraph = MapGraphUtil.getCloneDirectedGraph();
		 List<List<String>> list = ( List<List<String>>)this.getOCycleMaxStationMax(agvMapId, false);
		 List<String> print = CommonUtils.getCyclePrint(dGraph, agvMapId, list) ;
		 log.debug("agvMapId:{} , print:{}" ,agvMapId, JSON.toJSONString(print));
		if(CollectionUtils.isNotEmpty(list)) {
			List<String> list2 = list.get(0);
			return printLoopSidePath(list2);
		}

		return Lists.newArrayList();
	}
	
	
	 public static List<SidePath> printLoopSidePath(List<String> list ) {
		 List<SidePath> result = Lists.newArrayList();
	        int size = list.size();
			for (int i = 0 ; i< size; i ++) {
	        	if( (i+1) < size) {
	        		SidePath sidePath = MapGraphUtil.getSidePathByStartEndMarkerId( list.get(i) , list.get(i+1) );
	        		result.add(sidePath);
	        	}
	          
	        }
			SidePath sidePath = MapGraphUtil.getSidePathByStartEndMarkerId( list.get( size -1 ) , list.get(0 ) );
			result.add(sidePath);
	        return result;
	    }
	@ApiOperation(value = "获得路径和agv之间的对应关系")
	@GetMapping("/getCycle/pathIdAgvs")
	public MultiValueMap<String,String> pathIdAgvs(){
		
		return TjdCxt.pathIdAgvs();
	}

	@ApiOperation(value = "获得工作站,agv计数")
	@GetMapping("/get/stationCount")
	public MultiValuedMap<String, String> stationCount(){

		return TjdCxt.STATION_COUNT;
	}
	
	/**
	   curl  --retry-delay 50 --retry 20 'http://localhost:8080/api/v3/fleet/pauseScheduler'   -H "Content-Type:application/json"  -X POST  -d '{"pwd":"tsmc","secPwd":"youibot"}'
	
	 * @param vo
	 * @return
	 */
	@LogOperation("dd_recover_双击热备的情况下-切换为主机时触发")
	@ApiOperation(value = "暂停所有的调度-1分钟后自动恢复")
	@PostMapping("/pauseScheduler")
	public Boolean pauseScheduler(@RequestBody PauseVo vo ){
		
		
		HttpServletRequest req = CommonUtils.getReq();
		if( Objects.nonNull(req)) {
			String remoteHost = req.getRemoteHost();
			log.debug("remote_host:{}，trigget_master_occured", remoteHost ) ;
		}
		if(PauseVo.isRight(vo) && !TjdCxt.isPause()) {
			boolean pauseAllScheduler = TjdCxt.pauseAllScheduler();
			TjdCxt.HANDLING.set( true );
			CompletableFuture<Void> runAsync = CompletableFuture.runAsync( () ->{
				 
				log.debug("double_machine_recovery_re_login_start" );
				resendRelogin();
				log.debug("double_machine_recovery_delete_runing_mission_work" );
				stopRuning( ) ;
			}) ;
			try {
				runAsync.get();
			} catch (InterruptedException | ExecutionException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}finally {
				TjdCxt.HANDLING.set( false) ;
			}
			return pauseAllScheduler;
		}
		return false ;
	}

	/**
	 * 删除正在运行的missionWors
	 */
	private void stopRuning() {

		try {
			log.debug("double_machine_recovery_delete_runing_mission_work_start" );
			missionWorkService.stopUnCompleteMissionWorks();
			workSchedulerService.updateCancel();
			chargeSchedulerService.updateCancel();
			parkSchedulerService.updateCancel();
			log.debug("double_machine_recovery_delete_runing_mission_work_end" );
		} catch (Exception e) {
			String stackTraceAsString = Throwables.getStackTraceAsString(e);
			log.error("double_machine_recovery_stopRuning_error:{}", stackTraceAsString);

		}finally {
			agvService.updateEnable();
		}
		
	}

	/**
	 * 过十5秒钟再发
	 */
	public void resendRelogin() {
		try {
			CommonUtils.sleepSlient(TimeUnit.SECONDS, 20);
			boolean sendMqttMsg = MqttUtils.sendMqttMsg(RE_LOGIN, true, System.currentTimeMillis());
			agvService.updateEnable();
			log.debug("double_machine_recovery_re_login:{}", sendMqttMsg);
		} catch (Exception e) {
			String stackTraceAsString = Throwables.getStackTraceAsString(e);
			log.debug("double_machine_recovery_re_login_error:{}", stackTraceAsString );

		}
	}
	
	@ApiOperation(value = "判断当前调度系统是否处于暂停调度中")
	@GetMapping("/pausing")
	public Boolean isPause(){
		
		return TjdCxt.isPause();
	}
	
	@ApiOperation(value = "重新设置暂停恢复时间")
	@GetMapping("/modPauseTime")
	public int modPauseTime( int pauseTime ){
		
		 TjdCxt.resetStopScheduler( pauseTime);
		 return pauseTime ;
	}
	
	@ApiOperation(value = "根据起始点和终点求所有连通的路径")
	@GetMapping("/getAllPath/{agvMapId}/{startCode}/{endCode}")
	public Pair<Set<NodeCodeList<String>>, Set<NodeCodeList<String>>> getAllPathBetwnnPoints(@PathVariable String agvMapId , @PathVariable String startCode , @PathVariable String endCode ){
		 List<SidePath> list = MapGraphUtil.getSidePathsByAGVMapId( agvMapId );
		
		
		PointsPath path = new PointsPath();
		path.findAllPath( getNodeList( list ) , startCode, endCode);
		Set<NodeCodeList<String>> cycle = path.getCycle();
		Set<NodeCodeList<String>> path2 = path.getPath();
		Pair<Set<NodeCodeList<String>>, Set<NodeCodeList<String>>> res = new Pair<Set<NodeCodeList<String>>, Set<NodeCodeList<String>>>( cycle , path2);
		
		return res ;
	}

	private List<Node> getNodeList( List<SidePath> list ) {
		DirectedGraph graph = MapGraphUtil.getCloneDirectedGraph();
		List<Node> res = list.parallelStream().filter(p -> Objects.nonNull( graph.getEdgeBySidePath(p))).map( item ->{
			Node node = new  Node( graph.getEdgeBySidePath( item));
			return node; 
		 }).collect(Collectors.toList());
		return res;
	}
	
	@ApiOperation(value = "根据起始点和 一堆终点的 计算到那个终点的距离最短连通路径")
	@PostMapping("/getAllPath/{agvMapId}/{startCode}")
	public  Map<String, NodeCodeList<String>> getNearWorkCodeTable(@PathVariable String agvMapId , @PathVariable String startCode , @RequestBody Set<String> markCodes  ){
		 List<SidePath> list = MapGraphUtil.getSidePathsByAGVMapId( agvMapId );
		  Map<String, NodeCodeList<String>> res = Maps.newHashMap();
		 markCodes.forEach( item ->{
			    PointsPath path = new PointsPath();
				path.findAllPath( getNodeList( list ) , startCode, item);
			
				Set<NodeCodeList<String>> path2 = path.getPath();
				NodeCodeList<String> codeList = path2.parallelStream().min((o1, o2) -> {
					
					
					return Integer.compare( CollectionUtils.size(o1),  CollectionUtils.size(o2)) ;
				}).get();
				res.put(item, codeList);
		 });
		

		return res ;
	}
	
	@ApiOperation(value = "根据起始点和 一堆终点的 计算到那个终点的距离最短连通路径")
	@PostMapping("/getAllPath/{agvMapId}/{startCode}/perfect")
	public  NodeCodeList<String> getNearWorkCode(@PathVariable String agvMapId , @PathVariable String startCode , @RequestBody Set<String> markCodes  ){
		
		   Map<String, NodeCodeList<String>> workCodeTable = this.getNearWorkCodeTable(agvMapId, startCode, markCodes);
		   DualHashBidiMap<String, Integer> bidi = new DualHashBidiMap<>();
		   workCodeTable.entrySet().forEach( i ->{
			  bidi.put(i.getKey(),  CollectionUtils.size(i.getValue())) ;
			  
		   });
		   if( CollectionUtils.isEmpty(bidi.values())) {
			   
			   return  new NodeCodeList<String>();
		   }
		   Integer integer = bidi.values().parallelStream().min(Integer::compareTo).get();
		   String key = bidi.inverseBidiMap().get(integer);
		   return workCodeTable.get(key) ;
	}
	
	@Autowired
	private HttpClientService httpClientService ;
	
	
	@ApiOperation(value = "获得所有的充电桩的data")
	@GetMapping("/getAllCharges")
	public TjdChargerDto chargeData(){
		TjdChargerDto dto = null;
		try {
			HttpResult doGet = httpClientService.doGet( TjdCxt.CHARGE_URL ) ;
			log.debug("charge_data:{}" ,JSON.toJSONString(doGet));
			String body = doGet.getBody();
			 dto = JSON.parseObject(body , TjdChargerDto.class);
			
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return dto ;
	}
	
	
	@ApiOperation(value = "关闭所有的充电桩")
	@GetMapping("/charges/stop")
	public HttpResult stopCharge(){
		HttpResult doGet  = null;
		try {
			 doGet = httpClientService.doGet( TjdCxt.CHARGE_STOP_URL ) ;
			log.debug("charge_data:{}" ,JSON.toJSONString(doGet));
			
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return doGet ;
	}
	
	@ApiOperation(value = "查询agv当前是否着火及着火车辆")
	@GetMapping("/fire/agv/it")
	public List<Object> queryAgvIsFiring(){
		
		ArrayList<Object> newArrayList = Lists.newArrayList();
		newArrayList.add(TjdCxt.IS_AGV_FIRING);
		newArrayList.add(TjdCxt.FIRE_RETAKE_AGVS);
		return newArrayList ;
	}
	
	
	@ApiOperation(value = "异常-批量添加alert禁用")
	@PostMapping("/disable/alert")
	public Set<Integer> disableAlert( @RequestBody Set<Integer> alertId ){
		
	   if (CollectionUtils.isNotEmpty(alertId) ) {
		   
		   TjdCxt.DISABLE_ALERT.addAll(alertId);
	   }
		return TjdCxt.DISABLE_ALERT ;
	}
	
	@ApiOperation(value = "异常-查询某个alert是否已禁用")
	@GetMapping("/disable/alert/{alertId}")
	public boolean  queryDisableAlert( @PathVariable( "alertId" ) int alertId ){
		

		return TjdCxt.DISABLE_ALERT.contains( alertId) ;
	}
	
	@ApiOperation(value = "异常-删除某个alertId")
	@DeleteMapping("/disable/alert/{alertId}")
	public Set<Integer>  deleteDisableAlert( @PathVariable( "alertId" ) int alertId ){
		
		 TjdCxt.DISABLE_ALERT.remove( alertId ) ;
		 return TjdCxt.DISABLE_ALERT ;
	}
	
	@ApiOperation(value = "异常-清空所有的禁用列表")
	@GetMapping("/clear/disable/alert")
	public boolean  clearDisableAlert(  ){
		
		TjdCxt.DISABLE_ALERT.clear();
		
		return  true  ;
	}
	
	
	@ApiIgnore
	@ApiOperation(value = "for-test--- 人为向车辆中增加数据")
	@PostMapping("/add/{agvCode}/shelfData")
	public Map<String, TjdScreenItemDto> addShelfData( @PathVariable String agvCode ,  @RequestBody TjdScreenItemDto tjdScreenItemDto ){
		
		Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
		 Map<String, TjdScreenItemDto> data = Optional.ofNullable( vehicle.getShelfData()).orElse(new ConcurrentHashMap<>());
		
	    vehicle.getShelfData().put(tjdScreenItemDto.getBinNo(), tjdScreenItemDto);
		 vehicle.setShelfData(data);
		return vehicle.getShelfData();
	}
	
	@ApiIgnore
	@ApiOperation(value = "for-test--- 人为向车辆中删除数据")
	@DeleteMapping("/del/{agvCode}/shelfData")
	public Map<String, TjdScreenItemDto> delnetShelfData( @PathVariable String agvCode ,  @RequestBody TjdScreenItemDto tjdScreenItemDto ){
		
		Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
		Optional<Map<String, TjdScreenItemDto>> optional = Optional.ofNullable( vehicle.getShelfData());
		if( optional.isPresent()) {
			 vehicle.getShelfData().remove(tjdScreenItemDto.getBinNo());
		}
	    
		
		return vehicle.getShelfData();
	}
	
}
