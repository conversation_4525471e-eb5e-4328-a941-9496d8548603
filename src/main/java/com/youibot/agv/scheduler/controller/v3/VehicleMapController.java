package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSONArray;
import com.youibot.agv.scheduler.dto.HomeRelocationParam;
import com.youibot.agv.scheduler.dto.ManualRelocationParam;
import com.youibot.agv.scheduler.dto.RecordHomeMarkerParam;
import com.youibot.agv.scheduler.dto.SyncAGVMapParam;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.MapConstant.MARKER_TYPE_INITIAL;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/9/20 16:13
 */
@RestController("VehicleMapControllerV3")
@RequestMapping(value = "/api/v3/vehicles/{agvId}/maps", produces = "application/json")
@Api(value = "机器人地图操作", tags = "机器人地图操作", description = "机器人地图操作接口，用户可以通过这个接口录制地图，同步地图以及获取地图等操作。")
public class VehicleMapController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleMapController.class);

    @Autowired
    private MarkerService markerService;

    /**
     * 同步地图数据给agv并指定该地图
     *
     * @param param
     * @throws Exception
     * @throws
     */
    @ApiOperation(value = "同步地图")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/sync")
    @ResponseStatus(value = HttpStatus.OK)
    public void syncAGVMap(@RequestBody SyncAGVMapParam param, @PathVariable("agvId") String agvId) throws Exception {
        String agvMapId = param.getAgvMapId();
        if (StringUtils.isEmpty(agvMapId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        VehicleUtils.getOnlineVehicle(agvId).syncMap(agvMapId);
    }

    @ApiOperation(value = "自动重定位", notes = "vehicle自动重定位")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/relocation/auto")
    @ResponseStatus(value = HttpStatus.OK)
    public void autoRelocation(@PathVariable("agvId") String agvId) throws IOException, InterruptedException {
        VehicleUtils.getOnlineVehicle(agvId).autoRelocation();
    }

    @ApiOperation(value = "手动重定位", notes = "vehicle手动重定位")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/relocation/manual")
    @ResponseStatus(value = HttpStatus.OK)
    public void manualRelocation(@RequestBody ManualRelocationParam param, @PathVariable("agvId") String agvId) throws IOException, InterruptedException {
        Map<String, Object> sendParam = new HashMap<>();
        Double init_x = param.getInit_x();
        Double init_y = param.getInit_y();
        Double init_angle = param.getInit_angle();
        if (init_x == null || init_y == null || init_angle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        sendParam.put("init_x", init_x);
        sendParam.put("init_y", init_y);
        sendParam.put("init_angle", init_angle);
        VehicleUtils.getOnlineVehicle(agvId).manualRelocation(sendParam);
    }

    @ApiOperation(value = "初始点重定位", notes = "vehicle初始点重定位")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/relocation/home")
    @ResponseStatus(value = HttpStatus.OK)
    public void homeRelocation(@RequestBody HomeRelocationParam param, @PathVariable("agvId") String agvId) throws IOException, InterruptedException {
        String markerId = param.getMarkerId();
        if (StringUtils.isEmpty(markerId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Marker marker = markerService.selectById(markerId);
        if (marker == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.marker_is_null"));
        }
        if (!MARKER_TYPE_INITIAL.equals(marker.getType())) {
            throw new ExecuteException(MessageUtils.getMessage("http.marker_type_is_not_Initial"));
        }
        Double x = marker.getX();
        Double y = marker.getY();
        Double angle = marker.getAngle();
        String covariance = marker.getCovariance();
        if (x == null || y == null || angle == null || StringUtils.isEmpty(covariance)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Map<String, Object> sendParam = new HashMap<>();
        sendParam.put("init_x", x);
        sendParam.put("init_y", y);
        sendParam.put("init_angle", Math.toRadians(angle));
        sendParam.put("covariance", JSONArray.parseArray(covariance));
        VehicleUtils.getOnlineVehicle(agvId).manualRelocation(sendParam);
    }

    @ApiOperation(value = "录制初始位置", notes = "vehicle在激光地图中录制初始位置")
    @ApiImplicitParam(name = "agvId", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/markers/homes")
    @ResponseStatus(value = HttpStatus.OK)
    public Marker recordHomeMarkers(@RequestBody RecordHomeMarkerParam param, @PathVariable("agvId") String agvId) throws IOException {
        String agvMapId = param.getAgvMapId();
        if (StringUtils.isEmpty(agvMapId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Map<String, Object> resultMap = VehicleUtils.getOnlineVehicle(agvId).recordHomeMarker(agvMapId);
        if (resultMap == null || resultMap.isEmpty()) {
            LOGGER.error("record home marker error, agv result data is null");
            throw new ExecuteException(MessageUtils.getMessage("http.agv_return_failed"));
        }
        return markerService.insertHomeByAGVResultData(agvMapId, resultMap);
    }
}
