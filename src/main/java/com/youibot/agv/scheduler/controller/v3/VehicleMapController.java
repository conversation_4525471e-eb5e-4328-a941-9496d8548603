package com.youibot.agv.scheduler.controller.v3;

import static com.youibot.agv.scheduler.constant.MapConstant.MARKER_TYPE_INITIAL;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.bean.push.cmd.RelocationMessage;
import com.youibot.agv.scheduler.param.AgvMapParam;
import com.youibot.agv.scheduler.param.HomeRelocationParam;
import com.youibot.agv.scheduler.param.ManualRelocationParam;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.MessageUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/9/20 16:13
 */
@RestController("vehicleMapControllerV3")
@RequestMapping(value = "/api/v3/vehicles/{agvCode}/maps", produces = "application/json")
@Api(value = "机器人地图操作", tags = "机器人地图操作", description = "机器人地图操作接口，用户可以通过这个接口录制地图，同步地图以及获取地图等操作。")
public class VehicleMapController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(VehicleMapController.class);

    @Autowired
    private MarkerService markerService;

    @LogOperation
    @ApiOperation(value = "自动重定位", notes = "vehicle自动重定位")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/relocation/auto")
    @ResponseStatus(value = HttpStatus.OK)
    public void autoRelocation(@PathVariable("agvCode") String agvCode){

     
        super.getVehicle(agvCode).autoRelocation();
        logger.debug("agvCode:[{}],event:[自动重定位],content:{}", agvCode,"指令下发完成");
    }

    @LogOperation
    @ApiOperation(value = "手动重定位", notes = "vehicle手动重定位")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/relocation/manual")
    @ResponseStatus(value = HttpStatus.OK)
    public void manualRelocation(@PathVariable("agvCode") String agvCode, @RequestBody ManualRelocationParam param){
        Double init_x = param.getInit_x();
        Double init_y = param.getInit_y();
        Double init_angle = param.getInit_angle();
        if (init_x == null || init_y == null || init_angle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }

        RelocationMessage relocationMessage = new RelocationMessage();
        relocationMessage.setAgvCode(agvCode);
        relocationMessage.setType("manualRelocation");
        relocationMessage.setX(init_x);
        relocationMessage.setY(init_y);
        relocationMessage.setAngle(init_angle);
        super.getVehicle(agvCode).manualRelocation(relocationMessage);
        logger.debug("agvCode:[{}],event:[手动重定位],content:[{}]", agvCode, "指令下发完成");
    }

    @LogOperation
    @ApiOperation(value = "初始点重定位", notes = "vehicle初始点重定位")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/relocation/home")
    @ResponseStatus(value = HttpStatus.OK)
    public void homeRelocation(@PathVariable("agvCode") String agvCode, @RequestBody HomeRelocationParam param){
        String markerId = param.getMarkerId();
        if (StringUtils.isEmpty(markerId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Marker marker = markerService.selectById(param.getAgvMapName(),markerId,false);
        if (marker == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.marker_is_null"));
        }
        if (!MARKER_TYPE_INITIAL.equals(marker.getType())) {
            throw new ExecuteException(MessageUtils.getMessage("http.marker_type_is_not_Initial"));
        }
        Double x = marker.getX();
        Double y = marker.getY();
        Double angle = marker.getAngle();
        String covariance = marker.getCovariance();
        if (x == null || y == null || angle == null || StringUtils.isEmpty(covariance)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }

  
        RelocationMessage relocationMessage = new RelocationMessage();
        relocationMessage.setAgvCode(agvCode);
        relocationMessage.setType("homeRelocation");
        relocationMessage.setX(x);
        relocationMessage.setY(y);
        relocationMessage.setAngle(Math.toRadians(angle));
        relocationMessage.setCovariance(covariance);
        super.getVehicle(agvCode).manualRelocation(relocationMessage);
        logger.debug("agvCode:[{}],event:[初始点重定位],content:[{}]", agvCode, "指令下发完成");
    }

    @ApiOperation(value = "指定地图", notes = "指定地图")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "agvMapId", value = "地图ID", required = true, dataType = "String"),
        @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    })
    @PostMapping(value = "/appointMap")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public void getCurrentUseMap(@PathVariable("agvCode") String agvCode, @RequestBody AgvMapParam param) {
        String agvMapId = param.getAgvMapId();
        if (StringUtils.isEmpty(agvCode) || StringUtils.isEmpty(agvMapId)){
            throw new YOUIFleetException(MessageUtils.getMessage("http.missing_parameter"));
        }

  
        super.getVehicle(agvCode).appointMap(agvMapId);
        logger.debug("agvCode:[{}],event:[指定地图],content:[{}]", agvCode, "指令下发完成");
    }
}
