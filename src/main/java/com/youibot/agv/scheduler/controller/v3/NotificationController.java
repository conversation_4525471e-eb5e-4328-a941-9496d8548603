package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.Notification;
import com.youibot.agv.scheduler.service.NotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("notificationV3")
@RequestMapping(value = "/api/v3/notification", produces = "application/json")
@Api(value = "消息通知", tags = "消息通知", description = "获取修改Notification状态")
public class NotificationController extends BaseController {

    @Autowired
    private NotificationService notificationService;

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Notification> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return notificationService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Notification> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return notificationService.searchAll(searchMap);
    }


    @ApiOperation(value = "详情", notes = "根据ID获取Notification详情")
    @ApiImplicitParam(name = "id", value = "NotificationID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Notification get(@PathVariable("id") String id) {
        return notificationService.selectById(id);
    }

    @ApiOperation(value = "更新", notes = "根据ID更新Notification信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "NotificationID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "notification", value = "notification消息", required = true, dataType = "Notification")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Notification update(@PathVariable("id") String id, @RequestBody @Valid Notification notification) {
        notification.setId(id);
        this.notificationService.update(notification);
        return this.notificationService.selectById(id);
    }

    @ApiOperation(value = "批量更新", notes = "根据ids更新Notification信息")
    @ApiImplicitParam(name = "ids", value = "NotificationID集合", required = true, dataType = "List")
    @PutMapping(value = "/batchUpdate")
    @ResponseStatus(value = HttpStatus.OK)
    public int batchUpdate() {
        return notificationService.updateReadStatus();
    }

    @ApiOperation(value = "删除", notes = "根据ID删除Notification信息")
    @ApiImplicitParam(name = "id", value = "NotificationID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.notificationService.deleteById(id);
    }

    @ApiOperation(value = "获取消息类型列表")
    @GetMapping(value = "/getNotificationTypeList")
    @ResponseStatus(value = HttpStatus.OK)
    public List<String> getNotificationTypeList() {
        return notificationService.getNotificationTypeList();
    }

}
