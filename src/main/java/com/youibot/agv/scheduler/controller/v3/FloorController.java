package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.Floor;
import com.youibot.agv.scheduler.service.FloorService;
import com.youibot.agv.scheduler.service.MarkerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("FloorControllerV3")
@RequestMapping(value = "/api/v3/floors", produces = "application/json")
@Api(value = "楼层", tags = "楼层", description = "电梯在实现跨楼层功能时需要电梯与电梯点做楼层绑定")
public class FloorController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(FloorController.class);

    @Autowired
    private FloorService floorService;
    @Autowired
    private MarkerService markerService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Floor> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return floorService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Floor> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return floorService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "floor", value = "楼层", required = true, dataType = "Floor")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Floor save(@RequestBody @Valid Floor floor) {
        this.floorService.insert(floor);
        LOGGER.debug(floor.toString());
        return floor;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "楼层ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Floor get(@PathVariable("id") String id) {
        return floorService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "楼层ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "floor", value = "楼层", required = true, dataType = "Floor")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Floor update(@PathVariable("id") String id, @RequestBody @Valid Floor floor) {
        floor.setId(id);
        this.floorService.update(floor);
        return this.floorService.selectById(floor.getId());
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "楼层ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.floorService.deleteById(id);
    }
}
