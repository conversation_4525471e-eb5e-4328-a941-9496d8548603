package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/8/14 10:47
 */
@ApiIgnore
@RestController("TaiChiElevatorControllerV3")
@RequestMapping(value = "", produces = "application/json")
@Api(value = "太极电梯模拟接口", tags = "太极电梯模拟接口", description = "太极电梯模拟测试接口")
public class TaiChiElevatorController {

    @Autowired
    private HttpClientService httpClientService;

    private String dirfloor;

    @ApiOperation(value = "呼叫电梯")
    @PostMapping(value = "/elevatorcall")
    @ResponseStatus(value = HttpStatus.CREATED)
    public JSONObject elevatorcall(@RequestBody @Valid Map<String, Object> param) {
        String requestid = (String) param.get("requestid");
        String orifloor = (String) param.get("orifloor");
        dirfloor = (String) param.get("dirfloor");
        if (StringUtils.isEmpty(requestid) || StringUtils.isEmpty(orifloor) || StringUtils.isEmpty(dirfloor)) {
            throw new ExecuteException("缺少参数");
        }
        param.put("type", "call");
        CallbackThread thread = new CallbackThread(param);
        thread.start();
        JSONObject result = new JSONObject();
        result.put("code", "1");
        return result;
    }

    @ApiOperation(value = "关闭/释放电梯")
    @PostMapping(value = "/elevatoraction")
    @ResponseStatus(value = HttpStatus.CREATED)
    public JSONObject elevatoraction(@RequestBody @Valid Map<String, Object> param) {
        String actionid = (String) param.get("actionid");
        String actiontype = (String) param.get("actiontype");
        if (StringUtils.isEmpty(actionid) || StringUtils.isEmpty(actiontype)) {
            throw new ExecuteException("缺少参数");
        }
        if ("close".equals(actiontype)) {
            CallbackThread thread = new CallbackThread(param);
            thread.start();
        }
        JSONObject result = new JSONObject();
        result.put("code", "1");
        return result;
    }

    //推送数据到前端
    private class CallbackThread extends Thread {
        Map<String, Object> param;

        public CallbackThread(Map<String, Object> param) {
            this.param = param;
        }

        @Override
        public void run() {
            String type = (String) param.get("type");
            if ("call".equals(type)) {
                try {
                    Thread.sleep(30000);
                    Map<String, Object> callbackParam = new HashMap<>();
                    callbackParam.put("requestid", param.get("requestid"));
                    callbackParam.put("floor", param.get("orifloor"));
                    callbackParam.put("status", 1);
                    httpClientService.doPost("http://localhost:8080/elevatornotice", callbackParam);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                try {
                    Thread.sleep(30000);
                    Map<String, Object> callbackParam = new HashMap<>();
                    callbackParam.put("requestid", param.get("actionid"));
                    callbackParam.put("floor", dirfloor);
                    callbackParam.put("status", 1);
                    httpClientService.doPost("http://localhost:8080/elevatornotice", callbackParam);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
