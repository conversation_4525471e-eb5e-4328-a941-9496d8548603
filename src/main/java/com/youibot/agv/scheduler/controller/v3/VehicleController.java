package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.dto.AgvBatchParam;
import com.youibot.agv.scheduler.dto.AgvBatchResult;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.entity.AGVLog;
import com.youibot.agv.scheduler.entity.SchedulerSystems;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AGVLogService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.SchedulerSystemsService;
import com.youibot.agv.scheduler.service.SystemWorkModeService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.VehicleStatusInfo;
import com.youibot.agv.scheduler.vehicle.entity.VehicleWorkStatus;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_ONLINE_STATUS_ONLINE;

@RestController("VehicleControllerV3")
@RequestMapping(value = "/api/v3/vehicles", produces = "application/json")
@Api(value = "机器人", tags = "机器人", description = "机器人的实例对象，所有的连接成功的机器人都是通过这个对象进行操作。")
public class VehicleController extends BaseController {
    @Autowired
    private AGVService agvService;
    @Autowired
    private VehiclePool vehiclePool;
    @Autowired
    private SchedulerSystemsService schedulerSystemsService;


    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public VehicleStatusInfo get(@PathVariable("id") String id) {
        Vehicle vehicle = vehiclePool.getVehicle(id);
        return vehicleCopyInfo(vehicle);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "AGV ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        Vehicle vehicle = vehiclePool.getVehicle(id);
        if (vehicle == null) {
            throw new ExecuteException("没有找到要删除的机器人！");
        }
        if (SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus())) {
            throw new ExecuteException("当前机器人正在连接调度，不可删除");
        }
        vehiclePool.detachVehicle(id);
        agvService.deleteById(id);

    }


    @ApiOperation(value = "统计数量")
    @GetMapping(value = "/statistics")
    @ResponseStatus(value = HttpStatus.OK)
    public VehicleWorkStatus statistics() {
        List<Vehicle> vehicleList = vehiclePool.getAll();
        VehicleWorkStatus vehicleWorkStatus = new VehicleWorkStatus();
        vehicleWorkStatus.setTotal(vehicleList.size());
        for (Vehicle vehicle : vehicleList) {
            if (TASK_STATUS_FREE.equals(vehicle.getWorkStatus())) {
                vehicleWorkStatus.setFreeCount(vehicleWorkStatus.getFreeCount() + 1);
            } else if (TASK_STATUS_WORK.equals(vehicle.getWorkStatus())) {
                vehicleWorkStatus.setWorkCount(vehicleWorkStatus.getWorkCount() + 1);
            } else if (TASK_STATUS_CHARGING.equals(vehicle.getWorkStatus())) {
                vehicleWorkStatus.setChargeCount(vehicleWorkStatus.getChargeCount() + 1);
            }
            if (!SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus())) {
                vehicleWorkStatus.setOfflineCount(vehicleWorkStatus.getOfflineCount() + 1);
            }
        }
        return vehicleWorkStatus;
    }

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<VehicleStatusInfo> page(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        PageInfo<AGV> pageList = agvService.findPage(searchMap);
        List<AGV> agvList = pageList.getList();
        List<VehicleStatusInfo> vehicleList = new ArrayList<>();
        for (AGV agv : agvList) {
            vehicleList.add(vehicleCopyInfo(vehiclePool.getVehicle(agv.getId())));
        }
        PageInfo<VehicleStatusInfo> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(pageList, resultPage);
        resultPage.setList(vehicleList);
        return resultPage;
    }


    @ApiOperation(value = "列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "schedulerSystemsId", value = "调度地址", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "agvStatus", value = "agv状态 任务中-workStatus 空闲中-freeStatus 充电-chargeStatus 未连接-offlineStatus 异常-abnormalStatus", dataType = "String", paramType = "query"),
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<VehicleStatusInfo> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        List<AGV> agvList = agvService.getAll(searchMap);
        List<VehicleStatusInfo> vehicleList = new ArrayList<>();
        String agvStatus = searchMap.get("agvStatus");
        if (StringUtils.isEmpty(agvStatus)) {
            for (AGV agv : agvList) {
                vehicleList.add(vehicleCopyInfo(vehiclePool.getVehicle(agv.getId())));
            }
        } else {
            for (AGV agv : agvList) {
                Vehicle vehicle = vehiclePool.getVehicle(agv.getId());
                if (QUERY_WORK_STATUS.equals(agvStatus) && TASK_STATUS_WORK.equals(vehicle.getWorkStatus())) {
                    vehicleList.add(vehicleCopyInfo(vehicle));
                }
                if (QUERY_FREE_STATUS.equals(agvStatus) && TASK_STATUS_FREE.equals(vehicle.getWorkStatus())) {
                    vehicleList.add(vehicleCopyInfo(vehicle));
                }
                if (QUERY_CHARGER_STATUS.equals(agvStatus) && TASK_STATUS_CHARGING.equals(vehicle.getWorkStatus())) {
                    vehicleList.add(vehicleCopyInfo(vehicle));
                }
                if (QUERY_ONLINE_STATUS.equals(agvStatus) && !SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus())) {
                    vehicleList.add(vehicleCopyInfo(vehicle));
                }
                if (QUERY_ABNORMAL_STATUS.equals(agvStatus)) {
                    vehicleList.add(vehicleCopyInfo(vehicle));
                }
            }
        }
        return vehicleList;
    }


    @ApiOperation(value = "切换本地模式")
    @GetMapping(value = "/switchLocal/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Vehicle switchLocalMode(@PathVariable("id") String id) {
        AgvBatchParam agvBatchParam = new AgvBatchParam();
        List<String> agvIds = new ArrayList<>();
        agvIds.add(id);
        agvBatchParam.setAgvIds(agvIds);
        List<AgvBatchResult> resultData = this.batchLocal(agvBatchParam);
        if (!CollectionUtils.isEmpty(resultData)) {
            throw new ExecuteException(resultData.get(0).getErrorMessage());
        }
        return vehiclePool.getVehicle(id);
    }

    @ApiOperation(value = "批量一键停止(停止充电动作、归位动作、任务工作)")
    @PostMapping(value = "/batchStop")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvBatchResult> batchStop(@RequestBody AgvBatchParam agvBatchParam) {
        return schedulerSystemsService.batchStop(agvBatchParam);
    }


    @ApiOperation(value = "切换调度模式")
    @GetMapping(value = "/switchScheduler/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Vehicle switchSchedulerMode(@PathVariable("id") String id) throws InterruptedException {
        AgvBatchParam agvBatchParam = new AgvBatchParam();
        List<String> agvIds = new ArrayList<>();
        agvIds.add(id);
        agvBatchParam.setAgvIds(agvIds);
        List<AgvBatchResult> resultData = this.batchScheduler(agvBatchParam);
        if (!CollectionUtils.isEmpty(resultData)) {
            throw new ExecuteException(resultData.get(0).getErrorMessage());
        }
        return vehiclePool.getVehicle(id);
    }


    @ApiOperation(value = "批量上线")
    @PostMapping(value = "/batchScheduler")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvBatchResult> batchScheduler(@RequestBody AgvBatchParam agvBatchParam) throws InterruptedException {
        return schedulerSystemsService.batchScheduler(agvBatchParam);
    }

    @ApiOperation(value = "批量下线")
    @PostMapping(value = "/batchLocal")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvBatchResult> batchLocal(@RequestBody AgvBatchParam agvBatchParam) {
        return schedulerSystemsService.batchLocal(agvBatchParam);
    }

    @ApiOperation(value = "批量删除")
    @PostMapping(value = "/batchDelete")
    @ResponseStatus(value = HttpStatus.OK)
    public List<AgvBatchResult> batchDelete(@RequestBody AgvBatchParam agvBatchParam) {
        return schedulerSystemsService.batchDelete(agvBatchParam);
    }


    private VehicleStatusInfo vehicleCopyInfo(Vehicle vehicle) {
        VehicleStatusInfo vehicleStatusInfo = new VehicleStatusInfo();
        BeanUtils.copyProperties(vehicle, vehicleStatusInfo);
        vehicleStatusInfo.setAgvCode(vehicle.getDeviceNumber());
        if (!StringUtils.isEmpty(vehicle.getSchedulerSystemsId())) {
            SchedulerSystems systems = schedulerSystemsService.selectById(vehicle.getSchedulerSystemsId());
            if (systems != null) {
                vehicleStatusInfo.setSchedulerSystemsUrl("http://" + systems.getIp() + ":" + systems.getServicePort());
                vehicleStatusInfo.setSchedulerSystemsName(systems.getName());
            }
        }
        if(SCHEDULER_ONLINE_STATUS_ONLINE.equals(vehicle.getSchedulerStatus())){
            AGVLog onLineLog = vehicle.getOnLineLog();
            if (onLineLog != null) {
                vehicleStatusInfo.setOnlineTime(System.currentTimeMillis()/1000 - onLineLog.getStartTime());
            }
        }
        vehicleStatusInfo.setSignalStatus(((int) (new Random().nextDouble() * 50) + 300));
        return vehicleStatusInfo;
    }


}
