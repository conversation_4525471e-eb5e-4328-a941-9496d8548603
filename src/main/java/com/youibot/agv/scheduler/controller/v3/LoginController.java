package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.entity.User;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.param.UserParam;
import com.youibot.agv.scheduler.service.UserService;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.util.JWTUtil;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户
 *
 * @Author：yangpeilin
 * @Date: 2020/4/8 16:17
 */
@RestController("loginController")
@RequestMapping(value = "/api/v3/login", produces = "application/json")
@Api(value = "登录", tags = "登录", description = "登录相关接口")
public class LoginController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class);

    @Autowired
    private UserService userService;

    @ApiOperation(value = "用户分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<User> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return userService.findPage(searchMap);
    }

    @ApiOperation(value = "用户验证", notes = "用户验证")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "password", value = "admin用户密码", required = true, dataType = "String")
    })
    @GetMapping("/listUser")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public User listUser(@RequestBody User user) {
        return userService.selectByEntity(user);
    }

    @ApiOperation(value = "用户列表", notes = "查询用户列表")
    @ApiImplicitParam(name = "searchMap", value = "查询参数", required = false)
    @GetMapping("/user")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<User> allUser(@RequestParam(required = false) Map<String, String> searchMap) {
    	 HttpServletRequest request = CommonUtils.getReq();
        if (!UserUtil.isAdminUser(userService.selectById(UserUtil.getLoginUserId(request)))) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.user_is_not_admin"));
        }
        searchMap.put("sort", "create_time desc");
        return userService.findPage(searchMap);
    }

    @LogOperation
    @ApiOperation(value = "创建用户", notes = "创建用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "password", value = "admin用户密码", required = true, dataType = "String")
    })
    @PostMapping
    @ResponseStatus(value = HttpStatus.OK)
    public User createUser(@RequestBody UserParam user) {
        if (UserUtil.validateUser(user)) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.missing_parameter"));
        }
        List<User> userList = userService.findByUserNameOrPhoneOrEmail(user);
        if (!CollectionUtils.isEmpty(userList)) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.login_name_or_phone_or_email_exist"));
        }
        HttpServletRequest request = CommonUtils.getReq();
        User loginUser = UserUtil.getUser(request, userService);
        if (!loginUser.getAdmin().equals(1)) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.user_is_not_admin"));
        }
        User userInsert = new User();
        BeanUtils.copyPropertiesIgnoreNull(user, userInsert);
        userService.insert(userInsert);
        return userInsert;
    }

    @ApiOperation(value = "修改用户信息", notes = "修改用户信息",httpMethod = "PUT")
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable String id,
                                           @RequestBody UserParam userParam) {
        if (StringUtils.isEmpty(userParam.getEmail())
                || StringUtils.isEmpty(userParam.getPhone()) || StringUtils.isEmpty(userParam.getName())) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.missing_parameter"));
        }
        //不允许修改登录用户名
        userParam.setUserName(null);
        HttpServletRequest request = CommonUtils.getReq();
        User loginUser = UserUtil.getUser(request, userService);
        if (!loginUser.getId().equals(id) && !loginUser.getAdmin().equals(1)) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.user_is_not_admin"));
        }
        User userDB = this.userService.selectById(id);
        BeanUtils.copyPropertiesIgnoreNull(userParam, userDB);
        userService.update(userDB);
        return ResponseEntity.ok(userService.selectById(id));
    }

    @ApiOperation(value = "修改密码", notes = "修改用户密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "oldPassword", value = "admin用户旧密码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "password", value = "admin用户密码", required = true, dataType = "String")
    })
    @PutMapping("/updateUser")
    @ResponseStatus(value = HttpStatus.OK)
    public User updateUser(@RequestBody UserParam user) {
        String password = user.getPassword();
        String oldPassword = user.getOldPassword();
        if (StringUtils.isEmpty(password) || StringUtils.isEmpty(oldPassword)) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.old_password_or_password_is_empty"));
        }
        HttpServletRequest request = CommonUtils.getReq();
        User userDto = UserUtil.getUser(request, userService);
        if (!oldPassword.equals(userDto.getPassword())) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.old_password_error"));
        }
        userDto.setPassword(password);
        this.userService.update(userDto);
        return userDto;
    }

    @LogOperation
    @ApiOperation(value = "删除用户", notes = "删除用户")
    @ApiImplicitParam(name = "id", value = "用户id", required = true, dataType = "String")
    @DeleteMapping("/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public void deleteUser(@PathVariable("id") String id) {
    	 HttpServletRequest request = CommonUtils.getReq();
        if (!UserUtil.isAdminUser(userService.selectById(UserUtil.getLoginUserId(request)))) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.user_is_not_admin"));
        }
        User user = this.userService.selectById(id);
        if (user != null) {
            if ("admin".equals(user.getUserName())) {
                throw new YOUIFleetException(MessageUtils.getMessage("user.admin_not_allow_delete"));
            }
            this.userService.deleteById(id);
        }
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "用户ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public User get(@PathVariable("id") String id) {
        return userService.selectById(id);
    }

    @ApiOperation(value = "登录", notes = "登录")
    @ApiImplicitParam(name = "user", value = "用户账号密码信息", required = true, dataType = "User")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户账号", paramType = "userName", required = true, dataType = "String"),
            @ApiImplicitParam(name = "password", value = "用户密码", required = true, dataType = "String")})
    @PostMapping("/userLogin")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public Map<String, Object> login(@RequestBody UserParam user) {
        if (StringUtils.isEmpty(user.getUserName()) || StringUtils.isEmpty(user.getPassword())) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.username_or_password_is_empty"));
        }
        LOGGER.debug("user " + user.getUserName() + " Login ...");
        User userSearch = new User();
        userSearch.setUserName(user.getUserName());
//        BeanUtils.copyPropertiesIgnoreNull(user, userSearch);
        User userDto = this.userService.selectByEntity(userSearch);
        if (userDto == null || StringUtils.isEmpty(userDto.getUserName()) || !userDto.getPassword().equals(user.getPassword())) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.username_or_password_error"));
        }
        Map<String, Object> retMap = new HashMap<>();
        String token = JWTUtil.createToken(userDto);
		retMap.put("token", token);
        int value = JWTUtil.TOKEN_EXPRIE_DAY*1000*60*60*24;
		retMap.put("expireTime", value);
        retMap.put("id", userDto.getId());
        return retMap;
    }

    @ApiOperation(value = "获取当前登录用户", notes = "获取当前登录用户")
    @GetMapping("/currentUser")
    public User getCurrentUser(HttpServletRequest request) {
        return UserUtil.getUser(request, userService);
    }

    @ApiOperation(value = "重置密码", notes = "重置密码")
    @PostMapping("/{id}/resetPassword")
    public void resetPassword(@PathVariable String id, HttpServletRequest request) {
        String loginUserId = UserUtil.getLoginUserId(request);
        if (!UserUtil.isAdminUser(userService.selectById(loginUserId)) && !id.equals(loginUserId)) {
            throw new YOUIFleetException(MessageUtils.getMessage("user.user_is_not_admin"));
        }
        User user = userService.selectById(id);
        if (user != null) {
            user.setPassword("youibot");
            userService.update(user);
        }
    }

    @ApiOperation(value = "注销登录", notes = "注销登录")
    @PostMapping("/loginOut")
    public void loginOut(HttpServletResponse response) {
        //TODO 用redis注销登录
    }
}
