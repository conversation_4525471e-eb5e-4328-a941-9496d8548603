package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.dto.UserLoginParam;
import com.youibot.agv.scheduler.dto.UserPwdUpdateParam;
import com.youibot.agv.scheduler.entity.User;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.service.UserService;
import com.youibot.agv.scheduler.util.JWTUtil;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户
 *
 * @Author：yangpeilin
 * @Date: 2020/4/8 16:17
 */
@RestController("LoginControllerV3")
@RequestMapping(value = "/api/v3/login", produces = "application/json")
@Api(value = "登录", tags = "登录", description = "登录相关接口")
public class LoginController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class);

    @Autowired
    private UserService userService;

    @ApiOperation(value = "用户验证", notes = "用户验证")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginName", value = "用户名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "password", value = "admin用户密码", required = true, dataType = "String")
    })
    @GetMapping("/listUser")
    @ResponseStatus(value = HttpStatus.OK)
    public User listUser(User user) {
        return userService.selectByEntity(user);
    }

    @ApiOperation(value = "修改密码", notes = "修改用户密码")
    @ApiImplicitParam(name = "param", value = "修改密码参数", required = true, dataType = "UserPwdUpdateParam")
    @PutMapping("/updateUser")
    @ResponseStatus(value = HttpStatus.OK)
    public User updateUser(@RequestBody UserPwdUpdateParam param, HttpServletRequest request) {
        String password = param.getPassword();
        String oldPassword = param.getOldPassword();
        if (StringUtils.isEmpty(password) || StringUtils.isEmpty(oldPassword)) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.missing_parameter"));
        }
        User user = UserUtil.getUser(request, userService);
        if (!oldPassword.equals(user.getPassword())) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.old_password_error"));
        }
        user.setPassword(password);
        this.userService.update(user);
        return user;
    }

    @ApiOperation(value = "登录", notes = "登录")
    @ApiImplicitParam(name = "user", value = "用户账号密码信息", required = true, dataType = "User")
    @PostMapping("/userLogin")
    @ResponseStatus(value = HttpStatus.OK)
    public String login(@RequestBody @Valid UserLoginParam param) {
        String loginName = param.getLoginName();
        String password = param.getPassword();
        if (StringUtils.isEmpty(loginName) || StringUtils.isEmpty(password)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        LOGGER.debug("user " + loginName + " Login ...");
        User user = this.userService.selectByLoginName(loginName);
        if (user == null || StringUtils.isEmpty(user.getLoginName())) {
            throw new ExecuteException(MessageUtils.getMessage("service.user_is_null"));
        }
        if (!user.getPassword().equals(password)) {
            throw new ExecuteException(MessageUtils.getMessage("http.user_password_error"));
        } else {
            return JWTUtil.createToken(user);
        }
    }
}
