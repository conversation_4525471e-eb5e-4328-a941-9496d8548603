package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.dto.MissionWorkParam;
import com.youibot.agv.scheduler.dto.MissionWorkParams;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThread;
import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThreadPool;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.entity.MissionWorkActionParameter;
import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.AGVConstant.MANUAL_CONTROL_MODE;
import static com.youibot.agv.scheduler.constant.MissionConstant.*;

@RestController("missionWorkControllerV3")
@RequestMapping(value = "/api/v3/missionWorks", produces = "application/json")
@Api(value = "任务", tags = "任务", description = "任务的管理接口，任务是AGV实际执行的内容。")
public class MissionWorkController extends BaseController {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionWorkThreadPool missionWorkThreadPool;

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private AGVService agvService;

    @Autowired
    private MissionWorkActionParameterService missionWorkActionParameterService;

    @ApiOperation(value = "分页查询")
    @PostMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionWork> page(@RequestBody MissionWorkParams missionWorkParams) {
        if (missionWorkParams == null) {
            missionWorkParams = new MissionWorkParams();
        }
        return missionWorkService.selectPage(missionWorkParams);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWork> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkService.searchAll(searchMap);
    }

    @ApiOperation(value = "根据状态列表查询任务列表")
    @ApiImplicitParam(name = "statusList", value = "状态列表", example = "RUNNING,WAIT", required = true, dataType = "String", paramType = "query")
    @GetMapping("/listByStatus")
    @ResponseStatus(value = HttpStatus.OK)
    public Map<String, Object> getListByStatus(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("missionWorkList", missionWorkService.selectByStatusList(Arrays.asList(searchMap.get("statusList").split(","))));
        map.put("now", systemConfigService.getDataBaseCurrentTime().getTime());
        return map;
    }

    @ApiOperation(value = "根据预设任务创建任务")
    @ApiImplicitParam(name = "missionWorkParam", value = "创建任务对象", paramType = "body", required = true, dataType = "MissionWorkParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionWork createMissionWork(@RequestBody MissionWorkParam missionWorkParam) {
        return this.missionWorkService.createByMissionWorkParam(missionWorkParam);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWork get(@PathVariable("id") String id) {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork != null) {
            if (!StringUtils.isEmpty(missionWork.getAgvId())) {
                missionWork.setAgvName(agvService.selectById(missionWork.getAgvId()).getName());
            }
            if (MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus()) || MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus())) {
                if (missionWork.getEndTime() != null) {
                    missionWork.setRunTime(missionWork.getEndTime().getTime() - missionWork.getStartTime().getTime());
                } else if (missionWork.getUpdateTime() != null && missionWork.getStartTime() != null) {
                    missionWork.setRunTime(missionWork.getUpdateTime().getTime() - missionWork.getStartTime().getTime());
                }
            } else {
                if (missionWork.getStartTime() != null) {
                    long now = systemConfigService.getDataBaseCurrentTime().getTime();
                    missionWork.setRunTime(now - missionWork.getStartTime().getTime());
                }
            }
        }
        return missionWork;
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionWork", value = "任务", required = true, dataType = "MissionWork")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWork update(@PathVariable("id") String id, @RequestBody @Valid MissionWork missionWork) {
        MissionWork missionWorkDB = missionWorkService.selectById(id);
        if (missionWorkDB == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (!MISSION_WORK_STATUS_CREATE.equals(missionWorkDB.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_already_execute"));
        }
        missionWork.setId(id);
        this.missionWorkService.update(missionWork);
        return missionWorkService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public int delete(@PathVariable("id") String id) {
        return this.missionWorkService.deleteById(id);
    }

    @ApiOperation(value = "根据任务ID查询动作列表")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionWorkActions")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkAction> getMissionWorkActions(@PathVariable("id") String id) {
        List<MissionWorkAction> missionWorkActionList = missionWorkActionService.selectByMissionWorkId(id);
        String startPoint = null;
        for (MissionWorkAction missionWorkAction : missionWorkActionList) {
            if ("MOVE_SIDE_PATH".equals(missionWorkAction.getActionType())) {
                MissionWorkActionParameter actionParameter = missionWorkActionParameterService.selectByMissionWorkParam(missionWorkAction.getId(), "markerCode");
                if (actionParameter != null) {
                    String markerCode = actionParameter.getParameterValue();
                    missionWorkAction.setEndPoint(markerCode);
                    missionWorkAction.setStartPoint(startPoint);
                    startPoint = markerCode;
                }
            }
        }
        return missionWorkActionList;
    }

    @ApiOperation(value = "获取等待输入的变量列表")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/waitInputVariables")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkGlobalVariable> getWaitInputVariables(@PathVariable("id") String id) {
        return missionWorkGlobalVariableService.selectWaitInputByMissionWorkId(id);
    }

    @ApiOperation(value = "暂停任务")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/pause")
    @ResponseStatus(value = HttpStatus.OK)
    public void pause(@PathVariable("id") String id) throws InterruptedException {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_PAUSE.equals(missionWork.getStatus())) {//如果是暂停状态，直接返回成功。
            return;
        }
        if (!MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_running"));
        }
        MissionWorkThread missionWorkThread = missionWorkThreadPool.get(id);
        Vehicle vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
        if (missionWorkThread == null || !VehicleUtils.checkVehicleStatusOnMissionWork(vehicle)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.not_ready_for_the_mission"));
        }
        missionWorkThread.pauseWork();
    }

    @ApiOperation(value = "恢复任务")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/resume")
    @ResponseStatus(value = HttpStatus.OK)
    public void resume(@PathVariable("id") String id) throws InterruptedException {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus())) {//如果是运行状态，直接返回成功
            return;
        }
        if (!MISSION_WORK_STATUS_PAUSE.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_FAULT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_pause_or_fault"));
        }

        if (MISSION_WORK_ALLOCATION_STATUS_ASSIGNED.equals(missionWork.getAllocationStatus())) {//已分配到AGV执行
            MissionWorkThread missionWorkThread = missionWorkThreadPool.get(id);
            Vehicle vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
            if (missionWorkThread == null || !VehicleUtils.checkVehicleStatusOnMissionWork(vehicle)) {
                throw new ExecuteException(MessageUtils.getMessage("vehicle.not_ready_for_the_mission"));
            }
            missionWorkThread.resumeWork();
        } else {//未分配到AGV执行
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_CREATE);//修改为创建状态
        }
    }

    @ApiOperation(value = "停止任务并将机器人切换到手动模式")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/stopAndManualMode")
    @ResponseStatus(value = HttpStatus.OK)
    public void stopAndManualMode(@PathVariable("id") String id) throws InterruptedException {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {//如果是停止状态，直接返回成功。
            return;
        }
        if (MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_success"));
        }
        if (MISSION_WORK_ALLOCATION_STATUS_ASSIGNED.equals(missionWork.getAllocationStatus())) {//已分配到AGV执行
            MissionWorkThread missionWorkThread = getMissionWorkThread(id, missionWork);
            Vehicle vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
            vehicle.setControlMode(MANUAL_CONTROL_MODE);//手动控制模式
            missionWorkThread.stopWork();
        } else {//未分配到AGV执行
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SHUTDOWN);//修改为创建状态
        }
    }

    @ApiOperation(value = "停止任务")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/stop")
    @ResponseStatus(value = HttpStatus.OK)
    public void stop(@PathVariable("id") String id) throws InterruptedException {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {//如果是停止状态，直接返回成功。
            return;
        }
        if (MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_success"));
        }
        if (MISSION_WORK_ALLOCATION_STATUS_ASSIGNED.equals(missionWork.getAllocationStatus())) {//已分配到AGV执行
            MissionWorkThread missionWorkThread = getMissionWorkThread(id, missionWork);
            missionWorkThread.stopWork();
        } else {//未分配到AGV执行
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SHUTDOWN);//修改为停止状态
        }
    }

    private MissionWorkThread getMissionWorkThread(String id, MissionWork missionWork) {
        MissionWorkThread missionWorkThread = missionWorkThreadPool.get(id);
        if (missionWorkThread == null) {
            VehicleUtils.getOnlineVehicle(missionWork.getAgvId()).setMissionWork(missionWork);
            missionWorkThread = (MissionWorkThread) ApplicationUtils.getBean("missionWorkThread");
            missionWorkThread.recoveryWork(missionWork);
        }
        return missionWorkThread;
    }

    @ApiOperation(value = "继续执行任务")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/continue")
    @ResponseStatus(value = HttpStatus.OK)
    public void continueWork(@PathVariable("id") String id) throws InterruptedException {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus()) || MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus())) {
            return;
        }
        if (!MISSION_WORK_STATUS_WAIT.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_wait_or_wait_input"));
        }
        MissionWorkThread missionWorkThread = missionWorkThreadPool.get(id);
        Vehicle vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
        if (missionWorkThread == null || !VehicleUtils.checkVehicleStatusOnMissionWork(vehicle)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.not_ready_for_the_mission"));
        }
        missionWorkThread.continueWork();
    }

    @ApiOperation(value = "根据任务ID查询任务全局变量")
    @ApiImplicitParam(name = "id", value = "任务ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionWorkGlobalVariables")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkGlobalVariable> missionWorkGlobalVariables(@PathVariable("id") String id) {
        return missionWorkGlobalVariableService.selectByMissionWorkId(id);
    }

    @ApiOperation(value = "停止所有任务")
    @PostMapping(value = "/all/controls/stop")
    @ResponseStatus(value = HttpStatus.OK)
    public void shutdownByUnComplete() throws InterruptedException {
        missionWorkService.shutdownByUnComplete();
    }

}
