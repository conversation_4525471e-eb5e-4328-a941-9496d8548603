package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.engine.exception.MissionWorkException;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;
import com.youibot.agv.scheduler.entity.WorkScheduler;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.service.MissionWorkCommandService;
import com.youibot.agv.scheduler.param.MissionWorkCreateCheck;
import com.youibot.agv.scheduler.param.MissionWorkParam;
import com.youibot.agv.scheduler.param.SchedulerStopParam;
import com.youibot.agv.scheduler.param.SchedulerStopResult;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;

@RestController("missionWorkControllerV3")
@RequestMapping(value = "/api/v3/missionWorks", produces = "application/json")
@Api(value = "作业", tags = "作业", description = "作业的管理接口，作业是AGV实际执行的内容。")
public class MissionWorkController extends BaseController {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @ApiOperation(value = "导出任务记录")
	@GetMapping("/exportRecord")
	@ResponseStatus(value = HttpStatus.OK)
	public void exportRecord(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap,HttpServletResponse response) {
	     missionWorkService.exportRecord(searchMap,response);
	}


	@Autowired
    private MissionWorkCommandService missionWorkCommandService;

    private static final Logger logger = LoggerFactory.getLogger(MissionWorkController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionWork> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkService.findPage(searchMap);
    }


    @ApiOperation(value = "作业名称列表")
    @GetMapping("/getNames")
    @ResponseStatus(value = HttpStatus.OK)
    public Set<String> getNames() {
        List<MissionWork> missionWorks = missionWorkService.findAll();
        return Optional.ofNullable(missionWorks).orElse(new ArrayList<>()).stream().map(MissionWork::getName).collect(Collectors.toSet());
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWork> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkService.searchAll(searchMap);
    }

    @ApiOperation(value = "检测创建作业是否有冲突")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "missionId", value = "任务的ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "missionQueueId", value = "任务链ID", dataType = "String", paramType = "query")
    })
    @GetMapping(value = "/create/check")
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionWorkCreateCheck missionWorkCreateCheck(@RequestParam(required = false) @ApiIgnore Map<String, String> param) {
        MissionWorkParam missionWorkParam = JSONObject.parseObject(JSON.toJSONString(param), MissionWorkParam.class);
        return this.missionWorkService.createCheckByMissionWorkParam(missionWorkParam);
    }

    @ApiOperation(value = "根据任务创建作业")
    @ApiImplicitParam(name = "missionWorkParam", value = "创建任务对象", paramType = "body", required = true, dataType = "MissionWorkParam")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    @LogOperation
    public MissionWork createMissionWork(@RequestBody MissionWorkParam missionWorkParam) {
        return this.missionWorkService.createByMissionWorkParam(missionWorkParam);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWork get(@PathVariable("id") String id) {
        return missionWorkService.selectMissionWorkById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionWork", value = "工作", required = true, dataType = "MissionWork")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public MissionWork update(@PathVariable("id") String id, @RequestBody @Valid MissionWork missionWork) {
        MissionWork missionWorkDB = missionWorkService.selectById(id);
        if (missionWorkDB == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (!MISSION_WORK_STATUS_CREATE.equals(missionWorkDB.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_already_execute"));
        }
        missionWork.setId(id);
        this.missionWorkService.update(missionWork);
        return missionWorkService.selectById(id);
    }

    @LogOperation
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public int delete(@PathVariable("id") String id) {
        return this.missionWorkService.deleteById(id);
    }

    @ApiOperation(value = "根据作业ID查询执行动作列表")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionWorkActions")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkAction> getMissionWorkActions(@PathVariable("id") String id) {
        return missionWorkActionService.selectByMissionWorkId(id);
    }

    @ApiOperation(value = "获取等待输入的变量列表")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/waitInputVariables")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkGlobalVariable> getWaitInputVariables(@PathVariable("id") String id) {
        return missionWorkGlobalVariableService.selectWaitInputByMissionWorkId(id);
    }

    /**
     * 暂停任务
     *
     * @param id
     * @param
     * @return
     */
    @ApiOperation(value = "暂停作业")
    @ApiImplicitParam(name = "id", value = "作业ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/pause")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public void pause(@PathVariable("id") String id) {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_PAUSE.equals(missionWork.getStatus())) {//如果是暂停状态，直接返回成功。
            return;
        }
        if (!MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_running"));
        }

        
        this.getVehicle(missionWork.getAgvCode()).pauseMissionWork(id);
        //missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_PAUSE);//将missionWork状态置为暂停中
        logger.debug("agvCode:[{}],event:[暂停作业],当前暂停的作业数据：[{}]", missionWork.getAgvCode(),JSONObject.toJSONString(missionWork));
        
    }

    /**
     * 恢复任务
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "恢复作业")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/resume")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation("恢复作业")
    public void resume(@PathVariable("id") String id) {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus())) {//如果是运行状态，直接返回成功
            return;
        }
        if (!MISSION_WORK_STATUS_PAUSE.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_FAULT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_pause_or_fault"));
        }

        ThreadContext.put("ROUTINGKEY", missionWork.getAgvCode());
        this.getVehicle(missionWork.getAgvCode()).resumeMissionWork(id);
        //missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_RESUME);//将missionWork状态置为恢复中
//            if (MISSION_WORK_ALLOCATION_STATUS_ASSIGNED.equals(missionWork.getAllocationStatus())) {//已分配到AGV执行
//                this.getVehicle(missionWork.getAgvCode()).resumeCommand(id);
//                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_RESUME);//将missionWork状态置为恢复中
//            } else {//未分配到AGV执行
//                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_CREATE);//修改为创建状态
//            }
        logger.debug("agvCode:[{}],event:[恢复作业],当前恢复的作业数据：[{}]", missionWork.getAgvCode(), JSONObject.toJSONString(missionWork));
        ThreadContext.remove("ROUTINGKEY");

    }


    /**
     * 停止任务
     * TODO need update .
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "停止作业")
    @ApiImplicitParam(name = "id", value = "工作ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/stop")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public SchedulerStopResult stop(@PathVariable("id") String id, @RequestBody SchedulerStopParam param) {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new MissionWorkException(ExceptionInfoEnum.MISSION_WORK_STOP.getErrorCode(), ExceptionInfoEnum.MISSION_WORK_STOP.getMessage());
        }

   

        WorkScheduler workScheduler = workSchedulerService.selectPrepareAndRunningByWork(missionWork.getId());
        if (MISSION_WORK_STATUS_CREATE.equals(missionWork.getStatus()) && workScheduler == null) {//创建状态
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SHUTDOWN);//修改为已停止
        } else if (MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus()) || MISSION_WORK_STATUS_BEING_SHUTDOWN.equals(missionWork.getStatus())) {
            logger.debug("agvCode:[{}],event:[停止作业],当前作业已执行完成：[{}]", missionWork.getAgvCode(),JSONObject.toJSONString(missionWork));
        } else {
            if (StringUtils.isEmpty(missionWork.getAgvCode())) {
                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SHUTDOWN);//将missionWork状态置为停止中
                if (workScheduler != null) {
                    workSchedulerService.updateCancel(missionWork.getId());
                }
                logger.debug("agvCode:[{}],event:[停止作业],作业未指定小车，直接停止：[{}]", missionWork.getAgvCode(),JSONObject.toJSONString(missionWork));
            } else {
                Vehicle vehicle = vehiclePoolService.selectById(missionWork.getAgvCode());
                if (!param.isForcedStop() && (vehicle == null || !VehicleConstant.ONLINE.equals(vehicle.getOnlineStatus()))) {
                    return new SchedulerStopResult("ERROR", 1001);//非强制取消且机器人不在线
                }
                
                if (vehicle == null || !VehicleConstant.ONLINE.equals(vehicle.getOnlineStatus()) ||org.apache.commons.lang3.StringUtils.isBlank(vehicle.getMissionWorkId()) ) {
                    //如果机器人为离线状态, 不发送取消指令到mqtt, 直接清理相关资源, 目前该做法主要针对机器人关机掉线后, 无法快速清理资源的问题
                    missionWork.setStatus(MISSION_WORK_STATUS_SHUTDOWN);
                    missionWorkCommandService.updateMissionWork(missionWork);
                    logger.debug("agvCode:[{}],event:[停止作业],机器人已掉线，直接停止作业，不发送mq信息：[{}]", missionWork.getAgvCode(),JSONObject.toJSONString(missionWork));
                } else {
                    //missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_SHUTDOWN);//将missionWork状态置为停止中
                    vehicle.cancelMissionWork(missionWork.getId());
                }
            }
        }

        logger.debug("agvCode:[{}],event:[停止作业],当前停止的作业数据：[{}]", missionWork.getAgvCode(),JSONObject.toJSONString(missionWork));
    

        return new SchedulerStopResult("SUCCESS", null);
    }

    /**
     * 抛出异常
     *
     * @param missionWork
     * @param e
     */
    private void throwMissionWorkException(MissionWork missionWork, Exception e) {
        if (missionWork != null) {
            //记录agv返回的错误信息
            missionWork.setErrorCode(ExceptionInfoEnum.MISSION_WORK_STOP.getErrorCode());
//            missionWork.setMessage(e.getMessage());
            missionWorkService.update(missionWork);
        }
        logger.error("agvCode:[{}],event:[停止作业异常],停止的作业数据：[{}]，异常原因：[{}]", missionWork.getAgvCode(),JSONObject.toJSONString(missionWork),e);
        throw new MissionWorkException(ExceptionInfoEnum.MISSION_WORK_STOP.getErrorCode(), ExceptionInfoEnum.MISSION_WORK_STOP.getMessage());
//            throw e;
    }


    @ApiOperation(value = "继续执行作业")
    @ApiImplicitParam(name = "id", value = "作业ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/controls/continue")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation
    public void continueWork(@PathVariable("id") String id) {
        MissionWork missionWork = missionWorkService.selectById(id);
        if (missionWork == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
        }
        if (MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus()) || MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus())) {
            return;
        }
        if (!MISSION_WORK_STATUS_WAIT.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_wait_or_wait_input"));
        }

     
        this.getVehicle(missionWork.getAgvCode()).continueMissionWork(id);
        logger.debug("agvCode:[{}],event:[继续作业],继续执行的作业数据：[{}]", missionWork.getAgvCode(), JSONObject.toJSONString(missionWork));
     
    }


    @ApiOperation(value = "根据作业ID查询工作全局变量")
    @ApiImplicitParam(name = "id", value = "作业ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionWorkGlobalVariables")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkGlobalVariable> missionWorkGlobalVariables(@PathVariable("id") String id) {
        return missionWorkGlobalVariableService.selectByMissionWorkId(id);
    }


    @ApiOperation(value = "根据状态列表查询作业列表")
    @ApiImplicitParam(name = "statusList", value = "状态列表", example = "RUNNING,WAIT", required = true, dataType = "String", paramType = "query")
    @GetMapping("/listByStatus")
    @ResponseStatus(value = HttpStatus.OK)
    public Map<String, Object> getListByStatus(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("missionWorkList", missionWorkService.selectByStatusList(Arrays.asList(searchMap.get("statusList").split(",")), searchMap.get("missionGroupId")));
        map.put("now", systemConfigService.getDataBaseCurrentTime().getTime());
        return map;
    }


    @ApiOperation(value = "根据状态列表查询作业列表-分页版本")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "statusList", value = "状态列表", example = "RUNNING,WAIT", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/listByStatus/page")
    @ResponseStatus(value = HttpStatus.OK)
    public Map<String, Object> getListByStatusPage(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("missionWorkList", missionWorkService.selectByStatusListPage(searchMap));
        map.put("now", systemConfigService.getDataBaseCurrentTime().getTime());
        return map;
    }


    @ApiOperation(value = "查询机器人正在执行的作业")
    @ApiImplicitParam(name = "agvCode", value = "机器人编码", example = "108", required = true, dataType = "String", paramType = "path")
    @GetMapping("/getAgvMissionWork/{agvCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWork getListByStatus(@PathVariable("agvCode") String agvCode) {
        return missionWorkService.selectUnCompleteByAgvId(agvCode);
    }
}
