package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.DockingPoint;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.DockingPointService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("DockingPointControllerV3")
@RequestMapping(value = "/api/v3/dockingPoints", produces = "application/json")
@Api(value = "对接点", tags = "对接点", description = "对接点管理接口，对接点是指地图所有的对接点位资源，例如：反光条特征对接点，V型特征对接点，充电桩对接点。")
public class DockingPointController extends BaseController{

	@Autowired
	private DockingPointService dockingPointService;

	@Autowired
	private AGVMapService agvMapService;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(DockingPointController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<DockingPoint> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return dockingPointService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<DockingPoint> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return dockingPointService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "dockingPoint", value = "对接点", required = true, dataType = "DockingPoint")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public DockingPoint save(@RequestBody @Valid DockingPoint dockingPoint) {
        agvMapService.checkUpdateAllow(dockingPoint.getAgvMapId());
        this.dockingPointService.insert(dockingPoint);
        LOGGER.debug(dockingPoint.toString());
        return dockingPoint;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "对接点ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public DockingPoint get(@PathVariable("id") String id) {
        return dockingPointService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "对接点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "dockingPoint", value = "对接点", required = true, dataType = "DockingPoint")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public DockingPoint update(@PathVariable("id") String id, @RequestBody @Valid DockingPoint dockingPoint) {
        agvMapService.checkUpdateAllow(dockingPoint.getAgvMapId());
        dockingPoint.setId(id);
        this.dockingPointService.update(dockingPoint);
        return this.dockingPointService.selectById(dockingPoint.getId());
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "对接点ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        DockingPoint dockingPoint = dockingPointService.selectById(id);
        if (dockingPoint == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.docking_point_is_null"));
        }
        agvMapService.checkUpdateAllow(dockingPoint.getAgvMapId());
        this.dockingPointService.deleteById(id);
    }
}
