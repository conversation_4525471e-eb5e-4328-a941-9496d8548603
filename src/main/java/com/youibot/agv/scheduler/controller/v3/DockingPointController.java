package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.DockingPointService;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController("dockingPointControllerV3")
@RequestMapping(value = "/api/v3/dockingPoints", produces = "application/json")
@Api(value = "对接点", tags = "对接点", description = "对接点管理接口，对接点是指地图所有的对接点位资源，例如：反光条特征对接点，V型特征对接点，充电桩对接点。")
@Deprecated
public class DockingPointController {

    @Autowired
    private DockingPointService dockingPointService;

    @Autowired
    private AGVMapService agvMapService;

    private static final Logger LOGGER = LoggerFactory.getLogger(DockingPointController.class);

//    @ApiOperation(value = "分页查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
//    })
//    @GetMapping("/page")
//    @ResponseStatus(value = HttpStatus.OK)
//    public PageInfo<DockingPoint> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap, Boolean isDraft) {
//        return dockingPointService.findPage(searchMap,isDraft);
//    }
//
//    @ApiOperation(value = "列表")
//    @GetMapping
//    @ResponseStatus(value = HttpStatus.OK)
//    public List<DockingPoint> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap, Boolean isDraft) {
//        return dockingPointService.searchAll(searchMap,isDraft);
//    }
//
//    @ApiOperation(value = "创建")
//    @ApiImplicitParam(name = "dockingPoint", value = "对接点", required = true, dataType = "DockingPoint")
//    @PostMapping
//    @ResponseStatus(value = HttpStatus.CREATED)
//    public DockingPoint save(@RequestBody @Valid DockingPoint dockingPoint) {
//        this.dockingPointService.insert(dockingPoint);
//        LOGGER.debug(dockingPoint.toString());
//        return dockingPoint;
//    }
//
//    @ApiOperation(value = "详情")
//    @ApiImplicitParam(name = "id", value = "对接点ID", paramType = "path", required = true, dataType = "String")
//    @GetMapping(value = "/{id}")
//    @ResponseStatus(value = HttpStatus.OK)
//    public DockingPoint get(@PathVariable("id") String id,String agvMapId,Boolean isDraft) {
//        return dockingPointService.selectById(agvMapId,id,isDraft);
//    }
//
//    @ApiOperation(value = "更新")
//    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "对接点ID", paramType = "path", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "dockingPoint", value = "对接点", required = true, dataType = "DockingPoint")})
//    @PutMapping(value = "/{id}")
//    @ResponseStatus(value = HttpStatus.OK)
//    public DockingPoint update(@PathVariable("id") String id, @RequestBody @Valid DockingPoint dockingPoint) {
//        dockingPoint.setId(id);
//        this.dockingPointService.update(dockingPoint);
//        return this.dockingPointService.selectById(dockingPoint.getAgvMapId(),dockingPoint.getId(),true);
//    }
//
//    @ApiOperation(value = "删除")
//    @ApiImplicitParam(name = "id", value = "对接点ID", paramType = "path", required = true, dataType = "String")
//    @DeleteMapping(value = "/{id}")
//    @ResponseStatus(value = HttpStatus.NO_CONTENT)
//    public void delete(@PathVariable("id") String id,String agvMapId) {
//        DockingPoint dockingPoint = dockingPointService.selectById(agvMapId,id,true);
//        if (dockingPoint == null) {
//            throw new ExecuteException(MessageUtils.getMessage("service.docking_point_is_null"));
//        }
//        this.dockingPointService.deleteById(agvMapId,id);
//    }
}
