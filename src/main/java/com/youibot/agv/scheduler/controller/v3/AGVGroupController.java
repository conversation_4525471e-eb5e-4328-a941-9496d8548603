package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.AGVGroup;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.service.AGVGroupService;
import com.youibot.agv.scheduler.service.AGVService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("agvGroupControllerV3")
@RequestMapping(value = "/api/v3/agvGroups", produces = "application/json")
@Api(value = "AGV组", tags = "AGV组", description = "AGV分组管理功能，用户可以把AGV添加到不同的组中，分配任务时指定AGV组，在任务分配时只会分配到组内的AGV。")
public class AGVGroupController extends BaseController {

    @Autowired
    private AGVGroupService agvGroupService;

    @Autowired
    private AGVService agvService;

    private static final Logger LOGGER = LoggerFactory.getLogger(AGVGroupController.class);

    // 获取AGV组列表
    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AGVGroup> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvGroupService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<AGVGroup> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return agvGroupService.searchAll(searchMap);
    }

    // 创建agv组信息
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "agvGroup", value = "AGV组", required = true, dataType = "AGVGroup")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AGVGroup save(@RequestBody @Valid AGVGroup agvGroup) {
        this.agvGroupService.insert(agvGroup);
        LOGGER.debug(agvGroup.toString());
        return agvGroup;
    }

    // 获取agv组信息 ById
    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "AGV组ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVGroup get(@PathVariable("id") String id) {
        return agvGroupService.selectById(id);
    }

    // 更新agv组信息 ById
    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "AGV组ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "agvGroup", value = "AGV组", required = true, dataType = "AGVGroup")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVGroup update(@PathVariable("id") String id, @RequestBody @Valid AGVGroup agvGroup) {
        agvGroup.setId(id);
        this.agvGroupService.update(agvGroup);
        return this.agvGroupService.selectById(id);
    }

    // 删除agv组信息 ById
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "AGV组ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.agvGroupService.deleteById(id);
    }

    // 获取该agv组下的agv列表
    @ApiOperation(value = "查询AGV列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "AGV组ID", paramType = "path", required = true, dataType = "String")
            })
    @GetMapping(value = "/{id}/agvs")
    @ResponseStatus(value = HttpStatus.OK)
    public List<Agv> list(@PathVariable("id") String agvGroupId) {
        return this.agvService.selectByAgvGroupId(agvGroupId);
    }
}
