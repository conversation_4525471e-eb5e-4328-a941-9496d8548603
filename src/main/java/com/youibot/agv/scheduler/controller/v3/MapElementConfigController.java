package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MapElementConfig;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MapElementConfigService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("mapElementConfigControllerV3")
@RequestMapping(value = "/api/v3/mapElementConfig", produces = "application/json")
@Api(value = "地图元素配置", tags = "地图元素配置", description = "地图上路网信息的配置")
public class MapElementConfigController extends BaseController {

    @Autowired
    private MapElementConfigService mapElementConfigService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MapElementConfig> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return mapElementConfigService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MapElementConfig> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return mapElementConfigService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "mapElementConfig", value = "地图元素配置", required = true, dataType = "MapElementConfig")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MapElementConfig save(@RequestBody @Valid MapElementConfig mapElementConfig) {
        if (!StringUtils.isEmpty(mapElementConfig.getMapName())) {
            MapElementConfig mapElementConfigDB = mapElementConfigService.selectByMapName(mapElementConfig.getMapName());
            if(!StringUtils.isEmpty(mapElementConfigDB)){
                mapElementConfigDB.setLineWidthHeight(mapElementConfig.getLineWidthHeight());
                mapElementConfigDB.setMWidthHeight(mapElementConfig.getMWidthHeight());
                mapElementConfigService.update(mapElementConfigDB);
                return mapElementConfig;
            }else{
                mapElementConfigService.insert(mapElementConfig);
                return mapElementConfig;
            }
        }
         throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "地图元素配置ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MapElementConfig get(@PathVariable("id") String id) {
        return mapElementConfigService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "地图元素配置ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapElementConfig", value = "地图元素配置", required = true, dataType = "MapElementConfig")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MapElementConfig update(@PathVariable("id") String id, @RequestBody @Valid MapElementConfig mapElementConfig) {
        if (StringUtils.isEmpty(mapElementConfig.getMapName())) {
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        this.mapElementConfigService.update(mapElementConfig);
        return this.mapElementConfigService.selectById(id);
    }


    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "地图元素配置ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.mapElementConfigService.deleteById(id);
    }

    @ApiOperation(value = "根据地图名称查询")
    @ApiImplicitParam(name = "mapName", value = "地图名称", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/getByMapName/{mapName}")
    @ResponseStatus(value = HttpStatus.OK)
    public MapElementConfig getMapElementConfig(@PathVariable("mapName") String mapName) {
        return mapElementConfigService.selectByMapName(mapName);
    }

}
