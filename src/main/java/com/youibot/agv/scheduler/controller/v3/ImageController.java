package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.Base64Utils;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/8/5 17:25
 */
@RestController("imageControllerV3")
@RequestMapping(value = "/api/v3/images", produces = "application/json")
@Api(value = "图片", tags = "图片")
public class ImageController {

    private final static Logger LOGGER = LoggerFactory.getLogger(ImageController.class);

    @ApiOperation(value = "获取图片(文件流格式)", notes = "根据图片路径获取图片")
    @ApiImplicitParam(name = "url", value = "图片url", required = true)
    @GetMapping(value = "/stream")
    @ResponseStatus(value = HttpStatus.OK)
    public void download(HttpServletResponse response, @RequestParam String url) {
        InputStream in = null;
        try {
            File pictureFile = new File(url);
            if (!pictureFile.exists()) {
                throw new ExecuteException(MessageUtils.getMessage("http.image_does_not_exist"));
            }
            in = new FileInputStream(url);
            OutputStream out = response.getOutputStream();
            int len;
            byte[] buffer = new byte[10240];
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            throw new ExecuteException(MessageUtils.getMessage("http.image_acquisition_failed"));
        } finally {
            //关闭资源（response获得的流会自动关闭）
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    LOGGER.warn("inputStream close error", e);
                }
            }
        }
    }

    @ApiOperation(value = "获取图片(base64格式)", notes = "根据图片路径获取图片")
    @ApiImplicitParam(name = "url", value = "图片url", required = true)
    @GetMapping(value = "/base64")
    @ResponseStatus(value = HttpStatus.OK)
    public JSONObject download(@RequestParam String url) {
        JSONObject jsonObject = new JSONObject();
        String base64 = Base64Utils.imageToBase64(url);
        jsonObject.put("image", base64);
        return jsonObject;
    }
    @ApiOperation(value = "获取图片(base64格式)", notes = "根据图片路径获取FTP图片")
    @ApiImplicitParam(name = "url", value = "图片url", required = true)
    @GetMapping(value = "/FTP/base64")
    @ResponseStatus(value = HttpStatus.OK)
    public JSONObject queryImage(@RequestParam String url) throws IOException {
        JSONObject jsonObject = new JSONObject();
        FTPClient ftpClient = FtpUtils.getConnectClient();
        String base64 = Base64Utils.imageToBase64(url,ftpClient);
        jsonObject.put("image", base64);
        FtpUtils.disconnect(ftpClient);
        return jsonObject;
    }

}
