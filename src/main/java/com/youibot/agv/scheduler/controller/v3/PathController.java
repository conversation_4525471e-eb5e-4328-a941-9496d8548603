package com.youibot.agv.scheduler.controller.v3;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.vo.PathVo;
import com.youibot.agv.scheduler.entity.vo.SidePathVo;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.service.SidePathService;
import com.youibot.agv.scheduler.util.MessageUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@RestController("pathControllerV3")
@RequestMapping(value = "/api/v3/paths", produces = "application/json")
@Api(value = "路径", tags = "路径", description = "路径管理接口，路径是指两个标志点中间的连接线路。通过指定路径的方向，会自动生成相应的sidePath数据")
public class PathController extends BaseController {

    @Autowired
    private PathService pathService;

    @Autowired
    private SidePathService sidePathService;

    @ApiOperation(value = "列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvMapName", value = "地图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<PathVo> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap, @RequestParam boolean isDraft) {
        return pathService.searchAll(searchMap, isDraft);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "path", value = "路径", required = true, dataType = "Path")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public PathVo save(@RequestBody @Valid Path path) {
        String startId = path.getStartMarkerId();
        String endId = path.getEndMarkerId();
        String agvMapId = path.getAgvMapName();
        if (StringUtils.isEmpty(startId) || StringUtils.isEmpty(endId) || endId.equals(startId)) {
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        //若存在，判断方向
        //先判断是否已存在path数据
        //若存在，判断方向
        //先判断是否已存在path数据
        List<Path> pathList = pathService.selectByAgvMapIdAndStartIdOrEndId(startId, endId, agvMapId, true);
        //1.查询起始点和终点是否有停靠点，没有：可创建
        if (!CollectionUtils.isEmpty(pathList) && pathList.size() !=1) {
            throw new ExecuteException((MessageUtils.getMessage("http.path_already_exists")));
        }

        //如果路径已有的路径的起始点和我要添加的点是相同的则不允许添加
        if(!CollectionUtils.isEmpty(pathList) && pathList.get(0).getStartMarkerId().equals(path.getStartMarkerId())){
            throw new ExecuteException((MessageUtils.getMessage("http.path_already_exists")));
        }
        //如果自动门ID不为空则检测当前路径的停靠点是否有电梯点或者调整点
        if (!StringUtils.isEmpty(path.getAutoDoorId())) {
            pathService.checkPathNavigationType(path, true);
        }
        this.pathService.insert(path);
        List<SidePathVo> sidePathVos = sidePathService.selectSidePathVosByPathId(path.getId(), path.getAgvMapName(), true);
        PathVo pathVo = new PathVo();
        pathVo.setPath(path);
        pathVo.setSidePathVos(sidePathVos);
        return pathVo;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvMapName", value = "地图名称", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "id", value = "路径ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })

    @GetMapping(value = "{agvMapName}/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public PathVo get(@PathVariable("agvMapName") String agvMapName,@PathVariable("id") String id,@RequestParam boolean isDraft) {
        Path path = pathService.selectById(agvMapName, id, isDraft);
        List<SidePathVo> sidePathVos = sidePathService.selectSidePathVosByPathId(path.getId(), agvMapName, isDraft);
        PathVo pathVo = new PathVo();
        pathVo.setPath(path);
        pathVo.setSidePathVos(sidePathVos);
        return pathVo;
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "路径ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pathVo", value = "路径", required = true, dataType = "PathVo")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public PathVo update(@PathVariable("id") String id, @RequestBody @Valid PathVo pathVo) {
        if(pathVo==null || pathVo.getPath()==null){
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Path path = pathVo.getPath();
        path.setId(id);
        return pathService.update(pathVo);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvMapName", value = "地图名称", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "id", value = "路径ID", paramType = "path", required = true, dataType = "String"),
    })
    @DeleteMapping(value = "/{agvMapName}/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("agvMapName") String agvMapName,@PathVariable("id") String id) {
        this.pathService.delete(agvMapName,id);
    }

}
