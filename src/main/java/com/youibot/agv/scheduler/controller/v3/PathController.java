package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.AdjustAction;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.PathParam;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController("pathControllerV3")
@RequestMapping(value = "/api/v3/paths", produces = "application/json")
@Api(value = "路径", tags = "路径", description = "路径管理接口，路径是指两个标志点中间的连接线路。通过指定路径的方向，会自动生成相应的sidePath数据")
public class PathController extends BaseController {

    @Autowired
    private PathService pathService;

    @Autowired
    private PathParamService pathParamService;

    @Autowired
    private AGVMapService agvMapService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private AdjustActionService adjustActionService;

    private static final Logger LOGGER = LoggerFactory.getLogger(PathController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<Path> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return pathService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<Path> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return pathService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "path", value = "路径", required = true, dataType = "Path")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Path save(@RequestBody @Valid Path path) {
        String startId = path.getStartMarkerId();
        String endId = path.getEndMarkerId();
        String agvMapId = path.getAgvMapName();
        if (StringUtils.isEmpty(startId) || StringUtils.isEmpty(endId) || endId.equals(startId)){
            LOGGER.error("startId or endId params error");
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        //若存在，判断方向
        agvMapService.checkUpdateAllow(path.getAgvMapName());
        //先判断是否已存在path数据
        pathIsExists(path, startId, endId, agvMapId);
        //判断停靠点
        this.pathService.insert(path);
        LOGGER.debug(path.toString());
        return this.pathService.selectById(path.getId());
    }

    private void pathIsExists(@RequestBody @Valid Path path, String startId, String endId, String agvMapId) {
        List<Path> pathList = pathService.selectByAgvMapIdAndStartIdOrEndId(startId, endId, agvMapId);
        //1.查询起始点和终点是否有停靠点，没有：可创建
        if (!CollectionUtils.isEmpty(pathList)){
            if (pathList.size() > 1){//已经有两条数据（正向和反向）
                throw new ExecuteException((MessageUtils.getMessage("http.path_already_exists")));
            }
            //一条数据，若有方向为双向direction：0的数据，不允许创建；若为单向，只允许添加一条反向路径
            Path p = pathList.get(0);
            Integer direction = p.getDirection();
            String startMarkerId = p.getStartMarkerId();
            String endMarkerId = p.getEndMarkerId();
            if (direction == 0){
                throw new ExecuteException((MessageUtils.getMessage("http.path_already_exists")));
            }
            if (startMarkerId.equals(startId) && endMarkerId.equals(endId) && direction.equals(path.getDirection())){//起始点和方向一致
                throw new ExecuteException(MessageUtils.getMessage("http.path_already_exists"));
            }
            if (startMarkerId.equals(endId) && endMarkerId.equals(startId) && !direction.equals(path.getDirection())){////起始点相反和方向相反
                throw new ExecuteException(MessageUtils.getMessage("http.path_already_exists"));
            }
        }
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "路径ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Path get(@PathVariable("id") String id) {
        return pathService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "路径ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "path", value = "路径", required = true, dataType = "Path")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public Path update(@PathVariable("id") String id, @RequestBody @Valid Path path) {
        agvMapService.checkUpdateAllow(path.getAgvMapName());
        path.setId(id);
        this.pathService.update(path);
        return this.pathService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "路径ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        Path path = pathService.selectById(id);
        if (path == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.path_is_null"));
        }
        //判断两点中是否有调整点，有进行删除
        List<AdjustAction> list = adjustActionService.selectByReplaceMarkerId(path.getAgvMapName(), path.getStartMarkerId(), path.getEndMarkerId());
        if (!CollectionUtils.isEmpty(list)){
            adjustActionService.deleteByIds(list.stream().map(AdjustAction::getId).collect(Collectors.toList()));
        }
        agvMapService.checkUpdateAllow(path.getAgvMapName());
        this.pathService.deleteById(id);
    }

    @ApiOperation(value = "查询路径参数")
    @ApiImplicitParam(name = "id", value = "路径ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/pathParam")
    @ResponseStatus(value = HttpStatus.OK)
    public PathParam getByPathId(@PathVariable("id") String id) {
        return pathParamService.selectByPathId(id);
    }
}
