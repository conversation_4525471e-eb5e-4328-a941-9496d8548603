package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.entity.AGVStatistics;
import com.youibot.agv.scheduler.service.AGVStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 机器人统计
 * @Author：yangpeilin
 * @Date: 2020/5/21 16:41
 */
@RestController
@RequestMapping("/api/v3/statistics")
@Api(value = "机器人统计", tags = "机器人统计")
public class AGVStatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(AGVStatisticsController.class);

    @Autowired
    private AGVStatisticsService agvStatisticsService;

    @ApiOperation(value = "分类统计内容", notes = "分类统计内容")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataType", value = "数据类型：天:1，周:7，月:30，所有:0", required = true, dataType = "int")
    })
    @GetMapping("/{agvCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AGVStatistics> listAGVStatistics(@PathVariable("agvCode") String agvCode, @RequestParam(required = false) Map<String, String> searchMap) {
        if (CollectionUtils.isEmpty(searchMap) || !searchMap.containsKey("dataType")){
            //默认为查询 天 单位的数据
            searchMap.put("dataType", String.valueOf(VehicleConstant.DAY));
        }
        searchMap.put("agvCode", agvCode);
        return agvStatisticsService.findPage(searchMap);
    }

    @ApiOperation(value = "统计系统总数据", notes = "统计系统总数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, dataType = "long"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, dataType = "long"),
            @ApiImplicitParam(name = "dataType", value = "数据类型：天:1，周:7，月:30，所有:0", required = true, dataType = "int")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public AGVStatistics getAGVStatistics(@RequestParam(required = false) Map<String, String> searchMap) {
        AGVStatistics agvStatistics = agvStatisticsService.getAGVStatistics(searchMap);
        agvStatistics.setDefaultValue();
        return agvStatistics;
    }
}
