package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.AGVStatistics;
import com.youibot.agv.scheduler.service.AGVStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 机器人统计
 * @Author：yangpeilin
 * @Date: 2020/5/21 16:41
 */
@RestController("AGVStatisticsControllerV3")
@RequestMapping("/api/v3/statistics")
@Api(value = "机器人统计", tags = "机器人统计")
public class AGVStatisticsController {

    @Autowired
    private AGVStatisticsService agvStatisticsService;

	@ApiOperation(value = "统计系统总数据", notes = "统计系统总数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", dataType = "long"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "long"),
            @ApiImplicitParam(name = "dataType", value = "数据类型：天:1，周:7，月:30，所有:0", required = true, dataType = "int"),
            @ApiImplicitParam(name = "agvId", value = "AGV ID", required = true, dataType = "String")
    })
	@GetMapping
	@ResponseStatus(value = HttpStatus.OK)
	public AGVStatistics getAGVStatistics(@RequestParam(required = false) Map<String, String> searchMap) {
        AGVStatistics agvStatistics = agvStatisticsService.getAGVStatistics(searchMap);
        agvStatistics.setDefaultValue();
        return agvStatistics;
    }
}
