package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.service.DownLoadFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

@RestController
@RequestMapping("/api/v3/download")
@Api(value = "下载模板", tags = "用户操作指南下载模板(DownloadController)")
public class DownloadController {

    private static final Logger logger = LoggerFactory.getLogger(AdjustActionController.class);

    @Autowired
    private DownLoadFileService downLoadFileService;

    @Value("${FILE.DOWNLOAD_FILE_PATH.USER_MANUAL}")
    private String userManualFilePath;

    @Value("${FILE.DOWNLOAD_FILE_PATH.API_MANUAL}")
    private String apiManualFilePath;

    @ApiOperation(value = "操作手册下载", notes = "用户点击后下载操作手册")
    @ApiImplicitParams({@ApiImplicitParam(name = "fileType", value = "下载类型(1:用户手册下载，2:API操作手册下载)", paramType = "path", required = true, dataType = "Integer")})
    @GetMapping("/file/{fileType}")
    public void manualDownLoad(@PathVariable("fileType") Integer fileType, HttpServletResponse response) throws UnsupportedEncodingException {
        File file=null;
        response.setCharacterEncoding("UTF-8");
        if(fileType==1){
            file= new File(userManualFilePath);
        }else{
            file= new File(apiManualFilePath);
        }
        downLoadFileService.downloadFile(file,response);


    }

}
