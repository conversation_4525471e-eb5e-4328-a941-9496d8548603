package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.entity.Sensor;
import com.youibot.agv.scheduler.service.SensorService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @author: Tianshu.CHU
 * @Date: 2021/4/13 15:38
 * @Description:
 */
@RequestMapping("/api/v3/sensors")
@RestController
public class SensorController extends BaseController {


    @Autowired
    private SensorService sensorService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query"),
    })
    @GetMapping("/page")
    @ResponseStatus(HttpStatus.OK)
    public PageInfo<Sensor> findByPage(@RequestParam(required = false) Map<String, String> searchMap) {
        return sensorService.findPage(searchMap);
    }

    @ApiOperation(value = "查询所有")
    @GetMapping
    @ResponseStatus(HttpStatus.OK)
    public List<Sensor> findAll(@RequestParam Map<String, String> searchMap) {
        return sensorService.searchAll(searchMap);
    }

    @LogOperation
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "sensor", value = "传感器", required = true, dataType = "Sensor")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public String createSensor(@RequestBody @Valid Sensor sensor) {
       return sensorService.createSensor(sensor);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "传感器ID", paramType = "path", required = true, dataType = "String")
    @GetMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public Sensor detailById(@PathVariable String id) {
        return sensorService.detailById(id);
    }

    @ApiOperation(value = "修改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "传感器ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "sensor", value = "Sensor", required = true, dataType = "Sensor")
    })
    @PutMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public void updateById(@PathVariable String id, @RequestBody Sensor sensor) {
        sensor.setId(id);
        sensorService.updateSensor(sensor);
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/{id}")
    public void deleted(@PathVariable String id) {
        sensorService.deleteById(id);
    }

}
