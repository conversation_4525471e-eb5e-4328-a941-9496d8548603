package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionActionParameter;
import com.youibot.agv.scheduler.service.MissionActionParameterService;
import com.youibot.agv.scheduler.service.MissionActionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("missionActionControllerV3")
@RequestMapping(value = "/api/v3/missionActions", produces = "application/json")
@Api(value = "预设动作", tags = "预设动作", description = "预设动作是预设任务的最重要组成部分，通常一个预设任务中会有很多个预设动作。")
public class MissionActionController extends BaseController {

    @Autowired
    private MissionActionService missionActionService;

    @Autowired
    private MissionActionParameterService missionActionParameterService;


    private static final Logger LOGGER = LoggerFactory.getLogger(MissionActionController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionAction> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionActionService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionAction> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionActionService.searchAll(searchMap);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "missionAction", value = "预设动作", required = true, dataType = "MissionAction")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionAction save(@RequestBody @Valid MissionAction missionAction) {
        this.missionActionService.insert(missionAction);
        return missionAction;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "预设动作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionAction get(@PathVariable("id") String id) {
        return missionActionService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "预设动作ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionAction", value = "预设动作", required = true, dataType = "MissionAction")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionAction update(@PathVariable("id") String id, @RequestBody @Valid MissionAction missionAction) {
        missionAction.setId(id);
        missionActionService.update(missionAction);
        return this.missionActionService.selectById(id);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "预设动作ID", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        this.missionActionService.deleteById(id);
    }

    @ApiOperation(value = "根据预设动作ID查询参数列表")
    @ApiImplicitParam(name = "id", value = "动作ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}/missionActionParameters")
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionActionParameter> getMissionActionParameters(@PathVariable("id") String id) {
        MissionActionParameter missionActionParameter = new MissionActionParameter();
        missionActionParameter.setMissionActionId(id);
        return missionActionParameterService.findByEntity(missionActionParameter);
    }
}
