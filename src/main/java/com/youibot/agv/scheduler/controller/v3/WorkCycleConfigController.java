package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.entity.WorkCycleConfig;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.service.WorkCycleConfigService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/v3/workCycleConfig", produces = "application/json")
@Api(value = "作业调度", tags = "工作站配置", description = "巡线工作站配置接口")
public class WorkCycleConfigController extends BaseController {

	@Autowired
	private WorkCycleConfigService workCycleConfig;

	@SuppressWarnings("unused")
	private static final Logger LOGGER = LoggerFactory.getLogger(WorkCycleConfigController.class);

	@ApiOperation(value = "分页查询")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query") })
	@GetMapping("/page")
	@ResponseStatus(value = HttpStatus.OK)
	public PageInfo<WorkCycleConfig> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
		searchMap.put("sort","agv_group_id asc, seq asc") ;
		return workCycleConfig.findPage(searchMap);
	}

	@ApiOperation(value = "列表")
	@GetMapping
	@ResponseStatus(value = HttpStatus.OK)
	public List<WorkCycleConfig> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
		return workCycleConfig.searchAll(searchMap);
	}

	@ApiOperation(value = "根据工作站id查询详情")
	@ApiImplicitParam(name = "id", value = "工作站id", paramType = "path", required = true, dataType = "String")
	@GetMapping("/{id}")
	@ResponseStatus(value = HttpStatus.OK)
	public WorkCycleConfig getById(@PathVariable("id") String id) {
		return workCycleConfig.selectById(id);
	}
	
	@ApiOperation(value = "删除")
	@ApiImplicitParam(name = "id", value = "工作站id", paramType = "path", required = true, dataType = "String")
	@DeleteMapping(value = "/{id}")
	@ResponseStatus(value = HttpStatus.NO_CONTENT)
	public void delete(@PathVariable("id") String id) {

		workCycleConfig.deleteById(id);
		clearStation(id);
		workCycleConfig.refresh() ;
	}

	private static void clearStation(String id) {

		try {
			TjdCxt.getCycle().remove(id);
			TjdCxt.STATION_COUNT.remove(id) ;
			TjdCxt.removeStationCountByStation( id);
		} catch (Exception e) {

		}
	}

	@ApiOperation(value = "更新工作站")
	@ApiImplicitParam(name = "id", value = "更新工作站", paramType = "path", required = true, dataType = "String")
	@PutMapping(value = "/{id}")
	@ResponseStatus(value = HttpStatus.OK)
	public WorkCycleConfig update(@PathVariable("id") String id, @RequestBody WorkCycleConfig param) {
		if (StringUtils.isBlank(id) || Objects.isNull(param)) {
			throw new YOUIFleetException(MessageUtils.getMessage("service.id_param_cant_be_null"));
		}
		List<WorkCycleConfig> existList = getExistList(param);
		existList = existList.parallelStream().filter(p -> !StringUtils.equals(p.getId(), id))
				.collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(existList)) {
			throw new YOUIFleetException(MessageUtils.getMessage("service.update_name_seq_duplicate"));
		}
		WorkCycleConfig config = updateData(id, param);
		return config;
	}

	private WorkCycleConfig updateData(String id, WorkCycleConfig param) {
		workCycleConfig.updateByPrimaryKeySelective(param);
		WorkCycleConfig config = workCycleConfig.selectById(id);
		TjdCxt.getCycle().put( config.getId(), config);
		return config;
	}

	
	@ApiOperation(value = "启用禁用工作站")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "id", value = "工作站Id", paramType = "path", required = true, dataType = "String"),
		@ApiImplicitParam(name = "enable", value = "启用禁用工作站", paramType = "path", required = true, dataType = "String")
	})
	@PutMapping(value = "/{id}/update/{enable}")
	@ResponseStatus(value = HttpStatus.OK)
	public WorkCycleConfig updateEnabled(@PathVariable("id") String id, @PathVariable("enable") Boolean enable) {
		WorkCycleConfig param = new WorkCycleConfig();
		param.setId(id);
		param.setEnabled(enable);
		return updateData(id, param);
		
	}
	// 创建工作站
	@ApiOperation(value = "创建")
	@ApiImplicitParam(name = "workCycleConfig", value = "工作站", required = true, dataType = "WorkCycleConfig")
	@PostMapping
	@ResponseStatus(value = HttpStatus.CREATED)
	public WorkCycleConfig save(@RequestBody @Valid WorkCycleConfig param) {
		
	
		List<WorkCycleConfig> list = getExistList(param);
		if (CollectionUtils.isNotEmpty(list)) {
			Integer countByExample = workCycleConfig.selectCountByExample(getIdCriteria(param));
			String tip ="";
			if( countByExample > 0) {
				tip = param.getId() +MessageUtils.getMessage("_occupied");
			}
			Example ex = getIdCriteria(param);
			ex.clear();
			
			Criteria stationCriteria = mapStationCriteria(param , ex) ;
			
			ex.and(stationCriteria);
			 countByExample = workCycleConfig.selectCountByExample(ex);
			 if( countByExample > 0) {
					tip += param.getStationName() +MessageUtils.getMessage("_same_map_station_name_occupied");
				}
			 ex.clear();
			 Criteria codeCriteria = markCodeCriteria(param, ex);
			 ex.and(codeCriteria);
			 countByExample = workCycleConfig.selectCountByExample(ex);
			 if( countByExample > 0) {
					tip += param.getStationName() +MessageUtils.getMessage("_same_map_seq_occupied");
				}
			throw new YOUIFleetException(MessageUtils.getMessage("service.update_name_seq_duplicate") + tip);
		}
		workCycleConfig.insert(param);
		workCycleConfig.refresh() ;
		return param;
	}

	private void valid(@Valid WorkCycleConfig param) {

        if(StringUtils.isAnyBlank(param.getId(), param.getAgvMapId(),param.getStationName()) || Objects.isNull(param.getSeq())) {
        	throw new YOUIFleetException(MessageUtils.getMessage("service.id_station_seq_cant_be_null"));
        }
		
	}

	/**
	 * 查询db中已经存在的记录
	 * 
	 * @param param
	 * @return
	 */
	private List<WorkCycleConfig> getExistList(WorkCycleConfig param) {
		valid(param) ;
		Example example = getIdCriteria(param);

		Criteria criteria1 = mapStationCriteria(param, example);

		Criteria criteria2 = markCodeCriteria(param, example);
		
	

//		example.or(criteria);
		example.or(criteria1);
		example.or(criteria2);

		return workCycleConfig.selectByExample(example);
	}

	private Criteria markCodeCriteria(WorkCycleConfig param, Example example) {
		Criteria criteria2 = example.createCriteria().andEqualTo("markCode", param.getMarkCode())
				.andEqualTo("agvMapId", param.getAgvMapId()).andEqualTo("seq", param.getStationName());
		return criteria2;
	}

	private Criteria mapStationCriteria(WorkCycleConfig param, Example example) {
		Criteria criteria1 = example.createCriteria().andEqualTo("agvMapId", param.getAgvMapId())
				.andEqualTo("stationName", param.getStationName());
		return criteria1;
	}

	private Example getIdCriteria(WorkCycleConfig param) {
		Example example = new Example(WorkCycleConfig.class);
		
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("id",param.getId());
		return example;
	}
}
