package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.entity.MapArea;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.entity.PageInfo;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.MapAreaService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController("mapAreaControllerV3")
@RequestMapping(value = "/api/v3/mapAreas", produces = "application/json")
@Api(value = "地图区域", tags = "地图区域", description = "区域管理，用户可以通过接口管理区域信息。")
public class MapAreaController extends BaseController {

    @Autowired
    private MapAreaService mapAreaService;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MapArea> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap, Boolean isDraft) {
        return mapAreaService.findPage(searchMap, isDraft);
    }

    @ApiOperation(value = "列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvMapName", value = "地图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MapArea> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap, Boolean isDraft) {
        return mapAreaService.searchAll(searchMap, isDraft);
    }

    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "mapArea", value = "地图区域", required = true, dataType = "MapArea")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MapArea save(@RequestBody @Valid MapArea mapArea) {
        this.mapAreaService.insert(mapArea);
        return mapArea;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "id", value = "地图区域ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapName", value = "地图名称", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MapArea get(@PathVariable("id") String id, String mapName, Boolean isDraft) {
        return mapAreaService.selectById(mapName, id, isDraft);
    }


    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "地图区域ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapArea", value = "地图区域", required = true, dataType = "MapArea")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MapArea update(@PathVariable("id") String id, @RequestBody @Valid MapArea mapArea) {
        mapArea.setId(id);
        return mapAreaService.update(mapArea);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "地图区域ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapName", value = "地图名称", required = true, dataType = "String", paramType = "query")
    })
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public int delete(@PathVariable("id") String id, String mapName) {
        MapArea mapArea = mapAreaService.selectById(mapName, id, true);
        if (mapArea == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.map_area_is_null"));
        }
        return this.mapAreaService.deleteById(mapName, id);
    }

}
