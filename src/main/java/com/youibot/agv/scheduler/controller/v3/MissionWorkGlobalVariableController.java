package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;
import com.youibot.agv.scheduler.service.MissionWorkGlobalVariableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController("missionWorkGlobalVariableControllerV3")
@RequestMapping(value = "/api/v3/missionWorkGlobalVariables", produces = "application/json")
@Api(value = "任务全局变量", tags = "任务全局变量", description = "任务全局变量，在任务执行的动作里可根据全局变量的动态变化实现不同动作的效果。")
public class MissionWorkGlobalVariableController extends BaseController {

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkGlobalVariableController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<MissionWorkGlobalVariable> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkGlobalVariableService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<MissionWorkGlobalVariable> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return missionWorkGlobalVariableService.searchAll(searchMap);
    }

    @ApiIgnore
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "missionWorkGlobalVariable", value = "工作全局变量", required = true, dataType = "MissionWorkGlobalVariable")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public MissionWorkGlobalVariable save(@RequestBody @Valid MissionWorkGlobalVariable missionWorkGlobalVariable) {
        this.missionWorkGlobalVariableService.insert(missionWorkGlobalVariable);
        LOGGER.debug(missionWorkGlobalVariable.toString());
        return missionWorkGlobalVariable;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "工作全局变量ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWorkGlobalVariable get(@PathVariable("id") String id) {
        return missionWorkGlobalVariableService.selectById(id);
    }

    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "小视图ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "missionWorkGlobalVariable", value = "小视图", required = true, dataType = "MissionWorkGlobalVariable")})
    @PutMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    public MissionWorkGlobalVariable update(@PathVariable("id") String id, @RequestBody @Valid MissionWorkGlobalVariable missionWorkGlobalVariable) {
        missionWorkGlobalVariable.setId(id);
        this.missionWorkGlobalVariableService.update(missionWorkGlobalVariable);
        return this.missionWorkGlobalVariableService.selectById(id);
    }

    @ApiOperation(value = "批量更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "missionWorkGlobalVariables", value = "工作全局变量集合", required = true, dataType = "MissionWorkGlobalVariable", allowMultiple = true)})
    @PutMapping("/batch")
    @ResponseStatus(value = HttpStatus.OK)
    public void batchUpdate(@RequestBody @Valid MissionWorkGlobalVariable[] missionWorkGlobalVariables) {
        List<MissionWorkGlobalVariable> variables = Arrays.asList(missionWorkGlobalVariables);
        missionWorkGlobalVariableService.batchUpdate(variables);
    }
}
