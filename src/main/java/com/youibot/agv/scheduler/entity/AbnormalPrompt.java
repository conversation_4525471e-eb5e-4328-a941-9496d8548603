package com.youibot.agv.scheduler.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "abnormal_prompt")
@ApiModel(value = "AbnormalPrompt", description = "异常信息")
public class AbnormalPrompt {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "select uuid()")
    @ApiModelProperty(value = "ID")
    private String id;

    @Column
    @ApiModelProperty(value = "异常等级 1：普通 2：警告 3：错误", position = 2)
    private Integer abnormalLevel;

    @Column
    @ApiModelProperty(value = "异常类型", position = 3)
    private String abnormalType;

    @Column
    @ApiModelProperty(value = "异常编码", position = 4)
    private Integer abnormalCode;

    @Column
    @ApiModelProperty(value = "异常描述", position = 5)
    private String abnormalDescription;

    @Column
    @ApiModelProperty(value = "异常处理建议", position = 6)
    private String help;

    @Column
    @ApiModelProperty(value = "创建时间", position = 7)
    private Date createTime;

    @Column
    @ApiModelProperty(value = "更新时间", position = 8)
    private Date updateTime;
}
