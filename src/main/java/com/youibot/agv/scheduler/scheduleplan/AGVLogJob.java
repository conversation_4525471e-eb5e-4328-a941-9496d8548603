package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.entity.AGVLog;
import com.youibot.agv.scheduler.service.AGVLogService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.youibot.agv.scheduler.constant.AGVConstant.AGV_LOG_TYPE_CHARGE;
import static com.youibot.agv.scheduler.constant.AGVConstant.AGV_LOG_TYPE_ON_LINE;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/6/2 16:40
 */
@Component
public class AGVLogJob {

    @Autowired
    private AGVLogService agvLogService;

    // 每天凌晨0点执行一次
    @Scheduled(cron = "0 0 0 * * ?")
    public void secondDayAGVLog() {
        Vehicle vehicle = VehicleUtils.getVehicle();
        AGVLog onLineLog = vehicle.getOnLineLog();
        if (onLineLog != null) {
            //避免因为程序处理耗时导致结束时间处在第二天
            Long onlineEndTime = (new Date().getTime() / 1000) - 5;
            onLineLog.setEndTime(onlineEndTime);
            agvLogService.update(onLineLog);
            vehicle.setOnLineLog(null);
        }

        //创建新的在线日志
        onLineLog = new AGVLog(AGV_LOG_TYPE_ON_LINE, new Date().getTime() / 1000);
        agvLogService.insert(onLineLog);
        vehicle.setOnLineLog(onLineLog);

        AGVLog chargeLog = vehicle.getChargeLog();
        if (chargeLog != null) {
            //避免因为程序处理耗时导致结束时间处在第二天
            Long chargeEndTime = (new Date().getTime() / 1000) - 5;
            chargeLog.setEndTime(chargeEndTime);
            agvLogService.update(chargeLog);
            vehicle.setChargeLog(null);

            //创建新的充电日志
            chargeLog = new AGVLog(AGV_LOG_TYPE_CHARGE, new Date().getTime() / 1000);
            agvLogService.insert(chargeLog);
            vehicle.setChargeLog(chargeLog);
        }
    }

    @Scheduled(cron = "0 0/10 * * * ?")
    public void updateEndTime() {
        Vehicle vehicle = VehicleUtils.getVehicle();
        AGVLog onLineLog = vehicle.getOnLineLog();
        if (onLineLog != null) {
            onLineLog.setEndTime(new Date().getTime() / 1000);
            agvLogService.update(onLineLog);
        }
    }

}
