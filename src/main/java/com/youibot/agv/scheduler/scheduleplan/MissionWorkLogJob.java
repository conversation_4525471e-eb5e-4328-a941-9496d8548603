package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 作业过期/系统通知数据定时删除任务
 * @Author：yangpeilin
 * @Date: 2020/5/19 17:40
 */
@Component
public class MissionWorkLogJob {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionWorkActionParameterService missionWorkActionParameterService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private MqMessageService mqMessageService;

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private ParkSchedulerService parkSchedulerService;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;

    @Autowired
    private AGVLogService agvLogService;

    @Autowired
    private AGVMapUpdateQueueService agvMapUpdateQueueService;


    // 每天凌晨1点执行一次
    @Scheduled(cron = "0 0 3 * * ?")
//    @Scheduled(cron = "0 0/5 * * * ?")
    public void deleteMissionWorkLogs(){
        SystemConfigJobUtil.deleteMissionWorkLogs(systemConfigService, missionWorkService,
                missionWorkActionService, missionWorkActionParameterService,
                notificationService, mqMessageService, missionWorkGlobalVariableService,
                workSchedulerService, parkSchedulerService, chargeSchedulerService,
                agvLogService, agvMapUpdateQueueService);
    }
}
