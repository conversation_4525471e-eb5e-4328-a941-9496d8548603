package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.service.SystemConfigService;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;

/**
 * 定时删除过期日志job
 * @Author：yangpeilin
 * @Date: 2020/5/11 10:18
 */
@Component
public class LogFileScheduleJob {

    Logger logger = LoggerFactory.getLogger(LogFileScheduleJob.class);

    @Autowired
    private SystemConfigService systemConfigService;

    // 每天执行凌晨一点执行一次
    @Scheduled(cron = "0 0 2 * * ?")
    public void excuteUpdateLicenseUpdateTime(){
        logger.info("----------into logFile job-----------");
        try {
            SystemConfigJobUtil.deleteLogFile(systemConfigService);
        } catch (ParseException e) {
            logger.error("delete job fail:{}", e);
        }
        logger.info("----------end logFile job-----------");
    }
}
