package com.youibot.agv.scheduler.scheduleplan.local;

import java.text.SimpleDateFormat;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shimizukenta.secs.SecsException;
import com.shimizukenta.secs.ext.config.AbstractSecsMsgListener;
import com.shimizukenta.secs.secs2.Secs2;
import com.shimizukenta.secs.secs2.Secs2List;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.constant.vo.TjdSensorDto;
import com.youibot.agv.scheduler.constant.vo.TjdSensorDto.SmokeSensor;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage.ReportType;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.BatteryStatus;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> 台积电 车厢内部的温湿度数据上报
 */
@Slf4j
@DisallowConcurrentExecution
@Component
public class TempSensorReportJob extends AbstractSecsMsgListener implements Job {

	SimpleDateFormat sdf = new SimpleDateFormat("", Locale.SIMPLIFIED_CHINESE);

	{
		sdf.applyPattern("yyyy年MM月dd日 HH时mm分ss秒");
	}

	@Autowired
	private VehiclePoolService vehiclePoolService;

	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {

		
		  String format = sdf.format(System.currentTimeMillis());
		  log.debug("eventId:{},dynamic_report_battery 车内传感器周期上报:{}",ReportType.INNER_SENSOR.getEventId(), format);
		 

		report(context);
	}

//	String data = "{\"X\": \"0.98\",\"Y\": \"-0.07\",\"Z\": \"-0.18\",\"smkdataList\": [{\"SMK\": \"0.00\",\"T\": \"0.00\",\"H\": \"0.00\"},{\"SMK\": \"0.00\",\"T\": \"0.00\",\"H\": \"0.00\"},{\"SMK\": \"0.00\",\"T\": \"0.00\",\"H\": \"0.00\"}]}";

	// 每次间隔10分钟 上报agv电量
//	@Scheduled(fixedRate = 1000 * 60 * 10, initialDelay = 1000 * 60 * 2)
//	    @Scheduled(cron = "0 0/5 * * * ?")
	public void report(JobExecutionContext context) {

		List<Vehicle> waitForData = waitForData();

		if (CollectionUtils.isEmpty(waitForData)) {
			return;
		}
		try {
			List<Secs2List> agvsToData = agvsToData( waitForData);
			if(CollectionUtils.isEmpty(agvsToData)) {
				
				return ;
			}
			hsmsSsCommunicator.send( 6, 11 , false, Secs2.list(
					/**
					 * agv到达工作站事件别名
					 */
					Secs2.ascii( ReportType.INNER_SENSOR.getAlias()),
					/**
					 * 事件id
					 */
					Secs2.ascii( ReportType.INNER_SENSOR.getEventId()),
					/**
					 * DATA
					 */
					Secs2.list(
							agvsToData

					)

			))

			;
		} catch (SecsException | InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
	}

	public List<Secs2List> agvsToData(List<Vehicle> ves) {
		
		List<Secs2List> collect = ves.parallelStream().filter( item ->{
			
			DefaultVehicleStatus defaultVehicleStatus = item.getDefaultVehicleStatus();
			if (Objects.isNull(defaultVehicleStatus)) {
				log.error("agv_battery_is_null=>>{}", item.getId());
				return false;
			}
			TjdSensorDto sensorDto = item.getTjdSensorDto();

//			 sensorDto = JSON.parseObject(data , TjdSensorDto.class);
			if (Objects.isNull(sensorDto)) {
				log.debug("agvId:{},sensorDto_is_null", item.getId());
				return false;
			}

			if (Objects.isNull(sensorDto.getSmkdataList()) || sensorDto.getSmkdataList().size() < 3) {
				log.debug("agvId:{},sensorDto_is_invalid:{}", item.getId(), JSON.toJSONString(sensorDto));
				return false;
			}
			return true;
		}
				).map(i -> agvToData(i)).collect(Collectors.toList());
		
		return collect;
	};
	
	
	public Secs2List agvToData(Vehicle item) {
		TjdSensorDto sensorDto = item.getTjdSensorDto();
		List<SmokeSensor> smkdataList = sensorDto.getSmkdataList();
		List<Secs2List>  res = Lists.newArrayList();
		for (int i= 0 ;i < smkdataList.size();i++) {
			
			SmokeSensor smokeSensor = smkdataList.get( i );
			int j = i+1;
			res.add( Secs2.list(

							Secs2.ascii("SMK" + j),
							Secs2.ascii( smokeSensor.getSMK())));
			res.add(	Secs2.list(

					Secs2.ascii("T" + j),
					Secs2.ascii( smokeSensor.getT())

			));
			res.add(	Secs2.list(

					Secs2.ascii("H"+ j),
					Secs2.ascii( smokeSensor.getH())

			));
		
				

			
			
		}
		
	
		res.add( Secs2.list(Secs2.ascii("SPEED_X"), Secs2.ascii(Objects.toString(sensorDto.getX())
				
				)));
		res.add(	Secs2.list(Secs2.ascii("SPEED_Y"), Secs2.ascii(Objects.toString(sensorDto.getY())

				)));
		res.add( Secs2.list(Secs2.ascii("SPEED_Z"), Secs2.ascii(Objects.toString(sensorDto.getZ()))));
		
		
		return Secs2.list(
		/**
		 * agv所对应的code
		 */
		Secs2.list(Secs2.ascii("AgvID"), Secs2.ascii(item.getId()

		)),

		/**
		 * 对应的电子货架数据
		 */
		Secs2.list( res)

		)

;
	}

	/**
	 * 等待车辆数据
	 * 
	 * @param all
	 */
	private List<Vehicle> waitForData() {
		List<Vehicle> all = vehiclePoolService.selectAll();
		all = all.parallelStream()
				.filter(p -> Objects.nonNull(p) && Objects.nonNull(p.getDefaultVehicleStatus())
						&& Objects.nonNull(p.getDefaultVehicleStatus().getBattery())
						&& p.getOnlineStatus() == VehicleConstant.ONLINE)
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(all)) {
			try {
				TimeUnit.SECONDS.sleep(5);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}

		return all;
	}

}
