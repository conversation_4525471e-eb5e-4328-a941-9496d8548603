package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.service.SystemConfigService;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;

@Slf4j
@Component
public class AgvMapJob {

    Logger logger = LoggerFactory.getLogger(AgvMapJob.class);


    // 每天凌晨0点执行一次
    @Scheduled(cron = "0 0 0 * * ?")
    public void executeAgvMapJob() {
        logger.info("----------into agvMap job-----------");
        try {
            SystemConfigJobUtil.deleteMapFile();
        } catch (Exception e) {
            logger.error("delete agvMapJob fail:{}", e);
        }
        logger.info("----------end agvMap job-----------");
    }

}
