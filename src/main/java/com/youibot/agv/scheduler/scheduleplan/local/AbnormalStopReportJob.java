package com.youibot.agv.scheduler.scheduleplan.local;

import com.google.common.collect.Sets;
import com.shimizukenta.secs.SecsException;
import com.shimizukenta.secs.ext.config.AbstractSecsMsgListener;
import com.shimizukenta.secs.secs2.Secs2;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.constant.enums.ErrorEnum;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage.ReportType;
import com.youibot.agv.scheduler.service.AbnormalPromptService;
import com.youibot.agv.scheduler.service.NotificationService;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.DecStatus;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.EmecStatus;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.StopStatus;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 异常停止次数上报
 */
@Slf4j
@DisallowConcurrentExecution
@Component
public class AbnormalStopReportJob extends AbstractSecsMsgListener  implements Job {

	SimpleDateFormat sdf = new SimpleDateFormat("", Locale.SIMPLIFIED_CHINESE);
	{
		sdf.applyPattern("yyyy年MM月dd日 HH时mm分ss秒");
	}
	
	private static AbnormalPrompt abnormalByCode;

	@Autowired
	private NotificationService notificationService;
	
	@Autowired
	private VehiclePoolService vehiclePoolService;
	
	@Autowired
	private AbnormalPromptService abnormalPromptService;
	

	
	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		
		
		String format = sdf.format(System.currentTimeMillis());
		log.debug("eventId:{},abnormal_stop_check_count 无故异常停止间隔上报:{}" ,ReportType.ABNORMAL_STOP_CHECK.getEventId(), format );
		
		 reportAbnormalStop(context);
	}

	// 每次间隔10两秒 累计检测到8次 速度为零且非正在申请路径，且非工作站停止
//	@Scheduled(fixedRate = 1000 * 2, initialDelay = 1000 * 60 * 2)
//    @Scheduled(cron = "0 0/5 * * * ?")
	public void reportAbnormalStop(JobExecutionContext context) {

		List<Vehicle> waitForData = waitForData( context );
	
		if (CollectionUtils.isEmpty(waitForData)) {
			return;
		}
		if (Objects.isNull(abnormalByCode)) {
			log.error("异常停止15 秒 未配置_abnormal_stop_check_count_configured");
			init();
			if( Objects.isNull(abnormalByCode)) {
				return ;
			}
		}
		waitForData.parallelStream().forEach(item -> {

			boolean shoudlReport = TjdCxt.shoudlReport(item);
			if (!shoudlReport) {
				item.resetReport();
				return;
			}
			if(item.isUseElevator()){
				log.debug("agvId:{} 正在使用电梯，不上报异常停止", item.getId());
				return;
			}
			boolean reportNow = reportNow(item, shoudlReport);
			if (!reportNow) {
				return;
			}
			String position = TjdCxt.getHelper().getPosition(item.getId());
			log.debug("dynamic_report_abnormal_stop，agvId:{}", item.getId());
			
			DefaultVehicleStatus defaultVehicleStatus = item.getDefaultVehicleStatus();
			if( Objects.isNull(defaultVehicleStatus)) {
				return ;
			}
			StopStatus stop = defaultVehicleStatus.getStop();
			EmecStatus emec = defaultVehicleStatus.getEmec();
			
			boolean nonNull = Objects.nonNull(stop);
			boolean emecStatusNoNull = Objects.nonNull( emec);
			
			String stopReason =nonNull && !ArrayUtils.isEmpty( stop.getStop_reason()) ?  StringUtils.join( CommonUtils.Arrays.asList( stop.getStop_reason())," ") : StringUtils.EMPTY;
     		String stopStatus = nonNull ? Objects.toString( stop.getStop_status()) : StringUtils.EMPTY ;
     		String emecReason = emecStatusNoNull && !ArrayUtils.isEmpty( emec.getEmc_reason()) ?  StringUtils.join( CommonUtils.Arrays.asList( emec.getEmc_reason())," ") : StringUtils.EMPTY;
     		String emecStatus = emecStatusNoNull ? Objects.toString( emec.getEmc_status()) : StringUtils.EMPTY ;
     		String workId = item.getMissionWorkId();
			try {
				
				
				hsmsSsCommunicator.send( 6, 11, false, Secs2.list(
						/**
						 * agv到达工作站事件别名
						 */
						Secs2.ascii(ReportType.ABNORMAL_STOP_CHECK.getAlias()),
						/**
						 * 事件id
						 */
						Secs2.ascii(ReportType.ABNORMAL_STOP_CHECK.getEventId()),
						/**
						 * DATA
						 */
						Secs2.list(
								/**
								 * agv所对应的code
								 */
								Secs2.list(Secs2.ascii("AgvID"), Secs2.ascii(item.getId()

								)), Secs2.list(

										Secs2.ascii("desc"), Secs2.ascii(abnormalByCode.getEnDesc())),

								Secs2.list(

										Secs2.ascii("solution"), Secs2.ascii(abnormalByCode.getEnSlolution())),
								Secs2.list(

										Secs2.ascii("position"), Secs2.ascii(position)) ,
								
								Secs2.list(
										
										Secs2.ascii("stop_status"), Secs2.ascii( stopStatus)) ,
								
							
								Secs2.list(

										Secs2.ascii("stop_reason"), Secs2.ascii( stopReason ) ) ,
							
                            Secs2.list(
										
										Secs2.ascii("emc_status"), Secs2.ascii( emecStatus )) ,
								
							
								Secs2.list(

										Secs2.ascii("emc_reason"), Secs2.ascii( emecReason ) )  ,
								
								Secs2.list(

										Secs2.ascii("current_mission"), Secs2.ascii( Objects.toString( workId) ) ) 
						)

				));

			} catch (SecsException | InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} finally {
				if( reportNow) {
					
					notificationService.sendMessage(ErrorEnum.ABNORMAL_STOP_CHECK.code(), item.getId(), getMoreDesc(item) ,  position);

				}
			}

		});

	}

	/** 异常停止原因
	 * @param ve
	 * @return
	 */
	private String getMoreDesc( Vehicle ve ) {
		
		DefaultVehicleStatus defaultVehicleStatus = ve.getDefaultVehicleStatus();
		StopStatus stop = defaultVehicleStatus.getStop();
		DecStatus decStatus = defaultVehicleStatus.getDece();
		EmecStatus emecStatus = defaultVehicleStatus.getEmec();
		  Set<Object> set = Sets.newConcurrentHashSet();
		if( Objects.nonNull(stop) && ArrayUtils.isNotEmpty( stop.getStop_reason())) {
			for (Integer stopItem : stop.getStop_reason()) {
				String string = StopStatus.STOP.get( String.valueOf(stopItem));
				set.add(string);
			}
		}
		if( Objects.nonNull(emecStatus) && ArrayUtils.isNotEmpty( emecStatus.getEmc_reason())) {
			for (Integer stopItem :  emecStatus.getEmc_reason()) {
				String string = EmecStatus.EMC.get( String.valueOf(stopItem));
				set.add(string);
			}
		}
		
		if( Objects.nonNull(stop) && ArrayUtils.isNotEmpty( decStatus.getDece_reason())) {
			for (Integer stopItem :  decStatus.getDece_reason()) {
				String string = DecStatus.DEC.get( String.valueOf(stopItem));
				set.add(string);
			}
		}
		
		if( Objects.nonNull( ve.getWorkStatus()) && ve.getWorkStatus() != VehicleConstant.ABNORMAL_STATUS_NO) {
			String string = VehicleConstant.ABNORMAL_STATUS.get( String.valueOf(ve.getWorkStatus()));
			if(StringUtils.isNotBlank(string)) {
				set.add( string );
			}
			
		}
		
		return StringUtils.join( set , ";");
	}
	/**
	 * 是否应该立即上报异常
	 * 
	 * @param item
	 * @param shoudlReport
	 * @return
	 */
	private boolean reportNow(Vehicle item, boolean shoudlReport) {
		boolean reportNow = false ;
		try {
			if (shoudlReport) {
				item.decreaceReport();
			} else {
				item.resetReport();
			}
			reportNow = item.getStopCheckTimes() <= 0;
		} finally {
			if(reportNow) {
				item.resetReport();
			}
		}
		return reportNow ;
	}

	

	/**
	 * 等待车辆数据
	 * 
	 * @param all
	 */
	private List<Vehicle> waitForData( JobExecutionContext context) {
		List<Vehicle> all = vehiclePoolService.selectAll();
		List<Vehicle> result = all.parallelStream().filter(p -> Objects.nonNull(p) && Objects.nonNull(p.getControlMode())
				&& StringUtils.isNotBlank(p.getAgvMapId()) && VehicleConstant.AUTO_CONTROL_MODE.equals( p.getControlMode())
				&& !TjdCxt.isCharge(p)
				&& !TjdCxt.isWaiting(p)
				&& BooleanUtils.toBoolean( p.isAutoAllocation() )
			    && ( BooleanUtils.toBoolean(p.getLinePatrolMode()) || BooleanUtils.toBoolean(p.getDummy()) )).collect(Collectors.toList());
		 JobDataMap map = context.getMergedJobDataMap();
		 int defaultReportCount = map.getIntValue( TjdCxt.AGV_STOP_CHECK_TIMES_MAP_KEY );
		 if(defaultReportCount < TjdCxt.AGV_STOP_CHECK_TIMES) {
			 defaultReportCount = TjdCxt.AGV_STOP_CHECK_TIMES ;
		 }
		if (CollectionUtils.isEmpty(result)) {
			try {
				TimeUnit.SECONDS.sleep(5);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}
		final int stopCount = defaultReportCount ;
		Set<String> collect = result.parallelStream().map(i -> i.getId()).collect(Collectors.toSet());
       //** 恢复默认次数
		all.parallelStream().forEach( item ->{
			if(!collect.contains(item.getId())) {
				 item.setStopCheckTimes( stopCount );
			}
		});
		
		return result;
	}
	
	@PostConstruct
	public void init() {


		abnormalByCode = abnormalPromptService.getAbnormalByCode(ErrorEnum.ABNORMAL_STOP_CHECK.code());
	}
}
