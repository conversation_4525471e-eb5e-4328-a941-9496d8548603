package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.AgvStatisticsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.*;

/**
 * 机器人统计job
 * @Author：yangpeilin
 * @Date: 2020/5/20 10:21
 */
@Component
public class AgvStatisticsJob {

    Logger logger = LoggerFactory.getLogger(AgvStatisticsJob.class);

    @Autowired
    private AGVStatisticsService agvStatisticsService;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private AGVService agvService;

    @Autowired
    private AGVLogService agvLogService;

    // 每天凌晨1点执行一次
    @Scheduled(cron = "0 0 1 * * ?")
    // 测试 一分钟执行一次
//    @Scheduled(cron = "0 0/1 * * * ?")
    public void agvStatistics(){
        logger.info("----------start AgvStatisticsJob job-----------startTime:{}", System.currentTimeMillis());
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - 3);
            AgvStatisticsUtil.statisticsDataByDate(calendar.getTime(), agvStatisticsService, missionWorkService, agvService, agvLogService);
        } catch (ParseException e) {
            logger.error("定时任务机器人统计失败：{}", e);
        }
        logger.info("------------end AgvStatisticsJob job-----------endTime:{}", System.currentTimeMillis());
    }
}
