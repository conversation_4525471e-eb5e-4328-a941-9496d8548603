package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.util.AgvStatisticsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * 机器人统计job
 * @Author：yangpeilin
 * @Date: 2020/5/20 10:21
 */
@Component
public class AGVStatisticsJob {

    private Logger logger = LoggerFactory.getLogger(AGVStatisticsJob.class);

    // 每天凌晨1点执行一次
    @Scheduled(cron = "0 0 1 * * ?")
    // 测试 一分钟执行一次
//    @Scheduled(cron = "0 0/1 * * * ?")
    public void agvStatistics(){
        logger.info("----------start AgvStatisticsJob job-----------");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - 3);
        AgvStatisticsUtil.statisticsDataByDate(calendar.getTime());
        logger.info("------------end AgvStatisticsJob job-----------");
    }
}
