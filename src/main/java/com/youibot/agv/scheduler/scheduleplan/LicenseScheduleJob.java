package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.constant.LicenseConstant;
import com.youibot.agv.scheduler.license.License;
import com.youibot.agv.scheduler.service.LicenseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * license证书时间更新job(防止用户更新系统时间继续使用证书)
 * @Author：yangpeilin
 * @Date: 2020/5/7 17:41
 */
@Component
public class LicenseScheduleJob {

    Logger logger = LoggerFactory.getLogger(LicenseScheduleJob.class);

    @Autowired
    private LicenseService licenseService;

    // 每个整点执行一次
    @Scheduled(cron = "0 0 0/1 * * ?")
    //若修改时间间隔，需同步对licenseConstant中TIME_ERROR进行设置
    public void excuteUpdateLicenseUpdateTime(){
        logger.debug("----------into license job-----------");
        //1.清空缓存
        LicenseConstant.licenseMap.clear();

        //2.修改数据库
        List<License> list = licenseService.findAll();
        if (!CollectionUtils.isEmpty(list)){
            License license = list.get(0);
            if (1 != license.getExpire()){//未过期
                licenseService.updateTime(license.getId());
                logger.debug("----------start to update time------------");
            }
        }
    }
}
