package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.entity.TriggerSelector;
import com.youibot.agv.scheduler.listener.event.MissionWorkStartEvent;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.TriggerSelectorService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 用于触发器的相关调度任务创建
 *
 * <AUTHOR>
 */
@DisallowConcurrentExecution
@Component
@Slf4j
public class TriggerMissionWorkJob implements Job {

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private TriggerSelectorService triggerSelectorService;

    @Override
    public void execute(JobExecutionContext context) {
        log.info("进入到MissionWorkJob");
        String triggerSelectorId = context.getJobDetail().getKey().getName();

        TriggerSelector triggerSelector = triggerSelectorService.selectById(triggerSelectorId);
        if (triggerSelector == null) {
            log.error("schedule plan is null, schedulePlanId = " + triggerSelector);
            return;
        }
        log.debug(triggerSelector.getId() + "Start a timed task_" + new Date() + triggerSelector.toString());
        //创建missionWork
        insertMissionWorkJob(triggerSelector);
        //更新完成次数
        updateCompletedTimes(triggerSelector);
        applicationContext.publishEvent(new MissionWorkStartEvent("scheduler job call"));
    }

    private void updateCompletedTimes(TriggerSelector triggerSelector) {
        Integer completedTimes = triggerSelector.getCompletedTimes();
        completedTimes++;
        triggerSelector.setCompletedTimes(completedTimes);
        triggerSelectorService.update(triggerSelector);
    }

    private void insertMissionWorkJob(TriggerSelector triggerSelector) {
        missionWorkService.createByTrigger(triggerSelector);
    }
}
