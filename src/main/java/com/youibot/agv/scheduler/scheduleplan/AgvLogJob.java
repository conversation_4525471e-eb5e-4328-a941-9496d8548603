package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.entity.AGVLog;
import com.youibot.agv.scheduler.service.AGVLogService;
import com.youibot.agv.scheduler.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static com.youibot.agv.scheduler.constant.VehicleConstant.AGV_LOG_TYPE_WORKING;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/6/2 16:40
 */
@Slf4j
@Component
public class AgvLogJob {

    @Autowired
    private AGVLogService agvLogService;

    // 每天凌晨0点执行一次
    @Scheduled(cron = "0 0 0 * * ?")
    public void secondDayAGVLog() {
        List<AGVLog> agvLogList = agvLogService.listUnCompleted(0, 0);
//        List<Vehicle> vehicles = vehiclePool.getAll();
        long now = System.currentTimeMillis();
        if (!CollectionUtils.isEmpty(agvLogList)) {
            List<String> deleteList = new ArrayList<>();
            agvLogList.forEach(agvLog -> {
                if (AGV_LOG_TYPE_WORKING.equals(agvLog.getType()) && StringUtils.isEmpty(agvLog.getMissionWorkId())){//刪除日志记录中没有绑定作业id作业日志记录
                    deleteList.add(agvLog.getId());
                    log.debug("定时任务，删除未绑定作业id的作业记录，agvLog:{}", agvLogList.toString());
                    return;
                }
                //填充endTime, 当前时间-创建时间<一天，设置结束时间为当前时间
                agvLogService.setCurrentEndTimeByCreateTime(agvLog, now);
                if (!DateUtils.greaterThanOneday(now, agvLog.getCreateTime().getTime())) {
                    AGVLog onLineLog = new AGVLog(agvLog.getAgvCode(), agvLog.getType(), now / 1000);
                    log.debug("--------------定时任务，开始新增agvLog------------------");
                    agvLogService.insert(onLineLog);
                    log.debug("--------------定时任务，结束新增agvLog------------------");
                }
            });
            if (!CollectionUtils.isEmpty(deleteList)){
                agvLogService.deleteByIds(deleteList);
            }
        }
    }

}
