package com.youibot.agv.scheduler.scheduleplan.local;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.shimizukenta.secs.SecsException;
import com.shimizukenta.secs.ext.config.AbstractSecsMsgListener;
import com.shimizukenta.secs.secs2.Secs2;
import com.shimizukenta.secs.secs2.Secs2List;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage.ReportType;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.BatteryStatus;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *电池量上报
 */
@Slf4j
@DisallowConcurrentExecution
@Component
public class BatteryReportJob extends AbstractSecsMsgListener implements Job {

	
	
	SimpleDateFormat sdf = new SimpleDateFormat("", Locale.SIMPLIFIED_CHINESE);
	
	{
		sdf.applyPattern("yyyy年MM月dd日 HH时mm分ss秒");
	}
	
	@Autowired
	private VehiclePoolService vehiclePoolService;
	

	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
	
		
		  String format = sdf.format(System.currentTimeMillis());
		  log.debug("eventId:{},dynamic_report_battery 电池量上报间隔上报:{}" ,ReportType.BATTERY.getEventId(), format );
		 
		
		   report(context);
	}

	
	// 每次间隔10分钟 上报agv电量
//	@Scheduled(fixedRate = 1000 * 60 * 10, initialDelay = 1000 * 60 * 2)
//	    @Scheduled(cron = "0 0/5 * * * ?")
	public void report( JobExecutionContext context) {



		List<Vehicle> waitForData = waitForData();

		if (CollectionUtils.isEmpty(waitForData)) {
			return;
		}
		List<Secs2List> collect = waitForData.parallelStream().filter( p ->{
			
			DefaultVehicleStatus defaultVehicleStatus = p.getDefaultVehicleStatus();
			if (Objects.isNull(defaultVehicleStatus)) {
				log.error("agv_battery_is_null=>>{}", p.getId());
				return false;
			}
			BatteryStatus battery = defaultVehicleStatus.getBattery();
			if (Objects.isNull(battery)) {
				log.debug("agvId:{},battery_is_null", p.getId());
				return false;
			}
			return true;
		}).map( item ->{
			
			/**
			 * 电池电芯相关温度
			 */
			DefaultVehicleStatus defaultVehicleStatus = item.getDefaultVehicleStatus();
			BatteryStatus battery = defaultVehicleStatus.getBattery();
		    setBatteryVal(battery);
		    return agvToData(item, battery);
		}).collect(Collectors.toList());
		
		if(CollectionUtils.isNotEmpty(collect)) {
			
			try {
				hsmsSsCommunicator.send(6, 11, false, Secs2.list(
						/**
						 * agv到达工作站事件别名
						 */
						Secs2.ascii(ReportType.BATTERY.getAlias()),
						/**
						 * 事件id
						 */
						Secs2.ascii(ReportType.BATTERY.getEventId()),
						/**
						 * DATA
						 */
						Secs2.list(
								collect)

				))

				;
			} catch (SecsException | InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		

	}


	public Secs2List agvToData(Vehicle item, BatteryStatus battery) {
		
		String join = StringUtils.join( CommonUtils.Arrays.asList( battery.getBattery_cell_temperatures())  ," ,");
		String join2 = StringUtils.join( CommonUtils.Arrays.asList( battery.getBattery_mosfet_temperatures() )  ," ,");
		return Secs2.list(
		/**
		 * agv所对应的code
		 */
		Secs2.list(Secs2.ascii("AgvID"), Secs2.ascii(item.getId()

		)),
		/**
		 * 数据类别
		 */
		Secs2.list(

				Secs2.list(

						Secs2.ascii("battery_charge"),
						Secs2.ascii(String.format("%.2f", battery.getBattery_charge()))),

				Secs2.list(

						Secs2.ascii("battery_discharge"),
						Secs2.ascii(String.format("%.2f", battery.getBattery_discharge()))),
				Secs2.list(

						Secs2.ascii("battery_fullcapacity"),
						Secs2.ascii(String.format("%.2f", battery.getBattery_fullcapacity()))),
				Secs2.list(

						Secs2.ascii("battery_leftcapacity"),
						Secs2.ascii(String.format("%.2f", battery.getBattery_leftcapacity()))),
				Secs2.list(

						Secs2.ascii("battery_temp"),
						Secs2.ascii(String.format("%.2f", battery.getBattery_temp()))),
				Secs2.list(

						Secs2.ascii("battery_value"),
						Secs2.ascii(String.format("%.2f", battery.getBattery_value()))),
				Secs2.list(

						Secs2.ascii("battery_voltage"),
						Secs2.ascii(String.format("%.2f", battery.getBattery_voltage()))),
				/**
				 * 充放电次数
				 */
				Secs2.list(

						Secs2.ascii("battery_charge_num"),
						Secs2.ascii(String.format("%.2f", battery.getBattery_charge_num()))),
				/**
				 * 电池电芯温度
				 */
				Secs2.list(

						Secs2.ascii("battery_cell_temperatures"),
						Secs2.ascii(join)

				),
				/**
				 * 电池mosfet温度
				 */
				Secs2.list(

						Secs2.ascii("battery_mosfet_temperatures"),
						Secs2.ascii( join2)))

);
	}

	/**
	 * 电池电芯相关温度
	 */
	/**
	 * 电池电芯相关温度
	 */
	private void setBatteryVal(BatteryStatus battery) {
		
		
		/**
		 * 电池电芯相关温度
		 */
		  if( Objects.isNull( battery.getBattery_charge_num() )) {
			  ;
			  battery.setBattery_charge_num( 0 ); 
		  }
		  if( ArrayUtils.isEmpty( battery.getBattery_cell_temperatures() ) ) {
			  double[] newcellTempeatures = { 0, 0  };
			  battery.setBattery_cell_temperatures( newcellTempeatures );
		  }
		  
		  if( ArrayUtils.isEmpty( battery.getBattery_mosfet_temperatures() )) {
			  double[] mosfet_temperatures = { 0, 0  };
			  battery.setBattery_mosfet_temperatures(mosfet_temperatures);
		  }
		  
	}
	
	/**
	 * 等待车辆数据
	 * 
	 * @param all
	 */
	private List<Vehicle> waitForData() {
		List<Vehicle> all = vehiclePoolService.selectAll();
		all = all.parallelStream().filter(p -> Objects.nonNull(p) 
				&& Objects.nonNull(p.getDefaultVehicleStatus())
				&& Objects.nonNull(p.getDefaultVehicleStatus().getBattery())
				).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(all)) {
			try {
				TimeUnit.SECONDS.sleep(5);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}

		return all;
	}
	
}
