package com.youibot.agv.scheduler.scheduleplan.local;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shimizukenta.secs.SecsException;
import com.shimizukenta.secs.ext.config.AbstractSecsMsgListener;
import com.shimizukenta.secs.secs2.Secs2;
import com.shimizukenta.secs.secs2.Secs2List;
import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.constant.vo.TjdChargerDto;
import com.youibot.agv.scheduler.constant.vo.TjdChargerDto.TjdChargerItem;
import com.youibot.agv.scheduler.mqtt.bean.scribe.tjdagv.AgvArrivedErackInfoMessage.ReportType;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> 台积电 车厢内部的温湿度数据上报
 */
/**
 * 
 {"event": "reply","dataList": [{"name":"charge01","u": "0.0","i": "0.0","t": "36.0","oc": "0"},{"name":"charge02","u": "0.0","i": "0.0","t": "43.0","oc": "0"},{"name":"charge03","u": "0.0","i": "0.0","t": "41.0","oc": "0"},{"name":"charge04","u": "55.5","i": "60.0","t": "28.0","oc": "0"}]}

 *
 */
@Slf4j
@DisallowConcurrentExecution
@Component
public class ChargerDataReportJob extends AbstractSecsMsgListener implements Job {

	
	
	@Autowired
	private HttpClientService httpClientService ;


	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {

	
		try {
			HttpResult doGet = httpClientService.doGet( TjdCxt.CHARGE_URL ) ;
			log.debug("eventId:{}, charge_data:{}" , ReportType.CHARGER_STACK.getEventId(), JSON.toJSONString(doGet));
			String body = doGet.getBody();
			TjdChargerDto dto = JSON.parseObject(body , TjdChargerDto.class);
			this.report(context, dto);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}


	// 每次间隔10分钟 上报agv电量
//	@Scheduled(fixedRate = 1000 * 60 * 10, initialDelay = 1000 * 60 * 2)
//	    @Scheduled(cron = "0 0/5 * * * ?")
	public void report(JobExecutionContext context , TjdChargerDto dto) {

	
		try {
			List<Secs2List> agvsToData = chargersToData( dto.getDataList() );
			if(CollectionUtils.isEmpty(agvsToData)) {
				
				return ;
			}
			hsmsSsCommunicator.send( 6, 11 , false, Secs2.list(
					/**
					 * agv到达工作站事件别名
					 */
					Secs2.ascii( ReportType.CHARGER_STACK.getAlias()),
					/**
					 * 事件id
					 */
					Secs2.ascii( ReportType.CHARGER_STACK.getEventId()),
					/**
					 * DATA
					 */
					Secs2.list(
							agvsToData

					)

			))

			;
		} catch (SecsException | InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
	}

	/** 所有充电桩对应的数据
	 * @param dataList
	 * @return
	 */
	public List<Secs2List> chargersToData(List<TjdChargerItem>  dataList ) {
		if(CollectionUtils.isEmpty(dataList)) {
			
			return Lists.newArrayList();
		}
		List<Secs2List> collect = dataList.parallelStream().map(i -> chargerItemToData(i)).collect(Collectors.toList());
		
		return collect;
	};
	
	
	/** charge to Data
	 * @param item
	 * @return
	 */
	public Secs2List chargerItemToData(TjdChargerItem item) {
		
		List<Secs2List>  newRes = Lists.newArrayList(); 
		
		List<Secs2List>  res = Lists.newArrayList();
		
	   Secs2List list = Secs2.list(Secs2.ascii("ChargeId"), Secs2.ascii(Objects.toString( item.getName() )
					
					));
	   /**
	    * header
	    */
	   newRes.add(list);
	   
	   
	   res.add( Secs2.list(Secs2.ascii("u"), Secs2.ascii(Objects.toString( item.getU() )
			   
			   )));
	   res.add( Secs2.list(Secs2.ascii("i"), Secs2.ascii(Objects.toString( item.getI() )
			   
			   )));
	   res.add( Secs2.list(Secs2.ascii("t"), Secs2.ascii(Objects.toString( item.getT() )
			   
			   )));
	   res.add( Secs2.list(Secs2.ascii("oc"), Secs2.ascii(Objects.toString( item.getOc() )
			   
			   )));
	
		
	
	/**
	 * body
	 */
	   
	   Secs2List body = Secs2.list(
				/**
				 * 单个充电桩所对应的data
				 */
						res

				);
	   
	   newRes.add( body );
	   
		return Secs2.list(
		/**
		 * 单个充电桩所对应的data
		 */
				newRes

		)

;
	}



}
