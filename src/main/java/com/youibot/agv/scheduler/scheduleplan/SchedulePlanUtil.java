package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.constant.TjdCxt;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.SchedulePlan;
import com.youibot.agv.scheduler.entity.TriggerSelector;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.SchedulePlanService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Date;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SUCCESS;
import static com.youibot.agv.scheduler.constant.SchedulePlanConstant.*;


/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月16日 下午8:20:21
 */


@Service
public class SchedulePlanUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(SchedulePlanUtil.class);

    @Autowired
    private SchedulerFactoryBean schedulerFactoryBean;

    private Scheduler scheduler = null;

    @Lazy
    @Autowired
    private SchedulePlanService schedulePlanService;

    @Autowired
    private MissionWorkService missionWorkService;

    @PostConstruct
    private void init() {
        scheduler = schedulerFactoryBean.getScheduler();
    }

    public void addPlan(SchedulePlan plan) throws SchedulerException {
        JobKey jobKey = getJobKey(plan.getId());
        JobDataMap map = getJobDataMap(plan);
        JobDetail jobDetail = getJobDetail(jobKey, map, MissionWorkJob.class);
        Trigger trigger = getTrigger(plan);
        scheduler.scheduleJob(jobDetail, trigger);
    }

    @SuppressWarnings("unchecked")
	public void addPlan(TriggerSelector plan) throws SchedulerException {
        JobKey jobKey = getJobKey(plan.getId());
        JobDataMap map = getJobDataMap(plan);
        boolean isLocal =plan.isLocalJob();
        Class<? extends Job> missionWorkJobClass  = TriggerMissionWorkJob.class;
        if( isLocal) {
    		Object beanJob = ApplicationUtils.getBean(plan.getMissionId());
    		missionWorkJobClass = (Class<? extends Job>) beanJob.getClass();
        }
        JobDetail jobDetail = getJobDetail(jobKey, map, missionWorkJobClass);
        Trigger trigger = getTrigger(plan);
        scheduler.scheduleJob(jobDetail, trigger);
    }

    // 任务触发器
    private Trigger getTrigger(SchedulePlan plan) {
        if (StringUtils.isEmpty(plan.getCron())) {
            Date executionTime = plan.getExecuteTime();
            if (StringUtils.isEmpty(executionTime)) {
                executionTime = new Date();
            }
            return TriggerBuilder.newTrigger()
                    .startAt(executionTime)
                    .withIdentity(plan.getId(), SCHEDULE_JOB_KEY_TRIGGER_GROUP)
                    .withSchedule(SimpleScheduleBuilder.simpleSchedule().withRepeatCount(getRepeatCount(plan)).withIntervalInSeconds(getExecuteInterval(plan)))
                    .build();
        }
        return TriggerBuilder.newTrigger()
                .withIdentity(plan.getId(), SCHEDULE_JOB_KEY_TRIGGER_GROUP)
                .withSchedule(CronScheduleBuilder.cronSchedule(plan.getCron()))
                .build();

    }

    // 任务触发器
    private Trigger getTrigger(TriggerSelector plan) {
        //开始时间
        Date startTime = plan.getStartTime();
        //执行次数
        Integer executeTimes = plan.getExecuteTimes();
        //触发间隔,单位s
        int secondsPeriod = plan.getSecondsPeriod();
        //重复次数
        int repeatCount = getRepeatCount(executeTimes);
        //Repeat Interval cannot be zero.
        secondsPeriod = repeatCount > 0 && secondsPeriod == 0 ? 1 : secondsPeriod;
        if(plan.isLocalJob()) {
        	repeatCount = SimpleTrigger.REPEAT_INDEFINITELY;
        }
        return TriggerBuilder.newTrigger()
                .startAt(startTime)
                .withIdentity(plan.getId(), SCHEDULE_JOB_KEY_TRIGGER_GROUP)
                .withSchedule(SimpleScheduleBuilder.simpleSchedule().withRepeatCount(repeatCount)
                        .withIntervalInSeconds(secondsPeriod))
                .build();

    }

    // simpleTrigger executeInterval 默认执行次数为1
    private int getExecuteInterval(SchedulePlan plan) {
        return (StringUtils.isEmpty(plan.getExecuteInterval()) || plan.getExecuteInterval() < 1) ? 1 : plan.getExecuteInterval();
    }

    // JobDetail
    public <T extends Job> JobDetail getJobDetail(JobKey jobKey, JobDataMap map, Class<T> missionWorkJobClass) {
        return JobBuilder.newJob(missionWorkJobClass)
                .withIdentity(jobKey)
                .setJobData(map)
                //	.storeDurably() //即使没有Trigger关联时,也不需要删除该JobDetail
                .build();
    }

    /*// jobKey
    public JobKey getJobKey(SchedulePlan plan) {
        return JobKey.jobKey(plan.getId(), SCHEDULE_JOB_KEY_JOB_GROUP);
    }*/

    // jobKey
    public JobKey getJobKey(String key) {
        return JobKey.jobKey(key, SCHEDULE_JOB_KEY_JOB_GROUP);
    }

    // 获取JobDataMap.
    public JobDataMap getJobDataMap(SchedulePlan plan) {
        JobDataMap map = new JobDataMap();
        map.put(plan.getId(), plan);
        return map;
    }

    // 获取JobDataMap.
    public JobDataMap getJobDataMap(TriggerSelector plan) {
        JobDataMap map = new JobDataMap();
        map.put(plan.getId(), plan);
        map.put(TjdCxt.AGV_STOP_CHECK_TIMES_MAP_KEY, plan.getExecuteTimes() );
        return map;
    }

    /**
     * 获取重复次数
     *
     * @param plan
     * @return
     */
    private int getRepeatCount(SchedulePlan plan) {
        //获取执行次数
        Integer frequency = plan.getFrequency();
        if (frequency == null) {
            frequency = 0;
        }
        return frequency - 1;//重复次数为执行次数减1
    }

    /**
     * 获取重复次数
     *
     * @param executeTimes
     * @return
     */
    private int getRepeatCount(Integer executeTimes) {
        int lastExecuteTimes = 0;
        //获取执行次数
        if (executeTimes == null) {
            return lastExecuteTimes;
        }
        return executeTimes - 1;//重复次数为执行次数减1
    }

    // triggerKey
    public TriggerKey getTriggerKey(SchedulePlan plan) {
        return TriggerKey.triggerKey(plan.getId(), SCHEDULE_JOB_KEY_TRIGGER_GROUP);
    }

    //获取quartz 执行状态
    public String getTriggerState(SchedulePlan plan) {
        try {
            Trigger.TriggerState triggerState = scheduler.getTriggerState(this.getTriggerKey(plan));
            return String.valueOf(triggerState);
        } catch (SchedulerException e) {
            LOGGER.error("获取触发状态失败,", e);
            throw new ExecuteException(MessageUtils.getMessage("http.schedule_plan_get_trigger_state_error"));
        }
    }

    public void pauseJob(SchedulePlan plan) throws SchedulerException {
        JobKey jobKey = this.getJobKey(plan.getId());
        scheduler.pauseJob(jobKey);
        LOGGER.debug("pause job, schedulePlanId = " + plan.getId());
    }

    /**
     * 暂停一个Job
     *
     * @param key
     * @throws SchedulerException
     */
    public void pauseJob(String key) {
        try {
            JobKey jobKey = this.getJobKey(key);
            scheduler.pauseJob(jobKey);
            LOGGER.debug("pause job, schedulePlanId = " + key);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void resumeJob(SchedulePlan plan) throws SchedulerException {
        JobKey jobKey = this.getJobKey(plan.getId());
        scheduler.resumeJob(jobKey);
        LOGGER.debug("resume job, schedulePlanId = " + plan.getId());
    }

    public void resumeJob(String key) {
        try {
            JobKey jobKey = this.getJobKey(key);
            scheduler.resumeJob(jobKey);
            LOGGER.debug("resume job, schedulePlanId = " + key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Boolean checkExists(String key) {
        try {
            JobKey jobkey = this.getJobKey(key);
            return scheduler.checkExists(jobkey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public void deleteJob(SchedulePlan plan) throws SchedulerException {
        JobKey jobKey = this.getJobKey(plan.getId());
        scheduler.deleteJob(jobKey);
        LOGGER.debug("delete job, schedulePlanId = " + plan.getId());
    }

    /**
     * 删除一个Job
     *
     * @param key
     */
    public void deleteJob(String key) {
        try {
            JobKey jobKey = this.getJobKey(key);
            scheduler.deleteJob(jobKey);
            LOGGER.debug("delete job, schedulePlanId = " + key);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 调度计划quartz框架等待missionWork执行结束
     *
     * @param plan
     * @param missionWorkId
     * @throws SchedulerException
     */
    public void waitMissionWorkEnd(SchedulePlan plan, String missionWorkId) throws SchedulerException {
        LOGGER.debug("wait mission work end execute.");
        //获取triggerStatus
        String triggerState = this.getTriggerState(plan);
        LOGGER.debug("start trigger state: " + triggerState);
        if (SCHEDULE_TRIGGER_STATUS_COMPLETE.equals(triggerState)) {
            return;
        }
        if (SCHEDULE_TRIGGER_STATUS_NORMAL.equals(triggerState) || SCHEDULE_TRIGGER_STATUS_BLOCKED.equals(triggerState)) {
            this.pauseJob(plan);
        } else if (SCHEDULE_TRIGGER_STATUS_PAUSED.equals(triggerState)) {
            LOGGER.debug("job already pause, ");
        } else {
            LOGGER.error("job is not normal or pause or block");
            throw new ExecuteException(MessageUtils.getMessage("http.schedule_plan_trigger_state_error"));
        }
        while (true) {
            MissionWork missionWork = missionWorkService.selectById(missionWorkId);
            if (missionWork == null) {
                LOGGER.error("mission work is null, id = " + missionWorkId);
                break;
            }
            String workStatus = missionWork.getStatus();
            if (MISSION_WORK_STATUS_SUCCESS.equals(workStatus) || MISSION_WORK_STATUS_SHUTDOWN.equals(workStatus)) {
                LOGGER.debug("mission work execute success or shutdown, id = " + missionWorkId);
                break;
            }
            this.threadSleep(500);
        }
        this.threadSleep(plan.getExecuteInterval() * 1000);
        //如果用户没有暂停调度计划且triggerState是暂停状态
        triggerState = this.getTriggerState(plan);
        LOGGER.debug("end trigger state: " + triggerState);
        plan = schedulePlanService.selectById(plan.getId());
        if (!SCHEDULE_STATUS_PAUSE.equals(plan.getStatus()) && SCHEDULE_TRIGGER_STATUS_PAUSED.equals(triggerState)) {
            this.resumeJob(plan);
        }
        LOGGER.debug("wait mission work end success.");
    }

    @Async
    public void waitMissionWorkEndAsync(SchedulePlan plan, String missionWorkId) {
        try {
            this.waitMissionWorkEnd(plan, missionWorkId);
        } catch (Exception e) {
            LOGGER.error("wait mission work end error, ", e);
        }
    }

    private void threadSleep(long millsTime) {
        try {
            Thread.sleep(millsTime);
        } catch (InterruptedException e) {
            LOGGER.error("thread sleep error, ", e);
        }
    }
}
