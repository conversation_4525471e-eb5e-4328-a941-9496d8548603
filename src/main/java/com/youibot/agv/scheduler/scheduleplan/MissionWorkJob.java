package com.youibot.agv.scheduler.scheduleplan;

import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.SchedulePlan;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.listener.event.MissionWorkStartEvent;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.SchedulePlanService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_CREATE;
import static com.youibot.agv.scheduler.constant.SchedulePlanConstant.*;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月17日 下午8:34:36
 * 同一个任务不允许并发执行
 */
//@PersistJobDataAfterExecution
@DisallowConcurrentExecution
@Component
public class MissionWorkJob implements Job {

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkJob.class);

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionService missionService;

    @Autowired
    private SchedulePlanService schedulePlanService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private SchedulePlanUtil schedulePlanUtil;

    @Override
    public void execute(JobExecutionContext context) {
        String schedulePlanId = context.getJobDetail().getKey().getName();
        SchedulePlan plan = schedulePlanService.selectById(schedulePlanId);
        if (plan == null) {
            LOGGER.error("schedule plan is null, schedulePlanId = " + schedulePlanId);
            return;
        }
        LOGGER.debug(plan.getId() + "Start a timed task_" + new Date() + plan.toString());
        String missionWorkId = insertMissionWorkJob(plan);//创建missionWork
        updateCompleteFrequency(plan);//更新完成次数
        updateScheduleStatus(plan);//更新schedule plan状态
        applicationContext.publishEvent(new MissionWorkStartEvent("scheduler job call"));
        if (plan.getExecuteOverCreateNew()) {//执行完创建
            try {
                schedulePlanUtil.waitMissionWorkEnd(plan, missionWorkId);
            } catch (Exception e) {
                LOGGER.error("wait mission work end error, ", e);
            }
        }
    }



    private void updateCompleteFrequency(SchedulePlan plan) {
        SchedulePlan planEntity = new SchedulePlan();
        // 设置完成次数
        Integer completeFrequency = plan.getCompleteFrequency();
        if (completeFrequency == null) {
            completeFrequency = 1;
        } else {
            completeFrequency++;
        }
        planEntity.setCompleteFrequency(completeFrequency);
        planEntity.setId(plan.getId());
        schedulePlanService.updateByPrimaryKeySelective(planEntity);
    }

    private void updateScheduleStatus(SchedulePlan plan) {
        // 获取执行状态
        String triggerState = schedulePlanUtil.getTriggerState(plan);
        SchedulePlan planEntity = new SchedulePlan();
        planEntity.setId(plan.getId());
        if (SCHEDULE_STATUS_READY.equals(plan.getStatus())) {
            planEntity.setStartTime(new Date());
            planEntity.setStatus(SCHEDULE_STATUS_RUNNING);
        }
        if (SCHEDULE_TRIGGER_STATUS_COMPLETE.equals(triggerState) || SCHEDULE_TRIGGER_STATUS_NONE.equals(triggerState)) {
            planEntity.setEndTime(new Date());
            planEntity.setStatus(SCHEDULE_STATUS_SUCCESS);
        }
        schedulePlanService.updateByPrimaryKeySelective(planEntity);
    }

    private String insertMissionWorkJob(SchedulePlan plan) {
        MissionWork work = new MissionWork();
        String missionId = plan.getMissionId();
        if (StringUtils.isEmpty(missionId)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        Mission mission = missionService.selectById(missionId);
        work.setMissionId(missionId);
        // 以用户指定的AGV为准.
        if (!StringUtils.isEmpty(plan.getAgvCode())) {
            work.setAgvCode(plan.getAgvCode());
        } else {
            work.setAgvCode(mission.getAgvCode());
        }
        // set agv 组.
        work.setName(mission.getName());
        work.setStatus(MISSION_WORK_STATUS_CREATE);
        work.setSequence(mission.getSequence());
        work.setSchedulePlanId(plan.getId());
        work.setCallbackUrl(plan.getCallbackUrl());
        work.setAgvType(mission.getAgvType());
        work.setAgvGroupId(mission.getAgvGroupId());
        missionWorkService.insert(work);
        LOGGER.debug("Creation successful" + work.getId());
        return work.getId();
    }

}
