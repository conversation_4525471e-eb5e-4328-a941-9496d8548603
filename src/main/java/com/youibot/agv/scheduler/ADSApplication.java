package com.youibot.agv.scheduler;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.shimizukenta.secs.ext.annotation.EnableSecs;

/**
 sudo ssh -ND 0.0.0.0:1080 youibot@localhost
 *
 */
@EnableSecs
@SpringBootApplication
@EnableScheduling
@EnableAsync(proxyTargetClass = true )
public class ADSApplication {

	public static void main(String[] args) {
		
		SpringApplication.run(ADSApplication.class, args);
	}

}
