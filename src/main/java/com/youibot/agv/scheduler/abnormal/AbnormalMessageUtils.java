package com.youibot.agv.scheduler.abnormal;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.bean.send.AbnormalMessage;
import com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.SystemWorkModeService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2022/9/13 10:08
 */
public class AbnormalMessageUtils {

    private static final Logger logger = LoggerFactory.getLogger(AbnormalMessageUtils.class);

    public static void sendErrorCodeMessage(Integer code) throws InterruptedException {
        AbnormalMessage sendMessage = AbnormalMessage.builder().code(code).build();
        sendErrorCodeMessage(sendMessage);
    }

    public static void sendErrorCodeMessage(String agvCode, Integer code) throws InterruptedException {
        AbnormalMessage sendMessage = AbnormalMessage.builder()
                .agvCode(agvCode)
                .code(code).build();
        sendErrorCodeMessage(sendMessage);
    }

    public static void sendErrorCodeMessage(String agvCode, String missionWorkId, Integer code) throws InterruptedException {
        AbnormalMessage sendMessage = AbnormalMessage.builder()
                .agvCode(agvCode)
                .missionWorkId(missionWorkId)
                .code(code).build();
        sendErrorCodeMessage(sendMessage);
    }

    public static void sendErrorCodeMessage(AbnormalMessage sendMessage) throws InterruptedException {

        try {
            MqttUtils.pushMessage(MqTopicConstant.MQTT_PUBLISH_ERROR_CODE, sendMessage);
        }catch (Exception e){
            if(e instanceof InterruptedException){
                throw e;
            }
            logger.error("异常码上报异常：e:{},", e);
        }
    }


    public static RuntimeException getException(Integer errorCode, Exception e) {
        String msg = e.getMessage();
        if (e instanceof ActionException) {
            errorCode = ((ActionException) e).getCode();
            msg = ((ActionException) e).getMsg();
        }
        return new ActionException(errorCode, msg);
    }

    public static RuntimeException getException(Integer errorCode, String msg) {
        return new ActionException(errorCode, msg);
    }

    public static void throwMapUpdateExeception(Vehicle vehicle) {
        logger.debug("检测到fleet发布地图！{}" , vehicle.getMapUpdateMsg());
        vehicle.setMapUpdateMsg(null);
        throw new YOUIFleetException(-1,"发布地图导致任务重做");
    }
}
