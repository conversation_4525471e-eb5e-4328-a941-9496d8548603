package com.youibot.agv.scheduler.abnormal;

/**
 * Compass异常码常量
 */
public enum AbnormalCodeConstant {

    MISSION_PARAMETER_MISS(200001,"创建作业时动态参数不符合要求"),
    ANI_MARKER_IS_NOT_AVALIABLE(200002,"目标点不存在或被禁用"),
    ACTION_PARAMETER_ERROR(200003,"动作传入参数错误"),
    MISSION_WORK_HTTP_REQUEST_FAIL(200004,"Http连接失败"),
    MISSION_WORK_HTTP_ERROR_RESPONSE_CODE(200005,"HTTP返回错误码"),
    MISSION_WORK_HTTP_URL_IS_NULL_ERROR(200006,"未配置Http地址"),

    WAIT_FOR_PATH_PLAN_TIMEOUT(200007,"获取路径规划结果超时"),
    GET_PATH_PLAN_RESULT_ERROR(200008,"获取路径规划结果异常"),
    MISSION_WORK_CHECK_READ_WRONG_CODE(200009,"校验寄存器读到错误码"),
    MISSION_WORK_VARIABLE_ERROR(200010,"作业执行时找不到全局变量"),
    MISSION_WORK_VARIABLE_VALUE_ERROR(200011,"作业运行时找不到全局变量的值"),
    COMPASS_ACTION_RUN_ERROR(200012,"Compass动作执行异常"),
    INTERVAL_DATA_ERROR(200013,"内部数据异常"),

    PILOT_REJECT_FAILURE(200014,"Pilot拒绝接收指令"),
    PILOT_ACTION_RUN_ERROR(200015,"Pilot动作执行异常"),
    PILOT_PACKET_PARSE_ERROR(200016,"Pilot反馈数据格式错误"),
    ACTION_STATUS_IS_NULL(200017, "找不到已下发给Pilot的指令"),
    ACTION_START_NOT_AT_ELEVATOR(200018, "禁止路径规划从电梯点开始"),
    ACTION_AIM_MARKER_NOT_AT_ELEVATOR(200019, "禁止路径规划到电梯点结束"),


    //---------------线程
    THREAD_DATA_CLEAN_EXCEPTION(200201,"数据清理线程异常"),
    THREAD_MAP_SYNC_EXCEPTION(200202,"机器人地图下载线程异常"),
    THREAD_MAP_LOAD_MEMORY_EXCEPTION(200203,"机器人地图加载到内存线程异常"),
    THREAD_MAP_UPDATE_EXCEPTION(200204,"机器人地图更新线程异常"),
    THREAD_AUTO_DOOR_EXCEPTION(200206,"自动门线程异常"),
    THREAD_AIR_SHOWER_DOOR_EXCEPTION(200207,"风淋门线程异常"),
    THREAD_ALLOCATION_EXCEPTION(200208,"作业分配线程异常"),
    THREAD_PUSH_NOTIFICATION_EXCEPTION(200209,"推送提醒消息线程异常"),
    THREAD_PUSH_FLEET_VEHICLE_POSITION_DATA_EXCEPTION(200210,"推送调度定位线程异常"),
    THREAD_PUSH_FLEET_LASER_DATA_EXCEPTION(200211,"推送调度点云数据线程异常"),
    THREAD_PUSH_FLEET_VEHICLE_STATUS_EXCEPTION(200212,"推送调度状态数据线程异常"),
    THREAD_VEHICLE_HEARTBEAT_EXCEPTION(200213,"机器人心跳检测线程异常"),
    THREAD_VEHICLE_STATUS_QUERY_EXCEPTION(200214,"机器人状态查询线程异常"),
    THREAD_CONTROL_MODE_UPDATE_EXCEPTION(200215,"机器人控制模式更新线程异常"),
    THREAD_PUSH_MANIPULATORARM_STATUS_EXCEPTION(200216,"机器人机械臂状态推送前端线程异常"),
    THREAD_PUSH_GAS_DATA_EXCEPTION(200217,"机器人气体状态推送前端线程异常"),
    THREAD_QUERY_LASER_DATA_EXCEPTION(200218,"机器人查询点云数据线程异常"),
    THREAD_PUSH_LASER_DATA_EXCEPTION(200219,"机器人点云数据推送前端线程异常"),
    THREAD_PUSH_MISSION_WORK_STATUS_EXCEPTION(200220,"机器人作业状态推送前端线程异常"),
    THREAD_MANUAL_MOVE_EXCEPTION(200221,"机器人手工控制线程异常"),
    THREAD_PUSH_AUTO_MOVE_PLAN_EXCEPTION(200222,"机器人推送自由导航线程异常"),
    THREAD_SENSOR_TRIGGER_EXCEPTION(200223,"机器人传感器触发器线程异常"),
    THREAD_SIDE_PATH_OCCUPY_EXCEPTION(200224,"机器人路径占用处理线程异常"),
    THREAD_CALL_BOX_EXCEPTION(200225,"呼叫盒线程异常"),
    THREAD_CALL_BOX_HANDLE_EXCEPTION(200226,"呼叫盒处理线程异常"),
    THREAD_VEHICLE_SMART_CHARGE_EXCEPTION(200227,"机器人充电线程异常"),
    THREAD_VEHICLE_SMART_WAIT_EXCEPTION(200228,"机器人泊车线程异常"),
    THREAD_MQTT_CONNECTION_EXCEPTION(200229,"连接mqtt线程异常"),
    THREAD_MQTT_DISCONNECTION_EXCEPTION(200230,"机器人mq连接断连检测线程异常"),
    THREAD_MQTT_MESSAGE_HANDLE_EXCEPTION(200231,"机器人mq消息处理数据线程异常"),

    //---------------地图
    MAP_FTP_HOST_IS_NULL_ERROR(200301,"未配置FTP的HOST访问参数"),
    MAP_FTP_CONNECTION_ERROR(200302,"无法连接FTP服务，可能原因：端口未开、网络不通"),
    MAP_UPDATE_ERROR(200303,"地图实时更新失败"),
    MAP_FTP_LOGIN_ERROR(200304,"无法登录FTP服务，账号密码错误"),
    MAP_FTP_WRITE_IN_TIMEOUT_ERROR(200305,"FTP写入超时"),
    MAP_FILE_IS_NOT_FOUND_ERROR(200306,"地图关键文件缺失（MD5文件 Info文件）"),
    MAP_NAME_IS_INVALID_ERROR(200307,"地图名称不符合规则（中文或者特殊符号）"),
    MAP_UPLOAD_DATA_ERROR(200308,"网络中断导致地图传输失败"),
    MAP_LAST_PUBLISH_UNFINISH_ERROR(200309,"发布失败，该地图上一次发布的数据还未处理完成"),
    MAP_DOWNLOAD_FILE_BROKEN_ERROR(200310,"下载后文件缺失或者文件损坏"),

    //---------------设备
    CALLBOX_NO_CONNECTION_ERROR(200401,"呼叫盒未连接"),
    CALLBOX_HANDLE_DATA_ERROR(200402,"呼叫盒数据处理异常"),
    CALLBOX_NO_BOUND_TASK_ERROR(200403,"呼叫盒未绑定任务"),
    CALLBOX_TASK_UNFINISH_ERROR(200404,"呼叫盒存在未完成作业"),
    CALLBOX_MISS_PARAM_ERROR(200405,"呼叫盒参数缺失"),

    MODBUS_FUNCTION_CODE_IS_WRONG_ERROR(200406,"寄存器功能码不正确"),
    MODBUS_DATA_IS_NOT_IN_RANGE(200407,"寄存器值不在范围内"),
    MODBUS_WRONG_PARAM_ERROR(200408,"Modbus寄存器参数异常"),
    MODBUS_MISS_PARAM_ERROR(200409,"Modbus寄存器参数丢失"),
    MODBUS_CONNECT_ERROR(200410,"连接寄存器失败"),
    MODBUS_READ_ERROR(200411,"读取寄存器失败"),
    MODBUS_WRITE_IN_ERROR(200412,"写入寄存器失败"),
    MODBUS_COMMUNICATION_FAIL(200413, "Modbus通讯失败"),
    MODBUS_READ_NOT_EQUAL_TIME_OUT(200414, "读寄存器超时"),

    ELEVATOR_GET_DATA_TIMEOUT_ERROR(200415,"获取电梯数据超时"),

    MOS_CONNECTION_ERROR(200416,"Mos连接异常"),
    MOS_RUN_ACTION_ERROR(200417,"Mos执行动作异常"),
    MOS_REJECT_ERROR(200418, "Mos拒绝接收指令"),


    //----本地模式
    MISSION_IS_NULL(201001, "当前预设任务不存在"),
    BATTERY_VALUE_LOW(201002,"机器人电量低"),
    ACTION_AGV_IS_OFF_TRACK(201003, "机器人脱轨"),
    ROBOT_SWITCHES_TO_MANUAL_MODE_ERROR(201004, "切手动模式使任务异常"),
    BUTTON_EMERGENCY_STOP(201005, "按钮急停"),
    EQUIPMENT_EMERGENCY_STOP(201006, "安全设备急停"),
    COLLECTION_EMERGENCY_STOP(201007, "碰撞急停"),
    LIFT_EMERGENCY_STOP(201008, "升降电机急停"),
    ROLLER_EMERGENCY_STOP(201009, "辊筒急停"),
    CANCEL_NAVIGATION_ERROR(201010, "取消路径导航异常"),
    VEHICLE_PATH_PLAN_ERROR(201011,"机器人路径规划异常"),
    OBSTACLE_AVOIDANCE_TIMEOUT(201012, "可通行路径长时间存在障碍物, 机器人无法通行"),
    LOCATION_DATA_DELAY_ERROR(201013, "定位数据延时超过500毫秒"),

    FAILED_LOAD_MAP(201014,"加载地图失败"),
    MAP_NAME_EXISTS(201015,"地图名称已存在"),
    SEARCH_MAP_FAILED(201016,"查询地图数据失败"),
    ADD_MAP_FAILED(201017,"添加地图失败"),
    UPDATE_MAP_FAILED(201018,"修改地图失败"),
    MAP_MISS_PARAMETER(201019,"地图参数缺失"),
    ADD_MARKER_FAILED(201020,"添加标记点失败"),
    UPDATE_MARKER_FAILED(201021,"修改标记点失败"),
    READ_MARKER_DATA_IS_NULL(201022,"获取标记点数据为空"),
    DELETE_MARKER_FAILED(201023,"删除标记点失败"),
    READ_PATH_FILE_FAILED(201024,"查询路径文件失败"),
    ADD_PATH_FAILED(201025,"添加路径失败"),
    UPDATE_PATH_FAILED(201026,"修改路径失败"),
    DELETE_PATH_FAILED(201027,"删除路径失败"),
    MAP_NAME_INVALID(201028,"地图名称不合法"),
    FILE_CREATE_ERROR(201029,"本地文件创建失败"),
    MAP_MD5_ENCRYP_ERROR(201030,"本地文件md5加密异常"),
    AGV_MAP_ID_IS_NULL(201031, "获取目标地图Id为空"),
    AGV_MAP_IS_NULL(201032, "地图不存在"),
    AGV_MAP_NOT_ENABLE(201033, "目标地图未启用");

    private Integer errorCode;
    private String errorMessage;

    private AbnormalCodeConstant(Integer errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public Integer code() {
        return this.errorCode;
    }

    public String msg() {
        return this.errorMessage;
    }
}
