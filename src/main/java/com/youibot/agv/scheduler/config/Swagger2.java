package com.youibot.agv.scheduler.config;

import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import static springfox.documentation.builders.PathSelectors.ant;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月13日 下午3:00:59
 */
@Configuration
@EnableSwagger2
public class Swagger2 {
    // swagger2的配置文件,这里可以配置swagger2的一些基本信息的内容,比如扫描的包等等
    @Bean
    public Docket createRestApi() {
//        Predicate<RequestHandler> selector1 = RequestHandlerSelectors.basePackage("com.youibot.agv.scheduler.controller");
        Predicate<RequestHandler> selector2 = RequestHandlerSelectors.basePackage("com.youibot.agv.scheduler.controller.v3");
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                // 排除 error 相关的 url
                .paths(Predicates.and(ant("/**"), Predicates.not(ant("/error"))))
                // 扫描api接口的包路径
//                .apis(Predicates.or(selector1,selector2))
                .apis(selector2)
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("YOUISimulation API")
                .description("YOUISimulation 智能机器人仿真系统接口文档，第三方可以通过标准的Rest HTTP创建和调度机器人执行任务。")
                .version("4.9.1 RELEASE")
                .contact(new Contact("YOUIBOT", "http://www.youibot.com", null))
                .build();
    }


}
