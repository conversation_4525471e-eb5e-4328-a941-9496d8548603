package com.youibot.agv.scheduler.config;

import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.util.IpUtils;
import com.youibot.agv.scheduler.util.UserUtil;

/**
 * 操作日志，切面处理类
 */
@Aspect
@Component
public class LogOperationAspect {

    private static final Logger logger = LoggerFactory.getLogger(LogOperationAspect.class);

    @Pointcut("@annotation(com.youibot.agv.scheduler.annotation.LogOperation)")
    public void logPointCut() {

    }


    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {

        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if(Objects.isNull( attributes)) {
            	return point.proceed();
            }
            HttpServletRequest request = attributes.getRequest();
            
            Object[] parameters = point.getArgs();
            long startTime = System.currentTimeMillis();
            String userName = UserUtil.getLoginUserName();
            String ipAddr = IpUtils.getIpAddr(request);
           
            logger.debug("[request userName]:{},[request ip]:{}，[request api]URL:{},IP:{},Method:{},Param:{}" ,userName , ipAddr, request.getRequestURI(),request.getRemoteAddr(),request.getMethod(),parameters!=null?JSON.toJSONString(parameters):null);
            Object result = point.proceed();
            long times = System.currentTimeMillis() - startTime;
            logger.debug("[request userName]:{},[request ip]:{}，[response result]URL:{},IP:{},Result:{},spend:{}" ,userName, ipAddr,request.getRequestURI() ,request.getRemoteAddr(),result!=null?JSON.toJSONString(result):null,times);
            return result;
        } catch (Exception e) {
            logger.error("api around " + point + " with exception:",e);
            throw e;
        }
    }
}