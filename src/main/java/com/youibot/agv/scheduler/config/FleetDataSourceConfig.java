package com.youibot.agv.scheduler.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.quartz.QuartzDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * @author: Tianshu.CHU
 * @Date: 2020/11/17 14:33
 * @Description: 数据源配置类
 */
@Configuration
public class FleetDataSourceConfig {

    @Primary
    @Bean(name="defaultDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.default")
    public DataSource defaultDateSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name="quartzDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.quartz")
    @QuartzDataSource
    public DataSource quartzDateSource() {
        return DataSourceBuilder.create().build();
    }

}
