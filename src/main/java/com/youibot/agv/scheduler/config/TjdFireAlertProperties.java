package com.youibot.agv.scheduler.config;

import java.util.Set;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;


/**
  * @ClassName: TjdFireAlertProperties
  * @Description: 台积电项目火灾配置项
  * <AUTHOR>
  * @date 2022年1月20日 上午10:54:36
  *
  */
@Data
@ConfigurationProperties(prefix = "additions.devices.modbus.firealert" , ignoreInvalidFields = true)
public class TjdFireAlertProperties {

	/**
	  * @Fields host : 主机
	  */
	private Set<String> host;
	
	
	/**
	  * @Fields port : 端口
	  */
	private int port;
	
	/**
	  * @Fields slaveId : 从站地址
	  */
	private int  slaveId;
	
	/**
	  * @Fields address : 读取的地址
	  */
	private int address;
}
