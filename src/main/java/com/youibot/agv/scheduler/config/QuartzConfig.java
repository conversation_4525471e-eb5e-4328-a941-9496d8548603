package com.youibot.agv.scheduler.config;


import org.quartz.spi.JobFactory;
import org.quartz.spi.TriggerFiredBundle;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.boot.autoconfigure.quartz.QuartzDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.scheduling.quartz.SpringBeanJobFactory;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Properties;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月16日 下午4:27:13
 */
@Configuration
public class QuartzConfig {


    @Bean(name="defaultDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.default")
    public DataSource defaultDateSource() { 
    	 return DataSourceBuilder.create().build(); 
     }

    @Bean(name="quartzDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.quartz")
	@QuartzDataSource
    public DataSource quartzDateSource() { 
    	return DataSourceBuilder.create().build(); 
     }

	//从quartz.properties文件中读取Quartz配置属性
	@Bean
	public Properties quartzProperties() throws IOException {
		PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
		propertiesFactoryBean.setLocation(new ClassPathResource("/quartz/quartz.properties"));
		propertiesFactoryBean.afterPropertiesSet();
		return propertiesFactoryBean.getObject();
	}

	public static class AutowiringSpringBeanJobFactory extends SpringBeanJobFactory implements ApplicationContextAware {
       //为spring实例化对象,并自动装配
		private transient AutowireCapableBeanFactory beanFactory;

		@Override
		public void setApplicationContext(final ApplicationContext context) {
			beanFactory = context.getAutowireCapableBeanFactory();
		}

		/*
		 * 将job实例交给spring ioc 托管 我们在Job实例类可以直接使用spring注入的调用被spring ioc管理的实例
		 * @Param bundle
		 * @return
		 * @throws Exception
		 */
		@Override
		protected Object createJobInstance(final TriggerFiredBundle bundle) throws Exception {
			final Object job = super.createJobInstance(bundle);
			beanFactory.autowireBean(job);
			return job;
		}
	}

	/*
	 *配置任务工厂实例
	 *@Param applicationContext spring 上下文
	 *@return
	 */
	@Bean
	public JobFactory jobFactory(ApplicationContext applicationContext) {
		AutowiringSpringBeanJobFactory jobFactory = new AutowiringSpringBeanJobFactory();
		jobFactory.setApplicationContext(applicationContext);
		return jobFactory;
	}

	/*
	 * 配置任务调度器 使用项目数据源为quartz数据源
	 * @Param jobFactory 自定义配置任务工厂
	 * @Param dataSource 数据源实例
	 * @return
	 * @throws Exception
	 */
	@Bean
	public SchedulerFactoryBean schedulerFactoryBean(JobFactory jobFactory) throws Exception {
		SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
		// 将spring管理job自定义工厂交由调度器维护
		schedulerFactoryBean.setJobFactory(jobFactory);
		// 设置覆盖已存在的任务
		schedulerFactoryBean.setOverwriteExistingJobs(true);
		// 项目启动完成后，等待2秒后开始执行调度器初始化
		schedulerFactoryBean.setStartupDelay(2);
		// 设置调度器自动运行
		schedulerFactoryBean.setAutoStartup(true);
		// 设置数据源，使用与项目统一数据源
		schedulerFactoryBean.setDataSource(quartzDateSource());
		// 设置上下文spring bean name
		schedulerFactoryBean.setApplicationContextSchedulerContextKey("applicationContext");
		schedulerFactoryBean.setQuartzProperties(quartzProperties());
		return schedulerFactoryBean;
	}


}


