package com.youibot.agv.scheduler.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月16日 下午4:27:13
 */
@Configuration
public class QuartzConfig {


    @Bean(name="defaultDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.default")
    public DataSource defaultDateSource() { 
    	 return DataSourceBuilder.create().build();
    }
}


