package com.youibot.agv.scheduler.config;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.appender.rewrite.RewritePolicy;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.config.plugins.PluginFactory;
import org.apache.logging.log4j.core.impl.ContextDataFactory;
import org.apache.logging.log4j.core.impl.Log4jLogEvent;
import org.apache.logging.log4j.message.Message;
import org.apache.logging.log4j.util.StringMap;


/** 日志中注入agvCode ,单独一个日志
 * <AUTHOR>
 *
 */
@Plugin(name = LogInjectorRewritePolicy.INJECT_AGV_CODE_POLICY, category = LogInjectorRewritePolicy.CORE, elementType = LogInjectorRewritePolicy.REWRITE_POLICY, printObject = true)
public final class LogInjectorRewritePolicy implements RewritePolicy {

	static final String REWRITE_POLICY = "rewritePolicy";
	static final String CORE = "Core";
	static final String INJECT_AGV_CODE_POLICY = "InjectAgvCodePolicy";
	private static final String CLOSE = "]";
	private static final String ROUTINGKEY = "ROUTINGKEY";
	private static final String AGV_CODE = "agvCode:[";

	@Override
	public LogEvent rewrite(final LogEvent event) {
		
	
		
		Message message = event.getMessage();
		String formattedMessage = message.getFormattedMessage();
		String agvCode = StringUtils.substringBetween( formattedMessage, AGV_CODE, CLOSE);
		if(StringUtils.isNotBlank(agvCode)) {
		
			Log4jLogEvent.Builder builder =  new Log4jLogEvent.Builder();
			StringMap contextData = ContextDataFactory.createContextData();
//			contextData.putValue(AGV_CODE, agvCode);
			contextData.putValue(ROUTINGKEY, agvCode);
			builder.setContextData(contextData);
			
			builder.setLoggerName(event.getLoggerName());
			builder.setMarker(event.getMarker());
			builder.setLoggerFqcn(event.getLoggerFqcn());
			builder.setLevel(event.getLevel());
			builder.setMessage(event.getMessage());
			builder.setThrown(event.getThrown());
			builder.setContextStack(event.getContextStack());
			builder.setThreadName(event.getThreadName());
			builder.setSource(event.getSource());
			builder.setTimeMillis(event.getTimeMillis());
			return builder.build();
		}
		
		return event; 
		
		
		
		
	}

	@PluginFactory
	public static LogInjectorRewritePolicy createPolicy() {
		return new LogInjectorRewritePolicy();
	}
}