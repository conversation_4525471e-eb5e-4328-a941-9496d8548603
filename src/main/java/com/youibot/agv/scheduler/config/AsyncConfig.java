package com.youibot.agv.scheduler.config;

import com.youibot.agv.scheduler.util.ApplicationUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 * @Author：yangpeilin
 * @Date: 2020/8/15 10:36
 */

@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {

    @Value("${agv.manage.async.executor.thread.core_pool_size}")
    private Integer corePoolSize;

    @Value("${agv.manage.async.executor.thread.max_pool_size}")
    private Integer maxPoolSize;

    @Value("${agv.manage.async.executor.thread.queue_capacity}")
    private Integer queueCapacity;

    @Value("${agv.manage.async.executor.thread.keep_alive_time}")
    private Integer keepAliveTime;

    @Value("${agv.manage.async.executor.thread.name_prefix}")
    private String namePrefix;

    @Bean(name = "asyncTaskExecutor")
    public Executor taskExecutor() {
        return ApplicationUtils.getThreadPoolExecutor(corePoolSize, maxPoolSize, queueCapacity, keepAliveTime, namePrefix, new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Value("${mission_call.manage.executor.thread.core_pool_size}")
    private Integer missionCorePoolSize;

    @Value("${mission_call.manage.executor.thread.max_pool_size}")
    private Integer missionMaxPoolSize;

    @Value("${mission_call.manage.executor.thread.queue_capacity}")
    private Integer missionQueueCapacity;

    @Value("${mission_call.manage.executor.thread.keep_alive_time}")
    private Integer missionKeepAliveTime;

    @Value("${mission_call.manage.executor.thread.name_prefix}")
    private String missionNamePrefix;


    @Bean(name = "missionCallTaskExecutor")
    public ThreadPoolTaskExecutor missionCallTaskExecutor() {
        return ApplicationUtils.getThreadPoolExecutor(missionCorePoolSize, missionMaxPoolSize, missionQueueCapacity, missionKeepAliveTime, missionNamePrefix, new ThreadPoolExecutor.CallerRunsPolicy());
    }

}

