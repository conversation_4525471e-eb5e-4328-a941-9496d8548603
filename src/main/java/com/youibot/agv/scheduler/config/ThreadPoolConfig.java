package com.youibot.agv.scheduler.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Bean("asyAopActionLog")
    public Executor asyAopActionLog() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(5);
        //配置最大线程数
        executor.setMaxPoolSize(10);
        //配置队列大小
        executor.setQueueCapacity(200);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("thread-action-log");
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }


    @Value("${position_message_push.manage.executor.thread.core_pool_size}")
    private Integer corePoolSize;

    @Value("${position_message_push.manage.executor.thread.max_pool_size}")
    private Integer maxPoolSize;

    @Value("${position_message_push.manage.executor.thread.queue_capacity}")
    private Integer queueCapacity;

    @Value("${position_message_push.manage.executor.thread.keep_alive_time}")
    private Integer keepAliveTime;

    @Value("${position_message_push.manage.executor.thread.name_prefix}")
    private String namePrefix;

    @Bean(name ="positionMessagePush")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置线程池核心容量
        executor.setCorePoolSize(corePoolSize);
        // 设置线程池最大容量
        executor.setMaxPoolSize(maxPoolSize);
        // 设置任务队列长度
        executor.setQueueCapacity(queueCapacity);
        // 设置线程超时时间
        executor.setKeepAliveSeconds(keepAliveTime);
        // 设置线程名称前缀
        executor.setThreadNamePrefix(namePrefix);
        // 设置任务丢弃后的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }


    @Value("${status_message_push.manage.executor.thread.core_pool_size}")
    private Integer statusCorePoolSize;

    @Value("${status_message_push.manage.executor.thread.max_pool_size}")
    private Integer statusMaxPoolSize;

    @Value("${status_message_push.manage.executor.thread.queue_capacity}")
    private Integer statusQueueCapacity;

    @Value("${status_message_push.manage.executor.thread.keep_alive_time}")
    private Integer statusKeepAliveTime;

    @Value("${status_message_push.manage.executor.thread.name_prefix}")
    private String statusNamePrefix;


    @Bean(name ="statusMessagePush")
    public ThreadPoolTaskExecutor statusTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置线程池核心容量
        executor.setCorePoolSize(statusCorePoolSize);
        // 设置线程池最大容量
        executor.setMaxPoolSize(statusMaxPoolSize);
        // 设置任务队列长度
        executor.setQueueCapacity(statusQueueCapacity);
        // 设置线程超时时间
        executor.setKeepAliveSeconds(statusKeepAliveTime);
        // 设置线程名称前缀
        executor.setThreadNamePrefix(statusNamePrefix);
        // 设置任务丢弃后的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }



}