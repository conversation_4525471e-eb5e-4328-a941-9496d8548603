package com.youibot.agv.scheduler.config;

import com.youibot.agv.scheduler.callbox.thread.CallBoxServiceThread;
import com.youibot.agv.scheduler.callbox.thread.CallBoxStatusHandleThread;
import com.youibot.agv.scheduler.device.door.thread.AirShowerDoorControlThread;
import com.youibot.agv.scheduler.device.door.thread.AutoDoorControlThread;
import com.youibot.agv.scheduler.engine.missionwork.MissionWorkReadThread;
import com.youibot.agv.scheduler.engine.missionwork.ModbusSyncVehicleStatusThread;
import com.youibot.agv.scheduler.entity.SystemConfig;
import com.youibot.agv.scheduler.entity.SystemWorkMode;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.MapGraphInfo;
import com.youibot.agv.scheduler.map.thread.AGVMapSyncThread;
import com.youibot.agv.scheduler.map.thread.LoadMapCacheThread;
import com.youibot.agv.scheduler.map.thread.MapUpdateThread;
import com.youibot.agv.scheduler.mqtt.thread.MqMessageHandleThread;
import com.youibot.agv.scheduler.mqtt.thread.MqttConnectionThread;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.SchedulePlanService;
import com.youibot.agv.scheduler.service.SystemConfigService;
import com.youibot.agv.scheduler.service.SystemWorkModeService;
import com.youibot.agv.scheduler.thread.SensorTriggerThread;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.thread.TimingTaskCompensationThread;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import com.youibot.agv.scheduler.webSocket.thread.ManualMoveHandleThread;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.youibot.agv.scheduler.constant.AGVConstant.MAP_STATUS_NORMAL;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SCHEDULER_LOGIN_STATUS_NO_LOGGED_IN;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SYSTEM_WORK_MODE_SCHEDULER;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月27日 下午1:06:05
 */
@Component
public class InitSystem implements ApplicationListener<ApplicationReadyEvent> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InitSystem.class);

    @Autowired
    private MissionWorkReadThread missionWorkReadThread;
    @Autowired
    private SchedulePlanService schedulePlanService;
    @Autowired
    private AGVService agvService;
    @Autowired
    private TimingTaskCompensationThread timingTaskCompensationThread;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private SystemWorkModeService systemWorkModeService;
    @Autowired
    private CallBoxServiceThread callBoxServiceThread;
    @Autowired
    private CallBoxStatusHandleThread callBoxStatusHandleThread;
    @Autowired
    private ManualMoveHandleThread manualMoveHandleThread;
    @Autowired
    private MqMessageHandleThread mqMessageHandleThread;
    @Autowired
    private AutoDoorControlThread autoDoorControlThread;
    @Autowired
    private AirShowerDoorControlThread airShowerDoorControlThread;
    @Autowired
    private ModbusSyncVehicleStatusThread modbusSyncVehicleStatusThread;
    @Autowired
    private LoadMapCacheThread loadMapCacheThread;
    @Autowired
    private AGVMapSyncThread agvMapSyncThread;
    @Autowired
    private MapUpdateThread mapUpdateThread;
    @Autowired
    private FtpConfig ftpConfig;

    private final SensorTriggerThread sensorTriggerThread = new SensorTriggerThread();

    private static final ScheduledExecutorService INIT_EXECUTOR = new ScheduledThreadPoolExecutor(3,
            new BasicThreadFactory.Builder().namingPattern("SensorTriggerThreadFactory-").build());


    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        this.init();
    }

    private void init() {
        try {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("initSystem..........");
            }
            //地图数据初始化
            this.initVehicle();
            mapUpdateThread.start();
            loadMapCacheThread.start();
            //手工控制处理线程
            manualMoveHandleThread.start();
            //vehicle 任务检测线程
            missionWorkReadThread.start();
            //时区初始化
            initTimeZone();
            // 初始化系统工作模式
            this.initSystemWorkMode();
            this.initMissionCallThread();
            //初始化mqtt回调消息处理线程
            mqMessageHandleThread.start();
            //初始化quartz线程
            schedulePlanService.initSchedulePlanBySystemRestart();
            //定时任务补偿：1.统计AGV信息。2.过期数据根据配置定期删除
            timingTaskCompensationThread.start();
            // 1s周期执行定时任务
            INIT_EXECUTOR.scheduleWithFixedDelay(sensorTriggerThread, 500, 1500, TimeUnit.MILLISECONDS);
            //开启自动门 控制线程
            autoDoorControlThread.start();
            //开启风淋门 控制线程
            airShowerDoorControlThread.start();
            //modbusSyncVehicleStatusThread.start();
            //启动同步地图线程
            agvMapSyncThread.start();
            this.initLoadAGVMaps();

        } catch (Exception e) {
            LOGGER.error("系统初始化报错, ", e);
        }
    }


    private void initTimeZone() {
        List<SystemConfig> all = systemConfigService.findAll();
        String timeZone = all.get(0).getTimeZone();
        TimeZone.setDefault(TimeZone.getTimeZone(timeZone));
        LOGGER.debug("init timeZone success...");

        FtpUtils.host = all.get(0).getFtpUrl();
        this.ftpConfig.setUrl(all.get(0).getFtpUrl());
    }


    private void initLoadAGVMaps() throws InterruptedException {
        Vehicle vehicle = VehicleUtils.getVehicle();
        //防止vehicle初始化线程还未获取到机器人状态增加5秒延时
        TimeUnit.SECONDS.sleep(5);
        DefaultVehicleStatus defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
        if (defaultVehicleStatus != null && defaultVehicleStatus.getMap() != null) {
            DefaultVehicleStatus.MapStatus mapStatus = defaultVehicleStatus.getMap();
            String current_map_id = mapStatus.getCurrent_map_id();
            if (current_map_id != null) {
                MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(current_map_id);
                if (mapGraphInfo == null || mapGraphInfo.getAgvMap() == null) {
                    return;
                }
                vehicle.setAgvMap(mapGraphInfo.getAgvMap());
                vehicle.setMapStatus(MAP_STATUS_NORMAL);
            }
        }
    }

    private void initVehicle() {
        VehicleUtils.getVehicle().initialize(agvService.selectOne());
    }

    private void initSystemWorkMode() {
        try {
            SystemWorkMode systemWorkMode = systemWorkModeService.getOne();
            if (SYSTEM_WORK_MODE_SCHEDULER.equals(systemWorkMode.getMode())) {
                systemWorkMode.setLoginStatus(SCHEDULER_LOGIN_STATUS_NO_LOGGED_IN);
                systemWorkMode.setMessage(null);
                new MqttConnectionThread().start(systemWorkMode, false);
            }
        } catch (Exception e) {
            LOGGER.error("系统启动时初始化系统工作模式出错, ", e);
        }
    }

    private void initMissionCallThread() {
        try {
            callBoxServiceThread.start();
            callBoxStatusHandleThread.start();
        } catch (Exception e) {
            LOGGER.error("创建呼叫盒线程出错, ", e);
        }
    }

}
