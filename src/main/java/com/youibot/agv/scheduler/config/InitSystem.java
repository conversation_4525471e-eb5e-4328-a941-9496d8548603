package com.youibot.agv.scheduler.config;

import com.youibot.agv.scheduler.device.callbox.CallBoxServiceStartThread;
import com.youibot.agv.scheduler.device.door.thread.AirShowerDoorControlThread;
import com.youibot.agv.scheduler.device.door.thread.AutoDoorControlThread;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.BlockCheckService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ConflictManager;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger;
import com.youibot.agv.scheduler.engine.scheduler.TjdIntegrityComponent;
import com.youibot.agv.scheduler.engine.scheduler.VehicleSchedulerService;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.entity.SystemConfig;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.service.MqttService;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.SchedulePlanService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.service.SystemConfigService;
import com.youibot.agv.scheduler.thread.*;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.youibot.agv.scheduler.mqtt.constants.MqttConstant.RE_LOGIN;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月27日 下午1:06:05
 */
@Order(Ordered.LOWEST_PRECEDENCE - 3)
@Component
public class InitSystem implements ApplicationListener<ApplicationReadyEvent> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InitSystem.class);

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    @Autowired
    private TimingTaskCompensationThread timingTaskCompensationThread;

    @Autowired
    private VehicleInitThread vehicleInitThread;

    @Autowired
    private VehicleSchedulerService vehicleSchedulerService;

    @Autowired
    private SchedulePlanService schedulePlanService;

    @Autowired
    private CallBoxServiceStartThread socketServiceStartThread;

    @Autowired
    private BlockCheckService blockCheckService;

    @Autowired
    private MqttService mqttService;

    @Autowired
    private PathPlanManger pathPlanManger;

    @Autowired
    private ConflictManager conflictManager;

    @Autowired
    private AutoDoorControlThread autoDoorControlThread;

    @Autowired
    private AirShowerDoorControlThread airShowerDoorControlThread;

    @Autowired
    private UpdateMapCacheTriggerThread updateMapCacheTriggerThread;

    @Autowired
    private MapUpdateThread mapUpdateThread;

    @Autowired
    private MapUpdateTriggerThread mapUpdateTriggerThread;
    
    @Autowired
    private TjdIntegrityComponent tjdIntegrityComponent;

    @Autowired
    private FtpConfig ftpConfig;

    private SensorTriggerThread sensorTriggerThread = new SensorTriggerThread();

    private static ScheduledExecutorService INIT_EXECUTOR = new ScheduledThreadPoolExecutor(3,
            new BasicThreadFactory.Builder().namingPattern("SensorTriggerThreadFactory-").build());

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        this.init();
    }

    private void init() {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("initSystem..........");
        }
        //双击热备：设置系统启动时间，提供给路径规划使用
        DateUtils.initSystemDate = new Date();
        //缓存加载线程
        updateMapCacheTriggerThread.start();
        //初始化在线状态vehicle
        vehicleInitThread.start();

        // 初始化路径规划服务
        this.pathPlanManger.start();
        // 初始化冲突检测服务。
        this.conflictManager.start();
        //初始化quartz线程
        schedulePlanService.initSchedulePlanBySystemRestart();
        //时区初始化
        initTimeZone();
        //定时任务补偿：1.统计AGV信息。2.过期数据根据配置定期删除
        timingTaskCompensationThread.start();

        // 初始化设备管理模块。
        //this.initDeviceServer();
//        socketServiceStartThread.starThread();

        LOGGER.debug("initSystem  initMQServer...");
        // 初始化消息服务。
        this.initMQServer();

        LOGGER.debug("initSystem  initScheduleExecutor...");

        // 1s周期执行定时任务
        INIT_EXECUTOR.scheduleWithFixedDelay(sensorTriggerThread, 500, 1500, TimeUnit.MILLISECONDS);

        LOGGER.debug("initSystem  initBlockCheckService...");
        //初始化绕障线程
        this.initBlockCheckService();
        // 台积电集成服务测试
        tjdIntegrityComponent.start();

        //开启自动门控制线程
//        autoDoorControlThread.start();
        //开启风淋门控制线程
//        airShowerDoorControlThread.start();
        //机器人调度服务开始。
//      vehicleSchedulerService.start();
        //地图更新线程
        updateMapData();

        LOGGER.debug("initSystem success...");

    }

    private void initTimeZone() {
        List<SystemConfig> all = systemConfigService.findAll();
        String timeZone = all.get(0).getTimeZone();
        TimeZone.setDefault(TimeZone.getTimeZone(timeZone));


        //设置ftp的 host 用于地图更新
        FtpUtils.host = all.get(0).getFtpUrl();
        this.ftpConfig.setUrl(all.get(0).getFtpUrl());

        //先查询数据库，如果用户没有配置的话，再看默认的配置文件
        if(StringUtils.isBlank(FtpUtils.host)){
            String host = AGVPropertiesUtils.getString("FTP.URL");

            FtpUtils.host = host;
            this.ftpConfig.setUrl(host);
        }
    }

    private void initBlockCheckService() {
        SchedulerConfig schedulerConfig = schedulerConfigService.findAll().get(0);
        blockCheckService.setBlockCheckInterval(schedulerConfig.getBlockCheckInterval());
        blockCheckService.setRemoveBlockInterval(schedulerConfig.getRemoveBlockInterval());

        Integer blockCheckEnable = schedulerConfig.getBlockCheckEnable();
        if (blockCheckEnable.equals(1)) {
            blockCheckService.startBlockCheckThread();
        }
    }

    private void initMQServer() {
        //创建MQTT名称为YOUIFleet_server的client
        MqttUtils.server = MqttUtils.createConnection(null, MqttConstant.SERVER_ID);
        /**
         * 开始重连
         */
        new Thread( () ->{
       		 LOGGER.debug("initSystem  initMQServer  fail retry...");
       		 while(  !connectedLogic()) {
       			  CommonUtils.sleepSlient(TimeUnit.SECONDS, 30);
       			LOGGER.debug("  fail retry connect mqtt...");
       			 if(Objects.isNull(MqttUtils.server) || !MqttUtils.server.isConnected()) {
       				 MqttUtils.server = MqttUtils.createConnection(null, MqttConstant.SERVER_ID);
       				 
       			 }
       		 }
       		
       	}).start();
      
        
    }

	/**
	 * mqtt 连接成功的逻辑
	 */
	private boolean connectedLogic() {
		
		 if(Objects.isNull(MqttUtils.server) || !MqttUtils.server.isConnected()) {
			return  false;
			 
		 }
		try {
            //vehicle初始化完毕后，才进行重新登录消息发送
            vehicleInitThread.join();
        } catch (InterruptedException e) {
            LOGGER.error("vehicleInitThread join fail:{}", e);
        }
        //发送重新登录消息给到所有连接上调度系统的机器人，消息保留
        MqttUtils.sendMqttMsg(RE_LOGIN, true, System.currentTimeMillis());
        //默认2分钟后清空保留的重新登录消息
        mqttService.clearReLoginMessage();
        return true;
	}


    private void updateMapData() {
        mapUpdateThread.start();
        mapUpdateTriggerThread.start();
    }

}
