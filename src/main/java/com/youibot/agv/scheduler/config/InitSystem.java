package com.youibot.agv.scheduler.config;

import com.youibot.agv.scheduler.engine.missionwork.MissionWorkReadThread;
import com.youibot.agv.scheduler.engine.pathplan.data.MapMatrixData;
import com.youibot.agv.scheduler.engine.pathplan.data.MapMatrixDataUtil;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.SystemConfig;
import com.youibot.agv.scheduler.mqtt.batch.BatchMqttConnectionThread;
import com.youibot.agv.scheduler.mqtt.thread.MqMessageHandleThread;
import com.youibot.agv.scheduler.mqtt.util.MqStatusUtils;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import com.youibot.agv.scheduler.vehicle.thread.TimingTaskCompensationThread;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;

import static com.youibot.agv.scheduler.constant.AGVConstant.ABNORMAL_STATUS_WORK;
import static com.youibot.agv.scheduler.constant.AGVConstant.TASK_STATUS_WORK;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月27日 下午1:06:05
 */
@Component
public class InitSystem implements ApplicationListener<ApplicationReadyEvent> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InitSystem.class);

    @Autowired
    private MissionWorkReadThread missionWorkReadThread;
    @Autowired
    private AGVService agvService;
    @Autowired
    private AGVMapService agvMapService;
    @Autowired
    private TimingTaskCompensationThread timingTaskCompensationThread;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private MapMatrixData mapMatrixData;
    @Autowired
    private VehiclePool vehiclePool;
    @Autowired
    private MqMessageHandleThread mqMessageHandleThread;
    @Autowired
    private MissionWorkService missionWorkService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        this.init();
    }

    private void init() {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("initSystem..........");
        }
        //地图数据初始化
        mapMatrixData.init();
        this.initVehicle();
        this.initEnableAGVMap();
        //vehicle 任务检测线程
//        missionWorkReadThread.start();
        //时区初始化
        initTimeZone();
        //定时任务补偿：1.统计AGV信息。2.过期数据根据配置定期删除
        timingTaskCompensationThread.start();
        // mq消息消费线程
//        mqMessageHandleThread.start();
        //把正在运行未完成的任务状态都改为异常
        this.updateMissionWork();
        // 消息推送线程
        MqStatusUtils.startThread();
        // 批量操作启动连接线程
        new BatchMqttConnectionThread().startThread();
    }


    private void updateMissionWork() {
        List<MissionWork> missionWorkList = missionWorkService.selectUnCompleteWork();
        missionWorkList.forEach(missionWork -> {
            try {
                missionWork.setMessage("仿真系统重启，任务执行失败!");
                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_FAULT);
                Vehicle vehicle = VehicleUtils.getVehicle(missionWork.getAgvId());
                if (vehicle != null) {
                    vehicle.setMissionWork(missionWork);
                    vehicle.setWorkStatus(TASK_STATUS_WORK);
                }
            } catch (Exception e) {
                LOGGER.error("系统重新启动修改执行中的任务状态为失败出错, ", e);
            }
        });
    }

    private void initTimeZone() {
        List<SystemConfig> all = systemConfigService.findAll();
        String timeZone = all.get(0).getTimeZone();
        TimeZone.setDefault(TimeZone.getTimeZone(timeZone));
        LOGGER.debug("init timeZone success...");
    }

    private void initEnableAGVMap() {
        List<AGVMap> enableAGVMaps = agvMapService.getEnableAGVMaps();
        if (CollectionUtils.isNotEmpty(enableAGVMaps)) {
            enableAGVMaps.forEach(agvMap -> {
                try {
                    MapMatrixDataUtil.addAGVMap(agvMap.getId());
                } catch (Exception e) {
                    LOGGER.error("系统启动时加载启用地图数据到缓存出错, ", e);
                }
            });
        }
    }

    private void initVehicle() {
        List<AGV> agvList = agvService.getAll(new HashMap<>());
        for (AGV agv : agvList) {
            try {
                Vehicle vehicle = VehicleUtils.createVehicle(agv);
                vehiclePool.attachVehicle(vehicle);
            } catch (Exception e) {
                LOGGER.error("系统初始化, 加载vehicle出错, agvId:{}", agv.getId(), e);
            }
        }
    }


}
