package com.youibot.agv.scheduler.config;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Configuration
@Order(1)
public class AGVPropertiesConfig {

	@Resource
	private Environment env;
	
	@PostConstruct
	public void setProperties() {
		AGVPropertiesUtils.setEnvironment(env);
	}
}
