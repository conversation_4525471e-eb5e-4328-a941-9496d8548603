package com.youibot.agv.scheduler.device.plc.constant;

/**
 * PLC地址 读写保持寄存器
 *
 * <AUTHOR>
 */
public interface PlcAddressConstant {


    Integer HOLDING_REGISTERS_START = 40001;

    /**
     * 控制模式
     */
    Integer CONTROL_MODE = 49901 - HOLDING_REGISTERS_START;

    /**
     * 地图状态
     */
    Integer MAP_STATUS = 49902 - HOLDING_REGISTERS_START;

    /**
     * 定位状态
     */
    Integer POSITION_STATUS = 49903 - HOLDING_REGISTERS_START;

    /**
     * 任务状态
     */
    Integer WORK_STATUS = 49904 - HOLDING_REGISTERS_START;

    /**
     * 异常状态
     */
    Integer ABNORMAL_STATUS = 49905 - HOLDING_REGISTERS_START;

    /**
     * 连接状态
     */
    Integer CONNECT_STATUS = 49906 - HOLDING_REGISTERS_START;

    /**
     * 当前电量
     */
    Integer ELECTRICITY = 49907 - HOLDING_REGISTERS_START;

    /**
     * 任务执行状态
     */
    Integer MISSION_WORK_STATUS = 49908 - HOLDING_REGISTERS_START;

    /**
     * 急停状态
     */
    Integer EMC_STATUS = 49909 - HOLDING_REGISTERS_START;

    /**
     * 急停原因
     */
    Integer EMC_REASON = 49910 - HOLDING_REGISTERS_START;

    /**
     * 异常码 因为异常码是6位数，但是modbus保持寄存器只有2个字节，因此将异常码拆为上下两部分，第一个地址放3位数字，第二个地址放3位数字
     */
    Integer ERROR_CODE = 49911 - HOLDING_REGISTERS_START;

    /**
     * 一键清错
     */
    Integer ONE_KEY_RESET = 49917 - HOLDING_REGISTERS_START;

    /**
     * 停止所有任务
     */
    Integer ALL_WORK_STOP = 49918 - HOLDING_REGISTERS_START;
}
