package com.youibot.agv.scheduler.device.callbox;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.TriggerSelector;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.service.TriggerSelectorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.List;

import static com.youibot.agv.scheduler.device.callbox.CallBoxCache.*;
import static com.youibot.agv.scheduler.device.callbox.constant.CallBoxConstant.*;
import static com.youibot.agv.scheduler.device.callbox.constant.MessageConstant.CLOSE_ALL_LIGHTS;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SUCCESS;

/**
 * 呼叫盒服务处理线程
 *
 * @author: huangguanxin
 * @date: 2021-01-07 15:50
 */
@Component
@Scope("prototype")
public class ReceiveHandlerThread implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ReceiveHandlerThread.class);

    @Autowired
    private MissionWorkService missionWorkService;
    @Autowired
    private TriggerSelectorService triggerSelectorService;

    private Socket socket;

    public synchronized void start(Socket socket) {
        this.socket = socket;
        new Thread(this).start();
    }

    @Override
    public void run() {
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            inputStream = socket.getInputStream();
            outputStream = socket.getOutputStream();
            // 读取客户端数据
            int len = 0;
            byte[] buf = new byte[5];
            while ((len = inputStream.read(buf)) != -1) {
                ReceiveMessage receiveMessage = CallBoxUtils.readFromStream(buf);
                if (receiveMessage != null) {
                    logger.debug("呼叫盒上报信息 message:{} ", JSONObject.toJSONString(receiveMessage));
                    // 查询盒子状态放到缓存
                    if (READ_BOX_STATUS.equals(receiveMessage.getFunctionCode())) {
                        boxLastStatusMessage.put(receiveMessage.getAddress(), receiveMessage);
                    }
                    //socket缓存
                    clientSocketMap.put(receiveMessage.getAddress(), socket);

                    try {
                        handlerRequest(receiveMessage, outputStream);
                    } catch (Exception e) {
                        logger.error("呼叫盒报文业务处理出错: " + e.getMessage());
                        if (e instanceof IOException) {
                            throw e;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("呼叫盒服务器运行异常: " + e.getMessage());
        } finally {
            closeResources(socket, inputStream, outputStream);
        }
    }


    private void handlerRequest(ReceiveMessage receiveMessage, OutputStream outputStream) throws Exception {
        if (receiveMessage == null) {
            return;
        }
        // 按钮状态查询 由调用者处理
        if (READ_BOX_STATUS.equals(receiveMessage.getFunctionCode())) {
            return;
        }

        // 上电通知 可获取地址信息，需要把灯关闭
        if (FUNCTION_ADDRESS_REPORT.equals(receiveMessage.getFunctionCode())
                || FUNCTION_ADDRESS_REPORT_2.equals(receiveMessage.getFunctionCode())) {
            byte[] message = CLOSE_ALL_LIGHTS;
            message[1] = (byte) (0XFF & receiveMessage.getAddress());
            CallBoxUtils.write2Stream(outputStream, message);

            //通过设备按钮获取该呼叫盒的所有触发器数据
            this.initLightStatus(receiveMessage.getAddress());
        }

        // 按钮触发 判断任务是否执行完成
        if (FUNCTION_STATUS_CHANGE.equals(receiveMessage.getFunctionCode())) {
            logger.debug("呼叫盒按钮触发,设备地址:{},按钮地址:{}", receiveMessage.getAddress(), receiveMessage.getStatusCode());
            List<TriggerSelector> triggerSelectors = triggerSelectorService.selectByTriggerType(2);
            TriggerSelector triggerSelector = triggerSelectors.stream()
                    .filter(trigger -> trigger.getDeviceAddress().equals(receiveMessage.getAddress()) && trigger.getButtonAddress().equals(receiveMessage.getStatusCode()) && trigger.getIsDisabled() == 0).findFirst().orElse(null);
            if (triggerSelector == null) return;
            MissionWork missionWork = this.findMissionWork(receiveMessage.getAddress(), receiveMessage.getStatusCode());
            //查询当前触发按钮地址绑定的触发器是否已触发任务
            if (missionWork == null) {
                createMissionWorkByTriggerAddress(receiveMessage.getAddress(), receiveMessage.getStatusCode());
            } else {
                if (MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus()) || MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {
                    createMissionWorkByTriggerAddress(receiveMessage.getAddress(), receiveMessage.getStatusCode());
                } else {
                    byte[] message = CallBoxUtils.getOpenLightMessage(receiveMessage.getAddress(), receiveMessage.getStatusCode());
                    message[1] = (byte) (0XFF & receiveMessage.getAddress());
                    Thread.sleep(500);
                    CallBoxUtils.write2Stream(socket.getOutputStream(), message);
                    logger.debug("本次呼叫会忽略，当前呼叫盒地址:{},按钮地址:{},创建的作业未执行完成", receiveMessage.getAddress(), receiveMessage.getStatusCode());
                }
            }
            // 记录当前按钮触发
            buttonCache.put(receiveMessage.getStatusCode(), System.currentTimeMillis());

            Thread.sleep(500);
        }
    }

    private void initLightStatus(Integer deviceAddress) throws InterruptedException, IOException {
        List<TriggerSelector> triggerSelectors = triggerSelectorService.selectByDeviceAddress(deviceAddress);
        if (!CollectionUtils.isEmpty(triggerSelectors)) {
            for (TriggerSelector triggerSelector : triggerSelectors) {
                MissionWork missionWork = this.findMissionWork(triggerSelector.getDeviceAddress(), triggerSelector.getButtonAddress());
                if (missionWork != null && !MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {
                    Thread.sleep(500);
                    byte[] openMessage = CallBoxUtils.getOpenLightMessage(triggerSelector.getDeviceAddress(), triggerSelector.getButtonAddress());
                    CallBoxUtils.write2Stream(socket.getOutputStream(), openMessage);
                }
            }
        }
    }

    private MissionWork findMissionWork(Integer address, Integer buttonAddress) {
        Example example = new Example(TriggerSelector.class);
        example.createCriteria()
                .andEqualTo("deviceAddress", address)
                .andEqualTo("buttonAddress", buttonAddress)
        ;
        TriggerSelector triggerSelector = triggerSelectorService.selectOneByExample(example);
        if (triggerSelector == null) {
            return null;
        }
        return missionWorkService.getMissionWorkByTriggerSelectorId(triggerSelector.getId());
    }


    /*private void createMissionWork(Integer address) throws Exception {
        MissionCall missionCall = missionCallService.selectById(callId);
        MissionWork missionWork = missionWorkService.createMissionWorkByCode(missionCall.getMissionId(), callId);
        if (missionWork == null) {
            logger.error("通过呼叫盒创建任务失败 call:[{}]", callId);
            return;
        }
        byte[] message = CallBoxUtils.getOpenLightMessage(callId, CALL_CODE);
        message[1] = (byte) (0XFF & address);
        Thread.sleep(500);
        CallBoxUtils.write2Stream(socket.getOutputStream(), message);
        logger.debug("通过呼叫盒创建任务{}", JSONObject.toJSONString(missionWork));
        callBoxWorkMap.put(callId, missionWork.getId());
    }*/

    private void createMissionWorkByTriggerAddress(Integer deviceAddress, Integer buttonAddress) {
        Example example = new Example(TriggerSelector.class);
        example.createCriteria()
                .andEqualTo("deviceAddress", deviceAddress)
                .andEqualTo("buttonAddress", buttonAddress);
        TriggerSelector triggerSelector = triggerSelectorService.selectOneByExample(example);
        if (triggerSelector == null) {
            logger.warn("该设备地址:{},按钮地址:{},还未绑定触发器,不进行作业创建", deviceAddress, buttonAddress);
            return;
        }
        MissionWork missionWork = missionWorkService.createByTrigger(triggerSelector);

        byte[] message = CallBoxUtils.getOpenLightMessage(deviceAddress, buttonAddress);
        message[1] = (byte) (0XFF & deviceAddress);
        try {
            Thread.sleep(500);
            CallBoxUtils.write2Stream(socket.getOutputStream(), message);
            logger.debug("通过呼叫器>>触发器创建作业{}", JSONObject.toJSONString(missionWork));
            callBoxWorkMap.put(triggerSelector.getDeviceAddress(), missionWork.getId());
        } catch (Exception e) {

        }
    }

    /**
     * 校验盒子地址
     * 如果地址不相同 则修改设备地址
     *//*
    private void checkMissionAddress(Integer address) throws Exception {
        Integer dbAddress = callBoxAddressMap.get(callId);
        if (dbAddress == null) {
            return;
        }
        if (!address.equals(dbAddress)) {
            byte[] message = CHANGE_ADDRESS;
            // 原来地址
            message[1] = (byte) (0XFF & address);
            //修改到地址
            message[3] = (byte) (0XFF & dbAddress);
            Thread.sleep(1000);
            CallBoxUtils.write2Stream(socket.getOutputStream(), message);
            try {
                CallBoxUtils.closeSocket(socket);
            } catch (Exception e) {
                logger.error("修改地址上报出错{}", e);
            }
        }
    }
*/
    private void closeResources(Socket socket, InputStream inputStream, OutputStream outputStream) {
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (Exception e) {
                inputStream = null;
                logger.debug("服务端 finally 异常:" + e.getMessage());
            }
        }
        if (outputStream != null) {
            try {
                outputStream.close();
            } catch (Exception e) {
                outputStream = null;
                logger.debug("服务端 finally 异常:" + e.getMessage());
            }
        }
        if (socket != null) {
            try {
                socket.close();
            } catch (Exception e) {
                socket = null;
                logger.debug("服务端 finally 异常:" + e.getMessage());
            }
        }

    }


}

