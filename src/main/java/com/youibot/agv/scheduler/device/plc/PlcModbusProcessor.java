package com.youibot.agv.scheduler.device.plc;

import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.youibot.agv.scheduler.constant.AGVConstant;
import com.youibot.agv.scheduler.constant.ModbusConstant;
import com.youibot.agv.scheduler.device.plc.constant.MissionWorkStatusEnum;
import com.youibot.agv.scheduler.engine.manager.modbus.JLibModbusUtils;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

import static com.youibot.agv.scheduler.constant.AGVConstant.MANUAL_CONTROL_MODE;
import static com.youibot.agv.scheduler.constant.AGVConstant.TASK_STATUS_FREE;
import static com.youibot.agv.scheduler.device.plc.constant.PlcAddressConstant.*;

/**
 * 米松-派讯定制需求：上集成PLC通过modbus与compass进行交互，包含以下功能
 * 1、上报状态（地图状态、定位状态、任务状态、异常状态、连接状态、控制模式、当前电量、 任务状态、急停状态、急停原因、异常状态（err_code））
 * 2、能够执行一键清错
 * 3、能够停止所有任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PlcModbusProcessor implements CommandLineRunner {

    /**
     * 间隔时间 1s
     */
    private static final int INTERVAL_TIME = 1;

    @Value("${MODBUS.LOCAL_IP}")
    private String plcIp;

    @Value("${MODBUS.LOCAL_PORT}")
    private Integer plcPort;

    @Autowired
    private MissionWorkService missionWorkService;

    @Override
    @Async
    public void run(String... args) throws Exception {

        log.info("PLC - MODBUS处理线程启动");

        ModbusMaster master = null;

        while (true) {

            TimeUnit.SECONDS.sleep(INTERVAL_TIME);

            try {
                //plc modbus连接
                if (Objects.isNull(master) || !master.isConnected()) {
                    try {
                        master = JLibModbusUtils.createModbusMaster(plcIp, plcPort);
                        log.info("plc modbus连接成功 plcIp = {}, plcPort = {}", plcIp, plcPort);

                        countMap.get(1).set(0);
                    } catch (Exception e) {
                        if (print.test(1)) {
                            log.error("plc modbus连接失败 plcIp = {}, plcPort = {}, msg = {}", plcIp, plcPort, e.getMessage());
                        }
                        continue;
                    }
                }


                Vehicle vehicle = VehicleUtils.getVehicle();
                int slaveId = ModbusConstant.SLAVE_ID;

                //上报状态
                try {
                    master.writeSingleRegister(slaveId, CONTROL_MODE, vehicle.getControlMode());
                    master.writeSingleRegister(slaveId, MAP_STATUS, vehicle.getMapStatus());
                    master.writeSingleRegister(slaveId, POSITION_STATUS, Objects.nonNull(vehicle.getPositionStatus()) ? vehicle.getPositionStatus().getPos_islocated() : 0);
                    master.writeSingleRegister(slaveId, WORK_STATUS, vehicle.getWorkStatus());
                    master.writeSingleRegister(slaveId, ABNORMAL_STATUS, vehicle.getAbnormalStatus());
                    master.writeSingleRegister(slaveId, CONNECT_STATUS, vehicle.getConnectStatus());
                    if (Objects.nonNull(vehicle.getDefaultVehicleStatus()) && Objects.nonNull(vehicle.getDefaultVehicleStatus().getBattery())) {
                        master.writeSingleRegister(slaveId, ELECTRICITY, vehicle.getDefaultVehicleStatus().getBattery().getBattery_value().intValue());
                    } else {
                        master.writeSingleRegister(slaveId, ELECTRICITY, 0);
                    }
                    if (Objects.isNull(vehicle.getMissionWork())) {
                        //如果存在已创建未执行的作业，且还未分配给小车，也需要返回该作业的状态
                        List<MissionWork> missionWorks = missionWorkService.selectByStatus(MissionWorkStatusEnum.CREATE.name());
                        int value = CollectionUtils.isEmpty(missionWorks) ? MissionWorkStatusEnum.NONE.getValue() : MissionWorkStatusEnum.CREATE.getValue();
                        master.writeSingleRegister(slaveId, MISSION_WORK_STATUS, value);
                    } else {
                        master.writeSingleRegister(slaveId, MISSION_WORK_STATUS, MissionWorkStatusEnum.getValueByStatus(vehicle.getMissionWork().getStatus()));
                    }
                    if (Objects.nonNull(vehicle.getDefaultVehicleStatus()) && Objects.nonNull(vehicle.getDefaultVehicleStatus().getEmec())) {
                        DefaultVehicleStatus.EmecStatus emec = vehicle.getDefaultVehicleStatus().getEmec();
                        master.writeSingleRegister(slaveId, EMC_STATUS, emec.getEmc_status());
                        if (emec.getEmc_reason() != null && emec.getEmc_reason().length > 0) {
                            master.writeSingleRegister(slaveId, EMC_REASON, emec.getEmc_reason()[0]);
                        } else {
                            master.writeSingleRegister(slaveId, EMC_REASON, 0);
                        }
                    } else {
                        master.writeSingleRegister(slaveId, EMC_STATUS, 0);
                        master.writeSingleRegister(slaveId, EMC_REASON, 0);
                    }
                    if (!CollectionUtils.isEmpty(vehicle.getErrorCodes())) {
                        int index=0;
                        for (Integer errorCode : vehicle.getErrorCodes()) {
                            String str = errorCode.toString();
                            int pre = Integer.parseInt(str.substring(0, str.length() - 3));
                            int next = Integer.parseInt(str.substring(str.length() - 3));
                            //将两部分写在相邻的两个保持寄存器
                            master.writeSingleRegister(slaveId, ERROR_CODE + index++, pre);
                            master.writeSingleRegister(slaveId, ERROR_CODE + index++, next);
                            //最多写入三个异常码
                            if (index >= 6) {
                                break;
                            }
                        }
                    } else {
                        for (int i = 0; i < 6; i++) {
                            master.writeSingleRegister(slaveId, ERROR_CODE + i, 0);
                        }
                    }

                    countMap.get(2).set(0);
                } catch (Exception e) {
                    if (print.test(2)) {
                        log.error("上报状态失败 msg = {}", e.getMessage(), e);
                    }
                }

                //一键清错
                try {
                     if (Objects.equals(master.readHoldingRegisters(slaveId, ONE_KEY_RESET, 1)[0], 1)) {
                        if (Objects.equals(AGVConstant.CONNECT_STATUS_SUCCESS, vehicle.getConnectStatus())) {
                            if(TASK_STATUS_FREE.equals(vehicle.getWorkStatus()) && MANUAL_CONTROL_MODE.equals(vehicle.getControlMode())) {
                                vehicle.resetManualTask();
                            } else {
                                vehicle.oneKeyReset();
                            }
                        } else {
                            log.warn("机器人未连接成功，无法一键清错");
                        }
                    }

                    countMap.get(3).set(0);
                } catch (Exception e) {
                    if (print.test(3)) {
                        log.error("一键清错失败, msg = {}", e.getMessage(), e);
                    }
                } finally {
                    //不管是否清错成功都应置为0，表示已经操作
                    try {
                        master.writeSingleRegister(slaveId, ONE_KEY_RESET, 0);
                    } catch (Exception e) {
                        if (print.test(3)) {
                            log.error("一键清错失败, msg = {}", e.getMessage(), e);
                        }
                    }
                }

                //停止所有任务
                try {
                    if (Objects.equals(master.readHoldingRegisters(slaveId, ALL_WORK_STOP, 1)[0], 1)) {
                        missionWorkService.shutdownByUnComplete();
                    }

                    countMap.get(4).set(0);
                } catch (Exception e) {
                    if (print.test(4)) {
                        log.error("停止所有任务失败, msg = {}", e.getMessage(), e);
                    }
                } finally {
                    try {
                        master.writeSingleRegister(slaveId, ALL_WORK_STOP, 0);
                    } catch (Exception e) {
                        if (print.test(4)) {
                            log.error("停止所有任务失败, msg = {}", e.getMessage(), e);
                        }
                    }
                }

            } catch (Exception e) {
                log.error("plc modbus处理失败 msg = {}", e.getMessage(), e);
            }
        }

    }

    /**
     * 每10次报错打印1次
     */
    private static final int MAX_COUNT = 10;
    private Map<Integer, AtomicInteger> countMap = new ConcurrentHashMap<Integer, AtomicInteger>() {{
        put(1, new AtomicInteger(0));
        put(2, new AtomicInteger(0));
        put(3, new AtomicInteger(0));
        put(4, new AtomicInteger(0));
    }};

    /**
     * 是否打印判断
     */
    Predicate<Integer> print = key -> {
        int c = countMap.get(key).incrementAndGet();
        if (c >= MAX_COUNT) {
            countMap.get(key).set(0);
        }
        return c == 1;
    };

    /**
     * 减少日志打印
     * @param countKey 区分不同的日志
     * @param doTry 正常执行的方法
     * @param doCatch 异常时候执行的方法
     * @param errorInfo 异常时打印的日志
     * @param logParams 异常时打印日志的参数
     */
    private boolean logPrint(Integer countKey, Runnable doTry, Runnable doCatch, String errorInfo, Object...logParams) {
        boolean flag = true;
        try {
            countMap.putIfAbsent(countKey, new AtomicInteger(0));
            doTry.run();
            countMap.get(countKey).set(0);
        } catch (Exception e) {
            flag = false;
            int c = countMap.get(countKey).incrementAndGet();
            if (c >= MAX_COUNT) {
                countMap.get(countKey).set(0);
            }
            if (c == 1) {
                log.error(errorInfo, logParams);
                log.error("", e);
            }
            if (Objects.nonNull(doCatch)) {
                doCatch.run();
            }
        }
        return flag;
    }
}
