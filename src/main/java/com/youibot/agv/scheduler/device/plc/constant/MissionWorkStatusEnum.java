package com.youibot.agv.scheduler.device.plc.constant;

import com.youibot.agv.scheduler.exception.YOUIFleetException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 状态:
 * CREATE:创建(未执行)
 * START:开始执行
 * WAIT:等待(继续)执行
 * RUNNING:执行中
 * SUCCESS:执行成功
 * FAULT:执行错误
 * PAUSE:暂停
 * BEING_PAUSE:暂停中
 * BEING_RESUME:恢复中
 * SHUTDOWN:已停止
 * BEING_SHUTDOWN:停止中
 * WAITINPUT:等待输入
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MissionWorkStatusEnum {

    NONE(0, "无任务"),
    CREATE(1, "创建(未执行)"),
    START(2, "开始执行"),
    WAIT(3, "等待(继续)执行"),
    RUNNING(4, "执行中"),
    SUCCESS(5, "执行成功"),
    FAULT(6, "执行错误"),
    PAUSE(7, "暂停"),
    BEING_PAUSE(8, "暂停中"),
    BEING_RESUME(9, "恢复中"),
    SHUTDOWN(10, "已停止"),
    BEING_SHUTDOWN(11, "停止中"),
    WAITINPUT(12, "等待输入")
    ;

    private int value;

    private String desc;

    public static int getValueByStatus(String status) {
        return Stream.of(MissionWorkStatusEnum.values())
                .filter(i -> i.name().equals(status)).findFirst()
                .orElseThrow(() -> new YOUIFleetException("作业状态 " + status + "不存在对应的值")).getValue();
    }

}
