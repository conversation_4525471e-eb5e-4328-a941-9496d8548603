package com.youibot.agv.scheduler.device.door.util;

import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.AutoDoor;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2021/11/12 17:34
 */
public class AutoDoorUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoDoorUtils.class);

    private static PathService pathService = ApplicationUtils.getBean(PathService.class);
    private static PathPlanService pathPlanService = ApplicationUtils.getBean(PathPlanService.class);
    private static LocationService locationService = ApplicationUtils.getBean(LocationService.class);

    /**
     * 判断自动门是否将被使用或正在使用
     *
     * @param autoDoor
     * @return
     */
    public static boolean isUseAutoDoor(AutoDoor autoDoor) {
        return isWillUseAutoDoor(autoDoor) || isInUseAutoDoor(autoDoor);
    }

    /**
     * 判断自动门是否将被使用
     *
     * @param autoDoor
     * @return
     */
    public static boolean isWillUseAutoDoor(AutoDoor autoDoor) {
        List<Path> paths = CollectionUtils.isEmpty(autoDoor.getPaths()) ? pathService.selectByAutoDoorId(autoDoor.getId()) : autoDoor.getPaths();
        if (CollectionUtils.isEmpty(paths)) {
            throw new ExecuteException("自动(风淋)门未绑定路径");
        }
        Vehicle vehicle = VehicleUtils.getVehicle();
        int doorOpenAdvanceTime = AGVPropertiesUtils.getInt("AUTO_DOOR.OPEN_ADVANCE_TIME");
        for (Path path : paths) {
            List<String> planPathIds = vehicle.getSidePaths().stream().map(SidePath::getPathId).collect(Collectors.toList());
            List<String> usedPathIds = vehicle.getUsedSidePaths().stream().map(SidePath::getPathId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(planPathIds) && planPathIds.contains(path.getId())
                    && (CollectionUtils.isEmpty(usedPathIds) || !usedPathIds.contains(path.getId()))) {
                Double startMarkerCost = pathPlanService.calculateNavigationCost(path.getStartMarkerId());
//              LOGGER.debug("机器人距离自动门路径起点时间:{}s", startMarkerCost);
                if (startMarkerCost != null && startMarkerCost <= doorOpenAdvanceTime) {
                    //如果有机器人规划了本段路径、并且行驶到本段路径的起点预估时间小于N秒
                    return true;
                }
                Double endMarkerCost = pathPlanService.calculateNavigationCost(path.getEndMarkerId());
//              LOGGER.debug("机器人距离自动门路径终点时间:{}s", endMarkerCost);
                if (endMarkerCost != null && endMarkerCost <= doorOpenAdvanceTime) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断自动门是否正在使用
     *
     * @param autoDoor
     * @return
     */
    public static boolean isInUseAutoDoor(AutoDoor autoDoor) {
        List<Path> paths = CollectionUtils.isEmpty(autoDoor.getPaths()) ? pathService.selectByAutoDoorId(autoDoor.getId()) : autoDoor.getPaths();
        if (CollectionUtils.isEmpty(paths)) {
            throw new ExecuteException("自动(风淋)门未绑定路径");
        }
        boolean inUseAutoDoor = false;
        for (Path path : paths) {
            //如果机器人在该段路径上(在路径上没行驶也人为在使用, 不能让自动门关门导致碰撞)
            VehicleLocation vehicleLocation = locationService.getVehicleLocation();
            if (vehicleLocation != null) {
                List<SidePath> sidePaths = vehicleLocation.getSidePaths();
                if (!CollectionUtils.isEmpty(sidePaths)) {
                    List<SidePath> filterSidePaths = sidePaths.stream().filter(s -> path.getId().equals(s.getPathId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterSidePaths)) {
                        inUseAutoDoor = true;
                        break;
                    }
                }
            }
        }
        return inUseAutoDoor;
    }
}
