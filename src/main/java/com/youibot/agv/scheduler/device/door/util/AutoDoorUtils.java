package com.youibot.agv.scheduler.device.door.util;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.AutoDoor;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2021/11/12 17:34
 */
public class AutoDoorUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoDoorUtils.class);

    private static PathService pathService = (PathService) ApplicationUtils.getBean("pathServiceImpl");
    private static PathPlanService pathPlanService = (PathPlanService) ApplicationUtils.getBean("pathPlanServiceImpl");
    private static VehiclePool vehiclePool = (VehiclePool) ApplicationUtils.getBean("defaultVehiclePool");
    private static LocationService locationService = (LocationService) ApplicationUtils.getBean("locationServiceImpl");

    /**
     * 判断自动门是否将被使用或正在使用
     *
     * @param autoDoor
     * @return
     */
    public static boolean isUseAutoDoor(AutoDoor autoDoor) {
        return isWillUseAutoDoor(autoDoor) || isInUseAutoDoor(autoDoor);
    }

    /**
     * 判断自动门是否将被使用
     *
     * @param autoDoor
     * @return
     */
    public static boolean isWillUseAutoDoor(AutoDoor autoDoor) {
        boolean useAutoDoor = false;
        List<Path> paths = CollectionUtils.isEmpty(autoDoor.getPaths()) ? pathService.selectByAutoDoorId(autoDoor.getId()) : autoDoor.getPaths();
        if (CollectionUtils.isEmpty(paths)) {
            throw new ExecuteException("自动(风淋)门未绑定路径");
        }
        int doorOpenAdvanceTime = AGVPropertiesUtils.getInt("AUTO_DOOR.OPEN_ADVANCE_TIME");
        List<Vehicle> vehicles = vehiclePool.getAll();
        for (Path path : paths) {
            for (Vehicle vehicle : vehicles) {
                List<String> planedPathIds = vehicle.getPlanedPaths().stream().map(Path::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(planedPathIds) && planedPathIds.contains(path.getId())) {
                    Double startMarkerCost = pathPlanService.calculateNavigationCost(vehicle.getId(), path.getStartMarkerId());
//                  LOGGER.debug("机器人距离自动门路径起点时间:{}s", startMarkerCost);
                    if (startMarkerCost != null && startMarkerCost <= doorOpenAdvanceTime) {
                        //如果有机器人规划了本段路径、并且行驶到本段路径的起点预估时间小于N秒
                        useAutoDoor = true;
                        break;
                    }
                    Double endMarkerCost = pathPlanService.calculateNavigationCost(vehicle.getId(), path.getEndMarkerId());
//                  LOGGER.debug("机器人距离自动门路径终点时间:{}s", endMarkerCost);
                    if (endMarkerCost != null && endMarkerCost <= doorOpenAdvanceTime) {
                        //如果有机器人规划了本段路径、并且行驶到本段路径的终点预估时间小于N秒
                        useAutoDoor = true;
                        break;
                    }
                }
            }
        }
        return useAutoDoor;
    }

    /**
     * 判断自动门是否正在使用
     *
     * @param autoDoor
     * @return
     */
    public static boolean isInUseAutoDoor(AutoDoor autoDoor) {
        boolean willUseAutoDoor = false;
        List<Path> paths = CollectionUtils.isEmpty(autoDoor.getPaths()) ? pathService.selectByAutoDoorId(autoDoor.getId()) : autoDoor.getPaths();
        if (CollectionUtils.isEmpty(paths)) {
            throw new ExecuteException("自动(风淋)门未绑定路径");
        }
        List<Vehicle> vehicles = vehiclePool.getAll();
        for (Path path : paths) {
            for (Vehicle vehicle : vehicles) {
                List<String> runningPathIds = vehicle.getRunningPaths().stream().map(Path::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(runningPathIds) && runningPathIds.contains(path.getId())) {//如果机器人正在行驶本段路径
                    LOGGER.debug("agvCode:[{}],event:[自动/风淋门资源申请],content:{}", vehicle.getId(),"机器人将要行驶的路径中包含自动/风淋门所在的路径:"+autoDoor.toString());
                    willUseAutoDoor = true;
                    break;
                }

                //如果机器人在该段路径上(在路径上没行驶也人为在使用, 不能让自动门关门导致碰撞)
                VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicle.getId());
                if (vehicleLocation != null) {
                    List<SidePath> sidePaths = vehicleLocation.getSidePaths();
                    if (!CollectionUtils.isEmpty(sidePaths)) {
                        List<SidePath> filterSidePaths = sidePaths.stream().filter(s -> path.getId().equals(s.getPathId())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(filterSidePaths)) {
                            LOGGER.debug("agvCode:[{}],event:[自动/风淋门资源申请],content:{}", vehicle.getId(),"机器人当前所在路径中包含自动/风淋门所在的路径:"+autoDoor.toString());
                            willUseAutoDoor = true;
                            break;
                        }
                    }
                }
            }
        }
        return willUseAutoDoor;
    }
}
