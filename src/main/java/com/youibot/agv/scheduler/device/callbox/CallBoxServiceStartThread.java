package com.youibot.agv.scheduler.device.callbox;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.ServerSocket;
import java.net.Socket;

/**
 * 呼叫盒子socket服务端
 *
 * @author: huangguanxin
 * @date: 2021-01-07 15:32
 */
@Component
public class CallBoxServiceStartThread implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(CallBoxServiceStartThread.class);

    @Autowired
    private MissionWorkService missionWorkService;

    private Thread thread;

    public void starThread() {
        if (thread == null) {
            thread = new Thread(this);
            thread.start();
        }
    }

    @Override
    public void run() {
        /*// 启动呼叫任务监听线程
        startMonitorWorkThread();
        ServerSocket serverSocket = null;
        while (true) {
            try {
                if (serverSocket == null) {
                    serverSocket = new ServerSocket(AGVPropertiesUtils.getInteger("MISSION.CALL.PORT"));
                    logger.debug("呼叫盒socket服务启动中。。。");
                }
                //从请求队列中取出一个连接
                Socket client = serverSocket.accept();
                String hostAddress = client.getInetAddress().getHostAddress();
                MissionCall missionCall = missionCallService.selectByIp(hostAddress);
                if (missionCall != null) {
                    String callId = missionCall.getId();
                    ReceiveHandlerThread receiveHandlerThread = (ReceiveHandlerThread)ApplicationUtils.getBean("receiveHandlerThread");
                    receiveHandlerThread.start(client, callId);
                    //把客户端缓存
                    clientSocketMap.put(callId, client);
                } else {
                    logger.error("呼叫盒ip=[{}] 在数据库中不存在,请检测呼叫盒的ip地址", hostAddress);
                }
                Thread.sleep(500);
            } catch (Exception e) {
                logger.error("呼叫盒异常: " + e);
            }
        }*/

        ServerSocket serverSocket = null;
        while (true) {
            try {
                if (serverSocket == null) {
                    serverSocket = new ServerSocket(AGVPropertiesUtils.getInteger("MISSION.CALL.PORT"));
                    logger.debug("呼叫盒socket服务启动中。。。");
                }
                //从请求队列中取出一个连接
                Socket client = serverSocket.accept();
                // 将连接交给另一线程处理
                ReceiveHandlerThread receiveHandlerThread = (ReceiveHandlerThread) ApplicationUtils.getBean("receiveHandlerThread");
                receiveHandlerThread.start(client);
                Thread.sleep(500);
            } catch (Exception e) {
                logger.error("呼叫盒异常: " + e.getMessage());
            }
        }
    }


/*    private void startMonitorWorkThread() {
        List<MissionCall> missionCallList = missionCallService.findAll();
        for(MissionCall missionCall :missionCallList){
            MissionWork missionWork = missionWorkService.getMissionWorkByMissionCallId(missionCall.getId());
            if(missionWork != null ){
                if (!MISSION_WORK_STATUS_SUCCESS.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {
                    callBoxWorkMap.put(missionCall.getId(),missionWork.getId());
                }
            }
        }
        callBoxMonitorWorkThread.startThread();
    }*/

}
