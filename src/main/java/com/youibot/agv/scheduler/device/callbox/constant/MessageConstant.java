package com.youibot.agv.scheduler.device.callbox.constant;

/**
 * {阐述类的作用}
 *
 * @author: h<PERSON><PERSON><PERSON><PERSON>
 * @date: 2021-01-07 17:48
 */
public class MessageConstant {

    /**
     * 关闭全部灯
     * AA  00  04  FF  BB
     * 地址
     */
    public static final byte[] CLOSE_ALL_LIGHTS = {(byte) 0xAA, 0x00, 0x04, (byte) 0xFF, (byte) 0xBB};

    /**
     * 打开全部灯
     * AA 00  05 00 BB
     * 地址
     */
    public static final byte[] OPEN_ALL_LIGHTS = {(byte) 0xAA, 0x00, 0x05, (byte) 0x00, (byte) 0xBB};


    /**
     * 关闭地址为00 01的灯
     * AA   01   02    01   BB
     *     地址  关   灯位置
     */
    public static final byte[] CLOSE_LIGHTS = {(byte) 0xAA, 0x00, 0x02, (byte) 0x01, (byte) 0xBB};

    /**
     * 打开地址为00 01的灯
     * AA   00   03    01   BB
     *     地址  打开   灯位置
     */
    public static final byte[] OPEN_LIGHTS = {(byte) 0xAA, 0x00, 0x03, (byte) 0x01, (byte) 0xBB};


    /**
     * 修改呼叫盒地址
     * 输入如 AA 01 01 02 BB 命令，把设备地址从 01 改为 02，
     *  AA    01     01      02       BB
     *       地址位  功能码  需要修改的地址
     */
    public static final byte[] CHANGE_ADDRESS = {(byte) 0xAA, 0x00, 0x01, (byte) 0x00, (byte) 0xBB};



    /**
     *  查询灯的状态
     *  AA   00    06      01    BB
     *      地址   功能码  第一个灯
     */
    public static final byte[] BOX_STATUS = {(byte) 0xAA, 0x00, 0x06, (byte) 0x01, (byte) 0xBB};





}
