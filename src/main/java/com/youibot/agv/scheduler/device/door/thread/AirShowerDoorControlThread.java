package com.youibot.agv.scheduler.device.door.thread;

import static com.youibot.agv.scheduler.constant.DeviceConstant.AIR_SHOWER_DOOR_POSITION_FRONT;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_CLOSE;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_ERROR;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_OPEN;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_UNBOUND_PATH;
import static com.youibot.agv.scheduler.constant.DeviceConstant.DOOR_TYPE_AIR_SHOWER;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.youibot.agv.scheduler.device.door.util.AutoDoorUtils;
import com.youibot.agv.scheduler.entity.AirShowerDoor;
import com.youibot.agv.scheduler.entity.AutoDoor;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.service.AutoDoorService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;

/**
 * 风淋门控制线程
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2021/11/4 18:06
 */
@Component
public class AirShowerDoorControlThread extends DoorControlThread {

    private static final Logger LOGGER = LoggerFactory.getLogger(AirShowerDoorControlThread.class);

    @Autowired
    private AutoDoorService autoDoorService;

    @Autowired
    private VehiclePool vehiclePool;

    @Value("${AUTO_DOOR.CONTROL_THREAD_SLEEP_TIME}")
    private Integer controlThreadSleepTime;

    @Override
    public void run() {
        Thread.currentThread().setName("air-shower-door-control");
        LOGGER.debug("风淋门控制线程开启......");
        while (true) {
            try {
                Thread.sleep(controlThreadSleepTime);
                List<AutoDoor> autoDoors = autoDoorService.selectByType(DOOR_TYPE_AIR_SHOWER);//获取所有风淋门
                if (CollectionUtils.isEmpty(autoDoors)) {
                    continue;
                }
                super.updateAutoDoorsStatus(autoDoors);//查询、更新所有自动门的状态
                List<AirShowerDoor> airShowerDoors = this.combinedAirShowerDoor(autoDoors);//组合风淋门
                airShowerDoors.parallelStream().forEach(this::controlAirShowerDoor);//遍历控制风淋门
            } catch (Exception e) {
                LOGGER.error("控制风淋门出错, ", e);
            }
        }
    }

    private void controlAirShowerDoor(AirShowerDoor airShowerDoor) {
        AutoDoor frontDoor = airShowerDoor.getFrontDoor();
        AutoDoor backDoor = airShowerDoor.getBackDoor();
        try {
            if (AUTO_DOOR_STATUS_ERROR.equals(frontDoor.getCurrentStatus()) || AUTO_DOOR_STATUS_ERROR.equals(backDoor.getCurrentStatus())) {
                return;//如果风淋门状态为异常
            }
            if (AUTO_DOOR_STATUS_UNBOUND_PATH.equals(frontDoor.getCurrentStatus()) || AUTO_DOOR_STATUS_UNBOUND_PATH.equals(backDoor.getCurrentStatus())) {
                return;//如果风淋门未绑定路径
            }

            if (AutoDoorUtils.isInUseAutoDoor(frontDoor)) {//如果机器人在前门的路径上
                if (!AUTO_DOOR_STATUS_OPEN.equals(frontDoor.getCurrentStatus())) {
                    super.sendOpenInstructions(frontDoor);//发送前门开门指令
                }
                return;
            } else if (AutoDoorUtils.isInUseAutoDoor(backDoor)) {//如果机器人在后门的路径上
                if (!AUTO_DOOR_STATUS_OPEN.equals(backDoor.getCurrentStatus())) {
                    super.sendOpenInstructions(backDoor);//发送后门开门指令
                }
                return;
            }

            //判断有没有机器人在两个风淋门中间
            boolean betweenAirShowerDoors = isBetweenAirShowerDoors(airShowerDoor);
            if (betweenAirShowerDoors) {//机器人处理风淋门中间
                //目前判断门为已开启时才发送关闭指令, 若门为操作中, 则不发送, 避免人为开门马上又关闭的情况
                if (!AUTO_DOOR_STATUS_CLOSE.equals(frontDoor.getCurrentStatus())) {//前门未关闭
                    if (AUTO_DOOR_STATUS_OPEN.equals(frontDoor.getCurrentStatus())) {
                        super.sendCloseInstructions(frontDoor);//发送前门关门指令
                    }
                } else {//前门已关闭
                    if (frontDoor.getLastCloseDoorTime() != null && (System.currentTimeMillis() - frontDoor.getLastCloseDoorTime().getTime()) >= frontDoor.getResidenceTime() * 1000) {
                        //如果前门已经关闭超过X秒
                        if (!AUTO_DOOR_STATUS_OPEN.equals(backDoor.getCurrentStatus())) {
                            super.sendOpenInstructions(backDoor);//发送后门开门指令
                        }
                    }
                }
            } else {//机器人不在风淋门中间
                if (!AUTO_DOOR_STATUS_CLOSE.equals(backDoor.getCurrentStatus())) {//后门未关闭
                    if (AUTO_DOOR_STATUS_OPEN.equals(backDoor.getCurrentStatus())) {
                        super.sendCloseInstructions(backDoor);//发送后门关门指令
                    }
                } else {//后门已关闭
                    if (AutoDoorUtils.isWillUseAutoDoor(frontDoor)) {//前门被规划使用
                        if (!AUTO_DOOR_STATUS_OPEN.equals(frontDoor.getCurrentStatus())) {
                            super.sendOpenInstructions(frontDoor);//发送前门开门指令
                        }
                    } else {//前门不被规划使用
                        if (AUTO_DOOR_STATUS_OPEN.equals(frontDoor.getCurrentStatus())) {
                            super.sendCloseInstructions(frontDoor);//发送前门关门指令
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("控制风淋门出错, ", e);
            frontDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);//设置机器人为异常状态
            backDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);//设置机器人为异常状态
            autoDoorService.batchUpdate(Arrays.asList(frontDoor, backDoor));
        }
    }

    /**
     * 判断有没有机器人处于两道风淋门中间
     *
     * @param airShowerDoor
     * @return
     */
    private boolean isBetweenAirShowerDoors(AirShowerDoor airShowerDoor) {
        boolean isBetweenAirShower = false;
        Path frontPath = airShowerDoor.getFrontDoor().getPaths().get(0);
        Path backPath = airShowerDoor.getBackDoor().getPaths().get(0);
        List<Vehicle> vehicles = vehiclePool.getAll();
        for (Vehicle vehicle : vehicles) {
            //获取机器人当前点位
            String currentMarkerId = vehicle.getLastMarkerId();
            if (StringUtils.isEmpty(currentMarkerId)) {
                continue;
            }
            //判断机器人有没有在两道风淋门的交汇点
            if ((currentMarkerId.equals(frontPath.getStartMarkerId()) || currentMarkerId.equals(frontPath.getEndMarkerId()))
                    && (currentMarkerId.equals(backPath.getStartMarkerId()) || currentMarkerId.equals(backPath.getEndMarkerId()))) {
                LOGGER.debug("agvCode:[{}],event:[风淋门资源申请],content:{}", vehicle.getId(),"机器人区域两道风淋门之间:"+airShowerDoor.toString());
                isBetweenAirShower = true;
                break;
            }
        }
        return isBetweenAirShower;
    }

    /**
     * 组合风淋门
     *
     * @param autoDoors
     * @return
     */
    private List<AirShowerDoor> combinedAirShowerDoor(List<AutoDoor> autoDoors) {
        List<AirShowerDoor> airShowerDoors = new ArrayList<>();
        //获取所有前门
        List<AutoDoor> frontAutoDoors = autoDoors.stream().filter(a -> AIR_SHOWER_DOOR_POSITION_FRONT.equals(a.getPosition())).collect(Collectors.toList());
        //为所有前门寻找后门
        frontAutoDoors.parallelStream().forEach(frontAutoDoor -> {
            List<AutoDoor> backAutoDoors = autoDoors.stream().filter(a -> frontAutoDoor.getId().equals(a.getRelationDoorId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(backAutoDoors) || backAutoDoors.size() != 1) {
                //未绑定后门或者绑定了多个后门
                frontAutoDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);
                autoDoorService.update(frontAutoDoor);
                return;
            }
            AirShowerDoor airShowerDoor = new AirShowerDoor(frontAutoDoor, backAutoDoors.get(0));
            airShowerDoors.add(airShowerDoor);
        });
        return airShowerDoors;
    }
}
