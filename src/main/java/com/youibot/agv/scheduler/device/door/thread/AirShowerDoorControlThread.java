package com.youibot.agv.scheduler.device.door.thread;


import static com.youibot.agv.scheduler.constant.DeviceConstant.AIR_SHOWER_DOOR_POSITION_FRONT;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_CLOSE;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_ERROR;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_OPEN;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_OPERATING;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_UNBOUND_PATH;
import static com.youibot.agv.scheduler.constant.DeviceConstant.DOOR_TYPE_AIR_SHOWER;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SYSTEM_WORK_MODE_LOCAL;
import static com.youibot.agv.scheduler.constant.DeviceConstant.AUTO_DOOR_STATUS_NOT_OPEN ;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.youibot.agv.scheduler.device.door.util.AutoDoorUtils;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.entity.AirShowerDoor;
import com.youibot.agv.scheduler.entity.AutoDoor;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AutoDoorService;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.service.SystemWorkModeService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;

/**
 * 风淋门控制线程
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2021/11/4 18:06
 */
@Component
public class AirShowerDoorControlThread extends DoorControlThread {

    private static final Logger LOGGER = LoggerFactory.getLogger(AirShowerDoorControlThread.class);

    @Autowired
    private AutoDoorService autoDoorService;

    @Autowired
    private SystemWorkModeService systemWorkModeService;

    @Resource
    private PathService pathService;

    @Resource
    private LocationService locationService;

    @Resource
    private PathPlanService pathPlanService;


    @Value("${AUTO_DOOR.CONTROL_THREAD_SLEEP_TIME}")
    private Integer controlThreadSleepTime;

    @Value("${AUTO_DOOR.OPEN_ADVANCE_TIME}")
    private Integer openAdvanceTime;

    @Override
    public void run() {
        currentThread().setName("air-shower-door-control");
        LOGGER.debug("风淋门控制线程开启......");
        while (true) {
            try {
                sleep(controlThreadSleepTime);
                if (systemWorkModeService.isSchedulerMode()) {
                    //如果是调度模式, 自动门由调度系统控制
                    //清空所有自动门的状态且控制模式改为调度控制
                    super.clearDoorStatusAndUpdateControl(DOOR_TYPE_AIR_SHOWER);
                    //释放并清除池中所有资源
                    super.releaseModbusMasters();
                    continue;
                }
                List<AutoDoor> autoDoors = autoDoorService.selectByTypeAndControl(DOOR_TYPE_AIR_SHOWER, SYSTEM_WORK_MODE_LOCAL);//获取所有本地控制的风淋门
                if (CollectionUtils.isEmpty(autoDoors)) {
                    continue;
                }
                super.updateAutoDoorsStatus(autoDoors);//查询、更新所有自动门的状态
                List<AirShowerDoor> airShowerDoors = this.combinedAirShowerDoor(autoDoors);//组合风淋门
                airShowerDoors.forEach(this::controlAirShowerDoor);//遍历控制风淋门
            } catch (Exception e) {
                LOGGER.error("控制风淋门出错, ", e);
            }
        }
    }

    private void controlAirShowerDoor(AirShowerDoor airShowerDoor) {
        try {

            controlAirShowerDoor0(airShowerDoor);

        } catch (Exception e) {
            AutoDoor frontDoor = airShowerDoor.getFrontDoor();
            AutoDoor backDoor = airShowerDoor.getBackDoor();
            LOGGER.error("控制风淋门出错, ", e);
            // 设置机器人为异常状态
            frontDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);
            // 设置机器人为异常状态
            backDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);
            autoDoorService.batchUpdate(Arrays.asList(frontDoor, backDoor));
        }
    }

    private void controlAirShowerDoor0(AirShowerDoor airShowerDoor) {
        for (AutoDoor autoDoor : new AutoDoor[]{airShowerDoor.getFrontDoor(), airShowerDoor.getBackDoor()}) {
            if (AUTO_DOOR_STATUS_OPERATING.equals(autoDoor.getCurrentStatus()) || AUTO_DOOR_STATUS_ERROR.equals(autoDoor.getCurrentStatus())
                    || AUTO_DOOR_STATUS_UNBOUND_PATH.equals(autoDoor.getCurrentStatus())) {
                // 风淋门操作中 || 风淋门状态为异常 || 风淋门未绑定路径
                return;
            }
        }

        // 校验并获取门路径
        Map<String, AutoDoor> pathIdToDoor = validateAndGetAutoDoorPath(airShowerDoor);

        Vehicle vehicle = VehicleUtils.getVehicle();
        List<String> planedPathsWithDoor = vehicle.getSidePaths().stream().map(SidePath::getPathId).filter(pathIdToDoor::containsKey).collect(Collectors.toList());
        List<String> executedPathsWithDoor = vehicle.getUsedSidePaths().stream().map(SidePath::getPathId).filter(pathIdToDoor::containsKey).collect(Collectors.toList());

        // 第一个门
        if (planedPathsWithDoor.size() > 0 && executedPathsWithDoor.size() == 0) {
            if (pathIdToDoor.values().stream().allMatch(autoDoor -> Objects.equals(autoDoor.getCurrentStatus(), AUTO_DOOR_STATUS_NOT_OPEN))
                    && AutoDoorUtils.isUseAutoDoor(pathIdToDoor.get(planedPathsWithDoor.get(0)))) {
                AutoDoor autoDoor = pathIdToDoor.get(planedPathsWithDoor.get(0));
                AutoDoor another = airShowerDoor.getFrontDoor() == autoDoor ? airShowerDoor.getBackDoor() : airShowerDoor.getFrontDoor();
                super.sendCloseInstructions(another);
                super.sendOpenInstructions(autoDoor);
                LOGGER.debug("小车【{}】即将抵达或已抵达风淋门【{}】，开门", vehicle.getName(), autoDoor.getName());
                return;
            }
        }

        // 第二个门
        if (planedPathsWithDoor.size() == 2 && executedPathsWithDoor.size() == 1) {
            // 已通过第一个门，未通过第二个门
            AutoDoor first = pathIdToDoor.get(planedPathsWithDoor.get(0));
            AutoDoor second = pathIdToDoor.get(planedPathsWithDoor.get(1));
            if (Objects.equals(AUTO_DOOR_STATUS_NOT_OPEN, first.getCurrentStatus())
                    && first.getLastCloseDoorTime() != null && (System.currentTimeMillis() - first.getLastCloseDoorTime().getTime()) >= first.getResidenceTime() * 1000L) {
                // 如果入口已经关闭超过X秒，发送出口开门指令
                super.sendCloseInstructions(first);
                super.sendOpenInstructions(second);
                LOGGER.debug("小车【{}】完成吹风，打开风淋门【{}】", vehicle.getName(), second.getName());
                return;
            }
        }

        // 不被使用，关门
        for (AutoDoor autoDoor : new AutoDoor[]{airShowerDoor.getFrontDoor(), airShowerDoor.getBackDoor()}) {
            if (Objects.equals(AUTO_DOOR_STATUS_OPEN, autoDoor.getCurrentStatus()) && !AutoDoorUtils.isUseAutoDoor(autoDoor)) {
                super.sendCloseInstructions(autoDoor);
                LOGGER.debug("风淋门【{}】不被使用，关门", autoDoor.getName());
            }
        }
    }

    private String getCurPathId() {
        Vehicle vehicle = VehicleUtils.getVehicle();
        String result = "";
        DefaultVehicleStatus.PositionStatus positionStatus = vehicle.getPositionStatus();
        if (StringUtils.isEmpty(vehicle.getCurrentMarkerId()) && positionStatus != null) {
            SidePath sidePath = locationService.getSidePath(vehicle.getAGVMapId(), positionStatus.getPos_x(), positionStatus.getPos_y(), vehicle.getSidePaths());
            result = sidePath != null ? sidePath.getPathId() : result;
        }
        return result;
    }

    /**
     * 是否即将到达
     *
     * @param autoDoor
     * @return
     */
    private boolean isArrivedSoon(AutoDoor autoDoor) {
        Path path = autoDoor.getPaths().get(0);
        for (String markerId : new String[]{path.getStartMarkerId(), path.getEndMarkerId()}) {
            Double markerCost = pathPlanService.calculateNavigationCost(markerId);
            if (markerCost != null && markerCost <= openAdvanceTime) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验并获取自动门路径
     *
     * @param airShowerDoor key:pathId value:autoDoor
     */
    private Map<String, AutoDoor> validateAndGetAutoDoorPath(AirShowerDoor airShowerDoor) {
        Map<String, AutoDoor> result = new HashMap<>();
        for (AutoDoor autoDoor : new AutoDoor[]{airShowerDoor.getFrontDoor(), airShowerDoor.getBackDoor()}) {
            List<Path> paths = CollectionUtils.isEmpty(autoDoor.getPaths()) ? pathService.selectByAutoDoorId(autoDoor.getId()) : autoDoor.getPaths();
            autoDoor.setPaths(paths);
            if (CollectionUtils.isEmpty(paths)) {
                throw new ExecuteException(String.format("自动(风淋)门【%s】未绑定路径", autoDoor.getName()));
            }

            // 只取第一个路径
            Path path = paths.get(0);
            result.put(path.getId(), autoDoor);
        }
        return result;
    }

    /**
     * 组合风淋门
     *
     * @param autoDoors
     * @return
     */
    private List<AirShowerDoor> combinedAirShowerDoor(List<AutoDoor> autoDoors) {
        List<AirShowerDoor> airShowerDoors = new ArrayList<>();
        //获取所有前门
        List<AutoDoor> frontAutoDoors = autoDoors.stream().filter(a -> AIR_SHOWER_DOOR_POSITION_FRONT.equals(a.getPosition())).collect(Collectors.toList());
        //为所有前门寻找后门
        frontAutoDoors.parallelStream().forEach(frontAutoDoor -> {
            List<AutoDoor> backAutoDoors = autoDoors.stream().filter(a -> frontAutoDoor.getId().equals(a.getRelationDoorId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(backAutoDoors) || backAutoDoors.size() != 1) {
                //未绑定后门或者绑定了多个后门
                frontAutoDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);
                autoDoorService.update(frontAutoDoor);
                return;
            }
            AirShowerDoor airShowerDoor = new AirShowerDoor(frontAutoDoor, backAutoDoors.get(0));
            airShowerDoors.add(airShowerDoor);
        });
        return airShowerDoors;
    }
}
