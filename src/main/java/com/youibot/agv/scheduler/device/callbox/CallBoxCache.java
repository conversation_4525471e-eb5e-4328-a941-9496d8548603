package com.youibot.agv.scheduler.device.callbox;

import java.net.Socket;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {阐述类的作用}
 *
 * @author: huangguanxin
 * @date: 2021-01-08 12:00
 */
public class CallBoxCache {
    /**
     * 呼叫盒客户端缓存 key:设备地址 value :socket客户端
     */
    public static Map<Integer, Socket> clientSocketMap = new ConcurrentHashMap<>();

    /**
     * 查询盒子状态 key:设备地址,value socket消息
     */
    public static Map<Integer, ReceiveMessage> boxLastStatusMessage = new ConcurrentHashMap<>();


    /**
     * 呼叫器触发创建未执行完成缓存 key 设备地址 ,value misisonWorkId
     */
    public static Map<Integer, String> callBoxWorkMap = new ConcurrentHashMap<>();

    /**
     * 按钮缓存 key:当前按钮,value:触发按钮时间
     */
    public static Map<Integer, Long> buttonCache = new ConcurrentHashMap<>();

}
