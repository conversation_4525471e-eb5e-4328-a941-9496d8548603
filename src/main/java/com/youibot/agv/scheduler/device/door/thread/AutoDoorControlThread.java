package com.youibot.agv.scheduler.device.door.thread;

import com.youibot.agv.scheduler.device.door.util.AutoDoorUtils;
import com.youibot.agv.scheduler.entity.AutoDoor;
import com.youibot.agv.scheduler.service.AutoDoorService;
import com.youibot.agv.scheduler.service.SystemWorkModeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.youibot.agv.scheduler.constant.DeviceConstant.*;
import static com.youibot.agv.scheduler.constant.SystemWorkModeConstant.SYSTEM_WORK_MODE_LOCAL;

/**
 * 自动门控制线程
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2021/11/3 18:33
 */
@Component
public class AutoDoorControlThread extends DoorControlThread {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoDoorControlThread.class);

    @Autowired
    private AutoDoorService autoDoorService;
    @Autowired
    private SystemWorkModeService systemWorkModeService;

    @Value("${AUTO_DOOR.CONTROL_THREAD_SLEEP_TIME}")
    private Integer controlThreadSleepTime;

    @Override
    public void run() {
        currentThread().setName("auto-door-control");
        LOGGER.debug("自动门控制线程开启......");
        while (true) {
            try {
                sleep(controlThreadSleepTime);
                if (systemWorkModeService.isSchedulerMode()) {
                    //如果是调度模式, 自动门由调度系统控制
                    //清空所有自动门的状态且控制模式改为调度控制
                    super.clearDoorStatusAndUpdateControl(DOOR_TYPE_AUTO);
                    //释放并清除池中所有资源
                    super.releaseModbusMasters();
                    continue;
                }
                List<AutoDoor> autoDoors = autoDoorService.selectByTypeAndControl(DOOR_TYPE_AUTO, SYSTEM_WORK_MODE_LOCAL);//获取所有本地控制的自动门
                if (CollectionUtils.isEmpty(autoDoors)) {
                    continue;
                }
                super.updateAutoDoorsStatus(autoDoors);//查询、更新所有自动门的状态
                autoDoors.parallelStream().forEach(this::controlAutoDoor);//遍历数组, 控制自动门
            } catch (Exception e) {
                LOGGER.error("控制自动门出错, ", e);
            }
        }
    }

    /**
     * 控制自动门
     * 1、如果自动门被使用, 控制自动门打开
     * 2、如果自动门没被使用, 控制自动门关闭
     *
     * @param autoDoor
     */
    private void controlAutoDoor(AutoDoor autoDoor) {
        try {
            if (AUTO_DOOR_STATUS_ERROR.equals(autoDoor.getCurrentStatus())) {
                return;//自动门获取状态异常, 本轮不进行操作, 等待下轮
            }
            if (AUTO_DOOR_STATUS_UNBOUND_PATH.equals(autoDoor.getCurrentStatus())) {
                return;//自动门未绑定路径, 本轮不进行操作, 等待下轮
            }
            if (AutoDoorUtils.isUseAutoDoor(autoDoor)) {//自动门是否被使用或将被使用
                if (!AUTO_DOOR_STATUS_OPEN.equals(autoDoor.getCurrentStatus())) {
                    super.sendOpenInstructions(autoDoor);//发送开门指令
                }
            } else {
                if (AUTO_DOOR_STATUS_OPEN.equals(autoDoor.getCurrentStatus())) {
                    super.sendCloseInstructions(autoDoor);//发送关门指令
                }
            }
        } catch (Exception e) {
            LOGGER.error("控制自动门出错, ", e);
            autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);//设置机器人为异常状态
        }
    }
}
