/*
package com.youibot.agv.scheduler.device.callbox;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.net.Socket;

import static com.youibot.agv.scheduler.device.callbox.CallBoxCache.clientSocketMap;
import static com.youibot.agv.scheduler.device.callbox.constant.CallBoxConstant.GO_LET_CODE;
import static com.youibot.agv.scheduler.device.callbox.constant.CallBoxConstant.OPEN_LIGHTS_STATUS;

*/
/**
 * 放行按钮状态线程
 *
 * @author: huangguanxin
 * @date: 2021-01-13 9:54
 *//*

@Component
@Scope("prototype")
public class CallBoxReleaseThread implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(CallBoxReleaseThread.class);

    private String callId;


    public void startThread(String callId) {
        this.callId = callId;
        new Thread(this).start();
    }


    @Override
    public void run() {
        while (true){
            try {
                Socket socket = clientSocketMap.get(callId);
                Thread.sleep(500);
                if (socket != null && !socket.isClosed()) {
                    ReceiveMessage boxStatus = CallBoxUtils.getBoxStatus(socket, callId, GO_LET_CODE);
                    if (boxStatus != null) {
                        if (OPEN_LIGHTS_STATUS.equals(boxStatus.getStatusCode())) {
                            // 当前灯开着 关闭
                            byte[] message = CallBoxUtils.getCloseLightMessage(callId, GO_LET_CODE);
                            CallBoxUtils.write2Stream(socket.getOutputStream(), message);
                            logger.debug("当前放行灯亮着,重按取消关闭灯 callId=[{}]", callId);
                            break;
                        } else {
                            // 当前灯关闭 打开
                            byte[] message = CallBoxUtils.getOpenLightMessage(callId, GO_LET_CODE);
                            CallBoxUtils.write2Stream(socket.getOutputStream(), message);
                            logger.debug("当前放行灯关着,按下打开灯 callId=[{}]", callId);
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("打开或者关闭放行灯出错{}", e);
            }
        }

    }
}
*/
