package com.youibot.agv.scheduler.device.callbox.constant;

/**
 * {阐述类的作用}
 *
 * @author: huang<PERSON><PERSON><PERSON>
 * @date: 2021-01-07 11:43
 */
public class CallBoxConstant {

    // 心跳
    public static final String FUNCTION_PING_REPORT = "FF";
    // 4个按钮通电地址上报
    public static final String FUNCTION_ADDRESS_REPORT = "0B";
    // 2个按钮通电地址上报
    public static final String FUNCTION_ADDRESS_REPORT_2 = "00";
    // 修改地址
    public static final String FUNCTION_ADDRESS_UPDATE = "01";
    // 按钮触发
    public static final String FUNCTION_STATUS_CHANGE = "0A";
    // 主动查询按钮状态
    public static final String WRITE_BOX_STATUS = "06";
    // 返回按钮状态
    public static final String READ_BOX_STATUS = "07";



    // 呼叫按钮
    public static final Integer CALL_CODE = 01;
    // 放行按钮
    public static final Integer GO_LET_CODE = 02;

    // 灯亮状态
    public static final Integer OPEN_LIGHTS_STATUS = 0;
    // 关灯状态
    public static final Integer CLOSE_LIGHTS_STATUS = 255;













}
