package com.youibot.agv.scheduler.device.door.thread;

import com.intelligt.modbus.jlibmodbus.exception.ModbusIOException;
import com.intelligt.modbus.jlibmodbus.exception.ModbusNumberException;
import com.intelligt.modbus.jlibmodbus.exception.ModbusProtocolException;
import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.youibot.agv.scheduler.entity.AutoDoor;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.service.AutoDoorService;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.util.JLibModbusUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.net.UnknownHostException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.DeviceConstant.*;
import static com.youibot.agv.scheduler.constant.ModbusConstant.SLAVE_ID;

/**
 * 自动门&风淋门控制
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2021/11/5 16:20
 */
public class DoorControlThread extends Thread {

    private static final Logger LOGGER = LoggerFactory.getLogger(DoorControlThread.class);

    public Map<String, ModbusMaster> modbusTcpMasterMap = new ConcurrentHashMap<>();

    @Value("${AUTO_DOOR.MODBUS_TIME_OUT}")
    private Integer modbusTimeOut;//modbus超时时间

    @Autowired
    private AutoDoorService autoDoorService;

    @Autowired
    private PathService pathService;

    /**
     * 发送开门指令
     *
     * @param autoDoor
     */
    public void sendOpenInstructions(AutoDoor autoDoor) {
        ModbusMaster master = null;
        try {
            master = this.getModbusTcpMaster(autoDoor);
            if (autoDoor.getCloseAddress() != null) {
                //重置关门指令
                LOGGER.debug("自动门关门重置, autoDoorName:{}", autoDoor.getName());
                this.writeCoilValue(master, autoDoor.getCloseAddress(), false);
            }
            //发送开门指令
            LOGGER.debug("发送自动门开门指令, autoDoorName:{}", autoDoor.getName());
            this.writeCoilValue(master, autoDoor.getOpenAddress(), true);
        } catch (Exception e) {
            LOGGER.error("打开自动(风淋)门出错, autoDoorId:{}, autoDoorIp:{}, autoDoorPort:{}, ", autoDoor.getId(), autoDoor.getIp(), autoDoor.getPort(), e);
            JLibModbusUtils.release(master);
            modbusTcpMasterMap.remove(autoDoor.getIp() + "-" + autoDoor.getPort());
            autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);//设置机器人为异常状态
            autoDoorService.update(autoDoor);
        }
    }

    /**
     * 发送关门指令
     *
     * @param autoDoor
     */
    public void sendCloseInstructions(AutoDoor autoDoor) {
        ModbusMaster master = null;
        try {
            master = this.getModbusTcpMaster(autoDoor);
            //重置开门指令
            LOGGER.debug("自动门开门重置, autoDoorName:{}", autoDoor.getName());
            this.writeCoilValue(master, autoDoor.getOpenAddress(), false);
            if (autoDoor.getCloseAddress() != null) {
                //发送关门指令
                LOGGER.debug("发送自动门关门指令, autoDoorName:{}", autoDoor.getName());
                this.writeCoilValue(master, autoDoor.getCloseAddress(), true);
            }
        } catch (Exception e) {
            LOGGER.error("关闭自动(风淋)门出错, autoDoorId:{}, autoDoorIp:{}, autoDoorPort:{}, ", autoDoor.getId(), autoDoor.getIp(), autoDoor.getPort(), e);
            JLibModbusUtils.release(master);
            modbusTcpMasterMap.remove(autoDoor.getIp() + "-" + autoDoor.getPort());
            autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);//设置机器人为异常状态
            autoDoorService.update(autoDoor);
        }
    }

    /**
     * 更新所有自动门的状态
     *
     * @param autoDoors
     */
    public void updateAutoDoorsStatus(List<AutoDoor> autoDoors) {
        List<String> autoDoorIds = autoDoors.stream().map(AutoDoor::getId).collect(Collectors.toList());
        List<Path> paths = pathService.selectByAutoDoorIds(autoDoorIds);
        autoDoors.parallelStream().forEach(autoDoor -> {
            ModbusMaster master = null;
            try {//获取自动门所在路径
                List<Path> relationPaths = paths.stream().filter(p -> autoDoor.getId().equals(p.getAutoDoorId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(relationPaths)) {
                    autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_UNBOUND_PATH);//设置机器人为未绑路径状态
                    autoDoorService.update(autoDoor);
                    return;
                }
                autoDoor.setPaths(relationPaths);
                master = this.getModbusTcpMaster(autoDoor);
                Boolean doorOpen = this.readCoilStatus(master, autoDoor.getOpenStatusAddress());
                if (doorOpen) {
                    autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_OPEN);//设置自动门为开启状态
                    autoDoor.setLastCloseDoorTime(null);
                } else {
                    Integer closeStatusAddress = autoDoor.getCloseStatusAddress();
                    if (closeStatusAddress != null) {
                        Boolean doorClose = this.readCoilStatus(master, closeStatusAddress);
                        if (doorClose) {
                            String originStatus = autoDoor.getCurrentStatus();
                            if (AUTO_DOOR_STATUS_OPERATING.equals(originStatus) || AUTO_DOOR_STATUS_OPEN.equals(originStatus) || autoDoor.getLastCloseDoorTime() == null) {
                                autoDoor.setLastCloseDoorTime(new Date());
                            }
                            autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_CLOSE);//设置自动门为关闭状态
                        } else {
                            autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_OPERATING);//设置自动门为操作中状态
                            autoDoor.setLastCloseDoorTime(null);
                        }
                    } else {
                        String originStatus = autoDoor.getCurrentStatus();
                        if (AUTO_DOOR_STATUS_OPEN.equals(originStatus) || autoDoor.getLastCloseDoorTime() == null) {
                            autoDoor.setLastCloseDoorTime(new Date());
                        }
                        autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_CLOSE);
                    }
                }
                autoDoorService.updateStatus(autoDoor);
            } catch (Exception e) {
                if (!AUTO_DOOR_STATUS_ERROR.equals(autoDoor.getCurrentStatus())) {
                    LOGGER.error("读取自动(风淋)门状态出错, autoDoorId:{}, autoDoorIp:{}, autoDoorPort:{}, ", autoDoor.getId(), autoDoor.getIp(), autoDoor.getPort(), e);
                }
                JLibModbusUtils.release(master);
                modbusTcpMasterMap.remove(autoDoor.getIp() + "-" + autoDoor.getPort());
                autoDoor.setCurrentStatus(AUTO_DOOR_STATUS_ERROR);//设置机器人为异常状态
                autoDoorService.updateStatus(autoDoor);
            }
        });
    }

    /**
     * 获取modbus客户端连接, 同一个 ip及端口 使用同一个客户端, 避免某些服务端同时间只能连接一个客户端的问题
     *
     * @param autoDoor
     * @return
     * @throws UnknownHostException
     */
    public ModbusMaster getModbusTcpMaster(AutoDoor autoDoor) throws UnknownHostException {
        String key = autoDoor.getIp() + "-" + autoDoor.getPort();
        ModbusMaster master = modbusTcpMasterMap.get(key);
        if (master == null) {
            master = JLibModbusUtils.createModbusMaster(autoDoor.getIp(), autoDoor.getPort(), modbusTimeOut);
            modbusTcpMasterMap.put(key, master);
        }
        return master;
    }

    private Boolean readCoilStatus(ModbusMaster master, Integer coilAddress) throws ModbusIOException, ModbusNumberException, ModbusProtocolException {
        return master.readCoils(SLAVE_ID, coilAddress, 1)[0];
    }

    private void writeCoilValue(ModbusMaster master, Integer coilAddress, boolean value) throws ModbusIOException, ModbusNumberException, ModbusProtocolException {
        master.writeSingleCoil(SLAVE_ID, coilAddress, value);
    }
}
