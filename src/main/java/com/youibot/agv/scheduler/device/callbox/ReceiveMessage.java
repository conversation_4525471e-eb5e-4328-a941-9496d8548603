package com.youibot.agv.scheduler.device.callbox;

import lombok.Data;

import java.io.Serializable;

/**
 * 客户端向服务端发送的数据报文
 *
 * @author: huangguanxin
 * @date: 2021-01-07 17:10
 */
@Data
public class ReceiveMessage implements Serializable {
    // 报文头
    private String headerCode;

    // 地址位，范围 01～FF 即 0-255 ，向 00 地址发送，代表当前地址
    private Integer address;

    // 功能指令：上报地址，不能修改
    private String functionCode;

    // 功能代码，范围 01～FF，表示第 3 个按钮触发
    private Integer statusCode;

    //报文接收
    private String endCode;

}
