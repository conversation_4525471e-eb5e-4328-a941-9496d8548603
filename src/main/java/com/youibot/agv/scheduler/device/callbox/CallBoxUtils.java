package com.youibot.agv.scheduler.device.callbox;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;

import static com.youibot.agv.scheduler.device.callbox.CallBoxCache.*;
import static com.youibot.agv.scheduler.device.callbox.constant.CallBoxConstant.READ_BOX_STATUS;
import static com.youibot.agv.scheduler.device.callbox.constant.MessageConstant.*;

/**
 * 呼叫盒工具类
 *
 * @author: huangguanxin
 * @date: 2021-01-07 11:31
 */
public class CallBoxUtils {

    private static final Logger logger = LoggerFactory.getLogger(CallBoxUtils.class);


    /**
     * 获取当前灯状态
     * socket 资源由外部关闭
     *
     * @param socket
     * @param deviceAddress
     * @param position
     * @return
     * @throws IOException
     */
    public static synchronized ReceiveMessage getBoxStatus(Socket socket, Integer deviceAddress, Integer position) throws IOException {
        if (socket != null) {
            boxLastStatusMessage.remove(deviceAddress);
            byte[] boxStatusMessage = getBoxStatusMessage(deviceAddress, position);
            write2Stream(socket.getOutputStream(),boxStatusMessage);
            long startTime = System.currentTimeMillis();
            while ((System.currentTimeMillis() - startTime) / 1000 < 60) {
                ReceiveMessage boxStatus = boxLastStatusMessage.get(deviceAddress);
                if (boxStatus != null) {
                    if (READ_BOX_STATUS.equals(boxStatus.getFunctionCode())) {
                        boxLastStatusMessage.remove(deviceAddress);
                        return boxStatus;
                    }
                }
                try {
                    Thread.sleep(300);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }



    /**
     * 写数据
     *
     * @param outputStream
     */
    public static void write2Stream(OutputStream outputStream, byte[] message) throws IOException {
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(outputStream);
        bufferedOutputStream.write(message);
        bufferedOutputStream.flush();
    }


    public static ReceiveMessage readFromStream(byte[] message) {
        ReceiveMessage receiveMessage = new ReceiveMessage();
        try {
            for (int i = 0; i < message.length; i++) {
                String value = byteToHex(message[i]);
                if (i == 0 && !"AA".equals(value)) {
                    return null;
                }
                if ("BB".equals(value)) {
                    receiveMessage.setEndCode(value);
                    return receiveMessage;
                }
                switch (i) {
                    case 0: {
                        receiveMessage.setHeaderCode(value);
                        break;
                    }
                    case 1: {
                        receiveMessage.setAddress((int)Long.parseLong(value,  16));
                        break;
                    }
                    case 2: {
                        receiveMessage.setFunctionCode(value);
                        break;
                    }
                    case 3: {
                        receiveMessage.setStatusCode((int)Long.parseLong(value,  16));
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("呼叫盒返回报文转化失败{}", e);
        }
        return receiveMessage;

    }

    /**
     * 打开灯报文
     *
     * @param deviceAddress
     * @param position
     * @return
     */
    public static byte[] getOpenLightMessage(Integer deviceAddress, Integer position) {
        byte[] message = OPEN_LIGHTS;
        if (deviceAddress != null) {
            message[1] = (byte) (0XFF & deviceAddress);
        }
        message[3] = (byte) (0XFF & position);
        return message;
    }

    /**
     * 关闭灯报文
     *
     * @param address
     * @param position
     * @return
     */
    public static byte[] getCloseLightMessage(Integer address, Integer position) {
        byte[] message = CLOSE_LIGHTS;
        if (address != null) {
            message[1] = (byte) (0XFF & address);
        }
        message[3] = (byte) (0XFF & position);
        return message;
    }

    public static byte[] getBoxStatusMessage(Integer address, Integer position) {
        byte[] message = BOX_STATUS;
        if (address != null) {
            message[1] = (byte) (0XFF & address);
        }
        message[3] = (byte) (0XFF & position);
        return message;
    }


    public static void closeSocket(String callId) {
        try {
            Socket socket = clientSocketMap.get(callId);
            if(socket !=null ){
                clientSocketMap.remove(callId);
                socket.getInputStream().close();
                socket.getOutputStream().close();
                socket.close();
            }
        }catch (Exception e){
            logger.error("修改呼叫盒出错{}",e);
        }
    }


    public static void closeSocket(Socket socket) {
        try {
            if (socket != null) {
                socket.getInputStream().close();
                socket.getOutputStream().close();
                socket.close();
            }
        } catch (Exception e) {
            logger.error("修改呼叫盒出错{}", e);
        }
    }


    private static String byteToHex(byte b) {
        String hex = Integer.toHexString(b & 0xFF);
        if (hex.length() < 2) {
            hex = "0" + hex;
        }
        return hex.toUpperCase();
    }



}
