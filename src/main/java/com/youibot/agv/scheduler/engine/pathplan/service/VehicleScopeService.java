package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.vehicle.Vehicle;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 上午11:01 2020/9/26
 * @Description :
 * @Modified By :
 * @Version :
 */
public interface VehicleScopeService {

    /**
     * 根据车辆当前位置到目标点位置距离进行评分。
     * 距离系数 1.5
     *
     * @param markerId
     * @param vehicle
     * @return
     */
    Double scopeByDistance(String markerId, Vehicle vehicle);

    /**
     * 根据当前位置到目标点位置距离进行评分。
     * 距离系数 1.5
     *
     * @param vehicle
     * @param batteryValueRatio
     * @return
     */
    Double scopeByBattery(Vehicle vehicle, Double batteryValueRatio);

    /**
     * 根据车辆当前位置到目标点位置距离或者点与点之间的距离进行评分
     * 距离系数 1.5
     *
     * @param markerId
     * @param vehicle
     * @return
     */
    Double scopeByDistance(String markerId, Vehicle vehicle, Marker lastMarker);

    /**
     * 根据车辆当前状态来对时间评分
     * 1.非作业状态的空闲车辆，评分200
     * 2.作业状态车辆，评分为当前作业已执行的时间 * 评分系数，得到的最大值不超过200
     * @param vehicle
     * @return
     */
    Double scopeByTime(Vehicle vehicle, MissionWork missionWork);



}
