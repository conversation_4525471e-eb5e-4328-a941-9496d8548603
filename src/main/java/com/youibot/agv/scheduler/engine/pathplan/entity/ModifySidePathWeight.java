package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date :Created in 下午2:09 2020/12/29
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class ModifySidePathWeight {


    @Column
    @ApiModelProperty(value = "通过此agv修改的路径参数", position = 1)
    private String agvCode;

    @Column
    @ApiModelProperty(value = "路径ID", position = 2)
    private String sidePathId;

    @Column
    @ApiModelProperty(value = "路径开始点Id", position = 3)
    private String startMarkerCode;

    @Column
    @ApiModelProperty(value = "路径结束点Id", position = 4)
    private String endMarkerCode;

    @Column
    @ApiModelProperty(value = "修改类型，PLUS_WEIGHT，MINUS_WEIGHT，MULTIPLY_WEIGHT，RESET_WEIGHT", position = 5)
    private String type;

    @Column
    @ApiModelProperty(value = "修改的参数", position = 6)
    private Double autoWeight;

    @Column
    @ApiModelProperty(value = "修改后的结果", position = 7)
    private Double weightAuto;


    @Column
    @ApiModelProperty(value = "线程名称", position = 8)
    private String threadName;

    @Column
    @ApiModelProperty(value = "创建时间", position = 9)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Column
    @ApiModelProperty(value = "创建时间", position = 12)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
