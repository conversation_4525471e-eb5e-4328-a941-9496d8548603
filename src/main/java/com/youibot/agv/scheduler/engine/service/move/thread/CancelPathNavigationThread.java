package com.youibot.agv.scheduler.engine.service.move.thread;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.bean.send.CommandResultMessage;
import com.youibot.agv.scheduler.mqtt.constant.MqResultConstant;
import com.youibot.agv.scheduler.mqtt.constant.MqTypeConstant;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_COMMAND;

@Service
@Scope("prototype")
public class CancelPathNavigationThread extends Thread {

    private String missionWorkActionId;

    private Thread thread;

    private volatile boolean isReturn = false;

    private Vehicle vehicle;

    @Autowired
    MissionWorkService missionWorkService;

    @Autowired
    MissionWorkActionService missionWorkActionService;

    private static final Logger LOGGER = LoggerFactory.getLogger(CancelPathNavigationThread.class);

    public void startThread(String missionWorkActionId, Vehicle vehicle) {
        this.missionWorkActionId = missionWorkActionId;
        this.vehicle = vehicle;
        isReturn = true;
        thread = new Thread(this);
        thread.start();
    }

    public void stopThread() {
        isReturn = false;
    }

    @Override
    public void run() {
        LOGGER.debug("检测是否需要撤销规划路径线程开启, agvCode:{}", vehicle.getDeviceNumber());
        try {
            while (isReturn) {
                Thread.sleep(200);
                if (vehicle.isCancelPathNavigation()) {
                    LOGGER.debug("检测到需要撤销路径, agvCode:{}", vehicle.getDeviceNumber());
                    if (!"SMART_CHARGE_MOVE_TO_MARKER".equals(missionWorkActionId) && !"SMART_WAIT_MOVE_TO_MARKER".equals(missionWorkActionId)) {
                        MissionWorkAction missionWorkAction = missionWorkActionService.selectById(missionWorkActionId);
                        MissionWork missionWork = missionWorkService.selectById(missionWorkAction.getMissionWorkId());
                        if (missionWork == null) {
                            throw new ExecuteException(MessageUtils.getMessage("service.mission_work_is_null"));
                        }
                        if (MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {
                            throw new ExecuteException(MessageUtils.getMessage("service.mission_is_shutdown"));
                        }
                        if (!missionWorkAction.getActionType().equals("MOVE_SIDE_PATH") && !missionWorkAction.getActionType().equals("RANDOM_MOVE_SIDE_PATH")) {
                            throw new ExecuteException(MessageUtils.getMessage("action.action_type_error"));
                        }
                    }
                    //停止当前正在执行路径导航动作
                    vehicle.setSidePaths(new CopyOnWriteArrayList<>());
                    LOGGER.info("停止当前正在执行的路径导航 agvCode:{}", vehicle.getDeviceNumber());
                    vehicle.stopCommand();
                    vehicle.setCancelPathNavigation(false);
                    LOGGER.info("停止机器人路径导航成功 agvCode{}", vehicle.getDeviceNumber());
                    CommandResultMessage message = new CommandResultMessage(MqTypeConstant.MQ_TYPE_CMD_CANCEL_PATH_NAVIGATION_RESULT, vehicle.getDeviceNumber());
                    MqttUtils.pushMessage(MQTT_PUBLISH_COMMAND, message);
                }
            }
        } catch (Exception e) {
            LOGGER.error("取消当前路径导航失败{}", e);
            vehicle.setCancelPathNavigation(false);
            try {
                CommandResultMessage message = new CommandResultMessage(MqTypeConstant.MQ_TYPE_CMD_CANCEL_PATH_NAVIGATION_RESULT, vehicle.getDeviceNumber());
                message.setStatus(MqResultConstant.RESULT_FAIL);
                message.setMessage("撤销路径导航失败, " + e.getMessage());
                MqttUtils.pushMessage(MQTT_PUBLISH_COMMAND, message);
            } catch (InterruptedException e1) {
                LOGGER.error("返回指令执行状态失败{}", e);
            }
        } finally {
            vehicle.setEmc_time(null);
        }
        LOGGER.debug("检测是否需要撤销规划路径线程结束, agvCode:{}", vehicle.getDeviceNumber());
    }

}
