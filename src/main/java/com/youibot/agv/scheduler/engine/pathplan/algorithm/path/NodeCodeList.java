package com.youibot.agv.scheduler.engine.pathplan.algorithm.path;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

@SuppressWarnings("rawtypes")
public class NodeCodeList<E> extends LinkedList {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public int hashCode() {
		String join = StringUtils.join(this,"->");
		return Objects.hash(join);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (getClass() != obj.getClass())
			return false;
		String join = StringUtils.join(this,"->");
		@SuppressWarnings("unchecked")
		NodeCodeList<String> objNode = (NodeCodeList<String>)obj;
		String objStr = StringUtils.join( objNode,"->");
		return StringUtils.equals(join, objStr); 
	}

	public NodeCodeList() {
		super();
		// TODO Auto-generated constructor stub
	}

	@SuppressWarnings("unchecked")
	public NodeCodeList(Collection c) {
		super(c);
		// TODO Auto-generated constructor stub
	}

	
}
