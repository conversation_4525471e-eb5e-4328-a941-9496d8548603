package com.youibot.agv.scheduler.engine.service.soundLight;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class DefaultSoundLightConfigService extends DefaultApiService implements SoundLightConfigService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultSoundLightConfigService.class);

    @Value("${AGV_SOCKET.PORT.CONFIG}")
    private Integer agvConfigPort;//连接agv的config端口号

    @Value("${AGV_API_CODE.QUERY_SOUND_LIGHT_INFO}")
    private String querySoundLightConfigApiCode;// 查询agv配置信息apiCode

    @Value("${AGV_API_CODE.SAVE_SOUND_LIGHT_INFO}")
    private String saveSoundLightConfigApiCode;// 保存agv配置信息apiCode

    @Value("${AGV_API_CODE.UPLOAD_AUDIO}")
    private String uploadAudio;//上传音频

    @Value("${AGV_API_CODE.DOWNLOAD_AUDIO}")
    private String downloadAudio;//下载音频

    @Value("${AGV_API_CODE.QUERY_SOUND_LIGHT}")
    private String querySoundLight;

    @Value("${UPLOAD_FILE.AUDIO_FILE}")
    private String uploadFilePath;

    @Override
    public Map<String, Object> querySoundLightConfig(String ip) throws IOException {
        LOGGER.debug("查询声光配置:");
        return super.execute(ip, agvConfigPort, querySoundLightConfigApiCode, null);
    }

    @Override
    public void modifySoundLightConfig(String ip, JSONObject dataJson) throws IOException {
        LOGGER.debug("保存声光状态信息 dataJson: " + dataJson);
        super.execute(ip, agvConfigPort, saveSoundLightConfigApiCode, dataJson);
    }

    @Override
    public void uploadSoundLight(String ip, MultipartFile multipartFile, String uploadFileName) {
        InputStream ins = null;
        String fileName = multipartFile.getOriginalFilename();
        String ext = fileName.substring(fileName.lastIndexOf("."));
        if (!ext.equals(".mp3")) {
            throw new ExecuteException(MessageUtils.getMessage("service.only_mp3_file_can_be_upload"));
        }
        try {
            LOGGER.debug("上传音频文件");
            uploadFileName = uploadFileName.replace(ext, "");//去除后缀名称
            JSONObject sendDataJson = new JSONObject();
            ins = multipartFile.getInputStream();
            byte[] buffer = new byte[(int) multipartFile.getSize()];
            ins.read(buffer);
            String audioFile = Base64.getEncoder().encodeToString(buffer);
            sendDataJson.put("audioName", uploadFileName);
            //出去base64的换行符和空格
            sendDataJson.put("audioData", audioFile.replace("\n", "").replace("\r", ""));
            sendDataJson.put("type", 1);
            //将音频数据上传到Pilot
            super.execute(ip, agvConfigPort, uploadAudio, sendDataJson);
            //将音频文件上传到本地
            uploadFileToLocal(audioFile, uploadFilePath,uploadFileName + ".mp3");
        } catch (Exception e) {
            throw new ExecuteException(MessageUtils.getMessage("action.uploading_audio_file_fail"));
        } finally {
            try {
                if (ins != null) {
                    ins.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void downLoadSoundLight(String ip, String fileName, HttpServletResponse response) throws IOException {
        JSONObject sendDataJson = new JSONObject();
        OutputStream out = null;
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-type", "audio/mpeg;charset=utf-8");
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(fileName.getBytes("UTF-8"), "iso8859-1"));
            String audioData = null;
            if (fileExist(fileName)) {
                File downAudioFile = new File(uploadFilePath+fileName);
                InputStream ins = new FileInputStream(downAudioFile);
                byte [] buffer = new byte[(int) downAudioFile.length()];
                ins.read(buffer);
                audioData = Base64.getEncoder().encodeToString(buffer);
                LOGGER.debug("本地下载音频");
            } else {
                String ext = fileName.substring(fileName.lastIndexOf("."));
                fileName = fileName.replace(ext, "");
                sendDataJson.put("audioName", fileName.replace("\n", "").replace("\t", ""));
                Map<String, Object> audioFile = super.execute(ip, agvConfigPort, downloadAudio, sendDataJson);
                if (CollectionUtils.isEmpty(audioFile)) {
                    throw new ExecuteException(MessageUtils.getMessage("service.file_data_is_null"));
                }
                audioData = (String) audioFile.get("audioData");//获取音频数据(BASE64格式)
                uploadFileToLocal(audioData,uploadFilePath,fileName+".mp3");
            }
            byte[] audioByte = Base64.getDecoder().decode(audioData);
            out = response.getOutputStream();
            out.write(audioByte);
            out.flush();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    @Override
    public Map<String, Object> querySoundLight(String ip) throws IOException {
        Map<String, Object> resultMap = super.execute(ip, agvConfigPort, querySoundLight, null);
        if (CollectionUtils.isEmpty(resultMap)) {
            return null;
        }
        //根据时间进行排序
        List<Map<String, String>> audioNames = (List<Map<String, String>>) resultMap.get("info");
        audioNames.sort((o1, o2) -> {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss yyyy", Locale.UK);
                Date dt1 = sdf.parse(o1.get("audioDate"));
                Date dt2 = sdf.parse(o2.get("audioDate"));
                if ((dt1.getTime()) > (dt2.getTime())) {
                    return -1;
                } else if (dt1.getTime() < dt2.getTime()) {
                    return 1;
                } else {
                    return 0;
                }
            } catch (Exception e) {
                throw new ExecuteException(e.getMessage());
            }
        });
        Map<String, Object> audioMap = new HashMap<>();
        audioMap.put("audioName", audioNames);
        LOGGER.debug("获取声光列表" + audioNames.toString());
        return audioMap;
    }

    @Override
    public void deleteAudioFile(String ip, String fileName) throws IOException {
        JSONObject sendJsonData = new JSONObject();
        String ext = fileName.substring(fileName.lastIndexOf("."));
        fileName = fileName.replace(ext, "");
        sendJsonData.put("type", 2);
        sendJsonData.put("audioName", fileName);
        LOGGER.debug("删除音频文件 audioName" + fileName);
        super.execute(ip, agvConfigPort, uploadAudio, sendJsonData);
    }

    @Override
    public Map<String, Object> queryLightList(String ip) {
        String[] lightList = {"RedLight","RedBreath", "RedBlink", "GreenLight", "GreenBreath", "GreenBlink", "YellowLight","YellowBreath", "YellowBlink",
                "BlueLight", "BlueBreath", "PurpleLight", "PurpleBreath", "WhiteBreath", "RainbowBreath"};
        Map<String, Object> jsonParam = new HashMap<>();
        jsonParam.put("lights", lightList);
        return jsonParam;
    }

    //异步上传文件到本地
    @Async("asyThread")
    public void uploadFileToLocal(String audioData, String filePath, String fileName) throws IOException {
        OutputStream out = null;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                file.mkdirs();
            }
            file = new File(filePath + fileName);
            out = new FileOutputStream(file);
            byte[] audioByte = Base64.getDecoder().decode(audioData);
            out.write(audioByte);
        } catch (Exception e) {
            throw new ExecuteException(MessageUtils.getMessage("action.uploading_audio_file_fail"));
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    //判断目录中是否包含需要下载的音频
    private boolean fileExist(String fileName) {
        File file = new File(uploadFilePath);
        if(!file.exists()){
            return false;
        }
        String[] fileNames = file.list();
        return Arrays.asList(fileNames).contains(fileName);
    }

}
