package com.youibot.agv.scheduler.engine.pathplan.data;

import com.youibot.agv.scheduler.engine.util.SidePathUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.CloneUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.youibot.agv.scheduler.constant.ActionConstant.MARKER_NAVIGATION_TYPE_NORMAL;

/**
 * @version V1.0
 * @Title:
 * @ClassName: com.youibot.agv.scheduler.engine.pathplan.data
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/11/7 20:46
 */
public class MapMatrixDataUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(MapMatrixDataUtil.class);

    private static MapMatrixData mapMatrixData = (MapMatrixData) ApplicationUtils.getBean("mapMatrixData");

    /**
     * 判断边是否被包含 返回true代表包含 false代表不包含
     */
    public static Boolean containedSidePath(Map<String, Map<String, Double>> matrix, SidePath sidePath) {
        return matrix.get(sidePath.getStartMarkerId()).get(sidePath.getEndMarkerId()) != null;//占用边
    }

    /**
     * 获取反向边
     *
     * @param sidePath
     * @return
     */
    public static SidePath getReverseSidePath(SidePath sidePath) {
        String startMarkerId = sidePath.getStartMarkerId();
        String endMarkerId = sidePath.getEndMarkerId();
        SidePath reverseSidePath = mapMatrixData.getStartEndMarkerIdToSidePath().get(endMarkerId + startMarkerId);
        return reverseSidePath;
    }

    //恢复单向边 返回true代表成功 false代表失败
    public static void recoverySidePath(Map<String, Map<String, Double>> matrix, SidePath sidePath) {
        if (sidePath == null) {
            LOGGER.debug("恢复单向边输入参数为null");
            return;
        }
        matrix.get(sidePath.getStartMarkerId()).put(sidePath.getEndMarkerId(), sidePath.getLength());
    }

    //恢复反向边 返回true代表成功 false代表失败
    public static void recoveryReverseSidePath(Map<String, Map<String, Double>> matrix, SidePath sidePath) {
        if (sidePath == null) {
            LOGGER.debug("恢复反向边输入参数为null");
            return;
        }
        SidePath sidePathReverse = getReverseSidePath(sidePath);
        if (sidePathReverse == null) {
            LOGGER.debug("反向边不存在");
            return;
        }
        recoverySidePath(matrix, sidePathReverse);
    }

    //恢复双向边
    public static void recoverDoubleSidePath(Map<String, Map<String, Double>> matrix, SidePath sidePath) {
        recoverySidePath(matrix, sidePath);
        recoveryReverseSidePath(matrix, sidePath);
    }

    //设置堵塞路径的权重
    public static void setJamSidePaths(Map<String, Map<String, Double>> filterMatrix, Set<SidePath> jamSidePaths) {
        jamSidePaths.forEach(sidePath -> {
            String startMarkerId = sidePath.getStartMarkerId();
            String endMarkerId = sidePath.getEndMarkerId();
            filterMatrix.get(startMarkerId).put(endMarkerId, sidePath.getLength());
        });
    }

    public static void initFloor() {
        mapMatrixData.initFloor();
    }

    //恢复Marker点连接的所有边
    public void recoveryMarker(Map<String, Map<String, Double>> matrix, Marker marker) {
        Set<String> set = mapMatrixData.getIsToj().get(marker.getId());
        set.forEach(startMarkerId -> {
            SidePath sidePath = mapMatrixData.getStartEndMarkerIdToSidePath().get(startMarkerId + marker.getId());
            matrix.get(marker.getId()).put(startMarkerId, sidePath.getLength());
        });
    }

    //删除传参进来的所有的路径边
    public static void deleteSidePaths(Map<String, Map<String, Double>> matrix, Set<SidePath> sidePaths) {
        if (sidePaths != null) {
            sidePaths.forEach(sidePath -> deleteSidePath(matrix, sidePath));
        }
    }

    public static void deleteReverseSidePaths(Map<String, Map<String, Double>> matrix, List<SidePath> sidePaths) {
        if (sidePaths != null) {
            sidePaths.forEach(sidePath -> deleteReverseSidePath(matrix, sidePath));
        }
    }

    //删除传参进来的所有的路径边和其反向边
    public static void deleteDoubleSidePaths(Map<String, Map<String, Double>> matrix, List<SidePath> sidePaths) {
        if (sidePaths != null) {
            sidePaths.forEach(sidePath -> deleteDoubleSidePath(matrix, sidePath));
        }
    }

    //删除路径边
    public static void deleteSidePath(Map<String, Map<String, Double>> matrix, SidePath sidePath) {
        if (sidePath != null) {
            String startMarkerId = sidePath.getStartMarkerId();
            String endMarkerId = sidePath.getEndMarkerId();
            matrix.get(startMarkerId).remove(endMarkerId);
        }
    }

    //删除反向边
    public static void deleteReverseSidePath(Map<String, Map<String, Double>> matrix, SidePath sidePath) {
        if (sidePath != null) {
            SidePath sidePathReverse = getReverseSidePath(sidePath);
            if (sidePathReverse != null) {
                deleteSidePath(matrix, sidePathReverse);
            }
        }
    }

    //删除双向路径边
    public static void deleteDoubleSidePath(Map<String, Map<String, Double>> matrix, SidePath sidePath) {
        if (sidePath == null) {
            return;
        }
        //1.删除这条边
        deleteSidePath(matrix, sidePath);
        //2.删除这条边的反向边
        deleteReverseSidePath(matrix, sidePath);
    }

    //删除所有一步到达Marker点的路径
    public static void deleteMarkers(Map<String, Map<String, Double>> matrix, Set<Marker> markers) {
        markers.forEach(marker -> deleteMarker(matrix, marker));
    }

    //删除Marker点连接的所有边
    public static void deleteMarker(Map<String, Map<String, Double>> matrix, Marker marker) {
        if (marker == null) {
            return;
        }
        Set<String> set = mapMatrixData.getIsToj().get(marker.getId());
        set.forEach(startMarkerId -> matrix.get(startMarkerId).remove(marker.getId()));
    }


    public static Set<Marker> getBackMarkers(LinkedList<SidePath> sidePaths, SidePath currentSidePath, Double distance) {
        Set<Marker> markers = new HashSet<>();
        double currentDistance = SidePathUtils.getDistance(currentSidePath);
        if (currentDistance > distance / 2.0) {
            //当前路径的剩余距离大于安全距离时，不占用任何点，返回null
            return markers;
        } else {
            //当前路径的剩余距离小于安全距离时，需要继续搜索，直到到达安全距离或者路径结束，返回需要申请占用的marker点
            markers.add(mapMatrixData.getMarkerIdToMarker().get(currentSidePath.getStartMarkerId()));
            double totalDistance = currentDistance;
            if (sidePaths.contains(currentSidePath)) {
                int k = sidePaths.indexOf(currentSidePath);
                for (k = k - 1; k > -1; k--) {
                    totalDistance += sidePaths.get(k).getLength();
                    if (totalDistance > distance / 2.0) {
                        return markers;
                    } else {
                        markers.add(mapMatrixData.getMarkerIdToMarker().get(sidePaths.get(k).getStartMarkerId()));
                    }
                }
            }
        }
        return markers;
    }

    public static List<Marker> getNextMarkers(LinkedList<SidePath> sidePaths, SidePath currentSidePath, Double distance) {
        List<Marker> markers = new ArrayList<>();
        double distanceRemaing = SidePathUtils.getDistanceRemaining(currentSidePath);
        if (distanceRemaing > distance) {
            //当前路径的剩余距离大于安全距离时，不占用任何点，返回null
            return markers;
        } else {
            //当前路径的剩余距离小于安全距离时，需要继续搜索，直到到达安全距离或者路径结束，返回需要申请占用的marker点
            markers.add(mapMatrixData.getMarkerIdToMarker().get(currentSidePath.getEndMarkerId()));
            double totaldistance = distanceRemaing;
            if (sidePaths.contains(currentSidePath)) {
                int k = sidePaths.indexOf(currentSidePath);
                for (k = k + 1; k < sidePaths.size(); k++) {
                    totaldistance += sidePaths.get(k).getLength();
                    if (totaldistance > distance) {
                        return markers;
                    } else {
                        markers.add(mapMatrixData.getMarkerIdToMarker().get(sidePaths.get(k).getEndMarkerId()));
                    }
                }
            }
        }
        return markers;
    }

    //深拷贝动态变化的邻接地图
    public static ConcurrentHashMap<String, Map<String, Double>> getCloneMatrix() {
        return CloneUtils.clone(mapMatrixData.getMatrix());
    }

    //从起止markerId坐标计算得到sidepath路径
    public static SidePath getSidePath(String startMarkerId, String endMarkerId) {
        return mapMatrixData.getStartEndMarkerIdToSidePath().get(startMarkerId + endMarkerId);
    }

    //从markerId得到Marker对象
    public static Marker getMarkerByMarkerId(String markerId) {
        return mapMatrixData.getMarkerIdToMarker().get(markerId);
    }

    //从sidepathId得到sidepath对象 获取地图中的所有
    public static SidePath getSidePathBySidePathId(String sidePathId) {
        return mapMatrixData.getSidePathIdToSidePath().get(sidePathId);
    }

    //获取地图中的所有marker点
    public static List<Marker> getMarkersByAGVMapId(String agvMapId) {
        return mapMatrixData.getAgvMapIdToMarkers().get(agvMapId);
    }

    //获取地图中的所有sidepath
    public static List<SidePath> getSidePathsByAGVMapId(String agvMapId) {
        return mapMatrixData.getAgvMapIdToSidePaths().get(agvMapId);
    }

    //检查marker点是否不存在或者被禁用掉 false:被禁用或不存在
    public static boolean checkAimMarker(String aimMarkerId) {
        Marker marker = mapMatrixData.getMarkerIdToMarker().get(aimMarkerId);
        if (marker == null) {
            return false;
        }
        if (!mapMatrixData.getActiveMarkers().contains(marker)) {
            return false;
        }
        if (mapMatrixData.getDisableMarkers().contains(marker)) {
            return false;
        }
        return true;
    }

    public static void addAGVMap(String agvMapId) {
        mapMatrixData.addAGVMap(agvMapId);
    }

    public static void removeAGVMap(String agvMapId) {
        mapMatrixData.removeAGVMap(agvMapId);
    }

    public static Set<Marker> getActiveMarkers() {
        return mapMatrixData.getActiveMarkers();
    }

    public static Integer getMarkerNavigationType(String markerId) {
        Integer markerNavigationType = mapMatrixData.getMarkerIdToMarkerNavigationType().get(markerId);
        if (markerNavigationType == null) {
            return MARKER_NAVIGATION_TYPE_NORMAL;
        }
        return markerNavigationType;
    }

}
