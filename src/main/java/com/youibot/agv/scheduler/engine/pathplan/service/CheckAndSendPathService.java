package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.engine.pathplan.entity.SidePathPlanResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.PositionMessage;
import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;

/**
 * <AUTHOR>
 * @Date :Created in 下午4:57 2020/8/15
 * @Description :
 * @Modified By :
 * @Version :
 */
public interface CheckAndSendPathService {

    Map<String, String> getAgvCodeLock();

    boolean addSidePathPlanResult(SidePathPlanResult sidePathPlanResult, PathPlanMessage pathPlanMessage);

    PathPlanMessage delSidePathPlanResult(String agvCode);

    String getStartMarkerId(String agvCode);

    boolean checkAndReSendSidePath(String agvCode,
                                   VehicleLocation location,
                                   LinkedBlockingDeque<SidePath> planedSidePaths,
                                   LinkedBlockingDeque<SidePath> runningSidePaths,
                                   LinkedBlockingDeque<SidePath> executedSidePaths,
                                   boolean onlySendOneSidePath,
                                   boolean sendSidePath,
                                   String exceptMarkerId);

    void agvPositionCallBack(PositionMessage positionMessage);

    //清理AGV
    void clear(String agvCode, String missionWorkId);

    //启用和禁用地图时清理所有的缓存路径
    void clear();

    //启用和禁用地图时清理指定地图所有的缓存路径
    void clear(String agvMapId);

    PathPlanMessage getPathPlanMessage(String vehicleId);

    List<PathPlanMessage> getPathPlanMessageByMapName(String mapName);

    /**
     * 获得当前所有的路径规划中未执行完成的所有的路径点。
     * @return
     */
    Map<String, List<String>> getRunningPathMarkerIds();

    /**
     * 传入一个MarkerId，返回该MarkerId所在单机区域的所有MarkerIds
     * @param markerId
     * @return
     */
    List<String> getMarkerIdsBySingleAreaMarkerId(String markerId);

    //清理AGV
    void clear(String agvCode, VehicleLocation location, String missionWorkId);

    VehicleLocation setAGVPosition(String agvCode, VehicleLocation location);

    void modifyAutoWeight(String agvCode, LinkedBlockingDeque<SidePath> sidePaths, String type, Double obverseCost, Double reverseCost);

    boolean sendMqttSidePathMessage(String agvCode, LinkedBlockingDeque<SidePath> piecemealSidePaths, LinkedBlockingDeque<SidePath> plannedSidePaths);

    void convertPlanedSidePathsToRunningSidePaths(String agvCode, LinkedBlockingDeque<SidePath> first, LinkedBlockingDeque<SidePath> second);

    void convertRunningSidePathsToExecutedSidePaths(String agvCode, SidePath sidePath);

    ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToRunningSidePaths();

    ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToPlanedSidePaths();

    ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToExecutedSidePaths();

    ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToTotalSidePaths();

    /**
     * 清理agv的所占资源
     * @param agvCode
     */
    void clearAgvResource(String agvCode);

    List<Vehicle> getUnMoveVehicles(String mapName);

    void reApplyResource(Vehicle vehicle);

	



}
