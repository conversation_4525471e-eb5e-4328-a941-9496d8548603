package com.youibot.agv.scheduler.engine.service.elevator;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.entity.ElevatorFloor;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Date;

import static com.youibot.agv.scheduler.constant.ElevatorConstant.*;

/**
 * 太极电梯api服务
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/7/30 19:33
 */
@Component
public class TaiChiElevatorApiService implements ElevatorApiService {

    @Autowired
    private HttpClientService httpClientService;

    //发送电梯申请(呼叫)指令
    @Override
    public void elevatorApply(ElevatorFloor elevatorFloor) throws IOException {
        String url = "http://" + elevatorFloor.getIp() + ":" + elevatorFloor.getPort() + "/elevatorcall";
        JSONObject requestParam = new JSONObject();
        requestParam.put("requestid", elevatorFloor.getMissionWorkId());
        requestParam.put("orifloor", elevatorFloor.getOriFloor().toString());
        requestParam.put("dirfloor", elevatorFloor.getDirFloor().toString());
        checkCallReturn(httpClientService.doPost(url, requestParam));
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_APPLIED);
    }

    //检测是否到达呼叫楼层并打开电梯门
    @Override
    public void elevatorOpenByComeIn(ElevatorFloor elevatorFloor) throws InterruptedException {
        long startTime = System.currentTimeMillis();//开始时间
        Integer applyElevatorTimeout = elevatorFloor.getApplyTimeout() * 1000;//申请电梯超时时间
        while (!ELEVATOR_STATUS_CALL_LAYER_OPEN.equals(elevatorFloor.getElevatorStatus())) {//没有到达呼叫层打开电梯门，进入循环
            if ((new Date().getTime() - startTime) > applyElevatorTimeout) {//超时
                throw new ExecuteException(MessageUtils.getMessage("action.elevator_apply_time_out"));
            }
            if (ELEVATOR_STATUS_ERROR.equals(elevatorFloor.getElevatorStatus())) {
                throw new ExecuteException(MessageUtils.getMessage("action.elevator_abnormal"));
            }
            Thread.sleep(500);
        }
    }

    //发送关闭电梯指令
    @Override
    public void elevatorCloseByComeIn(ElevatorFloor elevatorFloor) throws IOException {
        String url = "http://" + elevatorFloor.getIp() + ":" + elevatorFloor.getPort() + "/elevatoraction";
        JSONObject requestParam = new JSONObject();
        requestParam.put("actionid", elevatorFloor.getMissionWorkId());
        requestParam.put("actiontype", "close");
        checkCallReturn(httpClientService.doPost(url, requestParam));
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_CALL_LAYER_CLOSE);
    }

    //不做操作
    @Override
    public void elevatorMove(ElevatorFloor elevatorFloor) {
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_MOVE);
    }

    //检测是否到达目标楼层并打开电梯门
    @Override
    public void elevatorOpenByComeOut(ElevatorFloor elevatorFloor) throws InterruptedException {
        long startTime = System.currentTimeMillis();//开始时间
        Integer controlElevatorTimeout = elevatorFloor.getControlTimeout() * 1000;//控制电梯超时时间
        while (!ELEVATOR_STATUS_TARGET_LAYER_OPEN.equals(elevatorFloor.getElevatorStatus())) {//没有到达目标层打开电梯门，进入循环
            if ((new Date().getTime() - startTime) > controlElevatorTimeout) {//超时
                throw new ExecuteException(MessageUtils.getMessage("action.elevator_move_time_out"));
            }
            if (ELEVATOR_STATUS_ERROR.equals(elevatorFloor.getElevatorStatus())) {
                throw new ExecuteException(MessageUtils.getMessage("action.elevator_abnormal"));
            }
            Thread.sleep(500);
        }
    }

    //不做操作
    @Override
    public void elevatorCloseByComeOut(ElevatorFloor elevatorFloor) {
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_TARGET_LAYER_CLOSE);
    }

    //发送释放电梯指令
    @Override
    public void elevatorRelease(ElevatorFloor elevatorFloor) throws IOException {
        String url = "http://" + elevatorFloor.getIp() + ":" + elevatorFloor.getPort() + "/elevatoraction";
        JSONObject requestParam = new JSONObject();
        requestParam.put("actionid", elevatorFloor.getMissionWorkId());
        requestParam.put("actiontype", "release");
        checkCallReturn(httpClientService.doPost(url, requestParam));
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_RELEASED);
    }

    /**
     * 校验太极远程接口的返回
     * @param httpResult
     */
    private void checkCallReturn(HttpResult httpResult) {
        if (httpResult == null || StringUtils.isEmpty(httpResult.getBody())) {
            throw new ActionException(MessageUtils.getMessage("action.http_post_return_error"));
        }
        Integer resultCode = httpResult.getCode();
        if (200 <= resultCode && resultCode < 300) {//接口调用返回成功
            JSONObject resultBody = JSONObject.parseObject(httpResult.getBody());
            String code = resultBody.getString("code");//结果编码 1为成功, 其他为失败
            if (!TAI_CHI_RESULT_CODE_CORRECT.equals(code)) {
                throw new ExecuteException(MessageUtils.getMessage("action.http_post_return_error") + " code=" + code);
            }
        } else {//失败
            throw new ExecuteException(MessageUtils.getMessage("action.http_post_return_error") + " resultCode=" + resultCode);
        }
    }
}
