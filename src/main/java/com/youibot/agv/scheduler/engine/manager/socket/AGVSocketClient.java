package com.youibot.agv.scheduler.engine.manager.socket;

import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

/**
 * socket连接客户端
 *
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月13日 下午4:18:30
 */
@Data
public class AGVSocketClient {

    private Socket socket;
    private OutputStream out;
    private InputStream in;
    private String address; // AGVSocket连接的ip地址
    private int port; // 端口号

    private int protocolType;// 协议类型，0代表数据协议，1代表心跳协议

    private static final Logger LOGGER = LoggerFactory.getLogger(AGVSocketClient.class);

    public AGVSocketClient() {
    }

    public AGVSocketClient(String address, int port) {
        this.address = address;
        this.port = port;
    }

    private Integer heartbeatPort = AGVPropertiesUtils.getInteger("AGV_SOCKET.PORT.HEARTBEAT");
    private Integer mapPort = AGVPropertiesUtils.getInteger("AGV_SOCKET.PORT.MAP");
    private Integer config = AGVPropertiesUtils.getInteger("AGV_SOCKET.PORT.CONFIG");
    
    

    private Integer audioSoTimeout = AGVPropertiesUtils.getInteger("AGV_SOCKET.AUDIO_SOTIMEOUT");
    private Integer socketSoTimeout = AGVPropertiesUtils.getInteger("AGV_SOCKET.SOTIMEOUT");
    private Integer heartbeatSocketSoTimeout = AGVPropertiesUtils.getInteger("AGV_SOCKET.HEARTBEAT_SOTIMEOUT");
    private Integer mapSocketSoTimeout = AGVPropertiesUtils.getInteger("AGV_SOCKET.MAP_SOTIMEOUT");

    // 创建AGV client 类
    public static synchronized AGVSocketClient createAGVClient(String address, Integer port) throws IOException {
        AGVSocketClient client = new AGVSocketClient(address, port);
        client.create();
        return client;
    }

    /**
     * socket连接启动
     */
    public void create() throws IOException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("socket创建连接........" + Thread.currentThread().getName());
        }
        socket = new Socket(address, port);
        //心跳socket连接超时时间与普通socket超时时间短
        if (port == heartbeatPort) {
            socket.setSoTimeout(heartbeatSocketSoTimeout);
        } else if (port == mapPort) {
            socket.setSoTimeout(mapSocketSoTimeout);
        } else if (port == config) {
            socket.setSoTimeout(audioSoTimeout);
        } else {
            socket.setSoTimeout(socketSoTimeout);
        }

        out = socket.getOutputStream();
        in = socket.getInputStream();
        LOGGER.warn("create socket client: " + socket);
    }

    public DataProtocol sendAndReceive(DataProtocol protocol) throws IOException, AGVResultException {
        //LOGGER.info("send to agv: "+ JSON.toJSON(protocol));
        AGVSocketUtils.write2Stream(protocol, out);// 向客户端发送数据
        return AGVSocketUtils.readFromStream(protocol, in);// 读取并解析返回数据
    }

    public byte[] receiveTimeDomain(int time) throws IOException {
        return AGVSocketUtils.readTimeFromStream(in, time);
    }

    public byte[] receiveFrequencyDomain(int time) throws IOException {
        return AGVSocketUtils.readFrequencyFromStream(in, time);
    }

    /**
     * socket连接关闭
     */
    public void stop() {
        try {
            LOGGER.warn("close socket client:" + socket);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("socket关闭连接........" + Thread.currentThread().getName());
            }
            if (socket != null && !socket.isClosed()) {
                socket.shutdownOutput();
                socket.shutdownInput();
                AGVSocketUtils.closeOutputStream(out);
                out = null;
                AGVSocketUtils.closeInputStream(in);
                in = null;
                socket.close();
            }
        } catch (IOException e) {
            LOGGER.error("Close socket error.", e);
        }
    }

}
