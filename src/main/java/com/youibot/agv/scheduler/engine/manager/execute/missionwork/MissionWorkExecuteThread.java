//package com.youibot.agv.scheduler.engine.manager.execute.missionwork;
//
//import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
//import com.youibot.agv.scheduler.engine.util.MissionActionSortUtils;
//import com.youibot.agv.scheduler.entity.MissionAction;
//import com.youibot.agv.scheduler.service.MissionActionService;
//import com.youibot.agv.scheduler.service.MissionWorkService;
//import com.youibot.agv.scheduler.util.MessageUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Scope;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//import static com.youibot.agv.scheduler.constant.AGVConstant.TASK_STATUS_FREE;
//import static com.youibot.agv.scheduler.constant.MissionConstant.*;
//
///**
// * <AUTHOR>  E-mail:<EMAIL>
// * @version CreateTime: 2019/9/29 14:35
// */
//@Service
//@Scope("prototype")
//public class MissionWorkExecuteThread extends MissionWorkThread {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkExecuteThread.class);
//
//    @Autowired
//    private ActionsExecute actionsExecute;
//
//    @Autowired
//    private MissionActionService missionActionService;
//
//    @Autowired
//    private MissionWorkService missionWorkService;
//
//    @Autowired
//    private MissionWorkThreadPool missionWorkThreadPool;
//
//    @Override
//    public void run() {
//        if (LOGGER.isDebugEnabled()) {
//            LOGGER.debug("Thread " + Thread.currentThread().getName() + " run sync task message:");
//        }
//        if (Thread.interrupted()) {//如果当前线程被中断了
//            return;
//        }
//        if (vehicle == null) {
//            missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
//            missionWork.setMessage(MessageUtils.getMessage("action.vehicle_is_null"));
//            missionWork.setErrorCode(ExceptionInfoEnum.PARAMS_EMPTY.getErrorCode());
//            missionWorkService.updateStatus(missionWork);
//            return;
//        }
//        // 获取MissionAction列表并排序
//        List<MissionAction> missionActions = missionActionService.selectByMissionIdAndIsNotSubAction(missionWork.getMissionId());
//        MissionActionSortUtils.sortMissionActionListBySequence(missionActions);
//
//        missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_RUNNING);//missionWork执行中
//        if (actionsExecute.executeActions(vehicle, missionActions)) {
//            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SUCCESS);//missionWork执行成功
//            vehicle.setMissionWork(null);
//            vehicle.setWorkStatus(TASK_STATUS_FREE);
//            missionWorkThreadPool.detach(this);
//        }
//    }
//
//}
