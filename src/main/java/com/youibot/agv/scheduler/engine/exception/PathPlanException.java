package com.youibot.agv.scheduler.engine.exception;

import com.youibot.agv.scheduler.exception.YOUIFleetException;

/**
 * <AUTHOR> E-mail:l<PERSON><PERSON><PERSON><PERSON>@youibot.com
 * @version CreateTime:2019/4/19 14:05
 */
public class PathPlanException extends YOUIFleetException {

    private String message;//完整信息描述，后台系统拼接更完整信息（如指定是哪台agv在去那个目标点出的错）

    public PathPlanException() {
    }

    public PathPlanException(String message) {
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public PathPlanException(int code, String msg, String errorMessage) {
        super(code, msg+" : "+errorMessage);
    }

    public PathPlanException(int code, String message) {
        super(code, message);
    }
}
