package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_RUNNING;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_WAIT;

/**
 * 等待继续执行action
 *
 * <AUTHOR>  E-mail:shis<PERSON><EMAIL>
 * @version CreateTime: 2019/12/10 10:26
 */
@Service
@Scope("prototype")
public class WaitAction extends DefaultAction {

    private static final Logger LOGGER = LoggerFactory.getLogger(WaitAction.class);

    @Autowired
    private MissionWorkService missionWorkService;

    @Override
    public Map<String, Object> execute(Vehicle vehicle) throws InterruptedException {
        try {
            super.vehicle = vehicle;
            JSONObject paramJson = super.getParamJson();
            Long now = System.currentTimeMillis();//获取当前时间
            Integer waitTime = paramJson.get("time") != null ? paramJson.getInteger("time") : 0;//等待时长, 单位:秒
            Long end = waitTime * 1000 + now;//结束等待时间
            //将missionWork状态置为等待继续执行状态
            MissionWork missionWork = vehicle.getMissionWork();
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_WAIT);
            while (true) {
                if (Thread.interrupted()) {//如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }
                if (System.currentTimeMillis() >= end && waitTime != 0) {
                    missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_RUNNING);
                    LOGGER.debug("wait action end by time out");
                    return null;
                }
                //检测missionWork状态，如果已被置换为运行中，action结束
                if (MISSION_WORK_STATUS_RUNNING.equals(missionWork.getStatus())) {
                    LOGGER.debug("wait action end by manual continue");
                    return null;
                }
                Thread.sleep(100);
            }
        } catch (InterruptedException e) {
            LOGGER.warn("mission action name : " + missionWorkAction.getName() + " thread is interrupted,");
            throw e;
        } catch (Exception e) {
//            LOGGER.warn("mission action name : " + missionWorkAction.getName() + " execute action error", e);
//            throw new ActionException(e.getMessage());
//            throw new ActionException("wait action error, " + e.getMessage());
            LOGGER.error("wait action error, " + e.getMessage());
            throw new ActionException(ErrorEnum.AGV_PILOT_ERROR.code(), MessageUtils.getMessage("vehicle.pilot_exception"));
        } finally {
            if (this.client != null) {
                this.client.stop();
                this.client = null;
            }
        }
    }

    @Override
    public String getAPICode() {
        return null;
    }
}
