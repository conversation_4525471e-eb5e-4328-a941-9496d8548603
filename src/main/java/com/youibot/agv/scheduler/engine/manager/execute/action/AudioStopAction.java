package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

@Service
@Scope("prototype")
public class AudioStopAction extends DefaultAction {

    private final static Logger LOGGER = LoggerFactory.getLogger(AudioStopAction.class);

    @Override
    public String getAPICode() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.AUDIO_STOP");
    }
}
