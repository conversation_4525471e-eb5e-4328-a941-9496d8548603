package com.youibot.agv.scheduler.engine.manager.modbus;

import com.intelligt.modbus.jlibmodbus.Modbus;
import com.intelligt.modbus.jlibmodbus.exception.ModbusIOException;
import com.intelligt.modbus.jlibmodbus.exception.ModbusNumberException;
import com.intelligt.modbus.jlibmodbus.exception.ModbusProtocolException;
import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.intelligt.modbus.jlibmodbus.master.ModbusMasterFactory;
import com.intelligt.modbus.jlibmodbus.tcp.TcpParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Modbus工具类
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2021/11/11 16:30
 */
public class JLibModbusUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(JLibModbusUtils.class);

    public static ModbusMaster createModbusMaster(String ip, int port, int timeOut) throws UnknownHostException {
        TcpParameters tcpParameters = new TcpParameters();
        tcpParameters.setHost(InetAddress.getByName(ip));
        tcpParameters.setKeepAlive(true);// TCP设置长连接
        tcpParameters.setPort(port);
        ModbusMaster master = ModbusMasterFactory.createModbusMasterTCP(tcpParameters);
        master.setResponseTimeout(timeOut);
        Modbus.setAutoIncrementTransactionId(true);
        return master;
    }

    public static ModbusMaster createModbusMaster(String ip, int port) throws UnknownHostException {
        return createModbusMaster(ip, port, 5000);
    }

    public static void release(ModbusMaster master) {
        if (master != null) {
            try {
                master.disconnect();
            } catch (Exception e) {
                LOGGER.error("断开modbus连接出错, ", e);
            }
        }
    }

    /**
     * 读单个线圈寄存器 01
     */
    public static boolean readCoil(String ip, int port, int serverAddress, int address) throws UnknownHostException, ModbusNumberException, ModbusProtocolException, ModbusIOException {
        return JLibModbusUtils.readCoils(ip, port, serverAddress, address, 1)[0];
    }

    /**
     * 读多个线圈寄存器 01
     */
    public static boolean[] readCoils(String ip, int port, int serverAddress, int startAddress, int quantity) throws UnknownHostException, ModbusNumberException, ModbusProtocolException, ModbusIOException {
        ModbusMaster modbusMaster = null;
        try {
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            return modbusMaster.readCoils(serverAddress, startAddress, quantity);
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    /**
     * 度离散输入寄存器 02
     */
    public static boolean[] readDiscreteInputs(String ip, int port, int serverAddress, int startAddress, int quantity) throws ModbusProtocolException, ModbusNumberException, ModbusIOException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            return modbusMaster.readDiscreteInputs(serverAddress, startAddress, quantity);
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    /**
     * 读保持寄存器 03
     */
    public static int[] readHoldingRegisters(String ip, int port, int serverAddress, int startAddress, int quantity) throws ModbusProtocolException, ModbusNumberException, ModbusIOException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            return modbusMaster.readHoldingRegisters(serverAddress, startAddress, quantity);
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    /**
     * 读输入寄存器 04
     */
    public static int[] readInputRegisters(String ip, int port, int serverAddress, int startAddress, int quantity) throws ModbusProtocolException, ModbusNumberException, ModbusIOException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            return modbusMaster.readInputRegisters(serverAddress, startAddress, quantity);
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    /**
     * 写单个线圈寄存器 05
     */
    public static void writeSingleCoil(String ip, int port, int serverAddress, int startAddress, boolean flag) throws ModbusProtocolException, ModbusNumberException, ModbusIOException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            modbusMaster.writeSingleCoil(serverAddress, startAddress, flag);
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    /**
     * 写单个保持寄存器 06
     */
    public static void writeSingleRegister(String ip, int port, int serverAddress, int startAddress, int register) throws ModbusProtocolException, ModbusNumberException, ModbusIOException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            modbusMaster.writeSingleRegister(serverAddress, startAddress, register);
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    /**
     * 写多个线圈寄存器 15
     */
    public static void writeMultipleCoils(String ip, int port, int serverAddress, int startAddress, boolean[] coils) throws ModbusProtocolException, ModbusNumberException, ModbusIOException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            modbusMaster.writeMultipleCoils(serverAddress, startAddress, coils);
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    /**
     * 写多个保持寄存器 16
     */
    public static void writeMultipleRegisters(String ip, int port, int serverAddress, int startAddress, int[] registers) throws ModbusProtocolException, ModbusNumberException, ModbusIOException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            modbusMaster.writeMultipleRegisters(serverAddress, startAddress, registers);
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

}
