package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool;

import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.OPathResource;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.TPathResource;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.ZonePathResource;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date :Created in 11:14 2021/4/1
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
@Component
public class TPathResourcePool extends ZonePathResourcePool<TPathResource> {

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private FullLocationService fullLocationService;

    public void addTPathResources(List<List<String>> tPaths) {
        if (CollectionUtils.isEmpty(tPaths)) return;
        for (Collection<String> tPathMarkerIds : tPaths) {
            if (CollectionUtils.isEmpty(tPathMarkerIds)) continue;
            TPathResource TPathResource = new TPathResource();
            TPathResource.setZonePathResource(new HashSet<>(tPathMarkerIds));
            super.addZonePathResources(TPathResource);
        }
    }

    public void clearTPathResources() {
        super.clearZonePathResources();
    }

    /**
     * 重写One path的查询是否可用的方法。
     * 添加了资源检测，检测区域是否已经被申请，还要检测区域已经被定位占用的
     * 根据所有的机器人的位置遍历是否存在实际定位在区域内的机器人）
     *
     * @param vehicleCode
     * @param pathResourceIds
     * @return
     */
    @Override
    public synchronized boolean queryPathResourceApplyAvailable(String vehicleCode, Set<String> pathResourceIds) {
        // 如果已经有申请占用的车辆了。直接返回false.
        if (!super.queryPathResourceApplyAvailable(vehicleCode, pathResourceIds)) {
            return false;
        }
        /**
         * 如果没有被申请。再检测所有的区域是否被其他机器人占用。
         * 检测所有区域是否被其他机器人占用。有一个区域被其他机器占用。则返回失败。
         */
        for (String pathResourceId : pathResourceIds) {
            if (this.checkOtherVehicleLocationResourceIds(vehicleCode, super.get(pathResourceId))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 遍历所有其他的机器人，检测是否有机器人占用了这个区域。
     * 如果有机器人位置占用这个区域则返回true. 如果没有机器人位置实际占用这个区域则返回false.
     */
    public boolean checkOtherVehicleLocationResourceIds(String currentVehicleCode, TPathResource tPathResource) {
        List<Vehicle> vehicles = vehiclePoolService.selectAll();
        for (Vehicle vehicle : vehicles) {
            if (vehicle.getId().equals(currentVehicleCode)) {
                continue;
            }
            /**
             * 机器人可能在点上，也可能在边上。如果在点上，直接根据点们去查询所有的资源。
             * 如果在边上。可能在多个边上。需要把所有边上上的站点都拿出来和区域进行比对。
             */
            VehicleLocation vehicleLocation = fullLocationService.getVehicleLocation(vehicle.getId());
            if (vehicleLocation != null) {
                // 如果机器人的站点在这个区域内。则返回true.
                if (vehicleLocation.getMarker() != null) {
                    return tPathResource.getZonePathResource().contains(vehicleLocation.getMarker().getId());
                }
                // 如果机器人在多个边上，则一条一条边进行检测。如果有一个边在区域内。则返回true.
                if (vehicleLocation.getSidePaths() != null) {
                    for (SidePath sidePath : vehicleLocation.getSidePaths()) {
                        String startMakerId = sidePath.getStartMarkerId();
                        String endMarkerId = sidePath.getEndMarkerId();
                        if (tPathResource.getZonePathResource().contains(startMakerId) && tPathResource.getZonePathResource().contains(endMarkerId)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public Set<String> queryTPathResourceIdsByAimMarkerIds(LinkedList<String> markerIds, String aimMarkerId) {
        HashSet<String> resultSet = new HashSet<>();
        if (CollectionUtils.isEmpty(markerIds)) {
            return resultSet;
        }
        // 如果aimMarkerId为空。则只返回markerId查询出的poll池。
        if (StringUtils.isEmpty(aimMarkerId)) {
            return queryZonePathResourceIdByPathResourcesId(markerIds.getLast());
        }
        //如果aimMarkerId不为空。则需要过滤一下重合的T路池才是需要申请的池。
        HashSet<String> markerResultSet = new HashSet<>();
        markerResultSet = queryZonePathResourceIdByPathResourcesId(markerIds.getLast());
        HashSet<String> aimResultSet = new HashSet<>();
        aimResultSet = queryZonePathResourceIdByPathResourcesId(aimMarkerId);
        // 必须同时marker点和aim点同时在的资源池才是需要申请的资源池。
        if (!CollectionUtils.isEmpty(markerResultSet)) {
            for (String tPathId : markerResultSet) {
                if (aimResultSet.contains(tPathId)) {
                    resultSet.add(tPathId);
                }
            }
        }
        return resultSet;
    }

    public Set<String> queryTPathResourceIdsByMarkerIds(LinkedList<String> markerIds) {
        return queryZonePathResourceIdsByPathResourcesIds(markerIds);
    }

    public HashSet<String> queryTPathResourceIdByMarkerId(String markerId) {
        return queryZonePathResourceIdByPathResourcesId(markerId);
    }

}

