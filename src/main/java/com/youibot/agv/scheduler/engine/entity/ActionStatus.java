package com.youibot.agv.scheduler.engine.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * pilot动作执行状态类
 * <AUTHOR>  E-mail:shishao<PERSON>@youibot.com
 * @version CreateTime: 2022/3/23 14:51
 */
@Data
public class ActionStatus {

    private String id;
    private String alter_id;

    private String code;//动作编码

    private Integer status;//状态 1、执行中 2、执行失败 3、执行完成 4、暂停

    private Integer error_code;//异常编码, 当status=2时获取改异常编码

    private JSONObject feedback;//返回数据
}
