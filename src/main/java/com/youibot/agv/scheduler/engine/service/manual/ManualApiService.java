package com.youibot.agv.scheduler.engine.service.manual;

import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:songju<PERSON>@youibot.com
 * @version CreateTime: 2019-05-15 11:46
 */
public interface ManualApiService {

    Map<String, Object> openManual(String ip) throws IOException;

    void manualMove(AGVSocketClient client, Double x, Double y, Double angle) throws AGVResultException, IOException;

}
