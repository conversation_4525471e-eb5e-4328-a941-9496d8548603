package com.youibot.agv.scheduler.engine.service.config;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;

import java.io.IOException;

/**
 * <AUTHOR> E-mail:song<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019-05-15 11:46
 */
public interface ConfigApiService {

    JSONObject queryAGVConfigInfo(String ip) throws AGVResultException, IOException;

    void saveAGVConfigInfo(String ip, JSONObject dataJson) throws AGVResultException, IOException;

}
