package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.webSocket.module.SocketReceiveModel;

import java.util.*;

/**
 * @version V1.0
 * @Title:
 * @ClassName: com.youibot.agv.scheduler.engine.pathplan.service
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/11/7 19:56
 */
public interface LocationService {

    //建立marker点的KDTree索引，加速搜索过程
    void buildKDTree(String agvMapId, Collection<Marker> markers);

    //删除地图，删除KDTree
    void removeKDTree(String agvMapId);

    //query_recent
    String queryRecent(String agvMapId, Double x, Double y);

    VehicleLocation getVehicleLocation();

    //根据定位数据和所在地图Id计算机器人位置
    VehicleLocation getVehicleLocation(String agvMapId, double x, double y, long update_time_millis);

    //从AGV路径和定位x,y计算在那条路径上
    SidePath getSidePath(String agvMapId, Double x, Double y, List<SidePath> sidePaths);

    //从AGV路径和路径id，t计算在那条路径上
    SidePath getSidePath(String segmentId, Double t, List<SidePath> sidePaths);

    //从AGV路径和定位x,y计算剩余路径
    LinkedList<SidePath> getRemainingSidePaths(String agvMapId, Double x, Double y, List<SidePath> sidePaths);

    //从AGV路径和路径id，t计算剩余路径
    LinkedList<SidePath> getRemainingSidePaths(String agvMapId, String segmentId, Double t, List<SidePath> sidePaths);

    /**
     * 判断agv是否在移动中,true:在移动中
     */
    Boolean isMoving(Vehicle vehicle);

    Boolean speedIsZero(Vehicle vehicle);

    /**
     * 获取AGV当前错在marker点
     *
     * @return
     */
    Marker getCurrentMarker(String agvMapId, Double x, Double y);

    Boolean isAimMarkerReached(String agvMapId, Double x, Double y, String aimMarkerId);
}
