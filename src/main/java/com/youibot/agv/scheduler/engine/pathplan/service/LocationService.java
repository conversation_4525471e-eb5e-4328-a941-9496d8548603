package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.engine.pathplan.entity.Position;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.PositionMessage;

import java.util.Collection;
import java.util.List;

import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 * @Date :Created in 18:43 2020/8/7
 * @Description :
 * @Modified By :
 * @Version :
 */
public interface LocationService {

    //建立marker点的KDTree索引，加速搜索过程
    void buildKDTree(String agvMapId, Collection<Marker> markers);

    //删除地图，删除KDTree
    void removeKDTree(String agvMapId);

    //query_recent
    String queryRecent(String agvMapId, Double x, Double y);

    VehicleLocation getVehicleLocation(String agvCode);

    VehicleLocation getVehicleLocation(String agvCode, List<SidePath> sidePaths);

    VehicleLocation getVehicleLocation(String agvMapId, double x, double y, long update_time_millis);

    //从AGV路径和定位x,y计算在那条路径上
    VehicleLocation getVehicleLocation(String agvMapId, Double x, Double y, List<SidePath> sidePaths, long update_time_millis);

    //从AGV路径和路径id，t计算在那条路径上
    SidePath getVehicleLocation(String segmentId, Double t, List<SidePath> sidePaths);

    //获取AGV当前错在marker点
    Marker getCurrentMarker(String agvMapId, Double x, Double y);

    //获取AGV当前错在marker点
    MutablePair<SidePath, Double> setCurrentSidePathT0(String agvMapId, Double x, Double y, List<SidePath> sidePaths);

    //设置机器人位置
    void setAGVPosition(String agvCode, PositionMessage positionMessage);

    Position getAgvPosition(String agvCode);

    void startMapChange();

    void endMapChange();

    void releaseEdgeWeight(String agvCode);

    void removeVehicleLocation(String agvCode);

}
