package com.youibot.agv.scheduler.engine.service.move;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.entity.ElevatorFloor;
import com.youibot.agv.scheduler.engine.entity.MapResourceCache;
import com.youibot.agv.scheduler.engine.entity.PathNavigationParam;
import com.youibot.agv.scheduler.engine.pathplan.service.PathSimulationService;
import com.youibot.agv.scheduler.engine.pool.ElevatorFloorPool;
import com.youibot.agv.scheduler.engine.service.elevator.ElevatorApiService;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.bean.callback.ResourceResultMessage;
import com.youibot.agv.scheduler.mqtt.bean.send.ResourceMessage;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.util.LinkedList;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.ActionConstant.*;
import static com.youibot.agv.scheduler.constant.MapConstant.AGV_MAP_TYPE_LASER;
import static com.youibot.agv.scheduler.constant.MapConstant.AGV_MAP_TYPE_VIRTUAL;
import static com.youibot.agv.scheduler.mqtt.constant.MqTopicConstant.MQTT_PUBLISH_RESOURCE;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/8/12 15:41
 */
@Service
public class PathNavigationExecute {

    private static final Logger LOGGER = LoggerFactory.getLogger(PathNavigationExecute.class);
    @Autowired
    private PathNavigationExecute pathNavigationExecute;
    @Autowired
    private PathParamService pathParamService;
    @Autowired
    private AGVPathParamService agvPathParamService;
    @Autowired
    private MarkerService markerService;
    @Autowired
    private AGVMapService agvMapService;
    @Autowired
    private AGVService agvService;
    @Autowired
    private ElevatorService elevatorService;
    @Autowired
    private ElevatorFloorPool elevatorFloorPool;
    @Autowired
    private AdjustActionService adjustActionService;
    @Autowired
    private FloorService floorService;
    @Autowired
    private ElevatorApiService elevatorApiService;
    @Autowired
    private SystemWorkModeService systemWorkModeService;
    @Autowired
    private MissionWorkService missionWorkService;
    @Autowired
    private PathSimulationService pathSimulationService;

    public Map<String, Object> moveBySidePath(LinkedList<SidePath> sidePathList, Vehicle vehicle, String missionWorkId, Map<String, Object> param) throws IOException, InterruptedException {
        while (!CollectionUtils.isEmpty(sidePathList)) {
            SidePath sidePath = sidePathList.get(0);
            switch (sidePath.getNavigationType()) {
                case SIDE_PATH_NAVIGATION_TYPE_NORMAL://正常导航
                    LOGGER.debug("正常路径导航......");
                    LinkedList<SidePath> executeSidePath = getLongestPathByNormalNavigation(sidePathList);//获取连续最长的正常导航类型路径列表
                    moveByNormalNavigation(executeSidePath, vehicle, missionWorkId, param);//发送路径导航（该段路径AGV）
                    break;
                case SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR://进入电梯
                    LOGGER.debug("进入电梯.......");
                    pullElevatorData(vehicle);
                    checkUseElevatorNavigationType(sidePathList);//检测 进入电梯、乘坐电梯、出来电梯三段sidePath是否连续
                    vehicle.setUseElevator(true);
                    ElevatorFloor elevatorFloor = applyElevator(vehicle, sidePathList.get(1), missionWorkId);//申请电梯
                    LOGGER.debug("进入电梯.......打开电梯门");
                    elevatorApiService.elevatorOpenByComeIn(elevatorFloor);//打开电梯门
                    LOGGER.debug("进入电梯.......导航进入电梯");
                    moveBySidePath(sidePath, missionWorkId, param);//导航进入电梯
                    LOGGER.debug("进入电梯.......关闭电梯门");
                    elevatorApiService.elevatorCloseByComeIn(elevatorFloor);//关闭电梯门
                    sidePathList.remove(0);
                    break;
                case SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR://乘坐电梯（包含切换地图）
                    LOGGER.debug("乘坐电梯.......");
                    elevatorApiService.elevatorMove(getFloor(missionWorkId));//移动电梯
                    LOGGER.debug("乘坐电梯.......切换地图、重定位");
                    switchMapBySidePath(sidePath, vehicle);//切换地图、重定位
                    sidePathList.remove(0);
                    break;
                case SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR://出来电梯
                    LOGGER.debug("出来电梯.......");
                    elevatorApiService.elevatorOpenByComeOut(getFloor(missionWorkId));//打开电梯门
                    LOGGER.debug("出来电梯.......导航出电梯");
                    moveBySidePath(sidePath, missionWorkId, param);//导航出电梯
                    LOGGER.debug("出来电梯.......关闭电梯门");
                    elevatorApiService.elevatorCloseByComeOut(getFloor(missionWorkId));//关闭电梯门
                    LOGGER.debug("出来电梯.......释放电梯");
                    elevatorApiService.elevatorRelease(getFloor(missionWorkId));//释放电梯
                    LOGGER.debug("出来电梯.......结束");
                    elevatorFloorPool.detach(missionWorkId);
                    vehicle.setUseElevator(false);
                    sidePathList.remove(0);
                    break;
                default:
                    throw new ExecuteException(MessageUtils.getMessage("action.internal_storage_data_error") + " navigationType=" + sidePath.getNavigationType());
            }
        }
        return null;
    }

    private void pullElevatorData(Vehicle vehicle) throws InterruptedException {
        vehicle.setResourceResultMessage(null);
        MqttUtils.pushMessage(MQTT_PUBLISH_RESOURCE, new ResourceMessage(vehicle.getDeviceNumber()), Integer.MAX_VALUE);
        //等待资源结果返回再处理任务
        Long startTime = System.currentTimeMillis();
        Integer timeOut = AGVPropertiesUtils.getInt("MQTT.RESOURCE_CALLBACK_TIME_OUT");
        while (true) {
            if (vehicle.getResourceResultMessage() != null) {
                break;
            }
            if (System.currentTimeMillis() - startTime > timeOut) {
                throw new ExecuteException(MessageUtils.getMessage("service.mqtt_download_elevator_error"));
            }
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                LOGGER.error("获取电梯数据睡眠出错, ", e);
            }
        }
    }

    //获取操作电梯的楼层数据
    private ElevatorFloor getFloor(String missionWorkId) {
        ElevatorFloor elevatorFloor = elevatorFloorPool.get(missionWorkId);
        if (elevatorFloor == null) {
            throw new ExecuteException(MessageUtils.getMessage("action.floor_is_null"));
        }
        return elevatorFloor;
    }

    //申请电梯
    private ElevatorFloor applyElevator(Vehicle vehicle, SidePath elevatorPath, String missionWorkId) throws IOException {
        String startMarkerId = elevatorPath.getStartMarkerId();
        String endMarkerId = elevatorPath.getEndMarkerId();
        if (StringUtils.isEmpty(endMarkerId) || StringUtils.isEmpty(startMarkerId)) {
            throw new ExecuteException(MessageUtils.getMessage("http.marker_start_or_end_not_exist"));
        }
        Marker startMarker = MapResourceCache.getMarker(startMarkerId);
        Marker endMarker = MapResourceCache.getMarker(endMarkerId);//获取标记点
        if (endMarker == null || startMarker == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.marker_is_null"));
        }
        Floor startFloor;
        Floor endFloor;
        Elevator elevator;
        ResourceResultMessage resourceResultMessage = vehicle.getResourceResultMessage();
        startFloor = resourceResultMessage.getFloor(startMarkerId);
        endFloor = resourceResultMessage.getFloor(endMarkerId);
        elevator = resourceResultMessage.getElevator(startFloor.getElevatorId());

        if (startFloor == null || endFloor == null) {
            LOGGER.error("内部数据存储错误, 起始楼层或者目标楼层数据为空, 起始点:{}, 目标点:{}", startMarkerId, endMarkerId);
            throw new ExecuteException(MessageUtils.getMessage("action.internal_storage_data_error"));
        }
        if (!startFloor.getElevatorId().equals(endFloor.getElevatorId())) {
            LOGGER.error("内部数据存储错误, 路径的起始点{}和终点{}绑定的不是同一个电梯", startMarkerId, endMarkerId);
            throw new ExecuteException(MessageUtils.getMessage("action.internal_storage_data_error"));
        }
//        if (SYSTEM_WORK_MODE_SCHEDULER.equals(systemWorkModeService.getOne(null).getMode())) {
//            ResourceResultMessage resourceResultMessage = VehicleUtils.getVehicle().getResourceResultMessage();
//            elevator = resourceResultMessage.getElevator(startFloor.getElevatorId());
//        } else {
//            elevator = elevatorService.selectById(startFloor.getElevatorId());//获取电梯
//        }
        if (elevator == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.elevator_is_null"));
        }
        //参数依次： 请求ID（任务ID） ip地址  端口号  申请楼层  目标楼层
        ElevatorFloor elevatorFloor = new ElevatorFloor(missionWorkId, elevator.getIp(), elevator.getPort(), startFloor.getNumber(), endFloor.getNumber(), elevator.getApplyTimeout(), elevator.getControlTimeout());
        elevatorFloorPool.attach(elevatorFloor);
        elevatorApiService.elevatorApply(elevatorFloor);
        return elevatorFloor;
    }

    //使用电梯时检测 进入电梯、乘坐电梯、出来电梯三段sidePath是否连续
    private void checkUseElevatorNavigationType(LinkedList<SidePath> sidePathList) {
        if (sidePathList.size() < 3) {
            throw new ExecuteException(MessageUtils.getMessage("action.side_path_elevator_navigation_discontinuity"));
        }
        SidePath comeInElevator = sidePathList.get(0);
        SidePath moveElevator = sidePathList.get(1);
        SidePath comeOutElevator = sidePathList.get(2);
        if (SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR != comeInElevator.getNavigationType() || SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR != moveElevator.getNavigationType()
                || SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR != comeOutElevator.getNavigationType()) {
            throw new ExecuteException(MessageUtils.getMessage("action.side_path_elevator_navigation_discontinuity"));
        }
    }

    //切换地图+重定位(激光地图时需要)
    private void switchMapBySidePath(SidePath sidePath, Vehicle vehicle) throws IOException {
        //获取目标点
        String endMarkerId = sidePath.getEndMarkerId();
        if (StringUtils.isEmpty(endMarkerId)) {
            throw new ExecuteException(MessageUtils.getMessage("http.marker_start_or_end_not_exist"));
        }
        Marker endMarker = MapResourceCache.getMarker(endMarkerId);
        if (endMarker == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.marker_is_null"));
        }
        String agvMapId = endMarker.getAgvMapName();//获取目标地图Id
        if (StringUtils.isEmpty(agvMapId)) {
            throw new ExecuteException(MessageUtils.getMessage("http.params_error") + " agvMapId is null");
        }
        //同步地图
        AGVMap agvMap = MapResourceCache.getAGVMap(agvMapId);//验证该地图是否启用
        vehicle.setAgvMap(agvMap);
        agvService.updateAGVMapIdById(vehicle.getId(), agvMapId);
        // 同步地图
        vehicle.getDefaultVehicleStatus().getMap().setCurrent_map_id(agvMapId);
        //重定位
        if (AGV_MAP_TYPE_LASER.equals(agvMap.getType())) {//如果是激光地图，需要进行重定位
            DefaultVehicleStatus.PositionStatus position = vehicle.getDefaultVehicleStatus().getPosition();
            position.setPos_x(endMarker.getX());
            position.setPos_y(endMarker.getY());
            position.setPos_angle(Math.toRadians(endMarker.getAngle()));

//            Map<String, Object> relocationParam = new HashMap<>();
//            relocationParam.put("init_x", endMarker.getX());
//            relocationParam.put("init_y", endMarker.getY());
//            relocationParam.put("init_angle", Math.toRadians(endMarker.getAngle()));
//            laserRelocationApiService.manualRelocation(vehicle.getIp(), relocationParam);
        }
    }

    //获取最长连续正常行驶的路径列表
    private LinkedList<SidePath> getLongestPathByNormalNavigation(LinkedList<SidePath> sidePathList) {
        LinkedList<SidePath> normalSidePaths = new LinkedList<>();
        for (SidePath sidePath : sidePathList) {
            if (SIDE_PATH_NAVIGATION_TYPE_NORMAL == sidePath.getNavigationType()) {
                normalSidePaths.add(sidePath);
            } else {
                break;
            }
        }
        sidePathList.removeAll(normalSidePaths);
        return normalSidePaths;
    }

    private void moveBySidePath(SidePath sidePath, String missionWorkId, Map<String, Object> param) throws IOException, InterruptedException {
        LinkedList<SidePath> sidePaths = new LinkedList<>();
        sidePaths.add(sidePath);
        MissionWork missionWork = missionWorkService.selectById(missionWorkId);
        moveByNormalNavigation(sidePaths, VehicleUtils.getOnlineVehicle(missionWork.getAgvId()), missionWorkId, param);
    }

    //根据正常行驶的路径列表移动
    private void moveByNormalNavigation(LinkedList<SidePath> executeSidePath, Vehicle vehicle, String missionWorkId, Map<String, Object> param) throws IOException, InterruptedException {
        //请求路径规划，返回sidePath集合，每个sidePath的方向不一定是相同的，遇到相邻sidePath的方向不同时，需要分开发送
        PathNavigationParam pathNavigationParam = null;
        String localization_method = "laser";//定位方式
        if (AGV_MAP_TYPE_VIRTUAL.equals(vehicle.getAGVMapType())) {
            localization_method = "qr";
        }
        Integer direction = null;//agv车的方向 1、正向 2、反向
        for (int i = 0; i < executeSidePath.size(); i++) {
            SidePath sidePath = executeSidePath.get(i);
            AGVPathParam agvPathParam = getAGVPathParam(vehicle.getId(), sidePath, param);
            Integer agvDirection = (sidePath.getAgvDirection() != null && sidePath.getAgvDirection() != 0) ? sidePath.getAgvDirection() : 1;
            if (i == 0) {
                direction = agvDirection;
                pathNavigationParam = new PathNavigationParam(sidePath, agvPathParam, direction, localization_method);
                continue;
            }
            if (agvDirection.equals(direction)) {// 该sidePath的方向与前一个sidePath的方向相同
                pathNavigationParam.addSegment(sidePath, agvPathParam);
            } else {
                // 该sidePath的方向与前一个sidePath的方向不同，发送前面记录的路径移动请求
                sendMoveCommand(vehicle, pathNavigationParam, missionWorkId);//发送移动指令
                direction = agvDirection;
                pathNavigationParam = new PathNavigationParam(sidePath, agvPathParam, direction, localization_method);
            }
        }
        sendMoveCommand(vehicle, pathNavigationParam, missionWorkId);//发送操作指令
    }

    //获取路径参数: 设置路径参数顺序：actionPathParam设置---->pathParam设置---->agvPathParam设置
    private AGVPathParam getAGVPathParam(String agvId, SidePath sidePath, Map<String, Object> param) {
        //获取agvPathParam数据
        Example example = new Example(AGVPathParam.class);
        example.createCriteria().andEqualTo("agvId", agvId);
        AGVPathParam agvPathParam = agvPathParamService.selectOneByExample(example);
        //替换pathParam不为空的数据到agvPathParam中
        PathParam pathParam = pathParamService.selectByPathId(sidePath.getPathId());
        if (pathParam != null) {
            BeanUtils.copyPropertiesIgnoreNull(pathParam, agvPathParam);
        }
        if (!CollectionUtils.isEmpty(param) && PATH_PARAM_ACTION_SETTING_YES.equals(param.get("pathParamSetting"))) {
            //替换action设置的路径数据不为空的数据到agvPathParam中
            AGVPathParam actionPathParam = JSONObject.parseObject(JSONObject.toJSONString(param), AGVPathParam.class);
            BeanUtils.copyPropertiesIgnoreNull(actionPathParam, agvPathParam);
        }
        if (agvPathParam == null) {
            agvPathParam = new AGVPathParam();
            agvPathParam.setAgvId(agvId);
            agvPathParam.setMax_translation_speed(0.8);
            agvPathParam.setMax_rotate_speed(0.8);
            agvPathParam.setTranslation_acc(0.5);
            agvPathParam.setRotate_acc(0.6);
        }
        return agvPathParam;
    }

    //发送移动指令
    private void sendMoveCommand(Vehicle vehicle, PathNavigationParam pathNavigationParam, String missionWorkId) throws IOException, InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        if (pathNavigationParam == null) {
            return;
        }
        LOGGER.debug("agvCode:{},  segments:{}", vehicle.getDeviceNumber(), pathNavigationParam.getSegments());
        pathSimulationService.startPathSimulation(vehicle.getDeviceNumber(), pathNavigationParam.getSegments());
    }
}
