package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedGraph;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date :Created in 上午11:28 2020/12/22
 * @Description :
 * @Modified By :
 * @Version :
 */
public class DFSLoopDetectManager {

    private DFSLoopDetect dfsLoopDetect = new DFSLoopDetect();

    //私有化构造方法
    private DFSLoopDetectManager() {
    }

    private static class DFSLoopDetectManagerHolder {
        //静态初始化器，由JVM来保证线程安全
        private static DFSLoopDetectManager instance = new DFSLoopDetectManager();
    }

    public static DFSLoopDetectManager getInstance() {
        return DFSLoopDetectManager.DFSLoopDetectManagerHolder.instance;
    }

    public void setUndirectedGraph(UndirectedGraph undirectedGraph) {
        dfsLoopDetect.setGraph(undirectedGraph);
    }

    public List<ArrayList<String>> getLoops() {
        if (dfsLoopDetect.hasCyclic()) {
            return dfsLoopDetect.DFSTraverse1();
        } else {
            return null;
        }
    }
}
