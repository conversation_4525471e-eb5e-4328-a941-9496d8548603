package com.youibot.agv.scheduler.engine.pathplan.util;

import com.youibot.agv.scheduler.engine.pathplan.algorithm.MathematicalGraphicsAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;

import static java.lang.Math.*;

/**
 * <AUTHOR>
 * @Date :Created in 下午12:09 19-10-30
 * @Description :
 * @Modified By :
 * @Version :
 */

public class BezierUtil {

    private final static Logger LOGGER = LoggerFactory.getLogger(BezierUtil.class);

    //求点在三阶贝塞尔曲线上的位置
    public static Double calPosition(Point[] p, Point p0, Double maxDerailmentDistance) {
        double t0 = 0;
        double eps = 0.001;
        //1.牛顿迭代法 只能找到迭代起点附近的零解
//		double t1= t0-g(p,t0,p0)/dg(p,t0,p0);

//		while (abs(g(p,t1,p0))>eps)
//		{
//			t0 = t1;
//			t1= t0-g(p,t0,p0)/dg(p,t0,p0);
//			System.out.println(t0+" "+t1);
//		}

        //2.弦截法 只能找到迭代起点附近的零解
//		double t1 = 1;
//		double t2 = t1 - (t1 - t0) * g(p, t1, p0) / (g(p, t1, p0) - g(p, t0, p0));
//		while (abs(g(p, t2, p0)) > eps) {
//			t0 = t1;
//			t1 = t2;
//			t2 = t1 - (t1 - t0) * g(p, t1, p0) / (g(p, t1, p0) - g(p, t0, p0));
//		}

        //3.牛顿区间二分法找到所有解 综合计算量和算法鲁棒性的选择
        double resolution = 0.001;
        double c = 0;
        ArrayList list = new ArrayList();//存放可能解的动态数组
        Boolean fa = null;//fx>0为true,fx<0为false，fx=0为null
        double gt0 = g(p, t0, p0);
        if (gt0 == 0) {
            list.add(t0);
        } else {
            fa = gt0 > 0;
        }
        for (double t = t0; t <= 1; t = t + resolution) {
            Boolean fb = null;
            double gt = g(p, t, p0);
            if (gt == 0) {
                list.add(t);
                fa = fb;
                continue;
            } else {
                fb = gt > 0;
            }
            if (fa == fb)//连续两次同号，则认为该区间内没有零点
            {
                fa = fb;
                continue;
            } else if ((fa != null) && (fb != null)) {//在该区间内出现变号，说明该区间内有零点
                double gc_temp = 1;
                Boolean fa_sub = fa;
                Boolean fb_sub = fb;
                for (double a = t - resolution, b = t; abs(gc_temp) > eps; ) {
                    c = (a + b) / 2;
                    Boolean fc = null;
                    double gc = g(p, c, p0);
                    gc_temp = gc;
                    if (gc == 0) {
                        list.add(c);
                        continue;
                    } else {
                        fc = gc > 0;
                    }
                    if (fc == fa_sub) { //零点在[c,b]区间内
                        fa_sub = fc;
                        a = c;
                    } else if (fc == fb_sub) {//零点在[a,c]区间内
                        b = c;
                        fb_sub = fc;
                    }
                }
                list.add(c);
            }
            fa = fb;
        }
        Double res = null;
        if (CollectionUtils.isEmpty(list)) {
            //Double maxDerailmentDistance = AGVPropertiesUtils.getDouble("PATH_PLAN.MAX_DERAILMENT_DISTANCE");
            if (p0.minus(p[0]).getNorm() < maxDerailmentDistance) {
                res = 0D;
            } else if (p0.minus(p[3]).getNorm() < maxDerailmentDistance) {
                res = 1D;
            }
            if (res == null) {
                LOGGER.debug("未找到点在贝塞尔曲线上的位置");
            }
        } else if (list.size() == 1) {
            res = (double) list.get(0);
        } else {
            double min = f(p, (double) list.get(0), p0);
            res = (double) list.get(0);
            for (int i = 1; i < list.size(); i++) {
                double temp = f(p, (double) list.get(i), p0);
                if (temp < min) {
                    min = temp;
                    res = (double) list.get(i);
                }
            }
        }
        return res;
    }

    //点p0到贝塞尔曲线上t点的距离对t求导，令df(t)=0,此df即为求解非线性方程的函数，记为g(t)
    public static Double g(Point[] p, Double t, Point p0) {
        if (t < 0 || t > 1) {
            LOGGER.debug("g::p = [{}], t = [{}], p0 = [{}]", p, t, p0);
            return null;
        }
        double g_df =
                2 * (p0.getX() + p[0].getX() * pow(t - 1, 3) - p[3].getX() * t * t * t - 3 * p[1].getX() * t * pow(t - 1, 2) + 3 * p[2].getX() * t * t * (t - 1)) * (3 * p[0].getX() * pow(t - 1, 2) - 3 * p[1].getX() * pow(t - 1, 2) + 3 * p[2].getX() * t * t - 3 * p[3].getX() * t * t + 6 * p[2].getX() * t * (t - 1) - 3 * p[1].getX() * t * (2 * t - 2)) + 2 * (p0.getY() + p[0].getY() * pow(t - 1, 3) - p[3].getY() * t * t * t - 3 * p[1].getY() * t * pow(t - 1, 2) + 3 * p[2].getY() * t * t * (t - 1)) * (3 * p[0].getY() * pow(t - 1, 2) - 3 * p[1].getY() * pow(t - 1, 2) + 3 * p[2].getY() * t * t - 3 * p[3].getY() * t * t + 6 * p[2].getY() * t * (t - 1) - 3 * p[1].getY() * t * (2 * t - 2));
        return g_df;
    }

    //g(t)的一阶导函数，非线性方程求解牛顿法需要
    public static Double dg(Point[] p, Double t, Point p0) {
        if (t < 0 || t > 1) {
            LOGGER.debug("dg::p = [{}], t = [{}], p0 = [{}]", p, t, p0);
            return null;
        }
        double dg =
                2 * (p0.getX() + p[0].getX() * pow(t - 1, 3) - p[3].getX() * t * t * t - 3 * p[1].getX() * t * pow(t - 1, 2) + 3 * p[2].getX() * t * t * (t - 1)) * (3 * p[0].getX() * (2 * t - 2) - 6 * p[1].getX() * (2 * t - 2) - 6 * p[1].getX() * t + 12 * p[2].getX() * t - 6 * p[3].getX() * t + 6 * p[2].getX() * (t - 1)) + 2 * (p0.getY() + p[0].getY() * pow(t - 1, 3) - p[3].getY() * t * t * t - 3 * p[1].getY() * t * pow(t - 1, 2) + 3 * p[2].getY() * t * 2 * (t - 1)) * (3 * p[0].getY() * (2 * t - 2) - 6 * p[1].getY() * (2 * t - 2) - 6 * p[1].getY() * t + 12 * p[2].getY() * t - 6 * p[3].getY() * t + 6 * p[2].getY() * (t - 1)) + 2 * pow(3 * p[0].getX() * pow(t - 1, 2) - 3 * p[1].getX() * pow(t - 1, 2) + 3 * p[2].getX() * t * t - 3 * p[3].getX() * t * t + 6 * p[2].getX() * t * (t - 1) - 3 * p[1].getX() * t * (2 * t - 2), 2) + 2 * pow(3 * p[0].getY() * pow(t - 1, 2) - 3 * p[1].getY() * pow(t - 1, 2) + 3 * p[2].getY() * t * t - 3 * p[3].getY() * t * t + 6 * p[2].getY() * t * (t - 1) - 3 * p[1].getY() * t * (2 * t - 2), 2);
        return dg;
    }

    //点到贝塞尔曲线上t点的距离公式，即最小化目标函数
    public static Double f(Point[] p, Double t, Point p0) {
        if (t < 0 || t > 1) {
            LOGGER.debug("f:: t = [{}], p0 = [{}]", t, p0);
            return null;
        }
        Point point = getPoint(p, t);
        return new Point(point.getX() - p0.getX(), point.getY() - p0.getY()).getNorm();
    }

    //点是否在三阶贝塞尔曲线上,若是返回true，若不是返回false
    public static Boolean isPtInPoly(Point[] p, Point p0, Double maxDerailmentDistance) {
        //Double maxDerailmentDistance = AGVPropertiesUtils.getDouble("PATH_PLAN.MAX_DERAILMENT_DISTANCE");
        //分辨率
        int N = 200;
        double resolution = 1.0 / N;
        Point[] points = new Point[2 * (N + 1)];

        //方法一，先用解析求解法向量再用离散求解等距曲线
        //for (int i = 0; i <= N; i++) {
        //	Point point = getPoint(p, i * resolution);
        //	Point unitNormalVector = bezier_unit_normal_vector(p, i * resolution);
        //	if (unitNormalVector == null) {
        //		LOGGER.error("计算贝塞尔曲线导数错误，请检查数据库输入数据是否正确 isPtInPoly error::p = [{}], p0 = [{}]", p, p0);
        //		return false;
        //	}
        //	Point normalVector = new Point(maxDerailmentDistance * unitNormalVector.getX(), maxDerailmentDistance * unitNormalVector.getY());
        //	Double xi = point.getX() + normalVector.getX();
        //	Double yi = point.getY() + normalVector.getY();
        //
        //	Double x_i = point.getX() - normalVector.getX();
        //	Double y_i = point.getY() - normalVector.getY();
        //	points[i] = new Point(xi, yi);
        //	points[2 * N + 1 - i] = new Point(x_i, y_i);
        //}

        //方法二，直接离散求解等距曲线
        Point point1 = new Point(p[0].getX(), p[0].getY());
        for (int i = 1; i <= N; i++) {
            Point point2 = getPoint(p, i * resolution);
            Point tangentVector = new Point(point2.getX() - point1.getX(), point2.getY() - point1.getY());
            if ((tangentVector.getX() == 0) && (tangentVector.getY() == 0)) {
                LOGGER.error("计算发生错误，请检查数据 isPtInPoly::p = [{}], p0 = [{}]", p, p0);
                return false;
            }
            Point unitNormalVector = LineUtil.line_unit_normal_vector(tangentVector);
            Point normalVector = new Point(maxDerailmentDistance * unitNormalVector.getX(), maxDerailmentDistance * unitNormalVector.getY());
            double x0 = point1.getX() + normalVector.getX();
            double y0 = point1.getY() + normalVector.getY();
            double x3 = point1.getX() - normalVector.getX();
            double y3 = point1.getY() - normalVector.getY();
            points[i - 1] = new Point(x0, y0);
            points[2 * N + 2 - i] = new Point(x3, y3);
            if (i == N) {
                double x1 = point2.getX() + normalVector.getX();
                double y1 = point2.getY() + normalVector.getY();
                double x2 = point2.getX() - normalVector.getX();
                double y2 = point2.getY() - normalVector.getY();
                points[i] = new Point(x1, y1);
                points[2 * N + 1 - i] = new Point(x2, y2);
            }
            point1 = point2;
        }

        return MathematicalGraphicsAlgorithm.isPtInPoly(p0.getX(), p0.getY(), points);
    }

    //获取三阶贝塞尔曲线从t0~t1的所有离散点
    public static Point[] getPoints(Point[] p, double t0, double t1, Integer n) {
        if (t0 < 0 || t0 > 1) {
            LOGGER.debug("getPoints:: t0 = [{}], t1 = [{}], n = [{}]", t0, t1, n);
            return null;
        } else if (t1 < 0 || t1 > 1) {
            LOGGER.debug("getPoints:: t0 = [{}], t1 = [{}], n = [{}]", t0, t1, n);
            return null;
        } else if (t0 > t1) {
            LOGGER.debug("getPoints:: t0 = [{}], t1 = [{}], n = [{}]", t0, t1, n);
            return null;
        }
        Point[] points = new Point[n + 1];
        double resolution = 1.0 / n;
        for (int i = 0; i <= n; i++) {
            points[i] = getPoint(p, i * resolution);
        }
        return points;
    }


    //计算贝塞尔曲线t处的坐标值
    public static Point getPoint(Point[] p, Double t) throws ArithmeticException {
        if (t < 0 || t > 1) {
            LOGGER.debug("getPoint:: t = [{}]", t);
            return null;
        }
        Double x = p[0].getX() * pow(1 - t, 3) + 3 * p[1].getX() * pow(1 - t, 2) * t + 3 * p[2].getX() * (1 - t) * pow(t, 2) + p[3].getX() * pow(t, 3);
        Double y = p[0].getY() * pow(1 - t, 3) + 3 * p[1].getY() * pow(1 - t, 2) * t + 3 * p[2].getY() * (1 - t) * pow(t, 2) + p[3].getY() * pow(t, 3);
        return new Point(x, y);
    }


    //计算贝塞尔曲线t0~t1区间长度
    public static Double getLength(Point[] points, Double t0, Double t1) throws ArithmeticException {
        if (t0 < 0 || t0 > 1) {
            LOGGER.debug("getLength:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        } else if (t1 < 0 || t1 > 1) {
            LOGGER.debug("getLength:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        } else if (t0 > t1) {
            LOGGER.debug("getLength:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        }
//		使用普通辛普森方法计算长度
//		return getSimpsonLength1(points, t0, t1);
        //使用变步长辛普森方法计算长度
        return getSimpsonLength2(points, t0, t1);
    }

    //普通辛普森数值积分方法计算贝塞尔曲线0~t区间长度,Simpson代数精度为3 高于3阶函数不能使用Simpson积分法
    public static Double getSimpsonLength1(Point[] points, Double t0, Double t1) throws ArithmeticException {
        if (t0 < 0 || t0 > 1) {
            LOGGER.debug("getSimpsonLength1:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        } else if (t1 < 0 || t1 > 1) {
            LOGGER.debug("getSimpsonLength1:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        } else if (t0 > t1) {
            LOGGER.debug("getSimpsonLength1:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        }
        //将区间分割段数，当场景尺度很大时，适当提高该数值提高计算段长精度
        int TOTAL_SIMPSON_STEP = 500;
        int stepCounts;
        int i = 0;
        Double sum1 = 0.0;
        Double sum2 = 0.0;

        stepCounts = (new Double(TOTAL_SIMPSON_STEP * (t1 - t0))).intValue();
        if (stepCounts == 0) {
            return 0.0;
        }
        if (stepCounts % 2 == 0) {
            stepCounts++;
        }
        double h = (t1 - t0) / stepCounts;

        while (i < stepCounts) {
            sum1 += bezier_speed(points, t0 + (i + 0.5) * h);
            sum2 += bezier_speed(points, t0 + (i + 1) * h);
            i++;
        }
        return ((bezier_speed(points, t0) + bezier_speed(points, t1) + 4 * sum1 + 2 * sum2) * h / 6);
    }

    //变步长辛普森数值积分方法计算贝塞尔曲线t0~t1区间长度,Simpson代数精度为3 高于3阶函数不能使用Simpson积分法
    public static Double getSimpsonLength2(Point[] points, Double t0, Double t1) throws ArithmeticException {
        if (t0 < 0 || t0 > 1) {
            LOGGER.debug("getSimpsonLength2:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        } else if (t1 < 0 || t1 > 1) {
            LOGGER.debug("getSimpsonLength2:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        } else if (t0 > t1) {
            LOGGER.debug("getSimpsonLength2:: t0 = [{}], t1 = [{}]", t0, t1);
            return null;
        }

        double eps = 0.001; //迭代精度
        double fa = bezier_speed(points, t0);
        double fb = bezier_speed(points, t1);
        double h = t1 - t0;
        double T = h / 2.0 * (fa + fb);
        double x;
        double S1 = 0;
        double S2 = 0;
        double T2 = 0;
        double ep = eps + 1.0;
        int n = 1;
        while (ep >= eps) {
            double p = 0.0;
            for (int k = 0; k <= n - 1; k++) {
                x = t0 + (k + 0.5) * h;
                p = p + bezier_speed(points, x);
            }
            T2 = (T + h * p) / 2.0;
            S2 = (4.0 * T2 - T) / 3.0;
            ep = abs(S2 - S1);
            T = T2;
            S1 = S2;
            n = n * 2;
            h = h / 2.0;
        }
        return S2;
    }

    //计算贝塞尔曲线上当前位置t0往前隔const安全距离(3.0m)的t1位置 t1=t0+i*h
    public static Double getSafePt(Point[] points, Double t0, Double agvSafeDistancePurePursuit) throws ArithmeticException {
        if (t0 < 0 || t0 > 1) {
            LOGGER.debug("getSafePt:: t0 = [{}]", t0);
            return null;
        }
        //Double agvSafeDistancePurePursuit = AGVPropertiesUtils.getDouble("PATH_PLAN.AGV_SAFE_DISTANCE_PURE_PURSUIT");
        //if (getSimpsonLength2(points, t0, 1.0) < agvSafeDistancePurePursuit) {
        //	System.out.println("当前路径曲线上机器人距离曲线终点小于安全距离");
        //	return null;
        //}
        //设定精度
        double eps = 0.001;
        double sum = 0.0;
        double h = eps;
        int i = 0;
        while (sum < agvSafeDistancePurePursuit) {
            sum += bezier_speed(points, t0 + i * h) * h;
            i++;
            if (t0 + i * h > 1.0) {
                LOGGER.info("当前路径曲线上机器人距离曲线终点小于安全距离");
                return null;
            }
        }
        return t0 + i * h;
    }


    //三阶贝塞尔曲线的一阶导函数 dx/dt
    public static Double bezier_speed_x(Point[] p, double t) throws ArithmeticException {
        if (t < 0 || t > 1) {
            LOGGER.debug("bezier_speed_x:: t = [{}]", t);
            return null;
        }
        return -3 * p[0].getX() * pow(1 - t, 2) + 3 * p[1].getX() * (t - 1) * (3 * t - 1) + 3 * p[2].getX() * t * (-3 * t + 2) + 3 * p[3].getX() * pow(t, 2);
    }

    //三阶贝塞尔曲线的一阶导函数 dy/dt
    public static Double bezier_speed_y(Point[] p, double t) throws ArithmeticException {
        if (t < 0 || t > 1) {
            LOGGER.debug("bezier_speed_y:: t = [{}]", t);
            return null;
        }
        return -3 * p[0].getY() * pow(1 - t, 2) + 3 * p[1].getY() * (t - 1) * (3 * t - 1) + 3 * p[2].getY() * t * (-3 * t + 2) + 3 * p[3].getY() * pow(t, 2);
    }

    //三阶贝塞尔曲线的合速度 ds/dt ds=sqrt(dx*dx+dy*dy)
    public static Double bezier_speed(Point[] p, double t) throws ArithmeticException {
        if (t < 0 || t > 1) {
            LOGGER.debug("bezier_speed:: t = [{}]", t);
            return null;
        }
        double vx = bezier_speed_x(p, t);
        double vy = bezier_speed_y(p, t);
        return sqrt(pow(vx, 2) + pow(vy, 2));
    }

    //三阶贝塞尔曲线的单位切向量
    public static Point bezier_unit_tangent_vector(Point[] p, double t) throws ArithmeticException {
        if (t < 0 || t > 1) {
            LOGGER.debug("bezier_unit_tangent_vector:: t = [{}]", t);
            return null;
        }
        double vx = bezier_speed_x(p, t);
        double vy = bezier_speed_y(p, t);

        double v = sqrt(pow(vx, 2) + pow(vy, 2));
        if (v == 0) {
            return new Point(0D, 0D);
        } else {
            return new Point(vx / v, vy / v);
        }

    }

    //三阶贝塞尔曲线的单位法向量
    public static Point bezier_unit_normal_vector(Point[] p, double t) throws ArithmeticException {
        if (t < 0 || t > 1) {
            LOGGER.debug("bezier_unit_normal_vector:: t = [{}]", t);
            return null;
        }
        Point point = bezier_unit_tangent_vector(p, t);
        return new Point(-point.getY(), point.getX());
    }

    //三阶贝塞尔曲线的入口方向角和出口方向角
    public static Double[] getInOutAngle(Point[] p) throws ArithmeticException {
        if (p == null || p.length != 4) {
            LOGGER.error("getInAngle:: p = [{}]", p.toString());
            return null;
        }
        Double[] inOutAngle = new Double[2];
        inOutAngle[0] = bezier_unit_tangent_vector(p, 0D).getAngle();
        inOutAngle[1] = bezier_unit_tangent_vector(p, 1D).getAngle();
        return inOutAngle;
    }
}
