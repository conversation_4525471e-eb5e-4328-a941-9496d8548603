package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.youibot.agv.scheduler.entity.SidePath;
import lombok.Data;

import java.util.LinkedList;

/**
 * <AUTHOR>
 * @Date :Created in 下午9:08 19-11-8
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class SidePathPlanResult {
    String agvCode;
    LinkedList<SidePath> sidePaths;
    Double cost;

    public SidePathPlanResult(String agvCode) {
        this.agvCode = agvCode;
        sidePaths = new LinkedList<>();
        cost = Double.MAX_VALUE;
    }
}
