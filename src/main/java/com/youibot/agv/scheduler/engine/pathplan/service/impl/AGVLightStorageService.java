package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.youibot.agv.scheduler.engine.constant.AGVLightConstant;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 记录agv红路灯的存储类
 *
 * <AUTHOR>
 * @version CreateTime:2019/12/12 17:13
 */
@Component
public class AGVLightStorageService {
    //存储agv的灯
    private Map<String, Integer> agvLights = new ConcurrentHashMap<>();

    public void add(String vehicleId) {
        agvLights.put(vehicleId, AGVLightConstant.RED_LIGHT);
    }

    public Map<String, Integer> getAgvLightsMap() {
        return agvLights;
    }


    public void setAgvLight(String vehicleId, Integer light) {
        if (agvLights.containsKey(vehicleId)) {
            agvLights.remove(vehicleId);
            agvLights.put(vehicleId, light);
        }
    }

    public void clean() {
        agvLights.clear();
    }
}
