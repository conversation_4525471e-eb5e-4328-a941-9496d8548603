package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil;
import com.youibot.agv.scheduler.exception.ExecuteException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil.getDistance;

/**
 * <AUTHOR>
 * @Date :Created in 19:05 2021/9/26
 * @Description :
 * @Modified By :
 * @Version :
 */
public class TSPTMS4GeneticAlgorithm {
    private static final Logger logger = LoggerFactory.getLogger(TSPTMS4GeneticAlgorithm.class);

    private Map<String, Map<String, Double>> distanceMatrix;                  //距离矩阵

    private List<String> loadMaterialList;
    private String unloadMaterial;

    public void GAInit(List<String> loadMaterialList, String unloadMaterial) {
        if (CollectionUtils.isEmpty(loadMaterialList) || StringUtils.isEmpty(unloadMaterial)) {
            logger.error("TSPTMS4GeneticAlgorithm.GAInit 错误的参数");
            throw new ExecuteException("错误的参数");
        }

        this.loadMaterialList = loadMaterialList;
        this.unloadMaterial = unloadMaterial;

        distanceMatrix = DistanceMatrixUtil.getDistanceMatrix();
    }

    public String run() {
        Double minLength = Double.POSITIVE_INFINITY;
        String bestLoadMaterialIndex = null;

        int loadSize = loadMaterialList.size();
        String unloadMaterialIndex = unloadMaterial;
        for (int i = 0; i < loadSize; i++) {
            String loadMaterialIndex = loadMaterialList.get(i);
            Double distance = getDistance(distanceMatrix, loadMaterialIndex, unloadMaterialIndex);
            if (distance < minLength) {
                minLength = distance;
                bestLoadMaterialIndex = loadMaterialIndex;
            }
        }
        if (minLength >= Double.POSITIVE_INFINITY) {
            logger.error("计算错误，请检查参数是否合规");
            throw new ExecuteException("计算错误，请检查参数是否合规");
        }
        return bestLoadMaterialIndex;
    }
}
