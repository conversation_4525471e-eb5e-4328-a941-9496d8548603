package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;

/**
 * if else 表达式action
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019年5月20日 下午4:43:10
 */
@Service
@Scope("prototype")
public class IfElseAction extends LogicAction {

    private Logger logger = LoggerFactory.getLogger(IfElseAction.class);

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionActionService missionActionService;

    @Override
    public Map<String, Object> execute(Vehicle vehicle) throws InterruptedException, IOException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        this.vehicle = vehicle;
        JSONObject paramJson = super.getParamJson();
        boolean compareResult;
        String resultData = missionWorkAction.getResultData();
        //如果是if action, 在resultData字段会存储上一次的比较结果trueActionList或falseActionList，之后恢复时都是按照上一次的比较结果执行对应的子action
        if (!StringUtils.isEmpty(resultData)) {
            if (resultData.equals(CHILD_TYPE_IF)) {//if 表达为true
                compareResult = true;
            } else if (resultData.equals(CHILD_TYPE_ELSE)) {
                compareResult = false;
            } else {
                logger.error("resultData error, missionWorkActionId: " + missionWorkAction.getId());
                throw new ActionException(MessageUtils.getMessage("action.internal_storage_data_error"));
            }
        } else {
            String compareAttribute = paramJson.getString("compare");
            String operator = paramJson.getString("operator");
            Double value = paramJson.getDouble("value");//value 为本身这个ifAction的value参数
            if (StringUtils.isEmpty(compareAttribute) || StringUtils.isEmpty(operator) || value == null) {
                logger.error("compare or operator or value is null, missionWorkActionId: " + missionWorkAction.getId());
                throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
            }
            if (MISSION_ACTION_ATTRIBUTE_BATTERY_VALUE.equals(compareAttribute) || MISSION_ACTION_ATTRIBUTE_FREE_TIME.equals(compareAttribute)
            		|| MISSION_ACTION_ATTRIBUTE_QR_ANGLE.equals(compareAttribute)
            		) {//属性为电量或空闲时间 或二维码角度
                Double attributeValue = this.getAttributeValue(compareAttribute);//获取需要比较的属性值
                compareResult = super.compare(attributeValue, operator, value);//进行比较, if条件成功返回true, 否则false
            } else {//如果属性类型非电量和空闲时间(使用全局变量value)
                compareResult = super.compare(Double.valueOf(compareAttribute), operator, value);//进行比较, if条件成功返回true, 否则false
            }
        }

        List<MissionAction> subMissionActions;//子missionActionId，多个以逗号隔开
        if (compareResult) {//if 表达为true
            subMissionActions = missionActionService.selectByParentActonIdAndChildType(missionWorkAction.getMissionActionId(), CHILD_TYPE_IF);
            missionWorkAction.setResultData(CHILD_TYPE_IF);
        } else {
            subMissionActions = missionActionService.selectByParentActonIdAndChildType(missionWorkAction.getMissionActionId(), CHILD_TYPE_ELSE);
            missionWorkAction.setResultData(CHILD_TYPE_ELSE);
        }
        missionWorkActionService.update(missionWorkAction);
        super.executeSubMissionAction(subMissionActions);//执行子action
        return null;
    }
}
