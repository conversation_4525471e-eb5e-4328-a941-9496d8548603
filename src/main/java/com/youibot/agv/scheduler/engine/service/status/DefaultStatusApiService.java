package com.youibot.agv.scheduler.engine.service.status;

import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 18:21
 */
@Service
public class DefaultStatusApiService extends DefaultApiService implements StatusApiService {

    @Value("${AGV_SOCKET.PORT.STATUS}")
    private Integer agvStatusPort;

    @Value("${AGV_API_CODE.QUERY_AGV_ALL_STATUS}")
    private String agvStatusCode;

    @Override
    public Map<String, Object> getStatus(String ip) throws AGVResultException, IOException, InterruptedException {
        AGVSocketClient agvSocketClient = super.createClient(ip, agvStatusPort);
        agvSocketClient.create();
        Map<String, Object> resultMap = this.getStatus(agvSocketClient);
        agvSocketClient.stop();
        return resultMap;
    }

    @Override
    public Map<String, Object> getStatus(AGVSocketClient client) throws AGVResultException, IOException, InterruptedException {
        return super.execute(client, agvStatusCode, null);
    }

}
