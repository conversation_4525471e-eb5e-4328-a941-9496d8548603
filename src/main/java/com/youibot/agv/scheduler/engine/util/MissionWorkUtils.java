package com.youibot.agv.scheduler.engine.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.constant.TaskConstant;
import com.youibot.agv.scheduler.engine.entity.ActionStatus;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.ActionUtils;
import com.youibot.agv.scheduler.util.ApiCodeUtils;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_ACTION_STATUS_RUNNING;
import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/3 12:09
 */
public class MissionWorkUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkUtils.class);

    public static void pauseCommand(String ip, String id) throws IOException, InterruptedException {
        Vehicle vehicle = VehicleUtils.getVehicle();
        MissionWorkService missionWorkService = (MissionWorkService) ApplicationUtils.getBean("missionWorkServiceImpl");
        MissionWork missionWork = vehicle.getMissionWork() != null ? vehicle.getMissionWork() : missionWorkService.selectExecutionIncomplete();
        if (isRobotScriptAction(missionWork)) {
            robotScriptPause(vehicle);
            return;
        }

        ActionStatus actionStatus = ActionUtils.getStatusById(ip, id);
        if (actionStatus == null) {
            LOGGER.warn("动作暂停, 但查询不到该动作状态, id:{}", id);
            return;
        }
        Integer status = actionStatus.getStatus();//执行状态
        if (!ACTION_EXECUTE_RUNNING.equals(status) && !ACTION_EXECUTE_PAUSE.equals(status)) {//AGV非执行动作状态，不发暂停指令
            LOGGER.warn("the action of agv is not running or pause");
            return;
        }
        ActionUtils.pauseAction(ip, actionStatus.getCode(), id);
    }


    public static void resumeCommand(String ip, String id) throws IOException {
        ActionStatus actionStatus = ActionUtils.getStatusById(ip, id);
        if (actionStatus == null) {
            LOGGER.warn("动作恢复, 但查询不到该动作状态, id:{}", id);
            return;
        }
        Integer status = actionStatus.getStatus();//执行状态
        if (!ACTION_EXECUTE_PAUSE.equals(status)) {//AGV非执行动作状态，不发暂停指令
            LOGGER.warn("the action of agv is not pause");
            return;
        }
        ActionUtils.sendInstruction(ip, AGVPropertiesUtils.getString("AGV_API_CODE.ACTION_RESUME"), null);
    }

    public static void stopCommand(Vehicle vehicle, String id) throws IOException, InterruptedException {
        LOGGER.warn("动作停止, id:{}", id);
        // 查询是否是机械臂脚本执行
        MissionWorkService missionWorkService = ApplicationUtils.getBean(MissionWorkService.class);
        MissionWork missionWork = vehicle.getMissionWork() != null ? vehicle.getMissionWork() : missionWorkService.selectExecutionIncomplete();
        if (isRobotScriptAction(missionWork)) {
            robotScriptStop(vehicle);
            return;
        }

        //如果是更新地图任务先停止地图更新在停止任务
        DefaultVehicleStatus defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
        if (defaultVehicleStatus != null && defaultVehicleStatus.getMap() != null) {
            if (!StringUtils.isEmpty(defaultVehicleStatus.getMap().getScan_update_status()) && !RECORDING_MAP_STATUS_SUCCESS.equals(defaultVehicleStatus.getMap().getScan_update_status())) {
                stopUpdateMap(vehicle.getIp(), id);
            }
        }
        //查询任务状态
        if (StringUtils.isEmpty(id)) {
            LOGGER.warn("动作停止, 但id为空");
            return;
        }
        List<String> actionIds = Lists.newArrayList( id);
        if (Objects.equals(id, TaskConstant.SMART_WAIT_ACTION_ID)) {
            actionIds.add(TaskConstant.SMART_WAIT_MOVE_TO_MARKER);
        }
        if (Objects.equals(id, TaskConstant.SMART_CHARGE_ACTION_ID)) {
            actionIds.add(TaskConstant.SMART_CHARGE_MOVE_TO_MARKER);
        }
        List<ActionStatus> actionStatusList = ActionUtils.getStatusByIds(vehicle.getIp(), actionIds);
        if (actionStatusList == null || actionStatusList.size() == 0) {
            LOGGER.warn("动作停止, 但查询不到该动作状态, id:{}", id);
            return;
        }
        LOGGER.warn("query_data:{}", actionStatusList);
        Optional<ActionStatus> first = actionStatusList.stream()
                // 可以停止的状态
                .filter(actionStatus -> ACTION_EXECUTE_PAUSE.equals(actionStatus.getStatus()) || ACTION_EXECUTE_RUNNING.equals(actionStatus.getStatus()))
                // 可以停止的接口
                .filter(actionStatus -> ApiCodeUtils.getCanStopActionCode().contains(actionStatus.getCode())).findFirst();

        if (first.isPresent()) {
            LOGGER.info("发送停止命令");
            ActionUtils.stopAction(vehicle.getIp(), first.get().getCode(), first.get().getId());
        }
/*        Integer docking_status = vehicle.getDefaultVehicleStatus().getRuntime().getDocking_status();
        if (docking_status != null) {
            if (vehicle.getDefaultVehicleStatus().getRuntime().getDocking_status() == 3) {
                LOGGER.debug("ip : " + vehicle.getIp() + " send leave docking command.");
                ActionUtils.sendInstruction(vehicle.getIp(), AGVPropertiesUtils.getString("AGV_API_CODE.LEAVE_DOCKING"), null);
                ActionUtils.checkActionStatus(vehicle.getIp(), id);
            }
        }*/
    }

    private static void stopUpdateMap(String ip, String missionWorkActionId) throws IOException {
        AGVSocketClient client = AGVSocketClient.createAGVClient(ip, AGVPropertiesUtils.getInteger("AGV_SOCKET.PORT.MAP"));
        JSONObject paramJson = new JSONObject();
        paramJson.put("id", missionWorkActionId);
        paramJson.put("is_save", 2);
        ActionUtils.sendInstruction(client, AGVPropertiesUtils.getString("AGV_API_CODE.END_UPDATE_MAP"), JSONObject.toJSONString(paramJson));
    }

    private static Boolean isRobotScriptAction(MissionWork missionWork) {
        if (missionWork != null) {
            MissionWorkActionService missionWorkActionService = (MissionWorkActionService) ApplicationUtils.getBean("missionWorkActionServiceImpl");
            MissionWorkAction missionWorkAction = missionWorkActionService.selectByMissionWorkIdAndStatus(missionWork.getId(), MISSION_WORK_ACTION_STATUS_RUNNING);
            if (missionWorkAction != null) {
                //机械臂脚本执行停止需要重新实现
                if ("SCRIPT_MANIPULATOR_ARM".equals(missionWorkAction.getActionType())) {
                    return true;
                }
            }
        }
        return false;
    }

    private static void robotScriptStop(Vehicle vehicle) throws IOException, InterruptedException {
        String mosIp = ActionUtils.getScriptActionIp();
        AGVSocketClient client = AGVSocketClient.createAGVClient(mosIp, ActionUtils.getScriptActionPort());
        Map<String, Object> dataMap = ActionUtils.sendInstruction(client, AGVPropertiesUtils.getString("AGV_API_CODE.ROBOT_SCRIPT_STATUS"), null);
        LOGGER.debug("call agv query action status, ip :" + mosIp + ", resultData:" + dataMap);
        if (dataMap == null || dataMap.isEmpty()) {
            return;//AGV刚开机没有执行过任务的情况下data为空
        }
        Integer status = (Integer) dataMap.get("status");//执行状态
        if (!ACTION_EXECUTE_PAUSE.equals(status) && !ACTION_EXECUTE_RUNNING.equals(status)) {//AGV动作非执行或暂停状态，不发停止指令
            LOGGER.warn("the action of agv is not pause or running");
            return;
        }
        AGVSocketClient agvSocketClient;
        try {
            String apiCode = AGVPropertiesUtils.getString("AGV_API_CODE.ROBOT_SCRIPT_STOP");
            agvSocketClient = AGVSocketClient.createAGVClient(mosIp, ActionUtils.getScriptActionPort());
            if (ApiCodeUtils.getCanStopActionCode().contains(apiCode)) {//如果该action可停止
                ActionUtils.sendInstruction(agvSocketClient, apiCode, null);
            }
            while (true) {
                Thread.sleep(200);
                if (Thread.interrupted()) {//如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }
                dataMap = ActionUtils.sendInstruction(agvSocketClient, AGVPropertiesUtils.getString("AGV_API_CODE.ROBOT_SCRIPT_STATUS"), null);
                LOGGER.debug("call agv query action status, ip :" + mosIp + ", resultData:" + dataMap);
                if (dataMap == null || dataMap.isEmpty()) {
                    continue;//AGV刚开机没有执行过任务的情况下data为空
                }
                status = (Integer) dataMap.get("status");//执行状态
                if (ACTION_EXECUTE_SUCCESS.equals(status)) {//执行成功
                    LOGGER.debug("stop robot script action of agv is success");
                    return;
                }
                if (ACTION_EXECUTE_FAULT.equals(status)) {
                    LOGGER.error("stop robot script action of agv is fault");
                    throw new ExecuteException(ErrorEnum.ROBOT_SCRIPT_ACTION_ERROR.code(), ErrorEnum.ROBOT_SCRIPT_ACTION_ERROR.msg());
                }
            }
        } catch (Exception e) {
            LOGGER.error("query action status error, ", e);
            throw e;
        }
    }

    private static void robotScriptPause(Vehicle vehicle) throws IOException, InterruptedException {
        String mosIp = ActionUtils.getScriptActionIp();
        AGVSocketClient client = AGVSocketClient.createAGVClient(mosIp, ActionUtils.getScriptActionPort());
        Map<String, Object> dataMap = ActionUtils.sendInstruction(client, AGVPropertiesUtils.getString("AGV_API_CODE.ROBOT_SCRIPT_STATUS"), null);
        LOGGER.debug("call agv query action status, ip :" + mosIp + ", resultData:" + dataMap);
        if (dataMap == null || dataMap.isEmpty()) {
            return;//AGV刚开机没有执行过任务的情况下data为空
        }
        while (true) {
            Thread.sleep(200);
            if (Thread.interrupted()) {//如果当前线程被中断了
                throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
            }
            dataMap = ActionUtils.sendInstruction(client, AGVPropertiesUtils.getString("AGV_API_CODE.ROBOT_SCRIPT_STATUS"), null);
            LOGGER.debug("call agv query action status, ip :" + mosIp + ", resultData:" + dataMap);
            if (dataMap == null || dataMap.isEmpty()) {
                continue;//AGV刚开机没有执行过任务的情况下data为空
            }
            Integer status = (Integer) dataMap.get("status");//执行状态
            if (!ACTION_EXECUTE_PAUSE.equals(status) && !ACTION_EXECUTE_RUNNING.equals(status)) {//AGV动作非执行或暂停状态，不发停止指令
                LOGGER.warn("the action of agv is not pause or running");
                return;
            }
            if (ACTION_EXECUTE_SUCCESS.equals(status)) {//执行成功
                LOGGER.debug("the action of agv is success");
                return;
            }
            if (ACTION_EXECUTE_FAULT.equals(status)) {
                LOGGER.error("the action of agv is fault");
                throw new ExecuteException(MessageUtils.getMessage("action.script_result_error"));
            }

        }
    }

}
