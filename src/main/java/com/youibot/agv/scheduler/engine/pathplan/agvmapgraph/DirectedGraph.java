package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.util.AGVUtil;
import com.youibot.agv.scheduler.engine.pathplan.util.CloneUtils;
import com.youibot.agv.scheduler.engine.pathplan.util.SidePathUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.util.MessageUtils;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date :Created in 12:00 2020/8/7
 * @Description : 加权有向图
 * @Modified By :
 * @Version :
 */
public final class DirectedGraph implements Cloneable {
    private static final Logger logger = LoggerFactory.getLogger(DirectedGraph.class);

    private volatile ConcurrentHashMap<String, DirectedNode> nodeMap = new ConcurrentHashMap<>();

    private volatile ConcurrentHashMap<String, DirectedEdge> nodeUVToEdge = new ConcurrentHashMap<>();

    private volatile ConcurrentHashMap<String, Set<String>> successorNodes = new ConcurrentHashMap<>();

    private volatile ConcurrentHashMap<String, Set<String>> predecessorNodes = new ConcurrentHashMap<>();

    private volatile ConcurrentHashMap<String, Set<String>> adjacentNodes = new ConcurrentHashMap<>();

    public DirectedGraph() {
        init();
    }

    public synchronized void init() {
        nodeMap = new ConcurrentHashMap<>();
        nodeUVToEdge = new ConcurrentHashMap<>();
        successorNodes = new ConcurrentHashMap<>();
        predecessorNodes = new ConcurrentHashMap<>();
        adjacentNodes = new ConcurrentHashMap<>();
    }

    public int size() {
        return nodeMap.size();
    }

    public ConcurrentHashMap<String, DirectedNode> getNodeMap() {
        return nodeMap;
    }

    public synchronized void addNode(Marker marker) {
        String id = marker.getId();
        DirectedNode directedNode = new DirectedNode(id, marker.getCode(), marker.getX(), marker.getY());
        if (!nodeMap.containsKey(id)) {
            nodeMap.put(id, directedNode);
            successorNodes.computeIfAbsent(id, k -> new HashSet<>());
            predecessorNodes.computeIfAbsent(id, k -> new HashSet<>());
            adjacentNodes.computeIfAbsent(id, k -> new HashSet<>());
        }
    }

    public synchronized void removeNode(Marker marker) {
        String id = marker.getId();
        nodeMap.remove(id);
        successorNodes.remove(id);
        predecessorNodes.remove(id);
        adjacentNodes.remove(id);
    }

    public synchronized void addNodes(List<Marker> markers) {
        markers.forEach(this::addNode);
    }

    /**
     * 边的开始站点，边的结束站点，边上移动速度
     *
     * @param sidePath
     * @param startMarker
     * @param endMarker
     * @param v
     * @param w
     */
    public synchronized void addEdge(SidePath sidePath, Marker startMarker, Marker endMarker, Double v, Double w, List<String> jamAgvTypes) {
        if (sidePath == null || startMarker == null || endMarker == null || v == null || w == null) return;
        addNode(startMarker);
        addNode(endMarker);
        successorNodes.computeIfAbsent(startMarker.getId(), k -> new HashSet<>());
        successorNodes.get(startMarker.getId()).add(endMarker.getId());
        predecessorNodes.computeIfAbsent(endMarker.getId(), k -> new HashSet<>());
        predecessorNodes.get(endMarker.getId()).add(startMarker.getId());
        adjacentNodes.computeIfAbsent(startMarker.getId(), k -> new HashSet<>());
        adjacentNodes.computeIfAbsent(endMarker.getId(), k -> new HashSet<>());
        adjacentNodes.get(startMarker.getId()).add(sidePath.getEndMarkerId());
        adjacentNodes.get(endMarker.getId()).add(sidePath.getStartMarkerId());
        DirectedEdge directedEdge = new DirectedEdge(sidePath, startMarker.getCode(), endMarker.getCode(), v, w, jamAgvTypes);
        nodeUVToEdge.put(startMarker.getId() + endMarker.getId(), directedEdge);
    }

    private synchronized void removeEdge(String nodeU, String nodeV) {
        nodeUVToEdge.remove(nodeU + nodeV);
        successorNodes.getOrDefault(nodeU, new HashSet<>()).remove(nodeV);
        predecessorNodes.getOrDefault(nodeV, new HashSet<>()).remove(nodeU);
        adjacentNodes.getOrDefault(nodeU, new HashSet<>()).remove(nodeV);
        adjacentNodes.getOrDefault(nodeV, new HashSet<>()).remove(nodeU);
    }

    public synchronized void removeEdge(SidePath sidePath) {
        removeEdge(sidePath.getStartMarkerId(), sidePath.getEndMarkerId());
    }

    public synchronized void removeEdges(Collection<SidePath> sidePaths) {
        sidePaths.forEach(this::removeEdge);
    }

    public synchronized void removeEdges(Set<DirectedEdge> directedEdges) {
        directedEdges.forEach(edge ->
                removeEdge(edge.getA(), edge.getB())
        );
    }

    //get adjacent Nodes
    public Set<DirectedNode> getAdjacentNodes(DirectedNode directedNode) {
        if (nodeMap.containsKey(directedNode.getId())) {
            Set<String> set = adjacentNodes.get(directedNode.getId());
            if (set == null) {
                return new HashSet<>();
            } else {
                return set.stream().map(nodeMap::get).collect(Collectors.toSet());
            }
        }
        return new HashSet<>();
    }

    //get adjacent markerIds
    public Set<String> getAdjacentMarkerIds(String nodeId) {
        if (nodeMap.containsKey(nodeId)) {
            Set<String> set = adjacentNodes.get(nodeId);
            if (set == null) {
                return new HashSet<>();
            } else {
                return new HashSet<>(set);
            }

        }
        return new HashSet<>();
    }

    //获得传入节点的后继节点
    public Set<DirectedNode> getSuccessors(String nodeId) {
        if (nodeMap.containsKey(nodeId)) {
            Set<String> set = successorNodes.get(nodeId);
            if (set == null) {
                return new HashSet<>();
            } else {
                return set.stream().map(nodeMap::get).collect(Collectors.toSet());
            }
        }
        return new HashSet<>();
    }

    //获得传入节点的后继节点
    public Set<DirectedNode> getSuccessors(DirectedNode directedNode) {
        return getSuccessors(directedNode.getId());
    }

    //获得传入节点的前代节点
    public Set<DirectedNode> getPredecessors(String nodeId) {
        if (nodeMap.containsKey(nodeId)) {
            Set<String> set = predecessorNodes.get(nodeId);
            if (set == null) {
                return new HashSet<>();
            } else {
                return set.stream().map(nodeMap::get).collect(Collectors.toSet());
            }
        }
        return new HashSet<>();
    }

    //获得传入节点的前代节点
    public Set<DirectedNode> getPredecessors(DirectedNode directedNode) {
        return getPredecessors(directedNode.getId());
    }

    //获得所有的路径边
    public Set<DirectedEdge> getAllEdges() {
        return new HashSet<>(nodeUVToEdge.values());
    }

    public Set<DirectedEdge> getInEdges(String nodeV) {
        if (nodeMap.containsKey(nodeV)) {
            Set<String> set = predecessorNodes.get(nodeV);
            return set.stream().map(nodeU -> nodeUVToEdge.get(nodeU + nodeV)).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    public Set<DirectedEdge> getOutEdges(String nodeU) {
        if (nodeMap.containsKey(nodeU)) {
            Set<String> set = successorNodes.get(nodeU);
            return set.stream().map(nodeV -> nodeUVToEdge.get(nodeU + nodeV)).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    public Set<DirectedEdge> getInOutEdges(String nodeId) {
        if (nodeMap.containsKey(nodeId)) {
            Set<DirectedEdge> inEdges = getInEdges(nodeId);
            Set<DirectedEdge> outEdges = getOutEdges(nodeId);
            Set<DirectedEdge> edges = new HashSet<>();
            edges.addAll(inEdges);
            edges.addAll(outEdges);
            return edges;
        }
        return new HashSet<>();
    }

    public DirectedNode getNodeById(String nodeId) {
        if (nodeMap == null || nodeMap.size() <= 0) {
            logger.error("directedGraph.nodeMap is null.");
        }
        if (StringUtil.isNotBlank(nodeId)) {
            return nodeMap.get(nodeId);
        }
        return null;
    }

    public DirectedEdge getEdgeByStartEndNode(String n1, String n2) {
        if (n1 == null || n2 == null) return null;
        return nodeUVToEdge.get(n1 + n2);
    }

    public DirectedEdge getEdgeByStartEndNode(DirectedNode n1, DirectedNode n2) {
        if (n1 == null || n2 == null) return null;
        return getEdgeByStartEndNode(n1.getId(), n2.getId());
    }

    public DirectedEdge getEdgeBySidePath(SidePath sidePath) {
        if (sidePath == null) return null;
        return getEdgeByStartEndNode(sidePath.getStartMarkerId(), sidePath.getEndMarkerId());
    }

    public DirectedEdge getReverseEdgeBySidePath(SidePath sidePath) {
        if (sidePath == null) return null;
        return getEdgeByStartEndNode(sidePath.getEndMarkerId(), sidePath.getStartMarkerId());
    }

    //正向搜索代价计算，区别在转动代价计算
    public Double getForwardCost(DirectedNode n1,
                                 DirectedNode n1Parent,
                                 DirectedNode n2,
                                 DirectedNode n2Parent,
                                 Map<DirectedEdge, DirectedEdgeProperty> edgePropertyMap,
                                 String agvCode,
                                 boolean dynamic) {
        DirectedEdge edge = getEdgeByStartEndNode(n1, n2);
        if (edge == null) {
            logger.error("getForwardCost:[{}]->[{}], edge is null", n1.getCode(), n2.getCode());
            return Double.POSITIVE_INFINITY;
        }
        double angleWeight = 0;
        if (n1Parent != null) {
            DirectedEdge lastEdge = getEdgeByStartEndNode(n1Parent, n1);
            double angle = Math.abs(limitRad(edge.getInAngle() - lastEdge.getOutAngle()));
            if (angle > 1.15f / 180f * Math.PI) {
                angleWeight = angle * 2 / Math.abs(lastEdge.getW());
            } else {
                angleWeight = 0d;
            }
        }
        Double weight = null;
        if (dynamic) {
            DirectedEdgeProperty edgeProperty = edgePropertyMap.get(edge);
            if (edgeProperty != null) {
                weight = edgeProperty.getWeight();
            } else {
                logger.error("error of get edgeProperty");
                weight = Double.POSITIVE_INFINITY;
            }
        } else {
            weight = edge.getOriginWeight();
        }
        return Math.abs(weight + angleWeight);
    }

    //反向搜索代价计算，区别在转动代价计算 /dynamic:true 动态权重, false,原始权重
    public Double getBackwardCost(DirectedNode n1,
                                  DirectedNode n1Child,
                                  DirectedNode n2,
                                  DirectedNode n2Child,
                                  Map<DirectedEdge, DirectedEdgeProperty> edgePropertyMap,
                                  String agvCode,
                                  boolean dynamic) {
        DirectedEdge edge = getEdgeByStartEndNode(n1, n2);
        if (edge == null) {
            logger.error("getBackwardCost:[{}]->[{}], edge is null", n1.getCode(), n2.getCode());
            return Double.POSITIVE_INFINITY;
        }
        if(StringUtils.isNotBlank(agvCode)) {
            String agvType = AGVUtil.getAGVTypeByAGVCode(agvCode);
            if (!StringUtils.isEmpty(agvType)) {
                //查找到AGV类型，判断该类型的车是否在这条路径上被禁用。
                if (edge.containedJamAgvType(agvType)) {
                    //被包含在禁用类型里，不可通行。
                    logger.debug("the agv type is limit." + agvType);
                    return Double.POSITIVE_INFINITY;
                }
                //没有被包含在禁用类型里，可通行。
            }
        }
    
   
        //无法查找到AGV类型，则放行

        double angleWeight = 0;
        if (n2Child != null) {
            DirectedEdge nextEdge = getEdgeByStartEndNode(n2, n2Child);
            double angle = Math.abs(limitRad(nextEdge.getInAngle() - edge.getOutAngle()));
            if (angle > 1.15f / 180f * Math.PI) {
                angleWeight = angle * 2 / Math.abs(edge.getW());
            }
            if (angleWeight < 0) {
                logger.error("getBackwardCost: {}", edge.toString());
                logger.error("getBackwardCost: {}", nextEdge.toString());
                throw new PathPlanException(MessageUtils.getMessage("path_plan.angle_weight_negative"));
            }
        } else {
            angleWeight = 0;
        }
        Double weight = null;
        if (dynamic) {
            DirectedEdgeProperty edgeProperty = edgePropertyMap.get(edge);
            if (edgeProperty != null) {
                weight = edgeProperty.getWeight();
            } else {
                logger.error("error of get edgeProperty");
                weight = Double.POSITIVE_INFINITY;
            }
            if (weight == Double.POSITIVE_INFINITY) {
                logger.debug("the edgeProperty is POSITIVE_INFINITY." + edgeProperty.getWeightAuto() + "," + edgeProperty.getWeightFixed() + "," + edgeProperty.getWeightUser());
            }
        } else {
            weight = edge.getOriginWeight();
        }
        if (weight < 0) {
            logger.error("getBackwardCost: {}", edge.toString());
            throw new PathPlanException(MessageUtils.getMessage("path_plan.auto_weight_negative"));
        }
        if (weight + angleWeight == Double.POSITIVE_INFINITY) {
            logger.debug("the weight " + weight + "," + angleWeight);
        }
        return weight + angleWeight;
    }

    //获得n1，n2连线的代价 + n1，n2，n3连线的夹角代价
    public synchronized Double getCost(String n1, String n2, String n3) {
        DirectedEdge edge = getEdgeByStartEndNode(n1, n2);
        DirectedEdge nextEdge = getEdgeByStartEndNode(n2, n3);
        if (edge == null) {
            logger.error("edge is null :[{}]->[{}].", n1, n2);
            return 0d;
        }
        if (nextEdge == null) {
            logger.error("next edge is null :[{}]->[{}].", n2, n3);
            return edge.getOriginWeight();
        }
        double angle = Math.abs(limitRad(nextEdge.getInAngle() - edge.getOutAngle()));
        double angleWeight = 0;
        if (angle > 1.15f / 180f * Math.PI) {
            //TODO:暂时粗略估算旋转代价，t
            angleWeight = angle * 2 / Math.abs(edge.getW()) + 1;
        }
        if (angleWeight < 0) {
            logger.error("getCost:{}", edge.toString());
            logger.error("getCost:{}", nextEdge.toString());
            throw new PathPlanException(MessageUtils.getMessage("path_plan.angle_weight_negative"));
        }
        return edge.getOriginWeight() + angleWeight;
    }

    //获得n1，n2，n3连线的夹角代价
    public synchronized Double getAngleCost(String n1, String n2, String n3) {
        DirectedEdge edge = getEdgeByStartEndNode(n1, n2);
        DirectedEdge nextEdge = getEdgeByStartEndNode(n2, n3);
        if (nextEdge == null) {
            return 0D;
        }
        double angle = Math.abs(limitRad(nextEdge.getInAngle() - edge.getOutAngle()));
        double angleWeight = 0;
        if (angle > 1.15f / 180f * Math.PI) {
            //TODO:暂时粗略估算旋转代价，t
            angleWeight = angle * 2 / Math.abs(edge.getW()) + 1;
        }
        if (angleWeight < 0) {
            logger.error("getAngleCost:{}", edge.toString());
            logger.error("getAngleCost:{}", nextEdge.toString());
            throw new PathPlanException(MessageUtils.getMessage("path_plan.angle_weight_negative"));
        }
        return angleWeight;
    }

    public Double getDirectCost(SidePath sidePath) {
        DirectedEdge edge = getEdgeByStartEndNode(sidePath.getStartMarkerId(), sidePath.getEndMarkerId());
        return edge.getOriginWeight();
    }

    public Double getSidePathCost(SidePath sidePath) {
        DirectedEdge edge = getEdgeByStartEndNode(sidePath.getStartMarkerId(), sidePath.getEndMarkerId());
        return edge.getTempWeight();
    }

    public synchronized Double getPiecemealCost(SidePath sidePath) {
        DirectedEdge edge = getEdgeByStartEndNode(sidePath.getStartMarkerId(), sidePath.getEndMarkerId());
        if (edge == null) {
            return Double.POSITIVE_INFINITY;
        }
        Double tc;
        if (sidePath.getT0().equals(0D)) {
            tc = edge.getOriginWeight();
        } else if (sidePath.getT0().equals(1D)) {
            tc = 0D;
        } else {
            tc = 2 * SidePathUtils.getDistanceRemaining(sidePath) / Math.abs(edge.getV());
        }
        if (tc < 0) {
            logger.error("getPiecemealCost:{}", edge.toString());
        }
        return Math.abs(tc);
    }

    @Override
    public synchronized DirectedGraph clone() {
        try {
            //DirectedGraph directedGraph = (DirectedGraph) super.clone();
            DirectedGraph directedGraph = new DirectedGraph();
            directedGraph.nodeMap = new ConcurrentHashMap<>(this.nodeMap);
            directedGraph.nodeUVToEdge = CloneUtils.clone(this.nodeUVToEdge);
            directedGraph.predecessorNodes = new ConcurrentHashMap<>(this.predecessorNodes);
            directedGraph.adjacentNodes = new ConcurrentHashMap<>(this.adjacentNodes);
            directedGraph.successorNodes = new ConcurrentHashMap<>(this.predecessorNodes);
            return directedGraph;
        } catch (Exception e) {
            logger.error("Clone DirectedGraph. [{}]", e.getMessage());
            throw new AssertionError();
        }
    }

    public static Double limitRad(Double a) {
        a = a % (2 * Math.PI);
        if (a > Math.PI) {
            a -= 2 * Math.PI;
        } else if (a < -Math.PI) {
            a += 2 * Math.PI;
        }
        return a;
    }
}
