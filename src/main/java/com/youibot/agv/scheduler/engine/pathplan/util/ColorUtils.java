package com.youibot.agv.scheduler.engine.pathplan.util;

import java.awt.*;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date :Created in 下午3:14 2020/10/21
 * @Description :
 * @Modified By :
 * @Version :
 */
public class ColorUtils {

    /**
     * Color对象转换成字符串
     *
     * @param color Color对象
     * @return 16进制颜色字符串
     */
    public static String RGBtoHexString(Color color) {
        String r, g, b;
        StringBuilder su = new StringBuilder();
        r = Integer.toHexString(color.getRed());
        g = Integer.toHexString(color.getGreen());
        b = Integer.toHexString(color.getBlue());
        r = r.length() == 1 ? "0" + r : r;
        g = g.length() == 1 ? "0" + g : g;
        b = b.length() == 1 ? "0" + b : b;
        r = r.toUpperCase();
        g = g.toUpperCase();
        b = b.toUpperCase();
        su.append("0x");
        su.append(r);
        su.append(g);
        su.append(b);
        //0xFFFFFF
        return su.toString();
    }

    /**
     * 字符串转换成Color对象
     *
     * @param colorStr 16进制颜色字符串
     * @return Color对象
     */
    public static Color fromString(String colorStr) {
        colorStr = colorStr.substring(2);
        Color color = new Color(Integer.parseInt(colorStr, 16));
        //java.awt.Color[r=0,g=0,b=255]
        return color;
    }

    public static double[] RGBtoHSV(int rgbR, int rgbG, int rgbB) {
        assert 0 <= rgbR && rgbR <= 255;
        assert 0 <= rgbG && rgbG <= 255;
        assert 0 <= rgbB && rgbB <= 255;
        int[] rgb = new int[]{rgbR, rgbG, rgbB};
        Arrays.sort(rgb);
        int max = rgb[2];
        int min = rgb[0];

        double hsbB = max / 255.0f;
        double hsbS = max == 0 ? 0 : (max - min) / (double) max;

        double hsbH = 0;
        if (max == rgbR && rgbG >= rgbB) {
            hsbH = (rgbG - rgbB) * 60f / (max - min) + 0;
        } else if (max == rgbR && rgbG < rgbB) {
            hsbH = (rgbG - rgbB) * 60f / (max - min) + 360;
        } else if (max == rgbG) {
            hsbH = (rgbB - rgbR) * 60f / (max - min) + 120;
        } else if (max == rgbB) {
            hsbH = (rgbR - rgbG) * 60f / (max - min) + 240;
        }
        return new double[]{hsbH, hsbS, hsbB};
    }

    public static int[] HSVtoRGB(double h, double s, double v) {
        h = h % 360;
        assert Double.compare(h, 0.0d) >= 0 && Double.compare(h, 360.0d) < 0;
        assert Double.compare(s, 0.0d) >= 0 && Double.compare(s, 1.0d) <= 0;
        assert Double.compare(v, 0.0d) >= 0 && Double.compare(v, 1.0d) <= 0;

        double R = 0, G = 0, B = 0;
        if (s == 0d) {
            R = G = B = v;
        } else {
            double H = h / 60;
            int i = (int) H;
            double f = H - i;
            double a = v * (1 - s);
            double b = v * (1 - s * f);
            double c = v * (1 - s * (1 - f));
            switch (i) {
                case 0:
                    R = v;
                    G = c;
                    B = a;
                    break;
                case 1:
                    R = b;
                    G = v;
                    B = a;
                    break;
                case 2:
                    R = a;
                    G = v;
                    B = c;
                    break;
                case 3:
                    R = a;
                    G = b;
                    B = v;
                    break;
                case 4:
                    R = c;
                    G = a;
                    B = v;
                    break;
                case 5:
                    R = v;
                    G = a;
                    B = b;
                    break;
                default:
                    break;
            }
        }
        return new int[]{(int) (R * 255.0), (int) (G * 255.0),
                (int) (B * 255.0)};
    }

    public static String HSVtoRGBHexString(double h, double s, double v) {
        h = h % 360;
        s = s % 1;
        v = v % 1;
        int[] rgb = HSVtoRGB(h, s, v);
        Color color = new Color(rgb[0], rgb[1], rgb[2]);
        return RGBtoHexString(color);
    }
}
