package com.youibot.agv.scheduler.engine.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import lombok.Data;

import javax.persistence.Transient;

/**
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019/6/28 11:40
 */
@Data
public class Resource {

    // 申请的vehicle
    @Transient
    @JsonIgnore
    private Vehicle applyVehicle;

    //占用的vehicle
    @Transient
    @JsonIgnore
    private Vehicle occupyVehicle;
}
