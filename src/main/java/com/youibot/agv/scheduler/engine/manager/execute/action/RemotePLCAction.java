package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import com.youibot.agv.scheduler.constant.ActionConstant;
import com.youibot.agv.scheduler.engine.manager.modbus.ModbusUtils;
import com.youibot.agv.scheduler.engine.util.NumericalUtils;
import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionWorkGlobalVariableService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.Date;
import java.util.Map;

@Service
@Scope("prototype")
public class RemotePLCAction extends DefaultAction {
    Logger logger = LoggerFactory.getLogger(ModbusAction.class);
    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    @Override
    protected Map<String, Object> sendCommand() throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        super.faultResendAllCount = 0;
        JSONObject paramJson = this.getParamJson();
        String ip = paramJson.getString("ip");
        Integer port = paramJson.getInteger("port");
        String code = paramJson.getString("code");
        Integer slaveId=paramJson.getInteger("slaveId");
        Integer startAddress = paramJson.getInteger("startAddress");
        Integer value = paramJson.getInteger("value");
        Integer checkValue = paramJson.getInteger("checkValue");

        Map<String, Object> resultData = new HashedMap<>(2);
        if (ActionConstant.REMOTE_PLC_FUNCTION_READ_01.equals(code) || ActionConstant.REMOTE_PLC_FUNCTION_READ_03.equals(code)) {//Modbus读
            Long time_out = paramJson.getLong("timeOut");//超时时间
            String condition = paramJson.getString("condition");
            if (checkValue == null || time_out == null || StringUtils.isEmpty(condition)) {
                throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
            }
            long startTime = System.currentTimeMillis();//开始时间
            int resultValue = readModbusValue(ip, port,code, slaveId,startAddress);//读取modbus值
            while (!NumericalUtils.numericalComparison(condition, resultValue, checkValue)) {//如果动作设置的值与读取plc的值不一致，继续读取
                if (Thread.interrupted()) {//如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }
                Thread.sleep(500);
                if ((new Date().getTime() - startTime) / 1000 > time_out) {
                    throw new ExecuteException(MessageFormat.format(MessageUtils.getMessage("action.condition_does_not_hold"), time_out, resultValue, condition, value));//条件判断无法满足
                }
                resultValue = readModbusValue(ip, port,code, slaveId,startAddress);
            }
            resultData.put("result", resultValue);
            String variable_key = paramJson.getString("variable_key");
            MissionWorkGlobalVariable missionWorkGlobalVariable = missionWorkGlobalVariableService.selectByMissionWorkIdAndKey(missionWorkId, variable_key);
            if (missionWorkGlobalVariable != null) {
                missionWorkGlobalVariable.setVariableValue(Integer.toString(resultValue));
                missionWorkGlobalVariableService.update(missionWorkGlobalVariable);
            }
        } else if (ActionConstant.REMOTE_PLC_FUNCTION_WRITE_05.equals(code) || ActionConstant.REMOTE_PLC_FUNCTION_WRITE_06.equals(code)) {
            //Modbus写
            boolean result;
            try {
                // 操作码为01 05 其值仅能是0 1
                if (ActionConstant.REMOTE_PLC_FUNCTION_WRITE_05.equals(code)) {
                    if (value != 0 && value != 1) {
                        throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
                    }
                    result = ModbusUtils.writeCoil(ip, port, slaveId, startAddress, value == 1 ? true : false);
                } else {
                    result = ModbusUtils.writeIntegerCoils(ip, port, slaveId, startAddress, value);
                }
                if (!result) {
                    throw new ExecuteException(MessageUtils.getMessage("action.write_modbus_fail"));
                }
                resultData.put("result", true);
            } catch (ModbusTransportException | ModbusInitException e) {
                logger.error(e.getMessage());
                throw new ExecuteException(MessageUtils.getMessage("action.modbus_communication_failure"));
            }
        } else {
            throw new ExecuteException(MessageUtils.getMessage("action.wrong_value") + " mode=" + code);
        }
        return resultData;
    }


    private int readModbusValue(String ip, Integer port,String code,Integer slaveId,Integer startAddress) {
        try {
            if (ActionConstant.REMOTE_PLC_FUNCTION_READ_01.equals(code)) {
                boolean[] coilStatus = ModbusUtils.readCoilStatus(ip, port, slaveId, startAddress, 1);
                if (coilStatus.length > 0) {
                    return coilStatus[0] ? 1 : 0;
                }
                throw new ExecuteException(MessageUtils.getMessage("action.read_modbus_result_null"));
            } else {
                int resultValues[] = ModbusUtils.readHoldingRegisters(ip, port, slaveId, startAddress.intValue(), 1);
                if (resultValues.length > 0) {
                    return resultValues[0];
                }
                throw new ExecuteException(MessageUtils.getMessage("action.read_modbus_result_null"));
            }
        } catch (ModbusTransportException | ModbusInitException e) {
            logger.error("读取modbus失败ip={} port={} 错误信息", ip, port, e);
            throw new ExecuteException(MessageUtils.getMessage("action.modbus_communication_failure"));
        }
    }

//    public static void main(String[] args) {
//        RemotePLCAction action = new RemotePLCAction();
//        try {
//            Map<String, Object> result= action.sendCommand();
//            System.out.println(result);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    public JSONObject getParamJson(){
//        JSONObject object = new JSONObject();
//        object.put("ip","127.0.0.1");
//        object.put("port",502);
//        object.put("code","06");
//        object.put("slaveId",1);
//        object.put("startAddress",3);
//        object.put("value",10);
//
//        object.put("condition","==");
//        object.put("timeOut",60);
//        object.put("checkValue",10);
//        return object;
//    }



}
