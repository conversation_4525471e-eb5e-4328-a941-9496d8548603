package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil;
import com.youibot.agv.scheduler.engine.pathplan.service.impl.AgvSortServiceImpl;
import com.youibot.agv.scheduler.exception.ExecuteException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil.getDistance;
import static com.youibot.agv.scheduler.engine.pathplan.service.impl.AgvSortServiceImpl.sort;

/**
 * <AUTHOR>
 * @Date :Created in 上午11:04 2021/1/8
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
public class TMSMarkerIdsSequenceAlgorithm {

    Map<String, Map<String, Double>> distanceMatrix;

    private String agvLocation;
    private List<String> markerIds;

    public void init(String agvLocation, List<String> markerIds) {
        if (agvLocation == null || CollectionUtils.isEmpty(markerIds)) {
            log.error("error param");
            throw new ExecuteException("error param");
        }
        this.agvLocation = agvLocation;
        this.markerIds = markerIds;
        distanceMatrix = DistanceMatrixUtil.getDistanceMatrix();
    }

    public List<String> run() {
        AgvSortServiceImpl.AGVCost[] agvCosts = new AgvSortServiceImpl.AGVCost[markerIds.size()];
        for (int i = 0; i < markerIds.size(); i++) {
            String markerId = markerIds.get(i);
            Double distance = getDistance(distanceMatrix, agvLocation, markerId);

            AgvSortServiceImpl.AGVCost agvCost = new AgvSortServiceImpl.AGVCost(markerId, distance);
            agvCosts[i] = agvCost;
        }
        sort(agvCosts);
        return Arrays.stream(agvCosts).map(agvCost -> agvCost.agvCode).collect(Collectors.toList());
    }
}
