package com.youibot.agv.scheduler.engine.pathplan.service.impl;


import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.constant.MissionConstant;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.Position;
import com.youibot.agv.scheduler.engine.pathplan.entity.SendSidePathMessage;
import com.youibot.agv.scheduler.engine.pathplan.entity.SidePathPlanResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.*;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.TrafficManager;
import com.youibot.agv.scheduler.engine.pathplan.util.ColorUtils;
import com.youibot.agv.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.listener.event.ReportSidePathDataEvent;
import com.youibot.agv.scheduler.mqtt.bean.push.pathplan.SidePathMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.PositionMessage;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.thread.MapUpdateThread;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.Colors;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.*;
import static com.youibot.agv.scheduler.constant.VehicleConstant.*;


/**
 * <AUTHOR> <EMAIL>
 * @Dara :Created in 下午4:57 2020/8/15
 * @Description :
 * @Modified By :
 * @Version :
 */
@Service
public class CheckAndSendPathServiceImpl implements CheckAndSendPathService {
    private static final Logger logger = LoggerFactory.getLogger(CheckAndSendPathServiceImpl.class);

    @Value("${PATH_PLAN.AGV_SAFE_TIME_PURE_PURSUIT}")
    private Double agvSafeTimePurePursuit;

    @Value("${PATH_PLAN.AGV_SAFE_DISTANCE_PURE_PURSUIT}")
    private Double agvSafeDistancePurePursuit;

    @Value("${PATH_PLAN.PLANNED_PLUS_AUTO_WEIGHT_OBVERSE}")
    private Double plannedObversePlusAutoWeight;

    @Value("${PATH_PLAN.PLANNED_PLUS_AUTO_WEIGHT_REVERSE}")
    private Double plannedReversePlusAutoWeight;

    @Value("${PATH_PLAN.RUNNING_PLUS_AUTO_WEIGHT_OBVERSE}")
    private Double runningObversePlusAutoWeight;

    @Value("${PATH_PLAN.RUNNING_PLUS_AUTO_WEIGHT_REVERSE}")
    private Double runningReversePlusAutoWeight;

    //等待系统启动2min后才开始发送路径
    private Integer sendPathAfterSystemInitWaitTime = 0;

    @Autowired
    private TrafficManager trafficManager;

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private LocationService locationService;

    @Autowired
    private PathPlanAndSendPathLogServiceImpl pathPlanAndSendPathLogServiceImpl;

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;

    @Autowired
    private ParkSchedulerService parkSchedulerService;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    

    private ElevatorPathResourcePool elevatorResourcePool;
    @Autowired
    private MarkerPathResourcePool markerPathResourcePool;
    @Autowired
    private SidePathResourcePool sidePathResourcePool;
    @Autowired
    private TPathResourcePool tPathResourcePool;
    @Autowired
    private OPathResourcePool oPathResourcePool;
    @Autowired
    private SingleAreaPathResourcePool singleAreaPathResourcePool;


    private Map<String, Colors> agvToColors = new HashMap<>();
    private Integer agvNum = 0;
    private Integer oldAgvNum = 0;

    private ConcurrentHashMap<String, PathPlanMessage> agvToPathPlanMessage = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, String> agvToStartMarkerId = new ConcurrentHashMap<>();

    private Map<String, String> agvCodeLock = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, Long> agvToLastLocationTime = new ConcurrentHashMap<>();

    private Map<String, AtomicInteger> agvToPositionMessageCount = new ConcurrentHashMap();

    private ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToExecutedSidePaths = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRunningSidePaths = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToTotalSidePaths = new ConcurrentHashMap<>();

    private CheckAndSendPathServiceImpl() {
        PositionMessageProcessThread positionMessageProcessThread = new PositionMessageProcessThread();
        positionMessageProcessThread.setPriority(7);
        positionMessageProcessThread.start();
    }

    @Override
    public Map<String, String> getAgvCodeLock(){
        return agvCodeLock;
    }

    @Override
    public boolean addSidePathPlanResult(SidePathPlanResult sidePathPlanResult, PathPlanMessage pathPlanMessage) {
        if (sidePathPlanResult.getSidePaths().size() == 0) {
            return false;
        }
        if (pathPlanMessage == null) {
            return false;
        }
        String agvCode = sidePathPlanResult.getAgvCode();

        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            //stop move and clean path.
            this.clear(agvCode, PATH_PLAN_MISSION_WORK_ID);
            agvToPathPlanMessage.put(agvCode, pathPlanMessage);
            // init side path object.
            agvToExecutedSidePaths.put(agvCode, new LinkedBlockingDeque<>());
            agvToRunningSidePaths.put(agvCode, new LinkedBlockingDeque<>());
            agvToPlanedSidePaths.put(agvCode, new LinkedBlockingDeque<>());

            modifyAutoWeight(agvCode, new LinkedBlockingDeque<>(sidePathPlanResult.getSidePaths()), PLUS_WEIGHT, plannedObversePlusAutoWeight, plannedReversePlusAutoWeight);
            agvToPlanedSidePaths.get(agvCode).addAll(sidePathPlanResult.getSidePaths());
            setSidePathToVehicle(agvCode);
        }
        return true;
    }


    @Override
    public PathPlanMessage delSidePathPlanResult(String agvCode) {
        return agvToPathPlanMessage.remove(agvCode);
    }

    class PositionMessageProcessThread extends Thread {
        @Override
        public void run() {
            this.setName("PositionMessage-CheckAndSendPath-Thread");
            Map<String, Integer> agvToPositionMessageLostCount = new HashMap<>();
            int sleepTime = 100;//ms
            int checkCount = 30000 / sleepTime;
            int waitTime = sendPathAfterSystemInitWaitTime * 60 * 1000;
            while (true) {
                try {
                    Thread.sleep(sleepTime);
                    /**
                     * 双机热备时，开机等两分钟开始路径规划。
                     */
                    if (DateUtils.initSystemDate == null) {
                        Thread.sleep(2000);
                        continue;
                    }
                    if (System.currentTimeMillis() - DateUtils.initSystemDate.getTime() < waitTime) {
                        continue;
                    }
                    /**
                     * 如果100MS内没有收到定位信息，则不下发路径。并记录丢失的次数。
                     * 但只要有定位数据就会下发路径。
                     */
                    List<String> list = new ArrayList<>();
                    for (Map.Entry<String, AtomicInteger> stringAtomicIntegerEntry : agvToPositionMessageCount.entrySet()) {
                        String agvCode = stringAtomicIntegerEntry.getKey();
                        AtomicInteger count = stringAtomicIntegerEntry.getValue();
                        if (count.get() > 0) {
                            count.set(0);
                            agvToPositionMessageLostCount.put(agvCode, 0);
                            list.add(agvCode);
                        } else {
                            agvToPositionMessageLostCount.put(agvCode, agvToPositionMessageLostCount.getOrDefault(agvCode, 0) + 1);
                        }
                    }
                    /**
                     * 30秒内机器人一直未反馈位置信息。则设置位置信息(会清理占用的路径)。
                     */
                    for (Map.Entry<String, Integer> stringIntegerEntry : agvToPositionMessageLostCount.entrySet()) {
                        String agvCode = stringIntegerEntry.getKey();
                        Integer lostCount = stringIntegerEntry.getValue();
                        if (lostCount > checkCount) {
                            setAGVPosition(agvCode, locationService.getVehicleLocation(agvCode));
                        }
                    }
                    /**
                     * agvPositionMessageProcess 调用路径下发逻辑。
                     */
                    if (!CollectionUtils.isEmpty(list)) {
                        int size = list.size();
                        /**
                         * 加个随机，任性，不知道为什么？@Thomas
                         */
                        List<Integer> randomArray = generateRandomArray(size);
                        for (int i = 0; i < size; i++) {
                            String agvCode = list.get(randomArray.get(i));
                            agvPositionMessageProcess(agvCode);
                        }
                    }
                } catch (Exception e) {
                    logger.error("process position message error. {}", LogExceptionStackUtil.LogExceptionStack(e));
                }
            }
        }
    }

    @Override
    public boolean checkAndReSendSidePath(String agvCode,
                                          VehicleLocation location,
                                          LinkedBlockingDeque<SidePath> planedSidePaths,
                                          LinkedBlockingDeque<SidePath> runningSidePaths,
                                          LinkedBlockingDeque<SidePath> executedSidePaths,
                                          boolean onlySendOneSidePath,
                                          boolean sendSidePath,
                                          String exceptMarkerId) {
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {

            //如果导航的目标点处于需要发布的地图上，暂不进行资源申请，等待地图发布完成后，再进行
            PathPlanMessage ppm = agvToPathPlanMessage.get(agvCode);
            Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
            if(ppm!=null && ( PathPlanManger.containsAniMarker(ppm.getAimMarkerId()) || PathPlanManger.containsVehicle(vehicle))){
                return false;
            }

            if (CollectionUtils.isEmpty(planedSidePaths) && CollectionUtils.isEmpty(runningSidePaths)) {
                trafficManager.applyAndReleaseResource(agvCode, planedSidePaths, null, runningSidePaths, executedSidePaths, location, exceptMarkerId);
                return false;
            }
            if (executedSidePaths == null) {
                executedSidePaths = new LinkedBlockingDeque<>();
            }
            /**
             * running side path 还有比较长的距离超过agvSafeTimePurePursuit。不进行下发。则继续申请和释放占用的资源。
             */
            Double cost = MapGraphUtil.getCost(runningSidePaths);
            if (cost > agvSafeTimePurePursuit && org.apache.commons.collections4.CollectionUtils.isNotEmpty(runningSidePaths)) {
               logger.info("agv {} running_side_path_cost_over_agv_safe_time_pure_pursuit, not_send_path,runningSidePaths:{}, cost:{},agvSafeTimePurePursuit:{}", agvCode, JSON.toJSONString(runningSidePaths), cost , agvSafeTimePurePursuit);
                trafficManager.applyAndReleaseResource(agvCode, planedSidePaths, null, runningSidePaths, executedSidePaths, location, exceptMarkerId);
                return false;
            }
            boolean haveElevatorPath = MapGraphUtil.haveElevatorPath(runningSidePaths ,executedSidePaths);
            if(haveElevatorPath){
                logger.info("agv {} have_elevator_path, not_send_path,runningSidePaths:{}, executedSidePaths:{}", agvCode, JSON.toJSONString(runningSidePaths), JSON.toJSONString(executedSidePaths));
                return  false ;
            }
            /**
             * 计算需要下发的路径。
             * 正常情况下，下发的路径长度评分不能大于agvSafeTimePurePursuit
             */
            LinkedBlockingDeque<SidePath> piecemealSidePaths = new LinkedBlockingDeque<>();
            LinkedBlockingDeque<SidePath> tempPlannedSidePaths = new LinkedBlockingDeque<>();
            if (sendSidePath) {
                if (onlySendOneSidePath) {
                    if (planedSidePaths.size() > 0) {
                        tempPlannedSidePaths = new LinkedBlockingDeque<>(planedSidePaths);
                        piecemealSidePaths.add(tempPlannedSidePaths.pollFirst());
                    }
                } else {
                    List<LinkedBlockingDeque<SidePath>> list = MapGraphUtil.spiltSidePathByCost(planedSidePaths, agvSafeTimePurePursuit - cost);
                    piecemealSidePaths.addAll(list.get(0));
                    tempPlannedSidePaths.addAll(list.get(1));
                }
            }
            /**
             * 如果没有下发路径。则清理并退出。
             */
            if (piecemealSidePaths.isEmpty()) {
                trafficManager.applyAndReleaseResource(agvCode, planedSidePaths, piecemealSidePaths, runningSidePaths, executedSidePaths, location, exceptMarkerId);
                return false;
            }
            /**
             * 下面的代码是为了处理电梯的逻辑，电梯是虚拟连接。并且需要三个路径一起下发。
             */
            boolean applyFlag = false;
            Integer lastNavigationType = piecemealSidePaths.getLast().getNavigationType();

            if (lastNavigationType.equals(SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR)) {
                SidePath takeElevatorSidePath = tempPlannedSidePaths.pollFirst();
                piecemealSidePaths.addLast(takeElevatorSidePath);
                SidePath outElevatorSidePath = tempPlannedSidePaths.pollFirst();
                piecemealSidePaths.addLast(outElevatorSidePath);

                SidePath outElevatorSidePath1 = tempPlannedSidePaths.pollFirst();
                piecemealSidePaths.addLast(outElevatorSidePath1);
                SidePath outElevatorSidePath2 = tempPlannedSidePaths.pollFirst();
                piecemealSidePaths.addLast(outElevatorSidePath2);


                applyFlag = trafficManager.applyAndReleaseResource(agvCode, planedSidePaths, piecemealSidePaths, runningSidePaths, executedSidePaths, location, exceptMarkerId);
                if (!applyFlag) {
                    SidePath sidePath = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath);
                    SidePath sidePath1 = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath1);
                    SidePath sidePath2 = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath2);

                    SidePath sidePath3= piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath3);
                    SidePath sidePath4 = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath4);

                    if (piecemealSidePaths.isEmpty()) {
                        return false;
                    }
                }
            } else if (lastNavigationType.equals(SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR)) {
                SidePath outElevatorSidePath = tempPlannedSidePaths.pollFirst();
                piecemealSidePaths.addLast(outElevatorSidePath);

                SidePath outElevatorSidePath2 = tempPlannedSidePaths.pollFirst();
                piecemealSidePaths.addLast(outElevatorSidePath2);
                SidePath outElevatorSidePath3 = tempPlannedSidePaths.pollFirst();
                piecemealSidePaths.addLast(outElevatorSidePath3);


                applyFlag = trafficManager.applyAndReleaseResource(agvCode, planedSidePaths, piecemealSidePaths, runningSidePaths, executedSidePaths, location, exceptMarkerId);
                if (!applyFlag) {
                    SidePath sidePath = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath);
                    SidePath sidePath1 = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath1);
                    SidePath sidePath2 = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath2);

                    SidePath sidePath3 = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath3);
                    SidePath sidePath4 = piecemealSidePaths.pollLast();
                    tempPlannedSidePaths.addFirst(sidePath4);

                    if (piecemealSidePaths.isEmpty()) {
                        return false;
                    }
                }
            }
            /**
             * 如果下发中存在多个路径。且申请路径占用失败的话。则去掉最后一个路径。再进行申请，直到最后一个路径。
             */
            if (!applyFlag && !ELEVATOR_POINT_TYPE_SET.contains(lastNavigationType)) {
                while (piecemealSidePaths.stream().anyMatch(p -> !ELEVATOR_POINT_TYPE_SET.contains( p.getNavigationType()))) {
                    applyFlag = trafficManager.applyAndReleaseResource(agvCode, planedSidePaths, piecemealSidePaths, runningSidePaths, executedSidePaths, location, exceptMarkerId);
                    if (!applyFlag) {

                        //如果申请不成功，就从下发路径中删除最后一段
                        SidePath sidePath = piecemealSidePaths.pollLast();
                        tempPlannedSidePaths.addFirst(sidePath);
                        if (piecemealSidePaths.isEmpty()) {
                            //当下发路径为空时，直接返回false，等待下此检查路径和发送
                            return false;
                        }
                    } else {
                        //如果申请成功，直接退出循环，发送路径
                        break;
                    }
                }
            }
            /**
             * 多线程问题，在两个计划下发后，取消上一个计划后，路径仍然下发引起的多线程问题。
             * 目前处理方式，在下发路径前对路径所在的消息pathPlanMessage判断当前作业是否有效
             * 如果无效直接清理，结束下发
             */
            PathPlanMessage pathPlanMessage = agvToPathPlanMessage.get(agvCode);
            if (null == pathPlanMessage) {
                this.clear(agvCode, PATH_PLAN_MISSION_WORK_ID);
                trafficManager.releaseApplyIncludeLocation(agvCode);
                return false;
            }
            String runningMissionWorkId = pathPlanMessage.getMissionWorkId();
            if (StringUtils.isNotBlank(runningMissionWorkId)) {
                if ("SMART_CHARGE".equalsIgnoreCase(runningMissionWorkId)) {
                    ChargeScheduler chargeScheduler = chargeSchedulerService.selectLastOne(agvCode);
                    if (null != chargeScheduler && ("CANCEL".equalsIgnoreCase(chargeScheduler.getStatus())
                            || "FAULT".equalsIgnoreCase(chargeScheduler.getStatus()) || "SUCCESS".equalsIgnoreCase(chargeScheduler.getStatus()))) {
                        logger.warn("agvCode:[{}],event:[路径下发],content:[机器人路径对应的充电调度计划{}已经失效，不再下发路径]", agvCode, chargeScheduler.getId());
                        this.clear(agvCode, runningMissionWorkId);
                        return false;
                    }
                } else if ("SMART_WAIT".equalsIgnoreCase(runningMissionWorkId)) {
                    ParkScheduler parkScheduler = parkSchedulerService.selectLastOne(agvCode);
                    if (null != parkScheduler && ("CANCEL".equalsIgnoreCase(parkScheduler.getStatus())
                            || "FAULT".equalsIgnoreCase(parkScheduler.getStatus()) || "SUCCESS".equalsIgnoreCase(parkScheduler.getStatus()))) {
                        logger.warn("agvCode:[{}],event:[路径下发],content:[机器人路径对应的泊车调度计划{}已经失效，不再下发路径]", agvCode, parkScheduler.getId());
                        this.clear(agvCode, runningMissionWorkId);
                        return false;
                    }
                } else {
                    //当作业状态为停止，停止中，错误，成功时，此时再下发路径为无效路径
                    MissionWork missionWork = missionWorkService.selectById(runningMissionWorkId);
                    if (null != missionWork && StringUtils.isNotBlank(missionWork.getStatus()) && (MissionConstant.MISSION_WORK_STATUS_SHUTDOWN.equalsIgnoreCase(missionWork.getStatus()) ||
                            MissionConstant.MISSION_WORK_STATUS_BEING_SHUTDOWN.equalsIgnoreCase(missionWork.getStatus()) || MissionConstant.MISSION_WORK_STATUS_FAULT.equalsIgnoreCase(missionWork.getStatus()) ||
                            MissionConstant.MISSION_WORK_STATUS_SUCCESS.equalsIgnoreCase(missionWork.getStatus()))) {
                        logger.warn("agvCode:[{}],event:[路径下发],content:[机器人路径对应的作业{}已经失效，不再下发路径]", agvCode, runningMissionWorkId);
                        this.clear(agvCode, runningMissionWorkId);
                        return false;
                    }
                }
            }
            /**
             * 路径申请成功。下发路径。
             */
            if (applyFlag) {
                sendMqttSidePathMessage(agvCode, piecemealSidePaths, tempPlannedSidePaths);
            }
            return applyFlag;
        }
    }

    @Override
    public boolean sendMqttSidePathMessage(String agvCode, LinkedBlockingDeque<SidePath> piecemealSidePaths, LinkedBlockingDeque<SidePath> tempPlannedSidePaths) {

  	  logger.debug("start_agvCode:[{}] send side path to vehicle: [{}]", agvCode, MapGraphUtil.printSidePaths(piecemealSidePaths));
        /**
         * 发布
         */
    	 publishEvents(agvCode, piecemealSidePaths);
    	 
    	PathPlanMessage pathPlanMessage = agvToPathPlanMessage.get(agvCode);
        if (pathPlanMessage.getIsCancel() != null && pathPlanMessage.getIsCancel() == 1) {
            logger.warn("agvCode:[{}],event:[路径下发],content:[当前路径规划已经取消,pathPlanMessage:{}]", agvCode, JSON.toJSONString(pathPlanMessage));
            return false;
        }
        SidePathMessage sidePathMessage = new SidePathMessage(piecemealSidePaths, pathPlanMessage.getMissionWorkActionId(), pathPlanMessage.getMissionWorkId(), SEND_SIDE_PATH_TYPE_SEND_PATH);
        MqttUtils.sendMqttMsg(MqttConstant.MOVE_BY_SIDE_PATH, agvCode, sidePathMessage);
        convertPlanedSidePathsToRunningSidePaths(agvCode, piecemealSidePaths, tempPlannedSidePaths);
        logger.warn("agvCode:[{}],event:[路径下发],content:[下发路径完成sidePaths:[{}]]", agvCode, MapGraphUtil.printSidePaths(piecemealSidePaths));
        SendSidePathMessage sendSidePathMessage = new SendSidePathMessage(agvCode, sidePathMessage);
        pathPlanAndSendPathLogServiceImpl.saveSendSidePathLog(sendSidePathMessage);
        return true;
    }


    /**
     * @param agvCode
     * @param piecemealSidePaths
     */
    private  void publishEvents(String agvCode , LinkedBlockingDeque<SidePath> piecemealSidePaths) {
    	
    	try {
    		CompletableFuture.runAsync( () -> {
    			for (SidePath sidePath : piecemealSidePaths) {
    				if (StringUtils.isNotBlank(sidePath.getReportType())) {
        				ReportSidePathData data = new ReportSidePathData(sidePath.getEndMarkerId(), sidePath, agvCode, true);
						/*
						 * logger.debug("test_report============>>>>>>>>>>{}", JSON.toJSONString(data));
						 */        				
        				applicationEventPublisher.publishEvent(new ReportSidePathDataEvent(data));
        			} 
    			}
    			
    			
    		});
		
		} catch (Exception e) {
			logger.error("agvCode_publish_events_error:{}" , agvCode);
		}
    }
    
    
    @Override
    public void clear() {
        if (agvToExecutedSidePaths != null) {
            for (String agvCode : agvToExecutedSidePaths.keySet()) {
                clear(agvCode, PATH_PLAN_MISSION_WORK_ID);
            }
        }
    }

    @Override
    public void clear(String agvMapId) {
        if (agvToExecutedSidePaths != null) {
            for (String agvCode : agvToExecutedSidePaths.keySet()) {
                Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
                if (vehicle != null && agvMapId.equals(vehicle.getAgvMapId())) {
                    clear(agvCode, PATH_PLAN_MISSION_WORK_ID);
                }
            }
        }
    }

    @Override
    public void clear(String agvCode, String missionWorkId) {
        long start = System.currentTimeMillis();
        VehicleLocation location = locationService.getVehicleLocation(agvCode) ;
        long end = System.currentTimeMillis();

        logger.debug("agvCode:[{}],event:[clear],计算定位数据耗时[{}]ms", agvCode, Math.abs(end - start));
        if (location != null && location.getMarker() != null) {
            logger.debug("agvCode:[{}],event:[clear],当前点位:[{}]", agvCode, location.getMarker().getCode());
        }
        clear(agvCode, location, missionWorkId);
    }

    @Override
    public void clearAgvResource(String agvCode) {
        if (StringUtils.isBlank(agvCode)) {
            return;
        }
        logger.debug("agvCode:[{}], event:[clearAgvResource],开始清理资源", agvCode);
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        VehicleLocation location = locationService.getVehicleLocation(agvCode);
        synchronized (agvCodeLock.get(agvCode)) {
            //清理sidePath
            if (agvToRunningSidePaths != null) {
                convertRunningSidePathsToExecutedSidePathsAccordingLocation(agvCode, agvToRunningSidePaths.get(agvCode), location);
            }
            LinkedBlockingDeque<SidePath> executedSidePaths = null;
            if (agvToExecutedSidePaths != null) executedSidePaths = agvToExecutedSidePaths.get(agvCode);
            else executedSidePaths = new LinkedBlockingDeque<>();

            LinkedBlockingDeque<SidePath> runningSidePaths = null;
            if (agvToRunningSidePaths != null) runningSidePaths = agvToRunningSidePaths.get(agvCode);
            else runningSidePaths = new LinkedBlockingDeque<>();

            LinkedBlockingDeque<SidePath> plannedSidePaths = null;
            if (agvToPlanedSidePaths != null) plannedSidePaths = agvToPlanedSidePaths.get(agvCode);
            else plannedSidePaths = new LinkedBlockingDeque<>();

            // first clean running side path.
            if (runningSidePaths != null) {
                modifyAutoWeight(agvCode, runningSidePaths, MINUS_WEIGHT, plannedObversePlusAutoWeight + runningObversePlusAutoWeight, plannedReversePlusAutoWeight + runningReversePlusAutoWeight);
                runningSidePaths.clear();
            }

            // second clean executed side path.
            if (plannedSidePaths != null) {
                modifyAutoWeight(agvCode, plannedSidePaths, MINUS_WEIGHT, plannedObversePlusAutoWeight, plannedReversePlusAutoWeight);
                plannedSidePaths.clear();
            }

            // third clean executed side path.
            if (executedSidePaths != null) {
                executedSidePaths.clear();
            }
            trafficManager.releaseApplyIncludeLocation(agvCode);
            locationService.removeVehicleLocation(agvCode);
            agvToStartMarkerId.remove(agvCode);
            setSidePathToVehicle(agvCode);
            agvToPathPlanMessage.remove(agvCode);
        }
        logger.debug("agvCode:[{}], event:[clearAgvResource],结束清理资源", agvCode);
    }

    @Override
    public void clear(String agvCode, VehicleLocation location, String missionWorkId) {

        logger.info("agvCode:[{}], event:[clear],打印方法调用栈信息:[{}]", agvCode,CommonUtils.getStackTrace());
        if (agvCode == null) return;
        if (missionWorkId == null) return;
        PathPlanMessage pathPlanMessage = agvToPathPlanMessage.get(agvCode);
        if (pathPlanMessage == null) return;
        String tempMissionWorkId = pathPlanMessage.getMissionWorkId();
        if (!PATH_PLAN_MISSION_WORK_ID.equals(missionWorkId) && !missionWorkId.equals(tempMissionWorkId)) {
            return;
        }

        logger.debug("agvCode:[{}], event:[clear],清理小车的路径资源-开始",agvCode);
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            if (agvToRunningSidePaths != null) {
                convertRunningSidePathsToExecutedSidePathsAccordingLocation(agvCode, agvToRunningSidePaths.get(agvCode), location);
            }
            LinkedBlockingDeque<SidePath> executedSidePaths = null;
            if (agvToExecutedSidePaths != null) executedSidePaths = agvToExecutedSidePaths.get(agvCode);
            else executedSidePaths = new LinkedBlockingDeque<>();

            LinkedBlockingDeque<SidePath> runningSidePaths = null;
            if (agvToRunningSidePaths != null) runningSidePaths = agvToRunningSidePaths.get(agvCode);
            else runningSidePaths = new LinkedBlockingDeque<>();

            LinkedBlockingDeque<SidePath> plannedSidePaths = null;
            if (agvToPlanedSidePaths != null) plannedSidePaths = agvToPlanedSidePaths.get(agvCode);
            else plannedSidePaths = new LinkedBlockingDeque<>();

            // first clean running side path.
            if (runningSidePaths != null) {
                modifyAutoWeight(agvCode, runningSidePaths, MINUS_WEIGHT, plannedObversePlusAutoWeight + runningObversePlusAutoWeight, plannedReversePlusAutoWeight + runningReversePlusAutoWeight);
                runningSidePaths.clear();
            }

            // second clean executed side path.
            if (plannedSidePaths != null) {
                modifyAutoWeight(agvCode, plannedSidePaths, MINUS_WEIGHT, plannedObversePlusAutoWeight, plannedReversePlusAutoWeight);
                plannedSidePaths.clear();
            }

            // third clean executed side path.
            if (executedSidePaths != null) {
                executedSidePaths.clear();
            }
            trafficManager.releaseApplyExceptLocation(agvCode, location);
            agvToStartMarkerId.remove(agvCode);
            setSidePathToVehicle(agvCode);
            agvToPathPlanMessage.remove(agvCode);
            agvToTotalSidePaths.remove(agvCode);
        }
        logger.debug("agvCode:[{}], event:[clear],清理小车的路径资源-结束",agvCode);
    }

    public void agvPositionCallBack(PositionMessage positionMessage) {
        if (positionMessage == null) {
            logger.error("position message is null");
            return;
        }
        String agvCode = positionMessage.getAgvCode();
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        if (vehicle != null && ONLINE.equals(vehicle.getOnlineStatus())) {
            //logger.debug("agvCode:[{}], x:[{}] y:[{}]", agvCode, positionMessage.getPositionStatus().getPos_x(), positionMessage.getPositionStatus().getPos_y());
            locationService.setAGVPosition(agvCode, positionMessage);
            agvToPositionMessageCount.computeIfAbsent(agvCode, k -> new AtomicInteger(0));
            agvToPositionMessageCount.get(agvCode).incrementAndGet();
        }
    }


    public void agvPositionMessageProcess(String agvCode) {
        if (agvCode == null) {
            logger.error("agvCode is null");
            return;
        }
        // 获取机器人的位置
        VehicleLocation location = locationService.getVehicleLocation(agvCode);
        if (location == null) {
            return;
        }
        //2. get current path.
        LinkedBlockingDeque<SidePath> runningPaths = agvToRunningSidePaths.get(agvCode);
        //3. 添加执行完成的路径到executedSidePath中。
        convertRunningSidePathsToExecutedSidePathsAccordingLocation(agvCode, runningPaths, location);
        //4. change plan path to running path. and send running path to robot.
        //Map<String, List<Pair<String, String>>> conflictAGVs = containConflict(agvCode);

        /**
         * 如果机器人在交通管制中，则由交通管制进行处理。
         * 在YOUIFleet 4.2.3 中去掉交通管制，当机器人发生对向和环型冲突时，会取消两个机器人中的一台路径规划，并且让这台机器人去最近的避让点来进行解决。
         */
        checkAndReSendSidePath(
                agvCode,
                location,
                agvToPlanedSidePaths.get(agvCode),
                agvToRunningSidePaths.get(agvCode),
                agvToExecutedSidePaths.get(agvCode),
                false,
                true,
                null);
    }

    //设置AGV位置到有向边上，会影响交通管制
    public VehicleLocation setAGVPosition(String agvCode, VehicleLocation location) {
        if (agvCode == null) {
            logger.error("agvCode is null");
            return null;
        }
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            boolean haveLocation = (location != null && location.getUpdateTimeMillis() != null && Math.abs(System.currentTimeMillis() - location.getUpdateTimeMillis()) < 5000);
            LinkedBlockingDeque<SidePath> executedPaths = agvToExecutedSidePaths.get(agvCode);
            LinkedBlockingDeque<SidePath> runningPaths = agvToRunningSidePaths.get(agvCode);
            LinkedBlockingDeque<SidePath> planedPaths = agvToPlanedSidePaths.get(agvCode);
            boolean moveMissionIsEmpty = (CollectionUtils.isEmpty(planedPaths) && CollectionUtils.isEmpty(executedPaths) && CollectionUtils.isEmpty(runningPaths));
            if (haveLocation && !moveMissionIsEmpty) {
                //有定位数据，有移动任务，设置AGV位置即可
                MapGraphUtil.setAGVPosition(agvCode, location);
                agvToLastLocationTime.put(agvCode, System.currentTimeMillis());
            } else if (haveLocation && moveMissionIsEmpty) {
                //有定位数据，没有移动任务，清理AGV占用资源,有可能出现问题
                MapGraphUtil.setAGVPosition(agvCode, location);
                agvToLastLocationTime.put(agvCode, System.currentTimeMillis());
                trafficManager.releaseApplyExceptLocation(agvCode, location);
                //trafficManager.applyAndReleaseResource(agvCode, null, runningPaths, executedPaths, location);
            } else if (!haveLocation && moveMissionIsEmpty) {
                //没有定位数据，没有移动任务，估计为机器人脱离轨道维修中，保留10s定位数据，然后清理
                Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
                if (vehicle != null) {
                    Integer onlineStatus = vehicle.getOnlineStatus();
                    if (OUTLINE.equals(onlineStatus) || OFFLINE.equals(onlineStatus)) {
                        clearOccupyPathResource(agvCode);
                    }
                } else {
                    clearOccupyPathResource(agvCode);
                }
            } else if (!haveLocation && !moveMissionIsEmpty) {
                //并且定位失败，有移动任务，估计为机器人在轨道上出故障或停电，一直保留历史定位信息，不清理
            }
            return location;
        }
    }

    private void clearOccupyPathResource(String agvCode) {
        if (agvCode == null) return;
        long tolerateTime = 30000;
        if (agvToLastLocationTime.get(agvCode) == null || Math.abs(agvToLastLocationTime.get(agvCode) - System.currentTimeMillis()) > tolerateTime) {
            MapGraphUtil.clearAGVPosition(agvCode);
            trafficManager.releaseApplyByAGV(agvCode);
        }
    }

    private void convertRunningSidePathsToExecutedSidePathsAccordingLocation(String agvCode, LinkedBlockingDeque<SidePath> runningPaths, VehicleLocation location) {
        if (agvCode == null || location == null || runningPaths == null) return;
        if (runningPaths.size() <= 0) return;
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            if (!CollectionUtils.isEmpty(runningPaths)) {
                List<SidePath> tempRunningPaths = new ArrayList<>(runningPaths);
                Position p = locationService.getAgvPosition(agvCode);
                locationService.setCurrentSidePathT0(p.getAgvMapId(), p.getPos_x(), p.getPos_y(), tempRunningPaths);
                if (location.getMarker() != null) {
                    String currentMarkerId = location.getMarker().getId();
                    SidePath currentSidePath = null;
                    for (SidePath s : tempRunningPaths) {
                        String startMarkerId = s.getStartMarkerId();
                        String endMarkerId = s.getEndMarkerId();
                        if (currentMarkerId.equals(startMarkerId) || currentMarkerId.equals(endMarkerId)) {
                            currentSidePath = s;
                            break;
                        }
                    }
                    if (currentSidePath != null) {
                        for (SidePath sidePath : tempRunningPaths) {
                            String startMarkerId = sidePath.getStartMarkerId();
                            String endMarkerId = sidePath.getEndMarkerId();
                            if (startMarkerId.equals(currentMarkerId)) {
                                break;
                            } else if (endMarkerId.equals(currentMarkerId)) {
                                convertRunningSidePathsToExecutedSidePaths(agvCode, sidePath);
                                break;
                            } else {
                                convertRunningSidePathsToExecutedSidePaths(agvCode, sidePath);
                            }
                        }
                    }
                } else if (location.getSidePaths() != null) {
                    List<SidePath> vls = location.getSidePaths();
                    SidePath currentSidePath = null;
                    for (int i = 0; i < tempRunningPaths.size(); i++) {
                        SidePath sidePathi = tempRunningPaths.get(i);
                        currentSidePath = find(vls, sidePathi);
                        if (currentSidePath != null) {
                            //从前至后找到机器人所在的第一条路径,如果找到，就退出
                            break;
                        }
                    }
                    //当前路径不为null，根据条件将其转化为已执行路径，若当前路径为null，定位丢失，不转化路径
                    if (currentSidePath != null) {
                        for (SidePath tempRunningPath : tempRunningPaths) {
                            if (currentSidePath.getId().equals(tempRunningPath.getId())) {
                                if (currentSidePath.getT0().equals(1D)) {
                                    convertRunningSidePathsToExecutedSidePaths(agvCode, tempRunningPath);
                                }
                                break;
                            } else {
                                //当前路径t0值不为1，仍在执行中，不将运行路径转化为已执行路径
                                convertRunningSidePathsToExecutedSidePaths(agvCode, tempRunningPath);
                            }
                        }
                    }
                }
            }
        }
    }

    private static SidePath find(List<SidePath> sidePaths, SidePath s) {
        for (SidePath sidePath : sidePaths) {
            if (sidePath.getId().equals(s.getId())) {
                return sidePath;
            }
        }
        return null;
    }

    private void convertRunningSidePathsToExecutedSidePaths(String agvCode, LinkedBlockingDeque<SidePath> sidePaths) {
        if (agvCode == null || sidePaths == null) return;
        SidePath[] sidePathsList = sidePaths.stream().toArray(SidePath[]::new);
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            for (int i = 0; i < sidePathsList.length; i++) {
                convertRunningSidePathsToExecutedSidePaths(agvCode, sidePathsList[i]);
            }
        }
    }

    public void convertRunningSidePathsToExecutedSidePaths(String agvCode, SidePath sidePath) {
        if (agvCode == null || sidePath == null) return;
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            agvToRunningSidePaths.get(agvCode).remove(sidePath);
            agvToExecutedSidePaths.get(agvCode).add(sidePath);
            modifyAutoWeight(agvCode, sidePath.getId(), MINUS_WEIGHT, plannedObversePlusAutoWeight + runningObversePlusAutoWeight, plannedReversePlusAutoWeight + runningReversePlusAutoWeight);
            setSidePathToVehicle(agvCode);
        }
    }

    @Override
    public void convertPlanedSidePathsToRunningSidePaths(String agvCode,
                                                         LinkedBlockingDeque<SidePath> first,
                                                         LinkedBlockingDeque<SidePath> second
    ) {
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            agvToStartMarkerId.put(agvCode, first.getLast().getEndMarkerId());
            agvToRunningSidePaths.get(agvCode).addAll(first);
            agvToPlanedSidePaths.put(agvCode, second);
            modifyAutoWeight(agvCode, first, PLUS_WEIGHT, runningObversePlusAutoWeight, runningReversePlusAutoWeight);
            setSidePathToVehicle(agvCode);
        }
    }

    @Override
    public void modifyAutoWeight(String agvCode,
                                 LinkedBlockingDeque<SidePath> sidePaths,
                                 String type,
                                 Double obverseCost,
                                 Double reverseCost) {
        sidePaths.forEach(sidePath -> modifyAutoWeight(agvCode, sidePath.getId(), type, obverseCost, reverseCost));
    }

    private void modifyAutoWeight(String agvCode, String sidePathId, String type, Double obverseCost, Double reverseCost) {
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            SidePath reverseSidePath = MapGraphUtil.getReverseSidePath(sidePathId);
            MapGraphUtil.modifyAutoWeight(agvCode, sidePathId, type, obverseCost);
            if (reverseSidePath != null) {
                MapGraphUtil.modifyAutoWeight(agvCode, reverseSidePath.getId(), type, reverseCost);
            }
        }
    }

    private void setSidePathToVehicle(String agvCode) {
        if (agvCode == null) return;
        try {
            agvCodeLock.computeIfAbsent(agvCode, k -> k);
            synchronized (agvCodeLock.get(agvCode)) {
                List<Path> executedPaths = convertSidePathsToPaths(agvToExecutedSidePaths.get(agvCode));
                List<Path> runningPaths = convertSidePathsToPaths(agvToRunningSidePaths.get(agvCode));
                List<Path> plannedPaths = convertSidePathsToPaths(agvToPlanedSidePaths.get(agvCode));
                Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
                if (vehicle != null) {
                    vehicle.setExecutedPaths(executedPaths);
                    vehicle.setRunningPaths(runningPaths);
                    vehicle.setPlanedPaths(plannedPaths);
                    setAgvPathColorsToVehicle(vehicle);
                }
            }
        } catch (Exception e) {
            logger.error("setSidePathToVehicle error", e);
        }
    }

    private void setAgvPathColorsToVehicle(Vehicle vehicle) {
        try {
            Colors agvPathColors = getAgvPathColors(vehicle);
            vehicle.setColors(agvPathColors);
        } catch (Exception e) {
            logger.error("setAgvPathColorsToVehicle error", e);
        }
    }

    private Colors getAgvPathColors(Vehicle vehicle) {
        String agvColor = vehicle.getAgvColor();
        Color color = ColorUtils.fromString(agvColor);
        Color brighter = color.brighter();
        String brighterColor = ColorUtils.RGBtoHexString(brighter);
        return new Colors("#8A8A8A", agvColor, brighterColor);
    }

    private List<Path> convertSidePathsToPaths(LinkedBlockingDeque<SidePath> sidePaths) {
        List<Path> paths = new ArrayList<>();
        if (sidePaths == null) {
            return paths;
        }
        LinkedBlockingDeque<SidePath> sidePathLinkedBlockingDeque = new LinkedBlockingDeque<>(sidePaths);

        if (!org.springframework.util.CollectionUtils.isEmpty(sidePathLinkedBlockingDeque)) {
            for (SidePath sidePath : sidePathLinkedBlockingDeque) {
                String pathId = sidePath.getPathId();
                if (pathId != null) {
                    Path path = MapGraphUtil.getPathByPathId(pathId);
                    if (path != null) {
                        paths.add(path);
                    }
                }
            }
        }
        return paths;
    }

    @Override
    public ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToExecutedSidePaths() {
        return getAgvToSidePaths(agvToExecutedSidePaths);
    }

    @Override
    public ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToRunningSidePaths() {
        return getAgvToSidePaths(agvToRunningSidePaths);
    }

    @Override
    public ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToPlanedSidePaths() {
        return getAgvToSidePaths(agvToPlanedSidePaths);
    }

    @Override
    public ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToTotalSidePaths() {
        return getAgvToSidePaths(agvToTotalSidePaths);
    }

    private ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> getAgvToSidePaths(ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvSidePaths) {
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> map = new ConcurrentHashMap<>();
        for (Map.Entry<String, LinkedBlockingDeque<SidePath>> stringLinkedBlockingDequeEntry : agvSidePaths.entrySet()) {
            String agvCode = stringLinkedBlockingDequeEntry.getKey();
            LinkedBlockingDeque<SidePath> sidePaths = stringLinkedBlockingDequeEntry.getValue();
            map.put(agvCode, new LinkedBlockingDeque<>(sidePaths));
        }
        return map;
    }

    public String getStartMarkerId(String agvCode) {
        return agvToStartMarkerId.get(agvCode);
    }

    public Map<String, List<String>> getRunningPathMarkerIds() {
        Map<String, List<String>> runningMakers = new HashMap<>();
        for (String key : agvToPlanedSidePaths.keySet()) {
            String agvCode = key;
            List<SidePath> planSidePath = CollectionUtils.arrayToList(agvToPlanedSidePaths.get(agvCode).toArray());
            List<SidePath> runningSidePath = CollectionUtils.arrayToList(agvToRunningSidePaths.get(agvCode).toArray());
            List<String> markerIdList = new ArrayList<>();
            markerIdList.addAll(planSidePath.stream().map(SidePath::getStartMarkerId).collect(Collectors.toList()));
            markerIdList.addAll(planSidePath.stream().map(SidePath::getEndMarkerId).collect(Collectors.toList()));
            markerIdList.addAll(runningSidePath.stream().map(SidePath::getStartMarkerId).collect(Collectors.toList()));
            markerIdList.addAll(runningSidePath.stream().map(SidePath::getEndMarkerId).collect(Collectors.toList()));
            runningMakers.put(agvCode, markerIdList);
        }
        return runningMakers;
    }

    public List<String> getMarkerIdsBySingleAreaMarkerId(String markerId) {
        List<String> markerIds = new ArrayList<>();
        HashSet<String> singleAreaIds = trafficManager.querySingleAreaResourceIdByMarkerId(markerId);
        for (String singleAreaId : singleAreaIds) {
            Set<String> set = trafficManager.queryMarkerIdsBySingleAreaPathResourceId(singleAreaId);
            if (!CollectionUtils.isEmpty(set)) {
                markerIds.addAll(set);
            }
        }
        return markerIds;
    }

    public PathPlanMessage getPathPlanMessage(String vehicleId) {
        PathPlanMessage pathPlanMessage = agvToPathPlanMessage.get(vehicleId);
        if (pathPlanMessage != null) {
            return pathPlanMessage;
        } else {
            return null;
        }
    }

    @Override
    public List<PathPlanMessage> getPathPlanMessageByMapName(String mapName) {
        Collection<PathPlanMessage> planMessages = agvToPathPlanMessage.values();
        if(CollectionUtils.isEmpty(planMessages)){
            return Collections.EMPTY_LIST;
        }
        List<Vehicle> all = defaultVehiclePool.getAll();
        if(CollectionUtils.isEmpty(all)){
            return Collections.EMPTY_LIST;
        }

        List<Vehicle> filterList = all.stream().filter(item -> mapName.equalsIgnoreCase(item.getAgvMapId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filterList)){
            return Collections.EMPTY_LIST;
        }
        List<String> vehicleList = filterList.stream().map(Vehicle::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(vehicleList)){
            return Collections.EMPTY_LIST;
        }
        List<PathPlanMessage> collect = planMessages.stream().filter(item -> markerService.selectById(mapName,item.getAimMarkerId(),false)!=null
        ).collect(Collectors.toList());

        return new ArrayList<>(collect);
    }

    private List<Integer> generateRandomArray(int size) {
        List<Integer> list = new ArrayList<>(size);
        int range = size;
        Random random = new Random(System.currentTimeMillis());
        while (list.size() < size) {
            Integer tmp = random.nextInt(65535) % range;
            if (!list.contains(tmp)) {
                list.add(tmp);
            }
        }
        return list;
    }

    @Override
    public List<Vehicle> getUnMoveVehicles(String mapName)  {
        List<Vehicle> vehicleList = defaultVehiclePool.getAll();
        if (CollectionUtils.isEmpty(vehicleList)) {
            return null;
        }

        List<Vehicle> unMoveVehicles = new ArrayList<>();
        //将所有的需要路径导航到当前地图的小车，停止导航
        for (Vehicle vehicle : vehicleList) {

            String agvCode = vehicle.getId();
            agvCodeLock.computeIfAbsent(agvCode, k -> k);
            synchronized (agvCodeLock.get(agvCode)) {

                PathPlanMessage ppm = agvToPathPlanMessage.get(agvCode);
                if(ppm!=null && ( PathPlanManger.containsAniMarker(ppm.getAimMarkerId()) || PathPlanManger.containsVehicle(vehicle))){
                    //将所有的需要导航到当前地图的小车，导航请求添加到小车上，重新导航
                    if(!CollectionUtils.isEmpty(agvToPlanedSidePaths.get(agvCode))) {
                        if(ppm.getIsCancel()!=null && ppm.getIsCancel() ==1){
                            logger.info("agvCode:[{}],event:[发布地图],content:[当前小车已经取消路径规划，不做重复操作]", agvCode);
                            //已经处于避让
                            continue;
                        }
                        //如果使用电梯，不操作
                        if (vehicle.isUseElevator()){
                            logger.info("agvCode:[{}],event:[发布地图],content:[当前小车使用电梯中，不取消路径规划]", agvCode);
                            continue;
                        }
                        if (null == ppm.getIsCancel() || ppm.getIsCancel() == 0) {
                            //取消路径导航的回调，会删除agvToPathPlanMessage
                            logger.debug("PathPlanManager: agv:{},ppm:{}",vehicle.getId(),vehicle.getPathPlanMessages());
                            vehicle.getPathPlanMessages().addFirst(JSON.parseObject(JSON.toJSONString(ppm),PathPlanMessage.class));
                            ppm.setIsCancel(1);
                            vehicle.cancelPathNavigation();
                        }
                    } else {
                        unMoveVehicles.add(vehicle);
                    }
                } else {
                    unMoveVehicles.add(vehicle);
                }

                //是否有占用电梯
                if(vehicle.isUseElevator()){
                    Set<String> elevatorIds = getElevatorResourcesFromVehicle(vehicle);
                    MapUpdateThread.ElevatorVehicle.put(vehicle.getId(),elevatorIds);
                }
            }
        }
        return unMoveVehicles;
    }

    //获取小车占用的电梯资源
    private Set<String> getElevatorResourcesFromVehicle(Vehicle vehicle){
        if(vehicle==null || !vehicle.isUseElevator()){
            return null;
        }

        VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicle.getId());
        Marker marker = vehicleLocation.getMarker();
        if(marker!=null && marker.getType().equalsIgnoreCase("ELEVATOR_MARKER")){
            HashSet<String> elevatorIdList = elevatorResourcePool.queryElevatorResourceIdByMarkerId(marker.getId());
            return elevatorIdList;
        }

        List<SidePath> sidePaths = vehicleLocation.getSidePaths();
        if(!CollectionUtils.isEmpty(sidePaths)){
            Set<String> markerids = new HashSet<>();
            sidePaths.forEach(sidepath -> {
                markerids.add(sidepath.getStartMarkerId());
                markerids.add(sidepath.getEndMarkerId());
            });

            List<Marker> listByIds = markerService.getListByIds(new ArrayList<>(markerids), true);
            List<Marker> elevatorMarkers = null;
            if(!CollectionUtils.isEmpty(listByIds)){
                elevatorMarkers = listByIds.stream().filter(item ->
                        item.getType().equalsIgnoreCase("ELEVATOR_MARKER")).collect(Collectors.toList());
            }
            marker = CollectionUtils.isEmpty(elevatorMarkers) ? null : elevatorMarkers.get(0);
            if(marker!=null && marker.getType().equalsIgnoreCase("ELEVATOR_MARKER")){
                HashSet<String> elevatorIdList = elevatorResourcePool.queryElevatorResourceIdByMarkerId(marker.getId());
                return elevatorIdList;
            }
        }
        return null;
    }

    //重新申请资源占用，用于不动的小车
    @Override
    public void reApplyResource(Vehicle vehicle){
        if(vehicle==null || StringUtils.isEmpty(vehicle.getId())){
            return;
        }

        String agvCode = vehicle.getId();
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {

            //1、获取定位的资源占用
            VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicle.getId());
            long start = System.currentTimeMillis();
            while (vehicleLocation == null && (System.currentTimeMillis() - start) < 3000) {
                vehicleLocation = locationService.getVehicleLocation(vehicle.getId());

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            //如果小车处于充电中，则定位不准，需要获取上一次充电调度的最后点作为其当前点位
            if(VehicleConstant.TASK_STATUS_CHARGE == vehicle.getWorkStatus()){
                ChargeScheduler chargeScheduler = null;
                if (vehicleLocation == null || (vehicleLocation.getMarker() == null && vehicleLocation.getSidePaths() == null)) {
                    logger.debug("MapUpdateThread: agv:{},content:[当前小车无定位数据，无法申请资源占用]", vehicle.getId());
                    chargeScheduler = chargeSchedulerService.selectLastOne(vehicle.getId());
                }
                Marker lastChargeMarker = null;
                if(chargeScheduler!=null && StringUtils.isNotBlank(chargeScheduler.getChargeId())){
                    lastChargeMarker = markerService.selectById(chargeScheduler.getChargeId());
                }
                if(lastChargeMarker!=null){
                    vehicleLocation = new VehicleLocation();
                    vehicleLocation.setMarker(lastChargeMarker);
                }
            }
            if (vehicleLocation == null || (vehicleLocation.getMarker() == null && vehicleLocation.getSidePaths() == null)) {
                logger.debug("MapUpdateThread: agv:{},content:[当前小车无定位数据，无法申请资源占用]", vehicle.getId());
                return;
            }

            boolean applyFlag = false;

            //2、申请当前点位所占用的资源
            LinkedList<String> applyMarkerIds = new LinkedList<>();
            HashSet<String> applyMarkerCodes = new HashSet<>();
            if (vehicleLocation.getMarker() != null) {
                Marker marker = vehicleLocation.getMarker();
                applyMarkerIds.add(marker.getId());
                applyMarkerCodes.add(marker.getCode());

                Set<String> applyMarkerResourceIds = markerPathResourcePool.queryMarkerPathResourceIdsByMarkerIds(applyMarkerIds);
                applyFlag = markerPathResourcePool.applyPathResources(vehicle.getId(), applyMarkerResourceIds);
                if (!applyFlag) {
                    logger.debug("MapUpdateThread: agv:{},content:[点位资源申请不成功，[{}]", vehicle.getId(), applyMarkerCodes);
                }

            } else {
                List<SidePath> sidePaths = vehicleLocation.getSidePaths();
                if (sidePaths != null && sidePaths.get(0) != null) {
                    applyMarkerIds.add(sidePaths.get(0).getStartMarkerId());
                    applyMarkerIds.add(sidePaths.get(0).getEndMarkerId());

                    Marker startMarker = markerService.selectById(sidePaths.get(0).getStartMarkerId());
                    Marker endMarker = markerService.selectById(sidePaths.get(0).getEndMarkerId());
                    if (startMarker != null) {
                        applyMarkerCodes.add(startMarker.getCode());
                    }
                    if (endMarker != null) {
                        applyMarkerCodes.add(endMarker.getCode());
                    }

                    Set<String> applyMarkerResourceIds = sidePathResourcePool.querySidePathResourceIdsBySidePathIds(Arrays.asList(sidePaths.get(0).getId()));
                    applyFlag = sidePathResourcePool.applyPathResources(vehicle.getId(), applyMarkerResourceIds);
                    if (!applyFlag) {
                        logger.debug("MapUpdateThread: agv:{},content:[路径资源申请不成功，[{}]", vehicle.getId(), applyMarkerCodes);
                    }
                }
            }

            if (applyFlag) {
                Set<String> applySingleAreaResourceIds = singleAreaPathResourcePool.querySingleAreaResourceIdsByMarkerIds(applyMarkerIds);
                applyFlag = singleAreaPathResourcePool.applyPathResources(vehicle.getId(), applySingleAreaResourceIds);
                if (!applyFlag) {
                    logger.debug("MapUpdateThread: agv:{},content:[单机区域资源申请不成功，[{}]", vehicle.getId(), applyMarkerCodes);
                }
            }
            if (applyFlag) {
                Set<String> applyOPathResourceIds = oPathResourcePool.queryOPathResourceIdsByMarkerIds(applyMarkerIds);
                applyFlag = oPathResourcePool.applyPathResources(vehicle.getId(), applyOPathResourceIds);
                if (!applyFlag) {
                    logger.debug("MapUpdateThread: agv:{},content:[一字路资源申请不成功，[{}]", vehicle.getId(), applyMarkerCodes);
                }
            }
            if (applyFlag) {
                Set<String> applyTPathResourceIds = tPathResourcePool.queryTPathResourceIdsByMarkerIds(applyMarkerIds);
                applyFlag = tPathResourcePool.applyPathResources(vehicle.getId(), applyTPathResourceIds);
                if (!applyFlag) {
                    logger.debug("MapUpdateThread: agv:{},content:[T字路资源申请不成功，[{}]", vehicle.getId(), applyMarkerCodes);
                }
            }

            //申请不成功，就释放
            if (!applyFlag) {
                markerPathResourcePool.releaseApplyByAGV(vehicle.getId());
                sidePathResourcePool.releaseApplyByAGV(vehicle.getId());
                singleAreaPathResourcePool.releaseApplyByAGV(vehicle.getId());
                oPathResourcePool.releaseApplyByAGV(vehicle.getId());
                tPathResourcePool.releaseApplyByAGV(vehicle.getId());
                elevatorResourcePool.releaseApplyByAGV( vehicle.getId());
                logger.debug("MapUpdateThread: agv:{},content:[地图更新完成后，由于资源申请不成功，释放已申请资源]", vehicle.getId());
            }
        }
    }

    
  
    
}
