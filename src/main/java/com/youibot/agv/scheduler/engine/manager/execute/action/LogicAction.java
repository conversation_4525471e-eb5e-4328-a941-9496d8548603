package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.exception.AvoidingObstaclesException;
import com.youibot.agv.scheduler.engine.manager.execute.action.attribute.AttributeFactory;
import com.youibot.agv.scheduler.engine.manager.execute.action.attribute.BaseAttribute;
import com.youibot.agv.scheduler.engine.util.MissionActionSortUtils;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionWorkActionParameterService;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;

/**
 * 逻辑action
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019年5月21日 下午2:23:16
 */
public abstract class LogicAction extends DefaultAction {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogicAction.class);

    @Autowired
    private MissionWorkActionService missionWorkActionService;
    @Autowired
    private MissionActionService missionActionService;
    @Autowired
    private MissionWorkService missionWorkService;
    @Autowired
    private MissionWorkActionParameterService missionWorkActionParameterService;

    private Action runningAction;

    /**
     * 执行子任务动作
     *
     * @param subMissionActionIdsStr
     * @throws InterruptedException
     */
    public List<MissionWorkAction> executeSubMissionAction(String subMissionActionIdsStr) throws InterruptedException, IOException {
        List<MissionWorkAction> workActions = new ArrayList<>();
        MissionWorkAction subMissionWorkAction = null;
        try {
            if (Thread.interrupted()) {//如果当前线程被中断了
                throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
            }
            if (StringUtils.isEmpty(subMissionActionIdsStr)) {
                return null;
            }
            MissionWork missionWork = missionWorkService.selectById(missionWorkAction.getMissionWorkId());//获取missionWork
            List<String> subMissionActionIdList = Arrays.asList(subMissionActionIdsStr.split(","));//将字符串ids转为list形式
            List<MissionAction> subMissionActionList = missionActionService.selectByIds(subMissionActionIdList);//获取子任务动作集合
            MissionActionSortUtils.sortMissionActionListBySequence(subMissionActionList);//根据sequence对missionAction进行排序
            //遍历执行子missionAction
            for (MissionAction subMissionAction : subMissionActionList) {
                //查看数据库中是否存在missionWorkAction
                MissionWorkAction missionWorkActionDB = missionWorkActionService.selectByMissionActionIdAndMissionWorkId(subMissionAction.getId(), missionWork.getId());
                String missionWorkActionId = null;
                if (missionWorkActionDB != null) {
                    if (MISSION_WORK_STATUS_SUCCESS.equals(missionWorkActionDB.getStatus())) {
                        workActions.add(missionWorkActionDB);
                        continue;//已经执行成功，跳过该action
                    } else {
                        missionWorkActionId = missionWorkActionDB.getId();
                        //数据库存在该action，但未执行成功，删除该action及parameter数据
                        missionWorkActionService.deleteById(missionWorkActionDB.getId());
                        missionWorkActionParameterService.deleteByMissionWorkActionId(missionWorkActionDB.getId());
                    }
                }
                subMissionWorkAction = missionWorkActionService.createMissionWorkAction(missionWorkActionId, subMissionAction, missionWork);
                missionWorkActionParameterService.addMissionWorkActionParameter(subMissionWorkAction, subMissionAction);//添加parameter数据，运行时参数直到用户设置参数后才返回数据
                runningAction = ActionFactory.createActionExecute(subMissionWorkAction);
                if (runningAction != null) {
                    this.missionWorkActionStatusUpdate(subMissionWorkAction, MISSION_WORK_ACTION_STATUS_RUNNING);//missionWorkAction执行中
                    Map<String, Object> resultDataMap = this.vehicle.executeCommand(runningAction);
                    if (resultDataMap != null) {
                        missionWorkActionService.saveSuccessResult(subMissionWorkAction, resultDataMap);
                    }
                    this.missionWorkActionStatusUpdate(subMissionWorkAction, MISSION_WORK_ACTION_STATUS_SUCCESS);//missionWorkAction执行成功
                }
                workActions.add(missionWorkActionService.selectById(subMissionWorkAction.getId()));
                subMissionWorkAction = null;
                // 每个action执行完成以后，睡眠500ms.
                Thread.sleep(500);
            }
            return workActions;
        } catch (InterruptedException | IOException | AvoidingObstaclesException e) {
            throw e;
        } catch (AGVResultException e) {
            LOGGER.error("agv result error", e);
            if (subMissionWorkAction != null) {//记录agv返回的错误信息
                subMissionWorkAction.setResultCode(e.getResultCode());
                subMissionWorkAction.setResultMessage(e.getErrorMessage());
            }
            this.missionWorkActionStatusFault(subMissionWorkAction, e.getMessage(), ExceptionInfoEnum.AGV_RETURN_CODE.getErrorCode());// AGV编码返回错误
            throw e;
        } catch (Exception e) {
            this.missionWorkActionStatusFault(subMissionWorkAction, e.getMessage(), ExceptionInfoEnum.EXCUTE_ACTIOM.getErrorCode());
            throw e;
        }
    }

    /**
     * 进行比较
     *
     * @param attributeValue
     * @param operator
     * @param value
     * @return
     */
    public boolean compare(Double attributeValue, String operator, Double value) {
        if ("==".equals(operator)) {
            return attributeValue.equals(value);
        } else if (">=".equals(operator)) {
            return attributeValue >= value;
        } else if (">".equals(operator)) {
            return attributeValue > value;
        } else if ("<=".equals(operator)) {
            return attributeValue <= value;
        } else if ("<".equals(operator)) {
            return attributeValue < value;
        } else if ("!=".equals(operator)) {
            return !attributeValue.equals(value);
        } else {
            throw new ExecuteException(MessageUtils.getMessage("action.wrong_value") + " operator = " + operator);
        }
    }

    /**
     * 获取要比较属性的值
     *
     * @param compareAttribute
     * @return
     */
    public Double getAttributeValue(String compareAttribute) {
        BaseAttribute attribute = AttributeFactory.createCompareAttribute(compareAttribute);
        if (attribute == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter") + " attribute is null.");
        }
        Double attributeValue = attribute.getAttributeValue();
        LOGGER.debug("compareAttribute: " + compareAttribute + ", attributeValue：" + attributeValue);
        return attributeValue;
    }

    //修改missionWorkAction状态
    private void missionWorkActionStatusUpdate(MissionWorkAction missionWorkAction, String status) {
        if (missionWorkAction != null) {
            missionWorkAction.setStatus(status);
            missionWorkActionService.updateStatus(missionWorkAction);
        }
    }

    private void missionWorkActionStatusFault(MissionWorkAction missionWorkAction, String message, Integer errorCode) {
        if (missionWorkAction != null) {
            missionWorkAction.setMessage(message);
            missionWorkAction.setStatus(MISSION_WORK_ACTION_STATUS_FAULT);
            missionWorkAction.setErrorCode(errorCode);
            missionWorkActionService.updateStatus(missionWorkAction);
        }
    }
}
