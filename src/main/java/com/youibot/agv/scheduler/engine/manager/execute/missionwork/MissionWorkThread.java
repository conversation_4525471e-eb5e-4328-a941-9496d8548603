package com.youibot.agv.scheduler.engine.manager.execute.missionwork;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.AGVConstant;
import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.engine.exception.MissionWorkException;
import com.youibot.agv.scheduler.engine.util.MissionActionSortUtils;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.MissionConstant.*;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-06-24 16:43
 */
@Service
@Scope("prototype")
public class MissionWorkThread implements Runnable {

    private static final Logger LOGGER = LoggerFactory.getLogger(MissionWorkThread.class);
    protected MissionWork missionWork;
    protected Vehicle vehicle;
    protected Thread thread;

    @Autowired
    private MissionWorkService missionWorkService;
    @Autowired
    private MissionWorkThreadPool missionWorkThreadPool;
    @Autowired
    private ActionsExecute actionsExecute;
    @Autowired
    private MissionActionService missionActionService;

    public MissionWork getMissionWork() {
        return missionWork;
    }

    public void setMissionWork(MissionWork missionWork) {
        this.missionWork = missionWork;
    }

    public void setVehicle(Vehicle vehicle) {
        this.vehicle = vehicle;
    }

    /**
     * start new mission work task.
     * create new thread and execute the mission work.
     *
     * @param missionWork
     */
    public void startWork(MissionWork missionWork, Vehicle vehicle) {
        LOGGER.debug("start mission work, mission work id: " + missionWork.getId() + " agv id :" + vehicle.getId());
        this.missionWork = missionWork;
        this.vehicle = vehicle;
        missionWorkThreadPool.attach(this);
        this.startThread();
    }

    /**
     * Recovery work.
     * if the agv connection to system. we will find un completion mission work. and recovery work.
     *
     * @param missionWork
     */
    public void recoveryWork(MissionWork missionWork) {
        LOGGER.debug("recover mission work, mission work id: " + missionWork.getId()+ "agvId: " + missionWork.getAgvId());
        this.missionWork = missionWork;
        missionWorkThreadPool.attach(this);
        //单机一般不存在网络问题, 目前为适配多机方案, 所有状态都不自动恢复执行, 系统重启则将执行中的任务改为失败
        /*String status = missionWork.getStatus();
        //如果任务状态时开始、运行中、等待输入或者是由于网络异常原因导致的任务失败状态，自动恢复任务执行
        if (MISSION_WORK_STATUS_START.equals(status) || MISSION_WORK_STATUS_RUNNING.equals(status) || MISSION_WORK_STATUS_WAIT_INPUT.equals(status) || MISSION_WORK_STATUS_WAIT.equals(status)) {
            if (VehicleUtils.checkVehicleStatusOnMissionWork() && ABNORMAL_STATUS_NO.equals(vehicle.getAbnormalStatus())) {//检测vehicle的状态是否满足执行任务
                this.startThread();
            }
        }*/
    }

    /**
     * Pause work.
     * <p>
     * send pause command to vehicle. and update mission work status.
     * stop current thread.
     *
     * @throws ActionException
     */
    public void pauseWork() throws ActionException, InterruptedException {
        if (vehicle == null) {
            vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
        }
        try {
            LOGGER.debug("pause mission work, mission work id: " + missionWork.getId() + "agvId: " + missionWork.getAgvId());
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_PAUSE);//将missionWork状态置为暂停中
            this.stopThread();
            this.vehicle.pauseCommand();
        } catch (Exception e) {
            LOGGER.error("pause command error. ", e);
            addExceptionInfo(ExceptionInfoEnum.MISSIONWORK_PAUSE.getErrorCode(), e); //add
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_FAULT);//将missionWork状态置为失败
            throw new MissionWorkException(MessageUtils.getMessage("action.pause_agv_error"));
        }
        missionWorkService.updateStatus(this.missionWork, MISSION_WORK_STATUS_PAUSE);//将missionWork状态置为暂停
    }

    /**
     * resume work.
     * <p>
     * just resume status is pause work. and fault status work. send resume command to vehicle . new thread to run mission work.
     *
     * @throws ActionException
     */
    public void resumeWork() throws ActionException, InterruptedException {
        if (vehicle == null) {
            vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
        }
        if (MISSION_WORK_STATUS_PAUSE.equals(missionWork.getStatus()) || MISSION_WORK_STATUS_FAULT.equals(missionWork.getStatus())) {
            try {
                LOGGER.debug("resume mission work, mission work id: " + missionWork.getId() + "agvId: " + missionWork.getAgvId());
                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_RESUME);//将missionWork状态置为恢复中
                vehicle.stopCommand();//先停止AGV运行中的任务，再开启执行线程重新执行
                LOGGER.debug("stop command success. ");
            } catch (Exception e) {
                LOGGER.error("stop command error. ", e);
                addExceptionInfo(ExceptionInfoEnum.MISSIONWORK_RESUME.getErrorCode(), e); //add
                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_FAULT);//将missionWork状态置为失败
                throw new MissionWorkException(MessageUtils.getMessage("action.stop_agv_error"));
            }
            this.startThread();
        }
    }

    /**
     * Stop work.
     * 1：stop command.
     * 2: stop thread.
     * 3: clean thread pool.
     * 4: set mission work status.
     */
    public void stopWork() throws InterruptedException {
        if (vehicle == null) {
            vehicle = VehicleUtils.getOnlineVehicle(missionWork.getAgvId());
        }
        try {
            LOGGER.debug("stop mission work, mission work id: " + missionWork.getId() + " agvId:" + missionWork.getAgvId());
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_BEING_SHUTDOWN);//将missionWork状态置为停止中
            this.stopThread();
            if (vehicle != null) {
                vehicle.stopCommand();
            }
            LOGGER.debug("stop command success. ");
        } catch (Exception e) {
            LOGGER.warn("stop command error. ", e);
            addExceptionInfo(ExceptionInfoEnum.MISSIONWORK_STOP.getErrorCode(), e); //add
            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_FAULT);//将missionWork状态置为失败
            throw new MissionWorkException(MessageUtils.getMessage("action.stop_agv_error"));
        }
        missionWorkThreadPool.detach(this);
        missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SHUTDOWN);//将missionWork状态置为停止
        if (vehicle != null) {
            //清理路径
            vehicle.setSidePaths(new LinkedList<>());
            vehicle.setWorkStatus(TASK_STATUS_FREE);
            vehicle.setMissionWork(null);
        }
        LOGGER.debug("set mission work status to shutdown");
    }

    public void continueWork() throws ActionException, InterruptedException {
        LOGGER.debug("continue mission work, mission work id: " + missionWork.getId() + "agvId :" + missionWork.getAgvId());
        //将missionWork状态置为运行中，waitAction检测到missionWork状态未运行中时，就会结束action，继续下一个action
        missionWorkService.updateStatus(this.missionWork, MISSION_WORK_STATUS_RUNNING);
    }

    private void startThread() {
        //从vehiclePool取出vehicle（可能出现任务已经绑定但线程未开启但vehicle连接正好断开等情况）
        if (vehicle == null || !MAP_STATUS_NORMAL.equals(vehicle.getMapStatus()) || !AUTO_CONTROL_MODE.equals(vehicle.getControlMode())) {
            //vehicle 断开连接 或者 状态不正确
            LOGGER.error("AGV分配任务成功, 在开启执行线程的时刻状态发生改变：" + vehicle);
            vehicle.setSidePaths(new LinkedList<>());
            missionWorkThreadPool.detach(this);
            vehicle.setWorkStatus(TASK_STATUS_FREE);
            vehicle.setMissionWork(null);
            return;
        }
        this.thread = new Thread(this);
        this.thread.start();
    }

    public void stopThread() {
        if (this.thread != null) {
            this.thread.interrupt();//线程中断
            LOGGER.debug("stop mission work thread, mission work id: " + missionWork.getId() + "agvId :" + missionWork.getAgvId());
            long startTime = System.currentTimeMillis();
            while (this.thread.isInterrupted() || !Thread.State.TERMINATED.equals(thread.getState())) {
                //在部分第三方库(如mqtt)处理InterruptedException会直接吞掉或者转换成其他异常抛出，导致线程还在执行，所以做两重检测
                //①执行线程检测到中断且②线程执行结束；一秒后线程没有执行完成则再中断一次，防止异常被吞掉继续执行等待时间过长。
                if (System.currentTimeMillis() - startTime > 1000) {
                    thread.interrupt();
                    startTime = System.currentTimeMillis();
                }
                //检测对应线程是否已经读取到中断,若线程中已经调用Thread.interrupted()或sleep、wait等方法, thread.isInterrupted()会重新置换为false
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    LOGGER.error("thread sleep error,");
                }
                LOGGER.debug("thread detection uninterrupted");
            }
            LOGGER.debug("thread detection interrupted");
        } else {
            LOGGER.error("current work thread is null ");
        }
    }

    private void addExceptionInfo(int errorCode, Exception e) {
        missionWork.setErrorCode(errorCode);
        missionWork.setMessage(e.getMessage());
    }

    @Override
    public void run() {
        try {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Thread " + Thread.currentThread().getName() + " run sync task message:");
            }
            if (Thread.interrupted()) {//如果当前线程被中断了
                return;
            }
            if (vehicle == null) {
                missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
                missionWork.setMessage(MessageUtils.getMessage("action.vehicle_is_null"));
                missionWork.setErrorCode(ExceptionInfoEnum.PARAMS_EMPTY.getErrorCode());
                missionWorkService.updateStatus(missionWork);
                return;
            }
            // 获取MissionAction列表并排序
            String missionId = missionWork.getMissionId();
            if (!missionId.endsWith("_" + missionWork.getAgvCode())) {
                missionId += "_" + missionWork.getAgvCode();
            }
            List<MissionAction> missionActions = missionActionService.selectByMissionIdAndIsNotSubAction(missionId);
            missionActions = MissionActionSortUtils.sortMissionActionListBySequence(missionActions);
            missionWork.setRemainingTime(RUNTIME_ACTION_TIME * missionActions.size());
            missionWork.setWorkProgress("0");

            missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_RUNNING);//missionWork执行中
            LOGGER.debug(">>>>>>>>运行中>>>>>>>>>>{}", JSONObject.toJSONString(missionWork));
            if (actionsExecute.executeActions(vehicle, missionActions)) {
                missionWork.setRemainingTime(0);
                missionWork.setWorkProgress("100");
                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SUCCESS);//missionWork执行成功
                vehicle.setMissionWork(null);
                vehicle.setWorkStatus(TASK_STATUS_FREE);
                missionWorkThreadPool.detach(this);
            }
        } catch (Exception e) {
            LOGGER.error("任务运行异常",e);
        }
    }

}
