package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.entity.SidePath;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date :Created in 11:51 2020/8/7
 * @Description :加权有向边
 * @Modified By :
 * @Version :
 */
@Getter
public final class UndirectedEdge implements Serializable {

    private static final Logger logger = LoggerFactory.getLogger(UndirectedEdge.class);
    private final String pathId;
    //边的端点，a
    private final String a;
    //边的端点，b
    private final String b;
    private final String aCode;
    private final String bCode;
    //边的权重
    private final Double weightFixed;

    private final double inAngle;
    private final double outAngle;
    private double v;//线速度
    private double w;//角速度
    private double length;//路径长度

    public UndirectedEdge(DirectedEdge directedEdge){
        this.pathId = directedEdge.getPathId();
        this.aCode = directedEdge.getACode();
        this.bCode = directedEdge.getBCode();
        this.a = directedEdge.getA();
        this.b = directedEdge.getB();
        this.length = directedEdge.getLength();
        this.inAngle = directedEdge.getInAngle();
        this.outAngle = directedEdge.getOutAngle();
        this.v = directedEdge.getV();
        this.w = directedEdge.getW();
        this.weightFixed = directedEdge.getOriginWeight();
    }

    public UndirectedEdge(SidePath sidePath, String aCode, String bCode, double v, double w){
        this.pathId = sidePath.getPathId();
        this.aCode = aCode;
        this.bCode = bCode;
        this.a = sidePath.getStartMarkerId();
        this.b = sidePath.getEndMarkerId();
        this.length = sidePath.getLength();
        this.inAngle = sidePath.getInAngle();
        this.outAngle = sidePath.getOutAngle();
        this.v = v;
        this.w = w;
        this.weightFixed = 2 * length / v;
    }

    @Override
    public String toString() {
        return String.format("%s -> %s:(%f)", aCode, bCode, weightFixed);
    }

    public double getWeight() {
        return weightFixed;
    }

    public String getPathId() {
        return pathId;
    }

    public String getACode() {
        return aCode;
    }

    public String getBCode() {
        return bCode;
    }

    public String getA() {
        return a;
    }

    public String getB() {
        return b;
    }

    public double getV() {
        return v;
    }

    public double getW() {
        return w;
    }

    public double getInAngle() {
        return inAngle;
    }

    public double getOutAngle() {
        return outAngle;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof UndirectedEdge))
            return false;
        UndirectedEdge that = (UndirectedEdge) o;
        return Objects.equals(pathId, that.pathId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pathId);
    }

}
