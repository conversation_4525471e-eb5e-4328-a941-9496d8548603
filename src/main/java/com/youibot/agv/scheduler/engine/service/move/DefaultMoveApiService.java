package com.youibot.agv.scheduler.engine.service.move;

import com.youibot.agv.scheduler.abnormal.AbnormalCodeConstant;
import com.youibot.agv.scheduler.abnormal.AbnormalMessageUtils;
import com.youibot.agv.scheduler.engine.entity.MapResourceCache;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathSimulationService;
import com.youibot.agv.scheduler.engine.service.move.thread.CancelPathNavigationThread;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.RequestPath;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.bean.callback.PathPlanResultMessage;
import com.youibot.agv.scheduler.mqtt.service.MqPathPlanService;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.MapConstant.MARKER_TYPE_ELEVATOR;
import static com.youibot.agv.scheduler.constant.PathSimulationConstant.PATH_SIMULATION_STANDBY;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.RESULT_FAIL;
import static com.youibot.agv.scheduler.mqtt.constant.MqResultConstant.RESULT_SUCCESS;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/11/13 11:23
 */
@Service
public class DefaultMoveApiService implements MoveApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultMoveApiService.class);

    @Autowired
    private PathPlanService pathPlanService;
    @Autowired
    private LocationService locationService;
    @Autowired
    private PathNavigationExecute pathNavigationExecute;
    @Autowired
    private AGVPathParamService agvPathParamService;
    @Autowired
    private MqPathPlanService mqPathPlanService;
    @Autowired
    private MissionWorkService missionWorkService;
    @Autowired
    private MarkerService markerService;
    @Autowired
    private AGVMapService agvMapService;
    @Autowired
    private PathSimulationService pathSimulationService;
    @Autowired
    private RequestPathService requestPathService;

    private Map<String, ConcurrentLinkedDeque<PathPlanResultMessage>> agvToPathPlanResultDeque = new ConcurrentHashMap<>();

    public void addPathPlanResult(String agvCode, PathPlanResultMessage pathPlanResultMessage) {
        agvToPathPlanResultDeque.computeIfAbsent(agvCode, k -> new ConcurrentLinkedDeque<>());
        agvToPathPlanResultDeque.get(agvCode).add(pathPlanResultMessage);
    }

    //调用自由导航后立即返回, 不检测执行状态
    @Override
    public void moveToPosition(String ip, Map<String, Object> param) throws IOException {
        LOGGER.debug("ip: + " + ip + " move to position, send data: " + param);
    }

    @Override
    public Map<String, Object> moveRotate(String ip, Map<String, Object> param) throws InterruptedException, IOException {
        LOGGER.debug("ip : " + ip + " send move rotate command.");
        return null;
    }

    @Override
    public Map<String, Object> moveToMarker(String markerId, String missionWorkId, Vehicle vehicle, Map<String, Object> param) throws InterruptedException, IOException {
        Map<String, Object> resultDataMap;
        param = checkMoveToMarkerParams(markerId, vehicle, param);
        LOGGER.debug("调度模式移动到目的点, agv:[{}],markerId:{}", vehicle.getDeviceNumber(), markerId);
        resultDataMap = schedulerMoveToMarker(markerId, missionWorkId, vehicle, param);
        return resultDataMap;
    }

    private Map<String, Object> schedulerMoveToMarker(String markerId, String missionWorkId, Vehicle vehicle, Map<String, Object> param) throws InterruptedException, IOException {
        Map<String, Object> resultDataMap = null;
        LinkedList<SidePath> sidePathList = null;//获取规划路径
        String missionWorkActionId = String.valueOf(param.get("missionWorkActionId"));
        vehicle.setCancelPathNavigation(false);
        CancelPathNavigationThread cancelPathPlanThread = (CancelPathNavigationThread) ApplicationUtils.getBean("cancelPathNavigationThread");
        cancelPathPlanThread.startThread(missionWorkActionId, vehicle);
        try {
            vehicle.setSidePaths(new LinkedList<>());// 清理路径
            //vehicle.setPathPlanResult(null);
            DefaultVehicleStatus.PositionStatus position = vehicle.getDefaultVehicleStatus().getPosition();
            VehicleLocation location = locationService.getLocationSidePath(vehicle.getAGVMapId(), position.getPos_x(), position.getPos_y(), System.currentTimeMillis());
            if (location != null && location.getMarker() != null && location.getMarker().getId().equals(markerId)) {
                return null;
            }
            agvToPathPlanResultDeque.computeIfAbsent(vehicle.getDeviceNumber(), k -> new ConcurrentLinkedDeque<>());
            agvToPathPlanResultDeque.get(vehicle.getDeviceNumber()).clear();
            RequestPath requestPath = requestPathService.save(vehicle.getId(), markerId, missionWorkId, missionWorkActionId);
            mqPathPlanService.applyPath(markerId, vehicle.getDeviceNumber(), missionWorkId, missionWorkActionId);
            LOGGER.debug("applePath AGVCODE:[{}] ,markerId:[{}]", vehicle.getDeviceNumber(), markerId);
            Long startTime = System.currentTimeMillis();
            Integer timeOut = AGVPropertiesUtils.getInt("MQTT.PATH_PLAN_CALLBACK_TIME_OUT");
            while (true) {
                //PathPlanResultMessage pathPlanResult = vehicle.getPathPlanResult();
                PathPlanResultMessage pathPlanResult = agvToPathPlanResultDeque.get(vehicle.getDeviceNumber()).pollLast();
                if (pathPlanResult != null) {
                    agvToPathPlanResultDeque.get(vehicle.getDeviceNumber()).clear();
                    if (markerId.equals(pathPlanResult.getAimMarkerId())) {
                        if (RESULT_SUCCESS.equals(pathPlanResult.getStatus())) {
                            break;
                        } else {
                            LOGGER.error("pathPlanResult:{} ", pathPlanResult);
                            throw new PathPlanException(pathPlanResult.getMessage() != null ? pathPlanResult.getMessage() : MessageUtils.getMessage("vehicle.path_plan_exception"));
                        }
                    }
                }
                if (Math.abs(System.currentTimeMillis() - startTime) > timeOut) {
                    throw new PathPlanException(MessageUtils.getMessage("action.path_planning_time_out"));
                }
                Thread.sleep(200);
            }
            while (true) {
                if (Thread.interrupted()) {
                    //如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }

                //如果小车处于取消中，跳过
                if (vehicle.isCancelPathNavigation()) {
                    continue;
                }

                //用于接收到地图更新信息后，抛出异常，然后清理掉资源，重试该任务
                if(vehicle.getMapUpdateMsg()!=null){//地图发布
                    AbnormalMessageUtils.throwMapUpdateExeception(vehicle);
                }

                // 判断是否到达目标点
                DefaultVehicleStatus.PositionStatus currentPosition = vehicle.getDefaultVehicleStatus().getPosition();
                Boolean isAimMarker = locationService.isAimMarkerReached(vehicle.getAGVMapId(), currentPosition.getPos_x(), currentPosition.getPos_y(), markerId);
                Integer executeState = pathSimulationService.queryPathSimulationState(vehicle.getDeviceNumber());
                if (isAimMarker && (PATH_SIMULATION_STANDBY == executeState)) {
                    Marker marker = MapResourceCache.getMarker(markerId);
                    LOGGER.debug("机器人到达目的地, 任务完成停止移动 AGV:[{}] X:[{}] Y:[{}], marker x:[{}] y:[{}]", vehicle.getDeviceNumber(), position.getPos_x(), position.getPos_y(), marker.getX(), marker.getY());
                    requestPath = requestPathService.selectById(requestPath.getId());
                    requestPath.setEndTime(new Date());
                    requestPathService.update(requestPath);
                    mqPathPlanService.pushPathExecuteResult(vehicle.getDeviceNumber(), sidePathList, RESULT_SUCCESS, null, missionWorkId, missionWorkActionId);
                    return resultDataMap;
                }

                //由于无限重试，会发生中途小车状态变化，需要判断小车状态是否可用
                if(!AUTO_CONTROL_MODE.equals(vehicle.getControlMode())){
                    throw new YOUIFleetException(AbnormalCodeConstant.COMPASS_ACTION_RUN_ERROR.code(),AbnormalCodeConstant.COMPASS_ACTION_RUN_ERROR.msg());
                }else if(!CONNECT_STATUS_SUCCESS.equals(vehicle.getConnectStatus())){
                    throw new YOUIFleetException(AbnormalCodeConstant.COMPASS_ACTION_RUN_ERROR.code(),AbnormalCodeConstant.COMPASS_ACTION_RUN_ERROR.msg());
                }else if(!MAP_STATUS_NORMAL.equals(vehicle.getMapStatus())){
                    throw new YOUIFleetException(AbnormalCodeConstant.COMPASS_ACTION_RUN_ERROR.code(),AbnormalCodeConstant.COMPASS_ACTION_RUN_ERROR.msg());
                }

                LinkedList<SidePath> moveSidePathList = new LinkedList<>(vehicle.getSidePaths());//防止对象指向同一地址, 误删没有执行的路径
                if (moveSidePathList.size() == 0) {
                    Thread.sleep(50);
                    continue;
                }
                sidePathList = new LinkedList<>(moveSidePathList);
                resultDataMap = pathNavigationExecute.moveBySidePath(moveSidePathList, vehicle, missionWorkId, param);
                // 删除以获取的路径
                sidePathList.forEach(sidePath -> {
                    if (!CollectionUtils.isEmpty(vehicle.getSidePaths())) {
                        vehicle.getSidePaths().remove(sidePath);
                    }
                });
            }
        } catch (PathPlanException e) {
            LOGGER.error("applePath error AGVCODE:[{}] ,markerId:[{}]", vehicle.getDeviceNumber(), markerId, e);
            mqPathPlanService.releasePathPlanResource(vehicle.getDeviceNumber());
            throw new ExecuteException(MessageUtils.getMessage("action.path_planning_error") + " " + e.getMessage());
        } catch (InterruptedException e) {
            LOGGER.error("线程被中止...", e);
            mqPathPlanService.pushPathExecuteResult(vehicle.getDeviceNumber(), sidePathList, RESULT_FAIL, e.getMessage(), missionWorkId, missionWorkActionId);
            throw e;
        } catch (YOUIFleetException e) {
            LOGGER.error("路径导航错误...", e);
            mqPathPlanService.releasePathPlanResource(vehicle.getDeviceNumber());
            throw new ActionException(AbnormalCodeConstant.COMPASS_ACTION_RUN_ERROR.code(), e.getMsg());
        } catch (Exception e) {
            LOGGER.error("路径导航错误...", e);
            mqPathPlanService.releasePathPlanResource(vehicle.getDeviceNumber());
            mqPathPlanService.pushPathExecuteResult(vehicle.getDeviceNumber(), sidePathList, RESULT_FAIL, e.getMessage(), missionWorkId, missionWorkActionId);
            throw new ExecuteException(e.getMessage());
        } finally {
            vehicle.setUseElevator(false);
            cancelPathPlanThread.stopThread();
        }
    }

    private Map<String, Object> checkMoveToMarkerParams(String markerId, Vehicle vehicle, Map<String, Object> param) {
        if (StringUtils.isEmpty(markerId)) {
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        DefaultVehicleStatus.PositionStatus position = vehicle.getDefaultVehicleStatus().getPosition();
        if (position != null && position.getPos_y() != null && position.getPos_x() != null) {
            Marker currentMarker = locationService.getCurrentMarker(vehicle.getAGVMapId(), position.getPos_x(), position.getPos_y());
            if (currentMarker != null && MARKER_TYPE_ELEVATOR.equals(currentMarker.getType())) {
                throw new ExecuteException(MessageUtils.getMessage("action.agv_at_elevator"));
            }
        }
        //校验目标点是否存在并启用，且不能是电梯点
        Marker marker = MapResourceCache.getMarker(markerId);
        boolean containMarker = false;
        if (marker != null) {
            containMarker = true;
//            String current_map_id = vehicle.getDefaultVehicleStatus().getMap().getCurrent_map_id();
//            if (current_map_id.contains(marker.getAgvMapName())) {
//                containMarker = true;
//            }
        }
        if (!containMarker) {
            throw new ExecuteException("marker=" + markerId + MessageUtils.getMessage("action.marker_is_null_or_disable"));
        }
        if (MARKER_TYPE_ELEVATOR.equals(marker.getType())) {
            throw new ExecuteException(MessageUtils.getMessage("action.aim_marker_not_at_elevator"));
        }
        if (CollectionUtils.isEmpty(param)) {
            param = new HashMap<>();
        }
        return param;
    }


}
