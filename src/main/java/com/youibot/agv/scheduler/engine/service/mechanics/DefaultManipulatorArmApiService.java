package com.youibot.agv.scheduler.engine.service.mechanics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/7/16 16:59
 */
@Service
public class DefaultManipulatorArmApiService extends DefaultApiService implements ManipulatorArmApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultManipulatorArmApiService.class);

    @Value("${AGV_SOCKET.PORT.DEVICE}")
    private Integer devicePort;

    @Value("${AGV_API_CODE.MANIPULATOR_ARM_STATUS_OPERATION}")
    private String manipulatorArmStatusOperationApiCode;

    @Value("${AGV_API_CODE.MANIPULATOR_ARM}")
    private String manipulatorArmControlApiCode;

    @Value("${AGV_API_CODE.MANIPULATOR_ARM_DEMO}")
    private String manipulatorArmDemoControlApiCode;

    @Value("${AGV_API_CODE.MANIPULATOR_ARM_QUERY_STATUS}")
    private String manipulatorArmQueryStatusApiCode;

    /**
     * 机械臂登录
     *
     * @param ip
     * @throws IOException
     */
    @Override
    public void login(String ip, Map<String, Object> param) throws IOException {
        JSONObject sendData = new JSONObject();
        sendData.put("command", 3);//操作类型指令：1：开机，2：关机，3：登录和松刹车，4：登出
        if (param != null) {
            sendData.put("id", param.get("id"));
        }
        LOGGER.debug("ip: " + ip + " manipulatorArm login, send data: " + sendData.toString());
        ActionUtils.sendInstruction(ip, manipulatorArmStatusOperationApiCode, sendData.toString());
    }

    /**
     * 控制机械臂
     *
     * @param ip
     * @param param
     * @throws IOException
     */
    @Override
    public Map<String, Object> control(String ip, Map<String, Object> param) throws IOException, InterruptedException {
        LOGGER.debug("ip: " + ip + " manipulatorArm control, send param: " + JSON.toJSONString(param));
        ActionUtils.sendInstruction(ip, manipulatorArmControlApiCode, JSON.toJSONString(param));
        //查看执行状态
        Map<String, Object> resultMap = ActionUtils.checkActionStatus(ip, (String) param.get("id"));
        return (JSONObject) resultMap.get("feedback");
    }

    /**
     * 查询机械臂状态
     *
     * @param ip
     * @return
     * @throws IOException
     */
    @Override
    public Map<String, Object> queryStatus(String ip) throws IOException {
        return super.execute(ip, devicePort, manipulatorArmQueryStatusApiCode, null);
    }

    /**
     * 查询机械臂状态
     *
     * @param client
     * @return
     * @throws IOException
     */
    @Override
    public Map<String, Object> queryStatus(AGVSocketClient client) throws IOException {
        return super.execute(client, manipulatorArmQueryStatusApiCode, null);
    }

    /**
     * 机械臂示教控制
     *
     * @param ip
     * @param param
     * @throws IOException
     */
    @Override
    public Map<String, Object> demoControl(String ip, Map<String, Object> param) throws IOException {
        LOGGER.debug("ManipulatorArm demo control, ip: " + ip + ", send param: " + JSON.toJSONString(param));
        return super.execute(ip, devicePort, manipulatorArmDemoControlApiCode, param);
    }

}
