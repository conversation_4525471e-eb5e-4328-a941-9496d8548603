package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.youibot.agv.scheduler.engine.manager.modbus.JLibModbusUtils;
import com.youibot.agv.scheduler.engine.manager.modbus.ModbusActionParams;
import com.youibot.agv.scheduler.entity.MissionWorkActionParameter;
import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;
import com.youibot.agv.scheduler.service.MissionWorkActionParameterService;
import com.youibot.agv.scheduler.service.MissionWorkGlobalVariableService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
public class ReadPLCAction extends ModbusAction {

    private Logger LOGGER = LoggerFactory.getLogger(ReadPLCAction.class);

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    @Autowired
    private MissionWorkActionParameterService missionWorkActionParameterService;


    @Override
    protected Map<String, Object> sendCommand() throws InterruptedException, UnknownHostException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        super.faultResendAllCount = 0;
        JSONObject paramJson = this.getParamJson();
        Map<String, Object> map = super.switchFunctionCode(paramJson);
        //如果是读寄存器将读取结果放入到全局变量当中
        if ("READ_PLC".equals(missionWorkAction.getActionType())) {
            List<MissionWorkGlobalVariable> missionWorkGlobalVariables = missionWorkGlobalVariableService.selectByMissionWorkId(missionWorkId);
            List<MissionWorkActionParameter> missionWorkActionParameters = missionWorkActionParameterService.selectByMissionWorkActionId(missionWorkAction.getId());
            MissionWorkActionParameter missionWorkActionParameter = missionWorkActionParameters.stream().filter(parameter -> parameter.getParameterKey().equals("readValue")).findFirst().orElse(null);
            if (missionWorkActionParameter != null) {
                missionWorkGlobalVariables = missionWorkGlobalVariables.stream().filter(globalVariable -> globalVariable.getVariableKey().equals(missionWorkActionParameter.getParameterValue())).collect(Collectors.toList());
                missionWorkGlobalVariables.forEach(missionWorkGlobalVariable -> {
                    String code = paramJson.getString("code");
                    if(code.equals("01") || code.equals("02")){
                        boolean[] readValue = (boolean[]) map.get("result");
                        int value = readValue[0]?1:0;
                        missionWorkGlobalVariable.setVariableValue(String.valueOf(value));
                    }else {
                        int[] readValue = (int[]) map.get("result");
                        missionWorkGlobalVariable.setVariableValue(String.valueOf(readValue[0]));
                    }
                    missionWorkGlobalVariableService.update(missionWorkGlobalVariable);
                });
            }
        }
        return map;
    }

    @Override
    public Map<String, Object> readFunction_01(JSONObject paramJson) throws UnknownHostException, InterruptedException {
        return this.readCoilData(paramJson);
    }

    @Override
    public Map<String, Object> readFunction_02(JSONObject paramJson) throws UnknownHostException, InterruptedException {
        return this.readCoilData(paramJson);
    }

    @Override
    public Map<String, Object> readFunction_03(JSONObject paramJson) throws UnknownHostException, InterruptedException {
        return this.readShortData(paramJson);
    }

    @Override
    public Map<String, Object> readFunction_04(JSONObject paramJson) throws UnknownHostException, InterruptedException {
        return this.readShortData(paramJson);
    }

    @Override
    public String getAPICode() {
        return null;
    }

    private Map<String, Object> readCoilData(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            if (Thread.interrupted()) {//如果当前线程被中断了
                throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
            }
            Map<String, Object> resultData = new HashedMap<>(2);
            ModbusActionParams actionParams = new ModbusActionParams(paramJson);
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
            boolean[] readCoilStatusValue = readCoilStatusValue(modbusMaster, actionParams.getCode(),
                    actionParams.getSlaveId(), actionParams.getStartAddress(), actionParams.getNumberOfBits());
            resultData.put("result", readCoilStatusValue);
            return resultData;
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    private Map<String, Object> readShortData(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        ModbusMaster modbusMaster = null;
        try {
            Map<String, Object> resultData = new HashedMap<>(2);
            if (Thread.interrupted()) {//如果当前线程被中断了
                throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
            }
            ModbusActionParams actionParams = new ModbusActionParams(paramJson);
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
            int[] resultValue = readShortValue(modbusMaster, actionParams.getCode(), actionParams.getSlaveId(), actionParams.getStartAddress(), actionParams.getNumberOfBits());
            resultData.put("result", resultValue);
            return resultData;
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    @Override
    public Map<String, Object> writeFunction_05(JSONObject paramJson) {
        return null;
    }

    @Override
    public Map<String, Object> writeFunction_15(JSONObject paramJson) {
        return null;
    }

    @Override
    public Map<String, Object> writeFunction_06(JSONObject paramJson) {
        return null;
    }

    @Override
    public Map<String, Object> writeFunction_16(JSONObject paramJson) {
        return null;
    }
}
