package com.youibot.agv.scheduler.engine.entity;

import com.youibot.agv.scheduler.entity.Floor;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FloorModbusParam {

    @Column
    @ApiModelProperty(value = "读modbus功能码", position = 7)
    private String readFunctionCode;

    @Column
    @ApiModelProperty(value = "门状态地址", position = 8)
    private Integer statusAddress;

    @Column
    @ApiModelProperty(value = "开门状态值", position = 9)
    private Integer openStatusValue;

    @Column
    @ApiModelProperty(value = "电梯到位地址",position = 15)
    private Integer elevatorArriveAddress;

    @Column
    @ApiModelProperty(value = "关门状态值", position = 10)
    private Integer closeStatusValue;

    @Column
    @ApiModelProperty(value = "写modbus功能码", position = 11)
    private String writeFunctionCode;

    @Column
    @ApiModelProperty(value = "门操作地址", position = 12)
    private Integer operateAddress;

    @Column
    @ApiModelProperty(value = "操作外呼开门值", position = 13)
    private Integer operate_out_open_value;

    @Column
    @ApiModelProperty(value = "操作内呼开门值", position = 14)
    private Integer operate_in_open_value;

    @Column
    @ApiModelProperty(value = "操作关门值", position = 15)
    private Integer operateCloseValue;


    public FloorModbusParam(Floor floor){
        this.readFunctionCode = floor.getReadFunctionCode();
        this.statusAddress = floor.getStatusAddress();
        this.openStatusValue = floor.getOpenStatusValue();
        this.closeStatusValue = floor.getCloseStatusValue();
        this.writeFunctionCode = floor.getWriteFunctionCode();
        this.operateAddress = floor.getOperateAddress();
        this.operate_out_open_value = floor.getOperate_out_open_value();
        this.operate_in_open_value = floor.getOperate_in_open_value();
        this.operateCloseValue = floor.getOperateCloseValue();
        this.elevatorArriveAddress = floor.getElevatorArriveAddress();
    }
}
