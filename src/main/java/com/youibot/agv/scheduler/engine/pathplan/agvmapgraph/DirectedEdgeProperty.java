package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date :Created in 下午1:14 2021/3/3
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class DirectedEdgeProperty {
    private final Double weightFixed;
    private final Double weightUser;
    private final Double weightAuto;

    public DirectedEdgeProperty(DirectedEdge edge) {
        this.weightFixed = edge.getOriginWeight();
        this.weightUser = edge.getWeightUser();
        this.weightAuto = edge.getWeightAuto();
    }

    public Double getWeight() {
        return weightFixed + weightUser + weightAuto;
    }
}
