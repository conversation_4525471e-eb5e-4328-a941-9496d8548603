package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

@Service
@Scope("prototype")
public class CommonAction extends DefaultAction {

    @Override
    public String getAPICode() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.COMMON_ACTION");
    }

}
