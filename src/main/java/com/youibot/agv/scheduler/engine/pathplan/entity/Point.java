package com.youibot.agv.scheduler.engine.pathplan.entity;

import lombok.Data;

import static java.lang.Math.*;

/**
 * 图形化检查中用到的类，代表一个点或者方向向量
 *
 * <AUTHOR> E-mail:l<PERSON><PERSON><PERSON>@youibot.com
 * @version CreateTime:2019/6/11 17:44
 */
@Data
public class Point {
    private Double x;
    private Double y;

    public Point(Double x, Double y) {
        this.x = x;
        this.y = y;
    }

    //获得点的范数
    public Double getNorm() {
        return sqrt(x * x + y * y);
    }

    //加法
    public Point plus(Point p) {
        return new Point(this.x + p.getX(), this.y + p.getY());
    }

    //减法
    public Point minus(Point p) {
        return new Point(this.x - p.getX(), this.y - p.getY());
    }

    //乘常数k
    public Point multiply(double k) {
        return new Point(this.x * k, this.y * k);
    }

    //方向向量与x轴夹角(弧度表示方法)
    public Double getAngle() {
        if (x == null || y == null) {
            return null;
        }
        if (x == 0 && y == 0) {
            return 0D;
        } else if (x == 0 && y != 0) {
            return y > 0 ? PI / 2 : -PI / 2;
        } else if (x != 0) {
            return atan2(y, x);
        }
        return null;
    }
}