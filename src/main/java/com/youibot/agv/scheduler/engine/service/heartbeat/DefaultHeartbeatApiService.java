package com.youibot.agv.scheduler.engine.service.heartbeat;

import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/9/10 11:47
 */
@Service
public class DefaultHeartbeatApiService extends DefaultApiService implements HeartbeatApiService {

    @Value("${AGV_SOCKET.PORT.HEARTBEAT}")
    private Integer heartbeatPort;

    @Value("${AGV_API_CODE.HEARTBEAT_CONNECTION}")
    private String heartbeatConnectionApiCode;//心跳连接apiCode

    /**
     * 心跳连接
     * @param client
     * @throws IOException
     */
    @Override
    public void heartbeat(AGVSocketClient client) throws IOException {
        super.execute(client, heartbeatConnectionApiCode, null);
    }

    @Override
    public void heartbeat(String ip) throws IOException {
        super.execute(ip, heartbeatPort, heartbeatConnectionApiCode, null);
    }

}
