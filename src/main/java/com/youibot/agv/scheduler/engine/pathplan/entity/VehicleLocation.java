package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import lombok.Data;

import java.util.List;

/**
 * @version V1.0
 * @Title:
 * @ClassName: com.youibot.agv.scheduler.engine.pathplan.entity
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/11/7 21:37
 */
@Data
public class VehicleLocation {

    private Long updateTimeMillis;

    private List<SidePath> sidePaths;

    private Marker marker;

}
