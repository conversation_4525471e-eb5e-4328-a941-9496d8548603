package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.List;

/**
 * @version V1.0
 * @Title:
 * @ClassName: com.youibot.agv.scheduler.engine.pathplan.entity
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/11/7 21:37
 */
@Data
public class VehicleLocation {

    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleLocation.class);

    private Long updateTimeMillis;

    private List<SidePath> sidePaths;

    private Marker marker;

    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            stringBuilder.append("AGV定位 location:");
            if (!CollectionUtils.isEmpty(sidePaths)){
                for (SidePath sidePath : sidePaths) {
                    String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                    String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                    String format = MessageFormat.format("side path: [{0}]->[{1}], t0:[{2}]", startCode, endCode, sidePath.getT0());
                    stringBuilder.append(format);
                }
            }
            if (marker != null) {
                String format = MessageFormat.format("marker:[{0}]", marker.getCode());
                stringBuilder.append(format);
            }
        } catch (Exception e) {
            LOGGER.error("托马斯的toString出错,", e);
        }
        return stringBuilder.toString();
    }
}
