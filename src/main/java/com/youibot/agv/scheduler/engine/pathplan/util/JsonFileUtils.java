package com.youibot.agv.scheduler.engine.pathplan.util;

import com.alibaba.fastjson.JSON;

import java.io.*;

/**
 * <AUTHOR>
 * @Date :Created in 15:49 2021/12/8
 * @Description :
 * @Modified By :
 * @Version :
 */
public class JsonFileUtils {

    //读取json文件
    public static String readJsonFile(String fileName) {
        try {
            File jsonFile = new File(fileName);
            FileReader fileReader = new FileReader(jsonFile);
            Reader reader = new InputStreamReader(new FileInputStream(fileName), "utf-8");
            int ch = 0;
            StringBuffer sb = new StringBuffer();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            fileReader.close();
            reader.close();
            return sb.toString();
        } catch (IOException e) {
            LogExceptionStackUtil.LogExceptionStack(e);
            return null;
        }
    }

    public static void writeJsonFile(String fileName, Object o) {
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(fileName);
            OutputStreamWriter osw = new OutputStreamWriter(fileOutputStream, "utf-8");
            osw.write(JSON.toJSONString(o));
            osw.flush();
            osw.close();
            fileOutputStream.close();
        } catch (Exception e) {
            LogExceptionStackUtil.LogExceptionStack(e);
        }
    }
}
