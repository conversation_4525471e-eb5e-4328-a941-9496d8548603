package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.google.common.util.concurrent.RateLimiter;
import com.youibot.agv.scheduler.entity.Marker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date :Created in 11:13 2020/10/28
 * @Description :
 * @Modified By :
 * @Version :
 */
public class KDTreeManager {
    private static final Logger logger = LoggerFactory.getLogger(KDTreeManager.class);

    private Map<String, KDTree> agvMapIdToKDTree = new ConcurrentHashMap<>();

    //private final RateLimiter rateLimiter = RateLimiter.create(100.0);

    private KDTreeManager() {
    }

    private static class KDTreeManagerHolder {
        //静态初始化器，由JVM来保证线程安全
        private static KDTreeManager instance = new KDTreeManager();
    }

    public static KDTreeManager getInstance() {
        return KDTreeManagerHolder.instance;
    }

    public void buildKDTree(String agvMapId, Collection<Marker> markers) {
        int size = markers.size();
        KDTree.Data[] data = new KDTree.Data[size];
        int i = 0;
        for (Marker marker : markers) {
            KDTree.Data nodeData = new KDTree.Data();
            nodeData.id = marker.getId();
            nodeData.point = new double[2];
            nodeData.point[0] = marker.getX();
            nodeData.point[1] = marker.getY();
            data[i++] = nodeData;
        }
        KDTree kdTree = KDTree.build(data);
        agvMapIdToKDTree.put(agvMapId, kdTree);
    }

    public void removeKDTree(String agvMapId) {
        if (agvMapId == null) return;
        agvMapIdToKDTree.remove(agvMapId);
    }

    public String query(String agvMapId, Double x, Double y) {
        if (agvMapId == null || x == null || y == null) {
            return null;
        }
        //boolean acquire = rateLimiter.tryAcquire();
        KDTree kdTree = agvMapIdToKDTree.get(agvMapId);
        //if (acquire && kdTree != null && !kdTree.isInit()) {
        if (kdTree != null && !kdTree.isInit()) {
            KDTree.Data queryData = new KDTree.Data();
            queryData.point = new double[2];
            queryData.point[0] = x;
            queryData.point[1] = y;
            KDTree.Data query = kdTree.query(queryData);
            return query.id;
        }
        return null;
    }
}
