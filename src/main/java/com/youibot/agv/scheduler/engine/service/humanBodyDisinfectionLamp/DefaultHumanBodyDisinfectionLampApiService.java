package com.youibot.agv.scheduler.engine.service.humanBodyDisinfectionLamp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Service
public class DefaultHumanBodyDisinfectionLampApiService extends DefaultApiService implements HumanBodyDisinfectionLampApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultHumanBodyDisinfectionLampApiService.class);

    @Value("${AGV_API_CODE.HUMAN_DISINFECT}")
    private String humanBodyDisinfectionLampApiCode;

    @Override
    public Map<String, Object> humanBodyDisinfectionLamp(String ip, Map<String, Object> param) throws IOException, InterruptedException {
        LOGGER.debug("ip : " + ip + " humanBodyDisinfectionLamp send data : " + JSON.toJSONString(param));
        ActionUtils.sendInstruction(ip, humanBodyDisinfectionLampApiCode, JSONObject.toJSONString(param));
        Map<String, Object> resultMap = ActionUtils.checkActionStatus(VehicleUtils.getVehicle().getIp(), (String) param.get("id"));
        return (JSONObject) resultMap.get("feedback");

    }
}
