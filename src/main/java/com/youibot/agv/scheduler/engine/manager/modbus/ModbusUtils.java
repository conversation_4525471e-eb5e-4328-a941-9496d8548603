package com.youibot.agv.scheduler.engine.manager.modbus;

import com.serotonin.modbus4j.ModbusFactory;
import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import com.serotonin.modbus4j.ip.IpParameters;
import com.serotonin.modbus4j.msg.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/6/6 13:49
 */
public class ModbusUtils {

    public static ModbusMaster createModbusMaster(String ip, Integer port) throws ModbusInitException {
        IpParameters params = new IpParameters();
        params.setHost(ip);
        params.setPort(port);
        // params.setEncapsulated(true);
        ModbusMaster modbusMaster = new ModbusFactory().createTcpMaster(params, false);// TCP 协议
        modbusMaster.setTimeout(1000);//设置超时时间
        modbusMaster.setRetries(3);//设置重连次数
        modbusMaster.init();//初始化
        return modbusMaster;
    }

    /**
     *
     * 读boolean类型数据
     * @param modbusMaster
     * @param slaveId
     * @param offset   位置
     * @param numberOfBits   位数
     * @return
     * @throws ModbusTransportException
     */
    public static boolean[] readCoilStatus(ModbusMaster modbusMaster, int slaveId, int offset, int numberOfBits) throws ModbusTransportException,RuntimeException {
        ReadCoilsRequest request = new ReadCoilsRequest(slaveId, offset, numberOfBits);
        ReadCoilsResponse response = (ReadCoilsResponse) modbusMaster.send(request);
        if(response.isException()){
             throw new RuntimeException("读取异常,返回值为空");
        }
        boolean[] booleans = response.getBooleanData();
        return valueRegroup(numberOfBits, booleans);
    }

    /**
     * 读boolean类型数据
     * @param ip    IP地址
     * @param port  端口号
     * @param slaveId
     * @param offset       位置
     * @param numberOfBits 位数
     * @return
     * @throws ModbusTransportException
     * @throws ModbusInitException
     */
    public static boolean[] readCoilStatus(String ip, int port, int slaveId, int offset, int numberOfBits) throws ModbusTransportException, ModbusInitException {
        ModbusMaster modbusMaster = createModbusMaster(ip, port);
        return readCoilStatus(modbusMaster, slaveId, offset, numberOfBits);
    }

    /**
     * 读取结果为int类型的数据
     * @param ip                IP
     * @param port              端口
     * @param slaveId           PLC地址
     * @param offset
     * @param numberOfBits
     * @return
     * @throws ModbusTransportException
     * @throws ModbusInitException
     */
    public static int[] readIntStatus(String ip, int port, int slaveId, int offset, int numberOfBits) throws ModbusTransportException, ModbusInitException,RuntimeException {
        ModbusMaster modbusMaster = createModbusMaster(ip, port);
        ReadCoilsRequest request = new ReadCoilsRequest(slaveId, offset, numberOfBits);
        ReadCoilsResponse response = (ReadCoilsResponse) modbusMaster.send(request);
        if(response.isException()){
            throw new RuntimeException("读取异常");
        }
        boolean[] booleans = response.getBooleanData();
        return valueIntRegroup(numberOfBits, booleans);
    }

    private static boolean[] valueRegroup(int numberOfBits, boolean[] values) {
        boolean[] bs = new boolean[numberOfBits];
        int temp = 1;
        for (boolean b : values) {
            bs[temp - 1] = b;
            temp++;
            if (temp > numberOfBits)
                break;
        }
        return bs;
    }

    /**
     *  将boolean结果转换成int true:1 false:0
     * @param numberOfBits
     * @param values
     * @return
     */
    private static int[] valueIntRegroup(int numberOfBits, boolean[] values) {
        int[] bs = new int[numberOfBits];
        int temp = 1;
        for (boolean b : values) {
            if (b){
                bs[temp - 1] = 1;
            }else {
                bs[temp - 1] = 0;
            }
            temp++;
            if (temp > numberOfBits)
                break;
        }
        return bs;
    }

    /**
     * 写单个boolean类型数据
     * @param modbusMaster
     * @param slaveId
     * @param writeOffset
     * @param writeValue
     * @return
     * @throws ModbusTransportException
     */
    public static boolean writeCoil(ModbusMaster modbusMaster, int slaveId, int writeOffset, boolean writeValue) throws ModbusTransportException {
        // 创建请求
        WriteCoilRequest request = new WriteCoilRequest(slaveId, writeOffset, writeValue);
        // 发送请求并获取响应对象
        WriteCoilResponse response = (WriteCoilResponse) modbusMaster.send(request);
        return !response.isException();
    }

    /**
     * 写单个boolean类型数据
     * @param ip
     * @param port
     * @param slaveId
     * @param writeOffset
     * @param writeValue
     * @return
     * @throws ModbusTransportException
     * @throws ModbusInitException
     */
    public static boolean writeCoil(String ip, int port, int slaveId, int writeOffset, boolean writeValue) throws ModbusTransportException, ModbusInitException {
        ModbusMaster modbusMaster = createModbusMaster(ip, port);
        return writeCoil(modbusMaster, slaveId, writeOffset, writeValue);
    }


    /**
     * 写多个boolean类型数据
     * @param modbusMaster
     * @param slaveId
     * @param startOffset
     * @param values
     * @return
     * @throws ModbusTransportException
     */
    public static boolean writeCoils(ModbusMaster modbusMaster, int slaveId, int startOffset, boolean[] values) throws ModbusTransportException {
        // 创建请求
        WriteCoilsRequest request = new WriteCoilsRequest(slaveId, startOffset, values);
        // 发送请求并获取响应对象
        WriteCoilsResponse response = (WriteCoilsResponse) modbusMaster.send(request);
        return !response.isException();

    }

    /**
     * 写多个boolean类型数据
     * @param ip
     * @param port
     * @param slaveId
     * @param startOffset
     * @param values
     * @return
     * @throws ModbusTransportException
     * @throws ModbusInitException
     */
    public static boolean writeCoils(String ip, int port, int slaveId, int startOffset, boolean[] values) throws ModbusTransportException, ModbusInitException {
        ModbusMaster modbusMaster = createModbusMaster(ip, port);
        return writeCoils(modbusMaster, slaveId, startOffset, values);
    }

    /**
     * 写short类型数组
     * @param ip
     * @param port
     * @param slaveId
     * @param startOffset
     * @param values
     * @return
     * @throws ModbusTransportException
     * @throws ModbusInitException
     */
    public static boolean writeShortCoils(String ip, int port, int slaveId, int startOffset, short[] values) throws ModbusTransportException, ModbusInitException {
        ModbusMaster modbusMaster = createModbusMaster(ip, port);
        WriteRegistersRequest request = new WriteRegistersRequest(slaveId, startOffset, values);
        WriteRegistersResponse response = (WriteRegistersResponse) modbusMaster.send(request);
        return !response.isException();
    }

    /**
     * 写单个06功能码
     * @param slaveId 从站ID
     * @param startOffset   起始地址偏移量
     * @param values  待写数据
     * @throws ModbusInitException
     */
    public static boolean writeIntegerCoils( String ip, int port,int slaveId, int startOffset, int values) throws ModbusTransportException, ModbusInitException{
        ModbusMaster modbusMaster = createModbusMaster(ip, port);
        WriteRegisterRequest request = new WriteRegisterRequest(slaveId, startOffset, values);
        WriteRegisterResponse response = (WriteRegisterResponse) modbusMaster.send(request);
        return !response.isException();
    }

    /**
     *  读多个03功能码
     * @param ip
     * @param port
     * @param slaveId
     * @param offset
     * @param numberOfBits
     * @return
     * @throws ModbusTransportException
     * @throws ModbusInitException
     */
    public static int [] readHoldingRegisters(String ip, int port, int slaveId, int offset, int numberOfBits) throws ModbusTransportException, ModbusInitException {
        ModbusMaster modbusMaster = createModbusMaster(ip, port);
        ReadHoldingRegistersRequest request = new ReadHoldingRegistersRequest(slaveId, offset, numberOfBits);
        ReadHoldingRegistersResponse response = (ReadHoldingRegistersResponse) modbusMaster.send(request);
        short[] responseShortData = response.getShortData();
        if(responseShortData.length >= 0 ){
            int resultData [] ={Integer.valueOf(responseShortData[0])};
            return resultData;
        }
        return null;
    }

}
