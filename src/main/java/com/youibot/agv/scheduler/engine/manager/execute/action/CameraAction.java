package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.camera.CameraApiService;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.ActionConstant.*;
import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.RESULT_TYPE_IMAGE;

/**
 * 摄像头
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/6 14:40
 */
@Service
@Scope("prototype")
public class CameraAction extends ImageAction {

    private static final Logger LOGGER = LoggerFactory.getLogger(CameraAction.class);

    @Value("${AGV_THREAD_CONFIG.WAIT_TIME.AFTER_SETTING_CAMERA_PARAM}")
    protected Integer afterSettingCameraParamWaitTime;//设置摄像头参数后等待完成时间

    private boolean isPtzCamera = false;

    @Autowired
    private CameraApiService cameraApiService;

    @Override
    public Map<String, Object> execute(Vehicle vehicle) throws IOException, InterruptedException {
        LOGGER.debug("mission work name : " + missionWorkAction.getName() + " start execute.");
        super.vehicle = vehicle;
        Map<String, Object> backupParam = new HashMap<>();//初始（拍照结束后要还原的）摄像头参数
        JSONObject paramJson = super.getParamJson();
        if (CAMERA_PARAM_SETTING_YES.equals(paramJson.getInteger("paramSetting"))) {
            backupParam.put("id", paramJson.getString("id"));
            backupParam = cameraApiService.queryParameter(vehicle.getIp(), backupParam);
            paramJson.put("id", paramJson.getString("id"));
            cameraApiService.settingParameter(vehicle.getIp(), paramJson);//设置摄像头参数
            if (isPtzCamera) {
                Thread.sleep(afterSettingCameraParamWaitTime);//设置摄像头参数后等待其完成
            }
        }

        if (CAMERA_PRESET_POINT_SETTING_YES.equals(paramJson.getInteger("presetPointSetting"))) {
            JSONObject presetPointParam = new JSONObject();
            presetPointParam.put("dwPTZPresetCmd", paramJson.get("dwPTZPresetCmd"));
            presetPointParam.put("dwPresetIndex", paramJson.get("dwPresetIndex"));
            presetPointParam.put("id", paramJson.getString("id"));
            cameraApiService.settingPresetPoint(vehicle.getIp(), presetPointParam);//设置预置点参数
        }

        if (CAMERA_FILL_LIGHT_OPEN.equals(paramJson.getInteger("fillLight"))) {
            JSONObject fillLightParam = new JSONObject();
            fillLightParam.put("id", paramJson.getString("id"));
            fillLightParam.put("operation", CAMERA_FILL_LIGHT_OPEN);
            cameraApiService.fillLightControl(vehicle.getIp(), fillLightParam);//打开补关灯
        }

        JSONObject resultMap = new JSONObject();
        Integer photoCount = paramJson.getInteger("photoCount");
        if (photoCount != null && photoCount > 0) {
            JSONArray imageUrlArr = new JSONArray();
            JSONObject photographParam = new JSONObject();
            photographParam.put("id", paramJson.getString("id"));
            //根据count发送拍照指令
            for (int num = 1; num <= photoCount; num++) {
                Map<String, Object> dataMap = cameraApiService.photograph(vehicle.getIp(), photographParam);
                String base64 = (String) dataMap.get("base64");
                //将base64转化成图片保存在项目中,得到图片的存储路径
                String imageFilePath = super.saveImage(base64, AGVPropertiesUtils.getString("IMAGE_SAVE_PATH.CAMERA_FOLDER"));
                //将路径添加在数组中
                imageUrlArr.add(imageFilePath);
            }
            resultMap.put("imageUrl", imageUrlArr);
            resultMap.put("resultDataType", RESULT_TYPE_IMAGE);
            LOGGER.debug("mission action name : " + missionWorkAction.getName() + ", result data : " + resultMap);
        }

        //拍照完成关闭补光灯
        if (CAMERA_FILL_LIGHT_OPEN.equals(paramJson.getInteger("fillLight"))) {
            JSONObject fillLightParam = new JSONObject();
            fillLightParam.put("operation", CAMERA_FILL_LIGHT_CLOSE);
            fillLightParam.put("id", paramJson.getString("id"));
            cameraApiService.fillLightControl(vehicle.getIp(), fillLightParam);//关闭补关灯
        }

        //拍照完成之后将摄像头参数还原
        if (CAMERA_PARAM_SETTING_YES.equals(paramJson.getInteger("paramSetting")) && !CAMERA_IS_RETURN_NO.equals(paramJson.getInteger("is_return"))) {
            backupParam.put("id", paramJson.getString("id"));
            cameraApiService.settingParameter(vehicle.getIp(), backupParam);//还原摄像头参数
            if (isPtzCamera) {
                Thread.sleep(afterSettingCameraParamWaitTime);//设置摄像头参数后等待其完成
            }
        }

        return resultMap;
    }

    @Override
    public String getAPICode() {
        return null;
    }

    public void setPtzCamera(boolean ptzCamera) {
        isPtzCamera = ptzCamera;
    }
}
