package com.youibot.agv.scheduler.engine.service.sensor;

import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/10/16 16:05
 */
@Service
public class DefaultSensorApiService extends DefaultApiService implements SensorApiService {

    @Value("${AGV_SOCKET.PORT.SENSOR}")
    private Integer sensorPort;//连接agv的传感器端口号

    @Value("${AGV_API_CODE.QUERY_LASER_DATA}")
    private String queryLaserDataApiCode;//查询激光数据apiCode

    /**
     * 查询激光数据
     * @param client
     * @return
     */
    @Override
    public Map<String, Object> queryLaserData(AGVSocketClient client) throws IOException {
        return super.execute(client, queryLaserDataApiCode, null);
    }

    /**
     * 查询激光数据
     * @param ip
     * @return
     * @throws IOException
     */
    @Override
    public Map<String, Object> queryLaserData(String ip) throws IOException {
        return super.execute(ip, sensorPort, queryLaserDataApiCode, null);
    }
}
