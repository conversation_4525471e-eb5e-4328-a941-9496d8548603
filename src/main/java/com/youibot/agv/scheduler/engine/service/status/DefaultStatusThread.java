package com.youibot.agv.scheduler.engine.service.status;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.constant.AGVConstant;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.vehicle.DefaultVehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.youibot.agv.scheduler.constant.AGVConstant.CONNECT_STATUS_FAULT;
import static com.youibot.agv.scheduler.constant.AGVConstant.CONNECT_STATUS_SUCCESS;

@Service
public class DefaultStatusThread extends Thread {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultStatusThread.class);

    private DefaultVehicle defaultVehicle;
    private AGVSocketClient client;

    @Value("${AGV_SOCKET.PORT.STATUS}")
    private Integer agvStatusPort;
    @Value("${AGV_THREAD_CONFIG.WAIT_TIME.GET_AGV_STATUS_INTERVAL}")
    private Integer intervalTime;//获取agvStatus时间间隔

    private boolean emc_status = false;

    @Autowired
    private DefaultStatusApiService defaultStatusApiService;

    @Autowired
    private LocationService locationService;

    public void start(DefaultVehicle defaultVehicle) {
        this.defaultVehicle = defaultVehicle;
        this.setPriority(MAX_PRIORITY);
        this.start();
    }

    @Override
    public void run() {
        LOGGER.debug("获取状态线程开启........");
        while (true) {
            try {
                Thread.sleep(intervalTime);
                if (!CONNECT_STATUS_SUCCESS.equals(defaultVehicle.getConnectStatus())) {
                    continue;
                }
                if (client == null) {
                    this.client = AGVSocketClient.createAGVClient(this.defaultVehicle.getIp(), agvStatusPort);
                }
                //参数：报文类型（API编号）     报文数据
                long startTime = System.currentTimeMillis();
                Map<String, Object> dataMap = defaultStatusApiService.getStatus(client);
                long endTime = System.currentTimeMillis();
                if (endTime - startTime > 500) {//查询时间超过500ms
                    LOGGER.error("查询总状态时间超过500ms, time = " + (endTime - startTime));
                    continue;
                }
                DefaultVehicleStatus defaultVehicleStatus = JSON.parseObject(JSON.toJSONString(dataMap), DefaultVehicleStatus.class);//解析在AGV查询的到的状态信息
                if (defaultVehicleStatus != null) {
                    this.setCurrentPos(defaultVehicleStatus);//设置当前站点ID
                    defaultVehicleStatus.getPosition().setUpdate_time_millis(endTime);
                    defaultVehicle.updateDefaultVehicleStatus(defaultVehicleStatus);
                    DefaultVehicleStatus.EmecStatus emecStatus = defaultVehicleStatus.getEmec();
                    if (emecStatus.getEmc_status() == 1 && emecStatus.getEmc_reason() != null && (emecStatus.getEmc_reason().length == 1 && emecStatus.getEmc_reason()[0] == 4)) {
                        if (!this.emc_status) {
                            Long currentTime = System.currentTimeMillis();
                            defaultVehicle.setEmc_time(currentTime);
                            this.emc_status = true;
                        }
                    } else {
                        defaultVehicle.setEmc_time(null);
                        defaultVehicleStatus.getEmec().setEmc_jam_start_time(null);
                        this.emc_status = false;
                    }
                    if (defaultVehicle.getEmc_time() != null) {
                        defaultVehicleStatus.getEmec().setEmc_jam_start_time(defaultVehicle.getEmc_time());
                    }
                    //置信度为0和1时，设置定位状态为未定位
                    if (defaultVehicleStatus.getPosition().getPos_confidence() != null) {
                        Double pos_confidence = defaultVehicleStatus.getPosition().getPos_confidence();
                        if (pos_confidence == 0.0 || pos_confidence == 1.0) {
                            defaultVehicleStatus.getPosition().setPos_islocated(AGVConstant.POS_CONFIDENCE_NO);
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.error("获取AGV总状态出错, ", e);
                defaultVehicle.updateDefaultVehicleStatus(null);
                defaultVehicle.setConnectStatus(CONNECT_STATUS_FAULT);
                if (client != null) {
                    client.stop();
                    client = null;
                }
            }
        }
    }

    /**
     * 设置当前站点Id
     *
     * @param defaultVehicleStatus
     */
    private void setCurrentPos(DefaultVehicleStatus defaultVehicleStatus) {
        try {
            DefaultVehicleStatus.PositionStatus position = defaultVehicleStatus.getPosition();
            if (position.getPos_y() == null || position.getPos_x() == null) {
                defaultVehicleStatus.getPosition().setPos_current_station(null);
                return;
            }
            String currentMarkerId = locationService.queryRecent(defaultVehicle.getAGVMapId(), position.getPos_x(), position.getPos_y());
            if (currentMarkerId != null) {
                //LOGGER.debug("currentMarkerCode:[{}]", MapMatrixDataUtil.getMarkerByMarkerId(currentMarkerId).getCode());
                defaultVehicleStatus.getPosition().setPos_current_station(currentMarkerId);
                VehicleUtils.getVehicle().setLastMarkerId(currentMarkerId);
            }
        } catch (Exception e) {
            LOGGER.error("get current pos error, ", e);
        }
    }

}
