package com.youibot.agv.scheduler.engine.manager.execute.action.attribute;

import com.youibot.agv.scheduler.util.ApplicationUtils;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_ACTION_ATTRIBUTE_QR_ANGLE;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * 逻辑表达式中 比较属性创建工厂
* <AUTHOR>  E-mail:<EMAIL>
* @version CreateTime: 2019年5月24日 下午2:54:22
 */
public class AttributeFactory {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(AttributeFactory.class);

	public static BaseAttribute createCompareAttribute(String compareAttribute) {
		if(StringUtils.isEmpty(compareAttribute)) {
			LOGGER.error("compareAttribute is null.");
            return null;
		}
		switch (compareAttribute) {
			case "BATTERY_VALUE":
				return (BatteryValueAttribute) ApplicationUtils.getBean("batteryValueAttribute");

			case "FREE_TIME":
				return (FreeTimeAttribute) ApplicationUtils.getBean("freeTimeAttribute");
			case  MISSION_ACTION_ATTRIBUTE_QR_ANGLE :
				return (QrAngleAttribute) ApplicationUtils.getBean( QrAngleAttribute.class);
		}
		LOGGER.error("compareAttribute is error. compareAttribute: " + compareAttribute);
		return null;
	}
	
}
