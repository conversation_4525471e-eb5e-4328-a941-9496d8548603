package com.youibot.agv.scheduler.engine.pathplan.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date :Created in 下午7:32 2020/8/21
 * @Description :
 * @Modified By :
 * @Version :
 */
public class ConversionUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConversionUtils.class);

    public static synchronized Point[] conversion(String json) {
        try {
            //需要使用的JSON的parseArray方法，将jsonArray解析为object类型的数组
            JSONArray objects = JSON.parseArray(json);
            Point[] points = new Point[objects.size()];
            //多边形区域建模
            for (int i = 0; i < objects.size(); i++) {
                //通过数组下标取到object，使用强转转为JSONObject，之后进行操作
                JSONObject object = (JSONObject) objects.get(i);
                points[i] = new Point(object.getDouble("x"), object.getDouble("y"));
            }
            return points;
        } catch (Exception e) {
            LOGGER.error("json：" + json + "解析错误！！查看json格式是否正确");
        }
        return null;
    }
}
