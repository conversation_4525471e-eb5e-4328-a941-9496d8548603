package com.youibot.agv.scheduler.engine.service.infrared;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.engine.service.camera.DefaultCameraApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/7 10:17
 */
@Service
public class DefaultInfraredApiService extends DefaultApiService implements InfraredApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultCameraApiService.class);

    @Value("${AGV_API_CODE.QUERY_INFRARED_PARAMETER}")
    private String queryInfraredParameterApiCode;//获取红外参数apiCode

    @Value("${AGV_API_CODE.SETTING_INFRARED_PARAMETER}")
    private String settingInfraredParameterApiCode;//设置红外参数apiCode

    @Value("${AGV_API_CODE.SETTING_INFRARED_PRESET}")
    private String settingInfraredPresetApiCode;//设置红外参数apiCode

    @Value("${AGV_API_CODE.INFRARED_PHOTOGRAPHY}")
    private String infraredPhotographApiCode;//设置红外参数apiCode

    @Value("${AGV_API_CODE.QUERY_INFRARED_TEMPERATURE}")
    private String infraredTemperatureApiCode;//红外测温apiCode

    @Override
    public Map<String, Object> settingParameter(String ip, Map<String, Object> param) throws IOException {
        //获取agv设备原本的摄像头参数值
        Map<String, Object> resultMap = ActionUtils.sendInstruction(ip, queryInfraredParameterApiCode, null);
        LOGGER.debug("ip : " + ip + " query infrared parameter return data : " + JSON.toJSONString(resultMap));
        //设置用户填写的参数值（用户无填写的参数使用agv设备原本数值）
        if (param != null && !param.isEmpty()) {
            for (String key : param.keySet()) {
                if (resultMap.containsKey(key) && !StringUtils.isEmpty(param.get(key))) {
                    resultMap.put(key, param.get(key));
                }
            }
        }
        //发送设置摄像头参数指令
        return ActionUtils.sendInstruction(ip, settingInfraredParameterApiCode, JSON.toJSONString(resultMap));
    }

    @Override
    public Map<String, Object> settingPresetPoint(String ip, Map<String, Object> param) throws IOException {
        return ActionUtils.sendInstruction(ip, settingInfraredPresetApiCode, JSON.toJSONString(param));
    }

    @Override
    public Map<String, Object> queryParameter(String ip, Map<String, Object> param) throws IOException {
        return ActionUtils.sendInstruction(ip, queryInfraredParameterApiCode, JSON.toJSONString(param));
    }

    @Override
    public Map<String, Object> photograph(String ip, Map<String, Object> param) throws IOException {
        return ActionUtils.sendInstruction(ip, infraredPhotographApiCode, JSON.toJSONString(param));
    }

    @Override
    public Map<String, Object> thermometer(String ip, Map<String, Object> param) throws IOException {
        return ActionUtils.sendInstruction(ip, infraredTemperatureApiCode, JSON.toJSONString(param));
    }


}
