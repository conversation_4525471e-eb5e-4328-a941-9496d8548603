package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.controller.v3.PathPlanTestController;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.MarkerPathResource;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.PathResource;
import com.youibot.agv.scheduler.entity.ReportSidePathData;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.listener.event.ReportSidePathDataEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 下午18:23 2020/6/25
 * @Description :
 * @Modified By :
 * @Version :
 */
abstract class PathResourcePool<T extends PathResource> {

    private static final Logger logger = LoggerFactory.getLogger(PathResourcePool.class);

    private volatile Map<String, T> poolEntries = new ConcurrentHashMap<>();

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;
    public synchronized T get(String resourceId) {
        if (StringUtils.isEmpty(resourceId)) {
            return null;
        } else {
            return poolEntries.get(resourceId);
        }
    }

    public synchronized Map<String, T> getPoolEntries() {
        return poolEntries;
    }

    public synchronized void attach(T resource) {
        poolEntries.put(resource.getId(), resource);
    }

    public synchronized void detach(String resourceId) {
        poolEntries.remove(resourceId);
    }

    //绑定申请资源
    public synchronized void bindApplyVehicle(String resourceId, String agvCode) {
        T resource = this.get(resourceId);
        if (resource == null) {
            return;
        }
        resource.setApplyAgvCode(agvCode);
        this.attach(resource);
    }

    //释放申请资源
    public synchronized void releaseApplyVehicle(String resourceId) {
        T resource = this.get(resourceId);
        if (resource == null) {
            return;
        }
        resource.setApplyAgvCode(null);
        this.attach(resource);
    }

    //将该车的申请占用释放
    public synchronized void releaseApplyByAGV(String agvCode) {
        int methodCode = 10;
        for (T pathResource : poolEntries.values()) {
            if (pathResource.getApplyAgvCode() != null && agvCode.equals(pathResource.getApplyAgvCode())) {
                this.releaseApplyVehicle(pathResource.getId());
                printResourceLog(pathResource, agvCode, false, true, methodCode);
            } else {
                printResourceLog(pathResource, agvCode, false, false, methodCode);
            }
        }
    }

    //反向释放，即将除此之外的资源都释放掉
    public synchronized void releaseReversePathResource(String agvCode, String pathResourceId) {
        int methodCode = 20;
        for (T pathResource : poolEntries.values()) {
            if (pathResource.getId().equals(pathResourceId)) continue;
            if (pathResource.getApplyAgvCode() != null && agvCode.equals(pathResource.getApplyAgvCode())) {
                this.releaseApplyVehicle(pathResource.getId());
                printResourceLog(pathResource, agvCode, false, true, methodCode);
            } else {
                printResourceLog(pathResource, agvCode, false, false, methodCode);
            }
        }
    }

    //释放agv的所有资源，包括自身的资源
    public void releasAllPathResource(String agvCode) {
        int methodCode = 20;
        for (T pathResource : poolEntries.values()) {
            if (pathResource.getApplyAgvCode() != null && agvCode.equals(pathResource.getApplyAgvCode())) {
                this.releaseApplyVehicle(pathResource.getId());
                printResourceLog(pathResource, agvCode, false, true, methodCode);
            } else {
                printResourceLog(pathResource, agvCode, false, false, methodCode);
            }
        }
    }

    //反向释放，即将除此之外的资源都释放掉
    public synchronized void releaseReversePathResource(String agvCode, Collection<String> pathResourceIds) {
        int methodCode = 30;
        for (T pathResource : poolEntries.values()) {
            if (pathResourceIds.contains(pathResource.getId())) continue;
            if (pathResource.getApplyAgvCode() != null && agvCode.equals(pathResource.getApplyAgvCode())) {
                this.releaseApplyVehicle(pathResource.getId());
                printResourceLog(pathResource, agvCode, false, true, methodCode);
            } else {
                printResourceLog(pathResource, agvCode, false, false, methodCode);
            }
        }
    }

    public synchronized void releasePathResources(String agvCode, Set<String> pathResourceIds) {
        int methodCode = 40;
        for (String resourceId : pathResourceIds) {
            T pathResource = get(resourceId);
            if (pathResource == null) {
                //TODO 黎江华 修改pathResource为空，导致日志空指针异常
                logger.error("error path resource id pathResource id:[{}]", resourceId);
                continue;
            }
            if (pathResource.getApplyAgvCode() != null && agvCode.equals(pathResource.getApplyAgvCode())) {
                this.releaseApplyVehicle(resourceId);
                printResourceLog(pathResource, agvCode, false, true, methodCode);
            } else if (pathResource.getApplyAgvCode() != null && !agvCode.equals(pathResource.getApplyAgvCode())) {
                printResourceLog(pathResource, agvCode, false, false, methodCode);
            }
        }
    }

    //当该路径资源没有被占用或者占用该路径资源的AGV是这个AGV时，返回True,有一个不能被占用就返回False
    public synchronized boolean queryPathResourceApplyAvailable(String agvCode, Set<String> pathResourceIds) {
        boolean applyAvailable = true;
        for (String pathResourceId : pathResourceIds) {
            T pathResource = get(pathResourceId);
            if (pathResource == null) continue;
            if (pathResource.getApplyAgvCode() == null || agvCode.equals(pathResource.getApplyAgvCode())) {
                continue;
            } else {
                applyAvailable = false;
            }
            if (!applyAvailable) {
                break;
            }
        }
        return applyAvailable;
    }

    //申请占用一组路径资源
    public synchronized boolean applyPathResources(String agvCode, Set<String> pathResourceIds) {
        int methodCode = 50;
        boolean apply = true;
        for (String pathResourceId : pathResourceIds) {
            T pathResource = get(pathResourceId);
            if (pathResource == null) continue;
            if (pathResource.getApplyAgvCode() == null || agvCode.equals(pathResource.getApplyAgvCode())) {
                bindApplyVehicle(pathResourceId, agvCode);
                printResourceLog(pathResource, agvCode, true, true, methodCode);
            } else {
                apply = false;
                printResourceLog(pathResource, agvCode, true, false, methodCode);
            }
        }
        return apply;
    }

    //释放当前机器人对资源的占用，并同时让另一个机器人占用该资源
    public synchronized boolean pathResourceSwap(String agvCode, String pathResourceId, String anotherAgvCode) {
        int methodCode = 60;
        T pathResource = get(pathResourceId);
        if (pathResource == null) return false;
        String applyAgvCode = pathResource.getApplyAgvCode();
        if (applyAgvCode != null && applyAgvCode.equals(agvCode)) {
            printResourceLog(pathResource, agvCode, false, true, methodCode);
            this.bindApplyVehicle(pathResourceId, anotherAgvCode);
            printResourceLog(pathResource, agvCode, true, true, methodCode);
            return true;
        }
        return false;
    }

    private void printResourceLog(T pathResource, String agvCode, boolean applyOrNot, boolean result, int methodCode) {
        try {
            if (pathResource.getClass().equals(MarkerPathResource.class)) {
                MarkerPathResource markerPathResource = (MarkerPathResource) pathResource;
                if (applyOrNot) {
                    if (result) {
                        logger.debug("agvCode:[{}],event:[路径(marker点)申请],content:[{}.apply success, marker:[{}], occupyAgvCode:[{}]]", agvCode, methodCode, markerPathResource.getMarkerCode(),pathResource.getApplyAgvCode());
                    } else {
                        //logger.debug("{}.apply fail agvCode:[{}], marker:[{}], occupyAgvCode:[{}]", methodCode, agvCode, markerPathResource.getMarkerCode(), pathResource.getApplyAgvCode());
                    }
                } else {
                    if (result) {
                        async(agvCode, markerPathResource);


                        logger.debug("agvCode:[{}],event:[路径(marker点)释放],content:[{}.release success, marker:[{}], occupyAgvCode:[{}]]", agvCode, methodCode, markerPathResource.getMarkerCode(),pathResource.getApplyAgvCode());
                    } else {
                        //logger.debug("{}.release fail agvCode:[{}], marker:[{}], occupyAgvCode:[{}]", methodCode, agvCode, markerPathResource.getMarkerCode(), pathResource.getApplyAgvCode());
                    }
                }
            }

            //if (pathResource.getClass().equals(SidePathResource.class)) {
            //    SidePathResource sidePathResource = (SidePathResource) pathResource;
            //    if (applyOrNot) {
            //        if (result) {
            //            logger.debug("{}.apply success agvCode:[{}], path:[{}]->[{}], occupyAgvCode:[{}]", methodCode, agvCode, sidePathResource.getNodeU(), sidePathResource.getNodeV(), pathResource.getApplyAgvCode());
            //        } else {
            //            logger.debug("{}.apply fail agvCode:[{}], path:[{}]->[{}], occupyAgvCode:[{}]", methodCode, agvCode, sidePathResource.getNodeU(), sidePathResource.getNodeV(), pathResource.getApplyAgvCode());
            //        }
            //    } else {
            //        if (result) {
            //            logger.debug("{}.release success agvCode:[{}], path:[{}]->[{}], occupyAgvCode:[{}]", methodCode, agvCode, sidePathResource.getNodeU(), sidePathResource.getNodeV(), pathResource.getApplyAgvCode());
            //        } else {
            //            logger.debug("{}.release fail agvCode:[{}], path:[{}]->[{}], occupyAgvCode:[{}]", methodCode, agvCode, sidePathResource.getNodeU(), sidePathResource.getNodeV(), pathResource.getApplyAgvCode());
            //        }
            //    }
            //}

            //if (pathResource.getClass().equals(SingleAreaPathResource.class)) {
            //    SingleAreaPathResource singleAreaPathResource = (SingleAreaPathResource) pathResource;
            //    if (applyOrNot) {
            //        if (result) {
            //            logger.debug("{}.apply success agvCode:[{}], SingleAreaPathResourceId:[{}], occupyAgvCode:[{}]", methodCode, agvCode, singleAreaPathResource.getId(), pathResource.getApplyAgvCode());
            //        } else {
            //            logger.debug("{}.apply fail agvCode:[{}], SingleAreaPathResourceId:[{}], occupyAgvCode:[{}]", methodCode, agvCode, singleAreaPathResource.getId(), pathResource.getApplyAgvCode());
            //        }
            //    } else {
            //        if (result) {
            //            logger.debug("{}.release success agvCode:[{}], SingleAreaPathResourceId:[{}], occupyAgvCode:[{}]", methodCode, agvCode, singleAreaPathResource.getId(), pathResource.getApplyAgvCode());
            //        } else {
            //            logger.debug("{}.release fail agvCode:[{}], SingleAreaPathResourceId:[{}], occupyAgvCode:[{}]", methodCode, agvCode, singleAreaPathResource.getId(), pathResource.getApplyAgvCode());
            //        }
            //    }
            //}
        } catch (Exception e) {
            logger.error("打印资源申请日志出错", e);
        }
    }

    private void async(String agvCode, MarkerPathResource markerPathResource) {
        CompletableFuture.runAsync(() ->{

            ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> planedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
            ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> runningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
            ;
            PathPlanTestController.AgvSidePath agvSidePath = new PathPlanTestController.AgvSidePath();
            agvSidePath.setAgvName(agvCode);
            LinkedBlockingDeque<SidePath> value = runningSidePaths.get(agvCode);
            LinkedBlockingDeque<SidePath> future = planedSidePaths.get(agvCode);
            value.addAll( future );
            List<SidePath > list = Lists.newArrayList( value) ;
            for (int i = 0; i <list.size() ; i++) {
                SidePath sidePath = list.get( i) ;
                String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                if(org.apache.commons.lang3.StringUtils.isNotBlank( sidePath.getReportType())){
                    ReportSidePathData data = new ReportSidePathData(markerPathResource.getMarkerCode(), sidePath, agvSidePath.getAgvName(), false);
                    agvSidePath.setReportSidePathData(data);
                    agvSidePath.setIndex( i);
                    logger.info("vvcurremtMarkCode:{},endCode:{} report_preamount:{}", startCode, endCode , JSON.toJSONString( data));
                    if( i <= data.getSidePath().getPreAmount()){
                        logger.info("vvcurremtMarkCode:{},endCode:{} report_preamount:{},index:{}", startCode, endCode ,data.getSidePath().getPreAmount(), i);

                        applicationEventPublisher.publishEvent( new ReportSidePathDataEvent(data));

                    }
                    break;
                }
            }
        });

    }


}
