package com.youibot.agv.scheduler.engine.manager.execute.action.attribute;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_ACTION_STATUS_SUCCESS;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.constant.ActionTypeConstant;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
/**
 * 空闲时间 属性
* <AUTHOR>  E-mail:<EMAIL>
* @version CreateTime: 2019年5月24日 下午3:52:51
 */
@Service
public class QrAngleAttribute extends BaseAttribute {

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Override
    public Double getAttributeValue() {

        //获取agv连接时间
        Vehicle vehicle = VehicleUtils.getVehicle();
       MissionWorkAction missionWorkAction = missionWorkActionService.selectLastActionType( vehicle.getMissionWork().getId() , MISSION_WORK_ACTION_STATUS_SUCCESS ,  ActionTypeConstant.QR_CODE_SCANNING );
       
       String resultData = missionWorkAction.getResultData();
       
        JSONObject object = JSON.parseObject(resultData);
        
       
        Double double1 = object.getDouble("Theta");
        String id = object.getString("id");
        if(Objects.isNull(double1) || StringUtils.isBlank(id)) {
        	return double1 ;
        }
		return  Math.abs( double1  );//以秒为单位
    }

}
