package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.constant.MapConstant;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerScope;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.MarkerAllocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.VehicleScopeService;
import com.youibot.agv.scheduler.entity.ChargeScheduler;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.ParkScheduler;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.service.ChargeSchedulerService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.ParkSchedulerService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 8/27/21 8:31 PM
 */
@Service
public class MarkerAllocationServiceImpl implements MarkerAllocationService {

    private static final Logger logger = LoggerFactory.getLogger(MarkerAllocationServiceImpl.class);

    @Autowired
    private MarkerService markerService;

    @Autowired
    private ParkSchedulerService parkSchedulerService;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private FullLocationService fullLocationService;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    @Autowired
    private VehicleScopeService vehicleScopeService;

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;


    // 无机器人占用，未被分配，不是他机器人路径规划目标点，不在其他机器人规划的目标点的单机区域内。并且移除掉自己的路径规划目标点。
    @Override
    public List<Marker> getVehicleEnablePark(Vehicle vehicle, String agvMapId) {
        //加入是否可以跨地图泊车代码，默认只能获取机器人当前所在地图的泊车点
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        List<Marker> parkMarkers = new ArrayList<>();
        if (schedulerConfig.getAcrossParkEnable() == 0) {
            if (vehicle.isBindParkConfig()) {
                //如果小车绑定了当前地图所在的泊车点，则过滤掉其他未绑定的泊车点
                if (!vehicle.getAllowParkMarkerIds().keySet().contains(agvMapId)) {
                    return null;
                }
                parkMarkers = markerService.selectParkMarkerByCodes(agvMapId, vehicle.getAllowParkMarkerIds().get(agvMapId), false);
            } else {
                parkMarkers = markerService.selectEnableParkMarkersByMapId(agvMapId);
            }
        } else {
            if (vehicle.isBindParkConfig()) {
                for (String mapId : vehicle.getAllowParkMarkerIds().keySet()) {
                    List<Marker> markers = markerService.selectParkMarkerByCodes(mapId, vehicle.getAllowParkMarkerIds().get(mapId), false);
                    if (!CollectionUtils.isEmpty(markers)) {
                        parkMarkers.addAll(markers);
                    }
                }
            } else {
                parkMarkers = markerService.selectEnableParkMarkers();
            }
        }
        if (CollectionUtils.isEmpty(parkMarkers)) {
            return null;
        }
        // 过滤已分配的park点。
        List<ParkScheduler> parkSchedulers = parkSchedulerService.selectRunning();
        for (ParkScheduler parkScheduler : parkSchedulers) {
            if (parkScheduler.getVehicleId().equals(vehicle.getId())) {
                continue;
            }
            parkMarkers = parkMarkers.stream().filter(x -> !x.getId().equals(parkScheduler.getParkId())).collect(Collectors.toList());
        }
        // 过滤掉其他车占用的站点及关联的单机区域的站点。
        List<String> otherVehicleMarkerIds = this.getOtherVehicleMarkers(vehicle.getId());
        if (!CollectionUtils.isEmpty(otherVehicleMarkerIds)) {
            for (String otherVehicleMarkerId : otherVehicleMarkerIds) {
                parkMarkers = parkMarkers.stream().filter(x -> !x.getId().equals(otherVehicleMarkerId)).collect(Collectors.toList());
            }
        }
        // 过滤车辆路径规划占用的park点。
        List<String> targetMarkerIds = this.getOtherVehicleTargetMarkerIds(vehicle.getId());
        if (!CollectionUtils.isEmpty(targetMarkerIds)) {
            for (String targetMarkerId : targetMarkerIds) {
                parkMarkers = parkMarkers.stream().filter(x -> !x.getId().equals(targetMarkerId)).collect(Collectors.toList());
            }
        }
        return parkMarkers;
    }

    // 获取其他所有的车占用的站点及单机区域关联的站点。。TODO 临时策略，未来需要保存资源分配情况。通过他来过滤已分配的泊车点资源。
    private List<String> getOtherVehicleMarkers(String vehicleId) {
        List<String> markerIds = new ArrayList<>();
        List<Vehicle> vehicles = vehiclePoolService.selectAll();
        for (Vehicle vehicle : vehicles) {
            // 去掉自己的占用的maker.
            if (vehicle.getId().equals(vehicleId)) {
                continue;
            }
            VehicleLocation vehicleLocation = fullLocationService.getVehicleLocation(vehicle.getId());
            if (vehicleLocation != null && vehicleLocation.getMarker() != null) {
                // 添加当前车辆占用的站点及单机区域的站点到列表中。
                markerIds.add(vehicleLocation.getMarker().getId());
                markerIds.addAll(checkAndSendPathService.getMarkerIdsBySingleAreaMarkerId(vehicleLocation.getMarker().getId()));
            }
        }
        return markerIds;
    }

    private List<String> getOtherVehicleTargetMarkerIds(String vehicleId) {
        // 获得所的路径规划的未执行完成的路径点。
        Map<String, List<String>> runningMarkerMap = checkAndSendPathService.getRunningPathMarkerIds();
        // 去掉自己的目标点。
        runningMarkerMap.remove(vehicleId);
        // 加入所有的站点。
        List<String> allRunningMarkerIds = new ArrayList<>();
        for (List<String> markerIds : runningMarkerMap.values()) {
            allRunningMarkerIds.addAll(markerIds);
        }
        List<String> resultMarkerIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allRunningMarkerIds)) {
            // 获得所有路径规划的目标点占用的单机区域中的所有站点。
            for (String targetMarkerId : allRunningMarkerIds) {
                List<String> markerIdsBySingleAreaMarkerId = checkAndSendPathService.getMarkerIdsBySingleAreaMarkerId(targetMarkerId);
                if (!CollectionUtils.isEmpty(markerIdsBySingleAreaMarkerId)) {
                    resultMarkerIds.addAll(markerIdsBySingleAreaMarkerId);
                }
                resultMarkerIds.add(targetMarkerId);
            }
            return resultMarkerIds;
        }
        return null;
    }

    /**
     * @param vehicle
     * @param shelter 是否避难点
     * @return
     */
    public MarkerScope getParkMarkerByVehicle(Vehicle vehicle , boolean shelter) {

        List<Marker> parkMarkers = this.getVehicleEnablePark(vehicle, vehicle.getAgvMapId());
        if (CollectionUtils.isEmpty(parkMarkers)) {
            return null;
        }
        if(shelter) {
        	parkMarkers = parkMarkers.parallelStream().filter( p -> StringUtils.equals(p.getType(), MapConstant.TAKE_REFUGE_MARKER)).collect(Collectors.toList());
        }
        //根据距离获取评分最高的泊车点。
        List<MarkerScope> scopes = new ArrayList<>();
        for (Marker currentMarker : parkMarkers) {
            try {
                /**
                 * 如果评分返回空值,则不分配。
                 * 1：机器人无法导航到目标点。
                 * 2：机器人的定位信息为空。
                 */
            	if(MapConstant.MARKER_TYPE_CHARGING.equals(currentMarker.getType())) {
            		Set<String> runningChargeCodes = chargeSchedulerService.selectRunning().parallelStream().map( ChargeScheduler::getChargePointCode).collect(Collectors.toSet());
            		/**
            		 * 当前泊车点是充电点,并且正有车在充电
            		 */
            		if(runningChargeCodes.contains(currentMarker.getCode())) {
            			continue;
            		}
            	}
                Double distanceScope = vehicleScopeService.scopeByDistance(currentMarker.getId(), vehicle);
                if (distanceScope != null) {
                    scopes.add(new MarkerScope(currentMarker, distanceScope));
                    continue;
                }
            } catch (Exception e) {
                logger.warn("The vehicle scope error.", e);
            }
        }
        // 根据距离分数进行排序。降序排序
        if (!CollectionUtils.isEmpty(scopes)) {
            scopes.sort((o1, o2) -> o2.getScope().compareTo(o1.getScope()));
            return scopes.get(0);
        }
    
    	return  getParkMarkerByVehicle(vehicle) ;
    }
    
    
    /**
     * 通过车辆获取普通的泊车点
     */
    public MarkerScope getParkMarkerByVehicle(Vehicle vehicle)  {
        List<Marker> parkMarkers = this.getVehicleEnablePark(vehicle, vehicle.getAgvMapId());
        if (CollectionUtils.isEmpty(parkMarkers)) {
            return null;
        }
        //根据距离获取评分最高的泊车点。
        List<MarkerScope> scopes = new ArrayList<>();
        for (Marker currentMarker : parkMarkers) {
            try {
                /**
                 * 如果评分返回空值,则不分配。
                 * 1：机器人无法导航到目标点。
                 * 2：机器人的定位信息为空。
                 */
            	if(MapConstant.MARKER_TYPE_CHARGING.equals(currentMarker.getType())) {
            		Set<String> runningChargeCodes = chargeSchedulerService.selectRunning().parallelStream().map( ChargeScheduler::getChargePointCode).collect(Collectors.toSet());
            		/**
            		 * 当前泊车点是充电点,并且正有车在充电
            		 */
            		if(runningChargeCodes.contains(currentMarker.getCode())) {
            			continue;
            		}
            	}
                Double distanceScope = vehicleScopeService.scopeByDistance(currentMarker.getId(), vehicle);
                if (distanceScope != null) {
                    scopes.add(new MarkerScope(currentMarker, distanceScope));
                    continue;
                }
            } catch (Exception e) {
                logger.warn("The vehicle scope error.", e);
            }
        }
        // 根据距离分数进行排序。降序排序
        if (!CollectionUtils.isEmpty(scopes)) {
            scopes.sort((o1, o2) -> o2.getScope().compareTo(o1.getScope()));
            return scopes.get(0);
        }
        return null;
    }


    /**
     * 从充电点中获取评分最高的充电点。如果没有则不分配。
     * 评分规则：距离最短的空闲充电点评分最高。
     * 评分规则：
     * 1：空闲充电点，直接scope+500分。
     * 2：有充电的机器人，但机器电量<CANCEL_CHARGE_BATTERY_VALUE。则不评分。否则scope+充电机器人电量）
     * 3：跟离scope-(距离评分)
     */
    public MarkerScope getChargeMarkerFromAll(Vehicle vehicle) {
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        //配置是否可以获取其他地图的充电点，默认只能获取机器人当前所在地图的充电点
        List<Marker> chargeMarkers = new ArrayList<>();
        Map<String, List<String>> allowChargeMarkerIds = vehicle.getAllowChargeMarkerIds();
        if (schedulerConfig.getAcrossChargeEnable() == 0) {
            //如果小车打开绑定了充电点配置，则查看小车是否有绑定当前地图的充电点
            if (vehicle.isBindChargeConfig()) {
                if (allowChargeMarkerIds.size() == 0 || !allowChargeMarkerIds.containsKey(vehicle.getAgvMapId())) {
                    return null;
                }
                chargeMarkers = markerService.selectByCodes(vehicle.getAgvMapId(), allowChargeMarkerIds.get(vehicle.getAgvMapId()), false);
            } else {
                chargeMarkers = markerService.selectEnableChargeMarkerByMapId(vehicle.getAgvMapId());
            }
        } else {
            if (vehicle.isBindChargeConfig()) {
                if (allowChargeMarkerIds.size() == 0) {
                    return null;
                }
                for (String agvMapId : allowChargeMarkerIds.keySet()) {
                    List<Marker> markers = markerService.selectByCodes(agvMapId, allowChargeMarkerIds.get(agvMapId), false);
                    if (!CollectionUtils.isEmpty(markers)) {
                        chargeMarkers.addAll(markers);
                    }
                }
            } else {
                chargeMarkers = markerService.selectEnableChargeMarker();
            }
        }

        if (CollectionUtils.isEmpty(chargeMarkers)) {
            return null;
        }

        List<MarkerScope> scopes = new ArrayList<>();
        /**
         * 车辆的定位排除
         */
        List<String> except = getExcept(vehicle);
    
        for (Marker chargeMarker : chargeMarkers) {
        	
        	/**
        	 * 充电电点是泊车点，且有车
        	 */
        	 if(except.contains(chargeMarker.getId()) && BooleanUtils.toBoolean( chargeMarker.getIsPark()) ) {
        		logger.debug("vehicle:{},skip_charge_park_point:{}", vehicle.getId(), chargeMarker.getCode() ) ;
        		 continue ; 
        	 }
            ChargeScheduler chargeScheduler = chargeSchedulerService.selectRunningByChargeId(chargeMarker.getId());
            if (chargeScheduler == null) {
                //空闲充电点，直接scope+500分。
                Double distanceScope = vehicleScopeService.scopeByDistance(chargeMarker.getId(), vehicle);
                if (distanceScope != null) {
                    scopes.add(new MarkerScope(chargeMarker, schedulerConfig.getFreeChargeScope() + distanceScope));
                }
            } else {
                //如果当前充电点进行的充电调度为校正充电调度，则不评分
                if (chargeScheduler.getChargeType() == 1) {
                    continue;
                }
                Vehicle v1 = vehiclePoolService.selectById(chargeScheduler.getVehicleId());
                // 有充电的机器人，但机器电量<CANCEL_BATTERY_VALUE。则不评分
                if (v1 != null && v1.getBatteryValue() != null && v1.getBatteryValue() > schedulerConfig.getCancelBatteryValue()) {
                    // 否则scope+充电机器人电量
                    Double batterScope = vehicleScopeService.scopeByBattery(v1, schedulerConfig.getBatteryValueRatio());
                    Double distanceScope = vehicleScopeService.scopeByDistance(chargeMarker.getId(), vehicle);
                    if (batterScope != null && distanceScope != null) {
                        scopes.add(new MarkerScope(chargeMarker, batterScope + distanceScope));
                    }
                }
            }
        }
        // 根据分数进行降序排序。
        if (!CollectionUtils.isEmpty(scopes)) {
            scopes.sort((o1, o2) -> o2.getScope().compareTo(o1.getScope()));
            return scopes.get(0);
        }
        return null;
    }

    /**
     * 已经被其它车辆占用，其它车辆禁止到达
     * @param vehicle
     * @return
     */
	public List<String> getExcept(Vehicle vehicle) {
		List<String> otherVehicleMarkers = getOtherVehicleMarkers(vehicle.getId());
        List<String> vehicleTargetMarkerIds = getOtherVehicleTargetMarkerIds(vehicle.getId());
        List<ParkScheduler> selectRunning = parkSchedulerService.selectRunning();
        List<String> runingParkId = selectRunning.parallelStream().map(ParkScheduler ::getParkId).collect(Collectors.toList()) ;
        List<String> except = Lists.newArrayList();
      if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(runingParkId)) {
        	
        	except.addAll( runingParkId) ;
        	
        	
        }
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(otherVehicleMarkers)) {
        	
        	except.addAll( otherVehicleMarkers) ;
        	
        	
        }
    if(org.apache.commons.collections4.CollectionUtils.isNotEmpty( vehicleTargetMarkerIds)) {
        	
        	except.addAll( vehicleTargetMarkerIds) ;
        	
        	
        }
		return except;
	}

    /**
     * 从所有的空闲的充电点中获取评分最高的充电点。如果没有则不分配。
     * 评分规则：距离最短的空闲充电点评分最高。
     */
    public MarkerScope getChargeMarkerFromEnable(Vehicle vehicle) {
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        // 获取启用的充电点。
        List<Marker> chargeMarkers = new ArrayList<>();
        Map<String, List<String>> allowChargeMarkerIds = vehicle.getAllowChargeMarkerIds();
        if (schedulerConfig.getAcrossChargeEnable() == 0) {
            //如果小车打开绑定了充电点配置，则查看小车是否有绑定当前地图的充电点
            if (vehicle.isBindChargeConfig()) {
                if (allowChargeMarkerIds.size() == 0 || !allowChargeMarkerIds.containsKey(vehicle.getAgvMapId())) {
                    return null;
                }
                chargeMarkers = markerService.selectByCodes(vehicle.getAgvMapId(), allowChargeMarkerIds.get(vehicle.getAgvMapId()), false);
            } else {
                chargeMarkers = markerService.selectEnableChargeMarkerByMapId(vehicle.getAgvMapId());
            }
        } else {
            if (vehicle.isBindChargeConfig()) {
                if (allowChargeMarkerIds.size() == 0) {
                    return null;
                }
                for (String agvMapId : allowChargeMarkerIds.keySet()) {
                    List<Marker> markers = markerService.selectByCodes(agvMapId, allowChargeMarkerIds.get(agvMapId), false);
                    if (!CollectionUtils.isEmpty(markers)) {
                        chargeMarkers.addAll(markers);
                    }
                }
            } else {
                chargeMarkers = markerService.selectEnableChargeMarker();
            }
        }
        // filter scheduler running charge markers.
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            chargeMarkers = chargeMarkers.stream().filter(x -> !x.getId().equals(chargeScheduler.getChargeId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(chargeMarkers)) {
            return null;
        }

        List<MarkerScope> scopes = new ArrayList<>();
        /**
         * 车辆的定位排除
         */
        List<String> except = getExcept(vehicle);
        
     

        //根据距离获取评分最高的充电点。
        List<MarkerScope> markerScopes = new ArrayList<>();

        for (Marker currentMarker : chargeMarkers) {
            try {
            	/**
            	 * 充电电点是泊车点，且有车
            	 */
            	 if(except.contains(currentMarker.getId()) && BooleanUtils.toBoolean( currentMarker.getIsPark()) ) {
            		logger.debug("vehicle:{},skip_charge_park_point:{}", vehicle.getId(), currentMarker.getCode() ) ;
            		 continue ; 
            	 }
            	 
                Double distanceScope = vehicleScopeService.scopeByDistance(currentMarker.getId(), vehicle);
                if (distanceScope != null) {
                    markerScopes.add(new MarkerScope(currentMarker, distanceScope));
                }
            } catch (Exception e) {
                logger.warn("The vehicle scope error.", e);
            }
        }
        // 根据分数进行降序排序。
        if (!CollectionUtils.isEmpty(markerScopes)) {
            markerScopes.sort((o1, o2) -> o2.getScope().compareTo(o1.getScope()));
            return markerScopes.get(0);
        }
        return null;
    }

    /**
     * 根据机器人的定位。获取距离评分最高的避让点。
     *
     * @param vehicle
     * @return
     */
    public MarkerScope getAvoidMarkerFromEnable(Vehicle vehicle) {
        //当机器人所在点位为电梯点，或者在电梯点关联的路径上时，不进行评分
        VehicleLocation vehicleLocation = fullLocationService.getVehicleLocation(vehicle.getId());
        if (null != vehicleLocation) {
            Marker vehicleMarker = vehicleLocation.getMarker();
            List<SidePath> vehicleSidePaths = vehicleLocation.getSidePaths();
            PathPlanMessage pathPlanMessage = checkAndSendPathService.getPathPlanMessage(vehicle.getId());
            if (vehicleMarker != null && MapConstant.MARKER_TYPE_ELEVATOR.equals(vehicleMarker.getType())) {
                logger.debug("机器人{}当前处于电梯点，不参与冲突避让评分", vehicle.getId());
                return null;
            } else if (!CollectionUtils.isEmpty(vehicleSidePaths)) {
                SidePath sidePath = vehicleSidePaths.get(0);
                Marker startMarker = markerService.selectById(sidePath.getAgvMapName(), sidePath.getStartMarkerId(), false);
                Marker endMarker = markerService.selectById(sidePath.getAgvMapName(), sidePath.getEndMarkerId(), false);
                if ((startMarker != null && MapConstant.MARKER_TYPE_ELEVATOR.equals(startMarker.getType())) || (endMarker != null && MapConstant.MARKER_TYPE_ELEVATOR.equals(endMarker.getType()))) {
                    logger.debug("机器人{}当前处于电梯点关联的路径上，不参与冲突避让评分", vehicle.getId());
                    return null;
                }
            } else if (pathPlanMessage != null && "AVOID".equals(pathPlanMessage.getType())) {
                logger.debug("机器人{}去避让点途中, 不参与冲突避让评分", vehicle.getId());
                return null;
            }
        }

        List<Marker> avoidMarkers = markerService.selectEnableAvoidMarkersByMapId(vehicle.getAgvMapId());
        // 过滤掉其他车占用的站点及关联的单机区域的站点。
        List<String> otherVehicleMarkerIds = this.getOtherVehicleMarkers(vehicle.getId());
        if (!CollectionUtils.isEmpty(otherVehicleMarkerIds)) {
            for (String otherVehicleMarkerId : otherVehicleMarkerIds) {
                avoidMarkers = avoidMarkers.stream().filter(x -> !x.getId().equals(otherVehicleMarkerId)).collect(Collectors.toList());
            }
        }
        //过滤掉已经分配的避让点
        List<String> otherVehicleAvoidMarkerIds = this.getOtherVehicleAvoidMarkerIds(vehicle.getId());
        if (!CollectionUtils.isEmpty(otherVehicleAvoidMarkerIds)) {
            avoidMarkers = avoidMarkers.stream().filter(x -> !otherVehicleAvoidMarkerIds.contains(x.getId())).collect(Collectors.toList());
        }
        // 过滤车辆路径规划占用的park点。
        List<String> targetMarkerIds = this.getOtherVehicleTargetMarkerIds(vehicle.getId());
        if (!CollectionUtils.isEmpty(targetMarkerIds)) {
            for (String targetMarkerId : targetMarkerIds) {
                avoidMarkers = avoidMarkers.stream().filter(x -> !x.getId().equals(targetMarkerId)).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(avoidMarkers)) {
            return null;
        }
        //根据距离获取评分最高的避让点。
        List<MarkerScope> markerScopes = new ArrayList<>();

        for (Marker currentMarker : avoidMarkers) {
            try {
                Double distanceScope = vehicleScopeService.scopeByDistance(currentMarker.getId(), vehicle);
                if (distanceScope != null) {
                    markerScopes.add(new MarkerScope(currentMarker, distanceScope));
                }
            } catch (Exception e) {
                logger.warn("The vehicle scope error.", e);
            }
        }
        // 根据分数进行降序排序。
        if (!CollectionUtils.isEmpty(markerScopes)) {
            markerScopes.sort((o1, o2) -> o2.getScope().compareTo(o1.getScope()));
            return markerScopes.get(0);
        }
        return null;
    }

    private List<String> getOtherVehicleAvoidMarkerIds(String vehicleId) {
        List<String> markerIds = new ArrayList<>();
        List<Vehicle> vehicles = vehiclePoolService.selectAll();
        for (Vehicle vehicle : vehicles) {
            // 去掉自己的占用的maker.
            if (vehicle.getId().equals(vehicleId)) {
                continue;
            }
            LinkedList<PathPlanMessage> pathPlanMessages = vehicle.getPathPlanMessages();
            for (PathPlanMessage pathPlanMessage : pathPlanMessages) {
                if ("AVOID".equals(pathPlanMessage.getType())) {
                    markerIds.add(pathPlanMessage.getAimMarkerId());
                }
            }
            PathPlanMessage pathPlanMessage = checkAndSendPathService.getPathPlanMessage(vehicleId);
            if ("AVOID".equals(pathPlanMessage.getType())) {
                markerIds.add(pathPlanMessage.getAimMarkerId());
            }
        }
        return markerIds;
    }
}
