package com.youibot.agv.scheduler.engine.entity;

import com.youibot.agv.scheduler.entity.Elevator;
import lombok.Data;

import static com.youibot.agv.scheduler.constant.ElevatorConstant.ELEVATOR_STATUS_CREATE;

/**
 * 楼层管理
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2020/7/31 10:59
 */
@Data
public class ElevatorFloor {

    public ElevatorFloor() {
    }

    public ElevatorFloor(String missionWorkId, String ip, Integer port, Integer oriFloor, Integer dirFloor, Integer applyTimeout, Integer controlTimeout) {
        this.missionWorkId = missionWorkId;
        this.ip = ip;
        this.port = port;
        this.oriFloor = oriFloor;
        this.dirFloor = dirFloor;
        this.applyTimeout = applyTimeout;
        this.controlTimeout = controlTimeout;
    }

    public ElevatorFloor(String missionWorkId, Integer oriFloor, Integer dirFloor, Elevator elevator, FloorModbusParam oriFloorParam, FloorModbusParam dirFloorParam) {
        this.missionWorkId = missionWorkId;
        this.oriFloor = oriFloor;
        this.dirFloor = dirFloor;
        this.elevatorId = elevator.getId();
        this.ip = elevator.getIp();
        this.port = elevator.getPort();
        this.applyTimeout = elevator.getApplyTimeout();
        this.controlTimeout = elevator.getControlTimeout();
        this.oriFloorParam = oriFloorParam;
        this.dirFloorParam = dirFloorParam;
    }

    private String elevatorId;//电梯ID

    private String missionWorkId;//任务ID

    private String ip;//IP地址

    private Integer port;//端口号

    private Integer oriFloor;//起始楼层

    private Integer dirFloor;//目标楼层

    private Integer elevatorStatus = ELEVATOR_STATUS_CREATE;//电梯状态 1、创建  2、申请成功  3、在呼叫层门打开  4、在呼叫层门关闭  5、移动中  6、在目标层门打开  7、在目标层门关闭  8、已释放  9、异常

    private String message;//异常信息

    private FloorModbusParam oriFloorParam;//起始楼层Modbus操作地址

    private FloorModbusParam dirFloorParam;//目标楼层Modbus操作地址

    private Integer applyTimeout;//申请电梯超时时间 单位：秒

    private Integer controlTimeout;//控制电梯超时时间 单位：秒
}
