package com.youibot.agv.scheduler.engine.service.soundLight;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 声光配置接口
 */
public interface SoundLightConfigService {
    /**
     * 获取声光状态列表
     * @param ip
     * @return
     */
    Map<String,Object> querySoundLightConfig(String ip) throws IOException;

    /**
     * 更新声光状态列表
     * @param ip
     * @param dataJson
     */
    void modifySoundLightConfig(String ip , JSONObject dataJson) throws IOException;

    /**
     * 上传声光
     */
    void uploadSoundLight(String ip, MultipartFile multipartFile, String uploadFileName);

    /**
     * 下载声光
     */
    void downLoadSoundLight(String ip,String fileName,HttpServletResponse response) throws IOException;

    /**
     * 获取声光列表
     */
    Map<String,Object> querySoundLight(String ip) throws IOException;

    /**
     *删除音频文件
     */
    void deleteAudioFile(String ip,String fileName) throws IOException;

    Map<String,Object> queryLightList(String ip);
}
