package com.youibot.agv.scheduler.engine.service.move;

import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019/6/28 14:26
 */
public interface MoveApiService {

    /**
     * 移动至指定marker点（二维码地图使用二维码导航，激光地图使用路径导航）
     *
     * @param markerId
     */
    Map<String, Object> moveToMarker(String markerId, String missionWorkId, Vehicle vehicle, Map<String, Object> param) throws InterruptedException, IOException, PathPlanException;

    /**
     * 移动到指定位置
     *
     * @param ip
     * @param param
     * @return
     */
    void moveToPosition(String ip, Map<String, Object> param) throws IOException;

    /**
     * 原地旋转
     *
     * @param ip
     * @param param
     * @return
     * @throws InterruptedException
     * @throws IOException
     */
    Map<String, Object> moveRotate(String ip, Map<String, Object> param) throws InterruptedException, IOException;


    /**
     * 调度模式下 移动至指定marker点
     * @param markerId
     */
//    Map<String, Object> modeMoveToMarker(String markerId, String missionWorkId, Map<String, Object> param) throws InterruptedException, IOException, PathPlanException;


}
