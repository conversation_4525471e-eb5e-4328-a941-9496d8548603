package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.youibot.agv.scheduler.mqtt.bean.push.pathplan.SidePathMessage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:27 2021/1/14
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class SendSidePathMessage {
    private String agvCode;

    private String sidePathMessageString;

    @Column
    @ApiModelProperty(value = "创建时间", position = 8)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Column
    @ApiModelProperty(value = "创建时间", position = 12)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public SendSidePathMessage(String agvCode, SidePathMessage sidePathMessage) {
        this.agvCode = agvCode;
        this.sidePathMessageString = sidePathMessage.toString();
        this.createTime = new Date();
        this.updateTime = new Date();
    }
}
