package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.entity.MapResourceCache;
import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 移动到指定位置-指定路径导航 动作执行器
 *
 * <AUTHOR>  E-mail:shis<PERSON><EMAIL> 刘擎权 E-mail:<EMAIL>
 * @version CreateTime: 2019年4月17日 下午2:51:13
 */
@Service
@Scope("prototype")
public class MoveSidePathAction extends DefaultAction {

    @Override
    public Map<String, Object> sendCommand() throws IOException, InterruptedException, PathPlanException {
        super.faultResendAllCount = 0;
        JSONObject paramJson = super.getParamJson();
        String markerId = this.getAimMarker(paramJson);//获取目标点
        return vehicle.moveToMarker(markerId, missionWorkId,vehicle ,paramJson);
    }

    private String getAimMarker(JSONObject paramJson) {
        String agvMapId = (String) paramJson.get("agvMapId");//此次moveAction的目标markerId
        String markerCode = paramJson.getString("markerCode");
        if (StringUtils.isEmpty(agvMapId) || StringUtils.isEmpty(markerCode)) {
            throw new ADSParameterException(MessageUtils.getMessage("action.aim_marker_is_empty"));
        }
        Set<Marker> markersByAGVMapId = MapResourceCache.getMarkersByAGVMapId(agvMapId);
        if (CollectionUtils.isEmpty(markersByAGVMapId)) {
            throw new ADSParameterException(MessageUtils.getMessage("service.agv_map_not_enable"));
        }
        //String markerId = MapResourceCache.getAllMarkers().stream().filter(marker -> agvMapId.equals(marker.getAgvMapId()) && markerCode.equals(marker.getCode()))
        //        .map(Marker::getId).collect(Collectors.joining());
        String markerId = null;
        for (Marker marker : markersByAGVMapId) {
            if (marker.getCode().equals(markerCode)) {
                markerId = marker.getId();
                break;
            }
        }
        if (StringUtils.isEmpty(markerId)) {
            throw new ADSParameterException(MessageUtils.getMessage("action.aim_marker_is_empty"));
        }
        if (missionWorkAction != null) {
            paramJson.put("missionWorkActionId", missionWorkAction.getId());
        }
        return markerId;
    }

}
