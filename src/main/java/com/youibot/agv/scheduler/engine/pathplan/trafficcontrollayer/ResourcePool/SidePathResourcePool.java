package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool;

import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.SidePathResource;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date :Created in 下午7:32 2020/8/21
 * @Description :
 * @Modified By :
 * @Version :
 */
@Component
public class SidePathResourcePool extends PathResourcePool<SidePathResource> {

    private static final Logger logger = LoggerFactory.getLogger(SidePathResourcePool.class);

    private Map<String, String> sidePathIdToResourceId = new ConcurrentHashMap<>();

    public void addSidePathResources(Collection<SidePath> sidePaths) {
        if (sidePaths == null) return;
        for (SidePath s : sidePaths) {
            //将所有的路径资源加入到资源缓存中
            SidePathResource sidePathResource = new SidePathResource();
            sidePathResource.setId(s.getId());
            sidePathResource.setSidePathId(s.getId());
            Marker startMarker = MapGraphUtil.getMarkerByMarkerId(s.getStartMarkerId());
            Marker endMarker = MapGraphUtil.getMarkerByMarkerId(s.getEndMarkerId());
            if (startMarker == null || endMarker == null) continue;
            sidePathResource.setNodeUId(startMarker.getId());
            sidePathResource.setNodeVId(endMarker.getId());
            sidePathResource.setNodeU(startMarker.getCode());
            sidePathResource.setNodeV(endMarker.getCode());
            sidePathIdToResourceId.put(s.getId(), sidePathResource.getId());
            super.attach(sidePathResource);
        }
    }

    public void removeSidePathResources(Collection<SidePath> sidePaths) {
        if (sidePaths == null) return;
        sidePaths.forEach(s -> {
            super.detach(s.getId());
            sidePathIdToResourceId.remove(s.getId());
        });
    }

    public Set<String> querySidePathResourceIdsBySidePaths(Collection<SidePath> sidePaths) {
        if (sidePaths == null || sidePaths.size() <= 0) {
            return new HashSet<>();
        }
        Set<String> sidePathIds = sidePaths.stream().map(SidePath::getId).collect(Collectors.toSet());
        Set<String> reverseSidePaths = MapGraphUtil.getReverseSidePathIds(sidePathIds);
        Set<String> temp = new HashSet<>(sidePathIds);
        temp.addAll(reverseSidePaths);
        Set<String> sidePathResourceIds = new HashSet<>();
        for (String s : temp) {
            if (s == null) continue;
            String resourceId = sidePathIdToResourceId.get(s);
            if (resourceId != null) {
                sidePathResourceIds.add(resourceId);
            } else {
                logger.error("sidePathId:[{}]找不到对应的resourceId", s);
            }
        }
        return sidePathResourceIds;
    }

    public Set<String> querySidePathResourceIdsBySidePathIds(Collection<String> sidePathIds) {
        Set<String> reverseSidePathIds = MapGraphUtil.getReverseSidePathIds(sidePathIds);
        Set<String> temp = new HashSet<>(sidePathIds);
        temp.addAll(reverseSidePathIds);
        return temp.stream().map(s -> sidePathIdToResourceId.get(s)).collect(Collectors.toSet());
    }
}
