package com.youibot.agv.scheduler.engine.pathplan.entity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date :Created in 9:30 2020/8/8
 * @Description :
 * @Modified By :
 * @Version :
 */
public class Pair<T, U> implements Cloneable, java.io.Serializable {
    /**
     * One of the two values, for the declared type T.
     */
    private T objectT;
    /**
     * One of the two values, for the declared type U.
     */
    private U objectU;
    private int objectsNull;//0:objectOk,1:object1Null,2:object2Null,3:dualNull

    public Pair(){}

    public Pair(T objectT, U objectU) {

        this.objectT = objectT;
        this.objectU = objectU;
        boolean objectTNull = objectT == null;
        boolean objectUNull = objectU == null;
        if (!objectTNull && !objectUNull) {
            objectsNull = 0;
        } else if (objectTNull && !objectUNull) {
            objectsNull = 1;
        } else if (!objectTNull) {
            objectsNull = 2;
        } else {
            objectsNull = 3;
        }
    }

    public T first() {
        return objectT;
    }

    public T getFirst(){
        return this.first();
    }

    public U second() {
        return objectU;
    }

    public U getSecond(){
        return this.second();
    }

    public void setFirst(T objectT) {
        this.objectT = objectT;
        boolean objectTNull = objectT == null;
        int oN = objectsNull;
        switch (oN) {
            case 0:
            case 1:
                if (objectTNull) objectsNull = 1;
                else objectsNull = 0;
                break;
            case 2:
            case 3:
                if (objectTNull) objectsNull = 3;
                else objectsNull = 2;
                break;
        }
    }

    public void setSecond(U objectU) {
        this.objectU = objectU;
        boolean objectUNull = objectU == null;
        int oN = objectsNull;
        switch (oN) {
            case 0:
            case 2:
                if (objectUNull) objectsNull = 2;
                else objectsNull = 0;
                break;
            case 1:
            case 3:
                if (objectUNull) objectsNull = 3;
                else objectsNull = 1;
                break;
        }
    }

    public Pair<T, U> clone() {
        return new Pair<>(objectT, objectU);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (this == obj) return true;
        if (!(obj instanceof Pair<?, ?>)) return false;
        final Pair<?, ?> that = (Pair<?, ?>) obj;
        if (objectsNull == 3) return objectsNull == that.objectsNull;
        if (that.objectsNull == 3) return false;
        switch (objectsNull) {
            case 1:
                switch (that.objectsNull) {
                    case 1:
                        return objectU.equals(that.objectU);
                    case 2:
                        //Yes: this has non-null part2, other has non-null part1
                        return objectU.equals(that.objectT);
                    default:
                        return false;
                }
            case 2:
                switch (that.objectsNull) {
                    case 1:
                        //Yes: this has non-null part1, other has non-null part2
                        return objectT.equals(that.objectU);
                    case 2:
                        return objectT.equals(that.objectT);
                    default:
                        return false;
                }
            default:
                if (objectT.equals(that.objectT)) return objectU.equals(that.objectU);
                else if (objectT.equals(that.objectU)) return objectU.equals(that.objectT);
                else return false;
        }
    }

    @Override
    public int hashCode() {
        return Objects.hash(objectT, objectU);
    }

    @Override
    public String toString() {
        return String.format("first:{%s}, second:{%s}", objectT.toString(), objectU.toString());
    }
}
