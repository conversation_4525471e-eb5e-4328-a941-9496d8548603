package com.youibot.agv.scheduler.engine.manager.socket;

import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import lombok.Data;

import java.io.ByteArrayOutputStream;
import java.net.ProtocolException;

/**
 * socket连接协议公共数据类
 *
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019年4月10日 下午12:08:30
 */
@Data
public abstract class BasicProtocol {

    //长度均以字节（byte）为单位
    public static final int MESSAGE_HEADER_LEN = 1;   //报文同步头长度
    public static final int VERSION_LEN = 1;       //协议的版本号长度

    public static final int SERIAL_NUMBER_LEN = 2;    //序号长度
    public static final int DATA_LENGTH_LEN = 4;        //数据区长度的长度
    public static final int MESSAGE_TYPE_LEN = 2;       //报文类型（API编号）的长度
    public static final int RESERVE_LEN = 6;            //保留区（用于未来可能的扩展，不必关注保留区的内容，其内容可能不为 0 且可能会发生变化）的长度


    private int messageHeader = 0x02;  // 报文同步头
    private int version = 0x01;         // 版本号
    private int serialNumber; // 序号
    private int dataLength;  // 数据区长度
    private int messageType;  // 报文类型（API编号）
    private long reserve = (long)000000000000;   // 保留区

    public BasicProtocol() {

    }

    public BasicProtocol(int serialNumber,int messageType,int dataLength){
        this.serialNumber = serialNumber;
        this.messageType = messageType;
        this.dataLength = dataLength;
    }

    public int getLength() {
        return MESSAGE_HEADER_LEN + VERSION_LEN + SERIAL_NUMBER_LEN + DATA_LENGTH_LEN + MESSAGE_TYPE_LEN + RESERVE_LEN;
    }

    public abstract String getData();

    /**
     * 拼接发送数据，此处拼接了报文同步头、协议版本号，序号，数据区长度，报文类型（API编号），保留区，数据区内容子类中再拼接
     * 按顺序拼接
     *
     * @return
     */
    public byte[] genContentData() {
        byte[] messageHeader = {(byte) getMessageHeader()};
        byte[] version = {(byte) getVersion()};
        byte[] serialNumber = AGVSocketUtils.int2ByteArrays(this.serialNumber);
        byte[] dataLength = AGVSocketUtils.int2ByteArrays(this.dataLength);
        byte[] messageType = AGVSocketUtils.int2ByteArrays(this.messageType);
        byte[] reserve = AGVSocketUtils.longToByteArray(this.reserve);

        ByteArrayOutputStream baos = new ByteArrayOutputStream(getLength());
        baos.write(messageHeader, messageHeader.length - MESSAGE_HEADER_LEN, MESSAGE_HEADER_LEN);
        baos.write(version, version.length - VERSION_LEN, VERSION_LEN);
        baos.write(serialNumber, serialNumber.length - SERIAL_NUMBER_LEN, SERIAL_NUMBER_LEN);
        baos.write(dataLength, dataLength.length - DATA_LENGTH_LEN, DATA_LENGTH_LEN);
        baos.write(messageType, messageType.length - MESSAGE_TYPE_LEN, MESSAGE_TYPE_LEN);
        baos.write(reserve, reserve.length - RESERVE_LEN, RESERVE_LEN);
        return baos.toByteArray();
    }

    /**
     * 解析出报文同步头
     *
     * @param data
     * @return
     */
    protected int parseMessageHeader(byte[] data) {
        byte m = data[0];//报文同步头放置在数据初始位置，占一个字节
        return m & 0xFF;
    }

    /**
     * 解析出协议版本号
     *
     * @param data
     * @return
     */
    protected int parseVersion(byte[] data) {
        byte v = data[MESSAGE_HEADER_LEN]; //协议版本号，位置在报文同步头后面，占一个字节
        return v & 0xFF;
    }


    /**
     * 解析接收数据，此处解析了协议版本、用于做版本比较，数据区内容子类中解析，其他内容使用到时再做解析使用
     *
     * @param data
     * @return
     * @throws ProtocolException 协议版本不一致，抛出异常
     */
    public int parseContentData(byte[] data) throws AGVResultException {
//        int messageHeader = parseMessageHeader(data);
//        int version = parseVersion(data);
//        if (version != getVersion()) {
//            throw new AGVResultException("input version is error: " + version);
//        }

        return getLength();
    }

    @Override
    public String toString() {
        return "Version: " + getVersion() + ", messageType: " + getMessageType();
    }
}
