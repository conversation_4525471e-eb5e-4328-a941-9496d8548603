package com.youibot.agv.scheduler.engine.manager.socket;

/**
 * AGVSocket 心跳协议类
* <AUTHOR>  E-mail:<EMAIL>
* @version CreateTime: 2019年4月10日 下午12:11:36
 */
public class HeartbeatProtocol extends BasicProtocol {
 
    public static final int PROTOCOL_TYPE = 1;
    
    private int messageType = 00001;  // 报文类型（API编号）
 
	public int getMessageType() {
		return messageType;
	}

	public void setMessageType(int messageType) {
		this.messageType = messageType;
	}

	@Override
	public String getData() {
		return null;
	}
	
}
