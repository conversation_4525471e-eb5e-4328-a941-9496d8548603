package com.youibot.agv.scheduler.engine.service.moveRotate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Service
public class DefaultMoveRotateApiService extends DefaultApiService implements MoveRotateApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultMoveRotateApiService.class);

    @Value("${AGV_API_CODE.MOVE_ROTATE}")
    private String moveRotateApiCode;

    @Override
    public Map<String, Object> moveRotate(String ip, Map<String, Object> param) throws IOException, InterruptedException {
        LOGGER.debug("ip : " + ip + " moveRotate send data : " + JSON.toJSONString(param));
        //查看执行状态
        ActionUtils.sendInstruction(ip, moveRotateApiCode, JSONObject.toJSONString(param));
        Map<String, Object> resultMap = ActionUtils.checkActionStatus(ip, (String) param.get("id"));
        return (JSONObject) resultMap.get("feedback");
    }
}
