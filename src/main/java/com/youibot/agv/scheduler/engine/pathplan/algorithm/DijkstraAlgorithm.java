package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.*;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date :Created in 下午2:55 2020/10/22
 * @Description :
 * @Modified By :
 * @Version :
 */
public class DijkstraAlgorithm {
    private static final Logger logger = LoggerFactory.getLogger(DStarLiteAlgorithm.class);

    private String agvCode;
    private DirectedGraph dGraph;//有向加权图
    private DirectedNode start;//起点
    private DirectedNode goal;//目标点
    private boolean dynamic = true;
    private PriorityQueue<Pair<DirectedNode, Double>> minHeap = new PriorityQueue<>(new DijkstraAlgorithm.NodeComparator());//节点的优先队列
    private Map<DirectedNode, DirectedNodeProperty> nodePropertyMap = new ConcurrentHashMap<>();//节点及其属性映射表
    private Map<DirectedEdge, DirectedEdgeProperty> edgePropertyMap = new ConcurrentHashMap<>();//边及其属性映射表

    public DijkstraAlgorithm(String agvCode, DirectedGraph dGraph) {
        this.agvCode = agvCode;
        this.dGraph = dGraph;
    }

    public void setDynamic(boolean dynamic) {
        this.dynamic = dynamic;
    }

    private void initialize(String startMarkerId, String goalMarkerId) {
        start = dGraph.getNodeById(startMarkerId);
        goal = dGraph.getNodeById(goalMarkerId);
        nodePropertyMap.clear();
        for (DirectedNode node : dGraph.getNodeMap().values()) {
            nodePropertyMap.put(node, new DirectedNodeProperty());
        }
        edgePropertyMap.clear();
        for (DirectedEdge edge : dGraph.getAllEdges()) {
            edgePropertyMap.put(edge, new DirectedEdgeProperty(edge));
        }
    }

    private DirectedNode getParent(DirectedNode node) {
        return nodePropertyMap.get(node).parent;
    }

    private void setParent(DirectedNode node, DirectedNode value) {
        nodePropertyMap.get(node).parent = value;
    }

    private void computeShortestPath() {
        Queue<DirectedNode> S = new LinkedList<>();
        Collection<DirectedNode> nodes = dGraph.getNodeMap().values();
        Map<DirectedNode, Double> dist = new HashMap<>();
        for (DirectedNode node : nodes) {
            if (!node.getId().equals(start.getId())) {
                dist.put(node, Double.POSITIVE_INFINITY);
            } else {
                dist.put(node, 0d);
            }
        }
        minHeap.add(new Pair<>(start, 0d));

        while (minHeap.size() > 0 && !S.contains(goal)) {
            Pair<DirectedNode, Double> poll = minHeap.poll();
            DirectedNode nodeV = poll.first();
            Double vCost = poll.second();
            if (S.contains(nodeV)) continue;
            else {
                S.add(nodeV);
                Set<DirectedNode> adjacentNodes = dGraph.getAdjacentNodes(nodeV);
                for (DirectedNode nodeT : adjacentNodes) {
                    Double vtCost = forwardCost(nodeV, nodeT);
                    Double tCost = dist.get(nodeT);
                    if (vCost + vtCost < tCost) {
                        Double v_vt = vCost + vtCost;
                        dist.put(nodeT, v_vt);
                        minHeap.add(new Pair<>(nodeT, v_vt));
                        if (getParent(nodeV) != null && getParent(nodeV).equals(nodeT)) {
                            setParent(nodeV, null);
                        }
                        setParent(nodeT, nodeV);
                    }
                }
            }
        }
    }

    public MarkerPathResult plan(String startMarkerId, String goalMarkerId) {
        initialize(startMarkerId, goalMarkerId);
        computeShortestPath();
        return getPath1();
    }

    private MarkerPathResult getPath1() {
        MarkerPathResult markerPathResult = new MarkerPathResult();
        markerPathResult.setSidePath(null);
        DirectedNode curr = goal;
        Double cost = 0D;
        LinkedList<String> markerPath = new LinkedList<>();
        while (!curr.equals(start)) {
            markerPath.add(curr.getId());
            if (getParent(curr) != null) {
                cost += this.forwardCost(getParent(curr), curr);
                curr = getParent(curr);
            } else {
                logger.error("目标点无法到达");
                markerPathResult.setCost(cost);
                markerPathResult.setResult(false);
                Collections.reverse(markerPath);
                markerPathResult.markerPath = markerPath;
                return markerPathResult;
            }
        }
        markerPath.add(start.getId());
        Collections.reverse(markerPath);
        markerPathResult.markerPath = markerPath;
        markerPathResult.setCost(cost);
        markerPathResult.setResult(true);
        return markerPathResult;
    }

    private double forwardCost(DirectedNode n1, DirectedNode n2) {
        return dGraph.getForwardCost(n1, getParent(n1), n2, getParent(n2), edgePropertyMap, agvCode, dynamic);
    }

    static class NodeComparator implements Comparator<Pair<DirectedNode, Double>> {
        @Override
        public int compare(Pair<DirectedNode, Double> n1, Pair<DirectedNode, Double> n2) {
            return n1.second() < n2.second() ? -1 : 1;
        }
    }

}
