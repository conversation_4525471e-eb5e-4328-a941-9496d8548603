package com.youibot.agv.scheduler.engine.service.remotePLC;

import java.io.IOException;
import java.util.Map;

public interface RemotePLCApiService {
    /**
     * 读写寄存器
     *
     * @param ip
     * @param param
     * @throws IOException
     */
    Map<String, Object> readPLC(String ip, Map<String, Object> param);

    /**
     * 写寄存器
     *
     * @param ip
     * @param param
     * @throws IOException
     */
    void writePLC(String ip, Map<String, Object> param);

    /**
     * 校验寄存器
     *
     * @param ip
     * @param param
     * @throws IOException
     */
    Map<String, Object> checkPLC(String ip, Map<String, Object> param);
}
