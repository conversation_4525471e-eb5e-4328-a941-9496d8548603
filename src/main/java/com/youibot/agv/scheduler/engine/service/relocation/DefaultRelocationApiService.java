package com.youibot.agv.scheduler.engine.service.relocation;

import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.util.Map;

import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.INSERT_SYSTEM_STATUS_FAULT;
import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.INSERT_SYSTEM_STATUS_SUCCESS;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 16:23
 */
public abstract class DefaultRelocationApiService extends DefaultApiService implements RelocationApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultRelocationApiService.class);

    @Value("${AGV_API_CODE.INSERT_SYSTEM_STATUS}")
    private String getStatusCode;

    @Value("${AGV_API_CODE.INSERT_SYSTEM}")
    private String insertSystemApiCode;

    @Value("${AGV_THREAD_CONFIG.WAIT_TIME.AGV_RETRY_GET_INSERT_SYSTEM}")
    private Integer retryWaitTime;

    @Value("${AGV_SOCKET.PORT.CONTROL}")
    private Integer controlPort;

    @Override
    public void insertSystem(String ip) throws IOException, InterruptedException {
        AGVSocketClient agvSocketClient = super.createClient(ip, controlPort);
        super.execute(agvSocketClient, insertSystemApiCode, null);
        LOGGER.debug("start insert system");
        // get agv insert system status .
        while (true) {
            Map<String, Object> dataMap = super.execute(agvSocketClient, getStatusCode, null);
            LOGGER.debug("ip : " + ip + ", insert system query status result data: " + dataMap);
            Integer status = (Integer) dataMap.get("reloc_status");//执行状态  2、执行中  1、执行完成  0、执行错误
            if (INSERT_SYSTEM_STATUS_SUCCESS.equals(status)) {//成功
                LOGGER.debug("agv insert system success");
                return;
            } else if (INSERT_SYSTEM_STATUS_FAULT.equals(status)) {//失败
                LOGGER.error("agv insert system api return error. ");
                throw new ExecuteException(MessageUtils.getMessage("http.agv_return_failed"));
            }
            Thread.sleep(retryWaitTime);
        }
    }
}
