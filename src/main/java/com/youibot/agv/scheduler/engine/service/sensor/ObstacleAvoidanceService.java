package com.youibot.agv.scheduler.engine.service.sensor;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:shishao<PERSON>@youibot.com
 * @version CreateTime: 2019/6/11 14:16
 */
public interface ObstacleAvoidanceService {

    //打开避障
    Map<String, Object> openObstacleAvoidance(String ip) throws IOException;

    //关闭避障
    Map<String, Object> closeObstacleAvoidance(String ip) throws IOException;
}
