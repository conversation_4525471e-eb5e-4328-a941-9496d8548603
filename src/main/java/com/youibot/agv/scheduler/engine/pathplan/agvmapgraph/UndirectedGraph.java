package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.engine.pathplan.util.CloneUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date :Created in 12:00 2020/8/7
 * @Description : 加权有向图
 * @Modified By :
 * @Version :
 */
public final class UndirectedGraph implements Cloneable {
    private static final Logger logger = LoggerFactory.getLogger(UndirectedGraph.class);

    private ConcurrentHashMap<String, UndirectedNode> nodeMap = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, UndirectedEdge> nodePairToEdge = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, Set<String>> adjacentNodes = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, Set<String>> adjacentEdges = new ConcurrentHashMap<>();

    public UndirectedGraph() {
        init();
    }

    public void init() {
        nodeMap = new ConcurrentHashMap<>();
        nodePairToEdge = new ConcurrentHashMap<>();
        adjacentNodes = new ConcurrentHashMap<>();
        adjacentEdges = new ConcurrentHashMap<>();
    }

    public int size() {
        return nodeMap.size();
    }

    public ConcurrentHashMap<String, UndirectedNode> getNodeMap() {
        return nodeMap;
    }

    public synchronized void addNode(UndirectedNode undirectedNode) {
        String id = undirectedNode.id;
        if (!nodeMap.containsKey(id)) {
            nodeMap.put(id, undirectedNode);
            adjacentNodes.computeIfAbsent(id, k -> new HashSet<>());
            adjacentEdges.computeIfAbsent(id, k -> new HashSet<>());
        }
    }

    public synchronized void removeNode(Marker marker) {
        String id = marker.getId();
        if (nodeMap.containsKey(id)) {
            nodeMap.remove(id);
            adjacentNodes.remove(id);
            adjacentEdges.remove(id);
        }
    }

    public synchronized void addEdge(UndirectedEdge edge) {
        if (edge == null) {
            return;
        }
        adjacentNodes.computeIfAbsent(edge.getA(), k -> new HashSet<>());
        adjacentNodes.computeIfAbsent(edge.getB(), k -> new HashSet<>());
        adjacentNodes.get(edge.getA()).add(edge.getB());
        adjacentNodes.get(edge.getB()).add(edge.getA());
        adjacentEdges.computeIfAbsent(edge.getA(), k -> new HashSet<>());
        adjacentEdges.computeIfAbsent(edge.getB(), k -> new HashSet<>());
        adjacentEdges.get(edge.getA()).add(edge.getPathId());
        adjacentEdges.get(edge.getB()).add(edge.getPathId());
        nodePairToEdge.put(edge.getPathId(), edge);
    }

    public synchronized void removeEdge(SidePath sidePath) {
        nodePairToEdge.remove(sidePath.getPathId());
        if (adjacentEdges.get(sidePath.getStartMarkerId()) != null)
            adjacentEdges.get(sidePath.getStartMarkerId()).remove(sidePath.getPathId());
        if (adjacentEdges.get(sidePath.getEndMarkerId()) != null)
            adjacentEdges.get(sidePath.getEndMarkerId()).remove(sidePath.getPathId());
    }

    public synchronized void removeEdges(List<SidePath> sidePaths) {
        sidePaths.forEach(sidePath -> removeEdge(sidePath));
    }

    public synchronized void removeEdges(Set<UndirectedEdge> undirectedEdges) {
        undirectedEdges.forEach(edge -> nodePairToEdge.remove(edge.getPathId()));
    }

    //get adjacent Nodes
    public synchronized Set<UndirectedNode> getAdjacentNodes(UndirectedNode undirectedNode) {
        if (nodeMap.containsKey(undirectedNode.getId())) {
            Set<String> set = adjacentNodes.get(undirectedNode.getId());
            return set.stream().map(nodeMap::get).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    //get adjacent markerIds
    public synchronized Set<String> getAdjacentNodeIds(String nodeId) {
        if (nodeMap.containsKey(nodeId)) {
            return new HashSet<>(adjacentNodes.get(nodeId));
        }
        return new HashSet<>();
    }

    //获得所有的路径边
    public synchronized Set<UndirectedEdge> getAllEdges() {
        return new HashSet<>(nodePairToEdge.values());
    }

    public synchronized Set<UndirectedEdge> getInOutEdges(String nodeId) {
        if (nodeMap.containsKey(nodeId)) {
            Set<String> set = adjacentEdges.get(nodeId);
            return set.stream().map(nodePairToEdge::get).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    public synchronized UndirectedNode getNodeById(String nodeId) {
        if (nodeMap.containsKey(nodeId)) {
            return nodeMap.get(nodeId);
        }
        return null;
    }

    public synchronized UndirectedEdge getEdgeByPathId(String pathId) {
        if (pathId == null) {
            return null;
        }
        return nodePairToEdge.get(pathId);
    }

    public synchronized UndirectedEdge getEdgeBySidePath(SidePath sidePath) {
        if (sidePath == null) return null;
        return getEdgeByPathId(sidePath.getPathId());
    }

    public synchronized boolean hasCyclic() {
        int size = nodeMap.size();
        Map<String, Boolean> visited = new HashMap<>(size);
        Map<String, Boolean> recursionStack = new HashMap<>(size);
        for (String nodeId : nodeMap.keySet()) {
            visited.put(nodeId, false);
            recursionStack.put(nodeId, false);
        }
        for (String nodeId : nodeMap.keySet()) {
            if (checkCyclic(nodeId, visited, recursionStack, null))
                return true;
        }
        return false;
    }

    private boolean checkCyclic(String nodeId, Map<String, Boolean> visited, Map<String, Boolean> recursionStack, String exceptNodeId) {
        if (!visited.get(nodeId)) {
            visited.put(nodeId, true);
            recursionStack.put(nodeId, true);
        }
        Set<String> adjacentNodeIds = getAdjacentNodeIds(nodeId);
        adjacentNodeIds.remove(exceptNodeId);
        for (String adjacentNodeId : adjacentNodeIds) {
            if (!visited.get(adjacentNodeId) && checkCyclic(adjacentNodeId, visited, recursionStack, nodeId)) {
                return true;
            } else if (recursionStack.get(adjacentNodeId)) {
                return true;
            }
        }
        recursionStack.put(nodeId, false);
        return false;
    }

    @Override
    public synchronized UndirectedGraph clone() {
        try {
            //UndirectedGraph directedGraph = (UndirectedGraph) super.clone();
            UndirectedGraph undirectedGraph = new UndirectedGraph();
            undirectedGraph.nodeMap = new ConcurrentHashMap<>(this.nodeMap);
            undirectedGraph.nodePairToEdge = CloneUtils.clone(this.nodePairToEdge);
            undirectedGraph.adjacentNodes = CloneUtils.clone(this.adjacentNodes);
            undirectedGraph.adjacentEdges = CloneUtils.clone(this.adjacentEdges);
            return undirectedGraph;
        } catch (Exception e) {
            logger.error("Clone DirectedGraph. [{}]", e.getMessage());
            throw new AssertionError();
        }
    }

}
