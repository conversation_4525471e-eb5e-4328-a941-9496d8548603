package com.youibot.agv.scheduler.engine.service.remotePLC;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.youibot.agv.scheduler.constant.ActionConstant;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.manager.modbus.JLibModbusUtils;
import com.youibot.agv.scheduler.engine.manager.modbus.ModbusFunction;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.UnknownHostException;
import java.util.*;

@Service
public class DefaultRemotePLCApiService extends DefaultApiService implements RemotePLCApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultRemotePLCApiService.class);

    @Value("${MODBUS.LOCAL_IP}")
    private String ip;

    @Value("${MODBUS.LOCAL_PORT}")
    private Integer port;

    @Autowired
    private ModbusFunction modbusFunction;

    @Override
    public Map<String, Object> readPLC(String ip, Map<String, Object> param) {
        LOGGER.debug("ip : " + ip + " readPLC send data : " + JSON.toJSONString(param));
        Map<String, Object> resultData = new HashMap<>();
        String functionCode = (String) param.get("functionCode");
        Integer slaveId = (Integer) param.get("slaveId");
        Integer address = (Integer) param.get("startAddress");
        try {
            switch (functionCode) {
                case ActionConstant.REMOTE_PLC_FUNCTION_READ_01:
                    boolean[] resultValue01 = JLibModbusUtils.readCoils(ip, port, slaveId, address, 1);
                    if (resultValue01[0]) {
                        resultData.put("result", 1);
                    } else {
                        resultData.put("result", 0);
                    }
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_READ_02:
                    boolean[] resultValue02 = JLibModbusUtils.readDiscreteInputs(ip, port, slaveId, address, 1);
                    if (resultValue02[0]) {
                        resultData.put("result", 1);
                    } else {
                        resultData.put("result", 0);

                    }
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_READ_03:
                    int[] resultValue03 = JLibModbusUtils.readHoldingRegisters(ip, port, slaveId, address, 1);
                    resultData.put("result", resultValue03[0]);
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_READ_04:
                    int[] resultValue04 = JLibModbusUtils.readInputRegisters(ip, port, slaveId, address, 1);
                    resultData.put("result", resultValue04[0]);
                    break;
                default:
                    throw new ExecuteException("找不到对应功能码functionCode:{}" + functionCode);
            }
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            throw new ExecuteException("操作读寄存器失败！");
        }
        return resultData;
    }

    @Override
    public void writePLC(String ip, Map<String, Object> param) {
        LOGGER.debug("ip : " + ip + " writePLC send data : " + JSON.toJSONString(param));
        String functionCode = (String) param.get("functionCode");
        Integer slaveId = (Integer) param.get("slaveId");
        Integer address = (Integer) param.get("startAddress");
        Object writeValue = param.get("writeValue");
        try {
            switch (functionCode) {
                case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_05:
                    JSONArray values05 = (JSONArray) writeValue;
                    if (values05 == null || values05.size() != 1) {
                        throw new ExecuteException("writeValue值不能为空且长度只能为1");
                    }
                    boolean[] booleans05 = getBooleansByWriteValue(values05);
                    JLibModbusUtils.writeSingleCoil(ip, port, slaveId, address, booleans05[0]);
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_06:
                    JSONArray values06 = (JSONArray) writeValue;
                    if (values06 == null || values06.size() != 1) {
                        throw new ExecuteException("writeValue值不能为空且长度只能为1");
                    }
                    int[] ints06 = getIntsByWriteValue(values06);
                    JLibModbusUtils.writeSingleRegister(ip, port, slaveId, address, ints06[0]);
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_15:
                    JSONArray values15 = (JSONArray) writeValue;
                    if (values15 == null || values15.size() == 0) {
                        throw new ExecuteException("writeValue值不能为空");
                    }
                    boolean[] booleans15 = getBooleansByWriteValue(values15);
                    JLibModbusUtils.writeMultipleCoils(ip, port, slaveId, address, booleans15);
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_16:
                    JSONArray values16 = (JSONArray) writeValue;
                    if (values16 == null || values16.size() == 0) {
                        throw new ExecuteException("writeValue值不能为空");
                    }
                    int[] ints16 = getIntsByWriteValue(values16);
                    JLibModbusUtils.writeMultipleRegisters(ip, port, slaveId, address, ints16);
                    break;
                default:
                    throw new ExecuteException("找不到对应功能码functionCode:{}" + functionCode);
            }
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            throw new ExecuteException("操作写寄存器失败！");
        }
    }

    private int[] getIntsByWriteValue(JSONArray values) {
        int[] ints = new int[values.size()];
        for (int i = 0; i < values.size(); i++) {
            ints[i] = (int) values.get(i);
        }
        return ints;
    }

    private boolean[] getBooleansByWriteValue(JSONArray values) {
        boolean[] booleans = new boolean[values.size()];
        for (int i = 0; i < values.size(); i++) {
            if (values.get(i).equals(1)) {
                booleans[i] = true;
            } else if (values.get(i).equals(0)) {
                booleans[i] = false;
            } else {
                throw new ExecuteException("writeValue值不合法，05和15功能码只支持写入0或1");
            }
        }
        return booleans;
    }

    @Override
    public Map<String, Object> checkPLC(String ip, Map<String, Object> param) {
        LOGGER.debug("ip : " + ip + " checkPLC send data : " + JSON.toJSONString(param));
        String functionCode = (String) param.get("functionCode");
        Map<String, Object> resultData;
        try {
            switch (functionCode) {
                case ActionConstant.REMOTE_PLC_FUNCTION_READ_01:
                    resultData = this.readCheckFunction_01(ip, param);
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_READ_02:
                    resultData = this.readCheckFunction_02(ip, param);
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_READ_03:
                    resultData = this.readCheckFunction_03(ip, param);
                    break;
                case ActionConstant.REMOTE_PLC_FUNCTION_READ_04:
                    resultData = this.readCheckFunction_04(ip, param);
                    break;
                default:
                    throw new ExecuteException("找不到对应功能码functionCode:{}" + functionCode);
            }
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            throw new ExecuteException("操作校验寄存器失败！");
        }
        return resultData;
    }

    private Map<String, Object> readCheckFunction_01(String ip, Map<String, Object> param) throws InterruptedException {
        return checkReadCoilValue(ip, param);
    }

    private Map<String, Object> readCheckFunction_02(String ip, Map<String, Object> param) throws InterruptedException {
        return checkReadCoilValue(ip, param);
    }

    private Map<String, Object> readCheckFunction_03(String ip, Map<String, Object> param) throws InterruptedException {
        return checkReadShortValue(ip, param);
    }

    private Map<String, Object> readCheckFunction_04(String ip, Map<String, Object> param) throws InterruptedException {
        return checkReadShortValue(ip, param);
    }

    private Map<String, Object> checkReadCoilValue(String ip, Map<String, Object> param) throws InterruptedException {
        ModbusMaster modbusMaster = null;
        Integer slaveId = (Integer) param.get("slaveId");
        Integer startAddress = (Integer) param.get("startAddress");
        Integer time_out = (Integer) param.get("timeOut");
        String functionCode = (String) param.get("functionCode");
        try {
            Map<String, Object> resultData = new HashedMap<>();
            long startTime = System.currentTimeMillis();//开始时间
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            while (true) {//如果动作设置的值与读取plc的值不一致，继续读取
                if (Thread.interrupted()) {//如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }
                Thread.sleep(500);
                if ((new Date().getTime() - startTime) / 1000 > time_out) {
                    throw new ExecuteException(ErrorEnum.MODBUS_READ_NOT_EQUAL_TIME_OUT.code(), ErrorEnum.MODBUS_READ_NOT_EQUAL_TIME_OUT.msg());
                }
                boolean[] resultValue = modbusFunction.readCoilStatusValue(modbusMaster, functionCode,
                        slaveId, startAddress, 1);//读取modbus值
                if (resultValue == null || resultValue.length == 0) {
                    continue;
                }
                if (resultValue[0]) {
                    resultData.put("result", 1);
                    return resultData;
                }
            }
        } catch (UnknownHostException e) {
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    private Map<String, Object> checkReadShortValue(String ip, Map<String, Object> param) throws InterruptedException {
        ModbusMaster modbusMaster = null;
        Integer slaveId = (Integer) param.get("slaveId");
        Integer startAddress = (Integer) param.get("startAddress");
        Integer timeOut = (Integer) param.get("timeOut");
        String modbusErrorCode = (String) param.get("modbusErrorCode");
        String modbusSuccessCode = (String) param.get("modbusSuccessCode");
        String functionCode = (String) param.get("functionCode");
        try {
            Map<String, Object> resultData = new HashedMap<>();
            long startTime = System.currentTimeMillis();//开始时间
            List<String> errorCodeList = new ArrayList<>(Arrays.asList(modbusErrorCode.split(",")));
            List<String> successCodeList = new ArrayList<>(Arrays.asList(modbusSuccessCode.split(",")));
            modbusMaster = JLibModbusUtils.createModbusMaster(ip, port);
            while (true) {//如果动作设置的值与读取plc的值不一致，继续读取
                if (Thread.interrupted()) {//如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }
                Thread.sleep(500);
                if ((new Date().getTime() - startTime) / 1000 > timeOut) {
                    throw new ExecuteException(ErrorEnum.MODBUS_READ_NOT_EQUAL_TIME_OUT.code(), ErrorEnum.MODBUS_READ_NOT_EQUAL_TIME_OUT.msg());
                }
                int[] resultValue = modbusFunction.readShortValue(modbusMaster, functionCode, slaveId, startAddress, 1);//读取modbus值
                if (resultValue == null || resultValue.length == 0) {
                    continue;
                }
                resultData.put("result", resultValue[0]);
                if (errorCodeList.contains(String.valueOf(resultValue[0]))) {
                    throw new ExecuteException(ErrorEnum.MODBUS_READ_DATA_EQUAL_OR_CONTAIN_ERROR_CODE.code(), ErrorEnum.MODBUS_READ_DATA_EQUAL_OR_CONTAIN_ERROR_CODE.msg());
                }
                if (successCodeList.contains(String.valueOf(resultValue[0]))) {
                    return resultData;
                }
            }
        } catch (UnknownHostException e) {
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }
}
