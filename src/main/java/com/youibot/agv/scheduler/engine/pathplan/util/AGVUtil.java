package com.youibot.agv.scheduler.engine.pathplan.util;

import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date :Created in 15:42 2021/8/2
 * @Description :
 * @Modified By :
 * @Version :
 */
public class AGVUtil {
    private static final Logger logger = LoggerFactory.getLogger(AGVUtil.class);

    private static DefaultVehiclePool defaultVehiclePool = (DefaultVehiclePool) ApplicationUtils.getBean("defaultVehiclePool");

    public static String getAGVTypeByAGVCode(String agvCode) {
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        if (vehicle == null) return null;
        return vehicle.getAgvType();
    }
}
