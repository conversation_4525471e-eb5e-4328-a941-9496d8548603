package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;

/**
 * <AUTHOR>
 * @Date :Created in 下午2:44 2021/2/11
 * @Description :
 * @Modified By :
 * @Version :
 */
public class DirectedNodeProperty {
    public DirectedNode parent = null;
    public DirectedNode child = null;
    public Double g = Double.POSITIVE_INFINITY;
    public Double rhs = Double.POSITIVE_INFINITY;
    public Pair<Double, Double> k = new Pair<>(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
}
