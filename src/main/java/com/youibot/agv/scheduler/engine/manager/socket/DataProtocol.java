package com.youibot.agv.scheduler.engine.manager.socket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AbnormalPromptService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;

import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.SUCCESS_RESULT_CODE;

/**
 * AGVSocket 数据协议类
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019年4月10日 下午12:10:42
 */
@Data
public class DataProtocol implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataProtocol.class);
    private static final long serialVersionUID = 8598315280545549302L;

    private AbnormalPromptService abnormalPromptService = (AbnormalPromptService) ApplicationUtils.getBean("abnormalPromptServiceImpl");

    //长度均以字节（byte）为单位
    public static final int PACKAGE_HEADER_LEN = 1;   //包头长度
    public static final int API_CODE_LEN = 5;         //API编号
    public static final int CRC_LEN = 4;              //crc校验区长度
    public static final int PACKAGE_END_LEN = 1;      //包尾长度

    private byte packageHeader = 0x02;   //包头
    private byte packageEnd = 0x03;      //包尾
    private String apiCode;              //api编号
    private String data;                 //数据区数据
    private String crc = "BBC6";                  //crc校验去内容

    public DataProtocol() {
    }

    public DataProtocol(String apiCode, String data) {
        this.apiCode = apiCode;
        this.data = data;
    }

    public DataProtocol(byte packageHeader, byte packageEnd, String apiCode, String data, String crc) {
        this.packageHeader = packageHeader;
        this.packageEnd = packageEnd;
        this.apiCode = apiCode;
        this.data = data;
        this.crc = crc;
    }

    /**
     * 获取数据包总长度
     *
     * @return
     */
    private int getLength() {
        int dataLen = data != null ? data.getBytes().length : 0;
        return PACKAGE_HEADER_LEN + API_CODE_LEN + dataLen + CRC_LEN + PACKAGE_END_LEN;
    }

    /**
     * 拼接发送数据
     *
     * @return
     */
    public byte[] genContentData() {
        byte[] dataBytes = new byte[0];
        if (!StringUtils.isEmpty(data)) {
            dataBytes = data.getBytes();//业务数据
        }
        byte[] packageBytes = new byte[getLength()];//数据包的全部数据
        packageBytes[0] = this.packageHeader;
        int copyEndSize = PACKAGE_HEADER_LEN;
        copyEndSize = byteArrAppend(packageBytes, apiCode.getBytes(), copyEndSize);//拼接apiCode
        copyEndSize = byteArrAppend(packageBytes, dataBytes, copyEndSize);         //拼接data
        copyEndSize = byteArrAppend(packageBytes, crc.getBytes(), copyEndSize);       //拼接crc
        packageBytes[copyEndSize] = packageEnd;
        return packageBytes;
    }

    private int byteArrAppend(byte[] target, byte[] source, int copyEndSize) {
        System.arraycopy(source, 0, target, copyEndSize, source.length);
        return copyEndSize + source.length;
    }

    /**
     * 解析接收数据
     *
     * @param
     * @return
     * @throws AGVResultException
     */
    public DataProtocol parseContentData(byte[] resultBytes) throws AGVResultException {
        if (resultBytes == null || resultBytes.length <= 0) {
            throw new AGVResultException(MessageUtils.getMessage("socket.return_empty"));
        }
        byte packageHeader = resultBytes[0];//包头
        if (this.packageHeader != packageHeader) {
            throw new AGVResultException(MessageUtils.getMessage("socket.return_packet_header_error"));
        }
        byte packageEnd = resultBytes[resultBytes.length - 1];//包尾
        if (this.packageEnd != packageEnd) {
            throw new AGVResultException(MessageUtils.getMessage("socket.return_packet_end_error"));
        }

        int dataLength = resultBytes.length - PACKAGE_HEADER_LEN - API_CODE_LEN - CRC_LEN - PACKAGE_END_LEN;//数据区长度
        String apiCode = new String(resultBytes, PACKAGE_HEADER_LEN, API_CODE_LEN, StandardCharsets.UTF_8);
        String data = new String(resultBytes, PACKAGE_HEADER_LEN + API_CODE_LEN, dataLength, StandardCharsets.UTF_8);
        String crc = new String(resultBytes, PACKAGE_HEADER_LEN + API_CODE_LEN + dataLength, CRC_LEN, StandardCharsets.UTF_8);

        if (StringUtils.isEmpty(data)) {
            throw new AGVResultException(MessageUtils.getMessage("socket.return_data_empty"));
        }
        
        LOGGER.debug("agv_result," + ", apiCode:" + apiCode + " data:" + data);
        //解析data
        JSONObject dataJson = JSON.parseObject(data);
        Integer resultCode = dataJson.getInteger("result_code");
        if (!resultCode.equals(SUCCESS_RESULT_CODE)) {
            LOGGER.error("Parse Result and code is failed. code:" + resultCode + " error message:" + dataJson.getString("error_message"));
            AbnormalPrompt abnormalByCode = abnormalPromptService.getAbnormalByCode(resultCode);
            if (abnormalByCode != null) {
                throw new ExecuteException(resultCode, abnormalByCode.getAbnormalDescription());
            }
            throw new ExecuteException(resultCode, dataJson.getString("error_message"));
        }
        return new DataProtocol(packageHeader, packageEnd, apiCode, data, crc);
    }

    @Override
    public String toString() {
        return "data: " + data + ", apiCode:" + apiCode;
    }

}
