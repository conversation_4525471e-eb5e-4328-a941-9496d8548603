package com.youibot.agv.scheduler.engine.constant;

import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 17:22
 */
public class AGVApiReturnConstant {

    public static final Integer ACTION_EXECUTE_RUNNING = 1;//执行中
    public static final Integer ACTION_EXECUTE_FAULT = 2;//执行错误
    public static final Integer ACTION_EXECUTE_SUCCESS = 3;//执行成功
    public static final Integer ACTION_EXECUTE_PAUSE = 4;//执行暂停中

    public static final Integer INSERT_SYSTEM_STATUS_RUNNING = 2;//插入系统执行中
    public static final Integer INSERT_SYSTEM_STATUS_SUCCESS = 1;//插入系统成功
    public static final Integer INSERT_SYSTEM_STATUS_FAULT = 0;//插入系统失败

    public static final Integer BLOCK_STATUS_NOT =0; //未阻挡
    public static final Integer BLOCK_STATUS_HAS =1; //阻挡中

    public static final Integer CHARGE_STATUS_NOT =0; //未充电
    public static final Integer CHARGE_STATUS_HAS =1; //充电中

    public static final Integer EMC_STATUS_NOT =0; //未急停
    public static final Integer EMC_STATUS_HAS =1; //急停中

    public static final Integer ROLLER_SENSOR_NOT_IN_PLACE = 0;//辊筒传感器未到位
    public static final Integer ROLLER_SENSOR_IN_PLACE = 1;//辊筒传感器到位
    public static final Integer ROLLER_SENSOR_EXECUTE_FAULT = 2;//辊筒传感器执行失败

    public static final Integer RECORDING_MAP_STATUS_SCAN = 1;//录制地图扫描中
    public static final Integer RECORDING_MAP_STATUS_PAUSE = 2;//录制地图暂停中
    public static final Integer RECORDING_MAP_STATUS_SUCCESS = 3;//录制地图完成

    public static final Integer RECORDING_MAP_SAVE = 1;//保存录制地图
    public static final Integer RECORDING_MAP_NOT_SAVE = 0;//不保存录制地图

    public static final Integer RELOCALIZE_SUCCESS = 1;//重定位成功
    public static final Integer RELOCALIZE_FAULT = 2;//重定位失败

    public static final Integer LOCATION_DATA_AVAILABLE = 1;//机器人激光定位数据可用
    public static final Integer LOCATION_DATA_NOT_AVAILABLE = 2;//机器人激光定位数据不可用

    public static final String POS_CURRENT_STATION_NOTHING = "-1";//总状态中返回的无当前位置(marker)

    public static final Integer SUCCESS_RESULT_CODE = 1001;//调用api的返回编码

    //返回的数据类型
    public static final String RESULT_TYPE_MESSAGE = "MESSAGE";//信息
    public static final String RESULT_TYPE_IMAGE = "IMAGE";//图片

    //随动模式
    public static final Integer AGV_FOLLOW_STATUS_SHELF = 1;//顶升货架随动
    public static final Integer AGV_FOLLOW_STATUS_NO = 0;//无


}
