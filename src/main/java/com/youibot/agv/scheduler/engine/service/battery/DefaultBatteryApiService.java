package com.youibot.agv.scheduler.engine.service.battery;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/13 14:19
 */
@Service
public class DefaultBatteryApiService extends DefaultApiService implements BatteryApiService {

    @Value("${AGV_API_CODE.BATTERY_CHARGE}")
    private String batteryChargeApiCode;
    @Value("${AGV_API_CODE.NEW_SMART_CHARGE}")
    private String newSmartChargeApiCode;

    @Override
    public Map<String, Object> batteryCharge(String ip, Map<String, Object> param) throws IOException, InterruptedException {
        //发送充电指令
        ActionUtils.sendInstruction(ip, batteryChargeApiCode, JSONObject.toJSONString(param));
        //查看agv执行充电状态
        return ActionUtils.checkActionStatus(ip, (String) param.get("id"));
    }

    @Override
    public Map<String, Object> newSmartCharge(String ip, Map<String, Object> param) throws IOException, InterruptedException {
        //发送充电指令
        ActionUtils.sendInstruction(ip, newSmartChargeApiCode, JSONObject.toJSONString(param));
        //查看agv执行充电状态
        return ActionUtils.checkActionStatus2(ip, (String) param.get("alter_id"));
    }

    @Override
    public Map<String, Object> batteryChargeIgnoreStatus(String ip, Map<String, Object> param) throws IOException {
        //发送充电指令
        return ActionUtils.sendInstruction(ip, batteryChargeApiCode, JSONObject.toJSONString(param));
    }
}
