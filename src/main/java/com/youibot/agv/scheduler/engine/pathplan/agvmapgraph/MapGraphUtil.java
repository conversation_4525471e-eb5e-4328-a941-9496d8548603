package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.impl.PathPlanAndSendPathLogServiceImpl;
import com.youibot.agv.scheduler.engine.pathplan.util.SidePathUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.engine.pathplan.entity.ModifySidePathWeight;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.*;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 18:43 2020/8/7
 * @Description :
 * @Modified By :
 * @Version :
 */
public class MapGraphUtil {

    private static final Logger logger = LoggerFactory.getLogger(MapGraphUtil.class);
    public static final ImmutableList<Integer> ELEVATORS_NAVATIOIN_TYPE = ImmutableList.of(SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR, SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR, SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR);

    public static final ImmutableList<Integer> TAKE_NAVATIOIN_TYPE = ImmutableList.of( SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR);

    private static MapGraph mapGraph = (MapGraph) ApplicationUtils.getBean("mapGraph");

    private static LocationService locationService = ApplicationUtils.getBean(LocationService.class);

    private static PathPlanAndSendPathLogServiceImpl pathPlanAndSendPathLogService = (PathPlanAndSendPathLogServiceImpl) ApplicationUtils.getBean("pathPlanAndSendPathLogServiceImpl");

    /**
     * get Origin DirectedGraph
     *
     * @return
     */
    public static DirectedGraph getOriginDirectedGraph() {
        return mapGraph.getDGraphOfAGVMap();
    }

    /**
     * get clone DirectedGraph
     *
     * @return
     */
    public static DirectedGraph getCloneDirectedGraph() {
        DirectedGraph graph = mapGraph.getDGraphOfAGVMap();
        if (graph != null) return graph.clone();
        else return null;
    }

    /**
     * get side path by start end marker id
     *
     * @param startMarkerId
     * @param endMarkerId
     * @return
     */
    public static SidePath getSidePathByStartEndMarkerId(String startMarkerId, String endMarkerId) {
        if (startMarkerId == null || endMarkerId == null) return null;
        SidePath sidePath = mapGraph.getStartEndMarkerIdToSidePath().get(startMarkerId + endMarkerId);
        if (sidePath != null) return sidePath.clone();
        else return null;
    }

    /**
     * get reverse side paths by side path ids
     *
     * @param sidePathIds
     * @return
     */
    public static Set<String> getReverseSidePathIds(Collection<String> sidePathIds) {
        if (CollectionUtils.isEmpty(sidePathIds)) return new HashSet<>();
        Set<String> reverseSidePathIds = new HashSet<>();
        sidePathIds.forEach(sId -> {
            SidePath rs = getReverseSidePath(sId);
            if (rs != null) {
                reverseSidePathIds.add(rs.getId());
            }
        });
        return reverseSidePathIds;
    }

    /**
     * get reverse side paths by side path id
     *
     * @param sidePathId
     * @return
     */
    public static SidePath getReverseSidePath(String sidePathId) {
        if (sidePathId == null) return null;
        SidePath sidePath = getSidePathBySidePathId(sidePathId);
        if (sidePath != null) {
            return getSidePathByStartEndMarkerId(sidePath.getEndMarkerId(), sidePath.getStartMarkerId());
        } else {
            return null;
        }

    }

    public static Set<String> getAdjacentMarkerId(String markerId) {
        if (markerId == null) return new HashSet<>();
        Set<String> adjacentMarkerIds = mapGraph.getAdjacentMarkerIds(markerId);
        return adjacentMarkerIds;
    }

    public static Set<String> getAdjacentSidePathId(String markerId) {
        if (markerId == null) return new HashSet<>();
        Set<DirectedEdge> inOutEdges = mapGraph.getDGraphOfAGVMap().getInOutEdges(markerId);
        return inOutEdges.stream().map(DirectedEdge::getSidePathId).collect(Collectors.toSet());
    }

    public static Set<String> getInSidePathId(String markerId) {
        if (markerId == null) return new HashSet<>();
        Set<DirectedEdge> inOutEdges = mapGraph.getDGraphOfAGVMap().getInEdges(markerId);
        return inOutEdges.stream().map(DirectedEdge::getSidePathId).collect(Collectors.toSet());
    }

    public static Set<String> getOutSidePathId(String markerId) {
        if (markerId == null) return new HashSet<>();
        Set<DirectedEdge> inOutEdges = mapGraph.getDGraphOfAGVMap().getOutEdges(markerId);
        return inOutEdges.stream().map(DirectedEdge::getSidePathId).collect(Collectors.toSet());
    }

    //从markerId得到Marker对象
    public static Marker getMarkerByMarkerId(String markerId) {
        if (markerId == null) return null;
        return mapGraph.getMarkerIdToMarker().get(markerId);
    }

    public static Path getPathByPathId(String pathId) {
        if (pathId == null) return null;
        return mapGraph.getPathIdToPath().get(pathId);
    }

    //从sidepathId得到sidepath对象 获取地图中的所有
    public static SidePath getSidePathBySidePathId(String sidePathId) {
        if (sidePathId == null) return null;
        SidePath sidePath = mapGraph.getSidePathIdToSidePath().get(sidePathId);
        if (sidePath != null) return sidePath.clone();
        else return null;
    }

    //获取地图中的所有marker点
    public static List<Marker> getMarkersByAGVMapId(String agvMapId) {
        if (agvMapId == null) return new ArrayList<>();
        return mapGraph.getMarkersByAGVMapId(agvMapId);
    }

    //获取地图中的所有sidepath
    public static List<SidePath> getSidePathsByAGVMapId(String agvMapId) {
        if (agvMapId == null) return new ArrayList<>();
        return mapGraph.getSidePathsByAGVMapId(agvMapId);
    }

    /**
     * get all enable markers
     *
     * @return
     */
    public static Set<Marker> getEnableMarkers() {
        Set<Marker> markers = new HashSet<>();
        Set<Marker> activeMarkers = mapGraph.getActiveMarkers();
        List<Marker> disableMarkers = mapGraph.getDisableMarkers();
        markers.addAll(activeMarkers);
        markers.removeAll(disableMarkers);
        return markers;
    }

    //检查marker点是否不存在或者被禁用掉 false:被禁用或不存在
    public static boolean checkAimMarker(String aimMarkerId) {
        if (aimMarkerId == null) return false;
        Marker marker = mapGraph.getMarkerIdToMarker().get(aimMarkerId);
        if (marker == null) {
            return false;
        }
        if (!mapGraph.getActiveMarkers().contains(marker)) {
            return false;
        }
        return !mapGraph.getDisableMarkers().contains(marker);
    }

    /**
     * 更新地图接口
     *
     * @param agvMapId
     */
    public static void addAGVMap(String agvMapId) {
        if (agvMapId == null) return;
        //查询是否有旧地图数据，如果有则删除
        removeAGVMap(agvMapId);
        mapGraph.addAGVMap(agvMapId);
    }

    /**
     * 禁用地图接口
     *
     * @param agvMapId
     */
    public static void removeAGVMap(String agvMapId) {
        if (agvMapId == null) return;
        mapGraph.removeAGVMap(agvMapId);
    }

    /**
     * get all active markers
     *
     * @return
     */
    public static Set<Marker> getActiveMarkers() {
        return mapGraph.getActiveMarkers();
    }

    public static Set<SidePath> getActiveSidePaths() {
        return mapGraph.getActiveSidePaths();
    }

    /**
     * get marker navigation type
     *
     * @param markerId
     * @return
     */
    public static Integer getMarkerNavigationType(String markerId) {
        if (markerId == null) return MARKER_NAVIGATION_TYPE_NORMAL;
        Integer markerNavigationType = mapGraph.getMarkerIdToMarkerNavigationType().get(markerId);
        if (markerNavigationType == null) {
            return MARKER_NAVIGATION_TYPE_NORMAL;
        }
        return markerNavigationType;
    }


    /**
     * get elevator id by elevator marker id
     *
     * @param elevatorMarkerId
     * @return
     */
    public static String getElevatorIdByElevatorMarkerId(String elevatorMarkerId) {
        if (elevatorMarkerId == null) return null;
        return mapGraph.getElevatorMarkerIdToElevatorId().get(elevatorMarkerId);
    }

    /**
     * init floor info when changed elevator or floor
     */
    public static void initFloor() {
        mapGraph.initFloor();
    }

    public static Set<String> getElevatorResourceIds(String inOutElevatorMarkerId) {
        String elevatorMarkerId = mapGraph.inOutMarkerIdToElevatorMarkerId.get(inOutElevatorMarkerId);
        Set<String> adjacentMarkerIds = mapGraph.getElevatorAdjacentMarkerIds(inOutElevatorMarkerId, elevatorMarkerId);
        return adjacentMarkerIds;
    }

    ///**
    // * modify autoWeight according to the type(+-x) by the double value
    // *
    // * @param sidePaths
    // * @param type
    // * @param autoWeight
    // */
    //public static void modifyAutoWeight(List<SidePath> sidePaths, String type, Double autoWeight) {
    //    sidePaths.forEach(s -> modifyAutoWeight(s.getId(), type, autoWeight));
    //}

    //public static void resetAutoWeight(List<SidePath> sidePaths) {
    //    sidePaths.forEach(s -> modifyAutoWeight(s.getId(), PATH_WEIGHT_AUTO_RESET, null));
    //}

    /**
     * modify autoWeight by side path id according to the type
     *
     * @param sidePathId
     * @param type
     * @param autoWeight
     */
    public static void modifyAutoWeight(String agvCode, String sidePathId, String type, Double autoWeight) {
        if (agvCode == null || sidePathId == null || type == null || autoWeight == null) return;
        SidePath sidePath = mapGraph.getSidePathIdToSidePath().get(sidePathId);
        if (sidePath != null) {
            String n1 = sidePath.getStartMarkerId();
            String n2 = sidePath.getEndMarkerId();
            DirectedEdge edge = mapGraph.getDGraphOfAGVMap().getEdgeByStartEndNode(n1, n2);
            if (edge != null) {
                edge.modifyAutoWeight(type, autoWeight);

                //TODO:delete
                ModifySidePathWeight modifySidePathWeight = new ModifySidePathWeight();
                modifySidePathWeight.setAgvCode(agvCode);
                modifySidePathWeight.setSidePathId(sidePathId);
                modifySidePathWeight.setAutoWeight(autoWeight);
                modifySidePathWeight.setType(type);
                modifySidePathWeight.setStartMarkerCode(getMarkerByMarkerId(n1).getCode());
                modifySidePathWeight.setEndMarkerCode(getMarkerByMarkerId(n2).getCode());
                modifySidePathWeight.setWeightAuto(edge.getWeightAuto());
                modifySidePathWeight.setCreateTime(new Date());
                modifySidePathWeight.setUpdateTime(new Date());
                modifySidePathWeight.setThreadName(Thread.currentThread().getName());
                pathPlanAndSendPathLogService.saveModifySidePathWeight(modifySidePathWeight);
            }
        }
    }

    public static void modifyUserWeight(String sidePathId, String type, Double userWeight) {
        if (sidePathId == null || type == null || userWeight == null) return;
        SidePath sidePath = mapGraph.getSidePathIdToSidePath().get(sidePathId);
        if (sidePath != null) {
            String n1 = sidePath.getStartMarkerId();
            String n2 = sidePath.getEndMarkerId();
            DirectedEdge edge = mapGraph.getDGraphOfAGVMap().getEdgeByStartEndNode(n1, n2);
            if (edge != null) {
                edge.modifyUserWeight(type, userWeight);
            }
        }
    }

    public static void modifyUserWeightTowWay(String sidePathId, String type, Double userWeight) {
        if (sidePathId == null || type == null || userWeight == null) return;
        SidePath sidePath = mapGraph.getSidePathIdToSidePath().get(sidePathId);
        if (sidePath != null) {
            String n1 = sidePath.getStartMarkerId();
            String n2 = sidePath.getEndMarkerId();
            DirectedEdge edge = mapGraph.getDGraphOfAGVMap().getEdgeByStartEndNode(n1, n2);
            if (edge != null) {
                edge.modifyUserWeight(type, userWeight);
            }
            //反向边增加权重
            DirectedEdge edge2 = mapGraph.getDGraphOfAGVMap().getEdgeByStartEndNode(n2, n1);
            if (edge2 != null) {
                edge2.modifyUserWeight(type, userWeight/2);
            }
        }
    }


    /**
     * 判断边是否被包含 返回true代表包含 false代表不包含
     */
    public static Boolean containedSidePath(SidePath sidePath) {
        if (sidePath == null) return false;
        DirectedEdge edge = mapGraph.getDGraphOfAGVMap().getEdgeBySidePath(sidePath);
        if (edge == null) {
            return false;
        }
        return edge.getTempWeight() < Double.POSITIVE_INFINITY;
    }


    /**
     * get velocity cost of side path from t0 to t=1;
     *
     * @param sidePath
     * @return
     */
    public static Double getPiecemealCost(SidePath sidePath) {
        if (sidePath != null) return mapGraph.getDGraphOfAGVMap().getPiecemealCost(sidePath);
        else return 0D;
    }

    /**
     * 获取路径上的权重之和
     *
     * @param sidePath
     * @return
     */
    public static Double getSidePathCost(SidePath sidePath) {
        if (sidePath != null) return mapGraph.getDGraphOfAGVMap().getSidePathCost(sidePath);
        else return 0D;
    }

    /**
     * get angle cost by continuous node n1,n2,n3
     *
     * @param n1
     * @param n2
     * @param n3
     * @return
     */
    public static Double getAngleCost(String n1, String n2, String n3) {
        if (n1 == null || n2 == null || n3 == null) return 0D;
        return mapGraph.getDGraphOfAGVMap().getAngleCost(n1, n2, n3);
    }

    /**
     * cal list of side paths cost(angle, velocity, autoWeight, userWeight)
     *
     * @param sidePaths
     * @return
     */
    public static Double getCost(LinkedBlockingDeque<SidePath> sidePaths) {
        if (CollectionUtils.isEmpty(sidePaths)) return 0D;
        LinkedList<SidePath> temp = new LinkedList<>(sidePaths);
        SidePath firstSidePath = temp.poll();
        Double cost = getPiecemealCost(firstSidePath);
        if (temp.size() != 0) {
            if (firstSidePath.getT0() != 1D) {
                cost += getAngleCost(firstSidePath.getStartMarkerId(), firstSidePath.getEndMarkerId(), temp.getFirst().getEndMarkerId());
            }
            for (int i = 0; i < temp.size() - 1; i++) {
                cost += mapGraph.getDGraphOfAGVMap().getCost(temp.get(i).getStartMarkerId(), temp.get(i).getEndMarkerId(), temp.get(i + 1).getEndMarkerId());
            }
            cost += mapGraph.getDGraphOfAGVMap().getDirectCost(temp.get(temp.size() - 1));
        }
        return cost;
    }

    public static boolean haveElevatorPath(LinkedBlockingDeque<SidePath> sidePaths , LinkedBlockingDeque<SidePath> executedSidePaths){
       List<SidePath > running = Lists.newArrayList( sidePaths);
        if(CollectionUtils.isEmpty(running)){
            return  false;
        }
       List<SidePath> list = Lists.newArrayList( executedSidePaths);
        if(!CollectionUtils.isEmpty( list)) {
            SidePath sidePath = list.get(list.size() - 1);
            running.add( sidePath);
            if(list.size()>=2){
                SidePath sidePath1 = list.get(list.size() - 2);
                running.add(sidePath1);
            }
        }


        Optional<SidePath> path = running.parallelStream().filter(p -> ELEVATORS_NAVATIOIN_TYPE.contains(p.getNavigationType())).findFirst();
        return  path.isPresent();


    }
    //从传入的sidePathList中截取时间长度为time的路径段并将其返回
    public static List<LinkedBlockingDeque<SidePath>> spiltSidePathByCost(LinkedBlockingDeque<SidePath> sidePaths, Double time) {
        if (CollectionUtils.isEmpty(sidePaths)) {
            return SidePathUtils.spiltSidePaths(new LinkedBlockingDeque<>(), 0);
        }
        LinkedList<SidePath> temp = new LinkedList<>(sidePaths);
        int index = 0;
        SidePath firstSidePath = temp.poll();
        Double cost = getPiecemealCost(firstSidePath);
        if (cost >= time || temp.size() == 0) {
            index = 0;
        } else {
            if (firstSidePath.getT0() != 1D) {
                cost += getAngleCost(firstSidePath.getStartMarkerId(), firstSidePath.getEndMarkerId(), temp.getFirst().getEndMarkerId());
            }
            for (int i = 0; i < temp.size() - 1; i++) {
                cost += mapGraph.getDGraphOfAGVMap().getCost(temp.get(i).getStartMarkerId(), temp.get(i).getEndMarkerId(), temp.get(i + 1).getEndMarkerId());
                index++;
                if (cost >= time) {
                    break;
                }
            }
            cost += mapGraph.getDGraphOfAGVMap().getDirectCost(temp.get(temp.size() - 1));
            index++;
        }
        //logger.debug("cost:" + cost);
        return SidePathUtils.spiltSidePaths(sidePaths, index);
    }

    /**
     * print DirectGraph edge cost current time
     */
    public static String printDGraphCost() {
        StringBuilder stringBuilder = new StringBuilder();
        DirectedGraph dGraph = mapGraph.getDGraphOfAGVMap();
        Set<DirectedEdge> allEdges = dGraph.getAllEdges();
        for (DirectedEdge edge : allEdges) {
            stringBuilder.append(edge.toString() + " ").append(System.lineSeparator());
        }
        return stringBuilder.toString();
    }

    public static String printSidePaths(BlockingDeque<SidePath> sidePaths) {
        if (CollectionUtils.isEmpty(sidePaths)) return "";
        StringBuilder stringBuilder = new StringBuilder();
        try {
            if (!CollectionUtils.isEmpty(sidePaths)) {
                for (SidePath sidePath : sidePaths) {
                    String startCode = getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                    String endCode = getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                    String format = MessageFormat.format("side path: [{0}]->[{1}], t0:[{2}], t1:[{3}], id:[{4}]", startCode, endCode, sidePath.getT0(), sidePath.getT1(), sidePath.getId());
                    stringBuilder.append(format);
                    stringBuilder.append(System.lineSeparator());
                }
            }
        } catch (Exception e) {
            logger.error("printSidePaths toString出错, " + e);
        }
        return stringBuilder.toString();
    }

    public static String printSidePaths(Set<SidePath> sidePaths) {
        if (CollectionUtils.isEmpty(sidePaths)) return "";
        StringBuilder stringBuilder = new StringBuilder();
        try {
            if (!CollectionUtils.isEmpty(sidePaths)) {
                for (SidePath sidePath : sidePaths) {
                    String startCode = getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                    String endCode = getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                    stringBuilder.append(MessageFormat.format("[{0}]->[{1}]", startCode, endCode)).append(",");
                }
            }
        } catch (Exception e) {
            logger.error("printSidePaths toString出错, " + e);
        }
        return stringBuilder.toString();
    }

    /**
     * set this agv position to side path, include t0 value in the side path
     *
     * @param agvCode
     * @param location
     */
    public static void setAGVPosition(String agvCode, VehicleLocation location) {
        if (agvCode == null || location == null) return;
        if (getActiveAgvMapIds().size() <= 0) {
            return;
        }
        if (location.getMarker() != null) {
            String nodeId = location.getMarker().getId();
            Set<DirectedEdge> inEdges = mapGraph.getDGraphOfAGVMap().getInEdges(nodeId);
            Set<DirectedEdge> outEdges = mapGraph.getDGraphOfAGVMap().getOutEdges(nodeId);
            inEdges.forEach(edge -> edge.addAgvLocation(agvCode, 1D));
            outEdges.forEach(edge -> edge.addAgvLocation(agvCode, 0D));
            Set<DirectedEdge> edgeSet = new HashSet<>();
            Set<DirectedEdge> allEdges = mapGraph.getDGraphOfAGVMap().getAllEdges();
            if (allEdges != null) {
                edgeSet.addAll(allEdges);
                edgeSet.removeAll(inEdges);
                edgeSet.removeAll(outEdges);
                edgeSet.forEach(edge -> edge.removeAgvLocation(agvCode));
            }
        } else if (location.getSidePaths() != null) {
            List<SidePath> sidePaths = location.getSidePaths();
            List<DirectedEdge> edgeList = new CopyOnWriteArrayList<>();
            for (SidePath sidePath : sidePaths) {
                DirectedEdge edge = mapGraph.getDGraphOfAGVMap().getEdgeBySidePath(sidePath);
                edge.addAgvLocation(agvCode, sidePath.getT0());
                edgeList.add(edge);
            }
            Set<DirectedEdge> edgeSet = new HashSet<>();
            Set<DirectedEdge> allEdges = mapGraph.getDGraphOfAGVMap().getAllEdges();
            if (allEdges != null) {
                edgeSet.addAll(allEdges);
                edgeSet.removeAll(edgeList);
                edgeSet.forEach(edge -> edge.removeAgvLocation(agvCode));
            }
        }
    }

    /**
     * 清理该AGV所占用的所有路径
     *
     * @param agvCode
     */
    public static void clearAGVPosition(String agvCode) {
        if (agvCode == null) return;
        Set<DirectedEdge> allEdges = mapGraph.getDGraphOfAGVMap().getAllEdges();
        for (DirectedEdge edge : allEdges) {
            edge.removeAgvLocation(agvCode);
        }
        locationService.releaseEdgeWeight(agvCode);
    }

    /**
     * query whether this agv can pass through these side paths
     *
     * @param agvCode
     * @param sidePaths
     * @return
     */
    public static boolean queryAGVPassThroughThisEdges(String agvCode, BlockingQueue<SidePath> sidePaths) {
        if (agvCode == null || CollectionUtils.isEmpty(sidePaths)) return false;
        Set<DirectedEdge> edges = new HashSet<>();
        sidePaths.forEach(s ->
                edges.add(mapGraph.getDGraphOfAGVMap().getEdgeBySidePath(s))
        );
        //boolean agvPassThroughEdges = true;
        for (DirectedEdge edge : edges) {
            ConcurrentHashMap<String, Double> agvPosition = edge.getAgvPosition();
            Double maxT = Double.NEGATIVE_INFINITY;
            String maxAgvCode = null;
            for (Map.Entry<String, Double> agvCodeToT : agvPosition.entrySet()) {
                String tAgvCode = agvCodeToT.getKey();
                Double tt = agvCodeToT.getValue();
                if (tt > maxT) {
                    maxT = tt;
                    maxAgvCode = tAgvCode;
                }
            }
            if (maxAgvCode != null && !agvCode.equals(maxAgvCode)) {
                return false;
            }
        }
        return true;
    }

    public static Set<String> getActiveAgvMapIds() {
        return new HashSet<>(mapGraph.getActiveAgvMapIds().keySet());
    }

    public static MapGraph getMapGraph() {
        return mapGraph;
    }
}
