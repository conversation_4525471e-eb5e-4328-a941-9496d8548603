package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.MARKER_NAVIGATION_TYPE_NORMAL;

/**
 * <AUTHOR>
 * @Date :Created in 18:43 2020/8/7
 * @Description :
 * @Modified By :
 * @Version :
 */
public class MapGraphUtil {

    private static final Logger logger = LoggerFactory.getLogger(MapGraphUtil.class);

    private static MapGraph mapGraph = (MapGraph) ApplicationUtils.getBean("mapGraph");

    /**
     * get Origin DirectedGraph
     *
     * @return
     */
    public static DirectedGraph getOriginDirectedGraph() {
        return mapGraph.getDGraphOfAGVMap();
    }

    /**
     * get clone DirectedGraph
     *
     * @return
     */
    public static DirectedGraph getCloneDirectedGraph() {
        return mapGraph.getDGraphOfAGVMap().clone();
    }

    /**
     * get side path by start end marker id
     *
     * @param startMarkerId
     * @param endMarkerId
     * @return
     */
    public static SidePath getSidePathByStartEndMarkerId(String startMarkerId, String endMarkerId) {
        if (startMarkerId == null || endMarkerId == null) return null;
        SidePath sidePath = mapGraph.getStartEndMarkerIdToSidePath().get(startMarkerId + endMarkerId);
        if (sidePath != null) return sidePath.clone();
        else return null;
    }

    /**
     * get reverse side paths by side path ids
     *
     * @param sidePathIds
     * @return
     */
    public static Set<String> getReverseSidePathIds(Collection<String> sidePathIds) {
        if (CollectionUtils.isEmpty(sidePathIds)) return new HashSet<>();
        Set<String> reverseSidePathIds = new HashSet<>();
        sidePathIds.forEach(sId -> {
            SidePath rs = getReverseSidePath(sId);
            if (rs != null) {
                reverseSidePathIds.add(rs.getId());
            }
        });
        return reverseSidePathIds;
    }

    /**
     * get reverse side paths by side path id
     *
     * @param sidePathId
     * @return
     */
    public static SidePath getReverseSidePath(String sidePathId) {
        if (sidePathId == null) return null;
        SidePath sidePath = getSidePathBySidePathId(sidePathId);
        return getSidePathByStartEndMarkerId(sidePath.getEndMarkerId(), sidePath.getStartMarkerId());
    }

    public static Set<String> getAdjacentSidePathId(String markerId) {
        if (markerId == null) return new HashSet<>();
        Set<DirectedEdge> inOutEdges = mapGraph.getDGraphOfAGVMap().getInOutEdges(markerId);
        return inOutEdges.stream().map(DirectedEdge::getSidePathId).collect(Collectors.toSet());
    }

    public static Set<String> getInSidePathId(String markerId) {
        if (markerId == null) return new HashSet<>();
        Set<DirectedEdge> inOutEdges = mapGraph.getDGraphOfAGVMap().getInEdges(markerId);
        return inOutEdges.stream().map(DirectedEdge::getSidePathId).collect(Collectors.toSet());
    }

    public static Set<String> getOutSidePathId(String markerId) {
        if (markerId == null) return new HashSet<>();
        Set<DirectedEdge> inOutEdges = mapGraph.getDGraphOfAGVMap().getOutEdges(markerId);
        return inOutEdges.stream().map(DirectedEdge::getSidePathId).collect(Collectors.toSet());
    }

    //从markerId得到Marker对象
    public static Marker getMarkerByMarkerId(String markerId) {
        if (markerId == null) return null;
        return mapGraph.getMarkerIdToMarker().get(markerId);
    }

    public static Path getPathByPathId(String pathId) {
        if (pathId == null) return null;
        return mapGraph.getPathIdToPath().get(pathId);
    }

    //从sidepathId得到sidepath对象 获取地图中的所有
    public static SidePath getSidePathBySidePathId(String sidePathId) {
        if (sidePathId == null) return null;
        SidePath sidePath = mapGraph.getSidePathIdToSidePath().get(sidePathId);
        if (sidePath != null) {
            return sidePath.clone();
        } else return null;
    }

    //获取地图中的所有marker点
    public static List<Marker> getMarkersByAGVMapId(String agvMapId) {
        if (agvMapId == null) return new ArrayList<>();
        return mapGraph.getMarkersByAGVMapId(agvMapId);
    }

    public static Marker getMarkerByAGVMapIdAndMarkerCode(String agvMapId, String markerCode) {
        List<Marker> markers = MapGraphUtil.getMarkersByAGVMapId(agvMapId);
        if (CollectionUtils.isEmpty(markers) || StringUtils.isEmpty(markerCode)) {
            return null;
        }
        markers = markers.stream().filter(marker -> markerCode.equals(marker.getCode())).collect(Collectors.toList());
        return !CollectionUtils.isEmpty(markers) ? markers.get(0) : null;
    }

    //获取地图中的所有sidepath
    public static List<SidePath> getSidePathsByAGVMapId(String agvMapId) {
        if (agvMapId == null) return new ArrayList<>();
        return mapGraph.getSidePathsByAGVMapId(agvMapId);
    }

    /**
     * get all enable markers
     *
     * @return
     */
    public static Set<Marker> getEnableMarkers() {
        Set<Marker> markers = new HashSet<>();
        Set<Marker> activeMarkers = mapGraph.getActiveMarkers();
        List<Marker> disableMarkers = mapGraph.getDisableMarkers();
        markers.addAll(activeMarkers);
        markers.removeAll(disableMarkers);
        return markers;
    }

    //检查marker点是否不存在或者被禁用掉 false:被禁用或不存在
    public static boolean checkAimMarker(String aimMarkerId) {
        if (aimMarkerId == null) return false;
        Marker marker = mapGraph.getMarkerIdToMarker().get(aimMarkerId);
        if (marker == null) {
            return false;
        }
        if (!mapGraph.getActiveMarkers().contains(marker)) {
            return false;
        }
        if (mapGraph.getDisableMarkers().contains(marker)) {
            return false;
        }
        return true;
    }

    /**
     * 启用地图接口
     *
     * @param agvMapId
     */
    public static void addAGVMap(String agvMapId) {
        if (agvMapId == null) return;
        removeAGVMap(agvMapId);
        mapGraph.addAGVMap(agvMapId);
    }

    /**
     * 禁用地图接口
     *
     * @param agvMapId
     */
    public static void removeAGVMap(String agvMapId) {
        if (agvMapId == null) return;
        mapGraph.removeAGVMap(agvMapId);
    }

    /**
     * get all active markers
     *
     * @return
     */
    public static Set<Marker> getActiveMarkers() {
        return mapGraph.getActiveMarkers();
    }

    /**
     * get marker navigation type
     *
     * @param markerId
     * @return
     */
    public static Integer getMarkerNavigationType(String markerId) {
        if (markerId == null) return MARKER_NAVIGATION_TYPE_NORMAL;
        Integer markerNavigationType = mapGraph.getMarkerIdToMarkerNavigationType().get(markerId);
        if (markerNavigationType == null) {
            return MARKER_NAVIGATION_TYPE_NORMAL;
        }
        return markerNavigationType;
    }


    /**
     * get elevator id by elevator marker id
     *
     * @param elevatorMarkerId
     * @return
     */
    public static String getElevatorIdByElevatorMarkerId(String elevatorMarkerId) {
        if (elevatorMarkerId == null) return null;
        return mapGraph.getElevatorMarkerIdToElevatorId().get(elevatorMarkerId);
    }

    /**
     * init floor info when changed elevator or floor
     */
    public static void initFloor() {
        mapGraph.initFloor();
    }

    /**
     * modify autoWeight by side path id according to the type
     *
     * @param sidePathId
     * @param type
     * @param autoWeight
     */
    public static void modifyAutoWeight(String agvCode, String sidePathId, String type, Double autoWeight) {
        if (agvCode == null || sidePathId == null || type == null || autoWeight == null) return;
        SidePath sidePath = mapGraph.getSidePathIdToSidePath().get(sidePathId);
        if (sidePath != null) {
            String n1 = sidePath.getStartMarkerId();
            String n2 = sidePath.getEndMarkerId();
            mapGraph.getDGraphOfAGVMap().getEdgeByStartEndNode(n1, n2).modifyAutoWeight(type, autoWeight);
        }
    }

    public static void modifyUserWeight(VehicleLocation location, String type, Double userWeight) {
        if (type == null || userWeight == null) return;
        Set<DirectedEdge> edges = new HashSet<>();
        if (location != null) {
            if (location.getMarker() != null) {
                String nodeId = location.getMarker().getId();
                Set<DirectedEdge> inEdges = mapGraph.getDGraphOfAGVMap().getInEdges(nodeId);
                Set<DirectedEdge> outEdges = mapGraph.getDGraphOfAGVMap().getOutEdges(nodeId);
                edges.addAll(inEdges);
                edges.addAll(outEdges);
            } else if (location.getSidePaths() != null) {
                List<SidePath> sidePaths = location.getSidePaths();
                sidePaths.forEach(sidePath -> {
                    DirectedEdge edge = mapGraph.getDGraphOfAGVMap().getEdgeBySidePath(sidePath);
                    DirectedEdge reverseEdge = mapGraph.getDGraphOfAGVMap().getReverseEdgeBySidePath(sidePath);
                    if (edge != null) {
                        edges.add(edge);
                    }
                    if (reverseEdge != null) {
                        edges.add(edge);
                    }
                });
            }
        }
        edges.forEach(edge -> {
            edge.modifyUserWeight(type, userWeight);
        });
    }


    /**
     * 判断边是否被包含 返回true代表包含 false代表不包含
     */
    public static Boolean containedSidePath(SidePath sidePath) {
        if (sidePath == null) return false;
        DirectedEdge edge = mapGraph.getDGraphOfAGVMap().getEdgeBySidePath(sidePath);
        if (edge == null) {
            return false;
        }
        if (edge.getWeight() < Double.POSITIVE_INFINITY) {
            return true;
        }
        return false;
    }


    /**
     * get velocity cost of side path from t0 to t=1;
     *
     * @param sidePath
     * @return
     */
    public static Double getPiecemealCost(SidePath sidePath) {
        if (sidePath != null) return mapGraph.getDGraphOfAGVMap().getPiecemealCost(sidePath);
        else return 0D;
    }

    /**
     * 获取路径上的权重之和
     *
     * @param sidePath
     * @return
     */
    public static Double getSidePathCost(SidePath sidePath) {
        if (sidePath != null) return mapGraph.getDGraphOfAGVMap().getSidePathCost(sidePath);
        else return 0D;
    }

    /**
     * get angle cost by continuous node n1,n2,n3
     *
     * @param n1
     * @param n2
     * @param n3
     * @return
     */
    public static Double getAngleCost(String n1, String n2, String n3) {
        if (n1 == null || n2 == null || n3 == null) return 0D;
        return mapGraph.getDGraphOfAGVMap().getAngleCost(n1, n2, n3);
    }

    /**
     * cal list of side paths cost(angle, velocity, autoWeight, userWeight)
     *
     * @param sidePaths
     * @return
     */
    public static Double getCost(LinkedBlockingDeque<SidePath> sidePaths) {
        if (sidePaths == null || sidePaths.size() == 0) return 0D;
        LinkedList<SidePath> temp = new LinkedList<>(sidePaths);
        SidePath firstSidePath = temp.poll();
        Double cost = getPiecemealCost(firstSidePath);
        if (temp.size() != 0) {
            if (firstSidePath.getT0() != 1D) {
                cost += getAngleCost(firstSidePath.getStartMarkerId(), firstSidePath.getEndMarkerId(), temp.getFirst().getEndMarkerId());
            }
            for (int i = 0; i < temp.size() - 1; i++) {
                cost += mapGraph.getDGraphOfAGVMap().getCost(temp.get(i).getStartMarkerId(), temp.get(i).getEndMarkerId(), temp.get(i + 1).getEndMarkerId());
            }
            cost += mapGraph.getDGraphOfAGVMap().getDirectCost(temp.get(temp.size() - 1));
        }
        return cost;
    }

    /**
     * print DirectGraph edge cost current time
     */
    public static void printDGraphCost() {
        DirectedGraph dGraph = mapGraph.getDGraphOfAGVMap();
        Set<DirectedEdge> allEdges = dGraph.getAllEdges();
        for (DirectedEdge edge : allEdges) {
            System.out.print(edge.toString() + " ");
        }
        System.out.println();
    }

    public static String printSidePaths(Collection<SidePath> sidePaths) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            if (!CollectionUtils.isEmpty(sidePaths)) {
                for (SidePath sidePath : sidePaths) {
                    String startCode = getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
                    String endCode = getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
                    String format = MessageFormat.format("side path: [{0}]->[{1}], t0:[{2}], t1:[{3}], id:[{4}]", startCode, endCode, sidePath.getT0(), sidePath.getT1(), sidePath.getId());
                    stringBuilder.append(format);
                    stringBuilder.append(System.lineSeparator());
                }
            }
        } catch (Exception e) {
            logger.error("printSidePaths toString出错, " + e);
        }
        return stringBuilder.toString();
    }

    /**
     * set this agv position to side path, include t0 value in the side path
     *
     * @param agvCode
     * @param location
     */
    public static void setAGVPosition(String agvCode, VehicleLocation location) {
        if (agvCode == null || location == null) return;
        if (getActiveAgvMapIds().size() <= 0) {
            return;
        }
        if (location.getMarker() != null) {
            String nodeId = location.getMarker().getId();
            Set<DirectedEdge> inEdges = mapGraph.getDGraphOfAGVMap().getInEdges(nodeId);
            Set<DirectedEdge> outEdges = mapGraph.getDGraphOfAGVMap().getOutEdges(nodeId);
            inEdges.forEach(edge -> edge.addAgvLocation(agvCode, 1D));
            outEdges.forEach(edge -> edge.addAgvLocation(agvCode, 0D));
            Set<DirectedEdge> edgeSet = new HashSet<>();
            Set<DirectedEdge> allEdges = mapGraph.getDGraphOfAGVMap().getAllEdges();
            if (allEdges != null) {
                edgeSet.addAll(allEdges);
                edgeSet.removeAll(inEdges);
                edgeSet.removeAll(outEdges);
                edgeSet.forEach(edge -> edge.removeAgvLocation(agvCode));
            }
        } else if (location.getSidePaths() != null) {
            List<SidePath> sidePaths = location.getSidePaths();
            List<DirectedEdge> edgeList = new CopyOnWriteArrayList<>();
            for (SidePath sidePath : sidePaths) {
                DirectedEdge edge = mapGraph.getDGraphOfAGVMap().getEdgeBySidePath(sidePath);
                edge.addAgvLocation(agvCode, sidePath.getT0());
                edgeList.add(edge);
            }
            Set<DirectedEdge> edgeSet = new HashSet<>();
            Set<DirectedEdge> allEdges = mapGraph.getDGraphOfAGVMap().getAllEdges();
            if (allEdges != null) {
                edgeSet.addAll(allEdges);
                edgeSet.removeAll(edgeList);
                edgeSet.forEach(edge -> edge.removeAgvLocation(agvCode));
            }
        }
    }

    /**
     * 清理该AGV所占用的所有路径
     *
     * @param agvCode
     */
    public static void clearAGVPosition(String agvCode) {
        if (agvCode == null) return;
        Set<DirectedEdge> allEdges = mapGraph.getDGraphOfAGVMap().getAllEdges();
        for (DirectedEdge edge : allEdges) {
            edge.removeAgvLocation(agvCode);
        }
    }

    /**
     * query whether this agv can pass through these side paths
     *
     * @param agvCode
     * @param sidePaths
     * @return
     */
    public static boolean queryAGVPassThroughThisEdges(String agvCode, BlockingQueue<SidePath> sidePaths) {
        if (agvCode == null || CollectionUtils.isEmpty(sidePaths)) return false;
        Set<DirectedEdge> edges = new HashSet<>();
        sidePaths.forEach(s ->
                edges.add(mapGraph.getDGraphOfAGVMap().getEdgeBySidePath(s))
        );
        //boolean agvPassThroughEdges = true;
        for (DirectedEdge edge : edges) {
            ConcurrentHashMap<String, Double> agvPosition = edge.getAgvPosition();
            Double maxT = Double.NEGATIVE_INFINITY;
            String maxAgvCode = null;
            for (Map.Entry<String, Double> agvCodeToT : agvPosition.entrySet()) {
                String tAgvCode = agvCodeToT.getKey();
                Double tt = agvCodeToT.getValue();
                if (tt > maxT) {
                    maxT = tt;
                    maxAgvCode = tAgvCode;
                }
            }
            if (maxAgvCode != null && !agvCode.equals(maxAgvCode)) {
                return false;
            }
        }
        return true;
    }

    public static Set<String> getActiveAgvMapIds() {
        return new HashSet<>(mapGraph.getActiveAgvMapIds().keySet());
    }

    public static void removeAllAGVMaps() {
        Set<String> activeAgvMapIds = getActiveAgvMapIds();
        if (CollectionUtils.isEmpty(activeAgvMapIds)) return;
        activeAgvMapIds.forEach(agvMapId -> mapGraph.removeAGVMap(agvMapId));
    }

    public static void addAGVMaps(Collection<String> agvMapIds) {
        if (CollectionUtils.isEmpty(agvMapIds)) return;
        agvMapIds.forEach(agvMapId -> mapGraph.addAGVMap(agvMapId));
    }

    //计算路径里程，单位：m
    public static Double getTotalLengthFromSidePath(Collection<SidePath> sidePaths) {
        if (sidePaths == null) return 0d;
        Double length = 0d;
        for (SidePath sidePath : sidePaths) {
            length += sidePath.getLength();
        }
        return length;
    }

    //计算路径里程，单位：m
    public static Double getTotalLengthFromPath(Collection<Path> paths) {
        if (paths == null) return 0d;
        Double length = 0d;
        for (Path path : paths) {
            length += path.getLength();
        }
        return length;
    }
}
