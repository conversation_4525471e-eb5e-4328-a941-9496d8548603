package com.youibot.agv.scheduler.engine.service.mechanics;

import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019/7/16 16:56
 */
public interface ManipulatorArmApiService {

    /**
     * 机械臂登录
     * @param ip
     */
    void login(String ip, Map<String, Object> param) throws IOException;

    /**
     * 控制机械臂
     * @param ip
     * @param param
     * @throws IOException
     */
    Map<String, Object> control(String ip, Map<String, Object> param) throws IOException, InterruptedException;

    /**
     * 查询机械臂状态
     * @param ip
     * @return
     */
    Map<String, Object> queryStatus(String ip) throws IOException;

    /**
     * 查询机械臂状态
     * @param client
     * @return
     */
    Map<String, Object> queryStatus(AGVSocketClient client) throws IOException;

    /**
     * 机械臂示教控制
     * @param ip
     * @param param
     * @throws IOException
     */
    Map<String, Object> demoControl(String ip, Map<String, Object> param) throws IOException;

}
