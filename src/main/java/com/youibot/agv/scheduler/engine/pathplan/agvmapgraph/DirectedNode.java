package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.entity.Marker;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date :Created in 14:38 2020/8/7
 * @Description :
 * @Modified By :
 * @Version :
 */

public final class DirectedNode implements Serializable {
    public final String id;
    public final String code;
    public DirectedNode parent = null;
    public DirectedNode child = null;
    public final double x;
    public final double y;
    public Double g = Double.POSITIVE_INFINITY;
    public Double rhs = Double.POSITIVE_INFINITY;
    public Pair<Double, Double> k = new Pair<>(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);

    public DirectedNode(String id, String code, double x, double y) {
        this.id = id;
        this.code = code;
        this.x = x;
        this.y = y;
    }

    public DirectedNode(Marker marker) {
        this.id = marker.getId();
        this.code = marker.getCode();
        this.x = marker.getX();
        this.y = marker.getY();
    }

    public String getId() {
        return id;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof DirectedNode))
            return false;
        DirectedNode that = (DirectedNode) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
