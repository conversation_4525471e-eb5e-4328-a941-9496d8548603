package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.entity.Marker;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date :Created in 14:38 2020/8/7
 * @Description :
 * @Modified By :
 * @Version :
 */

public final class DirectedNode implements Serializable {
    public final String id;
    public final String code;
    public final double x;
    public final double y;


    public DirectedNode(String id, String code, double x, double y) {
        this.id = id;
        this.code = code;
        this.x = x;
        this.y = y;
    }

    public DirectedNode(Marker marker) {
        this.id = marker.getId();
        this.code = marker.getCode();
        this.x = marker.getX();
        this.y = marker.getY();
    }

    public String getId() {
        return id;
    }

    public String getCode() {
        return code;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof DirectedNode))
            return false;
        DirectedNode that = (DirectedNode) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
