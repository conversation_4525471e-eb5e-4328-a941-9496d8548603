package com.youibot.agv.scheduler.engine.pathplan.data;

import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.youibot.agv.scheduler.constant.ActionConstant.*;
import static com.youibot.agv.scheduler.constant.MapConstant.*;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:52 19-11-4
 * @Description :
 * @Modified By :
 * @Version :
 */
@Component
public class MapMatrixData implements Cloneable {
    private static final Logger LOGGER = LoggerFactory.getLogger(MapMatrixData.class);

    @Autowired
    private MarkerService markerService;

    @Autowired
    private SidePathService sidePathService;

    @Autowired
    private ElevatorService elevatorService;

    @Autowired
    private FloorService floorService;

    @Autowired
    private AdjustActionService adjustActionService;

    @Autowired
    private LocationService locationService;

    //单向映射 Key:Marker.getId(),Value:Marker
    private Map<String, Marker> markerIdToMarker = new ConcurrentHashMap<>();
    //保存所有激活的地图
    private Set<String> activeAgvMapIds = new HashSet<>();

    public Set<String> getActiveAgvMapIds() {
        return activeAgvMapIds;
    }

    //保存所有激活的地图点
    private Set<Marker> activeMarkers = new HashSet<>();

    //单向映射 Key:ElevatorId,Value:Marker.getId()
    private Map<String, Set<String>> elevatorIdToMarkerIds = new ConcurrentHashMap<>();

    //单向映射 Key:elevatorMarkerId,Value:ElevatorId
    private Map<String, String> elevatorMarkerIdToElevatorId = new ConcurrentHashMap<>();

    //单向映射 Key:Marker.getId(),Value:Marker
    private Map<String, SidePath> sidePathIdToSidePath = new ConcurrentHashMap<>();

    //单向映射 Key:agvMapId,Value:Markers
    private Map<String, List<Marker>> agvMapIdToMarkers = new ConcurrentHashMap<>();

    //单向映射 Key:agvMapId,Value:Markers
    private Map<String, List<SidePath>> agvMapIdToSidePaths = new ConcurrentHashMap<>();

    //单向映射 Key:startMarker.getId()+endMarker.getId(),Value:SidePath
    private Map<String, SidePath> startEndMarkerIdToSidePath = new ConcurrentHashMap<>();

    //存储所有与Marker点i相连的Marker点（以i为起点）
    private Map<String, Set<String>> iTojs = new ConcurrentHashMap<>();

    //存储所有与Marker点j相连的Marker点（以j为终点）
    private Map<String, Set<String>> isToj = new ConcurrentHashMap<>();

    //存储所有被禁用的点
    private Set<Marker> disableMarkers = new HashSet<>();

    //存储激活地图中所有被禁用的点
    private Set<Marker> disableMarkersInActiveAGVMap = new HashSet<>();

    //存储所有被禁用的路径
    private Set<SidePath> disableSidePaths = new HashSet<>();

    //原始无占用邻接矩阵,用Map实现动态调整
    private ConcurrentHashMap<String, Map<String, Double>> matrix = new ConcurrentHashMap<>();

    //单向映射 Key:elevatorIds,Value:Elevators
    private Map<String, Elevator> elevatorIdToElevator = new ConcurrentHashMap<>();

    //单向映射 Key:markerId,Value:路径导航类型
    private Map<String, Integer> markerIdToMarkerNavigationType = new ConcurrentHashMap<>();

    public void init() {
        this.clean();
        initElevator();
    }

    private void initElevator() {
        List<Elevator> elevatorAll = elevatorService.findAll();
        elevatorAll.forEach(elevator -> elevatorIdToElevator.put(elevator.getId(), elevator));
    }

    //添加地图接口
    public void addAGVMap(String agvMapId) {
        activeAgvMapIds.add(agvMapId);
        //获取该地图中所有的marker点和路径
        List<Marker> markersInAGVMap = markerService.selectByAGVMapId(agvMapId);
        List<SidePath> sidePathsInAGVMap = sidePathService.selectByAGVMapId(agvMapId);
        //存储marker点和初始化邻接矩阵对角线上的元素为零
        markersInAGVMap.forEach(marker -> {
            markerIdToMarker.put(marker.getId(), marker);
            Map<String, Double> map = new ConcurrentHashMap<>();
            map.put(marker.getId(), 0D);
            matrix.put(marker.getId(), map);
        });
//        locationService.buildKDTree(agvMapId, markersInAGVMap);
        //所有所有的激活marker点
        activeMarkers.addAll(markersInAGVMap);
        //存储该地图下的所有marker点和路径
        agvMapIdToMarkers.put(agvMapId, markersInAGVMap);
        agvMapIdToSidePaths.put(agvMapId, sidePathsInAGVMap);
        //处理楼层信息，保存地图对应的电梯点
        List<Floor> floors = floorService.selectByAGVMapId(agvMapId);
        initFloor(floors);
        addSidePath(sidePathsInAGVMap);
        //生成虚拟的SidePath，只从已经激活的地图中选择电梯点，未激活的地图生成虚拟SidePath
        List<Marker> elevatorMarkers = addElevatorVirtualSidePath(agvMapId);
        //存储所有进入电梯点的marker点
        sidePathsInAGVMap.forEach(sidePath -> {
            if (elevatorMarkers.contains(markerIdToMarker.get(sidePath.getEndMarkerId()))) {
                markerIdToMarkerNavigationType.put(sidePath.getStartMarkerId(), MARKER_NAVIGATION_TYPE_IN_OUT);
            }
        });
        //存储调整点
        List<AdjustAction> adjustActions = adjustActionService.selectByAGVMapId(agvMapId);
        for (AdjustAction adjustAction : adjustActions) {
            markerIdToMarkerNavigationType.put(adjustAction.getMarkerId(), MARKER_NAVIGATION_TYPE_ADJUST);
            markerIdToMarkerNavigationType.put(adjustAction.getDestMarkerId(), MARKER_NAVIGATION_TYPE_ADJUST_DEST);
        }
        //将禁用的路径和marker点删除
        List<Marker> disableMarkersInAGVMap = markerService.selectByAGVMapIdAndUsageStatus(agvMapId, MARKER_USAGE_STATUS_DISABLE);
        disableMarkersInActiveAGVMap.addAll(disableMarkersInAGVMap);
        disableMarkers.addAll(disableMarkersInAGVMap);
        removeMarker(disableMarkersInAGVMap);
        List<SidePath> disableSidePathsInAGVMap = sidePathService.selectByAGVMapIdAndUsageStatus(agvMapId, PATH_USAGE_STATUS_DISABLE);
        disableSidePaths.addAll(disableSidePathsInAGVMap);
        removeSidePath(disableSidePathsInAGVMap);
        LOGGER.info("已添加地图，AGVMapId=[{}]", agvMapId);
    }

    private List<Marker> addElevatorVirtualSidePath(String agvMapId) {
        List<SidePath> sidePaths = new ArrayList<>();
        List<Marker> elevatorMarkers = markerService.selectByAGVMapIdAndType(agvMapId, MARKER_TYPE_ELEVATOR);
        for (Marker elevatorMarker : elevatorMarkers) {
            String thisElevatorMarkerId = elevatorMarker.getId();
            markerIdToMarkerNavigationType.put(thisElevatorMarkerId, MARKER_NAVIGATION_TYPE_ELEVATOR);
            String elevatorId = elevatorMarkerIdToElevatorId.get(thisElevatorMarkerId);
            if (elevatorId == null) continue;
            for (String thatElevatorMarkerId : elevatorIdToMarkerIds.get(elevatorId)) {
                if (thisElevatorMarkerId.equals(thatElevatorMarkerId)) continue;
                SidePath sidePath1 = new SidePath(thisElevatorMarkerId, thatElevatorMarkerId, 12D);
                SidePath sidePath2 = new SidePath(thatElevatorMarkerId, thisElevatorMarkerId, 12D);
                sidePaths.add(sidePath1);
                sidePaths.add(sidePath2);
            }
        }
        addSidePath(sidePaths);
        return elevatorMarkers;
    }

    private void addSidePath(List<SidePath> sidePaths) {
        sidePaths.forEach(sidePath -> {
            String startMarkerId = sidePath.getStartMarkerId();
            String endMarkerId = sidePath.getEndMarkerId();
            startEndMarkerIdToSidePath.put(startMarkerId + endMarkerId, sidePath);
            //从marker点i开始能一步到达的marker点j的集合
            if (iTojs.get(startMarkerId) == null) {
                Set<String> set = new HashSet<>();
                set.add(endMarkerId);
                iTojs.put(startMarkerId, set);
            } else {
                iTojs.get(startMarkerId).add(endMarkerId);
            }
            //所有能到达marker点j的点集合
            if (isToj.get(endMarkerId) == null) {
                Set<String> set = new HashSet<>();
                set.add(startMarkerId);
                isToj.put(endMarkerId, set);
            } else {
                isToj.get(endMarkerId).add(startMarkerId);
            }
            //将所有路径加入邻接矩阵中
            if (matrix.get(startMarkerId) != null) {
                matrix.get(startMarkerId).put(endMarkerId, sidePath.getLength());
            } else {
                Map<String, Double> map = new ConcurrentHashMap<>();
                map.put(startMarkerId, 0D);
                map.put(endMarkerId, sidePath.getLength());
                matrix.put(startMarkerId, map);
            }
        });
        //存储路径信息
        sidePaths.forEach(sidePath -> sidePathIdToSidePath.put(sidePath.getId(), sidePath));
    }

    private void removeSidePath(List<SidePath> sidePaths) {
        sidePaths.forEach(sidePath ->
                {
                    if (matrix.get(sidePath.getStartMarkerId()) != null) {
                        matrix.get(sidePath.getStartMarkerId()).remove(sidePath.getEndMarkerId());
                    }
                }
        );
    }

    private void removeMarker(List<Marker> markers) {
        markers.forEach(disableMarker -> {
            Set<String> set = isToj.get(disableMarker.getId());
            set.forEach(startMarkerId -> {
                if (matrix.get(startMarkerId) != null) {
                    matrix.get(startMarkerId).remove(disableMarker.getId());
                }
            });
        });
    }

    /**
     * 初始化电梯数据
     *
     * @param floors
     */
    private void initFloor(List<Floor> floors) {
        //处理楼层信息，保存地图对应的电梯点
        floors.forEach(floor -> {
            //存储elevatorMarkerId和电梯ElevatorId的对应关系
            elevatorMarkerIdToElevatorId.put(floor.getMarkerId(), floor.getElevatorId());
            //存储电梯Id对应的多个markerId
            if (elevatorIdToMarkerIds.get(floor.getElevatorId()) == null) {
                Set<String> set = new HashSet<>();
                set.add(floor.getMarkerId());
                elevatorIdToMarkerIds.put(floor.getElevatorId(), set);
            } else {
                elevatorIdToMarkerIds.get(floor.getElevatorId()).add(floor.getMarkerId());
            }
        });
        activeAgvMapIds.forEach(agvMapId -> addElevatorVirtualSidePath(agvMapId));
    }

    public void initFloor() {
        //处理楼层信息，保存地图对应的电梯点
        elevatorMarkerIdToElevatorId.clear();
        elevatorIdToMarkerIds.clear();
        initElevator();
        List<Floor> floors = floorService.findAll();
        initFloor(floors);
    }

    //删除地图接口
    public void removeAGVMap(String agvMapId) {
        activeAgvMapIds.remove(agvMapId);
        //List<Marker> markersInAGVMap = markerService.selectByAGVMapId(agvMapId);
        List<Marker> markersInAGVMap = agvMapIdToMarkers.get(agvMapId);
        activeMarkers.removeAll(markersInAGVMap);
        List<SidePath> sidePathsInAGVMap = sidePathService.selectByAGVMapId(agvMapId);
        sidePathsInAGVMap.forEach(sidePath -> {
            if (matrix.get(sidePath.getStartMarkerId()) != null) {
                matrix.get(sidePath.getStartMarkerId()).remove(sidePath.getEndMarkerId());
            }
        });
        markersInAGVMap.forEach(marker -> matrix.remove(marker.getId()));
        //List<Marker> disableMarkersInAGVMap = markerService.selectByAGVMapIdAndUsageStatus(agvMapId, MARKER_USAGE_STATUS_DISABLE);
        List<SidePath> disableMarkersInAGVMap = agvMapIdToSidePaths.get(agvMapId);
        disableMarkersInActiveAGVMap.removeAll(disableMarkersInAGVMap);
        LOGGER.info("已删除地图，AGVMapId=[{}]", agvMapId);
    }

    public void removeAllAGVMaps() {
        Set<String> activeAgvMapIds = this.getActiveAgvMapIds();
        if (CollectionUtils.isEmpty(activeAgvMapIds)) {
            return;
        }
        List<String> agvMapIds = new ArrayList<>(activeAgvMapIds);
        for (String agvMapId : agvMapIds) {
            removeAGVMap(agvMapId);
        }
    }

    public void addAGVMaps(List<String> agvMapIds) {
        if (CollectionUtils.isEmpty(agvMapIds)) {
            return;
        }
        for (String agvMapId : agvMapIds) {
            addAGVMap(agvMapId);
        }
    }


    //清除地图
    public void clean() {
        //清除所有地图
        markerIdToMarker.clear();
        elevatorIdToMarkerIds.clear();
        elevatorMarkerIdToElevatorId.clear();
        sidePathIdToSidePath.clear();
        agvMapIdToMarkers.clear();
        agvMapIdToSidePaths.clear();
        startEndMarkerIdToSidePath.clear();
        iTojs.clear();
        isToj.clear();
        disableMarkers.clear();
        disableSidePaths.clear();
        matrix.clear();
    }

    public ConcurrentHashMap<String, Map<String, Double>> getMatrix() {
        return matrix;
    }

    public Map<String, SidePath> getStartEndMarkerIdToSidePath() {
        return startEndMarkerIdToSidePath;
    }

    public Map<String, Set<String>> getiTojs() {
        return iTojs;
    }

    public Map<String, Set<String>> getIsToj() {
        return isToj;
    }

    public Map<String, Marker> getMarkerIdToMarker() {
        return markerIdToMarker;
    }

    public Map<String, SidePath> getSidePathIdToSidePath() {
        return sidePathIdToSidePath;
    }

    public Map<String, List<Marker>> getAgvMapIdToMarkers() {
        return agvMapIdToMarkers;
    }

    public Map<String, List<SidePath>> getAgvMapIdToSidePaths() {
        return agvMapIdToSidePaths;
    }

    public Set<Marker> getDisableMarkers() {
        return disableMarkers;
    }

    public Set<SidePath> getDisableSidePaths() {
        return disableSidePaths;
    }

    public Set<Marker> getActiveMarkers() {
        return activeMarkers;
    }

    public Set<Marker> getDisableMarkersInActiveAGVMap() {
        return disableMarkersInActiveAGVMap;
    }

    public Map<String, Set<String>> getElevatorIdToMarkerIds() {
        return elevatorIdToMarkerIds;
    }

    public Map<String, String> getElevatorMarkerIdToElevatorId() {
        return elevatorMarkerIdToElevatorId;
    }

    public Map<String, Elevator> getElevatorIdToElevator() {
        return elevatorIdToElevator;
    }

    public Map<String, Integer> getMarkerIdToMarkerNavigationType() {
        return markerIdToMarkerNavigationType;
    }
}



