package com.youibot.agv.scheduler.engine.service.mechanics;

import java.io.IOException;
import java.util.Map;

/**
 * 升降平台
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/31 19:12
 */
public interface LiftingPlatformApiService {

    /**
     * 控制升降平台
     * @param ip
     * @param param
     * @throws IOException
     */
    Map<String, Object> control(String ip, Map<String, Object> param) throws IOException, InterruptedException;

}
