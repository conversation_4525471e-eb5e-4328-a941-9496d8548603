package com.youibot.agv.scheduler.engine.service.elevator;

import com.intelligt.modbus.jlibmodbus.exception.ModbusIOException;
import com.intelligt.modbus.jlibmodbus.exception.ModbusNumberException;
import com.intelligt.modbus.jlibmodbus.exception.ModbusProtocolException;
import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.youibot.agv.scheduler.constant.ActionConstant;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.entity.ElevatorFloor;
import com.youibot.agv.scheduler.engine.entity.FloorModbusParam;
import com.youibot.agv.scheduler.engine.manager.modbus.JLibModbusUtils;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.UnknownHostException;
import java.util.Map;
import java.util.Objects;

import static com.youibot.agv.scheduler.constant.ElevatorConstant.*;

/**
 * 电梯api服务
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/7/30 19:33
 */
@Component
public class DefaultElevatorApiService implements ElevatorApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultElevatorApiService.class);

    private static final Integer SLAVE_ID = 1;//从站ID

    private static final Integer NUMBER_OF_BITS = 1;//读取数量

    private static final Integer IS_LOCAL = 0;//是否本地

    private static final Integer ARRIVE = 1;//电梯到位值

    private static final Integer MAX_VALUE = 32767;//最大输入值

    private static final Integer MIN_VALUE = 0;//最小输入值

    private static final Integer RETRY_TOTAL_COUNT = 1;

    //发送电梯申请(呼叫)指令
    @Override
    public void elevatorApply(ElevatorFloor elevatorFloor, ModbusMaster modbusMaster) throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        LOGGER.info("外呼电梯，申请电梯到当前楼层,当前楼层为{}", elevatorFloor.getOriFloor());
        callElevator(elevatorFloor, elevatorFloor.getOriFloorParam().getOperateAddress(), elevatorFloor.getOriFloorParam().getOperate_out_open_value(), modbusMaster);
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_APPLIED);
    }

    //检测是否到达呼叫楼层并打开电梯门
    @Override
    public void elevatorOpenByComeIn(ElevatorFloor elevatorFloor, ModbusMaster modbusMaster) throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        long startTime = System.currentTimeMillis();//开始时间
        Integer applyElevatorTimeout = elevatorFloor.getApplyTimeout() * 1000;//申请电梯超时时间
        LOGGER.info("检测电梯是否到达申请楼层");
        FloorModbusParam modbusParam = elevatorFloor.getOriFloorParam();
        while (!ELEVATOR_STATUS_CALL_LAYER_OPEN.equals(elevatorFloor.getElevatorStatus())) {//没有到达呼叫层打开电梯门，进入循环
            if ((System.currentTimeMillis() - startTime) > applyElevatorTimeout) {//超时
                throw new ExecuteException(ErrorEnum.ELEVATOR_APPLY_TIME_OUT.code(), ErrorEnum.ELEVATOR_APPLY_TIME_OUT.msg());
            }
            if (ELEVATOR_STATUS_ERROR.equals(elevatorFloor.getElevatorStatus())) {
                throw new ExecuteException(ErrorEnum.ELEVATOR_ABNORMAL.code(), ErrorEnum.ELEVATOR_ABNORMAL.msg());
            }
            getElevatorDoorStatus(elevatorFloor, modbusParam.getStatusAddress(), modbusParam.getOpenStatusValue(), modbusParam.getElevatorArriveAddress(), modbusMaster);
            Thread.sleep(500);
        }
    }

    //发送关闭电梯指令
    @Override
    public void elevatorCloseByComeIn(ElevatorFloor elevatorFloor, ModbusMaster modbusMaster) throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        LOGGER.info("电梯门在呼叫层关闭");
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_CALL_LAYER_CLOSE);
        callElevator(elevatorFloor, elevatorFloor.getOriFloorParam().getOperateAddress(), elevatorFloor.getOriFloorParam().getOperateCloseValue(), modbusMaster);

    }

    //不做操作
    @Override
    public void elevatorMove(ElevatorFloor elevatorFloor) {
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_MOVE);
    }

    //检测是否到达目标楼层并打开电梯门
    @Override
    public void elevatorOpenByComeOut(ElevatorFloor elevatorFloor, ModbusMaster modbusMaster) throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        long startTime = System.currentTimeMillis();//开始时间
        Integer controlElevatorTimeout = elevatorFloor.getControlTimeout() * 1000;//控制电梯超时时间
        LOGGER.info("内呼电梯，申请电梯到目标楼层，当前电梯楼层{}，目标电梯楼层{}", elevatorFloor.getOriFloor(), elevatorFloor.getDirFloor());
        FloorModbusParam dirFloorParam = elevatorFloor.getDirFloorParam();
        callElevator(elevatorFloor, dirFloorParam.getOperateAddress(), dirFloorParam.getOperate_in_open_value(), modbusMaster);
        while (!ELEVATOR_STATUS_TARGET_LAYER_OPEN.equals(elevatorFloor.getElevatorStatus())) {//没有到达目标层打开电梯门，进入循环
            if ((System.currentTimeMillis() - startTime) > controlElevatorTimeout) {//超时
                throw new ExecuteException(ErrorEnum.ELEVATOR_MOVE_TIME_OUT.code(), ErrorEnum.ELEVATOR_MOVE_TIME_OUT.msg());
            }
            if (ELEVATOR_STATUS_ERROR.equals(elevatorFloor.getElevatorStatus())) {
                throw new ExecuteException(ErrorEnum.ELEVATOR_ABNORMAL.code(), ErrorEnum.ELEVATOR_ABNORMAL.msg());
            }
            getElevatorDoorStatus(elevatorFloor, dirFloorParam.getStatusAddress(), dirFloorParam.getOpenStatusValue(), dirFloorParam.getElevatorArriveAddress(), modbusMaster);
            Thread.sleep(500);
        }

    }

    //不做操作
    @Override
    public void elevatorCloseByComeOut(ElevatorFloor elevatorFloor, ModbusMaster modbusMaster) throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        callElevator(elevatorFloor, elevatorFloor.getDirFloorParam().getOperateAddress(), elevatorFloor.getDirFloorParam().getOperateCloseValue(), modbusMaster);
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_TARGET_LAYER_CLOSE);
    }

    //发送释放电梯指令
    @Override
    public void elevatorRelease(ElevatorFloor elevatorFloor) {
        elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_RELEASED);
    }

    //呼叫电梯
    private void callElevator(ElevatorFloor elevatorFloor, Integer writeOffset, Integer writeValue, ModbusMaster modbusMaster) throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        try {
            this.switchWriteFunctionCode(elevatorFloor, elevatorFloor.getOriFloorParam().getWriteFunctionCode(), writeOffset, String.valueOf(writeValue), modbusMaster);
            LOGGER.info("呼叫电梯楼层 ip:{},port:{},functionCode:{},address:{},values:{}", elevatorFloor.getIp(), elevatorFloor.getPort(), elevatorFloor.getOriFloorParam().getWriteFunctionCode(), writeOffset, writeValue);
        } catch (Exception e) {
            LOGGER.error("呼叫电梯异常 ElevatorFloor{}", elevatorFloor);
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        }
    }

    //查询电梯门状态
    private void getElevatorDoorStatus(ElevatorFloor elevatorFloor, Integer startAddress, Integer resultValue, Integer arriveAddress, ModbusMaster modbusMaster) throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        try {
            //先查询电梯是否到位
            Map<String, Object> readElevatorArriveValue = this.switchReadFunctionCode(elevatorFloor, elevatorFloor.getOriFloorParam().getReadFunctionCode(), arriveAddress, modbusMaster);
            //再查询当前楼层电梯门是否开启
            Map<String, Object> readElevatorDoorValue = this.switchReadFunctionCode(elevatorFloor, elevatorFloor.getOriFloorParam().getReadFunctionCode(), startAddress, modbusMaster);
            String readFunctionCode = elevatorFloor.getOriFloorParam().getReadFunctionCode();
            boolean readCoilStatusValue = false;
            if (readFunctionCode.equals("03") || readFunctionCode.equals("04")) {
                int[] readArriveStatusValues = (int[]) readElevatorArriveValue.get("result");
                int[] readDoorStatusValues = (int[]) readElevatorDoorValue.get("result");
                LOGGER.debug("resultValue:{},readArriveStatusValues:{}, readDoorStatusValues:{}" , resultValue,  ArrayUtils.toString(readArriveStatusValues) , ArrayUtils.toString( readDoorStatusValues)  );
                if(  ArrayUtils.isNotEmpty( readArriveStatusValues) && !ArrayUtils.isNotEmpty( readDoorStatusValues)) {
                    int doostarus = readDoorStatusValues[0];
    				int arriveStatus = readArriveStatusValues[0];
    				if( Objects.nonNull( resultValue)) {
    					readCoilStatusValue = doostarus == resultValue && arriveStatus == ARRIVE;
    				}
                }
           
			
            } else {
                boolean[] readDoorStatusValues = (boolean[]) readElevatorDoorValue.get("result");
                boolean[] readArriveStatusValues = (boolean[]) readElevatorArriveValue.get("result");
                boolean openValue = resultValue == 1;
                boolean arriveValue = ARRIVE == 1;
                readCoilStatusValue = openValue == readDoorStatusValues[0] && arriveValue == readArriveStatusValues[0];
            }
            if (readCoilStatusValue) {
                Integer elevatorStatus = elevatorFloor.getElevatorStatus();
                if (ELEVATOR_STATUS_APPLIED.equals(elevatorStatus)) {
                    elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_CALL_LAYER_OPEN);
                } else if (ELEVATOR_STATUS_MOVE.equals(elevatorStatus)) {
                    elevatorFloor.setElevatorStatus(ELEVATOR_STATUS_TARGET_LAYER_OPEN);
                }
            }
        } catch (Exception e) {
            LOGGER.error("读取电梯状态异常 e:{}", e);
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        }
    }

    public Map<String, Object> switchReadFunctionCode(ElevatorFloor elevatorFloor, String code, Integer startAddress, ModbusMaster modbusMaster) throws ModbusIOException, ModbusProtocolException, ModbusNumberException, InterruptedException, UnknownHostException {
    	LOGGER.debug("Ip{},port，{} , code:{}， startAddress：{}" , elevatorFloor.getIp(), elevatorFloor.getPort(), code  ,  startAddress);
    	int retryCount = 0;
        while (true) {
            try {
                Map<String, Object> resultData = new HashedMap<>(2);
                switch (code) {
                    case ActionConstant.REMOTE_PLC_FUNCTION_READ_01:
                    case ActionConstant.REMOTE_PLC_FUNCTION_READ_02:
                        resultData.put("result", modbusMaster.readCoils(SLAVE_ID, startAddress, 1));
                        break;
                    case ActionConstant.REMOTE_PLC_FUNCTION_READ_03:
                    case ActionConstant.REMOTE_PLC_FUNCTION_READ_04:
					int[] readHoldingRegisters = modbusMaster.readHoldingRegisters(SLAVE_ID, startAddress, 1);
					  LOGGER.debug("readHoldingRegisters:{}", ArrayUtils.toString(readHoldingRegisters  ));
					resultData.put("result", readHoldingRegisters);
                        break;
                    default:
                        throw new ExecuteException(MessageUtils.getMessage("action.wrong_value") + " mode=" + code);
                }
                return resultData;
            } catch (Exception e) {
                if (e instanceof ExecuteException) {
                    throw e;
                }
                if (retryCount > RETRY_TOTAL_COUNT) {
                    LOGGER.error("Modbus通讯失败, ", e);
                    throw e;
                }
                retryCount++;
                LOGGER.error("寄存器读值失败, 地址:{}, 当前重试次数{}", startAddress, retryCount);
                modbusMaster.disconnect();
                Thread.sleep(500);
                modbusMaster = JLibModbusUtils.createModbusMaster(elevatorFloor.getIp(), elevatorFloor.getPort());
            }
        }
    }

    public void switchWriteFunctionCode(ElevatorFloor elevatorFloor, String code, Integer startAddress, String value, ModbusMaster modbusMaster) throws Exception {
        int retryCount = 0;
        String[] values = value.split(",");
        while (true) {
            try {
                LOGGER.debug("电梯控制值写入 elevatorFloor：{}", elevatorFloor);
                int writeValue;
                switch (code) {
                    case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_05:
                        writeValue = Integer.parseInt(values[0]);
                        if (writeValue != 0 && writeValue != 1) {
                            throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
                        }
                        modbusMaster.writeSingleCoil(SLAVE_ID, startAddress, writeValue != 0);
                        break;
                    case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_06:
                        writeValue = Integer.valueOf(values[0]);
                        if (writeValue > MAX_VALUE || writeValue < MIN_VALUE) {
                            throw new ADSParameterException(MessageUtils.getMessage("action.wrong_value"));
                        }
                        modbusMaster.writeSingleRegister(SLAVE_ID, startAddress, writeValue);
                        break;
                    case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_15:
                        boolean[] writeValues15 = new boolean[values.length];
                        for (int i = 0; i < values.length; i++) {
                            writeValue = Integer.parseInt(values[i]);
                            if (writeValue != 0 && writeValue != 1) {
                                throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
                            }
                            writeValues15[i] = writeValue == 1;
                        }
                        modbusMaster.writeMultipleCoils(SLAVE_ID, startAddress, writeValues15);
                        break;
                    case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_16:
                        int[] writeValues16 = new int[values.length];
                        for (int i = 0; i < values.length; i++) {
                            writeValue = Integer.parseInt(values[i]);
                            if (writeValue > MAX_VALUE || writeValue < MIN_VALUE) {
                                throw new ADSParameterException(MessageUtils.getMessage("action.wrong_value"));
                            }
                            writeValues16[i] = writeValue;
                        }
                        modbusMaster.writeMultipleRegisters(SLAVE_ID, startAddress, writeValues16);
                        break;
                    default:
                        throw new ExecuteException(MessageUtils.getMessage("action.wrong_value") + " mode=" + code);
                }
                return;
            } catch (Exception e) {
                if (e instanceof ExecuteException) {
                    throw e;
                }
                if (retryCount > RETRY_TOTAL_COUNT) {
                    LOGGER.error("Modbus通讯失败, ", e);
                    throw e;
                }
                retryCount++;
                LOGGER.error("寄存器写值失败, 地址:{}, 值:{}, 重试次数:{}", startAddress, values, retryCount);
                modbusMaster.disconnect();
                Thread.sleep(500);
                modbusMaster = JLibModbusUtils.createModbusMaster(elevatorFloor.getIp(), elevatorFloor.getPort());
            }
        }
    }
}
