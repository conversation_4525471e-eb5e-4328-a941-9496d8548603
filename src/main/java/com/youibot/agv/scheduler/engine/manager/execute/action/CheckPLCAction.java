package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.youibot.agv.scheduler.constant.ActionConstant;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.manager.modbus.JLibModbusUtils;
import com.youibot.agv.scheduler.engine.manager.modbus.ModbusCheckActionParams;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.net.UnknownHostException;
import java.util.*;

@Service
@Scope("prototype")
public class CheckPLCAction extends ModbusAction {

    Logger logger = LoggerFactory.getLogger(CheckPLCAction.class);

    @Override
    protected Map<String, Object> sendCommand() throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        super.faultResendAllCount = 0;
        JSONObject paramJson = this.getParamJson();
        ModbusCheckActionParams actionParams = new ModbusCheckActionParams(paramJson);
        if (actionParams.getTime_out() == null || StringUtils.isEmpty(actionParams.getSuccessCode())) {
            throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
        }
        String code = paramJson.getString("code");
        Map<String, Object> resultDataMap;
        switch (code) {
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_01:
                resultDataMap = this.readCheckFunction_01(actionParams);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_02:
                resultDataMap = this.readCheckFunction_02(actionParams);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_03:
                resultDataMap = this.readCheckFunction_03(actionParams);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_04:
                resultDataMap = this.readCheckFunction_04(actionParams);
                break;
            default:
                throw new ExecuteException(MessageUtils.getMessage("action.wrong_value") + " code=" + code);
        }
        return resultDataMap;
    }

    private Map<String, Object> readCheckFunction_01(ModbusCheckActionParams actionParams) throws InterruptedException {
        return checkReadCoilValue(actionParams);
    }

    private Map<String, Object> readCheckFunction_02(ModbusCheckActionParams actionParams) throws InterruptedException {
        return checkReadCoilValue(actionParams);
    }

    private Map<String, Object> readCheckFunction_03(ModbusCheckActionParams actionParams) throws InterruptedException {
        return checkReadShortValue(actionParams);
    }

    private Map<String, Object> readCheckFunction_04(ModbusCheckActionParams actionParams) throws InterruptedException {
        return checkReadShortValue(actionParams);
    }

    private Map<String, Object> checkReadCoilValue(ModbusCheckActionParams actionParams) throws InterruptedException {
        ModbusMaster modbusMaster = null;
        try {
            Map<String, Object> resultData = new HashedMap<>();
            long startTime = System.currentTimeMillis();//开始时间
            Boolean successCode = actionParams.getSuccessCode().equals("1");
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
            while (true) {//如果动作设置的值与读取plc的值不一致，继续读取
                if (Thread.interrupted()) {//如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }
                Thread.sleep(500);
                if ((new Date().getTime() - startTime) / 1000 > actionParams.getTime_out()) {
                    throw new ExecuteException(ErrorEnum.MODBUS_READ_NOT_EQUAL_TIME_OUT.code(), ErrorEnum.MODBUS_READ_NOT_EQUAL_TIME_OUT.msg());
                }
                boolean[] resultValue = super.readCoilStatusValue(modbusMaster, actionParams.getCode(),
                        actionParams.getSlaveId(), actionParams.getStartAddress(), actionParams.getNumberOfBits());//读取modbus值
                if (resultValue == null || resultValue.length == 0) {
                    continue;
                }
                resultData.put("value", resultValue[0]);
                if (successCode.equals(resultValue[0])) {
                    return resultData;
                }
            }
        } catch (UnknownHostException e) {
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    private Map<String, Object> checkReadShortValue(ModbusCheckActionParams actionParams) throws InterruptedException {
        ModbusMaster modbusMaster = null;
        try {
            Map<String, Object> resultData = new HashedMap<>();
            long startTime = System.currentTimeMillis();//开始时间
            List<String> errorCodeList = new ArrayList<>(Arrays.asList(actionParams.getErrorCode().split(",")));
            List<String> successCodeList = new ArrayList<>(Arrays.asList(actionParams.getSuccessCode().split(",")));
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
            while (true) {//如果动作设置的值与读取plc的值不一致，继续读取
                if (Thread.interrupted()) {//如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }
                Thread.sleep(500);
                if ((new Date().getTime() - startTime) / 1000 > actionParams.getTime_out()) {
                    throw new ExecuteException(ErrorEnum.MODBUS_READ_NOT_EQUAL_TIME_OUT.code(), ErrorEnum.MODBUS_READ_NOT_EQUAL_TIME_OUT.msg());
                }
                int[] resultValue = super.readShortValue(modbusMaster, actionParams.getCode(),
                        actionParams.getSlaveId(), actionParams.getStartAddress(), actionParams.getNumberOfBits());//读取modbus值
                if (resultValue == null || resultValue.length == 0) {
                    continue;
                }
                resultData.put("value", resultValue[0]);
                if (errorCodeList.contains(String.valueOf(resultValue[0]))) {
                    missionWorkAction.setResultData(JSONObject.toJSONString(resultData));
                    throw new ExecuteException(ErrorEnum.MODBUS_READ_DATA_EQUAL_OR_CONTAIN_ERROR_CODE.code(), ErrorEnum.MODBUS_READ_DATA_EQUAL_OR_CONTAIN_ERROR_CODE.msg());
                }
                if (successCodeList.contains(String.valueOf(resultValue[0]))) {
                    return resultData;
                }
            }
        } catch (UnknownHostException e) {
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        } finally {
            JLibModbusUtils.release(modbusMaster);
        }
    }

    @Override
    public String getAPICode() {
        return null;
    }

    @Override
    public Map<String, Object> readFunction_01(JSONObject paramJson) throws UnknownHostException, InterruptedException {
        return null;
    }

    @Override
    public Map<String, Object> readFunction_02(JSONObject paramJson) throws UnknownHostException, InterruptedException {
        return null;
    }

    @Override
    public Map<String, Object> readFunction_03(JSONObject paramJson) throws UnknownHostException, InterruptedException {
        return null;
    }

    @Override
    public Map<String, Object> readFunction_04(JSONObject paramJson) throws UnknownHostException, InterruptedException {
        return null;
    }

    @Override
    public Map<String, Object> writeFunction_05(JSONObject paramJson) throws UnknownHostException {
        return null;
    }

    @Override
    public Map<String, Object> writeFunction_15(JSONObject paramJson) throws UnknownHostException {
        return null;
    }

    @Override
    public Map<String, Object> writeFunction_06(JSONObject paramJson) throws UnknownHostException {
        return null;
    }

    @Override
    public Map<String, Object> writeFunction_16(JSONObject paramJson) throws UnknownHostException {
        return null;
    }
}
