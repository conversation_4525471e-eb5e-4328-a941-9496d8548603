package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date :Created in 下午7:26 2020/8/21
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SidePathResource extends PathResource {
    //资源Id
    private String sidePathId;
    private String nodeUId;
    private String nodeVId;
    private String nodeU;
    private String nodeV;
}
