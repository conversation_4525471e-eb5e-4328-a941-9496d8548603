package com.youibot.agv.scheduler.engine.service.status;

import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:songju<PERSON>@youibot.com
 * @version CreateTime: 2019-05-15 11:52
 */
public interface StatusApiService {

    Map<String, Object> getStatus(String ip) throws AGVResultException, IOException, InterruptedException;

    Map<String, Object> getStatus(AGVSocketClient client) throws AGVResultException, IOException, InterruptedException;
}
