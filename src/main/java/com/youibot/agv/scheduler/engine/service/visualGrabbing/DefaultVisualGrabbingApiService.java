package com.youibot.agv.scheduler.engine.service.visualGrabbing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Service
public class DefaultVisualGrabbingApiService extends DefaultApiService implements VisualGrabbingApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultVisualGrabbingApiService.class);

    @Value("${AGV_API_CODE.VISUAL_GRABBING}")
    private String visualGrabbingApiCode;

    @Override
    public Map<String, Object> visualGrabbing(String ip, Map<String, Object> param) throws IOException {
        LOGGER.debug("ip : " + ip + " visualGrabbing send data : " + JSON.toJSONString(param));
        return ActionUtils.sendInstruction(ip, visualGrabbingApiCode, JSONObject.toJSONString(param));
    }
}
