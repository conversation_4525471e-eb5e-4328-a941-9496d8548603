package com.youibot.agv.scheduler.engine.service.heartbeat;

import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;

import java.io.IOException;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/9/10 11:45
 */
public interface HeartbeatApiService {

    /**
     * 心跳连接
     * @param client
     * @throws IOException
     */
    void heartbeat(AGVSocketClient client) throws IOException;

    /**
     * 心跳连接
     * @param ip
     * @throws IOException
     */
    void heartbeat(String ip) throws IOException;

}
