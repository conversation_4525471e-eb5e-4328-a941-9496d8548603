package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.service.impl;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedEdge;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedNode;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DFSLoopDetectManager;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.SingleAreaPathResourcePool;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.service.ConflictCheck;
import com.youibot.agv.scheduler.engine.pathplan.util.SidePathUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.stream.Collectors;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 9/1/21 4:19 PM
 */
@Service
public class ConflictCheckImpl implements ConflictCheck {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConflictCheckImpl.class);

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    @Autowired
    private VehiclePool vehiclePool;

    @Autowired
    private LocationService locationService;

    @Autowired
    private SingleAreaPathResourcePool singleAreaPathResourcePool;

    private DFSLoopDetectManager dfsLoopDetectManager = DFSLoopDetectManager.getInstance();

    @Value("${PATH_PLAN.OPEN_OBSTRUCT_DETOUR}")
    private boolean obstructDetour;

    @Override
    public List<List<String>> getOppositeConflicts() {
        List<List<String>> result = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRunningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
        if (CollectionUtils.isEmpty(agvToPlanedSidePaths)) {
            return result;
        }
        String[] agvCodes = agvToPlanedSidePaths.keySet().stream().toArray(String[]::new);
        /**
         * 对向冲突检测及处理。
         * 1：获取所有已执行路径的AGVCode。
         * 2：获取所有的机器人。规划的路径。运行的路径，已执行的路径。
         * 3：如果机器人的runningSidePath不为空。则不存在冲突，忽略。
         * 4：如果机器人的planedPath为空。则不存在冲突。忽略。
         * 5：获得一辆车。再获得所有的别的车的planedPath中是否有冲突。
         * 6：如果有冲突。就把冲突的车放到冲突处理服务中进行解决。conflictProcessThread
         */
        for (int i = 0; i < agvCodes.length; i++) {
            LinkedBlockingDeque<SidePath> psA = agvToPlanedSidePaths.get(agvCodes[i]);
            LinkedBlockingDeque<SidePath> rsA = agvToRunningSidePaths.get(agvCodes[i]);
            if (psA.isEmpty()) continue;
            for (int j = i + 1; j < agvCodes.length; j++) {
                LinkedBlockingDeque<SidePath> psB = agvToPlanedSidePaths.get(agvCodes[j]);
                LinkedBlockingDeque<SidePath> rsB = agvToRunningSidePaths.get(agvCodes[j]);
                if (psB.isEmpty()) continue;
                if (detectConflict(psA, psB, rsA, rsB)) {
                    List<String> conflictList = new ArrayList<>();
                    conflictList.add(agvCodes[i]);
                    conflictList.add(agvCodes[j]);
                    result.add(conflictList);
                }
            }
        }
        return result;
    }

    /**
     * 如果路径是属于相同的Path。表示他们发生了对向冲突。返回true.
     *
     * @param planedSidePathsA
     * @param planedSidePathsB
     * @return
     */
    private boolean detectConflict(LinkedBlockingDeque<SidePath> planedSidePathsA, LinkedBlockingDeque<SidePath> planedSidePathsB, LinkedBlockingDeque<SidePath> runningSidePathsA, LinkedBlockingDeque<SidePath> runningSidePathsB) {
        if (planedSidePathsA.isEmpty() || planedSidePathsB.isEmpty()) {
            return false;
        }
        Pair<String ,SidePath>  planedElevatorsA = getElevetorIds(planedSidePathsA);
        Pair<String ,SidePath> planedElevatorsB = getElevetorIds(planedSidePathsB);
        Pair<String ,SidePath>  runningElevatorsA = getElevetorIds(runningSidePathsA);
        Pair<String ,SidePath> runningElevatorsB = getElevetorIds(runningSidePathsB);
        /**
         * 规划路径包含同一个电梯,但是电梯点不同
         */
        boolean planPart = getCommonPart(planedElevatorsA, planedElevatorsB);
        boolean runningPart = getCommonPart(runningElevatorsA, runningElevatorsB);
        if (planPart || runningPart) {
            LOGGER.debug("存在相同路径电梯的对向冲突 " );
            return true ;
        }

        SidePath sidePathA = planedSidePathsA.peekFirst();
        SidePath sidePathB = planedSidePathsB.peekFirst();
        /**
         * 检测是否对向行驶
         */
        if (sidePathA.getStartMarkerId().equals(sidePathB.getEndMarkerId())
                && sidePathA.getEndMarkerId().equals(sidePathB.getStartMarkerId())
                && !sidePathA.getId().equals(sidePathB.getId())) {
            return true;
        }
        /**
         * 如果存在T字路和一字路会存在机器人连接两个路径发生了冲突。并且进不去。
         */
        if (planedSidePathsA.size() >= 2 && planedSidePathsB.size() >= 2) {
            SidePath sidePathA1 = (SidePath) planedSidePathsA.toArray()[0];
            SidePath sidePathA2 = (SidePath) planedSidePathsA.toArray()[1];
            SidePath sidePathB1 = (SidePath) planedSidePathsB.toArray()[0];
            SidePath sidePathB2 = (SidePath) planedSidePathsB.toArray()[1];

            if (sidePathA1.getStartMarkerId().equals(sidePathB2.getEndMarkerId())
                    && sidePathA1.getEndMarkerId().equals(sidePathB2.getStartMarkerId())
                    && sidePathA2.getStartMarkerId().equals(sidePathB1.getEndMarkerId())
                    && sidePathA2.getEndMarkerId().equals(sidePathB1.getStartMarkerId())) {
                return true;
            }
            //苏州绿点对向冲突特殊识别处理，其他版本不用,当开启绕路功能时才需要该识别机制
            if (obstructDetour) {
                if (CollectionUtils.isEmpty(runningSidePathsA) && CollectionUtils.isEmpty(runningSidePathsB)) {
                    if (sidePathA1.getPathId().equals(sidePathB2.getPathId()) || sidePathA2.getPathId().equals(sidePathB1.getPathId())) {
                        LOGGER.debug("无正在运行路径下得两车识别冲突...");
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private Pair<String ,SidePath> getElevetorIds(LinkedBlockingDeque<SidePath> planedSidePathsA) {
        Optional<SidePath> first = planedSidePathsA.stream().filter(p -> MapGraphUtil.TAKE_NAVATIOIN_TYPE.contains(p.getNavigationType())).findFirst();
        if (first.isPresent()) {
            String startMarkerId = first.get().getStartMarkerId();
            String elevatorIdByElevatorMarkerId = MapGraphUtil.getElevatorIdByElevatorMarkerId(startMarkerId);
            return new Pair<>(elevatorIdByElevatorMarkerId, first.get());
        }
        return null;
    }


    /**
     * a b 电梯id相同,而电梯点不同
     * @param a
     * @param b
     * @return
     */
    public static boolean getCommonPart(  Pair<String ,SidePath> a,  Pair<String ,SidePath> b) {


        return Objects.nonNull( a) && Objects.nonNull(b) && a.getFirst().equals(b.getFirst()) && !a.getSecond().getId().equals(b.getSecond().getId());
    }
    @Override
    public List<List<String>> getRingConflicts() {
        List<List<String>> result = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        if (CollectionUtils.isEmpty(agvToPlanedSidePaths)) {
            return result;
        }
        String[] agvCodes = agvToPlanedSidePaths.keySet().stream().toArray(String[]::new);
        /**
         * 环形冲突检测及处理。
         * 1：生成车型规划路径无向图。
         * 2：如果无向图有机咕噜人路径行成了环。
         * 3：把机器人添加到冲突队列中。进行处理。
         */
        Map<String, String> nodeIdToAGVCode = new ConcurrentHashMap<>();
        UndirectedGraph graph = this.generateUndirectedGraph(agvCodes, agvToPlanedSidePaths, nodeIdToAGVCode);
        if (graph.hasCyclic()) {
            dfsLoopDetectManager.setUndirectedGraph(graph);
            List<ArrayList<String>> loops = dfsLoopDetectManager.getLoops();
            if (!CollectionUtils.isEmpty(loops)) {
                for (ArrayList<String> loop : loops) {
                    List<String> conflicts = new ArrayList<>();
                    loop.forEach(nodeId -> {
                        conflicts.add(nodeIdToAGVCode.get(nodeId));
                    });
                    result.add(conflicts);
                }
            }
        }
        return result;
    }

    @Override
    public List<String> getObstructConflicts() {
        List<String> result = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRunningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
        if (CollectionUtils.isEmpty(agvToPlanedSidePaths)) {
            return result;
        }
        agvToPlanedSidePaths.forEach((agvCode, psA) -> {
            if (!CollectionUtils.isEmpty(agvToRunningSidePaths.get(agvCode))) {
                return;//机器人行驶中, 不触发绕障
            }
            if (psA.size() <= 1) {
                return;//如果当前只存在一段路径, 不触发绕障（目标点被占用, 绕路无意义）
            }
            //获取除本身之外的所有vehicle
            List<Vehicle> vehicles = vehiclePool.getAll().stream().filter(vehicle -> !agvCode.equals(vehicle.getId())).collect(Collectors.toList());
            vehicles.forEach(vehicle -> {
                LinkedBlockingDeque<SidePath> psB = agvToPlanedSidePaths.get(vehicle.getId());
                if (detectObstructConflict(psA, psB, vehicle)) {
                    result.add(agvCode);
                }
            });
        });
        return result;
    }

    private boolean detectObstructConflict(LinkedBlockingDeque<SidePath> psA, LinkedBlockingDeque<SidePath> psB, Vehicle vehicleB) {
        if (!CollectionUtils.isEmpty(psB)) {//B机器人存在路径导航, 不触发阻挡绕障
            return false;
        }
        VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicleB.getId());//获取机器人占用的点位/路径
        if (vehicleLocation == null) {
            return false;//B机器人不在轨道上, 不触发阻挡绕障
        }
        LinkedList<SidePath> sidePaths = new LinkedList<>(psA);
        Marker currentMarker = vehicleLocation.getMarker();
        List<SidePath> currentSidePaths = vehicleLocation.getSidePaths();
        if (currentMarker != null) {//如果在点位上
            /**
             * 查看A机器人接下来要走的两个点位, 是否被B机器人占用
             */
            String markerId = currentMarker.getId();
            for (SidePath sidePath : sidePaths) {
                if (markerId.equals(sidePath.getEndMarkerId())) {
                    LOGGER.debug("检测到机器人在点位上阻挡冲突, 阻挡的机器人:{}", vehicleB.getId());
                    return true;//如果B机器人阻挡在A机器人接下来要行驶的两个点的其中一个点位上
                }
            }
            /**
             * 查看A机器人接下来要走的两个点位, 是否存在单机区域且被B机器人占用
             */
            //获取A机器人需要申请的单机区域
            List<String> applyMarkerIds = sidePaths.stream().map(SidePath::getEndMarkerId).collect(Collectors.toList());
            Set<String> applySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdsByMarkerIds(new LinkedList<>(applyMarkerIds));
            if (!CollectionUtils.isEmpty(applySingleAreaIds)) {//A机器人有需要申请的单机区域
                //获取B机器人占用的单机区域
                HashSet<String> occupySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdByMarkerId(markerId);
                applySingleAreaIds.retainAll(occupySingleAreaIds);//取差集, 判断A机器人申请的单机区域是否被B机器人占用
                if (!CollectionUtils.isEmpty(applySingleAreaIds)) {
                    LOGGER.debug("检测到机器人在单机区域上阻挡冲突, 阻挡的机器人:{}", vehicleB.getId());
                    return true;
                }
            }
        } else if (!CollectionUtils.isEmpty(currentSidePaths)) {//如果在路径上
            /**
             * 查看A机器人接下来要走的两段路径, 是否被B机器人占用
             */
            List<String> sidePathIds = sidePaths.stream().map(SidePath::getId).collect(Collectors.toList());
            List<SidePath> conflictPaths = currentSidePaths.stream().filter(sidePath -> sidePathIds.contains(sidePath.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(conflictPaths)) {
                LOGGER.debug("检测到机器人在路径上阻挡冲突, 阻挡的机器人:{}", vehicleB.getId());
                return true;
            }
            /**
             * 查看A机器人接下来要走的两个点位, 是否存在单机区域且被B机器人占用
             */
            //获取A机器人需要申请的单机区域
            List<String> applyMarkerIds = sidePaths.stream().map(SidePath::getEndMarkerId).collect(Collectors.toList());
            Set<String> applySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdsByMarkerIds(new LinkedList<>(applyMarkerIds));
            if (!CollectionUtils.isEmpty(applySingleAreaIds)) {//A机器人有需要申请的单机区域
                //获取B机器人占用的单机区域
                LinkedList<String> occupyMarkerIds = SidePathUtils.getMarkerIdsFromSidePaths(currentSidePaths);
                Set<String> occupySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdsByMarkerIds(occupyMarkerIds);
                applySingleAreaIds.retainAll(occupySingleAreaIds);//取差集, 判断A机器人申请的单机区域是否被B机器人占用
                if (!CollectionUtils.isEmpty(applySingleAreaIds)) {
                    LOGGER.debug("检测到机器人在单机区域上阻挡冲突, 阻挡的机器人:{}", vehicleB.getId());
                    return true;
                }
            }
        }
        return false;
    }

    private UndirectedGraph generateUndirectedGraph(String[] agvCodes,
                                                    ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths,
                                                    Map<String, String> nodeIdToAGVCode) {
        UndirectedGraph graph = new UndirectedGraph();
        for (String agvCode : agvCodes) {
            LinkedBlockingDeque<SidePath> psA = agvToPlanedSidePaths.get(agvCode);
            if (psA.isEmpty()) continue;
            Marker startMarker = MapGraphUtil.getMarkerByMarkerId(psA.peekFirst().getStartMarkerId());
            Marker endMarker = MapGraphUtil.getMarkerByMarkerId(psA.peekFirst().getEndMarkerId());
            UndirectedNode nodeU = new UndirectedNode(startMarker);
            UndirectedNode nodeV = new UndirectedNode(endMarker);
            UndirectedEdge edge = new UndirectedEdge(psA.peekFirst(), startMarker.getCode(), endMarker.getCode(), 0.8d, 0.8d);
            nodeIdToAGVCode.put(startMarker.getId(), agvCode);
            graph.addNode(nodeU);
            graph.addNode(nodeV);
            graph.addEdge(edge);
        }
        return graph;
    }
}
