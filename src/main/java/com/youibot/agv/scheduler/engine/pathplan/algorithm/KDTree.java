package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Stack;

/**
 * <AUTHOR>
 * @Date :Created in 17:58 2020/10/27
 * @Description :
 * @Modified By :
 * @Version :
 */
public class KDTree {
    private final static Logger LOGGER = LoggerFactory.getLogger(KDTree.class);

    public static class Data {
        public String id;
        public double[] point;
    }

    private class Node {
        //分割的维度
        int partition_dimension;
        //分割的值
        double partition_value;
        //如果为非叶子节点，该属性为空
        //否则为数据
        Data data;
        //是否为叶子
        boolean is_leaf = false;
        //左子树
        Node left;
        //右树
        Node right;
        //每个维度的最小值
        double[] min;
        //每个维度的最大值
        double[] max;
    }

    private static class UtilZ {
        /**
         * 计算给定维度的方差
         *
         * @param data      数据
         * @param dimension 维度
         * @return 方差
         */
        static double variance(ArrayList<Data> data, int dimension) {
            double v_sum = 0;
            double sum = 0;
            for (Data d : data) {
                sum += d.point[dimension];
                v_sum += d.point[dimension] * d.point[dimension];
            }
            int n = data.size();
            return v_sum / n - Math.pow(sum / n, 2);
        }

        /**
         * 取排序后的中间位置数值
         *
         * @param data      数据
         * @param dimension 维度
         * @return
         */
        static double median(ArrayList<Data> data, int dimension) {
            double[] d = new double[data.size()];
            int i = 0;
            for (Data k : data) {
                d[i++] = k.point[dimension];
            }
            return find_pos(d, 0, d.length - 1, d.length / 2);
        }

        static double[][] min_max(ArrayList<Data> data, int dimensions) {
            double[][] mm = new double[2][dimensions];
            //初始化 第一行为min，第二行为max
            for (int i = 0; i < dimensions; i++) {
                mm[0][i] = mm[1][i] = data.get(0).point[i];
                for (int j = 1; j < data.size(); j++) {
                    Data d = data.get(j);
                    if (d.point[i] < mm[0][i]) {
                        mm[0][i] = d.point[i];
                    } else if (d.point[i] > mm[1][i]) {
                        mm[1][i] = d.point[i];
                    }
                }
            }
            return mm;
        }

        static double distance(Data a, Data b) {
            double sum = 0;
            for (int i = 0; i < a.point.length; i++) {
                sum += Math.pow(a.point[i] - b.point[i], 2);
            }
            return sum;
        }

        /**
         * 在max和min表示的超矩形中的点和点a的最小距离
         *
         * @param a   点a
         * @param max 超矩形各个维度的最大值
         * @param min 超矩形各个维度的最小值
         * @return 超矩形中的点和点a的最小距离
         */
        static double min_distance(Data a, double[] max, double[] min) {
            double sum = 0;
            for (int i = 0; i < a.point.length; i++) {
                if (a.point[i] > max[i])
                    sum += Math.pow(a.point[i] - max[i], 2);
                else if (a.point[i] < min[i]) {
                    sum += Math.pow(min[i] - a.point[i], 2);
                }
            }

            return sum;
        }

        /**
         * 使用快速排序，查找排序后位置在pos_index处的值
         * 比Array.sort()后去对应位置值，大约快30%
         *
         * @param data      数据
         * @param low       参加排序的最低点
         * @param high      参加排序的最高点
         * @param pos_index 位置
         * @return
         */
        private static double find_pos(double[] data, int low, int high, int pos_index) {
            int low_t = low;
            int high_t = high;
            double v = data[low];
            ArrayList<Integer> same = new ArrayList<>((int) ((high - low) * 0.25));
            while (low < high) {
                while (low < high && data[high] >= v) {
                    if (data[high] == v) {
                        same.add(high);
                    }
                    high--;
                }
                data[low] = data[high];
                while (low < high && data[low] < v)
                    low++;
                data[high] = data[low];
            }
            data[low] = v;
            int upper = low + same.size();
            if (low <= pos_index && upper >= pos_index) {
                return v;
            }

            if (low > pos_index) {
                return find_pos(data, low_t, low - 1, pos_index);
            }

            int i = low + 1;
            for (int j : same) {
                if (j <= low + same.size())
                    continue;
                while (data[i] == v)
                    i++;
                data[j] = data[i];
                data[i] = v;
                i++;
            }

            return find_pos(data, low + same.size() + 1, high_t, pos_index);
        }
    }

    private Node root;

    private KDTree() {
    }

    public boolean isInit() {
        return this.root == null;
    }

    public static KDTree build(Data[] input) {
        int n = input.length;
        int m = input[0].point.length;

        ArrayList<Data> data = new ArrayList<>(n);
        for (int i = 0; i < n; i++) {
            Data d = new Data();
            d.id = input[i].id;
            d.point = new double[m];
            for (int j = 0; j < m; j++) {
                d.point[j] = input[i].point[j];
            }
            data.add(d);
        }
        KDTree tree = new KDTree();
        tree.root = tree.new Node();
        tree.buildDetail(tree.root, data, m);
        return tree;
    }

    /**
     * 循环构建树
     *
     * @param node       节点
     * @param data       数据
     * @param dimensions 数据的维度
     */
    private void buildDetail(Node node, ArrayList<Data> data, int dimensions) {
        if (data.size() == 1) {
            node.is_leaf = true;
            node.data = data.get(0);
            return;
        }
        //选择方差最大的维度
        node.partition_dimension = -1;
        double var = -1;
        double tmp_var;
        for (int i = 0; i < dimensions; i++) {
            tmp_var = UtilZ.variance(data, i);
            if (tmp_var > var) {
                var = tmp_var;
                node.partition_dimension = i;
            }
        }
        //如果方差=0，表示所有数据都相同，判定为叶子节点
        if (var == 0) {
            node.is_leaf = true;
            node.data = data.get(0);
            return;
        }

        //选择分割的值
        node.partition_value = UtilZ.median(data, node.partition_dimension);
        double[][] min_max = UtilZ.min_max(data, dimensions);
        node.min = min_max[0];
        node.max = min_max[1];

        int size = (data.size() - 1);
        ArrayList<Data> left = new ArrayList<>(size);
        ArrayList<Data> right = new ArrayList<>(size);
        ArrayList<Data> less = new ArrayList<>(size);
        ArrayList<Data> more = new ArrayList<>(size);
        ArrayList<Data> equals = new ArrayList<>(size);
        for (Data d : data) {
            if (d.point[node.partition_dimension] < node.partition_value) {
                less.add(d);
            } else if (d.point[node.partition_dimension] == node.partition_value) {
                equals.add(d);
            } else {
                more.add(d);
            }
        }
        if ((less.size() == 0) && (more.size() != 0)) {
            left.addAll(equals);
            right.addAll(more);
        } else if ((less.size() != 0) && (more.size() == 0)) {
            left.addAll(less);
            right.addAll(equals);
        } else if (less.size() == 0) {
            left.add(equals.get(0));
            right.add(equals.remove(0));
        } else {
            left.addAll(less);
            left.addAll(equals);
            right.addAll(more);
        }
        Node left_node = new Node();
        Node right_node = new Node();
        node.left = left_node;
        node.right = right_node;
        buildDetail(left_node, left, dimensions);
        buildDetail(right_node, right, dimensions);
    }

    /**
     * 打印树，测试时用
     */
    public void print() {
        printRec(root, 0);
    }

    private void printRec(Node node, int lv) {
        if (!node.is_leaf) {
            for (int i = 0; i < lv; i++)
                System.out.print("--");
            System.out.println(node.partition_dimension + ":" + node.partition_value);
            printRec(node.left, lv + 1);
            printRec(node.right, lv + 1);
        } else {
            for (int i = 0; i < lv; i++)
                System.out.print("--");
            StringBuilder s = new StringBuilder();
            s.append('(');
            for (int i = 0; i < node.data.point.length - 1; i++) {
                s.append(node.data.point[i]).append(',');
            }
            s.append(node.data.point[node.data.point.length - 1]).append(')');
            System.out.println(s);
        }
    }

    //用KDTree算法查找最近邻
    public Data query(Data input) {
        Node node = root;
        Stack<Node> stack = new Stack<>();
        while (!node.is_leaf) {
            if (input.point[node.partition_dimension] < node.partition_value) {
                stack.add(node.right);
                node = node.left;
            } else {
                stack.push(node.left);
                node = node.right;
            }
        }
        /**
         * 首先按树一路下来，得到一个想对较近的距离，再找比这个距离更近的点
         */
        double distance = UtilZ.distance(input, node.data);
        Data nearest = query_rec(input, distance, stack);
        return nearest == null ? node.data : nearest;
    }

    public Data query_rec(Data input, double distance, Stack<Node> stack) {
        Data nearest = null;
        Node node = null;
        double tdis;
        while (stack.size() != 0) {
            node = stack.pop();
            if (node.is_leaf) {
                tdis = UtilZ.distance(input, node.data);
                if (tdis < distance) {
                    distance = tdis;
                    nearest = node.data;
                }
            } else {
                /*
                 * 得到该节点代表的超矩形中点到查找点的最小距离mindistance
                 * 如果mindistance<distance表示有可能在这个节点的子节点上找到更近的点
                 * 否则不可能找到
                 */
                double mindistance = UtilZ.min_distance(input, node.max, node.min);
                if (mindistance < distance) {
                    while (!node.is_leaf) {
                        if (input.point[node.partition_dimension] < node.partition_value) {
                            stack.add(node.right);
                            node = node.left;
                        } else {
                            stack.push(node.left);
                            node = node.right;
                        }
                    }
                    tdis = UtilZ.distance(input, node.data);
                    if (tdis < distance) {
                        distance = tdis;
                        nearest = node.data;
                    }
                }
            }
        }
        return nearest;
    }

    /**
     * 线性查找，用于和kdtree查询做对照
     * 1.判断kdtree实现是否正确
     * 2.比较性能
     *
     * @param input
     * @param data
     * @return
     */
    private static Data nearest(Data input, Data[] data) {
        Data nearest = null;
        double dis = Double.MAX_VALUE;
        double t_dis;
        for (int i = 0; i < data.length; i++) {
            t_dis = UtilZ.distance(input, data[i]);
            if (t_dis < dis) {
                dis = t_dis;
                nearest = data[i];
            }
        }
        return nearest;
    }

    /**
     * 运行100000次，看运行结果是否和线性查找相同
     */
    public static void correct() {
        int count = 100000;
        while (count-- > 0) {
            int num = 100;
            Data[] input = new Data[num];
            for (int i = 0; i < num; i++) {
                input[i].point = new double[2];
                input[i].point[0] = Math.random() * 10;
                input[i].point[1] = Math.random() * 10;
            }
            Data query = new Data();
            query.point = new double[]{Math.random() * 50, Math.random() * 50};

            KDTree tree = KDTree.build(input);
            Data result = tree.query(query);
            Data result1 = nearest(query, input);
            if (result.point[0] != result1.point[0] || result.point[1] != result1.point[1]) {
                System.out.println("wrong");
                break;
            }
        }
    }

    public static void performance(int iteration, int data_size) {
        int count = iteration;

        int num = data_size;
        Data[] input = new Data[num];

        for (int i = 0; i < num; i++) {
            input[i].point = new double[2];
            input[i].point[0] = Math.random() * num;
            input[i].point[1] = Math.random() * num;
        }

        KDTree tree = KDTree.build(input);

        Data[] query = new Data[iteration];
        for (int i = 0; i < iteration; i++) {
            query[i].point = new double[2];
            query[i].point[0] = Math.random() * num * 1.5;
            query[i].point[1] = Math.random() * num * 1.5;
        }

        long start = System.currentTimeMillis();
        for (int i = 0; i < iteration; i++) {
            Data result = tree.query(query[i]);
        }
        long timekdtree = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        for (int i = 0; i < iteration; i++) {
            Data result = nearest(query[i], input);
        }
        long time_linear = System.currentTimeMillis() - start;

        System.out.println("data size:" + data_size + ";iteration:" + iteration);
        System.out.println("kdtree:" + timekdtree);
        System.out.println("linear:" + time_linear);
        System.out.println("linear/kdtree:" + (time_linear * 1.0 / timekdtree));
    }

}
