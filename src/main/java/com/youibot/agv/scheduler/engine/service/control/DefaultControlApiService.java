package com.youibot.agv.scheduler.engine.service.control;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.youibot.agv.scheduler.engine.constant.AGVApiParamConstant.CLOSE_EMERGENCY_STOP;
import static com.youibot.agv.scheduler.engine.constant.AGVApiParamConstant.OPEN_EMERGENCY_STOP;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/9/10 19:11
 */
@Service
public class DefaultControlApiService extends DefaultApiService implements ControlApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultControlApiService.class);

    @Value("${AGV_SOCKET.PORT.CONTROL}")
    private Integer controlPort;

    @Value("${AGV_API_CODE.EMERGENCY_STOP}")
    private String emergencyStopApiCode;//AMR电机软急停apiCode

    @Value("${AGV_API_CODE.AGV_RESTART}")
    private String agvRestartApiCode;//AGV重启apiCode

    @Value("${AGV_API_CODE.SHORT_STOP_RECOVERY}")
    private String shortStopRecoveryApiCode;//短暂急停恢复apiCode


    /**
     * 开启AMR电机软急停
     * @param ip
     * @throws IOException
     */
    @Override
    public void emergencyStop(String ip) throws IOException {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("stop", OPEN_EMERGENCY_STOP);
        LOGGER.debug("emergency stop, ip: " + ip + ", send data: " + JSON.toJSONString(dataMap));
        super.execute(ip, controlPort, emergencyStopApiCode, dataMap);
    }

    /**
     * 取消AMR电机软急停
     * @param ip
     * @throws IOException
     */
    @Override
    public void cancelEmergencyStop(String ip) throws IOException {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("stop", CLOSE_EMERGENCY_STOP);
        LOGGER.debug("cancel emergency stop, ip: " + ip + ", send data: " + JSON.toJSONString(dataMap));
        super.execute(ip, controlPort, emergencyStopApiCode, dataMap);
    }

    @Override
    public void AGVRestart(String ip) {
        LOGGER.debug("agv restart, ip: " + ip);
        try {
            super.execute(ip, controlPort, agvRestartApiCode, null);
        } catch (Exception e) {
            LOGGER.warn("agv restart error, ip: " + ip + ", message: " + e.getMessage());
        }
    }

    @Override
    public void shortStopRecovery(String ip) throws IOException {
        LOGGER.debug("short stop recovery, ip: " + ip);
        super.execute(ip, controlPort, shortStopRecoveryApiCode, null);
    }
}
