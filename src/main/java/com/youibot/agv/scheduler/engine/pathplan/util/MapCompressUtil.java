package com.youibot.agv.scheduler.engine.pathplan.util;

import com.youibot.agv.scheduler.exception.ExecuteException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Base64;


/**
 * <AUTHOR>
 * @Date :Created in 下午6:36 2020/11/17
 * @Description :
 * @Modified By :
 * @Version :
 */
public class MapCompressUtil {
    private static final Logger logger = LoggerFactory.getLogger(MapCompressUtil.class);

    public static final int THRESHOLD = 131;
    public static final int MAXVAL = 255;

    public static final int CV_THRESH_BINARY = 0;
    public static final int CV_THRESH_BINARY_INV = 1;
    public static final int CV_THRESH_TRUNC = 2;
    public static final int CV_THRESH_TOZERO = 3;
    public static final int CV_THRESH_TOZERO_INV = 4;

    /**
     * BufferedImage 编码转换为 base64
     *
     * @param bufferedImage
     * @return
     */
    public static String BufferedImageToBase64(BufferedImage bufferedImage) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();//io流
        try {
            ImageIO.write(bufferedImage, "png", baos);//写入流中
        } catch (IOException e) {
            e.printStackTrace();
        }
        byte[] bytes = baos.toByteArray();//转换成字节
        Base64.Encoder encoder = Base64.getEncoder();
        String png_base64 = encoder.encodeToString(bytes).trim();//转换成base64串
        png_base64 = png_base64.replaceAll("\n", "").replaceAll("\r", "");//删除 \r\n
        return "data:image/png;base64," + png_base64;
    }

    /**
     * base64 编码转换为 BufferedImage
     *
     * @param base64
     * @return
     */
    public static BufferedImage base64ToBufferedImage(String base64) {
        Base64.Decoder decoder = Base64.getDecoder();
        try {
            String[] split = base64.split("data:image/png;base64,");
            if (split[0].length() <= 0) {
                base64 = split[1];
            } else {
                base64 = split[0];
            }
            base64 = base64.replaceAll("\n", "").replaceAll("\r", "");//删除 \r\n
            byte[] bytes1 = decoder.decode(base64);
            ByteArrayInputStream bais = new ByteArrayInputStream(bytes1);
            return ImageIO.read(bais);
        } catch (IOException e) {
            logger.error("获取图片流失败, ", e);
            throw new ExecuteException("处理图片出错, " + e.getMessage());
        }
    }

    /**
     * 将bufferdimage转换为图片
     *
     * @param bi
     * @param pathAndName
     * @throws IOException
     */
    public static void writeImageFile(BufferedImage bi, String pathAndName) throws IOException {
        File outputfile = new File(pathAndName);
        ImageIO.write(bi, "jpg", outputfile);
    }

    /**
     * BufferImage转byte[]
     *
     * @param original
     * @return
     */
    public static byte[] bufImg2Bytes(BufferedImage original) {
        ByteArrayOutputStream bStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(original, "png", bStream);
        } catch (IOException e) {
            logger.error("{}", e);
            throw new RuntimeException("bugImg读取失败:" + e.getMessage(), e);
        }
        return bStream.toByteArray();
    }

    /**
     * byte[]转BufferImage
     *
     * @param imgBytes
     * @return
     */
    public static BufferedImage bytes2bufImg(byte[] imgBytes) {
        BufferedImage tagImg = null;
        try {
            tagImg = ImageIO.read(new ByteArrayInputStream(imgBytes));
            return tagImg;
        } catch (IOException e) {
            logger.error("{}", e);
            throw new RuntimeException("bugImg写入失败:" + e.getMessage(), e);
        }
    }

    /**
     * 根据长度和宽度生成图片并返回图片base64
     * @param height
     * @param width
     * @return
     */
    public static String generatePic(double height, double width) {
        //得到图片缓冲区
        int w = (int) width;
        int h = (int) height;
        BufferedImage bi = new BufferedImage(w, h,BufferedImage.TYPE_BYTE_GRAY);//INT精确度达到一定,RGB三原色，高度70,宽度150

        //得到它的绘制环境(这张图片的笔)
        Graphics2D g2 = (Graphics2D) bi.getGraphics();

        g2.fillRect(0,0,w,h);//填充一个矩形 左上角坐标(0,0),宽70,高150;填充整张图片
        //设置颜色
        g2.setColor(Color.WHITE);
        g2.fillRect(0,0,w,h);//填充整张图片(其实就是设置背景颜色)
        g2.setColor(Color.WHITE);
//        g2.setFont(new Font("宋体",Font.BOLD,18)); //设置字体:字体、字号、大小
//        g2.setColor(Color.BLACK);//设置背景颜色
//        g2.drawString("HelloWorld",3,50); //向图片上写字符串
//        ImageIO.write(bi,"PNG",new FileOutputStream("D:/a.png"));//保存图片 JPEG表示保存格式
        return MapCompressUtil.BufferedImageToBase64(bi);
    }

    /**
     * 将d的值保留num位小数
     * @param d
     * @param num
     * @return
     */
    public static Double format(double d, int num) {
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(num);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        return Double.parseDouble(nf.format(d).replaceAll(",", ""));
    }
}
