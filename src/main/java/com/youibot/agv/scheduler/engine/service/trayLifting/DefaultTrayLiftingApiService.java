package com.youibot.agv.scheduler.engine.service.trayLifting;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Service
public class DefaultTrayLiftingApiService extends DefaultApiService implements TrayLiftingApiService{
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultTrayLiftingApiService.class);

    @Value("${AGV_API_CODE.TRAY_LIFTING}")
    private String trayLiftingApiCode;

    @Override
    public Map<String, Object> trayLifting(String ip, Map<String, Object> param) throws IOException, InterruptedException {
        LOGGER.debug("ip : " + ip + " trayLifting send data : " + JSON.toJSONString(param));
        ActionUtils.sendInstruction(ip, trayLiftingApiCode, JSONObject.toJSONString(param));
        //查看执行状态
        Map<String, Object> resultMap = ActionUtils.checkActionStatus(ip, (String) param.get("id"));
        return (JSONObject) resultMap.get("feedback");
    }
}
