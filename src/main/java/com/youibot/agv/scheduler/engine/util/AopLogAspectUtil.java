package com.youibot.agv.scheduler.engine.util;

import com.youibot.agv.scheduler.entity.MissionWorkActionLog;
import com.youibot.agv.scheduler.thread.ActionLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
@Slf4j
@Aspect
@Component
public class AopLogAspectUtil {

    private static final Logger logger = LoggerFactory.getLogger(AopLogAspectUtil.class);

    @Autowired
    private ActionLogService actionLogService;

    private MissionWorkActionLog actionLog = null;

    @Pointcut("execution(* com.youibot.agv.scheduler.engine.service.action.DefaultActionOperationService.sendInstruction(com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient , String , String ))")
    public void sendLog() {
    }

    @Pointcut("execution(public * com.youibot.agv.scheduler.engine.service.action.DefaultActionOperationService.checkActionStatus(..)) || execution(* com.youibot.agv.scheduler.engine.service.move.PathNavigationExecute.getActionStatus())")
    public void resultLog() {
    }

    @Before("sendLog()")
    public void sendBefore(JoinPoint joinPoint) {//前置增强
        actionLogService.sendBeforeActionLog(joinPoint);
    }

    @After("sendLog()")
    public void sendAfter(JoinPoint joinPoint) {//后置增强
        actionLogService.sendAfterActionLog(joinPoint);
    }

    @AfterReturning(returning = "result", pointcut = "resultLog()")//后置返回增强
    public void resultAfter(JoinPoint joinPoint, Object result) {
        actionLogService.resultAfterActionLog(joinPoint,result);
    }

    @AfterThrowing(pointcut = "sendLog()", throwing = "ex")//任务异常增强
    public void sendEx(JoinPoint joinPoint, Throwable ex) {
        if (actionLog != null) {
            actionLogService.crateEx(ex);
        }
    }

    @AfterThrowing(pointcut = "resultLog()", throwing = "ex")
    public void resultEx(JoinPoint joinPoint, Throwable ex) {
        if (actionLog != null) {
            actionLogService.crateEx(ex);
        }
    }
}
