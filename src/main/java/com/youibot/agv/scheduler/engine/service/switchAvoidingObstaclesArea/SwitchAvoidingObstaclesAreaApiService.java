package com.youibot.agv.scheduler.engine.service.switchAvoidingObstaclesArea;

import java.io.IOException;
import java.util.Map;

public interface SwitchAvoidingObstaclesAreaApiService {
    /**
     * 速度区域组切换
     *
     * @param ip
     * @param param
     * @throws IOException
     */
    Map<String, Object> switchAvoidingObstaclesArea(String ip, Map<String, Object> param) throws IOException, InterruptedException;
}
