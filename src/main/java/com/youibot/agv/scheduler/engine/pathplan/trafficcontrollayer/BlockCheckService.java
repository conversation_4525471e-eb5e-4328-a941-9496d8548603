package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.youibot.agv.scheduler.controller.v3.PathPlanTestController;
import com.youibot.agv.scheduler.controller.v3.PathPlanTestController.SingleAreaResourceApplyVehicle;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.MarkerPathResourcePool;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.SingleAreaPathResourcePool;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.MarkerPathResource;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.SingleAreaPathResource;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;

import org.apache.commons.codec.binary.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.RESET_WEIGHT;
import static com.youibot.agv.scheduler.constant.PathPlanConstant.SET_WEIGHT;
import static com.youibot.agv.scheduler.constant.VehicleConstant.ENABLE;
import static com.youibot.agv.scheduler.constant.VehicleConstant.ONLINE;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 8/11/21 10:06 AM
 * 阻塞检测和解决线程，如果发现在检测人避障超过30秒，给路径上加上权重后。再发送给compass重新规划的线程。
 * 需要调用startBlockCheckThread启动程序
 */
@Service
public class BlockCheckService {

    private static final Logger logger = LoggerFactory.getLogger(BlockCheckService.class);

    @Value("${PATH_PLAN.AGV_ABNORMAL_STATUS_USER_WEIGHT}")
    private Double agvAbnormalStatusUserWeight;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    //障碍识别时间，单位:秒
    private Integer blockCheckInterval;

    //默认移除障碍（路径权重）时间，单位:秒
    private Integer removeBlockInterval;

    private BlockCheckThread blockCheckThread;


    //记录所有被阻塞的路径,已被修改过路径权重
    private Map<String, Long> jamSidePathMap = new ConcurrentHashMap<>();

    /**
     * 导航点资源
     */
    @Autowired
    private MarkerPathResourcePool markerPathResourcePool;
    
    /**
     * 单机区域自由人
     */
    @Autowired
    private SingleAreaPathResourcePool singleAreaResourcePool;
    /**
     * 阻塞检测和解决线程，如果发现在检测人避障超过30秒，给路径上加上权重后。再发送给compass重新规划的线程。
     */

    private class BlockCheckThread extends Thread {
        @Override
        public void run() {
            logger.debug("开启绕障线程");
            while (true) {
                SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
                if (null == schedulerConfig || null == schedulerConfig.getBlockCheckInterval() || null == schedulerConfig.getRemoveBlockInterval()){
                    logger.error("调度配置异常，无法启动绕障线程");
                    break;
                }
                if (!schedulerConfig.getBlockCheckEnable().equals(1)) {
                    logger.warn("已关闭绕障, 但绕障线程还未结束");
                    continue;
                }
                blockCheckInterval = schedulerConfig.getBlockCheckInterval();
                removeBlockInterval = schedulerConfig.getRemoveBlockInterval();
                try {
                    /**
                     * 需要重新规划机器人的列表。
                     * TODO 需要解决重新规划下发多次的问题。
                     * 解决办法：下发一次路规划，重新开始30秒计时
                     * 解决办法：如果路径权重已经添加过。说明已经重新路径规划过。就不需要再次下发相关机器人的路径规划。
                     */
                    Map<String, Vehicle> rePathPlanVehicleMap = new HashMap<>();


                    //获取所有在线的机器人。并检测避障状态是否超过30秒。
                    List<Vehicle> vehicles = vehiclePoolService.selectAll();
                    vehicles = vehicles.stream().filter(x -> ENABLE.equals(x.getStatus())).collect(Collectors.toList());
                    vehicles = vehicles.stream().filter(x -> ONLINE.equals(x.getOnlineStatus())).collect(Collectors.toList());
                    for (Vehicle vehicle : vehicles) {
                        if (vehicle == null) continue;
                        if (vehicle.getDefaultVehicleStatus() == null) continue;
                        if (vehicle.getDefaultVehicleStatus().getEmec() == null) continue;

                        DefaultVehicleStatus.EmecStatus emec = vehicle.getDefaultVehicleStatus().getEmec();
                        if (emec.getEmc_status() == 0){
                            //不急停时将车的急停时间置空
                            vehicle.setEmc_jam_start_time(null);
                            continue;
                        }
                        //急停，判断急停原因，当且仅当急停原因是 4:路径导航急停时才计时路径导航急停
                        Integer[] my_emc_reason = {4};
                        LinkedBlockingDeque<SidePath> runningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths().get(vehicle.getId());
                        if (Arrays.equals(emec.getEmc_reason(), my_emc_reason) && !CollectionUtils.isEmpty(runningSidePaths)) {
                            if (vehicle.getEmc_jam_start_time() != null) {
                                logger.debug("急停时间戳："+vehicle.getEmc_jam_start_time() + ",时间："+(System.currentTimeMillis() - vehicle.getEmc_jam_start_time()) + "障碍物识别时间:" + blockCheckInterval);
                                //持续急停中，发送重新路径规划请求，并将路径导航阻塞时间清空，重新开始路径导航阻塞时间计时
                                //急停30秒触发新的路径规划
                                if (System.currentTimeMillis() - vehicle.getEmc_jam_start_time() > blockCheckInterval * 1000) {
                                    logger.debug("进入绕障流程");
                                    List<Vehicle> vehicleList = checkSharedPath(vehicle, vehicles);
                                    vehicleList.forEach(v -> rePathPlanVehicleMap.put(v.getId(), v));
                                    vehicle.setEmc_jam_start_time(null);
                                }
                            } else {
                                //首次急停,重新开始计时
                                vehicle.setEmc_jam_start_time(System.currentTimeMillis());
                            }
                        }else {
                            //不是路径导航急停，重置阻塞时间为null
                            vehicle.setEmc_jam_start_time(null);
                        }
                    }
                    if (!CollectionUtils.isEmpty(rePathPlanVehicleMap)) {
//                        rePathPlanVehicleMap.values().forEach(Vehicle::rePathPlan);
                        PathPlanMessage pathPlanMessage = null;
                        for (Vehicle vehicle : rePathPlanVehicleMap.values()){
                            pathPlanMessage = checkAndSendPathService.getPathPlanMessage(vehicle.getId());
                            if (null != pathPlanMessage){
                                pathPlanMessage.setTime(System.currentTimeMillis());
                            }
                            vehicle.getPathPlanMessages().addFirst(pathPlanMessage);
                            logger.debug("PathPlanManager: agv:{},ppm:{}",vehicle.getId(),vehicle.getPathPlanMessages());
                            vehicle.cancelPathNavigation();
                        }
                        rePathPlanVehicleMap.clear();
                    }

                    //清理超时的阻塞路径
                    Map<String, Long> jamSidePaths = new HashMap<>(jamSidePathMap);
                    for (Map.Entry<String, Long> stringLongEntry : jamSidePaths.entrySet()) {
                        String sidePathId = stringLongEntry.getKey();
                        Long lastTimeMillis = stringLongEntry.getValue();
                        //检测到阻塞路径时间已经超时，自动清理用户权重
                        if (Math.abs(System.currentTimeMillis() - lastTimeMillis) > removeBlockInterval * 1000) {
                            clearJamSidePath(sidePathId);
//                            jamSidePathMap.remove(sidePathId);
                        }
                    }
                } catch (Exception e) {
                    logger.error("BlockCheckThread error:{}", e);
                }

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    logger.warn("打断线程异常，停止绕障线程", e);
                    break;
                }
            }
            logger.debug("结束绕障线程");
        }
    }

    //检查与当前AGV具有共同路径的AGV
    private synchronized List<Vehicle> checkSharedPath(Vehicle v1, List<Vehicle> vehicles) {
        List<Vehicle> vehicleList = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRunningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        LinkedBlockingDeque<SidePath> v1r = agvToRunningSidePaths.get(v1.getId());
        vehicleList.add(v1);
        for (Vehicle v2 : vehicles) {
            if (v2.getId().equals(v1.getId())) {
                continue;
            }
            LinkedBlockingDeque<SidePath> v2r = agvToRunningSidePaths.get(v2.getId());
            LinkedBlockingDeque<SidePath> v2p = agvToPlanedSidePaths.get(v2.getId());
            if (checkLinkedBlockingDequeMixed(v1r.getFirst(),null , v2r, v2p)) {
                vehicleList.add(v2);
            }
        }
        if (!CollectionUtils.isEmpty(vehicleList)) {
            modifyUserWeight(v1r.getFirst(), SET_WEIGHT, agvAbnormalStatusUserWeight);
            jamSidePathMap.put(v1r.getFirst().getId(),System.currentTimeMillis());
        }
        return vehicleList;
    }

    //检查传参进来的路径是否有交集,true:有交集
    private synchronized boolean checkLinkedBlockingDequeMixed(SidePath runningSidePath, Vehicle v2, LinkedBlockingDeque<SidePath> v2r, LinkedBlockingDeque<SidePath> v2p) {
        LinkedBlockingDeque<SidePath> sidePaths = new LinkedBlockingDeque<>();
        List<String> sidePathIds = new ArrayList<>();
        if (null != v2p && v2p.size() > 0){
            v2p.forEach(vp -> sidePathIds.add(vp.getId()));
        }
        if (null != v2r && v2r.size() > 0){
            v2r.forEach(vr -> sidePathIds.add(vr.getId()));
        }

        if (sidePathIds.contains(runningSidePath.getId())){
            return true;
        }
        if(Objects.nonNull(v2)) {
        	  String startMarkId = runningSidePath.getStartMarkerId();
              String endMarkId = runningSidePath.getEndMarkerId();
              String currentMarkId = v2.getCurrentMarkerId();
               if( StringUtils.equals(startMarkId, currentMarkId) ||  StringUtils.equals(endMarkId, currentMarkId)) {
               	return true;
               }
        }
      
        return false;
    }

    public synchronized void startBlockCheckThread() {
        if (blockCheckThread == null || blockCheckThread.isInterrupted()) {
            blockCheckThread = new BlockCheckThread();
            blockCheckThread.start();
        }
    }

    public synchronized void closeBlockCheckThread() {
        if (blockCheckThread != null) {
            blockCheckThread.interrupt();
            for (String sId : jamSidePathMap.keySet()) {
                clearJamSidePath(sId);
            }
            blockCheckThread = null;
        }
    }

    //设置障碍物检测时间 单位：秒
    public synchronized void setBlockCheckInterval(Integer blockCheckInterval) {
        this.blockCheckInterval = blockCheckInterval;
    }

    public synchronized void setRemoveBlockInterval(Integer removeBlockInterval) {
        this.removeBlockInterval = removeBlockInterval;
    }

    private synchronized void clearJamSidePath(String sidePathId) {
        jamSidePathMap.remove(sidePathId);
        MapGraphUtil.modifyUserWeightTowWay(sidePathId, RESET_WEIGHT, 0D);
    }

    private synchronized void modifyUserWeight(SidePath sidePath, String type, Double userWeight) {
        if (null == sidePath) return;
        MapGraphUtil.modifyUserWeightTowWay(sidePath.getId(), type, userWeight);
    }
    
    //检查与当前AGV具有共同路径的AGV
    public   List<Vehicle> checkSharedPath(String agvCode) {
    	
    	 List<Vehicle> vehicles = vehiclePoolService.selectAll();
         vehicles = vehicles.stream().filter(x -> ENABLE.equals(x.getStatus())).collect(Collectors.toList());
         vehicles = vehicles.stream().filter(x -> ONLINE.equals(x.getOnlineStatus())).collect(Collectors.toList());
         Vehicle v1 = defaultVehiclePool.getVehicle(agvCode) ;
        List<Vehicle> vehicleList = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        LinkedBlockingDeque<SidePath> v1p = agvToPlanedSidePaths.getOrDefault(v1.getId() ,  new LinkedBlockingDeque<SidePath>());
        if(CollectionUtils.isEmpty(v1p)) {
        	return Lists.newArrayList();
        }
        SidePath path = v1p.getFirst();
        String markerId = path.getEndMarkerId();
 

        for (Vehicle v2 : vehicles) {
            if (v2.getId().equals(v1.getId())) {
                continue;
            }
            Set<String> cantApplyMarkerId = cantApplyMarkerId(  v2.getId() );
             if(cantApplyMarkerId.contains( markerId)) {
            	 vehicleList.add(v2);
             }
		
        }
        return vehicleList;
    }

	/** 不能申请的markerId
	 * @param agvToRunningSidePaths
	 * @param markerPathResourcePool
	 * @param singleAreaResourcePoolEntries
	 * @param v2
	 * @return
	 */
	public Set<String> cantApplyMarkerId(String agvCode) {
		
		final Set<String> res = Sets.newConcurrentHashSet();
		  Vehicle v2 = defaultVehiclePool.getVehicle(agvCode) ;
		  if(Objects.isNull(v2)) {
			  return res;
		  }
		ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRunningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
		/**
		 * 运行中的点位
		 */
		LinkedBlockingDeque<SidePath> v2r = agvToRunningSidePaths.getOrDefault( agvCode  , new LinkedBlockingDeque<SidePath>());

	
	
		MarkerPathResource markerPathResource = markerPathResourcePool.get( agvCode );
		/**
		 * 占用的单机区域
		 */
		singleArea(agvCode, res);
		/**
		 * 占用的运行路径
		 */
		v2r.parallelStream().forEach(i -> {
			res.add( i.getStartMarkerId()) ;
			res.add( i.getEndMarkerId());
		});
		/**
		 * 占用的定位
		 */
		if(Objects.nonNull(markerPathResource)) {
			res.add(markerPathResource.getMarkerId());
		}

		
		if(org.apache.commons.lang3.StringUtils.isNotBlank(v2.getCurrentMarkerId())) {
			res.add( v2.getCurrentMarkerId());
		}
		
		return res;
	}

	/** agv 占用的单机区域
	 * @param agvCode
	 * @param res
	 */
	public void singleArea(String agvCode, final Set<String> res
			) {
     
		 try {
			PathPlanTestController pathPlanTestController = ApplicationUtils.getBean(PathPlanTestController.class);
			List<SingleAreaResourceApplyVehicle> singleAreaResource = pathPlanTestController.getSingleAreaResource();
			Set<String> collect = singleAreaResource.parallelStream()
					.filter(p -> StringUtils.equals(p.getAgvName(), agvCode)).flatMap(i -> i.getMarkerIds().stream())
					.collect(Collectors.toSet());
			res.addAll(collect);
		} catch (Exception e) {
			// TODO: handle exception
		}
	}
    
    /**查询当前agv 被那些车辆阻挡
     * @param agvCode
     * @return
     */
    public   List<String> checkSharedPathAgvId(String agvCode){
    	 List<Vehicle> checkSharedPath = checkSharedPath(agvCode);
    	 List<String> data = checkSharedPath.parallelStream().map(Vehicle::getId).collect(Collectors.toList()) ;
    	 return data ;
    }
}
