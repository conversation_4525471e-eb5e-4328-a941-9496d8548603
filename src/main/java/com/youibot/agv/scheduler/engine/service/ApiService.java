package com.youibot.agv.scheduler.engine.service;

import com.alibaba.fastjson.JSONArray;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 16:20
 */
public interface ApiService {

    Map<String, Object> execute(String ip, Integer port, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException;

    Map<String, Object> execute(AGVSocketClient client, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException;

    JSONArray executeByResultList(String ip, Integer port, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException;

    JSONArray executeByResultList(AGVSocketClient client, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException;

    String executeByResultStr(String ip, Integer port, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException;

    String executeByResultStr(AGVSocketClient client, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException;

}
