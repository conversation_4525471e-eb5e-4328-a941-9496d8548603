package com.youibot.agv.scheduler.engine.entity;

import com.google.common.util.concurrent.RateLimiter;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.mqtt.bean.callback.MapDataMessage;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/11/23 11:49
 */
public class MapResourceCache {

    private static Map<String, AGVMap> agvMapPool = new ConcurrentHashMap<>();

    private static Map<String, Marker> markerPool = new ConcurrentHashMap<>();

    private static Map<String, SidePath> sidePathPool = new ConcurrentHashMap<>();

    private static Map<String, MapArea> mapAreaPool = new ConcurrentHashMap<>();

    private static Map<String, Path> pathPool = new ConcurrentHashMap<>();

    private static Map<String, Set<Marker>> agvMapIdToMarkers = new ConcurrentHashMap<>();
    private static Map<String, Set<SidePath>> agvMapIdToSidePaths = new ConcurrentHashMap<>();
    private static Map<String, Set<Path>> agvMapIdToPaths = new ConcurrentHashMap<>();

    private static LocationService locationService = (LocationService) ApplicationUtils.getBean("locationServiceImpl");

    private static Map<String, RateLimiter> agvMapIdToRateLimiter = new ConcurrentHashMap<>();

    public static List<AGVMap> getAllAgvMap() {
        return new ArrayList<>(agvMapPool.values());
    }

    public synchronized static void addAGVMap(AGVMap agvMap) {
        agvMapPool.put(agvMap.getId(), agvMap);
    }

    public synchronized static void addMarker(Marker marker) {
        markerPool.put(marker.getId(), marker);
        String agvMapId = marker.getAgvMapName();
        agvMapIdToMarkers.computeIfAbsent(agvMapId, k -> new HashSet<>());
        agvMapIdToMarkers.get(agvMapId).add(marker);
    }

    public synchronized static void addSidePath(SidePath sidePath) {
        sidePathPool.put(sidePath.getId(), sidePath);
        String agvMapId = sidePath.getAgvMapName();
        agvMapIdToSidePaths.computeIfAbsent(agvMapId, k -> new HashSet<>());
        agvMapIdToSidePaths.get(agvMapId).add(sidePath);
    }

    public synchronized static void addPath(Path path) {
        pathPool.put(path.getId(), path);
        String agvMapId = path.getAgvMapName();
        agvMapIdToPaths.computeIfAbsent(agvMapId, k -> new HashSet<>());
        agvMapIdToPaths.get(agvMapId).add(path);
    }

    public synchronized static void addMapArea(MapArea mapArea) {
        mapAreaPool.put(mapArea.getId(), mapArea);
    }


    public synchronized static void addMarkers(List<Marker> markers) {
        if (!CollectionUtils.isEmpty(markers)) {
            markers.forEach(MapResourceCache::addMarker);
        }

    }

    public synchronized static void addPaths(List<Path> pathList) {
        if (!CollectionUtils.isEmpty(pathList)) {
            pathList.forEach(MapResourceCache::addPath);
        }
    }

    public synchronized static void addSidePaths(List<SidePath> sidePaths) {
        if (!CollectionUtils.isEmpty(sidePaths)) {
            sidePaths.forEach(MapResourceCache::addSidePath);
        }
    }

    public synchronized static void addMapAreas(List<MapArea> mapAreas) {
        if (!CollectionUtils.isEmpty(mapAreas)) {
            mapAreas.forEach(MapResourceCache::addMapArea);
        }
    }

    public static AGVMap getAGVMap(String agvMapId) {
        return agvMapPool.get(agvMapId);
    }

    public static Marker getMarker(String markerId) {
        return markerPool.get(markerId);
    }

    public static SidePath getSidePath(String sidePathId) {
        return sidePathPool.get(sidePathId);
    }

    public static MapArea getMapArea(String mapAreaId) {
        return mapAreaPool.get(mapAreaId);
    }

    public static Path getPath(String pathId) {
        return pathPool.get(pathId);
    }

    public static Set<Marker> getMarkersByAGVMapId(String agvMapId) {
        return agvMapIdToMarkers.get(agvMapId);
    }

    public static Set<SidePath> getSidePathsByAGVMapId(String agvMapId) {
        return agvMapIdToSidePaths.get(agvMapId);
    }

    public static Set<Path> getPathsByAGVMapId(String agvMapId) {
        return agvMapIdToPaths.get(agvMapId);
    }

    public static List<Marker> getAllMarkers() {
        return new LinkedList<>(markerPool.values());
    }

    public static String printSidePath(SidePath sidePath) {
        StringBuilder stringBuilder = new StringBuilder();
        if (sidePath != null) {
            String startCode = MapResourceCache.getMarker(sidePath.getStartMarkerId()).getCode();
            String endCode = MapResourceCache.getMarker(sidePath.getEndMarkerId()).getCode();
            String format = MessageFormat.format(" side path: [{0}]->[{1}], t0:[{2}], id:[{3}]", startCode, endCode, sidePath.getT0(), sidePath.getId());
            stringBuilder.append(format);
        }
        return stringBuilder.toString();
    }

    public static String printSidePaths(List<SidePath> sidePaths) {
        if (sidePaths == null) return null;
        StringBuilder stringBuilder = new StringBuilder();
        if (!CollectionUtils.isEmpty(sidePaths)) {
            for (SidePath sidePath : sidePaths) {
                Marker startMarker = MapResourceCache.getMarker(sidePath.getStartMarkerId());
                Marker endMarker = MapResourceCache.getMarker(sidePath.getEndMarkerId());
                if (startMarker == null || endMarker == null) continue;
                String startCode = startMarker.getCode();
                String endCode = endMarker.getCode();
                String format = MessageFormat.format(" side path: [{0}]->[{1}], t0:[{2}], id:[{3}]", startCode, endCode, sidePath.getT0(), sidePath.getId());
                stringBuilder.append(format);
            }
        }
        return stringBuilder.toString();
    }

    public synchronized static void deleteMapData(String agvMapId) {
        Set<Marker> markers = agvMapIdToMarkers.get(agvMapId);
        if (!CollectionUtils.isEmpty(markers)) {
            markers.forEach(marker -> markerPool.remove(marker.getId()));
        }
        Set<SidePath> sidePaths = agvMapIdToSidePaths.get(agvMapId);
        if (!CollectionUtils.isEmpty(sidePaths)) {
            sidePaths.forEach(sidePath -> sidePathPool.remove(sidePath.getId()));
        }
        Set<Path> paths = agvMapIdToPaths.get(agvMapId);
        if (!CollectionUtils.isEmpty(paths)) {
            paths.forEach(path -> pathPool.remove(path.getId()));
        }
        agvMapIdToMarkers.remove(agvMapId);
        agvMapIdToSidePaths.remove(agvMapId);
        agvMapIdToPaths.remove(agvMapId);
    }


    public synchronized static void addMapDataToCache(MapDataMessage mapDataMessage) {
        if (mapDataMessage == null) return;
        AGVMap agvMap = mapDataMessage.getAgvMap();
        if (agvMap == null) return;
        agvMapIdToRateLimiter.computeIfAbsent(agvMap.getId(), k -> RateLimiter.create(3));

        AGVMap cacheAgvMap = MapResourceCache.getAGVMap(agvMap.getId());

        boolean reloadMapData = false;
        if (cacheAgvMap == null) {
            reloadMapData = true;
        } else {
            if (cacheAgvMap.getVersion() == null) {
                reloadMapData = true;
            } else if (!cacheAgvMap.getVersion().equals(agvMap.getVersion())) {
                reloadMapData = true;
            } else {
                RateLimiter rateLimiter = agvMapIdToRateLimiter.get(agvMap.getId());
                boolean acquire = rateLimiter.tryAcquire();
                if (acquire) {
                    reloadMapData = true;
                } else {
                    //系统繁忙，实行限流
                }
            }
        }

        if (reloadMapData) {
            MapResourceCache.deleteMapData(mapDataMessage.getAgvMap().getId());
            locationService.removeKDTree(mapDataMessage.getAgvMap().getId());
            locationService.buildKDTree(mapDataMessage.getAgvMap().getId(), mapDataMessage.getMarkers());
            MapResourceCache.addAGVMap(mapDataMessage.getAgvMap());
            MapResourceCache.addMarkers(mapDataMessage.getMarkers());
            MapResourceCache.addSidePaths(mapDataMessage.getSidePaths());
            MapResourceCache.addMapAreas(mapDataMessage.getMapAreas());
            MapResourceCache.addPaths(mapDataMessage.getPaths());
        }
    }


}
