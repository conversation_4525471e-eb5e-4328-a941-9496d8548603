package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.entity.Marker;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date :Created in 14:38 2020/8/7
 * @Description :
 * @Modified By :
 * @Version :
 */

public final class UndirectedNode implements Serializable {
    public final String id;
    private final String code;
    public UndirectedNode parent = null;
    public UndirectedNode child = null;
    public final double x;
    public final double y;

    public UndirectedNode(String id, String code, double x, double y) {
        this.id = id;
        this.code = code;
        this.x = x;
        this.y = y;
    }

    public UndirectedNode(Marker marker) {
        this.id = marker.getId();
        this.code = marker.getCode();
        this.x = marker.getX();
        this.y = marker.getY();
    }

    public UndirectedNode(DirectedNode directedNode) {
        this.id = directedNode.id;
        this.code = directedNode.code;
        this.x = directedNode.x;
        this.y = directedNode.y;
    }

    public String getId() {
        return id;
    }

    private String getCode() {
        return code;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof UndirectedNode))
            return false;
        UndirectedNode that = (UndirectedNode) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
