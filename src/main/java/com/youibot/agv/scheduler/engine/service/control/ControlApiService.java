package com.youibot.agv.scheduler.engine.service.control;

import java.io.IOException;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/9/10 19:10
 */
public interface ControlApiService {

    /**
     * 开启AMR电机软急停
     * @param ip
     */
    void emergencyStop(String ip) throws IOException;

    /**
     * 取消AMR电机软急停
     * @param ip
     */
    void cancelEmergencyStop(String ip) throws IOException;

    /**
     * AGV重启
     * @param ip
     * @throws IOException
     */
    void AGVRestart(String ip) throws IOException;

    /**
     * 短暂急停恢复
     * @param ip
     * @throws IOException
     */
    void shortStopRecovery(String ip) throws IOException;
}
