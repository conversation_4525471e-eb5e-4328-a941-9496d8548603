package com.youibot.agv.scheduler.engine.constant;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 17:17
 */
public class AGVApiParamConstant {

    // 冲电模式
    public static final Integer CHARGE_MODEL_UNTILL_FULL = 1;//一直充电直至充满
    public static final Integer CHARGE_MODEL_SET_TIME = 0;//设置时间充电

    public static final Integer MAP_TYPE_LASER = 1;       //激光地图
    public static final Integer MAP_TYPE_FICTITIOUS = 2;	 //二维码地图

    public static final Integer MARKER_TYPE_NAVIGATION = 1;//导航点
    public static final Integer MARKER_TYPE_INITIAL = 2;//初始化位置点
    public static final Integer MARKER_TYPE_CHARGING_DOCKING = 3;//充电桩对接点
    public static final Integer MARKER_TYPE_V_DOCKING = 4;//V型特征对接点
    public static final Integer MARKER_TYPE_REFLECTOR_DOCKING = 5;//反光条特征对接点

    public static final Integer OPEN_OBSTACLE_AVOIDANCE = 1;//开启避障
    public static final Integer CLOSE_OBSTACLE_AVOIDANCE = 0;//关闭避障

    public static final Integer OPEN_FALL_PREVENT = 1;//开启防跌落
    public static final Integer CLOSE_FALL_PREVENT = 2;//关闭防跌落

    public static final String  ON_FALL_PREVENT= "on";//路径已开启防跌落
    public static final String  OFF_FALL_PREVENT= "off";//路径已关闭防跌落

    public static final Integer OPEN_EMERGENCY_STOP = 1;//开启AMR电机软急停
    public static final Integer CLOSE_EMERGENCY_STOP = 2;//关闭(取消)AMR电机软急停

    public static final Integer DENSE_CABINET_TYPE_TAKE = 1;//密集柜取货
    public static final Integer DENSE_CABINET_TYPE_STILL = 2;//密集柜还货
    public static final Integer WINDOW_FILE_TYPE_TAKE = 7;//窗口档案取货
    public static final Integer WINDOW_FILE_TYPE_STILL = 8;//窗口档案还货

    //控制模式
    public static final Integer CONTROL_MODE_AUTO = 1;//自动模式
    public static final Integer CONTROL_MODE_MANUAL = 2;//手动模式

}
