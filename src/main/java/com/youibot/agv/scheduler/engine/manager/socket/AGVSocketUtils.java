package com.youibot.agv.scheduler.engine.manager.socket;

import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * AGVSocket连接工具类
 *
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月11日 下午11:03:15
 */
public class AGVSocketUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(AGVSocketUtils.class);

    /**
     * 读数据
     *
     * @param inputStream
     * @throws
     * @returntry
     */
    public static DataProtocol readFromStream(DataProtocol protocol, InputStream inputStream) throws AGVResultException, IOException {
        BufferedInputStream bis = new BufferedInputStream(inputStream);
        String resultContainer = new String();                    //已读取数据暂时存放的容器
        byte[] readBytes = new byte[10240];                          //单次接受响应数据的容器
        int readLen = 0;                                          //此次读取的数据长度
        while ((readLen = bis.read(readBytes)) > -1) {
            String currentRead = new String(readBytes, 0, readLen, StandardCharsets.UTF_8);
            resultContainer = resultContainer + currentRead;
//            LOGGER.debug("-------->readBytes:" + bytesToHex(readBytes));
//            LOGGER.debug("------>currentRead:" + currentRead);
//            LOGGER.debug("readAllLen:" + resultContainer.getBytes().length + ", currentReadLen: " + readLen + ", currentReadEndByte:" + readBytes[readLen - 1]);
            LOGGER.debug("当前长度{}", resultContainer.length());
            if (readBytes[readLen - 1] == 0x03) {//最后一个字节为0x03，表示此次读取数据完毕
                break;
            }
        }
        DataProtocol resultProtocol = protocol.parseContentData(resultContainer.getBytes());// 解析返回数据
        LOGGER.debug("get:" + resultProtocol.getApiCode() + "," + bytesToHex(resultContainer.getBytes()));
        return resultProtocol;
    }

    /**
     * 计时读取数据
     */
    public static byte[] readFrequencyFromStream(InputStream inputStream, int time) throws IOException {
        InputStream bis = new BufferedInputStream(inputStream);
        byte[] getBytes = new byte[4000];     //所需接受响应数据的容器
        byte[] readBytes = new byte[time * 40000];//总接收数据量
        int fileNameReadLength = 0;
        int hasReadLength = 0;//已经读取的字节数
        //循环读取数据直到字节数量达到数组长度为止
        while ((fileNameReadLength = bis.read(readBytes, hasReadLength, time * 40000 - hasReadLength)) > 0) {
            hasReadLength = hasReadLength + fileNameReadLength;
        }
        int j = 0;
        //循环读取音频数组中音频字节获取最后4000个字节
        for (int i = readBytes.length - 4000; i < readBytes.length; i++) {
            getBytes[j] = readBytes[i];
            j++;
        }
        return getBytes;
    }

    /**
     * 计时读取数据
     */
    public static byte[] readTimeFromStream(InputStream inputStream, int time) throws IOException {
        InputStream bis = new BufferedInputStream(inputStream);
        byte[] readBytes = new byte[time * 80000];     //单次接受响应数据的容器
        int fileNameReadLength = 0;
        int hasReadLength = 0;//已经读取的字节数
        while ((fileNameReadLength = bis.read(readBytes, hasReadLength, time * 80000 - hasReadLength)) > 0) {
            hasReadLength = hasReadLength + fileNameReadLength;
        }
        return readBytes;
    }

    /**
     * 写数据
     *
     * @param protocol
     * @param outputStream
     */
    public static void write2Stream(DataProtocol protocol, OutputStream outputStream) throws IOException {
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(outputStream);
        byte[] writeData = protocol.genContentData();
        LOGGER.debug("send:" + protocol.getApiCode() + "," + bytesToHex(writeData));
        bufferedOutputStream.write(writeData);
        bufferedOutputStream.flush();
    }

    final protected static char[] hexArray = "0123456789ABCDEF".toCharArray();

    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < hexChars.length; i = i + 2) {
            buffer.append("0x" + hexChars[i] + hexChars[i + 1] + " ");
        }
        return buffer.toString();
    }

    /**
     * 关闭输入流
     *
     * @param is
     */
    public static void closeInputStream(InputStream is) throws IOException {
        if (is != null) {
            is.close();
        }
    }

    /**
     * 关闭输出流
     *
     * @param os
     */
    public static void closeOutputStream(OutputStream os) throws IOException {
        if (os != null) {
            os.close();
        }
    }

    /**
     * int转为byte数组
     *
     * @param i
     * @return
     */
    public static byte[] int2ByteArrays(int i) {
        byte[] result = new byte[4];
        result[0] = (byte) ((i >> 24) & 0xFF);
        result[1] = (byte) ((i >> 16) & 0xFF);
        result[2] = (byte) ((i >> 8) & 0xFF);
        result[3] = (byte) (i & 0xFF);
        return result;
    }

    /**
     * byte数组转为int
     *
     * @param b
     * @return
     */
    public static int byteArrayToInt(byte[] b) {
        int intValue = 0;
        for (int i = 0; i < b.length; i++) {
            intValue += (b[i] & 0xFF) << (8 * (3 - i)); // int占4个字节（0，1，2，3）
        }
        return intValue;
    }

    /**
     * 指定byte数组的部分内容转为int
     *
     * @param b
     * @param byteOffset
     * @param byteCount
     * @return
     */
    public static int byteArrayToInt(byte[] b, int byteOffset, int byteCount) {
        int intValue = 0;
        for (int i = byteOffset; i < (byteOffset + byteCount); i++) {
            intValue += (b[i] & 0xFF) << (8 * (3 - (i - byteOffset)));
        }
        return intValue;
    }

    /**
     * 指定byte数组的部分内容转为int
     *
     * @param b
     * @param byteOffset
     * @return
     */
    public static int byteArray2Int(byte[] b, int byteOffset) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(Integer.SIZE / Byte.SIZE);
        byteBuffer.put(b, byteOffset, 4); // 占4个字节
        byteBuffer.flip();
        return byteBuffer.getInt();
    }

    private static ByteBuffer buffer = ByteBuffer.allocate(8);

    /**
     * long转为byte数组
     *
     * @param x
     * @return
     */
    public static byte[] longToByteArray(long x) {
        buffer.putLong(0, x);
        return buffer.array();
    }

    /**
     * byte数组转为long
     *
     * @param bytes
     * @return
     */
    public static long byteArrayToLong(byte[] bytes) {
        buffer.put(bytes, 0, bytes.length);
        buffer.flip();// need flip
        return buffer.getLong();
    }
}
