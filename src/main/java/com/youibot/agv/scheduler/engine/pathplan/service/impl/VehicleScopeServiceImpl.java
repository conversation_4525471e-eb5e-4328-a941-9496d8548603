package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.google.common.base.Throwables;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DStarLiteAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.entity.*;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.service.VehicleScopeService;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 10:12
 * <p>
 * 车辆评分服务，
 * scope by distance .根据距离进行评分。
 * <p>
 * scope by battery value
 */
@Service
public class VehicleScopeServiceImpl implements VehicleScopeService {

    private static final Logger logger = LoggerFactory.getLogger(VehicleScopeServiceImpl.class);

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    @Autowired
    private PathPlanService pathPlanService;

    /**
     * 根据车辆当前位置到目标点位置距离进行评分。
     * 路离越近 评分越高。如果无法到达的返回null。
     * 评分=距离*-1,
     * TODO 需要优先性能。
     *
     * @param endMarkerId
     * @param vehicle
     * @return
     */
    public Double scopeByDistance(String endMarkerId, Vehicle vehicle) {
        if (StringUtils.isEmpty(endMarkerId) || vehicle == null) {
            return null;
        }
        try {
            MarkerPathResult markerPathResult = pathPlanService.search(vehicle.getId(), endMarkerId);
            if (markerPathResult != null) {
                //获取调度配置参数
                SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
                return markerPathResult.cost * schedulerConfig.getDistanceRatio();
            }
        } catch (Exception e) {
            logger.warn("path plan fault vehicle id :{}, aim marker id:{},e:{}", vehicle.getId(), endMarkerId, Throwables.getStackTraceAsString(e));
        }
        return null;

//        VehicleLocation vehicleLocation = fullLocationService.getVehicleLocation(vehicle.getId());
//        // 加载地图。
//        // if vehicle location on marker. else vehicle location on path.
//        String startMarkerId = null;
//        if (vehicleLocation != null) {
//            //检测延时状态。
//            long tolerateTime = 2000;
//            long timeOut = Math.abs(System.currentTimeMillis() - vehicleLocation.getUpdateTimeMillis());
//            if (timeOut > tolerateTime) {
//                logger.warn("vehicle name:[{}]，定位数据延时为[{}]ms，请检查网络和运行环境", vehicle.getName(), timeOut);
//            }
//            MarkerPathResult markerPathResult = null;
//            if (vehicleLocation.getMarker() != null) {
//                startMarkerId = vehicleLocation.getMarker().getId();
//            } else if (vehicleLocation.getSidePaths() != null) {
//                // 机器人可能同时占着多条路径。只需要获取一条路径后计算。不需要计算所有的路径。
//                SidePath sidePath = vehicleLocation.getSidePaths().get(0);
//                if (MapGraphUtil.containedSidePath(sidePath)) {
//                    startMarkerId = sidePath.getEndMarkerId();
//                }
//            }
//        }
//        // 计算距离并评分。
//        if (!StringUtils.isEmpty(startMarkerId)) {
//            DirectedGraph dGraph = MapGraphUtil.getOriginDirectedGraph();
//            DStarLiteAlgorithm dStarLiteAlgorithm = new DStarLiteAlgorithm(ALL_VEHICLE, dGraph);
//            dStarLiteAlgorithm.setDynamic(false);
//            MarkerPathResult markerPathResult = dStarLiteAlgorithm.rePlan(startMarkerId, endMarkerId);
//            // 如果路径规划失败。评分为null.
//            if (markerPathResult.isSuccess()) {
//                SidePathPlanResult sidePathPlanResult = linkerListPathToSidePath(vehicle.getId(), markerPathResult);
//                //获取调度配置参数
//                SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
//                return sidePathPlanResult.getCost() * schedulerConfig.getDistanceRatio();
//            }
//        }
//        return null;
    }

    /**
     * 根据车辆当前位置到目标点位置距离进行评分。
     * 电量系数 1.0
     * 无电量信息则返回空。
     * TODO 需要优先性能。
     *
     * @param vehicle
     * @param batteryValueRatio
     * @return
     */
    public Double scopeByBattery(Vehicle vehicle, Double batteryValueRatio) {
        if (vehicle == null || vehicle.getDefaultVehicleStatus() == null || vehicle.getDefaultVehicleStatus().getBattery() == null) {
            return null;
        }
        return vehicle.getDefaultVehicleStatus().getBattery().getBattery_value().doubleValue() * batteryValueRatio;
    }

    public Double scopeByDistance(String endMarkerId, Vehicle vehicle, Marker lastMarker) {
        if (StringUtils.isEmpty(endMarkerId) || vehicle == null) {
            return null;
        }
        try {
            MarkerPathResult markerPathResult = null;
            if (null != lastMarker){
                markerPathResult = pathPlanService.searchMarkerToMarker(lastMarker.getId(),endMarkerId);
            } else {
                markerPathResult = pathPlanService.search(vehicle.getId(), endMarkerId);
            }
            if (markerPathResult != null) {
                //获取调度配置参数
                SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
                return markerPathResult.cost * schedulerConfig.getDistanceRatio();
            }
        } catch (Exception e) {
            logger.warn("path plan fault vehicle id :{}, aim marker id:{}", vehicle.getId(), endMarkerId);
        }
        return null;
    }

    /**
     * 按作业执行的时间评分
     * 传入的作业为null，表示当前机器人处理非作业状态，直接评分为最大值
     * 作业没有开始时间，表示机器人还未返回作业开始的指令，作业未开始执行，评分为0
     * 正常评分为 （当前时间 - 作业开始时间）* 时间评分系数，如果该评分超过最大值，则直接评为最大值
     * @param vehicle
     * @param missionWork
     * @return
     */
    @Override
    public Double scopeByTime(Vehicle vehicle, MissionWork missionWork) {
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        if (schedulerConfig.getTimeRatio() == 0 && schedulerConfig.getMissionWorkSpendTime() == 0){
            return 0.0;
        }
        if (null == missionWork) {
            return 0.0;
        }
        if (null == missionWork.getCreateTime()){
            return 0.0;
        }
        long createTimeStamp = missionWork.getCreateTime().getTime();
        long executedTimeStamp = System.currentTimeMillis() - createTimeStamp;

        Double timeScope = schedulerConfig.getMissionWorkSpendTime() + executedTimeStamp/1000 * schedulerConfig.getTimeRatio();

        return timeScope > 0 ? timeScope * (-1) : 0;
    }
}
