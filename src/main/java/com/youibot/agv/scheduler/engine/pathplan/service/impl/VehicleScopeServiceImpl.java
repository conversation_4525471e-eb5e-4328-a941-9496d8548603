package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.service.VehicleScopeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class VehicleScopeServiceImpl implements VehicleScopeService {

    private static final Logger logger = LoggerFactory.getLogger(VehicleScopeServiceImpl.class);

    @Autowired
    private PathPlanService pathPlanService;

    /**
     * 根据车辆当前位置到目标点位置距离进行评分。
     * 路离越近 评分越高。如果无法到达的返回null。
     * 评分=距离*-1,
     * TODO 需要优先性能。
     *
     * @param endMarkerId
     * @return
     */
    public Double scopeByDistance(String endMarkerId) {
        try {
            MarkerPathResult markerPathResult = pathPlanService.pathScope(endMarkerId);
            if (markerPathResult != null) {
                //获取调度配置参数
                return markerPathResult.cost * -1;
            }
        } catch (Exception e) {
            logger.warn("距离评分出错, endMarkerId:{}", endMarkerId, e);
        }
        return null;
    }

}
