package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.youibot.agv.scheduler.mqtt.bean.scribe.status.PositionMessage;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus.PositionStatus;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:21 2020/8/15
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class Position {
    //机器人编号
    private String agvCode;
    //机器人所在的MapId
    private String agvMapId;
    private Double pos_x;// X坐标
    private Double pos_y;// y坐标
    private Double pos_angle;// angle 坐标, 单位 rad
    private Double pos_confidence;//机器人激光定位的置信度, 范围 [0, 1]
    private String pos_current_station;// 机器人当前所在站点的 ID
    private Double t;//曲线上的t值
    private String segment_id;//曲线Id
    private Double[] covariance;
    private Long update_time_millis;//数据更新时间，单位ms

    public Position(PositionMessage positionMessage) {
        this.agvCode = positionMessage.getAgvCode();
        this.agvMapId = positionMessage.getAgvMapId();
        PositionStatus positionStatus = positionMessage.getPositionStatus();
        this.pos_x = positionStatus.getPos_x();
        this.pos_y = positionStatus.getPos_y();
        this.pos_angle = positionStatus.getPos_angle();
        this.pos_confidence = positionStatus.getPos_confidence();
        this.pos_current_station = positionStatus.getPos_current_station();
        this.t = positionStatus.getT();
        this.segment_id = positionStatus.getSegment_id();
        this.covariance = positionStatus.getCovariance();
        this.update_time_millis = positionStatus.getUpdate_time_millis();
    }
}
