package com.youibot.agv.scheduler.engine.pathplan.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:21 2020/8/15
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class Position {
    //机器人编号
    private String agvId;
    //机器人所在的MapId
    private String agvMapId;
    // X坐标
    private Double x;
    // Y坐标
    private Double y;
    //单位 rad
    private Double angle;
    //激光定位的置信度, 范围 [0, 1]
    private Double confidence;
    //曲线Id
    private String segmentId;
    //曲线上的t值
    private Double t;
    //协方差矩阵
    private Double covariance[];
    //数据更新时间，单位ms
    private Long updateTimeMillis;
}
