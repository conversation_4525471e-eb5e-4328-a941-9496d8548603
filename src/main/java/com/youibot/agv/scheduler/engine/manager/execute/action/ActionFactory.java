package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * 动作执行抽象工厂
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019年4月17日 下午2:52:35
 */
public class ActionFactory {

    static Logger logger = LoggerFactory.getLogger(ActionFactory.class);

    public static Action createActionExecute(MissionWorkAction missionWorkAction) {

        // 动作类型
        String actionType = missionWorkAction.getActionType();
        if (StringUtils.isEmpty(actionType)) {
            logger.error("create command error. actionType is null, missionWorkActionId: " + missionWorkAction.getId());
            throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());

        }
        //actionType转换成大写，根据actionType的不同做相应的处理
        switch (actionType.toUpperCase()) {

            case "CAMERA":
                CameraAction cameraAction = (CameraAction) ApplicationUtils.getBean("cameraAction");
                return init(cameraAction, missionWorkAction);//摄像头

            case "PTZ_CAMERA":
                CameraAction ptzCameraAction = (CameraAction) ApplicationUtils.getBean("cameraAction");
                ptzCameraAction.setPtzCamera(true);
                return init(ptzCameraAction, missionWorkAction);//云台摄像头

            case "INFRARED":
                InfraredAction infraredAction = (InfraredAction) ApplicationUtils.getBean("infraredAction");
                return init(infraredAction, missionWorkAction);//红外热像仪

            case "FILL_LIGHT":
                FillLightAction fillLightAction = (FillLightAction) ApplicationUtils.getBean("fillLightAction");
                return init(fillLightAction, missionWorkAction);//补光灯

            case "REVERSE_MANIPULATOR_ARM":
                ManipulatorArmAction manipulatorArmAction = (ManipulatorArmAction) ApplicationUtils.getBean("manipulatorArmAction");
                return init(manipulatorArmAction, missionWorkAction);//机械臂

            case "LIFTING_PLATFORM":
                LiftingPlatformAction liftingPlatformAction = (LiftingPlatformAction) ApplicationUtils.getBean("liftingPlatformAction");
                return init(liftingPlatformAction, missionWorkAction);//升降平台

            case "ROLLER_AND_TRANSFORMER":
                RollerAndTransformerAction rollerAndTransformerAction = (RollerAndTransformerAction) ApplicationUtils.getBean("rollerAndTransformerAction");
                return init(rollerAndTransformerAction, missionWorkAction);//辊筒电缸

            case "VISUAL_JACK":
                VisualJackAction visualJackAction = (VisualJackAction) ApplicationUtils.getBean("visualJackAction");
                return init(visualJackAction, missionWorkAction);//视觉插孔

            case "NOISE_SENSOR":
                NoiseSensorAction noiseSensorAction = (NoiseSensorAction) ApplicationUtils.getBean("noiseSensorAction");
                return init(noiseSensorAction, missionWorkAction);//噪音传感器

            case "MOVE_DOCKING":
                MoveDockingAction moveDockingAction = (MoveDockingAction) ApplicationUtils.getBean("moveDockingAction");
                return init(moveDockingAction, missionWorkAction);//对接/取消对接

            case "MOVE_RELATIVE":
                MoveRelativeAction moveRelativeAction = (MoveRelativeAction) ApplicationUtils.getBean("moveRelativeAction");
                return init(moveRelativeAction, missionWorkAction);//相对移动

            case "MOVE_ROTATE":
                MoveRotateAction moveRotateAction = (MoveRotateAction) ApplicationUtils.getBean("moveRotateAction");
                return init(moveRotateAction, missionWorkAction);//原地旋转

            case "MOVE_SIDE_PATH":
                MoveSidePathAction moveSidePathAction = (MoveSidePathAction) ApplicationUtils.getBean("moveSidePathAction");
                return init(moveSidePathAction, missionWorkAction);//路径导航

            case "RANDOM_MOVE_SIDE_PATH":
                MoveSidePathAction moveSidePathAction2 = (MoveSidePathAction) ApplicationUtils.getBean("moveSidePathAction");
                return init(moveSidePathAction2, missionWorkAction);//随机路径导航

            case "WAIT":
                WaitAction waitAction = (WaitAction) ApplicationUtils.getBean("waitAction");
                return init(waitAction, missionWorkAction);//等待执行

            case "HTTP_POST_REQUEST":
                HttpPostRequestAction httpPostRequestAction = (HttpPostRequestAction) ApplicationUtils.getBean("httpPostRequestAction");
                return init(httpPostRequestAction, missionWorkAction);//http POST 请求

            case "STERILIZING_LAMP":
                SterilizingLampAction sterilizingLampAction = (SterilizingLampAction) ApplicationUtils.getBean("sterilizingLampAction");
                return init(sterilizingLampAction, missionWorkAction);//消毒灯控制

            case "JACKING_MECHANISM":
                JackingMechanismAction jackingMechanismAction = (JackingMechanismAction) ApplicationUtils.getBean("jackingMechanismAction");
                return init(jackingMechanismAction, missionWorkAction);//顶升机构

            case "MOVE_FREE_NAVIGATION":
                MoveFreeNavigationAction moveFreeNavigationAction = (MoveFreeNavigationAction) ApplicationUtils.getBean("moveFreeNavigationAction");
                return init(moveFreeNavigationAction, missionWorkAction);//自由导航

            case "VARIABLE_SETTING":
                VariableSettingAction variableSettingAction = (VariableSettingAction) ApplicationUtils.getBean("variableSettingAction");
                return init(variableSettingAction, missionWorkAction);//全局变量设置

            case "VARIABLE_RUNTIME_SETTING":
                VariableRuntimeSettingAction variableRuntimeSettingAction = (VariableRuntimeSettingAction) ApplicationUtils.getBean("variableRuntimeSettingAction");
                return init(variableRuntimeSettingAction, missionWorkAction);//全局变量运行时设置
            case "ROLLER_LEFT_RIGHT_OPERATE":
                RollerOperateAction rollerOperateAction = (RollerOperateAction) ApplicationUtils.getBean("rollerOperateAction");
                return init(rollerOperateAction, missionWorkAction);//滚筒左右进出料

            case "ROLLER_UP_DOWN":
                RollerUpDownAction rollerUpDownAction = (RollerUpDownAction) ApplicationUtils.getBean("rollerUpDownAction");
                return init(rollerUpDownAction, missionWorkAction);//滚筒左右进出料

            case "SIX_SULFUR_FLUORIDE_DETECTION":
                SixSulfurFluorideAction sixSulfurFluorideAction = (SixSulfurFluorideAction) ApplicationUtils.getBean("sixSulfurFluorideAction");
                return init(sixSulfurFluorideAction, missionWorkAction);//六氟化硫检测

            case "GAS_DETECTION":
                GasDetectionAction gasDetectionAction = (GasDetectionAction) ApplicationUtils.getBean("gasDetectionAction");
                return init(gasDetectionAction, missionWorkAction);//气体检测

            case "PLUG_IN_MOTOR":
                PlugInMotorAction plugInMotorAction = (PlugInMotorAction) ApplicationUtils.getBean("plugInMotorAction");
                return init(plugInMotorAction, missionWorkAction);//插取电机位置控制

            case "TEMPERATURE_AND_HUMIDITY":
                TemperatureAndHumidityAction temperatureAndHumidityAction = (TemperatureAndHumidityAction) ApplicationUtils.getBean("temperatureAndHumidityAction");
                return init(temperatureAndHumidityAction, missionWorkAction);//温湿度传感器

            case "VISUAL_GRABBING":
                VisualGrabbingAction visualGrabbingAction = (VisualGrabbingAction) ApplicationUtils.getBean("visualGrabbingAction");
                return init(visualGrabbingAction, missionWorkAction);//视觉抓取

            case "TRAY_LIFTING":
                TrayLiftingAction trayLiftingAction = (TrayLiftingAction) ApplicationUtils.getBean("trayLiftingAction");
                return init(trayLiftingAction, missionWorkAction);//托盘升降

            case "TRAY_FORWARD":
                TrayForwardAction trayForwardAction = (TrayForwardAction) ApplicationUtils.getBean("trayForwardAction");
                return init(trayForwardAction, missionWorkAction);//托盘前后

            case "SHELF_ROTATION":
                ShelfRotationAction shelfRotationAction = (ShelfRotationAction) ApplicationUtils.getBean("shelfRotationAction");
                return init(shelfRotationAction, missionWorkAction);//货架旋转

            case "SHELF_FOLLOW_UP":
                ShelfFollowUpAction shelfFollowUpAction = (ShelfFollowUpAction) ApplicationUtils.getBean("shelfFollowUpAction");
                return init(shelfFollowUpAction, missionWorkAction);//货架随动

            case "SHELF_ALIGNMENT":
                ShelfAlignmentAction shelfAlignmentAction = (ShelfAlignmentAction) ApplicationUtils.getBean("shelfAlignmentAction");
                return init(shelfAlignmentAction, missionWorkAction);//货架对准

            case "CLAMPS_LEFT_RIGHT_OPERATE":
                ClampsAction clampsAction = (ClampsAction) ApplicationUtils.getBean("clampsAction");
                return init(clampsAction, missionWorkAction);//夹抱左右进出料

            case "HUMAN_DISINFECT":
                HumanBodyDisinfectionLampAction humanBodyDisinfectionLampAction = (HumanBodyDisinfectionLampAction) ApplicationUtils.getBean("humanBodyDisinfectionLampAction");
                return init(humanBodyDisinfectionLampAction, missionWorkAction);//人体消毒灯控制

            case "DEVICE_OPERATE_APPLY":
                DeviceOperateApplyAction deviceOperateApplyAction = (DeviceOperateApplyAction) ApplicationUtils.getBean("deviceOperateApplyAction");
                return init(deviceOperateApplyAction, missionWorkAction);//人体消毒灯控制

            case "DEVICE_OPERATE_REPLY":
                DeviceOperateReplyAction deviceOperateReplyAction = (DeviceOperateReplyAction) ApplicationUtils.getBean("deviceOperateReplyAction");
                return init(deviceOperateReplyAction, missionWorkAction);//人体消毒灯控制

            case "IF_ELSE":
                IfElseAction ifElseAction = (IfElseAction) ApplicationUtils.getBean("ifElseAction");
                return init(ifElseAction, missionWorkAction);//if else 表达式

            case "WHILE":
                WhileAction whileAction = (WhileAction) ApplicationUtils.getBean("whileAction");
                return init(whileAction, missionWorkAction);//while 表达式

            case "RETURN":
                ReturnAction returnAction = (ReturnAction) ApplicationUtils.getBean("returnAction");
                return init(returnAction, missionWorkAction);//结束任务

            case "READ_PLC":
                ReadPLCAction readPLCAction = (ReadPLCAction) ApplicationUtils.getBean("readPLCAction");
                return init(readPLCAction, missionWorkAction);//读寄存器

            case "WRITE_PLC":
                WritePLCAction writePLCAction = (WritePLCAction) ApplicationUtils.getBean("writePLCAction");
                return init(writePLCAction, missionWorkAction);//写寄存器

            case "CHECK_PLC":
                CheckPLCAction checkPLCAction = (CheckPLCAction) ApplicationUtils.getBean("checkPLCAction");
                return init(checkPLCAction, missionWorkAction);//检验寄存器

            case "CONNECTING_FRAME":
                ConnectingFrameAction connectingFrameAction = (ConnectingFrameAction) ApplicationUtils.getBean("connectingFrameAction");
                return init(connectingFrameAction, missionWorkAction);//接驳架

            case "ROLLER_WIDTH":
                RollerWidthAction rollerWidthAction = (RollerWidthAction) ApplicationUtils.getBean("rollerWidthAction");
                return init(rollerWidthAction, missionWorkAction);//滚筒宽度控制

            case "GRIPPER":
                GripperAction gripperAction = (GripperAction) ApplicationUtils.getBean("gripperAction");
                return init(gripperAction, missionWorkAction);//夹持器

            case "AUDIO_CAPTURE":
                AudioAcquisitionAction audioAcquisitionAction = (AudioAcquisitionAction) ApplicationUtils.getBean("audioAcquisitionAction");
                return init(audioAcquisitionAction, missionWorkAction);//音频采集

            case "SWITCH_AVOIDING_OBSTACLES_AREA":
                SwitchAvoidingObstaclesAreaAction switchAvoidingObstaclesAreaAction = (SwitchAvoidingObstaclesAreaAction) ApplicationUtils.getBean("switchAvoidingObstaclesAreaAction");
                return init(switchAvoidingObstaclesAreaAction, missionWorkAction);//速度区域组切换

            case "RELEASE":
                ReleaseCheckAction releaseCheckAction = (ReleaseCheckAction) ApplicationUtils.getBean("releaseCheckAction");
                return init(releaseCheckAction, missionWorkAction);//放行检测控制

            case "AUDIO_PLAYBACK":
                AudioPlayBackAction audioPlayBackAction = (AudioPlayBackAction) ApplicationUtils.getBean("audioPlayBackAction");
                return init(audioPlayBackAction, missionWorkAction);//音频播放

            case "AUDIO_STOP":
                AudioStopAction audioStopAction = (AudioStopAction) ApplicationUtils.getBean("audioStopAction");
                return init(audioStopAction, missionWorkAction);

            case "RFID_CHECK_CODE":
                RFIDCheckCodeAction rfidCheckCodeAction = (RFIDCheckCodeAction) ApplicationUtils.getBean("RFIDCheckCodeAction");
                return init(rfidCheckCodeAction, missionWorkAction);

            case "SCRIPT_MANIPULATOR_ARM":
                RobotScriptAction robotScriptAction = (RobotScriptAction) ApplicationUtils.getBean("robotScriptAction");
                return init(robotScriptAction, missionWorkAction);//机械臂脚本指令

            case "QR_CODE_SCANNING":
                QrCodeScanningAction qrCodeScanningAction = (QrCodeScanningAction) ApplicationUtils.getBean("qrCodeScanningAction");
                return init(qrCodeScanningAction, missionWorkAction);//二维码扫描

            case "CLAMP_HUG":
                ClampHugAction clampHugAction = (ClampHugAction) ApplicationUtils.getBean("clampHugAction");
                return init(clampHugAction, missionWorkAction);

            case "CALIBRATION":
                CalibrationAction calibrationAction = (CalibrationAction) ApplicationUtils.getBean("calibrationAction");
                return init(calibrationAction, missionWorkAction);

            case "COMMON_ACTION":
                CommonAction commonAction = (CommonAction) ApplicationUtils.getBean("commonAction");
                return init(commonAction, missionWorkAction);

            case "UPDATE_MAP":
                UpdateMapAction updateMapAction = (UpdateMapAction) ApplicationUtils.getBean("updateMapAction");
                return init(updateMapAction, missionWorkAction);

            case "AGV_SHELF_ANGLE":
                AGVShelfAngleAction agvShelfAngleAction = (AGVShelfAngleAction) ApplicationUtils.getBean("AGVShelfAngleAction");
                return init(agvShelfAngleAction, missionWorkAction);

            case "MULTIPLE_PATH_NAVIGATION":
                MultiplePathNavigationAction multiplePathNavigationAction = ApplicationUtils.getBean(MultiplePathNavigationAction.class);
                return init(multiplePathNavigationAction, missionWorkAction);//多段路径导航

            case "CHARGE_OR_PARK_SWITCH":
                ChargeOrParkAction chargeOrParkAction = (ChargeOrParkAction) ApplicationUtils.getBean("chargeOrParkAction");
                return init(chargeOrParkAction,missionWorkAction);

        }
        throw new ActionException(MessageUtils.getMessage("action.action_type_error") + " actionType=" + actionType);
    }

    private static DefaultAction init(DefaultAction action, MissionWorkAction missionWorkAction) {
        action.init(missionWorkAction);
        return action;
    }
}
