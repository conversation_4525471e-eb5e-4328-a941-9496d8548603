package com.youibot.agv.scheduler.engine.service.sensor;

import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.youibot.agv.scheduler.engine.constant.AGVApiParamConstant.CLOSE_OBSTACLE_AVOIDANCE;
import static com.youibot.agv.scheduler.engine.constant.AGVApiParamConstant.OPEN_OBSTACLE_AVOIDANCE;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/6/11 14:20
 */
@Service
public class DefaultObstacleAvoidanceService extends DefaultApiService implements ObstacleAvoidanceService {

    @Value("${AGV_SOCKET.PORT.CONTROL}")
    private Integer agvControlPort;

    @Value("${AGV_API_CODE.OBSTACLE_AVOIDANCE}")
    private String obstacleAvoidanceApiCode;

    @Override
    public Map<String, Object> openObstacleAvoidance(String ip) throws IOException {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("open", OPEN_OBSTACLE_AVOIDANCE);
        return super.execute(ip, agvControlPort, obstacleAvoidanceApiCode, dataMap);
    }

    @Override
    public Map<String, Object> closeObstacleAvoidance(String ip) throws IOException {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("open", CLOSE_OBSTACLE_AVOIDANCE);
        return super.execute(ip, agvControlPort, obstacleAvoidanceApiCode, dataMap);
    }
}
