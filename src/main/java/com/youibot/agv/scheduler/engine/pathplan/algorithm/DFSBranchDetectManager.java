package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @Date :Created in 7:24 2021/3/10
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
public class DFSBranchDetectManager {

    private DFSBranchDetect dfsBranchDetect = new DFSBranchDetect();

    private DFSBranchDetectManager() {
    }

    private static class DFSBranchDetectManagerHolder {
        //静态初始化器，由JVM来保证线程安全
        private static DFSBranchDetectManager instance = new DFSBranchDetectManager();
    }

    public static DFSBranchDetectManager getInstance() {
        return DFSBranchDetectManagerHolder.instance;
    }

    public void setGraph(DirectedGraph directedGraph) {
        dfsBranchDetect.setGraph(directedGraph);
        dfsBranchDetect.DFSTraverse();
    }

    public List<List<String>> getTPaths() {
        return dfsBranchDetect.getTPaths();
    }

    public List<List<String>> getOPaths() {
        return dfsBranchDetect.getOPaths();
    }
}
