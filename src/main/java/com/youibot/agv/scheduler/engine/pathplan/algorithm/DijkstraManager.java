package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.entity.Marker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date :Created in 下午8:57 2020/10/23
 * @Description :
 * @Modified By :
 * @Version :
 */
public class DijkstraManager {
    private static final Logger logger = LoggerFactory.getLogger(DijkstraManager.class);
    /**
     * 类级的内部类，也就是静态的成员式内部类，该内部类的实例与外部类的实例
     * 没有绑定关系，而且只有被调用到才会装载，从而实现了延迟加载
     */
    //私有化构造方法
    private DijkstraManager() {
    }

    private static class DijkstraManagerHolder {
        //静态初始化器，由JVM来保证线程安全
        private static DijkstraManager instance = new DijkstraManager();
    }

    public static DijkstraManager getInstance() {
        return DijkstraManagerHolder.instance;
    }

    public synchronized MarkerPathResult plan(String agvCode, DirectedGraph directedGraph, String startMarkerId, String aimMarkerId, boolean dynamic) {
        if (agvCode == null) {
            logger.error("agvCode is null");
            return new MarkerPathResult();
        }
        if (startMarkerId == null) {
            logger.error("startMarkerId is null");
            return new MarkerPathResult();
        }
        if (startMarkerId.equals(aimMarkerId)) {
            //当起始点与目标点重合，返回成功
            MarkerPathResult markerPathResult = new MarkerPathResult();
            markerPathResult.setResult(true);
            markerPathResult.setCost(0D);
            return markerPathResult;
        }

        DijkstraAlgorithm dijkstraAlgorithm = new DijkstraAlgorithm(agvCode, directedGraph);
        dijkstraAlgorithm.setDynamic(dynamic);
        MarkerPathResult markerPathResult = dijkstraAlgorithm.plan(startMarkerId, aimMarkerId);
        List<Marker> markerList = markerPathResult.getMarkerPath().stream().map(MapGraphUtil::getMarkerByMarkerId).collect(Collectors.toList());
        //logger.debug("agvCode:" + agvCode + " markerPathResult:" + markerList.toString());
        return markerPathResult;
    }
}
