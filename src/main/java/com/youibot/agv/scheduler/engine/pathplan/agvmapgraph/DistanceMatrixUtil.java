package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.util.JsonFileUtils;
import com.youibot.agv.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.MapGraphInfo;
import com.youibot.agv.scheduler.map.entity.PathMd5;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 上午11:54 2021/1/8
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
public class DistanceMatrixUtil {

    private static AGVMapService agvMapService = (AGVMapService) ApplicationUtils.getBean("AGVMapServiceImpl");

    private static DistanceMatrix distanceMatrixClass = (DistanceMatrix) ApplicationUtils.getBean("distanceMatrix");

    private static String cacheMapDataPath = "/server/data/cacheMapData.json";
    //private static String cacheMapDataPath = "D://cacheMapData.json";

    private static boolean needToReload = true;

    public static void setNeedToReload() {
        needToReload = true;
    }

    private static synchronized void init() {
        if (distanceMatrixClass.getDistanceMatrix() == null || needToReload) {
            List<AGVMap> agvMaps = agvMapService.findAll();
            Set<String> activeAgvMapIds = MapGraphUtil.getActiveAgvMapIds();
            List<AGVMap> activeAgvMaps = agvMaps.stream().filter(agvMap -> activeAgvMapIds.contains(agvMap.getId())).collect(Collectors.toList());

            String jsonString = JsonFileUtils.readJsonFile(cacheMapDataPath);
            CalCacheMapData calCacheMapData = null;
            try {
                calCacheMapData = JSON.parseObject(jsonString, CalCacheMapData.class);
            } catch (Exception e) {
                LogExceptionStackUtil.LogExceptionStack(e);
            }
            boolean recalculateMapData = false;
            if (calCacheMapData != null) {
                Map<String, String> agvMapToPathMd5s = calCacheMapData.getAgvMapToPathMd5s();
                for (AGVMap activeAgvMap : activeAgvMaps) {
                    if (agvMapToPathMd5s == null) {
                        recalculateMapData = true;
                        break;
                    }
                    String agvMapId = activeAgvMap.getId();
                    MapGraphInfo graphInfo = AGVMapInfoCache.getCache(activeAgvMap.getName());
                    if(Objects.isNull(graphInfo)) {
                    	log.error("data_info_is_null:{}" , activeAgvMap.getName());
                    }
					String pathMd5 = graphInfo.getPathMd5().getPathMd5();
                    if (agvMapToPathMd5s.get(agvMapId) == null || !agvMapToPathMd5s.get(agvMapId).equals(pathMd5)) {
                        recalculateMapData = true;
                        break;
                    }
                }
            } else {
                recalculateMapData = true;
            }
            if (recalculateMapData) {
                distanceMatrixClass.distanceMatrixInit(MapGraphUtil.getOriginDirectedGraph());
                Map<String, String> agvMapToPathMd5s = new HashMap<>(16);
                for (AGVMap activeAgvMap : activeAgvMaps) {
                    MapGraphInfo cache = AGVMapInfoCache.getCache(activeAgvMap.getName());
					PathMd5 pathMd52 = cache.getPathMd5();
					if(Objects.isNull(pathMd52)) {
						log.error("pathMd5_error:{}" , activeAgvMap.getName());
						continue;
					}
					String pathMd5 = pathMd52.getPathMd5();
                    agvMapToPathMd5s.put(activeAgvMap.getId(), pathMd5);
                }
                CalCacheMapData cacheMapData = new CalCacheMapData();
                cacheMapData.setDistanceMatrix(distanceMatrixClass.getDistanceMatrix());
                cacheMapData.setAgvMapToPathMd5s(agvMapToPathMd5s);
                JsonFileUtils.writeJsonFile(cacheMapDataPath, cacheMapData);
            } else {
                distanceMatrixClass.setDistanceMatrix(calCacheMapData.getDistanceMatrix());
            }
            needToReload = false;
        }
       
    }


    public static Map<String, Map<String, Double>> getDistanceMatrix() {
        init();
        return distanceMatrixClass.getDistanceMatrix();
    }

    public static Double getDistance(Map<String, Map<String, Double>> distanceMatrix, String begin, String end) {
        Double temp = null;
        if (distanceMatrix.get(begin) == null || distanceMatrix.get(begin).get(end) == null) {
            temp = Double.POSITIVE_INFINITY;
        } else {
            temp = distanceMatrix.get(begin).get(end);
        }
        return temp;
    }


}
