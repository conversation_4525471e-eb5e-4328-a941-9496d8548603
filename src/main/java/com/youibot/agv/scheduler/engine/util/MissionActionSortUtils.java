package com.youibot.agv.scheduler.engine.util;

import com.youibot.agv.scheduler.controller.v3.AGVMapController;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MissionActionSortUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(AGVMapController.class);

    private static MissionActionService missionActionService = (MissionActionService) ApplicationUtils.getBean("missionActionServiceImpl");

    /**
     * 将missionAction列表根据sequence进行升序排序
     *
     * @param missionActionList
     */
    public static List<MissionAction> sortMissionActionListBySequence(List<MissionAction> missionActionList) {
        //根据missionAction中的 sequence 进行升序排序，保证获取任务动作的顺序准确性
        List<MissionAction> missionActions = new ArrayList<>();
        if (missionActionList.size() <= 1) {
            return missionActionList;
        }
        MissionAction firstAction = missionActionList.stream().filter(action -> StringUtils.isEmpty(action.getPreAction())).findFirst().orElse(null);
        MissionAction lastAction = missionActionList.stream().filter(action -> StringUtils.isEmpty(action.getNextAction())).findFirst().orElse(null);
        if (firstAction == null || lastAction == null) {
            LOGGER.error("任务列表的数据有误,找不到第一个动作");
            throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
        }

        missionActions.add(firstAction);
        getNextAction(firstAction, missionActions);
        return missionActions;
    }

    private static void getNextAction(MissionAction missionAction, List<MissionAction> missionActions) {
        String nextActionId = missionAction.getNextAction();
        if (!StringUtils.isEmpty(nextActionId)) {
            MissionAction nextAction = missionActionService.selectById(nextActionId);
            if (nextAction != null) {
                missionActions.add(nextAction);
                getNextAction(nextAction, missionActions);
            }
        }
    }
}
