package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.youibot.agv.scheduler.vehicle.Vehicle;
import lombok.Data;

/**
 * <AUTHOR> E-mail:song<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2021-07-15 14:46
 */
@Data
public class VehicleScope {
    private Vehicle vehicle;
    private Double scope;

    private String makerId;
    
    private Vehicle vehicle2;
    
    public VehicleScope(Vehicle vehicle, Double scope) {
        this.vehicle = vehicle;
        this.scope = scope;
    }
    
    public VehicleScope(Vehicle vehicle, Double scope , Vehicle vehicle2) {
        this.vehicle = vehicle;
        this.scope = scope;
        this.vehicle2 = vehicle2; 
    }
    public VehicleScope(String makerId, Double scope) {
        this.makerId = makerId;
        this.scope = scope;
    }
    
}
