package com.youibot.agv.scheduler.engine.entity;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.entity.AGVPathParam;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_CURVE;
import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_STRAIGHT;

/**
 * 路径导航参数(发送给AGV)
 * {
 * "path_params" : {
 * "localization_method": "laser",  // 定位方式 "laser":激光，"qr":二维码，"odom":里程计
 * "direction": 1  // 速度方向 1:正向, 2:反向
 * },
 * "segments": [{
 * "type": "bezier", // "line"
 * "shape": {
 * "segment_id": "1",
 * "length": 1.0, // 贝塞尔曲线长度
 * "t0": 0.0, //贝塞尔曲线的起始端点
 * "t1": 1.0, //贝塞尔曲线的结束端点
 * "start": {
 * "id": "1",
 * "x": 1.2, // m
 * "y": 1.3 // m
 * },
 * "end": {
 * "id": "2",
 * "x": 1.5, // m
 * "y": 1.3 // m
 * },
 * "control_1": {
 * "x": 1.2, // m
 * "y": 1.3 // m
 * },
 * "control_2": {
 * "x": 1.5, // m
 * "y": 1.3 // m
 * }
 * },
 * "segment_params": {
 * "safety": 1, // 1:打开避障，2:关闭避障
 * "front_laser_dec_area_x": 1.5,  // 前雷达避障减速区x >0 m
 * "front_laser_dec_area_y": 0.5,   // 前雷达避障减速区y >0 m
 * "front_laser_stop_area_x": 0.5,  // 前雷达避障停止区x >0 m
 * "front_laser_stop_area_y": 0.3,  // 前雷达避障停止区y >0 m
 * "back_laser_dec_area_x": 1.5,  // 后雷达避障减速区x >0 m
 * "back_laser_dec_area_y": 0.5,  // 后雷达避障减速区y >0 m
 * "back_laser_stop_area_x": 0.5,  // 后雷达避障停止区x >0 m
 * "back_laser_stop_area_y": 0.3,  // 后雷达避障停止区y >0 m
 * "max_translation_speed": 0.8, // 最大平移速度 >0 m/s
 * "max_rotate_speed": 0.8, // 最大旋转速度 rad/s >0
 * "P": 1.2,  // 平移加速比例控制系数 >0
 * "D": 0.15,  // 平移加速微分控制系数 >0
 * "translation_acc": 0.3, // 平移加（减）速度 m/s2 >0
 * "rotate_acc": 0.4 // 旋转加（减）速度 rad/s2 >0
 * }
 * }]
 * }
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/13 15:51
 */
@Data
public class PathNavigationParam {

    @JSONField(serialize = false)
    private Logger logger = LoggerFactory.getLogger(PathNavigationParam.class);

    private String id;

    private String alter_id;

    private path_params path_params;

    private List<segment> segments;

    public PathNavigationParam(SidePath sidePath, AGVPathParam agvPathParam, Integer direction , Integer direction2, Boolean need_direction2, String localization_method) {
        this.path_params = new path_params(direction, localization_method,need_direction2,direction2);
        this.segments = new ArrayList<>();
        segments.add(new segment(sidePath, agvPathParam));//第一次创建只添加一个segment，如有多个，后续调用addSegment()添加
        this.alter_id = UUID.randomUUID().toString();
    }

    public void addSegment(SidePath sidePath, AGVPathParam agvPathParam) {
        this.segments.add(new segment(sidePath, agvPathParam));
    }

    @JSONField(serialize = false)
    private MarkerService markerService = (MarkerService) ApplicationUtils.getBean("markerServiceImpl");


    @Data
    public class path_params {
        private String localization_method; //定位方式 "laser":激光，"qr":二维码，"odom":里程计
        private Integer direction; //速度方向 1:正向, 2:反向
        private Boolean need_direction2;
        private Integer direction2;

        public path_params(Integer direction, String localization_method,Boolean need_direction2,Integer direction2) {
            this.direction = direction;
            this.localization_method = localization_method;
            this.direction2 = direction2;
            this.need_direction2 = need_direction2;
        }
    }

    @Data
    public class segment {
        private String type;
        private Shape shape;
        private SegmentParam segment_params;

        public segment(SidePath sidePath, AGVPathParam agvPathParam) {
            switch (sidePath.getLineType()) {
                case PATH_LINE_TYPE_STRAIGHT: {
                    type = "line";
                    break;
                }
                case PATH_LINE_TYPE_CURVE: {
                    type = "bezier";
                    break;
                }
            }
            this.shape = new Shape(sidePath);
            SegmentParam segmentParam = new SegmentParam();
            BeanUtils.copyProperties(agvPathParam, segmentParam);
            //获取拓展布尔和拓展字符串的值并转换
            String extend_string = agvPathParam.getExtend_string();
            String extend_bit = agvPathParam.getExtend_bit();
            Integer i = Integer.parseInt(extend_bit, 2);
            String[] stringList = ArrayUtils.EMPTY_STRING_ARRAY;
            if (!StringUtils.isEmpty(extend_string)) {
                stringList = extend_string.split(",");
            }
            segmentParam.setAccurate_stop(agvPathParam.getAccurate_stop());
            segmentParam.setExtend_string(stringList);
            segmentParam.setExtend_bit(i);
            this.segment_params = segmentParam;
        }

        @Data
        public class Shape {
            private String segment_id; //sidePathId
            private Double length; //贝塞尔曲线长度
            private Double t0; //贝塞尔曲线的起始端点
            private Double t1; //贝塞尔曲线的结束端点
            private start start; //开始点坐标
            private end end; //结束点坐标
            private JSONObject control_1; //第一个（开始点）控制点坐标
            private JSONObject control_2; //第二个（结束点）控制点坐标

            public Shape(SidePath sidePath) {
                Marker startMarker = markerService.selectById(sidePath.getAgvMapName(), sidePath.getStartMarkerId(), false);
                Marker endMarker = markerService.selectById(sidePath.getAgvMapName(), sidePath.getEndMarkerId(), false);
                if (startMarker == null || endMarker == null) {
                    logger.error("start marker or end marker is null, sidePathId: " + sidePath.getId());
                    throw new PathPlanException(MessageUtils.getMessage("http.marker_start_or_end_not_exist"));
                }
                this.segment_id = sidePath.getId();
                this.t0 = sidePath.getT0();
                this.t1 = sidePath.getT1();
                this.length = sidePath.getLength();
                this.start = new start(startMarker);
                this.end = new end(endMarker);
                switch (sidePath.getLineType()) {
                    case PATH_LINE_TYPE_STRAIGHT: {
                        this.control_1 = null;
                        this.control_2 = null;
                        break;
                    }
                    case PATH_LINE_TYPE_CURVE: {
                        this.control_1 = JSONObject.parseObject(sidePath.getStartControl());
                        this.control_2 = JSONObject.parseObject(sidePath.getEndControl());
                        break;
                    }
                }
            }

            @Data
            public class start {
                private String id;
                private Double x;
                private Double y;

                public start(Marker marker) {
                    this.id = marker.getId();
                    this.x = marker.getX();
                    this.y = marker.getY();
                }
            }

            @Data
            public class end {
                private String id;
                private Double x;
                private Double y;

                public end(Marker marker) {
                    this.id = marker.getId();
                    this.x = marker.getX();
                    this.y = marker.getY();
                }
            }
        }

        @Data
        public class SegmentParam {
            private Integer safety_scanner_region;//安全雷达区域切换，可设置为0，9,10,11  此设置为安全雷达避障，与上方点云避障无关
            private Integer features_requested;//0: 在执行该segment时，不需要有足够多融合特征进行定位。1: 要求在执行该segment时，需要有足够多融合特征进行定位，否则会报错。（默认为0，在特征融合末端对接手动置1）
            private Integer safety; //是否打开避障 1:打开避障，2:关闭避障
            private Double front_laser_dec_area_x; //前雷达避障减速区x >0 m
            private Double front_laser_dec_area_y; //前雷达避障减速区y >0 m
            private Double front_laser_stop_area_x; //前雷达避障停止区x >0 m
            private Double front_laser_stop_area_y; //前雷达避障停止区y >0 m
            private Double back_laser_dec_area_x; //后雷达避障减速区x >0 m
            private Double back_laser_dec_area_y; //后雷达避障减速区y >0 m
            private Double back_laser_stop_area_x; //后雷达避障停止区x >0 m
            private Double back_laser_stop_area_y; //后雷达避障停止区y >0 m
            private Double max_translation_speed; //最大平移速度 >0 m/s
            private Double max_rotate_speed; //最大旋转速度 rad/s >0
            @JSONField(name = "P")
            private Double P; //平移加速比例控制系数 >0
            @JSONField(name = "D")
            private Double D; //平移加速微分控制系数 >0
            private Double translation_acc; //平移加（减）速度 m/s2 >0
            private Double rotate_acc; //旋转加（减）速度 rad/s2 >0
            private Integer accurate_stop;//末端精定位
            private Integer extend_bit;//拓展布尔
            private String[] extend_string;//拓展字符串
            /**
             * 是否启用定向导航
             */
            private Integer fix_angle_navi = 0;

            private Double[] fix_angle_vector ;
        }
    }

}
