package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Map;

/**
 * 设备上下料申请
 *
 * @Author：yangpeilin
 * @Date: 2020/7/7 15:48
 */
@Service
@Scope("prototype")
public class DeviceOperateApplyAction extends DefaultAction {

    Logger logger = LoggerFactory.getLogger(DeviceOperateApplyAction.class);

    @Autowired
    private HttpClientService httpClientService;

    @Override
    public Map<String, Object> sendCommand() throws IOException, InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        try {
            JSONObject paramJson = this.getParamJson();
            Integer type = paramJson.getInteger("type");
            String eqpId = paramJson.getString("eqpId");
            String address = paramJson.getString("address");
            if (type == null || StringUtils.isEmpty(type) || StringUtils.isEmpty(address) || StringUtils.isEmpty(eqpId)) {
                throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
            }
            paramJson.remove("address");
            paramJson.put("agv", vehicle.getId());
            paramJson.put("missionWorkId", missionWorkId);
            if (!address.startsWith("http://")) {
                address = "http://";
            }
            HttpResult httpResult = httpClientService.doPost(address, paramJson);
            logger.debug("deviceOperateApply httpResult:{}", JSON.toJSONString(httpResult));
            if (httpResult == null || StringUtils.isEmpty(httpResult.getBody())) {
                logger.error("http request deviceOperateApply error, result body: " + httpResult.getBody());
                throw new ActionException(ErrorEnum.HTTP_POST_RETURN_ERROR.code(), ErrorEnum.HTTP_POST_RETURN_ERROR.msg());
            }
            JSONObject parse = JSONObject.parseObject(httpResult.getBody());
            Integer code = (Integer) parse.get("code");
            if (code == null || code > 0) {
                logger.error("http request deviceOperateApply fail, result body: " + parse);
                throw new ActionException(ErrorEnum.HTTP_POST_RETURN_ERROR.code(), ErrorEnum.HTTP_POST_RETURN_ERROR.msg());
            }
            if (code == -1) {
                logger.error("http request deviceOperateApply timeout, result code: " + code);
                throw new ActionException(ErrorEnum.HTTP_POST_RETURN_ERROR.code(), ErrorEnum.HTTP_POST_RETURN_ERROR.msg());
            }
            return parse;
        } catch (Exception e) {
            return reExecute(e);
        }
    }

    @Override
    public String getAPICode() {
        return null;
    }
}
