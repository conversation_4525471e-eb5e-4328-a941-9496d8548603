package com.youibot.agv.scheduler.engine.service.heartbeat;

import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.vehicle.DefaultVehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import static com.youibot.agv.scheduler.constant.AGVConstant.CONNECT_STATUS_FAULT;
import static com.youibot.agv.scheduler.constant.AGVConstant.CONNECT_STATUS_SUCCESS;

@Service
@Scope("prototype")
public class DefaultHeartbeatThread extends Thread {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultHeartbeatThread.class);

    private DefaultVehicle defaultVehicle;
    private AGVSocketClient client;

    @Value("${AGV_SOCKET.PORT.HEARTBEAT}")
    private Integer heartbeatPort;
    @Value("${AGV_THREAD_CONFIG.WAIT_TIME.HEARTBEAT_CONNECTION_INTERVAL}")
    private Integer intervalTime;//发送心跳连接时间间隔

    @Autowired
    private DefaultHeartbeatApiService defaultHeartbeatApiService;

    public void start(DefaultVehicle defaultVehicle) {
        this.defaultVehicle = defaultVehicle;
        this.setPriority(MAX_PRIORITY);
        this.start();
    }

    @Override
    public void run() {
        LOGGER.debug("心跳检测线程开启...........");
        while (true) {
            try {
                Thread.sleep(intervalTime);
                if (client == null) {
                    this.client = AGVSocketClient.createAGVClient(defaultVehicle.getIp(), heartbeatPort);
                }
                defaultHeartbeatApiService.heartbeat(client);//发送心跳连接指令
                if (!CONNECT_STATUS_SUCCESS.equals(defaultVehicle.getConnectStatus())) {
                    defaultVehicle.initOnConnection();
                    defaultVehicle.setConnectStatus(CONNECT_STATUS_SUCCESS);
                }
            } catch (Exception e) {
                LOGGER.error("心跳检测出错, ", e.getMessage());
                defaultVehicle.setConnectStatus(CONNECT_STATUS_FAULT);
                if (client != null) {
                    client.stop();
                    client = null;
                }
            }
        }
    }

}
