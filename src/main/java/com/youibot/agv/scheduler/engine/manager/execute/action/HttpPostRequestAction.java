package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:shis<PERSON><EMAIL>
 * @version CreateTime: 2019/7/24 20:32
 */
@Service
@Scope("prototype")
public class HttpPostRequestAction extends DefaultAction {

    @Autowired
    private HttpClientService httpClientService;

    @Autowired
    private MissionWorkService missionWorkService;

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpPostRequestAction.class);

    @Override
    public Map<String, Object> execute(Vehicle vehicle) throws InterruptedException, IOException {
        try {
            if (Thread.interrupted()) {//如果当前线程被中断了
                throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
            }
            this.vehicle = vehicle;
            JSONObject paramJson = super.getParamJson();
            String httpUrl = paramJson.getString("httpUrl");
            String httpBody = !StringUtils.isEmpty(paramJson.getString("httpBody")) ? paramJson.getString("httpBody") : "{}";
            if (StringUtils.isEmpty(httpUrl)) {
                LOGGER.error("httpUrl is empty, missionWorkActionId =" + missionWorkAction.getId());
                throw new ADSParameterException(MessageUtils.getMessage("action.http_url_is_empty"));
            }
            JSONObject httpBodyJson = JSONObject.parseObject(httpBody);

            //添加默认参数
            MissionWork missionWork = missionWorkService.selectById(missionWorkAction.getMissionWorkId());
            if (missionWork == null) {
                LOGGER.error("mission work is null");
                throw new ADSParameterException(MessageUtils.getMessage("service.mission_work_is_null"));
            }
            String missionId = missionWork.getMissionId();
            httpBodyJson.put("missionWorkId", missionWorkAction.getMissionWorkId());
            httpBodyJson.put("missionId", missionId);

            HttpResult httpResult = httpClientService.doPost(httpUrl, httpBodyJson);
            Integer code = httpResult.getCode();
            if (200 <= code && code < 300) {//调用返回成功
                return null;
            } else {//失败
                LOGGER.error("http request error, result body: " + httpResult.getBody());
                throw new ActionException(code, MessageUtils.getMessage("action.http_post_return_error"));
            }
        } catch (InterruptedException e) {
            LOGGER.warn("mission action name : " + missionWorkAction.getName() + " thread is interrupted, ");
            throw e;
        } catch (Exception e) {
            LOGGER.error("http post request action execute", e);
            return super.reExecute(e);
        }
    }
}
