package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool;

import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.OPathResource;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.ZonePathResource;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date :Created in 11:18 2021/4/1
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
@Component
public class OPathResourcePool extends ZonePathResourcePool<OPathResource> {

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private FullLocationService fullLocationService;

    public void addOPathResources(List<List<String>> oPaths) {
        if (CollectionUtils.isEmpty(oPaths)) return;
        for (Collection<String> oPathMarkerIds : oPaths) {
            if (CollectionUtils.isEmpty(oPathMarkerIds)) continue;
            OPathResource pathResource = new OPathResource();
            pathResource.setZonePathResource(new HashSet<>(oPathMarkerIds));
            super.addZonePathResources(pathResource);
        }
    }

    public void clearOPathResources() {
        super.clearZonePathResources();
    }

    /**
     * 重写One path的查询是否可用的方法。
     * 添加了资源检测，检测区域是否已经被申请，还要检测区域已经被定位占用的
     * 根据所有的机器人的位置遍历是否存在实际定位在区域内的机器人）
     *
     * @param vehicleCode
     * @param pathResourceIds
     * @return
     */
    @Override
    public synchronized boolean queryPathResourceApplyAvailable(String vehicleCode, Set<String> pathResourceIds) {
        // 如果已经有申请占用的车辆了。直接返回false.
        if (!super.queryPathResourceApplyAvailable(vehicleCode, pathResourceIds)) {
            return false;
        }
        /**
         * 如果没有被申请。再检测所有的区域是否被其他机器人占用。
         * 检测所有区域是否被其他机器人占用。有一个区域被其他机器占用。则返回失败。
         */
        for (String pathResourceId : pathResourceIds) {
            if (this.checkOtherVehicleLocationResourceIds(vehicleCode, super.get(pathResourceId))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 遍历所有其他的机器人，检测是否有机器人占用了这个区域。
     * 如果有机器人位置占用这个区域则返回true. 如果没有机器人位置实际占用这个区域则返回false.
     */
    public boolean checkOtherVehicleLocationResourceIds(String currentVehicleCode, OPathResource oPathResource) {
        List<Vehicle> vehicles = vehiclePoolService.selectAll();
        for (Vehicle vehicle : vehicles) {
            if (vehicle.getId().equals(currentVehicleCode)) {
                continue;
            }
            /**
             * 机器人可能在点上，也可能在边上。如果在点上，直接根据点们去查询所有的资源。
             * 如果在边上。可能在多个边上。需要把所有边上上的站点都拿出来和区域进行比对。
             */
            VehicleLocation vehicleLocation = fullLocationService.getVehicleLocation(vehicle.getId());
            if (vehicleLocation != null) {
                // 如果机器人的站点在这个区域内。则返回true.
                if (vehicleLocation.getMarker() != null) {
                    return oPathResource.getZonePathResource().contains(vehicleLocation.getMarker().getId());
                }
                // 如果机器人在多个边上，则一条一条边进行检测。如果有一个边在区域内。则返回true.
                if (vehicleLocation.getSidePaths() != null) {
                    for (SidePath sidePath : vehicleLocation.getSidePaths()) {
                        String startMakerId = sidePath.getStartMarkerId();
                        String endMarkerId = sidePath.getEndMarkerId();
                        if (oPathResource.getZonePathResource().contains(startMakerId) && oPathResource.getZonePathResource().contains(endMarkerId)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }


    public HashSet<String> queryOPathResourceIdByMarkerId(String markerId) {
        return queryZonePathResourceIdByPathResourcesId(markerId);
    }

    /**
     * 通过marker列表，返回需要申请的资源列表。
     * 用于车辆在下发路径时，检测需要下发路径时如果通过OnePath资源需要进行申请。
     *
     * @param markerIds
     * @return
     */
    public HashSet<String> queryOPathResourceIdsByMarkerIds(LinkedList<String> markerIds) {
        HashSet<String> resultSet = new HashSet<>();
        if (CollectionUtils.isEmpty(markerIds)) {
            return resultSet;
        }
        /**
         * 检测下发路径的最后一个点是否在资源池内。
         * 如果在。则返回需要申请的资源。
         */
        resultSet = queryZonePathResourceIdByPathResourcesId(markerIds.getLast());

        /**
         * 如果站点列表大于2个点。
         * 则需要检测站点列表中是否包含资源池的站点列表。如果包含返回资源。
         */
        if (markerIds.size() > 2) {
            for (ZonePathResource zonePathResource : super.getPoolEntries().values()) {
                if (markerIds.containsAll(zonePathResource.getZonePathResource())) {
                    resultSet.add(zonePathResource.getId());
                }
            }
        }
        return resultSet;
    }

    public Set<String> queryOPathResourceIdsByMarkerIdsToRelease(LinkedList<String> markerIds) {
        return queryZonePathResourceIdsByPathResourcesIds(markerIds);
    }
}
