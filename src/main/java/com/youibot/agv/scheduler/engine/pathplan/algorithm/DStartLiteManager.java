package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.*;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.entity.Marker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date :Created in 下午4:44 2020/8/17
 * @Description :
 * @Modified By :
 * @Version :
 */
public class DStartLiteManager {

    private static final Logger logger = LoggerFactory.getLogger(DStartLiteManager.class);

    /**
     * 类级的内部类，也就是静态的成员式内部类，该内部类的实例与外部类的实例
     * 没有绑定关系，而且只有被调用到才会装载，从而实现了延迟加载
     */
    //私有化构造方法
    private DStartLiteManager() {
    }

    private static class DStartLiteManagerHolder {
        //静态初始化器，由JVM来保证线程安全
        private static DStartLiteManager instance = new DStartLiteManager();
    }

    public static DStartLiteManager getInstance() {
        return DStartLiteManagerHolder.instance;
    }

    public synchronized MarkerPathResult plan(String agvCode, DirectedGraph directedGraph, String startMarkerId, String aimMarkerId, boolean dynamic) {
        if (agvCode == null) {
            logger.error("agvCode is null");
            return new MarkerPathResult();
        }
        if (startMarkerId == null) {
            logger.error("startMarkerId is null");
            return new MarkerPathResult();
        }
        if (startMarkerId.equals(aimMarkerId)) {
            //当起始点与目标点重合，返回成功
            MarkerPathResult markerPathResult = new MarkerPathResult();
            markerPathResult.setResult(true);
            markerPathResult.setCost(0D);
            return markerPathResult;
        }

        DStarLiteAlgorithm dStarLiteAlgorithm = new DStarLiteAlgorithm(agvCode, directedGraph);
        dStarLiteAlgorithm.setDynamic(dynamic);
        MarkerPathResult markerPathResult = dStarLiteAlgorithm.plan(startMarkerId, aimMarkerId);
        //logger.debug("agvCode:[{}], markerPathResult:[{}]", agvCode, markerPathResult);
        return markerPathResult;
    }

    //public synchronized MarkerPathResult planNear(String agvCode, String startMarkerId, String aimMarkerId) {
    //    MarkerPathResult markerPathResult = new MarkerPathResult();
    //    markerPathResult.setSidePath(null);
    //    DirectedGraph fDirectedGraph = MapGraphUtil.getCloneDirectedGraph();
    //    List<String> reachableList = reachableMarkerIds(agvCode, fDirectedGraph, startMarkerId);
    //    if (reachableList == null) {
    //        markerPathResult.setResult(true);
    //        return markerPathResult;
    //    }
    //    int randomCount = 4;
    //    List<String> S = new ArrayList<>();//存放已经搜索过的点
    //    List<String> U = new ArrayList<>();//存放还未搜索过的点
    //    S.add(aimMarkerId);
    //    List<String> markerIds = MapGraphUtil.getEnableMarkers().stream().map(Marker::getId).collect(Collectors.toList());
    //    for (String markerCode : markerIds) {
    //        if (!startMarkerId.equals(markerCode)) {
    //            U.add(markerCode);
    //        }
    //    }
    //    List<String> aimNearPoint = new ArrayList<>();
    //
    //    //从U中选择点，使得该点能够到达S
    //    while (true) {
    //        boolean containedFlag = false;//U中是否存在能被S中的点到达的目标点
    //        //从U中选择点，使得S中的点能够到达改点
    //        for (int j = 0; j < U.size(); j++) {
    //            //判断S中的点k是否能到达点j
    //            for (int k = 0; k < S.size(); k++) {
    //                String sk = S.get(k);
    //                String uj = U.get(j);
    //                if (fDirectedGraph.getBackwardCost(uj, sk, agvCode, false) != Double.POSITIVE_INFINITY)//当S中的点能到达U中的点时，将其加入到S中
    //                {
    //                    S.add(uj);
    //                    U.remove(uj);
    //                    if (reachableList.contains(uj)) {
    //                        aimNearPoint.add(uj);
    //                    }
    //                    containedFlag = true;
    //                }
    //                if (containedFlag) {
    //                    break;
    //                }
    //            }
    //            if (containedFlag) {
    //                break;
    //            }
    //        }
    //        if (aimNearPoint.size() >= randomCount) {
    //            break;
    //        }
    //        if (!containedFlag)//U中不存在能被S中的点到达的目标点,迭代结束,返回集合S
    //        {
    //            break;
    //        }
    //    }
    //    if (aimNearPoint.size() == 0) {
    //        logger.debug("没有在标点附近找到可以到达的点，返回空数组，等待下一次规划");
    //        markerPathResult.setResult(true);
    //        return markerPathResult;
    //    } else {
    //        Random random = new Random(System.currentTimeMillis());
    //        int k = random.nextInt(65535) % aimNearPoint.size();
    //        String randomAimMarkerId = aimNearPoint.get(k);
    //        //logger.debug("aimNearPoint:" + aimNearPoint);
    //        logger.debug("aimMarkerId:[{}],randomAimMarkerId:[{}]", aimMarkerId, randomAimMarkerId);
    //        return this.plan(agvCode, startMarkerId, randomAimMarkerId);
    //    }
    //}

    //private static List<String> reachableMarkerIds(String agvCode, DirectedGraph directedGraph, String startMarkerId) {
    //    List<String> markerIds = getFilterMarkerId();
    //    List<String> S = new ArrayList<>();//存放可以到达的点
    //    List<String> U = new ArrayList<>();//存放还未放的点
    //    //初始化，将startIndex加入S中,将其它点加入到U中
    //    S.add(startMarkerId);
    //    for (String markerCode : markerIds) {
    //        if (!startMarkerId.equals(markerCode)) {
    //            U.add(markerCode);
    //        }
    //    }
    //    while (true) {
    //        boolean containedFlag = false;//U中是否存在能被S中的点到达的目标点
    //        //从U中选择点，使得S中的点能够到达改点
    //        for (int j = 0; j < U.size(); j++) {
    //            //判断S中的点k是否能到达点j
    //            for (int k = 0; k < S.size(); k++) {
    //                String sk = S.get(k);
    //                String uj = U.get(j);
    //                if (directedGraph.getBackwardCost(sk, uj, agvCode, true) != Double.POSITIVE_INFINITY)//当S中的点能到达U中的点时，将其加入到S中
    //                {
    //                    S.add(uj);
    //                    U.remove(uj);
    //                    containedFlag = true;
    //                }
    //                if (containedFlag) {
    //                    break;
    //                }
    //            }
    //            if (containedFlag) {
    //                break;
    //            }
    //        }
    //        if (!containedFlag)//U中不存在能被S中的点到达的目标点,迭代结束,返回集合S
    //        {
    //            break;
    //        }
    //    }
    //    //可达点中人为去除当前AGV站点
    //    S.remove(startMarkerId);
    //    return S;
    //}

    //private static List<String> getFilterMarkerId() {
    //    Set<Marker> enableMarkers = MapGraphUtil.getEnableMarkers();
    //    List<String> filterMarkerIds = new ArrayList<>();
    //    for (Marker marker : enableMarkers) {
    //        String type = marker.getType();
    //        if (!MARKER_TYPE_CHARGING.equals(type) && !MARKER_TYPE_ELEVATOR.equals(type) && !MARKER_TYPE_WORK.equals(type)) {
    //            filterMarkerIds.add(marker.getId());
    //        }
    //    }
    //    return filterMarkerIds;
    //}

}
