package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionWorkActionParameterService;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.MissionConstant.CHILD_TYPE_WHILE;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_ACTION_ATTRIBUTE_BATTERY_VALUE;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_ACTION_ATTRIBUTE_FREE_TIME;

/**
 * while 表达式action
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019年5月20日 下午4:45:53
 */
@Service
@Scope("prototype")
public class WhileAction extends LogicAction {

    Logger logger = LoggerFactory.getLogger(WhileAction.class);

    @Autowired
    private MissionWorkActionParameterService missionWorkActionParameterService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private MissionActionService missionActionService;

    @Override
    public Map<String, Object> execute(Vehicle vehicle) throws InterruptedException, IOException {
        super.vehicle = vehicle;
        while (true) {
            if (Thread.interrupted()) {//如果当前线程被中断了
                throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
            }

            JSONObject paramJson = this.getParamJson();
            String compareAttribute = paramJson.getString("compare");
            String operator = paramJson.getString("operator");
            Double value = paramJson.getDouble("value");
            if (StringUtils.isEmpty(compareAttribute) || StringUtils.isEmpty(operator) || value == null) {
                logger.error("compare or operator or value is null, missionWorkActionId: " + missionWorkAction.getId());
                throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
            }

            boolean compareResult;
            if (MISSION_ACTION_ATTRIBUTE_BATTERY_VALUE.equals(compareAttribute) || MISSION_ACTION_ATTRIBUTE_FREE_TIME.equals(compareAttribute)) {
                Double attributeValue = super.getAttributeValue(compareAttribute);//获取需要比较的属性值
                compareResult = super.compare(attributeValue, operator, value);//进行比较，if条件成功返回true 否则false
            } else {//如果属性类型非电量和空闲时间(使用全局变量value)
                compareResult = super.compare(Double.valueOf(compareAttribute), operator, value);//进行比较，if条件成功返回true 否则false
            }

            if (compareResult) {//if 表达为true
                List<MissionAction> subMissionActions = missionActionService.selectByParentActonIdAndChildType(missionWorkAction.getMissionActionId(), CHILD_TYPE_WHILE);
                List<MissionWorkAction> workActions = super.executeSubMissionAction(subMissionActions);//执行子action
                for (MissionWorkAction workAction : workActions) {
                    workAction.setThisLoopCompletes(true);
                    missionWorkActionService.update(workAction);
                }
            } else {
                return null;
            }
        }
    }

    @Override
    protected JSONObject getParamJson() {
        String parameters = missionWorkActionParameterService.getMissionWorkActionParameters(missionWorkAction, missionWorkId);
        missionWorkAction.setParameters(parameters);
        return super.getParamJson();
    }
}
