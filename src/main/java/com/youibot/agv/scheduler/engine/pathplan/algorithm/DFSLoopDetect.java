package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.google.common.collect.Lists;
import com.sun.javafx.binding.StringFormatter;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedNode;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:01 2020/12/15
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
public class DFSLoopDetect {

    private UndirectedGraph graph = new UndirectedGraph();                  //输入的无向图
    private final List<ArrayList<String>> loops = new ArrayList<>();                   //存储所有搜索到的环，最终输出结果

    //private final Map<String, List<ArrayList<String>>> nodeIdToLoops = new ConcurrentHashMap<>();

    public DFSLoopDetect() {
    }

    public void setGraph(UndirectedGraph graph) {
        this.graph = graph;
    }


    public boolean hasCyclic() {
        return graph.hasCyclic();
    }

    private String printLoop(ArrayList<String> list) {
        StringBuilder sb = new StringBuilder();
        for (String s : list) {
            sb.append(StringFormatter.format("[%s]->", s));
        }
        return sb.toString();
    }

    //快速一点的搜索，不找到所有的环，但环中的点都会被包含
    public List<ArrayList<String>> DFSTraverse1() {
        loops.clear();
        Map<String, UndirectedNode> nodeMap = graph.getNodeMap();
        Set<String> closedNodeIds = new CopyOnWriteArraySet<>();            //存储不需要搜索的点
        ArrayList<String> loopList = new ArrayList<>();
        for (String nodeId : nodeMap.keySet()) {
            DFS1(nodeId, loopList, closedNodeIds, null);
        }
        return loops;
    }

    private void DFS1(String nodeUId, ArrayList<String> loopList, Set<String> closedNodeIds, String except) {
        loopList.add(nodeUId);
        Set<String> nodeIds = graph.getAdjacentNodeIds(nodeUId);
        nodeIds.remove(except);
        for (String nodeVId : nodeIds) {
            if (closedNodeIds.contains(nodeVId)) continue;
            ArrayList<String> loop = findLoop1(loopList, nodeVId);
            if (loop == null) {
                DFS1(nodeVId, loopList, closedNodeIds, nodeUId);
            } else {
                loops.add(loop);
            }
        }
        loopList.remove(nodeUId);
        closedNodeIds.add(nodeUId);
    }

    private ArrayList<String> findLoop1(List<String> loopList, String nodeId) {
        int k = -1;
        for (int i = 0; i < loopList.size(); i++) {
            if (loopList.get(i).equals(nodeId)) {
                k = i;
                break;
            }
        }
        if (k != -1) {
            ArrayList<String> list = new ArrayList<>();
            for (int i = k; i < loopList.size(); i++) {
                list.add(loopList.get(i));
            }
            return list;
        } else {
            return null;
        }
    }

    //找到所有的环，但计算会慢一点
    public List<List<String>> DFSTraverse2() {
        loops.clear();
        Map<String, UndirectedNode> nodeMap = graph.getNodeMap();
        Set<String> closedNodeIds = new CopyOnWriteArraySet<>();            //存储不需要搜索的点
        ArrayList<String> loopList = new ArrayList<>();
        for (String nodeId : nodeMap.keySet()) {
            closedNodeIds.clear();
            DFS2(nodeId, loopList, closedNodeIds, null);
        }
        //return loops;
        //去除重复的环
        List<ArrayList<String>> loops_with_duplicate = new ArrayList<>(loops);
        int[] loop_length = new int[loops_with_duplicate.size()];
        for (int i = 0; i < loops_with_duplicate.size(); i++) {
            loop_length[i] = loops_with_duplicate.get(i).size();
        }
        List<Integer> duplicate_loop_index = Lists.newLinkedList();
        for (int i = 0; i < loop_length.length; i++) {
            if (duplicate_loop_index.contains(i)) continue;
            for (int j = i + 1; j < loop_length.length; j++) {
                if (duplicate_loop_index.contains(j)) continue;
                if (loop_length[i] == loop_length[j]) {
                    Set<String> set = new HashSet<>(loops_with_duplicate.get(i));
                    set.removeAll(loops_with_duplicate.get(j));
                    if (set.size() <= 0) {
                        duplicate_loop_index.add(j);
                    }
                }
            }
        }
        List<List<String>> loops_without_duplicate = Lists.newLinkedList();
        for (int i = 0; i < loops_with_duplicate.size(); i++) {
            if (duplicate_loop_index.contains(i)) continue;
            else loops_without_duplicate.add(loops_with_duplicate.get(i));
        }
        return loops_without_duplicate;
    }

    private void DFS2(String nodeUId, ArrayList<String> loopList, Set<String> closedNodeIds, String except) {
        loopList.add(nodeUId);
        Set<String> nodeIds = graph.getAdjacentNodeIds(nodeUId);
        nodeIds.remove(except);
        for (String nodeVId : nodeIds) {
            if (closedNodeIds.contains(nodeVId)) continue;
            ArrayList<String> loop = findLoop2(loopList, nodeVId);
            if (loop == null) {
                if (loopList.contains(nodeVId)) {
                    continue;
                }
                DFS2(nodeVId, loopList, closedNodeIds, nodeUId);
            } else {
                loops.add(loop);
            }
        }
        //remove the follow one step can get all loops, but calculate very slow.
        //closedNodeIds.add(nodeUId);
        loopList.remove(nodeUId);
    }

    private ArrayList<String> findLoop2(List<String> loopList, String nodeId) {
        if (loopList.get(0).equals(nodeId)) {
            return new ArrayList<>(loopList);
        } else {
            return null;
        }
    }

}