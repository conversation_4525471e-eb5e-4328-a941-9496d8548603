package com.youibot.agv.scheduler.engine.service.relocation;

import com.youibot.agv.scheduler.exception.ExecuteException;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/8/21 14:57
 */
@Service
public class QrCodeRelocationApiService extends DefaultRelocationApiService {

    @Override
    public void manualRelocation(String ip, Map<String, Object> param) {
        throw new ExecuteException("qr code map not support manual relocation");
    }

    @Override
    public void autoRelocation(String ip) {
    }

    @Override
    public Map<String, Object> checkDockingPoint(String ip, Integer feature_type) {
        return null;
    }

    @Override
    public Map<String, Object> recordHomeMarker(String ip) {
        return null;
    }
}
