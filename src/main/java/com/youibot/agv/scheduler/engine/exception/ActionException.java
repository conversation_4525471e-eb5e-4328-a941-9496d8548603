package com.youibot.agv.scheduler.engine.exception;

import com.youibot.agv.scheduler.exception.YOUIFleetException;

/**
 * agv动作执行api返回fault异常类
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019年4月18日 下午3:51:11
 */
public class ActionException extends YOUIFleetException {

    public ActionException(String message, Throwable cause) {
        super(message, cause);
    }

    public ActionException(String message) {
        super(message);
    }

    public ActionException(int code, String message) {
        super(code, message);
    }

    public ActionException(int code, String message, String errorMessage) {
        super(code, message+" : " + errorMessage);
    }

    public ActionException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }
}
