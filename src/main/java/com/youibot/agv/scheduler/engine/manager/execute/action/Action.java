package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.io.IOException;
import java.util.Map;

/**
 * 动作执行器工厂抽象类
 *
 * <AUTHOR>  E-mail:s<PERSON><PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019年4月17日 下午2:52:07
 */
public interface Action {

    Map<String, Object> execute(Vehicle vehicle) throws InterruptedException, IOException;

    void pause() throws IOException, InterruptedException;

    void resume() throws IOException;

    void stop() throws IOException, InterruptedException;

    void reset() throws IOException, InterruptedException;
}
