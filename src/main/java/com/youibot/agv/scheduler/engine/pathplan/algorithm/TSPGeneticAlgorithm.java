package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Random;

import static com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil.getDistance;

/**
 * <AUTHOR>
 * @Date :Created in 下午3:18 2019/12/9
 * @Description :使用遗传算法解决TSP问题，用于物料管理系统
 * @Modified By :
 * @Version :1.0.0
 */
public class TSPGeneticAlgorithm {

    private static final Logger logger = LoggerFactory.getLogger(TSPGeneticAlgorithm.class);

    private String start;                                  //任务开始站点
    private int T = 80;                                 //运行代数
    private int M = 10;                                 //种群规模
    private double pCorss = 0.4d;                       //交叉概率
    private double pMutate = 0.2d;                      //变异概率
    private String[] stations;                          //需要排序的站点
    private Map<String, Map<String, Double>> distanceMatrix;                  //距离矩阵
    private Double bestDistance = Double.POSITIVE_INFINITY;     //最佳长度
    private String[] bestPath;                             //最佳路径
    private String[][] oldPopulation;                      //父代种群
    private String[][] newPopulation;                      //子代种群
    private int cityNum;                                //需要访问站点的数量
    private double[] Pi;                                //个体的累计概率
    private double[] fitness;                           //个体的适应度
    private Random random;                              //随机对象

    public void GAInit(String startStation, String[] stations) {
        start = startStation;
        this.stations = stations;
        for (int i = 0; i < stations.length - 1; i++) {
            for (int j = i + 1; j < stations.length; j++) {
                if (stations[i] == stations[j]) {
                    logger.error("GAInit::请检查传入的站点是否重复, startStation = [{}], stations = [{}]", startStation, stations);
                }
            }
        }
        cityNum = this.stations.length;
        bestPath = new String[cityNum];
        Pi = new double[M];
        fitness = new double[M];
        random = new Random(System.currentTimeMillis());
        newPopulation = new String[M][cityNum];
        oldPopulation = new String[M][cityNum];
        distanceMatrix = DistanceMatrixUtil.getDistanceMatrix();
    }

    /**
     *
     */
    public String[] run() {
        //初始化种群
        initGroup();
        //计算初始化种群适应度，Fitness[max]
        for (int k = 0; k < M; k++) {
            fitness[k] = evaluate(oldPopulation[k]);
        }
        //计算初始化种群中各个个体的累积概率，Pi[MAX]
        countRate();
        for (int t = 0; t < T; t++) {
            evolution();
            // 将新种群newGroup复制到旧种群oldGroup中，准备下一代进化
            for (int k = 0; k < M; k++) {
                for (int i = 0; i < cityNum; i++) {
                    oldPopulation[k][i] = newPopulation[k][i];
                }
            }
            // 计算种群适应度
            for (int k = 0; k < M; k++) {
                fitness[k] = evaluate(oldPopulation[k]);
            }
            // 计算种群中各个个体的累积概率
            countRate();
        }
        //System.out.println("最后种群...");
        //for (int k = 0; k < M; k++) {
        //	for (int i = 0; i < cityNum; i++) {
        //		System.out.printf("%-4d", oldPopulation[k][i]);
        //	}
        //	System.out.println();
        //}
        String[] path = new String[bestPath.length];
        for (int i = 0; i < bestPath.length; i++) {
            path[i] = bestPath[i];
        }
        return path;
    }

    /**
     * 初始化种群
     */
    void initGroup() {
        //种群数
        for (int k = 0; k < M; k++) {
            oldPopulation[k][0] = stations[random.nextInt(65535) % cityNum];
            //染色体长度
            for (int i = 0; i < cityNum; ) {
                oldPopulation[k][i] = stations[random.nextInt(65535) % cityNum];
                int j;
                for (j = 0; j < i; j++) {
                    if (oldPopulation[k][i] == oldPopulation[k][j]) {
                        break;
                    }
                }
                if (j == i) {
                    i++;
                }
            }
        }
    }

    /**
     * 计算某个染色体的实际距离作为染色体适应度
     */
    public double evaluate(String[] chromosome) {
        double len = 0;
        try {
            if (start != null) {
                //有起始点，将起始点距离加入适应度中
                len += getDistance(distanceMatrix, start, chromosome[0]);
                //len += distanceMatrix[chromosome[cityNum - 1]][start];
            }
            for (int i = 0; i < cityNum - 1; i++) {
                len += getDistance(distanceMatrix, chromosome[i], chromosome[i + 1]);
            }

        } catch (Exception e) {
            logger.error("计算适应度报错，请检查输入的站点是否合法，染色体:chromosome = [{}]", chromosome);
        }
        return len;
    }

    /**
     * 计算中每个个体的累积概率
     */
    void countRate() {
        //适应度总和
        double sumFitness = 0;
        for (int k = 0; k < M; k++) {
            sumFitness += fitness[k];
        }
        Pi[0] = fitness[0] / sumFitness;
        for (int k = 1; k < M; k++) {
            Pi[k] = fitness[k] / sumFitness + Pi[k - 1];
        }
    }

    /**
     * 种群进化
     */
    private void evolution() {
        selectBestChild();
        selectChild();
        for (int k = 0; k < M; k += 2) {
            //交叉概率
            double r = random.nextDouble();
            if (r < pCorss) {
                orderCrossover(k, k + 1);
            } else {
                //变异概率
                r = random.nextDouble();
                if (r < pMutate) {
                    variation(k);
                }
                //变异概率
                r = random.nextDouble();
                if (r < pMutate) {
                    variation(k + 1);
                }
            }
        }
    }

    /**
     * 挑选适应度最高的个体
     */
    private void selectBestChild() {
        double maxevaluation = fitness[0];
        int maxid = 0;
        for (int k = 1; k < M; k++) {
            if (maxevaluation > fitness[k]) {
                maxevaluation = fitness[k];
                maxid = k;
            }
        }
        if (bestDistance > maxevaluation) {
            bestDistance = maxevaluation;
            for (int i = 0; i < cityNum; i++) {
                bestPath[i] = oldPopulation[maxid][i];
            }
        }
        //将当代种群中适应度最高的染色体K复制到新种群中的第一位
        copyGh(0, maxid);
    }

    /**
     * 复制染色体
     */
    private void copyGh(int k, int kk) {
        for (int i = 0; i < cityNum; i++) {
            newPopulation[k][i] = oldPopulation[kk][i];
        }
    }

    /**
     * 轮盘赌挑选子代个体
     */
    private void selectChild() {
        //挑选概率
        double ran1;
        int selectId;
        for (int k = 1; k < M; k++) {
            ran1 = random.nextDouble();
            int i;
            for (i = 0; i < M; i++) {
                if (ran1 <= Pi[i]) {
                    break;
                }
            }
            selectId = i;
            copyGh(k, selectId);
        }
    }

    /**
     * 顺序交叉
     */
    private void orderCrossover(int k1, int k2) {
        String[] child1 = new String[cityNum];
        String[] child2 = new String[cityNum];
        for (int i = 0; i < cityNum; i++) {
            child1[i] = "";
            child2[i] = "";
        }
        int ran1 = random.nextInt(65535) % cityNum;
        int ran2 = random.nextInt(65535) % cityNum;
        while (ran1 == ran2) {
            ran2 = random.nextInt(65535) % cityNum;
        }
        if (ran1 > ran2) {
            int temp = ran1;
            ran1 = ran2;
            ran2 = temp;
        }

        for (int i = ran1; i <= ran2; i++) {
            child2[i] = newPopulation[k1][i];
            child1[i] = newPopulation[k2][i];
        }
        for (int i = 0; i < cityNum; i++) {
            if (i >= ran1 && i <= ran2) {
                continue;
            }
            for (int j = 0; j < cityNum; j++) {
                if (!hasElement(child1, newPopulation[k1][j])) {
                    child1[i] = newPopulation[k1][j];
                    break;
                }
            }
        }
        for (int i = 0; i < cityNum; i++) {
            if (i >= ran1 && i <= ran2) {
                continue;
            }
            for (int j = 0; j < cityNum; j++) {
                if (!hasElement(child2, newPopulation[k2][j])) {
                    child2[i] = newPopulation[k2][j];
                    break;
                }
            }
        }
        //将顺序交叉的结果保存到newnewPopulation中
        for (int i = 0; i < cityNum; i++) {
            newPopulation[k1][i] = child1[i];// 交叉完毕放回种群
            newPopulation[k2][i] = child2[i];// 交叉完毕放回种群
        }
    }

    private boolean hasElement(String[] a, String b) {
        for (int i = 0; i < a.length; i++) {
            if (a[i] == b) {
                return true;
            }
        }
        return false;
    }

    /**
     * 随机多次变异
     */
    private void variation(int k) {
        //变异次数
        int count = random.nextInt(65535) % cityNum;
        for (int i = 0; i < count; i++) {
            int ran1 = random.nextInt(65535) % cityNum;
            int ran2 = random.nextInt(65535) % cityNum;
            while (ran1 == ran2) {
                ran2 = random.nextInt(65535) % cityNum;
            }
            String temp = newPopulation[k][ran1];
            newPopulation[k][ran1] = newPopulation[k][ran2];
            newPopulation[k][ran2] = temp;
        }
    }
}
