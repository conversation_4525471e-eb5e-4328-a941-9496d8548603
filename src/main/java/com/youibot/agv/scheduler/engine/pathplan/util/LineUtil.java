package com.youibot.agv.scheduler.engine.pathplan.util;

import com.youibot.agv.scheduler.engine.pathplan.algorithm.MathematicalGraphicsAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Math.sqrt;

/**
 * <AUTHOR>
 * @Date :Created in 下午8:29 20-03-25
 * @Description :
 * @Modified By :
 * @Version :
 */

public class LineUtil {

    private final static Logger LOGGER = LoggerFactory.getLogger(LineUtil.class);

    public static Double calPosition(Point[] p, Point p0) {
        Double res = null;
        if (p.length != 2) {
            LOGGER.error("请输入直线的两个端点");
            return res;
        }
        Point tangentVector = p[1].minus(p[0]);
        if ((tangentVector.getX() == 0) && (tangentVector.getY() == 0)) {
            LOGGER.error("直线的两个端点不能重合::p = [{}], p0 = [{}]", p, p0);
            return res;
        }
        Point unitNormalVector = line_unit_normal_vector(tangentVector);
        double temp_a = tangentVector.getX() * unitNormalVector.getY() - unitNormalVector.getX() * tangentVector.getY();
        if (temp_a == 0) {
            return res;
        }
        Point temp_b = p[0].minus(p0);
        double temp_c = unitNormalVector.getX() * temp_b.getY() - unitNormalVector.getY() * temp_b.getX();
        res = temp_c / temp_a;
        return res;
    }

    public static Boolean isPtInPoly(Point[] p, Point p0) throws ArithmeticException {
        Double maxDerailmentDistance = AGVPropertiesUtils.getDouble("PATH_PLAN.MAX_DERAILMENT_DISTANCE");
        if (p.length != 2) {
            LOGGER.error("请输入直线的两个端点");
            return false;
        }
        Point tangentVector = p[1].minus(p[0]);
        if ((tangentVector.getX() == 0) && (tangentVector.getY() == 0)) {
            LOGGER.error("直线的两个端点不能重合::p = [{}], p0 = [{}]", p, p0);
            return false;
        }
        Point unitNormalVector = line_unit_normal_vector(tangentVector);
        Point normalVector = unitNormalVector.multiply(maxDerailmentDistance);
        Point[] points = new Point[4];
        points[0] = p[0].plus(normalVector);
        points[1] = p[1].plus(normalVector);
        points[2] = p[1].minus(normalVector);
        points[3] = p[0].minus(normalVector);
        return MathematicalGraphicsAlgorithm.isPtInPoly(p0.getX(), p0.getY(), points);
    }

    public static Double getLength(Point[] p, Double t0, Double t1) throws ArithmeticException {
        if (p.length != 2) {
            LOGGER.error("请输入直线的两个端点");
            return null;
        }
        if (t0 < 0 || t0 > 1) {
            LOGGER.error("getLength::p = [{}], t0 = [{}], t1 = [{}]",p, t0, t1);
            return null;
        }
        if (t1 < 0 || t1 > 1) {
            LOGGER.error("getLength::p = [{}], t0 = [{}], t1 = [{}]",p, t0, t1);
            return null;
        }
        return p[1].minus(p[0]).multiply(t1 - t0).getNorm();
    }

    public static Point getPoint(Point[] p, Double t) throws ArithmeticException {
        if (p.length != 2) {
            LOGGER.error("请输入直线的两个端点");
            return null;
        }
        if (t < 0 || t > 1) {
            LOGGER.error("getPoint:: t = [{}]", t);
            return null;
        }
        return p[0].multiply(1 - t).plus(p[1].multiply(t));
    }

    //直线的单位法向量
    public static Point line_unit_normal_vector(Point p) throws ArithmeticException {
        if (p.getX() == 0 && p.getY() == 0) {
            LOGGER.error("输入错误");
            return null;
        }
        if (p.getX() == 0 && p.getY() != 0) {
            return new Point(1D, 0D);
        }
        if (p.getX() != 0 && p.getY() == 0) {
            return new Point(0D, 1D);
        }
        double ey = sqrt(p.getX() * p.getX() / (p.getX() * p.getX() + p.getY() * p.getY()));
        double ex = -p.getY() * ey / p.getX();
        return new Point(ex, ey);
    }

    public static Double[] getInOutAngle(Point[] p) {
        if (p.length != 2) {
            LOGGER.error("请输入直线的两个端点");
            return null;
        }
        Double[] inOutAngle = new Double[2];
        inOutAngle[0] = inOutAngle[1] = p[1].minus(p[0]).getAngle();
        return inOutAngle;
    }

}
