package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedEdge;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DijkstraManager;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.SidePathPlanResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.*;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 下午12:09 19-11-4
 * @Description :
 * @Modified By :
 * @Version :
 */
@Service
public class PathPlanServiceImpl implements PathPlanService {

    private final static Logger logger = LoggerFactory.getLogger(PathPlanServiceImpl.class);

    @Value("${PATH_PLAN.MAX_DERAILMENT_DISTANCE}")
    private Double maxDerailmentDistance;

    @Value("${PATH_PLAN.SEND_PATH_MIN_LENGTH}")
    private Double sendPathMinLength;

    @Autowired
    private LocationService locationService;

    //private DStartLiteManager pathPlanManager = DStartLiteManager.getInstance();

    private DijkstraManager pathPlanManager = DijkstraManager.getInstance();

    //当前堵塞路径，调用完成立即释放
    //private SidePath jamSidePath;
    //
    //堵塞路径的缓存，需要任务完成时清理
    private Set<String> jamSidePaths = new CopyOnWriteArraySet<>();

    @Override
    public synchronized LinkedList<SidePath> search(String aimMarkerId, String sidePathId) throws PathPlanException {
        if (sidePathId == null || aimMarkerId == null) {
            logger.error("aimMarkerId is null");
            throw new ExecuteException(ErrorEnum.MARKER_IS_NOT_EXIST.code(), MessageUtils.getMessage("vehicle.marker_is_not_exist"));
        }
        SidePath jamSidePath = MapGraphUtil.getSidePathBySidePathId(sidePathId);
        if (jamSidePath == null) {
            logger.error("找不到对应路径");
            throw new ExecuteException(ErrorEnum.PATH_PLAN_ERROR.code(), MessageUtils.getMessage("vehicle.path_plan_exception"));
        }
        jamSidePaths.add(jamSidePath.getId());

        DirectedGraph graph = MapGraphUtil.getOriginDirectedGraph();
        DirectedEdge jamEdge = graph.getEdgeByStartEndNode(jamSidePath.getStartMarkerId(), jamSidePath.getEndMarkerId());
        //临时将该条路径禁用
        Double fixedWeight = jamEdge.getOriginWeight();
        Double autoWeight = jamEdge.getWeightAuto();
        jamEdge.modifyUserWeight(PLUS_WEIGHT, Double.POSITIVE_INFINITY);
        //log of jam side paths.
        {
            String startCode = MapGraphUtil.getMarkerByMarkerId(jamSidePath.getStartMarkerId()).getCode();
            String endCode = MapGraphUtil.getMarkerByMarkerId(jamSidePath.getEndMarkerId()).getCode();
            Marker aimMarker = MapGraphUtil.getMarkerByMarkerId(aimMarkerId);
            if (aimMarker != null) {
                logger.debug("search::aimMarkerCode = [{}], current jam side path: [{}]->[{}]", aimMarker.getCode(), startCode, endCode);
            }

            logger.debug("----------当前任务存在的阻塞路径 start----------");
            for (String id : jamSidePaths) {
                SidePath temp = MapGraphUtil.getSidePathBySidePathId(id);
                Marker tempStartMarker = MapGraphUtil.getMarkerByMarkerId(temp.getStartMarkerId());
                Marker tempEndMarker = MapGraphUtil.getMarkerByMarkerId(temp.getEndMarkerId());
                if (tempEndMarker != null && tempStartMarker != null) {
                    logger.debug("side path:[{}]->[{}]", tempStartMarker.getCode(), tempEndMarker.getCode());
                }
            }
            logger.debug("----------当前任务存在的阻塞路径 end----------");
        }

        LinkedList<SidePath> sidePaths = search(aimMarkerId);

        double multiple = 20;
        if (autoWeight.equals(0D)) {
            jamEdge.modifyAutoWeight(PLUS_WEIGHT, fixedWeight * multiple);
        } else {
            jamEdge.modifyAutoWeight(MULTIPLY_WEIGHT, multiple);
        }
        jamEdge.modifyUserWeight(RESET_WEIGHT, 0D);
        if (sidePaths != null && sidePaths.getLast() != null && !aimMarkerId.equals(sidePaths.getLast().getEndMarkerId())) {
            return null;
        }
        return sidePaths;
    }

    @Override
    public synchronized LinkedList<SidePath> search(String aimMarkerId) throws PathPlanException {
        if (aimMarkerId == null) {
            logger.error("aimMarkerId is null");
            throw new ExecuteException(ErrorEnum.MARKER_IS_NOT_EXIST.code(), MessageUtils.getMessage("vehicle.marker_is_not_exist"));
        }
        if (!MapGraphUtil.checkAimMarker(aimMarkerId)) {
            throw new ExecuteException(ErrorEnum.ACTION_MARKER_IS_NULL_OR_DISABLE.code(), MessageUtils.getMessage("vehicle.action_marker_is_null_or_disable"));
        }
        ////获取需要搜索路径的车辆
        Vehicle thisVehicle = VehicleUtils.getVehicle();
        if (thisVehicle == null || thisVehicle.getDefaultVehicleStatus() == null || thisVehicle.getDefaultVehicleStatus().getPosition() == null) {
            logger.error("vehicle 路径规划失败，缺失信息。agvMapId:, aimMarkerId:" + aimMarkerId);
            throw new ExecuteException(ErrorEnum.AGV_SYSTEM_ERROR.code(), MessageUtils.getMessage("vehicle.system_exception"));
        }
        logger.info("开始路径规划, AGVName:{}, aimMarkerId:{}", thisVehicle.getName(), aimMarkerId);
        long startTime = System.currentTimeMillis();
        ////获得当前机器人占用的位置
        VehicleLocation location = locationService.getVehicleLocation();
        if (location == null) {
            DefaultVehicleStatus.PositionStatus agvPos = thisVehicle.getDefaultVehicleStatus().getPosition();
            logger.error("AGV已脱轨, AGV = [{}], pos x:[{}],y:[{}]", thisVehicle.getDeviceNumber(), agvPos.getPos_x(), agvPos.getPos_y());
            throw new ExecuteException(ErrorEnum.ACTION_AGV_IS_OFF_TRACK.code(), ErrorEnum.ACTION_AGV_IS_OFF_TRACK.msg());
        }
        if (location.getMarker() != null && aimMarkerId.equals(location.getMarker().getId())) {
            logger.info("AGV已到达目标点");
            return null;
        }
        long tolerateTime = 500;

        //定位延时检测，重试五次
        checkLocationTimeOut(location, tolerateTime);

        //原始地图
        DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();

        MarkerPathResult markerPath = this.getPath(originDirectedGraph, location, aimMarkerId);
        logger.debug("AGVName:[{}], marker path:[{}]", thisVehicle.getName(), markerPath.toString());
        SidePathPlanResult sidePathPlanResult = linkerListPathToSidePath(markerPath);
        if (sidePathPlanResult == null) {
            return null;
        }
        LinkedList<SidePath> sidePaths = sidePathPlanResult.getSidePaths();
        if (CollectionUtils.isEmpty(sidePaths)) {
            return null;
        }
        long costTime = System.currentTimeMillis() - startTime;
        if (costTime > tolerateTime) {
            //路径规划计算超过500ms说明运行很慢
            logger.warn("路径规划计算耗时[{}]ms，计算太慢，可能以已失效", costTime);
        }

        SidePath firstSidePath = sidePaths.get(0);
        if (Math.abs(firstSidePath.getT1() - firstSidePath.getT0()) * firstSidePath.getLength() < sendPathMinLength) {
            //如果第一段路径长度小于1cm, 则直接忽略
            logger.debug("路径规划的第一段路径长度小于{}m, 忽略掉, firstSidePath:{}", sendPathMinLength, firstSidePath);
            sidePaths.remove(0);
        }

        thisVehicle.setSidePaths(new CopyOnWriteArrayList<>(sidePaths));
        if (!sidePaths.isEmpty()) {
            boolean enterElevator = sidePaths.getFirst().getNavigationType().equals(SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR) && !sidePaths.getFirst().getT0().equals(0D);
            boolean outElevator = sidePaths.getFirst().getNavigationType().equals(SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR) && !sidePaths.getFirst().getT0().equals(1D);
            if (enterElevator || outElevator) {
                logger.error(MessageUtils.getMessage("action.hazards_path"));
                throw new ExecuteException(ErrorEnum.ACTION_HAZARDS_PATH.code(), ErrorEnum.ACTION_HAZARDS_PATH.msg());
            }
        }
        logger.debug("------路径规划结果 start -------");
        for (SidePath sidePath : sidePaths) {
            String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
            String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
            logger.debug("side path:[{}]->[{}]", startCode, endCode);
        }
        logger.debug("------路径规划结果 end -------");
        return new LinkedList<>(sidePaths);
    }

    private void checkLocationTimeOut(VehicleLocation location, long tolerateTime) {
        Integer number = 5;//失败后重试次数
        Integer faultResendCount = 0;//失败时当前重新发送命令的次数
        while (faultResendCount < number) {
            long timeOut = Math.abs(System.currentTimeMillis() - location.getUpdateTimeMillis());
            if (timeOut < tolerateTime) {
                return;
            }
            //如果当前时间与位置数据更新时间超过500ms，则之间返回空数组进行下一次路径规划尝试
            logger.warn("定位数据延时为[{}]ms,请检查网络和运行环境", timeOut);
            faultResendCount++;//重试次数叠加
            location = locationService.getVehicleLocation();
            try {
                TimeUnit.MILLISECONDS.sleep(200);
            } catch (InterruptedException e) {
               logger.error("sleep error");
            }
        }
        throw new ExecuteException(ErrorEnum.LOCATION_DATA_DELAY_ERROR.code(), ErrorEnum.LOCATION_DATA_DELAY_ERROR.msg());
    }

    @Override
    public MarkerPathResult pathScope(String aimMarkerId) throws PathPlanException {
        if (!MapGraphUtil.checkAimMarker(aimMarkerId)) {
            throw new PathPlanException(ErrorEnum.ACTION_MARKER_IS_NULL_OR_DISABLE.code(), ErrorEnum.ACTION_MARKER_IS_NULL_OR_DISABLE.msg());
        }
        ////获取需要搜索路径的车辆
        Vehicle thisVehicle = VehicleUtils.getVehicle();
        if (thisVehicle == null || thisVehicle.getDefaultVehicleStatus() == null || thisVehicle.getDefaultVehicleStatus().getPosition() == null) {
            logger.error("vehicle 路径规划失败，缺失信息。agvMapId:, aimMarkerId:" + aimMarkerId);
            throw new PathPlanException(MessageUtils.getMessage("action.vehicle_is_null"));
        }
        ////获得当前机器人占用的位置
        VehicleLocation location = locationService.getVehicleLocation();
        if (location == null) {
            DefaultVehicleStatus.PositionStatus agvPos = thisVehicle.getDefaultVehicleStatus().getPosition();
            logger.error("AGV已脱轨, AGV = [{}], pos x:[{}],y:[{}]", thisVehicle.getDeviceNumber(), agvPos.getPos_x(), agvPos.getPos_y());
            throw new PathPlanException(ErrorEnum.ACTION_AGV_IS_OFF_TRACK.code(), ErrorEnum.ACTION_AGV_IS_OFF_TRACK.msg());
        }
        if (location.getMarker() != null && aimMarkerId.equals(location.getMarker().getId())) {
            return new MarkerPathResult(0.0);
        }
        long tolerateTime = 500;
        long timeOut = Math.abs(System.currentTimeMillis() - location.getUpdateTimeMillis());
        if (timeOut > tolerateTime) {
            //如果当前时间与位置数据更新时间超过500ms，则之间返回空数组进行下一次路径规划尝试
            logger.warn("定位数据延时为[{}]ms,请检查网络和运行环境", timeOut);
        }

        //原始地图
        DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();
        return this.getPath(originDirectedGraph, location, aimMarkerId);
    }

    /**
     * Get path from map date.
     *
     * @param graph       地图数据
     * @param location    车辆定位数据
     * @param aimMarkerId 目标点位ID
     * @return
     */
    public MarkerPathResult getPath(DirectedGraph graph,
                                    VehicleLocation location,
                                    String aimMarkerId) throws PathPlanException {
        //机器人在marker点上
        if (location.getMarker() != null) {
//            logger.debug("该AGV在点位上，markerCode：{}", location.getMarker().getCode());
            String startMarkerId = location.getMarker().getId();
            if (aimMarkerId.equals(startMarkerId)) {
                //当目标点和当前点一致时，返回null
                logger.debug("AGV在目标点上");
                return null;
            }
            MarkerPathResult markerPathResult = pathPlanManager.plan(graph, startMarkerId, aimMarkerId, true);
            if (!markerPathResult.isSuccess()) {
                markerPathResult = pathPlanManager.plan(graph, startMarkerId, aimMarkerId, false);
                if (!markerPathResult.isSuccess()) {
                    //目标点永远到达不了，抛出异常
                    logger.error("当前目标点[{}]永远到达不了，请检查路径是否存在", MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
                    throw new PathPlanException(ErrorEnum.PATH_PLAN_ERROR.code(), ErrorEnum.PATH_PLAN_ERROR.msg());
                } else {
                    //当原始地图能到达目标点而过滤后的地图不能到达
                    logger.debug("原始地图能到达目标点，而过滤后的地图不能到达，返回目标点附近的点路径");
                    return new MarkerPathResult(0.0);
                    //throw new PathPlanException(MessageUtils.getMessage("action.obstacle_avoidance_timeout"));
                    //throw new PathPlanException(MessageUtils.getMessage("action.missing_path"));
                }
            } else {
                //当路径存在，返回当前路径
                return markerPathResult;
            }

        } else if (location.getSidePaths() != null) {
            //机器人在SidePath路径上,保存AGV占有的所有路径中到目标点的距离和路径参数.
            List<SidePath> sidePaths = location.getSidePaths();
//            logger.debug("该AGV在路径上，占用的路径为：{}", MapGraphUtil.printSidePaths(sidePaths));
            MarkerPathResult markerPathResult = planOnSidePath(sidePaths, aimMarkerId, graph, true);
            if (!markerPathResult.isSuccess()) {
                markerPathResult = planOnSidePath(sidePaths, aimMarkerId, graph, false);
                if (!markerPathResult.isSuccess()) {
                    //error in agv map
                    logger.error("当前目标点[{}]永远到达不了，请检查路径是否存在", MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
                    throw new PathPlanException(ErrorEnum.PATH_PLAN_ERROR.code(), ErrorEnum.PATH_PLAN_ERROR.msg());
                } else {
                    //当原始地图能到达目标点而过滤后的地图不能到达
                    logger.error("原始地图能到达目标点，而过滤后的地图不能到达，返回目标点附近的点路径");
                    //throw new PathPlanException(MessageUtils.getMessage("action.missing_path"));
                    return new MarkerPathResult(0.0);
                    //throw new PathPlanException(MessageUtils.getMessage("action.obstacle_avoidance_timeout"));
                }
            } else {
                //logger.debug("markerPathResult:[{}]", markerPathResult.toString());
                return markerPathResult;
            }
        }
        logger.error("AGV既不在路径上也不在边上，请检查是否完成重定位");
        throw new PathPlanException(ErrorEnum.ACTION_AGV_IS_OFF_TRACK.code(), ErrorEnum.ACTION_AGV_IS_OFF_TRACK.msg());
    }

    private MarkerPathResult planOnSidePath(List<SidePath> sidePaths, String aimMarkerId, DirectedGraph dGraph,
                                            boolean dynamic) throws PathPlanException {
        List<MarkerPathResult> markerPathResultList = new ArrayList<>();
        for (SidePath s : sidePaths) {
            if (MapGraphUtil.containedSidePath(s)) {
                String startMarkerId = s.getEndMarkerId();
                MarkerPathResult markerPathResult = pathPlanManager.plan(dGraph, startMarkerId, aimMarkerId, dynamic);
                if (!markerPathResult.isSuccess()) {
                    continue;
                }
                markerPathResult.setSidePath(s);
                double firstAngleCost = 0;
                if (markerPathResult.getMarkerPath().size() != 0) {
                    firstAngleCost = MapGraphUtil.getAngleCost(s.getStartMarkerId(), s.getEndMarkerId(), markerPathResult.getMarkerPath().getFirst());
                }
                Double cost = MapGraphUtil.getSidePathCost(s) + firstAngleCost + markerPathResult.getCost();
                markerPathResult.setCost(cost);
                markerPathResultList.add(markerPathResult);
            }
        }
        Double min = Double.POSITIVE_INFINITY;
        MarkerPathResult mPMin = new MarkerPathResult();
        for (MarkerPathResult markerPathResult : markerPathResultList) {
            if (!markerPathResult.isSuccess()) continue;
            if (markerPathResult.getCost() < min) {
                min = markerPathResult.getCost();
                mPMin = markerPathResult;
            }
        }
        return mPMin;
    }

    //将导航点的markerPath转化为SidePath
    @Override
    public SidePathPlanResult linkerListPathToSidePath(MarkerPathResult markerPathResult) {
        if (markerPathResult == null) {
            //机器人位置与目标点位置重合
            return null;
        }
        SidePathPlanResult sidePathPlanResult = new SidePathPlanResult();
        if (markerPathResult.getMarkerPath().size() == 0 && markerPathResult.getSidePath() == null) {
            //空数组，返回空return new SidePathPlanResult();
        } else if (markerPathResult.getSidePath() == null) {
            //机器人在导航点上
            LinkedList<SidePath> sidePaths = new LinkedList<>();
            for (int i = 0; i < markerPathResult.getMarkerPath().size() - 1; i++) {
                String startMarkerId = markerPathResult.getMarkerPath().get(i);
                String endMarkerId = markerPathResult.getMarkerPath().get(i + 1);
                SidePath sidePath = MapGraphUtil.getSidePathByStartEndMarkerId(startMarkerId, endMarkerId);
                sidePath.setNavigationType(getSidePathNavigationType(startMarkerId, endMarkerId));
                sidePath.setT0(0D);
                sidePath.setT1(1D);
                sidePaths.add(sidePath);
            }
            sidePathPlanResult.setSidePaths(sidePaths);
            sidePathPlanResult.setCost(markerPathResult.getCost());
        } else if (markerPathResult.getSidePath() != null) {
            //机器人在路径上
            LinkedList<SidePath> sidePaths = new LinkedList<>();
            SidePath currentSidePath = markerPathResult.getSidePath();
            //normally current Side Path t0 have been set
            if (currentSidePath.getT0() == null) {
                logger.error("机器人在路径上的起始位置尚未计算，请检查算法错误");
            }
            currentSidePath.setT1(1D);
            currentSidePath.setNavigationType(getSidePathNavigationType(currentSidePath));
            sidePaths.add(currentSidePath);
            for (int i = 0; i < markerPathResult.getMarkerPath().size() - 1; i++) {
                String startMarkerId = markerPathResult.getMarkerPath().get(i);
                String endMarkerId = markerPathResult.getMarkerPath().get(i + 1);
                SidePath sidePath = MapGraphUtil.getSidePathByStartEndMarkerId(startMarkerId, endMarkerId);
                sidePath.setNavigationType(getSidePathNavigationType(startMarkerId, endMarkerId));
                sidePath.setT0(0D);
                sidePath.setT1(1D);
                sidePaths.add(sidePath);
            }
            sidePathPlanResult.setSidePaths(sidePaths);
            sidePathPlanResult.setCost(markerPathResult.getCost());
        }
        return sidePathPlanResult;
    }

    /**
     * 根据路径的起始点和结束点的类型设置路径任务
     *
     * @param startMarkerId
     * @param endMarkerId
     * @return
     */
    private static Integer getSidePathNavigationType(String startMarkerId, String endMarkerId) {
        Integer startMarkerNavigationType = MapGraphUtil.getMarkerNavigationType(startMarkerId);
        Integer endMarkerNavigationType = MapGraphUtil.getMarkerNavigationType(endMarkerId);
        //进入电梯
        if (MARKER_NAVIGATION_TYPE_IN_OUT.equals(startMarkerNavigationType) && MARKER_NAVIGATION_TYPE_ELEVATOR.equals(endMarkerNavigationType)) {
            return SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR;
        }
        //乘坐电梯
        if (MARKER_NAVIGATION_TYPE_ELEVATOR.equals(startMarkerNavigationType) && MARKER_NAVIGATION_TYPE_ELEVATOR.equals(endMarkerNavigationType)) {
            return SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR;
        }
        //出来电梯
        if (MARKER_NAVIGATION_TYPE_ELEVATOR.equals(startMarkerNavigationType) && MARKER_NAVIGATION_TYPE_IN_OUT.equals(endMarkerNavigationType)) {
            return SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR;
        }
        //调整点
        if (MARKER_NAVIGATION_TYPE_ADJUST.equals(startMarkerNavigationType) && MARKER_NAVIGATION_TYPE_ADJUST_DEST.equals(endMarkerNavigationType)) {
            return SIDE_PATH_NAVIGATION_TYPE_ADJUSTMENT;
        }
        return SIDE_PATH_NAVIGATION_TYPE_NORMAL;
    }

    private static Integer getSidePathNavigationType(SidePath sidePath) {
        String startMarkerId = sidePath.getStartMarkerId();
        String endMarkerId = sidePath.getEndMarkerId();
        return getSidePathNavigationType(startMarkerId, endMarkerId);
    }

    @Override
    public void removeJamSidePaths(List<SidePath> sidePaths) {
        sidePaths.forEach(sidePath -> jamSidePaths.remove(sidePath.getId()));
    }

    @Override
    public Set<String> getJamSidePaths() {
        return new HashSet<>(jamSidePaths);
    }

    @Override
    public void clearJamSidePaths() {
        if (CollectionUtils.isEmpty(jamSidePaths)) return;
        jamSidePaths.forEach(sId -> {
            SidePath s = MapGraphUtil.getSidePathBySidePathId(sId);
            if (s != null) {
                DirectedEdge edge = MapGraphUtil.getOriginDirectedGraph().getEdgeByStartEndNode(s.getStartMarkerId(), s.getEndMarkerId());
                edge.modifyAutoWeight(RESET_WEIGHT, 0D);
                edge.modifyUserWeight(RESET_WEIGHT, 0D);
            }
        });
        jamSidePaths.clear();
    }

    /**
     * 根据AGV当前位置和AGV规划的路径，计算出agv从当前位置到达路径规划中算出的路径中指定marker点时间
     *
     * @return
     */
    @Override
    public Double calculateNavigationCost(String aimMarkerId) {
        MarkerPathResult markerPathResult = this.pathScope(aimMarkerId);
        if (markerPathResult == null) {
            return null;
        }
        return markerPathResult.getCost();
    }


}