package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DStarLiteAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DStartLiteManager;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.SidePathPlanResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.StatusMessage;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.*;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 上午10:25 2020/8/15
 * @Description :
 * @Modified By :
 * @Version :
 */
@Service
public class PathPlanServiceImpl extends Thread implements PathPlanService {

    private static final Logger logger = LoggerFactory.getLogger(PathPlanServiceImpl.class);

    @Value("${PATH_PLAN.AGV_ABNORMAL_STATUS_USER_WEIGHT}")
    private Double agvAbnormalStatusUserWeight;

    @Autowired
    private FullLocationService fullLocationService;

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    private DStartLiteManager pathPlanManager = DStartLiteManager.getInstance();

    private Map<String, Boolean> positionLock = new ConcurrentHashMap<>();

    public void lockPathPlan(String agvCode) {
        if (StringUtils.isEmpty(agvCode)) return;
        positionLock.put(agvCode, false);
    }

    public void unlockPathPlan(String agvCode) {
        if (StringUtils.isEmpty(agvCode)) return;
        positionLock.put(agvCode, true);
    }

    @Override
    public void addPositionLock(String agvCode) {
        if (StringUtils.isEmpty(agvCode)) return;
        positionLock.computeIfAbsent(agvCode, k -> true);
    }

    @Override
    public Boolean getPositionLock(String agvCode) {
        if (StringUtils.isEmpty(agvCode)) return false;
        return this.positionLock.get(agvCode);
    }

    public void addRequestPathPlanMessage(PathPlanMessage pathPlanMessage) {
        if (pathPlanMessage != null && !StringUtils.isEmpty(pathPlanMessage.getAgvCode())) {
            Vehicle vehicle = defaultVehiclePool.getVehicle(pathPlanMessage.getAgvCode());
            if (vehicle != null) {
                vehicle.getPathPlanMessages().addLast(pathPlanMessage);
            }
        }
    }

    public MarkerPathResult searchMarkerToMarker(String startMarkerId, String endMarkerId) throws PathPlanException {
        if (startMarkerId == null || endMarkerId == null) {
            logger.error("marker id is null");
            throw new PathPlanException(MessageUtils.getMessage("http.missing_parameter"));
        }
        if (!MapGraphUtil.checkAimMarker(startMarkerId) || !MapGraphUtil.checkAimMarker(endMarkerId)) {
            throw new PathPlanException(MessageUtils.getMessage("action.aim_marker_is_not_exist_or_disable"));
        }
        if (startMarkerId.equals(endMarkerId)) {
            //当起始点与目标点重合，返回成功
            MarkerPathResult markerPathResult = new MarkerPathResult();
            markerPathResult.setResult(true);
            markerPathResult.setCost(0D);
            return markerPathResult;
        }
        DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();
        DStarLiteAlgorithm dStarLiteAlgorithm = new DStarLiteAlgorithm(originDirectedGraph);
        MarkerPathResult markerPathResult = dStarLiteAlgorithm.plan(startMarkerId, endMarkerId);
        //logger.debug("agvCode:[{}], markerPathResult:[{}]", agvCode, markerPathResult);
        return markerPathResult;
    }

    public MarkerPathResult search(String agvCode, String aimMarkerId) throws PathPlanException {
        if (agvCode == null || aimMarkerId == null) {
            logger.debug("agvCode:[{}],event:[路径规划],content:[路径规划参数异常,agvCode or aimPosition is null]", agvCode);
            throw new PathPlanException(MessageUtils.getMessage("http.missing_parameter"));
        }
        if (!MapGraphUtil.checkAimMarker(aimMarkerId)) {
            throw new PathPlanException(ExceptionInfoEnum.AIM_MARKED_UNREACHABLE_ERROR.getErrorCode(),ExceptionInfoEnum.AIM_MARKED_UNREACHABLE_ERROR.getMessage());
        }
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        if (vehicle == null) {
            logger.debug("agvCode:[{}],event:[路径规划],content:[can not get the vehicle from defaultVehiclePool]", agvCode);
            throw new PathPlanException(MessageUtils.getMessage("http.agv_not_login"));
        }

        //logger.info("开始路径搜索:" + vehicle.getId() + ",aimMarkerCode:" + MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
        //如果当前时间与位置数据更新时间超过500ms，则不断获取定位，直到获得时间准确的定位数据

        long tolerateTime = 500;
        VehicleLocation thisVehicleLocation = fullLocationService.getVehicleLocation(agvCode);

        if (thisVehicleLocation == null) {
            logger.debug("agvCode:[{}],event:[路径规划],content:[agv脱轨，position:{}]", agvCode,fullLocationService.getAgvPosition(agvCode));
            throw new PathPlanException(ExceptionInfoEnum.AGV_OUT_SIDE_ERROR.getErrorCode(),ExceptionInfoEnum.AGV_OUT_SIDE_ERROR.getMessage());
        }
        long timeOut = Math.abs(System.currentTimeMillis() - thisVehicleLocation.getUpdateTimeMillis());
        if (timeOut > tolerateTime) {
            logger.warn("agvCode:[{}],event:[路径规划],content:[定位数据延时为[{}]ms，请检查网络和运行环境]", agvCode,timeOut);
        }

        //DirectedGraph filterDirectedGraph = MapGraphUtil.getCloneDirectedGraph();
        DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();

        if (thisVehicleLocation.getMarker() != null) {
            String startMarkerId = thisVehicleLocation.getMarker().getId();
            MarkerPathResult markerPathResult = pathPlanManager.plan(agvCode, originDirectedGraph, startMarkerId, aimMarkerId, true);
            if (!markerPathResult.isSuccess()) {
                markerPathResult = pathPlanManager.plan(agvCode, originDirectedGraph, startMarkerId, aimMarkerId, false);
                if (!markerPathResult.isSuccess()) {
                    logger.error("agvCode:[{}],event:[路径规划],content:[目标点{}无法到达]", agvCode,MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
                    throw new PathPlanException(ExceptionInfoEnum.AIM_MARKED_UNREACHABLE_ERROR.getErrorCode(),ExceptionInfoEnum.AIM_MARKED_UNREACHABLE_ERROR.getMessage());
                }
                throw new PathPlanException(ExceptionInfoEnum.AIM_MARKED_UNREACHABLE_ERROR.getErrorCode(),ExceptionInfoEnum.AIM_MARKED_UNREACHABLE_ERROR.getMessage());
            } else {
                return markerPathResult;
            }
        } else if (thisVehicleLocation.getSidePaths() != null) {
            List<SidePath> sidePaths = thisVehicleLocation.getSidePaths();
            MarkerPathResult markerPathResult = planOnSidePath(agvCode, sidePaths, aimMarkerId, originDirectedGraph, true);
            if (!markerPathResult.isSuccess()) {
                markerPathResult = planOnSidePath(agvCode, sidePaths, aimMarkerId, originDirectedGraph, false);
                if (!markerPathResult.isSuccess()) {
                    //error in agv map
                    throw new PathPlanException(MessageUtils.getMessage("action.missing_path"));
                } else {
                    // error in mission temporary
                    throw new PathPlanException(MessageUtils.getMessage("action.missing_path"));
                }
            } else {
                //logger.debug("markerPathResult:[{}]", markerPathResult.toString());
                return markerPathResult;
            }
        }
        return new MarkerPathResult();
    }

    @Override
    public MarkerPathResult search(String agvCode, String aimMarkerId, String startMarkerId) throws PathPlanException, InterruptedException {
        if (agvCode == null || aimMarkerId == null) {
            logger.error("agvCode or aimPosition is null");
            throw new PathPlanException(MessageUtils.getMessage("http.missing_parameter"));
        }
        if (!MapGraphUtil.checkAimMarker(aimMarkerId)) {
            throw new PathPlanException(MessageUtils.getMessage("action.aim_marker_is_not_exist_or_disable"));
        }
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        if (vehicle == null) {
            logger.error("can not get the vehicle from defaultVehiclePool,agvCode = [{}]", agvCode);
            throw new PathPlanException(MessageUtils.getMessage("http.agv_not_login"));
        }
        DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();
        MarkerPathResult markerPathResult = pathPlanManager.plan(agvCode, originDirectedGraph, startMarkerId, aimMarkerId, true);
        if (!markerPathResult.isSuccess()) {
            markerPathResult = pathPlanManager.plan(agvCode, originDirectedGraph, startMarkerId, aimMarkerId, false);
            if (!markerPathResult.isSuccess()) {
                logger.error("AGV[{}]无法到达目标点[{}]", agvCode, MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
            }
            throw new PathPlanException(MessageUtils.getMessage("action.missing_path"));
        } else {
            return markerPathResult;
        }
    }

    private MarkerPathResult planOnSidePath(String agvCode, List<SidePath> sidePaths, String aimMarkerId, DirectedGraph dGraph, boolean dynamic) throws PathPlanException {
        List<MarkerPathResult> markerPathResultList = new ArrayList<>();
        for (SidePath s : sidePaths) {
            if (MapGraphUtil.containedSidePath(s)) {
                String startMarkerId = s.getEndMarkerId();
                MarkerPathResult markerPathResult = pathPlanManager.plan(agvCode, dGraph, startMarkerId, aimMarkerId, dynamic);
                if (!markerPathResult.isSuccess()) {
                    continue;
                }
                markerPathResult.setSidePath(s);
                double firstAngleCost = 0;
                if (markerPathResult.getMarkerPath().size() != 0) {
                    firstAngleCost = MapGraphUtil.getAngleCost(s.getStartMarkerId(), s.getEndMarkerId(), markerPathResult.getMarkerPath().getFirst());
                }
                Double cost = MapGraphUtil.getSidePathCost(s) + firstAngleCost + markerPathResult.getCost();
                markerPathResult.setCost(cost);
                markerPathResultList.add(markerPathResult);
            }
        }
        Double min = Double.POSITIVE_INFINITY;
        MarkerPathResult mPMin = new MarkerPathResult();
        for (MarkerPathResult markerPathResult : markerPathResultList) {
            if (!markerPathResult.isSuccess()) continue;
            if (markerPathResult.getCost() < min) {
                min = markerPathResult.getCost();
                mPMin = markerPathResult;
            }
        }
        return mPMin;
    }

    public SidePathPlanResult linkerListPathToSidePath(String agvCode, MarkerPathResult markerPathResult) {
        if (markerPathResult == null) {
            //路径规划错误，返回null
            logger.error("路径规划发生错误");
            return null;
        }
        SidePathPlanResult sidePathPlanResult = new SidePathPlanResult(agvCode);
        if (markerPathResult.getSidePath() == null) {
            //机器人在导航点上
            assert (markerPathResult.getMarkerPath() != null);
            LinkedList<SidePath> sidePaths = new LinkedList<>();
            LinkedList<String> markerPath = markerPathResult.getMarkerPath();
            for (int i = 0; i < markerPath.size() - 1; i++) {
                String startMarkerId = markerPath.get(i);
                String endMarkerId = markerPath.get(i + 1);
                SidePath sidePath = MapGraphUtil.getSidePathByStartEndMarkerId(startMarkerId, endMarkerId);
                sidePath.setNavigationType(getSidePathNavigationType(startMarkerId, endMarkerId));
                sidePath.setT0(0D);
                sidePath.setT1(1D);
                sidePaths.add(sidePath);
            }
            sidePathPlanResult.setSidePaths(sidePaths);
            sidePathPlanResult.setCost(markerPathResult.cost);
        } else if (markerPathResult.getSidePath() != null) {
            //机器人在路径上
            LinkedList<SidePath> sidePaths = new LinkedList<>();
            SidePath currentSidePath = markerPathResult.getSidePath();
            //normally current Side Path t0 have been set
            if (currentSidePath.getT0() == null || currentSidePath.getT0() < 0d) {
                logger.error("机器人在路径上的起始位置尚未计算，请检查算法错误");
            }
            currentSidePath.setT1(1D);
            currentSidePath.setNavigationType(getSidePathNavigationType(currentSidePath));
            sidePaths.add(currentSidePath);
            for (int i = 0; i < markerPathResult.getMarkerPath().size() - 1; i++) {
                String startMarkerId = markerPathResult.getMarkerPath().get(i);
                String endMarkerId = markerPathResult.getMarkerPath().get(i + 1);
                SidePath sidePath = MapGraphUtil.getSidePathByStartEndMarkerId(startMarkerId, endMarkerId);
                sidePath.setNavigationType(getSidePathNavigationType(startMarkerId, endMarkerId));
                sidePath.setT0(0D);
                sidePath.setT1(1D);
                sidePaths.add(sidePath);
            }
            sidePathPlanResult.setSidePaths(sidePaths);
            sidePathPlanResult.setCost(markerPathResult.getCost());
        }
        return sidePathPlanResult;
    }

    /**
     * 根据路径的起始点和结束点的类型设置路径任务
     *
     * @param sidePath
     * @return
     */
    public Integer getSidePathNavigationType(SidePath sidePath) {
        String startMarkerId = sidePath.getStartMarkerId();
        String endMarkerId = sidePath.getEndMarkerId();
        return getSidePathNavigationType(startMarkerId, endMarkerId);
    }

    public Integer getSidePathNavigationType(String startMarkerId, String endMarkerId) {
        Integer startMarkerNavigationType = MapGraphUtil.getMarkerNavigationType(startMarkerId);
        Integer endMarkerNavigationType = MapGraphUtil.getMarkerNavigationType(endMarkerId);
        //进入电梯
        if (MARKER_NAVIGATION_TYPE_IN_OUT.equals(startMarkerNavigationType) && MARKER_NAVIGATION_TYPE_ELEVATOR.equals(endMarkerNavigationType)) {
            return SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR;
        }
        //乘坐电梯
        if (MARKER_NAVIGATION_TYPE_ELEVATOR.equals(startMarkerNavigationType) && MARKER_NAVIGATION_TYPE_ELEVATOR.equals(endMarkerNavigationType)) {
            return SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR;
        }
        //出来电梯
        if (MARKER_NAVIGATION_TYPE_ELEVATOR.equals(startMarkerNavigationType) && MARKER_NAVIGATION_TYPE_IN_OUT.equals(endMarkerNavigationType)) {
            return SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR;
        }
        //调整点
        if (MARKER_NAVIGATION_TYPE_ADJUST.equals(startMarkerNavigationType) && MARKER_NAVIGATION_TYPE_ADJUST_DEST.equals(endMarkerNavigationType)) {
            return SIDE_PATH_NAVIGATION_TYPE_ADJUSTMENT;
        }
        return SIDE_PATH_NAVIGATION_TYPE_NORMAL;
    }

    private ConcurrentHashMap<String, Integer> agvToVehicleStatus = new ConcurrentHashMap<>();

    @Override
    public void agvStatusCallBack(StatusMessage statusMessage) {
        String agvCode = statusMessage.getAgvCode();
        String agvMapId = statusMessage.getAgvMapId();
        //DefaultVehicleStatus.BatteryStatus batteryStatus = statusMessage.getBatteryStatus();
        //DefaultVehicleStatus.SpeedStatus speedStatus = statusMessage.getSpeedStatus();
        //DefaultVehicleStatus.EmecStatus emecStatus = statusMessage.getEmecStatus();

        //Integer currentVehicleStatus = statusMessage.getAbnormalStatus();
        //Integer lastVehicleStatus = agvToVehicleStatus.getOrDefault(agvCode, -1);
        //if (!lastVehicleStatus.equals(-1)) {
        //    if (lastVehicleStatus.equals(ABNORMAL_STATUS_NO) && !currentVehicleStatus.equals(ABNORMAL_STATUS_NO)) {
        //        VehicleLocation vl = locationService.getVehicleLocation(agvCode);
        //        MapGraphUtil.modifyUserWeight(vl, PLUS_WEIGHT, agvAbnormalStatusUserWeight);
        //    }
        //    if (!lastVehicleStatus.equals(ABNORMAL_STATUS_NO) && currentVehicleStatus.equals(ABNORMAL_STATUS_NO)) {
        //        VehicleLocation vl = locationService.getVehicleLocation(agvCode);
        //        MapGraphUtil.modifyUserWeight(vl, RESET_WEIGHT, agvAbnormalStatusUserWeight);
        //    }
        //}
        //agvToVehicleStatus.put(agvCode, currentVehicleStatus);
    }

    /**
     * 根据AGV当前位置和AGV规划的路径，计算出agv从当前位置到达路径规划中算出的路径中指定marker点时间
     *
     * @return
     */
    @Override
    public Double calculateNavigationCost(String agvCode, String aimMarkerId) {
        if (StringUtils.isEmpty(agvCode) || StringUtils.isEmpty(aimMarkerId)) {
            logger.error("机器人编号或者目标点为空");
            return null;
        }
        if (!MapGraphUtil.checkAimMarker(aimMarkerId)) {
            logger.error("目标点不存在或未启用, aimMarkerId{}, ", aimMarkerId);
            return null;
        }
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        if (vehicle == null) {
            logger.error("机器人不存在, agvCode:{}", agvCode);
            return null;
        }
        VehicleLocation thisVehicleLocation = fullLocationService.getVehicleLocation(agvCode);
        if (thisVehicleLocation == null) {
            logger.error("机器人不在轨道上, agvCode:{}", agvCode);
            return null;
        }
        DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();

        if (thisVehicleLocation.getMarker() != null) {
            String startMarkerId = thisVehicleLocation.getMarker().getId();
            MarkerPathResult markerPathResult = pathPlanManager.plan(agvCode, originDirectedGraph, startMarkerId, aimMarkerId, true);
            if (!markerPathResult.isSuccess()) {
                markerPathResult = pathPlanManager.plan(agvCode, originDirectedGraph, startMarkerId, aimMarkerId, false);
                if (!markerPathResult.isSuccess()) {
                    logger.error("目标点不可达, agvCode:{}, markerCode:{}", agvCode, MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
                } else {
                    logger.error("路径规划异常, agvCode:{}, markerCode:{}", agvCode, MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
                }
                return null;
            }
            return markerPathResult.getCost();
        } else if (thisVehicleLocation.getSidePaths() != null) {
            List<SidePath> sidePaths = thisVehicleLocation.getSidePaths();
            MarkerPathResult markerPathResult = planOnSidePath(agvCode, sidePaths, aimMarkerId, originDirectedGraph, true);
            if (!markerPathResult.isSuccess()) {
                markerPathResult = planOnSidePath(agvCode, sidePaths, aimMarkerId, originDirectedGraph, false);
                if (!markerPathResult.isSuccess()) {
                    logger.error("目标点不可达, agvCode:{}, markerCode:{}", agvCode, MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
                } else {
                    logger.error("路径规划异常, agvCode:{}, markerCode:{}", agvCode, MapGraphUtil.getMarkerByMarkerId(aimMarkerId).getCode());
                }
                return null;
            }
            return markerPathResult.getCost();
        }
        return null;
    }

}
