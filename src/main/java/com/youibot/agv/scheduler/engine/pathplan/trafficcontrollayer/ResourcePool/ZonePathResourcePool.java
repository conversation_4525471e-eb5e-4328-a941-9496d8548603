package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool;

import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.SingleAreaPathResource;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.ZonePathResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:12 2019/11/11
 * @Description :
 * @Modified By :
 * @Version :
 */
public class ZonePathResourcePool<T extends ZonePathResource> extends PathResourcePool<T> {

    private static final Logger logger = LoggerFactory.getLogger(ZonePathResourcePool.class);

    private Map<String, CopyOnWriteArraySet<String>> pathResourceIdToZonePathResourceIds = new ConcurrentHashMap<>();

    public void addZonePathResources(T zonePathResource) {
        String id = zonePathResource.getId();
        Set<String> markerZone = zonePathResource.getZonePathResource();
        for (String markerId : markerZone) {
            pathResourceIdToZonePathResourceIds.computeIfAbsent(markerId, k -> new CopyOnWriteArraySet<>());
            pathResourceIdToZonePathResourceIds.get(markerId).add(id);
        }
        super.attach(zonePathResource);
    }

    public void removeZonePathResources(String zonePathResourceId) {
        super.detach(zonePathResourceId);
        pathResourceIdToZonePathResourceIds.entrySet().removeIf(item -> item.getValue().equals(zonePathResourceId));
    }

    public void clearZonePathResources() {
        super.getPoolEntries().clear();
        pathResourceIdToZonePathResourceIds.clear();
    }

    public Set<String> queryZonePathResourceIdsByPathResourcesIds(LinkedList<String> markerIds) {
        if (markerIds == null) return new HashSet<>();
        Set<String> zonePathResourceIds = new HashSet<>();
        for (String markerId : markerIds) {
            CopyOnWriteArraySet<String> zonePathResourceId = pathResourceIdToZonePathResourceIds.get(markerId);
            if (zonePathResourceId != null) {
                zonePathResourceIds.addAll(zonePathResourceId);
            }
        }
        return zonePathResourceIds;
    }

    public HashSet<String> queryZonePathResourceIdByPathResourcesId(String markerId) {
        if (markerId == null) return new HashSet<>();
        CopyOnWriteArraySet<String> zonePathResourceIds = pathResourceIdToZonePathResourceIds.get(markerId);
        if (zonePathResourceIds != null) {
            return new HashSet<>(zonePathResourceIds);
        } else {
            return new HashSet<>();
        }
    }

    public Set<String> queryPathResourceIdsByZonePathResourceId(String zonePathResourceId){
        T t = this.get(zonePathResourceId);
        if (t != null) {
            return new HashSet<>(t.getZonePathResource());
        } else {
            return new HashSet<>();
        }
    }
}
