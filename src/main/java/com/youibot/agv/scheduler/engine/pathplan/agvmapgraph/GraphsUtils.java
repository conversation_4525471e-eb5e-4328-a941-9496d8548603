package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date :Created in 下午9:53 2020/10/18
 * @Description :
 * @Modified By :
 * @Version :
 */
public class GraphsUtils {

    public static UndirectedGraph convertGraph(DirectedGraph directedGraph) {
        UndirectedGraph undirectedGraph = new UndirectedGraph();
        Collection<DirectedNode> directedNodes = directedGraph.getNodeMap().values();
        for (DirectedNode directedNode : directedNodes) {
            UndirectedNode undirectedNode = new UndirectedNode(directedNode);
            undirectedGraph.addNode(undirectedNode);
        }
        Set<DirectedEdge> directedEdges = directedGraph.getAllEdges();
        for (DirectedEdge directedEdge : directedEdges) {
            UndirectedEdge undirectedEdge = new UndirectedEdge(directedEdge);
            undirectedGraph.addEdge(undirectedEdge);
        }
        return undirectedGraph;
    }

}
