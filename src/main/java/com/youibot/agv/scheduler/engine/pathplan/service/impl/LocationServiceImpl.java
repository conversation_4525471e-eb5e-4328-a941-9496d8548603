package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.entity.MapResourceCache;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.BezierUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.KDTreeManager;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.LineUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.youibot.agv.scheduler.constant.MapConstant.*;

/**
 * @version V1.0
 * @Title:
 * @ClassName: com.youibot.agv.scheduler.engine.pathplan.service.impl
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/11/7 19:58
 */
@Service
public class LocationServiceImpl implements LocationService {

    private static final Logger logger = LoggerFactory.getLogger(LocationServiceImpl.class);


    @Value("${PATH_PLAN.MAX_DERAILMENT_DISTANCE}")
    private Double maxDerailmentDistance;

    public synchronized VehicleLocation getLocationSidePath(String agvMapId, double x, double y, long update_time_millis) {
        try {
            Point point = new Point(x, y);
            Set<Marker> markers = MapResourceCache.getMarkersByAGVMapId(agvMapId);
            if (markers == null) {
                //当数据库中不存在marker点时直接返回null
                return null;
            }

            //优先判断机器人在二维码点上
            //1. first method
            //for (Marker marker : markers) {
            //    double distance = new Point(marker.getX() - x, marker.getY() - y).getNorm();
            //    if (distance < maxDerailmentDistance) {
            //        VehicleLocation vehicleLocation = new VehicleLocation();
            //        vehicleLocation.setMarker(marker);
            //        vehicleLocation.setSidePaths(null);
            //        vehicleLocation.setUpdateTimeMillis(update_time_millis);
            //        return vehicleLocation;
            //    }
            //}
            //2. second method
            String markerId = this.queryRecent(agvMapId, x, y);
            if (markerId != null) {
                VehicleLocation vehicleLocation = new VehicleLocation();
                vehicleLocation.setMarker(MapResourceCache.getMarker(markerId));
                vehicleLocation.setSidePaths(null);
                vehicleLocation.setUpdateTimeMillis(update_time_millis);
                return vehicleLocation;
            }

            /*最后用机器人的x,y计算所在的边*/
            Set<SidePath> sidePaths = MapResourceCache.getSidePathsByAGVMapId(agvMapId);
            if (sidePaths == null) {
                //当数据库中不存在路径时直接返回null
                return null;
            }
            VehicleLocation vehicleLocation = new VehicleLocation();
            List<SidePath> tempSidePath = new ArrayList<>();
            for (SidePath sidePath : sidePaths) {
                Point[] points = new Point[4];
                Marker startMarker = MapResourceCache.getMarker(sidePath.getStartMarkerId());
                Marker endMarker = MapResourceCache.getMarker(sidePath.getEndMarkerId());
                points[0] = new Point(startMarker.getX(), startMarker.getY());
                points[3] = new Point(endMarker.getX(), endMarker.getY());
                boolean isPtInPoly = false;
                //用side path的t0存储agv当前的位置
                double t0 = -1;
                switch (sidePath.getLineType()) {
                    case PATH_LINE_TYPE_CURVE: {
                        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                        isPtInPoly = BezierUtil.isPtInPoly(points, point);
                        if (isPtInPoly) {
                            t0 = BezierUtil.calPosition(points, point);
                        }
                        break;
                    }
                    case PATH_LINE_TYPE_STRAIGHT: {
                        Point[] line = new Point[2];
                        line[0] = points[0];
                        line[1] = points[3];
                        isPtInPoly = LineUtil.isPtInPoly(line, point);
                        if (isPtInPoly) {
                            t0 = LineUtil.calPosition(line, point);
                        }
                        break;
                    }
                }

                if (isPtInPoly) {
                    SidePath temp = sidePath.clone();
                    temp.setT0(t0);
                    tempSidePath.add(temp);
                }
            }
            //在side path上设置路径，并将占据的导航点置空
            if (!tempSidePath.isEmpty()) {
                vehicleLocation.setSidePaths(tempSidePath);
                vehicleLocation.setMarker(null);
                vehicleLocation.setUpdateTimeMillis(update_time_millis);
                return vehicleLocation;
            }
        } catch (Exception e) {
            logger.error("Exception:", e);
        }
        return null;
    }

    public synchronized SidePath getSidePath(String agvMapId, Double x, Double y, List<SidePath> vehicleSidePaths) {
        if (x == null || y == null || vehicleSidePaths == null) {
            return null;
        }
        SidePath sidePath = null;
        //遍历AGV的路径计算其所在的路径
        for (SidePath thisSidePath : vehicleSidePaths) {
            Marker startMarker = MapResourceCache.getMarker(thisSidePath.getStartMarkerId());
            Marker endMarker = MapResourceCache.getMarker(thisSidePath.getEndMarkerId());
            Point[] points = new Point[4];
            points[0] = new Point(startMarker.getX(), startMarker.getY());
            points[3] = new Point(endMarker.getX(), endMarker.getY());
            Point p0 = new Point(x, y);
            if (new Point(startMarker.getX() - x, startMarker.getY() - y).getNorm() < maxDerailmentDistance) {
                sidePath = thisSidePath;
                sidePath.setT0(0D);
                break;
            } else if (new Point(endMarker.getX() - x, endMarker.getY() - y).getNorm() < maxDerailmentDistance) {
                sidePath = thisSidePath;
                sidePath.setT0(1D);
                break;
            } else {
                switch (thisSidePath.getLineType()) {
                    case PATH_LINE_TYPE_STRAIGHT: {
                        Point[] line = new Point[2];
                        line[0] = points[0];
                        line[1] = points[3];
                        if (LineUtil.isPtInPoly(line, p0)) {
                            sidePath = thisSidePath;
                            sidePath.setT0(LineUtil.calPosition(line, p0));
                        }
                        break;
                    }
                    case PATH_LINE_TYPE_CURVE: {
                        points[1] = JSON.parseObject(thisSidePath.getStartControl(), Point.class);
                        points[2] = JSON.parseObject(thisSidePath.getEndControl(), Point.class);
                        if (BezierUtil.isPtInPoly(points, p0)) {
                            sidePath = thisSidePath;
                            sidePath.setT0(BezierUtil.calPosition(points, p0));
                        }
                        break;
                    }
                }
            }
            if (sidePath != null) {
                break;
            }
        }
        return sidePath;
    }

    public synchronized SidePath getSidePath(String segmentId, Double t, List<SidePath> vehicleSidePaths) {
        SidePath sidePath = null;
        if (!StringUtils.isEmpty(segmentId) && !segmentId.equals("-1") && containsSegmentId(vehicleSidePaths, segmentId)) {
            //通过反馈的路径信息计算agv所在的路径
            sidePath = MapResourceCache.getSidePath(segmentId);
            sidePath.setT0(t);
        }
        return sidePath;
    }

    public synchronized LinkedList<SidePath> getRemainingSidePaths(String agvMapId, Double x, Double y, List<SidePath> sidePaths) {
        if (x == null || y == null || sidePaths == null) {
            return null;
        }
        LinkedList<SidePath> remainingSidePaths = new LinkedList<>(sidePaths);
        SidePath currentSidePath = getSidePath(agvMapId, x, y, sidePaths);
        if (currentSidePath == null) {
            return null;
        }
        String segmentId = currentSidePath.getId();
        for (Iterator<SidePath> rIterator = remainingSidePaths.iterator(); rIterator.hasNext(); ) {
            SidePath thisSidePath = rIterator.next();
            if (!segmentId.equals(thisSidePath.getId())) {
                rIterator.remove();
            } else {
                break;
            }
        }
        return remainingSidePaths;
    }

    public synchronized LinkedList<SidePath> getRemainingSidePaths(String agvMapId, String segmentId, Double t, List<SidePath> sidePaths) {
        if (segmentId == null || t == null || sidePaths == null) {
            return null;
        }
        LinkedList<SidePath> remainingSidePaths = new LinkedList<>(sidePaths);
        for (Iterator<SidePath> rIterator = remainingSidePaths.iterator(); rIterator.hasNext(); ) {
            SidePath thisSidePath = rIterator.next();
            if (!segmentId.equals(thisSidePath.getId())) {
                rIterator.remove();
            } else {
                break;
            }
        }
        return remainingSidePaths;
    }

    private synchronized boolean containsSegmentId(List<SidePath> sidePaths, String segment_id) {
        boolean containFlag = false;
        for (SidePath sidePath : sidePaths) {
            containFlag |= sidePath.getId().equals(segment_id);
        }
        return containFlag;
    }

    /**
     * 判断速度是否为零
     *
     * @param vehicle
     * @return
     */
    public synchronized Boolean speedIsZero(Vehicle vehicle) {
        Double vx = vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_vx();
        Double w = vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_w();
        Double r_vx = vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_r_vx();
        Double r_w = vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_r_w();
        return isZero(vx) || isZero(w) || isZero(r_vx) || isZero(r_w);
    }

    public synchronized Marker getCurrentMarker(String agvMapId, Double x, Double y) {

        String markerId = queryRecent(agvMapId, x, y);
        if (markerId != null) {
            return MapResourceCache.getMarker(markerId);
        }
        return null;
    }

    public synchronized Boolean isAimMarkerReached(String agvMapId, Double x, Double y, String aimMarkerId) {
        if (agvMapId == null || x == null || y == null || aimMarkerId == null) return false;
        Marker marker = MapResourceCache.getMarker(aimMarkerId);
        if (marker == null || marker.getAgvMapName() == null) {
            return false;
        }
        if (!marker.getAgvMapName().equals(agvMapId)) {
            return false;
        } else {
            Double dis = new Point(marker.getX(), marker.getY()).minus(new Point(x, y)).getNorm();
            return dis < maxDerailmentDistance;
        }
    }

    private static KDTreeManager kdTreeManager = KDTreeManager.getInstance();

    public void buildKDTree(String agvMapId, Collection<Marker> markers) {
        if (CollectionUtils.isEmpty(markers)) {
            return;
        }
        try {
            kdTreeManager.buildKDTree(agvMapId, markers);
        } catch (Exception e) {
            logger.error("[{}]", e);
        }
    }

    public void removeKDTree(String agvMapId) {
        if (agvMapId == null) return;
        kdTreeManager.removeKDTree(agvMapId);
    }

    public String queryRecent(String agvMapId, Double x, Double y) {
        String markerId = kdTreeManager.query(agvMapId, x, y);
        if (markerId != null) {
            Marker marker = MapResourceCache.getMarker(markerId);
            if (marker != null) {
                if (new Point(marker.getX() - x, marker.getY() - y).getNorm() < maxDerailmentDistance) {
                    return markerId;
                }
            }
        }
        return null;
    }

    //判断一个Double是否为零
    private static Boolean isZero(Double d) {
        //判断是否停止阈值
        return Math.abs(d) <= 0.01;
    }
}
