package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.util.BezierUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.KDTreeManager;
import com.youibot.agv.scheduler.engine.pathplan.util.LineUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.manager.socket.DataProtocol;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.engine.util.AGVResultInfoUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.util.ActionUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.youibot.agv.scheduler.constant.MapConstant.MARKER_TYPE_WORK;
import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_CURVE;
import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_STRAIGHT;
import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.ACTION_EXECUTE_RUNNING;

/**
 * @version V1.0
 * @Title:
 * @ClassName: com.youibot.agv.scheduler.engine.pathplan.service.impl
 * @Description:
 * @Copyright 2019 - Powered By YOIUIBOT-SYSTEM
 * @author: 宋巨东 E-mail:<EMAIL>
 * @date: 2019/11/7 19:58
 */
@Service
public class LocationServiceImpl implements LocationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocationServiceImpl.class);

    private Map<String, AGVSocketClient> ipToAgvSocketClient;

    private List<String> agvMoveApiCode;

    private KDTreeManager kdTreeManager = KDTreeManager.getInstance();

    @Value("${PATH_PLAN.MAX_DERAILMENT_DISTANCE}")
    private Double maxDerailmentDistance;

    @Value("${PATH_PLAN.MAX_DERAILMENT_DISTANCE_AT_WORK_MARKER}")
    private Double workMarkerMaxDerailmentDistance;

    @Value("${AGV_API_CODE.MOVE_DOCKING}")
    private String moveDockingApiCode;

    @Value("${AGV_API_CODE.MOVE_FREE_NAVIGATION")
    private String moveFreeNavigationApiCode;

    @Value("${AGV_API_CODE.MOVE_SIDE_PATH}")
    private String moveSidePathApiCode;

    @Value("${AGV_API_CODE.MOVE_RELATIVE}")
    private String moveRelativeApiCode;

    @Value("${AGV_API_CODE.MOVE_ROTATE}")
    private String moveRotateApiCode;

    public LocationServiceImpl() {
        ipToAgvSocketClient = new ConcurrentHashMap<>();
        agvMoveApiCode = new ArrayList<>();
        agvMoveApiCode.add(moveDockingApiCode);
        agvMoveApiCode.add(moveFreeNavigationApiCode);
        agvMoveApiCode.add(moveSidePathApiCode);
        agvMoveApiCode.add(moveRelativeApiCode);
        agvMoveApiCode.add(moveRotateApiCode);
    }

    @Override
    public void buildKDTree(String agvMapId, Collection<Marker> markers) {
        if (markers.size() <= 0) {
            return;
        }
        try {
            kdTreeManager.buildKDTree(agvMapId, markers);
        } catch (Exception e) {
            LOGGER.error("[{}]", e);
        }
    }

    @Override
    public void removeKDTree(String agvMapId) {
        if (agvMapId == null) return;
        kdTreeManager.removeKDTree(agvMapId);
    }

    @Override
    public String queryRecent(String agvMapId, Double x, Double y) {
        String markerId = kdTreeManager.query(agvMapId, x, y);
        if (markerId != null) {
            Marker marker = MapGraphUtil.getMarkerByMarkerId(markerId);
            if (marker != null) {
                Double norm = new Point(marker.getX() - x, marker.getY() - y).getNorm();
                //判断marker点类型是否为工作点，如果最近的点为工作点则使用工作点的脱轨距离判断
                if (MARKER_TYPE_WORK.equals(marker.getType()) && norm < workMarkerMaxDerailmentDistance) {
                    return markerId;
                    //非工作点位用，非工作点最大脱轨距离判断
                } else if (!MARKER_TYPE_WORK.equals(marker.getType()) && norm < maxDerailmentDistance) {
                    return markerId;
                }
            }
        }
        return null;
    }

    @Override
    public VehicleLocation getVehicleLocation() {
        Vehicle vehicle = VehicleUtils.getVehicle();
        if (vehicle != null && vehicle.getDefaultVehicleStatus() != null && vehicle.getDefaultVehicleStatus().getPosition() != null) {
            DefaultVehicleStatus.PositionStatus agvPos = vehicle.getDefaultVehicleStatus().getPosition();
            return getVehicleLocation(vehicle.getAGVMapId(), agvPos.getPos_x(), agvPos.getPos_y(), agvPos.getUpdate_time_millis());
        }
        return null;
    }

    @Override
    public VehicleLocation getVehicleLocation(String agvMapId, double x, double y, long update_time_millis) {
        try {
            //LOGGER.debug("position x:[{}],y:[{}]", x, y);
            Point point = new Point(x, y);
            List<Marker> markers = MapGraphUtil.getMarkersByAGVMapId(agvMapId);
            if (markers == null) {
                //当数据库中不存在marker点时直接返回null
                return null;
            }
            //优先判断机器人在二维码点上
            //1. first method
            //for (Marker marker : markers) {
            //    if (!marker.getAgvMapId().equals(agvMapId)) continue;
            //    double distance = new Point(marker.getX() - x, marker.getY() - y).getNorm();
            //    if (distance < maxDerailmentDistance) {
            //        VehicleLocation vehicleLocation = new VehicleLocation();
            //        vehicleLocation.setMarker(marker);
            //        vehicleLocation.setSidePaths(null);
            //        vehicleLocation.setUpdateTimeMillis(update_time_millis);
            //        return vehicleLocation;
            //    }
            //}
            //2. second method
            //搜索最近的marker点
            String markerId = this.queryRecent(agvMapId, x, y);
            if (markerId != null) {
                VehicleLocation vehicleLocation = new VehicleLocation();
                vehicleLocation.setMarker(MapGraphUtil.getMarkerByMarkerId(markerId));
                vehicleLocation.setSidePaths(null);
                vehicleLocation.setUpdateTimeMillis(update_time_millis);
                return vehicleLocation;
            }

            /*最后用机器人的x,y计算所在的边*/
            List<SidePath> sidePaths = MapGraphUtil.getSidePathsByAGVMapId(agvMapId);
            if (sidePaths == null) {
                //当数据库中不存在路径时直接返回null
                return null;
            }
            VehicleLocation vehicleLocation = new VehicleLocation();
            List<SidePath> tempSidePath = new ArrayList<>();
            for (SidePath sidePath : sidePaths) {
                Point[] points = new Point[4];
                Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
                Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());

                if(startMarker==null || endMarker==null){
                    LOGGER.error("-------------------小车在路径上定位时，路径数据异常--------------------------");
                    LOGGER.error("当前路径数据有误，无法找到点位：[{}]",JSON.toJSONString(sidePath));
                    LOGGER.error("-------------------小车在路径上定位时，路径数据异常--------------------------");
                    continue;
                }

                points[0] = new Point(startMarker.getX(), startMarker.getY());
                points[3] = new Point(endMarker.getX(), endMarker.getY());
                boolean isPtInPoly = false;
                //用side path的t0存储agv当前的位置
                double t0 = -1;
                if (sidePath.getLineType() == null) continue;
                switch (sidePath.getLineType()) {
                    case PATH_LINE_TYPE_CURVE: {
                        if (sidePath.getStartControl() == null || sidePath.getEndControl() == null) continue;
                        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                        isPtInPoly = BezierUtil.isPtInPoly(points, point);
                        if (isPtInPoly) {
                            t0 = BezierUtil.calPosition(points, point);
                        }
                        break;
                    }
                    case PATH_LINE_TYPE_STRAIGHT: {
                        Point[] line = new Point[2];
                        line[0] = points[0];
                        line[1] = points[3];
                        isPtInPoly = LineUtil.isPtInPoly(line, point);
                        if (isPtInPoly) {
                            t0 = LineUtil.calPosition(line, point);
                        }
                        break;
                    }
                }

                if (isPtInPoly) {
                    SidePath temp = sidePath.clone();
                    temp.setT0(t0);
                    tempSidePath.add(temp);
                }
            }
            //在side path上设置路径，并将占据的导航点置空
            if (!tempSidePath.isEmpty()) {
                vehicleLocation.setSidePaths(tempSidePath);
                vehicleLocation.setMarker(null);
                vehicleLocation.setUpdateTimeMillis(update_time_millis);
                return vehicleLocation;
            }
        } catch (Exception e) {
            LOGGER.error("Exception:", e);
        }
        return null;
    }

    @Override
    public SidePath getSidePath(String agvMapId, Double x, Double y, List<SidePath> vehicleSidePaths) {
        if (x == null || y == null || vehicleSidePaths == null) {
            return null;
        }
        SidePath sidePath = null;
        //遍历AGV的路径计算其所在的路径
        for (SidePath thisSidePath : vehicleSidePaths) {
            Marker startMarker = MapGraphUtil.getMarkerByMarkerId(thisSidePath.getStartMarkerId());
            Marker endMarker = MapGraphUtil.getMarkerByMarkerId(thisSidePath.getEndMarkerId());
            Point[] points = new Point[4];
            points[0] = new Point(startMarker.getX(), startMarker.getY());
            points[3] = new Point(endMarker.getX(), endMarker.getY());
            Point p0 = new Point(x, y);
            if (new Point(startMarker.getX() - x, startMarker.getY() - y).getNorm() < maxDerailmentDistance) {
                sidePath = thisSidePath;
                sidePath.setT0(0D);
                break;
            } else if (new Point(endMarker.getX() - x, endMarker.getY() - y).getNorm() < maxDerailmentDistance) {
                sidePath = thisSidePath;
                sidePath.setT0(1D);
                break;
            } else {
                switch (thisSidePath.getLineType()) {
                    case PATH_LINE_TYPE_STRAIGHT: {
                        Point[] line = new Point[2];
                        line[0] = points[0];
                        line[1] = points[3];
                        if (LineUtil.isPtInPoly(line, p0)) {
                            sidePath = thisSidePath;
                            sidePath.setT0(LineUtil.calPosition(line, p0));
                        }
                        break;
                    }
                    case PATH_LINE_TYPE_CURVE: {
                        points[1] = JSON.parseObject(thisSidePath.getStartControl(), Point.class);
                        points[2] = JSON.parseObject(thisSidePath.getEndControl(), Point.class);
                        if (BezierUtil.isPtInPoly(points, p0)) {
                            sidePath = thisSidePath;
                            sidePath.setT0(BezierUtil.calPosition(points, p0));
                        }
                        break;
                    }
                }
            }
            if (sidePath != null) {
                break;
            }
        }
        return sidePath;
    }

    @Override
    public SidePath getSidePath(String segmentId, Double t, List<SidePath> vehicleSidePaths) {
        SidePath sidePath = null;
        if (!StringUtils.isEmpty(segmentId) && !segmentId.equals("-1") && containsSegmentId(vehicleSidePaths, segmentId)) {
            //通过反馈的路径信息计算agv所在的路径
            sidePath = MapGraphUtil.getSidePathBySidePathId(segmentId);
            sidePath.setT0(t);
        }
        return sidePath;
    }

    @Override
    public LinkedList<SidePath> getRemainingSidePaths(String agvMapId, Double x, Double y, List<SidePath> sidePaths) {
        if (x == null || y == null || sidePaths == null) {
            return null;
        }
        LinkedList<SidePath> remainingSidePaths = new LinkedList<>(sidePaths);
        SidePath currentSidePath = getSidePath(agvMapId, x, y, sidePaths);
        if (currentSidePath == null) {
            return null;
        }
        String segmentId = currentSidePath.getId();
        for (Iterator<SidePath> rIterator = remainingSidePaths.iterator(); rIterator.hasNext(); ) {
            SidePath thisSidePath = rIterator.next();
            if (!segmentId.equals(thisSidePath.getId())) {
                rIterator.remove();
            } else {
                break;
            }
        }
        return remainingSidePaths;
    }

    @Override
    public LinkedList<SidePath> getRemainingSidePaths(String agvMapId, String segmentId, Double t, List<SidePath> sidePaths) {
        if (segmentId == null || t == null || sidePaths == null) {
            return null;
        }
        LinkedList<SidePath> remainingSidePaths = new LinkedList<>(sidePaths);
        for (Iterator<SidePath> rIterator = remainingSidePaths.iterator(); rIterator.hasNext(); ) {
            SidePath thisSidePath = rIterator.next();
            if (!segmentId.equals(thisSidePath.getId())) {
                rIterator.remove();
            } else {
                break;
            }
        }
        return remainingSidePaths;
    }

    private boolean containsSegmentId(List<SidePath> sidePaths, String segment_id) {
        boolean containFlag = false;
        for (SidePath sidePath : sidePaths) {
            containFlag |= sidePath.getId().equals(segment_id);
        }
        return containFlag;
    }

    /**
     * 正在执行移动任务也判断为移动
     * true 移动 false 静止
     *
     * @param vehicle
     * @return
     */
    @Override
    public Boolean isMoving(Vehicle vehicle) {
        DefaultVehicleStatus defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
        if (defaultVehicleStatus == null) {
            return false;
        }
        Double vx = defaultVehicleStatus.getSpeed().getSpeed_vx();
        Double w = defaultVehicleStatus.getSpeed().getSpeed_w();
        Double r_vx = defaultVehicleStatus.getSpeed().getSpeed_r_vx();
        Double r_w = defaultVehicleStatus.getSpeed().getSpeed_r_w();

        if (!isZero(vx) || !isZero(w) || !isZero(r_vx) || !isZero(r_w)) {
            return true;
        }
        try {
            String ip = vehicle.getIp();
            if (!ipToAgvSocketClient.keySet().contains(ip)) {
                AGVSocketClient agvSocketClient = AGVSocketClient.createAGVClient(ip, ActionUtils.getStatusPort());
                ipToAgvSocketClient.put(ip, agvSocketClient);
            }
            AGVSocketClient client = ipToAgvSocketClient.get(ip);
            DataProtocol protocolOperation = new DataProtocol(AGVPropertiesUtils.getString("AGV_API_CODE.ACTION_STATUS"), null);
            DataProtocol receiveData = client.sendAndReceive(protocolOperation);
            Map<String, Object> dataMap = AGVResultInfoUtils.getDataMap(receiveData.getData());
            Integer status = (Integer) dataMap.get("status");
            String code = (String) (dataMap.get("code"));
            return ACTION_EXECUTE_RUNNING.equals(status) && agvMoveApiCode.contains(code);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            ipToAgvSocketClient.remove(vehicle.getIp());
        }
        return false;
    }

    /**
     * 判断速度是否为零
     *
     * @param vehicle
     * @return
     */
    @Override
    public Boolean speedIsZero(Vehicle vehicle) {
        Double vx = vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_vx();
        Double w = vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_w();
        Double r_vx = vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_r_vx();
        Double r_w = vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_r_w();
        return isZero(vx) || isZero(w) || isZero(r_vx) || isZero(r_w);
    }

    @Override
    public Marker getCurrentMarker(String agvMapId, Double x, Double y) {
        List<Marker> markers = MapGraphUtil.getMarkersByAGVMapId(agvMapId);
        if (markers == null) {
            return null;
        }

        for (Marker marker : markers) {
            double distance = new Point(marker.getX() - x, marker.getY() - y).getNorm();
            if (distance < maxDerailmentDistance) {
                return marker;
            }
        }
        return null;
    }

    @Override
    public Boolean isAimMarkerReached(String agvMapId, Double x, Double y, String aimMarkerId) {
        Marker marker = MapGraphUtil.getMarkerByMarkerId(aimMarkerId);
        if (!marker.getAgvMapName().equals(agvMapId)) {
            return false;
        } else {
            Double dis = new Point(marker.getX(), marker.getY()).minus(new Point(x, y)).getNorm();
            if (MARKER_TYPE_WORK.equals(marker.getType())) {
                return dis < workMarkerMaxDerailmentDistance;
                //非工作点位用，非工作点最大脱轨距离判断
            } else {
                return dis < maxDerailmentDistance;
            }
        }
    }

    //判断一个Double是否为零
    private static Boolean isZero(Double d) {
        //判断是否停止阈值
        return Math.abs(d) <= 0.01;
    }
}
