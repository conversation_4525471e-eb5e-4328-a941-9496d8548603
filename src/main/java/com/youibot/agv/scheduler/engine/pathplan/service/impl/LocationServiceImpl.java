package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youibot.agv.scheduler.constant.MapConstant;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedEdge;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.KDTreeManager;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.engine.pathplan.entity.Position;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.SingleAreaPathResourcePool;
import com.youibot.agv.scheduler.engine.pathplan.util.BezierUtil;
import com.youibot.agv.scheduler.engine.pathplan.util.LineUtil;
import com.youibot.agv.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.agv.scheduler.engine.pathplan.util.SidePathUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.ParkScheduler;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.PositionMessage;
import com.youibot.agv.scheduler.service.ChargeSchedulerService;
import com.youibot.agv.scheduler.service.ParkSchedulerService;

import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_CURVE;
import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_STRAIGHT;
import static com.youibot.agv.scheduler.constant.PathPlanConstant.MINUS_WEIGHT;
import static com.youibot.agv.scheduler.constant.PathPlanConstant.PLUS_WEIGHT;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 18:43 2020/8/7
 * @Description :
 * @Modified By :
 * @Version :
 */
@Service
public class LocationServiceImpl implements LocationService {

    private static final Logger logger = LoggerFactory.getLogger(LocationServiceImpl.class);

    @Value("${PATH_PLAN.NON_WORK_POINT_MAX_DERAILMENT_DISTANCE}")
    private Double nonWorkPointMaxDerailmentDistance;

    @Value("${PATH_PLAN.WORK_POINT_MAX_DERAILMENT_DISTANCE}")
    private Double workPointMaxDerailmentDistance;

    @Value("${PATH_PLAN.LOCATION_PLUS_AUTO_WEIGHT}")
    private Double locationPlusAutoWeight;

    @Value("${PATH_PLAN.SINGLE_AREA_PLUS_AUTO_WEIGHT}")
    private Double singleAreaPlusAutoWeight;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    @Autowired
    private PathPlanService pathPlanService;

    @Autowired
    private SingleAreaPathResourcePool singleAreaPathResourcePool;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;
    @Autowired
    private ParkSchedulerService parkSchedulerService;

    private PositionAndLocation positionAndLocation = new PositionAndLocation();

    private static class PositionAndLocation {
        Map<String, Position> agvToPosition = new HashMap<>();
        Map<String, VehicleLocation> agvToVehicleLocation = new HashMap<>();
        private Map<String, ReadWriteLock> positionReadWriteLock = new ConcurrentHashMap<>();
        private Map<String, ReadWriteLock> locationReadWriteLock = new ConcurrentHashMap<>();

        public Position getPosition(String agvCode) {
            positionReadWriteLock.computeIfAbsent(agvCode, k -> new ReentrantReadWriteLock());
            positionReadWriteLock.get(agvCode).readLock().lock();
            try {
                return agvToPosition.get(agvCode);
            } catch (Exception e) {
                logger.error("get position error", e);
                return null;
            } finally {
                positionReadWriteLock.get(agvCode).readLock().unlock();
            }
        }

        public void putPosition(String agvCode, Position position) {
            positionReadWriteLock.computeIfAbsent(agvCode, k -> new ReentrantReadWriteLock());
            positionReadWriteLock.get(agvCode).writeLock().lock();
            try {
                agvToPosition.put(agvCode, position);
            } catch (Exception e) {
                logger.error("put position error", e);
            } finally {
                positionReadWriteLock.get(agvCode).writeLock().unlock();
            }
        }

        public VehicleLocation getLocation(String agvCode) {
            locationReadWriteLock.computeIfAbsent(agvCode, k -> new ReentrantReadWriteLock());
            locationReadWriteLock.get(agvCode).readLock().lock();
            try {
                return agvToVehicleLocation.get(agvCode);
            } catch (Exception e) {
                logger.error("get position error", e);
                return null;
            } finally {
                locationReadWriteLock.get(agvCode).readLock().unlock();
            }
        }

        public void putLocation(String agvCode, VehicleLocation location) {
            locationReadWriteLock.computeIfAbsent(agvCode, k -> new ReentrantReadWriteLock());
            locationReadWriteLock.get(agvCode).writeLock().lock();
            try {
                agvToVehicleLocation.put(agvCode, location);
            } catch (Exception e) {
                logger.error("put location error", e);
            } finally {
                locationReadWriteLock.get(agvCode).writeLock().unlock();
            }
        }

    }

    private ExecutorService cachedThreadPool = Executors.newCachedThreadPool();

    private static KDTreeManager kdTreeManager = KDTreeManager.getInstance();

    @Override
    public void buildKDTree(String agvMapId, Collection<Marker> markers) {
        if (markers.size() <= 0) {
            return;
        }
        try {
            kdTreeManager.buildKDTree(agvMapId, markers);
        } catch (Exception e) {
            logger.error("[{}]", e);
        }
    }

    @Override
    public void removeKDTree(String agvMapId) {
        if (agvMapId == null) return;
        kdTreeManager.removeKDTree(agvMapId);
    }

    @Override
    public String queryRecent(String agvMapId, Double x, Double y) {
        String markerId = kdTreeManager.query(agvMapId, x, y);
        if (markerId != null) {
            Marker marker = MapGraphUtil.getMarkerByMarkerId(markerId);
            if (marker != null) {
                Double norm = new Point(marker.getX() - x, marker.getY() - y).getNorm();

                // 如果为工作点，则其允许距离为workPointMaxDerailmentDistance，否则允许距离为nonWorkPointMaxDerailmentDistance
                if ((Objects.equals(MapConstant.MARKER_TYPE_WORK, marker.getType()) && norm < workPointMaxDerailmentDistance)
                        || (!Objects.equals(MapConstant.MARKER_TYPE_WORK, marker.getType()) && norm < nonWorkPointMaxDerailmentDistance)) {
                    return markerId;
                }
            }
        }
        return null;
    }

    /**
     * @param agvCode
     * @return
     */
    @Override
    public VehicleLocation getVehicleLocation(String agvCode) {
        return positionAndLocation.getLocation(agvCode);
    }

    /**
     * 清理agv的location
     * 如果当前agv在充电点，或者泊车点，则取消当前的充电，泊车分配，并释放泊车，充电点
     *
     * @param agvCode
     */
    @Override
    public void removeVehicleLocation(String agvCode) {
        //清理自身的location
        positionAndLocation.agvToVehicleLocation.remove(agvCode);
    }

    private VehicleLocation getVehicleLocationFromPosition(Position position) {
        if (position == null || position.getAgvMapId() == null || position.getPos_x() == null || position.getPos_y() == null || position.getUpdate_time_millis() == null) {
            return null;
        }
        return getVehicleLocation(position.getAgvMapId(), position.getPos_x(), position.getPos_y(), position.getUpdate_time_millis());
    }

    @Override
    public VehicleLocation getVehicleLocation(String agvCode, List<SidePath> sidePaths) {
        Position position = positionAndLocation.getPosition(agvCode);
        if (position == null || position.getAgvMapId() == null || position.getPos_x() == null || position.getPos_y() == null || position.getUpdate_time_millis() == null) {
            return null;
        }
        return getVehicleLocation(position.getAgvMapId(), position.getPos_x(), position.getPos_y(), sidePaths, position.getUpdate_time_millis());
    }

    public VehicleLocation getVehicleLocation(String agvMapId, double x, double y, long update_time_millis) {
        List<Marker> markers = MapGraphUtil.getMarkersByAGVMapId(agvMapId);
        List<SidePath> sidePaths = MapGraphUtil.getSidePathsByAGVMapId(agvMapId);
        return getVehicleLocation(agvMapId, x, y, markers, sidePaths, update_time_millis);
    }


    public VehicleLocation getVehicleLocation(String agvMapId, double x, double y, List<Marker> markers, List<SidePath> sidePaths, long update_time_millis) {
        try {
//            logger.debug("position x:[{}],y:[{}]", x, y);
            Point point = new Point(x, y);
            if (markers == null) {
                //当数据库中不存在marker点时直接返回null
                return null;
            }
            //优先判断机器人在二维码点上
            //1. first method
            //for (Marker marker : markers) {
            //    if (!marker.getAgvMapId().equals(agvMapId)) continue;
            //    double distance = new Point(marker.getX() - x, marker.getY() - y).getNorm();
            //    if (distance < nonWorkPointMaxDerailmentDistance) {
            //        VehicleLocation vehicleLocation = new VehicleLocation();
            //        vehicleLocation.setMarker(marker);
            //        vehicleLocation.setSidePaths(null);
            //        vehicleLocation.setUpdateTimeMillis(update_time_millis);
            //        return vehicleLocation;
            //    }
            //}
            //2. second method
            String markerId = this.queryRecent(agvMapId, x, y);
            if (markerId != null) {
                VehicleLocation vehicleLocation = new VehicleLocation();
                vehicleLocation.setMarker(MapGraphUtil.getMarkerByMarkerId(markerId));
                vehicleLocation.setSidePaths(null);
                vehicleLocation.setUpdateTimeMillis(update_time_millis);
                return vehicleLocation;
            }

            /*最后用机器人的x,y计算所在的边*/
            if (sidePaths == null) {
                //当数据库中不存在路径时直接返回null
                return null;
            }
            VehicleLocation vehicleLocation = new VehicleLocation();
            List<SidePath> tempSidePath = new ArrayList<>();
            for (SidePath sidePath : sidePaths) {
                if (!sidePath.getAgvMapName().equals(agvMapId)) continue;
                Point[] points = new Point[4];
                Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
                Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());

                if (Objects.isNull(startMarker)) {
                    logger.error("sidePath--- {}", JSON.toJSONString(sidePath));
                }

                points[0] = new Point(startMarker.getX(), startMarker.getY());
                points[3] = new Point(endMarker.getX(), endMarker.getY());
                boolean isPtInPoly = false;
                //用side path的t0存储agv当前的位置
                double t0 = -1;
                if (sidePath.getPathId() == null || sidePath.getLineType() == null) {
                    continue;
                }
                switch (sidePath.getLineType()) {
                    case PATH_LINE_TYPE_CURVE: {
                        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                        isPtInPoly = BezierUtil.isPtInPoly(points, point, nonWorkPointMaxDerailmentDistance);
                        if (isPtInPoly) {
                            t0 = BezierUtil.calPosition(points, point, nonWorkPointMaxDerailmentDistance);
                        }
                        break;
                    }
                    case PATH_LINE_TYPE_STRAIGHT: {
                        Point[] line = new Point[2];
                        line[0] = points[0];
                        line[1] = points[3];
                        isPtInPoly = LineUtil.isPtInPoly(line, point, nonWorkPointMaxDerailmentDistance);
                        if (isPtInPoly) {
                            t0 = LineUtil.calPosition(line, point);
                        }
                        break;
                    }
                }

                if (isPtInPoly) {
                    SidePath temp = sidePath.clone();
                    temp.setT0(t0);
                    tempSidePath.add(temp);
                }
            }
            //在side path上设置路径，并将占据的导航点置空
            if (!CollectionUtils.isEmpty(tempSidePath)) {
                vehicleLocation.setSidePaths(tempSidePath);
                vehicleLocation.setMarker(null);
                vehicleLocation.setUpdateTimeMillis(update_time_millis);
                //logger.debug("Location:[{}]", vehicleLocation.toString());
                return vehicleLocation;
            }
        } catch (Exception e) {
            logger.error("getVehicleLocation error {}", LogExceptionStackUtil.LogExceptionStack(e));
        }
        return null;
    }

    public VehicleLocation getVehicleLocation(String agvMapId, Double x, Double y, List<SidePath> sidePaths, long update_time_millis) {
        if (agvMapId == null || x == null || y == null || sidePaths == null) {
            return null;
        }

        Set<Marker> markers = new HashSet<>();
        for (SidePath s : sidePaths) {
            markers.add(MapGraphUtil.getMarkerByMarkerId(s.getStartMarkerId()));
            markers.add(MapGraphUtil.getMarkerByMarkerId(s.getEndMarkerId()));
        }
        return getVehicleLocation(agvMapId, x, y, new ArrayList<>(markers), sidePaths, update_time_millis);
    }

    public SidePath getVehicleLocation(String segmentId, Double t, List<SidePath> vehicleSidePaths) {
        SidePath sidePath = null;
        if (!StringUtils.isEmpty(segmentId) && !segmentId.equals("-1") && containsSegmentId(vehicleSidePaths, segmentId)) {
            //通过反馈的路径信息计算agv所在的路径
            sidePath = MapGraphUtil.getSidePathBySidePathId(segmentId);
            sidePath.setT0(t);
        }
        return sidePath;
    }

    private static boolean containsSegmentId(List<SidePath> sidePaths, String segment_id) {
        boolean containFlag = false;
        for (SidePath sidePath : sidePaths) {
            containFlag |= sidePath.getId().equals(segment_id);
        }
        return containFlag;
    }

    @Override
    public Marker getCurrentMarker(String agvMapId, Double x, Double y) {
        List<Marker> markers = MapGraphUtil.getMarkersByAGVMapId(agvMapId);
        if (markers == null) {
            return null;
        }

        for (Marker marker : markers) {
            double distance = new Point(marker.getX() - x, marker.getY() - y).getNorm();

            if ((!Objects.equals(MapConstant.MARKER_TYPE_WORK, marker.getType()) && distance < nonWorkPointMaxDerailmentDistance)
                    || (Objects.equals(MapConstant.MARKER_TYPE_WORK, marker.getType()) && distance < workPointMaxDerailmentDistance)) {
                return marker;
            }
        }
        return null;
    }

    public MutablePair<SidePath, Double> setCurrentSidePathT0(String agvMapId, Double x, Double y, List<SidePath> sidePaths) {
    	MutablePair<SidePath, Double> pair = new MutablePair<SidePath, Double>();
    	SidePath tmpSidePath = null;
        try {
            Point point = new Point(x, y);
            List<SidePath> tempSidePath = new ArrayList<>(sidePaths);
          
            for (SidePath sidePath : tempSidePath) {
            	tmpSidePath =  sidePath;
                if (!sidePath.getAgvMapName().equals(agvMapId)) continue;
                Point[] points = new Point[4];
                Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
                Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
                points[0] = new Point(startMarker.getX(), startMarker.getY());
                points[3] = new Point(endMarker.getX(), endMarker.getY());
                boolean isPtInPoly = false;
                //用side path的t0存储agv当前的位置
                double t0 = -1;
                if (sidePath.getLineType() == null) {
                    //虚拟的路径，跨地图时生成的虚拟路径
                    if (point.minus(points[0]).getNorm() < nonWorkPointMaxDerailmentDistance) {
                        sidePath.setT0(0D);
                        
                    }
                    if (point.minus(points[3]).getNorm() < nonWorkPointMaxDerailmentDistance) {
                        sidePath.setT0(1D);
                    }
               
                    continue;
                }
                switch (sidePath.getLineType()) {
                    case PATH_LINE_TYPE_CURVE: {
                        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                        isPtInPoly = BezierUtil.isPtInPoly(points, point, nonWorkPointMaxDerailmentDistance);
                        if (isPtInPoly) {
                            t0 = BezierUtil.calPosition(points, point, nonWorkPointMaxDerailmentDistance);
                        }
                        break;
                    }
                    case PATH_LINE_TYPE_STRAIGHT: {
                        Point[] line = new Point[2];
                        line[0] = points[0];
                        line[1] = points[3];
                        isPtInPoly = LineUtil.isPtInPoly(line, point, nonWorkPointMaxDerailmentDistance);
                        if (isPtInPoly) {
                            t0 = LineUtil.calPosition(line, point);
                        }
                        break;
                    }
                }
                if (isPtInPoly) {
                    sidePath.setT0(t0);
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(Objects.nonNull(tmpSidePath)) {
        	   pair.setLeft( tmpSidePath);
               pair.setRight( tmpSidePath.getT0() );
        }
        return pair;
    }

    public Position getAgvPosition(String agvCode) {
        return positionAndLocation.getPosition(agvCode);
    }

    private Boolean mapChanged = false;

    @Override
    public void setAGVPosition(String agvCode, PositionMessage positionMessage) {
        if (agvCode == null || positionMessage == null) return;
        if (!mapChanged) {
            pathPlanService.lockPathPlan(agvCode);
            this.calculateLocation(agvCode, positionMessage);
//            cachedThreadPool.execute(new CalculateLocation(agvCode, positionMessage, this.pathPlanService));
           
        }
    }

    //判断一个Double是否为零
    private static Boolean isZero(Double d) {
        //判断是否停止阈值
        return Math.abs(d) <= 0.01;
    }

    private class CalculateLocation extends Thread {
        private String agvCode;
        private PositionMessage positionMessage;
        private PathPlanService pathPlanService;

        public CalculateLocation(String agvCode, PositionMessage positionMessage, PathPlanService pathPlanService) {
            this.agvCode = agvCode;
            this.positionMessage = positionMessage;
            this.pathPlanService = pathPlanService;
        }

        @Override
        public void run() {
            calculateLocation(agvCode, positionMessage);
        }
    }

    private Map<String, String> agvCodeLock = Maps.newConcurrentMap();

    private void calculateLocation(String agvCode, PositionMessage positionMessage) {
        //pathPlanService.lockPathPlan(agvCode);
        try {
            Position position = new Position(positionMessage);
            position.setUpdate_time_millis(System.currentTimeMillis());
            VehicleLocation vl = getVehicleLocationFromPosition(position);
            positionAndLocation.putPosition(agvCode, position);
            positionAndLocation.putLocation(agvCode, vl);

            agvCodeLock.computeIfAbsent(agvCode, k -> k);
            synchronized (agvCodeLock.get(agvCode)) {
                checkAndSendPathService.setAGVPosition(agvCode, vl);
                if (vl == null) {
                    //logger.error("agvCode:[{}] 已脱轨,请检查该机器人", agvCode);
                    return;
                }
                DirectedGraph originDirectedGraph = MapGraphUtil.getOriginDirectedGraph();
                if (vl.getMarker() != null) {
                    modifyAutoWeightByMarkerId(agvCode, vl.getMarker().getId(), originDirectedGraph);
                }
                if (vl.getSidePaths() != null) {
                    List<String> sidePathIds = vl.getSidePaths().stream().map(SidePath::getId).collect(Collectors.toList());
                    modifyAutoWeightBySidePathIds(agvCode, sidePathIds, originDirectedGraph);
                }
                this.modifyUserWeight(agvCode, this.getOccupySingleAreaIds(vl));
            }
        } finally {
            pathPlanService.unlockPathPlan(agvCode);
        }
    }

    /**
     * modify the autoWeight of param dGraph by markerId
     *
     * @param markerId
     * @param agvCode
     * @param dGraph
     */
    public void modifyAutoWeightByMarkerId(String agvCode, String markerId, DirectedGraph dGraph) {
        if (markerId == null || agvCode == null || dGraph == null) return;
        Set<DirectedEdge> inOutEdges = dGraph.getInOutEdges(markerId);
        Set<String> sidePathIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(inOutEdges)) {
            inOutEdges.forEach(edge -> {
                sidePathIds.add(edge.getSidePathId());
            });
        }
        modifyAutoWeight(agvCode, sidePathIds);
    }


    /**
     * modify the autoWeight of param dGraph's edge  By side paths
     *
     * @param sidePathIds
     * @param agvCode
     * @param dGraph
     */
    public void modifyAutoWeightBySidePathIds(String agvCode, List<String> sidePathIds, DirectedGraph dGraph) {
        if (sidePathIds == null || agvCode == null || dGraph == null) return;
        Set<String> observeReverseSidePathIds = new HashSet<>();
        for (String sidePathId : sidePathIds) {
            if (sidePathId != null) {
                observeReverseSidePathIds.add(sidePathId);
                SidePath reverseSidePath = MapGraphUtil.getReverseSidePath(sidePathId);
                if (reverseSidePath != null) {
                    observeReverseSidePathIds.add(reverseSidePath.getId());
                }
            }
        }
        modifyAutoWeight(agvCode, observeReverseSidePathIds);
    }

    private Map<String, Set<String>> agvCodeToModifyAutoWeightEdgesByLocation = new ConcurrentHashMap<>();

    private void modifyAutoWeight(String agvCode, Set<String> sidePathIds) {
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            if (agvCode == null || sidePathIds == null) return;
            Set<String> lastSidePathIds = agvCodeToModifyAutoWeightEdgesByLocation.getOrDefault(agvCode, new HashSet<>());
            //权重需要被减掉的差集
            Set<String> exceptToBeMinus = new HashSet<>(lastSidePathIds);
            exceptToBeMinus.removeAll(sidePathIds);
            //权重需要被加上的差集
            Set<String> exceptToBePlus = new HashSet<>(sidePathIds);
            exceptToBePlus.removeAll(lastSidePathIds);
            for (String sidePathId : exceptToBeMinus) {
                MapGraphUtil.modifyAutoWeight(agvCode, sidePathId, MINUS_WEIGHT, locationPlusAutoWeight);
            }
            for (String sidePathId : exceptToBePlus) {
                MapGraphUtil.modifyAutoWeight(agvCode, sidePathId, PLUS_WEIGHT, locationPlusAutoWeight);
            }
            agvCodeToModifyAutoWeightEdgesByLocation.put(agvCode, sidePathIds);
        }
    }

    private Map<String, Set<String>> agvCodeToModifyUserWeightEdgesByLocation = new ConcurrentHashMap<>();

    private void modifyUserWeight(String agvCode, Set<String> singleAreaIds) {
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            if (agvCode == null) return;
            Set<String> lastSingleAreaIds = agvCodeToModifyUserWeightEdgesByLocation.getOrDefault(agvCode, new HashSet<>());
            //权重需要被减掉的差集
            Set<String> exceptToBeMinus = new HashSet<>(lastSingleAreaIds);
            exceptToBeMinus.removeAll(singleAreaIds);
            //权重需要被加上的差集
            Set<String> exceptToBePlus = new HashSet<>(singleAreaIds);
            exceptToBePlus.removeAll(lastSingleAreaIds);

            exceptToBeMinus.forEach(singleAreaId -> modifyUserWeightByAreaId(singleAreaId, MINUS_WEIGHT));
            exceptToBePlus.forEach(singleAreaId -> modifyUserWeightByAreaId(singleAreaId, PLUS_WEIGHT));

            agvCodeToModifyUserWeightEdgesByLocation.put(agvCode, singleAreaIds);
        }
    }

    private void modifyUserWeightByAreaId(String singleAreaId, String type) {
        //获取区域内所有点
        Set<String> markerIds = singleAreaPathResourcePool.queryMarkerIdsBySingleAreaPathResourceId(singleAreaId);
        Set<SidePath> activeSidePaths = MapGraphUtil.getActiveSidePaths();
        //获取区域内所有路径(路径的两个点当且仅当一个点在单机区域内)
        Set<SidePath> singleAreaSidePaths = activeSidePaths.stream().filter(sidePath ->
                (markerIds.contains(sidePath.getStartMarkerId()) && !markerIds.contains(sidePath.getEndMarkerId())) ||
                        (!markerIds.contains(sidePath.getStartMarkerId()) && markerIds.contains(sidePath.getEndMarkerId())))
                .collect(Collectors.toSet());
//        logger.debug("单机区域内路径更新权重, 类型:{}, 路径:{}", type, MapGraphUtil.printSidePaths(singleAreaSidePaths));
        if (!CollectionUtils.isEmpty(singleAreaSidePaths)) {
            singleAreaSidePaths.forEach(sidePath -> MapGraphUtil.modifyUserWeight(sidePath.getId(), type, singleAreaPlusAutoWeight));
        }
    }

    /**
     * 获取所在单机区域
     *
     * @param vehicleLocation
     * @return
     */
    private Set<String> getOccupySingleAreaIds(VehicleLocation vehicleLocation) {
        Set<String> occupySingleAreaIds = new HashSet<>();
        if (vehicleLocation.getMarker() != null) {
            Marker marker = vehicleLocation.getMarker();
            //根据markerId, 获取其占用的单机区域
            occupySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdByMarkerId(marker.getId());
        } else if (vehicleLocation.getSidePaths() != null) {
            List<SidePath> sidePaths = vehicleLocation.getSidePaths();
            LinkedList<String> markerIds = SidePathUtils.getMarkerIdsFromSidePaths(sidePaths);
            occupySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdsByMarkerIds(markerIds);
        }
        return occupySingleAreaIds;
    }

    @Override
    public void startMapChange() {
        mapChanged = true;
        for (String agvCode : agvCodeToModifyAutoWeightEdgesByLocation.keySet()) {
            modifyAutoWeight(agvCode, new HashSet<>());
        }
        for (String agvCode : agvCodeToModifyUserWeightEdgesByLocation.keySet()) {
            modifyUserWeight(agvCode, new HashSet<>());
        }
    }

    @Override
    public void endMapChange() {
        mapChanged = false;
    }

    @Override
    public void releaseEdgeWeight(String agvCode) {
        if (!StringUtils.isEmpty(agvCode)) {
            modifyAutoWeight(agvCode, new HashSet<>());
            modifyUserWeight(agvCode, new HashSet<>());
        }
    }
}
