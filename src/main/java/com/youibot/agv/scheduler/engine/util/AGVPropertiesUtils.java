package com.youibot.agv.scheduler.engine.util;

import java.util.Objects;

import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

public class AGVPropertiesUtils {

    private static Environment env = null;

    public static void setEnvironment(Environment env) {
        AGVPropertiesUtils.env = env;
    }

    public static Integer getInteger(String key) {
        String value = getPro(key);
//        if (StringUtils.isEmpty(value) || !org.apache.commons.lang3.StringUtils.isNumeric(value)) {
        if (StringUtils.isEmpty(value)){
            return null;
        }
        return Integer.valueOf(value);
    }

    public static int getInt(String key) {
        String value = getPro(key);
        if (StringUtils.isEmpty(value)){
            return 0;
        }
        return Integer.valueOf(value);
    }

	private static String getPro(String key) {
		if(Objects.isNull(AGVPropertiesUtils.env)) {
			return null;
		}
		return AGVPropertiesUtils.env.getProperty(key);
	}

    public static Long getLong(String key) {
        String value = getPro(key);
        if (StringUtils.isEmpty(value)){
            return null;
        }
        return Long.valueOf(value);
    }

    public static Long getLong(String key, Long defaultValue) {
        Long value = getLong(key);
        if(value==null){
            value = defaultValue;
        }
        return value;
    }


    public static String getString(String key) {
        return getPro(key);
    }

    public static Double getDouble(String key) {
        String value = getPro(key);
        if (StringUtils.isEmpty(value) ){//|| !org.apache.commons.lang3.StringUtils.isNumeric(value)) {
            return null;
        }
        return Double.valueOf(value);
    }

    public static Boolean getBoolean(String key) {
        String value = getPro(key);
        if (StringUtils.isEmpty(value) ){//|| !org.apache.commons.lang3.StringUtils.isNumeric(value)) {
            return null;
        }
        return Boolean.valueOf(value);
    }

}
