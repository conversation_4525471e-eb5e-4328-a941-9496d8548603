package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.exception.ActionException;
import com.youibot.agv.scheduler.engine.exception.AvoidingObstaclesException;
import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 动作执行器
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/2/28 10:26
 */
public abstract class DefaultAction implements Action, Serializable {

    protected MissionWorkAction missionWorkAction;
    protected String missionWorkId;
    protected Vehicle vehicle;
    private Integer faultResendCount = 0;//agv执行指令失败时当前重新发送命令的次数

    public void init(MissionWorkAction missionWorkAction) {
        this.missionWorkAction = missionWorkAction;
        this.missionWorkId = missionWorkAction.getMissionWorkId();
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultAction.class);

    @Value("${AGV_THREAD_CONFIG.WAIT_TIME.AGV_RETRY_GET_STATUS}")
    protected Integer agvRetryGetStatusWaitTime;//查询执行状态每次等待时间间隔

    @Value("${CALL_AGV_API_CONFIG.FAULT_RESEND_COUNT}")
    protected Integer faultResendAllCount;//agv执行指令失败时重新发送命令的总次数

    @Override
    public Map<String, Object> execute(Vehicle vehicle) throws InterruptedException, IOException {
        try {
            if (Thread.interrupted()) {//如果当前线程被中断了
                throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
            }
            LOGGER.debug("mission work name : " + missionWorkAction.getName() + " start execute.");
            this.vehicle = vehicle;
//            this.client = AGVSocketClient.createAGVClient(this.vehicle.getIp(), this.getAPIPort());
//            LOGGER.debug("client system info " + client.getSocket().isConnected());
            return sendCommand();
        } catch (InterruptedException e) {
            LOGGER.warn("mission action name : " + missionWorkAction.getName() + " thread is interrupted, ", e);
            throw e;
        } catch (AvoidingObstaclesException e) {
            LOGGER.warn("mission action name : " + missionWorkAction.getName() + " obstacle avoidance timeout, ", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("action execute error, ", e);
            //如果agv执行指令失败，在限制的重新执行次数内重新发送执行指令
            return reExecute(e);
        }
    }

    protected Map<String, Object> sendCommand() throws IOException, InterruptedException, PathPlanException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        //发送操作指令
//        ActionUtils.sendInstruction(client, this.getAPICode(), this.getParamJson().toJSONString());
//        Map<String, Object> resultMap =ActionUtils.getActionStatus(vehicle.getIp());
//        return (JSONObject) resultMap.get("feedback");
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("code", "SUCCESS");
        return resultData;
    }

    /**
     * 重新执行任务
     *
     * @param e
     * @return
     */
    protected Map<String, Object> reExecute(Exception e) throws InterruptedException, IOException {
        if (faultResendAllCount > faultResendCount) {
            reset();
            faultResendCount++;
            return execute(vehicle);
        } else {
            if (e instanceof IOException) {
                LOGGER.warn("mission action name : " + missionWorkAction.getName() + " execute io error", e);
                throw new IOException(e);
            } else if (e instanceof YOUIFleetException) {
                LOGGER.warn("mission action name : " + missionWorkAction.getName() + " execute agv result error", e);
                throw new YOUIFleetException(e.getMessage());
            } else {
                LOGGER.warn("mission action name : " + missionWorkAction.getName() + " execute action error", e);
                throw new ActionException(e.getMessage());
            }
        }
    }

    /**
     * 获取该missionWorkAction的ParameterJson
     *
     * @return
     */
    protected JSONObject getParamJson() {
        String parameter = missionWorkAction.getParameters();
        if (StringUtils.isEmpty(parameter)) {
            parameter = "{}";
        }
        JSONObject paramJson = JSONObject.parseObject(parameter);//获取参数json格式
        paramJson.put("id", missionWorkId);
        return paramJson;
    }

    @Override
    public void pause() throws IOException, InterruptedException {
//        MissionWorkUtils.pauseCommand(vehicle.getIp());
    }

    @Override
    public void stop() throws IOException, InterruptedException {
//        MissionWorkUtils.stopCommand(vehicle);
    }

    @Override
    public void resume() throws IOException {
//        MissionWorkUtils.resumeCommand(vehicle.getIp());
    }

    @Override
    public void reset() throws IOException, InterruptedException {
    }
}
