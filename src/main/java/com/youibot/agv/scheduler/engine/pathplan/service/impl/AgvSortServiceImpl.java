package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DStarLiteAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.SidePathPlanResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.ALL_VEHICLE;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 上午11:03 2020/9/26
 * @Description :
 * @Modified By :
 * @Version :
 */
@Service
public class AgvSortServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(AgvSortServiceImpl.class);

    @Autowired
    private LocationService locationService;

    @Autowired
    private PathPlanService pathPlanService;

    public List<Vehicle> sort(String firstMarkerId, List<Vehicle> vehicles) {
        if (firstMarkerId == null) {
            return vehicles;
        }
        Map<String, Vehicle> vehicleMap = new HashMap<>();
        vehicles.forEach(vehicle -> vehicleMap.put(vehicle.getId(), vehicle));
        String aimMarkerId = firstMarkerId;

        //calculationCost
        AGVCost[] agvCosts = new AGVCost[vehicles.size()];
        int i = 0;
        for (Vehicle vehicle : vehicles) {
            long tolerateTime = 2000;
            VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicle.getId());
            if (vehicleLocation == null) {
                agvCosts[i] = new AGVCost(vehicle.getId(), Double.POSITIVE_INFINITY);
                i++;
                continue;
            }
            long timeOut = Math.abs(System.currentTimeMillis() - vehicleLocation.getUpdateTimeMillis());
            if (timeOut > tolerateTime) {
                logger.warn("vehicle name:[{}]，定位数据延时为[{}]ms，请检查网络和运行环境", vehicle.getName(), timeOut);
            }
            DirectedGraph dGraph = MapGraphUtil.getOriginDirectedGraph();
            DStarLiteAlgorithm dStarLiteAlgorithm = new DStarLiteAlgorithm(ALL_VEHICLE, dGraph);
            dStarLiteAlgorithm.setDynamic(false);
            if (vehicleLocation.getMarker() != null) {
                String startMarkerId = vehicleLocation.getMarker().getId();
                MarkerPathResult markerPathResult = dStarLiteAlgorithm.rePlan(startMarkerId, aimMarkerId);
                if (!markerPathResult.isSuccess()) {
                    agvCosts[i] = new AGVCost(vehicle.getId(), Double.POSITIVE_INFINITY);
                    i++;
                    continue;
                }
                SidePathPlanResult sidePathPlanResult = pathPlanService.linkerListPathToSidePath(vehicle.getId(), markerPathResult);
                agvCosts[i] = new AGVCost(vehicle.getId(), sidePathPlanResult.getCost());
                i++;
            } else if (vehicleLocation.getSidePaths() != null) {
                List<SidePath> sp = vehicleLocation.getSidePaths();
                List<MarkerPathResult> markerPathResultList = new ArrayList<>();
                for (SidePath s : sp) {
                    //if (MapGraphUtil.containedSidePath(s) && MapGraphUtil.SidePathIsAvailableByThisAGV(vehicle.getId(), s)) {
                    if (MapGraphUtil.containedSidePath(s)) {
                        String startMarkerId = s.getEndMarkerId();
                        MarkerPathResult markerPathResult = dStarLiteAlgorithm.rePlan(startMarkerId, aimMarkerId);
                        if (!markerPathResult.isSuccess()) {
                            continue;
                        }
                        markerPathResult.setSidePath(s);
                        Double cost = MapGraphUtil.getPiecemealCost(s) + markerPathResult.getCost();
                        if (markerPathResult.getMarkerPath().size() != 0) {
                            cost += MapGraphUtil.getAngleCost(s.getStartMarkerId(), s.getEndMarkerId(), markerPathResult.getMarkerPath().getFirst());
                        }
                        markerPathResult.setCost(cost);
                        markerPathResultList.add(markerPathResult);
                    }
                }
                Double min = Double.POSITIVE_INFINITY;
                MarkerPathResult rmin = null;
                for (MarkerPathResult markerPathResult : markerPathResultList) {
                    if (!markerPathResult.isSuccess()) continue;
                    if (markerPathResult.getCost() < min) {
                        min = markerPathResult.getCost();
                        rmin = markerPathResult;
                    }
                }
                if (rmin != null) {
                    SidePathPlanResult sidePathPlanResult = pathPlanService.linkerListPathToSidePath(vehicle.getId(), rmin);
                    agvCosts[i] = new AGVCost(vehicle.getId(), sidePathPlanResult.getCost());
                    i++;
                } else {
                    agvCosts[i] = new AGVCost(vehicle.getId(), Double.POSITIVE_INFINITY);
                    i++;
                }
            }
        }
        sort(agvCosts);
        return Arrays.stream(agvCosts).map(agvCost -> vehicleMap.get(agvCost.getAgvCode())).collect(Collectors.toList());
    }

    public static void sort(AGVCost[] agvCosts) {
        sort(agvCosts, 0, agvCosts.length - 1);
    }

    //快速排序算法
    public static void sort(AGVCost[] agvCosts, int begin, int end) {
        int low = begin;
        int high = end;
        if (low >= high) {
            return;
        }
        AGVCost key = agvCosts[low];
        while (low < high) {
            while (low < high && agvCosts[high].cost >= key.cost) {
                high--;
            }
            if (low < high) {
                agvCosts[low] = agvCosts[high];
                low++;
            }
            while (low < high && agvCosts[low].cost <= key.cost) {
                low++;
            }
            if (low < high) {
                agvCosts[high] = agvCosts[low];
                high--;
            }
        }
        agvCosts[low] = key;
        sort(agvCosts, begin, low - 1);
        sort(agvCosts, low + 1, end);
    }

    @Data
    public static class AGVCost {
        public String agvCode;
        public Double cost;

        public AGVCost(String agvCode, Double cost) {
            this.agvCode = agvCode;
            this.cost = cost;
        }
    }

}
