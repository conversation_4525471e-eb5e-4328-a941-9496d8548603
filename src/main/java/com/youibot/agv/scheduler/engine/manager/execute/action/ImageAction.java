package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.Base64Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 操作设备的action
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/6/19 11:25
 */
public abstract class ImageAction extends DefaultAction {

    private static final Logger logger = LoggerFactory.getLogger(ImageAction.class);


    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionService missionService;

    /**
     * 保存图片
     *
     * @param base64     base64数据
     * @param saveFolder 保存的文件夹名称
     * @return 图片存储路径
     */
    public String saveImage(String base64, String saveFolder) {
        //获取mission work
        MissionWork missionWork = missionWorkService.selectById(missionWorkAction.getMissionWorkId());
        if (missionWork == null || StringUtils.isEmpty(missionWork.getMissionId())) {
            logger.error("mission work or mission id is null, missionWorkId" + missionWorkId);
            throw new ExecuteException(ErrorEnum.MISSION_WORK_IS_NULL.code(), ErrorEnum.MISSION_WORK_IS_NULL.msg());
        }
        //获取mission
        Mission mission = missionService.selectById(missionWork.getMissionId());
        if (mission == null) {
            logger.error("mission is null, missionId = " + missionWork.getMissionId());
            throw new ExecuteException(ErrorEnum.MISSION_IS_NULL.code(), ErrorEnum.MISSION_IS_NULL.msg());
        }
        //图片名称：missionName-UUID
        String name = mission.getName().replace("/", "%2F");
        String imageName = name + "-" + UUID.randomUUID() + ".jpg";

        //获取年月日，格式：yyyyMMdd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateFormat.format(new Date());
        //图片保存路径中添加时间文件夹
        saveFolder = saveFolder + File.separator + dateStr;
        //将base64转成图片保存
        base64 = base64.replace("\r", "").replace("\t", "").replace("\n", "");
        Base64Utils.base64ToImage(base64, saveFolder, imageName);
        String imagePath = saveFolder + File.separator + imageName;
        return imagePath.replace("\\", "/");
    }
}
