package com.youibot.agv.scheduler.engine.service;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.manager.socket.DataProtocol;
import com.youibot.agv.scheduler.engine.util.AGVResultInfoUtils;
import com.youibot.agv.scheduler.entity.AGV;
import com.youibot.agv.scheduler.exception.ExecuteException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

/**
 * agv api执行器共用类
 *
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019年5月6日 下午4:38:57
 */
public class BaseApiExecute {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseApiExecute.class);

    /**
     * 执行方法
     *
     * @param agv     agv对象
     * @param port    端口号
     * @param apiCode api编号
     * @param dataMap 传递给agv中数据区的数据（Map类型）
     * @return
     * @throws AGVResultException
     * @throws IOException
     */
    public Map<String, Object> execute(AGV agv, Integer port, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException {
        AGVSocketClient client = null;
        try {
            if (agv == null || apiCode == null || port == null) {
                LOGGER.error("agv or agv ip or apiCode or agv map port is empty");
                throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
            }
            client = this.createAGVClient("**********", port);
            // 将参数map转成agv api 实际需要的json格式
            String dataJsonStr = "";
            if (dataMap != null) {
                dataJsonStr = JSON.toJSONString(dataMap);
            }
            DataProtocol protocol = new DataProtocol(apiCode, dataJsonStr);
            // 发送并读取数据
            DataProtocol protocolResult = client.sendAndReceive(protocol);
            String resultInfo = protocolResult.getData();// agv数据区返回的数据
            return AGVResultInfoUtils.getDataMap(resultInfo);// 解析获取agv返回的data数据并转成map
        } catch (IOException e) {
            LOGGER.error("Socket client send error.", e);
            throw e;
        } finally {
            if (client != null) {
                client.stop();
            }
        }
    }

    // 创建AGV client 类
    public AGVSocketClient createAGVClient(String address, int port) throws IOException {
        AGVSocketClient client = new AGVSocketClient(address, port);
        client.create();
        return client;
    }

}
