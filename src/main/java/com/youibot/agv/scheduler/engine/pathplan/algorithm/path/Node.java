package com.youibot.agv.scheduler.engine.pathplan.algorithm.path;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedEdge;
import com.youibot.agv.scheduler.entity.SidePath;

/**
 * @Annotion: 节点实体,一个实体对应的是一条线,链接两个节点
 * @ClassName: Node
 * @Author:
 * @Date: 2019/9/29 10:11
 * @Version: 1.0
 */
public class Node {
	/** 上游节点 */
	private String source;
	/** 下游节点 */
	private String target;

	public Node(String source, String target) {
		this.source = source;
		this.target = target;
	}

	public Node( DirectedEdge edge) {
		this.source = edge.getACode();
		this.target = edge.getBCode();
	}
	
	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}
}
