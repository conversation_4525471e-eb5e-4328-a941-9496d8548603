package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.util.List;

/**
 * <AUTHOR>
 * @Date :Created in 下午2:27 2020/8/27
 * @Description :
 * @Modified By :
 * @Version :
 */
public interface AGVAllocateService {

    /**
     * 根据agvCode查找空闲的agv
     *
     * @param agvCode
     * @param vehicles
     * @param targetSequence
     * @return
     */
    List<Vehicle> getVehiclesByAgvCode(String agvCode, List<Vehicle> vehicles, Integer targetSequence);

    /**
     * 根据missionWork信息过滤出可用的机器人
     *
     * @param availableVehicles
     * @param missionWork
     * @return
     */
    List<Vehicle> filterVehicles(List<Vehicle> availableVehicles, MissionWork missionWork);

}
