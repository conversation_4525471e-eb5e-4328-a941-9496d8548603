package com.youibot.agv.scheduler.engine.service.environment;

import java.io.IOException;
import java.util.Map;

/**
 * api温湿度service
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/10/16 16:05
 */
public interface TemperatureAndHumidityApiService {

    /**
     * 读取温湿度
     * @param ip
     * @return
     * @throws IOException
     */
    Map<String, Object> queryTemperatureAndHumidity(String ip) throws IOException;
}
