package com.youibot.agv.scheduler.engine.manager.modbus;

import com.alibaba.fastjson.JSONObject;
import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.serotonin.modbus4j.Modbus;
import com.youibot.agv.scheduler.constant.ActionConstant;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.UnknownHostException;
import java.util.Map;

/**
 * modbus 功能码
 */
@Component
public class ModbusFunction {
    private Logger LOGGER = LoggerFactory.getLogger(ModbusFunction.class);

    private static final Integer MAX_VALUE = 32767;//最大输入值
    private static final Integer MIN_VALUE = 0;//最小输入值


    public Map<String, Object> readFunction_01(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        return this.readCoilData(paramJson);

    }

    public Map<String, Object> readFunction_02(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        return this.readCoilData(paramJson);
    }


    public Map<String, Object> readFunction_03(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        return this.readShortData(paramJson);
    }

    public Map<String, Object> readFunction_04(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        return this.readShortData(paramJson);
    }


    private Map<String, Object> readCoilData(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        Map<String, Object> resultData = new HashedMap<>(2);
        ModbusActionParams actionParams = new ModbusActionParams(paramJson);
        ModbusMaster modbusMaster = (ModbusMaster) paramJson.get("modbusMaster");
        if (modbusMaster == null) {
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
        }
        boolean[] readCoilStatusValue = readCoilStatusValue(modbusMaster, actionParams.getCode(),
                actionParams.getSlaveId(), actionParams.getStartAddress(), actionParams.getNumberOfBits());
        resultData.put("result", readCoilStatusValue);
        return resultData;
    }


    private Map<String, Object> readShortData(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        Map<String, Object> resultData = new HashedMap<>(2);
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        ModbusActionParams actionParams = new ModbusActionParams(paramJson);
        ModbusMaster modbusMaster = (ModbusMaster) paramJson.get("modbusMaster");
        if (modbusMaster == null) {
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
        }
        int[] resultValue = readShortValue(modbusMaster, actionParams.getCode(), actionParams.getSlaveId(), actionParams.getStartAddress(), actionParams.getNumberOfBits());
        resultData.put("result", resultValue);
        return resultData;
    }

    /**
     * 写单个线圈
     *
     * @param paramJson
     * @return
     */
    public Map<String, Object> writeFunction_05(JSONObject paramJson) throws UnknownHostException {
        Map<String, Object> resultData = new HashedMap<>(2);
        ModbusActionParams actionParams = new ModbusActionParams(paramJson);
        ModbusMaster modbusMaster = (ModbusMaster) paramJson.get("modbusMaster");
        if (modbusMaster == null) {
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
        }
        String[] values = actionParams.getValue().split(",");
        try {
            Integer writeValue = Integer.valueOf(values[0]);
            if (writeValue != 0 && writeValue != 1) {
                throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
            }
            modbusMaster.writeSingleCoil(actionParams.getSlaveId(), actionParams.getStartAddress(), writeValue != 0);
        } catch (Exception e) {
            LOGGER.error("写入值失败参数+" + JSONObject.toJSONString(actionParams) + "错误信息" + e.getMessage());
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        }
        resultData.put("result", true);
        return resultData;
    }


    /**
     * 写多个线圈
     *
     * @param paramJson
     * @return
     */
    public Map<String, Object> writeFunction_15(JSONObject paramJson) throws UnknownHostException {
        Map<String, Object> resultData = new HashedMap<>(2);
        ModbusActionParams actionParams = new ModbusActionParams(paramJson);
        ModbusMaster modbusMaster = (ModbusMaster) paramJson.get("modbusMaster");
        if (modbusMaster == null) {
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
        }
        String[] paramValues = actionParams.getValue().split(",");
        try {
            boolean[] writeValues = new boolean[paramValues.length];
            for (int i = 0; i < paramValues.length; i++) {
                Integer writeValue = Integer.valueOf(paramValues[i]);
                if (writeValue != 0 && writeValue != 1) {
                    throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
                }
                writeValues[i] = writeValue == 1;
            }
            modbusMaster.writeMultipleCoils(actionParams.getSlaveId(), actionParams.getStartAddress(), writeValues);
        } catch (Exception e) {
            LOGGER.error("写入值失败参数+" + JSONObject.toJSONString(actionParams) + "错误信息" + e.getMessage());
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        }
        resultData.put("result", true);
        return resultData;
    }


    public Map<String, Object> writeFunction_06(JSONObject paramJson) throws UnknownHostException {
        Map<String, Object> resultData = new HashedMap<>(2);
        ModbusActionParams actionParams = new ModbusActionParams(paramJson);
        ModbusMaster modbusMaster = (ModbusMaster) paramJson.get("modbusMaster");
        if (modbusMaster == null) {
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
        }
        String[] values = actionParams.getValue().split(",");
        try {
            Integer writeValue = Integer.valueOf(values[0]);
            if (writeValue > MAX_VALUE || writeValue < MIN_VALUE) {
                throw new ADSParameterException(MessageUtils.getMessage("action.wrong_value"));
            }
            modbusMaster.writeSingleRegister(actionParams.getSlaveId(), actionParams.getStartAddress(), writeValue);
        } catch (Exception e) {
            LOGGER.error("写入值失败参数+" + JSONObject.toJSONString(actionParams) + "错误信息" + e.getMessage());
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        }
        resultData.put("result", true);
        return resultData;
    }

    public Map<String, Object> writeFunction_16(JSONObject paramJson) throws UnknownHostException {
        Map<String, Object> resultData = new HashedMap<>(2);
        ModbusActionParams actionParams = new ModbusActionParams(paramJson);
        ModbusMaster modbusMaster = (ModbusMaster) paramJson.get("modbusMaster");
        if (modbusMaster == null) {
            modbusMaster = JLibModbusUtils.createModbusMaster(actionParams.getIp(), actionParams.getPort());
        }
        String[] paramValues = actionParams.getValue().split(",");
        int[] writeValues = new int[paramValues.length];
        try {
            for (int i = 0; i < paramValues.length; i++) {
                int writeValue = Integer.valueOf(paramValues[i]);
                if (writeValue > MAX_VALUE || writeValue < MIN_VALUE) {
                    throw new ADSParameterException(MessageUtils.getMessage("action.wrong_value"));
                }
                writeValues[i] = writeValue;
            }
            modbusMaster.writeMultipleRegisters(actionParams.getSlaveId(), actionParams.getStartAddress(), writeValues);
        } catch (Exception e) {
            LOGGER.error("写入值失败参数+" + JSONObject.toJSONString(actionParams) + "错误信息" + e.getMessage());
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        }
        resultData.put("result", true);
        return resultData;
    }


    public boolean[] readCoilStatusValue(ModbusMaster modbusMaster, String code, Integer slaveId, Integer startAddress, Integer numberOfBits) {
        try {
            if (Modbus.DEFAULT_MAX_READ_REGISTER_COUNT < numberOfBits) {
                throw new ADSParameterException(MessageUtils.getMessage("action.wrong_value"));
            }
            try {
                if (ActionConstant.REMOTE_PLC_FUNCTION_READ_01.equals(code)) {
                    return modbusMaster.readCoils(slaveId, startAddress, numberOfBits);
                } else if (ActionConstant.REMOTE_PLC_FUNCTION_READ_02.equals(code)) {
                    return modbusMaster.readDiscreteInputs(slaveId, startAddress, numberOfBits);
                }
            } catch (RuntimeException re) {
                LOGGER.error("读取modbus失败,请检查当前寄存器是否支持该功能码code={} ip={} port={} 错误信息", code, re);
                throw new ExecuteException(ErrorEnum.READ_MODBUS_RESULT_NULL.code(), ErrorEnum.READ_MODBUS_RESULT_NULL.msg());
            }
        } catch (Exception e) {
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        }
        return null;
    }

    public int[] readShortValue(ModbusMaster master, String code, Integer slaveId, Integer
            startAddress, Integer numberOfBits) {
        if (Modbus.DEFAULT_MAX_READ_REGISTER_COUNT < numberOfBits) {
            throw new ADSParameterException(MessageUtils.getMessage("action.wrong_value"));
        }
        try {
            if (ActionConstant.REMOTE_PLC_FUNCTION_READ_03.equals(code)) {
                return master.readHoldingRegisters(slaveId, startAddress, numberOfBits);
            } else if (ActionConstant.REMOTE_PLC_FUNCTION_READ_04.equals(code)) {
                return master.readInputRegisters(slaveId, startAddress, numberOfBits);
            }
        } catch (Exception e) {
            LOGGER.error("寄存器读取失败, code:{}", code, e);
            throw new ExecuteException(ErrorEnum.READ_MODBUS_RESULT_NULL.code(), ErrorEnum.READ_MODBUS_RESULT_NULL.msg());
        }
        return null;
    }

    public Map<String, Object> switchFunctionCode(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        Map<String, Object> resultData;
        String code = paramJson.getString("code");
        paramJson.put("numberOfBits", 1);
        switch (code) {
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_01:
                resultData = readFunction_01(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_02:
                resultData = readFunction_02(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_03:
                resultData = readFunction_03(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_04:
                resultData = readFunction_04(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_05:
                resultData = writeFunction_05(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_06:
                resultData = writeFunction_06(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_15:
                resultData = writeFunction_15(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_16:
                resultData = writeFunction_16(paramJson);
                break;
            default:
                throw new ExecuteException(MessageUtils.getMessage("action.wrong_value") + " mode=" + code);
        }
        return resultData;
    }


}
