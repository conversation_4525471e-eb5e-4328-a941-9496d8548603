package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 夹持器
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2020/7/21 10:47
 */
@Service
@Scope("prototype")
public class GripperAction extends DefaultAction {

    @Override
    public String getAPICode() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.GRIPPER");
    }
}
