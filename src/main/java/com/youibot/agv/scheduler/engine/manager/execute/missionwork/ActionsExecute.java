package com.youibot.agv.scheduler.engine.manager.execute.missionwork;

import com.youibot.agv.scheduler.abnormal.AbnormalCodeConstant;
import com.youibot.agv.scheduler.abnormal.AbnormalMessageUtils;
import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.engine.manager.execute.action.Action;
import com.youibot.agv.scheduler.engine.manager.execute.action.ActionFactory;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionWorkActionParameterService;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.AGVConstant.*;
import static com.youibot.agv.scheduler.constant.MissionConstant.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/9/29 12:08
 */
@Service
public class ActionsExecute {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActionsExecute.class);

    @Autowired
    protected MissionActionService missionActionService;
    @Autowired
    protected MissionWorkService missionWorkService;
    @Autowired
    protected MissionWorkActionService missionWorkActionService;
    @Autowired
    protected MissionWorkActionParameterService missionWorkActionParameterService;
    @Autowired
    protected MissionWorkThreadPool missionWorkThreadPool;

    public boolean executeActions(Vehicle vehicle, List<MissionAction> missionActions) {
        MissionWorkAction missionWorkAction = null;
        MissionWork missionWork = vehicle.getMissionWork();
        label:
        while (true) {
            if(Thread.interrupted()){
                LOGGER.warn("mission work interrupt  error");
                return false;
            }
            try {
                Thread.sleep(100);
                // 遍历missionActionList、解析后相应新增missionWorkAction数据并执行
                int count = 0;
                for (MissionAction missionAction : missionActions) {

                    //由于无限重试，会发生中途小车状态变化，需要判断小车状态是否可用
                    if (!AUTO_CONTROL_MODE.equals(vehicle.getControlMode())) {
                        continue label;
                    } else if (!CONNECT_STATUS_SUCCESS.equals(vehicle.getConnectStatus())) {
                        continue label;
                    } else if (!MAP_STATUS_NORMAL.equals(vehicle.getMapStatus())) {
                        continue label;
                    }

                    if (Thread.interrupted()) {//如果当前线程被中断了
                        throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                    }
                    count++;
                    String ifActionResultData = null;
                    //还原Mission work action. 查看数据库中是否存在missionWorkAction
                    MissionWorkAction missionWorkActionDB = missionWorkActionService.selectByMissionActionIdAndMissionWorkId(missionAction.getId(), missionWork.getId());
                    String missionWorkActionId = null;
                    if (missionWorkActionDB != null) {
                        if (MISSION_WORK_STATUS_SUCCESS.equals(missionWorkActionDB.getStatus())) {
                            continue;//已经执行成功，跳过该action
                        } else {
                            if ("IF_ELSE".equals(missionWorkActionDB.getActionType())) {
                                ifActionResultData = missionWorkActionDB.getResultData();
                            }
                            missionWorkActionId = missionWorkActionDB.getId();
                            //数据库存在该action，但未执行成功，删除该action及parameter数据
                            missionWorkActionService.deleteById(missionWorkActionDB.getId());
                            missionWorkActionParameterService.deleteByMissionWorkActionId(missionWorkActionDB.getId());
                        }
                    }

                    //创建missionWorkAction
                    missionWorkAction = missionWorkActionService.createMissionWorkAction(missionWorkActionId, missionAction, missionWork);
                    missionWorkActionParameterService.addMissionWorkActionParameter(missionWorkAction, missionAction);//添加parameter数据，运行时参数直到用户设置参数后才返回数据
                    //如果是if action, 在resultData字段会存储上一次的比较结果trueActionList或falseActionList，之后恢复时都是按照上一次的比较结果执行对应的子action
                    if ("IF_ELSE".equals(missionWorkAction.getActionType())) {
                        missionWorkAction.setResultData(ifActionResultData);
                    }
                    Action action = ActionFactory.createActionExecute(missionWorkAction);
                    if (action != null) {
                        missionWorkActionService.updateStatus(missionWorkAction, MISSION_WORK_ACTION_STATUS_RUNNING);//missionWorkAction执行中
                        Map<String, Object> resultDataMap = vehicle.executeCommand(action);
                        if (resultDataMap != null) {
                            missionWorkActionService.saveSuccessResult(missionWorkAction, resultDataMap);
                        }
                        LOGGER.debug("mission work action execute success, mission work action id: " + missionWorkAction.getId());
                        missionWorkActionService.updateStatus(missionWorkAction, MISSION_WORK_ACTION_STATUS_SUCCESS);//missionWorkAction执行成功
                        if (MISSION_WORK_STATUS_SHUTDOWN.equals(missionWork.getStatus())) {//如果action中有需求将missionWork任务
                            return false;
                        }
                    }else{
                        Map<String, Object> resultDataMap = new HashMap<>();
                        resultDataMap.put("code","SUCCESS");
                        missionWorkAction.setStatus(MISSION_WORK_ACTION_STATUS_SUCCESS);
                        missionWorkActionService.saveSuccessResult(missionWorkAction, resultDataMap);

                    }
                    // 更新任务进度
                    NumberFormat numberFormat = NumberFormat.getInstance();
                    numberFormat.setMaximumFractionDigits(0);// 设置精确到小数点后2位
                    String progress = numberFormat.format((float) count / (float) missionActions.size() * 100);
                    missionWork.setWorkProgress(progress);
                    missionWork.setRemainingTime(missionWork.getRemainingTime() - RUNTIME_ACTION_TIME);
                    missionWorkService.update(missionWork);
                    missionWorkAction = null;
                }
                return true;
            } catch (InterruptedException | IOException e) {//中断异常以及IO异常不修改missionWork状态，由外部修改或者等待自动恢复
                LOGGER.warn("mission work interrupt or io error", e);
                return false;
            } catch (Exception e) {
                LOGGER.error("Mission work run error", e);

                //在重试过程中，可能遇到中断
                int interruptedFlag = 0;
                while(true){
                    //首次肯定不中断
                    if(interruptedFlag == 2){
                        LOGGER.debug("异常处理逻辑中检测到中断，退出无限循环重试", e);
                        return false;
                    }
                    try {
                        this.updateStatusByExecuteFault(vehicle, missionWork, missionWorkAction, e);
                    }catch (InterruptedException e2){
                        LOGGER.warn("mission work interrupt  error", e);
                        interruptedFlag = 1;
                        continue;
                    }


                    Integer code = e instanceof YOUIFleetException ? ((YOUIFleetException)e).getCode() : AbnormalCodeConstant.COMPASS_ACTION_RUN_ERROR.code();

                    try {
                        AbnormalMessageUtils.sendErrorCodeMessage(vehicle.getDeviceNumber(), missionWork.getId(), code);
                    } catch (InterruptedException e1) {
                        //此时遇到中断，不用处理了
                        return false;
                    }
                    LOGGER.debug("任务执行异常，已上报异常码，作业id：{}，作业名称：{}", missionWork.getId(), missionWork.getName());

                    if(interruptedFlag==1){
                        interruptedFlag = 2;//表明 catch 里面的逻辑已经完整处理过一次了
                    }else{
                        break;
                    }
                }
            }
        }
    }

    private void updateStatusByExecuteFault(Vehicle vehicle, MissionWork missionWork, MissionWorkAction missionWorkAction, Exception e) throws InterruptedException {
        Integer code;
        String msg = "";
        if (!StringUtils.isEmpty(e.getMessage())) {
            msg = e.getMessage();
        }
        if (e instanceof YOUIFleetException) {
            code = ((YOUIFleetException) e).getCode();
            if (StringUtils.isEmpty(msg)) {
                msg = ((YOUIFleetException) e).getMsg();
            }
        } else {
            code = ExceptionInfoEnum.EXCUTE_ACTIOM.getErrorCode();
            if (StringUtils.isEmpty(msg)) {
                msg = ExceptionInfoEnum.EXCUTE_ACTIOM.getMessage();
            }
        }
        if (missionWork != null) {
            missionWork.setMessage(msg);
            missionWork.setErrorCode(code);
            missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
            missionWorkService.updateStatus(missionWork);
        }
        if (missionWorkAction != null) {
            missionWorkAction.setStatus(MISSION_WORK_ACTION_STATUS_FAULT);
            missionWorkAction.setMessage(msg);
            missionWorkAction.setErrorCode(code);
            missionWorkActionService.updateStatus(missionWorkAction);
        }
    }

}
