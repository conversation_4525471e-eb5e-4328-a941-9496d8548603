package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.exception.ExecuteException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil.getDistance;

/**
 * <AUTHOR>
 * @Date :Created in 19:36 2021/9/26
 * @Description :
 * @Modified By :
 * @Version :
 */
public class TSPTMS5FloorAlgorithm {

    private static final Logger logger = LoggerFactory.getLogger(TSPTMS5FloorAlgorithm.class);

    private Map<String, Map<String, Double>> distanceMatrix;                  //距离矩阵

    private String startMarkerId;
    private String endMarkerId;
    private List<Pair<String, String>> floorMarkerPairList;

    public void GAInit(String startMarkerId, List<Pair<String, String>> floorMarkerPairList, String endMarkerId) {
        if (CollectionUtils.isEmpty(floorMarkerPairList) || StringUtils.isEmpty(endMarkerId)) {
            logger.error("TSPTMS4GeneticAlgorithm.GAInit 错误的参数");
            throw new ExecuteException("错误的参数");
        }

        this.startMarkerId = startMarkerId;
        this.endMarkerId = endMarkerId;
        this.floorMarkerPairList = floorMarkerPairList;

        distanceMatrix = DistanceMatrixUtil.getDistanceMatrix();
    }

    public Pair<String, String> run() {
        Double minLength = Double.POSITIVE_INFINITY;
        String bestFloorMarkerPairFirst = null;
        String bestFloorMarkerPairSecond = null;

        for (Pair<String, String> stringStringPair : floorMarkerPairList) {
            String floorMarkerPairFirst = stringStringPair.first();
            String floorMarkerPairSecond = stringStringPair.second();
            double distance = getDistance(distanceMatrix,startMarkerId, floorMarkerPairFirst)
                    + getDistance(distanceMatrix, floorMarkerPairSecond,endMarkerId);
            if (distance < minLength) {
                minLength = distance;
                bestFloorMarkerPairFirst = floorMarkerPairFirst;
                bestFloorMarkerPairSecond = floorMarkerPairSecond;
            }
        }
        if (minLength >= Double.POSITIVE_INFINITY) {
            logger.error("计算错误，请检查参数是否合规");
            throw new ExecuteException("计算错误，请检查参数是否合规");
        }
        return new Pair<>(bestFloorMarkerPairFirst,
                bestFloorMarkerPairSecond);
    }
}
