package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date :Created in 下午3:03 2021/1/5
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class ConflictProcessMonitor {
    private String threadName;
    private String agvCode;
    private String agvLocation;
    private String anotherAgvCode;
    private String anotherAgvLocation;
    private Map<String, List<String>> agvSidePaths;
    private String status;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
