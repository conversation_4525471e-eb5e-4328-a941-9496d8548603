package com.youibot.agv.scheduler.engine.util;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * @Date :Created in 下午8:21 2021/2/15
 * @Description :
 * @Modified By :
 * @Version :
 */
public class LogExceptionStackUtil {
    /**
     * @param e
     * @功能说明:在日志文件中，打印异常堆栈
     * @return:String
     */
    public static String LogExceptionStack(Throwable e) {
        StringWriter errorsWriter = new StringWriter();
        e.printStackTrace(new PrintWriter(errorsWriter));
        return errorsWriter.toString();
    }
}
