package com.youibot.agv.scheduler.engine.pathplan.entity;

import com.youibot.agv.scheduler.entity.SidePath;
import lombok.Data;

import java.util.LinkedList;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:23 19-11-8
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
public class MarkerPathResult {
	//导航点路径
	LinkedList<String> markerPath;
	//路径长度，会分步更新
	Double length;
	//记录规划路径后机器人的起始边
	SidePath sidePath;
	public MarkerPathResult(){
		markerPath = new LinkedList<>();
		length = Double.MAX_VALUE;
	}
}
