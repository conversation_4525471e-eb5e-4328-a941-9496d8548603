package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.infrared.InfraredApiService;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.ActionConstant.*;
import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.RESULT_TYPE_IMAGE;

/**
 * 红外热像仪
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/7 10:13
 */
@Service
@Scope("prototype")
public class InfraredAction extends ImageAction {

    private static final Logger LOGGER = LoggerFactory.getLogger(InfraredAction.class);

    @Autowired
    private InfraredApiService infraredApiService;

    @Override
    public Map<String, Object> execute(Vehicle vehicle) throws IOException {
        LOGGER.debug("mission work name : " + missionWorkAction.getName() + " start execute.");
        super.vehicle = vehicle;
        JSONObject paramJson = super.getParamJson();
        JSONObject resultMap = new JSONObject();

        if (INFRARED_PARAM_SETTING_YES.equals(paramJson.getInteger("paramSetting"))) {
            infraredApiService.settingParameter(vehicle.getIp(), paramJson);//设置摄像头参数
        }

        if (INFRARED_PRESET_POINT_SETTING_YES.equals(paramJson.getInteger("presetPointSetting"))) {
            JSONObject presetPointParam = new JSONObject();
            presetPointParam.put("dwPTZPresetCmd", paramJson.get("dwPTZPresetCmd"));
            presetPointParam.put("id", paramJson.getString("id"));
            presetPointParam.put("dwPresetIndex", paramJson.get("dwPresetIndex"));
            infraredApiService.settingPresetPoint(vehicle.getIp(), presetPointParam);//设置预置点参数
        }


        if (INFRARED_THERMOMETER_YES.equals(paramJson.getInteger("thermometer"))) {
            JSONObject thermometerParam = new JSONObject();
            thermometerParam.put("id", paramJson.getString("id"));
            Map<String, Object> thermometerMap = infraredApiService.thermometer(vehicle.getIp(), thermometerParam);
            LOGGER.debug("红外测温返回数据：" + thermometerMap);
            resultMap.put("highestTemp", thermometerMap.get("heightestTemp"));
            resultMap.put("lowestTemp", thermometerMap.get("lowestTemp"));
        }

        Integer photoCount = paramJson.getInteger("photoCount");
        if (photoCount != null && photoCount > 0) {
            JSONObject photographParam = new JSONObject();
            photographParam.put("id", paramJson.getString("id"));
            JSONArray imageUrlArr = new JSONArray();
            //根据count发送拍照指令
            for (int num = 1; num <= photoCount; num++) {
                Map<String, Object> dataMap = infraredApiService.photograph(vehicle.getIp(), photographParam);
                LOGGER.debug("红外拍照返回数据：" + dataMap);
                String base64 = (String) dataMap.get("base64");
                //将base64转化成图片保存在项目中,得到图片的存储路径
                String imageFilePath = super.saveImage(base64, AGVPropertiesUtils.getString("IMAGE_SAVE_PATH.INFRARED_FOLDER"));
                //将路径添加在数组中
                imageUrlArr.add(imageFilePath);
            }
            resultMap.put("imageUrl", imageUrlArr);
            resultMap.put("resultDataType", RESULT_TYPE_IMAGE);
            LOGGER.debug("mission action name : " + missionWorkAction.getName() + ", result data : " + resultMap);
        }
        return resultMap;
    }

    @Override
    public String getAPICode() {
        return null;
    }
}
