package com.youibot.agv.scheduler.engine.service.mechanics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/3/31 19:14
 */
@Service
public class DefaultLiftingPlatformApiService extends DefaultApiService implements LiftingPlatformApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultLiftingPlatformApiService.class);

    @Value("${AGV_API_CODE.LIFTING_PLATFORM}")
    private String liftingPlatformApiCode;

    @Override
    public Map<String, Object> control(String ip, Map<String, Object> param) throws IOException, InterruptedException {
        LOGGER.debug("ip : " + ip + " lifting platform send data : " + JSON.toJSONString(param));
        ActionUtils.sendInstruction(ip, liftingPlatformApiCode, JSONObject.toJSONString(param));
        //查看执行状态
        Map<String, Object> resultMap = ActionUtils.checkActionStatus(ip, (String) param.get("id"));
        return (JSONObject) resultMap.get("feedback");
    }
}
