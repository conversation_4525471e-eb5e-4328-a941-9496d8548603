package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.youibot.agv.scheduler.entity.SidePath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.youibot.agv.scheduler.constant.PathPlanConstant.*;

/**
 * <AUTHOR>
 * @Date :Created in 11:51 2020/8/7
 * @Description :加权有向边
 * @Modified By :
 * @Version :
 */
public final class DirectedEdge implements Serializable {

    private static final Logger logger = LoggerFactory.getLogger(DirectedEdge.class);

    //private static PathPlanService pathPlanService = (PathPlanService) ApplicationUtils.getBean("pathPlanServiceImpl");

    //边的起点，MarkerId
    private final String a;
    //边的终点
    private final String b;
    private final String aCode;
    private final String bCode;
    private final String sidePathId;
    private final String pathId;
    //边的权重
    private final Double weightFixed;
    private volatile Double weightUser = 0D;
    private volatile Double weightAuto = 0D;

    private final double inAngle;
    private final double outAngle;
    private double v;//线速度
    private double w;//角速度
    private double length;//路径长度

    private ConcurrentHashMap<String, Double> agvPosition = new ConcurrentHashMap<>();
    //在该路径上被阻塞的AGV类型,其它的AGV被运行通行
    private List<String> jamAgvType = new CopyOnWriteArrayList<>();

    public DirectedEdge(SidePath sidePath, String aCode, String bCode, double v, double w, List<String> jamAgvTypes) {
        this.aCode = aCode;
        this.bCode = bCode;
        this.a = sidePath.getStartMarkerId();
        this.b = sidePath.getEndMarkerId();
        this.sidePathId = sidePath.getId();
        this.pathId = sidePath.getPathId();
        this.length = sidePath.getLength();
        this.inAngle = sidePath.getInAngle();
        this.outAngle = sidePath.getOutAngle();
        this.v = Math.abs(v);
        this.w = Math.abs(w);
        Double weightRatio = sidePath.getWeightRatio() != null ? sidePath.getWeightRatio() : 1.0;
        this.weightFixed = Math.abs(2 * length * weightRatio / v);
        if (jamAgvTypes != null) {
            this.jamAgvType.addAll(jamAgvTypes);
        }
    }

    @Override
    public String toString() {
        return String.format("%s -> %s:(%f,%f,%f)", aCode, bCode, weightFixed, weightUser, weightAuto);
    }

    public Double getTempWeight() {
        return weightFixed + weightUser + weightAuto;
    }

    public Double getOriginWeight() {
        return weightFixed;
    }


    public Double getWeightUser() {
        return weightUser;
    }

    public Double getWeightAuto() {
        return weightAuto;
    }

    public void modifyUserWeight(String type, Double userWeight) {
        synchronized (this) {
            if (PLUS_WEIGHT.equals(type)) {
                this.weightUser += userWeight;
            } else if (MINUS_WEIGHT.equals(type)) {
                this.weightUser -= userWeight;
                if (this.weightUser < 0) {
                    this.weightUser = 0D;
                }
            } else if (MULTIPLY_WEIGHT.equals(type)) {
                this.weightUser *= userWeight;
            } else if (RESET_WEIGHT.equals(type)) {
                this.weightUser = 0D;
            } else if (SET_WEIGHT.equals(type)) {
                this.weightUser = userWeight;
            }
        }
    }

    public void modifyAutoWeight(String type, Double autoWeight) {
        synchronized (this) {
            if (PLUS_WEIGHT.equals(type)) {
                this.weightAuto += autoWeight;
            } else if (MINUS_WEIGHT.equals(type)) {
                this.weightAuto -= autoWeight;
                if (this.weightAuto < 0) {
                    this.weightAuto = 0D;
                }
            } else if (MULTIPLY_WEIGHT.equals(type)) {
                this.weightAuto *= autoWeight;
            } else if (RESET_WEIGHT.equals(type)) {
                this.weightAuto = 0D;
            }
        }
    }

    //true:被当前路径阻塞的agv类型包含，不能通过此路径;false:能通过此路径
    public boolean containedJamAgvType(String agvType) {
        //return false if agvtype is null. agvType为空时，给该AGV放行
        if (agvType == null) return false;
        return jamAgvType.contains(agvType);
    }

    public void addAgvLocation(String agvCode, Double t) {
        agvPosition.put(agvCode, t);
        //pathPlanService.addStartNodeOfChangedEdge(a);
    }

    public void removeAgvLocation(String agvCode) {
        agvPosition.remove(agvCode);
        //pathPlanService.addStartNodeOfChangedEdge(a);
    }

    public String getACode() {
        return aCode;
    }

    public String getBCode() {
        return bCode;
    }

    public String getSidePathId() {
        return sidePathId;
    }

    public String getPathId() {
        return pathId;
    }

    public String getA() {
        return a;
    }

    public String getB() {
        return b;
    }

    public double getLength() {
        return length;
    }

    public double getV() {
        return v;
    }

    public double getW() {
        return w;
    }

    public double getInAngle() {
        return inAngle;
    }

    public double getOutAngle() {
        return outAngle;
    }

    public ConcurrentHashMap<String, Double> getAgvPosition() {
        return agvPosition;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof DirectedEdge))
            return false;
        DirectedEdge that = (DirectedEdge) o;
        return Objects.equals(a, that.a) &&
                Objects.equals(b, that.b);
        //&&Objects.equals(weightFixed, that.weightFixed);
    }

    @Override
    public int hashCode() {
        return Objects.hash(a, b);
    }

}
