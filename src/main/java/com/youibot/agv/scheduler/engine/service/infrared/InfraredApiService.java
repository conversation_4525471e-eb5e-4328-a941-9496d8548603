package com.youibot.agv.scheduler.engine.service.infrared;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/7 10:14
 */
public interface InfraredApiService {

    /**
     * 设置红外热像仪参数
     *  1.查询红外热像仪参数
     *  2.在查询得到的参数里替换用户修改的参数
     *  3.发送修改参数指令
     * @param ip
     * @param param
     */
    Map<String, Object> settingParameter(String ip, Map<String, Object> param) throws IOException;

    /**
     * 设置红外热像仪预置点参数
     * @param ip
     * @param param
     * @throws IOException
     */
    Map<String, Object> settingPresetPoint(String ip, Map<String, Object> param) throws IOException;

    /**
     * 查询红外热像仪参数
     * @param ip
     * @param param
     */
    Map<String, Object> queryParameter(String ip, Map<String, Object> param) throws IOException;

    /**
     * 红外热像仪拍照
     * @param ip
     */
    Map<String, Object> photograph(String ip, Map<String, Object> param) throws IOException;

    /**
     * 红外热像仪测温
     * @param ip
     * @return
     * @throws IOException
     */
    Map<String, Object> thermometer(String ip, Map<String, Object> param) throws IOException;
}
