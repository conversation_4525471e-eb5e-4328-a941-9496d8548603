package com.youibot.agv.scheduler.engine.service.relocation;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-15 11:46
 */
public interface RelocationApiService {

    /**
     * 插入系统
     * @param ip
     * @return
     * @throws IOException
     * @throws InterruptedException
     */
    void insertSystem(String ip) throws IOException, InterruptedException;

    /**
     * 手动重定位
     * @param ip
     * @param param
     * @return
     */
    void manualRelocation(String ip, Map<String, Object> param) throws IOException;

    /**
     * 自动重定位
     * @param ip
     * @return
     */
    void autoRelocation(String ip) throws IOException;

    /**
     * 检测对接点
     * @param ip
     * @param feature_type  特征物体的形状：3、充电桩对接点  4、V形特征对接点  5、反光条特征对接点
     * @return
     */
    Map<String, Object> checkDockingPoint(String ip, Integer feature_type) throws IOException;

    /**
     * 录制初始位置
     * @param ip
     * @return
     */
    Map<String, Object> recordHomeMarker(String ip) throws IOException;

}
