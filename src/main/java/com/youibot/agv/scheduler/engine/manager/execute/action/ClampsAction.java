package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.ActionUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Map;

/**
 * 夹抱机器人（左右侧进出料）
 *
 * @Author：yangpeilin
 * @Date: 2020/6/19 15:06
 */
@Service
@Scope("prototype")
public class ClampsAction extends DefaultAction {

    Logger logger = LoggerFactory.getLogger(ClampsAction.class);


    @Override
    protected Map<String, Object> sendCommand() throws IOException, InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        JSONObject paramJson = this.getParamJson();
        Integer operationType = paramJson.getInteger("actionType");
        if (StringUtils.isEmpty(operationType)) {
            throw new ExecuteException(ErrorEnum.MISSING_PARAMETER.code(), ErrorEnum.MISSING_PARAMETER.msg());
        }
        ActionUtils.sendInstruction(client, getAPICode(), paramJson.toJSONString());
        Map<String, Object> resultMap = ActionUtils.checkActionStatus(vehicle.getIp(), missionWorkActionId);
        return (JSONObject) resultMap.get("feedback");
    }

    @Override
    public String getAPICode() {
        return AGVPropertiesUtils.getString("AGV_API_CODE.CLAMPS_OPERATE");
    }
}
