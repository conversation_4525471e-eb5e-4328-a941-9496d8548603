package com.youibot.agv.scheduler.engine.service.manual;

import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 16:23
 */
@Service
public class DefaultManualApiService extends DefaultApiService implements ManualApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultManualApiService.class);

    @Value("${AGV_API_CODE.AGV_MOVE_CONTROLLER}")
    private String agvManualMoveApiCode;

    @Value("${AGV_API_CODE.SWITCH_MANUAL_CONTROL_MODE}")
    private String switchManualModeApiCode;// 切换至手动控制模式apiCode

    @Value("${AGV_SOCKET.PORT.CONTROL}")
    private Integer controlPort;

    @Override
    public Map<String, Object> openManual(String ip) throws IOException {
        Map<String, Object> resultMap = super.execute(ip, controlPort, switchManualModeApiCode, null);
        LOGGER.debug("ip: " + ip + " open manual success.");
        return resultMap;
    }

    @Override
    public void manualMove(AGVSocketClient client, Double x, Double y, Double angle) throws AGVResultException, IOException {
        Map<String, Object> agvSocketParamMap = new HashMap<>();//用户传递给agv的paramMap
        agvSocketParamMap.put("vx", x);
        agvSocketParamMap.put("vy", y);
        agvSocketParamMap.put("vtheta", angle);
        super.execute(client, agvManualMoveApiCode, agvSocketParamMap);
    }
}
