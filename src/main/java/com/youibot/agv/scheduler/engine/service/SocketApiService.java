package com.youibot.agv.scheduler.engine.service;

import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;

import java.io.IOException;

/**
 * <AUTHOR> E-mail:song<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019-05-14 16:45
 */
public abstract class SocketApiService implements ApiService {

    public AGVSocketClient createClient(String ip, int port) throws IOException {
        AGVSocketClient client = new AGVSocketClient(ip, port);
        client.create();
        return client;
    }
}
