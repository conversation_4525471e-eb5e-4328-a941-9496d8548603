package com.youibot.agv.scheduler.engine.service.connectingFrame;

import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.io.IOException;
import java.util.Map;

public interface ConnectingFrameApiService {
    /**
     * 日月光接驳架
     * @param param
     * @return
     * @throws IOException
     */
    Map<String, Object> connectingFrame(String ip, Map<String, Object> param) throws IOException, InterruptedException;
}
