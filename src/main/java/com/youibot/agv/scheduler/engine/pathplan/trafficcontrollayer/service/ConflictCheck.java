package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.service;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 9/1/21 4:18 PM
 */
@Service
public interface ConflictCheck {

    /**
     * 获得的所有对向冲突的机器人列表。
     * @return
     */
    List<List<String>> getOppositeConflicts();

    /**
     * 获得所有环型冲突的机器人列表。
     * @return
     */
    List<List<String>> getRingConflicts();

    /**
     * 获取所有阻挡冲突的机器人列表。
     * @return
     */
    List<String> getObstructConflicts();
}
