package com.youibot.agv.scheduler.engine.entity;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Mq数据缓存
 *
 * @author: huangguanxin
 * @date: 2021-01-19 10:11
 */
public class MqResourceCache {

    // key=agv topic ,value=max messageId
    public volatile static ConcurrentHashMap<String, Integer> agvMaxMessageMap = new ConcurrentHashMap<>();

    // key=missionWorkId ,value=max messageId
    public volatile static ConcurrentHashMap<String, Integer> agvSidePathMaxMessageMap = new ConcurrentHashMap<>();


}
