package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool;

import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.MarkerPathResource;
import com.youibot.agv.scheduler.entity.Marker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date :Created in 下午2:31 2020/10/15
 * @Description :
 * @Modified By :
 * @Version :
 */
@Component
public class MarkerPathResourcePool extends PathResourcePool<MarkerPathResource> {

    private static final Logger logger = LoggerFactory.getLogger(MarkerPathResourcePool.class);

    private Map<String, String> markerIdToResourceId = new ConcurrentHashMap<>();

    public void addMarkerResources(Collection<Marker> markers) {
        if (markers == null) return;
        markers.forEach(marker -> {
            //将所有的路径资源加入到资源缓存中
            MarkerPathResource markerPathResource = new MarkerPathResource();
            markerPathResource.setId(marker.getId());
            markerPathResource.setMarkerId(marker.getId());
            markerPathResource.setMarkerCode(marker.getCode());
            markerIdToResourceId.put(marker.getId(), markerPathResource.getId());
            super.attach(markerPathResource);
        });
    }

    public void removeMarkerResources(Collection<Marker> markers) {
        if (markers == null) return;
        markers.forEach(marker -> {
            super.detach(marker.getId());
        });
    }

    public Set<String> queryMarkerPathResourceIdsByMarkerIds(Collection<String> markerIds) {
        if (markerIds == null) return null;
        Set<String> set = new HashSet<>();
        for (String markerId : markerIds) {
            if (markerId == null) continue;
            String resourceId = markerIdToResourceId.get(markerId);
            if (resourceId != null) {
                set.add(resourceId);
            } else {
                logger.error("MarkerPathResourcePool can not find markerId:[{}]", markerId);
            }
        }
        return set;
    }

    public String queryMarkerPathResourceIdByMarkerId(String markerId) {
        if (markerId == null) return null;
        return markerIdToResourceId.get(markerId);
    }
}
