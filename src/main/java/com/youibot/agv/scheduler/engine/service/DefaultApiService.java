package com.youibot.agv.scheduler.engine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.manager.socket.DataProtocol;
import com.youibot.agv.scheduler.engine.util.AGVResultInfoUtils;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 16:23
 */
public abstract class DefaultApiService extends SocketApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultApiService.class);

    @Override
    public Map<String, Object> execute(String ip, Integer port, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException {
        if (StringUtils.isEmpty(ip) || port == null || StringUtils.isEmpty(apiCode)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        AGVSocketClient agvSocketClient;
        try {
            agvSocketClient = super.createClient(ip, port);
        } catch (IOException e) {
            LOGGER.error("agv connection error, agv ip:" + ip + ", port:" + port);
            throw new IOException(MessageUtils.getMessage("socket.connection_failed") + " ip:" + ip + ", port:" + port);
        }
        Map<String, Object> resultMap = this.execute(agvSocketClient, apiCode, dataMap);
        agvSocketClient.stop();
        return resultMap;
    }

    @Override
    public Map<String, Object> execute(AGVSocketClient client, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException {
        DataProtocol protocolOperation = new DataProtocol(apiCode, JSON.toJSONString(dataMap));
        DataProtocol receiveData = client.sendAndReceive(protocolOperation);
        return AGVResultInfoUtils.getDataMap(receiveData.getData());
    }

    @Override
    public JSONArray executeByResultList(String ip, Integer port, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException {
        if (StringUtils.isEmpty(ip) || port == null || StringUtils.isEmpty(apiCode)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        AGVSocketClient agvSocketClient;
        try {
            agvSocketClient = super.createClient(ip, port);
        } catch (IOException e) {
            LOGGER.error("agv connection error, agv ip:" + ip + ", port:" + port);
            throw new IOException(MessageUtils.getMessage("socket.connection_failed") + " ip:" + ip + ", port:" + port);
        }
        JSONArray jsonArray = this.executeByResultList(agvSocketClient, apiCode, dataMap);
        agvSocketClient.stop();
        return jsonArray;
    }

    @Override
    public JSONArray executeByResultList(AGVSocketClient client, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException {
        DataProtocol protocolOperation = new DataProtocol(apiCode, JSON.toJSONString(dataMap));
        DataProtocol receiveData = client.sendAndReceive(protocolOperation);
        return AGVResultInfoUtils.getDataList(receiveData.getData());
    }

    @Override
    public String executeByResultStr(String ip, Integer port, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException {
        if (StringUtils.isEmpty(ip) || port == null || StringUtils.isEmpty(apiCode)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        AGVSocketClient agvSocketClient;
        try {
            agvSocketClient = super.createClient(ip, port);
        } catch (IOException e) {
            LOGGER.error("agv connection error, agv ip:" + ip + ", port:" + port);
            throw new IOException(MessageUtils.getMessage("socket.connection_failed") + " ip:" + ip + ", port:" + port);
        }
        String data = this.executeByResultStr(agvSocketClient, apiCode, dataMap);
        agvSocketClient.stop();
        return data;
    }

    @Override
    public String executeByResultStr(AGVSocketClient client, String apiCode, Map<String, Object> dataMap) throws AGVResultException, IOException {
        DataProtocol protocolOperation = new DataProtocol(apiCode, JSON.toJSONString(dataMap));
        DataProtocol receiveData = client.sendAndReceive(protocolOperation);
        return AGVResultInfoUtils.getDataStr(receiveData.getData());
    }

}
