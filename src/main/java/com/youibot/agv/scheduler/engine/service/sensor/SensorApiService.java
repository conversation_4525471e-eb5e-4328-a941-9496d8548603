package com.youibot.agv.scheduler.engine.service.sensor;

import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2019/10/16 16:05
 */
public interface SensorApiService {

    /**
     * 查询录制的地图详情
     * @param client
     * @return
     */
    Map<String, Object> queryLaserData(AGVSocketClient client) throws IOException;

    /**
     * 查询录制的地图详情
     * @param ip
     * @return
     * @throws IOException
     */
    Map<String, Object> queryLaserData(String ip) throws IOException;
}
