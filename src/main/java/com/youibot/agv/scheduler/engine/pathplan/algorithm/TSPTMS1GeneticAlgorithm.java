package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil;
import com.youibot.agv.scheduler.exception.ExecuteException;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

import static com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil.getDistance;

/**
 * <AUTHOR>
 * @Date :Created in 下午3:18 2019/12/9
 * @Description :使用遗传算法解决TSP问题，用于物料管理系统
 * @Modified By :
 * @Version :1.0.0
 */
public class TSPTMS1GeneticAlgorithm {

    private static final Logger logger = LoggerFactory.getLogger(TSPTMS1GeneticAlgorithm.class);

    private Map<String, Map<String, Double>> distanceMatrix;                  //距离矩阵

    private List<String> loadMaterial;
    private List<String> unloadMaterial;

    private int T = 80;                                 //运行代数
    private int M = 10;                                 //种群规模
    private double pCorss = 0.4d;                       //交叉概率
    private double pMutate = 0.2d;                      //变异概率
    private Double bestDistance = Double.POSITIVE_INFINITY;     //最佳长度
    private Chromosome bestPath;                             //最佳路径
    private int loadSize;
    private int unloadSize;
    private Chromosome[] oldPopulation;                      //父代种群
    private Chromosome[] newPopulation;                      //子代种群
    private double[] Pi;                                //个体的累计概率
    private double[] fitness;                           //个体的适应度
    private Random random;                              //随机对象

    @Data
    private class Chromosome implements Cloneable {
        private String[] load;
        private String[] unload;

        public Chromosome() {
        }

        public Chromosome(int loadSize, int unloadSize) {
            load = new String[loadSize];
            unload = new String[unloadSize];
        }

        public int size() {
            return load.length + unload.length;
        }

        public String[] toStringArray() {
            String[] stringArray = new String[this.size()];
            for (int i = 0; i < load.length; i++) {
                stringArray[i] = load[i];
            }
            for (int i = 0; i < unload.length; i++) {
                stringArray[load.length + i] = unload[i];
            }
            return stringArray;
        }

        public void swap(Chromosome other, int k) {
            if (other == null || k < 0 || k >= load.length + unload.length) {
                throw new ExecuteException("传入的参数错误。");
            }
            if (k < load.length) {
                String[] otherLoad = other.getLoad();
                String temp = otherLoad[k];
                otherLoad[k] = this.load[k];
                this.load[k] = temp;
                other.setLoad(otherLoad);
            } else if (k < load.length + unload.length) {
                int h = k - load.length;
                String[] otherLoad = other.getLoad();
                String temp = otherLoad[h];
                otherLoad[h] = this.load[h];
                this.load[h] = temp;
                other.setLoad(otherLoad);
            }
        }

        @Override
        public Chromosome clone() {
            try {
                return (Chromosome) super.clone();
            } catch (CloneNotSupportedException e) {
                logger.error("Clone Chromosome.", e.getMessage());
                throw new AssertionError();
            }
        }
    }

    public void GAInit(List<String> loadMaterial, List<String> unloadMaterial) {
        this.loadMaterial = loadMaterial;
        this.unloadMaterial = unloadMaterial;
        loadSize = loadMaterial.size();
        unloadSize = unloadMaterial.size();
        initPopulation(M, loadSize, unloadSize);
        bestPath = new Chromosome(loadSize, unloadSize);
        Pi = new double[M];
        fitness = new double[M];
        random = new Random(System.currentTimeMillis());
        distanceMatrix = DistanceMatrixUtil.getDistanceMatrix();
    }

    private void initPopulation(int M, int loadSize, int unloadSize) {
        oldPopulation = new Chromosome[M];
        newPopulation = new Chromosome[M];
        //for (int i = 0; i < M; i++) {
        //    oldPopulation[i] = new Individual(loadSize, unloadSize);
        //    newPopulation[i] = new Individual(loadSize, unloadSize);
        //}
    }

    /**
     *
     */
    public String[] run() {
        //初始化种群
        initGroup();
        //计算初始化种群适应度，Fitness[max]
        for (int k = 0; k < M; k++) {
            fitness[k] = evaluate(oldPopulation[k]);
        }
        //计算初始化种群中各个个体的累积概率，Pi[MAX]
        countRate();
        for (int t = 0; t < T; t++) {
            evolution();
            // 将新种群newGroup复制到旧种群oldGroup中，准备下一代进化
            for (int k = 0; k < M; k++) {
                oldPopulation[k] = newPopulation[k];
            }
            // 计算种群适应度
            for (int k = 0; k < M; k++) {
                fitness[k] = evaluate(oldPopulation[k]);
            }
            // 计算种群中各个个体的累积概率
            countRate();
        }

        return bestPath.toStringArray();
    }

    private String getLoadMaterialIndex(int k) {
        return loadMaterial.get(k);
    }

    private String getUnloadMaterialIndex(int k) {
        return unloadMaterial.get(k);
    }

    private List<Integer> generateRandomArray(int size) {
        List<Integer> list = new ArrayList<>(size);
        int range = size;
        Random random = new Random(System.currentTimeMillis());
        while (list.size() < size) {
            Integer tmp = random.nextInt(65535) % range;
            if (!list.contains(tmp)) {
                list.add(tmp);
            }
        }
        return list;
    }

    /**
     * 初始化种群
     */
    void initGroup() {
        //种群数
        for (int k = 0; k < M; k++) {
            //染色体长度
            String[] load = new String[loadSize];
            List<Integer> list1 = generateRandomArray(loadSize);
            for (int i = 0; i < loadSize; i++) {
                load[i] = getLoadMaterialIndex(list1.get(i));
            }

            String[] unload = new String[unloadSize];
            List<Integer> list2 = generateRandomArray(unloadSize);
            for (int i = 0; i < unloadSize; i++) {
                unload[i] = getUnloadMaterialIndex(list2.get(i));
            }
            Chromosome chromosome = new Chromosome();
            chromosome.setLoad(load);
            chromosome.setUnload(unload);
            oldPopulation[k] = chromosome;
        }
    }

    /**
     * 计算某个染色体的实际距离作为染色体适应度
     */
    public double evaluate(Chromosome chromosome) {
        double len = 0;
        try {
            String[] load = chromosome.getLoad();
            for (int i = 0; i < loadSize - 1; i++) {
                len += getDistance(distanceMatrix, load[i], load[i + 1]);
            }
            String[] unload = chromosome.getUnload();
            for (int i = 0; i < unloadSize - 1; i++) {
                len += getDistance(distanceMatrix, unload[i], unload[i + 1]);
            }
            len += getDistance(distanceMatrix, load[loadSize - 1], unload[0]);
        } catch (Exception e) {
            logger.error("计算适应度报错，请检查输入的站点是否合法，染色体:chromosome = [{}]", chromosome);
        }
        return len;
    }

    /**
     * 计算中每个个体的累积概率
     */
    void countRate() {
        //适应度总和
        double sumFitness = 0;
        for (int k = 0; k < M; k++) {
            sumFitness += fitness[k];
        }
        Pi[0] = fitness[0] / sumFitness;
        for (int k = 1; k < M; k++) {
            Pi[k] = fitness[k] / sumFitness + Pi[k - 1];
        }
    }

    /**
     * 种群进化
     */
    private void evolution() {
        selectBestChild();
        selectChild();
        for (int k = 0; k < M; k += 2) {
            //交叉概率
            double r = random.nextDouble();
            if (r < pCorss) {
                orderCrossover(k, k + 1);
            } else {
                //变异概率
                r = random.nextDouble();
                if (r < pMutate) {
                    variation(k);
                }
                //变异概率
                r = random.nextDouble();
                if (r < pMutate) {
                    variation(k + 1);
                }
            }
        }
    }

    /**
     * 挑选适应度最高的个体
     */
    private void selectBestChild() {
        double maxevaluation = fitness[0];
        int maxid = 0;
        for (int k = 1; k < M; k++) {
            if (maxevaluation > fitness[k]) {
                maxevaluation = fitness[k];
                maxid = k;
            }
        }
        if (bestDistance > maxevaluation) {
            bestDistance = maxevaluation;
            bestPath = oldPopulation[maxid];
        }
        //将当代种群中适应度最高的染色体K复制到新种群中的第一位
        copyGh(0, maxid);
    }

    /**
     * 复制染色体
     */
    private void copyGh(int k, int kk) {
        newPopulation[k] = oldPopulation[kk];
    }

    /**
     * 轮盘赌挑选子代个体
     */
    private void selectChild() {
        //挑选概率
        double ran1;
        int selectId;
        for (int k = 1; k < M; k++) {
            ran1 = random.nextDouble();
            int i;
            for (i = 0; i < M; i++) {
                if (ran1 <= Pi[i]) {
                    break;
                }
            }
            selectId = i;
            copyGh(k, selectId);
        }
    }

    /**
     * 顺序交叉
     */
    private void orderCrossover(int k1, int k2) {
        Chromosome chromosome1 = newPopulation[k1];
        Chromosome chromosome2 = newPopulation[k2];

        int size = loadSize + unloadSize;
        int ran1 = random.nextInt(65535) % size;
        int ran2 = random.nextInt(65535) % size;
        while (ran1 == ran2) {
            ran2 = random.nextInt(65535) % size;
        }
        if (ran1 > ran2) {
            int temp = ran1;
            ran1 = ran2;
            ran2 = temp;
        }

        for (int i = ran1; i <= ran2; i++) {
            chromosome1.swap(chromosome2, i);
        }
        //将顺序交叉的结果保存到newnewPopulation中
        newPopulation[k1] = chromosome1;
        newPopulation[k2] = chromosome2;
    }

    /**
     * 随机多次变异
     */
    private void variation(int k) {
        //变异次数
        int loadCount = random.nextInt(65535) % loadSize;
        String[] load = newPopulation[k].getLoad();
        for (int i = 0; i < loadCount; i++) {
            int ran1 = random.nextInt(65535) % loadCount;
            int ran2 = random.nextInt(65535) % loadCount;
            while (ran1 == ran2) {
                ran2 = random.nextInt(65535) % loadCount;
            }
            String temp = load[ran1];
            load[ran1] = load[ran2];
            load[ran2] = temp;
        }
        int unloadCount = random.nextInt(65535) % unloadSize;
        String[] unload = newPopulation[k].getUnload();
        for (int i = 0; i < unloadCount; i++) {
            int ran1 = random.nextInt(65535) % unloadCount;
            int ran2 = random.nextInt(65535) % unloadCount;
            while (ran1 == ran2) {
                ran2 = random.nextInt(65535) % unloadCount;
            }
            String temp = unload[ran1];
            unload[ran1] = unload[ran2];
            unload[ran2] = temp;
        }
        newPopulation[k].setLoad(load);
        newPopulation[k].setUnload(unload);
    }

}
