package com.youibot.agv.scheduler.engine.service.relocation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 16:23
 */
@Service
public class LaserRelocationApiService extends DefaultRelocationApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LaserRelocationApiService.class);

    @Value("${AGV_SOCKET.PORT.CONTROL}")
    private Integer controlPort;

    @Value("${AGV_API_CODE.MANUAL_RELOCATION}")
    private String manualRelocationApiCode;//手动重定位apiCode

    @Value("${AGV_API_CODE.AUTO_RELOCATION}")
    private String autoRelocationApiCode;//自动重定位apiCode

    @Value("${AGV_API_CODE.CHECK_DOCKING_POINT}")
    private String checkDockingPointApiCode;//检测对接点apiCode

    @Value("${AGV_API_CODE.HOME_MARKER_RECORD}")
    private String homeMarkerRecordApiCode;//录制初始点apiCode

    /**
     * 手动重定位
     * @param ip
     * @param param
     * @return
     */
    @Override
    public void manualRelocation(String ip, Map<String, Object> param) throws IOException {
        LOGGER.debug("ip: " + ip + ", 手动重定位 send data: " + param);
        super.execute(ip, controlPort, manualRelocationApiCode, param);
        LOGGER.debug("ip: " + ip + " 手动重定位成功");
    }

    /**
     * 自动重定位
     * @param ip
     * @return
     */
    @Override
    public void autoRelocation(String ip) throws IOException {
        LOGGER.debug("ip: " + ip + " 自动重定位");
        super.execute(ip, controlPort, autoRelocationApiCode, null);
        LOGGER.debug("ip: " + ip + " 自动重定位成功");
    }

    /**
     * 检测对接点
     * @param ip
     * @param feature_type  特征物体的形状：3、充电桩对接点  4、V形特征对接点  5、反光条特征对接点
     * @return
     * @throws IOException
     */
    @Override
    public Map<String, Object> checkDockingPoint(String ip, Integer feature_type) throws IOException {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("feature_type", feature_type);
        LOGGER.debug("ip: " + ip + " checkDockingPoint, send data: " + dataMap);
        return super.execute(ip, controlPort, checkDockingPointApiCode, dataMap);
    }

    /**
     * 录制初始位置
     * @param ip
     * @return
     * @throws IOException
     */
    @Override
    public Map<String, Object> recordHomeMarker(String ip) throws IOException {
        LOGGER.debug("ip: " + ip + " 录制初始位置");
        return super.execute(ip, controlPort, homeMarkerRecordApiCode, null);
    }
}
