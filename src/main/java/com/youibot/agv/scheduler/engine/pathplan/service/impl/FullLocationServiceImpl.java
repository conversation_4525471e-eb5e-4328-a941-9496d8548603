package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.youibot.agv.scheduler.engine.pathplan.entity.Position;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.entity.ChargeScheduler;
import com.youibot.agv.scheduler.entity.WorkScheduler;
import com.youibot.agv.scheduler.service.ChargeSchedulerService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.WorkSchedulerService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;

/**
 * <AUTHOR> extends location service implation.
 * @Date :Created in 18:43 2020/8/7
 * @Description : 如果获取机器人的站点位置不存，并且在作业中或者任务中，则返回机器人最后一次
 * @Modified By :
 * @Version :
 */

@Service
public class FullLocationServiceImpl implements FullLocationService {

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private LocationService locationService;

    /**
     * 如果机器人的当前位置为空。则代表即不在路径上，也不在站点上。
     * 检测他是不是在充电中，如果在充电中，应该是在对接状态，获取他最后一个站点位置做为他的位置信息。
     *
     * @param vehicleId
     * @return
     */
    @Override
    public VehicleLocation getVehicleLocation(String vehicleId) {
        VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicleId);
        if (vehicleLocation != null) {
            return vehicleLocation;
        } else {
            Vehicle vehicle = vehiclePoolService.selectById(vehicleId);
            if (vehicle != null && !StringUtils.isEmpty(vehicle.getLastMarkerId())) {
                String lastMarkerId = vehicle.getLastMarkerId();
                /**
                 * 如果机器人的当前位置为空。则代表即不在路径上，也不在站点上。
                 * 检测他是不是在充电中，如果在充电中，应该是在对接状态，获取他最后一个站点位置做为他的位置信息。
                 */
                ChargeScheduler chargeScheduler = chargeSchedulerService.selectRunningByVehicle(vehicleId);
                WorkScheduler workScheduler = workSchedulerService.selectRunningByWork(vehicleId);
                if (chargeScheduler != null || workScheduler != null) {
                    VehicleLocation location = new VehicleLocation();
                    location.setMarker(markerService.selectById(vehicle.getAgvMapId(),lastMarkerId,false));
                    location.setUpdateTimeMillis(System.currentTimeMillis());
                    return location;
                }
            }
        }
        return null;
    }


    @Override
    public Position getAgvPosition(String agvCode) {
        return locationService.getAgvPosition(agvCode);
    }
}
