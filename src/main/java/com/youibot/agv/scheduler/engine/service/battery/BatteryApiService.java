package com.youibot.agv.scheduler.engine.service.battery;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/1/13 14:18
 */
public interface BatteryApiService {

    /**
     * 电池充电
     *
     * @param ip
     * @param param
     * @return
     * @throws IOException
     */
    Map<String, Object> batteryCharge(String ip, Map<String, Object> param) throws IOException, InterruptedException;

    /**
     * 新智能充电
     *
     * @param ip
     * @param param
     * @return
     * @throws IOException
     */
    Map<String, Object> newSmartCharge(String ip, Map<String, Object> param) throws IOException, InterruptedException;

    /**
     * 电池充电, 只发送充电指令, 不实时检测执行状态
     * @param ip
     * @param param
     * @return
     * @throws IOException
     */
    Map<String, Object> batteryChargeIgnoreStatus(String ip, Map<String, Object> param) throws IOException;
}
