package com.youibot.agv.scheduler.engine.service.config;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.YamlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 16:23
 */
@Service
public class DefaultConfigApiService extends DefaultApiService implements ConfigApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultConfigApiService.class);

    @Value("${AGV_SOCKET.PORT.CONFIG}")
    private Integer agvConfigPort;//连接agv的config端口号

    @Value("${AGV_API_CODE.QUERY_AGV_CONFIG_INFO}")
    private String queryAgvConfigApiCode;// 查询agv配置信息apiCode

    @Value("${AGV_API_CODE.SAVE_AGV_CONFIG_INFO}")
    private String saveAgvConfigApiCode;// 保存agv配置信息apiCode

    @Override
    public JSONObject queryAGVConfigInfo(String ip) throws AGVResultException, IOException {
        String yamlData = super.executeByResultStr(ip, agvConfigPort, queryAgvConfigApiCode, null);//调用接口,返回socket数据区中的data数据
//        LOGGER.debug("queryAgvConfigInfo resultData: " + yamlData);
        return YamlUtils.yamlToJson(yamlData);
    }

    @Override
    public void saveAGVConfigInfo(String ip, JSONObject dataJson) throws AGVResultException, IOException {
        JSONObject sendDataJson = new JSONObject();
        String dataYaml = YamlUtils.jsonToYaml(dataJson);
        sendDataJson.put("data", dataYaml);
        LOGGER.debug("saveAGVConfigInfo sendDataJson: " + sendDataJson);
        super.execute(ip, agvConfigPort, saveAgvConfigApiCode, sendDataJson);
    }
}
