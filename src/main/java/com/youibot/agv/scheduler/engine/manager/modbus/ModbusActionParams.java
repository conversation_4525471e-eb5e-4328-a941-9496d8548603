package com.youibot.agv.scheduler.engine.manager.modbus;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * modbus 动作参数
 * @author: huangguanxin
 * @date: 2020-10-19 20:16
 */
@Data
public class ModbusActionParams implements Serializable {
    private static String IS_LOCAL ="1";

   private String ip;

    private Integer port;

    private Integer slaveId;

    private String code;// 功能码

    private String isLocal;// 是否本地 1 是，0 不是

    private Integer startAddress;// 开始地址

    private Integer numberOfBits;// 读取长度

    private String value;//输入值 写操作

    private String checkValue;// 检查值，读操作

    private Long time_out;//超时时间 读操作

    private String condition;// 条件过滤 读操作

    public ModbusActionParams() {

    }

    public ModbusActionParams(JSONObject paramJson) {
        String isLocal = paramJson.getString("isLocal");
        this.isLocal = isLocal;
        if (IS_LOCAL.equals(isLocal)) {
            this.port = AGVPropertiesUtils.getInteger("MODBUS.LOCAL_PORT");
            this.ip = AGVPropertiesUtils.getString("MODBUS.LOCAL_IP");
        } else {
            this.ip = paramJson.getString("ip");
            this.port = paramJson.getInteger("port");
        }
        this.slaveId = paramJson.getInteger("slaveId");
        this.code = paramJson.getString("code");
        this.startAddress = paramJson.getInteger("startAddress");

        this.value = paramJson.getString("value");

        this.numberOfBits = paramJson.getInteger("numberOfBits");
        this.checkValue = paramJson.getString("checkValue");
        this.time_out = paramJson.getLong("timeOut");
        this.condition = paramJson.getString("condition");
    }




}
