package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.entity.Point;

import java.util.List;

/**
 * 图形碰撞检测算法
 *
 * <AUTHOR> E-mail:l<PERSON><PERSON><EMAIL>
 * @version CreateTime:2019/6/12 15:49
 */
public class MathematicalGraphicsAlgorithm {

    //检测点是否在多边形内
    //点在边上的情况视为不在多边形内
    public static synchronized boolean isPtInPoly(double x, double y, Point[] ps) {
        int iSum, iCount, iIndex;
        double dLon1 = 0, dLon2 = 0, dLat1 = 0, dLat2 = 0, dLon;
        if (ps.length < 3) {
            return false;
        }
        iSum = 0;

        iCount = ps.length;
        for (iIndex = 0; iIndex < iCount; iIndex++) {
            if (iIndex == iCount - 1) {
                dLon1 = ps[iIndex].getX();
                dLat1 = ps[iIndex].getY();
                dLon2 = ps[0].getX();
                dLat2 = ps[0].getY();
            } else {
                dLon1 = ps[iIndex].getX();
                dLat1 = ps[iIndex].getY();
                dLon2 = ps[iIndex + 1].getX();
                dLat2 = ps[iIndex + 1].getY();
            }
            // 以下语句判断A点是否在边的两端点的水平平行线之间，在则可能有交点，开始判断交点是否在左射线上
            if (((y >= dLat1) && (y < dLat2)) || ((y >= dLat2) && (y < dLat1))) {
                if (Math.abs(dLat1 - dLat2) > 0) {
                    //得到 A点向左射线与边的交点的x坐标：
                    dLon = dLon1 - ((dLon1 - dLon2) * (dLat1 - y)) / (dLat1 - dLat2);
                    // 如果交点在A点左侧（说明是做射线与边的交点），则射线与边的全部交点数加一：
                    if (dLon < x) {
                        iSum++;
                    }
                }
            }
        }
        if ((iSum % 2) != 0) {
            return true;
        }
        return false;
    }

    //检测火车agv的火车模型是否与多边形碰撞
    public static synchronized boolean isInPolygon(Point[] ps, List<Point> model) {
        for (Point point :model) {
            if(isPtInPoly(point.getX(),point.getY(),ps)){
                return true;
            }
        }
        return false;
    }

    //检测圆形是否和多边形碰撞
    public static synchronized boolean isInPolygon(Point[] ps, Double x, Double y, Double r) {
        int count = 128; //128个等分点
        double radians = (Math.PI / 180) * Math.round(360.0 / count); //弧度
        for (int i = 0; i < count; i++) {
            double x1 = x + r * Math.sin(radians * i);
            double y1 = y + r * Math.cos(radians * i);
            if (isPtInPoly(x1, y1, ps)) {
                return true;
            }
        }
        return false;
    }

    //点到多边形的距离
    public static synchronized Double pointPolygonDis(Point[] points, Double x, Double y) {
        if (points.length < 3) {
            return null;
        }
        Double min = Double.MAX_VALUE;
        for (int i = 0; i < points.length - 1; i++) {
            Double dis = pointLineDis(x, y, points[i].getX(), points[i].getY(), points[i + 1].getX(), points[i + 1].getY());
            if (dis <= min) {
                min = dis;
            }
        }
        Double dis = pointLineDis(x, y, points[0].getX(), points[0].getY(), points[points.length - 1].getX(), points[points.length - 1].getY());
        if (dis < min) {
            min = dis;
        }
        return min;
    }

    //输入一个点和一条线段输出该点与线段的距离
    private static synchronized double pointLineDis(double xx, double yy, double x1, double y1, double x2, double y2) {
        double a, b, c, ang1, ang2, ang, m;
        double result = 0;
        //分别计算三条边的长度
        a = Math.sqrt((x1 - xx) * (x1 - xx) + (y1 - yy) * (y1 - yy));
        if (a == 0) {
            return -1;
        }
        b = Math.sqrt((x2 - xx) * (x2 - xx) + (y2 - yy) * (y2 - yy));
        if (b == 0) {
            return -1;
        }
        c = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
        //如果线段是一个点则退出函数并返回距离
        if (c == 0) {
            result = a;
            return result;
        }
        //如果点(xx,yy到点x1,y1)这条边短
        if (a < b) {
            //如果直线段AB是水平线。得到直线段AB的弧度
            if (y1 == y2) {
                if (x1 < x2) {
                    ang1 = 0;
                } else {
                    ang1 = Math.PI;
                }
            } else {
                m = (x2 - x1) / c;
                if (m - 1 > 0.00001) {
                    m = 1;
                }
                ang1 = Math.acos(m);
                if (y1 > y2) {//直线(x1,y1)-(x2,y2)与折X轴正向夹角的弧度
                    ang1 = Math.PI * 2 - ang1;
                }
            }
            m = (xx - x1) / a;
            if (m - 1 > 0.00001) {
                m = 1;
            }
            ang2 = Math.acos(m);
            if (y1 > yy) {
                //直线(x1,y1)-(xx,yy)与折X轴正向夹角的弧度
                ang2 = Math.PI * 2 - ang2;
            }

            ang = ang2 - ang1;
            if (ang < 0) {
                ang = -ang;
            }

            if (ang > Math.PI) {
                ang = Math.PI * 2 - ang;
            }
            //如果是钝角则直接返回距离
            if (ang > Math.PI / 2) {
                return a;
            } else {
                return a * Math.sin(ang);
            }
        } else {
            //如果两个点的纵坐标相同，则直接得到直线斜率的弧度
            if (y1 == y2)
                if (x1 < x2) {
                    ang1 = Math.PI;
                } else {
                    ang1 = 0;
                }
            else {
                m = (x1 - x2) / c;
                if (m - 1 > 0.00001) {
                    m = 1;
                }
                ang1 = Math.acos(m);
                if (y2 > y1) {
                    ang1 = Math.PI * 2 - ang1;
                }
            }
            m = (xx - x2) / b;
            if (m - 1 > 0.00001) {
                m = 1;
            }
            //直线(x2-x1)-(xx,yy)斜率的弧度
            ang2 = Math.acos(m);
            if (y2 > yy) {
                ang2 = Math.PI * 2 - ang2;
            }
            ang = ang2 - ang1;
            if (ang < 0) {
                ang = -ang;
            }
            //交角的大小
            if (ang > Math.PI) {
                ang = Math.PI * 2 - ang;
            }
            //如果是对角则直接返回距离
            if (ang > Math.PI / 2) {
                return b;
            } else {
                //如果是锐角，返回计算得到的距离
                return b * Math.sin(ang);
            }
        }
    }

    //返回agv的安全半径
    public static synchronized Double agvSafeRadius(Double agvLong, Double agvWidth, Double agvBrakeDistance) {
        Double agv_real_r = Math.sqrt(agvLong * agvLong + agvWidth * agvWidth) / 2;
        return agv_real_r + agvBrakeDistance;
    }

    //返回agv的真实半径
    public static synchronized Double agvRealRadius(Double agvLong, Double agvWidth) {
        Double agv_real_r = Math.sqrt(agvLong * agvLong + agvWidth * agvWidth) / 2;
        return agv_real_r;
    }

    //算出两条线段的夹角
    public static synchronized Double angle(Point o, Point s, Point e) {
        Double cosfi = 0D, fi = 0D, norm = 0D;
        Double dsx = s.getX() - o.getX();
        Double dsy = s.getY() - o.getY();
        Double dex = e.getX() - o.getX();
        Double dey = e.getY() - o.getY();

        cosfi = dsx * dex + dsy * dey;
        norm = (dsx * dsx + dsy * dsy) * (dex * dex + dey * dey);
        cosfi /= Math.sqrt(norm);
        if (cosfi >= 1.0) {
            return 0D;
        }
        if (cosfi <= -1.0) {
            return 180D;
        }
        fi = Math.acos(cosfi);
        if (180 * fi / Math.PI < 180) {
            return 180 * fi / Math.PI;
        } else {
            return 360 - 180 * fi / Math.PI;
        }
    }
}
