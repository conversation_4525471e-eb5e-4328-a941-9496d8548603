package com.youibot.agv.scheduler.engine.service.robotcoach;

import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.manager.socket.DataProtocol;
import com.youibot.agv.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.engine.service.config.DefaultConfigApiService;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.util.ActionUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date :Created in 16:50 2021/11/30
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
@Service
public class DefaultRobotCoachService extends DefaultApiService implements RobotCoachService {

    @Value("${AGV_API_CODE.ROBOT_COACH}")
    private String robotCoachApiCode;//获取MOS查询物料接口apiCode

    @Override
    public String queryRobotCoachInfo(String ip) throws Exception {
        try {
            DataProtocol protocolOperation = new DataProtocol(robotCoachApiCode, "");
            AGVSocketClient client = AGVSocketClient.createAGVClient(ip, ActionUtils.getScriptActionPort());
            DataProtocol receiveData = client.sendAndReceive(protocolOperation);
            client.stop();
            return receiveData.getData();
        } catch (Exception e) {
            //except process.
            log.error(LogExceptionStackUtil.LogExceptionStack(e));
            throw e;
        }
    }
}
