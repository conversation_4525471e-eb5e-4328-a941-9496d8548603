package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedNode;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @Date :Created in 10:28 2020/8/10
 * @Description :
 * @Modified By :
 * @Version :
 */
public class DStarLiteAlgorithm {
    private static final Logger logger = LoggerFactory.getLogger(DStarLiteAlgorithm.class);

    private String agvCode;
    private DirectedGraph dGraph;//有向加权图
    private static double km = 0;
    private DirectedNode start;//起点
    private DirectedNode last;//上一次的起点
    private DirectedNode goal;//目标点
    private PriorityQueue<DirectedNode> U = new PriorityQueue<>(new NodeComparator());//节点的优先队列
    private NodeComparator nodeComparator = new NodeComparator();//节点排序比较器
    private boolean dynamic = true;
    private Queue<String> startNodeOfChangedEdge = new ConcurrentLinkedQueue<>();//存放改变边际成本的路径

    public DStarLiteAlgorithm(String agvCode, DirectedGraph dGraph) {
        this.agvCode = agvCode;
        this.dGraph = dGraph;
    }

    public void addStartNodeOfChangedEdge(String nodeId) {
        this.startNodeOfChangedEdge.add(nodeId);
    }

    public void setDynamic(boolean dynamic) {
        this.dynamic = dynamic;
    }

    private void initialize(String startMarkerId, String goalMarkerId) {
        for (DirectedNode node : dGraph.getNodeMap().values()) {
            node.g = Double.POSITIVE_INFINITY;
            node.rhs = Double.POSITIVE_INFINITY;
        }
        DirectedNode s = dGraph.getNodeById(startMarkerId);
        DirectedNode goal = dGraph.getNodeById(goalMarkerId);
        U.clear();
        km = 0;
        start = s;
        last = start;
        goal.g = Double.POSITIVE_INFINITY;
        goal.rhs = 0D;
        goal.k = calculateKey(goal);
        this.goal = goal;
        U.add(goal);
    }

    private void updateNode(DirectedNode u) {
        if (!u.equals(goal)) {
            Set<DirectedNode> successors = dGraph.getSuccessors(u);
            Double min = Double.POSITIVE_INFINITY;
            DirectedNode node = null;//
            for (DirectedNode succ : successors) {
                Double value = backwardCost(u, succ) + succ.g;
                if (value < min) {
                    min = value;
                    node = succ;//
                }
            }
            if (node != null && u.equals(node.child)) {
                node.child = null;
            }
            u.child = node;//
            //node.parent = u;//
            u.rhs = min;
        }
        if (U.contains(u)) {
            U.remove(u);
        }
        if (!u.g.equals(u.rhs)) {
            u.k = calculateKey(u);
            U.add(u);
        }
    }

    private void computeShortestPath() {
        for (int c = 0; ; c++) {
            if (U.size() == 0) {
                break;
            }
            if (!nodeComparator.compare(U.peek().k, calculateKey(start)) && start.rhs.equals(start.g)) {
                break;
            }
            DirectedNode u = U.poll();
            Pair<Double, Double> k_old = u.k.clone();
            if (nodeComparator.compare(k_old, calculateKey(u))) {
                u.k = calculateKey(u);
                U.add(u);
            } else if (u.g > u.rhs) {
                u.g = u.rhs;
                Set<DirectedNode> predecessors = dGraph.getPredecessors(u);
                predecessors.forEach(s -> updateNode(s));
            } else {
                u.g = Double.POSITIVE_INFINITY;
                Set<DirectedNode> predecessors = dGraph.getPredecessors(u);
                Set<DirectedNode> nodes = new HashSet<>(predecessors);
                nodes.add(u);
                nodes.forEach(s -> updateNode(s));
            }
        }
    }

    public MarkerPathResult getPath1() {
        MarkerPathResult markerPathResult = new MarkerPathResult();
        markerPathResult.setSidePath(null);
        DirectedNode curr = start;
        Double cost = 0D;
        if (start.g.equals(Double.POSITIVE_INFINITY)) {
            //logger.error("目标点无法到达");
            markerPathResult.setResult(false);
            return markerPathResult;
        }
        while (!curr.equals(goal)) {
            markerPathResult.getMarkerPath().add(curr.getId());
            if (curr.child != null) {
                cost += this.backwardCost(curr, curr.child);
                curr = curr.child;
            } else {
                //logger.error("目标点无法到达");
                markerPathResult.setCost(cost);
                markerPathResult.setResult(false);
                return markerPathResult;
                //return new MarkerPathResult();
            }
        }
        markerPathResult.getMarkerPath().add(goal.getId());
        markerPathResult.setCost(cost);
        markerPathResult.setResult(true);
        return markerPathResult;
    }

    public MarkerPathResult getPath2() {
        LinkedList<String> path = new LinkedList<>();
        MarkerPathResult markerPathResult = new MarkerPathResult();
        markerPathResult.setSidePath(null);
        DirectedNode curr = start;
        Double cost = 0D;
        if (start.g == Double.POSITIVE_INFINITY) {
            //logger.error("目标点无法到达");
            return new MarkerPathResult();
        }
        while (!curr.equals(goal)) {
            markerPathResult.getMarkerPath().add(curr.getId());
            Set<DirectedNode> successors = dGraph.getSuccessors(curr);
            Double min = Double.POSITIVE_INFINITY;
            DirectedNode node = null;
            for (DirectedNode s : successors) {
                double v = backwardCost(curr, s) + s.g;
                if (v < min) {
                    min = v;
                    node = s;
                }
            }
            if (node == null) {
                //logger.error("目标点无法到达");
                return new MarkerPathResult();
            }
            cost += min;
            curr = node;
        }
        markerPathResult.getMarkerPath().add(goal.getId());
        markerPathResult.setCost(cost);
        markerPathResult.setResult(true);
        return markerPathResult;
    }

    public MarkerPathResult plan(String startMarkerId, String goalMarkerId) {
        initialize(startMarkerId, goalMarkerId);
        computeShortestPath();
        return getPath1();
    }

    public MarkerPathResult rePlan(String startMarkerId, String goalMarkerId) {
        if (goal != null && goal.getId().equals(goalMarkerId)) {
            start = dGraph.getNodeById(startMarkerId);//调整起始点
            km += heuristic(last, start);
            last = start;
            while (startNodeOfChangedEdge.size() != 0) {
                String nodeUId = startNodeOfChangedEdge.poll();
                DirectedNode nodeU = dGraph.getNodeById(nodeUId);
                if (nodeU != null) {
                    updateNode(nodeU);
                }
            }
            computeShortestPath();
            return getPath1();
        } else {
            return plan(startMarkerId, goalMarkerId);
        }

    }

    private Pair<Double, Double> calculateKey(DirectedNode node) {
        double v = Math.min(node.rhs, node.g);
        Double k1 = v + heuristic(node, start) + km;
        Double k2 = v;
        return new Pair<>(k1, k2);
    }

    private double heuristic(DirectedNode u, DirectedNode v) {
        return new Point(u.x, u.y).minus(new Point(v.x, v.y)).getNorm();
    }

    static class NodeComparator implements Comparator<DirectedNode> {
        @Override
        public int compare(DirectedNode n1, DirectedNode n2) {
            if (n1.k.first().equals(n2.k.first())) return n1.k.second() < n2.k.second() ? -1 : 1;
            else return n1.k.first() < n2.k.first() ? -1 : 1;
        }

        public boolean compare(Pair<Double, Double> n1k, Pair<Double, Double> n2k) {
            if (n1k.first().equals(n2k.first())) return n1k.second() < n2k.second();
            return n1k.first() < n2k.first();
        }
    }

    private Double backwardCost(DirectedNode n1, DirectedNode n2) {
        Double backwardCost = dGraph.getBackwardCost(n1, n2, agvCode, dynamic);
        if (backwardCost <= 0) {
            logger.error("backwardCost:");
            throw new PathPlanException(MessageUtils.getMessage("path_plan.auto_weight_negative"));
        }
        return backwardCost;
    }

}
