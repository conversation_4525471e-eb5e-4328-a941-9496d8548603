package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.*;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @Date :Created in 10:28 2020/8/10
 * @Description :
 * @Modified By :
 * @Version :
 */
public class DStarLiteAlgorithm {
    private static final Logger logger = LoggerFactory.getLogger(DStarLiteAlgorithm.class);

    private String agvCode;
    private DirectedGraph dGraph;//有向加权图
    private static double km = 0;
    private DirectedNode start;//起点
    private DirectedNode last;//上一次的起点
    private DirectedNode goal;//目标点
    private PriorityQueue<DirectedNode> U = new PriorityQueue<>(new NodeComparator());//节点的优先队列
    private Map<DirectedNode, DirectedNodeProperty> nodePropertyMap = new ConcurrentHashMap<>();//节点及其属性映射表
    private Map<DirectedEdge, DirectedEdgeProperty> edgePropertyMap = new ConcurrentHashMap<>();//边及其属性映射表
    private NodeComparator nodeComparator = new NodeComparator();//节点排序比较器
    private boolean dynamic = true;
    private Queue<String> startNodeOfChangedEdge = new ConcurrentLinkedQueue<>();//存放改变边际成本的路径

    public DStarLiteAlgorithm(String agvCode, DirectedGraph dGraph) {
        this.agvCode = agvCode;
        this.dGraph = dGraph;
    }

    public DStarLiteAlgorithm(DirectedGraph dGraph) {
        this.dGraph = dGraph;
    }

    public void addStartNodeOfChangedEdge(String nodeId) {
        this.startNodeOfChangedEdge.add(nodeId);
    }

    public void setDynamic(boolean dynamic) {
        this.dynamic = dynamic;
    }

    private void initialize(String startMarkerId, String goalMarkerId) {
        nodePropertyMap.clear();
        for (DirectedNode node : dGraph.getNodeMap().values()) {
            nodePropertyMap.put(node, new DirectedNodeProperty());
        }
        edgePropertyMap.clear();
        for (DirectedEdge edge : dGraph.getAllEdges()) {
            edgePropertyMap.put(edge, new DirectedEdgeProperty(edge));
        }
        DirectedNode s = dGraph.getNodeById(startMarkerId);
        DirectedNode goal = dGraph.getNodeById(goalMarkerId);
        U.clear();
        km = 0;
        start = s;
        last = start;
        setG(goal, Double.POSITIVE_INFINITY);
        setRhs(goal, 0D);
        setK(goal, calculateKey(goal));
        this.goal = goal;
        U.add(goal);
    }

    private Double getG(DirectedNode node) {
        return nodePropertyMap.get(node).g;
    }

    private void setG(DirectedNode node, Double value) {
        nodePropertyMap.get(node).g = value;
    }

    private Double getRhs(DirectedNode node) {
        return nodePropertyMap.get(node).rhs;
    }

    private void setRhs(DirectedNode node, Double value) {
        nodePropertyMap.get(node).rhs = value;
    }

    private Pair<Double, Double> getK(DirectedNode node) {
        return nodePropertyMap.get(node).k;
    }

    private void setK(DirectedNode node, Pair<Double, Double> value) {
        nodePropertyMap.get(node).k = value;
    }

    private DirectedNode getChild(DirectedNode node) {
        return nodePropertyMap.get(node).child;
    }

    private void setChild(DirectedNode node, DirectedNode value) {
        nodePropertyMap.get(node).child = value;
    }

    private void updateNode(DirectedNode u) {
        if (!u.equals(goal)) {
            Set<DirectedNode> successors = dGraph.getSuccessors(u);
            Double min = Double.POSITIVE_INFINITY;
            DirectedNode node = null;//
            for (DirectedNode succ : successors) {
                Double value = backwardCost(u, succ) + getG(succ);
                if (value <= min) {
                    min = value;
                    node = succ;//
                }
            }

            if (node != null && u.equals(getChild(node))) {
                setChild(node, null);
            }
            setChild(u, node);//
            //node.parent = u;//
            setRhs(u, min);
        }
        U.remove(u);
        if (!getG(u).equals(getRhs(u))) {
            setK(u, calculateKey(u));
            U.add(u);
        }
    }

    private void computeShortestPath() {
        for (int c = 0; ; c++) {
            if (U.size() == 0) {
                break;
            }
            if (!nodeComparator.compare(getK(U.peek()), calculateKey(start)) && getRhs(start).equals(getG(start))) {
                break;
            }
            DirectedNode u = U.poll();
            Pair<Double, Double> k_old = getK(u).clone();
            if (nodeComparator.compare(k_old, calculateKey(u))) {
                setK(u, calculateKey(u));
                U.add(u);
            } else if (getG(u) > getRhs(u)) {
                setG(u, getRhs(u));
                Set<DirectedNode> predecessors = dGraph.getPredecessors(u);
                predecessors.forEach(s -> updateNode(s));
            } else {
                setG(u, Double.POSITIVE_INFINITY);
                Set<DirectedNode> predecessors = dGraph.getPredecessors(u);
                Set<DirectedNode> nodes = new HashSet<>(predecessors);
                nodes.add(u);
                nodes.forEach(s -> updateNode(s));
            }
        }
    }

    public MarkerPathResult getPath1() {
        MarkerPathResult markerPathResult = new MarkerPathResult();
        markerPathResult.setSidePath(null);
        DirectedNode curr = start;
        Double cost = 0D;
		
		if (getG(start).equals(Double.POSITIVE_INFINITY)) {
			markerPathResult.setResult(false); //
			logger.error("1.目标点无法到达, agvCode:[{}], startCode:[{}], endCode:[{}]", agvCode, start.getCode(),
					goal.getCode()); //
			/*
			 * logger.error("1.markerPathResult: {}", markerPathResult); //
			 * logger.error("1.DGraphCost: {}", MapGraphUtil.printDGraphCost());
			 */
			return markerPathResult;
		}
		 
        while (!curr.equals(goal)) {
            markerPathResult.markerPath.add(curr.getId());
            if (getChild(curr) != null) {
                cost += this.backwardCost(curr, getChild(curr));
                curr = getChild(curr);
            } else {
                markerPathResult.setCost(cost);
                markerPathResult.setResult(false);
//                logger.error("2.目标点无法到达, agvCode:[{}], startCode:[{}], endCode:[{}]", agvCode, start.getCode(), goal.getCode());
//                logger.error("2.markerPathResult: {}", markerPathResult);
//                logger.error("2.DGraphCost: {}", MapGraphUtil.printDGraphCost());
                return markerPathResult;
            }
        }
        markerPathResult.markerPath.add(goal.getId());
        markerPathResult.setCost(cost);
        markerPathResult.setResult(true);
        return markerPathResult;
    }

    public MarkerPathResult getPath2() {
        MarkerPathResult markerPathResult = new MarkerPathResult();
        markerPathResult.setSidePath(null);
        DirectedNode curr = start;
        Double cost = 0D;
        if (getG(start) == Double.POSITIVE_INFINITY) {
            markerPathResult.setResult(false);
//            logger.error("1.目标点无法到达, agvCode:[{}], startCode:[{}], endCode:[{}]", agvCode, start.getCode(), goal.getCode());
//            logger.error("1.markerPathResult: {}", markerPathResult);
//            logger.error("1.DGraphCost: {}", MapGraphUtil.printDGraphCost());
            return markerPathResult;
        }
        while (!curr.equals(goal)) {
            if (markerPathResult.markerPath.contains(curr.getId())) {
                logger.error("path plan error");
                markerPathResult.setCost(cost);
                markerPathResult.setResult(false);
                return markerPathResult;
            }
            markerPathResult.markerPath.add(curr.getId());
            Set<DirectedNode> successors = dGraph.getSuccessors(curr);
            Double min = Double.POSITIVE_INFINITY;
            DirectedNode node = null;
            for (DirectedNode s : successors) {
                double v = backwardCost(curr, s) + getG(s);
                if (v < min) {
                    min = v;
                    node = s;
                }
            }
            if (node == null || min.equals(Double.POSITIVE_INFINITY)) {
                markerPathResult.setCost(cost);
                markerPathResult.setResult(false);
//                logger.error("2.目标点无法到达, agvCode:[{}], startCode:[{}], endCode:[{}]", agvCode, start.getCode(), goal.getCode());
//                logger.error("2.markerPathResult: {}", markerPathResult);
//                logger.error("2.DGraphCost: {}", MapGraphUtil.printDGraphCost());
                return markerPathResult;
            }
            cost += min;
            curr = node;
        }
        markerPathResult.markerPath.add(goal.getId());
        markerPathResult.setCost(cost);
        markerPathResult.setResult(true);
        return markerPathResult;
    }

    public MarkerPathResult plan(String startMarkerId, String goalMarkerId) {
        initialize(startMarkerId, goalMarkerId);
        computeShortestPath();
        return getPath1();
    }

    public MarkerPathResult rePlan(String startMarkerId, String goalMarkerId) {
        if (goal != null && goal.getId().equals(goalMarkerId)) {
            start = dGraph.getNodeById(startMarkerId);//调整起始点
            km += heuristic(last, start);
            last = start;
            while (startNodeOfChangedEdge.size() != 0) {
                String nodeUId = startNodeOfChangedEdge.poll();
                DirectedNode nodeU = dGraph.getNodeById(nodeUId);
                if (nodeU != null) {
                    updateNode(nodeU);
                }
            }
            computeShortestPath();
            return getPath1();
        } else {
            return plan(startMarkerId, goalMarkerId);
        }
    }

    private Pair<Double, Double> calculateKey(DirectedNode node) {
        double v = Math.min(getRhs(node), getG(node));
        Double k1 = v + heuristic(node, start) + km;
        Double k2 = v;
        return new Pair<>(k1, k2);
    }

    private double heuristic(DirectedNode u, DirectedNode v) {
        return new Point(u.x, u.y).minus(new Point(v.x, v.y)).getNorm();
    }

    class NodeComparator implements Comparator<DirectedNode> {
        @Override
        public int compare(DirectedNode n1, DirectedNode n2) {
            if (getK(n1).first().equals(getK(n2).first())) return getK(n1).second() < getK(n2).second() ? -1 : 1;
            else return getK(n1).first() < getK(n2).first() ? -1 : 1;
        }

        public boolean compare(Pair<Double, Double> n1k, Pair<Double, Double> n2k) {
            if (n1k.first().equals(n2k.first())) return n1k.second() < n2k.second();
            return n1k.first() < n2k.first();
        }
    }

    private Double backwardCost(DirectedNode n1, DirectedNode n2) {
        Double backwardCost = dGraph.getBackwardCost(n1, getChild(n1), n2, getChild(n2), edgePropertyMap, agvCode, dynamic);
        if (backwardCost < 0) {
            logger.error("backwardCost:[{}]->[{}], auto weight negative ", n1.getCode(), n2.getCode());
            throw new PathPlanException(MessageUtils.getMessage("path_plan.auto_weight_negative"));
        }
        if (backwardCost.equals(Double.POSITIVE_INFINITY)) {
            logger.error("backwardCost:[{}]->[{}], edge is not available.", n1.getCode(), n2.getCode());
        }
        return backwardCost;
    }

}
