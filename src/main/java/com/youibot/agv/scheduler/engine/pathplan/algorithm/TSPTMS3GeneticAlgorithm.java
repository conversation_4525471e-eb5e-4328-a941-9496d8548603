package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.exception.ExecuteException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DistanceMatrixUtil.getDistance;

/**
 * <AUTHOR>
 * @Date :Created in 18:04 2021/9/26
 * @Description :
 * @Modified By :
 * @Version :
 */
public class TSPTMS3GeneticAlgorithm {
    private static final Logger logger = LoggerFactory.getLogger(TSPTMS3GeneticAlgorithm.class);

    private Map<String, Map<String, Double>> distanceMatrix;                  //距离矩阵

    private List<String> loadMaterial;
    private List<String> unloadMaterial;

    public void GAInit(List<String> loadMaterial, List<String> unloadMaterial) {
        if (CollectionUtils.isEmpty(loadMaterial) || CollectionUtils.isEmpty(unloadMaterial)) {
            logger.error("TSPTMS3GeneticAlgorithm.GAInit 错误的参数");
            throw new ExecuteException("错误的参数");
        }

        this.loadMaterial = loadMaterial;
        this.unloadMaterial = unloadMaterial;

        distanceMatrix = DistanceMatrixUtil.getDistanceMatrix();
    }

    public Pair<String, String> run() {
        Double minLength = Double.POSITIVE_INFINITY;
        String bestLoadMaterialIndex = null;
        String bestUnloadMaterialIndex = null;

        int loadSize = loadMaterial.size();
        int unloadSize = unloadMaterial.size();
        for (int i = 0; i < loadSize; i++) {
            for (int j = 0; j < unloadSize; j++) {
                String loadMaterialIndex = loadMaterial.get(i);
                String unloadMaterialIndex = unloadMaterial.get(j);
                Double distance = getDistance(distanceMatrix, loadMaterialIndex, unloadMaterialIndex);
                if (distance < minLength) {
                    minLength = distance;
                    bestLoadMaterialIndex = loadMaterialIndex;
                    bestUnloadMaterialIndex = unloadMaterialIndex;
                }
            }
        }
        if (minLength >= Double.POSITIVE_INFINITY) {
            logger.error("计算错误，请检查参数是否合规");
            throw new ExecuteException("计算错误，请检查参数是否合规");
        }
        return new Pair<>(bestLoadMaterialIndex, bestUnloadMaterialIndex);
    }
}
