package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.SidePathPlanResult;
import com.youibot.agv.scheduler.entity.SidePath;

import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date :Created in 下午12:09 19-11-4
 * @Description :
 * @Modified By :
 * @Version :
 */
public interface PathPlanService {

    /**
     * 返回最长安全路径 返回null代表已经到达   返回[]代表当前获取到的路径为空需要等待
     * 返回[边的数组]代表安全路径    如果在无占用都没路径则抛出异常
     */
    LinkedList<SidePath> search(String aimMarkerId) throws PathPlanException, InterruptedException;

    MarkerPathResult pathScope(String aimMarkerId) throws PathPlanException, InterruptedException;

    /**
     * 过滤堵塞路径，进行路径规划
     * 返回最长安全路径 返回null代表已经到达   返回[]代表当前获取到的路径为空需要等待
     * 返回[边的数组]代表安全路径    如果在无占用都没路径则抛出异常
     */
    LinkedList<SidePath> search(String aimMarkerId, String sidePathId) throws PathPlanException, InterruptedException;

    /**
     * 移除禁行路径
     *
     * @param sidePaths
     */
    void removeJamSidePaths(List<SidePath> sidePaths);

    Set<String> getJamSidePaths();

    /**
     * 清楚堵塞路径
     */
    void clearJamSidePaths();

    SidePathPlanResult linkerListPathToSidePath(MarkerPathResult markerPathResult);

    Double calculateNavigationCost(String aimMarkerId);
}
