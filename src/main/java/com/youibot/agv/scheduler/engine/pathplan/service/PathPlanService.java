package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.engine.exception.PathPlanException;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.SidePathPlanResult;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.StatusMessage;

/**
 * Created by lijianghua on 19-11-1.
 */
public interface PathPlanService {

    /**
     * 路径规划的请求添加车辆的路径规划列表中。
     * 已过期，直接用车辆的路径规划列表中直接添加。
     *
     * @param pathPlanMessage
     */
    @Deprecated
    void addRequestPathPlanMessage(PathPlanMessage pathPlanMessage);

    void lockPathPlan(String agvCode);

    void addPositionLock(String agvCode);

    Boolean getPositionLock(String agvCode);

    void unlockPathPlan(String agvCode);

    SidePathPlanResult linkerListPathToSidePath(String agvCode, MarkerPathResult markerPathResult);

    Integer getSidePathNavigationType(SidePath sidePath);

    /**
     * 返回最长安全路径 返回null代表已经到达   返回[]代表当前获取到的路径为空需要等待
     * 返回[边的数组]代表安全路径    如果在无占用都没路径则抛出异常
     *
     * @param agvId
     * @param aimMarkerId
     * @return
     * @throws PathPlanException
     * @throws InterruptedException
     */
    MarkerPathResult search(String agvId, String aimMarkerId) throws PathPlanException, InterruptedException;

    MarkerPathResult search(String agvId, String aimMarkerId, String startMarkerId) throws PathPlanException, InterruptedException;

    MarkerPathResult searchMarkerToMarker(String startMarkerId, String endMarkerId) throws PathPlanException, InterruptedException;

    /**
     * agv状态处理
     *
     * @param statusMessage
     */
    void agvStatusCallBack(StatusMessage statusMessage);

    Double calculateNavigationCost(String agvCode, String aimMarkerId);
}
