package com.youibot.agv.scheduler.engine.pathplan.util;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_CURVE;
import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_STRAIGHT;

/**
 * <AUTHOR>
 * @Date :Created in 下午8:01 19-11-8
 * @Description :
 * @Modified By :
 * @Version :
 */
public class SidePathUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(SidePathUtils.class);

    private static MarkerService markerService = (MarkerService) ApplicationUtils.getBean("markerServiceImpl");

    //TODO marker参数有误待修改
    public static Double getLength(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        Marker startMarker = markerService.selectById(sidePath.getAgvMapName(),sidePath.getStartMarkerId(),false);
        Marker endMarker = markerService.selectById(sidePath.getAgvMapName(),sidePath.getEndMarkerId(),false);
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double length = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                length = LineUtil.getLength(line, 0D, 1D);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                length = BezierUtil.getLength(points, 0D, 1D);
                break;
            }
        }
        return length;
    }

    public static Double getDistance(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        Marker startmarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
        Marker endmarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
        points[0] = new Point(startmarker.getX(), startmarker.getY());
        points[3] = new Point(endmarker.getX(), endmarker.getY());
        Double distance = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                distance = LineUtil.getLength(line, 0D, sidePath.getT0());
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                distance = BezierUtil.getLength(points, 0D, sidePath.getT0());
                break;
            }
        }
        return distance;
    }

    public static Double getDistanceRemaining(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        Marker startmarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
        Marker endmarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());

        points[0] = new Point(startmarker.getX(), startmarker.getY());
        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
        points[3] = new Point(endmarker.getX(), endmarker.getY());
        Double distance = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                distance = LineUtil.getLength(line, sidePath.getT0(), 1D);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                distance = BezierUtil.getLength(points, sidePath.getT0(), 1D);
                break;
            }
        }

        return distance;
    }

    public static Point getSidePathPoint(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        Marker startmarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
        Marker endmarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());

        points[0] = new Point(startmarker.getX(), startmarker.getY());
        points[3] = new Point(endmarker.getX(), endmarker.getY());
        Point location = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                location = LineUtil.getPoint(line, sidePath.getT0());
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                location = BezierUtil.getPoint(points, sidePath.getT0());
                break;
            }
        }
        return location;
    }

    public static Double[] getInOutAngle(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        MarkerService markerService = (MarkerService) ApplicationUtils.getBean("markerServiceImpl");
        Marker startMarker = markerService.selectById(sidePath.getAgvMapName(),sidePath.getStartMarkerId(),true);
        Marker endMarker = markerService.selectById(sidePath.getAgvMapName(),sidePath.getEndMarkerId(),true);

        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double[] inOutAngle = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                inOutAngle = LineUtil.getInOutAngle(line);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                inOutAngle = BezierUtil.getInOutAngle(points);
                break;
            }
        }
        return inOutAngle;
    }

    public static Double[] getInOutAngle2(SidePath sidePath, Marker startMarker, Marker endMarker) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double[] inOutAngle = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                inOutAngle = LineUtil.getInOutAngle(line);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                inOutAngle = BezierUtil.getInOutAngle(points);
                break;
            }
        }
        return inOutAngle;
    }
}
