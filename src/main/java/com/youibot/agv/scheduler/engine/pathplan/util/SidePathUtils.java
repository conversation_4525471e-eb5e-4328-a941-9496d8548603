package com.youibot.agv.scheduler.engine.pathplan.util;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.LinkedBlockingDeque;

import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_CURVE;
import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_STRAIGHT;

/**
 * <AUTHOR>
 * @Date :Created in 下午8:01 19-11-8
 * @Description :
 * @Modified By :
 * @Version :
 */
public class SidePathUtils {
    private static final Logger logger = LoggerFactory.getLogger(SidePathUtils.class);

    private static MarkerService markerService = (MarkerService) ApplicationUtils.getBean("markerServiceImpl");

    public static Double getLength(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        Marker startMarker = markerService.selectById(sidePath.getAgvMapName(), sidePath.getStartMarkerId(), false);
        Marker endMarker = markerService.selectById(sidePath.getAgvMapName(), sidePath.getEndMarkerId(), false);
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double length = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                length = LineUtil.getLength(line, 0D, 1D);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                length = BezierUtil.getLength(points, 0D, 1D);
                break;
            }
        }
        return length;
    }

    public static Double getDistance(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
        Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double distance = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                distance = LineUtil.getLength(line, 0D, sidePath.getT0());
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                distance = BezierUtil.getLength(points, 0D, sidePath.getT0());
                break;
            }
        }
        return distance;
    }

    public static Double getDistanceRemaining(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
        Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double distance = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                distance = LineUtil.getLength(line, sidePath.getT0(), 1D);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                distance = BezierUtil.getLength(points, sidePath.getT0(), 1D);
                break;
            }
        }
        return distance;
    }

    public static Point getSidePathPoint(SidePath sidePath) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
        Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Point location = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                location = LineUtil.getPoint(line, sidePath.getT0());
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                location = BezierUtil.getPoint(points, sidePath.getT0());
                break;
            }
        }
        return location;
    }

    public static Double[] getInOutAngle(SidePath sidePath, boolean isDraft) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        //Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
        //Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
        Marker startMarker = markerService.selectById(sidePath.getAgvMapName(), sidePath.getStartMarkerId(), isDraft);
        Marker endMarker = markerService.selectById(sidePath.getAgvMapName(), sidePath.getEndMarkerId(), isDraft);

        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double[] inOutAngle = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                inOutAngle = LineUtil.getInOutAngle(line);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                inOutAngle = BezierUtil.getInOutAngle(points);
                break;
            }
        }
        return inOutAngle;
    }

    public static Double[] getInOutAngle2(SidePath sidePath, Marker startMarker, Marker endMarker) {
        if (sidePath == null) {
            return null;
        }
        Point[] points = new Point[4];
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double[] inOutAngle = null;
        switch (sidePath.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                inOutAngle = LineUtil.getInOutAngle(line);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
                points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
                inOutAngle = BezierUtil.getInOutAngle(points);
                break;
            }
        }
        return inOutAngle;
    }

    //把sidePaths按照索引k切为两片，第一段包括索引k
    public static List<LinkedBlockingDeque<SidePath>> spiltSidePaths(LinkedBlockingDeque<SidePath> sidePaths, Integer k) {
        LinkedBlockingDeque<SidePath> first = new LinkedBlockingDeque<>();
        LinkedBlockingDeque<SidePath> second = new LinkedBlockingDeque<>();
        SidePath[] tempSidePaths = sidePaths.stream().toArray(SidePath[]::new);
        for (int i = 0; i < tempSidePaths.length; i++) {
            if (i <= k) {
                first.add(tempSidePaths[i]);
            } else {
                second.add(tempSidePaths[i]);
            }
        }
        List<LinkedBlockingDeque<SidePath>> list = new CopyOnWriteArrayList<>();
        list.add(first);
        list.add(second);
        return list;
    }

    public static LinkedList<String> getMarkerIdsFromSidePaths(List<SidePath> sidePaths) {
        LinkedList<String> markerIds = new LinkedList<>();
        for (SidePath sidePath : sidePaths) {
            String startMarkerId = sidePath.getStartMarkerId();
            String endMarkerId = sidePath.getEndMarkerId();
            if (!markerIds.contains(startMarkerId)) {
                markerIds.add(startMarkerId);
            }
            if (!markerIds.contains(endMarkerId)) {
                markerIds.add(endMarkerId);
            }
        }
        return markerIds;
    }
}
