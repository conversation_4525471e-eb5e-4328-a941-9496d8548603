package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DFSBranchDetectManager;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.TrafficManager;
import com.youibot.agv.scheduler.engine.pathplan.util.SidePathUtils;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.service.*;
import lombok.Data;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapConstant.*;
import static com.youibot.agv.scheduler.constant.PathPlanConstant.*;

/**
 * <AUTHOR> <EMAIL>
 * @Date :Created in 18:43 2020/8/7
 * @Description :
 * @Modified By :
 * @Version :
 */
@Data
@Component
public class MapGraph {
    private static final Logger logger = LoggerFactory.getLogger(MapGraph.class);

    @Autowired
    private MarkerService markerService;

    @Autowired
    private SidePathService sidePathService;

    @Autowired
    private PathService pathService;

    @Autowired
    private MapAreaService mapAreaService;

    @Autowired
    private ElevatorService elevatorService;

    @Autowired
    private FloorService floorService;

    @Autowired
    private AdjustActionService adjustActionService;

    @Autowired
    private PathAgvTypeService pathAgvTypeService;

    @Autowired
    private TrafficManager trafficManager;

    @Autowired
    private LocationService locationService;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    private DFSBranchDetectManager branchDetectManager = DFSBranchDetectManager.getInstance();

    @Data
    private class MapElements {
        public String agvMapId;
        public List<Path> paths = new ArrayList<>();
        public List<Marker> markers = new ArrayList<>();
        public List<SidePath> sidePaths = new ArrayList<>();
        public List<MapArea> mapAreas = new ArrayList<>();
        public List<Marker> elevatorMarkers = new ArrayList<>();

        public MapElements(String agvMapId) {
            this.agvMapId = agvMapId;
        }
    }

    //单向映射 Key:Marker.getId(),Value:Marker
    private Map<String, Marker> markerIdToMarker = new ConcurrentHashMap<>();
    //单向映射 Key:Marker.getId(),Value:Marker
    private Map<String, SidePath> sidePathIdToSidePath = new ConcurrentHashMap<>();
    //单向映射 Key:startMarker.getId()+endMarker.getId(),Value:SidePath
    private Map<String, SidePath> startEndMarkerIdToSidePath = new ConcurrentHashMap<>();
    //单向映射 Key:PathId,Value:Path
    private Map<String, Path> pathIdToPath = new ConcurrentHashMap<>();
    //单向映射 key:PathId,Value:List<String> 在该条路径上被禁用的agv列表
    private Map<String, List<String>> pathIdToJamAGVTypes = new ConcurrentHashMap<>();

    //单向映射 Key:elevatorIds,Value:Elevators
    private Map<String, Elevator> elevatorIdToElevator = new ConcurrentHashMap<>();
    //单向映射 Key:agvMapId,Value:elevatorMarkerIds
    private Map<String, String> elevatorMarkerIdToElevatorId = new ConcurrentHashMap<>();
    //单向映射 Key:ElevatorId,Value:Marker.getId()
    private Map<String, Set<String>> elevatorIdToElevatorMarkerIds = new ConcurrentHashMap<>();
    //单向映射 Key:ElevatorId,Value:Marker.getId() 记录电梯对应的所有的进入电梯点Id
    private Map<String, Set<String>> elevatorIdToInOutMarkerIds = new ConcurrentHashMap<>();
    //单向映射 Key:InOutMarkerId,Value:elevator Marker id 记录电梯进入点id对应的电梯点markerId
    public Map<String, String> inOutMarkerIdToElevatorMarkerId = new ConcurrentHashMap<>();
    //单向映射 Key:markerId,Value:路径导航类型
    private Map<String, Integer> markerIdToMarkerNavigationType = new ConcurrentHashMap<>();

    //保存所有激活的地图
    private Map<String, MapElements> activeAgvMapIds = new ConcurrentHashMap<>();
    private Map<String, SidePath> virtualSidePathMap = new ConcurrentHashMap<>();
    //保存所有激活的地图点
    private Set<Marker> activeMarkers = new HashSet<>();
    //存储所有被禁用的点
    private List<Marker> disableMarkers = new ArrayList<>();
    //保存所有激活的路径
    private Set<SidePath> activeSidePaths = new HashSet<>();
    //存储所有被禁用的路径
    private List<SidePath> disableSidePaths = new ArrayList<>();
    //维护的加权有向图
    private DirectedGraph dGraphOfAGVMap = new DirectedGraph();

    public synchronized void init() {
        this.clean();
    }

    public synchronized void addAGVMap(String agvMapId) {
        if (agvMapId == null) return;
        try {
            locationService.startMapChange();
            MapElements mapElements = new MapElements(agvMapId);
            activeAgvMapIds.put(agvMapId, mapElements);
            //获取该地图中所有的marker点和路径
            List<Marker> markersInAGVMap = markerService.selectByAGVMapId(agvMapId, false);
            List<SidePath> sidePathsInAGVMap = sidePathService.selectByAGVMapId(agvMapId, false);
            List<Path> pathsInAGVMap = pathService.selectByAGVMapId(agvMapId, false);
            List<PathAgvType> pathAgvTypes = pathAgvTypeService.findAll();
            initPathAgvTypes(pathAgvTypes);

            //存储该地图下的所有marker点和路径
            mapElements.getMarkers().addAll(markersInAGVMap);
            mapElements.getSidePaths().addAll(sidePathsInAGVMap);
            mapElements.getPaths().addAll(pathsInAGVMap);

            pathsInAGVMap.forEach(path -> pathIdToPath.put(path.getId(), path));
            //check marker or sidepath data whether is bad
            checkAndRepairData(agvMapId, sidePathsInAGVMap);
            locationService.buildKDTree(agvMapId, markersInAGVMap);
            //存储marker点
            markersInAGVMap.forEach(marker -> markerIdToMarker.put(marker.getId(), marker));
            dGraphOfAGVMap.addNodes(markersInAGVMap);
            //保存所有的激活marker点
            activeMarkers.addAll(markersInAGVMap);
            //保存所有的激活sidePath
            activeSidePaths.addAll(sidePathsInAGVMap);

            //处理楼层信息，保存地图对应的电梯点
            List<Floor> floors = floorService.selectByAGVMapId(agvMapId);
            initFloorOutElevatorResource(agvMapId,floors);
            Double max_rotate_speed = 0.8D;
            Double max_translation_speed = 0.8D;
            addSidePath(sidePathsInAGVMap, max_rotate_speed, max_translation_speed);

            //生成虚拟的SidePath，只从已经激活的地图中选择电梯点，未激活的地图生成虚拟SidePath
            List<Marker> elevatorMarkers = markerService.selectByAGVMapIdAndType(agvMapId, MARKER_TYPE_ELEVATOR, false);
            mapElements.getElevatorMarkers().addAll(elevatorMarkers);
            List<String> elevatorMarkerIds = elevatorMarkers.stream().map(Marker::getId).collect(Collectors.toList());
            //存储所有进入电梯点的marker点
            sidePathsInAGVMap.forEach(sidePath -> {
                String startMarkerId = sidePath.getStartMarkerId();
                String endMarkerId = sidePath.getEndMarkerId();
                if (elevatorMarkerIds.contains(endMarkerId)) {
                    markerIdToMarkerNavigationType.put(startMarkerId, MARKER_NAVIGATION_TYPE_IN_OUT);
                    String elevatorId = elevatorMarkerIdToElevatorId.get(endMarkerId);
                    if (elevatorId != null) {
                        inOutMarkerIdToElevatorMarkerId.put(startMarkerId, endMarkerId);
                        elevatorIdToInOutMarkerIds.computeIfAbsent(elevatorId, value -> new HashSet<>());
                        elevatorIdToInOutMarkerIds.get(elevatorId).add(startMarkerId);
                    }
                }
            });
            //存储调整点
            List<AdjustAction> adjustActions = adjustActionService.selectByAGVMapId(agvMapId, false);
            for (AdjustAction adjustAction : adjustActions) {
                markerIdToMarkerNavigationType.put(adjustAction.getMarkerId(), MARKER_NAVIGATION_TYPE_ADJUST);
                markerIdToMarkerNavigationType.put(adjustAction.getDestMarkerId(), MARKER_NAVIGATION_TYPE_ADJUST_DEST);
            }
            //将禁用的路径和marker点删除
            List<Marker> disableMarkersInAGVMap = markerService.selectByAGVMapIdAndUsageStatus(agvMapId, MARKER_USAGE_STATUS_DISABLE, false);
            disableMarkers.addAll(disableMarkersInAGVMap);
            disableMarkersInAGVMap.forEach(disableMarker -> {
                Set<DirectedEdge> inEdges = dGraphOfAGVMap.getInEdges(disableMarker.getId());
                dGraphOfAGVMap.removeEdges(inEdges);
            });
            List<SidePath> disableSidePathsInAGVMap = sidePathService.selectByAGVMapIdAndUsageStatus(agvMapId, PATH_USAGE_STATUS_DISABLE, false);
            if (!CollectionUtils.isEmpty(disableSidePathsInAGVMap)) {
                disableSidePaths.addAll(disableSidePathsInAGVMap);
                disableSidePathsInAGVMap.forEach(dSidePath -> dGraphOfAGVMap.removeEdge(dSidePath));
            }

            //交通管制
            List<MapArea> mapAreas = mapAreaService.selectByAGVMapIdAndType(agvMapId, SINGLE_AGV_AREA, false);
            mapElements.setMapAreas(mapAreas);
            addElevatorVirtualSidePath();
            addPathResource(agvMapId);
            logger.info("已添加地图，AGVMapId=[{}]", agvMapId);
        } finally {
            locationService.endMapChange();
            setMapChanged(agvMapId);
        }
    }

    //删除地图接口
    public synchronized void removeAGVMap(String agvMapId) {
        if (agvMapId == null || !activeAgvMapIds.containsKey(agvMapId)) {
            return;
        }
        try {
            locationService.startMapChange();
            MapElements mapElements = activeAgvMapIds.get(agvMapId);
            if (mapElements == null) return;

            List<Marker> markersInAGVMap = mapElements.getMarkers();
            if (markersInAGVMap != null) {
                activeMarkers.removeAll(markersInAGVMap);
                markersInAGVMap.forEach(marker -> {
                    Set<DirectedEdge> inOutEdges = dGraphOfAGVMap.getInOutEdges(marker.getId());
                    dGraphOfAGVMap.removeEdges(inOutEdges);
                    dGraphOfAGVMap.removeNode(marker);
                    markerIdToMarker.remove(marker.getId());
                    markerIdToMarkerNavigationType.remove(marker.getId());
                });
            }

            List<Path> pathsInAGVMap = mapElements.getPaths();
            if (pathsInAGVMap != null) {
                pathsInAGVMap.forEach(path -> pathIdToPath.remove(path.getId()));
            }

            List<SidePath> sidePathsInAGVMap = mapElements.getSidePaths();
            if (sidePathsInAGVMap != null) {
                activeSidePaths.removeAll(sidePathsInAGVMap);
                dGraphOfAGVMap.removeEdges(sidePathsInAGVMap);
                sidePathsInAGVMap.forEach(sidePath -> {
                    sidePathIdToSidePath.remove(sidePath.getId());
                    startEndMarkerIdToSidePath.remove(sidePath.getStartMarkerId() + sidePath.getEndMarkerId());
                });
            }
            List<Marker> disableMarkersInAGVMap = disableMarkers.stream().filter(marker -> marker.getAgvMapName().equals(agvMapId)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(disableMarkersInAGVMap)) {
                disableMarkers.removeAll(disableMarkersInAGVMap);
            }
            List<SidePath> disableSidePathsInAGVMap = disableSidePaths.stream().filter(sidePath -> sidePath.getAgvMapName().equals(agvMapId)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(disableSidePaths)) {
                disableSidePaths.removeAll(disableSidePathsInAGVMap);
            }
            removePathResource(agvMapId);
            addElevatorVirtualSidePath();
            locationService.removeKDTree(agvMapId);
            activeAgvMapIds.remove(agvMapId);
            logger.info("已删除地图，AGVMapId=[{}]", agvMapId);
        } finally {
            locationService.endMapChange();
            setMapChanged(agvMapId);
        }
    }

    private synchronized void addSidePath(Collection<SidePath> sidePaths, Double max_rotate_speed, Double max_translation_speed) {
        for (SidePath sidePath : sidePaths) {
            //存储开始节点和结束节点到路径的映射
            String startMarkerId = sidePath.getStartMarkerId();
            String endMarkerId = sidePath.getEndMarkerId();
            startEndMarkerIdToSidePath.put(startMarkerId + endMarkerId, sidePath);
            //添加加权有向图的边，权用时间表示
            Marker startMarker = markerIdToMarker.get(startMarkerId);
            Marker endMarker = markerIdToMarker.get(endMarkerId);
            if (startMarker == null || endMarker == null) continue;

            //存储路径信息
            sidePathIdToSidePath.put(sidePath.getId(), sidePath);
            String agvMapId = sidePath.getAgvMapName();
            MapElements mapElements = activeAgvMapIds.get(agvMapId);
            if (mapElements != null) {
                mapElements.getSidePaths().add(sidePath);
            }

            String pathId = sidePath.getPathId();
            List<String> jamAgvTypes = pathIdToJamAGVTypes.get(pathId);

            dGraphOfAGVMap.addEdge(sidePath, startMarker, endMarker, max_translation_speed, max_rotate_speed, jamAgvTypes);
        }
    }

    private synchronized void initPathAgvTypes(List<PathAgvType> pathAgvTypes) {
        if (CollectionUtils.isEmpty(pathAgvTypes)) return;
        pathIdToJamAGVTypes.clear();
        for (PathAgvType pathAgvType : pathAgvTypes) {
            String pathId = pathAgvType.getPathId();
            String agvTypeIds = pathAgvType.getAgvTypeIds();
            if (pathId == null) continue;
            if (agvTypeIds == null) continue;
            List<String> agvTypes = Arrays.stream(agvTypeIds.split(",")).collect(Collectors.toList());
            pathIdToJamAGVTypes.put(pathId, agvTypes);
        }
    }

    /**
     * 添加到路径资源池。
     *
     * @param agvMapId
     */
    private synchronized void addPathResource(String agvMapId) {
        MapElements mapElements = activeAgvMapIds.get(agvMapId);
        if (mapElements != null) {
            trafficManager.addMarkerResources(mapElements.getMarkers());
            trafficManager.addSidePathResources(mapElements.getSidePaths());
            trafficManager.addSingleAreaResources(mapElements.getMapAreas());
        }
        initElevatorResource();
        mapBranchProcess();
    }

    /**
     * 移动路径资源池。
     *
     * @param agvMapId
     */
    private synchronized void removePathResource(String agvMapId) {
        MapElements mapElements = activeAgvMapIds.get(agvMapId);
        if (mapElements != null) {
            trafficManager.removeMarkerResources(mapElements.getMarkers());
            trafficManager.removeSidePathResources(mapElements.getSidePaths());
            trafficManager.removeSingleAreaResources(mapElements.getMapAreas());
        }
        initElevatorResource();
        mapBranchProcess();
    }

    /**
     * 地图分支路径处理。
     */
    private synchronized void mapBranchProcess() {
        trafficManager.clearTPathResources();
        trafficManager.clearOPathResources();
        branchDetectManager.setGraph(dGraphOfAGVMap);
        //在原来T字路所占资源的基础上，再增加相邻的一个点，作为T字路的所占资源
//        List<List<String>> tempTPaths = branchDetectManager.getTPaths();
//        List<List<String>> tPaths = new ArrayList<>();
//        for (List<String> tempPath : tempTPaths) {
//            Set<String> markerIds = new HashSet<>();
//            for (String markerId :tempPath){
//                markerIds.add(markerId);
//                Set<String> st = dGraphOfAGVMap.getAdjacentMarkerIds(markerId);
//                markerIds.addAll(st);
//            }
//            tPaths.add(new ArrayList<>(markerIds));
//        }
        List<List<String>> tPaths = branchDetectManager.getTPaths();
        List<List<String>> oPaths = branchDetectManager.getOPaths();
        trafficManager.addTPathResources(tPaths);
        trafficManager.addOPathResources(oPaths);
    }

    private synchronized void initElevatorResource() {
        trafficManager.clearElevatorResources();
        Set<String> elevatorIds = elevatorIdToElevator.keySet();
        elevatorIds.forEach(elevatorId -> {
            Set<String> elevatorResource = new HashSet<>();
            Set<String> inOutElevatorMarkerIds = elevatorIdToInOutMarkerIds.getOrDefault(elevatorId, new HashSet<>());
            Set<String> elevatorMarkerIds = elevatorIdToElevatorMarkerIds.getOrDefault(elevatorId, new HashSet<>());
            for (String inOutElevatorMarkerId : inOutElevatorMarkerIds) {
//                Set<String> adjacentMarkerIds = dGraphOfAGVMap.getAdjacentMarkerIds(inOutElevatorMarkerId);
                Set<String> adjacentMarkerIds = MapGraphUtil.getElevatorResourceIds(inOutElevatorMarkerId);
                elevatorResource.addAll(adjacentMarkerIds);
            }
            elevatorResource.addAll(inOutElevatorMarkerIds);
            elevatorResource.addAll(elevatorMarkerIds);
            trafficManager.addElevatorResources(elevatorId, elevatorResource);
        });
    }

    private synchronized void addElevatorVirtualSidePath() {
        if (activeAgvMapIds.isEmpty()) return;
        //首先清理虚拟路径相关缓存
        Collection<SidePath> virtualSidePaths = virtualSidePathMap.values();
        if (!CollectionUtils.isEmpty(virtualSidePaths)) {
            trafficManager.removeSidePathResources(virtualSidePaths);
            dGraphOfAGVMap.removeEdges(virtualSidePaths);
            virtualSidePaths.forEach(sidePath -> {
                sidePathIdToSidePath.remove(sidePath.getId());
                startEndMarkerIdToSidePath.remove(sidePath.getStartMarkerId() + sidePath.getEndMarkerId());
            });
        }
        virtualSidePathMap.clear();
        //生成新的虚拟路径, Map的目的是为了祛除重复生成的虚拟路径
        for (String agvMapId : activeAgvMapIds.keySet()) {
            List<Marker> elevatorMarkers = activeAgvMapIds.get(agvMapId).getElevatorMarkers();
            for (Marker elevatorMarker : elevatorMarkers) {
                String thisElevatorMarkerId = elevatorMarker.getId();
                String thisAgvMapId = elevatorMarker.getAgvMapName();
                markerIdToMarkerNavigationType.put(thisElevatorMarkerId, MARKER_NAVIGATION_TYPE_ELEVATOR);
                String elevatorId = elevatorMarkerIdToElevatorId.get(thisElevatorMarkerId);
                if (elevatorId == null) continue;
                for (String thatElevatorMarkerId : elevatorIdToElevatorMarkerIds.get(elevatorId)) {
                    if (thisElevatorMarkerId.equals(thatElevatorMarkerId)) continue;
                    Marker thatElevatorMarker = markerIdToMarker.get(thatElevatorMarkerId);
                    if (thatElevatorMarker == null) {
                        logger.error("elevator marker id:[{}] is not existed or not loaded. ", thatElevatorMarkerId);
                        continue;
                    }
                    String thatAgvMapId = thatElevatorMarker.getAgvMapName();
                    String pathId = UUID.randomUUID().toString();
                    SidePath sidePath1 = new SidePath(pathId, thisElevatorMarkerId, thatElevatorMarkerId, thisAgvMapId, 12D);
                    SidePath sidePath2 = new SidePath(pathId, thatElevatorMarkerId, thisElevatorMarkerId, thatAgvMapId, 12D);
                    sidePath1.setNavigationType(SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR);
                    sidePath2.setNavigationType(SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR);
                    virtualSidePathMap.put(sidePath1.getStartMarkerId() + sidePath1.getEndMarkerId(), sidePath1);
                    virtualSidePathMap.put(sidePath2.getStartMarkerId() + sidePath2.getStartMarkerId(), sidePath2);
                    sidePathIdToSidePath.put(sidePath1.getId(), sidePath1);
                    sidePathIdToSidePath.put(sidePath2.getId(), sidePath2);
                }
            }
        }
        trafficManager.addSidePathResources(virtualSidePathMap.values());
        //进入电梯代价较大
        addSidePath(virtualSidePathMap.values(), 0.1D, 0.1D);
    }

    /**
     * 获取进入电梯点相邻点
     *
     * @param inOutElevatorMarkerId
     * @return
     */
    public Set<String> getElevatorAdjacentMarkerIds(String inOutElevatorMarkerId, String elevatorMarkerId) {
        if (StringUtils.isBlank(inOutElevatorMarkerId) || StringUtils.isBlank(elevatorMarkerId)) {
            return null;
        }
        Set<String> inOutMarkerIds = new HashSet<>();
        Set<String> adjacentMarkerIds = null;
        String removeAjd = elevatorMarkerId;
        inOutMarkerIds.add(inOutElevatorMarkerId);
        while (true) {
            adjacentMarkerIds = dGraphOfAGVMap.getAdjacentMarkerIds(inOutElevatorMarkerId);
//            if (adjacentMarkerIds.size() > 2) {
//                break;
//            } else
            if (adjacentMarkerIds.size() == 2) {
                inOutMarkerIds.addAll(adjacentMarkerIds);
                adjacentMarkerIds.remove(removeAjd);
                removeAjd = inOutElevatorMarkerId;
                inOutElevatorMarkerId = adjacentMarkerIds.iterator().next();
            } else {
                break;
            }
        }
        return inOutMarkerIds;
    }

    /**
     * 初始化电梯数据
     *
     * @param floors
     */
    private synchronized void initFloor(List<Floor> floors) {
        setMapChanged();
        List<Elevator> elevatorAll = elevatorService.findAll();
        elevatorAll.forEach(elevator -> elevatorIdToElevator.put(elevator.getId(), elevator));
        //处理楼层信息，保存地图对应的电梯点
        floors.forEach(floor -> {
            //存储elevatorMarkerId和电梯ElevatorId的对应关系
            elevatorMarkerIdToElevatorId.put(floor.getMarkerId(), floor.getElevatorId());
            //存储电梯Id对应的多个markerId
            elevatorIdToElevatorMarkerIds.computeIfAbsent(floor.getElevatorId(), value -> new HashSet<>());
            elevatorIdToElevatorMarkerIds.get(floor.getElevatorId()).add(floor.getMarkerId());
        });
        addElevatorVirtualSidePath();
        initElevatorResource();
    }

    /**
     * 初始化楼层数据不单独加载电梯
     *
     * @param floors
     */
    private synchronized void initFloorOutElevatorResource(String agvMapId,List<Floor> floors) {
        setMapChanged(agvMapId);
        List<Elevator> elevatorAll = elevatorService.findAll();
        elevatorAll.forEach(elevator -> elevatorIdToElevator.put(elevator.getId(), elevator));
        //处理楼层信息，保存地图对应的电梯点
        floors.forEach(floor -> {
            //存储elevatorMarkerId和电梯ElevatorId的对应关系
            elevatorMarkerIdToElevatorId.put(floor.getMarkerId(), floor.getElevatorId());
            //存储电梯Id对应的多个markerId
            elevatorIdToElevatorMarkerIds.computeIfAbsent(floor.getElevatorId(), value -> new HashSet<>());
            elevatorIdToElevatorMarkerIds.get(floor.getElevatorId()).add(floor.getMarkerId());
        });
    }

    public synchronized void initFloor() {
        setMapChanged();
        //处理楼层信息，保存地图对应的电梯点
        elevatorMarkerIdToElevatorId.clear();
        elevatorIdToElevatorMarkerIds.clear();
        elevatorIdToElevator.clear();
        List<Floor> floors = floorService.findAll();
        initFloor(floors);
    }

    private void checkAndRepairData(String agvMapId, List<SidePath> sidePaths) {
        Set<SidePath> changSidePaths = new HashSet<>();
        for (SidePath sidePath : sidePaths) {
            if (sidePath.getInAngle() == null || sidePath.getOutAngle() == null) {
                Double[] inOutAngle = SidePathUtils.getInOutAngle(sidePath, false);
                sidePath.setInAngle(inOutAngle[0]);
                sidePath.setOutAngle(inOutAngle[1]);
                //sidePathService.update(sidePath);
                changSidePaths.add(sidePath);
            }
            if (sidePath.getLength() == null) {
                Double length = SidePathUtils.getLength(sidePath);
                sidePath.setLength(length);
                //sidePathService.update(sidePath);
                changSidePaths.add(sidePath);
            }
        }
        if (!changSidePaths.isEmpty()) {
            sidePathService.batchUpdate(agvMapId, new ArrayList<>(changSidePaths), false);
        }
    }

    public List<Marker> getMarkersByAGVMapId(String agvMapId) {
        MapElements mapElements = activeAgvMapIds.get(agvMapId);
        if (mapElements != null) return mapElements.getMarkers();
        else return null;
    }

    public Set<String> getAdjacentMarkerIds(String markerId) {
        Set<String> adjacentMarkerIds = dGraphOfAGVMap.getAdjacentMarkerIds(markerId);
        if (adjacentMarkerIds != null) {
            return adjacentMarkerIds;
        } else {
            return new HashSet<>();
        }
    }

    public List<SidePath> getSidePathsByAGVMapId(String agvMapId) {
        MapElements mapElements = activeAgvMapIds.get(agvMapId);
        if (mapElements != null) return mapElements.getSidePaths();
        else return null;
    }

    private synchronized void setMapChanged() {
        try {
            DistanceMatrixUtil.setNeedToReload();
            checkAndSendPathService.clear();
        } catch (Exception e) {
            logger.error("setMapChanged error", e);
        }
    }

    private synchronized void setMapChanged(String agvMapId) {
        try {
            DistanceMatrixUtil.setNeedToReload();
//            checkAndSendPathService.clear(agvMapId);
        } catch (Exception e) {
            logger.error("setMapChanged error", e);
        }
    }

    //清除所有地图
    public synchronized void clean() {
        setMapChanged();
        markerIdToMarker.clear();
        sidePathIdToSidePath.clear();
        activeAgvMapIds.clear();
        elevatorMarkerIdToElevatorId.clear();
        startEndMarkerIdToSidePath.clear();
        elevatorIdToElevator.clear();
        elevatorIdToElevatorMarkerIds.clear();
        markerIdToMarkerNavigationType.clear();
        activeMarkers.clear();
        disableMarkers.clear();
        disableSidePaths.clear();
        dGraphOfAGVMap.init();
    }

    /**
     * 获得路径上的限制车型
     * @param pathId
     * @return
     */
    public final  List<String> getJamAgvTypes(String pathId){
    	List<String> list = MapUtils.getObject(pathIdToJamAGVTypes , pathId,  Lists.newArrayList()) ;
    	list = list.parallelStream().filter(p -> StringUtils.isNotBlank(p)).collect(Collectors.toList());
    	return list;
    }
}
