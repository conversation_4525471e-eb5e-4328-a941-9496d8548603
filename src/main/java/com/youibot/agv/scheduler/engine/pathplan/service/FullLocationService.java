package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.engine.pathplan.entity.Position;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.PositionMessage;

import java.util.Collection;
import java.util.List;

/**
 * TODO 需要重构
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-22 20:01
 */
public interface FullLocationService {

    VehicleLocation getVehicleLocation(String vehicleId);

    Position getAgvPosition(String agvCode);
}
