package com.youibot.agv.scheduler.engine.pathplan.service.impl;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.pathplan.entity.ConflictProcessMonitor;
import com.youibot.agv.scheduler.engine.pathplan.entity.ModifySidePathWeight;
import com.youibot.agv.scheduler.engine.pathplan.entity.SendSidePathMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;

/**
 * <AUTHOR>
 * @Date :Created in 下午4:19 2020/12/29
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
@Lazy
@Service
public class PathPlanAndSendPathLogServiceImpl {

    private static File pathWeightFile = null;
    private static FileOutputStream pathWeightFileOutputStream = null;
    private static File conflictProcessFile = null;
    private static FileOutputStream conflictProcessFileOutputStream = null;
    private static File sendSidePathFile = null;
    private static FileOutputStream sendSidePathFileOutputStream = null;

    @Value("${PATH_PLAN.PRINT_LOG_OF_MODIFY_PATH_WEIGHT}")
    private boolean printLogOfModifyPathWeight;

    @Value("${PATH_PLAN.LOG_FILE_OF_MODIFY_PATH_WEIGHT}")
    private String logFileOfModifyPathWeight;

    @Value("${PATH_PLAN.PRINT_LOG_OF_CONFLICT_PROCESS}")
    private boolean printLogOfConflictProcess;

    @Value("${PATH_PLAN.LOG_FILE_OF_CONFLICT_PROCESS}")
    private String logFileOfConflictProcess;

    @Value("${PATH_PLAN.PRINT_LOG_OF_SEND_SIDE_PATH}")
    private boolean printLogOfSendSidePath;

    @Value("${PATH_PLAN.LOG_FILE_OF_SEND_SIDE_PATH}")
    private String logFileOfSendSidePath;

    public void saveModifySidePathWeight(ModifySidePathWeight modifySidePathWeight) {
        try {
            if (printLogOfModifyPathWeight) {
                if (pathWeightFile == null || pathWeightFileOutputStream == null) {
                    pathWeightFile = new File(logFileOfModifyPathWeight);
                    pathWeightFileOutputStream = new FileOutputStream(pathWeightFile);
                }
                if (!pathWeightFile.exists()) {
                    pathWeightFile.createNewFile();
                    pathWeightFileOutputStream = new FileOutputStream(pathWeightFile);
                }
                if (modifySidePathWeight != null) {
                    String str = JSON.toJSONString(modifySidePathWeight);
                    pathWeightFileOutputStream.write(str.getBytes());
                    pathWeightFileOutputStream.write(System.lineSeparator().getBytes());
                    pathWeightFileOutputStream.flush();
                }
            }
        } catch (Exception e) {
            log.error("{}", e);
        }
    }

    public void saveConflictProcessLog(ConflictProcessMonitor conflictProcessMonitor) {
        try {
            if (printLogOfConflictProcess) {
                if (conflictProcessFile == null || conflictProcessFileOutputStream == null) {
                    conflictProcessFile = new File(logFileOfConflictProcess);
                    conflictProcessFileOutputStream = new FileOutputStream(conflictProcessFile);
                }
                if (!conflictProcessFile.exists()) {
                    conflictProcessFile.createNewFile();
                    conflictProcessFileOutputStream = new FileOutputStream(conflictProcessFile);
                }
                if (conflictProcessMonitor != null) {
                    String str = JSON.toJSONString(conflictProcessMonitor);
                    conflictProcessFileOutputStream.write(str.getBytes());
                    conflictProcessFileOutputStream.write(System.lineSeparator().getBytes());
                    conflictProcessFileOutputStream.flush();
                }
            }
        } catch (Exception e) {
            log.error("{}", e);
        }
    }

    public void saveSendSidePathLog(SendSidePathMessage sendSidePathMessage) {
        try {
            if (printLogOfSendSidePath) {
                if (sendSidePathFile == null) {
                    sendSidePathFile = new File(logFileOfSendSidePath);
                    sendSidePathFileOutputStream = new FileOutputStream(sendSidePathFile);
                }
                if (!sendSidePathFile.exists()) {
                    sendSidePathFile.createNewFile();
                    sendSidePathFileOutputStream = new FileOutputStream(sendSidePathFile);
                }
                if (sendSidePathMessage != null) {
                    String str = JSON.toJSONString(sendSidePathMessage);
                    sendSidePathFileOutputStream.write(str.getBytes());
                    sendSidePathFileOutputStream.write(System.lineSeparator().getBytes());
                    sendSidePathFileOutputStream.flush();
                }
            }
        } catch (Exception e) {
            log.error("{}", e);
        }
    }
}
