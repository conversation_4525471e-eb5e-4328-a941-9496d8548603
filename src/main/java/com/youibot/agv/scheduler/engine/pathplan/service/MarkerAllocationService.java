package com.youibot.agv.scheduler.engine.pathplan.service;

import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerScope;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.vehicle.Vehicle;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 8/27/21 8:30 PM
 */
public interface MarkerAllocationService {

    // 无机器人占用，未被分配，不是他机器人路径规划目标点，不在其他机器人规划的目标点的单机区域内。并且移除掉自己的路径规划目标点。
    List<Marker> getVehicleEnablePark(Vehicle vehicle, String agvMapId);

    /**
     * 获得当前机器人可用的泊车点并且评分。
     * 就近排序，获取最近的可用的泊车站点。
     *
     * @param vehicle
     * @return
     */
    MarkerScope getParkMarkerByVehicle(Vehicle vehicle);
    
    /**
     * 获得当前机器人可用的泊车点并且评分。
     * 就近排序，获取最近的可用的避难泊车站点。
     *
     * @param vehicle
     * @param shelter
     * @return
     */
    MarkerScope getParkMarkerByVehicle(Vehicle vehicle , boolean shelter);

    /**
     * 从充电点中获取评分最高的充电点。如果没有则不分配。
     * 评分规则：距离最短的空闲充电点评分最高。
     * 评分规则：
     * 1：空闲充电点，直接scope+500分。
     * 2：有充电的机器人，但机器电量<CANCEL_CHARGE_BATTERY_VALUE。则不评分。否则scope+充电机器人电量）
     * 3：跟离scope-(距离评分)
     */
    MarkerScope getChargeMarkerFromAll(Vehicle vehicle);

    /**
     * 从所有的空闲的充电点中获取评分最高的充电点。如果没有则不分配。
     * 评分规则：距离最短的空闲充电点评分最高。
     */
    MarkerScope getChargeMarkerFromEnable(Vehicle vehicle);


    /**
     * 从所有可用的避让点中获取最近的可用的避让点。
     * 评分规则：距离最短的空闲避让点评分最高。
     * @param vehicle
     * @return
     */
    MarkerScope getAvoidMarkerFromEnable(Vehicle vehicle);

}
