package com.youibot.agv.scheduler.engine.pathplan.agvmapgraph;

import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.Queue;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date :Created in 下午1:11 2021/1/7
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
@Component
public class DistanceMatrix {

    private Map<String, Map<String, Double>> distanceMatrix = new HashMap<>();
//    private Map<String, Map<String, Double>> distanceCodeMatrix = new HashMap<>();
    private Map<DirectedNode, Map<DirectedNode, Double>> nodeDistanceMatrix = new HashMap<>();

    public Map<String, Map<String, Double>> getDistanceMatrix() {
        return distanceMatrix;
    }

    public void setDistanceMatrix(Map<String, Map<String, Double>> distanceMatrix) {
        this.distanceMatrix = distanceMatrix;
    }

    /**
     * 传入的为有向加权图
     * 将使用dijkstra初始化distanceMatrix，该矩阵保存了从i到j的最短距离
     *
     * @param dGraph
     */
    public void distanceMatrixInit(DirectedGraph dGraph) {
        if (dGraph == null) {
            log.error("传入的加权有向图为null，无地图数据。");
            return;
        }
        Collection<DirectedNode> nodes = dGraph.getNodeMap().values();
        for (DirectedNode node : nodes) {
            computeShortestPath(node, dGraph);
        }
        for (Map.Entry<DirectedNode, Map<DirectedNode, Double>> directedNodeMapEntry : nodeDistanceMatrix.entrySet()) {
            DirectedNode startMarkerNode = directedNodeMapEntry.getKey();
            for (Map.Entry<DirectedNode, Double> directedNodeDoubleEntry : directedNodeMapEntry.getValue().entrySet()) {
                DirectedNode endMarkerNode = directedNodeDoubleEntry.getKey();
                Double distance = directedNodeDoubleEntry.getValue();
                distanceMatrix.computeIfAbsent(startMarkerNode.getId(), k -> new HashMap<>());
//                distanceCodeMatrix.computeIfAbsent(startMarkerNode.getCode() , k -> new HashMap<>());
                distanceMatrix.get(startMarkerNode.getId()).put(endMarkerNode.getId(), distance);
//                distanceCodeMatrix.get(startMarkerNode.getCode()).put(endMarkerNode.getCode(), distance);
            }
        }
      
        nodeDistanceMatrix.clear();
    }

    private void computeShortestPath(DirectedNode start, DirectedGraph dGraph) {
        Collection<DirectedNode> nodes = dGraph.getNodeMap().values();
        Set<DirectedEdge> allEdges = dGraph.getAllEdges();

        PriorityQueue<Pair<DirectedNode, Double>> minHeap = new PriorityQueue<>(new DistanceMatrix.NodeComparator());//节点的优先队列
        Map<DirectedNode, DirectedNodeProperty> nodePropertyMap = new HashMap<>();//节点及其属性映射表
        Map<DirectedEdge, DirectedEdgeProperty> edgePropertyMap = new HashMap<>();//边及其属性映射表

        //init data
        for (DirectedNode node : nodes) {
            nodePropertyMap.put(node, new DirectedNodeProperty());
        }
        for (DirectedEdge edge : allEdges) {
            edgePropertyMap.put(edge, new DirectedEdgeProperty(edge));
        }

        //computeShortestPath
        Queue<DirectedNode> S = new LinkedList<>();
        Map<DirectedNode, Double> dist = new HashMap<>();
        for (DirectedNode node : nodes) {
            if (!node.getId().equals(start.getId())) {
                dist.put(node, Double.POSITIVE_INFINITY);
            } else {
                dist.put(node, 0d);
            }
        }
        minHeap.add(new Pair<>(start, 0d));

        while (minHeap.size() > 0) {
            Pair<DirectedNode, Double> poll = minHeap.poll();
            DirectedNode nodeV = poll.first();
            Double vCost = poll.second();
            if (S.contains(nodeV)) continue;
            else {
                S.add(nodeV);
                Set<DirectedNode> adjacentNodes = dGraph.getSuccessors(nodeV);
                for (DirectedNode nodeT : adjacentNodes) {
                    DirectedEdge edge = dGraph.getEdgeByStartEndNode(nodeV, nodeT);
                    
                    if (edge == null) continue;
                    Double vtCost = edge.getOriginWeight();
                    Double tCost = dist.get(nodeT);
//                    if(StringUtils.equals(nodeT.getCode(), "116") && StringUtils.equals(start.getCode(), "52")) {
//                    	System.err.println("test");
//                    	System.err.println("test");
//                    	System.err.println("test");
//                    	System.err.println("");
//                    	PathAgvType pathAgvType = TjdCxt.pathAgvType(edge.getPathId());
//                    	System.err.println("pathAgvType" + pathAgvType);
//                    }
                    if (vCost + vtCost < tCost) {
                        Double v_vt = vCost + vtCost;
                        /**
                         * 台积电定制添加，暂时未添加agvCode
                         */
                        MapGraph mapGraph = MapGraphUtil.getMapGraph();
                      
                        List<String> jamAgvTypes = mapGraph.getJamAgvTypes(edge.getPathId());
                        if(CollectionUtils.isNotEmpty(jamAgvTypes)) {
                        	v_vt = Double.POSITIVE_INFINITY;
                        }
                        dist.put(nodeT, v_vt);
                    
                        minHeap.add(new Pair<>(nodeT, v_vt));
                        if (getParent(nodePropertyMap, nodeV) != null && getParent(nodePropertyMap, nodeV).equals(nodeT)) {
                            setParent(nodePropertyMap, nodeV, null);
                        }
                        setParent(nodePropertyMap, nodeT, nodeV);
                    }
                }
            }
        }
        nodeDistanceMatrix.put(start, dist);
    }

    private DirectedNode getParent(Map<DirectedNode, DirectedNodeProperty> nodePropertyMap, DirectedNode node) {
        return nodePropertyMap.get(node).parent;
    }

    private void setParent(Map<DirectedNode, DirectedNodeProperty> nodePropertyMap, DirectedNode node, DirectedNode value) {
        nodePropertyMap.get(node).parent = value;
    }

    static class NodeComparator implements Comparator<Pair<DirectedNode, Double>> {
        @Override
        public int compare(Pair<DirectedNode, Double> n1, Pair<DirectedNode, Double> n2) {
            return n1.second() < n2.second() ? -1 : 1;
        }
    }

}
