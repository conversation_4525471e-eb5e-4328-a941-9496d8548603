package com.youibot.agv.scheduler.engine.service.elevator;

import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.youibot.agv.scheduler.engine.entity.ElevatorFloor;

import java.io.IOException;

/**
 * 电梯api服务
 *
 * <AUTHOR>  E-mail:shis<PERSON><EMAIL>
 * @version CreateTime: 2020/7/30 19:29
 */
public interface ElevatorApiService {

    /**
     * 申请电梯
     *
     * @param floor
     */
    void elevatorApply(ElevatorFloor floor, ModbusMaster modbusMaster) throws IOException, InterruptedException;

    /**
     * AGV要进入电梯打开电梯门
     *
     * @param floor
     */
    void elevatorOpenByComeIn(ElevatorFloor floor,ModbusMaster modbusMaster) throws InterruptedException;

    /**
     * AGV进入电梯后关闭电梯门
     *
     * @param floor
     */
    void elevatorCloseByComeIn(ElevatorFloor floor,ModbusMaster modbusMaster) throws IOException, InterruptedException;


    /**
     * 移动电梯
     *
     * @param floor
     */
    void elevatorMove(ElevatorFloor floor);

    /**
     * AGV要出来电梯打开电梯门
     *
     * @param floor
     */
    void elevatorOpenByComeOut(ElevatorFloor floor,ModbusMaster modbusMaster) throws InterruptedException;

    /**
     * AGV出来电梯后关闭电梯门
     *
     * @param floor
     */
    void elevatorCloseByComeOut(ElevatorFloor floor,ModbusMaster modbusMaster) throws InterruptedException;

    /**
     * 释放电梯
     *
     * @param floor
     */
    void elevatorRelease(ElevatorFloor floor) throws IOException, InterruptedException;
}
