package com.youibot.agv.scheduler.engine.service.elevator;

import com.youibot.agv.scheduler.engine.entity.ElevatorFloor;

import java.io.IOException;

/**
 * 电梯api服务
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/7/30 19:29
 */
public interface ElevatorApiService {

    /**
     * 申请电梯
     * @param floor
     */
    void elevatorApply(ElevatorFloor floor) throws IOException;

    /**
     * AGV要进入电梯打开电梯门
     * @param floor
     */
    void elevatorOpenByComeIn(ElevatorFloor floor) throws InterruptedException;

    /**
     * AGV进入电梯后关闭电梯门
     * @param floor
     */
    void elevatorCloseByComeIn(ElevatorFloor floor) throws IOException;


    /**
     * 移动电梯
     * @param floor
     */
    void elevatorMove(ElevatorFloor floor);

    /**
     * AGV要出来电梯打开电梯门
     * @param floor
     */
    void elevatorOpenByComeOut(ElevatorFloor floor) throws InterruptedException;

    /**
     * AGV出来电梯后关闭电梯门
     * @param floor
     */
    void elevatorCloseByComeOut(ElevatorFloor floor);

    /**
     * 释放电梯
     * @param floor
     */
    void elevatorRelease(ElevatorFloor floor) throws IOException;
}
