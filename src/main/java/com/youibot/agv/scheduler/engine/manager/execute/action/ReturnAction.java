package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.youibot.agv.scheduler.engine.manager.execute.missionwork.MissionWorkThreadPool;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.AGVConstant.TASK_STATUS_FREE;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;

/**
 * <AUTHOR>  E-mail:shis<PERSON><PERSON>@youibot.com
 * @version CreateTime: 2020/9/11 16:03
 */
@Service
@Scope("prototype")
public class ReturnAction extends DefaultAction {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReturnAction.class);

    @Autowired
    private MissionWorkThreadPool missionWorkThreadPool;

    @Autowired
    private MissionWorkService missionWorkService;

    @Override
    public Map<String, Object> execute(Vehicle vehicle) throws InterruptedException {
        LOGGER.debug("执行任务停止动作....");
        this.vehicle = vehicle;
        //如果没有空闲充电点，停止任务
        MissionWork missionWork = vehicle.getMissionWork();
        if (missionWork == null) {
            throw new ExecuteException("missionWork is null");
        }
        vehicle.setSidePaths(new LinkedList<>());
        missionWorkThreadPool.detach(missionWorkId);
        missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SHUTDOWN);//将missionWork状态置为停止
        vehicle.setWorkStatus(TASK_STATUS_FREE);
        this.vehicle.setMissionWork(null);
        return null;
    }
}
