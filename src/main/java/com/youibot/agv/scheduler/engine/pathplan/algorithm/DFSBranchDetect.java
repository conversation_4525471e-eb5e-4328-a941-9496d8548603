package com.youibot.agv.scheduler.engine.pathplan.algorithm;

import com.sun.javafx.binding.StringFormatter;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedEdge;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.DirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.GraphsUtils;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedGraph;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date :Created in 17:11 2021/3/9
 * @Description :
 * @Modified By :
 * @Version :
 */
@Slf4j
public class DFSBranchDetect {

    private UndirectedGraph graph = new UndirectedGraph();                                          //输入的无向图
    private DirectedGraph dGraph = new DirectedGraph();                                             //输入的有向图
    private final List<List<String>> tPaths = new ArrayList<>();                            //有去无回路
    private final List<List<String>> singlePlankPath = new ArrayList<>();                           //独木桥
    private final List<List<String>> oPaths = new ArrayList<>();              //独木桥双向的
    private final List<List<String>> singlePlankPathUnidirectional = new ArrayList<>();             //独木桥单向的

    public DFSBranchDetect() {
    }

    public void setGraph(DirectedGraph dGraph) {
        this.dGraph = dGraph;
        this.graph = GraphsUtils.convertGraph(dGraph);
    }

    private String printLoop(ArrayList<String> list) {
        StringBuilder sb = new StringBuilder();
        for (String s : list) {
            sb.append(StringFormatter.format("[%s]->", s));
        }
        return sb.toString();
    }

    //检测断头路和独木桥支线
    public void DFSTraverse() {
        tPaths.clear();
        singlePlankPath.clear();
        oPaths.clear();
        singlePlankPathUnidirectional.clear();

        HashSet<String> nodeIds = new HashSet<>(graph.getNodeMap().keySet());
        //度大于二的节点
        List<String> degreeNodeIds = new ArrayList<>();
        for (String nodeId : nodeIds) {
            Set<String> ids = graph.getAdjacentNodeIds(nodeId);
            if (ids.size() > 2) {
                degreeNodeIds.add(nodeId);
            }
        }
        List<String> path = new ArrayList<>();
        List<String> visited = new ArrayList<>();
        for (String degreeNodeId : degreeNodeIds) {
            path.add(degreeNodeId);
            visited.add(degreeNodeId);
            Set<String> adjacentNodeIds = graph.getAdjacentNodeIds(degreeNodeId);
            for (String adjacentNodeId : adjacentNodeIds) {
                if (degreeNodeIds.contains(adjacentNodeId)) continue;
                path.add(adjacentNodeId);
                DFS(adjacentNodeId, path, visited);
                path.remove(adjacentNodeId);
            }
            path.remove(degreeNodeId);
        }
        for (List<String> singlePlank : singlePlankPath) {
            boolean bidirectional = true;
            for (int i = 0; i < singlePlank.size() - 1; i++) {
                String markerId1 = singlePlank.get(i);
                String markerId2 = singlePlank.get(i + 1);
                DirectedEdge forward = dGraph.getEdgeByStartEndNode(markerId1, markerId2);
                DirectedEdge reverse = dGraph.getEdgeByStartEndNode(markerId2, markerId1);
                if (forward == null || reverse == null) {
                    bidirectional = false;
                    break;
                }
            }
            if (bidirectional) {
                oPaths.add(singlePlank);
            } else {
                singlePlankPathUnidirectional.add(singlePlank);
            }

        }
    }

    private void DFS(String nodeId, List<String> path, List<String> visited) {
        if (visited.contains(nodeId)) return;
        Set<String> adjacentNodeIds = graph.getAdjacentNodeIds(nodeId);
        if (CollectionUtils.isEmpty(adjacentNodeIds) || adjacentNodeIds.size() < 2) {
            tPaths.add(new ArrayList<>(path));
            return;
        } else if (adjacentNodeIds.size() > 2) {
            if (path.size() > 2) {
                singlePlankPath.add(new ArrayList<>(path));
            }
            return;
        } else {
            for (String adjacentNodeId : adjacentNodeIds) {
                if (path.contains(adjacentNodeId)) continue;
                path.add(adjacentNodeId);
                DFS(adjacentNodeId, path, visited);
                path.remove(adjacentNodeId);
            }
        }
    }

    public List<List<String>> getTPaths() {
        return tPaths;
    }

    public List<List<String>> getOPaths() {
        return oPaths;
    }
}