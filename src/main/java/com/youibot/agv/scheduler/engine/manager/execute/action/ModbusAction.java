package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.serotonin.modbus4j.Modbus;
import com.youibot.agv.scheduler.constant.ActionConstant;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.UnknownHostException;
import java.util.Map;

public abstract class ModbusAction extends DefaultAction {

    private Logger LOGGER = LoggerFactory.getLogger(ModbusAction.class);

    static final Integer MAX_VALUE = 32767;//最大输入值

    static final Integer MIN_VALUE = 0;//最小输入值

    public Map<String, Object> switchFunctionCode(JSONObject paramJson) throws InterruptedException, UnknownHostException {
        Map<String, Object> resultData;
        String code = paramJson.getString("code");
        paramJson.put("numberOfBits", 1);
        switch (code) {
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_01:
                resultData = readFunction_01(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_02:
                resultData = readFunction_02(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_03:
                resultData = readFunction_03(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_READ_04:
                resultData = readFunction_04(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_05:
                resultData = writeFunction_05(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_06:
                resultData = writeFunction_06(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_15:
                resultData = writeFunction_15(paramJson);
                break;
            case ActionConstant.REMOTE_PLC_FUNCTION_WRITE_16:
                resultData = writeFunction_16(paramJson);
                break;
            default:
                throw new ExecuteException(MessageUtils.getMessage("action.wrong_value") + " mode=" + code);
        }
        return resultData;
    }

    public boolean[] readCoilStatusValue(ModbusMaster modbusMaster, String code, Integer slaveId, Integer startAddress, Integer numberOfBits) {
        try {
            if (Modbus.DEFAULT_MAX_READ_REGISTER_COUNT < numberOfBits) {
                throw new ADSParameterException(MessageUtils.getMessage("action.wrong_value"));
            }
            try {
                if (ActionConstant.REMOTE_PLC_FUNCTION_READ_01.equals(code)) {
                    return modbusMaster.readCoils(slaveId, startAddress, numberOfBits);
                } else if (ActionConstant.REMOTE_PLC_FUNCTION_READ_02.equals(code)) {
                    return modbusMaster.readDiscreteInputs(slaveId, startAddress, numberOfBits);
                }
            } catch (RuntimeException re) {
                LOGGER.error("读取modbus失败,请检查当前寄存器是否支持该功能码code={} ip={} port={} 错误信息", code, re);
                throw new ExecuteException(ErrorEnum.READ_MODBUS_RESULT_NULL.code(), ErrorEnum.READ_MODBUS_RESULT_NULL.msg());
            }
        } catch (Exception e) {
            throw new ExecuteException(ErrorEnum.MODBUS_COMMUNICATION_FAIL.code(), ErrorEnum.MODBUS_COMMUNICATION_FAIL.msg());
        }
        return null;
    }

    public int[] readShortValue(ModbusMaster master, String code, Integer slaveId, Integer
            startAddress, Integer numberOfBits) {
        if (Modbus.DEFAULT_MAX_READ_REGISTER_COUNT < numberOfBits) {
            throw new ADSParameterException(MessageUtils.getMessage("action.wrong_value"));
        }
        try {
            if (ActionConstant.REMOTE_PLC_FUNCTION_READ_03.equals(code)) {
                return master.readHoldingRegisters(slaveId, startAddress, numberOfBits);
            } else if (ActionConstant.REMOTE_PLC_FUNCTION_READ_04.equals(code)) {
                return master.readInputRegisters(slaveId, startAddress, numberOfBits);
            }
        } catch (Exception e) {
            LOGGER.error("寄存器读取失败, code:{}", code, e);
            throw new ExecuteException(ErrorEnum.READ_MODBUS_RESULT_NULL.code(), ErrorEnum.READ_MODBUS_RESULT_NULL.msg());
        }
        return null;
    }

    public abstract Map<String, Object> readFunction_01(JSONObject paramJson) throws UnknownHostException, InterruptedException;

    public abstract Map<String, Object> readFunction_02(JSONObject paramJson) throws UnknownHostException, InterruptedException;

    public abstract Map<String, Object> readFunction_03(JSONObject paramJson) throws UnknownHostException, InterruptedException;

    public abstract Map<String, Object> readFunction_04(JSONObject paramJson) throws UnknownHostException, InterruptedException;

    public abstract Map<String, Object> writeFunction_05(JSONObject paramJson) throws UnknownHostException;

    public abstract Map<String, Object> writeFunction_15(JSONObject paramJson) throws UnknownHostException;

    public abstract Map<String, Object> writeFunction_06(JSONObject paramJson) throws UnknownHostException;

    public abstract Map<String, Object> writeFunction_16(JSONObject paramJson) throws UnknownHostException;

}
