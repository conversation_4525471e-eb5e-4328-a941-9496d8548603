package com.youibot.agv.scheduler.engine.manager.execute.action;

import com.alibaba.fastjson.JSONObject;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import com.youibot.agv.scheduler.constant.ActionConstant;
import com.youibot.agv.scheduler.engine.manager.modbus.ModbusUtils;
import com.youibot.agv.scheduler.engine.util.NumericalUtils;
import com.youibot.agv.scheduler.entity.MissionWorkGlobalVariable;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.MissionWorkGlobalVariableService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Date;
import java.util.Map;

/**
 * modbus
 *
 * @Author：yangpeilin
 * @Date: 2020/4/25 16:47
 */
@Component
@Scope("prototype")
public class ModbusAction extends DefaultAction {

    @Autowired
    private MissionWorkGlobalVariableService missionWorkGlobalVariableService;

    Logger logger = LoggerFactory.getLogger(ModbusAction.class);

    @Value("${MODBUS.SLAVE_ID}")
    private Integer slaveId;

    @Value("${MODBUS.NUMBER_OF_BITS}")
    private Integer numberOfBits;

    @Override
    protected Map<String, Object> sendCommand() throws InterruptedException {
        if (Thread.interrupted()) {//如果当前线程被中断了
            throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
        }
        super.faultResendAllCount = 0;
        JSONObject paramJson = this.getParamJson();
        String ip = paramJson.getString("ip");
        Integer mode = paramJson.getInteger("mode");
        Integer port = paramJson.getInteger("port");
        Integer offset = paramJson.getInteger("offset");
        Map<String, Object> resultData = new HashedMap<>(2);
        if (ActionConstant.PLC_MODE_READ.equals(mode)) {//Modbus读
            Integer value = paramJson.getInteger("value");//动作设置的值
            Long time_out = paramJson.getLong("time_out");//超时时间
            String condition = paramJson.getString("condition");
            if (value == null || time_out == null || StringUtils.isEmpty(condition)) {
                throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
            }
            long startTime = System.currentTimeMillis();//开始时间
            int resultValue = readModbusValue(ip, port, offset);//读取modbus值
            while (!NumericalUtils.numericalComparison(condition, resultValue, value)) {//如果动作设置的值与读取plc的值不一致，继续读取
                if (Thread.interrupted()) {//如果当前线程被中断了
                    throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
                }
                Thread.sleep(500);
                if ((new Date().getTime() - startTime) / 1000 > time_out) {
                    throw new ExecuteException(MessageFormat.format(MessageUtils.getMessage("action.condition_does_not_hold"), time_out, resultValue, condition, value));//条件判断无法满足
                }
                resultValue = readModbusValue(ip, port, offset);
            }
            String variable_key = paramJson.getString("variable_key");
            MissionWorkGlobalVariable missionWorkGlobalVariable = missionWorkGlobalVariableService.selectByMissionWorkIdAndKey(missionWorkId, variable_key);
            if (missionWorkGlobalVariable != null) {
                missionWorkGlobalVariable.setVariableValue(Integer.toString(resultValue));
                missionWorkGlobalVariableService.update(missionWorkGlobalVariable);
            }
        } else if (ActionConstant.PLC_MODE_WRITE.equals(mode)) {//Modbus写
            Integer value = paramJson.getInteger("value");
            try {
                boolean result = ModbusUtils.writeCoil(ip, port, slaveId, offset, value == 1);
                if (!result) {
                    throw new ExecuteException(MessageUtils.getMessage("action.write_modbus_fail"));
                }
                resultData.put("result", true);
            } catch (ModbusTransportException | ModbusInitException e) {
                logger.error(e.getMessage());
                throw new ExecuteException(MessageUtils.getMessage("action.modbus_communication_failure"));
            }
        } else {
            throw new ExecuteException(MessageUtils.getMessage("action.wrong_value") + " mode=" + mode);
        }
        return resultData;
    }

    private int readModbusValue(String ip, Integer port, Integer offset) {
        int[] resultValues;
        try {
            resultValues = ModbusUtils.readIntStatus(ip, port, slaveId, offset, numberOfBits);
        } catch (ModbusTransportException | ModbusInitException e) {
            throw new ExecuteException(MessageUtils.getMessage("action.modbus_communication_failure"));
        }
        if (resultValues == null || resultValues.length == 0) {
            throw new ExecuteException(MessageUtils.getMessage("action.read_modbus_result_null"));
        }
        return resultValues[0];
    }

}
