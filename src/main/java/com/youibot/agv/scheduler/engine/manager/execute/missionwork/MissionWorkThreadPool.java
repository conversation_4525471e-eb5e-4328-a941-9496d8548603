package com.youibot.agv.scheduler.engine.manager.execute.missionwork;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-14 13:57
 */

@Component
public class MissionWorkThreadPool {

    private Map<String, MissionWorkThread> poolEntries = new HashMap<>();

    public MissionWorkThread get(String id) {
        if (poolEntries.containsKey(id)) {
            return poolEntries.get(id);
        }
        return null;
    }

    public void attach(MissionWorkThread thread) {
        poolEntries.put(thread.getMissionWork().getId(), thread);
    }

    public void detach(MissionWorkThread thread) {
        poolEntries.remove(thread.getMissionWork().getId());
    }

    public void detach(String missionWorkId) {
        poolEntries.remove(missionWorkId);
    }

    public List<MissionWorkThread> get(List<String> ids) {
        return null;
    }

    public List<MissionWorkThread> getAll() {
        return new ArrayList<MissionWorkThread>(this.poolEntries.values());
    }
}
