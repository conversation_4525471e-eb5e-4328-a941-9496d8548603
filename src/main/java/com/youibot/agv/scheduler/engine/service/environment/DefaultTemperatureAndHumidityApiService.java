package com.youibot.agv.scheduler.engine.service.environment;

import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.util.ActionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * api温湿度service
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/10/16 16:05
 */
@Service
public class DefaultTemperatureAndHumidityApiService extends DefaultApiService implements TemperatureAndHumidityApiService {

    @Value("${AGV_API_CODE.TEMPERATURE_AND_HUMIDITY}")
    private String temperatureAndHumidityApiCode;//读取温湿度apiCode

    @Override
    public Map<String, Object> queryTemperatureAndHumidity(String ip) throws IOException {
        return ActionUtils.sendInstruction(ip, temperatureAndHumidityApiCode, null);
    }
}
