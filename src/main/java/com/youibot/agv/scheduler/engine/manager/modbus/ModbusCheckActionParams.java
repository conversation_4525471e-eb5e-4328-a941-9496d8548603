package com.youibot.agv.scheduler.engine.manager.modbus;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * modbus 动作参数
 *
 * @author: huangguanxin
 * @date: 2020-10-19 20:16
 */
@Data
@NoArgsConstructor
public class ModbusCheckActionParams implements Serializable {

    private static String IS_LOCAL = "1";

    private String ip;

    private Integer port;

    private Integer slaveId;

    private String code;// 功能码

    private String isLocal;// 是否本地 1 是，0 不是

    private Integer startAddress;// 开始地址

    private Integer numberOfBits = 1;//读取长度, 目前长度只能为1

    private String errorCode;//错误码

    private String successCode;// 成功码

    private Long time_out;//超时时间 读操作

    public ModbusCheckActionParams(JSONObject paramJson) {
        String isLocal = paramJson.getString("isLocal");
        this.isLocal = isLocal;
        if (IS_LOCAL.equals(isLocal)) {
            this.port = AGVPropertiesUtils.getInteger("MODBUS.LOCAL_PORT");
            this.ip = AGVPropertiesUtils.getString("MODBUS.LOCAL_IP");
        } else {
            this.ip = paramJson.getString("ip");
            this.port = paramJson.getInteger("port");
        }
        this.slaveId = paramJson.getInteger("slaveId");
        this.code = paramJson.getString("code");
        this.startAddress = paramJson.getInteger("startAddress");
//        this.numberOfBits = paramJson.getInteger("numberOfBits");
        this.errorCode = paramJson.getString("errorCode");
        this.successCode = paramJson.getString("successCode");
        this.time_out = paramJson.getLong("timeOut");
    }

}
