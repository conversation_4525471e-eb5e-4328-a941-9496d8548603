package com.youibot.agv.scheduler.engine.service.robotScript;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.ErrorEnum;
import com.youibot.agv.scheduler.engine.manager.socket.AGVSocketClient;
import com.youibot.agv.scheduler.engine.service.DefaultApiService;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AbnormalPromptService;
import com.youibot.agv.scheduler.util.ActionUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.ACTION_EXECUTE_FAULT;
import static com.youibot.agv.scheduler.engine.constant.AGVApiReturnConstant.ACTION_EXECUTE_SUCCESS;

@Service
public class DefaultRobotScriptApiService extends DefaultApiService implements RobotScriptApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultRobotScriptApiService.class);

    @Value("${AGV_API_CODE.ROBOT_SCRIPT}")
    private String robotScriptApiCode;

    @Autowired
    private AbnormalPromptService abnormalPromptService;


    @Override
    public Map<String, Object> robotScript(String ip, Map<String, Object> param) throws IOException, InterruptedException {
        LOGGER.debug("ip : " + ip + " robotScript send data : " + JSON.toJSONString(param));
        AGVSocketClient client = null;
        try {
            client = AGVSocketClient.createAGVClient(ip, ActionUtils.getScriptActionPort());
        } catch (IOException e) {
            LOGGER.error("agv connection error, agv ip:" + ip);
            throw new IOException(MessageUtils.getMessage("socket.connection_failed") + " ip:" + ip);
        }
        ActionUtils.sendInstruction(client, robotScriptApiCode, JSONObject.toJSONString(param));
        Thread.sleep(500);
        while (true) {
            Thread.sleep(500);
            if (Thread.interrupted()) {//如果当前线程被中断了
                throw new InterruptedException(MessageUtils.getMessage("action.thread_interrupt"));
            }

            Map<String, Object> dataMap = ActionUtils.sendInstruction(client, AGVPropertiesUtils.getString("AGV_API_CODE.ROBOT_SCRIPT_STATUS"), null);
            LOGGER.debug("call agv query action status, ip :" + ip + ", resultData:" + dataMap);
            if (dataMap == null || dataMap.isEmpty()) {
                continue;//AGV刚开机没有执行过任务的情况下data为空
            }
            Integer status = (Integer) dataMap.get("status");//执行状态
            if (ACTION_EXECUTE_SUCCESS.equals(status)) {//执行成功
                LOGGER.debug("the action of agv is success");
                return dataMap;
            }
            if (ACTION_EXECUTE_FAULT.equals(status)) {
                LOGGER.error("the action of agv is fault");
                Integer error_code = (Integer) dataMap.get("error_code");//执行状态
                AbnormalPrompt abnormalByCode = abnormalPromptService.getAbnormalByCode(error_code);
                if (abnormalByCode != null) {
                    throw new ExecuteException(error_code, abnormalByCode.getAbnormalDescription());
                }
                throw new ExecuteException(error_code, "消息管理中找不到异常码" + error_code + "的配置信息");
            }
        }
    }
}
