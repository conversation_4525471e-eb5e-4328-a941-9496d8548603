package com.youibot.agv.scheduler.engine.pathplan.util;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.ApplicationUtils;

import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_CURVE;
import static com.youibot.agv.scheduler.constant.MapConstant.PATH_LINE_TYPE_STRAIGHT;

/**
 * <AUTHOR>
 * @Date :Created in 下午2:13 2020/12/23
 * @Description :
 * @Modified By :
 * @Version :
 */
public class PathUtils {

    private static MarkerService markerService = (MarkerService) ApplicationUtils.getBean("markerServiceImpl");

    public static Double getLength(Path path, Marker startMarker, Marker endMarker) {
        if (path == null) {
            return null;
        }
        Point[] points = new Point[4];
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double length = null;
        switch (path.getLineType()) {
            case PATH_LINE_TYPE_STRAIGHT: {
                Point[] line = new Point[2];
                line[0] = points[0];
                line[1] = points[3];
                length = LineUtil.getLength(line, 0D, 1D);
                break;
            }
            case PATH_LINE_TYPE_CURVE: {
                points[1] = JSON.parseObject(path.getStartControl(), Point.class);
                points[2] = JSON.parseObject(path.getEndControl(), Point.class);
                length = BezierUtil.getLength(points, 0D, 1D);
                break;
            }
        }
        return length;
    }
}
