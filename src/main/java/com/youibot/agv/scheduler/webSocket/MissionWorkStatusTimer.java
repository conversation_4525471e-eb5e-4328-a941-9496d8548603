package com.youibot.agv.scheduler.webSocket;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.webSocket.controller.MissionWorkStatusSocketController;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketSendCodeEnum;
import com.youibot.agv.scheduler.webSocket.module.SocketSendModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月28日 下午4:28:32
 */
//@Component
@Deprecated
public class MissionWorkStatusTimer {

//    @Autowired
//    private MissionWorkService missionWorkService;
//
//    private static final ObjectMapper mapper = new ObjectMapper();
//
//    // 发送missionWork运行状态 2s一次
//    @Scheduled(cron = "0/2 * * * * *")
//    public void executeMissionWorkTask() throws JsonProcessingException {
//        int count = MissionWorkStatusSocketController.getOnlineCount();
//        if (count > 0) {
//            List<String> status = new ArrayList<>();
////            status.add(MISSION_WORK_STATUS_START);
//            status.add(MISSION_WORK_STATUS_RUNNING);
//            status.add(MISSION_WORK_STATUS_PAUSE);
//            status.add(MISSION_WORK_STATUS_WAIT_INPUT);
//            List<MissionWork> missionWorks = missionWorkService.selectByStatusList(status, null);
//            SocketSendModel sendModel = new SocketSendModel();
//            Map<String, Object> map = new HashMap<>();
//            map.put("missionWorks", missionWorks);
//            sendModel.setCode(WebSocketSendCodeEnum.MISSIONWORK.getCode());
//            sendModel.setData(map);
//            String message = mapper.writeValueAsString(sendModel);
//            MissionWorkStatusSocketController.sendMessage(message);
//        }
//    }

}
