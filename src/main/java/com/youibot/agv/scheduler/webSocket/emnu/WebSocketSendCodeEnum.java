package com.youibot.agv.scheduler.webSocket.emnu;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月24日 下午5:07:51
 */
public enum WebSocketSendCodeEnum {

    MISSIONWORK("MI<PERSON><PERSON>WORK"),
    AGVS<PERSON>TUS("AGVSTATUS"),
    M<PERSON>VE<PERSON><PERSON><PERSON><PERSON><PERSON>("MOVEAC<PERSON>ONPATH"),
    R<PERSON><PERSON><PERSON>NGMAP("RECORDINGMAP"),
    LASERDATA("LASERDATA"),
    AUTOMOVE("AUTOMOVE"),
    NOTIFICATION("NOTIFICATION");

    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    WebSocketSendCodeEnum(String code) {
        this.code = code;
    }

    public static WebSocketSendCodeEnum getByCodeType(String code) {
        for (WebSocketSendCodeEnum enums : WebSocketSendCodeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

}
