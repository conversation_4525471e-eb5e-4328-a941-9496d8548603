package com.youibot.agv.scheduler.webSocket.controller;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.config.webSocket.MyEndpointConfigure;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketReceiveCodeEnum;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketSendCodeEnum;
import com.youibot.agv.scheduler.webSocket.module.SocketReceiveModel;
import com.youibot.agv.scheduler.webSocket.module.SocketSendModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月30日 下午6:39:39
 */
@RestController
@ServerEndpoint(value = "/hearBeat", configurator = MyEndpointConfigure.class)
public class HearBeatSocketController {

	private static final Logger LOGGER = LoggerFactory.getLogger(HearBeatSocketController.class);

	private static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();
	
	@OnOpen
	public void onOpen(Session session) {
		WEBSOCKET_SESSION.put(session.getId(), session);
		LOGGER.debug("Open a websocket. id={}", session.getId());
	}

	@OnClose
	public void onClose(Session session) {
		WEBSOCKET_SESSION.remove(session.getId());
		LOGGER.debug("Close a websocket.", session.getId());
	}

	@OnError
	public void onError(Throwable throwable, Session session) {
		String id = session.getId();
		LOGGER.error("WebSocketServer,Connection Exception : id = " + id + " , throwable = " + throwable.getMessage());
	}

	// 获取消息
	@OnMessage
	public void onMessage(String message) {
		LOGGER.debug("Receive a message from client: " + message);
		SocketReceiveModel receiveModel = new SocketReceiveModel();
		String code = receiveModel.getCode();
		Map<String, Object> data = receiveModel.getData();
		WebSocketReceiveCodeEnum codeEmnu = WebSocketReceiveCodeEnum.getByCode(code);
		switch (codeEmnu) {
		case HEARBEAT:
			hearBeat(data);
			break;
		default:
			break;
		}
	}
   //心跳检测
	private void hearBeat(Map<String, Object> data) {
		String hearBeat=(String) data.get("hearBeat");
		SocketSendModel sendModel = new SocketSendModel();
		Map<String, Object> map = new HashMap<>();
		map.put("hearBeat", hearBeat);
		sendModel.setCode(WebSocketSendCodeEnum.MISSIONWORK.getCode());
		sendModel.setData(map);
	    String message=JSON.toJSONString(sendModel);
	    sendMessage(message);
	}

	public static void sendMessage(String message) {
		for (Session session : WEBSOCKET_SESSION.values()) {
			if (session.isOpen()) {
				try {
					session.getBasicRemote().sendText(message);
				} catch (IOException e) {
					LOGGER.debug("Send message error ,sessionId=" + session.getId() + "message" + message);
				}
			}
		}
	}

}
