package com.youibot.agv.scheduler.webSocket.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.config.webSocket.MyEndpointConfigure;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketReceiveCodeEnum;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketSendCodeEnum;
import com.youibot.agv.scheduler.webSocket.module.SocketReceiveModel;
import com.youibot.agv.scheduler.webSocket.module.SocketSendModel;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月30日 下午6:39:39
 */
@RestController
@ServerEndpoint(value = "/hearBeat", configurator = MyEndpointConfigure.class)
public class HearBeatSocketController {

    private static final Logger LOGGER = LoggerFactory.getLogger(HearBeatSocketController.class);

    private static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();

    @Value("${WEB_SOCKET.MAX_IDLE_TIMEOUT}")
    private Integer maxIdleTimeout;

    @OnOpen
    public void onOpen(Session session) {
        session.setMaxIdleTimeout(maxIdleTimeout);
        WEBSOCKET_SESSION.put(session.getId(), session);
    }

    @OnClose
    public void onClose(Session session) {
        WEBSOCKET_SESSION.remove(session.getId());
    }

    @OnError
    public void onError(Throwable throwable, Session session) {
        try {
            LOGGER.warn("web socket error and close.", throwable.getMessage());
            session.close();
        } catch (Exception e) {
            LOGGER.warn("web socket close error.", throwable.getMessage());
        }
    }

    // 获取消息
    @OnMessage
    public void onMessage(String message) {
//		LOGGER.debug("Receive a message from client: " + message);
        SocketReceiveModel receiveModel = new SocketReceiveModel();
        String code = receiveModel.getCode();
        Map<String, Object> data = receiveModel.getData();
        WebSocketReceiveCodeEnum codeEmnu = WebSocketReceiveCodeEnum.getByCode(code);
        switch (codeEmnu) {
            case HEARBEAT:
                hearBeat(data);
                break;
            default:
                break;
        }
    }

    //心跳检测
    private void hearBeat(Map<String, Object> data) {
        String hearBeat = (String) data.get("hearBeat");
        SocketSendModel sendModel = new SocketSendModel();
        Map<String, Object> map = new HashMap<>();
        map.put("hearBeat", hearBeat);
        sendModel.setCode(WebSocketSendCodeEnum.MISSIONWORK.getCode());
        sendModel.setData(map);
        String message = JSON.toJSONString(sendModel);
        sendMessage(message);
    }

    public static void sendMessage(String message) {
        for (Session session : WEBSOCKET_SESSION.values()) {
            if (session.isOpen()) {
                try {
                    session.getBasicRemote().sendText(message);
                } catch (Exception e) {
                    LOGGER.warn("web socket send error：{}", e);
                    try {
                        session.close();
                    } catch (IOException ioe) {
                        LOGGER.warn("web socket close error.：{}", e);
                    }
                }
            }
        }
    }

}
