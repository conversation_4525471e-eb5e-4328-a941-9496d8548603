package com.youibot.agv.scheduler.webSocket.controller;

import com.youibot.agv.scheduler.config.webSocket.MyEndpointConfigure;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import com.youibot.agv.scheduler.vehicle.thread.PushAGVStatusThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月24日 下午12:12:08
 */
@RestController
@ServerEndpoint(value = "/agv/status", configurator = MyEndpointConfigure.class)
public class VehicleStatusSocketController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleStatusSocketController.class);

    private static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();

    @Value("${WEB_SOCKET.MAX_IDLE_TIMEOUT}")
    private Integer maxIdleTimeout;

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @OnOpen
    public void onOpen(Session session) {
        /**
         * 一个session对应一个线程.开始推送机器人状态线程。
         */
        session.setMaxIdleTimeout(maxIdleTimeout);
        WEBSOCKET_SESSION.put(session.getId(), session);
        PushAGVStatusThread pushAGVStatusThread = new PushAGVStatusThread(session, defaultVehiclePool);
        pushAGVStatusThread.start();
    }

    @OnClose
    public void onClose(Session session) {
        WEBSOCKET_SESSION.remove(session.getId());
    }

    @OnError
    public void onError(Throwable throwable, Session session) {
        try {
            LOGGER.warn("web socket error and close.", throwable.getMessage());
            session.close();
        } catch (Exception e) {
            LOGGER.warn("web socket close error.", throwable.getMessage());
        }
    }

    @OnMessage
    public void onMessage(String message) {
    }

    public static void sendMessage(Session session, String message) {
        try {
            if (session != null && session.isOpen()) {
                session.getBasicRemote().sendText(message);
            }
        } catch (Exception e) {
            LOGGER.warn("web socket send error：{}", e);
            try {
                session.close();
            } catch (IOException ioe) {
                LOGGER.warn("web socket close error.：{}", e);
            }
        }
    }
}
