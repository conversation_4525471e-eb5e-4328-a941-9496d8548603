package com.youibot.agv.scheduler.webSocket.controller;

import com.youibot.agv.scheduler.config.webSocket.MyEndpointConfigure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date :Created in 下午4:29 2019/12/31
 * @Description :
 * @Modified By :
 * @Version :
 */
@RestController
@ServerEndpoint(value = "/agv/notification", configurator = MyEndpointConfigure.class)
public class NotificationSocketController {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationSocketController.class);

    protected static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();

    public static void sendMessage(String message) {
        for (Session session : WEBSOCKET_SESSION.values()) {
            if (session.isOpen()) {
                try {
                    session.getBasicRemote().sendText(message);
                } catch (IOException e) {
                    LOGGER.error("Send message error ,sessionId=" + session.getId() + "message" + message);
                } catch (Exception e) {
                    LOGGER.error("send to web socket error");
                }
            }
        }
    }

    @OnOpen
    public void onOpen(Session session) {
        session.setMaxIdleTimeout(600 * 1000);
        WEBSOCKET_SESSION.put(session.getId(), session);
        LOGGER.debug("Open a webSocket. id={}", session.getId());
    }

    @OnClose
    public void onClose(Session session) {
        WEBSOCKET_SESSION.remove(session.getId());
        LOGGER.debug("Close a webSocket.", session.getId());
    }

    @OnError
    public void onError(Throwable throwable, Session session) {
        String id = session.getId();
        LOGGER.error("WebSocketServer,Connection Exception : id = " + id + " , throwable = "
                + throwable.getMessage());
    }

    // 获取消息
    @OnMessage
    public void onMessage(String message) {
        LOGGER.debug("Receive a message from client: " + message);
    }

    public static Map<String, Session> getWebsocketSession() {
        return WEBSOCKET_SESSION;
    }
}
