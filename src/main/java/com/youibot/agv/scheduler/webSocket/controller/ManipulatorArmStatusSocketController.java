package com.youibot.agv.scheduler.webSocket.controller;

import com.youibot.agv.scheduler.config.webSocket.MyEndpointConfigure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/12/10 15:41
 */
@RestController
@ServerEndpoint(value = "/manipulatorArm/status", configurator = MyEndpointConfigure.class)
public class ManipulatorArmStatusSocketController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManipulatorArmStatusSocketController.class);

    private static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();

    @Value("${WEB_SOCKET.MAX_IDLE_TIMEOUT}")
    private Integer maxIdleTimeout;

    public static void sendMessage(String message) {
        for (Session session : WEBSOCKET_SESSION.values()) {
            if (session.isOpen()) {
                try {
                    session.getAsyncRemote().sendText(message);
                } catch (Exception e) {
                    LOGGER.warn("web socket send error：{}", e);
                    try {
                        session.close();
                    } catch (IOException ioe) {
                        LOGGER.warn("web socket close error.：{}", e);
                    }
                }
            }
        }
    }

    @OnOpen
    public void onOpen(Session session) {
        session.setMaxIdleTimeout(maxIdleTimeout);
        WEBSOCKET_SESSION.put(session.getId(), session);
    }

    @OnClose
    public void onClose(Session session) {
        WEBSOCKET_SESSION.remove(session.getId());
    }

    @OnError
    public void onError(Throwable throwable, Session session) {
        try {
            LOGGER.warn("web socket error and close.", throwable.getMessage());
            session.close();
        } catch (Exception e) {
            LOGGER.warn("web socket close error.", throwable.getMessage());
        }
    }

    // 获取消息
    @OnMessage
    public void onMessage(String message) {
    }

    public static Map<String, Session> getWebsocketSession() {
        return WEBSOCKET_SESSION;
    }

}
