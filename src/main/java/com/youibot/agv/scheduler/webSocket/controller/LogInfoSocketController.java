package com.youibot.agv.scheduler.webSocket.controller;

import com.youibot.agv.scheduler.config.webSocket.MyEndpointConfigure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 实时输出日志
 * @Author：yangpeilin
 * @Date: 2020/4/24 16:16
 */
@RestController
@ServerEndpoint(value = "/logInfo", configurator = MyEndpointConfigure.class)
public class LogInfoSocketController {

    private static final Logger logger = LoggerFactory.getLogger(LogInfoSocketController.class);

    private static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();

    //默认超时一小时，和ssh连接保持一致
    @Value("${WEB_SOCKET.LOG_INFO_TIMEOUT}")
    private Integer maxIdleTimeout;

    public static void sendMessage(String message) {
        for (Session session : WEBSOCKET_SESSION.values()) {
            if (session.isOpen()) {
                try {
                    session.getBasicRemote().sendText(message);
                } catch (IOException e) {
                    logger.error("Send message error ,sessionId=" + session.getId() + "message" + message);
                }
            }
        }
    }

    @OnOpen
    public void onOpen(Session session) {
        session.setMaxIdleTimeout(maxIdleTimeout);
        WEBSOCKET_SESSION.put(session.getId(), session);
        logger.debug("Open a webSocket. id={}", session.getId());
    }

    @OnClose
    public void onClose(Session session) {
        WEBSOCKET_SESSION.remove(session.getId());
        logger.debug("Close a webSocket.", session.getId());
    }

    @OnError
    public void onError(Throwable throwable, Session session) {
        String id = session.getId();
        logger.error("WebSocketServer,Connection Exception : id = " + id + " , throwable = "
                + throwable.getMessage());
    }

    // 获取消息
    @OnMessage
    public void onMessage(String message) {
        logger.debug("Receive a message from client: " + message);
    }

}
