package com.youibot.agv.scheduler.webSocket.controller;

import com.alibaba.fastjson.JSON;
import com.youibot.agv.scheduler.config.webSocket.MyEndpointConfigure;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.util.VehicleUtils;
import com.youibot.agv.scheduler.webSocket.emnu.WebSocketReceiveCodeEnum;
import com.youibot.agv.scheduler.webSocket.module.SocketReceiveModel;
import com.youibot.agv.scheduler.webSocket.module.SocketSendModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月24日 下午12:12:08
 */
@RestController
@ServerEndpoint(value = "/agv/manual", configurator = MyEndpointConfigure.class)
public class VehicleManualSocketController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleManualSocketController.class);

    private static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();

    @Value("${WEB_SOCKET.MAX_IDLE_TIMEOUT}")
    private Integer maxIdleTimeout;

    @OnOpen
    public void onOpen(Session session) {
        session.setMaxIdleTimeout(maxIdleTimeout);
        WEBSOCKET_SESSION.put(session.getId(), session);
    }

    @OnClose
    public void onClose(Session session) {
        WEBSOCKET_SESSION.remove(session.getId());
    }

    @OnError
    public void onError(Throwable throwable, Session session) {
        String id = session.getId();
        LOGGER.error("WebSocketServer,Connection Exception : id = " + id + " , throwable = "
                + throwable.getMessage());
    }

    // 获取消息
    @OnMessage
    public void onMessage(Session session, String message) {
        SocketReceiveModel receiveModel = JSON.parseObject(message, SocketReceiveModel.class);
        String code = receiveModel.getCode();
        Map<String, Object> data = receiveModel.getData();
        WebSocketReceiveCodeEnum codeEnum = WebSocketReceiveCodeEnum.getByCode(code);
        switch (codeEnum) {
            case SWITCH_MANUAL_MODEL:
                switchManualMode(session, data);
                break;
            case MOVE:
                AGVMove(session, data);
                break;
            case MANUAL_QRCODE_RELOCATION:
                manualQRCodeRelocation(session, data);
                break;
            default:
                break;
        }
    }
    //重定位
    private void manualQRCodeRelocation(Session session, Map<String, Object> data) {
        /*boolean result = false;
        try {
            String id = (String) data.get("id");
            Vehicle vehicle = defaultVehiclePool.getVehicle(id);
            if (vehicle != null) {
                result=vehicle.relocation();
                if(result) {
                    vehicle.closeManualModel();
                }
            }
        } catch (Exception e) {
            LOGGER.error("manual QRCode relocation error.", e);
        }
        this.sendResult(session, WebSocketReceiveCodeEnum.MANUAL_QRCODE_RELOCATION.getCode(), result);*/
    }

    private void switchManualMode(Session session, Map<String, Object> data) {
        boolean result = false;
        try {
            Vehicle vehicle = VehicleUtils.getVehicle();
            if (vehicle != null) {
                vehicle.switchManualMode();
                result = true;
            }
        } catch (Exception e) {
            LOGGER.error("open manual model error.", e);
        }
        this.sendResult(session, WebSocketReceiveCodeEnum.SWITCH_MANUAL_MODEL.getCode(), result);
    }

    private void AGVMove(Session session, Map<String, Object> data) {
        boolean result = false;
        try {
            String id = (String) data.get("id");
            Double vx = Double.valueOf(data.get("vx").toString());
            Double vy = Double.valueOf(data.get("vy").toString());
            Double vtheta = Double.valueOf(data.get("vtheta").toString());
            Vehicle vehicle = VehicleUtils.getVehicle();
            if (vehicle != null) {
                //AGV CONTROL(手工控制)
                result = vehicle.manualMove(vx, vy, vtheta);
            }
        } catch (Exception e) {
            LOGGER.error("AGV move error", e);
        }
        this.sendResult(session, WebSocketReceiveCodeEnum.MOVE.getCode(), result);
    }

    public void sendResult(Session session, String code, Object result) {
        SocketSendModel socketSendModel = new SocketSendModel();
        socketSendModel.setCode(code);
        Map<String, Object> data = new HashMap<>();
        data.put("result", result);
        socketSendModel.setData(data);
        this.sendMessage(session, JSON.toJSONString(socketSendModel));
    }

    public void sendMessage(Session session, String message) {
        if (session != null && session.isOpen()) {
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                LOGGER.debug("Send message error ,sessionId=" + session.getId() + "message" + message);
            }
        }
    }

    public static void sendMessage(String message) {
        for (Session session : WEBSOCKET_SESSION.values()) {
            if (session.isOpen()) {
                try {
                    session.getBasicRemote().sendText(message);
                } catch (IOException e) {
                    LOGGER.debug("Send message error ,sessionId=" + session.getId() + "message" + message);
                }
            }
        }
    }

}