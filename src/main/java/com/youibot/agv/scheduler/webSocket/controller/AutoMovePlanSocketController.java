package com.youibot.agv.scheduler.webSocket.controller;

import com.youibot.agv.scheduler.config.webSocket.MyEndpointConfigure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/8/14 11:41
 */
@RestController
@ServerEndpoint(value = "/agv/autoMove", configurator = MyEndpointConfigure.class)
public class AutoMovePlanSocketController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoMovePlanSocketController.class);

    private static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();

    @Value("${WEB_SOCKET.MAX_IDLE_TIMEOUT}")
    private Integer maxIdleTimeout;

    public static void sendMessage(String message) {
        for (Session session : WEBSOCKET_SESSION.values()) {
            if (session.isOpen()) {
                session.getAsyncRemote().sendText(message);
            }
        }
    }

    @OnOpen
    public void onOpen(Session session) {
        session.setMaxIdleTimeout(maxIdleTimeout);
        WEBSOCKET_SESSION.put(session.getId(), session);
    }

    @OnClose
    public void onClose(Session session) {
        WEBSOCKET_SESSION.remove(session.getId());
    }

    @OnError
    public void onError(Throwable throwable, Session session) {
        String id = session.getId();
        LOGGER.error("WebSocketServer,Connection Exception : id = " + id + " , throwable = "
                + throwable.getMessage());
    }

    // 获取消息
    @OnMessage
    public void onMessage(String message) {
        LOGGER.debug("Receive a message from client: " + message);
    }

}
