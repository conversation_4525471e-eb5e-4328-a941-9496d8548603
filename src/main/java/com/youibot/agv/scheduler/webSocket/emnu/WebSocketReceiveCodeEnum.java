package com.youibot.agv.scheduler.webSocket.emnu;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月24日 下午5:07:16
 */
public enum WebSocketReceiveCodeEnum {

	SWITCH_MANUAL_MODEL("SWITCH_MANUAL_MODEL"), MOVE("MOVE"), MANUAL_QRCODE_RELOCATION(
			"MANUAL_QRCODE_RELOCATION"), HEARBEAT("HEARBEAT");

    private String code;

    WebSocketReceiveCodeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static WebSocketReceiveCodeEnum getByCode(String code) {
        for (WebSocketReceiveCodeEnum enums : WebSocketReceiveCodeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
