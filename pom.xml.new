<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.github.kenta-shimizu</groupId>
    <artifactId>secs4java8</artifactId>
    <version>5.1</version>

    <n>secs4java8</n>
    <description>SEMI-SECS-communicate implementation on Java8</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
        <!-- ProGuard依赖 -->
        <dependency>
            <groupId>net.sf.proguard</groupId>
            <artifactId>proguard-base</artifactId>
            <version>6.2.2</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>

            <!-- 使用maven-jar-plugin排除examples目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <excludes>
                        <exclude>**/examples/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 混淆插件已移至生产环境配置文件中 -->

        </plugins>
    </build>

    <!-- 使用配置文件来控制是否包含examples目录 -->
    <profiles>
        <!-- 开发配置文件，包含examples目录（默认激活） -->
        <profile>
            <id>development</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>build-helper-maven-plugin</artifactId>
                        <version>3.3.0</version>
                        <executions>
                            <execution>
                                <id>add-test-source</id>
                                <phase>generate-test-sources</phase>
                                <goals>
                                    <goal>add-test-source</goal>
                                </goals>
                                <configuration>
                                    <sources>
                                        <source>src/examples</source>
                                    </sources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- 生产配置文件，不包含examples目录，并进行混淆 -->
        <profile>
            <id>production</id>
            <build>
                <plugins>
                    <!-- ProGuard混淆插件 -->
                    <plugin>
                        <groupId>com.github.wvengen</groupId>
                        <artifactId>proguard-maven-plugin</artifactId>
                        <version>2.5.2</version>
                        <executions>
                            <execution>
                                <id>obfuscate</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>proguard</goal>
                                </goals>
                            </execution>
                        </executions>

                        <!-- 确保混淆后的JAR包是主要成品 -->
                        <dependencies>
                            <dependency>
                                <groupId>net.sf.proguard</groupId>
                                <artifactId>proguard-base</artifactId>
                                <version>6.2.2</version>
                            </dependency>
                        </dependencies>
                        <configuration>
                            <!-- 指定混淆后的jar包名称，使用原始名称以便于安装 -->
                            <outjar>${project.build.finalName}.jar</outjar>
                            <!-- 备份原始的jar包 -->
                            <injarNotExistsSkip>false</injarNotExistsSkip>
                            <attachArtifactClassifier>original</attachArtifactClassifier>
                            <attachArtifactClassifierType>jar</attachArtifactClassifierType>
                            <!-- 是否混淆 -->
                            <obfuscate>true</obfuscate>
                            <!-- 使用外部配置文件 -->
                            <proguardInclude>proguard-specific.pro</proguardInclude>
                            <options>
                                <!-- 使用外部配置文件，这里只保留必要的选项 -->
                                <option>-verbose</option>
                                <option>-printmapping ${project.build.directory}/proguard-mapping.txt</option>
                                <option>-printseeds ${project.build.directory}/proguard-seeds.txt</option>
                                <option>-printusage ${project.build.directory}/proguard-usage.txt</option>
                                <option>-printconfiguration ${project.build.directory}/proguard-configuration.txt</option>
                            </options>
                            <libs>
                                <!-- 添加JDK库路径 -->
                                <lib>${java.home}/lib/rt.jar</lib>
                                <lib>${java.home}/lib/jce.jar</lib>
                            </libs>
                            <!-- 指定输入jar包 -->
                            <injar>${project.build.finalName}.jar</injar>
                            <!-- 指定输出目录 -->
                            <outputDirectory>${project.build.directory}</outputDirectory>
                        </configuration>
                    </plugin>
                    <!-- 确保混淆后的JAR包被正确安装 -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-install-plugin</artifactId>
                        <version>3.1.1</version>
                        <executions>
                            <execution>
                                <id>default-install</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>install</goal>
                                </goals>
                                <configuration>
                                    <createChecksum>true</createChecksum>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <repositories>
        <!-- 公司私有库 -->
        <repository>
            <id>nexus</id>
            <n>Nexus Snapshot Repository</n>
            <url>http://10.0.60.129:2081/repository/maven-public/</url>
        </repository>
        <repository>
            <id>public</id>
            <n>aliyun nexus</n>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <n>aliyun nexus</n>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!-- 部署配置 -->
    <distributionManagement>
        <repository>
            <id>nexus</id>
            <name>Internal Release Repository</name>
            <url>file:///E:/maven-repo/releases</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <name>Internal Snapshot Repository</name>
            <url>file:///E:/maven-repo/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>
