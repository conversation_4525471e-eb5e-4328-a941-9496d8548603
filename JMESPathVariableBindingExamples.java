package com.youibot.examples;

import io.burt.jmespath.Expression;
import io.burt.jmespath.JmesPath;
import io.burt.jmespath.jackson.JacksonRuntime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.HashMap;
import java.util.Map;

/**
 * JMESPath 变量绑定实例
 * 演示如何在JMESPath表达式中使用变量绑定功能
 */
public class JMESPathVariableBindingExamples {
    
    private static final JmesPath<JsonNode> jmesPath = new JacksonRuntime();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) throws Exception {
        // 示例1: 基本变量绑定
        basicVariableBinding();
        
        // 示例2: 复杂对象变量绑定
        complexObjectVariableBinding();
        
        // 示例3: 数组索引变量绑定
        arrayIndexVariableBinding();
        
        // 示例4: 条件过滤变量绑定
        conditionalFilterVariableBinding();
        
        // 示例5: 多变量组合使用
        multipleVariableBinding();
        
        // 示例6: 动态路径变量绑定
        dynamicPathVariableBinding();
    }
    
    /**
     * 示例1: 基本变量绑定
     * 使用let表达式定义变量并在后续表达式中使用
     */
    public static void basicVariableBinding() throws Exception {
        System.out.println("=== 示例1: 基本变量绑定 ===");
        
        String jsonData = """
            {
                "users": [
                    {"name": "Alice", "age": 30, "department": "Engineering"},
                    {"name": "Bob", "age": 25, "department": "Marketing"},
                    {"name": "Charlie", "age": 35, "department": "Engineering"}
                ]
            }
            """;
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 使用let表达式定义变量
        String expression = """
            let $targetDept = 'Engineering' in 
            users[?department == $targetDept].{name: name, age: age}
            """;
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例2: 复杂对象变量绑定
     * 绑定复杂对象作为变量使用
     */
    public static void complexObjectVariableBinding() throws Exception {
        System.out.println("=== 示例2: 复杂对象变量绑定 ===");
        
        String jsonData = """
            {
                "products": [
                    {"id": 1, "name": "Laptop", "price": 1000, "category": "Electronics"},
                    {"id": 2, "name": "Book", "price": 20, "category": "Education"},
                    {"id": 3, "name": "Phone", "price": 800, "category": "Electronics"}
                ],
                "filters": {
                    "minPrice": 500,
                    "category": "Electronics"
                }
            }
            """;
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 绑定过滤条件对象
        String expression = """
            let $filters = filters in 
            products[?price >= $filters.minPrice && category == $filters.category].{
                id: id,
                name: name,
                price: price
            }
            """;
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例3: 数组索引变量绑定
     * 使用变量作为数组索引
     */
    public static void arrayIndexVariableBinding() throws Exception {
        System.out.println("=== 示例3: 数组索引变量绑定 ===");
        
        String jsonData = """
            {
                "data": ["first", "second", "third", "fourth", "fifth"],
                "config": {
                    "startIndex": 1,
                    "endIndex": 3
                }
            }
            """;
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 使用变量作为数组切片索引
        String expression = """
            let $start = config.startIndex in
            let $end = config.endIndex in
            data[$start:$end]
            """;
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例4: 条件过滤变量绑定
     * 在过滤条件中使用变量
     */
    public static void conditionalFilterVariableBinding() throws Exception {
        System.out.println("=== 示例4: 条件过滤变量绑定 ===");
        
        String jsonData = """
            {
                "orders": [
                    {"id": 1, "amount": 100, "status": "completed", "date": "2024-01-15"},
                    {"id": 2, "amount": 250, "status": "pending", "date": "2024-01-16"},
                    {"id": 3, "amount": 300, "status": "completed", "date": "2024-01-17"},
                    {"id": 4, "amount": 150, "status": "cancelled", "date": "2024-01-18"}
                ],
                "criteria": {
                    "minAmount": 200,
                    "targetStatus": "completed"
                }
            }
            """;
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 使用多个变量进行复杂过滤
        String expression = """
            let $minAmount = criteria.minAmount in
            let $status = criteria.targetStatus in
            orders[?amount >= $minAmount && status == $status].{
                orderId: id,
                orderAmount: amount,
                orderDate: date
            }
            """;
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例5: 多变量组合使用
     * 演示多个变量的嵌套和组合使用
     */
    public static void multipleVariableBinding() throws Exception {
        System.out.println("=== 示例5: 多变量组合使用 ===");
        
        String jsonData = """
            {
                "employees": [
                    {"name": "Alice", "salary": 5000, "department": "IT", "experience": 5},
                    {"name": "Bob", "salary": 4000, "department": "HR", "experience": 3},
                    {"name": "Charlie", "salary": 6000, "department": "IT", "experience": 7},
                    {"name": "Diana", "salary": 4500, "department": "Finance", "experience": 4}
                ],
                "promotion": {
                    "targetDepartment": "IT",
                    "minSalary": 4500,
                    "minExperience": 5,
                    "bonus": 1000
                }
            }
            """;
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 使用多个变量计算晋升候选人
        String expression = """
            let $promo = promotion in
            let $dept = $promo.targetDepartment in
            let $minSal = $promo.minSalary in
            let $minExp = $promo.minExperience in
            let $bonus = $promo.bonus in
            employees[?department == $dept && salary >= $minSal && experience >= $minExp].{
                name: name,
                currentSalary: salary,
                newSalary: salary + $bonus,
                experience: experience
            }
            """;
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例6: 动态路径变量绑定
     * 使用变量构建动态的JSON路径
     */
    public static void dynamicPathVariableBinding() throws Exception {
        System.out.println("=== 示例6: 动态路径变量绑定 ===");
        
        String jsonData = """
            {
                "config": {
                    "dataSource": "sales",
                    "field": "revenue"
                },
                "sales": {
                    "revenue": [1000, 1500, 2000, 1800, 2200],
                    "units": [10, 15, 20, 18, 22]
                },
                "marketing": {
                    "revenue": [500, 800, 1200, 900, 1100],
                    "units": [5, 8, 12, 9, 11]
                }
            }
            """;
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 注意：JMESPath不支持完全动态的路径构建
        // 这里展示一种变通的方法
        String expression = """
            let $source = config.dataSource in
            let $field = config.field in
            {
                source: $source,
                field: $field,
                salesRevenue: sales.revenue,
                marketingRevenue: marketing.revenue
            }
            """;
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 工具方法：创建带有变量的JMESPath表达式执行器
     */
    public static class VariableJMESPathExecutor {
        private final JmesPath<JsonNode> jmesPath;
        private final ObjectMapper objectMapper;
        
        public VariableJMESPathExecutor() {
            this.jmesPath = new JacksonRuntime();
            this.objectMapper = new ObjectMapper();
        }
        
        /**
         * 执行带变量的JMESPath表达式
         * @param data JSON数据
         * @param expression JMESPath表达式模板
         * @param variables 变量映射
         * @return 执行结果
         */
        public JsonNode executeWithVariables(JsonNode data, String expression, Map<String, Object> variables) throws Exception {
            // 将变量注入到数据中
            ObjectNode dataWithVars = data.deepCopy();
            ObjectNode varsNode = objectMapper.createObjectNode();
            
            for (Map.Entry<String, Object> entry : variables.entrySet()) {
                JsonNode varValue = objectMapper.valueToTree(entry.getValue());
                varsNode.set(entry.getKey(), varValue);
            }
            dataWithVars.set("$vars", varsNode);
            
            // 修改表达式以使用注入的变量
            String modifiedExpression = expression.replaceAll("\\$([a-zA-Z_][a-zA-Z0-9_]*)", "\\$vars.$1");
            
            Expression<JsonNode> compiledExpression = jmesPath.compile(modifiedExpression);
            return compiledExpression.search(dataWithVars);
        }
    }
    
    /**
     * 演示变量执行器的使用
     */
    public static void demonstrateVariableExecutor() throws Exception {
        System.out.println("=== 变量执行器演示 ===");
        
        String jsonData = """
            {
                "products": [
                    {"name": "Laptop", "price": 1000, "category": "Electronics"},
                    {"name": "Book", "price": 20, "category": "Education"},
                    {"name": "Phone", "price": 800, "category": "Electronics"}
                ]
            }
            """;
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 定义变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("minPrice", 500);
        variables.put("targetCategory", "Electronics");
        
        // 使用变量的表达式
        String expression = "products[?price >= $minPrice && category == $targetCategory].name";
        
        VariableJMESPathExecutor executor = new VariableJMESPathExecutor();
        JsonNode result = executor.executeWithVariables(data, expression, variables);
        
        System.out.println("表达式: " + expression);
        System.out.println("变量: " + variables);
        System.out.println("结果: " + result.toPrettyString());
    }
}
