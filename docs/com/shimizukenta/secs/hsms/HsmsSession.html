<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>HsmsSession</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsms, interface: HsmsSession">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.hsms</a></div>
<h1 title="インタフェース HsmsSession" class="title">インタフェース HsmsSession</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateDetectable</a></code>, <code><a href="HsmsGemAccessor.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsGemAccessor</a></code>, <code><a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveObservable</a></code>, <code><a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageSendable</a></code>, <code><a href="../SecsCommunicateStateDetectable.html" title="com.shimizukenta.secs内のインタフェース">SecsCommunicateStateDetectable</a></code>, <code><a href="../SecsCommunicatorConfigValueGettable.html" title="com.shimizukenta.secs内のインタフェース">SecsCommunicatorConfigValueGettable</a></code>, <code><a href="../SecsGemAccessor.html" title="com.shimizukenta.secs内のインタフェース">SecsGemAccessor</a></code>, <code><a href="../SecsMessageReceiveObservable.html" title="com.shimizukenta.secs内のインタフェース">SecsMessageReceiveObservable</a></code>, <code><a href="../SecsMessageSendable.html" title="com.shimizukenta.secs内のインタフェース">SecsMessageSendable</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">HsmsSession</span>
extends <a href="HsmsGemAccessor.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsGemAccessor</a>, <a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveObservable</a>, <a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateDetectable</a></pre>
<div class="block">HSMS Session interface.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#deselect()">deselect</a></span>()</code></th>
<td class="col-last">
<div class="block">DESELECT.REQ.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#select()">select</a></span>()</code></th>
<td class="col-last">
<div class="block">SELECT.REQ.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#separate()">separate</a></span>()</code></th>
<td class="col-last">
<div class="block">SEPARATE.REQ.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.hsms.HsmsCommunicateStateDetectable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.hsms.<a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateDetectable</a></h3>
<code><a href="HsmsCommunicateStateDetectable.html#addHsmsCommunicateStateChangeBiListener(com.shimizukenta.secs.hsms.HsmsCommunicateStateChangeBiListener)">addHsmsCommunicateStateChangeBiListener</a>, <a href="HsmsCommunicateStateDetectable.html#addHsmsCommunicateStateChangeListener(com.shimizukenta.secs.hsms.HsmsCommunicateStateChangeListener)">addHsmsCommunicateStateChangeListener</a>, <a href="HsmsCommunicateStateDetectable.html#getHsmsCommunicateState()">getHsmsCommunicateState</a>, <a href="HsmsCommunicateStateDetectable.html#removeHsmsCommunicateStateChangeBiListener(com.shimizukenta.secs.hsms.HsmsCommunicateStateChangeBiListener)">removeHsmsCommunicateStateChangeBiListener</a>, <a href="HsmsCommunicateStateDetectable.html#removeHsmsCommunicateStateChangeListener(com.shimizukenta.secs.hsms.HsmsCommunicateStateChangeListener)">removeHsmsCommunicateStateChangeListener</a>, <a href="HsmsCommunicateStateDetectable.html#waitUntilHsmsCommunicateState(com.shimizukenta.secs.hsms.HsmsCommunicateState)">waitUntilHsmsCommunicateState</a>, <a href="HsmsCommunicateStateDetectable.html#waitUntilHsmsCommunicateState(com.shimizukenta.secs.hsms.HsmsCommunicateState,long,java.util.concurrent.TimeUnit)">waitUntilHsmsCommunicateState</a>, <a href="HsmsCommunicateStateDetectable.html#waitUntilNotHsmsCommunicateState(com.shimizukenta.secs.hsms.HsmsCommunicateState)">waitUntilNotHsmsCommunicateState</a>, <a href="HsmsCommunicateStateDetectable.html#waitUntilNotHsmsCommunicateState(com.shimizukenta.secs.hsms.HsmsCommunicateState,long,java.util.concurrent.TimeUnit)">waitUntilNotHsmsCommunicateState</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.hsms.HsmsMessageReceiveObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.hsms.<a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveObservable</a></h3>
<code><a href="HsmsMessageReceiveObservable.html#addHsmsMessageReceiveBiListener(com.shimizukenta.secs.hsms.HsmsMessageReceiveBiListener)">addHsmsMessageReceiveBiListener</a>, <a href="HsmsMessageReceiveObservable.html#addHsmsMessageReceiveListener(com.shimizukenta.secs.hsms.HsmsMessageReceiveListener)">addHsmsMessageReceiveListener</a>, <a href="HsmsMessageReceiveObservable.html#removeHsmsMessageReceiveBiListener(com.shimizukenta.secs.hsms.HsmsMessageReceiveBiListener)">removeHsmsMessageReceiveBiListener</a>, <a href="HsmsMessageReceiveObservable.html#removeHsmsMessageReceiveListener(com.shimizukenta.secs.hsms.HsmsMessageReceiveListener)">removeHsmsMessageReceiveListener</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.hsms.HsmsMessageSendable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.hsms.<a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageSendable</a></h3>
<code><a href="HsmsMessageSendable.html#send(com.shimizukenta.secs.hsms.HsmsMessage)">send</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsCommunicateStateDetectable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsCommunicateStateDetectable.html" title="com.shimizukenta.secs内のインタフェース">SecsCommunicateStateDetectable</a></h3>
<code><a href="../SecsCommunicateStateDetectable.html#addSecsCommunicatableStateChangeBiListener(com.shimizukenta.secs.SecsCommunicatableStateChangeBiListener)">addSecsCommunicatableStateChangeBiListener</a>, <a href="../SecsCommunicateStateDetectable.html#addSecsCommunicatableStateChangeListener(com.shimizukenta.secs.SecsCommunicatableStateChangeListener)">addSecsCommunicatableStateChangeListener</a>, <a href="../SecsCommunicateStateDetectable.html#isCommunicatable()">isCommunicatable</a>, <a href="../SecsCommunicateStateDetectable.html#removeSecsCommunicatableStateChangeBiListener(com.shimizukenta.secs.SecsCommunicatableStateChangeBiListener)">removeSecsCommunicatableStateChangeBiListener</a>, <a href="../SecsCommunicateStateDetectable.html#removeSecsCommunicatableStateChangeListener(com.shimizukenta.secs.SecsCommunicatableStateChangeListener)">removeSecsCommunicatableStateChangeListener</a>, <a href="../SecsCommunicateStateDetectable.html#waitUntilCommunicatable()">waitUntilCommunicatable</a>, <a href="../SecsCommunicateStateDetectable.html#waitUntilCommunicatable(long,java.util.concurrent.TimeUnit)">waitUntilCommunicatable</a>, <a href="../SecsCommunicateStateDetectable.html#waitUntilNotCommunicatable()">waitUntilNotCommunicatable</a>, <a href="../SecsCommunicateStateDetectable.html#waitUntilNotCommunicatable(long,java.util.concurrent.TimeUnit)">waitUntilNotCommunicatable</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsCommunicatorConfigValueGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsCommunicatorConfigValueGettable.html" title="com.shimizukenta.secs内のインタフェース">SecsCommunicatorConfigValueGettable</a></h3>
<code><a href="../SecsCommunicatorConfigValueGettable.html#deviceId()">deviceId</a>, <a href="../SecsCommunicatorConfigValueGettable.html#isEquip()">isEquip</a>, <a href="../SecsCommunicatorConfigValueGettable.html#sessionId()">sessionId</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsGemAccessor">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsGemAccessor.html" title="com.shimizukenta.secs内のインタフェース">SecsGemAccessor</a></h3>
<code><a href="../SecsGemAccessor.html#gem()">gem</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsMessageReceiveObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsMessageReceiveObservable.html" title="com.shimizukenta.secs内のインタフェース">SecsMessageReceiveObservable</a></h3>
<code><a href="../SecsMessageReceiveObservable.html#addSecsMessageReceiveBiListener(com.shimizukenta.secs.SecsMessageReceiveBiListener)">addSecsMessageReceiveBiListener</a>, <a href="../SecsMessageReceiveObservable.html#addSecsMessageReceiveListener(com.shimizukenta.secs.SecsMessageReceiveListener)">addSecsMessageReceiveListener</a>, <a href="../SecsMessageReceiveObservable.html#removeSecsMessageReceiveBiListener(com.shimizukenta.secs.SecsMessageReceiveBiListener)">removeSecsMessageReceiveBiListener</a>, <a href="../SecsMessageReceiveObservable.html#removeSecsMessageReceiveListener(com.shimizukenta.secs.SecsMessageReceiveListener)">removeSecsMessageReceiveListener</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsMessageSendable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsMessageSendable.html" title="com.shimizukenta.secs内のインタフェース">SecsMessageSendable</a></h3>
<code><a href="../SecsMessageSendable.html#send(int,int,boolean)">send</a>, <a href="../SecsMessageSendable.html#send(int,int,boolean,com.shimizukenta.secs.secs2.Secs2)">send</a>, <a href="../SecsMessageSendable.html#send(com.shimizukenta.secs.SecsMessage,int,int,boolean)">send</a>, <a href="../SecsMessageSendable.html#send(com.shimizukenta.secs.SecsMessage,int,int,boolean,com.shimizukenta.secs.secs2.Secs2)">send</a>, <a href="../SecsMessageSendable.html#send(com.shimizukenta.secs.SecsMessage,com.shimizukenta.secs.sml.SmlMessage)">send</a>, <a href="../SecsMessageSendable.html#send(com.shimizukenta.secs.sml.SmlMessage)">send</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="select()">
<h3>select</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">select</span>()
        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">SELECT.REQ.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if SELECT Success, otherwise false</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deselect()">
<h3>deselect</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">deselect</span>()
          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">DESELECT.REQ.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if DESELECT Success, otherwise false</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="separate()">
<h3>separate</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">separate</span>()
          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">SEPARATE.REQ.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if SEPARATE Success, otherwise false</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
