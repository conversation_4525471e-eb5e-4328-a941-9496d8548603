<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>HsmsConnectionMode</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsms, enum: HsmsConnectionMode">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":9,"i1":9};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],1:["t1","static\u30E1\u30BD\u30C3\u30C9"],8:["t4","concrete\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">ネスト</a>&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">列挙定数</a>&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li><a href="#enum.constant.detail">列挙定数</a>&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.hsms</a></div>
<h1 title="列挙 HsmsConnectionMode" class="title">列挙 HsmsConnectionMode</h1>
</div>
<div class="inheritance" title="継承ツリー">java.lang.Object
<div class="inheritance">java.lang.Enum&lt;<a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>&gt;
<div class="inheritance">com.shimizukenta.secs.hsms.HsmsConnectionMode</div>
</div>
</div>
<section class="description">
<dl class="notes">
<dt>すべての実装されたインタフェース:</dt>
<dd><code>java.io.Serializable</code>, <code>java.lang.Comparable&lt;<a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>&gt;</code>, <code>java.lang.constant.Constable</code></dd>
</dl>
<hr>
<pre>public enum <span class="type-name-label">HsmsConnectionMode</span>
extends java.lang.Enum&lt;<a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>&gt;</pre>
<div class="block">HSMS Connection Mode.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested.class.summary">
<h2>ネストされたクラスの概要</h2>
<div class="inherited-list">
<h2 id="nested.classes.inherited.from.class.java.lang.Enum">クラスから継承されたネストされたクラス/インタフェース&nbsp;java.lang.Enum</h2>
<code>java.lang.Enum.EnumDesc&lt;E extends java.lang.Enum&lt;E&gt;&gt;</code></div>
</section>
</li>
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<li>
<section class="constants-summary" id="enum.constant.summary">
<h2>列挙型定数の概要</h2>
<div class="member-summary">
<table class="summary-table">
<caption><span>列挙定数</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">列挙型定数</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color">
<th class="col-first" scope="row"><code><span class="member-name-link"><a href="#ACTIVE">ACTIVE</a></span></code></th>
<td class="col-last">
<div class="block">ACTIVE.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><code><span class="member-name-link"><a href="#PASSIVE">PASSIVE</a></span></code></th>
<td class="col-last">
<div class="block">PASSIVE.</div>
</td>
</tr>
</tbody>
</table>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t1" class="table-tab" onclick="show(1);">staticメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t4" class="table-tab" onclick="show(8);">concreteメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>static <a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#valueOf(java.lang.String)">valueOf</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="col-last">
<div class="block">指定した名前を持つこの型の列挙型定数を返します。
文字列は、この型の列挙型定数を宣言するのに使用した識別子と<i>正確に</i>
一致している必要があります。(余分な空白文字を含めることは
できません。)</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>static <a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>[]</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#values()">values</a></span>()</code></th>
<td class="col-last">
<div class="block">この列挙型の定数を含む配列を宣言されている順序で
返します。</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.lang.Enum">クラスから継承されたメソッド&nbsp;java.lang.Enum</h3>
<code>compareTo, describeConstable, equals, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.lang.Object">クラスから継承されたメソッド&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<li>
<section class="constant-details" id="enum.constant.detail">
<h2>列挙型定数の詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="PASSIVE">
<h3>PASSIVE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a></span>&nbsp;<span class="member-name">PASSIVE</span></div>
<div class="block">PASSIVE.</div>
</section>
</li>
<li>
<section class="detail" id="ACTIVE">
<h3>ACTIVE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a></span>&nbsp;<span class="member-name">ACTIVE</span></div>
<div class="block">ACTIVE.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="values()">
<h3>values</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>[]</span>&nbsp;<span class="member-name">values</span>()</div>
<div class="block">この列挙型の定数を含む配列を宣言されている順序で
返します。</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>この列挙型の定数を含む、宣言されている順序での配列</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="valueOf(java.lang.String)">
<h3>valueOf</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a></span>&nbsp;<span class="member-name">valueOf</span>&#8203;(<span class="parameters">java.lang.String&nbsp;name)</span></div>
<div class="block">指定した名前を持つこの型の列挙型定数を返します。
文字列は、この型の列挙型定数を宣言するのに使用した識別子と<i>正確に</i>
一致している必要があります。(余分な空白文字を含めることは
できません。)</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>name</code> - 返される列挙型定数の名前。</dd>
<dt>戻り値:</dt>
<dd>指定した名前の列挙型定数</dd>
<dt>例外:</dt>
<dd><code>java.lang.IllegalArgumentException</code> - この列挙型に、指定した名前の定数がない場合</dd>
<dd><code>java.lang.NullPointerException</code> - 引数がnullの場合</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">ネスト</a>&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">列挙定数</a>&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li><a href="#enum.constant.detail">列挙定数</a>&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
