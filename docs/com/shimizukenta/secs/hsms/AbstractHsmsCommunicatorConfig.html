<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>AbstractHsmsCommunicatorConfig</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsms, class: AbstractHsmsCommunicatorConfig">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],8:["t4","concrete\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.hsms</a></div>
<h1 title="クラス AbstractHsmsCommunicatorConfig" class="title">クラス AbstractHsmsCommunicatorConfig</h1>
</div>
<div class="inheritance" title="継承ツリー">java.lang.Object
<div class="inheritance"><a href="../AbstractSecsCommunicatorConfig.html" title="com.shimizukenta.secs内のクラス">com.shimizukenta.secs.AbstractSecsCommunicatorConfig</a>
<div class="inheritance">com.shimizukenta.secs.hsms.AbstractHsmsCommunicatorConfig</div>
</div>
</div>
<section class="description">
<dl class="notes">
<dt>すべての実装されたインタフェース:</dt>
<dd><code>java.io.Serializable</code></dd>
</dl>
<dl class="notes">
<dt>直系の既知のサブクラス:</dt>
<dd><code><a href="../hsmsgs/HsmsGsCommunicatorConfig.html" title="com.shimizukenta.secs.hsmsgs内のクラス">HsmsGsCommunicatorConfig</a></code>, <code><a href="../hsmsss/HsmsSsCommunicatorConfig.html" title="com.shimizukenta.secs.hsmsss内のクラス">HsmsSsCommunicatorConfig</a></code></dd>
</dl>
<hr>
<pre>public abstract class <span class="type-name-label">AbstractHsmsCommunicatorConfig</span>
extends <a href="../AbstractSecsCommunicatorConfig.html" title="com.shimizukenta.secs内のクラス">AbstractSecsCommunicatorConfig</a></pre>
<div class="block">This class is config of HSMS-Communicator.
 
 <ul>
 <li>To set Active or Passive mode, <a href="#connectionMode(com.shimizukenta.secs.hsms.HsmsConnectionMode)"><code>connectionMode(HsmsConnectionMode)</code></a></li>
 <li>To set linktest-cycle-time. <a href="#linktest(float)"><code>linktest(float)</code></a></li>
 <li>To set <strong>NOT</strong> linktest, <a href="#notLinktest()"><code>notLinktest()</code></a></li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="../../../../serialized-form.html#com.shimizukenta.secs.hsms.AbstractHsmsCommunicatorConfig">直列化された形式</a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t4" class="table-tab" onclick="show(8);">concreteメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#connectionMode()">connectionMode</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Connection-Mode property.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#connectionMode(com.shimizukenta.secs.hsms.HsmsConnectionMode)">connectionMode</a></span>&#8203;(<a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>&nbsp;mode)</code></th>
<td class="col-last">
<div class="block">ACTIVE or PASSIVE Connection-Mode setter</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code><a href="../local/property/BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#doLinktest()">doLinktest</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns do-linktest-property.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code><a href="../local/property/BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#doRebindIfPassive()">doRebindIfPassive</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns do-rebind-if-passive property.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#linktest(float)">linktest</a></span>&#8203;(float&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">Linktest cycle time setter.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code><a href="../local/property/TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#linktestTime()">linktestTime</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Linktest cycle TimeProperty.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#notLinktest()">notLinktest</a></span>()</code></th>
<td class="col-last">
<div class="block">Set Not-Linktest</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#notRebindIfPassive()">notRebindIfPassive</a></span>()</code></th>
<td class="col-last">
<div class="block">Set not rebind if Passive-protocol</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#rebindIfPassive(float)">rebindIfPassive</a></span>&#8203;(float&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">Rebind if Passive-Protocol, if bind failed, then rebind after this time.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code><a href="../local/property/TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#rebindIfPassiveTime()">rebindIfPassiveTime</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns rebind time TimeProperty.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.AbstractSecsCommunicatorConfig">クラスから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../AbstractSecsCommunicatorConfig.html" title="com.shimizukenta.secs内のクラス">AbstractSecsCommunicatorConfig</a></h3>
<code><a href="../AbstractSecsCommunicatorConfig.html#gem()">gem</a>, <a href="../AbstractSecsCommunicatorConfig.html#isEquip()">isEquip</a>, <a href="../AbstractSecsCommunicatorConfig.html#isEquip(boolean)">isEquip</a>, <a href="../AbstractSecsCommunicatorConfig.html#logSubjectHeader()">logSubjectHeader</a>, <a href="../AbstractSecsCommunicatorConfig.html#logSubjectHeader(java.lang.CharSequence)">logSubjectHeader</a>, <a href="../AbstractSecsCommunicatorConfig.html#name()">name</a>, <a href="../AbstractSecsCommunicatorConfig.html#name(java.lang.CharSequence)">name</a>, <a href="../AbstractSecsCommunicatorConfig.html#timeout()">timeout</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.lang.Object">クラスから継承されたメソッド&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="connectionMode(com.shimizukenta.secs.hsms.HsmsConnectionMode)">
<h3>connectionMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">connectionMode</span>&#8203;(<span class="parameters"><a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>&nbsp;mode)</span></div>
<div class="block">ACTIVE or PASSIVE Connection-Mode setter</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>mode</code> - the HSMS-Connection-Mode</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="connectionMode()">
<h3>connectionMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a>&gt;</span>&nbsp;<span class="member-name">connectionMode</span>()</div>
<div class="block">Returns Connection-Mode property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>connection-mode property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="notLinktest()">
<h3>notLinktest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">notLinktest</span>()</div>
<div class="block">Set Not-Linktest</div>
</section>
</li>
<li>
<section class="detail" id="linktest(float)">
<h3>linktest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">linktest</span>&#8203;(<span class="parameters">float&nbsp;seconds)</span></div>
<div class="block">Linktest cycle time setter.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - linktest-cycle-seconds. value is <code>&gt;= 0</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="linktestTime()">
<h3>linktestTime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">linktestTime</span>()</div>
<div class="block">Returns Linktest cycle TimeProperty.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Linktest cycle TimeProperty</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="doLinktest()">
<h3>doLinktest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></span>&nbsp;<span class="member-name">doLinktest</span>()</div>
<div class="block">Returns do-linktest-property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>do-linktest-property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="notRebindIfPassive()">
<h3>notRebindIfPassive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">notRebindIfPassive</span>()</div>
<div class="block">Set not rebind if Passive-protocol</div>
</section>
</li>
<li>
<section class="detail" id="rebindIfPassive(float)">
<h3>rebindIfPassive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">rebindIfPassive</span>&#8203;(<span class="parameters">float&nbsp;seconds)</span></div>
<div class="block">Rebind if Passive-Protocol, if bind failed, then rebind after this time.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - rebind after this time if Passive-protocol. value <code>&gt;=0</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="rebindIfPassiveTime()">
<h3>rebindIfPassiveTime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">rebindIfPassiveTime</span>()</div>
<div class="block">Returns rebind time TimeProperty.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Rebind-Time-Property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="doRebindIfPassive()">
<h3>doRebindIfPassive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></span>&nbsp;<span class="member-name">doRebindIfPassive</span>()</div>
<div class="block">Returns do-rebind-if-passive property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>do-rebind-if-passive property</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
