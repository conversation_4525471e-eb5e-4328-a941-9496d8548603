<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>HsmsLogObservable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsms, interface: HsmsLogObservable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.hsms</a></div>
<h1 title="インタフェース HsmsLogObservable" class="title">インタフェース HsmsLogObservable</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="../SecsLogObservable.html" title="com.shimizukenta.secs内のインタフェース">SecsLogObservable</a></code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="../hsmsgs/HsmsGsCommunicator.html" title="com.shimizukenta.secs.hsmsgs内のインタフェース">HsmsGsCommunicator</a></code>, <code><a href="../hsmsss/HsmsSsCommunicator.html" title="com.shimizukenta.secs.hsmsss内のインタフェース">HsmsSsCommunicator</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">HsmsLogObservable</span>
extends <a href="../SecsLogObservable.html" title="com.shimizukenta.secs内のインタフェース">SecsLogObservable</a></pre>
<div class="block">HSMS-Log Observable.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addHsmsChannelConnectionLogListener(com.shimizukenta.secs.SecsLogListener)">addHsmsChannelConnectionLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsChannelConnectionLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsChannelConnectionLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add HSMS channel connection log listener success, otherwise <code>false</code>, receive connection log.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addHsmsSessionCommunicateStateLogListener(com.shimizukenta.secs.SecsLogListener)">addHsmsSessionCommunicateStateLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsSessionCommunicateStateLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsSessionCommunicateStateLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add HSMS communicate state log listener success, otherwise <code>false</code>, receive HSMS communicate state log.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addReceiveHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addReceiveHsmsMessagePassThroughLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add Receive HSMS Message pass through log listener success, otherwise <code>false</code>, pass through HSMS message.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addSendedHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addSendedHsmsMessagePassThroughLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add Sended HSMS Message pass through log listener success, otherwise <code>false</code>, pass through HSMS message.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addTrySendHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addTrySendHsmsMessagePassThroughLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add Try-send HSMS Message pass through log listener success, otherwise <code>false</code>, pass through HSMS message.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeHsmsChannelConnectionLogListener(com.shimizukenta.secs.SecsLogListener)">removeHsmsChannelConnectionLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsChannelConnectionLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsChannelConnectionLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add success, otherwise <code>false</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeHsmsSessionCommunicateStateLogListener(com.shimizukenta.secs.SecsLogListener)">removeHsmsSessionCommunicateStateLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsSessionCommunicateStateLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsSessionCommunicateStateLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add success, otherwise <code>false</code>.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeReceiveHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeReceiveHsmsMessagePassThroughLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>trrue</code> if remove success, otherwise <code>false</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeSendedHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeSendedHsmsMessagePassThroughLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>trrue</code> if remove success, otherwise <code>false</code>.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeTrySendHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeTrySendHsmsMessagePassThroughLogListener</a></span>&#8203;(<a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>trrue</code> if add success, otherwise <code>false</code>.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsLogObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsLogObservable.html" title="com.shimizukenta.secs内のインタフェース">SecsLogObservable</a></h3>
<code><a href="../SecsLogObservable.html#addReceiveSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addReceiveSecsMessagePassThroughLogListener</a>, <a href="../SecsLogObservable.html#addSecsLogListener(com.shimizukenta.secs.SecsLogListener)">addSecsLogListener</a>, <a href="../SecsLogObservable.html#addSecsThrowableLogListener(com.shimizukenta.secs.SecsLogListener)">addSecsThrowableLogListener</a>, <a href="../SecsLogObservable.html#addSendedSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addSendedSecsMessagePassThroughLogListener</a>, <a href="../SecsLogObservable.html#addTrySendSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addTrySendSecsMessagePassThroughLogListener</a>, <a href="../SecsLogObservable.html#removeReceiveSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeReceiveSecsMessagePassThroughLogListener</a>, <a href="../SecsLogObservable.html#removeSecsLogListener(com.shimizukenta.secs.SecsLogListener)">removeSecsLogListener</a>, <a href="../SecsLogObservable.html#removeSecsThrowableLogListener(com.shimizukenta.secs.SecsLogListener)">removeSecsThrowableLogListener</a>, <a href="../SecsLogObservable.html#removeSendedSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeSendedSecsMessagePassThroughLogListener</a>, <a href="../SecsLogObservable.html#removeTrySendSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeTrySendSecsMessagePassThroughLogListener</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="addTrySendHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addTrySendHsmsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addTrySendHsmsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add Try-send HSMS Message pass through log listener success, otherwise <code>false</code>, pass through HSMS message.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the Try-send HSMS message pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeTrySendHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeTrySendHsmsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeTrySendHsmsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>trrue</code> if add success, otherwise <code>false</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the Try-send HSMS message pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSendedHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addSendedHsmsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addSendedHsmsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add Sended HSMS Message pass through log listener success, otherwise <code>false</code>, pass through HSMS message.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the Sended HSMS message pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeSendedHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeSendedHsmsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeSendedHsmsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>trrue</code> if remove success, otherwise <code>false</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the Sended HSMS message pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addReceiveHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addReceiveHsmsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addReceiveHsmsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add Receive HSMS Message pass through log listener success, otherwise <code>false</code>, pass through HSMS message.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the Receive HSMS message pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeReceiveHsmsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeReceiveHsmsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeReceiveHsmsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>trrue</code> if remove success, otherwise <code>false</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the Receive HSMS message pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addHsmsChannelConnectionLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addHsmsChannelConnectionLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addHsmsChannelConnectionLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsChannelConnectionLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsChannelConnectionLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add HSMS channel connection log listener success, otherwise <code>false</code>, receive connection log.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the HSMS channel connection log listener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeHsmsChannelConnectionLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeHsmsChannelConnectionLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeHsmsChannelConnectionLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsChannelConnectionLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsChannelConnectionLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add success, otherwise <code>false</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the HSMS channel connection log listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addHsmsSessionCommunicateStateLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addHsmsSessionCommunicateStateLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addHsmsSessionCommunicateStateLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsSessionCommunicateStateLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsSessionCommunicateStateLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add HSMS communicate state log listener success, otherwise <code>false</code>, receive HSMS communicate state log.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the HSMS communicate state change log listener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeHsmsSessionCommunicateStateLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeHsmsSessionCommunicateStateLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeHsmsSessionCommunicateStateLogListener</span>&#8203;(<span class="parameters"><a href="../SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="HsmsSessionCommunicateStateLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsSessionCommunicateStateLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add success, otherwise <code>false</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the HSMS communicate state change log listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
