<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>com.shimizukenta.secs.hsms クラス階層</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="tree: package: com.shimizukenta.secs.hsms">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li>クラス</li>
<li class="nav-bar-cell1-rev">階層ツリー</li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">パッケージcom.shimizukenta.secs.hsmsの階層</h1>
<span class="package-hierarchy-label">パッケージ階層:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">すべてのパッケージ</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="クラス階層">クラス階層</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">com.shimizukenta.secs.<a href="../AbstractSecsCommunicatorConfig.html" title="com.shimizukenta.secs内のクラス"><span class="type-name-link">AbstractSecsCommunicatorConfig</span></a> (implements java.io.Serializable)
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="AbstractHsmsCommunicatorConfig.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">AbstractHsmsCommunicatorConfig</span></a></li>
</ul>
</li>
<li class="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li class="circle">java.lang.Exception
<ul>
<li class="circle">java.lang.RuntimeException
<ul>
<li class="circle">java.lang.IllegalArgumentException
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageHeaderByteLengthIllegalArgumentException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsMessageHeaderByteLengthIllegalArgumentException</span></a></li>
</ul>
</li>
<li class="circle">java.lang.IllegalStateException
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsConnectionModeIllegalStateException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsConnectionModeIllegalStateException</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsException.html" title="com.shimizukenta.secs内のクラス"><span class="type-name-link">SecsException</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsException</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsDetectTerminateException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsDetectTerminateException</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageLengthBytesException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsMessageLengthBytesException</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsControlMessageLengthBytesGreaterThanTenException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsControlMessageLengthBytesGreaterThanTenException</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageLengthBytesLowerThanTenException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsMessageLengthBytesLowerThanTenException</span></a></li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsNotConnectedException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsNotConnectedException</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsSessionNotSelectedException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsSessionNotSelectedException</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsTimeoutT7Exception.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsTimeoutT7Exception</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsTimeoutT8Exception.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsTimeoutT8Exception</span></a></li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsSendMessageException.html" title="com.shimizukenta.secs内のクラス"><span class="type-name-link">SecsSendMessageException</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsSendMessageException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsSendMessageException</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmChannelAlreadyShutdownException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmChannelAlreadyShutdownException</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsTooBigSendMessageException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsTooBigSendMessageException</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsWaitReplyMessageException.html" title="com.shimizukenta.secs内のクラス"><span class="type-name-link">SecsWaitReplyMessageException</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsWaitReplyMessageException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsWaitReplyMessageException</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsNotExpectControlTypeReplyMessageException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsNotExpectControlTypeReplyMessageException</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsRejectException.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsRejectException</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsTimeoutT3Exception.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsTimeoutT3Exception</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsTimeoutT6Exception.html" title="com.shimizukenta.secs.hsms内のクラス"><span class="type-name-link">HsmsTimeoutT6Exception</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="インタフェース階層">インタフェース階層</h2>
<ul>
<li class="circle">java.util.EventListener
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsCommunicateStateChangeBiListener.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsCommunicateStateChangeBiListener</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsCommunicateStateChangeListener.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsCommunicateStateChangeListener</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageReceiveBiListener.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsMessageReceiveBiListener</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageReceiveListener.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsMessageReceiveListener</span></a></li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsCommunicateStateDetectable.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsCommunicateStateDetectable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsCommunicateStateDetectable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsSession.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsSession</span></a> (同様に extends com.shimizukenta.secs.hsms.<a href="HsmsGemAccessor.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsGemAccessor</a>, com.shimizukenta.secs.hsms.<a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveObservable</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsCommunicatorConfigValueGettable.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsCommunicatorConfigValueGettable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.<a href="../SecsGemAccessor.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsGemAccessor</span></a> (同様に extends com.shimizukenta.secs.<a href="../SecsMessageSendable.html" title="com.shimizukenta.secs内のインタフェース">SecsMessageSendable</a>)
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsGemAccessor.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsGemAccessor</span></a> (同様に extends com.shimizukenta.secs.hsms.<a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageSendable</a>)
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsSession.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsSession</span></a> (同様に extends com.shimizukenta.secs.hsms.<a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateDetectable</a>, com.shimizukenta.secs.hsms.<a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveObservable</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsLog.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsLog</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsChannelConnectionLog.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsChannelConnectionLog</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsSessionCommunicateStateLog.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsSessionCommunicateStateLog</span></a></li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsMessagePassThroughLog</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsMessagePassThroughLog</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsLogObservable.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsLogObservable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsLogObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsLogObservable</span></a></li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsMessage.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsMessage</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessage.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsMessage</span></a></li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsMessagePassThroughObservable.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsMessagePassThroughObservable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessagePassThroughObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsMessagePassThroughObservable</span></a></li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsMessageReceiveObservable.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsMessageReceiveObservable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsMessageReceiveObservable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsSession.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsSession</span></a> (同様に extends com.shimizukenta.secs.hsms.<a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateDetectable</a>, com.shimizukenta.secs.hsms.<a href="HsmsGemAccessor.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsGemAccessor</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsMessageSendable.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsMessageSendable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsMessageSendable</span></a>
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsGemAccessor.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsGemAccessor</span></a> (同様に extends com.shimizukenta.secs.<a href="../SecsGemAccessor.html" title="com.shimizukenta.secs内のインタフェース">SecsGemAccessor</a>)
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsSession.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsSession</span></a> (同様に extends com.shimizukenta.secs.hsms.<a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateDetectable</a>, com.shimizukenta.secs.hsms.<a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveObservable</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.shimizukenta.secs.<a href="../SecsGemAccessor.html" title="com.shimizukenta.secs内のインタフェース"><span class="type-name-link">SecsGemAccessor</span></a> (同様に extends com.shimizukenta.secs.<a href="../SecsCommunicatorConfigValueGettable.html" title="com.shimizukenta.secs内のインタフェース">SecsCommunicatorConfigValueGettable</a>)
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsGemAccessor.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsGemAccessor</span></a> (同様に extends com.shimizukenta.secs.hsms.<a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageSendable</a>)
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsSession.html" title="com.shimizukenta.secs.hsms内のインタフェース"><span class="type-name-link">HsmsSession</span></a> (同様に extends com.shimizukenta.secs.hsms.<a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateDetectable</a>, com.shimizukenta.secs.hsms.<a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveObservable</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="列挙型階層">列挙型階層</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.lang.constant.Constable, java.io.Serializable)
<ul>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsChannelConnectionLogState.html" title="com.shimizukenta.secs.hsms内の列挙型"><span class="type-name-link">HsmsChannelConnectionLogState</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsCommunicateState.html" title="com.shimizukenta.secs.hsms内の列挙型"><span class="type-name-link">HsmsCommunicateState</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型"><span class="type-name-link">HsmsConnectionMode</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageDeselectStatus.html" title="com.shimizukenta.secs.hsms内の列挙型"><span class="type-name-link">HsmsMessageDeselectStatus</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageRejectReason.html" title="com.shimizukenta.secs.hsms内の列挙型"><span class="type-name-link">HsmsMessageRejectReason</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageSelectStatus.html" title="com.shimizukenta.secs.hsms内の列挙型"><span class="type-name-link">HsmsMessageSelectStatus</span></a></li>
<li class="circle">com.shimizukenta.secs.hsms.<a href="HsmsMessageType.html" title="com.shimizukenta.secs.hsms内の列挙型"><span class="type-name-link">HsmsMessageType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li>クラス</li>
<li class="nav-bar-cell1-rev">階層ツリー</li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
