<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>com.shimizukenta.secs.hsms</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsms">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li class="nav-bar-cell1-rev">パッケージ</li>
<li>クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="パッケージ" class="title">パッケージ&nbsp;com.shimizukenta.secs.hsms</h1>
</div>
<section class="package-description" id="package.description">
<div class="block">Implementation package providing HSMS (SEMI-E37) Communicator, Message.</div>
</section>
<section class="summary">
<ul class="summary-list">
<li>
<div class="type-summary">
<table class="summary-table">
<caption><span>インタフェースの概要</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">インタフェース</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsChannelConnectionLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsChannelConnectionLog</a></th>
<td class="col-last">
<div class="block">HSMS-channel-connection Log.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsCommunicateStateChangeBiListener.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateChangeBiListener</a></th>
<td class="col-last">
<div class="block">HSMS Communicate State chenge-Bi-Listener.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsCommunicateStateChangeListener.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateChangeListener</a></th>
<td class="col-last">
<div class="block">HSMS Communicate State change listener.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsCommunicateStateDetectable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsCommunicateStateDetectable</a></th>
<td class="col-last">
<div class="block">HSMS-Communicate-State Detectable.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsGemAccessor.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsGemAccessor</a></th>
<td class="col-last">
<div class="block">HsmsGemAccessor.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsLogObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsLogObservable</a></th>
<td class="col-last">
<div class="block">HSMS-Log Observable.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsMessage.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessage</a></th>
<td class="col-last">
<div class="block">HSMS Message.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsMessagePassThroughLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughLog</a></th>
<td class="col-last">
<div class="block">This interface is extend with Pass-Through HsmsMessage.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsMessagePassThroughObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessagePassThroughObservable</a></th>
<td class="col-last">
<div class="block">HSMS Message pass through Observable.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsMessageReceiveBiListener.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveBiListener</a></th>
<td class="col-last">
<div class="block">HSMS Message receive Bi-Listener.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsMessageReceiveListener.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveListener</a></th>
<td class="col-last">
<div class="block">HSMS Message receive listener.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsMessageReceiveObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageReceiveObservable</a></th>
<td class="col-last">
<div class="block">HsmsMessageReceiveObservable.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageSendable</a></th>
<td class="col-last">
<div class="block">HsmsMessageSendable.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsSession.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsSession</a></th>
<td class="col-last">
<div class="block">HSMS Session interface.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsSessionCommunicateStateLog.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsSessionCommunicateStateLog</a></th>
<td class="col-last">
<div class="block">HSMS Session communicate state log.</div>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li>
<div class="type-summary">
<table class="summary-table">
<caption><span>クラスの概要</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">クラス</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="AbstractHsmsCommunicatorConfig.html" title="com.shimizukenta.secs.hsms内のクラス">AbstractHsmsCommunicatorConfig</a></th>
<td class="col-last">
<div class="block">This class is config of HSMS-Communicator.</div>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li>
<div class="type-summary">
<table class="summary-table">
<caption><span>列挙型の概要</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">列挙</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsChannelConnectionLogState.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsChannelConnectionLogState</a></th>
<td class="col-last">
<div class="block">HSMS-channel-connection state.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsCommunicateState.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsCommunicateState</a></th>
<td class="col-last">
<div class="block">HSMS Communicate State.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsConnectionMode.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsConnectionMode</a></th>
<td class="col-last">
<div class="block">HSMS Connection Mode.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsMessageDeselectStatus.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsMessageDeselectStatus</a></th>
<td class="col-last">
<div class="block">HSMS DESELECT staus.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsMessageRejectReason.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsMessageRejectReason</a></th>
<td class="col-last">
<div class="block">HSMS Message Reject Reason.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsMessageSelectStatus.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsMessageSelectStatus</a></th>
<td class="col-last">
<div class="block">HSMS Message Select status.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsMessageType.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsMessageType</a></th>
<td class="col-last">
<div class="block">HSMS Message Type.</div>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li>
<div class="type-summary">
<table class="summary-table">
<caption><span>例外の概要</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">例外</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmChannelAlreadyShutdownException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmChannelAlreadyShutdownException</a></th>
<td class="col-last">
<div class="block">HsmChannelAlreadyShutdownException.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsConnectionModeIllegalStateException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsConnectionModeIllegalStateException</a></th>
<td class="col-last">
<div class="block">HSMS Connection mode Illegal state Exception.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsControlMessageLengthBytesGreaterThanTenException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsControlMessageLengthBytesGreaterThanTenException</a></th>
<td class="col-last">
<div class="block">HSMS Control Message length-byte <code>&gt;10</code> Exception.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsDetectTerminateException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsDetectTerminateException</a></th>
<td class="col-last">
<div class="block">HSMS Detect terminate Exception.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsException</a></th>
<td class="col-last">
<div class="block">HSMS Communicate Exception.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsMessageHeaderByteLengthIllegalArgumentException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsMessageHeaderByteLengthIllegalArgumentException</a></th>
<td class="col-last">
<div class="block">HSMS-Message Header-10-bytes length is NOT equals 10 IllegalArgumentException.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsMessageLengthBytesException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsMessageLengthBytesException</a></th>
<td class="col-last">
<div class="block">HSMS Message Length byte Exception.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsMessageLengthBytesLowerThanTenException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsMessageLengthBytesLowerThanTenException</a></th>
<td class="col-last">
<div class="block">HSMS Message Length byte <code>&lt;10</code> Exception.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsNotConnectedException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsNotConnectedException</a></th>
<td class="col-last">
<div class="block">HSMS NOT Connected Exception.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsNotExpectControlTypeReplyMessageException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsNotExpectControlTypeReplyMessageException</a></th>
<td class="col-last">
<div class="block">HAMA nor expected Control type reply message Exception.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsRejectException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsRejectException</a></th>
<td class="col-last">
<div class="block">HSMS Reject Exception.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsSendMessageException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsSendMessageException</a></th>
<td class="col-last">
<div class="block">HSMS Send Message Exception.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsSessionNotSelectedException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsSessionNotSelectedException</a></th>
<td class="col-last">
<div class="block">Hsms Session NOT SELECTED Exception.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsTimeoutT3Exception.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsTimeoutT3Exception</a></th>
<td class="col-last">
<div class="block">Hsms T3-Timeout Exception.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsTimeoutT6Exception.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsTimeoutT6Exception</a></th>
<td class="col-last">
<div class="block">HSMS T6-Timeout Exception.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsTimeoutT7Exception.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsTimeoutT7Exception</a></th>
<td class="col-last">
<div class="block">HSMS T7-Timeout Exception.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsTimeoutT8Exception.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsTimeoutT8Exception</a></th>
<td class="col-last">
<div class="block">HSMS T8-Timeout Exception.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="HsmsTooBigSendMessageException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsTooBigSendMessageException</a></th>
<td class="col-last">
<div class="block">HSMS Too Big Send Message Size Exception.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="HsmsWaitReplyMessageException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsWaitReplyMessageException</a></th>
<td class="col-last">
<div class="block">HSMS Wait reply message Exception.</div>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li class="nav-bar-cell1-rev">パッケージ</li>
<li>クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
