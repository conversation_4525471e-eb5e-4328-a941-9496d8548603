<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>HsmsChannelConnectionLog</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsms, interface: HsmsChannelConnectionLog">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.hsms</a></div>
<h1 title="インタフェース HsmsChannelConnectionLog" class="title">インタフェース HsmsChannelConnectionLog</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="../SecsLog.html" title="com.shimizukenta.secs内のインタフェース">SecsLog</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">HsmsChannelConnectionLog</span>
extends <a href="../SecsLog.html" title="com.shimizukenta.secs内のインタフェース">SecsLog</a></pre>
<div class="block">HSMS-channel-connection Log.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>java.util.Optional&lt;java.net.SocketAddress&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#optionalLocalSocketAddress()">optionalLocalSocketAddress</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Local-SocketAddresss if exsit, otherwise empty.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>java.util.Optional&lt;java.net.SocketAddress&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#optionslRemoteSocketAddress()">optionslRemoteSocketAddress</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Remote-SocketAddresss if exsit, otherwise empty.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code><a href="HsmsChannelConnectionLogState.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsChannelConnectionLogState</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#state()">state</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns HsmsChannelConnectionLog-State.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsLog">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsLog.html" title="com.shimizukenta.secs内のインタフェース">SecsLog</a></h3>
<code><a href="../SecsLog.html#optionalValueString()">optionalValueString</a>, <a href="../SecsLog.html#subject()">subject</a>, <a href="../SecsLog.html#subjectHeader()">subjectHeader</a>, <a href="../SecsLog.html#timestamp()">timestamp</a>, <a href="../SecsLog.html#value()">value</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="state()">
<h3>state</h3>
<div class="member-signature"><span class="return-type"><a href="HsmsChannelConnectionLogState.html" title="com.shimizukenta.secs.hsms内の列挙型">HsmsChannelConnectionLogState</a></span>&nbsp;<span class="member-name">state</span>()</div>
<div class="block">Returns HsmsChannelConnectionLog-State.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>HsmsChannelConnectionLog-State</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="optionalLocalSocketAddress()">
<h3>optionalLocalSocketAddress</h3>
<div class="member-signature"><span class="return-type">java.util.Optional&lt;java.net.SocketAddress&gt;</span>&nbsp;<span class="member-name">optionalLocalSocketAddress</span>()</div>
<div class="block">Returns Local-SocketAddresss if exsit, otherwise empty.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Local-SocketAddresss if exsit, otherwise empty.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="optionslRemoteSocketAddress()">
<h3>optionslRemoteSocketAddress</h3>
<div class="member-signature"><span class="return-type">java.util.Optional&lt;java.net.SocketAddress&gt;</span>&nbsp;<span class="member-name">optionslRemoteSocketAddress</span>()</div>
<div class="block">Returns Remote-SocketAddresss if exsit, otherwise empty.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Remote-SocketAddresss if exsit, otherwise empty.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
