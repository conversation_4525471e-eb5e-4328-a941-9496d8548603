<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>HsmsNotConnectedException</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsms, class: HsmsNotConnectedException">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">コンストラクタ</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">コンストラクタ</a>&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.hsms</a></div>
<h1 title="クラス HsmsNotConnectedException" class="title">クラス HsmsNotConnectedException</h1>
</div>
<div class="inheritance" title="継承ツリー">java.lang.Object
<div class="inheritance">java.lang.Throwable
<div class="inheritance">java.lang.Exception
<div class="inheritance"><a href="../SecsException.html" title="com.shimizukenta.secs内のクラス">com.shimizukenta.secs.SecsException</a>
<div class="inheritance"><a href="HsmsException.html" title="com.shimizukenta.secs.hsms内のクラス">com.shimizukenta.secs.hsms.HsmsException</a>
<div class="inheritance">com.shimizukenta.secs.hsms.HsmsNotConnectedException</div>
</div>
</div>
</div>
</div>
</div>
<section class="description">
<dl class="notes">
<dt>すべての実装されたインタフェース:</dt>
<dd><code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public class <span class="type-name-label">HsmsNotConnectedException</span>
extends <a href="HsmsException.html" title="com.shimizukenta.secs.hsms内のクラス">HsmsException</a></pre>
<div class="block">HSMS NOT Connected Exception.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="../../../../serialized-form.html#com.shimizukenta.secs.hsms.HsmsNotConnectedException">直列化された形式</a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor.summary">
<h2>コンストラクタの概要</h2>
<div class="member-summary">
<table class="summary-table">
<caption><span>コンストラクタ</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">コンストラクタ</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color">
<th class="col-constructor-name" scope="row"><code><span class="member-name-link"><a href="#%3Cinit%3E()">HsmsNotConnectedException</a></span>()</code></th>
<td class="col-last">
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-constructor-name" scope="row"><code><span class="member-name-link"><a href="#%3Cinit%3E(java.lang.Throwable)">HsmsNotConnectedException</a></span>&#8203;(java.lang.Throwable&nbsp;cause)</code></th>
<td class="col-last">
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsException">クラスから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsException.html" title="com.shimizukenta.secs内のクラス">SecsException</a></h3>
<code><a href="../SecsException.html#secsMessage()">secsMessage</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.lang.Throwable">クラスから継承されたメソッド&nbsp;java.lang.Throwable</h3>
<code>addSuppressed, fillInStackTrace, getCause, getLocalizedMessage, getMessage, getStackTrace, getSuppressed, initCause, printStackTrace, printStackTrace, printStackTrace, setStackTrace, toString</code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.lang.Object">クラスから継承されたメソッド&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor.detail">
<h2>コンストラクタの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>HsmsNotConnectedException</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="member-name">HsmsNotConnectedException</span>()</div>
<div class="block">Constructor.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.Throwable)">
<h3>HsmsNotConnectedException</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="member-name">HsmsNotConnectedException</span>&#8203;(<span class="parameters">java.lang.Throwable&nbsp;cause)</span></div>
<div class="block">Constructor.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cause</code> - the cause</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">コンストラクタ</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">コンストラクタ</a>&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
