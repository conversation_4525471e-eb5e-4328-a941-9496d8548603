<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>HsmsGemAccessor</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsms, interface: HsmsGemAccessor">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.hsms</a></div>
<h1 title="インタフェース HsmsGemAccessor" class="title">インタフェース HsmsGemAccessor</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageSendable</a></code>, <code><a href="../SecsCommunicatorConfigValueGettable.html" title="com.shimizukenta.secs内のインタフェース">SecsCommunicatorConfigValueGettable</a></code>, <code><a href="../SecsGemAccessor.html" title="com.shimizukenta.secs内のインタフェース">SecsGemAccessor</a></code>, <code><a href="../SecsMessageSendable.html" title="com.shimizukenta.secs内のインタフェース">SecsMessageSendable</a></code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="HsmsSession.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsSession</a></code>, <code><a href="../hsmsss/HsmsSsCommunicator.html" title="com.shimizukenta.secs.hsmsss内のインタフェース">HsmsSsCommunicator</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">HsmsGemAccessor</span>
extends <a href="../SecsGemAccessor.html" title="com.shimizukenta.secs内のインタフェース">SecsGemAccessor</a>, <a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageSendable</a></pre>
<div class="block">HsmsGemAccessor.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.hsms.HsmsMessageSendable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.hsms.<a href="HsmsMessageSendable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsMessageSendable</a></h3>
<code><a href="HsmsMessageSendable.html#send(com.shimizukenta.secs.hsms.HsmsMessage)">send</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsCommunicatorConfigValueGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsCommunicatorConfigValueGettable.html" title="com.shimizukenta.secs内のインタフェース">SecsCommunicatorConfigValueGettable</a></h3>
<code><a href="../SecsCommunicatorConfigValueGettable.html#deviceId()">deviceId</a>, <a href="../SecsCommunicatorConfigValueGettable.html#isEquip()">isEquip</a>, <a href="../SecsCommunicatorConfigValueGettable.html#sessionId()">sessionId</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsGemAccessor">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsGemAccessor.html" title="com.shimizukenta.secs内のインタフェース">SecsGemAccessor</a></h3>
<code><a href="../SecsGemAccessor.html#gem()">gem</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsMessageSendable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsMessageSendable.html" title="com.shimizukenta.secs内のインタフェース">SecsMessageSendable</a></h3>
<code><a href="../SecsMessageSendable.html#send(int,int,boolean)">send</a>, <a href="../SecsMessageSendable.html#send(int,int,boolean,com.shimizukenta.secs.secs2.Secs2)">send</a>, <a href="../SecsMessageSendable.html#send(com.shimizukenta.secs.SecsMessage,int,int,boolean)">send</a>, <a href="../SecsMessageSendable.html#send(com.shimizukenta.secs.SecsMessage,int,int,boolean,com.shimizukenta.secs.secs2.Secs2)">send</a>, <a href="../SecsMessageSendable.html#send(com.shimizukenta.secs.SecsMessage,com.shimizukenta.secs.sml.SmlMessage)">send</a>, <a href="../SecsMessageSendable.html#send(com.shimizukenta.secs.sml.SmlMessage)">send</a></code></div>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
