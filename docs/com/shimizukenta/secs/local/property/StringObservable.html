<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>StringObservable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: StringObservable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":18,"i1":18,"i2":18,"i3":18,"i4":18,"i5":18,"i6":18,"i7":18,"i8":18,"i9":18,"i10":18,"i11":18,"i12":18,"i13":18,"i14":18,"i15":18,"i16":18,"i17":18,"i18":18,"i19":18,"i20":18,"i21":18,"i22":18,"i23":18,"i24":18,"i25":18,"i26":18,"i27":18,"i28":18,"i29":18,"i30":18,"i31":18,"i32":18,"i33":18,"i34":18,"i35":18,"i36":18,"i37":18,"i38":18,"i39":18,"i40":18,"i41":18,"i42":18,"i43":18,"i44":18,"i45":18,"i46":18,"i47":18,"i48":18,"i49":18,"i50":18,"i51":18,"i52":18,"i53":18,"i54":18,"i55":18,"i56":18,"i57":18,"i58":18,"i59":18,"i60":18,"i61":18,"i62":18,"i63":18,"i64":18,"i65":18,"i66":18,"i67":18,"i68":18,"i69":18,"i70":18,"i71":18,"i72":18,"i73":18,"i74":18,"i75":18,"i76":18,"i77":18,"i78":18,"i79":18,"i80":18,"i81":18,"i82":18,"i83":18,"i84":18,"i85":18,"i86":18,"i87":18,"i88":18,"i89":18,"i90":18,"i91":18,"i92":18,"i93":18,"i94":18,"i95":18,"i96":18,"i97":18,"i98":18,"i99":18,"i100":18,"i101":18,"i102":18,"i103":18,"i104":18,"i105":18,"i106":18,"i107":18,"i108":18,"i109":18,"i110":18,"i111":18,"i112":18,"i113":18,"i114":18,"i115":18,"i116":18,"i117":18,"i118":18,"i119":18,"i120":18,"i121":18,"i122":18};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],16:["t5","\u30C7\u30D5\u30A9\u30EB\u30C8\u30FB\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース StringObservable" class="title">インタフェース StringObservable</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.String&gt;</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code>, <code><a href="StringProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringProperty</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">StringObservable</span>
extends <a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.String&gt;</pre>
<div class="block">String value Observer.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Observable</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t5" class="table-tab" onclick="show(16);">デフォルト・メソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeConcat(java.lang.String)">computeConcat</a></span>&#8203;(java.lang.String&nbsp;str)</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of convert to concat.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeContains(java.lang.CharSequence)">computeContains</a></span>&#8203;(java.lang.CharSequence&nbsp;s)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of contais.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeContentEqualTo(java.lang.CharSequence)">computeContentEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of contentEquals.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeContentEqualTo(java.lang.StringBuffer)">computeContentEqualTo</a></span>&#8203;(java.lang.StringBuffer&nbsp;sb)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of contentEquals.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeEndsWith(java.lang.String)">computeEndsWith</a></span>&#8203;(java.lang.String&nbsp;suffix)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of endsWith.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIndexOf(int)">computeIndexOf</a></span>&#8203;(int&nbsp;ch)</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String indexOf.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIndexOf(int,int)">computeIndexOf</a></span>&#8203;(int&nbsp;ch,
int&nbsp;fromIndex)</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String indexOf.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIndexOf(java.lang.String)">computeIndexOf</a></span>&#8203;(java.lang.String&nbsp;str)</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String indexOf.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIndexOf(java.lang.String,int)">computeIndexOf</a></span>&#8203;(java.lang.String&nbsp;str,
int&nbsp;fromIndex)</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String indexOf.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEmpty()">computeIsEmpty</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of isEmpty.</div>
</td>
</tr>
<tr class="alt-color" id="i10">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(com.shimizukenta.secs.local.property.StringObservable)">computeIsEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of String equals.</div>
</td>
</tr>
<tr class="row-color" id="i11">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(java.lang.CharSequence)">computeIsEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of String equals.</div>
</td>
</tr>
<tr class="alt-color" id="i12">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">computeIsEqualToIgnoreCase</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of String equalsIgnoreCase.</div>
</td>
</tr>
<tr class="row-color" id="i13">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualToIgnoreCase(java.lang.CharSequence)">computeIsEqualToIgnoreCase</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of String equalsIgnoreCase.</div>
</td>
</tr>
<tr class="alt-color" id="i14">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThan(com.shimizukenta.secs.local.property.StringObservable)">computeIsGreaterThan</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of <code>String#compareTo &gt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i15">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThan(java.lang.CharSequence)">computeIsGreaterThan</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of <code>String#compareTo &gt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i16">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">computeIsGreaterThanOrEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of <code>String#compareTo &gt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i17">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanOrEqualTo(java.lang.CharSequence)">computeIsGreaterThanOrEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of <code>String#compareTo &gt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i18">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThan(com.shimizukenta.secs.local.property.StringObservable)">computeIsLessThan</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of <code>String#compareTo &lt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i19">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThan(java.lang.CharSequence)">computeIsLessThan</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of <code>String#compareTo &lt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i20">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">computeIsLessThanOrEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of <code>String#compareTo &lt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i21">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanOrEqualTo(java.lang.CharSequence)">computeIsLessThanOrEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of <code>String#compareTo &lt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i22">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEmpty()">computeIsNotEmpty</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of NOT isEmpty.</div>
</td>
</tr>
<tr class="row-color" id="i23">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(com.shimizukenta.secs.local.property.StringObservable)">computeIsNotEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of NOT String equals.</div>
</td>
</tr>
<tr class="alt-color" id="i24">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(java.lang.CharSequence)">computeIsNotEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of NOT String equals.</div>
</td>
</tr>
<tr class="row-color" id="i25">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">computeIsNotEqualToIgnoreCase</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of NOT String equalsIgnoreCase.</div>
</td>
</tr>
<tr class="alt-color" id="i26">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualToIgnoreCase(java.lang.CharSequence)">computeIsNotEqualToIgnoreCase</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of NOT String equalsIgnoreCase.</div>
</td>
</tr>
<tr class="row-color" id="i27">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeLastIndexOf(int)">computeLastIndexOf</a></span>&#8203;(int&nbsp;ch)</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String lastIndexOf.</div>
</td>
</tr>
<tr class="alt-color" id="i28">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeLastIndexOf(int,int)">computeLastIndexOf</a></span>&#8203;(int&nbsp;ch,
int&nbsp;fromIndex)</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String lastIndexOf.</div>
</td>
</tr>
<tr class="row-color" id="i29">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeLastIndexOf(java.lang.String)">computeLastIndexOf</a></span>&#8203;(java.lang.String&nbsp;str)</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String lastIndexOf.</div>
</td>
</tr>
<tr class="alt-color" id="i30">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeLastIndexOf(java.lang.String,int)">computeLastIndexOf</a></span>&#8203;(java.lang.String&nbsp;str,
int&nbsp;fromIndex)</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String lastIndexOf.</div>
</td>
</tr>
<tr class="row-color" id="i31">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeLength()">computeLength</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of String length.</div>
</td>
</tr>
<tr class="alt-color" id="i32">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeMatches(java.lang.String)">computeMatches</a></span>&#8203;(java.lang.String&nbsp;regex)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of matches.</div>
</td>
</tr>
<tr class="row-color" id="i33">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeNotContains(java.lang.CharSequence)">computeNotContains</a></span>&#8203;(java.lang.CharSequence&nbsp;s)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of NOT contais.</div>
</td>
</tr>
<tr class="alt-color" id="i34">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeReplace(char,char)">computeReplace</a></span>&#8203;(char&nbsp;oldChar,
char&nbsp;newChar)</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of convert to replace.</div>
</td>
</tr>
<tr class="row-color" id="i35">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeReplace(java.lang.CharSequence,java.lang.CharSequence)">computeReplace</a></span>&#8203;(java.lang.CharSequence&nbsp;target,
java.lang.CharSequence&nbsp;replacement)</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of convert to replace.</div>
</td>
</tr>
<tr class="alt-color" id="i36">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeReplaceAll(java.lang.String,java.lang.String)">computeReplaceAll</a></span>&#8203;(java.lang.String&nbsp;regex,
java.lang.String&nbsp;replacement)</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of convert to replaceAll.</div>
</td>
</tr>
<tr class="row-color" id="i37">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeReplaceFirst(java.lang.String,java.lang.String)">computeReplaceFirst</a></span>&#8203;(java.lang.String&nbsp;regex,
java.lang.String&nbsp;replacement)</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of convert to replaceFirst.</div>
</td>
</tr>
<tr class="alt-color" id="i38">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeStartsWith(java.lang.String)">computeStartsWith</a></span>&#8203;(java.lang.String&nbsp;prefix)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of startsWith.</div>
</td>
</tr>
<tr class="row-color" id="i39">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeStartsWith(java.lang.String,int)">computeStartsWith</a></span>&#8203;(java.lang.String&nbsp;prefix,
int&nbsp;toOffset)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of startsWith.</div>
</td>
</tr>
<tr class="alt-color" id="i40">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeToLowerCase()">computeToLowerCase</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of converted to lowercase.</div>
</td>
</tr>
<tr class="row-color" id="i41">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeToLowerCase(java.util.Locale)">computeToLowerCase</a></span>&#8203;(java.util.Locale&nbsp;locale)</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of converted to lowercase.</div>
</td>
</tr>
<tr class="alt-color" id="i42">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeToUpperCase()">computeToUpperCase</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of converted to uppercase.</div>
</td>
</tr>
<tr class="row-color" id="i43">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeToUpperCase(java.util.Locale)">computeToUpperCase</a></span>&#8203;(java.util.Locale&nbsp;locale)</code></th>
<td class="col-last">
<div class="block">Returns StringCompution of converted to uppercase.</div>
</td>
</tr>
<tr class="alt-color" id="i44">
<td class="col-first"><code>default <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeTrim()">computeTrim</a></span>()</code></th>
<td class="col-last">
<div class="block">Return StringCompution of converted to trim.</div>
</td>
</tr>
<tr class="row-color" id="i45">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContainsAndGet(java.lang.CharSequence)">waitUntilContainsAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;s)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contains, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i46">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContainsAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilContainsAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;s,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contains, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i47">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContainsAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContainsAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;s,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contains, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i48">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContentEqualToAndGet(java.lang.CharSequence)">waitUntilContentEqualToAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contentEquals, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i49">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContentEqualToAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilContentEqualToAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contentEquals, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i50">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContentEqualToAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContentEqualToAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contentEquals, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i51">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContentEqualToAndGet(java.lang.StringBuffer)">waitUntilContentEqualToAndGet</a></span>&#8203;(java.lang.StringBuffer&nbsp;sb)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contentEquals, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i52">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContentEqualToAndGet(java.lang.StringBuffer,long,java.util.concurrent.TimeUnit)">waitUntilContentEqualToAndGet</a></span>&#8203;(java.lang.StringBuffer&nbsp;sb,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contentEquals, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i53">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContentEqualToAndGet(java.lang.StringBuffer,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContentEqualToAndGet</a></span>&#8203;(java.lang.StringBuffer&nbsp;sb,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is contentEquals, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i54">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEndsWithAndGet(java.lang.String)">waitUntilEndsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;suffix)</code></th>
<td class="col-last">
<div class="block">Waiting until value is endsWith, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i55">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEndsWithAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">waitUntilEndsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;suffix,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is endsWith, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i56">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEndsWithAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEndsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;suffix,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is endsWith, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i57">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable)">waitUntilEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal to observer.</div>
</td>
</tr>
<tr class="alt-color" id="i58">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal to observer.</div>
</td>
</tr>
<tr class="row-color" id="i59">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal to observer.</div>
</td>
</tr>
<tr class="alt-color" id="i60">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(java.lang.CharSequence)">waitUntilEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal to cs.</div>
</td>
</tr>
<tr class="row-color" id="i61">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal to cs.</div>
</td>
</tr>
<tr class="alt-color" id="i62">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal to cs.</div>
</td>
</tr>
<tr class="row-color" id="i63">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">waitUntilEqualToIgnoreCase</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equalsIgnoreCase.</div>
</td>
</tr>
<tr class="alt-color" id="i64">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualToIgnoreCase</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equalsIgnoreCase.</div>
</td>
</tr>
<tr class="row-color" id="i65">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualToIgnoreCase</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equalsIgnoreCase.</div>
</td>
</tr>
<tr class="alt-color" id="i66">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToIgnoreCase(java.lang.CharSequence)">waitUntilEqualToIgnoreCase</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equalsIgnoreCase.</div>
</td>
</tr>
<tr class="row-color" id="i67">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToIgnoreCase(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilEqualToIgnoreCase</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equalsIgnoreCase.</div>
</td>
</tr>
<tr class="alt-color" id="i68">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToIgnoreCase(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualToIgnoreCase</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equalsIgnoreCase.</div>
</td>
</tr>
<tr class="row-color" id="i69">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable)">waitUntilGreaterThan</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i70">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i71">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i72">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(java.lang.CharSequence)">waitUntilGreaterThan</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i73">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i74">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i75">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i76">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i77">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i78">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(java.lang.CharSequence)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i79">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i80">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i81">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsEmpty()">waitUntilIsEmpty</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until value is empty.</div>
</td>
</tr>
<tr class="alt-color" id="i82">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsEmpty</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is empty.</div>
</td>
</tr>
<tr class="row-color" id="i83">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsEmpty</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is empty.</div>
</td>
</tr>
<tr class="alt-color" id="i84">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsNotEmptyAndGet()">waitUntilIsNotEmptyAndGet</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT empty, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i85">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsNotEmptyAndGet(long,java.util.concurrent.TimeUnit)">waitUntilIsNotEmptyAndGet</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT empty, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i86">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsNotEmptyAndGet(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsNotEmptyAndGet</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT empty, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i87">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable)">waitUntilLessThan</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i88">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i89">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i90">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(java.lang.CharSequence)">waitUntilLessThan</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i91">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i92">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i93">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i94">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i95">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i96">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(java.lang.CharSequence)">waitUntilLessThanOrEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i97">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i98">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i99">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilMatchesAndGet(java.lang.String)">waitUntilMatchesAndGet</a></span>&#8203;(java.lang.String&nbsp;regex)</code></th>
<td class="col-last">
<div class="block">Waiting until value is matches, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i100">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilMatchesAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">waitUntilMatchesAndGet</a></span>&#8203;(java.lang.String&nbsp;regex,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is matches, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i101">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilMatchesAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilMatchesAndGet</a></span>&#8203;(java.lang.String&nbsp;regex,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is matches, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i102">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotContainsAndGet(java.lang.CharSequence)">waitUntilNotContainsAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;s)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT contains, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i103">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotContainsAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilNotContainsAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;s,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT contains, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i104">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotContainsAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotContainsAndGet</a></span>&#8203;(java.lang.CharSequence&nbsp;s,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT contains, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i105">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable)">waitUntilNotEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal to observer value.</div>
</td>
</tr>
<tr class="alt-color" id="i106">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal to observer value.</div>
</td>
</tr>
<tr class="row-color" id="i107">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal to observer value.</div>
</td>
</tr>
<tr class="alt-color" id="i108">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(java.lang.CharSequence)">waitUntilNotEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal to cs.</div>
</td>
</tr>
<tr class="row-color" id="i109">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal to cs.</div>
</td>
</tr>
<tr class="alt-color" id="i110">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal to cs.</div>
</td>
</tr>
<tr class="row-color" id="i111">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">waitUntilNotEqualToIgnoreCase</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equalsIgnoreCase.</div>
</td>
</tr>
<tr class="alt-color" id="i112">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualToIgnoreCase</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equalsIgnoreCase.</div>
</td>
</tr>
<tr class="row-color" id="i113">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualToIgnoreCase</a></span>&#8203;(<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equalsIgnoreCase.</div>
</td>
</tr>
<tr class="alt-color" id="i114">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToIgnoreCase(java.lang.CharSequence)">waitUntilNotEqualToIgnoreCase</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equalsIgnoreCase.</div>
</td>
</tr>
<tr class="row-color" id="i115">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToIgnoreCase(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualToIgnoreCase</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equalsIgnoreCase.</div>
</td>
</tr>
<tr class="alt-color" id="i116">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToIgnoreCase(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualToIgnoreCase</a></span>&#8203;(java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equalsIgnoreCase.</div>
</td>
</tr>
<tr class="row-color" id="i117">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilStartsWithAndGet(java.lang.String)">waitUntilStartsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;prefix)</code></th>
<td class="col-last">
<div class="block">Waiting until value is startsWith, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i118">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilStartsWithAndGet(java.lang.String,int)">waitUntilStartsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;prefix,
int&nbsp;toOffset)</code></th>
<td class="col-last">
<div class="block">Waiting until value is startsWith, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i119">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilStartsWithAndGet(java.lang.String,int,long,java.util.concurrent.TimeUnit)">waitUntilStartsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;prefix,
int&nbsp;toOffset,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is startsWith, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i120">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilStartsWithAndGet(java.lang.String,int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilStartsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;prefix,
int&nbsp;toOffset,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is startsWith, and return last value.</div>
</td>
</tr>
<tr class="row-color" id="i121">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilStartsWithAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">waitUntilStartsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;prefix,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is startsWith, and return last value.</div>
</td>
</tr>
<tr class="alt-color" id="i122">
<td class="col-first"><code>default java.lang.String</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilStartsWithAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilStartsWithAndGet</a></span>&#8203;(java.lang.String&nbsp;prefix,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is startsWith, and return last value.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="computeToUpperCase()">
<h3>computeToUpperCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeToUpperCase</span>()</div>
<div class="block">Returns StringCompution of converted to uppercase.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>StringCompution of converted to uppercase</dd>
<dt>関連項目:</dt>
<dd><code>String.toUpperCase()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeToUpperCase(java.util.Locale)">
<h3>computeToUpperCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeToUpperCase</span>&#8203;(<span class="parameters">java.util.Locale&nbsp;locale)</span></div>
<div class="block">Returns StringCompution of converted to uppercase.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>locale</code> - use the case transformation rules for this locale</dd>
<dt>戻り値:</dt>
<dd>StringCompution of converted to uppercase</dd>
<dt>関連項目:</dt>
<dd><code>String.toUpperCase(Locale)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeToLowerCase()">
<h3>computeToLowerCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeToLowerCase</span>()</div>
<div class="block">Returns StringCompution of converted to lowercase.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>StringCompution of converted to lowercase</dd>
<dt>関連項目:</dt>
<dd><code>String.toLowerCase()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeToLowerCase(java.util.Locale)">
<h3>computeToLowerCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeToLowerCase</span>&#8203;(<span class="parameters">java.util.Locale&nbsp;locale)</span></div>
<div class="block">Returns StringCompution of converted to lowercase.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>locale</code> - use the case transformation rules for this locale</dd>
<dt>戻り値:</dt>
<dd>StringCompution of converted to lowercase</dd>
<dt>関連項目:</dt>
<dd><code>String.toLowerCase(Locale)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeTrim()">
<h3>computeTrim</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeTrim</span>()</div>
<div class="block">Return StringCompution of converted to trim.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>StringCompution of converted to trim</dd>
<dt>関連項目:</dt>
<dd><code>String.trim()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeConcat(java.lang.String)">
<h3>computeConcat</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeConcat</span>&#8203;(<span class="parameters">java.lang.String&nbsp;str)</span></div>
<div class="block">Returns StringCompution of convert to concat.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>str</code> - the String that is concatenated to the end of this String.</dd>
<dt>戻り値:</dt>
<dd>StringCompution of convert to concat</dd>
<dt>関連項目:</dt>
<dd><code>String.concat(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeReplace(char,char)">
<h3>computeReplace</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeReplace</span>&#8203;(<span class="parameters">char&nbsp;oldChar,
char&nbsp;newChar)</span></div>
<div class="block">Returns StringCompution of convert to replace.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>oldChar</code> - the old character.</dd>
<dd><code>newChar</code> - the new character.</dd>
<dt>戻り値:</dt>
<dd>StringCompution of convert to replace</dd>
<dt>関連項目:</dt>
<dd><code>String.replace(char, char)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeReplace(java.lang.CharSequence,java.lang.CharSequence)">
<h3>computeReplace</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeReplace</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;target,
java.lang.CharSequence&nbsp;replacement)</span></div>
<div class="block">Returns StringCompution of convert to replace.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>target</code> - The sequence of char values to be replaced</dd>
<dd><code>replacement</code> - The replacement sequence of char values</dd>
<dt>戻り値:</dt>
<dd>StringCompution of convert to replace</dd>
<dt>関連項目:</dt>
<dd><code>String.replace(CharSequence, CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeReplaceAll(java.lang.String,java.lang.String)">
<h3>computeReplaceAll</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeReplaceAll</span>&#8203;(<span class="parameters">java.lang.String&nbsp;regex,
java.lang.String&nbsp;replacement)</span></div>
<div class="block">Returns StringCompution of convert to replaceAll.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>regex</code> - the regular expression to which this string is to be matched</dd>
<dd><code>replacement</code> - the string to be substituted for each match</dd>
<dt>戻り値:</dt>
<dd>StringCompution of convert to replaceAll</dd>
<dt>関連項目:</dt>
<dd><code>String.replaceAll(String, String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeReplaceFirst(java.lang.String,java.lang.String)">
<h3>computeReplaceFirst</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">computeReplaceFirst</span>&#8203;(<span class="parameters">java.lang.String&nbsp;regex,
java.lang.String&nbsp;replacement)</span></div>
<div class="block">Returns StringCompution of convert to replaceFirst.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>regex</code> - the regular expression to which this string is to be matched</dd>
<dd><code>replacement</code> - the string to be substituted for the first match</dd>
<dt>戻り値:</dt>
<dd>StringCompution of convert to replaceFirst</dd>
<dt>関連項目:</dt>
<dd><code>String.replaceFirst(String, String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEmpty()">
<h3>computeIsEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsEmpty</span>()</div>
<div class="block">Returns BooleanCompution of isEmpty.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>BooleanCompution of isEmpty</dd>
<dt>関連項目:</dt>
<dd><code>CharSequence.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEmpty()">
<h3>computeIsNotEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsNotEmpty</span>()</div>
<div class="block">Returns BooleanCompution of NOT isEmpty.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>BooleanCompution of NOT isEmpty</dd>
<dt>関連項目:</dt>
<dd><code>CharSequence.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeContains(java.lang.CharSequence)">
<h3>computeContains</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeContains</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;s)</span></div>
<div class="block">Returns BooleanCompution of contais.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>s</code> - the sequence to search for</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of contais</dd>
<dt>関連項目:</dt>
<dd><code>String.contains(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeNotContains(java.lang.CharSequence)">
<h3>computeNotContains</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeNotContains</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;s)</span></div>
<div class="block">Returns BooleanCompution of NOT contais.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>s</code> - the sequence to search for</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of NOT contais</dd>
<dt>関連項目:</dt>
<dd><code>String.contains(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeStartsWith(java.lang.String)">
<h3>computeStartsWith</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeStartsWith</span>&#8203;(<span class="parameters">java.lang.String&nbsp;prefix)</span></div>
<div class="block">Returns BooleanCompution of startsWith.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>prefix</code> - the prefix.</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of startsWith</dd>
<dt>関連項目:</dt>
<dd><code>String.startsWith(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeStartsWith(java.lang.String,int)">
<h3>computeStartsWith</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeStartsWith</span>&#8203;(<span class="parameters">java.lang.String&nbsp;prefix,
int&nbsp;toOffset)</span></div>
<div class="block">Returns BooleanCompution of startsWith.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>prefix</code> - the prefix.</dd>
<dd><code>toOffset</code> - where to begin looking in this string.</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of startsWith</dd>
<dt>関連項目:</dt>
<dd><code>String.startsWith(String, int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeEndsWith(java.lang.String)">
<h3>computeEndsWith</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeEndsWith</span>&#8203;(<span class="parameters">java.lang.String&nbsp;suffix)</span></div>
<div class="block">Returns BooleanCompution of endsWith.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>suffix</code> - the suffix.</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of endsWith</dd>
<dt>関連項目:</dt>
<dd><code>String.endsWith(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeMatches(java.lang.String)">
<h3>computeMatches</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeMatches</span>&#8203;(<span class="parameters">java.lang.String&nbsp;regex)</span></div>
<div class="block">Returns BooleanCompution of matches.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>regex</code> - the regular expression to which this string is to be matched</dd>
<dt>戻り値:</dt>
<dd>Returns BooleanCompution of matches</dd>
<dt>関連項目:</dt>
<dd><code>String.matches(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeContentEqualTo(java.lang.CharSequence)">
<h3>computeContentEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeContentEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of contentEquals.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The sequence to compare this String against</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of contentEquals</dd>
<dt>関連項目:</dt>
<dd><code>String.contentEquals(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeContentEqualTo(java.lang.StringBuffer)">
<h3>computeContentEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeContentEqualTo</span>&#8203;(<span class="parameters">java.lang.StringBuffer&nbsp;sb)</span></div>
<div class="block">Returns BooleanCompution of contentEquals.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>sb</code> - The StringBuffer to compare this String against</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of contentEquals</dd>
<dt>関連項目:</dt>
<dd><code>String.contentEquals(StringBuffer)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeLength()">
<h3>computeLength</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeLength</span>()</div>
<div class="block">Returns IntegerCompution of String length.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>IntegerCompution of String length</dd>
<dt>関連項目:</dt>
<dd><code>String.length()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIndexOf(java.lang.String)">
<h3>computeIndexOf</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeIndexOf</span>&#8203;(<span class="parameters">java.lang.String&nbsp;str)</span></div>
<div class="block">Returns IntegerCompution of String indexOf.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>str</code> - the substring to search for.</dd>
<dt>戻り値:</dt>
<dd>IntegerCompution of String IndexOf</dd>
<dt>関連項目:</dt>
<dd><code>String.indexOf(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIndexOf(int)">
<h3>computeIndexOf</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeIndexOf</span>&#8203;(<span class="parameters">int&nbsp;ch)</span></div>
<div class="block">Returns IntegerCompution of String indexOf.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>ch</code> - a character (Unicode code point).</dd>
<dt>戻り値:</dt>
<dd>IntegerCompution of String indexOf</dd>
<dt>関連項目:</dt>
<dd><code>String.indexOf(int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIndexOf(java.lang.String,int)">
<h3>computeIndexOf</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeIndexOf</span>&#8203;(<span class="parameters">java.lang.String&nbsp;str,
int&nbsp;fromIndex)</span></div>
<div class="block">Returns IntegerCompution of String indexOf.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>str</code> - the substring to search for.</dd>
<dd><code>fromIndex</code> - the index from which to start the search.</dd>
<dt>戻り値:</dt>
<dd>IntegerCompution of String indexOf</dd>
<dt>関連項目:</dt>
<dd><code>String.indexOf(String, int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIndexOf(int,int)">
<h3>computeIndexOf</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeIndexOf</span>&#8203;(<span class="parameters">int&nbsp;ch,
int&nbsp;fromIndex)</span></div>
<div class="block">Returns IntegerCompution of String indexOf.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>ch</code> - a character (Unicode code point).</dd>
<dd><code>fromIndex</code> - the index to start the search from.</dd>
<dt>戻り値:</dt>
<dd>IntegerCompution of String indexOf</dd>
<dt>関連項目:</dt>
<dd><code>String.indexOf(int, int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeLastIndexOf(java.lang.String)">
<h3>computeLastIndexOf</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeLastIndexOf</span>&#8203;(<span class="parameters">java.lang.String&nbsp;str)</span></div>
<div class="block">Returns IntegerCompution of String lastIndexOf.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>str</code> - the substring to search for.</dd>
<dt>戻り値:</dt>
<dd>IntegerCompution of String lastIndexOf</dd>
<dt>関連項目:</dt>
<dd><code>String.lastIndexOf(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeLastIndexOf(java.lang.String,int)">
<h3>computeLastIndexOf</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeLastIndexOf</span>&#8203;(<span class="parameters">java.lang.String&nbsp;str,
int&nbsp;fromIndex)</span></div>
<div class="block">Returns IntegerCompution of String lastIndexOf.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>str</code> - the substring to search for.</dd>
<dd><code>fromIndex</code> - the index to start the search from.</dd>
<dt>戻り値:</dt>
<dd>IntegerCompution of String lastIndexOf</dd>
<dt>関連項目:</dt>
<dd><code>String.lastIndexOf(String, int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeLastIndexOf(int)">
<h3>computeLastIndexOf</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeLastIndexOf</span>&#8203;(<span class="parameters">int&nbsp;ch)</span></div>
<div class="block">Returns IntegerCompution of String lastIndexOf.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>ch</code> - a character (Unicode code point).</dd>
<dt>戻り値:</dt>
<dd>IntegerCompution of String lastIndexOf</dd>
<dt>関連項目:</dt>
<dd><code>String.lastIndexOf(int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeLastIndexOf(int,int)">
<h3>computeLastIndexOf</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeLastIndexOf</span>&#8203;(<span class="parameters">int&nbsp;ch,
int&nbsp;fromIndex)</span></div>
<div class="block">Returns IntegerCompution of String lastIndexOf.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>ch</code> - a character (Unicode code point).</dd>
<dd><code>fromIndex</code> - the index to start the search from. There is no restriction on the value of fromIndex. If it is greater than or equal to the length of this string, it has the same effect as if it were equal to one less than the length of this string: this entire string may be searched. If it is negative, it has the same effect as if it were -1: -1 is returned.</dd>
<dt>戻り値:</dt>
<dd>IntegerCompution of String lastIndexOf</dd>
<dt>関連項目:</dt>
<dd><code>String.lastIndexOf(int, int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualTo(java.lang.CharSequence)">
<h3>computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of String equals.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of String equals</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualTo(com.shimizukenta.secs.local.property.StringObservable)">
<h3>computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns BooleanCompution of String equals.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of String equals</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(java.lang.CharSequence)">
<h3>computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of NOT String equals.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of NOT String equals</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(com.shimizukenta.secs.local.property.StringObservable)">
<h3>computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns BooleanCompution of NOT String equals.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of NOT String equals</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThan(java.lang.CharSequence)">
<h3>computeIsLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsLessThan</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of <code>String#compareTo &lt; 0</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of <code>String#compareTo &lt; 0</code></dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThan(com.shimizukenta.secs.local.property.StringObservable)">
<h3>computeIsLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsLessThan</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns BooleanCompution of <code>String#compareTo &lt; 0</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of <code>String#compareTo &lt; 0</code></dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanOrEqualTo(java.lang.CharSequence)">
<h3>computeIsLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanOrEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of <code>String#compareTo &lt;= 0</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of <code>String#compareTo &lt;= 0</code></dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">
<h3>computeIsLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns BooleanCompution of <code>String#compareTo &lt;= 0</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of <code>String#compareTo &lt;= 0</code></dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThan(java.lang.CharSequence)">
<h3>computeIsGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThan</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of <code>String#compareTo &gt; 0</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of <code>String#compareTo &gt; 0</code></dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThan(com.shimizukenta.secs.local.property.StringObservable)">
<h3>computeIsGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThan</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns BooleanCompution of <code>String#compareTo &gt; 0</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of <code>String#compareTo &gt; 0</code></dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanOrEqualTo(java.lang.CharSequence)">
<h3>computeIsGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of <code>String#compareTo &gt;= 0</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of <code>String#compareTo &gt;= 0</code></dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">
<h3>computeIsGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns BooleanCompution of <code>String#compareTo &gt;= 0</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of <code>String#compareTo &gt;= 0</code></dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualToIgnoreCase(java.lang.CharSequence)">
<h3>computeIsEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsEqualToIgnoreCase</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of String equalsIgnoreCase.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of String equalsIgnoreCase</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">
<h3>computeIsEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsEqualToIgnoreCase</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns BooleanCompution of String equalsIgnoreCase.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of String equalsIgnoreCase</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualToIgnoreCase(java.lang.CharSequence)">
<h3>computeIsNotEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualToIgnoreCase</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns BooleanCompution of NOT String equalsIgnoreCase.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of NOT String equalsIgnoreCase</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">
<h3>computeIsNotEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualToIgnoreCase</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns BooleanCompution of NOT String equalsIgnoreCase.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of NOT String equalsIgnoreCase</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsEmpty()">
<h3>waitUntilIsEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsEmpty</span>()
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is empty.
 
 <p>
 This is blocking method.<br/>
 If already value is empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsEmpty(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilIsEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsEmpty</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is empty.
 
 <p>
 This is blocking method.<br/>
 If already value is empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilIsEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsEmpty</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is empty.
 
 <p>
 This is blocking method.<br/>
 If already value is empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsNotEmptyAndGet()">
<h3>waitUntilIsNotEmptyAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilIsNotEmptyAndGet</span>()
                                            throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT empty, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is empty, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsNotEmptyAndGet(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilIsNotEmptyAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilIsNotEmptyAndGet</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                            throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT empty, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is empty, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsNotEmptyAndGet(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilIsNotEmptyAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilIsNotEmptyAndGet</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                            throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT empty, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is empty, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContainsAndGet(java.lang.CharSequence)">
<h3>waitUntilContainsAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContainsAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;s)</span>
                                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is contains, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contains, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>s</code> - the sequence to search for</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.contains(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContainsAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilContainsAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContainsAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;s,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is contains, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contains, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>s</code> - the sequence to search for</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.contains(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContainsAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilContainsAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContainsAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;s,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is contains, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contains, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>s</code> - the sequence to search for</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.contains(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotContainsAndGet(java.lang.CharSequence)">
<h3>waitUntilNotContainsAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilNotContainsAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;s)</span>
                                             throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT contains, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contains, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>s</code> - the sequence to search for</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.contains(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotContainsAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotContainsAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilNotContainsAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;s,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                             throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT contains, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is NOT contains, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>s</code> - the sequence to search for</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.contains(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotContainsAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotContainsAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilNotContainsAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;s,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                             throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT contains, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is NOT contains, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>s</code> - the sequence to search for</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.contains(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilStartsWithAndGet(java.lang.String)">
<h3>waitUntilStartsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilStartsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;prefix)</span>
                                            throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is startsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is startsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>prefix</code> - the prefix</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.startsWith(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilStartsWithAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilStartsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilStartsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;prefix,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                            throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is startsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is startsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>prefix</code> - the prefix</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.startsWith(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilStartsWithAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilStartsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilStartsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;prefix,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                            throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is startsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is startsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>prefix</code> - the prefix</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.startsWith(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilStartsWithAndGet(java.lang.String,int)">
<h3>waitUntilStartsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilStartsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;prefix,
int&nbsp;toOffset)</span>
                                            throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is startsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is startsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>prefix</code> - the prefix.</dd>
<dd><code>toOffset</code> - where to begin looking in this string.</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.startsWith(String, int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilStartsWithAndGet(java.lang.String,int,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilStartsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilStartsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;prefix,
int&nbsp;toOffset,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                            throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is startsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is startsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>prefix</code> - the prefix.</dd>
<dd><code>toOffset</code> - where to begin looking in this string.</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.startsWith(String, int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilStartsWithAndGet(java.lang.String,int,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilStartsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilStartsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;prefix,
int&nbsp;toOffset,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                            throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is startsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is startsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>prefix</code> - the prefix.</dd>
<dd><code>toOffset</code> - where to begin looking in this string.</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.startsWith(String, int)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEndsWithAndGet(java.lang.String)">
<h3>waitUntilEndsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilEndsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;suffix)</span>
                                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is endsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is endsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>suffix</code> - the suffix</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.endsWith(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEndsWithAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEndsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilEndsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;suffix,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is endsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is endsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>suffix</code> - the suffix</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.endsWith(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEndsWithAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEndsWithAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilEndsWithAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;suffix,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is endsWith, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is endsWith, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>suffix</code> - the suffix</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.endsWith(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilMatchesAndGet(java.lang.String)">
<h3>waitUntilMatchesAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilMatchesAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;regex)</span>
                                         throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is matches, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is matches, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>regex</code> - the regular expression to which this string is to be matched</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.matches(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilMatchesAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilMatchesAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilMatchesAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;regex,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                         throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is matches, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is matches, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>regex</code> - the regular expression to which this string is to be matched</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.matches(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilMatchesAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilMatchesAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilMatchesAndGet</span>&#8203;(<span class="parameters">java.lang.String&nbsp;regex,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                         throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is matches, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is matches, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>regex</code> - the regular expression to which this string is to be matched</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.matches(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContentEqualToAndGet(java.lang.CharSequence)">
<h3>waitUntilContentEqualToAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContentEqualToAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                                                throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is contentEquals, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contentEquals, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The sequence to compare this String against</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.contentEquals(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContentEqualToAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilContentEqualToAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContentEqualToAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                                throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is contentEquals, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contentEquals, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The sequence to compare this String against</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.contentEquals(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContentEqualToAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilContentEqualToAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContentEqualToAndGet</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                                throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is contentEquals, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contentEquals, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The sequence to compare this String against</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.contentEquals(CharSequence)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContentEqualToAndGet(java.lang.StringBuffer)">
<h3>waitUntilContentEqualToAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContentEqualToAndGet</span>&#8203;(<span class="parameters">java.lang.StringBuffer&nbsp;sb)</span>
                                                throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is contentEquals, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contentEquals, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>sb</code> - The StringBuffer to compare this String against</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.contentEquals(StringBuffer)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContentEqualToAndGet(java.lang.StringBuffer,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilContentEqualToAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContentEqualToAndGet</span>&#8203;(<span class="parameters">java.lang.StringBuffer&nbsp;sb,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                                throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is contentEquals, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contentEquals, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>sb</code> - The StringBuffer to compare this String against</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.contentEquals(StringBuffer)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContentEqualToAndGet(java.lang.StringBuffer,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilContentEqualToAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.lang.String</span>&nbsp;<span class="member-name">waitUntilContentEqualToAndGet</span>&#8203;(<span class="parameters">java.lang.StringBuffer&nbsp;sb,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                                throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is contentEquals, and return last value.
 
 <p>
 This is blocking method.<br/>
 If already value is contentEquals, return last value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>sb</code> - The StringBuffer to compare this String against</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>last value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.contentEquals(StringBuffer)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(java.lang.CharSequence)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is equal to cs.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equal to cs.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equal to cs.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span>
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is equal to observer.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equal to observer.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equal to observer.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(java.lang.CharSequence)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT equal to cs.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equal to cs.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equal to cs.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the character sequence</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span>
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT equal to observer value.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equal to observer value.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equal to observer value.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equals(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(java.lang.CharSequence)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span>
                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(java.lang.CharSequence)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(java.lang.CharSequence)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(java.lang.CharSequence)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(cs.toString()) &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the Character Sequence</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is <code>compareTo(observer.toString()) &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the observer</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.compareTo(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToIgnoreCase(java.lang.CharSequence)">
<h3>waitUntilEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToIgnoreCase</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The Character Sequence to compare this String against</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToIgnoreCase(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToIgnoreCase</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The Character Sequence to compare this String against</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToIgnoreCase(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToIgnoreCase</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The Character Sequence to compare this String against</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">
<h3>waitUntilEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToIgnoreCase</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - The observer to compare this String against</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToIgnoreCase</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - The observer to compare this String against</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToIgnoreCase</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - The observer to compare this String against</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToIgnoreCase(java.lang.CharSequence)">
<h3>waitUntilNotEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToIgnoreCase</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The Character Sequence to compare this String against</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToIgnoreCase(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToIgnoreCase</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The Character Sequence to compare this String against</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToIgnoreCase(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToIgnoreCase</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - The Character Sequence to compare this String against</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">
<h3>waitUntilNotEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToIgnoreCase</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - The observer to compare this String against</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToIgnoreCase</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - The observer to compare this String against</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualToIgnoreCase</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToIgnoreCase</span>&#8203;(<span class="parameters"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equalsIgnoreCase.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - The observer to compare this String against</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>String.equalsIgnoreCase(String)</code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
