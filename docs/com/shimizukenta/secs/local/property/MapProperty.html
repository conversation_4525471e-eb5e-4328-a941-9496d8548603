<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>MapProperty</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: MapProperty">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">ネスト</a>&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース MapProperty" class="title">インタフェース MapProperty&lt;K,&#8203;V&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>K</code> - Key Type</dd>
<dd><code>V</code> - Value Type</dd>
</dl>
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code>java.util.Map&lt;K,&#8203;V&gt;</code>, <code><a href="MapObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapObservable</a>&lt;K,&#8203;V&gt;</code>, <code><a href="MapSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapSettable</a>&lt;K,&#8203;V&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.util.Map&lt;K,&#8203;V&gt;&gt;</code>, <code>java.io.Serializable</code>, <code><a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a>&lt;java.util.Map&lt;K,&#8203;V&gt;&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">MapProperty&lt;K,&#8203;V&gt;</span>
extends java.util.Map&lt;K,&#8203;V&gt;, <a href="MapSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapSettable</a>&lt;K,&#8203;V&gt;, <a href="MapObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapObservable</a>&lt;K,&#8203;V&gt;, java.io.Serializable</pre>
<div class="block">Map value Property, includes Getter, Setter, Observer.
 
 <ul>
 <li>To build instance, <a href="#newInstance()"><code>newInstance()</code></a></li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>To compute,
 <ul>
 <li><a href="MapObservable.html#computeIsEmpty()"><code>MapObservable.computeIsEmpty()</code></a></li>
 <li><a href="MapObservable.html#computeIsNotEmpty()"><code>MapObservable.computeIsNotEmpty()</code></a></li>
 <li><a href="MapObservable.html#computeContainsKey(java.lang.Object)"><code>MapObservable.computeContainsKey(Object)</code></a></li>
 <li><a href="MapObservable.html#computeNotContainsKey(java.lang.Object)"><code>MapObservable.computeNotContainsKey(Object)</code></a></li>
 <li><a href="MapObservable.html#computeKeySet()"><code>MapObservable.computeKeySet()</code></a></li>
 <li><a href="MapObservable.html#computeSize()"><code>MapObservable.computeSize()</code></a></li>
 </ul>
 </li>
 <li>To wait until condition is true,
 <ul>
 <li><a href="MapObservable.html#waitUntilIsEmpty()"><code>MapObservable.waitUntilIsEmpty()</code></a></li>
 <li><a href="MapObservable.html#waitUntilIsNotEmpty()"><code>MapObservable.waitUntilIsNotEmpty()</code></a></li>
 <li><a href="MapObservable.html#waitUntilContainsKeyAndGet(java.lang.Object)"><code>MapObservable.waitUntilContainsKeyAndGet(Object)</code></a></li>
 <li><a href="MapObservable.html#waitUntilNotContainsKey(java.lang.Object)"><code>MapObservable.waitUntilNotContainsKey(Object)</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="MapSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>MapSettable</code></a>, 
<a href="MapObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>MapObservable</code></a>, 
<code>Map</code></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested.class.summary">
<h2>ネストされたクラスの概要</h2>
<div class="inherited-list">
<h2 id="nested.classes.inherited.from.class.java.util.Map">インタフェースから継承されたネストされたクラス/インタフェース&nbsp;java.util.Map</h2>
<code>java.util.Map.Entry&lt;K extends java.lang.Object,&#8203;V extends java.lang.Object&gt;</code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<table class="summary-table">
<caption><span>staticメソッド</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>static &lt;K,&#8203;
V&gt;&nbsp;<a href="MapProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapProperty</a>&lt;K,&#8203;V&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance()">newInstance</a></span>()</code></th>
<td class="col-last">
<div class="block">Instance builder.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>static &lt;K,&#8203;
V&gt;&nbsp;<a href="MapProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapProperty</a>&lt;K,&#8203;V&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(java.util.Map)">newInstance</a></span>&#8203;(java.util.Map&lt;? extends K,&#8203;? extends V&gt;&nbsp;initial)</code></th>
<td class="col-last">
<div class="block">Instance builder.</div>
</td>
</tr>
</tbody>
</table>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.util.Map">インタフェースから継承されたメソッド&nbsp;java.util.Map</h3>
<code>clear, compute, computeIfAbsent, computeIfPresent, containsKey, containsValue, entrySet, equals, forEach, get, getOrDefault, hashCode, isEmpty, keySet, merge, put, putAll, putIfAbsent, remove, remove, replace, replace, replaceAll, size, values</code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.MapObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="MapObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapObservable</a></h3>
<code><a href="MapObservable.html#computeContainsKey(java.lang.Object)">computeContainsKey</a>, <a href="MapObservable.html#computeIsEmpty()">computeIsEmpty</a>, <a href="MapObservable.html#computeIsNotEmpty()">computeIsNotEmpty</a>, <a href="MapObservable.html#computeKeySet()">computeKeySet</a>, <a href="MapObservable.html#computeNotContainsKey(java.lang.Object)">computeNotContainsKey</a>, <a href="MapObservable.html#computeSize()">computeSize</a>, <a href="MapObservable.html#waitUntilContainsKeyAndGet(java.lang.Object)">waitUntilContainsKeyAndGet</a>, <a href="MapObservable.html#waitUntilContainsKeyAndGet(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilContainsKeyAndGet</a>, <a href="MapObservable.html#waitUntilContainsKeyAndGet(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContainsKeyAndGet</a>, <a href="MapObservable.html#waitUntilIsEmpty()">waitUntilIsEmpty</a>, <a href="MapObservable.html#waitUntilIsEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsEmpty</a>, <a href="MapObservable.html#waitUntilIsEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsEmpty</a>, <a href="MapObservable.html#waitUntilIsNotEmpty()">waitUntilIsNotEmpty</a>, <a href="MapObservable.html#waitUntilIsNotEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsNotEmpty</a>, <a href="MapObservable.html#waitUntilIsNotEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsNotEmpty</a>, <a href="MapObservable.html#waitUntilNotContainsKey(java.lang.Object)">waitUntilNotContainsKey</a>, <a href="MapObservable.html#waitUntilNotContainsKey(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilNotContainsKey</a>, <a href="MapObservable.html#waitUntilNotContainsKey(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotContainsKey</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Settable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a></h3>
<code><a href="Settable.html#bind(com.shimizukenta.secs.local.property.Observable)">bind</a>, <a href="Settable.html#unbind(com.shimizukenta.secs.local.property.Observable)">unbind</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="newInstance()">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="type-parameters">&lt;K,&#8203;
V&gt;</span>&nbsp;<span class="return-type"><a href="MapProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapProperty</a>&lt;K,&#8203;V&gt;</span>&nbsp;<span class="member-name">newInstance</span>()</div>
<div class="block">Instance builder.</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>K</code> - Key Type</dd>
<dd><code>V</code> - Value Type</dd>
<dt>戻り値:</dt>
<dd>new-instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newInstance(java.util.Map)">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="type-parameters">&lt;K,&#8203;
V&gt;</span>&nbsp;<span class="return-type"><a href="MapProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapProperty</a>&lt;K,&#8203;V&gt;</span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters">java.util.Map&lt;? extends K,&#8203;? extends V&gt;&nbsp;initial)</span></div>
<div class="block">Instance builder.</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>K</code> - Key Type</dd>
<dd><code>V</code> - Value Type</dd>
<dt>パラメータ:</dt>
<dd><code>initial</code> - the initial <code>Map&lt;K, V&gt;</code></dd>
<dt>戻り値:</dt>
<dd>new-instance.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">ネスト</a>&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
