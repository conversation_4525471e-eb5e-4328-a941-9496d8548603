<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>StringCompution</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: StringCompution">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース StringCompution" class="title">インタフェース StringCompution</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code>java.lang.CharSequence</code>, <code>java.lang.Comparable&lt;<a href="StringGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringGettable</a>&gt;</code>, <code><a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;java.lang.String&gt;</code>, <code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;java.lang.String&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.String&gt;</code>, <code>java.io.Serializable</code>, <code><a href="StringGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringGettable</a></code>, <code><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">StringCompution</span>
extends <a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;java.lang.String&gt;, <a href="StringGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringGettable</a>, <a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a></pre>
<div class="block">String value compution, includes Getter, Observer.
 
 <p>
 <strong>NOT</strong> includes Setter.<br/>
 </p>
 <p>
 This instance is built from other Property or Compution.<br/>
 </p>
 <ul>
 <li>To build joined StringCompution, <a href="#join(java.lang.CharSequence,com.shimizukenta.secs.local.property.StringObservable...)"><code>join(CharSequence, StringObservable...)</code></a>.</li>
 <li>To toString(), <code>CharSequence.toString()</code></li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>To compute,
 <ul>
 <li><a href="StringObservable.html#computeIsEmpty()"><code>StringObservable.computeIsEmpty()</code></a>.</li>
 <li><a href="StringObservable.html#computeIsNotEmpty()"><code>StringObservable.computeIsNotEmpty()</code></a></li>
 <li><a href="StringObservable.html#computeContains(java.lang.CharSequence)"><code>StringObservable.computeContains(CharSequence)</code></a></li>
 <li><a href="StringObservable.html#computeTrim()"><code>StringObservable.computeTrim()</code></a></li>
 <li><a href="StringObservable.html#computeToUpperCase()"><code>StringObservable.computeToUpperCase()</code></a></li>
 <li><a href="StringObservable.html#computeToLowerCase()"><code>StringObservable.computeToLowerCase()</code></a></li>
 <li><a href="StringObservable.html#computeMatches(java.lang.String)"><code>StringObservable.computeMatches(String)</code></a></li>
 <li><a href="StringObservable.html#computeLength()"><code>StringObservable.computeLength()</code></a></li>
 </ul>
 </li>
 <li>To wait until condition is true,
 <ul>
 <li><a href="StringObservable.html#waitUntilIsEmpty()"><code>StringObservable.waitUntilIsEmpty()</code></a></li>
 <li><a href="StringObservable.html#waitUntilIsNotEmptyAndGet()"><code>StringObservable.waitUntilIsNotEmptyAndGet()</code></a></li>
 <li><a href="StringObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable)"><code>StringObservable.waitUntilEqualTo(StringObservable)</code></a></li>
 <li><a href="StringObservable.html#waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)"><code>StringObservable.waitUntilEqualToIgnoreCase(StringObservable)</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="StringGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>StringGettable</code></a>, 
<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>StringObservable</code></a>, 
<a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Compution</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<table class="summary-table">
<caption><span>staticメソッド</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>static <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#join(java.lang.CharSequence,com.shimizukenta.secs.local.property.StringObservable...)">join</a></span>&#8203;(java.lang.CharSequence&nbsp;delimiter,
<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>...&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns joined StringCompution.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>static <a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#join(java.lang.CharSequence,java.lang.Iterable)">join</a></span>&#8203;(java.lang.CharSequence&nbsp;delimiter,
java.lang.Iterable&lt;<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&gt;&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns joined StringCompution.</div>
</td>
</tr>
</tbody>
</table>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.lang.CharSequence">インタフェースから継承されたメソッド&nbsp;java.lang.CharSequence</h3>
<code>chars, codePoints, isEmpty, toString</code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.StringGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="StringGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringGettable</a></h3>
<code><a href="StringGettable.html#charAt(int)">charAt</a>, <a href="StringGettable.html#compareTo(com.shimizukenta.secs.local.property.StringGettable)">compareTo</a>, <a href="StringGettable.html#length()">length</a>, <a href="StringGettable.html#subSequence(int,int)">subSequence</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.StringObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a></h3>
<code><a href="StringObservable.html#computeConcat(java.lang.String)">computeConcat</a>, <a href="StringObservable.html#computeContains(java.lang.CharSequence)">computeContains</a>, <a href="StringObservable.html#computeContentEqualTo(java.lang.CharSequence)">computeContentEqualTo</a>, <a href="StringObservable.html#computeContentEqualTo(java.lang.StringBuffer)">computeContentEqualTo</a>, <a href="StringObservable.html#computeEndsWith(java.lang.String)">computeEndsWith</a>, <a href="StringObservable.html#computeIndexOf(int)">computeIndexOf</a>, <a href="StringObservable.html#computeIndexOf(int,int)">computeIndexOf</a>, <a href="StringObservable.html#computeIndexOf(java.lang.String)">computeIndexOf</a>, <a href="StringObservable.html#computeIndexOf(java.lang.String,int)">computeIndexOf</a>, <a href="StringObservable.html#computeIsEmpty()">computeIsEmpty</a>, <a href="StringObservable.html#computeIsEqualTo(com.shimizukenta.secs.local.property.StringObservable)">computeIsEqualTo</a>, <a href="StringObservable.html#computeIsEqualTo(java.lang.CharSequence)">computeIsEqualTo</a>, <a href="StringObservable.html#computeIsEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">computeIsEqualToIgnoreCase</a>, <a href="StringObservable.html#computeIsEqualToIgnoreCase(java.lang.CharSequence)">computeIsEqualToIgnoreCase</a>, <a href="StringObservable.html#computeIsGreaterThan(com.shimizukenta.secs.local.property.StringObservable)">computeIsGreaterThan</a>, <a href="StringObservable.html#computeIsGreaterThan(java.lang.CharSequence)">computeIsGreaterThan</a>, <a href="StringObservable.html#computeIsGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">computeIsGreaterThanOrEqualTo</a>, <a href="StringObservable.html#computeIsGreaterThanOrEqualTo(java.lang.CharSequence)">computeIsGreaterThanOrEqualTo</a>, <a href="StringObservable.html#computeIsLessThan(com.shimizukenta.secs.local.property.StringObservable)">computeIsLessThan</a>, <a href="StringObservable.html#computeIsLessThan(java.lang.CharSequence)">computeIsLessThan</a>, <a href="StringObservable.html#computeIsLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">computeIsLessThanOrEqualTo</a>, <a href="StringObservable.html#computeIsLessThanOrEqualTo(java.lang.CharSequence)">computeIsLessThanOrEqualTo</a>, <a href="StringObservable.html#computeIsNotEmpty()">computeIsNotEmpty</a>, <a href="StringObservable.html#computeIsNotEqualTo(com.shimizukenta.secs.local.property.StringObservable)">computeIsNotEqualTo</a>, <a href="StringObservable.html#computeIsNotEqualTo(java.lang.CharSequence)">computeIsNotEqualTo</a>, <a href="StringObservable.html#computeIsNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">computeIsNotEqualToIgnoreCase</a>, <a href="StringObservable.html#computeIsNotEqualToIgnoreCase(java.lang.CharSequence)">computeIsNotEqualToIgnoreCase</a>, <a href="StringObservable.html#computeLastIndexOf(int)">computeLastIndexOf</a>, <a href="StringObservable.html#computeLastIndexOf(int,int)">computeLastIndexOf</a>, <a href="StringObservable.html#computeLastIndexOf(java.lang.String)">computeLastIndexOf</a>, <a href="StringObservable.html#computeLastIndexOf(java.lang.String,int)">computeLastIndexOf</a>, <a href="StringObservable.html#computeLength()">computeLength</a>, <a href="StringObservable.html#computeMatches(java.lang.String)">computeMatches</a>, <a href="StringObservable.html#computeNotContains(java.lang.CharSequence)">computeNotContains</a>, <a href="StringObservable.html#computeReplace(char,char)">computeReplace</a>, <a href="StringObservable.html#computeReplace(java.lang.CharSequence,java.lang.CharSequence)">computeReplace</a>, <a href="StringObservable.html#computeReplaceAll(java.lang.String,java.lang.String)">computeReplaceAll</a>, <a href="StringObservable.html#computeReplaceFirst(java.lang.String,java.lang.String)">computeReplaceFirst</a>, <a href="StringObservable.html#computeStartsWith(java.lang.String)">computeStartsWith</a>, <a href="StringObservable.html#computeStartsWith(java.lang.String,int)">computeStartsWith</a>, <a href="StringObservable.html#computeToLowerCase()">computeToLowerCase</a>, <a href="StringObservable.html#computeToLowerCase(java.util.Locale)">computeToLowerCase</a>, <a href="StringObservable.html#computeToUpperCase()">computeToUpperCase</a>, <a href="StringObservable.html#computeToUpperCase(java.util.Locale)">computeToUpperCase</a>, <a href="StringObservable.html#computeTrim()">computeTrim</a>, <a href="StringObservable.html#waitUntilContainsAndGet(java.lang.CharSequence)">waitUntilContainsAndGet</a>, <a href="StringObservable.html#waitUntilContainsAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilContainsAndGet</a>, <a href="StringObservable.html#waitUntilContainsAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContainsAndGet</a>, <a href="StringObservable.html#waitUntilContentEqualToAndGet(java.lang.CharSequence)">waitUntilContentEqualToAndGet</a>, <a href="StringObservable.html#waitUntilContentEqualToAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilContentEqualToAndGet</a>, <a href="StringObservable.html#waitUntilContentEqualToAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContentEqualToAndGet</a>, <a href="StringObservable.html#waitUntilContentEqualToAndGet(java.lang.StringBuffer)">waitUntilContentEqualToAndGet</a>, <a href="StringObservable.html#waitUntilContentEqualToAndGet(java.lang.StringBuffer,long,java.util.concurrent.TimeUnit)">waitUntilContentEqualToAndGet</a>, <a href="StringObservable.html#waitUntilContentEqualToAndGet(java.lang.StringBuffer,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContentEqualToAndGet</a>, <a href="StringObservable.html#waitUntilEndsWithAndGet(java.lang.String)">waitUntilEndsWithAndGet</a>, <a href="StringObservable.html#waitUntilEndsWithAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">waitUntilEndsWithAndGet</a>, <a href="StringObservable.html#waitUntilEndsWithAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEndsWithAndGet</a>, <a href="StringObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable)">waitUntilEqualTo</a>, <a href="StringObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="StringObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="StringObservable.html#waitUntilEqualTo(java.lang.CharSequence)">waitUntilEqualTo</a>, <a href="StringObservable.html#waitUntilEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="StringObservable.html#waitUntilEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="StringObservable.html#waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">waitUntilEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilEqualToIgnoreCase(java.lang.CharSequence)">waitUntilEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilEqualToIgnoreCase(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilEqualToIgnoreCase(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable)">waitUntilGreaterThan</a>, <a href="StringObservable.html#waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a>, <a href="StringObservable.html#waitUntilGreaterThan(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a>, <a href="StringObservable.html#waitUntilGreaterThan(java.lang.CharSequence)">waitUntilGreaterThan</a>, <a href="StringObservable.html#waitUntilGreaterThan(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a>, <a href="StringObservable.html#waitUntilGreaterThan(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a>, <a href="StringObservable.html#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">waitUntilGreaterThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilGreaterThanOrEqualTo(java.lang.CharSequence)">waitUntilGreaterThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilGreaterThanOrEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilGreaterThanOrEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilIsEmpty()">waitUntilIsEmpty</a>, <a href="StringObservable.html#waitUntilIsEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsEmpty</a>, <a href="StringObservable.html#waitUntilIsEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsEmpty</a>, <a href="StringObservable.html#waitUntilIsNotEmptyAndGet()">waitUntilIsNotEmptyAndGet</a>, <a href="StringObservable.html#waitUntilIsNotEmptyAndGet(long,java.util.concurrent.TimeUnit)">waitUntilIsNotEmptyAndGet</a>, <a href="StringObservable.html#waitUntilIsNotEmptyAndGet(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsNotEmptyAndGet</a>, <a href="StringObservable.html#waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable)">waitUntilLessThan</a>, <a href="StringObservable.html#waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a>, <a href="StringObservable.html#waitUntilLessThan(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a>, <a href="StringObservable.html#waitUntilLessThan(java.lang.CharSequence)">waitUntilLessThan</a>, <a href="StringObservable.html#waitUntilLessThan(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a>, <a href="StringObservable.html#waitUntilLessThan(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a>, <a href="StringObservable.html#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable)">waitUntilLessThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilLessThanOrEqualTo(java.lang.CharSequence)">waitUntilLessThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilLessThanOrEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilLessThanOrEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a>, <a href="StringObservable.html#waitUntilMatchesAndGet(java.lang.String)">waitUntilMatchesAndGet</a>, <a href="StringObservable.html#waitUntilMatchesAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">waitUntilMatchesAndGet</a>, <a href="StringObservable.html#waitUntilMatchesAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilMatchesAndGet</a>, <a href="StringObservable.html#waitUntilNotContainsAndGet(java.lang.CharSequence)">waitUntilNotContainsAndGet</a>, <a href="StringObservable.html#waitUntilNotContainsAndGet(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilNotContainsAndGet</a>, <a href="StringObservable.html#waitUntilNotContainsAndGet(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotContainsAndGet</a>, <a href="StringObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable)">waitUntilNotEqualTo</a>, <a href="StringObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="StringObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="StringObservable.html#waitUntilNotEqualTo(java.lang.CharSequence)">waitUntilNotEqualTo</a>, <a href="StringObservable.html#waitUntilNotEqualTo(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="StringObservable.html#waitUntilNotEqualTo(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="StringObservable.html#waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable)">waitUntilNotEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilNotEqualToIgnoreCase(com.shimizukenta.secs.local.property.StringObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilNotEqualToIgnoreCase(java.lang.CharSequence)">waitUntilNotEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilNotEqualToIgnoreCase(java.lang.CharSequence,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilNotEqualToIgnoreCase(java.lang.CharSequence,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualToIgnoreCase</a>, <a href="StringObservable.html#waitUntilStartsWithAndGet(java.lang.String)">waitUntilStartsWithAndGet</a>, <a href="StringObservable.html#waitUntilStartsWithAndGet(java.lang.String,int)">waitUntilStartsWithAndGet</a>, <a href="StringObservable.html#waitUntilStartsWithAndGet(java.lang.String,int,long,java.util.concurrent.TimeUnit)">waitUntilStartsWithAndGet</a>, <a href="StringObservable.html#waitUntilStartsWithAndGet(java.lang.String,int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilStartsWithAndGet</a>, <a href="StringObservable.html#waitUntilStartsWithAndGet(java.lang.String,long,java.util.concurrent.TimeUnit)">waitUntilStartsWithAndGet</a>, <a href="StringObservable.html#waitUntilStartsWithAndGet(java.lang.String,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilStartsWithAndGet</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="join(java.lang.CharSequence,com.shimizukenta.secs.local.property.StringObservable...)">
<h3>join</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">join</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;delimiter,
<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>...&nbsp;observers)</span></div>
<div class="block">Returns joined StringCompution.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>delimiter</code> - CharSequence</dd>
<dd><code>observers</code> - StringObservers</dd>
<dt>戻り値:</dt>
<dd>joined StringCompution</dd>
<dt>関連項目:</dt>
<dd><code>StringJoiner</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="join(java.lang.CharSequence,java.lang.Iterable)">
<h3>join</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></span>&nbsp;<span class="member-name">join</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;delimiter,
java.lang.Iterable&lt;<a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a>&gt;&nbsp;observers)</span></div>
<div class="block">Returns joined StringCompution.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>delimiter</code> - CharSequence</dd>
<dd><code>observers</code> - StringObservers</dd>
<dt>戻り値:</dt>
<dd>joined StringCompution</dd>
<dt>関連項目:</dt>
<dd><code>StringJoiner</code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
