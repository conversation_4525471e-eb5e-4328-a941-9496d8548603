<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>BooleanObservable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: BooleanObservable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":18,"i1":18,"i2":18,"i3":18,"i4":18,"i5":18,"i6":18,"i7":18,"i8":18,"i9":6,"i10":6,"i11":18,"i12":18,"i13":18,"i14":18,"i15":18,"i16":18,"i17":18,"i18":18,"i19":18};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"],16:["t5","\u30C7\u30D5\u30A9\u30EB\u30C8\u30FB\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース BooleanObservable" class="title">インタフェース BooleanObservable</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.Boolean&gt;</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code>, <code><a href="BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></code>, <code><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code>, <code><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">BooleanObservable</span>
extends <a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.Boolean&gt;</pre>
<div class="block">Boolean value Observer.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>Boolean</code>, 
<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Observable</code></a>, 
<a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>LogicalCompution</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t5" class="table-tab" onclick="show(16);">デフォルト・メソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#and(boolean)">and</a></span>&#8203;(boolean&nbsp;f)</code></th>
<td class="col-last">
<div class="block">Returns <code>(this &amp;&amp; f)</code> LogicalCompution instance.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#and(com.shimizukenta.secs.local.property.BooleanObservable)">and</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns <code>(this &amp;&amp; observer)</code> LogicalCompution instance.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#nand(boolean)">nand</a></span>&#8203;(boolean&nbsp;f)</code></th>
<td class="col-last">
<div class="block">Returns <code>(! (this &amp;&amp; f))</code> LogicalCompution instance.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#nand(com.shimizukenta.secs.local.property.BooleanObservable)">nand</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns <code>(! (this &amp;&amp; observer))</code> LogicalCompution instance.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#nor(boolean)">nor</a></span>&#8203;(boolean&nbsp;f)</code></th>
<td class="col-last">
<div class="block">Returns <code>(! (this || f))</code> LogicalCompution instance.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#nor(com.shimizukenta.secs.local.property.BooleanObservable)">nor</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns <code>(! (this || observer))</code>aaa LogicalCompution instance.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#not()">not</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns (! this) LogicalCompution instance.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#or(boolean)">or</a></span>&#8203;(boolean&nbsp;f)</code></th>
<td class="col-last">
<div class="block">Returns <code>(this || f)</code> LogicalCompution instance.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#or(com.shimizukenta.secs.local.property.BooleanObservable)">or</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns <code>(this || observer)</code> LogicalCompution instance.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntil(boolean)">waitUntil</a></span>&#8203;(boolean&nbsp;condition)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(condtion == this.booleanValue())</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i10">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntil(boolean,long,java.util.concurrent.TimeUnit)">waitUntil</a></span>&#8203;(boolean&nbsp;condition,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(condition == this.booleanValue())</code>.</div>
</td>
</tr>
<tr class="row-color" id="i11">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntil(boolean,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntil</a></span>&#8203;(boolean&nbsp;condition,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(condition == this.booleanValue())</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i12">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilFalse()">waitUntilFalse</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(this.booleanValue() == false)</code>.</div>
</td>
</tr>
<tr class="row-color" id="i13">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilFalse(long,java.util.concurrent.TimeUnit)">waitUntilFalse</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(this.booleanValue() == false)</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i14">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilFalse(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilFalse</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(this.booleanValue() == false)</code>.</div>
</td>
</tr>
<tr class="row-color" id="i15">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilTrue()">waitUntilTrue</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(this.booleanValue() == true)</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i16">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilTrue(long,java.util.concurrent.TimeUnit)">waitUntilTrue</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(this.booleanValue() == true)</code>.</div>
</td>
</tr>
<tr class="row-color" id="i17">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilTrue(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilTrue</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>(this.booleanValue() == true)</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i18">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#xor(boolean)">xor</a></span>&#8203;(boolean&nbsp;f)</code></th>
<td class="col-last">
<div class="block">Returns <code>(this ^ f)</code> LogicalCompution instance.</div>
</td>
</tr>
<tr class="row-color" id="i19">
<td class="col-first"><code>default <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#xor(com.shimizukenta.secs.local.property.BooleanObservable)">xor</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns <code>(this ^ observer)</code> LogicalCompution instance.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="and(com.shimizukenta.secs.local.property.BooleanObservable)">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">and</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns <code>(this &amp;&amp; observer)</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the BooleanObserver</dd>
<dt>戻り値:</dt>
<dd><code>(this &amp;&amp; observer)</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#and(boolean)"><code>and(boolean)</code></a>, 
<a href="LogicalCompution.html#and(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>LogicalCompution.and(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="or(com.shimizukenta.secs.local.property.BooleanObservable)">
<h3>or</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">or</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns <code>(this || observer)</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the BooleanObserver</dd>
<dt>戻り値:</dt>
<dd><code>(this || observer)</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="LogicalCompution.html#or(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>LogicalCompution.or(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="not()">
<h3>not</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">not</span>()</div>
<div class="block">Returns (! this) LogicalCompution instance.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd><code>(! this)</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="LogicalCompution.html#not(com.shimizukenta.secs.local.property.BooleanObservable)"><code>LogicalCompution.not(BooleanObservable)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="xor(com.shimizukenta.secs.local.property.BooleanObservable)">
<h3>xor</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">xor</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns <code>(this ^ observer)</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the BooleanObserver</dd>
<dt>戻り値:</dt>
<dd><code>(this ^ observer)</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#xor(boolean)"><code>xor(boolean)</code></a>, 
<a href="LogicalCompution.html#xor(com.shimizukenta.secs.local.property.BooleanObservable,com.shimizukenta.secs.local.property.BooleanObservable)"><code>LogicalCompution.xor(BooleanObservable, BooleanObservable)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nand(com.shimizukenta.secs.local.property.BooleanObservable)">
<h3>nand</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">nand</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns <code>(! (this &amp;&amp; observer))</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the BooleanObserver</dd>
<dt>戻り値:</dt>
<dd>(! <code>(this &amp;&amp; observer))</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="LogicalCompution.html#nand(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>LogicalCompution.nand(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nor(com.shimizukenta.secs.local.property.BooleanObservable)">
<h3>nor</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">nor</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns <code>(! (this || observer))</code>aaa LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the BooleanObserver</dd>
<dt>戻り値:</dt>
<dd>(! <code>(this || observer))</code> LogicalCompution instance.</dd>
<dt>関連項目:</dt>
<dd><a href="#nor(boolean)"><code>nor(boolean)</code></a>, 
<a href="LogicalCompution.html#nor(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>LogicalCompution.nor(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="and(boolean)">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">and</span>&#8203;(<span class="parameters">boolean&nbsp;f)</span></div>
<div class="block">Returns <code>(this &amp;&amp; f)</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>f</code> - the boolean</dd>
<dt>戻り値:</dt>
<dd><code>(this &amp;&amp; f)</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#and(com.shimizukenta.secs.local.property.BooleanObservable)"><code>and(BooleanObservable)</code></a>, 
<a href="#and(com.shimizukenta.secs.local.property.BooleanObservable)"><code>and(BooleanObservable)</code></a>, 
<a href="LogicalCompution.html#and(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>LogicalCompution.and(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="or(boolean)">
<h3>or</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">or</span>&#8203;(<span class="parameters">boolean&nbsp;f)</span></div>
<div class="block">Returns <code>(this || f)</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>f</code> - the boolean</dd>
<dt>戻り値:</dt>
<dd><code>(this || f)</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#or(com.shimizukenta.secs.local.property.BooleanObservable)"><code>or(BooleanObservable)</code></a>, 
<a href="LogicalCompution.html#or(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>LogicalCompution.or(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="xor(boolean)">
<h3>xor</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">xor</span>&#8203;(<span class="parameters">boolean&nbsp;f)</span></div>
<div class="block">Returns <code>(this ^ f)</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>f</code> - the boolean value</dd>
<dt>戻り値:</dt>
<dd><code>(this ^ f)</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#xor(com.shimizukenta.secs.local.property.BooleanObservable)"><code>xor(BooleanObservable)</code></a>, 
<a href="LogicalCompution.html#xor(com.shimizukenta.secs.local.property.BooleanObservable,com.shimizukenta.secs.local.property.BooleanObservable)"><code>LogicalCompution.xor(BooleanObservable, BooleanObservable)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nand(boolean)">
<h3>nand</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">nand</span>&#8203;(<span class="parameters">boolean&nbsp;f)</span></div>
<div class="block">Returns <code>(! (this &amp;&amp; f))</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>f</code> - the boolean value</dd>
<dt>戻り値:</dt>
<dd><code>(! (this &amp;&amp; f))</code> LogicalCompution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#nand(com.shimizukenta.secs.local.property.BooleanObservable)"><code>nand(BooleanObservable)</code></a>, 
<a href="LogicalCompution.html#nand(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>LogicalCompution.nand(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nor(boolean)">
<h3>nor</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">nor</span>&#8203;(<span class="parameters">boolean&nbsp;f)</span></div>
<div class="block">Returns <code>(! (this || f))</code> LogicalCompution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>f</code> - the boolean value</dd>
<dt>戻り値:</dt>
<dd><code>(! (this || f))</code> LogicalCompution instance.</dd>
<dt>関連項目:</dt>
<dd><a href="#nor(com.shimizukenta.secs.local.property.BooleanObservable)"><code>nor(BooleanObservable)</code></a>, 
<a href="LogicalCompution.html#nor(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>LogicalCompution.nor(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntil(boolean)">
<h3>waitUntil</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">waitUntil</span>&#8203;(<span class="parameters">boolean&nbsp;condition)</span>
        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>(condtion == this.booleanValue())</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>condition</code> - the boolean value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean,long,java.util.concurrent.TimeUnit)"><code>waitUntil(boolean, long, TimeUnit)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntil(boolean,long,java.util.concurrent.TimeUnit)">
<h3>waitUntil</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">waitUntil</span>&#8203;(<span class="parameters">boolean&nbsp;condition,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>(condition == this.booleanValue())</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>condition</code> - the boolean value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean)"><code>waitUntil(boolean)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntil(boolean,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntil</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntil</span>&#8203;(<span class="parameters">boolean&nbsp;condition,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>(condition == this.booleanValue())</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>condition</code> - the boolean value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean,long,java.util.concurrent.TimeUnit)"><code>waitUntil(boolean, long, TimeUnit)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilTrue()">
<h3>waitUntilTrue</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilTrue</span>()
                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>(this.booleanValue() == true)</code>.
 
 <p>
 This is blocking method.<br/>
 If already this.value is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean)"><code>#see #waitUntilFalse()</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilTrue(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilTrue</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilTrue</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>(this.booleanValue() == true)</code>.
 
 <p>
 This is blocking method.<br/>
 If already this.value is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean,long,java.util.concurrent.TimeUnit)"><code>waitUntil(boolean, long, TimeUnit)</code></a>, 
<a href="#waitUntilFalse(long,java.util.concurrent.TimeUnit)"><code>waitUntilFalse(long, TimeUnit)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilTrue(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilTrue</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilTrue</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>(this.booleanValue() == true)</code>.
 
 <p>
 This is blocking method.<br/>
 If already this.value is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean,com.shimizukenta.secs.local.property.TimeoutGettable)"><code>waitUntil(boolean, TimeoutGettable)</code></a>, 
<a href="#waitUntilFalse(com.shimizukenta.secs.local.property.TimeoutGettable)"><code>waitUntilFalse(TimeoutGettable)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilFalse()">
<h3>waitUntilFalse</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilFalse</span>()
                     throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>(this.booleanValue() == false)</code>.
 
 <p>
 This is blocking method.<br/>
 If already this.value is false, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean)"><code>waitUntil(boolean)</code></a>, 
<a href="#waitUntilTrue()"><code>waitUntilTrue()</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilFalse(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilFalse</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilFalse</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                     throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>(this.booleanValue() == false)</code>.
 
 <p>
 This is blocking method.<br/>
 If already this.value is false, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean,long,java.util.concurrent.TimeUnit)"><code>waitUntil(boolean, long, TimeUnit)</code></a>, 
<a href="#waitUntilTrue(long,java.util.concurrent.TimeUnit)"><code>waitUntilTrue(long, TimeUnit)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilFalse(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilFalse</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilFalse</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                     throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>(this.booleanValue() == false)</code>.
 
 <p>
 This is blocking method.<br/>
 If already this.value is false, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><a href="#waitUntil(boolean,com.shimizukenta.secs.local.property.TimeoutGettable)"><code>waitUntil(boolean, TimeoutGettable)</code></a>, 
<a href="#waitUntilTrue(com.shimizukenta.secs.local.property.TimeoutGettable)"><code>waitUntilTrue(TimeoutGettable)</code></a></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
