<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>ObjectProperty</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: ObjectProperty">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース ObjectProperty" class="title">インタフェース ObjectProperty&lt;T&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
</dl>
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;T&gt;</code>, <code><a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectGettable</a>&lt;T&gt;</code>, <code><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;T&gt;</code>, <code><a href="ObjectSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectSettable</a>&lt;T&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;T&gt;</code>, <code><a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース">Property</a>&lt;T&gt;</code>, <code>java.io.Serializable</code>, <code><a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">ObjectProperty&lt;T&gt;</span>
extends <a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース">Property</a>&lt;T&gt;, <a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectGettable</a>&lt;T&gt;, <a href="ObjectSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectSettable</a>&lt;T&gt;, <a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;T&gt;</pre>
<div class="block">Object value Property, includes Getter, Setter, Observer.
 
 <ul>
 <li>To build instance, <a href="#newInstance(T)"><code>newInstance(Object)</code></a></li>
 <li>To get value, <a href="ObjectGettable.html#get()"><code>ObjectGettable.get()</code></a></li>
 <li>To get Optional, <a href="ObjectGettable.html#optional()"><code>ObjectGettable.optional()</code></a></li>
 <li>To set value, <a href="ObjectSettable.html#set(T)"><code>ObjectSettable.set(Object)</code></a></li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>To compute,
 <ul>
 <li><a href="ObjectObservable.html#computeIsEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)"><code>ObjectObservable.computeIsEqualTo(ObjectObservable)</code></a></li>
 <li><a href="ObjectObservable.html#computeIsNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)"><code>ObjectObservable.computeIsNotEqualTo(ObjectObservable)</code></a></li>
 </ul>
 </li>
 <li>To wait until condition is true,
 <ul>
 <li><a href="ObjectObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)"><code>ObjectObservable.waitUntilEqualTo(ObjectObservable)</code></a></li>
 <li><a href="ObjectObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)"><code>ObjectObservable.waitUntilNotEqualTo(ObjectObservable)</code></a></li>
 <li><a href="ObjectObservable.html#waitUntilNotNullAndGet()"><code>ObjectObservable.waitUntilNotNullAndGet()</code></a></li>
 <li><a href="ObjectObservable.html#waitUntilNull()"><code>ObjectObservable.waitUntilNull()</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ObjectGettable</code></a>, 
<a href="ObjectSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ObjectSettable</code></a>, 
<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ObjectObservable</code></a>, 
<a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Property</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<table class="summary-table">
<caption><span>staticメソッド</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>static &lt;T&gt;&nbsp;<a href="ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;T&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(T)">newInstance</a></span>&#8203;(T&nbsp;initial)</code></th>
<td class="col-last">
<div class="block">Instance builder.</div>
</td>
</tr>
</tbody>
</table>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.ObjectGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectGettable</a></h3>
<code><a href="ObjectGettable.html#get()">get</a>, <a href="ObjectGettable.html#isNull()">isNull</a>, <a href="ObjectGettable.html#optional()">optional</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.ObjectObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a></h3>
<code><a href="ObjectObservable.html#computeIsEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">computeIsEqualTo</a>, <a href="ObjectObservable.html#computeIsEqualTo(U)">computeIsEqualTo</a>, <a href="ObjectObservable.html#computeIsNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">computeIsNotEqualTo</a>, <a href="ObjectObservable.html#computeIsNotEqualTo(U)">computeIsNotEqualTo</a>, <a href="ObjectObservable.html#computeIsNotNull()">computeIsNotNull</a>, <a href="ObjectObservable.html#computeIsNull()">computeIsNull</a>, <a href="ObjectObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(U)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(U,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(U,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(U)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(U,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(U,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotNullAndGet()">waitUntilNotNullAndGet</a>, <a href="ObjectObservable.html#waitUntilNotNullAndGet(long,java.util.concurrent.TimeUnit)">waitUntilNotNullAndGet</a>, <a href="ObjectObservable.html#waitUntilNotNullAndGet(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotNullAndGet</a>, <a href="ObjectObservable.html#waitUntilNull()">waitUntilNull</a>, <a href="ObjectObservable.html#waitUntilNull(long,java.util.concurrent.TimeUnit)">waitUntilNull</a>, <a href="ObjectObservable.html#waitUntilNull(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNull</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.ObjectSettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="ObjectSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectSettable</a></h3>
<code><a href="ObjectSettable.html#set(T)">set</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Settable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a></h3>
<code><a href="Settable.html#bind(com.shimizukenta.secs.local.property.Observable)">bind</a>, <a href="Settable.html#unbind(com.shimizukenta.secs.local.property.Observable)">unbind</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="newInstance(T)">
<h3 id="newInstance(java.lang.Object)">newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;T&gt;</span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters">T&nbsp;initial)</span></div>
<div class="block">Instance builder.</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>initial</code> - the <code>&lt;T&gt;</code> value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
