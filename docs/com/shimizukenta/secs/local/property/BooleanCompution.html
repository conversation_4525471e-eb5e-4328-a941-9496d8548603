<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>BooleanCompution</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: BooleanCompution">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース BooleanCompution" class="title">インタフェース BooleanCompution</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a></code>, <code><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></code>, <code><a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;java.lang.Boolean&gt;</code>, <code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;java.lang.Boolean&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.Boolean&gt;</code>, <code>java.io.Serializable</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code>, <code><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">BooleanCompution</span>
extends <a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;java.lang.Boolean&gt;, <a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a>, <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></pre>
<div class="block">Boolean value compution, includes Getter and Observer.
 
 <p>
 <strong>NOT</strong> includes Setter.<br/>
 </p>
 <p>
 This instance is built from other Property or Compution.<br/>
 </p>
 <ul>
 <li>To get value, <a href="BooleanGettable.html#booleanValue()"><code>BooleanGettable.booleanValue()</code></a></li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>To wait until condition is true,
 <ul>
 <li><a href="BooleanObservable.html#waitUntil(boolean)"><code>BooleanObservable.waitUntil(boolean)</code></a></li>
 <li><a href="BooleanObservable.html#waitUntil(boolean,long,java.util.concurrent.TimeUnit)"><code>BooleanObservable.waitUntil(boolean, long, java.util.concurrent.TimeUnit)</code></a></li>
 </ul>
 </lI>
 <li>To logical-compute,
 <ul>
 <li><a href="BooleanObservable.html#and(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.and(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#or(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.or(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#not()"><code>BooleanObservable.not()</code></a></li>
 <li><a href="BooleanObservable.html#xor(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.xor(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#nand(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.nand(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#nor(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.nor(BooleanObservable)</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>Boolean</code>, 
<a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanGettable</code></a>, 
<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanObservable</code></a>, 
<a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Compution</code></a>, 
<a href="BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanProperty</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.BooleanGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a></h3>
<code><a href="BooleanGettable.html#booleanValue()">booleanValue</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.BooleanObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></h3>
<code><a href="BooleanObservable.html#and(boolean)">and</a>, <a href="BooleanObservable.html#and(com.shimizukenta.secs.local.property.BooleanObservable)">and</a>, <a href="BooleanObservable.html#nand(boolean)">nand</a>, <a href="BooleanObservable.html#nand(com.shimizukenta.secs.local.property.BooleanObservable)">nand</a>, <a href="BooleanObservable.html#nor(boolean)">nor</a>, <a href="BooleanObservable.html#nor(com.shimizukenta.secs.local.property.BooleanObservable)">nor</a>, <a href="BooleanObservable.html#not()">not</a>, <a href="BooleanObservable.html#or(boolean)">or</a>, <a href="BooleanObservable.html#or(com.shimizukenta.secs.local.property.BooleanObservable)">or</a>, <a href="BooleanObservable.html#waitUntil(boolean)">waitUntil</a>, <a href="BooleanObservable.html#waitUntil(boolean,long,java.util.concurrent.TimeUnit)">waitUntil</a>, <a href="BooleanObservable.html#waitUntil(boolean,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntil</a>, <a href="BooleanObservable.html#waitUntilFalse()">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilFalse(long,java.util.concurrent.TimeUnit)">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilFalse(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilTrue()">waitUntilTrue</a>, <a href="BooleanObservable.html#waitUntilTrue(long,java.util.concurrent.TimeUnit)">waitUntilTrue</a>, <a href="BooleanObservable.html#waitUntilTrue(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilTrue</a>, <a href="BooleanObservable.html#xor(boolean)">xor</a>, <a href="BooleanObservable.html#xor(com.shimizukenta.secs.local.property.BooleanObservable)">xor</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
