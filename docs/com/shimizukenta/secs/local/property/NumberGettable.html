<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>NumberGettable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: NumberGettable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース NumberGettable" class="title">インタフェース NumberGettable&lt;T extends java.lang.Number&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
</dl>
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;T&gt;</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="DoubleCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleCompution</a></code>, <code><a href="DoubleProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleProperty</a></code>, <code><a href="FloatCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatCompution</a></code>, <code><a href="FloatProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatProperty</a></code>, <code><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code>, <code><a href="IntegerProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerProperty</a></code>, <code><a href="LongCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongCompution</a></code>, <code><a href="LongProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongProperty</a></code>, <code><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code>, <code><a href="NumberProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberProperty</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">NumberGettable&lt;T extends java.lang.Number&gt;</span>
extends <a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;T&gt;</pre>
<div class="block">Number value Getter.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>Number</code>, 
<a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Gettable</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>byte</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#byteValue()">byteValue</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns the value of the specified number as a byte.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>double</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#doubleValue()">doubleValue</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns the value of the specified number as a double.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>float</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#floatValue()">floatValue</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns the value of the specified number as a float.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>int</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#intValue()">intValue</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns the value of the specified number as a int.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>long</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#longValue()">longValue</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns the value of the specified number as a long.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>short</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#shortValue()">shortValue</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns the value of the specified number as a short.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="byteValue()">
<h3>byteValue</h3>
<div class="member-signature"><span class="return-type">byte</span>&nbsp;<span class="member-name">byteValue</span>()</div>
<div class="block">Returns the value of the specified number as a byte.
 
 <p>This implementation returns Number#byteValue().</p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>the numeric value represented by this object after conversion to type byte.</dd>
<dt>関連項目:</dt>
<dd><code>Number.byteValue()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shortValue()">
<h3>shortValue</h3>
<div class="member-signature"><span class="return-type">short</span>&nbsp;<span class="member-name">shortValue</span>()</div>
<div class="block">Returns the value of the specified number as a short.
 
 <p>This implementation returns Number#shortValue().</p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>the numeric value represented by this object after conversion to type short.</dd>
<dt>関連項目:</dt>
<dd><code>Number.shortValue()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="intValue()">
<h3>intValue</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="member-name">intValue</span>()</div>
<div class="block">Returns the value of the specified number as a int.
 
 <p>This implementation returns Number#intValue().</p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>the numeric value represented by this object after conversion to type int.</dd>
<dt>関連項目:</dt>
<dd><code>Number.intValue()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="longValue()">
<h3>longValue</h3>
<div class="member-signature"><span class="return-type">long</span>&nbsp;<span class="member-name">longValue</span>()</div>
<div class="block">Returns the value of the specified number as a long.
 
 <p>This implementation returns Number#longValue().</p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>the numeric value represented by this object after conversion to type long.</dd>
<dt>関連項目:</dt>
<dd><code>Number.longValue()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="floatValue()">
<h3>floatValue</h3>
<div class="member-signature"><span class="return-type">float</span>&nbsp;<span class="member-name">floatValue</span>()</div>
<div class="block">Returns the value of the specified number as a float.
 
 <p>This implementation returns Number#floatValue().</p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>the numeric value represented by this object after conversion to type float.</dd>
<dt>関連項目:</dt>
<dd><code>Number.floatValue()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="doubleValue()">
<h3>doubleValue</h3>
<div class="member-signature"><span class="return-type">double</span>&nbsp;<span class="member-name">doubleValue</span>()</div>
<div class="block">Returns the value of the specified number as a double.
 
 <p>This implementation returns Number#doubleValue().</p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>the numeric value represented by this object after conversion to type double.</dd>
<dt>関連項目:</dt>
<dd><code>Number.doubleValue()</code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
