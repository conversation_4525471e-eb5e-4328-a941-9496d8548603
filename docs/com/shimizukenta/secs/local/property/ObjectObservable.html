<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>ObjectObservable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: ObjectObservable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":18,"i1":18,"i2":18,"i3":18,"i4":18,"i5":18,"i6":18,"i7":18,"i8":18,"i9":18,"i10":18,"i11":18,"i12":18,"i13":18,"i14":18,"i15":18,"i16":18,"i17":18,"i18":18,"i19":18,"i20":18,"i21":18,"i22":18,"i23":18};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],16:["t5","\u30C7\u30D5\u30A9\u30EB\u30C8\u30FB\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース ObjectObservable" class="title">インタフェース ObjectObservable&lt;T&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
</dl>
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;T&gt;</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="ObjectCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectCompution</a>&lt;T&gt;</code>, <code><a href="ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">ObjectObservable&lt;T&gt;</span>
extends <a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;T&gt;</pre>
<div class="block">Object value Observer.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Observable</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t5" class="table-tab" onclick="show(16);">デフォルト・メソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>default &lt;U&gt;&nbsp;<a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">computeIsEqualTo</a></span>&#8203;(<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution instance of isEqualTo.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>default &lt;U&gt;&nbsp;<a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(U)">computeIsEqualTo</a></span>&#8203;(U&nbsp;ref)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution instance of isEqualTo.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>default &lt;U&gt;&nbsp;<a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">computeIsNotEqualTo</a></span>&#8203;(<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution instance of isNotEqualTo.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>default &lt;U&gt;&nbsp;<a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(U)">computeIsNotEqualTo</a></span>&#8203;(U&nbsp;ref)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution instance of isNotEqualTo.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotNull()">computeIsNotNull</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution instance of isNotEqualTo null.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNull()">computeIsNull</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution instance of isEqualTo null.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">waitUntilEqualTo</a></span>&#8203;(<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(U)">waitUntilEqualTo</a></span>&#8203;(U&nbsp;ref)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal.</div>
</td>
</tr>
<tr class="alt-color" id="i10">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(U,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(U&nbsp;ref,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal.</div>
</td>
</tr>
<tr class="row-color" id="i11">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(U,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(U&nbsp;ref,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is equal.</div>
</td>
</tr>
<tr class="alt-color" id="i12">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">waitUntilNotEqualTo</a></span>&#8203;(<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal.</div>
</td>
</tr>
<tr class="row-color" id="i13">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal.</div>
</td>
</tr>
<tr class="alt-color" id="i14">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal.</div>
</td>
</tr>
<tr class="row-color" id="i15">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(U)">waitUntilNotEqualTo</a></span>&#8203;(U&nbsp;ref)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal.</div>
</td>
</tr>
<tr class="alt-color" id="i16">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(U,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(U&nbsp;ref,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal.</div>
</td>
</tr>
<tr class="row-color" id="i17">
<td class="col-first"><code>default &lt;U&gt;&nbsp;void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(U,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(U&nbsp;ref,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT equal.</div>
</td>
</tr>
<tr class="alt-color" id="i18">
<td class="col-first"><code>default <a href="ObjectObservable.html" title="ObjectObservable内の型パラメータ">T</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotNullAndGet()">waitUntilNotNullAndGet</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT null, and return value.</div>
</td>
</tr>
<tr class="row-color" id="i19">
<td class="col-first"><code>default <a href="ObjectObservable.html" title="ObjectObservable内の型パラメータ">T</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotNullAndGet(long,java.util.concurrent.TimeUnit)">waitUntilNotNullAndGet</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT null, and return value.</div>
</td>
</tr>
<tr class="alt-color" id="i20">
<td class="col-first"><code>default <a href="ObjectObservable.html" title="ObjectObservable内の型パラメータ">T</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotNullAndGet(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotNullAndGet</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is NOT null, and return value.</div>
</td>
</tr>
<tr class="row-color" id="i21">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNull()">waitUntilNull</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until value is null.</div>
</td>
</tr>
<tr class="alt-color" id="i22">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNull(long,java.util.concurrent.TimeUnit)">waitUntilNull</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until value is null.</div>
</td>
</tr>
<tr class="row-color" id="i23">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNull(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNull</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until value is null.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="computeIsEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">
<h3>computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer)</span></div>
<div class="block">Returns ComparativeCompution instance of isEqualTo.</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>observer</code> - the ObjectObserver</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution instance of isEqualTo</dd>
<dt>関連項目:</dt>
<dd><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ComparativeCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualTo(U)">
<h3 id="computeIsEqualTo(java.lang.Object)">computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters">U&nbsp;ref)</span></div>
<div class="block">Returns ComparativeCompution instance of isEqualTo.</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>ref</code> - the Object value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution instance of isEqualTo</dd>
<dt>関連項目:</dt>
<dd><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ComparativeCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">
<h3>computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer)</span></div>
<div class="block">Returns ComparativeCompution instance of isNotEqualTo.</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>observer</code> - the ObjectObserver</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution instance of isNotEqualTo</dd>
<dt>関連項目:</dt>
<dd><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ComparativeCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(U)">
<h3 id="computeIsNotEqualTo(java.lang.Object)">computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters">U&nbsp;ref)</span></div>
<div class="block">Returns ComparativeCompution instance of isNotEqualTo.</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>ref</code> - the Object value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution instance of isNotEqualTo</dd>
<dt>関連項目:</dt>
<dd><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ComparativeCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is equal.
 
 <p>
 This is blocking method.<br/>
 If already value is equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>observer</code> - the ObjectObserver</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equal.
 
 <p>
 This is blocking method.<br/>
 If already value is equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>observer</code> - the ObjectObserver</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equal.
 
 <p>
 This is blocking method.<br/>
 If already value is equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>observer</code> - the ObjectObserver</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(U)">
<h3 id="waitUntilEqualTo(java.lang.Object)">waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">U&nbsp;ref)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is equal.
 
 <p>
 This is blocking method.<br/>
 If already value is equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>ref</code> - the Object value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(U,long,java.util.concurrent.TimeUnit)">
<h3 id="waitUntilEqualTo(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">U&nbsp;ref,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equal.
 
 <p>
 This is blocking method.<br/>
 If already value is equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>ref</code> - the Object value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(U,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3 id="waitUntilEqualTo(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">U&nbsp;ref,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is equal.
 
 <p>
 This is blocking method.<br/>
 If already value is equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>ref</code> - the Object value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer)</span>
                              throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT equal.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>observer</code> - the ObjectObserver</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equal.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>observer</code> - the Object value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;U&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equal.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>observer</code> - the ObjectObserver</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(U)">
<h3 id="waitUntilNotEqualTo(java.lang.Object)">waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">U&nbsp;ref)</span>
                              throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT equal.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>ref</code> - the Object value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(U,long,java.util.concurrent.TimeUnit)">
<h3 id="waitUntilNotEqualTo(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">U&nbsp;ref,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equal.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>ref</code> - the Object value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(U,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3 id="waitUntilNotEqualTo(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;U&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">U&nbsp;ref,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT equal.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> equal, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>U</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>ref</code> - the Object value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNull()">
<h3>computeIsNull</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsNull</span>()</div>
<div class="block">Returns BooleanCompution instance of isEqualTo null.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>BooleanCompution instance of isEqualTo null</dd>
<dt>関連項目:</dt>
<dd><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotNull()">
<h3>computeIsNotNull</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsNotNull</span>()</div>
<div class="block">Returns BooleanCompution instance of isNotEqualTo null.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>BooleanCompution instance of isNotEqualTo null</dd>
<dt>関連項目:</dt>
<dd><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotNullAndGet()">
<h3>waitUntilNotNullAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ObjectObservable.html" title="ObjectObservable内の型パラメータ">T</a></span>&nbsp;<span class="member-name">waitUntilNotNullAndGet</span>()
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is NOT null, and return value.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> null, return value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotNullAndGet(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotNullAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ObjectObservable.html" title="ObjectObservable内の型パラメータ">T</a></span>&nbsp;<span class="member-name">waitUntilNotNullAndGet</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT null, and return value.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> null, return value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotNullAndGet(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotNullAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ObjectObservable.html" title="ObjectObservable内の型パラメータ">T</a></span>&nbsp;<span class="member-name">waitUntilNotNullAndGet</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is NOT null, and return value.
 
 <p>
 This is blocking method.<br/>
 If already value is <strong>NOT</strong> null, return value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNull()">
<h3>waitUntilNull</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNull</span>()
                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until value is null.
 
 <p>
 This is blocking method.<br/>
 If already value is null, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNull(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNull</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNull</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is null.
 
 <p>
 This is blocking method.<br/>
 If already value is null, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNull(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNull</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNull</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until value is null.
 
 <p>
 This is blocking method.<br/>
 If already value is null, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
