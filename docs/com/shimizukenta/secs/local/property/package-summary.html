<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>com.shimizukenta.secs.local.property</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li class="nav-bar-cell1-rev">パッケージ</li>
<li>クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="パッケージ" class="title">パッケージ&nbsp;com.shimizukenta.secs.local.property</h1>
</div>
<section class="package-description" id="package.description">
<div class="block">Provides 'Property' intarfaces. this is similar to JavaFX 'javafx.beans.property'.
 
 <p>
 Includes Setter/Getter/Observer, Number/Comparative/Logical compution and blocking methods to wait until condition is true.<br/>
 </p>
 <ul>
 <li>Property
 <ul>
 <li><a href="BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanProperty</code></a></li>
 <li><a href="IntegerProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>IntegerProperty</code></a></li>
 <li><a href="LongProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>LongProperty</code></a></li>
 <li><a href="FloatProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>FloatProperty</code></a></li>
 <li><a href="DoubleProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>DoubleProperty</code></a></li>
 <li><a href="ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ObjectProperty</code></a></li>
 <li><a href="StringProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>StringProperty</code></a></li>
 <li><a href="ListProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ListProperty</code></a></li>
 <li><a href="SetProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>SetProperty</code></a></li>
 <li><a href="MapProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>MapProperty</code></a></li>
 <li><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>TimeoutProperty</code></a></li>
 </ul>
 </li>
 <li>Compution
 <ul>
 <li><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>NumberCompution</code></a></li>
 <li><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ComparativeCompution</code></a></li>
 <li><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>LogicalCompution</code></a></li>
 </ul>
 </li>
 </ul>
 <ul>
 <li>To build new instance, #newInstance</li>
 <li>To set value, #set</li>
 <li>To get value, #get</li>
 <li>To detect value changed, #addChangeListener</li>
 <li>To compute value, #compute... methods</li>
 <li>To block until condition is true, #waitUntil... methods</li>
 </ul></div>
</section>
<section class="summary">
<ul class="summary-list">
<li>
<div class="type-summary">
<table class="summary-table">
<caption><span>インタフェースの概要</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">インタフェース</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></th>
<td class="col-last">
<div class="block">Boolean value compution, includes Getter and Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a></th>
<td class="col-last">
<div class="block">Boolean value Getter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></th>
<td class="col-last">
<div class="block">Boolean value Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></th>
<td class="col-last">
<div class="block">Boolean value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="BooleanSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanSettable</a></th>
<td class="col-last">
<div class="block">Boolean value Setter.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="ChangeListener.html" title="com.shimizukenta.secs.local.property内のインタフェース">ChangeListener</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Change Listener, used in Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="CollectionCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionCompution</a>&lt;E,&#8203;T extends java.util.Collection&lt;E&gt;&gt;</th>
<td class="col-last">
<div class="block">Collection value Compution, includes Collection-methods, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="CollectionObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionObservable</a>&lt;E,&#8203;T extends java.util.Collection&lt;E&gt;&gt;</th>
<td class="col-last">
<div class="block">Collection value Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="CollectionProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionProperty</a>&lt;E,&#8203;T extends java.util.Collection&lt;E&gt;&gt;</th>
<td class="col-last">
<div class="block">Collection value Property, includes Collection-methods, Setter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="CollectionSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionSettable</a>&lt;E,&#8203;T extends java.util.Collection&lt;E&gt;&gt;</th>
<td class="col-last">
<div class="block">Collection value Setter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></th>
<td class="col-last">
<div class="block">Comparative Boolean Compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Super Compution interface, includes Getter and Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="DoubleCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleCompution</a></th>
<td class="col-last">
<div class="block">Double value Compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="DoubleProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleProperty</a></th>
<td class="col-last">
<div class="block">Double value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="DoubleSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleSettable</a></th>
<td class="col-last">
<div class="block">Double value Setter.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="FloatCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatCompution</a></th>
<td class="col-last">
<div class="block">Float value Compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="FloatProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatProperty</a></th>
<td class="col-last">
<div class="block">Float value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="FloatSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatSettable</a></th>
<td class="col-last">
<div class="block">Float value Setter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Super Getter interface.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></th>
<td class="col-last">
<div class="block">Integer value Compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="IntegerProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerProperty</a></th>
<td class="col-last">
<div class="block">Integer value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="IntegerSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerSettable</a></th>
<td class="col-last">
<div class="block">Integer value Setter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="ListCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ListCompution</a>&lt;E&gt;</th>
<td class="col-last">
<div class="block">List value Compution, includes List-methods, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="ListObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ListObservable</a>&lt;E&gt;</th>
<td class="col-last">
<div class="block">List value Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="ListProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ListProperty</a>&lt;E&gt;</th>
<td class="col-last">
<div class="block">List value Property, include List-methods, Setter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="ListSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ListSettable</a>&lt;E&gt;</th>
<td class="col-last">
<div class="block">List value Setter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></th>
<td class="col-last">
<div class="block">Logical Boolean compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="LongCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongCompution</a></th>
<td class="col-last">
<div class="block">Long value Compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="LongProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongProperty</a></th>
<td class="col-last">
<div class="block">Long value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="LongSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongSettable</a></th>
<td class="col-last">
<div class="block">Long value Setter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="MapObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapObservable</a>&lt;K,&#8203;V&gt;</th>
<td class="col-last">
<div class="block">Map value Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="MapProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapProperty</a>&lt;K,&#8203;V&gt;</th>
<td class="col-last">
<div class="block">Map value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="MapSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapSettable</a>&lt;K,&#8203;V&gt;</th>
<td class="col-last">
<div class="block">Map value Setter.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></th>
<td class="col-last">
<div class="block">Number value Compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="NumberGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberGettable</a>&lt;T extends java.lang.Number&gt;</th>
<td class="col-last">
<div class="block">Number value Getter.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;T extends java.lang.Number&gt;</th>
<td class="col-last">
<div class="block">Number value Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="NumberProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberProperty</a>&lt;T extends java.lang.Number&gt;</th>
<td class="col-last">
<div class="block">Number value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="NumberSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberSettable</a>&lt;T extends java.lang.Number&gt;</th>
<td class="col-last">
<div class="block">Number value Setter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="ObjectCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectCompution</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Object value Compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectGettable</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Object value Getter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Object value Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Object value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="ObjectSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectSettable</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Object value Setter.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Super Observer interface.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース">Property</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Super Property interface, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="SetCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">SetCompution</a>&lt;E&gt;</th>
<td class="col-last">
<div class="block">Set value Compution, includes Set-methods, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="SetObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">SetObservable</a>&lt;E&gt;</th>
<td class="col-last">
<div class="block">Set value Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="SetProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">SetProperty</a>&lt;E&gt;</th>
<td class="col-last">
<div class="block">Set value Property, include Set-methods, Setter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="SetSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">SetSettable</a>&lt;E&gt;</th>
<td class="col-last">
<div class="block">Set value Setter.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a>&lt;T&gt;</th>
<td class="col-last">
<div class="block">Super Setter interface.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></th>
<td class="col-last">
<div class="block">String value compution, includes Getter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="StringGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringGettable</a></th>
<td class="col-last">
<div class="block">String value getter.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="StringObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringObservable</a></th>
<td class="col-last">
<div class="block">String value Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="StringProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringProperty</a></th>
<td class="col-last">
<div class="block">String value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="StringSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringSettable</a></th>
<td class="col-last">
<div class="block">String value setter.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></th>
<td class="col-last">
<div class="block">This interface instance is a pair of timeout and TimeUnit.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a></th>
<td class="col-last">
<div class="block">TimeoutAndUnit value Getter.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="TimeoutObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutObservable</a></th>
<td class="col-last">
<div class="block">TimeoutAndUnit value Observer.</div>
</td>
</tr>
<tr class="alt-color">
<th class="col-first" scope="row"><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></th>
<td class="col-last">
<div class="block">TimeoutAndUnit value Property, includes Getter, Setter, Observer.</div>
</td>
</tr>
<tr class="row-color">
<th class="col-first" scope="row"><a href="TimeoutSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutSettable</a></th>
<td class="col-last">
<div class="block">TimeoutAndUnit value Setter.</div>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li class="nav-bar-cell1-rev">パッケージ</li>
<li>クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
