<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>MapObservable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: MapObservable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":18,"i1":18,"i2":18,"i3":18,"i4":18,"i5":18,"i6":18,"i7":18,"i8":18,"i9":18,"i10":18,"i11":18,"i12":18,"i13":18,"i14":18,"i15":18,"i16":18,"i17":18};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],16:["t5","\u30C7\u30D5\u30A9\u30EB\u30C8\u30FB\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース MapObservable" class="title">インタフェース MapObservable&lt;K,&#8203;V&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>K</code> - Key Type</dd>
<dd><code>V</code> - Value Type</dd>
</dl>
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.util.Map&lt;K,&#8203;V&gt;&gt;</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="MapProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">MapProperty</a>&lt;K,&#8203;V&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">MapObservable&lt;K,&#8203;V&gt;</span>
extends <a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.util.Map&lt;K,&#8203;V&gt;&gt;</pre>
<div class="block">Map value Observer.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Observable</code></a>, 
<code>Map</code></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t5" class="table-tab" onclick="show(16);">デフォルト・メソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeContainsKey(java.lang.Object)">computeContainsKey</a></span>&#8203;(java.lang.Object&nbsp;key)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of Map#containsKey(Object) is true.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEmpty()">computeIsEmpty</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of Map#isEmpty() is true.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEmpty()">computeIsNotEmpty</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of Map#isEmpty() is false.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>default <a href="SetCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">SetCompution</a>&lt;<a href="MapObservable.html" title="MapObservable内の型パラメータ">K</a>&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeKeySet()">computeKeySet</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SetCompution of Map#keySet().</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>default <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeNotContainsKey(java.lang.Object)">computeNotContainsKey</a></span>&#8203;(java.lang.Object&nbsp;key)</code></th>
<td class="col-last">
<div class="block">Returns BooleanCompution of Map#containsKey(Object) is false</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeSize()">computeSize</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution of Map#size().</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>default <a href="MapObservable.html" title="MapObservable内の型パラメータ">V</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContainsKeyAndGet(java.lang.Object)">waitUntilContainsKeyAndGet</a></span>&#8203;(java.lang.Object&nbsp;key)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#containsKey(Object) is true, and return value.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>default <a href="MapObservable.html" title="MapObservable内の型パラメータ">V</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContainsKeyAndGet(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilContainsKeyAndGet</a></span>&#8203;(java.lang.Object&nbsp;key,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#containsKey(Object) is true, and return value.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>default <a href="MapObservable.html" title="MapObservable内の型パラメータ">V</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilContainsKeyAndGet(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContainsKeyAndGet</a></span>&#8203;(java.lang.Object&nbsp;key,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#containsKey(Object) is true, and return value.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsEmpty()">waitUntilIsEmpty</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until Map#isEmpty() is true.</div>
</td>
</tr>
<tr class="alt-color" id="i10">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsEmpty</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#isEmpty() is true.</div>
</td>
</tr>
<tr class="row-color" id="i11">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsEmpty</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#isEmpty() is true.</div>
</td>
</tr>
<tr class="alt-color" id="i12">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsNotEmpty()">waitUntilIsNotEmpty</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until Map#isEmpty() is false.</div>
</td>
</tr>
<tr class="row-color" id="i13">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsNotEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsNotEmpty</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#isEmpty() is false.</div>
</td>
</tr>
<tr class="alt-color" id="i14">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilIsNotEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsNotEmpty</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#isEmpty() is false.</div>
</td>
</tr>
<tr class="row-color" id="i15">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotContainsKey(java.lang.Object)">waitUntilNotContainsKey</a></span>&#8203;(java.lang.Object&nbsp;key)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#containsKey(Object) is false.</div>
</td>
</tr>
<tr class="alt-color" id="i16">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotContainsKey(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilNotContainsKey</a></span>&#8203;(java.lang.Object&nbsp;key,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#containsKey(Object) is false.</div>
</td>
</tr>
<tr class="row-color" id="i17">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotContainsKey(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotContainsKey</a></span>&#8203;(java.lang.Object&nbsp;key,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until Map#containsKey(Object) is false.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="computeContainsKey(java.lang.Object)">
<h3>computeContainsKey</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeContainsKey</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;key)</span></div>
<div class="block">Returns BooleanCompution of Map#containsKey(Object) is true.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>key</code> - key whose presence in this map is to be tested</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of Map#containsKey(Object) is true</dd>
<dt>関連項目:</dt>
<dd><code>Map.containsKey(Object)</code>, 
<a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeNotContainsKey(java.lang.Object)">
<h3>computeNotContainsKey</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeNotContainsKey</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;key)</span></div>
<div class="block">Returns BooleanCompution of Map#containsKey(Object) is false</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>key</code> - key whose presence in this map is to be tested</dd>
<dt>戻り値:</dt>
<dd>BooleanCompution of Map#containsKey(Object) is false</dd>
<dt>関連項目:</dt>
<dd><code>Map.containsKey(Object)</code>, 
<a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEmpty()">
<h3>computeIsEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsEmpty</span>()</div>
<div class="block">Returns BooleanCompution of Map#isEmpty() is true.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>BooleanCompution of Map#isEmpty() is true</dd>
<dt>関連項目:</dt>
<dd><code>Map.isEmpty()</code>, 
<a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEmpty()">
<h3>computeIsNotEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></span>&nbsp;<span class="member-name">computeIsNotEmpty</span>()</div>
<div class="block">Returns BooleanCompution of Map#isEmpty() is false.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>BooleanCompution of Map#isEmpty() is false</dd>
<dt>関連項目:</dt>
<dd><code>Map.isEmpty()</code>, 
<a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeKeySet()">
<h3>computeKeySet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="SetCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">SetCompution</a>&lt;<a href="MapObservable.html" title="MapObservable内の型パラメータ">K</a>&gt;</span>&nbsp;<span class="member-name">computeKeySet</span>()</div>
<div class="block">Returns SetCompution of Map#keySet().</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>SetCompution of Map#keySet()</dd>
<dt>関連項目:</dt>
<dd><code>Map.keySet()</code>, 
<a href="SetCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>SetCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeSize()">
<h3>computeSize</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">computeSize</span>()</div>
<div class="block">Returns IntegerCompution of Map#size().</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>IntegerCompution of Map#size()</dd>
<dt>関連項目:</dt>
<dd><code>Map.size()</code>, 
<a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>IntegerCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContainsKeyAndGet(java.lang.Object)">
<h3>waitUntilContainsKeyAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="MapObservable.html" title="MapObservable内の型パラメータ">V</a></span>&nbsp;<span class="member-name">waitUntilContainsKeyAndGet</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;key)</span>
                              throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until Map#containsKey(Object) is true, and return value.
 
 <p>
 This is blocking method.<br/>
 If already Map#containsKey(Object) is true, return value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>key</code> - key whose presence in this map is to be tested</dd>
<dt>戻り値:</dt>
<dd>Map#get(Object)</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>Map.containsKey(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContainsKeyAndGet(java.lang.Object,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilContainsKeyAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="MapObservable.html" title="MapObservable内の型パラメータ">V</a></span>&nbsp;<span class="member-name">waitUntilContainsKeyAndGet</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;key,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until Map#containsKey(Object) is true, and return value.
 
 <p>
 This is blocking method.<br/>
 If already Map#containsKey(Object) is true, return value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>key</code> - key whose presence in this map is to be tested</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>戻り値:</dt>
<dd>Map#get(Object)</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>Map.containsKey(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilContainsKeyAndGet(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilContainsKeyAndGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="MapObservable.html" title="MapObservable内の型パラメータ">V</a></span>&nbsp;<span class="member-name">waitUntilContainsKeyAndGet</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;key,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until Map#containsKey(Object) is true, and return value.
 
 <p>
 This is blocking method.<br/>
 If already Map#containsKey(Object) is true, return value immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>key</code> - key whose presence in this map is to be tested</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>戻り値:</dt>
<dd>Map#get(Object)</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>Map.containsKey(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotContainsKey(java.lang.Object)">
<h3>waitUntilNotContainsKey</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotContainsKey</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;key)</span>
                              throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until Map#containsKey(Object) is false.
 
 <p>
 This is blocking method.<br/>
 If already Map#containsKey(Object) is false, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>key</code> - key whose presence in this map is to be tested</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>Map.containsKey(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotContainsKey(java.lang.Object,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotContainsKey</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotContainsKey</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;key,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until Map#containsKey(Object) is false.
 
 <p>
 This is blocking method.<br/>
 If already Map#containsKey(Object) is false, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>key</code> - key whose presence in this map is to be tested</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>Map.containsKey(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotContainsKey(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotContainsKey</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotContainsKey</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;key,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until Map#containsKey(Object) is false.
 
 <p>
 This is blocking method.<br/>
 If already Map#containsKey(Object) is false, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>key</code> - key whose presence in this map is to be tested</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>Map.containsKey(Object)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsEmpty()">
<h3>waitUntilIsEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsEmpty</span>()
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until Map#isEmpty() is true.
 
 <p>
 This is blocking method.<br/>
 If already empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>Map.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsEmpty(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilIsEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsEmpty</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until Map#isEmpty() is true.
 
 <p>
 This is blocking method.<br/>
 If already empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>Map.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilIsEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsEmpty</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until Map#isEmpty() is true.
 
 <p>
 This is blocking method.<br/>
 If already empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>Map.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsNotEmpty()">
<h3>waitUntilIsNotEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsNotEmpty</span>()
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until Map#isEmpty() is false.
 
 <p>
 This is blocking method.<br/>
 If already <strong>NOT</strong> empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>Map.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsNotEmpty(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilIsNotEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsNotEmpty</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until Map#isEmpty() is false.
 
 <p>
 This is blocking method.<br/>
 If already <strong>NOT</strong> empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>Map.isEmpty()</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilIsNotEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilIsNotEmpty</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilIsNotEmpty</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until Map#isEmpty() is false.
 
 <p>
 This is blocking method.<br/>
 If already <strong>NOT</strong> empty, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dt>関連項目:</dt>
<dd><code>Map.isEmpty()</code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
