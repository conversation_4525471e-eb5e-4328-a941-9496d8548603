<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>ListObservable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: ListObservable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース ListObservable" class="title">インタフェース ListObservable&lt;E&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>E</code> - Element</dd>
</dl>
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="CollectionObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionObservable</a>&lt;E,&#8203;java.util.List&lt;E&gt;&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.util.List&lt;E&gt;&gt;</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="ListCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ListCompution</a>&lt;E&gt;</code>, <code><a href="ListProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ListProperty</a>&lt;E&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">ListObservable&lt;E&gt;</span>
extends <a href="CollectionObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionObservable</a>&lt;E,&#8203;java.util.List&lt;E&gt;&gt;</pre>
<div class="block">List value Observer.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>List</code>, 
<a href="CollectionObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>CollectionObservable</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.CollectionObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="CollectionObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionObservable</a></h3>
<code><a href="CollectionObservable.html#computeContains(java.lang.Object)">computeContains</a>, <a href="CollectionObservable.html#computeContainsAll(java.util.Collection)">computeContainsAll</a>, <a href="CollectionObservable.html#computeIsEmpty()">computeIsEmpty</a>, <a href="CollectionObservable.html#computeIsNotEmpty()">computeIsNotEmpty</a>, <a href="CollectionObservable.html#computeNotContains(java.lang.Object)">computeNotContains</a>, <a href="CollectionObservable.html#computeNotContainsAll(java.util.Collection)">computeNotContainsAll</a>, <a href="CollectionObservable.html#computeSize()">computeSize</a>, <a href="CollectionObservable.html#waitUntilContains(java.lang.Object)">waitUntilContains</a>, <a href="CollectionObservable.html#waitUntilContains(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilContains</a>, <a href="CollectionObservable.html#waitUntilContains(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContains</a>, <a href="CollectionObservable.html#waitUntilContainsAll(java.util.Collection)">waitUntilContainsAll</a>, <a href="CollectionObservable.html#waitUntilContainsAll(java.util.Collection,long,java.util.concurrent.TimeUnit)">waitUntilContainsAll</a>, <a href="CollectionObservable.html#waitUntilContainsAll(java.util.Collection,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContainsAll</a>, <a href="CollectionObservable.html#waitUntilIsEmpty()">waitUntilIsEmpty</a>, <a href="CollectionObservable.html#waitUntilIsEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsEmpty</a>, <a href="CollectionObservable.html#waitUntilIsEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsEmpty</a>, <a href="CollectionObservable.html#waitUntilIsNotEmpty()">waitUntilIsNotEmpty</a>, <a href="CollectionObservable.html#waitUntilIsNotEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsNotEmpty</a>, <a href="CollectionObservable.html#waitUntilIsNotEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsNotEmpty</a>, <a href="CollectionObservable.html#waitUntilNotContains(java.lang.Object)">waitUntilNotContains</a>, <a href="CollectionObservable.html#waitUntilNotContains(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilNotContains</a>, <a href="CollectionObservable.html#waitUntilNotContains(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotContains</a>, <a href="CollectionObservable.html#waitUntilNotContainsAll(java.util.Collection)">waitUntilNotContainsAll</a>, <a href="CollectionObservable.html#waitUntilNotContainsAll(java.util.Collection,long,java.util.concurrent.TimeUnit)">waitUntilNotContainsAll</a>, <a href="CollectionObservable.html#waitUntilNotContainsAll(java.util.Collection,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotContainsAll</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
