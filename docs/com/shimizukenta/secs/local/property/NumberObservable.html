<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>NumberObservable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: NumberObservable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":18,"i1":18,"i2":18,"i3":18,"i4":18,"i5":18,"i6":18,"i7":18,"i8":18,"i9":18,"i10":18,"i11":18,"i12":18,"i13":18,"i14":18,"i15":18,"i16":18,"i17":18,"i18":18,"i19":18,"i20":18,"i21":18,"i22":18,"i23":18,"i24":18,"i25":18,"i26":18,"i27":18,"i28":18,"i29":18,"i30":18,"i31":18,"i32":18,"i33":18,"i34":18,"i35":18,"i36":18,"i37":18,"i38":18,"i39":18,"i40":18,"i41":18,"i42":18,"i43":18,"i44":18,"i45":18,"i46":18,"i47":18,"i48":18,"i49":18,"i50":18,"i51":18,"i52":18,"i53":18,"i54":18,"i55":18,"i56":18,"i57":18,"i58":18,"i59":18,"i60":18,"i61":18,"i62":18,"i63":18,"i64":18,"i65":18,"i66":18,"i67":18,"i68":18,"i69":18,"i70":18,"i71":18,"i72":18,"i73":18,"i74":18,"i75":18,"i76":18,"i77":18,"i78":18,"i79":18,"i80":18,"i81":18,"i82":18,"i83":18,"i84":18,"i85":18,"i86":18,"i87":18,"i88":18,"i89":18,"i90":18,"i91":18,"i92":18,"i93":18,"i94":18,"i95":18,"i96":18,"i97":18,"i98":18,"i99":18,"i100":18,"i101":18,"i102":18,"i103":18,"i104":18,"i105":18,"i106":18,"i107":18,"i108":18,"i109":18,"i110":18,"i111":18,"i112":18,"i113":18,"i114":18,"i115":18,"i116":18,"i117":18,"i118":18,"i119":18,"i120":18,"i121":18,"i122":18,"i123":18,"i124":18,"i125":18,"i126":18,"i127":18,"i128":18,"i129":18,"i130":18,"i131":18,"i132":18,"i133":18,"i134":18,"i135":18,"i136":18,"i137":18,"i138":18,"i139":18,"i140":18,"i141":18,"i142":18,"i143":18,"i144":18,"i145":18,"i146":18,"i147":18,"i148":18,"i149":18,"i150":18,"i151":18,"i152":18,"i153":18,"i154":18,"i155":18,"i156":18,"i157":18,"i158":18,"i159":18,"i160":18,"i161":18,"i162":18,"i163":18,"i164":18,"i165":18,"i166":18,"i167":18};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],16:["t5","\u30C7\u30D5\u30A9\u30EB\u30C8\u30FB\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース NumberObservable" class="title">インタフェース NumberObservable&lt;T extends java.lang.Number&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;T&gt;</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="DoubleCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleCompution</a></code>, <code><a href="DoubleProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleProperty</a></code>, <code><a href="FloatCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatCompution</a></code>, <code><a href="FloatProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatProperty</a></code>, <code><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code>, <code><a href="IntegerProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerProperty</a></code>, <code><a href="LongCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongCompution</a></code>, <code><a href="LongProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongProperty</a></code>, <code><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code>, <code><a href="NumberProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberProperty</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">NumberObservable&lt;T extends java.lang.Number&gt;</span>
extends <a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;T&gt;</pre>
<div class="block">Number value Observer.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>Number</code>, 
<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Observable</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t5" class="table-tab" onclick="show(16);">デフォルト・メソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#add(double)">add</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value + value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#add(float)">add</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value + value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#add(int)">add</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value + value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#add(long)">add</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value + value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#add(com.shimizukenta.secs.local.property.NumberObservable)">add</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value + observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(double)">computeIsEqualTo</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(float)">computeIsEqualTo</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(int)">computeIsEqualTo</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(long)">computeIsEqualTo</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">computeIsEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value == observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i10">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsEqualToZero()">computeIsEqualToZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value == 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i11">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThan(double)">computeIsGreaterThan</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i12">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThan(float)">computeIsGreaterThan</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i13">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThan(int)">computeIsGreaterThan</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i14">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThan(long)">computeIsGreaterThan</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i15">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThan(com.shimizukenta.secs.local.property.NumberObservable)">computeIsGreaterThan</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt; observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i16">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanOrEqualTo(double)">computeIsGreaterThanOrEqualTo</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i17">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanOrEqualTo(float)">computeIsGreaterThanOrEqualTo</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i18">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanOrEqualTo(int)">computeIsGreaterThanOrEqualTo</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i19">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanOrEqualTo(long)">computeIsGreaterThanOrEqualTo</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i20">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">computeIsGreaterThanOrEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i21">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanOrEqualToZero()">computeIsGreaterThanOrEqualToZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i22">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsGreaterThanZero()">computeIsGreaterThanZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &gt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i23">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThan(double)">computeIsLessThan</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i24">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThan(float)">computeIsLessThan</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i25">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThan(int)">computeIsLessThan</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i26">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThan(long)">computeIsLessThan</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i27">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThan(com.shimizukenta.secs.local.property.NumberObservable)">computeIsLessThan</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt; observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i28">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanOrEqualTo(double)">computeIsLessThanOrEqualTo</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i29">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanOrEqualTo(float)">computeIsLessThanOrEqualTo</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i30">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanOrEqualTo(int)">computeIsLessThanOrEqualTo</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i31">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanOrEqualTo(long)">computeIsLessThanOrEqualTo</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i32">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">computeIsLessThanOrEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i33">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanOrEqualToZero()">computeIsLessThanOrEqualToZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i34">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsLessThanZero()">computeIsLessThanZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value &lt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i35">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(double)">computeIsNotEqualTo</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i36">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(float)">computeIsNotEqualTo</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i37">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(int)">computeIsNotEqualTo</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i38">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(long)">computeIsNotEqualTo</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i39">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">computeIsNotEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value != observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i40">
<td class="col-first"><code>default <a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#computeIsNotEqualToZero()">computeIsNotEqualToZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns ComparativeCompution of <code>this.value != 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i41">
<td class="col-first"><code>default boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#isDouble()">isDouble</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns true if Number is instance of Double, otherwise false.</div>
</td>
</tr>
<tr class="alt-color" id="i42">
<td class="col-first"><code>default boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#isFloat()">isFloat</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns true if Number is instance of Float, otherwise false.</div>
</td>
</tr>
<tr class="row-color" id="i43">
<td class="col-first"><code>default boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#isInteger()">isInteger</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns true if Number is instance of Integer, otherwise false.</div>
</td>
</tr>
<tr class="alt-color" id="i44">
<td class="col-first"><code>default boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#isLong()">isLong</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns true if Number is instance of Long, otherwise false.</div>
</td>
</tr>
<tr class="row-color" id="i45">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#multiply(double)">multiply</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value * value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i46">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#multiply(float)">multiply</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value * value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i47">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#multiply(int)">multiply</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value * value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i48">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#multiply(long)">multiply</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value * value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i49">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#multiply(com.shimizukenta.secs.local.property.NumberObservable)">multiply</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">NumberCompution of <code>this.value * observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i50">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#negate()">negate</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>-(this.value)</code>.</div>
</td>
</tr>
<tr class="row-color" id="i51">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#subtract(double)">subtract</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value - value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i52">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#subtract(float)">subtract</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value - value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i53">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#subtract(int)">subtract</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value - value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i54">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#subtract(long)">subtract</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value - value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i55">
<td class="col-first"><code>default <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#subtract(com.shimizukenta.secs.local.property.NumberObservable)">subtract</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns NumberCompution of <code>this.value - observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i56">
<td class="col-first"><code>default <a href="DoubleCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#toDouble()">toDouble</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns DoubleCompution converted instance.</div>
</td>
</tr>
<tr class="row-color" id="i57">
<td class="col-first"><code>default <a href="FloatCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#toFloat()">toFloat</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns FloatCompution converted instance.</div>
</td>
</tr>
<tr class="alt-color" id="i58">
<td class="col-first"><code>default <a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#toInteger()">toInteger</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns IntegerCompution coverted instance.</div>
</td>
</tr>
<tr class="row-color" id="i59">
<td class="col-first"><code>default <a href="LongCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#toLong()">toLong</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns LongComputionn converted instance.</div>
</td>
</tr>
<tr class="alt-color" id="i60">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(double)">waitUntilEqualTo</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i61">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(double,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i62">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i63">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(float)">waitUntilEqualTo</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i64">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(float,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i65">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i66">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(int)">waitUntilEqualTo</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i67">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(int,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i68">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i69">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(long)">waitUntilEqualTo</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i70">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(long,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i71">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i72">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this value == observable value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i73">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i74">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i75">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToZero()">waitUntilEqualToZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i76">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToZero(long,java.util.concurrent.TimeUnit)">waitUntilEqualToZero</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i77">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualToZero</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value == 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i78">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(double)">waitUntilGreaterThan</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i79">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(double,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a></span>&#8203;(double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i80">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a></span>&#8203;(double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i81">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(float)">waitUntilGreaterThan</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i82">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(float,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a></span>&#8203;(float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i83">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a></span>&#8203;(float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i84">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(int)">waitUntilGreaterThan</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i85">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(int,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a></span>&#8203;(int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i86">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a></span>&#8203;(int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i87">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(long)">waitUntilGreaterThan</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i88">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(long,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a></span>&#8203;(long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i89">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a></span>&#8203;(long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i90">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilGreaterThan</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i91">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i92">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i93">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(double)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i94">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(double,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i95">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i96">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(float)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i97">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(float,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i98">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i99">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(int)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i100">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(int,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i101">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i102">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(long)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i103">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(long,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i104">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i105">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i106">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i107">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i108">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualToZero()">waitUntilGreaterThanOrEqualToZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i109">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualToZero(long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualToZero</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i110">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanOrEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualToZero</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i111">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanZero()">waitUntilGreaterThanZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i112">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanZero(long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanZero</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i113">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilGreaterThanZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanZero</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &gt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i114">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(double)">waitUntilLessThan</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i115">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(double,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a></span>&#8203;(double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i116">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a></span>&#8203;(double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i117">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(float)">waitUntilLessThan</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i118">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(float,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a></span>&#8203;(float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i119">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a></span>&#8203;(float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">v        * Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i120">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(int)">waitUntilLessThan</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i121">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(int,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a></span>&#8203;(int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i122">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a></span>&#8203;(int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i123">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(long)">waitUntilLessThan</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i124">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(long,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a></span>&#8203;(long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i125">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a></span>&#8203;(long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i126">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilLessThan</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i127">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i128">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i129">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(double)">waitUntilLessThanOrEqualTo</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i130">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(double,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a></span>&#8203;(double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i131">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i132">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(float)">waitUntilLessThanOrEqualTo</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i133">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(float,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a></span>&#8203;(float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i134">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i135">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(int)">waitUntilLessThanOrEqualTo</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i136">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(int,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a></span>&#8203;(int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i137">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i138">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(long)">waitUntilLessThanOrEqualTo</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i139">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(long,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a></span>&#8203;(long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i140">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">v        * Waiting until <code>this.value &lt;= value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i141">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i142">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i143">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i144">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualToZero()">waitUntilLessThanOrEqualToZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i145">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualToZero(long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualToZero</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i146">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanOrEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualToZero</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt;= 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i147">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanZero()">waitUntilLessThanZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i148">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanZero(long,java.util.concurrent.TimeUnit)">waitUntilLessThanZero</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i149">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilLessThanZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanZero</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value &lt; 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i150">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(double)">waitUntilNotEqualTo</a></span>&#8203;(double&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i151">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(double,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i152">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i153">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(float)">waitUntilNotEqualTo</a></span>&#8203;(float&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i154">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(float,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i155">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i156">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(int)">waitUntilNotEqualTo</a></span>&#8203;(int&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i157">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(int,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i158">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i159">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(long)">waitUntilNotEqualTo</a></span>&#8203;(long&nbsp;value)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i160">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(long,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i161">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i162">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilNotEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i163">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != observable.value</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i164">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a></span>&#8203;(<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != observable.value</code>.</div>
</td>
</tr>
<tr class="row-color" id="i165">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToZero()">waitUntilNotEqualToZero</a></span>()</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != 0</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i166">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToZero(long,java.util.concurrent.TimeUnit)">waitUntilNotEqualToZero</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != 0</code>.</div>
</td>
</tr>
<tr class="row-color" id="i167">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#waitUntilNotEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualToZero</a></span>&#8203;(<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</code></th>
<td class="col-last">
<div class="block">Waiting until <code>this.value != 0</code>.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="isInteger()">
<h3>isInteger</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">isInteger</span>()</div>
<div class="block">Returns true if Number is instance of Integer, otherwise false.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if Number is instance of Integer, otherwise false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isLong()">
<h3>isLong</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">isLong</span>()</div>
<div class="block">Returns true if Number is instance of Long, otherwise false.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if Number is instance of Long, otherwise false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFloat()">
<h3>isFloat</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">isFloat</span>()</div>
<div class="block">Returns true if Number is instance of Float, otherwise false.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if Number is instance of Float, otherwise false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isDouble()">
<h3>isDouble</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">isDouble</span>()</div>
<div class="block">Returns true if Number is instance of Double, otherwise false.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if Number is instance of Double, otherwise false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toInteger()">
<h3>toInteger</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></span>&nbsp;<span class="member-name">toInteger</span>()</div>
<div class="block">Returns IntegerCompution coverted instance.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>IntegerComution converted instance.</dd>
<dt>関連項目:</dt>
<dd><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>IntegerCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toLong()">
<h3>toLong</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="LongCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongCompution</a></span>&nbsp;<span class="member-name">toLong</span>()</div>
<div class="block">Returns LongComputionn converted instance.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>LongCompution converted instance.</dd>
<dt>関連項目:</dt>
<dd><a href="LongCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>LongCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toFloat()">
<h3>toFloat</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="FloatCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatCompution</a></span>&nbsp;<span class="member-name">toFloat</span>()</div>
<div class="block">Returns FloatCompution converted instance.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>FloatCompution converted instance.</dd>
<dt>関連項目:</dt>
<dd><a href="FloatCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>FloatCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toDouble()">
<h3>toDouble</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="DoubleCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleCompution</a></span>&nbsp;<span class="member-name">toDouble</span>()</div>
<div class="block">Returns DoubleCompution converted instance.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>DoubleCompution comerted instance.</dd>
<dt>関連項目:</dt>
<dd><a href="DoubleCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>DoubleCompution</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">add</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">Returns NumberCompution of <code>this.value + observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value + observable.value</code>.</dd>
<dt>関連項目:</dt>
<dd><a href="NumberCompution.html#sum(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberCompution.sum(NumberObservable, NumberObservable)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="multiply(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>multiply</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">multiply</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">NumberCompution of <code>this.value * observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value * observable.value</code>.</dd>
<dt>関連項目:</dt>
<dd><a href="NumberCompution.html#multiply(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberCompution.multiply(NumberObservable, NumberObservable)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subtract(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>subtract</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">subtract</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">Returns NumberCompution of <code>this.value - observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value - observable.value</code>.</dd>
<dt>関連項目:</dt>
<dd><a href="NumberCompution.html#subtract(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberCompution.subtract(NumberObservable, NumberObservable)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="negate()">
<h3>negate</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">negate</span>()</div>
<div class="block">Returns NumberCompution of <code>-(this.value)</code>.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>NumberCompution of <code>-(this.value)</code>.</dd>
<dt>関連項目:</dt>
<dd><a href="#negate()"><code>negate()</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(int)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">add</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value + value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value + value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(long)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">add</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value + value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value + value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(float)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">add</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value + value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value + value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(double)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">add</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value + value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value + value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="multiply(int)">
<h3>multiply</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">multiply</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value * value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value * value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="multiply(long)">
<h3>multiply</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">multiply</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value * value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value * value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="multiply(float)">
<h3>multiply</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">multiply</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value * value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value * value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="multiply(double)">
<h3>multiply</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">multiply</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value * value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value * value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subtract(int)">
<h3>subtract</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">subtract</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value - value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value - value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subtract(long)">
<h3>subtract</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">subtract</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value - value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value - value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subtract(float)">
<h3>subtract</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">subtract</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value - value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value - value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subtract(double)">
<h3>subtract</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></span>&nbsp;<span class="member-name">subtract</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns NumberCompution of <code>this.value - value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>NumberCompution of <code>this.value - value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value == observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value == observable.value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value != observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value != observable.value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThan(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>computeIsLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThan</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt; observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt; observable.value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>computeIsLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt;= observable.value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThan(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>computeIsGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThan</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt; observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt; observable.value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>computeIsGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= observable.value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt;= observable.value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualTo(int)">
<h3>computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value == value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value == value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualTo(long)">
<h3>computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value == value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value == value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualTo(float)">
<h3>computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value == value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value == value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualTo(double)">
<h3>computeIsEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value == value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value == value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(int)">
<h3>computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value != value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(long)">
<h3>computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value != value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(float)">
<h3>computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value != value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualTo(double)">
<h3>computeIsNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value != value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThan(int)">
<h3>computeIsLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThan</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value != value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value != value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThan(long)">
<h3>computeIsLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThan</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt; value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt; value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThan(float)">
<h3>computeIsLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThan</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt; value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt; value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThan(double)">
<h3>computeIsLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThan</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt; value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt; value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanOrEqualTo(int)">
<h3>computeIsLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanOrEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt;= value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanOrEqualTo(long)">
<h3>computeIsLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanOrEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt;= value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanOrEqualTo(float)">
<h3>computeIsLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanOrEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt;= value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanOrEqualTo(double)">
<h3>computeIsLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanOrEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt;= value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThan(int)">
<h3>computeIsGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThan</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt; value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt; value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThan(long)">
<h3>computeIsGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThan</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt; value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt; value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThan(float)">
<h3>computeIsGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThan</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt; value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt; value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThan(double)">
<h3>computeIsGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThan</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt; value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt; value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanOrEqualTo(int)">
<h3>computeIsGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt;= value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanOrEqualTo(long)">
<h3>computeIsGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt;= value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanOrEqualTo(float)">
<h3>computeIsGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt;= value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanOrEqualTo(double)">
<h3>computeIsGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value)</span></div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= value</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt;= value</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsEqualToZero()">
<h3>computeIsEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsEqualToZero</span>()</div>
<div class="block">Returns ComparativeCompution of <code>this.value == 0</code>.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value == 0</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsNotEqualToZero()">
<h3>computeIsNotEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsNotEqualToZero</span>()</div>
<div class="block">Returns ComparativeCompution of <code>this.value != 0</code>.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value != 0</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanZero()">
<h3>computeIsLessThanZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanZero</span>()</div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt; 0</code>.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt; 0</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsLessThanOrEqualToZero()">
<h3>computeIsLessThanOrEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsLessThanOrEqualToZero</span>()</div>
<div class="block">Returns ComparativeCompution of <code>this.value &lt;= 0</code>.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &lt;= 0</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanZero()">
<h3>computeIsGreaterThanZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanZero</span>()</div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt; 0</code>.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt; 0</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="computeIsGreaterThanOrEqualToZero()">
<h3>computeIsGreaterThanOrEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></span>&nbsp;<span class="member-name">computeIsGreaterThanOrEqualToZero</span>()</div>
<div class="block">Returns ComparativeCompution of <code>this.value &gt;= 0</code>.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>ComparativeCompution of <code>this.value &gt;= 0</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span>
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this value == observable value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>p</code> - is TimeProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span>
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value != observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>p</code> - is TimeProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span>
                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt; observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>p</code> - is TimeProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt;= observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>p</code> - is TimeProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt; observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>p</code> - is TimeProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt;= observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters"><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;? extends java.lang.Number&gt;&nbsp;observer,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= observable.value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - the NumberObserver</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(int)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value)</span>
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(long)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value)</span>
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(float)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value)</span>
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(double)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value)</span>
                       throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(int,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(long,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(float,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(double,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                       throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(int)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value)</span>
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(long)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value)</span>
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(float)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value)</span>
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(double)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value)</span>
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(int,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(long,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(float,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(double,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                          throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(int)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">int&nbsp;value)</span>
                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(long)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">long&nbsp;value)</span>
                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(float)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">float&nbsp;value)</span>
                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(double)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">double&nbsp;value)</span>
                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(int,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(long,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(float,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(double,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(int,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(long,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(float,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">v        * Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThan(double,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThan</span>&#8203;(<span class="parameters">double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(int)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(long)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(float)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(double)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(int,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(long,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(float,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(double,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - s int</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">v        * Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(int)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">int&nbsp;value)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(long)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">long&nbsp;value)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(float)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">float&nbsp;value)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(double)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">double&nbsp;value)</span>
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(int,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(long,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(float,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(double,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(int,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(long,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(float,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThan(double,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThan</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThan</span>&#8203;(<span class="parameters">double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(int)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(long)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(float)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(double)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(int,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(long,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(float,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(double,long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value,
long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">int&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the int value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">long&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the long value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">float&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the float value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanOrEqualTo</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualTo</span>&#8203;(<span class="parameters">double&nbsp;value,
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                    throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= value</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the double value</dd>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToZero()">
<h3>waitUntilEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToZero</span>()
                           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value == 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToZero(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToZero</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilEqualToZero</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                           throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value == 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToZero()">
<h3>waitUntilNotEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToZero</span>()
                              throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value != 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToZero(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilNotEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToZero</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilNotEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilNotEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilNotEqualToZero</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                              throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value != 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanZero()">
<h3>waitUntilLessThanZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanZero</span>()
                            throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanZero(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanZero</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                            throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanZero(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanZero</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                            throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualToZero()">
<h3>waitUntilLessThanOrEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualToZero</span>()
                                     throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualToZero(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilLessThanOrEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualToZero</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                     throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilLessThanOrEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilLessThanOrEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilLessThanOrEqualToZero</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                     throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &lt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanZero()">
<h3>waitUntilGreaterThanZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanZero</span>()
                               throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanZero(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanZero</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                               throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanZero(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanZero</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                               throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt; 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualToZero()">
<h3>waitUntilGreaterThanOrEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualToZero</span>()
                                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">Waiting until <code>this.value &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualToZero(long,java.util.concurrent.TimeUnit)">
<h3>waitUntilGreaterThanOrEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualToZero</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span>
                                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilGreaterThanOrEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">
<h3>waitUntilGreaterThanOrEqualToZero</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">waitUntilGreaterThanOrEqualToZero</span>&#8203;(<span class="parameters"><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>&nbsp;p)</span>
                                        throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException</span></div>
<div class="block">Waiting until <code>this.value &gt;= 0</code>.
 
 <p>
 This is blocking method.<br/>
 If already condition is true, pass through immediately.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>p</code> - the TimeoutProperty</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
