<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>TimeoutProperty</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: TimeoutProperty">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース TimeoutProperty" class="title">インタフェース TimeoutProperty</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&gt;</code>, <code><a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース">Property</a>&lt;<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&gt;</code>, <code>java.io.Serializable</code>, <code><a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a>&lt;<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&gt;</code>, <code><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a></code>, <code><a href="TimeoutObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutObservable</a></code>, <code><a href="TimeoutSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutSettable</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">TimeoutProperty</span>
extends <a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース">Property</a>&lt;<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&gt;, <a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a>, <a href="TimeoutSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutSettable</a>, <a href="TimeoutObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutObservable</a></pre>
<div class="block">TimeoutAndUnit value Property, includes Getter, Setter, Observer.
 
 <p>
 This instance can be used as waitUntil-method's timeout parameter.<br/>
 </p>
 <ul>
 <li>To build instance,
 <ul>
 <li><a href="#newInstance(long,java.util.concurrent.TimeUnit)"><code>newInstance(long, TimeUnit)</code></a></li>
 <li><a href="#newInstance(double)"><code>newInstance(float)</code></a></li>
 </ul>
 </li>
 <li>To set value,
 <ul>
 <li><a href="TimeoutSettable.html#set(long,java.util.concurrent.TimeUnit)"><code>TimeoutSettable.set(long, TimeUnit)</code></a></li>
 <li><a href="TimeoutSettable.html#set(double)"><code>TimeoutSettable.set(float)</code></a></li>
 </ul>
 </li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>Utility methods,
 <ul>
 <li><a href="TimeoutGettable.html#sleep()"><code>TimeoutGettable.sleep()</code></a></li>
 <li><a href="TimeoutGettable.html#wait(java.lang.Object)"><code>TimeoutGettable.wait(Object)</code></a></li>
 <li><a href="TimeoutGettable.html#blockingQueuePoll(java.util.concurrent.BlockingQueue)"><code>TimeoutGettable.blockingQueuePoll(java.util.concurrent.BlockingQueue)</code></a></li>
 <li><a href="TimeoutGettable.html#futureGet(java.util.concurrent.Future)"><code>TimeoutGettable.futureGet(java.util.concurrent.Future)</code></a></li>
 <li><a href="TimeoutGettable.html#invokeAll(java.util.concurrent.ExecutorService,java.util.Collection)"><code>TimeoutGettable.invokeAll(java.util.concurrent.ExecutorService, java.util.Collection)</code></a></li>
 <li><a href="TimeoutGettable.html#invokeAny(java.util.concurrent.ExecutorService,java.util.Collection)"><code>TimeoutGettable.invokeAny(java.util.concurrent.ExecutorService, java.util.Collection)</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>TimeUnit</code>, 
<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>TimeoutAndUnit</code></a>, 
<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>TimeoutGettable</code></a>, 
<a href="TimeoutSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>TimeoutSettable</code></a>, 
<a href="TimeoutObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>TimeoutObservable</code></a>, 
<a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Property</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<table class="summary-table">
<caption><span>staticメソッド</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>static <a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(double)">newInstance</a></span>&#8203;(double&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">TimeoutProperty builder.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>static <a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(float)">newInstance</a></span>&#8203;(float&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">TimeoutProperty builder.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>static <a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(int)">newInstance</a></span>&#8203;(int&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">TimeouyProperty builder.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>static <a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(long)">newInstance</a></span>&#8203;(long&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">TimeoutProperty builder.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>static <a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(long,java.util.concurrent.TimeUnit)">newInstance</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">TimeoutProperty builder.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>static <a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(com.shimizukenta.secs.local.property.TimeoutAndUnit)">newInstance</a></span>&#8203;(<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&nbsp;value)</code></th>
<td class="col-last">
<div class="block">TimeoutProperty builder.</div>
</td>
</tr>
</tbody>
</table>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Settable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a></h3>
<code><a href="Settable.html#bind(com.shimizukenta.secs.local.property.Observable)">bind</a>, <a href="Settable.html#unbind(com.shimizukenta.secs.local.property.Observable)">unbind</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.TimeoutGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a></h3>
<code><a href="TimeoutGettable.html#awaitTermination(java.util.concurrent.ExecutorService)">awaitTermination</a>, <a href="TimeoutGettable.html#blockingQueuePoll(java.util.concurrent.BlockingQueue)">blockingQueuePoll</a>, <a href="TimeoutGettable.html#conditionAwait(java.util.concurrent.locks.Condition)">conditionAwait</a>, <a href="TimeoutGettable.html#futureGet(java.util.concurrent.Future)">futureGet</a>, <a href="TimeoutGettable.html#get()">get</a>, <a href="TimeoutGettable.html#getTimeout()">getTimeout</a>, <a href="TimeoutGettable.html#getTimeUnit()">getTimeUnit</a>, <a href="TimeoutGettable.html#invokeAll(java.util.concurrent.ExecutorService,java.util.Collection)">invokeAll</a>, <a href="TimeoutGettable.html#invokeAny(java.util.concurrent.ExecutorService,java.util.Collection)">invokeAny</a>, <a href="TimeoutGettable.html#join(java.lang.Thread)">join</a>, <a href="TimeoutGettable.html#lockTryLock(java.util.concurrent.locks.Lock)">lockTryLock</a>, <a href="TimeoutGettable.html#sleep()">sleep</a>, <a href="TimeoutGettable.html#wait(java.lang.Object)">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.TimeoutSettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="TimeoutSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutSettable</a></h3>
<code><a href="TimeoutSettable.html#set(double)">set</a>, <a href="TimeoutSettable.html#set(float)">set</a>, <a href="TimeoutSettable.html#set(int)">set</a>, <a href="TimeoutSettable.html#set(long)">set</a>, <a href="TimeoutSettable.html#set(long,java.util.concurrent.TimeUnit)">set</a>, <a href="TimeoutSettable.html#set(com.shimizukenta.secs.local.property.TimeoutAndUnit)">set</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="newInstance(int)">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters">int&nbsp;seconds)</span></div>
<div class="block">TimeouyProperty builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newInstance(long)">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters">long&nbsp;seconds)</span></div>
<div class="block">TimeoutProperty builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newInstance(float)">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters">float&nbsp;seconds)</span></div>
<div class="block">TimeoutProperty builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newInstance(double)">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters">double&nbsp;seconds)</span></div>
<div class="block">TimeoutProperty builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newInstance(long,java.util.concurrent.TimeUnit)">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span></div>
<div class="block">TimeoutProperty builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the long value</dd>
<dd><code>unit</code> - the TimeUnit</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newInstance(com.shimizukenta.secs.local.property.TimeoutAndUnit)">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters"><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&nbsp;value)</span></div>
<div class="block">TimeoutProperty builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>value</code> - the TimeoutAndUnit</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
