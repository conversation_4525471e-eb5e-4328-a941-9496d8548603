<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>LogicalCompution</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: LogicalCompution">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース LogicalCompution" class="title">インタフェース LogicalCompution</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code>, <code><a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a></code>, <code><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></code>, <code><a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;java.lang.Boolean&gt;</code>, <code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;java.lang.Boolean&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.Boolean&gt;</code>, <code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">LogicalCompution</span>
extends <a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></pre>
<div class="block">Logical Boolean compution, includes Getter, Observer.
 
 <p>
 <strong>NOT</strong> includes Setter.<br/>
 </p>
 <ul>
 <li>To build LogicalCompution instance,
 <ul>
 <li>AND<code>(&amp;&amp;)</code> Compution, <a href="#and(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>and(BooleanObservable...)</code></a>.</li>
 <li>OR<code>(||)</code> Compution, <a href="#or(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>or(BooleanObservable...)</code></a>.</li>
 <li>NOT<code>(!)</code> Compution, <a href="#not(com.shimizukenta.secs.local.property.BooleanObservable)"><code>not(BooleanObservable)</code></a>.</li>
 <li>XOR<code>(^)</code> Compution, <a href="#xor(com.shimizukenta.secs.local.property.BooleanObservable,com.shimizukenta.secs.local.property.BooleanObservable)"><code>xor(BooleanObservable, BooleanObservable)</code></a>.</li>
 <li>NAND Compution, <a href="#nand(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>nand(BooleanObservable...)</code></a>.</li>
 <li>NOR Compution, <a href="#nor(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>nor(BooleanObservable...)</code></a>.</li>
 </ul>
 </li>
 <li>To get value, <a href="BooleanGettable.html#booleanValue()"><code>BooleanGettable.booleanValue()</code></a>.</li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a>.</li>
 <li>To wait until condition is true,
 <ul>
 <li><a href="BooleanObservable.html#waitUntil(boolean)"><code>BooleanObservable.waitUntil(boolean)</code></a></li>
 <li><a href="BooleanObservable.html#waitUntil(boolean,long,java.util.concurrent.TimeUnit)"><code>BooleanObservable.waitUntil(boolean, long, java.util.concurrent.TimeUnit)</code></a></li>
 </ul>
 </lI>
 <li>To logical-compute,
 <ul>
 <li><a href="BooleanObservable.html#and(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.and(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#or(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.or(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#not()"><code>BooleanObservable.not()</code></a></li>
 <li><a href="BooleanObservable.html#xor(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.xor(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#nand(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.nand(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#nor(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.nor(BooleanObservable)</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanCompution</code></a>, 
<code>Boolean</code></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<table class="summary-table">
<caption><span>staticメソッド</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#and(com.shimizukenta.secs.local.property.BooleanObservable...)">and</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>...&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns AND<code>(&amp;&amp;)</code> Logical Compution instance.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#and(java.util.Collection)">and</a></span>&#8203;(java.util.Collection&lt;? extends <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&gt;&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns AND<code>(&amp;&amp;)</code> Logical Compution instance.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#nand(com.shimizukenta.secs.local.property.BooleanObservable...)">nand</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>...&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns NAND Logical Compution instance.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#nand(java.util.Collection)">nand</a></span>&#8203;(java.util.Collection&lt;? extends <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&gt;&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns NAND Logical Compution instance.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#nor(com.shimizukenta.secs.local.property.BooleanObservable...)">nor</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>...&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns NOR Logical Compution instance.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#nor(java.util.Collection)">nor</a></span>&#8203;(java.util.Collection&lt;? extends <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&gt;&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns NOR Logical Compution instance</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#not(com.shimizukenta.secs.local.property.BooleanObservable)">not</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</code></th>
<td class="col-last">
<div class="block">Returns NOT<code>(!)</code> Logical Compution instance.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#or(com.shimizukenta.secs.local.property.BooleanObservable...)">or</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>...&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns OR<code>(||)</code> Logical Compution instance.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#or(java.util.Collection)">or</a></span>&#8203;(java.util.Collection&lt;? extends <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&gt;&nbsp;observers)</code></th>
<td class="col-last">
<div class="block">Returns OR<code>(||)</code> Logical Compution instance.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>static <a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#xor(com.shimizukenta.secs.local.property.BooleanObservable,com.shimizukenta.secs.local.property.BooleanObservable)">xor</a></span>&#8203;(<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;a,
<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;b)</code></th>
<td class="col-last">
<div class="block">Returns XOR<code>(^)</code> Logical Compution instance.</div>
</td>
</tr>
</tbody>
</table>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.BooleanGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a></h3>
<code><a href="BooleanGettable.html#booleanValue()">booleanValue</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.BooleanObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></h3>
<code><a href="BooleanObservable.html#and(boolean)">and</a>, <a href="BooleanObservable.html#and(com.shimizukenta.secs.local.property.BooleanObservable)">and</a>, <a href="BooleanObservable.html#nand(boolean)">nand</a>, <a href="BooleanObservable.html#nand(com.shimizukenta.secs.local.property.BooleanObservable)">nand</a>, <a href="BooleanObservable.html#nor(boolean)">nor</a>, <a href="BooleanObservable.html#nor(com.shimizukenta.secs.local.property.BooleanObservable)">nor</a>, <a href="BooleanObservable.html#not()">not</a>, <a href="BooleanObservable.html#or(boolean)">or</a>, <a href="BooleanObservable.html#or(com.shimizukenta.secs.local.property.BooleanObservable)">or</a>, <a href="BooleanObservable.html#waitUntil(boolean)">waitUntil</a>, <a href="BooleanObservable.html#waitUntil(boolean,long,java.util.concurrent.TimeUnit)">waitUntil</a>, <a href="BooleanObservable.html#waitUntil(boolean,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntil</a>, <a href="BooleanObservable.html#waitUntilFalse()">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilFalse(long,java.util.concurrent.TimeUnit)">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilFalse(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilTrue()">waitUntilTrue</a>, <a href="BooleanObservable.html#waitUntilTrue(long,java.util.concurrent.TimeUnit)">waitUntilTrue</a>, <a href="BooleanObservable.html#waitUntilTrue(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilTrue</a>, <a href="BooleanObservable.html#xor(boolean)">xor</a>, <a href="BooleanObservable.html#xor(com.shimizukenta.secs.local.property.BooleanObservable)">xor</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="and(com.shimizukenta.secs.local.property.BooleanObservable...)">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">and</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>...&nbsp;observers)</span></div>
<div class="block">Returns AND<code>(&amp;&amp;)</code> Logical Compution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observers</code> - is BooleanObserver</dd>
<dt>戻り値:</dt>
<dd>AND<code>(&amp;&amp;</code>) Logical Compution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#and(java.util.Collection)"><code>and(Collection)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="and(java.util.Collection)">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">and</span>&#8203;(<span class="parameters">java.util.Collection&lt;? extends <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&gt;&nbsp;observers)</span></div>
<div class="block">Returns AND<code>(&amp;&amp;)</code> Logical Compution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observers</code> - is BooleanObservers</dd>
<dt>戻り値:</dt>
<dd>AND<code>(&amp;&amp;)</code> Logical Compution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#and(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>and(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="or(com.shimizukenta.secs.local.property.BooleanObservable...)">
<h3>or</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">or</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>...&nbsp;observers)</span></div>
<div class="block">Returns OR<code>(||)</code> Logical Compution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observers</code> - is BooleanObservers</dd>
<dt>戻り値:</dt>
<dd>OR<code>(||)</code> Logical Compution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#or(java.util.Collection)"><code>or(Collection)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="or(java.util.Collection)">
<h3>or</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">or</span>&#8203;(<span class="parameters">java.util.Collection&lt;? extends <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&gt;&nbsp;observers)</span></div>
<div class="block">Returns OR<code>(||)</code> Logical Compution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observers</code> - is BooleanObservers</dd>
<dt>戻り値:</dt>
<dd>OR<code>(||)</code> Logical Compution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#or(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>or(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="not(com.shimizukenta.secs.local.property.BooleanObservable)">
<h3>not</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">not</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;observer)</span></div>
<div class="block">Returns NOT<code>(!)</code> Logical Compution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observer</code> - is BooleanObserver</dd>
<dt>戻り値:</dt>
<dd>NOT<code>(!)</code> Logical Compution instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="xor(com.shimizukenta.secs.local.property.BooleanObservable,com.shimizukenta.secs.local.property.BooleanObservable)">
<h3>xor</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">xor</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;a,
<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&nbsp;b)</span></div>
<div class="block">Returns XOR<code>(^)</code> Logical Compution instance.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>a</code> - is BooleanObserver</dd>
<dd><code>b</code> - is BooleanObserver</dd>
<dt>戻り値:</dt>
<dd>XOR<code>(^)</code> Logical Compution instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nand(com.shimizukenta.secs.local.property.BooleanObservable...)">
<h3>nand</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">nand</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>...&nbsp;observers)</span></div>
<div class="block">Returns NAND Logical Compution instance.
 
 <p>
 Returns NOT(AND) compution.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observers</code> - is BooleanObservers</dd>
<dt>戻り値:</dt>
<dd>NAND Logical Compution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#nand(java.util.Collection)"><code>nand(Collection)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nand(java.util.Collection)">
<h3>nand</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">nand</span>&#8203;(<span class="parameters">java.util.Collection&lt;? extends <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&gt;&nbsp;observers)</span></div>
<div class="block">Returns NAND Logical Compution instance.
 
 <p>
 Returns NOT(AND) compution.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observers</code> - is BooleanObservers</dd>
<dt>戻り値:</dt>
<dd>NAND Logical Compution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#nand(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>nand(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nor(com.shimizukenta.secs.local.property.BooleanObservable...)">
<h3>nor</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">nor</span>&#8203;(<span class="parameters"><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>...&nbsp;observers)</span></div>
<div class="block">Returns NOR Logical Compution instance.
 
 <p>
 Returns NOT(OR) compution.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observers</code> - is BooleanObservers</dd>
<dt>戻り値:</dt>
<dd>NOR Logical Compution instance</dd>
<dt>関連項目:</dt>
<dd><a href="#nor(java.util.Collection)"><code>nor(Collection)</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nor(java.util.Collection)">
<h3>nor</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></span>&nbsp;<span class="member-name">nor</span>&#8203;(<span class="parameters">java.util.Collection&lt;? extends <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a>&gt;&nbsp;observers)</span></div>
<div class="block">Returns NOR Logical Compution instance
 
 <p>
 Returns NOT(OR) compution.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>observers</code> - is BooleanObservers</dd>
<dt>戻り値:</dt>
<dd>NOR Logical Compution instance.</dd>
<dt>関連項目:</dt>
<dd><a href="#nor(com.shimizukenta.secs.local.property.BooleanObservable...)"><code>nor(BooleanObservable...)</code></a></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
