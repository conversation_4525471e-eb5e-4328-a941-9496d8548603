<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>Gettable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: Gettable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース Gettable" class="title">インタフェース Gettable&lt;T&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanCompution</a></code>, <code><a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a></code>, <code><a href="BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></code>, <code><a href="ComparativeCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ComparativeCompution</a></code>, <code><a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;T&gt;</code>, <code><a href="DoubleCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleCompution</a></code>, <code><a href="DoubleProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">DoubleProperty</a></code>, <code><a href="FloatCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatCompution</a></code>, <code><a href="FloatProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">FloatProperty</a></code>, <code><a href="IntegerCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerCompution</a></code>, <code><a href="IntegerProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">IntegerProperty</a></code>, <code><a href="LogicalCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LogicalCompution</a></code>, <code><a href="LongCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongCompution</a></code>, <code><a href="LongProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">LongProperty</a></code>, <code><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code>, <code><a href="NumberGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberGettable</a>&lt;T&gt;</code>, <code><a href="NumberProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberProperty</a>&lt;T&gt;</code>, <code><a href="ObjectCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectCompution</a>&lt;T&gt;</code>, <code><a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectGettable</a>&lt;T&gt;</code>, <code><a href="ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;T&gt;</code>, <code><a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース">Property</a>&lt;T&gt;</code>, <code><a href="StringCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringCompution</a></code>, <code><a href="StringGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringGettable</a></code>, <code><a href="StringProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringProperty</a></code>, <code><a href="TimeoutGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutGettable</a></code>, <code><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">Gettable&lt;T&gt;</span></pre>
<div class="block">Super Getter interface.</div>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
