<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>ObjectCompution</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: ObjectCompution">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース ObjectCompution" class="title">インタフェース ObjectCompution&lt;T&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
</dl>
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;T&gt;</code>, <code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;T&gt;</code>, <code><a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectGettable</a>&lt;T&gt;</code>, <code><a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;T&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;T&gt;</code>, <code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">ObjectCompution&lt;T&gt;</span>
extends <a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;T&gt;, <a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectGettable</a>&lt;T&gt;, <a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a>&lt;T&gt;</pre>
<div class="block">Object value Compution, includes Getter, Observer.
 
 <p>
 <strong>NOT</strong> includes Setter.<br/>
 </p>
 <p>
 This instance is built from other Property or Compution.<br/>
 </p>
 <ul>
 <li>To get value, <a href="ObjectGettable.html#get()"><code>ObjectGettable.get()</code></a></li>
 <li>To get Optional, <a href="ObjectGettable.html#optional()"><code>ObjectGettable.optional()</code></a></li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>To compute,
 <ul>
 <li><a href="ObjectObservable.html#computeIsEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)"><code>ObjectObservable.computeIsEqualTo(ObjectObservable)</code></a></li>
 <li><a href="ObjectObservable.html#computeIsNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)"><code>ObjectObservable.computeIsNotEqualTo(ObjectObservable)</code></a></li>
 </ul>
 </li>
 <li>To wait until condition is true,
 <ul>
 <li><a href="ObjectObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)"><code>ObjectObservable.waitUntilEqualTo(ObjectObservable)</code></a></li>
 <li><a href="ObjectObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)"><code>ObjectObservable.waitUntilNotEqualTo(ObjectObservable)</code></a></li>
 <li><a href="ObjectObservable.html#waitUntilNotNullAndGet()"><code>ObjectObservable.waitUntilNotNullAndGet()</code></a></li>
 <li><a href="ObjectObservable.html#waitUntilNull()"><code>ObjectObservable.waitUntilNull()</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ObjectGettable</code></a>, 
<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ObjectObservable</code></a>, 
<a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Compution</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.ObjectGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="ObjectGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectGettable</a></h3>
<code><a href="ObjectGettable.html#get()">get</a>, <a href="ObjectGettable.html#isNull()">isNull</a>, <a href="ObjectGettable.html#optional()">optional</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.ObjectObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="ObjectObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectObservable</a></h3>
<code><a href="ObjectObservable.html#computeIsEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">computeIsEqualTo</a>, <a href="ObjectObservable.html#computeIsEqualTo(U)">computeIsEqualTo</a>, <a href="ObjectObservable.html#computeIsNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">computeIsNotEqualTo</a>, <a href="ObjectObservable.html#computeIsNotEqualTo(U)">computeIsNotEqualTo</a>, <a href="ObjectObservable.html#computeIsNotNull()">computeIsNotNull</a>, <a href="ObjectObservable.html#computeIsNull()">computeIsNull</a>, <a href="ObjectObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(U)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(U,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilEqualTo(U,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.ObjectObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(U)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(U,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotEqualTo(U,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="ObjectObservable.html#waitUntilNotNullAndGet()">waitUntilNotNullAndGet</a>, <a href="ObjectObservable.html#waitUntilNotNullAndGet(long,java.util.concurrent.TimeUnit)">waitUntilNotNullAndGet</a>, <a href="ObjectObservable.html#waitUntilNotNullAndGet(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotNullAndGet</a>, <a href="ObjectObservable.html#waitUntilNull()">waitUntilNull</a>, <a href="ObjectObservable.html#waitUntilNull(long,java.util.concurrent.TimeUnit)">waitUntilNull</a>, <a href="ObjectObservable.html#waitUntilNull(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNull</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
