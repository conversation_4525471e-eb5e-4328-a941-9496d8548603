<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>TimeoutAndUnit</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: TimeoutAndUnit">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":18,"i1":1,"i2":1,"i3":1,"i4":1,"i5":1,"i6":6,"i7":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],1:["t1","static\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"],16:["t5","\u30C7\u30D5\u30A9\u30EB\u30C8\u30FB\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース TimeoutAndUnit" class="title">インタフェース TimeoutAndUnit</h1>
</div>
<section class="description">
<hr>
<pre>public interface <span class="type-name-label">TimeoutAndUnit</span></pre>
<div class="block">This interface instance is a pair of timeout and TimeUnit.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>TimeUnit</code></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t1" class="table-tab" onclick="show(1);">staticメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t5" class="table-tab" onclick="show(16);">デフォルト・メソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>default long</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#getMilliSeconds()">getMilliSeconds</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Milli-Seconds.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>static <a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#of(double)">of</a></span>&#8203;(double&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">Instance builder.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>static <a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#of(float)">of</a></span>&#8203;(float&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">Instance builder.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>static <a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#of(int)">of</a></span>&#8203;(int&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">Instance builder.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>static <a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#of(long)">of</a></span>&#8203;(long&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">Instance builder.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>static <a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#of(long,java.util.concurrent.TimeUnit)">of</a></span>&#8203;(long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</code></th>
<td class="col-last">
<div class="block">Instance builder.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>long</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#timeout()">timeout</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns timeout.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>java.util.concurrent.TimeUnit</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#unit()">unit</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns TimeUnit.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="timeout()">
<h3>timeout</h3>
<div class="member-signature"><span class="return-type">long</span>&nbsp;<span class="member-name">timeout</span>()</div>
<div class="block">Returns timeout.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>timeout.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="unit()">
<h3>unit</h3>
<div class="member-signature"><span class="return-type">java.util.concurrent.TimeUnit</span>&nbsp;<span class="member-name">unit</span>()</div>
<div class="block">Returns TimeUnit.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>TImeUnit.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMilliSeconds()">
<h3>getMilliSeconds</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="member-name">getMilliSeconds</span>()</div>
<div class="block">Returns Milli-Seconds.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Milli-Seconds.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(long,java.util.concurrent.TimeUnit)">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></span>&nbsp;<span class="member-name">of</span>&#8203;(<span class="parameters">long&nbsp;timeout,
java.util.concurrent.TimeUnit&nbsp;unit)</span></div>
<div class="block">Instance builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>timeout</code> - the long value</dd>
<dd><code>unit</code> - the TimeUnit</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(int)">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></span>&nbsp;<span class="member-name">of</span>&#8203;(<span class="parameters">int&nbsp;seconds)</span></div>
<div class="block">Instance builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - the int value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(long)">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></span>&nbsp;<span class="member-name">of</span>&#8203;(<span class="parameters">long&nbsp;seconds)</span></div>
<div class="block">Instance builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - the long value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(float)">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></span>&nbsp;<span class="member-name">of</span>&#8203;(<span class="parameters">float&nbsp;seconds)</span></div>
<div class="block">Instance builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - the float value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(double)">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></span>&nbsp;<span class="member-name">of</span>&#8203;(<span class="parameters">double&nbsp;seconds)</span></div>
<div class="block">Instance builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - the double value</dd>
<dt>戻り値:</dt>
<dd>new-instance</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
