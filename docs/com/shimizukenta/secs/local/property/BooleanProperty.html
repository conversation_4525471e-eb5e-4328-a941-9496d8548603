<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>BooleanProperty</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: BooleanProperty">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース BooleanProperty" class="title">インタフェース BooleanProperty</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a></code>, <code><a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></code>, <code><a href="BooleanSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanSettable</a></code>, <code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;java.lang.Boolean&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.Boolean&gt;</code>, <code><a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース">Property</a>&lt;java.lang.Boolean&gt;</code>, <code>java.io.Serializable</code>, <code><a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a>&lt;java.lang.Boolean&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">BooleanProperty</span>
extends <a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース">Property</a>&lt;java.lang.Boolean&gt;, <a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a>, <a href="BooleanSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanSettable</a>, <a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></pre>
<div class="block">Boolean value Property, includes Getter, Setter, Observer.
 
 <ul>
 <li>To build instance, <a href="#newInstance(boolean)"><code>newInstance(boolean)</code></a></li>
 <li>To get value, <a href="BooleanGettable.html#booleanValue()"><code>BooleanGettable.booleanValue()</code></a></li>
 <li>To set value, <a href="BooleanSettable.html#set(boolean)"><code>BooleanSettable.set(boolean)</code></a></li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>To wait until condition is true,
 <ul>
 <li><a href="BooleanObservable.html#waitUntil(boolean)"><code>BooleanObservable.waitUntil(boolean)</code></a></li>
 <li><a href="BooleanObservable.html#waitUntil(boolean,long,java.util.concurrent.TimeUnit)"><code>BooleanObservable.waitUntil(boolean, long, java.util.concurrent.TimeUnit)</code></a></li>
 </ul>
 </lI>
 <li>To logical-compute,
 <ul>
 <li><a href="BooleanObservable.html#and(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.and(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#or(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.or(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#not()"><code>BooleanObservable.not()</code></a></li>
 <li><a href="BooleanObservable.html#xor(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.xor(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#nand(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.nand(BooleanObservable)</code></a></li>
 <li><a href="BooleanObservable.html#nor(com.shimizukenta.secs.local.property.BooleanObservable)"><code>BooleanObservable.nor(BooleanObservable)</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>Boolean</code>, 
<a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanGettable</code></a>, 
<a href="BooleanSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanSettable</code></a>, 
<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanObservable</code></a>, 
<a href="Property.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Property</code></a>, 
<a href="BooleanCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>BooleanCompution</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<table class="summary-table">
<caption><span>staticメソッド</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>static <a href="BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#newInstance(boolean)">newInstance</a></span>&#8203;(boolean&nbsp;initial)</code></th>
<td class="col-last">
<div class="block">BooleanProperty builder.</div>
</td>
</tr>
</tbody>
</table>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.BooleanGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="BooleanGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanGettable</a></h3>
<code><a href="BooleanGettable.html#booleanValue()">booleanValue</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.BooleanObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="BooleanObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanObservable</a></h3>
<code><a href="BooleanObservable.html#and(boolean)">and</a>, <a href="BooleanObservable.html#and(com.shimizukenta.secs.local.property.BooleanObservable)">and</a>, <a href="BooleanObservable.html#nand(boolean)">nand</a>, <a href="BooleanObservable.html#nand(com.shimizukenta.secs.local.property.BooleanObservable)">nand</a>, <a href="BooleanObservable.html#nor(boolean)">nor</a>, <a href="BooleanObservable.html#nor(com.shimizukenta.secs.local.property.BooleanObservable)">nor</a>, <a href="BooleanObservable.html#not()">not</a>, <a href="BooleanObservable.html#or(boolean)">or</a>, <a href="BooleanObservable.html#or(com.shimizukenta.secs.local.property.BooleanObservable)">or</a>, <a href="BooleanObservable.html#waitUntil(boolean)">waitUntil</a>, <a href="BooleanObservable.html#waitUntil(boolean,long,java.util.concurrent.TimeUnit)">waitUntil</a>, <a href="BooleanObservable.html#waitUntil(boolean,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntil</a>, <a href="BooleanObservable.html#waitUntilFalse()">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilFalse(long,java.util.concurrent.TimeUnit)">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilFalse(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilFalse</a>, <a href="BooleanObservable.html#waitUntilTrue()">waitUntilTrue</a>, <a href="BooleanObservable.html#waitUntilTrue(long,java.util.concurrent.TimeUnit)">waitUntilTrue</a>, <a href="BooleanObservable.html#waitUntilTrue(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilTrue</a>, <a href="BooleanObservable.html#xor(boolean)">xor</a>, <a href="BooleanObservable.html#xor(com.shimizukenta.secs.local.property.BooleanObservable)">xor</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.BooleanSettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="BooleanSettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanSettable</a></h3>
<code><a href="BooleanSettable.html#set(boolean)">set</a>, <a href="BooleanSettable.html#setFalse()">setFalse</a>, <a href="BooleanSettable.html#setTrue()">setTrue</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Settable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Settable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Settable</a></h3>
<code><a href="Settable.html#bind(com.shimizukenta.secs.local.property.Observable)">bind</a>, <a href="Settable.html#unbind(com.shimizukenta.secs.local.property.Observable)">unbind</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="newInstance(boolean)">
<h3>newInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></span>&nbsp;<span class="member-name">newInstance</span>&#8203;(<span class="parameters">boolean&nbsp;initial)</span></div>
<div class="block">BooleanProperty builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>initial</code> - the boolean value</dd>
<dt>戻り値:</dt>
<dd>new-instance.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
