<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>TimeoutGettable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: TimeoutGettable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":18,"i1":18,"i2":18,"i3":18,"i4":6,"i5":18,"i6":18,"i7":18,"i8":18,"i9":18,"i10":18,"i11":18,"i12":18};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"],16:["t5","\u30C7\u30D5\u30A9\u30EB\u30C8\u30FB\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース TimeoutGettable" class="title">インタフェース TimeoutGettable</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&gt;</code></dd>
</dl>
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">TimeoutGettable</span>
extends <a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a>&gt;</pre>
<div class="block">TimeoutAndUnit value Getter.</div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>TimeUnit</code>, 
<a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>TimeoutAndUnit</code></a>, 
<a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>Gettable</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t5" class="table-tab" onclick="show(16);">デフォルト・メソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>default boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#awaitTermination(java.util.concurrent.ExecutorService)">awaitTermination</a></span>&#8203;(java.util.concurrent.ExecutorService&nbsp;executorService)</code></th>
<td class="col-last">
<div class="block">This calls ExecutorService#awaitTermination(long, TimeUnit).</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>default &lt;T&gt;&nbsp;T</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#blockingQueuePoll(java.util.concurrent.BlockingQueue)">blockingQueuePoll</a></span>&#8203;(java.util.concurrent.BlockingQueue&lt;T&gt;&nbsp;queue)</code></th>
<td class="col-last">
<div class="block">This calls BlockingQueue#poll(long, TimeUnit).</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>default boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#conditionAwait(java.util.concurrent.locks.Condition)">conditionAwait</a></span>&#8203;(java.util.concurrent.locks.Condition&nbsp;condition)</code></th>
<td class="col-last">
<div class="block">This calls Condition#await(long, TimeUnit).</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>default &lt;T&gt;&nbsp;T</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#futureGet(java.util.concurrent.Future)">futureGet</a></span>&#8203;(java.util.concurrent.Future&lt;T&gt;&nbsp;future)</code></th>
<td class="col-last">
<div class="block">This calls Future#get(long, TimeUnit).</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#get()">get</a></span>()</code></th>
<td class="col-last">
<div class="block">Value Getter.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>default long</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#getTimeout()">getTimeout</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns timeout(long) value.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>default java.util.concurrent.TimeUnit</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#getTimeUnit()">getTimeUnit</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns TimeUnit.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>default &lt;T&gt;&nbsp;java.util.List&lt;java.util.concurrent.Future&lt;T&gt;&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#invokeAll(java.util.concurrent.ExecutorService,java.util.Collection)">invokeAll</a></span>&#8203;(java.util.concurrent.ExecutorService&nbsp;executorService,
java.util.Collection&lt;? extends java.util.concurrent.Callable&lt;T&gt;&gt;&nbsp;tasks)</code></th>
<td class="col-last">
<div class="block">This calls ExecutorService#invokeAll(Collection, long, TimeUnit).</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>default &lt;T&gt;&nbsp;T</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#invokeAny(java.util.concurrent.ExecutorService,java.util.Collection)">invokeAny</a></span>&#8203;(java.util.concurrent.ExecutorService&nbsp;executorService,
java.util.Collection&lt;? extends java.util.concurrent.Callable&lt;T&gt;&gt;&nbsp;tasks)</code></th>
<td class="col-last">
<div class="block">This calls ExecutorService#invokeAny(Collection, long, TimeUnit).</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#join(java.lang.Thread)">join</a></span>&#8203;(java.lang.Thread&nbsp;thread)</code></th>
<td class="col-last">
<div class="block">This calls TimeUnit#timedJoin(Thread, long).</div>
</td>
</tr>
<tr class="alt-color" id="i10">
<td class="col-first"><code>default boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#lockTryLock(java.util.concurrent.locks.Lock)">lockTryLock</a></span>&#8203;(java.util.concurrent.locks.Lock&nbsp;lock)</code></th>
<td class="col-last">
<div class="block">This calls Lock#tryLock(long, TimeUnit).</div>
</td>
</tr>
<tr class="row-color" id="i11">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#sleep()">sleep</a></span>()</code></th>
<td class="col-last">
<div class="block">This calls TimeUnit#sleep(long).</div>
</td>
</tr>
<tr class="alt-color" id="i12">
<td class="col-first"><code>default void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#wait(java.lang.Object)">wait</a></span>&#8203;(java.lang.Object&nbsp;sync)</code></th>
<td class="col-last">
<div class="block">This calls TimeUnit#timedWait(Object, long).</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="get()">
<h3>get</h3>
<div class="member-signature"><span class="return-type"><a href="TimeoutAndUnit.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutAndUnit</a></span>&nbsp;<span class="member-name">get</span>()</div>
<div class="block">Value Getter.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTimeout()">
<h3>getTimeout</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="member-name">getTimeout</span>()</div>
<div class="block">Returns timeout(long) value.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>timeout(long) value</dd>
<dt>関連項目:</dt>
<dd><a href="TimeoutAndUnit.html#timeout()"><code>TimeoutAndUnit.timeout()</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTimeUnit()">
<h3>getTimeUnit</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">java.util.concurrent.TimeUnit</span>&nbsp;<span class="member-name">getTimeUnit</span>()</div>
<div class="block">Returns TimeUnit.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>TimeUnit</dd>
<dt>関連項目:</dt>
<dd><a href="TimeoutAndUnit.html#unit()"><code>TimeoutAndUnit.unit()</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sleep()">
<h3>sleep</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">sleep</span>()
            throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">This calls TimeUnit#sleep(long).</div>
<dl class="notes">
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while sleeping</dd>
<dt>関連項目:</dt>
<dd><code>TimeUnit.sleep(long)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="join(java.lang.Thread)">
<h3>join</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">join</span>&#8203;(<span class="parameters">java.lang.Thread&nbsp;thread)</span>
           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">This calls TimeUnit#timedJoin(Thread, long).</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>thread</code> - the Thread</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>TimeUnit.timedJoin(Thread, long)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="wait(java.lang.Object)">
<h3>wait</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">wait</span>&#8203;(<span class="parameters">java.lang.Object&nbsp;sync)</span>
           throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">This calls TimeUnit#timedWait(Object, long).</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>sync</code> - the object to wait on</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>TimeUnit.timedWait(Object, long)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="blockingQueuePoll(java.util.concurrent.BlockingQueue)">
<h3>blockingQueuePoll</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="member-name">blockingQueuePoll</span>&#8203;(<span class="parameters">java.util.concurrent.BlockingQueue&lt;T&gt;&nbsp;queue)</span>
                         throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">This calls BlockingQueue#poll(long, TimeUnit).</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>queue</code> - the BlockingQueue</dd>
<dt>戻り値:</dt>
<dd>poll-value</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>BlockingQueue.poll(long, TimeUnit)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="lockTryLock(java.util.concurrent.locks.Lock)">
<h3>lockTryLock</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">lockTryLock</span>&#8203;(<span class="parameters">java.util.concurrent.locks.Lock&nbsp;lock)</span>
                     throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">This calls Lock#tryLock(long, TimeUnit).</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>lock</code> - the Lock</dd>
<dt>戻り値:</dt>
<dd>true if the lock was acquired and false if the waiting time elapsed before the lock was acquired</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if the current thread is interrupted while acquiring the lock (and interruption of lock acquisition is supported)</dd>
<dt>関連項目:</dt>
<dd><code>Lock.tryLock(long, TimeUnit)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="conditionAwait(java.util.concurrent.locks.Condition)">
<h3>conditionAwait</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">conditionAwait</span>&#8203;(<span class="parameters">java.util.concurrent.locks.Condition&nbsp;condition)</span>
                        throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">This calls Condition#await(long, TimeUnit).</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>condition</code> - the Condition</dd>
<dt>戻り値:</dt>
<dd>false if the waiting time detectably elapsed before return from the method, else true</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if the current thread is interrupted (and interruption of thread suspension is supported)</dd>
<dt>関連項目:</dt>
<dd><code>Condition.await(long, TimeUnit)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="futureGet(java.util.concurrent.Future)">
<h3>futureGet</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="member-name">futureGet</span>&#8203;(<span class="parameters">java.util.concurrent.Future&lt;T&gt;&nbsp;future)</span>
                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException,
java.util.concurrent.ExecutionException</span></div>
<div class="block">This calls Future#get(long, TimeUnit).</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>future</code> - the Future</dd>
<dt>戻り値:</dt>
<dd>future-result</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if the current thread was interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dd><code>java.util.concurrent.ExecutionException</code> - if the computation threw an exception</dd>
<dt>関連項目:</dt>
<dd><code>Future.get(long, TimeUnit)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="invokeAll(java.util.concurrent.ExecutorService,java.util.Collection)">
<h3>invokeAll</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>
<span class="return-type">java.util.List&lt;java.util.concurrent.Future&lt;T&gt;&gt;</span>&nbsp;<span class="member-name">invokeAll</span>&#8203;(<span class="parameters">java.util.concurrent.ExecutorService&nbsp;executorService,
java.util.Collection&lt;? extends java.util.concurrent.Callable&lt;T&gt;&gt;&nbsp;tasks)</span>
                                                  throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">This calls ExecutorService#invokeAll(Collection, long, TimeUnit).</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>executorService</code> - the ExecutorService</dd>
<dd><code>tasks</code> - collection of tasks</dd>
<dt>戻り値:</dt>
<dd>Future result list</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if the current thread was interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>ExecutorService.invokeAll(Collection, long, TimeUnit)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="invokeAny(java.util.concurrent.ExecutorService,java.util.Collection)">
<h3>invokeAny</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="member-name">invokeAny</span>&#8203;(<span class="parameters">java.util.concurrent.ExecutorService&nbsp;executorService,
java.util.Collection&lt;? extends java.util.concurrent.Callable&lt;T&gt;&gt;&nbsp;tasks)</span>
                 throws <span class="exceptions">java.lang.InterruptedException,
java.util.concurrent.TimeoutException,
java.util.concurrent.ExecutionException</span></div>
<div class="block">This calls ExecutorService#invokeAny(Collection, long, TimeUnit).</div>
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>T</code> - Type</dd>
<dt>パラメータ:</dt>
<dd><code>executorService</code> - the ExecutorService</dd>
<dd><code>tasks</code> - the collection of tasks</dd>
<dt>戻り値:</dt>
<dd>Callable result</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if the current thread was interrupted while waiting</dd>
<dd><code>java.util.concurrent.TimeoutException</code> - if the wait timed out</dd>
<dd><code>java.util.concurrent.ExecutionException</code> - if the computation threw an exception</dd>
<dt>関連項目:</dt>
<dd><code>ExecutorService.invokeAny(Collection, long, TimeUnit)</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="awaitTermination(java.util.concurrent.ExecutorService)">
<h3>awaitTermination</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">awaitTermination</span>&#8203;(<span class="parameters">java.util.concurrent.ExecutorService&nbsp;executorService)</span>
                          throws <span class="exceptions">java.lang.InterruptedException</span></div>
<div class="block">This calls ExecutorService#awaitTermination(long, TimeUnit).</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>executorService</code> - the ExecutorService</dd>
<dt>戻り値:</dt>
<dd>true if this executor terminated and false if the timeout elapsed before termination</dd>
<dt>例外:</dt>
<dd><code>java.lang.InterruptedException</code> - if interrupted while waiting</dd>
<dt>関連項目:</dt>
<dd><code>ExecutorService.awaitTermination(long, TimeUnit)</code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
