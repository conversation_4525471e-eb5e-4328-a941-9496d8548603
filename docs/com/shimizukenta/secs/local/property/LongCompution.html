<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>LongCompution</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: LongCompution">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース LongCompution" class="title">インタフェース LongCompution</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="Compution.html" title="com.shimizukenta.secs.local.property内のインタフェース">Compution</a>&lt;java.lang.Number&gt;</code>, <code><a href="Gettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Gettable</a>&lt;java.lang.Number&gt;</code>, <code><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></code>, <code><a href="NumberGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberGettable</a>&lt;java.lang.Number&gt;</code>, <code><a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a>&lt;java.lang.Number&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.lang.Number&gt;</code>, <code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">LongCompution</span>
extends <a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberCompution</a></pre>
<div class="block">Long value Compution, includes Getter, Observer.
 
 <p>
 <strong>NOT</strong> includes Setter.<br/>
 </p>
 <p>
 This instance is built from other Property or Compution<br/>
 </p>
 <ul>
 <li>To get value,
 <ul>
 <li><a href="NumberGettable.html#byteValue()"><code>NumberGettable.byteValue()</code></a></li>
 <li><a href="NumberGettable.html#shortValue()"><code>NumberGettable.shortValue()</code></a></li>
 <li><a href="NumberGettable.html#intValue()"><code>NumberGettable.intValue()</code></a></li>
 <li><a href="NumberGettable.html#longValue()"><code>NumberGettable.longValue()</code></a></li>
 <li><a href="NumberGettable.html#floatValue()"><code>NumberGettable.floatValue()</code></a></li>
 <li><a href="NumberGettable.html#doubleValue()"><code>NumberGettable.doubleValue()</code></a></li>
 </ul>
 </li>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>To number-compute,
 <ul>
 <li><a href="NumberObservable.html#add(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.add(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#subtract(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.subtract(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#multiply(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.multiply(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#negate()"><code>NumberObservable.negate()</code></a></li>
 </ul>
 </li>
 <li>To comparative-compute,
 <ul>
 <li><a href="NumberObservable.html#computeIsEqualTo(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.computeIsEqualTo(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#computeIsNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.computeIsNotEqualTo(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#computeIsLessThan(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.computeIsLessThan(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#computeIsGreaterThan(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.computeIsGreaterThan(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#computeIsLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.computeIsLessThanOrEqualTo(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#computeIsGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.computeIsGreaterThanOrEqualTo(NumberObservable)</code></a></li>
 </ul>
 <li>To wait until condition is true,
 <ul>
 <li><a href="NumberObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.waitUntilEqualTo(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.waitUntilNotEqualTo(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.waitUntilLessThan(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.waitUntilGreaterThan(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.waitUntilLessThanOrEqualTo(NumberObservable)</code></a></li>
 <li><a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)"><code>NumberObservable.waitUntilGreaterThanOrEqualTo(NumberObservable)</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="NumberCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>NumberCompution</code></a>, 
<code>Long</code></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.NumberGettable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="NumberGettable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberGettable</a></h3>
<code><a href="NumberGettable.html#byteValue()">byteValue</a>, <a href="NumberGettable.html#doubleValue()">doubleValue</a>, <a href="NumberGettable.html#floatValue()">floatValue</a>, <a href="NumberGettable.html#intValue()">intValue</a>, <a href="NumberGettable.html#longValue()">longValue</a>, <a href="NumberGettable.html#shortValue()">shortValue</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.NumberObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="NumberObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">NumberObservable</a></h3>
<code><a href="NumberObservable.html#add(double)">add</a>, <a href="NumberObservable.html#add(float)">add</a>, <a href="NumberObservable.html#add(int)">add</a>, <a href="NumberObservable.html#add(long)">add</a>, <a href="NumberObservable.html#add(com.shimizukenta.secs.local.property.NumberObservable)">add</a>, <a href="NumberObservable.html#computeIsEqualTo(double)">computeIsEqualTo</a>, <a href="NumberObservable.html#computeIsEqualTo(float)">computeIsEqualTo</a>, <a href="NumberObservable.html#computeIsEqualTo(int)">computeIsEqualTo</a>, <a href="NumberObservable.html#computeIsEqualTo(long)">computeIsEqualTo</a>, <a href="NumberObservable.html#computeIsEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">computeIsEqualTo</a>, <a href="NumberObservable.html#computeIsEqualToZero()">computeIsEqualToZero</a>, <a href="NumberObservable.html#computeIsGreaterThan(double)">computeIsGreaterThan</a>, <a href="NumberObservable.html#computeIsGreaterThan(float)">computeIsGreaterThan</a>, <a href="NumberObservable.html#computeIsGreaterThan(int)">computeIsGreaterThan</a>, <a href="NumberObservable.html#computeIsGreaterThan(long)">computeIsGreaterThan</a>, <a href="NumberObservable.html#computeIsGreaterThan(com.shimizukenta.secs.local.property.NumberObservable)">computeIsGreaterThan</a>, <a href="NumberObservable.html#computeIsGreaterThanOrEqualTo(double)">computeIsGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsGreaterThanOrEqualTo(float)">computeIsGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsGreaterThanOrEqualTo(int)">computeIsGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsGreaterThanOrEqualTo(long)">computeIsGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">computeIsGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsGreaterThanOrEqualToZero()">computeIsGreaterThanOrEqualToZero</a>, <a href="NumberObservable.html#computeIsGreaterThanZero()">computeIsGreaterThanZero</a>, <a href="NumberObservable.html#computeIsLessThan(double)">computeIsLessThan</a>, <a href="NumberObservable.html#computeIsLessThan(float)">computeIsLessThan</a>, <a href="NumberObservable.html#computeIsLessThan(int)">computeIsLessThan</a>, <a href="NumberObservable.html#computeIsLessThan(long)">computeIsLessThan</a>, <a href="NumberObservable.html#computeIsLessThan(com.shimizukenta.secs.local.property.NumberObservable)">computeIsLessThan</a>, <a href="NumberObservable.html#computeIsLessThanOrEqualTo(double)">computeIsLessThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsLessThanOrEqualTo(float)">computeIsLessThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsLessThanOrEqualTo(int)">computeIsLessThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsLessThanOrEqualTo(long)">computeIsLessThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">computeIsLessThanOrEqualTo</a>, <a href="NumberObservable.html#computeIsLessThanOrEqualToZero()">computeIsLessThanOrEqualToZero</a>, <a href="NumberObservable.html#computeIsLessThanZero()">computeIsLessThanZero</a>, <a href="NumberObservable.html#computeIsNotEqualTo(double)">computeIsNotEqualTo</a>, <a href="NumberObservable.html#computeIsNotEqualTo(float)">computeIsNotEqualTo</a>, <a href="NumberObservable.html#computeIsNotEqualTo(int)">computeIsNotEqualTo</a>, <a href="NumberObservable.html#computeIsNotEqualTo(long)">computeIsNotEqualTo</a>, <a href="NumberObservable.html#computeIsNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">computeIsNotEqualTo</a>, <a href="NumberObservable.html#computeIsNotEqualToZero()">computeIsNotEqualToZero</a>, <a href="NumberObservable.html#isDouble()">isDouble</a>, <a href="NumberObservable.html#isFloat()">isFloat</a>, <a href="NumberObservable.html#isInteger()">isInteger</a>, <a href="NumberObservable.html#isLong()">isLong</a>, <a href="NumberObservable.html#multiply(double)">multiply</a>, <a href="NumberObservable.html#multiply(float)">multiply</a>, <a href="NumberObservable.html#multiply(int)">multiply</a>, <a href="NumberObservable.html#multiply(long)">multiply</a>, <a href="NumberObservable.html#multiply(com.shimizukenta.secs.local.property.NumberObservable)">multiply</a>, <a href="NumberObservable.html#negate()">negate</a>, <a href="NumberObservable.html#subtract(double)">subtract</a>, <a href="NumberObservable.html#subtract(float)">subtract</a>, <a href="NumberObservable.html#subtract(int)">subtract</a>, <a href="NumberObservable.html#subtract(long)">subtract</a>, <a href="NumberObservable.html#subtract(com.shimizukenta.secs.local.property.NumberObservable)">subtract</a>, <a href="NumberObservable.html#toDouble()">toDouble</a>, <a href="NumberObservable.html#toFloat()">toFloat</a>, <a href="NumberObservable.html#toInteger()">toInteger</a>, <a href="NumberObservable.html#toLong()">toLong</a>, <a href="NumberObservable.html#waitUntilEqualTo(double)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(double,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(float)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(float,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(int)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(int,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(long)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(long,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualTo</a>, <a href="NumberObservable.html#waitUntilEqualToZero()">waitUntilEqualToZero</a>, <a href="NumberObservable.html#waitUntilEqualToZero(long,java.util.concurrent.TimeUnit)">waitUntilEqualToZero</a>, <a href="NumberObservable.html#waitUntilEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilEqualToZero</a>, <a href="NumberObservable.html#waitUntilGreaterThan(double)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(double,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(float)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(float,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(int)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(int,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(long)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(long,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThan(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThan</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(double)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(double,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(float)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(float,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(int)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(int,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(long)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(long,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualToZero()">waitUntilGreaterThanOrEqualToZero</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualToZero(long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanOrEqualToZero</a>, <a href="NumberObservable.html#waitUntilGreaterThanOrEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanOrEqualToZero</a>, <a href="NumberObservable.html#waitUntilGreaterThanZero()">waitUntilGreaterThanZero</a>, <a href="NumberObservable.html#waitUntilGreaterThanZero(long,java.util.concurrent.TimeUnit)">waitUntilGreaterThanZero</a>, <a href="NumberObservable.html#waitUntilGreaterThanZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilGreaterThanZero</a>, <a href="NumberObservable.html#waitUntilLessThan(double)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(double,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(float)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(float,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(int)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(int,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(long)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(long,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThan(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThan</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(double)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(double,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(float)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(float,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(int)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(int,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(long)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(long,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualTo</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualToZero()">waitUntilLessThanOrEqualToZero</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualToZero(long,java.util.concurrent.TimeUnit)">waitUntilLessThanOrEqualToZero</a>, <a href="NumberObservable.html#waitUntilLessThanOrEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanOrEqualToZero</a>, <a href="NumberObservable.html#waitUntilLessThanZero()">waitUntilLessThanZero</a>, <a href="NumberObservable.html#waitUntilLessThanZero(long,java.util.concurrent.TimeUnit)">waitUntilLessThanZero</a>, <a href="NumberObservable.html#waitUntilLessThanZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilLessThanZero</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(double)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(double,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(double,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(float)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(float,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(float,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(int)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(int,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(int,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(long)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(long,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(long,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable,long,java.util.concurrent.TimeUnit)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualTo(com.shimizukenta.secs.local.property.NumberObservable,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualTo</a>, <a href="NumberObservable.html#waitUntilNotEqualToZero()">waitUntilNotEqualToZero</a>, <a href="NumberObservable.html#waitUntilNotEqualToZero(long,java.util.concurrent.TimeUnit)">waitUntilNotEqualToZero</a>, <a href="NumberObservable.html#waitUntilNotEqualToZero(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotEqualToZero</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
