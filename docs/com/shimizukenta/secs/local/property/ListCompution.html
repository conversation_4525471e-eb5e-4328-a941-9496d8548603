<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>ListCompution</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.local.property, interface: ListCompution">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.local.property</a></div>
<h1 title="インタフェース ListCompution" class="title">インタフェース ListCompution&lt;E&gt;</h1>
</div>
<section class="description">
<dl class="notes">
<dt>型パラメータ:</dt>
<dd><code>E</code> - Element</dd>
</dl>
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code>java.util.Collection&lt;E&gt;</code>, <code><a href="CollectionCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionCompution</a>&lt;E,&#8203;java.util.List&lt;E&gt;&gt;</code>, <code><a href="CollectionObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionObservable</a>&lt;E,&#8203;java.util.List&lt;E&gt;&gt;</code>, <code>java.lang.Iterable&lt;E&gt;</code>, <code>java.util.List&lt;E&gt;</code>, <code><a href="ListObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ListObservable</a>&lt;E&gt;</code>, <code><a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a>&lt;java.util.List&lt;E&gt;&gt;</code>, <code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">ListCompution&lt;E&gt;</span>
extends java.util.List&lt;E&gt;, <a href="CollectionCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionCompution</a>&lt;E,&#8203;java.util.List&lt;E&gt;&gt;, <a href="ListObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">ListObservable</a>&lt;E&gt;</pre>
<div class="block">List value Compution, includes List-methods, Observer.
 
 <p>
 Unsupport List-methods to change value.<br/>
 </p>
 <p>
 This interface is built from other Property or Compution.<br/>
 </p>
 <ul>
 <li>To detect value changed, <a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)"><code>Observable.addChangeListener(ChangeListener)</code></a></li>
 <li>To compute,
 <ul>
 <li><a href="CollectionObservable.html#computeIsEmpty()"><code>CollectionObservable.computeIsEmpty()</code></a></li>
 <li><a href="CollectionObservable.html#computeIsNotEmpty()"><code>CollectionObservable.computeIsNotEmpty()</code></a></li>
 <li><a href="CollectionObservable.html#computeContains(java.lang.Object)"><code>CollectionObservable.computeContains(Object)</code></a></li>
 <li><a href="CollectionObservable.html#computeNotContains(java.lang.Object)"><code>CollectionObservable.computeNotContains(Object)</code></a></li>
 <li><a href="CollectionObservable.html#computeContainsAll(java.util.Collection)"><code>CollectionObservable.computeContainsAll(Collection)</code></a></li>
 <li><a href="CollectionObservable.html#computeNotContainsAll(java.util.Collection)"><code>CollectionObservable.computeNotContainsAll(Collection)</code></a></li>
 <li><a href="CollectionObservable.html#computeSize()"><code>CollectionObservable.computeSize()</code></a>.</li>
 </ul>
 </li>
 <li>To wait until condition is true,
 <ul>
 <li><a href="CollectionObservable.html#waitUntilIsEmpty()"><code>CollectionObservable.waitUntilIsEmpty()</code></a></li>
 <li><a href="CollectionObservable.html#waitUntilIsNotEmpty()"><code>CollectionObservable.waitUntilIsNotEmpty()</code></a></li>
 <li><a href="CollectionObservable.html#waitUntilContains(java.lang.Object)"><code>CollectionObservable.waitUntilContains(Object)</code></a></li>
 <li><a href="CollectionObservable.html#waitUntilNotContains(java.lang.Object)"><code>CollectionObservable.waitUntilNotContains(Object)</code></a></li>
 <li><a href="CollectionObservable.html#waitUntilContainsAll(java.util.Collection)"><code>CollectionObservable.waitUntilContainsAll(Collection)</code></a></li>
 <li><a href="CollectionObservable.html#waitUntilNotContainsAll(java.util.Collection)"><code>CollectionObservable.waitUntilNotContainsAll(Collection)</code></a></li>
 </ul>
 </li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><code>List</code>, 
<a href="CollectionCompution.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>CollectionCompution</code></a>, 
<a href="ListObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース"><code>ListObservable</code></a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.util.Collection">インタフェースから継承されたメソッド&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.CollectionObservable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="CollectionObservable.html" title="com.shimizukenta.secs.local.property内のインタフェース">CollectionObservable</a></h3>
<code><a href="CollectionObservable.html#computeContains(java.lang.Object)">computeContains</a>, <a href="CollectionObservable.html#computeContainsAll(java.util.Collection)">computeContainsAll</a>, <a href="CollectionObservable.html#computeIsEmpty()">computeIsEmpty</a>, <a href="CollectionObservable.html#computeIsNotEmpty()">computeIsNotEmpty</a>, <a href="CollectionObservable.html#computeNotContains(java.lang.Object)">computeNotContains</a>, <a href="CollectionObservable.html#computeNotContainsAll(java.util.Collection)">computeNotContainsAll</a>, <a href="CollectionObservable.html#computeSize()">computeSize</a>, <a href="CollectionObservable.html#waitUntilContains(java.lang.Object)">waitUntilContains</a>, <a href="CollectionObservable.html#waitUntilContains(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilContains</a>, <a href="CollectionObservable.html#waitUntilContains(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContains</a>, <a href="CollectionObservable.html#waitUntilContainsAll(java.util.Collection)">waitUntilContainsAll</a>, <a href="CollectionObservable.html#waitUntilContainsAll(java.util.Collection,long,java.util.concurrent.TimeUnit)">waitUntilContainsAll</a>, <a href="CollectionObservable.html#waitUntilContainsAll(java.util.Collection,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilContainsAll</a>, <a href="CollectionObservable.html#waitUntilIsEmpty()">waitUntilIsEmpty</a>, <a href="CollectionObservable.html#waitUntilIsEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsEmpty</a>, <a href="CollectionObservable.html#waitUntilIsEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsEmpty</a>, <a href="CollectionObservable.html#waitUntilIsNotEmpty()">waitUntilIsNotEmpty</a>, <a href="CollectionObservable.html#waitUntilIsNotEmpty(long,java.util.concurrent.TimeUnit)">waitUntilIsNotEmpty</a>, <a href="CollectionObservable.html#waitUntilIsNotEmpty(com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilIsNotEmpty</a>, <a href="CollectionObservable.html#waitUntilNotContains(java.lang.Object)">waitUntilNotContains</a>, <a href="CollectionObservable.html#waitUntilNotContains(java.lang.Object,long,java.util.concurrent.TimeUnit)">waitUntilNotContains</a>, <a href="CollectionObservable.html#waitUntilNotContains(java.lang.Object,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotContains</a>, <a href="CollectionObservable.html#waitUntilNotContainsAll(java.util.Collection)">waitUntilNotContainsAll</a>, <a href="CollectionObservable.html#waitUntilNotContainsAll(java.util.Collection,long,java.util.concurrent.TimeUnit)">waitUntilNotContainsAll</a>, <a href="CollectionObservable.html#waitUntilNotContainsAll(java.util.Collection,com.shimizukenta.secs.local.property.TimeoutGettable)">waitUntilNotContainsAll</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.lang.Iterable">インタフェースから継承されたメソッド&nbsp;java.lang.Iterable</h3>
<code>forEach</code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.util.List">インタフェースから継承されたメソッド&nbsp;java.util.List</h3>
<code>add, add, addAll, addAll, clear, contains, containsAll, equals, get, hashCode, indexOf, isEmpty, iterator, lastIndexOf, listIterator, listIterator, remove, remove, removeAll, replaceAll, retainAll, set, size, sort, spliterator, subList, toArray, toArray</code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.local.property.Observable">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.local.property.<a href="Observable.html" title="com.shimizukenta.secs.local.property内のインタフェース">Observable</a></h3>
<code><a href="Observable.html#addChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">addChangeListener</a>, <a href="Observable.html#computeToString()">computeToString</a>, <a href="Observable.html#removeChangeListener(com.shimizukenta.secs.local.property.ChangeListener)">removeChangeListener</a></code></div>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li>メソッド</li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
