<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>Secs1Message</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.secs1, interface: Secs1Message">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":6,"i1":1,"i2":1,"i3":1,"i4":6,"i5":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],1:["t1","static\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.secs1</a></div>
<h1 title="インタフェース Secs1Message" class="title">インタフェース Secs1Message</h1>
</div>
<section class="description">
<dl class="notes">
<dt>すべてのスーパーインタフェース:</dt>
<dd><code><a href="../SecsMessage.html" title="com.shimizukenta.secs内のインタフェース">SecsMessage</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">Secs1Message</span>
extends <a href="../SecsMessage.html" title="com.shimizukenta.secs内のインタフェース">SecsMessage</a></pre>
<div class="block">SECS-I Message.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t1" class="table-tab" onclick="show(1);">staticメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#isValidBlocks()">isValidBlocks</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns true if All Secs1Blocks is valid.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>static <a href="Secs1Message.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1Message</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#of(byte%5B%5D)">of</a></span>&#8203;(byte[]&nbsp;header10Bytes)</code></th>
<td class="col-last">
<div class="block">Returns SECS-I Message, HEAD-ONLY Message builder.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>static <a href="Secs1Message.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1Message</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#of(byte%5B%5D,com.shimizukenta.secs.secs2.Secs2)">of</a></span>&#8203;(byte[]&nbsp;header10Bytes,
<a href="../secs2/Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a>&nbsp;body)</code></th>
<td class="col-last">
<div class="block">Returns SECS-I Message.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>static <a href="Secs1Message.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1Message</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#of(java.util.List)">of</a></span>&#8203;(java.util.List&lt;? extends <a href="Secs1MessageBlock.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1MessageBlock</a>&gt;&nbsp;blocks)</code></th>
<td class="col-last">
<div class="block">Returns Secs-I Message.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#rbit()">rbit</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Message R-Bit.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>java.util.List&lt;<a href="Secs1MessageBlock.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1MessageBlock</a>&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#toBlocks()">toBlocks</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Message blocks.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.SecsMessage">インタフェースから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../SecsMessage.html" title="com.shimizukenta.secs内のインタフェース">SecsMessage</a></h3>
<code><a href="../SecsMessage.html#deviceId()">deviceId</a>, <a href="../SecsMessage.html#getFunction()">getFunction</a>, <a href="../SecsMessage.html#getStream()">getStream</a>, <a href="../SecsMessage.html#header10Bytes()">header10Bytes</a>, <a href="../SecsMessage.html#secs2()">secs2</a>, <a href="../SecsMessage.html#sessionId()">sessionId</a>, <a href="../SecsMessage.html#toJson()">toJson</a>, <a href="../SecsMessage.html#wbit()">wbit</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="rbit()">
<h3>rbit</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">rbit</span>()</div>
<div class="block">Returns Message R-Bit.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if has r-bit</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toBlocks()">
<h3>toBlocks</h3>
<div class="member-signature"><span class="return-type">java.util.List&lt;<a href="Secs1MessageBlock.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1MessageBlock</a>&gt;</span>&nbsp;<span class="member-name">toBlocks</span>()</div>
<div class="block">Returns Message blocks.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>List of Message blocks</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isValidBlocks()">
<h3>isValidBlocks</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">isValidBlocks</span>()</div>
<div class="block">Returns true if All Secs1Blocks is valid.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>true if All Secs1Blocks is valid.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(byte[])">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="Secs1Message.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1Message</a></span>&nbsp;<span class="member-name">of</span>&#8203;(<span class="parameters">byte[]&nbsp;header10Bytes)</span></div>
<div class="block">Returns SECS-I Message, HEAD-ONLY Message builder.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>header10Bytes</code> - the HEAD-10-bytes.</dd>
<dt>戻り値:</dt>
<dd>SECS-I Message instance</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if input null</dd>
<dd><code><a href="Secs1MessageHeaderByteLengthIllegalArgumentException.html" title="com.shimizukenta.secs.secs1内のクラス">Secs1MessageHeaderByteLengthIllegalArgumentException</a></code> - if byte length is NOT equals 10</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(byte[],com.shimizukenta.secs.secs2.Secs2)">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="Secs1Message.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1Message</a></span>&nbsp;<span class="member-name">of</span>&#8203;(<span class="parameters">byte[]&nbsp;header10Bytes,
<a href="../secs2/Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a>&nbsp;body)</span></div>
<div class="block">Returns SECS-I Message.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>header10Bytes</code> - the HEAD-10-bytes.</dd>
<dd><code>body</code> - the body SECS-II</dd>
<dt>戻り値:</dt>
<dd>SECS-I Message instance</dd>
<dt>例外:</dt>
<dd><code><a href="Secs1TooBigMessageBodyException.html" title="com.shimizukenta.secs.secs1内のクラス">Secs1TooBigMessageBodyException</a></code> - if SECS-II body is too big</dd>
<dd><code>java.lang.NullPointerException</code> - if input null</dd>
<dd><code><a href="Secs1MessageHeaderByteLengthIllegalArgumentException.html" title="com.shimizukenta.secs.secs1内のクラス">Secs1MessageHeaderByteLengthIllegalArgumentException</a></code> - if byte length is NOT　equals 10</dd>
<dd><code><a href="Secs1TooBigMessageBodyException.html" title="com.shimizukenta.secs.secs1内のクラス">Secs1TooBigMessageBodyException</a></code> - if body is too big</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(java.util.List)">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="Secs1Message.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1Message</a></span>&nbsp;<span class="member-name">of</span>&#8203;(<span class="parameters">java.util.List&lt;? extends <a href="Secs1MessageBlock.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1MessageBlock</a>&gt;&nbsp;blocks)</span></div>
<div class="block">Returns Secs-I Message.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>blocks</code> - the Secs-I Message blocks</dd>
<dt>戻り値:</dt>
<dd>Secs-I Message instance</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if blocks is null.</dd>
<dd><code><a href="Secs1MessageEmptyBlockListIllegalArgumentException.html" title="com.shimizukenta.secs.secs1内のクラス">Secs1MessageEmptyBlockListIllegalArgumentException</a></code> - if blocks is empty</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
