<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>SecsLogObservable</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs, interface: SecsLogObservable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../index-all.html">索引</a></li>
<li><a href="../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs</a></div>
<h1 title="インタフェース SecsLogObservable" class="title">インタフェース SecsLogObservable</h1>
</div>
<section class="description">
<dl class="notes">
<dt>既知のサブインタフェースのリスト:</dt>
<dd><code><a href="hsmsgs/HsmsGsCommunicator.html" title="com.shimizukenta.secs.hsmsgs内のインタフェース">HsmsGsCommunicator</a></code>, <code><a href="hsms/HsmsLogObservable.html" title="com.shimizukenta.secs.hsms内のインタフェース">HsmsLogObservable</a></code>, <code><a href="hsmsss/HsmsSsCommunicator.html" title="com.shimizukenta.secs.hsmsss内のインタフェース">HsmsSsCommunicator</a></code>, <code><a href="secs1/Secs1Communicator.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1Communicator</a></code>, <code><a href="secs1/Secs1LogObservable.html" title="com.shimizukenta.secs.secs1内のインタフェース">Secs1LogObservable</a></code>, <code><a href="secs1ontcpip/Secs1OnTcpIpCommunicator.html" title="com.shimizukenta.secs.secs1ontcpip内のインタフェース">Secs1OnTcpIpCommunicator</a></code>, <code><a href="secs1ontcpip/Secs1OnTcpIpLogObservable.html" title="com.shimizukenta.secs.secs1ontcpip内のインタフェース">Secs1OnTcpIpLogObservable</a></code>, <code><a href="secs1ontcpip/Secs1OnTcpIpReceiverCommunicator.html" title="com.shimizukenta.secs.secs1ontcpip内のインタフェース">Secs1OnTcpIpReceiverCommunicator</a></code>, <code><a href="SecsCommunicator.html" title="com.shimizukenta.secs内のインタフェース">SecsCommunicator</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="type-name-label">SecsLogObservable</span></pre>
<div class="block">SecsLog Observable, add/remove listeners.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addReceiveSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addReceiveSecsMessagePassThroughLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add Receive SecsMessage pass through log listener, otherwise <code>false</code>, receive Receive SecsMessage pass through log.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addSecsLogListener(com.shimizukenta.secs.SecsLogListener)">addSecsLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsLog.html" title="com.shimizukenta.secs内のインタフェース">SecsLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add SecsLog listener success, otherwise <code>false</code>, receive all SecsLog.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addSecsThrowableLogListener(com.shimizukenta.secs.SecsLogListener)">addSecsThrowableLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsThrowableLog.html" title="com.shimizukenta.secs内のインタフェース">SecsThrowableLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add SecsThrowableLog listener success, otherwise <code>false</code>, receive SecsThrowableLog.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addSendedSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addSendedSecsMessagePassThroughLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add Sended SecsMessage pass through log listener, otherwise <code>false</code>, receive Sended SecsMessage pass through log.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addTrySendSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">addTrySendSecsMessagePassThroughLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if add TrySend SecsMessage pass through log listener success, otherwise <code>false</code>, receive Try-Send SecsMessage pass through log.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeReceiveSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeReceiveSecsMessagePassThroughLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if remove success, otherwise <code>false</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeSecsLogListener(com.shimizukenta.secs.SecsLogListener)">removeSecsLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsLog.html" title="com.shimizukenta.secs内のインタフェース">SecsLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if remove success, otherwise false.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeSecsThrowableLogListener(com.shimizukenta.secs.SecsLogListener)">removeSecsThrowableLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsThrowableLog.html" title="com.shimizukenta.secs内のインタフェース">SecsThrowableLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if remove success, otherwise <code>false</code>.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeSendedSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeSendedSecsMessagePassThroughLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>trues</code> if remove success, otherwise <code>false</code>.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeTrySendSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">removeTrySendSecsMessagePassThroughLogListener</a></span>&#8203;(<a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</code></th>
<td class="col-last">
<div class="block">Returns <code>true</code> if remove success, otherwise false.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="addSecsLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addSecsLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addSecsLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsLog.html" title="com.shimizukenta.secs内のインタフェース">SecsLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add SecsLog listener success, otherwise <code>false</code>, receive all SecsLog.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsLogListener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeSecsLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeSecsLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeSecsLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsLog.html" title="com.shimizukenta.secs内のインタフェース">SecsLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if remove success, otherwise false.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsLog listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSecsThrowableLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addSecsThrowableLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addSecsThrowableLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsThrowableLog.html" title="com.shimizukenta.secs内のインタフェース">SecsThrowableLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add SecsThrowableLog listener success, otherwise <code>false</code>, receive SecsThrowableLog.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsThrowableLog listener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeSecsThrowableLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeSecsThrowableLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeSecsThrowableLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsThrowableLog.html" title="com.shimizukenta.secs内のインタフェース">SecsThrowableLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if remove success, otherwise <code>false</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsThrowableLog listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addTrySendSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addTrySendSecsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addTrySendSecsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add TrySend SecsMessage pass through log listener success, otherwise <code>false</code>, receive Try-Send SecsMessage pass through log.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsMessage pass through listener</dd>
<dt>戻り値:</dt>
<dd>true</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeTrySendSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeTrySendSecsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeTrySendSecsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if remove success, otherwise false.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsMessage pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSendedSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addSendedSecsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addSendedSecsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add Sended SecsMessage pass through log listener, otherwise <code>false</code>, receive Sended SecsMessage pass through log.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsMessage pass through log listener</dd>
<dt>戻り値:</dt>
<dd><code>true</code> if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeSendedSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeSendedSecsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeSendedSecsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>trues</code> if remove success, otherwise <code>false</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsMessage pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addReceiveSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>addReceiveSecsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">addReceiveSecsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if add Receive SecsMessage pass through log listener, otherwise <code>false</code>, receive Receive SecsMessage pass through log.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsMessage pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeReceiveSecsMessagePassThroughLogListener(com.shimizukenta.secs.SecsLogListener)">
<h3>removeReceiveSecsMessagePassThroughLogListener</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="member-name">removeReceiveSecsMessagePassThroughLogListener</span>&#8203;(<span class="parameters"><a href="SecsLogListener.html" title="com.shimizukenta.secs内のインタフェース">SecsLogListener</a>&lt;? super <a href="SecsMessagePassThroughLog.html" title="com.shimizukenta.secs内のインタフェース">SecsMessagePassThroughLog</a>&gt;&nbsp;listener)</span></div>
<div class="block">Returns <code>true</code> if remove success, otherwise <code>false</code>.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>listener</code> - the SecsMessage pass through log listener</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../index-all.html">索引</a></li>
<li><a href="../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
