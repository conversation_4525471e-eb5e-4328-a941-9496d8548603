<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>Secs2Builder</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.secs2, interface: Secs2Builder">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":1,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6,"i47":6,"i48":6,"i49":6,"i50":6,"i51":6,"i52":6,"i53":6,"i54":6,"i55":6,"i56":6,"i57":6,"i58":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],1:["t1","static\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.secs2</a></div>
<h1 title="インタフェース Secs2Builder" class="title">インタフェース Secs2Builder</h1>
</div>
<section class="description">
<hr>
<pre>public interface <span class="type-name-label">Secs2Builder</span></pre>
<div class="block">This interface is implements of building SECS-II (SEMI-E5) Data.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t1" class="table-tab" onclick="show(1);">staticメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#ascii(java.lang.CharSequence)">ascii</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-String Data of cs.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#binary()">binary</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-Binary Empty Data.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#binary(byte...)">binary</a></span>&#8203;(byte...&nbsp;bs)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-Binary Data.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#binary(java.util.List)">binary</a></span>&#8203;(java.util.List&lt;java.lang.Byte&gt;&nbsp;bs)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-Binary Data.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#bool()">bool</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-Boolean Empty Data.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#bool(boolean...)">bool</a></span>&#8203;(boolean...&nbsp;bools)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-Boolean Data.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#bool(java.util.List)">bool</a></span>&#8203;(java.util.List&lt;java.lang.Boolean&gt;&nbsp;bools)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-Boolean Data.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#empty()">empty</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns empty SECS-II Data.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#float4()">float4</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-FLOAT4 Empty  Data.</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#float4(float...)">float4</a></span>&#8203;(float...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-FLOAT4 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i10">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#float4(java.util.List)">float4</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-FLOAT4 Data.</div>
</td>
</tr>
<tr class="row-color" id="i11">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#float8()">float8</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-FLOAT8 Empty  Data.</div>
</td>
</tr>
<tr class="alt-color" id="i12">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#float8(double...)">float8</a></span>&#8203;(double...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-FLOAT8 Data.</div>
</td>
</tr>
<tr class="row-color" id="i13">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#float8(java.util.List)">float8</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-FLOAT8 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i14">
<td class="col-first"><code>static <a href="Secs2Builder.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2Builder</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#getInstance()">getInstance</a></span>()</code></th>
<td class="col-last">
<div class="block">Secs2Builder instance getter.</div>
</td>
</tr>
<tr class="row-color" id="i15">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int1()">int1</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT1 Empty Data.</div>
</td>
</tr>
<tr class="alt-color" id="i16">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int1(int...)">int1</a></span>&#8203;(int...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT1 Data.</div>
</td>
</tr>
<tr class="row-color" id="i17">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int1(long...)">int1</a></span>&#8203;(long...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT1 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i18">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int1(java.math.BigInteger...)">int1</a></span>&#8203;(java.math.BigInteger...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT1 Data.</div>
</td>
</tr>
<tr class="row-color" id="i19">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int1(java.util.List)">int1</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT1 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i20">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int2()">int2</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT2 Empty Data.</div>
</td>
</tr>
<tr class="row-color" id="i21">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int2(int...)">int2</a></span>&#8203;(int...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT2 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i22">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int2(long...)">int2</a></span>&#8203;(long...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT2 Data.</div>
</td>
</tr>
<tr class="row-color" id="i23">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int2(java.math.BigInteger...)">int2</a></span>&#8203;(java.math.BigInteger...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT2 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i24">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int2(java.util.List)">int2</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT2 Data.</div>
</td>
</tr>
<tr class="row-color" id="i25">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int4()">int4</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT4 Empty Data.</div>
</td>
</tr>
<tr class="alt-color" id="i26">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int4(int...)">int4</a></span>&#8203;(int...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT4 Data.</div>
</td>
</tr>
<tr class="row-color" id="i27">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int4(long...)">int4</a></span>&#8203;(long...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT4 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i28">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int4(java.math.BigInteger...)">int4</a></span>&#8203;(java.math.BigInteger...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT4 Data.</div>
</td>
</tr>
<tr class="row-color" id="i29">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int4(java.util.List)">int4</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT4 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i30">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int8()">int8</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT8 Empty Data.</div>
</td>
</tr>
<tr class="row-color" id="i31">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int8(int...)">int8</a></span>&#8203;(int...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT8 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i32">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int8(long...)">int8</a></span>&#8203;(long...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT8 Data.</div>
</td>
</tr>
<tr class="row-color" id="i33">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int8(java.math.BigInteger...)">int8</a></span>&#8203;(java.math.BigInteger...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT8 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i34">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#int8(java.util.List)">int8</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-INT8 Data.</div>
</td>
</tr>
<tr class="row-color" id="i35">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#list()">list</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns empty SECS-II-List Empty Data.</div>
</td>
</tr>
<tr class="alt-color" id="i36">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#list(com.shimizukenta.secs.secs2.Secs2...)">list</a></span>&#8203;(<a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a>...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-List Data of values.</div>
</td>
</tr>
<tr class="row-color" id="i37">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#list(java.util.List)">list</a></span>&#8203;(java.util.List&lt;? extends <a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a>&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-List Data of values.</div>
</td>
</tr>
<tr class="alt-color" id="i38">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#raw(byte%5B%5D)">raw</a></span>&#8203;(byte[]&nbsp;bs)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II Data from receiving bytes data.</div>
</td>
</tr>
<tr class="row-color" id="i39">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint1()">uint1</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT1 Empty Data.</div>
</td>
</tr>
<tr class="alt-color" id="i40">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint1(int...)">uint1</a></span>&#8203;(int...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT1 Data.</div>
</td>
</tr>
<tr class="row-color" id="i41">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint1(long...)">uint1</a></span>&#8203;(long...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT1 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i42">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint1(java.math.BigInteger...)">uint1</a></span>&#8203;(java.math.BigInteger...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT1 Data.</div>
</td>
</tr>
<tr class="row-color" id="i43">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint1(java.util.List)">uint1</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT1 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i44">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint2()">uint2</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT2 Empty Data.</div>
</td>
</tr>
<tr class="row-color" id="i45">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint2(int...)">uint2</a></span>&#8203;(int...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT2 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i46">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint2(long...)">uint2</a></span>&#8203;(long...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT2 Data.</div>
</td>
</tr>
<tr class="row-color" id="i47">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint2(java.math.BigInteger...)">uint2</a></span>&#8203;(java.math.BigInteger...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT2 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i48">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint2(java.util.List)">uint2</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT2 Data.</div>
</td>
</tr>
<tr class="row-color" id="i49">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint4()">uint4</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT4 Empty Data.</div>
</td>
</tr>
<tr class="alt-color" id="i50">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint4(int...)">uint4</a></span>&#8203;(int...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT4 Data.</div>
</td>
</tr>
<tr class="row-color" id="i51">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint4(long...)">uint4</a></span>&#8203;(long...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT4 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i52">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint4(java.math.BigInteger...)">uint4</a></span>&#8203;(java.math.BigInteger...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT4 Data.</div>
</td>
</tr>
<tr class="row-color" id="i53">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint4(java.util.List)">uint4</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT4 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i54">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint8()">uint8</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT8 Empty Data.</div>
</td>
</tr>
<tr class="row-color" id="i55">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint8(int...)">uint8</a></span>&#8203;(int...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT8 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i56">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint8(long...)">uint8</a></span>&#8203;(long...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT8 Data.</div>
</td>
</tr>
<tr class="row-color" id="i57">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint8(java.math.BigInteger...)">uint8</a></span>&#8203;(java.math.BigInteger...&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT8 Data.</div>
</td>
</tr>
<tr class="alt-color" id="i58">
<td class="col-first"><code><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#uint8(java.util.List)">uint8</a></span>&#8203;(java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</code></th>
<td class="col-last">
<div class="block">Returns SECS-II-UINT8 Data.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="getInstance()">
<h3>getInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="Secs2Builder.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2Builder</a></span>&nbsp;<span class="member-name">getInstance</span>()</div>
<div class="block">Secs2Builder instance getter.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2Builder instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="empty()">
<h3>empty</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">empty</span>()</div>
<div class="block">Returns empty SECS-II Data.
 
 <p>
 Used in Header-only-SECS-Message.
 </p>
 <p>
 This instance is Singleton-pattern.
 </p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of 0 bytes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="raw(byte[])">
<h3>raw</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">raw</span>&#8203;(<span class="parameters">byte[]&nbsp;bs)</span></div>
<div class="block">Returns SECS-II Data from receiving bytes data.
 
 <p>
 Used in receiving bytes data.
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>bs</code> - bytes</dd>
<dt>戻り値:</dt>
<dd>SECS-II Data from receiving bytes data</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="list()">
<h3>list</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">list</span>()</div>
<div class="block">Returns empty SECS-II-List Empty Data.
 
 <p>
 This instance is Singleton-pattern.
 </p></div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of L[0]</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="list(com.shimizukenta.secs.secs2.Secs2...)">
<h3>list</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">list</span>&#8203;(<span class="parameters"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a>...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-List Data of values.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the Secs2 values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of List</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="list(java.util.List)">
<h3>list</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">list</span>&#8203;(<span class="parameters">java.util.List&lt;? extends <a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a>&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-List Data of values.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Secs2</dd>
<dt>戻り値:</dt>
<dd>Secs2 of List</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ascii(java.lang.CharSequence)">
<h3>ascii</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">ascii</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Returns SECS-II-String Data of cs.
 
 <p>
 Not accept <code>null</code>.<br/>
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - the CharSequence</dd>
<dt>戻り値:</dt>
<dd>Secs2 of Ascii</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="binary()">
<h3>binary</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">binary</span>()</div>
<div class="block">Returns SECS-II-Binary Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of B[0]</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="binary(byte...)">
<h3>binary</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">binary</span>&#8203;(<span class="parameters">byte...&nbsp;bs)</span></div>
<div class="block">Returns SECS-II-Binary Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>bs</code> - the bytes</dd>
<dt>戻り値:</dt>
<dd>Secs of Binary</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if bs is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="binary(java.util.List)">
<h3>binary</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">binary</span>&#8203;(<span class="parameters">java.util.List&lt;java.lang.Byte&gt;&nbsp;bs)</span></div>
<div class="block">Returns SECS-II-Binary Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>bs</code> - List of Byte</dd>
<dt>戻り値:</dt>
<dd>Secs2 of binary</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if bs is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bool()">
<h3>bool</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">bool</span>()</div>
<div class="block">Returns SECS-II-Boolean Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of BOOLEAN[0]</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bool(boolean...)">
<h3>bool</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">bool</span>&#8203;(<span class="parameters">boolean...&nbsp;bools)</span></div>
<div class="block">Returns SECS-II-Boolean Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>bools</code> - booleans</dd>
<dt>戻り値:</dt>
<dd>Secs2 of BOOLEAN</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if bools is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bool(java.util.List)">
<h3>bool</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">bool</span>&#8203;(<span class="parameters">java.util.List&lt;java.lang.Boolean&gt;&nbsp;bools)</span></div>
<div class="block">Returns SECS-II-Boolean Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>bools</code> - list of boolean</dd>
<dt>戻り値:</dt>
<dd>Secs2 of boolean</dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if bools is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int1()">
<h3>int1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int1</span>()</div>
<div class="block">Returns SECS-II-INT1 Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I1[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int1(int...)">
<h3>int1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int1</span>&#8203;(<span class="parameters">int...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT1 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the int values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I1[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int1(long...)">
<h3>int1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int1</span>&#8203;(<span class="parameters">long...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT1 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the long values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I1[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int1(java.math.BigInteger...)">
<h3>int1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int1</span>&#8203;(<span class="parameters">java.math.BigInteger...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT1 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the BigInteger values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I1[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int1(java.util.List)">
<h3>int1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int1</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT1 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Number</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I1[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int2()">
<h3>int2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int2</span>()</div>
<div class="block">Returns SECS-II-INT2 Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I2[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int2(int...)">
<h3>int2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int2</span>&#8203;(<span class="parameters">int...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT2 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the int values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I2[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int2(long...)">
<h3>int2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int2</span>&#8203;(<span class="parameters">long...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT2 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the long values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I2[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int2(java.math.BigInteger...)">
<h3>int2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int2</span>&#8203;(<span class="parameters">java.math.BigInteger...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT2 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the BigInteger values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I2[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int2(java.util.List)">
<h3>int2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int2</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT2 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I2[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int4()">
<h3>int4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int4</span>()</div>
<div class="block">Returns SECS-II-INT4 Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I4[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int4(int...)">
<h3>int4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int4</span>&#8203;(<span class="parameters">int...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the int values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int4(long...)">
<h3>int4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int4</span>&#8203;(<span class="parameters">long...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the long values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int4(java.math.BigInteger...)">
<h3>int4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int4</span>&#8203;(<span class="parameters">java.math.BigInteger...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the BigInteger values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int4(java.util.List)">
<h3>int4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int4</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int8()">
<h3>int8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int8</span>()</div>
<div class="block">Returns SECS-II-INT8 Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I8[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int8(int...)">
<h3>int8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int8</span>&#8203;(<span class="parameters">int...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the int values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int8(long...)">
<h3>int8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int8</span>&#8203;(<span class="parameters">long...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the long values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int8(java.math.BigInteger...)">
<h3>int8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int8</span>&#8203;(<span class="parameters">java.math.BigInteger...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the BigInteger values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="int8(java.util.List)">
<h3>int8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">int8</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-INT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;I8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint1()">
<h3>uint1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint1</span>()</div>
<div class="block">Returns SECS-II-UINT1 Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U1[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint1(int...)">
<h3>uint1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint1</span>&#8203;(<span class="parameters">int...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT1 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the int values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U1[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint1(long...)">
<h3>uint1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint1</span>&#8203;(<span class="parameters">long...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT1 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the long values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U1[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint1(java.math.BigInteger...)">
<h3>uint1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint1</span>&#8203;(<span class="parameters">java.math.BigInteger...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT1 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the BigInteger values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U1[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint1(java.util.List)">
<h3>uint1</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint1</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT1 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U1[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint2()">
<h3>uint2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint2</span>()</div>
<div class="block">Returns SECS-II-UINT2 Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U2[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint2(int...)">
<h3>uint2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint2</span>&#8203;(<span class="parameters">int...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT2 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the int values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U2[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint2(long...)">
<h3>uint2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint2</span>&#8203;(<span class="parameters">long...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT2 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the long values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U2[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint2(java.math.BigInteger...)">
<h3>uint2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint2</span>&#8203;(<span class="parameters">java.math.BigInteger...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT2 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the BigInteger values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U2[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint2(java.util.List)">
<h3>uint2</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint2</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT2 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U2[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint4()">
<h3>uint4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint4</span>()</div>
<div class="block">Returns SECS-II-UINT4 Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U4[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint4(int...)">
<h3>uint4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint4</span>&#8203;(<span class="parameters">int...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the int values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint4(long...)">
<h3>uint4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint4</span>&#8203;(<span class="parameters">long...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the long values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint4(java.math.BigInteger...)">
<h3>uint4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint4</span>&#8203;(<span class="parameters">java.math.BigInteger...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the BigInteger values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint4(java.util.List)">
<h3>uint4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint4</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint8()">
<h3>uint8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint8</span>()</div>
<div class="block">Returns SECS-II-UINT8 Empty Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U8[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint8(int...)">
<h3>uint8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint8</span>&#8203;(<span class="parameters">int...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the int values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint8(long...)">
<h3>uint8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint8</span>&#8203;(<span class="parameters">long...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the long values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint8(java.math.BigInteger...)">
<h3>uint8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint8</span>&#8203;(<span class="parameters">java.math.BigInteger...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the BigInteger values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uint8(java.util.List)">
<h3>uint8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">uint8</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-UINT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;U8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="float4()">
<h3>float4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">float4</span>()</div>
<div class="block">Returns SECS-II-FLOAT4 Empty  Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;F4[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="float4(float...)">
<h3>float4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">float4</span>&#8203;(<span class="parameters">float...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-FLOAT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the float values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;F4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="float4(java.util.List)">
<h3>float4</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">float4</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-FLOAT4 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;F4[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="float8()">
<h3>float8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">float8</span>()</div>
<div class="block">Returns SECS-II-FLOAT8 Empty  Data.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd><code>&lt;F8[0]&gt;</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="float8(double...)">
<h3>float8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">float8</span>&#8203;(<span class="parameters">double...&nbsp;values)</span></div>
<div class="block">Returns SECS-II-FLOAT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - the double values</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;F8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="float8(java.util.List)">
<h3>float8</h3>
<div class="member-signature"><span class="return-type"><a href="Secs2.html" title="com.shimizukenta.secs.secs2内のインタフェース">Secs2</a></span>&nbsp;<span class="member-name">float8</span>&#8203;(<span class="parameters">java.util.List&lt;? extends java.lang.Number&gt;&nbsp;values)</span></div>
<div class="block">Returns SECS-II-FLOAT8 Data.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>values</code> - list of Numbers</dd>
<dt>戻り値:</dt>
<dd>Secs2 of <code>&lt;F8[n] n...&gt;</code></dd>
<dt>例外:</dt>
<dd><code>java.lang.NullPointerException</code> - if values is null.</dd>
<dd><code><a href="Secs2LengthByteOutOfRangeException.html" title="com.shimizukenta.secs.secs2内のクラス">Secs2LengthByteOutOfRangeException</a></code> - if length-byte-size <code>&gt;0x00FFFFFF</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
