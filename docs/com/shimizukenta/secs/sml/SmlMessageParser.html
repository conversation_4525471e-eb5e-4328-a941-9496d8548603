<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>SmlMessageParser</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.sml, interface: SmlMessageParser">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":1,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],1:["t1","static\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.sml</a></div>
<h1 title="インタフェース SmlMessageParser" class="title">インタフェース SmlMessageParser</h1>
</div>
<section class="description">
<hr>
<pre>public interface <span class="type-name-label">SmlMessageParser</span></pre>
<div class="block">This instance is implementation of SML-Parse.
 
 <ul>
 <li>To get parser instance, <a href="#getInstance()"><code>getInstance()</code></a></li>
 <li>To parse SML, <a href="#parse(java.lang.CharSequence)"><code>parse(CharSequence)</code></a></li>
 </ul></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t1" class="table-tab" onclick="show(1);">staticメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>static <a href="SmlMessageParser.html" title="com.shimizukenta.secs.sml内のインタフェース">SmlMessageParser</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#getInstance()">getInstance</a></span>()</code></th>
<td class="col-last">
<div class="block">Parser instance getter.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code><a href="SmlMessage.html" title="com.shimizukenta.secs.sml内のインタフェース">SmlMessage</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#parse(java.io.Reader)">parse</a></span>&#8203;(java.io.Reader&nbsp;reader)</code></th>
<td class="col-last">
<div class="block">Parse to SML-Message from Reader.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code><a href="SmlMessage.html" title="com.shimizukenta.secs.sml内のインタフェース">SmlMessage</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#parse(java.lang.CharSequence)">parse</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Parse to SML-Message from character sequence.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code><a href="SmlMessage.html" title="com.shimizukenta.secs.sml内のインタフェース">SmlMessage</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#parse(java.nio.file.Path)">parse</a></span>&#8203;(java.nio.file.Path&nbsp;path)</code></th>
<td class="col-last">
<div class="block">Parse to SML-Message from File Path.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="parse(java.lang.CharSequence)">
<h3>parse</h3>
<div class="member-signature"><span class="return-type"><a href="SmlMessage.html" title="com.shimizukenta.secs.sml内のインタフェース">SmlMessage</a></span>&nbsp;<span class="member-name">parse</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span>
          throws <span class="exceptions"><a href="SmlParseException.html" title="com.shimizukenta.secs.sml内のクラス">SmlParseException</a></span></div>
<div class="block">Parse to SML-Message from character sequence.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - SML-Format-Character-Sequence</dd>
<dt>戻り値:</dt>
<dd>SmlMessage</dd>
<dt>例外:</dt>
<dd><code><a href="SmlParseException.html" title="com.shimizukenta.secs.sml内のクラス">SmlParseException</a></code> - if parse failed</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parse(java.io.Reader)">
<h3>parse</h3>
<div class="member-signature"><span class="return-type"><a href="SmlMessage.html" title="com.shimizukenta.secs.sml内のインタフェース">SmlMessage</a></span>&nbsp;<span class="member-name">parse</span>&#8203;(<span class="parameters">java.io.Reader&nbsp;reader)</span>
          throws <span class="exceptions"><a href="SmlParseException.html" title="com.shimizukenta.secs.sml内のクラス">SmlParseException</a>,
java.io.IOException</span></div>
<div class="block">Parse to SML-Message from Reader.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>reader</code> - SML-Format-Reader</dd>
<dt>戻り値:</dt>
<dd>SMLMessage</dd>
<dt>例外:</dt>
<dd><code><a href="SmlParseException.html" title="com.shimizukenta.secs.sml内のクラス">SmlParseException</a></code> - if parse failed</dd>
<dd><code>java.io.IOException</code> - if IO failed</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parse(java.nio.file.Path)">
<h3>parse</h3>
<div class="member-signature"><span class="return-type"><a href="SmlMessage.html" title="com.shimizukenta.secs.sml内のインタフェース">SmlMessage</a></span>&nbsp;<span class="member-name">parse</span>&#8203;(<span class="parameters">java.nio.file.Path&nbsp;path)</span>
          throws <span class="exceptions"><a href="SmlParseException.html" title="com.shimizukenta.secs.sml内のクラス">SmlParseException</a>,
java.io.IOException</span></div>
<div class="block">Parse to SML-Message from File Path.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>path</code> - SML-Format-File-Path</dd>
<dt>戻り値:</dt>
<dd>SmlMessage</dd>
<dt>例外:</dt>
<dd><code><a href="SmlParseException.html" title="com.shimizukenta.secs.sml内のクラス">SmlParseException</a></code> - if parse failed</dd>
<dd><code>java.io.IOException</code> - if IO failed</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInstance()">
<h3>getInstance</h3>
<div class="member-signature"><span class="modifiers">static</span>&nbsp;<span class="return-type"><a href="SmlMessageParser.html" title="com.shimizukenta.secs.sml内のインタフェース">SmlMessageParser</a></span>&nbsp;<span class="member-name">getInstance</span>()</div>
<div class="block">Parser instance getter.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>parser</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
