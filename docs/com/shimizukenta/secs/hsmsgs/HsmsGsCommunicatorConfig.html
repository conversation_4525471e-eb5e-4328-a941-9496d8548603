<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>HsmsGsCommunicatorConfig</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.hsmsgs, class: HsmsGsCommunicatorConfig">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],8:["t4","concrete\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">コンストラクタ</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">コンストラクタ</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.hsmsgs</a></div>
<h1 title="クラス HsmsGsCommunicatorConfig" class="title">クラス HsmsGsCommunicatorConfig</h1>
</div>
<div class="inheritance" title="継承ツリー">java.lang.Object
<div class="inheritance"><a href="../AbstractSecsCommunicatorConfig.html" title="com.shimizukenta.secs内のクラス">com.shimizukenta.secs.AbstractSecsCommunicatorConfig</a>
<div class="inheritance"><a href="../hsms/AbstractHsmsCommunicatorConfig.html" title="com.shimizukenta.secs.hsms内のクラス">com.shimizukenta.secs.hsms.AbstractHsmsCommunicatorConfig</a>
<div class="inheritance">com.shimizukenta.secs.hsmsgs.HsmsGsCommunicatorConfig</div>
</div>
</div>
</div>
<section class="description">
<dl class="notes">
<dt>すべての実装されたインタフェース:</dt>
<dd><code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public class <span class="type-name-label">HsmsGsCommunicatorConfig</span>
extends <a href="../hsms/AbstractHsmsCommunicatorConfig.html" title="com.shimizukenta.secs.hsms内のクラス">AbstractHsmsCommunicatorConfig</a></pre>
<div class="block">This class is config of HSMS-GS-Communicator.
 
 <ul>
 <li>To add SESSION-ID, <a href="#addSessionId(int)"><code>addSessionId(int)</code></a>.</li>
 <li>To set Connect or Bind SocketAddress, <a href="#socketAddress(java.net.SocketAddress)"><code>socketAddress(SocketAddress)</code></a>}.</li>
 <li>To set not try-SELECT.REQ communicator, <a href="#notTrySelectRequest()"><code>notTrySelectRequest()</code></a>.</li>
 <li>To set retry-SELECT.REQ timeout, <a href="#retrySelectRequestTimeout(float)"><code>retrySelectRequestTimeout(float)</code></a>.</li>
 </ul></div>
<dl class="notes">
<dt>関連項目:</dt>
<dd><a href="../../../../serialized-form.html#com.shimizukenta.secs.hsmsgs.HsmsGsCommunicatorConfig">直列化された形式</a></dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor.summary">
<h2>コンストラクタの概要</h2>
<div class="member-summary">
<table class="summary-table">
<caption><span>コンストラクタ</span></caption>
<thead>
<tr>
<th class="col-first" scope="col">コンストラクタ</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color">
<th class="col-constructor-name" scope="row"><code><span class="member-name-link"><a href="#%3Cinit%3E()">HsmsGsCommunicatorConfig</a></span>()</code></th>
<td class="col-last">
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t4" class="table-tab" onclick="show(8);">concreteメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#addSessionId(int)">addSessionId</a></span>&#8203;(int&nbsp;sessionId)</code></th>
<td class="col-last">
<div class="block">Returns true if add success, otherwise false.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code><a href="../local/property/BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#isTrySelectRequest()">isTrySelectRequest</a></span>()</code></th>
<td class="col-last">
<div class="block">ReadOnlyBooleanProperty of is-try-SELECT.REQ getter.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#notTrySelectRequest()">notTrySelectRequest</a></span>()</code></th>
<td class="col-last">
<div class="block">Set Not Try SELECT.REQ.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>boolean</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#removeSessionId(int)">removeSessionId</a></span>&#8203;(int&nbsp;sessionId)</code></th>
<td class="col-last">
<div class="block">Returns true if remove success, otherwise false.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code><a href="../local/property/TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#retrySelectRequestTimeout()">retrySelectRequestTimeout</a></span>()</code></th>
<td class="col-last">
<div class="block">ReadOnlyTimeProperty of SELECT.REQ Timeout getter.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#retrySelectRequestTimeout(float)">retrySelectRequestTimeout</a></span>&#8203;(float&nbsp;seconds)</code></th>
<td class="col-last">
<div class="block">SELECT.REQ retry-timeout setter.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code><a href="../local/property/SetProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">SetProperty</a>&lt;java.lang.Integer&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#sessionIds()">sessionIds</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Session-ID set.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;java.net.SocketAddress&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#socketAddress()">socketAddress</a></span>()</code></th>
<td class="col-last">
<div class="block">Connect or bind SocketAddress getter</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#socketAddress(java.net.SocketAddress)">socketAddress</a></span>&#8203;(java.net.SocketAddress&nbsp;socketAddress)</code></th>
<td class="col-last">
<div class="block">Connect or bind SocketAddress setter</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.hsms.AbstractHsmsCommunicatorConfig">クラスから継承されたメソッド&nbsp;com.shimizukenta.secs.hsms.<a href="../hsms/AbstractHsmsCommunicatorConfig.html" title="com.shimizukenta.secs.hsms内のクラス">AbstractHsmsCommunicatorConfig</a></h3>
<code><a href="../hsms/AbstractHsmsCommunicatorConfig.html#connectionMode()">connectionMode</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#connectionMode(com.shimizukenta.secs.hsms.HsmsConnectionMode)">connectionMode</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#doLinktest()">doLinktest</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#doRebindIfPassive()">doRebindIfPassive</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#linktest(float)">linktest</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#linktestTime()">linktestTime</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#notLinktest()">notLinktest</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#notRebindIfPassive()">notRebindIfPassive</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#rebindIfPassive(float)">rebindIfPassive</a>, <a href="../hsms/AbstractHsmsCommunicatorConfig.html#rebindIfPassiveTime()">rebindIfPassiveTime</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.com.shimizukenta.secs.AbstractSecsCommunicatorConfig">クラスから継承されたメソッド&nbsp;com.shimizukenta.secs.<a href="../AbstractSecsCommunicatorConfig.html" title="com.shimizukenta.secs内のクラス">AbstractSecsCommunicatorConfig</a></h3>
<code><a href="../AbstractSecsCommunicatorConfig.html#gem()">gem</a>, <a href="../AbstractSecsCommunicatorConfig.html#isEquip()">isEquip</a>, <a href="../AbstractSecsCommunicatorConfig.html#isEquip(boolean)">isEquip</a>, <a href="../AbstractSecsCommunicatorConfig.html#logSubjectHeader()">logSubjectHeader</a>, <a href="../AbstractSecsCommunicatorConfig.html#logSubjectHeader(java.lang.CharSequence)">logSubjectHeader</a>, <a href="../AbstractSecsCommunicatorConfig.html#name()">name</a>, <a href="../AbstractSecsCommunicatorConfig.html#name(java.lang.CharSequence)">name</a>, <a href="../AbstractSecsCommunicatorConfig.html#timeout()">timeout</a></code></div>
<div class="inherited-list">
<h3 id="methods.inherited.from.class.java.lang.Object">クラスから継承されたメソッド&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor.detail">
<h2>コンストラクタの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>HsmsGsCommunicatorConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="member-name">HsmsGsCommunicatorConfig</span>()</div>
<div class="block">Constructor.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="addSessionId(int)">
<h3>addSessionId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">addSessionId</span>&#8203;(<span class="parameters">int&nbsp;sessionId)</span></div>
<div class="block">Returns true if add success, otherwise false.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>sessionId</code> - Session-ID</dd>
<dt>戻り値:</dt>
<dd>true if add success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeSessionId(int)">
<h3>removeSessionId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="member-name">removeSessionId</span>&#8203;(<span class="parameters">int&nbsp;sessionId)</span></div>
<div class="block">Returns true if remove success, otherwise false.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>sessionId</code> - Session-ID</dd>
<dt>戻り値:</dt>
<dd>true if remove success, otherwise false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sessionIds()">
<h3>sessionIds</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/SetProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">SetProperty</a>&lt;java.lang.Integer&gt;</span>&nbsp;<span class="member-name">sessionIds</span>()</div>
<div class="block">Returns Session-ID set.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Session-ID set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="socketAddress(java.net.SocketAddress)">
<h3>socketAddress</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">socketAddress</span>&#8203;(<span class="parameters">java.net.SocketAddress&nbsp;socketAddress)</span></div>
<div class="block">Connect or bind SocketAddress setter</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>socketAddress</code> - PASSIVE/ACTIVE SocketAddress</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="socketAddress()">
<h3>socketAddress</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;java.net.SocketAddress&gt;</span>&nbsp;<span class="member-name">socketAddress</span>()</div>
<div class="block">Connect or bind SocketAddress getter</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>socketAddress PASSIVE/ACTIVE SocketAddress</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="notTrySelectRequest()">
<h3>notTrySelectRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">notTrySelectRequest</span>()</div>
<div class="block">Set Not Try SELECT.REQ.</div>
</section>
</li>
<li>
<section class="detail" id="isTrySelectRequest()">
<h3>isTrySelectRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/BooleanProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">BooleanProperty</a></span>&nbsp;<span class="member-name">isTrySelectRequest</span>()</div>
<div class="block">ReadOnlyBooleanProperty of is-try-SELECT.REQ getter.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>ReadOnlyProperty of is-try-SELECT.REQ</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="retrySelectRequestTimeout(float)">
<h3>retrySelectRequestTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="member-name">retrySelectRequestTimeout</span>&#8203;(<span class="parameters">float&nbsp;seconds)</span></div>
<div class="block">SELECT.REQ retry-timeout setter.</div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>seconds</code> - Timeout seconds</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="retrySelectRequestTimeout()">
<h3>retrySelectRequestTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../local/property/TimeoutProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">TimeoutProperty</a></span>&nbsp;<span class="member-name">retrySelectRequestTimeout</span>()</div>
<div class="block">ReadOnlyTimeProperty of SELECT.REQ Timeout getter.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>ReadOnlyTimeProperty</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">コンストラクタ</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">コンストラクタ</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
