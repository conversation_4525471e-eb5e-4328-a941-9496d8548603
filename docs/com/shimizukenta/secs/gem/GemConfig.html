<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (15) on Mon May 06 17:26:43 JST 2024 -->
<title>GemConfig</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-05-06">
<meta name="description" content="declaration: package: com.shimizukenta.secs.gem, interface: GemConfig">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6};
var tabs = {65535:["t0","\u3059\u3079\u3066\u306E\u30E1\u30BD\u30C3\u30C9"],2:["t2","\u30A4\u30F3\u30B9\u30BF\u30F3\u30B9\u30FB\u30E1\u30BD\u30C3\u30C9"],4:["t3","abstract\u30E1\u30BD\u30C3\u30C9"]};
var altColor = "alt-color";
var rowColor = "row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>ブラウザのJavaScriptが無効になっています。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar.top">
<div class="skip-nav"><a href="#skip.navbar.top" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.top.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip.navbar.top">
<!--   -->
</span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">パッケージ</span>&nbsp;<a href="package-summary.html">com.shimizukenta.secs.gem</a></div>
<h1 title="インタフェース GemConfig" class="title">インタフェース GemConfig</h1>
</div>
<section class="description">
<hr>
<pre>public interface <span class="type-name-label">GemConfig</span></pre>
<div class="block">This interface is GEM config.
 
 <p>
 To set Model-Number, <a href="#mdln(java.lang.CharSequence)"><code>mdln(CharSequence)</code></a><br/>
 To set Software-Revision, <a href="#softrev(java.lang.CharSequence)"><code>softrev(CharSequence)</code></a><br/>
 To set Clock-type, <a href="#clockType(com.shimizukenta.secs.gem.ClockType)"><code>clockType(ClockType)</code></a><br/>
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method.summary">
<h2>メソッドの概要</h2>
<div class="member-summary" id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" id="t0" class="active-table-tab">すべてのメソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t2" class="table-tab" onclick="show(2);">インスタンス・メソッド</button><button role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" id="t3" class="table-tab" onclick="show(4);">abstractメソッド</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<table class="summary-table" aria-labelledby="t0">
<thead>
<tr>
<th class="col-first" scope="col">修飾子とタイプ</th>
<th class="col-second" scope="col">メソッド</th>
<th class="col-last" scope="col">説明</th>
</tr>
</thead>
<tbody>
<tr class="alt-color" id="i0">
<td class="col-first"><code><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="ClockType.html" title="com.shimizukenta.secs.gem内の列挙型">ClockType</a>&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#clockType()">clockType</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Clock-type property.</div>
</td>
</tr>
<tr class="row-color" id="i1">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#clockType(com.shimizukenta.secs.gem.ClockType)">clockType</a></span>&#8203;(<a href="ClockType.html" title="com.shimizukenta.secs.gem内の列挙型">ClockType</a>&nbsp;type)</code></th>
<td class="col-last">
<div class="block">Clock-type setter.</div>
</td>
</tr>
<tr class="alt-color" id="i2">
<td class="col-first"><code><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#collectionEventIdSecs2Item()">collectionEventIdSecs2Item</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns COLLECTION-EVENT-ID Secs2Item type property.</div>
</td>
</tr>
<tr class="row-color" id="i3">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#collectionEventIdSecs2Item(com.shimizukenta.secs.secs2.Secs2Item)">collectionEventIdSecs2Item</a></span>&#8203;(<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&nbsp;item)</code></th>
<td class="col-last">
<div class="block">COLLECTION-EVENT-ID Secs2Item type setter.</div>
</td>
</tr>
<tr class="alt-color" id="i4">
<td class="col-first"><code><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#dataIdSecs2Item()">dataIdSecs2Item</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns DATA-ID Secs2Item type property.</div>
</td>
</tr>
<tr class="row-color" id="i5">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#dataIdSecs2Item(com.shimizukenta.secs.secs2.Secs2Item)">dataIdSecs2Item</a></span>&#8203;(<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&nbsp;item)</code></th>
<td class="col-last">
<div class="block">DATA-ID Secs2Item type setter.</div>
</td>
</tr>
<tr class="alt-color" id="i6">
<td class="col-first"><code><a href="../local/property/StringProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#mdln()">mdln</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Model-Number Property.</div>
</td>
</tr>
<tr class="row-color" id="i7">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#mdln(java.lang.CharSequence)">mdln</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Model-Number setter.</div>
</td>
</tr>
<tr class="alt-color" id="i8">
<td class="col-first"><code><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#reportIdSecs2Item()">reportIdSecs2Item</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns REPORT-ID Secs2Item type property</div>
</td>
</tr>
<tr class="row-color" id="i9">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#reportIdSecs2Item(com.shimizukenta.secs.secs2.Secs2Item)">reportIdSecs2Item</a></span>&#8203;(<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&nbsp;item)</code></th>
<td class="col-last">
<div class="block">REPORT-ID Secs2Item type setter.</div>
</td>
</tr>
<tr class="alt-color" id="i10">
<td class="col-first"><code><a href="../local/property/StringProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringProperty</a></code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#softrev()">softrev</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns Software-Revision Property.</div>
</td>
</tr>
<tr class="row-color" id="i11">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#softrev(java.lang.CharSequence)">softrev</a></span>&#8203;(java.lang.CharSequence&nbsp;cs)</code></th>
<td class="col-last">
<div class="block">Software-Revision setter.</div>
</td>
</tr>
<tr class="alt-color" id="i12">
<td class="col-first"><code><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&gt;</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#vIdSecs2Item()">vIdSecs2Item</a></span>()</code></th>
<td class="col-last">
<div class="block">Returns V-ID Secs2Item type property.</div>
</td>
</tr>
<tr class="row-color" id="i13">
<td class="col-first"><code>void</code></td>
<th class="col-second" scope="row"><code><span class="member-name-link"><a href="#vIdSecs2Item(com.shimizukenta.secs.secs2.Secs2Item)">vIdSecs2Item</a></span>&#8203;(<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&nbsp;item)</code></th>
<td class="col-last">
<div class="block">V-ID Secs2Item type setter.</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method.detail">
<h2>メソッドの詳細</h2>
<ul class="member-list">
<li>
<section class="detail" id="mdln(java.lang.CharSequence)">
<h3>mdln</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">mdln</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Model-Number setter.
 
 <p>
 use S1F2, S1F13, S1F14
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - MODEL-NUMBER</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="mdln()">
<h3>mdln</h3>
<div class="member-signature"><span class="return-type"><a href="../local/property/StringProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringProperty</a></span>&nbsp;<span class="member-name">mdln</span>()</div>
<div class="block">Returns Model-Number Property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Model-Number Property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="softrev(java.lang.CharSequence)">
<h3>softrev</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">softrev</span>&#8203;(<span class="parameters">java.lang.CharSequence&nbsp;cs)</span></div>
<div class="block">Software-Revision setter.
 
 <p>
 use S1F2, S1F13, S1F14
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>cs</code> - SOFTWARE-RESION</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="softrev()">
<h3>softrev</h3>
<div class="member-signature"><span class="return-type"><a href="../local/property/StringProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">StringProperty</a></span>&nbsp;<span class="member-name">softrev</span>()</div>
<div class="block">Returns Software-Revision Property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Software-Revision-property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clockType(com.shimizukenta.secs.gem.ClockType)">
<h3>clockType</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">clockType</span>&#8203;(<span class="parameters"><a href="ClockType.html" title="com.shimizukenta.secs.gem内の列挙型">ClockType</a>&nbsp;type)</span></div>
<div class="block">Clock-type setter.
 
 <p>
 use S2F18, S2F31
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>type</code> - A16 or A12</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clockType()">
<h3>clockType</h3>
<div class="member-signature"><span class="return-type"><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="ClockType.html" title="com.shimizukenta.secs.gem内の列挙型">ClockType</a>&gt;</span>&nbsp;<span class="member-name">clockType</span>()</div>
<div class="block">Returns Clock-type property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Clock-type-property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dataIdSecs2Item(com.shimizukenta.secs.secs2.Secs2Item)">
<h3>dataIdSecs2Item</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">dataIdSecs2Item</span>&#8203;(<span class="parameters"><a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&nbsp;item)</span></div>
<div class="block">DATA-ID Secs2Item type setter.
 
 <p>
 type: INT1, INT2, INT4, INT8, UINT1, UINT2, UINT4, UINT8
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>item</code> - item-type</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dataIdSecs2Item()">
<h3>dataIdSecs2Item</h3>
<div class="member-signature"><span class="return-type"><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&gt;</span>&nbsp;<span class="member-name">dataIdSecs2Item</span>()</div>
<div class="block">Returns DATA-ID Secs2Item type property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2Item property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="vIdSecs2Item(com.shimizukenta.secs.secs2.Secs2Item)">
<h3>vIdSecs2Item</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">vIdSecs2Item</span>&#8203;(<span class="parameters"><a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&nbsp;item)</span></div>
<div class="block">V-ID Secs2Item type setter.
 
 <p>
 type: INT1, INT2, INT4, INT8, UINT1, UINT2, UINT4, UINT8
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>item</code> - item-type</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="vIdSecs2Item()">
<h3>vIdSecs2Item</h3>
<div class="member-signature"><span class="return-type"><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&gt;</span>&nbsp;<span class="member-name">vIdSecs2Item</span>()</div>
<div class="block">Returns V-ID Secs2Item type property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2Item property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="reportIdSecs2Item(com.shimizukenta.secs.secs2.Secs2Item)">
<h3>reportIdSecs2Item</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">reportIdSecs2Item</span>&#8203;(<span class="parameters"><a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&nbsp;item)</span></div>
<div class="block">REPORT-ID Secs2Item type setter.
 
 <p>
 type: INT1, INT2, INT4, INT8, UINT1, UINT2, UINT4, UINT8
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>item</code> - item-type</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="reportIdSecs2Item()">
<h3>reportIdSecs2Item</h3>
<div class="member-signature"><span class="return-type"><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&gt;</span>&nbsp;<span class="member-name">reportIdSecs2Item</span>()</div>
<div class="block">Returns REPORT-ID Secs2Item type property</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2Item property</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="collectionEventIdSecs2Item(com.shimizukenta.secs.secs2.Secs2Item)">
<h3>collectionEventIdSecs2Item</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="member-name">collectionEventIdSecs2Item</span>&#8203;(<span class="parameters"><a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&nbsp;item)</span></div>
<div class="block">COLLECTION-EVENT-ID Secs2Item type setter.
 
 <p>
 type: INT1, INT2, INT4, INT8, UINT1, UINT2, UINT4, UINT8
 </p></div>
<dl class="notes">
<dt>パラメータ:</dt>
<dd><code>item</code> - item-type</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="collectionEventIdSecs2Item()">
<h3>collectionEventIdSecs2Item</h3>
<div class="member-signature"><span class="return-type"><a href="../local/property/ObjectProperty.html" title="com.shimizukenta.secs.local.property内のインタフェース">ObjectProperty</a>&lt;<a href="../secs2/Secs2Item.html" title="com.shimizukenta.secs.secs2内の列挙型">Secs2Item</a>&gt;</span>&nbsp;<span class="member-name">collectionEventIdSecs2Item</span>()</div>
<div class="block">Returns COLLECTION-EVENT-ID Secs2Item type property.</div>
<dl class="notes">
<dt>戻り値:</dt>
<dd>Secs2Item property</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottom-nav" id="navbar.bottom">
<div class="skip-nav"><a href="#skip.navbar.bottom" title="ナビゲーション・リンクをスキップ">ナビゲーション・リンクをスキップ</a></div>
<ul id="navbar.bottom.firstrow" class="nav-list" title="ナビゲーション">
<li><a href="../../../../index.html">概要</a></li>
<li><a href="package-summary.html">パッケージ</a></li>
<li class="nav-bar-cell1-rev">クラス</li>
<li><a href="package-tree.html">階層ツリー</a></li>
<li><a href="../../../../deprecated-list.html">非推奨</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html">ヘルプ</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>ネスト&nbsp;|&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.summary">メソッド</a></li>
</ul>
<ul class="sub-nav-list">
<li>詳細:&nbsp;</li>
<li>フィールド&nbsp;|&nbsp;</li>
<li>コンストラクタ&nbsp;|&nbsp;</li>
<li><a href="#method.detail">メソッド</a></li>
</ul>
</div>
</div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<span class="skip-nav" id="skip.navbar.bottom">
<!--   -->
</span></nav>
</footer>
</div>
</div>
</body>
</html>
