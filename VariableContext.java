package com.youibot.jmespath.advanced;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 变量上下文管理器
 * 支持多层级变量作用域、变量继承和动态变量计算
 */
public class VariableContext {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 变量作用域栈
    private final Deque<Map<String, Object>> scopeStack = new ArrayDeque<>();
    
    // 全局变量
    private final Map<String, Object> globalVariables = new ConcurrentHashMap<>();
    
    // 源数据引用
    private JsonNode sourceData;
    
    // 计算变量缓存
    private final Map<String, Object> computedVariableCache = new ConcurrentHashMap<>();
    
    // 变量解析模式
    private final Pattern variablePattern = Pattern.compile("\\$\\{([^}]+)\\}");
    private final Pattern functionPattern = Pattern.compile("\\$fn\\{([^}]+)\\}");
    private final Pattern expressionPattern = Pattern.compile("\\$expr\\{([^}]+)\\}");
    
    public VariableContext() {
        // 初始化全局作用域
        scopeStack.push(new HashMap<>());
    }
    
    /**
     * 设置变量
     */
    public void setVariable(String name, Object value) {
        getCurrentScope().put(name, value);
        // 清除相关的计算缓存
        clearComputedCache(name);
    }
    
    /**
     * 批量设置变量
     */
    public void setVariables(Map<String, Object> variables) {
        if (variables != null) {
            getCurrentScope().putAll(variables);
            // 清除所有计算缓存
            computedVariableCache.clear();
        }
    }
    
    /**
     * 获取变量值
     */
    public Object getVariable(String name) {
        // 1. 从当前作用域开始查找
        for (Map<String, Object> scope : scopeStack) {
            if (scope.containsKey(name)) {
                return scope.get(name);
            }
        }
        
        // 2. 查找全局变量
        if (globalVariables.containsKey(name)) {
            return globalVariables.get(name);
        }
        
        // 3. 查找计算变量
        if (computedVariableCache.containsKey(name)) {
            return computedVariableCache.get(name);
        }
        
        // 4. 尝试从源数据中提取
        return extractFromSourceData(name);
    }
    
    /**
     * 设置全局变量
     */
    public void setGlobalVariable(String name, Object value) {
        globalVariables.put(name, value);
        clearComputedCache(name);
    }
    
    /**
     * 推入新的作用域
     */
    public void pushScope() {
        scopeStack.push(new HashMap<>());
    }
    
    /**
     * 推入新的作用域并设置变量
     */
    public void pushScope(Map<String, Object> variables) {
        Map<String, Object> newScope = new HashMap<>();
        if (variables != null) {
            newScope.putAll(variables);
        }
        scopeStack.push(newScope);
    }
    
    /**
     * 弹出当前作用域
     */
    public void popScope() {
        if (scopeStack.size() > 1) { // 保留全局作用域
            scopeStack.pop();
        }
    }
    
    /**
     * 解析字符串中的变量
     */
    public String resolveVariables(String input) {
        if (input == null) {
            return null;
        }
        
        String result = input;
        
        // 1. 解析普通变量 ${variableName}
        result = resolveSimpleVariables(result);
        
        // 2. 解析函数调用 $fn{functionName(args)}
        result = resolveFunctionCalls(result);
        
        // 3. 解析表达式 $expr{expression}
        result = resolveExpressions(result);
        
        return result;
    }
    
    /**
     * 解析普通变量
     */
    private String resolveSimpleVariables(String input) {
        Matcher matcher = variablePattern.matcher(input);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = getVariable(variableName);
            String replacement = value != null ? value.toString() : "";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 解析函数调用
     */
    private String resolveFunctionCalls(String input) {
        Matcher matcher = functionPattern.matcher(input);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String functionCall = matcher.group(1);
            Object value = executeFunction(functionCall);
            String replacement = value != null ? value.toString() : "";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 解析表达式
     */
    private String resolveExpressions(String input) {
        Matcher matcher = expressionPattern.matcher(input);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String expression = matcher.group(1);
            Object value = evaluateExpression(expression);
            String replacement = value != null ? value.toString() : "";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 执行函数
     */
    private Object executeFunction(String functionCall) {
        try {
            // 解析函数名和参数
            int parenIndex = functionCall.indexOf('(');
            if (parenIndex == -1) {
                return executePredefinedFunction(functionCall, new Object[0]);
            }
            
            String functionName = functionCall.substring(0, parenIndex);
            String argsString = functionCall.substring(parenIndex + 1, functionCall.lastIndexOf(')'));
            Object[] args = parseArguments(argsString);
            
            return executePredefinedFunction(functionName, args);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 执行预定义函数
     */
    private Object executePredefinedFunction(String functionName, Object[] args) {
        switch (functionName.toLowerCase()) {
            case "now":
                return System.currentTimeMillis();
            case "uuid":
                return UUID.randomUUID().toString();
            case "concat":
                return String.join("", Arrays.stream(args).map(Object::toString).toArray(String[]::new));
            case "upper":
                return args.length > 0 ? args[0].toString().toUpperCase() : "";
            case "lower":
                return args.length > 0 ? args[0].toString().toLowerCase() : "";
            case "length":
                return args.length > 0 ? args[0].toString().length() : 0;
            case "substring":
                if (args.length >= 3) {
                    String str = args[0].toString();
                    int start = Integer.parseInt(args[1].toString());
                    int end = Integer.parseInt(args[2].toString());
                    return str.substring(start, Math.min(end, str.length()));
                }
                return "";
            case "format":
                if (args.length >= 2) {
                    String format = args[0].toString();
                    Object[] formatArgs = Arrays.copyOfRange(args, 1, args.length);
                    return String.format(format, formatArgs);
                }
                return "";
            default:
                return null;
        }
    }
    
    /**
     * 解析函数参数
     */
    private Object[] parseArguments(String argsString) {
        if (argsString.trim().isEmpty()) {
            return new Object[0];
        }
        
        List<Object> args = new ArrayList<>();
        String[] parts = argsString.split(",");
        
        for (String part : parts) {
            String trimmed = part.trim();
            
            // 移除引号
            if (trimmed.startsWith("'") && trimmed.endsWith("'")) {
                args.add(trimmed.substring(1, trimmed.length() - 1));
            } else if (trimmed.startsWith("\"") && trimmed.endsWith("\"")) {
                args.add(trimmed.substring(1, trimmed.length() - 1));
            } else {
                // 尝试解析为数字
                try {
                    if (trimmed.contains(".")) {
                        args.add(Double.parseDouble(trimmed));
                    } else {
                        args.add(Integer.parseInt(trimmed));
                    }
                } catch (NumberFormatException e) {
                    // 作为变量名处理
                    Object value = getVariable(trimmed);
                    args.add(value != null ? value : trimmed);
                }
            }
        }
        
        return args.toArray();
    }
    
    /**
     * 评估表达式
     */
    private Object evaluateExpression(String expression) {
        // 这里可以集成表达式引擎，如MVEL、SpEL等
        // 简单实现：支持基本的算术运算
        try {
            // 替换变量
            String resolvedExpression = resolveSimpleVariables(expression);
            
            // 简单的算术表达式评估
            if (resolvedExpression.matches("[0-9+\\-*/().\\s]+")) {
                return evaluateArithmeticExpression(resolvedExpression);
            }
            
            return resolvedExpression;
        } catch (Exception e) {
            return expression;
        }
    }
    
    /**
     * 评估算术表达式
     */
    private double evaluateArithmeticExpression(String expression) {
        // 简单的算术表达式评估器
        // 实际项目中建议使用专业的表达式引擎
        return 0.0;
    }
    
    /**
     * 从源数据中提取变量
     */
    private Object extractFromSourceData(String path) {
        if (sourceData == null) {
            return null;
        }
        
        try {
            // 使用JSONPath风格的路径提取
            String[] pathParts = path.split("\\.");
            JsonNode current = sourceData;
            
            for (String part : pathParts) {
                if (current == null || current.isNull()) {
                    return null;
                }
                
                if (current.isArray() && part.matches("\\d+")) {
                    int index = Integer.parseInt(part);
                    current = current.get(index);
                } else {
                    current = current.get(part);
                }
            }
            
            return convertJsonNodeToObject(current);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 转换JsonNode为Java对象
     */
    private Object convertJsonNodeToObject(JsonNode node) {
        if (node == null || node.isNull()) return null;
        if (node.isTextual()) return node.asText();
        if (node.isNumber()) return node.isInt() ? node.asInt() : node.asDouble();
        if (node.isBoolean()) return node.asBoolean();
        return node;
    }
    
    /**
     * 清除计算缓存
     */
    private void clearComputedCache(String variableName) {
        computedVariableCache.entrySet().removeIf(entry -> 
            entry.getKey().contains(variableName));
    }
    
    /**
     * 获取当前作用域
     */
    private Map<String, Object> getCurrentScope() {
        return scopeStack.peek();
    }
    
    /**
     * 设置源数据
     */
    public void setSourceData(JsonNode sourceData) {
        this.sourceData = sourceData;
        computedVariableCache.clear();
    }
    
    /**
     * 获取所有变量
     */
    public Map<String, Object> getAllVariables() {
        Map<String, Object> allVariables = new HashMap<>();
        
        // 从底层作用域开始合并
        for (Map<String, Object> scope : scopeStack) {
            allVariables.putAll(scope);
        }
        
        // 添加全局变量
        allVariables.putAll(globalVariables);
        
        return allVariables;
    }
    
    /**
     * 清除所有变量
     */
    public void clear() {
        scopeStack.clear();
        scopeStack.push(new HashMap<>());
        globalVariables.clear();
        computedVariableCache.clear();
        sourceData = null;
    }
    
    /**
     * 获取上下文统计信息
     */
    public Map<String, Object> getContextStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("scopeCount", scopeStack.size());
        stats.put("globalVariableCount", globalVariables.size());
        stats.put("computedCacheSize", computedVariableCache.size());
        stats.put("hasSourceData", sourceData != null);
        return stats;
    }
}
