package com.youibot.vehicle.scheduler.common.utils;

import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.LocalDateTime;

public class SimpleTest {

    @Test
    public  void test1() {

        String statusDataPushThread = CommonUtils.convertToKebabCase("StatusDataPushThread");
        System.out.println("statusDataPushThread = " + statusDataPushThread);
    }

    @Test
    public  void test2() throws UnsupportedEncodingException {

        String dateStr = "2024-10-03%2000:00:00";
        String decodedString = URLDecoder.decode(dateStr, "UTF-8");
        LocalDateTime localDateTime = CommonUtils.parseDateTime(decodedString);

    }
}
