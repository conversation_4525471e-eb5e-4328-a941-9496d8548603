package com.youibot.vehicle.scheduler.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 语言
 */
@AllArgsConstructor
@Getter
public enum Language {

    SIMPLE_CHINESE("zh-CN", "简体中文"),
    TRADITIONAL_CHINESE("zh-TW", "繁体中文"),
    ENGLISH("en-US", "英文");

    String type;
    String desc;

    public static Language getByLanguage(String language) {
        return Stream.of(Language.values()).filter(l -> language.contains(l.getType())).findFirst().orElse(SIMPLE_CHINESE);
    }
}
