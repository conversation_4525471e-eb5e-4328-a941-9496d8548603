package com.youibot.vehicle.scheduler.common.utils;

import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 请求上下文
 */
public class RequestContextUtils {

    public static  <T> void setAttribute(String name, T value) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            attributes.setAttribute(name, value, RequestAttributes.SCOPE_REQUEST);
        }
    }

    /**
     * 清理数据
     * @param name
     * @param <T>
     */
    public static  <T> void removeAttribute(String name) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if(attributes != null) {
            attributes.removeAttribute(name, RequestAttributes.SCOPE_REQUEST);
        }
    }

    public static  <T>  T getAttribute(String name ,  Class<T> type) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            Object value = attributes.getAttribute(name, RequestAttributes.SCOPE_REQUEST);
            if (type.isInstance(value)) {
                return type.cast(value);
            }
        }
        return null;
    }
}
