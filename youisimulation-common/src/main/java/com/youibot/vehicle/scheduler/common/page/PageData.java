package com.youibot.vehicle.scheduler.common.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页工具类
 */
@Data
@ApiModel(value = "PageData")
public class PageData<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总记录数")
    private int total;

    @ApiModelProperty(value = "列表数据")
    private List<T> list;


    @ApiModelProperty(value = "当前页码")
    private Integer page; //当前页码

    @ApiModelProperty(value = "每页显示的数量")
    private Integer limit;//每页显示的数量
    /**
     * 分页
     * @param list   列表数据
     * @param total  总记录数
     */
    public PageData(List<T> list, long total) {
        this.list = list;
        this.total = (int)total;
    }

    public PageData(Integer pageNum, Integer pageSize, List<T> list, int total) {
        this.page = pageNum;
        this.limit = pageSize;
        this.total = total;
        this.list = list;
    }
}