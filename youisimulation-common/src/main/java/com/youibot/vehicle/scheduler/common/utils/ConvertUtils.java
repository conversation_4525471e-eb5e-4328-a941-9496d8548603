package com.youibot.vehicle.scheduler.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 转换工具类
 */
public class ConvertUtils {

    private static Logger logger = LoggerFactory.getLogger(ConvertUtils.class);
    private static final Map<Class<?>, PropertyDescriptor[]> descriptorCache = new ConcurrentHashMap<>();

    public static <T> T mapToBean(Map<String, Object> map, Class<T> beanClass) {
        if (map == null) {
            return null;
        }
        T bean = null;
        try {
            bean = beanClass.getDeclaredConstructor().newInstance();
            PropertyDescriptor[] propertyDescriptors = getPropertyDescriptors(beanClass);
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                String propertyName = propertyDescriptor.getName();
                if (map.containsKey(propertyName)) {
                    Object value = map.get(propertyName);
                    propertyDescriptor.getWriteMethod().invoke(bean, value);
                }
            }
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException | NoSuchMethodException |
                 IntrospectionException e) {
            e.printStackTrace();
        }
        return bean;
    }

    private static PropertyDescriptor[] getPropertyDescriptors(Class<?> beanClass) throws  IntrospectionException {
        PropertyDescriptor[] propertyDescriptors = descriptorCache.get(beanClass);
        if (propertyDescriptors == null) {
            propertyDescriptors = Introspector.getBeanInfo(beanClass).getPropertyDescriptors();
            descriptorCache.put(beanClass, propertyDescriptors);
        }
        return propertyDescriptors;
    }



    public static <T> T sourceToTarget(Object source, Class<T> target){
        if(source == null){
            return null;
        }
        T targetObject = null;
        try {
            targetObject = target.newInstance();
            BeanUtils.copyProperties(source, targetObject);
        } catch (Exception e) {
            logger.error("convert error ", e);
        }

        return targetObject;
    }

    public static <T> List<T> sourceToTarget(Collection<?> sourceList, Class<T> target){
        if(sourceList == null){
            return null;
        }

        List targetList = new ArrayList<>(sourceList.size());
        try {
            for(Object source : sourceList){
                if (source == null) {
                    continue;
                }
                T targetObject = target.newInstance();
                BeanUtils.copyProperties(source, targetObject);
                targetList.add(targetObject);
            }
        }catch (Exception e){
            logger.error("convert error ", e);
        }

        return targetList;
    }

    /**
     * 获取真正的值
     * @param type
     * @param parameterValue
     * @return
     */
    public static Object getRealParameterValue(String type, String parameterValue) {
        if (StringUtils.isEmpty(parameterValue)) {
            return "";
        }
        if ("Double".equals(type)) {
            return Double.valueOf(parameterValue);
        } else if ("Boolean".equals(type)) {
            return Boolean.valueOf(parameterValue);
        } else if ("Float".equals(type)) {
            return Float.valueOf(parameterValue);
        } else if ("Integer".equals(type)) {
            return Integer.valueOf(parameterValue);
        } else if ("Long".equals(type)) {
            return Long.valueOf(parameterValue);
        } else if ("BigDecimal".equals(type)) {
            return new BigDecimal(parameterValue);
        }
        return parameterValue;
    }

    /**
     * 驼峰转下划线
     * @param c
     * @return
     */
    public static String camel2under(String c) {
        String separator = "_";
        c = c.replaceAll("([a-z])([A-Z])", "$1"+separator+"$2").toLowerCase();
        return c;
    }

    /**
     * 根据分隔符，将字符串分割
     * @param param
     * @param ch
     * @return
     */
    public static List<String> split(String param,String ch){
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(param)){
            return null;
        }
        List<String> strs = Arrays.stream(param.split(ch)).collect(Collectors.toList());
        return strs;
    }
}