/*
 *
 *  Copyright 2015-2017 the original author or authors.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *         http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.youibot.vehicle.scheduler.common.handler;

import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.fasterxml.classmate.TypeResolver;
import com.google.common.base.MoreObjects;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.controller.BaseController;
import com.youibot.vehicle.scheduler.common.utils.CommonUtils;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.method.HandlerMethod;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.AllowableValues;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.OperationBuilderPlugin;
import springfox.documentation.spi.service.contexts.OperationContext;
import springfox.documentation.spi.service.contexts.RequestMappingContext;
import springfox.documentation.spring.web.DescriptionResolver;
import springfox.documentation.spring.web.scanners.ApiModelReader;
import springfox.documentation.swagger.common.SwaggerPluginSupport;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

import static com.google.common.base.Strings.emptyToNull;
import static springfox.documentation.schema.Types.isBaseType;
import static springfox.documentation.swagger.common.SwaggerPluginSupport.SWAGGER_PLUGIN_ORDER;
import static springfox.documentation.swagger.common.SwaggerPluginSupport.pluginDoesApply;
import static springfox.documentation.swagger.readers.parameter.Examples.examples;
import static springfox.documentation.swagger.schema.ApiModelProperties.allowableValueFromString;
@Component
@Order(SwaggerPluginSupport.SWAGGER_PLUGIN_ORDER)
public class ApiOperationImplicitParametersReader implements OperationBuilderPlugin {

  private final DescriptionResolver descriptions;

  @Autowired
  private ApiModelReader apiModelReader;

  @Autowired
  private TypeResolver typeResolver ;
  
  private static Field cxt =  null;

  private static Field hd =  null;

  static {

    cxt =  ReflectionUtils.findField( OperationContext.class, "requestContext");
    hd =  ReflectionUtils.findField( RequestMappingContext.class, "handler");
    assert cxt != null;
    assert hd != null;
    cxt.setAccessible( true );
    hd.setAccessible( true );
  }
  @Autowired
  public ApiOperationImplicitParametersReader(DescriptionResolver descriptions) {
    this.descriptions = descriptions;
  }

  @Override
  public void apply(OperationContext context) {
    context.operationBuilder().parameters(readParameters(context));
  }

  @Override
  public boolean supports(DocumentationType delimiter) {
    return pluginDoesApply(delimiter);
  }

  private List<Parameter> readParameters(OperationContext context) {
    Optional<ApiImplicitParams> annotation = context.findAnnotation(ApiImplicitParams.class);


    List<Parameter> parameters = Lists.newArrayList();
    try {
      RequestMappingContext requestMappingContext = (RequestMappingContext) cxt.get( context);;
      RequestHandler  requestHandler =(RequestHandler) hd.get(requestMappingContext);

      HandlerMethod handlerMethod = requestHandler.getHandlerMethod();
      ApiImplicitParams implicitParams = CommonUtils.getParentMethodAnnotation(handlerMethod.getMethod(), ApiImplicitParams.class);

      if( !annotation.isPresent() && Objects.isNull( implicitParams )){
        return Lists.newArrayList();
      }
//      ApiImplicitParams methodAnnotation = handlerMethod.getMethodAnnotation(ApiImplicitParams.class);
//      System.out.println("requestMappingContext = " + requestMappingContext);
      Class<?> bean = handlerMethod.getBeanType();
      Class<?> superClassGenericType = ReflectionKit.getSuperClassGenericType(bean, BaseController.class, 2);
//      System.out.println("superClassGenericType = " + superClassGenericType);

      if( Objects.nonNull( superClassGenericType)){
        /**
         * 添加父类的注解
         */
        addParentApiImplicit(handlerMethod, parameters);

        /**
         * 添加model 的相关配置进当前 context
         */
        addPojoApiModelProperty( context, parameters, superClassGenericType);
        /**
         * 修改apiOperation 的value
         */
        changeApiOperationValue(context, requestHandler , superClassGenericType);
      }

    } catch (IllegalAccessException e) {
      throw new RuntimeException(e);
    }
    /**
     * 添加自身的注解
     */
    if (annotation.isPresent()) {
      for (ApiImplicitParam param : annotation.get().value()) {
        parameters.add(implicitParameter(descriptions, param));

      }
    }

    return parameters;
  }

  private void addParentApiImplicit(HandlerMethod handlerMethod, List<Parameter> parameters) {
    Method method = handlerMethod.getMethod();
    ApiImplicitParams parentMethodAnnotation = CommonUtils.getParentMethodAnnotation( method, ApiImplicitParams.class);
//      AnnotationUtils.findAnnotation(method, ApiImplicitParams.class) ;
//        System.out.println("parentMethodAnnotation = " + parentMethodAnnotation);
    if(Objects.nonNull( parentMethodAnnotation )){
      for (ApiImplicitParam param : parentMethodAnnotation.value()) {
        parameters.add(implicitParameter(descriptions, param));
      }
    }
  }

  /**
   * 修改@apiOperation 的value值
   * @param context
   * @param requestHandler
   * @param superClassGenericType
   */
  private static void changeApiOperationValue(OperationContext context, RequestHandler requestHandler ,  Class<?> superClassGenericType ) {
    // 获取@ApiOperation注解
    ApiOperation apiOperation = requestHandler.getHandlerMethod().getMethodAnnotation(ApiOperation.class);
    if (apiOperation != null) {
      ApiModel apiModel = superClassGenericType.getDeclaredAnnotation(ApiModel.class);
      String newValue = "";
      if(Objects.nonNull( apiModel)){
        // 动态修改value值
        newValue = apiModel.value();

      }
      String oldValue = apiOperation.value();
      newValue = StringUtils.replace( oldValue, Constant.POJO, newValue) ;

      context.operationBuilder().summary( newValue );
    }
  }

  /**
   * 将标注有@ApiImplicitParam注解的参数 添加到parameters 中
   * @param context
   * @param parameters
   * @param superClassGenericType
   */
  private void addPojoApiModelProperty(OperationContext context, List<Parameter> parameters, Class<?> superClassGenericType) {
    for (Field field : superClassGenericType.getDeclaredFields()) {
      if (field.isAnnotationPresent(ApiModelProperty.class)) {
        ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
        String typeName = field.getType().getSimpleName();
        typeName = StringUtils.lowerCase( typeName) ;
        parameters.add(new ParameterBuilder()
                .name(field.getName())
                .description(apiModelProperty.value())
                .required(apiModelProperty.required())
                .parameterType("query")  // 可以根据需要调整
                .modelRef(new ModelRef( typeName ))  // 根据字段类型调整
                .build());
      }
    }

  }

  private static ModelRef maybeGetModelRef(ApiImplicitParam param) {
    String dataType = MoreObjects.firstNonNull(emptyToNull(param.dataType()), "string");
    AllowableValues allowableValues = null;
    if (isBaseType(dataType)) {
      allowableValues = allowableValueFromString(param.allowableValues());
    }
    if (param.allowMultiple()) {
      return new ModelRef("", new ModelRef(dataType, allowableValues));
    }
    return new ModelRef(dataType, allowableValues);
  }
  static Parameter implicitParameter(DescriptionResolver descriptions, ApiImplicitParam param) {
    ModelRef modelRef = maybeGetModelRef(param);
    return new ParameterBuilder()
            .name(param.name())
            .description(descriptions.resolve(param.value()))
            .defaultValue(param.defaultValue())
            .required(param.required())
            .allowMultiple(param.allowMultiple())
            .modelRef(modelRef)
            .allowableValues(allowableValueFromString(param.allowableValues()))
            .parameterType(emptyToNull(param.paramType()))
            .parameterAccess(param.access())
            .order(SWAGGER_PLUGIN_ORDER)
            .scalarExample(param.example())
            .complexExamples(examples(param.examples()))
            .build();
  }
}
