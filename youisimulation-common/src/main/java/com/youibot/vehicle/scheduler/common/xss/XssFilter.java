package com.youibot.vehicle.scheduler.common.xss;

import com.youibot.vehicle.scheduler.common.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * XSS过滤
 */
public class XssFilter implements Filter {

	@Override
	public void init(FilterConfig config) throws ServletException {
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
		String token = CommonUtils.getRequestToken((HttpServletRequest) request);
		if(StringUtils.isNotBlank( token)){
			CommonUtils.TOKEN_THREAD_LOCAL_WRAPPER.set( token );
		}

		XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(
				(HttpServletRequest) request);
		chain.doFilter(xssRequest, response);
	}

	@Override
	public void destroy() {
	}

}