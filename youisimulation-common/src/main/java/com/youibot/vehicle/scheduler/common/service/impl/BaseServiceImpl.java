package com.youibot.vehicle.scheduler.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.google.common.collect.Maps;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.entity.QueryCol;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.BaseService;
import com.youibot.vehicle.scheduler.common.utils.CommonUtils;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

/**
 * 基础服务类，所有Service都要继承
 */
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements BaseService<T>   {
    @Autowired
    protected M baseDao;

    /**
     * 获取分页对象
     * @param params      分页查询参数
     * @param defaultOrderField  默认排序字段
     * @param isAsc              排序方式
     */
    protected IPage<T> getPage(Map<String, Object> params, String defaultOrderField, boolean isAsc) {
        //分页参数
        long curPage = 1;
        long limit = 10;

        if(params.get(Constant.PAGE) != null){
            curPage = Long.parseLong((String)params.get(Constant.PAGE));
        }
        if(params.get(Constant.LIMIT) != null){
            limit = Long.parseLong((String)params.get(Constant.LIMIT));
        }

        //分页对象
        Page<T> page = new Page<>(curPage, limit);

        //分页参数
        params.put(Constant.PAGE, page);

        //排序字段
        String orderField = (String)params.get(Constant.ORDER_FIELD);
        String order = (String)params.get(Constant.ORDER);

        //前端字段排序
        if(StringUtils.isNotBlank(orderField) && StringUtils.isNotBlank(order)){
            if(Constant.ASC.equalsIgnoreCase(order)) {
                return page.addOrder(OrderItem.asc(ConvertUtils.camel2under( orderField)));
            }else {
                return page.addOrder(OrderItem.desc(ConvertUtils.camel2under(orderField)));
            }
        }

        //没有排序字段，则不排序
        if(StringUtils.isBlank(defaultOrderField)){
            return page;
        }

        //默认排序
        if(isAsc) {
            page.addOrder(OrderItem.asc(defaultOrderField));
        }else {
            page.addOrder(OrderItem.desc(defaultOrderField));
        }

        return page;
    }

    protected Map<String, Object> removeKeyWords(Map<String, Object> params) {
        params.remove( Constant.LIMIT) ;
        params.remove( Constant.PAGE) ;
        params.remove( Constant.ORDER_FIELD) ;
        params.remove( Constant.ORDER) ;
        return params;
    }

    protected <T> PageData<T> getPageData(List<?> list, long total, Class<T> target){
        List<T> targetList = ConvertUtils.sourceToTarget(list, target);

        return new PageData<>(targetList, total);
    }

    protected <T> PageData<T> getPageData(List<?> list, long pageNum, long pageSize, long total, Class<T> target) {
        List<T> targetList = ConvertUtils.sourceToTarget(list, target);
        return new PageData<T>((int) pageNum, (int) pageSize, targetList, (int) total);
    }
    protected <T> PageData<T> getPageData(IPage page, Class<T> target){
        return getPageData(page.getRecords(), page.getTotal(), target);
    }

    public Map<String, Object> paramsToLike(Map<String, Object> params, String... likes){
        for (String like : likes){
            String val = (String)params.get(like);
            if (StringUtils.isNotBlank(val)){
                params.put(like, "%" + val + "%");
            }else {
                params.put(like, null);
            }
        }
        return params;
    }

    /**
     * <p>
     * 判断数据库操作是否成功
     * </p>
     * <p>
     * 注意！！ 该方法为 Integer 判断，不可传入 int 基本类型
     * </p>
     *
     * @param result 数据库操作返回影响条数
     * @return boolean
     */
    protected  boolean retBool(Integer result) {
        return SqlHelper.retBool(result);
    }



    /**
     * <p>
     * 批量操作 SqlSession
     * </p>
     */
    protected SqlSession sqlSessionBatch() {
        return SqlHelper.sqlSessionBatch(currentModelClass());
    }

    /**
     * 释放sqlSession
     * @param sqlSession session
     */
    protected void closeSqlSession(SqlSession sqlSession){
        SqlSessionUtils.closeSqlSession(sqlSession, GlobalConfigUtils.currentSessionFactory(currentModelClass()));
    }

    /**
     * 获取SqlStatement
     *
     * @param sqlMethod
     * @return
     */
    protected String sqlStatement(SqlMethod sqlMethod) {
        return SqlHelper.table(currentModelClass()).getSqlStatement(sqlMethod.getMethod());
    }

    @Override
    public boolean insert(T entity) {
        return retBool(baseDao.insert(entity));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertBatch(Collection<T> entityList) {
        return insertBatch(entityList, 100);
    }

    /**
     * 批量插入
     *
     * @param entityList
     * @param batchSize
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertBatch(Collection<T> entityList, int batchSize) {
        SqlSession batchSqlSession = sqlSessionBatch();
        int i = 0;
        String sqlStatement = sqlStatement(SqlMethod.INSERT_ONE);
        try {
            for (T anEntityList : entityList) {
                batchSqlSession.insert(sqlStatement, anEntityList);
                if (i >= 1 && i % batchSize == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        }finally {
            closeSqlSession(batchSqlSession);
        }
        return true;
    }

    @Override
    public boolean updateById(T entity) {
        return retBool(baseDao.updateById(entity));
    }

    @Override
    public boolean update(T entity, Wrapper<T> updateWrapper) {
        return retBool(baseDao.update(entity, updateWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(Collection<T> entityList) {
        return updateBatchById(entityList, 30);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(Collection<T> entityList, int batchSize) {
        if (CollectionUtils.isEmpty(entityList)) {
            throw new IllegalArgumentException("Error: entityList must not be empty");
        }
        SqlSession batchSqlSession = sqlSessionBatch();
        int i = 0;
        String sqlStatement = sqlStatement(SqlMethod.UPDATE_BY_ID);
        try {
            for (T anEntityList : entityList) {
                MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
                param.put(Constants.ENTITY, anEntityList);
                batchSqlSession.update(sqlStatement, param);
                if (i >= 1 && i % batchSize == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        }finally {
            closeSqlSession(batchSqlSession);
        }
        return true;
    }

    @Override
    public T selectById(Serializable id) {
        return baseDao.selectById(id);
    }

    @Override
    public boolean deleteById(Serializable id) {
        return SqlHelper.retBool(baseDao.deleteById(id));
    }

    @Override
    public boolean deleteBatchIds(Collection<? extends Serializable> idList) {
        return SqlHelper.retBool(baseDao.deleteBatchIds(idList));
    }




    @Override
    public QueryWrapper<T> getWrapper(Map<String, Object> params, QueryCol col) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        List<String> fields = getFileds();


        boolean eq = StringUtils.isBlank(col.getEqCol()) &&StringUtils.isBlank(col.getLikeCol())
                &&StringUtils.isBlank(col.getInCol()) &&StringUtils.isBlank(col.getTimeCol());
        Set<String> keySet = params.keySet();
        if (eq) {
            keySet.stream().filter(key -> fields.contains(key)).forEach(key -> {
                String val = (String) params.get(key);
                queryWrapper.eq(StringUtils.isNotBlank(val), ConvertUtils.camel2under(key), val);
            });

        }
        if (StringUtils.isNotBlank(col.getEqCol())) {
            Stream.of(col.getEqCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                queryWrapper.eq(StringUtils.isNotBlank(val), ConvertUtils.camel2under(key), val);
            });
        }

        if (StringUtils.isNotBlank(col.getLikeCol())) {
            String[] like = col.getLikeCol().split(",");
            paramsToLike(params, like);
            Stream.of(like).forEach(key -> {
                String val = (String) params.get(key);
                queryWrapper.like(StringUtils.isNotBlank(val), ConvertUtils.camel2under(key), val);
            });
        }

        if (StringUtils.isNotBlank(col.getInCol())) {
            Stream.of(col.getInCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                if (StringUtils.isNotBlank(val)) {
                    queryWrapper.in(ConvertUtils.camel2under(key), val.split(","));
                }
            });
        }

        if (StringUtils.isNotBlank(col.getTimeCol())) {
            Stream.of(col.getTimeCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                if (StringUtils.isNotBlank(val)) {
                    String[] split = val.split(",");
                    if (split.length >= 1) {
                        queryWrapper.ge( ConvertUtils.camel2under(key), CommonUtils.parseDateTime(split[0]));
                    }
                    if (split.length >= 2) {
                        queryWrapper.le(ConvertUtils.camel2under(key),CommonUtils.parseDateTime(split[1]));
                    }
                }
            });
        }

        //排序字段
        String orderField = (String)params.get(Constant.ORDER_FIELD);
        String order = (String)params.get(Constant.ORDER);

        //前端字段排序
        if(StringUtils.isNotBlank( orderField ) && StringUtils.isNotBlank(order)){
            if(Constant.ASC.equalsIgnoreCase(order)) {
                queryWrapper.orderByAsc(ConvertUtils.camel2under( orderField));
            }else {
                queryWrapper.orderByDesc( ConvertUtils.camel2under(orderField)) ;
            }
        }

        params.entrySet().removeIf(entry -> !fields.contains( entry.getKey()));
        return queryWrapper;
    }

    @Override
    public List<String> getFileds() {
        Class<?> type = ReflectionKit.getSuperClassGenericType(this.getClass(), IService.class, 0);
        ;

        List<String> fields = CommonUtils.getEntityFields(type);
        return fields;
    }

    @Override
    public  void likeWrapper(Map<String, Object> params) {
        Map<String, Object> temp = Maps.newHashMap();
        List<String> fileds = getFileds();
        fileds.retainAll( params.keySet());
        if(params.containsKey(Constant.createTime)){
            temp.put(Constant.createTime,params.get(Constant.createTime));
            fileds.remove(Constant.createTime);
        }
        if(params.containsKey(Constant.updateTime)){
            temp.put(Constant.updateTime,params.get(Constant.updateTime));
            fileds.remove(Constant.updateTime);
        }

        if(!fileds.isEmpty()){
            String like = org.apache.commons.lang3.StringUtils.join(fileds, ",");
            params.put(Constant.LIKE_COL,like);
        }
        if(!temp.isEmpty()) {
            params.put(Constant.TIME_COL, org.apache.commons.lang3.StringUtils.join(temp.keySet(), ","));
            params.putAll(temp);
        }

    }

}