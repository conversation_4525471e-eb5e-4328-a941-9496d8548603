package com.youibot.vehicle.scheduler.common.utils;

import com.youibot.vehicle.scheduler.common.exception.ErrorCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 响应数据
 */
@ApiModel(value = "Result")
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 编码：0表示成功，其他值表示失败
     */
    @ApiModelProperty(value = "编码：0表示成功，其他值表示失败")
    private int code = 0;
    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String msg = "success";
    /**
     * 响应数据
     */
    @ApiModelProperty(value = "响应数据")
    private T data;

    public Result<T> ok(T data) {
        this.setData(data);
        return this;
    }

    public boolean success(){
        return code == 0 ? true : false;
    }

    public Result<T> error() {
        this.code = ErrorCode.INTERNAL_SERVER_ERROR;
        this.msg = MessageUtils.getMessage(this.code);
        return this;
    }

    public Result<T> error(int code) {
        this.code = code;
        this.msg = MessageUtils.getMessage(this.code);
        return this;
    }

    public Result<T> error(int code, String msg) {
        this.code = code;
        this.msg = msg;
        return this;
    }

    public Result<T> error(String msg) {
        this.code = ErrorCode.INTERNAL_SERVER_ERROR;
        this.msg = msg;
        return this;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> Result<T> suc() {
        return suc(null);
    }

    public static <T> Result<T> suc(T data) {
        Result<T> result = new Result<T>();
        result.ok(data);
        return result;
    }

    public static <T> Result<T> errors(String msg) {
        Result<T> result = new Result<T>();
//        result.error(data);
        result.setCode(ErrorCode.PARAMS_GET_ERROR );
        result.setMsg( msg );
        return result;
    }

    public static <T> Result<T> fail(int code, String... params) {
        Result<T> result = new Result<T>();
        result.setCode(code);
        result.setMsg(MessageUtils.getMessage(code,params));
        return result;
    }
    public static <T> Result<T> fail( String... params) {

        return fail(ErrorCode.PARAMS_GET_ERROR, params );
    }
}
