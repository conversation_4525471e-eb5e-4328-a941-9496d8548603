package com.youibot.vehicle.scheduler.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Throwables;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.CrudService;
import com.youibot.vehicle.scheduler.common.utils.CommonUtils;
import com.youibot.vehicle.scheduler.common.utils.ExcelUtils;
import com.youibot.vehicle.scheduler.common.utils.RequestContextUtils;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.tools.picocli.CommandLine;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *  CRUD基础服务类
 */
@Slf4j
public abstract class CrudServiceImpl<M extends BaseMapper<T>,K extends Serializable, T> extends BaseServiceImpl<M, T> implements CrudService<K , T> {


    public static final String DATA = "data";

    @Override
    public PageData<T> page(Map<String, Object> params) {
        IPage<T> iPage = getPage(params, null, false);

        QueryWrapper<T> wrapper = getWrapper(params);
        IPage<T> page = baseDao.selectPage(
                iPage,
                wrapper
        );

        return getPageData(page.getRecords(),page.getCurrent(),page.getSize(), page.getTotal(), currentModelClass());
    }

    @Override
    public List<T> list(Map<String, Object> params) {
        List<T> entityList = baseDao.selectList(getWrapper(params));

        return entityList ;
    }


    @Override
    public void export(Map<String, Object> searchMap, HttpServletResponse response) throws IOException {

        try {
            String fileName = "导出-";
            Class<T> tClass = currentModelClass();
            ApiModel apiModel = tClass.getDeclaredAnnotation(ApiModel.class);
            if(Objects.nonNull( apiModel)){
                // 动态修改value值
                fileName = StringUtils.join( fileName , apiModel.description() , "--" , CommonUtils.getFormatNowFileName() );

            }else{

                fileName = StringUtils.join( fileName  ,"--", CommonUtils.getFormatNowFileName() );
            }
            ExcelUtils.exportExcelToWeb(response, fileName, fileName, exportData(searchMap), currentModelClass());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Transactional( rollbackFor = Exception.class)
    @Override
    public void importFile(MultipartFile multiPartFile) throws IOException {
        try {
            List<T> ts = importData(multiPartFile);
            if(CollectionUtils.isNotEmpty( ts)){
                boolean b = this.saveOrUpdateBatch(ts);
                log.debug("导入结果:{}", b);
                RequestContextUtils.setAttribute(DATA, ts );
            }

        } catch (Exception e) {
            log.error("导入失败:{}", Throwables.getStackTraceAsString(e));
            throw e;
        }
    }



}