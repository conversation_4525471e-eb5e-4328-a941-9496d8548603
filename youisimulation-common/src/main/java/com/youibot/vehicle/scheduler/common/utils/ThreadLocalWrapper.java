package com.youibot.vehicle.scheduler.common.utils;


import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class ThreadLocalWrapper<T> {
    // 内部包装 TransmittableThreadLocal
    private final TransmittableThreadLocal<T> transmittableThreadLocal = new TransmittableThreadLocal<>();
    // 存储线程值
    private final Map<Thread, T> threadLocalMap = new ConcurrentHashMap<>();
    private final int maxSize;

    public ThreadLocalWrapper(int maxSize) {
        this.maxSize = maxSize;
    }

    public void set(T value) {
        // 在设置值之前检查是否超出大小限制
        if (currentSize() >= maxSize) {
            cleanup();
        }
        transmittableThreadLocal.set(value);
        threadLocalMap.put(Thread.currentThread(), value);
    }

    public T get() {
        return transmittableThreadLocal.get();
    }

    public void remove() {
        transmittableThreadLocal.remove();
        threadLocalMap.remove(Thread.currentThread());
    }

    // 清理最早的一个线程数据
    private void cleanup() {
        Thread oldestThread = threadLocalMap.keySet().iterator().next();
        if (oldestThread != null) {
            threadLocalMap.remove(oldestThread);
            log.debug("ThreadLocal value for thread " + oldestThread.getName() + " has been cleaned up.");
        }
    }

    public int currentSize() {
        return threadLocalMap.size();
    }
}
