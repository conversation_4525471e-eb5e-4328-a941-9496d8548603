package com.youibot.vehicle.scheduler.common.handler;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.classmate.ResolvedType;
import com.fasterxml.classmate.TypeResolver;
import com.google.common.base.Optional;
import com.youibot.vehicle.scheduler.common.annotation.ApiGlobalModel;
import io.swagger.annotations.ApiModelProperty;
import javassist.*;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;
import javassist.bytecode.annotation.StringMemberValue;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.ParameterBuilderPlugin;
import springfox.documentation.spi.service.contexts.OperationContext;
import springfox.documentation.spi.service.contexts.ParameterContext;

import java.util.*;
import java.util.stream.Collectors;
 
/**
 * <AUTHOR>
 * @Date 2022-10-10
 * 将map入参匹配到swagger文档中
 * plugin加载顺序，默认是最后加载
 */
@Component
@Order
@AllArgsConstructor
public class ApiGlobalModelBuilder implements ParameterBuilderPlugin {
    private static final Logger logger = LoggerFactory.getLogger(ApiGlobalModelBuilder.class);
 
    private final TypeResolver typeResolver;
 
    @Override
    public void apply(ParameterContext context) {
 
        try {
            // 从方法或参数上获取指定注解的Optional
            OperationContext operationContext = context.getOperationContext();

            Optional<ApiGlobalModel> optional = operationContext.findAnnotation(ApiGlobalModel.class);
            if (!optional.isPresent()) {
 
                optional = context.resolvedMethodParameter().findAnnotation(ApiGlobalModel.class);
            }
            if (optional.isPresent()) {
                Class originClass = context.resolvedMethodParameter().getParameterType().getErasedType();
                String name = originClass.getSimpleName() + "Model" + IdUtil.objectId();
                ApiGlobalModel apiAnnotation = optional.get();
                String[] fields = apiAnnotation.value();
                String separator = apiAnnotation.separator();
                ClassPool pool = ClassPool.getDefault();
                CtClass ctClass = pool.makeClass(name);
                ctClass.setModifiers(Modifier.PUBLIC);
                //处理 javassist.NotFoundException
                pool.insertClassPath(new ClassClassPath(apiAnnotation.component()));
                CtClass globalCtClass = pool.getCtClass(apiAnnotation.component().getName());
                // 将生成的Class添加到SwaggerModels
                Set<ResolvedType> additionalModels = context.getDocumentationContext()
                        .getAdditionalModels();
                additionalModels
                        .add(typeResolver.resolve(createRefModel(fields,separator,globalCtClass,ctClass)));
                // 修改Json参数的ModelRef为动态生成的class
                boolean query = apiAnnotation.query();
                if(query){
                    context.parameterBuilder()
                            .parameterType("query")
                            .modelRef(new ModelRef(name)).name(name).description("body");
                }else{
                    context.parameterBuilder()
                            .parameterType("body")
                            .modelRef(new ModelRef(name)).name(name).description("body");
                }

            }
        } catch (Exception e) {
          logger.error("@ApiGlobalModel Error",e);
        }
    }
 
 
    @Override
    public boolean supports(DocumentationType delimiter) {
 
        return true;
    }
 
 
    /**
     * 根据fields中的值动态生成含有Swagger注解的javaBeen modelClass
     */
    private Class createRefModel(String[] fieldValues,String separator,CtClass origin,CtClass modelClass) throws NotFoundException, CannotCompileException, ClassNotFoundException {
        List<CtField> allField=getAllFields(origin);
        List<CtField> modelField;
        if (ArrayUtils.isEmpty(fieldValues)){
            modelField = allField;
        }else {
            List<String> mergeField = merge(fieldValues, separator);
            modelField = allField.stream().filter(e->mergeField.contains(e.getName())).collect(Collectors.toList());
        }
        createCtFields(modelField, modelClass);
        return modelClass.toClass();
    }
 
    public void createCtFields(List<CtField> modelField, CtClass ctClass) throws CannotCompileException, ClassNotFoundException, NotFoundException {
        for (CtField ctField : modelField) {
            CtField field = new CtField(ClassPool.getDefault().get(ctField.getType().getName()), ctField.getName(), ctClass);
            field.setModifiers(Modifier.PUBLIC);
            ApiModelProperty annotation = (ApiModelProperty) ctField.getAnnotation(ApiModelProperty.class);
            String apiModelPropertyValue = java.util.Optional.ofNullable(annotation).map(s -> s.value()).orElse("");
            //添加model属性说明
            if (StringUtils.isNotBlank(apiModelPropertyValue)) {
                ConstPool constPool = ctClass.getClassFile().getConstPool();
                AnnotationsAttribute attr = new AnnotationsAttribute(constPool, AnnotationsAttribute.visibleTag);
                Annotation ann = new Annotation(ApiModelProperty.class.getName(), constPool);
                ann.addMemberValue("value", new StringMemberValue(apiModelPropertyValue, constPool));
                attr.addAnnotation(ann);
                field.getFieldInfo().addAttribute(attr);
            }
            ctClass.addField(field);
        }
    }
 
    /**
     * 获取本类及其父类的字段属性 字段属性去重
     * @param clazz 当前类对象
     * @return 字段数组
     */
    public List<CtField> getAllFields(CtClass clazz) throws NotFoundException {
        List<CtField> fieldList = new ArrayList<>();
        while (clazz != null){
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
 
        return fieldList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<CtField>(Comparator.comparing(CtField::getName))), ArrayList::new)
        );
    }
 
    /**
     * 字符串列表 分隔符 合并
     * A{"a","b,c","d"} => B{"a","d","b","c"}
     *
     * @param arr arr
     * @return list
     */
    private List<String> merge(String[] arr, String separator) {
        List<String> tmp = new ArrayList<>();
        Arrays.stream(arr).forEach(s -> {
            if (s.contains(separator)) {
                tmp.addAll(Arrays.asList(s.split(separator)));
            } else {
                tmp.add(s);
            }
        });
        return tmp;
    }
 
}
 
 