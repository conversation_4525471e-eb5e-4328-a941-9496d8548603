package com.youibot.vehicle.scheduler.common.utils;

import com.youibot.vehicle.scheduler.common.exception.FleetException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.io.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2022/3/18 13:58
 */
public class MD5Utils {

    private static final Logger LOGGER = LoggerFactory.getLogger(MD5Utils.class);

    /**
     * md5加密
     *
     * @param data 原数据
     * @return
     */
    public static String dataEncryption(String data) {
        if (StringUtils.isEmpty(data)) {
            throw new FleetException("data is empty");
        }
        return DigestUtils.md5DigestAsHex(data.getBytes());
    }
    public static String dataEncryption(byte[] data) {
        if (data==null || data.length<=0) {
            throw new FleetException("data is empty");
        }
        return DigestUtils.md5DigestAsHex(data);
    }

    /**
     * md5加密
     *
     * @param filePath 文件路径
     * @return
     */
    public static String fileEncryption(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            throw new FleetException("file path missing");
        }
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(filePath);
            return DigestUtils.md5DigestAsHex(inputStream);
        } catch (Exception e) {
            throw new FleetException("md5 encryption fail",e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    LOGGER.error("inputStream close error", e);
                }
            }
        }
    }

    /**
     * 校验md5值是否正确
     *
     * @param data 原数据
     * @param md5  已加密数据
     * @return
     */
    public static boolean verifyMd5ByData(String data, String md5) {
        return MD5Utils.dataEncryption(data).equals(md5);
    }

    /**
     * 校验md5值是否正确
     *
     * @param filePath 文件路径
     * @param md5      已加密数据
     * @return
     */
    public static boolean verifyMd5ByFile(String filePath, String md5) {
        return MD5Utils.fileEncryption(filePath).equals(md5);
    }

    /**
     * 根据md5文件路径获取md5文件内容
     *
     * @param filePath
     * @return
     */
    public static String getMd5ByFilePath(String filePath) {
        File file = new File(filePath);
        StringBuilder sb = new StringBuilder();
        BufferedReader br = null;
        try {
            if (file.exists() && file.isFile()) {
                br = new BufferedReader(new InputStreamReader(new FileInputStream(file), "utf-8"));
                String buff;
                while ((buff = br.readLine()) != null) {
                    sb.append(buff);
                }
            }
        } catch (Exception e) {
            throw new FleetException("file read error",e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    LOGGER.error("BufferedReader close error", e);
                }
            }
        }
        return sb.toString();
    }
}
