package com.youibot.vehicle.scheduler.common.utils;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.google.common.base.Charsets;
import com.youibot.vehicle.scheduler.common.annotation.Mix;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

public class CommonUtils {


    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_M_MDD_H_HMMSS = "yyyyMMdd_HHmmss";

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);

    public final static ThreadLocalWrapper<String> TOKEN_THREAD_LOCAL_WRAPPER  = new ThreadLocalWrapper<>(30);

    public static String convertToKebabCase(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z]+)", "$1-$2").toLowerCase();
    }


    /**
     * 获得格式化的当前时间
     * @return
     */
    public static String getFormatNow( ) {

        return  getFormatDateTime( LocalDateTime.now() , FORMATTER) ;
    }

    public static String getFormatNowFileName( ) {

        return  getFormatDateTime( LocalDateTime.now() , YYYY_M_MDD_H_HMMSS) ;
    }


    /**
     * 获得格式化的日期时间
     * @param localDateTime
     * @param formatter
     * @return
     */
    public static String getFormatDateTime(  LocalDateTime localDateTime , String formatter  ) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(formatter);
        return  getFormatDateTime( localDateTime , dateTimeFormatter) ;
    }

    public static String getFormatDateTime(  LocalDateTime localDateTime , DateTimeFormatter formatter  ) {
        return  localDateTime.format( formatter) ;
    }

    /**
     * 获得实体类搜所包含的字段属性
     * @param clazz
     * @return
     */
    public static List<String> getEntityFields(Class<?> clazz) {
        List<String> fieldNames = new ArrayList<>();
        // 获取 TableInfo
        TableInfo tableInfo = TableInfoHelper.getTableInfo(clazz);
        if (tableInfo != null) {
            // 获取所有字段信息
            List<TableFieldInfo> fieldInfos = tableInfo.getFieldList();
            for (TableFieldInfo fieldInfo : fieldInfos) {
                fieldNames.add(fieldInfo.getProperty());
            }
            // 获取主键字段
            String keyProperty = tableInfo.getKeyProperty();
            if (keyProperty != null) {
                fieldNames.add(keyProperty);
            }
        }
        return fieldNames;
    }

    /**
     * 获取超类的泛型参数类型
     *
     * @param clazz 要解析的类
     * @param index 泛型参数索引，从0开始
     * @return 泛型参数的类型，如果没有找到则返回null
     */
    public static Class<?> getSuperClassGenericType(Class<?> clazz, int index) {
        // 获取带有泛型信息的超类
        Type genType = clazz.getGenericSuperclass();

        // 检查超类是否是参数化类型
        if (!(genType instanceof ParameterizedType)) {
            return null;
        }

        // 获取泛型参数数组
        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();

        // 检查索引是否有效
        if (index >= params.length || index < 0) {
            throw new RuntimeException("Index out of bounds");
        }

        // 获取泛型参数的类型
        if (!(params[index] instanceof Class)) {
            return null;
        }

        return (Class<?>) params[index];
    }

    /**
     *  获得父类同名同参数方法的注解
     * @param childMethod
     * @param annotationClass
     * @return
     * @param <T>
     */
    public static <T extends Annotation> T getParentMethodAnnotation( Method childMethod , Class<T> annotationClass) {
        try {

            Class<?> declaringClass = childMethod.getDeclaringClass();
            Method parentMethod = getParentMethod(childMethod, declaringClass);

            return parentMethod.getAnnotation(annotationClass);
        } catch (NoSuchMethodException e) {

            return null;
        }
    }

    /**
     * 获得父类同名同参数的方法
     * @param childMethod
     * @param declaringClass
     * @return
     * @throws NoSuchMethodException
     */
    public static Method getParentMethod(Method childMethod, Class<?> declaringClass) throws NoSuchMethodException {
        Method parentMethod = declaringClass.getSuperclass().getMethod(childMethod.getName(), childMethod.getParameterTypes());
        return parentMethod;
    }

    /**
     * 休眠一段时间
     * @param times
     * @param unit
     */

    public static  void silentSleep(long times , TimeUnit unit){


        if(Objects.nonNull( unit) && times >=   0){

                LockSupport.parkNanos( unit.toNanos( times )); ;

        }else{
            LockSupport.parkNanos( 0 ); ;
        }

    }


    public static  void silentSleep(long times ){

         silentSleep( times ,TimeUnit.MILLISECONDS );
    }


    /**
     * 获得类的字符串常量
     * @param interfaces
     * @return
     */
    public static List<String> getStringConstants(Class<?>... interfaces) {
        List<String> stringConstants = new ArrayList<>();

        for (Class<?> iface : interfaces) {
            if (iface.isInterface()) {
                for (Field field : iface.getDeclaredFields()) {
                    if (isStringConstant(field)) {
                        try {
                            stringConstants.add((String) field.get(null));
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException("Unable to access the constant: " + field.getName(), e);
                        }
                    }
                }
            } else {
                throw new IllegalArgumentException(iface.getName() + " is not an interface");
            }
        }

        return stringConstants;
    }

    private static boolean isStringConstant(Field field) {
        int modifiers = field.getModifiers();
        return String.class.equals(field.getType())
                && java.lang.reflect.Modifier.isPublic(modifiers)
                && java.lang.reflect.Modifier.isStatic(modifiers)
                && java.lang.reflect.Modifier.isFinal(modifiers);
    }


    public static String getMethodParameterSignature(Method method) {
        String returnType = method.getReturnType().getTypeName();
        return returnType + " " + Arrays.stream(method.getParameterTypes())
                .map(Class::getTypeName)
                .collect(Collectors.joining(", ", "(", ")"));
    }


    // Check if the method is declared in the specified class and not inherited
    public static boolean isDeclaredInClass(Method method, Class<?> clazz) {
        return method.getDeclaringClass().equals(clazz);
    }



    /**
     * 获取当前线程的堆栈跟踪字符串。
     *
     * @return 当前线程的堆栈跟踪字符串
     */
    public static String getStackTraceString() {
        // 创建一个 StringWriter 和 PrintWriter
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);

        // 将当前线程的堆栈跟踪写入 PrintWriter
        new Exception("Stack trace").printStackTrace(pw);

        // 返回堆栈跟踪的字符串
        return sw.toString();
    }


    public static   double halfRoud(double value){
       return Math.round(value * 100.0) / 100.0;
    }

    public final static String getRequestToken(HttpServletRequest httpRequest){
        //从header中获取token
        String token = httpRequest.getHeader(Constant.TOKEN_HEADER);

        //如果header中不存在token，则从参数中获取token
        if(StringUtils.isBlank(token)){
            token = httpRequest.getParameter(Constant.TOKEN_HEADER);
        }

        return token;
    }


    /**
     * 获得实体类中包含有Mix注解的类
     */
    private static final ConcurrentHashMap<Class<?>, Set<String>> mixFieldsCache = new ConcurrentHashMap<>();

    public static Set<String> getMixFields(Class<?> clazz) {
        return mixFieldsCache.computeIfAbsent(clazz,CommonUtils:: computeMixFields);
    }

    private static Set<String> computeMixFields(Class<?> clazz) {
        Set<String> mixFields = new HashSet<>();
        TableInfo tableInfo = TableInfoHelper.getTableInfo(clazz);
        if (tableInfo != null) {
            // Get all field names from TableInfo
            Set<String> fieldNames = tableInfo.getFieldList().stream()
                    .map(field -> field.getProperty())
                    .collect(Collectors.toSet());

            for (String fieldName : fieldNames) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    if (field.isAnnotationPresent(Mix.class)) {
                        mixFields.add(fieldName);
                    }
                } catch (NoSuchFieldException e) {
                    // Handle the case where field is not found
                    e.printStackTrace();
                }
            }
        }
        return mixFields;
    }

    // 定义日期时间格式
   final static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 将时间字符串转换为 LocalDateTime 对象
     * @param dateString
     * @return
     */
    public final static LocalDateTime parseDateTime(String    dateString) {

        try {
            dateString = URLDecoder.decode(dateString, Charsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {

        }
        LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);
        return dateTime;
    }

}
