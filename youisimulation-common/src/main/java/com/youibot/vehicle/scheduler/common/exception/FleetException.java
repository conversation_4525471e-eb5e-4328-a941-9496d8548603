package com.youibot.vehicle.scheduler.common.exception;


import com.youibot.vehicle.scheduler.common.utils.MessageUtils;

/**
 * 自定义异常
 *
 * <AUTHOR>
 */
public class FleetException extends RuntimeException {
	private static final long serialVersionUID = 1L;

    private int code;
	private String msg;

	public FleetException() {
		super();
	}

	public FleetException(int code) {
		this.code = code;
		this.msg = MessageUtils.getMessage(code);
	}

	public FleetException(int code, String... params) {
		this.code = code;
		this.msg = MessageUtils.getMessage(code, params);
	}

	public FleetException(int code, Throwable e) {
		super(e);
		this.code = code;
		this.msg = MessageUtils.getMessage(code);
	}

	public FleetException(int code, Throwable e, String... params) {
		super(e);
		this.code = code;
		this.msg = MessageUtils.getMessage(code, params);
	}

	public FleetException(String msg) {
		super(msg);
		this.code = ErrorCode.INTERNAL_SERVER_ERROR;
		this.msg = msg;
	}

	public FleetException(String msg, Throwable e) {
		super(msg, e);
		this.code = ErrorCode.INTERNAL_SERVER_ERROR;
		this.msg = msg;
	}

	public FleetException(String msg, Integer code, Throwable e) {
		super(msg, e);
		this.code = code;
		this.msg = msg;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

}