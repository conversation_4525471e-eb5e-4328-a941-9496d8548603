package com.youibot.vehicle.scheduler.common.utils;

import java.util.Random;

public class RandomUtil  extends cn.hutool.core.util.RandomUtil {

    public static String randomIp() {
        // 生成每个段的随机数 (1-254)，避免0和255
        int part1 = RandomUtil.randomInt(1, 255);
        int part2 = RandomUtil.randomInt(0, 256);
        int part3 = RandomUtil.randomInt(0, 256);
        int part4 = RandomUtil.randomInt(1, 255);
        // 组合成 IP 地址
        return part1 + "." + part2 + "." + part3 + "." + part4;
    }

    /**
     * 随机获取传入枚举类的一个随机值
     *
     * @param enumClass 枚举类的Class对象
     * @param <T> 枚举类型
     * @return 枚举类的一个随机值
     */
    public static <T extends Enum<?>> T randomEnumValue(Class<T> enumClass) {
        if (enumClass == null || !enumClass.isEnum()) {
            throw new IllegalArgumentException("The provided class must be an enum type");
        }
        T[] enumConstants = enumClass.getEnumConstants();
        int randomIndex = RandomUtil.randomInt(enumConstants.length);
        return enumConstants[randomIndex];
    }

    /**
     * 生成随机mac地址
     * @return
     */
    public static String generateRandomMac() {
        Random rand = new Random();
        byte[] macAddr = new byte[6];
        rand.nextBytes(macAddr);

        // Set the first byte to ensure it's a unicast address (LSB is 0)
        macAddr[0] = (byte) (macAddr[0] & (byte) 254);

        StringBuilder macAddress = new StringBuilder();
        for (int i = 0; i < macAddr.length; i++) {
            // Convert byte to an unsigned int, then to hexadecimal string
            macAddress.append(String.format("%02X%s", macAddr[i], (i < macAddr.length - 1) ? ":" : ""));
        }

        return macAddress.toString();
    }

}
