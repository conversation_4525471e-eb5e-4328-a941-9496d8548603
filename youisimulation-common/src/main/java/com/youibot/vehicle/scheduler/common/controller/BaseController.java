package com.youibot.vehicle.scheduler.common.controller;

import com.google.common.base.Charsets;
import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.CrudService;
import com.youibot.vehicle.scheduler.common.utils.Result;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.beans.PropertyEditorSupport;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
/**
 * 基础服务类，所有Service都要继承
 */
@Slf4j
public abstract class BaseController<M extends CrudService< K ,D>,K extends Serializable, D> {

    @Autowired
    protected M baseService;





    @GetMapping("/page")
    @ApiOperation("分页"  )
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataTypeClass = Integer.class ,example = "1", defaultValue = "1") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataTypeClass = Integer.class ,example = "20", defaultValue = "20") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataTypeClass = String.class ) ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataTypeClass = String.class ) ,
            @ApiImplicitParam(name = Constant.EQ_COL, value = "相等的字段,多个以',' 分隔 ", paramType = "query", dataTypeClass = String.class ) ,
            @ApiImplicitParam(name = Constant.LIKE_COL, value = "模糊查询的字段,多个以',' 分隔 ", paramType = "query", dataTypeClass = String.class ) ,
            @ApiImplicitParam(name = Constant.IN_COL, value = "in查询字段,多个以',' 分隔 ", paramType = "query", dataTypeClass = String.class ) ,
            @ApiImplicitParam(name = Constant.TIME_COL, value = "时间字段,多个以',' 分隔 ", paramType = "query", dataTypeClass = String.class ) ,

    })
    //@RequiresPermissions("task:node:list")
    public Result<PageData<D>> page( @ApiIgnore @RequestParam Map<String, Object> params){
        PageData<D> page = baseService.page(params);
        return Result.suc(page);
    }

    @ApiImplicitParams({})
    @GetMapping("/list")
    @ApiOperation("列表")
    //@RequiresPermissions("task:node:list")
    public Result<List<D>> list(@ApiIgnore @RequestParam Map<String, Object> params){
        List<D> list = baseService.list(params);
        return Result.suc(list);
    }



    @GetMapping("/{id}")
    @ApiOperation("详情")
    //@RequiresPermissions("task:node:list")
    public Result<D> info(@PathVariable K id){
        D dto = baseService.selectById( id);
        return Result.suc(dto);
    }

    @LogOperation("保存"  + Constant.POJO  )
    @PostMapping
    @ApiOperation("保存")
    //@RequiresPermissions("task:node:save")
    public Result<D> save(@RequestBody D dto){
        boolean res = baseService.save(dto);

        return Result.suc(dto);
    }

    @LogOperation("修改"   )
    @PutMapping
    @ApiOperation("修改")
    //@RequiresPermissions("task:node:update")
    public Result<D> update(@RequestBody D dto){
        boolean b = baseService.updateById(dto);
        log.debug("update:{}" , b );
        return new Result<D>().ok(dto);
    }

    @LogOperation("删除" +   Constant.POJO )
    @DeleteMapping
    @ApiOperation("删除")
    //@RequiresPermissions("task:node:delete")
    public Result<Boolean> delete(@RequestBody List<String> ids){
        boolean b = baseService.removeBatchByIds(ids);
        return Result.suc( b );
    }



    @ApiOperation(value = "导出"    )
    @LogOperation("导出" + Constant.POJO )
    @GetMapping(value = "/export")
    public void export(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap, HttpServletResponse response) {

        try {
            baseService.export(searchMap, response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @LogOperation("导入" )
    @ApiOperation(value = "导入")
    @PostMapping(value = "/import")
    @ResponseStatus(value = HttpStatus.OK)
    public Result importFile(@RequestParam(name = "multiPartFile") MultipartFile multiPartFile)  {

        try {
            baseService.importFile(multiPartFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return Result.suc();
    }



}