package com.youibot.vehicle.scheduler.common.service;

import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ExcelUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *  CRUD基础服务接口
 */
public interface CrudService<K extends Serializable, D> extends BaseService<D>  {

    PageData<D> page(Map<String, Object> params);

    List<D> list(Map<String, Object> params);



    default List<D> exportData(Map<String, Object> params){
        return this.list(params);
    };
    default List<D> importData(MultipartFile multiPartFile) throws IOException {

        List<D> ts = ExcelUtils.importExcel(multiPartFile.getInputStream(),  (Class<D>) ReflectionKit.getSuperClassGenericType(this.getClass(), BaseService.class,0 ));

        return ts;
    };

    void export(Map<String, Object> searchMap, HttpServletResponse response) throws IOException;

    void importFile(MultipartFile multiPartFile) throws IOException;
}