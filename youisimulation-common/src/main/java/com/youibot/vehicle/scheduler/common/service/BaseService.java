package com.youibot.vehicle.scheduler.common.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youibot.vehicle.scheduler.common.entity.QueryCol;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 基础服务接口，所有Service接口都要继承
 */
public interface BaseService<T> extends IService<T> {

    /**
     * <p>
     * 插入一条记录（选择字段，策略插入）
     * </p>
     *
     * @param entity 实体对象
     */
    boolean insert(T entity);

    /**
     * <p>
     * 插入（批量），该方法不支持 Oracle、SQL Server
     * </p>
     *
     * @param entityList 实体对象集合
     */
    boolean insertBatch(Collection<T> entityList);

    /**
     * <p>
     * 插入（批量），该方法不支持 Oracle、SQL Server
     * </p>
     *
     * @param entityList 实体对象集合
     * @param batchSize  插入批次数量
     */
    boolean insertBatch(Collection<T> entityList, int batchSize);

    /**
     * <p>
     * 根据 ID 选择修改
     * </p>
     *
     * @param entity 实体对象
     */
    boolean updateById(T entity);

    /**
     * <p>
     * 根据 whereEntity 条件，更新记录
     * </p>
     *
     * @param entity        实体对象
     * @param updateWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper}
     */
    boolean update(T entity, Wrapper<T> updateWrapper);

    /**
     * <p>
     * 根据ID 批量更新
     * </p>
     *
     * @param entityList 实体对象集合
     */
    boolean updateBatchById(Collection<T> entityList);

    /**
     * <p>
     * 根据ID 批量更新
     * </p>
     *
     * @param entityList 实体对象集合
     * @param batchSize  更新批次数量
     */
    boolean updateBatchById(Collection<T> entityList, int batchSize);

    /**
     * <p>
     * 根据 ID 查询
     * </p>
     *
     * @param id 主键ID
     */
    T selectById(Serializable id);

    /**
     * <p>
     * 根据 ID 删除
     * </p>
     *
     * @param id 主键ID
     */
    boolean deleteById(Serializable id);

    /**
     * <p>
     * 删除（根据ID 批量删除）
     * </p>
     *
     * @param idList 主键ID列表
     */
    boolean deleteBatchIds(Collection<? extends Serializable> idList);

     default QueryWrapper<T> getWrapper(Map<String, Object> params){
         QueryCol queryCol = ConvertUtils.mapToBean(params, QueryCol.class);
         return   getWrapper(params, queryCol);
    };

    default Map<String, Object> paramsToLike(Map<String, Object> params, String... likes){
        for (String like : likes){
            String val = (String)params.get(like);
            if (StringUtils.isNotBlank(val)){
                params.put(like, "%" + val + "%");
            }else {
                params.put(like, null);
            }
        }
        return params;
    }

    /**
     * 得到查询条件
     * @param params 参数
     * @param col 查询条件
     * @return
     */

    /**
     * 得到查询条件
     * @param params 参数
     * @param col 查询条件
     * @return
     */
    default  QueryWrapper<T> getWrapper(Map<String, Object> params, QueryCol col) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(col.getEqCol())) {
            Stream.of(col.getEqCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                queryWrapper.eq(StringUtils.isNotBlank(val), ConvertUtils.camel2under(key), val);
            });
        }

        if (StringUtils.isNotBlank(col.getLikeCol())) {
            String[] like = col.getLikeCol().split(",");
            paramsToLike(params, like);
            Stream.of(like).forEach(key -> {
                String val = (String) params.get(key);
                queryWrapper.like(StringUtils.isNotBlank(val), ConvertUtils.camel2under(key), val);
            });
        }

        if (StringUtils.isNotBlank(col.getInCol())) {
            Stream.of(col.getInCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                if (StringUtils.isNotBlank(val)) {
                    queryWrapper.in(ConvertUtils.camel2under(key), val.split(","));
                }
            });
        }

        if (StringUtils.isNotBlank(col.getTimeCol())) {
            Stream.of(col.getTimeCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                if (StringUtils.isNotBlank(val)) {
                    String[] split = val.split(",");
                    if (split.length >= 1) {
                        queryWrapper.ge( ConvertUtils.camel2under(key), split[0]);
                    }
                    if (split.length >= 2) {
                        queryWrapper.le(ConvertUtils.camel2under(key), split[1]);
                    }
                }
            });
        }

        return queryWrapper;
    }

    List<String> getFileds();

    void likeWrapper(Map<String, Object> params);
}