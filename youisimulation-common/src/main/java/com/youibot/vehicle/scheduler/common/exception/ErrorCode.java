package com.youibot.vehicle.scheduler.common.exception;

/**
 * 错误编码，由5位数字组成，前2位为模块编码，后3位为业务编码
 * 如：10001（10代表系统模块，001代表业务代码）
 */
public interface ErrorCode {
    int INTERNAL_SERVER_ERROR = 500;
    int UNAUTHORIZED = 401;
    int FORBIDDEN = 403;

    int NOT_NULL = 10001;
    int DB_RECORD_EXISTS = 10002;
    int PARAMS_GET_ERROR = 10003;
    int ACCOUNT_PASSWORD_ERROR = 10004;
    int ACCOUNT_DISABLE = 10005;
    int IDENTIFIER_NOT_NULL = 10006;
    int ACCOUNT_NOT_MENU = 10007;
    int CAPTCHA_ERROR = 10007;
    int SUB_MENU_EXIST = 10008;
    int PASSWORD_ERROR = 10009;
    int ACCOUNT_NOT_EXIST = 10010;
    int SUPERIOR_DEPT_ERROR = 10011;
    int SUPERIOR_MENU_ERROR = 10012;
    int DATA_SCOPE_PARAMS_ERROR = 10013;
    int DEPT_SUB_DELETE_ERROR = 10014;
    int DEPT_USER_DELETE_ERROR = 10015;
    int ACT_DEPLOY_ERROR = 10016;
    int ACT_MODEL_IMG_ERROR = 10017;
    int ACT_MODEL_EXPORT_ERROR = 10018;
    int UPLOAD_FILE_EMPTY = 10019;
    int TOKEN_NOT_EMPTY = 10020;
    int TOKEN_INVALID = 10021;
    int ACCOUNT_LOCK = 10022;
    int ACT_DEPLOY_FORMAT_ERROR = 10023;
    int OSS_UPLOAD_FILE_ERROR = 10024;
    int SEND_SMS_ERROR = 10025;
    int MAIL_TEMPLATE_NOT_EXISTS = 10026;
    int REDIS_ERROR = 10027;
    int JOB_ERROR = 10028;
    int INVALID_SYMBOL = 10029;
    int JSON_FORMAT_ERROR = 10030;
    int SMS_CONFIG = 10031;
    int ACCOUNT_EXIST = 10200;
    int TASK_CLIME_FAIL = 10032;
    int NONE_EXIST_PROCESS = 10033;
    int SUPERIOR_NOT_EXIST = 10034;
    int REJECT_MESSAGE = 10035;
    int ROLLBACK_MESSAGE = 10036;
    int UNCLAIM_ERROR_MESSAGE = 10037;
    int SUPERIOR_REGION_ERROR = 10038;
    int REGION_SUB_DELETE_ERROR = 10039;
    int PROCESS_START_ERROR = 10040;
    int REJECT_PROCESS_PARALLEL_ERROR = 10041;
    int REJECT_PROCESS_HANDLEING_ERROR = 10042;
    int END_PROCESS_PARALLEL_ERROR = 10043;
    int END_PROCESS_HANDLEING_ERROR = 10044;
    int END_PROCESS_MESSAGE = 10045;
    int BACK_PROCESS_PARALLEL_ERROR = 10046;
    int BACK_PROCESS_HANDLEING_ERROR = 10047;

    // socket
    int CONNECTION_FAILED = 10060;
    int RETURN_EMPTY = 10061;
    int RETURN_PACKET_HEADER_ERROR = 10062;
    int RETURN_PACKET_END_ERROR = 10063;
    int RETURN_DATA_EMPTY = 10064;
    int RETURN_CODE_ERROR = 10065;

    // action
    int ACTION_THREAD_INTERRUPT=10070;
    int ACTION_STATUS_IS_NULL=10071;

    // vehicle
    int VEHICLE_PILOT_EXCEPTION=10080;


    int SUCCESS = 200;


    //证书相关
    int LICENSE_CERTIFICATE_FAILURE = 10048;
    int LICENSE_CERTIFICATE_NOT_UPLOADED = 10049;
    int LICENSE_CERTIFICATE_VALIDATE_FAILED = 10050;
    int LICENSE_CERTIFICATE_AUTHENTICATION_FAILED = 10051;
    int CERTIFICATE_EXPIRED = 10052;
    int LICENSE_CERTIFICATE_HAS_BEEN_UPLOADED = 10053;


    //模块------------系统
    //系统属性
    int PROPERTY_EXIST_ERROR = 10054;//数据库 已存在 type +  category 重复了
    int TYPE_CATEGORY_REPEAT_ERROR = 10055;//type +  category 重复了
    int MD5_ENCRYPTION_FAIL = 10056;
    int DATA_EXPORT_ERROR = 10057;
    int CODE_OR_NAME_IS_EXIST = 10058;
    int MISSING_PARAMETER = 10059;//参数丢失
    int FILE_MISSING_PARAMETER = 10060;//文件缺少参数或参数值为空！
    int MUST_UPLOAD_EXCEL_FILE_FORMAT = 10061;//只能上传excel文件格式！
    int READ_EXCEL_FILE_FAIL = 10062;//读取excel文件失败！！
    int INVALIDE_CODE_ERROR = 10063;//编码不合法


    //模块------------地图 11
    //地图-基本信息相关
    int MAP_CODE_IS_NULL_ERROR = 11000;//地图编码为空
    int MAP_NOT_EXISTS_ERROR = 11001;//地图不存在
    int MAP_INVALID_SUFFIX_FORMAT = 11002;//地图文件格式不合法！
    int MAP_ADD_ERROR = 11003;//新增地图失败
    int MAP_QUERY_ERROR = 11004;//查询地图失败
    int READ_MAP_INFO_DATA_ERROR = 11005;//读取info数据失败
    int MAP_UPDATE_ERROR = 11006;//修改地图失败
    int MAP_DELETE_ERROR = 11007;//删除地图失败

    int UPDATE_PATH_DATA_ERROR = 11008;//更新路网数据失败
    int DELETE_PATH_DATA_ERROR = 11009;//删除路网数据失败
    int DELETE_DRAFT_PATH_DATA_ERROR = 11010;//删除草稿路网数据失败
    //地图处理补充异常码
    int MAP_IMPORT_SELECT_CONTENT = 11011; //导入地图，选择导入内容
    int MAP_IS_NOT_PUBLISH = 11012;  //地图没有正式版本

    //地图-点位相关
    int MARKER_ADD_ERROR = 11013;//新增点位失败
    int MARKER_UPDATE_ERROR = 11014;//修改点位失败
    int MARKER_DELETE_ERROR = 11015;//删除点位失败
    int MARKER_NOT_EXISTS_ERROR = 11016;//点位不存在
    int MARKER_ALREADY_BIND_DEVICE_ERROR = 11017;//点位已经被绑定电梯异常
    int MARKER_SPACING_ERROR = 11018; //点位间距小于设定值

    //地图-区域相关
    int AREA_NOT_EXIST_ERROR = 11019;//区域找不到
    int AREA_ADD_ERROR = 11020;//新增区域异常
    int AREA_UPDATE_ERROR = 11021;//修改区域异常
    int AREA_DELETE_ERROR = 11022;//删除区域异常

    //地图-路径相关
    int PATH_BIND_MARKER_NOT_EXIST_ERROR = 11023;//路径绑定的点位不存在
    int PATH_ALREADY_EXIST_ERROR = 11024;//路径已经存在异常
    int PATH_ADD_ERROR = 11025;//路径新增失败
    int PATH_UPDATE_ERROR = 11026;//路径修改失败
    int PATH_DELETE_ERROR = 11027;//路径删除失败
    int PATH_NOT_EXIST_ERROR = 11028;//路径不存在异常

    //地图-自动门相关
    int AUTODOOR_NOT_EXIST_ERROR = 11029;//自动门不存在
    int PATH_ALREADY_BIND_ERROR = 11030;//路径已经被绑定异常
    int AUTODOOR_QUERY_ERROR = 11031;//查询自动门异常！
    int AUTODOOR_ADD_ERROR = 11032;//新增自动门异常
    int AUTODOOR_UPDATE_ERROR = 11033;//修改自动门异常
    int AUTODOOR_DELETE_ERROR = 11034;//删除自动门异常

    //地图-风淋门相关
    int AIRSHOWERDOOR_NOT_EXIST_ERROR = 11035;//风淋门门不存在
    int AIRSHOWERDOOR_ADD_ERROR = 11036;//新增风淋门异常
    int AIRSHOWERDOOR_UPDATE_ERROR = 11037;//修改风淋门异常
    int AIRSHOWERDOOR_DELETE_ERROR = 11038;//删除风淋门异常
    int AIRSHOWERDOOR_QUERY_ERROR = 11039;//查询风淋门异常

    //地图-电梯相关
    int ELEVATOR_ADD_DATA_ERROR = 11040;//新增电梯数据失败
    int ELEVATOR_UPDATE_DATA_ERROR = 11041;//修改电梯数据失败FILE
    int ELEVATOR_DELETE_DATA_ERROR = 11042;//删除电梯数据失败
    int ELEVATOR_NOT_EXIST_ERROR = 11043;//电梯不存在
    int ELEVATOR_CODE_PARSE_ERROR = 11044;//电梯编号解析异常
    int ELEVATOR_FILE_FORMAT_ERROR = 11045;//电梯文件格式错误
    int ELEVATOR_RECORD_EXIST = 11046;//电梯已存在,导入失败
    int ELEVATOR_WARING_BE_USING = 11047;//地图发布检查，有关联电梯正在被使用，是否强制发布
    int ELEVATOR_IMPORT_MAPs_BEFORE = 11048;//导入失败，请先导入电梯绑定的所有地图
    int MAP_ALREADY_BIND_ELEVATOR_ERROR = 11049;//电梯在一张地图上只能绑定一个点

    int LOCATING_MAP_NOT_EXISTS_ERROR = 11050;//定位图不存在

    int MAP_CAN_NOT_UNDO = 11051;//当前已不可再回退
    int MAP_CAN_NOT_REDO = 11052;//当前已不可再恢复


    //模块------------设备  12
    int DEVICE_COMMUNICATE_ERROR = 12000;//操作设备失败，设备[{0}]网络未连接
    int DEVICE_OPEN_ERROR = 12001;//开启设备[{0}]异常，请查看操作日志
    int DEVICE_CLOSE_ERROR = 12002;//关闭设备[{0}]异常，请查看操作日志
    int DEVICE_IS_IN_USER_ERROR = 12003;//操作设备失败，设备[{0}]正在使用中


    //模块------------机器人 13
    // 机器人相关
    int VEHICLE_NOT_LOGIN = 13000;//机器人未登录,请先登录！
    int VEHICLE_OPERATE_ERROR = 13001;//操作机器人[{0}]失败，[{1}]
    int VEHICLE_OFFLINE = 13002; //操作机器人失败，机器人[{0}]网络未连接
    int AGV_OUT_SIDE_ERROR = 13003;//机器人脱轨
    int AIM_MARKED_UNREACHABLE_ERROR = 13004;//目标点不可达!
    int INVALID_PATH_ERROR = 13005;//无效路径!
    int PATH_PLAN_ERROR = 13006;//路径规划异常!
    int VEHICLE_NOT_EXIST_ERROR = 13007;//机器人不存在
    int VEHICLE_TYPE_NOT_EXIST_ERROR = 13008;//机器人类型不存在
    int VEHICLE_TYPE_BIND_REPEAT_ERROR = 13009;//机器人类型绑定重复


    //模块------------任务 14
    //任务相关
    int EL_EXPRESS_PARSING_FAIL = 14001; //启用失败，原因为[{0}]
    int TASK_TYPE_UNPUBLISHED = 14002; //新增任务失败，使用了未发布的任务流程[{0}]
    int TASK_TYPE_NODE_NULL = 14003; //发布任务失败，缺少可用的节点
    int PARAM_CHECK_FAIL = 14004; //参数校验失败
    int POINT_LOCKED = 14005; //该点位已被其它任务占用
    int VEHICLE_UN_EXIST = 14006; //新增任务失败，输入的机器人[{0}]不存在
    int POINT_UN_EXIST = 14007; //新增任务失败，输入的点位[{0}]不存在
    int MAP_UN_EXIST = 14008; //新增任务失败，输入的地图[{0}]不存在
    int TASK_CANCEL_FAIL = 14009; //无法取消机器人指令，请手动重启机器人
    int OPERATING_ERROR = 14010;//执行中，请勿重复操作
    int TASK_IMPORT_ERROR_TASK_NO_REPEAT = 14011;//导入失败，任务编码重复
    int TASK_UN_EXIST = 14012;//任务不存在
    int TASK_NODE_UN_EXIST = 14013;//任务节点不存在
    int TASK_NODE_CANNOT_BE_SKIP = 14014;//任务节点[{0}]不允许被跳过
    int TASK_NODE_CANNOT_RETRY = 14015;//任务节点[{0}]不允许重试
    int DYNAMIC_PARAM_CHECK_FAIL = 14016;//传入的动态参数[{}]格式不正确
    int NO_AVALIABLE_MARKER = 14017;//传入点位集合不可用
    int NO_AVALIABLE_VEHICLE = 14018;//传入机器人集合不可用

    // 动作节点模块
    // Modbus-寄存器功能码不正确
    int MODBUS_FUNCTION_CODE_IS_WRONG_ERROR = 20001;
    // 寄存器值不在范围内
    int MODBUS_DATA_IS_NOT_IN_RANGE = 20002;
    // 写入寄存器失败
    int MODBUS_WRITE_IN_ERROR = 20003;
    // 读寄存器失败
    int MODBUS_READ_IN_ERROR = 20004;
}
