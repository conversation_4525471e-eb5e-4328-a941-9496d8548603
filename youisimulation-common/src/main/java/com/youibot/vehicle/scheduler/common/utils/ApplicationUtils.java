package com.youibot.vehicle.scheduler.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: yaoh<PERSON><PERSON>
 * @Date: 2022/12/08/11:25
 * @Description:
 */
@Component
public class ApplicationUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationUtils.applicationContext = applicationContext;
    }

    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }

    public static <T> T getBean(Class<T> clz) throws BeansException {
        final T result = applicationContext.getBean(clz);
        return result;
    }


    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static ThreadPoolTaskExecutor getThreadPoolExecutor(Integer corePoolSize, Integer maxPoolSize, Integer queueCapacity, Integer keepAliveTime, String namePrefix, ThreadPoolExecutor.CallerRunsPolicy callerRunsPolicy) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置线程池核心容量
        executor.setCorePoolSize(corePoolSize);
        // 设置线程池最大容量
        executor.setMaxPoolSize(maxPoolSize);
        // 设置任务队列长度
        executor.setQueueCapacity(queueCapacity);
        // 设置线程超时时间
        executor.setKeepAliveSeconds(keepAliveTime);
        // 设置线程名称前缀
        executor.setThreadNamePrefix(namePrefix);
        // 设置任务丢弃后的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }
}
