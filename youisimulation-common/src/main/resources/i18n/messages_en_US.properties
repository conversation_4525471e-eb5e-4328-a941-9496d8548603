#English
500=Server internal exception
401=Unauthorized
403=Access denied, no permissions
10001={0} cannot be empty
10002=The record already exists in the database
10003=Failed to get parameters
10004=The account number or password is incorrect.
10005=Account has been deactivated
10006=Unique ID cannot be empty
10007=The verification code is incorrect
10008=First delete submenu or button
10009=The original password is incorrect
10010=The account does not exist
10011=The superior department made a wrong choice
10012=Upper menu cannot be for itself
10013=Data permission interface, which can only be a Map type parameter.
10014=Please delete the subordinate department first
10015=Please delete the user under the department first
10016=Deployment failed, no process
10017=Model diagram is incorrect, please check
10018=The export failed with the model ID {0}
10019=Please upload a file
10020=token cannot be empty
10021=token is invalid, please log in again
10022=The account has been locked
10023=Please upload zip, bar, bpmn, bpmn20.xml format file
10024=Failed to upload file {0}
10025=Failed to send SMS {0}
10026=Mail template does not exist
10027=Redis service exception
10028=Timed task failed
10029=Cannot contain illegal characters
10030=The parameter format is incorrect. Please use JSON format.
10031=Please complete the SMS configuration first.
10200=Account already exists
10032=Task has been signed and operation failed
10033=Non-existent process definition
10034=Superior node does not exist
10035=Reject
10036=Rollback
10037=Tasks are not grouped and cannot be cancelled
10038=Upper area selection error
10039=Please delete the subordinate area first
10040=The process has been suspended and the instance cannot be started
10041=Multi-instance tasks cannot be rejected
10042=Tasks in multiple processes cannot be rejected
10043=Multi-instance tasks cannot be terminated
10045=END
10046=Multi-instance tasks cannot be rolled back
10047=There are multiple parallel tasks that cannot be rolled back
10044=There are tasks in multiple processes that cannot terminate the process

#socket message
10060=Data interconnection failed!
10061=The communication of return is empty!
10062=The head of communication package error!
10063=The tail of communication package error!
10064=The content of communication is empty!
10065=The communication returns an error code!

#action
10070=Execution is interrupted!
10071=The instruction issued to pilot cannot be found

#vehicle
10080=Abnormal robot chassis