<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.youibot</groupId>
		<artifactId>youisimulation-plus</artifactId>
		<version>5.0.10</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>youisimulation-admin</artifactId>
	<version>5.0.10</version>
	<packaging>jar</packaging>
	<description>youisimulation-admin</description>

	<properties>
		<quartz.version>2.3.0</quartz.version>
		<shiro.version>1.6.0</shiro.version>
		<springfox-version>2.9.2</springfox-version>
		<swagger.model>1.5.21</swagger.model>
		<netty.version>4.1.70.Final</netty.version>
		<app.name>${project.artifactId}</app.name>
		<app.version>${project.version}</app.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.youibot</groupId>
			<artifactId>youisimulation-common</artifactId>
			<version>5.0.10</version>
		</dependency>
	<!--	<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>${quartz.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.mchange</groupId>
					<artifactId>c3p0</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.zaxxer</groupId>
					<artifactId>HikariCP-java6</artifactId>
				</exclusion>
			</exclusions>
		</dependency>-->
		<dependency>
		    <groupId>org.apache.shiro</groupId>
		    <artifactId>shiro-core</artifactId>
		    <version>${shiro.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.shiro</groupId>
		    <artifactId>shiro-spring</artifactId>
		    <version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${springfox-version}</version>
			<exclusions>
				<exclusion>
					<groupId>io.swagger</groupId>
					<artifactId>swagger-models</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>${springfox-version}</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-models</artifactId>
			<version>${swagger.model}</version>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
			<version>${netty.version}</version>
		</dependency>

		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.7.0</version> <!-- 使用最新版本 -->
		</dependency>


		<!-- https://mvnrepository.com/artifact/org.jgrapht/jgrapht-core -->
		<dependency>
			<groupId>org.jgrapht</groupId>
			<artifactId>jgrapht-core</artifactId>
			<version>1.4.0</version>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>org.jgrapht</groupId>-->
<!--			<artifactId>jgrapht-ext</artifactId>-->
<!--			<version>1.4.0</version> &lt;!&ndash; 用于可视化 &ndash;&gt;-->
<!--		</dependency>-->


		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
			<version>0.9.11</version>
		</dependency>

<!--		<dependency>
			<groupId>co.paralleluniverse</groupId>
			<artifactId>quasar-core</artifactId>
			<version>0.7.10</version>
		</dependency>

		<dependency>
			<groupId>com.esotericsoftware</groupId>
			<artifactId>kryo</artifactId>
			<version>5.0.0</version> &lt;!&ndash; Adjust version as per your requirements &ndash;&gt;
		</dependency>-->

    </dependencies>

	<build>
		<finalName>youisimulation-${project.version}</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<version>${docker.plugin.version}</version>
				<configuration>
					<imageName>${project.artifactId}</imageName>
					<dockerDirectory>${project.basedir}/</dockerDirectory>
					<resources>
						<resource>
							<targetPath>/</targetPath>
							<directory>${project.build.directory}</directory>
							<include>${project.build.finalName}.jar</include>
						</resource>
					</resources>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>