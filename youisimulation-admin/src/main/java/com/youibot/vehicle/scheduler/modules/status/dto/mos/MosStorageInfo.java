package com.youibot.vehicle.scheduler.modules.status.dto.mos;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class MosStorageInfo implements Serializable  {

    private String id;//料箱号码

    private String state;//储位状态, FULL:有, EMPTY:无, ERROR:检测错误

    private String code;//物料编码, UNDEFINE:未定义, NONE:读取失败, 其他值为正常读取到的数据

    private Integer error_code;//错误编码

    private String message;//信息

}
