package com.youibot.vehicle.scheduler.modules.event.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.youibot.vehicle.scheduler.common.util.ShellUtils;
import com.youibot.vehicle.scheduler.common.util.VehicleUtils;
import com.youibot.vehicle.scheduler.modules.client.constant.VehicleSocketConstant;
import com.youibot.vehicle.scheduler.modules.client.entity.VehicleInstructionData;
import com.youibot.vehicle.scheduler.modules.client.entity.VehicleSocketMessage;
import com.youibot.vehicle.scheduler.modules.client.utils.VehicleSocketUtils;
import com.youibot.vehicle.scheduler.modules.config.entity.SystemConfigEntity;
import com.youibot.vehicle.scheduler.modules.config.service.SystemConfigService;
import com.youibot.vehicle.scheduler.modules.event.dto.EventResponseData;
import com.youibot.vehicle.scheduler.modules.event.dto.EventSendData;
import com.youibot.vehicle.scheduler.modules.event.dto.RegisterData;
import com.youibot.vehicle.scheduler.modules.event.dto.VehicleEventRespData;
import com.youibot.vehicle.scheduler.modules.event.enums.EventTypeEnum;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.instruction.entity.VehicleInstructionEntity;
import com.youibot.vehicle.scheduler.modules.vehicle.instruction.service.VehicleInstructionService;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.InstructionDataPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.Socket;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: yaohaohui
 * @Date: 2022/12/27/16:24
 * @Description:
 */
@Service
public class VehicleEventServiceImpl implements VehicleEventService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Resource
    private SystemConfigService systemConfigService;
    @Autowired
    private VehicleInstructionService vehicleInstructionService;
    @Autowired
    private InstructionDataPool instructionDataPool;

    @Override
    public void register() {
        try {
            Vehicle vehicle = VehicleUtils.getVehicle();
//            VehicleSocketUtils.logoutCoreClient();
            vehicle.setRegisterSuccess(false);
            //vehicle.stopPushThread();

            SystemConfigEntity systemConfig = systemConfigService.getSystemConfig();
            if (systemConfig == null || StrUtil.isBlank(systemConfig.getFleetIp())) {
                LOGGER.error("系统配置为空或者调度地址未配置，无法注册");
                return;
            }

            RegisterData registerData = new RegisterData();
            registerData.setMacAddress(systemConfig.getMacAddress());
            if(StringUtils.isEmpty(registerData.getMacAddress())){
                String macAddress = ShellUtils.getMacAddress();
                systemConfig.setMacAddress(macAddress);
                registerData.setMacAddress(macAddress);
                systemConfigService.updateById(systemConfig);
            }
            String[] split = systemConfig.getFleetIp().split(":");
            Socket client = VehicleSocketUtils.createClient(split[0], Integer.valueOf(split[1]));
            EventSendData eventSendData = EventSendData.builder().type(EventTypeEnum.REGISTER.getType()).data(registerData).build();
            VehicleSocketMessage vehicleSocketMessage = VehicleSocketUtils.sendMessage(client, VehicleSocketConstant.VEHICLE_EVENT, eventSendData);
            VehicleEventRespData vehicleEventRespData = vehicleSocketMessage.getData().toJavaObject(VehicleEventRespData.class);
            if (VehicleSocketConstant.RESULT_SUCCESS.equals(vehicleEventRespData.getResult())) {
                vehicle.setRegisterSuccess(true);
                EventResponseData eventResponseData = vehicleEventRespData.getData().toJavaObject(EventResponseData.class);
                vehicle.setVehicleCode(eventResponseData.getVehicleCode());
                if (!CollectionUtils.isEmpty(eventResponseData.getInstructList())) {
                    List<String> fleetInstructIds = eventResponseData.getInstructList().stream().filter(i ->i.isMyTask( vehicle) ).map(VehicleInstructionData::getInstructId).collect(Collectors.toList());
                    List<String> existInstructIds = vehicleInstructionService.list().stream().filter(i ->i.isMyTask( vehicle) ).map(VehicleInstructionEntity::getInstructId).collect(Collectors.toList());
                    existInstructIds.removeIf(fleetInstructIds::contains);
                    existInstructIds.forEach(c -> instructionDataPool.detachInstructionData(vehicle , c));
                }

                // 开始推送
                //vehicle.startPushThread();
                vehicle.setFleetIp(systemConfig.getFleetIp());
                VehicleSocketUtils.registerCoreClient(client);
                VehicleUtils.invalidInRegister( vehicle ) ;
                LOGGER.info("vehicleCode:{}注册成功,channelId:{},data:{}",vehicle.getVehicleCode(),vehicle.getFleetChannel().id(),  JSONObject.toJSONString(eventResponseData));
            } else {
                LOGGER.info("注册失败,failMessage:{}", vehicleSocketMessage.getData().get("failMessage"));
            }
        } catch (IOException e) {
            LOGGER.error("创建socket客户端失败", e);
        }
    }

    @Override
    public void register(Vehicle vehicle) {
        try {
            if( vehicle.isRegisterSuccess() ){
                return ;
            }

            RegisterData registerData = new RegisterData();
            registerData.setMacAddress(vehicle.getMacAddress());

            EventSendData eventSendData = EventSendData.builder().type(EventTypeEnum.REGISTER.getType()).data(registerData).build();
            VehicleSocketUtils.sendMessage(vehicle ,VehicleSocketConstant.VEHICLE_EVENT, eventSendData);

        } catch (Exception e) {
            LOGGER.error("机器人发起注册申请失败", e);
            VehicleUtils.invalidInRegister( vehicle );
        }


                ;

    }

    @Override
    public boolean offLine() {
        Vehicle vehicle = VehicleUtils.getVehicle();
        if (StringUtils.isEmpty(vehicle.getFleetIp())) {
            LOGGER.info("机器人下线失败, 未配置ip与端口");
            return false;
        }
        Map<String, Object> data = new HashMap<>(1);
        data.put("vehicleCode", vehicle.getVehicleCode());
        EventSendData eventSendData = EventSendData.builder().type(EventTypeEnum.OFF_LINE.getType()).data(data).build();
        try {
            String[] split = vehicle.getFleetIp().split(":");
            Socket client = VehicleSocketUtils.createClient(split[0], Integer.valueOf(split[1]));
            VehicleSocketMessage vehicleSocketMessage = VehicleSocketUtils.sendMessage(client, VehicleSocketConstant.VEHICLE_EVENT, eventSendData);
            VehicleEventRespData vehicleEventRespData = vehicleSocketMessage.getData().toJavaObject(VehicleEventRespData.class);
            if (!VehicleSocketConstant.RESULT_SUCCESS.equals(vehicleEventRespData.getResult())) {
                LOGGER.info("机器人下线失败:{}", vehicleEventRespData.getFailMessage());
                return false;
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("机器人下线异常", e);
            return false;
        }
    }
}
