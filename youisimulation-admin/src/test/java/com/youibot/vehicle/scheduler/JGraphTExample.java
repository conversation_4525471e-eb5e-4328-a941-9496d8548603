/*
package com.youibot.vehicle.scheduler;

import com.alibaba.fastjson.JSON;
import com.mxgraph.layout.hierarchical.mxHierarchicalLayout;
import com.mxgraph.swing.mxGraphComponent;
import com.youibot.vehicle.scheduler.map.GraphVisualization;
import com.youibot.vehicle.scheduler.modules.map.dto.PathInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.MapGraphInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import org.apache.commons.io.FileUtils;
import org.jgrapht.Graph;
import org.jgrapht.alg.shortestpath.AllDirectedPaths;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;
public class JGraphTExample {


    */
/**
     * 两点之前的top n 条路径
     *//*


    private static  String roadNet ="E:\\home\\youibot\\youibot_map\\map724_roadnet\\locating\\current\\map724.roadnet" ;
    public static void main(String[] args) throws IOException {

        String s = FileUtils.readFileToString(new File(roadNet), "UTF-8");

        MapGraphInfo mapGraphInfo = JSON.parseObject(s, MapGraphInfo.class);

        DirectedWeightedPseudograph<Marker, DefaultWeightedEdge> graph =
                new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        Map<String, Marker> markerMap = new HashMap<>();

        mapGraphInfo.getMarkers().forEach( marker -> {

            graph.addVertex(marker);
            markerMap.put(marker.getCode(), marker);
        });
        // 添加顶点
        mapGraphInfo.getPaths().forEach( path -> {
            List<PathInfo> pathInfos = path.getPathInfos();
            PathInfo pathInfo = pathInfos.get(0);
            Marker source = markerMap.get(path.getStartMarkerCode());
            Marker target = markerMap.get(path.getEndMarkerCode());

            DefaultWeightedEdge edge = graph.addEdge(source, target);
            // 确保边权重是正数
            graph.setEdgeWeight(edge,  pathInfo.getLength() ); // 权重在 1 到 10 之间


        });


        int topNPaths = 10 ;

        Marker start = markerMap.get("map724_P_7");
        Marker end = markerMap.get("map724_P_4");
        visualizeGraph(graph, start, end , topNPaths  );

        List<List<DefaultWeightedEdge>> shortestPaths = findShortestPaths(graph, start, end ,  topNPaths);

        // 打印前5条最小权重路径
        printShortestPaths(shortestPaths, graph);


    }




        private static List<GraphVisualization.PathWithWeight> getAllPaths(Graph<Marker, DefaultWeightedEdge> graph, Marker start, Marker end, int maxPathLength) {
        AllDirectedPaths<Marker, DefaultWeightedEdge> allPathsFinder = new AllDirectedPaths<>(graph);
        return allPathsFinder.getAllPaths(start, end, true, maxPathLength)
                .stream()
                .map(path -> new GraphVisualization.PathWithWeight(path.getEdgeList(), path.getWeight()))
                .collect(Collectors.toList());
    }
        private static void visualizeGraph(Graph<Marker, DefaultWeightedEdge> graph, Marker start, Marker end, int topNPaths) {
        // Get all paths with a maximum length to avoid infinite cycles
        List<GraphVisualization.PathWithWeight> allPaths = getAllPaths(graph, start, end, 10  );

        // Sort paths by their total weight
        allPaths.sort(Comparator.comparingDouble(GraphVisualization.PathWithWeight::getTotalWeight));

        // Create JGraphX adapter
        JGraphXAdapter<Marker, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // Create a Swing window to display the graph
        JFrame frame = new JFrame();
        frame.setLayout(new BorderLayout());

        // Graph panel
        mxGraphComponent graphComponent = new mxGraphComponent(graphAdapter);
        graphComponent.setPreferredSize(new Dimension(800, 600));
        frame.add(graphComponent, BorderLayout.CENTER);

        // Create a panel to list all paths
        JPanel pathPanel = new JPanel();
        pathPanel.setLayout(new GridLayout(Math.min(allPaths.size(), topNPaths), 1)); // Limit to topNPaths

        // Define colors
        Color[] colors = {Color.RED, Color.BLUE, Color.GREEN, Color.ORANGE, Color.MAGENTA};

        // Display paths and highlight them
        graphAdapter.getModel().beginUpdate();
        try {
            // Add each path to the panel and visualize it
            for (int i = 0; i < Math.min(allPaths.size(), topNPaths); i++) {
                GraphVisualization.PathWithWeight pathWithWeight = allPaths.get(i);
                List<DefaultWeightedEdge> path = pathWithWeight.getPath();
                Color color = colors[i % colors.length];

                // Add path description to the panel
                String pathDescription = path.stream()
                        .map(edge -> String.format("%s->%s(%.2f)", graph.getEdgeSource(edge).getCode(), graph.getEdgeTarget(edge).getCode(), graph.getEdgeWeight(edge)))
                        .collect(Collectors.joining(" -> "));
                JLabel pathLabel = new JLabel(String.format("Path %d (Weight: %.2f): %s", i + 1, pathWithWeight.getTotalWeight(), pathDescription));
                pathLabel.setForeground(color);
                pathPanel.add(pathLabel);

                // Highlight the path
                for (DefaultWeightedEdge edge : path) {
                    String style = String.format("strokeColor=%s;strokeWidth=2;", toHexString(color));
                    graphAdapter.setCellStyle(style, new Object[]{graphAdapter.getEdgeToCellMap().get(edge)});
                }
            }
        } finally {
            graphAdapter.getModel().endUpdate();
        }

        // Add path panel to a scroll pane
        JScrollPane scrollPane = new JScrollPane(pathPanel);
        scrollPane.setPreferredSize(new Dimension(800, 200)); // Adjust the size as needed
        frame.add(scrollPane, BorderLayout.SOUTH);

        frame.setTitle("Graph Visualization with Multiple Paths");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(1000, 800); // Increase frame size if needed
        SwingUtilities.invokeLater(() -> frame.setVisible(true));

        // Use hierarchical layout to display the graph
        mxHierarchicalLayout layout = new mxHierarchicalLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());
    }



        // Stub class for PathWithWeight, replace with actual implementation
        public static class PathWithWeight {
            private final List<DefaultWeightedEdge> path;
            private final double totalWeight;

            public PathWithWeight(List<DefaultWeightedEdge> path, double totalWeight) {
                this.path = path;
                this.totalWeight = totalWeight;
            }

            public List<DefaultWeightedEdge> getPath() {
                return path;
            }

            public double getTotalWeight() {
                return totalWeight;
            }
        }

        private static String toHexString(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }
        private static List<List<DefaultWeightedEdge>> findShortestPaths(Graph<Marker, DefaultWeightedEdge> graph, Marker start, Marker end, int topNPaths ) {
        AllDirectedPaths<Marker, DefaultWeightedEdge> allPathsAlg = new AllDirectedPaths<>(graph);
        Collection<org.jgrapht.GraphPath<Marker, DefaultWeightedEdge>> paths = allPathsAlg.getAllPaths(start, end, true, null);

        List<List<DefaultWeightedEdge>> allPaths = new ArrayList<>();
        for (org.jgrapht.GraphPath<Marker, DefaultWeightedEdge> path : paths) {
            allPaths.add(new ArrayList<>(path.getEdgeList()));
        }

        // 根据路径总权重排序
        allPaths.sort(Comparator.comparingDouble(path ->
                path.stream().mapToDouble(edge -> graph.getEdgeWeight(edge)).sum()
        ));

        return allPaths.stream().limit( topNPaths ).collect(Collectors.toList()); // 获取前5条最小权重路径
    }

        private static void printShortestPaths(List<List<DefaultWeightedEdge>> paths, Graph<Marker, DefaultWeightedEdge> graph) {
        System.out.println("Top 5 Shortest Paths:");
        for (int i = 0; i < paths.size(); i++) {
            List<DefaultWeightedEdge> path = paths.get(i);
            double totalWeight = path.stream().mapToDouble(edge -> graph.getEdgeWeight(edge)).sum();
            System.out.println("Path " + (i + 1) + " (Total Weight: " + totalWeight + "):");
            for (DefaultWeightedEdge edge : path) {
                Marker source = graph.getEdgeSource(edge);
                Marker target = graph.getEdgeTarget(edge);
                System.out.println("  " + source.getCode() + " -> " + target.getCode() + " (Weight: " + graph.getEdgeWeight(edge) + ")");
            }
            System.out.println();
        }
    }

}
*/
