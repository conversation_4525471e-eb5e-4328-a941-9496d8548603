package com.youibot.vehicle.scheduler.modules.sim.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.youibot.vehicle.scheduler.AdminApplication;
import com.youibot.vehicle.scheduler.common.utils.RandomUtil;
import com.youibot.vehicle.scheduler.modules.sim.entity.SimConf;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertTrue;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = AdminApplication.class)
@SpringBootTest
public class SimConfServiceTest {

    @Autowired
    private  SimConfService simConfService;

    @Test
    public  void test1(){
        SimConf conf = new SimConf();
        conf.setIp("127.0.0.1");
        conf.setPort( 8080 );
        conf.setChargingTimeToFull( 1.0 );
        conf.setDischargeTimeToEmpty( 1.0 );
        conf.setSpeedOfMovement( 1.0 );
        conf.setRotationalSpeed( 1.0 );
        boolean save = simConfService.save(conf);
        System.out.println("save = " + save);
        System.out.println("id = " + conf.getName() );

    }
    @Test
    public  void testBatch(){
        List<SimConf> list = Lists.newArrayList();
        for (int i = 0; i < 10000 ; i++) {
            SimConf conf = new SimConf();
            conf.setIp( RandomUtil.randomIp());
//            if(i == 8500){
//                conf.setIp(StringUtils.repeat( conf.getIp() , 100));
//            }
            conf.setName( RandomUtil.randomString( 10));
            conf.setPort(RandomUtil.randomInt(0,65535));
            conf.setChargingTimeToFull( RandomUtil.randomDouble(1 , 20D) );
            conf.setDischargeTimeToEmpty(  RandomUtil.randomDouble(1 , 20D));
            conf.setSpeedOfMovement(  RandomUtil.randomDouble(1 , 20D) );
            conf.setRotationalSpeed(  RandomUtil.randomDouble(1 , 20D) );
            list.add( conf) ;

        }
        boolean b = simConfService.saveBatch(list);
        System.out.println("b = " + b);
        System.out.println("list = " + JSON.toJSONString( list ));
    }

    @Test
    public  void testUpdate(){
        SimConf conf = new SimConf();
        conf.setName("mytest");
        conf.setName("1815629527087321089");
        conf.setChargingTimeToFull( 1.0 );
        conf.setDischargeTimeToEmpty( 1.0 );
        conf.setSpeedOfMovement( 1.0 );
        conf.setRotationalSpeed( 1.0 );
        boolean save = simConfService.updateById(conf);
        System.out.println("save = " + save);
        assertTrue(save);
    }

    @Test
    public  void testPage(){

        Page<SimConf> page = new Page<>(1, 20) ;
        Page<SimConf> page1 = simConfService.page(page);
        System.out.println("page1 = " + JSON.toJSONString( page1 ));

    }



    @Test
    public  void testDefaultLocation(){

        String fleetIP = "**********";
        String mapCode="FinalMap0611" ;
        String defaultLocationCode = simConfService.getDefaultLocationCode(fleetIP,8080,  mapCode);
        System.out.println("defaultLocationCode = " + defaultLocationCode);

    }

}