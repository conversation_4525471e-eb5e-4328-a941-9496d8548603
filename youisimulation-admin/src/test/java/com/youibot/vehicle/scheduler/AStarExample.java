/*
package com.youibot.vehicle.scheduler;

import com.mxgraph.layout.mxCircleLayout;
import com.mxgraph.swing.mxGraphComponent;
import org.jgrapht.Graph;
import org.jgrapht.alg.interfaces.AStarAdmissibleHeuristic;
import org.jgrapht.alg.shortestpath.AStarShortestPath;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.SimpleWeightedGraph;

import javax.swing.*;
import java.util.List;

public class AStarExample {

    public static void main(String[] args) {
        // 创建一个简单的有向加权图
        Graph<String, DefaultWeightedEdge> graph = new SimpleWeightedGraph<>(DefaultWeightedEdge.class);

        // 添加顶点
        graph.addVertex("A");
        graph.addVertex("B");
        graph.addVertex("C");
        graph.addVertex("D");
        graph.addVertex("E");

        // 添加边及其权重
        graph.setEdgeWeight(graph.addEdge("A", "B"), 2.0);
        graph.setEdgeWeight(graph.addEdge("A", "C"), 1.0);
        graph.setEdgeWeight(graph.addEdge("B", "D"), 1.0);
        graph.setEdgeWeight(graph.addEdge("C", "D"), 2.0);
        graph.setEdgeWeight(graph.addEdge("B", "E"), 4.0);
        graph.setEdgeWeight(graph.addEdge("D", "E"), 1.0);

        // 定义启发式函数（假设目标为顶点 "E"）
        AStarAdmissibleHeuristic<String> heuristic = (source, target) -> {
            // 这里定义一个简单的启发式函数，你可以根据实际情况调整
            // 比如，可以通过字典顺序的距离来简单估计
            return Math.abs(source.charAt(0) - target.charAt(0));
        };

        // 使用A*算法计算最短路径
        AStarShortestPath<String, DefaultWeightedEdge> aStarAlg = new AStarShortestPath<>(graph, heuristic);
        List<DefaultWeightedEdge> path = aStarAlg.getPath("A", "E").getEdgeList();

        // 输出路径及其总权重
        System.out.println("Shortest path from A to E:");
        path.forEach(edge -> System.out.println(graph.getEdgeSource(edge) + " -> " + graph.getEdgeTarget(edge)));
        System.out.println("Total path weight: " + aStarAlg.getPath("A", "E").getWeight());

        visualizeGraph( graph  , path );
    }


    private static void visualizeGraph(Graph<String, DefaultWeightedEdge> graph, List<DefaultWeightedEdge> path) {
        // 创建JGraphX适配器
        JGraphXAdapter<String, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // 创建一个Swing窗口来展示图
        JFrame frame = new JFrame();
        frame.getContentPane().add(new mxGraphComponent(graphAdapter));
        frame.setTitle("AStarShortestPath Visualization");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.setVisible(true);

        // 使用圆形布局将图形展现出来
        mxCircleLayout layout = new mxCircleLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());

        // 显示边的权重和最短路径
        path.forEach(edge -> {
            String edgeLabel = String.format("%.2f", graph.getEdgeWeight(edge));
            graphAdapter.getModel().setValue(graphAdapter.getEdgeToCellMap().get(edge), edgeLabel);
            graphAdapter.setCellStyle("strokeColor=red", new Object[]{graphAdapter.getEdgeToCellMap().get(edge)});
        });
    }


    */
/**
     * 可视化并显示权重
     * @param graph
     * @param path
     *//*

    private static void visualizeGraph2(Graph<String, DefaultWeightedEdge> graph, List<DefaultWeightedEdge> path) {
        // 创建JGraphX适配器
        JGraphXAdapter<String, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // 创建一个Swing窗口来展示图
        JFrame frame = new JFrame();
        frame.getContentPane().add(new mxGraphComponent(graphAdapter));
        frame.setTitle("AStarShortestPath Visualization");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.setVisible(true);

        // 使用圆形布局将图形展现出来
        mxCircleLayout layout = new mxCircleLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());

        // 显示边的权重和最短路径
        path.forEach(edge -> {
            String edgeLabel = String.format("%.2f", graph.getEdgeWeight(edge));
            graphAdapter.getModel().setValue(graphAdapter.getEdgeToCellMap().get(edge), edgeLabel);
        });
    }
}
*/
