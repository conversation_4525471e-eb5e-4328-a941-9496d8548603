package com.youibot.vehicle.scheduler.modules.sim.helper.distance6;

import com.alibaba.fastjson.JSON;
import com.youibot.vehicle.scheduler.map.pathsplit2.PathUtils;
import com.youibot.vehicle.scheduler.modules.sim.dto.Point;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.Ellipse2D;
import java.awt.geom.GeneralPath;
import java.awt.geom.Line2D;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
public class PathVisualizer extends JPanel {
    private GraphPositionUtils pathUtils;
    private GraphPositionUtils.PathSegment highlightSegment;
    private Point highlightPoint;

    public PathVisualizer(GraphPositionUtils pathUtils) {
        this.pathUtils = pathUtils;
    }

    public void highlightPoint(Point point) {
        this.highlightPoint = point;
        repaint();
    }

    public void highlightSegment(GraphPositionUtils.PathSegment segment) {
        this.highlightSegment = segment;
        repaint();
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;

        for (GraphPositionUtils.PathSegment segment : pathUtils.segments) {
            if (segment.isBezier()) {
                drawBezierCurve(g2d, segment);
            } else {
                drawLineSegment(g2d, segment);
            }
        }

        if (highlightPoint != null) {
            drawHighlightPoint(g2d, highlightPoint);
        }

        if (highlightSegment != null) {
            drawHighlightSegment(g2d, highlightSegment);
        }
    }

    private void drawBezierCurve(Graphics2D g2d, GraphPositionUtils.PathSegment segment) {
        List<Point> points = segment.getPoints();
        GeneralPath path = new GeneralPath();
        Point p0 = points.get(0);
        Point p1 = points.get(1);
        Point p2 = points.get(2);
        Point p3 = points.get(3);

        path.moveTo(p0.getX(), p0.getY());
        path.curveTo(p1.getX(), p1.getY(), p2.getX(), p2.getY(), p3.getX(), p3.getY());
        g2d.setColor(Color.BLUE);
        g2d.draw(path);

        g2d.setColor(Color.RED);
        for (Point p : points) {
            g2d.fill(new Ellipse2D.Double(p.getX() - 3, p.getY() - 3, 6, 6));
        }
    }

    private void drawLineSegment(Graphics2D g2d, GraphPositionUtils.PathSegment segment) {
        List<Point> points = segment.getPoints();
        Line2D line = new Line2D.Double(points.get(0).getX(), points.get(0).getY(),
                                        points.get(1).getX(), points.get(1).getY());
        g2d.setColor(Color.BLUE);
        g2d.draw(line);

        g2d.setColor(Color.RED);
        for (Point p : points) {
            g2d.fill(new Ellipse2D.Double(p.getX() - 3, p.getY() - 3, 6, 6));
        }
    }

    private void drawHighlightPoint(Graphics2D g2d, Point point) {
        g2d.setColor(Color.GREEN);
        g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));
    }

    private void drawHighlightSegment(Graphics2D g2d, GraphPositionUtils.PathSegment segment) {
        if (segment.isBezier()) {
            drawBezierCurve(g2d, segment);
        } else {
            drawLineSegment(g2d, segment);
        }
        g2d.setColor(Color.GREEN);
        for (Point p : segment.getPoints()) {
            g2d.fill(new Ellipse2D.Double(p.getX() - 5, p.getY() - 5, 10, 10));
        }
    }

    public static void main(String[] args) {
        JFrame frame = new JFrame("Path Visualizer");
        GraphPositionUtils pathUtils = new GraphPositionUtils();

        // Add points
        pathUtils.addPoint(1, new Point(100.0, 100.0));
        pathUtils.addPoint(2, new Point(200.0, 200.0));
        pathUtils.addPoint(3, new Point(300.0, 100.0));
        pathUtils.addPoint(4, new Point(400.0, 200.0));

        // Add segments
        pathUtils.addSegment(new GraphPositionUtils.PathSegment(1, Arrays.asList(
                new Point(100.0, 100.0),
                new Point(200.0, 200.0)
        ), false));
        Point[] points = {new Point(200.0, 200.0),
                new Point(250.0, 150.0),
                new Point(300.0, 150.0),
                new Point(350.0, 200.0)};
        pathUtils.addSegment(new GraphPositionUtils.PathSegment(2, Arrays.asList(
                points
        ), true));
        pathUtils.addSegment(new GraphPositionUtils.PathSegment(3, Arrays.asList(
                new Point(300.0, 100.0),
                new Point(400.0, 200.0)
        ), false));
        pathUtils.addSegment(new GraphPositionUtils.PathSegment(4, Arrays.asList(
                new Point(400.0, 200.0),
                new Point(100.0, 100.0)
        ), false));

        List<Point> points1 = PathUtils.splitBezierCurve(points[0], points[1], points[2], points[3], 10);
        points1.forEach(p -> {
            Map<String, Object> position = pathUtils.findPosition(p.getX(), p.getY() );
            System.out.println("position = " + JSON.toJSONString( position ));
        });

        PathVisualizer visualizer = new PathVisualizer(pathUtils);
        frame.add(visualizer);
        frame.setSize(800, 600);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);

        // Highlight example points and segments
        visualizer.highlightPoint(new Point(150.0, 150.0)); // Example point
        visualizer.highlightSegment(new GraphPositionUtils.PathSegment(2, Arrays.asList(
                new Point(200.0, 200.0),
                new Point(250.0, 150.0),
                new Point(300.0, 150.0),
                new Point(350.0, 200.0)
        ), true)); // Example segment
    }
}
