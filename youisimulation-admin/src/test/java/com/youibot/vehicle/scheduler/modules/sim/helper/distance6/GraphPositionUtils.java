package com.youibot.vehicle.scheduler.modules.sim.helper.distance6;

import com.youibot.vehicle.scheduler.modules.sim.dto.Point;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class GraphPositionUtils {


    public static class PathSegment {
        private int id;
        private List<Point> points;
        private boolean isBezier;

        public PathSegment(int id, List<Point> points, boolean isBezier) {
            if (isBezier && points.size() != 4) {
                throw new IllegalArgumentException("Bezier curve must have exactly 4 points.");
            }
            if (!isBezier && points.size() != 2) {
                throw new IllegalArgumentException("Line segment must have exactly 2 points.");
            }
            this.id = id;
            this.points = points;
            this.isBezier = isBezier;
        }

        public int getId() { return id; }
        public List<Point> getPoints() { return points; }
        public boolean isBezier() { return isBezier; }
    }

    List<PathSegment> segments = new ArrayList<>();
    private Map<Integer, Point> pointsMap = new HashMap<>();

    public void addSegment(PathSegment segment) {
        segments.add(segment);
    }

    public void addPoint(int id, Point point) {
        pointsMap.put(id, point);
    }

    public Map<String, Object> findPosition(double x, double y) {
        Point p = new Point(x, y);
        Map<String, Object> result = new HashMap<>();

        // Check if the point is exactly on a predefined point
        for (Map.Entry<Integer, Point> entry : pointsMap.entrySet()) {
            Point point = entry.getValue();
            if (Math.abs(p.getX() - point.getX()) < 1e-6 && Math.abs(p.getY() - point.getY()) < 1e-6) {
                result.put("type", "point");
                result.put("id", entry.getKey());
                return result;
            }
        }

        // Check if the point is on a path segment
        for (PathSegment segment : segments) {
            if (segment.isBezier()) {
                double t = findBezierParameter(p, segment);
                if (t >= 0 && t <= 1) {
                    result.put("type", "path");
                    result.put("id", segment.getId());
                    result.put("percent", t * 100);
                    return result;
                }
            } else {
                if (isPointOnLine(p, segment)) {
                    result.put("type", "path");
                    result.put("id", segment.getId());
                    result.put("percent", calculateLinePercent(p, segment));
                    return result;
                }
            }
        }

        result.put("type", "none");
        return result;
    }

    private boolean isPointOnLine(Point p, PathSegment segment) {
        List<Point> points = segment.getPoints();
        Point p0 = points.get(0);
        Point p1 = points.get(1);
        double epsilon = 1e-6;

        double lineLength = p0.minus(p1).getNorm();
        double d1 = p0.minus(p).getNorm();
        double d2 = p.minus(p1).getNorm();

        return Math.abs(d1 + d2 - lineLength) < epsilon;
    }

    private double calculateLinePercent(Point p, PathSegment segment) {
        List<Point> points = segment.getPoints();
        Point p0 = points.get(0);
        Point p1 = points.get(1);

        double totalLength = p0.minus(p1).getNorm();
        double partLength = p0.minus(p).getNorm();

        return partLength / totalLength;
    }

    private double findBezierParameter(Point p, PathSegment segment) {
        List<Point> points = segment.getPoints();
        Point p0 = points.get(0);
        Point p1 = points.get(1);
        Point p2 = points.get(2);
        Point p3 = points.get(3);

        double epsilon = 1e-6;
        for (double t = 0; t <= 1; t += 0.01) {
            double x = Math.pow(1 - t, 3) * p0.getX() +
                       3 * Math.pow(1 - t, 2) * t * p1.getX() +
                       3 * (1 - t) * Math.pow(t, 2) * p2.getX() +
                       Math.pow(t, 3) * p3.getX();
            double y = Math.pow(1 - t, 3) * p0.getY() +
                       3 * Math.pow(1 - t, 2) * t * p1.getY() +
                       3 * (1 - t) * Math.pow(t, 2) * p2.getY() +
                       Math.pow(t, 3) * p3.getY();
            Point bezierPoint = new Point(x, y);
            if (p.minus(bezierPoint).getNorm() < epsilon) {
                return t;
            }
        }
        return -1;
    }
}
