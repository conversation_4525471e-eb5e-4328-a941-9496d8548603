package com.youibot.vehicle.scheduler;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class InterfaceStringConstantsUtil {

    public static List<String> getStringConstants(Class<?>... interfaces) {
        List<String> stringConstants = new ArrayList<>();

        for (Class<?> iface : interfaces) {
            if (iface.isInterface()) {
                for (Field field : iface.getDeclaredFields()) {
                    if (isStringConstant(field)) {
                        try {
                            stringConstants.add((String) field.get(null));
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException("Unable to access the constant: " + field.getName(), e);
                        }
                    }
                }
            } else {
                throw new IllegalArgumentException(iface.getName() + " is not an interface");
            }
        }

        return stringConstants;
    }

    private static boolean isStringConstant(Field field) {
        int modifiers = field.getModifiers();
        return String.class.equals(field.getType())
                && java.lang.reflect.Modifier.isPublic(modifiers)
                && java.lang.reflect.Modifier.isStatic(modifiers)
                && java.lang.reflect.Modifier.isFinal(modifiers);
    }

    public static void main(String[] args) {
        // Example usage with sample interfaces
        List<String> constants = getStringConstants(SampleInterface1.class, SampleInterface2.class);
        constants.forEach(System.out::println);
    }
}

// Sample interfaces for demonstration
interface SampleInterface1 {
    String CONSTANT1 = "Value1";
    int NON_STRING_CONSTANT = 123;
}

interface SampleInterface2 {
    String CONSTANT2 = "Value2";
    String CONSTANT3 = "Value3";
}
