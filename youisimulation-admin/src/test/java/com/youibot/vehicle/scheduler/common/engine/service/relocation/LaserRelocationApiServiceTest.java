package com.youibot.vehicle.scheduler.common.engine.service.relocation;

import com.youibot.vehicle.scheduler.AdminApplication;
import com.youibot.vehicle.scheduler.common.engine.param.LaserDataVo;
import com.youibot.vehicle.scheduler.common.engine.service.map.DefaultMapApiService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.List;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = AdminApplication.class)
@SpringBootTest
public class LaserRelocationApiServiceTest {

    @Autowired
    private  LaserRelocationApiService laserRelocationApiService;

    @Autowired
    private DefaultMapApiService defaultMapApiService;

    @Test
    public void manualRelocation() throws IOException {
        List<LaserDataVo> laserDataVos = laserRelocationApiService.queryLaserData("127.0.0.1");
        System.out.println("laserDataVos = " + laserDataVos);
    }

}