/*
package com.youibot.vehicle.scheduler.modules.map.utils;

import co.paralleluniverse.fibers.Fiber;
import co.paralleluniverse.fibers.SuspendExecution;

public class FiberExample {
    public static void main(String[] args) throws Exception {
        // 创建并启动5个Fiber
        for (int i = 0; i < 5; i++) {
            new Fiber<Void>() {
                @Override
                protected Void run() throws SuspendExecution, InterruptedException {
                    // 模拟任务执行
                    System.out.println("Fiber " + Fiber.currentFiber().getName() + " is running.");
                    Fiber.sleep(1000);  // 模拟一些耗时操作
                    System.out.println("Fiber " + Fiber.currentFiber().getName() + " has finished.");
                    return null;
                }
            }.start();  // 启动 Fiber
        }

        // 等待所有 Fiber 完成
        Thread.sleep(2000); // 主线程等待Fiber执行完成
    }
}
*/
