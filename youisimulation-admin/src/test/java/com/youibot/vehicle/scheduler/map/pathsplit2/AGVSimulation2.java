package com.youibot.vehicle.scheduler.map.pathsplit2;

import com.youibot.vehicle.scheduler.modules.sim.dto.Point;
import javax.swing.*;
import java.awt.*;
import java.awt.geom.*;
import java.util.List;
public class AGVSimulation2 extends JPanel {
    private static final int WIDTH = 800;
    private static final int HEIGHT = 600;

    private List<Point> pathPoints;
    private int currentIndex = 0;
    private double speed = 1.0; // Speed of AGV (points per frame)

    public AGVSimulation2(List<Point> pathPoints) {
        this.pathPoints = pathPoints;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        drawPath(g2d);
        drawAGV(g2d);
        updateAGVPosition();
    }

    private void drawPath(Graphics2D g2d) {
        g2d.setColor(Color.BLACK);
        GeneralPath path = new GeneralPath();
        if (!pathPoints.isEmpty()) {
            Point firstPoint = pathPoints.get(0);
            path.moveTo(firstPoint.getX(), firstPoint.getY());
            for (Point point : pathPoints) {
                path.lineTo(point.getX(), point.getY());
            }
            g2d.draw(path);
        }
    }

    private void drawAGV(Graphics2D g2d) {
        if (currentIndex < pathPoints.size()) {
            Point agvPosition = pathPoints.get(currentIndex);
            g2d.setColor(Color.RED);
            g2d.fillOval((int) agvPosition.getX().intValue() - 10, (int) agvPosition.getY().intValue() - 10, 20, 20);
        }
    }

    private void updateAGVPosition() {
        if (currentIndex < pathPoints.size()) {
            currentIndex += speed;
            if (currentIndex >= pathPoints.size()) {
                currentIndex = pathPoints.size() - 1; // Ensure we don't go out of bounds
            }
        }
        repaint();
    }

    public static void main(String[] args) {
        // Example path points (Bezier and Line)
        List<Point> bezierPoints = PathUtils.splitBezierCurve(
            new Point(100.0, 500.0),
            new Point(200.0, 100.0),
            new Point(600.0, 100.0),
            new Point(700.0, 500.0),
            100
        );
        List<Point> linePoints = PathUtils.splitLine(
            new Point(700, 500),
            new Point(800, 200),
            50
        );

        bezierPoints.addAll(linePoints);

        JFrame frame = new JFrame("AGV Simulation");
        AGVSimulation2 panel = new AGVSimulation2(bezierPoints);
        frame.add(panel);
        frame.setSize(WIDTH, HEIGHT);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}
