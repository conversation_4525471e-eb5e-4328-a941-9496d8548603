/*
package com.youibot.vehicle.scheduler.map.mapTest;

import com.mxgraph.layout.mxOrganicLayout;
import com.mxgraph.swing.mxGraphComponent;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import org.jgrapht.Graph;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

public class GraphExample {

    public static void main(String[] args) {
        DirectedWeightedPseudograph<Marker, DefaultWeightedEdge> graph =
                new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        // Add nodes
        for (int i = 1; i <= 50; i++) {
            Marker marker = new Marker("code" + i, "Marker" + i, null, "Type", "VehicleMapCode", 1, 1, 0, "DockingType", 1,null);
            graph.addVertex(marker);
        }

        // Add edges
        Random rand = new Random();
        for (int i = 0; i < 150; i++) {
            Marker source = (Marker) graph.vertexSet().toArray()[rand.nextInt(graph.vertexSet().size())];
            Marker target = (Marker) graph.vertexSet().toArray()[rand.nextInt(graph.vertexSet().size())];
            if (!source.equals(target)) {
                DefaultWeightedEdge edge = graph.addEdge(source, target);
                graph.setEdgeWeight(edge, rand.nextDouble() * 10 + 1);  // Set random weight between 1 and 10
            }
        }

        // Example of nodes to highlight
        Set<Marker> highlightedNodes = new HashSet<>(graph.vertexSet().stream().limit(5).collect(Collectors.toSet()));

        // Visualize graph
        visualizeGraph(graph, highlightedNodes);
    }

    private static void visualizeGraph(Graph<Marker, DefaultWeightedEdge> graph, Set<Marker> highlightedNodes) {
        // Create JGraphX adapter
        JGraphXAdapter<Marker, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // Create a Swing window to display the graph
        JFrame frame = new JFrame();
        frame.setLayout(new BorderLayout());

        // Graph panel
        mxGraphComponent graphComponent = new mxGraphComponent(graphAdapter);
        frame.add(graphComponent, BorderLayout.CENTER);

        // Use organic layout
        mxOrganicLayout layout = new mxOrganicLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());

        // Highlight nodes
        graphAdapter.getModel().beginUpdate();
        try {
            // Define colors
            Color highlightColor = Color.RED;
            String highlightStyle = "fillColor=" + toHexString(highlightColor) + ";strokeColor=" + toHexString(highlightColor) + ";";

            // Highlight nodes
            for (Marker node : highlightedNodes) {
                graphAdapter.setCellStyle(highlightStyle, new Object[]{graphAdapter.getVertexToCellMap().get(node)});
            }

            // Adjust edge labels
            for (DefaultWeightedEdge edge : graph.edgeSet()) {
                Object edgeCell = graphAdapter.getEdgeToCellMap().get(edge);
                Marker source = graph.getEdgeSource(edge);
                Marker target = graph.getEdgeTarget(edge);
                double weight = graph.getEdgeWeight(edge);

                // Create label with source, target, and weight
                String label = String.format("%s -> %s (%.2f)", source.getName(), target.getName(), weight);
                graphAdapter.getModel().setValue(edgeCell, label);
            }
        } finally {
            graphAdapter.getModel().endUpdate();
        }

        // Add the graph component to the frame
        frame.setTitle("Graph Visualization with Edge Labels");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        SwingUtilities.invokeLater(() -> frame.setVisible(true));
    }

    private static String toHexString(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }
}
*/
