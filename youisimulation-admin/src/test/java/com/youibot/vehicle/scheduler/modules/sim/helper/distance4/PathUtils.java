package com.youibot.vehicle.scheduler.modules.sim.helper.distance4;

import com.alibaba.fastjson.JSON;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.geom.CubicCurve2D;
import java.awt.geom.Ellipse2D;
import java.awt.geom.Line2D;
import java.awt.geom.Point2D;
import java.util.List;
import java.util.*;

public class PathUtils {

    // Define a segment class to handle both line and Bezier curve segments
    public static class PathSegment {
        private final int id;
        private final List<Point2D.Double> points;
        private final boolean isBezier;

        public PathSegment(int id, List<Point2D.Double> points, boolean isBezier) {
            if (isBezier && points.size() != 4) {
                throw new IllegalArgumentException("Bezier curve must have exactly 4 points.");
            }
            if (!isBezier && points.size() != 2) {
                throw new IllegalArgumentException("Line segment must have exactly 2 points.");
            }
            this.id = id;
            this.points = points;
            this.isBezier = isBezier;
        }

        public int getId() {
            return id;
        }

        public List<Point2D.Double> getPoints() {
            return points;
        }

        public boolean isBezier() {
            return isBezier;
        }
    }

    // Define a class to store the position result
    public static class PositionResult {
        private final boolean isPoint;
        private final Integer pointId;
        private final Integer segmentId;
        private final double percentage;

        public PositionResult(boolean isPoint, Integer pointId, Integer segmentId, double percentage) {
            this.isPoint = isPoint;
            this.pointId = pointId;
            this.segmentId = segmentId;
            this.percentage = percentage;
        }

        public boolean isPoint() {
            return isPoint;
        }

        public Integer getPointId() {
            return pointId;
        }

        public Integer getSegmentId() {
            return segmentId;
        }

        public double getPercentage() {
            return percentage;
        }
    }

    private final Map<Integer, Point2D.Double> points = new HashMap<>();
    final List<PathSegment> segments = new ArrayList<>();

    public void addPoint(int id, Point2D.Double point) {
        points.put(id, point);
    }

    public void addSegment(PathSegment segment) {
        segments.add(segment);
    }

    public PositionResult findPosition(double x, double y) {
        Point2D.Double queryPoint = new Point2D.Double(x, y);

        // Check if the point is one of the predefined points
        for (Map.Entry<Integer, Point2D.Double> entry : points.entrySet()) {
            if (queryPoint.equals(entry.getValue())) {
                return new PositionResult(true, entry.getKey(), null, 0);
            }
        }

        // Check if the point is on one of the segments
        for (PathSegment segment : segments) {
            if (segment.isBezier()) {
                double[] t = new double[1];
                Point2D.Double closestPoint = findClosestPointOnBezier(segment.getPoints(), queryPoint, t);
                if (closestPoint != null && isPointNear(queryPoint, closestPoint)) {
                    return new PositionResult(false, null, segment.getId(), t[0]);
                }
            } else {
                double[] t = new double[1];
                Point2D.Double closestPoint = findClosestPointOnLine(segment.getPoints(), queryPoint, t);
                if (closestPoint != null && isPointNear(queryPoint, closestPoint)) {
                    return new PositionResult(false, null, segment.getId(), t[0]);
                }
            }
        }

        // If not found, return null or appropriate result
        return new PositionResult(false, null, null, -1);
    }

    private Point2D.Double findClosestPointOnBezier(List<Point2D.Double> points, Point2D.Double queryPoint, double[] t) {
        CubicCurve2D curve = new CubicCurve2D.Double(
                points.get(0).getX(), points.get(0).getY(),
                points.get(1).getX(), points.get(1).getY(),
                points.get(2).getX(), points.get(2).getY(),
                points.get(3).getX(), points.get(3).getY()
        );

        double minDist = Double.MAX_VALUE;
        Point2D.Double closestPoint = null;
        double closestT = 0;

        for (double i = 0; i <= 1; i += 0.01) {
            Point2D.Double curvePoint = new Point2D.Double(
                    cubicBezierX(points, i),
                    cubicBezierY(points, i)
            );
            double dist = queryPoint.distance(curvePoint);
            if (dist < minDist) {
                minDist = dist;
                closestPoint = curvePoint;
                closestT = i;
            }
        }

        if (closestPoint != null) {
            t[0] = closestT;
        }

        return closestPoint;
    }

    private Point2D.Double findClosestPointOnLine(List<Point2D.Double> points, Point2D.Double queryPoint, double[] t) {
        Line2D line = new Line2D.Double(points.get(0), points.get(1));
        double lineLength = line.getP1().distance(line.getP2());
        double lineT = ((queryPoint.getX() - line.getX1()) * (line.getX2() - line.getX1()) +
                (queryPoint.getY() - line.getY1()) * (line.getY2() - line.getY1())) / (lineLength * lineLength);
        lineT = Math.max(0, Math.min(1, lineT));

        Point2D.Double closestPoint = new Point2D.Double(
                line.getX1() + lineT * (line.getX2() - line.getX1()),
                line.getY1() + lineT * (line.getY2() - line.getY1())
        );

        t[0] = lineT;
        return closestPoint;
    }

    private double cubicBezierX(List<Point2D.Double> points, double t) {
        double x0 = points.get(0).getX();
        double x1 = points.get(1).getX();
        double x2 = points.get(2).getX();
        double x3 = points.get(3).getX();
        return Math.pow(1 - t, 3) * x0 +
               3 * Math.pow(1 - t, 2) * t * x1 +
               3 * (1 - t) * Math.pow(t, 2) * x2 +
               Math.pow(t, 3) * x3;
    }

    private double cubicBezierY(List<Point2D.Double> points, double t) {
        double y0 = points.get(0).getY();
        double y1 = points.get(1).getY();
        double y2 = points.get(2).getY();
        double y3 = points.get(3).getY();
        return Math.pow(1 - t, 3) * y0 +
               3 * Math.pow(1 - t, 2) * t * y1 +
               3 * (1 - t) * Math.pow(t, 2) * y2 +
               Math.pow(t, 3) * y3;
    }

    private boolean isPointNear(Point2D.Double queryPoint, Point2D.Double closestPoint) {
        return queryPoint.distance(closestPoint) < 5; // Tolerance distance
    }
}

class PathVisualizer extends JPanel {
    private final PathUtils pathUtils;
    private Point2D.Double highlightPoint;
    private PathUtils.PositionResult positionResult;

    public PathVisualizer(PathUtils pathUtils) {
        this.pathUtils = pathUtils;
        this.highlightPoint = null;
        this.positionResult = null;
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                highlightPoint = new Point2D.Double(e.getX(), e.getY());
                positionResult = pathUtils.findPosition(e.getX(), e.getY());
                repaint();
            }
        });
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        drawPaths(g2d);
        if (highlightPoint != null) {
            drawHighlight(g2d);
        }
    }

    private void drawPaths(Graphics2D g2d) {
        for (PathUtils.PathSegment segment : pathUtils.segments) {
            if (segment.isBezier()) {
                drawBezierCurve(g2d, segment);
            } else {
                drawLineSegment(g2d, segment);
            }
        }
    }

    private void drawBezierCurve(Graphics2D g2d, PathUtils.PathSegment segment) {
        List<Point2D.Double> points = segment.getPoints();
        CubicCurve2D curve = new CubicCurve2D.Double(
                points.get(0).getX(), points.get(0).getY(),
                points.get(1).getX(), points.get(1).getY(),
                points.get(2).getX(), points.get(2).getY(),
                points.get(3).getX(), points.get(3).getY()
        );
        g2d.setColor(Color.BLUE);
        g2d.draw(curve);

        // Draw endpoints
        g2d.setColor(Color.RED);
        for (Point2D.Double point : points) {
            g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));
        }
    }

    private void drawLineSegment(Graphics2D g2d, PathUtils.PathSegment segment) {
        List<Point2D.Double> points = segment.getPoints();
        Line2D line = new Line2D.Double(points.get(0), points.get(1));
        g2d.setColor(Color.BLUE);
        g2d.draw(line);

        // Draw endpoints
        g2d.setColor(Color.RED);
        for (Point2D.Double point : points) {
            g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));
        }
    }

    private void drawHighlight(Graphics2D g2d) {
        if (positionResult.isPoint()) {
            g2d.setColor(Color.GREEN);
            g2d.fill(new Ellipse2D.Double(highlightPoint.getX() - 5, highlightPoint.getY() - 5, 10, 10));
            g2d.setColor(Color.BLACK);
            g2d.drawString("Point ID: " + positionResult.getPointId(), (int) highlightPoint.getX(), (int) highlightPoint.getY() - 10);
        } else {
            g2d.setColor(Color.GREEN);
            if (positionResult.getSegmentId() != null) {
                PathUtils.PathSegment seg = pathUtils.segments.stream()
                    .filter(s -> s.getId() == positionResult.getSegmentId())
                    .findFirst().orElse(null);
                if (seg != null) {
                    if (seg.isBezier()) {
                        drawBezierCurve(g2d, seg);
                    } else {
                        drawLineSegment(g2d, seg);
                    }
                }
                g2d.setColor(Color.BLACK);
                g2d.drawString(String.format("Segment ID: %d, Position: %.2f%%", positionResult.getSegmentId(), positionResult.getPercentage() * 100),
                        (int) highlightPoint.getX(), (int) highlightPoint.getY() - 10);
            }
        }
    }

    public static void main(String[] args) {
        PathUtils pathUtils = new PathUtils();

        // Add some points
        pathUtils.addPoint(1, new Point2D.Double(100, 100));
        pathUtils.addPoint(2, new Point2D.Double(200, 100));
        pathUtils.addPoint(3, new Point2D.Double(300, 200));
        pathUtils.addPoint(4, new Point2D.Double(400, 300));
        pathUtils.addPoint(5, new Point2D.Double(500, 200));

        // Add some segments
        pathUtils.addSegment(new PathUtils.PathSegment(1, Arrays.asList(
                new Point2D.Double(100, 100),
                new Point2D.Double(200, 100)
        ), false));

        pathUtils.addSegment(new PathUtils.PathSegment(2, Arrays.asList(
                new Point2D.Double(200, 100),
                new Point2D.Double(300, 200)
        ), false));

        pathUtils.addSegment(new PathUtils.PathSegment(3, Arrays.asList(
                new Point2D.Double(300, 200),
                new Point2D.Double(400, 300)
        ), false));

        pathUtils.addSegment(new PathUtils.PathSegment(4, Arrays.asList(
                new Point2D.Double(400, 300),
                new Point2D.Double(500, 200)
        ), false));

        pathUtils.addSegment(new PathUtils.PathSegment(5, Arrays.asList(
                new Point2D.Double(500, 200),
                new Point2D.Double(600, 100),
                new Point2D.Double(700, 300),
                new Point2D.Double(800, 400)
        ), true));

        PathUtils.PositionResult position = pathUtils.findPosition(400, 300);
        System.out.println("Position: " + JSON.toJSONString( position ));
        JFrame frame = new JFrame("Path Visualizer");
        PathVisualizer panel = new PathVisualizer(pathUtils);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.add(panel);
        frame.setVisible(true);
    }
}
