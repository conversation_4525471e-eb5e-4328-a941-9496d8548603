/*
package com.youibot.vehicle.scheduler;

import com.mxgraph.layout.mxCircleLayout;
import com.mxgraph.swing.mxGraphComponent;
import org.jgrapht.Graph;
import org.jgrapht.alg.interfaces.AStarAdmissibleHeuristic;
import org.jgrapht.alg.shortestpath.AStarShortestPath;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.stream.Collectors;

public class AStarMultiplePathsVisualization {

    public static void main(String[] args) {
        // 创建一个有向加权伪图
        Graph<String, DefaultWeightedEdge> graph = new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        // 添加顶点
        graph.addVertex("A");
        graph.addVertex("B");
        graph.addVertex("C");
        graph.addVertex("D");
        graph.addVertex("E");

        // 添加边及其权重
        graph.setEdgeWeight(graph.addEdge("A", "B"), 2.0);
        graph.setEdgeWeight(graph.addEdge("A", "C"), 1.0);
        graph.setEdgeWeight(graph.addEdge("B", "D"), 1.0);
        graph.setEdgeWeight(graph.addEdge("C", "D"), 2.0);
        graph.setEdgeWeight(graph.addEdge("B", "E"), 4.0);
        graph.setEdgeWeight(graph.addEdge("D", "E"), 1.0);
        graph.setEdgeWeight(graph.addEdge("A", "D"), 4.0);
        graph.setEdgeWeight(graph.addEdge("C", "E"), 3.0);

        // 定义启发式函数
        AStarAdmissibleHeuristic<String> heuristic = (source, target) -> Math.abs(source.charAt(0) - target.charAt(0));

        // 可视化图并高亮路径
        visualizeGraph(graph, "A", "E", heuristic);
    }

    private static void visualizeGraph(Graph<String, DefaultWeightedEdge> graph, 
                                       String start, String end,
                                       AStarAdmissibleHeuristic<String> heuristic) {
        AStarShortestPath<String, DefaultWeightedEdge> aStarAlg = new AStarShortestPath<>(graph, heuristic);

        // 获取所有最短路径
        List<List<DefaultWeightedEdge>> allPaths = getAllShortestPaths(graph, start, end, aStarAlg);

        // 创建JGraphX适配器
        JGraphXAdapter<String, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // 创建一个Swing窗口来展示图
        JFrame frame = new JFrame();
        frame.getContentPane().add(new mxGraphComponent(graphAdapter));
        frame.setTitle("AStarShortestPath Visualization");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.setVisible(true);

        // 使用圆形布局将图形展现出来
        mxCircleLayout layout = new mxCircleLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());

        // 使用不同颜色显示不同路径
        Color[] colors = {Color.RED, Color.BLUE, Color.GREEN, Color.ORANGE, Color.MAGENTA};
        
        for (int i = 0; i < allPaths.size(); i++) {
            List<DefaultWeightedEdge> path = allPaths.get(i);
            Color color = colors[i % colors.length];

            for (DefaultWeightedEdge edge : path) {
                // 显示边的权重
                String edgeLabel = String.format("%.2f", graph.getEdgeWeight(edge));
                graphAdapter.getModel().setValue(graphAdapter.getEdgeToCellMap().get(edge), edgeLabel);

                // 高亮路径
                String style = String.format("strokeColor=%s", toHexString(color));
                graphAdapter.setCellStyle(style, new Object[]{graphAdapter.getEdgeToCellMap().get(edge)});
            }
        }
    }

    private static List<List<DefaultWeightedEdge>> getAllShortestPaths(Graph<String, DefaultWeightedEdge> graph, 
                                                                       String start, String end, 
                                                                       AStarShortestPath<String, DefaultWeightedEdge> aStarAlg) {
        // 获取最短路径的权重
        double minPathWeight = aStarAlg.getPath(start, end).getWeight();
        
        // 过滤出所有最短路径
        return graph.edgeSet().stream()
                .map(edge -> {
                    // 过滤边到当前路径的计算
                    AStarShortestPath<String, DefaultWeightedEdge> tempAStar = new AStarShortestPath<>(graph, (s, t) -> heuristic(s, t));
                    return tempAStar.getPath(start, end) != null ? tempAStar.getPath(start, end).getEdgeList() : null;
                })
                .filter(path -> path != null && !path.isEmpty() && 
                        path.stream().map(graph::getEdgeWeight).reduce(0.0, Double::sum).equals(minPathWeight))
                .collect(Collectors.toList());
    }

    private static double heuristic(String source, String target) {
        // 启发式函数实现，可以自定义
        return Math.abs(source.charAt(0) - target.charAt(0));
    }

    private static String toHexString(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }
}
*/
