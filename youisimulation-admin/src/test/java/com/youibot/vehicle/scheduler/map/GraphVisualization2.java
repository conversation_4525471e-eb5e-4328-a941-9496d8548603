/*
package com.youibot.vehicle.scheduler.map;

import com.alibaba.fastjson.JSON;
import com.mxgraph.layout.mxCircleLayout;
import com.mxgraph.swing.mxGraphComponent;
import com.youibot.vehicle.scheduler.modules.map.dto.PathInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.MapGraphInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import org.apache.commons.io.FileUtils;
import org.jgrapht.Graph;
import org.jgrapht.alg.shortestpath.AllDirectedPaths;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class GraphVisualization2 {

    private static  String roadNet ="E:\\home\\youibot\\youibot_map\\map724_roadnet\\locating\\current\\map724.roadnet" ;

    public static void main(String[] args) throws IOException {
        // Create a directed weighted graph
      */
/*  Graph<Marker, DefaultWeightedEdge> graph = new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        // Create and add Marker nodes
        Marker markerA = new Marker("A", "Start A", 0.0, "Type1", "Map1", 1, 1, 0, "TypeA", 1);
        Marker markerB = new Marker("B", "Point B", 0.0, "Type2", "Map1", 1, 1, 1, "TypeB", 2);
        Marker markerC = new Marker("C", "Point C", 0.0, "Type3", "Map1", 1, 0, 0, "TypeC", 1);
        Marker markerD =new Marker("D", "Point D", 0.0, "Type3", "Map1", 1, 0, 0, "TypeC", 1);
        Marker markerE =new Marker("E", "Point E", 0.0, "Type3", "Map1", 1, 0, 0, "TypeC", 1);
        Marker markerF = new Marker("F", "Point F", 0.0, "Type3", "Map1", 1, 0, 0, "TypeC", 1);
        Marker markerG = new Marker("G", "Point G", 0.0, "Type3", "Map1", 1, 0, 0, "TypeC", 1);
        Marker markerH = new Marker("H", "End Point H ", 0.0, "Type3", "Map1", 1, 0, 0, "TypeC", 1);

        // Add vertices to the graph
        graph.addVertex(markerA);
        graph.addVertex(markerB);
        graph.addVertex(markerC);
        graph.addVertex(markerD);
        graph.addVertex(markerE);
        graph.addVertex(markerF);
        graph.addVertex(markerG);
        graph.addVertex(markerH);*//*


        String s = FileUtils.readFileToString(new File(roadNet), "UTF-8");

        MapGraphInfo mapGraphInfo = JSON.parseObject(s, MapGraphInfo.class);

        Graph<Marker, DefaultWeightedEdge> graph = new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        Map<String, Marker> markerMap = new HashMap<>();

        mapGraphInfo.getMarkers().forEach( marker -> {

            graph.addVertex(marker);
            markerMap.put(marker.getCode(), marker);
        });
        // 添加顶点
        mapGraphInfo.getPaths().forEach( path -> {
            List<PathInfo> pathInfos = path.getPathInfos();
            PathInfo pathInfo = pathInfos.get(0);

            addEdge(graph, markerMap.get( path.getStartMarkerCode() ), markerMap.get( path.getEndMarkerCode() ), pathInfo.getLength());
        });



        // Visualize the graph and display paths
        Marker markerA = markerMap.get("map724_P_7");
        Marker markerH = markerMap.get("map724_P_4");
        System.out.println(markerA);
        System.out.println(markerH);
        // Visualize the graph and display paths
        visualizeGraph(graph, markerA, markerH);
    }

    private static void addEdge(Graph<Marker, DefaultWeightedEdge> graph, Marker source, Marker target, double weight) {
        DefaultWeightedEdge edge = graph.addEdge(source, target);
        graph.setEdgeWeight(edge, weight);
    }

    private static void visualizeGraph(Graph<Marker, DefaultWeightedEdge> graph, Marker start, Marker end) {
        // Get all paths with a maximum length to avoid infinite cycles
        List<PathWithWeight> allPaths = getAllPaths(graph, start, end, 10);

        // Sort paths by their total weight
        allPaths.sort(Comparator.comparingDouble(PathWithWeight::getTotalWeight));

        // Create JGraphX adapter
        JGraphXAdapter<Marker, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // Create a Swing window to display the graph
        JFrame frame = new JFrame();
        frame.setLayout(new BorderLayout());

        // Graph panel
        mxGraphComponent graphComponent = new mxGraphComponent(graphAdapter);
        frame.add(graphComponent, BorderLayout.CENTER);

        // Create a panel to list all paths
        JPanel pathPanel = new JPanel();
        pathPanel.setLayout(new GridLayout(allPaths.size(), 1));

        // Define colors
        Color[] colors = {Color.RED, Color.BLUE, Color.GREEN, Color.ORANGE, Color.MAGENTA};

        // Display paths and highlight them
        graphAdapter.getModel().beginUpdate();
        try {
            // Add each path to the panel and visualize it
            for (int i = 0; i < allPaths.size(); i++) {
                PathWithWeight pathWithWeight = allPaths.get(i);
                List<DefaultWeightedEdge> path = pathWithWeight.getPath();
                Color color = colors[i % colors.length];
                
                // Add path description to the panel
                String pathDescription = path.stream()
                        .map(edge -> String.format("%s->%s(%.2f)", graph.getEdgeSource(edge).getCode(), graph.getEdgeTarget(edge).getCode(), graph.getEdgeWeight(edge)))
                        .collect(Collectors.joining(" -> "));
                JLabel pathLabel = new JLabel(String.format("Path %d (Weight: %.2f): %s", i + 1, pathWithWeight.getTotalWeight(), pathDescription));
                pathLabel.setForeground(color);
                pathPanel.add(pathLabel);

                // Highlight the path
                for (DefaultWeightedEdge edge : path) {
                    String style = String.format("strokeColor=%s;strokeWidth=2;", toHexString(color));
                    graphAdapter.setCellStyle(style, new Object[]{graphAdapter.getEdgeToCellMap().get(edge)});
                }
            }
        } finally {
            graphAdapter.getModel().endUpdate();
        }

        // Add path panel to the frame
        frame.add(new JScrollPane(pathPanel), BorderLayout.SOUTH);

        frame.setTitle("Graph Visualization with Multiple Paths");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        SwingUtilities.invokeLater(() -> frame.setVisible(true));

        // Use circle layout to display the graph
        mxCircleLayout layout = new mxCircleLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());
    }

    private static List<PathWithWeight> getAllPaths(Graph<Marker, DefaultWeightedEdge> graph, Marker start, Marker end, int maxPathLength) {
        AllDirectedPaths<Marker, DefaultWeightedEdge> allPathsFinder = new AllDirectedPaths<>(graph);
        return allPathsFinder.getAllPaths(start, end, false, maxPathLength)
                .stream()
                .map(path -> new PathWithWeight(path.getEdgeList(), path.getWeight()))
                .collect(Collectors.toList());
    }

    private static String toHexString(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }

    public static class PathWithWeight {
        private final List<DefaultWeightedEdge> path;
        private final double totalWeight;

        public PathWithWeight(List<DefaultWeightedEdge> path, double totalWeight) {
            this.path = path;
            this.totalWeight = totalWeight;
        }

        public List<DefaultWeightedEdge> getPath() {
            return path;
        }

        public double getTotalWeight() {
            return totalWeight;
        }
    }
}
*/
