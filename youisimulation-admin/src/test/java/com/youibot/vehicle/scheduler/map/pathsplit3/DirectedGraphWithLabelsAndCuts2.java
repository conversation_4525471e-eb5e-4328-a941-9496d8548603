package com.youibot.vehicle.scheduler.map.pathsplit3;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.*;
import java.util.ArrayList;
import java.util.List;

public class DirectedGraphWithLabelsAndCuts2 extends JPanel {

    private List<GeneralPath> edges;
    private List<Point2D.Double> vertices;
    private List<String> edgeLabels;
    private List<String> vertexLabels;
    private List<List<Point2D.Double>> cutPoints;
    private int cutEdgeIndex;

    public DirectedGraphWithLabelsAndCuts2(List<GeneralPath> edges, List<Point2D.Double> vertices,
                                           List<String> edgeLabels, List<String> vertexLabels,
                                           List<List<Point2D.Double>> cutPoints, int cutEdgeIndex) {
        this.edges = edges;
        this.vertices = vertices;
        this.edgeLabels = edgeLabels;
        this.vertexLabels = vertexLabels;
        this.cutPoints = cutPoints;
        this.cutEdgeIndex = cutEdgeIndex;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // Draw edges
        g2d.setColor(Color.BLUE);
        for (int i = 0; i < edges.size(); i++) {
            GeneralPath path = edges.get(i);
            g2d.draw(path);
            // Draw edge labels
            Rectangle2D bounds = path.getBounds2D();
            g2d.setColor(Color.BLACK);
            g2d.drawString(edgeLabels.get(i), (float) (bounds.getX() + bounds.getWidth() / 2), (float) (bounds.getY() + bounds.getHeight() / 2));
        }

        // Draw vertices
        g2d.setColor(Color.RED);
        for (int i = 0; i < vertices.size(); i++) {
            Point2D.Double vertex = vertices.get(i);
            g2d.fill(new Ellipse2D.Double(vertex.getX() - 5, vertex.getY() - 5, 10, 10));
            // Draw vertex labels
            g2d.setColor(Color.BLACK);
            g2d.drawString(vertexLabels.get(i), (float) vertex.getX() + 10, (float) vertex.getY() - 10);
        }

        // Draw cut points
        g2d.setColor(Color.GREEN);
        if (cutEdgeIndex >= 0 && cutEdgeIndex < cutPoints.size()) {
            for (Point2D.Double point : cutPoints.get(cutEdgeIndex)) {
                g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));
            }
        }
    }

    public static void main(String[] args) {
        int n = 12; // Number of edges
        List<GeneralPath> edges = new ArrayList<>();
        List<Point2D.Double> vertices = new ArrayList<>();
        List<String> edgeLabels = new ArrayList<>();
        List<String> vertexLabels = new ArrayList<>();
        List<List<Point2D.Double>> cutPoints = new ArrayList<>();
        int cutEdgeIndex = 5; // Index of the edge to cut

        // Define a layout for vertices
        double radius = 200;
        double centerX = 300;
        double centerY = 300;
        double angleStep = 2 * Math.PI / n;

        // Create vertices in a circular pattern
        for (int i = 0; i < n; i++) {
            double angle = i * angleStep;
            double x = centerX + radius * Math.cos(angle);
            double y = centerY + radius * Math.sin(angle);
            vertices.add(new Point2D.Double(x, y));
            vertexLabels.add("V" + i);
        }

        // Create edges with alternating Bezier curves and lines
        for (int i = 0; i < n; i++) {
            GeneralPath path = new GeneralPath();
            Point2D.Double start = vertices.get(i);
            Point2D.Double end = vertices.get((i + 1) % n);

            if (i % 2 == 0) {
                // Create a Bezier curve
                Point2D.Double control1 = new Point2D.Double(start.getX() + 50, start.getY() - 50);
                Point2D.Double control2 = new Point2D.Double(end.getX() - 50, end.getY() + 50);
                path.moveTo(start.getX(), start.getY());
                path.curveTo(control1.getX(), control1.getY(), control2.getX(), control2.getY(), end.getX(), end.getY());
            } else {
                // Create a line
                path.moveTo(start.getX(), start.getY());
                path.lineTo(end.getX(), end.getY());
            }

            edges.add(path);
            edgeLabels.add("E" + i);

            // Calculate cut points for the specified edge
            if (i == cutEdgeIndex) {
                List<Point2D.Double> points = new ArrayList<>();
                int numCuts = 10; // Number of cuts
                for (int j = 0; j <= numCuts; j++) {
                    double t = j / (double) numCuts;
                    Point2D.Double cutPoint = getPointOnPath(path, t);
                    points.add(cutPoint);
                }
                cutPoints.add(points);
            } else {
                cutPoints.add(new ArrayList<>()); // No cut points for other edges
            }
        }

        // Create and show the JFrame
        JFrame frame = new JFrame("Directed Graph with Labels and Cuts");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.add(new DirectedGraphWithLabelsAndCuts2(edges, vertices, edgeLabels, vertexLabels, cutPoints, cutEdgeIndex));
        frame.setVisible(true);
    }

    private static Point2D.Double getPointOnPath(GeneralPath path, double t) {
        PathIterator iterator = path.getPathIterator(null);
        double[] coords = new double[6];
        Point2D.Double point = null;
        List<Point2D.Double> segmentPoints = new ArrayList<>();
        while (!iterator.isDone()) {
            int type = iterator.currentSegment(coords);
            switch (type) {
                case PathIterator.SEG_MOVETO:
                case PathIterator.SEG_LINETO:
                    segmentPoints.add(new Point2D.Double(coords[0], coords[1]));
                    break;
                case PathIterator.SEG_CUBICTO:
                    // Store points for cubic Bezier curve segments
                    segmentPoints.add(new Point2D.Double(coords[4], coords[5]));
                    break;
                default:
                    break;
            }
            iterator.next();
        }

        if (segmentPoints.size() >= 2) {
            // Use segment points to compute interpolation
            if (segmentPoints.size() == 2) {
                Point2D.Double p0 = segmentPoints.get(0);
                Point2D.Double p1 = segmentPoints.get(1);
                point = getPointOnLine(new Line2D.Double(p0, p1), t);
            } else if (segmentPoints.size() == 4) {
                Point2D.Double p0 = segmentPoints.get(0);
                Point2D.Double p1 = segmentPoints.get(1);
                Point2D.Double p2 = segmentPoints.get(2);
                Point2D.Double p3 = segmentPoints.get(3);
                point = bezierPoint(new CubicCurve2D.Double(p0.getX(), p0.getY(), p1.getX(), p1.getY(), p2.getX(), p2.getY(), p3.getX(), p3.getY()), t);
            }
        }
        return point != null ? point : new Point2D.Double(0, 0); // Return (0,0) if no point is found
    }

    private static Point2D.Double bezierPoint(CubicCurve2D curve, double t) {
        double x = Math.pow(1 - t, 3) * curve.getX1() +
                   3 * Math.pow(1 - t, 2) * t * curve.getCtrlX1() +
                   3 * (1 - t) * Math.pow(t, 2) * curve.getCtrlX2() +
                   Math.pow(t, 3) * curve.getX2();
        double y = Math.pow(1 - t, 3) * curve.getY1() +
                   3 * Math.pow(1 - t, 2) * t * curve.getCtrlY1() +
                   3 * (1 - t) * Math.pow(t, 2) * curve.getCtrlY2() +
                   Math.pow(t, 3) * curve.getY2();
        return new Point2D.Double(x, y);
    }

    private static Point2D.Double getPointOnLine(Line2D line, double t) {
        double x = (1 - t) * line.getX1() + t * line.getX2();
        double y = (1 - t) * line.getY1() + t * line.getY2();
        return new Point2D.Double(x, y);
    }
}
