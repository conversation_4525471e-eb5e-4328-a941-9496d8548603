package com.youibot.vehicle.scheduler.map.pathsplit2;

import com.youibot.vehicle.scheduler.modules.sim.dto.Point;

import java.util.ArrayList;
import java.util.List;

public class PathUtils {

    public static List<Point> splitBezierCurve(Point p0, Point p1, Point p2, Point p3, int n) {
        List<Point> points = new ArrayList<>();
        for (int i = 0; i <= n; i++) {
            double t = (double) i / n;
            Point point = bezierPoint(p0, p1, p2, p3, t);
            points.add(point);
        }
        return points;
    }

    public static List<Point> splitLine(Point p0, Point p1, int n) {
        List<Point> points = new ArrayList<>();
        for (int i = 0; i <= n; i++) {
            double t = (double) i / n;
            Point point = linePoint(p0, p1, t);
            points.add(point);
        }
        return points;
    }

    private static Point bezierPoint(Point p0, Point p1, Point p2, Point p3, double t) {
        double x = Math.pow(1 - t, 3) * p0.getX() +
                   3 * Math.pow(1 - t, 2) * t * p1.getX() +
                   3 * (1 - t) * Math.pow(t, 2) * p2.getX() +
                   Math.pow(t, 3) * p3.getX();
        double y = Math.pow(1 - t, 3) * p0.getY() +
                   3 * Math.pow(1 - t, 2) * t * p1.getY() +
                   3 * (1 - t) * Math.pow(t, 2) * p2.getY() +
                   Math.pow(t, 3) * p3.getY();
        return new Point(x, y);
    }

    private static Point linePoint(Point p0, Point p1, double t) {
        double x = (1 - t) * p0.getX() + t * p1.getX();
        double y = (1 - t) * p0.getY() + t * p1.getY();
        return new Point(x, y);
    }
}
