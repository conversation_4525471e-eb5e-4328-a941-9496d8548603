package com.youibot.vehicle.scheduler.modules.sim.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.youibot.vehicle.scheduler.common.entity.QueryCol;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.RandomUtil;
import com.youibot.vehicle.scheduler.modules.sim.enums.ExceptionStatus;
import org.apache.poi.ss.formula.functions.T;
import org.junit.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class SimpleTest {

    public static final String YYYY_M_MDD_H_HMMSS = "yyyyMMdd_HHmmss";

    /**
     *         return super.getWrapper(params, QueryCol.builder().likeCol("content,vehicleCodes,taskNos,data").eqCol("type,module,nodeCode").timeCol("createDate,lastTime").build());
     */
    @Test
    public  void test1(){
        Map<String, Object> map = Maps.newHashMap();
        map.put("likeCol","content,vehicleCodes,taskNos,data");
        map.put("eqCol","type,module,nodeCode");
        map.put("timeCol","createDate,lastTime");
         Class<T> beanClass = null;
        QueryCol queryCol = ConvertUtils.mapToBean(map, QueryCol.class);
        System.out.println("JSON.toJSONString( queryCol ) = " + JSON.toJSONString( queryCol ));
    }

    @Test
    public  void test2(){

        LocalDateTime now = LocalDateTime.now();
        // 定义格式化模式
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_M_MDD_H_HMMSS);

        // 格式化 LocalDateTime
        String formattedDateTime = now.format(formatter);
        System.out.println("格式化后的日期和时间：" + formattedDateTime);
    }


    @Test
    public  void test3(){

        ExceptionStatus status = RandomUtil.randomEnumValue(ExceptionStatus.class);
        System.out.println("now = " + status);
        status = RandomUtil.randomEnumValue(ExceptionStatus.class);
        System.out.println("now = " + status);
        status = RandomUtil.randomEnumValue(ExceptionStatus.class);
        System.out.println("now = " + status);
    }
}
