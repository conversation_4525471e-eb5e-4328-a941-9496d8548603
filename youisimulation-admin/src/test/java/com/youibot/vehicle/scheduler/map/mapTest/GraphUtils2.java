package com.youibot.vehicle.scheduler.map.mapTest;

import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import org.jgrapht.alg.connectivity.ConnectivityInspector;
import org.jgrapht.graph.AbstractBaseGraph;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class GraphUtils2 {

    // 排除指定节点
    private static <V, E> AbstractBaseGraph<V, E> excludeNodes(AbstractBaseGraph<V, E> graph, Set<V> nodesToExclude) {
        AbstractBaseGraph<V, E> newGraph = (AbstractBaseGraph<V, E>) graph.clone();

        for (V node : nodesToExclude) {
            newGraph.removeVertex(node);
        }
        return newGraph;
    }

    // 获取图的连通分块
    private static <V, E> List<Set<V>> getConnectedComponents(AbstractBaseGraph<V, E> graph) {
        ConnectivityInspector<V, E> inspector = new ConnectivityInspector<>(graph);
        return inspector.connectedSets();
    }

    // 从每个分块中选择n个非孤立的界点
    private static <V, E> List<V> selectBoundaryNodes(AbstractBaseGraph<V, E> graph, Set<V> nodesToExclude, int n) {
        AbstractBaseGraph<V, E> filteredGraph = excludeNodes(graph, nodesToExclude);
        List<Set<V>> connectedComponents = getConnectedComponents(filteredGraph);

        List<V> selectedNodes = new ArrayList<>();
        for (Set<V> component : connectedComponents) {
            List<V> nonIsolatedNodes = component.stream()
                    .filter(v -> filteredGraph.edgesOf(v).size() > 0)
                    .collect(Collectors.toList());

            // Ensure we don't pick more nodes than available
            int numToPick = Math.min(n, nonIsolatedNodes.size());
            selectedNodes.addAll(nonIsolatedNodes.subList(0, numToPick));
        }
        return selectedNodes;
    }

    public static void main(String[] args) {
        // 创建示例图
        DirectedWeightedPseudograph<Marker, DefaultWeightedEdge> graph = new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        // 添加顶点和边到图中
        graph.clone();
        // 排除的节点
        Set<Marker> nodesToExclude = new HashSet<>();
        // 添加需要排除的节点到 nodesToExclude 集合

        // 选择非孤立的界点
        int n = 3; // 选择的节点数量
        List<Marker> selectedNodes = selectBoundaryNodes(graph, nodesToExclude, n);

        // 打印选择的节点
        System.out.println("Selected nodes: " + selectedNodes);
    }
}
