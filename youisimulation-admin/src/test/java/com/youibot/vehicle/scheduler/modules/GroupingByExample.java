package com.youibot.vehicle.scheduler.modules;

import com.youibot.vehicle.scheduler.modules.sim.entity.SimRobot;
import com.youibot.vehicle.scheduler.modules.sim.enums.EnableStatus;
import org.jeasy.random.EasyRandom;
import org.jeasy.random.EasyRandomParameters;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class GroupingByExample {

    EasyRandomParameters parameters = new EasyRandomParameters();
    EasyRandom easyRandom = new EasyRandom(parameters);
    @Test
    public  void test1(){



        List<SimRobot> simRobot = Arrays.asList(
                easyRandom.nextObject(SimRobot.class),
                easyRandom.nextObject(SimRobot.class),
                easyRandom.nextObject(SimRobot.class)

        );

        // 使用 Collectors.mapping 提取名称，并将结果的值类型设为 List<String>
        Map<EnableStatus, List<String>> groupedByGroup = simRobot.stream()
                .collect(Collectors.groupingBy(
                        SimRobot::getEnabled,
                        Collectors.mapping(
                                SimRobot::getMac, // 提取名称
                                Collectors.toList() // 将名称收集到 List<String>
                        )
                ));

        System.out.println("groupedByGroup = " + groupedByGroup);

    }
    public static void main(String[] args) {
        List<Person> people = Arrays.asList(
                new Person("Alice", "Group1"),
                new Person("Bob", "Group2"),
                new Person("Charlie", "Group1")
        );

        // 使用 Collectors.mapping 提取名称，并将结果的值类型设为 List<String>
        Map<String, List<String>> groupedByGroup = people.stream()
                .collect(Collectors.groupingBy(
                        Person::getGroup,
                        Collectors.mapping(
                                Person::getName, // 提取名称
                                Collectors.toList() // 将名称收集到 List<String>
                        )
                ));

        groupedByGroup.forEach((group, names) -> {
            System.out.println(group + ": " + names);
        });
    }
}

class Person {
    private String name;
    private String group;

    public Person(String name, String group) {
        this.name = name;
        this.group = group;
    }

    public String getName() {
        return name;
    }

    public String getGroup() {
        return group;
    }

    @Override
    public String toString() {
        return name;
    }
}
