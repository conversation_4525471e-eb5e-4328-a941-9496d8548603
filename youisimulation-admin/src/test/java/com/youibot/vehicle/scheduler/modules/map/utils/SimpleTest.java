package com.youibot.vehicle.scheduler.modules.map.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.youibot.vehicle.scheduler.modules.client.entity.ObservablePriorityBlockingQueue;
import com.youibot.vehicle.scheduler.modules.client.entity.TaskComparator;
import com.youibot.vehicle.scheduler.modules.client.entity.VehicleInstructionData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.stream.Collectors;

@Slf4j
public class SimpleTest {


    @Test
    public void test2(){

        PriorityQueue<VehicleInstructionData> taskQueue = new PriorityQueue<>(new TaskComparator());

        // 添加任务
        taskQueue.add(new VehicleInstructionData("Task1", LocalDateTime.now().minusSeconds(45), VehicleInstructionData.UN_EXECUTED, 5));
        taskQueue.add(new VehicleInstructionData("Task2", LocalDateTime.now().minusSeconds(45), VehicleInstructionData.UN_EXECUTED ,4));
        taskQueue.add(new VehicleInstructionData("Task3", LocalDateTime.now().minusMinutes(45), VehicleInstructionData.UN_EXECUTED ,3 ));
        taskQueue.add(new VehicleInstructionData("Task4", LocalDateTime.now(), VehicleInstructionData.UN_EXECUTED, 2));
        taskQueue.add(new VehicleInstructionData("Task5", LocalDateTime.now().minusMinutes(45 ), VehicleInstructionData.UN_EXECUTED, 1));
        taskQueue.add(new VehicleInstructionData("Task6", LocalDateTime.now().minusSeconds(1 ), VehicleInstructionData.EXECUTING, 0 ));

        Optional<VehicleInstructionData> min = taskQueue.stream().min(new TaskComparator());
        System.out.println( "mini: "+ JSON.toJSONString(min.get()));

        ConcurrentSkipListSet<VehicleInstructionData> sortedSet = new ConcurrentSkipListSet<>( new TaskComparator());

        sortedSet.addAll(taskQueue);

     /*   VehicleInstructionData o = sortedSet.pollFirst();
        System.out.println("first: " + JSON.toJSONString(o));
        VehicleInstructionData last = sortedSet.pollLast();
        System.out.println("last: " + JSON.toJSONString(last));*/
        for (int i = 0; i < sortedSet.size(); i++) {
            System.out.println("skip:" + i + ",: " + JSON.toJSONString( sortedSet.stream().skip(i ).findFirst().orElse(null)));
        }


        List<VehicleInstructionData> sortedList = taskQueue.stream().sorted(new TaskComparator()).collect(Collectors.toList());
        for (VehicleInstructionData vehicleInstructionData : sortedList) {
            System.out.println("inst: " +vehicleInstructionData.getInstructId());
        }
        // 处理任务
        while (!taskQueue.isEmpty()) {
            VehicleInstructionData task = taskQueue.poll();  // 获取并移除优先级最高的元素
            System.out.println("Processing: " + task);
        }



    }

    String data ="\n" +
            "[\n" +
            "\t{\n" +
            "\t\t\"cancel\": false,\n" +
            "\t\t\"code\": \"VehicleMove\",\n" +
            "\t\t\"end\": false,\n" +
            "\t\t\"finishTime\": 1728216576286,\n" +
            "\t\t\"finished\": true,\n" +
            "\t\t\"first\": false,\n" +
            "\t\t\"instructId\": \"T20241006000297_N2\",\n" +
            "\t\t\"move\": true,\n" +
            "\t\t\"parameter\": {\n" +
            "\t\t\t\"id\": \"T20241006000297_N2\",\n" +
            "\t\t\t\"alter_id\": \"T20241006000297_N2\",\n" +
            "\t\t\t\"segments\": [\n" +
            "\t\t\t\t{\n" +
            "\t\t\t\t\t\"agvDirection\": 0,\n" +
            "\t\t\t\t\t\"offsetX\": 0.0,\n" +
            "\t\t\t\t\t\"cameraObstacle\": 0,\n" +
            "\t\t\t\t\t\"offsetY\": 0.0,\n" +
            "\t\t\t\t\t\"shape\": {\n" +
            "\t\t\t\t\t\t\"control2\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -22.051657912214347,\n" +
            "\t\t\t\t\t\t\t\"y\": 20.326666666666669\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"control1\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -22.051657912214347,\n" +
            "\t\t\t\t\t\t\t\"y\": 20.108333333333336\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"segmentId\": \"FinalMap0611_L_3562_4207\",\n" +
            "\t\t\t\t\t\t\"length\": 1.0916666666666706,\n" +
            "\t\t\t\t\t\t\"start\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -22.051657912214347,\n" +
            "\t\t\t\t\t\t\t\"angle\": 90.0,\n" +
            "\t\t\t\t\t\t\t\"y\": 19.671666666666668\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"end\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -22.051657912214347,\n" +
            "\t\t\t\t\t\t\t\"angle\": 0.0,\n" +
            "\t\t\t\t\t\t\t\"y\": 20.763333333333337\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"t0\": 0.0,\n" +
            "\t\t\t\t\t\t\"t1\": 1.0\n" +
            "\t\t\t\t\t},\n" +
            "\t\t\t\t\t\"segmentParams\": {\n" +
            "\t\t\t\t\t\t\"extendString\": [],\n" +
            "\t\t\t\t\t\t\"rotateAcc\": 0.5,\n" +
            "\t\t\t\t\t\t\"moveObstacleRegion\": 1,\n" +
            "\t\t\t\t\t\t\"obstacleAvoidance\": false,\n" +
            "\t\t\t\t\t\t\"rotationObstacleRegion\": 5,\n" +
            "\t\t\t\t\t\t\"extendBit\": 10,\n" +
            "\t\t\t\t\t\t\"rotationSpeed\": 0.5,\n" +
            "\t\t\t\t\t\t\"translationAcc\": 0.5,\n" +
            "\t\t\t\t\t\t\"movingSpeed\": 0.4,\n" +
            "\t\t\t\t\t\t\"featureFusion\": false\n" +
            "\t\t\t\t\t},\n" +
            "\t\t\t\t\t\"extendParamList\": [],\n" +
            "\t\t\t\t\t\"offsetAngle\": 0.0,\n" +
            "\t\t\t\t\t\"pathType\": \"Common\",\n" +
            "\t\t\t\t\t\"type\": \"bezier\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t]\n" +
            "\t\t},\n" +
            "\t\t\"poolKey\": \"127.0.0.1:16646\",\n" +
            "\t\t\"status\": \"Finished\",\n" +
            "\t\t\"taskId\": \"T20241006000297\",\n" +
            "\t\t\"timestamp\": \"2024-10-06T20:09:32.747\",\n" +
            "\t\t\"uniqueCode\": \"2e875593-b1f9-4c9f-aec8-7553acb43fe0\",\n" +
            "\t\t\"vehicleCode\": 1099\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"cancel\": false,\n" +
            "\t\t\"code\": \"VehicleMove\",\n" +
            "\t\t\"end\": false,\n" +
            "\t\t\"finished\": false,\n" +
            "\t\t\"first\": false,\n" +
            "\t\t\"instructId\": \"T20241006000297_N2\",\n" +
            "\t\t\"move\": true,\n" +
            "\t\t\"parameter\": {\n" +
            "\t\t\t\"segments\": [\n" +
            "\t\t\t\t{\n" +
            "\t\t\t\t\t\"agvDirection\": 0,\n" +
            "\t\t\t\t\t\"offsetX\": 0.0,\n" +
            "\t\t\t\t\t\"cameraObstacle\": 0,\n" +
            "\t\t\t\t\t\"offsetY\": 0.0,\n" +
            "\t\t\t\t\t\"shape\": {\n" +
            "\t\t\t\t\t\t\"control2\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -19.52816906525953,\n" +
            "\t\t\t\t\t\t\t\"y\": 20.743533333333326\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"control1\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -20.36933201424447,\n" +
            "\t\t\t\t\t\t\t\"y\": 20.75013333333333\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"segmentId\": \"FinalMap0611_L_4207_4208\",\n" +
            "\t\t\t\t\t\t\"length\": 4.205944206551714,\n" +
            "\t\t\t\t\t\t\"start\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -22.051657912214347,\n" +
            "\t\t\t\t\t\t\t\"angle\": 0.0,\n" +
            "\t\t\t\t\t\t\t\"y\": 20.763333333333337\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"end\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -17.845843167289656,\n" +
            "\t\t\t\t\t\t\t\"angle\": 0.0,\n" +
            "\t\t\t\t\t\t\t\"y\": 20.730333333333328\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"t0\": 0.0,\n" +
            "\t\t\t\t\t\t\"t1\": 1.0\n" +
            "\t\t\t\t\t},\n" +
            "\t\t\t\t\t\"segmentParams\": {\n" +
            "\t\t\t\t\t\t\"extendString\": [],\n" +
            "\t\t\t\t\t\t\"rotateAcc\": 0.5,\n" +
            "\t\t\t\t\t\t\"obstacleAvoidance\": false,\n" +
            "\t\t\t\t\t\t\"rotationObstacleRegion\": 5,\n" +
            "\t\t\t\t\t\t\"extendBit\": 10,\n" +
            "\t\t\t\t\t\t\"rotationSpeed\": 0.5,\n" +
            "\t\t\t\t\t\t\"translationAcc\": 0.5,\n" +
            "\t\t\t\t\t\t\"movingSpeed\": 1.5,\n" +
            "\t\t\t\t\t\t\"featureFusion\": false\n" +
            "\t\t\t\t\t},\n" +
            "\t\t\t\t\t\"extendParamList\": [],\n" +
            "\t\t\t\t\t\"offsetAngle\": 0.0,\n" +
            "\t\t\t\t\t\"pathType\": \"Common\",\n" +
            "\t\t\t\t\t\"type\": \"bezier\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t]\n" +
            "\t\t},\n" +
            "\t\t\"poolKey\": \"127.0.0.1:16646\",\n" +
            "\t\t\"status\": \"UnExecuted\",\n" +
            "\t\t\"taskId\": \"T20241006000297\",\n" +
            "\t\t\"timestamp\": \"2024-10-06T20:09:32.894\",\n" +
            "\t\t\"uniqueCode\": \"7cddcf78-3b1b-4675-9811-f210e09c206c\",\n" +
            "\t\t\"vehicleCode\": 1099\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"cancel\": false,\n" +
            "\t\t\"code\": \"VehicleMove\",\n" +
            "\t\t\"end\": false,\n" +
            "\t\t\"finishTime\": 1728216572299,\n" +
            "\t\t\"finished\": true,\n" +
            "\t\t\"first\": false,\n" +
            "\t\t\"instructId\": \"T20241006000297_N2\",\n" +
            "\t\t\"move\": true,\n" +
            "\t\t\"parameter\": {\n" +
            "\t\t\t\"id\": \"T20241006000297_N2\",\n" +
            "\t\t\t\"alter_id\": \"T20241006000297_N2\",\n" +
            "\t\t\t\"segments\": [\n" +
            "\t\t\t\t{\n" +
            "\t\t\t\t\t\"agvDirection\": 0,\n" +
            "\t\t\t\t\t\"offsetX\": 0.0,\n" +
            "\t\t\t\t\t\"cameraObstacle\": 0,\n" +
            "\t\t\t\t\t\"offsetY\": 0.0,\n" +
            "\t\t\t\t\t\"shape\": {\n" +
            "\t\t\t\t\t\t\"control2\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -20.36933201424447,\n" +
            "\t\t\t\t\t\t\t\"y\": 19.66046666666667\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"control1\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -19.52816906525953,\n" +
            "\t\t\t\t\t\t\t\"y\": 19.65486666666667\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"segmentId\": \"FinalMap0611_L_3564_3562\",\n" +
            "\t\t\t\t\t\t\"length\": 4.205907948187396,\n" +
            "\t\t\t\t\t\t\"start\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -17.845843167289656,\n" +
            "\t\t\t\t\t\t\t\"angle\": 90.0,\n" +
            "\t\t\t\t\t\t\t\"y\": 19.643666666666669\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"end\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -22.051657912214347,\n" +
            "\t\t\t\t\t\t\t\"angle\": 90.0,\n" +
            "\t\t\t\t\t\t\t\"y\": 19.671666666666668\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"t0\": 0.0,\n" +
            "\t\t\t\t\t\t\"t1\": 1.0\n" +
            "\t\t\t\t\t},\n" +
            "\t\t\t\t\t\"segmentParams\": {\n" +
            "\t\t\t\t\t\t\"extendString\": [],\n" +
            "\t\t\t\t\t\t\"rotateAcc\": 0.5,\n" +
            "\t\t\t\t\t\t\"obstacleAvoidance\": false,\n" +
            "\t\t\t\t\t\t\"rotationObstacleRegion\": 5,\n" +
            "\t\t\t\t\t\t\"extendBit\": 10,\n" +
            "\t\t\t\t\t\t\"rotationSpeed\": 0.5,\n" +
            "\t\t\t\t\t\t\"translationAcc\": 0.5,\n" +
            "\t\t\t\t\t\t\"movingSpeed\": 1.5,\n" +
            "\t\t\t\t\t\t\"featureFusion\": false\n" +
            "\t\t\t\t\t},\n" +
            "\t\t\t\t\t\"extendParamList\": [],\n" +
            "\t\t\t\t\t\"offsetAngle\": 0.0,\n" +
            "\t\t\t\t\t\"pathType\": \"Common\",\n" +
            "\t\t\t\t\t\"type\": \"bezier\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t]\n" +
            "\t\t},\n" +
            "\t\t\"poolKey\": \"127.0.0.1:16646\",\n" +
            "\t\t\"status\": \"Finished\",\n" +
            "\t\t\"taskId\": \"T20241006000297\",\n" +
            "\t\t\"timestamp\": \"2024-10-06T20:09:24.774\",\n" +
            "\t\t\"uniqueCode\": \"4c8a771d-aa5c-43fa-a19d-506d193dafc7\",\n" +
            "\t\t\"vehicleCode\": 1099\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"cancel\": false,\n" +
            "\t\t\"code\": \"VehicleMove\",\n" +
            "\t\t\"end\": false,\n" +
            "\t\t\"finishTime\": 1728216564220,\n" +
            "\t\t\"finished\": true,\n" +
            "\t\t\"first\": true,\n" +
            "\t\t\"instructId\": \"T20241006000297_N2\",\n" +
            "\t\t\"move\": true,\n" +
            "\t\t\"parameter\": {\n" +
            "\t\t\t\"id\": \"T20241006000297_N2\",\n" +
            "\t\t\t\"alter_id\": \"T20241006000297_N2\",\n" +
            "\t\t\t\"segments\": [\n" +
            "\t\t\t\t{\n" +
            "\t\t\t\t\t\"agvDirection\": 0,\n" +
            "\t\t\t\t\t\"offsetX\": 0.0,\n" +
            "\t\t\t\t\t\"cameraObstacle\": 0,\n" +
            "\t\t\t\t\t\"offsetY\": 0.0,\n" +
            "\t\t\t\t\t\"shape\": {\n" +
            "\t\t\t\t\t\t\"control2\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -16.83049005210192,\n" +
            "\t\t\t\t\t\t\t\"y\": 19.63690700440158\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"control1\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -16.72937424163882,\n" +
            "\t\t\t\t\t\t\t\"y\": 18.539973920634126\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"segmentId\": \"FinalMap0611_L_3349_3564\",\n" +
            "\t\t\t\t\t\t\"length\": 2.5695331310892675,\n" +
            "\t\t\t\t\t\t\"start\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -15.616325575105947,\n" +
            "\t\t\t\t\t\t\t\"angle\": 90.0,\n" +
            "\t\t\t\t\t\t\t\"y\": 18.522999999999997\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"end\": {\n" +
            "\t\t\t\t\t\t\t\"x\": -17.845843167289656,\n" +
            "\t\t\t\t\t\t\t\"angle\": 90.0,\n" +
            "\t\t\t\t\t\t\t\"y\": 19.643666666666669\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t\"t0\": 0.0,\n" +
            "\t\t\t\t\t\t\"t1\": 1.0\n" +
            "\t\t\t\t\t},\n" +
            "\t\t\t\t\t\"segmentParams\": {\n" +
            "\t\t\t\t\t\t\"extendString\": [],\n" +
            "\t\t\t\t\t\t\"rotateAcc\": 0.5,\n" +
            "\t\t\t\t\t\t\"obstacleAvoidance\": false,\n" +
            "\t\t\t\t\t\t\"rotationObstacleRegion\": 5,\n" +
            "\t\t\t\t\t\t\"extendBit\": 10,\n" +
            "\t\t\t\t\t\t\"rotationSpeed\": 0.5,\n" +
            "\t\t\t\t\t\t\"translationAcc\": 0.5,\n" +
            "\t\t\t\t\t\t\"movingSpeed\": 1.3,\n" +
            "\t\t\t\t\t\t\"featureFusion\": false\n" +
            "\t\t\t\t\t},\n" +
            "\t\t\t\t\t\"extendParamList\": [],\n" +
            "\t\t\t\t\t\"offsetAngle\": 0.0,\n" +
            "\t\t\t\t\t\"pathType\": \"Common\",\n" +
            "\t\t\t\t\t\"type\": \"bezier\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t]\n" +
            "\t\t},\n" +
            "\t\t\"poolKey\": \"127.0.0.1:16646\",\n" +
            "\t\t\"status\": \"Finished\",\n" +
            "\t\t\"taskId\": \"T20241006000297\",\n" +
            "\t\t\"timestamp\": \"2024-10-06T20:09:17.867\",\n" +
            "\t\t\"uniqueCode\": \"baf3bdd5-bdfd-4d17-a87b-e5b9110bbe80\",\n" +
            "\t\t\"vehicleCode\": 1099\n" +
            "\t}\n" +
            "]" ;

     String data2 ="\n" +
             "\t\t\t\t\n" +
             "\t\t\t\t\n" +
             "\t\t\t\t\n" +
             "\t\t\t\t\n" +
             "\t\t\t\t [\n" +
             "    {\n" +
             "      \"vehicleCode\": 1107,\n" +
             "      \"taskId\": \"T20241006000661\",\n" +
             "      \"instructId\": \"T20241006000661_N3\",\n" +
             "      \"code\": \"VehicleMove\",\n" +
             "      \"parameter\": {\n" +
             "        \"id\": \"T20241006000661_N3\",\n" +
             "        \"alter_id\": \"T20241006000661_N3\",\n" +
             "        \"segments\": [\n" +
             "          {\n" +
             "            \"agvDirection\": 0,\n" +
             "            \"offsetX\": 0,\n" +
             "            \"cameraObstacle\": 0,\n" +
             "            \"offsetY\": 0,\n" +
             "            \"shape\": {\n" +
             "              \"control2\": {\n" +
             "                \"x\": 75.0284,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"control1\": {\n" +
             "                \"x\": 75.61710000000001,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"segmentId\": \"FinalMap0611_L_4168_4167\",\n" +
             "              \"length\": 2.9435000000000087,\n" +
             "              \"start\": {\n" +
             "                \"x\": 76.79450000000001,\n" +
             "                \"angle\": 0,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"end\": {\n" +
             "                \"x\": 73.851,\n" +
             "                \"angle\": 0,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"t0\": 0,\n" +
             "              \"t1\": 1\n" +
             "            },\n" +
             "            \"segmentParams\": {\n" +
             "              \"extendString\": [],\n" +
             "              \"rotateAcc\": 0.5,\n" +
             "              \"obstacleAvoidance\": false,\n" +
             "              \"rotationObstacleRegion\": 5,\n" +
             "              \"extendBit\": 10,\n" +
             "              \"rotationSpeed\": 0.5,\n" +
             "              \"translationAcc\": 0.5,\n" +
             "              \"movingSpeed\": 1.5,\n" +
             "              \"featureFusion\": false\n" +
             "            },\n" +
             "            \"extendParamList\": [],\n" +
             "            \"offsetAngle\": 0,\n" +
             "            \"pathType\": \"Common\",\n" +
             "            \"type\": \"bezier\"\n" +
             "          }\n" +
             "        ]\n" +
             "      },\n" +
             "      \"status\": \"Executing\",\n" +
             "      \"uniqueCode\": \"787d5e92-c001-478f-acb6-f1ef23dfb355\",\n" +
             "      \"first\": false,\n" +
             "      \"end\": false,\n" +
             "      \"cancel\": false,\n" +
             "      \"finishTime\": null,\n" +
             "      \"poolKey\": \"127.0.0.1:16646\",\n" +
             "      \"timestamp\": {\n" +
             "        \"month\": \"OCTOBER\",\n" +
             "        \"year\": 2024,\n" +
             "        \"dayOfMonth\": 6,\n" +
             "        \"hour\": 23,\n" +
             "        \"minute\": 20,\n" +
             "        \"monthValue\": 10,\n" +
             "        \"nano\": 502000000,\n" +
             "        \"second\": 43,\n" +
             "        \"dayOfWeek\": \"SUNDAY\",\n" +
             "        \"dayOfYear\": 280,\n" +
             "        \"chronology\": {\n" +
             "          \"id\": \"ISO\",\n" +
             "          \"calendarType\": \"iso8601\"\n" +
             "        }\n" +
             "      },\n" +
             "      \"finished\": false,\n" +
             "      \"move\": true\n" +
             "    },\n" +
             "    {\n" +
             "      \"vehicleCode\": 1107,\n" +
             "      \"taskId\": \"T20241006000661\",\n" +
             "      \"instructId\": \"T20241006000661_N3\",\n" +
             "      \"code\": \"VehicleMove\",\n" +
             "      \"parameter\": {\n" +
             "        \"id\": \"T20241006000661_N3\",\n" +
             "        \"alter_id\": \"T20241006000661_N3\",\n" +
             "        \"segments\": [\n" +
             "          {\n" +
             "            \"agvDirection\": 0,\n" +
             "            \"offsetX\": 0,\n" +
             "            \"cameraObstacle\": 0,\n" +
             "            \"offsetY\": 0,\n" +
             "            \"shape\": {\n" +
             "              \"control2\": {\n" +
             "                \"x\": 82.39200000000002,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"control1\": {\n" +
             "                \"x\": 82.61300000000003,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"segmentId\": \"FinalMap0611_L_1737_1769\",\n" +
             "              \"length\": 1.1049999999999602,\n" +
             "              \"start\": {\n" +
             "                \"x\": 83.055,\n" +
             "                \"angle\": 90,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"end\": {\n" +
             "                \"x\": 81.95000000000005,\n" +
             "                \"angle\": 90,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"t0\": 0,\n" +
             "              \"t1\": 1\n" +
             "            },\n" +
             "            \"segmentParams\": {\n" +
             "              \"extendString\": [],\n" +
             "              \"rotateAcc\": 0.5,\n" +
             "              \"obstacleAvoidance\": false,\n" +
             "              \"rotationObstacleRegion\": 5,\n" +
             "              \"extendBit\": 10,\n" +
             "              \"rotationSpeed\": 0.5,\n" +
             "              \"translationAcc\": 0.5,\n" +
             "              \"movingSpeed\": 1.5,\n" +
             "              \"featureFusion\": false\n" +
             "            },\n" +
             "            \"extendParamList\": [],\n" +
             "            \"offsetAngle\": 0,\n" +
             "            \"pathType\": \"Common\",\n" +
             "            \"type\": \"bezier\"\n" +
             "          },\n" +
             "          {\n" +
             "            \"agvDirection\": 0,\n" +
             "            \"offsetX\": 0,\n" +
             "            \"cameraObstacle\": 0,\n" +
             "            \"offsetY\": 0,\n" +
             "            \"shape\": {\n" +
             "              \"control2\": {\n" +
             "                \"x\": 81.28640000000003,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"control1\": {\n" +
             "                \"x\": 81.50760000000002,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"segmentId\": \"FinalMap0611_L_1769_1768\",\n" +
             "              \"length\": 1.1060000000000396,\n" +
             "              \"start\": {\n" +
             "                \"x\": 81.95000000000005,\n" +
             "                \"angle\": 90,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"end\": {\n" +
             "                \"x\": 80.84400000000001,\n" +
             "                \"angle\": 90,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"t0\": 0,\n" +
             "              \"t1\": 1\n" +
             "            },\n" +
             "            \"segmentParams\": {\n" +
             "              \"extendString\": [],\n" +
             "              \"rotateAcc\": 0.5,\n" +
             "              \"obstacleAvoidance\": false,\n" +
             "              \"rotationObstacleRegion\": 5,\n" +
             "              \"extendBit\": 10,\n" +
             "              \"rotationSpeed\": 0.5,\n" +
             "              \"translationAcc\": 0.5,\n" +
             "              \"movingSpeed\": 1.5,\n" +
             "              \"featureFusion\": false\n" +
             "            },\n" +
             "            \"extendParamList\": [],\n" +
             "            \"offsetAngle\": 0,\n" +
             "            \"pathType\": \"Common\",\n" +
             "            \"type\": \"bezier\"\n" +
             "          },\n" +
             "          {\n" +
             "            \"agvDirection\": 0,\n" +
             "            \"offsetX\": 0,\n" +
             "            \"cameraObstacle\": 0,\n" +
             "            \"offsetY\": 0,\n" +
             "            \"shape\": {\n" +
             "              \"control2\": {\n" +
             "                \"x\": 80.18039999999999,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"control1\": {\n" +
             "                \"x\": 80.4016,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"segmentId\": \"FinalMap0611_L_1768_1748\",\n" +
             "              \"length\": 1.1060000000000336,\n" +
             "              \"start\": {\n" +
             "                \"x\": 80.84400000000001,\n" +
             "                \"angle\": 90,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"end\": {\n" +
             "                \"x\": 79.73799999999999,\n" +
             "                \"angle\": 90,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"t0\": 0,\n" +
             "              \"t1\": 1\n" +
             "            },\n" +
             "            \"segmentParams\": {\n" +
             "              \"extendString\": [],\n" +
             "              \"rotateAcc\": 0.5,\n" +
             "              \"obstacleAvoidance\": false,\n" +
             "              \"rotationObstacleRegion\": 5,\n" +
             "              \"extendBit\": 10,\n" +
             "              \"rotationSpeed\": 0.5,\n" +
             "              \"translationAcc\": 0.5,\n" +
             "              \"movingSpeed\": 1.5,\n" +
             "              \"featureFusion\": false\n" +
             "            },\n" +
             "            \"extendParamList\": [],\n" +
             "            \"offsetAngle\": 0,\n" +
             "            \"pathType\": \"Common\",\n" +
             "            \"type\": \"bezier\"\n" +
             "          },\n" +
             "          {\n" +
             "            \"agvDirection\": 0,\n" +
             "            \"offsetX\": 0,\n" +
             "            \"cameraObstacle\": 0,\n" +
             "            \"offsetY\": 0,\n" +
             "            \"shape\": {\n" +
             "              \"control2\": {\n" +
             "                \"x\": 77.9719,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"control1\": {\n" +
             "                \"x\": 78.5606,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"segmentId\": \"FinalMap0611_L_1748_4168\",\n" +
             "              \"length\": 2.9434999999999767,\n" +
             "              \"start\": {\n" +
             "                \"x\": 79.73799999999999,\n" +
             "                \"angle\": 90,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"end\": {\n" +
             "                \"x\": 76.79450000000001,\n" +
             "                \"angle\": 0,\n" +
             "                \"y\": 19.451999999999998\n" +
             "              },\n" +
             "              \"t0\": 0,\n" +
             "              \"t1\": 1\n" +
             "            },\n" +
             "            \"segmentParams\": {\n" +
             "              \"extendString\": [],\n" +
             "              \"rotateAcc\": 0.5,\n" +
             "              \"obstacleAvoidance\": false,\n" +
             "              \"rotationObstacleRegion\": 5,\n" +
             "              \"extendBit\": 10,\n" +
             "              \"rotationSpeed\": 0.5,\n" +
             "              \"translationAcc\": 0.5,\n" +
             "              \"movingSpeed\": 1.5,\n" +
             "              \"featureFusion\": false\n" +
             "            },\n" +
             "            \"extendParamList\": [],\n" +
             "            \"offsetAngle\": 0,\n" +
             "            \"pathType\": \"Common\",\n" +
             "            \"type\": \"bezier\"\n" +
             "          }\n" +
             "        ]\n" +
             "      },\n" +
             "      \"status\": \"Executing\",\n" +
             "      \"uniqueCode\": \"d417e33a-c9ce-4bbf-a6ad-383f99809e77\",\n" +
             "      \"first\": true,\n" +
             "      \"end\": false,\n" +
             "      \"cancel\": false,\n" +
             "      \"finishTime\": null,\n" +
             "      \"poolKey\": \"127.0.0.1:16646\",\n" +
             "      \"timestamp\": {\n" +
             "        \"month\": \"OCTOBER\",\n" +
             "        \"year\": 2024,\n" +
             "        \"dayOfMonth\": 6,\n" +
             "        \"hour\": 23,\n" +
             "        \"minute\": 20,\n" +
             "        \"monthValue\": 10,\n" +
             "        \"nano\": 922000000,\n" +
             "        \"second\": 15,\n" +
             "        \"dayOfWeek\": \"SUNDAY\",\n" +
             "        \"dayOfYear\": 280,\n" +
             "        \"chronology\": {\n" +
             "          \"id\": \"ISO\",\n" +
             "          \"calendarType\": \"iso8601\"\n" +
             "        }\n" +
             "      },\n" +
             "      \"finished\": false,\n" +
             "      \"move\": true\n" +
             "    }\n" +
             "  ]";

     String data3 ="[\n" +
             "\t\t{\n" +
             "\t\t\t\"vehicleCode\": 1104,\n" +
             "\t\t\t\"taskId\": \"T20241007000128\",\n" +
             "\t\t\t\"instructId\": \"T20241007000128_N3\",\n" +
             "\t\t\t\"code\": \"VehicleMove\",\n" +
             "\t\t\t\"parameter\": {\n" +
             "\t\t\t\t\"id\": \"T20241007000128_N3\",\n" +
             "\t\t\t\t\"alter_id\": \"T20241007000128_N3\",\n" +
             "\t\t\t\t\"segments\": [\n" +
             "\t\t\t\t\t{\n" +
             "\t\t\t\t\t\t\"agvDirection\": 0,\n" +
             "\t\t\t\t\t\t\"offsetX\": 0,\n" +
             "\t\t\t\t\t\t\"cameraObstacle\": 0,\n" +
             "\t\t\t\t\t\t\"offsetY\": 0,\n" +
             "\t\t\t\t\t\t\"shape\": {\n" +
             "\t\t\t\t\t\t\t\"control2\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -60.826070268148097,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.055270798656765\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"control1\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -60.20325916243766,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.052388122021357\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"segmentId\": \"FinalMap0611_L_1337_1336\",\n" +
             "\t\t\t\t\t\t\t\"length\": 3.114088884492697,\n" +
             "\t\t\t\t\t\t\t\"start\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -58.95763695101679,\n" +
             "\t\t\t\t\t\t\t\t\"angle\": 90,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.04662276875054\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"end\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -62.071692479568969,\n" +
             "\t\t\t\t\t\t\t\t\"angle\": 90,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.061036151927575\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"t0\": 0,\n" +
             "\t\t\t\t\t\t\t\"t1\": 1\n" +
             "\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\"segmentParams\": {\n" +
             "\t\t\t\t\t\t\t\"extendString\": [],\n" +
             "\t\t\t\t\t\t\t\"rotateAcc\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"obstacleAvoidance\": false,\n" +
             "\t\t\t\t\t\t\t\"rotationObstacleRegion\": 5,\n" +
             "\t\t\t\t\t\t\t\"extendBit\": 10,\n" +
             "\t\t\t\t\t\t\t\"rotationSpeed\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"translationAcc\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"movingSpeed\": 1.5,\n" +
             "\t\t\t\t\t\t\t\"featureFusion\": false\n" +
             "\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\"extendParamList\": [],\n" +
             "\t\t\t\t\t\t\"offsetAngle\": 0,\n" +
             "\t\t\t\t\t\t\"pathType\": \"Common\",\n" +
             "\t\t\t\t\t\t\"type\": \"bezier\"\n" +
             "\t\t\t\t\t}\n" +
             "\t\t\t\t]\n" +
             "\t\t\t},\n" +
             "\t\t\t\"status\": \"Executing\",\n" +
             "\t\t\t\"uniqueCode\": \"0403e9b5-ae36-46cd-987d-2deef6a66e74\",\n" +
             "\t\t\t\"first\": false,\n" +
             "\t\t\t\"end\": false,\n" +
             "\t\t\t\"cancel\": false,\n" +
             "\t\t\t\"finishTime\": null,\n" +
             "\t\t\t\"poolKey\": \"127.0.0.1:16646\",\n" +
             "\t\t\t\"seq\": \"18\",\n" +
             "\t\t\t\"timestamp\": {\n" +
             "\t\t\t\t\"month\": \"OCTOBER\",\n" +
             "\t\t\t\t\"year\": 2024,\n" +
             "\t\t\t\t\"dayOfMonth\": 7,\n" +
             "\t\t\t\t\"hour\": 0,\n" +
             "\t\t\t\t\"minute\": 59,\n" +
             "\t\t\t\t\"monthValue\": 10,\n" +
             "\t\t\t\t\"nano\": 998000000,\n" +
             "\t\t\t\t\"second\": 37,\n" +
             "\t\t\t\t\"dayOfWeek\": \"MONDAY\",\n" +
             "\t\t\t\t\"dayOfYear\": 281,\n" +
             "\t\t\t\t\"chronology\": {\n" +
             "\t\t\t\t\t\"id\": \"ISO\",\n" +
             "\t\t\t\t\t\"calendarType\": \"iso8601\"\n" +
             "\t\t\t\t}\n" +
             "\t\t\t},\n" +
             "\t\t\t\"finished\": false,\n" +
             "\t\t\t\"executing\": true,\n" +
             "\t\t\t\"move\": true\n" +
             "\t\t},\n" +
             "\t\t{\n" +
             "\t\t\t\"vehicleCode\": 1104,\n" +
             "\t\t\t\"taskId\": \"T20241007000128\",\n" +
             "\t\t\t\"instructId\": \"T20241007000128_N3\",\n" +
             "\t\t\t\"code\": \"VehicleMove\",\n" +
             "\t\t\t\"parameter\": {\n" +
             "\t\t\t\t\"id\": \"T20241007000128_N3\",\n" +
             "\t\t\t\t\"alter_id\": \"T20241007000128_N3\",\n" +
             "\t\t\t\t\"segments\": [\n" +
             "\t\t\t\t\t{\n" +
             "\t\t\t\t\t\t\"agvDirection\": 0,\n" +
             "\t\t\t\t\t\t\"offsetX\": 0,\n" +
             "\t\t\t\t\t\t\"cameraObstacle\": 0,\n" +
             "\t\t\t\t\t\t\"offsetY\": 0,\n" +
             "\t\t\t\t\t\t\"shape\": {\n" +
             "\t\t\t\t\t\t\t\"control2\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -53.200823279112288,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.01997739888762\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"control1\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -52.38465096042538,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.016199751295959\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"segmentId\": \"FinalMap0611_L_1328_1267\",\n" +
             "\t\t\t\t\t\t\t\"length\": 4.080905305235826,\n" +
             "\t\t\t\t\t\t\t\"start\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -50.752306323051588,\n" +
             "\t\t\t\t\t\t\t\t\"angle\": 90,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.008644456112653\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"end\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -54.83316791648608,\n" +
             "\t\t\t\t\t\t\t\t\"angle\": 90,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.027532694070936\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"t0\": 0,\n" +
             "\t\t\t\t\t\t\t\"t1\": 1\n" +
             "\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\"segmentParams\": {\n" +
             "\t\t\t\t\t\t\t\"extendString\": [],\n" +
             "\t\t\t\t\t\t\t\"rotateAcc\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"obstacleAvoidance\": false,\n" +
             "\t\t\t\t\t\t\t\"rotationObstacleRegion\": 5,\n" +
             "\t\t\t\t\t\t\t\"extendBit\": 10,\n" +
             "\t\t\t\t\t\t\t\"rotationSpeed\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"translationAcc\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"movingSpeed\": 0.7,\n" +
             "\t\t\t\t\t\t\t\"featureFusion\": false\n" +
             "\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\"extendParamList\": [],\n" +
             "\t\t\t\t\t\t\"offsetAngle\": 0,\n" +
             "\t\t\t\t\t\t\"pathType\": \"Common\",\n" +
             "\t\t\t\t\t\t\"type\": \"bezier\"\n" +
             "\t\t\t\t\t},\n" +
             "\t\t\t\t\t{\n" +
             "\t\t\t\t\t\t\"agvDirection\": 0,\n" +
             "\t\t\t\t\t\t\"offsetX\": 0,\n" +
             "\t\t\t\t\t\t\"cameraObstacle\": 0,\n" +
             "\t\t\t\t\t\t\"offsetY\": 0,\n" +
             "\t\t\t\t\t\t\"shape\": {\n" +
             "\t\t\t\t\t\t\t\"control2\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -55.74786848310385,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.031766378935587\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"control1\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -55.44296829423126,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.030355150647368\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"segmentId\": \"FinalMap0611_L_1267_1285\",\n" +
             "\t\t\t\t\t\t\t\"length\": 1.5245172739249398,\n" +
             "\t\t\t\t\t\t\t\"start\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -54.83316791648608,\n" +
             "\t\t\t\t\t\t\t\t\"angle\": 90,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.027532694070936\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"end\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -56.357668860849027,\n" +
             "\t\t\t\t\t\t\t\t\"angle\": 90,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.03458883551202\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"t0\": 0,\n" +
             "\t\t\t\t\t\t\t\"t1\": 1\n" +
             "\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\"segmentParams\": {\n" +
             "\t\t\t\t\t\t\t\"extendString\": [],\n" +
             "\t\t\t\t\t\t\t\"rotateAcc\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"moveObstacleRegion\": 1,\n" +
             "\t\t\t\t\t\t\t\"obstacleAvoidance\": false,\n" +
             "\t\t\t\t\t\t\t\"rotationObstacleRegion\": 5,\n" +
             "\t\t\t\t\t\t\t\"extendBit\": 10,\n" +
             "\t\t\t\t\t\t\t\"rotationSpeed\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"translationAcc\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"movingSpeed\": 0.4,\n" +
             "\t\t\t\t\t\t\t\"featureFusion\": false\n" +
             "\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\"extendParamList\": [],\n" +
             "\t\t\t\t\t\t\"offsetAngle\": 0,\n" +
             "\t\t\t\t\t\t\"pathType\": \"Common\",\n" +
             "\t\t\t\t\t\t\"type\": \"bezier\"\n" +
             "\t\t\t\t\t},\n" +
             "\t\t\t\t\t{\n" +
             "\t\t\t\t\t\t\"agvDirection\": 0,\n" +
             "\t\t\t\t\t\t\"offsetX\": 0,\n" +
             "\t\t\t\t\t\t\"cameraObstacle\": 0,\n" +
             "\t\t\t\t\t\t\"offsetY\": 0,\n" +
             "\t\t\t\t\t\t\"shape\": {\n" +
             "\t\t\t\t\t\t\t\"control2\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -57.91764971494968,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.04180919545513\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"control1\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -57.39765609691613,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.039402408807434\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"segmentId\": \"FinalMap0611_L_1285_1337\",\n" +
             "\t\t\t\t\t\t\t\"length\": 2.599995939504481,\n" +
             "\t\t\t\t\t\t\t\"start\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -56.357668860849027,\n" +
             "\t\t\t\t\t\t\t\t\"angle\": 90,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.03458883551202\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"end\": {\n" +
             "\t\t\t\t\t\t\t\t\"x\": -58.95763695101679,\n" +
             "\t\t\t\t\t\t\t\t\"angle\": 90,\n" +
             "\t\t\t\t\t\t\t\t\"y\": 20.04662276875054\n" +
             "\t\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\t\"t0\": 0,\n" +
             "\t\t\t\t\t\t\t\"t1\": 1\n" +
             "\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\"segmentParams\": {\n" +
             "\t\t\t\t\t\t\t\"extendString\": [],\n" +
             "\t\t\t\t\t\t\t\"rotateAcc\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"obstacleAvoidance\": false,\n" +
             "\t\t\t\t\t\t\t\"rotationObstacleRegion\": 5,\n" +
             "\t\t\t\t\t\t\t\"extendBit\": 10,\n" +
             "\t\t\t\t\t\t\t\"rotationSpeed\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"translationAcc\": 0.5,\n" +
             "\t\t\t\t\t\t\t\"movingSpeed\": 0.7,\n" +
             "\t\t\t\t\t\t\t\"featureFusion\": false\n" +
             "\t\t\t\t\t\t},\n" +
             "\t\t\t\t\t\t\"extendParamList\": [],\n" +
             "\t\t\t\t\t\t\"offsetAngle\": 0,\n" +
             "\t\t\t\t\t\t\"pathType\": \"Common\",\n" +
             "\t\t\t\t\t\t\"type\": \"bezier\"\n" +
             "\t\t\t\t\t}\n" +
             "\t\t\t\t]\n" +
             "\t\t\t},\n" +
             "\t\t\t\"status\": \"Executing\",\n" +
             "\t\t\t\"uniqueCode\": \"d8abc0ba-67b3-4f87-9bd7-89823ef6a1fe\",\n" +
             "\t\t\t\"first\": false,\n" +
             "\t\t\t\"end\": false,\n" +
             "\t\t\t\"cancel\": false,\n" +
             "\t\t\t\"finishTime\": null,\n" +
             "\t\t\t\"poolKey\": \"127.0.0.1:16646\",\n" +
             "\t\t\t\"seq\": \"17\",\n" +
             "\t\t\t\"timestamp\": {\n" +
             "\t\t\t\t\"month\": \"OCTOBER\",\n" +
             "\t\t\t\t\"year\": 2024,\n" +
             "\t\t\t\t\"dayOfMonth\": 7,\n" +
             "\t\t\t\t\"hour\": 0,\n" +
             "\t\t\t\t\"minute\": 59,\n" +
             "\t\t\t\t\"monthValue\": 10,\n" +
             "\t\t\t\t\"nano\": 90000000,\n" +
             "\t\t\t\t\"second\": 14,\n" +
             "\t\t\t\t\"dayOfWeek\": \"MONDAY\",\n" +
             "\t\t\t\t\"dayOfYear\": 281,\n" +
             "\t\t\t\t\"chronology\": {\n" +
             "\t\t\t\t\t\"id\": \"ISO\",\n" +
             "\t\t\t\t\t\"calendarType\": \"iso8601\"\n" +
             "\t\t\t\t}\n" +
             "\t\t\t},\n" +
             "\t\t\t\"finished\": false,\n" +
             "\t\t\t\"executing\": true,\n" +
             "\t\t\t\"move\": true\n" +
             "\t\t}\n" +
             "\t]";
     @Test
    public void testSort(){

         List<VehicleInstructionData> beanList = JSON.parseObject(data, new TypeReference<List<VehicleInstructionData>>() {});

         ObservablePriorityBlockingQueue<VehicleInstructionData> queue = new ObservablePriorityBlockingQueue<>(200,  new TaskComparator());
         beanList.forEach( i ->{
             queue.offer(i);
                 }
                 );

         boolean work = work(queue);
         VehicleInstructionData peek = queue.peek();
         log.debug("peek:{}", JSON.toJSONString(peek));
         log.debug("work:{}", work );
     }

    @Test
    public void testSort2(){

        List<VehicleInstructionData> beanList = JSON.parseObject(data3, new TypeReference<List<VehicleInstructionData>>() {});

        ObservablePriorityBlockingQueue<VehicleInstructionData> queue = new ObservablePriorityBlockingQueue<>(200,  new TaskComparator());
        beanList.forEach( i ->{
                    queue.offer(i);
                }
        );

        boolean work = work(queue);
        VehicleInstructionData peek = queue.peek();
        log.debug("peek:{}", JSON.toJSONString(peek));
        log.debug("work:{}", work );
    }

    public boolean work(  PriorityBlockingQueue<VehicleInstructionData> all){

        if(CollectionUtils.isEmpty( all) ){

            return false;
        }else if( (Objects.requireNonNull(all.peek()).isFinished())){
            List<VehicleInstructionData> list = new ArrayList<>( all);
            VehicleInstructionData data = list.get(list.size() - 1);
            return !data.isFinished();
        }else{
            return  true ;
        }

    }
}
