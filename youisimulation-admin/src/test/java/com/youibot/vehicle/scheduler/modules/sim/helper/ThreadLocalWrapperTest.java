package com.youibot.vehicle.scheduler.modules.sim.helper;

import static org.junit.Assert.*;

public class ThreadLocalWrapperTest {


        public static void main(String[] args) throws InterruptedException {
            // 设置最大容量为 3
            ThreadLocalWrapper<String> threadLocalWrapper = new ThreadLocalWrapper<>(3);

            // 在多个线程中设置 ThreadLocal 值
            for (int i = 1; i <= 5; i++) {
                final int index = i;
                new Thread(() -> {
                    threadLocalWrapper.set("Thread " + index);
                    System.out.println("Thread " + index + ": " + threadLocalWrapper.get());
                }).start();
                Thread.sleep(1000);  // 模拟一些延迟
            }

            Thread.sleep(5000); // 等待所有线程执行完成

            // 输出当前存储的线程数量
            System.out.println("Current size of stored threads: " + threadLocalWrapper.currentSize());
        }


}