package com.youibot.vehicle.scheduler.map.pathsplit2;

import com.youibot.vehicle.scheduler.modules.sim.dto.Point;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.geom.GeneralPath;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class AGVSimulation extends JPanel {
    private static final int WIDTH = 800;
    private static final int HEIGHT = 600;
    private static final int TIMER_DELAY = 50; // Timer delay in milliseconds

    private List<Point> pathPoints;
    private Map<Integer, Double> segmentSpeeds;
    private List<Integer> segmentStarts;
    private int currentSegmentIndex = 0;
    private double t = 0;
    private Timer timer;
    private double maxSpeed = 500.0; // Maximum speed to adjust density inversely
    private double minDensity = 10;  // Minimum density when speed is at maximum
    private double maxDensity = 100; // Maximum density when speed is at minimum

    public AGVSimulation(List<Point> pathPoints, Map<Integer, Double> segmentSpeeds) {
        this.pathPoints = pathPoints;
        this.segmentSpeeds = segmentSpeeds;
        this.segmentStarts = new ArrayList<>();
        setSegmentStarts(); // Initialize segmentStarts

        setLayout(new BorderLayout());

        JButton restartButton = new JButton("Restart");
        restartButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                restartSimulation();
            }
        });

        JPanel controlPanel = new JPanel();
        controlPanel.setLayout(new GridLayout(segmentSpeeds.size() + 1, 2));
        for (int i = 0; i < segmentSpeeds.size(); i++) {
            final int segmentIndex = i;
            JLabel label = new JLabel("Speed for Segment " + i + ":");
            JTextField speedField = new JTextField(String.valueOf(segmentSpeeds.getOrDefault(i, 1.0)));
            speedField.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    try {
                        double speed = Double.parseDouble(speedField.getText());
                        segmentSpeeds.put(segmentIndex, speed);
                        updatePathPoints();
                        setSegmentStarts(); // Update segment starts after path update
                        restartSimulation();
                    } catch (NumberFormatException ex) {
                        JOptionPane.showMessageDialog(null, "Invalid speed value.");
                    }
                }
            });
            controlPanel.add(label);
            controlPanel.add(speedField);
        }

        add(controlPanel, BorderLayout.NORTH);
        add(restartButton, BorderLayout.SOUTH);

        timer = new Timer(TIMER_DELAY, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                updateAGVPosition();
                repaint();
            }
        });
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        drawPath(g2d);
        drawAGV(g2d);
    }

    private void drawPath(Graphics2D g2d) {
        g2d.setColor(Color.BLACK);
        GeneralPath path = new GeneralPath();
        if (!pathPoints.isEmpty()) {
            Point firstPoint = pathPoints.get(0);
            path.moveTo(firstPoint.getX(), firstPoint.getY());
            for (int i = 1; i < pathPoints.size(); i++) {
                Point point = pathPoints.get(i);
                path.lineTo(point.getX(), point.getY());
            }
            g2d.draw(path);
        }
    }

    private void drawAGV(Graphics2D g2d) {
        if (currentSegmentIndex < pathPoints.size()) {
            Point agvPosition = pathPoints.get(currentSegmentIndex);
            g2d.setColor(Color.RED);
            g2d.fillOval( agvPosition.getX().intValue() - 10,  agvPosition.getY() .intValue()- 10, 20, 20);
            System.out.println("draw_agvPosition = " + agvPosition);
        }
    }

    private void updateAGVPosition() {
        if (currentSegmentIndex < pathPoints.size()) {
            double speed = getCurrentSegmentSpeed();
            double stepSize = 1.0 / getCurrentSegmentDensity(); // Adjust step size based on density
            t += stepSize;
            if (t >= 1.0) {
                t = 0.0;
                currentSegmentIndex++;
                if (currentSegmentIndex >= pathPoints.size()) {
                    currentSegmentIndex = pathPoints.size() - 1;
                    timer.stop(); // Stop timer when reaching the end
                }
            }
            System.out.println("AGV Position Updated: Index = " + currentSegmentIndex + ", t = " + t + ", Speed = " + speed);
        }
    }

    private double getCurrentSegmentSpeed() {
        int segmentIndex = findSegmentIndex();
        double speed = segmentSpeeds.getOrDefault(segmentIndex, 1.0); // Default speed if not found
        return speed;
    }

    private int findSegmentIndex() {
        for (int i = 0; i < segmentStarts.size() - 1; i++) {
            if (currentSegmentIndex >= segmentStarts.get(i) && currentSegmentIndex < segmentStarts.get(i + 1)) {
                return i;
            }
        }
        return segmentStarts.size() - 1; // Last segment if no other found
    }

    private double getCurrentSegmentDensity() {
        double speed = getCurrentSegmentSpeed();
        // Calculate density inversely proportional to speed
        return Math.max(minDensity, maxDensity * (1 - speed / maxSpeed));
    }

    private void updatePathPoints() {
        List<Point> updatedPoints = new ArrayList<>();
        for (int i = 0; i < pathPoints.size() - 1; i++) {
            Point start = pathPoints.get(i);
            Point end = pathPoints.get(i + 1);
            int segments = (int) getCurrentSegmentDensity(); // Get the number of segments based on density
            if (segments < 1) segments = 1; // Ensure at least one segment
            List<Point> segmentPoints = PathUtils.splitLine(start, end, segments);
            updatedPoints.addAll(segmentPoints);
        }
        pathPoints = updatedPoints;
    }

    private void setSegmentStarts() {
        segmentStarts.clear();
        int startIndex = 0;
        for (int i = 0; i < pathPoints.size() - 1; i++) {
            segmentStarts.add(startIndex);
            int segments = (int) getCurrentSegmentDensity(); // Adjust density based on speed
            startIndex += segments;
        }
        if (!pathPoints.isEmpty()) {
            segmentStarts.add(startIndex); // Add end index
        }
        System.out.println("Segment Starts: " + segmentStarts);
    }

    private void restartSimulation() {
        currentSegmentIndex = 0;
        t = 0;
        updatePathPoints();
        setSegmentStarts(); // Update segment starts for new path
        timer.start(); // Restart timer to animate AGV
    }

    public static void main(String[] args) {
        List<Point> bezierPoints = PathUtils.splitBezierCurve(
            new Point(100.0, 500.0),
            new Point(200.0, 100.0),
            new Point(600.0, 100.0),
            new Point(700.0, 500.0),
            100
        );
        List<Point> linePoints = PathUtils.splitLine(
            new Point(700.0, 500.0),
            new Point(800.0, 200.0),
            50
        );

        bezierPoints.addAll(linePoints);

        Map<Integer, Double> speeds = new HashMap<>();
        speeds.put(0, 5.0); // Speed for Bezier segment
        speeds.put(1, 500.0); // Speed for Line segment

        JFrame frame = new JFrame("AGV Simulation");
        AGVSimulation panel = new AGVSimulation(bezierPoints, speeds);
        frame.add(panel);
        frame.setSize(WIDTH, HEIGHT);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}
