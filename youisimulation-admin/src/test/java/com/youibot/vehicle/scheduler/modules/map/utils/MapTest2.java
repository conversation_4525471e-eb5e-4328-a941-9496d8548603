package com.youibot.vehicle.scheduler.modules.map.utils;

import com.alibaba.fastjson.JSON;
import com.dtflys.forest.utils.StringUtils;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.youibot.vehicle.scheduler.BaseTest;
import com.youibot.vehicle.scheduler.common.util.VehicleUtils;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.sim.dto.Point;
import com.youibot.vehicle.scheduler.modules.sim.helper.BezierUtil;
import com.youibot.vehicle.scheduler.modules.sim.helper.PathUtils;
import com.youibot.vehicle.scheduler.modules.sim.helper.PurePursuit;
import com.youibot.vehicle.scheduler.modules.sim.helper.SidePathUtils;
import com.youibot.vehicle.scheduler.modules.status.dto.DefaultVehicleStatus;
import com.youibot.vehicle.scheduler.modules.status.dto.pilot.PositionStatus;
import com.youibot.vehicle.scheduler.modules.status.dto.pilot.SpeedStatus;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.util.DevicesDataPushUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

import static com.youibot.vehicle.scheduler.common.util.VehicleUtils.vehicle;
import static com.youibot.vehicle.scheduler.modules.sim.constants.PathSimulationConstant.AGV_DIRECTION_NORMAL;
import static com.youibot.vehicle.scheduler.modules.sim.constants.PathSimulationConstant.AGV_DIRECTION_REVERSE;

@Slf4j
public class MapTest2 extends BaseTest {
    private List<Path> sidePaths= Lists.newArrayList();

    private LinkedBlockingDeque<Point> pointQueue = new LinkedBlockingDeque<>();

    private PriorityQueue<PointAndDistance> pointSort = new PriorityQueue<>(new Cmp());

    private static double k = 0.01; // 前视距离系数
    private static double Lfc = 0.05; //前视距离
    private static double Kp = 1.0; //速度P控制器系数
    private static double dt = 0.01; //时间间隔，单位：s
    private static double L = 0.2; //车辆轴距，单位：m

    private Point targetPoint = null;

    private State state = new State();

    String mapCode  ="10.0.60.15:16646_FinalMap0611";
    String locatingCode= "AT_v2";

    List<String> pathIds = ImmutableList.of(
            "FinalMap0611_L_2159_2163",
            "FinalMap0611_L_2163_2162",
            "FinalMap0611_L_2162_2161",
            "FinalMap0611_L_2161_2003",
            "FinalMap0611_L_2003_1968",
            "FinalMap0611_L_1968_1933",
            "FinalMap0611_L_1933_1898",
            "FinalMap0611_L_1898_3786"
    );
    @Before
    public void init() {

        pathIds.forEach(pathCode -> {
          Path  path = MapGraph.getPath( pathCode);
            sidePaths.add(path);
        });


    }


    @Test
    public void testAngle(){

        Double[] inOutAngle = SidePathUtils.getInOutAngle(sidePaths.get(0) ,  locatingCode );

    }
    @Test
    public void test1(){

        int t_num = 100;
        double resolution = 1.0 / t_num;
        for (Path sidePath : sidePaths) {
            Double t0 = sidePath.getT0();
            if(Objects.isNull(t0)){
                t0 = 0D;
            }
            double t = t0;
            while (t <= 1.0) {
                Point point = PathUtils.getSidePathPoint(sidePath, t, locatingCode);
                if (point != null && BezierUtil.isPtInPoly( SidePathUtils.getPathControlPoints(sidePath ,locatingCode ) , point , 0.2)) {
                    point.setPathId( sidePath.getCode() );
                    pointQueue.add(point);
                }
                t += resolution;
            }
        }



        Path lastSidePath = sidePaths.get(sidePaths.size() - 1);
        targetPoint = PathUtils.getSidePathPoint(lastSidePath, 1D, locatingCode );
        pointQueue.add(targetPoint);
        log.debug("lastSidePath = " + lastSidePath);

        log.debug("pointQueue size "+  pointQueue.size());
        log.debug( "targetPoint " + targetPoint );
        Integer direction = sidePaths.get(0).getAgvDirection();
        Double[] inOutAngle = SidePathUtils.getInOutAngle(sidePaths.get(0), locatingCode);


        int agvCode = 1 ;
        double target_speed_v = 2;

        while (pointQueue.size() > 1) {//|| targetPoint.minus(new Point(state.x, state.y)).getNorm() > 0.01
            try {
              
                double acc = PIDControl(target_speed_v, state.v);
                Point temp_target_point = calc_target_point(state);
                ;log.debug("temp_target_point = " + temp_target_point);
                double delta = pure_pursuit_control(state, temp_target_point);
                update(state, acc, delta);



//                vehicle.getWriteLock().lock();
                setAGVPosition("", state, direction , temp_target_point);
;
                TimeUnit.MILLISECONDS.sleep((int) (dt * 1000));

            } catch (InterruptedException e) {
                log.debug("Throwables.getStackTraceAsString(e) = " + Throwables.getStackTraceAsString(e));
            }finally {
//                vehicle.getWriteLock().unlock();
            }
        }
        Point poll = calc_target_point(state);
        state.x = poll.getX();
        state.y = poll.getY();
        state.v = 0;
        state.w = 0;
        setAGVPosition("agvCode", state, direction , poll);
        
    }


    public void setAGVPosition(String agvCode, State state, Integer direction, Point point) {

        DefaultVehicleStatus defaultVehicleStatus = new DefaultVehicleStatus();

        PositionStatus positionStatus =  new PositionStatus();

        SpeedStatus speedStatus = new SpeedStatus();


        positionStatus.setPos_x(state.x);
        positionStatus.setPos_y(state.y);
        positionStatus.setSegment_id( point.getPathId());
        Double pos_angle = 0d;
        switch (direction) {
            case AGV_DIRECTION_NORMAL:
                pos_angle = limitRad(state.yaw);
                break;
            case AGV_DIRECTION_REVERSE:
                pos_angle = limitRad(state.yaw + Math.PI);
                break;
        }
        positionStatus.setPos_angle(limitRad(pos_angle));


        speedStatus.setSpeed_vx(state.v);
        speedStatus.setSpeed_vy(0d);
        speedStatus.setSpeed_w(state.w);
        log.debug("J = " + JSON.toJSONString(positionStatus));
        log.debug("speed = " + JSON.toJSONString(speedStatus));
        if(StringUtils.isNotBlank( point.getPathId())){
            Path path = MapGraph.getPath(point.getPathId());
            boolean isPtInPoly = BezierUtil.isPtInPoly(path.getControlPoints(locatingCode).toArray(new Point[4]), point, this.getTrackRadius());
            if (!isPtInPoly) {
                System.err.println( path.getOffsetX() +",,,not_in_path: "  + point.getX() + " " + point.getY() + ": " + isPtInPoly);
            }

            defaultVehicleStatus.setPosition(positionStatus);
            defaultVehicleStatus.setSpeed(speedStatus);
        }




    }

    private Double getTrackRadius() {
        return 0.2D;
    }

    private void update(State state, double acc, double delta) {
        double x = state.x + state.v * Math.cos(state.yaw) * dt;
        double y = state.y + state.v * Math.sin(state.yaw) * dt;
        double yaw = state.yaw + state.v / L * Math.tan(delta) * dt;
        double v = state.v + acc * dt;
        double w = (yaw - state.yaw) / dt;
        state.setValue(x, y, yaw, v, w);
    }

    
    
    private double PIDControl(double target, double current) {
        return Kp * (target - current);
    }

    private double pure_pursuit_control(State state, Point point) {
        double tx = point.getX();
        double ty = point.getY();
        double alpha = Math.atan2(ty - state.y, tx - state.x) - state.yaw;
        if (state.v < 0) {
            alpha = Math.PI - alpha;
        }
        double Lf = k * state.v + Lfc;
        return Math.atan2(2.0 * L * Math.sin(alpha) / Lf, 1.0); //delta
    }

    private Point calc_target_point(State state) {
        if (pointQueue.size() <= 1) {
            return targetPoint;
        }
        pointSort.clear();
        Point current_point = new Point(state.x, state.y);
        for (Point point : pointQueue) {
            PointAndDistance pointAndDistance = new PointAndDistance(point, point.minus(current_point).getNorm());
            pointSort.add(pointAndDistance);
        }
       PointAndDistance nearPointAndDistance = pointSort.poll();
        if (nearPointAndDistance == null) {
            return targetPoint;
        }

        Point nearPoint = nearPointAndDistance.point;

        Point point = pointQueue.peek();
        while (point != null && !point.equals(nearPoint)) {
            point = pointQueue.poll();
        }

        double Lf = k * state.v + Lfc;
        double l = nearPointAndDistance.distance;

        double tempLf = new Point(l, Lf).getNorm();
        List<Point> tempPointList = new ArrayList<>(pointQueue);
        int q = 0;
        while (l < tempLf && q < tempPointList.size()) {
            Point new_point = tempPointList.get(q);
            l += new_point.minus(point).getNorm();
            point = new_point;
            q++;
        }
        return point;
    }


    public static Double limitRad(Double a) {
        a = a % (2 * Math.PI);
        if (a > Math.PI) {
            a -= 2 * Math.PI;
        } else if (a < -Math.PI) {
            a += 2 * Math.PI;
        }
        return a;
    }

    private static class PointAndDistance {
        public Point point;
        public Double distance;

        public PointAndDistance(Point point, Double distance) {
            this.point = point;
            this.distance = distance;
        }
    }


    static class Cmp implements Comparator<PointAndDistance> {
        @Override
        public int compare(PointAndDistance p1,PointAndDistance p2) {
            BigDecimal p1Distance = new BigDecimal(p1.distance);
            p1Distance = p1Distance.setScale(5, RoundingMode.HALF_UP);
            BigDecimal p2Distance = new BigDecimal(p2.distance);
            p2Distance= p2Distance.setScale(5, BigDecimal.ROUND_HALF_UP);
            return p1Distance.compareTo(p2Distance);
        }
    }

   static public class State {
        private double x;
        private double y;
        private double yaw;
        private double v;
        private double w;

        public void setValue(double x, double y, double yaw, double v, double w) {
            this.x = x;
            this.y = y;
            this.yaw = limitRad(yaw);
            this.v = v;
            this.w = w;
        }
    }


}

