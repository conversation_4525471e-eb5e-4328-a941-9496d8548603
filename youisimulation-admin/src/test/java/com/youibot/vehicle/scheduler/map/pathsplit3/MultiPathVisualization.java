package com.youibot.vehicle.scheduler.map.pathsplit3;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.*;
import java.util.ArrayList;
import java.util.List;

public class MultiPathVisualization extends JPanel {

    private List<GeneralPath> originalPaths;
    private List<List<Point2D.Double>> splitPoints;

    public MultiPathVisualization(List<GeneralPath> originalPaths, List<List<Point2D.Double>> splitPoints) {
        this.originalPaths = originalPaths;
        this.splitPoints = splitPoints;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // Draw Original Paths
        g2d.setColor(Color.BLUE);
        for (GeneralPath path : originalPaths) {
            g2d.draw(path);
        }

        // Draw Split Points
        g2d.setColor(Color.GREEN);
        for (List<Point2D.Double> points : splitPoints) {
            for (Point2D.Double point : points) {
                g2d.fill(new Ellipse2D.Double(point.getX() - 2, point.getY() - 2, 4, 4));
            }
        }
    }

    public static void main(String[] args) {
        // Define some example paths
        List<GeneralPath> originalPaths = new ArrayList<>();

        // Add Bezier curves
        for (int i = 0; i < 10; i++) {
            GeneralPath bezierPath = new GeneralPath();
            Point2D.Double p0 = new Point2D.Double(50 + i * 50, 300);
            Point2D.Double p1 = new Point2D.Double(100 + i * 50, 100);
            Point2D.Double p2 = new Point2D.Double(200 + i * 50, 100);
            Point2D.Double p3 = new Point2D.Double(250 + i * 50, 300);
            bezierPath.moveTo(p0.getX(), p0.getY());
            bezierPath.curveTo(p1.getX(), p1.getY(), p2.getX(), p2.getY(), p3.getX(), p3.getY());
            originalPaths.add(bezierPath);
        }

        // Add lines
        for (int i = 10; i < 20; i++) {
            GeneralPath linePath = new GeneralPath();
            Point2D.Double l0 = new Point2D.Double(50 + (i - 10) * 50, 300);
            Point2D.Double l1 = new Point2D.Double(100 + (i - 10) * 50, 100);
            linePath.moveTo(l0.getX(), l0.getY());
            linePath.lineTo(l1.getX(), l1.getY());
            originalPaths.add(linePath);
        }

        // Split paths into equal intervals
        List<List<Point2D.Double>> splitPoints = new ArrayList<>();
        for (GeneralPath path : originalPaths) {
            List<Point2D.Double> points = new ArrayList<>();
            PathIterator iterator = path.getPathIterator(null);
            double[] coords = new double[6];
            double[] prevCoords = new double[2];
            boolean firstPoint = true;

            while (!iterator.isDone()) {
                int type = iterator.currentSegment(coords);
                if (type == PathIterator.SEG_MOVETO || type == PathIterator.SEG_LINETO) {
                    if (!firstPoint) {
                        addEqualIntervalPoints(points, prevCoords[0], prevCoords[1], coords[0], coords[1], 20);
                    }
                    prevCoords[0] = coords[0];
                    prevCoords[1] = coords[1];
                    firstPoint = false;
                }
                iterator.next();
            }
            splitPoints.add(points);
        }

        // Create and show the JFrame
        JFrame frame = new JFrame("Multi Path Visualization");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.add(new MultiPathVisualization(originalPaths, splitPoints));
        frame.setVisible(true);
    }

    private static void addEqualIntervalPoints(List<Point2D.Double> points, double x0, double y0, double x1, double y1, int intervalCount) {
        double dx = (x1 - x0) / intervalCount;
        double dy = (y1 - y0) / intervalCount;
        for (int i = 0; i <= intervalCount; i++) {
            double x = x0 + i * dx;
            double y = y0 + i * dy;
            points.add(new Point2D.Double(x, y));
        }
    }
}
