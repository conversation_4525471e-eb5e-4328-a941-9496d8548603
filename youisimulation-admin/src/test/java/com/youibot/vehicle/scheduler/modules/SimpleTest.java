package com.youibot.vehicle.scheduler.modules;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.youibot.vehicle.scheduler.common.utils.RandomUtil;
import com.youibot.vehicle.scheduler.modules.status.dto.DefaultVehicleStatus;
import com.youibot.vehicle.scheduler.modules.status.dto.pilot.BatteryStatus;
import com.youibot.vehicle.scheduler.modules.status.dto.pilot.ErrorStatus;
import com.youibot.vehicle.scheduler.modules.status.dto.pilot.PositionStatus;
import com.youibot.vehicle.scheduler.modules.status.dto.pilot.RuntimeStatus;
import org.jeasy.random.EasyRandom;
import org.jeasy.random.EasyRandomParameters;
import org.jeasy.random.api.Randomizer;
import org.junit.Test;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Random;
import java.util.function.Predicate;

public class SimpleTest {

    String map_data= "";
    @Test
    public void test1(){
        EasyRandom easyRandom = new EasyRandom();
        DefaultVehicleStatus defaultVehicleStatus =  easyRandom.nextObject( DefaultVehicleStatus.class);
        System.out.println("JSON.toJSONString( defaultVehicleStatus ) = " + JSON.toJSONString( defaultVehicleStatus ));


    }

    @Test
    public void test2(){

        int min = 1;
        int max = 100;
        int a = RandomUtil.randomInt( min , max );
        int b = RandomUtil.randomInt( min , max );
        Integer[] res = new Integer[]{a , b};
        for (int i = 0; i < res.length; i++) {
            System.out.println(res[i]);
        }
    }

    // 定义一个字符串列表，包含可能的值
    List<String> possibleValues = ImmutableList.of("Apple", "Banana", "Cherry", "Date", "Elderberry");




    // 自定义 Randomizer 从指定的字符串列表中随机选择一个值
    Randomizer<String> stringRandomizer = new Randomizer<String>() {
        private final Random random = new Random();

        @Override
        public String getRandomValue() {
            return possibleValues.get(random.nextInt(possibleValues.size()));
        }
    };

    // 定义 Predicate 来筛选字段，这里筛选字段名称包含 "favorite" 的字段
    Predicate<Field> favoriteFieldPredicate = field -> field.getName().contains("favorite");


    // 配置 EasyRandomParameters，指定字符串字段的随机化规则
    EasyRandomParameters parameters = new EasyRandomParameters()
            .randomize(String.class , stringRandomizer);


    // 自定义 Randomizer，为 street 字段设置固定值
    static Randomizer<String> robotStatusRandomizer = () -> "normal";

    // 定义 Predicate 来筛选 Address 类中的 street 字段
    static  Predicate<Field> robotStatusPredicate = field ->
            field.getName().equals("robot_status") && field.getDeclaringClass() == ErrorStatus.class;

    // 配置 EasyRandomParameters，应用自定义的 Randomizer 到 street 字段
   static   EasyRandomParameters defaultVehicleStatusParameters = new EasyRandomParameters()
            .randomize( robotStatusPredicate,  robotStatusRandomizer )

            ;


    static EasyRandom easyRandom = new EasyRandom( defaultVehicleStatusParameters);

    static  DefaultVehicleStatus defaultVehicleStatus =  easyRandom.nextObject( DefaultVehicleStatus.class);

    @Test
    public  void testDefaultVehicleStatus3(){

        ErrorStatus status = defaultVehicleStatus.getStatus();
        System.out.println("status.getRobot_status()  = " + status.getRobot_status() );



    }


    static List<String> doubleFileds = ImmutableList.of(
            "battery_value",
            "battery_charge",
            "battery_discharge",
            "battery_voltage",
            "battery_temp",
            "battery_health" );

    static List<String> arrayFileds = ImmutableList.of(
            "battery_cell_temperatures",
            "battery_mosfet_temperatures"
           );
    /**
     * @see com.youibot.vehicle.scheduler.modules.status.dto.pilot.BatteryStatus
     */
    @Test
    public  void testBatteryStatus(){

        // 自定义年龄生成器，生成范围在 18 到 65 之间
        Randomizer<Double> ageRandomizer = () -> (Double) (0 + Math.random() * (100D - 0D));
//        Predicate<Field> robotStatusPredicate = field -> field.getName().equals("battery_value") || field.getName().equals("battery_health");
        Predicate<Field> robotStatusPredicate = field -> doubleFileds.contains( field.getName() );

//        Randomizer<Double> ageRandomizer2 = () -> (Double) (0 + Math.random() * (100D - 0D));
        Predicate<Field> arrayFiledsPredicate = field -> arrayFileds.contains( field.getName() );

        // 自定义 Randomizer，为 double 数组中的每个元素生成在指定范围内的值
        Randomizer<Integer[]> integerRandomizer = () -> {

            int min = 1;
            int max = 100;
            int a =   RandomUtil.randomInt( min , max ) ;
            int b =   RandomUtil.randomInt( min , max ) ;
            return new Integer[]{a , b}; // 生成 min 到 max 之间的随机 double 值
        };

        EasyRandom easyRandom = new EasyRandom( new EasyRandomParameters()

                .randomize(robotStatusPredicate ,ageRandomizer )
                .collectionSizeRange(2, 3)
                .randomize(arrayFiledsPredicate ,integerRandomizer)

//                .randomize(robotStatusPredicate2 ,ageRandomizer2 )
        );

        BatteryStatus batteryStatus = easyRandom.nextObject(BatteryStatus.class);
        System.out.println("batteryStatus = " + batteryStatus);

    }

    /**
     * @see com.youibot.vehicle.scheduler.modules.status.dto.pilot.RuntimeStatus
     */
    @Test
    public  void testRuntimeStatus() {


            Randomizer<Integer> agvStatusRandomizer = () -> 0;

            // 定义 Predicate 来筛选 Address 类中的 street 字段
            Predicate<Field>  agvStatusPredicate = field ->
                    field.getName().equals("agv_status") && field.getDeclaringClass() == RuntimeStatus.class;



            Randomizer<Integer>  agvModeRandomizer = () -> 1;

            // 定义 Predicate 来筛选 Address 类中的 street 字段
            Predicate<Field>  agvModePredicate = field ->
                    field.getName().equals("agv_mode") && field.getDeclaringClass() == RuntimeStatus.class;


            Randomizer<Integer>  dockingStatusRandomizer = () -> 1;

            // 定义 Predicate 来筛选 Address 类中的 street 字段
            Predicate<Field> dockingStatusPredicate = field ->
                    field.getName().equals("docking_status") && field.getDeclaringClass() == RuntimeStatus.class;



            Randomizer<Integer> followStatusRandomizer = () -> 0;

            // 定义 Predicate 来筛选 Address 类中的 street 字段
            Predicate<Field>   followStatusPredicate = field ->
                    field.getName().equals("follow_status") && field.getDeclaringClass() == RuntimeStatus.class;


         List<String> customeDoubleFileds = ImmutableList.of(
                "odom",
                "controller_temp",
                "motor_temp"
                );


        // 自定义年龄生成器，生成范围在 18 到 65 之间
        Randomizer<Double> doubleRandomizer  = () -> (Double) (0 + Math.random() * (100D - 0D));
        Predicate<Field> doublePredicate = field -> customeDoubleFileds.contains( field.getName() );



        EasyRandom easyRandom = new EasyRandom(new EasyRandomParameters()

                .randomize(agvStatusPredicate, agvStatusRandomizer)
                .randomize(agvModePredicate, agvModeRandomizer)
                .randomize(dockingStatusPredicate, dockingStatusRandomizer)
                .randomize(followStatusPredicate, followStatusRandomizer)
                .randomize(doublePredicate , doubleRandomizer)
                .collectionSizeRange(2, 3)


//                .randomize(robotStatusPredicate2 ,ageRandomizer2 )
        );

        RuntimeStatus runtimeStatus = easyRandom.nextObject(RuntimeStatus.class);

        System.out.println("runtimeStatus = " + runtimeStatus);
    }


     @Test
    public  void testPositionStatus(){
         PositionStatus positionStatus = new PositionStatus();

     }
}
