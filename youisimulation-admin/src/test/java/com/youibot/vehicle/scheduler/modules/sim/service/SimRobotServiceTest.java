package com.youibot.vehicle.scheduler.modules.sim.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.youibot.vehicle.scheduler.AdminApplication;
import com.youibot.vehicle.scheduler.common.utils.RandomUtil;
import com.youibot.vehicle.scheduler.modules.sim.dao.SimRobotMapper;
import com.youibot.vehicle.scheduler.modules.sim.entity.SimConf;
import com.youibot.vehicle.scheduler.modules.sim.entity.SimRobot;
import com.youibot.vehicle.scheduler.modules.sim.enums.EnableStatus;
import com.youibot.vehicle.scheduler.modules.sim.enums.ExceptionStatus;
import com.youibot.vehicle.scheduler.modules.sim.enums.PauseStatus;
import com.youibot.vehicle.scheduler.modules.sim.enums.WorkStatus;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = AdminApplication.class)
@SpringBootTest
public class SimRobotServiceTest {


    @Autowired
    private SimRobotMapper simRobotMapper ;
    @Autowired
    private  SimRobotService simRobotService ;

    @Autowired
    private  SimConfService simConfService;

    @Test
    public  void test1(){
        String confId ="1816040301819637762";
        SimRobot robot = new SimRobot();
        robot.setMac(RandomUtil.randomString( 10 ));
        robot.setEnabled(EnableStatus.DISABLE );
        robot.setBatteryValue( RandomUtil.randomDouble( 0, 100 ));
        robot.setCurrentMapId( RandomUtil.randomString( 2 ));
        robot.setExceptionStatus( ExceptionStatus.NORMAL );
        robot.setConfName( confId );
        boolean save = simRobotService.save( robot );
        System.out.println("save = " + save);
        System.out.println("id = " + robot.getVehicleCode() );
        System.out.println("enable = " + robot.getEnabled());

    }

    @Test
  public   void testJoin() {
        //和Mybatis plus一致，MPJLambdaWrapper的泛型必须是主表的泛型，并且要用主表的Mapper来调用
        MPJLambdaWrapper<SimRobot> wrapper = JoinWrappers.lambda(SimRobot.class)
                .selectAll(SimRobot.class)//查询user表全部字段
                .select(SimConf::getName)//查询user_address tel 字段
                .selectAs(SimConf::getName, SimRobot::getConfName)//别名
                .innerJoin(SimConf.class, SimConf::getName, SimRobot::getConfName)
                
        ;

        //连表查询 返回自定义ResultType
        List<SimRobot> list = simRobotMapper.selectJoinList(SimRobot.class, wrapper);

        //分页查询 （需要启用 mybatis plus 分页插件）
        SimRobot robot = list.get(0);
        System.out.println("robot.getConfName()  = "
                + robot.getConfName() );
        System.out.println("robot.getConfName()  = "
                + robot.getConfName() );

    }
        @Test
    public  void testGetById(){

        String id = "1818201244502601729";
        SimRobot robot = simRobotService.selectById( id );
        System.out.println("robot = " +  robot.getExceptionStatus() );

    }


    @Test
    public  void testBatch(){
        String confId ="00ekgxnz1j";
        List<SimRobot> list = Lists.newArrayList();
        for (int i = 0; i < 1000 ; i++) {
            SimRobot robot = new SimRobot();
//            robot.setVehicleCode( RandomUtil.randomString( 10 ));
            robot.setMac(RandomUtil.generateRandomMac());
            robot.setEnabled( RandomUtil.randomEnumValue( EnableStatus.class ));
            robot.setExceptionStatus( RandomUtil.randomEnumValue( ExceptionStatus.class ));
            robot.setWorkStatus( RandomUtil.randomEnumValue(WorkStatus.class));
            robot.setPauseStatus( RandomUtil.randomEnumValue(PauseStatus.class));
            robot.setBatteryValue( RandomUtil.randomDouble( 0, 100 ));
            robot.setCurrentMapId( RandomUtil.randomString( 2 ));
            robot.setConfName( confId );
            list.add( robot) ;

        }
        boolean b = simRobotService.saveBatch(list);
        System.out.println("b = " + b);
        System.out.println("list = " + JSON.toJSONString( list ));
    }

    @Test
    public  void testUpdate(){

        String mac = "00:01:F2:52:77:4C";
        String newName ="yyou_name_is_top_yy";


        LambdaUpdateWrapper<SimRobot> simRobotLambdaUpdateWrapper = Wrappers.lambdaUpdate(SimRobot.class).eq(SimRobot::getMac, mac).set(SimRobot::getCurrentMapId, newName);
        boolean update = simRobotService.update( simRobotLambdaUpdateWrapper);
        System.out.println("update = " + update);

    }

    @Test
    public  void testUpdate2(){
        String mac = "0C:3B:21:CD:E3:4F";
        SimRobot robot = simRobotService.selectById(mac);
        System.out.println("robot = " + robot.getIsSim());



    }


    public static String getMethodParameterSignature(Method method) {
        return Arrays.stream(method.getParameterTypes())
                .map(Class::getTypeName)
                .collect(Collectors.joining(", ", "(", ")"));
    }
}