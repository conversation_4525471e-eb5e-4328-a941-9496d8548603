/*
package com.youibot.vehicle.scheduler.map.mapTest;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import com.mxgraph.layout.hierarchical.mxHierarchicalLayout;
import com.mxgraph.swing.mxGraphComponent;
import com.youibot.vehicle.scheduler.modules.map.dto.PathInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.MapGraphInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import org.apache.commons.io.FileUtils;
import org.jgrapht.Graph;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

*/
/**
 * 随机均匀分布在有向图的不同节点
 * 带节点排除  双向路径 路径产生重叠  边产生了分离
 * 但是顶点产生了重叠
 * @see  com.youibot.vehicle.scheduler.map.dis2.GraphVisualization
 *//*

public class GraphVisualization4 {

    private static  String roadNet ="E:\\home\\youibot\\youibot_map\\map724_roadnet\\locating\\current\\map724.roadnet" ;


    public static void main(String[] args) throws IOException {
        // Create a directed weighted graph with isolated nodes
        String s = FileUtils.readFileToString(new File(roadNet), Charsets.UTF_8);

        MapGraphInfo mapGraphInfo = JSON.parseObject(s, MapGraphInfo.class);

        DirectedWeightedPseudograph<Marker, DefaultWeightedEdge> graph =
                new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        Map<String, Marker> markerMap = new HashMap<>();

        mapGraphInfo.getMarkers().forEach( marker -> {

            graph.addVertex(marker);
            markerMap.put(marker.getCode(), marker);
        });
        // 添加顶点
        mapGraphInfo.getPaths().forEach( path -> {
            List<PathInfo> pathInfos = path.getPathInfos();
            PathInfo pathInfo = pathInfos.get(0);
            Marker source = markerMap.get(path.getStartMarkerCode());
            Marker target = markerMap.get(path.getEndMarkerCode());

            DefaultWeightedEdge edge = graph.addEdge(source, target);
            // 确保边权重是正数
            graph.setEdgeWeight(edge,  pathInfo.getLength() ); // 权重在 1 到 10 之间


        });

        Marker marker1 = markerMap.get("map724_P_1");


        // Nodes to exclude
        Set<Marker> nodesToExclude = new HashSet<>(Collections.singletonList( marker1));

        // Number of nodes to select
        int n = 3;

        // Select and highlight nodes
        List<Marker> selectedNodes = selectAndDistributeNodes(graph, n, nodesToExclude);
        visualizeGraph(graph, selectedNodes);
    }

    private static List<Marker> selectAndDistributeNodes(Graph<Marker, DefaultWeightedEdge> graph, int n, Set<Marker> nodesToExclude) {
        // Filter out isolated vertices and nodes to exclude
        List<Marker> nonIsolatedVertices = graph.vertexSet().stream()
                .filter(vertex -> graph.edgesOf(vertex).size() > 0 && !nodesToExclude.contains(vertex))
                .collect(Collectors.toList());

        if (nonIsolatedVertices.size() < n) {
            throw new IllegalArgumentException("Not enough non-isolated vertices for the number of nodes.");
        }

        // Select n nodes uniformly distributed
        List<Marker> selectedNodes = new ArrayList<>();
        int step = nonIsolatedVertices.size() / n;
        for (int i = 0; i < n; i++) {
            selectedNodes.add(nonIsolatedVertices.get(i * step));
        }

        // Ensure no selected node is isolated
        return selectedNodes;
    }

    private static void visualizeGraph(Graph<Marker, DefaultWeightedEdge> graph, List<Marker> highlightedNodes) {
        // Create JGraphX adapter
        JGraphXAdapter<Marker, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // Create a Swing window to display the graph
        JFrame frame = new JFrame();
        frame.setLayout(new BorderLayout());

        // Graph panel
        mxGraphComponent graphComponent = new mxGraphComponent(graphAdapter);
        frame.add(graphComponent, BorderLayout.CENTER);

        // Use hierarchical layout
        mxHierarchicalLayout layout = new mxHierarchicalLayout(graphAdapter);
        layout.setIntraCellSpacing(150); // Space between nodes at the same level
        layout.setInterRankCellSpacing(200); // Space between levels
        // Execute layout
        layout.execute(graphAdapter.getDefaultParent());

        // Highlight nodes
        graphAdapter.getModel().beginUpdate();
        try {
            // Define colors
            Color highlightColor = Color.RED;
            String highlightStyle = "fillColor=" + toHexString(highlightColor) + ";strokeColor=" + toHexString(highlightColor) + ";";

            // Highlight nodes
            for (Marker node : highlightedNodes) {
                graphAdapter.setCellStyle(highlightStyle, new Object[]{graphAdapter.getVertexToCellMap().get(node)});
            }

            // Adjust edge labels
            for (DefaultWeightedEdge edge : graph.edgeSet()) {
                Object edgeCell = graphAdapter.getEdgeToCellMap().get(edge);
                Marker source = graph.getEdgeSource(edge);
                Marker target = graph.getEdgeTarget(edge);
                double weight = graph.getEdgeWeight(edge);

                // Create label with source, target, and weight
                String label = String.format("%s -> %s (%.2f)", source.getCode(), target.getCode(), weight);
                graphAdapter.getModel().setValue(edgeCell, label);
            }
        } finally {
            graphAdapter.getModel().endUpdate();
        }

        // Add the graph component to the frame
        frame.setTitle("Graph Visualization with Edge Labels");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        SwingUtilities.invokeLater(() -> frame.setVisible(true));
    }








    private static String toHexString(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }
}
*/
