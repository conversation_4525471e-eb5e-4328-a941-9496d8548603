package com.youibot.vehicle.scheduler.modules.map.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.youibot.vehicle.scheduler.BaseTest;
import com.youibot.vehicle.scheduler.modules.map.dto.MarkerInfo;
import com.youibot.vehicle.scheduler.modules.map.dto.PathInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.MapGraphInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.sim.dto.Point;
import com.youibot.vehicle.scheduler.modules.sim.helper.BezierUtil;
import com.youibot.vehicle.scheduler.modules.sim.helper.PathUtils;
import com.youibot.vehicle.scheduler.modules.sim.helper.SidePathUtils;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.LinkedBlockingDeque;

import static com.youibot.vehicle.scheduler.modules.map.constant.MapConstant.PATH_LINE_TYPE_CURVE;
import static com.youibot.vehicle.scheduler.modules.map.constant.MapConstant.PATH_LINE_TYPE_STRAIGHT;

public class MapGraphTest  extends BaseTest {


    private static KDTreeManager kdTreeManager = KDTreeManager.getInstance();


    @Test
    public void test1(){

        String mapCode  ="test0807";
        int n = 30;
        Set<String> nodesToExclude = Sets.newHashSet();
        nodesToExclude.add( "test0807_P_36" );
        nodesToExclude.add( "test0807_P_34" );
        nodesToExclude.add( "test0807_P_34000" );
        Set<Marker> markers = MapGraph.selectNodesForPartitioning(mapCode, n, nodesToExclude);
        System.out.println(" size: " + markers.size() + " markers = " + JSON.toJSONString( markers ) );
       markers.forEach(  m -> System.out.println( m.getCode() + " " )  );
    }
    String mapCode  ="10.0.60.15:16646_FinalMap0611";
    String locatingCode= "AT_v2";
    String pathCode  ="FinalMap0611_L_1898_3786";
    Path path = null;
    List<Path> sidePaths = Lists.newArrayList();
    @Before
    public void setUp() throws Exception {
        Set<String> strings = MapGraph.GRAPHS.keySet();
        System.out.println(strings);

        MapGraphInfo mapGraphInfo = MapGraph.markerCodeToMapGraphInfo.get(mapCode);
        kdTreeManager.buildKDTree(mapCode, mapGraphInfo.getMarkers());
        sidePaths.add(MapGraph.getPath( pathCode)) ;
        path = MapGraph.getPath( pathCode);
    }

    @Test
    public  void testkdTree(){


        Double x= 94.86d;
        Double y = 65.12d;

        KDTree kdTree = kdTreeManager.getKDTree(mapCode, locatingCode);
        String markerCode = this.queryRecent(kdTree, x, y, locatingCode);
        System.out.println(markerCode);
    }


    private LinkedBlockingDeque<Point> pointQueue = new LinkedBlockingDeque<>();

    /**
     * 会产生点不再曲线上的情况
     */
    @Test
    public  void getSplitPathToPoint(){


        int t_num = 100;
        double resolution = 1.0 / t_num;
        pointQueue.clear();

        for (Path sidePath : sidePaths) {
            double t = sidePath.getT0();
            while (t <= 1.0) {
                Point point = PathUtils.getSidePathPoint(sidePath, t, locatingCode );
                if (point != null) {
                    point.setPathId( sidePath.getCode() );
                    pointQueue.add(point);
                }
                t += resolution;
            }
        }

        List<Point> points = new ArrayList<>();
        points.addAll(pointQueue);
        System.out.println(points.size());
        for (int i = 0; i < points.size(); i++) {
            Point point = points.get(i);
            boolean isPtInPoly = BezierUtil.isPtInPoly(path.getControlPoints(locatingCode).toArray(new Point[4]), point, this.getTrackRadius());
            if (!isPtInPoly) {
                System.out.println("idex: " + i + ", " + point.getX() + " " + point.getY() + ": " + isPtInPoly);
            }
        }



    }


    @Test
    public  void getSplitPathToPoint2(){

        Point[] points = BezierUtil.getPoints(path.getControlPoints(locatingCode).toArray(new Point[4]), 0, 1, 100);
        System.out.println(points.length);
        for (int i = 0; i < points.length; i++) {
            Point point = points[i];
            boolean isPtInPoly = BezierUtil.isPtInPoly(path.getControlPoints(locatingCode).toArray(new Point[4]), point, this.getTrackRadius());
            if (!isPtInPoly) {
                System.out.println("idex: " + i + ", " + point.getX() + " " + point.getY() + ": " + isPtInPoly);
            }
        }
        Point point = PathUtils.getSidePathPoint(path, 1D, locatingCode);
        boolean isPtInPoly = BezierUtil.isPtInPoly(path.getControlPoints(locatingCode).toArray(new Point[4]), point, this.getTrackRadius());
        System.out.println("isPtInPoly = " + isPtInPoly + ",point" + point);
    }

    @Test
    public  void testSplitPathToPoint3(){

        MapGraph.pathIdsToPaths.keySet().forEach(pathId -> {
            Path path = MapGraph.getPath(pathId);
            Point[] points = SidePathUtils.getPathControlPoints(path, locatingCode);
            Point point = BezierUtil.getPoint(points, 0.9999999999);
            boolean isPtInPoly = BezierUtil.isPtInPoly( points , point, this.getTrackRadius());
            if(!isPtInPoly) {
            System.out.println(pathId + " " + point.getX() + " " + point.getY() + " " + isPtInPoly);
            }

        });


    }

    @Test
    public  void testSplitPathToPoint4(){

        MapGraph.pathIdsToPaths.keySet().forEach(pathId -> {
            Path path = MapGraph.getPath(pathId);
            Point[] points = SidePathUtils.getPathControlPoints(path, locatingCode);
            Point point = PathUtils.getSidePathPoint(path, 1d, locatingCode);
            boolean isPtInPoly = BezierUtil.isPtInPoly( points , point, this.getTrackRadius());
            if(!isPtInPoly) {
                System.out.println(pathId + " " + point.getX() + " " + point.getY() + " " + isPtInPoly);
            }

        });


    }



    public String queryRecent(KDTree kdTree, Double x, Double y, String locatingCode) {
        String markerCode = kdTreeManager.query(kdTree, x, y);
        double radius = Optional.ofNullable(markerCode)
                .map(MapGraph::getMarker)
                .map(marker -> marker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null))
                .map(markerInfo -> new Point(markerInfo.getX() - x, markerInfo.getY() - y).getNorm())
                .orElse(Double.MAX_VALUE);
        if (radius < 0.2) {
            return markerCode;
        }
        return null;
    }


    public double setCurrentSidePathT0(String mapCode, Double x, Double y, List<Path> paths, String locatingCode) {
        try {
            Point point = new Point(x, y);
            List<Path> tempSidePath = new ArrayList<>(paths);
            for (Path path : tempSidePath) {
                if (!path.getVehicleMapCode().equals(mapCode)) {
                    continue;
                }
                Point[] points = new Point[4];
                Marker startMarker = MapGraph.getMarker(path.getStartMarkerCode());
                Marker endMarker = MapGraph.getMarker(path.getEndMarkerCode());
                MarkerInfo startMarkerInfo = startMarker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                MarkerInfo endMarkerInfo = endMarker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                if(!startMarker.getVehicleMapCode().equals(endMarker.getVehicleMapCode())){
                    //说明当前路径是电梯虚拟路径
                    continue;
                }
                points[0] = new Point(startMarkerInfo.getX(), startMarkerInfo.getY());
                points[3] = new Point(endMarkerInfo.getX(), endMarkerInfo.getY());
                boolean isPtInPoly = false;
                //用side path的t0存储agv当前的位置
                double t0 = -1;
                if (path.getLineType() == null) {
                    //虚拟的路径，跨地图时生成的虚拟路径
                    if (point.minus(points[0]).getNorm() < this.getTrackRadius()) {
                        path.setT0(0D);
                    }
                    if (point.minus(points[3]).getNorm() < this.getTrackRadius()) {
                        path.setT0(1D);
                    }
                    continue;
                }

                PathInfo pathInfo = path.getPathInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                points[1] = JSON.parseObject(pathInfo.getStartControl(), Point.class);
                points[2] = JSON.parseObject(pathInfo.getEndControl(), Point.class);
                isPtInPoly = BezierUtil.isPtInPoly(points, point, getTrackRadius());
                if (isPtInPoly) {
                    t0 = BezierUtil.calPosition(points, point, getTrackRadius());
                }
                if (isPtInPoly) {
                    path.setT0(t0);
                    return t0;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -2D;
    }

    private Double getTrackRadius() {
        return 0.2;
    }

}