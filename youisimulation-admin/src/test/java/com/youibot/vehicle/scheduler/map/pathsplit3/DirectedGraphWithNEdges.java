package com.youibot.vehicle.scheduler.map.pathsplit3;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.*;
import java.util.ArrayList;
import java.util.List;

public class DirectedGraphWithNEdges extends JPanel {

    private List<GeneralPath> edges;
    private List<Point2D.Double> vertices;

    public DirectedGraphWithNEdges(List<GeneralPath> edges, List<Point2D.Double> vertices) {
        this.edges = edges;
        this.vertices = vertices;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // Draw edges
        g2d.setColor(Color.BLUE);
        for (GeneralPath path : edges) {
            g2d.draw(path);
        }

        // Draw vertices
        g2d.setColor(Color.RED);
        for (Point2D.Double vertex : vertices) {
            g2d.fill(new Ellipse2D.Double(vertex.getX() - 5, vertex.getY() - 5, 10, 10));
        }
    }

    public static void main(String[] args) {
        int n = 12; // Number of edges
        List<GeneralPath> edges = new ArrayList<>();
        List<Point2D.Double> vertices = new ArrayList<>();

        // Define a layout for vertices
        double radius = 200;
        double centerX = 300;
        double centerY = 300;
        double angleStep = 2 * Math.PI / n;

        // Create vertices in a circular pattern
        for (int i = 0; i < n; i++) {
            double angle = i * angleStep;
            double x = centerX + radius * Math.cos(angle);
            double y = centerY + radius * Math.sin(angle);
            vertices.add(new Point2D.Double(x, y));
        }

        // Create edges with alternating Bezier curves and lines
        for (int i = 0; i < n; i++) {
            GeneralPath path = new GeneralPath();
            Point2D.Double start = vertices.get(i);
            Point2D.Double end = vertices.get((i + 1) % n);

            if (i % 2 == 0) {
                // Create a Bezier curve
                Point2D.Double control1 = new Point2D.Double(start.getX() + 50, start.getY() - 50);
                Point2D.Double control2 = new Point2D.Double(end.getX() - 50, end.getY() + 50);
                path.moveTo(start.getX(), start.getY());
                path.curveTo(control1.getX(), control1.getY(), control2.getX(), control2.getY(), end.getX(), end.getY());
            } else {
                // Create a line
                path.moveTo(start.getX(), start.getY());
                path.lineTo(end.getX(), end.getY());
            }

            edges.add(path);
        }

        // Create and show the JFrame
        JFrame frame = new JFrame("Directed Graph with N Edges");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.add(new DirectedGraphWithNEdges(edges, vertices));
        frame.setVisible(true);
    }
}
