package com.youibot.vehicle.scheduler.modules.map.dao;

import com.alibaba.fastjson.JSON;
import com.youibot.vehicle.scheduler.BaseTest;
import com.youibot.vehicle.scheduler.modules.map.entity.VehicleMapMd5;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class VehicleMapMd5DaoTest extends BaseTest {

    @Autowired
    private  VehicleMapMd5Dao vehicleMapMd5Dao;

    @Test
    public  void testSelectByCode(){
        String confName = "00ekgxnz1j";
        List<VehicleMapMd5> vehicleMapMd5s = vehicleMapMd5Dao.selectByConfName(confName);
        System.out.println("vehicleMapMd5s = "  + JSON.toJSONString( vehicleMapMd5s ));
    }

}