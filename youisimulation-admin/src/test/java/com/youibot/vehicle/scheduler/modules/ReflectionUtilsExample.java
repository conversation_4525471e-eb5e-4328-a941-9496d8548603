package com.youibot.vehicle.scheduler.modules;

import com.youibot.vehicle.scheduler.common.utils.CommonUtils;
import com.youibot.vehicle.scheduler.modules.client.entity.VehicleSocketMessage;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import org.springframework.util.ReflectionUtils;

public class ReflectionUtilsExample {

    public final static   String Signature ="(com.youibot.vehicle.scheduler.modules.vehicle.Vehicle, com.youibot.vehicle.scheduler.modules.client.entity.VehicleSocketMessage)";
    public static void main(String[] args) {
        // Example class
        Class<?> clazz = ExampleClass.class;

        // Process all methods of the ExampleClass
        ReflectionUtils.doWithMethods(clazz, method -> {
            // Print method name and parameter types
            if(CommonUtils.isDeclaredInClass(method, clazz)){
                String signature = CommonUtils.getMethodParameterSignature(method);
                System.out.println("Method: " + method.getName() + ", Signature: " + signature);
            }

        });
    }



}

class ExampleClass {
    public void exampleMethod(String param1, Integer param2) { }
    public void anotherMethod(int param1, boolean param2, Object param3) { }

    public  void appointMap(Vehicle vehicle , VehicleSocketMessage message){



    }
}
