package com.youibot.vehicle.scheduler.map.pathsplit3;

import com.youibot.vehicle.scheduler.map.pathsplit2.PathUtils;
import com.youibot.vehicle.scheduler.modules.sim.dto.Point;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.Ellipse2D;
import java.awt.geom.GeneralPath;
import java.awt.geom.Line2D;
import java.awt.geom.Point2D;
import java.util.List;
public class PathVisualization3 extends JPanel {

    private List<Point> bezierPoints;
    private List<Point> linePoints;
    private Point p0, p1, p2, p3;
    private Point l0, l1;

    public PathVisualization3(List<Point> bezierPoints, List<Point> linePoints,
                              Point p0, Point p1, Point p2, Point p3,
                              Point l0, Point l1) {
        this.bezierPoints = bezierPoints;
        this.linePoints = linePoints;
        this.p0 = p0;
        this.p1 = p1;
        this.p2 = p2;
        this.p3 = p3;
        this.l0 = l0;
        this.l1 = l1;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // Draw Original Bezier Curve
        g2d.setColor(Color.BLUE);
        GeneralPath bezierPath = new GeneralPath();
        bezierPath.moveTo(p0.getX(), p0.getY());
        bezierPath.curveTo(p1.getX(), p1.getY(), p2.getX(), p2.getY(), p3.getX(), p3.getY());
        g2d.draw(bezierPath);

        // Draw Original Line
        g2d.setColor(Color.RED);
        g2d.draw(new Line2D.Double(new Point2D.Double(l0.getX(), l0.getY()), new Point2D.Double(l1.getX(), l1.getY())));

        // Draw Bezier Curve Points
        g2d.setColor(Color.GREEN);
        for (Point point : bezierPoints) {
            g2d.fill(new Ellipse2D.Double(point.getX() - 2, point.getY() - 2, 4, 4));
        }

        // Draw Line Points
        g2d.setColor(Color.MAGENTA);
        for (Point point : linePoints) {
            g2d.fill(new Ellipse2D.Double(point.getX() - 2, point.getY() - 2, 4, 4));
        }
    }

    public static void main(String[] args) {
        // Define some test points for the Bezier curve and line
        Point p0 = new Point(50.0, 300.0);
        Point p1 = new Point(150.0, 50.0);
        Point p2 = new Point(250.0, 50.0);
        Point p3 = new Point(350.0, 300.0);
        List<Point> bezierPoints = PathUtils.splitBezierCurve(p0, p1, p2, p3, 4 );

        Point l0 = new Point(350.0, 300.0);
        Point l1 = new Point(500.0, 100.0);
        List<Point> linePoints = PathUtils.splitLine(l0, l1, 5);

        // Create and show the JFrame
        JFrame frame = new JFrame("Path Visualization");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(600, 400);
        frame.add(new PathVisualization3(bezierPoints, linePoints, p0, p1, p2, p3, l0, l1));
        frame.setVisible(true);
    }
}
