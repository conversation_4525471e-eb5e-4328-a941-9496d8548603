/*
package com.youibot.vehicle.scheduler.map.dis5;

import com.mxgraph.layout.mxCircleLayout;
import com.mxgraph.swing.mxGraphComponent;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import org.jgrapht.Graph;
import org.jgrapht.alg.shortestpath.AllDirectedPaths;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

*/
/**
 * 面板对齐
 * 展示前5条记录
 *//*

public class GraphExample {

    public static void main(String[] args) {
        // 创建一个有向带权伪图
        Graph<Marker, DefaultWeightedEdge> graph = new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        // 创建Marker实例作为顶点
        Marker a = new Marker("A", "Start", 0.0, "ChargingMarker", "Map1", 1, 1, 0, "ReflectorPoint", 1, null);
        Marker b = new Marker("B", "Intermediate1", 0.0, "WorkMarker", "Map1", 1, 1, 1, "Vpoint", 2 ,null);
        Marker c = new Marker("C", "Intermediate2", 0.0, "NavigationMarker", "Map1", 1, 1, 1, "ReflectorPoint", 1 ,null);
        Marker d = new Marker("D", "End", 0.0, "ChargingMarker", "Map1", 1, 1, 0, "Vpoint", 2 ,null);
        Marker e = new Marker("E", "Intermediate3", 0.0, "NavigationMarker", "Map1", 1, 1, 1, "ReflectorPoint", 1,null);

        // 添加顶点到图中
        graph.addVertex(a);
        graph.addVertex(b);
        graph.addVertex(c);
        graph.addVertex(d);
        graph.addVertex(e);

        // 添加带权边到图中
        addEdgeWithWeight(graph, a, b, 2.0);
        addEdgeWithWeight(graph, a, c, 1.0);
        addEdgeWithWeight(graph, b, d, 1.0);
        addEdgeWithWeight(graph, c, d, 2.0);
        addEdgeWithWeight(graph, b, e, 3.0);
        addEdgeWithWeight(graph, e, d, 1.0);
        addEdgeWithWeight(graph, a, d, 4.0); // 直接从起点到终点

        // 计算起点到终点的所有路径
        List<List<DefaultWeightedEdge>> allPaths = findAllPaths(graph, a, d);

        // 打印并可视化路径
        printAndVisualizePaths(graph, allPaths);
    }

    // 添加带权边
    private static void addEdgeWithWeight(Graph<Marker, DefaultWeightedEdge> graph, Marker source, Marker target, double weight) {
        DefaultWeightedEdge edge = graph.addEdge(source, target);
        graph.setEdgeWeight(edge, weight);
    }

    // 寻找所有路径（通过所有路径算法）
    private static List<List<DefaultWeightedEdge>> findAllPaths(Graph<Marker, DefaultWeightedEdge> graph, Marker source, Marker target) {
        List<List<DefaultWeightedEdge>> allPaths = new ArrayList<>();
        
        // 使用 AllDirectedPaths 找到所有路径
        AllDirectedPaths<Marker, DefaultWeightedEdge> allPathsAlg = new AllDirectedPaths<>(graph);
        Collection<org.jgrapht.GraphPath<Marker, DefaultWeightedEdge>> paths = allPathsAlg.getAllPaths(source, target, true, null);
        
        for (org.jgrapht.GraphPath<Marker, DefaultWeightedEdge> path : paths) {
            allPaths.add(path.getEdgeList());
        }
        
        // 根据路径总权重排序
        allPaths.sort(Comparator.comparingDouble(path -> 
            path.stream().mapToDouble(edge -> graph.getEdgeWeight(edge)).sum()
        ));
        
        return allPaths;
    }

    // 打印和可视化路径
    private static void printAndVisualizePaths(Graph<Marker, DefaultWeightedEdge> graph, List<List<DefaultWeightedEdge>> paths) {
        JFrame frame = new JFrame("Graph Visualization");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);

        JPanel panel = new JPanel();
        panel.setLayout(new BorderLayout());

        // 使用 JGraphX 适配器
        JGraphXAdapter<Marker, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // 使用圆形布局
        mxCircleLayout layout = new mxCircleLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());

        // 添加到面板
        mxGraphComponent graphComponent = new mxGraphComponent(graphAdapter);
        panel.add(graphComponent, BorderLayout.CENTER);

        // 路径面板
        JPanel pathPanel = new JPanel();
        pathPanel.setLayout(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // 添加路径标题
        gbc.gridwidth = GridBagConstraints.REMAINDER;
        pathPanel.add(new JLabel("Paths from source to target:"), gbc);

        // 添加路径内容
        int index = 1;
        for (List<DefaultWeightedEdge> path : paths) {
            double pathWeight = 0;
            StringBuilder pathString = new StringBuilder();
            for (DefaultWeightedEdge edge : path) {
                pathWeight += graph.getEdgeWeight(edge);
                pathString.append(graph.getEdgeSource(edge).getName())
                        .append(" -> ")
                        .append(graph.getEdgeTarget(edge).getName())
                        .append(" (")
                        .append(graph.getEdgeWeight(edge))
                        .append("), ");
            }
            pathString.append(" Total weight: ").append(pathWeight);

            // 添加路径信息到面板
            gbc.gridwidth = GridBagConstraints.RELATIVE;
            pathPanel.add(new JLabel("Path " + index + ":"), gbc);
            gbc.gridwidth = GridBagConstraints.REMAINDER;
            pathPanel.add(new JLabel(pathString.toString()), gbc);
            index++;
        }

        // 添加到 JFrame
        panel.add(pathPanel, BorderLayout.SOUTH);
        frame.add(panel);
        frame.setVisible(true);
    }
}
*/
