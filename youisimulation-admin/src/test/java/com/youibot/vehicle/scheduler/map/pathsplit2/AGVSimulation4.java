package com.youibot.vehicle.scheduler.map.pathsplit2;

import com.youibot.vehicle.scheduler.modules.sim.dto.Point;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.geom.GeneralPath;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 肉眼可见的移动速度添加
 * 但是界面不能控制agv 速度  有控制 但是速度设置不生效
 */
public class AGVSimulation4 extends JPanel {
    private static final int WIDTH = 800;
    private static final int HEIGHT = 600;

    private List<Point> pathPoints;
    private Map<Integer, Double> segmentSpeeds;
    private int currentIndex = 0;
    private double t = 0;
    private Timer timer; // Timer to handle animation

    public AGVSimulation4(List<Point> pathPoints, Map<Integer, Double> segmentSpeeds) {
        this.pathPoints = pathPoints;
        this.segmentSpeeds = segmentSpeeds;
        setLayout(new BorderLayout());

        JButton restartButton = new JButton("Restart");
        restartButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                restartSimulation();
            }
        });

        // Control Panel for speed adjustments
        JPanel controlPanel = new JPanel();
        controlPanel.setLayout(new GridLayout(segmentSpeeds.size(), 2));
        for (int i = 0; i < segmentSpeeds.size(); i++) {
            final int segmentIndex = i;
            JLabel label = new JLabel("Speed for Segment " + i + ":");
            JTextField speedField = new JTextField(String.valueOf(segmentSpeeds.getOrDefault(i, 1.0)));
            speedField.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    double speed = Double.parseDouble(speedField.getText());
                    segmentSpeeds.put(segmentIndex, speed);
                }
            });
            controlPanel.add(label);
            controlPanel.add(speedField);
        }

        add(controlPanel, BorderLayout.NORTH);
        add(restartButton, BorderLayout.SOUTH);

        // Timer to update AGV position
        timer = new Timer(20, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                updateAGVPosition();
                repaint();
            }
        });
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        drawPath(g2d);
        drawAGV(g2d);
    }

    private void drawPath(Graphics2D g2d) {
        g2d.setColor(Color.BLACK);
        GeneralPath path = new GeneralPath();
        if (!pathPoints.isEmpty()) {
            Point firstPoint = pathPoints.get(0);
            path.moveTo(firstPoint.getX(), firstPoint.getY());
            for (int i = 1; i < pathPoints.size(); i++) {
                Point point = pathPoints.get(i);
                path.lineTo(point.getX(), point.getY());
            }
            g2d.draw(path);
        }
    }

    private void drawAGV(Graphics2D g2d) {
        if (currentIndex < pathPoints.size()) {
            Point agvPosition = pathPoints.get(currentIndex);
            g2d.setColor(Color.RED);
            g2d.fillOval((int) agvPosition.getX().intValue() - 10, (int) agvPosition.getY().intValue() - 10, 20, 20);
        }
    }

    private void updateAGVPosition() {
        if (currentIndex < pathPoints.size()) {
            double speed = getCurrentSegmentSpeed();
            t += speed / 100.0; // Adjust step size based on speed
            if (t >= 1.0) {
                t = 0.0;
                currentIndex++;
                if (currentIndex >= pathPoints.size() - 1) {
                    currentIndex = pathPoints.size() - 1;
                    timer.stop(); // Stop timer when reaching the end
                }
            }
        }
    }

    private double getCurrentSegmentSpeed() {
        int segmentIndex = findSegmentIndex();
        return segmentSpeeds.getOrDefault(segmentIndex, 1.0); // Default speed if not found
    }

    private int findSegmentIndex() {
        for (int i = 0; i < pathPoints.size() - 1; i++) {
            if (currentIndex <= i) {
                return i;
            }
        }
        return pathPoints.size() - 2;
    }

    private void restartSimulation() {
        currentIndex = 0;
        t = 0;
        timer.start(); // Restart timer to animate AGV
    }

    public static void main(String[] args) {
        // Example path points (Bezier and Line)
        List<Point> bezierPoints = PathUtils.splitBezierCurve(
            new Point(100, 500),
            new Point(200, 100),
            new Point(600, 100),
            new Point(700, 500),
            100
        );
        List<Point> linePoints = PathUtils.splitLine(
            new Point(700, 500),
            new Point(800, 200),
            50
        );

        bezierPoints.addAll(linePoints);

        // Example speeds for each segment
        Map<Integer, Double> speeds = new HashMap<>();
        speeds.put(0, 0.5); // Speed for Bezier segment
        speeds.put(1, 1.0); // Speed for Line segment

        JFrame frame = new JFrame("AGV Simulation");
        AGVSimulation4 panel = new AGVSimulation4(bezierPoints, speeds);
        frame.add(panel);
        frame.setSize(WIDTH, HEIGHT);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}
