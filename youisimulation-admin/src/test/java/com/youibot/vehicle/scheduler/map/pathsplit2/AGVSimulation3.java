package com.youibot.vehicle.scheduler.map.pathsplit2;

import com.youibot.vehicle.scheduler.modules.sim.dto.Point;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.GeneralPath;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 肉眼可见的移动速度添加
 * 但是界面不能控制agv 速度
 */
public class AGVSimulation3 extends JPanel {
    private static final int WIDTH = 800;
    private static final int HEIGHT = 600;

    private List<Point> pathPoints;
    private Map<Integer, Double> segmentSpeeds; // Segment index to speed mapping
    private int currentIndex = 0;
    private double t = 0; // Normalized parameter for current segment

    public AGVSimulation3(List<Point> pathPoints, Map<Integer, Double> segmentSpeeds) {
        this.pathPoints = pathPoints;
        this.segmentSpeeds = segmentSpeeds;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        drawPath(g2d);
        drawAGV(g2d);
        updateAGVPosition();
    }

    private void drawPath(Graphics2D g2d) {
        g2d.setColor(Color.BLACK);
        GeneralPath path = new GeneralPath();
        if (!pathPoints.isEmpty()) {
            Point firstPoint = pathPoints.get(0);
            path.moveTo(firstPoint.getX(), firstPoint.getY());
            for (int i = 1; i < pathPoints.size(); i++) {
                Point point = pathPoints.get(i);
                path.lineTo(point.getX(), point.getY());
            }
            g2d.draw(path);
        }
    }

    private void drawAGV(Graphics2D g2d) {
        if (currentIndex < pathPoints.size()) {
            Point agvPosition = pathPoints.get(currentIndex);
            g2d.setColor(Color.RED);
            g2d.fillOval((int) agvPosition.getX().intValue() - 10, (int) agvPosition.getY().intValue() - 10, 20, 20);
        }
    }

    private void updateAGVPosition() {
        if (currentIndex < pathPoints.size()) {
            double speed = getCurrentSegmentSpeed();
            t += speed / 100.0; // Adjust step size based on speed
            if (t >= 1.0) {
                t = 0.0;
                currentIndex++;
                if (currentIndex >= pathPoints.size() - 1) {
                    currentIndex = pathPoints.size() - 1; // Ensure we don't go out of bounds
                }
            }
        }
        repaint();
    }

    private double getCurrentSegmentSpeed() {
        int segmentIndex = findSegmentIndex();
        return segmentSpeeds.getOrDefault(segmentIndex, 1.0); // Default speed if not found
    }

    private int findSegmentIndex() {
        // Simple linear search; adjust for more efficient index finding
        for (int i = 0; i < pathPoints.size() - 1; i++) {
            if (currentIndex <= i) {
                return i;
            }
        }
        return pathPoints.size() - 2; // Last segment
    }

    public static void main(String[] args) {
        // Example path points (Bezier and Line)
        List<Point> bezierPoints = PathUtils.splitBezierCurve(
            new Point(100, 500),
            new Point(200, 100),
            new Point(600, 100),
            new Point(700, 500),
            100
        );
        List<Point> linePoints = PathUtils.splitLine(
            new Point(700, 500),
            new Point(800, 200),
            50
        );

        bezierPoints.addAll(linePoints);

        // Example speeds for each segment
        Map<Integer, Double> speeds = new HashMap<>();
        speeds.put(0, 0.5); // Speed for Bezier segment
        speeds.put(1, 1.0); // Speed for Line segment

        JFrame frame = new JFrame("AGV Simulation");
        AGVSimulation3 panel = new AGVSimulation3(bezierPoints, speeds);
        frame.add(panel);
        frame.setSize(WIDTH, HEIGHT);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}
