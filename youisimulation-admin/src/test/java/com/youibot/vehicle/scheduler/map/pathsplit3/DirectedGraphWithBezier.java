package com.youibot.vehicle.scheduler.map.pathsplit3;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.*;
import java.util.ArrayList;
import java.util.List;

public class DirectedGraphWithBezier extends JPanel {

    private List<GeneralPath> edges;
    private List<Point2D.Double> vertices;

    public DirectedGraphWithBezier(List<GeneralPath> edges, List<Point2D.Double> vertices) {
        this.edges = edges;
        this.vertices = vertices;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // Draw edges
        g2d.setColor(Color.BLUE);
        for (GeneralPath path : edges) {
            g2d.draw(path);
        }

        // Draw vertices
        g2d.setColor(Color.RED);
        for (Point2D.Double vertex : vertices) {
            g2d.fill(new Ellipse2D.Double(vertex.getX() - 5, vertex.getY() - 5, 10, 10));
        }
    }

    public static void main(String[] args) {
        int n = 12; // Number of edges
        List<GeneralPath> edges = new ArrayList<>();
        List<Point2D.Double> vertices = new ArrayList<>();

        // Define vertices in a grid-like pattern
        double[][] vertexPositions = {
            {100, 100}, {200, 100}, {300, 100}, {400, 100},
            {100, 200}, {200, 200}, {300, 200}, {400, 200},
            {100, 300}, {200, 300}, {300, 300}, {400, 300}
        };

        // Create vertices
        for (double[] pos : vertexPositions) {
            vertices.add(new Point2D.Double(pos[0], pos[1]));
        }

        // Create edges with alternating Bezier curves and lines
        for (int i = 0; i < vertexPositions.length - 1; i++) {
            GeneralPath path = new GeneralPath();
            Point2D.Double start = vertices.get(i);
            Point2D.Double end = vertices.get((i + 1) % vertexPositions.length);

            if (i % 2 == 0) {
                // Create a Bezier curve
                Point2D.Double control1 = new Point2D.Double(start.getX() + 50, start.getY() - 50);
                Point2D.Double control2 = new Point2D.Double(end.getX() - 50, end.getY() + 50);
                path.moveTo(start.getX(), start.getY());
                path.curveTo(control1.getX(), control1.getY(), control2.getX(), control2.getY(), end.getX(), end.getY());
            } else {
                // Create a line
                path.moveTo(start.getX(), start.getY());
                path.lineTo(end.getX(), end.getY());
            }

            edges.add(path);
        }

        // Create and show the JFrame
        JFrame frame = new JFrame("Directed Graph with Bezier Curves");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.add(new DirectedGraphWithBezier(edges, vertices));
        frame.setVisible(true);
    }
}
