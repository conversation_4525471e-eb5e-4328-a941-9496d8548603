package com.youibot.vehicle.scheduler.modules.netty.tcp;

import com.youibot.vehicle.scheduler.AdminApplication;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = AdminApplication.class)
@SpringBootTest
public class NettyTcpClientTest {

    /*@Autowired
    private  NettyTcpClient nettyTcpClient;

    @Test
    public  void test1() throws InterruptedException {
        while (true) {
            nettyTcpClient.sendMessage("10.0.60.10", 16646, "123");
            Thread.sleep(2000);
        }

    }*/

}