package com.youibot.vehicle.scheduler.modules.map.utils;

import cn.hutool.core.util.StrUtil;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import com.youibot.vehicle.scheduler.modules.map.dto.MarkerInfo ;

/**
 * <AUTHOR>
 * @Date :Created in 11:13 2020/10/28
 * @Description :
 * @Modified By :
 * @Version :
 */
public class KDTreeManager {
    private static final Logger logger = LoggerFactory.getLogger(KDTreeManager.class);

    /**
     *  Map<mapCode,Map<locatingCode, KDTree>>
     */
    private Map<String,Map<String, KDTree>> mapCodeToKDTree = new ConcurrentHashMap<>();

    //private final RateLimiter rateLimiter = RateLimiter.create(100.0);

    private KDTreeManager() {
    }

    private static class KDTreeManagerHolder {
        //静态初始化器，由JVM来保证线程安全
        private static KDTreeManager instance = new KDTreeManager();
    }

    public static KDTreeManager getInstance() {
        return KDTreeManagerHolder.instance;
    }

    public void buildKDTree(String mapCode, Collection<Marker> markers) {
        Marker first = new ArrayList<>(markers).get(0);
        List<String> locatingCodeList = first.getMarkInfos().stream().map(MarkerInfo::getLocatingCode).collect(Collectors.toList());

        Map<String, KDTree> locationTreeMap = new ConcurrentHashMap<>();
        for(String locatingCode : locatingCodeList) {
            int size = markers.size();
            KDTree.Data[] data = new KDTree.Data[size];
            int i = 0;
            for (Marker marker : markers) {
                KDTree.Data nodeData = new KDTree.Data();
                nodeData.code = marker.getCode();
                nodeData.point = new double[2];
                MarkerInfo markerInfo = marker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                nodeData.point[0] = markerInfo.getX();
                nodeData.point[1] = markerInfo.getY();
                data[i++] = nodeData;
            }
            KDTree kdTree = KDTree.build(data);
            locationTreeMap.put(locatingCode,kdTree);
        }
        mapCodeToKDTree.put(mapCode, locationTreeMap);
    }

    public KDTree buildKDTree(Collection<Marker> markers, String locatingCode) {
        if (CollectionUtils.isEmpty(markers)) {
            return null;
        }
        int size = markers.size();
        KDTree.Data[] data = new KDTree.Data[size];
        int i = 0;
        for (Marker marker : markers) {
            KDTree.Data nodeData = new KDTree.Data();
            nodeData.code = marker.getCode();
            nodeData.point = new double[2];
            MarkerInfo markerInfo = marker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
            nodeData.point[0] = markerInfo.getX();
            nodeData.point[1] = markerInfo.getY();
            data[i++] = nodeData;
        }
        return KDTree.build(data);
    }

    public void removeKDTree(String mapCode) {
        if (mapCode == null) {
            return;
        }
        mapCodeToKDTree.remove(mapCode);
    }

    public KDTree getKDTree(String mapCode, String locatingCode) {
        if (StrUtil.isEmpty(mapCode) || StrUtil.isEmpty(locatingCode)) {
            return null;
        }
        Map<String, KDTree> locationTreeMap = mapCodeToKDTree.get(mapCode);
        return locationTreeMap != null ? locationTreeMap.get(locatingCode) : null;
    }

    public String query(String mapCode, Double x, Double y, String locatingCode) {
        if (mapCode == null || x == null || y == null) {
            return null;
        }
        //boolean acquire = rateLimiter.tryAcquire();
        Map<String, KDTree> locationTreeMap = mapCodeToKDTree.get(mapCode);
        KDTree kdTree = locationTreeMap!=null ? locationTreeMap.get(locatingCode) : null;
        //if (acquire && kdTree != null && !kdTree.isInit()) {
        if (kdTree != null && !kdTree.isInit()) {
            KDTree.Data queryData = new KDTree.Data();
            queryData.point = new double[2];
            queryData.point[0] = x;
            queryData.point[1] = y;
            KDTree.Data query = kdTree.query(queryData);
            return query.code;
        }
        return null;
    }

    public String query(KDTree kdTree, Double x, Double y) {
        if (kdTree == null || x == null || y == null) {
            return null;
        }
        if (!kdTree.isInit()) {
            KDTree.Data queryData = new KDTree.Data();
            queryData.point = new double[2];
            queryData.point[0] = x;
            queryData.point[1] = y;
            KDTree.Data query = kdTree.query(queryData);
            return query.code;
        }
        return null;
    }
}
