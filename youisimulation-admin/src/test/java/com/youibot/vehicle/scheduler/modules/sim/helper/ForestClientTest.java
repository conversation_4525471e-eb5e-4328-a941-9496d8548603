package com.youibot.vehicle.scheduler.modules.sim.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jayway.jsonpath.JsonPath;
import com.youibot.vehicle.scheduler.BaseTest;
import com.youibot.vehicle.scheduler.modules.map.entity.MapGraphInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.utils.MapGraph;
import com.youibot.vehicle.scheduler.modules.sim.dto.Point;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.List;

public class ForestClientTest extends BaseTest {

    @Autowired
    private  ForestClient forestClient;

    @Test
    public  void test1(){

        String host ="localhost" ;

        String vehicleMaps = forestClient.getVehicleMaps(host , 8080);
//        System.out.println("vehicleMaps = " + vehicleMaps);
        // 使用 JSONPath 获取所有 code 的集合
        List<String> codes = JsonPath.read(vehicleMaps, "$.data[*].code");
        System.out.println("codes = " + codes);
    }

    @Test
    public  void test2getRoadNet(){

        String host ="localhost" ;
       String code = "aaaaa" ;
        String vehicleMaps = forestClient.getVehicleMapsRoadNets(host , 8080, code);
        System.out.println("vehicleMaps = " + vehicleMaps);
        JSONObject object = JSON.parseObject(vehicleMaps);
        String data = object.getString("data");

        MapGraphInfo read = JSON.parseObject( data, MapGraphInfo.class);
        System.out.println("read = " + read);
    }

    @Test
    public  void test2getRoadNe3(){

        String host ="**********" ;
        String code = "aaaaa" ;
        String vehicleMaps = forestClient.getVehicleMapsRoadNets(host ,8080, code);
        System.out.println("vehicleMaps = " + vehicleMaps);
        JSONObject object = JSON.parseObject(vehicleMaps);
        String data = object.getString("data");

        MapGraphInfo read = JSON.parseObject( data, MapGraphInfo.class);
        Collection<Path> values = MapGraph.pathIdsToPaths.values();
        MapGraph.addVehicleMap( code , null , read );

        values.stream().forEach( i ->{
            System.out.println("i = " + i);
            String locationCode ="maptest071";
            List<Point> points = PathUtils.splitPath(i, 4 );
            System.out.println("i = " + i.getCode() +" points = " + points);
        });


    }

}