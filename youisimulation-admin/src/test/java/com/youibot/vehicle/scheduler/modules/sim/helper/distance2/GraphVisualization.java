package com.youibot.vehicle.scheduler.modules.sim.helper.distance2;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.*;
import java.util.*;
import java.util.List;

public class GraphVisualization extends JPanel {

    private final List<PathUtils.PathSegment> segments;
    private final Point2D.Double point;
    private PathUtils.SegmentPosition closestPosition;

    public GraphVisualization(List<PathUtils.PathSegment> segments, Point2D.Double point) {
        this.segments = segments;
        this.point = point;
        this.closestPosition = PathUtils.findSegmentPosition(segments, point.getX(), point.getY());
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;

        // Draw segments
        g2d.setColor(Color.BLACK);
        for (PathUtils.PathSegment segment : segments) {
            segment.draw(g2d);
        }

        // Draw the point
        g2d.setColor(Color.RED);
        g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));

        // Highlight the closest segment and position
        if (closestPosition != null) {
            g2d.setColor(Color.BLUE);
            PathUtils.PathSegment segment = closestPosition.getSegment();
            segment.highlight(g2d, closestPosition.getPercentage());

            // Draw information text
            g2d.setColor(Color.BLACK);
            String info = closestPosition.isOnPath() ? "On Path" : "Off Path";
            g2d.drawString(info, (float) point.getX() + 10, (float) point.getY() - 10);
        }
    }

    public static void main(String[] args) {
        // Define example segments and point
        List<PathUtils.PathSegment> segments = new ArrayList<>();
        Point2D.Double p0 = new Point2D.Double(50, 50);
        Point2D.Double p1 = new Point2D.Double(150, 50);
        Point2D.Double p2 = new Point2D.Double(200, 100);
        Point2D.Double p3 = new Point2D.Double(250, 0);
        Point2D.Double p4 = new Point2D.Double(300, 50);

        segments.add(new PathUtils.LineSegment(p0, p1));
        segments.add(new PathUtils.BezierSegment(p1, p2, p3, p4));
        segments.add(new PathUtils.LineSegment(p4, p0)); // Adding a loop to test multiple paths at vertices

        Point2D.Double point = new Point2D.Double(180, 60);

        // Create and show the GUI
        JFrame frame = new JFrame("Graph Visualization");
        GraphVisualization panel = new GraphVisualization(segments, point);
        frame.add(panel);
        frame.setSize(400, 300);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}

// Utility classes

class PathUtils {

    public static SegmentPosition findSegmentPosition(List<PathSegment> segments, double x, double y) {
        double minDistance = Double.MAX_VALUE;
        SegmentPosition closestPosition = null;

        for (PathSegment segment : segments) {
            SegmentPosition position = segment.getPosition(x, y);
            if (position != null) {
                double distance = segment.distanceTo(x, y);
                if (distance < minDistance) {
                    minDistance = distance;
                    closestPosition = position;
                }
            }
        }
        return closestPosition;
    }

    public interface PathSegment {
        double distanceTo(double x, double y);
        SegmentPosition getPosition(double x, double y);
        void draw(Graphics2D g2d);
        void highlight(Graphics2D g2d, double percentage);
    }

    public static class LineSegment implements PathSegment {
        private final Point2D.Double start;
        private final Point2D.Double end;

        public LineSegment(Point2D.Double start, Point2D.Double end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public double distanceTo(double x, double y) {
            double lengthSquared = start.distanceSq(end);
            if (lengthSquared == 0) return start.distance(x, y);
            double t = Math.max(0, Math.min(1, ((x - start.getX()) * (end.getX() - start.getX()) +
                                                (y - start.getY()) * (end.getY() - start.getY())) / lengthSquared));
            Point2D.Double projection = new Point2D.Double(start.getX() + t * (end.getX() - start.getX()),
                                                          start.getY() + t * (end.getY() - start.getY()));
            return projection.distance(x, y);
        }

        @Override
        public SegmentPosition getPosition(double x, double y) {
            double length = start.distance(end);
            double t = ((x - start.getX()) * (end.getX() - start.getX()) +
                        (y - start.getY()) * (end.getY() - start.getY())) / length;
            double percent = Math.max(0, Math.min(1, t / length));
            return new SegmentPosition(this, percent, true);
        }

        @Override
        public void draw(Graphics2D g2d) {
            g2d.draw(new Line2D.Double(start, end));
        }

        @Override
        public void highlight(Graphics2D g2d, double percentage) {
            Point2D.Double point = new Point2D.Double(start.getX() + percentage * (end.getX() - start.getX()),
                                                      start.getY() + percentage * (end.getY() - start.getY()));
            g2d.setColor(Color.GREEN);
            g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));
        }
    }

    public static class BezierSegment implements PathSegment {
        private final Point2D.Double p0, p1, p2, p3;

        public BezierSegment(Point2D.Double p0, Point2D.Double p1, Point2D.Double p2, Point2D.Double p3) {
            this.p0 = p0;
            this.p1 = p1;
            this.p2 = p2;
            this.p3 = p3;
        }

        @Override
        public double distanceTo(double x, double y) {
            double minDistance = Double.MAX_VALUE;
            int numSamples = 100;
            for (int i = 0; i <= numSamples; i++) {
                double t = i / (double) numSamples;
                Point2D.Double pointOnCurve = getBezierPoint(t);
                double distance = pointOnCurve.distance(x, y);
                minDistance = Math.min(minDistance, distance);
            }
            return minDistance;
        }

        @Override
        public SegmentPosition getPosition(double x, double y) {
            double minDistance = Double.MAX_VALUE;
            double bestT = 0;
            int numSamples = 100;
            for (int i = 0; i <= numSamples; i++) {
                double t = i / (double) numSamples;
                Point2D.Double pointOnCurve = getBezierPoint(t);
                double distance = pointOnCurve.distance(x, y);
                if (distance < minDistance) {
                    minDistance = distance;
                    bestT = t;
                }
            }
            return new SegmentPosition(this, bestT, minDistance < 10); // Using 10 as a threshold for being "on path"
        }

        @Override
        public void draw(Graphics2D g2d) {
            GeneralPath path = new GeneralPath();
            path.moveTo(p0.getX(), p0.getY());
            path.curveTo(p1.getX(), p1.getY(), p2.getX(), p2.getY(), p3.getX(), p3.getY());
            g2d.draw(path);
        }

        @Override
        public void highlight(Graphics2D g2d, double percentage) {
            Point2D.Double point = getBezierPoint(percentage);
            g2d.setColor(Color.GREEN);
            g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));
        }

        private Point2D.Double getBezierPoint(double t) {
            double x = Math.pow(1 - t, 3) * p0.getX() +
                       3 * Math.pow(1 - t, 2) * t * p1.getX() +
                       3 * (1 - t) * Math.pow(t, 2) * p2.getX() +
                       Math.pow(t, 3) * p3.getX();
            double y = Math.pow(1 - t, 3) * p0.getY() +
                       3 * Math.pow(1 - t, 2) * t * p1.getY() +
                       3 * (1 - t) * Math.pow(t, 2) * p2.getY() +
                       Math.pow(t, 3) * p3.getY();
            return new Point2D.Double(x, y);
        }
    }

    public static class SegmentPosition {
        private final PathSegment segment;
        private final double percentage;
        private final boolean isOnPath;

        public SegmentPosition(PathSegment segment, double percentage, boolean isOnPath) {
            this.segment = segment;
            this.percentage = percentage;
            this.isOnPath = isOnPath;
        }

        public PathSegment getSegment() {
            return segment;
        }

        public double getPercentage() {
            return percentage;
        }

        public boolean isOnPath() {
            return isOnPath;
        }
    }
}
