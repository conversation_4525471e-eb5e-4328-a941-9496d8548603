package com.youibot.vehicle.scheduler.modules.sim.helper.distance3;

import com.alibaba.fastjson.JSON;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.*;
import java.util.*;
import java.util.List;

public class GraphVisualization extends JPanel {

    private final List<PathUtils.PathSegment> segments;
    private final Point2D.Double point;
    private PathUtils.SegmentPosition closestPosition;

    public GraphVisualization(List<PathUtils.PathSegment> segments, Point2D.Double point) {
        this.segments = segments;
        this.point = point;
        this.closestPosition = PathUtils.findSegmentPosition(segments, point.getX(), point.getY());

        System.out.println("closestPosition = " + JSON.toJSONString( closestPosition ));
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;

        // Draw segments
        g2d.setColor(Color.BLACK);
        for (PathUtils.PathSegment segment : segments) {
            segment.draw(g2d);
        }

        // Draw the point
        g2d.setColor(Color.RED);
        g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));

        // Highlight the closest segment and position
        if (closestPosition != null) {
            g2d.setColor(Color.BLUE);
            PathUtils.PathSegment segment = closestPosition.getSegment();
            segment.highlight(g2d, closestPosition.getPercentage());

            // Draw information text
            g2d.setColor(Color.BLACK);
            String info = closestPosition.isOnPath() ? "On Path" : "Off Path";
            g2d.drawString(info, (float) point.getX() + 10, (float) point.getY() - 10);

            // Draw the exact position on the path
            Point2D.Double exactPoint = segment.getExactPoint(closestPosition.getPercentage());
            g2d.setColor(Color.GREEN);
            g2d.fill(new Ellipse2D.Double(exactPoint.getX() - 5, exactPoint.getY() - 5, 10, 10));
            g2d.drawString(String.format("Exact: (%.2f, %.2f)", exactPoint.getX(), exactPoint.getY()),
                    (float) exactPoint.getX() + 10, (float) exactPoint.getY() - 10);
        }
    }

    public static void main(String[] args) {
        // Define example segments and point
        List<PathUtils.PathSegment> segments = new ArrayList<>();
        Point2D.Double p0 = new Point2D.Double(50, 50);
        Point2D.Double p1 = new Point2D.Double(150, 50);
        Point2D.Double p2 = new Point2D.Double(200, 100);
        Point2D.Double p3 = new Point2D.Double(250, 0);
        Point2D.Double p4 = new Point2D.Double(300, 50);

        segments.add(new PathUtils.LineSegment(p0, p1));
        segments.add(new PathUtils.BezierSegment(p1, p2, p3, p4));
        segments.add(new PathUtils.LineSegment(p4, p0)); // Adding a loop to test multiple paths at vertices

        Point2D.Double point = new Point2D.Double(180, 60);

        // Create and show the GUI
        JFrame frame = new JFrame("Graph Visualization");
        GraphVisualization panel = new GraphVisualization(segments, point);
        frame.add(panel);
        frame.setSize(400, 300);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}
