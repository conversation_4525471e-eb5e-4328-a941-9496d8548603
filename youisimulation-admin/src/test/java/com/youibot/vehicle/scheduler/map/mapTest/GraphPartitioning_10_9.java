/*
package com.youibot.vehicle.scheduler.map.mapTest;

import com.mxgraph.layout.mxCircleLayout;
import com.mxgraph.swing.mxGraphComponent;
import org.jgrapht.Graph;
import org.jgrapht.alg.connectivity.ConnectivityInspector;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.AbstractBaseGraph;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

*/
/**
 * 选择10个节点,为什么只返回9个
 *//*

public class GraphPartitioning_10_9 {

    // Function to split the graph into components after excluding certain nodes
    private static <V, E> List<Set<V>> splitGraphIntoComponents(AbstractBaseGraph<V, E> graph, Set<V> nodesToExclude) {
        // Create a copy of the graph excluding certain nodes
        AbstractBaseGraph<V, E> subgraph = (AbstractBaseGraph<V, E>) graph.clone();
        subgraph.removeAllVertices(nodesToExclude);

        // Find connected components
        ConnectivityInspector<V, E> inspector = new ConnectivityInspector<>(subgraph);
        return inspector.connectedSets();
    }

    // Function to evaluate partition balance
    private static <V> double evaluatePartitionBalance(List<Set<V>> partitions) {
        double avgSize = partitions.stream().mapToInt(Set::size).average().orElse(0.0);
        double maxSize = partitions.stream().mapToInt(Set::size).max().orElse(0);
        double minSize = partitions.stream().mapToInt(Set::size).min().orElse(0);

        // Metric to balance partitions, considering average size
        double balanceMetric = Math.max(maxSize - avgSize, avgSize - minSize);
        return balanceMetric; // Adjust this metric as needed
    }

    // Function to select n nodes that best partition the graph
    private static <V, E> Set<V> selectNodesForPartitioning(AbstractBaseGraph<V, E> graph, int n, Set<V> nodesToExclude) {
        // Calculate centrality for all nodes that are not in the excluded set
        Map<V, Double> centralityMap = new HashMap<>();
        for (V node : graph.vertexSet()) {
            if (!nodesToExclude.contains(node)) {
                double centrality = graph.degreeOf(node); // Example centrality: degree
                centralityMap.put(node, centrality);
            }
        }

        // Sort nodes by centrality (descending)
        List<V> sortedNodes = centralityMap.entrySet().stream()
                .sorted(Map.Entry.<V, Double>comparingByValue().reversed())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // Initial selection
        Set<V> selectedNodes = new HashSet<>();
        for (int i = 0; i < Math.min(n, sortedNodes.size()); i++) {
            selectedNodes.add(sortedNodes.get(i));
        }

        // Refine node selection
        double bestBalance = Double.MAX_VALUE;
        Set<V> bestNodes = new HashSet<>(selectedNodes);

        for (int i = 0; i < 100; i++) { // Number of iterations for refinement
            Set<V> currentSelection = new HashSet<>(selectedNodes);
            // Randomly add or remove nodes
            if (currentSelection.size() > 1) {
                V nodeToRemove = new ArrayList<>(currentSelection).get(new Random().nextInt(currentSelection.size()));
                currentSelection.remove(nodeToRemove);
            }
            if (currentSelection.size() < n && sortedNodes.size() > currentSelection.size()) {
                V nodeToAdd = sortedNodes.get(new Random().nextInt(sortedNodes.size()));
                if (!currentSelection.contains(nodeToAdd)) {
                    currentSelection.add(nodeToAdd);
                }
            }

            // Ensure selection size is exactly n
            if (currentSelection.size() < n) {
                for (int j = 0; j < n - currentSelection.size() && sortedNodes.size() > currentSelection.size(); j++) {
                    V nodeToAdd = sortedNodes.get(new Random().nextInt(sortedNodes.size()));
                    if (!currentSelection.contains(nodeToAdd)) {
                        currentSelection.add(nodeToAdd);
                    }
                }
            }

            List<Set<V>> components = splitGraphIntoComponents(graph, currentSelection);
            double currentBalance = evaluatePartitionBalance(components);
            if (currentBalance < bestBalance) {
                bestBalance = currentBalance;
                bestNodes = new HashSet<>(currentSelection);
            }
        }

        return bestNodes;
    }


    // Function to visualize the graph
    private static <V, E> void visualizeGraph(Graph<V, E> graph, Set<V> highlightedNodes, Set<V> nodesToExclude) {
        JGraphXAdapter<V, E> graphAdapter = new JGraphXAdapter<>(graph);

        JFrame frame = new JFrame();
        frame.setLayout(new BorderLayout());

        mxGraphComponent graphComponent = new mxGraphComponent(graphAdapter);
        frame.add(graphComponent, BorderLayout.CENTER);

        // Use circular layout
        mxCircleLayout layout = new mxCircleLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());

        // Highlight nodes
        graphAdapter.getModel().beginUpdate();
        try {
            // Define colors
            Color selectedColor = Color.RED;
            Color excludedColor = Color.GREEN;
            String selectedStyle = "fillColor=" + toHexString(selectedColor) + ";strokeColor=" + toHexString(selectedColor) + ";";
            String excludedStyle = "fillColor=" + toHexString(excludedColor) + ";strokeColor=" + toHexString(excludedColor) + ";";

            // Highlight selected nodes
            for (V node : highlightedNodes) {
                Object cell = graphAdapter.getVertexToCellMap().get(node);
                if (cell != null) {
                    graphAdapter.getModel().setStyle(cell, selectedStyle);
                }
            }

            // Highlight excluded nodes
            for (V node : graph.vertexSet()) {
                if (nodesToExclude.contains(node)) {
                    Object cell = graphAdapter.getVertexToCellMap().get(node);
                    if (cell != null) {
                        graphAdapter.getModel().setStyle(cell, excludedStyle);
                    }
                }
            }

            // Adjust edge labels
            for (E edge : graph.edgeSet()) {
                Object edgeCell = graphAdapter.getEdgeToCellMap().get(edge);
                V source = graph.getEdgeSource(edge);
                V target = graph.getEdgeTarget(edge);
                double weight = graph.getEdgeWeight(edge);

                String label = String.format("%s -> %s (%.2f)", source, target, weight);
                graphAdapter.getModel().setValue(edgeCell, label);
            }
        } finally {
            graphAdapter.getModel().endUpdate();
        }

        frame.setTitle("Graph Visualization with Highlighted Nodes");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        SwingUtilities.invokeLater(() -> frame.setVisible(true));
    }

    // Utility function to convert color to hex string
    private static String toHexString(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }

    public static void main(String[] args) {
        DirectedWeightedPseudograph<String, DefaultWeightedEdge> graph = new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        // Add nodes
        for (int i = 1; i <= 50; i++) {
            graph.addVertex("Node" + i);
        }

        // Add edges
        Random rand = new Random();
        for (int i = 0; i < 150; i++) {
            String source = "Node" + (rand.nextInt(50) + 1);
            String target = "Node" + (rand.nextInt(50) + 1);
            if (!source.equals(target)) {
                DefaultWeightedEdge edge = graph.addEdge(source, target);
                graph.setEdgeWeight(edge, rand.nextDouble() * 10 + 1); // Random weight between 1 and 10
            }
        }

        // Example of nodes to exclude
        Set<String> nodesToExclude = new HashSet<>(Arrays.asList("Node1", "Node2"));

        // Number of nodes to select
        int n = 20;

        // Select nodes that will partition the graph evenly
        Set<String> selectedNodes = selectNodesForPartitioning(graph, n, nodesToExclude);

        // Print the selected nodes
        System.out.println("Selected nodes for partitioning: " + selectedNodes);

        // Partition the graph based on the selected nodes
        List<Set<String>> partitions = splitGraphIntoComponents(graph, selectedNodes);

        // Print partitions
        for (int i = 0; i < partitions.size(); i++) {
            System.out.println("Partition " + (i + 1) + ": " + partitions.get(i));
        }

        // Visualize the graph with highlighted nodes and excluded nodes
        visualizeGraph(graph, selectedNodes, nodesToExclude);
    }
}
*/
