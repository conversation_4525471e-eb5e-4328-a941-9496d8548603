package com.youibot.vehicle.scheduler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.ReadContext;
import com.youibot.vehicle.scheduler.common.util.Utils;
import com.youibot.vehicle.scheduler.common.utils.CommonUtils;
import com.youibot.vehicle.scheduler.modules.sim.convert.Description;
import net.minidev.json.JSONArray;
import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.junit.Test;
import org.reflections.Reflections;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Set;

import static com.youibot.vehicle.scheduler.common.util.Utils.ACTION_STATUS_LIST;

public class SimpltTest {

    String data = "{\n" +
            "\t\"code\": 0,\n" +
            "\t\"msg\": \"success\",\n" +
            "\t\"data\": {\n" +
            "\t\t\"code\": \"qwe123\",\n" +
            "\t\t\"name\": \"qwe123\",\n" +
            "\t\t\"createTime\": 1723113106089,\n" +
            "\t\t\"publishTime\": 1723533166477,\n" +
            "\t\t\"locatingInfos\": [\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\"isDefault\": 1,\n" +
            "\t\t\t\t\"vehicleTypeCodes\": null,\n" +
            "\t\t\t\t\"vehicleTypeNames\": null,\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"type\": \"LASER_MAP\",\n" +
            "\t\t\t\t\"image\": \"Default-White-Map.png\",\n" +
            "\t\t\t\t\"width\": 2000,\n" +
            "\t\t\t\t\"height\": 2000,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"originX\": 0,\n" +
            "\t\t\t\t\"originY\": 0,\n" +
            "\t\t\t\t\"originYaw\": 0,\n" +
            "\t\t\t\t\"resolution\": 0.05\n" +
            "\t\t\t}\n" +
            "\t\t],\n" +
            "\t\t\"paths\": [\n" +
            "\n" +
            "\t\n" +
            "\t\t],\n" +
            "\t\t\"markers\": [\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_10\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 1,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 41.998125173189979,\n" +
            "\t\t\t\t\t\t\"y\": 71.87812381288859\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": null\n" +
            "\t\t\t\t}\n" +
            "\t\t\t},\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_11\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 0,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 50.78321903255961,\n" +
            "\t\t\t\t\t\t\"y\": 71.8781238128886\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": null\n" +
            "\t\t\t\t}\n" +
            "\t\t\t},\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_12\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 1,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 59.568312891929299,\n" +
            "\t\t\t\t\t\t\"y\": 71.87812381288859\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": null\n" +
            "\t\t\t\t}\n" +
            "\t\t\t},\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_13\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 0,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 41.998125173189979,\n" +
            "\t\t\t\t\t\t\"y\": 75.21112381288862\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": null\n" +
            "\t\t\t\t}\n" +
            "\t\t\t},\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_14\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 0,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 50.78321903255959,\n" +
            "\t\t\t\t\t\t\"y\": 75.21112381288862\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": null\n" +
            "\t\t\t\t}\n" +
            "\t\t\t},\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_15\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 0,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 59.568312891929299,\n" +
            "\t\t\t\t\t\t\"y\": 75.21112381288862\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": null\n" +
            "\t\t\t\t}\n" +
            "\t\t\t},\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_16\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 1,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 41.998125173189979,\n" +
            "\t\t\t\t\t\t\"y\": 78.54412381288859\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": [\n" +
            "\t\t\t\t\t\t30\n" +
            "\t\t\t\t\t]\n" +
            "\t\t\t\t}\n" +
            "\t\t\t},\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_17\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 0,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 50.783219032559617,\n" +
            "\t\t\t\t\t\t\"y\": 78.54412381288859\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": null\n" +
            "\t\t\t\t}\n" +
            "\t\t\t},\n" +
            "\t\t\t{\n" +
            "\t\t\t\t\"code\": \"qwe123_P_18\",\n" +
            "\t\t\t\t\"name\": null,\n" +
            "\t\t\t\t\"angle\": 0,\n" +
            "\t\t\t\t\"type\": \"NavigationMarker\",\n" +
            "\t\t\t\t\"vehicleMapCode\": \"qwe123\",\n" +
            "\t\t\t\t\"isPark\": 0,\n" +
            "\t\t\t\t\"isAvoid\": 1,\n" +
            "\t\t\t\t\"networkMarkerType\": 1,\n" +
            "\t\t\t\t\"dockingType\": null,\n" +
            "\t\t\t\t\"dockingDirection\": null,\n" +
            "\t\t\t\t\"markInfos\": [\n" +
            "\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\"locatingCode\": \"Default-White-Map\",\n" +
            "\t\t\t\t\t\t\"x\": 59.568312891929299,\n" +
            "\t\t\t\t\t\t\"y\": 78.54412381288859\n" +
            "\t\t\t\t\t}\n" +
            "\t\t\t\t],\n" +
            "\t\t\t\t\"params\": {\n" +
            "\t\t\t\t\t\"extendParam1\": null,\n" +
            "\t\t\t\t\t\"extendParam2\": null,\n" +
            "\t\t\t\t\t\"extendParam3\": null,\n" +
            "\t\t\t\t\t\"extendParam4\": null,\n" +
            "\t\t\t\t\t\"extendParam5\": null,\n" +
            "\t\t\t\t\t\"fixAngleVector\": null\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}\n" +
            "\t\t],\n" +
            "\t\t\"mapAreas\": []\n" +
            "\t}\n" +
            "}";

    @Test
    public void test6() {
        // Create a ReadContext from the JSON string
        ReadContext ctx = JsonPath.parse(data);
        Object result = ctx.read("$.data.locatingInfos[?(@.isDefault == 1)].locatingCode");
        // Print the result
        if (result instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) result;
            Object o = jsonArray.get(0);
            System.out.println("1 Locating Code: " + o);

        } else {
            System.out.println("result = " + result.getClass());
            System.out.println("2 Locating Code: " + result);
        }
    }

    @Test
    public void test4() {

        String alterId = "123456";
        Utils.mockActionStatus(alterId, 1);
        System.out.println("alterId = " + JSON.toJSONString(ACTION_STATUS_LIST));
        Utils.mockActionStatus(alterId, 3);
        System.out.println("alterId = " + JSON.toJSONString(ACTION_STATUS_LIST));

    }

    @Test
    public void test() {
        Multimap<String, String> multimap = ArrayListMultimap.create();

        multimap.put("key1", "value1");
        multimap.put("key1", "value2");
        multimap.put("key2", "value3");

        System.out.println(multimap);


    }

    @Test
    public void test1() {
        CaseInsensitiveMap<String, String> caseInsensitiveMap = new CaseInsensitiveMap<>();
        caseInsensitiveMap.put("Key", "Value1");
        caseInsensitiveMap.put("key", "Value2");

        // Test case-insensitive map
        System.out.println("Key: " + caseInsensitiveMap.get("Key")); // Output: Value2
        System.out.println("key: " + caseInsensitiveMap.get("key")); // Output: Value2
        System.out.println("kEy: " + caseInsensitiveMap.get("kEy")); // Output: Value2


    }


    @SuppressWarnings("unchecked")
    @Test
    public void test3() {
        Reflections reflections = new Reflections("com.youibot.vehicle.scheduler.modules.sim.enums");
        // Find all classes that are enums

        Set<Class<? extends Description>> allEnums = reflections.getSubTypesOf(Description.class);

        Map<String, Map<String, String>> map = Maps.newConcurrentMap();
        // Filter the ones that are enums

        allEnums.stream()
                .filter(Class::isEnum)
                .forEach(i -> {

                    String name = i.getName();
                    Class<? extends Enum<?>> enumType = (Class<? extends Enum<?>>) i;
                    Map<String, String> data = Maps.newHashMap();
                    // 获取方法 getDescription
                    try {
                        Method getDescriptionMethod = enumType.getMethod("getDescription");
                        for (Enum<?> enumConstant : enumType.getEnumConstants()) {

                            System.out.println("Enum constant: " + enumConstant);
                            String description = (String) getDescriptionMethod.invoke(enumConstant);
                            data.put(enumConstant.name(), description);
                        }
                        System.out.println(name);

                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    // 打印所有枚举值
                    map.put(i.getSimpleName(), data);
                });

        System.out.println(JSON.toJSONString(map));
        // Print the enums

    }


    @Test
    public void test11() {

        int i = calculateSegments(100);
        System.out.println("i = " + i);
        int i1 = calculateSegments(0);
        System.out.println("i1 = " + i1);
        int i2 = calculateSegments(-90.0);
        System.out.println("i2 = " + i2);


    }

    private static int calculateSegments(double value) {
        // 定义给定值的范围
        double minV = 0;
        double maxV = 5;

        // 定义分段数量的范围
        int minSegments = 2;
        int maxSegments = 10;

        // 计算反向映射
        double normalizedValue = (value - minV) / (maxV - minV);
        int segments = (int) Math.round(maxSegments - (normalizedValue * (maxSegments - minSegments)));

        // 确保分段数量在 1 到 10 的范围内
        return Math.max(minSegments, Math.min(segments, maxSegments));
    }

    @Test
    public void test111() {

        double v = CommonUtils.halfRoud(0.779797d);
        System.out.println("v = " + v);
    }

    @Test
    public void cal() {

        int i = calculateSegments2(0);
        System.out.println("i = " + i);
    }

    private static int calculateSegments2(double value) {
        // 定义给定值的范围
        double minV = 0;
        double maxV = 5;

        // 定义分段数量的范围
        int minSegments = 2;
        int maxSegments = 10;

        // 计算反向映射
        double normalizedValue = (value - minV) / (maxV - minV);
        int segments = (int) Math.round(maxSegments - (normalizedValue * (maxSegments - minSegments)));

        // 确保分段数量在 1 到 10 的范围内
        return Math.max(minSegments, Math.min(segments, maxSegments));
    }

    // 获取字段的 IEnum 接口中 getValue() 的值
    public static Object getEnumValue(Object entityInstance, String fieldName) {
        try {
            // 获取实体类中的字段
            Field field = entityInstance.getClass().getDeclaredField(fieldName);
            field.setAccessible(true); // 确保可以访问私有字段

            // 获取字段的值
            Object fieldValue = field.get(entityInstance);

            // 如果字段实现了 IEnum 接口，则调用 getValue() 方法
            if (fieldValue instanceof IEnum) {
                IEnum<?> iEnumValue = (IEnum<?>) fieldValue;
                return iEnumValue.getValue();
            }

            throw new IllegalArgumentException("Field does not implement IEnum: " + fieldName);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // 如果字段不存在或者无法访问，抛出异常
            throw new IllegalArgumentException("Error accessing field: " + fieldName, e);

        }
    }

    @Test
    public  void test2(){


    }
}