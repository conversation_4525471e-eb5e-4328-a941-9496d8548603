/*
package com.youibot.vehicle.scheduler;

import com.mxgraph.layout.mxCircleLayout;
import com.mxgraph.swing.mxGraphComponent;
import org.jgrapht.Graph;
import org.jgrapht.alg.shortestpath.AllDirectedPaths;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class GraphVisualization2 {

    public static void main(String[] args) {
        // Create a directed weighted graph
        Graph<String, DefaultWeightedEdge> graph = new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        // Add vertices
        String[] vertices = {"a", "b", "c", "d", "e", "f", "g", "h"};
        for (String vertex : vertices) {
            graph.addVertex(vertex);
        }

        // Add edges with weights to create multiple paths from 'a' to 'h'
        addEdge(graph, "a", "b", 2);
        addEdge(graph, "a", "c", 3);
        addEdge(graph, "b", "d", 4);
        addEdge(graph, "c", "d", 1);
        addEdge(graph, "b", "e", 5);
        addEdge(graph, "d", "e", 1);
        addEdge(graph, "d", "f", 2);
        addEdge(graph, "e", "g", 3);
        addEdge(graph, "f", "g", 4);
        addEdge(graph, "g", "h", 2);
        addEdge(graph, "a", "f", 6);
        addEdge(graph, "b", "h", 7);
        addEdge(graph, "c", "h", 5);
        addEdge(graph, "e", "h", 4);

        // Visualize the graph and display paths
        visualizeGraph(graph, "a", "h");
    }

    private static void addEdge(Graph<String, DefaultWeightedEdge> graph, String source, String target, double weight) {
        DefaultWeightedEdge edge = graph.addEdge(source, target);
        graph.setEdgeWeight(edge, weight);
    }

    private static void visualizeGraph(Graph<String, DefaultWeightedEdge> graph, String start, String end) {
        // Get all paths with a maximum length to avoid infinite cycles
        List<PathWithWeight> allPaths = getAllPaths(graph, start, end, 10);

        // Sort paths by their total weight
        allPaths.sort(Comparator.comparingDouble(PathWithWeight::getTotalWeight));

        // Create JGraphX adapter
        JGraphXAdapter<String, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // Create a Swing window to display the graph
        JFrame frame = new JFrame();
        frame.setLayout(new BorderLayout());

        // Graph panel
        mxGraphComponent graphComponent = new mxGraphComponent(graphAdapter);
        frame.add(graphComponent, BorderLayout.CENTER);

        // Create a panel to list all paths
        JPanel pathPanel = new JPanel();
        pathPanel.setLayout(new GridLayout(allPaths.size(), 1));

        // Define colors
        Color[] colors = {Color.RED, Color.BLUE, Color.GREEN, Color.ORANGE, Color.MAGENTA};

        // Display paths and highlight them
        graphAdapter.getModel().beginUpdate();
        try {
            // Add each path to the panel and visualize it
            for (int i = 0; i < allPaths.size(); i++) {
                PathWithWeight pathWithWeight = allPaths.get(i);
                List<DefaultWeightedEdge> path = pathWithWeight.getPath();
                Color color = colors[i % colors.length];
                
                // Add path description to the panel
                String pathDescription = path.stream()
                        .map(edge -> String.format("%s->%s(%.2f)", graph.getEdgeSource(edge), graph.getEdgeTarget(edge), graph.getEdgeWeight(edge)))
                        .collect(Collectors.joining(" -> "));
                JLabel pathLabel = new JLabel(String.format("Path %d (Weight: %.2f): %s", i + 1, pathWithWeight.getTotalWeight(), pathDescription));
                pathLabel.setForeground(color);
                pathPanel.add(pathLabel);

                // Highlight the path
                for (DefaultWeightedEdge edge : path) {
                    String style = String.format("strokeColor=%s;strokeWidth=2;", toHexString(color));
                    graphAdapter.setCellStyle(style, new Object[]{graphAdapter.getEdgeToCellMap().get(edge)});
                }
            }
        } finally {
            graphAdapter.getModel().endUpdate();
        }

        // Add path panel to the frame
        frame.add(new JScrollPane(pathPanel), BorderLayout.SOUTH);

        frame.setTitle("Graph Visualization with Multiple Paths");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        SwingUtilities.invokeLater(() -> frame.setVisible(true));

        // Use circle layout to display the graph
        mxCircleLayout layout = new mxCircleLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());
    }

    private static List<PathWithWeight> getAllPaths(Graph<String, DefaultWeightedEdge> graph, String start, String end, int maxPathLength) {
        AllDirectedPaths<String, DefaultWeightedEdge> allPathsFinder = new AllDirectedPaths<>(graph);
        return allPathsFinder.getAllPaths(start, end, false, maxPathLength)
                .stream()
                .map(path -> new PathWithWeight(path.getEdgeList(), path.getWeight()))
                .collect(Collectors.toList());
    }

    private static String toHexString(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }

    private static class PathWithWeight {
        private final List<DefaultWeightedEdge> path;
        private final double totalWeight;

        public PathWithWeight(List<DefaultWeightedEdge> path, double totalWeight) {
            this.path = path;
            this.totalWeight = totalWeight;
        }

        public List<DefaultWeightedEdge> getPath() {
            return path;
        }

        public double getTotalWeight() {
            return totalWeight;
        }
    }
}
*/
