/*
package com.youibot.vehicle.scheduler.map.dis6;

import com.mxgraph.layout.hierarchical.mxHierarchicalLayout;
import com.mxgraph.swing.mxGraphComponent;
import com.youibot.vehicle.scheduler.map.GraphVisualization;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import org.jgrapht.Graph;
import org.jgrapht.alg.shortestpath.AllDirectedPaths;
import org.jgrapht.ext.JGraphXAdapter;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.DirectedWeightedPseudograph;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

*/
/**
 * 大规模地图可视化
 * 可以上下拖动
 *//*

public class GraphExample3 {

    public static void main(String[] args) {
        // 创建有向带权图
        DirectedWeightedPseudograph<Marker, DefaultWeightedEdge> graph =
                new DirectedWeightedPseudograph<>(DefaultWeightedEdge.class);

        // 创建50个节点
        List<Marker> nodes = new ArrayList<>();
        for (int i = 0; i < 50; i++) {
            Marker marker = new Marker("code" + i, "name" + i, 0.0, "type", "mapCode", 1, 1, 1, "dockingType", 1 ,null);
            graph.addVertex(marker);
            nodes.add(marker);
        }

        // 确保节点数大于1，避免选择不当
        if (nodes.size() > 1) {
            // 随机添加100条边
            Random rand = new Random();
            for (int i = 0; i < 100; i++) {
                Marker source = nodes.get(rand.nextInt(nodes.size()));
                Marker target = nodes.get(rand.nextInt(nodes.size()));
                if (!source.equals(target) && !graph.containsEdge(source, target)) {
                    DefaultWeightedEdge edge = graph.addEdge(source, target);
                    // 确保边权重是正数
                    graph.setEdgeWeight(edge, 1.0 + rand.nextDouble() * 9); // 权重在 1 到 10 之间
                }
            }

            // 可视化图


            int topNPaths = 1 ;
            // 计算起点到终点的所有路径
            Marker start = nodes.get(0);
            Marker end = nodes.get(1);
            visualizeGraph(graph, start, end , topNPaths  );

            List<List<DefaultWeightedEdge>> shortestPaths = findShortestPaths(graph, start, end ,  topNPaths);

            // 打印前5条最小权重路径
            printShortestPaths(shortestPaths, graph);
        } else {
            System.out.println("Not enough nodes to create edges.");
        }
    }


    private static List<GraphVisualization.PathWithWeight> getAllPaths(Graph<Marker, DefaultWeightedEdge> graph, Marker start, Marker end, int maxPathLength) {
        AllDirectedPaths<Marker, DefaultWeightedEdge> allPathsFinder = new AllDirectedPaths<>(graph);
        return allPathsFinder.getAllPaths(start, end, false, maxPathLength)
                .stream()
                .map(path -> new GraphVisualization.PathWithWeight(path.getEdgeList(), path.getWeight()))
                .collect(Collectors.toList());
    }
    private static void visualizeGraph(Graph<Marker, DefaultWeightedEdge> graph, Marker start, Marker end, int topNPaths) {
        // Get all paths with a maximum length to avoid infinite cycles
        List<GraphVisualization.PathWithWeight> allPaths = getAllPaths(graph, start, end, 10);

        // Sort paths by their total weight
        allPaths.sort(Comparator.comparingDouble(GraphVisualization.PathWithWeight::getTotalWeight));

        // Create JGraphX adapter
        JGraphXAdapter<Marker, DefaultWeightedEdge> graphAdapter = new JGraphXAdapter<>(graph);

        // Create a Swing window to display the graph
        JFrame frame = new JFrame();
        frame.setLayout(new BorderLayout());

        // Graph panel
        mxGraphComponent graphComponent = new mxGraphComponent(graphAdapter);
        graphComponent.setPreferredSize(new Dimension(800, 600));
        frame.add(graphComponent, BorderLayout.CENTER);

        // Create a panel to list all paths
        JPanel pathPanel = new JPanel();
        pathPanel.setLayout(new GridLayout(Math.min(allPaths.size(), topNPaths), 1)); // Limit to topNPaths

        // Define colors
        Color[] colors = {Color.RED, Color.BLUE, Color.GREEN, Color.ORANGE, Color.MAGENTA};

        // Display paths and highlight them
        graphAdapter.getModel().beginUpdate();
        try {
            // Add each path to the panel and visualize it
            for (int i = 0; i < Math.min(allPaths.size(), topNPaths); i++) {
                GraphVisualization.PathWithWeight pathWithWeight = allPaths.get(i);
                List<DefaultWeightedEdge> path = pathWithWeight.getPath();
                Color color = colors[i % colors.length];

                // Add path description to the panel
                String pathDescription = path.stream()
                        .map(edge -> String.format("%s->%s(%.2f)", graph.getEdgeSource(edge).getName(), graph.getEdgeTarget(edge).getName(), graph.getEdgeWeight(edge)))
                        .collect(Collectors.joining(" -> "));
                JLabel pathLabel = new JLabel(String.format("Path %d (Weight: %.2f): %s", i + 1, pathWithWeight.getTotalWeight(), pathDescription));
                pathLabel.setForeground(color);
                pathPanel.add(pathLabel);

                // Highlight the path
                for (DefaultWeightedEdge edge : path) {
                    String style = String.format("strokeColor=%s;strokeWidth=2;", toHexString(color));
                    graphAdapter.setCellStyle(style, new Object[]{graphAdapter.getEdgeToCellMap().get(edge)});
                }
            }
        } finally {
            graphAdapter.getModel().endUpdate();
        }

        // Add path panel to a scroll pane
        JScrollPane scrollPane = new JScrollPane(pathPanel);
        scrollPane.setPreferredSize(new Dimension(800, 200)); // Adjust the size as needed
        frame.add(scrollPane, BorderLayout.SOUTH);

        frame.setTitle("Graph Visualization with Multiple Paths");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(1000, 800); // Increase frame size if needed
        SwingUtilities.invokeLater(() -> frame.setVisible(true));

        // Use hierarchical layout to display the graph
        mxHierarchicalLayout layout = new mxHierarchicalLayout(graphAdapter);
        layout.execute(graphAdapter.getDefaultParent());
    }



    // Stub class for PathWithWeight, replace with actual implementation
    public static class PathWithWeight {
        private final List<DefaultWeightedEdge> path;
        private final double totalWeight;

        public PathWithWeight(List<DefaultWeightedEdge> path, double totalWeight) {
            this.path = path;
            this.totalWeight = totalWeight;
        }

        public List<DefaultWeightedEdge> getPath() {
            return path;
        }

        public double getTotalWeight() {
            return totalWeight;
        }
    }

    private static String toHexString(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }
    private static List<List<DefaultWeightedEdge>> findShortestPaths(Graph<Marker, DefaultWeightedEdge> graph, Marker start, Marker end, int topNPaths ) {
        AllDirectedPaths<Marker, DefaultWeightedEdge> allPathsAlg = new AllDirectedPaths<>(graph);
        Collection<org.jgrapht.GraphPath<Marker, DefaultWeightedEdge>> paths = allPathsAlg.getAllPaths(start, end, true, null);

        List<List<DefaultWeightedEdge>> allPaths = new ArrayList<>();
        for (org.jgrapht.GraphPath<Marker, DefaultWeightedEdge> path : paths) {
            allPaths.add(new ArrayList<>(path.getEdgeList()));
        }

        // 根据路径总权重排序
        allPaths.sort(Comparator.comparingDouble(path ->
            path.stream().mapToDouble(edge -> graph.getEdgeWeight(edge)).sum()
        ));

        return allPaths.stream().limit( topNPaths ).collect(Collectors.toList()); // 获取前5条最小权重路径
    }

    private static void printShortestPaths(List<List<DefaultWeightedEdge>> paths, Graph<Marker, DefaultWeightedEdge> graph) {
        System.out.println("Top 5 Shortest Paths:");
        for (int i = 0; i < paths.size(); i++) {
            List<DefaultWeightedEdge> path = paths.get(i);
            double totalWeight = path.stream().mapToDouble(edge -> graph.getEdgeWeight(edge)).sum();
            System.out.println("Path " + (i + 1) + " (Total Weight: " + totalWeight + "):");
            for (DefaultWeightedEdge edge : path) {
                Marker source = graph.getEdgeSource(edge);
                Marker target = graph.getEdgeTarget(edge);
                System.out.println("  " + source.getName() + " -> " + target.getName() + " (Weight: " + graph.getEdgeWeight(edge) + ")");
            }
            System.out.println();
        }
    }
}
*/
