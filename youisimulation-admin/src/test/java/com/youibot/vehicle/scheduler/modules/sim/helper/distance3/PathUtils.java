package com.youibot.vehicle.scheduler.modules.sim.helper.distance3;

import java.awt.*;
import java.awt.geom.*;
import java.util.List;

public class PathUtils {

    public static SegmentPosition findSegmentPosition(List<PathSegment> segments, double x, double y) {
        double minDistance = Double.MAX_VALUE;
        SegmentPosition closestPosition = null;

        for (PathSegment segment : segments) {
            SegmentPosition position = segment.getPosition(x, y);
            if (position != null) {
                double distance = segment.distanceTo(x, y);
                if (distance < minDistance) {
                    minDistance = distance;
                    closestPosition = position;
                }
            }
        }
        return closestPosition;
    }

    public interface PathSegment {
        double distanceTo(double x, double y);
        SegmentPosition getPosition(double x, double y);
        void draw(Graphics2D g2d);
        void highlight(Graphics2D g2d, double percentage);
        Point2D.Double getExactPoint(double percentage);
    }

    public static class LineSegment implements PathSegment {
        private final Point2D.Double start;
        private final Point2D.Double end;

        public LineSegment(Point2D.Double start, Point2D.Double end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public double distanceTo(double x, double y) {
            double lengthSquared = start.distanceSq(end);
            if (lengthSquared == 0) return start.distance(x, y);
            double t = Math.max(0, Math.min(1, ((x - start.getX()) * (end.getX() - start.getX()) +
                                                (y - start.getY()) * (end.getY() - start.getY())) / lengthSquared));
            Point2D.Double projection = new Point2D.Double(start.getX() + t * (end.getX() - start.getX()),
                                                          start.getY() + t * (end.getY() - start.getY()));
            return projection.distance(x, y);
        }

        @Override
        public SegmentPosition getPosition(double x, double y) {
            double length = start.distance(end);
            double t = ((x - start.getX()) * (end.getX() - start.getX()) +
                        (y - start.getY()) * (end.getY() - start.getY())) / length;
            double percent = Math.max(0, Math.min(1, t / length));
            return new SegmentPosition(this, percent, true);
        }

        @Override
        public void draw(Graphics2D g2d) {
            g2d.draw(new Line2D.Double(start, end));
        }

        @Override
        public void highlight(Graphics2D g2d, double percentage) {
            Point2D.Double point = new Point2D.Double(start.getX() + percentage * (end.getX() - start.getX()),
                                                      start.getY() + percentage * (end.getY() - start.getY()));
            g2d.setColor(Color.GREEN);
            g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));
        }

        @Override
        public Point2D.Double getExactPoint(double percentage) {
            return new Point2D.Double(start.getX() + percentage * (end.getX() - start.getX()),
                                      start.getY() + percentage * (end.getY() - start.getY()));
        }
    }

    public static class BezierSegment implements PathSegment {
        private final Point2D.Double p0, p1, p2, p3;

        public BezierSegment(Point2D.Double p0, Point2D.Double p1, Point2D.Double p2, Point2D.Double p3) {
            this.p0 = p0;
            this.p1 = p1;
            this.p2 = p2;
            this.p3 = p3;
        }

        @Override
        public double distanceTo(double x, double y) {
            double minDistance = Double.MAX_VALUE;
            int numSamples = 100;
            for (int i = 0; i <= numSamples; i++) {
                double t = i / (double) numSamples;
                Point2D.Double pointOnCurve = getBezierPoint(t);
                double distance = pointOnCurve.distance(x, y);
                minDistance = Math.min(minDistance, distance);
            }
            return minDistance;
        }

        @Override
        public SegmentPosition getPosition(double x, double y) {
            double minDistance = Double.MAX_VALUE;
            double bestT = 0;
            int numSamples = 100;
            for (int i = 0; i <= numSamples; i++) {
                double t = i / (double) numSamples;
                Point2D.Double pointOnCurve = getBezierPoint(t);
                double distance = pointOnCurve.distance(x, y);
                if (distance < minDistance) {
                    minDistance = distance;
                    bestT = t;
                }
            }
            return new SegmentPosition(this, bestT, minDistance < 10); // Using 10 as a threshold for being "on path"
        }

        @Override
        public void draw(Graphics2D g2d) {
            GeneralPath path = new GeneralPath();
            path.moveTo(p0.getX(), p0.getY());
            path.curveTo(p1.getX(), p1.getY(), p2.getX(), p2.getY(), p3.getX(), p3.getY());
            g2d.draw(path);
        }

        @Override
        public void highlight(Graphics2D g2d, double percentage) {
            Point2D.Double point = getBezierPoint(percentage);
            g2d.setColor(Color.GREEN);
            g2d.fill(new Ellipse2D.Double(point.getX() - 5, point.getY() - 5, 10, 10));
        }

        @Override
        public Point2D.Double getExactPoint(double percentage) {
            return getBezierPoint(percentage);
        }

        private Point2D.Double getBezierPoint(double t) {
            double x = Math.pow(1 - t, 3) * p0.getX() +
                       3 * Math.pow(1 - t, 2) * t * p1.getX() +
                       3 * (1 - t) * Math.pow(t, 2) * p2.getX() +
                       Math.pow(t, 3) * p3.getX();
            double y = Math.pow(1 - t, 3) * p0.getY() +
                       3 * Math.pow(1 - t, 2) * t * p1.getY() +
                       3 * (1 - t) * Math.pow(t, 2) * p2.getY() +
                       Math.pow(t, 3) * p3.getY();
            return new Point2D.Double(x, y);
        }
    }

    public static class SegmentPosition {
        private final PathSegment segment;
        private final double percentage;
        private final boolean isOnPath;

        public SegmentPosition(PathSegment segment, double percentage, boolean isOnPath) {
            this.segment = segment;
            this.percentage = percentage;
            this.isOnPath = isOnPath;
        }

        public PathSegment getSegment() {
            return segment;
        }

        public double getPercentage() {
            return percentage;
        }

        public boolean isOnPath() {
            return isOnPath;
        }
    }
}
