package com;

import com.alibaba.fastjson.JSON;
import com.youibot.vehicle.scheduler.AdminApplication;
import com.youibot.vehicle.scheduler.common.engine.service.control.DefaultControlApiService;
import com.youibot.vehicle.scheduler.common.engine.service.relocation.LaserApiService;
import com.youibot.vehicle.scheduler.common.engine.service.relocation.RelocationApiService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.Map;

@RunWith(SpringRunner.class)
@ContextConfiguration(classes = AdminApplication.class)
@SpringBootTest
public class DefaultControlApiServiceTest {

    @Autowired
    private DefaultControlApiService defaultControlApiService ;

    @Autowired
    private RelocationApiService relocationApiService;
    @Test
    public void test1() throws IOException {

        Map<String, Object> objectMap = defaultControlApiService.vehicleSensorStatus("192.168.3.149");
        String jsonString = JSON.toJSONString(objectMap);
        System.out.println("jsonString = " + jsonString);
    }



    public void test2() throws IOException {

        relocationApiService.autoRelocation( "192.168.3.149" );


    }


    @Test
    public void test3() throws IOException {

        defaultControlApiService.vehicleDockingReset("192.168.3.149");


    }

}
