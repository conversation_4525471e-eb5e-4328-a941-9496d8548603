/**
 * @brief 判断定向曲线角度变换是否安全
 * @param angleTable 抽象出来的角度列表(单位：rad)
 * @param fixAngleVector 定向的方向容器(单位：deg)
 * @param maxSteerAngle 最大舵轮转角(单位：rad)
 * @param isFixNavi 是否需要定向
 * @returns 如果安全则返回true，否则返回false
 */
export function isDirectionalCurveSafety(
  angleTable: number[], // 假设angleTable是一个包含弧度的数字数组
  fixAngleVector: number[], // 假设fixAngleVector是一个包含角度的数字数组，单位为度
  maxSteerAngle: number, // 最大舵轮转角，单位为弧度
  isFixNavi: boolean // 是否需要定向的布尔值
): boolean {
  for (let i = 0; i < fixAngleVector.length; i++) {
    if (!isFixNavi) {
      return false // 如果不需要定向，直接返回不安全
    }
    // 这里不需要打印maxSteerAngle，除非有特定的调试需求

    const direction = (fixAngleVector[i] * Math.PI) / 180 // 将度转换为弧度
    const angleLimit = maxSteerAngle // 最大转角限制，单位与maxSteerAngle相同（弧度）

    let angleSafety = true
    let angleSafetyComplement = true

    for (const tmpAngle of angleTable) {
      const deltaAngle = verifyAngle(tmpAngle - direction) // 验证角度差
      const deltaAngleComplement = verifyAngle(tmpAngle - (direction + Math.PI)) // 验证补角的角度差

      if (Math.abs(angleLimit) < Math.abs(deltaAngle)) {
        angleSafety = false // 如果角度差超过限制，标记为不安全
      }
      if (Math.abs(angleLimit) < Math.abs(deltaAngleComplement)) {
        angleSafetyComplement = false // 如果补角的角度差超过限制，标记补角为不安全
      }
    }

    // 如果原始角度和补角寻迹都超限，路径不安全
    if (!angleSafety && !angleSafetyComplement) {
      return false
    }
  }

  return true // 所有定向方向都检查完毕，返回安全
}
/**
 * 确保角度在-PI到PI之间
 *
 * @param input 输入的角度值（单位：rad）
 * @returns 处理后的角度值，确保在-PI到PI之间（单位：rad）
 */
export function verifyAngle(input: number): number {
  while (input > Math.PI) {
    input -= 2 * Math.PI
  }
  while (input < -Math.PI) {
    input += 2 * Math.PI
  }
  return input
}
// 定义节点类型
interface Node {
  x: number
  y: number
}

// 计算正切角
export function calculateTangentAngles(nodes: Node[]): number[] {
  const tangentAngles: number[] = []
  const size = nodes.length

  // 对于每个点，计算切线方向角度
  for (let i = 0; i < size; ++i) {
    let tangentAngle: number
    if (i === 0) {
      // 使用前向差分计算第一个点的切线方向角度
      tangentAngle = Math.atan2(nodes[i + 1].y - nodes[i].y, nodes[i + 1].x - nodes[i].x)
    } else if (i === size - 1) {
      // 使用后向差分计算最后一个点的切线方向角度
      // 注意：如果只有一个节点，这里将不会执行（但你可能需要处理这种边界情况）
      tangentAngle = Math.atan2(nodes[i].y - nodes[i - 1].y, nodes[i].x - nodes[i - 1].x)
    } else {
      // 使用中心差分计算中间点的切线方向角度
      tangentAngle = Math.atan2(nodes[i + 1].y - nodes[i - 1].y, nodes[i + 1].x - nodes[i - 1].x)
    }
    tangentAngles.push(tangentAngle)
  }

  return tangentAngles
}
export class DirectionalCurveSafetyDetector {
  private _angleDeque: number[] = [] // 明确声明_angleDeque的类型为number数组

  constructor() {
    // 构造函数保持不变
  }

  // 修正方法名中的拼写错误，并且使用正确的属性名
  resetAngleBoundary() {
    this._angleDeque = [] // 使用正确的属性名_angleDeque
  }

  isAngleBoundary(currentAngle: number): boolean {
    // 为currentAngle添加类型注解
    this._angleDeque.push(currentAngle)

    if (this._angleDeque.length > 3) {
      this._angleDeque.shift() // 移除数组第一个元素
    }

    if (this._angleDeque.length === 3) {
      const deltaPlus = this._angleDeque[0] - this._angleDeque[1]
      const deltaMinus = this._angleDeque[1] - this._angleDeque[2]

      if ((deltaPlus >= 0 && deltaMinus < 0) || (deltaPlus < 0 && deltaMinus >= 0)) {
        return true
      }
    }

    return false
  }
}
