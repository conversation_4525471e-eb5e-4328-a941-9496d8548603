export default {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  beforeMount(el: HTMLElement, binding: any) {
    document.addEventListener(
      'click',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (e: any) => {
        !el.contains(e.target) && binding.value()
      },
      false
    )
  },
  unmounted() {
    document.removeEventListener('click', () => {})
  }
}
