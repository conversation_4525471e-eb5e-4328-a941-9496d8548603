import { debounce } from '../index'
export default {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  created(el: any, binding: any) {
    let execFunc
    // 传参,参数为函数
    //v-debounce="() => batchDelete('delete')"
    if (binding.value instanceof Array) {
      const [func, time = 200, isDebounce] = binding.value
      if (isDebounce) {
        execFunc = debounce(func, time)
      }
    } else {
      // 不传参
      //v-debounce="batchDelete"
      execFunc = debounce(binding.value, 200)
    }
    el.addEventListener('click', execFunc)
  }
}
