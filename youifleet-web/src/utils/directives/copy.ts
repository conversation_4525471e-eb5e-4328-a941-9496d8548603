export default {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  beforeMount(el: any, binding: any) {
    el.targetContent = binding.value
    el.addEventListener('click', () => {
      if (!el.targetContent) return console.warn('没有需要复制的目标内容')
      // 创建textarea标签
      const textarea = document.createElement('textarea')
      // 设置相关属性
      textarea.readOnly = true
      textarea.style.position = 'fixed'
      textarea.style.top = '-99999px'
      // 把目标内容赋值给它的value属性
      textarea.value = el.targetContent
      // 插入到页面
      document.body.appendChild(textarea)
      // 调用onselect()方法
      textarea.select()
      // 把目标内容复制进剪贴板, 该API会返回一个Boolean
      const res = document.execCommand('Copy')
      el.targetContent = binding.value
      const successFn = binding.arg
      //如果需要触发function后的回调，需要在参数上带个success的function
      res && successFn ? successFn(el.targetContent) : console.log('复制成功，剪贴板内容：' + el.targetContent)
      // 移除textarea标签
      document.body.removeChild(textarea)
    })
  },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updated(el: any, binding: any) {
    // 实时更新最新的目标内容
    el.targetContent = binding.value
  },
  unmounted(el: HTMLElement) {
    el.removeEventListener('click', () => {})
  }
}
