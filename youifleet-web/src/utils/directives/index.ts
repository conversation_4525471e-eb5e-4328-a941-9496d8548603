import permission from './permission' //按钮级别的鉴权
import copy from './copy' //拷贝到剪贴板
import clickOutside from './clickOutside' //点击外部区域触发事件
import debounce from './debounce'
import drag from './drag'

/**
 *  获取所有自定义指令
 *
 * @returns {any} 自定义指令
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function getDirectives(): any {
  return { permission, copy, clickOutside, debounce, drag }
}
