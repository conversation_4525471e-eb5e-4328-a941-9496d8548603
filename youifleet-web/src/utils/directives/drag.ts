export default {
  mounted(el: HTMLDivElement, binding: any) {
    el.onmousedown = ev => {
      // 鼠标按下的位置
      const mouseXStart = ev.clientX
      const mouseYStart = ev.clientY
      // console.log('按下开始', mouseXStart, mouseYStart)
      // 当前滑块位置
      const rectLeft = el.offsetLeft
      const rectTop = el.offsetTop
      document.onmousemove = e => {
        // 鼠标移动的位置
        const mouseXEnd = e.clientX
        const mouseYEnd = e.clientY
        const moveX = mouseXEnd - mouseXStart + rectLeft
        const moveY = mouseYEnd - mouseYStart + rectTop
        // console.log(rectLeft, rectTop)
        el.style['top'] = moveY + 'px'
        el.style['left'] = moveX + 'px'
        el.dataset.dataTop = `${moveY + 48}`
        el.dataset.dataLeft = `${moveX}`
        e.preventDefault()
        binding.value({ moveY, moveX })
      }
      document.onmouseup = e => {
        // 取消事件
        e.preventDefault()
        document.onmousemove = null
      }
    }
  }
}
