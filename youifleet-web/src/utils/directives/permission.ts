import { permission } from '@/router'

/**
 * 用于判断权限，避免多次使用v-if或者v-show，如果没有权限则直接删除此行Dom
 *
 * @param {HTMLElement}el Dom元素
 * @param {string} permission 权限状态
 */
function toolPermission(el: HTMLElement, p?: string | string[]) {
  if (p && typeof p === 'string' && !permission.includes(p)) {
    el.parentNode && el.parentNode.removeChild(el) // 没有权限则删除元素
  } else if (p && typeof p === 'object') {
    const findData = p.find(item => {
      return permission.includes(item)
    })
    !findData && el.parentNode && el.parentNode.removeChild(el) // 没有权限则删除元素
  }
}

export default {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  mounted(el: HTMLElement, binding: any) {
    toolPermission(el, binding.value)
  },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updated(el: HTMLElement, binding: any) {
    toolPermission(el, binding.value)
  }
}
