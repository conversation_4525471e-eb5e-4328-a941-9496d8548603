/**
 * websocket封装
 * data: {url: '', open: fun, message: fun, close: fun}
 *
 * 消息通知: /agv/notification
 * AGV控制: /agv/manual
 * 获取missionWork列表: /missionWork/status
 * 获取运行中AGV列表: /agv/status
 * 激光地图点云: /agv/laserData
 * 获取运行路径 二维码导航: /missionWorkAction/movePath
 * 获取运行路径 自由导航: /agv/autoMove
 * 录制地图信息: /agvMap/recordingData
 */

interface Data {
  url: string | (() => string)
  open: () => void
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  message: (data: any) => void
  close: () => void
}
type Socket = WebSocket
export default class YOUISocket {
  reconnectNum = 1 //异常断开重连次数
  isClose = false //防止手动断开后，由于重连机制再次创建socket
  data: Data | undefined
  socket: Socket | undefined
  heartbeat: NodeJS.Timer | undefined
  constructor(data: Data) {
    this.data = data
    this.createSocket()
  }
  createSocket() {
    this.socket = new WebSocket(`ws://${window.apiUrl}${typeof this.data?.url === 'string' ? this.data?.url : this.data?.url()}`)
    //连接成功
    this.socket.onopen = () => {
      this.data?.open()
      this.reconnectNum = 1
      //心跳包
      this.heartbeat = setInterval(() => {
        try {
          this.socket?.send('{}')
        } catch (e) {
          clearInterval(this.heartbeat)
          delete this.heartbeat
        }
      }, 30000)
      console.log(`${typeof this.data?.url === 'string' ? this.data?.url : this.data?.url()}连接成功`)
    }
    //错误
    this.socket.onerror = res => {
      console.log(`错误:`, res)
    }
    //断开
    this.socket.onclose = e => {
      console.log(`断开:`)
      this.data?.close()
      this.heartbeat && clearInterval(this.heartbeat)
      delete this.heartbeat
      //异常断开重连
      // if (this.reconnectNum-- > 0) {
      setTimeout(() => {
        if (this.isClose) {
          return false
        }
        console.log('重连次数:', this.reconnectNum++)
        this.createSocket()
      }, 2000)
      // }
      console.log(
        `${typeof this.data?.url === 'string' ? this.data?.url : this.data?.url()}断开，code：${e.code}，reason：${e.reason}，wasClean：${
          e.wasClean
        }`
      )
    }
    //接收消息
    if (this.data?.message) {
      this.socket.onmessage = res => {
        this.data?.message(res)
      }
    }
  }
  close() {
    this.heartbeat && clearInterval(this.heartbeat)
    delete this.heartbeat
    this.socket?.close()
    delete this.socket
    this.isClose = true
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  send(data: any) {
    this.socket?.send(JSON.stringify(data))
  }
}
