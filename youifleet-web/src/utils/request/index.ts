import type { AxiosInstance, AxiosRequestConfig, AxiosRequestHeaders } from 'axios'
import axios from 'axios'
import { tokenLocalData } from '@/utils/storage'
import { toNext, getRoute } from '@/router'
import { message } from 'ant-design-vue'
import useStore from '@/stores'
declare module 'axios' {
  interface AxiosInstance {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (config: AxiosRequestConfig): Promise<any>
  }
}

class HttpRequest {
  private readonly timeout: number
  private readonly headers: AxiosRequestHeaders
  constructor() {
    this.timeout = 10000
    this.headers = {
      'Cache-Control': 'no-cache',
      'Access-Control-Allow-Credentials': '*',
      'Content-Type': 'application/json'
    }
  }
  getInitConfig(): AxiosRequestConfig {
    return {
      timeout: this.timeout,
      headers: this.headers,
      baseURL: '//' + window.apiUrl
    }
  }
  interceptors(instance: AxiosInstance): void {
    instance.interceptors.response.use(
      // 请求码为200
      response => {
        // 关闭心跳异常显示
        const { srviceExceptionTipStore } = useStore()

        srviceExceptionTipStore.setShow(false)
        switch (response.data.code) {
          case 401:
          case 10007:
          case 10021:
            // 清空token
            tokenLocalData('')
            setTimeout(() => {
              toNext('/Login')
            }, 500)
            break

          // 成功
          case 10413:
          case 10414:
          case 0:
            return Promise.resolve(response.data)
          case 10048:
          case 10049:
          case 10050:
          case 10051:
          case 10052:
            toNext('/operations/setting/system-settings')
            break
          default:
            break
        }
        // get请求的报错直接用后端的返回信息显示
        if (response.config.method === 'get') {
          response.data.msg && message.error(response.data.msg)
        }

        // get请求外的报错，用catch来重写报错信息
        return Promise.reject(response.data)
      },
      error => {
        console.error(error)
        const { srviceExceptionTipStore } = useStore()
        if (['/operations/tasks/task-management', '/monitoring/agv-map', '/mapEdit'].includes(getRoute().path)) {
          srviceExceptionTipStore.setShow(true)
          return Promise.reject(error)
        }
        error.message && message.error(error.message)
        return Promise.reject(error)
      }
    )
    instance.interceptors.request.use(
      function (config) {
        if (config.headers) {
          config.headers.token = tokenLocalData() as string
        }
        if (
          ['/fleet/map/vehicleMaps/updatePauseStatus', '/fleet/task/cancelAll'].includes(config.url as string) ||
          (config.url?.indexOf('/fleet/task/cancel/') !== -1 && config.method === 'post')
        ) {
          config.timeout = 35000
        } else if (config.url?.indexOf('/fleet/map/vehicleMaps/pushMapData/') !== -1) {
          config.timeout = 0
        }

        //
        // 在发送请求之前做些什么
        return config
      },
      function (error) {
        // 对请求错误做些什么
        return Promise.reject(error)
      }
    )
  }
  request(): AxiosInstance {
    const instance = axios.create(this.getInitConfig())

    this.interceptors(instance)
    return instance
  }
}

const http = new HttpRequest()

export default http.request()
