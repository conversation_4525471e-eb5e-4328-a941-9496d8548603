import { IBtnType, isObject, markerHConfig, pathLineWConfig, useTable, useLocale, Translator, INode } from 'youibot-plus'
import { elementSizeLocalData, tokenLocalData } from '@/utils/storage'
import { permission } from '@/router'
import { message } from 'ant-design-vue'
import { ConfigOnClose, MessageArgsProps } from 'ant-design-vue/lib/message'
import { VueNode } from 'ant-design-vue/es/_util/type'
import YIGl, { CHANGE_ZOOM } from 'youibot-gl'
import { MarkerMap } from '@/pages/operations/maps/map-edit/config'
import { IBasePath } from '@/hooks/use-map'
/**
 * 本地存储
 *
 * @param {string} name key
 * @param {string} value 值
 * @returns {null | string | undefined } 值
 */
export function localDB(name: string, value?: string): null | string | undefined {
  if (value !== undefined) {
    localStorage.setItem(name, value)
  } else {
    return localStorage.getItem(name)
  }
}
export interface RandomColors {
  running: string
  executed: string
  planed: string
}
export type TState = {
  [n: string]: unknown
}
type KState = keyof TState
/**
 *  覆盖原对象属性方法，返回原对象类型
 *
 * @param {TState} baseState 原对象
 * @param {TState} state 新对象
 * @returns {TState} 返回
 */
export function mixins_extend(baseState: TState, state: TState): TState {
  const result = baseState

  for (const key in state) {
    if (Object.prototype.hasOwnProperty.call(result, key) && Object.prototype.hasOwnProperty.call(state, key)) {
      const k = key as KState

      if (isObject(state[k])) {
        result[k] = mixins_extend(result[k] as TState, state[k] as TState)
      } else {
        result[k] = state[k]
      }
    }
  }
  return result
}

/**
 *监听按键按下
 *
 * @param {void} fn 回调函数
 * @returns {void} 无返回值
 */
export function listenKeykeydown(fn: (eventKey: KeyboardEvent) => void) {
  document.addEventListener('keydown', fn)
}
/**
 *监听按键弹起
 *
 * @param {void} fn 回调函数
 * @returns {void}
 */
export function listenKeyKeyup(fn: (eventKey: KeyboardEvent) => void): void {
  document.addEventListener('keyup', fn)
}
/**
 * 防抖
 *
 * @param {Function} fun 方法
 * @param {number} time 时间
 * @returns {void}
 */
export function debounce(fun: () => void, time = 200) {
  let timer: NodeJS.Timeout | number | null = null
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return function () {
    timer && clearTimeout(timer)
    timer = setTimeout(fun, time)
  }
}
export const throttle = <T extends (...args: any[]) => void>(fn: T, delay: number) => {
  let last: number = 0
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - last > delay) {
      last = now
      fn.apply(this as any, args)
    }
  }
}
//时间格式化
export const formateDateTime = (time: Date, fmt: string) => {
  const o = {
    'M+': time.getMonth() + 1, // 月份
    'D+': time.getDate(), // 日
    'H+': time.getHours(), // 小时
    'm+': time.getMinutes(), // 分
    's+': time.getSeconds(), // 秒
    'q+': Math.floor((time.getMonth() + 3) / 3), // 季度
    S: time.getMilliseconds() // 毫秒
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as { [key: string]: any }
  if (/(Y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (time.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(fmt))
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
  }
  return fmt
}

// 时间戳转换函数
export const setTimeStr = (timestamp: string | number, interval = '-'): string => {
  if (!timestamp) return ''
  const timer = new Date(timestamp)
  const year = timer.getFullYear() //年
  const month = timer.getMonth() + 1 //月
  const date = timer.getDate() //日
  const hours = timer.getHours() //时
  const minutes = timer.getMinutes() //分
  const seconds = timer.getSeconds() //秒

  return `${year}${interval}${month > 9 ? month : '0' + month}${interval}${date > 9 ? date : '0' + date} ${
    hours > 9 ? hours : '0' + hours
  }:${minutes > 9 ? minutes : '0' + minutes}:${seconds > 9 ? seconds : '0' + seconds}`
}
// 秒转为 x小时x分x秒
export const setTimeSecond = (timestamp: number, t: Translator) => {
  if (!timestamp) return ''
  const baseH = 60 * 60 // 一小时 3600s
  const baseM = 60 // 一分钟 60s

  let str = ''
  const h = parseInt((timestamp / baseH).toString(), 10)
  const m = parseInt(((timestamp % baseH) / baseM).toString(), 10)
  const s = timestamp - h * baseH - m * baseM

  str = (h > 0 ? h + t('message.hour1') : '') + (m > 0 ? m + t('message.minute') : '') + (s > 0 ? s + t('message.second') : '')
  return str
}

// 获取随机颜色（hex）
export const getRandomColor = () => {
  const range = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f']
  let str = '#'
  for (let i = 0; i < 6; i++) {
    const index = Math.floor(Math.random() * 16)
    str += range[index]
  }
  return str
}
// 获取随机颜色组（hex）
export const getRandomColors = () => {
  const range: RandomColors[] = [
    {
      running: '#43B041',
      executed: '#E7FCE3',
      planed: '#8CE384'
    },
    { running: '#10AAD6', executed: '#B5F9FF', planed: '#5DDAF0' },
    { running: '#FC9C08', executed: '#FFF1C9', planed: '#FFD278' },
    { running: '#763CD6', executed: '#DDC7FC', planed: '#B78AFF' },
    { running: '#D8147E', executed: '#FBBDE0', planed: '#F56ABD' }
  ]
  const data: string[] = []
  return function (id?: string): RandomColors {
    if (id) {
      const dataIndex = data.indexOf(id)
      if (dataIndex !== undefined) {
        if (dataIndex === -1) {
          data.push(id)
          if (data.length > 5) {
            return range[dataIndex % 5]
          }
        } else {
          return range[dataIndex >= 5 ? dataIndex % 5 : dataIndex]
        }
      }
    }
    const index = Math.floor(Math.random() * 4)

    return range[index]
  }
}
//导出 创建下载链接下载
export const downLoadFile = (data: Buffer, fileName: string) => {
  const blob = new Blob([data]) // 构造一个blob对象来处理数据
  const elink = document.createElement('a') // 创建a标签
  elink.download = fileName // a标签添加属性
  elink.style.display = 'none'
  elink.href = URL.createObjectURL(blob)
  document.body.appendChild(elink)
  elink.click() // 执行下载
  URL.revokeObjectURL(elink.href) // 释放URL 对象
  document.body.removeChild(elink) // 释放标签
}

export const messageTip = (content: VueNode | MessageArgsProps, type = 'success', onClose?: ConfigOnClose) => {
  switch (type) {
    case 'success':
      message.success(content, 1, onClose)
      break
    case 'error':
      message.error(content, 5, onClose)
      break
    case 'info':
      message.info(content, 60, onClose)
      break
    case 'warning':
      message.warning(content, 4, onClose)
      break
    case 'loading':
      message.loading(content, 3, onClose)
      break
  }
}

export const downLoadFileOpen = <T>(t: Translator, url: string, param?: T) => {
  try {
    let str = ''
    if (param) {
      for (const key in param) {
        if (typeof param[key] === 'number' || typeof param[key] === 'boolean' || param[key]) {
          str += `&${key}=${param[key]}`
        }
      }
    }
    window.open(`//${window.apiUrl}${url}?token=${tokenLocalData()}${str}`)
    getOperateText(t, {
      type: 'export'
    })
  } catch (err) {
    getOperateText(t, {
      type: 'export',
      result: 'error',
      reason: err as string
    })
  }
}

export const calcTableOperatWidth = (tableOptions: IBtnType[], path: string, number?: number) => {
  const { calcTableOperatWidth: calcTableOperatWidthFn } = useTable()
  const list: IBtnType[] = []
  tableOptions.forEach(item => {
    const option = { ...item }
    if (item.permission) {
      option.permission = path + '/' + item.permission
    }
    list.push(option)
  })
  return calcTableOperatWidthFn(permission, list, number)
}

//数组去重
export const uniqueArray = <T>(array: Array<T>): T[] => {
  return Array.from(new Set(array))
}
//数组对象去重
export const uniqueArrayObject = <T>(arrObject: Array<T>, key: string): T[] => {
  const map = new Map()
  return arrObject.filter(v => !map.has(v[key]) && map.set(v[key], v))
}
// /**
//  * 循环调用
//  * @returns Object
//  */
export class Timer {
  private _handleTimer: NodeJS.Timeout | null = null
  private _state = 'none'
  private _t = 1000
  private _fn = () => {}

  constructor() {}
  /**
   * 设置循环
   *
   * @param {Function} fn 方法
   * @param {number} wait 时间
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public setTimer(fn: any, wait = 1000) {
    this._state = 'start'
    this._t = wait
    this._fn = fn
    /**
     * 调用体
     */
    const next = async () => {
      await fn()
      if (this._handleTimer !== null && this._state !== 'stop') {
        this._handleTimer = setTimeout(next, wait)
      }
    }
    this._handleTimer = setTimeout(next, wait)
  }
  /**
   * 开始
   */
  public start() {
    this.setTimer(this._fn, this._t)
  }
  /**
   * 停止后开始
   */
  public stop() {
    this._state = 'stop'
    if (this._handleTimer) {
      clearTimeout(this._handleTimer)
    }
  }
  /**
   * 清除
   */
  public clear() {
    this._state = 'stop'
    if (this._handleTimer) {
      clearTimeout(this._handleTimer)
      this._handleTimer = null
    }
    this._handleTimer = null
  }
  /**
   * 立即执行一次
   *
   * @param {Function}fn 方法
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public now(fn: any) {
    if (fn) {
      fn()
    } else {
      this._fn()
    }
  }
}
export class CustomTimer {
  private fn: any = () => {}
  private timeHandler: NodeJS.Timeout | null = null
  private wait = 25
  private failWait = 50
  private state: string | null = null
  private count = 1
  private hasPromise = false
  constructor() {}
  /**
   * 设置轮询函数，该函数必须是异步的
   * @param {Promise} fn
   * @returns
   */
  setTimerFn(fn: any) {
    if (!fn) {
      console.log('设置轮询函数，该函数必须是异步的,切不能为空')
      return this
    }
    this.registerStart()
    this.fn = fn
    return this
  }
  registerStart() {
    if (this.state === 'stop') {
      return
    }
    this.state = 'start'
    this.clearTimeHandler()
    nextTick(() => {
      this.setSuccessNext()
    })
  }
  setWait(wait = 25) {
    this.wait = wait
    return this
  }
  setFailWait(failWait = 50) {
    this.failWait = failWait
    return this
  }
  isCanSetNext() {
    return !this.timeHandler && !['stop', 'none'].includes(this.state as string)
  }
  next() {
    // console.timeEnd("s");

    new Promise<void>(async (resolve, reject) => {
      try {
        await this.fn()
        resolve()
      } catch (error) {
        reject(error)
      }
    })
      .then(() => {
        this.timeHandler = null
        this.setSuccessNext()
      })
      .catch(() => {
        this.timeHandler = null
        this.setFialNext()
      })
    return this
  }
  clearTimeHandler() {
    if (this.timeHandler) {
      clearTimeout(this.timeHandler)
    }
    this.timeHandler = null
  }
  setNext(time: number | undefined) {
    if (this.isCanSetNext()) {
      // console.time("s");

      this.timeHandler = setTimeout(this.next.bind(this), time)
    }
    return this
  }
  setSuccessNext() {
    this.setNext(this.wait)
    return this
  }
  setFialNext() {
    this.setNext(this.failWait)
    return this
  }
  once(fn: (arg0: any) => void, ...args: any[]) {
    if (fn) {
      fn(args)
    } else {
      this.fn()
    }
    return this
  }
  now() {
    if (this.timeHandler) {
      clearTimeout(this.timeHandler)
    }
    this.timeHandler = null
    this.next()
    return this
  }
  reset() {
    this.state = 'none'
    return this
  }
  start() {
    if ((this.state = 'none')) {
      this.registerStart()
    }
    return this
  }
  stop() {
    this.state = 'stop'
    if (this.timeHandler) {
      clearTimeout(this.timeHandler)
    }
    this.timeHandler = null
    return this
  }
}
// 地图元素大小设置
export const getElementSize = (mapCode: string, yiGl: YIGl) => {
  const data = elementSizeLocalData(mapCode)

  const parseData = data ? JSON.parse(data) : ''
  if (parseData.pathWdth && parseData.markerHeght) {
    pathLineWConfig.maxLineWidth = parseData.pathWdth
    markerHConfig.maxHeight = parseData.markerHeght
  }
  yiGl.event.emit(CHANGE_ZOOM, yiGl.cameras.getJson().scale)
}
// 验证字符串是否是json
export function checkIsJSON(str: string): boolean {
  if (typeof str == 'string') {
    try {
      // console.log(JSON.stringify(str))

      var obj = JSON.parse(str)
      if (typeof obj == 'object' && obj) {
        return true
      } else {
        return false
      }
    } catch (e) {
      return false
    }
  }
  return false
}
// 地图输入框阻止事件冒泡
export function stopPropagationFn(e: KeyboardEvent) {
  if (e.key && ['x', 'c', 'z', 's', 'a', 'r', 'e', 'w', 'q', 'f', '-', '=', '+', 'v', 'b', 'n', 'y', 'm'].includes(e.key.toLowerCase())) {
    e.stopPropagation()
  }
}
// 触发事件后失焦
export function evetBlur(event: KeyboardEvent) {
  const e = event as unknown as { target: HTMLInputElement }
  e.target?.blur()
}

type TOperateText = {
  type?: string //类型，add/update/cancel/upload/import/download/delete（操作的时候直接传操作文案，如'message.map.manualDoorClosing1')
  result?: string // 结果，success/error，默认是success
  reason?: string //错误信息
  isBatch?: boolean //是否为批量操作
  isPrefix?: boolean //是否显示自己加的前缀内容
}

// 获取操作后的提示文案
export function getOperateText(t: Translator, obj: TOperateText = {}) {
  const { type = 'add', result = 'success', reason = '', isBatch = false, isPrefix = true } = obj
  let text = ''

  if (result == 'success') {
    text = (isBatch ? t('message.batch') : '') + t(`message.${type}`) + t('message.success')
  } else if (result == 'error') {
    text = isPrefix
      ? (isBatch ? t('message.batch') : '') + t(`message.${type}`) + t('message.fail') + '！' + (reason ? reason : '')
      : reason
      ? reason
      : ''
  }

  messageTip(text, result)
}

//保留n位小数
export function roundFun(value: number, n: number) {
  return Math.round(value * Math.pow(10, n)) / Math.pow(10, n)
}
/**
 * 控制点转string
 *
 * @param {string | INode} control 控制点
 * @returns {string} 控制点
 */
export function setControlString(control: string | INode): string {
  if (typeof control === 'string') {
    return control
  }
  return JSON.stringify(control)
}
export function getAngle(angle: number | undefined) {
  if (angle !== undefined && angle !== null) {
    return (angle * (180 / Math.PI)).toFixed(2)
  }
  return ''
}
// 两个多层嵌套对象对比出不同key
export function compareObjects(obj1: any, obj2: any) {
  let differentKeys: any[] = []

  function traverse(obj1: any, obj2: any) {
    for (let key in obj1) {
      if (!obj2.hasOwnProperty(key)) {
        differentKeys.push(key)
      } else if (typeof obj1[key] === 'object' && obj1[key] !== null) {
        // 如果值是对象，则递归遍历子对象
        traverse(obj1[key], obj2[key])
      }
    }

    // 检查obj2中独有的键
    for (let key in obj2) {
      if (!obj1.hasOwnProperty(key)) {
        differentKeys.push(key)
      }
    }
  }

  traverse(obj1, obj2)
  return differentKeys
}
// 数组对象下相同某值去重，并对比另一值是否相同不同则设置为undefined
export function uniqueObjectsByKey1AndSetKey2IfDifferent(arr: any[], key1: string | number, key2: string | number) {
  // 使用Map来存储key1作为键，以及一个包含所有相同key1的对象的数组作为值
  const map = new Map()

  // 遍历对象数组
  arr.forEach(obj => {
    const currentKey1 = obj[key1]

    // 如果map中不存在当前key1，则创建一个新数组来存储对象
    if (!map.has(currentKey1)) {
      map.set(currentKey1, [obj])
    } else {
      // 如果map中存在当前key1，则获取存储的数组并遍历它
      const objectsWithSameKey1 = map.get(currentKey1)
      let shouldSetUndefined = false

      // 遍历已存储的对象，比较key2的值
      objectsWithSameKey1.forEach((storedObj: { [x: string]: any }) => {
        if (storedObj[key2] !== obj[key2]) {
          // 如果找到不同的key2值，则将所有相同key1的对象的key2值设置为undefined
          shouldSetUndefined = true
        }
      })

      // 如果需要设置undefined，则设置当前对象和已存储的所有对象的key2值为undefined
      if (shouldSetUndefined) {
        objectsWithSameKey1.forEach((storedObj: { [x: string]: undefined }) => {
          storedObj[key2] = undefined
        })
        obj[key2] = undefined
      }

      // 将当前对象添加到map中对应的数组中
      objectsWithSameKey1.push(obj)
    }
  })

  // 从Map中提取唯一的对象数组（去重后的）
  const uniqueArr = []
  for (const objectsWithSameKey1 of map.values()) {
    // 只取第一个对象，因为其他对象的key2值已被设置为undefined
    uniqueArr.push(objectsWithSameKey1[0])
  }

  return uniqueArr
}
type OrientationAngle = {
  angle: number | undefined | null
}

type OrientationAngleList = Array<OrientationAngle>

type Params = {
  orientationAngleList: OrientationAngleList
}

export function mergeOrientationAngleLists(arrays: Array<{ params: Params }>): Array<OrientationAngle> {
  // 找出最长的orientationAngleList数组长度
  const maxLength = Math.max(...arrays.map(item => item.params.orientationAngleList?.length))

  // 创建一个新数组来保存合并后的结果
  const merged: Array<OrientationAngle> = Array.from({ length: maxLength }, () => ({ angle: null }))
  // 遍历每个数组
  for (const item of arrays) {
    const list = item.params.orientationAngleList
    if (!list?.length) {
      continue
    }

    // 遍历每个索引位置
    for (let i = 0; i < list?.length; i++) {
      // 如果当前索引在合并后的数组范围内，并且当前值不是undefined
      if (i < merged.length && merged[i].angle !== null) {
        // 如果合并后的数组当前位置的angle值和当前对象的angle值不同

        if (merged[i].angle !== list[i].angle) {
          // 将合并后的数组当前位置的angle设置为undefined
          merged[i].angle = undefined
        }
      } else if (i < merged.length) {
        // 如果合并后的数组当前位置还没有值，或者值是undefined，则设置当前对象的angle值
        merged[i].angle = list[i].angle
      }
    }
  }

  return merged
}
export function pathDtaProcessing(paths: IBasePath[], markerMap: MarkerMap): IBasePath {
  const path = paths[0]

  return {
    ...path,
    direction: paths.length == 2 ? 0 : 1,
    startControl: typeof path.startControl == 'string' ? JSON.parse(path.startControl) : path.startControl,
    endControl: typeof path.endControl == 'string' ? JSON.parse(path.endControl) : path.endControl,
    start: markerMap[path.startMarkerCode],
    end: markerMap[path.endMarkerCode],
    endAgvDirection: paths.length == 2 ? paths[1].agvDirection : undefined
  }
}
//hex颜色转rgb颜色
function HexToRgb(str: string) {
  var r = /^\#?[0-9A-F]{6}$/
  //test方法检查在字符串中是否存在一个模式，如果存在则返回true，否则返回false
  if (!r.test(str)) return window.alert('输入错误的hex')
  //replace替换查找的到的字符串
  str = str.replace('#', '')
  //match得到查询数组
  var hxs = str.match(/../g)
  if (hxs) {
    for (var i = 0; i < 3; i++) hxs[i as unknown as string] = parseInt(hxs[i], 16)
  }
  //alert('bf:'+hxs)
  //alert(parseInt(80, 16))
  //console.log(hxs);
  return hxs
}
const subtractLight = (color: string, amount: number) => {
  const cc = parseInt(color, 16) - amount
  const c = cc < 0 ? 0 : cc
  return c.toString(16).length > 1 ? c.toString(16) : `0${c.toString(16)}`
}

export const darken = (color: string, amount: number) => {
  color = color.indexOf('#') >= 0 ? color.substring(1, color.length) : color
  amount = Math.trunc((255 * amount) / 100)
  return `#${subtractLight(color.substring(0, 2), amount)}${subtractLight(color.substring(2, 4), amount)}${subtractLight(
    color.substring(4, 6),
    amount
  )}`
}
// 全屏切换
export function toggleFullScreen() {
  if (!document.fullscreenElement) {
    // 如果当前没有全屏元素
    document.documentElement.requestFullscreen().catch(err => {
      console.log(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`)
    })
  } else {
    if (document.exitFullscreen) {
      // 如果是全屏状态，则退出全屏
      document.exitFullscreen()
    }
  }
}
// 将秒转换为小时
export function secondsToHours(seconds: number) {
  const hours = seconds / 3600
  return hours.toFixed(2) // 保留两位小数
}
