// 曲线图
export const basicLineOption = {
  tooltip: {
    trigger: 'axis',
    confine: true
  },
  legend: {
    bottom: 7
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: []
  },
  yAxis: {
    type: 'value',
    axisLabel: {

    }
  },
  dataZoom: {
    type: 'inside'
  },
  backgroundColor: '#f5f5f5'
}
// 数据看板曲线图
export const basicLineScreenOption = {
  grid: {
    top: 10, // 根据需要调整
    left: 70
  },
  tooltip: {
    trigger: 'axis',
    confine: true
  },
  legend: {
    textStyle: {
      fontSize: '14px',
      color: '#13eaf0' // 设置文字颜色
    },
    bottom: 7
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [],
    axisLabel: {
      color: '#13eaf0', // 设置 x 轴坐标文字颜色
      fontSize: 14, // 设置字体大小
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      color: '#13eaf0', // 设置 y 轴坐标文字颜色
      fontSize: 12, // 设置字体大小
      // fontWeight: 'bold' // 设置字体粗细
    }
  },
  dataZoom: {
    type: 'inside'
  },
  backgroundColor: ''
}

// 饼图
export const basicBarSeries = {
  type: 'pie',
  radius: '70%',
  right: '20%',
  top: '7%',
  label: {
    show: false
  },
  labelLine: {
    show: false
  }
}
// 饼图
export const basicBarOption = {
  tooltip: {
    trigger: 'item',
    confine: true
  },
  legend: {
    orient: 'vertical',
    right: '2%',
    y: 'center',
    textStyle: {}
  },
  backgroundColor: '#f5f5f5'
}

// 数据看板饼图
export const basicBarScreenOption = {
  tooltip: {
    trigger: 'item',
    confine: true
  },
  legend: {
    orient: 'vertical',
    right: '2%',
    y: 'center',
    textStyle: {
      fontSize: '14px',
      color: '#fff' // 设置文字颜色为白色
    }
  },
  backgroundColor: ''
}
