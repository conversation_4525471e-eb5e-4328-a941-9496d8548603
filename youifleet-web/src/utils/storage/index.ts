import { localDB } from '@/utils'
import type { TableColumnsType } from 'ant-design-vue'
import type { ICamera } from 'youibot-gl'
export interface IUser {
  name: string
  realName: string
  superAdmin: number
}

export interface ITable {
  filterCheckList?: { [key: string]: boolean }
  columnsCheckList?: { [key: string]: boolean }
}
export interface MapBack {
  mapCode: string
  locatingCode: string
}
/**
 *  语言
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const langLocalData = function (value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB('langLocalData', value)
  } else {
    return localDB('langLocalData')
  }
}
/**
 *  token
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const tokenLocalData = function (value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB('tokenLocalData', value)
  } else {
    return localDB('tokenLocalData')
  }
}
/**
 *地图操作
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const mapOperationLocalData = function (value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB('mapOperationLocalData', value)
  } else {
    return localDB('mapOperationLocalData')
  }
}
/**
 *地图显示设置
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const mapEditDisplaySetupLocalData = function (value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB('mapEditDisplaySetupLocalData', value)
  } else {
    return localDB('mapEditDisplaySetupLocalData')
  }
}
/**
 *agv地图显示设置
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const agvMapDisplaySetupLocalData = function (value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB('agvMapDisplaySetupLocalData', value)
  } else {
    return localDB('agvMapDisplaySetupLocalData')
  }
}
/**
 *地图元素大小显示设置
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const elementSizeLocalData = function (key: string, value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB(key, value)
  } else {
    return localDB(key)
  }
}
/**
 *  记住密码
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const rememberPswLocalData = function (value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB('rememberPswLocalData', value)
  } else {
    return localDB('rememberPswLocalData')
  }
}

/**
 *  用户信息
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const userLocalData = function (value?: string): IUser | void {
  if (value !== undefined) {
    localDB('userLocalData', JSON.stringify(value))
  } else {
    const data = localDB('userLocalData')
    return data ? JSON.parse(data) : ({} as IUser)
  }
}

/**
 * 监控台
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const monitorLocalData = function (value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB('monitorLocalData', value)
  } else {
    return localDB('monitorLocalData')
  }
}

/**
 *  路由跳转地址
 *
 *@param {string} value value
 * @returns {string | undefined | null} 值
 */
export const routerLocalData = function (value?: string): string | undefined | null {
  if (value !== undefined) {
    localDB('routerLocalData', value)
  } else {
    return localDB('routerLocalData')
  }
}

/**
 *  表格记录筛选设置和列表设置,每个页面都有，用token + '_' + url 来记录key
 *
 *@param {string} url value
 *@param {string} value value
 * @returns {ITable | null} 值
 */
export const tabelLocalData = function (url: string, value?: ITable): ITable | void {
  if (value !== undefined) {
    localDB(url, JSON.stringify(value))
  } else {
    const data = localDB(url)
    return data ? JSON.parse(data) : ({} as ITable)
  }
}

/**
 *  选点组件记录上次记录，用_pointSelectionElement + '_' + 地图cdoe 来记录key
 *
 *@param {string} url value
 *@param {string} value value
 * @returns {ITable | null} 值
 */
export const localPointSelectionLocalCode = function (
  vehicleMapCode: string,
  value?: ICamera & { vehicleMapCode: string }
): (ICamera & { vehicleMapCode: string }) | null | undefined {
  if (value !== undefined) {
    localDB(`${vehicleMapCode}_pointSelectionElement`, JSON.stringify(value))
  } else {
    const data = localDB(`${vehicleMapCode}_pointSelectionElement`)
    return data ? JSON.parse(data) : (null as unknown as ICamera)
  }
}
/**
 *  记录监控台跳转到地图编辑上次数据
 *
 *@param {string} url value
 *@param {string} value value
 * @returns {ITable | null} 值
 */
export const mapCodeBackLocalCode = function (value?: MapBack | string): undefined | MapBack | null {
  if (value !== undefined) {
    localDB('_mapCodeBack', JSON.stringify(value))
  } else {
    const data = localDB('_mapCodeBack')
    return data ? JSON.parse(data) : (null as unknown as MapBack)
  }
}
/**
 *  记录单机多机模式
 *
 *@param {string} url value
 *@param {string} value value
 * @returns {ITable | null} 值
 */
export const modeLocalCode = function (value?: string): undefined | string | null {
  if (value !== undefined) {
    localDB('_mode', value)
  } else {
    const data = localDB('_mode')
    return data ? data : (null as unknown as string)
  }
}
