// 机器人统计、任务统计弹窗
.wrap {
  width: 100%;
  height: 700px;
  padding: 8px 0;

  .date-wrap {
    position: absolute;
    top: 5px;
    left: 678px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .statistic-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;

    &-row {
      display: flex;
      flex: 0 0 50%;
      height: 100%;
      padding-bottom: 20px;

      .block {
        position: relative;
        height: 100%;
        padding-right: 20px;

        &:last-child {
          padding-right: 0;
        }

        .chart-title {
          position: absolute;
          top: 10px;
          left: 10px;
          z-index: 2;
          height: 17px;
          border-left: 3px solid #e6731b;

          &-text {
            height: 17px;
            padding-left: 7px;
            font-weight: bold;
            line-height: 17px;
          }
        }
      }

      .flex1 {
        flex: 1;
      }

      .flex25 {
        flex: 0 0 25%;
      }

      .flex5 {
        flex: 0 0 50%;
      }

      &:last-child {
        padding-bottom: 0;
      }
    }
  }
}
