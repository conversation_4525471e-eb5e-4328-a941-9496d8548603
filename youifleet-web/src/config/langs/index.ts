// import enLang from '@/config/langs/en'
import cnLang from '@/config/langs/zh-cn'
import { langLocalData } from '@/utils/storage'
import useStore from '@/stores'
// import { compareObjects } from '@/utils'
import zhAnt from 'ant-design-vue/es/locale/zh_CN'
import enAnt from 'ant-design-vue/es/locale/en_US'
import HKAnt from 'ant-design-vue/es/locale/zh_HK'
import TWAnt from 'ant-design-vue/es/locale/zh_TW'
import en_GBAnt from 'ant-design-vue/es/locale/en_GB'
import ar_EGAnt from 'ant-design-vue/es/locale/ar_EG'
import az_AZAnt from 'ant-design-vue/es/locale/az_AZ'
import bg_BGAnt from 'ant-design-vue/es/locale/bg_BG'
import bn_BDAnt from 'ant-design-vue/es/locale/bn_BD'
import by_BYAnt from 'ant-design-vue/es/locale/by_BY'
import ca_ESAnt from 'ant-design-vue/es/locale/ca_ES'
import cs_CZAnt from 'ant-design-vue/es/locale/cs_CZ'
import da_DKAnt from 'ant-design-vue/es/locale/da_DK'
import de_DEAnt from 'ant-design-vue/es/locale/de_DE'
import el_GRAnt from 'ant-design-vue/es/locale/el_GR'
import es_ESAnt from 'ant-design-vue/es/locale/es_ES'
import et_EEAnt from 'ant-design-vue/es/locale/et_EE'
import fa_IRAnt from 'ant-design-vue/es/locale/fa_IR'
import fi_FIAnt from 'ant-design-vue/es/locale/fi_FI'
import fr_BEAnt from 'ant-design-vue/es/locale/fr_BE'
import fr_CAAnt from 'ant-design-vue/es/locale/fr_CA'
import fr_FRAnt from 'ant-design-vue/es/locale/fr_FR'
import ga_IEAnt from 'ant-design-vue/es/locale/ga_IE'
import gl_ESAnt from 'ant-design-vue/es/locale/gl_ES'
import he_ILAnt from 'ant-design-vue/es/locale/he_IL'
import hi_INAnt from 'ant-design-vue/es/locale/hi_IN'
import hr_HRAnt from 'ant-design-vue/es/locale/hr_HR'
import hu_HUAnt from 'ant-design-vue/es/locale/hu_HU'
import hy_AMAnt from 'ant-design-vue/es/locale/hy_AM'
import id_IDAnt from 'ant-design-vue/es/locale/id_ID'
import it_ITAnt from 'ant-design-vue/es/locale/it_IT'
import is_ISAnt from 'ant-design-vue/es/locale/is_IS'
import ja_JPAnt from 'ant-design-vue/es/locale/ja_JP'
import ka_GEAnt from 'ant-design-vue/es/locale/ka_GE'
import km_KHAnt from 'ant-design-vue/es/locale/km_KH'
import kmr_IQAnt from 'ant-design-vue/es/locale/kmr_IQ'
import kn_INAnt from 'ant-design-vue/es/locale/kn_IN'
import kk_KZAnt from 'ant-design-vue/es/locale/kk_KZ'
import ko_KRAnt from 'ant-design-vue/es/locale/ko_KR'
import lt_LTAnt from 'ant-design-vue/es/locale/lt_LT'
import lv_LVAnt from 'ant-design-vue/es/locale/lv_LV'
import mk_MKAnt from 'ant-design-vue/es/locale/mk_MK'
import ml_INAnt from 'ant-design-vue/es/locale/ml_IN'
import mn_MNAnt from 'ant-design-vue/es/locale/mn_MN'
import ms_MYAnt from 'ant-design-vue/es/locale/ms_MY'
import nb_NOAnt from 'ant-design-vue/es/locale/nb_NO'
import ne_NPAnt from 'ant-design-vue/es/locale/ne_NP'
import nl_BEAnt from 'ant-design-vue/es/locale/nl_BE'
import nl_NLAnt from 'ant-design-vue/es/locale/nl_NL'
import pl_PLAnt from 'ant-design-vue/es/locale/pl_PL'
import pt_BRAnt from 'ant-design-vue/es/locale/pt_BR'
import pt_PTAnt from 'ant-design-vue/es/locale/pt_PT'
import ro_ROAnt from 'ant-design-vue/es/locale/ro_RO'
import ru_RUAnt from 'ant-design-vue/es/locale/ru_RU'
import sk_SKAnt from 'ant-design-vue/es/locale/sk_SK'
import sr_RSAnt from 'ant-design-vue/es/locale/sr_RS'
import sl_SIAnt from 'ant-design-vue/es/locale/sl_SI'
import sv_SEAnt from 'ant-design-vue/es/locale/sv_SE'
import ta_INAnt from 'ant-design-vue/es/locale/ta_IN'
import th_THAnt from 'ant-design-vue/es/locale/th_TH'
import tr_TRAnt from 'ant-design-vue/es/locale/tr_TR'
import ur_PKAnt from 'ant-design-vue/es/locale/ur_PK'
import uk_UAAnt from 'ant-design-vue/es/locale/uk_UA'
import vi_VNAnt from 'ant-design-vue/es/locale/vi_VN'
import { getLanguageGetInUseLanguage, getLanguageId } from '@/api/language'
let webProperties = ref(cnLang)
// console.log(compareObjects(enLang.message, cnLang.message))
/**
 * 获取当前语种的语言列表
 *
 * @returns {object} message
 */
export function getMessageList(): object {
  let message = ref(cnLang)
  const { langStore } = useStore()
  const lang = langStore.getLang || 'zh_CN'
  switch (lang) {
    case 'zh_CN': //简体中文
      message.value = { ...zhAnt, ...webProperties.value }
      break
    case 'zh_HK': //繁体中文（中国香港）
      message.value = { ...HKAnt, ...webProperties.value }
      break
    case 'zh_TW': //繁体中文（中国台湾）
      message.value = { ...TWAnt, ...webProperties.value }
      break
    case 'en_US': //英语（美式）
      message.value = { ...enAnt, ...webProperties.value }
      break
    case 'en_GB': //英语
      message.value = { ...en_GBAnt, ...webProperties.value }
      break
    case 'ar_EG': //阿拉伯语
      message.value = { ...ar_EGAnt, ...webProperties.value }
      break
    case 'az_AZ': //阿塞拜疆语
      message.value = { ...az_AZAnt, ...webProperties.value }
      break
    case 'bg_BG': //保加利亚语
      message.value = { ...bg_BGAnt, ...webProperties.value }
      break
    case 'bn_BD': //孟加拉语（孟加拉国）
      message.value = { ...bn_BDAnt, ...webProperties.value }
      break
    case 'by_BY': //白俄罗斯语
      message.value = { ...by_BYAnt, ...webProperties.value }
      break
    case 'ca_ES': //加泰罗尼亚语
      message.value = { ...ca_ESAnt, ...webProperties.value }
      break
    case 'cs_CZ': //捷克语
      message.value = { ...cs_CZAnt, ...webProperties.value }
      break
    case 'da_DK': //丹麦语
      message.value = { ...da_DKAnt, ...webProperties.value }
      break
    case 'de_DE': //德语
      message.value = { ...de_DEAnt, ...webProperties.value }
      break
    case 'el_GR': //希腊语
      message.value = { ...el_GRAnt, ...webProperties.value }
      break
    case 'es_ES': //西班牙语
      message.value = { ...es_ESAnt, ...webProperties.value }
      break
    case 'et_EE': //爱沙尼亚语
      message.value = { ...et_EEAnt, ...webProperties.value }
      break
    case 'fa_IR': //波斯语
      message.value = { ...fa_IRAnt, ...webProperties.value }
      break
    case 'fi_FI': //芬兰语
      message.value = { ...fi_FIAnt, ...webProperties.value }
      break
    case 'fr_BE': //法语（比利时）
      message.value = { ...fr_BEAnt, ...webProperties.value }
      break
    case 'fr_CA': //法语（加拿大）
      message.value = { ...fr_CAAnt, ...webProperties.value }
      break
    case 'fr_FR': //法语（法国）
      message.value = { ...fr_FRAnt, ...webProperties.value }
      break
    case 'ga_IE': //爱尔兰语
      message.value = { ...ga_IEAnt, ...webProperties.value }
      break
    case 'gl_ES': //加利西亚语（西班牙）
      message.value = { ...gl_ESAnt, ...webProperties.value }
      break
    case 'he_IL': //希伯来语
      message.value = { ...he_ILAnt, ...webProperties.value }
      break
    case 'hi_IN': //印地语
      message.value = { ...hi_INAnt, ...webProperties.value }
      break
    case 'hr_HR': //克罗地亚语
      message.value = { ...hr_HRAnt, ...webProperties.value }
      break
    case 'hu_HU': //匈牙利语
      message.value = { ...hu_HUAnt, ...webProperties.value }
      break
    case 'hy_AM': //亚美尼亚
      message.value = { ...hy_AMAnt, ...webProperties.value }
      break
    case 'id_ID': //印度尼西亚语
      message.value = { ...id_IDAnt, ...webProperties.value }
      break
    case 'it_IT': //意大利语
      message.value = { ...it_ITAnt, ...webProperties.value }
      break
    case 'is_IS': //冰岛语
      message.value = { ...is_ISAnt, ...webProperties.value }
      break
    case 'ja_JP': //日语
      message.value = { ...ja_JPAnt, ...webProperties.value }
      break
    case 'ka_GE': //格鲁吉亚语
      message.value = { ...ka_GEAnt, ...webProperties.value }
      break
    case 'km_KH': //高棉语
      message.value = { ...km_KHAnt, ...webProperties.value }
      break
    case 'kmr_IQ': //北库尔德语
      message.value = { ...kmr_IQAnt, ...webProperties.value }
      break
    case 'kn_IN': //卡纳达语
      message.value = { ...kn_INAnt, ...webProperties.value }
      break
    case 'kk_KZ': //哈萨克语
      message.value = { ...kk_KZAnt, ...webProperties.value }
      break
    case 'ko_KR': //韩语/朝鲜语
      message.value = { ...ko_KRAnt, ...webProperties.value }
      break
    case 'lt_LT': //立陶宛语
      message.value = { ...lt_LTAnt, ...webProperties.value }
      break
    case 'lv_LV': //拉脱维亚语
      message.value = { ...lv_LVAnt, ...webProperties.value }
      break
    case 'mk_MK': //马其顿语
      message.value = { ...mk_MKAnt, ...webProperties.value }
      break
    case 'ml_IN': //马拉雅拉姆语
      message.value = { ...ml_INAnt, ...webProperties.value }
      break
    case 'mn_MN': //蒙古语
      message.value = { ...mn_MNAnt, ...webProperties.value }
      break
    case 'ms_MY': //马来语 (马来西亚)
      message.value = { ...ms_MYAnt, ...webProperties.value }
      break
    case 'nb_NO': //挪威语
      message.value = { ...nb_NOAnt, ...webProperties.value }
      break
    case 'ne_NP': //尼泊尔语
      message.value = { ...ne_NPAnt, ...webProperties.value }
      break
    case 'nl_BE': //荷兰语（比利时）
      message.value = { ...nl_BEAnt, ...webProperties.value }
      break
    case 'nl_NL': //荷兰语
      message.value = { ...nl_NLAnt, ...webProperties.value }
      break
    case 'pl_PL': //波兰语
      message.value = { ...pl_PLAnt, ...webProperties.value }
      break
    case 'pt_BR': //葡萄牙语(巴西)
      message.value = { ...pt_BRAnt, ...webProperties.value }
      break
    case 'pt_PT': //葡萄牙语
      message.value = { ...pt_PTAnt, ...webProperties.value }
      break
    case 'ro_RO': //罗马尼亚语
      message.value = { ...ro_ROAnt, ...webProperties.value }
      break
    case 'ru_RU': //俄罗斯语
      message.value = { ...ru_RUAnt, ...webProperties.value }
      break
    case 'sk_SK': //斯洛伐克语
      message.value = { ...sk_SKAnt, ...webProperties.value }
      break
    case 'sr_RS': //塞尔维亚语
      message.value = { ...sr_RSAnt, ...webProperties.value }
      break
    case 'sl_SI': //斯洛文尼亚语
      message.value = { ...sl_SIAnt, ...webProperties.value }
      break
    case 'sv_SE': //瑞典语
      message.value = { ...sv_SEAnt, ...webProperties.value }
      break
    case 'ta_IN': //泰米尔语
      message.value = { ...ta_INAnt, ...webProperties.value }
      break
    case 'th_TH': //泰语
      message.value = { ...th_THAnt, ...webProperties.value }
      break
    case 'tr_TR': //土耳其语
      message.value = { ...tr_TRAnt, ...webProperties.value }
      break
    case 'ur_PK': //乌尔都语 (巴基斯坦)
      message.value = { ...ur_PKAnt, ...webProperties.value }
      break
    case 'uk_UA': //乌克兰语
      message.value = { ...uk_UAAnt, ...webProperties.value }
      break
    case 'vi_VN': //越南语
      message.value = { ...vi_VNAnt, ...webProperties.value }
      break
    default:
      break
  }

  return message.value
}

/**
 * 切换语言
 *
 * @param {string} lang 语言
 */
export function changeLang(data: { code: string; lang: string; webProperties: string }, isSwitch = true) {
  const { langStore } = useStore()
  webProperties.value = JSON.parse(data.webProperties)
  let lang = langStore.getLang
  langStore.setLang('')
  langStore.setLang(data.code)
  langLocalData(data.code)
  if (lang !== data.code && isSwitch) {
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }
}

// 切换获取详情
export function getLanguageIdFn(id: string): void {
  getLanguageId({}, { id }).then(res => {
    webProperties.value = JSON.parse(res.data.webProperties)
    const { langStore } = useStore()
    langStore.setLang(res.data.code)
    langLocalData(res.data.code)
  })
}
export function getLanguageGetInUseLanguageFn(): void {
  getLanguageGetInUseLanguage().then(res => {
    changeLang(res.data, false)
  })
}
