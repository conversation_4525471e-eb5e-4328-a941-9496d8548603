// import ant from 'ant-design-vue/es/locale/zh_CN'
// import youibot from 'youibot-plus/lib/locale/lang/zh-cn'
export const cnLang = {
  message: {
    hello: '你好',
    edit: '编辑',
    del: '删除',
    add: '新增',
    success: '成功',
    fail: '失败',
    reasonIs: '原因是',
    update: '更新',
    download: '下载',
    delete: '删除',
    export: '导出',
    multExport: '批量导出',
    import: '导入',
    multImport: '批量导入',
    switchEn: '切换至英文',
    switchzh: '切换至中文',
    options: '操作',
    reset: '重置',
    status: '状态',
    statusList: {
      0: '禁用',
      1: '启用'
    },
    refresh: '刷新',
    nickname: '账号',
    userName: '用户名称',
    menu: '菜单',
    role: '角色',
    describe: '描述',
    updateTime: '更新时间',
    createTime: '创建时间',
    storageUpdateTime: '库存更新时间',
    passWord: '密码',
    custom: '自定义',
    requiredTips: '请输入',
    pleaseSelect: '请选择',
    enableList: {
      0: '是',
      1: '否'
    },
    addForm: '新增',
    editForm: '编辑',
    cancel: '取消',
    submit: '提交',
    totalData: '共 {total} 条',
    type: '类型',
    group: '分组',
    range: '范围',
    formRules: {
      phoneLen: '请输入正确的11位电话号码',
      port: '请输入正确的端口号',
      ip: '请输入正确的IP地址',
      isLength: `长度超过{max}字符`,
      isNumber: `值只能必须为数字`,
      englishFirst_: `仅支持英文（首字符）、下划线、数字组成的字符串`,
      englishFirst: `仅支持英文（首字母）、数字组成的字符串`,
      english: '仅支持英文的字符串',
      isName: '仅支持英文、下划线、数字组成的字符串'
    },
    ChargingMarker: '充电点',
    ParkingMarker: '泊车点',
    WorkMarker: '工作点',
    NavigationMarker: '导航点',
    message: '消息',
    language: '语言',
    languageSwitch: '语言切换',
    importLanguage: '语言导入',
    my: '我的',
    delTips: '确认删除？删除的内容不可恢复。',
    details: '详情',
    individuation: '个性化',
    searchSettings: '搜索设置',
    complete: '完成',
    connectTimeOut: '网络连接已超时',
    vehicle: '机器人',
    lowBattery: '低电量',
    highBattery: '高电量',
    pleaseSelectOneVehicle: '请至少选择一个机器人',
    second: '秒',
    minute: '分',
    hour: '小时',
    hour1: '时',
    day: '天',
    angle: '角度',
    speed: '速度',
    orientation: '定位',
    ip: 'IP',
    online: '在线',
    unconnected: '未连接',
    abnormal: '异常',
    inExecution: '执行中',
    encoding: '编码',
    join: '连接',
    professional: '工作',
    controls: '控制',
    all: '全部',
    previous: '上一页',
    nextPage: '下一页',
    thereIsNoPublishedTaskType: '不存在已启用的任务流程',
    solution: '解决方案',
    dragTheMapFileZipIntoThisArea: '将地图文件拖拽在此区域内',
    dragLocationMapFileZipIntoThisArea: '将定位图文件拖拽在此区域内',
    orClickHereToUpload: '或点击后选择文件上传',
    fileImport: '文件导入',
    uploadSuccessfully: '上传成功',
    confirm: '确定',
    listSetting: '列表设置',
    pleaseSelectAtLeastOneItem: '请至少选择一项属性',
    clickToUpload: '点击上传',
    fileUploadFailed: '文件上传失败',
    deleteOrNot: '确认删除',
    messageCannotReturn: '删除的内容不可恢复',
    quit: '退出',
    append: '添加',
    deleteAll: '全部删除',
    port: '端口',
    required: '必填',
    variable: '变量',
    defaultValue: '默认值',
    maximumValue: '最大值',
    minimumValue: '最小值',
    yes: '是',
    no: '否',
    value: '值',
    typeName: '类型名称',
    batch: '批量',
    create: '创建',
    batchCreate: '批量创建',
    login: '登录',
    updateAirShower: '更新风淋门',
    updateArea: '更新区域',
    updateAutodoor: '更新自动门',
    updateElevator: '更新电梯',
    updateMap: '更新地图',
    updateMarker: '更新点位',
    updatePath: '更新路径',
    deletePath: '删除路径',
    relocationManual: '重定位',
    scrapDraft: '废弃草稿',
    publish: '发布',
    copy: '复制',
    name: '名称',
    serialNumber: '编号',
    startTime: '开始时间',
    endTime: '结束时间',
    createTask: '创建任务',
    noData: '无数据',
    arguments: '参数',
    priority: '优先级',
    selectTheDataYouWantToDelete: '请选择数据',
    selectTheDataThatYouWantToModify: '请选择需要修改的数据',
    passwordInconsistencyTip: '密码和确认密码不一致，请重新输入',
    theEnteredPasswordIsInconsistent: '密码输入不一致',
    systemServiceException: '后端服务调用失败',
    founder: '创建人',
    result: '结果',
    meter: '米',
    file: '文件',
    creationMode: '创建方式',
    externalCoding: '外部编码',
    task: '任务',
    default: '默认',
    remark: '备注',
    addRemark: '添加备注',
    form: '表单',
    height: '高度',
    point: '点位',
    noDataAvailable: '暂无数据',
    cm: '厘米',
    userSetting: {
      roleManage: '角色管理',
      accountManage: '账号管理',
      auth: '功能权限',
      addRole: '新增角色',
      rename: '重命名',
      account: '账号',
      name: '姓名',
      role: '角色',
      email: '邮箱',
      phone: '电话',
      enable: '启用状态',
      updateDate: '更新时间',
      automaticLogout: '登录过期时间',
      password: '密码',
      surePassword: '确认密码',
      enableList: {
        1: '是',
        0: '否'
      },

      roleName: '值不允许超过20个字符;仅支持中文、英文、数字',
      username: '值不允许超过20个字符;英文开头;仅支持支持英文、下划线、数字',
      realName: '值不允许超过20个字符',
      autoLogoutTime: '范围为[0,9999]',
      changePassword: '修改密码',
      logout: '退出登录',
      pleaseSelectARole: '请选择角色',
      nameTip: '值不允许为空',
      theNameCannotExceed20Characters: '值不允许超过20个字符'
    },
    changePassword: {
      changePassword: '修改密码',
      resetPassword: '重置密码',
      oldPassword: '原密码',
      loginUserPassword: '登录用户密码',
      newPassword: '新密码',
      confirmNewPassword: '确认新密码'
    },
    map: {
      markerName: '值不允许超过20个字符;英文开头;仅支持英文、下划线、数字',
      type: {
        Elevator: '电梯',
        AutoDoor: '自动门',
        MapArea: '区域',
        Marker: '点位',
        Vehicle: '机器人',
        All: '所有结果',
        AirShowerDoor: '风淋门'
      },
      autoDoor: '自动门',
      manualDoorOpening: '手动开门',
      manualDoorClosing: '手动关门',
      automaticDoorCoding: '自动门编码',

      autoDoorStatus: {
        OPEN: '已开启',
        CLOSE: '已关闭',
        COMMUNICATION_ERROR: '通讯异常',
        OPERATING: '控制中',
        PARAM_ERROR: '参数异常'
      },
      areaTypes: {
        ControlArea: '封控区域',
        SingleAgvArea: '单机区域',
        ShowArea: '显示区域',
        ChannelArea: '通道区域',
        NoRotatingArea: '禁旋区域',
        NoParkingArea: '禁停区域',
        ForbiddenArea: '禁入区域',
        TrafficArea: '交管区域',
        ThirdSystemTrafficArea: '第三方交管区域'
      },
      markerTypes: {
        ChargingMarker: '充电点',
        WorkMarker: '工作点',
        NavigationMarker: '导航点'
      },
      AirShowerDoor: '风淋门',
      showerDoorCode: '风淋门编码',
      manualDoorOpening1: '手动开门1',
      manualDoorClosing1: '手动关门1',
      manualDoorOpening2: '手动开门2',
      manualDoorClosing2: '手动关门2',
      gate1Status: '门1状态',
      gate2State: '门2状态',
      areaCoding: '区域编码',
      robotCoding: '机器人编码',
      offSite: '离场',
      pointPosition: '点位',
      coordinate: '坐标',
      elevator: '电梯',
      elevatorCode: '电梯编码',
      searchMapElements: '搜索地图元素',
      currentMap: '当前地图',
      map: '地图',
      currentCoordinate: '当前坐标',
      pointType: '点位类型',
      customCoding: '自定义编码',
      pointCoding: '点位编码',
      batchNew: '批量新建',
      lineNumber: '行数',
      lineSpacing: '行间距',
      numberOfColumns: '列数',
      spaceBetweenColumns: '列间距',
      element: '元素',
      searchElement: '搜索元素',
      area: '区域',
      select: '选中',
      justificationLeft: '左对齐',
      justifyRight: '右对齐',
      topJustification: '上对齐',
      alignBottom: '下对齐',
      horizontalEquidistance: '水平等间距',
      verticalEquidistance: '垂直等间距',
      exportDraft: '导出草稿',
      publishMap: '发布地图',
      scrapDraft: '废弃草稿',
      elementList: '元素列表',
      unidirectionalPath: '单向路径',
      bidirectionalPath: '双向路径',
      quiescentTime: '静止时间',
      networkIp: '网络IP',
      door: '门',
      networkPort: '网络端口',
      openDoorControlAddress: '开门控制地址',
      openDoorControlAddressTip: '控制风淋门开门的地址，调度系统使用功能码06H写入该地址',
      openAutoDoorControlAddressTip: '控制自动门开门的地址，调度系统使用功能码06H写入该地址',
      doorControlAddress: '关门控制地址',
      doorControlAddressTip: '控制风淋门关门的地址，调度系统使用功能码06H写入该地址',
      autoDoorControlAddressTip: '控制自动门关门的地址，调度系统使用功能码06H写入该地址',
      openStateAddress: '开门状态地址',
      openStateAddressTip: '风淋门开门完成的地址，调度系统使用功能码03H读取该地址',
      autoOpenStateAddressTip: '自动门开门完成的地址，调度系统使用功能码03H读取该地址',
      closedAddress: '关门状态地址',
      closedAddressTip: '风淋门关门完成的地址，调度系统使用功能码03H读取该地址',
      autoClosedAddressTip: '自动门关门完成的地址，调度系统使用功能码03H读取该地址',
      bindingPath: '绑定路径',
      pleaseSelectAPath: '请选择设备所在的路径',
      noPointsOfIntersectionCanBeAdded: '风淋门的两条路径必须有交点',
      areaType: '区域类型',
      ControlArea: '封控区域',
      SingleAgvArea: '单机区域',
      ShowArea: '显示区域',
      ChannelArea: '通道区域',
      NoRotatingArea: '禁旋区域',
      NoParkingArea: '禁停区域',
      TrafficArea: '交管区域',
      ForbiddenArea: '禁入区域',

      ThirdSystemTrafficArea: '第三方交管区域',
      multipleValues: '多个值',
      displayName: '显示名称',
      areaColor: '区域颜色',
      position: '位置',
      mapPoint: '地图点位',
      callAddress: '呼梯地址',
      callAddressTip: '控制电梯升降的地址，调度系统使用功能码写入该地址',
      arrivalStatusAddress: '到达状态地址',
      arrivalStatusAddressTip: '判断电梯是否到达该地图的地址，调度系统使用功能码读取该地址',
      statusAddressOfTheLadderDoor: '梯门状态地址',
      statusAddressOfTheLadderDoorTip: '判断是否已打开电梯门的地址，调度系统使用功能码读取该地址',
      path: '路径',
      mapCoding: '地图编码',
      mapName: '地图名称',
      mapType: '地图类型',
      mapResolution: '地图分辨率',
      mapSize: '地图尺寸',
      originMigration: '原点偏移',
      releaseTime: '发布时间',
      qrCodeMap: '二维码地图',
      laserMap: '激光地图',
      normalResolution: '正常分辨率',
      highResolution: '高分辨率',
      enableParking: '启用泊车',
      networkType: '路网点类型',
      networkTypeList: {
        0: '交叉路网点',
        1: '普通路网点'
      },
      chargingAttribute: '充电属性',
      chargingDirection: '充电方向',
      dockingType: '对接类型',
      buttJoint: '车头对接',
      buttbutt: '车尾对接',
      reflectiveStripFeaturesContactPoints: '反光条特征对接点',
      vTypeFeatureContact: 'V型特征对接点',
      pathType: '路径类型',
      stationCode: '工位编码',
      pathWeight: '路径权重',
      vehicleTypeRestriction: '车型限制',
      lackOfRobotTypes: '缺少机器人类型',
      movingSpeed: '移动速度',
      rotationalSpeed: '旋转速度',
      movingAcceleration: '移动加速度',
      rotationalAcceleration: '旋转加速度',
      moveObstacleAvoidanceArea: '移动避障区域',
      obstacleAvoidanceArea: '避障区域',
      rotationObstacleRegion: '旋转避障区域',
      noseDirection: '车头朝向',
      potholeDetection: '坑洞检测',
      dObstacleAvoidance: '3D避障',
      featureNavigation: '融合特征',
      navigationPath: '导航路径',
      QR_Down: '二维码对接',
      LeaveDocking: '脱离对接',
      Shelflegs: '货架腿对接',
      Symbol_V: 'V型板对接',
      Reflector: '反光板对接',
      Pallet: '托盘对接',
      unsetRegion: '不设置区域',
      pleaseSelectAPointPosition: '请选择点位',
      selectPoint: '选择点位',
      aPointBitHasBeenSelected: '已选点位',
      clear: '清空',
      pleaseSelectParkingLocation: '请选择：泊车点',
      displayElement: '显示元素',
      pathDirection: '路径方向',
      displayText: '显示文字',
      areaName: '区域名称',
      elementSize: '元素大小',
      forceTheElementSizeToChange: '强制修改元素大小',
      backgroundSettings: '背景设置',
      operationHabit: '操作习惯',
      doubleClickCreateElement: '双击创建元素',
      backgroundColor: '背景颜色',
      showBackground: '显示背景图',
      displaysThePngDiagramBorder: '显示地图边界',
      limitTheDistanceBetweenPointAndPoint: '限制点位与点位的距离',
      limitTheDistanceBetweenPointsAndPaths: '限制点位与路径的距离',
      displayRobot: '显示机器人',
      realTimePointCloud: '实时点云',
      mapEditor: '地图编辑',
      monitoring: '监控',
      editParameter: '编辑参数',
      parkingOrNot: '是否泊车',
      noMapYet: '暂无地图',
      pleaseRepositionTheRobot: '请重定位机器人，Esc取消操作',
      pleaseSelectAnEndpoint: '请选择EndPoint点位',
      recordingPoint: '录制点位',
      pointRecordingSucceeded: '点位录制成功',
      discardDraftTip: '该操作不可回退，请确认是否丢弃?',
      publishMapOrNot: '是否发布地图？',
      failedToPublishMap: '发布地图失败',
      publishMapSuccessfullyTip: '发布地图成功，将自动同步地图到机器人',
      exitTheRecordingPoint: '退出录制点位',
      recordingPointTip: '“↓ ↑ ← →” 移动旋转; “Enter” 录制点位',
      remoteControlMode: '↓ ↑ ← →” 移动旋转; “Esc” 退出遥控模式',
      thereAreNoBotsOnTheCurrentMap: '当前地图不存在机器人，请先在机器人列表页切换机器人到该地图',
      thePathSelectionOperationIsCancelled: '取消选择路径操作',
      pleaseSelectRobot: '请选择机器人',
      robotIsNotConnectedTip: '机器人未连接，录制失败',
      haveBeenPublishedTip: '该地图已被发布,请退回列表',
      haveBeenDiscardedTip: '该地图草稿已被丢弃,请退回列表',
      thePointIsCreatedSuccessfully: '点位创建成功',
      outletElevator: '导出电梯',
      leadInElevator: '导入电梯',
      transferElevatorFileJson: '将电梯文件拖拽在此区域内',
      doubleClickMultiplexing: '双击复用',
      parkingSign: '泊车标识',
      locationMapList: '定位图列表',
      locationMap: '定位图',
      leadinMap: '导入定位图',
      setAsDefault: '设为默认',
      setTheRobotType: '设定机器人类型',
      deleteLocationMap: '删除定位图',
      locationMapDeletedTip: '定位图已被删除，请重新打开',
      currentAngle: '当前角度',
      fixedAngle: '固定角度',
      areavehicleTypeNameTip: '机器人类型为空时，所有机器人类型生效',
      directionToast: '值只能在-180到180之间',
      offsetX: '偏差X',
      offsetY: '偏差Y',
      offsetAngle: '偏差角度',
      dockingDirection: '对接方向',
      Head: '车头对接',
      Tail: '车尾对接',
      revocation: '撤销',
      renewal: '重做',
      cameraObstacleAvoidance: '相机避障',
      templateNumber: '模板编号',
      text1: '文本1',
      text2: '文本2',
      text3: '文本3',
      number1: '数字1',
      number2: '数字2',
      number3: '数字3',
      obstacleAvoidance: '自主绕障',
      enableAvoidance: '启用避让',
      batchNewMaximumTips: '批量新建点位数量最大限制为2000个',
      unmapped: '无定位图数据',
      key: '字段',
      value: '值',
      addExtendParam: '添加参数',
      fieldDplication: '字段不允许重复！',
      addOrientationAngle: '增加导航角度',
      checkDirectionalNavigation: '校验定向导航',
      navigationAngle: '导航角度',
      operationFailedMandatoryFieldsCannotEmpty: '操作失败，界面必填项不可为空',
      readingFunctionCode: '读功能码',
      writingFeatureCode: '写功能码',
      elevatorUsageScenario: '电梯使用场景',
      elevatorModeControlAddress: '电梯模式控制地址',
      enterRobotModeValue: '进入机器人模式值',
      exitRobotModeValue: '退出机器人模式值',
      loadDetectedValue: '有货状态值',
      doorOpenedValue: '梯门打开状态值',
      doorClosedValue: '梯门关闭状态值',
      arrivalStatusValue: '到达状态值',
      robotOnly: '机器人专用',
      humanAndRobotShared: '人机共用',
      modeStatusAddress: '模式状态地址',
      robotModeStatusValue: '机器人模式状态值',
      outgoingAddress: '外呼地址',
      internalCallAddress: '内呼地址',
      outOperateOpenValue: '外呼开门值',
      innerOperateOpenValue: '内呼开门值',
      goodsCheckAddress: '有货状态地址',
      currentStatus: '通讯状态',
      goodsCheckAddressTip: '进入前校验设备内是否有货，如果有货则不进入。该地址为空时不校验',
      currentStatusObject: {
        NORMAL: '通讯正常',
        ERROR: '通讯异常'
      },
      open: '开门',
      close: '关门',
      occupyCode: '占用方',
      occupyVehicleCode: '占用机器人编码',
      manualRelease: '手动释放',
      manualReleaseTip: '操作不可回退，确认是否释放区域占用？'
    },
    mapList: {
      releaseStatus: '发布状态',
      releaseStatusList: {
        1: '已发布',
        0: '未发布'
      },
      resolution: '分辨率',
      dimension: '尺寸',
      radioList: {
        normalResolution: '正常分辨率(0.05)',
        highResolution: '高分辨率(0.03)'
      },
      copyMap: '复制地图',
      newMapCoding: '新地图编码',
      newMapName: '新地图名称',
      selectImportMode: '选择导入方式',
      importAll: '导入全部',
      leadInNetwork: '导入路网',
      leadInLaser: '导入激光',
      importMap: '导入地图',
      importMapTip: '导入成功后，ID相同的地图数据会被覆盖掉',
      fileIsSuccessfullyImportedTip: '地图文件导入成功'
    },
    robotManage: {
      storageLocation: '储位',
      vehicle: '载具',
      status: '状态',
      abnormalStatus: '异常状态',
      vehicleCode: '编号',
      controlMode: '控制模式',
      controlModeList: {
        Manual: '手动控制',
        Auto: '自动控制',
        Repair: '检修模式'
      },
      dispatch: '调度',
      scheduleMode: '调度模式',
      scheduleModeList: {
        ManualSchedule: '手动调度',
        AutoSchedule: '自动调度'
      },

      connectStatus: '连接状态',
      connectStatusList: {
        Disconnect: '未连接',
        Connect: '已连接'
      },
      softEmerStopStatus: '运行',
      softEmerStopStatusList: {
        Close: '运行中',
        Open: '暂停中'
      },
      storageState: {
        FULL: '有料',
        EMPTY: '无料',
        ERROR: '检测错误'
      },
      errorState: {
        0: '正常',
        1: '异常'
      },
      softEmerStopStatusListBut: {
        Close: '恢复',
        Open: '暂停'
      },
      // abnormalStatus: '异常状态',
      abnormalStatusList: {
        Abnormal: '异常',
        Normal: '无异常'
      },
      workbenchAbnormalStatusList: {
        Abnormal: '异常',
        Normal: '正常'
      },
      locatedStatus: '定位状态',
      locatedStatusList: {
        NotLocated: '未定位',
        Located: '已定位'
      },

      workStatus: '工作状态',
      workStatusList: {
        Offline: '离线',
        Work: '忙碌',
        Free: '空闲'
      },
      missionName: '任务',
      missionWorkActions: '动作',
      rate: '电量',
      vehicleTypeName: '机器人类型',
      vehicleGroupName: '机器人分组',
      pilotVersion: 'Pilot版本',
      mosVersion: 'Mos版本',
      ip: 'IP地址',
      mac: 'Mac地址',
      pause: '暂停',
      restore: '恢复',
      manual: '手动',
      semiAutomatic: '半自动',
      automatic: '自动',
      restart: '重启',
      clear: '清理',
      assignMap: '指定地图',
      stopTask: '停止任务',
      viewLog: '查看日志',
      batchOperation: '批量操作',
      setUp: '设定',
      details: '详情',
      allocationProcess: '分配过程',
      softEmergencyStopBut: {
        openSoftEmergencyStop: '暂停',
        closeSoftEmergencyStop: '恢复'
      },
      enableCharging: '启用充电',
      enableParking: '启用泊车',
      chargingMarker: '充电点',
      parkingMarker: '泊车点',
      chargingList: {
        2: '默认',
        1: '启用',
        0: '禁用'
      },
      relocation: '重定位',
      powerOff: '关机',
      importRobotGrouping: '导入机器人分组',
      statistics: '统计',
      offSite: '离场',
      historicalTask: '历史任务',
      setType: '设定类型',
      setGroup: '设定分组',
      pleaseSelectARobotGroup: '请选择机器人分组',
      pleaseSelectARobotType: '请选择机器人类型',
      importedRobotType: '导入机器人类型',
      allowedRotation: '允许旋转',
      canRotateList: {
        true: '是',
        false: '否'
      },
      currentLocationMap: '当前定位图',
      switchMap: '切换地图',
      carryTask: '执行任务',
      turnCharge: '开启充电',
      openParking: '开启泊车',
      autoChargeState: {
        0: '禁止自动充电',
        1: '启用自动充电'
      },
      autoParkState: {
        0: '禁止自动泊车',
        1: '启用自动泊车'
      },
      automaticRelocation: '自动重定位',
      manualRelocation: '手动重定位',
      resetting: '复位',
      ordinaryCharging: '普通充电',
      buttReset: '对接复位'
    },
    actionSetting: {
      code: '编码',
      name: '名称',
      type: '分类',
      enterParamCount: '入参数量',
      outParamCount: '出参数量',
      add: '新增',
      export: '导出',
      import: '导入',
      preview: '预览',
      notice: '提示',
      icon: '图标',
      parameter: '参数',
      addInputParameters: '新增输入参数',
      addOutputParameters: '新增输出参数',
      parameterType: '参数类型',
      parameterCode: '参数编码',
      parameterName: '参数名称',
      category: '',
      iconUploadComplete: '图标上传完成',
      isCommonList: {
        true: '是',
        false: '否'
      },
      isAllowSkipList: {
        true: '是',
        false: '否'
      },
      baseTypeList: {
        Text: '文本',
        Number: '数字',
        Common: '通用'
      },
      componentOptions: {
        Default: '默认',
        Json: 'JSON',
        Bool: '布尔',
        RadioList: '单选列表',
        MultiList: '多选列表',
        VehicleMapCode: '单选地图',
        VehicleMapCodeList: '多选地图',
        MarkerCode: '单选点位',
        MarkerCodeList: '多选点位',
        VehicleCode: '单选机器人',
        VehicleCodeList: '多选机器人',
        VehicleTypeCode: '单选机器人类型',
        VehicleGroupCode: '单选机器人组',
        WarehouseLocationType: '单选库位类型',
        WarehouseLocation: '单选库位',
        WarehouseArea: '单选库区',
        VehicleGroupCodeList: '多选机器人组',
        VehicleTypeCodeList: '多选机器人类型',
        TaskType: '单选任务类型',
        customParameter: '自定义参数',
        MultiText: '多文本'
      },
      nodeSettingsImport: '节点设置导入',
      fileFormatJson: '拖拽文件',
      inputBox: '输入框',
      skipAllow: '允许跳过',
      retryNum: '重试次数',
      allowRetry: '允许重试'
    },
    taskManage: {
      code: '编码',
      name: '名称',
      status: '状态',
      priority: '优先级',
      robot: '机器人',
      callbackUrl: '上游地址',
      source: '来源',
      createDate: '创建时间',
      startTime: '开始时间',
      endTime: '结束时间',
      log: '日志',
      taskExecution: '任务执行',
      currentNode: '当前节点',
      executionTime: '执行时长',
      runningLog: '运行日志',
      pleaseEnterADescriptionSearch: '请输入描述搜索',
      statistics: '统计',
      runningTime: '运行时长',
      runTimes: '运行次数',
      totalHours: '总时长',
      inputValue: '输入值',
      outputValue: '输出值',
      taskInformation: '任务信息',
      nodeInformation: '节点信息',
      jsonFormatError: 'JSON格式错误',
      cancelATask: '取消任务',
      whetherToCancelATask: '是否取消任务？',
      downloadRecord: '下载记录',
      optionsStatus: {
        Create: '等待',
        Running: '执行中',
        Finished: '已完成',
        Cancel: '取消'
      },
      optionsSource: {
        Api: '接口',
        Manual: '手动下发',
        Charge: '充电策略',
        Park: '泊车策略',
        Traffic: '交管策略',
        Task: '其他任务',
        Pda: 'PDA'
      },
      batchCancellation: '批量取消',
      uploadRecord: '上传记录',
      importTaskManagement: '导入任务管理',
      uploadLog: '上传日志',
      theCompletedOrCanceledTaskIsSelected: '选中了已完成或已取消的任务',
      successfullyCancelledTask: '取消任务成功',
      failedToCancelTheTaskBecause: '取消任务失败,原因为',
      failedToCancelTask: '取消任务失败',
      successfulOperation: '操作成功',
      operationFailure: '操作失败',
      noPublishedTaskProcessExists: '不存在已启用的任务流程',
      skip: '跳过',
      remainingDistance: '剩余距离',
      totalDistance: '总距离',
      retry: '重试',
      retryTip: '是否重试',
      skipTip: '确认跳过该节点，直接执行后续节点？',
      isBreak: '可中断',
      isBreakObject: {
        0: '是',
        1: '否'
      }
    },
    taskType: {
      code: '编码',
      name: '名称',
      priority: '优先级',
      selfCheckStatus: '自检状态',
      predictExecTime: '预计执行时间',
      layout: '编排',
      implement: '执行',
      clone: '克隆',
      eventType: '事件类型',
      eventTypeList: {
        Interface: '默认',
        FixedTime: '定时事件',
        Button: '按钮事件',
        Plc: '寄存器事件',
        VehiclePlc: '机器人寄存器事件',
        VehicleAbnormal: '机器人异常事件',
        TaskCancel: '任务取消事件',
        TaskFinished: '任务完成事件'
      },
      Event: '事件类型',
      optionsTemplateType: {
        Common: '通用类型',
        Event: '事件类型'
      },
      optionsPublishStatus: {
        Published: '启用',
        Unpublished: '禁用'
      },
      typeList: {
        Common: '新建通用类型',
        Event: '新建事件类型'
      },
      jobFlowType: '作业流程类型',
      releaseStatus: '启用状态',
      importTaskType: '导入任务类型'
    },
    taskTypeArrangement: {
      Business: '通用控制',
      Common: '常用',
      Communication: '通讯组件',
      Process: '流程控制',
      AllocationResource: '分配资源',
      ObtainResource: '获取资源',
      Other: '其他',
      Vehicle: '机器人控制',
      commonNode: '常用节点',
      import: '输入',
      export: '输出',
      otherCondition: '其他条件',
      parallelBranch: '并行分支',
      cycleCondition: '循环条件',
      endLoop: '结束循环',
      start: '开始',
      end: '结束',
      settingsCommonlyUsed: '设置常用',
      parallel: '并行',
      parallelNode: '并行节点',
      condition: '条件',
      conditionalNode: '条件节点',
      circulation: '循环',
      loopNode: '循环节点',
      addCondition: '添加条件',
      settingCommonNodes: '设置常用节点',
      addParallel: '添加并行',
      displayName: '显示名称',
      thePropertiesAreNotSavedPressEnterToConfirm: '属性未保存，请使用“Enter”键确认',
      cycleTime: '循环时间',
      cycleNumber: '循环次数',
      constantValue: '定值',
      setOfConditions: '条件组',
      and: '且',
      or: '或',
      addConditionGroup: '添加条件组',
      lt: '小于',
      ne: '不等于',
      eq: '等于',
      gt: '大于',
      ge: '大于等于',
      le: '小于等于',
      belong: '属于',
      contain: '包含',
      eventType: '事件类型',
      ip: 'IP地址',
      portNumber: '端口号',
      functionCode: '功能码',
      registerAddress: '寄存器地址',
      registerValue: '寄存器值',
      section: '部分',
      effectiveScopeRobot: '机器人',
      effectiveScopeTask: '任务',
      taskAttribute: '任务属性',
      conditionalAttribute: '条件属性',
      nodeAttribute: '节点属性',
      taskVariableInput: '任务变量（输入）',
      variableName: '变量名',
      taskVariableOutput: '任务变量（输出）',
      owningNode: '节点',
      priorityState: {
        5: '最高',
        4: '高',
        3: '中',
        2: '低',
        1: '最低'
      },
      interfaceInputFormType: {
        Json: 'JSON',
        MarkerCode: '点位',
        VehicleCode: '机器人',
        Default: '默认'
      },
      variableTypeList: {
        Default: '默认',
        Bool: '布尔',
        RadioList: '单选列表',
        MultiList: '多选列表',
        VehicleMapCode: '单选地图',
        VehicleMapCodeList: '多选地图',
        MarkerCode: '单选点位',
        MarkerCodeList: '多选点位',
        VehicleCode: '单选机器人',
        VehicleCodeList: '多选机器人',
        VehicleTypeCode: '单选机器人类型',
        VehicleTypeCodeList: '多选机器人类型',
        VehicleGroupCode: '单选机器人组',
        VehicleGroupCodeList: '多选机器人组',
        WarehouseArea: '单选库区',
        WarehouseLocation: '单选库位',
        WarehouseLocationType: '单选库位类型',
        Json: 'JSON',
        Object: '对象'
      },
      variableCategoryList: {
        Text: '文本',
        Number: '数字',
        Common: '通用'
      },
      effectiveScopeTaskState: {
        1: '全部',
        2: '部分'
      },
      variableNameDuplication: '变量名重复',
      settlementOfCondition: '条件设置',
      unpublish: '禁用',
      haveReleased: '启用',
      publishingFailedEmptyLoopExists: '启用任务失败，存在空循环的节点',
      publishingFailedTaskMustContainOtherNodes: '启用任务失败，取消任务中必须包含其他节点',
      isParameterMandatoryTip: '启用任务失败，存在未设定参数的节点',
      eventTypeList: {
        FixedTime: '定时事件',
        Button: '按钮事件',
        Plc: '寄存器事件',
        VehiclePlc: '机器人寄存器事件',
        VehicleAbnormal: '机器人异常事件',
        TaskCancel: '任务取消事件',
        TaskFinished: '任务完成事件',
        Interface: '默认'
      },
      effectiveDate: '生效日期',
      pleaseEnterTheEffectiveStartDate: '请输入生效开始日期',
      pleaseEnterTheEffectiveEndDate: '请输入生效结束日期',
      effectiveTime: '生效时间',
      pleaseEnterTheEffectiveStartTime: '请输入生效开始时间',
      pleaseEnterTheEffectiveEndTime: '请输入生效结束时间',
      interval: '间隔',
      enableOrNot: '是否启用',
      callBoxNumber: '呼叫盒编号',
      buttonNumber: '按钮编号',
      robotNumber: '机器人编号',
      robotLocationMap: '机器人所在地图',
      exceptionCoding: '异常编码',
      anomalyLevel: '异常等级',
      exceptionDetails: '异常详情',
      robotAssembly: '机器人集合',
      taskPublishingSucceeded: '任务启用成功',
      searchNode: '搜索节点',
      quickAccess: '快捷方式',
      quickAccessTip: '启用此项将允许在监控中心的机器人卡片创建任务',
      inputParameter: '输入参数',
      outputParameter: '输出参数',
      missionNumber: '任务编号',
      customOutput: '自定义输出',
      customInput: '自定义输入',
      pda: 'PDA',
      pdaTip: '启用后在 PDA 可创建此任务'
    },
    notice: {
      levelList: {
        1: '普通',
        2: '警告',
        3: '错误'
      },
      statusList: {
        0: '激活',
        1: '忽略',
        2: '关闭'
      },
      ignore: '忽略',
      level: '等级',
      source: '来源',
      quest: '任务',
      equipment: '设备',
      closingTime: '关闭时间',
      intervalTime: '间隔时间',
      importTheNotificationProfile: '导入通知配置文件',
      isUpload: '是否上报',
      isUploadState: {
        0: '否',
        1: '是'
      }
    },
    exception: {
      ignore: '忽略',
      cancelIgnore: '取消忽略',
      exceptionLevel: '异常等级',
      sourceSystem: '来源系统',
      exceptionType: '异常类型',
      solution: '解决措施',
      exceptionStatus: '异常状态',
      ignoreStatus: '忽略状态',
      robot: '机器人',
      taskId: '任务ID',
      deviceId: '设备ID',
      mapName: '地图名称',
      info: '普通',
      warning: '警告',
      error: '异常',
      unread: '未读',
      readAll: '全部已读',
      read: '已读',
      closeTime: '关闭时间',
      exceptionStatusList: {
        0: '未关闭',
        1: '已关闭'
      },
      ignoreStatusList: {
        0: '未忽略',
        1: '已忽略'
      },
      exceptionMessage: '异常消息',
      exceptionMessageDetails: '异常消息详情',
      source: '来源'
    },
    chargeConfig: {
      dialogInputList: {
        lowBattery: '设置低电量',
        highBattery: '设置高电量',
        minBatteryValue: '设置最少充电电量',
        minChargeTime: '设置最少充电时长',
        bindChargeMarkers: '设置充电点',
        chargeTaskTypeId: '设置任务'
      },
      lowBattery: '低电量（%）',
      highBattery: '高电量（%）',
      minBatteryValue: '最少充电电量（%）',
      minChargeTime: '最少充电时长（分）',
      createTask: '创建任务',
      title: '充电策略',
      describe: '机器人低电量、空闲时创建充电任务策略'
    },
    parkConfig: {
      dialogInputList: {
        bindParkMarkers: '设置泊车点',
        parkTaskTypeId: '设置任务'
      },
      createTask: '创建任务',
      title: '泊车策略',
      describe: '机器人空闲时创建泊车任务策略'
    },
    trafficConfig: {
      title: '交通配置',
      describe: '机器人绕行、路径申请、冲突处理',
      faultOptions: {
        1: '等待',
        2: '绕行'
      },
      banOptions: {
        1: '等待',
        2: '绕行',
        3: '驱赶'
      },
      collisionOptions: {
        1: '导航点',
        2: '充电点',
        3: '工作点'
      }
    },
    errorStatistical: {
      vehicleAbnormalPieChart: '机器人异常比例',
      abnormalDetailPieChart: '异常分类比例',
      avgHandleDurationPieChart: '异常平均处理时间',
      newCountLineChart: '新增异常数量',
      avgHandleDurationLineChart: '异常平均处理时间'
    },
    taskStatistical: {
      taskStatusPieChart: '任务完成比例',
      createTaskCountPieChart: '新建任务数量',
      avgAllocationDurationPieChart: '平均分配时长',
      avgExecuteDurationPieChart: '平均工作时长',
      createTaskCountLineChart: '新增任务数量',
      endTaskCountLineChart: '结束任务数量',
      avgAllocationDurationLineChart: '平均分配时长',
      avgExecuteDurationDurationLineChart: '平均工作时长'
    },
    screen: {
      agvNumStatistical: '机器人数量统计',
      taskNumStatistical: '任务数量统计',
      agvTotal: '机器人总数量',
      totalSize: '任务总数量',
      runningSize: '执行中任务数量',
      successSize: '任务成功数量',
      cancelSize: '任务取消数量',
      waitSize: '任务等待数量',
      completionRate: '任务达成率',
      titie: '调度系统可视化大屏',
      visualLargeScreen: '可视化大屏'
    },
    serverMonitoring: {
      serverParameter: '服务器规格',
      serverUsage: '服务器资源使用率',
      cpuLineChart: 'CPU使用率',
      memLineChart: '内存使用率',
      diskLineChart: '硬盘容量变化',
      mysqlLineChart: 'MySQL操作次数',
      cpuCores: 'CPU核数',
      cpuCoresUnit: '个',
      totalThreads: '线程总数',
      thread: '线程',
      memory: '内存',
      diskCapacity: '硬盘容量',
      diskUsage: '硬盘使用率'
    },
    statisticsRobots: {
      statusLineChart: '机器人状态趋势',
      statusPieChart: '机器人状态比例',
      utilizeRateLineChart: '机器人稼动率',
      text: '机器人统计'
    },
    taskTypeStatistic: {
      taskStatusPieChart: '任务完成比例',
      taskCountLineChart: '任务数量',
      avgAllocationDurationLineChart: '平均分配时长',
      avgExecuteDurationLineChart: '平均工作时长',
      text: '任务流程统计'
    },
    robotManagementStatistic: {
      statusPieChart: '机器人状态比例',
      workStatusPieChart: '机器人工作比例',
      statusLineChart: '机器人状态趋势',
      utilizeRateLineChart: '机器人稼动率'
    },
    robotMonitorStatistic: {
      taskStatusPieChart: '任务状态',
      vehicleStatusPieChart: '机器人状态',
      vehicleBatteryPieChart: '机器人电量'
    },
    licence: {
      licenseRemaining: '许可证有效期',
      licenseNotInForce: '许可证未生效',
      licenseHasExpired: '许可证已过期',
      lackOfLicense: '缺少许可证',
      renewalOfLicense: '更新许可证',
      deleteLicense: '删除许可证',
      renewalAuthorization: '更新授权'
    },
    logins: {
      userLogin: '用户登录',
      rememberThePassword: '记住密码',
      copyrightShenzhenYouaiZhiheCoLtd: '版权所有 深圳优艾智合机器人科技有限公司'
    },
    log: {
      causeOfFailure: '失败原因',
      responseTime: '响应时长',
      url: 'URL',
      requestInformation: '请求信息',
      returnInformation: '返回信息',
      successList: {
        true: '成功',
        false: '失败'
      },
      user: '用户',
      ipAddressOfTheClient: '客户端ip地址',
      operatingTime: '操作时间',
      typeList: {
        Error: '错误',
        Running: '信息',
        Warning: '警告'
      },
      category: '类别',
      data: '数据',
      lastTime: '最后更新时间',
      downloadDetailsLog: '下载详细日志',
      filename: '文件名',
      message: '报文'
    },
    systemConfiguration: {
      licenseAllocation: '许可证配置',
      licenseAllocationDescribe: '许可证信息',
      storageConfiguration: '存储配置',
      storageConfigurationDescribe: '日志数据清理、文件数据清理、业务数据清理',
      pushInterfaceConfiguration: '推送接口配置',
      robotState: '机器人状态'
    },
    storageLocation: {
      reservoirArea: '库区',
      row: '排',
      column: '列',
      layer: '层',
      operatingHeight: '作业高度',
      jobPoint: '作业点位',
      occupiedState: '状态',
      containerBarCode: '容器条码',
      storyHeight: '层高度',
      occupyStatus: {
        Lock: '锁定',
        Free: '空闲',
        Store: '存储'
      },
      setPoint: '设定点位',
      importDatabaseLocationArea: '导入库区',
      importLibraryType: '导入库位类型',
      importLocation: '导入库位',
      usageStatus: {
        Disable: '禁用',
        Enable: '启用'
      },
      enabledState: '启用状态'
    },
    today: '今天',
    yesterday: '昨天',
    thisWeek: '本周',
    lastWeek: '上周',
    thisMonth: '本月',
    lastMonth: '上月',
    last7Days: '过去7天',
    last30Days: '过去30天',
    fullTimeOut: '全场暂停中',
    fullRecoveryUnderway: '全场恢复中',
    completeTimeout: '全场暂停成功',
    fullRecoverySuccessful: '全场恢复成功',
    OperationIsTooFrequent: '操作太频繁了',
    save: '保存',
    extendedAttribute1: '扩展属性1',
    extendedAttribute2: '扩展属性2',
    extendedAttribute3: '扩展属性3',
    extendedAttribute4: '扩展属性4',
    extendedAttribute5: '扩展属性5',
    containerEncoding: '容器编码',
    currentReservoirArea: '当前库区',
    locationType: '库位类型',
    warehouse: '库位',
    languageConfiguration: '语言配置',
    languageConfigurationDescribe: '添加，下载语言包',
    numberTriggers: '触发次数',
    allowRepetition: '允许重复',
    exceptionMessage: '异常信息',
    incomingParameter: '传入参数',
    isAllowRepeatState: {
      0: '不允许',
      1: '允许'
    },
    importEvent: '导入事件',
    eventCoding: '事件编码',
    beChecking: '校验中...',
    delLanguageTip: '确定要删除该语言包吗?',
    endCancelRange: '结束取消范围',
    checkValue: '校验值',
    menuList: {
      menu: {
        monitoringCenter: '监控中心',
        PDA: 'PDA',
        agv: '机器人',
        task: '任务',
        equipment: '设备',
        operations: '运维配置',
        taskManager: '任务管理',
        taskList: '任务列表',
        taskType: '任务流程',
        eventList: '事件列表',
        robots: '机器人管理',
        robotList: '机器人列表',
        robotType: '机器人类型',
        robotGroup: '机器人组',
        mapList: '地图管理',
        storageLocation: '库位管理',
        storageLocationList: '库位列表',
        storageLocationType: '库位类型',
        storageLocationArea: '库区列表',
        notificationManager: '通知管理',
        systemLog: '系统日志',
        operationLog: '操作日志',
        interfaceLog: '接口日志',
        runningLog: '运行日志',
        setting: '系统设置',
        schedulingConfiguration: '调度配置',
        systemSettings: '系统配置',
        userSettings: '账户权限',
        notificationSettings: '通知模板',
        nodeSettings: '节点设置',
        statistical: '统计报表',
        statisticsRobots: '机器人统计',
        taskStatistical: '任务统计',
        errorStatistical: '异常统计'
      },
      button: {
        view: '查看',
        relocation: '重定位',
        switchMap: '切换地图',
        controlMode: '控制模式',
        dispatchingMode: '调度模式',
        ordinaryCharging: '普通充电',
        autocharge: '自动充电',
        automaticParking: '自动泊车',
        pause_resume: '暂停/恢复',
        reset: '复位',
        buttReset: '对接复位',
        restart: '重启',
        shutdown: '关机',
        departure: '离场',
        newTask: '新增任务',
        cancelTask: '取消任务',
        taskDetails: '任务详情',
        execution: '执行任务',
        elevato: '电梯',
        autoDoor: '自动门',
        airShower: '风淋门',
        bulkExport: '批量导出',
        batchImport: '批量导入',
        cancle: '取消',
        batchCancellation: '批量取消',
        uploadRecord: '上传记录',
        details: '详情',
        downloadRecord: '下载记录',
        remark: '备注',
        add: '新增',
        del: '删除',
        edit: '编辑',
        implement: '执行',
        layout: '编排',
        clone: '复制',
        statistic: '统计',
        enabledState: '启用/禁用',
        assignMap: '指定地图',
        SoftEmergencyStop: '开启暂停/关闭暂停',
        schedule: '自动调度/手动调度',
        controlModeState: '手动控制/自动控制',
        historicalTask: '历史任务',
        setType: '设定类型',
        setGroup: '设定分组',
        export: '导出',
        import: '导入',
        ignore: '忽略',
        activation: '激活',
        downloadDetailedLog: '下载详细日志',
        download: '下载',
        viewRoles: '查看角色',
        addRoles: '新增角色',
        rename: '重命名',
        delRoles: '删除角色',
        viewAccount: '查看账号',
        addAccount: '新增账号',
        editAccount: '编辑账号',
        delAccount: '删除账号',
        resetPasswords: '重置密码',
        containerExit: '容器出场',
        containerEntry: '容器入场',
        taskView: '任务查看',
        taskCancel: '任务取消'
      }
    }
  },
  //pda页面翻译
  pdaLang: {
    login: '登录',
    account: '账户',
    password: '密码',
    changingServerIPAddress: '修改服务器IP',
    serverIPAddressTip: '服务器地址,请谨慎修改!',
    serverAddress: '服务器地址',
    version: '版本信息',
    messageExceptionAlarm: '消息异常报警',
    logout: '退出登录',
    logoutOrNot: '是否退出登录?',
    pleaseEnterAGVNumber: '请输入AGV编号',
    refreshSuccessful: '刷新成功',
    message: '消息',
    ignore: '忽略',
    code: '编码',
    describe: '描述',
    robot: '机器人',
    taskID: '任务ID',
    operate: '操作',
    operateSuccessfully: '操作成功',
    task: '任务',
    Abnormal: '异常',
    Normal: '正常',
    cancel: '取消',
    taskdetail: '任务详情',
    taskEntry: '任务入参',
    state: '状态',
    name: '名称',
    createDate: '创建时间',
    taskDelivery: '任务下发',
    storageLocation: '库位',
    storageLocationTip: '请输入或者扫描库位',
    materialType: '物料类型',
    materialTypeTip: '请选择物料类型',
    containerCode: '容器条码',
    containerCodeTip: '请输入或者扫描容器条码',
    OK: '确定',
    containerEntry: '容器入场',
    containerExit: '容器出场',
    scnContainerRepositoryLocation: '请扫描容器或库位',
    userInformationHasExpired: '用户信息已过期',
    networkPrompt: '连接失败，请检查连接服务是否正确',
    networkPrompt2: '无法连接后端服务',
    Disconnect: '未连接',
    Connect: '已连接',
    map: '地图',
    type: '类型',
    scheduleMode: '调度',
    controlStatus: '控制',
    orientation: '定位',
    softStopSwitch: '运行',
    currentNode: '当前节点',
    executionTime: '执行时长',
    Offline: '离线',
    Work: '忙碌',
    Free: '空闲',
    ManualSchedule: '手动调度',
    AutoSchedule: '自动调度',
    Manual: '手动控制',
    Auto: '自动控制',
    NotLocated: '未定位',
    Located: '已定位',
    Close: '运行中',
    Open: '暂停中',
    mine: '我的',
    updating: '更新中....',
    updatesucceededRestarting: '更新成功，重启中',
    updateFailure: '更新失败'
  }
}

// ui库组件
const youibot = {
  yi: {
    switchLanguage: '切换语言',
    map: '地图',
    size: '尺寸',
    deleteModel: '删除元素',
    test: '测试',
    options: '操作',
    addMarker: '新建点位',
    addArea: '新建区域',
    canvasReset: '画布重置',
    canvasRotate: '画布旋转',
    canvasFullScreen: '画布全屏',
    displaySetting: '显示设置',
    addOneWayPath: '新建单向路径',
    addTwoWayPath: '新建双向路径',
    straightenCarve: '拉直路径',
    canvasSmallScreen: '画布归位',
    addPath: '新建路径',
    systemSetup: '个性化设置',
    endRecordingPoint: '结束录制点位',
    recordingPoint: '录制点位',
    mapEditor: '地图编辑',
    batchNew: '批量新建',
    rangingPath: '测量距离',
    selectedPath: '选中路径',
    up: '向上',
    down: '向下',
    aleft: '向左',
    right: '向右',
    hideSelectedPath: '隐藏路径',
    smoothPath: '路径平滑',
    beChecking: '校验中...'
  }
}
export default {
  // ...ant,
  ...youibot,
  ...cnLang
}
