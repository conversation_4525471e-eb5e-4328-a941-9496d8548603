import type { RouteLocationNormalized, RouteLocationRaw, RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory, onBeforeRouteUpdate } from 'vue-router'
import { whiteRoutes, defaultRoute } from './whiteList'
import { tokenLocalData, routerLocalData, modeLocalCode, langLocalData } from '@/utils/storage'
import { getSysMenuSelect, getSysMode } from '@/api/sys'
import { getLanguageGetInUseLanguageFn } from '@/config/langs'
import { throttle } from '@/utils'

export type RouterType = RouteRecordRaw & {
  meta: {
    title: string
    icon: string
    attribution: string
  }
  children?: RouterType[]
}

export let routes: RouterType[] = defaultRoute
interface IRouteData {
  type: number
  name: string
  url: string
  icon: string
  flag: number
  source: string
  children?: IRouteData[]
}

// 获取按钮权限列表
const generatepermission = (list: Array<IRouteData>) => {
  const permission: string[] = []
  /**
   * 递归 获取按钮数组
   *
   * @param {Array<IRouteData>} list 接口返回数据
   * @returns {void}
   */
  function recursion(list: Array<IRouteData>) {
    list.forEach(item => {
      //按钮
      if (item.type == 1 && item.source !== 'Pda') {
        permission.push(item.url)
      }
      if (item.children && item.children.length > 0) {
        recursion(item.children)
      }
    })
  }
  if (list && list.length > 0) {
    recursion(list)
  }
  return permission
}

// 生成路由数据
const generateRoute = (list: Array<IRouteData>): RouterType[] => {
  const routerList: RouterType[] = []
  const modules = require.context('../pages', true, /\.vue$/).keys()

  /**
   *
   * @param { Array<IRouteData>} routers 接口返回数据
   * @param {RouterType[]} routerData 生成数据存储
   */
  function generateRouter(routers: Array<IRouteData>, routerData: RouterType[] = []): void {
    routers.forEach(routerItem => {
      const { url, name, icon, children } = routerItem
      if (modeLocalCode() !== 'multi' && ['/operations/robots', '/statistical'].includes(url)) {
      } else {
        //判断是否存在子路由
        const isRouteChildren = children && children.length && children[0].type === 0
        const redirect = isRouteChildren ? children[0].url : undefined
        const component =
          modules.indexOf(`.${url}/index.vue`) !== -1 ? () => import(/* webpackChunkName: "[request]" */ `@/pages${url}/index.vue`) : null

        const routerItemData: RouterType = {
          path: url,
          redirect,
          name,
          component,
          meta: {
            title: name,
            icon: icon,
            attribution: name
          },
          children: []
        }

        if (isRouteChildren && routerItem.source !== 'Pda') {
          generateRouter(children, routerItemData.children)
        }
        if (routerItem.source !== 'Pda') {
          routerData.push(routerItemData)
        }
      }
    })
  }
  generateRouter(list, routerList)
  return routerList
}

/**
 * 此处返回动态路由及权限白名单
 */
async function getRoutes() {
  let loginStatus = true
  const res = await getSysMenuSelect().catch(err => {
    // messageTip(err.msg, 'error')
    loginStatus = false
    // 报错会阻断进程导致跳不回登录页面
  })
  routes = res ? generateRoute(res.data) : []
  const permission = res ? generatepermission(res.data) : []
  return { routes, permission, loginStatus }
}

export const router = createRouter({
  history: createWebHashHistory(),
  routes
})
export let routeList: RouterType[] = whiteRoutes
export let permission: string[] = []
// 记录路由
let hasRoles = true
// 白名单(不需要登录就可以访问的名单)
const whiteList = [
  '/Login',
  '/external-monitor',
  '/external-statistics-robots',
  'external-error-statistical',
  'external-task-statistical',
  'screen'
]
// 获取系统模式
const getSysModeFn = async () => {
  getLanguageGetInUseLanguageFn()
  const { data } = await getSysMode()
  if (modeLocalCode() && data.mode !== modeLocalCode()) {
    localStorage.clear()
  }
  modeLocalCode(data.mode)
}
const throttleFn = throttle(getSysModeFn, 500)
router.beforeEach(async (to, form, next) => {
  throttleFn()
  // 是否跳转到操作页或者统计页面
  const userInfo = tokenLocalData()
  if (userInfo) {
    // 如果有token
    // 获取处理好的路由和按钮权限
    const {
      routes: newRouteList,
      permission: respermission,
      loginStatus
    } = await getRoutes().then(async res => {
      return res
    })
    permission = respermission

    // 路由添加进去了没有及时更新 需要重新进去一次拦截
    if (hasRoles) {
      if (loginStatus && newRouteList.length === 0) {
        // '该账户没有任何页面权限，请联系管理员'
        tokenLocalData('')
        return
      }
      newRouteList.forEach(item => {
        return router.addRoute('Index', item)
      })
      routeList = newRouteList
      hasRoles = false
      if (to.fullPath === '/') {
        if (newRouteList.length) {
          next({ path: newRouteList[0].path })
        } else {
          next({ path: '/Index' })
        }
      } else {
        const routerLocal = routerLocalData()
        const isStatistical = routerLocal ? JSON.parse(routerLocal).statistical : false
        const isOperations = routerLocal ? JSON.parse(routerLocal).operations : false
        // 运维配置和统计页面需要记录上次跳转路由
        if (to.path.split('/')[1] === 'operations') {
          if (isOperations) {
            console.log(to.query.skip)
            if (form.path.split('/')[1] === to.path.split('/')[1] || to.query.skip) {
              next(to)
              routerLocal && routerLocalData(JSON.stringify({ ...JSON.parse(routerLocal), ...{ operations: to.path } }))
            } else {
              next(isOperations)
            }
          } else {
            routerLocal
              ? routerLocalData(JSON.stringify({ ...JSON.parse(routerLocal), ...{ operations: to.path } }))
              : routerLocalData(JSON.stringify({ operations: to.path }))
            next({ ...to, replace: true }) // 这里相当于push到一个页面 不在进入路由拦截
          }
        } else if (to.path.split('/')[1] === 'statistical') {
          if (isStatistical) {
            if (form.path.split('/')[1] === to.path.split('/')[1]) {
              next(to)
              routerLocal && routerLocalData(JSON.stringify({ ...JSON.parse(routerLocal), ...{ statistical: to.path } }))
            } else {
              next(isStatistical)
            }
          } else {
            routerLocal
              ? routerLocalData(JSON.stringify({ ...JSON.parse(routerLocal), ...{ statistical: to.path } }))
              : routerLocalData(JSON.stringify({ statistical: to.path }))
            next({ ...to, replace: true })
          }
        } else {
          next({ ...to, replace: false })
        }
      }
    } else {
      hasRoles = true
      next() // 如果不传参数就会重新执行路由拦截，重新进到这里
    }
  } else {
    hasRoles = true
    if (whiteList.includes(to.path)) {
      next()
    } else {
      next(`/Login`)
    }
  }
})
/**
 *清空动态路由
 */
export function resetRoute() {
  routeList.forEach(item => {
    if (item) {
      router.removeRoute(item.name as string)
    }
  })
}

/**
 * 路由跳转
 *
 * @param {RouteLocationRaw} to 下一个路由参数
 */
export function toNext(to: RouteLocationRaw): void {
  router.push(to)
}
/**
 *返回
 */
export function back(): void {
  router.back()
}
/**
 * 获取当前路由
 *
 * @returns {RouteRecordRaw} 当前路由
 */
export function getRoute() {
  return router.currentRoute.value
}
/**
 * 获取上一个路由
 *
 * @returns {RouteRecordRaw} 当前路由
 */
export function getHistoryBackRoute() {
  return router.options.history.state.back
}
/**
 * 获取路由query参数类型s参数
 *
 * @param {string} type query参数类型
 * @returns { RouteRecordRaw } 路由query参数类型s参数
 */
export function getQuery(type: string) {
  const $route = getRoute()

  return $route.query[type]
}

/**
 * 获取路由params参数
 *
 * @param {string} type params参数类型
 * @returns { RouteRecordRaw } 路由params参数
 */
export function getParams(type: string) {
  const $route = getRoute()

  return $route.params[type]
}
/**
 * 监听跳转后
 *
 * @param {void} fn 回调函数
 */
export function afterEach(fn: (to?: RouteLocationNormalized, from?: RouteLocationNormalized) => void) {
  router.afterEach((to, from) => {
    fn(to, from)
  })
}
/**
 *onBeforeRouteUpdate路由守卫
 *
 * @param {void} fn 回调函数
 */
export function beforeRouteUpdate(fn: (to?: RouteLocationNormalized, from?: RouteLocationNormalized) => void) {
  onBeforeRouteUpdate((to, from) => {
    fn(to, from)
  })
}
