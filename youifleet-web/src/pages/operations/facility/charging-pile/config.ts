import type { IBtnType, IFormItemType } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, SELECT } from 'youibot-plus'
import { isRequire, englishFirst } from '@/utils/form-rules'

export interface ITableRow {
  code: string
  level: number
  sourceSystem: string
  type: string
  agvCode: string
  description: string
  solution: string
  status: number
  ignoreStatus: number
  id: number
  missionWorkId: number
  deviceId: number
  agvMapName: string
  createTime: number
  updateTime: number
  closeTime: number
  deviceCode: string
}

//table操作栏
const tableOptions: IBtnType[] = [
  {
    name: 'message.del',
    type: 'link',
    slotName: 'stateBtn'
  },
  {
    name: 'message.menuList.button.reset',
    type: 'link',
    permission: 'reset'
  },
  {
    name: 'message.menuList.button.terminationDischarge',
    type: 'link',
    permission: 'terminationDischarge'
  }
]

//table 配置
const columns: TableColumnsType = [
  {
    // 序列号
    title: 'message.chargingPile.serialNumber',
    dataIndex: 'deviceId',
    width: 130,
    sorter: false
  },
  {
    // 设备类型
    title: 'message.chargingPile.deviceType',
    dataIndex: 'deviceType',
    width: 140,
    sorter: false
  },
  {
    // 设备型号
    title: 'message.chargingPile.equipmentType',
    dataIndex: 'deviceMode',
    width: 140,
    sorter: false
  },
  {
    // 网络状态
    title: 'message.chargingPile.networkState',
    key: 'networkStatus',
    width: 110,
    sorter: false
  },
  {
    // 运行状态
    title: 'message.chargingPile.runningState',
    key: 'workStatus',
    width: 110,
    sorter: false
  },
  {
    // 控制模式
    title: 'message.chargingPile.controlMode',
    key: 'controlMode',
    width: 110,
    sorter: false
  },
  {
    // 放电状态
    title: 'message.chargingPile.dischargeStatus',
    key: 'dischargeStatus',
    width: 110,
    sorter: false
  },
  {
    // 复位状态
    title: 'message.chargingPile.resetStatus',
    key: 'resetStatus',
    width: 110,
    sorter: false
  },
  {
    // 占用机器人
    title: 'message.chargingPile.occupancyRobot',
    dataIndex: 'occupyVehicleCode',
    width: 140,
    sorter: false
  },
  {
    // 充电点
    title: 'message.chargingPile.chargingPoint',
    dataIndex: 'markerCode',
    width: 140,
    sorter: false
  },
  {
    // 充电类型
    title: 'message.chargingPile.chargeTypes',
    dataIndex: 'chargeTypes',
    width: 120,
    sorter: false
  },
  {
    // 充电标定电压
    title: 'message.chargingPile.setVoltage',
    key: 'setVoltage',
    width: 140,
    sorter: false
  },
  {
    // 当前电压
    title: 'message.chargingPile.voltage',
    key: 'voltage',
    width: 110,
    sorter: false
  },
  {
    // 充电标定电流
    title: 'message.chargingPile.setCurrent',
    key: 'setCurrent',
    width: 140,
    sorter: false
  },
  {
    // 当前电流
    title: 'message.chargingPile.current',
    key: 'current',
    width: 110,
    sorter: false
  },
  {
    // 实时功率
    title: 'message.chargingPile.power',
    key: 'power',
    width: 110,
    sorter: false
  },
  {
    // DC模块温度
    title: 'message.chargingPile.dcTemp',
    key: 'dcTemp',
    width: 120,
    sorter: false
  },
  {
    // 刷头温度
    title: 'message.chargingPile.brushTemp',
    key: 'brushTemp',
    width: 110,
    sorter: false
  },
  {
    // 空气温度
    title: 'message.chargingPile.airTemp',
    key: 'airTemp',
    width: 110,
    sorter: false
  },
  {
    // 工作总时长
    title: 'message.chargingPile.durationTimes',
    key: 'durationTimes',
    width: 130,
    sorter: false
  },
  {
    // 支持电压范围
    title: 'message.chargingPile.voltageRange',
    dataIndex: 'voltageRange',
    width: 140,
    sorter: false
  },
  {
    // 固件版本
    title: 'message.chargingPile.softwareVer',
    dataIndex: 'softwareVer',
    width: 110,
    sorter: false
  },
  {
    // IP地址
    title: 'message.ip',
    dataIndex: 'ip',
    width: 100,
    sorter: false
  },
  {
    // 启用状态
    title: 'message.userSetting.enable',
    key: 'status',
    sorter: false,
    width: 110
  },
  { title: 'message.options', key: 'options', fixed: 'right' }
]
// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    // 编码
    is: INPUT,
    name: 'deviceId',
    label: 'message.chargingPile.serialNumber',
    allowClear: true
  },
  {
    // 网络状态
    is: SELECT,
    name: 'networkStatus',
    label: 'message.chargingPile.networkState',
    allowClear: true,
    isTranslated: true,

    options: [
      { label: 'message.chargingPile.networkStatusState.online', value: 'online' },
      { label: 'message.chargingPile.networkStatusState.offline', value: 'offline' }
    ]
  },
  {
    // 运行状态
    is: SELECT,
    name: 'workStatus',
    label: 'message.chargingPile.runningState',
    allowClear: true,
    isTranslated: true,

    options: [
      { label: 'message.chargingPile.workStatusState.normal', value: 'normal' },
      { label: 'message.chargingPile.workStatusState.abnormal', value: 'abnormal' }
    ]
  },
  {
    // 控制模式
    is: SELECT,
    name: 'controlMode',
    label: 'message.chargingPile.controlMode',
    allowClear: true,
    isTranslated: true,

    options: [
      { label: 'message.chargingPile.controlModeList.auto', value: 'auto' },
      { label: 'message.chargingPile.controlModeList.manual', value: 'manual' }
    ]
  },
  {
    // 放电状态
    is: SELECT,
    name: 'dischargeStatus',
    label: 'message.chargingPile.dischargeStatus',
    allowClear: true,
    isTranslated: true,

    options: [
      { label: 'message.chargingPile.dischargeStatusList.discharging', value: 'discharging' },
      { label: 'message.chargingPile.dischargeStatusList.no_discharge', value: 'no_discharge' }
    ]
  },
  {
    // 启用状态
    is: SELECT,
    name: 'status',
    label: 'message.userSetting.enable',
    allowClear: true,
    isTranslated: true,
    options: [
      { label: 'message.storageLocation.usageStatus.Disable', value: 'Disable' },
      { label: 'message.storageLocation.usageStatus.Enable', value: 'Enable' }
    ]
  }
]

//底部按钮
const btnList: IBtnType[] = [
  {
    name: 'message.menuList.button.reset',
    type: 'link',
    permission: 'reset'
  },
  {
    // 删除
    name: 'message.del',
    icon: 'iconfont icon-xiazai14',
    permission: 'del'
  },

  {
    name: 'message.menuList.button.terminationDischarge',
    type: 'link',
    permission: 'terminationDischarge'
  }
]

const formConfigList: IFormItemType[] = [
  {
    // 编码
    is: INPUT,
    name: 'code',
    label: 'message.encoding',
    rules: [isRequire(), englishFirst()]
  },
  {
    // 名称
    is: INPUT,
    name: 'name',
    label: 'message.name',
    rules: [isRequire()]
  }
]
export { columns, filterConfigList, tableOptions, btnList, formConfigList }
