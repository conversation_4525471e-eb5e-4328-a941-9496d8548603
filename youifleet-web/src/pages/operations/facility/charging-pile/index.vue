<template>
  <MyTable
    :filterConfigList="State.filterConfigList"
    :filterData="State.filterData"
    :columns="State.columns"
    :tableOptions="State.tableOptions"
    :pagination="pageState.pagination"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    :row-key="handleRowKey"
    @formChange="filterChange">
    <template #networkStatus="{ scope }">
      <a-tag v-if="scope.record.networkStatus" :color="isColor(scope.record.networkStatus)">
        {{ t('message.chargingPile.networkStatusState.' + scope.record.networkStatus) }}
      </a-tag>
    </template>
    <template #workStatus="{ scope }">
      <a-tag v-if="scope.record.workStatus" :color="isColor(scope.record.workStatus)">
        {{ t('message.chargingPile.workStatusState.' + scope.record.workStatus) }}
      </a-tag>
    </template>
    <template #controlMode="{ scope }">
      <a-tag v-if="scope.record.controlMode" :color="isColor(scope.record.controlMode)">
        {{ t('message.chargingPile.controlModeList.' + scope.record.controlMode) }}
      </a-tag>
    </template>
    <template #dischargeStatus="{ scope }">
      <a-tag v-if="scope.record.dischargeStatus" :color="isColor(scope.record.dischargeStatus)">
        {{ t('message.chargingPile.dischargeStatusList.' + scope.record.dischargeStatus) }}
      </a-tag>
    </template>
    <template #resetStatus="{ scope }">
      <span v-if="scope.record.resetStatus" :style="{ color: isColor(scope.record.resetStatus) }">
        {{ t('message.chargingPile.resetStatusList.' + scope.record.resetStatus) }}
      </span>
    </template>
    <template #setVoltage="{ scope }">
      <span v-if="scope.record.setVoltage !== null">{{ scope.record.setVoltage }}V</span>
    </template>
    <template #voltage="{ scope }">
      <span v-if="scope.record.voltage !== null">{{ scope.record.voltage }}V</span>
    </template>
    <template #setCurrent="{ scope }">
      <span v-if="scope.record.setCurrent !== null">{{ scope.record.setCurrent }}A</span>
    </template>
    <template #current="{ scope }">
      <span v-if="scope.record.current !== null">{{ scope.record.current }}A</span>
    </template>
    <template #power="{ scope }">
      <span v-if="scope.record.power !== null">{{ scope.record.power }}KW</span>
    </template>
    <template #dcTemp="{ scope }">
      <span v-if="scope.record.dcTemp !== null">{{ scope.record.dcTemp }}℃</span>
    </template>
    <template #brushTemp="{ scope }">
      <span v-if="scope.record.brushTemp !== null">{{ scope.record.brushTemp }}℃</span>
    </template>
    <template #airTemp="{ scope }">
      <span v-if="scope.record.airTemp !== null">{{ scope.record.airTemp }}℃</span>
    </template>
    <template #durationTimes="{ scope }">
      <span v-if="scope.record.onlineTime !== null">{{ setTimeStr(scope.record.onlineTime) }}</span>
    </template>
    <template #status="{ scope }">
      <a-tag v-if="scope.record.status" :color="isColor(scope.record.status)">
        {{ t('message.storageLocation.usageStatus.' + scope.record.status) }}
      </a-tag>
    </template>
    <!-- 自定义取消按钮 -->
    <template #stateBtn="{ scope }">
      <my-button
        v-if="scope.record.status === 'Disable'"
        :btnItem="{
          name: 'message.taskTypeArrangement.haveReleased',
          type: 'link',
          onClick: debounce(() => stateBtn(scope.record, 'Enable'), 200),
          permission: 'enabledState'
        }"></my-button>
      <my-button
        v-else
        :btnItem="{
          name: 'message.taskTypeArrangement.unpublish',
          type: 'link',
          onClick: debounce(() => stateBtn(scope.record, 'Disable'), 200),
          permission: 'enabledState'
        }"></my-button>
    </template>
  </MyTable>
</template>

<script setup lang="ts">
import type { ITableScope } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { useLocale } from 'youibot-plus'
import { columns, filterConfigList, btnList, tableOptions, formConfigList } from './config'
import type { ITableRow } from './config'
import { getOperateText, messageTip, secondsToHours } from '@/utils'
import { setTimeStr, downLoadFileOpen, debounce } from '@/utils/index'
import { permission } from '@/router'
import {
  getChargeStationPage,
  postChargeStationReset,
  postChargeStationStopCharge,
  postChargeStationDisable,
  postChargeStationEnable,
  deleteChargeStation
} from '@/api/chargeStation'
import { Modal } from 'ant-design-vue'
const apiUrl = `//${window.apiUrl}`
const { pageState, deleteItemConfirm, changeFilter, getData } = usePage({
  getPageFn: getChargeStationPage,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  deleteBatchFn: deleteChargeStation as unknown as (params: number[]) => Promise<any>,
  deleteSuccessFn: () => {
    State.selectedRowKeys = []
  }
})
const { t } = useLocale()
const visible = ref<boolean>(false)
const fileDialogVisible = ref<boolean>(false)
const State = reactive({
  columns,
  tableOptions,
  filterData: {},
  filterConfigList,
  formData: {},
  selectedRowKeys: [] as (string | number)[],
  formConfigList: formConfigList
})
const handleRowKey = (record: ITableRow) => {
  return record.deviceCode
}
//封装底部操作栏按钮
const btnHandleClick = (type: string, data: (number | string)[], value?: string) => {
  if (['reset', 'terminationDischarge'].includes(type)) {
    if (data.length <= 0) {
      messageTip(t(value ? value : 'message.selectTheDataYouWantToDelete'), 'error')
      return
    }
    switch (type) {
      // 复位
      case 'reset':
        postChargeStationResetFn(State.selectedRowKeys as unknown as string[])

        break
      case 'terminationDischarge': //终止放电
        postChargeStationStopChargeFn(State.selectedRowKeys as unknown as string[])

        break
    }
  } else {
    if (data.length <= 0) {
      messageTip(t(value ? value : 'message.selectTheDataYouWantToDelete'), 'error')
      return
    }
    switch (type) {
      // 删除
      case 'delete':
        deleteItemConfirm(State.selectedRowKeys as unknown as string | number[], t('message.delTips'))

        break
    }
  }
}

;(function btnListClickHandler() {
  const list = ['reset', 'delete', 'terminationDischarge']
  list.map((item, index) => {
    btnList[index].onClick = () => btnHandleClick(item, State.selectedRowKeys, btnList[index].message)
  })
})()

//封装右侧操作栏按钮
const operationHandleClick = (type: string, data: ITableScope<ITableRow>) => {
  if (!data) return
  const {
    record: { ...rest }
  } = data as ITableScope<ITableRow>
  switch (type) {
    // 编辑
    case 'reset':
      postChargeStationResetFn([rest.deviceCode])
      break
    case 'terminationDischarge':
      postChargeStationStopChargeFn([rest.deviceCode])
      break
  }
}

;(function operationClickHandler() {
  const list = ['', 'reset', 'terminationDischarge']
  list.map((item, index) => {
    tableOptions[index].onClick = (text?: object) => operationHandleClick(item, text as ITableScope<ITableRow>)
  })
})()

// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}
  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      const data = toRaw(filterData[item]) as unknown as string[]
      params[item] = data.join(',')
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}
const isColor = (state: string) => {
  switch (state) {
    case 'online':
    case 'normal':
    case 'auto':
    case 'Enable':
      return 'success'
    case 'offline':
    case 'abnormal':
    case 'manual':
    case 'Disable':
      return 'error'
    case 'discharging':
      return 'warning'

    default:
      return 'default'
  }
}

// 复位
const postChargeStationResetFn = (ids: string[]) => {
  Modal.confirm({
    content: t('message.chargingPile.whetherToPerformReset'),
    onOk() {
      postChargeStationReset(ids)
        .then(res => {
          getData()
          let errorLength = Object.keys(res.data.errorList).length
          messageTip(
            t('message.chargingPile.resetBatchPrompt', {
              successLength: Object.keys(res.data.successList).length,
              errorLength: errorLength
            }),
            errorLength ? 'error' : 'success'
          )
        })
        .catch(err => {
          getOperateText(t, {
            type: 'menuList.button.reset',
            result: 'error',
            reason: err.msg
          })
        })
    },
    onCancel() {}
  })
}
// 终止放电
const postChargeStationStopChargeFn = (ids: string[]) => {
  Modal.confirm({
    content: t('message.chargingPile.WhetherPerformTerminationDischarge'),
    onOk() {
      postChargeStationStopCharge(ids)
        .then(res => {
          getData()
          let errorLength = Object.keys(res.data.errorList).length
          messageTip(
            t('message.chargingPile.resetBatchPrompt', {
              successLength: Object.keys(res.data.successList).length,
              errorLength: errorLength
            }),
            errorLength ? 'error' : 'success'
          )
        })
        .catch(err => {
          getOperateText(t, {
            type: 'menuList.button.terminationDischarge',
            result: 'error',
            reason: err.msg
          })
        })
    },
    onCancel() {}
  })
}
const stateBtn = (record: ITableRow, type: string) => {
  if (type === 'Disable') {
    postChargeStationDisable([record.deviceCode])
      .then(res => {
        getData()
        getOperateText(t, {
          type: 'taskTypeArrangement.unpublish'
        })
      })
      .catch(err => {
        getOperateText(t, {
          type: 'taskTypeArrangement.unpublish',
          result: 'error',
          reason: err.msg
        })
      })
  } else {
    postChargeStationEnable([record.deviceCode])
      .then(res => {
        getData()
        getOperateText(t, {
          type: 'taskTypeArrangement.haveReleased'
        })
      })
      .catch(err => {
        getOperateText(t, {
          type: 'taskTypeArrangement.haveReleased',
          result: 'error',
          reason: err.msg
        })
      })
  }
}
onMounted(() => {
  getData()
})
</script>
