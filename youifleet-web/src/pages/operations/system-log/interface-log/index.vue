<template>
  <MyTable
    :filterConfigList="State.filterConfigList"
    :filterData="State.filterData"
    :columns="State.columns"
    :pagination="pageState.pagination"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    @formChange="filterChange">
    <template #operationTime="{ scope }">{{ setTimeStr(scope.record.operationTime) }}</template>
    <template #success="{ scope }">{{ t('message.log.successList.' + scope.record.success) }}</template>
    <template #description="{ scope }">
      <a-tooltip :overlayClassName="'operation-log-tooltip'" placement="bottomLeft">
        <template #title>
          <span>{{ scope.record.description }}</span>
        </template>
        <div class="table-cell-ellipsis">{{ scope.record.description }}</div>
      </a-tooltip>
    </template>
    <template #errorMsg="{ scope }">
      <a-tooltip :overlayClassName="'operation-log-tooltip'" placement="bottomLeft">
        <template #title>
          <span>{{ scope.record.errorMsg }}</span>
        </template>
        <div class="table-cell-ellipsis">{{ scope.record.errorMsg }}</div>
      </a-tooltip>
    </template>
    <template #wasteTime="{ scope }">
      <span>{{ scope.record.wasteTime }}ms</span>
    </template>
    <template #url="{ scope }">
      <a-tooltip :overlayClassName="'operation-log-tooltip'" placement="bottomLeft">
        <template #title>
          <span>{{ scope.record.url }}</span>
        </template>
        <div class="table-cell-ellipsis">{{ scope.record.url }}</div>
      </a-tooltip>
    </template>

    <template #paramsIn="{ scope }">
      <a-tooltip :overlayClassName="'operation-log-tooltip'" placement="bottomLeft">
        <template #title>
          <span>{{ scope.record.paramsIn }}</span>
        </template>
        <div class="table-cell-ellipsis">{{ scope.record.paramsIn }}</div>
      </a-tooltip>
    </template>
    <template #paramsOut="{ scope }">
      <a-tooltip :overlayClassName="'operation-log-tooltip'" placement="bottomLeft">
        <template #title>
          <span>{{ scope.record.paramsOut }}</span>
        </template>
        <div class="table-cell-ellipsis">{{ scope.record.paramsOut }}</div>
      </a-tooltip>
    </template>
  </MyTable>
</template>

<script setup lang="ts">
import type { ITableScope } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { useLocale } from 'youibot-plus'
import { columns, filterConfigList, btnList } from './config'
import type { ITableRow } from './config'
import { messageTip } from '@/utils'
import { setTimeStr, downLoadFileOpen } from '@/utils/index'
import { getLogOperationApiPage } from '@/api/log'
const { pageState, changeFilter, getData } = usePage({
  getPageFn: getLogOperationApiPage
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
})
const { t } = useLocale()
const visible = ref<boolean>(false)
btnList[0].onClick = () => {
  downLoadFileOpen(t, '/fleet/log/operation/apiExport', { ids: State.selectedRowKeys.join(','), ...pageState.filterData })
}

const State = reactive({
  columns,
  filterData: {},
  filterConfigList,
  formData: {},
  selectedRowKeys: [] as (string | number)[]
})
// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}
  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      const data = toRaw(filterData[item]) as unknown as string[]
      params[item] = data.join(',')
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}

onMounted(() => {
  getData()
})
</script>
<style lang="less">
.operation-log-tooltip {
  max-width: 1300px;
  max-height: 900px;
  overflow-y: auto;
}

.table-cell-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
}
</style>
