import type { IBtnType, IFormItemType } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, SELECT, RANGEPICKER } from 'youibot-plus'

export interface ITableRow {
  code: string
  level: number
  sourceSystem: string
  type: string
  agvCode: string
  description: string
  solution: string
  status: number
  ignoreStatus: number
  id: number
  missionWorkId: number
  deviceId: number
  agvMapName: string
  createTime: number
  updateTime: number
  closeTime: number
}

//table 配置
const columns: TableColumnsType = [
  {
    // 详情
    title: 'message.details',
    key: 'description',
    width: 200
  },
  {
    // 结果
    title: 'message.result',
    key: 'success',
    width: 100
  },
  {
    // 失败原因
    title: 'message.log.causeOfFailure',
    key: 'errorMsg'
  },
  {
    // 响应时长
    title: 'message.log.responseTime',
    key: 'wasteTime',
    width: 120
  },
  {
    // url
    title: 'message.log.url',
    key: 'url'
  },
  {
    // 请求信息
    title: 'message.log.requestInformation',
    key: 'paramsIn'
  },
  {
    // 返回信息
    title: 'message.log.returnInformation',
    key: 'paramsOut'
  },
  {
    // 创建时间
    title: 'message.createTime',
    key: 'operationTime',
    width: 200
  }
]
// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    // 详情
    is: INPUT,
    name: 'description',
    label: 'message.details',
    allowClear: true
  },
  {
    // 结果
    is: SELECT,
    name: 'success',
    label: 'message.result',
    allowClear: true,
    isTranslated: true,
    options: [
      {
        label: 'message.log.successList.true',
        value: true
      },
      {
        label: 'message.log.successList.false',
        value: false
      }
    ]
  },
  {
    // 失败原因
    is: INPUT,
    name: 'errorMsg',
    label: 'message.log.causeOfFailure',
    allowClear: true
  },

  {
    // 响应时长
    is: INPUT,
    name: 'wasteTime',
    label: 'message.log.responseTime',
    allowClear: true
  },
  {
    // URL
    is: INPUT,
    name: 'url',
    label: 'message.log.url',
    allowClear: true
  },
  {
    // 请求信息
    is: INPUT,
    name: 'paramsIn',
    label: 'message.log.requestInformation',
    allowClear: true
  },
  {
    // 返回信息
    is: INPUT,
    name: 'paramsOut',
    label: 'message.log.returnInformation',
    allowClear: true
  },
  {
    // 创建时间
    is: RANGEPICKER,
    name: 'operationTime',
    allowClear: true,
    label: 'message.createTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  }
]

//底部按钮
const btnList: IBtnType[] = [
  {
    name: 'message.multExport',
    permission: 'export'
  }
]

export { columns, filterConfigList, btnList }
