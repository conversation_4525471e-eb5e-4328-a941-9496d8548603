<template>
  <MyTable
    :filterConfigList="State.filterConfigList"
    :filterData="State.filterData"
    :columns="State.columns"
    :tableOptions="State.tableOptions"
    :pagination="pageState.pagination"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    @formChange="filterChange">
    <template #createDate="{ scope }">{{ setTimeStr(scope.record.createDate) }}</template>
    <template #lastTime="{ scope }">{{ setTimeStr(scope.record.lastTime) }}</template>
    <template #type="{ scope }">
      {{ t('message.log.typeList.' + scope.text.type) }}
      <!-- {{ typeStatus.find(item => item.value == scope.text.type)?.label }} -->
    </template>
    <template #data="{ scope }">
      <a-tooltip :overlayClassName="'operation-log-tooltip'" placement="bottomLeft">
        <template #title>
          <span>{{ scope.record.data }}</span>
        </template>
        <div class="table-cell-ellipsis">{{ scope.record.data }}</div>
      </a-tooltip>
    </template>
    <template #content="{ scope }">
      <a-tooltip :overlayClassName="'operation-log-tooltip'" placement="bottomLeft">
        <template #title>
          <span>{{ scope.record.content }}</span>
        </template>
        <div class="table-cell-ellipsis">{{ scope.record.content }}</div>
      </a-tooltip>
    </template>
    <template #message="{ scope }">
      <a-tooltip :overlayClassName="'operation-log-tooltip'" placement="bottomLeft">
        <template #title>
          <span>{{ scope.record.message }}</span>
        </template>
        <div class="table-cell-ellipsis">{{ scope.record.message }}</div>
      </a-tooltip>
    </template>
  </MyTable>
  <system-details-log v-if="visible" v-model:visible="visible"></system-details-log>
</template>

<script setup lang="ts">
import type { ITableScope } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { useLocale } from 'youibot-plus'
import { columns, filterConfigList, btnList, tableOptions, typeOptions } from './config'
import type { ITableRow } from './config'
import { setTimeStr, downLoadFileOpen } from '@/utils/index'
import { getLogSysPage } from '@/api/log'
const { pageState, changeFilter, getData } = usePage({
  getPageFn: getLogSysPage
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
})
const { t } = useLocale()
const visible = ref<boolean>(false)

//封装底部操作栏按钮
const btnHandleClick = (type: string, data: string[], value?: string) => {
  switch (type) {
    // 新增

    case 'export': //导出
      downLoadFileOpen(t, '/fleet/log/sys/export', { ids: State.selectedRowKeys.join(','), ...pageState.filterData })
      break
    case 'downloadDetailedLog':
      visible.value = true
      break
  }
}

;(function btnListClickHandler() {
  const list = ['export', 'downloadDetailedLog']
  list.map((item, index) => {
    btnList[index].onClick = () => btnHandleClick(item, State.selectedRowKeys as string[], btnList[index].message)
  })
})()

tableOptions[0].onClick = (text?: object) => {
  if (text) {
    const {
      record: { ...rest }
    } = text as ITableScope<ITableRow>
    State.formData = { ...rest }
    visible.value = true
  }
}

const State = reactive({
  columns,
  tableOptions,
  filterData: {},
  filterConfigList,
  formData: {},
  selectedRowKeys: [] as (string | number)[]
})
// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}
  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      const data = toRaw(filterData[item]) as unknown as string[]
      params[item] = data.join(',')
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}

onMounted(() => {
  getData()
})
</script>
<style lang="less">
.operation-log-tooltip {
  max-width: 1300px;
  max-height: 900px;
  overflow-y: auto;
}

.table-cell-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
}
</style>
