import type { IBtnType, IFormItemType } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, SELECT, RANGEPICKER } from 'youibot-plus'
import { getLogSysModuleSelect } from '@/api/log'
import permission from '@/utils/directives/permission'

export interface ITableRow {
  code: string
  level: number
  sourceSystem: string
  type: string
  agvCode: string
  description: string
  solution: string
  status: number
  ignoreStatus: number
  id: number
  missionWorkId: number
  deviceId: number
  agvMapName: string
  createTime: number
  updateTime: number
  closeTime: number
}

//table操作栏
const tableOptions: IBtnType[] = [
  {
    name: 'message.edit',
    type: 'link',
    permission: 'edit'
  }
]
const typeOptions = [
  {
    label: 'message.log.typeList.Error',
    value: 'Error'
  },
  {
    label: 'message.log.typeList.Running',
    value: 'Running'
  },
  {
    label: 'message.log.typeList.Warning',
    value: 'Warning'
  }
]
const moduleList = ref<{ label: string; value: string }[]>([])

//table 配置
const columns: TableColumnsType = [
  {
    title: 'message.log.category',
    dataIndex: 'module',
    width: 120
  },
  {
    title: 'message.notice.level',
    key: 'type',
    width: 120
  },
  {
    title: 'message.describe',
    key: 'content',
    width: 300
  },
  // 报文
  {
    title: 'message.log.data',
    key: 'data',
    width: 250
  },
  // 报文
  {
    title: 'message.log.message',
    key: 'message',
    width: 250
  },
  {
    title: 'message.vehicle',
    dataIndex: 'vehicleCodes',
    width: 160
  },
  {
    title: 'message.notice.quest',
    dataIndex: 'taskNos',
    width: 250
  },
  {
    title: 'message.createTime',
    key: 'createDate',
    width: 200
  },
  {
    title: 'message.log.lastTime',
    key: 'lastTime',
    width: 200
  }
]
// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    is: SELECT,
    name: 'module',
    label: 'message.log.category',
    allowClear: true,
    isTranslated: true,
    options: moduleList.value
  },
  {
    is: SELECT,
    name: 'type',
    label: 'message.notice.level',
    allowClear: true,
    isTranslated: true,
    options: typeOptions
  },

  {
    is: INPUT,
    name: 'content',
    label: 'message.describe',
    allowClear: true
  },

  {
    is: INPUT,
    name: 'data',
    label: 'message.log.data',
    allowClear: true
  },
  {
    is: INPUT,
    name: 'message',
    label: 'message.log.message',
    allowClear: true
  },
  {
    is: INPUT,
    name: 'vehicleCodes',
    label: 'message.vehicle',
    allowClear: true
  },
  {
    is: INPUT,
    name: 'taskNos',
    label: 'message.notice.quest',
    allowClear: true
  },
  {
    is: RANGEPICKER,
    name: 'createDate',
    allowClear: true,
    label: 'message.createTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  },
  {
    is: RANGEPICKER,
    name: 'lastTime',
    allowClear: true,
    label: 'message.log.lastTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  }
]

//底部按钮
const btnList: IBtnType[] = [
  {
    name: 'message.multExport',
    permission: 'export'
  },
  {
    name: 'message.log.downloadDetailsLog',
    permission: 'downloadDetailedLog'
  }
]

const getLogSysModuleSelectFn = () => {
  getLogSysModuleSelect().then((res: { data: string[] }) => {
    res.data.forEach((item: string) => {
      moduleList.value?.push({
        label: item,
        value: item
      })
    })
  })
}
getLogSysModuleSelectFn()
export { columns, filterConfigList, tableOptions, btnList, typeOptions }
