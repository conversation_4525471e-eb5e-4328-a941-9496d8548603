import type { IBtnType, IFormItemType } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, SELECT, RANGEPICKER } from 'youibot-plus'

export interface ITableRow {
  code: string
  level: number
  sourceSystem: string
  type: string
  agvCode: string
  description: string
  solution: string
  status: number
  ignoreStatus: number
  id: number
  missionWorkId: number
  deviceId: number
  agvMapName: string
  createTime: number
  updateTime: number
  closeTime: number
}

//table操作栏
const tableOptions: IBtnType[] = [
  {
    name: 'message.edit',
    type: 'link',
    permission: 'edit'
  }
]

//table 配置
const columns: TableColumnsType = [
  {
    // 用户
    title: 'message.log.user',
    dataIndex: 'operator',
    width: 120
  },
  {
    // 操作
    title: 'message.options',
    key: 'description',
    width: 180
  },
  {
    // 结果
    title: 'message.result',
    key: 'success',
    width: 100
  },
  {
    // 失败原因
    title: 'message.log.causeOfFailure',
    key: 'errorMsg'
  },
  {
    // 响应时长
    title: 'message.log.responseTime',
    key: 'wasteTime',
    width: 120
  },
  {
    // 客户端ip地址
    title: 'message.log.ipAddressOfTheClient',
    dataIndex: 'ip',
    width: 150
  },
  {
    // 请求信息
    title: 'message.log.requestInformation',
    key: 'paramsIn'
  },
  {
    // 返回信息
    title: 'message.log.returnInformation',
    key: 'paramsOut'
  },
  {
    // 操作时间
    title: 'message.log.operatingTime',
    key: 'operationTime',
    width: 200
  }
]
// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    // 用户
    is: INPUT,
    name: 'operator',
    label: 'message.log.user',
    allowClear: true
  },
  {
    // 操作
    is: INPUT,
    name: 'description',
    label: 'message.options',
    allowClear: true
  },
  {
    // 结果
    is: SELECT,
    name: 'success',
    label: 'message.result',
    allowClear: true,
    isTranslated: true,
    options: [
      {
        label: 'message.log.successList.true',
        value: true
      },
      {
        label: 'message.log.successList.false',
        value: false
      }
    ]
  },
  {
    // 失败原因
    is: INPUT,
    name: 'errorMsg',
    label: 'message.log.causeOfFailure',
    allowClear: true
  },

  {
    // 响应时长
    is: INPUT,
    name: 'wasteTime',
    label: 'message.log.responseTime',
    allowClear: true
  },
  {
    // 客户端ip地址
    is: INPUT,
    name: 'ip',
    label: 'message.log.ipAddressOfTheClient',
    allowClear: true
  },
  {
    // 请求信息
    is: INPUT,
    name: 'paramsIn',
    label: 'message.log.requestInformation',
    allowClear: true
  },
  {
    // 返回信息
    is: INPUT,
    name: 'paramsOut',
    label: 'message.log.returnInformation',
    allowClear: true
  },
  {
    // 操作时间
    is: RANGEPICKER,
    name: 'operationTime',
    allowClear: true,
    label: 'message.log.operatingTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  }
]

//底部按钮
const btnList: IBtnType[] = [
  {
    name: 'message.multExport',
    permission: 'export'
  }
]

export { columns, filterConfigList, tableOptions, btnList }
