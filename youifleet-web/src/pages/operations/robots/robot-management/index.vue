<template>
  <MyTable
    ref="tableRef"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :pagination="pageState.pagination"
    :filterData="State.filterData"
    :filterConfigList="State.filterConfigList"
    :columns="State.columns"
    :tableOptions="State.tableOptions"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    :row-key="handleRowKey"
    @formChange="filterChange">
    <!-- 控制模式 -->
    <template #odom="{ scope }">{{ scope.record.odom ? scope.record.odom.toFixed(2) : 0 }}m</template>
    <!-- 控制模式 -->
    <template #controlMode="{ scope }">
      <a-tag v-if="scope.record.controlMode" :color="isTagColor(scope.record.controlMode)">
        {{ t('message.robotManage.controlModeList.' + scope.record.controlMode) }}
      </a-tag>
    </template>
    <!-- 连接 -->
    <template #connectStatus="{ scope }">
      <a-tag v-if="scope.record.connectStatus" :color="isTagColor(scope.record.connectStatus)">
        {{ t('message.robotManage.connectStatusList.' + scope.record.connectStatus) }}
      </a-tag>
    </template>
    <!-- 软急停 -->
    <template #softEmerStopStatus="{ scope }">
      <a-tag v-if="scope.record.softEmerStopStatus" :color="isTagColor(scope.record.softEmerStopStatus)">
        {{ t('message.robotManage.softEmerStopStatusList.' + scope.record.softEmerStopStatus) }}
      </a-tag>
    </template>
    <!-- 定位 -->
    <template #locatedStatus="{ scope }">
      <a-tag v-if="scope.record.locatedStatus" :color="isTagColor(scope.record.locatedStatus)">
        {{ t('message.robotManage.locatedStatusList.' + scope.record.locatedStatus) }}
      </a-tag>
    </template>
    <!-- 工作状态 -->
    <template #workStatus="{ scope }">
      <a-tag v-if="scope.record.workStatus" :color="isTagColor(scope.record.workStatus)">
        {{ t('message.robotManage.workStatusList.' + scope.record.workStatus) }}
      </a-tag>
    </template>
    <!-- 异常 -->
    <template #abnormalStatus="{ scope }">
      <a-tag v-if="scope.record.abnormalStatus" :color="isTagColor(scope.record.abnormalStatus)">
        {{ t('message.robotManage.abnormalStatusList.' + scope.record.abnormalStatus) }}
      </a-tag>
    </template>
    <!-- 调度模式 -->
    <template #scheduleMode="{ scope }">
      <a-tag v-if="scope.record.scheduleMode" :color="isTagColor(scope.record.scheduleMode)">
        {{ t('message.robotManage.scheduleModeList.' + scope.record.scheduleMode) }}
      </a-tag>
    </template>
    <template #rate="{ scope }">
      <div v-if="scope.record.rate !== null && scope.record.rate !== undefined">
        <my-battery :isCharging="scope.record.charging" :batteryNum="scope.record.rate"></my-battery>
      </div>
    </template>

    <template #schedule="{ scope }">
      <my-button :btnItem="isScheduleBut('schedule', scope)" :data="scope"></my-button>
    </template>
    <template #softEmergencyStop="{ scope }">
      <my-button :btnItem="isScheduleBut('softEmergencyStop', scope)" :data="scope"></my-button>
    </template>
    <template #controlModeBut="{ scope }">
      <my-button :btnItem="isScheduleBut('controlMode', scope)" :data="scope"></my-button>
    </template>
  </MyTable>
  <MyForm
    :visible="visible"
    :title="t('message.edit')"
    :formConfigList="State.formConfigList"
    :formData="State.formData"
    @close="onClose"
    @submit="submitSetUp"></MyForm>
  <AssignMap :visible="visibleMap" @close="closeAssignMap" @submit="submitAssignMap" />
  <RobotManagementStatistic
    v-if="visibleStatistic"
    :visible="visibleStatistic"
    :statisticVehicleCode="statisticVehicleCode"
    @cancelStatistic="visibleStatistic = false" />
  <!--设定分组/ 设定类型 -->
  <my-modal
    v-model:visible="visibleGroup"
    :title="formState.type === 1 ? t('message.robotManage.setGroup') : t('message.robotManage.setType')"
    @confirm="groupOrTypeConfirm">
    <a-row justify="center">
      <a-col :span="16">
        <a-form :model="formState" ref="formRef">
          <!-- 机器人分组 -->
          <a-form-item v-if="formState.type === 1" :label="t('message.robotManage.vehicleGroupName')" name="vehicleGroupId">
            <a-select ref="select" v-model:value="formState.vehicleGroupId" style="width: 200px" allowClear>
              <a-select-option v-for="(item, index) in State.vehicleGroupList" :key="index" :title="item.name" :value="item.id">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <!-- 机器人类型 -->
          <a-form-item v-else :label="t('message.robotManage.vehicleTypeName')" name="vehicleTypeId">
            <a-select ref="select" v-model:value="formState.vehicleTypeId" style="width: 200px" allowClear>
              <a-select-option v-for="(item, index) in State.vehicleTypeList" :title="item.name" :key="index" :value="item.id">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
  </my-modal>
</template>

<script setup lang="ts">
import type { IFormRules, ITableScope } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { useLocale, deepClone } from 'youibot-plus'
import { columns, filterConfigList, btnList, tableOptions, formConfigList, isTagColor } from './config'
import type { ITableRow } from './config'
import { getOperateText, messageTip } from '@/utils'
import {
  getVehiclePage,
  deleteVehicle,
  postVehicleSoftStopOpen,
  postVehicleSoftStopClose,
  postVehicleRestartVehicleCode,
  postVehicleControlsManualMode,
  postVehicleControlsAutoMode,
  postVehicleSchedulerAutoMode,
  postVehicleSchedulerManualMode,
  getVehicleType,
  getVehicleGroup,
  postVehicleMapAppointMapCode,
  putVehicleUpdate,
  putVehicleResourceClear,
  postVehicleShutdownVehicleCode,
  putVehicleUpdateGroupBatch,
  putVehicleUpdateTypeBatch,
  putVehicleUpdateVehicleName,
  postVehicleCloseSoundLightAlarmVehicleCode
} from '@/api/vehicle'
import AssignMap from '@/components/robotManage/assignMap/index.vue'
import { getMapVehicleMaps } from '@/api/map'
import { IBtnType } from 'youibot-plus'
import { IBaseBtnType } from '@/hooks/use-table'
import { toNext } from '@/router'
import { FormInstance, Modal } from 'ant-design-vue'
import { isPureNumber } from '@/utils/form-rules'
interface IRowKey {
  id: string
  vehicleCode: string
}

const { pageState, deleteItemConfirm, changeFilter, getData } = usePage({
  getPageFn: getVehiclePage,
  deleteBatchFn: deleteVehicle,
  deleteSuccessFn: () => {
    State.selectedRowKeys = []
  }
})

const { t } = useLocale()
const tableRef = ref()
const State = reactive({
  columns,
  tableOptions,
  filterData: {},
  filterConfigList,
  formConfigList,
  formData: {},
  selectedRowKeys: [] as string[],
  assignMapIds: [] as string[],
  vehicleTypeList: [] as { id: string; name: string }[],
  vehicleGroupList: [] as { id: string; name: string }[]
})
const formState = reactive({
  vehicleCodes: [] as string[],
  vehicleTypeId: '',
  vehicleGroupId: '',
  type: 1 //1设置类型 2设置分组
})
const formRef = ref<FormInstance>()
const visibleGroup = ref<boolean>(false)
const visible = ref<boolean>(false)
const visibleMap = ref<boolean>(false)
const visibleStatistic = ref<boolean>(false)
const statisticVehicleCode = ref()

const rules: { [key: string]: IFormRules[] } = {
  name: [isPureNumber()]
}
Object.keys(rules).forEach((item, index) => {
  State.formConfigList[index].rules = rules[item]
})
const handleRowKey = (record: IRowKey) => {
  const { vehicleCode, id } = record
  return JSON.stringify({ vehicleCode, id })
}

const onClose = () => {
  visible.value = false
}
const closeAssignMap = () => {
  visibleMap.value = false
}
const submitAssignMap = (mapCode: string) => {
  postVehicleMapAppointMapCode(JSON.stringify(State.assignMapIds), { mapCode })
    .then(res => {
      refreshAfterRequest('robotManage.assignMap')
      visibleMap.value = false
      getData()
    })
    .catch(err => {
      visibleMap.value = false
      messageTip(err.msg, 'error')
    })
}

//封装底部操作栏按钮
const btnHandleClick = (type: string, data: string[], btnConfig: IBtnType) => {
  if (data.length <= 0) {
    messageTip(t(btnConfig.message ? btnConfig.message : 'message.selectTheDataYouWantToDelete'), 'error')
    return
  }
  let dataList = [] as IRowKey[]
  for (let i = 0; i < data.length; i++) {
    dataList.push(JSON.parse(data[i]))
  }
  const _dataIds: string[] = []
  const _dataCodes: string[] = []
  dataList.forEach(item => {
    _dataIds.push(item.id)
    _dataCodes.push(item.vehicleCode)
  })
  const _data = JSON.stringify(_dataCodes)
  switch (type) {
    // 指定地图
    case 'assignMap':
      State.assignMapIds = _dataCodes
      visibleMap.value = true
      break
    // 删除
    case 'delete':
      deleteItemConfirm(_dataCodes as unknown as number[], t('message.delTips'))
      break
    // 手动控制
    case 'manualControl':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleControlsManualMode(_data)
            .then(res => {
              refreshAfterRequest('robotManage.controlModeList.Manual')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.controlModeList.Manual',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 自动控制
    case 'autoControl':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleControlsAutoMode(_data)
            .then(res => {
              refreshAfterRequest('robotManage.controlModeList.Auto')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.controlModeList.Auto',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 开启软急停
    case 'openSoftEmergencyStop':
      postVehicleSoftStopOpen(_data)
        .then(res => {
          refreshAfterRequest('robotManage.softEmerStopStatusListBut.Open')
        })
        .catch(err => {
          getOperateText(t, {
            type: 'robotManage.softEmerStopStatusListBut.Open',
            result: 'error',
            reason: err.msg
          })
        })
      break
    // 关闭软急停
    case 'closeSoftEmergencyStop':
      postVehicleSoftStopClose(_data)
        .then(res => {
          refreshAfterRequest('robotManage.softEmerStopStatusListBut.Close')
        })
        .catch(err => {
          getOperateText(t, {
            type: 'robotManage.softEmerStopStatusListBut.Close',
            result: 'error',
            reason: err.msg
          })
        })
      break
    // 手动调度
    case 'manualSchedule':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleSchedulerManualMode(_data)
            .then(res => {
              refreshAfterRequest('robotManage.scheduleModeList.ManualSchedule')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.scheduleModeList.ManualSchedule',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 自动调度
    case 'autoSchedule':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleSchedulerAutoMode(_data)
            .then(res => {
              refreshAfterRequest('robotManage.scheduleModeList.AutoSchedule')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.scheduleModeList.AutoSchedule',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 设定类型
    case 'setType':
      formState.vehicleCodes = _dataCodes
      formState.vehicleGroupId = ''
      formState.vehicleTypeId = ''
      getRobotTypeList(1)
      formState.type = 2
      visibleGroup.value = true

      break
    //设定分组
    case 'setGroup':
      formState.vehicleCodes = _dataCodes
      formState.vehicleGroupId = ''
      formState.vehicleTypeId = ''
      getRobotGroupList(1)
      formState.type = 1
      visibleGroup.value = true
      break
    default:
      break
  }
}

;(function btnListClickHandler() {
  const list = [
    'assignMap',
    'delete',
    'manualControl',
    'autoControl',
    // 'openSoftEmergencyStop',
    // 'closeSoftEmergencyStop',
    'manualSchedule',
    'autoSchedule',
    'setType',
    'setGroup'
  ]
  list.map((item, index) => {
    btnList[index].onClick = () => btnHandleClick(item, State.selectedRowKeys, btnList[index])
  })
})()
//封装右侧操作栏按钮
const operationHandleClick = (type: string, data: ITableScope<ITableRow>) => {
  if (!data) return

  const { record } = data
  const codeList = JSON.stringify([record.vehicleCode])
  switch (type) {
    case 'edit':
      getRobotTypeList(1)
      getRobotGroupList(1)
      State.formData = deepClone(record)
      visible.value = true

      break
    // 重启
    case 'restart':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleRestartVehicleCode({}, { vehicleCode: record.vehicleCode })
            .then(res => {
              refreshAfterRequest('robotManage.restart')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.restart',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 指定地图
    case 'assignMap':
      State.assignMapIds = [record.vehicleCode]
      visibleMap.value = true
      break
    // 关闭软急停
    case 'closeSoftEmergencyStop':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleSoftStopClose(codeList)
            .then(res => {
              refreshAfterRequest('robotManage.softEmerStopStatusListBut.Close')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.softEmerStopStatusListBut.Close',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 开启软急停
    case 'openSoftEmergencyStop':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleSoftStopOpen(codeList)
            .then(res => {
              refreshAfterRequest('robotManage.softEmerStopStatusListBut.Open')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.softEmerStopStatusListBut.Open',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 手动调度
    case 'manualSchedule':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleSchedulerManualMode(codeList)
            .then(res => {
              refreshAfterRequest('robotManage.scheduleModeList.ManualSchedule')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.scheduleModeList.ManualSchedule',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 自动调度
    case 'autoSchedule':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleSchedulerAutoMode(codeList)
            .then(res => {
              refreshAfterRequest('robotManage.scheduleModeList.AutoSchedule')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.scheduleModeList.AutoSchedule',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 清理
    case 'clear':
      putVehicleResourceClear(codeList)
        .then(res => {
          refreshAfterRequest('map.offSite')
        })
        .catch(err => {
          getOperateText(t, {
            type: 'map.offSite',
            result: 'error',
            reason: err.msg
          })
        })
      break
    // 打开统计弹窗
    case 'statistic':
      statisticVehicleCode.value = record.vehicleCode
      visibleStatistic.value = true
      break
    // 手动控制
    case 'manualControl':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleControlsManualMode(codeList)
            .then(res => {
              refreshAfterRequest('robotManage.controlModeList.Manual')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.controlModeList.Manual',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 自动控制
    case 'autoControl':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleControlsAutoMode(codeList)
            .then(res => {
              refreshAfterRequest('robotManage.controlModeList.Auto')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.controlModeList.Auto',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    // 历史任务
    case 'historicalTask':
      toNext({
        path: `/operations/tasks/task-management`,
        query: { vehicleCodes: record.vehicleCode, jst: 'jst' }
      })
      break
    // 关机
    case 'powerOff':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleShutdownVehicleCode({}, { vehicleCode: record.vehicleCode })
            .then(res => {
              refreshAfterRequest('robotManage.powerOff')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'robotManage.powerOff',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    //停止警报
    case 'stopAlarm':
      Modal.confirm({
        zIndex: 3000,
        content: t('message.secondaryConfirmationPrompt'),
        onOk() {
          postVehicleCloseSoundLightAlarmVehicleCode({}, { vehicleCode: record.vehicleCode })
            .then(res => {
              refreshAfterRequest('menuList.button.stopAlarm')
            })
            .catch(err => {
              getOperateText(t, {
                type: 'menuList.button.stopAlarm',
                result: 'error',
                reason: err.msg
              })
            })
        },
        onCancel() {}
      })

      break
    default:
      break
  }
}

;(function operationClickHandler() {
  const list = [
    'statistic',
    'clear',
    'edit',
    'assignMap',
    'manualControl',
    'manualSchedule',
    'stopAlarm',

    'closeSoftEmergencyStop',
    'restart',
    'powerOff',
    'historicalTask'
  ]
  list.map((item, index) => {
    tableOptions[index].onClick = (text?: object) => {
      operationHandleClick(item, text as ITableScope<ITableRow>)
    }
  })
})()

// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}

  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      params.startTime = filterData[item][0]
      params.endTime = filterData[item][1]
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}

const submitSetUp = (data: ITableRow) => {
  putVehicleUpdateVehicleName({
    ...data
  })
    .then(() => {
      onClose()
      filterChange(State.filterData)
      getOperateText(t, {
        type: 'update'
      })
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg
      })
    })
}

const getRobotTypeList = async (type?: number) => {
  const { data } = await getVehicleType()
  State.vehicleTypeList = data

  State.filterConfigList.map(item => {
    if (item.name === 'vehicleTypeId') {
      item.options = data
    }
  })
  tableRef.value.generateNewConfigList()
}
const getRobotGroupList = async (type?: number) => {
  const { data } = await getVehicleGroup({})
  State.vehicleGroupList = data

  State.filterConfigList.map(item => {
    if (item.name === 'vehicleGroupId') {
      item.options = data
    }
  })
  tableRef.value.generateNewConfigList()
}

const getMapList = () => {
  getMapVehicleMaps().then(res => {
    const { data } = res
    State.filterConfigList.map(item => {
      if (item.name === 'vehicleMapCode') {
        item.options = data
      }
    })
    tableRef.value.generateNewConfigList()
  })
}
// 操作栏按钮 根据状态显示
const isScheduleBut = (type: string, row: ITableScope<ITableRow>): IBaseBtnType => {
  const { record } = row
  let data = {
    name: '',
    type: '',
    position: '',
    permission: '',
    onClick: () => {}
  }

  switch (type) {
    case 'schedule':
      if (record.scheduleMode == 'ManualSchedule') {
        data = {
          name: 'message.robotManage.scheduleModeList.AutoSchedule',
          type: 'link',
          position: 'more',
          permission: 'Schedule',
          onClick: () => operationHandleClick('autoSchedule', row)
        }
      } else {
        data = {
          name: 'message.robotManage.scheduleModeList.ManualSchedule',
          type: 'link',
          position: 'more',
          permission: 'Schedule',
          onClick: () => operationHandleClick('manualSchedule', row)
        }
      }
      break
    case 'softEmergencyStop':
      if (record.softEmerStopStatus == 'Open') {
        data = {
          name: 'message.robotManage.softEmerStopStatusListBut.Close',
          type: 'link',
          position: 'more',
          permission: 'SoftEmergencyStop',
          onClick: () => operationHandleClick('closeSoftEmergencyStop', row)
        }
      } else {
        data = {
          name: 'message.robotManage.softEmerStopStatusListBut.Open',
          type: 'link',
          position: 'more',
          permission: 'SoftEmergencyStop',
          onClick: () => operationHandleClick('openSoftEmergencyStop', row)
        }
      }
      break
    case 'controlMode':
      if (record.controlMode === 'Manual') {
        data = {
          name: 'message.robotManage.controlModeList.Auto',
          type: 'link',
          position: 'more',
          permission: 'controlMode',
          onClick: () => operationHandleClick('autoControl', row)
        }
      } else {
        data = {
          name: 'message.robotManage.controlModeList.Manual',
          type: 'link',
          position: 'more',
          permission: 'controlMode',
          onClick: () => operationHandleClick('manualControl', row)
        }
      }
    default:
      break
  }
  return data
}
//请求 操作后刷新数据
const refreshAfterRequest = (type: string) => {
  getOperateText(t, {
    type
  })
  let timer = setTimeout(() => {
    filterChange(State.filterData)
    clearTimeout(timer)
  }, 1000)
}

const groupOrTypeConfirm = () => {
  formRef.value?.validate().then(() => {
    if (formState.type === 1) {
      putVehicleUpdateGroupBatch({
        vehicleCodes: formState.vehicleCodes,
        vehicleGroupId: formState.vehicleGroupId as unknown as number
      })
        .then(res => {
          visibleGroup.value = false
          getOperateText(t, {
            type: 'update',
            isBatch: true
          })
          getData()
        })
        .catch(err => {
          getOperateText(t, {
            type: 'update',
            result: 'error',
            reason: err.msg,
            isBatch: true
          })
        })
    } else {
      putVehicleUpdateTypeBatch({
        vehicleCodes: formState.vehicleCodes,
        vehicleTypeId: formState.vehicleTypeId as unknown as number
      })
        .then(res => {
          visibleGroup.value = false
          getOperateText(t, {
            type: 'update',
            isBatch: true
          })
          getData()
        })
        .catch(err => {
          getOperateText(t, {
            type: 'update',
            result: 'error',
            reason: err.msg,
            isBatch: true
          })
        })
    }
  })
}
onMounted(() => {
  getData()
  getRobotTypeList()
  getRobotGroupList()
  getMapList()
})
</script>
<style lang="less" scoped>
.color-red {
  color: red;
}
</style>
