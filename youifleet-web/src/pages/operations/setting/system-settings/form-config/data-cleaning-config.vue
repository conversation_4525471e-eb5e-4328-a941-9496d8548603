<template>
  <a-form :model="formState" ref="formRef" name="basic">
    <template v-for="(item, index) in formState.systemConfig">
      <div v-if="item.propertyKey === 'pushCycle'" class="form-list-border">
        <div class="form-list form-list-padding">
          <div class="form-list-title">
            <div>{{ item.title }}</div>
            <div class="form-list-title-describe">{{ item.remark }}</div>
          </div>
          <div>
            <a-form-item :style="{ width: '101px' }" :name="['systemConfig', index, 'propertyValue']" :rules="[isRequire()]">
              <a-input-number
                v-model:value="item.propertyValue"
                style="width: 101px"
                :controls="false"
                :min="isMin(item.propertyKey)"
                :max="isMax(item.propertyKey)"
                @blur="propertyValueInput(item)"
                @pressEnter="evetBlur" />
              <div class="form-list-item-unit">{{ isUnit(item.propertyKey) }}</div>
            </a-form-item>
          </div>
        </div>
        <div class="form-list form-list-operate form-list-padding">
          <div class="form-list-label" style="margin-right: 15px">{{ formState.systemConfig[index + 1].remark }}</div>
          <a-form-item class="form-list-item">
            <a-input
              v-model:value="formState.systemConfig[index + 1].propertyValue"
              :controls="false"
              style="width: 250px"
              @blur="propertyValueInput(formState.systemConfig[index + 1])"
              @pressEnter="evetBlur" />
          </a-form-item>
        </div>
      </div>
      <div
        v-else-if="!['vehicleStatusPushUrl', 'noticePushLanguageType', 'noticePushLanguage'].includes(item.propertyKey)"
        class="form-list form-list-border">
        <div class="form-list-title">
          <div>{{ item.title }}</div>
          <div class="form-list-title-describe">{{ item.remark }}</div>
        </div>

        <div class="form-list-item">
          <a-form-item
            :style="{ width: item.valueType === 'String' ? '100%' : '101px' }"
            :name="['systemConfig', index, 'propertyValue']"
            :rules="item.propertyKey == 'noticePushUrl' ? [] : [isRequire()]">
            <a-input
              v-if="item.valueType === 'String'"
              v-model:value="item.propertyValue"
              :controls="false"
              style="width: 250px"
              @blur="propertyValueInput(item)"
              @pressEnter="evetBlur" />
            <template v-else>
              <a-input-number
                v-model:value="item.propertyValue"
                style="width: 100%"
                :controls="false"
                :min="isMin(item.propertyKey)"
                :max="isMax(item.propertyKey)"
                @blur="propertyValueInput(item)"
                @pressEnter="evetBlur" />
              <div class="form-list-item-unit">{{ isUnit(item.propertyKey) }}</div>
            </template>
          </a-form-item>
        </div>
      </div>
      <div v-else-if="item.propertyKey === 'noticePushLanguageType'" class="form-list form-list-border form-list-padding">
        <div class="form-list-title">
          <div>{{ item.title }}</div>
          <div class="form-list-title-describe">{{ item.remark }}</div>
        </div>
        <div class="form-list-input">
          <a-form-item :name="['systemConfig', index, 'propertyValue']" :rules="[isRequire()]">
            <a-select
              v-model:value="item.propertyValue"
              :mode="''"
              :style="'width:120px'"
              :options="isOptions(item.propertyKey)"
              @change="propertyValueInput(item)"></a-select>
            <a-select
              v-if="item.propertyValue === '2'"
              v-model:value="formState.systemConfig[index + 1].propertyValue"
              :mode="''"
              :fieldNames="{ label: 'name', value: 'code' }"
              :style="'margin-left:12px;width:120px'"
              :options="languageList"
              @change="propertyValueInput(formState.systemConfig[index + 1])"></a-select>
          </a-form-item>
        </div>
      </div>
    </template>
  </a-form>
</template>
<script lang="ts" setup>
import { FormState } from '@/pages/operations/setting/system-settings/config'
import { PropType } from 'vue'
import { isRequire } from '@/utils/form-rules'
import { FormInstance } from 'ant-design-vue'
import { debounce, evetBlur } from '@/utils'
import { useLocale } from 'youibot-plus'
import { getLanguage } from '@/api/language'
interface LanguageList {
  name: string
  code: string
  id: string
  inUse: boolean
}
const { t } = useLocale()
const props = defineProps({
  systemConfig: { type: Object as PropType<FormState[]>, default: () => ({}) }
})
const formRef = ref<FormInstance>()
const emit = defineEmits(['refresh', 'submitUpdate'])
let formState = reactive<{ systemConfig: FormState[] }>({
  systemConfig: props.systemConfig
})
const languageList = ref<LanguageList[]>()
watchEffect(() => {
  formState.systemConfig = props.systemConfig
})
const formData = ref<FormState>()

const propertyValueInput = (item: FormState) => {
  formData.value = item
  nameTest()
}
const isValidate = () => {
  formRef.value?.validate().then(() => {
    emit('submitUpdate', toRaw(formData.value))
  })
}
const isMin = (type: string) => {
  switch (type) {
    case 'userOptLogExpireTime':
      return 30
    case 'businessDataExpireTime':
    case 'reportDataExpireTime':
      return 60

    default:
      return 1
  }
}
const isMax = (type: string) => {
  switch (type) {
    case 'businessDataExpireTime':
    case 'reportDataExpireTime':
      return 1095
    case 'pushCycle':
      return 100
    default:
      return 365
  }
}
const isUnit = (type: string) => {
  switch (type) {
    case 'pushCycle':
      return t('message.second')
    default:
      return t('message.day')
  }
}
const noticePushLanguageTypeOptions = ref([
  {
    label: t('message.followingSystem'),
    value: '1'
  },
  {
    label: t('message.customization'),
    value: '2'
  }
])
const noticePushLanguageOptions = ref([
  {
    label: t('message.zh_CN'),
    value: 'zh_CN'
  },
  {
    label: t('message.en_US'),
    value: 'en_US'
  },
  {
    label: t('message.ja_JP'),
    value: 'ja_JP '
  }
])
const isOptions = (key: string) => {
  let list: { label: string; value: string }[] = []
  switch (key) {
    case 'noticePushLanguageType':
      list = noticePushLanguageTypeOptions.value
      break

    default:
      break
  }
  return list
}
const getLanguageFn = () => {
  getLanguage().then(res => {
    languageList.value = res.data
  })
}
getLanguageFn()
const nameTest = debounce(isValidate, 500)
</script>
<style lang="less" scoped>
.ant-form-item {
  margin: 0;
  margin-top: 6px;
}

.form-list-padding {
  min-height: 64px;
  padding-top: 16px;
  padding-bottom: 16px;
}

.form-list {
  display: flex;
  // align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  min-height: 64px;
  padding-top: 10px;
  padding-bottom: 10px;

  &-border {
    box-sizing: border-box;
    min-width: 800px;
    margin-top: 20px;
    margin-left: 16px;
    padding-right: 28px;
    padding-left: 32px;
    border: 1px solid #e4e4e4;
    border-radius: 2px;

    &:hover {
      background: #fffaf7;
    }
  }

  &-title {
    color: #000;
    font-weight: 400;
    font-size: 14px;

    &-describe {
      color: #a3a3a3;
      font-weight: 400;
      font-size: 12px;
    }
  }

  &-item {
    &-unit {
      position: absolute;
      top: 1px;
      right: 7px;
      min-width: 20px;
      height: 30px;
      color: #9d9d9d;
      font-weight: 400;
      font-size: 14px;
      line-height: 30px;
      text-align: center;
      background-color: #fff;
    }
  }

  &-operate {
    align-items: center;
    justify-content: start;
    border-top: 1px solid #e4e4e4;
  }

  .ant-form-item {
    justify-content: space-between;
  }

  .ant-input-number {
    position: relative;
  }
}
/* stylelint-disable-next-line selector-pseudo-element-no-unknown */
</style>
