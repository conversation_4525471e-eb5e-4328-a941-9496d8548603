<template>
  <MyTable
    :filterConfigList="State.filterConfigList"
    :filterData="State.filterData"
    :columns="State.columns"
    :tableOptions="State.tableOptions"
    :pagination="pageState.pagination"
    :row-selection="false"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    @formChange="filterChange">
    <template #createDate="{ scope }">{{ setTimeStr(scope.record.createDate) }}</template>
    <template #updateDate="{ scope }">{{ setTimeStr(scope.record.updateDate) }}</template>
  </MyTable>

  <my-import-file-dialog
    v-model:visible="fileDialogVisible"
    :accept="'.zip'"
    :title="t('message.importLanguage')"
    :describeFileName="t('message.actionSetting.fileFormatJson')"
    :action="apiUrl + '/fleet/language/upload'"
    @success="getListData"></my-import-file-dialog>
</template>

<script setup lang="ts">
import type { ITableScope } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { useLocale } from 'youibot-plus'
import { columns, filterConfigList, btnList, tableOptions } from './language-configuration'
import type { ITableRow } from './language-configuration'
import { getOperateText } from '@/utils'
import { getLanguagePage, deleteLanguageId } from '@/api/language'
import myImportFileDialog from '@/components/common/my-import-file-dialog.vue'
import { setTimeStr, downLoadFileOpen } from '@/utils/index'
import { Modal } from 'ant-design-vue'
const apiUrl = `//${window.apiUrl}`
const { pageState, changeFilter, getData } = usePage({
  getPageFn: getLanguagePage,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  deleteSuccessFn: () => {
    State.selectedRowKeys = []
  }
})
const { t } = useLocale()
const fileDialogVisible = ref<boolean>(false)

btnList[0].onClick = () => {
  fileDialogVisible.value = true
}

//封装右侧操作栏按钮
const operationHandleClick = (type: string, data: ITableScope<ITableRow>) => {
  if (!data) return
  const {
    record: { id }
  } = data
  switch (type) {
    // 编辑
    case 'del':
      Modal.confirm({
        content: t('message.delLanguageTip'),
        zIndex: 2100,
        onOk: () => {
          deleteLanguageId({}, { id: id as any })
            .then(res => {
              getData()
              getOperateText(t, {
                type: 'del'
              })
            })
            .catch(err => {
              getOperateText(t, {
                type: 'del',
                result: 'error',
                reason: err.msg
              })
            })
        }
      })

      break
    case 'export':
      downLoadFileOpen(t, '/fleet/language/export', { id: id })
      break
  }
}

;(function operationClickHandler() {
  const list = ['del', 'export']
  list.map((item, index) => {
    tableOptions[index].onClick = (text?: object) => operationHandleClick(item, text as ITableScope<ITableRow>)
  })
})()

const State = reactive({
  columns,
  tableOptions,
  filterData: {},
  filterConfigList,
  selectedRowKeys: [] as (string | number)[]
})

// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}

  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      const data = toRaw(filterData[item]) as unknown as string[]
      params[item] = data.join(',')
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}

const getListData = () => {
  filterChange(State.filterData)
}
onMounted(() => {
  getData()
})
</script>
