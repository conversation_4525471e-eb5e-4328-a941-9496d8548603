import type { IBtnType, IFormItemType } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, SELECT, TEXTAREA, INPUTNUMBER } from 'youibot-plus'

export interface ITableRow {
  closeTime?: string
  code?: number
  createDate?: string
  description?: string
  deviceId?: string
  id?: string
  ignoreStatus?: number
  invalidTime?: number
  lastPushTime?: string
  level?: number
  mapName?: string
  missionWorkId?: string
  solution?: string
  sourceSystem?: string
  status?: number
  type?: string
  updateDate?: string
  vehicleCode?: string
}

//table操作栏
const tableOptions: IBtnType[] = [
  {
    name: 'message.edit',
    type: 'link',
    permission: 'edit'
  }
]

//table 配置
const columns: TableColumnsType = [
  {
    // 编码
    title: 'message.encoding',
    dataIndex: 'code',
    width: 120
  },
  {
    // 等级
    title: 'message.notice.level',
    key: 'level',
    width: 80
  },
  {
    // 来源
    title: 'message.notice.source',
    dataIndex: 'sourceSystem',
    width: 120
  },
  // {
  //   // 类型
  //   title: 'message.type',
  //   dataIndex: 'type',
  //   width: 120
  // },
  {
    // 间隔时间
    title: 'message.notice.intervalTime',
    key: 'invalidTime',
    width: 80
  },
  {
    // 是否上报
    title: 'message.notice.isUpload',
    key: 'isUpload',
    width: 120
  },
  {
    // 描述
    title: 'message.describe',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    // 解决方案
    title: 'message.solution',
    key: 'solution',
    width: 200,
    ellipsis: true
  },

  {
    title: 'message.options',
    key: 'options',
    fixed: 'right'
  }
]
// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    // 编码
    is: INPUT,
    name: 'code',
    label: 'message.encoding',
    allowClear: true
  },
  {
    // 等级
    is: SELECT,
    name: 'level',
    label: 'message.notice.level',
    allowClear: true,
    isTranslated: true,
    options: [
      { label: 'message.notice.levelList.1', value: 1 }, //普通
      { label: 'message.notice.levelList.2', value: 2 }, //警告
      { label: 'message.notice.levelList.3', value: 3 } //错误
    ]
  },
  {
    // 来源
    is: INPUT,
    name: 'sourceSystem',
    label: 'message.notice.source',
    allowClear: true
  },
  // {
  //   // 类型
  //   is: INPUT,
  //   name: 'type',
  //   label: 'message.type',
  //   allowClear: true
  // },
  {
    // 间隔时间
    is: INPUT,
    name: 'invalidTime',
    label: 'message.notice.intervalTime',
    allowClear: true
  },
  {
    // 是否上报
    is: SELECT,
    name: 'isUpload',
    label: 'message.notice.isUpload',
    allowClear: true,
    isTranslated: true,
    options: [
      { label: 'message.notice.isUploadState.0', value: 0 }, //否
      { label: 'message.notice.isUploadState.1', value: 1 } //是
    ]
  },
  {
    // 描述
    is: INPUT,
    name: 'description',
    label: 'message.describe',
    allowClear: true
  },
  {
    // 解决方案
    is: INPUT,
    name: 'solution',
    label: 'message.solution',
    allowClear: true
  }
]

//底部按钮
const btnList: IBtnType[] = [
  {
    name: 'message.add',
    type: 'primary',
    icon: 'iconfont icon-zengjia',
    permission: 'add'
  },
  {
    name: 'message.del',
    icon: 'iconfont icon-xiazai14',
    permission: 'del'
  },
  {
    name: 'message.multImport',
    permission: 'import'
  },
  {
    name: 'message.multExport',
    permission: 'export'
  }
]

const formConfigList: IFormItemType[] = [
  {
    // 编码
    is: INPUT,
    name: 'code',
    label: 'message.encoding'
  },
  {
    // 等级
    is: SELECT,
    name: 'level',
    label: 'message.notice.level',
    isTranslated: true,
    options: [
      { label: 'message.notice.levelList.1', value: 1 }, //普通
      { label: 'message.notice.levelList.2', value: 2 }, //警告
      { label: 'message.notice.levelList.3', value: 3 } //错误
    ]
  },
  {
    // 来源
    is: INPUT,
    name: 'sourceSystem',
    label: 'message.notice.source'
  },
  // {
  //   // 类型
  //   is: INPUT,
  //   name: 'type',
  //   label: 'message.type'
  // },
  {
    // 间隔时间
    is: INPUTNUMBER,
    name: 'invalidTime',
    label: 'message.notice.intervalTime',
    min: 0
  },
  {
    // 是否上报
    is: SELECT,
    name: 'isUpload',
    label: 'message.notice.isUpload',
    isTranslated: true,
    options: [
      { label: 'message.notice.isUploadState.0', value: 0 }, //否
      { label: 'message.notice.isUploadState.1', value: 1 } //是
    ]
  },
  {
    // 描述
    is: INPUT,
    name: 'description',
    label: 'message.describe'
  },
  {
    // 解决方案
    is: TEXTAREA,
    name: 'solution',
    label: 'message.solution'
  }
]
export { columns, filterConfigList, tableOptions, btnList, formConfigList }
