<template>
  <MyTable
    :filterConfigList="State.filterConfigList"
    :filterData="State.filterData"
    :columns="State.columns"
    :tableOptions="State.tableOptions"
    :pagination="pageState.pagination"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    :customRow="customRow"
    @formChange="filterChange">
    <template #level="{ scope }">
      <a-tag :color="isColor(scope.record.level)">
        {{ t('message.notice.levelList.' + scope.record.level) }}
      </a-tag>
    </template>
    <template #isUpload="{ scope }">
      {{ t('message.notice.isUploadState.' + scope.record.isUpload) }}
    </template>
    <template #invalidTime="{ scope }">{{ scope.record.invalidTime }} {{ t('message.second') }}</template>
  </MyTable>
  <MyForm :visible="visible" :formConfigList="formConfigList" :formData="State.formData" @close="onClose" @submit="formSubmit"></MyForm>
  <my-import-file-dialog
    v-model:visible="fileDialogVisible"
    :accept="'.xlsx'"
    :title="t('message.notice.importTheNotificationProfile')"
    :describeFileName="t('message.actionSetting.fileFormatJson')"
    :action="apiUrl + '/fleet/notice/config/uploadExcelFile'"
    @success="getListData"></my-import-file-dialog>
</template>

<script setup lang="ts">
import type { ITableScope, IFormRules } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { downLoadFileOpen, getOperateText } from '@/utils'
import { useLocale } from 'youibot-plus'
import { columns, filterConfigList, btnList, tableOptions, formConfigList } from './config'
import type { ITableRow } from './config'
import { messageTip } from '@/utils'
import { postNoticeConfig, getNoticeConfigPage, putNoticeConfig, deleteNoticeConfig } from '@/api/notice'
import myImportFileDialog from '@/components/common/my-import-file-dialog.vue'
import { isRequire, isPositiveInteger, isLength } from '@/utils/form-rules'
import { permission } from '@/router'
const apiUrl = `//${window.apiUrl}`
const { pageState, deleteItemConfirm, changeFilter, getData } = usePage({
  getPageFn: getNoticeConfigPage,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  deleteBatchFn: deleteNoticeConfig as unknown as (params: number[]) => Promise<any>
})
const { t } = useLocale()
const visible = ref<boolean>(false)
const fileDialogVisible = ref<boolean>(false)
const onClose = () => {
  visible.value = false
}

btnList[0].onClick = () => {
  visible.value = true
  State.formData = { isUpload: 1 }
}
btnList[1].onClick = () => {
  if (State.selectedRowKeys.length > 0) {
    deleteItemConfirm(State.selectedRowKeys as number[], t('message.delTips'))
  } else {
    messageTip(t('message.selectTheDataYouWantToDelete'), 'error')
  }
}
btnList[2].onClick = () => {
  fileDialogVisible.value = true
}
btnList[3].onClick = () => {
  downLoadFileOpen(t, '/fleet/notice/config/export', { ids: State.selectedRowKeys.join(','), ...pageState.filterData })
}
//封装右侧操作栏按钮
const operationHandleClick = (type: string, data: ITableScope<ITableRow>) => {
  if (!data) return

  switch (type) {
    // 编辑
    case 'edit':
      const {
        record: { ...rest }
      } = data as ITableScope<ITableRow>
      // 数据类型不一致导致校验不通过
      Object.assign(State.formData, { ...rest, code: rest.code?.toString() })
      visible.value = true
      break
  }
}

;(function operationClickHandler() {
  const list = ['edit']
  list.map((item, index) => {
    tableOptions[index].onClick = (text?: object) => operationHandleClick(item, text as ITableScope<ITableRow>)
  })
})()

const State = reactive({
  columns,
  tableOptions,
  filterData: {},
  filterConfigList,
  formData: { isUpload: 1 },
  formConfigList,

  selectedRowKeys: [] as (string | number)[]
})
const rules: { [key: string]: IFormRules[] } = {
  code: [isLength(1, 10), isPositiveInteger()],
  level: [isRequire('message.pleaseSelect')],
  sourceSystem: [isRequire()],
  // type: [isRequire()],
  invalidTime: [isRequire()],
  isUpload: [isRequire('message.pleaseSelect')],
  description: [isRequire()],
  solution: [isRequire()]
}
Object.keys(rules).forEach((item, index) => {
  State.formConfigList[index].rules = rules[item]
})
// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}

  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      params.startTime = filterData[item][0]
      params.endTime = filterData[item][1]
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}
const customRow = (record: ITableRow) => {
  return {
    onDblclick: () => {
      if (permission.includes('/operations/setting/notification-settings/edit')) {
        operationHandleClick('edit', { record: record } as ITableScope<ITableRow>)
      }
    }
  }
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const formSubmit = (formData: any) => {
  if (formData.id) {
    putNoticeConfig(formData)
      .then(res => {
        getOperateText(t, {
          type: 'update'
        })
        getData()
        State.formData = { isUpload: 1 }
        visible.value = false
      })
      .catch(err => {
        getOperateText(t, {
          type: 'update',
          result: 'error',
          reason: err.msg
        })
      })
  } else {
    postNoticeConfig(formData)
      .then(res => {
        getOperateText(t)
        getData()
        State.formData = { isUpload: 1 }
        visible.value = false
      })
      .catch(err => {
        getOperateText(t, {
          result: 'error',
          reason: err.msg
        })
      })
  }
}
const isColor = (type: number) => {
  switch (type) {
    case 1:
      return 'success'
    case 2:
      return 'warning'
    case 3:
      return 'error'
    default:
      break
  }
}

const getListData = () => {
  filterChange(State.filterData)
}

onMounted(() => {
  getData()
})
</script>
