<template>
  <div class="system-settings-wrap">
    <div class="system-settings-inner">
      <div class="system-settings-menu">
        <ul>
          <li
            v-for="(item, index) in list"
            class="system-settings-menu-list"
            :class="{ 'system-settings-menu-list-activate': item.type === configurineMenu?.type }"
            :key="index"
            @click="menuClick(item)">
            <div>
              <i class="iconfont list-icon" :class="`icon-${item.icon}`"></i>
            </div>
            <div class="system-settings-menu-list-content">
              <div class="system-settings-menu-list-content-title">{{ t(item.title) }}</div>
              <a-tooltip placement="bottom">
                <template #title>{{ t(item.describe) }}</template>
                <div class="system-settings-menu-list-content-notes">{{ t(item.describe) }}</div>
              </a-tooltip>
            </div>
            <i class="iconfont icon-jiantouyou"></i>
          </li>
        </ul>
      </div>
      <div class="system-settings-page">
        <div class="system-settings-page-title">{{ t(configurineMenu?.title) }}</div>
        <div class="system-settings-page-spacing"></div>
        <component
          v-if="state.systemConfig?.length && configurineMenu?.component"
          :is="configurineMenu?.component"
          :systemConfig="state.systemConfig"
          @refresh="getSysPropertyListFn"
          @submitUpdate="propertyValueInput"></component>
      </div>
    </div>
  </div>
  <div class="extra-box"></div>
</template>

<script lang="ts" setup>
import { getOperateText } from '@/utils'
import { getSysPropertyGetSystemConfig, putSysPropertyId } from '@/api/sys'
import { FormState, ConfigurineMenu, list } from './config'
import { useLocale } from 'youibot-plus'
import { getQuery } from '@/router'
const { t } = useLocale()

const configurineMenu = ref<ConfigurineMenu>(list.value[0])
const state = reactive({
  systemConfig: []
})
const menuClick = (item: ConfigurineMenu) => {
  configurineMenu.value = item
  state.systemConfig = []
  getSysPropertyListFn()
}
const getSysPropertyListFn = () => {
  getSysPropertyGetSystemConfig({ category: configurineMenu.value?.type }).then(res => {
    state.systemConfig = []

    state.systemConfig = res.data
  })
}
const propertyValueInput = (item: FormState) => {
  putSysPropertyFn(item)
}
const putSysPropertyFn = (item: FormState) => {
  putSysPropertyId(item, { id: item.id })
    .then(res => {
      getOperateText(t, {
        type: 'update'
      })
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg
      })
    })
}
onMounted(() => {
  const index = getQuery('index') as unknown as number
  if (index !== undefined && index !== null) {
    menuClick(list.value[index])
  } else {
    menuClick(list.value[0])
  }
})
// 名称防抖校验
</script>

<style lang="less" scoped>
.system-settings-wrap {
  height: 100%;

  .system-settings-inner {
    display: flex;
    height: 100%;

    .system-settings-menu {
      width: 371px;
      padding: 20px 16px 12px 0;
      border-right: 1px solid #dcdcdc;

      &-list {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 11px 16px;
        border: 1px solid #dcdcdc;
        cursor: pointer;

        &-activate {
          background: rgb(238 116 42 / 10%);
        }

        .list-icon {
          margin-right: 11px;
          color: #000;
          font-size: 24px;
        }

        &-content {
          &-title {
            width: 210px;
            overflow: hidden;
            color: #000;
            font-weight: 400;
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          &-notes {
            width: 210px;
            overflow: hidden;
            color: #313131;
            font-weight: 400;
            font-size: 12px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        .icon-jiantouyou {
          position: absolute;
          right: 12px;
          color: #1a1a1a;
        }

        &:hover {
          background: rgb(238 116 42 / 10%);
        }
      }
    }

    .system-settings-page {
      width: calc(100% - 371px);
      height: 100%;

      &-title {
        margin: 21px 0 12px 16px;
        padding-left: 12px;
        font-weight: 400;
        font-size: 16px;
        border-left: 4px solid #ee742a;
      }

      &-spacing {
        min-width: 800px;
        height: 1px;
        margin-bottom: 4px;
        margin-left: 16px;
        background-color: #e7e7e7;
      }
    }
  }
}

.extra-box {
  position: absolute;
  bottom: 0;
  left: 212px;
  width: 371px;
  height: 16px;
  border-right: 1px solid #dcdcdc;
}
</style>
