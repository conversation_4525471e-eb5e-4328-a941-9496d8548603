import trafficConfig from './form-config/traffic-config.vue'
import chargConfig from './form-config/charging-config.vue'
import parkConfig from './form-config/parking-config.vue'

export interface FormState {
  id: number
  type: string
  category: string
  title: string
  remark: string
  propertyKey: string
  propertyValue: string | string[]
  valueType: string
}
export interface ConfigurineMenu {
  title: string
  describe: string
  type: string
  icon: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component?: any
}
export const list = ref<ConfigurineMenu[]>([
  {
    title: 'message.chargeConfig.title',
    describe: 'message.chargeConfig.describe',
    type: 'charge',
    icon: 'dianchi-zhongdianliang',
    component: chargConfig
  },
  {
    title: 'message.parkConfig.title',
    describe: 'message.parkConfig.describe',
    type: 'park',
    icon: 'tingche',
    component: parkConfig
  },

  {
    title: 'message.trafficConfig.title',
    describe: 'message.trafficConfig.describe',
    type: 'traffic',
    icon: 'daohang',
    component: trafficConfig
  }
])
