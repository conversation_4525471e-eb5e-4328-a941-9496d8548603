<template>
  <div class="traffic-config">
    <a-form :model="formState" ref="formRef" name="basic">
      <template v-for="(item, index) in formState.systemConfig" :key="index">
        <div v-if="['blockCheckEnable'].includes(item.propertyKey)" class="form-list-border">
          <div class="form-list form-list-padding">
            <div class="form-list-title">
              <div>{{ item.title }}</div>
              <div class="form-list-title-describe">{{ item.remark }}</div>
            </div>
            <div class="form-list-input">
              <a-form-item>
                <a-switch v-model:checked="item.propertyValue" checkedValue="1" unCheckedValue="0" @change="propertyValueInput(item)" />
              </a-form-item>
            </div>
          </div>
          <div class="form-list form-list-padding form-list-operate">
            <div class="form-list" style="margin-right: 48px">
              <div class="form-list-label">{{ formState.systemConfig[1].remark }}</div>
              <a-form-item
                class="form-list-item"
                :style="{ width: '101px' }"
                :name="['systemConfig', 1, 'propertyValue']"
                :rules="[isRequire()]">
                <a-input-number
                  v-model:value="formState.systemConfig[1].propertyValue"
                  :controls="false"
                  :min="10"
                  :max="100"
                  @blur="propertyValueInput(formState.systemConfig[1])"
                  @pressEnter="evetBlur" />
                <div class="form-list-item-unit">{{ t('message.second') }}</div>
              </a-form-item>
            </div>
            <div class="form-list">
              <div class="form-list-label">{{ formState.systemConfig[2].remark }}</div>
              <a-form-item
                class="form-list-item"
                :style="{ width: '101px' }"
                :name="['systemConfig', 2, 'propertyValue']"
                :rules="[isRequire()]">
                <a-input-number
                  v-model:value="formState.systemConfig[2].propertyValue"
                  :controls="false"
                  :min="10"
                  @blur="propertyValueInput(formState.systemConfig[2])"
                  @pressEnter="evetBlur" />
                <div class="form-list-item-unit">{{ t('message.second') }}</div>
              </a-form-item>
            </div>
          </div>
        </div>
        <template v-if="modeLocalCode() == 'multi'">
          <div v-if="item.propertyKey === 'autoReleaseResource'" class="form-list-border">
            <div class="form-list form-list-padding">
              <div class="form-list-title">
                <div>{{ item.title }}</div>
                <div class="form-list-title-describe">{{ item.remark }}</div>
              </div>
              <div>
                <a-form-item>
                  <a-switch v-model:checked="item.propertyValue" checkedValue="1" unCheckedValue="0" @change="propertyValueInput(item)" />
                </a-form-item>
              </div>
            </div>
            <div class="form-list form-list-operate form-list-padding">
              <div class="form-list" style="margin-right: 48px">
                <div class="form-list-label">{{ formState.systemConfig[index + 1].remark }}</div>
                <a-form-item
                  class="form-list-item"
                  :style="{ width: '101px' }"
                  :name="['systemConfig', index + 1, 'propertyValue']"
                  :rules="[isRequire()]">
                  <a-input-number
                    v-model:value="formState.systemConfig[index + 1].propertyValue"
                    :controls="false"
                    @blur="propertyValueInput(formState.systemConfig[index + 1])"
                    @pressEnter="evetBlur" />
                  <div class="form-list-item-unit">{{ t('message.second') }}</div>
                </a-form-item>
              </div>
            </div>
          </div>

          <div v-if="item.propertyKey === 'freeVehicleRunPolicy'" class="form-list-border">
            <div class="form-list form-list-padding">
              <div class="form-list-title">
                <div>{{ item.title }}</div>
                <div class="form-list-title-describe">{{ item.remark }}</div>
              </div>
              <div>
                <a-form-item v-if="['freeVehicleRunPolicy'].includes(item.propertyKey)" :rules="[isRequire()]">
                  <a-select
                    v-model:value="item.propertyValue"
                    :mode="''"
                    :style="'width:120px'"
                    :options="isOptions(item.propertyKey)"
                    @change="propertyValueInput(item)"></a-select>
                </a-form-item>
              </div>
            </div>
            <div v-if="item.propertyValue === '3'" class="form-list form-list-operate form-list-padding">
              <div class="form-list" style="margin-right: 48px">
                <div class="form-list-label">{{ formState.systemConfig[index + 1].title }}</div>
                <a-form-item
                  class="form-list-item"
                  :style="{ width: '101px' }"
                  :name="['systemConfig', index + 1, 'propertyValue']"
                  :rules="[isRequire()]">
                  <a-input-number
                    v-model:value="formState.systemConfig[index + 1].propertyValue"
                    :controls="false"
                    :min="0"
                    :max="9999"
                    @blur="propertyValueInput(formState.systemConfig[index + 1])"
                    @pressEnter="evetBlur" />
                  <div class="form-list-item-unit">{{ t('message.second') }}</div>
                </a-form-item>
              </div>
            </div>
          </div>
          <div v-if="isShow(item.propertyKey)" class="form-list form-list-border form-list-padding">
            <div class="form-list-title">
              <div>{{ item.title }}</div>
              <div class="form-list-title-describe">{{ item.remark }}</div>
            </div>
            <div class="form-list-input">
              <!-- 静止机器人和故障机器人是Int类型，但要单独处理 -->
              <a-form-item
                v-if="
                  [
                    'faultVehicleRunPolicy',
                    'staticVehicleRunPolicy',
                    'workVehicleRunPolicy',
                    'abnormalVehicleRunPolicy',
                    'globalPauseExecutingArmScriptIsStop'
                  ].includes(item.propertyKey)
                "
                :name="['systemConfig', index, 'propertyValue']"
                :rules="[isRequire()]">
                <a-select
                  v-model:value="item.propertyValue"
                  :mode="''"
                  :style="'width:120px'"
                  :options="isOptions(item.propertyKey)"
                  @change="propertyValueInput(item)"></a-select>
              </a-form-item>

              <!-- 通道避让 -->
              <a-form-item
                v-else-if="
                  (item.valueType === 'Integer' || item.valueType === 'Double') &&
                  ['channelAvoidance', 'highPerformanceMode'].includes(item.propertyKey)
                "
                class="form-list-item"
                :name="['systemConfig', index, 'propertyValue']"
                :rules="[isRequire()]">
                <a-switch v-model:checked="item.propertyValue" checkedValue="1" unCheckedValue="0" @change="propertyValueInput(item)" />
              </a-form-item>
              <!-- Int类型，且非静止机器人和故障机器人 -->
              <a-form-item
                v-else-if="
                  (item.valueType === 'Integer' || item.valueType === 'Double') &&
                  !['faultVehicleRunPolicy', 'staticVehicleRunPolicy'].includes(item.propertyKey)
                "
                :style="{ width: '101px' }"
                class="form-list-item"
                :name="['systemConfig', index, 'propertyValue']"
                :rules="[isRequire()]">
                <a-input-number
                  v-model:value="item.propertyValue"
                  :controls="false"
                  :min="isMin(item.propertyKey)"
                  :max="isMax(item.propertyKey)"
                  :precision="item.propertyKey === 'occupyResourceRange' ? 2 : 0"
                  @blur="propertyValueInput(item)"
                  @pressEnter="evetBlur" />
                <div
                  class="form-list-item-unit"
                  v-if="
                    [
                      'pathApplyLength',
                      'occupyResourceRange',
                      'autoDoorAdvanceLength',
                      'showerDoorAdvanceLength',
                      'elevatorAdvanceLength'
                    ].includes(item.propertyKey)
                  ">
                  {{ t('message.meter') }}
                </div>
                <div class="form-list-item-unit" v-if="['trackRadius'].includes(item.propertyKey)">
                  {{ t('message.cm') }}
                </div>
                <div class="form-list-item-unit" v-if="['exceptionNotifyTime'].includes(item.propertyKey)">
                  {{ t('message.minutes') }}
                </div>
              </a-form-item>
              <a-form-item v-else-if="item.propertyKey === 'thirdSystemTrafficAreaReqUrl'">
                <a-input v-model:value="item.propertyValue" @blur="propertyValueInput(item)" @pressEnter="evetBlur"></a-input>
              </a-form-item>
              <a-form-item v-else :name="['systemConfig', index, 'propertyValue']" :rules="[isRequire()]">
                <a-select
                  v-model:value="item.propertyValue"
                  :mode="item.propertyKey === 'avoidMarkerTypes' ? 'multiple' : ''"
                  :style="item.propertyKey === 'avoidMarkerTypes' ? 'max-width: 240px;min-width:120px' : 'width:120px'"
                  :options="isOptions(item.propertyKey)"
                  @change="propertyValueInput(item)"></a-select>
              </a-form-item>
            </div>
          </div>
        </template>
      </template>
    </a-form>
  </div>
</template>
<script lang="ts" setup>
import { FormState } from '@/pages/operations/setting/system-settings/config'
import { FormInstance, Modal } from 'ant-design-vue'
import { PropType } from 'vue'
import { isRequire } from '@/utils/form-rules'
import { debounce, evetBlur, messageTip } from '@/utils'
import { useLocale } from 'youibot-plus'
import useStore from '@/stores'
import { modeLocalCode } from '@/utils/storage'
const { t } = useLocale()
const props = defineProps({
  systemConfig: { type: Object as PropType<FormState[]>, default: () => ({}) }
})
const emit = defineEmits(['refresh', 'submitUpdate'])
let formState = reactive<{ systemConfig: FormState[] }>({
  systemConfig: props.systemConfig
})
const formRef = ref<FormInstance>()
watchEffect(() => {
  formState.systemConfig = props.systemConfig
  formState.systemConfig.map(item => {
    if (item.propertyKey === 'avoidMarkerTypes' && typeof item.propertyValue === 'string') {
      item.propertyValue = item.propertyValue.split(',')
    }
  })
})
const faultOptions = ref([
  {
    label: t('message.trafficConfig.faultOptions.1'),
    value: '1'
  },
  {
    label: t('message.trafficConfig.faultOptions.2'),
    value: '2'
  }
])
const banOptions = ref([
  {
    label: t('message.trafficConfig.banOptions.1'),
    value: '1'
  },
  {
    label: t('message.trafficConfig.banOptions.2'),
    value: '2'
  },
  {
    label: t('message.trafficConfig.banOptions.3'),
    value: '3'
  }
])
const collisionOptions = ref([
  {
    label: t('message.trafficConfig.collisionOptions.1'),
    value: '1'
  },
  {
    label: t('message.trafficConfig.collisionOptions.2'),
    value: '2'
  },
  {
    label: t('message.trafficConfig.collisionOptions.3'),
    value: '3'
  }
])
const globalPauseExecutingArmScriptIsStop = ref([
  {
    label: t('message.globalPauseExecutingArmScriptIsStop.Immediately'),
    value: 'Immediately'
  },
  {
    label: t('message.globalPauseExecutingArmScriptIsStop.Later'),
    value: 'Later'
  }
])
const isMin = (type: string) => {
  switch (type) {
    case 'trackRadius':
      return 0

    default:
      return -Infinity
  }
}
const isMax = (type: string) => {
  switch (type) {
    case 'trackRadius':
      return 100
    default:
      return Infinity
  }
}
const isOptions = (key: string) => {
  let list: { label: string; value: string }[] = []
  switch (key) {
    case 'faultVehicleRunPolicy':
    case 'workVehicleRunPolicy':
    case 'abnormalVehicleRunPolicy':
      list = faultOptions.value
      break
    case 'staticVehicleRunPolicy':
      list = banOptions.value
      break
    case 'avoidMarkerTypes':
      list = collisionOptions.value
      break
    case 'freeVehicleRunPolicy':
      list = banOptions.value
      break
    case 'globalPauseExecutingArmScriptIsStop':
      list = globalPauseExecutingArmScriptIsStop.value
      break
    default:
      break
  }
  return list
}
const isShow = (key: string) => {
  let list = [
    'blockCheckEnable',
    'blockCheckInterval',
    'removeBlockInterval',
    'autoReleaseResource',
    'freeVehicleRunPolicy',
    'driveFreeVehicleFreeTime',
    'disconnectionTime',
    'faultVehicleRunPolicy', //故障机器人因没实现而隐藏的,需要时去掉
    'staticVehicleRunPolicy' //静止机器人因没实现而隐藏的,需要时去掉
  ]
  if (list.includes(key)) {
    return false
  }
  return true
}
const formData = ref<FormState>()

const propertyValueInput = (item: FormState) => {
  if (item.propertyKey === 'highPerformanceMode' && item.propertyValue == '1') {
    Modal.confirm({
      content: t('message.trafficConfig.highPerformanceMode'),
      zIndex: 2100,
      onOk: () => {
        formData.value = item
        nameTest()
      },
      onCancel: () => {
        item.propertyValue = '0'
      }
    })
    return
  }
  if (item.propertyKey === 'exceptionNotifyTime' && Number(item.propertyValue) < 0) {
    messageTip(t('message.exceptionNotifyTimeTip'), 'error')
    item.propertyValue = undefined as unknown as string
    return
  }
  formData.value = item
  nameTest()
}
const isValidate = () => {
  formRef.value?.validate().then(() => {
    if (formData.value?.propertyKey === 'avoidMarkerTypes' && typeof formData.value.propertyValue === 'object') {
      formData.value.propertyValue = formData.value.propertyValue.join(',')
    }
    emit('submitUpdate', toRaw(formData.value))
  })
}
const nameTest = debounce(isValidate, 500)
</script>
<style lang="less" scoped>
.traffic-config {
  height: calc(100% - 60px);
  overflow: auto;
}

.ant-form-item {
  margin: 0;
}

.form-list-padding {
  min-height: 64px;
  padding-top: 16px;
  padding-bottom: 16px;
}

.form-list {
  display: flex;
  // align-items: center;
  justify-content: space-between;
  // box-sizing: border-box;

  &-border {
    box-sizing: border-box;
    min-width: 800px;
    margin-top: 20px;
    margin-left: 16px;
    padding-right: 28px;
    padding-left: 32px;
    border: 1px solid #e4e4e4;
    border-radius: 2px;

    &:hover {
      background: #fffaf7;
    }
  }

  &-title {
    color: #000;
    font-weight: 400;
    font-size: 14px;

    &-describe {
      color: #a3a3a3;
      font-weight: 400;
      font-size: 12px;
    }
  }

  &-label {
    line-height: 32px;
  }

  &-input {
    margin-top: 5px;
  }

  &-item {
    position: relative;
    margin-left: 16px;

    &-unit {
      position: absolute;
      top: 1px;
      right: 2px;
      min-width: 20px;
      height: 30px;
      color: #9d9d9d;
      font-weight: 400;
      font-size: 14px;
      line-height: 30px;
      text-align: center;
      background-color: #fff;
    }
  }

  &-operate {
    justify-content: start;
    border-top: 1px solid #e4e4e4;
  }

  .ant-input-number {
    width: 101px;
  }

  .ant-input {
    width: 254px;
  }
}
/* stylelint-disable-next-line selector-pseudo-element-no-unknown */
</style>
