import { useLocale, SELECT } from 'youibot-plus'

export default () => {
  const { t } = useLocale()

  const columns = [
    {
      title: t('message.vehicle'),
      dataIndex: 'vehicleCode',
      width: 80
    },
    {
      title: t('message.ParkingMarker'),
      dataIndex: 'bindParkMarkers'
    },
    {
      title: t('message.parkConfig.createTask'),
      dataIndex: 'parkTaskTypeId'
    },
    {
      title: t('message.status'),
      dataIndex: 'autoParkStatus'
    },
    {
      title: t('message.options'),
      dataIndex: 'autoPark'
    }
  ]

  const parkEnableOptions = [
    {
      label: t('message.statusList.1'),
      value: '1'
    },
    {
      label: t('message.statusList.0'),
      value: '0'
    }
  ]

  const formConfigList = [
    {
      is: SELECT,
      name: 'parkTaskTypeId',
      label: 'message.chargeConfig.createTask',
      allowClear: true,
      options: []
    },
    {
      is: SELECT,
      name: 'autoPark',
      label: 'message.status',
      isTranslated: true,
      options: [
        { label: 'message.statusList.0', value: 0 },
        { label: 'message.statusList.1', value: 1 }
      ]
    }
  ]

  return {
    columns,
    parkEnableOptions,
    formConfigList
  }
}
