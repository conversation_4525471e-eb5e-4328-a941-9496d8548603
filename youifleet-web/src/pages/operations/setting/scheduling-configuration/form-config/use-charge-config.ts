import { useLocale, INPUTNUMBER, SELECT } from 'youibot-plus'
import { isRequire } from '@/utils/form-rules'

export default () => {
  const { t } = useLocale()

  const columns = [
    {
      title: t('message.vehicle'),
      dataIndex: 'vehicleCode',
      width: 80
    },
    {
      title: t('message.chargeConfig.lowBattery'),
      dataIndex: 'lowBattery',
      width: 120
    },
    {
      title: t('message.chargeConfig.highBattery'),
      dataIndex: 'highBattery',
      width: 120
    },
    {
      title: t('message.chargeConfig.minBatteryValue'),
      dataIndex: 'minBatteryValue',
      width: 160
    },
    {
      title: t('message.chargeConfig.minChargeTime'),
      dataIndex: 'minChargeTime',
      width: 160
    },
    {
      title: t('message.ChargingMarker'),
      dataIndex: 'bindChargeMarkers'
    },
    {
      title: t('message.chargeConfig.createTask'),
      dataIndex: 'chargeTaskTypeId'
    },
    {
      title: t('message.status'),
      dataIndex: 'autoChargeStatus'
    },
    {
      title: t('message.options'),
      dataIndex: 'autoCharge'
    }
  ]

  const chargeEnableOptions = [
    {
      label: t('message.statusList.1'),
      value: '1'
    },
    {
      label: t('message.statusList.0'),
      value: '0'
    }
  ]

  const formConfigList = [
    {
      is: INPUTNUMBER,
      name: 'lowBattery',
      label: t('message.chargeConfig.lowBattery'),
      min: 0,
      max: 100,
      precision: 0,
      rules: [isRequire()]
    },
    {
      is: INPUTNUMBER,
      name: 'highBattery',
      label: t('message.chargeConfig.highBattery'),
      min: 0,
      max: 100,
      precision: 0,
      rules: [isRequire()]
    },
    {
      is: INPUTNUMBER,
      name: 'minBatteryValue',
      label: t('message.chargeConfig.minBatteryValue'),
      min: 0,
      max: 100,
      precision: 0,
      rules: [isRequire()]
    },
    {
      is: INPUTNUMBER,
      name: 'minChargeTime',
      label: t('message.chargeConfig.minChargeTime'),
      min: 0,
      max: 10000000,
      precision: 0,
      rules: [isRequire()]
    },
    {
      is: SELECT,
      name: 'chargeTaskTypeId',
      label: 'message.chargeConfig.createTask',
      allowClear: true,
      options: []
    },
    {
      is: SELECT,
      name: 'autoCharge',
      label: 'message.status',
      isTranslated: true,
      options: [
        { label: 'message.statusList.0', value: 0 },
        { label: 'message.statusList.1', value: 1 }
      ]
    }
  ]

  return {
    columns,
    chargeEnableOptions,
    formConfigList
  }
}
