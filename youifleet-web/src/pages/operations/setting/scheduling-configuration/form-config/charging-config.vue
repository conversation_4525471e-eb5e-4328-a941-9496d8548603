<template>
  <div class="wrap">
    <div class="select-wrap">
      <div class="select-wrap-inner">
        <a-select
          :placeholder="t('message.vehicle')"
          v-model:value="filterState.selectedAgv"
          showSearch
          allowClear
          :options="avgOptions"
          @change="getData"></a-select>
      </div>
      <div class="select-wrap-inner">
        <a-input-number
          :placeholder="t('message.lowBattery')"
          v-model:value="filterState.lowBattery"
          :min="0"
          :max="100"
          @change="getData"></a-input-number>
      </div>
      <div class="select-wrap-inner">
        <a-input-number
          :placeholder="t('message.highBattery')"
          v-model:value="filterState.highBattery"
          :min="0"
          :max="100"
          @change="getData"></a-input-number>
      </div>
      <div class="select-wrap-inner">
        <a-select
          :placeholder="t('message.ChargingMarker')"
          v-model:value="filterState.bindChargeMarkers"
          allowClear
          showSearch
          :options="chargeMarkerOptions"
          @change="getData"></a-select>
      </div>
      <div class="select-wrap-inner">
        <a-select
          :placeholder="t('message.chargeConfig.createTask')"
          v-model:value="filterState.chargeTaskTypeId"
          allowClear
          :options="chargeTaskOptions"
          @change="getData"></a-select>
      </div>
      <div class="select-wrap-inner">
        <a-select
          :placeholder="t('message.status')"
          v-model:value="filterState.autoCharge"
          allowClear
          :options="chargeEnableOptions"
          @change="getData"></a-select>
      </div>
    </div>
    <div class="table-wrap" :style="{ height: tableHeight + 'px' }">
      <a-table
        :row-selection="{ selectedRowKeys: formState.selectedRowKeys, onChange: onSelectChange }"
        :data-source="data"
        :columns="columns"
        :scroll="{ x: '100%', y: '800px' }"
        :customRow="customRow"
        :pagination="false">
        <template #bodyCell="{ text, column, record }">
          <!-- 充电点 -->
          <template v-if="column.dataIndex === 'bindChargeMarkers'">
            <a-popover placement="bottom">
              <template #content>
                <p>
                  {{ text }}
                </p>
              </template>
              <p class="ellipsis">
                {{ text }}
              </p>
            </a-popover>
          </template>
          <!-- 创建任务 -->
          <template v-if="column.dataIndex === 'chargeTaskTypeId'">
            {{ chargeTaskList.filter(item => item.id === text)[0]?.name }}
          </template>
          <!-- 状态 -->
          <template v-if="column.dataIndex === 'autoChargeStatus'">
            {{ t('message.statusList.' + text) }}
          </template>
          <!-- 启用/禁用 -->
          <template v-if="column.dataIndex === 'autoCharge'">
            <a v-if="text === 1" @click="changeOne(record, 'autoCharge', '0')">{{ t('message.statusList.0') }}</a>
            <a v-if="text === 0" @click="changeOne(record, 'autoCharge', '1')">{{ t('message.statusList.1') }}</a>
            <a class="margin-left" @click="openForm(record)">
              {{ t('message.edit') }}
            </a>
          </template>
        </template>
      </a-table>
    </div>
    <div class="button-wrap">
      <a-button @click="changeAll('enable')">{{ t('message.statusList.1') }}</a-button>
      <a-button @click="changeAll('disable')">{{ t('message.statusList.0') }}</a-button>
      <a-button @click="clickBatchDialog('lowBattery')">{{ t('message.chargeConfig.dialogInputList.lowBattery') }}</a-button>
      <a-button @click="clickBatchDialog('highBattery')">{{ t('message.chargeConfig.dialogInputList.highBattery') }}</a-button>
      <a-button @click="clickBatchDialog('minBatteryValue')">{{ t('message.chargeConfig.dialogInputList.minBatteryValue') }}</a-button>
      <a-button @click="clickBatchDialog('minChargeTime')">{{ t('message.chargeConfig.dialogInputList.minChargeTime') }}</a-button>
      <a-button @click="clickBatchDialog('bindChargeMarkers')">{{ t('message.chargeConfig.dialogInputList.bindChargeMarkers') }}</a-button>
      <a-button @click="clickBatchDialog('chargeTaskTypeId')">{{ t('message.chargeConfig.dialogInputList.chargeTaskTypeId') }}</a-button>
    </div>
  </div>
  <MyForm
    :title="t('message.edit')"
    :visible="formVisible"
    :formConfigList="formState.formConfigList"
    :formData="formState.formData"
    :width="620"
    @close="formVisible = false"
    @submit="changeOne(formState.formData)">
    <template #default>
      <a-form-item :label="t('message.ChargingMarker')">
        <MapSelectionPoints
          v-model:markerData="formState.formData.bindChargeMarkers"
          :type="2"
          :markerType="['ChargingMarker']"></MapSelectionPoints>
      </a-form-item>
    </template>
  </MyForm>
  <myModal
    :width="'600px'"
    class="details-dialog"
    v-model:visible="dialogVisible"
    :append-to-body="true"
    @confirm="
      () => {
        formRef.validate().then(() => {
          dialogVisible = false
          changeAll(dialogVisibleType)
        })
      }
    "
    @cancel="
      () => {
        dialogVisible = false
        getData()
      }
    ">
    <template #title>{{ t(`message.chargeConfig.dialogInputList.${dialogVisibleType}`) }}</template>
    <a-form ref="formRef" :model="dialogFormState">
      <div v-if="dialogVisibleType === 'lowBattery'" class="dialogInput">
        <a-form-item name="lowBattery" :rules="[isRequire()]">
          <a-input-number
            :placeholder="t('message.lowBattery')"
            v-model:value="dialogFormState.lowBattery"
            :min="0"
            :max="99"></a-input-number>
        </a-form-item>
      </div>

      <div v-else-if="dialogVisibleType === 'highBattery'" class="dialogInput">
        <a-form-item name="highBattery" :rules="[isRequire()]">
          <a-input-number
            :placeholder="t('message.highBattery')"
            v-model:value="dialogFormState.highBattery"
            :min="0"
            :max="99"></a-input-number>
        </a-form-item>
      </div>
      <div v-else-if="dialogVisibleType === 'minBatteryValue'" class="dialogInput">
        <a-form-item name="minBatteryValue" :rules="[isRequire()]">
          <a-input-number
            :placeholder="t('message.chargeConfig.minBatteryValue')"
            v-model:value="dialogFormState.minBatteryValue"
            :min="0"
            :max="99"></a-input-number>
        </a-form-item>
      </div>
      <div v-else-if="dialogVisibleType === 'minChargeTime'" class="dialogInput">
        <a-form-item name="minChargeTime" :rules="[isRequire()]">
          <a-input-number
            :placeholder="t('message.chargeConfig.minChargeTime')"
            v-model:value="dialogFormState.minChargeTime"
            :min="0"
            :max="9999"></a-input-number>
        </a-form-item>
      </div>
      <div v-else-if="dialogVisibleType === 'bindChargeMarkers'">
        <!-- 点位组件 -->
        <MapSelectionPoints
          v-model:markerData="dialogFormState.bindChargeMarkers"
          :type="2"
          :markerType="['ChargingMarker']"></MapSelectionPoints>
      </div>
      <div v-else-if="dialogVisibleType === 'chargeTaskTypeId'" class="dialogInput">
        <a-select
          v-model:value="dialogFormState.chargeTaskTypeId"
          :placeholder="t('message.pleaseSelect')"
          :options="chargeTaskOptions"
          allowClear></a-select>
      </div>
    </a-form>
  </myModal>
</template>
<script lang="ts" setup>
import { postVehicleList, putVehicleUpdate, putVehicleUpdateBatch } from '@/api/vehicle'
import { getMapMarkersChargeMarkerList } from '@/api/map'
import { getTaskTypeList } from '@/api/task'
import useMapSelectionPoints from '@/hooks/use-map-selection-points'
import type { IBaseMarkerData } from '@/hooks/use-map-selection-points'
import { getOperateText, messageTip } from '@/utils'
import { IFormItemType, useLocale, deepClone } from 'youibot-plus'
import useChargeConfig from './use-charge-config'
import { isRequire } from '@/utils/form-rules'
type Key = string | number
const { t } = useLocale()
const { columns, chargeEnableOptions, formConfigList } = useChargeConfig()
const { translateArrayToMarkers } = useMapSelectionPoints()

/* 筛选 */
let chargeTaskOptions = ref()
let chargeMarkerOptions = ref()
let avgOptions = ref([] as any[])
const filterState = reactive({
  selectedAgv: undefined,
  lowBattery: undefined,
  highBattery: undefined,
  bindChargeMarkers: undefined,
  chargeTaskTypeId: undefined,
  autoCharge: undefined
})

/* 表格和表单 */
const formVisible = ref(false) //表单显示
const tableHeight = ref(0)
let chargeTaskList = ref([] as any[]) // 任务类型数组
const data = ref() //机器人列表的数据
// 表单
const formState = reactive<{
  selectedRowKeys: Key[]
  formConfigList: IFormItemType[]
  formData: {
    lowBattery: number
    highBattery: number
    minBatteryValue: number
    minChargeTime: number
    chargeTaskTypeId?: string
    autoCharge?: string
    bindChargeMarkers?: IBaseMarkerData[]
  }
}>({
  selectedRowKeys: [],
  formConfigList,
  formData: {
    lowBattery: 20,
    highBattery: 80,
    minBatteryValue: 40,
    minChargeTime: 20,
    chargeTaskTypeId: undefined,
    autoCharge: '0',
    bindChargeMarkers: []
  }
})
const getTableHeight = () => {
  const wrap = document.querySelector('.wrap')
  const selectWrap = document.querySelector('.select-wrap')
  const buttonWrap = document.querySelector('.button-wrap')
  const tableHeaderM = document.querySelector('.ant-table-header')
  const tableWrap = document.querySelector('.table-wrap')

  if (wrap && selectWrap && buttonWrap && tableHeaderM && tableWrap) {
    // tableHeight.value = wrap.clientHeight - selectWrap.clientHeight - buttonWrap.clientHeight
    const computedStyle = window.getComputedStyle(selectWrap, null)
    const marginBottom = parseInt(computedStyle.marginBottom, 10)
    const tableWrapStyle = window.getComputedStyle(tableWrap, null)
    const tableWrapMarginBottom = parseInt(tableWrapStyle.marginBottom, 10)

    tableHeight.value =
      wrap.getBoundingClientRect().height -
      selectWrap.getBoundingClientRect().height -
      buttonWrap.getBoundingClientRect().height -
      marginBottom -
      tableWrapMarginBottom
  }
}
onMounted(() => {
  getTableHeight()
})
const getData = () => {
  postVehicleList().then(res => {
    //组装agv下拉筛选数据
    avgOptions.value = []
    res.data.forEach((item: any) => {
      avgOptions.value.push({
        label: item.vehicleCode,
        value: item.vehicleCode
      })
    })

    //根据筛选值筛选数组
    data.value = res.data.filter((item: any) => {
      return (
        (filterState.selectedAgv ? item.vehicleCode == filterState.selectedAgv : true) &&
        (filterState.lowBattery ? item.lowBattery == filterState.lowBattery : true) &&
        (filterState.highBattery ? item.highBattery == filterState.highBattery : true) &&
        (filterState.bindChargeMarkers
          ? item.bindChargeMarkers && item.bindChargeMarkers.split(',').indexOf(filterState.bindChargeMarkers) != -1
          : true) &&
        (filterState.chargeTaskTypeId ? item.chargeTaskTypeId == filterState.chargeTaskTypeId : true) &&
        (filterState.autoCharge ? item.autoCharge == filterState.autoCharge : true)
      )
    })

    // 给单个input赋值
    data.value.forEach((item: any, index: number) => {
      data.value[index]['key'] = item.id
      data.value[index].autoChargeStatus = item.autoCharge
    })
  })
}

getData()

getTaskTypeList({ publishStatus: 'Published' }).then((res: any) => {
  chargeTaskList.value = res.data
  chargeTaskOptions.value = res.data.map((data: any) => {
    return {
      value: data.id,
      label: data.name
    }
  })
  formState.formConfigList[4].options = chargeTaskOptions.value
})
getMapMarkersChargeMarkerList({}).then((res: any) => {
  chargeMarkerOptions.value = res.data.map((data: any) => {
    return {
      value: data.code,
      label: data.name ? data.name : data.code
    }
  })
})

const openForm = (row: any) => {
  formState.formData = deepClone(row)
  formState.formData['bindChargeMarkers'] = deepClone(row).bindChargeMarkers
    ? translateArrayToMarkers(deepClone(row).bindChargeMarkers.split(','))
    : []
  formVisible.value = true
}

const onSelectChange = (selectedRowKeys: Key[]) => {
  formState.selectedRowKeys = selectedRowKeys
}
const customRow = (record: any) => {
  return {
    onDblclick: () => {
      openForm(record)
    }
  }
}
/**
 * 修改单行的值
 * @param rowData 当前行的数据
 * @param key 修改单行的单个数据
 * @param value 改变的值
 */
const changeOne = function <T>(rowData: {}, key?: string, value?: string | Array<T>) {
  const data = deepClone(rowData)
  if (!key) {
    data['bindChargeMarkers'] = data['bindChargeMarkers']
      .map((item: any) => {
        return item.code
      })
      .join()
  } else {
    data[key] = value
  }
  putVehicleUpdate(data)
    .then(res => {
      getOperateText(t, {
        type: key === 'autoCharge' ? (value === '0' ? 'statusList.0' : 'statusList.1') : 'update'
      })
      getData()
      formVisible.value = false
    })
    .catch(err => {
      getOperateText(t, {
        type: key === 'autoCharge' ? (value === '0' ? 'statusList.0' : 'statusList.1') : 'update',
        result: 'error',
        reason: err.msg
      })
      formVisible.value = false
    })
}

/* 按钮弹窗 */
let dialogVisible = ref(false)
let dialogVisibleType = ref('')
// 弹窗内的输入框值
const dialogFormState = reactive({
  lowBattery: 20,
  highBattery: 80,
  minBatteryValue: 40,
  minChargeTime: 20,
  bindChargeMarkers: [],
  chargeTaskTypeId: undefined
})
const formRef = ref()

// 打开批量修改的弹窗
const clickBatchDialog = (type: string) => {
  if (!formState.selectedRowKeys.length) {
    messageTip(t('message.pleaseSelectOneVehicle'), 'warning')
    return
  }
  dialogFormState.bindChargeMarkers = []
  dialogVisible.value = true
  dialogVisibleType.value = type
}
//修改所有值
const changeAll = (type: string) => {
  if (!formState.selectedRowKeys.length) {
    messageTip(t('message.pleaseSelectOneVehicle'), 'warning')
    return
  }
  let arr = []
  for (let i = 0; i < data.value.length; i++) {
    for (let j = 0; j < formState.selectedRowKeys.length; j++) {
      if (data.value[i].id === formState.selectedRowKeys[j]) {
        arr.push(data.value[i])
      }
    }
  }
  switch (type) {
    case 'enable':
      arr.map(item => (item['autoCharge'] = 1))
      break
    case 'disable':
      arr.map(item => (item['autoCharge'] = 0))
      break
    case 'lowBattery':
      arr.map(item => (item['lowBattery'] = dialogFormState.lowBattery))
      break
    case 'highBattery':
      arr.map(item => (item['highBattery'] = dialogFormState.highBattery))
      break
    case 'minBatteryValue':
      arr.map(item => (item['minBatteryValue'] = dialogFormState.minBatteryValue))
      break
    case 'minChargeTime':
      arr.map(item => (item['minChargeTime'] = dialogFormState.minChargeTime))
      break
    case 'bindChargeMarkers':
      arr.map(
        item =>
          (item['bindChargeMarkers'] = dialogFormState.bindChargeMarkers
            .map((item: any) => {
              return item.code
            })
            .join())
      )
      break
    case 'chargeTaskTypeId':
      arr.map(item => (item['chargeTaskTypeId'] = dialogFormState.chargeTaskTypeId))
      break
    default:
      break
  }
  putVehicleUpdateBatch(arr)
    .then(() => {
      getOperateText(t, {
        type: 'update',
        isBatch: formState.selectedRowKeys.length >= 2 ? true : false
      })
      getData()
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg,
        isBatch: formState.selectedRowKeys.length >= 2 ? true : false
      })
    })
}
</script>
<style lang="less" scoped>
.margin-left {
  margin-left: 16px;
}

.ant-select,
.ant-input-number {
  width: 180px;
}

.dialogInput {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 20px 0;
}

.wrap {
  min-width: 800px;
  height: calc(100% - 65px);
  margin: 10px 0 10px 16px;

  .select-wrap {
    display: flex;
    width: 100%;
    margin-bottom: 10px;

    &-inner {
      display: inline-block;
      margin-right: 20px;
    }
  }

  .table-wrap {
    width: 100%;
    margin-bottom: 10px;
    overflow-y: auto;
    border: 1px solid #e7e7e7;
  }

  .button-wrap {
    .ant-btn {
      margin-right: 5px;
    }
  }

  a {
    color: rgb(16 142 233);
  }
}
</style>
