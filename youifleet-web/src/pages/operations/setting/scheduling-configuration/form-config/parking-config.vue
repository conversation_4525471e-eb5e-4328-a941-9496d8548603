<template>
  <div class="wrap">
    <div class="select-wrap">
      <div class="select-wrap-inner">
        <a-select
          :placeholder="t('message.vehicle')"
          v-model:value="filterState.selectedAgv"
          allowClear
          showSearch
          :options="avgOptions"
          @change="getData"></a-select>
      </div>
      <div class="select-wrap-inner">
        <a-select
          :placeholder="t('message.ParkingMarker')"
          v-model:value="filterState.bindParkMarkers"
          allowClear
          showSearch
          :options="parkMarkerOptions"
          @change="getData"></a-select>
      </div>
      <div class="select-wrap-inner">
        <a-select
          :placeholder="t('message.parkConfig.createTask')"
          v-model:value="filterState.parkTaskTypeId"
          allowClear
          :options="parkTaskOptions"
          @change="getData"></a-select>
      </div>
      <div class="select-wrap-inner">
        <a-select
          :placeholder="t('message.status')"
          v-model:value="filterState.autoPark"
          allowClear
          :options="parkEnableOptions"
          @change="getData"></a-select>
      </div>
    </div>
    <div class="table-wrap" :style="{ height: tableHeight + 'px' }">
      <a-table
        :row-selection="{ selectedRowKeys: formState.selectedRowKeys, onChange: onSelectChange }"
        :data-source="data"
        :columns="columns"
        :scroll="{ x: '100%', y: tableHeight }"
        :customRow="customRow"
        :pagination="false">
        <template #bodyCell="{ text, column, record }">
          <!-- 泊车点 -->
          <template v-if="column.dataIndex === 'bindParkMarkers'">
            <a-popover placement="bottom">
              <template #content>
                <p>
                  {{ text }}
                </p>
              </template>
              <p class="ellipsis">
                {{ text }}
              </p>
            </a-popover>
          </template>
          <!-- 创建任务 -->
          <template v-if="column.dataIndex === 'parkTaskTypeId'">
            {{ parkTaskList.filter(item => item.id === text)[0]?.name }}
          </template>
          <!-- 状态 -->
          <template v-if="column.dataIndex === 'autoParkStatus'">
            {{ t('message.statusList.' + text) }}
          </template>
          <!-- 启用/禁用 -->
          <template v-if="column.dataIndex === 'autoPark'">
            <a v-if="text === 1" @click="changeOne(record, 'autoPark', '0')">{{ t('message.statusList.0') }}</a>
            <a v-if="text === 0" @click="changeOne(record, 'autoPark', '1')">{{ t('message.statusList.1') }}</a>
            <a class="margin-left" @click="openForm(record)">
              {{ t('message.edit') }}
            </a>
          </template>
        </template>
      </a-table>
    </div>
    <div class="button-wrap">
      <a-button @click="changeAll('enable')">{{ t('message.statusList.1') }}</a-button>
      <a-button @click="changeAll('disable')">{{ t('message.statusList.0') }}</a-button>
      <a-button @click="clickBatchDialog('bindParkMarkers')">{{ t('message.parkConfig.dialogInputList.bindParkMarkers') }}</a-button>
      <a-button @click="clickBatchDialog('parkTaskTypeId')">{{ t('message.parkConfig.dialogInputList.parkTaskTypeId') }}</a-button>
    </div>
  </div>
  <MyForm
    :title="t('message.edit')"
    :visible="formVisible"
    :formConfigList="formState.formConfigList"
    :formData="formState.formData"
    @close="formVisible = false"
    @submit="changeOne(formState.formData)">
    <template #default>
      <a-form-item :label="t('message.ParkingMarker')">
        <MapSelectionPoints v-model:markerData="formState.formData.bindParkMarkers" :isPark="true" :type="2"></MapSelectionPoints>
      </a-form-item>
    </template>
  </MyForm>
  <myModal
    :width="'600px'"
    class="details-dialog"
    v-model:visible="dialogVisible"
    :append-to-body="true"
    @confirm="
      () => {
        dialogVisible = false
        changeAll(dialogVisibleType)
      }
    "
    @cancel="
      () => {
        dialogVisible = false
        getData()
      }
    ">
    <template #title>{{ t(`message.parkConfig.dialogInputList.${dialogVisibleType}`) }}</template>
    <div v-if="dialogVisibleType === 'bindParkMarkers'">
      <!-- 点位组件 -->
      <MapSelectionPoints v-model:markerData="dialogFormState.bindParkMarkers" :isPark="true" :type="2"></MapSelectionPoints>
    </div>
    <div v-else-if="dialogVisibleType === 'parkTaskTypeId'" class="dialogInput">
      <a-select
        v-model:value="dialogFormState.parkTaskTypeId"
        :placeholder="t('message.pleaseSelect')"
        :options="parkTaskOptions"
        style="width: 100%"
        allowClear></a-select>
    </div>
  </myModal>
</template>
<script lang="ts" setup>
import { postVehicleList, putVehicleUpdate, putVehicleUpdateBatch } from '@/api/vehicle'
import { getMapMarkersParkMarkerList } from '@/api/map'
import { getTaskTypeList } from '@/api/task'
import useMapSelectionPoints from '@/hooks/use-map-selection-points'
import type { IBaseMarkerData } from '@/hooks/use-map-selection-points'
import { getOperateText, messageTip } from '@/utils'
import { IFormItemType, useLocale, deepClone } from 'youibot-plus'
import useParkConfig from './use-park-config'

type Key = string | number
const { t } = useLocale()
const { columns, parkEnableOptions, formConfigList } = useParkConfig()
const { translateArrayToMarkers } = useMapSelectionPoints()

/* 筛选 */
let parkTaskOptions = ref()
let parkMarkerOptions = ref()
let avgOptions = ref([] as any[])
// 筛选值
const filterState = reactive({
  selectedAgv: undefined,
  bindParkMarkers: undefined,
  parkTaskTypeId: undefined,
  autoPark: undefined
})

/* 表格和表单 */
const data = ref() //机器人列表的数据
const formVisible = ref(false) //表单显示
let parkTaskList = ref([] as any[]) // 任务类型数组
const tableHeight = ref(0)
// 表单
const formState = reactive<{
  selectedRowKeys: Key[]
  formConfigList: IFormItemType[]
  formData: {
    parkTaskTypeId?: string
    autoPark?: string
    bindParkMarkers?: IBaseMarkerData[]
  }
}>({
  selectedRowKeys: [],
  formConfigList,
  formData: {
    parkTaskTypeId: undefined,
    autoPark: '0',
    bindParkMarkers: []
  }
})
const getTableHeight = () => {
  const wrap = document.querySelector('.wrap')
  const selectWrap = document.querySelector('.select-wrap')
  const buttonWrap = document.querySelector('.button-wrap')
  const tableHeaderM = document.querySelector('.ant-table-header')
  const tableWrap = document.querySelector('.table-wrap')

  if (wrap && selectWrap && buttonWrap && tableHeaderM && tableWrap) {
    // tableHeight.value = wrap.clientHeight - selectWrap.clientHeight - buttonWrap.clientHeight
    const computedStyle = window.getComputedStyle(selectWrap, null)
    const marginBottom = parseInt(computedStyle.marginBottom, 10)
    const tableWrapStyle = window.getComputedStyle(tableWrap, null)
    const tableWrapMarginBottom = parseInt(tableWrapStyle.marginBottom, 10)

    tableHeight.value =
      wrap.getBoundingClientRect().height -
      selectWrap.getBoundingClientRect().height -
      buttonWrap.getBoundingClientRect().height -
      marginBottom -
      tableWrapMarginBottom
  }
}
onMounted(() => {
  getTableHeight()
})
const getData = () => {
  postVehicleList().then(res => {
    //组装agv下拉筛选数据
    avgOptions.value = []
    res.data.forEach((item: any) => {
      avgOptions.value.push({
        label: item.vehicleCode,
        value: item.vehicleCode
      })
    })

    //根据筛选值筛选数组
    data.value = res.data.filter((item: any) => {
      return (
        (filterState.selectedAgv ? item.vehicleCode == filterState.selectedAgv : true) &&
        (filterState.parkTaskTypeId ? item.parkTaskTypeId == filterState.parkTaskTypeId : true) &&
        (filterState.bindParkMarkers
          ? item.bindParkMarkers && item.bindParkMarkers.split(',').indexOf(filterState.bindParkMarkers) != -1
          : true) &&
        (filterState.autoPark ? item.autoPark == filterState.autoPark : true)
      )
    })
    // 给单个input赋值
    data.value.forEach((item: any, index: number) => {
      data.value[index]['key'] = item.id
      data.value[index].autoParkStatus = item.autoPark
    })
  })
}

getData()

getTaskTypeList({ publishStatus: 'Published' }).then((res: any) => {
  parkTaskList.value = res.data
  parkTaskOptions.value = res.data.map((data: any) => {
    return {
      value: data.id,
      label: data.name
    }
  })
  formState.formConfigList[0].options = parkTaskOptions.value
})

getMapMarkersParkMarkerList({}).then((res: any) => {
  parkMarkerOptions.value = res.data.map((data: any) => {
    return {
      value: data.code,
      label: data.name ? data.name : data.code
    }
  })
})

const openForm = (row: any) => {
  formState.formData = deepClone(row)
  formState.formData['bindParkMarkers'] = deepClone(row).bindParkMarkers
    ? translateArrayToMarkers(deepClone(row).bindParkMarkers.split(','))
    : []
  formVisible.value = true
}

const onSelectChange = (selectedRowKeys: Key[]) => {
  formState.selectedRowKeys = selectedRowKeys
}

/**
 * 修改单行的值
 * @param rowData 当前行的数据
 * @param key 修改单行的单个数据
 * @param value 改变的值
 */
const changeOne = function <T>(rowData: {}, key?: string, value?: string | Array<T>) {
  const data = deepClone(rowData)
  if (!key) {
    data['bindParkMarkers'] = data['bindParkMarkers']
      .map((item: any) => {
        return item.code
      })
      .join()
  } else {
    data[key] = value
  }
  putVehicleUpdate(data)
    .then(res => {
      getOperateText(t, {
        type: key === 'autoPark' ? (value === '0' ? 'statusList.0' : 'statusList.1') : 'update'
      })
      getData()
      formVisible.value = false
    })
    .catch(err => {
      getOperateText(t, {
        type: key === 'autoPark' ? (value === '0' ? 'statusList.0' : 'statusList.1') : 'update',
        result: 'error',
        reason: err.msg
      })
      formVisible.value = false
    })
}

/* 按钮弹窗 */
let dialogVisible = ref(false)
let dialogVisibleType = ref('')
// 弹窗内的输入框值
const dialogFormState = reactive({
  bindParkMarkers: [],
  parkTaskTypeId: undefined
})

// 打开批量修改的弹窗
const clickBatchDialog = (type: string) => {
  if (!formState.selectedRowKeys.length) {
    messageTip(t('message.pleaseSelectOneVehicle'), 'warning')
    return
  }
  dialogFormState.bindParkMarkers = []
  dialogVisible.value = true
  dialogVisibleType.value = type
}
const customRow = (record: any) => {
  return {
    onDblclick: () => {
      openForm(record)
    }
  }
}
//修改所有值
const changeAll = (type: string) => {
  if (!formState.selectedRowKeys.length) {
    messageTip(t('message.pleaseSelectOneVehicle'), 'warning')
    return
  }
  let arr = []
  for (let i = 0; i < data.value.length; i++) {
    for (let j = 0; j < formState.selectedRowKeys.length; j++) {
      if (data.value[i].id === formState.selectedRowKeys[j]) {
        arr.push(data.value[i])
      }
    }
  }
  switch (type) {
    case 'enable':
      arr.map(item => (item['autoPark'] = 1))
      break
    case 'disable':
      arr.map(item => (item['autoPark'] = 0))
      break
    case 'bindParkMarkers':
      arr.map(
        item =>
          (item['bindParkMarkers'] = dialogFormState.bindParkMarkers
            .map((item: any) => {
              return item.code
            })
            .join())
      )
      break
    case 'parkTaskTypeId':
      arr.map(item => (item['parkTaskTypeId'] = dialogFormState.parkTaskTypeId))
      break
    default:
      break
  }
  putVehicleUpdateBatch(arr)
    .then(() => {
      getOperateText(t, {
        type: 'update',
        isBatch: formState.selectedRowKeys.length >= 2 ? true : false
      })
      getData()
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg,
        isBatch: formState.selectedRowKeys.length >= 2 ? true : false
      })
    })
}
</script>
<style lang="less" scoped>
.margin-left {
  margin-left: 16px;
}

.ant-select,
.ant-input-number {
  width: 180px;
}

.dialogInput {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 20px 0;
}

.editable-cell-icon {
  padding-top: 3px;
}

.wrap {
  min-width: 800px;
  height: calc(100% - 65px);
  margin: 10px 0 10px 16px;

  .select-wrap {
    width: 100%;
    margin-bottom: 10px;

    &-inner {
      display: inline-block;
      margin-right: 20px;
    }
  }

  .table-wrap {
    width: 100%;
    // height: calc(100% - 90px - 70px);
    margin-bottom: 10px;
    overflow-y: auto;
    border: 1px solid #e7e7e7;
  }

  .button-wrap {
    .ant-btn {
      margin-right: 5px;
    }
  }

  a {
    color: rgb(16 142 233);
  }

  .editable-cell-text-wrapper {
    display: flex;
    justify-content: space-between;
  }
}
</style>
