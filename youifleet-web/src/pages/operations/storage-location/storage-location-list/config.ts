import type { IBtnType, IFormItemType } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, INPUTNUMBER, RANGEPICKER, SELECT, SWITCH } from 'youibot-plus'
import { isRequire } from '@/utils/form-rules'

export interface ITableRow {
  creatorId: number
  creatorName: string
  id: number
  code: string
  warehouseAreaCode: number
  warehouseTypeCode: number
  rowNum: number
  columnNum: number
  layerNum: number
  workHeight: number
  workMarkerCode: string
  occupyStatus: string
  containerBarcode: string
  creator: string
  createDate: string
  updateDate: string
  isEdit: boolean
  warehouseTypeName: string
  warehouseAreaName: string
  extendParam1: string
  extendParam2: string
  extendParam3: string
  extendParam4: string
  extendParam5: string
  extendParam6: string
  extendParam7: string
  extendParam8: string
  extendParam9: string
  extendParam10: string
  usageStatus: string
  lastStatusUpdateDate: string
}

//table操作栏
const tableOptions: IBtnType[] = [
  {
    name: 'message.edit',
    type: 'link',
    permission: 'edit'
  }
]

//table 配置
const columns: TableColumnsType = [
  {
    // 编码
    title: 'message.encoding',
    dataIndex: 'code',
    width: 200
  },

  {
    // 类型
    title: 'message.type',
    dataIndex: 'warehouseTypeName',
    width: 150
  },

  {
    // 库区
    title: 'message.storageLocation.reservoirArea',
    dataIndex: 'warehouseAreaName',
    width: 150
  },
  {
    // 排
    title: 'message.storageLocation.row',
    dataIndex: 'rowNum',
    width: 80
  },
  {
    // 列
    title: 'message.storageLocation.column',
    dataIndex: 'columnNum',
    width: 80
  },
  {
    // 层
    title: 'message.storageLocation.layer',
    dataIndex: 'layerNum',
    width: 80
  },
  {
    // 作业高度
    title: 'message.storageLocation.operatingHeight',
    dataIndex: 'workHeight',
    width: 120
  },
  {
    // 作业点位
    title: 'message.storageLocation.jobPoint',
    key: 'workMarkerCode',
    width: 160
  },
  {
    // 占用状态
    title: 'message.storageLocation.occupiedState',
    key: 'occupyStatus',
    width: 120
  },
  {
    // 启用状态
    title: 'message.storageLocation.enabledState',
    key: 'usageStatus',
    width: 120
  },
  {
    // 容器条码
    title: 'message.storageLocation.containerBarCode',
    dataIndex: 'containerBarcode',
    width: 120
  },
  {
    // 扩展属性1
    title: 'message.extendedAttribute1',
    dataIndex: 'extendParam1',
    width: 130
  },
  {
    // 扩展属性2
    title: 'message.extendedAttribute2',
    dataIndex: 'extendParam2',
    width: 130
  },
  {
    // 扩展属性3
    title: 'message.extendedAttribute3',
    dataIndex: 'extendParam3',
    width: 130
  },
  {
    // 扩展属性4
    title: 'message.extendedAttribute4',
    dataIndex: 'extendParam4',
    width: 130
  },
  {
    // 扩展属性5
    title: 'message.extendedAttribute5',
    dataIndex: 'extendParam5',
    width: 130
  },
  {
    // 扩展属性6
    title: 'message.extendedAttribute6',
    dataIndex: 'extendParam6',
    width: 130
  },
  {
    // 扩展属性7
    title: 'message.extendedAttribute7',
    dataIndex: 'extendParam7',
    width: 130
  },
  {
    // 扩展属性8
    title: 'message.extendedAttribute8',
    dataIndex: 'extendParam8',
    width: 130
  },
  {
    // 扩展属性9
    title: 'message.extendedAttribute9',
    dataIndex: 'extendParam9',
    width: 130
  },
  {
    // 扩展属性10
    title: 'message.extendedAttribute10',
    dataIndex: 'extendParam10',
    width: 135
  },
  {
    // 创建人
    title: 'message.founder',
    dataIndex: 'creatorName',
    width: 140
  },
  {
    // 创建时间
    title: 'message.createTime',
    key: 'createDate',
    width: 200
  },
  {
    // 修改时间
    title: 'message.updateTime',
    key: 'updateDate',
    width: 200
  },
  {
    // 库存更新时间
    title: 'message.storageUpdateTime',
    key: 'lastStatusUpdateDate',
    width: 200
  },
  {
    title: 'message.options',
    key: 'options',
    fixed: 'right'
  }
]
const occupyStatus = [
  { label: 'message.storageLocation.occupyStatus.Lock', value: 'Lock' },
  { label: 'message.storageLocation.occupyStatus.Free', value: 'Free' },
  { label: 'message.storageLocation.occupyStatus.Store', value: 'Store' }
]
const usageStatus = [
  { label: 'message.storageLocation.usageStatus.Enable', value: 'Enable' },
  { label: 'message.storageLocation.usageStatus.Disable', value: 'Disable' }
]
// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    // 编码
    is: INPUT,
    name: 'code',
    label: 'message.encoding',
    allowClear: true
  },

  {
    // 类型
    is: SELECT,
    name: 'warehouseTypeCode',
    label: 'message.type',
    fieldNames: { label: 'name', value: 'code' },
    allowClear: true,
    options: []
  },
  {
    // 库区
    is: SELECT,
    name: 'warehouseAreaCode',
    label: 'message.storageLocation.reservoirArea',
    fieldNames: { label: 'name', value: 'code' },
    allowClear: true,
    options: []
  },
  {
    // 排
    is: INPUT,
    name: 'rowNum',
    label: 'message.storageLocation.row',
    allowClear: true
  },

  {
    // 列
    is: INPUT,
    name: 'columnNum',
    label: 'message.storageLocation.column',
    allowClear: true
  },
  {
    // 层
    is: INPUT,
    name: 'layerNum',
    label: 'message.storageLocation.layer',
    allowClear: true
  },
  {
    // 作业高度
    is: INPUT,
    name: 'workHeight',
    label: 'message.storageLocation.operatingHeight',
    allowClear: true
  },
  {
    // 作业点位
    is: INPUT,
    name: 'workMarkerCode',
    label: 'message.storageLocation.jobPoint',
    allowClear: true
  },
  {
    // 占用状态
    is: SELECT,
    name: 'occupyStatus',
    label: 'message.storageLocation.occupiedState',
    allowClear: true,
    options: occupyStatus,
    isTranslated: true
  },
  {
    // 容器条码
    is: INPUT,
    name: 'containerBarcode',
    label: 'message.storageLocation.containerBarCode',
    allowClear: true
  },
  {
    // 扩展属性1
    is: INPUT,
    name: 'extendParam1',
    label: 'message.extendedAttribute1',
    allowClear: true
  },
  {
    // 扩展属性2
    is: INPUT,
    name: 'extendParam2',
    label: 'message.extendedAttribute2',
    allowClear: true
  },
  {
    // 扩展属性3
    is: INPUT,
    name: 'extendParam3',
    label: 'message.extendedAttribute3',
    allowClear: true
  },
  {
    // 扩展属性4
    is: INPUT,
    name: 'extendParam4',
    label: 'message.extendedAttribute4',
    allowClear: true
  },
  {
    // 扩展属性5
    is: INPUT,
    name: 'extendParam5',
    label: 'message.extendedAttribute5',
    allowClear: true
  },
  {
    // 扩展属性6
    is: INPUT,
    name: 'extendParam6',
    label: 'message.extendedAttribute6',
    allowClear: true
  },
  {
    // 扩展属性7
    is: INPUT,
    name: 'extendParam7',
    label: 'message.extendedAttribute7',
    allowClear: true
  },
  {
    // 扩展属性8
    is: INPUT,
    name: 'extendParam8',
    label: 'message.extendedAttribute8',
    allowClear: true
  },
  {
    // 扩展属性9
    is: INPUT,
    name: 'extendParam9',
    label: 'message.extendedAttribute9',
    allowClear: true
  },
  {
    // 扩展属性10
    is: INPUT,
    name: 'extendParam10',
    label: 'message.extendedAttribute10',
    allowClear: true
  },
  {
    // 创建人
    is: INPUT,
    name: 'creatorName',
    label: 'message.founder',
    allowClear: true
  },
  {
    // 创建时间
    is: RANGEPICKER,
    name: 'createDate',
    allowClear: true,
    label: 'message.createTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  },
  {
    // 修改时间
    is: RANGEPICKER,
    name: 'updateDate',
    allowClear: true,
    label: 'message.updateTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  },
  {
    // 库存更新时间
    is: RANGEPICKER,
    name: 'lastStatusUpdateDate',
    allowClear: true,
    label: 'message.storageUpdateTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  }
]

//底部按钮
const btnList: IBtnType[] = [
  {
    name: 'message.add',
    type: 'primary',
    icon: 'iconfont icon-zengjia',
    permission: 'add'
  },
  {
    name: 'message.del',
    icon: 'iconfont icon-xiazai14',
    permission: 'del'
  },
  {
    name: 'message.multImport',
    permission: 'import'
  },
  {
    name: 'message.multExport',
    permission: 'export'
  }
]

const formConfigList: IFormItemType[] = [
  {
    // 作业高度
    is: INPUTNUMBER,
    name: 'workHeight',
    label: 'message.storageLocation.operatingHeight',
    min: 0,
    max: 9999
  },
  {
    // 作业点位
    is: INPUTNUMBER,
    slotName: 'workMarkerCode',
    label: 'message.storageLocation.jobPoint',
    name: 'workMarkerCode'
  },
  {
    // 占用状态
    is: SELECT,
    name: 'occupyStatus',
    label: 'message.storageLocation.occupiedState',
    options: occupyStatus,
    isTranslated: true
  },
  {
    // 启用状态
    is: SELECT,
    name: 'usageStatus',
    label: 'message.storageLocation.enabledState',
    options: usageStatus,
    isTranslated: true
  },
  {
    // 容器条码
    is: INPUT,
    name: 'containerBarcode',
    label: 'message.storageLocation.containerBarCode'
  },
  {
    // 扩展属性1
    is: INPUT,
    name: 'extendParam1',
    label: 'message.extendedAttribute1'
  },
  {
    // 扩展属性2
    is: INPUT,
    name: 'extendParam2',
    label: 'message.extendedAttribute2'
  },
  {
    // 扩展属性3
    is: INPUT,
    name: 'extendParam3',
    label: 'message.extendedAttribute3'
  },
  {
    // 扩展属性4
    is: INPUT,
    name: 'extendParam4',
    label: 'message.extendedAttribute4'
  },
  {
    // 扩展属性5
    is: INPUT,
    name: 'extendParam5',
    label: 'message.extendedAttribute5'
  },
  {
    // 扩展属性6
    is: INPUT,
    name: 'extendParam6',
    label: 'message.extendedAttribute6'
  },
  {
    // 扩展属性7
    is: INPUT,
    name: 'extendParam7',
    label: 'message.extendedAttribute7'
  },
  {
    // 扩展属性8
    is: INPUT,
    name: 'extendParam8',
    label: 'message.extendedAttribute8'
  },
  {
    // 扩展属性9
    is: INPUT,
    name: 'extendParam9',
    label: 'message.extendedAttribute9'
  },
  {
    // 扩展属性10
    is: INPUT,
    name: 'extendParam10',
    label: 'message.extendedAttribute10'
  }
]

const columnsCheckList: { [key: string]: boolean } = {
  creatorId: true,
  creatorName: true,
  id: true,
  code: true,
  warehouseAreaCode: true,
  warehouseTypeCode: true,
  rowNum: true,
  columnNum: true,
  layerNum: true,
  workHeight: true,
  workMarkerCode: true,
  occupyStatus: true,
  containerBarcode: true,
  creator: true,
  createDate: true,
  updateDate: true,
  isEdit: true,
  warehouseTypeName: true,
  warehouseAreaName: true,
  extendParam1: true,
  extendParam2: true,
  extendParam3: true,
  extendParam4: true,
  extendParam5: true,
  extendParam6: false,
  extendParam7: false,
  extendParam8: false,
  extendParam9: false,
  extendParam10: false,
  usageStatus: true,
  lastStatusUpdateDate: true,
  options: true
}

export { columns, filterConfigList, tableOptions, btnList, formConfigList, columnsCheckList }
