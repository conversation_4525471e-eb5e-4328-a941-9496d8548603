<template>
  <MyTable
    ref="tableRef"
    id="tableStorageLocation"
    :filterConfigList="State.filterConfigList"
    :columnsCheckList="State.columnsCheckList"
    :filterData="State.filterData"
    :columns="State.columns"
    :tableOptions="State.tableOptions"
    :pagination="pageState.pagination"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    :customRow="customRow"
    @formChange="filterChange"
    @columnChange="getData"
  >
    <template #occupyStatus="{ scope }">
      <a-tag v-if="scope.record.occupyStatus" class="tag-status" :color="getStatusColor(scope.record.occupyStatus)">
        {{ t('message.storageLocation.occupyStatus.' + scope.record.occupyStatus) }}
      </a-tag>
    </template>
    <template #usageStatus="{ scope }">
      <a-tag v-if="scope.record.usageStatus" class="tag-status" :color="getStatusColor(scope.record.usageStatus)">
        {{ t('message.storageLocation.usageStatus.' + scope.record.usageStatus) }}
      </a-tag>
    </template>
    <template #workMarkerCode="{ scope }">
      <!-- <div v-show="!scope.record.isEdit" @click.stop="tableEdit(scope.record)" class="content-auto">{{ scope.record.workMarkerCode }}</div> -->
      <map-selection-points
        :markerData="getMarker(scope.record)"
        :type="1"
        :allowClear="false"
        :is-tooltip="true"
        :placeholder="'message.storageLocation.setPoint'"
        @change="(markerList: INewMarkerData[])=>{pointsChage(markerList,scope.record)}"
      ></map-selection-points>
    </template>
    <template #createDate="{ scope }">{{ setTimeStr(scope.record.createDate) }}</template>
    <template #updateDate="{ scope }">{{ setTimeStr(scope.record.updateDate) }}</template>
    <template #lastStatusUpdateDate="{ scope }">{{ setTimeStr(scope.record.lastStatusUpdateDate) }}</template>
    <!-- <template #enable_disable="{ scope }">
      <my-button
        v-if="scope.record.usageStatus === 'Disable'"
        :btnItem="{
          name: 'message.storageLocation.usageStatus.Enable',
          type: 'link',
          onClick: () => {
            stateBtn(scope.record, 'enable')
          },
          permission: 'enable_disable'
        }"></my-button>
      <my-button
        v-else
        :btnItem="{
          name: 'message.storageLocation.usageStatus.Disable',
          type: 'link',
          onClick: () => {
            stateBtn(scope.record, 'disable')
          },
          permission: 'enable_disable'
        }"></my-button>
    </template> -->
  </MyTable>
  <MyForm :visible="visible" :formConfigList="formConfigList" :formData="State.formData" @close="onClose" @submit="formSubmit">
    <template #workMarkerCode>
      <!-- <a-form-item :label="t('message.storageLocation.jobPoint')" required name="workMarker" :rules="[isRequire()]"> -->
      <map-selection-points v-model:markerData="State.formData.workMarker" :type="1"></map-selection-points>
      <!-- </a-form-item> -->
    </template>
  </MyForm>
  <my-import-file-dialog
    v-model:visible="fileDialogVisible"
    :accept="'.xlsx'"
    :title="t('message.storageLocation.importLocation')"
    :describeFileName="t('message.actionSetting.fileFormatJson')"
    :action="apiUrl + '/fleet/warehouse/uploadExcelFile'"
    @success="getListData"
  ></my-import-file-dialog>
  <storage-batch-add-form v-model:visible="visibleBatchAdd" @confirm="batchAddForm"></storage-batch-add-form>
</template>

<script setup lang="ts">
import type { IFormRules, ITableScope } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { useLocale } from 'youibot-plus'
import { columns, filterConfigList, btnList, tableOptions, formConfigList, columnsCheckList } from './config'
import type { ITableRow } from './config'
import { getOperateText, messageTip } from '@/utils'
import { putWarehouse, getWarehousePage, deleteWarehouse, putWarehouseIdDisable, putWarehouseIdEnable } from '@/api/warehouse'
import myImportFileDialog from '@/components/common/my-import-file-dialog.vue'
import { setTimeStr, downLoadFileOpen } from '@/utils/index'
import { isRequire } from '@/utils/form-rules'
import { IBaseMarkerData } from '@/hooks/use-map-selection-points'
import { getWarehouseArea } from '@/api/warehouseArea'
import { getWarehouseType } from '@/api/warehouseType'
import { INewMarkerData } from '@/hooks/use-map'
import { permission } from '@/router'
const apiUrl = `//${window.apiUrl}`
const { pageState, deleteItemConfirm, changeFilter, getData } = usePage({
  getPageFn: getWarehousePage,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  deleteBatchFn: deleteWarehouse as unknown as (params: number[]) => Promise<any>,
  deleteSuccessFn: () => {
    State.selectedRowKeys = []
  }
})
const { t } = useLocale()
const visible = ref<boolean>(false)
const fileDialogVisible = ref<boolean>(false)
const visibleBatchAdd = ref<boolean>(false)
const tableRef = ref()
const State = reactive({
  columns,
  tableOptions,
  filterData: {},
  filterConfigList,
  columnsCheckList,
  formData: {
    workMarker: [] as IBaseMarkerData[]
  },
  selectedRowKeys: [] as string[],
  formConfigList
})
const onClose = () => {
  visible.value = false
}
const rules: { [key: string]: IFormRules[] } = {
  workHeight: [isRequire()],
  workMarkerCode: [],
  occupyStatus: [isRequire()],
  usageStatus: [isRequire()],
  containerBarcode: []
}
Object.keys(rules).forEach((item, index) => {
  State.formConfigList[index].rules = rules[item]
})
//封装底部操作栏按钮
const btnHandleClick = (type: string, data: string[], value?: string) => {
  if (['add', 'import', 'export'].includes(type)) {
    switch (type) {
      // 新增
      case 'add':
        visibleBatchAdd.value = true
        break
      case 'import': //导入
        fileDialogVisible.value = true
        break
      case 'export': //导出
        downLoadFileOpen(t, '/fleet/warehouse/export', { ids: State.selectedRowKeys.join(','), ...pageState.filterData })
        break
    }
  } else {
    if (data.length <= 0) {
      messageTip(t(value ? value : 'message.selectTheDataYouWantToDelete'), 'error')
      return
    }
    switch (type) {
      // 删除
      case 'delete':
        if (State.selectedRowKeys.length <= 0) {
          messageTip(t('message.selectTheDataYouWantToDelete'), 'error')
          return
        }
        deleteItemConfirm(State.selectedRowKeys as unknown as number[], t('message.delTips'))
        break
    }
  }
}

;(function btnListClickHandler() {
  const list = ['add', 'delete', 'import', 'export']
  list.map((item, index) => {
    btnList[index].onClick = () => btnHandleClick(item, State.selectedRowKeys, btnList[index].message)
  })
})()

//封装右侧操作栏按钮
const operationHandleClick = (type: string, data: ITableScope<ITableRow>) => {
  if (!data) return
  const { record } = data
  switch (type) {
    // 编辑
    case 'edit':
      if (record) {
        const { ...rest } = record as unknown as ITableRow

        State.formData = {
          ...rest,
          workMarker: rest.workMarkerCode ? [{ vehicleMapCode: rest.workMarkerCode.split('_')[0], code: rest.workMarkerCode }] : []
        }
        visible.value = true
      }
      break
  }
}

;(function operationClickHandler() {
  const list = ['edit']
  list.map((item, index) => {
    tableOptions[index].onClick = (text?: object) => operationHandleClick(item, text as ITableScope<ITableRow>)
  })
})()
const getMarker = (record: ITableRow) => {
  const { ...rest } = record
  return rest.workMarkerCode ? [{ vehicleMapCode: rest.workMarkerCode.split('_')[0], code: rest.workMarkerCode }] : []
}
const pointsChage = (markerList: INewMarkerData[], record: ITableRow) => {
  const { ...rest } = record
  State.formData = {
    ...rest,
    workMarker: markerList
  }

  formSubmit(State.formData)
}

// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}

  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      const data = toRaw(filterData[item]) as unknown as string[]
      params[item] = data.join(',')
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const formSubmit = (formData: any) => {
  const data = { ...formData, workMarkerCode: formData.workMarker.length ? formData.workMarker[0].code : null }
  putWarehouse(data)
    .then(res => {
      visible.value = false
      getData()
      getOperateText(t, {
        type: 'update'
      })
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg
      })
    })
}
const getWarehouseAreaFn = () => {
  getWarehouseArea().then(res => {
    filterConfigList[2].options = res.data
    tableRef.value.generateNewConfigList()
  })
}
const getWarehouseTypeFn = () => {
  getWarehouseType().then(res => {
    filterConfigList[1].options = res.data
    tableRef.value.generateNewConfigList()
  })
}
const batchAddForm = () => {
  getListData()
}
const stateBtn = (record: ITableRow, type: string) => {
  if (type === 'disable') {
    let type = 'storageLocation.usageStatus.Enable'
    putWarehouseIdDisable({}, { id: record.id as unknown as number })
      .then(res => {
        getListData()
        getOperateText(t, {
          type: type
        })
      })
      .catch(err => {
        getOperateText(t, {
          type: type,
          result: 'error',
          reason: err.msg
        })
      })
  } else {
    let type = 'storageLocation.usageStatus.Disable'
    putWarehouseIdEnable({}, { id: record.id as unknown as number })
      .then(res => {
        getListData()
        getOperateText(t, {
          type: type
        })
      })
      .catch(err => {
        getOperateText(t, {
          type: type,
          result: 'error',
          reason: err.msg
        })
      })
  }
}
const customRow = (record: ITableRow) => {
  return {
    onDblclick: () => {
      if (permission.includes('/operations/storage-location/storage-location-list/edit')) {
        operationHandleClick('edit', { record: record } as ITableScope<ITableRow>)
      }
    }
  }
}
const getStatusColor = (state: string) => {
  switch (state) {
    case 'Disable':
    case 'Lock':
    case 'Store':
      return 'error'
    case 'Enable':
    case 'Free':
      return 'success'
  }
  return 'error'
}
const getListData = () => {
  filterChange(State.filterData)
}

const tableRefClick = () => {}
const initEvent = () => {
  document.getElementById('tableStorageLocation')?.addEventListener('click', tableRefClick)
}
onMounted(() => {
  getData()
  getWarehouseAreaFn()
  getWarehouseTypeFn()
  initEvent()
})
</script>
<style lang="less" scoped>
.content-auto {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 7px 24px;
}
</style>
