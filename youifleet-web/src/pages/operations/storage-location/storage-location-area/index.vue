<template>
  <MyTable
    :filterConfigList="State.filterConfigList"
    :filterData="State.filterData"
    :columns="State.columns"
    :tableOptions="State.tableOptions"
    :pagination="pageState.pagination"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    :customRow="customRow"
    @formChange="filterChange">
    <template #createDate="{ scope }">{{ setTimeStr(scope.record.createDate) }}</template>
    <template #updateDate="{ scope }">{{ setTimeStr(scope.record.updateDate) }}</template>
  </MyTable>
  <MyForm :visible="visible" :formConfigList="formConfigList" :formData="State.formData" @close="onClose" @submit="formSubmit"></MyForm>
  <my-import-file-dialog
    v-model:visible="fileDialogVisible"
    :accept="'.xlsx'"
    :title="t('message.storageLocation.importDatabaseLocationArea')"
    :describeFileName="t('message.actionSetting.fileFormatJson')"
    :action="apiUrl + '/fleet/warehouseArea/uploadExcelFile'"
    @success="getListData"></my-import-file-dialog>
</template>

<script setup lang="ts">
import type { IFormRules, ITableScope } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { useLocale } from 'youibot-plus'
import { columns, filterConfigList, btnList, tableOptions, formConfigList } from './config'
import type { ITableRow } from './config'
import { getOperateText, messageTip } from '@/utils'
import { postWarehouseArea, putWarehouseArea, getWarehouseAreaPage, deleteWarehouseArea } from '@/api/warehouseArea'
import myImportFileDialog from '@/components/common/my-import-file-dialog.vue'
import { setTimeStr, downLoadFileOpen } from '@/utils/index'
import { isRequire, isLength, english } from '@/utils/form-rules'
import { permission } from '@/router'
const apiUrl = `//${window.apiUrl}`
const { pageState, deleteItemConfirm, changeFilter, getData } = usePage({
  getPageFn: getWarehouseAreaPage,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  deleteBatchFn: deleteWarehouseArea as unknown as (params: number[]) => Promise<any>,
  deleteSuccessFn: () => {
    State.selectedRowKeys = []
  }
})
const { t } = useLocale()
const visible = ref<boolean>(false)
const fileDialogVisible = ref<boolean>(false)

const State = reactive({
  columns,
  tableOptions,
  filterData: {},
  filterConfigList,
  formData: {},
  selectedRowKeys: [] as (string | number)[],
  formConfigList
})
const onClose = () => {
  visible.value = false
}
const rules: { [key: string]: IFormRules[] } = {
  code: [isRequire(), isLength(0, 6), english()],
  name: [isRequire(), isLength(0, 20)]
}
Object.keys(rules).forEach((item, index) => {
  State.formConfigList[index].rules = rules[item]
})
//封装底部操作栏按钮
const btnHandleClick = (type: string, data: string[], value?: string) => {
  if (['add', 'import', 'export'].includes(type)) {
    switch (type) {
      // 新增
      case 'add':
        State.formConfigList[0].disabled = false
        visible.value = true
        State.formData = {}

        break
      case 'import': //导入
        fileDialogVisible.value = true
        break
      case 'export': //导出
        downLoadFileOpen(t, '/fleet/warehouseArea/export', { ids: State.selectedRowKeys.join(','), ...pageState.filterData })
        break
    }
  } else {
    if (data.length <= 0) {
      messageTip(t(value ? value : 'message.selectTheDataYouWantToDelete'), 'error')
      return
    }
    switch (type) {
      // 删除
      case 'delete':
        if (State.selectedRowKeys.length <= 0) {
          messageTip(t('message.selectTheDataYouWantToDelete'), 'error')
          return
        }
        deleteItemConfirm(State.selectedRowKeys as number[], t('message.delTips'))
        break
    }
  }
}

;(function btnListClickHandler() {
  const list = ['add', 'delete', 'import', 'export']
  list.map((item, index) => {
    btnList[index].onClick = () => btnHandleClick(item, State.selectedRowKeys as string[], btnList[index].message)
  })
})()

//封装右侧操作栏按钮
const operationHandleClick = (type: string, data: ITableScope<ITableRow>) => {
  if (!data) return
  const { record } = data
  switch (type) {
    // 编辑
    case 'edit':
      if (record) {
        State.formConfigList[0].disabled = true
        const { ...rest } = record as ITableRow
        State.formData = { ...rest }
        visible.value = true
      }
      break
  }
}

;(function operationClickHandler() {
  const list = ['edit']
  list.map((item, index) => {
    tableOptions[index].onClick = (text?: object) => operationHandleClick(item, text as ITableScope<ITableRow>)
  })
})()

// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}

  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      const data = toRaw(filterData[item]) as unknown as string[]
      params[item] = data.join(',')
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const formSubmit = (formData: any) => {
  if (formData.id) {
    putWarehouseArea(formData)
      .then(res => {
        visible.value = false
        getData()
        getOperateText(t, {
          type: 'update'
        })
      })
      .catch(err => {
        getOperateText(t, {
          type: 'update',
          result: 'error',
          reason: err.msg
        })
      })
  } else {
    postWarehouseArea(formData)
      .then(res => {
        visible.value = false
        getData()
        getOperateText(t)
      })
      .catch(err => {
        getOperateText(t, {
          result: 'error',
          reason: err.msg
        })
      })
  }
}
const customRow = (record: ITableRow) => {
  return {
    onDblclick: () => {
      if (permission.includes('/operations/storage-location/storage-location-area/edit')) {
        operationHandleClick('edit', { record: record } as ITableScope<ITableRow>)
      }
    }
  }
}
const getListData = () => {
  filterChange(State.filterData)
}
onMounted(() => {
  getData()
})
</script>
