import type { IBtnType, IFormItemType } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, RANGEPICKER, SELECT, SWITCH } from 'youibot-plus'
import { isRequire } from '@/utils/form-rules'

export interface ITableRow {
  code: string
  level: number
  sourceSystem: string
  type: string
  agvCode: string
  description: string
  solution: string
  status: number
  ignoreStatus: number
  id: number
  missionWorkId: number
  deviceId: number
  agvMapName: string
  createTime: number
  updateTime: number
  closeTime: number
}

//table操作栏
const tableOptions: IBtnType[] = [
  {
    name: 'message.edit',
    type: 'link',
    permission: 'edit'
  }
]

//table 配置
const columns: TableColumnsType = [
  {
    // 编码
    title: 'message.encoding',
    dataIndex: 'code',
    width: 180
  },
  {
    // 名称
    title: 'message.name',
    dataIndex: 'name',
    width: 270
  },

  {
    // 创建人
    title: 'message.founder',
    dataIndex: 'creatorName',
    width: 140
  },
  {
    // 创建时间
    title: 'message.createTime',
    key: 'createDate',
    width: 200
  },
  {
    // 修改时间
    title: 'message.updateTime',
    key: 'updateDate',
    width: 200
  },
  {
    title: 'message.options',
    key: 'options',
    fixed: 'right'
  }
]
// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    // 编码
    is: INPUT,
    name: 'code',
    label: 'message.encoding',
    allowClear: true
  },
  {
    // 名称
    is: INPUT,
    name: 'name',
    label: 'message.name',
    allowClear: true
  },

  {
    // 创建人
    is: INPUT,
    name: 'creatorName',
    label: 'message.founder',
    allowClear: true
  },
  {
    // 创建时间
    is: RANGEPICKER,
    name: 'createDate',
    allowClear: true,
    label: 'message.createTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  },
  {
    // 修改时间
    is: RANGEPICKER,
    name: 'updateDate',
    allowClear: true,
    label: 'message.updateTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  }
]

//底部按钮
const btnList: IBtnType[] = [
  {
    name: 'message.add',
    type: 'primary',
    icon: 'iconfont icon-zengjia',
    permission: 'add'
  },
  {
    name: 'message.del',
    icon: 'iconfont icon-xiazai14',
    permission: 'del'
  },
  {
    name: 'message.multImport',
    permission: 'import'
  },
  {
    name: 'message.multExport',
    permission: 'export'
  }
]

const formConfigList: IFormItemType[] = [
  {
    // 编码
    is: INPUT,
    name: 'code',
    label: 'message.encoding',
    disabled: false
  },
  {
    // 名称
    is: INPUT,
    name: 'name',
    label: 'message.name',
    rules: [isRequire()]
  }
]
export { columns, filterConfigList, tableOptions, btnList, formConfigList }
