<template>
  <taskTop :nodeConfig="nodeConfig" @change="getDetails"></taskTop>

  <div id="container">
    <div id="graph-container" />
    <TeleportContainer />

    <!-- <div id="form-container"> -->
    <!-- </div> -->
  </div>
  <a-drawer class="default-node" :placement="'left'" :closable="false" :mask="false" :open="open" @close="onClose" width="300px">
    <div id="stencil">
      <initialNode
        @handleMouseDown="
          (e, item) => {
            handleMouseDown(e, item)
          }
        "></initialNode>
    </div>
  </a-drawer>
  <taskForm ref="taskFormRef" @formDataChange="formDataChange"></taskForm>

  <div class="left-open" :style="{ left: open ? '231px' : '0px' }" @click="setLeftOpen">
    <i class="iconfont" :class="[open ? 'icon-jiantou_yemian_xiangzuo' : 'icon-jiantou_yemian_xiangyou']"></i>
    <span>{{ open ? t('message.taskArrangementNew.putAway') : t('message.taskArrangementNew.unfold') }}</span>
  </div>
  <div class="zoom">
    <a-button class="zoom-out" :class="nowVal == 20 && 'disabled'" @click="zoomSize(2)"><i class="iconfont icon-zengjia"></i></a-button>
    <div>{{ nowVal.toFixed(0) }}%</div>
    <a-button class="zoom-in" :class="nowVal == 300 && 'disabled'" @click="zoomSize(1)"><i class="iconfont icon-jianhao"></i></a-button>
  </div>
</template>

<script setup lang="ts">
// @ts-nocheck
import initialNode from '@/components/task-type-arrangement/initial-node/index.vue'
import taskForm from '@/components/task-type-arrangement/task-form/index.vue'
import { onMounted } from 'vue'
import { Graph } from '@antv/x6'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { Selection } from '@antv/x6-plugin-selection'
import { Dnd } from '@antv/x6-plugin-dnd'
import { startNode, endNode, getEdgeConfig, imageConfig, rectConfig, rectHeight, rectWidth } from './config'
import useAddHooks from '@/node-hooks/use-node-add'
import useBasicHooks from '@/node-hooks/use-node-basic'
import useLayoutHooks from '@/node-hooks/use-layout'
import useDragHooks from '@/node-hooks/use-node-drag'
import useDeleteHooks from '@/node-hooks/use-node-delete'
import useDataTreating from '@/node-hooks/use-node-data-treating'
import useStore from '@/stores'
import emitter from '@/utils/mitts'
import { getTaskTypeInfo, putTaskType } from '@/api/task'
import { getQuery, resetRoute } from '@/router'
import { getOperateText, debounce } from '@/utils'
import { deepClone, useLocale } from 'youibot-plus'
import useNode from '@/hooks/use-node'
import useNodeFrom from '@/hooks/use-node-form'
import NodeStyle from './node-style.vue'
import { register, getTeleport } from '@antv/x6-vue-shape'

const { nodeStore } = useStore()
const { t } = useLocale()
let handleMouseDown = () => {}
let dragObj = null // 记录当前被拖拽的节点数据
window.edgeArrMap = new Map() //记录边数据,避免自动计算的时候边挪动.当有并行分支的时候,就收集,只收集第一行的数据就可以,按记录的顺序展示数据.
window.whileEdgeArrMap = new Map() //记录循环节点的回环
window.sourceEdgeArrMap = new Map() //key为边的起点，value为边的终点
window.targetEdgeArrMap = new Map() //key为边的终点，value为边的起点
let graph
const selectNode = ref()
const open = ref(true)
// 判断拖拽是否替换节点
let isNodeCollision = false
// 判断是否增加节点
let isAddNode = false
let isDragTimr = null
let nowVal = ref(100)
const dragNodeType = ref()
const { transitionTree } = useDataTreating()
const { getOutputArr, translateEL, clearReferenceVariable } = useNode()
const { translateParams } = useNodeFrom()

// 任务属性参数
let nodeConfig = ref<INodeBaseData>({
  id: '',
  code: '',
  name: '',
  formatData: '',
  priority: '',
  publishStatus: 'Unpublished',
  childNode: null,
  type: '0',
  params: [],
  nodeParam: ''
})
let updateTime = 0
let output = ref()
register({
  shape: 'custom-rect',
  width: rectWidth,
  height: rectHeight,
  component: NodeStyle
})
const TeleportContainer = getTeleport()
const formDataChange = data => {
  selectNode.value?.setData(data, { overwrite: true })
  submitData()
}
// 保存数据
const submitData = async () => {
  const tojson = graph.toJSON()
  const tree = transitionTree(tojson.cells)
  tojson.cells.map(item => {
    if (item.shape === 'edge') {
      delete item.data
    } else if ((item.whenIndex !== 0 || item.whenChildIndex !== 0) && item.commonId) {
      delete item.whenList
      delete item.tools
    } else {
      delete item.tools
    }
    return item
  })
  nodeConfig.value.childNode = tree
  // return
  const { childNode, params, ...rest } = nodeConfig.value
  const formatData = translateEL(nodeConfig.value, '', false, false, nodeConfig.value.params)
  const paramsRes = {
    ...rest,
    nodeParam: JSON.stringify(tojson),
    formatData,
    params: translateParams(params)
  }
  await putTaskType(paramsRes)
    .then(res => {
      const { data } = res
      nodeConfig.value.publishStatus = data.publishStatus
      initOutput()
      // getOperateText(t, {
      //   type: 'update'
      // })
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg
      })
    })
}
const initOutput = () => {
  const { type, code } = nodeStore.getFormData
  output.value = getOutputArr(nodeConfig.value, type == '0' ? 'global' : code)
  graph.getNodes()?.forEach(itemNodes => {
    const data = itemNodes.getData()
    if (data) {
      const { params, taskInParams, code: nodeCode } = itemNodes.getData()
      if (params) {
        params.map(item => {
          if (item.category === 'In' && item.variable) {
            if (
              !getOutputArr(nodeConfig.value, nodeCode).some((_item: InputVariableList) => {
                return item.variable === _item.variable || (item.variable === _item.code && item.nodeCode === _item.number)
              })
            ) {
              item.variable = ''
              item.variableName = ''
              item.value = ''
              item.nodeCode = ''
              item.source = 'Fixed'
            }
          }

          return item
        })
        itemNodes.setData({ params: params })
      }
      if (taskInParams) {
        taskInParams.map(item => {
          if (item.variable) {
            if (
              !getOutputArr(nodeConfig.value, nodeCode).some((_item: InputVariableList) => {
                return item.variable === _item.variable || (item.variable === _item.code && item.nodeCode === _item.number)
              })
            ) {
              item.variable = ''
              item.variableName = ''
              item.value = ''
              item.nodeCode = ''
              item.source = 'Fixed'
            }
          }

          return item
        })
        itemNodes.setData({ taskInParams })
      }
    }
  })
}
const initCode = () => {
  nodeStore.setCode(0)
  graph.getNodes()?.forEach(item => {
    const data = item.getData()
    let codeNum = data?.code ? parseInt(data.code.split('N')[1]) : 0
    if (codeNum > nodeStore.getCode) {
      nodeStore.setCode(codeNum)
    }
  })
  nodeStore.setCode(nodeStore.getCode + 1)
}
const updateNode = data => {
  if (data.type === 'add') {
    const { isClone, type, whenIndex = 0, whenList = [], whileList = [], isPassAddFn = false, commonId = '' } = data.node.toJSON()
    if (
      isClone ||
      (whenList.length && type === 'Judge' && whenIndex === whenList.length - 1) ||
      (type === 'While' && isPassAddFn) ||
      data.node.id === 'start' ||
      data.node.id === 'end' ||
      type === 'WHEN' ||
      (type === 'CancelTask' && isPassAddFn)
    ) {
      submitData()

      return
    }
    selectNode.value = data.node
  }
  submitData()
}
provide('output', output)
provide('nodeConfig', nodeConfig)
emitter.on('update:node', updateNode)

onMounted(() => {
  // #region 初始化画布
  graph = new Graph({
    container: document.getElementById('graph-container'),
    grid: false,
    autoResize: true,
    embedding: {
      enabled: true,
      findParent({ node }) {
        const bbox = node.getBBox()
        const filterNode = this.getNodes().filter(node => {
          const data = node.store.data
          if (!isAddNode && data.type && !['Judge', 'While', 'CancelTask', 'WHEN'].includes(data.type) && !data.isClone) {
            const targetBBox = node.getBBox()
            return bbox.isIntersectWithRect(targetBBox)
          }
          return false
        })
        // // console.log(filterNode, 'filterNode')
        isNodeCollision = !!filterNode.length
        return filterNode
      }
    },

    panning: {
      enabled: true,
      eventTypes: ['rightMouseDown', 'mouseWheel', 'mouseWheelDown']
    },
    background: { color: '#f5f5f7' },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.2,
      maxScale: 3,
      guard: e => {
        if (e.ctrlKey) {
          setTimeout(() => {
            nowVal.value = graph.zoom() * 100
          }, 50)
        }
        return true
      }
    },
    connecting: {
      router: 'orth',
      connectionPoint: 'bbox',
      fill: 'none'
    },
    interacting: function () {
      return {
        nodeMovable: false,
        edgeMovable: false
      }
    }
  })
  const { layout } = useLayoutHooks(graph, t)
  const {
    createNode,
    isNearTop,
    getLastNode,
    getNextNode,
    setLabel,
    clearLabel,
    setWhenEdgeArrMap,
    setWhileEdgeArrMap,
    setEdgeArrMap,
    getForArrayNodeLength,
    getWhenListMaxRealCount
  } = useBasicHooks(graph, t, dragNodeType)
  const { add } = useAddHooks(graph, t)
  const { drag } = useDragHooks(graph, t)
  const { remove } = useDeleteHooks(graph, t)

  const render = data => {
    if (data) {
      graph.fromJSON(data)
    } else {
      graph.fromJSON({
        nodes: [
          { ...startNode, ...{ label: t(startNode.label) } },
          { ...endNode, ...{ label: t(endNode.label) } }
        ],
        edges: [
          {
            source: 'start',
            target: 'end',
            ...getEdgeConfig()
          }
        ]
      })
    }
    setTimeout(() => {
      //初始化加载的时候需要先处理map
      graph.getNodes().map(item => {
        const { whenList = [] } = item.toJSON()
        if (whenList.length) {
          setWhenEdgeArrMap(whenList)
        }
        return item
      })

      graph.getEdges().map(item => {
        const { isWhile = false } = item.toJSON()
        if (isWhile) {
          setWhileEdgeArrMap(graph.getCellById(item.getSource().cell), graph.getCellById(item.getTarget().cell))
        }
        setEdgeArrMap(item)
        return item
      })

      layout()
      graph.positionContent('top', { useCellGeometry: true })
      const pos = graph.translate()
      graph.translate(pos.tx, pos.ty + 45)
    }, 0)
  }

  // 获取数据
  const getDetails = () => {
    getTaskTypeInfo({ id: getQuery('id') as unknown as number })
      .then(res => {
        const { data } = res
        if (data) {
          const { nodeParam = null, templateType, event, ...rest } = data
          const cells = nodeParam ? JSON.parse(nodeParam) : null
          cells?.cells?.map(element => {
            if (
              element.whenIndex !== undefined &&
              element.whenChildIndex !== undefined &&
              (element.whenIndex !== 0 || element.whenChildIndex !== 0) &&
              element.commonId
            ) {
              const cellFind = cells.cells.find(item => {
                return element.commonId === 'when' + item.id
              })
              if (cellFind) {
                element['whenList'] = cellFind.whenList
              }
              //
            }
            return element
          })
          nodeConfig.value = {
            ...rest,
            templateType,
            event,
            type: '0',
            childNode: cells ? cells : null
          }

          render(cells)
          if (updateTime == 0) {
            const tojson = graph.toJSON()
            const tree = transitionTree(tojson.cells)
            nodeConfig.value.childNode = tree
            nodeStore.setFormData(nodeConfig.value, 1)
            initOutput()
            // emitter.emit('childNode', { ...nodeConfig.value, childNode: null })
          }
          initCode()
          updateTime += 1
        }
      })
      .catch(err => {
        console.log('🚀 ~ getDetails ~ err:', err)
        // render()
      })
  }

  const dnd = new Dnd({
    target: graph
  })

  const startDrag = (node, e) => {
    setLabel()
    setMousewheel()
    dnd.start(node, e)
  }
  handleMouseDown = (e, item) => {
    dragNodeType.value = item.type
    isAddNode = true
    startDrag(createNode(item), e)
  }

  // #region 使用插件
  graph
    .use(new Snapline({ tolerance: 0 }))
    .use(new Keyboard())
    .use(new Clipboard())
    .use(new History())
    .use(
      new Selection({
        enabled: true,
        multiple: false,
        rubberband: false,
        movable: false
      })
    )
  graph.disableSnaplineOnResizing()
  // delete
  graph.bindKey('backspace', () => {
    const cells = graph.getSelectedCells()
    if (cells.length) {
      graph.removeCells(cells)
    }
  })

  const blankClick = () => {
    clearLabel()
    selectNode.value = null
    nodeStore.setFormData(nodeConfig.value, 1)
    initOutput()
  }

  graph.on('node:removed', ({ view, e }) => {
    blankClick()
  })

  graph.on('blank:click', ({ e, x, y, edge, view }) => {
    blankClick()
  })

  graph.on('edge:click', ({ e, x, y, edge, view }) => {
    console.log('🚀 ~ graph.on ~ edge:', edge)
  })

  graph.on('cell:mousewheel', e => {
    console.log('node:mousewheel', e)
  })

  graph.on('node:click', ({ node }) => {
    const { isClone, type, whenIndex = 0, whenList = [], whileList = [], isPassAddFn = false, commonId = '' } = node.toJSON()
    console.log('🚀 ~ graph.on ~ whenList:', node, node.id, whenList, whileList)
    if (
      isClone ||
      (whenList.length && type === 'Judge' && whenIndex === whenList.length - 1) ||
      (type === 'While' && isPassAddFn) ||
      node.id === 'start' ||
      node.id === 'end' ||
      type === 'WHEN' ||
      (type === 'CancelTask' && isPassAddFn)
    ) {
      blankClick()
      return
    }
    const data = node.getData()
    selectNode.value = node
    nodeStore.setFormData(data, 1)
    initOutput()
    // emitter.emit('childNode', data)
  })
  graph.on('node:mousedown', ({ e, x, y, node }) => {
    isDragTimr = new Date().getTime()
  })
  // 画布上的节点
  graph.on('node:mouseenter', ({ node }) => {
    if (!['start', 'end'].includes(node.id)) {
      const { type, whenList, whenIndex = 0, isClone = false, isPassAddFn = false, whileIndex = 0, whileList } = node.toJSON()
      if (isClone) return
      if (type === 'Judge' && isPassAddFn) return
      if (type === 'While' && isPassAddFn) return
      if (type === 'CancelTask' && isPassAddFn) return
      const removeFn = () => {
        if (node) remove(node)
      }
      const handleClick = debounce(removeFn, 300)
      node.addTools({
        name: 'button',
        args: {
          markup: [
            {
              tagName: 'image',
              attrs: {
                width: 26,
                height: 26,
                'xlink:href': require('../../../../assets/images/delete.png')
              }
            }
          ],
          x: '100%',
          y: 0,
          offset: { x: -20, y: -10 },
          onClick: () => handleClick()
        }
      })
    }
  })

  graph.on('node:mousemove', ({ e, node, view }) => {
    if (new Date().getTime() - isDragTimr < 200) {
      return
    }
    const { type, isClone } = node.store.data
    if (!dragObj) {
      let sourceNode = getLastNode(node)
      let targetNode = getNextNode(node)
      if (!sourceNode || !targetNode) return
      const { whenList: sourceWhenList = [] } = sourceNode.toJSON()
      const lastNode = sourceWhenList.length ? graph.getCellById(sourceWhenList[0][0].lastNode) : null

      if (node.id != 'start' && node.id != 'end' && !['WHEN', 'Judge', 'While', 'CancelTask'].includes(type) && !isClone) {
        if (node.id !== lastNode) {
          dragObj = {
            node,
            sourceNode,
            targetNode
          }
          node.removeTools()
          setMousewheel()
          dragNodeType.value = node.toJSON().type
          startDrag(node, e)
        }
      }
    }
  })
  graph.on('node:mouseleave', ({ e, x, y, node }) => {
    node.removeTools()
    graph.enablePanning() // 恢复画布平移
  })

  graph.on('node:added', ({ node }) => {
    if (dragObj) {
      if (!isNodeCollision) {
        drag(node, dragObj)
        dragObj = null
      }
    } else {
      add(node)
      isAddNode = false
    }
  })
  graph.on('edge:added', ({ edge }) => {
    setEdgeArrMap(edge)
  })

  graph.on('node:embedded', ({ node, currentParent }) => {
    const dragNode = graph.getCellById(dragObj.node.id)
    const nodeJSON = deepClone(node.toJSON())
    const currentParentJSON = deepClone(currentParent.toJSON())
    const nodeData = deepClone(node.getData())
    const currentParentData = deepClone(currentParent.getData())
    dragNode.prop('type', currentParentJSON.type)
    dragNode.prop('code', currentParentJSON.code)
    dragNode.prop('label', currentParentJSON.label)
    currentParent.prop('type', nodeJSON.type)
    currentParent.prop('code', nodeJSON.code)
    currentParent.prop('label', nodeJSON.label)

    dragNode.removeData({ silent: true })
    dragNode.setData(currentParentData)
    currentParent.removeData({ silent: true })
    currentParent.setData(nodeData)
    graph.removeNode(node)
    dragObj = null
    isNodeCollision = false
    initOutput()
    submitData()
  })
  // 回显
  getDetails()
  nowVal.value = graph.zoom() * 100
  const mouseup = () => {
    clearLabel()
    removeMousewheel()
    isDragTimr = null
  }
  document.addEventListener('mouseup', mouseup)

  const mousewheelTranslate = e => {
    const pos = graph.translate()
    graph.translate(pos.tx, pos.ty - e.deltaY)
  }
  const setMousewheel = () => {
    document.addEventListener('mousewheel', mousewheelTranslate)
  }
  const removeMousewheel = () => {
    document.removeEventListener('mousewheel', mousewheelTranslate)
  }

  onBeforeUnmount(() => {
    document.removeEventListener('mouseup', mouseup)
    document.removeEventListener('mousewheel', mousewheelTranslate)
    window.edgeArrMap.clear()
    window.whileEdgeArrMap.clear()
    window.sourceEdgeArrMap.clear()
    window.targetEdgeArrMap.clear()
    emitter.off('update:node', updateNode)
  })
})
const zoomSize = (type: number) => {
  setTimeout(() => {
    if (type == 1) {
      if (nowVal.value == 20) {
        // messageTip('最小缩放至10%', 'error')
        return
      }
      nowVal.value -= 10
      graph.zoom(nowVal.value / 100, { absolute: true })
    } else {
      if (nowVal.value == 300) {
        // messageTip('最大缩放至200%', 'error')
        return
      }
      nowVal.value += 10
      graph.zoom(nowVal.value / 100, { absolute: true })
    }
  }, 100)
}
const setLeftOpen = () => {
  open.value = !open.value
}
</script>

<style lang="less">
.default-node {
  .ant-drawer-body {
    padding: 0 !important;
  }
}

#container {
  display: flex;
  height: 100%;
  // border: 1px solid #dfe3e8;
}

#stencil {
  position: relative;
  width: 300px;
  height: 100%;
  border-right: 1px solid #dfe3e8;
}

.stencil-item {
  box-sizing: border-box;
  width: 66px;
  height: 36px;
  margin: 20px;
  font-size: 12px;
  line-height: 36px;
  text-align: center;
  background-color: rgb(239 244 255);
  border: 1px solid rgb(95 149 255);
  cursor: move;
}

#graph-container {
  /* width: calc(100% - 180px - 400px); */

  /* width: calc(100% - 180px); */
  width: 100%;
  height: 100%;
}

#form-container {
  width: 400px;
  height: 100%;
  border: 1px solid #eee;
}

.x6-widget-transform {
  margin: -1px 0 0 -1px;
  padding: 0;
  border: 1px solid #239edd;
}

.x6-widget-transform > div {
  border: 1px solid #239edd;
}

.x6-widget-transform > div:hover {
  background-color: #3dafe4;
}

.x6-widget-transform-active-handle {
  background-color: #3dafe4;
}

.x6-widget-transform-resize {
  border-radius: 0;
}

.x6-widget-selection-inner {
  border: 1px solid #239edd;
}

.x6-widget-selection-box {
  opacity: 0;
}

.left-open {
  position: fixed;
  bottom: 0;
  left: 232px;
  z-index: 2100;
  width: 68px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  background-color: #fff;
  cursor: pointer;
  transition-duration: 0.3s;
}
</style>
<style lang="less" scoped>
@border-color: #a6b4bf;
@node-text-color: #7a7a7a;
@bg-color: #f5f5f7;
@form-width: 378px;
@node-width: 180px;
@node-height: 44px;
@icon-color: #09bc86;

.zoom {
  position: absolute;
  top: calc(@head_height + 25px);
  right: calc(@form-width + 30px);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  > div {
    width: 80px;
    height: 32px;
    margin: 0 4px;
    padding: 4px 12px;
    color: #000;
    font-size: 14px;
    background-color: #f5f5f5;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
  }

  .ant-btn {
    width: 32px;
    height: 32px;
    padding: 0 4px;
    color: #0009;
    background: #ebebeb;
    border: 1px solid #ebebeb;

    &:focus,
    &:hover {
      color: #000;
      background: #d9d9d9;
      border-color: #d9d9d9;
    }
  }
}

.event-node {
  width: 160px;
  height: 44px;
  font-size: 14px;
  line-height: 44px;
  text-align: center;
  background: #fff;
  border-radius: 4px;
  box-shadow: 4px 4px 8px 0 #bdbdbd40;
}

.event {
  position: relative;

  .node-mistake {
    position: absolute;
    top: -10px;
    right: -10px;
    display: none;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    background-color: #ce1313;
    border-radius: 50%;
    cursor: pointer;

    i {
      color: #fff;
      font-size: 14px;
    }

    &-show {
      display: block;
    }
  }
}

.node-wrap {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  text-align: center;

  &-box {
    text-align: center;
  }
}

// 开始节点
.start-node {
  position: relative;
  margin: 0 auto;
  padding: 8px 0;
  color: @node-text-color;

  &::after {
    position: absolute;
    bottom: 0;
    left: calc(50% - 4.5px);
    width: 9px;
    height: 9px;
    font-size: 0;
    background-color: @border-color;
    border-radius: 50%;
    content: '';
  }
}

// 条件节点 并行节点
.branch-wrap {
  display: inline-flex;
  width: 100%;
  margin-top: 9px;

  .branch-box-wrap {
    display: flex;
    flex-flow: column wrap;
    flex-shrink: 0;
    align-items: center;
    width: 100%;

    .branch-box {
      position: relative;
      display: flex;
      height: auto;
      // margin-top: 15px;
      overflow: visible;
      // border-top: 1px solid @border-color;
      // border-bottom: 1px solid @border-color;
    }

    .col-box {
      position: relative;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      background-color: @bg-color;

      &::before {
        .node-line-before();
      }
    }

    .condition-node {
      display: inline-flex;
      flex-direction: column;

      &-box {
        position: relative;
        flex-grow: 1;
        align-items: center;
        justify-content: center;
        // padding: 30px 50px 0;
        text-align: center;

        // &::before {
        //   .node-line-before();
        // }
      }
    }

    .top-left-cover-line,
    .top-right-cover-line {
      position: absolute;
      top: -4px;
      width: 50%;
      height: 8px;
      background-color: @bg-color;
    }

    .top-left-cover-line {
      left: -1px;
    }

    .top-right-cover-line {
      right: -1px;
    }

    .bottom-left-cover-line {
      left: -1px;
    }

    .bottom-left-cover-line,
    .bottom-right-cover-line {
      position: absolute;
      bottom: -4px;
      width: 50%;
      height: 8px;
      background-color: @bg-color;
    }

    .bottom-right-cover-line {
      right: -1px;
    }
  }
}

// 配置节点
.setting-node {
  position: relative;

  &-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: @node-width;
    height: @node-height;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #e2e2e2;
    border-radius: 6px;

    &.is-edit {
      cursor: pointer;
    }
  }

  &.actived {
    .setting-node-wrap {
      border: 1px solid #ffbe96;
    }
  }

  &-icon {
    display: inline-block;
    width: @node-height;
    height: calc(@node-height - 2px);
    line-height: calc(@node-height - 2px);
    text-align: center;
    background-color: #ecfaf6;

    i {
      color: @icon-color;
      font-size: 20px;
    }
  }

  &-text {
    flex: 1;
    padding: 12px;
    overflow: hidden;
    color: #000;
    font-size: 14px;
    white-space: nowrap;
    text-align: left;
    text-overflow: ellipsis;
  }

  &-del {
    position: absolute;
    top: -10px;
    right: -10px;
    display: none;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    background-color: #ce1313;
    border-radius: 50%;
    cursor: pointer;

    i {
      color: #fff;
      font-size: 14px;
    }
  }

  &:hover {
    .setting-node-wrap {
      border: 1px solid #ffbe96;
    }

    .setting-node-del {
      display: block;
    }
  }
}

// 添加按钮
.branch-btn-add {
  position: absolute;
  top: -16px;
  left: 50%;
  z-index: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  color: @icon-color;
  font-size: 12px;
  background: #fff;
  border: 1px solid @border-color;
  border-radius: 4px;
  outline: none;
  box-shadow: 4px 4px 8px 0 #bdbdbd40;
  transform: translateX(-50%);
  transform-origin: center center;
  cursor: pointer;
  user-select: none;

  &:hover {
    border-color: #ffbe96;
  }

  &-icon {
    width: 28px;
    text-align: center;
  }

  &-text {
    padding-right: 8px;
  }
}
</style>
