<template>
  <div class="wrap">
    <div class="date-wrap">
      <div class="date">
        <DatePicker v-model:dateVal="dateVal" @update:dateVal="getData"></DatePicker>
      </div>
    </div>
    <div class="statistic-wrap">
      <div class="statistic-wrap-content">
        <div class="statistic-wrap-row">
          <div class="block flex25">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.taskStatistical.taskStatusPieChart') }}</p>
            </div>
            <v-chart id="taskStatusPieChart" autoresize />
          </div>
          <div class="block flex25">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.taskStatistical.createTaskCountPieChart') }}</p>
            </div>
            <v-chart id="createTaskCountPieChart" autoresize />
          </div>
          <div class="block flex25">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.taskStatistical.avgAllocationDurationPieChart') }}</p>
            </div>
            <v-chart id="avgAllocationDurationPieChart" autoresize />
          </div>
          <div class="block flex25">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.taskStatistical.avgExecuteDurationPieChart') }}</p>
            </div>
            <v-chart id="avgExecuteDurationPieChart" autoresize />
          </div>
        </div>
        <div class="statistic-wrap-row">
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.taskStatistical.createTaskCountLineChart') }}</p>
            </div>
            <v-chart id="createTaskCountLineChart" autoresize />
          </div>
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.taskStatistical.endTaskCountLineChart') }}</p>
            </div>
            <v-chart id="endTaskCountLineChart" autoresize />
          </div>
        </div>
        <div class="statistic-wrap-row">
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.taskStatistical.avgAllocationDurationLineChart') }}</p>
            </div>
            <v-chart id="avgAllocationDurationLineChart" autoresize />
          </div>
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.taskStatistical.avgExecuteDurationDurationLineChart') }}</p>
            </div>
            <v-chart id="avgExecuteDurationDurationLineChart" autoresize />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs, { Dayjs } from 'dayjs'
import useStatical from '@/hooks/use-statical'
import VChart from 'vue-echarts'
import {
  taskStatusPieChartOption,
  createTaskCountPieChartOption,
  avgAllocationDurationPieChartOption,
  avgExecuteDurationPieChartOption,
  createTaskCountLineChartOption,
  endTaskCountLineChartOption,
  avgAllocationDurationLineChartOption,
  avgExecuteDurationDurationLineChartOption
} from './option'
import { getStatisticsTask } from '@/api/statistics'
import { deepClone, useLocale } from 'youibot-plus'
import { messageTip } from '@/utils'

const { t } = useLocale()
const titleShow = ref(false)
//过去七天的数据
const dateVal = ref<[Dayjs, Dayjs]>([dayjs(new Date().getTime() - 3600 * 1000 * 24 * 6), dayjs(new Date())])
const transalteT = (option: any) => {
  Object.keys(option).forEach((key: string) => {
    if (key === 'series') {
      option[key].map((item: any, index: number) => {
        option[key][index].name = t(option[key][index].name)
      })
    }
  })
  return option
}
const getData = () => {
  getStatisticsTask({
    startTime: dayjs(dateVal.value[0]).format('YYYY-MM-DD'),
    endTime: dayjs(dateVal.value[1]).format('YYYY-MM-DD')
  }).then(res => {
    const {
      taskStatusPieChart = null,
      createTaskCountPieChart = null,
      avgAllocationDurationPieChart = null,
      avgExecuteDurationPieChart = null,
      createTaskCountLineChart = null,
      endTaskCountLineChart = null,
      avgAllocationDurationLineChart = null,
      avgExecuteDurationDurationLineChart = null
    } = res.data
    console.log(createTaskCountPieChart, 'createTaskCountPieChart')
    useStatical('taskStatusPieChart').init({
      type: 'pie',
      config: {
        chart: taskStatusPieChart,
        option: transalteT(taskStatusPieChartOption)
      }
    })
    useStatical('createTaskCountPieChart').init({
      type: 'pie',
      config: {
        chart: createTaskCountPieChart,
        option: transalteT(createTaskCountPieChartOption)
      }
    })
    useStatical('avgAllocationDurationPieChart').init({
      type: 'pie',
      config: {
        chart: avgAllocationDurationPieChart,
        option: transalteT(avgAllocationDurationPieChartOption)
      }
    })
    useStatical('avgExecuteDurationPieChart').init({
      type: 'pie',
      config: {
        chart: avgExecuteDurationPieChart,
        option: transalteT(avgExecuteDurationPieChartOption)
      }
    })

    useStatical('createTaskCountLineChart').init({
      type: 'line',
      config: {
        chart: createTaskCountLineChart,
        option: transalteT(deepClone(createTaskCountLineChartOption))
      }
    })
    useStatical('endTaskCountLineChart').init({
      type: 'line',
      config: {
        chart: endTaskCountLineChart,
        option: transalteT(deepClone(endTaskCountLineChartOption))
      }
    })
    useStatical('avgAllocationDurationLineChart').init({
      type: 'line',
      config: {
        chart: avgAllocationDurationLineChart,
        option: transalteT(deepClone(avgAllocationDurationLineChartOption))
      }
    })

    useStatical('avgExecuteDurationDurationLineChart').init({
      type: 'line',
      config: {
        chart: avgExecuteDurationDurationLineChart,
        option: transalteT(deepClone(avgExecuteDurationDurationLineChartOption))
      }
    })

    titleShow.value = true
  })
}

onBeforeUnmount(() => {
  useStatical('taskStatusPieChart').destroy()
  useStatical('createTaskCountPieChart').destroy()
  useStatical('avgAllocationDurationPieChart').destroy()
  useStatical('avgExecuteDurationPieChart').destroy()
  useStatical('createTaskCountLineChart').destroy()
  useStatical('endTaskCountLineChart').destroy()
  useStatical('avgAllocationDurationLineChart').destroy()
  useStatical('avgExecuteDurationDurationLineChart').destroy()
})

getData()
</script>
<style lang="less" scoped>
@import '@/styles/statistical.less';
</style>
