import { basicLineOption, basicBarSeries, basicBarOption } from '@/utils/chart-options'

// 机器人异常比例
export const vehicleAbnormalPieChartOption = {
  ...basicBarOption,
  series: [
    {
      ...basicBarSeries
      // name: 'message.errorStatistical.vehicleAbnormalPieChart'
    }
  ]
}
// 异常分类比例
export const abnormalDetailPieChartOption = {
  ...basicBarOption,
  series: [
    {
      ...basicBarSeries
      // name: 'message.errorStatistical.abnormalDetailPieChart'
    }
  ],
  legend: {
    formatter: function (name: string) {
      return name.split('-')[0]
    },
    orient: 'vertical',
    right: '2%', // legend自定义后要再写一次
    y: 'center'
  }
}

// 异常平均处理时间
export const avgHandleDurationPieChartOption = {
  ...basicBarOption,
  series: [
    {
      ...basicBarSeries
      // name: 'message.errorStatistical.avgHandleDurationPieChart'
    }
  ],
  legend: {
    formatter: function (name: string) {
      return name.split('-')[0]
    },
    orient: 'vertical',
    right: '2%',
    y: 'center'
  },
  unit: 'time'
}

// 新增异常数量
export const newCountLineChartOption = {
  ...basicLineOption
}

// 异常平均处理时间
export const avgHandleDurationLineChartOption = {
  ...basicLineOption,
  unit: 'time'
}
