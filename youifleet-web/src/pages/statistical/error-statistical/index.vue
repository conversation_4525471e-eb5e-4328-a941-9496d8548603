<template>
  <div class="wrap">
    <div class="date-wrap">
      <div class="date">
        <DatePicker v-model:dateVal="dateVal" @update:dateVal="getData"></DatePicker>
      </div>
    </div>
    <div class="statistic-wrap">
      <div class="statistic-wrap-error-content">
        <div class="statistic-wrap-error-content-row">
          <div class="block flex25">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.errorStatistical.vehicleAbnormalPieChart') }}</p>
            </div>
            <v-chart id="vehicleAbnormalPieChart" autoresize></v-chart>
          </div>
          <div class="block flex25">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.errorStatistical.abnormalDetailPieChart') }}</p>
            </div>
            <v-chart id="abnormalDetailPieChart" autoresize></v-chart>
          </div>
          <div class="block flex25">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.errorStatistical.avgHandleDurationPieChart') }}</p>
            </div>
            <v-chart id="avgHandleDurationPieChart" autoresize />
          </div>
          <div class="block flex25"></div>
        </div>
        <div class="statistic-wrap-error-content-row">
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.errorStatistical.newCountLineChart') }}</p>
            </div>
            <v-chart id="newCountLineChart" autoresize />
          </div>
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.errorStatistical.avgHandleDurationLineChart') }}</p>
            </div>
            <v-chart id="avgHandleDurationLineChart" autoresize />
          </div>
        </div>
        <!-- <div class="statistic-wrap-content-row"></div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs, { Dayjs } from 'dayjs'
import useStatical from '@/hooks/use-statical'
import VChart from 'vue-echarts'
import {
  vehicleAbnormalPieChartOption,
  abnormalDetailPieChartOption,
  avgHandleDurationPieChartOption,
  newCountLineChartOption,
  avgHandleDurationLineChartOption
} from './option'
import { getStatisticsAbnormal } from '@/api/statistics'
import { deepClone, useLocale } from 'youibot-plus'
import { messageTip } from '@/utils'

const { t } = useLocale()
const titleShow = ref(false)
//过去七天的数据
const dateVal = ref<[Dayjs, Dayjs]>([dayjs(new Date().getTime() - 3600 * 1000 * 24 * 6), dayjs(new Date())])
const transalteT = (option: any) => {
  Object.keys(option).forEach((key: string) => {
    if (key === 'series') {
      option[key].map((item: any, index: number) => {
        if (option[key][index].name) {
          option[key][index].name = t(option[key][index].name)
        }
      })
    }
  })
  return option
}

const getData = () => {
  getStatisticsAbnormal({
    startTime: dayjs(dateVal.value[0]).format('YYYY-MM-DD'),
    endTime: dayjs(dateVal.value[1]).format('YYYY-MM-DD')
  }).then(res => {
    const {
      vehicleAbnormalPieChart = null,
      abnormalDetailPieChart = null,
      avgHandleDurationPieChart = null,
      newCountLineChart = null,
      avgHandleDurationLineChart = null
    } = res.data

    useStatical('vehicleAbnormalPieChart').init({
      type: 'pie',
      config: {
        chart: vehicleAbnormalPieChart,
        option: transalteT(vehicleAbnormalPieChartOption)
      }
    })

    useStatical('abnormalDetailPieChart').init({
      type: 'pie',
      config: {
        chart: abnormalDetailPieChart,
        option: transalteT(abnormalDetailPieChartOption)
      }
    })
    useStatical('avgHandleDurationPieChart').init({
      type: 'pie',
      config: {
        chart: avgHandleDurationPieChart,
        option: transalteT(avgHandleDurationPieChartOption)
      }
    })
    useStatical('newCountLineChart').init({
      type: 'line',
      config: {
        chart: newCountLineChart,
        option: transalteT(deepClone(newCountLineChartOption))
      }
    })
    useStatical('avgHandleDurationLineChart').init({
      type: 'line',
      config: {
        chart: avgHandleDurationLineChart,
        option: transalteT(deepClone(avgHandleDurationLineChartOption))
      }
    })

    titleShow.value = true
  })
}

onBeforeUnmount(() => {
  useStatical('vehicleAbnormalPieChart').destroy()
  useStatical('abnormalDetailPieChart').destroy()
  useStatical('avgHandleDurationPieChart').destroy()
  useStatical('newCountLineChart').destroy()
  useStatical('avgHandleDurationLineChart').destroy()
})

getData()
</script>
<style lang="less" scoped>
@import '@/styles/statistical.less';
</style>
