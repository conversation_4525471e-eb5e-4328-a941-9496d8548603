import { basicLineOption, basicBarSeries, basicBarOption } from '@/utils/chart-options'

// 任务完成比例
export const taskStatusPieChartOption = {
  ...basicBarOption,
  series: [
    {
      ...basicBarSeries,
      name: 'message.taskStatistical.taskStatusPieChart'
    }
  ]
}
// 新建任务数量
export const createTaskCountPieChartOption = {
  ...basicBarOption,
  series: [
    {
      ...basicBarSeries,
      name: 'message.taskStatistical.createTaskCountPieChart'
    }
  ]
}
// 平均分配时长
export const avgAllocationDurationPieChartOption = {
  ...basicBarOption,
  series: [
    {
      ...basicBarSeries,
      name: 'message.taskStatistical.avgAllocationDurationPieChart'
    }
  ],
  unit: 'time'
}

// 平均工作时长
export const avgExecuteDurationPieChartOption = {
  ...basicBarOption,
  series: [
    {
      ...basicBarSeries,
      name: 'message.taskStatistical.avgExecuteDurationPieChart'
    }
  ],
  unit: 'time'
}

// 新增任务数量
export const createTaskCountLineChartOption = {
  ...basicLineOption
}
// 结束任务数量
export const endTaskCountLineChartOption = {
  ...basicLineOption
}
// 平均分配时长
export const avgAllocationDurationLineChartOption = {
  ...basicLineOption,
  unit: 'time'
}
// 平均工作时长
export const avgExecuteDurationDurationLineChartOption = {
  ...basicLineOption,
  unit: 'time'
}

// CPU使用率
export const cpuUseRateOption = {
  ...basicLineOption,
  yAxisUnit: '%'
}
// 内存使用率
export const memoryUseRateOption = {
  ...basicLineOption,
  yAxisUnit: '%'
}
// 硬盘容量变化
export const hardDiskCapOption = {
  ...basicLineOption,
  yAxisUnit: 'GB'
}
// MySql操作次数
export const mySqlTimesOption = {
  ...basicLineOption
}
