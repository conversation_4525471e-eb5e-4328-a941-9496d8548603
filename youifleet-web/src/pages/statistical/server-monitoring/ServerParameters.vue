<template>
  <div class="container">
    <div class="box" v-for="item in columns" :key="item.prop">
      <span>{{ item.title }}</span>
      <div>
        <span class="value">{{ serverParameter[item.prop as keyof ServerParameter] }}</span>
        <span class="unit">{{ item.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLocale } from 'youibot-plus'
const { t } = useLocale()

interface ServerParameter {
  cpuCore: number
  threadCount: number
  memory: number
  disk: number
}
const props = defineProps({
  serverParameter: {
    type: Object as () => ServerParameter,
    default: () => ({
      cpuCore: 0,
      threadCount: 0,
      memory: 0,
      disk: 0
    })
  }
})
const columns = [
  {
    title: t('message.serverMonitoring.cpuCores'),
    unit: t('message.serverMonitoring.cpuCoresUnit'),
    prop: 'cpuCore'
  },
  {
    title: t('message.serverMonitoring.totalThreads'),
    unit: t('message.serverMonitoring.thread'),
    prop: 'threadCount'
  },
  {
    title: t('message.serverMonitoring.memory'),
    unit: 'G',
    prop: 'memory'
  },
  {
    title: t('message.serverMonitoring.diskCapacity'),
    unit: 'GB',
    prop: 'disk'
  }
]
</script>

<style lang="less" scoped>
.container {
  display: flex;
  gap: 10px;

  .box {
    width: 25%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #fff;
    gap: 20px;
    font-size: 18px;
    font-weight: 700;
  }
}
</style>
