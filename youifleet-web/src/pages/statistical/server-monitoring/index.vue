<template>
  <div class="wrap">
    <div class="date-wrap">
      <div class="date">
        <DatePicker v-model:dateVal="dateVal" @update:dateVal="getData"></DatePicker>
      </div>
    </div>
    <div class="statistic-wrap">
      <div class="statistic-wrap-content">
        <div class="statistic-wrap-row">
          <div class="block flex5">
            <div class="bg" style="height: 100%">
              <div class="chart-title" v-if="titleShow">
                <p class="chart-title-text">{{ t('message.serverMonitoring.serverParameter') }}</p>
              </div>
              <ServerParameters :serverParameter="serverData" style="height: 100%; width: 100%; padding: 40px 15px 20px"></ServerParameters>
            </div>
          </div>
          <div class="block flex5 bg">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.serverMonitoring.serverUsage') }}</p>
            </div>
            <ServerUsage :usageRate="usageRateData" style="height: 100%; width: 100%; padding: 40px 15px 20px"></ServerUsage>
          </div>
        </div>

        <div class="statistic-wrap-row">
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.serverMonitoring.cpuLineChart') }}</p>
            </div>
            <v-chart id="cpuLineChart" autoresize />
          </div>
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.serverMonitoring.memLineChart') }}</p>
            </div>
            <v-chart id="memLineChart" autoresize />
          </div>
        </div>

        <div class="statistic-wrap-row">
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.serverMonitoring.diskLineChart') }}</p>
            </div>
            <v-chart id="diskLineChart" autoresize />
          </div>
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.serverMonitoring.mysqlLineChart') }}</p>
            </div>
            <v-chart id="mysqlLineChart" autoresize />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs, { Dayjs } from 'dayjs'
import useStatical from '@/hooks/use-statical'
import VChart from 'vue-echarts'
import { cpuUseRateOption, memoryUseRateOption, hardDiskCapOption, mySqlTimesOption } from './option'
import { getStatisticsServer } from '@/api/statistics'
import { deepClone, useLocale } from 'youibot-plus'

const { t } = useLocale()
const titleShow = ref(false)
//过去七天的数据
const dateVal = ref<[Dayjs, Dayjs]>([dayjs(new Date().getTime() - 3600 * 1000 * 24 * 6), dayjs(new Date())])
const transalteT = (option: any) => {
  Object.keys(option).forEach((key: string) => {
    if (key === 'series') {
      option[key].map((item: any, index: number) => {
        option[key][index].name = t(option[key][index].name)
      })
    }
  })
  return option
}
const serverData = ref<object>({})
const usageRateData = ref<object>({
  cpuRate: 0,
  memRate: 0,
  diskRate: 0
})
const getData = () => {
  getStatisticsServer({
    startTime: dayjs(dateVal.value[0]).format('YYYY-MM-DD'),
    endTime: dayjs(dateVal.value[1]).format('YYYY-MM-DD')
  }).then(res => {
    const {
      cpuLineChart = null,
      memLineChart = null,
      diskLineChart = null,
      mysqlLineChart = null,
      specification = null,
      usageRate = null
    } = res.data

    serverData.value = specification
    usageRateData.value = usageRate

    useStatical('cpuLineChart').init({
      type: 'line',
      config: {
        chart: cpuLineChart,
        option: transalteT(deepClone(cpuUseRateOption))
      }
    })
    useStatical('memLineChart').init({
      type: 'line',
      config: {
        chart: memLineChart,
        option: transalteT(deepClone(memoryUseRateOption))
      }
    })
    useStatical('diskLineChart').init({
      type: 'line',
      config: {
        chart: diskLineChart,
        option: transalteT(deepClone(hardDiskCapOption))
      }
    })

    useStatical('mysqlLineChart').init({
      type: 'line',
      config: {
        chart: mysqlLineChart,
        option: transalteT(deepClone(mySqlTimesOption))
      }
    })

    titleShow.value = true
  })
}

onBeforeUnmount(() => {
  useStatical('cpuLineChart').destroy()
  useStatical('memLineChart').destroy()
  useStatical('diskLineChart').destroy()
  useStatical('mysqlLineChart').destroy()
})

getData()
</script>
<style lang="less" scoped>
@import '@/styles/statistical.less';
</style>
