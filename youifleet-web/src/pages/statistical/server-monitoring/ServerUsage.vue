<template>
  <div class="container">
    <div class="usage-row" v-for="column in columns" :key="column.props">
      <div class="title" :style="{color: props.textColor ? props.textColor : ''}">{{ column.title }}</div>
      <div class="bar-box">
        <span v-for="num in 20" class="bar" :class="getBgClass(num)" :style="getOpacity(column, num)">
          <span v-if="false">{{ num * 5 }}</span>
        </span>
      </div>
      <div class="percent" :style="{color: props.textColor ? props.textColor : ''}">{{ usageRate[column.prop] + '%' }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue'
import { useLocale } from 'youibot-plus'

const { t } = useLocale()
const props = defineProps({
  usageRate: {
    type: Object as PropType<{ [key: string]: any }>,
    default: () => ({
      cpuRate: 0,
      memRate: 0,
      diskRate: 0
    })
  },
  textColor: {
    type: String,
    default: ''
  }
})
const columns = ref<{ [key: string]: any }[]>([])
columns.value = [
  {
    title: t('message.serverMonitoring.cpuLineChart'),
    prop: 'cpuRate'
  },
  {
    title: t('message.serverMonitoring.memLineChart'),
    prop: 'memRate'
  },
  {
    title: t('message.serverMonitoring.diskUsage'),
    prop: 'diskRate'
  }
]

const getBgClass = (num: number) => {
  switch (true) {
    case num <= 10:
      return 'free'
    case num <= 14:
      return 'warning'
    default:
      return 'full'
  }
}

const getOpacity = (column: any, num: number) => {
  const lastIndex = Math.ceil(props.usageRate[column.prop] / 5)
  if (num <= lastIndex) {
    return {
      opacity: 1
    }
  } else {
    return {
      opacity: 0.3
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  gap: 14px;

  .usage-row {
    height: 33%;
    display: flex;
    align-items: center;

    .title {
      margin-right: 24px;
      font-size: 18px;
      font-weight: 700;
    }

    .bar-box {
      flex: 1;
      height: 70%;
      display: flex;
      // gap: 10px;
      justify-content: space-around;
      .bar {
        width: 3.4%;
        height: 100%;
      }
    }

    .percent {
      width: 10%;
      padding-left: 10px;
      font-size: 18px;
      font-weight: 700;
    }
  }
}

.free {
  background: #14da14 !important;
}
.warning {
  background: #de8807 !important;
}
.full {
  background: #c81207 !important;
}
</style>
