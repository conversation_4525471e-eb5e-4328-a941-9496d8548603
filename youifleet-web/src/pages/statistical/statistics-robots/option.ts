import { basicLineOption, basicBarSeries, basicBarOption } from '@/utils/chart-options'

// 机器人状态趋势
export const statusLineChartOption = {
  ...basicLineOption,
  color: ['#627cdc', '#a5e07b', '#fbdb55', '#f66f6e', '#b9b9b9'],
  unit: 'time'
}

// 机器人状态比例
export const statusPieChartOption = {
  ...basicBarOption,
  series: [
    {
      ...basicBarSeries,
      name: 'message.statisticsRobots.statusPieChart'
    }
  ],
  color: ['#627cdc', '#a5e07b', '#fbdb55', '#f66f6e', '#b9b9b9'],
  unit: 'time'
}

// 机器人稼动率
export const utilizeRateLineChartOption = {
  ...basicLineOption
}
