<template>
  <div class="wrap">
    <div class="date-wrap">
      <div class="date">
        <DatePicker v-model:dateVal="dateVal" @update:dateVal="getData"></DatePicker>
      </div>
    </div>
    <div class="statistic-wrap">
      <div class="statistic-wrap-robots-content">
        <div class="statistic-wrap-robots-content-row">
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.statisticsRobots.statusLineChart') }}</p>
            </div>
            <v-chart id="statusLineChart" autoresize />
          </div>
          <div class="block flex5">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.statisticsRobots.utilizeRateLineChart') }}</p>
            </div>
            <v-chart id="utilizeRateLineChart" autoresize />
          </div>
        </div>
        <div class="statistic-wrap-robots-content-row">
          <div class="block flex25">
            <div class="chart-title" v-if="titleShow">
              <p class="chart-title-text">{{ t('message.statisticsRobots.statusPieChart') }}</p>
            </div>
            <v-chart id="statusPieChart" autoresize />
          </div>
          <div class="block flex25"></div>
          <div class="block flex25"></div>
          <div class="block flex25"></div>
        </div>
        <!-- <div class="statistic-wrap-content-row"></div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs, { Dayjs } from 'dayjs'
import useStatical from '@/hooks/use-statical'
import VChart from 'vue-echarts'
import { statusLineChartOption, statusPieChartOption, utilizeRateLineChartOption } from './option'
import { getStatisticsVehicle } from '@/api/statistics'
import { deepClone, useLocale } from 'youibot-plus'

const { t } = useLocale()
const titleShow = ref(false)

//过去七天的数据
const dateVal = ref<[Dayjs, Dayjs]>([dayjs(new Date().getTime() - 3600 * 1000 * 24 * 6), dayjs(new Date())])

const transalteT = (option: any) => {
  Object.keys(option).forEach((key: string) => {
    if (key === 'series') {
      option[key].map((item: any, index: number) => {
        option[key][index].name = t(option[key][index].name)
      })
    }
  })
  return option
}

const getData = () => {
  getStatisticsVehicle({
    startTime: dayjs(dateVal.value[0]).format('YYYY-MM-DD'),
    endTime: dayjs(dateVal.value[1]).format('YYYY-MM-DD')
  }).then(res => {
    const { statusLineChart = null, statusPieChart = null, utilizeRateLineChart = null } = res.data

    useStatical('statusLineChart').init({
      type: 'line',
      config: {
        chart: statusLineChart,
        option: transalteT(deepClone(statusLineChartOption))
      }
    })

    useStatical('statusPieChart').init({
      type: 'pie',
      config: { chart: statusPieChart, option: transalteT(statusPieChartOption) }
    })
    useStatical('utilizeRateLineChart').init({
      type: 'line',
      config: {
        chart: utilizeRateLineChart,
        option: transalteT(deepClone(utilizeRateLineChartOption))
      }
    })
    titleShow.value = true
  })
}

onBeforeUnmount(() => {
  useStatical('statusLineChart').destroy()
  useStatical('statusPieChart').destroy()
  useStatical('utilizeRateLineChart').destroy()
})

getData()
</script>
<style lang="less" scoped>
@import '@/styles/statistical.less';
</style>
