<template>
  <a-spin :spinning="isSpinning">
    <div class="agv-map-wrap">
      <div class="agv-map-wrap-inner" :class="{ fullscreen: fullScreen }" :style="{ background: backgroundColor }">
        <yi-agv-map
          v-if="mapData && mapData?.mapInfoList"
          :map-data="mapData"
          :baseRightOperation="baseRightOperation"
          :marker-config="mapConfig.markerConfig"
          :path-config="mapConfig.pathConfig"
          :area-config="mapConfig.areaConfig"
          :isSelectiveMarkerMode="isSelectiveMarkerMode"
          :wrapDompWidth="wrapDompWidth"
          :concludeFn="concludeFn"
          @initFinish="initFinish"
          @resetAgvPosition="resetAgvPosition"
          @changeMode="changeModel"
          @clickAgv="changeAgv"
          @escCancelEvent="clickMapOutside"
          @escapeKey="escapeKey"
          @unrightClick="unrightClick"
          @clickMarker="markerClick"
          @clickArea="operationCancellation"
          @clickElevator="operationCancellation"
          @clickAirShower="operationCancellation"
          @clickAutoDoor="operationCancellation"
          @clickPath="operationCancellation"
          @dbClickAgv="dbClickAgv"
          @dbClickMarker="clickItem"
          @dbClickArea="clickItem"
          @dbClickElevator="clickItem"
          @dbClickAirShower="clickItem"
          @dbClickAutoDoor="clickItem"
          @rightClickEvent="rightClickEvent"
          @dbClickWarehouse="
            (data: any, e: EventPoint) => {
              clickItem({ ...data, isWarehouse: true }, e)
            }
          " />
        <!-- 暂无地图 -->
        <NoData v-else-if="!mapData && !isSpinning" :text="t('message.map.noMapYet')" />
        <!-- 暂无定位图数据 -->
        <NoData v-else-if="!mapData?.mapInfoList && !isSpinning" :text="t('message.map.unmapped')" />
        <div v-if="mapData && !isSpinning && isPause === 1" class="pause-mask">
          <div class="pause-prompt">{{ t('message.fullTimeOut') }}...</div>
        </div>
        <a-spin
          size="large"
          v-if="isPausePrompSpinning"
          :spinning="isPausePrompSpinning"
          :tip="pausePrompTip + '  ' + count"
          wrapperClassName="pause-prompt-mask">
          <span></span>
        </a-spin>
      </div>
    </div>
    <AgvDetailCard
      v-if="currentClick === 'agv'"
      :visible="currentClick === 'agv'"
      :vehicleCode="selectedAgv"
      :currentX="currentX"
      :currentY="currentY - 48"
      :locatingCode="selectedLocatingCode"
      :type="agvCardType"
      @changeModel="agvModeEvent"
      @taskCancel="taskCancelByTaskNo"
      @clickTask="clickTask"
      @agvDataChange="agvDataChange"></AgvDetailCard>

    <AgvMarkerCard
      v-if="currentClick === 'Marker'"
      :visible="currentClick === 'Marker'"
      :code="currentClickCode"
      :vehicleMapCode="selectedMap"
      :locatingCode="selectedLocatingCode"
      :currentX="currentX"
      :currentY="currentY - 48" />
    <AgvAreaCard
      v-if="currentClick === 'MapArea'"
      :visible="currentClick === 'MapArea'"
      :code="currentClickCode"
      :vehicleMapCode="selectedMap"
      :currentX="currentX"
      :currentY="currentY - 48" />
    <AgvEleavtorCard
      v-if="currentClick === 'Elevator'"
      :visible="currentClick === 'Elevator'"
      :code="currentClickCode"
      :currentX="currentX"
      :currentY="currentY - 48" />
    <AgvAutodoorCard
      v-if="currentClick === 'AutoDoor'"
      :visible="currentClick === 'AutoDoor'"
      :code="currentClickCode"
      :vehicleMapCode="selectedMap"
      :currentX="currentX"
      :currentY="currentY - 48" />
    <AgvAirShowerCard
      v-if="currentClick === 'AirShowerDoor'"
      :visible="currentClick === 'AirShowerDoor'"
      :code="currentClickCode"
      :vehicleMapCode="selectedMap"
      :currentX="currentX"
      :currentY="currentY - 48" />
    <AgvWarehouseCard
      v-if="currentClick === 'warehouse'"
      :visible="currentClick === 'warehouse'"
      :warehouse-list="warehouseData"
      :currentX="currentX"
      :currentY="currentY - 48" />
    <AgvMapSearch
      :mapList="mapList"
      v-model:selectedMap="selectedMap"
      v-model:locatingCode="selectedLocatingCode"
      @handleMapChange="handleMapChange"
      @handleLocatingMapChange="agvMapSearchLocatingMapChange"
      @click.native="cancelAllRightClick" />
    <AgvMapItemSearch
      v-model:selectedMap="selectedMap"
      v-model:selectedItem="selectedItem"
      :locatingCode="selectedLocatingCode"
      @clickSearchItem="clickSearchItem"
      @click="cancelAllRightClick" />

    <!-- <div v-if="!isShowPause" class="global-operation">
      <my-button
        v-if="isPause === 0"
        class="global-operation-suspend"
        :ghost="false"
        shape="round"
        :loading="pauseButState"
        :debounceTime="500"
        :btn-item="{
          name: t('message.robotManage.pause'),
          icon: 'iconfont icon-zanting',
          onClick: () => {
            postMapVehicleMapsPauseMapFn()
          }
        }"></my-button>
      <my-button
        v-else
        class="global-operation-recover"
        :ghost="false"
        shape="round"
        :loading="pauseButState"
        :debounceTime="500"
        :btn-item="{
          name: t('message.robotManage.restore'),
          icon: 'iconfont icon-huifu',
          onClick: () => {
            postMapVehicleMapsRecoverMapFn()
          }
        }"></my-button>
    </div> -->
  </a-spin>
  <add-form
    v-model:visible="visibleAdd"
    v-model:data="State.addData"
    :isMonitoringStation="true"
    :zIndex="2100"
    :marker="selectionMarker"
    @amonitoringClick="amonitoringClickClose"
    @confirm="taskReset"
    @cancel="taskReset"></add-form>
  <AssignMap :visible="visibleMap" :zIndex="3200" @close="closeAssignMap" @submit="submitAssignMap" />
</template>
<script setup lang="ts">
import type { ITableRow as ITypeTableRow } from '@/pages/operations/tasks/task-type/config'
import AddForm from '@/components/task-manage/add-form.vue'
import { ITableRow as ILocationList } from '@/pages/operations/storage-location/storage-location-list/config'
import useNode from '@/hooks/use-node'
import { BaseArea, EventPoint, fullscreenchange, useLocale, globalRightOperatObj, deepClone, AreaData, MarkerData } from 'youibot-plus'

import { ICamera } from 'youibot-gl'
import { useMap, INewMarkerData, IBasePath, AgvMap, PathMap } from '@/hooks/use-map'
import { useAgv } from '@/hooks/use-agv'
import { taskCancelByTaskNo, getAgvDisplaySetupData } from '@/pages/monitoring/agv-map/config'
import { InitFinishObj, MapConfig } from '@/pages/operations/maps/map-edit/config'
import {
  mapCodeBackLocalCode,
  mapEditDisplaySetupLocalData,
  monitorLocalData,
  agvMapDisplaySetupLocalData,
  tokenLocalData
} from '@/utils/storage'
import useStore from '@/stores'
import { getQuery, permission, toNext } from '@/router'
import { useInitMap } from '@/pages/monitoring/agv-map/use-init-map'
import { useMapOperate } from '@/pages/monitoring/agv-map/use-map-operate'
import { useInitDisplay } from '@/pages/operations/maps/map-edit/use-init-display'
import { useInitAgvDisplay } from '@/pages/monitoring/agv-map/use-init-agv-display'
import { useTaskDelivery } from '@/pages/monitoring/agv-map/use-task-delivery'
import { useSelectiveFocus } from '@/pages/monitoring/agv-map/use-selective-focus'
import { useGetTimingData } from '@/pages/monitoring/agv-map/use-get-timing-data'
import { useAgvOperate } from '@/pages/monitoring/agv-map/use-agv-operate'
import { useKeydown } from '@/pages/monitoring/agv-map/use-keydown'
import { useClearReset } from '@/pages/monitoring/agv-map/use-clear-reset'
import { useMapSocket } from '@/pages/monitoring/agv-map/use-socket'

import { useAgvControl } from '@/hooks/use-agv-control'
import { getVehicleDetailVehicleCode, postVehicleMapAppointMapCode } from '@/api/vehicle'
import {
  postMapVehicleMapsPauseMapVehicleMapCode,
  postMapVehicleMapsRecoverMapVehicleMapCode,
  postMapVehicleMapsGetAllWithoutMapInfo
} from '@/api/map'
import { getOperateText, messageTip } from '@/utils'
import { setOperatList } from '@/components/agv-map/left-agv-list/config'
import { useCountdown } from '@/hooks/use-countdown'
const { getTaskTypeDefault, isDefaultRequired, isDefaultRequiredOnePoint } = useNode()
const { t } = useLocale()
const { initAgvControl, openManualAGVSocket, closeManualAGVSocket, destroy: agvControlDestroy } = useAgvControl()
// 地图配置
const mapConfig = reactive({
  markerConfig: {
    isInteractive: true,
    isOnlyClickInteractive: true,
    maxHeight: undefined as undefined | number,
    depth: 30
  },
  pathConfig: {
    isInteractive: true,
    isOnlyClickAutoDoor: true,
    isOnlyClickPath: true,
    maxLineWidth: undefined as undefined | number
  },
  areaConfig: {
    isInteractive: true,
    isOnlyClickAreaArr: ['ControlArea', 'ShowArea', 'ChannelArea', 'NoRotatingArea', 'NoParkingArea'],
    codeConfig: { height: undefined as undefined | number }
  }
})
// 右键初始菜单缓存
const agvMapOperation = ref(deepClone(globalRightOperatObj))
// 右键菜单
const baseRightOperation = ref(deepClone(globalRightOperatObj))
// 右键事件
const rightEvennt = ref()
// 下发任务弹窗
const visibleAdd = ref(false)
// 选中任务
const State = reactive({
  addData: {} as ITypeTableRow
})
// ref
const leftTaskListRef = ref<any>(null)
const leftAgvListRef = ref<any>(null)
// 选中菜单
const isMenu = ref(0)
// 地图数据
const mapData = ref()
let mapList = ref<AgvMap[] | undefined>([])
// 选中地图
let selectedMap = ref()
// 选中定位图
let selectedLocatingCode = ref()
// 选中元素
let selectedItem = ref()
// 定位图镜头缓存
const camerasData = ref<ICamera | undefined>()
// 选中agv
let selectedAgv = ref()
let selectedAgvData = ref()
// let 重定位时agv数据缓存
const reorientationAgvData = ref()
// 选中元素类型
let currentClick = ref() // 当前选中的元素(agv、area、marker、elevator、autodoor、airShower)
let currentClickCode = ref() // 当前选中的元素数据的code，agv的选中数据由selectedAgv单独维护
// 卡片偏移量
const currentX = ref()
const currentY = ref()
const agvCardType = ref('all')
// 库位集合
const warehouseData = ref<ILocationList[]>([])

// loading状态
const isSpinning = ref(true)
// 全屏状态
const fullScreen = ref(false)
// 需要显示运行路径的agv
let isShowPathAgvCodes = ref<string[]>([])

// marker点数据集合
const markers = ref<INewMarkerData[]>([])
// 路径集合
const paths = ref<IBasePath[]>([])
const pathMap = ref<PathMap>(new Map())
// 区域集合
const areas = ref<BaseArea[]>([])
const areaMap = ref<Map<string, BaseArea>>(new Map())
// 背景颜色
const backgroundColor = ref('#fff')
// 是否启用点云
let isCloud = ref(false)
// 选中点位数据
const selectionMarker = ref(undefined)
// 是否能点击点位
const isSelectiveMarkerMode = ref(false)
//
const wrapDompWidth = ref(0)

// 地图初始后返回方法集合
const mapFn = ref<InitFinishObj>()
const visibleMap = ref(false)

//socket 设置参数
let socketSetDataFn = ref<((data: any) => void) | null>(null)
//
const ssignMap = reactive({
  assignMapIds: [] as string[]
})
// 获取地图缓存
const getMonitorLocalData = () => {
  const monitorStr = monitorLocalData()
  return monitorStr ? JSON.parse(monitorStr) : null
}
const monitorData = getMonitorLocalData()
// 地图缓存
const currentTokenMap = monitorData?.currentMap
// 底部详情ref
const bottomDetail = ref()
const bottomDetailData = ref()
// 消息Data
const noticeList = ref()

// 菜单
isMenu.value = monitorData?.isMenu
//获取是否有地图选中缓存
selectedMap.value = currentTokenMap ? currentTokenMap : ''
const mapBack = mapCodeBackLocalCode()
if (mapBack?.mapCode && mapBack?.locatingCode) {
  selectedMap.value = mapBack.mapCode
  selectedLocatingCode.value = mapBack.locatingCode
  mapCodeBackLocalCode('')
}
// 是否有配置
let concludeFn = ref<() => void | undefined>()
const setConcludeFn = () => {
  // let setupData = agvMapDisplaySetupLocalData()
  concludeFn.value = initDisplaySetup
}
// 全场暂停lodin
const pausePrompTip = ref(t('message.fullTimeOut'))
const isPausePrompSpinning = ref(false)
const isPause = ref(0)
const runningVehicleNum = ref(0)
const pauseButState = ref(false)

const { agvMapDisplaySetupData, settings, mapDisplaySetupData } = useStore()
// 地图获取初始话
const { getMapData } = useMap()
// agv初始后
const useAgvFn = useAgv(mapFn, mapData, markers, areas)
// 定时获取设备状态
const { getMapAirShowerDoorsFn, getMapAutoDoorsFn, getResourceTrafficVehicleMapCodeFn, getMapMarkersGetAllWithWarehouseFn } =
  useGetTimingData(mapFn, mapData, pathMap, markers, areaMap)
// /初始化Socket
const {
  webSocketFn,
  close: webSocketClose,
  socketSetData
} = useMapSocket(
  mapFn,
  mapData,
  warehouseData,
  currentClickCode,
  markers,
  areaMap,
  pathMap,
  useAgvFn,
  isShowPathAgvCodes,
  bottomDetailData,
  noticeList,
  isPause,
  runningVehicleNum,
  isPausePrompSpinning,
  getMapMarkersGetAllWithWarehouseFn,
  getMapAirShowerDoorsFn,
  getMapAutoDoorsFn,
  getResourceTrafficVehicleMapCodeFn
)
// 重置
const { cancelAgvMapRightClick, cancelAllRightClick, clickMapOutside, unrightClick, operationCancellation } = useClearReset(
  mapFn,
  isShowPathAgvCodes,
  selectedAgv,
  currentClick,
  leftTaskListRef,
  leftAgvListRef,
  isSelectiveMarkerMode,
  selectedAgvData,
  useAgvFn,
  closeManualAGVSocket,
  socketSetData
)
// 地图配置监听
agvMapDisplaySetupData.$subscribe((mutation, state) => {
  initDisplaySetup()
})
// 个性化弹窗监听
settings.$subscribe((mutation, state) => {
  if (state.visible) {
    cancelAllRightClick()
  }
})
// 地图配置监听
mapDisplaySetupData.$subscribe((mutation, state) => {
  setElementSize()
})
// 设置元素大小
const setElementSize = () => {
  const elementSizeStr = mapEditDisplaySetupLocalData()
  if (elementSizeStr) {
    const data = JSON.parse(elementSizeStr)
    displaySetupConfirm(data)
  }
}
// 元素大小显示
const { displaySetupConfirm } = useInitDisplay(mapFn, mapData, mapConfig as unknown as MapConfig, ref(''))
// 元素控制
const { initDisplaySetup } = useInitAgvDisplay(mapFn, mapData, isCloud, selectedAgv, backgroundColor, useAgvFn)

//点击除Agv外的元素
const clickItem = (data: any, e: EventPoint) => {
  if (!isSelectiveMarkerMode.value) {
    currentX.value = e?.clientX
    currentY.value = e?.clientY
    const {
      code = null,
      spriteType = null,
      areaType = null,
      autoDoorData = null,
      elevatorData = null,
      airShowerData = null,
      isWarehouse = null,
      warehouseList = null
    } = data
    areaType && ((currentClick.value = 'MapArea'), (currentClickCode.value = code))
    spriteType === 'marker' && ((currentClick.value = 'Marker'), (currentClickCode.value = code))
    elevatorData && ((currentClick.value = 'Elevator'), (currentClickCode.value = elevatorData.code))
    autoDoorData && ((currentClick.value = 'AutoDoor'), (currentClickCode.value = autoDoorData.code))
    airShowerData && ((currentClick.value = 'AirShowerDoor'), (currentClickCode.value = airShowerData.code))
    isWarehouse && ((currentClick.value = 'warehouse'), (warehouseData.value = warehouseList), (currentClickCode.value = code))
  }
}

// 菜单点击
const menuClick = (type: number) => {
  let data = getMonitorLocalData()
  isMenu.value = type === isMenu.value ? 0 : type
  data = data ? { ...data, isMenu: isMenu.value } : { isMenu: isMenu.value }
  monitorLocalData(JSON.stringify(data))
  bottomDetail.value.getData()
}

// esc清除
const escapeKey = () => {
  mapConfig.markerConfig.depth = 30

  isSelectiveMarkerMode.value = false
  messageHideFn()
  resetPositionMessageHideFn()
}

// 右侧菜单缓存
const handleCollapsed = () => {
  nextTick(() => {
    wrapDompWidth.value = document.getElementsByClassName('agv-map-wrap')[0].clientWidth
  })
  isMenu.value = [1, 2, 3].includes(isMenu.value) ? 0 : monitorData?.isMenu ? monitorData?.isMenu : 1
  currentClick.value = null
  let data = getMonitorLocalData()
  data = data ? { ...data, isMenu: isMenu.value } : { isMenu: isMenu.value }
  monitorLocalData(JSON.stringify(data))
}
const setAgvFocusXY = (vehicleCode: string) => {
  getVehicleDetailVehicleCode({}, { vehicleCode: vehicleCode }).then(res => {
    const vehicleMapCode = res.data?.vehicleMapCode
    if (vehicleMapCode) {
      clickSearchItem('Vehicle', { data: res.data, type: 'Vehicle' }, vehicleMapCode === mapData.value.code, vehicleMapCode)
    }
  })
}
// 按键事件初始
useKeydown(mapFn, fullScreen, t, initAgvControl, agvControlDestroy)
// 任务下发
const { markerClick, taskReset, amonitoringClickClose, clickTask, messageHideFn } = useTaskDelivery(
  mapFn,
  mapConfig as unknown as MapConfig,
  State,
  visibleAdd,
  isSelectiveMarkerMode,
  selectionMarker,
  selectedAgv,
  currentClick,
  useAgvFn,
  t,
  isDefaultRequired,
  getTaskTypeDefault,
  isDefaultRequiredOnePoint,

  operationCancellation
)
const handleLocatingMapChangeFn = (value: string) => {
  handleLocatingMapChange(value)
}
const agvMapSearchLocatingMapChange = (value: string) => {
  reorientationAgvData.value = undefined

  handleLocatingMapChange(value)
}
const clickSearchItemFn = (type: string, data: any, isCurrentMap: boolean, vehicleMapCode: string) => {
  clickSearchItem(type, data, isCurrentMap, vehicleMapCode)
}

// agv操作
const { agvModeEvent, dbClickAgv, resetAgvPosition, changeAgv, resetPositionMessageHideFn, agvDataChange, resetPositionHandler } =
  useAgvOperate(
    mapFn,
    mapData,
    selectedAgv,
    isShowPathAgvCodes,
    currentClick,
    currentX,
    currentY,
    isCloud,
    isSelectiveMarkerMode,
    reorientationAgvData,
    visibleMap,
    agvCardType,
    ssignMap,
    useAgvFn,
    t,
    messageHideFn,
    initDisplaySetup,
    handleLocatingMapChangeFn,
    openManualAGVSocket,
    clickSearchItemFn,
    socketSetData
  )

// 地图操作
const { getData, handleMapChange, handleLocatingMapChange } = useMapOperate(
  mapFn,
  mapData,
  pathMap,
  markers,
  areas,
  areaMap,
  mapList,
  selectedMap,
  selectedLocatingCode,
  isSpinning,
  isSelectiveMarkerMode,
  selectionMarker,
  selectedAgv,
  isCloud,
  camerasData,
  currentClick,
  isShowPathAgvCodes,
  reorientationAgvData,
  useAgvFn,
  getMapData,
  getMonitorLocalData,
  messageHideFn,
  resetPositionMessageHideFn
)
// 选中,聚焦
const { clickSearchItem, agvFocusXY } = useSelectiveFocus(
  mapFn,
  mapData,
  pathMap,
  markers,
  currentClick,
  currentClickCode,
  isSelectiveMarkerMode,
  selectionMarker,
  selectedMap,
  selectedAgv,
  isSpinning,
  isCloud,
  currentX,
  currentY,
  useAgvFn,
  messageHideFn,
  resetPositionMessageHideFn,
  handleMapChange
)

// 获取地图暂停状态
function postMapVehicleMapsGetAllWithoutMapInfoFn() {
  return postMapVehicleMapsGetAllWithoutMapInfo(mapData.value?.code ? [mapData.value?.code] : []).then(res => {
    isPause.value = res.data.length && res.data[0].isPause
  })
}

// 地图初始化
const { initFinish } = useInitMap(
  mapFn,
  mapData,
  pathMap,
  markers,
  areas,
  isSpinning,
  wrapDompWidth,
  camerasData,
  reorientationAgvData,
  selectedAgv,
  initDisplaySetup,
  setElementSize,
  resetPositionHandler,
  postMapVehicleMapsGetAllWithoutMapInfoFn,
  webSocketFn,
  webSocketClose
)
onBeforeMount(() => {
  if (getQuery('token')) {
    tokenLocalData(getQuery('token') as string)
  }
})
onMounted(() => {
  // 挂载完成后通知外层tms页面发送地图编号
  window.parent.postMessage('mounted', '*')
  // 接收外层tms传送的地图编号
  window.addEventListener('message', handleMessage)

  getData(currentTokenMap)
  // 监听全屏，放大缩小
  fullscreenchange(b => {
    fullScreen.value = !b
  })
  setConcludeFn()
})

const handleMessage = (event: any) => {
  let data = event.data
  // console.log('get data from outer ---', data)
  if (data.type === 'mapCode') {
    // 切换为tms仓库绑定的地图
    selectedMap.value = data.value
    handleMapChange(selectedMap.value)
  }
}
onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})

onBeforeUnmount(() => {
  isSelectiveMarkerMode.value = false
  messageHideFn()
  resetPositionMessageHideFn()
  useAgvFn.clearCloudTimer()
  agvControlDestroy()
})
/**z
 * 操作回调
 *
 * @param {number} mode "操作映射值"
 */
function changeModel(mode: number): void {
  currentClick.value = ''
  if (mode === 0.5) {
    fullScreen.value = !fullScreen.value
  } else if (mode === 6) {
    settings.setVisible(true)
    settings.setSelectedMenu(4)
  } else if (mode === 10.2) {
    toNext({
      path: `/mapEdit`,
      query: { mapCode: mapData.value.code, locatingCode: mapData.value.locatingCode, jumpType: 'agvMap' }
    })
  } else if ([11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32].includes(mode)) {
    agvModeEvent(mode, selectedAgvData.value.code, selectedAgvData.value, rightEvennt.value)
  }
}
const rightClickEvent = (evt: any, data: any, type: any) => {
  if (type === 'agv') {
    baseRightOperation.value.agvMap = setOperatList(data)
    selectedAgvData.value = data
    rightEvennt.value = evt
  } else {
    const rightOperation = deepClone(agvMapOperation.value.agvMap)
    if (!permission.includes('/operations/maps/map-list/edit')) {
      rightOperation.elementsOperate = []
    }
    baseRightOperation.value.agvMap = rightOperation
    selectedAgvData.value = null
  }
}
const closeAssignMap = () => {
  visibleMap.value = false
}
const { initCountdown, destroy: countdownDestroy, count } = useCountdown()

const postMapVehicleMapsPauseMapFn = () => {
  if (pauseButState.value) {
    messageTip(t('message.OperationIsTooFrequent'), 'error')
    return
  }
  pauseButState.value = true

  postMapVehicleMapsPauseMapVehicleMapCode({}, { vehicleMapCode: mapData.value.code })
    .then(res => {
      postMapVehicleMapsGetAllWithoutMapInfoFn()
      messageTip(t('message.completeTimeout'))
    })
    .finally(() => {
      pauseButState.value = false
    })
    .catch(err => {
      messageTip(err.msg, 'error')
    })
}
const postMapVehicleMapsRecoverMapFn = () => {
  if (pauseButState.value) {
    messageTip(t('message.OperationIsTooFrequent'), 'error')
    return
  }
  pauseButState.value = true
  pausePrompTip.value = t('message.fullRecoveryUnderway')
  isPausePrompSpinning.value = true
  initCountdown()

  postMapVehicleMapsRecoverMapVehicleMapCode({}, { vehicleMapCode: mapData.value.code })
    .then(res => {
      setTimeout(() => {
        postMapVehicleMapsGetAllWithoutMapInfoFn()
        messageTip(t('message.fullRecoverySuccessful'))
      }, 1000)
    })
    .finally(() => {
      setTimeout(() => {
        pauseButState.value = false
        isPausePrompSpinning.value = false
        countdownDestroy()
      }, 1000)
    })
    .catch(err => {
      messageTip(err.msg, 'error')
    })
}
const submitAssignMap = (mapCode: string) => {
  postVehicleMapAppointMapCode(JSON.stringify(ssignMap.assignMapIds), { mapCode })
    .then(res => {
      getOperateText(t, {
        type: 'robotManage.assignMap'
      })

      if (ssignMap.assignMapIds[0] === selectedAgv.value) {
        operationCancellation()
        ssignMap.assignMapIds = []
      }
      visibleMap.value = false
    })
    .catch(err => {
      visibleMap.value = false
      messageTip(err.msg, 'error')
    })
}
</script>
<style lang="less">
.pause-prompt-mask {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 4000;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 30%);

  & > div > .ant-spin {
    color: red !important;
  }

  .ant-spin-text {
    font-size: 38px;
  }
}
</style>
<style lang="less" scoped>
.agv-map-wrap {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;

  &-inner {
    width: 100%;
    height: 100%;
    padding: 10px;

    .no-map-wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #00000040;
      font-size: 32px;

      svg {
        display: block;
        margin: auto;
      }

      &-inner {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  &-list {
    width: 405px;
  }

  .fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 11;
    width: 100% !important;
    height: 100% !important;
  }

  .aside-middle {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 49px;
    height: 40px;
    // background-color: @aside-menu-background;

    &-collapsed {
      color: @collapsed_color;
      font-size: 20px;
    }
  }
}

.global-operation {
  position: absolute;
  top: 15px;
  right: 28px;
  z-index: 2001;

  &-suspend,
  &-suspend:hover,
  &-suspend:focus,
  &-suspend:active {
    display: flex;
    color: #333;
    background: #e8e8e8;
  }

  &-recover,
  &-recover:hover,
  &-recover:focus,
  &-recover:active {
    display: flex;
    color: #fff;
    background: #ff4d4f;
  }
}

.pause-mask {
  pointer-events: none;

  .pause-prompt {
    position: absolute;
    top: calc(50% - 50px);
    left: calc(50% - 140px);
    z-index: 500;
    color: #ff4d4f;
    font-size: 48px;
    text-align: center;
    user-select: none;
    pointer-events: none;

    /* 火狐 */

    /* webkit浏览器 */

    /* IE10 */

    /* 早期浏览器 */
  }
}
</style>
