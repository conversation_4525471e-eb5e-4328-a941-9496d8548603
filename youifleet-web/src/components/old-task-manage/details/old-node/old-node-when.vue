<template>
  <div class="branch-wrap" v-if="nodeConfig.conditionNodes">
    <div class="branch-box-wrap">
      <div class="branch-box">
        <div class="col-box" v-for="(item, index) in nodeConfig.conditionNodes" :key="index">
          <div class="condition-node">
            <div class="condition-node-box">
              <div class="setting-node">
                <div class="setting-node-wrap">
                  <span class="setting-node-icon" :class="`setting-node-icon-${getWhenState(item)?.state}`">
                    <i class="iconfont icon-a-binghangfenzhi"></i>
                  </span>
                  <span class="setting-node-text">{{ t('message.taskTypeArrangement.parallelBranch') }}</span>
                  <div
                    v-if="getNodeStatus?.length"
                    class="setting-node-icon-status"
                    :class="`setting-node-icon-status-${getWhenState(item)?.state}`">
                    <i v-if="getWhenState(item)?.state === 'Fail'" class="iconfont icon-chacha"></i>
                    <i v-else-if="getWhenState(item)?.state === 'Finished'" class="iconfont icon-gou"></i>
                    <i v-if="['Running', 'Cancel'].includes(getWhenState(item)?.state as string)" class="iconfont icon-shenglvehao"></i>
                  </div>
                </div>
              </div>
              <node-line />
            </div>
          </div>

          <old-node-status v-if="item.childNode" :nodeConfig="item.childNode" />
          <template v-if="index == 0">
            <div class="top-left-cover-line"></div>
            <div class="bottom-left-cover-line"></div>
          </template>
          <template v-if="index == nodeConfig.conditionNodes.length - 1">
            <div class="top-right-cover-line"></div>
            <div class="bottom-right-cover-line"></div>
          </template>
        </div>
      </div>
      <node-line />
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PropType, Ref } from 'vue'
import type { INodeConfig } from '@/hooks/use-node'
import { useNodeStatus, JudgeState } from './use-node-status'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeConfig>,
    default: () => ({})
  }
})
const nodeConfig = computed(() => {
  return props.nodeConfig
})
const getWhenState = (data: INodeConfig) => {
  const state = getNodeStatus.value?.find(item => {
    if (data.name == item.code) {
      return item
    }
  })
  return state
}
const { getNodeStatus } = useNodeStatus(props.nodeConfig) as unknown as { getNodeStatus: Ref<JudgeState[]> }
</script>
<style lang="less" scoped>
.setting-node-wrap {
  cursor: pointer;

  &:hover {
    border-color: #ffbe96;
  }
}
</style>
