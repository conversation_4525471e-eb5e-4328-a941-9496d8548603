<template>
  <div class="while-wrap">
    <div class="while-wrap-box" @click="setStateData">
      <div class="setting-node">
        <div class="setting-node-wrap is-edit" :class="{ actived: getNodeConfig.code == activedCode }">
          <span class="setting-node-icon" :class="`setting-node-icon-${getNodeStatus}`">
            <i class="iconfont icon-zhihangchaoshi-02"></i>
          </span>
          <span class="node-item-name">
            <a-tooltip placement="top" :overlayStyle="{ zIndex: 2500 }">
              <template #title>
                <span>{{ nodeConfig.showName ? nodeConfig.showName : nodeConfig.name }}</span>
              </template>
              <span class="setting-node-text">{{ nodeConfig.showName ? nodeConfig.showName : nodeConfig.name }}</span>
            </a-tooltip>
          </span>

          <div v-if="getNodeStatus" class="setting-node-icon-status" :class="`setting-node-icon-status-${getNodeStatus}`">
            <i v-if="getNodeStatus === 'Fail'" class="iconfont icon-chacha"></i>
            <i v-else-if="getNodeStatus === 'Finished'" class="iconfont icon-gou"></i>
            <i v-if="['Running', 'Cancel'].includes(getNodeStatus as string)" class="iconfont icon-shenglvehao"></i>
          </div>
        </div>
      </div>
      <node-line :class="{ 'has-child': getNodeConfig.cancelTaskList }" />
      <template v-if="getNodeConfig.cancelTaskList">
        <old-node-status :nodeConfig="getNodeConfig.cancelTaskList"></old-node-status>
      </template>

      <div class="setting-node">
        <div class="setting-node-wrap">
          <span class="setting-node-icon" :class="`setting-node-icon-${getNodeStatus}`">
            <i class="iconfont icon-zhihangchaoshi-02"></i>
          </span>
          <!-- 结束取消任务 -->
          <span class="setting-node-text">{{ t('message.endCancelRange') }}</span>
          <div v-if="getNodeStatus" class="setting-node-icon-status" :class="`setting-node-icon-status-${getNodeStatus}`">
            <i v-if="getNodeStatus === 'Fail'" class="iconfont icon-chacha"></i>
            <i v-else-if="getNodeStatus === 'Finished'" class="iconfont icon-gou"></i>
            <i v-if="['Running', 'Cancel'].includes(getNodeStatus as string)" class="iconfont icon-shenglvehao"></i>
          </div>
        </div>
      </div>
      <div class="while-across-top-line"></div>
      <div class="while-across-bottom-line"></div>
      <div class="while-down-line"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type { INodeConfig } from '@/hooks/use-node'
import { useNodeStatus } from './use-node-status'
import useStore from '@/stores'
import emitter from '@/utils/mitts'
import { useLocale } from 'youibot-plus'
import { INodeDetails } from '@/pages/operations/tasks/task-management/config'
const { t } = useLocale()
const { nodeStore } = useStore()
let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeConfig>,
    default: () => ({})
  }
})
const nodeDetails = ref<INodeDetails | null>(null)
const setStateData = (e: Event) => {
  e.stopPropagation()
  const { code, type } = props.nodeConfig

  if (!nodeDetails.value || !nodeDetails.value[code as string]) {
    nodeStore.setStatisticsData({
      endTime: null,
      startTime: null,
      executionDuration: null,
      totalDuration: null,
      runningCount: null,
      code: code,
      type: type
    })
    nodeStore.setVariableData({ paramOut: {}, paramIn: {} })
  } else {
    const { paramIn = {}, paramOut = {}, ...rest } = nodeDetails.value[code as string]
    nodeStore.setStatisticsData({ ...rest })
    nodeStore.setVariableData({ paramIn, paramOut })
  }
}
const activedCode = ref()
nodeStore.$subscribe((e, s) => {
  activedCode.value = s.statisticsData.code
})
const { getNodeConfig, getNodeStatus } = useNodeStatus(props.nodeConfig)
const getNodeDetail = (data: INodeDetails) => {
  nodeDetails.value = data
}
onMounted(() => {
  emitter.on('nodeDetail', getNodeDetail)
})
onBeforeUnmount(() => {
  emitter.off('nodeDetail', getNodeDetail)
})
</script>
<style lang="less" scoped>
@border-color: #bac5cd;
@add-height: 42px;
@node-width: 176px;
@setting-height: 44px;
@line-width: 40px;
@setting-width: 180px;

.arrow-while {
  position: absolute;
  top: -5px;
  left: 0;
  border-color: transparent @border-color transparent transparent;
  border-style: solid;
  border-width: 6px;
  transform: translateX(-50%);
  content: '';
}

.while-wrap {
  position: relative;
  // width: 100%;
  text-align: center;

  &-box {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 0 24px;

    .actived {
      border: 1px solid #ffbe96;
    }

    .while-node {
      position: relative;
      width: @node-width;
      min-height: 72px;
      padding: 14px 19px;
      background: #fff;
      border: 2px solid @border-color;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  // 横底部
  .while-across-bottom-line {
    position: absolute;
    right: 0;
    bottom: calc(@setting-height / 2);
    width: calc(50% - @setting-width / 2);
    height: 4px;
    // background-color: @border-color;
    border-bottom: 1px solid @border-color;
  }
  // 竖
  .while-down-line {
    position: absolute;
    right: 0;
    bottom: calc(@setting-height / 2);
    width: 4px;
    height: calc(100% - @setting-height);
    // background-color: @border-color;
    border-right: 1px solid @border-color;
  }
  // 顶部 横
  .while-across-top-line {
    position: absolute;
    top: 19px;
    right: 0;
    width: calc(50% - @setting-width / 2);
    height: 4px;
    // background-color: @border-color;
    border-bottom: 1px solid @border-color;

    &::before {
      .arrow-while();

      top: -3px;
    }
  }

  .while-end-node {
    position: relative;
    text-align: center;

    &-text {
      width: 80px;
      margin: auto;
      padding: 5px 12px;
      background-color: #d9d9dd;
      border-radius: 8px;
    }
  }
}

.setting-node-icon {
  background-color: #ddd;

  i {
    color: #666;
  }
}
</style>
