<template>
  <div class="node-item" @click="setStateData" :class="{ actived: nodeConfig.code == activedCode }">
    <div class="node-item-icon" :class="`node-item-icon-${getNodeStatus}`">
      <p>
        <span v-if="nodeConfig.icon" class="node-item-image">
          <a-image :src="nodeConfig.icon" :preview="false" :previewMask="false" :width="'14px'"></a-image>
        </span>
        <i class="iconfont icon-tingche" v-else></i>
        <span class="node-item-name">
          <a-tooltip placement="top" :overlayStyle="{ zIndex: 2500 }">
            <template #title>
              <span>{{ nodeConfig.showName ? nodeConfig.showName : nodeConfig.name }}</span>
            </template>
            <span class="normal-node-name">{{ nodeConfig.showName ? nodeConfig.showName : nodeConfig.name }}</span>
          </a-tooltip>
        </span>
      </p>
    </div>
    <div v-if="getNodeStatus" class="setting-node-icon-status" :class="`setting-node-icon-status-${getNodeStatus}`">
      <i v-if="getNodeStatus === 'Fail'" class="iconfont icon-chacha"></i>
      <i v-else-if="getNodeStatus === 'Finished'" class="iconfont icon-gou"></i>
      <i v-if="['Running', 'Cancel'].includes(getNodeStatus as string)" class="iconfont icon-shenglvehao"></i>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { INodeConfig } from '@/hooks/use-node'
import type { PropType } from 'vue'
import useStore from '@/stores'
import { useNodeStatus } from './use-node-status'
import emitter from '@/utils/mitts'
import { INodeDetails } from '@/pages/operations/tasks/task-management/config'
const { nodeStore } = useStore()
let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeConfig>,
    default: () => ({})
  }
})
const activedCode = ref()
let nodeConfig = computed(() => {
  return props.nodeConfig
})

const { getNodeStatus } = useNodeStatus(nodeConfig.value)
nodeStore.$subscribe((e, s) => {
  activedCode.value = s.statisticsData.code
})
const nodeDetails = ref<INodeDetails | null>(null)

const getNodeDetail = (data: INodeDetails) => {
  nodeDetails.value = data
}

const setStateData = (e: Event) => {
  e.stopPropagation()
  const { code, type } = props.nodeConfig

  if (!nodeDetails.value || !nodeDetails.value[code as string]) {
    nodeStore.setStatisticsData({
      endTime: null,
      startTime: null,
      executionDuration: null,
      totalDuration: null,
      runningCount: null,
      code: code,
      type: type,
      totalDistance: undefined,
      leftDistance: undefined
    })
    nodeStore.setVariableData({ paramOut: {}, paramIn: {} })
  } else {
    const { paramIn = {}, paramOut = {}, ...rest } = nodeDetails ? nodeDetails.value[code as string] : {}
    nodeStore.setStatisticsData({ ...rest })
    nodeStore.setVariableData({ paramIn, paramOut })
  }
}
emitter.on('nodeDetail', getNodeDetail)

onBeforeUnmount(() => {
  emitter.off('nodeDetail', getNodeDetail)
})
</script>
<style lang="less" scoped>
.node-item {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  width: 180px;
  height: 54px;
  line-height: 54px;
  background: #fff;
  border: 1px solid #f9f9f9;
  border-radius: 4px;
  box-shadow: 4px 4px 8px 0 #bdbdbd40;

  &-name {
    font-size: 14px;
    vertical-align: top;
  }

  &.actived {
    border: 1px solid #ffbe96;
  }

  &-icon {
    width: 100%;

    i {
      padding: 0 7px 0 13px;
      color: #c6c6c6;
      font-size: 18px;
    }

    > p {
      flex: 1;
      margin: 0;
      overflow: hidden;
      white-space: nowrap;
      text-align: left;
      text-overflow: ellipsis;
    }
  }

  &-image {
    padding: 0 7px 0 13px;
    color: #c6c6c6;
    font-size: 18px;
  }
}
</style>
