<template>
  <div class="branch-wrap" v-if="nodeConfig.conditionNodes">
    <div class="branch-box-wrap">
      <div class="branch-box">
        <div class="col-box" v-for="(item, index) in nodeConfig.conditionNodes" :key="index">
          <div class="condition-node">
            <div class="condition-node-box">
              <div class="setting-node" :class="{ actived: item.code == nodeStore.getFormData.code }">
                <div class="setting-node-wrap">
                  <span
                    class="setting-node-icon"
                    :class="`setting-node-icon-${item.code === getNodeStatus?.code ? getNodeStatus.state : ''}`">
                    <i class="iconfont icon-tiaojian"></i>
                  </span>
                  <span class="setting-node-text">
                    {{
                      index < nodeConfig.conditionNodes.length - 1
                        ? item.name + (index + 1)
                        : t('message.taskTypeArrangement.otherCondition')
                    }}
                  </span>
                  <template v-if="getNodeStatus instanceof Object && item.code === getNodeStatus?.code">
                    <div
                      v-if="getNodeStatus.state"
                      class="setting-node-icon-status"
                      :class="`setting-node-icon-status-${getNodeStatus.state}`">
                      <i v-if="getNodeStatus.state === 'Fail'" class="iconfont icon-chacha"></i>
                      <i v-else-if="getNodeStatus.state === 'Finished'" class="iconfont icon-gou"></i>
                      <i v-if="['Running', 'Cancel'].includes(getNodeStatus.state as string)" class="iconfont icon-shenglvehao"></i>
                    </div>
                  </template>
                </div>
              </div>
              <node-line :class="{ 'has-child': item.childNode }" />
            </div>
          </div>
          <old-node-status v-if="item.childNode" :nodeConfig="item.childNode" />
          <template v-if="index == 0">
            <div class="top-left-cover-line"></div>
            <div class="bottom-left-cover-line"></div>
          </template>
          <template v-if="index == nodeConfig.conditionNodes.length - 1">
            <div class="top-right-cover-line"></div>
            <div class="bottom-right-cover-line"></div>
          </template>
        </div>
      </div>
      <node-line />
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type { INodeConfig } from '@/hooks/use-node'
import useStore from '@/stores'
import { useNodeStatus, JudgeState } from './use-node-status'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const { nodeStore } = useStore()
let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeConfig>,
    default: () => ({})
  }
})

const nodeConfig = computed(() => {
  return props.nodeConfig
})
const { getNodeStatus } = useNodeStatus(props.nodeConfig) as unknown as { getNodeStatus: JudgeState }
</script>

<style lang="less" scoped>
.setting-node-wrap {
  cursor: pointer;

  &:hover {
    border-color: #ffbe96;
  }
}
</style>
