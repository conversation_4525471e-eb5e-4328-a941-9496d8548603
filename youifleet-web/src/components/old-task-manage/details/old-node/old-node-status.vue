<template>

    <old-node-judge v-if="['Judge'].includes(nodeConfig.type) && nodeConfig.conditionNodes" :nodeConfig="nodeConfig"></old-node-judge>
    <old-node-when v-else-if="['WHEN'].includes(nodeConfig.type) && nodeConfig.conditionNodes" :nodeConfig="nodeConfig"></old-node-when>
    <old-node-while v-else-if="nodeConfig.type == 'While'" :nodeConfig="nodeConfig"></old-node-while>
    <old-node-task-cancellation v-else-if="nodeConfig.type == 'CancelTask'" :nodeConfig="nodeConfig"></old-node-task-cancellation>
    <old-node-normal v-else :nodeConfig="nodeConfig"></old-node-normal>
    <node-line v-if="!['WHEN'].includes(nodeConfig.type)"></node-line>

    <old-node-status v-if="nodeConfig.childNode" :nodeConfig="nodeConfig.childNode"></old-node-status>

</template>

<script lang="ts" setup>
import oldNodeNormal from './old-node-normal.vue'
import { INodeConfig } from '@/hooks/use-node'
import { PropType } from 'vue'
let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeConfig>,
    default: () => ({})
  }
})

const nodeConfig = computed(() => {
  return props.nodeConfig
})
</script>
<style lang="less">
@import './node-status.less';

@bg-color: #fff;
@node-text-color: #7a7a7a;
@node-width: 180px;
@node-height: 44px;
@border-color: #bac5cd;
@icon-color: #09bc86;

.details {
  .old-task-manage {
    // 条件节点 并行节点
    .branch-wrap {
      display: inline-flex;
      // width: 100%;
      margin-top: 0;

      .branch-box-wrap {
        display: flex;
        flex-flow: column wrap;
        flex-shrink: 0;
        align-items: center;
        // width: 100%;

        .branch-box {
          position: relative;
          display: flex;
          height: auto;
          overflow: visible;
          border-top: 1px solid @border-color;
          border-bottom: 1px solid @border-color;
        }

        .col-box {
          position: relative;
          display: inline-flex;
          flex-direction: column;
          align-items: center;
          background-color: @bg-color;

          &::before {
            .node-line-before();
          }
        }

        .condition-node {
          display: inline-flex;
          flex-direction: column;

          &-box {
            position: relative;
            flex-grow: 1;
            align-items: center;
            justify-content: center;
            padding: 30px 50px 0;
            text-align: center;

            &::before {
              .node-line-before();
            }
          }
        }

        .top-left-cover-line,
        .top-right-cover-line {
          position: absolute;
          top: -4px;
          width: 50%;
          height: 8px;
          background-color: @bg-color;
        }

        .top-left-cover-line {
          left: -1px;
        }

        .top-right-cover-line {
          right: -1px;
        }

        .bottom-left-cover-line {
          left: -1px;
        }

        .bottom-left-cover-line,
        .bottom-right-cover-line {
          position: absolute;
          bottom: -4px;
          width: 50%;
          height: 8px;
          background-color: @bg-color;
        }

        .bottom-right-cover-line {
          right: -1px;
        }
      }
    }

    .setting-node {
      position: relative;

      &-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: @node-width;
        height: @node-height;
        overflow: hidden;
        background-color: #fff;
        border: 1px solid #e2e2e2;
        border-radius: 6px;
      }

      &-icon {
        width: @node-height;
        height: calc(@node-height - 2px);
        line-height: calc(@node-height - 2px);
        text-align: center;
        background-color: #fcfcfc;
        border-right: 1px solid #e2e2e2;

        i {
          color: #c6c6c6;
          font-size: 20px;
        }
      }

      &-text {
        flex: 1;
        padding: 12px;
        color: #000;
        font-size: 14px;
        text-align: left;
      }
    }
  }
}
</style>
