<template>
  <div>
    <div class="details-log-top">
      <p class="title">{{ t('message.taskManage.runningLog') }}</p>

      <!-- <div>
        <a-range-picker
          popupClassName="details-log-date-picker"
          v-model:value="createDate"
          style="width: 330px"
          :show-time="{ defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }"
          format="YYYY-MM-DD HH:mm:ss"
          valueFormat="YYYY-MM-DD HH:mm:ss"
          @change="dateChange" />
      </div>
      <div>
        <a-input
          style="width: 100px"
          v-model:value="content"
          :placeholder="t('message.taskManage.pleaseEnterADescriptionSearch')"
          @blur="getData"
          @pressEnter="getData"></a-input>
      </div> -->
    </div>
    <div class="details-log-list">
      <ul>
        <li v-for="(item, index) in logSysData" :key="index">
          <span class="details-log-list-li-date">
            {{ setTimeStr(item.createDate) }}
          </span>
          <span class="details-log-list-li-text">{{ item.content }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getLogSysPage } from '@/api/log'
import useStore from '@/stores'
import { ITableRow } from '@/pages/operations/tasks/task-management/config'
import { PropType, onMounted } from 'vue'
import { setTimeStr } from '@/utils/index'
import dayjs from 'dayjs'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const props = defineProps({
  data: {
    type: Object as PropType<ITableRow>,
    required: true
  }
})
const { nodeStore } = useStore()
const createDate = ref()
const State = computed(() => {
  return nodeStore.getStatisticsData
})
nodeStore.$subscribe(() => {
  getData()
})
const content = ref()
const logSysData = ref()
const getData = () => {
  getLogSysPage({
    pageNum: 1,
    pageSize: 50,
    createDate: createDate.value?.join(),
    taskNos: props.data.taskNo,
    nodeCode: State.value.code as string,
    content: content.value,
    orderField: 'createDate'
  }).then(res => {
    logSysData.value = res.data.list
  })
}
const dateChange = () => {
  getData()
}
onMounted(() => {
  getData()
})
</script>
<style lang="less">
.details-log-date-picker {
  z-index: 2007 !important;
}
</style>
<style lang="less" scoped>
.details-log {
  &-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-list {
    max-height: 250px;
    overflow-y: auto;

    ul {
      margin: 12px;

      li {
        padding: 8px 0;
        border-bottom: 1px solid #f1f1f1;

        .details-log-list-li {
          &-date {
            margin-right: 16px;
            color: #666;
          }

          &-text {
            color: #000;
          }
        }
      }
    }
  }
}

.title {
  margin: 0 !important;
}
</style>
