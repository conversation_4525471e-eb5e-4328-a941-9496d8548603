<template>
  <div class="variable">
    <!-- 变量 -->
    <p class="title">{{ t('message.variable') }}</p>
    <div class="variable-table" v-if="varState.paramIn">
      <p class="variable-table-title">
        <i class="iconfont icon-tingche"></i>
        <!-- 输入值 -->
        <span>{{ t('message.taskManage.inputValue') }}</span>
        <span class="number">{{ Object.keys(varState.paramIn).length || 0 }}</span>
      </p>
      <div class="variable-table-content">
        <div class="variable-item" v-for="(item, index) in varState.paramIn" :key="index">
          <p class="name">
            <i class="iconfont icon-tingche"></i>
            {{ index }}
          </p>
          <a-tooltip v-if="item || item === 0 || item === false" placement="topLeft" :overlayStyle="{ zIndex: 2100 }">
            <template #title>
              <span>{{ item }}</span>
            </template>
            <p class="notice">{{ item }}</p>
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="variable-table" v-if="paramOutLength && staticState.code">
      <p class="variable-table-title">
        <i class="iconfont icon-tingche"></i>
        <!-- 输出值 -->
        <span>{{ t('message.taskManage.outputValue') }}</span>
        <span class="number">{{ Object.keys(varState.paramOut).length || 0 }}</span>
      </p>
      <div class="variable-table-content">
        <div class="variable-item" v-for="(item, index) in varState.paramOut" :key="index">
          <p class="name">
            <i class="iconfont icon-tingche"></i>
            {{ index }}
          </p>
          <a-tooltip v-if="item || item === 0 || item === false" placement="topLeft" :overlayStyle="{ zIndex: 2100 }">
            <template #title>
              <span>{{ item }}</span>
            </template>
            <p class="notice">{{ item }}</p>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import useStore from '@/stores'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const { nodeStore } = useStore()
const varState = computed(() => {
  return nodeStore.getVariableData
})
const paramOutLength = computed(() => {
  if (varState.value.paramOut) {
    return Object.keys(varState.value.paramOut).length
  }
  return 0
})
const staticState = computed(() => {
  return nodeStore.getStatisticsData
})

//默认paramOut是数组无序，需转为map变成有序
// const sortParamOut = () => {
//   const data = varState.paramOut
//   const keys = Object.keys(data)
//   const sortedObject = keys.map(key => {
//     return {
//       key,
//       value: data[key]
//     }
//   })
//   console.log('sortParamOut -> imgList', sortedObject)
//   return Object.entries(sortedObject)
// }
// sortParamOut()
</script>
<style lang="less" scoped>
.variable {
  &-table {
    margin-bottom: 16px;
    padding-top: 4px;
    border-right: 1px solid #f1f1f1;
    border-left: 1px solid #f1f1f1;

    &-title {
      margin: 0;
      font-size: 14px;
      background-color: #fafafa;

      .iconfont {
        padding: 0 6px;
        font-size: 16px;
      }

      .number {
        margin-left: 12px;
        padding: 0 8px;
        color: #fff;
        font-size: 12px;
        background-color: #e1e1e1;
        border-radius: 5px;
      }
    }

    &-content {
      max-height: 96px;
      overflow: auto;

      .variable-item {
        display: grid;
        grid-template-columns: 50% 50%;
        height: 32px;
        line-height: 32px;
        border-bottom: 1px solid #f1f1f1;

        > p {
          margin-bottom: 0;
        }

        .name {
          padding-left: 32px;
          color: #a6a6a6;
          .ellipsis();

          .iconfont {
            font-size: 14px;
          }
        }

        .notice {
          .ellipsis();

          color: #000;
        }
      }
    }

    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
