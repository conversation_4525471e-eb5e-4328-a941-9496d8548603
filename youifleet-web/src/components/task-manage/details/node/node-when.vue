<template>
  <div class="branch-wrap">
    <div class="branch-box-wrap">
      <div class="branch-box">
        <div class="condition-node">
          <div class="condition-node-box">
            <div class="setting-node">
              <div class="setting-node-wrap">
                <span class="setting-node-icon" :class="`setting-node-icon-${getNodeStatus?.length ? 'Finished' : ''}`">
                  <i class="iconfont icon-a-binghangfenzhi"></i>
                </span>
                <span class="setting-node-text">{{ t('message.taskTypeArrangement.parallelBranch') }}</span>
                <div v-if="getNodeStatus?.length" class="setting-node-icon-status" :class="`setting-node-icon-status-Finished`">
                  <i class="iconfont icon-gou"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PropType, Ref } from 'vue'
import type { INodeConfig } from '@/hooks/use-node'
import { useNodeStatus, JudgeState } from './use-node-status'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeConfig>,
    default: () => ({})
  },
  currentNode: {
    type: Object,
    default: () => ({})
  }
})
const nodeConfig = computed(() => {
  return props.nodeConfig
})
const getWhenState = (data: INodeConfig) => {
  const state = getNodeStatus.value?.find(item => {
    if (data.name == item.code) {
      return item
    }
  })
  return state
}
const { getNodeStatus } = useNodeStatus(props.nodeConfig) as unknown as { getNodeStatus: Ref<JudgeState[]> }
</script>
<style lang="less" scoped>
.setting-node-wrap {
  cursor: pointer;

  &:hover {
    border-color: #ffbe96;
  }
}
</style>
