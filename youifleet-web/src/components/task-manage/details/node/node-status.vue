<template>
  <div>
    <node-judge v-if="['Judge'].includes(data?.type)" :nodeConfig="data"></node-judge>
    <node-when v-else-if="['WHEN'].includes(data?.type)" :nodeConfig="data" :currentNode="currentNode"></node-when>
    <node-while v-else-if="data?.type == 'While'" :nodeConfig="data"></node-while>
    <node-task-cancellation v-else-if="data?.type == 'CancelTask'" :nodeConfig="data"></node-task-cancellation>
    <node-normal v-else-if="data?.type" :nodeConfig="data"></node-normal>
    <div v-if="isClone">
      <div class="line">
        <svg viewBox="0 0 180 48" xmlns="http://www.w3.org/2000/svg">
          <line x1="90" y1="0" x2="90" y2="48" stroke="#a6b4bf" stroke-width="1.2" />
          <!-- 如果不指定描边颜色，则无法看见线条 -->
        </svg>
      </div>
    </div>
  </div>
  <!-- <node-status v-if="nodeConfig.childNode" :nodeConfig="nodeConfig.childNode"></node-status> -->
</template>

<script lang="ts" setup>
// @ts-nocheck

import emitter from '@/utils/mitts'
import useStore from '@/stores'
const { nodeStore } = useStore()
const getNodeData = inject('getNode')
const data = ref()
const store = ref()
const isClone = ref()
const isAdd = ref()
const isAddIcon = ref(false)
const selectedNode = ref()
const currentNode = ref()
const changeData = ({ cell, current }) => {
  data.value = current

  isClone.value = cell?.store?.data?.isClone
  isAdd.value = cell?.store?.data?.isAdd
}
const chnageAttrs = ({ cell }) => {
  isClone.value = cell?.store?.data?.isClone
  isAdd.value = cell?.store?.data?.isAdd
  // console.log('markup 节点数据是否发生变化了 111111111111111>>>', cell)
}
const clearIcon = b => {
  isClone.value = getNodeData()?.store?.data?.isClone
  isAdd.value = getNodeData()?.store?.data?.isAdd
  isAddIcon.value = b
}

const getSelectedNodeCode = code => {
  selectedNode.value = code
}
onMounted(() => {
  currentNode.value = getNodeData()
  data.value = currentNode.value.getData()

  isClone.value = currentNode.value?.store?.data?.isClone
  isAdd.value = currentNode.value?.store?.data?.isAdd
  currentNode.value.on('change:data', changeData)
  currentNode.value.on('change:attrs', chnageAttrs)
  emitter.on('clear:icon', clearIcon)
  emitter.on('node:selectedCode', getSelectedNodeCode)
  onBeforeUnmount(() => {
    currentNode.value.off('change:data', changeData)
    currentNode.value.off('change:attrs', chnageAttrs)
    emitter.off('clear:icon', clearIcon)
    emitter.off('node:selectedCode', getSelectedNodeCode)
  })
})
</script>
<style lang="less">
@import './node-status.less';

@bg-color: #fff;
@node-text-color: #7a7a7a;
@node-width: 180px;
@node-height: 48px;
@border-color: #bac5cd;
@icon-color: #09bc86;

.details {
  .new-task-manage {
    // 条件节点 并行节点
    .branch-wrap {
      display: inline-flex;
      // width: 100%;
      margin-top: 0;

      .branch-box-wrap {
        display: flex;
        flex-flow: column wrap;
        flex-shrink: 0;
        align-items: center;
        // width: 100%;

        .branch-box {
          position: relative;
          display: flex;
          height: auto;
          overflow: visible;
          // border-top: 1px solid @border-color;
          // border-bottom: 1px solid @border-color;
        }

        .col-box {
          position: relative;
          display: inline-flex;
          flex-direction: column;
          align-items: center;
          background-color: @bg-color;

          &::before {
            .node-line-before();
          }
        }

        .condition-node {
          display: inline-flex;
          flex-direction: column;

          &-box {
            position: relative;
            flex-grow: 1;
            align-items: center;
            justify-content: center;
            // padding: 30px 50px 0;
            text-align: center;

            // &::before {
            //   .node-line-before();
            // }
          }
        }
      }
    }

    .setting-node {
      position: relative;

      &-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: @node-width;
        height: @node-height;
        overflow: hidden;
        background-color: #fff;
        border: 1px solid #e2e2e2;
        border-radius: 6px;
      }

      &-icon {
        width: @node-height;
        height: calc(@node-height - 2px);
        line-height: calc(@node-height - 2px);
        text-align: center;
        background-color: #fcfcfc;
        border-right: 1px solid #e2e2e2;

        i {
          color: #c6c6c6;
          font-size: 20px;
        }
      }

      &-text {
        flex: 1;
        padding: 12px;
        color: #000;
        font-size: 14px;
        text-align: left;
      }
    }
  }
}
</style>
