import { ComputedRef, Ref } from 'vue'
import type { INodeConfig } from '@/hooks/use-node'
import useStore from '@/stores'
import { INodeDetails } from '@/pages/operations/tasks/task-management/config'
import emitter from '@/utils/mitts'
export interface JudgeState {
  code: string
  state: string
}

interface useNodeStatusHooks {
  getNodeConfig: ComputedRef<INodeConfig>
  getNodeStatus: Ref<string | null | JudgeState | JudgeState[]>
}

/**
 * 获取节点状态
 *
  @param {INodeConfig} nodeConfig 节点
  @returns {useNodeStatusHooks} 节点状态
 */
export function useNodeStatus(nodeConfig: INodeConfig): useNodeStatusHooks {
  const getNodeConfig = computed(() => {
    return nodeConfig
  })

  function getConditionNodeStatus(childNode: INodeConfig | null, nodeDetails: INodeDetails): string {
    if (childNode) {
      const { code, type, conditionNodes, whileList } = childNode
      const status = nodeDetails[code as string]?.status
      switch (status) {
        case undefined:
          return ''
        case 'Finished':
          // 并行 与 条件
          if (type === 'WHEN' || type == 'Judge') {
            conditionNodes?.forEach(item => {
              getConditionNodeStatus(item, nodeDetails)
            })
          }
          // 循环
          if (type == 'While' && whileList) {
            return getConditionNodeStatus(whileList, nodeDetails)
          }
          if (childNode.childNode) {
            return getConditionNodeStatus(childNode.childNode, nodeDetails)
          }

          return status
        case 'Running':
        case 'Cancel':
          return status
        case 'Fail':
          return status
      }
    }
    return ''
  }
  const nodeDetails = ref<INodeDetails | null>()
  const getNodeStatus = ref<string | null | JudgeState | JudgeState[]>(null)
  const getNodeDetail = (data: INodeDetails) => {
    nodeDetails.value = data
    const { code, type, conditionNodes } = getNodeConfig.value
    if (type === 'Judge') {
      let state = ''
      let itemCode = ''
      conditionNodes?.some(item => {
        if (nodeDetails.value && nodeDetails.value[item as unknown as string]) {
          itemCode = item as unknown as string
          state = 'Finished'
          return
        }
      })
      getNodeStatus.value = { code: itemCode, state: state }
      return
    } else if (type === 'WHEN') {
      let arr: { state: string; code: string }[] = []
      let state = ''
      conditionNodes?.forEach(item => {
        if (nodeDetails.value && nodeDetails.value[item as unknown as string]) {
          arr.push({ state: 'Finished', code: item as unknown as string })
        }
      })
      getNodeStatus.value = arr
      return
    }
    getNodeStatus.value = nodeDetails.value ? nodeDetails.value[code as string]?.status : null
  }
  emitter.on('nodeDetail', getNodeDetail)
  // const getNodeStatus = computed(() => {
  //   const nodeDetails = nodeStore.getNodeDetails
  //   const { code, type, conditionNodes } = getNodeConfig.value
  //   if (type === 'Judge') {
  //     let state = ''
  //     let itemCode = ''
  //     conditionNodes?.some(item => {
  //       if (nodeDetails[item as unknown as string]) {
  //         itemCode = item as unknown as string
  //         state = 'Finished'
  //         return
  //       }
  //     })
  //     return { code: itemCode, state: state }
  //   } else if (type === 'WHEN') {
  //     let arr: { state: string; code: string }[] = []
  //     let state = ''
  //     conditionNodes?.forEach(item => {
  //       if (nodeDetails[item as unknown as string]) {
  //         arr.push({ state: 'Finished', code: item as unknown as string })
  //       }
  //     })
  //     return arr
  //   }
  //   return nodeDetails ? nodeDetails[code as string]?.status : null
  // })
  onBeforeUnmount(() => {
    emitter.off('nodeDetail', getNodeDetail)
  })
  return {
    getNodeConfig,
    getNodeStatus
  }
}
