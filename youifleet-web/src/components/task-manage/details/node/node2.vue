<template>
  <div class="node-wrap new-task-manage" id="container" @click="clickBg">
    <div id="graph-container11"></div>
    <TeleportContainer />
  </div>
  <div class="zoom">
    <a-button class="zoom-out" :class="nowVal == 10 && 'disabled'" @click="zoomSize(2)"><i class="iconfont icon-zengjia"></i></a-button>
    <div>{{ nowVal.toFixed(0) }}%</div>
    <a-button class="zoom-in" :class="nowVal == 300 && 'disabled'" @click="zoomSize(1)"><i class="iconfont icon-jianhao"></i></a-button>
  </div>
</template>

<script lang="ts" setup>
// @ts-nocheck
import emitter from '@/utils/mitts'
import type { INodeConfig } from '@/hooks/use-node'
import type { PropType } from 'vue'
import type { INodeDetailItem } from '@/pages/operations/tasks/task-management/config'
import useStore from '@/stores'
import { Graph } from '@antv/x6'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { register, getTeleport } from '@antv/x6-vue-shape'
import { Selection } from '@antv/x6-plugin-selection'
import { isArray, useLocale } from 'youibot-plus'
import {
  startNode,
  endNode,
  getEdgeConfig,
  imageConfig,
  rectConfig,
  rectHeight,
  rectWidth
} from '../../../../pages/operations/tasks/task-type-arrangementa/config'
import NodeStyle from './node-status.vue'
import useWhenHooks from '@/node-hooks/use-node-when'
import useBasicHooks from '@/node-hooks/use-node-basic'
import useLayoutHooks from '@/node-hooks/use-layout'
import useDragHooks from '@/node-hooks/use-node-drag'
import useDeleteHooks from '@/node-hooks/use-node-delete'
import useDataTreating from '@/node-hooks/use-node-data-treating'
const { t } = useLocale()
window.edgeArrMap = new Map() //记录边数据,避免自动计算的时候边挪动.当有并行分支的时候,就收集,只收集第一行的数据就可以,按记录的顺序展示数据.
window.whileEdgeArrMap = new Map() //记录循环节点的回环
window.sourceEdgeArrMap = new Map() //key为边的起点，value为边的终点
window.targetEdgeArrMap = new Map() //key为边的终点，value为边的起点
const { nodeStore } = useStore()
let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeConfig>,
    default: () => ({})
  },
  data: {
    type: Object as PropType<INodeDetailItem>,
    default: () => ({})
  },
  nowVal: {
    type: Number,
    default: 0
  }
})
let graph
let nowVal = ref(100)
const nodeConfig = computed(() => {
  return props.nodeConfig
})
register({
  shape: 'custom-rect',
  width: rectWidth,
  height: rectHeight,
  component: NodeStyle
})
const TeleportContainer = getTeleport()
const clickBg = (e: Event) => {
  e.stopPropagation()
  const { paramIn, paramOut, ...rest } = props.data
  nodeStore.setStatisticsData({ ...rest, code: null })
  nodeStore.setVariableData({ paramIn, paramOut })
}
const render = (data, fn, setWhileEdgeArrMap, setEdgeArrMap, setWhenEdgeArrMap) => {
  if (graph) {
    if (data) {
      data?.cells?.map(element => {
        if (
          element.whenIndex !== undefined &&
          element.whenChildIndex !== undefined &&
          (element.whenIndex !== 0 || element.whenChildIndex !== 0) &&
          element.commonId
        ) {
          const cellFind = data.cells.find(item => {
            return element.commonId === 'when' + item.id
          })
          if (cellFind) {
            element['whenList'] = cellFind.whenList
          }
          //
        }
        return element
      })
      graph.fromJSON(data)

      data.cells?.forEach(item => {
        if (item.shape === 'custom-rect') {
          const { type = null, whenIndex, code = null, id, isPassAddFn, whileCommonId = null, isClone } = item
          if ((type === 'Judge' || type === 'WHEN') && !isClone) {
            let codes = []
            item?.whenList[whenIndex]?.forEach((whenItem, index) => {
              if (index != 0) {
                if (!isArray(whenItem)) {
                  const node = graph.getCellById(whenItem.nodeId)
                  if (node) {
                    codes.push(node.getData()?.code)
                  }
                } else {
                  whenItem?.forEach(whenItem2 => {
                    whenItem2.forEach(whenItem3 => {
                      if (index != 0) {
                        const node = graph.getCellById(whenItem3.nodeId)
                        if (node) {
                          codes.push(node.getData()?.code)
                        }
                      }
                    })
                  })
                }
              }
            })

            const node = graph.getCellById(id)
            node.setData({
              conditionNodes: codes
            })
          } else if ((type === 'While' || type === 'CancelTask') && isPassAddFn) {
            const node = graph.getCellById(whileCommonId)
            if (node) {
              const itemNode = graph.getCellById(id)
              itemNode.setData({
                code: node.getData()?.code
              })
            }
          }
        }
      })

      setTimeout(() => {
        const graphData = graph
          .getNodes()
          .map(item => {
            const { whenList = [] } = item.toJSON()
            if (whenList.length) {
              setWhenEdgeArrMap(whenList)
            }
            return item
          })
          .concat(
            graph.getEdges().map(item => {
              const { isWhile = false } = item.toJSON()
              if (isWhile) {
                setWhileEdgeArrMap(graph.getCellById(item.getSource().cell), graph.getCellById(item.getTarget().cell))
              }
              setEdgeArrMap(item)
              return item
            })
          )
        fn()
        graph.positionContent('top', { useCellGeometry: true })
        const pos = graph.translate()
        graph.translate(pos.tx, pos.ty + 45)
      }, 0)
    }
  }
}
provide('nodeConfig', nodeConfig)
onMounted(() => {
  // #region 初始化画布
  graph = new Graph({
    container: document.getElementById('graph-container11'),
    grid: false,
    panning: {
      enabled: true,
      eventTypes: ['rightMouseDown', 'mouseWheel', 'mouseWheelDown']
    },
    background: { color: '#fff' },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.1,
      maxScale: 3,
      guard: e => {
        if (e.ctrlKey) {
          setTimeout(() => {
            nowVal.value = graph.zoom() * 100
          }, 50)
        }
        return true
      }
    },
    connecting: {
      router: 'orth',
      connectionPoint: 'bbox',
      fill: 'none'
    },
    interacting: function () {
      return {
        nodeMovable: false,
        edgeMovable: false
      }
    }
  })

  const { createNode, isNearTop, getLastNode, getNextNode, setLabel, setEdgeArrMap, clearLabel, setWhenEdgeArrMap, setWhileEdgeArrMap } =
    useBasicHooks(graph)
  const { layout } = useLayoutHooks(graph, t)
  const { drag } = useDragHooks(graph)
  const { remove } = useDeleteHooks(graph)
  const { connectWhenNode, connectWhileNode, connectNormalNode } = useWhenHooks(graph)

  // #region 使用插件
  graph
    .use(new Snapline())
    .use(new Keyboard())
    .use(new Clipboard())
    .use(new History())
    .use(
      new Selection({
        enabled: true,
        multiple: false,
        rubberband: false,
        movable: false
      })
    )

  // delete

  const blankClick = () => {
    clearLabel()
  }

  graph.on('blank:click', ({ e, x, y, edge, view }) => {
    blankClick()
    emitter.emit('node:selectedCode', '')
  })

  graph.on('edge:click', ({ e, x, y, edge, view }) => {
    console.log('🚀 ~ graph.on ~ edge:', edge)
  })

  graph.on('node:click', ({ node }) => {
    console.log(node)
  })
  graph.on('node:mousedown', ({ e, x, y, node }) => {
    // graph.disablePanning() // 禁止画布平移
  })
  // 画布上的节点
  graph.on('node:mouseenter', ({ node }) => {})

  graph.on('node:mousemove', ({ e, node, view }) => {})
  graph.on('node:mouseleave', ({ e, x, y, node }) => {
    graph.enablePanning() // 恢复画布平移
  })

  // 回显
  render(nodeConfig.value.childNode, layout, setWhileEdgeArrMap, setEdgeArrMap, setWhenEdgeArrMap)
  watchEffect(() => {
    if (props.nodeConfig) {
      render(props.nodeConfig.childNode, layout, setWhileEdgeArrMap, setEdgeArrMap, setWhenEdgeArrMap)
    }
  })
})
const zoomSize = (type: number) => {
  setTimeout(() => {
    if (type == 1) {
      if (nowVal.value == 10) {
        // messageTip('最小缩放至10%', 'error')
        return
      }
      nowVal.value -= 10
      console.log(nowVal.value / 100)
      graph.zoom(nowVal.value / 100, { absolute: true })
    } else {
      if (nowVal.value == 300) {
        // messageTip('最大缩放至200%', 'error')
        return
      }
      nowVal.value += 10
      console.log(nowVal.value / 100)
      graph.zoom(nowVal.value / 100, { absolute: true })
    }
  }, 100)
}
</script>

<style lang="less" scoped>
@border-color: #a6b4bf;
@node-text-color: #7a7a7a;
@bg-color: #f5f5f7;
@form-width: 378px;

#graph-container11 {
  /* width: calc(100% - 180px - 400px); */

  width: 100%;
  height: calc(100% - 2px);
}

.node-wrap {
  flex: 1;

  .event-node {
    width: 180px;
    height: 44px;
    font-size: 14px;
    line-height: 44px;
    text-align: center;
    background: #fff;
    border-radius: 4px;
    box-shadow: 4px 4px 8px 0 #bdbdbd40;
  }
}

.zoom {
  position: absolute;
  top: 25px;
  right: 35px;
  display: flex;
  flex-direction: inherit !important;
  align-items: center;
  justify-content: center;
  height: 32px !important;
  text-align: center;

  > div {
    width: 80px;
    height: 32px;
    margin: 0 4px;
    padding: 4px 12px;
    color: #000;
    font-size: 14px;
    background-color: #f5f5f5;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
  }

  .ant-btn {
    width: 32px;
    height: 32px;
    padding: 0 4px;
    color: #0009;
    background: #ebebeb;
    border: 1px solid #ebebeb;

    &:focus,
    &:hover {
      color: #000;
      background: #d9d9d9;
      border-color: #d9d9d9;
    }
  }
}
</style>
