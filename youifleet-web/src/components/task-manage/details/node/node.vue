<template>
  <div>
    <div class="node-wrap" @click="clickBg">
      <div class="node-wrap-box">
        <div class="event-node" v-if="nodeConfig.templateType == 'Event'">{{ nodeName }}</div>
        <div class="start-node" v-else>{{ t('message.taskTypeArrangement.start') }}</div>
      </div>
      <nodeLine></nodeLine>
      <nodeStatus :nodeConfig="nodeConfig.childNode" v-if="nodeConfig.childNode"></nodeStatus>
      <div class="end-node">
        <div class="end-node-text">{{ t('message.taskTypeArrangement.end') }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { INodeConfig } from '@/hooks/use-node'
import type { PropType } from 'vue'
import type { INodeDetailItem } from '@/pages/operations/tasks/task-management/config'
import useStore from '@/stores'
import useNodeForm from '@/hooks/use-node-form'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const { eventTypeList } = useNodeForm()
const { nodeStore } = useStore()
let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeConfig>,
    default: () => ({})
  },
  data: {
    type: Object as PropType<INodeDetailItem>,
    default: () => ({})
  },
  nowVal: {
    type: Number,
    default: 0
  }
})

const nodeConfig = computed(() => {
  return props.nodeConfig
})

const nodeName = computed(() => {
  const { event } = nodeConfig.value
  if (event) {
    return eventTypeList.find(item => item.value === event.type)?.label
  }
})

const clickBg = (e: Event) => {
  e.stopPropagation()
  const { paramIn, paramOut, ...rest } = props.data
  nodeStore.setStatisticsData({ ...rest, code: null })
  nodeStore.setVariableData({ paramIn, paramOut })
}
</script>

<style lang="less" scoped>
@border-color: #a6b4bf;
@node-text-color: #7a7a7a;

.node-wrap {
  flex: 1;

  .end-node {
    position: relative;
    padding: 8px 0;
    text-align: center;

    &::before {
      position: absolute;
      top: 0;
      left: calc(50% - 4.5px);
      width: 9px;
      height: 9px;
      font-size: 0;
      background-color: @border-color;
      border-radius: 50%;
      content: '';
    }
  }

  // 开始节点
  .start-node {
    position: relative;
    margin: 0 auto;
    padding: 8px 0;
    color: @node-text-color;

    &::after {
      position: absolute;
      bottom: 0;
      left: calc(50% - 4.5px);
      width: 9px;
      height: 9px;
      font-size: 0;
      background-color: @border-color;
      border-radius: 50%;
      content: '';
    }
  }

  .event-node {
    width: 180px;
    height: 44px;
    font-size: 14px;
    line-height: 44px;
    text-align: center;
    background: #fff;
    border-radius: 4px;
    box-shadow: 4px 4px 8px 0 #bdbdbd40;
  }
}
</style>
