//Running 和 Cancel相同样式
@Finished-bg-color: #ecfaf6;
@Finished-icon-color: #09bc86;
@Finished-status-color: #0a9b55;
@Running-bg-color: #007bed;
@Running-icon-color: #007bed;
@Running-status-color: #007bed;
@Fail-bg-color: #ffb737;
@Fail-icon-color: #ffa70b;
@Fail-status-color: #ffa70b;

.setting-node-wrap,
.node-item {
  cursor: pointer;

  &:hover {
    border-color: #ffbe96;
  }

  .setting-node-icon-status {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    background-color: #ce1313;
    border-radius: 50%;
    cursor: pointer;

    i {
      color: #fff;
      font-size: 14px;
    }
  }
}

.setting-node-icon-Finished {
  background-color: @Finished-bg-color !important;

  i {
    color: @Finished-icon-color !important;
  }
}

.setting-node-icon-status-Finished {
  background-color: @Finished-status-color !important;
}

.setting-node-icon-Running,
.setting-node-icon-Cancel {
  background-color: #d4eaff !important;

  i {
    color: @Running-icon-color !important;
  }
}

.setting-node-icon-status-Running,
.setting-node-icon-status-Cancel {
  background-color: @Running-status-color !important;
}

.setting-node-icon-Fail {
  background-color: @Fail-bg-color !important;

  i {
    color: @Fail-icon-color !important;
  }
}

.setting-node-icon-status-Fail {
  background-color: @Fail-status-color !important;
}

.node-item-icon-Finished {
  i {
    color: @Finished-icon-color !important;
  }
}

.node-item-icon-Running,
.node-item-icon-Cancel {
  i {
    color: @Running-icon-color !important;
  }
}

.node-item-icon-Fail {
  i {
    color: @Fail-icon-color !important;
  }
}
