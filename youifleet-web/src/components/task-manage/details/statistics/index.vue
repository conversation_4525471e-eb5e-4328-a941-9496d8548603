<template>
  <div class="statistics" :class="{ 'not-node': !State.code }">
    <p class="title">{{ t('message.taskManage.statistics') }}</p>

    <!-- 节点时展示 -->

    <a-row>
      <a-col :span="8">
        <p class="param">{{ t('message.taskManage.startTime') }}</p>

        <p class="value">
          {{ State.startTime ? setTimeStr(State.startTime, '\\') : '--' }}
        </p>
      </a-col>
      <a-col :span="8">
        <p class="param">{{ t('message.taskManage.endTime') }}</p>

        <p class="value">{{ State.endTime ? setTimeStr(State.endTime, '\\') : '--' }}</p>
      </a-col>
      <a-col v-if="!State.code" :span="8">
        <p class="param">{{ t('message.taskManage.executionTime') }}</p>

        <p class="value">{{ State.executionDuration ? setTimeSecond(State.executionDuration, t) : '--' }}</p>
      </a-col>
    </a-row>
    <a-row v-if="State.code">
      <a-col :span="8" v-if="State.type !== 'While'">
        <p class="param">{{ t('message.taskManage.runningTime') }}</p>

        <p class="value">{{ State.executionDuration ? setTimeSecond(State.executionDuration, t) : 0 }}</p>
      </a-col>
      <a-col :span="8" v-if="State.type === 'While'">
        <p class="param">{{ t('message.taskManage.runTimes') }}</p>

        <p class="value">{{ State.runningCount ? State.runningCount : 0 }}</p>
      </a-col>
      <a-col v-if="State.type === 'VehicleMove'" :span="8">
        <p class="param">{{ t('message.taskManage.remainingDistance') }}</p>

        <p class="value">{{ State.leftDistance ? State.leftDistance : 0 }}{{ t('message.meter') }}</p>
      </a-col>
      <a-col v-if="State.type === 'VehicleMove'" :span="8">
        <p class="param">{{ t('message.taskManage.totalDistance') }}</p>

        <p class="value">{{ State.totalDistance ? State.totalDistance : 0 }}{{ t('message.meter') }}</p>
      </a-col>
    </a-row>
    <div v-if="!State.code" class="details-interval"></div>
    <!-- 任务时展示 -->
    <a-table v-if="!State.code" :columns="columns" :data-source="nodeDetailList" :pagination="false">
      <!-- <template #publishTime="{ scope }">{{ setTimeStr(scope.record.publishTime) }}</template>
    <template #createDate="{ scope }">{{ setTimeStr(scope.record.createDate) }}</template> -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'startTime'">
          {{ setTimeStr(record.startTime) }}
        </template>
        <template v-if="column.key === 'endTime'">
          {{ setTimeStr(record.endTime)?.split(' ')[1] }}
        </template>
        <template v-if="column.key === 'executionDuration'">
          {{ setTimeSecond(record.executionDuration, t) || 0 + t('message.second') }}
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { setTimeSecond, setTimeStr } from '@/utils/index'
import useStore from '@/stores'
import { useLocale } from 'youibot-plus'
import emitter from '@/utils/mitts'
import { INodeDetails } from '@/pages/operations/tasks/task-management/config'
const { t } = useLocale()

const { nodeStore } = useStore()
interface ISeriesData {
  label: string
  value: number
}
const nodeDetail = ref<INodeDetails | null>(null)
const State = computed(() => {
  return nodeStore.getStatisticsData
})
const nodeDetailList = ref<any[]>([])
watch(
  () => [State.value],
  () => {
    if (!State.value.code) {
      nodeDetailList.value = []
      let data: any[] = []
      for (let i in nodeDetail.value) {
        const { executionDuration, name, code } = nodeDetail.value[i]
        nodeDetailList.value.push(nodeDetail.value[i])
        data.push({ label: name + '   ' + code, value: executionDuration ? executionDuration : 0 })
      }
      nodeDetailList.value = nodeDetailList.value.sort((a, b) => {
        return a.startTime < b.startTime ? 1 : -1
      })
    }
  }
)
const getNodeDetail = (data: INodeDetails) => {
  nodeDetail.value = data
}
onMounted(() => {
  emitter.on('nodeDetail', getNodeDetail)
})
onBeforeUnmount(() => {
  emitter.off('nodeDetail', getNodeDetail)
})
const columns = ref([
  {
    title: t('message.name'),
    dataIndex: 'name'
  },
  {
    title: t('message.serialNumber'),
    dataIndex: 'code'
  },
  {
    title: t('message.startTime'),
    dataIndex: 'startTime',
    key: 'startTime'
  },
  {
    title: t('message.endTime'),
    key: 'endTime',
    dataIndex: 'endTime'
  },
  {
    title: t('message.taskManage.executionTime'),
    dataIndex: 'executionDuration',
    key: 'executionDuration'
  }
])
</script>

<style lang="less" scoped>
/* stylelint-disable-next-line selector-pseudo-class-no-unknown */
:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td),
:deep(.ant-table tfoot > tr > th),
:deep(.ant-table tfoot > tr > td) {
  padding: 4px;
  border: none;
}
/* stylelint-disable-next-line selector-pseudo-class-no-unknown */
:deep(.ant-table-thead > tr > th) {
  color: #a9a8a8;
  background-color: #fff;
}

/* stylelint-disable-next-line selector-pseudo-class-no-unknown */
:deep(.ant-table-tbody > tr > td) {
  color: #000;
}

.statistics {
  &.not-node {
    flex: 1;
    height: 0;
  }

  #action {
    height: calc(100% - 140px);
    overflow: hidden;
  }
}

.ant-row {
  .ant-col {
    > p {
      margin: 0;
    }

    .value {
      font-size: 16px;
    }

    .param {
      color: #a9a8a8;
      font-size: 14px;
    }
  }
}

.ant-col {
  padding: 5px 0;
}

.details-interval {
  width: 100%;
  height: 15px;
  margin: 16px 0;
  background-color: #f5f5f5;
}
</style>
