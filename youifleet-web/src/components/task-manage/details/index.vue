<template>
  <my-modal :visible="props.visible" :modalFooter="false" :width="'1688px'" :zIndex="props.zIndex" class="details" @cancel="cancel">
    <template #title>{{ State.title }} {{ props.data.nodeParam }} （ {{ props.data.taskNo }} ）</template>
    <div class="details-node" ref="node" :style="{ overflow: State.nodeConfig.version ? 'hidden' : 'auto' }">
      <div v-if="State.nodeConfig && State.nodeConfig.version && props.visible" class="details-scale">
        <nodeStatus :node-config="State.nodeConfig" :data="State.baseData"></nodeStatus>
      </div>
      <div
        v-else-if="State.nodeConfig && props.visible"
        :node-config="State.nodeConfig"
        class="details-scale"
        :style="`transform: scale(${nowVal / 100});  transform-origin: top;`">
        <oldNodeStatus v-if="State.nodeConfig && props.visible" :node-config="State.nodeConfig" :data="State.baseData"></oldNodeStatus>
      </div>
    </div>
    <div class="details-interval"></div>
    <div class="details-data">
      <div class="details-data-title">
        <div>
          {{ !staticState.code ? t('message.taskManage.taskInformation') : t('message.taskManage.nodeInformation') + nodeCode }}
        </div>

        <div v-if="staticState.status === 'Fail'">
          <my-button
            v-if="staticState.isAllowSkip && !staticState.isSkip"
            :btnItem="{
              name: t('message.taskManage.skip'),
              onClick: () => {
                postTaskSkipFn()
              }
            }"></my-button>
          <my-button
            v-if="staticState.isAllowRetry && !staticState.isSkip"
            type="primary"
            :btnItem="{
              name: t('message.taskManage.retry'),
              onClick: () => {
                postTaskRetryFn()
              }
            }"></my-button>
        </div>
      </div>
      <div class="details-data-content">
        <variableModule v-if="State.baseData.paramIn || State.baseData.paramOut" />
        <div class="details-data-interval"></div>
        <statisticsModule :visible="props.visible" />

        <div v-if="staticState.code" class="details-data-interval"></div>
        <log v-if="staticState.code" :data="props.data"></log>
        <div v-if="State.baseData.noticeRecord" class="warning">
          <i class="iconfont icon-shanchuguanbicha"></i>
          <span class="warning-code">{{ State.baseData.noticeRecord.code }}</span>
          <span class="warning-tips">
            <a-tooltip placement="topLeft" :title="State.baseData.noticeRecord.description" :overlayStyle="{ zIndex: 2100 }">
              <span>{{ State.baseData.noticeRecord.description }}</span>
            </a-tooltip>
          </span>
        </div>
      </div>
    </div>
    <div v-if="!State.nodeConfig.version" class="zoom">
      <a-button class="zoom-out" :class="nowVal == 50 && 'disabled'" @click="zoomSize(2)"><i class="iconfont icon-zengjia"></i></a-button>
      <div>{{ nowVal }}%</div>
      <a-button class="zoom-in" :class="nowVal == 300 && 'disabled'" @click="zoomSize(1)"><i class="iconfont icon-jianhao"></i></a-button>
    </div>
  </my-modal>
</template>

<script lang="ts" setup>
import { ITableRow, INodeDetailItem } from '@/pages/operations/tasks/task-management/config'
import { INodeConfig } from '@/hooks/use-node'
import { PropType } from 'vue'
import nodeStatus from './node/node2.vue'
import oldNodeStatus from '@/components/old-task-manage/details/old-node/old-node.vue'
import variableModule from './variable/index.vue'
import statisticsModule from './statistics/index.vue'
import log from './log/index.vue'
import { getTaskTypeInfo, getTaskDetailTaskId, postTaskSkipTaskNodeId, postTaskRetryTaskNodeId } from '@/api/task'
import useStore from '@/stores'
import { getRoute } from '@/router'
import { useLocale } from 'youibot-plus'
import { useCustomTimer } from '@/hooks/use-custom-timer'
import { getOperateText } from '@/utils'
import { Modal } from 'ant-design-vue'
import emitter from '@/utils/mitts'
const { t } = useLocale()
const { nodeStore } = useStore()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object as PropType<ITableRow>,
    required: true
  },
  zIndex: {
    type: Number,
    default: 2005
  }
})
const staticState = computed(() => {
  return nodeStore.getStatisticsData
})
const State = reactive({
  nodeConfig: {} as INodeConfig,
  title: '',
  baseData: {} as INodeDetailItem,
  nameArr: [] as any[] //存储所有节点自定义名称的数组
})
let nowVal = ref(100)
const node = ref()
let timer: NodeJS.Timer | null
const emit = defineEmits(['update:visible', 'confirm', 'cancelDetailDialog'])
const cancel = function () {
  emit('update:visible', false)
  emit('cancelDetailDialog')
}
const nodeCode = computed(() => {
  return `（${props.data.taskNo}_${staticState.value.code}）`
})
// setObj 和 objectToArray方法，是为了递归获取所有节点自定义名称showName
function setObj(obj: any, arr: any): any {
  arr.push({ code: obj['code'], showName: obj['showName'], name: obj['name'] })
  return arr
}

function objectToArray(obj: any, arr: any): any {
  Object.keys(obj).forEach(key => {
    if (key === 'code') {
      arr = setObj(obj, arr)
    }
    if (key === 'childNode' && obj['childNode']) {
      if (obj['whileList']) {
        objectToArray(obj['whileList'], arr) && objectToArray(obj['childNode'], arr)
      }
      objectToArray(obj['childNode'], arr)
    }
    if (key === 'conditionNodes') {
      obj['conditionNodes'].forEach((conditionNode: any) => {
        objectToArray(conditionNode, arr)
      })
    }
  })
}

const getTaskTypeDetails = (id: number) => {
  getTaskTypeInfo({ id }).then(res => {
    const { data } = res
    if (data) {
      const { nodeParam, templateType, event, ...rest } = data
      const nodeParmObj = JSON.parse(data.nodeParam)
      objectToArray(nodeParmObj, State.nameArr)

      State.nodeConfig = {
        ...rest,
        templateType,
        event,
        type: '0',
        childNode: nodeParam ? JSON.parse(nodeParam) : null
      }
      if (!data.version) {
        setScroll()
      }
      setTimeout(() => {
        getTaskDetails(props.data.id)
      }, 500)
    }
  })
}

const getTaskDetails = (taskId: number) => {
  return getTaskDetailTaskId({}, { taskId }).then(res => {
    const { data } = res
    State.baseData = data
    //当前有被点击的节点
    const isClickNode = nodeStore.getStatisticsData.code as string
    if (isClickNode && isClickNode != 'null') {
      if (!data.nodeDetail || !data.nodeDetail[isClickNode]) return
      const {
        paramIn = {},
        paramOut = {},
        endTime,
        executionDuration,
        startTime,
        totalDuration,
        runningCount,
        code,
        type,
        totalDistance,
        leftDistance,
        isSkip,
        isAllowSkip,
        status,
        isAllowRetry,
        isAllowAlarm,
        id
      } = data.nodeDetail[isClickNode]
      let statisticsData = {
        endTime, // 结束时间
        executionDuration, // 执行时长(秒)
        startTime, // 	开始时间
        totalDuration, // 总时长（秒）
        runningCount, // 	运行次数
        code,
        type,
        totalDistance,
        leftDistance,
        isSkip,
        isAllowSkip,
        status,
        isAllowRetry,
        isAllowAlarm,
        id
      }
      if (data.nodeDetail) {
        State.nameArr.forEach((item: any) => {
          if (data.nodeDetail[item.code]) {
            data.nodeDetail[item.code].name = item.showName ? item.showName : item.name
          }
        })
      }
      nodeStore.setVariableData({ paramIn, paramOut })
      nodeStore.setStatisticsData(statisticsData)
      // nodeStore.setNodeDetails(data.nodeDetail)
      emitter.emit('nodeDetail', data.nodeDetail)
    } else {
      const { nodeDetail, paramIn = {}, paramOut = {}, endTime, executionDuration, startTime, totalDuration, runningCount } = data
      let statisticsData = {
        endTime, // 结束时间
        executionDuration, // 执行时长(秒)
        startTime, // 	开始时间
        totalDuration, // 总时长（秒）
        runningCount // 	运行次数
      }
      if (nodeDetail) {
        State.nameArr.forEach((item: any) => {
          if (nodeDetail[item.code]) {
            nodeDetail[item.code].name = item.showName ? item.showName : item.name
          }
        })
      }
      nodeStore.setVariableData({ paramIn, paramOut })
      nodeStore.setStatisticsData(statisticsData)
      // nodeStore.setNodeDetails(nodeDetail)
      emitter.emit('nodeDetail', data.nodeDetail)
    }
  })
}
const postTaskSkipFn = () => {
  Modal.confirm({
    content: t('message.taskManage.skipTip'),
    zIndex: props.zIndex + 3600,
    onOk() {
      postTaskSkipTaskNodeId({}, { taskNodeId: staticState.value.id as unknown as number })
        .then(res => {
          getOperateText(t, {
            type: 'taskManage.skip'
          })
          getTaskDetails(props.data.id)
        })
        .catch(err => {
          getOperateText(t, {
            type: 'taskManage.skip',
            result: 'error',
            reason: err.msg
          })
        })
    },
    onCancel() {}
  })
}
const postTaskRetryFn = () => {
  postTaskRetryTaskNodeId({}, { taskNodeId: staticState.value.id as unknown as number })
    .then(res => {
      getOperateText(t, {
        type: 'taskManage.retry'
      })
      getTaskDetails(props.data.id)
    })
    .catch(err => {
      getOperateText(t, {
        type: 'taskManage.retry',
        result: 'error',
        reason: err.msg
      })
    })
}

const { initTimer, stopTimerFn } = useCustomTimer(() => getTaskDetails(props.data.id), 3000)
const zoomSize = (type: number) => {
  if (type == 1) {
    if (nowVal.value == 10) {
      // messageTip('最小缩放至10%', 'error')
      return
    }
    nowVal.value -= 10
  } else {
    if (nowVal.value == 200) {
      // messageTip('最大缩放至200%', 'error')
      return
    }
    nowVal.value += 10
  }
  setScroll()
}
const setScroll = () => {
  if (timer) {
    clearTimeout(timer)
  }
  const { name, id, taskTypeId } = props.data
  getTaskDetails(id)
  timer = setTimeout(() => {
    if (node.value.scrollWidth > node.value.clientWidth) {
      node.value.scrollTo((node.value.scrollWidth - node.value.clientWidth) / 2, 0)
    }
  }, 500)
}
watch(
  () => props.visible,
  async val => {
    if (val) {
      const { name, id, taskTypeId } = props.data
      await getTaskTypeDetails(taskTypeId)
      // getTaskDetails(id)
      await getTaskDetails(id)
      initTimer()
      State.title = name
      // 恢复右键
      document.oncontextmenu = (e: MouseEvent): boolean => {
        return true
      }
    } else {
      if (getRoute().path === '/monitoring/agv-map') {
        // 禁用
        document.oncontextmenu = (e: MouseEvent): boolean => {
          return false
        }
      }

      stopTimerFn()
      nodeStore.setStatisticsData({
        code: 'null'
      })
      State.nodeConfig = {} as INodeConfig
      State.baseData = {} as INodeDetailItem
    }
  }
)
</script>
<style lang="less">
@bg-color: #f5f5f7;
@border-color: #a6b4bf;
@node-text-color: #7a7a7a;
@form-width: 378px;

.details {
  top: 0;
  height: 90%;
  max-height: 956px;
  margin: 0;
  padding: 0;

  &-data {
    position: relative;
    width: 35%;

    > div:not(.details-data-interval) {
      padding: 16px 20px;

      .title {
        margin-bottom: 16px;
        padding-left: 8px;
        color: #000;
        font-weight: 400;
        font-size: 14px;
        border-left: 2px solid #ee742a;
      }
    }

    .warning {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      width: 100%;
      height: 34px;
      padding: 0;
      padding: 0 20px;
      color: #ed8f03;
      font-size: 14px;
      line-height: 34px;
      background-color: #fafafa;
      border-top: 1px solid #dcdcdc;

      .warning-code {
        padding: 0 12px;
      }

      > i {
        font-size: 20px;
      }

      .warning-tips {
        .ellipsis();
      }
    }

    &-interval {
      height: 15px;
      margin: 16px 0;
      background-color: #f5f5f5;
    }

    &-title {
      display: flex;
      justify-content: space-between;
      color: #000;
      font-weight: bold;
      font-size: 16px;
    }

    &-content {
      height: 718px;
      overflow-y: auto;
    }
  }

  .ant-modal-content {
    height: 100%;

    .ant-modal-body {
      height: calc(100% - 42px);

      .my-modal-body {
        position: relative;
        display: flex;
        height: 100%;
        max-height: 100%;
        padding: 0;
        overflow: hidden;

        > .details-node > .details-scale {
          display: inline-block;
          width: 100%;
          min-width: min-content;
          height: 100%;

          > div {
            display: flex;
            flex-direction: column;
            height: 100%;
          }
        }
      }
    }
  }

  &-node {
    position: relative;
    flex: 1;
    width: 65%;
    overflow-x: auto;
  }

  &-interval {
    width: 15px;
    background-color: #f5f5f5;
  }

  .node-wrap {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
  }
}
</style>
<style lang="less" scoped>
// 缩放
.zoom {
  position: absolute;
  top: 20px;
  left: calc(100% - 35% - 35px - 152px);
  display: flex;
  align-items: center;
  justify-content: center;

  > div {
    width: 80px;
    height: 32px;
    margin: 0 4px;
    padding: 4px 12px;
    color: #000;
    font-size: 14px;
    text-align: center;
    background-color: #f5f5f5;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
  }

  .ant-btn {
    width: 32px;
    height: 32px;
    padding: 0 4px;
    color: #0009;
    background: #ebebeb;
    border: 1px solid #ebebeb;

    &:focus,
    &:hover {
      color: #000;
      background: #d9d9d9;
      border-color: #d9d9d9;
    }
  }
}
</style>
