<!-- 任务列表新增弹窗 -->
<template>
  <my-modal
    v-model:visible="visible"
    class="add-form"
    :zIndex="props.zIndex"
    :width="'600px'"
    :maskClosable="false"
    @cancel="cancel"
    @confirm="confirm">
    <template #title>
      {{ props.data.name }}
    </template>
    <a-form :labelCol="{ style: { width: '150px' } }" ref="formRef" :model="State">
      <a-form-item
        v-for="(item, index) in State.list"
        :key="item.variable"
        :label="item.variable"
        :rules="item.type === 'Json' ? [isRequire(), { required: true, validator: validatePass }] : [isRequire()]"
        :name="['list', index, 'defaultValue']">
        <task-form-type
          ref="taskFormTypeRef"
          :type="item.variableCategory === 'Number' ? item.variableCategory : item.type"
          :z-index="props.zIndex"
          v-model:value="item.defaultValue"
          :isMonitoringStation="props.isMonitoringStation"
          :options="isGetOptiosList(item.type) || item.selectionBoxParam"
          :fieldNames="getFieldNames(item.type)"
          :marker="props.marker"
          :maxValue="item.maxValue"
          :minValue="item.minValue"
          @amonitoringClick="amonitoringClick(index)"
          :placeholder="t('message.defaultValue')"></task-form-type>
      </a-form-item>
    </a-form>
  </my-modal>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type { ITableRow } from '@/pages/operations/tasks/task-type/config'
import type { InputVariableList } from '@/hooks/use-node'
import useNode from '@/hooks/use-node'

import { isRequire } from '@/utils/form-rules'
import { checkIsJSON, getOperateText, messageTip } from '@/utils'
import { postTaskTaskTypeId } from '@/api/task'
import useNodeForm from '@/hooks/use-node-form'
import { MarkerData, useLocale } from 'youibot-plus'
import { Rule } from 'ant-design-vue/lib/form'
const { t } = useLocale()
const props = defineProps({
  visible: Boolean,
  data: {
    type: Object as PropType<ITableRow>,
    required: true
  },
  zIndex: {
    type: Number,
    default: 1000
  },
  isMonitoringStation: {
    type: Boolean,
    default: false
  },
  vehicleCode: {
    type: String,
    default: ''
  },
  marker: {
    type: Object as PropType<MarkerData> | null
  }
})
const { initOptions, isGetOptiosList, getFieldNames } = useNodeForm()
const { getTaskTypeDefault } = useNode()
// 加载下拉框数据
initOptions()
const visible = computed(() => props.visible)
const formRef = ref()
let fromIndex: number | null = null
const State = reactive({
  list: [] as InputVariableList[],
  testObj: null
})
const emit = defineEmits(['update:visible', 'confirm', 'amonitoringClick', 'cancel'])

const cancel = function () {
  emit('update:visible', false)
  emit('cancel')
}

// 提交数据
const submitData = () => {
  let list = [] as InputVariableList[]
  for (let i = 0; i < State.list.length; i++) {
    const { defaultValue, type, variable, ...rest } = State.list[i]
    list[i] = {
      type,
      variable,
      defaultValue,
      ...rest
    }
  }
  let paramsData = getTaskTypeDefault(list)
  postTaskTaskTypeId(paramsData, { taskTypeId: props.data.id })
    .then(() => {
      getOperateText(t, {
        type: 'createTask'
      })
      emit('confirm')
    })
    .catch(err => {
      getOperateText(t, {
        type: 'createTask',
        result: 'error',
        reason: err.msg
      })
    })
}

const confirm = function () {
  formRef.value.validate().then(() => {
    cancel()
    submitData()
  })
}
const amonitoringClick = (index: number) => {
  fromIndex = index
  emit('amonitoringClick', State.list[index].variable)
}

const validatePass = async (_rule: Rule, item: string) => {
  if (item) {
    const val = item
    const str = val.replace(/[ ]|[\r\n]/g, '')
    if (!checkIsJSON(str)) {
      return Promise.reject(t('message.taskManage.jsonFormatError'))
    }
  }
  return Promise.resolve()
}
watch(
  () => props.marker,
  marker => {
    if (marker && props.isMonitoringStation && fromIndex !== null && State.list[fromIndex]) {
      State.list[fromIndex].defaultValue = marker.code
      const someState = State.list.some(item => {
        return !item.defaultValue
      })

      if (!someState) {
        submitData()
      } else {
        emit('update:visible', true)
      }
    }
  }
)
watch(visible, visible => {
  if (visible) {
    const { params } = props.data
    for (let i = 0; i < params.length; i++) {
      State.list[i] = { ...params[i] }
      if (props.marker && fromIndex === i) {
        State.list[i].defaultValue = props.marker.code
      }
    }
  } else {
    // 打开多个弹窗后，表单显示上次弹窗的值
    if (!props.isMonitoringStation) {
      State.list = [] as InputVariableList[]
    }
  }
})
</script>
<style lang="less" scoped>
.add-form {
  &-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}

.ant-form-item {
  /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
  :deep(&-label) {
    label {
      justify-content: end;
      width: 100%;
    }
  }
}
</style>
