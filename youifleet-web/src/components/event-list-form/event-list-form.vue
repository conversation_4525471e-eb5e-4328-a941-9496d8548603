<template>
  <!-- 事件类型 -->
  <a-form-item :label="t('message.taskTypeArrangement.eventType')" :rules="[isRequire()]" required>
    <a-select :options="eventTypeList" v-model:value="state.formData.type" @change="changeEventType"></a-select>
  </a-form-item>
  <p class="interval"></p>

  <!-- 生效日期 -->
  <template v-if="state.formData.type == eventTypeList[0].value">
    <a-form-item :label="t('message.taskTypeArrangement.effectiveDate')" required style="margin-bottom: 0">
      <a-row>
        <a-col :span="10">
          <a-form-item
            :name="['param', 'startDate']"
            :rules="[isRequire(t('message.taskTypeArrangement.pleaseEnterTheEffectiveStartDate'))]">
            <a-date-picker
              v-model:value="state.formData.param.startDate"
              :valueFormat="yearFormat"
              :defaultValue="dayjs().format(yearFormat)"
              @change="setFormStore" />
          </a-form-item>
        </a-col>
        <a-col :span="2" class="time-interval">一</a-col>
        <a-col :span="10">
          <a-form-item :name="['param', 'endDate']" :rules="[isRequire(t('message.taskTypeArrangement.pleaseEnterTheEffectiveEndDate'))]">
            <a-date-picker
              v-model:value="state.formData.param.endDate"
              :valueFormat="yearFormat"
              :defaultValue="dayjs().add(1, 'day').format(yearFormat)"
              @change="setFormStore" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form-item>
    <!-- 生效时间 -->
    <a-form-item :label="t('message.taskTypeArrangement.effectiveTime')" required style="margin-bottom: 0">
      <a-row>
        <a-col :span="10">
          <a-form-item
            :rules="[isRequire(t('message.taskTypeArrangement.pleaseEnterTheEffectiveStartTime'))]"
            :name="['param', 'startTime']">
            <a-time-picker
              v-model:value="state.formData.param.startTime"
              defaultValue="00:00:00"
              valueFormat="HH:mm:ss"
              @change="setFormStore" />
          </a-form-item>
        </a-col>
        <a-col :span="2" class="time-interval">—</a-col>
        <a-col :span="10">
          <a-form-item :name="['param', 'endTime']" :rules="[isRequire(t('message.taskTypeArrangement.pleaseEnterTheEffectiveEndTime'))]">
            <a-time-picker
              v-model:value="state.formData.param.endTime"
              defaultValue="23:59:59"
              valueFormat="HH:mm:ss"
              @change="setFormStore" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form-item>
    <!-- 间隔 -->
    <a-form-item :label="t('message.taskTypeArrangement.interval')" required :name="['param', 'intervalTime']">
      <a-input-number
        v-model:value="state.formData.param.intervalTime"
        :min="state.formData.param.unit == 'Second' ? 10 : 0"
        :max="99999"
        :precision="0"
        @blur="setFormStore"
        @pressEnter="evetBlur">
        <template #addonAfter>
          <a-select :options="timeList" v-model:value="state.formData.param.unit" @change="unitChange"></a-select>
        </template>
      </a-input-number>
    </a-form-item>
  </template>
  <!-- 按钮事件 -->
  <template v-else-if="state.formData.type == eventTypeList[1].value">
    <!-- 呼叫盒编号 -->
    <a-form-item :label="t('message.taskTypeArrangement.callBoxNumber')" :rules="[isRequire()]" :name="['param', 'callBoxCode']">
      <a-input-number
        min="1"
        max="99999"
        :precision="0"
        v-model:value="state.formData.param.callBoxCode"
        @blur="setFormStore"
        @pressEnter="evetBlur"></a-input-number>
    </a-form-item>
    <!-- 按钮编号 -->
    <a-form-item :label="t('message.taskTypeArrangement.buttonNumber')" :rules="[isRequire()]" :name="['param', 'buttoncode']">
      <a-input-number
        min="1"
        max="8"
        :precision="0"
        v-model:value="state.formData.param.buttoncode"
        @blur="setFormStore"
        @pressEnter="evetBlur"></a-input-number>
    </a-form-item>
  </template>
  <template v-else-if="state.formData.type == eventTypeList[2].value || state.formData.type == eventTypeList[3].value">
    <!-- 寄存器事件 -->
    <template v-if="state.formData.type == eventTypeList[2].value">
      <!-- IP地址 -->
      <a-form-item :label="t('message.taskTypeArrangement.ip')" :rules="[isIp()]" :name="['param', 'ip']">
        <a-input v-model:value="state.formData.param.ip" @blur="setFormStore" @pressEnter="evetBlur"></a-input>
      </a-form-item>
      <!-- 端口号 -->
      <a-form-item :label="t('message.taskTypeArrangement.portNumber')" :rules="[isPort(), isRequire()]" :name="['param', 'port']">
        <a-input-number
          min="1"
          max="30000"
          :precision="0"
          v-model:value="state.formData.param.port"
          @blur="setFormStore"
          @pressEnter="evetBlur"></a-input-number>
      </a-form-item>
      <!-- 功能码 -->
      <a-form-item :label="t('message.taskTypeArrangement.functionCode')" :rules="[isRequire()]" :name="['param', 'code']">
        <a-select v-model:value="state.formData.param.code" :options="codeList" @change="setFormStore"></a-select>
      </a-form-item>
    </template>

    <!-- 机器人寄存器事件 -->
    <template v-else-if="state.formData.type == eventTypeList[3].value">
      <!-- 机器人 -->
      <a-form-item
        :label="t('message.vehicle')"
        :rules="[{ required: true, validator: validateMulti, trigger: 'change' }]"
        :name="['param', 'vehicleCodeList']">
        <a-form-item-rest>
          <a-radio-group v-model:value="state.formData.param.multiSelect" name="multiSelect" @change="changeMulList">
            <!-- 全部 -->
            <a-radio :value="1">{{ t('message.taskTypeArrangement.effectiveScopeTaskState.1') }}</a-radio>
            <!-- 部分 -->
            <a-radio :value="2">{{ t('message.taskTypeArrangement.effectiveScopeTaskState.2') }}</a-radio>
          </a-radio-group>
        </a-form-item-rest>
        <a-select
          v-if="state.formData.param.multiSelect == 2"
          v-model:value="state.formData.param.vehicleCodeList"
          :options="vehicleList"
          mode="multiple"
          :fieldNames="{ label: 'name', value: 'vehicleCode', options: 'options' }"
          @change="setFormStore"></a-select>
      </a-form-item>
      <!-- 功能码 -->
      <a-form-item :label="t('message.taskTypeArrangement.functionCode')" :rules="[isRequire()]" :name="['param', 'code']">
        <a-select v-model:value="state.formData.param.code" :options="codeList" @change="setFormStore"></a-select>
      </a-form-item>
    </template>

    <template v-if="state.formData.param?.params">
      <div v-for="(item, index) in state.formData.param?.params" :key="index" class="condition">
        <div class="condition-group" :class="{ last: state.formData.param?.params && index == state.formData.param?.params.length - 1 }">
          <p class="condition-group-title">
            <!-- 条件组 -->
            <span>{{ t('message.taskTypeArrangement.setOfConditions') }}</span>
            <i v-if="index !== 0" class="iconfont icon-xiazai14" @click="delOr(index)"></i>
          </p>
          <a-row class="condition-group-content-title" style="margin-bottom: 15px" :gutter="16">
            <a-col :span="8">{{ t('message.taskTypeArrangement.registerAddress') }}</a-col>
            <a-col :span="5">{{ t('message.taskTypeArrangement.condition') }}</a-col>
            <a-col :span="8">{{ t('message.taskTypeArrangement.registerValue') }}</a-col>
            <a-col :span="3">{{ t('message.options') }}</a-col>
          </a-row>
          <div v-for="(child, i) in item" :key="i" class="condition-group-item" :class="{ last: i == item.length - 1 }">
            <div class="condition-group-content">
              <a-row :gutter="16">
                <!-- t('message.taskTypeArrangement.registerAddress') -->
                <a-col class="condition-group-variable" :span="8">
                  <a-form-item :label="''" :wrapperCol="{ span: 24 }" :rules="[isRequire()]" :name="['param', 'params', index, i, 'name']">
                    <a-input-number v-model:value="child.name" :precision="0" @blur="setFormStore" @pressEnter="evetBlur"></a-input-number>
                  </a-form-item>
                </a-col>
                <a-col class="condition-group-value" :span="5">
                  <a-select :options="getOptionCondition(child)" v-model:value="child.condition" @change="handleChange"></a-select>
                </a-col>
                <a-col class="condition-group-value" :span="8">
                  <!-- 值： -->
                  <a-input-number v-model:value="child.value" :precision="0" @blur="setFormStore" @pressEnter="evetBlur"></a-input-number>
                </a-col>
                <a-col v-if="i !== 0" class="condition-group-del" :span="3">
                  <i class="iconfont icon-xiazai14" @click="delAdd(index, i)"></i>
                </a-col>
              </a-row>
            </div>
            <!-- 且 -->
            <p class="condition-group-and" v-if="i < item.length - 1">{{ t('message.taskTypeArrangement.and') }}</p>
          </div>

          <a-button type="dashed" class="add-btn" @click="addAddParams(index)">
            <i class="iconfont icon-zengjia"></i>
            <!-- 添加条件 -->
            {{ t('message.taskTypeArrangement.addCondition') }}
          </a-button>
        </div>
        <!-- 或 -->
        <p v-if="state.formData.param?.params && index < state.formData.param?.params.length - 1" class="or-p">
          {{ t('message.taskTypeArrangement.or') }}
        </p>
      </div>
    </template>
    <a-button style="margin-bottom: 15px" type="dashed" class="add-btn" @click="addOrParams()">
      <i class="iconfont icon-zengjia"></i>
      <!-- 添加条件组 -->
      {{ t('message.taskTypeArrangement.addConditionGroup') }}
    </a-button>
  </template>
  <!-- 机器人异常事件 -->
  <template v-else-if="state.formData.type == eventTypeList[4].value">
    <!-- 生效范围（机器人） -->
    <a-form-item
      :label="t('message.taskTypeArrangement.effectiveScopeRobot')"
      :rules="[{ required: true, validator: validateMulti, trigger: 'change' }]"
      :name="['param', 'vehicleCodeList']">
      <a-form-item-rest>
        <a-radio-group v-model:value="state.formData.param.multiSelect" name="multiSelect" @change="changeMulList">
          <!-- 全部 -->
          <a-radio :value="1">{{ t('message.taskTypeArrangement.effectiveScopeTaskState.1') }}</a-radio>
          <!-- 部分 -->
          <a-radio :value="2">{{ t('message.taskTypeArrangement.effectiveScopeTaskState.2') }}</a-radio>
        </a-radio-group>
      </a-form-item-rest>
      <a-select
        v-if="state.formData.param.multiSelect == 2"
        v-model:value="state.formData.param.vehicleCodeList"
        mode="multiple"
        :options="vehicleList"
        :fieldNames="{ label: 'name', value: 'vehicleCode', options: 'options' }"
        @change="setFormStore"></a-select>
    </a-form-item>
  </template>
  <!-- 机器人任务完成事件 -->
  <template v-else-if="state.formData.type == eventTypeList[5].value">
    <!-- 生效范围（任务） -->
    <a-form-item
      :label="t('message.taskTypeArrangement.effectiveScopeTask')"
      :rules="[{ required: true, validator: validateMulti, trigger: 'change' }]"
      :name="['param', 'taskTypeIdList']">
      <a-form-item-rest>
        <a-radio-group v-model:value="state.formData.param.multiSelect" name="multiSelect" @change="changeMulList">
          <!-- 全部 -->
          <a-radio :value="1">{{ t('message.taskTypeArrangement.effectiveScopeTaskState.1') }}</a-radio>
          <!-- 部分 -->
          <a-radio :value="2">{{ t('message.taskTypeArrangement.effectiveScopeTaskState.2') }}</a-radio>
        </a-radio-group>
      </a-form-item-rest>
      <a-select
        v-if="state.formData.param.multiSelect == 2"
        v-model:value="state.formData.param.taskTypeIdList"
        mode="multiple"
        :options="taskTypeList"
        :filter-option="filterOption"
        :fieldNames="{ label: 'name', value: 'id', options: 'options' }"
        @change="setFormStore"></a-select>
    </a-form-item>
  </template>
  <eventFormOutput v-if="hasOutput(state.formData.type)" :templateType="state.formData.type"></eventFormOutput>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type {
  INodeConfig,
  IFixedTimeEvent,
  IButtonEvent,
  IPlcEvent,
  IVehiclePlcEvent,
  IVehicleAbnormalEvent,
  ITaskStatusEvent,
  IBaseEvent,
  InputVariableList
} from '@/hooks/use-node'
import dayjs from 'dayjs'
import { isIp, isPort } from '@/utils/form-rules'
import useNodeForm from '@/hooks/use-node-form'
import { isRequire } from '@/utils/form-rules'
import { postVehicleList } from '@/api/vehicle'
import { getTaskTypeList } from '@/api/task'
import { Rule } from 'ant-design-vue/es/form/interface'
import { useLocale, deepClone } from 'youibot-plus'
import useStore from '@/stores'
import { checkIsJSON, evetBlur, messageTip } from '@/utils'
const { eventTypeList, hasOutput, isMultiple, isNumber, isSelect, isGetOptiosList, getFieldNames, initOptions } = useNodeForm()
// 加载下拉框数据
initOptions()

const emits = defineEmits(['formDataChange', 'updeta:formData', 'changeEventType'])
const props = defineProps({
  formData: { type: Object, default: () => ({}) }
})
let formRef = ref()
let yearFormat = 'YYYY-MM-DD'
const vehicleList = ref([])
const taskTypeList = ref()
const { t } = useLocale()
const { nodeStore } = useStore()
type IEventData = IFixedTimeEvent & IButtonEvent & IPlcEvent & IVehiclePlcEvent & IVehicleAbnormalEvent & ITaskStatusEvent

const timeList = [
  { label: t('message.second'), value: 'Second' },
  { label: t('message.minute'), value: 'Minute' },
  { label: t('message.hour'), value: 'Hour' },
  { label: t('message.day'), value: 'Day' }
]

const codeList = [
  { label: '01', value: '01' },
  { label: '02', value: '02' },
  { label: '03', value: '03' },
  { label: '04', value: '04' }
]

const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const state = reactive({
  formData: props.formData
})

const setEventType = (val: string): IEventData => {
  //设置初始值，每次切换事件的时候，要切回到默认值数据
  let data = {} as IEventData
  const {
    unit = 'Second',
    intervalTime = 100,
    startDate = dayjs().format(yearFormat),
    endDate = dayjs().add(1, 'day').format(yearFormat),
    startTime = '00:00:00',
    endTime = '23:59:59',
    enable = true,
    callBoxCode = '',
    buttoncode = '',
    ip = '',
    port = '',
    code = '',
    address = '',
    value = '',
    vehicleCodeList = [],
    taskTypeIdList = [],
    multiSelect = 1,
    params = [
      [
        {
          condition: 'eq',
          name: '',
          value: ''
        }
      ]
    ]
  } = state.formData.param
  switch (val) {
    case eventTypeList[0].value:
      data = {
        unit, // 单位
        intervalTime, // 间隔
        startDate, // 生效开始日期
        endDate, // 生效结束日期
        startTime, // 生效开始时间
        endTime, // 生效结束时间
        enable
      }
      break
    case eventTypeList[1].value:
      data = {
        callBoxCode,
        buttoncode
      }
      break
    case eventTypeList[2].value:
      data = {
        ip, // ip
        port, // 端口
        code, // 功能码
        params,
        address, // 寄存器地址
        value // 寄存器值
      }
      break
    case eventTypeList[3].value:
      data = {
        vehicleCodeList, // 机器人
        code, // 功能码
        address, // 寄存器地址
        value, // 寄存器值
        multiSelect,
        params
      }
      break
    case eventTypeList[4].value:
      data = {
        vehicleCodeList, // 机器人
        multiSelect
      }
      break
    case eventTypeList[5].value:
      data = {
        taskTypeIdList, // 任务
        multiSelect
      }
      break
  }
  return data
}
const changeEventType = (val: string) => {
  state.formData.param = {
    unit: 'Second',
    intervalTime: 100,
    startDate: dayjs().format(yearFormat),
    endDate: dayjs().add(1, 'day').format(yearFormat),
    startTime: '00:00:00',
    endTime: '23:59:59',
    enable: true,
    callBoxCode: '',
    buttoncode: '',
    ip: '',
    port: '',
    code: '',
    address: '',
    value: '',
    vehicleCodeList: [],
    taskTypeIdList: [],
    multiSelect: 1,
    params: [
      [
        {
          condition: 'eq',
          name: '',
          value: ''
        }
      ]
    ]
  }
  const data = setEventType(val)
  state.formData.param = data
  formDataChangeFn(val, data)
  emits('changeEventType', val)
}
// 传值
const formDataChangeFn = (type: string, data: IEventData) => {
  emits('updeta:formData', {
    ...props.formData,
    param: data
  })
}

const setFormStore = () => {
  const { type } = state.formData
  formRef.value?.validate().then(() => {
    const data = setEventType(type)
    state.formData.param = data
    formDataChangeFn(type, data)
    nodeStore.setIsEventMistake(false)
  })
}
const unitChange = () => {
  if (
    state.formData.param.unit == 'Second' &&
    state.formData.param.intervalTime !== undefined &&
    state.formData.param.intervalTime !== null &&
    state.formData.param.intervalTime < 10
  ) {
    state.formData.param.intervalTime = 10
  }
  setFormStore()
}
const getVehicleList = () => {
  postVehicleList().then(res => {
    const { data } = res
    vehicleList.value = data
  })
}

const getAllTaskTypeList = () => {
  getTaskTypeList({}).then(res => {
    const { data } = res
    taskTypeList.value = data?.map((item: { id: any; name: any }) => {
      return {
        id: item.id,
        name: item.name
      }
    })
  })
}

const validateMulti = async (_rule: Rule, value: string) => {
  const {
    param: { multiSelect: number }
  } = state.formData
  if (number == 2 && value.length == 0) {
    return Promise.reject(t('message.requiredTips'))
  } else {
    return Promise.resolve()
  }
}

const changeMulList = () => {
  const {
    type,
    param: { multiSelect: number }
  } = state.formData
  if (number == 1) {
    if ([eventTypeList[2].value, eventTypeList[3].value].includes(type)) {
      state.formData.param.vehicleCodeList = []
    }
    if ([eventTypeList[5].value].includes(type)) {
      state.formData.param.taskTypeIdList = []
    }
  }
  setFormStore()
}
const delOr = (i: number) => {
  state.formData.param?.params?.splice(i, 1)
  handleChange()
}
const delAdd = (index: number, i: number) => {
  if (state.formData.param?.params) {
    state.formData.param?.params[index]?.splice(i, 1)
    handleChange()
  }
}
// 条件组下拉框
const getOptionCondition = (data: InputVariableList) => {
  return [
    { label: t('message.taskTypeArrangement.eq'), value: 'eq' }, //等于
    { label: t('message.taskTypeArrangement.ne'), value: 'ne' } //不等于
  ]
}
const handleChange = () => {
  emits('updeta:formData', state.formData.value)
}

const addOrParams = () => {
  state.formData.param?.params.push([
    {
      condition: 'eq',
      name: '',
      value: ''
    }
  ])
  // visible.value = true
  // State.nodeIndex = null
}

const addAddParams = (i: number) => {
  state.formData.param?.params[i].push({
    condition: 'eq',
    name: '',
    value: ''
  })
  // visible.value = true
  // State.nodeIndex = i
}
getAllTaskTypeList()
getVehicleList()
watchEffect(() => {
  state.formData = props.formData
  //进来先初始化值
  const { type } = props.formData
  if (state.formData.param && Object.keys(state.formData.param)) {
    state.formData.param = setEventType(type)
  } else {
    changeEventType(type)
  }
})
</script>
<style lang="less">
.event-form {
  .ant-col-4 {
    line-height: 32px;
  }

  .ant-col-8 {
    line-height: 32px;
  }

  .ant-col-5 {
    line-height: 32px;
  }

  .ant-col-3 {
    line-height: 32px;
  }

  .ant-input-number,
  .ant-input-number-group-wrapper {
    width: 100%;
  }

  .ant-radio-group {
    margin-bottom: 8px;
  }

  .ant-form-item {
    // margin-bottom: 0;

    .icon-jieshi {
      margin-left: 12px;
    }
  }

  &-title {
    height: 22px;
    margin: 0 20px 12px;
    padding-left: 12px;
    color: #000;
    line-height: 22px;
    border-left: 2px solid @primary-color;
  }

  &-header {
    padding: 0 12px;
    color: #a6a6a6;

    .ant-col {
      height: 32px;
      line-height: 32px;
    }
  }

  .event-form-item {
    margin: 0 20px;
  }

  &-add {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    margin: 12px 20px;
    color: #4f4f4f;
    line-height: 32px;
    border: 1px dashed #4f4f4f;
    border-radius: 4px;

    > i {
      margin-right: 4px;
    }
  }

  i {
    + i {
      margin-left: 8px;
    }
  }

  .row-list {
    min-height: 30px;
    padding: 4px 12px;
    color: #000;

    .ant-col {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &:hover {
      background: rgb(238 116 42 / 10%);
    }
  }

  .edit-row {
    padding: 12px;
    border: 1px solid #d9d9d9;

    .ant-select {
      width: 100%;
    }

    &-btn {
      margin-top: 12px;
      text-align: center;
    }

    &-default {
      margin-top: 12px;
    }
  }

  .input-number {
    width: 100%;
  }
}
</style>
<style scoped lang="less">
.time-interval {
  padding-top: 5px;
  text-align: center;
}

.add-btn {
  width: calc(100% - 40px);
  margin: 0 20px;

  i {
    margin-right: 4px;
  }
}

.icon-xiazai14 {
  color: #c1c1c1;
}

.or-p {
  height: 32px;
  margin-bottom: 0;
  line-height: 32px;
}

.condition {
  padding: 0 0 0 20px;
}

.condition-group {
  padding-bottom: 12px;
  border: 1px solid #dcdcdc;

  &.last {
    margin-bottom: 8px;
  }

  &-item {
    margin: 0 12px;

    &.last {
      margin-bottom: 8px;
    }
  }

  &-content {
    padding: 10px 12px;
    border: 1px dashed #cacaca;
  }

  &-content-title {
    margin-bottom: 15px;
    padding: 0 24px;
  }

  &-variable {
    height: 32px;

    .ant-select {
      width: 100px;
    }

    > p {
      flex: 1;
      height: 100%;
      margin-bottom: 0;
      line-height: 32px;
    }
  }

  &-value {
    display: flex;
    align-items: center;
    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.variable-input) {
      flex: 1;

      .ant-select {
        width: 100%;
      }
    }
  }

  &-del {
    margin-bottom: 0;
  }

  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    margin-bottom: 12px;
    padding: 0 12px;
    background-color: #dcdcdc;
  }

  &-and {
    height: 32px;
    margin: 0;
    line-height: 32px;
    text-align: center;
  }

  .add-btn {
    width: calc(100% - 24px);
    margin: 0 12px;
  }
}
</style>
