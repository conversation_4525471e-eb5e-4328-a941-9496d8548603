.agv-detail {
  padding: 7px 16px;

  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 0 4px;
    color: #000;
    font-weight: 400;
    font-size: 14px;
  }

  &-status {
    ul {
      display: flex;
      align-items: center;

      li {
        color: #e96868;

        > .iconfont {
          font-size: 14px;
        }
      }

      .li-default {
        color: #333;
        background-color: #fff;
        border: 1px solid #ecebeb;
        cursor: pointer;
      }
    }
  }

  &-attribute {
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 20px;

    &-left-col {
      display: flex;

      &-lable {
        min-width: 90px;
        // margin-right: 12px;
        color: #969696;
      }

      &-value {
        width: 90px;
        color: #000;
        .ellipsis();

        &-but {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 22px;
          padding: 4px 8px;
          border: 1px solid #ecebeb;
          cursor: pointer;
          /* stylelint-disable-next-line no-descending-specificity */
          .iconfont {
            font-size: inherit;
          }
        }
      }
    }

    &-right-col {
      display: flex;

      &-lable {
        min-width: 90px;
        // width: 36px;
        // margin-right: 12px;
        color: #969696;
      }

      &-value {
        width: 90px;
        color: #000;
        .ellipsis();

        &-but {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 22px;
          padding: 4px 8px;
          border: 1px solid #ecebeb;
          cursor: pointer;
          /* stylelint-disable-next-line no-descending-specificity */
          .iconfont {
            font-size: inherit;
          }
        }
      }
    }
  }
}

/* stylelint-disable-next-line selector-pseudo-class-no-unknown */
:deep(.ant-divider-horizontal) {
  margin: 6px 0;
}
