<template>
  <div class="left-agv-list" id="agv-list" @click=";(rightVisible = false), emit('cancelAgvMapRightClick')">
    <div class="left-agv-list-title">
      <div class="left-agv-list-title-name">{{ t('message.vehicle') }}</div>
      <div class="left-agv-list-title-search">
        <a-input ref="input" v-model:value="inputCode" :placeholder="t('message.requiredTips')" @input="getData">
          <template #suffix>
            <a-tooltip>
              <i class="iconfont icon-31sousuo" />
            </a-tooltip>
          </template>
        </a-input>
      </div>
    </div>
    <a-divider />
    <div class="left-agv-list-tabs">
      <a-tabs v-model:activeKey="activeKey" @change="tabsChange">
        <!-- 全部 -->
        <a-tab-pane key="all" :tab="`${t('message.all')} ${agvData?.all.length}`"></a-tab-pane>
        <!-- 未连接 -->
        <a-tab-pane key="disconnect" :tab="`${t('message.unconnected')} ${agvData?.disconnect.length}`"></a-tab-pane>
        <!-- <a-tab-pane key="offline" :tab="`离线 ${agvData?.offline.length}`"></a-tab-pane> -->
        <!-- 异常 -->
        <a-tab-pane key="abnormal" :tab="`${t('message.abnormal')} ${agvData?.abnormal.length}`"></a-tab-pane>
        <!-- 低电量 -->
        <a-tab-pane key="lowBattery" :tab="`${t('message.lowBattery')} ${agvData?.lowBattery.length}`"></a-tab-pane>
      </a-tabs>
    </div>
    <div class="left-agv-list-table">
      <a-table
        style="height: 100%; overflow-y: auto"
        :columns="columns"
        :row-key="handleRowKey"
        :row-selection="{
          type: 'radio',
          hideSelectAll: true,
          columnWidth: 0,
          selectedRowKeys: selectedRowKeys
        }"
        :data-source="dataSource"
        :pagination="false"
        :customRow="customRowFn">
        <template #bodyCell="scope">
          <template v-if="scope.column.key === 'abnormalStatus'">
            <a-tag
              v-if="scope.record?.defaultVehicleStatus?.runningStatus?.abnormalStatus"
              :color="isTagColor(scope.record?.defaultVehicleStatus?.runningStatus?.abnormalStatus)">
              {{
                t('message.robotManage.workbenchAbnormalStatusList.' + scope.record?.defaultVehicleStatus?.runningStatus?.abnormalStatus)
              }}
            </a-tag>
          </template>
          <template v-if="scope.column.key === 'connectStatus'">
            <span v-if="scope.record?.connectStatus">
              {{ t('message.robotManage.connectStatusList.' + scope.record?.connectStatus) }}
            </span>
            <!-- <a-tag v-if="scope.record.connectStatus" :color="isTagColor(scope.record.connectStatus)">
              {{ t('message.robotManage.connectStatusList.' + scope.record.connectStatus) }}
            </a-tag> -->
          </template>
          <template v-if="scope.column.key === 'workStatus'">
            <a-tag
              v-if="scope.record?.defaultVehicleStatus?.runningStatus?.workStatus"
              :color="isWorkColor(scope.record?.defaultVehicleStatus?.runningStatus?.workStatus)">
              {{ t('message.robotManage.workStatusList.' + scope.record?.defaultVehicleStatus?.runningStatus?.workStatus) }}
            </a-tag>

            <!-- <a-tag v-if="scope.record.workStatus" :color="isTagColor(scope.record.workStatus)">
             
            </a-tag> -->
          </template>
          <!-- 定位 -->
          <template v-if="scope.column.key === 'status'">
            <a-tag
              class="tag-status"
              v-if="scope.record?.defaultVehicleStatus?.positionStatus?.status"
              :color="isTagColor(scope.record?.defaultVehicleStatus?.positionStatus?.status)">
              {{ t('message.robotManage.locatedStatusList.' + scope.record?.defaultVehicleStatus?.positionStatus?.status) }}
            </a-tag>
          </template>
          <!-- 控制 -->
          <template v-if="scope.column.key === 'controlStatus'">
            <span v-if="scope.record?.defaultVehicleStatus?.runningStatus?.controlStatus">
              {{ t('message.robotManage.controlModeList.' + scope.record?.defaultVehicleStatus?.runningStatus?.controlStatus) }}
            </span>
          </template>
          <template v-if="scope.column.key === 'rate'">
            <span v-if="scope.record?.defaultVehicleStatus?.batteryList?.length">
              {{ calculatedRate(scope.record.defaultVehicleStatus.batteryList) }}
            </span>
            <!-- <a-tag v-if="scope.record.workStatus" :color="isTagColor(scope.record.workStatus)">
             
            </a-tag> -->
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <div v-if="rightVisible">
    <yi-right-click-operation
      :isShow="rightVisible"
      :operatList="operatList"
      :event="event"
      :activeMode="0"
      :fullScreen="false"
      @changeMode="changeModeEvent"></yi-right-click-operation>
  </div>
</template>
<script lang="ts" setup>
import { postVehicleDetailListGroup } from '@/api/vehicle'
import { ref } from 'vue'
import { EventPoint, GlobalIconList, useLocale } from 'youibot-plus'
import { isTagColor, AgvDataList, VehicleData, setOperatList, isWorkColor } from './config'
import { Key } from 'ant-design-vue/es/vc-table/interface'
import { useCustomTimer } from '@/hooks/use-custom-timer'
const { t } = useLocale()
const emit = defineEmits(['changeModeEvent', 'focusXY', 'cancelAgvMapRightClick'])
const props = defineProps({
  mapCode: {
    type: String,
    default: ''
  },
  locatingCode: { type: String, default: '' }
})
const inputCode = ref<string>()
const rightVisible = ref(false)
const event = ref<EventPoint | undefined>()
const activeKey = ref<string>('all')
const agvCode = ref<string>('')
const selectedRowKeys = ref()
const selectAgv = ref()
const operatList = ref<{
  elementsOperate: GlobalIconList[]
  canvasOperate: GlobalIconList[]
  globalOperation: GlobalIconList[]
}>({
  elementsOperate: [],
  canvasOperate: [],
  globalOperation: []
})
let agvData = ref<AgvDataList>({
  all: [],
  abnormal: [],
  disconnect: [],
  lowBattery: [],
  offline: []
})

const columns = ref([
  {
    //编码
    title: t('message.encoding'),
    dataIndex: 'vehicleCode',
    key: 'vehicleCode',
    ellipsis: true
  },
  {
    //连接
    title: t('message.join'),
    dataIndex: 'connectStatus',
    key: 'connectStatus',
    width: 60
  },
  {
    //工作
    title: t('message.professional'),
    dataIndex: 'workStatus',
    key: 'workStatus',
    width: 50
  },
  {
    //异常
    title: t('message.abnormal'),
    key: 'abnormalStatus',
    width: 50
  },
  {
    //定位
    title: t('message.orientation'),
    dataIndex: 'status',
    key: 'status',
    width: 60
  },
  {
    //控制
    title: t('message.controls'),
    dataIndex: 'controlStatus',
    key: 'controlStatus',
    width: 60
  },
  {
    title: t('message.robotManage.rate'),
    dataIndex: 'rate',
    key: 'rate',
    width: 50
  }
])

let dataSource = ref<never[]>([])
// 设置右键菜单列表
// eslint-disable-next-line @typescript-eslint/no-explicit-any

const clickInit = () => {
  const agvList = document.getElementById('agv-list')
  if (agvList) {
    agvList.oncontextmenu = (e: MouseEvent): boolean => {
      rightVisible.value = false
      e.preventDefault()
      return false
    }
    agvList.onclick = (): boolean => {
      rightVisible.value = false
      return true
    }
  }
}
const handleRowKey = (record: { vehicleCode: string }) => {
  return record.vehicleCode
}

const rightClickFn = (e: MouseEvent, record: VehicleData) => {
  emit('cancelAgvMapRightClick')
  operatList.value = setOperatList(record)
  selectAgv.value = record
  selectedRowKeys.value = record.vehicleCode
  agvCode.value = record.vehicleCode
  rightVisible.value = true
  event.value = e as unknown as EventPoint
  // const x = record?.defaultVehicleStatus?.positionStatus?.x
  // const y = record?.defaultVehicleStatus?.positionStatus?.y
  // if (x !== null && y !== null && x !== undefined && y !== undefined) {
  //   emit('focusXY', { x: x, y: y, vehicleCode: record.vehicleCode }, 'rightClick')
  // }

  e.stopPropagation()
  e.preventDefault()
}

const getData = () => {
  let data = { mapCode: props.mapCode, vehicleCode: inputCode.value, locatingCode: props.locatingCode }
  return postVehicleDetailListGroup(data).then(res => {
    agvData.value = res.data

    // 机器人连接为已连接的排前面
    dataSource.value = res.data[activeKey.value].sort(function (a: any, b: any) {
      var x = a['connectStatus'].substr(0, 1).charCodeAt()
      var y = b['connectStatus'].substr(0, 1).charCodeAt()
      return x - y
    })
  })
}
const { initTimer, stopTimerFn } = useCustomTimer(getData, 5000)

watchEffect(() => {
  if (props.mapCode) {
    initTimer()
  } else {
    stopTimerFn()
  }
})
const changeModeEvent = (mode: number) => {
  rightVisible.value = false
  emit('changeModeEvent', mode, agvCode.value, selectAgv.value, undefined, getData)
}

const tableLeftClickFn = (e: MouseEvent, record: VehicleData) => {
  selectedRowKeys.value = record.vehicleCode
  const x = record?.defaultVehicleStatus?.positionStatus?.x
  const y = record?.defaultVehicleStatus?.positionStatus?.y
  if (x !== null && y !== null && x !== undefined && y !== undefined) {
    emit('focusXY', record, 'leftClick')
  }
}

const customRowFn = (record: VehicleData) => {
  return {
    onContextmenu: (event: MouseEvent) => {
      rightVisible.value = false
      setTimeout(() => {
        rightClickFn(event, record)
      }, 5)
    },
    onclick: (event: MouseEvent) => {
      rightVisible.value = false
      tableLeftClickFn(event, record)
    }
  }
}
const calculatedRate = (list: { rate: number }[]) => {
  let rate = 0
  list.forEach(item => {
    rate += item.rate
  })
  return list.length ? rate / list.length + '%' : 0 + '%'
}
const tabsChange = (activeKey: Key) => {
  dataSource.value = agvData.value[activeKey]
}

// 后台时不发起轮询
const visibilitychangeFn = () => {
  var string = document.visibilityState
  if (string === 'hidden') {
    // 当页面由前端运行在后端时，出发此代码
    stopTimerFn()
  }
  if (string === 'visible') {
    // 当页面由隐藏至显示时
    initTimer()
  }
}
document.addEventListener('visibilitychange', visibilitychangeFn)
onMounted(() => {
  clickInit()
})
onBeforeUnmount(() => {
  document.removeEventListener('visibilitychange', visibilitychangeFn)
  stopTimerFn()
})
defineExpose({
  rightVisible
})
</script>
<style lang="less" scoped>
.left-agv-list {
  width: 425px;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #dcdcdc;

  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 37px;
    padding: 6px 12px 0;

    &-name {
      color: #000;
      font-size: 14px;
    }

    &-search {
      .ant-input {
        width: 120px;
        height: 24px;
      }

      .ant-input-affix-wrapper {
        padding: 0 8px;
      }
    }
  }

  &-tabs {
    padding: 0 12px;
    font-size: 12px;

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-tabs) {
      color: #969696;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-tabs-tab) {
      padding: 2px 3px;
      font-size: 14px;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-tabs-tab + .ant-tabs-tab) {
      margin-left: 13px;
    }
  }

  &-table {
    height: calc(100% - 60px - 83px);
    padding: 0 12px;
    font-size: 14px;

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-table-thead > tr > th) {
      color: #a0a0a0;
      font-size: 14px;
      background-color: #f9f8f8;
      border-bottom: none;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-table-thead > tr > th, .ant-table-tbody > tr > td, .ant-table tfoot > tr > th, .ant-table tfoot > tr > td) {
      padding: 3px 9px;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-table-thead > tr > th:not(:last-child, .ant-table-selection-column, .ant-table-row-expand-icon-cell, [colspan])::before) {
      display: none;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-table-cell) {
      padding: 4px 0;
      text-align: center;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-table-tbody > tr > td) {
      font-size: 14px;
      border-bottom-color: #f4f4f4;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(
        .ant-table-tbody > tr.ant-table-row:hover > td,
        .ant-table-tbody > tr > td.ant-table-cell-row-hover,
        .ant-table-tbody > tr.ant-table-row:hover > td,
        .ant-table-tbody > tr > td.ant-table-cell-row-hover
      ) {
      background: #f8f8f8;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-tag-error) {
      color: #ee7070;
      background-color: #fff4f4;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-tag-success) {
      background-color: #fff;
    }

    /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
    :deep(.ant-tag) {
      margin: 0;
      padding: 0 6px;
    }
  }

  /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
  :deep(td.ant-table-selection-column) {
    // position: absolute;
    visibility: hidden;
    // width: 0;
  }
}

// 分割线
/* stylelint-disable-next-line selector-pseudo-class-no-unknown */
.ant-divider-horizontal {
  margin: 7px 0;
  background-color: #dcdcdc;
}
</style>
