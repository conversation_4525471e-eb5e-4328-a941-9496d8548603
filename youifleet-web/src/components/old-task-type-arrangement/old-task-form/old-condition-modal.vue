<template>
  <!-- 条件设置 -->
  <my-modal
    v-model:visible="visible"
    :title="t('message.taskTypeArrangement.settlementOfCondition')"
    :width="'700px'"
    class="condition-modal"
    @confirm="confirm">
    <a-form :labelAlign="'left'" :colon="false">
      <!-- 定值 -->
      <a-form-item :label="t('message.taskTypeArrangement.constantValue')" v-if="props.type == 'While'">
        <a-checkbox-group v-model:value="State.checkedList" style="width: 100%">
          <a-checkbox :value="item.value" v-for="(item, index) in LetList" :key="index">{{ item.label }}</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <!-- 变量 -->
      <a-form-item :label="t('message.variable')">
        <a-checkbox-group v-model:value="State.variableList" style="width: 100%">
          <a-checkbox v-for="(item, index) in outputs" :key="index" :value="JSON.stringify(item)">
            {{ item.number }}.{{ item.name ? item.name : item.variable }}
          </a-checkbox>
        </a-checkbox-group>
      </a-form-item>
    </a-form>
  </my-modal>
</template>

<script lang="ts" setup>
import type { IOutput, InputVariableList } from '@/hooks/use-node'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const props = defineProps({
  visible: Boolean,
  type: {
    type: String,
    required: true,
    default: 'While'
  }
})
let emits = defineEmits(['update:visible', 'confirm'])
const visible = computed({
  get: () => {
    if (props.visible) {
      State.checkedList = []
      State.variableList = []
    }
    return props.visible
  },
  set: val => {
    emits('update:visible', val)
  }
})
const State = reactive({
  checkedList: [] as string[],
  variableList: [] as string[]
})
const LetList = [
  { label: t('message.taskTypeArrangement.cycleTime'), value: 'cycleTime' }, //循环时间
  { label: t('message.taskTypeArrangement.cycleNumber'), value: 'cycleCount' } //循环次数
]
const outputs = inject<IOutput[]>('output')

const confirm = function () {
  // 根据checkedList variableList 转一下的数据格式
  let data = [] as InputVariableList[]
  if (State.checkedList) {
    for (let i = 0; i < State.checkedList.length; i++) {
      const selectItem = LetList.find(item => item.value == State.checkedList[i])
      let value = selectItem ? selectItem.value : ''
      let label = selectItem ? selectItem.label : ''
      // 定值
      let item = {
        name: t('message.taskTypeArrangement.constantValue') + ':' + label,
        source: 'Fixed',
        isVariable: true,
        editable: true,
        variable: value,
        nodeCode: '',
        code: value,
        type: 'Default',
        condition: value === 'cycleTime' ? 'lt' : 'eq', // 循环时间为小于，其它全部为等于。
        defaultValue: '',
        number: label,
        variableCategory: 'Number'
      } as unknown as InputVariableList
      data.push(item)
    }
  }

  // 变量模块
  if (outputs && State.variableList) {
    for (let i = 0; i < State.variableList.length; i++) {
      const selectVariable = State.variableList[i] ? JSON.parse(State.variableList[i]) : ''
      let variableName = selectVariable.number + '.' + (selectVariable.name ? selectVariable.name : selectVariable.variable)
      let variableCode = selectVariable.number + '.' + (selectVariable.variable ? selectVariable.variable : selectVariable.code)
      const isTask = variableName.indexOf('Task') > -1
      let item = {
        name: t('message.variable') + ':' + variableName,
        source: '',
        isVariable: true,
        editable: true,
        variable: isTask ? variableCode : 'Node.' + variableCode,
        nodeCode: '',
        code: isTask ? variableName : 'Node.' + variableCode,
        condition: 'eq',
        type: selectVariable?.type,
        number: variableName,
        variableCategory: selectVariable.variableCategory
        // attribute: getattribute(selectVariable?.type) // 条件机器人，点位属性字段
      } as unknown as InputVariableList
      data.push(item)
    }
  }

  emits('confirm', data)
  visible.value = false
}
// const getattribute = (type: string) => {
//   if (['MarkerCode'].includes(type)) {
//     return 'code'
//   } else if (['VehicleCode'].includes(type)) {
//     return 'vehicleCode'
//   }
// }
</script>
<style lang="less">
.condition-modal {
  .ant-form-item-label {
    width: 100px;
  }

  .ant-checkbox-wrapper {
    min-width: calc(25% - 8px);
    margin: 0 8px 0 0;
  }

  .ant-form-item {
    margin-bottom: 12px;
  }
}
</style>
