import type { IBtnType, IFormItemType } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, SELECT, RANGEPICKER } from 'youibot-plus'
import { getLogSysModuleSelect } from '@/api/log'
import permission from '@/utils/directives/permission'

export interface ITableRow {
  name: string
  createTime: number
  updateTime: number
  closeTime: number
}

//table操作栏
const tableOptions: IBtnType[] = [
  {
    name: 'message.download',
    type: 'link',
    permission: 'download'
  }
]

//table 配置
const columns: TableColumnsType = [
  // 文件名
  {
    title: 'message.log.filename',
    dataIndex: 'name'
  },

  // 创建时间
  {
    title: 'message.createTime',
    key: 'createDate',
    width: 200
  },

  {
    title: 'message.options',
    key: 'options',
    fixed: 'right'
  }
]
// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    is: INPUT,
    name: 'name',
    label: 'message.log.filename',
    allowClear: true
  },

  {
    is: RANGEPICKER,
    name: 'createDate',
    allowClear: true,
    label: 'message.createTime',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  }
]

//底部按钮
const btnList: IBtnType[] = []

export { columns, filterConfigList, tableOptions, btnList }
