<template>
  <my-modal v-model:visible="visible" :modalFooter="false" :width="'80%'" @cancel="cancel" @confirm="cancel">
    <div style="height: 600px">
      <MyTable
        :isSelected="false"
        :filterConfigList="State.filterConfigList"
        :filterData="State.filterData"
        :columns="State.columns"
        :tableOptions="State.tableOptions"
        :pagination="pageState.pagination"
        v-model:selectedRowKeys="State.selectedRowKeys"
        :data="pageState.tableData"
        :is-loading="pageState.isLoading"
        :btnList="btnList"
        :isFilter="false"
        :tabelLocalKey="'systemDetailsLog'"
        @formChange="filterChange">
        <template #createDate="{ scope }">{{ setTimeStr(scope.record.createDate) }}</template>
        <template #lastTime="{ scope }">{{ setTimeStr(scope.record.lastTime) }}</template>
      </MyTable>
    </div>
  </my-modal>
</template>

<script setup lang="ts">
import type { ITableScope } from 'youibot-plus'
import usePage from '@/hooks/use-page'
import { useLocale } from 'youibot-plus'
import { columns, filterConfigList, btnList, tableOptions } from './config'
import type { ITableRow } from './config'
import { setTimeStr, downLoadFileOpen } from '@/utils/index'
import { getLogFilePage } from '@/api/log'
const { pageState, changeFilter, getData } = usePage({
  getPageFn: getLogFilePage
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
})
let emits = defineEmits(['update:visible'])
const props = defineProps({
  visible: Boolean
})

const { t } = useLocale()
const visible = ref<boolean>(false)
watchEffect(() => {
  visible.value = props.visible
})
tableOptions[0].onClick = (text?: object) => {
  if (text) {
    const { record } = text as ITableScope<ITableRow>
    downLoadFileOpen(t, '/fleet/log/file/download', { fileName: record.name })
  }
}

const State = reactive({
  columns,
  tableOptions,
  filterData: {},
  filterConfigList,
  formData: {},
  selectedRowKeys: [] as (string | number)[]
})
// 筛选条件
const filterChange = (filterData: { [key: string]: string }, current = 1) => {
  let params: { [key: string]: string } = {}
  for (let item in filterData) {
    if (Array.isArray(filterData[item])) {
      const data = toRaw(filterData[item]) as unknown as string[]
      params[item] = data.join(',')
    } else {
      params[item] = filterData[item]
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}
const cancel = () => {
  emits('update:visible', false)
}
onMounted(() => {
  getData()
})
</script>
<style lang="less">
.operation-log-tooltip {
  max-width: 1300px;
  max-height: 900px;
  overflow-y: auto;
}

.table-cell-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
}
</style>
