<template>
  <my-modal
    v-model:visible="visible"
    class="modal-param"
    :title="t('message.actionSetting.parameter')"
    :width="'700px'"
    @cancel="cancel"
    @confirm="confirm">
    <a-form
      :labelCol="{ style: { width: '80px' } }"
      autocomplete="off"
      ref="formRef"
      labelAlign="left"
      :model="state.formData"
      :rules="rules">
      <a-row :gutter="20">
        <a-col :span="12">
          <a-form-item :label="t('message.actionSetting.parameterCode')" name="code" class="bolok">
            <a-input v-model:value="state.formData.code"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="t('message.actionSetting.parameterName')" name="name" class="bolok">
            <a-input v-model:value="state.formData.name"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item :label="t('message.actionSetting.notice')" class="bolok">
        <a-textarea v-model:value="state.formData.notice"></a-textarea>
      </a-form-item>
      <a-row>
        <a-col :span="6">
          <!--  必填-->
          <a-form-item :label="t('message.required')" name="required" class="switch-item">
            <a-switch v-model:checked="state.formData.required" size="small" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <!--  编辑-->
          <a-form-item :label="t('message.edit')" name="editable" class="switch-item">
            <a-switch v-model:checked="state.formData.editable" size="small" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <!-- 变量 -->
          <a-form-item :label="t('message.variable')" name="isVariable" class="switch-item">
            <a-switch v-model:checked="state.formData.isVariable" size="small" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item :label="t('message.actionSetting.parameterType')" name="type">
        <a-radio-group v-model:value="state.formData.variableCategory" @change="variableCategoryChange">
          <a-radio v-for="(item, index) in baseTypeList" :key="index" :value="item.value">{{ t(item.label) }}</a-radio>
        </a-radio-group>
      </a-form-item>
      <template v-if="props.paramType == 'Out' || state.formData.category == 'Out'">
        <a-form-item
          v-if="state.formData.variableCategory === 'Text' || state.formData.variableCategory == 'Common'"
          :label="t('message.actionSetting.inputBox')"
          name="type">
          <a-radio-group v-model:value="state.formData.type" @change="selectChange">
            <a-radio value="Default">
              {{ t('message.actionSetting.componentOptions.Default') }}
            </a-radio>
            <a-radio value="Combination">{{ t('message.actionSetting.componentOptions.customParameter') }}</a-radio>
          </a-radio-group>
        </a-form-item>
      </template>
      <!-- 输入参数可编辑默认值 -->
      <template v-if="props.paramType == 'In' || state.formData.category == 'In'">
        <a-form-item
          v-if="state.formData.variableCategory === 'Text' || state.formData.variableCategory == 'Common'"
          :label="t('message.actionSetting.inputBox')"
          name="type">
          <a-radio-group v-model:value="state.formData.type" @change="selectChange">
            <a-radio v-for="(item, index) in componentOptions" :key="index" :value="item.value">{{ t(item.label) }}</a-radio>
          </a-radio-group>
        </a-form-item>
        <template v-if="state.formData.variableCategory == 'Text' || state.formData.variableCategory == 'Common'">
          <!-- 默认值 -->
          <a-form-item :label="t('message.defaultValue')" class="bolok" v-if="state.formData.type === 'Default'">
            <a-input v-model:value="state.formData.defaultValue"></a-input>
          </a-form-item>
          <!-- 默认值 -->
          <a-form-item :label="t('message.defaultValue')" class="switch-item" v-if="state.formData.type === 'Bool'">
            <a-switch v-model:checked="state.formData.defaultValue" :checkedValue="'true'" :unCheckedValue="'false'" />
          </a-form-item>
          <!-- 默认值 -->
          <a-form-item :label="t('message.defaultValue')" class="bolok" v-if="state.formData.type === 'Json'">
            <a-textarea v-model:value="state.formData.defaultValue"></a-textarea>
          </a-form-item>
          <template v-if="['RadioList', 'MultiList'].includes(state.formData.type)">
            <a-row class="list-param" v-for="(item, index) in state.formData.selectionBoxParam" :key="index">
              <a-col :span="10">
                <!-- 描述 -->
                <a-form-item :label="t('message.describe')" class="bolok"><a-input v-model:value="item.desc"></a-input></a-form-item>
              </a-col>
              <a-col :span="10">
                <!-- 值 -->
                <a-form-item :label="t('message.value')" class="bolok"><a-input v-model:value="item.value"></a-input></a-form-item>
              </a-col>
              <a-col :span="4" class="del-params"><i class="iconfont icon-chacha" @click="delListItem(index)"></i></a-col>
            </a-row>
            <a-row justify="center">
              <!-- 新增 -->
              <a-button type="link" @click="addListItem">
                <i class="iconfont icon-zengjia"></i>
                {{ t('message.add') }}
              </a-button>
            </a-row>
          </template>
        </template>
        <template v-if="['Int', 'Float', 'Number'].includes(state.formData.variableCategory)">
          <a-row :gutter="20">
            <a-col :span="12">
              <!-- 最大值 -->
              <a-form-item :label="t('message.maximumValue')" name="maxValue">
                <a-input v-model:value="state.formData.maxValue"></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <!-- 最小值 -->
              <a-form-item :label="t('message.minimumValue')" name="minValue">
                <a-input v-model:value="state.formData.minValue"></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="12">
              <!-- 默认值 -->
              <a-form-item :label="t('message.defaultValue')" class="bolok">
                <a-input v-model:value="state.formData.defaultValue"></a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </template>
      </template>
    </a-form>
  </my-modal>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type { IActionParam, IActionParamSelect } from '@/pages/operations/setting/node-settings/config'
import { isRequire, isLength, englishFirst } from '@/utils/form-rules'
import { componentOptions, baseTypeList } from '@/pages/operations/setting/node-settings/config'
import { deepClone, useLocale } from 'youibot-plus'

const { t } = useLocale()
const props = defineProps({
  visible: Boolean,
  data: {
    type: Object as PropType<IActionParam>,
    required: true
  },
  paramType: {
    type: String
  }
})
const visible = ref<boolean>(false)
const formRef = ref()
const state = reactive({
  formData: {
    code: '',
    name: '',
    notice: '',
    type: '',
    required: true,
    editable: true,
    isVariable: true,
    category: '',
    defaultValue: '',
    maxValue: null,
    minValue: null,
    componentType: '',
    selectionBoxParam: [{ desc: '', value: '' }] as IActionParamSelect[],
    variableCategory: 'Text'
  } as IActionParam
})
const emit = defineEmits(['update:visible', 'confirm'])
watchEffect(() => {
  visible.value = props.visible
  if (props.visible) {
    state.formData = deepClone(props.data)
  }
})
const rules = {
  code: [englishFirst(), isRequire()], //长度限制20个字符;英文开头;支持英文、下划线、数字
  name: [isLength(1, 20), isRequire()],
  required: [isRequire()],
  editable: [isRequire()],
  isVariable: [isRequire()],
  type: [isRequire()],
  maxValue: [isRequire()],
  minValue: [isRequire()]
}

// 参数类型修改
const selectChange = function () {
  if (state.formData.type === 'Bool') {
    state.formData.defaultValue = true
    return
  } else if (['RadioList', 'MultiList'].includes(state.formData.type)) {
    state.formData.selectionBoxParam = [{ desc: '', value: '' }]
  }
  state.formData.defaultValue = ''
}
const variableCategoryChange = function () {
  state.formData.type = 'Default'
  state.formData.defaultValue = ''
}
const cancel = function () {
  emit('update:visible', false)
}

const confirm = function () {
  formRef.value.validate().then(() => {
    let data = state.formData
    if (!['RadioList', 'MultiList'].includes(state.formData.type)) {
      data = Object.assign({}, data, {
        selectionBoxParam: []
      })
    }
    console.log(data, 'data')
    emit('confirm', toRaw(data))
    emit('update:visible', false)
  })
}

// 单选列表、多选列表新增
const addListItem = () => {
  const { selectionBoxParam } = state.formData
  selectionBoxParam.push({ desc: '', value: '' })
}

// 单选列表、多选列表删除
const delListItem = (index: number) => {
  const { selectionBoxParam } = state.formData
  selectionBoxParam.splice(index, 1)
}
</script>

<style lang="less" scoped>
.modal-param {
  .ant-form-item {
    margin-bottom: 12px;

    &.switch-item {
      /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
      :deep(.ant-form-item-label > label) {
        height: 22px;
      }

      /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
      :deep(.ant-form-item-control-input-content) {
        line-height: 1;
      }

      /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
      :deep(.ant-form-item-control-input) {
        min-height: 22px;
      }
    }
  }

  .del-params {
    height: 32px;
    line-height: 32px;
    text-align: center;
  }

  .ant-radio-group-outline {
    .ant-radio-wrapper {
      width: 130px;
      margin-right: 0;
      margin-bottom: 8px;
    }
  }
}

.bolok {
  display: block;
}
</style>
