<template>
  <my-drawer
    class="default-form-drawer"
    :mask="true"
    :maskStyle="{ opacity: 0, animation: 'none' }"
    :closable="true"
    :title="props.data.id ? t('message.edit') : t('message.add')"
    :width="442"
    :visible="props.visible"
    @close="handleCancel">
    <a-form
      :labelAlign="'left'"
      layout="vertical"
      ref="formActionRef"
      autocomplete="off"
      :labelWrap="true"
      :model="state.formData"
      :rules="rules">
      <!-- 类型 -->
      <a-form-item :label="t('message.type')" name="type">
        <a-input v-model:value="state.formData.type"></a-input>
      </a-form-item>
      <!-- 类型名称 -->
      <a-form-item :label="t('message.typeName')" name="name">
        <a-input v-model:value="state.formData.name"></a-input>
      </a-form-item>
      <a-form-item :label="t('message.actionSetting.notice')" name="notice">
        <a-textarea v-model:value="state.formData.notice"></a-textarea>
      </a-form-item>
      <a-form-item :label="t('message.actionSetting.type')" name="category">
        <a-select v-model:value="state.formData.category">
          <a-select-option v-for="(item, index) in optionsType" :value="item.value">{{ t(item.label) }}</a-select-option>
        </a-select>
      </a-form-item>
      <!-- 重试次数 -->
      <a-form-item :label="t('message.actionSetting.retryNum')">
        <a-input-number v-model:value="state.formData.retryNum"></a-input-number>
      </a-form-item>
      <!-- 允许跳过 -->
      <a-form-item :label="t('message.actionSetting.skipAllow')" name="isAllowSkip">
        <a-switch v-model:checked="state.formData.isAllowSkip"></a-switch>
      </a-form-item>
      <!-- 允许重试 -->
      <a-form-item :label="t('message.actionSetting.allowRetry')" name="isAllowRetry">
        <a-switch v-model:checked="state.formData.isAllowRetry"></a-switch>
      </a-form-item>
      <!-- 异常报警 -->
      <a-form-item :label="t('message.abnormalAlarm')" name="isAllowAlarm">
        <a-switch v-model:checked="state.formData.isAllowAlarm"></a-switch>
      </a-form-item>
      <!-- 常用节点 -->
      <a-form-item :label="t('message.taskTypeArrangement.commonNode')" name="isCommon">
        <a-switch v-model:checked="state.formData.isCommon"></a-switch>
      </a-form-item>
      <a-form-item :label="t('message.actionSetting.icon')">
        <my-upload :showUploadList="false" :customRequest="customRequest" accept="image/png"></my-upload>
      </a-form-item>
      <div class="parameter">
        <p>{{ t('message.actionSetting.parameter') }}</p>
        <template v-if="state.formData.nodeParamConfigDTOList">
          <a-row class="parameter-title">
            <!-- 分类 -->
            <a-col :span="4">{{ t('message.actionSetting.type') }}</a-col>
            <a-col :span="7">{{ t('message.actionSetting.parameterCode') }}</a-col>
            <a-col :span="7">{{ t('message.actionSetting.parameterName') }}</a-col>
            <a-col :span="6"></a-col>
          </a-row>
          <a-row class="parameter-data" v-for="(item, index) in state.formData.nodeParamConfigDTOList" :key="item.id">
            <a-col :span="4">
              {{ item.category === 'In' ? t('message.taskTypeArrangement.import') : t('message.taskTypeArrangement.export') }}
            </a-col>
            <a-col :span="7">{{ item.code }}</a-col>
            <a-col :span="7">{{ item.name }}</a-col>
            <a-col :span="6">
              <a-button type="link" primary @click="addParameters(item, index + 1)">{{ t('message.edit') }}</a-button>
              <a-button type="link" danger @click="delParameters(index)">{{ t('message.del') }}</a-button>
            </a-col>
          </a-row>
        </template>

        <a-row class="parameter-btn" :justify="'center'">
          <a-col :span="10">
            <a-button @click="addParameters('In')">
              <i class="iconfont icon-zengjia"></i>
              {{ t('message.actionSetting.addInputParameters') }}
            </a-button>
          </a-col>
          <a-col :span="10">
            <a-button @click="addParameters('Out')">
              <i class="iconfont icon-zengjia"></i>
              {{ t('message.actionSetting.addOutputParameters') }}
            </a-button>
          </a-col>
        </a-row>
      </div>
    </a-form>

    <template #footer>
      <a-button @click="previewParam">{{ t('message.actionSetting.preview') }}</a-button>
      <a-button @click="handleCancel">{{ t('message.cancel') }}</a-button>
      <a-button type="primary" @click="handleSubmit">{{ t('message.submit') }}</a-button>
    </template>
    <ModalParam v-model:visible="visibleParam" :data="state.paramData" :paramType="state.paramType" @confirm="submitParamData"></ModalParam>
    <ModalPreview v-model:visible="visiblePreview" :data="state.previewData"></ModalPreview>
  </my-drawer>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type { IActionParam, ITableRow } from '@/pages/operations/setting/node-settings/config'
import { useLocale } from 'youibot-plus'
import ModalParam from './modal-param.vue'
import ModalPreview from './modal-preview.vue'
import { isRequire, isLength, englishFirst } from '@/utils/form-rules'
import { optionsType } from '@/pages/operations/setting/node-settings/config'
import { messageTip } from '@/utils'
import { getTaskNodeConfigInfo } from '@/api/task'
const { t } = useLocale()
const visibleParam = ref(false)
const visiblePreview = ref(false)
const state = reactive({
  formData: {
    nodeParamConfigDTOList: [] as IActionParam[],
    category: '',
    createDate: '',
    creator: 0,
    enterParamCount: 0,
    icon: '',
    name: '',
    notice: '',
    outParamCount: 0,
    system: '',
    type: '',
    updateDate: '',
    updater: 0,
    isCommon: false,
    isAllowSkip: false,
    retryNum: undefined as undefined | number,
    isAllowRetry: false,
    isAllowAlarm: false
  },
  paramData: {} as IActionParam,
  previewData: {} as ITableRow,
  paramType: '',
  curEdit: 0
})
const rules = {
  type: [englishFirst(), isRequire()], //长度限制20个字符;英文开头;支持英文、下划线、数字
  name: [isLength(1, 20), isRequire()],
  notice: [isRequire()],
  category: [isRequire()]
}
const emit = defineEmits(['close', 'submit'])
const formActionRef = ref()

const props = defineProps({
  visible: Boolean,
  data: {
    type: Object as PropType<ITableRow>,
    required: true
  }
})
const handleCancel = () => {
  formActionRef.value.resetFields()
  emit('close')
}
const handleSubmit = () => {
  formActionRef.value.validate().then(() => {
    emit('close')
    emit('submit', state.formData)
  })
}
// 新增参数
const addParameters = (category: string | IActionParam, index?: number) => {
  if (typeof category == 'string') {
    state.paramData = {
      code: '',
      name: '',
      notice: '',
      type: 'Default',
      required: true,
      editable: true,
      isVariable: true,
      category: category,
      defaultValue: '',
      maxValue: 0,
      minValue: 0,
      componentType: '',
      selectionBoxParam: [{ desc: '', value: '' }],
      variableCategory: 'Text'
    }
    state.paramType = category
  } else {
    state.paramData = category
  }
  if (index) {
    state.curEdit = index
  } else {
    state.curEdit = 0
  }
  visibleParam.value = true
}
// 删除参数
const delParameters = (index: number) => {
  state.formData.nodeParamConfigDTOList.splice(index, 1)
}
// 预览
const previewParam = () => {
  state.previewData = state.formData
  visiblePreview.value = true
}

// 转base64
const getBase64 = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

// 图标 转base64 赋值给icon字段
const customRequest = async (data: { file: File }) => {
  const param = await getBase64(data.file)
  if (typeof param == 'string') {
    state.formData.icon = param
    messageTip(t('message.actionSetting.iconUploadComplete'))
  }
}
// 新增参数回传
const submitParamData = (data: IActionParam) => {
  if (state.formData.nodeParamConfigDTOList == null) {
    state.formData.nodeParamConfigDTOList = []
  }
  const { type, selectionBoxParam } = data
  if (['RadioList', 'MultiList'].includes(type)) {
    data.defaultValue = selectionBoxParam[0].value
  }
  if (state.curEdit > 0) {
    state.formData.nodeParamConfigDTOList[state.curEdit - 1] = data
  } else {
    state.formData.nodeParamConfigDTOList.push(data)
  }
}

const getTaskNodeConfigDetails = (id: number) => {
  getTaskNodeConfigInfo({ id }).then(res => {
    state.formData = res.data as ITableRow
  })
}

watchEffect(() => {
  if (props.visible) {
    const { id } = props.data
    if (id) {
      getTaskNodeConfigDetails(id)
    } else {
      state.formData = props.data as ITableRow
    }
  }
})
</script>
<style lang="less" scoped>
.default-form-drawer {
  .ant-form-item {
    margin-bottom: 12px;
  }

  .ant-btn-link {
    padding: 0;
  }

  .parameter {
    margin-top: 24px;

    > p {
      height: 32px;
      margin-bottom: 0;
      padding-left: 12px;
      line-height: 32px;
      border-left: 6px solid @primary-color;
    }

    &-title {
      color: #9999;

      .ant-col {
        height: 32px;
        line-height: 32px;
      }
    }

    &-data {
      .ant-col {
        height: 32px;
        overflow-x: hidden;
        line-height: 32px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    &-btn {
      margin-top: 20px;

      .ant-btn {
        i {
          margin-right: 4px;
          line-height: 1;
        }

        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
