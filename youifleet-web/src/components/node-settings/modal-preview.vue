<!-- 节点设置 -->
<template>
  <my-modal v-model:visible="visible" class="modal-preview" :width="'600px'" @cancel="cancel" @confirm="confirm">
    <template #title>
      <a-image v-if="props.data.icon" :src="props.data.icon" :width="20" :preview="false"></a-image>
      {{ props.data.name }}
    </template>
    <a-form :labelCol="{ style: { width: '100px' } }" :colon="false">
      <a-form-item v-for="item in props.data.nodeParamConfigDTOList" :key="item.notice">
        <template #label>
          <div class="modal-preview-label">
            <a-tooltip placement="top" v-if="item.notice">
              <i class="icon-jieshi iconfont"></i>
              <template #title>
                <span>{{ item.notice }}</span>
              </template>
            </a-tooltip>
            <span>{{ item.name }}</span>
          </div>
        </template>
        <task-form-type
          :type="item.type"
          v-model:value="item.defaultValue"
          :options="isGetOptiosList(item.type) || item.selectionBoxParam"
          :fieldNames="getFieldNames(item.type)"
          :labelInValue="item.selectionBoxParam ? false : true"
          :maxValue="item.maxValue"
          :minValue="item.minValue"
          :disabled="!item.editable"
          :placeholder="t('message.defaultValue')"></task-form-type>
      </a-form-item>
    </a-form>
  </my-modal>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type { ITableRow } from '@/pages/operations/setting/node-settings/config'
// import { useLocale } from 'youibot-plus'
// const { t } = useLocale()
import useNodeForm from '@/hooks/use-node-form'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const props = defineProps({
  visible: Boolean,
  data: {
    type: Object as PropType<ITableRow>,
    required: true
  }
})
const visible = ref<boolean>(false)
const emit = defineEmits(['update:visible', 'confirm'])
const { initOptions, isGetOptiosList, getFieldNames } = useNodeForm()
// 加载下拉框数据
initOptions()
watchEffect(() => {
  visible.value = props.visible
  if (props.visible) {
    for (let i in props.data.nodeParamConfigDTOList) {
      const item = props.data.nodeParamConfigDTOList[i]
      if (item.type === 'MultiList') {
        item.defaultValue = [item.selectionBoxParam[0].value] as unknown as string
      }
      if (item.type === 'RadioList') {
        item.defaultValue = item.selectionBoxParam[0].value
      }
    }
  }
})
const cancel = function () {
  emit('update:visible', false)
}

const confirm = function () {
  cancel()
}
</script>
<style lang="less" scoped>
.modal-preview {
  &-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}

.ant-form-item {
  /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
  :deep(&-label) {
    label {
      width: 100%;
    }
  }
}
</style>
