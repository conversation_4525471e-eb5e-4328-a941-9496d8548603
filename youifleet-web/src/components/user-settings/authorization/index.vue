<template>
  <div class="authorization-wrap">
    <checkModule v-if="state.menuList && state.menuList.length > 0" :menu-list="state.menuList" level="1"></checkModule>
    <NoData v-else :text="t('message.userSetting.pleaseSelectARole')" />
  </div>
</template>
<script lang="ts" setup>
import useStore from '@/stores'
import checkModule from './checkModule.vue'
import type { IAuthDataType } from './config'
import { initMenuList } from './config'
import { getSysRoleId } from '@/api/sys'
import { messageTip } from '@/utils'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const { roleStore } = useStore()
let state = reactive({
  menuList: [] as IAuthDataType[]
})

// 获取当前角色的菜单列表
const getRoleMenu = () => {
  const id = roleStore.getRoleId
  if (id) {
    getSysRoleId({}, { id }).then(({ data }) => {
      let list = []
      if (data && data.menuList) {
        list = data.menuList
      }
      state.menuList = list
      roleStore.setMenuList(list)
      initMenuList(roleStore.getMenuList)
    })
  } else {
    state.menuList = []
    roleStore.setMenuList([])
  }
}

watchEffect(() => {
  const activeKey = roleStore.getActiveKey
  if (activeKey === '2') {
    getRoleMenu()
  }
})

defineExpose({
  getRoleMenu
})
</script>
<style lang="less" scoped>
.authorization-wrap {
  width: 100%;
  height: 100%;
  overflow: auto;

  .authorization-inner {
    display: flex;
    width: 100%;
    margin-bottom: 16px;

    &:last-of-type {
      margin-bottom: 0;
    }

    .authorization-stair {
      display: flex;
      align-items: center;
      width: 138px;
      min-height: 48px;
      padding-left: 16px;
      background-color: #fafafa;
      border: 1px solid #dcdcdc;
    }

    .authorization-right {
      flex: 1;
      border: 1px solid #dcdcdc;
      border-top: none;
      border-left: none;

      &-line {
        display: flex;
        border-left: none;

        &-secondary {
          display: flex;
          align-items: center;
          width: 184px;
          min-height: 48px;
          padding-left: 16px;
          background-color: #fafafa;
          border-top: 1px solid #dcdcdc;
        }

        &-three {
          display: flex;
          flex: 1;
          flex-wrap: wrap;
          align-items: center;
          min-height: 48px;
          padding: 8px 0 8px 16px;
          background-color: #fff;
          border-top: 1px solid #dcdcdc;
          border-left: 1px solid #dcdcdc;

          .ant-checkbox-wrapper {
            margin: 8px;
            margin-right: 32px;
            margin-left: 0;
          }
        }
      }
    }
  }
}
</style>
