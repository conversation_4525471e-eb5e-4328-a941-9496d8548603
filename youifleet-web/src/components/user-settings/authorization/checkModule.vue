<template>
  <template v-for="(item, index) in props.menuList" :key="item.name">
    <!-- 1040592168881004545过滤掉节点设置 -->
    <div :sid="item.id" v-if="item.id !== '1040592168881004545'" :class="styleCss(item)">
      <template v-if="isShow(item)">
        <a-checkbox v-model:checked="item.checked" :indeterminate="item.indeterminate" @change="changeCheckbox($event, item)">
          <span v-if="item.type === 0">{{ t('message.menuList.menu.' + item.name) }}</span>
          <span v-else>{{ t('message.menuList.button.' + item.name) }}</span>
        </a-checkbox>
      </template>

      <div class="inner">
        <checkModule
          v-if="item.children && item.children.length > 0"
          :menu-list="item.children"
          :level="props.level ? props.level + index + '-' : index + '-'"></checkModule>
        <!-- 一级菜单无子节点时补全内容 -->
        <div v-else-if="isFirstMenu()" class="flex last">
          <div class="ant-checkbox-wrapper bor-b-none"></div>
          <div class="inner bor-b-none"></div>
        </div>
      </div>
    </div>
  </template>
</template>

<script lang="ts" setup>
import { changeCheckbox, isLastMenu } from './config'
import type { IAuthDataType } from './config'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const props = defineProps({
  menuList: {
    type: Array<IAuthDataType>,
    default() {
      return []
    }
  },
  level: {
    type: String,
    default: '1'
  }
})
// 只显示一级菜单、最后一级菜单一级按钮
const isShow = (item: IAuthDataType): boolean => {
  const { type } = item

  return type == 1 || isLastMenu(item) || isFirstMenu()
}
// 是否是一级菜单
const isFirstMenu = (): boolean => {
  return props.level == '1'
}
// 判断是否是按钮
const isBtn = (type: number): boolean => {
  return type == 1
}

// 样式
const styleCss = (item: IAuthDataType): string => {
  const { type, children } = item
  let className = 'flex '

  if (isFirstMenu()) {
    className += 'first '
  }
  if (isLastMenu(item)) {
    className += 'last '
    if (children.length > 0) {
      className += 'has-child '
    }
  }

  if (isBtn(type)) {
    className += 'btn '
  }

  return className
}
</script>
<style lang="less" scoped>
/* stylelint-disable no-descending-specificity */
.flex {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  > .inner {
    flex: 1;
    min-height: 48px;
  }

  > .ant-checkbox-wrapper {
    min-width: 140px;
    height: 48px;
    padding-left: 16px;
    line-height: 48px;
  }

  .bor-b-none {
    border-top: none;
  }

  &.has-child {
    .inner {
      .btn {
        display: inline-block;
        height: 48px;
      }
    }
  }

  &.last {
    height: 48px;

    > .inner {
      position: relative;
      height: 100%;
      background-color: #fff;
      border-top: 1px solid #dcdcdc;

      &::before {
        position: absolute;
        left: 0;
        width: 1px;
        height: 100%;
        background-color: #dcdcdc;
        content: '';
      }
    }

    > .ant-checkbox-wrapper {
      border-top: 1px solid #dcdcdc;
      border-right: 1px solid #dcdcdc;
      border-left: 1px solid #dcdcdc;
    }

    &.has-child {
      position: relative;
      width: auto;
      height: auto;
      border-top: 1px solid #dcdcdc;
      border-left: 1px solid #dcdcdc;

      > .ant-checkbox-wrapper {
        border: none;
      }

      > .inner {
        border-top: none;
      }
    }
  }

  &.first {
    width: 100%;
    margin-bottom: 16px;
    background-color: #fafafa;
    border: 1px solid #dcdcdc;

    &.last {
      > .ant-checkbox-wrapper {
        background-color: #fafafa;
        border-left: none;
      }

      > .inner {
        width: 140px;
        height: 100%;
      }
    }

    .flex:first-of-type {
      .inner:first-of-type {
        .flex {
          &:first-of-type {
            border-top: none;

            .inner {
              border-top: none;
            }

            .ant-checkbox-wrapper {
              border-top: none;
            }
          }
        }
      }
    }

    > .inner:first-of-type {
      > .flex:first-of-type {
        > .ant-checkbox-wrapper,
        > .inner {
          border-top: none;

          &:first-of-type {
            > .flex:first-of-type {
              > .ant-checkbox-wrapper,
              > .inner {
                border-top: none;
              }
            }
          }
        }
      }
    }
  }
}
</style>
