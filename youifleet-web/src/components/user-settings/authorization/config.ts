import useStore from '@/stores'
import { putSysRole } from '@/api/sys'
import { messageTip } from '@/utils'
export interface IAuthDataType {
  name: string
  id: string
  flag: boolean
  type: number
  checked: boolean
  indeterminate: boolean
  children: IAuthDataType[]
}
const checkAllOrALl = (checked: boolean, item: IAuthDataType) => {
  const { children } = item
  if (children && children.length > 0) {
    children.forEach(child => {
      child.checked = checked
      child.flag = checked
      if (checked) child.indeterminate = false
      checkAllOrALl(checked, child)
    })
  }
}
// 判断是否是最后一级按钮
export const isLastMenu = (item: IAuthDataType) => {
  const { type, children } = item
  const childLen = children.length
  let isLast = childLen == 0
  if (children && childLen > 0) {
    const btnList = children.find(item => item.type == 1)
    isLast = typeof btnList == 'object'
  }
  return type == 0 && isLast
}
// 判断子元素是否被选中
const getChildrenChecked = (item: IAuthDataType) => {
  const { children } = item
  if (children && children.length > 0) {
    let checkedLen = 0
    let flagLen = 0
    let indeterminateLen = 0
    children.forEach(child => {
      if (child.checked) {
        checkedLen += 1
      }
      if (child.indeterminate) {
        indeterminateLen += 1
      }
      if (child.flag) {
        flagLen += 1
      }
    })
    item.checked = checkedLen === children.length
    if (isLastMenu(item)) {
      item.indeterminate = checkedLen > 0 && checkedLen < children.length
    } else {
      item.indeterminate = indeterminateLen > 0 || (checkedLen > 0 && checkedLen < children.length)
    }

    item.flag = flagLen > 0
  }

  return item
}
// 修改 indeterminate
export const setAllIndeterminate = (list: IAuthDataType[], id: string, checked: boolean) => {
  list.forEach(child => {
    if (child.children) {
      setAllIndeterminate(child.children, id, checked)
    }
    if (child.id == id) {
      checkAllOrALl(checked, child)
    }
    child = getChildrenChecked(child)
  })
}
const { roleStore } = useStore()
export const changeCheckbox = ($event: { target: { checked: boolean } }, item: IAuthDataType) => {
  const checked = $event.target.checked
  item.flag = checked

  setAllIndeterminate(roleStore.getMenuList, item.id, checked)

  updateRoleMenu()
}

const baseFlagSetCheck = (list: IAuthDataType[]) => {
  list.forEach(child => {
    if (child.children) {
      baseFlagSetCheck(child.children)
    }
    child.checked = child.flag
    child = getChildrenChecked(child)
  })
}

export const initMenuList = (menuList: IAuthDataType[]) => {
  baseFlagSetCheck(menuList)
}

// const isChildrenChecked = (item: IAuthDataType) => {
//   const { children } = item

//   const list = children.filter(child => child.checked)
//   return list.length > 0
// }
// const setCheckStatus = (menuList: IAuthDataType[]) => {
//   menuList.forEach(menu => {
//     const { children } = menu
//     if (children && children.length > 0) {
//       children.forEach(child => {
//         child.checked = isChildrenChecked(child)
//       })
//       menu.checked = isChildrenChecked(menu)
//     } else {
//       menu.checked = isChildrenChecked(menu)
//     }
//   })

//   return menuList
// }

const updateRoleMenu = () => {
  const { getMenuList, getRoleId, getRoleName } = roleStore
  const data = {
    id: getRoleId,
    menuList: getMenuList,
    name: getRoleName
  }

  putSysRole(data)
    .then(() => {
      // const { roleStore } = useStore()
      // roleStore.setMenuList(data.menuList)
    })
    .catch(err => {
      messageTip(err.msg, 'error')
    })
}
