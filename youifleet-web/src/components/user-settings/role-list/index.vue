<template>
  <div class="role-list-wrap" @click="cancel">
    <div class="role-list-inner" v-permission="permissions">
      <div class="role-list-title">{{ t('message.userSetting.roleManage') }}</div>
      <ul class="role-list-items">
        <li
          v-for="(item, index) in roleList"
          :key="index"
          class="role-list-items-item"
          :class="{ 'role-list-items-selected-item': itemId === item.id }"
          @click.stop="listItemClick(item)">
          <div>
            <div class="role-list-items-item-name">
              <template v-if="!item.isEdit">
                <a-tooltip v-if="item.name.length > 15" :destroyTooltipOnHide="true" placement="right">
                  <template #title>
                    <span>{{ item.name }}</span>
                  </template>
                  <span>{{ item.name }}</span>
                </a-tooltip>
                <span v-else>{{ item.name }}</span>
              </template>

              <a-input
                v-else
                v-model:value="item.name"
                :placeholder="t('message.requiredTips')"
                @blur="updetaRole(item)"
                ref="roleInput"
                @pressEnter="evetBlur" />
            </div>
            <a-dropdown :overlayClassName="'menu-item'">
              <div v-permission="[permissions[0], permissions[1]]" class="ant-dropdown-link" @click.prevent>
                <i class="icon-more iconfont"></i>
              </div>
              <template #overlay>
                <a-menu>
                  <div v-permission="permissions[0]">
                    <a-menu-item @click="setEdit(index)">
                      <a href="javascript:;">{{ t('message.userSetting.rename') }}</a>
                    </a-menu-item>
                  </div>
                  <div v-permission="permissions[1]">
                    <a-menu-item @click="deleteRole(index)">
                      <a href="javascript:;">{{ t('message.del') }}</a>
                    </a-menu-item>
                  </div>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="role-list-items-item-note">{{ item.realNames ? item.realNames : '---' }}</div>
        </li>
      </ul>
      <div class="add-but">
        <my-button
          class="but"
          :btnItem="{
            name: 'message.userSetting.addRole',
            debounceTime: 500,
            onClick: addRole,
            permission: 'role/add',
            icon: 'iconfont icon-zengjia'
          }"></my-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import useStore from '@/stores'
import { useLocale } from 'youibot-plus'
import { postSysRole, putSysRole, deleteSysRole, getSysRoleId, getSysRoleList } from '@/api/sys'
import { getOperateText, messageTip, evetBlur } from '@/utils'
const { t } = useLocale()
const emit = defineEmits(['addRoleSuccess'])

interface Role {
  name: string
  id: number
  realNames: string
  isEdit: boolean
}
const state = reactive({
  roleName: '',
  roleId: 0
})
let roleList = ref<Role[]>([])
let itemId = ref<number | null>()
let roleInput = ref()
const permissions = ref<string[]>([
  '/operations/setting/user-settings/role/rename',
  '/operations/setting/user-settings/role/del',
  '/operations/setting/user-settings/role/page',
  '/operations/setting/user-settings/role/add',
  '/operations/setting/user-settings/role/edit'
])
const { roleStore } = useStore()
/**
 *
 * @param {Role} item id
 */
function listItemClick(item: Role) {
  itemId.value = item.id
  roleStore.setRoleId(item.id)
  roleStore.setRoleName(item.name)
}

const generateRoleName = () => {
  let i = 1
  let len = roleList.value.length

  while (i <= len) {
    const list = roleList.value.find(item => {
      return item.name == t('message.userSetting.role') + i
    })
    if (list) {
      i += 1
    } else {
      break
    }
  }
  let str = t('message.userSetting.role') + i
  state.roleName = str
  return str
}

/**
 * 新增角色
 */
function addRole() {
  let params = {
    name: generateRoleName(),
    realNames: '',
    createDate: '',
    menuList: []
  }
  postSysRole(params)
    .then(res => {
      const { data } = res
      state.roleId = data.id
      roleStore.setRoleId(state.roleId)
      roleStore.setRoleName(state.roleName)
      // itemId.value = data.id
      getRoleList('add')
      getOperateText(t)
      emit('addRoleSuccess')
    })
    .catch(err => {
      getOperateText(t, {
        result: 'error',
        reason: err.msg
      })
    })
}
/**
 *
 * @param {Role } item Role
 */
async function updetaRole(item: Role) {
  if (!item.name) {
    messageTip(t('message.userSetting.nameTip'), 'warning')
    return
  }
  if (item.name.length > 20) {
    messageTip(t('message.userSetting.theNameCannotExceed20Characters'), 'warning')
    return
  }
  let roleInfo = await getRoleInfo()
  putSysRole({ ...item, menuList: roleInfo.menuList })
    .then(() => {
      getRoleList('updeta')
      if (state.roleId) {
        roleStore.setRoleId(state.roleId)
        state.roleId = 0
      }
      getOperateText(t, {
        type: 'update'
      })
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg
      })
    })
}
/**
 *
 * @param {string} type 是否是添加后
 */
async function getRoleList(type?: string) {
  const { data } = await getSysRoleList()
  if (data) {
    roleList.value = data
    roleStore.setRoleList(data)
    if (type === 'add') {
      focusNewRole()
    }
  }
}

const focusNewRole = () => {
  roleList.value.forEach((item, index) => {
    if (item.id === state.roleId) {
      itemId.value = roleList.value[index]?.id
      roleList.value[index].isEdit = true
      roleInputFocus()
    }
  })
}

/**
 * 获取角色信息
 */
async function getRoleInfo() {
  const { data } = await getSysRoleId({}, { id: (itemId.value as number) || state.roleId })
  return data
}

/**
 * 删除
 *
 * @param {number} index role一项
 * @returns {void}
 */
async function deleteRole(index: number) {
  await deleteSysRole([roleList.value[index]?.id])
    .then(() => {
      getOperateText(t, {
        type: 'delete'
      })
    })
    .catch(err => {
      getOperateText(t, {
        type: 'delete',
        result: 'error',
        reason: err.msg
      })
    })
  roleStore.setRoleId(null)
  getRoleList()
}
/**
 *
 * @param {number} index 下标
 */
function setEdit(index: number) {
  listItemClick(roleList.value[index])
  roleList.value[index].isEdit = true
  roleInputFocus()
}

/**
 * input 获取焦点
 */
async function roleInputFocus() {
  await nextTick()
  roleInput.value[0].focus()
  roleInput.value[0].setSelectionRange(0, roleInput.value[0].stateValue.length)
}
const cancel = () => {
  if (itemId.value) {
    roleStore.setRoleId(null)
    roleStore.setRoleName('')
    getRoleList()
    itemId.value = null
  }
}
roleStore.$subscribe((mutation, state) => {
  itemId.value = state.roleId
})
onMounted(() => {
  getRoleList()
})

defineExpose({
  getRoleList
})
</script>
<style lang="less" scoped>
.role-list-wrap {
  position: relative;
  width: 272px;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #dcdcdc;
}

.role-list-inner {
  width: 272px;
  height: 100%;
}

.add-but {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 16px 0;
  text-align: center;
  background-color: #fff;

  .but {
    width: 211px;
    height: 32px;
    color: @primary-color;
    border: 1px solid @primary-color;
    border-radius: 3px;

    &:hover,
    &:focus {
      color: @primary-color !important;
      border-color: @primary-color !important;
    }
  }
}

.role-list {
  height: 100%;

  &-title {
    height: 45px;
    padding: 10px 16px;
    color: #000;
    font-size: 16px;
    border-bottom: 1px solid #dcdcdc;
  }

  &-items {
    height: calc(100% - 64px - 47px);
    margin-bottom: 0;
    overflow-y: auto;

    &-selected-item {
      color: @primary-color !important;
      background: rgb(238 116 42 / 5%);
    }

    &-item {
      height: 68px;
      padding: 12px 16px;
      color: #000;
      border-bottom: 1px solid #e7e7e7;
      cursor: pointer;

      &-name {
        width: 200px;
        overflow: hidden;
        font-size: 14px;
        white-space: nowrap;
        text-overflow: ellipsis;

        .ant-form-item {
          margin-bottom: 0;
        }
      }

      > div:first-of-type {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &-note {
        margin-top: 2px;
        overflow: hidden;
        color: #999;
        font-size: 12px;
        white-space: nowrap; /* 规定段落中的文本不进行换行 */
        text-overflow: ellipsis;
      }
    }

    &-item:hover {
      color: @primary-color !important;
      background: rgb(238 116 42 / 5%);
    }

    .ant-dropdown-link {
      i {
        display: inline-block;
        color: #9b9b9b;
        font-size: 16px;
        transform: rotate(90deg);

        &:hover {
          color: @primary-color;
          cursor: pointer;
        }
      }
    }
  }
}

/* stylelint-disable-next-line selector-pseudo-class-no-unknown */
:deep(.ant-dropdown-menu-item:hover) {
  color: @primary-color;
  background-color: rgb(238 116 42 / 10%);
}

/* stylelint-disable-next-line selector-pseudo-class-no-unknown */
:deep(.ant-dropdown-menu-title-content > a:hover) {
  color: @primary-color;
}

.ant-dropdown.menu-item {
  .ant-dropdown-menu {
    min-width: 110px;
  }
}
</style>
