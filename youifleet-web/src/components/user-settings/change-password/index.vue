<template>
  <my-modal
    v-model:visible="visible"
    :title="t(`message.changePassword.${type === 'changePassword' ? 'changePassword' : 'resetPassword'}`)"
    :width="'658px'"
    :zIndex="3000"
    @cancel="cancel"
    @confirm="confirm">
    <div class="change-password">
      <a-form :label-col="{ span: 7 }" :rules="rules" ref="formRef" :model="formState">
        <a-form-item
          :label="t(`message.changePassword.${type === 'changePassword' ? 'oldPassword' : 'loginUserPassword'}`)"
          name="password">
          <a-input v-model:value="formState.password" type="password" />
        </a-form-item>
        <a-form-item :label="t('message.changePassword.newPassword')" name="newPassword">
          <a-input v-model:value="formState.newPassword" type="password" />
        </a-form-item>
        <a-form-item :label="t('message.changePassword.confirmNewPassword')" name="confirmNewPassword">
          <a-input v-model:value="formState.confirmNewPassword" type="password" />
        </a-form-item>
      </a-form>
    </div>
  </my-modal>
</template>
<script setup lang="ts">
import { Rule } from 'ant-design-vue/es/form'
import { isRequire } from '@/utils/form-rules'
import { postSysUserPassword, postSysUserPasswordReset } from '@/api/sys'
import { messageTip, getOperateText } from '@/utils'
import useStore from '@/stores'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()

const { changePassword } = useStore()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 1
  }
})
interface FormState {
  password: string
  newPassword: string
  confirmNewPassword: string
}

const formState = reactive<FormState>({
  password: '',
  newPassword: '',
  confirmNewPassword: ''
})
let formRef = ref()
let validatePass = async (_rule: Rule, value: string) => {
  if (value === '') {
    return Promise.reject(t('message.userSetting.nameTip'))
  } else {
    if (formState.confirmNewPassword !== '') {
      formRef.value?.validateFields('confirmNewPassword')
    }
    return Promise.resolve()
  }
}
let validatePass2 = async (_rule: Rule, value: string) => {
  if (value === '') {
    return Promise.reject(t('message.userSetting.nameTip'))
  } else if (value !== formState.newPassword) {
    return Promise.reject(t('message.theEnteredPasswordIsInconsistent'))
  } else {
    return Promise.resolve()
  }
}
const rules: Record<string, Rule[]> = {
  password: [isRequire()],
  newPassword: [{ required: true, validator: validatePass, trigger: 'change' }],
  confirmNewPassword: [{ required: true, validator: validatePass2, trigger: 'change' }]
}

const visible = ref(false)
const type = ref('changePassword')
const userIds = ref<string[]>([])
const cancel = () => {
  changePassword.setUserIds([])
  changePassword.setVisible(false)
}
const confirm = () => {
  formRef.value?.validate().then(() => {
    if (type.value === 'changePassword') {
      postSysUserPasswordFn()
    } else {
      postSysUserPasswordResetFn()
    }
  })
}

const postSysUserPasswordResetFn = () => {
  const data = {
    newPassword: formState.newPassword,
    loginPassword: formState.password,
    userIds: userIds.value
  }
  postSysUserPasswordReset(data)
    .then(res => {
      getOperateText(t, {
        type: 'reset'
      })
      cancel()
    })
    .catch(err => {
      getOperateText(t, {
        type: 'reset',
        result: 'error',
        reason: err.msg
      })
    })
}
const postSysUserPasswordFn = () => {
  const data = {
    newPassword: formState.newPassword,
    password: formState.password
  }
  postSysUserPassword(data)
    .then(res => {
      getOperateText(t, {
        type: 'update'
      })
      cancel()
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg
      })
    })
}
changePassword.$subscribe((mutation, state) => {
  if (!state.visible) {
    formRef.value?.resetFields()
  }
  visible.value = state.visible
  type.value = state.type
  userIds.value = state.userIds
})
</script>
<style lang="less" scoped>
.change-password {
  padding: 16px;
}
</style>
