<template>
  <MyTable
    ref="tableRef"
    :filterConfigList="filterConfigList"
    :filterData="State.filterData"
    :columns="State.columns"
    :tableOptions="State.tableOptions"
    :pagination="pageState.pagination"
    v-model:selectedRowKeys="State.selectedRowKeys"
    :data="pageState.tableData"
    :is-loading="pageState.isLoading"
    :btnList="btnList"
    class="account-wrap"
    :customRow="customRow"
    @formChange="filterChange">
    <template #status="{ scope }">
      <a-tag :color="scope.record.status === 1 ? 'success' : 'error'">
        {{ t('message.userSetting.enableList.' + scope.record.status) }}
      </a-tag>
    </template>
    <template #updateDate="{ scope }">{{ setTimeStr(scope.record.updateDate) }}</template>
  </MyTable>
  <MyForm
    class="account-form"
    :visible="visible"
    :formConfigList="formConfigList"
    :formData="State.formData"
    @close="onClose"
    @submit="onSubmit">
    <template #autoLogoutTime>
      <div style="position: relative; display: flex">
        <a-input-number v-model:value="State.formData.autoLogoutTime" :controls="false"></a-input-number>
        <div class="form-item-unit">{{ t('message.minute') }}</div>
      </div>
    </template>
  </MyForm>
</template>

<script lang="ts" setup>
import { ITableScope } from 'youibot-plus'
import type { ITableRow, IFilterDataType } from './config'
import usePage from '@/hooks/use-page'
import { useLocale, deepClone } from 'youibot-plus'
import { message } from 'ant-design-vue'
import useStore from '@/stores'
import { isRequire } from '@/utils/form-rules'
import { messageTip, setTimeStr, getOperateText } from '@/utils'
import { getSysUserPage, deleteSysUser, getSysUserId, putSysUser, postSysUser, getSysRoleList } from '@/api/sys'
import { columns, filterConfigList, btnList, tableOptions, formConfigList } from './config'
import { permission } from '@/router'
const emit = defineEmits(['accountFormSubmit'])
const { t } = useLocale()
const { roleStore, changePassword } = useStore()
const visible = ref<boolean>(false)
const tableRef = ref()

const State = reactive({
  columns,
  tableOptions,
  filterData: {
    roleId: null
  } as IFilterDataType,
  formData: {
    autoLogoutTime: 30,
    status: 1
  },
  selectedRowKeys: [] as number[]
})

const { pageState, deleteItemConfirm, changeFilter, getData } = usePage(
  {
    getPageFn: getSysUserPage,
    deleteBatchFn: deleteSysUser
  },
  { pagination: { pageSizeOptions: [15, 30, 50, 100], pageSize: 15 } }
)
const onClose = () => {
  visible.value = false
}

// 筛选条件
const filterChange = (filterData: IFilterDataType, current = 1) => {
  let params: { [key: string]: string } = {}

  type IFilterType = keyof typeof filterData
  if (!Object.keys(filterData).length) {
    roleStore.setRoleId(null)
  }
  for (let item in filterData) {
    if (Array.isArray(filterData[item as IFilterType])) {
      const data = toRaw(filterData[item as IFilterType]) as unknown as string[]
      params[item] = data.join(',')
    } else {
      params[item] = filterData[item as IFilterType] as string
    }
    if (item === 'roleId' && !params[item]) {
      roleStore.setRoleId(null)
    } else if (item === 'roleId' && params[item]) {
      roleStore.setRoleId(params[item] as unknown as number)
    }
  }
  State.filterData = filterData
  changeFilter(params, current)
}

btnList[0].onClick = () => {
  State.formData = { autoLogoutTime: 30, status: 1 }
  formConfigList[0].disabled = false
  formConfigList[2].isHide = false
  formConfigList[3].isHide = false
  formConfigList[2].rules = [isRequire()]
  formConfigList[3].rules = [isRequire()]
  visible.value = true
}
btnList[1].onClick = () => {
  if (State.selectedRowKeys.length > 0) {
    deleteItemConfirm(State.selectedRowKeys, t('message.delTips'))
  } else {
    message.error(t('message.selectTheDataYouWantToDelete'))
  }
}
btnList[2].onClick = () => {
  if (State.selectedRowKeys.length > 0) {
    changePassword.setVisible(false)
    changePassword.setVisible(true)
    changePassword.setType('resetPasswords')
    changePassword.setUserIds(State.selectedRowKeys as unknown as string[])
  } else {
    message.error(t('message.selectTheDataThatYouWantToModify'))
  }
}
//封装右侧操作栏按钮
const operationHandleClick = (type: string, data: ITableScope<ITableRow>) => {
  if (!data) return
  const {
    record: { id }
  } = data
  switch (type) {
    // 编辑
    case 'edit':
      getRoleDetails(id)
      break
  }
}

;(function operationClickHandler() {
  const list = ['edit']
  list.map((item, index) => {
    tableOptions[index].onClick = (text?: object) => operationHandleClick(item, text as ITableScope<ITableRow>)
  })
})()

const getRoleDetails = (id: number) => {
  getSysUserId({}, { id }).then(res => {
    const { data } = res
    State.formData = data
    formConfigList[0].disabled = true
    formConfigList[2].isHide = true
    formConfigList[3].isHide = true
    formConfigList[2].rules = []
    formConfigList[3].rules = []
    visible.value = true
  })
}

const onSubmit = (formData: any) => {
  if (formData.password !== formData.surePassword) {
    message.warning(t('message.passwordInconsistencyTip'))
    let input = document.getElementById('form_item_password')
    input?.focus()
    return
  }
  if (formData.id) {
    putSysUser(formData)
      .then(res => {
        if (res.code == 0) {
          getOperateText(t, {
            type: 'update'
          })
          getData()
          onClose()
          emit('accountFormSubmit')
        }
      })
      .catch(err => {
        getOperateText(t, {
          type: 'update',
          result: 'error',
          reason: err.msg
        })
      })
  } else {
    postSysUser(formData)
      .then(res => {
        if (res.code == 0) {
          getOperateText(t)
          getData()
          onClose()
          emit('accountFormSubmit')
        }
      })
      .catch(err => {
        getOperateText(t, {
          result: 'error',
          reason: err.msg
        })
      })
  }
}
const customRow = (record: ITableRow) => {
  return {
    onDblclick: () => {
      if (permission.includes('/operations/setting/user-settings/user/edit')) {
        operationHandleClick('edit', { record: record } as ITableScope<ITableRow>)
      }
    }
  }
}
async function getRoleList() {
  getSysRoleList().then(res => {
    const { data } = res
    filterConfigList[2].options = data
    formConfigList[4].options = data
    //要通知filter改变
    tableRef.value.generateNewConfigList()
  })
}
roleStore.$subscribe((mutation, state) => {
  const activeKey = state.activeKey
  if (activeKey == '1') {
    State.filterData.roleId = state.roleId
    changeFilter({ roleId: state.roleId })
    getRoleList()
  }
})

onMounted(() => {
  roleStore.setActiveKey('1')
  getData()
})
onBeforeUnmount(() => {
  roleStore.setRoleId(null)
})
</script>
<style lang="less">
.yi-filter-table.account-wrap {
  .yi-filter {
    padding-top: 0;

    .yi-filter-btn {
      top: 0;
    }
  }
}

.account-form {
  .form-item-unit {
    position: absolute;
    top: 1px;
    right: 10px;
    height: 30px;
    line-height: 30px;
    background-color: #fff;
  }
}
</style>
