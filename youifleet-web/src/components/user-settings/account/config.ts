import { IBtnType, IFormItemType, RANGEPICKER } from 'youibot-plus'
import type { TableColumnsType } from 'ant-design-vue'
import { INPUT, SELECT, INPUTPASSWORD } from 'youibot-plus'
import { isRequire, isPhone, isLength } from '@/utils/form-rules'

export interface ITableRow {
  autoLogoutTime: number
  createDate: string
  email: string
  mobile: string
  id: number
  password: string
  surePassword: string
  realName: string
  roleIdList: string[]
  roleNames: string
  status: number
  username: string
}
export interface IFilterDataType {
  roleId: number | null
  username?: string
  realName?: string
  email?: string
  mobile?: string
  status?: string
  updateDate?: Array<string>
}
const tableOptions: IBtnType[] = [
  {
    name: 'message.edit',
    type: 'link',
    permission: 'user/edit'
  }
]

//table 配置
const columns: TableColumnsType = [
  {
    title: 'message.userSetting.account',
    dataIndex: 'username',
    width: 230
  },
  {
    title: 'message.userSetting.name',
    dataIndex: 'realName',
    width: 230
  },
  {
    title: 'message.userSetting.role',
    dataIndex: 'roleNames',
    width: 200,
    sorter: false,
    ellipsis: true
  },
  {
    title: 'message.userSetting.email',
    dataIndex: 'email',
    width: 200
  },
  {
    title: 'message.userSetting.phone',
    dataIndex: 'mobile',
    width: 200
  },
  {
    title: 'message.userSetting.enable',
    key: 'status',
    width: 120
  },
  {
    title: 'message.userSetting.updateDate',
    key: 'updateDate',
    width: 200
  },
  {
    title: 'message.userSetting.automaticLogout',
    dataIndex: 'autoLogoutTime',
    width: 160
  },
  {
    title: 'message.options',
    key: 'options',
    fixed: 'right'
  }
]

// 筛选条件配置
const filterConfigList: IFormItemType[] = [
  {
    is: INPUT,
    name: 'username',
    label: 'message.userSetting.account',
    allowClear: true
  },
  {
    is: INPUT,
    name: 'realName',
    label: 'message.userSetting.name',
    allowClear: true
  },
  {
    is: SELECT,
    name: 'roleId',
    label: 'message.userSetting.role',
    allowClear: true,
    fieldNames: { label: 'name', value: 'id' },
    options: [],
    optionFilterProp: 'name'
  },
  {
    is: INPUT,
    name: 'email',
    label: 'message.userSetting.email',
    allowClear: true
  },
  {
    is: INPUT,
    name: 'mobile',
    label: 'message.userSetting.phone',
    allowClear: true
  },
  {
    is: SELECT,
    name: 'status',
    label: 'message.userSetting.enable',
    allowClear: true,
    isTranslated: true,
    options: [
      { label: 'message.userSetting.enableList.0', value: 0 },
      { label: 'message.userSetting.enableList.1', value: 1 }
    ]
  },
  {
    is: RANGEPICKER,
    name: 'updateDate',
    allowClear: true,
    label: 'message.userSetting.updateDate',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  }
]

//底部按钮
const btnList: IBtnType[] = [
  {
    name: 'message.add',
    type: 'primary',
    icon: 'iconfont icon-zengjia',
    permission: 'user/add'
  },
  {
    name: 'message.del',
    icon: 'iconfont icon-xiazai14',
    permission: 'user/del'
  },
  {
    name: 'message.changePassword.resetPassword',
    icon: 'iconfont icon-zhongzhi',
    permission: 'user/resetPasswords'
  }
]

const formConfigList: IFormItemType[] = [
  {
    is: INPUT,
    name: 'username',
    label: 'message.userSetting.account',
    rules: [{ required: true, pattern: /^[a-zA-Z][a-zA-Z0-9_]{0,19}$/, message: 'message.userSetting.username' }] //长度限制20个字符;英文开头;支持英文、下划线、数字
  },
  {
    is: INPUT,
    name: 'realName',
    label: 'message.userSetting.name',
    rules: [isRequire(), isLength(0, 20, 'message.userSetting.realName')]
  },
  {
    is: INPUTPASSWORD,
    name: 'password',
    label: 'message.userSetting.password',
    rules: [isRequire()]
  },
  {
    is: INPUTPASSWORD,
    name: 'surePassword',
    label: 'message.userSetting.surePassword',
    rules: [isRequire()]
  },

  {
    is: SELECT,
    name: 'roleIdList',
    label: 'message.userSetting.role',
    mode: 'multiple',
    fieldNames: { label: 'name', value: 'id' },
    optionFilterProp: 'name',
    allowClear: true,
    options: []
  },
  {
    is: INPUT,
    name: 'email',
    label: 'message.userSetting.email'
  },
  {
    is: INPUT,
    name: 'mobile',
    label: 'message.userSetting.phone',
    rules: [isPhone()]
  },
  {
    slotName: 'autoLogoutTime',
    name: 'autoLogoutTime',
    is: '',
    label: 'message.userSetting.automaticLogout',
    min: 0,
    max: 9999,
    rules: [isRequire(), { type: 'number', min: 0, max: 9999, message: 'message.userSetting.autoLogoutTime' }]
  },
  // {
  //   is: INPUTNUMBER,
  //   name: 'autoLogoutTime',
  //   label: 'message.userSetting.automaticLogout',
  //   min: 0,
  //   controls: false,
  //   max: 9999,
  //   rules: [isRequire(), { type: 'number', min: 0, max: 9999, message: 'message.userSetting.autoLogoutTime' }]
  // },
  {
    is: SELECT,
    name: 'status',
    label: 'message.userSetting.enable',
    isTranslated: true,
    rules: [isRequire()],
    options: [
      { label: 'message.userSetting.enableList.0', value: 0 },
      { label: 'message.userSetting.enableList.1', value: 1 }
    ]
  }
]
export { columns, filterConfigList, tableOptions, btnList, formConfigList }
