<template>
  <my-modal
    class="assign-map"
    :title="t('message.robotManage.assignMap')"
    :closable="true"
    :visible="props.visible"
    :width="props.width"
    :z-index="props.zIndex"
    @cancel="onClose"
    @confirm="submitData">
    <a-row :gutter="[16, 16]">
      <a-col :span="8" v-for="item in State.mapList" :key="item.code">
        <div class="assign-map-item" :class="{ active: isActive(item) }" @click="selectMap(item)">
          {{ item.name }}
        </div>
      </a-col>
    </a-row>
  </my-modal>
</template>

<script lang="ts" setup>
import { useLocale } from 'youibot-plus'
import { getMapVehicleMaps } from '@/api/map'
import type { VehicleMapDTO } from '@/api/interface'
const props = defineProps({
  visible: Boolean,
  formData: Object,
  formKey: {
    type: String,
    default: 'id'
  },
  width: {
    type: String,
    default: '535px'
  },
  zIndex: {
    type: Number,
    default: 2001
  },
  formConfigList: Array
})
const { t } = useLocale()
const emit = defineEmits(['close', 'submit'])
const State = reactive({
  mapList: [] as VehicleMapDTO[],
  activeItem: ''
})

const onClose = () => {
  emit('close')
}

const submitData = () => {
  emit('submit', State.activeItem)
}

const getList = () => {
  getMapVehicleMaps().then(res => {
    const { data } = res
    State.mapList = (data as VehicleMapDTO[]).filter(item => item.isProd)
  })
}
const selectMap = (map: VehicleMapDTO) => {
  if (map.code) {
    State.activeItem = map.code
  }
}

const isActive = (map: VehicleMapDTO) => {
  return State.activeItem == map.code
}
getList()
</script>
<style lang="less">
.assign-map.ant-modal {
  .assign-map-item {
    height: 32px;
    padding: 0 12px;
    overflow: hidden;
    line-height: 32px;
    white-space: nowrap;
    text-align: center;
    text-overflow: ellipsis;
    border: 1px solid #9f9f9f;
    border-radius: 2px;

    &.active,
    &:hover {
      color: #fff;
      background-color: @primary-color;
      border-color: @primary-color;
    }
  }
}
</style>
