<template>
  <div class="map-editor-wrap">
    <div class="map-editor-wrap-title">{{ t('message.map.map') }}</div>
    <ul>
      <li>
        <div class="form-label">{{ t('message.map.displayElement') }}</div>

        <!-- 元素显示 -->
        <div>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 单机区域 -->
              <a-checkbox
                v-model:checked="formState.isShowSingleAgvArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.SingleAgvArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 封控区域 -->
              <a-checkbox
                v-model:checked="formState.isShowControlArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.ControlArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 显示区域 -->
              <a-checkbox
                v-model:checked="formState.isShowShowArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.ShowArea') }}
              </a-checkbox>
            </a-col>
          </a-row>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 通道区域 -->
              <a-checkbox
                v-model:checked="formState.isShowChannelArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.ChannelArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 禁旋区域 -->
              <a-checkbox
                v-model:checked="formState.isShowNoRotatingArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.NoRotatingArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 禁停区域 -->
              <a-checkbox
                v-model:checked="formState.isShowNoParkingArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.NoParkingArea') }}
              </a-checkbox>
            </a-col>
          </a-row>

          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 交管区域 -->
              <a-checkbox
                v-model:checked="formState.isShowTrafficArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.TrafficArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 第三方交管区域 -->
              <a-checkbox
                v-model:checked="formState.isShowThirdSystemTrafficArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.ThirdSystemTrafficArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 禁入区域 -->
              <a-checkbox
                v-model:checked="formState.isShowForbiddenArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.ForbiddenArea') }}
              </a-checkbox>
            </a-col>
          </a-row>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 导航点 -->
              <a-checkbox
                v-model:checked="formState.isShowNavigationMarker"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.NavigationMarker') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 工作点 -->
              <a-checkbox
                v-model:checked="formState.isShowWorkMarker"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.WorkMarker') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 充电点 -->
              <a-checkbox
                v-model:checked="formState.isShowChargingMarker"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.ChargingMarker') }}
              </a-checkbox>
            </a-col>

            <!-- <a-col :span="7"><a-checkbox v-model:checked="formState.checked" @change="setLocalData">路径权重</a-checkbox></a-col> -->
          </a-row>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 泊车标识 -->
              <a-checkbox
                v-model:checked="formState.isPark"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.parkingSign') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 路径箭头 -->
              <a-checkbox
                v-model:checked="formState.isShowPathArrow"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.pathDirection') }}
              </a-checkbox>
            </a-col>
            <!-- <a-col :span="7"><a-checkbox v-model:checked="formState.checked" @change="setLocalData">路径权重</a-checkbox></a-col> -->
          </a-row>
        </div>
      </li>
      <a-divider class="map-editor-divider" />
      <li>
        <!-- 文字显示 -->
        <div class="form-label">{{ t('message.map.displayText') }}</div>

        <div>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 是否显示点位编码 -->
              <a-checkbox
                v-model:checked="formState.isMarkerCode"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.pointCoding') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 是否显示自定义编码 -->
              <a-checkbox
                v-model:checked="formState.isCustomCoding"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.customCoding') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 是否显示区域名称 -->
              <a-checkbox
                v-model:checked="formState.isShowAreaText"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.areaName') }}
              </a-checkbox>
            </a-col>
          </a-row>
        </div>
      </li>
      <a-divider class="map-editor-divider" />
      <li>
        <!-- 元素大小设置 -->
        <div class="form-label">{{ t('message.map.elementSize') }}</div>
        <div class="checkbox-group">
          <a-checkbox
            v-model:checked="formState.isForceChangeElementSize"
            @change="
              () => {
                setLocalData('isForceChangeElementSize')
              }
            ">
            {{ t('message.map.forceTheElementSizeToChange') }}
          </a-checkbox>
        </div>
        <div v-if="formState.isForceChangeElementSize">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item :label="t('message.map.pointPosition')" :labelCol="{ span: 24 }" style="margin-bottom: 0">
                <a-input-number
                  id="inputNumber"
                  style="width: 160px"
                  v-model:value="formState.markerHeght"
                  :min="0.01"
                  :max="100"
                  :controls="false"
                  @change="
                    () => {
                      setLocalData()
                    }
                  "
                  @keydown="stopPropagationFn" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :label="t('message.map.path')" :labelCol="{ span: 24 }" style="margin-bottom: 0">
                <a-input-number
                  id="inputNumber"
                  style="width: 160px"
                  v-model:value="formState.pathWdth"
                  :min="0.01"
                  :max="100"
                  :controls="false"
                  @change="
                    () => {
                      setLocalData()
                    }
                  "
                  @keydown="stopPropagationFn" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </li>

      <a-divider class="map-editor-divider" />
      <li>
        <!-- 背景设置 -->
        <div class="form-label">{{ t('message.map.backgroundSettings') }}</div>

        <div>
          <div class="back-color checkbox-group">
            <a-input
              type="color"
              v-model:value="formState.backgroundColor"
              style="width: 16px; height: 16px; padding: 0"
              @change="
                () => {
                  setLocalData()
                }
              "></a-input>
            <span class="back-color-describe">{{ formState.backgroundColor }}</span>
            <span class="back-color-label">{{ t('message.map.backgroundColor') }}</span>
          </div>
          <div>
            <!-- 显示背景图 -->
            <a-checkbox
              v-model:checked="formState.isMapimage"
              @change="
                () => {
                  setLocalData()
                }
              ">
              {{ t('message.map.showBackground') }}
            </a-checkbox>
          </div>
          <!--显示PNG图边框  -->
          <div>
            <a-checkbox
              v-model:checked="formState.isBorder"
              @change="
                () => {
                  setLocalData()
                }
              ">
              {{ t('message.map.displaysThePngDiagramBorder') }}
            </a-checkbox>
          </div>
        </div>
      </li>
      <a-divider class="map-editor-divider" />
      <li>
        <!-- 编辑参数 -->
        <div class="form-label">{{ t('message.map.editParameter') }}</div>

        <div>
          <a-row class="operation-habit checkbox-group">
            <template v-for="(item, index) in operateFormState">
              <!-- 限制点位与点位的距离 -->

              <a-col span="11" v-if="item.propertyKey === 'markerSpacingCheck'">
                <a-checkbox
                  v-model:checked="item.propertyValue"
                  @change="
                    () => {
                      putSysPropertyFn(item)
                    }
                  ">
                  {{ t('message.map.limitTheDistanceBetweenPointAndPoint') }}
                </a-checkbox>
              </a-col>
              <a-col span="13" class="operation-habit-input" v-if="item.propertyKey === 'markerSpacing'">
                <a-input-number
                  v-model:value="item.propertyValue"
                  style="width: 102px"
                  :controls="false"
                  :max="9999"
                  :min="0.1"
                  @blur="
                    () => {
                      putSysPropertyFn(item)
                    }
                  "></a-input-number>
                <span class="suffix">m</span>
              </a-col>
              <!-- 限制点位与路径的距离 -->

              <!-- <a-col span="11" style="margin-top: 6px" v-if="item.propertyKey === 'markerAndPathSpacingCheck'">
                <a-checkbox
                  v-model:checked="item.propertyValue"
                  @change="
                    () => {
                      putSysPropertyFn(item)
                    }
                  ">
                  {{ t('message.map.limitTheDistanceBetweenPointsAndPaths') }}
                </a-checkbox>
              </a-col>
              <a-col span="13" style="margin-top: 6px" class="operation-habit-input" v-if="item.propertyKey === 'markerAndPathSpacing'">
                <a-input-number
                  v-model:value="item.propertyValue"
                  style="width: 102px"
                  :controls="false"
                  :min="0"
                  @blur="
                    () => {
                      putSysPropertyFn(item)
                    }
                  "></a-input-number>
                <span class="suffix">mm</span>
              </a-col> -->
            </template>
          </a-row>
        </div>
      </li>
      <a-divider class="map-editor-divider" />
      <li>
        <!-- 背景设置 -->
        <div class="form-label">{{ t('message.map.operationHabit') }}</div>
        <div>
          <div>
            <!--双击创建元素 -->
            <a-checkbox
              v-model:checked="formState.isDoubleClick"
              @change="
                () => {
                  setLocalData()
                }
              ">
              {{ t('message.map.doubleClickCreateElement') }}
            </a-checkbox>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
import { mapEditDisplaySetupLocalData } from '@/utils/storage'
import useStore from '@/stores'
import { getSysPropertyGetSystemConfig, putSysPropertyId } from '@/api/sys'
import { debounce, getOperateText, messageTip, stopPropagationFn } from '@/utils'

import { markerHConfig, pathLineWConfig, useLocale } from 'youibot-plus'
import { EditMapForm, getEditMapForm } from '@/pages/operations/maps/map-edit/config'
const { t } = useLocale()

interface OperateFormState {
  id: number
  category: string
  type: string
  propertyKey: string
  title: string
  propertyValue: string | boolean
  valueType: string
}
const formState = ref<EditMapForm>(getEditMapForm())
const operateFormState = ref<OperateFormState[]>([])
const handleSettings = () => {
  mapDisplaySetupData.setMapDisplaySetupData(formState.value)
}
// 防抖
const nameTest = debounce(handleSettings, 100)
const setLocalData = (type?: string) => {
  if (type === 'isForceChangeElementSize') {
    if (formState.value.isForceChangeElementSize) {
      if (!formState.value.markerHeght) {
        formState.value.markerHeght = markerHConfig.maxHeight
      }
      if (!formState.value.pathWdth) {
        formState.value.pathWdth = pathLineWConfig.maxLineWidth
      }
    }
  }
  nameTest()
  mapEditDisplaySetupLocalData(JSON.stringify(formState.value))
}
const getLocalData = () => {
  const data = mapEditDisplaySetupLocalData()
  if (data) {
    formState.value = JSON.parse(data)
  }
}
const { mapDisplaySetupData } = useStore()

getLocalData()
const getSysPropertyGetSystemConfigFn = () => {
  getSysPropertyGetSystemConfig({ category: 'map' }).then(res => {
    res.data.forEach((item: OperateFormState) => {
      if (['markerSpacingCheck', 'markerAndPathSpacingCheck'].includes(item.propertyKey)) {
        item.propertyValue = item.propertyValue === '1' ? true : false
      }
      operateFormState.value.push(item)
    })
  })
}
const putSysPropertyFn = (item: OperateFormState) => {
  const data = { ...item }

  if (['markerSpacingCheck', 'markerAndPathSpacingCheck'].includes(item.propertyKey)) {
    data.propertyValue = item.propertyValue ? '1' : '0'
  }
  putSysPropertyId({ ...data, propertyValue: data.propertyValue as string }, { id: item.id })
    .then(res => {
      getOperateText(t, {
        type: 'update'
      })
    })
    .catch(err => {
      getOperateText(t, {
        type: 'update',
        result: 'error',
        reason: err.msg
      })
    })
}
getSysPropertyGetSystemConfigFn()
</script>
<style lang="less">
.map-editor-select {
  z-index: 3001;
}

.map-editor-divider {
  margin: 16px 0;
  background-color: #e8e8e8;
}
</style>
<style lang="less" scoped>
.map-editor-wrap {
  min-height: 690px;

  &-title {
    margin-bottom: 6px;
    color: #000;
    font-size: 16px;
  }

  ul {
    .form-label {
      margin-bottom: 5px;
      color: #848484;
    }

    li:last-child {
      margin-bottom: 15px;
    }

    .checkbox-group {
      margin-bottom: 8px;
    }

    .back-color {
      display: flex;
      align-items: center;

      &-describe {
        margin-left: 8px;
      }

      &-label {
        margin-left: 29px;
      }
    }

    .operation-habit {
      display: flex;
      align-items: center;
      padding-bottom: 10px;

      &-label {
        margin-right: 29px;
        color: #000;
      }

      &-input {
        position: relative;

        .suffix {
          position: absolute;
          top: 1px;
          left: 65px;
          width: 36px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          background-color: #fff;
        }
      }
    }
  }
}
</style>
