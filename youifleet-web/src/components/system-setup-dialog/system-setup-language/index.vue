<template>
  <div class="language-wrap">
    <div class="language-wrap-title">语言</div>
    <ul>
      <li>
        <div class="form-label">系统语言</div>

        <div>
          <a-select ref="select" popupClassName="language-select" v-model:value="language" style="width: 297px">
            <a-select-option value="jack">Jack</a-select-option>
            <a-select-option value="lucy">Lucy</a-select-option>
            <a-select-option value="disabled" disabled>Disabled</a-select-option>
            <a-select-option value="Yiminghe">yiminghe</a-select-option>
          </a-select>
        </div>
      </li>
      <a-divider class="language-divider" style="background-color: #e8e8e8" />
      <li>
        <div class="form-label">语言设置</div>

        <div>
          <ul class="form-language-settings">
            <li>
              <div class="form-language-settings-label">中文 Chinese</div>
              <div>
                <my-button
                  :btnItem="{
                    name: '删除',
                    type: 'text',
                    icon: 'icon-shanchu iconfont'
                  }"></my-button>
                <my-button
                  :btnItem="{
                    name: '下载',
                    type: 'text',
                    icon: 'icon-daochu iconfont'
                  }"></my-button>
              </div>
            </li>
            <li>
              <div class="form-language-settings-label">中文 Chinese</div>
              <div>
                <my-button
                  :btnItem="{
                    name: '删除',
                    type: 'text',
                    icon: 'icon-shanchu iconfont'
                  }"></my-button>
                <my-button
                  :btnItem="{
                    name: '下载',
                    type: 'text',
                    icon: 'icon-daochu iconfont'
                  }"></my-button>
              </div>
            </li>
          </ul>
        </div>
      </li>
      <li>
        <my-button
          class="uploading-but"
          :btnItem="{
            name: '上传新语言',
            icon: 'icon-daochu iconfont',
            onClick: () => {
              fileDialogVisible = true
            }
          }"></my-button>
      </li>
    </ul>
  </div>
  <my-import-file-dialog
    v-model:visible="fileDialogVisible"
    :title="'上传新语言'"
    :describeFileName="'文件'"
    :action="apiUrl + '/fleet/license/valiadateLicense'"
    :z-index="3002"
    @success="importSuccess"></my-import-file-dialog>
</template>
<script lang="ts" setup>
const language = ref()
const emit = defineEmits(['refresh'])
const fileDialogVisible = ref<boolean>(false)
const apiUrl = `//${window.apiUrl}`
const importSuccess = () => {
  emit('refresh')
}
</script>
<style lang="less">
.language-select {
  z-index: 3001;
}

.language-divider {
  margin: 16px 0;
}
</style>
<style lang="less" scoped>
.language-wrap {
  &-title {
    margin-bottom: 6px;
    color: #000;
    font-size: 16px;
  }

  .form-label {
    margin-bottom: 5px;
    color: #848484;
  }

  .form-language-settings {
    li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
      :deep(.ant-btn) {
        height: 22px;
        margin: 0 !important;
        padding: 0 10px;
        color: #848484;
        line-height: initial;
      }

      .form-language-settings-label {
        color: #000;
      }
    }
  }

  .uploading-but {
    margin-top: 10px;
    padding: 0 15px;
  }
}
</style>
