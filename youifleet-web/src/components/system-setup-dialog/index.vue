<template>
  <my-modal
    v-model:visible="visible"
    class="system-setup-dialog"
    :isTitle="false"
    :width="'658px'"
    :zIndex="3000"
    :modalFooter="false"
    :closable="false"
    @cancel="cancel">
    <div class="system-setup-dialog-wrap">
      <div class="system-setup-dialog-wrap-left">
        <div class="system-setup-dialog-wrap-left-title">
          <i class="iconfont icon-a-shezhi_jiankongfuben20_jiankongfuben23"></i>
          {{ t('message.individuation') }}
        </div>
        <ul class="system-setup-dialog-wrap-left-list">
          <li
            v-for="(item, index) in menuList"
            :class="{ 'system-setup-dialog-wrap-left-list-li-active': menuActive === item.type }"
            :key="index"
            @click="menuClick(item.type)">
            {{ item.name }}
          </li>
        </ul>
      </div>
      <div class="system-setup-dialog-wrap-right">
        <div class="system-setup-dialog-wrap-right-close" @click="cancel"><i class="iconfont icon-cha"></i></div>
        <div class="system-setup-dialog-wrap-right-inner">
          <system-setup-language v-if="menuActive === 1"></system-setup-language>
          <system-setup-authorization v-else-if="menuActive === 2"></system-setup-authorization>
          <system-setup-map-editor v-else-if="menuActive === 3"></system-setup-map-editor>
          <system-setup-monitoring v-else-if="menuActive === 4"></system-setup-monitoring>
        </div>
      </div>
    </div>
  </my-modal>
</template>
<script lang="ts" setup>
import useStore from '@/stores'
import { getRoute } from '@/router'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const { settings } = useStore()
const visible = ref<boolean>(true)
const menuActive = ref<number>(3)
watchEffect(() => {
  visible.value = settings.getVisible
  const isMap = getRoute().path === '/mapEdit'
  const isAgvMap = getRoute().path === '/monitoring/agv-map'
  if (settings.getSelectedMenu !== null) {
    menuActive.value = settings.getSelectedMenu
  } else {
    menuActive.value = isMap ? 3 : isAgvMap ? 4 : 3
  }
})

const menuList = ref([
  // {
  //   name: '语言',
  //   type: 1
  // },
  // {
  //   name: '许可证',
  //   type: 2
  // },
  {
    name: t('message.map.mapEditor'),
    type: 3
  },
  {
    name: t('message.map.monitoring'),
    type: 4
  }
])
const cancel = () => {
  settings.setVisible(false)
  settings.setSelectedMenu(null)
}
const menuClick = (type: number) => {
  menuActive.value = type
}
</script>
<style lang="less">
.system-setup-dialog {
  .my-modal-body {
    padding: 0;
  }
}
</style>
<style lang="less" scoped>
.system-setup-dialog {
  /* stylelint-disable-next-line selector-pseudo-class-no-unknown */

  &-wrap {
    display: flex;
    min-height: 591px;

    &-left {
      width: 178px;
      padding: 7px 8px;
      background-color: #fafafa;

      &-title {
        padding-left: 12px;
        color: #000;
        font-size: 14px;
      }

      &-list {
        margin-top: 9px;

        &-li-active {
          background-color: #ffefe5;
        }

        li {
          height: 30px;
          padding-left: 12px;
          color: #000;
          line-height: 30px;
          cursor: pointer;

          &:hover {
            background-color: #ffefe5;
          }
        }
      }
    }

    &-right {
      position: relative;
      width: 480px;

      &-close {
        position: absolute;
        top: 12px;
        right: 25px;
        font-size: 14px;
      }

      &-inner {
        height: 690px;
        margin-top: 32px;
        padding: 0 24px;
        overflow-y: auto;
      }
    }
  }
}
</style>
