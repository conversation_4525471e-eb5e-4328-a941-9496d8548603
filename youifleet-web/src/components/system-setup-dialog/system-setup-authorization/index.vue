<template>
  <div class="authorization-wrap">
    <div class="authorization-wrap-title">许可证</div>
    <ul>
      <li>
        <div class="form-label">版本号</div>

        <div class="form-value">{{ authorizationData[0]?.propertyValue }}</div>
      </li>
      <a-divider class="authorization-divider" style="background-color: #e8e8e8" />
      <li>
        <div class="form-label">版权所有</div>
        <div class="form-value">{{ authorizationData[1]?.propertyValue }}</div>
      </li>
      <a-divider class="authorization-divider" style="background-color: #e8e8e8" />
      <li>
        <div class="form-label">许可证信息</div>
        <div class="form-authpriv">
          <div class="form-authpriv-label">公司</div>
          <div class="form-authpriv-value">{{ authorizationData[2]?.propertyValue }}</div>
        </div>
        <div class="form-authpriv">
          <div class="form-authpriv-label">时间</div>
          <div class="form-authpriv-value">{{ authorizationData[3]?.propertyValue }}</div>
        </div>
      </li>
      <li class="operation">
        <my-button
          class="uploading-but"
          :btnItem="{
            name: '更新许可证',
            icon: 'icon-daochu iconfont',
            onClick: () => {
              fileDialogVisible = true
            }
          }"></my-button>
        <my-button
          class="uploading-but"
          :btnItem="{
            name: '删除许可证',
            icon: 'icon-daochu iconfont',
            onClick: removeAuthorization
          }"></my-button>
      </li>
    </ul>
  </div>
  <my-import-file-dialog
    v-model:visible="fileDialogVisible"
    :title="'上传许可证'"
    :describeFileName="'文件'"
    :action="apiUrl + '/fleet/license/valiadateLicense'"
    :z-index="3002"
    @success="importSuccess"></my-import-file-dialog>
</template>
<script lang="ts" setup>
import { deleteLicense } from '@/api/license'
import { getSysPropertyGetSystemConfig } from '@/api/sys'
import { getOperateText, messageTip } from '@/utils'
import { Modal } from 'ant-design-vue'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
interface AuthorizationData {
  title: string
  propertyValue: string
  endTime: string
  fileId: string
  id: string
  serverType: string
  startTime: string
  type: string
  updateDate: string
}

const authorizationData = ref<AuthorizationData[]>([])
const fileDialogVisible = ref<boolean>(false)
const apiUrl = `//${window.apiUrl}`
const getData = () => {
  getSysPropertyGetSystemConfig({ category: 'info' }).then(res => {
    authorizationData.value = res.data
  })
}
const removeAuthorization = () => {
  Modal.confirm({
    content: `${t('message.deleteOrNot')}？${t('message.messageCannotReturn')}`,
    zIndex: 3002,
    onOk: () => {
      deleteLicense()
        .then(res => {
          getOperateText(t, {
            type: 'delete'
          })
          getData()
        })
        .catch(err => {
          getOperateText(t, {
            type: 'delete',
            result: 'error',
            reason: err.msg
          })
        })
    }
  })
}
getData()
const importSuccess = () => {
  getData()
}
</script>
<style lang="less">
.authorization-select {
  z-index: 3001;
}

.authorization-divider {
  margin: 16px 0;
}
</style>
<style lang="less" scoped>
.authorization-wrap {
  &-title {
    margin-bottom: 6px;
    color: #000;
    font-size: 16px;
  }

  .form-label {
    margin-bottom: 5px;
    color: #848484;
  }

  .form-value {
    width: 295px;
    height: 32px;
    padding: 0 12px;
    color: #000;
    line-height: 32px;
    background-color: #f8f8f8;
  }

  .form-authpriv {
    display: flex;
    align-items: center;

    &-label {
      margin-right: 33px;
      color: #505050;
    }

    &-value {
      width: 285px;
      height: 32px;
      margin-bottom: 18px;
      padding: 0 12px;
      color: #000;
      line-height: 32px;
      background-color: #f8f8f8;
    }
  }

  .uploading-but {
    margin-top: 10px;
    padding: 0 15px;
  }

  .operation {
    padding-right: 84px;
    text-align: right;
  }
}
</style>
