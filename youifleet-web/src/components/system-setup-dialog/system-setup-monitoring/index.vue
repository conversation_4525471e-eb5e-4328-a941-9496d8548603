<template>
  <div class="map-editor-wrap">
    <div class="map-editor-wrap-title">{{ t('message.map.map') }}</div>
    <ul>
      <li>
        <div class="form-label">{{ t('message.map.displayElement') }}</div>
        <!-- 元素显示 -->
        <div>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 单机区域 -->
              <a-checkbox v-model:checked="formState.isShowSingleAgvArea" @change="setLocalData">
                {{ t('message.map.SingleAgvArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 封控区域 -->
              <a-checkbox v-model:checked="formState.isShowControlArea" @change="setLocalData">
                {{ t('message.map.ControlArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 显示区域 -->
              <a-checkbox v-model:checked="formState.isShowShowArea" @change="setLocalData">{{ t('message.map.ShowArea') }}</a-checkbox>
            </a-col>
          </a-row>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 通道区域 -->
              <a-checkbox v-model:checked="formState.isShowChannelArea" @change="setLocalData">
                {{ t('message.map.ChannelArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 禁旋区域 -->
              <a-checkbox v-model:checked="formState.isShowNoRotatingArea" @change="setLocalData">
                {{ t('message.map.NoRotatingArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 禁停区域 -->
              <a-checkbox v-model:checked="formState.isShowNoParkingArea" @change="setLocalData">
                {{ t('message.map.NoParkingArea') }}
              </a-checkbox>
            </a-col>
          </a-row>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 交管区域 -->
              <a-checkbox
                v-model:checked="formState.isShowTrafficArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.TrafficArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 第三方交管区域 -->
              <a-checkbox
                v-model:checked="formState.isShowThirdSystemTrafficArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.ThirdSystemTrafficArea') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 禁入区域 -->
              <a-checkbox
                v-model:checked="formState.isShowForbiddenArea"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.ForbiddenArea') }}
              </a-checkbox>
            </a-col>
          </a-row>

          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 导航点 -->
              <a-checkbox v-model:checked="formState.isShowNavigationMarker" @change="setLocalData">
                {{ t('message.NavigationMarker') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 工作点 -->
              <a-checkbox v-model:checked="formState.isShowWorkMarker" @change="setLocalData">{{ t('message.WorkMarker') }}</a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 充电点 -->
              <a-checkbox v-model:checked="formState.isShowChargingMarker" @change="setLocalData">
                {{ t('message.ChargingMarker') }}
              </a-checkbox>
            </a-col>
          </a-row>
          <a-row class="checkbox-group">
            <a-col :span="8">
              <!-- 泊车标识 -->
              <a-checkbox
                v-model:checked="formState.isPark"
                @change="
                  () => {
                    setLocalData()
                  }
                ">
                {{ t('message.map.parkingSign') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 路径箭头 -->
              <a-checkbox v-model:checked="formState.isShowPathArrow" @change="setLocalData">
                {{ t('message.map.pathDirection') }}
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <!-- 库位信息 -->
              <a-checkbox v-model:checked="formState.isWarehouse" @change="setLocalData">
                {{ t('message.warehouse') }}
              </a-checkbox>
            </a-col>
          </a-row>
        </div>
      </li>
      <a-divider class="map-editor-divider" />
      <li>
        <!-- 文字显示 -->
        <div class="form-label">{{ t('message.map.displayText') }}</div>

        <div>
          <a-row class="checkbox-group">
            <!-- 是否显示点位编码 -->
            <a-col :span="8">
              <a-checkbox v-model:checked="formState.isMarkerCode" @change="setLocalData">{{ t('message.map.pointCoding') }}</a-checkbox>
            </a-col>
            <!-- 是否显示自定义编码 -->
            <a-col :span="8">
              <a-checkbox v-model:checked="formState.isCustomCoding" @change="setLocalData">{{ t('message.map.customCoding') }}</a-checkbox>
            </a-col>
            <!-- 是否显示区域名称 -->
            <a-col :span="8">
              <a-checkbox v-model:checked="formState.isShowAreaText" @change="setLocalData">{{ t('message.map.areaName') }}</a-checkbox>
            </a-col>
          </a-row>
        </div>
      </li>
      <a-divider class="map-editor-divider" />
      <li>
        <!-- 元素大小设置 -->
        <div class="form-label">{{ t('message.map.elementSize') }}</div>
        <div class="checkbox-group">
          <a-checkbox v-model:checked="editMapForm.isForceChangeElementSize" @change="setEditMapLocalData('isForceChangeElementSize')">
            {{ t('message.map.forceTheElementSizeToChange') }}
          </a-checkbox>
        </div>
        <div v-if="editMapForm.isForceChangeElementSize">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item :label="t('message.map.pointPosition')" :labelCol="{ span: 24 }" style="margin-bottom: 0">
                <a-input-number
                  id="inputNumber"
                  style="width: 160px"
                  v-model:value="editMapForm.markerHeght"
                  :min="0.01"
                  :max="100"
                  :controls="false"
                  @change="() => setEditMapLocalData()"
                  @keydown="stopPropagationFn" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :label="t('message.map.path')" :labelCol="{ span: 24 }" style="margin-bottom: 0">
                <a-input-number
                  id="inputNumber"
                  style="width: 160px"
                  v-model:value="editMapForm.pathWdth"
                  :min="0.01"
                  :max="100"
                  :controls="false"
                  @change="() => setEditMapLocalData()"
                  @keydown="stopPropagationFn" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </li>
      <a-divider class="map-editor-divider" />
      <li>
        <!-- 背景设置 -->
        <div class="form-label">{{ t('message.map.backgroundSettings') }}</div>

        <div>
          <div class="back-color checkbox-group">
            <a-input
              type="color"
              v-model:value="formState.backgroundColor"
              style="width: 16px; height: 16px; padding: 0"
              @change="setLocalData"></a-input>
            <span class="back-color-describe">{{ formState.backgroundColor }}</span>
            <span class="back-color-label">{{ t('message.map.backgroundColor') }}</span>
          </div>
          <!-- 显示背景图 -->
          <div>
            <a-checkbox v-model:checked="formState.isMapimage" @change="setLocalData">{{ t('message.map.showBackground') }}</a-checkbox>
          </div>
          <div>
            <!--显示PNG图边框  -->
            <a-checkbox v-model:checked="formState.isBorder" @change="setLocalData">
              {{ t('message.map.displaysThePngDiagramBorder') }}
            </a-checkbox>
          </div>
        </div>
      </li>
      <a-divider class="map-editor-divider" />
      <li>
        <!-- 显示机器人 -->
        <div class="form-label">{{ t('message.map.displayRobot') }}</div>

        <div class="form-border">
          <a-row class="checkbox-group" style="margin: 6px 16px">
            <a-col :span="5" style="margin-right: 16px; border-right: 1px solid #e7e7e7">
              <!-- 机器人 -->
              <a-checkbox v-model:checked="formState.isAgvShow" @change="onCheckAllChange">
                {{ t('message.vehicle') }}
              </a-checkbox>
            </a-col>
            <a-checkbox-group v-model:value="formState.checkedList" :options="plainOptions" @change="agvChange">
              <template #label="{ label }">
                <span>{{ label }}</span>
              </template>
            </a-checkbox-group>
          </a-row>
        </div>
      </li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
import { agvMapDisplaySetupLocalData, mapEditDisplaySetupLocalData } from '@/utils/storage'
import { debounce, stopPropagationFn } from '@/utils'
import { AgvDisplaySetupData, getAgvDisplaySetupData } from '@/pages/monitoring/agv-map/config'
import useStore from '@/stores'
import { useLocale } from 'youibot-plus'
import { EditMapForm, getEditMapForm } from '@/pages/operations/maps/map-edit/config'

import { pathLineWConfig, markerHConfig } from 'youibot-plus'
const { t } = useLocale()

const formState = ref<AgvDisplaySetupData>(getAgvDisplaySetupData())
const editMapForm = ref<EditMapForm>(getEditMapForm())
const plainOptions = [
  { label: t('message.encoding'), value: 'agvCode' }, //编码
  { label: t('message.map.realTimePointCloud'), value: 'cloud' } //实时点云
]
const onCheckAllChange = (e: any) => {
  formState.value.checkedList = e.target.checked ? ['agvCode', 'cloud'] : []
  formState.value.indeterminate = false
  setLocalData()
}
watch(
  () => formState.value.checkedList,
  val => {
    if (!formState.value.isAgvShow) {
      formState.value.isAgvShow = val && !!val.length && val.length < plainOptions.length
    }
    // formState.value.isAgvShow = val && val.length === plainOptions.length
  }
)

const setLocalData = () => {
  handleSettings()
  agvMapDisplaySetupLocalData(JSON.stringify(formState.value))
}
const handleEditMapSettings = () => {
  mapDisplaySetupData.setMapDisplaySetupData(editMapForm.value)
}
// 防抖
const nameTest = debounce(handleEditMapSettings, 100)
const setEditMapLocalData = (type?: string) => {
  if (type === 'isForceChangeElementSize') {
    if (editMapForm.value.isForceChangeElementSize) {
      if (!editMapForm.value.markerHeght) {
        editMapForm.value.markerHeght = markerHConfig.maxHeight
      }
      if (!editMapForm.value.pathWdth) {
        editMapForm.value.pathWdth = pathLineWConfig.maxLineWidth
      }
    } else {
      editMapForm.value.markerHeght = undefined
      editMapForm.value.pathWdth = undefined
    }
  }
  nameTest()

  mapEditDisplaySetupLocalData(JSON.stringify(editMapForm.value))
}
const getLocalData = () => {
  const data = agvMapDisplaySetupLocalData()
  if (data) {
    formState.value = JSON.parse(data)
  }
}
const getEditMapLocalData = () => {
  const data = mapEditDisplaySetupLocalData()
  if (data) {
    editMapForm.value = JSON.parse(data)
  }
}
const { agvMapDisplaySetupData, mapDisplaySetupData } = useStore()
const handleSettings = () => {
  agvMapDisplaySetupData.setAgvMapDisplaySetupData(formState.value)
}

const agvChange = () => {
  setLocalData()
}
getLocalData()
getEditMapLocalData()
</script>
<style lang="less">
.map-editor-select {
  z-index: 3001;
}

.map-editor-divider {
  margin: 16px 0;
  background-color: #e8e8e8;
}
</style>
<style lang="less" scoped>
.map-editor-wrap {
  min-height: 690px;

  &-title {
    margin-bottom: 6px;
    color: #000;
    font-size: 16px;
  }

  ul {
    .form-label {
      margin-bottom: 5px;
      color: #848484;
    }

    .checkbox-group {
      margin-bottom: 8px;
    }

    .back-color {
      display: flex;
      align-items: center;

      &-describe {
        margin-left: 8px;
      }

      &-label {
        margin-left: 29px;
      }
    }

    .form-border {
      border: 1px solid #e7e7e7;
    }

    .element-size {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-form {
        display: flex;
        align-items: center;

        &-label {
          width: 28px;
          margin-right: 12px;
          color: #000;
        }
      }
    }
  }
}
</style>
