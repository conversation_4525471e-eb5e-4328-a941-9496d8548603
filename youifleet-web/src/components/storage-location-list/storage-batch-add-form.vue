<template>
  <my-modal
    v-model:visible="visible"
    class="add-form"
    :zIndex="props.zIndex"
    :width="'600px'"
    :maskClosable="false"
    @cancel="cancel"
    @confirm="confirm">
    <a-form :model="formState" ref="formRef" labelAlign="right" :labelCol="{ span: 6 }">
      <!-- 库区 -->
      <a-form-item :label="t('message.storageLocation.reservoirArea')" name="warehouseAreaCode" :rules="[isRequire()]">
        <a-select
          ref="select"
          v-model:value="formState.warehouseAreaCode"
          :fieldNames="{ label: 'name', value: 'code' }"
          :options="warehouseAreaList"></a-select>
      </a-form-item>
      <!-- 类型 -->
      <a-form-item :label="t('message.type')" name="warehouseTypeCode" :rules="[isRequire()]">
        <a-select
          ref="select"
          v-model:value="formState.warehouseTypeCode"
          :fieldNames="{ label: 'name', value: 'code' }"
          :options="warehouseTypeList"></a-select>
      </a-form-item>
      <a-form-item :label="t('message.storageLocation.row')" class="margin0" required>
        <a-row :gutter="16">
          <a-col :span="9">
            <!-- 排 -->
            <a-form-item name="startRow" :rules="[isRequire()]">
              <a-input-number
                class="width160"
                v-model:value="formState.startRow"
                :min="1"
                :max="99"
                :precision="0"
                :controls="false"></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="1">—</a-col>
          <a-col :span="9">
            <!-- 排 -->
            <a-form-item name="endRow" :rules="[isRequire()]" class="input-number-sufffix long">
              <a-input-number
                class="width160"
                v-model:value="formState.endRow"
                :max="99"
                :min="formState.startRow"
                :precision="0"
                :controls="false"></a-input-number>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-item>
      <a-form-item :label="t('message.storageLocation.column')" class="margin0" required>
        <a-row :gutter="16">
          <a-col :span="9">
            <!--  列-->
            <a-form-item name="startColumn" :rules="[isRequire()]">
              <a-input-number
                class="width160"
                v-model:value="formState.startColumn"
                :min="1"
                :max="99"
                :precision="0"
                :controls="false"></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="1">—</a-col>
          <a-col :span="9">
            <!-- 列 -->
            <a-form-item name="endColumn" :rules="[isRequire()]" class="input-number-sufffix long">
              <a-input-number
                class="width160"
                v-model:value="formState.endColumn"
                :max="99"
                :min="formState.startColumn"
                :precision="0"
                :controls="false"></a-input-number>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-item>
      <a-form-item :label="t('message.storageLocation.layer')" class="margin0" required>
        <a-row :gutter="16">
          <a-col :span="9">
            <!--  层-->
            <a-form-item name="startLayer" :rules="[isRequire()]">
              <a-input-number
                class="width160"
                v-model:value="formState.startLayer"
                :min="1"
                :max="9"
                :precision="0"
                :controls="false"
                @change="endLayerChange"></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="1">—</a-col>
          <a-col :span="9">
            <!-- 层 -->
            <a-form-item name="endLayer" :rules="[isRequire()]">
              <a-input-number
                class="width160"
                v-model:value="formState.endLayer"
                :max="9"
                :min="formState.startLayer"
                :precision="0"
                :controls="false"
                @change="endLayerChange"></a-input-number>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-item>
      <!-- 层高度 -->
      <a-form-item
        v-for="(item, index) in formState.layerHeights"
        :label="(formState.startLayer ? formState.startLayer : 1) + index + t('message.storageLocation.storyHeight')"
        :name="['layerHeights', index, 'value']"
        :rules="[isRequire()]"
        class="input-number-sufffix long">
        <a-input-number class="width160" v-model:value="item.value" :min="0" :max="9999" :controls="false"></a-input-number>
        <span class="suffix">mm</span>
      </a-form-item>
    </a-form>
  </my-modal>
</template>
<script setup lang="ts">
import { useLocale } from 'youibot-plus'
import { isRequire } from '@/utils/form-rules'
import { getWarehouseArea } from '@/api/warehouseArea'
import { getWarehouseType } from '@/api/warehouseType'
import { postWarehouseBatch } from '@/api/warehouse'
import { getOperateText } from '@/utils'
const { t } = useLocale()
let emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps({
  visible: Boolean,
  zIndex: {
    type: Number,
    default: 1000
  }
})
const visible = ref(false)
const formRef = ref()
const warehouseAreaList = ref([])
const warehouseTypeList = ref([])
const getFormData = () => {
  return {
    warehouseAreaCode: '',
    warehouseTypeCode: '',
    startRow: 1,
    endRow: 1,
    startColumn: 1,
    endColumn: 1,
    startLayer: 1,
    endLayer: 1,
    layerHeights: [
      {
        value: 0
      }
    ]
  }
}
const formState = reactive(getFormData())

watchEffect(() => {
  visible.value = props.visible
  Object.assign(formState, getFormData())
})
const confirm = () => {
  formRef.value?.validate().then(() => {
    const data = {
      warehouseAreaCode: formState.warehouseAreaCode as unknown as number,
      warehouseTypeCode: formState.warehouseTypeCode as unknown as number,
      startRow: formState.startRow,
      endRow: formState.endRow,
      startColumn: formState.startColumn,
      endColumn: formState.endColumn,
      startLayer: formState.startLayer,
      endLayer: formState.endLayer,
      layerHeights: [] as number[]
    }
    formState.layerHeights.forEach(item => {
      data.layerHeights.push(item.value)
    })
    postWarehouseBatch(data)
      .then(res => {
        cancel()
        emits('confirm')
        getOperateText(t)
      })
      .catch(err => {
        getOperateText(t, {
          result: 'error',
          reason: err.msg
        })
      })
  })
}
const cancel = () => {
  emits('update:visible', false)
}
const layer = computed(() => {
  const num = formState.endLayer - (formState.startLayer ? formState.startLayer - 1 : 0)
  return num
})
const endLayerChange = () => {
  formState.layerHeights = []
  for (let i = 0; i < layer.value; i++) {
    formState.layerHeights.push({
      value: 0
    })
  }
}
const getWarehouseAreaFn = () => {
  getWarehouseArea().then(res => {
    warehouseAreaList.value = res.data
  })
}
const getWarehouseTypeFn = () => {
  getWarehouseType().then(res => {
    warehouseTypeList.value = res.data
  })
}
onMounted(() => {
  getWarehouseAreaFn()
  getWarehouseTypeFn()
})
</script>
<style lang="less" scoped>
.margin0 {
  margin: 0;
}

.width160 {
  width: 160px;
}
</style>
