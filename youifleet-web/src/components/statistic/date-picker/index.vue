<template>
  <a-range-picker
    v-model:value="dateVal"
    :presets="rangePresets"
    :disabled-date="disabledDate"
    :valueFormat="'YYYY-MM-DD'"
    :format="'YYYY-MM-DD'"
    :size="size"
    @change="emit('update:dateVal', dateVal)" />
</template>

<script setup lang="ts">
import dayjs, { Dayjs } from 'dayjs'
import type { PropType } from 'vue'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const size = ref<'default' | 'large' | 'small'>('default')
const props = defineProps({
  dateVal: {
    type: Array as unknown as PropType<[Dayjs, Dayjs]>,
    default: []
  }
})
const dateVal = ref<[Dayjs, Dayjs]>()
const emit = defineEmits(['update:dateVal'])

watchEffect(() => {
  if (props.dateVal.length) {
    dateVal.value = [dayjs(props.dateVal[0]), dayjs(props.dateVal[1])]
  } else {
    dateVal.value = props.dateVal
  }
})

const rangePresets = [
  {
    //今天
    label: t('message.today'),
    value: [dayjs().add(0, 'd'), dayjs().add(0, 'd')]
  },
  {
    //'昨天'
    label: t('message.yesterday'),
    value: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')]
  },
  {
    //本周
    label: t('message.thisWeek'),
    value: [dayjs().startOf('week').add(1, 'day'), dayjs()]
  },
  {
    //'上周'
    label: t('message.lastWeek'),
    value: [dayjs().add(-1, 'week').startOf('week').add(1, 'day'), dayjs().add(-1, 'week').endOf('week').add(1, 'day')]
  },
  {
    //本月
    label: t('message.thisMonth'),
    value: [dayjs().startOf('month').add(0, 'day'), dayjs()]
  },
  {
    //上月
    label: t('message.lastMonth'),
    value: [dayjs().add(-1, 'month').startOf('month').add(0, 'day'), dayjs().add(-1, 'month').endOf('month').add(0, 'day')]
  },
  {
    //过去7天
    label: t('message.last7Days'),
    value: [dayjs().add(-7, 'd'), dayjs()]
  },
  {
    //过去30天
    label: t('message.last30Days'),
    value: [dayjs().add(-30, 'd'), dayjs()]
  }
]

//禁止选择超过今天的日期 且 禁止选择一年前的日期
const disabledDate = (current: Dayjs) => {
  return (current && current > dayjs().endOf('day')) || (current && current < dayjs().add(-1, 'year'))
}
</script>

<style>
.ant-picker-dropdown {
  z-index: 9999 !important;
}
</style>
