<template>
  <a-spin :spinning="isSpinning" wrapperClassName="thermal-loading">
    <div id="carousel">
      <div class="title">{{ props.type === 'task' ? '任务热力图' : '异常位置热力图' }}</div>
      <el-carousel
        :height="props.mapStyle.height"
        :interval="interval"
        :arrow="index === -1 ? 'hover' : 'never'"
        :loop="true"
        @change="carouselChange"
        indicator-position="none">
        <el-carousel-item v-for="(item, index) in mapDataList" :key="index">
          <yi-heat-map
            v-if="item.mapData && Object.keys(item.mapData).length"
            :style="{
              ...props.mapStyle
            }"
            :title="item.mapData.code"
            :mapData="item.mapData"
            :isShowOperation="true"
            :isShowMapInfo="true"
            :pathConfig="mapConfig.pathConfig"
            :isShowAgvSunx="false"
            :markerConfig="mapConfig.markerConfig"
            :agvConfig="mapConfig.agvConfig"
            :areaConfig="mapConfig.areaConfig"
            :wrapDompWidth="width"
            :wrapDompHeight="height"
            @initFinish="item.initFinish" />
        </el-carousel-item>
      </el-carousel>
    </div>
  </a-spin>
</template>
<script setup lang="ts">
import { AgvMap } from '@/hooks/use-map'
import useMapInit, { IMapFn } from '@/hooks/use-map-init'
import YIGl from 'youibot-gl'
import { InitFinishObj } from '@/hooks/use-map-init'
import { getElementSize, messageTip } from '@/utils'
import { ThermalPointData } from 'youibot-plus'
import { PropType, Ref } from 'vue'
import { getStatisticsAbnormalHeatMap, getStatisticsTaskHeatMap } from '@/api/statistics'
import { Timer } from '@/utils'
const props = defineProps({
  mapStyle: {
    type: Object,
    default: {}
  },
  heatMapData: {
    type: Array as PropType<ThermalPointData[]>,
    default: []
  },
  startTime: {
    type: String,
    default: ''
  },
  endTime: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  }
})
const isSpinning = ref(true)
const mapConfig = reactive({
  markerConfig: { isInteractive: false },
  pathConfig: { isInteractive: false },
  agvConfig: { isInteractive: false },
  areaConfig: { isInteractive: false }
})
const width = ref(0)
const height = ref(0)
const index = ref(1)
const interval = ref(0)
let carouselIndex = 0
let vehicleMapCode: string | undefined = ''
// const mapDataList = ref<{ mapData: AgvMap | undefined | null; initFinish: (obj: InitFinishObj) => Promise<void>; mapFn: InitFinishObj }[]>(
//   []
// )
const mapDataList = reactive<
  { mapFn: Ref<InitFinishObj | undefined>; mapData: AgvMap | undefined | null; initFinish: (obj: InitFinishObj) => Promise<void> }[]
>([])
let timer: Timer | null
let timeout: NodeJS.Timer | null
const { mapData, mapList, mapFn, initFinish, getBaseMapData } = useMapInit(
  () => {
    if (mapData.value?.code && mapFn.value) {
      getElementSize(mapData.value?.code, mapFn.value.gl)
      nextTick(() => {
        timeout = setTimeout(() => {
          if (mapList.value && mapList.value[index.value]) {
            initMap(mapList.value[index.value])
          } else {
            index.value = -1
            interval.value = 30000
          }
          carouselChange(0)
        }, 500)
      })
    }
    isSpinning.value = false
  },
  () => {
    if (mapData.value?.code) {
      mapDataList.push({
        initFinish: initFinish,
        mapData: null,
        mapFn: mapFn.value
      })
      timeout = setTimeout(() => {
        mapDataList[0].mapData = mapData.value
      }, 50)
    }
  }
)
const initMap = (item: { code: string | undefined }) => {
  const {
    mapData: itemMapData,
    mapFn: itemMapFn,
    initFinish: itemInitFinish,
    getBaseMapData: itemGetBaseMapData
  } = useMapInit(
    () => {
      if (itemMapFn.value && itemMapData.value?.code) {
        getElementSize(itemMapData.value?.code, itemMapFn.value.gl)
      }
      index.value = index.value + 1
      nextTick(() => {
        timeout = setTimeout(() => {
          if (mapList.value && mapList.value[index.value]) {
            initMap(mapList.value[index.value])
          } else {
            index.value = -1
            interval.value = 30000
          }
        }, 500)
      })
    },
    () => {
      mapDataList.push({
        initFinish: itemInitFinish,
        mapData: null,
        mapFn: itemMapFn.value
      })
      timeout = setTimeout(() => {
        mapDataList[mapDataList.length - 1].mapData = itemMapData.value
      }, 50)
    }
  )
  itemGetBaseMapData(item.code)
}

const carouselChange = (index: number) => {
  carouselIndex = index
  vehicleMapCode = mapDataList[index].mapData?.code
  if (props.type === 'task') {
    if (!timer) {
      timer = new Timer()
      timer.setTimer(getTaskHeatMap, 10000)
    }

    getTaskHeatMap()
  } else {
    if (!timer) {
      timer = new Timer()
      timer.setTimer(getAbnormalHeatMap, 10000)
    }

    getAbnormalHeatMap()
  }
}
const getTaskHeatMap = () => {
  if (vehicleMapCode) {
    getStatisticsTaskHeatMap({
      startTime: props.startTime,
      endTime: props.endTime,
      vehicleMapCode: vehicleMapCode
    }).then(res => {
      setThermalPoint(res.data)
    })
  }
}
const getAbnormalHeatMap = () => {
  if (vehicleMapCode) {
    getStatisticsAbnormalHeatMap({
      startTime: props.startTime,
      endTime: props.endTime,
      vehicleMapCode: vehicleMapCode
    }).then(res => {
      setThermalPoint(res.data)
    })
  }
}

const setThermalPoint = (data: ThermalPointData[]) => {
  if (mapDataList[carouselIndex]) {
    const array = data.map(element => {
      element['code'] = element.x + '_' + element.y
      element['name'] = 'x:' + element.x + ',y:' + element.y
      return toRaw(element)
    })

    mapDataList[carouselIndex].mapFn?.initThermalPoint(mapDataList[carouselIndex].mapFn?.gl as YIGl, array as unknown as ThermalPointData[])
  }
}

// 移入禁用滚轮事件
const mousemoveChange = () => {
  interval.value = 0
  document.addEventListener('wheel', onScroll, { passive: false })
}
// 移除解除禁用滚轮事件
const mouseleaveChange = () => {
  interval.value = 30000
  document.removeEventListener('wheel', onScroll)
}
onMounted(() => {
  nextTick(() => {
    const carousel = document.getElementById('carousel')
    if (carousel) {
      width.value = carousel?.clientWidth + carousel?.offsetLeft - 60
      height.value = carousel?.clientHeight + carousel?.offsetTop - 60
    }
    // 移入禁用滚轮事件
    document.getElementById('carousel')?.addEventListener('mousemove', mousemoveChange)
    // 移除解除禁用滚轮事件
    document.getElementById('carousel')?.addEventListener('mouseleave', mouseleaveChange)
  })
})
getBaseMapData()
const onScroll = (e: { preventDefault: () => void }) => {
  e.preventDefault()
  // 后续逻辑
}
onBeforeUnmount(() => {
  timer?.clear()
  timer = null
  timeout && clearTimeout(timeout)
  timeout = null
  // 移除悬停事件
  document.getElementById('carousel')?.removeEventListener('mousemove', mousemoveChange)
  document.getElementById('carousel')?.removeEventListener('mouseleave', mouseleaveChange)
  // 删除滚轮事件
  document.removeEventListener('wheel', onScroll)
})
</script>

<style scoped lang="less">
.map {
  position: relative;
  // width: 1230px !important;
  // height: 600px;
  overflow: hidden;
}

.yi-heat-map {
  // width: 1230px !important;
  // height: 600px;
  overflow: hidden;
}
</style>
<style scoped>
/* stylelint-disable-next-line selector-pseudo-class-no-unknown */

/* :deep(.el-carousel__container) {
  height: 600px !important;
} */
.thermal-loading {
  width: 100%;
  height: 100%;
}

#carousel {
  position: relative;
  width: 100%;
  height: 100%;

  .title {
    position: absolute;
    top: 12px;
    left: 16px;
    z-index: 2003;
    font-weight: bold;
    font-size: 18px;
  }
}
</style>
