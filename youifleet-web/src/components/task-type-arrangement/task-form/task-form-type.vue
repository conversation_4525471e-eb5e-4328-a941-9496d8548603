<template>
  <a-input
    v-if="['Text', 'Object', 'Default', 'Common'].includes(props.type)"
    :disabled="props.disabled"
    :placeholder="t(placeholder)"
    v-model:value="state.value"
    @change="emitChange"
    @pressEnter="evetBlur" />
  <a-textarea
    v-else-if="['Json', 'MultiText'].includes(props.type)"
    :placeholder="t(placeholder)"
    v-model:value="state.value"
    @change="emitChange"
    :rows="props.textareaRow" />
  <a-input-number
    v-else-if="isNumber(props.type)"
    class="input-number"
    v-model:value="state.value"
    :placeholder="t(placeholder)"
    :min="props.minValue"
    :max="props.maxValue"
    :precision="isInt()"
    :disabled="props.disabled"
    @change.native="(val:string) =>{ inputNativeFn(val)}"
    @step="emitChange"
    @pressEnter="evetBlur" />
  <a-select
    v-else-if="isSelect(props.type)"
    :disabled="(props.type === 'VehicleCode' && props.isMonitoringStation) || props.disabled"
    :options="props.options"
    v-model:value="state.value"
    :fieldNames="props.fieldNames"
    :placeholder="t(placeholder)"
    :dropdownStyle="{ zIndex: props.zIndex }"
    :mode="isMultiple(props.type) ? 'multiple' : null"
    allowClear
    @change="emitChange"></a-select>

  <a-switch
    v-else-if="props.type === 'Bool'"
    v-model:checked="state.value"
    :checkedValue="'true'"
    :unCheckedValue="'false'"
    :disabled="props.disabled"
    @change="emitChange" />
  <map-selection-points
    v-else-if="isPoint(props.type)"
    v-model:markerData="state.markerData"
    :isMonitoringStation="props.isMonitoringStation"
    :vehicleCode="props.vehicleCode"
    :disabled="props.disabled"
    :type="isSinglePoint(props.type) ? 1 : 2"
    @change="emitChange"
    @monitoringClick="amonitoringClick()"></map-selection-points>
</template>
<script lang="ts" setup>
import mapSelectionPoints from '@/components/map/map-selection-points/index.vue'
import useNodeForm from '@/hooks/use-node-form'
import useMapSelectionPoints from '@/hooks/use-map-selection-points'
import type { IBaseMarkerData } from '@/hooks/use-map-selection-points'
import { PropType } from 'vue'
import { MarkerData } from 'youibot-plus'
import { evetBlur } from '@/utils'
import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const { isMultiple, isPoint, isSelect, isSinglePoint, isMultiList, isNumber } = useNodeForm()
const emit = defineEmits(['update:value', 'change', 'amonitoringClick'])
const { translateMarkersToArray, translateArrayToMarkers } = useMapSelectionPoints()
const props = defineProps({
  type: {
    type: String,
    default: 'Text'
  },
  value: [String, Number, Boolean, Array, Array<string>, Object],
  options: {
    type: Array,
    default: []
  },
  placeholder: {
    type: String,
    default: 'message.defaultValue'
  },
  fieldNames: {
    type: Object
  },
  isMonitoringStation: {
    type: Boolean,
    default: false
  },
  vehicleCode: {
    type: String,
    default: ''
  },
  marker: {
    type: Object as PropType<MarkerData> | null
  },
  zIndex: {
    type: Number,
    default: 1500
  },
  minValue: {
    type: Number,
    default: -Infinity
  },
  maxValue: {
    type: Number,
    default: Infinity
  },
  disabled: {
    type: Boolean,
    default: false
  },
  textareaRow: {
    type: Number,
    default: 0
  }
})

const state = reactive({
  markerData: (props.value == '' ? [] : props.value) as IBaseMarkerData[],
  value: props.value,
  type: props.type,
  options: props.options
})
watchEffect(() => {
  // 初始化值处理
  if (props.value == '') {
    if (isMultiple(props.type)) {
      state.value = []
    } else {
      state.value = props.value
    }
  } else {
    // 多选列表默认值为单个字符串 需要单独处理
    if (isMultiList(props.type)) {
      if (typeof props.value == 'string') {
        state.value = [props.value]
      } else if (typeof props.value == 'object') {
        state.value = toRaw(props.value)
      }
    } else {
      state.value = props.value
    }
  }

  if (isPoint(props.type) && props.value) {
    if (Array.isArray(props.value)) {
      state.markerData = translateArrayToMarkers(props.value)
    }
    if (typeof props.value == 'string') {
      let list = props.value.split(',')
      state.markerData = translateArrayToMarkers(list)
    }
  } else {
    state.markerData = []
  }
  state.options = props.options
  state.type = props.type
})

const isInt = () => {
  if (state.type === 'Int') {
    return 0
  } else {
    return -1
  }
}
const amonitoringClick = () => {
  emit('amonitoringClick')
}
// a-input-number 修改输入框值的时候change都不生效，只能input.native触发值修改。
const inputNativeFn = (val: string) => {
  emit('update:value', val)
  emit('change')
}

const change = () => {
  if (isPoint(state.type)) {
    let value = translateMarkersToArray(state.markerData)
    if (isSinglePoint(props.type) && Array.isArray(value)) {
      emit('update:value', value[0])
    } else {
      emit('update:value', value)
    }
  } else {
    emit('update:value', isSelect(props.type) ? state.value : state.value)
  }
}
// 值修改，触发回调
const emitChange = (value: any, option: any) => {
  change()
  emit('change', props.type, value, option)
}
</script>
<style lang="less" scoped>
.input-number {
  width: 100%;
}
</style>
