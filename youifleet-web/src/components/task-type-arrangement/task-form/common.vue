<template>
  <a-form :labelAlign="'left'" layout="vertical" autocomplete="off" ref="formRef" class="common" :model="formData" :labelWrap="true">
    <!-- 编码 -->
    <a-form-item :label="t('message.encoding')"><a-input v-model:value="formData.code" disabled></a-input></a-form-item>
    <!-- 类型名称 -->
    <a-form-item :label="t('message.typeName')">
      <a-input v-model:value="formData.name" disabled></a-input>
    </a-form-item>
    <!-- 显示名称 -->
    <a-form-item :label="t('message.taskTypeArrangement.displayName')" name="showName">
      <a-input v-model:value="formData.showName" @blur="debounceFn" @pressEnter="evetBlur"></a-input>
    </a-form-item>

    <p class="interval"></p>
    <template v-if="params.inList.length">
      <!-- 参数 -->
      <p class="params">{{ t('message.taskTypeArrangement.inputParameter') }}</p>
      <template v-if="formData?.params">
        <template v-for="(item, index) in formData.params">
          <template v-if="item?.type !== 'Combination'">
            <a-form-item
              :key="index"
              v-if="item.category === 'In'"
              :rules="
                item.required
                  ? [{ required: true, validator: validatePass, trigger: 'change' }]
                  : [{ required: false, validator: validateJSON, trigger: 'change' }]
              "
              :name="item.type === 'Bool' ? undefined : ['params', index]">
              <!-- item.type === 'Bool' ? undefined : ['params', index]解决 switch 组件可以点击名称触发数据变更-->
              <template #label>
                <span>{{ item.name }}</span>
                <a-tooltip placement="top" v-if="item.notice">
                  <i class="icon-jieshi iconfont" @click.stop=""></i>
                  <template #title>
                    <span>{{ item.notice }}</span>
                  </template>
                </a-tooltip>
              </template>

              <VariableInput
                v-model:formData="formData.params[index]"
                :code="formData.code"
                :options="isGetOptiosList(formData.params[index].type)"
                :fieldNames="getFieldNames(formData.params[index].type)"
                @change="debounceFn"></VariableInput>
            </a-form-item>
          </template>
          <template v-else-if="item?.type === 'Combination' && item.category === 'In'">
            <VariableInput
              v-model:formData="formData.params[index]"
              :code="formData.code"
              :options="isGetOptiosList(formData.params[index].type)"
              :fieldNames="getFieldNames(formData.params[index].type)"
              @change="debounceFn"></VariableInput>
          </template>
        </template>
      </template>
    </template>

    <template v-if="params.outList.length && outParams?.length">
      <!-- 任务变量（输出） -->
      <p class="task-form-title">{{ t('message.taskTypeArrangement.outputParameter') }}</p>
      <a-row class="task-form-header task-form-item">
        <!-- 表单 -->
        <a-col :span="4">{{ t('message.type') }}</a-col>
        <!-- 类型 -->
        <a-col :span="10">{{ t('message.form') }}</a-col>

        <!-- 变量名 -->
        <a-col :span="10">{{ t('message.taskTypeArrangement.variableName') }}</a-col>
      </a-row>
      <template v-for="(item, index) in formData.params">
        <a-row class="row-list task-form-item" v-if="item.category === 'Out' && item?.type !== 'Combination'">
          <a-col :span="4">
            <a-tooltip placement="topLeft" :title="t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory)">
              {{ t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory) }}
            </a-tooltip>
          </a-col>
          <a-col :span="10">
            <a-tooltip placement="topLeft" :title="t('message.taskTypeArrangement.variableTypeList.' + item.type)">
              {{ t('message.taskTypeArrangement.variableTypeList.' + item.type) }}
            </a-tooltip>
          </a-col>
          <a-col :span="10">
            <a-tooltip placement="topLeft" :title="item.name">{{ item.name }}</a-tooltip>
          </a-col>
        </a-row>
      </template>
    </template>
    <template v-if="formData?.params">
      <template v-for="(item, index) in formData.params">
        <template v-if="item?.type === 'Combination' && item.category === 'Out'">
          <VariableInput
            v-model:formData="formData.params[index]"
            :code="formData.code"
            :options="isGetOptiosList(formData.params[index].type)"
            :fieldNames="getFieldNames(formData.params[index].type)"
            @change="debounceFn"></VariableInput>
        </template>
      </template>
    </template>
  </a-form>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import type { INodeConfig, InputVariableList } from '@/hooks/use-node'
import { checkIsJSON, debounce, evetBlur, messageTip, uniqueArray } from '@/utils'
import VariableInput from './variable-input.vue'
import useNodeForm from '@/hooks/use-node-form'
import { Rule } from 'ant-design-vue/lib/form'
import { useLocale } from 'youibot-plus'
import useStore from '@/stores'

const { t } = useLocale()
const { initOptions, isGetOptiosList, getFieldNames } = useNodeForm()
const { nodeStore } = useStore()
// 加载下拉框数据
initOptions()
const emit = defineEmits(['formDataChange'])
const props = defineProps({
  formData: {
    type: Object as PropType<INodeConfig>,
    required: true
  }
})

const formRef = ref()
const params = reactive({
  outList: [] as InputVariableList[],
  inList: [] as InputVariableList[]
})
const formData = computed(() => {
  // 重置表单校验结果，避免其他节点校验错误污染
  formRef.value?.clearValidate()
  params.inList = []
  params.outList = []
  props.formData.params?.forEach(item => {
    if (item.category === 'In') {
      params.inList.push(item)
    } else {
      params.outList.push(item)
    }
  })
  return props.formData
})
const outParams = computed(() => {
  return formData.value.params?.filter(item => {
    return item.category === 'Out' && item.type !== 'Combination'
  })
})
const handleChange = () => {
  if (formRef.value) {
    formRef.value
      ?.validate()
      .then(() => {
        formDataChange()
      })
      .catch(() => {
        formDataChange()
      })
  } else {
    // 属性未保存，请使用“Enter”键确认
    messageTip(t('message.taskTypeArrangement.thePropertiesAreNotSavedPressEnterToConfirm'), 'warning')
  }
}
const formDataChange = () => {
  const mistakeData = nodeStore.getMistakeData
  const mistakeIndex = mistakeData.findIndex(item => {
    return item === formData.value.code
  })
  if (mistakeIndex !== -1 && mistakeIndex !== undefined) {
    mistakeData.splice(mistakeIndex, 1)
    nodeStore.setMistakeData(mistakeData)
  }
  emit('formDataChange', formData.value)
}

// 防抖
const debounceFn = debounce(handleChange, 0)
const validateJSON = async (_rule: Rule, item: InputVariableList) => {
  const { defaultValue, source, variable, value, type } = item
  if (type === 'Json') {
    if (defaultValue || value) {
      const val = (defaultValue || value) as string
      const str = val.replace(/[ ]|[\r\n]/g, '')
      if (!checkIsJSON(str)) {
        // json格式错误
        return Promise.reject(t('message.taskManage.jsonFormatError'))
      }
    }
  }
  return Promise.resolve()
}
const validatePass = async (_rule: Rule, item: InputVariableList) => {
  const { defaultValue, source, variable, value, type } = item

  if (source) {
    // 节点值
    if (
      (source !== 'Fixed' && variable === '') ||
      ((defaultValue === undefined || defaultValue === null) && (value === null || value === undefined))
    ) {
      return Promise.reject(t('message.userSetting.nameTip'))
    }
    // 固定值
    if (
      source == 'Fixed' &&
      (defaultValue === undefined || defaultValue === null || defaultValue === '') &&
      (value === '' || value === null || value === undefined)
    ) {
      return Promise.reject(t('message.userSetting.nameTip'))
    }
  } else {
    if (
      (defaultValue === undefined || defaultValue === null || defaultValue === '') &&
      (value === '' || value === null || value === undefined)
    ) {
      return Promise.reject(t('message.userSetting.nameTip'))
    }
  }
  if (type === 'Json') {
    if (defaultValue || value) {
      const val = (defaultValue || value) as string
      const str = val.replace(/[ ]|[\r\n]/g, '')
      if (!checkIsJSON(str)) {
        // json格式错误
        return Promise.reject(t('message.taskManage.jsonFormatError'))
      }
    }
  }
  return Promise.resolve()
}
</script>
<style lang="less" scoped>
.task-form {
  &-title {
    height: 22px;
    margin: 30px 20px 12px;
    padding-left: 12px;
    color: #000;
    line-height: 22px;
    border-left: 2px solid @primary-color;
  }

  &-header {
    padding: 0 12px;
    color: #a6a6a6;

    .ant-col {
      height: 32px;
      line-height: 32px;
    }
  }

  &-item {
    margin: 0 20px;
  }
}

.row-list {
  min-height: 30px;
  padding: 4px 12px;
  color: #000;

  .ant-col {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &:hover {
    background: rgb(238 116 42 / 10%);
  }
}
</style>
