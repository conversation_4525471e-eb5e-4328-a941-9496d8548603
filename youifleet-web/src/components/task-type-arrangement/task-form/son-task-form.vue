<template>
  <a-form :labelAlign="'left'" layout="vertical" ref="formActionRef" class="son-task-form" :labelWrap="true">
    <a-form-item label="编码"><a-input></a-input></a-form-item>
    <a-form-item label="类型名称">
      <a-input :value="'子任务'" disabled></a-input>
    </a-form-item>
    <a-form-item label="名称"><a-select></a-select></a-form-item>

    <p>参数</p>
    <a-form-item>
      <template #label>
        <span>佐治</span>
        <a-tooltip placement="top">
          <i class="icon-jieshi iconfont"></i>
          <template #title>
            <span>提示</span>
          </template>
        </a-tooltip>
      </template>
      <a-input></a-input>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup></script>
<style lang="less" scoped>
.son-task-form {
  .ant-form-item {
    margin-bottom: 12px;

    .icon-jieshi {
      margin-left: 12px;
    }
  }

  p {
    height: 28px;
    margin-bottom: 0;
    margin-bottom: 12px;
    padding-left: 12px;
    line-height: 28px;
    border-left: 6px solid @primary-color;
  }
}
</style>
