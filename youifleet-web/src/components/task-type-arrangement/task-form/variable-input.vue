<template>
  <div>
    <a-row v-if="props.formData?.type !== 'Combination'">
      <a-col :span="formData?.isVariable ? 18 : 24">
        <task-form-type
          v-if="formData?.nodeCode == undefined || formData?.nodeCode == ''"
          :type="props.variableCategory || formData.variableCategory === 'Number' ? formData.variableCategory : formData.type"
          :textareaRow="15"
          v-model:value="formData.defaultValue"
          :options="props.options || formData?.selectionBoxParam"
          :fieldNames="props.fieldNames"
          :maxValue="formData?.maxValue"
          :minValue="formData?.minValue"
          :disabled="!formData?.editable"
          @change="handleChangeValue"
          placeholder="message.requiredTips"></task-form-type>

        <div class="input-tag" v-else>
          <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
            <template #title>{{ formData?.nodeCode }}</template>
            <a-tag color="#EEEEEE" class="variable-list-tag-number">{{ formData?.nodeCode }}</a-tag>
          </a-tooltip>
          <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
            <template #title>{{ formData?.variableName }}</template>
            <a-tag color="#F7F7F7" :title="formData?.variableName" class="variable-list-tag-name">{{ formData?.variableName }}</a-tag>
          </a-tooltip>

          <i class="icon-shanchuguanbicha1 iconfont" @click="removeVariableName()"></i>
        </div>
      </a-col>
      <a-col span="6" v-if="formData?.isVariable">
        <a-dropdown
          placement="bottomRight"
          overlayClassName="variable"
          :trigger="getOutputList(formData.variableCategory) ? ['click'] : []">
          <a-button class="variable-btn" :disabled="!getOutputList(formData.variableCategory)?.length">
            {{ t('message.variable') }}
            <i class="icon-xialajiantouxiao iconfont"></i>
          </a-button>
          <template #overlay>
            <div class="variable-list" v-if="getOutputList(formData.variableCategory)?.length">
              <ul>
                <li v-for="(output, index2) in getOutputList(formData.variableCategory)" :key="index2" @click="outputItemClick(output)">
                  <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                    <template #title>{{ output.number }}</template>
                    <a-tag color="#EEEEEE" class="variable-list-tag-number">{{ output.number }}</a-tag>
                  </a-tooltip>
                  <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                    <template #title>{{ output.name || output.variable }}</template>
                    <a-tag color="#F7F7F7" class="variable-list-tag-name">
                      {{ output.name || output.variable }}
                    </a-tag>
                  </a-tooltip>
                  <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                    <template #title>{{ t('message.taskTypeArrangement.variableCategoryList.' + output.variableCategory) }}</template>
                    <span class="variable-list-tag-type">
                      {{ t('message.taskTypeArrangement.variableCategoryList.' + output.variableCategory) }}
                    </span>
                  </a-tooltip>

                  <i
                    class="icon-gou iconfont"
                    v-if="formData?.nodeCode == output.number && formData?.variableName == (output.name || output.variable)"></i>
                </li>
              </ul>
            </div>
          </template>
        </a-dropdown>
      </a-col>
    </a-row>
    <template v-else-if="props.formData?.type === 'Combination' && props.formData?.category === 'In'">
      <a-form class="common" :labelAlign="'left'" layout="vertical" autocomplete="off" ref="formRef" :model="state">
        <!-- 任务变量（输出） -->
        <p class="task-form-title">
          {{ t('message.taskTypeArrangement.customInput') }}
        </p>
        <div class="event-form">
          <!-- 任务变量（输入） -->

          <a-row class="event-form-header event-form-item">
            <!-- 类型 -->
            <a-col :span="4">{{ t('message.type') }}</a-col>
            <!-- 参数编码 -->
            <a-col :span="8">{{ t('message.taskTypeArrangement.variableName') }}</a-col>
            <!-- 参数值 -->
            <a-col :span="8">{{ t('message.defaultValue') }}</a-col>
          </a-row>
          <div v-for="(item, index) in state.inputVariableList" :key="index" class="event-form-item">
            <a-row v-if="!item.isEdit" class="row-list">
              <a-col :span="4">
                <a-tooltip placement="topLeft" :title="t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory)">
                  {{ t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory) }}
                </a-tooltip>
              </a-col>
              <a-col :span="8">
                <a-tooltip placement="topLeft" :title="item.code">{{ item.code }}</a-tooltip>
              </a-col>
              <a-col :span="8">
                <a-tooltip placement="topLeft" :title="defaultValueHtml(item)">
                  <span v-html="defaultValueHtml(item)"></span>
                </a-tooltip>
              </a-col>
              <a-col :span="3">
                <i class="icon-compile iconfont" @click="editInputVariable(index)"></i>
                <i class="icon-a-shanchu_jiankongfuben22 iconfont" @click="removeInputVariable(index)"></i>
              </a-col>
            </a-row>
            <div v-else class="edit-row">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item>
                    <a-select
                      v-model:value="item.variableCategory"
                      :options="variableCategoryList"
                      @change="changeType('variableCategory', index)"></a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :name="['inputVariableList', index, 'code']" :rules="isRequire()">
                    <a-input
                      v-model:value="item.code"
                      :placeholder="t('message.actionSetting.parameterCode')"
                      @pressEnter="evetBlur"></a-input>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row class="edit-row-default">
                <a-col :span="18">
                  <a-form-item
                    :name="['inputVariableList', index, 'value']"
                    :rules="item?.nodeCode == undefined || item?.nodeCode == '' ? isRequire() : ''">
                    <task-form-type
                      v-if="item?.nodeCode == undefined || item?.nodeCode == ''"
                      :type="item.variableCategory === 'Number' ? item.variableCategory : item.type"
                      v-model:value="item.value"
                      :options="isGetOptiosList(item.type) || item.selectionBoxParam"
                      :fieldNames="getFieldNames(item.type)"
                      :placeholder="t('message.defaultValue')"></task-form-type>
                    <div class="input-tag" v-else>
                      <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                        <template #title>{{ item?.nodeCode }}</template>
                        <a-tag color="#EEEEEE" class="variable-list-tag-number">{{ item?.nodeCode }}</a-tag>
                      </a-tooltip>
                      <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                        <template #title>{{ item?.variableName }}</template>
                        <a-tag color="#F7F7F7" :title="item?.variableName" class="variable-list-tag-name">
                          {{ item?.variableName }}
                        </a-tag>
                      </a-tooltip>

                      <i class="icon-shanchuguanbicha1 iconfont" @click="removeVariableName2(index)"></i>
                    </div>
                  </a-form-item>
                </a-col>
                <a-col span="6">
                  <a-dropdown
                    placement="bottomRight"
                    overlayClassName="variable"
                    :trigger="getOutputList(item.variableCategory) ? ['click'] : []">
                    <a-button class="variable-btn" :disabled="!getOutputList(item.variableCategory)?.length">
                      {{ t('message.variable') }}
                      <i class="icon-xialajiantouxiao iconfont"></i>
                    </a-button>
                    <template #overlay>
                      <div class="variable-list" v-if="getOutputList(item.variableCategory)?.length">
                        <ul>
                          <li
                            v-for="(output, index2) in getOutputList(item.variableCategory)"
                            :key="index2"
                            @click="outputItemClick2(output, index)">
                            <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                              <template #title>{{ output.number }}</template>
                              <a-tag color="#EEEEEE" class="variable-list-tag-number">{{ output.number }}</a-tag>
                            </a-tooltip>
                            <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                              <template #title>{{ output.name || output.variable }}</template>
                              <a-tag color="#F7F7F7" class="variable-list-tag-name">
                                {{ output.name || output.variable }}
                              </a-tag>
                            </a-tooltip>
                            <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                              <template #title>
                                {{ t('message.taskTypeArrangement.variableCategoryList.' + output.variableCategory) }}
                              </template>
                              <span class="variable-list-tag-type">
                                {{ t('message.taskTypeArrangement.variableCategoryList.' + output.variableCategory) }}
                              </span>
                            </a-tooltip>

                            <i
                              class="icon-gou iconfont"
                              v-if="item?.nodeCode == output.number && item?.variableName == (output.name || output.variable)"></i>
                          </li>
                        </ul>
                      </div>
                    </template>
                  </a-dropdown>
                </a-col>
              </a-row>
              <a-row class="edit-row-btn" justify="end">
                <!-- 取消 -->
                <a-col :span="6">
                  <a-button @click="cancelInputVariable(index)">{{ t('message.cancel') }}</a-button>
                </a-col>
                <!-- 确定 -->
                <a-col :span="6">
                  <a-button type="primary" @click="confirmInputVariable(item.type, index)">{{ t('message.confirm') }}</a-button>
                </a-col>
              </a-row>
            </div>
          </div>
          <a-row justify="center" class="event-form-add" @click="addInputVariable('Fixed')">
            <i class="icon-zengjia iconfont"></i>
            <!-- 新增 -->
            {{ t('message.add') }}
          </a-row>
        </div>
      </a-form>
    </template>
    <template v-else-if="props.formData?.type === 'Combination' && props.formData?.category === 'Out'">
      <a-form class="common" :labelAlign="'left'" layout="vertical" autocomplete="off" ref="formRef" :model="state">
        <!-- 任务变量（输出） -->
        <p class="task-form-title">
          {{ t('message.taskTypeArrangement.customOutput') }}
        </p>
        <div class="event-form">
          <!-- 任务变量（输入） -->

          <a-row class="event-form-header event-form-item">
            <!-- 类型 -->
            <a-col :span="4">{{ t('message.type') }}</a-col>
            <!-- 变量名 -->
            <a-col :span="8">{{ t('message.taskTypeArrangement.variableName') }}</a-col>
            <!-- 参数值 -->
            <a-col :span="5">{{ t('message.checkValue') }}</a-col>
          </a-row>
          <div v-for="(item, index) in state.inputVariableList" :key="index" class="event-form-item">
            <a-row v-if="!item.isEdit" class="row-list">
              <a-col :span="4">
                <a-tooltip placement="topLeft" :title="t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory)">
                  {{ t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory) }}
                </a-tooltip>
              </a-col>
              <a-col :span="8">
                <a-tooltip placement="topLeft" :title="item.code">{{ item.code }}</a-tooltip>
              </a-col>
              <a-col :span="5">
                <a-tooltip placement="topLeft" :title="defaultValueHtml(item)">
                  <span v-html="defaultValueHtml(item)"></span>
                </a-tooltip>
              </a-col>
              <a-col :span="3">
                <i class="icon-compile iconfont" @click="editInputVariable(index)"></i>
                <i class="icon-a-shanchu_jiankongfuben22 iconfont" @click="removeInputVariable(index)"></i>
              </a-col>
            </a-row>
            <div v-else class="edit-row">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item>
                    <a-select v-model:value="item.variableCategory" :options="variableCategoryList"></a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :name="['inputVariableList', index, 'code']" :rules="isRequire()">
                    <a-input
                      v-model:value="item.code"
                      :placeholder="t('message.actionSetting.parameterCode')"
                      @pressEnter="evetBlur"></a-input>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row class="edit-row-default">
                <a-col :span="18">
                  <a-form-item>
                    <task-form-type
                      v-if="item?.nodeCode == undefined || item?.nodeCode == ''"
                      :type="item.variableCategory === 'Number' ? item.variableCategory : item.type"
                      v-model:value="item.value"
                      :options="isGetOptiosList(item.type) || item.selectionBoxParam"
                      :fieldNames="getFieldNames(item.type)"
                      :placeholder="t('message.defaultValue')"></task-form-type>
                    <div class="input-tag" v-else>
                      <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                        <template #title>{{ item?.nodeCode }}</template>
                        <a-tag color="#EEEEEE" class="variable-list-tag-number">{{ item?.nodeCode }}</a-tag>
                      </a-tooltip>
                      <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                        <template #title>{{ item?.variableName }}</template>
                        <a-tag color="#F7F7F7" :title="item?.variableName" class="variable-list-tag-name">
                          {{ item?.variableName }}
                        </a-tag>
                      </a-tooltip>

                      <i class="icon-shanchuguanbicha1 iconfont" @click="removeVariableName2(index)"></i>
                    </div>
                  </a-form-item>
                </a-col>
                <a-col span="6">
                  <a-dropdown
                    placement="bottomRight"
                    overlayClassName="variable"
                    :trigger="getOutputList(item.variableCategory) ? ['click'] : []">
                    <a-button class="variable-btn" :disabled="!getOutputList(item.variableCategory)?.length">
                      {{ t('message.variable') }}
                      <i class="icon-xialajiantouxiao iconfont"></i>
                    </a-button>
                    <template #overlay>
                      <div class="variable-list" v-if="getOutputList(item.variableCategory)?.length">
                        <ul>
                          <li
                            v-for="(output, index2) in getOutputList(item.variableCategory)"
                            :key="index2"
                            @click="outputItemClick2(output, index)">
                            <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                              <template #title>{{ output.number }}</template>
                              <a-tag color="#EEEEEE" class="variable-list-tag-number">{{ output.number }}</a-tag>
                            </a-tooltip>
                            <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                              <template #title>{{ output.name || output.variable }}</template>
                              <a-tag color="#F7F7F7" class="variable-list-tag-name">
                                {{ output.name || output.variable }}
                              </a-tag>
                            </a-tooltip>
                            <a-tooltip placement="bottomLeft" destroyTooltipOnHide>
                              <template #title>
                                {{ t('message.taskTypeArrangement.variableCategoryList.' + output.variableCategory) }}
                              </template>
                              <span class="variable-list-tag-type">
                                {{ t('message.taskTypeArrangement.variableCategoryList.' + output.variableCategory) }}
                              </span>
                            </a-tooltip>

                            <i
                              class="icon-gou iconfont"
                              v-if="item?.nodeCode == output.number && item?.variableName == (output.name || output.variable)"></i>
                          </li>
                        </ul>
                      </div>
                    </template>
                  </a-dropdown>
                </a-col>
              </a-row>
              <a-row class="edit-row-btn" justify="end">
                <!-- 取消 -->
                <a-col :span="6">
                  <a-button @click="cancelInputVariable(index)">{{ t('message.cancel') }}</a-button>
                </a-col>
                <!-- 确定 -->
                <a-col :span="6">
                  <a-button type="primary" @click="confirmInputVariable(item.type, index, 'code')">{{ t('message.confirm') }}</a-button>
                </a-col>
              </a-row>
            </div>
          </div>
          <a-row justify="center" class="event-form-add" @click="addInputVariable('')">
            <i class="icon-zengjia iconfont"></i>
            <!-- 新增 -->
            {{ t('message.add') }}
          </a-row>
        </div>
      </a-form>
    </template>
  </div>
</template>

<script lang="ts" setup>
import type { IOutput, InputVariableList, INodeConfig } from '@/hooks/use-node'
import type { PropType, Ref } from 'vue'
import useNodeForm from '@/hooks/use-node-form'
import { useLocale, deepClone } from 'youibot-plus'
import { isRequire } from '@/utils/form-rules'
import { Modal } from 'ant-design-vue'
import { messageTip, evetBlur } from '@/utils'
const { t } = useLocale()
const { isObject, isMultiple, isGetOptiosList, getFieldNames } = useNodeForm()
const emits = defineEmits(['update:formData', 'change'])
const props = defineProps({
  formData: {
    type: Object as PropType<InputVariableList>,
    required: true
  },
  options: {
    type: Array<never>
  },
  fieldNames: {
    type: Object
  },
  code: {
    type: String,
    default: ''
  },
  variableCategory: {
    type: String,
    default: ''
  }
})
const variableCategoryList = [
  {
    label: t('message.taskTypeArrangement.variableCategoryList.Text'),
    value: 'Text'
  }, //文本 str
  {
    label: t('message.taskTypeArrangement.variableCategoryList.Number'),
    value: 'Number'
  }, //数字
  {
    label: t('message.taskTypeArrangement.variableCategoryList.Common'),
    value: 'Common'
  } //通用
]
const state = reactive({
  inputVariableList: [] as unknown as InputVariableList[],
  inputVariableListBackup: [] as unknown as InputVariableList[]
})
let formRef = ref()
const outputs = inject<Ref<IOutput[]>>('output')
const nodeConfig = inject<Ref<INodeConfig>>('nodeConfig')
let isInTaskParams = ref(true)
const formData = ref<InputVariableList>(props.formData)
// const formData = computed(() => {
//   let data = deepClone(props.formData)
//   const { nodeCode, variableName } = props.formData

//   if (nodeConfig) {
//     const { params, templateType } = nodeConfig.value
//     // 是否在 任务输入参数中 普通模板 如果参数不在任务属性的输入参数中需要置空
//     if (params && nodeCode == 'Task' && templateType == 'Common') {
//       const list = params.find(item => item.variable == variableName)
//       isInTaskParams.value = !!list
//       if (!isInTaskParams.value) {
//         data.nodeCode = ''
//       }
//     }
//   }

//   return data
// })
watchEffect(() => {
  // 阻止在输入变量时的回调响应
  // if (props.formData.attribute) {
  //   formData.value.attribute = props.formData.attribute
  // }

  // if (code === props.code) {
  //   return
  // }

  let data = deepClone(props.formData)
  const { nodeCode, variableName } = props.formData

  if (nodeConfig) {
    const { params, templateType } = nodeConfig.value
    // 是否在 任务输入参数中 普通模板 如果参数不在任务属性的输入参数中需要置空
    if (params && nodeCode == 'Task' && templateType == 'Common') {
      const list = params.find(item => item.variable == variableName)
      isInTaskParams.value = !!list
      if (!isInTaskParams.value) {
        data.nodeCode = ''
      }
    }
  }
  formData.value = data
})
watchEffect(() => {
  if (props.formData) {
    let data = deepClone(props.formData)
    const { type } = data
    if (type === 'Combination') {
      state.inputVariableList = data.defaultValue ? (data.defaultValue as InputVariableList[]) : []
      state.inputVariableListBackup = deepClone(toRaw(state.inputVariableList))
    }
  }
})
const handleChangeValue = (type?: string, value?: any, option?: any) => {
  formData.value.value = formData.value.defaultValue
  formData.value.source = 'Fixed'
  formData.value.variableName = ''
  formData.value.variable = ''
  formData.value.nodeCode = ''
  emits('update:formData', toRaw(formData.value))
  emits('change', type, value, option)
}

const outputItemClick = (item: IOutput) => {
  formData.value.value = item.defaultValue as string
  formData.value.source = item.source ? item.source : 'Node'
  formData.value.variableName = item.name || item.variable
  formData.value.variable = item.code || item.variable
  formData.value.nodeCode = item.number
  emits('update:formData', toRaw(formData.value))
  emits('change')
}
const outputItemClick2 = (item: IOutput, index: number) => {
  state.inputVariableList[index].value = item.defaultValue as string
  state.inputVariableList[index].source = item.source ? item.source : 'Node'
  state.inputVariableList[index].variableName = item.name || item.variable
  state.inputVariableList[index].variable = item.code || item.variable
  state.inputVariableList[index].nodeCode = item.number
  // emits('update:formData', toRaw(formData.value))
  // emits('change')
}

const removeVariableName = () => {
  //清掉全局变量时，原本的默认值也要一起清掉
  formData.value.defaultValue = isMultiple(formData.value.type) ? [] : ''
  formData.value.value = formData.value.defaultValue
  formData.value.source = 'Fixed'
  formData.value.variableName = ''
  formData.value.nodeCode = ''
  formData.value.variable = ''
  emits('update:formData', toRaw(formData.value))
  emits('change')
}
const removeVariableName2 = (index: number) => {
  //清掉全局变量时，原本的默认值也要一起清掉
  state.inputVariableList[index].defaultValue = isMultiple(formData.value.type) ? [] : ''
  state.inputVariableList[index].value = ''
  state.inputVariableList[index].source = 'Fixed'
  state.inputVariableList[index].variableName = ''
  state.inputVariableList[index].nodeCode = ''
  state.inputVariableList[index].variable = ''
}
const addInputVariable = (source: string) => {
  let data = {
    type: 'Default',
    variable: '',
    defaultValue: '',
    isEdit: true,
    source: source,
    value: '',
    nodeCode: '',
    options: [],
    variableName: '',
    number: '',
    condition: '',
    variableCategory: 'Text',
    code: ''
  }
  state.inputVariableList.push(data as InputVariableList)
  state.inputVariableListBackup.push({
    ...data,
    isEdit: false
  } as InputVariableList)
}
const cancelInputVariable = (index: number) => {
  const { type, code } = state.inputVariableListBackup[index]
  if (!type || !code) {
    state.inputVariableListBackup.splice(index, 1)
    state.inputVariableList.splice(index, 1)
  } else {
    state.inputVariableList[index] = state.inputVariableListBackup[index]
    state.inputVariableList[index].isEdit = false
  }
}

const changeType = (type: string, index: number) => {
  if (type === 'variableCategory') {
    state.inputVariableList[index].type = 'Default'
  }
  state.inputVariableList[index].variable = ''
  state.inputVariableList[index].source = 'Fixed'
  state.inputVariableList[index].defaultValue = ''
  state.inputVariableList[index].value = ''
  state.inputVariableList[index].nodeCode = ''
  state.inputVariableList[index].variableName = ''
}
const confirmInputVariable = (type?: string, index?: number, valueName?: string) => {
  const params = JSON.parse(JSON.stringify(toRaw(state.inputVariableList)))
  if (index !== undefined) {
    const key = valueName ? valueName : 'code'
    formRef.value
      ?.validateFields([
        ['inputVariableList', index, key],
        ['inputVariableList', index, 'value']
      ])
      .then(() => {
        if (type) {
          const { defaultValue } = params[index]
          let i = params.findIndex((item: InputVariableList) => item[key] == params[index][key])
          if (i >= 0 && i !== index) {
            // 变量名重复
            messageTip(t('message.taskTypeArrangement.variableNameDuplication'), 'error')
            params[index].isEdit = true
            return false
          }
          if (typeof defaultValue == 'object') {
            params[index].defaultValue = defaultValue
          }
          if (!defaultValue) {
            if (type == 'Bool') {
              params[index].defaultValue = false
            }
          }
        }
        if (!type) {
          params.splice(index, 1)
        }
        requestSuccess(params, type, index)
      })
  } else {
    if (formRef.value) {
      formRef.value?.validate().then(() => {
        requestSuccess(params, type, index)
      })
    } else {
      // 属性未保存，请使用“Enter”键确认
      messageTip(t('message.taskTypeArrangement.thePropertiesAreNotSavedPressEnterToConfirm'), 'warning')
    }
  }
}
const requestSuccess = (params: InputVariableList[], type?: string, index?: number) => {
  if (!type && index !== undefined) {
    state.inputVariableList.splice(index, 1)
  }
  if (type && index !== undefined) {
    state.inputVariableList[index].isEdit = false
    params[index].isEdit = false
    state.inputVariableList[index].defaultValue = params[index].value
    state.inputVariableList[index].value = params[index].value
  }
  state.inputVariableListBackup = params
  formData.value.defaultValue = params
  handleChangeValue()
  // emits('formDataChange', data)
}
const defaultValueHtml = (item: InputVariableList) => {
  const { type, value } = item
  if (item.nodeCode) {
    return `${item.nodeCode}.${item?.variableName}`
  }

  return value
}
const removeInputVariable = (index: number) => {
  Modal.confirm({
    content: `${t('message.deleteOrNot')}？${t('message.messageCannotReturn')}`,
    zIndex: 2100,
    onOk: () => {
      confirmInputVariable('', index)
    }
  })
}
const editInputVariable = (index: number) => {
  const { defaultValue, type } = state.inputVariableList[index]

  state.inputVariableList[index].isEdit = true
  state.inputVariableListBackup[index] = deepClone(state.inputVariableList[index])
}
const getOutputList = (variableCategory: string) => {
  return outputs?.value?.filter(item => {
    return (
      variableCategory == 'Common' ||
      item.variableCategory == 'Common' ||
      item.variableCategory == variableCategory ||
      isObject(item.variableCategory)
    )
  })
}
</script>
<style lang="less">
.event-form {
  .ant-col-4 {
    line-height: 32px;
  }

  .ant-col-8 {
    line-height: 32px;
  }

  .ant-col-5 {
    line-height: 32px;
  }

  .ant-col-3 {
    line-height: 32px;
  }

  .ant-input-number,
  .ant-input-number-group-wrapper {
    width: 100%;
  }

  .ant-radio-group {
    margin-bottom: 8px;
  }

  .ant-form-item {
    // margin-bottom: 0;

    .icon-jieshi {
      margin-left: 12px;
    }
  }

  &-title {
    height: 22px;
    margin: 0 20px 12px;
    padding-left: 12px;
    color: #000;
    line-height: 22px;
    border-left: 2px solid @primary-color;
  }

  &-header {
    padding: 0 12px;
    color: #a6a6a6;

    .ant-col {
      height: 32px;
      line-height: 32px;
    }
  }

  .event-form-item {
    margin: 0 20px;
  }

  &-add {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    margin: 12px 20px;
    color: #4f4f4f;
    line-height: 32px;
    border: 1px dashed #4f4f4f;
    border-radius: 4px;

    > i {
      margin-right: 4px;
    }
  }

  i {
    + i {
      margin-left: 8px;
    }
  }

  .row-list {
    min-height: 30px;
    padding: 4px 12px;
    color: #000;

    .ant-col {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &:hover {
      background: rgb(238 116 42 / 10%);
    }
  }

  .edit-row {
    padding: 12px;
    border: 1px solid #d9d9d9;

    .ant-select {
      width: 100%;
    }

    &-btn {
      margin-top: 12px;
      text-align: center;
    }

    &-default {
      margin-top: 12px;
    }
  }

  .input-number {
    width: 100%;
  }
}

.variable {
  &-list {
    width: 280px;
    max-height: 400px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid #ededed;
    box-shadow: 4px 4px 8px rgb(189 189 189 / 25%);

    ul {
      margin-bottom: 0;
    }

    li {
      position: relative;
      display: flex;
      align-items: center;
      height: 28px;
      padding: 0 14px;
      line-height: 28px;
      cursor: pointer;

      &:hover {
        background: rgb(238 116 42 / 10%);
      }

      .ant-tag {
        margin-right: 0;
        border-radius: 0;
      }

      .icon-gou {
        position: absolute;
        right: 12px;
        color: @primary-color;
      }
    }

    &-tag {
      &-number {
        width: 35px;
        height: 22px;
        padding: 0 4px;
        overflow: hidden;
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;

        &.ant-tag-has-color {
          color: #979797;
        }
      }

      &-name {
        width: 120px;
        height: 22px;
        padding: 0 4px;
        overflow: hidden;
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;

        &.ant-tag-has-color {
          color: #000;
        }
      }

      &-type {
        margin-left: 16px;
        color: #9c9c9c;
      }
    }
  }
}

.variable-btn {
  width: 100%;
  padding: 0 10px;
  line-height: 1;
}
</style>
<style lang="less" scoped>
.task-form {
  &-title {
    height: 22px;
    margin: 30px 20px 12px;
    padding-left: 12px;
    color: #000;
    line-height: 22px;
    border-left: 2px solid @primary-color;
  }
}
</style>
