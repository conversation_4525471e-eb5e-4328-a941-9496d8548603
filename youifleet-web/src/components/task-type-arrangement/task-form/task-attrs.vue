<template>
  <a-form :labelAlign="'left'" autocomplete="off" :model="state" layout="vertical" ref="formActionRef" class="task-form" :labelWrap="true">
    <!-- 编码 -->
    <a-form-item :label="t('message.encoding')" name="code" :rules="[isLength(1, 20), englishFirst(), isRequire()]">
      <a-input v-model:value="state.code" @blur="nameTest" @pressEnter="evetBlur"></a-input>
    </a-form-item>
    <!-- 类型名称 -->
    <a-form-item :label="t('message.typeName')" name="name" :rules="[isLength(1, 20), isRequire()]">
      <a-input v-model:value="state.name" @blur="nameTest" @pressEnter="evetBlur"></a-input>
    </a-form-item>
    <!-- 优先级 -->
    <a-form-item :label="t('message.priority')" name="priority" :rules="isRequire()">
      <a-select class="input-number" v-model:value="state.priority" :options="optionsPriority" @change="nameTest" />
    </a-form-item>
    <!-- 快捷入口 -->

    <a-form-item>
      <template #label>
        <span>{{ t('message.taskTypeArrangement.quickAccess') }}</span>
        <a-tooltip placement="top">
          <i class="icon-jieshi iconfont"></i>
          <template #title>
            <span>{{ t('message.taskTypeArrangement.quickAccessTip') }}</span>
          </template>
        </a-tooltip>
      </template>
      <a-switch v-model:checked="state.isQuickVisit" @change="nameTest" />
    </a-form-item>
    <!-- Pda -->

    <a-form-item>
      <template #label>
        <span>{{ t('message.taskTypeArrangement.pda') }}</span>
        <a-tooltip placement="top">
          <i class="icon-jieshi iconfont"></i>
          <template #title>
            <span>{{ t('message.taskTypeArrangement.pdaTip') }}</span>
          </template>
        </a-tooltip>
      </template>
      <a-switch v-model:checked="state.isPdaVisit" @change="nameTest" />
    </a-form-item>
    <!-- 事件无输入变量 -->

    <!-- 任务变量（输入） -->
    <p class="task-form-title">{{ t('message.taskTypeArrangement.taskVariableInput') }}</p>
    <a-row class="task-form-header task-form-item">
      <!-- 类型 -->
      <a-col class="ellipsis" :span="4" :title="t('message.type')">{{ t('message.type') }}</a-col>

      <!-- 表单 -->
      <a-col class="ellipsis" :span="4" :title="t('message.form')">{{ t('message.form') }}</a-col>

      <!-- 变量名 -->
      <a-col class="ellipsis" :span="8" :title="t('message.taskTypeArrangement.variableName')">
        {{ t('message.taskTypeArrangement.variableName') }}
      </a-col>
      <!-- 默认值 -->
      <a-col class="ellipsis" :span="5" :title="t('message.defaultValue')">{{ t('message.defaultValue') }}</a-col>
    </a-row>
    <div v-for="(item, index) in state.inputVariableList" :key="index" class="task-form-item">
      <a-row v-if="!item.isEdit" class="row-list">
        <a-col :span="4">
          <a-tooltip placement="topLeft" :title="t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory)">
            {{ t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory) }}
          </a-tooltip>
        </a-col>
        <a-col :span="4">
          <a-tooltip placement="topLeft" :title="t('message.taskTypeArrangement.interfaceInputFormType.' + item.type)">
            {{ t('message.taskTypeArrangement.interfaceInputFormType.' + item.type) }}
          </a-tooltip>
        </a-col>

        <a-col :span="8">
          <a-tooltip placement="topLeft" :title="item.variable">{{ item.variable }}</a-tooltip>
        </a-col>
        <a-col :span="5">
          <a-tooltip placement="topLeft" :title="defaultValueHtml(item)">
            <span v-html="defaultValueHtml(item)"></span>
          </a-tooltip>
        </a-col>
        <a-col :span="3">
          <i class="icon-compile iconfont" @click="editInputVariable(index)"></i>
          <i class="icon-a-shanchu_jiankongfuben22 iconfont" @click="removeInputVariable(index)"></i>
        </a-col>
      </a-row>
      <div v-else class="edit-row">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item>
              <a-select
                v-model:value="item.variableCategory"
                :options="variableCategoryList"
                @change="changeType('variableCategory', index)"></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <a-select v-model:value="item.type" :options="inputVariableTypeList" @change="changeType('type', index)"></a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="edit-row-default" :gutter="16">
          <a-col :span="12">
            <a-form-item :name="['inputVariableList', index, 'variable']" :rules="isRequire()">
              <a-input
                v-model:value="item.variable"
                :placeholder="t('message.taskTypeArrangement.variableName')"
                @pressEnter="evetBlur"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <task-form-type
                :type="item.variableCategory === 'Number' ? item.variableCategory : item.type"
                v-model:value="item.defaultValue"
                :options="isGetOptiosList(item.type) || item.selectionBoxParam"
                :fieldNames="getFieldNames(item.type)"
                :placeholder="t('message.defaultValue')"></task-form-type>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="edit-row-btn" justify="end">
          <!-- 取消 -->
          <a-col :span="6">
            <a-button @click="cancelInputVariable(index)">{{ t('message.cancel') }}</a-button>
          </a-col>
          <!-- 确定 -->
          <a-col :span="6">
            <a-button type="primary" @click="confirmInputVariable(item.type, index)">{{ t('message.confirm') }}</a-button>
          </a-col>
        </a-row>
      </div>
    </div>
    <a-row justify="center" class="task-form-add" @click="addInputVariable">
      <i class="icon-zengjia iconfont"></i>
      <!-- 新增 -->
      {{ t('message.add') }}
    </a-row>

    <template v-if="outputs?.length">
      <!-- 任务变量（输出） -->
      <p class="task-form-title">{{ t('message.taskTypeArrangement.taskVariableOutput') }}</p>
      <a-row class="task-form-header task-form-item">
        <!-- 表单 -->
        <a-col class="ellipsis" :span="4" :title="t('message.type')">{{ t('message.type') }}</a-col>
        <!-- 类型 -->
        <a-col class="ellipsis" :span="6" :title="t('message.form')">{{ t('message.form') }}</a-col>

        <!-- 变量名 -->
        <a-col class="ellipsis" :span="9" :title="t('message.taskTypeArrangement.variableName')">
          {{ t('message.taskTypeArrangement.variableName') }}
        </a-col>
        <!-- 所属节点 -->
        <a-col class="ellipsis" :span="5" :title="t('message.taskTypeArrangement.owningNode')">
          {{ t('message.taskTypeArrangement.owningNode') }}
        </a-col>
      </a-row>
      <template v-for="item in outputs">
        <a-row
          class="row-list task-form-item"
          v-if="props.formData.templateType !== 'Event' || (props.formData.templateType === 'Event' && item.number !== 'Task')">
          <a-col :span="4">
            <a-tooltip placement="topLeft" :title="t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory)">
              {{ t('message.taskTypeArrangement.variableCategoryList.' + item.variableCategory) }}
            </a-tooltip>
          </a-col>
          <a-col :span="6">
            <a-tooltip placement="topLeft" :title="t('message.taskTypeArrangement.variableTypeList.' + item.type)">
              {{ t('message.taskTypeArrangement.variableTypeList.' + item.type) }}
            </a-tooltip>
          </a-col>
          <a-col :span="9">
            <a-tooltip placement="topLeft" :title="item.name">{{ item.name }}</a-tooltip>
          </a-col>
          <a-col :span="5">
            <a-tooltip placement="topLeft" :title="item.number">{{ item.number }}</a-tooltip>
          </a-col>
        </a-row>
      </template>
    </template>
  </a-form>
</template>

<script lang="ts" setup>
import type { IOutput, InputVariableList } from '@/hooks/use-node'
import { isRequire, englishFirst, isLength } from '@/utils/form-rules'
import { FormInstance, Modal, message } from 'ant-design-vue'
import { checkIsJSON, debounce, evetBlur, messageTip } from '@/utils'
import { deepClone } from 'youibot-plus'
import useNodeForm from '@/hooks/use-node-form'
import { getQuery } from '@/router'

import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const emit = defineEmits(['change'])
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})
const outputs = inject<IOutput[]>('output')
const formActionRef = ref<FormInstance>()
const { initOptions, isGetOptiosList, getFieldNames, isMultiple, isNumber, isSelect } = useNodeForm()
initOptions()
const optionsPriority = [
  // { label: t('message.taskTypeArrangement.priorityState.5'), value: 5 }, //最高
  { label: t('message.taskTypeArrangement.priorityState.4'), value: 4 }, //较高
  { label: t('message.taskTypeArrangement.priorityState.3'), value: 3 }, //高
  { label: t('message.taskTypeArrangement.priorityState.2'), value: 2 }, //中
  { label: t('message.taskTypeArrangement.priorityState.1'), value: 1 } //低
]
const variableCategoryList = [
  { label: t('message.taskTypeArrangement.variableCategoryList.Text'), value: 'Text' }, //文本 str
  { label: t('message.taskTypeArrangement.variableCategoryList.Number'), value: 'Number' }, //数字
  { label: t('message.taskTypeArrangement.variableCategoryList.Common'), value: 'Common' } //通用
]
let inputVariableTypeList = ref([
  { label: t('message.taskTypeArrangement.variableTypeList.Default'), value: 'Default' },
  { label: t('message.vehicle'), value: 'VehicleCode' },
  { label: t('message.point'), value: 'MarkerCode' },
  { label: t('message.taskTypeArrangement.variableTypeList.Json'), value: 'Json' },
  { label: t('message.actionSetting.componentOptions.MultiText'), value: 'MultiText' }
])
const state = reactive({
  inputVariableList: [] as unknown as InputVariableList[],
  inputVariableListBackup: [] as unknown as InputVariableList[],
  code: '',
  name: '',
  priority: '',
  id: '',
  isQuickVisit: false,
  eventType: 'Interface',
  isPdaVisit: false
})

watchEffect(() => {
  if (props.formData) {
    state.id = props.formData.id
    state.code = props.formData.code
    state.name = props.formData.name
    state.priority = props.formData.priority
    state.isQuickVisit = props.formData.isQuickVisit
    const params = JSON.parse(JSON.stringify(toRaw(props.formData.params)))

    state.inputVariableList = deepClone(params)
    state.inputVariableListBackup = deepClone(params)
    state.eventType = props.formData.eventType
    state.isPdaVisit = props.formData.isPdaVisit
  }
})
const addInputVariable = () => {
  state.inputVariableList.push({
    type: 'Default',
    variable: '',
    defaultValue: '',
    isEdit: true,
    variableCategory: 'Text'
  } as InputVariableList)
  state.inputVariableListBackup.push({
    type: 'Default',
    variable: '',
    defaultValue: '',
    isEdit: false,
    variableCategory: 'Text'
  } as InputVariableList)
  setInputVariableTypeList(state.inputVariableList.length - 1)
}
const editInputVariable = (index: number) => {
  setInputVariableTypeList(index)

  const { defaultValue, type } = state.inputVariableList[index]
  if (isMultiple(type) && typeof defaultValue === 'string') {
    state.inputVariableList[index].defaultValue = defaultValue.split(',')
  }
  state.inputVariableList[index].isEdit = true
  state.inputVariableListBackup[index] = deepClone(state.inputVariableList[index])
}
const cancelInputVariable = (index: number) => {
  const { type, variable } = state.inputVariableListBackup[index]
  if (!type || !variable) {
    state.inputVariableListBackup.splice(index, 1)
    state.inputVariableList.splice(index, 1)
  } else {
    state.inputVariableList[index] = state.inputVariableListBackup[index]
    state.inputVariableList[index].isEdit = false
  }
}
const setInputVariableTypeList = (index: number) => {
  if (['Common', 'Text'].includes(state.inputVariableList[index].variableCategory)) {
    inputVariableTypeList.value = [
      { label: t('message.taskTypeArrangement.variableTypeList.Default'), value: 'Default' },
      { label: t('message.vehicle'), value: 'VehicleCode' },
      { label: t('message.point'), value: 'MarkerCode' },
      { label: t('message.taskTypeArrangement.variableTypeList.Json'), value: 'Json' },
      { label: t('message.actionSetting.componentOptions.MultiText'), value: 'MultiText' }
    ]
  } else {
    inputVariableTypeList.value = [{ label: t('message.taskTypeArrangement.variableTypeList.Default'), value: 'Default' }]
  }
}
const removeInputVariable = (index: number) => {
  Modal.confirm({
    content: `${t('message.deleteOrNot')}？${t('message.messageCannotReturn')}`,
    zIndex: 2100,
    onOk: () => {
      confirmInputVariable('', index)
    }
  })
}
const changeType = (type: string, index: number) => {
  if (type === 'variableCategory') {
    setInputVariableTypeList(index)
    state.inputVariableList[index].type = 'Default'
  }

  state.inputVariableList[index].defaultValue = ''
}
const confirmInputVariable = (type?: string, index?: number) => {
  const params = JSON.parse(JSON.stringify(toRaw(state.inputVariableList)))
  if (index !== undefined) {
    formActionRef.value
      ?.validateFields([
        ['inputVariableList', index, 'variable'],
        ['inputVariableList', index, 'defaultValue']
      ])
      .then(() => {
        if (type) {
          const { defaultValue, variable } = params[index]
          let i = params.findIndex((item: InputVariableList) => item.variable == variable)
          if (i >= 0 && i !== index) {
            // 变量名重复
            message.error(t('message.taskTypeArrangement.variableNameDuplication'))
            params[index].isEdit = true
            return false
          }
          if (typeof defaultValue == 'object') {
            params[index].defaultValue = defaultValue
          }
          if (!defaultValue) {
            if (type == 'Bool') {
              params[index].defaultValue = false
            }
          }
        }
        if (!type) {
          params.splice(index, 1)
        }
        requestSuccess(params, type, index)
      })
  } else {
    if (formActionRef.value) {
      formActionRef.value?.validate().then(() => {
        requestSuccess(params, type, index)
      })
    } else {
      // 属性未保存，请使用“Enter”键确认
      messageTip(t('message.taskTypeArrangement.thePropertiesAreNotSavedPressEnterToConfirm'), 'warning')
    }
  }
}
const requestSuccess = (params: InputVariableList[], type?: string, index?: number) => {
  if (!type && index !== undefined) {
    state.inputVariableList.splice(index, 1)
  }
  if (type && index !== undefined) {
    state.inputVariableList[index].isEdit = false
    params[index].isEdit = false
    state.inputVariableList[index].defaultValue = params[index].defaultValue
  }
  const data = {
    code: state.code,
    name: state.name,
    priority: state.priority,
    id: state.id || getQuery('id'),
    params: params,
    type: '0',
    templateType: props.formData.templateType,
    isQuickVisit: state.isQuickVisit,
    eventType: state.eventType,
    isPdaVisit: state.isPdaVisit
  }
  emit('change', data)
}
// 名称防抖校验
const nameTest = debounce(confirmInputVariable, 0)

const defaultValueHtml = (item: { type: string; defaultValue: string | Array<string> | number | InputVariableList[] }) => {
  const { type, defaultValue } = item
  if (!item.defaultValue) {
    if (type === 'Bool') {
      return false
    }
    return ''
  }
  if (isMultiple(type) && Array.isArray(defaultValue)) {
    return defaultValue.join(',')
  }
  if (isSelect(type) && typeof defaultValue == 'string') {
    const optionsList = isGetOptiosList(type)
    const { label, value } = getFieldNames(type)
    let labelName: string[] = []
    let valueList = defaultValue.split(',')
    optionsList?.forEach(item => {
      const valueRes = item[value]
      if (valueList.includes(valueRes)) {
        labelName.push(item[label])
      }
    })

    return labelName.join(',')
  }
  return defaultValue
}

defineExpose({
  formActionRef
})
</script>
<style>
.action-form {
  .ant-drawer-body {
    padding: 0 !important;
  }
}
</style>
<style lang="less" scoped>
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.task-form {
  .ant-form-item {
    margin-bottom: 12px;

    .icon-jieshi {
      margin-left: 12px;
    }
  }

  &-title {
    height: 22px;
    margin: 0 20px 12px;
    padding-left: 12px;
    color: #000;
    line-height: 22px;
    border-left: 2px solid @primary-color;
  }

  &-header {
    padding: 0 12px;
    color: #a6a6a6;

    .ant-col {
      height: 32px;
      line-height: 32px;
    }
  }

  .task-form-item {
    margin: 0 20px;
  }

  &-add {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    margin: 12px 20px;
    color: #4f4f4f;
    line-height: 32px;
    border: 1px dashed #4f4f4f;
    border-radius: 4px;

    > i {
      margin-right: 4px;
    }
  }

  i {
    + i {
      margin-left: 5px;
    }
  }

  .row-list {
    min-height: 30px;
    padding: 4px 12px;
    color: #000;

    .ant-col {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &:hover {
      background: rgb(238 116 42 / 10%);
    }
  }

  .edit-row {
    padding: 12px;
    border: 1px solid #d9d9d9;

    .ant-select {
      width: 100%;
    }

    &-btn {
      margin-top: 12px;
      text-align: center;
    }

    &-default {
      margin-top: 12px;
    }
  }

  .input-number {
    width: 100%;
  }

  .task-variable-input {
    padding-bottom: 15px;
  }
}
</style>
