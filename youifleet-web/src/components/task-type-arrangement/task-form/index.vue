<template>
  <a-drawer class="action-form" :title="''" width="400px" :closable="false" :open="true" :mask="false" :keyboard="false">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" :tab="tabName">
        <TaskAttrs v-if="formData && formData?.type === '0'" :formData="formData" @change="formDataChange" ref="taskFrom"></TaskAttrs>
        <ConditionForm
          v-else-if="formData && ['While', 'Judge'].includes(formData?.type)"
          :formData="formData"
          @formDataChange="formDataChange"></ConditionForm>
        <taskEventForm
          v-else-if="formData && ['CancelTask', 'CreateTask'].includes(formData?.type)"
          :formData="formData"
          @formDataChange="formDataChange"></taskEventForm>
        <CommonModule v-else-if="formData && formData?.type !== '0'" :formData="formData" @formDataChange="formDataChange"></CommonModule>
      </a-tab-pane>
    </a-tabs>
  </a-drawer>
</template>

<script lang="ts" setup>
import type { INodeConfig } from '@/hooks/use-node'
import useStore from '@/stores'
import CommonModule from './common.vue'
import ConditionForm from './condition.vue'
import TaskAttrs from './task-attrs.vue'
import taskEventForm from './task-event-form.vue'
import useNodeForm from '@/hooks/use-node-form'

import { useLocale } from 'youibot-plus'
const { t } = useLocale()
const activeKey = ref('1')
const tabName = ref(t('message.taskTypeArrangement.taskAttribute')) //任务属性
const taskFrom = ref()
const { nodeStore } = useStore()
const { isEventType } = useNodeForm()
// 加载下拉框数据
const emit = defineEmits(['formDataChange'])
defineExpose({ taskFrom })
const formData = computed(() => {
  return nodeStore.getFormData
})
watchEffect(() => {
  const { type } = nodeStore.getFormData
  if (type === '0') {
    tabName.value = t('message.taskTypeArrangement.taskAttribute') //任务属性
  } else if (['While', 'Judge'].includes(type)) {
    tabName.value = t('message.taskTypeArrangement.conditionalAttribute') //条件属性
  } else {
    tabName.value = t('message.taskTypeArrangement.nodeAttribute') //节点属性
  }
})
const formDataChange = (data: INodeConfig) => {
  // console.log(formData.value, data)
  Object.assign(formData.value, data)
  nodeStore.setFormData(formData.value)
  emit('formDataChange', formData.value)
}

onBeforeUnmount(() => {})
</script>
<style lang="less">
@formWidth: 400px;
@topHeight: 48px;

.ant-drawer-content.action-form {
  top: @topHeight;
  width: @formWidth;
  height: 100%;

  .ant-drawer-content-wrapper {
    border-left: 1px solid #d8d8d8;
    box-shadow: none;

    .ant-drawer-body {
      padding: 0 !important;
    }
  }

  .ant-tabs {
    height: 100%;

    .ant-tabs-nav {
      height: 34px;
      margin: 0;

      .ant-tabs-nav-list {
        padding: 0 20px;
      }

      .ant-tabs-tab + .ant-tabs-tab {
        margin-left: 20px;
      }
    }

    .ant-tabs-content-holder {
      height: 100%;
    }

    .ant-tabs-content {
      height: 100%;
    }
  }

  .ant-form {
    height: 100%;
    padding: 12px 0;
    overflow: auto;

    > .ant-form-item {
      padding: 0 20px;
    }
  }

  .common {
    .ant-form-item {
      margin-bottom: 12px;

      .icon-jieshi {
        margin-left: 12px;
      }
    }

    p.params {
      height: 22px;
      margin: 0 20px 12px;
      padding-left: 12px;
      line-height: 22px;
      border-left: 2px solid @primary-color;
    }
  }

  .input-tag {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 32px;
    padding: 0 12px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;

    .ant-tag {
      margin-right: 0;
      border-radius: 0;
    }

    .icon-shanchuguanbicha1 {
      position: absolute;
      right: 12px;
    }
  }

  .interval {
    height: 16px;
    margin-bottom: 12px;
    background-color: #f9f9f9;
  }
}
</style>
