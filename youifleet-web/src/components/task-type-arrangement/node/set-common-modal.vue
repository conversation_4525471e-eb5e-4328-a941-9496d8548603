<template>
  <!-- 设置常用节点 -->
  <my-modal
    v-model:visible="visible"
    :title="t('message.taskTypeArrangement.settingCommonNodes')"
    :width="'700px'"
    class="set-common-modal"
    :zIndex="10001"
    @cancel="cancel"
    @confirm="confirm">
    <a-form :labelAlign="'left'" :colon="false" v-if="State.checkedList">
      <a-form-item :label="t('message.taskTypeArrangement.' + index)" v-for="(item, index) in State.typeList" :key="index">
        <a-checkbox-group v-model:value="State.checkedList[index]" style="width: 100%">
          <a-checkbox :value="node.id" v-for="(node, i) in item" :key="i">{{ node.name }}</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
    </a-form>
  </my-modal>
</template>

<script lang="ts" setup>
import type { ITableRow } from '@/pages/operations/setting/node-settings/config'
import { getTaskNodeConfigGroup, postTaskNodeConfigBatchCommon } from '@/api/task'
import { useLocale } from 'youibot-plus'

interface INode {
  [key: string]: ITableRow[]
}
const { t } = useLocale()
const props = defineProps({
  visible: Boolean
})
let emits = defineEmits(['update:visible', 'change'])
const visible = computed({
  get: () => {
    return props.visible
  },
  set: val => {
    emits('update:visible', val)
  }
})
watch(
  () => props.visible,
  visible => {
    if (visible) {
      getTypeList()
    }
  }
)
const State = reactive({
  typeList: {} as INode,
  checkedList: {} as { [key: string]: Array<string> }
})

const getTypeList = () => {
  getTaskNodeConfigGroup().then(res => {
    const { data } = res

    State.typeList = {}
    ;['Common', 'Process', 'Vehicle', 'AllocationResource', 'ObtainResource', 'Communication', 'Other'].forEach((i: string) => {
      if (!data[i]) return
      const list = data[i]
      State.checkedList[i] = []
      if (i !== 'Common') {
        State.typeList[i] = data[i]
        for (let j = 0; j < list.length; j++) {
          if (list[j].isCommon) {
            State.checkedList[i].push(list[j].id)
          }
        }
      }
    })
  })
}

const cancel = function () {
  visible.value = false
}

const confirm = function () {
  let params = [] as string[]
  for (let i in State.checkedList) {
    params = params.concat(State.checkedList[i])
  }
  postTaskNodeConfigBatchCommon(params as unknown as number[]).then(() => {
    cancel()
    emits('change')
  })
}
</script>
<style lang="less">
.set-common-modal {
  .ant-form-item-label {
    width: 100px;
  }

  .ant-checkbox-wrapper {
    min-width: calc(25% - 8px);
    margin: 0 8px 0 0;
  }

  .ant-form-item {
    margin-bottom: 12px;
  }
}
</style>
