<template>
  <div class="drag-top">
    <div class="drag-top-left">
      <span class="back" @click="goBack"><i class="iconfont icon-31fanhui1"></i></span>
    </div>
    <div class="drag-top-middle">
      <span class="task-type">{{ props.nodeConfig.name }}</span>
      <p class="task-release">
        <!-- 未发布 -->
        <span class="task-release-no" v-if="props.nodeConfig.publishStatus === 'Unpublished'">
          {{ t('message.taskTypeArrangement.unpublish') }}
        </span>
        <!-- \已发布 -->
        <span class="task-release-is" v-else>{{ t('message.taskTypeArrangement.haveReleased') }}</span>
      </p>
    </div>
    <div class="drag-top-right">
      <a-button
        class="but"
        :class="[props.nodeConfig.publishStatus === 'Published' ? 'but-disabled' : '']"
        :disabled="props.nodeConfig.publishStatus === 'Published'"
        v-debounce="[() => publishTask(), 200, true]">
        {{ t('message.taskTypeArrangement.haveReleased') }}
      </a-button>
      <!-- <a-popover placement="bottom" :zIndex="2005" overlayClassName="pop-header-right">
        <template #content>
          <span>{{ t('message.set') }}</span>
        </template>
        <i class="iconfont icon-shezhi1" @click="settings.setVisible(true)"></i>
      </a-popover>
      <UserSetting popDropClass="pop-map-header-right-drop"></UserSetting> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { INodeBaseData } from '@/hooks/use-node'
import type { PropType } from 'vue'
import { postTaskTypeEnableTaskTypeId } from '@/api/task'
import { getOperateText, messageTip } from '@/utils'
import useNode from '@/hooks/use-node'
import { back, getHistoryBackRoute, getRoute, toNext } from '@/router'
import useStore from '@/stores'
import { useLocale } from 'youibot-plus'
import UserSetting from '@/components/layout/nav-view/user-setting.vue'
const { settings, nodeStore } = useStore()
const { t } = useLocale()

let props = defineProps({
  nodeConfig: {
    type: Object as PropType<INodeBaseData>,
    required: true
  }
})
let emits = defineEmits(['change'])
const { isWhileListBull, isParameterMandatory, isTaskParamNull, mistakeCodeList, whileMistakeData, cancelTaskData } = useNode()
const publishTask = () => {
  const { templateType = null, event } = props.nodeConfig
  // 清空数据
  mistakeCodeList.length = 0
  whileMistakeData.length = 0
  cancelTaskData.length = 0
  nodeStore.setMistakeData([])
  nodeStore.setWhileMistakeData([])
  nodeStore.setIsEventMistake(false)
  const isWhileListBullState = isWhileListBull(props.nodeConfig)

  // setIsEventMistake
  const isParameterMandatoryState = isParameterMandatory(props.nodeConfig)
  // 检测是否存在空循环
  if (!isWhileListBullState.while) {
    messageTip(t('message.taskTypeArrangement.publishingFailedEmptyLoopExists'), 'error')
  } else if (!isWhileListBullState.cancelTask) {
    messageTip(t('message.taskTypeArrangement.publishingFailedTaskMustContainOtherNodes'), 'error')
    // 是否存在空参数
  } else if (!isParameterMandatoryState) {
    messageTip(t('message.taskTypeArrangement.isParameterMandatoryTip'), 'error')
  } else {
    postTaskTypeEnableTaskTypeId({}, { taskTypeId: props.nodeConfig.id as unknown as number })
      .then(res => {
        messageTip(t('message.taskTypeArrangement.taskPublishingSucceeded'))
        emits('change')
        goBack()
      })
      .catch(err => {
        getOperateText(t, {
          result: 'error',
          reason: err.msg,
          isPrefix: false
        })
      })
  }
}

const goBack = () => {
  const backRoute = getHistoryBackRoute()
  const arr = ['/operations/tasks/task-type', '/operations/tasks/task-management', '/monitoring/agv-map']
  const b = arr.some(item => {
    return item.indexOf(backRoute as string) !== -1
  })
  if (b) {
    back()
    return
  }
  toNext('/operations/tasks/task-type')
}
const keydowFm = (e: KeyboardEvent) => {
  switch (e.key) {
    case 'Escape':
      goBack()
      break
    default:
      break
  }
}
document.addEventListener('keydown', keydowFm)
onBeforeUnmount(() => {
  document.removeEventListener('keydown', keydowFm)
})
</script>

<style lang="less" scoped>
.drag-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 20px 0 0;
  background-color: #2c2c2c;

  > div {
    height: 100%;
    line-height: 48px;
  }

  &-left {
    span {
      display: inline-block;
      width: 40px;
      height: 100%;
      margin: 0;
      line-height: 48px;
      text-align: center;

      &:hover {
        background: #414141;
      }

      &.back {
        position: relative;
      }

      i {
        color: #fff;
      }
    }
  }

  &-middle {
    display: flex;
    align-items: center;

    .task-type {
      color: #dbdbdb;
      font-size: 16px;
    }

    .task-release {
      margin: 0;

      > span {
        margin-left: 8px;
        padding: 0 5px;
        font-size: 12px;
        border: 1px solid;
        border-radius: 2px;
      }

      &-no {
        color: #4ca9ff;
        border-color: #4ca9ff;
      }

      &-is {
        color: #149a59;
        border-color: #149a59;
      }
    }
  }

  &-right {
    .icon-shezhi1 {
      margin-left: 14px;
      padding-right: 4px;
      color: #fff;
      font-size: 18px !important;
      vertical-align: bottom !important;

      &:hover {
        color: #ee742a;
      }
    }

    .ant-btn.but {
      width: 71px;
      height: 27px;
      padding: 0;
      color: #dbdbdb;
      font-size: 14px;
      background: #4b4b4b;
      border: #4b4b4b;

      &:hover {
        color: #fff;
        background-color: #ee742a;
      }

      &:active {
        color: #dbdbdb;
      }
    }
  }
}

.but-disabled {
  &:hover {
    color: #dbdbdb !important;
    background: #4b4b4b !important;
  }
}
</style>
