<template>
  <div class="new-add-task-type">
    <div class="search">
      <a-input
        ref="searchRef"
        @click.stop
        class="search-input"
        v-model:value="searchVal"
        :placeholder="t('message.taskTypeArrangement.searchNode')">
        <template #addonAfter><i class="iconfont icon-31sousuo"></i></template>
      </a-input>
    </div>
    <a-collapse v-model:activeKey="activeKey" ghost accordion v-if="searchVal.length == 0">
      <a-collapse-panel v-for="(item, index) in filterTypeList" :key="index" :show-arrow="false">
        <template #header>
          <div class="task-type-header" :class="{ expanded: activeKey === index }">
            <div>
              {{ t('message.taskTypeArrangement.' + index) }}
              <i class="iconfont icon-xiala"></i>
            </div>
            <a-button type="link" v-if="index == 'Common'" @click.stop="showCommonSetting()">
              <!-- 设置常用 -->
              {{ t('message.taskTypeArrangement.settingsCommonlyUsed') }}
            </a-button>
          </div>
        </template>
        <template v-for="node in item" :key="node.id">
          <a-tooltip placement="right" :mouseEnterDelay="0.5">
            <template #title>
              <span>{{ node?.notice }}</span>
            </template>
            <div
              :class="{ isdisabled: props.isDisabled.includes(node.type) }"
              @mousedown="
                e => {
                  handleMouseDown(e, node)
                }
              ">
              <div class="node-type" @click="addType(node)">
                <i :class="'iconfont ' + node.icon" v-if="node.icon && node.icon.indexOf('icon-') >= 0"></i>
                <a-image :src="node.icon" v-else-if="node.icon" :preview="false" :previewMask="false"></a-image>
                <i class="iconfont icon-tingche" v-else></i>
                <span>{{ node.name }}</span>
              </div>
            </div>
          </a-tooltip>
        </template>
      </a-collapse-panel>
    </a-collapse>
    <div class="search-result" v-else>
      <div
        class="node-type-wrap"
        v-if="searchNodeList.filter(item => item.name.toLowerCase().indexOf(searchVal.toLowerCase()) > -1).length">
        <template
          v-for="node in searchNodeList.filter(item => item.name.toLowerCase().indexOf(searchVal.toLowerCase()) > -1)"
          :key="node.id">
          <a-tooltip placement="right" :mouseEnterDelay="0.5">
            <template #title>
              <span>{{ node?.notice }}</span>
            </template>
            <div
              :class="{ isdisabled: props.isDisabled.includes(node.type) }"
              @mousedown="
                e => {
                  handleMouseDown(e, node)
                }
              ">
              <div class="node-type" @click="addType(node)">
                <i :class="'iconfont ' + node.icon" v-if="node.icon && node.icon.indexOf('icon-') >= 0"></i>
                <a-image :src="node.icon" v-else-if="node.icon" :preview="false" :previewMask="false"></a-image>
                <i class="iconfont icon-tingche" v-else></i>
                <span>{{ node.name }}</span>
              </div>
            </div>
          </a-tooltip>
        </template>
      </div>
      <div
        class="no-node-match"
        v-if="searchNodeList.filter(item => item.name.toLowerCase().indexOf(searchVal.toLowerCase()) > -1).length == 0">
        <!-- 无数据 -->
        {{ t('message.noData') }}
      </div>
    </div>
  </div>

  <SetCommonModal v-model:visible="visibleCommon" @change="getTypeList"></SetCommonModal>
</template>

<script lang="ts" setup>
import type { ITableRow } from '@/pages/operations/setting/node-settings/config'
import { getTaskNodeConfigGroup } from '@/api/task'
import useStore from '@/stores'
import { useLocale, deepClone } from 'youibot-plus'
import SetCommonModal from '../node/set-common-modal.vue'
import { INodeConfig, InputVariableList } from '@/hooks/use-node'
import emitter from '@/utils/mitts'

const { t } = useLocale()
const { nodeStore } = useStore()

let props = defineProps({
  childNodeP: {
    type: Object,
    default: () => ({})
  },
  isDisabled: {
    type: Array,
    default: () => []
  },
  // 是否绑定父节点
  parentCode: {
    type: String,
    default: ''
  }
})

interface INode {
  [key: string]: ITableRow[]
}

const normalNode = [
  {
    category: 'Other',
    enterParamCount: 0,
    icon: '',
    id: 1,
    isCommon: true,
    name: t('message.taskTypeArrangement.parallel'), //并行
    notice: t('message.taskTypeArrangement.parallelNode'), //并行节点
    outParamCount: 0,
    type: 'WHEN',
    nodeParamConfigDTOList: [],
    createDate: 'string',
    creator: 0,
    system: '',
    updateDate: '',
    updater: 0,
    isAllowSkip: false,
    retryNum: undefined,
    isAllowRetry: false,
    isAllowAlarm: false
  },
  {
    category: 'Other',
    enterParamCount: 0,
    icon: '',
    id: 2,
    isCommon: true,
    name: t('message.taskTypeArrangement.condition'), //条件
    notice: t('message.taskTypeArrangement.conditionalNode'), //条件节点
    outParamCount: 0,
    type: 'Judge',
    nodeParamConfigDTOList: [],
    createDate: 'string',
    creator: 0,
    system: '',
    updateDate: '',
    updater: 0,
    isAllowSkip: false,
    retryNum: undefined,
    isAllowRetry: false,
    isAllowAlarm: false
  },
  {
    category: 'Other',
    enterParamCount: 0,
    icon: '',
    id: 3,
    isCommon: true,
    name: t('message.taskTypeArrangement.circulation'), //循环
    notice: t('message.taskTypeArrangement.loopNode'), //循环节点
    outParamCount: 0,
    type: 'While',
    nodeParamConfigDTOList: [],
    createDate: 'string',
    creator: 0,
    system: '',
    updateDate: '',
    updater: 0,
    isAllowSkip: false,
    retryNum: undefined,
    isAllowRetry: false,
    isAllowAlarm: false
  }
]
// 显示的所有节点项
let filterTypeList = ref<INode>({})
// 可搜索的节点项（条件、并行、循环节点 + 除通用外的节点）
let searchNodeList = ref<ITableRow[]>([])
let activeKey = ref('Common')
let emits = defineEmits(['update:childNodeP', 'handleMouseDown'])
let visibleCommon = ref(false)
let insertionData: INodeConfig | null
let endData: INodeConfig | null

const showCommonSetting = () => {
  visibleCommon.value = true
}
let searchVal = ref('')
let visible = ref(false)
const searchRef = ref()
const showAddNode = () => {
  visible.value = true
  activeKey.value = 'Common'
  searchVal.value = ''
  insertionData = null
  endData = props.childNodeP && Object.keys(props.childNodeP).length > 0 ? (deepClone(toRaw(props.childNodeP)) as INodeConfig) : null
  getTypeList()
}

const getTypeList = () => {
  //防止searchNodeList的数组一直拼接上一个
  searchNodeList.value = []
  getTaskNodeConfigGroup().then(res => {
    const { data } = res
    searchNodeList.value = searchNodeList.value.concat(normalNode)
    ;['Common', 'Process', 'Vehicle', 'AllocationResource', 'ObtainResource', 'Communication', 'Other'].forEach((i: string) => {
      if (!data[i]) return
      if (i == 'Common') {
        filterTypeList.value[i] = normalNode.concat(data[i])
      } else if (i == 'Process') {
        filterTypeList.value[i] = normalNode.concat(data[i])
        searchNodeList.value = searchNodeList.value.concat(data[i])
      } else {
        filterTypeList.value[i] = data[i]
        searchNodeList.value = searchNodeList.value.concat(data[i])
      }
    })
    // console.log(filterTypeList)
  })
}

// 新增数据
const addType = (params: ITableRow) => {
  const { type, name, nodeParamConfigDTOList, ...rest } = params
  const code = nodeStore.getCode
  let data = {}

  if (type == 'Judge') {
    data = interpositionData(deepClone(insertionData), {
      name: t('message.taskTypeArrangement.condition'), //条件
      type: type,
      childNode: null,
      parentCode: props.parentCode,
      conditionNodes: [
        {
          name: t('message.taskTypeArrangement.condition'), //条件
          type: 'Judge',
          code: 'N' + (code + 1),
          childNode: null,
          parentCode: props.parentCode
        },
        {
          name: t('message.taskTypeArrangement.condition'), //条件
          type: 'Judge',
          code: 'N' + (code + 2),
          childNode: null,
          parentCode: props.parentCode
        }
      ]
    }) as INodeConfig
    insertionData = deepClone(data) as INodeConfig

    data = interpositionData(data as INodeConfig, endData) as INodeConfig
    nodeStore.setCode(code + 2)
  } else if (type == 'While') {
    data = interpositionData(deepClone(insertionData), {
      name: t('message.taskTypeArrangement.cycleCondition'), //循环条件
      code: 'N' + +(code + 1),
      type: type,
      childNode: null,
      whileList: undefined,
      parentCode: props.parentCode,
      params: []
    }) as INodeConfig
    insertionData = deepClone(data) as INodeConfig

    data = interpositionData(data as INodeConfig, endData) as INodeConfig
    nodeStore.setCode(code + 1)
  } else if (type == 'CancelTask') {
    data = interpositionData(deepClone(insertionData), {
      name: name, //取消任务
      value: ' ',
      code: 'N' + +(code + 1),
      type: type,
      params: nodeParamConfigDTOList as unknown as InputVariableList[],
      childNode: null,
      cancelTaskList: undefined,
      parentCode: props.parentCode,
      ...rest
    } as INodeConfig) as INodeConfig
    insertionData = deepClone(data) as INodeConfig

    data = interpositionData(data as INodeConfig, endData) as INodeConfig
    nodeStore.setCode(code + 1)
    //选中
    nodeStore.setFormData(
      {
        type: '',
        name: '',
        childNode: null,
        ...data
      },
      1
    )
    emitter.emit('childNode', {
      type: '',
      name: '',
      childNode: null,
      ...data
    })
  } else if (type == 'WHEN') {
    data = interpositionData(deepClone(insertionData), {
      name: t('message.taskTypeArrangement.parallel'), //并行
      type: type,
      childNode: null,
      parentCode: props.parentCode,
      conditionNodes: [
        {
          name: t('message.taskTypeArrangement.parallel') + '1', //并行
          type: '1',
          childNode: null,
          parentCode: props.parentCode
        },
        {
          name: t('message.taskTypeArrangement.parallel') + '2', //并行
          type: '1',
          childNode: null,
          parentCode: props.parentCode
        }
      ]
    }) as INodeConfig
    insertionData = deepClone(data) as INodeConfig

    data = interpositionData(data as INodeConfig, endData) as INodeConfig
  } else {
    data = interpositionData(deepClone(insertionData), {
      name,
      code: 'N' + (code + 1),
      type: type,
      parentCode: props.parentCode,
      params: nodeParamConfigDTOList as unknown as InputVariableList[],
      value: ' ',
      childNode: null,
      ...rest
    } as INodeConfig) as INodeConfig
    insertionData = deepClone(data) as INodeConfig
    data = interpositionData(data as INodeConfig, endData) as INodeConfig
    nodeStore.setCode(code + 1)
    //选中
    nodeStore.setFormData(
      {
        type: '',
        name: '',
        childNode: null,
        ...data
      },
      1
    )
    emitter.emit('childNode', {
      type: '',
      name: '',
      childNode: null,
      ...data
    })
  }
  nodeStore.setIsRefresh(true)
  // visible.value = false
  // activeKey.value = 'Common'
  // searchVal.value = ''
  searchRef.value?.focus()
  emits('update:childNodeP', data)
}
function interpositionData(data: INodeConfig | null, newData: INodeConfig | null): INodeConfig | null {
  if (data) {
    if (data.childNode) {
      data.childNode = interpositionData(data.childNode, newData)
      return data
    }

    data.childNode = newData
    return data
  }
  data = newData
  return data
}
const handleMouseDown = (e: any, node: any) => {
  emits('handleMouseDown', e, node)
}
onMounted(() => {
  showAddNode()
})
</script>
<style lang="less">
@border-color: #a6b4bf;
@add-height: 48px;

.new-add-task-type {
  .arrow-while {
    position: absolute;
    bottom: -6px;
    left: calc(50% + 0.1667px);
    border-color: @border-color transparent transparent transparent;
    border-style: solid;
    border-width: 6px;
    transform: translateX(-50%);
    content: '';
  }

  .add-node-btn-box {
    position: relative;
    display: inline-flex;
    flex-shrink: 0;
    height: @add-height;

    &::before {
      position: absolute;
      z-index: -1;
      width: 1px;
      height: 100%;
      margin: auto;
      background-color: @border-color;
      content: '';
      inset: 0;
    }

    &.has-child {
      .add-node-btn {
        margin-bottom: 5px;
      }

      &::after {
        .arrow-while();
      }
    }

    /* stylelint-disable-next-line no-descending-specificity */
    .add-node-btn {
      display: flex;
      flex-grow: 1;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      height: @add-height;
      cursor: pointer;

      .icon-con {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 14px;
        height: 14px;
        margin-bottom: 4px;
        padding: 0;
        background-color: #d2d2db;
        border: none;
        border-radius: 2px;

        i {
          padding-top: 1px;
          padding-left: 0.4px;
          color: #fff;
          font-size: 12px;
        }

        &:hover {
          background-color: #ffb78b;
        }

        &:focus {
          background-color: #ee742a;
        }
      }
    }
  }

  width: 300px;
  height: calc(100vh - 48px);

  .ant-popover-inner-content {
    padding: 0;
  }

  .ant-collapse-ghost {
    height: 100%;
    padding: 32px 16px 12px;
    overflow: auto;

    > .ant-collapse-item {
      .ant-collapse-header {
        padding: 8px 0;
      }

      > .ant-collapse-content > .ant-collapse-content-box {
        display: grid;
        grid-gap: 8px 12px;
        grid-template-columns: calc(50% - 8px) calc(50% - 8px);
        padding: 0;
      }
    }
  }

  .search {
    position: fixed;
    z-index: 10;
    width: 300px;
    background-color: #fff;

    .search-input {
      width: 100%;

      .ant-input {
        border-right: none;
      }

      .ant-input-group-addon {
        border-left: none;
      }
    }
  }

  .task-type-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    font-weight: 600;
    user-select: none;

    .icon-shezhi1 {
      margin-right: 4px;
      font-size: 14px;
    }

    i.icon-xiala {
      display: inline-block;
      transform: rotate(-90deg);
      transition: transform 0.24s;
    }

    &.expanded {
      i.icon-xiala {
        transform: rotate(0deg);
      }
    }

    .ant-btn-link {
      padding: 0;
      font-size: 12px;
    }
  }

  .no-node-match {
    height: 140px;
    padding: 12px;
    color: #a6a6a6;
    line-height: 140px;
    text-align: center;
  }

  .node-type-wrap {
    display: grid;
    grid-gap: 8px 16px;
    grid-template-columns: calc(50% - 8px) calc(50% - 8px);
    max-height: 280px;
    padding: 12px;
    overflow: auto;
  }

  .node-type {
    display: flex;
    height: 36px;
    padding-left: 8px;
    color: #000;
    font-size: 12px;
    background-color: #f4f4f5;
    border-radius: 3;
    cursor: pointer;
    user-select: none;

    .ant-image {
      width: 16px;
      height: 36px;
      margin-right: 4px;
      line-height: 36px;
    }

    .iconfont {
      margin-right: 4px;
      font-size: 16px;
      line-height: 36px;
    }

    > span {
      flex: 1;
      height: 36px;
      overflow: hidden;
      line-height: 36px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .search-result {
    padding-top: 32px;
  }

  .isdisabled {
    cursor: not-allowed;

    .node-type {
      color: #999;
      pointer-events: none;
    }
  }
}
</style>
