package com.youibot.tms.common.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static final String HOUR_SUFFIX = ":00:00";
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static final String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日/时/分/秒 如20180808080808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMddHHmmss");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 获取过去第几天的日期
     *
     * @param past
     * @return
     */
    public static Date getPastDate(int past) {
        Calendar calendar = Calendar.getInstance();
        Date date = new Date();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - past);
        return calendar.getTime();
    }


    /**
     * 获取某年的某个月有多少天
     *
     * @param str 时间
     * @return
     */
    public static int getMonthDays(String str) throws ParseException {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = sdf.parse(str);
        calendar.setTime(parse);
        int days = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        return days;
    }


    /**
     * 获取两个日期之间的月份
     *
     * @param minDate
     * @param maxDate
     * @return
     * @throws ParseException
     */
    public static List<String> getMonthBetween(Date minDate, Date maxDate) {
        ArrayList<String> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");//格式化为年月

        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        min.setTime(minDate);
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

        max.setTime(maxDate);
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }

    /**
     * 获取两个日期之间的所有日期
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<Date> getDatesBetweenUsing(Date startDate, Date endDate) {
        List<Date> datesInRange = new ArrayList<>();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(startDate);

        Calendar endCalendar = new GregorianCalendar();
        endCalendar.setTime(endDate);

        while (calendar.before(endCalendar)) {
            Date result = calendar.getTime();
            datesInRange.add(result);
            calendar.add(Calendar.DATE, 1);
        }
        return datesInRange;
    }


    /**
     * 获取传入日期的过去第几天的日期
     *
     * @param specifiedDay 日期
     * @param past         几天
     * @return
     */
    public static String getPastDate(String specifiedDay, int past) {
        Calendar calendar = Calendar.getInstance();
        Date date = null;
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(specifiedDay);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - past);
        Date today = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String result = format.format(today);
        return result;
    }

    /**
     * 获取传入日期的未来 第 past 天的日期
     *
     * @param specifiedDay 日期
     * @param past         几天
     * @return
     */
    public static String getFetureDate(String specifiedDay, int past) {
        Calendar calendar = Calendar.getInstance();
        Date date = null;
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(specifiedDay);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + past);
        Date today = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String result = format.format(today);
        return result;
    }

    /**
     * 获取传入日期的指定天数的日期列表
     *
     * @return
     */
    public static List<String> getLastWeekDayList(String specifiedDay, int days) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < days; i++) {
            String pastDate = getPastDate(specifiedDay, i);
            list.add(pastDate);
        }
        return list;
    }
    /**
     * 获取传入日期的一周日期列表
     *
     * @return
     */
    public static List<String> getLastWeekDayList(String specifiedDay) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            String pastDate = getFetureDate(specifiedDay, i);
            list.add(pastDate);
        }
        return list.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
    }

    /**
     * 获取传入日期的之前 month 个月的日期
     * @param date
     * @param month
     * @return
     */
    public static String getPastMonthDate(Date date,int month) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //设置为前3月
        calendar.add(Calendar.MONTH, -month);
        date = calendar.getTime();
        return simpleDateFormat.format(date);
    }

    /**
     * 获取传入日期的未来 month 个月的日期
     * @param date
     * @param month
     * @return
     */
    public static String getFetureMonthDate(Date date,int month,String formatStr) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formatStr);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //设置为前3月
        calendar.add(Calendar.MONTH, month);
        date = calendar.getTime();
        return simpleDateFormat.format(date);
    }


    public static class WeekDate {
        /**
         * 周一
         */
        private String monday;
        /**
         * 周日
         */
        private String sunday;

        /**
         * 获取周一的日期
         *
         * @return
         */
        public String getMonday() {
            return monday;
        }

        /**
         * 设置周一的日期
         *
         * @param monday
         */
        public void setMonday(String monday) {
            this.monday = monday;
        }

        /**
         * 获取周日的日期
         *
         * @return
         */
        public String getSunday() {
            return sunday;
        }

        /**
         * 设置周日的日期
         *
         * @param sunday
         */
        public void setSunday(String sunday) {
            this.sunday = sunday;
        }
    }

    /**
     * 获取当前是周几，返回数字
     *
     * @return
     */
    public static int getWeekInt() {
        int week = 1;
        Date today = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        int weekday = c.get(Calendar.DAY_OF_WEEK);
        if (weekday == 1) {
            week = 7;
        } else if (weekday == 2) {
            week = 1;
        } else if (weekday == 3) {
            week = 2;
        } else if (weekday == 4) {
            week = 3;
        } else if (weekday == 5) {
            week = 4;
        } else if (weekday == 6) {
            week = 5;
        } else if (weekday == 7) {
            week = 6;
        }
        return week;
    }

    /**
     * 获取上几周的周一和周日日期
     *
     * @return
     */
    public static WeekDate getLastWeekDays(int num) {
        int weekInt = getWeekInt();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String result = format.format(new Date());
        //获取上周的周一日期和周日日期
        String monday = getPastDate(result, (weekInt + 6) + 7 * ((num - 1) < 0 ? 0 : (num - 1)));
        String sunday = getPastDate(result, weekInt + 7 * ((num - 1) < 0 ? 0 : (num - 1)));
        WeekDate weekDate = new WeekDate();
        weekDate.setMonday(monday);
        weekDate.setSunday(sunday);
        return weekDate;
    }

    /**
     * 获取上周的周一和周日日期
     *
     * @return
     */
    public static WeekDate getLastWeekDays() {
        int weekInt = getWeekInt();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String result = format.format(new Date());
        //获取上周的周一日期和周日日期
        String monday = getPastDate(result, weekInt + 6);
        String sunday = getPastDate(result, weekInt);
        WeekDate weekDate = new WeekDate();
        weekDate.setMonday(monday);
        weekDate.setSunday(sunday);
        return weekDate;
    }
    /**
     * 获取某天的上周的周一和周日日期
     *
     * @param year    哪一年
     * @param weekNum 第几周
     * @return
     */
    public static WeekDate getLastWeekDays(int year, int weekNum) {
        WeekDate weekDate = new WeekDate();
        //周日
        String monday = getDateBayWeekOfYear(year, weekNum, Calendar.SUNDAY);
        //周六
        String sunday = getDateBayWeekOfYear(year, weekNum, Calendar.SATURDAY);
        weekDate.setMonday(monday);
        weekDate.setSunday(sunday);
        return weekDate;
    }
    /**
     * 获取某年的某一周的周几日期
     *
     * @param year      哪一年
     * @param week      第几周
     * @param dayOfWeek 星期几 Calendar.MONDAY 代表周一 Calendar.SUNDAY 代表周日
     * @return
     */
    public static String getDateBayWeekOfYear(int year, int week, int dayOfWeek) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.SUNDAY);
        calendar.setWeekDate(year,week,dayOfWeek);
        Date time = calendar.getTime();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sf.format(time);
        return format;
    }

    /**
     * 获取当前日期是第几周
     *
     * @param date
     * @return
     */
    public static int getWhatWeek(String date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = format.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);//设置星期一为一周开始的第一天
        calendar.setMinimalDaysInFirstWeek(4);//可以不用设置
        int weekYear = calendar.get(Calendar.YEAR);//获得当前的年
        calendar.setTime(parse);//时间戳
        int weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR);//获得当前日期属于今年的第几周
        System.out.println("第几年：" + weekYear);
        System.out.println("第几周：" + weekOfYear);
        return weekOfYear;
    }


}
