package com.youibot.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;
import java.util.HashMap;

/**
 * JMESPath变量绑定工具类测试
 */
public class JMESPathVariableUtilsTest {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Test
    @DisplayName("测试基本变量绑定")
    public void testBasicVariableBinding() throws Exception {
        String jsonData = """
            {
                "users": [
                    {"name": "Alice", "age": 30, "department": "Engineering"},
                    {"name": "Bob", "age": 25, "department": "Marketing"},
                    {"name": "<PERSON>", "age": 35, "department": "Engineering"}
                ]
            }
            """;
        
        Map<String, Object> variables = JMESPathVariableUtils.createVariables();
        JMESPathVariableUtils.addVariable(variables, "targetDept", "Engineering");
        
        String expression = "users[?department == ${targetDept}].name";
        String result = JMESPathVariableUtils.executeWithVariables(jsonData, expression, variables);
        
        System.out.println("基本变量绑定结果: " + result);
        assertNotNull(result);
        assertTrue(result.contains("Alice"));
        assertTrue(result.contains("Charlie"));
    }
    
    @Test
    @DisplayName("测试数值变量绑定")
    public void testNumericVariableBinding() throws Exception {
        String jsonData = "{\n" +
            "    \"products\": [\n" +
            "        {\"name\": \"Laptop\", \"price\": 1000},\n" +
            "        {\"name\": \"Book\", \"price\": 20},\n" +
            "        {\"name\": \"Phone\", \"price\": 800}\n" +
            "    ]\n" +
            "}";

        Map<String, Object> variables = new HashMap<>();
        variables.put("minPrice", 500);

        String expression = "products[?price >= ${minPrice}].{name: name, price: price}";
        String result = JMESPathVariableUtils.executeWithVariables(jsonData, expression, variables);

        System.out.println("数值变量绑定结果: " + result);
        assertNotNull(result);
        assertTrue(result.contains("Laptop"));
        assertTrue(result.contains("Phone"));
        assertFalse(result.contains("Book"));
    }
    
    @Test
    @DisplayName("测试复杂对象变量绑定")
    public void testComplexObjectVariableBinding() throws Exception {
        String jsonData = "{\n" +
            "    \"orders\": [\n" +
            "        {\"id\": 1, \"amount\": 100, \"status\": \"completed\"},\n" +
            "        {\"id\": 2, \"amount\": 250, \"status\": \"pending\"},\n" +
            "        {\"id\": 3, \"amount\": 300, \"status\": \"completed\"}\n" +
            "    ]\n" +
            "}";
        
        Map<String, Object> criteria = new HashMap<>();
        criteria.put("minAmount", 200);
        criteria.put("status", "completed");
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("criteria", criteria);
        
        String expression = "orders[?amount >= ${criteria}.minAmount && status == ${criteria}.status]";
        String result = JMESPathVariableUtils.executeWithVariables(jsonData, expression, variables);
        
        System.out.println("复杂对象变量绑定结果: " + result);
        assertNotNull(result);
    }
    
    @Test
    @DisplayName("测试构建器模式")
    public void testBuilderPattern() throws Exception {
        String jsonData = "{\n" +
            "    \"employees\": [\n" +
            "        {\"name\": \"Alice\", \"salary\": 5000, \"department\": \"IT\"},\n" +
            "        {\"name\": \"Bob\", \"salary\": 4000, \"department\": \"HR\"},\n" +
            "        {\"name\": \"Charlie\", \"salary\": 6000, \"department\": \"IT\"}\n" +
            "    ]\n" +
            "}";

        String result = JMESPathVariableUtils.builder()
            .expression("employees[?department == ${dept} && salary >= ${minSalary}].name")
            .variable("dept", "IT")
            .variable("minSalary", 5000)
            .execute(jsonData);

        System.out.println("构建器模式结果: " + result);
        assertNotNull(result);
        assertTrue(result.contains("Alice"));
        assertTrue(result.contains("Charlie"));
        assertFalse(result.contains("Bob"));
    }
    
    @Test
    @DisplayName("测试变量提取")
    public void testVariableExtraction() throws Exception {
        String jsonData = "{\n" +
            "    \"config\": {\n" +
            "        \"department\": \"Engineering\",\n" +
            "        \"minAge\": 25,\n" +
            "        \"active\": true\n" +
            "    },\n" +
            "    \"metadata\": {\n" +
            "        \"version\": \"1.0\",\n" +
            "        \"author\": \"System\"\n" +
            "    }\n" +
            "}";
        
        Map<String, String> variablePaths = new HashMap<>();
        variablePaths.put("targetDept", "config.department");
        variablePaths.put("ageLimit", "config.minAge");
        variablePaths.put("isActive", "config.active");
        variablePaths.put("version", "metadata.version");
        
        Map<String, Object> extractedVars = JMESPathVariableUtils.extractVariables(jsonData, variablePaths);
        
        System.out.println("提取的变量: " + extractedVars);
        assertEquals("Engineering", extractedVars.get("targetDept"));
        assertEquals(25, extractedVars.get("ageLimit"));
        assertEquals(true, extractedVars.get("isActive"));
        assertEquals("1.0", extractedVars.get("version"));
    }
    
    @Test
    @DisplayName("测试简单变量替换")
    public void testSimpleVariableReplace() {
        String template = "Hello ${name}, your age is ${age} and you work in ${department}";
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "Alice");
        variables.put("age", 30);
        variables.put("department", "Engineering");
        
        String result = JMESPathVariableUtils.simpleVariableReplace(template, variables);
        
        System.out.println("简单变量替换结果: " + result);
        assertEquals("Hello Alice, your age is 30 and you work in Engineering", result);
    }
    
    @Test
    @DisplayName("测试表达式验证")
    public void testExpressionValidation() {
        assertTrue(JMESPathVariableUtils.isValidExpression("users[0].name"));
        assertTrue(JMESPathVariableUtils.isValidExpression("users[?age > 25].name"));
        assertFalse(JMESPathVariableUtils.isValidExpression("users[invalid syntax"));
    }
    
    @Test
    @DisplayName("测试多变量组合")
    public void testMultipleVariableCombination() throws Exception {
        String jsonData = "{\n" +
            "    \"sales\": [\n" +
            "        {\"region\": \"North\", \"amount\": 1000, \"quarter\": \"Q1\"},\n" +
            "        {\"region\": \"South\", \"amount\": 1500, \"quarter\": \"Q1\"},\n" +
            "        {\"region\": \"North\", \"amount\": 1200, \"quarter\": \"Q2\"},\n" +
            "        {\"region\": \"South\", \"amount\": 1800, \"quarter\": \"Q2\"}\n" +
            "    ]\n" +
            "}";

        Map<String, Object> variables = new HashMap<>();
        variables.put("targetRegion", "North");
        variables.put("targetQuarter", "Q2");
        variables.put("minAmount", 1000);

        String expression = "sales[?region == ${targetRegion} && quarter == ${targetQuarter} && amount >= ${minAmount}].{" +
            "region: region, amount: amount, quarter: quarter}";
        
        String result = JMESPathVariableUtils.executeWithVariables(jsonData, expression, variables);
        
        System.out.println("多变量组合结果: " + result);
        assertNotNull(result);
        assertTrue(result.contains("North"));
        assertTrue(result.contains("Q2"));
        assertTrue(result.contains("1200"));
    }
    
    @Test
    @DisplayName("测试实际业务场景 - 任务过滤")
    public void testBusinessScenarioTaskFiltering() throws Exception {
        String jsonData = """
            {
                "tasks": [
                    {"id": 1, "status": "completed", "priority": "high", "assignee": "Alice", "dueDate": "2024-01-15"},
                    {"id": 2, "status": "in_progress", "priority": "medium", "assignee": "Bob", "dueDate": "2024-01-20"},
                    {"id": 3, "status": "pending", "priority": "high", "assignee": "Alice", "dueDate": "2024-01-18"},
                    {"id": 4, "status": "completed", "priority": "low", "assignee": "Charlie", "dueDate": "2024-01-12"}
                ],
                "filters": {
                    "assignee": "Alice",
                    "priority": "high",
                    "excludeStatus": "completed"
                }
            }
            """;
        
        // 使用构建器模式创建复杂查询
        String result = JMESPathVariableUtils.builder()
            .expression("tasks[?assignee == ${assignee} && priority == ${priority} && status != ${excludeStatus}].{" +
                "taskId: id, status: status, priority: priority, dueDate: dueDate}")
            .variable("assignee", "Alice")
            .variable("priority", "high")
            .variable("excludeStatus", "completed")
            .execute(jsonData);
        
        System.out.println("业务场景 - 任务过滤结果: " + result);
        assertNotNull(result);
        assertTrue(result.contains("\"taskId\":3"));
        assertTrue(result.contains("pending"));
        assertFalse(result.contains("completed"));
    }
    
    public static void main(String[] args) {
        JMESPathVariableUtilsTest test = new JMESPathVariableUtilsTest();
        try {
            test.testBasicVariableBinding();
            test.testNumericVariableBinding();
            test.testComplexObjectVariableBinding();
            test.testBuilderPattern();
            test.testVariableExtraction();
            test.testSimpleVariableReplace();
            test.testExpressionValidation();
            test.testMultipleVariableCombination();
            test.testBusinessScenarioTaskFiltering();
            
            System.out.println("\n=== 所有测试完成 ===");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
