# JMESPath 变量绑定实例

本项目提供了完整的JMESPath变量绑定解决方案，包括工具类、示例代码和测试用例。

## 文件结构

```
├── JMESPathVariableBindingExamples.java  # 详细的变量绑定示例
├── JMESPathVariableUtils.java            # 实用工具类
├── JMESPathVariableUtilsTest.java        # 测试用例
├── jmespath-dependencies.xml             # Maven依赖配置
└── README.md                             # 本文档
```

## 快速开始

### 1. 添加依赖

将 `jmespath-dependencies.xml` 中的依赖添加到你的 `pom.xml` 文件中。

### 2. 基本用法

```java
import com.youibot.utils.JMESPathVariableUtils;
import java.util.Map;
import java.util.HashMap;

// 准备数据
String jsonData = """
    {
        "users": [
            {"name": "Alice", "age": 30, "department": "Engineering"},
            {"name": "Bob", "age": 25, "department": "Marketing"}
        ]
    }
    """;

// 定义变量
Map<String, Object> variables = new HashMap<>();
variables.put("targetDept", "Engineering");
variables.put("minAge", 25);

// 执行带变量的表达式
String result = JMESPathVariableUtils.executeWithVariables(
    jsonData, 
    "users[?department == ${targetDept} && age >= ${minAge}].name", 
    variables
);
```

### 3. 构建器模式

```java
String result = JMESPathVariableUtils.builder()
    .expression("users[?department == ${dept} && age >= ${minAge}].name")
    .variable("dept", "Engineering")
    .variable("minAge", 25)
    .execute(jsonData);
```

## 核心功能

### 1. 变量类型支持

- **字符串变量**: `${name}` → `'Alice'`
- **数值变量**: `${age}` → `30`
- **布尔变量**: `${active}` → `true`
- **复杂对象**: `${config}` → `{"key": "value"}`

### 2. 表达式语法

支持所有标准JMESPath语法，变量使用 `${variableName}` 格式：

```java
// 过滤表达式
"users[?age >= ${minAge}]"

// 投影表达式
"users[?department == ${dept}].{name: name, age: age}"

// 复杂条件
"orders[?amount >= ${minAmount} && status == ${status} && date >= ${startDate}]"
```

### 3. 变量提取

从JSON数据中提取变量值：

```java
Map<String, String> paths = Map.of(
    "dept", "config.department",
    "minAge", "config.minAge"
);

Map<String, Object> variables = JMESPathVariableUtils.extractVariables(jsonData, paths);
```

### 4. 简单模板替换

对于简单的字符串模板：

```java
String template = "Hello ${name}, you are ${age} years old";
Map<String, Object> vars = new HashMap<>();
vars.put("name", "Alice");
vars.put("age", 30);
String result = JMESPathVariableUtils.simpleVariableReplace(template, vars);
// 结果: "Hello Alice, you are 30 years old"
```

## 实际应用场景

### 1. 动态查询过滤

```java
// 根据用户输入动态构建查询条件
Map<String, Object> filters = getUserFilters(); // 从前端获取
String expression = "products[?price >= ${minPrice} && category == ${category}]";
String result = JMESPathVariableUtils.executeWithVariables(productData, expression, filters);
```

### 2. 配置驱动的数据处理

```java
// 从配置文件读取查询规则
String configData = loadConfiguration();
Map<String, Object> queryVars = JMESPathVariableUtils.extractVariables(
    configData, 
    Map.of("dept", "query.department", "status", "query.status")
);

String result = JMESPathVariableUtils.executeWithVariables(
    userData, 
    "users[?department == ${dept} && status == ${status}]", 
    queryVars
);
```

### 3. 报表数据聚合

```java
// 动态报表查询
String reportResult = JMESPathVariableUtils.builder()
    .expression("""
        sales[?region == ${region} && quarter == ${quarter}].{
            total: sum(amount),
            count: length(@),
            average: avg(amount)
        }
        """)
    .variable("region", "North")
    .variable("quarter", "Q1")
    .execute(salesData);
```

## 高级特性

### 1. 嵌套变量绑定

```java
// 支持多层变量绑定
String expression = """
    let $filters = ${filterConfig} in
    let $minAmount = $filters.minAmount in
    let $status = $filters.status in
    orders[?amount >= $minAmount && status == $status]
    """;
```

### 2. 条件变量使用

```java
// 根据条件使用不同变量
String expression = """
    users[?department == ${dept}][?age >= ${minAge}].{
        name: name,
        level: age >= ${seniorAge} ? 'Senior' : 'Junior'
    }
    """;
```

### 3. 数组操作变量

```java
// 在数组操作中使用变量
String expression = "data[${startIndex}:${endIndex}]";
Map<String, Object> vars = new HashMap<>();
vars.put("startIndex", 0);
vars.put("endIndex", 5);
```

## 性能优化建议

1. **表达式缓存**: 对于重复使用的表达式，建议缓存编译后的Expression对象
2. **变量预处理**: 提前准备好变量映射，避免在循环中重复创建
3. **数据结构优化**: 对于大型JSON数据，考虑使用流式处理

## 错误处理

工具类提供了完善的错误处理机制：

```java
try {
    String result = JMESPathVariableUtils.executeWithVariables(data, expression, vars);
} catch (RuntimeException e) {
    // 处理表达式语法错误或执行错误
    logger.error("JMESPath执行失败: " + e.getMessage());
}

// 表达式验证
if (!JMESPathVariableUtils.isValidExpression(expression)) {
    throw new IllegalArgumentException("无效的JMESPath表达式");
}
```

## 与现有代码集成

如果你的项目已经在使用JSONPath（如代码库中的示例），可以逐步迁移到JMESPath：

```java
// 原有JSONPath代码
Object result = JSONPath.eval(data, "$.users[?(@.age > 25)]");

// 迁移到JMESPath变量绑定
Map<String, Object> vars = new HashMap<>();
vars.put("minAge", 25);
String result = JMESPathVariableUtils.executeWithVariables(
    jsonData,
    "users[?age > ${minAge}]",
    vars
);
```

## 测试

运行测试用例：

```bash
mvn test -Dtest=JMESPathVariableUtilsTest
```

或直接运行测试类的main方法查看所有示例的执行结果。

## 注意事项

1. JMESPath语法与JSONPath略有不同，迁移时需要注意语法差异
2. 变量名必须是有效的标识符（字母开头，包含字母、数字、下划线）
3. 复杂对象变量会被序列化为JSON，确保对象可序列化
4. 字符串变量中的单引号会被自动转义

## 扩展功能

可以根据项目需要扩展以下功能：

1. **自定义函数**: 添加项目特定的JMESPath函数
2. **类型转换**: 增强变量类型转换能力
3. **缓存机制**: 实现表达式编译结果缓存
4. **异步执行**: 支持异步的表达式执行

这套JMESPath变量绑定解决方案提供了灵活、强大的JSON数据查询和处理能力，特别适合需要动态查询条件的业务场景。
