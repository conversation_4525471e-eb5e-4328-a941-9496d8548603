<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.youibot</groupId>
		<artifactId>youifleet-plus</artifactId>
		<version>5.0.11</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>youifleet-admin</artifactId>
	<packaging>jar</packaging>
	<version>5.0.13</version>
	<description>youifleet-admin</description>

	<properties>
		<quartz.version>2.3.0</quartz.version>
		<shiro.version>1.6.0</shiro.version>
		<springfox-version>2.9.2</springfox-version>
		<swagger.model>1.5.21</swagger.model>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.youibot</groupId>
			<artifactId>youifleet-common</artifactId>
			<version>5.0.11</version>
		</dependency>
		<dependency>
			<groupId>com.youibot</groupId>
			<artifactId>youifleet-engine</artifactId>
			<version>5.0.11</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>${quartz.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.mchange</groupId>
					<artifactId>c3p0</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.zaxxer</groupId>
					<artifactId>HikariCP-java6</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
		    <groupId>org.apache.shiro</groupId>
		    <artifactId>shiro-core</artifactId>
		    <version>${shiro.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.shiro</groupId>
		    <artifactId>shiro-spring</artifactId>
		    <version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${springfox-version}</version>
			<exclusions>
				<exclusion>
					<groupId>io.swagger</groupId>
					<artifactId>swagger-models</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>${springfox-version}</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-models</artifactId>
			<version>${swagger.model}</version>
		</dependency>
		<!-- licenseEntity -->
		<dependency>
			<groupId>de.schlichtherle.truelicense</groupId>
			<artifactId>truelicense-core</artifactId>
			<version>1.33</version>
		</dependency>
		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-spring-boot-starter</artifactId>
			<version>********</version>
		</dependency>
		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-script-javax</artifactId>
			<version>********</version>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
			<version>4.1.70.Final</version>
		</dependency>
		<dependency>
			<groupId>com.infiniteautomation</groupId>
			<artifactId>modbus4j</artifactId>
			<version>3.0.3</version>
		</dependency>
		<dependency>
			<groupId>com.github.oshi</groupId>
			<artifactId>oshi-core</artifactId>
			<version>6.4.1</version>
		</dependency>
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.7.0</version> <!-- 使用最新版本 -->
		</dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

		<!-- JMESPath Java 实现 -->
		<dependency>
			<groupId>io.burt</groupId>
			<artifactId>jmespath-jackson</artifactId>
			<version>0.6.0</version>
		</dependency>

		<!-- Jackson JSON 处理库 -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.15.2</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.15.2</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.15.2</version>
		</dependency>

		<!-- JUnit 5 测试框架 (用于测试) -->
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<version>5.9.3</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<version>5.9.3</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<finalName>youifleet-${project.version}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<version>${docker.plugin.version}</version>
				<configuration>
					<imageName>${project.artifactId}</imageName>
					<dockerDirectory>${project.basedir}/</dockerDirectory>
					<resources>
						<resource>
							<targetPath>/</targetPath>
							<directory>${project.build.directory}</directory>
							<include>${project.build.finalName}.jar</include>
						</resource>
					</resources>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>