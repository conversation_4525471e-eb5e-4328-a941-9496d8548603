package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RoadNetData {

    private String mapCode;

    private String mapName;

    private String creatorName;

    private Date createTime;

    private List<MapInfoDTO> locatingInfo;

    private List<Marker> markers;

    private List<Path> paths;

    private List<MapAreaDTO> mapAreas;

    private List<AutoDoorDTO> autoDoors;

    private List<AirShowerDoorDTO> airShowerDoors;
}
