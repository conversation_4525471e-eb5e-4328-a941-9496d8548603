package com.youibot.vehicle.scheduler.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.impl.BaseServiceImpl;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.TreeUtils;
import com.youibot.vehicle.scheduler.modules.security.user.SecurityUser;
import com.youibot.vehicle.scheduler.modules.sys.dao.SysRoleDao;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysRoleDTO;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysRoleMenuDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SysMenuEntity;
import com.youibot.vehicle.scheduler.modules.sys.entity.SysRoleEntity;
import com.youibot.vehicle.scheduler.modules.sys.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 角色
 */
@Service
public class SysRoleServiceImpl extends BaseServiceImpl<SysRoleDao, SysRoleEntity> implements SysRoleService {
	@Autowired
	private SysRoleMenuService sysRoleMenuService;
	@Autowired
	private SysRoleUserService sysRoleUserService;
	@Autowired
	private SysMenuService sysMenuService;
	@Autowired
	private SysUserService sysUserService;

	@Override
	public PageData<SysRoleDTO> page(Map<String, Object> params) {
		IPage<SysRoleEntity> page = baseDao.selectPage(
			getPage(params, Constant.CREATE_DATE, false),
			getWrapper(params)
		);

		return getPageData(page, SysRoleDTO.class);
	}

	@Override
	public List<SysRoleDTO> list(Map<String, Object> params) {
		List<SysRoleEntity> entityList = baseDao.selectList(getWrapper(params));
		List<SysRoleDTO> sysRoleDTOS = ConvertUtils.sourceToTarget(entityList, SysRoleDTO.class);
		//按照授权的菜单数量排序
		sysRoleDTOS.sort((a, b) -> {
			int aSize = sysRoleMenuService.getMenuIdList(a.getId()).size();
			int bSize = sysRoleMenuService.getMenuIdList(b.getId()).size();
			return aSize < bSize ? 1 : aSize > bSize ? -1 : a.getCreateDate().getTime() > b.getCreateDate().getTime() ? 1 : -1;
		});
		sysRoleDTOS.parallelStream().forEach(r -> {
			List<String> names = sysUserService.realNamesByRoleId(r.getId());
			r.setRealNames(String.join(",", names));
		});
		return sysRoleDTOS;
	}

	private QueryWrapper<SysRoleEntity> getWrapper(Map<String, Object> params){
		String name = (String)params.get("name");

		QueryWrapper<SysRoleEntity> wrapper = new QueryWrapper<>();
		wrapper.like(StringUtils.isNotBlank(name), "name", name);

		return wrapper;
	}

	@Override
	public SysRoleDTO get(Long id) {
		SysRoleEntity entity = baseDao.selectById(id);
		SysRoleDTO sysRoleDTO = ConvertUtils.sourceToTarget(entity, SysRoleDTO.class);

		if (Objects.nonNull(sysRoleDTO)) {
			List<SysMenuEntity> list = sysMenuService.getTileUserMenu(SecurityUser.getUser(), null);
			List<SysRoleMenuDTO> dtoList = ConvertUtils.sourceToTarget(list, SysRoleMenuDTO.class);
			List<Long> menuIdList = sysRoleMenuService.getMenuIdList(id);
			dtoList.forEach(i -> i.setFlag(menuIdList.contains(i.getId())));
			sysRoleDTO.setMenuList(TreeUtils.build(dtoList));
		}

		return sysRoleDTO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SysRoleDTO save(SysRoleDTO dto) {
		SysRoleEntity entity = ConvertUtils.sourceToTarget(dto, SysRoleEntity.class);

		entity.setCreateDate(new Date());
		//保存角色
		insert(entity);

		//保存角色菜单关系
		sysRoleMenuService.saveOrUpdate(entity.getId(), getMenuIdsByTree(dto.getMenuList()));
		return get(entity.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void update(SysRoleDTO dto) {
		SysRoleEntity exist = selectById(dto.getId());
		if (Objects.nonNull(exist)) {
			exist.setName(dto.getName());
			exist.setRemark(dto.getRemark());
			//更新角色
			updateById(exist);
			//更新角色菜单关系
			sysRoleMenuService.saveOrUpdate(exist.getId(), getMenuIdsByTree(dto.getMenuList()));
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Long[] ids) {
		//删除角色
		baseDao.deleteBatchIds(Arrays.asList(ids));

		//删除角色用户关系
		sysRoleUserService.deleteByRoleIds(ids);

		//删除角色菜单关系
		sysRoleMenuService.deleteByRoleIds(ids);
	}

	@Override
	public List<String> getRoleNamesByUserId(Long userId) {
		return baseDao.getRoleNamesByUserId(userId);
	}

	/**
	 * 获取该角色被授予的菜单ID
	 */
	private List<Long> getMenuIdsByTree(List<SysRoleMenuDTO> menuList) {
		menuList = Optional.ofNullable(menuList).orElse(new ArrayList<>());
		List<Long> menuIds = new ArrayList<>();
		for (SysRoleMenuDTO menuDTO : menuList) {
			if (menuDTO.getFlag()) {
				menuIds.add(menuDTO.getId());
			}
			if (!CollectionUtils.isEmpty(menuDTO.getChildren())) {
				menuIds.addAll(getMenuIdsByTree(menuDTO.getChildren()));
			}
		}
		return menuIds;
	}
}