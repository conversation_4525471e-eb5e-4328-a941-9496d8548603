package com.youibot.vehicle.scheduler.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.vehicle.scheduler.common.entity.BaseEntity;
import com.youibot.vehicle.scheduler.modules.sys.constant.SysConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 系统用户
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("sys_user")
public class SysUserEntity extends BaseEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 真实姓名
	 */
	private String realName;
	/**
	 * 账号
	 */
	private String username;
	/**
	 * 密码
	 */
	private String password;
	/**
	 * 邮箱
	 */
	private String email;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 超级管理员   0：否   1：是
	 */
	private Integer superAdmin;
	/**
	 * 状态  0：停用   1：正常
	 */
	private Integer status;
	/**
	 * 自动登出时间（分钟） 默认0不登出
	 */
	private Integer autoLogoutTime;

	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;

	public boolean isUsable(){
		return SysConstant.SYS_USER_STATUS_ENABLE.equals(status);
	}
}