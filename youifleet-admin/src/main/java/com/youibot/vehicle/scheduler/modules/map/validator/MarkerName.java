package com.youibot.vehicle.scheduler.modules.map.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.METHOD,ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = MarkerNameValidator.class)
public @interface MarkerName {
    /**
     * 是否允许为空
     */
    boolean required() default true;

    /**
     * 长度限制
     */
    int len() default 20;

    /**
     * 校验不通过返回的提示信息
     */
    String message() default "点位名称，命名规则：字母或数字、下划线，字母开头，长度不超过20个字符";

    /**
     * Constraint要求的属性，用于分组校验和扩展，留空就好
     */
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
