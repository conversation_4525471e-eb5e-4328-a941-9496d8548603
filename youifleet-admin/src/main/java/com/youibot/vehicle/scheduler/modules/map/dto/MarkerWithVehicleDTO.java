package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标记点
 * 类名称：Marker
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "MarkerWithVehicleDTO", description = "标记点")
public class MarkerWithVehicleDTO extends Marker {

    @ApiModelProperty(value = "占用的机器人")
    private String vehicleCode;

}
