package com.youibot.vehicle.scheduler.modules.map.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.intelligt.modbus.jlibmodbus.exception.ModbusIOException;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.engine.execute.node.entity.PathPlanMessage;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.modules.device.utils.ElevatorUtils;
import com.youibot.vehicle.scheduler.modules.device.utils.ModbusUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.constant.MapFileSuffixConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.ElevatorDao;
import com.youibot.vehicle.scheduler.modules.map.dao.ElevatorToMapDao;
import com.youibot.vehicle.scheduler.modules.map.dto.ElevatorDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.ElevatorRelateDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.ElevatorToMapDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.Elevator;
import com.youibot.vehicle.scheduler.modules.map.entity.ElevatorToMap;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.entity.VehicleMap;
import com.youibot.vehicle.scheduler.modules.map.service.ElevatorService;
import com.youibot.vehicle.scheduler.modules.map.service.MapElementService;
import com.youibot.vehicle.scheduler.modules.map.service.MarkerService;
import com.youibot.vehicle.scheduler.modules.map.service.VehicleMapService;
import com.youibot.vehicle.scheduler.modules.map.utils.CodeFormatUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.FileUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.MapPublishUtil;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ElevatorServiceImpl implements ElevatorService, MapElementService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ElevatorServiceImpl.class);

    @Autowired
    private VehicleMapService vehicleMapService;
    @Autowired(required = false)
    private ElevatorDao elevatorDao;
    @Autowired(required = false)
    private ElevatorToMapDao elevatorToMapDao;
    @Autowired
    private MarkerService markerService;
    @Autowired
    private VehiclePool vehiclePool;

    @Override
    public List<ElevatorDTO> selectList() {
        return searchAll(new HashMap<>());
    }

    @Override
    public List<ElevatorDTO> searchAll(Map<String, Object> searchMap) {
        List<Elevator> elevators = elevatorDao.selectList(getWrapper(searchMap));
        if (CollectionUtils.isEmpty(elevators)) {
            return null;
        }
        List<VehicleMap> allVehicleMap = vehicleMapService.getAllVehicleMap(null);
        Map<String, String> mapMap = allVehicleMap.stream().collect(Collectors.toMap(VehicleMap::getCode, VehicleMap::getName));
        List<ElevatorDTO> elevatorDTOS = ConvertUtils.sourceToTarget(elevators, ElevatorDTO.class);
        elevatorDTOS.forEach(elevatorDTO -> {
            List<ElevatorToMap> elevatorToMaps = elevatorToMapDao.selectList(new QueryWrapper<ElevatorToMap>().eq("elevator_code", elevatorDTO.getCode()));
            if (!CollectionUtils.isEmpty(elevatorToMaps)) {
                elevatorDTO.setElevatorToMaps(ConvertUtils.sourceToTarget(elevatorToMaps, ElevatorToMapDTO.class));
                elevatorDTO.getElevatorToMaps().forEach(r -> {
                    r.setVehicleMapName(mapMap.get(r.getVehicleMapCode()));
                });
            }
        });
        return elevatorDTOS;
    }

    @Override
    public ElevatorDTO insert(ElevatorDTO elevatorDTO) {
        if (!MapPublishUtil.applyLockForElevator()) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        Elevator elevator = null;
        try {
            elevator = ConvertUtils.sourceToTarget(elevatorDTO, Elevator.class);
            List<Elevator> elevators = elevatorDao.selectList(null);
            elevator.setCode(CodeFormatUtils.getFormatCode(getMaxCode(elevators), CodeFormatUtils.ELEVATOR_CODE_PREFIX));
            elevatorDao.insert(elevator);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("电梯数据新增异常", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.elevator.add.error");
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseElevator();
        }
        return ConvertUtils.sourceToTarget(elevator, ElevatorDTO.class);
    }

    private String getMaxCode(List<Elevator> elevators) {
        String code = MapConstant.DefaultCode;
        if (!CollectionUtils.isEmpty(elevators)) {
            Integer maxVal = elevators.stream().map(item -> Integer.valueOf(CodeFormatUtils.getIntegerCode(item.getCode()))).max(Comparator.comparingInt(a -> a)).orElse(0);
            code = String.valueOf(maxVal + 1);
        }
        return code;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ElevatorDTO elevatorDTO) {
        //校验点位
        checkMarker(elevatorDTO);
        //获取锁成功后，才能执行更新操作
        if (!MapPublishUtil.applyLockForElevator()) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            if (ElevatorUtils.isInUsedElevator(Arrays.asList(elevatorDTO))) {
                LOGGER.error("电梯{}正在使用中，不能进行更新",elevatorDTO.getCode());
                String message = I18nMessageUtils.getMessage("device.is.in.use.error", elevatorDTO.getCode());
                throw new FleetException(message);
            }
            //小车重新规划
            this.reNavigation(elevatorDTO.getCode());

            //获取电梯关联的点位
            List<ElevatorToMapDTO> elevatorToMapDTOS = elevatorDTO.getElevatorToMaps();
            //有过电梯关联的地图code集合，用于推送电梯更新的消息到地图
            Set<String> mapCodes = new HashSet<>();
            elevatorToMapDTOS.forEach(etm -> {
                mapCodes.add(etm.getVehicleMapCode());
            });
            List<ElevatorToMap> originElevatorToMapS = elevatorToMapDao.selectList(new QueryWrapper<ElevatorToMap>().eq("elevator_code", elevatorDTO.getCode()));
            if (CollectionUtils.isEmpty(originElevatorToMapS)) {
                List<ElevatorToMap> elevatorToMaps = ConvertUtils.sourceToTarget(elevatorToMapDTOS, ElevatorToMap.class);
                elevatorToMaps.stream().forEach(elevatorToMap -> {
                    elevatorToMap.setCode(UUID.randomUUID().toString());
                    elevatorToMap.setElevatorCode(elevatorDTO.getCode());
                    elevatorToMapDao.insert(elevatorToMap);
                });
            } else {
                Map<String, ElevatorToMapDTO> updateMap = elevatorToMapDTOS.stream().collect(Collectors.toMap(ElevatorToMapDTO::getCode, Function.identity(), (key1, key2) -> key2));
                originElevatorToMapS.forEach(oElevatorToMap -> {
                    mapCodes.add(oElevatorToMap.getVehicleMapCode());
                    if (updateMap.containsKey(oElevatorToMap.getCode())) {
                        oElevatorToMap = ConvertUtils.sourceToTarget(updateMap.get(oElevatorToMap.getCode()), ElevatorToMap.class);
                        elevatorToMapDao.updateById(oElevatorToMap);
                        updateMap.remove(oElevatorToMap.getCode());
                    } else {
                        elevatorToMapDao.deleteById(oElevatorToMap.getCode());
                    }
                });
                if (!CollectionUtils.isEmpty(updateMap)) {
                    updateMap.values().stream().forEach(elevatorToMapDTO -> {
                        ElevatorToMap elevatorToMap = ConvertUtils.sourceToTarget(elevatorToMapDTO, ElevatorToMap.class);
                        elevatorToMap.setCode(UUID.randomUUID().toString());
                        elevatorToMap.setElevatorCode(elevatorDTO.getCode());
                        elevatorToMapDao.insert(elevatorToMap);
                    });
                }
            }
            Elevator elevator = ConvertUtils.sourceToTarget(elevatorDTO, Elevator.class);
            elevatorDao.updateById(elevator);
            ElevatorDTO updateElevator = this.selectByCode(elevatorDTO.getCode());
            MapGraphUtil.addSingleElevator(updateElevator);
            //发送电梯修改的消息
            if (!CollectionUtils.isEmpty(mapCodes)) {
                mapCodes.forEach(mapCode -> {
                    MapUpdateSocketController.sendMessageOfUpdate(mapCode, "Elevator", Arrays.asList(updateElevator));
                });
            }
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("电梯数据更新异常", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.elevator.update.error", elevatorDTO.getCode());
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseElevator();
        }
    }

    private void checkMarker(ElevatorDTO elevator) {
        List<ElevatorToMapDTO> bindMaps = elevator.getElevatorToMaps();
        //点位是否重复绑定
        if (!CollectionUtils.isEmpty(bindMaps)) {
            Set<String> markerCodes = new HashSet<>();
            Set<String> mapCodes = new HashSet<>();
            bindMaps.forEach(relate->{
                if(!markerCodes.contains(relate.getMarkerCode())){
                    markerCodes.add(relate.getMarkerCode());
                } else {
                    String message = I18nMessageUtils.getMessage("vehicleMap.marker.already.bind.other.device.error", relate.getMarkerCode());
                    throw new FleetException(message);
                }
                if (!mapCodes.contains(relate.getVehicleMapCode())) {
                    mapCodes.add(relate.getVehicleMapCode());
                } else {
                    String message = I18nMessageUtils.getMessage("vehicleMap.elevator.bind.multi.marker.error", relate.getMarkerCode());
                    throw new FleetException(message);
                }
            });

            List<ElevatorDTO> elevators = selectList();
            elevators.removeIf(i -> Objects.equals(i.getCode(), elevator.getCode()));
            for (ElevatorDTO exist : elevators) {
                if (!CollectionUtils.isEmpty(exist.getElevatorToMaps())) {
                    exist.getElevatorToMaps().forEach(i -> {
                        if (markerCodes.contains(i.getMarkerCode())) {
                            String message = I18nMessageUtils.getMessage("vehicleMap.marker.already.bind.other.device.error", i.getMarkerCode());
                            throw new FleetException(message);
                        }
                    });
                }
            }
        }
    }

    /**
     * 如果有小车的规划路径，包含当前电梯所处的路径，则小车需要重规划
     */
    private void reNavigation(String elevatorCode){
        Set<String> elevatorPathCodes = MapGraphUtil.getElevatorPathCodes(elevatorCode);
        if(CollUtil.isEmpty(elevatorPathCodes)){
            return;
        }
        List<Vehicle> vehicleList = vehiclePool.getAll();
        if(CollUtil.isEmpty(vehicleList)){
            return;
        }
        for(Vehicle vehicle : vehicleList){
            List<Path> planedPaths = vehicle.getPlanedPaths();
            if(CollUtil.isEmpty(planedPaths)) continue;

            Set<String> planedPathCodes = planedPaths.stream().map(Path::getCode).collect(Collectors.toSet());
            Set<String> commonPathCodes = CollUtil.intersectionDistinct(planedPathCodes, elevatorPathCodes);
            if(CollUtil.isEmpty(commonPathCodes)) continue;

            //有交集，说明机器人规划了电梯路径
            PathPlanMessage pathPlanMessage = vehicle.getPathPlanMessage();
            //重新路径规划给机器人
            PathPlanMessage newPathPlanMessage = PathPlanMessage.builder()
                    .vehicleCode(pathPlanMessage.getVehicleCode())
                    .aimMarkerCode(pathPlanMessage.getAimMarkerCode())
                    .taskCode(pathPlanMessage.getTaskCode())
                    .nodeCode(pathPlanMessage.getNodeCode())
                    .type(PathPlanMessage.TYPE_NORMAL)
                    .id(pathPlanMessage.getNodeCode() + "_N_" + UUID.randomUUID())
                    .cancel(false)
                    .build();
            vehicle.getPathPlanMessageQueue().addFirst(newPathPlanMessage);
            //撤销当前路径导航
            pathPlanMessage.setCancel(true);
            LOGGER.debug("电梯{}编辑，引发小车{}重规划", elevatorCode, vehicle.getVehicleCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCode(String code) {
        //获取锁成功后，才能执行更新操作
        if (!MapPublishUtil.applyLockForElevator()) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            //获取电梯关联的点位
            ElevatorDTO elevatorDTO = this.selectByCode(code);
            if (ElevatorUtils.isInUsedElevator(Arrays.asList(elevatorDTO))) {
                LOGGER.error("电梯{}正在使用中，不能进行删除",elevatorDTO.getCode());
                String message = I18nMessageUtils.getMessage("device.is.in.use.error", elevatorDTO.getCode());
                throw new FleetException(message);
            }
            //小车重新规划
            this.reNavigation(elevatorDTO.getCode());
            elevatorToMapDao.delete(new QueryWrapper<ElevatorToMap>().eq("elevator_code", code));
            elevatorDao.deleteById(code);
            MapGraphUtil.removeElevator(code);
            //发送电梯删除的消息
            if (!CollectionUtils.isEmpty(elevatorDTO.getElevatorToMaps())) {
                elevatorDTO.getElevatorToMaps().forEach(elevatorToMapDTO -> {
                    MapUpdateSocketController.sendMessageOfDelete(elevatorToMapDTO.getVehicleMapCode(), "Elevator", Arrays.asList(elevatorDTO));
                });
            }
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("电梯数据删除异常", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.elevator.delete.error", code);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseElevator();
        }
    }

    @Override
    public ElevatorDTO selectByCode(String code) {
        List<ElevatorDTO> elevators = this.selectList();
        if (CollectionUtils.isEmpty(elevators)) {
            return null;
        }
        return elevators.stream().filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
    }

    @Override
    public ElevatorRelateDTO selectByMarkerCode(String markerCode) {
        List<ElevatorDTO> elevators = this.selectList();
        if (CollectionUtils.isEmpty(elevators)) {
            return null;
        }
        ElevatorRelateDTO elevatorRelateDTO = null;
        out:
        for (ElevatorDTO elevator : elevators) {
            if (CollectionUtils.isEmpty(elevator.getElevatorToMaps())) {
                continue;
            }
            for (ElevatorToMapDTO relate : elevator.getElevatorToMaps()) {
                if (relate.getMarkerCode().equals(markerCode)) {
                    elevatorRelateDTO = getElevatorRelateDTO(elevator, relate);
                    break out;
                }
            }
        }
        return elevatorRelateDTO;
    }

    private ElevatorRelateDTO getElevatorRelateDTO(ElevatorDTO elevator, ElevatorToMapDTO relate){
        ElevatorRelateDTO elevatorRelateDTO = ConvertUtils.sourceToTarget(relate, ElevatorRelateDTO.class);
        elevatorRelateDTO.setCode(elevator.getCode());
        elevatorRelateDTO.setIp(elevator.getIp());
        elevatorRelateDTO.setPort(elevator.getPort());
        elevatorRelateDTO.setReadFunctionCode(elevator.getReadFunctionCode());
        elevatorRelateDTO.setWriteFunctionCode(elevator.getWriteFunctionCode());
        elevatorRelateDTO.setGoodsCheckAddress(elevator.getGoodsCheckAddress());
        return elevatorRelateDTO;
    }

    @Override
    public void importElevator(MultipartFile multiPartFile) {
        if (!MapPublishUtil.applyLockForElevator()) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            /**
             * 文件和格式检查
             */
            if (multiPartFile == null) {
                String message = I18nMessageUtils.getMessage("system.missing.parameter");
                throw new FleetException(message);
            }
            byte[] fileBytes = FileUtils.getByteArray(multiPartFile.getInputStream());
            if (fileBytes == null || fileBytes.length <= 0) {
                String message = I18nMessageUtils.getMessage("system.missing.parameter");
                throw new FleetException(message);
            }

            String originalFilename = multiPartFile.getOriginalFilename();
            if(!originalFilename.endsWith(MapFileSuffixConstant.ELEVATOR_FILE_SUFFIX)){
                String message = I18nMessageUtils.getMessage("vehicleMap.elevator.file.format.error", originalFilename);
                throw new FleetException(message);
            }
            List<ElevatorDTO> elevatorDTOS = JSONArray.parseArray(new String(fileBytes), ElevatorDTO.class);
            List<String> elevatorCodes = elevatorDao.getAllElevatorCode();
            List<String> elevatorMarkerCodes = elevatorToMapDao.getAllElevatorMarkerCodes();
            if (!CollectionUtils.isEmpty(elevatorDTOS)) {
                /**
                 * 数据校验:电梯唯一性;绑定点位存在，且没有绑定过其他电梯
                 */
                elevatorDTOS.forEach(elevatorDTO -> {
                    if (!CollectionUtils.isEmpty(elevatorCodes) && elevatorCodes.contains(elevatorDTO.getCode())) {
                        String message = I18nMessageUtils.getMessage("vehicleMap.elevator.import.already.exist.error", elevatorDTO.getCode());
                        throw new FleetException(message);
                    }
                    if (!CollectionUtils.isEmpty(elevatorDTO.getElevatorToMaps())) {
                        elevatorDTO.getElevatorToMaps().forEach(elevatorToMapDTO -> {
                            if (markerService.selectByCode(elevatorToMapDTO.getMarkerCode()) == null) {
                                String message = I18nMessageUtils.getMessage("vehicleMap.elevator.import.bind.map.error", elevatorDTO.getCode());
                                throw new FleetException(message);
                            }
                            if (!CollectionUtils.isEmpty(elevatorMarkerCodes) && elevatorMarkerCodes.contains(elevatorToMapDTO.getMarkerCode())) {
                                String message = I18nMessageUtils.getMessage("vehicleMap.marker.already.bind.other.device.error", elevatorToMapDTO.getMarkerCode());
                                throw new FleetException(message);
                            }
                        });
                    }
                });
                /**
                 * 写入数据，应用电梯
                 */
                elevatorDTOS.forEach(elevatorDTO -> {
                    Elevator elevator = ConvertUtils.sourceToTarget(elevatorDTO, Elevator.class);
                    elevatorDao.insert(elevator);
                    if (!CollectionUtils.isEmpty(elevatorDTO.getElevatorToMaps())) {
                        List<ElevatorToMap> elevatorToMaps = ConvertUtils.sourceToTarget(elevatorDTO.getElevatorToMaps(), ElevatorToMap.class);
                        elevatorToMaps.forEach(elevatorToMap -> {
                            elevatorToMapDao.insert(elevatorToMap);
                        });
                        MapGraphUtil.addSingleElevator(elevatorDTO);
                    }
                });
            }
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("电梯导入异常,", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.elevator.import.error", e.getMessage());
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseElevator();
        }
    }

    @Override
    public void exportElevator(HttpServletResponse response) {
        List<ElevatorDTO> elevatorDTOS = this.selectList();
        if (CollectionUtils.isEmpty(elevatorDTOS)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.elevator.not.exist.error");
            throw new FleetException(message);
        }
        byte[] bytes = null;
        try {
            bytes = JSON.toJSONBytes(elevatorDTOS);
            //返回前端数据
            String fileName = "elevator" + System.currentTimeMillis();
            fileName = new String(fileName.getBytes(), StandardCharsets.ISO_8859_1) + ".json";
            FileUtils.writeBytesToResponse(response, bytes, fileName);
        } catch (Exception e) {
            LOGGER.error("电梯导出异常", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.elevator.export.error", e.getMessage());
            throw new FleetException(message);
        }
    }

    @Override
    public List<ElevatorDTO> selectElevatorByMapCode(String mapCode) {
        List<ElevatorToMap> elevatorToMaps = elevatorToMapDao.selectList(new QueryWrapper<ElevatorToMap>().eq("vehicle_map_code", mapCode));
        if (CollectionUtils.isEmpty(elevatorToMaps)) {
            return null;
        }
        Set<String> elevatorCodes = new HashSet<>();
        elevatorToMaps.stream().forEach(elevatorToMap -> {
            elevatorCodes.add(elevatorToMap.getElevatorCode());
        });
        List<ElevatorDTO> elevators = this.selectList();
        return elevators.stream().filter(elevatorDTO -> elevatorCodes.contains(elevatorDTO.getCode())).collect(Collectors.toList());
    }

    @Override
    public void open(String code) {
        String elevatorCode = "";
        try {
            if (StringUtils.isEmpty(code)) {
                String message = I18nMessageUtils.getMessage("system.missing.parameter");
                throw new FleetException(message);
            }
            //找到关联的那个电梯
            ElevatorDTO elevatorDTO = Optional.ofNullable(selectList())
                    .map(elevatorList -> elevatorList.stream()
                            .filter(e -> CollUtil.isNotEmpty(e.getElevatorToMaps()) && e.getElevatorToMaps().stream().filter(r -> r.getCode().equals(code)).findFirst().orElse(null) != null)
                            .findFirst().orElse(null))
                    .orElseThrow(() -> {
                        String message = I18nMessageUtils.getMessage("vehicleMap.elevator.not.exist.error", code);
                        return new FleetException(message);
                    });
            elevatorCode = elevatorDTO.getCode();
            List<ElevatorToMapDTO> relateList = elevatorDTO.getElevatorToMaps().stream().filter(r -> r.getCode().equals(code)).collect(Collectors.toList());
            ElevatorToMapDTO currentFloor = relateList.get(0);
            ElevatorRelateDTO elevatorRelateDTO = getElevatorRelateDTO(elevatorDTO, currentFloor);
            //在当前楼层外呼开门
            ModbusUtils.writeFunctionCode(elevatorRelateDTO.getIp(), elevatorRelateDTO.getPort(), elevatorRelateDTO.getWriteFunctionCode(), elevatorRelateDTO.getOutOperateAddress(), elevatorRelateDTO.getOutOperateOpenValue());
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("发送电梯开门信号{}失败, ", elevatorCode, e);
            if (e instanceof UnknownHostException || e instanceof ModbusIOException) {
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.ELEVATOR_CONNECT_ERROR).placeholders(Arrays.asList(code)).build());
                String message = I18nMessageUtils.getMessage("device.connect.error", elevatorCode);
                throw new FleetException(message);
            }
            String message = I18nMessageUtils.getMessage("device.open.error", elevatorCode);
            throw new FleetException(message);
        }
    }

    @Override
    public void close(String code) {
        String elevatorCode = "";
        try {
            if (StringUtils.isEmpty(code)) {
                String message = I18nMessageUtils.getMessage("system.missing.parameter");
                throw new FleetException(message);
            }
            //找到关联的那个电梯
            ElevatorDTO elevatorDTO = Optional.ofNullable(selectList())
                    .map(elevatorList -> elevatorList.stream()
                            .filter(e -> CollUtil.isNotEmpty(e.getElevatorToMaps()) && e.getElevatorToMaps().stream().filter(r -> r.getCode().equals(code)).findFirst().orElse(null) != null)
                            .findFirst().orElse(null))
                    .orElseThrow(() -> {
                        String message = I18nMessageUtils.getMessage("vehicleMap.elevator.not.exist.error", code);
                        return new FleetException(message);
                    });

            elevatorCode = elevatorDTO.getCode();
            List<ElevatorToMapDTO> relateList = elevatorDTO.getElevatorToMaps().stream().filter(r -> r.getCode().equals(code)).collect(Collectors.toList());
            ElevatorToMapDTO currentFloor = relateList.get(0);
            ElevatorRelateDTO elevatorRelateDTO = getElevatorRelateDTO(elevatorDTO, currentFloor);
            //在当前楼层外呼、内呼关门
            ModbusUtils.writeFunctionCode(elevatorRelateDTO.getIp(), elevatorRelateDTO.getPort(), elevatorRelateDTO.getWriteFunctionCode(), elevatorRelateDTO.getOutOperateAddress(), 0);
            ModbusUtils.writeFunctionCode(elevatorRelateDTO.getIp(), elevatorRelateDTO.getPort(), elevatorRelateDTO.getWriteFunctionCode(), elevatorRelateDTO.getInnerOperateAddress(), 0);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("发送电梯关门信号{}失败, ", elevatorCode, e);
            if (e instanceof UnknownHostException || e instanceof ModbusIOException) {
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.ELEVATOR_CONNECT_ERROR).placeholders(Arrays.asList(code)).build());
                String message = I18nMessageUtils.getMessage("device.connect.error", elevatorCode);
                throw new FleetException(message);
            }
            String message = I18nMessageUtils.getMessage("device.close.error", elevatorCode);
            throw new FleetException(message);
        }
    }

}
