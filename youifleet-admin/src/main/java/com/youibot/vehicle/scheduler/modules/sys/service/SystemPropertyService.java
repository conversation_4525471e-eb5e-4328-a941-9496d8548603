package com.youibot.vehicle.scheduler.modules.sys.service;

import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.BaseService;
import com.youibot.vehicle.scheduler.modules.sys.dto.SystemPropertyDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemConfigEntity;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemPropertyEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author：yangpeilin
 * @Date: 2020/5/9 16:43
 */
public interface SystemPropertyService extends BaseService<SystemPropertyEntity> {

    SystemConfigEntity getSystemConfig();

    String getSysMode();

    boolean isAloneMode();

    PageData<SystemPropertyDTO> page(Map<String, Object> params);

    List<SystemPropertyDTO> list(Map<String, Object> searchMap);

    boolean insert(SystemPropertyDTO systemProperty);

    boolean insertBatch(List<SystemPropertyDTO> list);

    boolean updateById(SystemPropertyDTO systemProperty);

    boolean updateBatchById(List<SystemPropertyDTO> list);
}
