package com.youibot.vehicle.scheduler.modules.map.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.METHOD,ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = PathTypeValidator.class)
public @interface PathType {
    /**
     * 是否允许为空
     */
    boolean required() default true;

    /**
     * 校验不通过返回的提示信息
     */
    String message() default "路径类型，Common、普通路径，QR_Down、二维码对接路径，Shelflegs、货架腿对接，Symbol_V、V型板对接，Reflector、反光板对接，LeaveDocking、脱离对接，Pallet、托盘对接";

    /**
     * Constraint要求的属性，用于分组校验和扩展，留空就好
     */
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
