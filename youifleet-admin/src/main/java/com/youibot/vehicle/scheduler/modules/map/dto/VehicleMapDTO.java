package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.common.validator.group.DefaultGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 地图
 * 类名称：Map
 * 创建时间：2019年4月2日 下午3:35:52
 */
@Data
@ApiModel(value = "VehicleMapDTO", description = "地图")
public class VehicleMapDTO implements Serializable {

    @ApiModelProperty(value = "类型：LaserMap:激光地图", position = 2)
    @NotBlank(message = "{map.type.require}", groups = DefaultGroup.class)
    private String type;

    @ApiModelProperty(value = "编码", position = 3)
    @NotBlank(message = "{map.code.require}", groups = DefaultGroup.class)
    private String code;

    @ApiModelProperty(value = "名称", position = 4)
    @Length(min = 1, max = 20, message = "{map.name.require}", groups = DefaultGroup.class)
    private String name;

    @ApiModelProperty(value = "栅格图片", position = 5)
    private String image;

    @ApiModelProperty(value = "角度", position = 7)
    @Range(min = -180, max = 180)
    private Double angle;

    @ApiModelProperty(value = "中心x坐标", position = 6)
    @NotNull(message = "{map.originX.require}", groups = DefaultGroup.class)
    private Double originX;

    @ApiModelProperty(value = "中心y坐标", position = 7)
    @NotNull(message = "{map.originY.require}", groups = DefaultGroup.class)
    private Double originY;

    @ApiModelProperty(value = "角度", position = 8)
    private Double originYaw;

    @ApiModelProperty(value = "分辨率", position = 9)
    @NotNull(message = "{map.resolution.require}", groups = DefaultGroup.class)
    private Double resolution;

    @ApiModelProperty(value = "像素高度", position = 10)
    @Min(value = 0, message = "{map.height.require}", groups = DefaultGroup.class)
    private Double height;

    @ApiModelProperty(value = "像素宽度", position = 11)
    @Min(value = 0, message = "{map.width.require}", groups = DefaultGroup.class)
    private Double width;

    @ApiModelProperty(value = "创建人id", position = 13)
    private Long creator;

    @ApiModelProperty(value = "创建人名称", position = 14)
    private String creatorName;

    @ApiModelProperty(value = "创建时间", position = 15)
    private Date createDate;

    @ApiModelProperty(value = "发布时间", position = 16)
    private Date publishTime;

    /**
     * 路网md5值
     */
    private String pathMd5;

    /**
     * 定位文件md5值
     */
    private String locatingMd5;

    /**
     * 是否有草稿地图 1：有 0：没有
     */
    private Integer isDraft;

    /**
     * 是否有正式地图 1：有 0：没有
     */
    private Integer isProd;

    public void setResolution(Double resolution) {
        if (resolution != null) {
            BigDecimal val = new BigDecimal(resolution).setScale(2, BigDecimal.ROUND_HALF_UP);
            this.resolution = val.doubleValue();
        }
    }

    public Double getResolution() {
        if (resolution != null) {
            BigDecimal val = new BigDecimal(resolution).setScale(2, BigDecimal.ROUND_HALF_UP);
            this.resolution = val.doubleValue();
        }
        return resolution;
    }
}
