package com.youibot.vehicle.scheduler.modules.sys.controller;

import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.AssertUtils;
import com.youibot.vehicle.scheduler.common.validator.ValidatorUtils;
import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.DefaultGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysRoleDTO;
import com.youibot.vehicle.scheduler.modules.sys.service.SysRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色管理
 */
@RestController
@RequestMapping("/sys/role")
@Api(tags="角色管理")
public class SysRoleController {
	@Autowired
	private SysRoleService sysRoleService;

	@GetMapping("page")
	@ApiOperation("分页")
	@ApiImplicitParams({
		@ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
		@ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
		@ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
		@ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String") ,
		@ApiImplicitParam(name = "name", value = "角色名", paramType = "query", dataType="String")
	})
	//@RequiresPermissions("sys:role:page")
	public Result<PageData<SysRoleDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
		PageData<SysRoleDTO> page = sysRoleService.page(params);

		return new Result<PageData<SysRoleDTO>>().ok(page);
	}

	@GetMapping("list")
	@ApiOperation("列表")
	//@RequiresPermissions("sys:role:list")
	public Result<List<SysRoleDTO>> list(){
		List<SysRoleDTO> data = sysRoleService.list(new HashMap<>(1));

		return new Result<List<SysRoleDTO>>().ok(data);
	}

	@GetMapping("{id}")
	@ApiOperation("信息")
	//@RequiresPermissions("sys:role:info")
	public Result<SysRoleDTO> get(@PathVariable("id") Long id){
		SysRoleDTO data = sysRoleService.get(id);
		return new Result<SysRoleDTO>().ok(data);
	}

	@LogOperation("log.controller.sys.role.insert")
	@PostMapping
	@ApiOperation("保存")
	//@RequiresPermissions("sys:role:save")
	public Result<SysRoleDTO> save(@RequestBody SysRoleDTO dto){
		//效验数据
		ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

		SysRoleDTO sysRoleDTO = sysRoleService.save(dto);

		return Result.suc(sysRoleDTO);
	}

	@LogOperation("log.controller.sys.role.update")
	@PutMapping
	@ApiOperation("修改")
	//@RequiresPermissions("sys:role:update")
	public Result update(@RequestBody SysRoleDTO dto){
		//效验数据
		ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

		sysRoleService.update(dto);

		return Result.suc(sysRoleService.get(dto.getId()));
	}

	@LogOperation("log.controller.sys.role.delete")
	@DeleteMapping
	@ApiOperation("删除")
	//@RequiresPermissions("sys:role:delete")
	public Result delete(@RequestBody Long[] ids){
		//效验数据
		AssertUtils.isArrayEmpty(ids, "id");

		sysRoleService.delete(ids);

		return new Result();
	}
}