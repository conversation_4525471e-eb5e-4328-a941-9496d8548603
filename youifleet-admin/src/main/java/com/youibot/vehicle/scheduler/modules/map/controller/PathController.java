package com.youibot.vehicle.scheduler.modules.map.controller;

import cn.hutool.core.collection.CollUtil;
import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.exception.ErrorCode;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.ValidatorUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.dto.ConbinationPathDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.PathDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.PathExtendsParamDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.ReducePathDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.service.PathService;
import com.youibot.vehicle.scheduler.modules.map.utils.CodeFormatUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.*;

@RestController("pathController")
@RequestMapping(value = "/map/paths", produces = "application/json")
@Api(value = "路径", tags = "路径", description = "路径管理接口，路径是指两个标志点中间的连接线路。通过指定路径的方向，会自动生成相应的sidePath数据")
public class PathController {

    @Autowired
    private PathService pathService;


    @ApiOperation(value = "列表（不区分定位图）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean", paramType = "query")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<Path>> getAll(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        List<Path> paths = pathService.searchAll(searchMap);
        return Result.suc(paths);
    }

    @ApiOperation(value = "列表（区分定位图）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean", paramType = "query")
    })
    @GetMapping("/getAllWithLocatingCode")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Map<String, List<ReducePathDTO>>> getAllWithLocatingCode(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        List<ReducePathDTO> pathList = pathService.searchAllWithLocatingCode(searchMap);
        return Result.suc(changeToReducePathDTOMap(pathList));
    }

    private Map<String, List<ReducePathDTO>> changeToReducePathDTOMap(List<ReducePathDTO> conbinationList){
        Map<String, List<ReducePathDTO>> map = new HashMap<>();
        if (CollUtil.isNotEmpty(conbinationList)) {
            conbinationList.forEach(path -> {
                String pathCode = CodeFormatUtils.getPathCode(path.getVehicleMapCode(), path.getStartMarkerCode(), path.getEndMarkerCode());
                List<ReducePathDTO> paths = map.computeIfAbsent(pathCode, k -> new ArrayList<>());
                path.setCommonPathCode(pathCode);
                paths.add(path);
            });
            //对每一对分组路径排序，对于map_L_1_2、map_L_2_1，使得总是map_L_1_2在前，map_L_2_1在后
            map.values().forEach(list -> {
                list.sort((p1, p2) -> {
                    String p1StartCode = CodeFormatUtils.getIntegerCodeV2(p1.getStartMarkerCode());
                    String p2StartCode = CodeFormatUtils.getIntegerCodeV2(p2.getStartMarkerCode());
                    Integer integerP1Start = Optional.ofNullable(p1StartCode).map(Integer::valueOf).orElse(0);
                    Integer integerP2Start = Optional.ofNullable(p2StartCode).map(Integer::valueOf).orElse(0);
                    return integerP1Start.compareTo(integerP2Start);
                });
            });
        }
        return map;
    }

    private Map<String, List<ConbinationPathDTO>> changetoMap(List<PathDTO> pathList){
        Map<String, List<ConbinationPathDTO>> map = new HashMap<>();
        if (CollUtil.isNotEmpty(pathList)) {
            List<ConbinationPathDTO> conbinationList = ConvertUtils.sourceToTarget(pathList, ConbinationPathDTO.class);
            conbinationList.forEach(path -> {
                String pathCode = CodeFormatUtils.getPathCode(path.getVehicleMapCode(), path.getStartMarkerCode(), path.getEndMarkerCode());
                List<ConbinationPathDTO> paths = map.computeIfAbsent(pathCode, k -> new ArrayList<>());
                path.setCommonPathCode(pathCode);
                paths.add(path);
            });
            //对每一对分组路径排序，对于map_L_1_2、map_L_2_1，使得总是map_L_1_2在前，map_L_2_1在后
            map.values().forEach(list -> {
                list.sort((p1, p2) -> {
                    String p1StartCode = CodeFormatUtils.getIntegerCodeV2(p1.getStartMarkerCode());
                    String p2StartCode = CodeFormatUtils.getIntegerCodeV2(p2.getStartMarkerCode());
                    Integer integerP1Start = Optional.ofNullable(p1StartCode).map(Integer::valueOf).orElse(0);
                    Integer integerP2Start = Optional.ofNullable(p2StartCode).map(Integer::valueOf).orElse(0);
                    return integerP1Start.compareTo(integerP2Start);
                });
            });
        }
        return map;
    }

    @LogOperation("log.controller.path.insert")
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "list", value = "路径", required = true, allowMultiple = true, dataType = "PathDTO", paramType = "body")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Result<Map<String, List<ConbinationPathDTO>>> insert(@RequestBody List<PathDTO> list) {
        //校验数据
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(path-> ValidatorUtils.validateEntity(path));
        }
        List<PathDTO> dtoList = this.pathService.insert(list);
        return Result.suc(changetoMap(dtoList));
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "路径ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "{vehicleMapCode}/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Path> getDetail(@PathVariable("vehicleMapCode") String vehicleMapCode,
                            @PathVariable("code") String code,
                            @RequestParam boolean isDraft) {
        Path path = pathService.selectByCode(vehicleMapCode, code, isDraft);
        return Result.suc(path);
    }

    @ApiOperation(value = "详情（区分定位图）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "路径ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "{vehicleMapCode}/{locatingCode}/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<PathDTO> getDetailWithLocatingCode(@PathVariable("vehicleMapCode") String vehicleMapCode,
                                                  @PathVariable("locatingCode") String locatingCode,
                                                  @PathVariable("code") String code,
                                                  @RequestParam boolean isDraft) {
        PathDTO path = pathService.selectByCodeWithLocatingCode(vehicleMapCode, locatingCode, code, isDraft);
        return Result.suc(path);
    }

    @ApiOperation(value = "详情集合（不区分定位图）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "codes", value = "路径code集合", required = true, dataType = "String", allowMultiple = true, paramType = "body"),
    })
    @PostMapping(value = "/batchQuery")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<Path>> batchQuery(@RequestBody List<String> codes,
                                         String vehicleMapCode,
                                         Boolean isDraft) {
        List<Path> paths = pathService.selectByCodes(vehicleMapCode, codes, isDraft);
        return Result.suc(paths);
    }

    @ApiOperation(value = "详情集合(区分定位图)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "codes", value = "路径code集合", required = true, dataType = "String", allowMultiple = true, paramType = "body"),
    })
    @PostMapping(value = "/batchQueryWithLocatingCode")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<PathDTO>> batchQueryWithLocatingCode(@RequestBody List<String> codes,
                                                         String vehicleMapCode,
                                                         String locatingCode,
                                                         Boolean isDraft) {
        List<PathDTO> paths = pathService.selectByCodesWithLocatingCode(vehicleMapCode, locatingCode, codes, isDraft);
        return Result.suc(paths);
    }

    @LogOperation("log.controller.path.update")
    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "路径code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "path", value = "路径", required = true, dataType = "Path")
    })
    @PutMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<PathDTO> update(@PathVariable("code") String code,
                                  @RequestBody @Valid PathDTO pathDTO) {
        if (pathDTO == null) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        pathDTO.setCode(code);
        pathService.update(pathDTO);
        return Result.suc(pathDTO);
    }

    @LogOperation("log.controller.path.update")
    @ApiOperation(value = "批量更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "paths", value = "路径集合", allowMultiple = true, required = true, dataType = "Path")
    })
    @PutMapping(value = "/batchUpdate")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Map<String, List<ConbinationPathDTO>>> batchUpdate(@RequestBody List<PathDTO> paths) {
        pathService.batchUpdate(paths);
        return Result.suc(changetoMap(paths));
    }

    @LogOperation("log.controller.path.update")
    @ApiOperation(value = "批量更新路径上的扩展参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "extendsParamList", value = "路径扩展参数集合", allowMultiple = true, required = true, dataType = "PathExtendsParamDTO")
    })
    @PutMapping(value = "/batchUpdateExtendParams")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<PathExtendsParamDTO>> batchUpdateExtendParams(@RequestBody List<PathExtendsParamDTO> extendsParamList) {
        pathService.batchUpdateExtendParams(extendsParamList);
        return Result.suc(extendsParamList);
    }

    @LogOperation("log.controller.path.delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "code", value = "路径ID", paramType = "path", required = true, dataType = "String"),
    })
    @DeleteMapping(value = "/{vehicleMapCode}/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result delete(@PathVariable("vehicleMapCode") String vehicleMapCode,
                         @PathVariable("code") String code) {
        this.pathService.delete(vehicleMapCode, code);
        return Result.suc();
    }

    @LogOperation("log.controller.path.delete")
    @ApiOperation(value = "批量删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "codes", value = "路径ID", required = true, dataType = "String"),
    })
    @DeleteMapping(value = "/batchDel/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result batchDel(@PathVariable("vehicleMapCode") String vehicleMapCode,
                           @RequestParam("codes") List<String> codes) {
        this.pathService.batchDel(vehicleMapCode, codes);
        return Result.suc();
    }

    @ApiOperation(value = "获取路径分组列表（开始点、结束点相同的路径为一组）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query")
    })
    @GetMapping(value = "/getGroupPath/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Map<String,List<Path>>> getGroupPath(@PathVariable("vehicleMapCode") String vehicleMapCode,
                                                       @RequestParam("isDraft") Boolean isDraft) {
        return Result.suc(this.pathService.getGroupPath(vehicleMapCode, isDraft));
    }

}
