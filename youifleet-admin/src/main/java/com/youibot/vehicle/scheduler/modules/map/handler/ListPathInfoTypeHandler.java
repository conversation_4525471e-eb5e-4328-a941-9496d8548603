package com.youibot.vehicle.scheduler.modules.map.handler;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.entity.PathInfo;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

@MappedTypes({List.class, PathInfo.class})
public class ListPathInfoTypeHandler extends AbstractJsonTypeHandler<List<PathInfo>> {

    @Override
    protected List<PathInfo> parse(String json) {
        return JSONArray.parseArray(json, PathInfo.class);
    }

    @Override
    protected String toJson(List<PathInfo> obj) {
        return JSONArray.toJSONString(obj);
    }
}
