package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.vehicle.scheduler.modules.map.handler.ListMarkerInfoTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.handler.MarkerChargePropTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.handler.MarkerParamTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "marker", autoResultMap = true)
public class Marker {

    /**
     * 编码
     */
    @TableId(type = IdType.INPUT)
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 角度
     */
    private Double angle;

    /**
     * 类型 ChargingMarker:充电点, NavigationMarker:导航点, WorkMarker:工作点
     */
    private String type;

    /**
     * 地图编码
     */
    private String vehicleMapCode;

    /**
     * 是否可泊车 1：可 0：不可
     */
    private Integer isPark;

    /**
     * 是否可避让 1：可 0：不可
     */
    private Integer isAvoid;

    /**
     * 路网点类型, 0:交叉路网点，1：普通路网点
     */
    private Integer networkMarkerType;

    /**
     * 点位信息
     */
    @TableField(typeHandler = ListMarkerInfoTypeHandler.class)
    private List<MarkerInfo> markInfos;

    /**
     * 点位扩展属性
     */
    @TableField(typeHandler = MarkerParamTypeHandler.class)
    private MarkerParam params;

    /**
     * 点位充电属性
     */
    @TableField(typeHandler = MarkerChargePropTypeHandler.class)
    private MarkerChargeProp chargeProp;
}
