package com.youibot.vehicle.scheduler.modules.sys.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 重置密码
 */
@Data
@ApiModel(value = "ResetPasswordDTO",description = "重置密码")
public class ResetPasswordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private List<Long> userIds;

    @ApiModelProperty(value = "登录密码")
    @NotBlank(message="{sysuser.password.require}")
    private String loginPassword;

    @ApiModelProperty(value = "新密码")
    @NotBlank(message="{sysuser.password.require}")
    private String newPassword;

}