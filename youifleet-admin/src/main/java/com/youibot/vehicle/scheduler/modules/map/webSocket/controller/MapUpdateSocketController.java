package com.youibot.vehicle.scheduler.modules.map.webSocket.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.map.config.MyEndpointConfigure;
import com.youibot.vehicle.scheduler.modules.map.dto.*;
import com.youibot.vehicle.scheduler.modules.map.entity.*;
import com.youibot.vehicle.scheduler.modules.map.utils.CodeFormatUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.MapUtils;
import com.youibot.vehicle.scheduler.modules.map.webSocket.module.SocketMessageModel;
import com.youibot.vehicle.scheduler.modules.map.webSocket.thread.MapSocketSendThread;
import com.youibot.vehicle.scheduler.modules.security.entity.SysUserTokenEntity;
import com.youibot.vehicle.scheduler.modules.security.service.ShiroService;
import com.youibot.vehicle.scheduler.modules.sys.entity.SysUserEntity;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月30日 下午6:39:39
 */
@RestController
@ServerEndpoint(value = "/mapUpdate", configurator = MyEndpointConfigure.class)
public class MapUpdateSocketController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MapUpdateSocketController.class);
    private static final Map<String, Session> WEBSOCKET_SESSION = new ConcurrentHashMap<>();
    private static final Map<String, String> SESSION_MAPCODE = new ConcurrentHashMap<>();
    private static final Map<String, String> SESSION_LOCATINGCODE = new ConcurrentHashMap<>();
    private static final Map<String, Long> SESSION_TOKEN = new ConcurrentHashMap<>();

    private static final Map<String, String> mapCodelock = new ConcurrentHashMap<>();
    private static final long max_idle_timeout = 30 * 1000;

    @Autowired
    private ShiroService shiroService;

    public static List<String> getActiveMap() {
        return new ArrayList<>(SESSION_MAPCODE.values());
    }

    @OnOpen
    public void onOpen(Session session) {
        Map<String, List<String>> requestParameterMap = session.getRequestParameterMap();
        //当前用户打开的地图
        String vehicleMapCode = getParamValue(requestParameterMap,"vehicleMapCode");
        //当前用户打开的定位图
        String locatingCode = getParamValue(requestParameterMap,"locatingCode");
        //登录用户信息id
        String token = getParamValue(requestParameterMap,"token");
        if (StringUtils.isEmpty(vehicleMapCode) || StringUtils.isEmpty(locatingCode) || StringUtils.isEmpty(token)) {
            String errorMsg = "vehicleMapCode 或 locatingCode 或 token 参数为空，连接断开";
            errorExit(session,errorMsg);
            return;
        }
        Long userId = getUserId(token);
        if (userId == null) {
            String errorMsg = "未登录，连接断开";
            errorExit(session,errorMsg);
            return;
        }

        WEBSOCKET_SESSION.put(session.getId(), session);
        SESSION_MAPCODE.put(session.getId(), vehicleMapCode);
        SESSION_LOCATINGCODE.put(session.getId(), locatingCode);
        SESSION_TOKEN.put(session.getId(), userId);
        session.setMaxIdleTimeout(max_idle_timeout);
    }

    /**
     * 获取key对应的参数
     */
    private String getParamValue(Map<String, List<String>> requestParameterMap, String key){
        List<String> vehicleMapCodeList = requestParameterMap.get(key);
        String val = null;
        if (!CollectionUtils.isEmpty(vehicleMapCodeList)) {
            val = vehicleMapCodeList.get(0);
        }
        return val;
    }

    /**
     * 异常退出
     */
    private void errorExit(Session session, String errorMsg){
        try {
            Result result = new Result().error(errorMsg);
            session.getBasicRemote().sendText(JSON.toJSONString(result));
            session.close();
        } catch (IOException e) {
            LOGGER.error(errorMsg, e);
        }
    }

    /**
     * 获取用户id
     */
    private Long getUserId(String token) {
        SysUserTokenEntity tokenEntity = shiroService.getByToken(token);
        if (tokenEntity == null) {
            return null;
        }
        //查询用户信息
        SysUserEntity userEntity = shiroService.getUser(tokenEntity.getUserId());
        if (userEntity == null || !userEntity.isUsable()) {
            return null;
        }
        return userEntity.getId();
    }

    private static void closeSession(Session session){
        WEBSOCKET_SESSION.remove(session.getId());
        SESSION_TOKEN.remove(session.getId());
        SESSION_MAPCODE.remove(session.getId());
        SESSION_LOCATINGCODE.remove(session.getId());
        try {
            session.close();
        } catch (IOException e) {
            LOGGER.error("关闭会话连接异常", e);
        }
    }

    @OnClose
    public void onClose(Session session) {
        LOGGER.debug("web socket close session {} .", session.getId());
        closeSession(session);
    }

    @OnError
    public void onError(Throwable throwable, Session session) {
        LOGGER.error("web socket error and close : {} .", throwable.getMessage());
        closeSession(session);
    }

    @Data
    private static class LocatingCodeChange{
        private String locatingCode;
    }

    // 获取消息
    @OnMessage
    public void onMessage(Session session, String message) {
        if(StringUtils.isEmpty(message) || !session.isOpen()){
            return;
        }
        LocatingCodeChange changeLocatingCodeObj = JSON.parseObject(message,LocatingCodeChange.class);
        String locatingCode = changeLocatingCodeObj.getLocatingCode();
        if(!StringUtils.isEmpty(locatingCode)){
            SESSION_LOCATINGCODE.put(session.getId(),locatingCode);
        }
    }

    /**
     * 新增的数据需要排除当前用户自己
     * 更新删除的不排除
     *
     * @param vehicleMapCode
     * @param type
     * @param list
     * @param <T>
     */
    public static <T> void sendMessageOfAdd(String vehicleMapCode, String type, List<T> list) {
        SocketMessageModel messageModel = new SocketMessageModel().add();
        fillData(type, list, messageModel);
        sendMessage(vehicleMapCode, messageModel);
    }

    public static <T> void sendMessageOfUpdate(String vehicleMapCode, String type, List<T> list) {
        SocketMessageModel messageModel = new SocketMessageModel().update();
        fillData(type, list, messageModel);
        sendMessage(vehicleMapCode, messageModel);
    }

    public static <T> void sendMessageOfDelete(String vehicleMapCode, String type, List<T> list) {
        SocketMessageModel messageModel = new SocketMessageModel().delete();
        fillData(type, list, messageModel);
        sendMessage(vehicleMapCode, messageModel);
    }

    public static <T> void sendMessageOfPublish(String vehicleMapCode, String type, List<T> list) {
        SocketMessageModel messageModel = new SocketMessageModel().publish();
        fillData(type, list, messageModel);
        sendMessage(vehicleMapCode, messageModel);
    }

    public static <T> void sendMessageOfDiscard(String vehicleMapCode, String type, List<T> list) {
        SocketMessageModel messageModel = new SocketMessageModel().discard();
        fillData(type, list, messageModel);
        sendMessage(vehicleMapCode, messageModel);
    }

    public static <T> void fillData(String type, List<T> list, SocketMessageModel messageModel) {
        switch (type) {
            case "Map":
                messageModel.setAgvMaps((List<VehicleMapDetailDTO>) list);
                break;
            case "LocatingMap":
                messageModel.setLocatingMaps((List<MapInfoDTO>) list);
                break;
            case "Marker":
                messageModel.setMarkers((List<Marker>) list);
                break;
            case "Path":
                messageModel.setPaths((List<Path>) list);
                break;
            case "MapArea":
                messageModel.setMapAreas((List<MapArea>) list);
                break;
            case "AutoDoor":
                messageModel.setAutoDoors((List<AutoDoor>) list);
                break;
            case "AirShowerDoor":
                messageModel.setAirShowerDoors((List<AirShowerDoor>) list);
                break;
            case "Elevator":
                messageModel.setElevators((List<Elevator>) list);
                break;
            default:
                break;
        }
    }

    /**
     * 该方法为统一入口：将消息存储到 MapSocketSendThread 的 messagePool 中
     *
     * @param vehicleMapCode
     * @param messageModel
     */
    public static void sendMessage(String vehicleMapCode, SocketMessageModel messageModel) {
        String lock = mapCodelock.computeIfAbsent(vehicleMapCode, mapCode -> mapCode + "_ws_msg");
        synchronized (lock.intern()) {
            ConcurrentLinkedQueue<SocketMessageModel> list = MapSocketSendThread.messagePool.computeIfAbsent(vehicleMapCode, (tmp) -> new ConcurrentLinkedQueue<>());
            list.add(messageModel);
            MapSocketSendThread.messagePool.put(vehicleMapCode, list);
        }
    }

    /**
     * 该方法为实际发送消息到前端的方法
     *
     * 给绑定了指定地图的会话，发消息
     * 不排除当前用户自己
     *
     * @param vehicleMapCode
     * @param message
     */
    public static void actualSendMessage(String vehicleMapCode, SocketMessageModel message) {
        List<String> relateIds = SESSION_MAPCODE.entrySet().stream().filter(item -> item.getValue().equals(vehicleMapCode)).map(Map.Entry::getKey).collect(Collectors.toList());
        List<Session> relateSessions = WEBSOCKET_SESSION.entrySet().stream().filter(item -> relateIds.contains(item.getKey())).map(Map.Entry::getValue).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relateSessions)) {
            return;
        }
        sendBatchMessage(relateSessions, message);
    }

    private static void sendBatchMessage(Collection<Session> list, SocketMessageModel message) {
        for (Session session : list) {
            sendSingleSessionMessage(session,message);
        }
    }

    private static void sendSingleSessionMessage(Session session, SocketMessageModel message) {
        if (session.isOpen()) {
            try {
                String result = resolve(session,message);
                session.getBasicRemote().sendText(result);
            } catch (Exception e) {
                LOGGER.error("web socket send error ", e);
                closeSession(session);
            }
        }
    }

    private static String resolve(Session session, SocketMessageModel message) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(message));
        String locatingCode = SESSION_LOCATINGCODE.get(session.getId());

        List<Marker> markers = message.getMarkers();
        if (!CollectionUtils.isEmpty(markers)) {
            List<MarkerDTO> dtoList = MapUtils.toMarkerDTO(locatingCode, markers);
            jsonObject.put("markers", dtoList);
        }

        List<Path> paths = message.getPaths();
        if (!CollectionUtils.isEmpty(paths)) {
            List<PathDTO> dtoList = MapUtils.toPathDTO(locatingCode, paths);
            jsonObject.put("paths", combinePath(dtoList));
        }
        return JSON.toJSONString(Result.suc(jsonObject));
    }

    /**
     * 将路径集合分组，例如，使得map_L_1_2、map_L_2_1分为一组:
     * key: 两个点位中编号较小的点位在前的路径编号，例如：map_L_1_2、map_L_2_1，则key是map_L_1_2
     * value: 路径集合：map_L_1_2、map_L_2_1
     * @param dtoList
     * @return
     */
    private static Map<String, List<ReducePathDTO>> combinePath(List<PathDTO> dtoList) {
        //将路径集合分组，使得map_L_1_2、map_L_2_1分为一组
        Map<String, List<ReducePathDTO>> map = new HashMap<>();
        if (CollUtil.isNotEmpty(dtoList)) {
            List<ReducePathDTO> conbinationList = ConvertUtils.sourceToTarget(dtoList, ReducePathDTO.class);
            for (ReducePathDTO item : conbinationList) {
                String pathCode = CodeFormatUtils.getPathCode(item.getVehicleMapCode(), item.getStartMarkerCode(), item.getEndMarkerCode());
                List<ReducePathDTO> list = map.getOrDefault(pathCode, new ArrayList<>());
                item.setCommonPathCode(pathCode);
                list.add(item);
                map.put(pathCode, list);
            }
            //对每一对分组路径排序，对于map_L_1_2、map_L_2_1，使得总是map_L_1_2在前，map_L_2_1在后
            map.values().forEach(list -> {
                list.sort((p1, p2) -> {
                    String p1StartCode = CodeFormatUtils.getIntegerCodeV2(p1.getStartMarkerCode());
                    String p2StartCode = CodeFormatUtils.getIntegerCodeV2(p2.getStartMarkerCode());
                    Integer integerP1Start = Optional.ofNullable(p1StartCode).map(Integer::valueOf).orElse(0);
                    Integer integerP2Start = Optional.ofNullable(p2StartCode).map(Integer::valueOf).orElse(0);
                    return integerP1Start.compareTo(integerP2Start);
                });
            });
        }
        return map;
    }

}
