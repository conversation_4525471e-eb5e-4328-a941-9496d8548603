package com.youibot.vehicle.scheduler.modules.map.controller;

import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.ValidatorUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.entity.MarkerResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.MarkerResourcePool;
import com.youibot.vehicle.scheduler.modules.map.dto.MarkerDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.MarkerWithVehicleDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.MarkerWithWarehouseDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.service.MarkerService;
import com.youibot.vehicle.scheduler.modules.map.utils.MapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController("markerController")
@RequestMapping(value = "/map/markers", produces = "application/json")
@Api(value = "标记点", tags = "标记点", description = "标志管理接口，标志是指地图所有的标志点位资源，例如：导航点，充电点，待机点。")
public class MarkerController {
    private static final Logger LOGGER = LoggerFactory.getLogger(MarkerController.class);

    @Autowired
    private MarkerService markerService;
    @Autowired
    private MarkerResourcePool markerResourcePool;


    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<PageData<Marker>> page(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        PageData<Marker> page = markerService.page(searchMap);
        return Result.suc(page);
    }

    @ApiOperation(value = "列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean", paramType = "query")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<Marker>> getAll(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        List<Marker> markers = markerService.searchAll(searchMap);
        return Result.suc(markers);
    }

    @ApiOperation(value = "点位列表（区分定位图）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean", paramType = "query")
    })
    @GetMapping("/getAllWithLocatingCode")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MarkerDTO>> getAllWithLocatingCode(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        List<MarkerDTO> markers = markerService.searchAllWithLocatingCode(searchMap);
        return Result.suc(markers);
    }

    @ApiOperation(value = "点位列表（区分定位图、关联库位）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean", paramType = "query")
    })
    @GetMapping("/getAllWithWarehouse")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MarkerWithWarehouseDTO>> searchAllWithWarehouse(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        List<MarkerWithWarehouseDTO> markers = markerService.searchAllWithWarehouse(searchMap);
        return Result.suc(markers);
    }

    @ApiOperation(value = "列表(查询所有地图的点位)")
    @GetMapping("/all")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<Marker>> all() {
        List<Marker> allWithOutCondition = markerService.getAllWithOutCondition();
        return Result.suc(allWithOutCondition);
    }

    @LogOperation("log.controller.marker.insert")
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "markerDTO", value = "标记点", required = true, dataType = "MarkerDTO")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Result<MarkerDTO> insert(@RequestBody @Validated MarkerDTO markerDTO) {
        MarkerDTO insert = this.markerService.insert(markerDTO);
        return Result.suc(insert);
    }

    @LogOperation("log.controller.marker.transcribe")
    @ApiOperation(value = "录制")
    @ApiImplicitParam(name = "marker", value = "标记点", required = true, dataType = "MarkerDTO")
    @PostMapping("/transcribe")
    public Result<MarkerDTO> transcribe(@RequestBody @Validated MarkerDTO markerDTO) {
        return Result.suc(this.markerService.transcribe(markerDTO));
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "标记点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "/{vehicleMapCode}/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<MarkerWithVehicleDTO> getDetail(@PathVariable("code") String code,
                                                  @PathVariable("vehicleMapCode") String vehicleMapCode,
                                                  @RequestParam boolean isDraft) {
        Marker marker = markerService.selectByCode(vehicleMapCode, code, isDraft);
        MarkerWithVehicleDTO markerDTO = ConvertUtils.sourceToTarget(marker, MarkerWithVehicleDTO.class);
        if (Objects.nonNull(marker)) {
            MarkerResource resource = markerResourcePool.get(marker.getCode());
            if (Objects.nonNull(resource)) {
                markerDTO.setVehicleCode(resource.getOccupyVehicleCode());
            }
        }
        return Result.suc(markerDTO);
    }

    @ApiOperation(value = "详情（区分定位图）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "标记点ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "/{vehicleMapCode}/{locatingCode}/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<MarkerDTO> getDetail(@PathVariable("code") String code,
                                       @PathVariable("vehicleMapCode") String vehicleMapCode,
                                       @PathVariable("locatingCode") String locatingCode,
                                       @RequestParam boolean isDraft) {
        Marker marker = markerService.selectByCode(vehicleMapCode, code, isDraft);
        return Result.suc(MapUtils.convertToMarkerDTO(locatingCode,marker));
    }

    @ApiOperation(value = "详情集合（不区分定位图）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "codes", value = "标记点code集合", required = true, dataType = "String", allowMultiple = true, paramType = "body"),
    })
    @PostMapping(value = "/batchQuery")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<Marker>> batchQuery(@RequestBody List<String> codes,
                                           String vehicleMapCode,
                                           Boolean isDraft) {
        return Result.suc(markerService.selectByCodes(vehicleMapCode, codes, isDraft));
    }

    @ApiOperation(value = "详情集合(区分定位图)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "codes", value = "标记点code集合", required = true, dataType = "String", allowMultiple = true, paramType = "body"),
    })
    @PostMapping(value = "/batchQueryWithLocatingCode")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MarkerDTO>> batchQueryOfLocating(@RequestBody List<String> codes,
                                                        String vehicleMapCode,
                                                        String locatingCode,
                                                        Boolean isDraft) {
        return Result.suc(markerService.selectByCodesWithLocatingCode(vehicleMapCode, locatingCode, codes, isDraft));
    }

    @ApiOperation(value = "充电点集合")
    @GetMapping(value = "/chargeMarkerList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图名称", required = false, dataType = "String", paramType = "query")
    })
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<Marker>> chargeMarkerList(@RequestParam(required = false) String vehicleMapCode) {
        List<Marker> markers = markerService.getChargeMarkerList(vehicleMapCode);
        return Result.suc(markers);
    }

    @ApiOperation(value = "泊车点集合")
    @GetMapping(value = "/parkMarkerList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图名称", required = false, dataType = "String", paramType = "query")
    })
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<Marker>> parkMarkerList(@RequestParam(required = false) String vehicleMapCode) {
        List<Marker> markers = markerService.getParkMarkerList(vehicleMapCode);
        return Result.suc(markers);
    }

    @LogOperation("log.controller.marker.update")
    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "标记点code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "markerDTO", value = "标记点", required = true, dataType = "MarkerDTO")
    })
    @PutMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<MarkerDTO> update(@PathVariable("code") String code,
                                    @RequestBody @Validated MarkerDTO markerDTO) {
        markerDTO.setCode(code);
        this.markerService.update(markerDTO);
        return Result.suc(markerDTO);
    }

    @LogOperation("log.controller.marker.update")
    @ApiOperation(value = "批量更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "markers", value = "标记点集合", allowMultiple = true, required = true, dataType = "Marker")
    })
    @PutMapping(value = "/batchUpdate")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MarkerDTO>> batchUpdate(@RequestBody List<MarkerDTO> markers) {
        long start = System.currentTimeMillis();
        LOGGER.debug("markers: {} , 接收：{}", markers.size(),start);
        //效验数据
        if(!CollectionUtils.isEmpty(markers)){
            markers.parallelStream().forEach(ValidatorUtils::validateEntity);
        }
        List<MarkerDTO> markerDTOS = markerService.batchUpdate(markers);
        long timeDiff = System.currentTimeMillis() - start;
        LOGGER.debug("markers: {} , 耗时：{}", markers.size(),timeDiff);
        return Result.suc(markerDTOS);
    }

    @LogOperation("log.controller.marker.delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "标记点code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/{vehicleMapCode}/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result delete(@PathVariable("vehicleMapCode") String vehicleMapCode,
                         @PathVariable("code") String code) {
        this.markerService.deleteByCode(vehicleMapCode, code);
        return Result.suc();
    }

    @ApiOperation(value = "根据标记点类型查询与当前标记点有路径的所有点位列表", notes = "根据标记点类型查询与当前标记点有路径的所有点位列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "标记ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "标记点类型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "/{code}/listMakerCodes")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<Marker>> listMakerIds(@PathVariable("code") String code,
                                             @RequestParam String vehicleMapCode,
                                             @RequestParam(required = false) String type,
                                             @RequestParam boolean isDraft) {
        List<Marker> markers = this.markerService.selectRelatePathMarkerIdsByType(code, vehicleMapCode, type, isDraft);
        return Result.suc(markers);
    }

//    @ApiOperation(value = "根据标记点类型查询离我们当前位置最近的标记点", notes = "根据标记点类型查询离我们最近的标记点")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "vehicleMapCode", value = "地图id", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "agvCode", value = "小车编号", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "type", value = "标记点类型", required = true, dataType = "String")
//    })
//    @GetMapping(value = "/listMakerByScope")
//    @ResponseStatus(value = HttpStatus.OK)
//    public Result<List<MarkerScope>> listMakerByScope(@RequestParam String vehicleMapCode,
//                                              @RequestParam String agvCode,
//                                              @RequestParam String type) {
//        return Result.suc(this.markerService.selectMarkersByTypeAndScope(vehicleMapCode,agvCode,type));
//    }

//    @ApiOperation(value = "查看两点是否可达接口")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "startMarkMapId", value = "起点地图Id", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "startMarkCode", value = "起点标记Code", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "endMarkMapId", value = "终点地图Id", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "endMarkCode", value = "终点标记Code", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
//    })
//    @GetMapping("/searchMarkCodeToMarkCode")
//    @ResponseStatus(value = HttpStatus.OK)
//    public Result<MarkerPathResult> searchMarkCodeToMarkCode(@RequestParam String startMarkMapId, @RequestParam String startMarkCode, @RequestParam String endMarkMapId, @RequestParam String endMarkCode, @RequestParam boolean isDraft){
//        MarkerPathResult markerPathResult = new MarkerPathResult();
//        try {
//            markerPathResult = this.markerService.searchMarkCodeToMarkCode(startMarkMapId, startMarkCode, endMarkMapId, endMarkCode, isDraft);
//        } catch (InterruptedException e) {
//            // 路径规划失败
//            markerPathResult.setResult(false);
//        }
//        return Result.suc(markerPathResult);
//    }

}
