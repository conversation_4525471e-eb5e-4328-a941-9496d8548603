package com.youibot.vehicle.scheduler.modules.map.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月23日 下午4:24:59
 */
@Configuration //thomas test
public class WebSocketConfig {
	@Bean
	public ServerEndpointExporter serverEndpointExporter() {
		return new ServerEndpointExporter();
	}


    @Bean
    public MyEndpointConfigure newConfigure(){
        return new MyEndpointConfigure();
    }
}
