package com.youibot.vehicle.scheduler.modules.device.utils;

import cn.hutool.core.collection.CollUtil;
import com.intelligt.modbus.jlibmodbus.exception.ModbusIOException;
import com.intelligt.modbus.jlibmodbus.exception.ModbusNumberException;
import com.intelligt.modbus.jlibmodbus.exception.ModbusProtocolException;
import com.youibot.vehicle.scheduler.common.utils.ApplicationUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraph;
import com.youibot.vehicle.scheduler.engine.pathplan.constant.PathPlanConstant;
import com.youibot.vehicle.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.vehicle.scheduler.modules.log.constant.LogConstant;
import com.youibot.vehicle.scheduler.modules.log.dto.SysLogPushDTO;
import com.youibot.vehicle.scheduler.modules.log.util.SysLogUtils;
import com.youibot.vehicle.scheduler.modules.map.dto.ElevatorDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.ElevatorRelateDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.service.ElevatorService;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.net.UnknownHostException;
import java.util.*;

import static com.youibot.vehicle.scheduler.engine.pathplan.constant.PathPlanConstant.*;
import static com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant.*;

public class ElevatorUtils {

    private static final Logger logger = LoggerFactory.getLogger(ElevatorUtils.class);

    private static MapGraph mapGraph = (MapGraph) ApplicationUtils.getBean("mapGraph");
    private static VehiclePool vehiclePool = (VehiclePool) ApplicationUtils.getBean("defaultVehiclePool");
    private static ElevatorService elevatorService = (ElevatorService) ApplicationUtils.getBean("elevatorServiceImpl");

    /**
     * 判断是否已经可以开始乘梯
     */
    public static boolean isTake(ElevatorRelateDTO elevatorRelateDTO, String ip, int port, String readFunctionCode, int elevatorControlAddress, int doorOpenAddress) {
        try {
            int[] controlValues = ModbusUtils.readFunctionCode(ip, port, readFunctionCode, elevatorControlAddress, 1);
            int[] openValues = ModbusUtils.readFunctionCode(ip, port, readFunctionCode, doorOpenAddress, 1);
            if (controlValues[0] == 0 && openValues[0] == elevatorRelateDTO.getCloseStatusValue()) {
                logger.debug("-----电梯{}信号已重置，可以开始乘梯-----", elevatorRelateDTO.getCode());
                return true;
            }
        } catch (Exception e) {
            logger.error("读取电梯信号异常", e);
            if(e instanceof UnknownHostException  || e instanceof ModbusIOException){
                NoticeMessageUtils.pushQueue(NoticeMessage.builder()
                        .code(ELEVATOR_CONNECT_ERROR)
                        .deviceId(elevatorRelateDTO.getCode())
                        .placeholders(Collections.singletonList(elevatorRelateDTO.getCode()))
                        .build());
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_ELEVATOR)
                        .content("log.system.elevator.connect.error")
                        .data(String.join(",",Arrays.asList(elevatorRelateDTO.getCode())))
                        .message(LogExceptionStackUtil.LogExceptionStack(e)).build());
            }else if(e instanceof ModbusNumberException || e instanceof ModbusProtocolException){
                NoticeMessageUtils.pushQueue(NoticeMessage.builder()
                        .code(ELEVATOR_READ_ERROR)
                        .deviceId(elevatorRelateDTO.getCode())
                        .placeholders(Collections.singletonList(elevatorRelateDTO.getCode()))
                        .build());
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_ELEVATOR)
                        .content("log.system.elevator.read.error")
                        .data(String.join(",",Arrays.asList(elevatorRelateDTO.getCode())))
                        .message(LogExceptionStackUtil.LogExceptionStack(e)).build());
            }
        }
        return false;
    }
    /**
     * 判断电梯是否达到
     */
    public static boolean isArrived(ElevatorRelateDTO elevatorRelateDTO, String ip, int port, String readFunctionCode, int elevatorArrivedAddress, int doorOpenAddress) {
        try {
            int[] arrivedValues = ModbusUtils.readFunctionCode(ip, port, readFunctionCode,elevatorArrivedAddress,1);
            int[] openValues = ModbusUtils.readFunctionCode(ip, port, readFunctionCode, doorOpenAddress, 1);
            if (arrivedValues[0] == elevatorRelateDTO.getDestStatusValue() && openValues[0] == elevatorRelateDTO.getOpenStatusValue()) {
                logger.debug("-----电梯{}已经到达-----", elevatorRelateDTO.getCode());
                return true;
            }
        } catch (Exception e) {
            logger.error("读取电梯到达信号异常", e);
            if(e instanceof UnknownHostException  || e instanceof ModbusIOException){
                NoticeMessageUtils.pushQueue(NoticeMessage.builder()
                        .code(ELEVATOR_CONNECT_ERROR)
                        .deviceId(elevatorRelateDTO.getCode())
                        .placeholders(Collections.singletonList(elevatorRelateDTO.getCode()))
                        .build());
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_ELEVATOR)
                        .content("log.system.elevator.connect.error")
                        .data(String.join(",",Arrays.asList(elevatorRelateDTO.getCode())))
                        .message(LogExceptionStackUtil.LogExceptionStack(e)).build());
            }else if(e instanceof ModbusNumberException || e instanceof ModbusProtocolException){
                NoticeMessageUtils.pushQueue(NoticeMessage.builder()
                        .code(ELEVATOR_READ_ERROR)
                        .deviceId(elevatorRelateDTO.getCode())
                        .placeholders(Collections.singletonList(elevatorRelateDTO.getCode()))
                        .build());
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_ELEVATOR)
                        .content("log.system.elevator.read.error")
                        .data(String.join(",",Arrays.asList(elevatorRelateDTO.getCode())))
                        .message(LogExceptionStackUtil.LogExceptionStack(e)).build());
            }
        }
        return false;
    }

    /**
     * 呼梯
     */
    public static void callElevator(ElevatorDTO elevator, String ip, int port, String writeFunctionCode, int controlAddress, int value) {
        try {
            ModbusUtils.writeFunctionCode(ip, port, writeFunctionCode, controlAddress, value);
        } catch (Exception e) {
            logger.error("呼叫电梯异常", e);
            if(e instanceof UnknownHostException  || e instanceof ModbusIOException){
                NoticeMessageUtils.pushQueue(NoticeMessage.builder()
                        .code(ELEVATOR_CONNECT_ERROR)
                        .deviceId(elevator.getCode())
                        .placeholders(Collections.singletonList(elevator.getCode()))
                        .build());
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_ELEVATOR)
                        .content("log.system.elevator.connect.error")
                        .data(String.join(",",Arrays.asList(elevator.getCode())))
                        .message(LogExceptionStackUtil.LogExceptionStack(e)).build());
            }else if(e instanceof ModbusNumberException || e instanceof ModbusProtocolException){
                NoticeMessageUtils.pushQueue(NoticeMessage.builder()
                        .code(ELEVATOR_WRITE_ERROR)
                        .deviceId(elevator.getCode())
                        .placeholders(Collections.singletonList(elevator.getCode()))
                        .build());
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_ELEVATOR)
                        .content("log.system.elevator.write.error")
                        .data(String.join(",",Arrays.asList(elevator.getCode())))
                        .message(LogExceptionStackUtil.LogExceptionStack(e)).build());
            }
        }
    }

    /**
     * 判断电梯是否正在使用
     */
    public static boolean isInUsedElevator(List<ElevatorDTO> elevators) {
        boolean isInUsed = false;
        List<Vehicle> vehicles = vehiclePool.getAll();
        if (CollectionUtils.isEmpty(vehicles)) {
            return isInUsed;
        }
        for (ElevatorDTO elevatorDTO : elevators) {
            Set<String> elevatorMarkerCodes = Optional.ofNullable(mapGraph.getElevatorCodeToElevatorMarkerCodes().get(elevatorDTO.getCode())).orElse(new HashSet<>());
            for (Vehicle vehicle : vehicles) {
                List<Path> runningPaths = vehicle.getOccupyPaths();
                if (CollectionUtils.isEmpty(runningPaths)) {
                    continue;
                }
                for (Path path : runningPaths) {
                    if (isElevatorPath(path) && (elevatorMarkerCodes.contains(path.getStartMarkerCode()) || elevatorMarkerCodes.contains(path.getEndMarkerCode()))) {
                        isInUsed = true;
                    }
                }
            }
        }
        return isInUsed;
    }

    /**
     * 判断机器人是否正在使用电梯
     * 1、运行路径包含电梯路径，说明在使用电梯
     */
    public static boolean isTakingElevator(Vehicle vehicle) {
        List<Path> runningPaths = vehicle.getRunningPaths();
        if (CollUtil.isNotEmpty(runningPaths)) {
            return containsElevatorPath(runningPaths);
        } else {
            List<Path> waitRunPaths = vehicle.getWaitRunPaths();
            List<Path> executedPaths = vehicle.getExecutedPaths();
            if (containsElevatorPath(waitRunPaths) && containsElevatorPath(executedPaths)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否包含电梯路径
     */
    private static boolean containsElevatorPath(List<Path> paths){
        for (Path path : paths) {
            if (isElevatorPath(path)) {
                return true;
            }
        }
        return false;
    }

    /**
     *校验路径是否为电梯路径，只要是入梯，出梯，乘梯路径，都属于电梯路径
     *
     * @param path
     * @return
     */
    public static boolean isElevatorPath(Path path) {
        Integer type = path.getNavigationType();
        if (StringUtils.isEmpty(type)) {
            return false;
        }
        if (type == SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR || type == SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR
                || type == SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR) {
            return true;
        }
        return false;
    }

    /**
     * 验证电梯是否可用(电梯是否到位，门是否已开)
     *
     * @param vehicleCode
     * @param applyPath
     * @return
     */
    public static boolean verifyElevatorAvailable(String vehicleCode, Path applyPath) {
        if (CollectionUtils.isEmpty(elevatorService.selectList())) {
            return true;
        }
        if (!ElevatorUtils.isElevatorPath(applyPath)){
            return true;
        }
        ElevatorRelateDTO elevatorRelateDTO;
        if (applyPath.getNavigationType() == PathPlanConstant.SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR) {
            elevatorRelateDTO = elevatorService.selectByMarkerCode(applyPath.getEndMarkerCode());
            if (elevatorRelateDTO == null) {
                logger.error("获取不到点位{}与电梯的绑定关系", applyPath.getEndMarkerCode());
                return true;
            }
            //检查电梯是否到达，门是否已开
            boolean flag = ElevatorUtils.isArrived(elevatorRelateDTO, elevatorRelateDTO.getIp(), elevatorRelateDTO.getPort(), elevatorRelateDTO.getReadFunctionCode(), elevatorRelateDTO.getDestAddress(), elevatorRelateDTO.getOpenAddress());
            if (flag) {
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_ELEVATOR)
                        .content("log.system.resource.elevator.apply.success")
                        .data(String.join(",",Arrays.asList(elevatorRelateDTO.getCode())))
                        .vehicleCodes(vehicleCode)
                        .build());
            }
            return flag;
        } else if (applyPath.getNavigationType() == PathPlanConstant.SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR) {
            elevatorRelateDTO = elevatorService.selectByMarkerCode(applyPath.getStartMarkerCode());
            if (elevatorRelateDTO == null) {
                logger.error("获取不到点位{}与电梯的绑定关系", applyPath.getStartMarkerCode());
                return true;
            }
            //检查电梯是否到达，门是否已开
            boolean flag = ElevatorUtils.isArrived(elevatorRelateDTO, elevatorRelateDTO.getIp(), elevatorRelateDTO.getPort(), elevatorRelateDTO.getReadFunctionCode(), elevatorRelateDTO.getDestAddress(), elevatorRelateDTO.getOpenAddress());
            if (flag) {
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_ELEVATOR)
                        .content("log.system.resource.elevator.ride.success")
                        .data(String.join(",",Arrays.asList(elevatorRelateDTO.getCode())))
                        .vehicleCodes(vehicleCode)
                        .build());
            }
            return flag;
        } else if (applyPath.getNavigationType() == PathPlanConstant.SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR) {
            elevatorRelateDTO = elevatorService.selectByMarkerCode(applyPath.getStartMarkerCode());
            if (elevatorRelateDTO == null) {
                logger.error("获取不到点位{}与电梯的绑定关系", applyPath.getStartMarkerCode());
                return true;
            }
            return ElevatorUtils.isTake(elevatorRelateDTO, elevatorRelateDTO.getIp(), elevatorRelateDTO.getPort(), elevatorRelateDTO.getReadFunctionCode(), elevatorRelateDTO.getOutOperateAddress(), elevatorRelateDTO.getOpenAddress());
        }
        return true;
    }
}
