package com.youibot.vehicle.scheduler.modules.map.service;

import com.youibot.vehicle.scheduler.modules.map.entity.*;

import java.util.List;

public interface UndoManageService {

    void undo(String mapCode);

    void redo(String mapCode);

    boolean canUndo(String mapCode);

    boolean canRedo(String mapCode);

    void clear(String mapCode);

    void pushToUndoPool(String mapCode, Integer actionType, List<MarkerDraft> oriMarkers, List<PathDraft> oriPaths, List<MapAreaDraft> oriMapAreas,
                        List<MarkerDraft> preMarkers, List<PathDraft> prePaths, List<MapAreaDraft> preMapAreas);
}
