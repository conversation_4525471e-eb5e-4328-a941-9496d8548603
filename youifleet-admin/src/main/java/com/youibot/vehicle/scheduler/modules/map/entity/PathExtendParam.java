package com.youibot.vehicle.scheduler.modules.map.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
@ApiModel(value = "PathExtendParam", description = "可变路径扩展参数")
public class PathExtendParam implements Cloneable {

    @ApiModelProperty(value = "可变路径扩展参数key")
    private String key ;

    @ApiModelProperty(value = "可变路径扩展参数value")
    private String value ;

}