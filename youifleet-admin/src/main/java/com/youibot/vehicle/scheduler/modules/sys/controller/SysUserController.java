package com.youibot.vehicle.scheduler.modules.sys.controller;

import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.ValidatorUtils;
import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.DefaultGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.security.password.PasswordUtils;
import com.youibot.vehicle.scheduler.modules.security.service.SysUserTokenService;
import com.youibot.vehicle.scheduler.modules.security.user.SecurityUser;
import com.youibot.vehicle.scheduler.modules.security.user.UserDetail;
import com.youibot.vehicle.scheduler.modules.sys.dto.PasswordDTO;
import com.youibot.vehicle.scheduler.modules.sys.dto.ResetPasswordDTO;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysUserDTO;
import com.youibot.vehicle.scheduler.modules.sys.enums.UserStatusEnum;
import com.youibot.vehicle.scheduler.modules.sys.service.SysRoleUserService;
import com.youibot.vehicle.scheduler.modules.sys.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户管理
 */
@RestController
@RequestMapping("/sys/user")
@Api(tags="用户管理")
public class SysUserController {
	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private SysRoleUserService sysRoleUserService;
	@Autowired
	private SysUserTokenService sysUserTokenService;

	@GetMapping("page")
	@ApiOperation("分页")
	@ApiImplicitParams({
		@ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
		@ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
		@ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
		@ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String") ,
		@ApiImplicitParam(name = "username", value = "用户名", paramType = "query", dataType="String"),
		@ApiImplicitParam(name = "realName", value = "姓名", paramType = "query", dataType="String"),
		@ApiImplicitParam(name = "roleId", value = "角色ID", paramType = "query", dataType="String"),
		@ApiImplicitParam(name = "email", value = "邮箱", paramType = "query", dataType="String"),
		@ApiImplicitParam(name = "mobile", value = "电话", paramType = "query", dataType="String"),
		@ApiImplicitParam(name = "status", value = "启用状态", paramType = "query", dataType="String"),
		@ApiImplicitParam(name = "updateDate", value = "更新时间", paramType = "query", dataType="String")
	})
	//@RequiresPermissions("sys:user:page")
	public Result<PageData<SysUserDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
		PageData<SysUserDTO> page = sysUserService.page(params);
		return new Result<PageData<SysUserDTO>>().ok(page);
	}

	@GetMapping("{id}")
	@ApiOperation("信息")
	//@RequiresPermissions("sys:user:info")
	public Result<SysUserDTO> get(@PathVariable("id") Long id){
		SysUserDTO data = sysUserService.get(id);

		//用户角色列表
		List<Long> roleIdList = sysRoleUserService.getRoleIdList(id);
		data.setRoleIdList(roleIdList);
		return new Result<SysUserDTO>().ok(data);
	}

	@GetMapping("info")
	@ApiOperation("登录用户信息")
	public Result<SysUserDTO> info(){
		UserDetail user = SecurityUser.getUser();
		SysUserDTO data = ConvertUtils.sourceToTarget(user, SysUserDTO.class);
		return new Result<SysUserDTO>().ok(data);
	}

	@LogOperation("log.controller.sys.user.password")
	@PostMapping("/password")
	@ApiOperation("修改密码")
	public Result password(@RequestBody PasswordDTO dto){
		//效验数据
		ValidatorUtils.validateEntity(dto);

		UserDetail user = SecurityUser.getUser();

		//原密码不正确
		if(!PasswordUtils.matches(dto.getPassword(), user.getPassword())){
			return new Result().error(I18nMessageUtils.getMessage("system.account.old.passwd.is.error"));
		}

		sysUserService.updatePassword(user.getId(), dto.getNewPassword());
		return Result.suc();
	}

	@LogOperation("log.controller.sys.user.password.reset")
	@PostMapping("/password/reset")
	@ApiOperation("重置密码")
	public Result resetPassword(@RequestBody ResetPasswordDTO dto) {
		//效验数据
		UserDetail user = SecurityUser.getUser();
//		if(user.getSuperAdmin() == SuperAdminEnum.NO.value()) {
//			return new Result().error(ErrorCode.FORBIDDEN);
//		}

		if(!PasswordUtils.matches(dto.getLoginPassword(), user.getPassword())){
			return new Result().error(I18nMessageUtils.getMessage("system.account.old.passwd.is.error"));
		}

		Optional.ofNullable(dto.getUserIds()).ifPresent(userIds -> userIds.forEach(userId -> {
			sysUserService.updatePassword(userId, dto.getNewPassword());
			sysUserTokenService.logout(userId);
		}));

		return Result.suc();
	}

	@LogOperation("log.controller.sys.user.insert")
	@PostMapping
	@ApiOperation("保存")
	//@RequiresPermissions("sys:user:save")
	public Result<SysUserDTO> save(@RequestBody SysUserDTO dto){
		//效验数据
		ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

		SysUserDTO sysUserDTO = sysUserService.save(dto);

		return get(sysUserDTO.getId());
	}

	@LogOperation("log.controller.sys.user.update")
	@PutMapping
	@ApiOperation("修改")
	//@RequiresPermissions("sys:user:update")
	public Result<SysUserDTO> update(@RequestBody SysUserDTO dto){
		//效验数据
		ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

		sysUserService.update(dto);

		if(dto.getStatus() == UserStatusEnum.DISABLE.value()) {
			sysUserTokenService.logout(dto.getId());
		}

		return get(dto.getId());
	}

	@LogOperation("log.controller.sys.user.delete")
	@DeleteMapping
	@ApiOperation("删除")
	//@RequiresPermissions("sys:user:delete")
	public Result delete(@RequestBody List<Long> ids){
		if(!CollectionUtils.isEmpty(ids)){
			sysUserService.delete(ids.toArray(new Long[ids.size()]));
			ids.forEach(id -> sysUserTokenService.logout(id));
		}
		return new Result();
	}
}