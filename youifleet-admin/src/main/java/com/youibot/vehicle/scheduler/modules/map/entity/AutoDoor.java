package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("auto_door")
public class AutoDoor {

    @TableId(value = "code")
    private String code;

    private String vehicleMapCode;

    private String readFunctionCode;

    private String writeFunctionCode;

    private String ip;

    private Integer port;

    private Integer openAddress;

    private Integer openStatusAddress;

    private Integer closeAddress;

    private Integer closeStatusAddress;

    private String pathCodes ;

    /**
     * 门的状态，门已开:OPEN 门未开:CLOSE 通讯异常:COMMUNICATION_ERROR 操作中:OPERATING 参数异常:PARAM_ERROR
     */
    private String currentStatus;

    /**
     * 最后一次完成关门时间
     */
    private Date lastCloseDoorTime;

    /**
     * 关联的风淋门code
     */
    private String asdCode;
}
