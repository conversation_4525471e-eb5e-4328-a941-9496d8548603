package com.youibot.vehicle.scheduler.modules.map.utils;

import cn.hutool.json.JSONUtil;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;

public class FormatJsonUtils {

    private static final Logger log = LoggerFactory.getLogger(FormatJsonUtils.class);
    //格式化json字符串
    public static String formatJson(String jsonStr) {
        if (null == jsonStr || "".equals(jsonStr)) return "";
        StringBuilder sb = new StringBuilder();
        char last = '\0';
        char current = '\0';
        int indent = 0;
        for (int i = 0; i < jsonStr.length(); i++) {
            last = current;
            current = jsonStr.charAt(i);
            switch (current) {
                case '{':
                case '[':
                    sb.append(current);
                    sb.append('\n');
                    indent++;
                    addIndentBlank(sb, indent);
                    break;
                case '}':
                case ']':
                    sb.append('\n');
                    indent--;
                    addIndentBlank(sb, indent);
                    sb.append(current);
                    break;
                case ',':
                    sb.append(current);
                    if (last != '\\') {
                        sb.append('\n');
                        addIndentBlank(sb, indent);
                    }
                    break;
                default:
                    sb.append(current);
            }
        }
        return sb.toString();
    }

    /**
     * 添加space
     *
     * @param sb
     * @param indent
     * <AUTHOR>
     * @Date 2015-10-14 上午10:38:04
     */
    public static void addIndentBlank(StringBuilder sb, int indent) {
        for (int i = 0; i < indent; i++) {
            sb.append('\t');
        }
    }

    //直接用字符流写入文本了. str表示已经通过上面方法格式化后的字符串
    public static void writeFile(String str, String path) {
        writeFile(str,new File(path));
    }

    //直接用字符流写入文本了. str表示已经通过上面方法格式化后的字符串
    public static void writeFile(String str, File file) {
        String b = JSONUtil.toJsonPrettyStr(str);
        try {
            FileWriter fw = new FileWriter(file);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(b);
            bw.flush();//强制输出下免得 en写入数据不完整
            bw.close();
            fw.close();
        } catch (IOException e) {
            log.error("writeFile",e);
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.DISK_WRITE_ERROR).placeholders(Arrays.asList(file.getPath())).build());
           throw new FleetException(e.getMessage());
        }
    }
}
