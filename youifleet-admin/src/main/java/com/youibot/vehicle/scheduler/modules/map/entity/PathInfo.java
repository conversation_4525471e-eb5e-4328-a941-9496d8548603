package com.youibot.vehicle.scheduler.modules.map.entity;

import com.youibot.vehicle.scheduler.modules.map.dto.PathDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class PathInfo implements Cloneable {

    /**
     * 定位图编码
     */
    private String locatingCode;

    /**
     * 开始点的控制点xy坐标
     */
    private String startControl;

    /**
     * 结束点的控制点xy坐标
     */
    private String endControl;

    /**
     * 长度
     */
    private Double length;

    /**
     * 进入路径角度
     */
    private Double inRadian;

    /**
     * 走出路径角度
     */
    private Double outRadian;

    public PathInfo(PathDTO pathDTO) {
        this.locatingCode = pathDTO.getLocatingCode();
        this.startControl = pathDTO.getStartControl();
        this.endControl = pathDTO.getEndControl();
        this.inRadian = pathDTO.getInRadian();
        this.outRadian = pathDTO.getOutRadian();
    }

    public void init(PathDTO pathDTO) {
        this.locatingCode = pathDTO.getLocatingCode();
        this.startControl = pathDTO.getStartControl();
        this.endControl = pathDTO.getEndControl();
        this.inRadian = pathDTO.getInRadian();
        this.outRadian = pathDTO.getOutRadian();
    }
}
