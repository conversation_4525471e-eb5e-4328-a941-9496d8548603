package com.youibot.vehicle.scheduler.modules.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 任务
 * <AUTHOR>
 */
@Data
@ApiModel(value = "TaskDTO",description = "任务")
public class TaskDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "编号 如T20230202000001")
    private String taskNo;

    @ApiModelProperty(value = "外部编码")
    private String externalTaskNo;

    @ApiModelProperty(value = "任务类型ID")
    private Long taskTypeId;

    @ApiModelProperty(value = "任务类型名称")
    private String name;

    @ApiModelProperty(value = "优先级 最高=5 较高=4 高=3 中=2 低=1, 默认低")
    private Integer priority;

    @ApiModelProperty(value = "机器人")
    private String vehicleCodes;

    @ApiModelProperty(value = "上游回调地址")
    private String callbackUrl;

    @ApiModelProperty(value = "状态 创建=Create 执行中=Running 已完成=Finished 取消=Cancel")
    private String status;

    @ApiModelProperty(value = "创建该任务的来源：外部系统（接口）：Api 手动下发：Manual 充电策略：Charge 泊车策略：Park 交通管制策略：Traffic")
    private String source;

    @ApiModelProperty(value = "事件编码")
    private String eventCode;

    @ApiModelProperty(value = "是否可中断,0:可中断,1:不可中断")
    private Integer isBreak;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否可取消,0:不可取消,1:可取消 默认值1")
    private int isCancel;

    @ApiModelProperty(value = "创建人")
    private Long  creator;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "修改人")
    private Long updater;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;
}
