package com.youibot.vehicle.scheduler.modules.map.enums;

/**
 * 路径类型枚举
 *
 * 0、普通路径 1、二维码对接路径 2、脱离对接路径
 */
public enum PathTypeEnum {

    Common("Common"),
    QR_Down("QR_Down"),
    ShelfLegs("Shelflegs"),
    SymbolV("Symbol_V"),
    Reflector("Reflector"),
    LeaveDocking("LeaveDocking"),
    Pallet("Pallet"),
    ;

    private String value;

    PathTypeEnum(String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }

}
