package com.youibot.vehicle.scheduler.modules.sys.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "SystemConfigEntity", description = "系统配置")
public class SystemConfigEntity {

    @ApiModelProperty(value = "版本号", position = 1)
    private String systemVersion;
    @ApiModelProperty(value = "版权所有", position = 2)
    private String ownCompany;
    @ApiModelProperty(value = "授权信息-公司名称", position = 3)
    private String licenseCompanyName;
    @ApiModelProperty(value = "授权信息-有效期", position = 4)
    private String licenseValidTimeRange;

    @ApiModelProperty(value = "用户操作日志保留时间，单位：天", position = 5)
    private Integer userOptLogExpireTime;
    @ApiModelProperty(value = "系统接口日志保留时间，单位：天", position = 5)
    private Integer interfaceLogExpireTime;
    @ApiModelProperty(value = "系统运行日志保留时间，单位：天", position = 5)
    private Integer runningLogExpireTime;
    @ApiModelProperty(value = "系统告警通知保留时间，单位：天", position = 5)
    private Integer notificationExpireTime;
    @ApiModelProperty(value = "业务的运行数据保留时间，包含任务列表等业务数据，单位：天", position = 5)
    private Integer businessDataExpireTime;
    @ApiModelProperty(value = "统计归档后的报表数据保留时间，单位：天", position = 5)
    private Integer reportDataExpireTime;


    @ApiModelProperty(value = "是否开启点位间距判断 1:开启 0:关闭", position = 6)
    private Integer markerSpacingCheck;
    @ApiModelProperty(value = "点位间距（m）", position = 7)
    private Double markerSpacing;
    @ApiModelProperty(value = "是否开启点位和边间距判断 1:开启 0:关闭", position = 8)
    private Integer markerAndPathSpacingCheck;
    @ApiModelProperty(value = "点位和边间距（m）", position = 9)
    private Double markerAndPathSpacing;

    @ApiModelProperty(value = "遇到障碍物后，调度系统重新规划路径使机器人绕行，枚举：1：开启，0：关闭", position = 21)
    private Integer blockCheckEnable;
    @ApiModelProperty(value = "识别障碍物时间，单位：秒", position = 22)
    private Integer blockCheckInterval;
    @ApiModelProperty(value = "障碍物存在时间，单位：秒", position = 23)
    private Integer removeBlockInterval;
    @ApiModelProperty(value = "前方有故障机器人时的执行策略,枚举：1：等待，2：绕行", position = 24)
    private Integer abnormalVehicleRunPolicy;
    @ApiModelProperty(value = "前方有忙碌机器人时的执行策略,枚举：1：等待，2：绕行", position = 24)
    private Integer workVehicleRunPolicy;
    @ApiModelProperty(value = "前方有空闲机器人时的执行策略,枚举：1：等待，2：绕行，3：驱赶", position = 25)
    private Integer freeVehicleRunPolicy;
    @ApiModelProperty(value = "允许多个冲突机器人去避让的点位类型：1: 导航点,2: 充电点,3: 工作点。多选,按,分割。例如：1,2,3", position = 26)
    private String avoidMarkerTypes;
    @ApiModelProperty(value = "调度系统为机器人分配的最大路径长度，单位：米", position = 27)
    private Double pathApplyLength;
    @ApiModelProperty(value = "自动释放资源 1是 0否", position = 28)
    private Integer autoReleaseResource;
    @ApiModelProperty(value = "断网时间(秒)", position = 29)
    private Integer disconnectionTime;
    @ApiModelProperty(value = "占用资源范围(米)", position = 30)
    private Double occupyResourceRange;
    @ApiModelProperty(value = "通道避让, 启用通道避让后，对向机器人可在通道外主动避让, 枚举：1：开启，0：关闭", position = 30)
    private Integer channelAvoidance;
    @ApiModelProperty(value = "轨道半径, 当机器人到最近的点位或路径距离超过该值时, 系统判定机器人脱轨, 单位: 厘米", position = 30)
    private Double trackRadius;
    @ApiModelProperty(value = "提前呼叫自动门, 单位: 米", position = 30)
    private Double autoDoorAdvanceLength;
    @ApiModelProperty(value = "提前呼叫风淋门, 单位: 米", position = 30)
    private Double showerDoorAdvanceLength;
    @ApiModelProperty(value = "提前呼叫电梯, 单位: 米", position = 30)
    private Double elevatorAdvanceLength;
    @ApiModelProperty(value = "高性能模式, 1：启用，0：禁用", position = 30)
    private Integer highPerformanceMode;
    @ApiModelProperty(value = "空闲时长，单位：秒，当遇到空闲机器人执行策略为驱赶策略时，需要设置空闲时长", position = 30)
    private Integer driveFreeVehicleFreeTime;
    @ApiModelProperty(value = "全场暂停时正在执行机械臂动作的机器人是否立即停止机械臂动作，枚举：Immediately：立即停止，Later：完成后停止", position = 30)
    private String globalPauseExecutingArmScriptIsStop;
    @ApiModelProperty(value = "通知时间，单位：分钟,机器人触发急停，分配/申请不到点位、区城资源达到设定时间时发起通知", position = 30)
    private Integer exceptionNotifyTime;


    @ApiModelProperty(value = "高电量（%）", position = 31)
    private Integer highBattery;
    @ApiModelProperty(value = "低电量（%）", position = 32)
    private Integer lowBattery;
    @ApiModelProperty(value = "充电任务类型ID", position = 33)
    private Long chargeTaskTypeId;
    @ApiModelProperty(value = "自动充电", position = 34)
    private Integer autoCharge;
    @ApiModelProperty(value = "泊车任务类型ID", position = 35)
    private Long parkTaskTypeId;
    @ApiModelProperty(value = "自动泊车", position = 36)
    private Integer autoPark;

    @ApiModelProperty(value = "推送周期", position = 37)
    private Integer pushCycle;
    @ApiModelProperty(value = "机器人状态接口地址", position = 38)
    private String vehicleStatusPushUrl;
    @ApiModelProperty(value = "消息推送地址", position = 39)
    private String noticePushUrl;
    @ApiModelProperty(value = "通知语言类型 1:跟随系统语言,2:自定义", position = 39)
    private Integer noticePushLanguageType;
    @ApiModelProperty(value = "自定义语言的语言类型(zh_CN,en_US,ja_JP等)", position = 39)
    private String noticePushLanguage;

    @ApiModelProperty(value = "监控台机器人状态推送间隔(ms)", position = 40)
    private Long vehicleStatusPushInterval;
    @ApiModelProperty(value = "监控台小铃铛推送间隔(ms)", position = 41)
    private Long noticePushInterval;
    @ApiModelProperty(value = "监控台地图元素状态变更推送间隔(ms)", position = 42)
    private Long mapElementPushInterval;

    @ApiModelProperty(value = "CPU资源占用提醒限制(%)", position = 43)
    private Long cpuOccupyLimit;
    @ApiModelProperty(value = "内存资源占用提醒限制(%)", position = 44)
    private Long memoOccupyLimit;
    @ApiModelProperty(value = "硬盘资源占用提醒限制(%)", position = 45)
    private Long diskOccupyLimit;

    @ApiModelProperty(value = "第三方交管区域的请求地址", position = 43)
    private String thirdSystemTrafficAreaReqUrl;
}
