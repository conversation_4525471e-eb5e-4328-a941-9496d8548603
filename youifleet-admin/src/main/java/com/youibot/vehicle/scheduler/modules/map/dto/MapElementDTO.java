package com.youibot.vehicle.scheduler.modules.map.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "MapElementDTO",description = "地图元素")
@Builder
public class MapElementDTO implements Serializable {

    //此元素包含：点位（编号、名称）、电梯、自动门、风淋门

    @ApiModelProperty(value = "地图编码")
    private String vehicleMapCode;

    @ApiModelProperty(value = "资源类型：电梯Elevator、自动门AutoDoor、风淋门AirShowerDoor、区域MapArea、点位Marker、小车Vehicle")
    private String type;

    @ApiModelProperty(value = "资源编码")
    private String code;

    @ApiModelProperty(value = "资源名称")
    private String name;

    @ApiModelProperty(value = "资源数据")
    private Object data;

    @ApiModelProperty(value = "是否是当前地图：true/false")
    private Boolean isCurrentMap;


    @JsonIgnore
    @ApiModelProperty(value = "匹配类型：1精准匹配 2模糊匹配")
    private Integer matchType;
    @JsonIgnore
    @ApiModelProperty(value = "排序类型：0机器人编码 1电梯编码 2自动门编码 3风淋门编码 4区域编码 5点位名称 6点位编码 ")
    private Integer sortType;
    @JsonIgnore
    @ApiModelProperty(value = "模糊匹配长度")
    private int matchLength;


    public static Integer MATCHTYPE_ACCURATE = 1;//1精准匹配
    public static Integer MATCHTYPE_DIM = 2;//模糊匹配
}
