package com.youibot.vehicle.scheduler.modules.map.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
@ApiModel(value = "MarkerParam", description = "点位扩展参数")
public class MarkerParam implements Cloneable {

    @ApiModelProperty(value = "扩展属性1")
    private String extendParam1;

    @ApiModelProperty(value = "扩展属性2")
    private String extendParam2;

    @ApiModelProperty(value = "扩展属性3")
    private String extendParam3;

    @ApiModelProperty(value = "扩展属性4")
    private String extendParam4;

    @ApiModelProperty(value = "扩展属性5")
    private String extendParam5;

    @ApiModelProperty(value = "扩展属性6")
    private String extendParam6;

    @ApiModelProperty(value = "扩展属性7")
    private String extendParam7;

    @ApiModelProperty(value = "扩展属性8")
    private String extendParam8;

    @ApiModelProperty(value = "扩展属性9")
    private String extendParam9;

    @ApiModelProperty(value = "扩展属性10")
    private String extendParam10;

    @ApiModelProperty(value = "定向角度,可以多个")
    private Double[] fixAngleVector ;
}
