package com.youibot.vehicle.scheduler.modules.map.init;

import com.youibot.vehicle.scheduler.modules.map.webSocket.thread.MapSocketSendThread;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 初始化地图更新，相关线程
 */
@Component
public class MapCommandLineRunner implements CommandLineRunner {

    @Autowired
    private MapSocketSendThread mapSocketSendThread;

    @Override
    public void run(String... args) {
        if (mapSocketSendThread != null) {
            mapSocketSendThread.start();
        }
    }
}