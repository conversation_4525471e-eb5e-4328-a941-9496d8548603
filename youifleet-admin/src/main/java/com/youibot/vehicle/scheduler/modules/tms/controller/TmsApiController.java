package com.youibot.vehicle.scheduler.modules.tms.controller;

import cn.hutool.core.collection.CollUtil;
import com.youibot.vehicle.scheduler.api.aspect.RateLimiter;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.log.dto.SysLogDTO;
import com.youibot.vehicle.scheduler.modules.log.service.SysLogService;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeRecordDTO;
import com.youibot.vehicle.scheduler.modules.notice.service.NoticeRecordService;
import com.youibot.vehicle.scheduler.modules.map.dto.VehicleMapDetailDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.service.MarkerService;
import com.youibot.vehicle.scheduler.modules.map.service.VehicleMapService;
import com.youibot.vehicle.scheduler.modules.statistics.service.VehicleStatisticsService;
import com.youibot.vehicle.scheduler.modules.task.dto.TaskTypeDTO;
import com.youibot.vehicle.scheduler.modules.task.service.TaskTypeService;
import com.youibot.vehicle.scheduler.modules.tms.dto.*;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.VehicleGroupDTO;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.VehicleTypeDTO;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.impl.DefaultVehiclePool;
import com.youibot.vehicle.scheduler.modules.vehicle.service.VehicleGroupService;
import com.youibot.vehicle.scheduler.modules.vehicle.service.VehicleService;
import com.youibot.vehicle.scheduler.modules.vehicle.service.VehicleTypeService;
import com.youibot.vehicle.scheduler.modules.warehouse.dto.WarehouseAreaDTO;
import com.youibot.vehicle.scheduler.modules.warehouse.dto.WarehouseDTO;
import com.youibot.vehicle.scheduler.modules.warehouse.dto.WarehouseTypeDTO;
import com.youibot.vehicle.scheduler.modules.warehouse.service.WarehouseAreaService;
import com.youibot.vehicle.scheduler.modules.warehouse.service.WarehouseService;
import com.youibot.vehicle.scheduler.modules.warehouse.service.WarehouseTypeService;
import com.youibot.vehicle.scheduler.modules.device.dto.ChargeStationDTO;
import com.youibot.vehicle.scheduler.modules.device.service.ChargeStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 暴露给tms相关接口
 */
@Api(tags = "tms")
@RestController
@RequestMapping("/tms")
@Slf4j
public class TmsApiController {

    @Value("${youifleet.system.version}")
    private String systemVersion;

    @Autowired
    private TaskTypeService taskTypeService;
    @Autowired
    private VehicleMapService vehicleMapService;
    @Autowired
    private DefaultVehiclePool defaultVehiclePool;
    @Autowired
    private MarkerService markerService;
    @Autowired
    private VehicleTypeService vehicleTypeService;
    @Autowired
    private VehicleGroupService vehicleGroupService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private WarehouseTypeService warehouseTypeService;
    @Autowired
    private WarehouseAreaService warehouseAreaService;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private VehicleStatisticsService vehicleStatisticsService;
    @Autowired
    private ChargeStationService chargeStationService;

    @Autowired
    private SysLogService sysLogService;
    @Autowired
    private NoticeRecordService noticeRecordService;

    @ApiOperation("查询任务流程")
    @ApiImplicitParam(name = "id", value = "任务类型id", paramType = "query", dataType = "Long")
    @GetMapping("/getTaskTypeInfo")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<TaskTypeForTmsDTO> getTaskTypeInfo(Long id) {
        TaskTypeDTO dto = taskTypeService.info(id);
        TaskTypeForTmsDTO taskTypeForTmsDTO = ConvertUtils.sourceToTarget(dto, TaskTypeForTmsDTO.class);
        return Result.suc(taskTypeForTmsDTO);
    }

    @ApiOperation("查询任务流程列表")
    @GetMapping("/getTaskTypeList")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<TaskTypeForTmsDTO>> getTaskTypeList(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        if (searchMap == null) {
            searchMap = new HashMap<>();
        }
        //只查询启用的
        searchMap.put("publishStatus", "Published");
        List<TaskTypeDTO> dtoList = taskTypeService.list(searchMap);
        //去掉编排数据等不重要字段
        List<TaskTypeForTmsDTO> list = ConvertUtils.sourceToTarget(dtoList, TaskTypeForTmsDTO.class);
        return Result.suc(list);
    }

    @ApiOperation(value = "查询地图列表")
    @GetMapping("/getVehicleMapList")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<VehicleMapDetailDTO>> getVehicleMapList(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) throws Exception {
        if (searchMap == null) {
            searchMap = new HashMap<>();
        }
        searchMap.put("isDraft", "false");
        List<VehicleMapDetailDTO> list = vehicleMapService.searchAll(searchMap);
        //设置每个地图的机器人数量
        Map<String, List<Vehicle>> collect = defaultVehiclePool.getAll().stream().filter(i -> Objects.nonNull(i.getVehicleMapCode())).collect(Collectors.groupingBy(Vehicle::getVehicleMapCode));
        list.forEach(map -> map.setVehicleCount(Optional.ofNullable(collect.get(map.getCode())).map(List::size).orElse(0)));
        return Result.suc(list);
    }

    @ApiOperation(value = "查询点位列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/getMarkerList")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MarkerForTmsDTO>> getMarkerList(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        searchMap.put("isDraft", false);
        List<Marker> markers = markerService.searchAll(searchMap);
        List<MarkerForTmsDTO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(markers)) {
            list = ConvertUtils.sourceToTarget(markers, MarkerForTmsDTO.class);
        }
        return Result.suc(list);
    }

    @ApiOperation(value = "查询机器人类型列表")
    @GetMapping("/getVehicleTypeList")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<VehicleTypeDTO>> getVehicleTypeList() {
        return Result.suc(vehicleTypeService.findAll());
    }

    @ApiOperation(value = "查询机器人分组列表")
    @GetMapping("/getVehicleGroupList")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<VehicleGroupDTO>> getVehicleGroupList(VehicleGroupDTO vehicleGroupDTO) {
        return Result.suc(vehicleGroupService.searchAll(vehicleGroupDTO));
    }

    @ApiOperation(value = "查询库位列表")
    @GetMapping("/getWarehouseList")
    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<WarehouseDTO>> getWarehouseList() {
        return Result.suc(warehouseService.findAll());
    }

    @ApiOperation(value = "查询库位类型列表")
    @GetMapping("/getWarehouseTypeList")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<WarehouseTypeDTO>> getWarehouseTypeList() {
        return Result.suc(warehouseTypeService.findAll());
    }

    @ApiOperation(value = "查询库位区域列表")
    @GetMapping("/getWarehouseAreaList")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<WarehouseAreaDTO>> getWarehouseAreaList() {
        return Result.suc(warehouseAreaService.findAll());
    }

    @ApiOperation(value = "查询系统版本信息")
    @GetMapping("/getSystemVersion")
    //@RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<SystemVersionDTO> getSystemVersion() {
        return Result.suc(SystemVersionDTO.builder().systemVersion(systemVersion).build());
    }

    @ApiOperation(value = "查询系统机器人可用状态（可用列表、不可用列表及原因）")
    @GetMapping("/getVehicleAvaliableState")
//    @RateLimiter(qps = 4 )
    @ResponseStatus(value = HttpStatus.OK)
    public Result<VehicleAvaliableStateDTO> getVehicleAvaliableState() {
        return Result.suc(vehicleService.getVehicleAvaliableState());
    }

    @ApiOperation(value = "返回机器人在某个时间段的统计信息,传入 agvCode,查询单个，否则查询所有")
    @GetMapping("/statistic/getMissionWorkStatistic")
    //@RateLimiter(qps = 1)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvCode", value = "agvCode", required = false, dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd HH:mm:ss", required = false, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd HH:mm:ss", required = false, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "枚举值：NONE、HOUR、DAY、MONTH", required = true, dataType = "String")
    })
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Map<String, List<TaskStatisticsResultDTO>>> getMissionWorkStatistic(@RequestParam(value = "agvCode", required = false) String agvCode,
                                                                                      @RequestParam(value = "startTime", required = false) String startTime,
                                                                                      @RequestParam(value = "endTime", required = false) String endTime,
                                                                                      @RequestParam(value = "type", required = true) String type) {
        TaskStatisticsQueryDTO query = TaskStatisticsQueryDTO.builder().type(type).agvCode(agvCode).startTime(startTime).endTime(endTime).build();
        return Result.suc(vehicleStatisticsService.getMissionWorkStatistic(query));
    }

    @ApiOperation(value = "获取充电桩列表（TMS接口）")
    @GetMapping("/chargeStation/list")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<ChargeStationDTO>> getChargeStationList(@RequestParam(required = false) @ApiIgnore Map<String, Object> params) {
        List<ChargeStationDTO> list = chargeStationService.list(params);
        return Result.suc(list);
    }

    @ApiOperation(value = "获取充电桩详情（TMS接口）")
    @GetMapping("/chargeStation/info")
    @ApiImplicitParam(name = "id", value = "充电桩id", paramType = "query", dataType = "Long")
//    @RateLimiter(qps = 1)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<ChargeStationDTO> getChargeStationInfo(Long id) {
        ChargeStationDTO dto = chargeStationService.info(id);
        return Result.suc(dto);
    }


    @GetMapping("/page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = "createDate", value = "创建时间，开始时间和结束时间用英文逗号分隔，如 2020-02-02 02:02:02,2020-02-02 02:02:02", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "type", value = "类型，运行日志：Running, 告警日志：Warning，错误日志：Error", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "module", value = "功能模块", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "content", value = "描述", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "data", value = "日志数据", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "message", value = "报文", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "vehicleCodes", value = "机器人", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "taskNos", value = "任务", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "nodeCode", value = "节点编码", paramType = "query", dataType="String"),
    })
    public Result<PageData<SysLogDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        try {
            log.info("TMS调用Fleet日志分页查询，参数：{}", params);

            // 处理分页参数 - 使用Fleet系统期望的参数名
            if (!params.containsKey("pageNum")) {
                params.put("pageNum", 1);
            }
            if (!params.containsKey("pageSize")) {
                params.put("pageSize", 50);
            }

            // 同时保留原有的参数名以兼容不同的调用方式
            if (!params.containsKey("page")) {
                params.put("page", params.get("pageNum"));
            }
            if (!params.containsKey("limit")) {
                params.put("limit", params.get("pageSize"));
            }

            PageData<SysLogDTO> page = sysLogService.page(params);

            log.info("TMS日志分页查询成功，返回{}条记录",
                    page.getList() != null ? page.getList().size() : 0);

            return Result.suc(page);
        } catch (Exception e) {
            log.error("TMS日志分页查询失败", e);
            Result<PageData<SysLogDTO>> result = new Result<>();
            return result.error("查询日志失败：" + e.getMessage());
        }
    }

    @GetMapping("/notice/record/page")
    @ApiOperation("通知记录分页查询（TMS专用接口）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = "limit", value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = "code", value = "异常编码", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "description", value = "异常描述", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "level", value = "异常级别(1-普通,2-警告,3-错误)", paramType = "query", dataType="Integer"),
            @ApiImplicitParam(name = "vehicleCode", value = "机器人编码", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "status", value = "状态(1-激活,2-忽略,3-关闭)", paramType = "query", dataType="Integer"),
            @ApiImplicitParam(name = "createDate", value = "创建时间范围，格式：开始时间,结束时间", paramType = "query", dataType="String"),
    })
//    @RateLimiter(qps = 2)
    @ResponseStatus(value = HttpStatus.OK)
    public Result<PageData<NoticeRecordDTO>> getNoticeRecordPage(@ApiIgnore @RequestParam Map<String, Object> params) {
        try {
            log.info("TMS调用Fleet通知记录分页查询，参数：{}", params);

            // 处理分页参数 - 使用Fleet系统期望的参数名
            if (!params.containsKey("pageNum")) {
                params.put("pageNum", 1);
            }
            if (!params.containsKey("pageSize")) {
                params.put("pageSize", 50);
            }

            // 同时保留原有的参数名以兼容不同的调用方式
            if (!params.containsKey("page")) {
                params.put("page", params.get("pageNum"));
            }
            if (!params.containsKey("limit")) {
                params.put("limit", params.get("pageSize"));
            }

            // 处理排序参数，默认按最后推送时间倒序
            if (!params.containsKey(Constant.ORDER_FIELD)) {
                params.put(Constant.ORDER_FIELD, "last_push_time");
            }
            if (!params.containsKey(Constant.ORDER)) {
                params.put(Constant.ORDER, "desc");
            }

            // 调用NoticeRecordService的分页查询方法
            PageData<NoticeRecordDTO> pageData = noticeRecordService.page(params);

            log.info("TMS通知记录分页查询成功，返回{}条记录，共{}页",
                    pageData.getList() != null ? pageData.getList().size() : 0,
                    pageData.getTotal());

            return Result.suc(pageData);

        } catch (Exception e) {
            log.error("TMS通知记录分页查询失败", e);
            Result<PageData<NoticeRecordDTO>> result = new Result<>();
            return result.error("查询通知记录失败：" + e.getMessage());
        }
    }
}
