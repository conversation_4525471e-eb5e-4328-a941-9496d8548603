package com.youibot.vehicle.scheduler.modules.map.handler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.entity.PathParam;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes({PathParam.class})
public class PathParamTypeHandler extends AbstractJsonTypeHandler<PathParam> {

    @Override
    protected PathParam parse(String json) {
        return JSONObject.parseObject(json, PathParam.class);
    }

    @Override
    protected String toJson(PathParam obj) {
        return JSONObject.toJSONString(obj);
    }
}
