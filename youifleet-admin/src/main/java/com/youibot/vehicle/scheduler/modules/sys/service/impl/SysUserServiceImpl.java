package com.youibot.vehicle.scheduler.modules.sys.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.impl.BaseServiceImpl;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.security.password.PasswordUtils;
import com.youibot.vehicle.scheduler.modules.security.service.SysUserTokenService;
import com.youibot.vehicle.scheduler.modules.sys.cache.RealNameCache;
import com.youibot.vehicle.scheduler.modules.sys.dao.SysUserDao;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysUserDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SysUserEntity;
import com.youibot.vehicle.scheduler.modules.sys.enums.SuperAdminEnum;
import com.youibot.vehicle.scheduler.modules.sys.service.SysRoleService;
import com.youibot.vehicle.scheduler.modules.sys.service.SysRoleUserService;
import com.youibot.vehicle.scheduler.modules.sys.service.SysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 系统用户
 */
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {
	@Autowired
	private SysRoleUserService sysRoleUserService;
	@Autowired
	private SysRoleService sysRoleService;
	@Autowired
	private SysUserTokenService sysUserTokenService;

	@Override
	public PageData<SysUserDTO> page(Map<String, Object> params) {
		//转换成like
		paramsToLike(params, "username", "realName", "email", "mobile");

		if (params.get("updateDate") != null) {
			String[] updateDates = String.valueOf(params.get("updateDate")).split(",");
			if (updateDates.length > 0) {
				params.put("startUpdateDate", updateDates[0]);
				if (updateDates.length > 1) {
					params.put("endUpdateDate", updateDates[1]);
				}
			}
		}

		//分页
		IPage<SysUserEntity> page = getPage(params, "update_date", false);

		List<SysUserEntity> list = baseDao.getList(params);
		PageData<SysUserDTO> pageData = getPageData(list, page.getCurrent(), page.getSize(), page.getTotal(), SysUserDTO.class);
		pageData.getList().forEach(u -> {
			List<String> roleNames = sysRoleService.getRoleNamesByUserId(u.getId());
			u.setRoleNames(String.join(",", roleNames));
		});
		return pageData;
	}

	@Override
	public List<SysUserDTO> list(Map<String, Object> params) {
		List<SysUserEntity> list = baseDao.getList(params);
		return ConvertUtils.sourceToTarget(list, SysUserDTO.class);
	}

	@Override
	public SysUserDTO get(Long id) {
		SysUserEntity entity = baseDao.getById(id);
		return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
	}

	@Override
	public SysUserDTO getByUsername(String username) {
		SysUserEntity entity = baseDao.getByUsername(username);
		return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SysUserDTO save(SysUserDTO dto) {
		SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

		//校验用户名是否重复
		SysUserEntity exist = baseDao.getByUsername(dto.getUsername());
		if (Objects.nonNull(exist)) {
            String message = I18nMessageUtils.getMessage("system.account.is.already.exists");
            throw new FleetException(message);
		}

		//密码加密
		String password = PasswordUtils.encode(entity.getPassword());
		entity.setPassword(password);

		entity.setSuperAdmin(SuperAdminEnum.NO.value());
		insert(entity);

		//保存角色用户关系
		sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());
		return get(entity.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void update(SysUserDTO dto) {

		//校验用户名是否重复
		SysUserEntity exist = baseDao.getByUsername(dto.getUsername());
		if (Objects.nonNull(exist) && !Objects.equals(dto.getId(), exist.getId())) {
            String message = I18nMessageUtils.getMessage("system.account.is.already.exists");
            throw new FleetException(message);
		}

		exist.setRealName(dto.getRealName());
		exist.setEmail(dto.getEmail());
		exist.setMobile(dto.getMobile());
		exist.setAutoLogoutTime(dto.getAutoLogoutTime());
		exist.setStatus(dto.getStatus());

		//更新用户
		updateById(exist);

		//更新角色用户关系
		sysRoleUserService.saveOrUpdate(dto.getId(), dto.getRoleIdList());

		RealNameCache.remove(dto.getId());
	}

	@Override
	public void delete(Long[] ids) {
		//删除用户
		baseDao.deleteBatchIds(Arrays.asList(ids));

		//删除角色用户关系
		sysRoleUserService.deleteByUserIds(ids);

		sysUserTokenService.deleteByUserIds(ids);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updatePassword(Long id, String newPassword) {
		newPassword = PasswordUtils.encode(newPassword);

		baseDao.updatePassword(id, newPassword);
	}

	@Override
	public List<String> realNamesByRoleId(Long roleId) {
		return baseDao.realNamesByRoleId(roleId);
	}

    @Override
    public String realNameByUserId(Long userId) {
        String realName = RealNameCache.get(userId);
        if (StringUtils.isBlank(realName)) {
            SysUserEntity entity = this.selectById(userId);
            realName = "";
            if (entity != null) {
                realName = entity.getRealName();
            }
            RealNameCache.put(userId, realName);
        }
        return RealNameCache.get(userId);
    }

	@Override
	public List<Long> getUserIdsByRealName(String realName) {
		return baseDao.getUserIdsByRealName("%" + realName + "%");
	}

    @Override
    public void setWrapperCreatorName(QueryWrapper queryWrapper, Map<String, Object> params) {
        Object o = params.get("creatorName");
        if (o != null) {
            String createName = o.toString();
            List<Long> userIds = this.getUserIdsByRealName(createName);
            if (CollUtil.isNotEmpty(userIds)) {
                queryWrapper.in("creator", userIds);
            } else {
                queryWrapper.like("creator", userIds);
            }
        }
    }
}
