package com.youibot.vehicle.scheduler.modules.map.webSocket.thread;

import com.youibot.vehicle.scheduler.common.utils.AGVPropertiesUtils;
import com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import com.youibot.vehicle.scheduler.modules.map.webSocket.module.SocketMessageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;


/**
 * 地图元素，做增删改操作时，加锁获取编号，同时将ws消息存储到容器中
 * 当前线程，实时检测，无消息跳过，有消息则按顺序将ws消息发送到前端
 *
 * 前端按顺序存储接收到的的消息，按顺序渲染页面
 *
 * 当前线程的目的：
 *       保证ws消息顺序性，以防关联的两个并发请求1、2 ，对应的ws消息 2、1先到，接口请求后到，ws消息把页面修改后，接口把页面还原，导致不一致
 */
@Component
public class MapSocketSendThread extends Thread{

    private static final Logger LOGGER = LoggerFactory.getLogger(MapSocketSendThread.class);

    //key:value  ->  地图编号：地图对应的ws消息
    public static final Map<String, ConcurrentLinkedQueue<SocketMessageModel>> messagePool = new ConcurrentHashMap<>();


    @Override
    public void run() {
        Thread.currentThread().setName("MapSocketSendThread");
        Long time_interval = AGVPropertiesUtils.getLong("VEHICLE_MAP.WS_MSG_SEND_INTERVAL", 100L);

        while (true) {
            try {
                //此处用并发流，目的：多张地图可同时处理，避免多个地图串行执行，提高效率
                messagePool.entrySet().forEach(this::handleWsMsgSend);
            } catch (Exception e) {
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.SYSTEM_INTERNAL_ERROR).build());
                LOGGER.error("MapSocketSendThread error : {}", e);
            }

            //2、睡眠 100毫秒 后继续
            try {
                TimeUnit.MILLISECONDS.sleep(time_interval);
            } catch (Exception e) {
                LOGGER.error("sleep error : {}", e);
            }
        }
    }

    /**
     * 处理实际的对应地图的消息发送
     */
    private void handleWsMsgSend(Map.Entry<String, ConcurrentLinkedQueue<SocketMessageModel>> item){
        List<String> activeMap = MapUpdateSocketController.getActiveMap();
        String mapCode = item.getKey();
        if(CollectionUtils.isEmpty(activeMap) || !activeMap.contains(mapCode)){
            messagePool.remove(mapCode);
            return;
        }

        ConcurrentLinkedQueue<SocketMessageModel> messages = item.getValue();
        if(messages.isEmpty()){
            return;
        }

        //发送一个，删除一个，此时其他线程正在往里面添加数据，不能直接清空
        while(!CollectionUtils.isEmpty(messages)){
            SocketMessageModel msg = messages.poll();
            MapUpdateSocketController.actualSendMessage(mapCode, msg);
        }
    }
}
