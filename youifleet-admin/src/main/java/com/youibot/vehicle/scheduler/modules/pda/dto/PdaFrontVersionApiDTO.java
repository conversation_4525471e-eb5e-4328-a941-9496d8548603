package com.youibot.vehicle.scheduler.modules.pda.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(value = "PdaFrontVersionApiDTO",description = "PDA前端最新版本")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PdaFrontVersionApiDTO {

    @ApiModelProperty(value = "最新的地址")
    private String url;

    @ApiModelProperty(value = "版本号", position = 1)
    private String version;

}
