package com.youibot.vehicle.scheduler.modules.map.service;

import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.MarkerScope;
import com.youibot.vehicle.scheduler.modules.map.dto.MarkerDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.MarkerWithWarehouseDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;

import java.util.List;
import java.util.Map;

public interface MarkerService {

    List<Marker> searchAll(Map<String, Object> searchMap);

    List<MarkerDTO> searchAllWithLocatingCode(Map<String, Object> searchMap);

    List<MarkerWithWarehouseDTO> searchAllWithWarehouse(Map<String, Object> searchMap);

    PageData<Marker> page(Map<String, Object> searchMap);

    List<Marker> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft);

    List<Marker> selectByVehicleMapCodes(List<String> vehicleMapCodes, boolean isDraft);

    Marker selectByCode(String vehicleMapCode, String code, boolean isDraft);

    Marker selectByCode(String code);

    List<Marker> selectByName(String name);

    List<Marker> selectByCodes(String vehicleMapCode, List<String> codes, boolean isDraft);

    List<MarkerDTO> selectByCodesWithLocatingCode(String vehicleMapCode, String locatingCode, List<String> codes, boolean isDraft);

    /**
     * 根据标记点类型查询与当前标记点有路径的所有点位列表
     */
    List<Marker> selectRelatePathMarkerIdsByType(String markerCode, String vehicleMapCode, String type, boolean isDraft);

    /**
     * 查询所有启用的充电点。
     * 非草稿数据
     *
     * @return
     */
    List<Marker> selectEnableChargeMarker();

    /**
     * 查询所有可分配的泊车点。
     * 过滤掉禁用的，
     * 已分配的泊车点。
     * 非草稿数据
     *
     * @return
     */
    List<Marker> selectEnableParkMarkers();

    MarkerDTO insert(MarkerDTO marker);

    MarkerDTO update(MarkerDTO marker);

    List<MarkerDTO> batchUpdate(List<MarkerDTO> markers);

    List<Marker> getAllWithOutCondition();

    void checkBindElevator(List<Marker> markers);

    boolean checkBindElevator(String markerCode);

    void deleteByCode(String vehicleMapCode, String code);

    void deleteByVehicleMapCode(String vehicleMapCode);

    /**
     * 根据地图id列表删除所有的标记点
     */
    void deleteByVehicleMapCodes(List<String> vehicleMapCodeList);

    List<MarkerScope> selectMarkersByTypeAndScope(String vehicleMapCode, String agvCode, String type);

    /**
     * 修改点位时进行间距判断
     * @param mapCode 地图编号
     * @param ms 修改的点位
     */
    void checkMarkerSpacingByUpdate(String mapCode, String locatingCode, List<MarkerDTO> ms);

    /**
     * 新增点位间距判断
     * @param markerDTO
     * @param existList
     */
    void checkMarkerSpacing(MarkerDTO markerDTO, List<Marker> existList);

    void deleteDraftByVehicleMapCode(String mapCode);

    /**
     * 录制点位
     */
    MarkerDTO transcribe(MarkerDTO marker);

    List<Marker> getChargeMarkerList(String vehicleMapCode);

    List<Marker> getParkMarkerList(String vehicleMapCode);
}
