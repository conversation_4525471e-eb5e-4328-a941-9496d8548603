package com.youibot.vehicle.scheduler.modules.map.handler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.entity.PathExtendParam;
import org.apache.ibatis.type.MappedTypes;
import java.util.List;

@MappedTypes({List.class,PathExtendParam.class})
public class ListPathExtendParamTypeHandler extends AbstractJsonTypeHandler<List<PathExtendParam>> {

    @Override
    protected List<PathExtendParam> parse(String json) {
        return JSONObject.parseArray(json, PathExtendParam.class);
    }

    @Override
    protected String toJson(List<PathExtendParam> list) {
        return JSONObject.toJSONString(list);
    }
}
