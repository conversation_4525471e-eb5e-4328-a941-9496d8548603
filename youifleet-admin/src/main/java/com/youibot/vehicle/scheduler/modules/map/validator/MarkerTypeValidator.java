package com.youibot.vehicle.scheduler.modules.map.validator;

import com.youibot.vehicle.scheduler.modules.map.enums.MarkerTypeEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

public class MarkerTypeValidator implements ConstraintValidator<MarkerType,CharSequence> {

    @Override
    public boolean isValid(CharSequence value, ConstraintValidatorContext constraintValidatorContext) {
        Set<String> types = Arrays.stream(MarkerTypeEnum.values()).map(item -> item.value()).collect(Collectors.toSet());
        return types.contains(value);
    }
}
