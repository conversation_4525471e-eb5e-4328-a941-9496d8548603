package com.youibot.vehicle.scheduler.modules.map.validator;

import com.youibot.vehicle.scheduler.modules.map.enums.PathTypeEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

public class PathTypeValidator implements ConstraintValidator<PathType,String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        Set<String> types = Arrays.stream(PathTypeEnum.values()).map(PathTypeEnum::value).collect(Collectors.toSet());
        return types.contains(value);
    }
}
