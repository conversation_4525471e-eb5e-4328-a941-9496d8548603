package com.youibot.vehicle.scheduler.modules.map.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import lombok.Data;
import java.util.Collection;
import java.util.Set;

public class ResourceLock {

    @Data
    public class InnerResource {
        private Object lock = new Object();
        private Set<String> resource = new ConcurrentHashSet<>();
    }

    private final InnerResource resource = new InnerResource();

    public boolean applyLock(Collection<String> resources) {
        synchronized (resource.getLock()) {
            if (CollUtil.isEmpty(resources)) {
                return false;
            }
            Collection<String> intersection = CollUtil.intersection(resources, resource.getResource());
            if (CollUtil.isNotEmpty(intersection)) {
                return false;
            }
            resource.getResource().addAll(resources);
            return true;
        }
    }

    public void release(Collection<String> resources) {
        synchronized (resource.getLock()) {
            if (CollUtil.isEmpty(resources)) {
                return;
            }
            resource.getResource().removeIf(resources::contains);
        }
    }

}
