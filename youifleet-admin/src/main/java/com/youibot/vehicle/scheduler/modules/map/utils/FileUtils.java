package com.youibot.vehicle.scheduler.modules.map.utils;

import cn.hutool.json.JSONUtil;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.Pair;
import com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static java.lang.Runtime.getRuntime;

public class FileUtils {

    private static Logger logger = LoggerFactory.getLogger(FileUtils.class);


    public static String readFileData(String path) {
        byte[] byteArray = getByteArray(path);
        if (byteArray == null) {
            return null;
        }
        return new String(byteArray);
    }

    public static byte[] getByteArray(String path) {
        if (StringUtils.isBlank(path)) {
            return null;
        }

        FileInputStream fis = null;
        try {
            fis = new FileInputStream(path);
            return getByteArray(fis);
        } catch (Exception e) {
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.DISK_READ_ERROR).placeholders(Arrays.asList(path)).build());
            return null;
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static byte[] getByteArray(InputStream inputStream) throws Exception {
        ByteArrayOutputStream outputStream = getOutputStream(inputStream);
        if (outputStream == null) {
            return null;
        }
        return outputStream.toByteArray();
    }

    public static ByteArrayOutputStream getOutputStream(InputStream inputStream) throws Exception {
        if (inputStream == null) {
            return null;
        }
        ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream();
        int len = 0;
        byte[] buffer = new byte[1024];
        while ((len = inputStream.read(buffer)) != -1) {
            byteOutputStream.write(buffer, 0, len);
        }
        return byteOutputStream;
    }


    public static void deleteFile(String path) {
        deleteFile(new File(path));
    }

    /**
     * 删除
     *
     * @param file
     */
    public static void deleteFile(File file) {
        if (file.exists()) {// 判断文件是否存在
            if (file.isFile()) {// 判断是否是文件
                file.delete();// 删除文件
            } else if (file.isDirectory()) {// 否则如果它是一个目录
                File[] files = file.listFiles();// 声明目录下所有的文件 files[];
                if (files != null) {
                    for (File value : files) {// 遍历目录下所有的文件
                        deleteFile(value);// 把每个文件用这个方法进行迭代
                    }
                }
                file.delete();// 删除文件夹
            }
        }
    }

    /**
     * 复制文件或文件夹到指定目录
     *
     * @param oldFilePath 旧文件目录
     * @param newFilePath 新目录
     * @throws IOException
     */
    public static void copyFolder(String oldFilePath, String newFilePath) throws IOException {
        try {
            //获取文件源
            File oldFile = new File(oldFilePath);
            //获取目标地址
            File newFile = new File(newFilePath);
            //目标地址不存在就创建
            if (!newFile.exists()) {
                newFile.mkdirs();
            }
            // 如果要复制的文件是文件夹就在目标地址下创建一个文件夹
            if (oldFile.isDirectory()) {
                File copyFolder = new File(newFilePath + File.separator + oldFile.getName());
                if (!copyFolder.exists()) {
                    copyFolder.mkdirs();
                }
                //获取文件中所有的文件
                File[] copyFiles = oldFile.listFiles();
                if (copyFiles == null || copyFiles.length == 0) {
                    return;
                }
                for (File file : copyFiles) {
                    //如果文件名称是我们创建的目录就跳过
                    copyFolder(file.getPath(), copyFolder.getPath());
                }
            } else if (oldFile.isFile()) {
                //如果是文件，就在目录下面创建一个和源文件相同名称的文件，并吧源文件内容写入到文件中
                FileInputStream ins = new FileInputStream(oldFile);
                File copyFile = new File(newFile.getPath() + File.separator + oldFile.getName());
                if (!copyFile.exists()) {
                    copyFile.createNewFile();
                }
                OutputStream out = new FileOutputStream(copyFile);
                int len;
                byte[] buff = new byte[1024];
                while ((len = ins.read(buff)) != -1) {
                    out.write(buff, 0, len);
                }
                ins.close();
                out.flush();
                out.close();

            }
        }catch (IOException e){
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.DISK_WRITE_ERROR).placeholders(Arrays.asList(newFilePath)).build());
            throw e;
        }
    }

    /**
     * 复制文件或文件夹到指定目录
     *
     * @param oldFilePath 旧文件目录
     * @param newFilePath 新目录
     * @throws IOException
     */
    public static void copyFolderContent(String oldFilePath, boolean first, String newFilePath) throws IOException {
        try {
            //获取文件源
            File oldFile = new File(oldFilePath);
            //获取目标地址
            File newFile = new File(newFilePath);
            //目标地址不存在就创建
            if (!newFile.exists()) {
                newFile.mkdirs();
            }
            // 如果要复制的文件是文件夹就在目标地址下创建一个文件夹
            if (oldFile.isDirectory()) {
                File copyFolder = null;
                if(first){
                    copyFolder = newFile;
                }else {
                    copyFolder = new File(newFilePath + File.separator + oldFile.getName());
                    if (!copyFolder.exists()) {
                        copyFolder.mkdirs();
                    }
                }
                //获取文件中所有的文件
                File[] copyFiles = oldFile.listFiles();
                if (copyFiles == null || copyFiles.length == 0) {
                    return;
                }
                for (File file : copyFiles) {
                    //如果文件名称是我们创建的目录就跳过
                    copyFolderContent(file.getPath(), false, copyFolder.getPath());
                }
            } else if (oldFile.isFile()) {
                //如果是文件，就在目录下面创建一个和源文件相同名称的文件，并吧源文件内容写入到文件中
                FileInputStream ins = new FileInputStream(oldFile);
                File copyFile = new File(newFile.getPath() + File.separator + oldFile.getName());
                if (!copyFile.exists()) {
                    copyFile.createNewFile();
                }
                OutputStream out = new FileOutputStream(copyFile);
                int len;
                byte[] buff = new byte[1024];
                while ((len = ins.read(buff)) != -1) {
                    out.write(buff, 0, len);
                }
                ins.close();
                out.flush();
                out.close();

            }
        }catch (IOException e){
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.DISK_WRITE_ERROR).placeholders(Arrays.asList(newFilePath)).build());
            throw e;
        }
    }

    /**
     * 复制文件或文件夹到指定目录
     *
     * @param oldFilePath 旧文件目录
     * @param newFilePath 新目录
     * @param oldMapCode 地图编码
     * @param newMapCode 地图编码
     * @throws IOException
     */
    public static void copyFolderAndChangeName(String oldFilePath, String newFilePath, String oldMapCode, String newMapCode) throws IOException {
        try {
            //获取文件源
            File oldFile = new File(oldFilePath);
            //获取目标地址
            File newFile = new File(newFilePath);
            //目标地址不存在就创建
            if (!newFile.exists()) {
                newFile.mkdirs();
            }
            // 如果要复制的文件是文件夹就在目标地址下创建一个文件夹
            if (oldFile.isDirectory()) {
                File copyFolder = new File(newFilePath + File.separator + newMapCode + File.separator);
                if (!copyFolder.exists()) {
                    copyFolder.mkdirs();
                }
                //获取文件中所有的文件
                File[] copyFiles = oldFile.listFiles();
                if (copyFiles == null || copyFiles.length == 0) {
                    return;
                }
                for (File file : copyFiles) {
                    //如果文件名称是我们创建的目录就跳过
                    copyFolderAndChangeName(file.getPath(), copyFolder.getPath(), oldMapCode, newMapCode);
                }
            } else if (oldFile.isFile()) {
                //如果是文件，就在目录下面创建一个和源文件相同名称的文件，并吧源文件内容写入到文件中
                FileInputStream ins = new FileInputStream(oldFile);
                File copyFile = new File(newFile.getPath() + File.separator + oldFile.getName().replace(oldMapCode, newMapCode));
                if (!copyFile.exists()) {
                    copyFile.createNewFile();
                }
                OutputStream out = new FileOutputStream(copyFile);
                int len;
                byte[] buff = new byte[1024];
                while ((len = ins.read(buff)) != -1) {
                    out.write(buff, 0, len);
                }
                ins.close();
                out.flush();
                out.close();

            }
        }catch (IOException e){
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.DISK_WRITE_ERROR).placeholders(Arrays.asList(newFilePath)).build());
            throw e;
        }
    }

    /**
     * 将数据写入响应流
     */
    public static void writeBytesToResponse(HttpServletResponse response, byte[] bytes, String fileName) {
        if (bytes == null) {
            bytes = new byte[0];
        }
        OutputStream os = null;
        try {
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.addHeader("Content-Length", "" + bytes.length);
            os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            os.write(bytes);// 输出文件
            os.flush();
            os.close();
        } catch (Exception e) {
            logger.error("writeBytesToResponse ", e);
            throw new FleetException("");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    logger.error("", e);
                    throw new FleetException("");
                }
            }
        }
    }

    public static ByteArrayOutputStream getOutputStream(ZipInputStream inputStream) throws Exception {
        byte[] data = new byte[1024];
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        int len = 0;
        while ((len = inputStream.read(data)) != -1) {
            outputStream.write(data, 0, len);
        }
        return outputStream;
    }

    /**
     * 创建本地目录
     * @param path
     */
    public static void createLocalDir(String path) {
        if (StringUtils.isEmpty(path)) {
            return;
        }
        if (path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }
        File file = new File(path);
        if (file.exists()) {
            return;
        }
        int index = path.lastIndexOf("/");
        if (index < 0) {
            return;
        }

        String parentPath = path.substring(0, index);
        if (!new File(parentPath).exists()) {
            createLocalDir(parentPath);
        }
        file.mkdirs();
    }

    /**
     * 压缩文件写入指定文件夹
     */
    public static void writeZipDataToLocal(String path, InputStream inputStream) {
        ZipInputStream zip = null;
        try {
            zip = new ZipInputStream(new BufferedInputStream(inputStream));
            ZipEntry zipEntry = null;
            while ((zipEntry = zip.getNextEntry()) != null) {
                String localDir = path + zipEntry.getName();
                File file = new File(localDir);
                if (!zipEntry.isDirectory()) {
                    // 读取Entry对象
                    File fileParentFile = file.getParentFile();
                    if (!fileParentFile.exists()) {
                        fileParentFile.mkdirs();
                    }
                    BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(file));
                    byte[] bytes = new byte[1024];
                    int len;
                    while ((len = zip.read(bytes)) != -1) {
                        bos.write(bytes, 0, len);
                    }
                    bos.close();
                } else {
                    file.mkdirs();
                }
            }
        } catch (Exception e) {
            logger.error("导入文件失败{}", e);
        } finally {
            try {
                if (zip != null) {
                    zip.close();
                }
            } catch (Exception e) {
                logger.error("导入文件失败，关闭流出错{}", e);
            }
        }
    }


    /**
     * 从压缩包中获取指定的文件
     *
     * @param inputStream
     */
    public static Map<String, HashMap<String, ByteArrayOutputStream>> getFileFromZipFile(ZipInputStream inputStream) throws Exception {
        if (inputStream == null) {
            throw new RuntimeException("param is null ");
        }
        Map<String, HashMap<String, ByteArrayOutputStream>> result = new HashMap<>();
        ZipEntry zipEntry = null;
        boolean flag = false;//如果 == true 表明不是只有一级目录
        String prefix = "";
        while ((zipEntry = inputStream.getNextEntry()) != null) {
            if (zipEntry.isDirectory()) {
                flag = true;
                prefix = zipEntry.getName();
            } else {
                if (!flag) {
                    int indexOf = zipEntry.getName().lastIndexOf("/");
                    if (indexOf >= 0) {
                        prefix = zipEntry.getName().substring(0, indexOf + 1);
                    }
                }
                ByteArrayOutputStream outputStream = FileUtils.getOutputStream(inputStream);
                inputStream.closeEntry();
                HashMap<String, ByteArrayOutputStream> byteOutputStreams = result.get(prefix);
                if (byteOutputStreams == null) {
                    byteOutputStreams = new HashMap<>();
                }
                byteOutputStreams.put(zipEntry.getName(), outputStream);

                result.put(prefix, byteOutputStreams);
            }
        }
        return result;
    }


    public static boolean fileEmpowerment(String filePath) {
        //只有linux 系统才会执行授权
        String systemName = System.getProperty("os.name");
        if (org.apache.commons.lang3.StringUtils.isBlank(systemName) || !systemName.toLowerCase().contains("linux")) {
            return true;
        }

        Runtime runtime = getRuntime();
        String command = "chmod 777 -R " + filePath;
        try {
            Process process = runtime.exec(command);
            process.waitFor();
            int existValue = process.exitValue();
            if (existValue != 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            throw new RuntimeException("Command execute failed." + e);
        }
    }

    /**
     * 写值
     *
     * @param path
     * @return
     * @throws IOException
     */
    public static void writeValue(String data, String path) throws IOException {
        File file = new File(path);
        File fileParent = file.getParentFile();
        if (!fileParent.exists()) {
            fileParent.mkdirs();

        }
        if(!file.exists()){
            file.createNewFile();
        }
        data = JSONUtil.toJsonPrettyStr(data);
        writeFile(data, file);// 写入到文件当中
    }

    //直接用字符流写入文本了. str表示已经通过上面方法格式化后的字符串
    public static void writeFile(String str, File file) {
        try {
            FileWriter fw = new FileWriter(file);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(str);
            bw.flush();//强制输出下免得 en写入数据不完整
            bw.close();
            fw.close();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 将字节写入文件
     * @param data
     * @param filePath
     */
    public static void writeFile(byte[] data, String filePath) {
        try {
            FileOutputStream fw = new FileOutputStream(filePath);
            fw.write(data);
            fw.flush();
            fw.close();
        } catch (IOException e) {
            logger.error("写入文件失败，文件路径：{}，e：", filePath, e);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取磁盘空间大小：first: 总容量, second: 已使用容量
     * @return
     */
    public static Pair<Long, Long> getDiskSpace() {
        long totalDisk = 0;
        long usedDisk = 0;
        File[] files = File.listRoots();
        for (File file : files) {
            totalDisk += file.getTotalSpace();
            usedDisk += file.getTotalSpace() - file.getUsableSpace();
        }
        return new Pair<>(totalDisk, usedDisk);
    }
}