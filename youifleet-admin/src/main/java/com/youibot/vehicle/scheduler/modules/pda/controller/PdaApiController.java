package com.youibot.vehicle.scheduler.modules.pda.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.vehicle.scheduler.api.dto.TaskDetailApiDTO;
import com.youibot.vehicle.scheduler.api.dto.TaskExecuteApiDTO;
import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import com.youibot.vehicle.scheduler.modules.pda.dto.PDAMaterialTypeDTO;
import com.youibot.vehicle.scheduler.modules.pda.dto.PDAWarehouseDTO;
import com.youibot.vehicle.scheduler.modules.pda.dto.PdaFrontVersionApiDTO;
import com.youibot.vehicle.scheduler.modules.pda.dto.VehicleDetailPdaDTO;
import com.youibot.vehicle.scheduler.modules.sys.dto.SystemPropertyDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemPropertyEntity;
import com.youibot.vehicle.scheduler.modules.sys.service.SystemPropertyService;
import com.youibot.vehicle.scheduler.modules.task.constant.TaskConstant;
import com.youibot.vehicle.scheduler.modules.task.dto.RunningTaskDTO;
import com.youibot.vehicle.scheduler.modules.task.entity.TaskEntity;
import com.youibot.vehicle.scheduler.modules.task.service.TaskService;
import com.youibot.vehicle.scheduler.modules.vehicle.service.VehicleService;
import com.youibot.vehicle.scheduler.modules.warehouse.service.WarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * pda相关接口
 */
@Api(tags = "pda")
@RestController
@RequestMapping("/pda")
@Slf4j
public class PdaApiController {

    @Autowired
    private SystemPropertyService systemPropertyService;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private WarehouseService warehouseService;

    @ApiOperation(value = "(PDA)查询最新版本")
    @GetMapping("/getLatestVersion")
    public Result<PdaFrontVersionApiDTO> getLatestVersion() {
        SystemPropertyEntity query = new SystemPropertyEntity();
        query.setType("user");
        query.setCategory("pda");
        query.setPropertyKey("pdaVersion");
        List<SystemPropertyEntity> list = systemPropertyService.selectList(query);
        if (CollUtil.isEmpty(list)) {
            throw new FleetException("系统关于PDA的版本配置缺失");
        }

        SystemPropertyEntity systemPropertyEntity = list.get(0);
        String propertyValue = systemPropertyEntity.getPropertyValue();
        if (StringUtils.isEmpty(propertyValue)) {
            throw new FleetException("系统关于PDA的版本配置缺失");
        }
        JSONObject object = JSON.parseObject(propertyValue);
        PdaFrontVersionApiDTO version = PdaFrontVersionApiDTO.builder().url(object.getString("url")).version(object.getString("version")).build();
        return Result.suc(version);
    }

    @LogOperation("log.controller.pda.config")
    @ApiOperation(value = "(PDA)配置版本信息")
    @PostMapping("/config")
    public Result config(@RequestBody PdaFrontVersionApiDTO pdaParamDTO) {
        String propertyValue = JSON.toJSONString(pdaParamDTO);

        SystemPropertyEntity query = new SystemPropertyEntity();
        query.setType("user");
        query.setCategory("pda");
        query.setPropertyKey("pdaVersion");
        List<SystemPropertyEntity> list = systemPropertyService.selectList(query);
        SystemPropertyEntity pdaConfig;
        if (CollUtil.isNotEmpty(list)) {
            pdaConfig = list.get(0);
            pdaConfig.setPropertyValue(propertyValue);
            systemPropertyService.updateById(ConvertUtils.sourceToTarget(pdaConfig, SystemPropertyDTO.class));
        }else{
            pdaConfig = new SystemPropertyEntity();
            pdaConfig.setType("user");
            pdaConfig.setCategory("pda");
            pdaConfig.setPropertyKey("pdaVersion");
            pdaConfig.setValueType("Json");
            pdaConfig.setTitle("pda最新版本");
            pdaConfig.setRemark("pda最新版本");
            pdaConfig.setPropertyValue(propertyValue);
            systemPropertyService.insert(ConvertUtils.sourceToTarget(pdaConfig, SystemPropertyDTO.class));
        }
        return Result.suc();
    }

    @ApiOperation(value = "(PDA)分页获取所有机器人的详情数据列表")
    @GetMapping(value = "/vehiclePage")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "vehicleCode", value = "机器人编号", paramType = "query", dataType = "String")
    })
    @ResponseStatus(value = HttpStatus.OK)
    public Result<PageData<VehicleDetailPdaDTO>> vehiclePage(@ApiIgnore @RequestParam Map<String, Object> params) {
        return Result.suc(vehicleService.vehiclePage(params));
    }

    @ApiOperation(value = "(PDA)获取物料类型列表")
    @GetMapping(value = "/getMaterialTypeList")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<PDAMaterialTypeDTO>> getMaterialTypeList() {
        return Result.suc(warehouseService.getMaterialTypeList());
    }

    @LogOperation("log.controller.pda.containerEnter")
    @ApiOperation(value = "(PDA)容器进场")
    @PutMapping("/pdaContainerEnter")
    public Result pdaContainerEnter(@RequestBody @Validated(UpdateGroup.class) PDAWarehouseDTO dto) {
        warehouseService.pdaContainerEnter(dto);
        return Result.suc();
    }

    @LogOperation("log.controller.pda.containerExit")
    @ApiOperation(value = "(PDA)容器出场")
    @PutMapping("/pdaContainerExit")
    public Result pdaContainerExit(@RequestBody @Validated(UpdateGroup.class) PDAWarehouseDTO dto) {
        warehouseService.pdaContainerExit(dto);
        return Result.suc();
    }

    @LogOperation("log.controller.pda.execute")
    @ApiOperation(value = "(PDA)创建任务")
    @PostMapping("/execute")
    @ApiImplicitParam(name = "executeDTO", value = "创建任务", paramType = "body", required = true, dataType = "TaskExecuteApiDTO")
    public Result<TaskDetailApiDTO> execute(@RequestBody TaskExecuteApiDTO executeDTO) {
        TaskEntity taskEntity = taskService.executeByBusinessSystem(executeDTO, TaskConstant.SOURCE_PDA);
        TaskDetailApiDTO taskDetailDTO = this.getTaskDetailDTO(taskEntity);
        return Result.suc(taskDetailDTO);
    }

    @ApiOperation(value = "(PDA)任务列表")
    @GetMapping("/runningList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "taskName", value = "任务名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "status", value = "状态 Normal:正常 Abnormal:异常", paramType = "query", dataType = "String")
    })
    public Result<PageData<RunningTaskDTO>> runningList(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        PageData<RunningTaskDTO> page = taskService.pdaRunningList(searchMap);
        return Result.suc(page);
    }

    private TaskDetailApiDTO getTaskDetailDTO(TaskEntity taskEntity) {
        return TaskDetailApiDTO.builder()
                .taskTypeCode(taskEntity.getTaskTypeCode())
                .taskNo(taskEntity.getTaskNo())
                .externalTaskNo(taskEntity.getExternalTaskNo())
                .name(taskEntity.getName())
                .status(taskEntity.getStatus())
                .priority(taskEntity.getPriority())
                .callbackUrl(taskEntity.getCallbackUrl())
                .paramIn(JSONObject.parseObject(taskEntity.getParamIn()))
                .paramOut(JSONObject.parseObject(taskEntity.getParamOut()))
                .startTime(taskEntity.getStartTime())
                .endTime(taskEntity.getEndTime())
                .build();
    }



}
