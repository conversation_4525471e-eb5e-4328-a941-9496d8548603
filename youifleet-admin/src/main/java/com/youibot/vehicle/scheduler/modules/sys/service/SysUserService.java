package com.youibot.vehicle.scheduler.modules.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.BaseService;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysUserDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SysUserEntity;

import java.util.List;
import java.util.Map;


/**
 * 系统用户
 */
public interface SysUserService extends BaseService<SysUserEntity> {

	PageData<SysUserDTO> page(Map<String, Object> params);

	List<SysUserDTO> list(Map<String, Object> params);

	SysUserDTO get(Long id);

	SysUserDTO getByUsername(String username);

	SysUserDTO save(SysUserDTO dto);

	void update(SysUserDTO dto);

	void delete(Long[] ids);

	/**
	 * 修改密码
	 * @param id           用户ID
	 * @param newPassword  新密码
	 */
	void updatePassword(Long id, String newPassword);

	/**
	 * 角色绑定的用户姓名列表
	 * @param roleId
	 * @return
	 */
	List<String> realNamesByRoleId(Long roleId);

	/**
	 * 根据ID查询真实姓名
	 * @param userId 用户ID
	 * @return
	 */
	String realNameByUserId(Long userId);

    List<Long> getUserIdsByRealName(String realName);

	/**
	 * 设置“创建者”字段查询
	 *
	 * @param queryWrapper
	 * @param params
	 */
    void setWrapperCreatorName(QueryWrapper queryWrapper, Map<String, Object> params);
}
