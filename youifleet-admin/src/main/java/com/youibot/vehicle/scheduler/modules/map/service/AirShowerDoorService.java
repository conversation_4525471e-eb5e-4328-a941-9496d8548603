package com.youibot.vehicle.scheduler.modules.map.service;

import com.youibot.vehicle.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.vehicle.scheduler.modules.map.dto.AirShowerDoorDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.AirShowerDoor;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;

import java.util.List;
import java.util.Map;

public interface AirShowerDoorService {

    List<AirShowerDoorDTO> searchAll(Map<String, Object> searchMap, boolean isDraft);

    AirShowerDoorDTO insert(AirShowerDoorDTO airShowerDoorDTO);

    AirShowerDoorDTO update(AirShowerDoorDTO airShowerDoorDTO);

    void deleteByCode(String vehicleMapCode, String code);

    AirShowerDoorDTO selectByCode(String vehicleMapCode, String code, boolean isDraft);

    AirShowerDoorDTO selectByCode(String code);

    List<AirShowerDoorDTO> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft);

    /**
     * 获取地图正式风淋门数据
     *
     * @param vehicleMapCode
     * @return
     */
    List<AirShowerDoor> selectByVehicleMapCode(String vehicleMapCode);

    List<AirShowerDoorDTO> getAllAirShowerDoors(boolean isDraft);

    List<AirShowerDoorDTO> getAirShowerDoorsByPaths(List<Path> pathList, boolean isDraft);

    List<AirShowerDoorDTO> getAirShowerDoorsByMarkerCode(String startMarkerCode, String endMarkerCode);

    List<AirShowerDoorDTO> isInAirShowerDoors(VehicleLocation vehicleLocation, boolean isDraft);

    void open(String code, String doorCode);

    void close(String code, String doorCode);

    void deleteDraftByVehicleMapCode(String mapCode);

    /**
     * 删除草稿和正式数据
     *
     * @param mapCode
     */
    void deleteByVehicleMapCode(String mapCode);
}
