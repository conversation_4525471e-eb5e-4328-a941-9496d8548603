package com.youibot.vehicle.scheduler.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统属性
 * @Author：yangpeilin
 * @Date: 2020/5/9 16:40
 */
@Data
@TableName("system_property")
public class SystemPropertyEntity implements Serializable {

    @TableId
    @ApiModelProperty(value="ID",position = 0)
    private Long id;

    @ApiModelProperty(value="类型:system:系统类型、user:用户类型",position = 1)
    private String type;

    @ApiModelProperty(value="分类:info、data、map、traffic等",position = 1)
    private String category;

    @TableField(exist = false)
    @ApiModelProperty(value="title",position = 2)
    private String title;

    @TableField(exist = false)
    @ApiModelProperty(value="备注",position = 4)
    private String remark;

    @ApiModelProperty(value="key",position = 2)
    @TableField("property_key")
    private String propertyKey;

    @ApiModelProperty(value="value",position = 3)
    @TableField("property_value")
    private String propertyValue;

    @ApiModelProperty(value="String,Integer,Double,Long,JSON等",position = 4)
    @TableField("value_type")
    private String valueType;

    @ApiModelProperty(value="创建时间",position = 6)
    private Date createDate;
    
    @ApiModelProperty(value="更新时间",position = 7)
    private Date updateDate;
}
