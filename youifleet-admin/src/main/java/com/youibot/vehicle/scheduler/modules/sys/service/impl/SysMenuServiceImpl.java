package com.youibot.vehicle.scheduler.modules.sys.service.impl;

import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.service.impl.BaseServiceImpl;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.HttpContextUtils;
import com.youibot.vehicle.scheduler.common.utils.TreeUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.security.user.UserDetail;
import com.youibot.vehicle.scheduler.modules.sys.dao.SysMenuDao;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysMenuDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SysMenuEntity;
import com.youibot.vehicle.scheduler.modules.sys.enums.SuperAdminEnum;
import com.youibot.vehicle.scheduler.modules.sys.service.SysLanguageService;
import com.youibot.vehicle.scheduler.modules.sys.service.SysMenuService;
import com.youibot.vehicle.scheduler.modules.sys.service.SysRoleMenuService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class SysMenuServiceImpl extends BaseServiceImpl<SysMenuDao, SysMenuEntity> implements SysMenuService {
	@Autowired
	private SysRoleMenuService sysRoleMenuService;
	@Autowired
	private SysLanguageService sysLanguageService;

	@Override
	public SysMenuDTO get(Long id) {
		SysMenuEntity entity = baseDao.getById(id, HttpContextUtils.getLanguage());

		SysMenuDTO dto = ConvertUtils.sourceToTarget(entity, SysMenuDTO.class);

		return dto;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void save(SysMenuDTO dto) {
		SysMenuEntity entity = ConvertUtils.sourceToTarget(dto, SysMenuEntity.class);

		//保存菜单
		insert(entity);
		saveLanguage(entity.getId(), "name", entity.getName());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void update(SysMenuDTO dto) {
		SysMenuEntity entity = ConvertUtils.sourceToTarget(dto, SysMenuEntity.class);

		//上级菜单不能为自身
		if(entity.getId().equals(entity.getPid())){
            String message = I18nMessageUtils.getMessage("system.menu.config.is.error");
            throw new FleetException(message);
		}

		//更新菜单
		updateById(entity);
		saveLanguage(entity.getId(), "name", entity.getName());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Long id) {
		//删除菜单
		deleteById(id);

		//删除菜单国际化
		sysLanguageService.deleteLanguage("sys_menu", id);

		//删除角色菜单关系
		sysRoleMenuService.deleteByMenuId(id);
	}

	@Override
	public List<SysMenuDTO> getAllMenuList(Integer type) {
		List<SysMenuEntity> menuList = baseDao.getMenuList(type, HttpContextUtils.getLanguage());

		List<SysMenuDTO> dtoList = ConvertUtils.sourceToTarget(menuList, SysMenuDTO.class);

		return TreeUtils.build(dtoList, Constant.MENU_ROOT);
	}

	@Override
	public List<SysMenuDTO> getUserMenuList(UserDetail user, Integer type) {
		//系统管理员，拥有最高权限
		List<SysMenuEntity> menuList = getTileUserMenu(user, type);

		List<SysMenuDTO> dtoList = ConvertUtils.sourceToTarget(menuList, SysMenuDTO.class);

		return TreeUtils.build(dtoList);
	}

	@Override
	public List<SysMenuEntity> getTileUserMenu(UserDetail user, Integer type) {
		return user.getSuperAdmin() == SuperAdminEnum.YES.value() ? baseDao.getMenuList(type, HttpContextUtils.getLanguage()) : baseDao.getUserMenuList(user.getId(), type, HttpContextUtils.getLanguage());
	}

	@Override
	public List<SysMenuDTO> getListPid(Long pid) {
		List<SysMenuEntity> menuList = baseDao.getListPid(pid);

		return ConvertUtils.sourceToTarget(menuList, SysMenuDTO.class);
	}

	@Override
	public List<String> getRolePermissionsList(Long roleId) {
		List<String> permissionsList = baseDao.getRolePermissionsList(roleId);
		Set<String> set = new HashSet<>();
		for (String permission : permissionsList) {
			if (StringUtils.isNotBlank(permission)) {
				set.addAll(Stream.of(permission.trim().split(",")).collect(Collectors.toSet()));
			}
		}
		return new ArrayList<>(set);
	}

	private void saveLanguage(Long tableId, String fieldName, String fieldValue){
		sysLanguageService.saveOrUpdate("sys_menu", tableId, fieldName, fieldValue);
	}

}