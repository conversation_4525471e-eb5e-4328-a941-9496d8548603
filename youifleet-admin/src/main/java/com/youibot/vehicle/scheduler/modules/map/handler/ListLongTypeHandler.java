package com.youibot.vehicle.scheduler.modules.map.handler;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

@MappedTypes({List.class, Long.class})
public class ListLongTypeHandler extends AbstractJsonTypeHandler<List<Long>> {

    @Override
    protected List<Long> parse(String json) {
        return JSONArray.parseArray(json, Long.class);
    }

    @Override
    protected String toJson(List<Long> obj) {
        return JSONArray.toJSONString(obj);
    }
}
