package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.vehicle.scheduler.modules.map.handler.ListLongTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "map_area", autoResultMap = true)
public class MapArea {

    /**
     * 编码
     */
    @TableId(type = IdType.INPUT)
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 区域类型 单机区域:SingleAgvArea 显示区域: ShowArea 封控区域：ControlArea 通道区域：ChannelArea 禁旋区域：NoRotatingArea 禁停区域：NoParkingArea  禁入区域：ForbiddenArea
     */
    private String areaType;

    /**
     * 区域坐标列表，最终能够围成一个方形;
     */
    private String polygon;

    /**
     * 地图编码
     */
    private String vehicleMapCode;

    /**
     * 区域颜色
     */
    private String areaColor;

    /**
     * 构建区域的方式：1、多边形区域方式构建、2、矩形区域方式构建
     */
    private String operateType;

    /**
     * 绑定的机器人类型ID列表, 空数组是表示所有机器人都生效
     */
    @TableField(typeHandler = ListLongTypeHandler.class)
    private List<Long> vehicleTypeIds;

    /**
     * 使用状态 启用:true  禁用:false
     */
    private Boolean enable;
}
