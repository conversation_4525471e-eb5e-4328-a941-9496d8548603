package com.youibot.vehicle.scheduler.modules.warehouse.entity;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.vehicle.scheduler.common.entity.BaseEntity;
import lombok.*;

import java.util.Date;

/**
 * 库位
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "warehouse")
public class WarehouseEntity extends BaseEntity {

    /**
     * 编码
     */
    private String code;

    /**
     * 库位区域code
     */
    private String warehouseAreaCode;

    /**
     * 库位类型code
     */
    private String warehouseTypeCode;

    /**
     * 排数
     */
    private Integer rowNum;

    /**
     * 列数
     */
    private Integer columnNum;

    /**
     * 层数
     */
    private Integer layerNum;

    /**
     * 作业高度 单位:毫米(ms)
     */
    private Integer workHeight;

    /**
     * 作业点位编码
     */
    private String workMarkerCode;

    /**
     * 占用状态  占用：Lock  空闲：Free  存储：Store
     */
    private String occupyStatus;

    /**
     * 容器条码
     */
    private String containerBarcode;

    /**
     * 使用状态 启用:Enable  禁用:Disable
     */
    private String usageStatus;

    /**
     * 扩展属性1
     */
    private String extendParam1;

    /**
     * 扩展属性2
     */
    private String extendParam2;

    /**
     * 扩展属性3
     */
    private String extendParam3;

    /**
     * 扩展属性4
     */
    private String extendParam4;

    /**
     * 扩展属性5
     */
    private String extendParam5;

    /**
     * 扩展属性6
     */
    private String extendParam6;

    /**
     * 扩展属性7
     */
    private String extendParam7;

    /**
     * 扩展属性8
     */
    private String extendParam8;

    /**
     * 扩展属性9
     */
    private String extendParam9;

    /**
     * 扩展属性10
     */
    private String extendParam10;

    /**
     * 库位占用状态变更时间
     */
    private Date lastStatusUpdateDate;

    /**
     * 修改者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
