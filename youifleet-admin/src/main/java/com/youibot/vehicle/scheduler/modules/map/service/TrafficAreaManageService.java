package com.youibot.vehicle.scheduler.modules.map.service;

import com.youibot.vehicle.scheduler.api.dto.OccupyDto;
import com.youibot.vehicle.scheduler.api.dto.ReleaseDto;
import com.youibot.vehicle.scheduler.modules.map.entity.TrafficAreaManage;
import java.util.List;

public interface TrafficAreaManageService {

    void initAreaResources();

    List<TrafficAreaManage> getThirdSystemAreaResources();

    void occupy(OccupyDto occupyDto);

    void release(ReleaseDto occupyDto);

    void clearResources(String areaCode);

    void occupyThirdSystem(String areaCode, String vehicleCode);

    void releaseThirdSystem(String areaCode);
}
