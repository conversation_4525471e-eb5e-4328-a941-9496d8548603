package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.youibot.vehicle.scheduler.modules.map.handler.ListPathExtendParamTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.handler.ListPathInfoTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.handler.PathParamTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
@TableName(value = "path", autoResultMap = true)
public class Path  implements Cloneable {

    /**
     * 编码
     */
    @TableId(type = IdType.INPUT)
    private String code;

    /**
     * 地图编码
     */
    private String vehicleMapCode;

    /**
     * 开始标记点
     */
    private String startMarkerCode;

    /**
     * 结束标记点
     */
    private String endMarkerCode;

    /**
     * 禁用的机器人类型，多个用逗号隔开: Lift 顶升,Compose 复合
     */
    private String disableAgvTypes;

    /**
     * 车头朝向: none 自适应,0,90,180,-90,-180
     */
    private Integer agvDirection;

    /**
     * 路径类型，Common、普通路径，QR_Down、二维码对接路径，Shelflegs、货架腿对接，Symbol_V、V型板对接，Reflector、反光板对接，LeaveDocking、脱离对接，Pallet、托盘对接
     * 各个类型所需数据：
     * 普通路径            无
     * 二维码对接路径      偏差X 偏差Y 偏差角度 对接方向 对接的二维码编码
     * 货架腿对接          偏差X 偏差Y 偏差角度 对接方向
     * V型板对接           偏差X 偏差Y 偏差角度 对接方向
     * 反光板对接          偏差X 偏差Y 偏差角度 对接方向
     * 脱离对接            无
     */
    private String pathType;

    /**
     * 对接策略：1、始终对接 2、非终点不对接
     */
    private Integer dockingStrategy;

    /**
     * 线类型 1、直线 2、曲线（只要不为1,目前都默认为曲线）
     */
    private Integer lineType;

    /**
     * 对接的二维码编码
     */
    private Integer workStationCode;

    /**
     * 偏差X
     */
    private Double offsetX;

    /**
     * 偏差Y
     */
    private Double offsetY;

    /**
     * 偏差角度
     */
    private Double offsetAngle;

    /**
     * 模板编号
     */
    private Integer templateNo;

    /**
     * 相机避障: 1开启 0关闭 默认1
     */
    private Integer cameraObstacle;

    /**
     * 路径权重系数
     */
    private Double weightRatio;

    /**
     * 平移速度 m/s
     */
    private Double movingSpeed;

    /**
     * 旋转速度 rad/s
     */
    private Double rotationSpeed;

    /**
     * 移动避障区域，值1-16代表第1-16组区域，可以传空
     */
    private Integer moveObstacleRegion;

    /**
     * 旋转避障区域，值1-16代表第1-16组区域，可以传空
     */
    private Integer rotationObstacleRegion;

    /**
     * 融合特征 1、开启 0、关闭
     */
    private Integer featureFusion;

    /**
     * 避障 1、打开避障  2、关闭避障
     */
    private Integer safety;

    /**
     * 拓展布尔
     */
    private String extendBit;

    /**
     * 拓展字符串
     */
    private String extendString;

    /**
     * 自主绕障: 1开启 0关闭 默认0
     */
    private Integer obstacleAvoidance;

    @TableField(exist = false)
    private Integer navigationType;//导航类型  0、正常导航 1、方向调整+导航 2、进入电梯 3、出来电梯 4、乘坐电梯 5、经过自动门
    //三阶贝塞尔曲线的起始端点
    @JsonIgnore
    @TableField(exist = false)
    private Double t0;
    //三阶贝塞尔曲线的结束端点
    @JsonIgnore
    @TableField(exist = false)
    private Double t1;
    //路径所属电梯,虚拟路径属于哪个电梯
    @JsonIgnore
    @TableField(exist = false)
    private String elevatorCode;

    /**
     * 路径信息
     */
    @TableField(typeHandler = ListPathInfoTypeHandler.class)
    private List<PathInfo> pathInfos;

    /**
     * 路径扩展参数：固定在页面展示的参数
     */
    @TableField(typeHandler = PathParamTypeHandler.class)
    private PathParam params;

    /**
     * 可变路径扩展参数：不固定，下发给底盘用的定制化参数
     */
    @TableField(typeHandler = ListPathExtendParamTypeHandler.class)
    private List<PathExtendParam> extendParamList;


    public Path(String startMarkerCode, String endMarkerCode, String vehicleMapCode, Double length, String elevatorCode) {
        //这个构造函数是创建电梯时候调的, 因此code采用 起点编码 - 终点编码 生成
        this.code = startMarkerCode + "-" + endMarkerCode;
        this.vehicleMapCode = vehicleMapCode;
        this.startMarkerCode = startMarkerCode;
        this.endMarkerCode = endMarkerCode;
        this.elevatorCode = elevatorCode;
        this.agvDirection = 0;
    }

    @Override
    public Path clone() {
        try {
            return (Path) super.clone();
        } catch (CloneNotSupportedException e) {
            log.error("Clone side path error.", e.getMessage());
            throw new AssertionError();
        }
    }
}
