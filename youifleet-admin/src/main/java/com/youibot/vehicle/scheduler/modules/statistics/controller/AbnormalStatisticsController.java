package com.youibot.vehicle.scheduler.modules.statistics.controller;

import cn.hutool.core.date.DateUtil;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.statistics.dto.AbnormalHeatMapDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.AbnormalStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.service.AbnormalStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/statistics/abnormal")
@Api(tags = "统计-异常")
public class AbnormalStatisticsController {

    @Autowired
    private AbnormalStatisticsService abnormalStatisticsService;

    @ApiOperation(value = "异常统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String")
    })
    @GetMapping()
    public Result<AbnormalStatisticsDTO> statistics(@RequestParam(value = "startTime") String startTime,
                                                    @RequestParam(value = "endTime") String endTime) {
        return Result.suc(abnormalStatisticsService.statistics(DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1)));
    }

    @ApiOperation(value = "异常热力图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图编码", required = true, dataType = "String")
    })
    @GetMapping("/heatMap")
    public Result<List<AbnormalHeatMapDTO>> heatMap(@RequestParam(value = "startTime") String startTime,
                                                   @RequestParam(value = "endTime") String endTime,
                                                   @RequestParam(value = "vehicleMapCode") String vehicleMapCode) {
        return Result.suc(abnormalStatisticsService.heatMap(DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1), vehicleMapCode));
    }

}
