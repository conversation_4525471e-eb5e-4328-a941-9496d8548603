package com.youibot.vehicle.scheduler.modules.map.handler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.entity.MarkerParam;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes({MarkerParam.class})
public class MarkerParamTypeHandler extends AbstractJsonTypeHandler<MarkerParam> {

    @Override
    protected MarkerParam parse(String json) {
        return JSONObject.parseObject(json, MarkerParam.class);
    }

    @Override
    protected String toJson(MarkerParam obj) {
        return JSONObject.toJSONString(obj);
    }
}
