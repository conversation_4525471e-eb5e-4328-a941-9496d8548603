package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 点位和路径批量生成
 * <AUTHOR>
 */
@Data
@ApiModel(value = "MarkerAndPathBatchGenerateDTO", description = "标记点")
public class MarkerAndPathBatchGenerateDTO {

    @ApiModelProperty(value = "地图编码", position = 0)
    private String vehicleMapCode;

    @ApiModelProperty(value = "x坐标", position = 1)
    private Double x;

    @ApiModelProperty(value = "y坐标", position = 2)
    private Double y;

    @ApiModelProperty(value = "行数", position = 3)
    private Integer rows;

    @ApiModelProperty(value = "列数", position = 4)
    private Integer columns;

    @ApiModelProperty(value = "行间距", position = 5)
    private Integer rowSpacing;

    @ApiModelProperty(value = "列间距", position = 6)
    private Integer columnSpacing;

    @ApiModelProperty(value = "旋转角度", position = 7)
    private Double angle = 0.0;

    @ApiModelProperty(value = "定位图编码", position = 8)
    private String locatingCode;
}
