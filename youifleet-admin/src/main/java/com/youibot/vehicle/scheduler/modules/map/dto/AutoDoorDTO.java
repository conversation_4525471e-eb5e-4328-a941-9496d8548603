package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import com.youibot.vehicle.scheduler.modules.map.entity.MapElement;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "AutoDoorDTO", description = "自动门")
public class AutoDoorDTO extends MapElement {

    @NotNull(message="{door.code.require}", groups = UpdateGroup.class)
    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @NotBlank(message = "{map.code.require}", groups = {AddGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "地图编码", position = 2)
    private String vehicleMapCode;

    @ApiModelProperty(value = "角度,用于前端页面显示", position = 4)
    @Range(min = -180,max = 180)
    private Double angle = 0.0;

    @ApiModelProperty(value = "Modbus读功能码（01，02，03，04）", position = 4)
    private String readFunctionCode;

    @ApiModelProperty(value = "Modbus写功能码（05，06）", position = 5)
    private String writeFunctionCode;

    @NotNull(message="{door.ip.require}", groups = UpdateGroup.class)
    @ApiModelProperty(value = "自动门ip", position = 2)
    private String ip;

    @NotNull(message="{door.port.require}", groups = UpdateGroup.class)
    @ApiModelProperty(value = "自动门端口", position = 3)
    private Integer port;

    @NotNull(message="{door.openAddress.require}", groups = UpdateGroup.class)
    @ApiModelProperty(value = "开门地址", position = 4)
    private Integer openAddress;

    @NotNull(message="{door.openStatusAddress.require}", groups = UpdateGroup.class)
    @ApiModelProperty(value = "开门状态地址", position = 5)
    private Integer openStatusAddress;

    @ApiModelProperty(value = "关门地址", position = 6)
    private Integer closeAddress;

    @ApiModelProperty(value = "关门状态地址", position = 7)
    private Integer closeStatusAddress;

    @NotNull(message="{door.pathCodes.require}", groups = UpdateGroup.class)
    @ApiModelProperty(value = "关联的路径code", position = 9)
    private List<String> pathCodes ;

    @ApiModelProperty(value = "门已开:OPEN 门未开:CLOSE 通讯异常:COMMUNICATION_ERROR 操作中:OPERATING 参数异常:PARAM_ERROR", position = 10)
    private String currentStatus;

    @ApiModelProperty(value = "最后一次关门完成时间", position = 11)
    private Date lastCloseDoorTime;

}
