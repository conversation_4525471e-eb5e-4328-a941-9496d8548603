package com.youibot.vehicle.scheduler.modules.sys.service;

import com.youibot.vehicle.scheduler.common.service.BaseService;
import com.youibot.vehicle.scheduler.modules.sys.entity.SysLanguageEntity;


/**
 * 国际化
 */
public interface SysLanguageService extends BaseService<SysLanguageEntity> {

    /**
     * 保存或更新
     * @param tableName   表名
     * @param tableId     表主键
     * @param fieldName   字段名
     * @param fieldValue  字段值
     */
    void saveOrUpdate(String tableName, Long tableId, String fieldName, String fieldValue);

    /**
     * 删除国际化
     * @param tableName   表名
     * @param tableId     表主键
     */
    void deleteLanguage(String tableName, Long tableId);
}

