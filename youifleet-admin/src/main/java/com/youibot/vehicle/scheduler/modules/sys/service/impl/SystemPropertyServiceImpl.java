package com.youibot.vehicle.scheduler.modules.sys.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.impl.BaseServiceImpl;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.sys.dao.SystemPropertyDao;
import com.youibot.vehicle.scheduler.modules.sys.dto.SystemPropertyDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemConfigEntity;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemPropertyEntity;
import com.youibot.vehicle.scheduler.modules.sys.enums.SysModeEnum;
import com.youibot.vehicle.scheduler.modules.sys.service.SystemPropertyService;
import com.youibot.vehicle.scheduler.modules.vehicle.service.VehicleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.youibot.vehicle.scheduler.modules.sys.constant.SysConstant.I18N_MESSAGE_REMARK_PREFIX;
import static com.youibot.vehicle.scheduler.modules.sys.constant.SysConstant.I18N_MESSAGE_TITLE_PREFIX;

/**
 * @Author：xyh
 * @Date: 2023/1/7 16:44
 */
@Service
public class SystemPropertyServiceImpl extends BaseServiceImpl<SystemPropertyDao, SystemPropertyEntity> implements SystemPropertyService {

    private volatile SystemConfigEntity systemConfigEntity;
    @Autowired
    private VehicleService vehicleService;

    @Value("${youifleet.system.version}")
    private String systemVersion;

    @Value("${youifleet.system.mode}")
    private String systemMode;


    //用于系统配置
    @Override
    public SystemConfigEntity getSystemConfig() {
        if (systemConfigEntity != null) {
            return systemConfigEntity;
        }

        List<SystemPropertyEntity> list = this.baseDao.selectList(new QueryWrapper<>());
        this.setTitleAndRemark(list);
        if (CollectionUtils.isEmpty(list)) {
            systemConfigEntity = new SystemConfigEntity();
            return systemConfigEntity;
        }
        Map<String, SystemPropertyEntity> map = list.stream().collect(Collectors.toMap(SystemPropertyEntity::getPropertyKey, Function.identity()));

        JSONObject configObj = new JSONObject();
        Set<String> strings = map.keySet();
        for (String key : strings) {
            SystemPropertyEntity systemProperty = map.get(key);
            Object val = null;
            if (systemProperty != null) {
                val = ConvertUtils.getRealParameterValue(systemProperty.getValueType(), systemProperty.getPropertyValue());
            }
            configObj.put(key, val);
        }

        systemConfigEntity = JSON.parseObject(configObj.toJSONString(), SystemConfigEntity.class);
        return systemConfigEntity;
    }

    @Override
    public String getSysMode() {
        return systemMode;
    }

    @Override
    public boolean isAloneMode() {
        return Objects.equals(systemMode, SysModeEnum.alone.name());
    }

    private QueryWrapper<SystemPropertyEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<SystemPropertyEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(params.get("type") != null, "type", params.get("type"));
        queryWrapper.eq(params.get("category") != null, "category", params.get("category"));
        queryWrapper.eq(params.get("propertyKey") != null, "property_key", params.get("propertyKey"));
        queryWrapper.eq(params.get("valueType") != null, "value_type", params.get("valueType"));
        return queryWrapper;
    }

    @Override
    public PageData<SystemPropertyDTO> page(Map<String, Object> params) {
        QueryWrapper<SystemPropertyEntity> queryWrapper = getWrapper(params);
        IPage<SystemPropertyEntity> page = baseDao.selectPage(getPage(params, "id", true), queryWrapper);
        this.setTitleAndRemark(page.getRecords());
        return getPageData(page, SystemPropertyDTO.class);
    }

    @Override
    public List<SystemPropertyEntity> selectList(SystemPropertyEntity systemPropertyEntity) {
        List<SystemPropertyEntity> list = super.selectList(systemPropertyEntity);
        this.setTitleAndRemark(list);
        return list;
    }

    @Override
    public SystemPropertyEntity selectById(Serializable id) {
        SystemPropertyEntity entity = super.selectById(id);
        this.setTitleAndRemark(entity);
        return entity;
    }

    @Override
    public List<SystemPropertyDTO> list(Map<String, Object> searchMap) {
        QueryWrapper<SystemPropertyEntity> queryWrapper = getWrapper(searchMap);
        List<SystemPropertyEntity> list = this.baseDao.selectList(queryWrapper);
        this.setTitleAndRemark(list);
        /**
         * 更新版本号到页面显示，每次新增功能和修复问题都需要更新
         * 结构实例：YOUIFleet v5.0.6.20240617（主版本），YOUIFleet v5.0.4-dgjx.20240617（定制分支）
         */
        for (SystemPropertyEntity entity : list) {
            if (entity.getPropertyKey().equalsIgnoreCase("systemVersion") && StringUtils.isNotBlank(systemVersion)) {
                entity.setPropertyValue(systemVersion);
            }
            if (Objects.equals(entity.getPropertyKey(), "licenseValidTimeRange")) {
                if (isAloneMode()) entity.setPropertyValue("- - -");
            }
        }
        return ConvertUtils.sourceToTarget(list, SystemPropertyDTO.class);
    }

    private void setTitleAndRemark(List<SystemPropertyEntity> list) {
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(this::setTitleAndRemark);
        }
    }

    private void setTitleAndRemark(SystemPropertyEntity entity) {
        if (entity != null) {
            entity.setTitle(I18nMessageUtils.getMessage(I18N_MESSAGE_TITLE_PREFIX + entity.getPropertyKey()));
            entity.setRemark(I18nMessageUtils.getMessage(I18N_MESSAGE_REMARK_PREFIX + entity.getPropertyKey()));
            if ("ownCompany".equals(entity.getPropertyKey())) {
                entity.setPropertyValue(I18nMessageUtils.getMessage("config.company.name"));
            }
            if ("licenseCompanyName".equals(entity.getPropertyKey()) && StrUtil.isBlankIfStr(entity.getPropertyValue())) {
                entity.setPropertyValue(I18nMessageUtils.getMessage("config.company.name"));
            }
        }
    }

    @Override
    public boolean insert(SystemPropertyDTO systemProperty) {
        checkPropertyValue(systemProperty);
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("type", systemProperty.getType());
        searchMap.put("category", systemProperty.getCategory());
        searchMap.put("property_key", systemProperty.getPropertyKey());
        QueryWrapper<SystemPropertyEntity> queryWrapper = getWrapper(searchMap);
        List<SystemPropertyEntity> list = this.baseDao.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            String message = I18nMessageUtils.getMessage("config.property.is.exist.error", systemProperty.getType() + "," + systemProperty.getCategory() + "," + systemProperty.getPropertyKey());
            throw new FleetException(message);
        }
        SystemPropertyEntity systemPropertyEntity = ConvertUtils.sourceToTarget(systemProperty, SystemPropertyEntity.class);
        return super.insert(systemPropertyEntity);
    }

    @Override
    public boolean updateById(SystemPropertyDTO systemProperty) {
        checkPropertyValue(systemProperty);
        SystemPropertyEntity systemPropertyEntity = ConvertUtils.sourceToTarget(systemProperty, SystemPropertyEntity.class);
        QueryWrapper<SystemPropertyEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("id", systemProperty.getId());
        queryWrapper.eq("type", systemProperty.getType());
        queryWrapper.eq("category", systemProperty.getCategory());
        queryWrapper.eq("property_key", systemProperty.getPropertyKey());
        List<SystemPropertyEntity> list = this.baseDao.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            String message = I18nMessageUtils.getMessage("config.property.is.exist.error", systemProperty.getType() + "," + systemProperty.getCategory() + "," + systemProperty.getPropertyKey());
            throw new FleetException(message);
        }
        //如果是修改充电泊车配置，则将配置修改到所有机器人
        if (Lists.newArrayList("charge", "park").contains(systemProperty.getCategory())) {
            vehicleService.updateConfig(systemProperty.getPropertyKey(), systemProperty.getPropertyValue());
        }
        boolean result = super.updateById(systemPropertyEntity);
        this.systemConfigEntity = null;
        return result;
    }

    @Override
    public boolean deleteById(Serializable id) {
        boolean result = super.deleteById(id);
        this.systemConfigEntity = null;
        return result;
    }

    @Override
    public boolean insertBatch(List<SystemPropertyDTO> list) {
        checkRepeat(list);
        list.forEach(item -> {
            insert(item);
        });
        return true;
    }

    @Override
    public boolean updateBatchById(List<SystemPropertyDTO> list) {
        checkRepeat(list);
        list.forEach(item -> {
            updateById(item);
        });
        this.systemConfigEntity = null;
        return true;
    }

    private void checkRepeat(List<SystemPropertyDTO> list) {
        //type + category + propertyKey 不能重复
        Set<String> keys = new HashSet<>();
        list.forEach(item -> {
            String key = item.getType() + "_" + item.getCategory() + "_" + item.getPropertyKey();
            if (keys.contains(key)) {
                String message = I18nMessageUtils.getMessage("config.property.type.is.duplicate.error", item.getType() + "," + item.getCategory() + "," + item.getPropertyKey());
                throw new FleetException(message);
            } else {
                keys.add(key);
            }
        });
    }

    /**
     * 校验数据清理配置的数据范围
     * userOptLogExpireTime ("用户操作日志保留时间，范围：30-365，单位：天")
     * interfaceLogExpireTime ("系统接口日志保留时间，范围：1-365，单位：天")
     * runningLogExpireTime ("系统运行日志保留时间，范围：1-365，单位：天")
     * notificationExpireTime ("系统告警通知保留时间，范围：1-365，单位：天")
     * businessDataExpireTime ("业务的运行数据保留时间，包含任务列表等业务数据，范围：60-1095，单位：天")
     * reportDataExpireTime ("统计归档后的报表数据保留时间，范围：60-1095，单位：天")
     */
    private void checkPropertyValue(SystemPropertyDTO systemProperty) {
        String propertyKey = systemProperty.getPropertyKey();
        String propertyValue = systemProperty.getPropertyValue();
        String title = I18nMessageUtils.getMessage(I18N_MESSAGE_TITLE_PREFIX + propertyKey);
        switch (propertyKey) {
            case "userOptLogExpireTime":
                checkRange(title, Integer.valueOf(propertyValue), 30, 365, I18nMessageUtils.getMessage("config.unit.day"));
                break;
            case "interfaceLogExpireTime":
            case "runningLogExpireTime":
            case "notificationExpireTime":
                checkRange(title, Integer.valueOf(propertyValue), 1, 365, I18nMessageUtils.getMessage("config.unit.day"));
                break;
            case "businessDataExpireTime":
            case "reportDataExpireTime":
                checkRange(title, Integer.valueOf(propertyValue), 60, 1095, I18nMessageUtils.getMessage("config.unit.day"));
                break;
            case "blockCheckInterval":
            case "removeBlockInterval":
            case "disconnectionTime":
                checkRange(title, Double.valueOf(propertyValue), 0, 99999, I18nMessageUtils.getMessage("config.unit.second"));
                break;
            case "pathApplyLength":
            case "occupyResourceRange":
                checkRange(title, Double.valueOf(propertyValue), 0.1, 9999, I18nMessageUtils.getMessage("config.unit.meter"));
                break;
            case "markerSpacing":
                if(1 == systemConfigEntity.getMarkerSpacingCheck()){
                    checkRange(title, Double.valueOf(propertyValue), 0.1, 9999, I18nMessageUtils.getMessage("config.unit.meter"));
                }
                break;
            case "markerAndPathSpacing":
                if(1 == systemConfigEntity.getMarkerAndPathSpacingCheck()) {
                    checkRange(title, Double.valueOf(propertyValue), 0.1, 9999, I18nMessageUtils.getMessage("config.unit.meter"));
                }
                break;
            case "autoDoorAdvanceLength":
            case "showerDoorAdvanceLength":
            case "elevatorAdvanceLength":
                checkRange(title, Double.valueOf(propertyValue), 0, 99, I18nMessageUtils.getMessage("config.unit.meter"));
                break;
            default:
        }
    }

    private static <T extends Number> void checkRange(String message, T val, T start, T end, String unit) {
        String format = MessageFormat.format("[{0}]{1}：{2} - {3} {4}", message, I18nMessageUtils.getMessage("config.value.range"), start, end, unit);
        if (val.doubleValue() < start.doubleValue() || val.doubleValue() > end.doubleValue()) {
            throw new FleetException(format);
        }
    }

}
