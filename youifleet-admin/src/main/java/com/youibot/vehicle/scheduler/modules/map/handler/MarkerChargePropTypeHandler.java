package com.youibot.vehicle.scheduler.modules.map.handler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.entity.MarkerChargeProp;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes({MarkerChargeProp.class})
public class MarkerChargePropTypeHandler extends AbstractJsonTypeHandler<MarkerChargeProp> {

    @Override
    protected MarkerChargeProp parse(String json) {
        return JSONObject.parseObject(json, MarkerChargeProp.class);
    }

    @Override
    protected String toJson(MarkerChargeProp obj) {
        return JSONObject.toJSONString(obj);
    }
}
