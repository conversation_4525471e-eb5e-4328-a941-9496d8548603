package com.youibot.vehicle.scheduler.modules.map.handler;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.youibot.vehicle.scheduler.modules.map.entity.MarkerInfo;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

@MappedTypes({List.class, MarkerInfo.class})
public class ListMarkerInfoTypeHandler extends AbstractJsonTypeHandler<List<MarkerInfo>> {

    @Override
    protected List<MarkerInfo> parse(String json) {
        return JSONArray.parseArray(json, MarkerInfo.class);
    }

    @Override
    protected String toJson(List<MarkerInfo> obj) {
        return JSONArray.toJSONString(obj);
    }
}
