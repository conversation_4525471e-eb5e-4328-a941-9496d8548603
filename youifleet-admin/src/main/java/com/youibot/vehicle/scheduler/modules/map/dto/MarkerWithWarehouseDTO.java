package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.modules.warehouse.dto.WarehouseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 标记点
 * 类名称：Marker
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "MarkerWithWarehouseDTO", description = "标记点")
public class MarkerWithWarehouseDTO extends MarkerDTO{

    @ApiModelProperty(value = "点位关联的库位集合")
    private List<WarehouseDTO> warehouseList;

}
