package com.youibot.vehicle.scheduler.modules.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "TaskTypeParamDTO",description = "任务类型参数")
public class TaskTypeParamDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 任务类型ID
     */
    @ApiModelProperty(value = "任务类型ID")
    private Long taskTypeId;

    /**
     * 参数类别: Text、Number
     */
    @ApiModelProperty(value = "参数类别: Text、Number")
    private String variableCategory;

    /**
     * 参数类型 参照ParamConstant
     */
    @ApiModelProperty(value = "参数类型")
    private String type;

    /**
     * 参数变量
     */
    @ApiModelProperty(value = "参数变量")
    private String variable;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    @ApiModelProperty(value = "创建人")
    private Long  creator;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "修改人")
    private Long updater;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

}
