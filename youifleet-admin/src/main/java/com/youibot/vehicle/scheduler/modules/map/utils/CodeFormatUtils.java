package com.youibot.vehicle.scheduler.modules.map.utils;

import org.springframework.util.StringUtils;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 地图元素编码格式化工具
 * <AUTHOR>
 */
public class CodeFormatUtils {

    //分隔符
    public static final String SPLIT_CHAR = "_";

    //地图元素编码格式前缀
    //点位前缀
    public static final String MARKER_CODE_PREFIX = "P";
    //路径前缀
    public static final String PATH_CODE_PREFIX = "L";
    //区域前缀
    public static final String AREA_CODE_PREFIX = "A";
    //电梯前缀
    public static final String ELEVATOR_CODE_PREFIX = "E";
    //自动门前缀
    public static final String AUTODOOR_CODE_PREFIX = "D";
    //风淋门前缀
    public static final String AUTOSHOWERDOOR_CODE_PREFIX = "WD";

    /**
     * 格式：地图编码_prefix_序号
     *
     * @param mapCode
     * @param code
     * @return
     */
    public static String getFormatCode(String mapCode, String code,String prefix) {
        return mapCode + SPLIT_CHAR + prefix + SPLIT_CHAR + code;
    }

    /**
     * 格式：prefix_序号
     *
     * @param code
     * @return
     */
    public static String getFormatCode(String code,String prefix) {
        return prefix + SPLIT_CHAR + code;
    }


    /**
     * 获取地图元素的编码中的数字部分
     *
     * 格式：
     * mapCode_P_code
     * P_code
     * @param code
     */
    public static String getIntegerCode(String code){
        if(StringUtils.isEmpty(code)){
            return null;
        }
        String regStr1 = "[\\w]+\\_[A-Z]{1,2}\\_([\\d]+)";
        Pattern pattern = Pattern.compile(regStr1);
        Matcher matcher = pattern.matcher(code);
        if(matcher.find()){
            return matcher.group(1);
        }
        String regStr2 = "[A-Z]{1,2}\\_([\\d]+)";
        pattern = Pattern.compile(regStr2);
        matcher = pattern.matcher(code);
        if(matcher.find()){
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 获取地图元素的编码中的数字部分
     *
     * 格式：
     * mapCode_P_code
     * P_code
     * @param code
     */
    public static String getIntegerCodeV2(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        String[] split = code.split(SPLIT_CHAR);
        return split[split.length - 1];
    }

    /**
     * 获取地图元素的编码中的mapCode部分
     *
     * 格式：
     * mapCode_P_code
     * @param code
     */
    private static final String regMapCode = "([\\w]+)\\_[A-Z]{1,2}\\_[\\d]+";
    public static String getMapCode(String code){
        if(StringUtils.isEmpty(code)){
            return null;
        }
        Pattern pattern = Pattern.compile(regMapCode);
        Matcher matcher = pattern.matcher(code);
        if(matcher.find()){
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 例如：map_L_1_2、map_L_2_1，返回map_L_1_2
     */
    public static String getPathCode(String vehicleMapCode, String startMarkerCode, String endMarkerCode){
        String startCode = CodeFormatUtils.getIntegerCodeV2(startMarkerCode);
        String endCode = CodeFormatUtils.getIntegerCodeV2(endMarkerCode);
        int integerStart = Optional.ofNullable(startCode).map(Integer::valueOf).orElse(0);
        int integerEnd = Optional.ofNullable(endCode).map(Integer::valueOf).orElse(0);
        String newCodeNum = integerStart <= integerEnd ? integerStart + CodeFormatUtils.SPLIT_CHAR + integerEnd : integerEnd + CodeFormatUtils.SPLIT_CHAR + integerStart;
        String pathCode = CodeFormatUtils.getFormatCode(vehicleMapCode, newCodeNum, CodeFormatUtils.PATH_CODE_PREFIX);
        return pathCode;
    }

    public static void main(String[] args) {
        System.out.println(getMapCode("ewrwerewr545646_L_44334_t2222"));
        System.out.println(getIntegerCodeV2("L_44334"));
    }
}
