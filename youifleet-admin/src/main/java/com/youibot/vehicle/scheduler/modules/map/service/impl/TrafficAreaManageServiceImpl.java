package com.youibot.vehicle.scheduler.modules.map.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.youibot.vehicle.scheduler.api.dto.OccupyDto;
import com.youibot.vehicle.scheduler.api.dto.ReleaseDto;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.TrafficResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.TrafficAreaResourcePool;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.TrafficAreaManageDao;
import com.youibot.vehicle.scheduler.modules.map.entity.TrafficAreaManage;
import com.youibot.vehicle.scheduler.modules.map.service.TrafficAreaManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * {阐述类的作用}
 *
 * @author: YU
 * @date: 2024-08-07 9:54
 */
@Slf4j
@Service
public class TrafficAreaManageServiceImpl implements TrafficAreaManageService {
    @Autowired
    private TrafficAreaManageDao trafficAreaManageDao;

    @Override
    public void initAreaResources() {
        LambdaQueryWrapper<TrafficAreaManage> query = Wrappers.<TrafficAreaManage>lambdaQuery()
                .isNotNull(TrafficAreaManage::getOccupySource);
        List<TrafficAreaManage> dbDatas = trafficAreaManageDao.selectList(query);
        for (TrafficAreaManage dbData : dbDatas) {
            List<String> dbVehics = JSONUtil.toList(JSONUtil.parseArray(dbData.getOccupyVehicleCodes()), String.class);
            Set<String> dbVehicSet = new HashSet<>(dbVehics);

            TrafficResource trafficResource = new TrafficResource();
            trafficResource.setAreaCode(dbData.getResourceId());
            trafficResource.setOccupySource(dbData.getOccupySource());
            trafficResource.setOccupyVehicleCodes(dbVehicSet);
            //直接插入内存
            TrafficAreaResourcePool.TRAFFIC_VEHICLE_MANAGE.put(dbData.getResourceId(), trafficResource);
            log.debug("初始化服务:交管区域重新占用，占用的区域资源：{}", trafficResource);
        }
    }

    /**
     * 获取第三方系统交管区域资源：主要用于地图发布或系统重启时，恢复资源占用
     */
    @Override
    public List<TrafficAreaManage> getThirdSystemAreaResources() {
        LambdaQueryWrapper<TrafficAreaManage> query = Wrappers.<TrafficAreaManage>lambdaQuery()
                .eq(TrafficAreaManage::getAreaType, MapConstant.MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC);
        return trafficAreaManageDao.selectList(query);
    }

    @Override
    public void occupy(OccupyDto occupyDto) {
        LambdaQueryWrapper<TrafficAreaManage> query = Wrappers.<TrafficAreaManage>lambdaQuery()
                .eq(TrafficAreaManage::getAreaCode, occupyDto.getAreaCode());
        List<TrafficAreaManage> dbDatas = trafficAreaManageDao.selectList(query);
        if (CollUtil.isNotEmpty(dbDatas)) {
            TrafficAreaManage dbData = dbDatas.get(0);
            String dbOccupySource = dbData.getOccupySource();
            String dbOccupyVehicleCodes = dbData.getOccupyVehicleCodes();
            List<String> dbVehics = JSONUtil.toList(JSONUtil.parseArray(dbOccupyVehicleCodes), String.class);
            Set<String> dbVehicSet = new HashSet<>(dbVehics);
            //如果是同一系统的，将车辆编号叠加存储
            if (Objects.equals(dbOccupySource, occupyDto.getSystemCode())) {
                if (StrUtil.isNotEmpty(dbOccupyVehicleCodes)) {
                    dbVehicSet.add(occupyDto.getVehicleCode());
                }
                dbData.setOccupyVehicleCodes(String.valueOf(dbVehicSet));
            } else {
                dbData.setOccupySource(occupyDto.getSystemCode());
                dbData.setOccupyDate(new Date());
                dbData.setOccupyVehicleCodes(String.valueOf(new ArrayList<>(Collections.singletonList(occupyDto.getVehicleCode()))));
            }
            trafficAreaManageDao.updateById(dbData);
            return;
        }
        //没有数据则插入
        TrafficAreaManage trafficAreaManage = new TrafficAreaManage();
        trafficAreaManage.setId(StrUtil.uuid());
        trafficAreaManage.setResourceId(occupyDto.getAreaCode());
        trafficAreaManage.setAreaCode(occupyDto.getAreaCode());
        trafficAreaManage.setAreaType(MapConstant.MAP_AREA_TYPE_TRAFFIC);
        trafficAreaManage.setOccupySource(occupyDto.getSystemCode());
        trafficAreaManage.setOccupyVehicleCodes(String.valueOf(new ArrayList<>(Collections.singletonList(occupyDto.getVehicleCode()))));
        trafficAreaManageDao.insert(trafficAreaManage);
    }

    @Override
    public void release(ReleaseDto releaseDto) {
        LambdaQueryWrapper<TrafficAreaManage> query = Wrappers.<TrafficAreaManage>lambdaQuery()
                .eq(TrafficAreaManage::getAreaCode, releaseDto.getAreaCode());
        List<TrafficAreaManage> dbDatas = trafficAreaManageDao.selectList(query);
        if (CollUtil.isNotEmpty(dbDatas)) {
            TrafficAreaManage dbData = dbDatas.get(0);
            String dbOccupySource = dbData.getOccupySource();
            String dbOccupyVehicleCodes = dbData.getOccupyVehicleCodes();
            List<String> dbVehics = JSONUtil.toList(JSONUtil.parseArray(dbOccupyVehicleCodes), String.class);
            Set<String> dbVehicSet = new HashSet<>(dbVehics);
            //如果是同一系统的，将车辆编号移除后存储
            if (Objects.equals(dbOccupySource, releaseDto.getSystemCode())) {
                dbVehicSet.remove(releaseDto.getVehicleCode());
                if (dbVehicSet.isEmpty()) {
                    //最后一台车释放后 将系统占用的也释放
                    dbData.setOccupySource(null);
                    dbData.setOccupyDate(null);
                }
                dbData.setOccupyVehicleCodes(String.valueOf(dbVehicSet));
            } else {
                dbData.setOccupySource(releaseDto.getSystemCode());
                dbData.setOccupyVehicleCodes(String.valueOf(new ArrayList<>(Collections.singletonList(releaseDto.getVehicleCode()))));
            }
            trafficAreaManageDao.updateById(dbData);
        }
    }

    @Override
    public void clearResources(String areaCode) {
        LambdaQueryWrapper<TrafficAreaManage> query = Wrappers.<TrafficAreaManage>lambdaQuery()
                .eq(TrafficAreaManage::getAreaCode, areaCode);
        List<TrafficAreaManage> dbDatas = trafficAreaManageDao.selectList(query);
        dbDatas.forEach(dbData -> {
            dbData.setOccupySource(null);
            dbData.setOccupyVehicleCodes(String.valueOf(new ArrayList<>()));
            trafficAreaManageDao.updateById(dbData);
        });
    }

    @Override
    public void occupyThirdSystem(String areaCode, String vehicleCode) {
        LambdaQueryWrapper<TrafficAreaManage> deleteWrapper = Wrappers.<TrafficAreaManage>lambdaQuery()
                .eq(TrafficAreaManage::getAreaCode, areaCode)
                .eq(TrafficAreaManage::getAreaType, MapConstant.MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC);
        List<TrafficAreaManage> dbList = trafficAreaManageDao.selectList(deleteWrapper);
        if (CollUtil.isNotEmpty(dbList)) {
            dbList.forEach(db -> trafficAreaManageDao.deleteById(db.getId()));
        }

        TrafficAreaManage trafficAreaManage = new TrafficAreaManage();
        trafficAreaManage.setId(StrUtil.uuid());
        trafficAreaManage.setResourceId(areaCode);
        trafficAreaManage.setAreaCode(areaCode);
        trafficAreaManage.setAreaType(MapConstant.MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC);
        trafficAreaManage.setOccupyVehicleCodes(JSON.toJSONString(Lists.newArrayList(vehicleCode)));
        trafficAreaManageDao.insert(trafficAreaManage);
    }

    @Override
    public void releaseThirdSystem(String areaCode) {
        LambdaUpdateWrapper<TrafficAreaManage> deleteWrapper = Wrappers.<TrafficAreaManage>lambdaUpdate()
                .eq(TrafficAreaManage::getAreaCode, areaCode)
                .eq(TrafficAreaManage::getAreaType, MapConstant.MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC);
        trafficAreaManageDao.delete(deleteWrapper);
    }
}
