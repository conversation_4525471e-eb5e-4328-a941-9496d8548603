package com.youibot.vehicle.scheduler.modules.sys.controller;

import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.licence.annotation.LicenseVerifys;
import com.youibot.vehicle.scheduler.modules.sys.dto.SystemPropertyDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemPropertyEntity;
import com.youibot.vehicle.scheduler.modules.sys.enums.PropertyTypeEnum;
import com.youibot.vehicle.scheduler.modules.sys.service.SystemPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 系统配置
 * @Author：yangpeilin
 * @Date: 2020/5/9 16:42
 */
@RestController("SystemPropertyController")
@RequestMapping("/sys/property")
@Api(value = "系统属性配置", tags = "系统属性配置", produces = "application/json")
public class SystemPropertyController {

    @Autowired
    private SystemPropertyService systemPropertyService;



    @GetMapping("/page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = "type", value = "类型:system:系统类型、user:用户类型", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "category", value = "分类:info、data、map、charge、park", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "propertyKey", value = "属性key", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "valueType", value = "值类型：String,Integer,Double,Long,JSON等", paramType = "query", dataType="String")
    })
    //@RequiresPermissions("property:page")
    @LicenseVerifys
    public Result<PageData<SystemPropertyDTO>> page(@RequestParam Map<String, Object> params){
        PageData<SystemPropertyDTO> page = systemPropertyService.page(params);
        return Result.suc(page);
    }

    @ApiOperation(value = "列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "系统配置记录id", dataType = "Long",paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型:system:系统类型、user:用户类型", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "category", value = "分类:info、data、map、traffic、charge、park", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "propertyKey", value = "属性key", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = "valueType", value = "值类型：String,Integer,Double,Long,JSON等", paramType = "query", dataType="String")
    })
    @GetMapping("/list")
    @ResponseStatus(value = HttpStatus.OK)
    @LicenseVerifys
    //@RequiresPermissions("property:list")
    public Result<List<SystemPropertyDTO>> list(@RequestParam Map<String, Object> searchMap) {
        return Result.suc(systemPropertyService.list(searchMap));
    }

    @ApiOperation(value = "根据id查看系统属性")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "系统配置记录id", required = true, dataType = "Long",paramType = "path")
    })
    @GetMapping("/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    @LicenseVerifys
    //@RequiresPermissions("property:query")
    public Result<SystemPropertyEntity> getSystemProperty(@PathVariable("id") Long id) {
        return Result.suc(systemPropertyService.selectById(id));
    }

    @ApiOperation(value = "查看系统属性配置")
    @GetMapping("/getSystemConfig")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "category", value = "分类:info、dataClean、map、traffic、charge、park等", required = true, dataType = "String")
    })
    @LicenseVerifys
    @ResponseStatus(value = HttpStatus.OK)
    //@RequiresPermissions("property:list")
    public Result<List<SystemPropertyDTO>> getSystemConfig(@RequestParam(required = true)String category) {
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("type", PropertyTypeEnum.SYSTEM.value());
        searchMap.put("category",category);
        List<SystemPropertyDTO> list = systemPropertyService.list(searchMap);
        return Result.suc(list);
    }

    @LogOperation("log.controller.sys.property.batchUpdate")
    @ApiOperation(value = "批量修改系统配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "list", value = "配置集合", required = true, dataType = "SystemPropertyDTO", allowMultiple = true)
    })
    @PutMapping("/batchUpdate")
    @ResponseStatus(value = HttpStatus.OK)
    //@RequiresPermissions("property:update")
    public Result<Boolean> updateSystemConfig(@RequestBody @Validated List<SystemPropertyDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        list.forEach(item->{
            if(StringUtils.isEmpty(item.getPropertyValue())){
                throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
            }
            if (!checkType(item)) {
                throw new FleetException(I18nMessageUtils.getMessage("system.data.type.error"));
            }
        });
        systemPropertyService.updateBatchById(list);
        return Result.suc(true);
    }

    @LogOperation("log.controller.sys.property.update")
    @ApiOperation(value = "修改单个系统配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "系统配置记录id", required = true, dataType = "Long",paramType = "path"),
            @ApiImplicitParam(name = "systemProperty", value = "系统配置信息", required = true, dataType = "SystemPropertyDTO")
    })
    @PutMapping("/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    //@RequiresPermissions("property:update")
    public Result<Boolean> updateSystemConfig(@PathVariable("id") Long id,@RequestBody @Validated SystemPropertyDTO systemProperty) {
        systemProperty.setId(id);
        if (!checkType(systemProperty)) {
            throw new FleetException(I18nMessageUtils.getMessage("system.data.type.error"));
        }
        return Result.suc(systemPropertyService.updateById(systemProperty));
    }

    @LogOperation("log.controller.sys.property.insert")
    @ApiOperation(value = "增加系统属性")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "systemProperty", value = "系统配置信息", required = true, dataType = "SystemPropertyDTO")
    })
    @PostMapping
    @ResponseStatus(value = HttpStatus.OK)
    //@RequiresPermissions("property:save")
    public Result<Boolean> insert(@RequestBody @Validated SystemPropertyDTO systemProperty) {
        if (!checkType(systemProperty)) {
            throw new FleetException(I18nMessageUtils.getMessage("system.data.type.error"));
        }
        return Result.suc(systemPropertyService.insert(systemProperty));
    }

    @LogOperation("log.controller.sys.property.delete")
    @ApiOperation(value = "删除系统属性")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "系统配置记录id", required = true, dataType = "Long",paramType = "path")
    })
    @DeleteMapping("/{id}")
    @ResponseStatus(value = HttpStatus.OK)
    //@RequiresPermissions("property:delete")
    public Result<Boolean> delete(@PathVariable("id") Long id) {
        return Result.suc(systemPropertyService.deleteById(id));
    }

    /**
     * 校验value和参数类型是否匹配
     */
    private static boolean checkType(SystemPropertyDTO systemProperty) {
        if (Objects.equals(systemProperty.getValueType(), "String")) {
            return true;
        }
        if (Objects.equals(systemProperty.getValueType(), "Boolean")) {
            return Objects.equals("true", systemProperty.getPropertyValue()) || Objects.equals("false", systemProperty.getPropertyValue());
        }
        try {
            Class<?> aClass = Class.forName("java.lang." + systemProperty.getValueType());
            Method valueOf = aClass.getDeclaredMethod("valueOf", String.class);
            valueOf.invoke(null, systemProperty.getPropertyValue());
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            return false;
        }
        return true;
    }

}
