package com.youibot.vehicle.scheduler.modules.sys.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.youibot.vehicle.scheduler.common.utils.TreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 角色菜单
 * <AUTHOR>
 */
@Data
public class SysRoleMenuDTO extends TreeNode<SysRoleMenuDTO> {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "上级ID")
    private Long pid;

    @ApiModelProperty(value = "菜单名称")
    private String name;

    @ApiModelProperty(value = "菜单URL")
    private String url;

    @ApiModelProperty(value = "类型  0：菜单   1：按钮")
    private Integer type;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @ApiModelProperty(value = "授权(多个用逗号分隔，如：sys:user:list,sys:user:save)")
    private String permissions;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date createDate;

    @ApiModelProperty(value = "上级菜单名称")
    private String parentName;

    @ApiModelProperty(value = "是否拥有菜单权限")
    private Boolean flag;

}
