package com.youibot.vehicle.scheduler.modules.pda.dto;

import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.DefaultVehicleStatus;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.VehicleTaskDTO;
import com.youibot.vehicle.scheduler.modules.vehicle.entity.VehicleEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: yaoh<PERSON><PERSON>
 * @Date: 2022/12/29/10:18
 * @Description: 机器人详情DTO;PDA的机器人详情数据
 */
@Data
@NoArgsConstructor
@ApiModel(value = "VehicleDetailPdaDTO", description = "PDA上机器人详情")
public class VehicleDetailPdaDTO {
    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "机器人编号")
    private String vehicleCode;
    @ApiModelProperty(value = "机器人名称")
    private String name;
    @ApiModelProperty(value = "机器人IP地址")
    private String ip;
    @ApiModelProperty(value = "调度模式")
    private String scheduleMode;

    @ApiModelProperty(value = "机器人类型")
    private String vehicleTypeName;
    @ApiModelProperty(value = "机器人分组")
    private String vehicleGroupName;

    @ApiModelProperty(value = "机器人总状态")
    private DefaultVehicleStatus defaultVehicleStatus;
    @ApiModelProperty(value = "vehicleMapCode")
    private String vehicleMapCode;
    @ApiModelProperty(value = "连接状态 未连接=disconnect 已连接=connect")
    protected String connectStatus = VehicleConstant.DISCONNECT_STATUS;

    @ApiModelProperty(value = "任务")
    private VehicleTaskDTO task;

    public VehicleDetailPdaDTO(VehicleEntity vehicle) {
        this.id = vehicle.getId();
        this.vehicleCode = vehicle.getVehicleCode();
        this.name = vehicle.getName();
        this.ip = vehicle.getIp();
        this.scheduleMode = vehicle.getScheduleMode();
    }

    public void setVehicle(Vehicle vehicle, VehicleTaskDTO task) {
        if (vehicle != null) {
            this.defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
            this.vehicleMapCode = vehicle.getVehicleMapCode();
            this.connectStatus = vehicle.getConnectStatus();
            this.task = task;
        }
    }

}
