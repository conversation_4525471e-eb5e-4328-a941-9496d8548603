package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.common.page.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "MapElementQueryDTO",description = "地图元素查询对象")
public class MapElementQueryDTO extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    //此元素包含：点位（编号、名称）、电梯、自动门、风淋门、Vehicle

    @ApiModelProperty(value = "地图编码")
    private String vehicleMapCode;

    @ApiModelProperty(value = "定位图code")
    private String locatingCode;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    @ApiModelProperty(value = "是否草稿",required=true)
    private Boolean isDraft;

    @ApiModelProperty(value = "资源类型：点位Marker、电梯Elevator、自动门AutoDoor、风淋门AirShowerDoor、区域MapArea、小车Vehicle")
    private String type;

    @ApiModelProperty(value = "数据条数")
    private Integer size;

}
