package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("vehicle_map")
public class VehicleMap {

    /**
     * 地图编码
     */
    @TableId(type = IdType.INPUT)
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 是否有草稿地图 1：有 0：没有
     */
    private Integer isDraft;

    /**
     * 是否有正式地图 1：有 0：没有
     */
    private Integer isProd;

    /**
     * 地图暂停状态：0:正常，1:暂停中，2:暂停完成
     */
    private Integer pauseStatus;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 编辑时间
     */
    private Date editTime;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 路网md5值
     */
    private String pathMd5;
}
