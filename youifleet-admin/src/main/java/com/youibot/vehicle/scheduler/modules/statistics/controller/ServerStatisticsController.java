package com.youibot.vehicle.scheduler.modules.statistics.controller;

import cn.hutool.core.date.DateUtil;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.statistics.dto.ServerStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.ServerUsageRateDTO;
import com.youibot.vehicle.scheduler.modules.statistics.service.ServerStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/statistics/server")
@Api(tags = "统计-服务器")
public class ServerStatisticsController {

    @Autowired
    private ServerStatisticsService serverStatisticsService;

    @ApiOperation(value = "服务器监控统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String")
    })
    @GetMapping
    public Result<ServerStatisticsDTO> statistics(@RequestParam(value = "startTime") String startTime,
                                                  @RequestParam(value = "endTime") String endTime) {
        return Result.suc(serverStatisticsService.statistics(DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1)));
    }

    @ApiOperation(value = "资源实时使用率")
    @GetMapping("/usageRate")
    public Result<ServerUsageRateDTO> usageRate() {
        return Result.suc(serverStatisticsService.getServerUsageRate());
    }
}
