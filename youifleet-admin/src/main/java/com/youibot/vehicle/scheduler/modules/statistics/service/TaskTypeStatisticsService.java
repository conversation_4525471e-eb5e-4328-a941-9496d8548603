package com.youibot.vehicle.scheduler.modules.statistics.service;

import cn.hutool.core.date.DateTime;
import com.youibot.vehicle.scheduler.common.service.BaseService;
import com.youibot.vehicle.scheduler.modules.statistics.dto.TaskHeatMapDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.TaskStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.TaskSumStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.TaskTypeStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.entity.TaskTypeStatisticsEntity;

import java.util.Date;
import java.util.List;

public interface TaskTypeStatisticsService extends BaseService<TaskTypeStatisticsEntity> {

    TaskTypeStatisticsDTO statistics(String taskTypeCode, Date startTime, Date endTime);

    TaskStatisticsDTO statistics(Date startTime, Date endTime);

    List<TaskHeatMapDTO> heatMap(DateTime startTime, DateTime endTime, String vehicleMapCode);

    List<TaskTypeStatisticsEntity> selectByTaskTypeCodeAndStatisticsDate(String taskTypeCode, Date date1, Date date2);

    List<TaskTypeStatisticsEntity> selectByStatisticsDate(Date date1, Date date2);

    TaskTypeStatisticsEntity getLastStatisticsEntity();

    void createStatisticsData(DateTime statisticsStartTime, DateTime statisticsEndTime);

    void triggerStatistics();

    TaskSumStatisticsDTO getTaskSumStatistics(DateTime startTime, DateTime endTime);
}
