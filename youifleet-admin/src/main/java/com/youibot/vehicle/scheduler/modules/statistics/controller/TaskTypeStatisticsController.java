package com.youibot.vehicle.scheduler.modules.statistics.controller;

import cn.hutool.core.date.DateUtil;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.statistics.dto.TaskTypeStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.service.TaskTypeStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/statistics/taskType")
@Api(tags = "统计-任务类型")
public class TaskTypeStatisticsController {

    @Autowired
    private TaskTypeStatisticsService taskTypeStatisticsService;

    @ApiOperation(value = "任务类型统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String")
    })
    @GetMapping("/{taskTypeCode}")
    public Result<TaskTypeStatisticsDTO> statistics(@PathVariable String taskTypeCode,
                                                    @RequestParam(value = "startTime") String startTime,
                                                    @RequestParam(value = "endTime") String endTime) {
        return Result.suc(taskTypeStatisticsService.statistics(taskTypeCode, DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1)));
    }

}
