package com.youibot.vehicle.scheduler.modules.map.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.dao.BaseDao;
import com.youibot.vehicle.scheduler.common.entity.QueryCol;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

public interface  MapElementService {

    Integer IMPORT_ROADNET_MAP = 0;
    Integer IMPORT_LOCATING_MAP = 1;
    Integer IMPORT_ALL_MAP = 2;

    default <T> List<T> getListByVehicleMapCode(String vehicleMapCode, BaseDao<T> baseDao) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("vehicle_map_code", vehicleMapCode);
        return baseDao.selectList(queryWrapper);
    }

    default <T> void deleteByVehicleMapCode(String vehicleMapCode, BaseDao<T> baseDao) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("vehicle_map_code", vehicleMapCode);
        baseDao.delete(queryWrapper);
    }

    default <T> QueryWrapper<T> getWrapper(Map<String, Object> searchMap) {
        Set<String> set = searchMap.keySet();
        set.remove("isDraft");
        return getWrapper(searchMap, QueryCol.builder().eqCol(String.join(",", set)).build());
    }

    /**
     * 得到查询条件
     * @param params 参数
     * @param col 查询条件
     * @return
     */
    default <T> QueryWrapper<T> getWrapper(Map<String, Object> params, QueryCol col) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();

        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(col.getEqCol())) {
            Stream.of(col.getEqCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                queryWrapper.eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(val), ConvertUtils.camel2under(key), val);
            });
        }

        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(col.getLikeCol())) {
            String[] like = col.getLikeCol().split(",");
            paramsToLike(params, like);
            Stream.of(like).forEach(key -> {
                String val = (String) params.get(key);
                queryWrapper.like(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(val), ConvertUtils.camel2under(key), val);
            });
        }

        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(col.getInCol())) {
            Stream.of(col.getInCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(val)) {
                    queryWrapper.in(ConvertUtils.camel2under(key), val.split(","));
                }
            });
        }

        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(col.getTimeCol())) {
            Stream.of(col.getTimeCol().split(",")).forEach(key -> {
                String val = (String) params.get(key);
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(val)) {
                    String[] split = val.split(",");
                    if (split.length >= 1) {
                        queryWrapper.ge( ConvertUtils.camel2under(key), split[0]);
                    }
                    if (split.length >= 2) {
                        queryWrapper.le(ConvertUtils.camel2under(key), split[1]);
                    }
                }
            });
        }

        return queryWrapper;
    }

    default Map<String, Object> paramsToLike(Map<String, Object> params, String... likes) {
        for (String like : likes) {
            String val = (String) params.get(like);
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(val)) {
                params.put(like, "%" + val + "%");
            } else {
                params.put(like, null);
            }
        }
        return params;
    }

    /**
     * 获取分页对象
     *
     * @param params            分页查询参数
     * @param defaultOrderField 默认排序字段
     * @param isAsc             排序方式
     */
    default <T> IPage<T> getPage(Map<String, Object> params, String defaultOrderField, boolean isAsc) {
        //分页参数
        long curPage = 1;
        long limit = 10;

        if (params.get(Constant.PAGE) != null) {
            curPage = Long.parseLong(params.get(Constant.PAGE).toString());
        }
        if (params.get(Constant.LIMIT) != null) {
            limit = Long.parseLong(params.get(Constant.LIMIT).toString());
        }

        //分页对象
        Page<T> page = new Page<>(curPage, limit);

        //分页参数
        params.put(Constant.PAGE, page);

        //排序字段
        String orderField = (String) params.get(Constant.ORDER_FIELD);
        String order = (String) params.get(Constant.ORDER);

        //前端字段排序
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(orderField) && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(order)) {
            if (Constant.ASC.equalsIgnoreCase(order)) {
                return page.addOrder(OrderItem.asc(ConvertUtils.camel2under(orderField)));
            } else {
                return page.addOrder(OrderItem.desc(ConvertUtils.camel2under(orderField)));
            }
        }

        //没有排序字段，则不排序
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(defaultOrderField)) {
            return page;
        }

        //默认排序
        if (isAsc) {
            page.addOrder(OrderItem.asc(defaultOrderField));
        } else {
            page.addOrder(OrderItem.desc(defaultOrderField));
        }

        return page;
    }

    default  <T> PageData<T> getPageData(List<?> list, long pageNum, long pageSize, long total, Class<T> target) {
        List<T> targetList = ConvertUtils.sourceToTarget(list, target);
        return new PageData<T>((int) pageNum, (int) pageSize, targetList, (int) total);
    }

    default <T> PageData<T> getPageData(IPage page, Class<T> target) {
        return getPageData(page.getRecords(), page.getCurrent(), page.getSize(), page.getTotal(), target);
    }

}
