package com.youibot.vehicle.scheduler.modules.sys.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.AssertUtils;
import com.youibot.vehicle.scheduler.common.validator.ValidatorUtils;
import com.youibot.vehicle.scheduler.common.validator.group.DefaultGroup;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.licence.annotation.LicenseVerifys;
import com.youibot.vehicle.scheduler.modules.security.service.ShiroService;
import com.youibot.vehicle.scheduler.modules.security.user.SecurityUser;
import com.youibot.vehicle.scheduler.modules.security.user.UserDetail;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysMenuDTO;
import com.youibot.vehicle.scheduler.modules.sys.enums.MenuTypeEnum;
import com.youibot.vehicle.scheduler.modules.sys.service.SysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 菜单管理
 */
@RestController
@RequestMapping("/sys/menu")
@Api(tags="菜单管理")
public class SysMenuController {
	@Autowired
	private SysMenuService sysMenuService;
	@Autowired
	private ShiroService shiroService;

	@GetMapping("nav")
	@ApiOperation("导航栏")
	@LicenseVerifys
	public Result<List<SysMenuDTO>> nav(){
		UserDetail user = SecurityUser.getUser();
		List<SysMenuDTO> list = sysMenuService.getUserMenuList(user, MenuTypeEnum.MENU.value());

		return new Result<List<SysMenuDTO>>().ok(list);
	}

	@GetMapping("permissions")
	@ApiOperation("权限标识")
	@LicenseVerifys
	public Result<Set<String>> permissions(){
		UserDetail user = SecurityUser.getUser();
		Set<String> set = shiroService.getUserPermissions(user);

		return new Result<Set<String>>().ok(set);
	}

	@GetMapping("list")
	@ApiOperation("列表")
	@LicenseVerifys
	@ApiImplicitParam(name = "type", value = "菜单类型 0：菜单 1：按钮  null：全部", paramType = "query", dataType="int")
	//@RequiresPermissions("sys:menu:list")
	public Result<List<SysMenuDTO>> list(Integer type){
		List<SysMenuDTO> list = sysMenuService.getAllMenuList(type);

		return new Result<List<SysMenuDTO>>().ok(list);
	}

	@GetMapping("{id}")
	@ApiOperation("信息")
	@LicenseVerifys
	//@RequiresPermissions("sys:menu:info")
	public Result<SysMenuDTO> get(@PathVariable("id") Long id){
		SysMenuDTO data = sysMenuService.get(id);

		return new Result<SysMenuDTO>().ok(data);
	}

	@LogOperation("log.controller.sys.menu.insert")
	@PostMapping
	@ApiOperation("保存")
	//@RequiresPermissions("sys:menu:save")
	public Result save(@RequestBody SysMenuDTO dto){
		//效验数据
		ValidatorUtils.validateEntity(dto, DefaultGroup.class);

		sysMenuService.save(dto);

		return new Result();
	}

	@LogOperation("log.controller.sys.menu.update")
	@PutMapping
	@ApiOperation("修改")
	//@RequiresPermissions("sys:menu:update")
	public Result update(@RequestBody SysMenuDTO dto){
		//效验数据
		ValidatorUtils.validateEntity(dto, DefaultGroup.class);

		sysMenuService.update(dto);

		return new Result();
	}

	@LogOperation("log.controller.sys.menu.delete")
	@DeleteMapping("{id}")
	@ApiOperation("删除")
	//@RequiresPermissions("sys:menu:delete")
	public Result delete(@PathVariable("id") Long id){
		//效验数据
		AssertUtils.isNull(id, "id");

		//判断是否有子菜单或按钮
		List<SysMenuDTO> list = sysMenuService.getListPid(id);
		if(list.size() > 0){
            String message = I18nMessageUtils.getMessage("system.menu.delete.error");
            return new Result().error(message);
		}

		sysMenuService.delete(id);

		return new Result();
	}

	@GetMapping("select")
	@ApiOperation("角色菜单权限")
	@LicenseVerifys
	//@RequiresPermissions("sys:menu:select")
	public Result<List<SysMenuDTO>> select(){
		UserDetail user = SecurityUser.getUser();
		List<SysMenuDTO> list = sysMenuService.getUserMenuList(user, null);

		if (CollectionUtils.isEmpty(list)) {
            String message = I18nMessageUtils.getMessage("system.account.has.no.permission");
            throw new FleetException(message);
		}

		return new Result<List<SysMenuDTO>>().ok(list);
	}
}