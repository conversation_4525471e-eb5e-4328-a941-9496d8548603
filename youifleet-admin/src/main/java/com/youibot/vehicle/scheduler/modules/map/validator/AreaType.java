package com.youibot.vehicle.scheduler.modules.map.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.METHOD,ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = AreaTypeValidator.class)
public @interface AreaType {
    /**
     * 是否允许为空
     */
    boolean required() default true;

    /**
     * 校验不通过返回的提示信息
     */
    String message() default "区域类型 单机区域:SingleAgvArea 显示区域: ShowArea 封控区域：ControlArea 通道区域：ChannelArea 禁旋区域：NoRotatingArea 禁停区域：NoParkingArea 交管区域：TrafficArea 禁入区域：ForbiddenArea";

    /**
     * Constraint要求的属性，用于分组校验和扩展，留空就好
     */
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
