package com.youibot.vehicle.scheduler.modules.sys.dao;

import com.youibot.vehicle.scheduler.modules.sys.entity.SysUserEntity;
import com.youibot.vehicle.scheduler.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 系统用户
 */
@Mapper
public interface SysUserDao extends BaseDao<SysUserEntity> {

	List<SysUserEntity> getList(Map<String, Object> params);

	SysUserEntity getById(Long id);

	SysUserEntity getByUsername(String username);

	int updatePassword(@Param("id") Long id, @Param("newPassword") String newPassword);

	List<String> realNamesByRoleId(Long roleId);

    List<Long> getUserIdsByRealName(String realName);
}