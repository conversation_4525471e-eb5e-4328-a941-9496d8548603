package com.youibot.vehicle.scheduler.modules.map.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yomahub.liteflow.util.CopyOnWriteHashMap;
import com.youibot.vehicle.scheduler.common.constant.RegexConstant;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.AGVPropertiesUtils;
import com.youibot.vehicle.scheduler.common.utils.Base64Utils;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.modules.map.entity.Elevator;
import com.youibot.vehicle.scheduler.modules.map.entity.ElevatorResultData;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.VehicleMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

public class MapFileUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(MapFileUtils.class);

    private static final List<String> keyWords = Arrays.asList("path", "locating", "tmp", "current", "draft", "back");

    public static String LOCATING_PUBLISH_PATH = AGVPropertiesUtils.getString("VEHICLE_MAP.FILE_PATH_LOCATING_PUBLISH");
    public static String LOCATING_DRAFT_PATH = AGVPropertiesUtils.getString("VEHICLE_MAP.FILE_PATH_LOCATING_DRAFT");
    public static String LOCATING_CURRENT_PATH = AGVPropertiesUtils.getString("VEHICLE_MAP.FILE_PATH_LOCATING_CURRENT");

    public static void locatingCurrentToDraft (String vehicleMapCode) throws IOException {
        //判断正式目录下是否有地图的激光文件
        String currentMapPath = LOCATING_CURRENT_PATH + vehicleMapCode + "/";
        File currentMapFile = new File(currentMapPath);
        if (!currentMapFile.exists() || !currentMapFile.isDirectory()) {
            return;
        }
        FileUtils.copyFolder(currentMapPath, LOCATING_DRAFT_PATH);
    }

    public static void locatingDraftToCurrent (String mapCode) throws IOException {
        String draftMapPath = LOCATING_DRAFT_PATH + mapCode + "/";
        String currentMapPath = LOCATING_CURRENT_PATH + mapCode + "/";
        FileUtils.deleteFile(currentMapPath);
        FileUtils.copyFolder(draftMapPath, LOCATING_CURRENT_PATH);
        FileUtils.deleteFile(draftMapPath);
    }

    //校验地图名称是否合法
    public static boolean checkMapCodeValid(String vehicleMapCode) {
        if (StringUtils.isEmpty(vehicleMapCode)) {
            return false;
        }
        if (keyWords.contains(vehicleMapCode.toLowerCase())) {
            return false;
        }
        return RegexConstant.isMatched(vehicleMapCode, RegexConstant.CODE_REGEX);
    }

    public static ElevatorResultData elevatorFileDataToObjectData(String data) {
        ElevatorResultData elevatorResultData = new ElevatorResultData();
        if (StringUtils.isEmpty(data)) {
            return elevatorResultData;
        }
        CopyOnWriteHashMap<String, Object> map = JSONObject.parseObject(data, CopyOnWriteHashMap.class);
        List<Elevator> elevators = JSON.parseArray(JSONObject.toJSONString(map.get("elevator")), Elevator.class);
        if (!CollectionUtils.isEmpty(elevators)) {
            elevatorResultData.setElevator(new CopyOnWriteHashMap(new ConcurrentHashMap(elevators.stream().collect(Collectors.toMap(Elevator::getCode, elevator -> elevator)))));
        }
        return elevatorResultData;
    }

    /**
     * 对字节数组字符串进行Base64解码并生成图片
     *
     * @param base64          图片base64数据
     * @param imageFolderPath 图片存储的文件夹路径
     * @param imageName       图片名称
     */
    public static boolean base64ToImage(String base64, String imageFolderPath, String imageName) {
        if (StringUtils.isEmpty(base64) || StringUtils.isEmpty(imageFolderPath) || StringUtils.isEmpty(imageName)) {
            LOGGER.warn("base64 or imageFolderPath or imageName is empty");
            return false;
        }
        OutputStream out = null;
        try {
            File file = new File(imageFolderPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            //Base64解码
            byte[] b = Base64Utils.base64ToImageBytes(base64);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {//调整异常数据
                    b[i] += 256;
                }
            }
            //生成图片
            out = new FileOutputStream(imageFolderPath + File.separator + imageName);
            out.write(b);
            out.flush();
            return true;
        } catch (Exception e) {
            LOGGER.error("base64 to image error");
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    LOGGER.error("out close error");
                }
            }
        }
        return false;
    }

    /**
     * 系统启动时加载所有地图
     */
    public static void loadAllMap(List<VehicleMap> vehicleMapDTOs) {
        if (CollectionUtils.isEmpty(vehicleMapDTOs)) {
            return;
        }
        vehicleMapDTOs.forEach(vehicleMapDTO -> {
            MapGraphUtil.addAGVMap(vehicleMapDTO.getCode());
        });
    }

    /**
     * 根据页码和每页数据数量获取分页数据
     */
    public static <T> PageData<T> getPageList(List<T> list, Integer pageNum, Integer pageSize) {
        PageData page = new PageData(pageNum, pageSize, list, list.size());
        if (CollectionUtils.isEmpty(list)) {
            return page;
        }
        if (pageSize <= 0) {
            pageSize = 10;
        }

        int maxPageNo = list.size() % pageSize == 0 ? list.size() / pageSize : list.size() / pageSize + 1;
        int realPageNo = pageNum;
        if (pageNum > maxPageNo) {
            realPageNo = maxPageNo;
        } else if (pageNum < 1) {
            realPageNo = 1;
        }

        int fromIdx = (realPageNo - 1) * pageSize;
        int toIdx = Math.min(realPageNo * pageSize, page.getTotal());

        List<T> result = list.subList(fromIdx, toIdx);
        page.setList(result);
        return page;
    }

    public static boolean getGetMethod(Object ob, String property, String value) throws InvocationTargetException, IllegalAccessException {
        Method[] methods = ob.getClass().getMethods();
        String condition = ("get" + property).toLowerCase();
        Method method = Arrays.stream(methods).filter(item -> item.getName().toLowerCase().equals(condition)).findFirst().orElse(null);
        if (method == null) {
            return false;
        }
        Object objects = method.invoke(ob);
        if (value == null && objects == null) {
            return true;
        }
        return value.equals(String.valueOf(objects));
    }

    public static final Map<String, AtomicLong> markerCounterMap = new ConcurrentHashMap<>();
    public static void removeCounter(String mapCode) {
        markerCounterMap.remove(mapCode);
    }
    public static AtomicLong getCounterOfMap(String mapCode, List<Marker> list) {
        AtomicLong markerLong = markerCounterMap.get(mapCode);
        if (markerLong == null) {
            synchronized (markerCounterMap) {
                markerLong = markerCounterMap.get(mapCode);
                if (markerLong == null) {
                    long maxCode = getMaxCode(list);
                    markerLong = new AtomicLong(maxCode);
                    markerCounterMap.put(mapCode, markerLong);
                }
            }
        }
        return markerLong;
    }

    private static int getMaxCode(List<Marker> list) {
        int code = 0;
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            code = list.stream().map(item -> Integer.valueOf(Objects.requireNonNull(CodeFormatUtils.getIntegerCode(item.getCode())))).max(Comparator.comparingInt(a -> a)).orElse(0);
        }
        return code;
    }

}
