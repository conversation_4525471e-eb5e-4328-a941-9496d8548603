package com.youibot.vehicle.scheduler.modules.map.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.youibot.vehicle.scheduler.api.aspect.RateLimiter;
import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.Base64Utils;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.device.utils.ElevatorUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dto.*;
import com.youibot.vehicle.scheduler.modules.map.entity.VehicleMap;
import com.youibot.vehicle.scheduler.modules.map.service.ElevatorService;
import com.youibot.vehicle.scheduler.modules.map.service.UndoManageService;
import com.youibot.vehicle.scheduler.modules.map.service.VehicleMapService;
import com.youibot.vehicle.scheduler.modules.map.utils.MapFileUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.MapPublishUtil;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePool;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.impl.DefaultVehiclePool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController("vehicleMapController")
@RequestMapping(value = "/map/vehicleMaps", produces = "application/json")
@Api(value = "地图", tags = "地图", description = "地图管理功能，用户可以创建和查询所有的地图，并且可能通过地图获取标志，路径，障碍墙，区域等图层信息。")
public class VehicleMapController {

    @Autowired
    private VehicleMapService vehicleMapService;
    @Autowired
    private DefaultVehiclePool defaultVehiclePool;
    @Autowired
    private ElevatorService elevatorService;
    @Autowired
    private UndoManageService undoManageService;
    @Autowired
    private VehiclePool vehiclePool;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "int", paramType = "query"),
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<PageData<VehicleMap>> page(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        PageData<VehicleMap> page = vehicleMapService.page(searchMap);
        return Result.suc(page);
    }

    @ApiOperation(value = "列表(带定位图)")
    @ApiImplicitParam(name = "isDraft", value = "是否草稿", defaultValue = "true", dataType = "Boolean")
    @GetMapping(value = {"", "/getMaplist"})
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<VehicleMapDetailDTO>> searchAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) throws Exception {
        List<VehicleMapDetailDTO> list = vehicleMapService.searchAll(searchMap);
        //设置每个地图的机器人数量
        Map<String, List<Vehicle>> collect = defaultVehiclePool.getAll().stream().filter(i -> Objects.nonNull(i.getVehicleMapCode())).collect(Collectors.groupingBy(Vehicle::getVehicleMapCode));
        list.forEach(map -> map.setVehicleCount(Optional.ofNullable(collect.get(map.getCode())).map(List::size).orElse(0)));
        return Result.suc(list);
    }

    @ApiOperation(value = "列表(不带定位图)")
    @PostMapping("/getAllWithoutMapInfo")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<VehicleMapInfoDTO>> getAllWithoutMapInfo(@RequestBody List<String> vehicleMapCodes){
        List<VehicleMap> allVehicleMap = vehicleMapService.getAllVehicleMap(vehicleMapCodes);
        List<VehicleMapInfoDTO> dtoList = ConvertUtils.sourceToTarget(allVehicleMap, VehicleMapInfoDTO.class);
        Optional.ofNullable(dtoList).ifPresent(list -> list.forEach(this::handleMapPauseStatus));
        return Result.suc(dtoList);
    }

    /**
     * 当地图处于暂停中状态时，统计未暂停机器人数量
     */
    private void handleMapPauseStatus(VehicleMapInfoDTO vehicleMapDto) {
        if (vehicleMapDto != null && MapConstant.MAP_PAUSE_STATUS_PAUSING.equals(vehicleMapDto.getPauseStatus())) {
            String mapCode = vehicleMapDto.getCode();
            List<Vehicle> vehicleList = vehiclePool.getAll().stream().filter(v -> mapCode.equals(v.getVehicleMapCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(vehicleList)) {
                Long count = vehicleList.stream().filter(v -> v.isConnect() && !v.isSoftStopOpen()).count();
                vehicleMapDto.setRunningVehicleNum(count.intValue());
            }
        }
    }

    @LogOperation("log.controller.vehicleMap.insert")
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "VehicleMapDTO", value = "地图", required = true, dataType = "VehicleMapDTO")
    @PostMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result<VehicleMapDTO> insert(@RequestBody @Valid VehicleMapDTO vehicleMapDTO) {
        VehicleMapDTO insert = vehicleMapService.insert(vehicleMapDTO);
        return Result.suc(insert);
    }

    @ApiOperation(value = "编辑地图")
    @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/edit/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result editVehicleMap(@PathVariable("vehicleMapCode") String vehicleMapCode) {
        if(!StringUtils.isEmpty(vehicleMapCode)) {
            //todo 此处加一把同步锁，防止同一个用户的并发请求
            synchronized (vehicleMapCode.intern()) {
                vehicleMapService.editMap(vehicleMapCode);
            }
        }
        return Result.suc();
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿", defaultValue = "true", dataType = "Boolean")
    })
    @GetMapping(value = {"/{vehicleMapCode}","/getMaplist/{vehicleMapCode}"})
    @ResponseStatus(value = HttpStatus.OK)
    public Result<VehicleMapDetailDTO> getDetail(@PathVariable("vehicleMapCode") String vehicleMapCode,
                                           @RequestParam(defaultValue = "true") Boolean isDraft) {
        VehicleMapDetailDTO vehicleMapDTO = vehicleMapService.selectByCode(vehicleMapCode, isDraft);
        if (Objects.isNull(vehicleMapDTO)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", vehicleMapCode);
            throw new FleetException(message);
        }
        Map<String, List<Vehicle>> collect = defaultVehiclePool.getAll().stream().filter(i -> Objects.nonNull(i.getVehicleMapCode())).collect(Collectors.groupingBy(Vehicle::getVehicleMapCode));
        vehicleMapDTO.setVehicleCount(Optional.ofNullable(collect.get(vehicleMapDTO.getCode())).map(List::size).orElse(0));
        return Result.suc(vehicleMapDTO);
    }

    @LogOperation("log.controller.vehicleMap.update")
    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapDTO", value = "地图", required = true, dataType = "VehicleMapDTO")
    })
    @PutMapping(value = "/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<VehicleMapDTO> update(@PathVariable("vehicleMapCode") String vehicleMapCode,
                                        @RequestBody @Valid VehicleMapDTO vehicleMapDTO) {
        vehicleMapDTO.setCode(vehicleMapCode);
        VehicleMapDTO update = this.vehicleMapService.update(vehicleMapDTO);
        return Result.suc(update);
    }

    @LogOperation("log.controller.vehicleMap.delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result delete(@PathVariable("vehicleMapCode") String vehicleMapCode) {
        vehicleMapService.delete(vehicleMapCode);
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.batchDelete")
    @ApiOperation(value = "批量删除")
    @DeleteMapping(value = "/batchDel")
    @ResponseStatus(value = HttpStatus.OK)
    public Result batchDel(@RequestBody List<String> list) {
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(mapCode-> vehicleMapService.delete(mapCode));
        }
        return Result.suc();
    }

    @ApiOperation(value = "查询当前地图是否有草稿数据", notes = "查询当前地图是否有草稿数据")
    @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/existDraft/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Boolean> existDraft(@PathVariable("vehicleMapCode") String vehicleMapCode) {
        return Result.suc(vehicleMapService.existDraft(vehicleMapCode));
    }

    @LogOperation("log.controller.vehicleMap.deleteDraft")
    @ApiOperation(value = "删除草稿数据", notes = "删除当前草稿数据")
    @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/deleteDraftFile/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result deleteDraftFile(@PathVariable("vehicleMapCode") String vehicleMapCode) {
        vehicleMapService.deleteDraftFile(vehicleMapCode);
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.batchGenerateElement")
    @ApiOperation(value = "批量生成点位和路径", notes = "用于批量创建点位和路径")
    @PostMapping("/batchGenerateElements")
    @ResponseStatus(HttpStatus.OK)
    public Result batchGenerate(@RequestBody MarkerAndPathBatchGenerateDTO generateDTO) {
        vehicleMapService.batchGenerate(generateDTO);
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.batchUpdateElement")
    @ApiOperation(value = "修改元素")
    @PutMapping(value = "/updateMapElements/{vehicleMapCode}/{locatingCode}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "elementData", value = "路网数据", dataType = "BatchUpdateElementData", paramType = "body")
    })
    @ResponseStatus(value = HttpStatus.OK)
    public Result batchUpdate(@PathVariable("vehicleMapCode") String vehicleMapCode,
                              @PathVariable("locatingCode") String locatingCode,
                              @RequestBody BatchUpdateElementData elementData) {
        if (elementData == null) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        vehicleMapService.batchUpdate(elementData, vehicleMapCode, locatingCode);
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.batchDeleteElement")
    @ApiOperation(value = "删除元素")
    @PutMapping(value = "/delMapElements/{vehicleMapCode}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "pathResult", value = "路网数据", required = true, dataType = "BatchDeleteElementDTO", paramType = "body")
    })
    @ResponseStatus(value = HttpStatus.OK)
    public Result batchDelete(@PathVariable("vehicleMapCode") String vehicleMapCode,
                              @RequestBody BatchDeleteElementDTO pathResult) {
        if (pathResult == null) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        vehicleMapService.batchDelete(pathResult, vehicleMapCode);
        return Result.suc();
    }

    @ApiOperation(value = "分页查询指定地图上的元素（包含电梯、自动门、风淋门、点位等）")
    @ApiImplicitParam(name = "query", value = "地图元素查询对象", paramType = "body", required = true, dataType = "MapElementQueryDTO")
    @PostMapping(value = "/page/elements")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MapElementDTO>> getElements(@RequestBody MapElementQueryDTO query) {
        //根据地图code，分页查询当前地图上的元素集合
        return Result.suc(vehicleMapService.getElements(query));
    }

    @ApiOperation(value = "地图发布前检查", notes = "地图发布前检查")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
    })
    @GetMapping(value = "/pushMapCheck/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result publishCheck(@PathVariable("vehicleMapCode") String vehicleMapCode) {
        /**
         * 判断是否有地图关联的电梯正在使用
         */
        List<ElevatorDTO> elevatorDTOS = elevatorService.selectElevatorByMapCode(vehicleMapCode);
        if (!CollectionUtils.isEmpty(elevatorDTOS) && ElevatorUtils.isInUsedElevator(elevatorDTOS)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.elevator.publish.check.error");
            return Result.suc(message);
        }
        return Result.suc();
    }

    @RateLimiter(qps = 1)
    @LogOperation("log.controller.vehicleMap.publish")
    @ApiOperation(value = "地图发布", notes = "将草稿数据转为正式数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
    })
    @PostMapping(value = "/pushMapData/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result publish(@PathVariable("vehicleMapCode") String vehicleMapCode) throws Exception {
        if(!StringUtils.isEmpty(vehicleMapCode)){
            //todo 此处加一把同步锁，防止同一个用户的并发请求
            synchronized (vehicleMapCode.intern()) {
                vehicleMapService.publish(vehicleMapCode);
                vehicleMapService.editMap(vehicleMapCode);
            }
        }
        return Result.suc();
    }

    @ApiOperation(value = "定位图详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿", defaultValue = "true", dataType = "Boolean")
    })
    @GetMapping(value = "/{vehicleMapCode}/{locatingCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<MapInfoDTO> getLocatingMapData(@PathVariable("vehicleMapCode") String vehicleMapCode,
                                           @PathVariable("locatingCode") String locatingCode,
                                           @RequestParam(defaultValue = "true") Boolean isDraft) {
        return Result.suc(vehicleMapService.getLocatingMap(vehicleMapCode, locatingCode, isDraft));
    }

    @ApiOperation(value = "定位图列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿", defaultValue = "true", dataType = "Boolean")
    })
    @GetMapping(value = "/{vehicleMapCode}/locatingMaps")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MapInfoDTO>> getLocatingMapList(@PathVariable("vehicleMapCode") String vehicleMapCode,
                                                 @RequestParam(defaultValue = "true") Boolean isDraft) {
        return Result.suc(vehicleMapService.getMapInfoList(vehicleMapCode, isDraft));
    }

    @LogOperation("log.controller.vehicleMap.locatingMap.update")
    @ApiOperation(value = "修改定位图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapInfoDTO", value = "定位图", required = true, dataType = "MapInfoUpdateDTO")
    })
    @PutMapping(value = "/{vehicleMapCode}/{locatingCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<MapInfoDTO> updateLocatingMapData(@PathVariable("vehicleMapCode") String vehicleMapCode,
                                        @PathVariable("locatingCode") String locatingCode,
                                        @RequestBody @Valid MapInfoUpdateDTO mapInfoDTO) {
        mapInfoDTO.setVehicleMapCode(vehicleMapCode);
        mapInfoDTO.setLocatingCode(locatingCode);
        return Result.suc(this.vehicleMapService.updateLocatingMap(mapInfoDTO));
    }

    @LogOperation("log.controller.vehicleMap.locatingMap.import")
    @ApiOperation(value = "导入定位图", notes = "导入地图数据zip文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "multiPartFile", value = "文件", required = true, dataType = "file")
    })
    @PostMapping("/importLocatingMap/{vehicleMapCode}")
    @ResponseStatus(HttpStatus.OK)
    public Result<MapInfoDTO> importLocatingMapData(@PathVariable("vehicleMapCode") String vehicleMapCode,
                                        @RequestBody MultipartFile multiPartFile) throws Exception {
        MapInfoDTO mapInfoDTO = vehicleMapService.importLocatingMap(vehicleMapCode, multiPartFile);
        return Result.suc(mapInfoDTO);
    }

    @GetMapping("/exportLocationMap")
    @ApiOperation(value = "导出定位图数据", notes = "导出定位图数据zip文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿", defaultValue = "true", dataType = "Boolean")
    })
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void exportLocationMap(@RequestParam(value = "vehicleMapCode") String vehicleMapCode,
                                  @RequestParam(value = "locatingCode") String locatingCode,
                                  @RequestParam(defaultValue = "true") Boolean isDraft,
                                  HttpServletResponse response) {
        vehicleMapService.exportLocationMap(vehicleMapCode, locatingCode, isDraft, response);
    }

    @LogOperation("log.controller.vehicleMap.locatingMap.changeDefault")
    @ApiOperation(value = "切换默认定位图", notes = "切换默认定位图")
    @ApiImplicitParam(name = "mapLocationDTO", value = "定位图", required = true, dataType = "MapLocationDTO")
    @PostMapping("/changeDefaultLocatingMap")
    @ResponseStatus(HttpStatus.OK)
    public Result changeDefaultLocatingMapData(@RequestBody MapLocationDTO mapLocationDTO){
        vehicleMapService.changeDefaultLocatingMap(mapLocationDTO);
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.locatingMap.delete")
    @ApiOperation(value = "删除定位图", notes = "删除定位图")
    @ApiImplicitParam(name = "mapLocationDTO", value = "定位图", required = true, dataType = "MapLocationDTO")
    @DeleteMapping("/deleteLocatingMap")
    @ResponseStatus(HttpStatus.OK)
    public Result deleteLocatingMapData(@RequestBody MapLocationDTO mapLocationDTO){
        vehicleMapService.deleteLocatingMap(mapLocationDTO);
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.import")
    @ApiOperation(value = "导入地图", notes = "导入地图数据zip文件")
    @ApiImplicitParam(name = "multiPartFile", value = "文件", required = true, dataType = "file")
    @PostMapping("/importMapData")
    @ResponseStatus(HttpStatus.OK)
    public Result importMapData(@RequestBody MultipartFile multiPartFile) throws Exception {
        vehicleMapService.importMap(multiPartFile);
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.export")
    @ApiOperation(value = "导出地图", notes = "导出地图数据zip文件")
    @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String")
    @GetMapping("/export/{vehicleMapCode}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void export(HttpServletResponse response,
                       @PathVariable("vehicleMapCode") String vehicleMapCode,
                       boolean isDraft) {
        vehicleMapService.export(response, vehicleMapCode, isDraft);
    }

    @LogOperation("log.controller.vehicleMap.copy")
    @ApiOperation(value = "克隆地图", notes = "克隆地图")
    @PostMapping("/copyMap")
    @ResponseStatus(HttpStatus.OK)
    public Result copyMap(@RequestBody @Valid CopyMapDTO copyMapDTO) throws IOException {
        //校验地图名称是否合法
        boolean mapCodeValid = MapFileUtils.checkMapCodeValid(copyMapDTO.getCode());
        if (!mapCodeValid) {
            String message = I18nMessageUtils.getMessage("system.code.format.error", copyMapDTO.getOriginCode());
            return new Result().error(message);
        }
        VehicleMapDetailDTO vehicleMap = vehicleMapService.selectByCode(copyMapDTO.getOriginCode());
        if (vehicleMap == null) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", copyMapDTO.getOriginCode());
            return new Result().error(message);
        }
        if (vehicleMap.getIsProd() == 0) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.is.not.publish.error", copyMapDTO.getOriginCode());
            return new Result().error(message);
        }
        //复制地图
        vehicleMapService.copyMap(copyMapDTO);
        return Result.suc();
    }

    @ApiOperation(value = "获取定位图图片(base64格式)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "locatingCode", value = "定位图code", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否草稿", defaultValue = "true", dataType = "Boolean")
    })
    @GetMapping(value = "/getLocatingMapImage")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<JSONObject> getLocatingMapImage(@RequestParam("vehicleMapCode") String vehicleMapCode,
                                          @RequestParam("locatingCode") String locatingCode,
                                          @RequestParam(defaultValue = "true") Boolean isDraft) {
        String imagePath = isDraft ? MapFileUtils.LOCATING_DRAFT_PATH : MapFileUtils.LOCATING_CURRENT_PATH;
        imagePath = imagePath + File.separator + vehicleMapCode + File.separator + locatingCode + ".png";
        String base64 = Base64Utils.imageToBase64(imagePath);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("image", base64);
        return Result.suc(jsonObject);
    }

    @ApiOperation(value = "undo回退", notes = "地图元素编辑操作回退")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
    })
    @GetMapping(value = "/undo/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result undo(@PathVariable("vehicleMapCode") String vehicleMapCode) throws Exception {
        if (!MapPublishUtil.applyLockForMap(vehicleMapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            undoManageService.undo(vehicleMapCode);
        }finally {
            MapPublishUtil.releaseMap(vehicleMapCode);
        }
        return Result.suc();
    }

    @ApiOperation(value = "redo恢复", notes = "地图元素编辑操作回退后恢复")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
    })
    @GetMapping(value = "/redo/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result redo(@PathVariable("vehicleMapCode") String vehicleMapCode) throws Exception {
        if (!MapPublishUtil.applyLockForMap(vehicleMapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            undoManageService.redo(vehicleMapCode);
        }finally {
            MapPublishUtil.releaseMap(vehicleMapCode);
        }
        return Result.suc();
    }

    @ApiOperation(value = "是否可以undo回退", notes = "是否可以undo回退")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
    })
    @GetMapping(value = "/canUndo/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result canUndo(@PathVariable("vehicleMapCode") String vehicleMapCode) throws Exception {
        if (!undoManageService.canUndo(vehicleMapCode)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.reset.error");
            return Result.suc(message);
        }
        return Result.suc();
    }

    @ApiOperation(value = "是否可以redo恢复", notes = "是否可以redo恢复")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
    })
    @GetMapping(value = "/canRedo/{vehicleMapCode}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result canRedo(@PathVariable("vehicleMapCode") String vehicleMapCode) throws Exception {
        if (!undoManageService.canRedo(vehicleMapCode)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.recover.error");
            return Result.suc(message);
        }
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.pause")
    @ApiOperation(value = "地图全场暂停", notes = "地图全场暂停")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
    })
    @PostMapping("/pauseMap/{vehicleMapCode}")
    @ResponseStatus(HttpStatus.OK)
    public Result pauseMap(@PathVariable String vehicleMapCode) {
        vehicleMapService.pauseMap(vehicleMapCode);
        return Result.suc();
    }

    @LogOperation("log.controller.vehicleMap.recover")
    @ApiOperation(value = "地图全场恢复", notes = "地图全场恢复")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
    })
    @PostMapping("/recoverMap/{vehicleMapCode}")
    @ResponseStatus(HttpStatus.OK)
    public Result recoverMap(@PathVariable String vehicleMapCode) {
        List<VehiclePauseResultDTO> list = vehicleMapService.recoverMap(vehicleMapCode);
        List<String> errorVehicleCodes = list.stream().filter(r -> !r.getResult()).map(VehiclePauseResultDTO::getVehicleCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errorVehicleCodes)) {
            String prefix = "机器人恢复失败：";
            String msg = String.join(",", errorVehicleCodes);
            return new Result<>().error(prefix + msg);
        } else {
            return Result.suc();
        }
    }
}
