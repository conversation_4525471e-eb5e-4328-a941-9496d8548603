package com.youibot.vehicle.scheduler.modules.map.pool;

import com.youibot.vehicle.scheduler.modules.map.dto.EditMapAction;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 地图操作缓存池
 */
@Component
public class EditMapActionPool {

    @Value("${VEHICLE_MAP.EDIT_UNDO_POOL_SIZE}")
    private Integer undoPoolSize;

    private volatile ConcurrentHashMap<String, List<EditMapAction>> undoPool = new ConcurrentHashMap<>();

    private volatile ConcurrentHashMap<String, List<EditMapAction>> redoPool = new ConcurrentHashMap<>();

    public void addUndo(String mapCode, EditMapAction editMapAction) {
        undoPool.computeIfAbsent(mapCode,key -> new ArrayList<EditMapAction>()).add(0, editMapAction);
        if (undoPool.get(mapCode).size() > undoPoolSize) {
            undoPool.get(mapCode).remove(undoPool.get(mapCode).size() - 1);
        }
    }

    public void removeUndo(String mapCode) {
        undoPool.get(mapCode).remove(0);
    }

    public void addRedo(String mapCode, EditMapAction editMapAction) {
        redoPool.computeIfAbsent(mapCode,key -> new ArrayList<EditMapAction>()).add(0, editMapAction);
    }

    public void removeRedo(String mapCode) {
        redoPool.get(mapCode).remove(0);
    }

    public EditMapAction getUndoAction(String mapCode) {
        if (!undoPool.containsKey(mapCode)) {
            return null;
        }
        if (undoPool.get(mapCode).isEmpty()) {
            return null;
        }
        return undoPool.get(mapCode).get(0);
    }

    public EditMapAction getRedoAction(String mapCode) {
        if (!redoPool.containsKey(mapCode)) {
            return null;
        }
        if (redoPool.get(mapCode).isEmpty()) {
            return null;
        }
        return redoPool.get(mapCode).get(0);
    }

    public boolean canUndo(String mapCode) {
        if (!undoPool.containsKey(mapCode)) {
            return false;
        }
        return !undoPool.get(mapCode).isEmpty();
    }

    public boolean canRedo(String mapCode) {
        if (!redoPool.containsKey(mapCode)) {
            return false;
        }
        return !redoPool.get(mapCode).isEmpty();
    }

    public void clearRedo(String mapCode) {
        redoPool.computeIfAbsent(mapCode,key -> new ArrayList<EditMapAction>()).clear();
    }

    public void clear(String mapCode) {
        Optional.ofNullable(undoPool.get(mapCode)).ifPresent(List::clear);
        Optional.ofNullable(redoPool.get(mapCode)).ifPresent(List::clear);
    }
}
