package com.youibot.vehicle.scheduler.modules.tms.dto;

import com.youibot.vehicle.scheduler.modules.map.entity.MapElement;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统版本信息
 * 类名称：SystemVersionDTO
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@EqualsAndHashCode
@Builder
@ApiModel(value = "SystemVersionDTO", description = "系统版本信息")
public class SystemVersionDTO extends MapElement{

    @ApiModelProperty(value = "系统版本号", position = 1)
    private String systemVersion;

}
