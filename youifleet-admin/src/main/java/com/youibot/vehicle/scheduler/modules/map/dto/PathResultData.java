package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.modules.map.entity.*;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

@Data
public class PathResultData {

    private Map<String, Path> paths;

    private Map<String, Marker> markers;

    private Map<String, MapArea> mapAreas;

    private Map<String, AutoDoor> autoDoors;

    private Map<String, AirShowerDoor> airShowerDoors;

    public PathResultData() {
        this.paths = new HashMap<>();
        this.markers = new HashMap<>();
        this.mapAreas = new HashMap<>();
        this.autoDoors = new HashMap<>();
        this.airShowerDoors = new HashMap<>();
    }

    public Map<String, Path> getPaths() {
        if (CollectionUtils.isEmpty(paths)) {
            paths =  new HashMap<>();
        }
        return paths;
    }

    public Map<String, Marker> getMarkers() {
        if (CollectionUtils.isEmpty(markers)) {
            markers = new HashMap<>();
        }
        return markers;
    }

    public Map<String, MapArea> getMapAreas() {
        if (CollectionUtils.isEmpty(mapAreas)) {
            mapAreas =  new HashMap<>();
        }
        return mapAreas;
    }

    public Map<String, AutoDoor> getAutoDoors() {
        if (CollectionUtils.isEmpty(autoDoors)) {
            autoDoors =  new HashMap<>();
        }
        return autoDoors;
    }

    public Map<String, AirShowerDoor> getAirShowerDoors() {
        if (CollectionUtils.isEmpty(airShowerDoors)) {
            airShowerDoors = new HashMap<>();
        }
        return airShowerDoors;
    }
}
