package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 边
 * 类名称：Edge
 * 创建时间：2019年4月2日 下午3:50:30
 */
@Data
@NoArgsConstructor
@ApiModel(value = "ConbinationPathDTO", description = "组合路径")
public class ConbinationPathDTO extends PathDTO {

    @ApiModelProperty(value = "双向路径的共同编码", position = 1)
    private String commonPathCode;

}
