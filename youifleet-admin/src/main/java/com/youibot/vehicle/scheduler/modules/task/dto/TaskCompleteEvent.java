package com.youibot.vehicle.scheduler.modules.task.dto;

import com.youibot.vehicle.scheduler.modules.task.entity.TaskEntity;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
public class TaskCompleteEvent extends ApplicationEvent {

    private TaskEntity task;

    public TaskCompleteEvent(Object source) {
        super(source);
    }

    public TaskCompleteEvent(Object source, TaskEntity task) {
        super(source);
        this.task = task;
    }

    public TaskEntity getTask() {
        return task;
    }
}
