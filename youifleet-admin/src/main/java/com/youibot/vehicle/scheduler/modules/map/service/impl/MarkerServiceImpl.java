package com.youibot.vehicle.scheduler.modules.map.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.BeanUtils;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.MarkerScope;
import com.youibot.vehicle.scheduler.engine.pathplan.service.VehicleScopeService;
import com.youibot.vehicle.scheduler.modules.device.entity.ChargeStationEntity;
import com.youibot.vehicle.scheduler.modules.device.service.ChargeStationService;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.MarkerDao;
import com.youibot.vehicle.scheduler.modules.map.dao.MarkerDraftDao;
import com.youibot.vehicle.scheduler.modules.map.dao.PathDraftDao;
import com.youibot.vehicle.scheduler.modules.map.dto.*;
import com.youibot.vehicle.scheduler.modules.map.entity.*;
import com.youibot.vehicle.scheduler.modules.map.service.*;
import com.youibot.vehicle.scheduler.modules.map.utils.*;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import com.youibot.vehicle.scheduler.modules.map.webSocket.module.SocketMessageModel;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemConfigEntity;
import com.youibot.vehicle.scheduler.modules.sys.service.SystemPropertyService;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePool;
import com.youibot.vehicle.scheduler.modules.warehouse.dto.WarehouseDTO;
import com.youibot.vehicle.scheduler.modules.warehouse.service.WarehouseService;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.youibot.vehicle.scheduler.modules.map.constant.MapConstant.*;

@Service
public class MarkerServiceImpl implements MarkerService, MapElementService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarkerServiceImpl.class);

    @Autowired
    private PathService pathService;
    @Autowired
    private ElevatorService elevatorService;
    @Autowired
    private VehicleScopeService vehicleScopeService;
    @Autowired
    private VehiclePool vehiclePool;
    @Autowired
    private SystemPropertyService systemPropertyService;
    @Autowired
    private MarkerDao markerDao;
    @Autowired
    private MarkerDraftDao markerDraftDao;
    @Autowired
    private PathDraftDao pathDraftDao;
    @Autowired
    private DbUtils dbUtils;
    @Autowired
    private VehicleMapService vehicleMapService;
    @Autowired
    private UndoManageService undoManageService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private ChargeStationService chargeStationService;

    @Override
    public PageData<Marker> page(Map<String, Object> searchMap) {
        PageData<Marker> pageData;
        if (Boolean.parseBoolean(searchMap.get("isDraft").toString())) {
            QueryWrapper<MarkerDraft> queryWrapper = getWrapper(searchMap);
            IPage<MarkerDraft> page = markerDraftDao.selectPage(getPage(searchMap, "code", true), queryWrapper);
            pageData = getPageData(page, Marker.class);
        } else {
            QueryWrapper<Marker> queryWrapper = getWrapper(searchMap);
            IPage<Marker> page = markerDao.selectPage(getPage(searchMap, "code", true), queryWrapper);
            pageData = getPageData(page, Marker.class);
        }
        return pageData;
    }

    @Override
    public List<Marker> searchAll(Map<String, Object> searchMap) {
        List<Marker> list;
        if (Boolean.parseBoolean(searchMap.get("isDraft").toString())) {
            List<MarkerDraft> markerDrafts = markerDraftDao.selectList(getWrapper(searchMap));
            list = ConvertUtils.sourceToTarget(markerDrafts, Marker.class);
        } else {
            list = markerDao.selectList(getWrapper(searchMap));
        }
        return list;
    }

    @Override
    public List<MarkerDTO> searchAllWithLocatingCode(Map<String, Object> searchMap) {
        String locatingCode = (String) searchMap.remove("locatingCode");
        List<Marker> markers = searchAll(searchMap);
        if (CollectionUtils.isEmpty(markers)) {
            return new ArrayList<>();
        }
        return MapUtils.toMarkerDTO(locatingCode, markers);
    }

    @Override
    public List<MarkerWithWarehouseDTO> searchAllWithWarehouse(Map<String, Object> searchMap) {
        String locatingCode = (String) searchMap.remove("locatingCode");
        List<Marker> markers = searchAll(searchMap);
        if (CollectionUtils.isEmpty(markers)) {
            return new ArrayList<>();
        }
        List<MarkerDTO> markerDTOList = MapUtils.toMarkerDTO(locatingCode, markers);
        List<MarkerWithWarehouseDTO> dtoList = ConvertUtils.sourceToTarget(markerDTOList, MarkerWithWarehouseDTO.class);
        this.fillWarehouseDTO(dtoList);
        return dtoList;
    }

    private void fillWarehouseDTO(List<MarkerWithWarehouseDTO> dtoList) {
        List<WarehouseDTO> warehouseList = warehouseService.getWarehouseListWithMarkerCode();
        if (CollUtil.isNotEmpty(warehouseList)) {
            Map<String, List<WarehouseDTO>> markerMap = warehouseList.stream().collect(Collectors.groupingBy(WarehouseDTO::getWorkMarkerCode));
            dtoList.forEach(dto -> {
                List<WarehouseDTO> warehouseEntities = markerMap.get(dto.getCode());
                dto.setWarehouseList(warehouseEntities);
            });
        }
    }

    @Override
    public MarkerDTO insert(MarkerDTO markerDTO) {
        String mapCode = markerDTO.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        Marker marker;
        try {
            //校验点位的充电属性
            checkMarkerChargeProp(markerDTO);
            if (!MARKER_TYPE_CHARGING.equals(markerDTO.getType())) {
                markerDTO.setChargeProp(new MarkerChargeProp());
            }

            List<MapInfoDTO> mapInfoList = vehicleMapService.getMapInfoList(mapCode, true);
            if (CollectionUtils.isEmpty(mapInfoList)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", mapCode);
                throw new FleetException(message);
            }
            marker = ConvertUtils.sourceToTarget(markerDTO, Marker.class);
            marker.setMarkInfos(new ArrayList<>());
            mapInfoList.forEach(map -> {
                MarkerInfo info = MarkerInfo.builder().x(markerDTO.getX()).y(markerDTO.getY()).locatingCode(map.getLocatingCode()).build();
                marker.getMarkInfos().add(info);
            });

            List<Marker> markers = selectByVehicleMapCode(mapCode, true);
            //点位间距判断
            checkMarkerSpacing(markerDTO, markers);

            AtomicLong markerCounter = MapFileUtils.getCounterOfMap(mapCode, markers);
            String newCodeNum = String.valueOf(markerCounter.incrementAndGet());
            marker.setCode(CodeFormatUtils.getFormatCode(mapCode, newCodeNum, CodeFormatUtils.MARKER_CODE_PREFIX));
            if (marker.getParams() == null) {
                marker.setParams(new MarkerParam());
            }
            //默认不是通道点
            marker.setNetworkMarkerType(MapConstant.MARKER_COMMON_TYPE);
            markerDraftDao.insert(ConvertUtils.sourceToTarget(marker, MarkerDraft.class));
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfAdd(mapCode, "Marker", Collections.singletonList(marker));
            undoManageService.pushToUndoPool(mapCode, MapConstant.CREATE_ELEMENTS, null, null, null, ConvertUtils.sourceToTarget(Collections.singletonList(marker), MarkerDraft.class), null, null);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("add marker failed :{}", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.marker.add.error");
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }

        BeanUtils.copyPropertiesIgnoreNull(marker, markerDTO);
        return markerDTO;
    }

    //校验点位的充电属性
    private void checkMarkerChargeProp(MarkerDTO markerDTO) {
        if (MARKER_TYPE_CHARGING.equals(markerDTO.getType())) {
            MarkerChargeProp chargeProp = markerDTO.getChargeProp();
            if (chargeProp == null || chargeProp.getChargeType() == null || chargeProp.getDockingDirection() == null) {
                throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
            }
            if (CHARGE_TYPE_SMART_CHARGE.equals(chargeProp.getChargeType()) && chargeProp.getDockingType() == null) {
                throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
            }
            //如果是关联充电桩,则校验是否存在
            String chargeStationCode = chargeProp.getChargeStationCode();
            if (!StringUtils.isEmpty(chargeStationCode)) {
                ChargeStationEntity station = chargeStationService.getByDeviceCode(chargeStationCode);
                if (station == null) {
                    String message = I18nMessageUtils.getMessage("charge.station.not.exist.error", chargeStationCode);
                    throw new FleetException(message);
                }
                //校验充电桩是否被其他点位绑定
                List<MarkerDraft> markerDrafts = markerDraftDao.selectList(new QueryWrapper<>());
                List<String> bindStationMarkerCodes = markerDrafts.stream()
                        .filter(m -> MARKER_TYPE_CHARGING.equals(m.getType()) && m.getChargeProp() != null && chargeStationCode.equals(m.getChargeProp().getChargeStationCode()))
                        .map(MarkerDraft::getCode)
                        .collect(Collectors.toList());
                Optional.ofNullable(bindStationMarkerCodes).ifPresent(codes -> codes.remove(markerDTO.getCode()));
                if (CollUtil.isNotEmpty(bindStationMarkerCodes)) {
                    String message = I18nMessageUtils.getMessage("charge.station.duplicate.bind.marker.error", chargeStationCode, CollUtil.join(bindStationMarkerCodes, ","));
                    throw new FleetException(message);
                }
            }
        }
    }

    /**
     * 录制点位
     */
    @Override
    public MarkerDTO transcribe(MarkerDTO markerDTO) {
        SystemConfigEntity systemConfig = systemPropertyService.getSystemConfig();
        if (!BooleanUtils.toBoolean(systemConfig.getMarkerSpacingCheck()) || systemConfig.getMarkerSpacing() == null) {
            return insert(markerDTO);
        }

        //在当前定位图上判断
        //如果存在间距判断，则找到间距内最近的一个点位，如果存在则将该点位移动至当前坐标做修改操作，否则做新增操作
        List<Marker> markers = selectByVehicleMapCode(markerDTO.getVehicleMapCode(), true);
        Map<String, Double> collect = markers.stream().collect(Collectors.toMap(Marker::getCode, e -> {
            MarkerInfo info = e.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(markerDTO.getLocatingCode())).findFirst().orElse(null);
            double rate = Double.POSITIVE_INFINITY;
            if (info != null) {
                rate = Math.sqrt(Math.pow(info.getX() - markerDTO.getX(), 2) + Math.pow(info.getY() - markerDTO.getY(), 2));
            }
            return rate;
        }));
        Set<Map.Entry<String, Double>> entries = collect.entrySet();
        entries.removeIf(i -> i.getValue() >= systemConfig.getMarkerSpacing());
        if (entries.isEmpty()) {
            return insert(markerDTO);
        }

        //有最近的点
        List<Map.Entry<String, Double>> entryList = entries.stream().sorted(Map.Entry.comparingByValue()).collect(Collectors.toList());
        String code = entryList.get(0).getKey();
        Marker exist = markers.stream().filter(i -> Objects.equals(code, i.getCode())).findFirst().orElse(null);
        MarkerDTO existDTO = ConvertUtils.sourceToTarget(exist, MarkerDTO.class);
        existDTO.setX(markerDTO.getX());
        existDTO.setY(markerDTO.getY());
        existDTO.setLocatingCode(markerDTO.getLocatingCode());
        return update(existDTO);
    }

    @Override
    public List<Marker> getChargeMarkerList(String vehicleMapCode) {
        LambdaQueryWrapper<Marker> queryWrapper = new LambdaQueryWrapper<Marker>()
                .eq(Marker::getType, MARKER_TYPE_CHARGING)
                .eq(vehicleMapCode != null, Marker::getVehicleMapCode, vehicleMapCode);
        return markerDao.selectList(queryWrapper);
    }

    @Override
    public List<Marker> getParkMarkerList(String vehicleMapCode) {
        LambdaQueryWrapper<Marker> queryWrapper = new LambdaQueryWrapper<Marker>()
                .eq(Marker::getIsPark, 1)
                .eq(vehicleMapCode != null, Marker::getVehicleMapCode, vehicleMapCode);
        return markerDao.selectList(queryWrapper);
    }

    @Override
    public void checkMarkerSpacingByUpdate(String mapCode, String locatingCode, List<MarkerDTO> ms) {
        SystemConfigEntity systemConfig = systemPropertyService.getSystemConfig();
        if (Objects.isNull(systemConfig) || !BooleanUtils.toBoolean(systemConfig.getMarkerSpacingCheck())) {
            return;
        }
        //间距判断
        List<Marker> markerList = selectByVehicleMapCode(mapCode, true);
        List<MarkerDTO> dtoList = MapUtils.toMarkerDTO(locatingCode, markerList);

        Map<String, MarkerDTO> originMarkers = dtoList.stream().collect(Collectors.toMap(MarkerDTO::getCode, Function.identity()));
        Map<String, MarkerDTO> spacingCheckMap = new HashMap<>(originMarkers);
        ms.forEach(m -> spacingCheckMap.put(m.getCode(), m));
        List<MarkerDTO> spacingCheckList = new ArrayList<>(spacingCheckMap.values());
        ms.forEach(m -> {
            try {
                checkMarkerSpacingOfLocatingCode(m, spacingCheckList);
            } catch (Exception e) {
                throw e;
            }
        });
    }

    @Override
    public void checkMarkerSpacing(MarkerDTO markerDTO, List<Marker> markerList) {
        List<MarkerDTO> dtoList = MapUtils.toMarkerDTO(markerDTO.getLocatingCode(), markerList);
        checkMarkerSpacingOfLocatingCode(markerDTO, dtoList);
    }

    /**
     * 在某个定位图上，进行点位间距判断
     */
    public void checkMarkerSpacingOfLocatingCode(MarkerDTO markerDTO, List<MarkerDTO> existList) {
        SystemConfigEntity systemConfig = systemPropertyService.getSystemConfig();
        if (!BooleanUtils.toBoolean(systemConfig.getMarkerSpacingCheck()) || systemConfig.getMarkerSpacing() == null) {
            return;
        }
        existList.forEach(e -> {
            if (!Objects.equals(e.getCode(), markerDTO.getCode())) {
                if (Math.sqrt(Math.pow(e.getX() - markerDTO.getX(), 2) + Math.pow(e.getY() - markerDTO.getY(), 2)) < systemConfig.getMarkerSpacing()) {
                    String message = I18nMessageUtils.getMessage("vehicleMap.marker.spacing.error", systemConfig.getMarkerSpacing());
                    throw new FleetException(message);
                }
            }
        });
    }

    @Override
    public void deleteDraftByVehicleMapCode(String mapCode) {
        deleteByVehicleMapCode(mapCode, markerDraftDao);
    }

    @Override
    @Transactional
    public MarkerDTO update(MarkerDTO markerDTO) {
        String mapCode = markerDTO.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            this.doUpdate(mapCode, markerDTO.getLocatingCode(), Arrays.asList(markerDTO));
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("update marker failed:{}", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.marker.update.error", markerDTO.getCode());
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
        return markerDTO;
    }

    @Override
    public List<MarkerDTO> batchUpdate(List<MarkerDTO> markers) {
        if (CollectionUtils.isEmpty(markers)) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }

        Map<String, List<MarkerDTO>> listMap = markers.stream().collect(Collectors.groupingBy(it -> it.getVehicleMapCode() + "_" + it.getLocatingCode()));
        for (Map.Entry<String, List<MarkerDTO>> entity : listMap.entrySet()) {
            List<MarkerDTO> list = entity.getValue();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            MarkerDTO markerDTO = list.get(0);
            String mapCode = markerDTO.getVehicleMapCode();
            if (!MapPublishUtil.applyLockForMap(mapCode)) {
                throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
            }
            try {
                doUpdate(mapCode, markerDTO.getLocatingCode(), list);
            } catch (FleetException e) {
                LOGGER.error("操作异常", e);
                throw e;
            } catch (Exception e) {
                LOGGER.error("batchUpdate markers failed:{}", e);
                String message = I18nMessageUtils.getMessage("vehicleMap.marker.update.error", list.stream().map(MarkerDTO::getCode).collect(Collectors.joining(",")));
                throw new FleetException(message);
            } finally {
                MapPublishUtil.releaseMap(mapCode);
            }
        }
        return markers;
    }

    /**
     * 修改某个地图上，某个定位图上的数据
     */
    private void doUpdate(String mapCode, String locatingCode, List<MarkerDTO> markerDTOList) {
        //1、批量校验
        for (MarkerDTO marker : markerDTOList) {
            //如果是充电点，需要传入对接参数
            checkMarkerChargeProp(marker);
            if (!MARKER_TYPE_CHARGING.equals(marker.getType())) {
                //默认置空
                marker.setChargeProp(new MarkerChargeProp());
            }
            Marker markerOfDB = selectByCode(mapCode, marker.getCode(), true);
            if (markerOfDB == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.marker.not.exist.error", marker.getCode());
                throw new FleetException(message);
            }
            if (marker.getParams() == null) {
                marker.setParams(new MarkerParam());
            }
        }
        List<MarkerDraft> oriMarkers = markerDraftDao.selectBatchIds(markerDTOList.stream().map(MarkerDTO::getCode).collect(Collectors.toList()));

        //校验点位间距
        checkMarkerSpacingByUpdate(mapCode, locatingCode, markerDTOList);

        List<Path> paths = pathService.selectByVehicleMapCode(mapCode, true);
        List<Marker> markers = selectByVehicleMapCode(mapCode, true);
        Map<String, Marker> markerOfDbMap = markers.stream().collect(Collectors.toMap(Marker::getCode, Function.identity()));
        Map<String, MarkerDTO> markerOfParamMap = markerDTOList.stream().collect(Collectors.toMap(MarkerDTO::getCode, Function.identity()));

        //2、批量获取影响的路径
        Set<Path> bindPaths = new HashSet<>();
        for (MarkerDTO markerDTO : markerDTOList) {
            //修改影响到的数据 path
            List<Path> relateList = paths.stream().filter(path -> path.getEndMarkerCode().equals(markerDTO.getCode()) || path.getStartMarkerCode().equals(markerDTO.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(relateList)) {
                continue;
            }

            for (Path path : relateList) {
                MarkerInfo startMarkerInfo;
                MarkerDTO startMarker = markerOfParamMap.get(path.getStartMarkerCode());
                if (startMarker != null) {
                    startMarkerInfo = MarkerInfo.builder().x(startMarker.getX()).y(startMarker.getY()).locatingCode(startMarker.getLocatingCode()).build();
                } else {
                    Marker marker = markerOfDbMap.get(path.getStartMarkerCode());
                    startMarkerInfo = marker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                }

                MarkerInfo endMarkerInfo;
                MarkerDTO endMarker = markerOfParamMap.get(path.getEndMarkerCode());
                if (endMarker != null) {
                    endMarkerInfo = MarkerInfo.builder().x(endMarker.getX()).y(endMarker.getY()).locatingCode(endMarker.getLocatingCode()).build();
                } else {
                    Marker marker = markerOfDbMap.get(path.getEndMarkerCode());
                    endMarkerInfo = marker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                }

                Optional.ofNullable(path.getPathInfos())
                        .map(list -> list.stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null))
                        .ifPresent(pathInfo -> {
                            pathInfo.setLength(PathUtils.getLength(pathInfo, startMarkerInfo, endMarkerInfo));
                            Double[] inOutAngle = PathUtils.getInOutAngle2(pathInfo, startMarkerInfo, endMarkerInfo);
                            pathInfo.setInRadian(inOutAngle[0]);
                            pathInfo.setOutRadian(inOutAngle[1]);
                        });
            }
            bindPaths.addAll(relateList);
        }

        List<MarkerDraft> markerDrafts = new ArrayList<>();
        markerDTOList.forEach(dto -> {
            MarkerDraft markerDraft = ConvertUtils.sourceToTarget(dto, MarkerDraft.class);
            Marker originMarker = markerOfDbMap.get(dto.getCode());
            markerDraft.setMarkInfos(originMarker.getMarkInfos());
            markerDraft.getMarkInfos().forEach(info -> {
                if (info.getLocatingCode().equals(locatingCode)) {
                    info.setX(dto.getX());
                    info.setY(dto.getY());
                }
            });
            markerDrafts.add(markerDraft);
        });
        dbUtils.updateBatchById(MarkerDraft.class, markerDrafts, 50);

        //获取元素更新前的基本数据，用于存储做回退使用
        List<PathDraft> oriPaths = null;
        List<PathDraft> pathDrafts = null;
        if (!CollectionUtils.isEmpty(bindPaths)) {
            oriPaths = pathDraftDao.selectBatchIds(bindPaths.stream().map(Path::getCode).collect(Collectors.toList()));
            pathDrafts = ConvertUtils.sourceToTarget(bindPaths, PathDraft.class);
            dbUtils.updateBatchById(PathDraft.class, pathDrafts, 50);
        }

        //发送消息到打开当前地图的窗口
        SocketMessageModel messageModel = new SocketMessageModel().update();
        List<Marker> collect = ConvertUtils.sourceToTarget(markerDrafts, Marker.class);
        messageModel.setMarkers(collect);
        MapUpdateSocketController.sendMessage(mapCode, messageModel);
        undoManageService.pushToUndoPool(mapCode, MapConstant.UPDATE_ELEMENTS, oriMarkers, oriPaths, null, markerDrafts, pathDrafts, null);
    }

    /**
     * 查询所有的地图的点位（非草稿）
     */
    @Override
    public List<Marker> getAllWithOutCondition() {
        return markerDao.selectList(new QueryWrapper<>());
    }

    @Override
    public void checkBindElevator(List<Marker> markers) {
        if (CollectionUtils.isEmpty(markers)) {
            return;
        }
        List<ElevatorDTO> elevators = elevatorService.selectList();
        if (CollectionUtils.isEmpty(elevators)) {
            return;
        }
        markers.forEach(marker -> {
            elevators.forEach(elevator -> {
                if (CollectionUtils.isEmpty(elevator.getElevatorToMaps())) {
                    return;
                }
                elevator.getElevatorToMaps().forEach(bind -> {
                    if (Objects.equals(bind.getVehicleMapCode(), marker.getVehicleMapCode()) && bind.getMarkerCode().equals(marker.getCode())) {
                        String message = I18nMessageUtils.getMessage("vehicleMap.marker.already.bind.other.device.error", marker.getCode());
                        throw new FleetException(message);
                    }
                });
            });
        });
    }

    @Override
    public boolean checkBindElevator(String markerCode) {
        if (markerCode == null) {
            return false;
        }
        ElevatorRelateDTO elevatorRelateDTO = elevatorService.selectByMarkerCode(markerCode);
        if (elevatorRelateDTO != null) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public void deleteByCode(String mapCode, String code) {
        boolean applyLockFlag = MapPublishUtil.applyLockForMap(mapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            Marker markerOfFile = selectByCode(mapCode, code, true);
            if (markerOfFile == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.marker.not.exist.error", code);
                throw new FleetException(message);
            }
            List<Path> paths = pathService.selectByVehicleMapCode(mapCode, true);
            checkBindElevator(Collections.singletonList(markerOfFile));
            List<Path> delPaths = paths.stream().filter(path -> path.getEndMarkerCode().equals(code) || path.getStartMarkerCode().equals(code)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delPaths)) {
                //绑定门校验
                pathService.checkBindDoors(delPaths);
            }
            markerDraftDao.deleteById(code);
            if (CollUtil.isNotEmpty(delPaths)) {
                pathDraftDao.deleteBatchIds(delPaths.stream().map(Path::getCode).collect(Collectors.toList()));
            }
            SocketMessageModel messageModel = new SocketMessageModel().delete();
            messageModel.setMarkers(Arrays.asList(markerOfFile));
            if (!CollectionUtils.isEmpty(delPaths)) {
                messageModel.setPaths(delPaths);
            }
            MapUpdateSocketController.sendMessage(mapCode, messageModel);
            undoManageService.pushToUndoPool(mapCode, MapConstant.DELETE_ELEMENTS, ConvertUtils.sourceToTarget(Collections.singletonList(markerOfFile),
                    MarkerDraft.class), ConvertUtils.sourceToTarget(delPaths, PathDraft.class), null, null, null, null);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("delete marker failed :{}", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.marker.delete.error", code);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    /**
     * 获取点位集合，不区分定位图
     */
    @Override
    public List<Marker> selectByCodes(String vehicleMapCode, List<String> codes, boolean isDraft) {
        List<Marker> markers = selectByVehicleMapCode(vehicleMapCode, isDraft);
        return markers.stream().filter(marker -> codes.contains(marker.getCode())).collect(Collectors.toList());
    }

    /**
     * 获取点位集合，区分定位图
     */
    @Override
    public List<MarkerDTO> selectByCodesWithLocatingCode(String vehicleMapCode, String locatingCode, List<String> codes, boolean isDraft) {
        List<Marker> collect = selectByCodes(vehicleMapCode, codes, isDraft);
        if (CollectionUtils.isEmpty(collect)) {
            return new ArrayList<>();
        }
        return MapUtils.toMarkerDTO(locatingCode, collect);
    }

    @Override
    public List<Marker> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft) {
        if (StringUtils.isEmpty(vehicleMapCode)) {
            return Lists.newArrayList();
        }
        List<Marker> markers;
        if (isDraft) {
            List<MarkerDraft> draftList = getListByVehicleMapCode(vehicleMapCode, markerDraftDao);
            markers = ConvertUtils.sourceToTarget(draftList, Marker.class);
        } else {
            markers = getListByVehicleMapCode(vehicleMapCode, markerDao);
        }
        return markers;
    }

    @Override
    public List<Marker> selectByVehicleMapCodes(List<String> vehicleMapCodes, boolean isDraft) {
        List<Marker> result = new ArrayList<>();
        vehicleMapCodes.forEach(vehicleMapCode -> {
            List<Marker> markers = selectByVehicleMapCode(vehicleMapCode, isDraft);
            result.addAll(markers);
        });
        return result;
    }

    @Override
    public Marker selectByCode(String vehicleMapCode, String code, boolean isDraft) {
        if (isDraft) {
            QueryWrapper<MarkerDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("code", code);
            MarkerDraft markerDraft = markerDraftDao.selectOne(queryWrapper);
            return ConvertUtils.sourceToTarget(markerDraft, Marker.class);
        } else {
            QueryWrapper<Marker> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("code", code);
            return markerDao.selectOne(queryWrapper);
        }
    }

    @Override
    public Marker selectByCode(String code) {
        QueryWrapper<Marker> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        return markerDao.selectOne(queryWrapper);
    }

    @Override
    public List<Marker> selectByName(String name) {
        QueryWrapper<Marker> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        return markerDao.selectList(queryWrapper);
    }

    @Override
    public List<Marker> selectRelatePathMarkerIdsByType(String markerCode, String vehicleMapCode, String type, boolean isDraft) {
        List<Path> paths = pathService.selectByVehicleMapCode(vehicleMapCode, isDraft);
        List<Marker> markers = selectByVehicleMapCode(vehicleMapCode, isDraft);
        if (!CollectionUtils.isEmpty(markers)) {
            List<String> startPath = paths.stream().filter(path -> path.getVehicleMapCode().equals(vehicleMapCode) && path.getStartMarkerCode().equals(markerCode))
                    .map(Path::getEndMarkerCode).collect(Collectors.toList());
            List<String> endPath = paths.stream().filter(path -> path.getVehicleMapCode().equals(vehicleMapCode) && path.getEndMarkerCode().equals(markerCode))
                    .map(Path::getStartMarkerCode).collect(Collectors.toList());
            Set<String> markerIds = new HashSet<>();
            markerIds.addAll(startPath);
            markerIds.addAll(endPath);
            if (CollectionUtils.isEmpty(markerIds)) {
                return new ArrayList<>();
            }
            List<Marker> markerList = markers.stream().filter(marker -> markerIds.contains(marker.getCode())).collect(Collectors.toList());
            if (type != null) {
                markerList = markerList.stream().filter(marker -> marker.getType().equals(type)).collect(Collectors.toList());
            }
            return markerList;
        }
        return null;
    }

    @Override
    public void deleteByVehicleMapCode(String vehicleMapCode) {
        deleteByVehicleMapCode(vehicleMapCode, markerDraftDao);
        deleteByVehicleMapCode(vehicleMapCode, markerDao);
        pathService.deleteByVehicleMapCode(vehicleMapCode);
    }

    @Override
    public void deleteByVehicleMapCodes(List<String> vehicleMapCodeList) {
        vehicleMapCodeList.forEach(this::deleteByVehicleMapCode);
    }

    @Override
    public List<Marker> selectEnableChargeMarker() {
        QueryWrapper<Marker> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", MARKER_TYPE_CHARGING);
        return markerDao.selectList(queryWrapper);
    }

    /**
     * 查询所有的可用的未分配的泊车点。
     *
     * @return
     */
    @Override
    public List<Marker> selectEnableParkMarkers() {
        QueryWrapper<Marker> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_park", MARKER_PARK_ATTR_ENABLE);
        return markerDao.selectList(queryWrapper);
    }

    @Override
    public List<MarkerScope> selectMarkersByTypeAndScope(String vehicleMapCode, String agvCode, String type) {
        List<MarkerScope> markerScopes = new ArrayList<>();

        List<Marker> markers = this.selectByVehicleMapCode(vehicleMapCode, false);
        markers = markers.stream().filter(item -> type.equals(item.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(markers)) {
            LOGGER.debug("agvCode:[{}],event:[查询当前地图（{}）上点位，按距离小车的距离从小到大排序，点位（{}）集合为空]", agvCode, vehicleMapCode, type);
            return markerScopes;
        }
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if (vehicle == null) {
            LOGGER.debug("agvCode:[{}],event:[查询当前地图（{}）上点位，按距离小车的距离从小到大排序，找不到对应的小车]", agvCode, vehicleMapCode);
            return markerScopes;
        }

        //根据距离获取点位。
        for (Marker marker : markers) {
            try {
                Double distanceScope = vehicleScopeService.scopeByDistance(marker.getCode(), vehicle);
                if (distanceScope != null) {
                    markerScopes.add(new MarkerScope(marker.getCode(), distanceScope));
                }
            } catch (Exception e) {
                LOGGER.warn("点位打分出错, ", e);
            }
        }
        // 根据距离分数进行排序。降序排序
        if (!CollectionUtils.isEmpty(markerScopes)) {
            markerScopes.sort((o1, o2) -> o2.getScope().compareTo(o1.getScope()));
        }
        return markerScopes;
    }
}
