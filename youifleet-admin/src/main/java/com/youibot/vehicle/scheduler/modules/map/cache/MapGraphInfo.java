package com.youibot.vehicle.scheduler.modules.map.cache;

import com.youibot.vehicle.scheduler.modules.map.dto.PathResultData;
import com.youibot.vehicle.scheduler.modules.map.dto.VehicleMapDetailDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.*;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@Data
@ToString
public class MapGraphInfo {

    private VehicleMapDetailDTO agvMap;

    private Map<String, Path> paths;

    private Map<String, Marker> markers;

    private Map<String, MapArea> mapAreas;

    private Map<String, AutoDoor> autoDoors;

    private Map<String, AirShowerDoor> airShowerDoors;

    public MapGraphInfo(PathResultData pathResultData) {
        this.paths = pathResultData.getPaths();
        this.markers = pathResultData.getMarkers();
        this.mapAreas = pathResultData.getMapAreas();
        this.autoDoors = pathResultData.getAutoDoors();
        this.airShowerDoors = pathResultData.getAirShowerDoors();
    }

    public MapGraphInfo() {
    }
}
