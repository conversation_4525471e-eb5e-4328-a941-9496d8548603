package com.youibot.vehicle.scheduler.modules.map.utils;

import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.modules.map.dto.MarkerDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.PathDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.ReducePathDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.entity.PathDraft;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class MapUtils {

    /**
     * 转换成某个定位图上的点位信息
     */
    public static List<MarkerDTO> toMarkerDTO(String locatingCode, List<Marker> markerList) {
        List<MarkerDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(markerList)) {
            return dtoList;
        }
        markerList.forEach(marker -> dtoList.add(convertToMarkerDTO(locatingCode,marker)));
        return dtoList;
    }

    public static MarkerDTO convertToMarkerDTO(String locatingCode, Marker marker) {
        if (marker == null || StringUtils.isEmpty(locatingCode)) {
            return null;
        }
        MarkerDTO markerDTO = ConvertUtils.sourceToTarget(marker, MarkerDTO.class);
        Optional.ofNullable(marker.getMarkInfos())
                .map(list -> list.stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null))
                .ifPresent(markerDTO::init);
        return markerDTO;
    }


    /**
     * 转换成某个定位图上的路径信息
     */
    public static List<PathDTO> toPathDTO(String locatingCode, List<Path> pathList) {
        List<PathDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(pathList)) {
            return dtoList;
        }
        pathList.forEach(path -> dtoList.add(convertToPathDTO(locatingCode, path)));
        return dtoList;
    }

    public static PathDTO convertToPathDTO(String locatingCode, Path path) {
        if (path == null || StringUtils.isEmpty(locatingCode)) {
            return null;
        }
        PathDTO pathDTO = ConvertUtils.sourceToTarget(path, PathDTO.class);
        Optional.ofNullable(path.getPathInfos())
                .map(list -> list.stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null))
                .ifPresent(pathDTO::init);
        return pathDTO;
    }

    /**
     * 转换成某个定位图上的路径信息
     */
    public static List<ReducePathDTO> toReducePathDTO(String locatingCode, List<Path> pathList) {
        List<ReducePathDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(pathList)) {
            return dtoList;
        }
        pathList.forEach(path -> dtoList.add(convertToReducePathDTO(locatingCode, path)));
        return dtoList;
    }

    public static ReducePathDTO convertToReducePathDTO(String locatingCode, Path path) {
        if (path == null || StringUtils.isEmpty(locatingCode)) {
            return null;
        }
        ReducePathDTO pathDTO = ConvertUtils.sourceToTarget(path, ReducePathDTO.class);
        Optional.ofNullable(path.getPathInfos())
                .map(list -> list.stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null))
                .ifPresent(pathDTO::init);
        return pathDTO;
    }

    /**
     * 转换成某个定位图上的路径信息
     */
    public static List<ReducePathDTO> draftToPathDTO(String locatingCode, List<PathDraft> pathList) {
        List<ReducePathDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(pathList)) {
            return dtoList;
        }
        pathList.forEach(path -> dtoList.add(convertToPathDTO(locatingCode, path)));
        return dtoList;
    }

    public static ReducePathDTO convertToPathDTO(String locatingCode, PathDraft path) {
        if (path == null || StringUtils.isEmpty(locatingCode)) {
            return null;
        }
        ReducePathDTO pathDTO = ConvertUtils.sourceToTarget(path, ReducePathDTO.class);
        Optional.ofNullable(path.getPathInfos())
                .map(list -> list.stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null))
                .ifPresent(pathDTO::init);
        return pathDTO;
    }
}
