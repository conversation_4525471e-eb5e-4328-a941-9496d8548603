package com.youibot.vehicle.scheduler.engine.pathplan.resource.thread;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.youibot.vehicle.scheduler.common.entity.Point;
import com.youibot.vehicle.scheduler.engine.execute.node.entity.PathPlanMessage;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.TrafficResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.entity.*;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.*;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.service.ResourceService;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.util.ResourceUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.service.LocationService;
import com.youibot.vehicle.scheduler.engine.pathplan.traffic.pool.VehicleConflictPool;
import com.youibot.vehicle.scheduler.modules.log.constant.LogConstant;
import com.youibot.vehicle.scheduler.modules.log.dto.SysLogPushDTO;
import com.youibot.vehicle.scheduler.modules.log.util.SysLogUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dto.PublishMap;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.MarkerInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.pool.PublishMapHandleNavigationQueue;
import com.youibot.vehicle.scheduler.modules.map.pool.PublishMapHandleResourceQueue;
import com.youibot.vehicle.scheduler.modules.map.service.MarkerService;
import com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemConfigEntity;
import com.youibot.vehicle.scheduler.modules.sys.service.SystemPropertyService;
import com.youibot.vehicle.scheduler.modules.vehicle.DefaultVehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePool;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePoolService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.TrafficAreaResourcePool.OCCUPY_TYPE_OWN;
import static com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant.*;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2023/3/25 14:08
 */
@Component
public class ResourceManageThread extends Thread {

    private final static Logger logger = LoggerFactory.getLogger(ResourceManageThread.class);

    //资源占用类型
    public static final String RESOURCE_OCCUPY_TYPE_POSITION = "PositionOccupy";//当前位置占用
    public static final String RESOURCE_OCCUPY_TYPE_NAVIGATION = "NavigationOccupy";//导航提前占用

    @Autowired
    private VehiclePool vehiclePool;

    @Autowired
    private MarkerResourcePool markerResourcePool;

    @Autowired
    private ElevatorResourcePool elevatorResourcePool;

    @Autowired
    private SingleAreaResourcePool singleAreaResourcePool;

    @Autowired
    private TrafficAreaResourcePool trafficAreaResourcePool;

    @Autowired
    private ThirdSystemTrafficAreaResourcePool thirdSystemTrafficAreaResourcePool;

    @Autowired
    private NoParkingAreaResourcePool noParkingAreaResourcePool;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private SystemPropertyService systemPropertyService;

    @Autowired
    private LocationService locationService;

    @Autowired
    private PublishMapHandleResourceQueue publishMapHandleResourceQueue;

    @Autowired
    private PublishMapHandleNavigationQueue publishMapHandleNavigationQueue;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private MarkerService markerService;

    @Override
    public void run() {
        Thread.currentThread().setName("resource-manage-thread");
        logger.debug("资源管理线程开启......");
        while (true) {
            try {
                Thread.sleep(10);
                List<Vehicle> vehicles = vehiclePool.getAll();
                /**
                 * 检测并处理地图发布
                 */
                this.checkAndHandlePublishMap(vehicles);
                /**
                 * 清理长期断线机器人的位置信息
                 */
                this.clearPositionByDisconnection(vehicles);
                /**
                 * 根据机器人当前位置占用资源
                 */
                this.occupyResourcesByPosition(vehicles);
                /**
                 * 根据路径规划提前占用资源
                 */
                this.occupyResourcesByNavigation(vehicles);
                /**
                 * 机器人移动, 释放机器人位置占用的资源
                 */
                this.releaseResourcesByPosition(vehicles);
                /**
                 * 撤销路径导航, 将规划占用类型转换成位置占用
                 */
                this.releaseResourcesByNavigation(vehicles);
            } catch (Exception e) {
                logger.error("资源处理失败, ", e);
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.TRAFFIC_MODULE_ERROR).build());
            }
        }
    }

    /**
     * 检测并处理地图发布
     *
     * @param vehicles
     * @return
     * @throws InterruptedException
     */
    private void checkAndHandlePublishMap(List<Vehicle> vehicles) throws InterruptedException {
        while (publishMapHandleResourceQueue.hasNext()) {
            PublishMap publishMap = publishMapHandleResourceQueue.poll();
            if (publishMap == null) {
                break;
            }
            logger.debug("资源处理线程确认发布地图, mapCode:{}", publishMap.getCode());
            publishMap.setStatus(PublishMap.STATUS_CONFIRMED);
            while (true) {
                Thread.sleep(10);
                if (PublishMap.STATUS_PUBLISHED.equals(publishMap.getStatus())) {
                    logger.debug("资源处理线程检测到地图发布完成, 恢复资源占用开始, mapCode:{}", publishMap.getCode());
                    this.resumeResourcesOccupyByPublishMap(publishMap);//路径导航占用资源
                    this.occupyResourcesByPosition(vehicles);//当前位置占用资源
                    publishMapHandleNavigationQueue.add(publishMap);//资源占用恢复完成, 触发导航重规划
                    logger.debug("资源处理线程检测到地图发布完成, 恢复资源占用结束, mapCode:{}", publishMap.getCode());
                    break;
                }
            }
        }
    }

    private void resumeResourcesOccupyByPublishMap(PublishMap publishMap) {
        List<Vehicle> vehicles = vehiclePoolService.getNavigationVehicleByPublishMap(publishMap);
        if (CollectionUtils.isEmpty(vehicles)) {
            return;
        }
        for (Vehicle vehicle : vehicles) {
            PathPlanMessage pathPlanMessage = vehicle.getPathPlanMessage();
            if (pathPlanMessage == null) {
                continue;
            }
            Set<String> occupyMarkerCodes = vehicle.getOccupyPaths().stream().map(Path::getEndMarkerCode).collect(Collectors.toSet());
            boolean occupyResult = resourceService.occupyResourcesByMarkerCodes(vehicle.getVehicleCode(), occupyMarkerCodes, new LinkedList<>(vehicle.getOccupyPaths()), RESOURCE_OCCUPY_TYPE_NAVIGATION, pathPlanMessage);
            if (!occupyResult) {
                logger.error("资源处理线程检测到地图发布完成, 恢复机器人导航资源占用失败, mapCode:{}, vehicleCode:{}, occupyPathCodes:{}", publishMap.getCode(), vehicle.getVehicleCode(), occupyMarkerCodes);
            }
        }
    }

    private void clearPositionByDisconnection(List<Vehicle> vehicles) {
        Integer autoReleaseResource = Optional.ofNullable(systemPropertyService.getSystemConfig())
                .map(SystemConfigEntity::getAutoReleaseResource)
                .orElse(0);
        Integer disconnectionTime = Optional.ofNullable(systemPropertyService.getSystemConfig())
                .map(SystemConfigEntity::getDisconnectionTime)
                .orElse(null);
        if (autoReleaseResource != 1 || disconnectionTime == null) {
            return;
        }
        vehicles.forEach(vehicle -> {
            Long lashPushPositionTime = vehicle.getLashPushPositionTime();
            if (lashPushPositionTime != null
                    && vehicle.getDefaultVehicleStatus() != null
                    && System.currentTimeMillis() - lashPushPositionTime > disconnectionTime * 1000) {
                logger.error("机器人 {} 断网超过{}秒, 自动清理资源", vehicle.getVehicleCode(), disconnectionTime);
                vehicle.setDefaultVehicleStatus(null);

                String data = String.join(",", locationService.getLocationMarkerCodes(vehicle.getVehicleCode()));
                SysLogUtils.pushWarningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY)
                        .content("log.system.resource.vehicle.clear.resource")
                        .data(String.join(",", Arrays.asList(vehicle.getVehicleCode())))
                        .message(data).vehicleCodes(vehicle.getVehicleCode()).build());
                locationService.removeVehicleLocation(vehicle.getVehicleCode());
            }
        });
    }

    /**
     * 撤销路径导航, 将规划占用类型转换成位置占用
     *
     * @param vehicles
     */
    private void releaseResourcesByNavigation(List<Vehicle> vehicles) {

        //获取所有机器人正在执行的点位
        Set<String> travelMarkerCodes = new HashSet<>();
        vehicles.forEach(vehicle -> travelMarkerCodes.addAll(ResourceUtils.getMarkerCodesFromPaths(vehicle.getOccupyPaths())));

        //释放点位资源
        Set<MarkerResource> markerResources = markerResourcePool.getResourcesByOccupyType(RESOURCE_OCCUPY_TYPE_NAVIGATION);
        markerResources.forEach(resource -> {
            Vehicle vehicle = vehiclePool.getVehicle(resource.getOccupyVehicleCode());
            if (!travelMarkerCodes.contains(resource.getMarkerCode())) {
                logger.debug("机器人走完路径, 点位资源导航占用更改为位置占用, vehicleCode:[{}], markerCode:[{}], x-y:[{}]-[{}]", resource.getOccupyVehicleCode(), resource.getMarkerCode(), vehicle.getX(), vehicle.getY());
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
            String pathPlanId = Optional.ofNullable(resource.getOccupyVehicleCode())
                    .map(vehiclePool::getVehicle)
                    .map(Vehicle::getPathPlanMessage)
                    .map(PathPlanMessage::getId)
                    .orElse("");
            if (!pathPlanId.equals(resource.getPathPlanId())) {
                logger.debug("路径导航执行结束, 点位资源导航占用更改为位置占用, vehicleCode:[{}], markerCode:[{}], pathPlanId:[{}]], x-y:[{}]-[{}]", resource.getOccupyVehicleCode(), resource.getMarkerCode(), resource.getPathPlanId(), vehicle.getX(), vehicle.getY());
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
        });

        //释放单机区域资源
        Set<SingleAreaResource> singleAreaResources = singleAreaResourcePool.getResourcesByOccupyType(RESOURCE_OCCUPY_TYPE_NAVIGATION);
        singleAreaResources.forEach(resource -> {
            Vehicle vehicle = vehiclePool.getVehicle(resource.getOccupyVehicleCode());
            if (!CollectionUtils.containsAny(travelMarkerCodes, resource.getMarkerCodes())) {
                logger.debug("机器人走完路径, 单机区域资源导航占用更改为位置占用, vehicleCode:[{}], areaCode:[{}], x-y:[{}]-[{}]", resource.getOccupyVehicleCode(), resource.getId(), vehicle.getX(), vehicle.getY());
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
            String pathPlanId = Optional.ofNullable(resource.getOccupyVehicleCode())
                    .map(vehiclePool::getVehicle)
                    .map(Vehicle::getPathPlanMessage)
                    .map(PathPlanMessage::getId)
                    .orElse("");
            if (!pathPlanId.equals(resource.getPathPlanId())) {
                logger.debug("路径导航执行结束, 单机区域资源导航占用更改为位置占用, vehicleCode:[{}], areaCode:[{}], pathPlanId:[{}], x-y:[{}]-[{}]", resource.getOccupyVehicleCode(), resource.getId(), resource.getPathPlanId(), vehicle.getX(), vehicle.getY());
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
        });

        //释放第三方交管区域资源
        Set<ThirdSystemTrafficAreaResource> thirdSystemAreaResources = thirdSystemTrafficAreaResourcePool.getResourcesByOccupyType(RESOURCE_OCCUPY_TYPE_NAVIGATION);
        thirdSystemAreaResources.forEach(resource -> {
            Vehicle vehicle = vehiclePool.getVehicle(resource.getOccupyVehicleCode());
            if (!CollectionUtils.containsAny(travelMarkerCodes, resource.getMarkerCodes())) {
                logger.debug("机器人走完路径, 第三方交管区域资源导航占用更改为位置占用, vehicleCode:[{}], areaCode:[{}], x-y:[{}]-[{}]", resource.getOccupyVehicleCode(), resource.getId(), vehicle.getX(), vehicle.getY());
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
            String pathPlanId = Optional.ofNullable(resource.getOccupyVehicleCode())
                    .map(vehiclePool::getVehicle)
                    .map(Vehicle::getPathPlanMessage)
                    .map(PathPlanMessage::getId)
                    .orElse("");
            if (!pathPlanId.equals(resource.getPathPlanId())) {
                logger.debug("路径导航执行结束, 第三方交管区域资源导航占用更改为位置占用, vehicleCode:[{}], areaCode:[{}], pathPlanId:[{}], x-y:[{}]-[{}]", resource.getOccupyVehicleCode(), resource.getId(), resource.getPathPlanId(), vehicle.getX(), vehicle.getY());
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
        });

        //释放交管区域资源
        Set<TrafficAreaResource> trafficAreaResources = trafficAreaResourcePool.getResourcesByOccupyType(RESOURCE_OCCUPY_TYPE_NAVIGATION);
        for (TrafficAreaResource resource : trafficAreaResources) {
            if (!Objects.equals(resource.getOccupySource(), OCCUPY_TYPE_OWN)) {
                continue;
            }
            TrafficResource trafficResource = TrafficAreaResourcePool.TRAFFIC_VEHICLE_MANAGE.get(resource.getId());
            Set<String> occupyVehicles = trafficResource.getOccupyVehicleCodes();
            Set<String> cacheVehicles = new HashSet<>(occupyVehicles);
            AtomicInteger counter = new AtomicInteger(0);
            cacheVehicles.forEach(vehicleCode -> {
                Vehicle vehicle = vehiclePool.getVehicle(vehicleCode);
                Set<String> useMarkers = ResourceUtils.getMarkerCodesFromPaths(vehicle.getUsePaths());
                if (!CollectionUtils.containsAny(useMarkers, resource.getMarkerCodes())) {
                    //判断机器人是否停在区域内，不在则释放交管资源
                    Set<String> currentMarkerCodes = ResourceUtils.getCurrentMarkerCodesByPosition(vehicleCode);
                    if (!CollectionUtils.containsAny(currentMarkerCodes, resource.getMarkerCodes())) {
                        logger.debug("交管区域：{}的机器人：{}不在区域内，将释放交管资源", trafficResource.getAreaCode(), vehicle.getVehicleCode());
                        trafficAreaResourcePool.releaseResource(OCCUPY_TYPE_OWN, vehicleCode, resource.getId());
                        counter.getAndIncrement();
                    }
                }
            });
            //如果所有车都没有路径，将资源类型改为位置占用
            if (counter.get() == cacheVehicles.size()) {
                logger.debug("交管区域：{}的所有机器人都没有使用路径，将该交管占用类型为位置占用", trafficResource.getAreaCode());
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
        }

        //释放电梯资源
        Set<ElevatorResource> elevatorResources = elevatorResourcePool.getResourcesByOccupyType(RESOURCE_OCCUPY_TYPE_NAVIGATION);
        elevatorResources.forEach(resource -> {
            String pathPlanId = Optional.ofNullable(resource.getOccupyVehicleCode())
                    .map(vehiclePool::getVehicle)
                    .map(Vehicle::getPathPlanMessage)
                    .map(PathPlanMessage::getId)
                    .orElse("");
            if (!pathPlanId.equals(resource.getPathPlanId())) {
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
            if (!CollectionUtils.containsAny(travelMarkerCodes, resource.getMarkerCodes())) {
                resource.setOccupyType(RESOURCE_OCCUPY_TYPE_POSITION);
            }
        });
    }

    /**
     * 根据路径规划提前占用资源
     *
     * @param vehicles
     */
    private void occupyResourcesByNavigation(List<Vehicle> vehicles) {
        //过滤离线的机器人
        List<Vehicle> onLineVehicles = vehicles.stream().filter(i -> Objects.equals(i.getConnectStatus(), CONNECT_STATUS)).collect(Collectors.toList());
        //打乱机器人申请资源的顺序, 避免极端场景同一台机器人来回避让直至机器人没电关机
        Collections.shuffle(onLineVehicles);
        for (Vehicle vehicle : onLineVehicles) {
            String vehicleCode = vehicle.getVehicleCode();
            PathPlanMessage pathPlanMessage = vehicle.getPathPlanMessage();
            if (pathPlanMessage == null || pathPlanMessage.isCancel()
                    || (CollectionUtils.isEmpty(pathPlanMessage.getPlanedPaths()) && CollectionUtils.isEmpty(pathPlanMessage.getWaitRunPaths()) && CollectionUtils.isEmpty(pathPlanMessage.getRunningPaths()))) {
                //当机器人无导航任务时，需要清空它的等待原因
                vehicle.setWaitReason(null);
            }
            if (pathPlanMessage == null || pathPlanMessage.isCancel() || CollectionUtils.isEmpty(pathPlanMessage.getPlanedPaths())) {
                VehicleConflictPool.remove(vehicleCode);
                continue;
            }
            /**
             * 如果机器人当前位置在封控区域, 不做路径导航资源申请
             */
            boolean inControlArea = ResourceUtils.vehicleIsInControlArea(vehicleCode);
            if (inControlArea) {
                continue;
            }

            LinkedBlockingDeque<Path> planedPaths = pathPlanMessage.getPlanedPaths();
            LinkedBlockingDeque<Path> waitRunPaths = pathPlanMessage.getWaitRunPaths();
            LinkedBlockingDeque<Path> runningPaths = pathPlanMessage.getRunningPaths();
            double earlyOccupyTime = Optional.ofNullable(systemPropertyService.getSystemConfig())
                    .map(SystemConfigEntity::getPathApplyLength)
                    .orElse(3.0);//提前占用时间, 单位s
            /**
             * 正在执行的路径预计执行时间大于提前占用时间, 不进行提前占用申请
             */
            DefaultVehicle defaultVehicle = (DefaultVehicle) vehicle;
            String locatingCode = defaultVehicle.selectLocatingCode();
            if (StringUtils.isBlank(locatingCode)) {
                continue;
            }
            double cost = MapGraphUtil.getCost(waitRunPaths, locatingCode) + MapGraphUtil.getCost(runningPaths, locatingCode);
            if (cost > earlyOccupyTime && !this.isInAisle(planedPaths)) {
                continue;
            }

            //占用最短连续资源，进通道与在禁停区域内时，需多个连续点位要么都占用，要么都不占用
            boolean occupyResult = this.occupyResourceByNavigation(vehicle, planedPaths, waitRunPaths);
            if (!occupyResult) {
                continue;//前方资源占用失败, 停止申请
            }

            //如果本次占用的最后一个点为通道点且不是禁停区域内的点位, 则可不受距离限制, 一次性申请最长连续可用通道点
            boolean lastOccupyIsAisleMarker = Optional.ofNullable(planedPaths.peekFirst())
                    .map(Path::getStartMarkerCode)
                    .map(MapGraphUtil::getMarkerByMarkerCode)
                    .filter(marker -> MapConstant.MARKER_COMMON_TYPE.equals(marker.getNetworkMarkerType()))
                    .isPresent();
            if (lastOccupyIsAisleMarker) {
                while (CollUtil.isNotEmpty(planedPaths)) {
                    boolean nextIsAisleMarker = Optional.ofNullable(planedPaths.getFirst())
                            .map(Path::getEndMarkerCode)
                            .map(MapGraphUtil::getMarkerByMarkerCode)
                            .filter(marker -> MapConstant.MARKER_COMMON_TYPE.equals(marker.getNetworkMarkerType()))
                            .isPresent();
                    if (!nextIsAisleMarker) {//下一个点位不是通道点位, 停止申请
                        break;
                    }

                    boolean nextIsNoParkMarker = Optional.ofNullable(planedPaths.getFirst())
                            .map(Path::getEndMarkerCode)
                            .map(noParkingAreaResourcePool::getResourcesByMarkerCode)
                            .filter(CollUtil::isNotEmpty)
                            .isPresent();
                    if (nextIsNoParkMarker) {//下一个点位属于禁停区域内的点位, 停止申请
                        break;
                    }

                    occupyResult = this.occupyResourceByNavigation(vehicle, planedPaths, waitRunPaths);
                    if (!occupyResult) {//前方资源占用失败, 停止申请
                        break;
                    }
                }
            }
        }
    }

    /**
     * 根据路径导航信息占用资源, 占用最短连续资源（进通道与禁停区域有连续路径必须同时申请的情况）
     *
     * @param vehicle 机器人
     * @param planedPaths 规划路径
     * @param waitRunPaths 待执行路径
     * @return 占用成功
     */
    private boolean occupyResourceByNavigation(Vehicle vehicle, LinkedBlockingDeque<Path> planedPaths, LinkedBlockingDeque<Path> waitRunPaths) {
        if (CollectionUtils.isEmpty(planedPaths)) {
            return false;
        }
        //获取最短申请路径
        LinkedList<Path> applyPaths = this.getShortestApplyPaths(planedPaths);
        //获取申请点位编码
        Set<String> applyMarkerCodes = applyPaths.stream().map(Path::getEndMarkerCode).collect(Collectors.toSet());
        //占用交管区域资源 TODO 要放到下面的 resourceService.occupyResourcesByMarkerCodes 方法内统一处理
        this.occupyTrafficAreaResources(vehicle, new HashSet<>(applyMarkerCodes), RESOURCE_OCCUPY_TYPE_NAVIGATION);
        //占用申请点位的所有类型资源，如点位、单机区域等
        boolean occupyResult = resourceService.occupyResourcesByMarkerCodes(vehicle.getVehicleCode(), new HashSet<>(applyMarkerCodes), applyPaths, RESOURCE_OCCUPY_TYPE_NAVIGATION, vehicle.getPathPlanMessage());
        if (!occupyResult) {
            return false;
        }

        //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请路径资源成功",vehicleCode)).data(String.join(",", applyMarkerCodes)).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());

        //已规划路径转变成待执行路径
        for (int i = 1; i <= applyMarkerCodes.size(); i++) {
            Path path = planedPaths.poll();
            logger.debug("规划路径转换成待执行路径: vehicleCode:[{}], pathCode:[{}], [{}] -> [{}]", vehicle.getVehicleCode(), path.getCode(), path.getStartMarkerCode(), path.getEndMarkerCode());
            waitRunPaths.add(path);
        }
        return true;
    }

    /**
     * 获取必须同时申请的连续路径
     *
     * @param planedPaths 规划路径
     * @return 最短申请路径
     */
    private LinkedList<Path> getShortestApplyPaths(LinkedBlockingDeque<Path> planedPaths) {
        //是否开启进通道时需两个点一起申请的配置
        double channelAvoidance = Optional.ofNullable(systemPropertyService.getSystemConfig()).map(SystemConfigEntity::getChannelAvoidance).orElse(1);
        LinkedList<Path> tempPlanedPaths = new LinkedList<>(planedPaths);
        LinkedList<Path> applyPaths = new LinkedList<>(Collections.singletonList(tempPlanedPaths.getFirst()));
        /**
         * 如果是进入通道, 或者申请点位于禁停区域内, 需要跟下一个点位一起申请
         */
        while (CollUtil.isNotEmpty(tempPlanedPaths)) {
            if ((channelAvoidance == 1 && this.isEnterAisle(tempPlanedPaths)) || this.acrossAreaContinuousApply(tempPlanedPaths)) {
                applyPaths.add(tempPlanedPaths.get(1));
                tempPlanedPaths.poll();
                continue;
            }
            break;
        }
        return applyPaths;
    }

    /**
     * 是否在禁停区域内连续申请
     */
    private boolean acrossAreaContinuousApply(LinkedList<Path> planedPaths) {
        if (planedPaths.size() < 2) {
            return false;
        }
        return Optional.ofNullable(planedPaths.getFirst())
                .map(Path::getEndMarkerCode)
                .map(noParkingAreaResourcePool::getResourcesByMarkerCode)
                .filter(CollUtil::isNotEmpty)
                .isPresent();
    }

    private boolean isInAisle(LinkedBlockingDeque<Path> planedPaths) {
        if (CollectionUtil.isEmpty(planedPaths)) {
            return false;
        }
        //最后占用点是否是通道点
        boolean lastOccupyIsAisleMarker = Optional.ofNullable(planedPaths.getFirst())
                .map(Path::getStartMarkerCode)
                .map(MapGraphUtil::getMarkerByMarkerId)
                .filter(marker -> MapConstant.MARKER_COMMON_TYPE.equals(marker.getNetworkMarkerType()))
                .isPresent();
        //下一个申请点是否为通道点
        boolean nextApplyIsAisleMarker = Optional.ofNullable(planedPaths.getFirst())
                .map(Path::getEndMarkerCode)
                .map(MapGraphUtil::getMarkerByMarkerId)
                .filter(marker -> MapConstant.MARKER_COMMON_TYPE.equals(marker.getNetworkMarkerType()))
                .isPresent();
        return lastOccupyIsAisleMarker && nextApplyIsAisleMarker;
    }

    /**
     * 判断是否为离开通道(普通路网点): 机器人当前申请的点位是交叉路网点，且其上一个点为普通路网点, 视为离开通道(普通路网点)
     *
     * @param planedPaths
     * @return
     */
    private boolean isExitAisle(LinkedList<Path> planedPaths) {
        if (planedPaths.size() < 2) {
            return false;
        }
        boolean currentApplyIsAisleMarker = Optional.ofNullable(planedPaths.getFirst())
                .map(Path::getEndMarkerCode)
                .map(MapGraphUtil::getMarkerByMarkerCode)
                .filter(marker -> MapConstant.MARKER_COMMON_TYPE.equals(marker.getNetworkMarkerType()))
                .isPresent();
        boolean previousOneIsAisleMarker = Optional.ofNullable(planedPaths.getFirst())
                .map(Path::getStartMarkerCode)
                .map(MapGraphUtil::getMarkerByMarkerCode)
                .filter(marker -> MapConstant.MARKER_COMMON_TYPE.equals(marker.getNetworkMarkerType()) && !MapGraphUtil.checkIsElevatorMarker(marker.getCode()))
                .isPresent();
        return !currentApplyIsAisleMarker && previousOneIsAisleMarker;
    }

    /**
     * 判断是否为进入通道(普通路网点): 机器人当前申请的点位是交叉路网点，且其下一个点为普通路网点时, 视为进入通道(普通路网点)
     *
     * @param planedPaths
     * @return
     */
    private boolean isEnterAisle(LinkedList<Path> planedPaths) {
        if (planedPaths.size() < 2) {
            return false;
        }
        boolean currentApplyIsAisleMarker = Optional.ofNullable(planedPaths.getFirst())
                .map(Path::getEndMarkerCode)
                .map(MapGraphUtil::getMarkerByMarkerCode)
                .filter(marker -> MapConstant.MARKER_COMMON_TYPE.equals(marker.getNetworkMarkerType()))
                .isPresent();
        boolean nextOneIsAisleMarker = Optional.ofNullable(planedPaths.get(1))
                .map(Path::getEndMarkerCode)
                .map(MapGraphUtil::getMarkerByMarkerCode)
                .filter(marker -> MapConstant.MARKER_COMMON_TYPE.equals(marker.getNetworkMarkerType()) && !MapGraphUtil.checkIsElevatorMarker(marker.getCode()))
                .isPresent();
        return !currentApplyIsAisleMarker && nextOneIsAisleMarker;
    }

    /**
     * 根据机器人当前位置释放资源
     *
     * @param vehicles
     */
    private void releaseResourcesByPosition(List<Vehicle> vehicles) {
        vehicles.forEach(vehicle -> {
            String vehicleCode = vehicle.getVehicleCode();
            if (!DISCONNECT_STATUS.equalsIgnoreCase(vehicle.getConnectStatus()) && !LOCATED_STATUS.equals(vehicle.getLocationStatus())) {
                return;//状态为未定位的机器人不自动释放资源
            }
            Set<String> currentMarkerCodes = ResourceUtils.getCurrentMarkerCodesByPosition(vehicleCode);

            //释放单机区域资源
            Set<SingleAreaResource> singleAreaResources = singleAreaResourcePool.getResourcesByVehicleCodeAndOccupyType(vehicleCode, RESOURCE_OCCUPY_TYPE_POSITION);
            singleAreaResources.forEach(resource -> {
                if (!CollectionUtils.containsAny(currentMarkerCodes, resource.getMarkerCodes())) {//不存在公共点位
                    logger.debug("释放单机区域资源, vehicleCode:[{}], areaCode:[{}], x-y:[{}]-[{}]", vehicleCode, resource.getId(), vehicle.getX(), vehicle.getY());
                    singleAreaResourcePool.releaseResource(resource.getId());
                    //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]释放位置资源成功",vehicleCode)).data(resource.getId()).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                }
            });

            //释放交管区域资源
            Set<TrafficAreaResource> trafficAreaResourceSet = trafficAreaResourcePool.getTraResourcesByVehicleCodeAndOccupyType(vehicleCode, RESOURCE_OCCUPY_TYPE_POSITION);
            for (TrafficAreaResource resource : trafficAreaResourceSet) {
                if (!Objects.equals(resource.getOccupySource(), OCCUPY_TYPE_OWN)) {
                    continue;
                }
                if (!CollectionUtils.containsAny(currentMarkerCodes, resource.getMarkerCodes())) {//不存在公共点位
                    trafficAreaResourcePool.releaseResource(OCCUPY_TYPE_OWN, vehicleCode, resource.getId());
                    //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]释放位置资源成功",vehicleCode)).data(resource.getId()).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                }
            }

            //释放第三方交管区域资源
            Set<ThirdSystemTrafficAreaResource> thirdSystemResourceSet = thirdSystemTrafficAreaResourcePool.getResourcesByVehicleCodeAndOccupyType(vehicleCode, RESOURCE_OCCUPY_TYPE_POSITION);
            for (ThirdSystemTrafficAreaResource resource : thirdSystemResourceSet) {
                if (!CollectionUtils.containsAny(currentMarkerCodes, resource.getMarkerCodes())) {//不存在公共点位
                    thirdSystemTrafficAreaResourcePool.releaseResource(resource.getId());
                }
            }

            //释放电梯资源
            Set<ElevatorResource> elevatorResources = elevatorResourcePool.getResourcesByVehicleCodeAndOccupyType(vehicleCode, RESOURCE_OCCUPY_TYPE_POSITION);
            elevatorResources.forEach(resource -> {
                if (!CollectionUtils.containsAny(currentMarkerCodes, resource.getMarkerCodes())) {//不存在公共点位
                    logger.debug("释放电梯资源, vehicleCode:[{}], areaCode:[{}], x-y:[{}]-[{}]", vehicleCode, resource.getId(), vehicle.getX(), vehicle.getY());
                    elevatorResourcePool.releaseResource(resource.getId());
                    //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]释放位置资源成功",vehicleCode)).data(resource.getId()).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                }
            });

            //释放点位资源
            if (CollectionUtils.isEmpty(currentMarkerCodes)) {//如果机器人脱轨, 不释放N米范围内的点位资源占用
                currentMarkerCodes.addAll(this.getOccupyMarkerCodesByDerailed(vehicleCode));
            }
            Set<MarkerResource> markerResources = markerResourcePool.getResourcesByVehicleCodeAndOccupyType(vehicleCode, RESOURCE_OCCUPY_TYPE_POSITION);
            markerResources.forEach(resource -> {
                if (!currentMarkerCodes.contains(resource.getMarkerCode())) {//机器人不在该点位资源上了
                    logger.debug("释放点位资源, vehicleCode:[{}], markerCode:[{}], x-y:[{}]-[{}]", vehicleCode, resource.getMarkerCode(), vehicle.getX(), vehicle.getY());
                    markerResourcePool.releaseResource(resource.getId());
                    //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]释放位置资源成功",vehicleCode)).data(resource.getId()).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                }
            });
        });
    }

    /**
     * 根据机器人当前位置占用资源
     *
     * @param vehicles
     */
    private void occupyResourcesByPosition(List<Vehicle> vehicles) {
        vehicles.forEach(vehicle -> {
            String vehicleCode = vehicle.getVehicleCode();
            Set<String> currentMarkerCodes = ResourceUtils.getCurrentMarkerCodesByPosition(vehicleCode);
            if (CollectionUtils.isEmpty(currentMarkerCodes)) {//如果机器人脱轨, 占用N米范围内的点位资源
                /*Position agvPosition = locationService.getAgvPosition(vehicleCode);
                if(CONNECT_STATUS.equals(vehicle.getConnectStatus())) {
                    logger.debug("机器人[{}]脱轨, 当前坐标:[{}]", vehicleCode, agvPosition);
                }*/
                Set<String> derailedRelateMarkerCodes = this.getOccupyMarkerCodesByDerailed(vehicleCode);
                /*if(CONNECT_STATUS.equals(vehicle.getConnectStatus())) {
                    logger.debug("机器人[{}]脱轨, 占用周围的点位：[{}]", vehicleCode, derailedRelateMarkerCodes);
                }*/
                occupyMarkerResources(vehicle, derailedRelateMarkerCodes);
                return;
            }
            //点位资源占用
            occupyMarkerResources(vehicle, currentMarkerCodes);
            //单机区域资源占用
            occupySingleAreaResources(vehicle, currentMarkerCodes);
            //交管区域资源占用
            occupyTrafficAreaResources(vehicle, currentMarkerCodes, RESOURCE_OCCUPY_TYPE_POSITION);
            //第三方交管区域资源占用
            occupyThirdSystemTrafficAreaResources(vehicle, currentMarkerCodes, RESOURCE_OCCUPY_TYPE_POSITION);
            //电梯资源占用
            occupyElevatorResources(vehicle, currentMarkerCodes);
        });
    }

    private void occupyElevatorResources(Vehicle vehicle, Set<String> applyMarkerCodes) {
        String vehicleCode = vehicle.getVehicleCode();
        Set<String> travelEndMarkerCodes = vehicle.getOccupyPaths().stream().map(Path::getEndMarkerCode).collect(Collectors.toSet());
        applyMarkerCodes.forEach(applyMarkerCode -> {
            Set<String> resourceIds = elevatorResourcePool.getResourceIdsByMarkerCode(applyMarkerCode);
            resourceIds.forEach(resourceId -> {
                boolean available = elevatorResourcePool.verifyResourceIsAvailable(vehicleCode, resourceId);
                if (available) {
                    ElevatorResource elevatorResource = elevatorResourcePool.get(resourceId);
                    if (elevatorResource != null
                            && RESOURCE_OCCUPY_TYPE_NAVIGATION.equals(elevatorResource.getOccupyType())
                            && CollectionUtils.containsAny(travelEndMarkerCodes, elevatorResource.getMarkerCodes())) {
                        return;//如果该位置属于导航占用, 且机器人还未行驶到该点, 则保留该占用未导航占用
                    }
                    elevatorResourcePool.occupyResource(vehicleCode, RESOURCE_OCCUPY_TYPE_POSITION, resourceId);
                    //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请位置资源成功",vehicleCode)).data(resourceId).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                } else {
                    NoticeMessage noticeMessage = NoticeMessage.builder()
                            .code(NoticeCodeConstant.ELEVATOR_RESOURCE_OCCUPY_FAIL)
                            .placeholders(Arrays.asList(vehicleCode, resourceId))
                            .vehicleCode(vehicleCode).build();
                    NoticeMessageUtils.pushQueue(noticeMessage);
                    //SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请位置资源失败",vehicleCode)).data(resourceId).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                }
            });
        });
    }

    private void occupySingleAreaResources(Vehicle vehicle, Set<String> applyMarkerCodes) {
        String vehicleCode = vehicle.getVehicleCode();
        Set<String> travelEndMarkerCodes = vehicle.getOccupyPaths().stream().map(Path::getEndMarkerCode).collect(Collectors.toSet());
        applyMarkerCodes.forEach(applyMarkerCode -> {
            //一个点位可能占用多个单机区域, 需要逐个申请占用
            Set<String> resourceIds = singleAreaResourcePool.getResourceIdsByMarkerCode(applyMarkerCode);
            resourceIds.forEach(resourceId -> {
                boolean available = singleAreaResourcePool.verifyResourceIsAvailable(vehicleCode, resourceId);
                if (available) {
                    SingleAreaResource singleAreaResource = singleAreaResourcePool.get(resourceId);
                    if (singleAreaResource != null
                            && RESOURCE_OCCUPY_TYPE_NAVIGATION.equals(singleAreaResource.getOccupyType())
                            && CollectionUtils.containsAny(travelEndMarkerCodes, singleAreaResource.getMarkerCodes())) {
                        return;//如果该位置属于导航占用, 且机器人还未行驶到该点, 则保留该占用未导航占用
                    }
                    singleAreaResourcePool.occupyResource(vehicleCode, RESOURCE_OCCUPY_TYPE_POSITION, resourceId);
                    //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请位置资源成功",vehicleCode)).data(resourceId).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                } else {
                    NoticeMessage noticeMessage = NoticeMessage.builder()
                            .code(NoticeCodeConstant.SINGLE_AREA_RESOURCE_OCCUPY_FAIL)
                            .placeholders(Arrays.asList(vehicleCode, resourceId))
                            .vehicleCode(vehicleCode).build();
                    NoticeMessageUtils.pushQueue(noticeMessage);
                    //SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请位置资源失败",vehicleCode)).data(resourceId).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                }
            });
        });
    }

    private void occupyTrafficAreaResources(Vehicle vehicle, Set<String> applyMarkerCodes, String type) {
        String vehicleCode = vehicle.getVehicleCode();
        Set<String> travelEndMarkerCodes = vehicle.getOccupyPaths().stream().map(Path::getEndMarkerCode).collect(Collectors.toSet());
        applyMarkerCodes.forEach(applyMarkerCode -> {
            //一个点位可能占用多个交管区域, 需要逐个申请占用
            Set<String> resourceIds = trafficAreaResourcePool.getResourceIdsByMarkerCode(applyMarkerCode);
            resourceIds.forEach(resourceId -> {
                synchronized (resourceId.intern()) {
                    boolean available = trafficAreaResourcePool.verifyTrafficResourcesAreAvailable(OCCUPY_TYPE_OWN, resourceId);
                    if (available) {
                        TrafficAreaResource trafficAreaResource = trafficAreaResourcePool.get(resourceId);
                        if (trafficAreaResource != null
                                && RESOURCE_OCCUPY_TYPE_NAVIGATION.equals(trafficAreaResource.getOccupyType())
                                && CollectionUtils.containsAny(travelEndMarkerCodes, trafficAreaResource.getMarkerCodes())) {
                            //如果该位置属于导航占用, 且机器人还未行驶到该点, 则保留该占用为导航占用
//                            logger.debug("机器人：{}提前申请交管区域：{}", vehicleCode, resourceId);
//                            return;
                        }
                        //允许直接申请 根据条件得到申请结果
                        trafficAreaResourcePool.occupyTrafficResource(vehicleCode, OCCUPY_TYPE_OWN, type, resourceId, null);
                        //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请位置资源成功",vehicleCode)).data(resourceId).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                    } else {
//                        NoticeMessage noticeMessage = NoticeMessage.builder()
//                                .code(NoticeCodeConstant.SINGLE_AREA_RESOURCE_OCCUPY_FAIL)
//                                .placeholders(Arrays.asList(vehicleCode, resourceId))
//                                .vehicleCode(vehicleCode).build();
//                        NoticeMessageUtils.pushQueue(noticeMessage);
                        //SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请位置资源失败",vehicleCode)).data(resourceId).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
                    }
                }
            });
        });
    }

    private void occupyThirdSystemTrafficAreaResources(Vehicle vehicle, Set<String> applyMarkerCodes, String type) {
        String vehicleCode = vehicle.getVehicleCode();
        applyMarkerCodes.forEach(applyMarkerCode -> {
            //一个点位可能占用多个交管区域, 需要逐个申请占用
            Set<String> resourceIds = thirdSystemTrafficAreaResourcePool.getResourceIdsByMarkerCode(applyMarkerCode);
            resourceIds.forEach(resourceId -> {
                ThirdSystemTrafficAreaResource resource = thirdSystemTrafficAreaResourcePool.get(resourceId);
                if (resource != null && vehicleCode.equals(resource.getOccupyVehicleCode())) {
                    return;
                }
                boolean available = thirdSystemTrafficAreaResourcePool.verifyResourceIsAvailable(vehicleCode, resourceId);
                if (available) {
                    thirdSystemTrafficAreaResourcePool.occupyResource(vehicleCode, type, resourceId, null);
                }else {
                    NoticeMessage noticeMessage = NoticeMessage.builder()
                            .code(NoticeCodeConstant.SINGLE_AREA_RESOURCE_OCCUPY_FAIL)
                            .placeholders(Arrays.asList(vehicleCode, resourceId))
                            .vehicleCode(vehicleCode).build();
                    NoticeMessageUtils.pushQueue(noticeMessage);
                }
            });
        });
    }

    private void occupyMarkerResources(Vehicle vehicle, Set<String> applyMarkerCodes) {
        Set<String> travelEndMarkerCodes = vehicle.getOccupyPaths().stream().map(Path::getEndMarkerCode).collect(Collectors.toSet());
        String vehicleCode = vehicle.getVehicleCode();
        applyMarkerCodes.forEach(applyMarkerCode -> {
            boolean available = markerResourcePool.verifyResourceIsAvailable(vehicleCode, applyMarkerCode);
            if (available) {
                MarkerResource markerResource = markerResourcePool.get(applyMarkerCode);
                if (markerResource != null && RESOURCE_OCCUPY_TYPE_NAVIGATION.equals(markerResource.getOccupyType())) {
                    if (travelEndMarkerCodes.contains(markerResource.getMarkerCode())) {
                        return;//如果该位置属于导航占用, 且机器人还未行驶到该点, 则保留该占用未导航占用
                    } else {
                        logger.debug("机器人走完路径, 点位资源导航占用更改为位置占用, vehicleCode:[{}], markerCode:[{}], x-y:[{}]-[{}]", markerResource.getOccupyVehicleCode(), markerResource.getMarkerCode(), vehicle.getX(), vehicle.getY());
                    }
                }
                markerResourcePool.occupyResource(vehicleCode, RESOURCE_OCCUPY_TYPE_POSITION, applyMarkerCode);
                //SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请位置资源成功",vehicleCode)).data(applyMarkerCode).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
            } else {
//                String vehicleMapCode = Optional.ofNullable(MapGraphUtil.getMarkerByMarkerCode(applyMarkerCode)).map(Marker::getVehicleMapCode).orElse(null);
//                NoticeMessage noticeMessage = NoticeMessage.builder()
//                        .code(NoticeCodeConstant.MARKER_RESOURCE_OCCUPY_FAIL)
//                        .placeholders(Arrays.asList(vehicleCode, applyMarkerCode))
//                        .vehicleCode(vehicleCode)
//                        .mapName(vehicleMapCode).build();
//                NoticeMessageUtils.pushQueue(noticeMessage);
                //SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_RESOURCE_APPLY).content(String.format("机器人[%s]申请位置资源失败",vehicleCode)).data(applyMarkerCode).vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).nodeCode(Optional.ofNullable(vehicle.getPathPlanMessage()).map(PathPlanMessage::getNodeCode).orElse(null)).build());
            }
        });
    }

    private Set<String> getOccupyMarkerCodesByDerailed(String vehicleCode) {
        Set<String> markerCodes = new HashSet<>();
        DefaultVehicle vehicle = (DefaultVehicle) vehiclePool.getVehicle(vehicleCode);
        if (vehicle == null) {
            return markerCodes;
        }
        Double x = vehicle.getX();
        Double y = vehicle.getY();
        if (x == null || y == null) {
            return markerCodes;
        }
        Double occupyResourceRange = Optional.ofNullable(systemPropertyService.getSystemConfig())
                .map(SystemConfigEntity::getOccupyResourceRange)
                .orElse(0.0);
        List<Marker> markers = Optional.ofNullable(vehicle.getVehicleMapCode())
                .map(MapGraphUtil::getMarkersByMapCode)
                .orElse(new ArrayList<>());
        try {
            String locatingCode = vehicle.selectLocatingCode();
            markers.forEach(marker -> {
                MarkerInfo markerInfo = marker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                if (markerInfo != null) {
                    Double norm = new Point(markerInfo.getX() - x, markerInfo.getY() - y).getNorm();
                    if (norm < occupyResourceRange) {
                        markerCodes.add(marker.getCode());
                    }
                }
            });
        } catch (Exception e) {
            logger.error("获取机器人当前地图的定位编码失败, ", e);
        }
        return markerCodes;
    }
}
