package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.modules.map.entity.AirShowerDoor;
import com.youibot.vehicle.scheduler.modules.map.entity.AutoDoor;
import com.youibot.vehicle.scheduler.modules.map.entity.MapArea;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "BatchUpdateElementData",description = "批量更新")
public class BatchUpdateElementData {

    @ApiModelProperty(value = "点位集合", position = 1)
    private List<MarkerDTO> markers;

    @ApiModelProperty(value = "路径集合", position = 2)
    private List<BatchUpdatePathDTO> paths;

    @ApiModelProperty(value = "区域集合", position = 3)
    private List<MapArea> mapAreas;

    @ApiModelProperty(value = "自动门集合", position = 4)
    private List<AutoDoor> autoDoors;

    @ApiModelProperty(value = "风淋门集合", position = 5)
    private List<AirShowerDoor> airShowerDoors;
}
