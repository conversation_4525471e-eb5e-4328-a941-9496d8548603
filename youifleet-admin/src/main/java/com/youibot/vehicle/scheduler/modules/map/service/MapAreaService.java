package com.youibot.vehicle.scheduler.modules.map.service;


import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.modules.map.entity.MapArea;

import java.util.List;
import java.util.Map;

public interface MapAreaService {

    PageData<MapArea> findPage(Map<String, Object> searchMap, boolean isDraft);

    List<MapArea> searchAll(Map<String, Object> searchMap, boolean isDraft);

    List<MapArea> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft);

    List<MapArea> selectByVehicleMapCodeAndType(String vehicleMapCode, String areaTypeId, boolean isDraft);


    MapArea selectByCode(String vehicleMapCode, String code, boolean isDraft);

    MapArea selectByCode(String code);

    List<MapArea> selectByCodes(String vehicleMapCode, List<String> codes, boolean isDraft);

    MapArea insert(MapArea mapArea);

    MapArea update(MapArea mapArea);

    List<MapArea> batchUpdate(List<MapArea> mapAreas);

    boolean deleteByCode(String vehicleMapCode, String code);

    void deleteByVehicleMapCode(String vehicleMapCode);

    void deleteDraftByVehicleMapCode(String mapCode);

    void enable(String vehicleMapCode, String code);

    void disable(String vehicleMapCode, String code);

}
