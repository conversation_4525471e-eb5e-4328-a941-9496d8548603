package com.youibot.vehicle.scheduler.modules.task.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.youibot.vehicle.scheduler.modules.notice.entity.NoticeRecordEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * 任务详情
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "TaskDetailDTO",description = "任务详情")
public class TaskDetailDTO {

    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "任务类型ID")
    private Long taskTypeId;

    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "任务状态")
    private String status;

    @ApiModelProperty(value = "输入参数")
    private Map<String, Object> paramIn;

    @ApiModelProperty(value = "输出参数")
    private Map<String, Object> paramOut;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "执行时长(秒)")
    private Integer executionDuration;

    @ApiModelProperty(value = "错误信息")
    private NoticeRecordEntity noticeRecord;

    @ApiModelProperty(value = "节点详情")
    private Map<String, TaskNodeDetailDTO> nodeDetail;

}
