package com.youibot.vehicle.scheduler.modules.map.pool;

import com.youibot.vehicle.scheduler.common.utils.SpringContextUtils;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.service.PathService;
import com.youibot.vehicle.scheduler.modules.security.user.SecurityUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2023年04月20日 15:40
 * @ClassName: MarkerLockUtil
 * @Description: 点位锁工具类
 */
public class MarkerLockUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarkerLockUtil.class);

    private static PathService pathService = SpringContextUtils.getBean(PathService.class);
    /**
     * 点位资源锁，用于避免更新点位、路径、自动门、风淋门时，多线程冲突引发数据错误
     *
     * 当并发修改点位、路径、自动门、风淋门时，
     * 由于都与点位有关（自动门关联的路径，本质也是点位），
     * 所以，在接口并发编辑的时候，需要添加一个点位资源锁
     *
     * 如果修改点位、路径、自动门、风淋门时，同时关联了同一个点位，只有一个请求可以成功
     */
    public static final Map<String,String> MARKER_POOL = new ConcurrentHashMap<>();




    public static boolean apply(String mapCode, List<String> resourceIds){
        return applyUntilSuccess(mapCode,resourceIds);
    }

    public static boolean applyOnce(String mapCode, List<String> resourceIds){
        Long userId = SecurityUser.getUserId();
        return apply(String.valueOf(userId),mapCode,resourceIds);
    }

    public static boolean applyFixTime(String mapCode, List<String> resourceIds,int num){
        Long userId = SecurityUser.getUserId();
        int idx = 0;
        boolean flag;
        while(!(flag = apply(String.valueOf(userId),mapCode,resourceIds)) && idx<num){
            try {
                TimeUnit.MILLISECONDS.sleep(200);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            idx++;
        }
        return flag;
    }

    public static boolean applyUntilSuccess(String mapCode, List<String> resourceIds){
        Long userId = SecurityUser.getUserId();
        boolean flag;
        while(!(flag = apply(String.valueOf(userId),mapCode,resourceIds))){
            try {
                TimeUnit.MILLISECONDS.sleep(200);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return flag;
    }

    /**
     * 申请点位资源
     * 申请成功后，将资源加入到资源池
     */
    public static boolean apply(String userId, String mapCode, List<String> resourceIds){
        if(StringUtils.isEmpty(userId) || StringUtils.isEmpty(mapCode)){
            return false;
        }
        //如果资源id为空，表示不申请资源，直接返回true
        if(CollectionUtils.isEmpty(resourceIds)){
            return true;
        }

        String lock = mapCode + "_marker";
        boolean applyFlag = true;
        synchronized (lock.intern()){
            //由于顺序抢锁，所以每次的时间戳不一样
            final String finalUserId = userId + "_" +System.currentTimeMillis();
            for(String id : resourceIds){
                String tmpUserId = MARKER_POOL.get(id);
                if(StringUtils.isEmpty(tmpUserId)) {
                    continue;
                }
                if(!finalUserId.equals(tmpUserId)){
                    applyFlag = false;
                    break;
                }
            }
            synchronized (MARKER_POOL) {
                if (applyFlag) {
                    resourceIds.forEach(id -> {
                        MARKER_POOL.put(id, finalUserId);
                        //LOGGER.debug("MarkerLockUtil：[{}}占用的点位资源：[{}]",finalUserId,id);
                    });
                }
            }
        }
        return applyFlag;
    }

    public static void release(List<String> resourceIds){
        Long userId = SecurityUser.getUserId();
        release(String.valueOf(userId),resourceIds);
    }

    /**
     * 释放点位资源
     * 释放自己申请的资源
     * @param userId
     * @param resourceIds
     * @return
     */
    public static void release(String userId, List<String> resourceIds) {
        if (StringUtils.isEmpty(userId) || CollectionUtils.isEmpty(resourceIds)) {
            return;
        }
        synchronized (MARKER_POOL) {
            for (String id : resourceIds) {
                String tmpUserId = MARKER_POOL.get(id);
                if (StringUtils.isEmpty(tmpUserId)) {
                    continue;
                }
                if (tmpUserId.startsWith(userId)) {
                    String finalUserId = MARKER_POOL.remove(id);
                    //LOGGER.debug("MarkerLockUtil：[{}]释放的点位资源：[{}]",finalUserId,id);
                }
            }
        }
    }

    /**
     * 根据路径集合，获取点位编码集合
     */
    public static List<String> getApplyResourceCodes(String mapCode,List<String> pathCodeList,boolean isDraft){
        if(StringUtils.isEmpty(pathCodeList)){
            return Collections.EMPTY_LIST;
        }

        Set<String> codes = new HashSet<>();
        List<Path> paths = pathService.selectByCodes(mapCode, pathCodeList, isDraft);
        if(!CollectionUtils.isEmpty(paths)){
            paths.forEach(path->{
                codes.add(path.getEndMarkerCode());
                codes.add(path.getStartMarkerCode());
            });
        }
        return new ArrayList<>(codes);
    }

    public static List<String> getApplyResourceCodes(List<Marker> markerList, List<Path> paths) {
        Set<String> markerIds = new HashSet<>();
        for (Marker marker : markerList) {
            markerIds.add(marker.getCode());

            //将点位关联的路径上的点位，也加锁
            List<Path> bindPaths = paths.stream().filter(path -> path.getEndMarkerCode().equals(marker.getCode()) || path.getStartMarkerCode().equals(marker.getCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(bindPaths)) {
                bindPaths.forEach(path -> {
                    markerIds.add(path.getStartMarkerCode());
                    markerIds.add(path.getEndMarkerCode());
                });
            }
        }
        return new ArrayList<>(markerIds);
    }
}
