package com.youibot.vehicle.scheduler.modules.task.dto;

import com.youibot.vehicle.scheduler.engine.execute.node.enums.NodeVariableType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "NodeParamConfigDTO",description = "节点参数配置")
public class NodeParamConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 节点id
     */
    @ApiModelProperty(value = "nodeConfigId")
    private Long nodeConfigId;

    /**
     * 节点参数编码
     */
    @ApiModelProperty(value = "节点参数编码")
    private String code;

    /**
     * 动作参数名称
     */
    @ApiModelProperty(value = "节点参数名称")
    private String name;

    /**
     * 动作参数提示
     */
    @ApiModelProperty(value = "节点参数提示")
    private String notice;

    /**
     * 是否必填 1.可 0.不可
     */
    @ApiModelProperty(value = "是否必填 是：true 否：false")
    private Boolean required;

    /**
     * 是否可编辑 1.可 0.不可
     */
    @ApiModelProperty(value = "是否可编辑 是：true 否：false")
    private Boolean editable;

    /**
     * 是否变量
     */
    @ApiModelProperty(value = "是否变量 是：true 否：false")
    private Boolean isVariable;

    /**
     * 参数类别: Text、Number
     */
    @ApiModelProperty(value = "参数类别: Text、Number")
    private NodeVariableType variableCategory;

    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型")
    private String type;

    /**
     * 参数类别
     * 入参:In 出参:Out
     */
    @ApiModelProperty(value = "参数方向 入参:In 出参:Out")
    private String category;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    /**
     * 最大值
     */
    @ApiModelProperty(value = "最大值")
    private Double maxValue;

    /**
     * 最小值
     */
    @ApiModelProperty(value = "最小值")
    private Double minValue;

    /**
     * 选框参数
     * eg:[{“desc”:”描述”,”value”:”值”}]
     */
    @ApiModelProperty(value = "选框参数")
    private List<Map> selectionBoxParam;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private Long  creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Long updater;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

}
