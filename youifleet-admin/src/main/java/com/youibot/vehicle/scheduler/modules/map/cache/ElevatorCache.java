package com.youibot.vehicle.scheduler.modules.map.cache;

import com.yomahub.liteflow.util.CopyOnWriteHashMap;
import com.youibot.vehicle.scheduler.common.utils.AGVPropertiesUtils;
import com.youibot.vehicle.scheduler.common.utils.DateUtils;
import com.youibot.vehicle.scheduler.modules.map.entity.Elevator;
import com.youibot.vehicle.scheduler.modules.map.entity.ElevatorResultData;
import com.youibot.vehicle.scheduler.modules.map.utils.FileUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.MapFileUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 电梯缓存数据：正式数据，电梯没有草稿数据
 */
public class ElevatorCache {

    private static CopyOnWriteHashMap<String, Elevator> cache;
    private static Object lock = new Object();

    public static CopyOnWriteHashMap<String, Elevator> getElevatorData() {
        if (cache != null) {
            return cache;
        }
        synchronized (lock) {
            if (cache != null) {
                return cache;
            }
            String elevator_path = AGVPropertiesUtils.getString("MAP_FILE_PATH.ELEVATOR_PATH");
            String result = FileUtils.readFileData(elevator_path);
            if (StringUtils.isEmpty(result)) {
                cache = new CopyOnWriteHashMap<>();
            }
            ElevatorResultData elevatorData = MapFileUtils.elevatorFileDataToObjectData(result);
            cache = elevatorData.getElevator();
        }
        return cache;
    }

    public static synchronized void setCache(CopyOnWriteHashMap<String, Elevator> data) {
        cache = data;
        addMessage(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
    }

    public static synchronized Elevator addElevator(Elevator elevator) {
        return cache.put(elevator.getCode(), elevator);
    }

    public static Elevator removeElevator(String elevatorId) {
        return cache.remove(elevatorId);
    }


    //二、更新消息队列
    private static final List<String> queue = new ArrayList<>();

    public static List<String> getQueue() {
        return queue;
    }

    public static void addMessage(String message) {
        queue.add(message);
    }

    public static synchronized void clearQueue() {
        queue.clear();
    }

}
