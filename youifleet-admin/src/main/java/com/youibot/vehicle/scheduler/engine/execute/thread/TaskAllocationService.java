package com.youibot.vehicle.scheduler.engine.execute.thread;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Sets;
import com.youibot.vehicle.scheduler.engine.execute.node.DynamicAllocationVehicleNode;
import com.youibot.vehicle.scheduler.engine.execute.node.entity.AllocationVehicleMessage;
import com.youibot.vehicle.scheduler.engine.execute.node.entity.PreAllocationTaskResult;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.vehicle.scheduler.engine.pathplan.service.VehicleScopeService;
import com.youibot.vehicle.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.vehicle.scheduler.modules.device.utils.ElevatorUtils;
import com.youibot.vehicle.scheduler.modules.log.constant.LogConstant;
import com.youibot.vehicle.scheduler.modules.log.dto.SysLogPushDTO;
import com.youibot.vehicle.scheduler.modules.log.util.SysLogUtils;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import com.youibot.vehicle.scheduler.modules.task.cache.RunningTaskPool;
import com.youibot.vehicle.scheduler.modules.task.constant.TaskConstant;
import com.youibot.vehicle.scheduler.modules.task.entity.TaskEntity;
import com.youibot.vehicle.scheduler.modules.task.service.TaskService;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.dao.VehicleLockDao;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.DefaultVehicleStatus;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.PositionStatus;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.RunningStatus;
import com.youibot.vehicle.scheduler.modules.vehicle.entity.VehicleLockEntity;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePoolService;
import com.youibot.vehicle.scheduler.modules.vehicle.service.VehicleLockService;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant.*;
import static com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant.*;

/**
 * 任务分配
 *
 * <AUTHOR>
 */
@Component
public class TaskAllocationService extends Thread {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private VehiclePoolService vehiclePoolService;
    @Resource
    private VehicleScopeService vehicleScopeService;
    @Resource
    private FullLocationService fullLocationService;
    @Autowired
    private TaskService taskService;
    @Resource
    private VehicleLockDao vehicleLockDao;
    @Resource
    protected VehicleLockService vehicleLockService;

    private volatile ConcurrentHashMap<Long, AllocationVehicleMessage> nodeIdToAllocationMsg = new ConcurrentHashMap<>();

    private volatile ConcurrentHashMap<Long, String> nodeIdToAllocationVehicleCode = new ConcurrentHashMap<>();

    private List<AllocationVehicleMessage> allocationMsgs = new ArrayList<>();

    @Getter
    private List<PreAllocationTaskResult> preAllocationTaskResults = new LinkedList<>();

    /**
     * 任务分配
     * 1.获取分配信息集合数据
     * 2.分配信息集合排序
     * 3.遍历分配信息集合，开始分配
     * 4.分配机器人
     * 5.锁定机器人
     */
    @Override
    public void run() {
        Thread.currentThread().setName("Task-Allocation-Thread");
        List<Vehicle> availableVehicles = null;
        while (true) {
            try {
                if (nodeIdToAllocationMsg.size() == 0) {
                    TimeUnit.MILLISECONDS.sleep(1000);
                }

                //获取本轮分配可用的所有车辆
                availableVehicles = vehiclePoolService.getAllocationTaskVehicle();
                if (CollectionUtils.isEmpty(availableVehicles)) {
                    continue;
                }
                //处理预分配任务
                Iterator<PreAllocationTaskResult> preIterator = preAllocationTaskResults.iterator();
                while (preIterator.hasNext()) {
                    PreAllocationTaskResult data = preIterator.next();
                    availableVehicles.remove(vehiclePoolService.selectByCode(data.getVehicleCode()));
                    if (!RunningTaskPool.isFinished(data.getInterruptTaskNo())) {
                        continue;//任务未中断完成
                    }
                    //如果新任务被取消了，则删除，避免小车被卡死在这里，无法分配
                    TaskEntity task = taskService.selectByTaskNo(data.getTaskNo());
                    if (Objects.equals(task.getStatus(), TaskConstant.STATUS_FINISHED) || Objects.equals(task.getStatus(), TaskConstant.STATUS_CANCEL)) {
                        logger.debug("任务已经取消:{}，移除当前分配:{}", task, data);
                        //preAllocationTaskResults.remove(data);
                        preIterator.remove();
                        continue;
                    }
                    Optional.ofNullable(vehiclePoolService.selectByCode(data.getVehicleCode()))
                            .map(vehicle -> vehiclePoolService.lockVehicleByTask(vehicle, data.getTaskNo(), data.getNodeCode()))
                            .ifPresent(vehicleCode -> {
                                SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_TASK_ALLOCATION)
                                        .content("log.system.task.allocation.cancel.old.task.success")
                                        .data(String.join(",",Arrays.asList(data.getInterruptTaskNo(), data.getTaskNo(), vehicleCode)))
                                        .vehicleCodes(vehicleCode).taskNos(data.getTaskNo()).build());
                                nodeIdToAllocationVehicleCode.put(data.getNodeId(), vehicleCode);
                                //preAllocationTaskResults.remove(data);
                                preIterator.remove();
                            });
                }

                //1.获取分配信息集合
                allocationMsgs.addAll(this.getAndRemoveAllocationMsg());
                if (allocationMsgs.size() == 0) {
                    continue;
                }

                //2.排序,项目定制：指定型任务排前面，动态分配排后面，然后再按照优先级降序，创建时间升序
                List<AllocationVehicleMessage> allMsgs = new ArrayList<>(allocationMsgs);
                allocationMsgs.clear();
                List<AllocationVehicleMessage> assignMsgs = allMsgs.stream().filter(msg -> !StringUtils.isEmpty(msg.getVehicleCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(assignMsgs)) {
                    assignMsgs.sort(Comparator.comparing(AllocationVehicleMessage::getPriority).reversed().thenComparing(AllocationVehicleMessage::getCreateTime));
                    allocationMsgs.addAll(assignMsgs);
                }
                List<AllocationVehicleMessage> dyMsgs = allMsgs.stream().filter(msg -> StringUtils.isEmpty(msg.getVehicleCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(dyMsgs)) {
                    dyMsgs.sort(Comparator.comparing(AllocationVehicleMessage::getPriority).reversed().thenComparing(AllocationVehicleMessage::getCreateTime));
                    allocationMsgs.addAll(dyMsgs);
                }

                //3.分配
                Iterator<AllocationVehicleMessage> iterator = allocationMsgs.iterator();
                while (iterator.hasNext()) {
                    //分配机器人
                    AllocationVehicleMessage allocationVehicleMessage = iterator.next();
                    TaskEntity task = taskService.selectByTaskNo(allocationVehicleMessage.getTaskCode());
                    if (Objects.nonNull(task) && !Objects.equals(task.getStatus(), TaskConstant.STATUS_RUNNING)) {
                        iterator.remove();
                        continue;
                    }
                    this.checkAllocationMsg(allocationVehicleMessage);
                    //查询当前任务、当前节点是否已经锁定了某个小车，如果有的话，先释放
                    List<VehicleLockEntity> vehicleLocks = vehicleLockService.getVehicleLockByTaskNo(allocationVehicleMessage.getTaskCode());
                    if (CollUtil.isNotEmpty(vehicleLocks)) {
                        //正常情况下，走到这里，说明刚分配完成后系统重启了，需要释放机器人
                        releaseVehicle(vehicleLocks, allocationVehicleMessage.getNodeCode(), task);
                    }
                    Vehicle vehicle = this.selectVehiclesByAllMsg(allocationVehicleMessage, availableVehicles, task);
                    if (null == vehicle) {
                        continue;
                    }

                    logger.debug("分配的机器人:{}", vehicle);
                    String taskCode = allocationVehicleMessage.getTaskCode();
                    String nodeCode = allocationVehicleMessage.getNodeCode();
                    String vehicleTaskNo = vehicle.getTaskNo();
                    if (!StringUtils.isEmpty(vehicleTaskNo) && !Objects.equals(vehicleTaskNo, taskCode)) {
                        //如果机器人正在执行其他任务, 中断该任务
                        String content = MessageFormat.format("任务[{0}]分配机器人[{1}], 中断机器人正在执行的任务[{2}]", taskCode, vehicle.getVehicleCode(), vehicle.getTaskNo());
                        logger.debug(content);
                        SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_TASK_ALLOCATION)
                                .content("log.system.task.allocation.interrupt.current.task")
                                .data(String.join(",",Arrays.asList(taskCode, vehicle.getVehicleCode(), vehicle.getTaskNo())))
                                .vehicleCodes(vehicle.getVehicleCode()).taskNos(vehicle.getTaskNo()).build());
                        Optional.ofNullable(RunningTaskPool.getRunning(vehicle.getTaskNo()))
                                .ifPresent(interruptTask -> interruptTask.setInterrupt(Boolean.TRUE));
                        preAllocationTaskResults.add(PreAllocationTaskResult.builder().nodeId(allocationVehicleMessage.getNodeId()).taskNo(taskCode).nodeCode(nodeCode).vehicleCode(vehicle.getVehicleCode()).interruptTaskNo(vehicleTaskNo).build());
                        availableVehicles.remove(vehicle);
                        iterator.remove();
                        continue;
                    }

                    //锁定机器人
                    String vehicleCode = vehiclePoolService.lockVehicleByTask(vehicle, allocationVehicleMessage.getTaskCode(), allocationVehicleMessage.getNodeCode());
                    if (!StringUtils.isEmpty(vehicleCode)) {
                        String content = MessageFormat.format("任务[{0}]分配成功，机器人[{1}]", allocationVehicleMessage.getTaskCode(), vehicleCode);
                        logger.debug(content);
                        SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_TASK_ALLOCATION)
                                .content("log.system.task.allocation.success")
                                .data(String.join(",",Arrays.asList(allocationVehicleMessage.getTaskCode(), vehicleCode)))
                                .vehicleCodes(vehicleCode).taskNos(allocationVehicleMessage.getTaskCode()).build());
                        nodeIdToAllocationVehicleCode.put(allocationVehicleMessage.getNodeId(), vehicleCode);
                    }
                    //移除已經完成分配的消息
                    iterator.remove();
                    availableVehicles.remove(vehicle);
                    //唤醒执行node
//                    this.notifyNodeAllocation(allocationVehicleMessage.getNodeId());
                }
            } catch (Exception e) {
                SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_TASK_ALLOCATION)
                        .content("log.system.task.allocation.error")
                        .data("")
                        .message(LogExceptionStackUtil.LogExceptionStack(e)).build());
                logger.error("任务分配异常 ：", e);
            }
        }
    }

    private void releaseVehicle(List<VehicleLockEntity> vehicleLocks, String nodeCode, TaskEntity task){
        String vehicleCode = vehicleLocks.stream().filter(l -> l.getNodeCode().equals(nodeCode)).findFirst().map(VehicleLockEntity::getVehicleCode).orElse(null);
        if(!StringUtils.isEmpty(vehicleCode)){
            //1、释放机器人锁
            vehicleLockService.releaseVehicleLockByVehicleCode(vehicleCode);
            //2、同时释放任务中绑定的机器人编号
            String vehicleCodes = task.getVehicleCodes();
            Set<String> vehicleCodeSet = new HashSet<>();
            if (!StringUtils.isEmpty(vehicleCodes)) {
                vehicleCodeSet = Sets.newHashSet(vehicleCodes.split(TaskConstant.TASK_VEHICLE_SPLIT));
            }
            vehicleCodeSet.remove(vehicleCode);
            String vehicleCodeStr = String.join(TaskConstant.TASK_VEHICLE_SPLIT, vehicleCodeSet);
            UpdateWrapper<TaskEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("task_no", task.getTaskNo());
            updateWrapper.set("vehicle_codes", vehicleCodeStr);
            taskService.update(null, updateWrapper);
            //3、更新内存数据状态
            taskService.updateDetailCache(task.getId());
            //4、更新执行器的内存数据
            RunningTaskPool.getRunningTasks().stream().filter(t -> t.getTaskNo().equals(task.getTaskNo())).findFirst()
                    .ifPresent(t -> t.setVehicleCodes(vehicleCodeStr));
        }
    }

    private void checkAllocationMsg(AllocationVehicleMessage allocationVehicleMessage) {
        List<Vehicle> vehicles = vehiclePoolService.selectAll();
        String vehicleCode = allocationVehicleMessage.getVehicleCode();
        if (StrUtil.isNotEmpty(vehicleCode) && vehicles.stream().noneMatch(vehicle -> vehicleCode.equals(vehicle.getVehicleCode()))) {
            //传参为机器人名称时可通过校验
            if (vehicles.stream().noneMatch(vehicle -> vehicleCode.equals(vehicle.getName()))){
            NoticeMessage message = NoticeMessage.builder().code(VEHICLE_ABSENT).placeholders(Collections.singletonList(vehicleCode)).missionWorkId(allocationVehicleMessage.getTaskCode()).build();
            NoticeMessageUtils.pushQueue(message);
            }
        }
        String vehicleGroupCode = allocationVehicleMessage.getVehicleGroupCode();
        if (StrUtil.isNotEmpty(vehicleGroupCode) && vehicles.stream().noneMatch(vehicle -> vehicleGroupCode.equals(vehicle.getVehicleGroupCode()))) {
            NoticeMessage message = NoticeMessage.builder().code(VEHICLE_GROUP_ABSENT).placeholders(Collections.singletonList(vehicleGroupCode)).missionWorkId(allocationVehicleMessage.getTaskCode()).build();
            NoticeMessageUtils.pushQueue(message);
        }
        String vehicleTypeCode = allocationVehicleMessage.getVehicleTypeCode();
        if (StrUtil.isNotEmpty(vehicleTypeCode) && vehicles.stream().noneMatch(vehicle -> vehicleTypeCode.equals(vehicle.getVehicleTypeCode()))) {
            NoticeMessage message = NoticeMessage.builder().code(VEHICLE_TYPE_ABSENT).placeholders(Collections.singletonList(vehicleTypeCode)).missionWorkId(allocationVehicleMessage.getTaskCode()).build();
            NoticeMessageUtils.pushQueue(message);
        }
        List<String> mapCodes = allocationVehicleMessage.getMapCodes();
        if (CollUtil.isNotEmpty(mapCodes) && vehicles.stream().noneMatch(vehicle -> mapCodes.contains(vehicle.getVehicleMapCode()))) {
            NoticeMessage message = NoticeMessage.builder().code(VEHICLE_MAP_ABSENT).placeholders(Collections.singletonList(mapCodes.toString())).missionWorkId(allocationVehicleMessage.getTaskCode()).build();
            NoticeMessageUtils.pushQueue(message);
        }
    }

    private void notifyNodeAllocation(Long nodeId) {
        if (StringUtils.isEmpty(DynamicAllocationVehicleNode.nodeIdLock.get(nodeId))) {
            logger.error("分配node锁处理异常！");
            return;
        }
        synchronized (DynamicAllocationVehicleNode.nodeIdLock.get(nodeId)) {
            logger.debug("开始唤醒分配机器人节点{}", nodeId);
            DynamicAllocationVehicleNode.nodeIdLock.get(nodeId).notify();
        }
    }

    private Vehicle selectVehiclesByAllMsg(AllocationVehicleMessage allocationVehicleMessage, List<Vehicle> availableVehicles, TaskEntity taskEntity) {
        //指定机器人分配
        if (!StringUtils.isEmpty(allocationVehicleMessage.getVehicleCode())) {
            return getAssignAllocationVehicle(allocationVehicleMessage,availableVehicles, taskEntity);
        }
        if (CollectionUtils.isEmpty(availableVehicles)) {
            return null;
        }
        List<Vehicle> vehicles = vehiclePoolService.filterVehiclesToDynamicAllocation(allocationVehicleMessage, availableVehicles);
        // 过滤完的车辆等于空，结束。
        if (CollectionUtils.isEmpty(vehicles)) {
//            logger.error("当前没有符合执行任务:{}的机器人，请检查", allocationMessage.getTaskId());
            return null;
        }
        if (vehicles.size() == 1) {
            return vehicles.get(0);
        }

        //TODO:机器人评分，根据节点入参是否有目标点，判断根据距离+电量评分还是仅电量评分
        Double finalScope = null;
        Vehicle finalVehicle = null;
        if (null == allocationVehicleMessage.getMarkerCode()) {
            for (Vehicle vehicle : vehicles) {
                Double batteryScope = vehicleScopeService.scopeByBattery(vehicle);
                if (batteryScope == null) {
                    logger.error("机器人{}电量评分为null,请检查机器人状态", vehicle.getVehicleCode());
                    continue;
                }
                if (finalScope == null || finalScope < batteryScope) {
                    finalScope = batteryScope;
                    finalVehicle = vehicle;
                }
            }
        } else {
            Marker marker = MapGraphUtil.getMarkerByMarkerCode(allocationVehicleMessage.getMarkerCode());
            if (marker == null) {
                logger.error("节点{}传入的目标点位参数错误", allocationVehicleMessage.getNodeId());
                return null;
            }
            for (Vehicle vehicle : vehicles) {
                Double distanceScope = vehicleScopeService.scopeByDistance(marker.getCode(), vehicle);
                Double batteryScope = vehicleScopeService.scopeByBattery(vehicle);
                if (distanceScope == null || batteryScope == null) {
                    logger.error("目标点{}无法评分或机器人{}电量状态为空", marker.getCode(), vehicle.getVehicleCode());
                    continue;
                }
                Double tmpScope = distanceScope + batteryScope;
                if (finalScope == null || finalScope < tmpScope) {
                    finalScope = tmpScope;
                    finalVehicle = vehicle;
                }
            }
        }
        return finalVehicle;
    }

    //获取指定分配的小车
    private Vehicle getAssignAllocationVehicle(AllocationVehicleMessage allocationVehicleMessage, List<Vehicle> availableVehicles, TaskEntity taskEntity) {
        String vehicleCodeParam = allocationVehicleMessage.getVehicleCode();
        List<Vehicle> allVehicles = vehiclePoolService.selectAll();
        Vehicle first = allVehicles.stream().filter(v -> vehicleCodeParam.equals(v.getVehicleCode()) || vehicleCodeParam.equals(v.getName())).findFirst().orElse(null);
        //找不到机器人
        if (first == null) {
            SysLogUtils.pushErrorLog(SysLogPushDTO.builder().module(LogConstant.MODULE_NODE_EXECUTE)
                    .content("log.system.task.allocation.fail.vehicle.no.exist")
                    .data(String.join(",",Arrays.asList(vehicleCodeParam)))
                    .taskNos(allocationVehicleMessage.getTaskCode()).nodeCode(allocationVehicleMessage.getNodeCode()).build());
            return null;
        }

        String vehicleCode = first.getVehicleCode();
        Set<String> preAllocationVehicleCodes = preAllocationTaskResults.stream().map(PreAllocationTaskResult::getVehicleCode).collect(Collectors.toSet());
        if (preAllocationVehicleCodes.contains(vehicleCode)) {
            return null;//机器人已被预分配
        }

        Vehicle available = Optional.ofNullable(availableVehicles).map(v -> v.stream().filter(i -> i.getVehicleCode().equals(vehicleCode)).findFirst().orElse(null)).orElse(null);
        //找到了，但状态不符合
        if (available == null) {
            String controlStatus = Optional.ofNullable(first.getDefaultVehicleStatus()).map(DefaultVehicleStatus::getRunningStatus).map(RunningStatus::getControlStatus).orElse(null);
            SysLogUtils.pushWarningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_NODE_EXECUTE)
                    .content("log.system.task.allocation.fail.vehicle.state.error")
                    .data(String.join(",",Arrays.asList(vehicleCode, CONNECT_STATUS.equals(first.getConnectStatus()) ? "已连接" : "已断连",
                            ABNORMAL_STATUS.equals(Optional.ofNullable(first.getDefaultVehicleStatus()).map(DefaultVehicleStatus::getRunningStatus).map(RunningStatus::getAbnormalStatus).orElse(null)) ? "异常" : "无异常",
                            AUTO_CONTROL_MODE.equals(controlStatus) ? "自动模式" : (MANUAL_CONTROL_MODE.equals(controlStatus) ? "手动模式" : "检修模式"),
                            SOFTSTOP_STATUS_OPEN.equals(Optional.ofNullable(first.getDefaultVehicleStatus()).map(DefaultVehicleStatus::getRunningStatus).map(RunningStatus::getSoftStopSwitch).orElse(null)) ? "开启暂停" : "关闭暂停",
                            LOCATED_STATUS.equals(Optional.ofNullable(first.getDefaultVehicleStatus()).map(DefaultVehicleStatus::getPositionStatus).map(PositionStatus::getStatus).orElse(null)) ? "已定位" : "未定位",
                            fullLocationService.getVehicleLocation(vehicleCode) == null ? "已脱轨" : "未脱轨",
                            WORK_STATUS_WORK.equals(Optional.ofNullable(first.getDefaultVehicleStatus()).map(DefaultVehicleStatus::getRunningStatus).map(RunningStatus::getWorkStatus).orElse(null)) ? "工作" : "空闲",
                            Optional.ofNullable(first.getAvgBatteryValue()).map(Object::toString).orElse(""))))
                    .taskNos(allocationVehicleMessage.getTaskCode()).nodeCode(allocationVehicleMessage.getNodeCode()).build());
            return null;
        }

        //机器人是否正在禁停区域，禁停区域内不参与分配
        if (vehiclePoolService.isVehicleRunningInNoParkingArea(available)) {
            logger.info("待分配小车{}当前正在禁停区域行驶，不参与分配", available.getVehicleCode());
            return null;
        }

        //机器人是否正在乘梯，乘梯中不参与分配
        if (ElevatorUtils.isTakingElevator(available)) {
            logger.info("待分配小车{}当前正在乘梯中，不参与分配", available.getVehicleCode());
            return null;
        }

        TaskEntity target = getTaskByBindVehicleCode(vehicleCode);
        //说明状态符合，但是被任务占用了
        if (target != null) {
            SysLogUtils.pushWarningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_NODE_EXECUTE)
                    .content("log.system.task.allocation.fail.vehicle.locked")
                    .data(String.join(",",Arrays.asList(vehicleCode, target.getTaskNo())))
                    .taskNos(allocationVehicleMessage.getTaskCode()).nodeCode(allocationVehicleMessage.getNodeCode()).build());
            return null;
        }

        //找到了
        return available;
    }

    //根据绑定的小车，找到对应的任务
    private TaskEntity getTaskByBindVehicleCode(String vehicleCode) {
        List<VehicleLockEntity> vehicleLockEntities = vehicleLockDao.selectUsedVehicleLock();
        for (VehicleLockEntity vehicleLockEntity : vehicleLockEntities) {
            TaskEntity taskEntity = null;
            if (vehicleLockEntity.getVehicleCode().equals(vehicleCode)) {
                taskEntity = RunningTaskPool.getRunning(vehicleLockEntity.getTaskCode());
            }
            if (taskEntity != null && taskEntity.getIsBreak() != 0) {
                return taskEntity;
            }
        }
        return null;
    }

    public String getAllocationVehicleCodeByNodeId(Long nodeId) {
        return nodeIdToAllocationVehicleCode.get(nodeId);
    }

    public void putAllocationMsg(Long nodeId, AllocationVehicleMessage allocationVehicleMessage) {
        synchronized (nodeIdToAllocationMsg) {
            nodeIdToAllocationMsg.put(nodeId, allocationVehicleMessage);
        }
    }

    public List<AllocationVehicleMessage> getAndRemoveAllocationMsg() {
        synchronized (nodeIdToAllocationMsg) {
            List<AllocationVehicleMessage> msgs = new ArrayList<>(nodeIdToAllocationMsg.values());
            nodeIdToAllocationMsg.clear();
            return msgs;
        }
    }

}
