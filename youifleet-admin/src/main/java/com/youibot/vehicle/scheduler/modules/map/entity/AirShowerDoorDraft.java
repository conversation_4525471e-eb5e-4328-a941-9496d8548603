package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.vehicle.scheduler.modules.map.entity.MapElement;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("air_shower_door_draft")
public class AirShowerDoorDraft extends MapElement {

    @ApiModelProperty(value = "编码", position = 1)
    @TableId(value = "code")
    private String code;

    @ApiModelProperty(value = "地图编码", position = 2)
    private String vehicleMapCode;

    @ApiModelProperty(value = "停留时间 单位:秒  机器人需要在两道风淋门间停留一段时间", position = 3)
    private Integer residenceTime;

    @ApiModelProperty(value = "Modbus读功能码（01，02，03，04）", position = 4)
    private String readFunctionCode;

    @ApiModelProperty(value = "Modbus写功能码（05，06）", position = 5)
    private String writeFunctionCode;

    @ApiModelProperty(value = "电梯是否有货的检测地址", position = 6)
    private Integer goodsCheckAddress;

    @ApiModelProperty(value = "角度,用于前端页面显示", position = 7)
    private Double angle;
}
