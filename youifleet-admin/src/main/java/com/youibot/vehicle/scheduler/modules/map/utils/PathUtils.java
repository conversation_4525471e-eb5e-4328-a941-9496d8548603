package com.youibot.vehicle.scheduler.modules.map.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.vehicle.scheduler.common.entity.Point;
import com.youibot.vehicle.scheduler.common.utils.ApplicationUtils;
import com.youibot.vehicle.scheduler.common.utils.BezierUtil;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.MarkerInfo;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.entity.PathInfo;
import com.youibot.vehicle.scheduler.modules.map.service.MarkerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.LinkedBlockingDeque;

/**
 * <AUTHOR>
 * @Date :Created in 下午8:01 19-11-8
 * @Description :
 * @Modified By :
 * @Version :
 */
public class PathUtils {
    private static final Logger logger = LoggerFactory.getLogger(PathUtils.class);

    private static MarkerService markerService = (MarkerService) ApplicationUtils.getBean("markerServiceImpl");


    public static Double getLength(PathInfo pathInfo, MarkerInfo startMarker, MarkerInfo endMarker) {
        if (pathInfo == null) {
            return null;
        }
        Point[] points = new Point[4];
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double length = null;
        points[1] = JSON.parseObject(pathInfo.getStartControl(), Point.class);
        points[2] = JSON.parseObject(pathInfo.getEndControl(), Point.class);
        length = BezierUtil.getLength(points, 0D, 1D);
        return length;
    }


//    public static Double getLength(Path sidePath) {
//        if (sidePath == null) {
//            return null;
//        }
//        Point[] points = new Point[4];
//        Marker startMarker = markerService.selectByCode(sidePath.getVehicleMapCode(), sidePath.getStartMarkerCode(), false);
//        Marker endMarker = markerService.selectByCode(sidePath.getVehicleMapCode(), sidePath.getEndMarkerCode(), false);
//        points[0] = new Point(startMarker.getX(), startMarker.getY());
//        points[3] = new Point(endMarker.getX(), endMarker.getY());
//        Double length = null;
//        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
//        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
//        length = BezierUtil.getLength(points, 0D, 1D);
//        return length;
//    }

//    public static Double getDistance(Path sidePath) {
//        if (sidePath == null) {
//            return null;
//        }
//        Point[] points = new Point[4];
//        Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
//        Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
//        points[0] = new Point(startMarker.getX(), startMarker.getY());
//        points[3] = new Point(endMarker.getX(), endMarker.getY());
//        Double distance = null;
//        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
//        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
//        distance = BezierUtil.getLength(points, 0D, sidePath.getT0());
//        return distance;
//    }
//
//    public static Double getDistanceRemaining(Path sidePath) {
//        if (sidePath == null) {
//            return null;
//        }
//        Point[] points = new Point[4];
//        Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
//        Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
//        points[0] = new Point(startMarker.getX(), startMarker.getY());
//        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
//        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
//        points[3] = new Point(endMarker.getX(), endMarker.getY());
//        Double distance = null;
//        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
//        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
//        distance = BezierUtil.getLength(points, sidePath.getT0(), 1D);
//        return distance;
//    }
//
//    public static Point getPathPoint(Path sidePath) {
//        if (sidePath == null) {
//            return null;
//        }
//        Point[] points = new Point[4];
//        Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
//        Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
//        points[0] = new Point(startMarker.getX(), startMarker.getY());
//        points[3] = new Point(endMarker.getX(), endMarker.getY());
//        Point location = null;
//        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
//        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
//        location = BezierUtil.getPoint(points, sidePath.getT0());
//        return location;
//    }

//    public static Double[] getInOutAngle(Path sidePath, boolean isDraft) {
//        if (sidePath == null) {
//            return null;
//        }
//        Point[] points = new Point[4];
//        //Marker startMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId());
//        //Marker endMarker = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId());
//        Marker startMarker = markerService.selectByCode(sidePath.getVehicleMapCode(), sidePath.getStartMarkerCode(), isDraft);
//        Marker endMarker = markerService.selectByCode(sidePath.getVehicleMapCode(), sidePath.getEndMarkerCode(), isDraft);
//
//        points[0] = new Point(startMarker.getX(), startMarker.getY());
//        points[3] = new Point(endMarker.getX(), endMarker.getY());
//        Double[] inOutAngle = null;
//        points[1] = JSON.parseObject(sidePath.getStartControl(), Point.class);
//        points[2] = JSON.parseObject(sidePath.getEndControl(), Point.class);
//        inOutAngle = BezierUtil.getInOutAngle(points);
//        return inOutAngle;
//    }

    public static PathInfo getPathInfo(Path path, String locationCode) {
        return path.getPathInfos().stream().filter(pathInfo -> pathInfo.getLocatingCode().equals(locationCode)).findFirst().orElse(null);
    }

    public static MarkerInfo getStartMarkerInfo(Path path, String locationCode) {
        List<MarkerInfo> markerInfoList = Optional.ofNullable(MapGraphUtil.getMarkerByMarkerId(path.getStartMarkerCode()))
                .map(Marker::getMarkInfos)
                .orElse(null);
        if (CollUtil.isEmpty(markerInfoList)) {
            return null;
        }
        return markerInfoList.stream().filter(markerInfo -> markerInfo.getLocatingCode().equals(locationCode)).findFirst().orElse(null);
    }

    public static MarkerInfo getEndMarkerInfo(Path path, String locationCode) {
        List<MarkerInfo> markerInfoList = Optional.ofNullable(MapGraphUtil.getMarkerByMarkerId(path.getEndMarkerCode()))
                .map(Marker::getMarkInfos)
                .orElse(null);
        if (CollUtil.isEmpty(markerInfoList)) {
            return null;
        }
        return markerInfoList.stream().filter(markerInfo -> markerInfo.getLocatingCode().equals(locationCode)).findFirst().orElse(null);
    }

    /**
     * 获取在路径上指定的t0值，机器人行驶前需要旋转到的弧度
     *
     * @param path
     * @param locationCode
     * @param t0
     * @return
     */
    public static Double getTravelRadian(Path path, String locationCode, double t0) {
        PathInfo pathInfo = PathUtils.getPathInfo(path, locationCode);
        MarkerInfo startMarkerInfo = PathUtils.getStartMarkerInfo(path, locationCode);
        MarkerInfo endMarkerInfo = PathUtils.getEndMarkerInfo(path, locationCode);
        if (pathInfo == null || startMarkerInfo == null || endMarkerInfo == null) {
            logger.error("获取机器人在指定路径的t0值上的行驶角度时, 路网数据为空, path:{}, locationCode:{}", path, locationCode);
            return null;
        }
        Point[] points = new Point[4];
        points[0] = new Point(startMarkerInfo.getX(), startMarkerInfo.getY());
        points[1] = JSON.parseObject(pathInfo.getStartControl(), Point.class);
        points[2] = JSON.parseObject(pathInfo.getEndControl(), Point.class);
        points[3] = new Point(endMarkerInfo.getX(), endMarkerInfo.getY());
        Double tRadian = BezierUtil.getTRadian(points, t0);
        return tRadian != null ? tRadian - Math.toRadians(path.getAgvDirection()) : null;
    }

    public static Double[] getInOutAngle2(PathInfo pathInfo, MarkerInfo startMarker, MarkerInfo endMarker) {
        if (pathInfo == null) {
            return null;
        }
        Point[] points = new Point[4];
        points[0] = new Point(startMarker.getX(), startMarker.getY());
        points[3] = new Point(endMarker.getX(), endMarker.getY());
        Double[] inOutAngle = null;
        points[1] = JSON.parseObject(pathInfo.getStartControl(), Point.class);
        points[2] = JSON.parseObject(pathInfo.getEndControl(), Point.class);
        inOutAngle = BezierUtil.getInOutAngle(points);
        return inOutAngle;
    }

    //把sidePaths按照索引k切为两片，第一段包括索引k
    public static List<LinkedBlockingDeque<Path>> spiltPaths(LinkedBlockingDeque<Path> sidePaths, Integer k) {
        LinkedBlockingDeque<Path> first = new LinkedBlockingDeque<>();
        LinkedBlockingDeque<Path> second = new LinkedBlockingDeque<>();
        Path[] tempPaths = sidePaths.stream().toArray(Path[]::new);
        for (int i = 0; i < tempPaths.length; i++) {
            if (i <= k) {
                first.add(tempPaths[i]);
            } else {
                second.add(tempPaths[i]);
            }
        }
        List<LinkedBlockingDeque<Path>> list = new CopyOnWriteArrayList<>();
        list.add(first);
        list.add(second);
        return list;
    }

    public static LinkedList<String> getMarkerIdsFromPaths(List<Path> sidePaths) {
        LinkedList<String> markerIds = new LinkedList<>();
        for (Path sidePath : sidePaths) {
            String startMarkerId = sidePath.getStartMarkerCode();
            String endMarkerId = sidePath.getEndMarkerCode();
            if (!markerIds.contains(startMarkerId)) {
                markerIds.add(startMarkerId);
            }
            if (!markerIds.contains(endMarkerId)) {
                markerIds.add(endMarkerId);
            }
        }
        return markerIds;
    }

    /**
     * 计算控制点
     * @param startX 起始点x坐标
     * @param startY 起始点y坐标
     * @param endX 结束点x坐标
     * @param endY 结束点y坐标
     * @param percentage 直线位置百分比 0 - 1，默认0.4
     * @return 控制点坐标
     */
    public static String getControl(Double startX, Double startY, Double endX, Double endY, Double percentage) {
        percentage = Optional.ofNullable(percentage).orElse(0.4D);
        Double x = startX + (endX - startX) * percentage;
        Double y = startY + (endY - startY) * percentage;
        JSONObject jo = new JSONObject();
        jo.put("x", x);
        jo.put("y", y);
        return jo.toJSONString();
    }
}
