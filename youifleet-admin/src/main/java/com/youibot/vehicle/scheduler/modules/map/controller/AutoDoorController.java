package com.youibot.vehicle.scheduler.modules.map.controller;

import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.annotation.ConcurrentLimit;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import com.youibot.vehicle.scheduler.modules.map.dto.AutoDoorDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.AutoDoor;
import com.youibot.vehicle.scheduler.modules.map.service.AutoDoorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

@RestController("autoDoorController")
@RequestMapping(value = "/map/autoDoors", produces = "application/json")
@Api(value = "自动门", tags = "自动门", description = "自动门增删改查，手动开关门")
public class AutoDoorController {

    @Autowired
    private AutoDoorService autoDoorService;

    @ApiOperation(value = "自动门列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String",paramType = "query" ),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<AutoDoorDTO>> get(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap, boolean isDraft) {
        List<AutoDoorDTO> autoDoors = autoDoorService.searchAll(searchMap, isDraft);
        return Result.suc(autoDoors);
    }

    @LogOperation("log.controller.autoDoor.insert")
    @ApiOperation(value = "创建自动门")
    @ApiImplicitParam(name = "autoDoor", value = "自动门", required = true, dataType = "AutoDoorDTO")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Result save(@RequestBody @Validated(AddGroup.class) AutoDoorDTO autoDoor) {
        return Result.suc(this.autoDoorService.insert(autoDoor));
    }

    @ApiOperation(value = "自动门详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "自动门id", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "/{vehicleMapCode}/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<AutoDoorDTO> get(@PathVariable("code") String code, @PathVariable("vehicleMapCode") String vehicleMapCode, @RequestParam boolean isDraft) {
        AutoDoorDTO autoDoor = autoDoorService.selectByCode(vehicleMapCode, code, isDraft);
        return Result.suc(autoDoor);
    }

    @LogOperation("log.controller.autoDoor.update")
    @ApiOperation(value = "更新自动门")
    @ApiImplicitParam(name = "autoDoor", value = "自动门", required = true, dataType = "AutoDoorDTO")
    @PutMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result update(@RequestBody @Validated(UpdateGroup.class) AutoDoorDTO autoDoor) {
        this.autoDoorService.update(autoDoor);
        return Result.suc(autoDoor);
    }

    @LogOperation("log.controller.autoDoor.delete")
    @ApiOperation(value = "删除自动门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "自动门code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "query", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result delete(@PathVariable("code") String code,String vehicleMapCode) {
        //删除自动门
        this.autoDoorService.deleteByCode(vehicleMapCode,code);
        return Result.suc();
    }

    @LogOperation("log.controller.autoDoor.open")
    @ConcurrentLimit
    @ApiOperation(value = "打开自动门")
    @ApiImplicitParam(name = "code", value = "自动门code", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{code}/open")
    @ResponseStatus(value = HttpStatus.OK)
    public Result open(@PathVariable("code") String code) {
        this.autoDoorService.open(code);
        return Result.suc();
    }

    @LogOperation("log.controller.autoDoor.close")
    @ConcurrentLimit
    @ApiOperation(value = "关闭自动门")
    @ApiImplicitParam(name = "code", value = "自动门code", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{code}/close")
    @ResponseStatus(value = HttpStatus.OK)
    public Result close(@PathVariable("code") String code) {
        this.autoDoorService.close(code);
        return Result.suc();
    }

}
