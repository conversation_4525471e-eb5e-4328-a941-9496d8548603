package com.youibot.vehicle.scheduler.modules.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 任务备注
 * <AUTHOR>
 */
@Data
@ApiModel(value = "TaskRemarkDTO",description = "任务备注")
public class TaskRemarkDTO {

    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "任务备注，方便FAE备注任务异常信息")
    private String remark;
}
