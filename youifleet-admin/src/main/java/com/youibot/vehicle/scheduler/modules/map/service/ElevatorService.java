package com.youibot.vehicle.scheduler.modules.map.service;

import com.youibot.vehicle.scheduler.modules.map.dto.ElevatorDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.ElevatorRelateDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/7/30 16:07
 */
public interface ElevatorService {

    List<ElevatorDTO> searchAll(Map<String, Object> searchMap);

    List<ElevatorDTO> selectList();

    ElevatorDTO insert(ElevatorDTO elevator);

    void update(ElevatorDTO elevator);

    void deleteByCode(String code);

    ElevatorDTO selectByCode(String code);

    ElevatorRelateDTO selectByMarkerCode(String markerCode);

    void importElevator(MultipartFile multiPartFile);

    void exportElevator(HttpServletResponse response);

    /**
     * 根据地图编码，获取地图关联的电梯
     * @param mapCode
     * @return
     */
    List<ElevatorDTO> selectElevatorByMapCode(String mapCode);

    void open(String code);

    void close(String code);
}
