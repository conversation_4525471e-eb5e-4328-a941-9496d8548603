package com.youibot.vehicle.scheduler.modules.map.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
@ApiModel(value = "PathParam", description = "路径扩展参数")
public class PathParam implements Cloneable {

    @ApiModelProperty(value = "移动加速度 m/s2")
    private Double translationAcc = 0.5;

    @ApiModelProperty(value = "旋转加速度 rad/s2")
    private Double rotateAcc = 0.6;

}