package com.youibot.vehicle.scheduler.modules.statistics.service;

import cn.hutool.core.date.DateTime;
import com.youibot.vehicle.scheduler.common.service.BaseService;
import com.youibot.vehicle.scheduler.modules.statistics.entity.VehicleStatusRecordEntity;

import java.util.Date;
import java.util.List;

public interface VehicleStatusRecordService extends BaseService<VehicleStatusRecordEntity> {

    VehicleStatusRecordEntity getLastByStartTime(String vehicleCode, String status);

    void updateLastEndTime(String vehicleCode, String status, Date endTime);

    List<VehicleStatusRecordEntity> getStatisticsData(DateTime startTime, DateTime endTime);

    List<VehicleStatusRecordEntity> getStatisticsDataWithStatus(DateTime startTime, DateTime endTime, String status);
}
