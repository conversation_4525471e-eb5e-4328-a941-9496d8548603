package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "VehicleMapInfoDTO",description = "地图信息")
public class VehicleMapInfoDTO {

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "名称", position = 1)
    private String name;

    @ApiModelProperty(value = "是否有草稿地图 1：有 0：没有", position = 1)
    private Integer isDraft;

    @ApiModelProperty(value = "是否有正式地图 1：有 0：没有", position = 1)
    private Integer isProd;

    @ApiModelProperty(value = "地图暂停状态：0:正常，1:暂停中，2:暂停完成", position = 1)
    private Integer pauseStatus;

    @ApiModelProperty(value = "未处于暂停态的机器人数量", position = 1)
    private Integer runningVehicleNum = 0;

    @ApiModelProperty(value = "创建人名称", position = 1)
    private String creatorName;

    @ApiModelProperty(value = "创建时间", position = 1)
    private Date createTime;

    @ApiModelProperty(value = "编辑时间", position = 1)
    private Date editTime;

    @ApiModelProperty(value = "发布时间", position = 1)
    private Date publishTime;

    @ApiModelProperty(value = "路网md5值", position = 1)
    private String pathMd5;

}
