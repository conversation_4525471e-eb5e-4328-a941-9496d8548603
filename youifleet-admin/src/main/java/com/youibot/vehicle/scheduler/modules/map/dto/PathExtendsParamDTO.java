package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.modules.map.entity.MapElement;
import com.youibot.vehicle.scheduler.modules.map.entity.PathExtendParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@ApiModel(value = "PathExtendsParamDTO", description = "路径扩展参数")
public class PathExtendsParamDTO extends MapElement implements Cloneable {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "地图编码")
    private String vehicleMapCode;

    @ApiModelProperty(value = "可变路径扩展参数")
    private List<PathExtendParam> extendParamList;


}
