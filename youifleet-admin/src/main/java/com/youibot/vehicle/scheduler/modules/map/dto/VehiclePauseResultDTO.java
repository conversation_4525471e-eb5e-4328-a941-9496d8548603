package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
@ApiModel(value = "VehiclePauseResultDTO", description = "机器人暂停、恢复结果")
public class VehiclePauseResultDTO {

    @ApiModelProperty(value = "机器人编码")
    private String vehicleCode;

    @ApiModelProperty(value = "机器人暂停、恢复结果:true false")
    private Boolean result;

    @ApiModelProperty(value = "异常描述")
    private String desc;
}
