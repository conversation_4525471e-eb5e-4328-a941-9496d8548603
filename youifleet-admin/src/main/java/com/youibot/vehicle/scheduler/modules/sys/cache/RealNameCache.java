package com.youibot.vehicle.scheduler.modules.sys.cache;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class RealNameCache {

    private static final Map<Long, String> cache = new HashMap<>();

    public static void put(Long userId, String realName) {
        cache.put(userId, realName);
    }

    public static String get(Long userId) {
        if (userId == null) {
            return null;
        }
        return cache.get(userId);
    }

    public static void remove(Long userId) {
        cache.remove(userId);
    }

    /**
     * 根据姓名模糊匹配得到对应的用户ID
     * @return
     */
    public static List<Long> getUserIdsByFuzzyRealName(String realName) {
        return cache.entrySet().stream().filter(i -> i.getValue().contains(realName)).map(Map.Entry::getKey).distinct().collect(Collectors.toList());
    }
}
