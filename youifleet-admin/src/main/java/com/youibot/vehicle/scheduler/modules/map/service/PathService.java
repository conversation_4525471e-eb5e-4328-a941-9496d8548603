package com.youibot.vehicle.scheduler.modules.map.service;

import com.youibot.vehicle.scheduler.modules.map.dto.PathDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.PathExtendsParamDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.ReducePathDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;

import java.util.List;
import java.util.Map;

public interface PathService {

    List<Path> searchAll(Map<String, Object> searchMap);

    List<ReducePathDTO> searchAllWithLocatingCode(Map<String, Object> searchMap);

    List<Path> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft);

    List<Path> selectCodeByVehicleMapCode(String vehicleMapCode, boolean isDraft);

    List<Path> getAllPath(boolean isDraft);

    Path selectByCode(String vehicleMapCode, String code, boolean isDraft);

    PathDTO selectByCodeWithLocatingCode(String vehicleMapCode, String locatingCode, String code, boolean isDraft);

    List<Path> selectByCodes(String vehicleMapCode, List<String> codes, boolean isDraft);

    List<PathDTO> selectByCodesWithLocatingCode(String vehicleMapCode, String locatingCode, List<String> codes, boolean isDraft);

    /**
     * 根据地图id和 结束点和起始点查询路径列表
     *
     * @param startId
     * @param endId
     * @param vehicleMapCode
     * @param isDraft
     * @return
     */
    List<Path> selectByAgvMapIdAndStartIdOrEndId(String startId, String endId, String vehicleMapCode, boolean isDraft);

    List<Path> selectByMarker(Marker marker, boolean isDraft);

    List<Path> selectByStartMarkerCodeAndEndMarkerCode(String startMarkerCode, String endMarkerCode, boolean isDraft);

    /**
     * 根据标记点id查询指定地图中结束点或起始点为id值的路径列表
     *
     * @param vehicleMapCode
     * @param markerCode
     * @param isDraft
     * @return
     */
    List<Path> selectByVehicleMapCodeAndMarkerCode(String markerCode, String vehicleMapCode, boolean isDraft);

    List<PathDTO> insert(List<PathDTO> paths);

    Path update(PathDTO pathDTO);

    List<PathDTO> batchUpdate(List<PathDTO> paths);

    List<PathExtendsParamDTO> batchUpdateExtendParams(List<PathExtendsParamDTO> extendsParamList);

    void checkBindDoors(List<Path> paths);

    void checkBindDoorsWithCodes(List<String> paths);

    void delete(String vehicleMapCode, String code);

    void batchDel(String vehicleMapCode, List<String> codes);

    void deleteByVehicleMapCode(String vehicleMapCode);

    /**
     * 将路径按照开始点结束点，分组
     *
     * @param vehicleMapCode
     * @param isDraft
     * @return
     */
    Map<String, List<Path>> getGroupPath(String vehicleMapCode, boolean isDraft);

    void deleteDraftByVehicleMapCode(String mapCode);

    /**
     * 根据两个点位查询路径
     *
     * @return
     */
    List<Path> selectByMarkerCodes(String markerCode1, String markerCode2, boolean isDraft);
}
