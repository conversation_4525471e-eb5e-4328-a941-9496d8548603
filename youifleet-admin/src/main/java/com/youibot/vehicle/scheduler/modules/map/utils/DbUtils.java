package com.youibot.vehicle.scheduler.modules.map.utils;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.util.Collection;

@Component
public class DbUtils {

    @Transactional(rollbackFor = Exception.class)
    public <T> boolean updateBatchById(Class clazz, Collection<T> entityList, int batchSize) {
        if (CollectionUtils.isEmpty(entityList)) {
            //throw new IllegalArgumentException("Error: entityList must not be empty");
            return false;
        }
        SqlSession batchSqlSession = SqlHelper.sqlSessionBatch(clazz);
        int i = 0;
        String sqlStatement = SqlHelper.table(clazz).getSqlStatement(SqlMethod.UPDATE_BY_ID.getMethod());
        try {
            for (T anEntityList : entityList) {
                MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
                param.put(Constants.ENTITY, anEntityList);
                batchSqlSession.update(sqlStatement, param);
                if (i >= 1 && i % batchSize == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        } finally {
            SqlSessionUtils.closeSqlSession(batchSqlSession, GlobalConfigUtils.currentSessionFactory(clazz));
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public <T> boolean insertBatchById(Class clazz, Collection<T> entityList, int batchSize) {
        if (CollectionUtils.isEmpty(entityList)) {
            //throw new IllegalArgumentException("Error: entityList must not be empty");
            return false;
        }
        SqlSession batchSqlSession = SqlHelper.sqlSessionBatch(clazz);
        int i = 0;
        String sqlStatement = SqlHelper.table(clazz).getSqlStatement(SqlMethod.INSERT_ONE.getMethod());
        try {
            for (T anEntityList : entityList) {
                batchSqlSession.insert(sqlStatement, anEntityList);
                if (i >= 1 && i % batchSize == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        } finally {
            SqlSessionUtils.closeSqlSession(batchSqlSession, GlobalConfigUtils.currentSessionFactory(clazz));
        }
        return true;
    }
}
