package com.youibot.vehicle.scheduler.modules.map.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.exception.ParameterException;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.Pair;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.vehicle.scheduler.modules.device.thread.door.AirShowerDoorControlThread;
import com.youibot.vehicle.scheduler.modules.device.utils.AutoDoorUtils;
import com.youibot.vehicle.scheduler.modules.device.utils.ModbusUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.AirShowerDoorDao;
import com.youibot.vehicle.scheduler.modules.map.dao.AirShowerDoorDraftDao;
import com.youibot.vehicle.scheduler.modules.map.dao.AutoDoorDao;
import com.youibot.vehicle.scheduler.modules.map.dao.AutoDoorDraftDao;
import com.youibot.vehicle.scheduler.modules.map.dto.AirShowerDoorDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.AutoDoorDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.*;
import com.youibot.vehicle.scheduler.modules.map.service.*;
import com.youibot.vehicle.scheduler.modules.map.utils.CodeFormatUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.MapPublishUtil;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AirShowerDoorServiceImpl implements AirShowerDoorService, MapElementService {

    private static final Logger logger = LoggerFactory.getLogger(AirShowerDoorServiceImpl.class);

    @Resource
    private AirShowerDoorDao airShowerDoorDao;
    @Resource
    private AirShowerDoorDraftDao airShowerDoorDraftDao;
    @Resource
    private AutoDoorDao autoDoorDao;
    @Resource
    private AutoDoorDraftDao autoDoorDraftDao;
    @Resource
    private AutoDoorService autoDoorService;
    @Resource
    public PathService pathService;
    @Resource
    public MarkerService markerService;


    @Override
    public List<AirShowerDoorDTO> searchAll(Map<String, Object> searchMap, boolean isDraft) {
        List<AirShowerDoorDTO> list;
        if (isDraft) {
            List<AirShowerDoorDraft> airShowerDoorDrafts = airShowerDoorDraftDao.selectList(getWrapper(searchMap));
            list = this.airShowerDoorDraftEntityToDTO(airShowerDoorDrafts);
        } else {
            List<AirShowerDoor> airShowerDoors = airShowerDoorDao.selectList(getWrapper(searchMap));
            list = this.airShowerDoorEntityToDTO(airShowerDoors);
            updateDoorStatus(list);
        }
        list.removeIf(airShowerDoorDTO -> CollectionUtil.isEmpty(airShowerDoorDTO.getDoors()) || airShowerDoorDTO.getDoors().size() != 2);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AirShowerDoorDTO insert(AirShowerDoorDTO airShowerDoorDTO) {
        String mapCode = airShowerDoorDTO.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            String code = CodeFormatUtils.getFormatCode(mapCode, getMaxCode(mapCode), CodeFormatUtils.AUTOSHOWERDOOR_CODE_PREFIX);
            airShowerDoorDTO.setCode(code);
            AirShowerDoorDraft airShowerDoorDraft = ConvertUtils.sourceToTarget(airShowerDoorDTO, AirShowerDoorDraft.class);
            List<AutoDoorDTO> autoDoorDTOS = airShowerDoorDTO.getDoors();
            //校验两个门是否有绑定相同的路径，如果有则为true,抛出异常
            if (checkAirShowerDoorsPaths(autoDoorDTOS)) {
                throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.bind.path.error",airShowerDoorDTO.getCode()));
            }
            autoDoorDTOS.stream().forEach(autoDoorDTO -> {
                String doorCode = UUID.randomUUID().toString();
                autoDoorDTO.setCode(doorCode);
                AutoDoorDraft autoDoorDraft = ConvertUtils.sourceToTarget(autoDoorDTO, AutoDoorDraft.class);
                autoDoorDraft.setAsdCode(code);
                if (!CollectionUtils.isEmpty(autoDoorDTO.getPathCodes())) {
                    autoDoorDraft.setPathCodes(String.join(",", autoDoorDTO.getPathCodes()));
                }
                autoDoorDraftDao.insert(autoDoorDraft);
            });
            if (airShowerDoorDraft.getAngle() == null) {
                airShowerDoorDraft.setAngle(0.0);
            }
            airShowerDoorDraftDao.insert(airShowerDoorDraft);
            //发送消息到打开当前地图的窗口
            AirShowerDoorDTO airShowerDoorDTO1 = this.selectByCode(mapCode, code, true);
            MapUpdateSocketController.sendMessageOfAdd(mapCode, "AirShowerDoor", Arrays.asList(airShowerDoorDTO1));

        } catch (FleetException e) {
            throw e;
        } catch (Exception e) {
            logger.error("add airShowerDoor failed", e);
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.add.error"));
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
        return airShowerDoorDTO;
    }

    /**
     * 获取最大的编号
     */
    private String getMaxCode(String vehicleMapCode) {
        String code = MapConstant.DefaultCode;
        List<AirShowerDoorDraft> airShowerDoorDrafts = this.getListByVehicleMapCode(vehicleMapCode, airShowerDoorDraftDao);
        if (!CollectionUtils.isEmpty(airShowerDoorDrafts)) {
            Integer maxVal = airShowerDoorDrafts.stream().map(item -> Integer.valueOf(CodeFormatUtils.getIntegerCode(item.getCode()))).max(Comparator.comparingInt(a -> a)).orElse(0);
            code = String.valueOf(maxVal + 1);
        }
        return code;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AirShowerDoorDTO update(AirShowerDoorDTO airShowerDoorDTO) {
        String mapCode = airShowerDoorDTO.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        AirShowerDoorDTO updateAirShowerDoorDTO = null;
        try {
            AirShowerDoorDraft airShowerDoorDraft = ConvertUtils.sourceToTarget(airShowerDoorDTO, AirShowerDoorDraft.class);
            List<AutoDoorDTO> autoDoorDTOS = airShowerDoorDTO.getDoors();
            //校验两个门是否有绑定相同的路径，如果有则为true,抛出异常
            if (checkAirShowerDoorsPaths(autoDoorDTOS)) {
                throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.bind.path.error",airShowerDoorDTO.getCode()));
            }
            if (!CollectionUtils.isEmpty(autoDoorDTOS)) {
                List<AutoDoorDraft> autoDoorDrafts = AutoDoorUtils.autoDoorDTOToAutoDoorDraft(autoDoorDTOS);
                autoDoorDrafts.stream().forEach(autoDoorDraft -> {
                    autoDoorDraft.setAsdCode(airShowerDoorDTO.getCode());
                    if (StringUtils.isEmpty(autoDoorDraft.getCode())) {
                        autoDoorDraft.setCode(UUID.randomUUID().toString());
                        autoDoorDraftDao.insert(autoDoorDraft);
                    } else {
                        autoDoorDraftDao.updateById(autoDoorDraft);
                    }
                });
            }
            airShowerDoorDraftDao.updateById(airShowerDoorDraft);
            //发送消息到打开当前地图的窗口
            updateAirShowerDoorDTO = this.selectByCode(mapCode, airShowerDoorDTO.getCode(), true);
            MapUpdateSocketController.sendMessageOfUpdate(mapCode, "AirShowerDoor", Arrays.asList(updateAirShowerDoorDTO));
        } catch (FleetException e) {
            throw e;
        } catch (Exception e) {
            logger.error("update AirShowerDoor failed", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.update.error", airShowerDoorDTO.getCode());
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
        return updateAirShowerDoorDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCode(String mapCode, String code) {
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            AirShowerDoorDTO airShowerDoorDTO = this.selectByCode(mapCode, code, true);
            airShowerDoorDraftDao.deleteById(code);
            autoDoorService.deleteByAsdCode(code, true);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfDelete(mapCode, "AirShowerDoor", Arrays.asList(airShowerDoorDTO));
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("delete AirShowerDoor failed", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.delete.error", code);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    @Override
    public AirShowerDoorDTO selectByCode(String vehicleMapCode, String code, boolean isDraft) {
        List<AirShowerDoorDTO> airShowerDoorDTOS = selectByVehicleMapCode(vehicleMapCode, isDraft);
        return airShowerDoorDTOS.stream().filter(airShowerDoorDTO -> airShowerDoorDTO.getCode().equals(code)).findFirst().orElse(null);
    }

    @Override
    public AirShowerDoorDTO selectByCode(String code) {
        AirShowerDoor airShowerDoor = airShowerDoorDao.selectById(code);
        List<AutoDoorDTO> autoDoorDTOS = autoDoorService.selectByAsdCode(code, false);
        AirShowerDoorDTO airShowerDoorDTO = ConvertUtils.sourceToTarget(airShowerDoor, AirShowerDoorDTO.class);
        airShowerDoorDTO.setDoors(autoDoorDTOS);
        updateDoorStatus(Arrays.asList(airShowerDoorDTO));
        return airShowerDoorDTO;
    }

    @Override
    public List<AirShowerDoorDTO> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft) {
        List<AirShowerDoorDTO> airShowerDoorDTOS = null;
        if (isDraft) {
            List<AirShowerDoorDraft> airShowerDoorDrafts = this.getListByVehicleMapCode(vehicleMapCode, airShowerDoorDraftDao);
            airShowerDoorDTOS = this.airShowerDoorDraftEntityToDTO(airShowerDoorDrafts);
        } else {
            List<AirShowerDoor> airShowerDoors = this.getListByVehicleMapCode(vehicleMapCode, airShowerDoorDao);
            airShowerDoorDTOS = this.airShowerDoorEntityToDTO(airShowerDoors);
            updateDoorStatus(airShowerDoorDTOS);
        }
        return airShowerDoorDTOS;
    }

    private void updateDoorStatus(List<AirShowerDoorDTO> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(door -> {
                if (CollectionUtils.isEmpty(door.getDoors())) {
                    return;
                }
                door.getDoors().forEach(d -> {
                    Pair<String, Date> pair = AirShowerDoorControlThread.AUTODOOR_STATUS.get(d.getCode());
                    Optional.ofNullable(pair).ifPresent(p -> {
                        d.setCurrentStatus(p.getFirst());
                    });
                });
            });
        }
    }

    @Override
    public List<AirShowerDoor> selectByVehicleMapCode(String vehicleMapCode) {
        List<AirShowerDoor> airShowerDoors = this.getListByVehicleMapCode(vehicleMapCode, airShowerDoorDao);
        if (CollectionUtils.isEmpty(airShowerDoors)) {
            return null;
        }
        return airShowerDoors;
    }

    private List<AirShowerDoorDTO> airShowerDoorEntityToDTO(List<AirShowerDoor> airShowerDoors) {
        List<AirShowerDoorDTO> airShowerDoorDTOS = new ArrayList<>();
        airShowerDoors.forEach(airShowerDoor -> {
            AirShowerDoorDTO airShowerDoorDTO = ConvertUtils.sourceToTarget(airShowerDoor, AirShowerDoorDTO.class);
            List<AutoDoorDTO> autoDoorDTOS = autoDoorService.selectByAsdCode(airShowerDoor.getCode(), false);
            airShowerDoorDTO.setDoors(autoDoorDTOS);
            airShowerDoorDTOS.add(airShowerDoorDTO);
        });
        return airShowerDoorDTOS;
    }

    private List<AirShowerDoorDTO> airShowerDoorDraftEntityToDTO(List<AirShowerDoorDraft> airShowerDoorDrafts) {
        List<AirShowerDoorDTO> airShowerDoorDTOS = new ArrayList<>();
        airShowerDoorDrafts.forEach(airShowerDoor -> {
            AirShowerDoorDTO airShowerDoorDTO = ConvertUtils.sourceToTarget(airShowerDoor, AirShowerDoorDTO.class);
            List<AutoDoorDTO> autoDoorDTOS = autoDoorService.selectByAsdCode(airShowerDoor.getCode(), true);
            airShowerDoorDTO.setDoors(autoDoorDTOS);
            airShowerDoorDTOS.add(airShowerDoorDTO);
        });
        return airShowerDoorDTOS;
    }

    @Override
    public List<AirShowerDoorDTO> getAllAirShowerDoors(boolean isDraft) {
        List<AirShowerDoorDTO> airShowerDoorDTOS;
        if (isDraft) {
            List<AirShowerDoorDraft> airShowerDoorDrafts = airShowerDoorDraftDao.selectList(null);
            airShowerDoorDTOS = this.airShowerDoorDraftEntityToDTO(airShowerDoorDrafts);
        } else {
            List<AirShowerDoor> airShowerDoors = airShowerDoorDao.selectList(null);
            airShowerDoorDTOS = this.airShowerDoorEntityToDTO(airShowerDoors);
            updateDoorStatus(airShowerDoorDTOS);
        }
        airShowerDoorDTOS.removeIf(airShowerDoorDTO -> CollectionUtil.isEmpty(airShowerDoorDTO.getDoors()) || airShowerDoorDTO.getDoors().size() != 2);
        return airShowerDoorDTOS;
    }


    /**
     * 根据路径，获取关联的风淋门
     *
     * @param pathList
     * @param isDraft
     * @return
     */
    @Override
    public List<AirShowerDoorDTO> getAirShowerDoorsByPaths(List<Path> pathList, boolean isDraft) {
        List<AirShowerDoorDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(pathList)) {
            return list;
        }
        List<AirShowerDoorDTO> airShowerDoorDTOS = getAllAirShowerDoors(isDraft);
        if (CollectionUtils.isEmpty(airShowerDoorDTOS)) {
            return list;
        }

        Set<AirShowerDoorDTO> set = new HashSet<>();
        List<String> pathCodes = pathList.stream().map(Path::getCode).collect(Collectors.toList());
        pathCodes.forEach(code -> {
            airShowerDoorDTOS.forEach(item -> {
                item.getDoors().forEach(it -> {
                    if (it.getPathCodes().contains(code)) {
                        set.add(item);
                    }
                });
            });
        });
        list.addAll(set);
        return list;
    }

    /**
     * 根据点位，获取关联的风淋门
     */
    @Override
    public List<AirShowerDoorDTO> getAirShowerDoorsByMarkerCode(String startMarkerCode, String endMarkerCode) {
        Marker startMarker = markerService.selectByCode(startMarkerCode);
        Marker endMarker = markerService.selectByCode(endMarkerCode);
        if (startMarker == null || endMarker == null) {
            return Collections.EMPTY_LIST;
        }
        List<Path> paths = pathService.selectByStartMarkerCodeAndEndMarkerCode(startMarkerCode, endMarkerCode, false);
        return getAirShowerDoorsByPaths(paths, false);
    }

    @Override
    public List<AirShowerDoorDTO> isInAirShowerDoors(VehicleLocation vehicleLocation, boolean isDraft) {
        if (Objects.isNull(vehicleLocation) || (Objects.isNull(vehicleLocation.getMarker())) && CollUtil.isEmpty(vehicleLocation.getSidePaths())) {
            logger.error("机器人脱轨，不在风淋门内，点位数据：{}", vehicleLocation.toString());
            return Collections.EMPTY_LIST;
        }
        List<Path> allPath = new ArrayList<>();
//        allPath.addAll(pathService.selectByMarker(vehicleLocation.getMarker(), isDraft));
        if (!CollUtil.isEmpty(vehicleLocation.getSidePaths())) {
            allPath.addAll(vehicleLocation.getSidePaths());
        }
        return getAirShowerDoorsByPaths(allPath, isDraft);
    }

    @Override
    public void open(String code, String doorCode) {
        try {
            if (StringUtils.isEmpty(code) || StringUtils.isEmpty(doorCode)) {
                String message = I18nMessageUtils.getMessage("system.missing.parameter");
                throw new FleetException(message);
            }
            AirShowerDoor airShowerDoor = airShowerDoorDao.selectById(code);
            if (airShowerDoor == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.not.exist.error", code);
                throw new FleetException(message);
            }
            AutoDoor autoDoor = autoDoorDao.selectById(doorCode);
            if (autoDoor == null || !code.equals(autoDoor.getAsdCode())) {
                String message = I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.not.exist.error", code);
                throw new FleetException(message);
            }

            Pair<String, Date> pair = AutoDoorUtils.getDoorStatusByDoorCode(autoDoor.getCode(), AirShowerDoorControlThread.AUTODOOR_STATUS);
            String status = pair.getFirst();
            if (MapConstant.AUTO_DOOR_STATUS_ERROR.equals(status)) {
                String message = I18nMessageUtils.getMessage("device.connect.error", autoDoor.getCode());
                throw new FleetException(message);
            } else if (MapConstant.AUTO_DOOR_STATUS_OPEN.equals(status)) {
                logger.debug("门已开启, doorName:{},autoDoorCode:{}", airShowerDoor.getCode(), autoDoor.getCode());
                return;
            }

            //重置关门状态值
            if (autoDoor.getCloseAddress() != null) {
                writeModbusValue(airShowerDoor, autoDoor.getIp(), autoDoor.getPort(), airShowerDoor.getWriteFunctionCode(), autoDoor.getCloseAddress(), false);
            }
            //发送开门指令
            writeModbusValue(airShowerDoor, autoDoor.getIp(), autoDoor.getPort(), airShowerDoor.getWriteFunctionCode(), autoDoor.getOpenAddress(), true);
            //检测自动门状态, 直到自动门打开
            while (true) {
                Thread.sleep(500);
                boolean open = readModbusValue(airShowerDoor, autoDoor.getIp(), autoDoor.getPort(), airShowerDoor.getReadFunctionCode(), autoDoor.getOpenStatusAddress());
                if (open) {
                    logger.debug("自动门已打开, 检测结束");
                    break;
                }
            }
            pair.setFirst(MapConstant.AUTO_DOOR_STATUS_OPEN);
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("打开自动门失败, ", e);
            if (e instanceof ParameterException || e instanceof UnknownHostException) {
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AIR_SHOWER_DOOR_CONNECT_ERROR).placeholders(Arrays.asList(code)).build());
            }
            String message = I18nMessageUtils.getMessage("device.open.error", code);
            throw new FleetException(message);
        }
    }

    @Override
    public void close(String code, String doorCode) {
        try {
            if (StringUtils.isEmpty(code) || StringUtils.isEmpty(doorCode)) {
                String message = I18nMessageUtils.getMessage("system.missing.parameter");
                throw new FleetException(message);
            }
            AirShowerDoor airShowerDoor = airShowerDoorDao.selectById(code);
            if (airShowerDoor == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.not.exist.error", code);
                throw new FleetException(message);
            }
            AutoDoor autoDoor = autoDoorDao.selectById(doorCode);
            if (autoDoor == null || !code.equals(autoDoor.getAsdCode())) {
                String message = I18nMessageUtils.getMessage("vehicleMap.airShowerDoor.not.exist.error", code);
                throw new FleetException(message);
            }
            Pair<String, Date> pair = AutoDoorUtils.getDoorStatusByDoorCode(autoDoor.getCode(), AirShowerDoorControlThread.AUTODOOR_STATUS);
            String status = pair.getFirst();
            if (MapConstant.AUTO_DOOR_STATUS_ERROR.equals(status)) {
                String message = I18nMessageUtils.getMessage("device.connect.error", autoDoor.getCode());
                throw new FleetException(message);
            } else if (MapConstant.AUTO_DOOR_STATUS_CLOSE.equals(status)) {
                logger.debug("门已关闭, doorName:{},doorId:{}", airShowerDoor.getCode(), autoDoor.getCode());
                return;
            }
            if (AutoDoorUtils.isUseAutoDoor(autoDoor, airShowerDoor.getVehicleMapCode())) {
                String message = I18nMessageUtils.getMessage("device.is.in.use.error", code);
                throw new FleetException(message);
            }

            //重置开门状态值
            writeModbusValue(airShowerDoor, autoDoor.getIp(), autoDoor.getPort(), airShowerDoor.getWriteFunctionCode(), autoDoor.getOpenAddress(), false);
            //如果关门指令地址为空则不发关门指令
            if (autoDoor.getCloseAddress() == null) {
                return;
            }
            //发送关门指令
            writeModbusValue(airShowerDoor, autoDoor.getIp(), autoDoor.getPort(), airShowerDoor.getWriteFunctionCode(), autoDoor.getCloseAddress(), true);
            //检测自动门状态, 直到自动门打开
            while (true) {
                Thread.sleep(500);
                boolean close = readModbusValue(airShowerDoor, autoDoor.getIp(), autoDoor.getPort(), airShowerDoor.getReadFunctionCode(), autoDoor.getCloseStatusAddress());
                if (close) {
                    logger.debug("自动门已关闭, 检测结束");
                    break;
                }
            }
            pair.setFirst(MapConstant.AUTO_DOOR_STATUS_CLOSE);
            pair.setSecond(new Date());
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("关闭自动门失败, ", e);
            if (e instanceof ParameterException || e instanceof UnknownHostException) {
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AIR_SHOWER_DOOR_CONNECT_ERROR).placeholders(Arrays.asList(code)).build());
            }
            String message = I18nMessageUtils.getMessage("device.close.error", code);
            throw new FleetException(message);
        }
    }

    private boolean readModbusValue(AirShowerDoor autoDoor, String ip, Integer port, String readFunctionCode, Integer coilAddress) throws Exception {
        try {
            int[] ints = ModbusUtils.readFunctionCode(ip, port, readFunctionCode, coilAddress, 1);
            return ints[0] != 0;
        } catch (Exception e) {
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AIR_SHOWER_DOOR_READ_ERROR).placeholders(Arrays.asList(autoDoor.getCode())).build());
            throw e;
        }
    }

    private void writeModbusValue(AirShowerDoor autoDoor, String ip, Integer port, String writeFunctionCode, Integer coilAddress, boolean value) throws Exception {
        try {
            ModbusUtils.writeFunctionCode(ip, port, writeFunctionCode, coilAddress, value ? 1 : 0);
        } catch (Exception e) {
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AIR_SHOWER_DOOR_WRITE_ERROR).placeholders(Arrays.asList(autoDoor.getCode())).build());
            throw e;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDraftByVehicleMapCode(String mapCode) {
        try {
            List<AirShowerDoorDraft> airShowerDoorDrafts = this.getListByVehicleMapCode(mapCode, airShowerDoorDraftDao);
            if (!CollectionUtils.isEmpty(airShowerDoorDrafts)) {
                deleteByVehicleMapCode(mapCode, airShowerDoorDraftDao);
                airShowerDoorDrafts.forEach(airShowerDoorDraft -> {
                    autoDoorService.deleteByAsdCode(airShowerDoorDraft.getCode(), true);
                });
            }
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("Failed execute to deleteByAGVMapId method", e);
            throw new FleetException("删除地图风淋门异常");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByVehicleMapCode(String mapCode) {
        try {
            List<AirShowerDoorDTO> airShowerDoorDTOS = selectByVehicleMapCode(mapCode, true);
            /**
             * 删除草稿
             */
            this.deleteDraftByVehicleMapCode(mapCode);
            /**
             * 删除正式数据
             */
            List<AirShowerDoor> airShowerDoors = this.getListByVehicleMapCode(mapCode, airShowerDoorDao);
            if (!CollectionUtils.isEmpty(airShowerDoors)) {
                deleteByVehicleMapCode(mapCode, airShowerDoorDao);
                airShowerDoors.stream().forEach(airShowerDoor -> {
                    autoDoorService.deleteByAsdCode(airShowerDoor.getCode(), false);
                });
            }
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("Failed execute to deleteByAGVMapId method", e);
            throw new FleetException("删除地图风淋门异常");
        }
    }

    /**
     * 校验风淋门的两个门是否绑定了同一条路径
     * @param autoDoorDTOS
     * @return
     */
    private boolean checkAirShowerDoorsPaths (List<AutoDoorDTO> autoDoorDTOS) {
        if (CollectionUtils.isEmpty(autoDoorDTOS)) {
            return false;
        }
        List<String> door1PathCodes = autoDoorDTOS.get(0).getPathCodes();
        List<String> door2PathCodes = null;
        if (autoDoorDTOS.size() == 2) {
            door2PathCodes = autoDoorDTOS.get(1).getPathCodes();
        }
        if (CollectionUtils.isEmpty(door1PathCodes) || CollectionUtils.isEmpty(door2PathCodes)) {
            return false;
        }
        return door1PathCodes.stream().anyMatch(door2PathCodes::contains);
    }
}
