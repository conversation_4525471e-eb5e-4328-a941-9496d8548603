package com.youibot.vehicle.scheduler.modules.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.FlowBus;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.youibot.vehicle.scheduler.api.dto.TaskDetailApiDTO;
import com.youibot.vehicle.scheduler.api.dto.TaskExecuteApiDTO;
import com.youibot.vehicle.scheduler.api.dto.TaskNodeDetailApiDTO;
import com.youibot.vehicle.scheduler.common.constant.Constant;
import com.youibot.vehicle.scheduler.common.entity.QueryCol;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.impl.BaseServiceImpl;
import com.youibot.vehicle.scheduler.common.thread.ThreadUtils;
import com.youibot.vehicle.scheduler.common.utils.AGVPropertiesUtils;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.ExcelUtils;
import com.youibot.vehicle.scheduler.common.utils.PageUtil;
import com.youibot.vehicle.scheduler.engine.execute.constant.ParamConstant;
import com.youibot.vehicle.scheduler.engine.execute.entity.TaskContext;
import com.youibot.vehicle.scheduler.engine.execute.node.DefaultNode;
import com.youibot.vehicle.scheduler.engine.execute.pool.VehicleInstructDataPool;
import com.youibot.vehicle.scheduler.engine.execute.util.TaskUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.log.constant.LogConstant;
import com.youibot.vehicle.scheduler.modules.log.dto.SysLogPushDTO;
import com.youibot.vehicle.scheduler.modules.log.entity.SysLogEntity;
import com.youibot.vehicle.scheduler.modules.log.service.SysLogService;
import com.youibot.vehicle.scheduler.modules.log.util.SysLogUtils;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.entity.PathInfo;
import com.youibot.vehicle.scheduler.modules.map.service.VehicleMapService;
import com.youibot.vehicle.scheduler.modules.notice.service.NoticeRecordService;
import com.youibot.vehicle.scheduler.modules.task.cache.RunningTaskPool;
import com.youibot.vehicle.scheduler.modules.task.constant.TaskConstant;
import com.youibot.vehicle.scheduler.modules.task.dao.TaskDao;
import com.youibot.vehicle.scheduler.modules.task.dao.TaskNodeDao;
import com.youibot.vehicle.scheduler.modules.task.dto.*;
import com.youibot.vehicle.scheduler.modules.task.entity.NodeConfigEntity;
import com.youibot.vehicle.scheduler.modules.task.entity.TaskEntity;
import com.youibot.vehicle.scheduler.modules.task.entity.TaskNodeEntity;
import com.youibot.vehicle.scheduler.modules.task.entity.TaskTypeEntity;
import com.youibot.vehicle.scheduler.modules.task.service.NodeConfigService;
import com.youibot.vehicle.scheduler.modules.task.service.TaskService;
import com.youibot.vehicle.scheduler.modules.task.service.TaskTypeService;
import com.youibot.vehicle.scheduler.modules.task.util.TaskNoGenUtils;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.VehicleTaskDTO;
import com.youibot.vehicle.scheduler.modules.vehicle.entity.VehicleLockEntity;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.impl.DefaultVehiclePool;
import com.youibot.vehicle.scheduler.modules.vehicle.service.VehicleLockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.youibot.vehicle.scheduler.engine.execute.constant.NodeConstant.NODE_NAME_VEHICLE_MOVE;
import static com.youibot.vehicle.scheduler.engine.execute.constant.NodeConstant.NODE_NAME_WHILE;
import static com.youibot.vehicle.scheduler.modules.task.constant.TaskConstant.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TaskServiceImpl extends BaseServiceImpl<TaskDao, TaskEntity> implements TaskService {

    @Autowired
    private TaskDao taskDao;
    @Autowired
    private TaskNodeDao taskNodeDao;
    @Autowired
    private TaskTypeService taskTypeService;
    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private VehicleLockService vehicleLockService;
    @Autowired
    private DefaultVehiclePool defaultVehiclePool;
    @Autowired
    private NoticeRecordService noticeRecordService;
    @Autowired
    private SysLogService sysLogService;
    @Autowired
    private VehicleMapService vehicleMapService;
    @Autowired
    private VehicleInstructDataPool vehicleInstructDataPool;

    @Autowired
    private FlowExecutor flowExecutor;
    @Autowired
    private ThreadPoolTaskExecutor asyncExecutor;

    @Value("${task.exec-max-num}")
    private Integer maxTaskCount;

    @Value("${task.cancel-wait-time}")
    private Integer cancelWaitTime;

    /**
     * 创建任务锁，主要是防止编号重复
     */
    private static final Lock createLock = new ReentrantLock();

    /**
     * 任务取消锁，防止多次点击线程阻塞
     */
    private static final Map<String, ReentrantLock> cancelLock = new ConcurrentHashMap<>();

    /**
     * 监听任务完成事件
     */
    @EventListener
    public void handleEvent(TaskCompleteEvent event) {
        TaskEntity taskEntity = event.getTask();

        //接口回调
        CompletableFuture.runAsync(() -> {
            if (StringUtils.isNotBlank(taskEntity.getCallbackUrl())) {
                int maxCount = 10;
                int tryCount = 0;
                int sleepSecond = 1;
                while (true) {
                    try {
                        TaskDetailApiDTO taskDetailApiDTO = this.taskEntityConvertToTaskDetail(taskEntity);
                        HttpResponse execute = HttpUtil.createPost(taskEntity.getCallbackUrl()).body(JSON.toJSONString(taskDetailApiDTO)).executeAsync();
                        SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_NODE_EXECUTE)
                                .content("log.system.task.status.change.callback.request.param")
                                .data(String.join(",",Arrays.asList(taskEntity.getTaskNo())))
                                .taskNos(taskEntity.getTaskNo()).message(JSON.toJSONString(taskDetailApiDTO)).build());
                        SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_NODE_EXECUTE)
                                .content("log.system.task.status.change.callback.response.param")
                                .data(String.join(",",Arrays.asList(taskEntity.getTaskNo())))
                                .taskNos(taskEntity.getTaskNo()).message(execute.body()).build());
                        break;
                    } catch (Exception e) {
                        log.error("回调失败 taskNo:{} callbackUrl:{}, 当前重试次数:{}", taskEntity.getTaskNo(), taskEntity.getCallbackUrl(), tryCount);
                        if (++tryCount > maxCount) {
                            break;
                        }
                        try {
                            TimeUnit.SECONDS.sleep(sleepSecond);
                        } catch (InterruptedException interruptedException) {
                            interruptedException.printStackTrace();
                        }
                        sleepSecond = sleepSecond * 2;
                    }
                }
            }
        }, asyncExecutor);

        CompletableFuture.runAsync(() -> {
            //更新详情缓存
            updateDetailCache(taskEntity.getId());
        }, asyncExecutor);

        vehicleInstructDataPool.detachByTaskNo(taskEntity.getTaskNo());
    }

    @Override
    public PageData<TaskDTO> page(Map<String, Object> params) {
        IPage<TaskEntity> page = baseDao.selectPage(
                getPage(params, "create_date", false),
                getWrapper(params)
        );
        PageData<TaskDTO> pageData = getPageData(page, TaskDTO.class);
        return pageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<Long> ids) {
        List<TaskEntity> entities = baseDao.selectBatchIds(ids);
        entities = entities.stream().filter(i -> Objects.equals(i.getStatus(), TaskConstant.STATUS_RUNNING)).collect(Collectors.toList());
        if (entities.size() > 0) {
            String message = I18nMessageUtils.getMessage("task.delete.running.error");
            throw new FleetException(message);
        }
        deleteBatchIds(ids);
        QueryWrapper<TaskNodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("task_id", ids);
        taskNodeDao.delete(queryWrapper);
    }

    @Override
    public void export(Map<String, Object> searchMap, HttpServletResponse response) {
        List<TaskDTO> list = list(searchMap);
        if (Objects.nonNull(searchMap.get("ids"))) {
            String ids = (String) searchMap.get("ids");
            List<Long> collect = Stream.of(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
            list.removeIf(i -> !collect.contains(i.getId()));
        }
        List<TaskExportDTO> exportList = ConvertUtils.sourceToTarget(list, TaskExportDTO.class);
        try {
            String fileName = I18nMessageUtils.getMessage("task.export.file.name");
            ExcelUtils.exportExcel(response, fileName, fileName, exportList, TaskExportDTO.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public TaskEntity executeByBusinessSystem(TaskExecuteApiDTO executeDTO, String source) {
        TaskTypeEntity taskType = Optional.ofNullable(taskTypeService.getByCode(executeDTO.getTaskTypeCode()))
                .filter(taskTypeEntity -> TaskConstant.PUBLISHED.equals(taskTypeEntity.getPublishStatus()))
                .orElseThrow(() -> {
                    String message = I18nMessageUtils.getMessage("task.type.is.not.published.error", executeDTO.getTaskTypeCode());
                    return new FleetException(message);
                });
        Map<String, Object> params = Optional.ofNullable(executeDTO.getParams()).orElse(new HashMap<>());
        //参数校验
        paramCheck(taskType.getId(), params);

        TaskEntity task = createTask(taskType, params, source, null, null, executeDTO.getPriority());
        task.setExternalTaskNo(executeDTO.getExternalTaskNo());
        task.setCallbackUrl(executeDTO.getCallbackUrl());
        this.updateById(task);

        //充电与泊车策略创建的任务, 直接执行无需等待
        if (RunningTaskPool.getRunningTasks().size() < maxTaskCount) {
            execute(task);
        } else {
            //当超过最大可执行任务数时进入等待队列
            RunningTaskPool.toWait(task);
        }
        return task;
    }

    @Override
    public List<TaskEntity> selectRunningTask() {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", TaskConstant.STATUS_RUNNING, TaskConstant.STATUS_CREATE);
        //运行中的放前面，让运行中的先恢复任务
        queryWrapper.orderByDesc("status");
        return baseDao.selectList(queryWrapper);
    }

    @Override
    public List<TaskNodeEntity> selectNodesByTaskId(Long taskId) {
        QueryWrapper<TaskNodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.orderByDesc("id");
        return taskNodeDao.selectList(queryWrapper);
    }

    @Override
    public List<TaskNodeEntity> selectNodesByTaskIdAndStatus(Long taskId, String status) {
        QueryWrapper<TaskNodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.eq("status", status);
        queryWrapper.orderByDesc("id");
        return taskNodeDao.selectList(queryWrapper);
    }

    @Override
    public void recoverTask(String taskNo) {
        TaskEntity task = selectByTaskNo(taskNo);
        TaskTypeEntity taskType = taskTypeService.selectById(task.getTaskTypeId());
        if (Objects.equals(taskType.getPublishStatus(), TaskConstant.UNPUBLISHED) || Objects.isNull(FlowBus.getChain(taskType.getCode()))) {
            log.debug("恢复失败，任务类型未发布 type:{}", taskType.getCode());
            cancel(taskNo);
            String message = I18nMessageUtils.getMessage("task.type.is.not.published.error", taskType.getCode());
            throw new FleetException(message);
        }

        if (Objects.equals(task.getStatus(), TaskConstant.STATUS_CREATE)) {
            RunningTaskPool.toWait(task);
            return;
        }

        //参数
        Map<String, Object> param = JSON.parseObject(task.getParamIn(), Map.class);
        //节点
        List<TaskNodeEntity> nodes = selectNodesByTaskId(task.getId());
        nodes = nodes.stream().filter(i -> Objects.equals(i.getStatus(), TaskConstant.NODE_STATUS_FINISHED)).collect(Collectors.toList());
        Map<String, List<TaskNodeEntity>> collect = nodes.stream().collect(Collectors.groupingBy(TaskNodeEntity::getCode));
        collect.values().forEach(l -> l.sort(Comparator.comparing(TaskNodeEntity::getCreateDate)));
        //恢复任务
        Future<LiteflowResponse> future = flowExecutor.execute2Future(taskType.getCode(), param, new TaskContext(task, collect));
        RunningTaskPool.put(task.getTaskNo(), task, future);
    }

    @Override
    public void bindVehicle(String taskNo, String nodeCode, Vehicle vehicle) {
        try {
            TaskEntity task = selectByTaskNo(taskNo);
            List<TaskNodeEntity> nodes = selectNodesByTaskId(task.getId());
            nodes = nodes.stream().filter(i -> Objects.equals(i.getCode(), nodeCode)).collect(Collectors.toList());
            nodes.forEach(node -> {
                if (StringUtils.isBlank(node.getVehicleCode())) {
                    node.setVehicleCode(vehicle.getVehicleCode());
                    taskNodeDao.updateById(node);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<String> selectRunningTaskNos() {
        return taskDao.selectRunningTaskNos();
    }

    @Override
    public void batchToRunning(List<String> taskNos) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("task_no", taskNos);
        List<TaskEntity> taskEntities = baseDao.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(taskEntities)) {
            taskEntities.forEach(t -> t.setStatus(TaskConstant.STATUS_RUNNING));
            updateBatchById(taskEntities);
        }
    }

    @Override
    public TaskEntity getLastTaskByTaskTypeId(Long taskTypeId) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_type_id", taskTypeId);
        queryWrapper.orderByDesc("create_date");
        queryWrapper.last("limit 1");
        return taskDao.selectOne(queryWrapper);
    }

    @Override
    public TaskDTO execute(Long taskTypeId, Map<String, Object> params, String source, String eventCode) {
        return executePriority(taskTypeId, params, source, eventCode, null, null);
    }

    @Override
    public TaskDTO executePriority(Long taskTypeId, Map<String, Object> params, String source, String eventCode, Integer isBreak, Integer priority) {
        params = Optional.ofNullable(params).orElse(new HashMap<>());
        //参数校验
        paramCheck(taskTypeId, params);
        //异步执行作业
        TaskTypeEntity taskType = taskTypeService.selectById(taskTypeId);
        if (taskType == null) {
            String message = I18nMessageUtils.getMessage("task.type.is.not.exist.error", taskTypeId);
            throw new FleetException(message);
        }
        if (Objects.equals(taskType.getPublishStatus(), TaskConstant.UNPUBLISHED) ||
                Objects.isNull(FlowBus.getChain(taskType.getCode()))) {
            log.error("任务类型未发布：{}", taskType.getCode());
            String message = I18nMessageUtils.getMessage("task.type.is.not.published.error", taskType.getCode());
            throw new FleetException(message);
        }
        TaskEntity task = createTask(taskType, params, source, eventCode, isBreak, priority);

        //充电与泊车策略创建的任务, 直接执行无需等待
        if (RunningTaskPool.getRunningTasks().size() < maxTaskCount || SOURCE_CHARGE.equals(source) || SOURCE_PARK.equals(source)) {
            execute(task);
        } else {
            //当超过最大可执行任务数时进入等待队列
            RunningTaskPool.toWait(task);
        }

        return ConvertUtils.sourceToTarget(task, TaskDTO.class);
    }

    @Override
    public void execute(TaskEntity task) {
        //需要从数据库拿，防止锁定点位的数据被清空
        task = selectByTaskNo(task.getTaskNo());
        task.setStartTime(new Date());
        task.setStatus(TaskConstant.STATUS_RUNNING);
        updateById(task);

        SysLogUtils.pushRunningLog(SysLogPushDTO.builder().module(LogConstant.MODULE_NODE_EXECUTE)
                .content("log.system.task.start.run")
                .data(String.join(",",Arrays.asList(task.getTaskNo())))
                .taskNos(task.getTaskNo()).build());
        TaskTypeEntity taskType = taskTypeService.selectById(task.getTaskTypeId());
        Map<String, Object> params = JSONObject.parseObject(JSONUtil.toJsonPrettyStr(task.getParamIn()), Map.class);
        //这里将任务对象作为参数传入liteflow的容器中
        Future<LiteflowResponse> future = flowExecutor.execute2Future(taskType.getCode(), params, new TaskContext(task));
        //同时也将该任务对象放入一个池中，在后续对该任务进行停止时，可以将该池中的该实体的中断标志位进行变更
        //这样子liteflow容器内的引用也会变更，在执行节点时通过判断该标志位进行任务中断
        RunningTaskPool.put(task.getTaskNo(), task, future);
    }

    /**
     * 参数校验
     */
    public void paramCheck(Long taskTypeId, Map<String, Object> params) {
        //参数校验
        List<TaskTypeParamDTO> taskParams = taskTypeService.getParamByTaskTypeIds(Lists.newArrayList(taskTypeId));
        Map<String, TaskTypeParamDTO> collect = taskParams.stream().collect(Collectors.toMap(TaskTypeParamDTO::getVariable, Function.identity()));
        collect.forEach((k, v) -> {
            Object o = params.get(k);
            if (Objects.isNull(o)) {
                if (StringUtils.isBlank(v.getDefaultValue())) {
                    String message = I18nMessageUtils.getMessage("system.code.is.empty.error", k);
                    throw new FleetException(message);
                } else {
                    if (ParamConstant.isSelectListType(v.getType())) {
                        params.put(k, Lists.newArrayList(v.getDefaultValue().split(",")));
                    } else {
                        params.put(k, v.getDefaultValue());
                    }
                }
            } else {
                if (Objects.equals(v.getVariableCategory(), "Number")) {
                    try {
                        double checkNum = Double.parseDouble(String.valueOf(o));
                    } catch (NumberFormatException e) {
                        String message = I18nMessageUtils.getMessage("task.insert.dynamic.param.format.error", k, String.valueOf(o));
                        throw new FleetException(message);
                    }
                }
                if (Objects.equals(o.getClass(), String.class)) {
                    String s = String.valueOf(o);
                    if (ParamConstant.isSelectListType(v.getType())) {
                        //如果是字符串类型的，则认为多个数据之间用英文逗号分隔
                        params.put(k, Lists.newArrayList(s.split(",")));
                    }
                }
            }
            o = params.get(k);
            switch (v.getType()) {
                case ParamConstant.VEHICLE_SELECT:
                    Vehicle vehicle = defaultVehiclePool.getVehicle(String.valueOf(o));
                    if (Objects.isNull(vehicle)) {
                        vehicle = defaultVehiclePool.getVehicleByName(String.valueOf(o));
                    }
                    if (Objects.isNull(vehicle)) {
                        String message = I18nMessageUtils.getMessage("task.insert.vehicle.not.exist.error", String.valueOf(o));
                        throw new FleetException(message);
                    }
                    break;
                case ParamConstant.MULTI_VEHICLE_SELECT:
                    List<String> vehicleCodes = (List<String>) o;
                    vehicleCodes.forEach(code -> {
                        if (Objects.isNull(defaultVehiclePool.getVehicle(code))) {
                            String message = I18nMessageUtils.getMessage("task.insert.vehicle.not.exist.error", code);
                            throw new FleetException(message);
                        }
                    });
                    break;
                case ParamConstant.POINT_SELECT:
                    String c = String.valueOf(o);
                    if (Objects.isNull(MapGraphUtil.getMarkerByMarkerCode(c))) {
                        String message = I18nMessageUtils.getMessage("task.insert.marker.not.exist.error", c);
                        throw new FleetException(message);
                    }
                    break;
                case ParamConstant.MULTI_POINT_SELECT:
                    List<String> markerCodes = (List<String>) o;
                    List<String> lc = TaskUtils.getLockedMarkerIds();
                    markerCodes.forEach(markerCode -> {
                        if (Objects.isNull(MapGraphUtil.getMarkerByMarkerCode(markerCode))) {
                            String message = I18nMessageUtils.getMessage("task.insert.marker.not.exist.error", markerCode);
                            throw new FleetException(message);
                        }
                        if (lc.contains(markerCode)) {
                            String message = I18nMessageUtils.getMessage("task.insert.marker.lock.error", markerCode);
                            throw new FleetException(message);
                        }
                    });
                    break;
                case ParamConstant.MAP_SELECT:
                    if (Objects.isNull(vehicleMapService.selectByCode(String.valueOf(o)))) {
                        String message = I18nMessageUtils.getMessage("task.insert.map.not.exist.error", String.valueOf(o));
                        throw new FleetException(message);
                    }
                    break;
                case ParamConstant.MULTI_MAP_SELECT:
                    List<String> mapCodes = (List<String>) o;
                    mapCodes.forEach(code -> {
                        if (Objects.isNull(vehicleMapService.selectByCode(code))) {
                            String message = I18nMessageUtils.getMessage("task.insert.map.not.exist.error", code);
                            throw new FleetException(message);
                        }
                    });
                    break;
                default:
                    break;
            }
        });
    }

    /**
     * 界面点进来的取消任务不能阻塞，拿不到锁直接返回
     *
     * @param id
     * @return
     */
    @Override
    public boolean cancel(Long id) {
        String taskNo = selectById(id).getTaskNo();
        cancelLock.putIfAbsent(taskNo, new ReentrantLock());
        Lock lock = cancelLock.get(taskNo);
        if (lock.tryLock()) {
            try {
                return cancel(taskNo);
            } finally {
                lock.unlock();
                cancelLock.remove(taskNo);
            }
        } else {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error");
            throw new FleetException(message);
        }
    }

    @Override
    public boolean cancel(String taskNo) {
        boolean timeout = false;
        StopWatch stopWatch = StopWatch.createStarted();
        TaskEntity task = RunningTaskPool.getRunning(taskNo);
        if (Objects.nonNull(task)) {
            task.setInterrupt(Boolean.TRUE);
            while (!RunningTaskPool.isFinished(taskNo)) {
                try {
                    task.setInterrupt(Boolean.TRUE);
                    TimeUnit.MILLISECONDS.sleep(500);
                    log.debug("任务取消中... taskNo:{}", taskNo);
                    if (stopWatch.getTime(TimeUnit.SECONDS) > cancelWaitTime) {
                        timeout = true;
                    }
                } catch (InterruptedException e) {
                    log.error("睡眠出错, ", e);
                }
            }
            log.debug("任务取消完成 taskNo:{}", taskNo);
        } else {
            RunningTaskPool.remove(taskNo);
            task = selectByTaskNo(taskNo);
            if (Objects.nonNull(task) && Lists.newArrayList(TaskConstant.STATUS_RUNNING, TaskConstant.STATUS_CREATE).contains(task.getStatus())) {
                task.setStatus(TaskConstant.STATUS_CANCEL);
                task.setEndTime(new Date());
                updateById(task);
                vehicleLockService.releaseVehicleLockByTaskCode(taskNo);
            }
        }
        if (timeout) {
            String vehicleCodes = task.getVehicleCodes();
            String message = I18nMessageUtils.getMessage("task.cancel.timeout.error", vehicleCodes);
            throw new FleetException(message);
        }
        return true;
    }

    /**
     * 找到当前任务中运行的节点中可以跳过的节点，跳过
     */
    @Override
    public boolean skipTaskNodeByTaskId(Long taskId) {
        TaskEntity taskEntity = selectById(taskId);
        if (taskEntity == null) {
            String message = I18nMessageUtils.getMessage("task.is.not.exist.error", String.valueOf(taskId));
            throw new FleetException(message);
        }

        //过滤出当前任务执行错误的节点
        List<TaskNodeEntity> taskNodes = selectNodesByTaskId(taskId);
        List<Long> configIdList = taskNodes.stream().map(TaskNodeEntity::getNodeConfigId).collect(Collectors.toList());
        List<NodeConfigEntity> configEntityList = nodeConfigService.getListByIds(configIdList);
        Map<Long, NodeConfigEntity> nodeConfigMap = configEntityList.stream().collect(Collectors.toMap(NodeConfigEntity::getId, k -> k, (k1, k2) -> k1));

        //过滤出可以跳过的节点
        List<TaskNodeEntity> canBeSkipedNodes = new ArrayList<>();
        //1、运行失败，但可以跳过的节点
        List<TaskNodeEntity> failedNodes = taskNodes.stream().filter(n -> TaskConstant.NODE_STATUS_FAIL.equals(n.getStatus())).collect(Collectors.toList());
        List<TaskNodeEntity> failedCanBeSkipedNodes = failedNodes.stream().filter(r -> {
            NodeConfigEntity configEntity = nodeConfigMap.get(r.getNodeConfigId());
            if (configEntity == null) return false;
            //节点配置可跳过, 且节点重试次数已达到配置的的重试次数
            return configEntity.getIsAllowSkip() && (r.getTotalRetryNum() != null && r.getTotalRetryNum() <= Optional.ofNullable(r.getRetryNum()).orElse(0L));
        }).collect(Collectors.toList());
        //2、运行中，但可以跳过的节点
        List<TaskNodeEntity> runningNodes = taskNodes.stream().filter(n -> TaskConstant.NODE_STATUS_RUNNING.equals(n.getStatus())).collect(Collectors.toList());
        List<TaskNodeEntity> runningCanBeSkipedNodes = runningNodes.stream().filter(r -> {
            NodeConfigEntity configEntity = nodeConfigMap.get(r.getNodeConfigId());
            //节点配置可跳过
            return configEntity == null ? false : configEntity.getIsAllowSkip();
        }).collect(Collectors.toList());

        canBeSkipedNodes.addAll(failedCanBeSkipedNodes);
        canBeSkipedNodes.addAll(runningCanBeSkipedNodes);
        if (CollUtil.isEmpty(canBeSkipedNodes)) {
            return false;
        }

        //跳过节点
        List<Long> canBeSkipedNodeIds = canBeSkipedNodes.stream().map(TaskNodeEntity::getId).collect(Collectors.toList());
        LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<TaskNodeEntity>()
                .in(TaskNodeEntity::getId, canBeSkipedNodeIds)
                .set(TaskNodeEntity::getIsSkip, true);
        taskNodeDao.update(null, updateWrapper);
        log.debug("当前任务：{}，跳过的节点集合是：{}", taskEntity, canBeSkipedNodes);

        //同步等待，直到节点跳过完成或超时，再返回结果
        this.waitUntilTimeoutOrFinish(() -> {
            List<TaskNodeEntity> list = taskNodeDao.selectBatchIds(canBeSkipedNodeIds);
            return list.stream().noneMatch(node -> NODE_STATUS_RUNNING.equals(node.getStatus()));
        }, 20 * 1000, 1000);
        updateDetailCache(taskId);
        return true;
    }

    @Override
    public boolean skipTaskNode(Long taskNodeId) {
        TaskNodeEntity taskNode = taskNodeDao.selectById(taskNodeId);
        if (taskNode == null) {
            String message = I18nMessageUtils.getMessage("task.node.is.not.exist.error", String.valueOf(taskNodeId));
            throw new FleetException(message);
        }
        NodeConfigEntity configEntity = nodeConfigService.selectById(taskNode.getNodeConfigId());
        if (configEntity == null || !configEntity.getIsAllowSkip()) {
            String message = I18nMessageUtils.getMessage("task.node.is.not.allow.skip.error", String.valueOf(taskNodeId));
            throw new FleetException(message);
        }
        LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<TaskNodeEntity>()
                .eq(TaskNodeEntity::getId, taskNodeId)
                .set(TaskNodeEntity::getIsSkip, true);
        taskNodeDao.update(null, updateWrapper);
        log.debug("当前跳过的节点是：{}", taskNode);

        //同步等待，直到节点跳过完成或超时，再返回结果
        waitUntilTimeoutOrFinish(() -> {
            TaskNodeEntity node = taskNodeDao.selectById(taskNodeId);
            return !NODE_STATUS_RUNNING.equals(node.getStatus());
        }, 20 * 1000, 1000);

        updateDetailCache(taskNode.getTaskId());
        return true;
    }

    @Override
    public boolean retryTaskNode(Long taskNodeId) {
        TaskNodeEntity taskNode = taskNodeDao.selectById(taskNodeId);
        if (taskNode == null) {
            String message = I18nMessageUtils.getMessage("task.node.is.not.exist.error", String.valueOf(taskNodeId));
            throw new FleetException(message);
        }
        NodeConfigEntity configEntity = nodeConfigService.selectById(taskNode.getNodeConfigId());
        if (configEntity == null || !configEntity.getIsAllowRetry()) {
            String message = I18nMessageUtils.getMessage("task.node.is.not.allow.retry.error", String.valueOf(taskNodeId));
            throw new FleetException(message);
        }
        //如果为null将无限重试
        if (taskNode.getTotalRetryNum() == null) {
            return true;
        }
        log.debug("当前重试的节点是：{}", taskNode);
        //此处如果总次数大于0，则给0，继续重试totalRetryNum次，如果是0，则-1，最多重试一次
        long retryNum = taskNode.getTotalRetryNum() <= 0 ? taskNode.getTotalRetryNum() - 1 : 0;
        //将当前节点的重试次数重置
        LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<TaskNodeEntity>()
                .eq(TaskNodeEntity::getId, taskNodeId)
                .set(TaskNodeEntity::getRetryNum, retryNum);
        taskNodeDao.update(null, updateWrapper);
        updateDetailCache(taskNode.getTaskId());
        return true;
    }

    /**
     * 线程等待，直到supplier返回true，或者timeout超时，期间每次睡眠sleepTime
     *
     * @param supplier
     * @param timeout
     * @param sleepTime
     * @return
     */
    private boolean waitUntilTimeoutOrFinish(Supplier<Boolean> supplier, long timeout, long sleepTime) {
        long start = System.currentTimeMillis();
        boolean timeoutExit = false;
        while (true) {
            if (supplier.get()) {
                break;
            }
            if (System.currentTimeMillis() - start > timeout) {
                timeoutExit = true;
                break;
            }
            ThreadUtils.sleep(sleepTime);
        }
        return timeoutExit;
    }


    @Override
    public TaskNodeEntity createTaskNode(Long taskId, TaskNodeCreateDTO create) {
        NodeConfigEntity nodeConfig = nodeConfigService.getByType(create.getType());
        TaskNodeEntity taskNode = new TaskNodeEntity();
        taskNode.setTaskId(taskId);
        taskNode.setNodeConfigId(Objects.nonNull(nodeConfig) ? nodeConfig.getId() : 0);
        taskNode.setCode(create.getCode());
        taskNode.setName(create.getName());
        taskNode.setType(create.getType());
        taskNode.setCategory(create.getCategory());
        taskNode.setStatus(TaskConstant.NODE_STATUS_RUNNING);
        taskNode.setTotalRetryNum(Objects.nonNull(nodeConfig) ? nodeConfig.getRetryNum() : null);
        taskNode.setParamIn(create.getParamIn());
        taskNode.setStartTime(new Date());
        taskNode.setCreateDate(new Date());
        taskNodeDao.insert(taskNode);
        return taskNode;
    }

    @Override
    public void updateNodeStatus(Long taskNodeId, String status, String outParam) {
        TaskNodeEntity taskNode = taskNodeDao.selectById(taskNodeId);
        if (Objects.nonNull(taskNode)) {
            taskNode.setStatus(status);
            taskNode.setUpdateDate(new Date());
            if (Objects.equals(status, TaskConstant.NODE_STATUS_CANCEL) || Objects.equals(status, TaskConstant.NODE_STATUS_FINISHED)) {
                taskNode.setEndTime(new Date());
                taskNode.setParamOut(outParam);
                taskNodeDao.updateById(taskNode);
                CompletableFuture.runAsync(() -> updateDetailCache(taskNode.getTaskId()), asyncExecutor);
            } else {
                taskNodeDao.updateById(taskNode);
                //更新详情缓存
                CompletableFuture.runAsync(() -> updateDetailCache(taskNode.getTaskId()), asyncExecutor);
            }
        }
    }

    @Override
    public void appendVehicle(String taskNo, Set<String> vehicleCodes) {
        try {
            TaskEntity task = selectByTaskNo(taskNo);
            String existVehicles = task.getVehicleCodes();
            if (StringUtils.isNotBlank(existVehicles)) {
                vehicleCodes.addAll(Sets.newHashSet(existVehicles.split(TaskConstant.TASK_VEHICLE_SPLIT)));
            }
            List<String> list = Lists.newArrayList(vehicleCodes);
            Collections.sort(list);
            task.setVehicleCodes(String.join(TaskConstant.TASK_VEHICLE_SPLIT, list));
            updateById(task);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public TaskEntity selectByTaskNo(String taskNo) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_no", taskNo);
        return baseDao.selectOne(queryWrapper);
    }

    @Override
    public TaskEntity selectByExternalTaskNo(String externalTaskNo) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("external_task_no", externalTaskNo);
        queryWrapper.orderByDesc("create_date");
        queryWrapper.last("limit 1");
        return baseDao.selectOne(queryWrapper);
    }

    @Override
    public List<TaskEntity> selectRunningTaskByCode(String vehicleCode, String source) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("source", source);
        queryWrapper.in("status", Arrays.asList("Create", "Running"));
        List<TaskEntity> taskEntities = baseDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(taskEntities)) {
            return null;
        }
        taskEntities = taskEntities.stream().filter(taskEntity -> taskEntity.getParamIn() != null && vehicleCode.equals(JSONObject.parseObject(taskEntity.getParamIn()).getString("vehicleCode"))).collect(Collectors.toList());
        return taskEntities;
    }

    @Override
    public List<TaskEntity> selectRunningTaskBySource(String source) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("source", source);
        queryWrapper.in("status", Arrays.asList("Create", "Running"));
        List<TaskEntity> taskEntities = baseDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(taskEntities)) {
            return null;
        }
        return taskEntities;
    }

    @Override
    public List<TaskEntity> getTaskByQueryWrapper(LambdaQueryWrapper<TaskEntity> queryWrapper) {
        List<TaskEntity> taskEntities = baseDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(taskEntities)) {
            return null;
        }
        return taskEntities;
    }

    @Override
    public String getMaxTaskNo() {
        return Optional.ofNullable(taskDao.selectOne(new QueryWrapper<TaskEntity>().select("max(task_no) as task_no"))).map(TaskEntity::getTaskNo).orElse(null);
    }

    /**
     * 构建初始化状态的任务
     */
    private TaskEntity createTask(TaskTypeEntity taskType, Map<String, Object> params, String source, String eventCode, Integer isBreak, Integer priority) {
        TaskEntity entity = TaskEntity.builder()
                .taskTypeId(taskType.getId())
                .taskTypeCode(taskType.getCode())
                .taskNo(getMaxTaskNoWithMemo())
                .name(taskType.getName())
                .priority(priority == null ? taskType.getPriority() : priority)
                .status(TaskConstant.STATUS_CREATE)
                .source(source)
                .eventCode(eventCode)
                .isBreak(isBreak == null ? 1 : isBreak)
                .isCancel(1)
                .paramIn(JSON.toJSONString(params))
                .build();
        insert(entity);
        return entity;
    }

    private static String maxTaskNo = null;

    private synchronized String getMaxTaskNoWithMemo() {
        if (StringUtils.isBlank(maxTaskNo)) {
            maxTaskNo = this.getMaxTaskNo();
        }
        maxTaskNo = TaskNoGenUtils.genCode(() -> maxTaskNo);
        return maxTaskNo;
    }

    @Override
    public List<TaskDTO> list(Map<String, Object> params) {
        List<TaskEntity> taskEntities = baseDao.selectList(getWrapper(params));
        return ConvertUtils.sourceToTarget(taskEntities, TaskDTO.class);
    }

    @Override
    public TaskNodeEntity getFailTaskNodeByNodeCode(Long taskId, String nodeCode) {
        QueryWrapper<TaskNodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.eq("code", nodeCode);
        queryWrapper.in("status", TaskConstant.NODE_STATUS_FAIL, TaskConstant.NODE_STATUS_RUNNING);
        queryWrapper.last("limit 1");
        return taskNodeDao.selectOne(queryWrapper);
    }


    /**
     * 详情缓存
     */
    private static final Cache<Long, TaskDetailDTO> DETAIL_CACHE = CacheBuilder.newBuilder().expireAfterAccess(4, TimeUnit.SECONDS).build();

    /**
     * 当前正在查看的任务详情
     */
    private static final Cache<Long, Long> DETAIL_WATCH = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.SECONDS).build();

    @Override
    public TaskDetailDTO getDetail(Long taskId) {
        DETAIL_WATCH.put(taskId, taskId);
        TaskDetailDTO detail = DETAIL_CACHE.getIfPresent(taskId);
        if (Objects.isNull(detail)) {
            updateDetailCache(taskId);
        }
        detail = DETAIL_CACHE.getIfPresent(taskId);
        //运行中的任务时间实时更新
        if (Objects.nonNull(detail) && Objects.equals(detail.getStatus(), TaskConstant.STATUS_RUNNING)) {
            detail.setExecutionDuration((int) (getDuration(detail.getStartTime(), detail.getEndTime()) / 1000));
            detail.getNodeDetail().values().stream().filter(i -> Objects.equals(i.getStatus(), TaskConstant.NODE_STATUS_RUNNING)).forEach(d -> {
                int now = (int) (getDuration(d.getStartTime(), d.getEndTime()) / 1000);
                d.setExecutionDuration(now);
            });
            //是否允许重试，需要实时更新
        }
        return detail;
    }

    @Override
    public TaskDetailDTO getDetail(String taskNo) {
        return Optional.ofNullable(selectByTaskNo(taskNo)).map(t -> getDetail(t.getId())).orElse(null);
    }

    /**
     * 更新详情缓存 ，在节点变为运行中状态或者失败状态时主动调用，以及任务取消或者结束时主动调用
     *
     * @param taskId
     */
    @Override
    public void updateDetailCache(Long taskId) {
        if (Objects.isNull(DETAIL_WATCH.getIfPresent(taskId))) {
            DETAIL_CACHE.asMap().remove(taskId);
            return;
        }
        synchronized (taskId.toString().intern()) {
            TaskEntity task = taskDao.selectById(taskId);
            TaskDetailDTO detail = TaskDetailDTO.builder()
                    .taskId(taskId)
                    .taskTypeId(task.getTaskTypeId())
                    .taskNo(task.getTaskNo())
                    .name(task.getName())
                    .status(task.getStatus())
                    .paramIn(JSONObject.parseObject(task.getParamIn()))
                    .paramOut(JSONObject.parseObject(task.getParamOut()))
                    .startTime(task.getStartTime())
                    .endTime(task.getEndTime())
                    .executionDuration((int) (getDuration(task.getStartTime(), task.getEndTime()) / 1000)).build();

            List<TaskNodeEntity> taskNodes = selectNodesByTaskId(taskId);
            if (CollectionUtils.isNotEmpty(taskNodes)) {
                Map<String, List<TaskNodeEntity>> nodeMap = taskNodes.stream().collect(Collectors.groupingBy(TaskNodeEntity::getCode));

                List<Long> configIdList = taskNodes.stream().map(TaskNodeEntity::getNodeConfigId).collect(Collectors.toList());
                List<NodeConfigEntity> configEntityList = nodeConfigService.getListByIds(configIdList);
                Map<Long, NodeConfigEntity> nodeConfigMap = configEntityList.stream().collect(Collectors.toMap(NodeConfigEntity::getId, k -> k, (k1, k2) -> k1));

                Map<String, TaskNodeDetailDTO> nodeDetail = new HashMap<>();
                AtomicReference<Boolean> failFlag = new AtomicReference<>(false);
                nodeMap.forEach((code, nodes) -> {
                    if (CollectionUtils.isNotEmpty(nodes)) {
                        nodes.sort(Comparator.comparing(TaskNodeEntity::getCreateDate).reversed());
                        TaskNodeEntity n = nodes.get(0);
                        NodeConfigEntity configEntity = nodeConfigMap.get(n.getNodeConfigId());

                        //是否允许重试
                        boolean isAllowRetry = false;
                        if (Objects.equals(n.getStatus(), TaskConstant.NODE_STATUS_FAIL)) {
                            isAllowRetry = configEntity == null ? false : configEntity.getIsAllowRetry();
                            Long nodeRetryNum = n.getRetryNum() == null ? 0 : n.getRetryNum();
                            isAllowRetry = isAllowRetry && n.getTotalRetryNum() != null && nodeRetryNum >= n.getTotalRetryNum();
                        }

                        //是否允许跳过
                        boolean isAllowSkip = false;
                        if (Objects.equals(n.getStatus(), TaskConstant.NODE_STATUS_FAIL)) {
                            isAllowSkip = configEntity == null ? false : configEntity.getIsAllowSkip();
                            Long nodeRetryNum = n.getRetryNum() == null ? 0 : n.getRetryNum();
                            isAllowSkip = isAllowSkip && (n.getTotalRetryNum() == null || nodeRetryNum >= n.getTotalRetryNum());
                        }

                        TaskNodeDetailDTO nd = TaskNodeDetailDTO.builder()
                                .id(n.getId())
                                .code(n.getCode())
                                .name(n.getName())
                                .type(n.getType())
                                .isAllowSkip(isAllowSkip)
                                .isSkip(n.getIsSkip())
                                .isAllowRetry(isAllowRetry)
                                .status(n.getStatus())
                                .paramOut(Optional.ofNullable(n.getParamOut()).map(JSONObject::parseObject).orElse(null))
                                .startTime(n.getStartTime())
                                .endTime(n.getEndTime())
                                .executionDuration((int) (getDuration(n.getStartTime(), n.getEndTime())) / 1000)
                                .runningCount(nodes.size())
                                .build();
                        if (Objects.equals(n.getStatus(), TaskConstant.NODE_STATUS_FAIL)) {
                            failFlag.set(true);
                        }
                        if (!DefaultNode.JUDGE_TYPE.contains(n.getType())) {
                            nd.setParamIn(JSONObject.parseObject(n.getParamIn()));
                        }
                        this.updateNodeDetail(nodes, n, nd, detail);
                        nodeDetail.put(code, nd);
                    }
                });
                detail.setNodeDetail(nodeDetail);
                if (failFlag.get()) {
                    detail.setNoticeRecord(noticeRecordService.getLastRecordByTaskId(task.getTaskNo()));
                }
            }
            DETAIL_CACHE.put(taskId, detail);
        }
    }

    /**
     * 根据节点类型更新节点详情
     *
     * @param currentNode
     * @param nodeDetail
     */
    private void updateNodeDetail(List<TaskNodeEntity> nodes,
                                  TaskNodeEntity currentNode,
                                  TaskNodeDetailDTO nodeDetail,
                                  TaskDetailDTO detail) {
        switch (currentNode.getType()) {
            case NODE_NAME_WHILE:
                //如果当前节点是循环，则根据节点返回的执行结果，如果是true，则将状态置为运行中
                JSONObject paramOut = JSONObject.parseObject(currentNode.getParamOut());
                if (paramOut != null) {
                    Boolean condition = paramOut.getBoolean("condition");
                    if (TaskConstant.STATUS_RUNNING.equals(detail.getStatus()) && condition) {
                        nodeDetail.setStatus(TaskConstant.NODE_STATUS_RUNNING);
                    }
                    nodeDetail.setRunningCount(condition ? nodeDetail.getRunningCount() : nodeDetail.getRunningCount() - 1);
                }
                //开始时间是第一条循环记录的开始时间
                TaskNodeEntity firstNode = nodes.get(nodes.size() - 1);
                nodeDetail.setStartTime(firstNode.getStartTime());
                //运行中时，结束时间置空
                if (NODE_STATUS_RUNNING.equals(nodeDetail.getStatus())) {
                    nodeDetail.setEndTime(null);
                }
                break;
            case NODE_NAME_VEHICLE_MOVE:
                //如果是路径导航节点，需要计算：0、机器人移动总距离 1、机器人移动时剩下的距离 2、剩余时间
                double totalDistance = 0.0;
                double leftDistance = 0.0;
                if (NODE_STATUS_RUNNING.equals(nodeDetail.getStatus())) {
                    Vehicle vehicle = Optional.ofNullable(currentNode.getVehicleCode())
                            .map(vehicleCode -> defaultVehiclePool.getVehicle(vehicleCode))
                            .orElse(null);
                    if (vehicle != null) {
                        String locatingCode = vehicle.selectLocatingCode();
                        //剩余距离
                        List<Path> executingPaths = Lists.newArrayList();
                        executingPaths.addAll(vehicle.getOccupyPaths());
                        executingPaths.addAll(vehicle.getPlanedPaths());
                        leftDistance = getPathLength(executingPaths, locatingCode);
                        //总距离
                        List<Path> executedPaths = vehicle.getExecutedPaths();
                        totalDistance = getPathLength(executedPaths, locatingCode);
                        totalDistance += leftDistance;
                    }
                }
                nodeDetail.setTotalDistance(BigDecimal.valueOf(totalDistance).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                nodeDetail.setLeftDistance(BigDecimal.valueOf(leftDistance).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                break;
            default:
                break;
        }
    }

    private double getPathLength(List<Path> paths, String locatingCode) {
        double length = 0.0;
        if (CollUtil.isNotEmpty(paths)) {
            for (Path p : paths) {
                if (CollUtil.isEmpty(p.getPathInfos())) continue;
                PathInfo pathInfo = p.getPathInfos().stream().filter(i -> i.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                if (pathInfo == null) continue;
                length += pathInfo.getLength();
            }
        }
        return length;
    }

    @Override
    public PageData<RunningTaskDTO> runningList(Map<String, Object> searchMap) {
        List<TaskEntity> list = RunningTaskPool.getRunningTasks();
        if (Objects.nonNull(searchMap.get("taskName")) && StringUtils.isNotBlank(searchMap.get("taskName").toString())) {
            list = list.stream().filter(i -> i.getName().contains(searchMap.get("taskName").toString())).collect(Collectors.toList());
        }
        List<Long> taskIds = list.stream().map(TaskEntity::getId).collect(Collectors.toList());
        //数据库的数据最真实
        if (!taskIds.isEmpty()) {
            list = baseDao.selectBatchIds(taskIds);
        }
        List<RunningTaskDTO> runningTasks = list.stream().map(i -> {
            RunningTaskDTO t = RunningTaskDTO.builder().taskId(i.getId()).taskTypeId(i.getTaskTypeId()).taskNo(i.getTaskNo()).taskName(i.getName()).vehicleCodes(i.getVehicleCodes()).externalTaskNo(i.getExternalTaskNo()).createDate(i.getCreateDate()).isCancel(i.getIsCancel()).build();
            TaskNodeEntity node = selectLastNodesByTaskId(i.getId());
            if (Objects.nonNull(node)) {
                t.setCurrentNode(node.getName());
                t.setStatus(Objects.equals(node.getStatus(), TaskConstant.NODE_STATUS_FAIL) ? TaskConstant.TASK_RUNNING_STATUS_ABNORMAL : TaskConstant.TASK_RUNNING_STATUS_NORMAL);
            }
            return t;
        }).collect(Collectors.toList());

        if (Objects.nonNull(searchMap.get("status"))) {
            runningTasks = runningTasks.stream().filter(i -> Objects.equals(i.getStatus(), searchMap.get("status").toString())).collect(Collectors.toList());
        }
        runningTasks = sortRunningList(runningTasks);
        return PageUtil.getPage(runningTasks, searchMap.get(Constant.PAGE) == null ? null : Integer.valueOf(searchMap.get(Constant.PAGE).toString()), searchMap.get(Constant.LIMIT) == null ? null : Integer.valueOf(searchMap.get(Constant.LIMIT).toString()));
    }

    /**
     * 数据排序方式：先排序异常的任务，再按照绑定的机器人编排正序排列，再按照任务创建时间倒叙排列
     *
     * @param runningTasks
     */
    private List<RunningTaskDTO> sortRunningList(List<RunningTaskDTO> runningTasks) {
        List<RunningTaskDTO> normal = new ArrayList<>();
        List<RunningTaskDTO> abnormal = new ArrayList<>();
        runningTasks.forEach(t -> {
            if (TaskConstant.TASK_RUNNING_STATUS_ABNORMAL.equals(t.getStatus())) {
                abnormal.add(t);
            } else {
                normal.add(t);
            }
        });
        normal.sort(comparator);
        abnormal.sort(comparator);
        List<RunningTaskDTO> result = new ArrayList<>();
        result.addAll(abnormal);
        result.addAll(normal);
        return result;
    }

    private Comparator<RunningTaskDTO> comparator = (r1, r2) -> {
        if (StringUtils.isBlank(r1.getVehicleCodes()) && StringUtils.isBlank(r2.getVehicleCodes())) {
            if (r1.getCreateDate() == null) {
                return 1;
            }
            if (r2.getCreateDate() == null) {
                return -1;
            }
            return r2.getCreateDate().compareTo(r1.getCreateDate());
        } else if (StringUtils.isNotBlank(r1.getVehicleCodes()) && StringUtils.isNotBlank(r2.getVehicleCodes())) {
            int compare = r1.getVehicleCodes().compareTo(r2.getVehicleCodes());
            if (compare != 0) {
                return compare;
            }
            if (r1.getCreateDate() == null) {
                return 1;
            }
            if (r2.getCreateDate() == null) {
                return -1;
            }
            return r2.getCreateDate().compareTo(r1.getCreateDate());
        } else {
            if (StringUtils.isBlank(r1.getVehicleCodes())) {
                return 1;
            }
            return -1;
        }
    };


    @Override
    public VehicleTaskDTO getVehicleTask(String vehicleCode) {
        VehicleLockEntity lock = vehicleLockService.getVehicleLockByVehicleCode(vehicleCode);
        if (Objects.isNull(lock) || StringUtils.isBlank(lock.getTaskCode())) {
            return null;
        }
        TaskEntity task = RunningTaskPool.getRunning(lock.getTaskCode());
        if (Objects.isNull(task)) {
            task = selectByTaskNo(lock.getTaskCode());
        }
        VehicleTaskDTO build = VehicleTaskDTO.builder().taskId(task.getId()).taskTypeId(task.getTaskTypeId()).taskNo(task.getTaskNo()).taskName(task.getName()).isCancel(task.getIsCancel()).build();
        List<TaskNodeEntity> taskNodes = selectRunningNodesByTaskId(task.getId());
        if (!taskNodes.isEmpty()) {
            List<VehicleTaskDTO.VehicleTaskNode> collect = taskNodes.stream().map(taskNode -> {
                long time = (Optional.ofNullable(taskNode.getEndTime()).orElse(new Date()).getTime() - taskNode.getStartTime().getTime()) / 1000;
                return VehicleTaskDTO.VehicleTaskNode.builder().vehicleCode(Optional.ofNullable(taskNode.getVehicleCode()).orElse("")).currentNode(taskNode.getName()).executionDuration(getDurationStr(time)).build();
            }).sorted(Comparator.comparing(a -> !Objects.equals(a.getVehicleCode(), vehicleCode))).collect(Collectors.toList());
            build.setTaskNodes(collect);
        }
        return build;
    }

    @Override
    public List<String> getRunningChargeTaskVehicleList() {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("source", SOURCE_CHARGE);
        queryWrapper.in("status", Arrays.asList("Create", "Running"));
        List<TaskEntity> taskEntities = baseDao.selectList(queryWrapper);
        return taskEntities.stream().map(t -> JSONObject.parseObject(t.getParamIn()).getString("vehicleCode")).collect(Collectors.toList());
    }

    private String getDurationStr(long secondTime) {
        long hour = secondTime / (60 * 60);
        long minute = (secondTime % (60 * 60)) / 60;
        long second = secondTime % 60;
        Function<Long, String> f = t -> t < 10 ? "0" + t : String.valueOf(t);
        return f.apply(hour) + ":" + f.apply(minute) + ":" + f.apply(second);
    }

    @Override
    public List<TaskNodeEntity> selectRunningNodesByTaskId(Long taskId) {
        QueryWrapper<TaskNodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.in("status", TaskConstant.NODE_STATUS_RUNNING, TaskConstant.NODE_STATUS_FAIL);
        queryWrapper.orderByDesc("update_date");
        return taskNodeDao.selectList(queryWrapper);
    }

    @Override
    public TaskNodeEntity selectLastNodesByTaskId(Long taskId) {
        QueryWrapper<TaskNodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.in("status", TaskConstant.NODE_STATUS_RUNNING, TaskConstant.NODE_STATUS_FAIL);
        queryWrapper.orderByDesc("update_date");
        queryWrapper.last("limit 1");
        return taskNodeDao.selectOne(queryWrapper);
    }

    @Override
    public void exportDetail(Long taskId, HttpServletResponse response) {
        OutputStream os = null;
        try {
            TaskDetailExportDTO export = new TaskDetailExportDTO();
            TaskEntity task = selectById(taskId);
            export.setTask(task);
            export.setTaskNodeList(selectNodesByTaskId(taskId));
            export.setTaskType(taskTypeService.info(export.getTask().getTaskTypeId()));
            export.setNoticeRecord(noticeRecordService.getLastRecordByTaskId(task.getTaskNo()));
            export.setSysLogList(sysLogService.listByTaskNo(task.getTaskNo()));

            byte[] bytes = JSON.toJSONString(export).getBytes();
            // 将格式化后的字符串写入文件
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(export.getTask().getTaskNo().getBytes(), StandardCharsets.ISO_8859_1) + ".json");
            response.addHeader("Content-Length", "" + bytes.length);
            os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            os.write(bytes);// 输出文件
            os.flush();
            os.close();
        } catch (Exception e) {
            log.error("导出失败 ", e);
            String message = I18nMessageUtils.getMessage("task.export.error", e.getMessage());
            throw new FleetException(message);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importDetail(MultipartFile multiPartFile) {
        try {
            String taskTypeStr = new String(multiPartFile.getBytes(), StandardCharsets.UTF_8);
            TaskDetailExportDTO dto = JSONObject.parseObject(taskTypeStr, TaskDetailExportDTO.class);
            TaskEntity exist = selectByTaskNo(dto.getTask().getTaskNo());
            if (Objects.nonNull(exist)) {
                String message = I18nMessageUtils.getMessage("task.import.code.duplicate.error", exist.getTaskNo());
                throw new FleetException(message);
            }
            taskDao.insert(dto.getTask());
            batchInsertNode(dto.getTaskNodeList());
            taskTypeService.deleteByCode(dto.getTaskType().getCode());
            taskTypeService.save(dto.getTaskType());
            Optional.ofNullable(dto.getNoticeRecord()).ifPresent(i -> {
                noticeRecordService.deleteById(i.getId());
                noticeRecordService.insert(i);
            });
            if (CollectionUtils.isNotEmpty(dto.getSysLogList())) {
                sysLogService.deleteByTaskNo(dto.getTask().getTaskNo());
                List<List<SysLogEntity>> partition = Lists.partition(dto.getSysLogList(), 100);
                partition.forEach(p -> sysLogService.insertBatch(p));
            }
        } catch (Exception e) {
            log.error("导入失败", e);
            String msg = e.getMessage();
            if (e instanceof FleetException) {
                msg = ((FleetException) e).getMsg();
            }
            String message = I18nMessageUtils.getMessage("task.import.error", msg);
            throw new FleetException(message);
        }
    }

    private void batchInsertNode(List<TaskNodeEntity> taskNodeList) {
        if (CollectionUtils.isNotEmpty(taskNodeList)) {
            List<List<TaskNodeEntity>> partition = Lists.partition(taskNodeList, 100);
            partition.forEach(l -> taskNodeDao.insertBatch(l));
        }
    }

    public QueryWrapper<TaskEntity> getWrapper(Map<String, Object> params) {
        return super.getWrapper(params, QueryCol.builder().eqCol("isBreak").likeCol("taskNo,eventCode,externalTaskNo,name,remark,vehicleCodes,callbackUrl").inCol("status,priority,source").timeCol("createDate,startTime,endTime").build());
    }

    private long getDuration(Date start, Date end) {
        if (start == null) {
            return 0L;
        }
        return Optional.ofNullable(end).orElse(new Date()).getTime() - start.getTime();
    }

    @Override
    public List<TaskEntity> selectByCreateTimeInterval(Date time1, Date time2) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("create_date", time1, time2);
        return taskDao.selectList(queryWrapper);
    }

    @Override
    public List<TaskEntity> selectByEndTimeInterval(Date time1, Date time2) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("end_time", time1, time2);
        return taskDao.selectList(queryWrapper);
    }

    @Override
    public List<TaskEntity> selectByBeforeStartTimeAndAfterEndTime(Date time) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("start_time").or(qr -> qr.lt("start_time", time));
        queryWrapper.and(qr -> qr.isNull("end_time").or(qr2 -> qr2.ge("end_time", time)));
        return taskDao.selectList(queryWrapper);
    }

    @Override
    public TaskEntity remark(TaskRemarkDTO remarkDTO) {
        TaskEntity taskEntity = selectById(remarkDTO.getTaskId());
        if (taskEntity != null) {
            taskEntity.setRemark(remarkDTO.getRemark());
            updateById(taskEntity);
        }
        return taskEntity;
    }

    @Override
    public PageData<RunningTaskDTO> pdaRunningList(Map<String, Object> searchMap) {
        List<TaskEntity> list = RunningTaskPool.getRunningTasks();
        if (Objects.nonNull(searchMap.get("taskName")) && StringUtils.isNotBlank(searchMap.get("taskName").toString())) {
            list = list.stream().filter(i -> i.getName().contains(searchMap.get("taskName").toString())).collect(Collectors.toList());
        }
        List<Long> taskIds = list.stream().map(TaskEntity::getId).collect(Collectors.toList());
        //数据库的数据最真实
        if (!taskIds.isEmpty()) {
            list = baseDao.selectBatchIds(taskIds);
        }
        List<RunningTaskDTO> runningTasks = list.stream().map(i -> {
            RunningTaskDTO t = RunningTaskDTO.builder().taskId(i.getId()).taskTypeId(i.getTaskTypeId()).taskNo(i.getTaskNo()).taskName(i.getName()).vehicleCodes(i.getVehicleCodes()).externalTaskNo(i.getExternalTaskNo()).createDate(i.getCreateDate()).build();
            TaskNodeEntity node = selectLastNodesByTaskId(i.getId());
            if (Objects.nonNull(node)) {
                t.setCurrentNode(node.getName());
                t.setStatus(Objects.equals(node.getStatus(), TaskConstant.NODE_STATUS_FAIL) ? TaskConstant.TASK_RUNNING_STATUS_ABNORMAL : TaskConstant.TASK_RUNNING_STATUS_NORMAL);
            }
            return t;
        }).collect(Collectors.toList());

        if (Objects.nonNull(searchMap.get("status"))) {
            runningTasks = runningTasks.stream().filter(i -> Objects.equals(i.getStatus(), searchMap.get("status").toString())).collect(Collectors.toList());
        }
        runningTasks = pdaSortRunningList(runningTasks);
        return PageUtil.getPage(runningTasks, searchMap.get(Constant.PAGE) == null ? null : Integer.valueOf(searchMap.get(Constant.PAGE).toString()), searchMap.get(Constant.LIMIT) == null ? null : Integer.valueOf(searchMap.get(Constant.LIMIT).toString()));
    }

    @Override
    public TaskDetailApiDTO taskEntityConvertToTaskDetail(TaskEntity taskEntity) {
        TaskDetailApiDTO taskDetailDTO = this.getTaskDetailDTO(taskEntity);
        List<TaskNodeEntity> nodeList = this.selectNodesByTaskId(taskEntity.getId());
        if (CollUtil.isNotEmpty(nodeList)) {
            List<TaskNodeDetailApiDTO> taskNodeDetails = new ArrayList<>();
            Map<String, List<TaskNodeEntity>> nodeMap = nodeList.stream().collect(Collectors.groupingBy(TaskNodeEntity::getCode));
            for (Map.Entry<String, List<TaskNodeEntity>> item : nodeMap.entrySet()) {
                taskNodeDetails.add(this.getTaskNodeDetailDTO(taskDetailDTO, item));
            }
            taskDetailDTO.setTaskNodeDetails(taskNodeDetails);
        }
        return taskDetailDTO;
    }

    private TaskDetailApiDTO getTaskDetailDTO(TaskEntity taskEntity) {
        return TaskDetailApiDTO.builder()
                .taskTypeCode(taskEntity.getTaskTypeCode())
                .taskNo(taskEntity.getTaskNo())
                .externalTaskNo(taskEntity.getExternalTaskNo())
                .name(taskEntity.getName())
                .status(taskEntity.getStatus())
                .priority(taskEntity.getPriority())
                .callbackUrl(taskEntity.getCallbackUrl())
                .paramIn(JSONObject.parseObject(taskEntity.getParamIn()))
                .paramOut(JSONObject.parseObject(taskEntity.getParamOut()))
                .startTime(taskEntity.getStartTime())
                .endTime(taskEntity.getEndTime())
                .shutdownMsg(taskEntity.getShutdownMsg())
                .build();
    }

    private TaskNodeDetailApiDTO getTaskNodeDetailDTO(TaskDetailApiDTO taskDetailDTO,
                                                      Map.Entry<String, List<TaskNodeEntity>> entry) {
        List<TaskNodeEntity> nodes = entry.getValue();
        nodes.sort(Comparator.comparing(TaskNodeEntity::getCreateDate).reversed());
        TaskNodeEntity node = nodes.get(0);
        TaskNodeDetailApiDTO build = TaskNodeDetailApiDTO.builder()
                .code(node.getCode())
                .name(node.getName())
                .status(node.getStatus())
                .type(node.getType())
                .startTime(node.getStartTime())
                .endTime(node.getEndTime())
                .build();
        if (!DefaultNode.JUDGE_TYPE.contains(node.getType()) && !org.springframework.util.StringUtils.isEmpty(node.getParamIn())) {
            build.setParamIn(JSONObject.parseObject(node.getParamIn()));
        }
        if (!org.springframework.util.StringUtils.isEmpty(node.getParamOut())) {
            build.setParamOut(JSONObject.parseObject(node.getParamOut()));
        }
        updateNodeDetail(nodes, node, build, taskDetailDTO);
        return build;
    }

    private void updateNodeDetail(List<TaskNodeEntity> nodes,
                                  TaskNodeEntity currentNode,
                                  TaskNodeDetailApiDTO nodeDetail,
                                  TaskDetailApiDTO detail) {
        switch (currentNode.getType()) {
            case NODE_NAME_WHILE:
                //如果当前节点是循环，则根据节点返回的执行结果，如果是true，则将状态置为运行中
                JSONObject paramOut = JSONObject.parseObject(currentNode.getParamOut());
                if (paramOut != null) {
                    Boolean condition = paramOut.getBoolean("condition");
                    if (TaskConstant.STATUS_RUNNING.equals(detail.getStatus()) && condition) {
                        nodeDetail.setStatus(TaskConstant.NODE_STATUS_RUNNING);
                    }
                }
                //开始时间是第一条循环记录的开始时间
                TaskNodeEntity firstNode = nodes.get(nodes.size() - 1);
                nodeDetail.setStartTime(firstNode.getStartTime());
                //运行中时，结束时间置空
                if (NODE_STATUS_RUNNING.equals(nodeDetail.getStatus())) {
                    nodeDetail.setEndTime(null);
                }
                break;
            case NODE_NAME_VEHICLE_MOVE:
                //如果是路径导航节点，需要计算：0、机器人移动总距离 1、机器人移动时剩下的距离 2、剩余时间
                double totalDistance = 0.0;
                double remainDistance = 0.0;
                double remainDuration = 0.0;
                if (NODE_STATUS_RUNNING.equals(nodeDetail.getStatus())) {
                    Vehicle vehicle = Optional.ofNullable(currentNode.getVehicleCode())
                            .map(vehicleCode -> defaultVehiclePool.getVehicle(vehicleCode))
                            .orElse(null);
                    if (vehicle != null) {
                        String locatingCode = vehicle.selectLocatingCode();
                        //剩余距离
                        List<Path> executingPaths = Lists.newArrayList();
                        executingPaths.addAll(vehicle.getOccupyPaths());
                        executingPaths.addAll(vehicle.getPlanedPaths());
                        remainDistance = this.getPathLength(executingPaths, locatingCode);
                        //总距离
                        List<Path> executedPaths = vehicle.getExecutedPaths();
                        totalDistance = this.getPathLength(executedPaths, locatingCode);
                        totalDistance += remainDistance;
                        //剩余时间
                        Double speed = AGVPropertiesUtils.getDouble("AGV.MOVE_SPEED");
                        speed = speed == null || speed <= 0.0 ? 0.5 : speed;
                        remainDuration = remainDistance / speed;
                    }
                }
                nodeDetail.setTotalDistance(BigDecimal.valueOf(totalDistance).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                nodeDetail.setRemainDistance(BigDecimal.valueOf(remainDistance).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                nodeDetail.setRemainDuration(BigDecimal.valueOf(remainDuration).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                break;
            default:
                break;
        }
    }

    /**
     * 数据排序方式：先排序异常的任务，再按照任务创建时间倒叙排列
     */
    private List<RunningTaskDTO> pdaSortRunningList(List<RunningTaskDTO> runningTasks) {
        List<RunningTaskDTO> normal = runningTasks.stream().filter(r -> !TaskConstant.TASK_RUNNING_STATUS_ABNORMAL.equals(r.getStatus())).collect(Collectors.toList());
        List<RunningTaskDTO> abnormal = runningTasks.stream().filter(r -> TaskConstant.TASK_RUNNING_STATUS_ABNORMAL.equals(r.getStatus())).collect(Collectors.toList());
        normal.sort(Comparator.comparing(RunningTaskDTO::getCreateDate).reversed());
        abnormal.sort(Comparator.comparing(RunningTaskDTO::getCreateDate).reversed());
        List<RunningTaskDTO> result = new ArrayList<>();
        result.addAll(abnormal);
        result.addAll(normal);
        return result;
    }

    @Override
    public boolean cancelbyVehicleCode(String vehicleCode) {

        List<TaskEntity> all = RunningTaskPool.getAll(vehicleCode);
        if(CollectionUtils.isNotEmpty( all)){
            CompletableFuture.runAsync(() ->{
                all.forEach(task -> {
                    String taskNo = task.getTaskNo();
                    cancel(taskNo);
                });
            }) ;

        }

        return true;
    }

}
