package com.youibot.vehicle.scheduler.modules.map.controller;

import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.annotation.ConcurrentLimit;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import com.youibot.vehicle.scheduler.modules.map.dto.AirShowerDoorDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.AirShowerDoor;
import com.youibot.vehicle.scheduler.modules.map.service.AirShowerDoorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

@RestController("airShowerDoorController")
@RequestMapping(value = "/map/airShowerDoors", produces = "application/json")
@Api(value = "风淋门", tags = "风淋门", description = "风淋门增删改查，手动开关门")
public class AirShowerDoorController {

    @Autowired
    private AirShowerDoorService airShowerDoorService;

    @ApiOperation(value = "风淋门列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<AirShowerDoorDTO>> get(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap, boolean isDraft) {
        List<AirShowerDoorDTO> airShowerDoors = airShowerDoorService.searchAll(searchMap, isDraft);
        return Result.suc(airShowerDoors);
    }

    @LogOperation("log.controller.airShowerDoor.insert")
    @ApiOperation(value = "创建风淋门")
    @ApiImplicitParam(name = "airShowerDoorDTO", value = "风淋门", required = true, dataType = "AirShowerDoorDTO")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Result save(@RequestBody @Validated(AddGroup.class) AirShowerDoorDTO airShowerDoor) {
        return Result.suc(this.airShowerDoorService.insert(airShowerDoor));
    }

    @ApiOperation(value = "风淋门详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "风淋门code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean")
    })
    @GetMapping(value = "/{vehicleMapCode}/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<AirShowerDoorDTO> get(@PathVariable("code") String code, @PathVariable("vehicleMapCode") String vehicleMapCode, @RequestParam boolean isDraft) {
        AirShowerDoorDTO airShowerDoor = airShowerDoorService.selectByCode(vehicleMapCode, code, isDraft);
        return Result.suc(airShowerDoor);
    }

    @LogOperation("log.controller.airShowerDoor.update")
    @ApiOperation(value = "更新风淋门")
    @ApiImplicitParam(name = "airShowerDoorDTO", value = "风淋门", required = true, dataType = "AirShowerDoorDTO")
    @PutMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result<AirShowerDoorDTO> update(@RequestBody @Validated(UpdateGroup.class) AirShowerDoorDTO airShowerDoor) {
        AirShowerDoorDTO airShowerDoorDTO = airShowerDoorService.update(airShowerDoor);
        return Result.suc(airShowerDoorDTO);
    }

    @LogOperation("log.controller.airShowerDoor.delete")
    @ApiOperation(value = "删除风淋门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "风淋门ID", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", paramType = "query", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result delete(@PathVariable("code") String code,@RequestParam("vehicleMapCode") String vehicleMapCode) {
        this.airShowerDoorService.deleteByCode(vehicleMapCode,code);
        return Result.suc();
    }

    @LogOperation("log.controller.airShowerDoor.open")
    @ConcurrentLimit
    @ApiOperation(value = "打开风淋门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "风淋门code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "doorCode", value = "风淋门关联的自动门code", paramType = "path", required = true, dataType = "String")
    })
    @PostMapping(value = "/{code}/{doorCode}/open")
    @ResponseStatus(value = HttpStatus.OK)
    public Result open(@PathVariable("code") String code,@PathVariable("doorCode") String doorCode) {
        this.airShowerDoorService.open(code,doorCode);
        return Result.suc();
    }

    @LogOperation("log.controller.airShowerDoor.close")
    @ConcurrentLimit
    @ApiOperation(value = "关闭风淋门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "风淋门code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "doorCode", value = "风淋门关联的自动门code", paramType = "path", required = true, dataType = "String")
    })
    @PostMapping(value = "/{code}/{doorCode}/close")
    @ResponseStatus(value = HttpStatus.OK)
    public Result close(@PathVariable("code") String code,@PathVariable("doorCode") String doorCode) {
        this.airShowerDoorService.close(code,doorCode);
        return Result.suc();
    }

}
