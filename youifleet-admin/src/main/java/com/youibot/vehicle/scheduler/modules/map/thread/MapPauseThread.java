package com.youibot.vehicle.scheduler.modules.map.thread;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Sets;
import com.youibot.vehicle.scheduler.common.thread.ThreadUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.service.MarkerAllocationService;
import com.youibot.vehicle.scheduler.modules.device.utils.AutoDoorUtils;
import com.youibot.vehicle.scheduler.modules.device.utils.ElevatorUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.VehicleMapDao;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.VehicleMap;
import com.youibot.vehicle.scheduler.modules.map.service.VehicleMapService;
import com.youibot.vehicle.scheduler.modules.map.utils.MapPauseUtil;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemConfigEntity;
import com.youibot.vehicle.scheduler.modules.sys.service.SystemPropertyService;
import com.youibot.vehicle.scheduler.modules.task.cache.RunningTaskPool;
import com.youibot.vehicle.scheduler.modules.task.constant.TaskConstant;
import com.youibot.vehicle.scheduler.modules.task.entity.TaskEntity;
import com.youibot.vehicle.scheduler.modules.task.service.TaskService;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePoolService;
import com.youibot.vehicle.scheduler.modules.vehicle.util.VehicleUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant.VEHICLE_MAP_MODULE_ERROR;
import static com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant.CONNECT_STATUS;
import static com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant.SOFTSTOP_STATUS_OPEN;

@Slf4j
@Component
public class MapPauseThread implements CommandLineRunner {

    @Autowired
    private VehicleMapDao vehicleMapDao;
    @Autowired
    private VehicleMapService vehicleMapService;
    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private SystemPropertyService systemPropertyService;
    @Autowired
    private MarkerAllocationService markerAllocationService;

    @Async
    @Override
    public void run(String... args) {
        Runnable runnable = () -> {
            while (true) {
                try {
                    TimeUnit.SECONDS.sleep(1);
                    this.updateMapPauseStatus();
                } catch (Exception e) {
                    log.error("地图暂停处理出错, ", e);
                    NoticeMessageUtils.pushQueue(VEHICLE_MAP_MODULE_ERROR);
                }
            }
        };
        ThreadUtils.newThread("MapPauseThread", runnable).start();
    }


    /**
     * 需要轮询暂停态、暂停完成态的地图
     * 避免地图暂停完成后，又有新机器人上线到该地图上
     */
    private static final Set<Integer> MapPauseStateSet = Sets.newHashSet(MapConstant.MAP_PAUSE_STATUS_PAUSING, MapConstant.MAP_PAUSE_STATUS_PAUSED);

    private void updateMapPauseStatus() {
        List<VehicleMap> allVehicleMap = vehicleMapService.getAllVehicleMap(new ArrayList<>());
        List<String> needPauseMapCodeList = allVehicleMap.stream().filter(m -> MapPauseStateSet.contains(m.getPauseStatus())).map(VehicleMap::getCode).collect(Collectors.toList());
        if (CollUtil.isEmpty(needPauseMapCodeList)) {
            return;
        }

        for (String mapCode : needPauseMapCodeList) {
            List<String> mapCodeList = Arrays.asList(mapCode);
            boolean lockResult = MapPauseUtil.applyLock(mapCodeList);
            if (!lockResult) {
                continue;
            }
            try {
                List<Vehicle> vehicles = vehiclePoolService.selectAll();
                List<Vehicle> vehiclesOfCurrentMap = vehicles.stream().filter(v -> mapCode.equals(v.getVehicleMapCode())).collect(Collectors.toList());
                vehiclesOfCurrentMap = vehiclesOfCurrentMap.stream().filter(v-> v.isConnect() && !v.isSoftStopOpen()).collect(Collectors.toList());
                if (CollUtil.isEmpty(vehiclesOfCurrentMap)) {
                    //修改数据库
                    LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<VehicleMap>()
                            .set(VehicleMap::getPauseStatus, MapConstant.MAP_PAUSE_STATUS_PAUSED)
                            .eq(VehicleMap::getCode, mapCode);
                    vehicleMapDao.update(null, updateWrapper);
                } else {
                    vehiclesOfCurrentMap.forEach(this::pauseVehicle);
                }
            } finally {
                MapPauseUtil.release(mapCodeList);
            }
        }
    }

    private void pauseVehicle(Vehicle vehicle) {
        if (VehicleUtils.isExecutingArmScript(vehicle)) {
            SystemConfigEntity systemConfig = systemPropertyService.getSystemConfig();
            String globalPauseExecutingArmScriptIsStop = systemConfig.getGlobalPauseExecutingArmScriptIsStop();
            //判断系统配置中是否有配置假如机器人正在执行机械臂动作，那么先不暂停此机器人，等待其动作执行完毕后再暂停
            if (VehicleConstant.VEHICLE_GLOBAL_PAUSE_EXECUTING_ARM_SCRIPT_IS_STOP_LATER.equals(globalPauseExecutingArmScriptIsStop)) {
                return;
            }
        }
        if (!vehicle.isConnect()) {
            return;
        } else if (vehicle.isSoftStopOpen()) {
            return;
        } else if (ElevatorUtils.isTakingElevator(vehicle)) {
            //空等待
            return;
        } else if (AutoDoorUtils.isInUseAutoDoor(vehicle)) {
            //空等待
            return;
        } else if (AutoDoorUtils.isInUseAirShowerDoor(vehicle)) {
            //空等待
            return;
        } else if (VehicleUtils.isRunningInNoParkingArea(vehicle)) {
            //空等待
            return;
        } else if (VehicleUtils.isParkingInNoParkingArea(vehicle)) {
            //如果小车停在禁停区，则需要驱赶出禁停区后，再暂停
            driveVehiclesWaitInNoParkingArea(vehicle.getVehicleCode());
        } else {
            List<TaskEntity> chargeList = taskService.selectRunningTaskByCode(vehicle.getVehicleCode(), TaskConstant.SOURCE_CHARGE);
            if (CollUtil.isNotEmpty(chargeList)) {
                Set<String> taskNoSet = chargeList.stream().map(TaskEntity::getTaskNo).collect(Collectors.toSet());
                RunningTaskPool.getAll().stream().filter(t -> taskNoSet.contains(t.getTaskNo())).forEach(t -> t.setInterrupt(true));
            } else {
                vehicle.openSoftStop();
            }
        }
    }

    /**
     * 将停在禁停区的小车，驱赶出禁停区后，再暂停
     * @param drivenVehicleCode
     */
    private void driveVehiclesWaitInNoParkingArea(String drivenVehicleCode) {
        try {
            //机器人是否已存在正在执行驱赶任务检查
            List<TaskEntity> driveTasks = taskService.selectRunningTaskByCode(drivenVehicleCode, TaskConstant.SOURCE_TRAFFIC);
            if (CollUtil.isNotEmpty(driveTasks)) {
                return;
            }
            Long driveTaskTypeId = Optional.ofNullable(vehiclePoolService.selectByCode(drivenVehicleCode))
                    .map(Vehicle::getParkTaskTypeId)
                    .orElse(null);
            if (driveTaskTypeId == null) {
                log.warn("检测到有可驱赶的空闲机器人在禁停区域内, 驱赶机器人, 机器人未绑定泊车任务, vehicleCode:[{}]", drivenVehicleCode);
                return;
            }
            //获取最优可用驱赶点
            Marker optimalDriveMarker = markerAllocationService.getOptimalDriveMarker(drivenVehicleCode);
            if (optimalDriveMarker == null) {
                log.warn("检测到有可驱赶的空闲机器人在禁停区域内, 驱赶机器人, 无可用驱赶点, vehicleCode:[{}]", drivenVehicleCode);
                return;
            }
            String driveMarkerCode = optimalDriveMarker.getCode();
            JSONObject driveParam = new JSONObject();
            driveParam.put("vehicleCode", drivenVehicleCode);
            driveParam.put("markerCode", driveMarkerCode);
            taskService.executePriority(driveTaskTypeId, driveParam, TaskConstant.SOURCE_TRAFFIC, null, TaskConstant.TASK_IS_BREAK, 5);
        } catch (Exception e) {
            log.error("检测到有空闲机器人[{}]在禁停区域内, 创建驱赶任务失败", drivenVehicleCode, e);
        }
    }
}
