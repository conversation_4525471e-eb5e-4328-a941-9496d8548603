package com.youibot.vehicle.scheduler.modules.statistics.controller;

import cn.hutool.core.date.DateUtil;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.statistics.dto.MultipleVehicleStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.SingleVehicleStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.service.VehicleStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 机器人统计控制层
 */
@RestController
@RequestMapping("/statistics/vehicle")
@Api(tags = "统计-机器人")
public class VehicleStatisticsController {

    @Autowired
    private VehicleStatisticsService vehicleStatisticsService;

    @ApiOperation(value = "多台机器人统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String")
    })
    @GetMapping()
    public Result<MultipleVehicleStatisticsDTO> statistics(@RequestParam(value = "startTime") String startTime,
                                                           @RequestParam(value = "endTime") String endTime) {
        return Result.suc(vehicleStatisticsService.statistics(DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1)));
    }

    @ApiOperation(value = "单台机器人统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String")
    })
    @GetMapping("/{vehicleCode}")
    public Result<SingleVehicleStatisticsDTO> getDetail(@PathVariable String vehicleCode,
                                                        @RequestParam(value = "startTime") String startTime,
                                                        @RequestParam(value = "endTime") String endTime) {
        return Result.suc(vehicleStatisticsService.statistics(vehicleCode, DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1)));
    }

}
