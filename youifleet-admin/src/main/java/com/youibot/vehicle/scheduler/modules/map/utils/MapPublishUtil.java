package com.youibot.vehicle.scheduler.modules.map.utils;

import cn.hutool.core.collection.ConcurrentHashSet;
import lombok.Data;
import org.springframework.stereotype.Component;
import java.util.Set;

@Component
public class MapPublishUtil {

    @Data
    public static class InnerResource {
        private Object lock = new Object();
        private Set<String> resource = new ConcurrentHashSet<>();
    }

    private static final String ELEVATOR_KEY = "elevator";

    /**
     * 一个地图一把锁，所有地图操作拿同一把锁
     */
    public static final InnerResource resource = new InnerResource();

    public static boolean applyLockForMap(String mapCode) {
        synchronized (resource.getLock()) {
            //有电梯正在操作，拒绝拿锁
            if (resource.getResource().contains(ELEVATOR_KEY)) {
                return false;
            }
            if (resource.getResource().contains(mapCode)) {
                return false;
            }
            resource.getResource().add(mapCode);
            return true;
        }
    }

    /**
     * 释放锁
     */
    public static void releaseMap(String mapCode) {
        synchronized (resource.getLock()) {
        resource.getResource().remove(mapCode);}
    }

    /**
     * 申请电梯锁，当有任意一张地图被操作时都无法操作电梯
     */
    public static boolean applyLockForElevator() {
        synchronized (resource.getLock()) {
            if (!resource.getResource().isEmpty()) {
                return false;
            }
            resource.getResource().add(ELEVATOR_KEY);
            return true;
        }
    }

    public static void releaseElevator() {
        releaseMap(ELEVATOR_KEY);
    }

    /**
     * 获取锁
     * @param mapCode
     * @param seconds  <=0 无限循环,> 0 等待一定秒数
     * @return
     */
//    public static boolean applyLockForMapUntilSuccess(String mapCode,int seconds){
//        boolean applyLockFlag = false;
//        long start = System.currentTimeMillis();
//        while(seconds <= 0 || System.currentTimeMillis() - start <= seconds * 1000){
//            applyLockFlag = applyLockForMap(mapCode);
//            if(applyLockFlag){
//                break;
//            }
//            //抢不到锁，跳过，下次再处理
//            try {
//                TimeUnit.MILLISECONDS.sleep(200);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            logger.debug("MapPublishUtil applyLockForMap,:{} ",mapCode);
//        }
//        return applyLockFlag;
//    }

}
