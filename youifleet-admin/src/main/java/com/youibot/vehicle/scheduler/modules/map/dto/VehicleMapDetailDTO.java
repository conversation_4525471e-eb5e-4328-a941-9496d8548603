package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 地图
 * 类名称：Map
 * 创建时间：2019年4月2日 下午3:35:52
 */
@Data
@ApiModel(value = "VehicleMapDetailDTO", description = "地图")
public class VehicleMapDetailDTO implements Serializable {

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    @ApiModelProperty(value = "默认定位图编码", position = 2)
    private String defaultLocatingCode;

    @ApiModelProperty(value = "是否有草稿地图 1：有 0：没有", position = 3)
    private Integer isDraft;

    @ApiModelProperty(value = "是否有正式地图 1：有 0：没有", position = 4)
    private Integer isProd;

    @ApiModelProperty(value = "当前地图暂停状态：0:正常，1:暂停中，2:暂停完成", position = 4)
    private Integer pauseStatus;

    @ApiModelProperty(value = "创建人id", position = 5)
    private Long creator;

    @ApiModelProperty(value = "创建人名称", position = 6)
    private String creatorName;

    @ApiModelProperty(value = "创建时间", position = 7)
    private Date createTime;

    @ApiModelProperty(value = "发布时间", position = 8)
    private Date publishTime;

    @ApiModelProperty(value = "编辑时间", position = 9)
    private Date editTime;

    @ApiModelProperty(value = "路网md5值", position = 10)
    private String pathMd5;

    @ApiModelProperty(value = "机器人数量", position = 11)
    private Integer vehicleCount;

    @ApiModelProperty(value = "定位图集合", position = 12)
    private List<MapInfoDTO> mapInfoList;
}
