package com.youibot.vehicle.scheduler.modules.map.service;

import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.modules.map.dto.*;
import com.youibot.vehicle.scheduler.modules.map.entity.VehicleMap;
import com.youibot.vehicle.scheduler.modules.monitor.dto.ElementsQueryDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface VehicleMapService {

    PageData<VehicleMap> page(Map<String, Object> searchMap);

    List<VehicleMap> getAllVehicleMap(List<String> vehicleMapCodes);

    List<VehicleMapDetailDTO> findAllWithMapInfo(boolean isDraft);

    List<VehicleMapDetailDTO> searchAll(Map<String, String> searchMap) throws Exception;

    VehicleMapDetailDTO selectByCode(String vehicleMapCode);

    VehicleMapDetailDTO selectByCode(String vehicleMapCode, Boolean isDraft);

    List<MapInfoDTO> getMapInfoList(String mapCode, boolean isDraft);

    VehicleMapDTO insert(VehicleMapDTO vehicleMapDTO);

    VehicleMapDTO update(VehicleMapDTO vehicleMapDTO);

    void delete(String vehicleMapCode);

    /**
     * 查询草稿文件是否存在
     */
    boolean existDraft(String vehicleMapCode);

    /**
     * 删除草稿文件
     */
    void deleteDraftFile(String vehicleMapCode);

    void batchGenerate(MarkerAndPathBatchGenerateDTO generateDTO);

    void batchUpdate(BatchUpdateElementData pathResult, String vehicleMapCode, String locatingCode);

    void batchDelete(BatchDeleteElementDTO pathResult, String vehicleMapCode);

    MapInfoDTO getLocatingMap(String vehicleMapCode, String locatingCode, Boolean isDraft);

    MapInfoDTO updateLocatingMap(MapInfoUpdateDTO vehicleMapDTO);

    MapInfoDTO importLocatingMap(String vehicleMapCode,MultipartFile multiPartFile) throws Exception;

    void exportLocationMap(String mapCode, String locatingCode, Boolean isDraft, HttpServletResponse response);

    void changeDefaultLocatingMap(MapLocationDTO mapLocationDTO);

    void deleteLocatingMap(MapLocationDTO mapLocationDTO);

    void publish(String vehicleMapCode) throws Exception;

    void importMap(MultipartFile multiPartFile) throws Exception;

    void export(HttpServletResponse response, String vehicleMapCode, boolean isDraft);

    void copyMap(CopyMapDTO copyMapDTO) throws IOException;

    /**
     * 模糊查询当前地图上的元素
     */
    List<MapElementDTO> getElements(MapElementQueryDTO query);

    ElementsQueryDTO getMonitorElements(MapElementQueryDTO query);

    /**
     * 编辑地图
     */
    void editMap(String vehicleMapCode);

    void processUndo(String mapCode,EditMapAction editMapAction);

    void processRedo(String mapCode,EditMapAction editMapAction);

    void pauseMap(String mapCode);

    List<VehiclePauseResultDTO> recoverMap(String mapCode);
}
