package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ElevatorToMapDTO", description = "电梯点位绑定实体")
public class ElevatorToMapDTO {

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "电梯编码", position = 1)
    private String elevatorCode;

    @ApiModelProperty(value = "地图编码", position = 1)
    private String vehicleMapCode;

    @ApiModelProperty(value = "地图名称", position = 1)
    private String vehicleMapName;

    @ApiModelProperty(value = "点位编号", position = 2)
    private String markerCode;

    @ApiModelProperty(value = "电梯外呼开门地址", position = 3)
    private Integer outOperateAddress;

    @ApiModelProperty(value = "电梯外呼开门值", position = 3)
    private Integer outOperateOpenValue;

    @ApiModelProperty(value = "电梯内呼开门地址", position = 3)
    private Integer innerOperateAddress;

    @ApiModelProperty(value = "电梯内呼开门值", position = 3)
    private Integer innerOperateOpenValue;

    @ApiModelProperty(value = "开门地址", position = 3)
    private Integer openAddress;

    @ApiModelProperty(value = "电梯开门状态值", position = 4)
    private Integer openStatusValue;

    @ApiModelProperty(value = "电梯关门状态值", position = 4)
    private Integer closeStatusValue;

    @ApiModelProperty(value = "目的地址", position = 4)
    private Integer destAddress;

    @ApiModelProperty(value = "电梯到达状态值", position = 4)
    private Integer destStatusValue;

    @ApiModelProperty(value = "角度", position = 6)
    private Double angle = 0.0;
}
