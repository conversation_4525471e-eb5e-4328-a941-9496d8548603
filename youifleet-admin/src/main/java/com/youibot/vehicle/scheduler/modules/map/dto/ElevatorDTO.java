package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2020/7/30 14:14
 */
@Data
@ApiModel(value = "ElevatorDTO", description = "电梯")
public class ElevatorDTO {

    @NotBlank(message = "{elevator.code.require}", groups = {UpdateGroup.class})
    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @NotBlank(message = "{elevator.ip.require}", groups = {UpdateGroup.class})
    @ApiModelProperty(value = "操作电梯的IP地址", position = 2)
    private String ip;

    @NotNull(message = "{elevator.port.require}", groups = {UpdateGroup.class})
    @ApiModelProperty(value = "操作电梯的端口号", position = 3)
    private Integer port;

    @ApiModelProperty(value = "电梯门通讯状态：通讯正常：NORMAL,通讯异常：ERROR", position = 4)
    private String currentStatus;

    @NotBlank(message = "{elevator.readFunctionCode.require}", groups = {UpdateGroup.class})
    @ApiModelProperty(value = "Modbus读功能码（01，02，03，04）", position = 5)
    private String readFunctionCode;

    @NotBlank(message = "{elevator.writeFunctionCode.require}", groups = {UpdateGroup.class})
    @ApiModelProperty(value = "Modbus写功能码（05，06）", position = 6)
    private String writeFunctionCode;

    @ApiModelProperty(value = "电梯是否有货的检测地址", position = 7)
    private Integer goodsCheckAddress;

    @ApiModelProperty(value = "电梯有货的值", position = 7)
    private Integer goodsHasValue;

    @ApiModelProperty(value = "电梯使用场景：AGV_ONLY： 机器人专用，AGV_WITH_PEOPLE：人机共用", position = 7)
    private String usageScene;

    @ApiModelProperty(value = "电梯模式控制地址，用于写入enterAgvModeWriteValue、exitAgvModeWriteValue", position = 7)
    private Integer modeControlAddress;

    @ApiModelProperty(value = "进入机器人模式写入值，用于写入", position = 7)
    private Integer enterAgvModeWriteValue;

    @ApiModelProperty(value = "退出机器人模式写入值，用于写入", position = 7)
    private Integer exitAgvModeWriteValue;

    @ApiModelProperty(value = "电梯模式状态地址，用于读取", position = 7)
    private Integer modeStatusAddress;

    @ApiModelProperty(value = "电梯处于机器人模式时的值，用于和modeStatusAddress比较是否是AGV模式", position = 7)
    private Integer agvModeValue;


    @Valid
    @ApiModelProperty(value = "电梯关联的绑定关系", position = 8)
    private List<ElevatorToMapDTO> elevatorToMaps;
}
