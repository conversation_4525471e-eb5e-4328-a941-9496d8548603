package com.youibot.vehicle.scheduler.modules.map.pool;

import com.youibot.vehicle.scheduler.modules.map.dto.PublishMap;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 发布地图时处理点位距离矩阵数据
 */
@Component
public class PublishMapHandleMatrixQueue {

    /**
     * 发布地图资源处理完成后, 插入数据进行点位距离矩阵数据重规划
     */
    private ConcurrentLinkedQueue<PublishMap> mapPublishQueue = new ConcurrentLinkedQueue<>();

    public void add(PublishMap publishMap) {
        mapPublishQueue.add(publishMap);
    }

    public void addAll(List<PublishMap> publishMaps) {
        mapPublishQueue.addAll(publishMaps);
    }

    public PublishMap poll() {
        return mapPublishQueue.poll();
    }

    public List<PublishMap> pollAll() {
        List<PublishMap> list = new LinkedList<>();
        while (mapPublishQueue.iterator().hasNext()) {
            list.add(mapPublishQueue.poll());
        }
        return list;
    }

    public boolean isEmpty() {
        return mapPublishQueue.isEmpty();
    }

}
