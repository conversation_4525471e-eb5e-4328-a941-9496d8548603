package com.youibot.vehicle.scheduler.modules.map.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.exception.ParameterException;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.Pair;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.vehicle.scheduler.modules.device.thread.door.AutoDoorControlThread;
import com.youibot.vehicle.scheduler.modules.device.utils.AutoDoorUtils;
import com.youibot.vehicle.scheduler.modules.device.utils.ModbusUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.AutoDoorDao;
import com.youibot.vehicle.scheduler.modules.map.dao.AutoDoorDraftDao;
import com.youibot.vehicle.scheduler.modules.map.dto.AutoDoorDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.AutoDoor;
import com.youibot.vehicle.scheduler.modules.map.entity.AutoDoorDraft;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.map.service.AutoDoorService;
import com.youibot.vehicle.scheduler.modules.map.service.MapElementService;
import com.youibot.vehicle.scheduler.modules.map.service.MarkerService;
import com.youibot.vehicle.scheduler.modules.map.service.PathService;
import com.youibot.vehicle.scheduler.modules.map.utils.CodeFormatUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.MapPublishUtil;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AutoDoorServiceImpl implements AutoDoorService, MapElementService {

    private static final Logger logger = LoggerFactory.getLogger(AutoDoorServiceImpl.class);

    @Resource
    private AutoDoorDao autoDoorDao;
    @Resource
    private AutoDoorDraftDao autoDoorDraftDao;
    @Resource
    public PathService pathService;
    @Resource
    public MarkerService markerService;


    @Override
    public List<AutoDoorDTO> searchAll(Map<String, Object> searchMap, boolean isDraft) {
        List<AutoDoorDTO> list;
        if (isDraft) {
            QueryWrapper<AutoDoorDraft> queryWrapper = getWrapper(searchMap);
            queryWrapper.isNull("asd_code");
            List<AutoDoorDraft> autoDoorDrafts = autoDoorDraftDao.selectList(queryWrapper);
            list = AutoDoorUtils.autoDoorDraftToAutoDoorDTO(autoDoorDrafts);
        } else {
            QueryWrapper<AutoDoor> queryWrapper = getWrapper(searchMap);
            queryWrapper.isNull("asd_code");
            List<AutoDoor> autoDoors = autoDoorDao.selectList(queryWrapper);
            list = AutoDoorUtils.autoDoorToAutoDoorDTO(autoDoors);
            //更新状态
            updateDoorStatus(list);
        }
        return list;
    }

    @Override
    public List<AutoDoorDTO> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft) {
        List<AutoDoorDTO> autoDoorDTOS;
        if (isDraft) {
            List<AutoDoorDraft> allAutoDoorDrafts = this.getListByVehicleMapCode(vehicleMapCode, autoDoorDraftDao);
            List<AutoDoorDraft> autoDoorDrafts = allAutoDoorDrafts.stream().filter(autoDoorDraft -> StringUtils.isEmpty(autoDoorDraft.getAsdCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(autoDoorDrafts)) {
                return new ArrayList<>();
            }
            autoDoorDTOS = AutoDoorUtils.autoDoorDraftToAutoDoorDTO(autoDoorDrafts);
        } else {
            List<AutoDoor> allAutoDoors = this.getListByVehicleMapCode(vehicleMapCode, autoDoorDao);
            List<AutoDoor> autoDoors = allAutoDoors.stream().filter(autoDoor -> StringUtils.isEmpty(autoDoor.getAsdCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(autoDoors)) {
                return new ArrayList<>();
            }
            autoDoorDTOS = AutoDoorUtils.autoDoorToAutoDoorDTO(autoDoors);
            //更新状态
            updateDoorStatus(autoDoorDTOS);
        }
        return autoDoorDTOS;
    }

    @Override
    public List<AutoDoor> selectByVehicleMapCode(String vehicleMapCode) {
        List<AutoDoor> allAutoDoors = this.getListByVehicleMapCode(vehicleMapCode, autoDoorDao);
        List<AutoDoor> autoDoors = allAutoDoors.stream().filter(autoDoor -> StringUtils.isEmpty(autoDoor.getAsdCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(autoDoors)) {
            return null;
        }
        return autoDoors;
    }

    @Override
    public AutoDoorDTO insert(AutoDoorDTO autoDoorDTO) {
        String mapCode = autoDoorDTO.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            autoDoorDTO.setCode(CodeFormatUtils.getFormatCode(mapCode, this.getMaxCode(mapCode), CodeFormatUtils.AUTODOOR_CODE_PREFIX));
            AutoDoorDraft autoDoorDraft = ConvertUtils.sourceToTarget(autoDoorDTO, AutoDoorDraft.class);
            if (!CollectionUtils.isEmpty(autoDoorDTO.getPathCodes())) {
                autoDoorDraft.setPathCodes(String.join(",", autoDoorDTO.getPathCodes()));
            }
            //生成code
            if (autoDoorDraft.getAngle() == null) {
                autoDoorDraft.setAngle(0.0);
            }
            autoDoorDraftDao.insert(autoDoorDraft);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfAdd(mapCode, "AutoDoor", Arrays.asList(autoDoorDTO));
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("add AutoDoor failed", e);
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.autoDoor.add.error"));
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
        return autoDoorDTO;
    }


    /**
     * 获取最大的编号
     */
    private String getMaxCode(String vehicleMapCode) {
        String code = MapConstant.DefaultCode;
        List<AutoDoorDraft> allAutoDoorDrafts = this.getListByVehicleMapCode(vehicleMapCode, autoDoorDraftDao);
        List<AutoDoorDraft> autoDoorDrafts = allAutoDoorDrafts.stream().filter(autoDoorDraft -> StringUtils.isEmpty(autoDoorDraft.getAsdCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(autoDoorDrafts)) {
            Integer maxVal = autoDoorDrafts.stream().map(item -> Integer.valueOf(CodeFormatUtils.getIntegerCode(item.getCode()))).max(Comparator.comparingInt(a -> a)).orElse(0);
            code = String.valueOf(maxVal + 1);
        }
        return code;
    }


    @Override
    public void update(AutoDoorDTO autoDoorDTO) {
        String mapCode = autoDoorDTO.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            if (CollectionUtils.isEmpty(autoDoorDTO.getPathCodes())) {
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AUTO_DOOR_UNBOUND_PATH).placeholders(Arrays.asList(autoDoorDTO.getCode())).mapName(mapCode).build());
                String message = I18nMessageUtils.getMessage("vehicleMap.path.not.exist.error", autoDoorDTO.getCode());
                throw new FleetException(message);
            }
            AutoDoorDraft autoDoorDraft = ConvertUtils.sourceToTarget(autoDoorDTO, AutoDoorDraft.class);
            if (!CollectionUtils.isEmpty(autoDoorDTO.getPathCodes())) {
                autoDoorDraft.setPathCodes(String.join(",", autoDoorDTO.getPathCodes()));
            }
            if (autoDoorDraft.getAngle() == null) {
                autoDoorDraft.setAngle(0.0);
            }
            autoDoorDraftDao.updateById(autoDoorDraft);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfUpdate(mapCode, "AutoDoor", Arrays.asList(autoDoorDTO));
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("update AutoDoor failed", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.autoDoor.update.error", autoDoorDTO.getCode());
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    @Override
    public void deleteByCode(String mapCode, String code) {
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            AutoDoorDraft autoDoorDraft = autoDoorDraftDao.selectById(code);
            autoDoorDraftDao.deleteById(code);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfDelete(mapCode, "AutoDoor", Arrays.asList(autoDoorDraft));
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("delete AutoDoor failed", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.autoDoor.delete.error", code);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    @Override
    public AutoDoorDTO selectByCode(String vehicleMapCode, String code, boolean isDraft) {
        List<AutoDoorDTO> autoDoors = selectByVehicleMapCode(vehicleMapCode, isDraft);
        return autoDoors.stream().filter(autoDoor -> autoDoor.getCode().equals(code)).findFirst().orElse(null);
    }

    @Override
    public AutoDoorDTO selectByCode(String code) {
        AutoDoor autoDoor = autoDoorDao.selectById(code);
        AutoDoorDTO autoDoorDTO = ConvertUtils.sourceToTarget(autoDoor, AutoDoorDTO.class);
        autoDoorDTO.setPathCodes(Arrays.asList(autoDoor.getPathCodes().split(",")));
        updateDoorStatus(Arrays.asList(autoDoorDTO));
        return autoDoorDTO;
    }

    @Override
    public List<AutoDoorDTO> getAllAutoDoors(boolean isDraft) {
        List<AutoDoorDTO> autoDoorDTOS;
        if (isDraft) {
            QueryWrapper<AutoDoorDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.isNull("asd_code");
            List<AutoDoorDraft> autoDoorDrafts = autoDoorDraftDao.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(autoDoorDrafts)) {
                return null;
            }
            autoDoorDTOS = AutoDoorUtils.autoDoorDraftToAutoDoorDTO(autoDoorDrafts);
        } else {
            QueryWrapper<AutoDoor> queryWrapper = new QueryWrapper<>();
            queryWrapper.isNull("asd_code");
            List<AutoDoor> autoDoors = autoDoorDao.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(autoDoors)) {
                return null;
            }
            autoDoorDTOS = AutoDoorUtils.autoDoorToAutoDoorDTO(autoDoors);
            //更新状态
            updateDoorStatus(autoDoorDTOS);
        }
        return autoDoorDTOS;
    }

    private void updateDoorStatus(List<AutoDoorDTO> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(door -> {
                Pair<String, Date> pair = AutoDoorControlThread.AUTODOOR_STATUS.get(door.getCode());
                Optional.ofNullable(pair).ifPresent(p -> {
                    door.setCurrentStatus(p.getFirst());
                });
            });
        }
    }

    /**
     * 根据路径，获取关联的自动门
     *
     * @param pathList
     * @param isDraft
     * @return
     */
    @Override
    public List<AutoDoorDTO> getAutoDoorsByPaths(List<Path> pathList, boolean isDraft) {
        List<AutoDoorDTO> allAutoDoors = getAllAutoDoors(isDraft);
        if (CollectionUtils.isEmpty(allAutoDoors)) {
            return null;
        }
        List<String> pathCodes = pathList.stream().map(Path::getCode).collect(Collectors.toList());
        Set<AutoDoorDTO> set = new HashSet<>();
        pathCodes.forEach(code -> {
            List<AutoDoorDTO> collect = allAutoDoors.stream().filter(door -> !CollectionUtils.isEmpty(door.getPathCodes()) && door.getPathCodes().contains(code)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                set.addAll(collect);
            }
        });
        return new ArrayList<>(set);
    }

    /**
     * 根据点位，获取关联的自动门
     */
    @Override
    public List<AutoDoorDTO> getAutoDoorsByMarkerCode(String startMarkerCode, String endMarkerCode) {
        Marker startMarker = MapGraphUtil.getMarkerByMarkerCode(startMarkerCode);
        Marker endMarker = MapGraphUtil.getMarkerByMarkerCode(endMarkerCode);
        if (startMarker == null || endMarker == null) {
            return Collections.EMPTY_LIST;
        }
        List<Path> paths = pathService.selectByStartMarkerCodeAndEndMarkerCode(startMarkerCode, endMarkerCode, false);
        return getAutoDoorsByPaths(paths, false);
    }

    @Override
    public List<AutoDoorDTO> isInAutoDoors(VehicleLocation vehicleLocation, boolean isDraft) {
        if (Objects.isNull(vehicleLocation) || (Objects.isNull(vehicleLocation.getMarker())) && CollUtil.isEmpty(vehicleLocation.getSidePaths())) {
            logger.error("机器人脱轨，不在自动门内，点位数据：{}", vehicleLocation.toString());
            return Collections.EMPTY_LIST;
        }
        List<Path> allPath = new ArrayList<>();
//        allPath.addAll(pathService.selectByMarker(vehicleLocation.getMarker(), isDraft));
        if (!CollUtil.isEmpty(vehicleLocation.getSidePaths())) {
            allPath.addAll(vehicleLocation.getSidePaths());
        }
        return getAutoDoorsByPaths(allPath, isDraft);
    }


    @Override
    public void open(String code) {
        AutoDoor autoDoor = autoDoorDao.selectById(code);
        try {
            if (autoDoor == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.autoDoor.not.exist.error", code);
                throw new FleetException(message);
            }
            Pair<String, Date> pair = AutoDoorUtils.getDoorStatusByDoorCode(autoDoor.getCode(), AutoDoorControlThread.AUTODOOR_STATUS);
            String status = pair.getFirst();
            if (MapConstant.AUTO_DOOR_STATUS_ERROR.equals(status)) {
                String message = I18nMessageUtils.getMessage("device.connect.error", code);
                throw new FleetException(message);
            } else if (MapConstant.AUTO_DOOR_STATUS_OPEN.equals(status)) {
                logger.debug("门已开启, doorName:{}", autoDoor.getCode());
                return;
            }

            //重置关门状态值
            if (autoDoor.getCloseAddress() != null) {
                writeModbusValue(autoDoor, autoDoor.getCloseAddress(), 0);
            }
            //发送开门指令
            writeModbusValue(autoDoor, autoDoor.getOpenAddress(), 1);
            //检测自动门状态, 直到自动门打开
            while (true) {
                Thread.sleep(500);
                boolean open = readModbusValue(autoDoor, autoDoor.getOpenStatusAddress());
                if (open) {
                    logger.debug("自动门已打开, 检测结束");
                    break;
                }
            }
            pair.setFirst(MapConstant.AUTO_DOOR_STATUS_OPEN);
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            if (e instanceof ParameterException || e instanceof UnknownHostException) {
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AUTO_DOOR_CONNECT_ERROR).placeholders(Arrays.asList(code)).build());
            }
            logger.error("打开自动门失败, ", e);
            String message = I18nMessageUtils.getMessage("device.open.error", code);
            throw new FleetException(message);
        }
    }

    @Override
    public void close(String code) {
        try {
            AutoDoor autoDoor = autoDoorDao.selectById(code);
            if (autoDoor == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.autoDoor.not.exist.error", code);
                throw new FleetException(message);
            }
            Pair<String, Date> pair = AutoDoorUtils.getDoorStatusByDoorCode(autoDoor.getCode(), AutoDoorControlThread.AUTODOOR_STATUS);
            String status = pair.getFirst();
            if (MapConstant.AUTO_DOOR_STATUS_ERROR.equals(status)) {
                String message = I18nMessageUtils.getMessage("device.connect.error", code);
                throw new FleetException(message);
            } else if (MapConstant.AUTO_DOOR_STATUS_CLOSE.equals(status)) {
                logger.debug("门已关闭, doorName:{}", autoDoor.getCode());
                return;
            }
            if (AutoDoorUtils.isUseAutoDoor(autoDoor, autoDoor.getVehicleMapCode())) {
                String message = I18nMessageUtils.getMessage("device.is.in.use.error", code);
                throw new FleetException(message);
            }

            //重置开门状态值
            writeModbusValue(autoDoor, autoDoor.getOpenAddress(), 0);
            //如果关门指令地址为空则不发关门指令
            if (autoDoor.getCloseAddress() == null) {
                return;
            }
            //发送关门指令
            writeModbusValue(autoDoor, autoDoor.getCloseAddress(), 1);
            //检测自动门状态, 直到自动门打开
            while (true) {
                Thread.sleep(500);
                boolean close = readModbusValue(autoDoor, autoDoor.getCloseStatusAddress());
                if (close) {
                    logger.debug("自动门已关闭, 检测结束");
                    break;
                }
            }
            pair.setFirst(MapConstant.AUTO_DOOR_STATUS_CLOSE);
            pair.setSecond(new Date());
        } catch (FleetException e) {
            logger.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("关闭自动门失败, ", e);
            if (e instanceof ParameterException || e instanceof UnknownHostException) {
                NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AUTO_DOOR_CONNECT_ERROR).placeholders(Arrays.asList(code)).build());
            }
            String message = I18nMessageUtils.getMessage("device.close.error", code);
            throw new FleetException(message);
        }
    }

    private boolean readModbusValue(AutoDoor autoDoor, Integer coilAddress) throws Exception {
        try {
            int[] ints = ModbusUtils.readFunctionCode(autoDoor.getIp(), autoDoor.getPort(), autoDoor.getReadFunctionCode(), coilAddress, 1);
            return ints[0] != 0;
        } catch (Exception e) {
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AUTO_DOOR_READ_ERROR).placeholders(Arrays.asList(autoDoor.getCode())).build());
            throw e;
        }
    }

    private void writeModbusValue(AutoDoor autoDoor, Integer coilAddress, int value) throws Exception {
        try {
            ModbusUtils.writeFunctionCode(autoDoor.getIp(), autoDoor.getPort(), autoDoor.getWriteFunctionCode(), coilAddress, value);
        } catch (Exception e) {
            NoticeMessageUtils.pushQueue(NoticeMessage.builder().code(NoticeCodeConstant.AUTO_DOOR_WRITE_ERROR).placeholders(Arrays.asList(autoDoor.getCode())).build());
            throw e;
        }
    }

    @Override
    public void deleteDraftByVehicleMapCode(String mapCode) {
        deleteByVehicleMapCode(mapCode, autoDoorDraftDao);
    }

    @Override
    public void deleteByAsdCode(String asdCode, boolean isDraft) {
        if (isDraft) {
            QueryWrapper<AutoDoorDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("asd_code", asdCode);
            autoDoorDraftDao.delete(queryWrapper);
        } else {
            List<AutoDoorDTO> autoDoorDTOS = this.selectByAsdCode(asdCode, false);
            AutoDoorUtils.releaseAutoDoorModbusLink(autoDoorDTOS);
            QueryWrapper<AutoDoor> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("asd_code", asdCode);
            autoDoorDao.delete(queryWrapper);
        }
    }

    @Override
    public void deleteByVehicleMapCode(String mapCode) {
        List<AutoDoorDTO> autoDoorDTOS = selectByVehicleMapCode(mapCode, false);
        AutoDoorUtils.releaseAutoDoorModbusLink(autoDoorDTOS);
        QueryWrapper<AutoDoorDraft> draftQueryWrapper = new QueryWrapper<>();
        draftQueryWrapper.eq("vehicle_map_code", mapCode);
        draftQueryWrapper.isNull("asd_code");
        autoDoorDraftDao.delete(draftQueryWrapper);
        QueryWrapper<AutoDoor> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("vehicle_map_code", mapCode);
        queryWrapper.isNull("asd_code");
        autoDoorDao.delete(queryWrapper);
    }

    @Override
    public List<AutoDoorDTO> selectByAsdCode(String asdCode, boolean isDraft) {
        List<AutoDoorDTO> autoDoorDTOS = null;
        if (isDraft) {
            QueryWrapper<AutoDoorDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("asd_code", asdCode);
            List<AutoDoorDraft> autoDoorDrafts = autoDoorDraftDao.selectList(queryWrapper);
            autoDoorDTOS = AutoDoorUtils.autoDoorDraftToAutoDoorDTO(autoDoorDrafts);
        } else {
            QueryWrapper<AutoDoor> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("asd_code", asdCode);
            List<AutoDoor> autoDoors = autoDoorDao.selectList(queryWrapper);
            autoDoorDTOS = AutoDoorUtils.autoDoorToAutoDoorDTO(autoDoors);
        }
        return autoDoorDTOS;
    }
}
