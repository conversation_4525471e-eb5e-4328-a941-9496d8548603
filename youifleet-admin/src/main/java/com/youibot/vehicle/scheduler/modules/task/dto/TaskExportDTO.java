package com.youibot.vehicle.scheduler.modules.task.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TaskExportDTO {

    @ExcelProperty(value = "task.excel.head.taskNo", index = 0)
    private String taskNo;

    @ExcelProperty(value = "task.excel.head.externalTaskNo", index = 1)
    private String externalTaskNo;

    @ExcelProperty(value = "task.excel.head.name", index = 2)
    private String name;

    @ExcelProperty(value = "task.excel.head.status", index = 3)
    private String status;

    @ExcelProperty(value = "task.excel.head.priority", index = 4)
    private Integer priority;

    @ExcelProperty(value = "task.excel.head.vehicleCodes", index = 5)
    private String vehicleCodes;

    @ExcelProperty(value = "task.excel.head.source", index = 6)
    private String source;

    @ExcelProperty(value = "task.excel.head.createDate", index = 7)
    private Date createDate;

    @ExcelProperty(value = "task.excel.head.startTime", index = 8)
    private Date startTime;

    @ExcelProperty(value = "task.excel.head.endTime", index = 9)
    private Date endTime;

    @ExcelProperty(value = "task.excel.head.remark", index = 10)
    private String remark;

    @ExcelProperty(value = "task.excel.head.callbackUrl", index = 11)
    private String callbackUrl;

}
