package com.youibot.vehicle.scheduler.modules.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "TaskNodeDetailDTO",description = "任务节点详情")
public class TaskNodeDetailDTO {

    private Long id;

    @ApiModelProperty(value = "节点编码")
    private String code;

    @ApiModelProperty(value = "节点名称")
    private String name;

    @ApiModelProperty(value = "节点类型")
    private String type;

    @ApiModelProperty(value = "是否允许跳过")
    private Boolean isAllowSkip;

    @ApiModelProperty(value = "是否允许重试")
    private Boolean isAllowRetry;

    @ApiModelProperty(value = "是否已经跳过")
    private Boolean isSkip;

    @ApiModelProperty(value = "节点状态 Running/Finished/Cancel/Fail")
    private String status;

    @ApiModelProperty(value = "输入参数")
    private Map<String, Object> paramIn;

    @ApiModelProperty(value = "输出参数")
    private Map<String, Object> paramOut;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "执行时长(秒)")
    private Integer executionDuration;

    @ApiModelProperty(value = "运行次数")
    private Integer runningCount;

    @ApiModelProperty(value = "总距离(米)(移动节点特有)")
    private Double totalDistance;

    @ApiModelProperty(value = "剩下的距离(米)(移动节点特有)")
    private Double leftDistance;

}
