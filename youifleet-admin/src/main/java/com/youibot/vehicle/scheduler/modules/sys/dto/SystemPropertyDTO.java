package com.youibot.vehicle.scheduler.modules.sys.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 系统属性
 * @Author：yangpeilin
 * @Date: 2020/5/9 16:40
 */
@Data
@ApiModel(value = "SystemPropertyDTO",description = "系统属性")
public class SystemPropertyDTO implements Serializable {

    @ApiModelProperty(value="ID",position = 0)
    private Long id;

    @ApiModelProperty(value="类型:system:系统类型、user:用户类型",position = 1)
    @NotBlank(message = "{property.type.require}")
    private String type;

    @ApiModelProperty(value="分类:info、dataClean、map、traffic、charge、park、interface",position = 1)
    @NotBlank(message = "{property.category.require}")
    private String category;

    @ApiModelProperty(value="title",position = 2)
    private String title;

    @ApiModelProperty(value="备注",position = 4)
    private String remark;

    @ApiModelProperty(value="key",position = 2)
    @NotBlank(message = "{property.propertyKey.require}")
    private String propertyKey;

    @ApiModelProperty(value="value",position = 3)
    private String propertyValue;

    @ApiModelProperty(value="String,Integer,Double,Long,JSON等",position = 4)
    @NotBlank(message = "{property.valueType.require}")
    private String valueType;

}
