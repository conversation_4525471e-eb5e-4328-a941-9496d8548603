package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * {交管区域管理}
 *
 * @author: YU
 * @date: 2024-08-06 15:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("traffic_area_manage")
public class TrafficAreaManage {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域类型：TrafficArea：交管区域、ThirdSystemTrafficArea：第三方交管区域
     */
    private String areaType;

    /**
     * 占用方
     */
    private String occupySource;

    /**
     * 占用方车辆
     */
    private String occupyVehicleCodes;

    /**
     * 申请占用时间
     */
    private Date occupyDate;

}
