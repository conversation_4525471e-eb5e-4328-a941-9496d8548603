package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("map_info_draft")
public class MapInfoDraft {

    /**
     * 定位图编码
     */
    @TableId(type = IdType.INPUT)
    private String locatingCode;

    /**
     * 是否是默认定位图 1：是 0：不是
     */
    private Integer isDefault = MapConstant.LOCATING_IS_NOT_DEFAULT;

    /**
     * 绑定的机器人类型编码
     */
    private String vehicleTypeCodes;

    /**
     * 关联的地图编码
     */
    private String vehicleMapCode;

    /**
     * 类型 LaserMap
     */
    private String type;

    /**
     * 栅格图片存储路径
     */
    private String image;

    /**
     * 像素宽度
     */
    private Double width;

    /**
     * 像素高度
     */
    private Double height;

    /**
     * 旋转角度
     */
    private Double angle;

    /**
     * 中心x坐标
     */
    private Double originX;

    /**
     * 中心y坐标
     */
    private Double originY;

    /**
     * 偏移角度
     */
    private Double originYaw;

    /**
     * 分辨率
     */
    private Double resolution;

    /**
     * 定位文件md5值
     */
    private String locatingMd5;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateDate;

    public boolean isDefault(){
        return MapConstant.LOCATING_IS_DEFAULT.equals(this.isDefault);
    }

}
