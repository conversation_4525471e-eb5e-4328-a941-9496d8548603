package com.youibot.vehicle.scheduler.modules.map.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.youibot.vehicle.scheduler.common.exception.ErrorCode;
import com.youibot.vehicle.scheduler.common.constant.RegexConstant;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.util.ResourceUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.entity.ThirdSystemTrafficAreaResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.ThirdSystemTrafficAreaResourcePool;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.MapAreaDao;
import com.youibot.vehicle.scheduler.modules.map.dao.MapAreaDraftDao;
import com.youibot.vehicle.scheduler.modules.map.entity.MapArea;
import com.youibot.vehicle.scheduler.modules.map.entity.MapAreaDraft;
import com.youibot.vehicle.scheduler.modules.map.service.MapAreaService;
import com.youibot.vehicle.scheduler.modules.map.service.MapElementService;
import com.youibot.vehicle.scheduler.modules.map.service.UndoManageService;
import com.youibot.vehicle.scheduler.modules.map.utils.CodeFormatUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.MapPublishUtil;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import com.youibot.vehicle.scheduler.modules.monitor.webSocket.controller.MonitorSocketController;
import com.youibot.vehicle.scheduler.modules.monitor.webSocket.module.MapAreaPushDTO;
import com.youibot.vehicle.scheduler.modules.monitor.webSocket.module.MonitorMessageModel;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import static com.youibot.vehicle.scheduler.modules.map.constant.MapConstant.CannotOperateAreaType;

@Service
public class MapAreaServiceImpl implements MapAreaService, MapElementService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MapAreaServiceImpl.class);

    @Autowired
    private MapAreaDao mapAreaDao;
    @Autowired
    private MapAreaDraftDao mapAreaDraftDao;
    @Autowired
    private UndoManageService undoManageService;
    @Autowired
    private ThirdSystemTrafficAreaResourcePool thirdSystemTrafficAreaResourcePool;

    @Override
    public PageData<MapArea> findPage(Map<String, Object> searchMap, boolean isDraft) {
        PageData<MapArea> pageData;
        if (isDraft) {
            QueryWrapper<MapAreaDraft> queryWrapper = getWrapper(searchMap);
            IPage<MapAreaDraft> page = mapAreaDraftDao.selectPage(getPage(searchMap, "code", true), queryWrapper);
            pageData = getPageData(page, MapArea.class);
        } else {
            QueryWrapper<MapArea> queryWrapper = getWrapper(searchMap);
            IPage<MapArea> page = mapAreaDao.selectPage(getPage(searchMap, "code", true), queryWrapper);
            pageData = getPageData(page, MapArea.class);
        }
        return pageData;
    }

    @Override
    public List<MapArea> searchAll(Map<String, Object> searchMap, boolean isDraft) {
        List<MapArea> list;
        if (isDraft) {
            List<MapAreaDraft> mapAreaDrafts = mapAreaDraftDao.selectList(getWrapper(searchMap));
            list = ConvertUtils.sourceToTarget(mapAreaDrafts, MapArea.class);
        } else {
            list = mapAreaDao.selectList(getWrapper(searchMap));
        }
        return list;
    }

    @Override
    public List<MapArea> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft) {
        List<MapArea> mapAreas;
        if (isDraft) {
            List<MapAreaDraft> mapAreaDrafts = getListByVehicleMapCode(vehicleMapCode, mapAreaDraftDao);
            mapAreas = ConvertUtils.sourceToTarget(mapAreaDrafts, MapArea.class);
        } else {
            mapAreas = getListByVehicleMapCode(vehicleMapCode, mapAreaDao);
        }
        return mapAreas;
    }

    @Override
    public List<MapArea> selectByVehicleMapCodeAndType(String vehicleMapCode, String areaType, boolean isDraft) {
        List<MapArea> list;
        if (isDraft) {
            QueryWrapper<MapAreaDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("area_type", areaType);
            List<MapAreaDraft> mapAreaDrafts = mapAreaDraftDao.selectList(queryWrapper);
            list = ConvertUtils.sourceToTarget(mapAreaDrafts, MapArea.class);
        } else {
            QueryWrapper<MapArea> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("area_type", areaType);
            list = mapAreaDao.selectList(queryWrapper);
        }
        return list;
    }

    @Override
    public MapArea selectByCode(String vehicleMapCode, String code, boolean isDraft) {
        MapArea mapArea;
        if (isDraft) {
            QueryWrapper<MapAreaDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("code", code);
            MapAreaDraft areaDraft = mapAreaDraftDao.selectOne(queryWrapper);
            mapArea = ConvertUtils.sourceToTarget(areaDraft, MapArea.class);
        } else {
            QueryWrapper<MapArea> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("code", code);
            mapArea = mapAreaDao.selectOne(queryWrapper);
        }
        return mapArea;
    }

    @Override
    public MapArea selectByCode(String code) {
        QueryWrapper<MapArea> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        return mapAreaDao.selectOne(queryWrapper);
    }

    @Override
    public List<MapArea> selectByCodes(String vehicleMapCode, List<String> codes, boolean isDraft) {
        List<MapArea> mapAreas;
        if (isDraft) {
            QueryWrapper<MapAreaDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.in("code", codes);
            List<MapAreaDraft> areaDrafts = mapAreaDraftDao.selectList(queryWrapper);
            mapAreas = ConvertUtils.sourceToTarget(areaDrafts, MapArea.class);
        } else {
            QueryWrapper<MapArea> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.in("code", codes);
            mapAreas = mapAreaDao.selectList(queryWrapper);
        }
        return mapAreas;
    }

    @Override
    public MapArea insert(MapArea mapArea) {
        String mapCode = mapArea.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            List<MapArea> mapAreas = selectByVehicleMapCode(mapCode, true);
            //生成code
            mapArea.setCode(CodeFormatUtils.getFormatCode(mapCode, getMaxCode(mapAreas), CodeFormatUtils.AREA_CODE_PREFIX));
            MapAreaDraft mapAreaDraft = ConvertUtils.sourceToTarget(mapArea, MapAreaDraft.class);
            mapAreaDraftDao.insert(mapAreaDraft);
            //发送消息到打开当前地图的其他窗口（除了当前用户）
            MapUpdateSocketController.sendMessageOfAdd(mapCode, "MapArea", Collections.singletonList(mapArea));
            undoManageService.pushToUndoPool(mapCode, MapConstant.CREATE_ELEMENTS,null,null,null,null,null,Collections.singletonList(mapAreaDraft));
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("", e);
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.mapArea.add.error"));
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
        return mapArea;
    }

    /**
     * 获取最大的编号
     */
    private String getMaxCode(List<MapArea> mapAreas) {
        String code = MapConstant.DefaultCode;
        if (!org.springframework.util.CollectionUtils.isEmpty(mapAreas)) {
            Integer maxVal = mapAreas.stream().map(item -> Integer.valueOf(CodeFormatUtils.getIntegerCode(item.getCode()))).max(Comparator.comparingInt(a -> a)).orElse(0);
            code = String.valueOf(maxVal + 1);
        }
        return code;
    }

    @Override
    @Transactional
    public MapArea update(MapArea mapArea) {
        String mapCode = mapArea.getVehicleMapCode();
        MapAreaDraft oriMapArea = mapAreaDraftDao.selectById(mapArea.getCode());
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            doUpdate(mapArea);
            undoManageService.pushToUndoPool(mapCode, MapConstant.UPDATE_ELEMENTS,null,null,
                    Collections.singletonList(oriMapArea),null,null,Collections.singletonList(ConvertUtils.sourceToTarget(mapArea, MapAreaDraft.class)));
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("update mapArea failed:{}", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.mapArea.update.error", mapArea.getCode());
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
        return mapArea;
    }

    private MapArea doUpdate(MapArea mapArea) {
        if (StringUtils.isNotBlank(mapArea.getCode()) && !RegexConstant.isMatched(mapArea.getCode(), RegexConstant.CODE_REGEX)) {
            String message = I18nMessageUtils.getMessage("system.code.format.error", mapArea.getCode());
            throw new FleetException(message);
        }
        String mapCode = mapArea.getVehicleMapCode();
        MapArea mapAreaDb = selectByCode(mapCode, mapArea.getCode(), true);
        if (mapAreaDb == null) {
            String message = I18nMessageUtils.getMessage("vehicleMap.mapArea.not.exist.error", mapArea.getCode());
            throw new FleetException(message);
        }
        //当修改区域类型时，需要判断是否有车占用
        if (MapConstant.MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC.equals(mapAreaDb.getAreaType()) &&
                !MapConstant.MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC.equals(mapArea.getAreaType())) {
            ThirdSystemTrafficAreaResource resource = thirdSystemTrafficAreaResourcePool.get(mapArea.getCode());
            if (resource != null && StringUtils.isNotBlank(resource.getOccupyVehicleCode())) {
                String message = I18nMessageUtils.getMessage("vehicleMap.mapArea.update.occupied.error", mapArea.getCode());
                throw new FleetException(message);
            }
        }

        //如果目标区域是交管区域、通道区域、显示区域，则默认启用
        if(CannotOperateAreaType.contains(mapArea.getAreaType())){
            mapArea.setEnable(true);
        }
        mapAreaDraftDao.updateById(ConvertUtils.sourceToTarget(mapArea, MapAreaDraft.class));
        //发送消息到打开当前地图的窗口
        MapUpdateSocketController.sendMessageOfUpdate(mapCode, "MapArea", Arrays.asList(mapArea));
        return mapArea;
    }

    @Override
    public List<MapArea> batchUpdate(List<MapArea> mapAreas) {
        if (org.springframework.util.CollectionUtils.isEmpty(mapAreas)) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        mapAreas.forEach(this::update);
        return mapAreas;
    }


    @Override
    @Transactional
    public boolean deleteByCode(String mapCode, String code) {
        if (StringUtils.isEmpty(mapCode) || StringUtils.isEmpty(code)) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            MapArea mapAreaOfFile = selectByCode(mapCode, code, true);
            if (mapAreaOfFile == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.mapArea.not.exist.error", code);
                throw new FleetException(message);
            }
            mapAreaDraftDao.deleteById(code);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfDelete(mapCode, "MapArea", Collections.singletonList(mapAreaOfFile));
            undoManageService.pushToUndoPool(mapCode, MapConstant.DELETE_ELEMENTS,null,null,
                    Collections.singletonList(ConvertUtils.sourceToTarget(mapAreaOfFile, MapAreaDraft.class)),null,null,null);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("",e);
            String message = I18nMessageUtils.getMessage("vehicleMap.mapArea.delete.error", code);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
        return true;
    }

    @Override
    public void deleteByVehicleMapCode(String vehicleMapCode) {
        deleteByVehicleMapCode(vehicleMapCode, mapAreaDraftDao);
        deleteByVehicleMapCode(vehicleMapCode, mapAreaDao);
    }

    @Override
    public void deleteDraftByVehicleMapCode(String mapCode) {
        deleteByVehicleMapCode(mapCode, mapAreaDraftDao);
    }

    @Override
    public void enable(String vehicleMapCode, String code) {
        MapArea mapArea = this.selectByCode(vehicleMapCode, code, false);
        if (mapArea == null) {
            throw new FleetException(ErrorCode.AREA_NOT_EXIST_ERROR, code);
        }
        //修改数据库
        mapArea.setEnable(true);
        mapAreaDao.updateById(mapArea);
        //修改缓存数据
        Optional.ofNullable(MapGraphUtil.getMapAreaByCode(vehicleMapCode, code))
                .ifPresent(area -> area.setEnable(true));
        //添加区域资源
        ResourceUtils.addResource(mapArea);

        //区域状态变化，推送监控台
        pushToMonitor(mapArea);
    }

    @Override
    public void disable(String vehicleMapCode, String code) {
        MapArea mapArea = this.selectByCode(vehicleMapCode, code, false);
        if (mapArea == null) {
            throw new FleetException(ErrorCode.AREA_NOT_EXIST_ERROR, code);
        }
        //修改数据库
        mapArea.setEnable(false);
        mapAreaDao.updateById(mapArea);
        //修改缓存数据
        Optional.ofNullable(MapGraphUtil.getMapAreaByCode(vehicleMapCode, code))
                .ifPresent(area -> area.setEnable(false));
        //删除区域资源
        ResourceUtils.removeResource(mapArea);

        //区域状态变化，推送监控台
        pushToMonitor(mapArea);
    }

    /**
     * 推送监控台
     * @param mapArea
     */
    private void pushToMonitor(MapArea mapArea) {
        MapAreaPushDTO mapAreaPushDTO = ConvertUtils.sourceToTarget(mapArea, MapAreaPushDTO.class);
        List<MapAreaPushDTO> areaList = Lists.newArrayList(mapAreaPushDTO);
        MonitorMessageModel messageModel = MonitorMessageModel.builder().messageType(MonitorMessageModel.MESSAGE_TYPE_AREA).areaList(areaList).build();
        MonitorSocketController.storeMessage(mapArea.getVehicleMapCode(), messageModel);
    }
}
