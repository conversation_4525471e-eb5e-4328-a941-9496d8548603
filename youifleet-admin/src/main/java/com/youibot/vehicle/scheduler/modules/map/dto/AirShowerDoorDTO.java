package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import com.youibot.vehicle.scheduler.modules.map.entity.MapElement;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "AirShowerDoorDTO", description = "风淋门")
public class AirShowerDoorDTO extends MapElement {

    @NotNull(message="{door.code.require}", groups = UpdateGroup.class)
    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @NotBlank(message = "{map.code.require}", groups = {AddGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "地图编码", position = 2)
    private String vehicleMapCode;

    @ApiModelProperty(value = "停留时间 单位:秒  机器人需要在两道风淋门间停留一段时间", position = 3)
    private Integer residenceTime;

    @ApiModelProperty(value = "Modbus读功能码（01，02，03，04）", position = 4)
    @NotBlank(message = "{map.code.require}", groups = {UpdateGroup.class})
    private String readFunctionCode;

    @ApiModelProperty(value = "Modbus写功能码（05，06）", position = 5)
    @NotBlank(message = "{map.code.require}", groups = {UpdateGroup.class})
    private String writeFunctionCode;

    @ApiModelProperty(value = "电梯是否有货的检测地址", position = 6)
    private Integer goodsCheckAddress;

    @ApiModelProperty(value = "角度,用于前端页面显示", position = 7)
    @Range(min = -180,max = 180)
    private Double angle = 0.0;

    @Valid
    @ApiModelProperty(value = "关联门集合", position = 8)
    private List<AutoDoorDTO> doors;
}
