package com.youibot.vehicle.scheduler.modules.map.constant;

import com.google.common.collect.Sets;
import java.util.Set;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 15:33
 */
public class MapConstant {

    public static final String DEFAULT_LOCATING_MAP_CODE = "Default-White-Map";//默认定位图编码

    public static final String MAP_TYPE_LASER = "LASER_MAP";//激光地图

    public static final String MARKER_TYPE_CHARGING = "ChargingMarker";//充电点
    public static final String MARKER_TYPE_NAVIGATION = "NavigationMarker";//导航点
    public static final String MARKER_TYPE_WORK = "WorkMarker";//工作点
    public static final String MARKER_TYPE_INITIAL = "InitialMarker";//初始点

    //标记点使用状态
    public static final Integer MARKER_PARK_ATTR_ENABLE = 1;//泊车属性启用
    public static final Integer MARKER_PARK_ATTR_DISABLE = 0;//泊车属性禁用

    //自动门状态
    public static final String AUTO_DOOR_STATUS_OPEN = "OPEN";//开启
    public static final String AUTO_DOOR_STATUS_CLOSE = "CLOSE";//关闭
    public static final String AUTO_DOOR_STATUS_OPERATING = "OPERATING";//操作中
    public static final String AUTO_DOOR_STATUS_ERROR = "COMMUNICATION_ERROR";//通讯异常
    public static final String AUTO_DOOR_STATUS_PARAM_ERROR = "PARAM_ERROR";//参数异常

    //电梯、自动门、风淋门、点位、code默认从1开始
    public static final String DefaultCode = "1";

    //path线类型
    public static final int PATH_LINE_TYPE_STRAIGHT = 1;//直线
    public static final int PATH_LINE_TYPE_CURVE = 2;//曲线

    public static final Integer PATH_TYPE_QR_CODE_DOCKING = 1;//二维码对接路径
    public static final Integer PATH_TYPE_LEAVE_DOCKING = 2;//脱离对接路径

    //是否是充电桩对接点
    public static final Integer DOCKING_POINT_IS_CHARGE_STATION = 1;//是充电桩
    public static final Integer DOCKING_POINT_IS_NOT_CHARGE_STATION = 0;//不是充电桩

    //区域类型
    public static final String MAP_AREA_TYPE_SINGLE_VEHICLE = "SingleAgvArea";//单机区域
    public static final String MAP_AREA_TYPE_CONTROL = "ControlArea";//封控区域
    public static final String MAP_AREA_TYPE_SINGLE_SHOW = "ShowArea";//显示区域
    public static final String MAP_AREA_TYPE_NO_PARKING = "NoParkingArea";//禁停区域
    public static final String MAP_AREA_TYPE_CHANNEL = "ChannelArea";//通道区域
    public static final String MAP_AREA_TYPE_NO_ROTATING = "NoRotatingArea";//禁旋区域
    public static final String MAP_AREA_TYPE_TRAFFIC = "TrafficArea";//交管区域
    public static final String MAP_AREA_TYPE_FORBIDDEN = "ForbiddenArea";//禁入区域
    public static final String MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC = "ThirdSystemTrafficArea";//第三方交管区域

    //无法操作的区域类型
    public static final Set<String> CannotOperateAreaType = Sets.newHashSet(MAP_AREA_TYPE_CHANNEL, MAP_AREA_TYPE_SINGLE_SHOW, MAP_AREA_TYPE_TRAFFIC, MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC);

    //网点类型
    public static final Integer MARKER_COMMON_TYPE = 1;//普通路网点
    public static final Integer MARKER_CROSS_TYPE = 0;//交叉路网点

    //是否是默认定位图 1：是 0：不是
    public static final Integer LOCATING_IS_DEFAULT = 1;
    public static final Integer LOCATING_IS_NOT_DEFAULT = 0;

    //地图元素操作，0:新增，1:修改，2:删除
    public static final int CREATE_ELEMENTS = 0;
    public static final int UPDATE_ELEMENTS = 1;
    public static final int DELETE_ELEMENTS = 2;

    //地图暂停状态：0:正常，1:暂停中，2:暂停完成
    public static final Integer MAP_PAUSE_STATUS_NORMAL = 0;//正常
    public static final Integer MAP_PAUSE_STATUS_PAUSING = 1;//暂停中
    public static final Integer MAP_PAUSE_STATUS_PAUSED = 2;//暂停完成

    /**
     * 路径扩展参数:定向角度 key
     */
    public static final String PATH_FIX_ANGVEC = "fixAngVec";

    /**
     * 充电方式： SmartCharge:对接充电、CommonCharge:导航充电
     */
    public static final String CHARGE_TYPE_SMART_CHARGE = "SmartCharge";
    public static final String CHARGE_TYPE_COMMON_CHARGE = "CommonCharge";

    /**
     * 作业时充电：开启：1，关闭：0，默认关闭
     */
    public static final Integer CHARGE_WITH_WORK_ON = 1;
    public static final Integer CHARGE_WITH_WORK_OFF = 0;
}
