package com.youibot.vehicle.scheduler.modules.map.controller;

import com.youibot.vehicle.scheduler.common.annotation.ConcurrentLimit;
import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import com.youibot.vehicle.scheduler.modules.map.dto.ElevatorDTO;
import com.youibot.vehicle.scheduler.modules.map.service.ElevatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController("elevatorController")
@RequestMapping(value = "/map/elevators", produces = "application/json")
@Api(value = "电梯", tags = "电梯", description = "机器人在执行跨楼层任务时需要电梯辅助完成")
public class ElevatorController {

    @Autowired
    private ElevatorService elevatorService;

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<ElevatorDTO>> getAll(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap) {
        List<ElevatorDTO> elevators = elevatorService.searchAll(searchMap);
        return Result.suc(elevators);
    }

    @LogOperation("log.controller.elevator.insert")
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "elevator", value = "电梯", required = true, dataType = "ElevatorDTO")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Result<ElevatorDTO> save(@RequestBody @Validated(AddGroup.class) ElevatorDTO elevator) {
        ElevatorDTO elevatorDTO = this.elevatorService.insert(elevator);
        return Result.suc(elevatorDTO);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "code", value = "电梯code", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<ElevatorDTO> get(@PathVariable("code") String code) {
        ElevatorDTO elevator = elevatorService.selectByCode(code);
        return Result.suc(elevator);
    }

    @LogOperation("log.controller.elevator.update")
    @ApiOperation(value = "更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "电梯code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "elevator", value = "电梯", required = true, dataType = "ElevatorDTO")
    })
    @PutMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result update(@PathVariable("code") String code, @RequestBody @Validated(UpdateGroup.class) ElevatorDTO elevator) {
        elevator.setCode(code);
        this.elevatorService.update(elevator);
        return Result.suc();
    }

    @LogOperation("log.controller.elevator.delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "code", value = "电梯code", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result delete(@PathVariable("code") String code) {
        this.elevatorService.deleteByCode(code);
        return Result.suc();
    }

    @LogOperation("log.controller.elevator.import")
    @ApiOperation(value = "导入电梯", notes = "导入电梯数据json文件")
    @ApiImplicitParam(name = "multiPartFile", value = "文件", required = true, dataType = "file")
    @PostMapping("/importElevator")
    @ResponseStatus(HttpStatus.OK)
    public Result importMapData(@RequestBody MultipartFile multiPartFile) {
        elevatorService.importElevator(multiPartFile);
        return new Result();
    }

    @LogOperation("log.controller.elevator.export")
    @ApiOperation(value = "导出电梯", notes = "导出电梯数据json文件")
    @GetMapping("/export")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void export(HttpServletResponse response) {
        elevatorService.exportElevator(response);
    }

    @LogOperation("log.controller.elevator.open")
    @ConcurrentLimit
    @ApiOperation(value = "打开电梯门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "电梯关联的楼层编码code", paramType = "path", required = true, dataType = "String")
    })
    @PostMapping(value = "/{code}/open")
    @ResponseStatus(value = HttpStatus.OK)
    public Result open(@PathVariable("code") String code) {
        this.elevatorService.open(code);
        return Result.suc();
    }

    @LogOperation("log.controller.elevator.close")
    @ConcurrentLimit
    @ApiOperation(value = "关闭电梯门")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "电梯关联的楼层编码code", paramType = "path", required = true, dataType = "String")
    })
    @PostMapping(value = "/{code}/close")
    @ResponseStatus(value = HttpStatus.OK)
    public Result close(@PathVariable("code") String code) {
        this.elevatorService.close(code);
        return Result.suc();
    }
}
