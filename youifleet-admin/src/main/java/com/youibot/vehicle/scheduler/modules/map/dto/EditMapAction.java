package com.youibot.vehicle.scheduler.modules.map.dto;

import com.youibot.vehicle.scheduler.modules.map.entity.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 绘制地图动作
 */
@Data
@ApiModel(value = "DrawMapAction", description = "画地图动作")
public class EditMapAction {
    @ApiModelProperty(value = "画地图类型,0:新增,1:修改,2:删除")
    private Integer actionType;

    private List<MarkerDraft> oriMarkers;

    private List<MarkerDraft> preMarkers;

    private List<PathDraft> oriPaths;

    private List<PathDraft> prePaths;

    private List<MapAreaDraft> oriMapAreas;

    private List<MapAreaDraft> preMapAreas;
}
