package com.youibot.vehicle.scheduler.modules.statistics.controller;

import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.statistics.dto.RealTimeStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.service.RealTimeStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/statistics/monitor")
@Api(tags = "统计-监控页面(实时)")
public class RealTimeStatisticsController {

    @Autowired
    private RealTimeStatisticsService realTimeStatisticsService;

    @ApiOperation(value = "实时统计")
    @GetMapping()
    public Result<RealTimeStatisticsDTO> statistics() {
        return Result.suc(realTimeStatisticsService.statistics());
    }

}
