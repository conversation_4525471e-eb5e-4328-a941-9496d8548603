package com.youibot.vehicle.scheduler.modules.map.entity;

import com.yomahub.liteflow.util.CopyOnWriteHashMap;
import lombok.Data;
import org.springframework.util.CollectionUtils;

@Data
public class ElevatorResultData {

    private CopyOnWriteHashMap<String, Elevator> elevator;

    public ElevatorResultData() {
        this.elevator = new CopyOnWriteHashMap<>();
    }

    public CopyOnWriteHashMap<String, Elevator> getElevator() {
        if (CollectionUtils.isEmpty(elevator)) {
            elevator = new CopyOnWriteHashMap<>();
        }
        return elevator;
    }
}
