package com.youibot.vehicle.scheduler.modules.map.validator;

import com.youibot.vehicle.scheduler.common.constant.RegexConstant;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class MarkerNameValidator implements ConstraintValidator<MarkerName,CharSequence> {

    private int len;

    @Override
    public void initialize(MarkerName constraintAnnotation) {
        this.len = constraintAnnotation.len();
    }

    @Override
    public boolean isValid(CharSequence value, ConstraintValidatorContext constraintValidatorContext) {
        if(StringUtils.isEmpty(value)){
            return true;
        }
        if(value.length() > len){
            return false;
        }
        return RegexConstant.isMatched((String) value,RegexConstant.NAME_REG);
    }
}
