package com.youibot.vehicle.scheduler.modules.map.webSocket.module;

import com.youibot.vehicle.scheduler.modules.map.dto.MapInfoDTO;
import com.youibot.vehicle.scheduler.modules.map.dto.VehicleMapDetailDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "SocketMessageModel",description = "地图实时更新消息对象")
public class SocketMessageModel {

	@ApiModelProperty(value = "更新类型：add 、update 、delete",required=true)
	private String operateType;

    @ApiModelProperty(value = "地图",required=true)
    private List<VehicleMapDetailDTO> agvMaps;

	@ApiModelProperty(value = "定位图",required=true)
	private List<MapInfoDTO> locatingMaps;

	@ApiModelProperty(value = "点位",required=true)
	private List<Marker> markers;

	@ApiModelProperty(value = "路径",required=true)
	private List<Path> paths;

	@ApiModelProperty(value = "区域",required=true)
	private List<MapArea> mapAreas;

	@ApiModelProperty(value = "自动门",required=true)
	private List<AutoDoor> autoDoors;

	@ApiModelProperty(value = "风淋门",required=true)
	private List<AirShowerDoor> airShowerDoors;

	@ApiModelProperty(value = "电梯",required=true)
	private List<Elevator> elevators;

	@ApiModelProperty(value = "是否可以撤销，0:否，1:是",required=true)
	private Integer isUndo;

	@ApiModelProperty(value = "是否可以恢复，0:否，1:是",required=true)
	private Integer isRedo;

	public SocketMessageModel() {
	}

	public SocketMessageModel add() {
		this.operateType = "add";
		return this;
	}
	public SocketMessageModel update() {
		this.operateType = "update";
		return this;
	}
	public SocketMessageModel delete() {
		this.operateType = "delete";
		return this;
	}
	public SocketMessageModel publish() {
		this.operateType = "publish";
		return this;
	}
	public SocketMessageModel discard() {
		this.operateType = "discard";
		return this;
	}

	public SocketMessageModel checkUnReDo() {
		this.operateType = "checkUnReDo";
		return this;
	}
}
