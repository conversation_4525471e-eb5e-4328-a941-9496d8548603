package com.youibot.vehicle.scheduler.modules.sys.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.DefaultGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 角色管理
 */
@Data
@ApiModel(value = "SysRoleDTO",description = "角色管理")
public class SysRoleDTO implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "id")
	@Null(message="{id.null}", groups = AddGroup.class)
	@NotNull(message="{id.require}", groups = UpdateGroup.class)
	private Long id;

	@ApiModelProperty(value = "角色名称")
	@NotBlank(message="{sysrole.name.require}", groups = DefaultGroup.class)
	private String name;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "创建时间")
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	private Date createDate;

	@ApiModelProperty(value = "菜单列表")
	private List<SysRoleMenuDTO> menuList;

	@ApiModelProperty(value = "绑定的用户姓名")
	private String realNames;
}