package com.youibot.vehicle.scheduler.modules.statistics.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.system.oshi.OshiUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.Pair;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.utils.FileUtils;
import com.youibot.vehicle.scheduler.modules.notice.dto.NoticeMessage;
import com.youibot.vehicle.scheduler.modules.notice.util.NoticeMessageUtils;
import com.youibot.vehicle.scheduler.modules.statistics.constant.StatisticsConstant;
import com.youibot.vehicle.scheduler.modules.statistics.dao.*;
import com.youibot.vehicle.scheduler.modules.statistics.dto.ServerSpecificationDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.ServerStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.ServerUsageDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.ServerUsageRateDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.chart.LineChart;
import com.youibot.vehicle.scheduler.modules.statistics.entity.*;
import com.youibot.vehicle.scheduler.modules.statistics.pool.ServerMonitorRecordPool;
import com.youibot.vehicle.scheduler.modules.statistics.service.ServerStatisticsService;
import com.youibot.vehicle.scheduler.modules.statistics.thread.ServerCpuUpdateThread;
import com.youibot.vehicle.scheduler.modules.statistics.util.ProcessUtil;
import com.youibot.vehicle.scheduler.modules.statistics.util.StatisticsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import static com.youibot.vehicle.scheduler.modules.notice.constant.NoticeCodeConstant.STATISTICS_MODULE_ERROR;

/**
 * 服务器监控统计服务层
 */
@Slf4j
@Service
public class ServerStatisticsServiceImpl implements ServerStatisticsService {

    @Autowired
    private ServerMonitorRecordPool serverMonitorRecordPool;
    @Autowired
    private ServerCpuStatisticsDao serverCpuStatisticsDao;
    @Autowired
    private ServerMemStatisticsDao serverMemStatisticsDao;
    @Autowired
    private ServerDiskStatisticsDao serverDiskStatisticsDao;
    @Autowired
    private ServerMysqlStatisticsDao serverMysqlStatisticsDao;
    @Autowired
    private ServerMysqlTimeStatisticsDao serverMysqlTimeStatisticsDao;

    @Override
    public synchronized void triggerStatistics() {
        try {
            //获取统计的结束时间, 舍弃秒数
            DateTime statisticsEndTime = DateUtil.truncate(DateUtil.date(), DateField.MINUTE);
            //系统最后一次统计时间
            Date lastStatisticsDate = null;
            ServerDiskStatisticsEntity lastStatisticsEntity = getLastStatisticsEntity();
            if (lastStatisticsEntity != null) {//最后一次统计记录
                lastStatisticsDate = lastStatisticsEntity.getStatisticsDate();
            }
            if (lastStatisticsDate == null) {
                lastStatisticsDate = DateUtil.truncate(statisticsEndTime, DateField.HOUR_OF_DAY);
            }
            while (lastStatisticsDate.before(statisticsEndTime)) {
                //本次统计开始时间, 由于折线图、条形图等的x轴数据最小间隔为1小时, 且是整点到整点, 所以统计数据不能跨小时统计
                //获取整点时间
                DateTime hourTruncateTime = DateUtil.truncate(statisticsEndTime, DateField.HOUR_OF_DAY);
                //获取统计开始时间
                DateTime statisticsStartTime;
                if (statisticsEndTime.equals(hourTruncateTime)) {//现在是整点时间统计
                    DateTime anHourAgo = DateUtil.offsetHour(statisticsEndTime, -1);
                    statisticsStartTime = anHourAgo.after(lastStatisticsDate) ? anHourAgo : DateTime.of(lastStatisticsDate);
                } else {//现在是非整点统计
                    statisticsStartTime = hourTruncateTime.after(lastStatisticsDate) ? hourTruncateTime : DateTime.of(lastStatisticsDate);
                }
                log.debug("异常统计开始, 统计开始时间:{}, 统计结束时间:{}", statisticsStartTime, statisticsEndTime);
                this.createStatisticsData(statisticsStartTime, statisticsEndTime);
                log.debug("异常统计完成, 统计开始时间:{}, 统计结束时间:{}", statisticsStartTime, statisticsEndTime);
                statisticsEndTime = DateTime.of(statisticsStartTime);
            }

        } catch (Exception e) {
            log.error("异常统计出错", e);
            NoticeMessage message = NoticeMessage.builder().code(STATISTICS_MODULE_ERROR).build();
            NoticeMessageUtils.pushQueue(message);
        }
    }

    private ServerDiskStatisticsEntity getLastStatisticsEntity() {
        QueryWrapper<ServerDiskStatisticsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("statistics_date");
        queryWrapper.last("limit 1");
        return serverDiskStatisticsDao.selectOne(queryWrapper);
    }

    @Override
    public void createStatisticsData(DateTime statisticsStartTime, DateTime statisticsEndTime) {
        //删除过期数据
        serverMonitorRecordPool.removeInvalidServerUsageRecord(statisticsStartTime);
        //过滤当期时间段的数据
        List<ServerUsageDTO> list = serverMonitorRecordPool.getServerUsageRecords(statisticsStartTime, statisticsEndTime);

        //组装Cpu统计数据
        createCpuStatisticsData(list, statisticsStartTime, statisticsEndTime);
        //组装Memo统计数据
        createMemStatisticsData(list, statisticsStartTime, statisticsEndTime);
        //组装磁盘统计数据
        createDiskStatisticsData(list, statisticsStartTime, statisticsEndTime);

        //组装mysql操作次数统计数据
        createMysqlOperateStatisticsData(statisticsStartTime, statisticsEndTime);
        //组装mysql操作平均时间统计数据
        createMysqlTimeStatisticsData(statisticsStartTime, statisticsEndTime);
    }

    private void createCpuStatisticsData(List<ServerUsageDTO> list, DateTime statisticsStartTime, DateTime statisticsEndTime){

        double totalCpu = list.stream().mapToDouble(ServerUsageDTO::getTotalCpu).average().orElse(0.0d);
        double javaCpu = list.stream().mapToDouble(ServerUsageDTO::getJavaCpu).average().orElse(0.0d);
        double mysqlCpu = list.stream().mapToDouble(ServerUsageDTO::getMysqlCpu).average().orElse(0.0d);
        double otherCpu = list.stream().mapToDouble(ServerUsageDTO::getOtherCpu).average().orElse(0.0d);
        ServerCpuStatisticsEntity cpuStatisticsEntity = ServerCpuStatisticsEntity.builder()
                .startTime(statisticsStartTime)
                .endTime(statisticsEndTime)
                .statisticsDate(statisticsEndTime)
                .total(totalCpu)
                .java(javaCpu)
                .mysql(mysqlCpu)
                .other(otherCpu)
                .build();
        this.serverCpuStatisticsDao.insert(cpuStatisticsEntity);
    }

    private void createMemStatisticsData(List<ServerUsageDTO> list, DateTime statisticsStartTime, DateTime statisticsEndTime){
        double totalMem = list.stream().mapToDouble(ServerUsageDTO::getTotalMem).average().orElse(0.0d);
        double javaMem = list.stream().mapToDouble(ServerUsageDTO::getJavaMem).average().orElse(0.0d);
        double mysqlMem = list.stream().mapToDouble(ServerUsageDTO::getMysqlMem).average().orElse(0.0d);
        double otherMem = list.stream().mapToDouble(ServerUsageDTO::getOtherMem).average().orElse(0.0d);
        ServerMemStatisticsEntity memStatisticsEntity = ServerMemStatisticsEntity.builder()
                .startTime(statisticsStartTime)
                .endTime(statisticsEndTime)
                .statisticsDate(statisticsEndTime)
                .total(totalMem)
                .java(javaMem)
                .mysql(mysqlMem)
                .other(otherMem)
                .build();
        this.serverMemStatisticsDao.insert(memStatisticsEntity);
    }

    private void createDiskStatisticsData(List<ServerUsageDTO> list, DateTime statisticsStartTime, DateTime statisticsEndTime){
        double totalDisk = list.stream().mapToDouble(ServerUsageDTO::getTotalDisk).average().orElse(0.0d);
        double usedDisk = list.stream().mapToDouble(ServerUsageDTO::getUsedDisk).average().orElse(0.0d);
        double freeDisk = list.stream().mapToDouble(ServerUsageDTO::getFreeDisk).average().orElse(0.0d);
        ServerDiskStatisticsEntity diskStatisticsEntity = ServerDiskStatisticsEntity.builder()
                .startTime(statisticsStartTime)
                .endTime(statisticsEndTime)
                .statisticsDate(statisticsEndTime)
                .total(totalDisk)
                .used(usedDisk)
                .free(freeDisk)
                .build();
        this.serverDiskStatisticsDao.insert(diskStatisticsEntity);
    }

    private void createMysqlOperateStatisticsData(DateTime statisticsStartTime, DateTime statisticsEndTime){
        //删除过期数据
        serverMonitorRecordPool.removeInvalidMysqlOperateRecord(statisticsStartTime);
        //过滤当期时间段的数据
        Map<Long, Map<String, Integer>> mysqlOperateRecords = serverMonitorRecordPool.getMysqlOperateRecords(statisticsStartTime, statisticsEndTime);
        Integer select = mysqlOperateRecords.values().stream().filter(m -> m.get("select") != null).mapToInt(m -> m.get("select")).sum();
        Integer delete = mysqlOperateRecords.values().stream().filter(m -> m.get("delete") != null).mapToInt(m -> m.get("delete")).sum();
        Integer update = mysqlOperateRecords.values().stream().filter(m -> m.get("update") != null).mapToInt(m -> m.get("update")).sum();
        Integer insert = mysqlOperateRecords.values().stream().filter(m -> m.get("insert") != null).mapToInt(m -> m.get("insert")).sum();
        //组装mysql执行次数统计数据
        ServerMysqlStatisticsEntity mysqlStatisticsEntity = ServerMysqlStatisticsEntity.builder()
                .startTime(statisticsStartTime)
                .endTime(statisticsEndTime)
                .statisticsDate(statisticsEndTime)
                .selectNum(select)
                .deleteNum(delete)
                .updateNum(update)
                .insertNum(insert)
                .build();
        this.serverMysqlStatisticsDao.insert(mysqlStatisticsEntity);
    }

    private void createMysqlTimeStatisticsData(DateTime statisticsStartTime, DateTime statisticsEndTime){
        //删除过期数据
        serverMonitorRecordPool.removeInvalidMysqlTimeRecord(statisticsStartTime);
        //过滤当期时间段的数据
        Map<Long, Map<String, List<Integer>>> mysqlTimeRecords = serverMonitorRecordPool.getMysqlTimeRecords(statisticsStartTime, statisticsEndTime);
        Integer select = (int) mysqlTimeRecords.values().stream().filter(m -> m.get("select") != null).map(m -> m.get("select").stream().mapToInt(k -> k).sum()).mapToInt(s -> s).average().orElse(0d);
        Integer delete = (int) mysqlTimeRecords.values().stream().filter(m -> m.get("delete") != null).map(m -> m.get("delete").stream().mapToInt(k -> k).sum()).mapToInt(s -> s).average().orElse(0d);
        Integer update = (int) mysqlTimeRecords.values().stream().filter(m -> m.get("update") != null).map(m -> m.get("update").stream().mapToInt(k -> k).sum()).mapToInt(s -> s).average().orElse(0d);
        Integer insert = (int) mysqlTimeRecords.values().stream().filter(m -> m.get("insert") != null).map(m -> m.get("insert").stream().mapToInt(k -> k).sum()).mapToInt(s -> s).average().orElse(0d);
        //组装mysql执行时间统计数据
        ServerMysqlTimeStatisticsEntity mysqlTimeStatisticsEntity = ServerMysqlTimeStatisticsEntity.builder()
                .startTime(statisticsStartTime)
                .endTime(statisticsEndTime)
                .statisticsDate(statisticsEndTime)
                .selectNum(select)
                .deleteNum(delete)
                .updateNum(update)
                .insertNum(insert)
                .build();
        this.serverMysqlTimeStatisticsDao.insert(mysqlTimeStatisticsEntity);
    }

    @Override
    public ServerStatisticsDTO statistics(Date startTime, Date endTime) {
        //校验时间段的合理性: 禁止选择超过今天的日期, 禁止选择一年前的日期, 开始日期必须小于等于结束日期
        StatisticsUtils.checkStatisticsDate(startTime, endTime);
        //计算统计单位: 天数=1则以小时为单位, 1<天数<=60则以天为单位, 天数<60则以月为单位
        String statisticsUnit = StatisticsUtils.getStatisticsUnit(startTime, endTime);
        //折线图x轴时间数据
        List<Date> xAxisStartTimes = StatisticsUtils.getXAxisStartTimes(startTime, endTime);

        //服务器规格
        ServerSpecificationDTO specification = getSpecification();
        //资源实时使用率
        ServerUsageRateDTO usageRate = getServerUsageRate();
        //CPU使用率（折线图）
        LineChart cpuLineChart = this.getCpuLineChart(startTime, endTime, xAxisStartTimes, statisticsUnit);
        //内存使用率（折线图）
        LineChart memLineChart = this.getMemoLineChart(startTime, endTime, xAxisStartTimes, statisticsUnit);
        //硬盘容量（折线图）
        LineChart diskLineChart = this.getDiskLineChart(startTime, endTime, xAxisStartTimes, statisticsUnit);
        //MySQL操作次数（折线图）
        LineChart mysqlLineChart = this.getMySqlLineChart(startTime, endTime, xAxisStartTimes, statisticsUnit);
        //MySQL平均操作时间（折线图）
        LineChart mysqlTimeLineChart = this.getMySqlTimeLineChart(startTime, endTime, xAxisStartTimes, statisticsUnit);

        //组装统计数据
        return ServerStatisticsDTO.builder()
                .specification(specification)
                .usageRate(usageRate)
                .cpuLineChart(cpuLineChart)
                .memLineChart(memLineChart)
                .diskLineChart(diskLineChart)
                .mysqlLineChart(mysqlLineChart)
                .mysqlTimeLineChart(mysqlTimeLineChart)
                .build();
    }

    private ServerSpecificationDTO getSpecification(){
        int cpuCore = OshiUtil.getProcessor().getPhysicalProcessorCount();
        int threadCount = OshiUtil.getProcessor().getLogicalProcessorCount();

        long totalMemoryBytes = OshiUtil.getMemory().getTotal();
        double totalMemory = ProcessUtil.getGbFromBytes(totalMemoryBytes);

        Pair<Long, Long> disk = FileUtils.getDiskSpace();
        long totalDiskBytes = disk.first();
        double totalDisk = ProcessUtil.getGbFromBytes(totalDiskBytes);

        return ServerSpecificationDTO.builder()
                .cpuCore(cpuCore)
                .threadCount(threadCount)
                .memory(NumberUtil.round(totalMemory, 2).doubleValue())
                .disk(NumberUtil.round(totalDisk, 2).doubleValue())
                .build();
    }

    @Override
    public ServerUsageRateDTO getServerUsageRate(){
        double cpuRate = ServerCpuUpdateThread.SERVER_CPU_RATE;
        cpuRate = NumberUtil.round(cpuRate, 2).doubleValue();

        long memTotal = OshiUtil.getMemory().getTotal();
        long memAvailable = OshiUtil.getMemory().getAvailable();
        long memUsed = memTotal - memAvailable;
        double memRate = memTotal <= 0 ? 0.0 : NumberUtil.div(memUsed * 100, memTotal, 2);

        Pair<Long, Long> disk = FileUtils.getDiskSpace();
        long totalDisk = disk.first();
        long usedDisk = disk.second();
        double diskRate = totalDisk <= 0 ? 0.0 : NumberUtil.div(usedDisk * 100, totalDisk, 2);

        return ServerUsageRateDTO.builder()
                .cpuRate(cpuRate)
                .memRate(memRate)
                .diskRate(diskRate)
                .build();
    }

    private LineChart getCpuLineChart(Date start, Date end, List<Date> xAxisStartTimes, String statisticsUnit) {
        //获取统计的基础数据
        QueryWrapper<ServerCpuStatisticsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("statistics_date", start);
        queryWrapper.le("statistics_date", end);
        List<ServerCpuStatisticsEntity> list = serverCpuStatisticsDao.selectList(queryWrapper);
        //根据统计时间从小到达排序
        list.sort(Comparator.comparing(ServerCpuStatisticsEntity::getStatisticsDate));

        LineChart cpuLineChart = new LineChart();
        //获取x轴名称格式
        String xAxisNameFormat = StatisticsUtils.getXAxisNameFormat(statisticsUnit);
        List<String> xAxisNames = new LinkedList<>();

        String totalName = I18nMessageUtils.getMessage("statistics.name.cpu.rate.total");
        String javaName = I18nMessageUtils.getMessage("statistics.name.cpu.rate.java");
        String mysqlName = I18nMessageUtils.getMessage("statistics.name.cpu.rate.mysql");
        String otherName = I18nMessageUtils.getMessage("statistics.name.cpu.rate.other");
        LineChart.Series totalSeries = LineChart.Series.builder().name(totalName).values(new LinkedList<>()).build();
        LineChart.Series javaSeries = LineChart.Series.builder().name(javaName).values(new LinkedList<>()).build();
        LineChart.Series mysqlSeries = LineChart.Series.builder().name(mysqlName).values(new LinkedList<>()).build();
        LineChart.Series otherSeries = LineChart.Series.builder().name(otherName).values(new LinkedList<>()).build();

        xAxisStartTimes.forEach(startTime -> {
            //获取统计开始时间, xAxisDate就是统计结束时间
            DateTime endTime = DateUtil.offset(startTime, StatisticsUtils.getOffsetDateField(statisticsUnit), 1);
            //获取异常发生开始时间在统计时间段内的异常数据
            List<ServerCpuStatisticsEntity> memStatisticsEntities = list.stream().filter(entity -> entity.getStatisticsDate().after(startTime) && !entity.getStatisticsDate().after(endTime)).collect(Collectors.toList());
            //设置折线图数据
            xAxisNames.add(StatisticsUtils.getXAxisName(statisticsUnit, xAxisNameFormat, startTime, endTime));

            double totalMemUsage = memStatisticsEntities.stream().mapToDouble(ServerCpuStatisticsEntity::getTotal).average().orElse(0d);
            totalSeries.getValues().add(NumberUtil.round(totalMemUsage, 2).doubleValue());
            totalSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_PERCENT);

            double javaMemUsage = memStatisticsEntities.stream().mapToDouble(ServerCpuStatisticsEntity::getJava).average().orElse(0d);
            javaSeries.getValues().add(NumberUtil.round(javaMemUsage, 2).doubleValue());
            javaSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_PERCENT);

            double mysqlMemUsage = memStatisticsEntities.stream().mapToDouble(ServerCpuStatisticsEntity::getMysql).average().orElse(0d);
            mysqlSeries.getValues().add(NumberUtil.round(mysqlMemUsage, 2).doubleValue());
            mysqlSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_PERCENT);

            double otherMemUsage = memStatisticsEntities.stream().mapToDouble(ServerCpuStatisticsEntity::getOther).average().orElse(0d);
            otherSeries.getValues().add(NumberUtil.round(otherMemUsage, 2).doubleValue());
            otherSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_PERCENT);

        });

        cpuLineChart.setXAxisNames(xAxisNames);
        cpuLineChart.setSeriesList(Arrays.asList(totalSeries, javaSeries, mysqlSeries, otherSeries));
        return cpuLineChart;
    }

    private LineChart getMemoLineChart(Date start, Date end, List<Date> xAxisStartTimes, String statisticsUnit) {
        //获取统计的基础数据
        QueryWrapper<ServerMemStatisticsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("statistics_date", start);
        queryWrapper.le("statistics_date", end);
        List<ServerMemStatisticsEntity> list = serverMemStatisticsDao.selectList(queryWrapper);
        //根据统计时间从小到达排序
        list.sort(Comparator.comparing(ServerMemStatisticsEntity::getStatisticsDate));

        LineChart memoLineChart = new LineChart();
        //获取x轴名称格式
        String xAxisNameFormat = StatisticsUtils.getXAxisNameFormat(statisticsUnit);
        List<String> xAxisNames = new LinkedList<>();

        String totalName = I18nMessageUtils.getMessage("statistics.name.memo.rate.total");
        String javaName = I18nMessageUtils.getMessage("statistics.name.memo.rate.java");
        String mysqlName = I18nMessageUtils.getMessage("statistics.name.memo.rate.mysql");
        String otherName = I18nMessageUtils.getMessage("statistics.name.memo.rate.other");
        LineChart.Series totalSeries = LineChart.Series.builder().name(totalName).values(new LinkedList<>()).build();
        LineChart.Series javaSeries = LineChart.Series.builder().name(javaName).values(new LinkedList<>()).build();
        LineChart.Series mysqlSeries = LineChart.Series.builder().name(mysqlName).values(new LinkedList<>()).build();
        LineChart.Series otherSeries = LineChart.Series.builder().name(otherName).values(new LinkedList<>()).build();

        xAxisStartTimes.forEach(startTime -> {
            //获取统计开始时间, xAxisDate就是统计结束时间
            DateTime endTime = DateUtil.offset(startTime, StatisticsUtils.getOffsetDateField(statisticsUnit), 1);
            //获取异常发生开始时间在统计时间段内的异常数据
            List<ServerMemStatisticsEntity> memStatisticsEntities = list.stream().filter(entity -> entity.getStatisticsDate().after(startTime) && !entity.getStatisticsDate().after(endTime)).collect(Collectors.toList());
            //设置折线图数据
            xAxisNames.add(StatisticsUtils.getXAxisName(statisticsUnit, xAxisNameFormat, startTime, endTime));

            double totalMemUsage = memStatisticsEntities.stream().mapToDouble(ServerMemStatisticsEntity::getTotal).average().orElse(0d);
            totalSeries.getValues().add(NumberUtil.round(totalMemUsage, 2).doubleValue());
            totalSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_PERCENT);

            double javaMemUsage = memStatisticsEntities.stream().mapToDouble(ServerMemStatisticsEntity::getJava).average().orElse(0d);
            javaSeries.getValues().add(NumberUtil.round(javaMemUsage, 2).doubleValue());
            javaSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_PERCENT);

            double mysqlMemUsage = memStatisticsEntities.stream().mapToDouble(ServerMemStatisticsEntity::getMysql).average().orElse(0d);
            mysqlSeries.getValues().add(NumberUtil.round(mysqlMemUsage, 2).doubleValue());
            mysqlSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_PERCENT);

            double otherMemUsage = memStatisticsEntities.stream().mapToDouble(ServerMemStatisticsEntity::getOther).average().orElse(0d);
            otherSeries.getValues().add(NumberUtil.round(otherMemUsage, 2).doubleValue());
            otherSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_PERCENT);

        });

        memoLineChart.setXAxisNames(xAxisNames);
        memoLineChart.setSeriesList(Arrays.asList(totalSeries, javaSeries, mysqlSeries, otherSeries));
        return memoLineChart;
    }

    private LineChart getDiskLineChart(Date start, Date end, List<Date> xAxisStartTimes, String statisticsUnit) {
        //获取统计的基础数据
        QueryWrapper<ServerDiskStatisticsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("statistics_date", start);
        queryWrapper.le("statistics_date", end);
        List<ServerDiskStatisticsEntity> list = serverDiskStatisticsDao.selectList(queryWrapper);
        //根据统计时间从小到达排序
        list.sort(Comparator.comparing(ServerDiskStatisticsEntity::getStatisticsDate));

        LineChart diskLineChart = new LineChart();
        //获取x轴名称格式
        String xAxisNameFormat = StatisticsUtils.getXAxisNameFormat(statisticsUnit);
        List<String> xAxisNames = new LinkedList<>();

        String totalName = I18nMessageUtils.getMessage("statistics.name.disk.total");
        String usedName = I18nMessageUtils.getMessage("statistics.name.disk.used");
        String freeName = I18nMessageUtils.getMessage("statistics.name.disk.free");
        LineChart.Series totalSeries = LineChart.Series.builder().name(totalName).values(new LinkedList<>()).build();
        LineChart.Series usedSeries = LineChart.Series.builder().name(usedName).values(new LinkedList<>()).build();
        LineChart.Series freeSeries = LineChart.Series.builder().name(freeName).values(new LinkedList<>()).build();

        xAxisStartTimes.forEach(startTime -> {
            //获取统计开始时间, xAxisDate就是统计结束时间
            DateTime endTime = DateUtil.offset(startTime, StatisticsUtils.getOffsetDateField(statisticsUnit), 1);
            //获取异常发生开始时间在统计时间段内的异常数据
            List<ServerDiskStatisticsEntity> memStatisticsEntities = list.stream().filter(entity -> entity.getStatisticsDate().after(startTime) && !entity.getStatisticsDate().after(endTime)).collect(Collectors.toList());
            //设置折线图数据
            xAxisNames.add(StatisticsUtils.getXAxisName(statisticsUnit, xAxisNameFormat, startTime, endTime));

            double totalDiskUsage = memStatisticsEntities.stream().mapToDouble(ServerDiskStatisticsEntity::getTotal).average().orElse(0d);
            totalSeries.getValues().add(NumberUtil.round(totalDiskUsage, 2).doubleValue());
            totalSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_GB);

            double usedDiskUsage = memStatisticsEntities.stream().mapToDouble(ServerDiskStatisticsEntity::getUsed).average().orElse(0d);
            usedSeries.getValues().add(NumberUtil.round(usedDiskUsage, 2).doubleValue());
            usedSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_GB);

            double freeDiskUsage = memStatisticsEntities.stream().mapToDouble(ServerDiskStatisticsEntity::getFree).average().orElse(0d);
            freeSeries.getValues().add(NumberUtil.round(freeDiskUsage, 2).doubleValue());
            freeSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_GB);
        });

        diskLineChart.setXAxisNames(xAxisNames);
        diskLineChart.setSeriesList(Arrays.asList(totalSeries, usedSeries, freeSeries));
        return diskLineChart;
    }

    private LineChart getMySqlLineChart(Date start, Date end, List<Date> xAxisStartTimes, String statisticsUnit) {
        //获取统计的基础数据
        QueryWrapper<ServerMysqlStatisticsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("statistics_date", start);
        queryWrapper.le("statistics_date", end);
        List<ServerMysqlStatisticsEntity> list = serverMysqlStatisticsDao.selectList(queryWrapper);
        //根据统计时间从小到达排序
        list.sort(Comparator.comparing(ServerMysqlStatisticsEntity::getStatisticsDate));

        LineChart mysqlLineChart = new LineChart();
        //获取x轴名称格式
        String xAxisNameFormat = StatisticsUtils.getXAxisNameFormat(statisticsUnit);
        List<String> xAxisNames = new LinkedList<>();

        String selectName = I18nMessageUtils.getMessage("statistics.name.mysql.select");
        String deleteName = I18nMessageUtils.getMessage("statistics.name.mysql.delete");
        String updateName = I18nMessageUtils.getMessage("statistics.name.mysql.update");
        String insertName = I18nMessageUtils.getMessage("statistics.name.mysql.insert");
        LineChart.Series selectSeries = LineChart.Series.builder().name(selectName).values(new LinkedList<>()).build();
        LineChart.Series deleteSeries = LineChart.Series.builder().name(deleteName).values(new LinkedList<>()).build();
        LineChart.Series updateSeries = LineChart.Series.builder().name(updateName).values(new LinkedList<>()).build();
        LineChart.Series insertSeries = LineChart.Series.builder().name(insertName).values(new LinkedList<>()).build();

        String displayUnit = I18nMessageUtils.getMessage("statistics.unit.time");
        xAxisStartTimes.forEach(startTime -> {
            //获取统计开始时间, xAxisDate就是统计结束时间
            DateTime endTime = DateUtil.offset(startTime, StatisticsUtils.getOffsetDateField(statisticsUnit), 1);
            //获取异常发生开始时间在统计时间段内的异常数据
            List<ServerMysqlStatisticsEntity> memStatisticsEntities = list.stream().filter(entity -> entity.getStatisticsDate().after(startTime) && !entity.getStatisticsDate().after(endTime)).collect(Collectors.toList());
            //设置折线图数据
            xAxisNames.add(StatisticsUtils.getXAxisName(statisticsUnit, xAxisNameFormat, startTime, endTime));

            double selectNum = memStatisticsEntities.stream().mapToDouble(ServerMysqlStatisticsEntity::getSelectNum).sum();
            selectSeries.getValues().add(NumberUtil.round(selectNum, 0).doubleValue());
            selectSeries.getDisplayUnits().add(displayUnit);

            double deleteNum = memStatisticsEntities.stream().mapToDouble(ServerMysqlStatisticsEntity::getDeleteNum).sum();
            deleteSeries.getValues().add(NumberUtil.round(deleteNum, 0).doubleValue());
            deleteSeries.getDisplayUnits().add(displayUnit);

            double updateNum = memStatisticsEntities.stream().mapToDouble(ServerMysqlStatisticsEntity::getUpdateNum).sum();
            updateSeries.getValues().add(NumberUtil.round(updateNum, 0).doubleValue());
            updateSeries.getDisplayUnits().add(displayUnit);

            double insertNum = memStatisticsEntities.stream().mapToDouble(ServerMysqlStatisticsEntity::getInsertNum).sum();
            insertSeries.getValues().add(NumberUtil.round(insertNum, 0).doubleValue());
            insertSeries.getDisplayUnits().add(displayUnit);
        });

        mysqlLineChart.setXAxisNames(xAxisNames);
        mysqlLineChart.setSeriesList(Arrays.asList(selectSeries, deleteSeries, updateSeries, insertSeries));
        return mysqlLineChart;
    }

    private LineChart getMySqlTimeLineChart(Date start, Date end, List<Date> xAxisStartTimes, String statisticsUnit) {
        //获取统计的基础数据
        QueryWrapper<ServerMysqlTimeStatisticsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("statistics_date", start);
        queryWrapper.le("statistics_date", end);
        List<ServerMysqlTimeStatisticsEntity> list = serverMysqlTimeStatisticsDao.selectList(queryWrapper);
        //根据统计时间从小到达排序
        list.sort(Comparator.comparing(ServerMysqlTimeStatisticsEntity::getStatisticsDate));

        LineChart mysqlTimeLineChart = new LineChart();
        //获取x轴名称格式
        String xAxisNameFormat = StatisticsUtils.getXAxisNameFormat(statisticsUnit);
        List<String> xAxisNames = new LinkedList<>();

        String selectName = I18nMessageUtils.getMessage("statistics.name.mysql.times.select");
        String deleteName = I18nMessageUtils.getMessage("statistics.name.mysql.times.delete");
        String updateName = I18nMessageUtils.getMessage("statistics.name.mysql.times.update");
        String insertName = I18nMessageUtils.getMessage("statistics.name.mysql.times.insert");
        LineChart.Series selectSeries = LineChart.Series.builder().name(selectName).values(new LinkedList<>()).build();
        LineChart.Series deleteSeries = LineChart.Series.builder().name(deleteName).values(new LinkedList<>()).build();
        LineChart.Series updateSeries = LineChart.Series.builder().name(updateName).values(new LinkedList<>()).build();
        LineChart.Series insertSeries = LineChart.Series.builder().name(insertName).values(new LinkedList<>()).build();

        xAxisStartTimes.forEach(startTime -> {
            //获取统计开始时间, xAxisDate就是统计结束时间
            DateTime endTime = DateUtil.offset(startTime, StatisticsUtils.getOffsetDateField(statisticsUnit), 1);
            //获取异常发生开始时间在统计时间段内的异常数据
            List<ServerMysqlTimeStatisticsEntity> memStatisticsEntities = list.stream().filter(entity -> entity.getStatisticsDate().after(startTime) && !entity.getStatisticsDate().after(endTime)).collect(Collectors.toList());
            //设置折线图数据
            xAxisNames.add(StatisticsUtils.getXAxisName(statisticsUnit, xAxisNameFormat, startTime, endTime));

            double selectNum = memStatisticsEntities.stream().mapToInt(ServerMysqlTimeStatisticsEntity::getSelectNum).average().orElse(0d);
            selectSeries.getValues().add(NumberUtil.round(selectNum, 0).doubleValue());
            selectSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_MS);

            double deleteNum = memStatisticsEntities.stream().mapToDouble(ServerMysqlTimeStatisticsEntity::getDeleteNum).average().orElse(0d);
            deleteSeries.getValues().add(NumberUtil.round(deleteNum, 0).doubleValue());
            deleteSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_MS);

            double updateNum = memStatisticsEntities.stream().mapToDouble(ServerMysqlTimeStatisticsEntity::getUpdateNum).average().orElse(0d);
            updateSeries.getValues().add(NumberUtil.round(updateNum, 0).doubleValue());
            updateSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_MS);

            double insertNum = memStatisticsEntities.stream().mapToDouble(ServerMysqlTimeStatisticsEntity::getInsertNum).average().orElse(0d);
            insertSeries.getValues().add(NumberUtil.round(insertNum, 0).doubleValue());
            insertSeries.getDisplayUnits().add(StatisticsConstant.STATISTIC_UNIT_MS);
        });

        mysqlTimeLineChart.setXAxisNames(xAxisNames);
        mysqlTimeLineChart.setSeriesList(Arrays.asList(selectSeries, deleteSeries, updateSeries, insertSeries));
        return mysqlTimeLineChart;
    }

    @Override
    public ServerUsageDTO getServerUsage(){
        //计算cpu相关
        double totalCpu = ServerCpuUpdateThread.SERVER_CPU_RATE;
        double javaCpu = ProcessUtil.getProcessCpu("java");
        double mysqlCpu = ProcessUtil.getProcessCpu("mysqld");
        int logicalProcessorCount = OshiUtil.getProcessor().getLogicalProcessorCount();
        //totalCpu是服务器的平均使用率，javaCpu或mysqlCpu是多核的使用率叠加之和，所以需要除以逻辑处理器数量，获取平均值
        double otherCpu = totalCpu - (javaCpu + mysqlCpu) / logicalProcessorCount;
        //window平台显示的进程的平均使用率，linux平台显示的是使用率叠加之和
        javaCpu = ProcessUtil.isWin() ? javaCpu / logicalProcessorCount : javaCpu;
        mysqlCpu = ProcessUtil.isWin() ? mysqlCpu / logicalProcessorCount : mysqlCpu;

        //计算内存相关
        long total = OshiUtil.getMemory().getTotal();
        long available = OshiUtil.getMemory().getAvailable();
        double totalMem = NumberUtil.div((total - available) * 100, total, 2);
        double javaMem = ProcessUtil.getCurrentProcessMemory();
        double mysqlMem = ProcessUtil.getProcessMemory("mysqld");
        double otherMem = totalMem - javaMem - mysqlMem;

        //计算磁盘相关
        Pair<Long, Long> disk = FileUtils.getDiskSpace();
        long totalBytes = disk.first();
        long usedBytes = disk.second();
        double totalDisk = ProcessUtil.getGbFromBytes(totalBytes);
        double usedDisk = ProcessUtil.getGbFromBytes(usedBytes);

        return ServerUsageDTO.builder()
                .totalCpu(NumberUtil.round(totalCpu, 2).doubleValue())
                .javaCpu(NumberUtil.round(javaCpu, 2).doubleValue())
                .mysqlCpu(NumberUtil.round(mysqlCpu, 2).doubleValue())
                .otherCpu(NumberUtil.round(otherCpu, 2).doubleValue())

                .totalMem( NumberUtil.round(totalMem, 2).doubleValue())
                .javaMem(NumberUtil.round(javaMem, 2).doubleValue())
                .mysqlMem(NumberUtil.round(mysqlMem, 2).doubleValue())
                .otherMem(NumberUtil.round(otherMem, 2).doubleValue())

                .totalDisk(NumberUtil.round(totalDisk, 2).doubleValue())
                .usedDisk(NumberUtil.round(usedDisk, 2).doubleValue())
                .freeDisk(NumberUtil.round(totalDisk - usedDisk, 2).doubleValue())
                .build();
    }

}
