package com.youibot.vehicle.scheduler.modules.map.entity;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedList;

/**
 * <AUTHOR>
 * @Date :Created in 下午5:23 19-11-8
 * @Description :
 * @Modified By :
 * @Version :
 */
public class MarkerPathResult {

    private static final Logger logger = LoggerFactory.getLogger(MarkerPathResult.class);

    //false:路径规划失败
    private boolean result;
    //导航点路径
    public LinkedList<String> markerPath;
    //路径长度，会分步更新
    public Double cost;
    //记录规划路径后机器人的起始边
    public Path sidePath;

    public MarkerPathResult() {
        markerPath = new LinkedList<>();
        cost = Double.MAX_VALUE;
        result = false;
    }

    public boolean isSuccess() {
        return result;
    }

    public Path getPath() {
        return sidePath;
    }

    public void setPath(Path sidePath) {
        this.sidePath = sidePath;
    }

    public LinkedList<String> getMarkerPath() {
        return markerPath;
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
//        try {
//            stringBuilder.append("MarkerPathResult:").append(System.lineSeparator());
//            stringBuilder.append(MessageFormat.format("result:[{0}]", result)).append(System.lineSeparator());
//            if (sidePath != null) {
//                String startCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getStartMarkerId()).getCode();
//                String endCode = MapGraphUtil.getMarkerByMarkerId(sidePath.getEndMarkerId()).getCode();
//                stringBuilder.append(MessageFormat.format("side path:[{0}]->[{1}], t0:[{2}].", startCode, endCode, sidePath.getT0())).append(System.lineSeparator());
//            }
//            int i = 0;
//            if (!CollectionUtils.isEmpty(markerPath)) {
//                for (String temp : markerPath) {
//                    String tempCode = MapGraphUtil.getMarkerByMarkerId(temp).getCode();
//                    stringBuilder.append(MessageFormat.format("第[{0}]个markerCode:[{1}].", i++, tempCode)).append(System.lineSeparator());
//                }
//            }
//        } catch (Exception e) {
//            logger.error("MarkerPathResult toString出错, " + e);
//        }
        return stringBuilder.toString();
    }
}
