package com.youibot.vehicle.scheduler.engine.pathplan.resource.util;

import com.youibot.vehicle.scheduler.common.utils.ApplicationUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.*;
import com.youibot.vehicle.scheduler.engine.pathplan.service.LocationService;
import com.youibot.vehicle.scheduler.modules.map.entity.MapArea;
import com.youibot.vehicle.scheduler.modules.map.entity.Marker;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;
import com.youibot.vehicle.scheduler.modules.task.cache.RunningTaskPool;
import com.youibot.vehicle.scheduler.modules.task.constant.TaskConstant;
import com.youibot.vehicle.scheduler.modules.task.entity.TaskEntity;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePoolService;
import com.youibot.vehicle.scheduler.modules.vehicle.util.VehicleUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.youibot.vehicle.scheduler.modules.map.constant.MapConstant.*;
import static com.youibot.vehicle.scheduler.modules.map.constant.MapConstant.MAP_AREA_TYPE_NO_ROTATING;
import static com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant.CONNECT_STATUS;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2023/4/26 18:46
 */
public class ResourceUtils {

    private static final Logger log = LoggerFactory.getLogger(ResourceUtils.class);
    private static ControlAreaResourcePool controlAreaResourcePool = ApplicationUtils.getBean(ControlAreaResourcePool.class);
    private static NoRotatingAreaResourcePool noRotatingAreaResourcePool = ApplicationUtils.getBean(NoRotatingAreaResourcePool.class);
    private static NoParkingAreaResourcePool noParkingAreaResourcePool = ApplicationUtils.getBean(NoParkingAreaResourcePool.class);
    private static SingleAreaResourcePool singleAreaResourcePool = ApplicationUtils.getBean(SingleAreaResourcePool.class);
    private static ForbiddenResourcePool forbiddenResourcePool = ApplicationUtils.getBean(ForbiddenResourcePool.class);
    private static TrafficAreaResourcePool trafficAreaResourcePool = ApplicationUtils.getBean(TrafficAreaResourcePool.class);
    private static LocationService locationService = ApplicationUtils.getBean(LocationService.class);
    private static VehiclePoolService vehiclePoolService = ApplicationUtils.getBean(VehiclePoolService.class);

    /**
     * 判断机器人是否在封控区域内
     *
     * @param vehicleCode
     * @return
     */
    public static boolean vehicleIsInControlArea(String vehicleCode) {
        return Optional.ofNullable(getCurrentMarkerCodesByPosition(vehicleCode))
                .map(controlAreaResourcePool::getResourcesOfContainAnyMarkerCode)
                .filter(CollectionUtils::isNotEmpty)
                .isPresent();
    }

    /**
     * 判断机器人是否在禁入区域内
     *
     * @param vehicleCode
     * @return
     */
    public static boolean vehicleIsInForbiddenArea(String vehicleCode) {
        return Optional.of(getCurrentMarkerCodesByPosition(vehicleCode))
                .map(forbiddenResourcePool::getResourcesOfContainAnyMarkerCode)
                .filter(CollectionUtils::isNotEmpty)
                .isPresent();
    }

    /**
     * 获取机器人当前点位列表, 机器人在路线上则取路径两端的点位, 机器人充电时返回充电点
     *
     * @param vehicleCode
     * @return
     */
    public static Set<String> getCurrentMarkerCodesByPosition(String vehicleCode) {
        final Set<String> currentMarkerCodes = new HashSet<>();
        VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicleCode);
        //机器人在点位上
        Optional.ofNullable(vehicleLocation).map(VehicleLocation::getMarker)
                .ifPresent(marker -> currentMarkerCodes.add(marker.getCode()));
        //机器人在路径上
        Optional.ofNullable(vehicleLocation).map(VehicleLocation::getSidePaths)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(paths -> currentMarkerCodes.addAll(getMarkerCodesFromPaths(vehicleLocation.getSidePaths())));
        //如果在充电, 添加充电点，如果小车断线了，就不占用充电点
        Vehicle vehicle = VehicleUtils.getVehicle(vehicleCode);
        TaskEntity taskEntity = Optional.ofNullable(vehicle).map(Vehicle::getTaskNo).map(RunningTaskPool::get).orElse(null);
        if (vehicle != null
                && CONNECT_STATUS.equals(vehicle.getConnectStatus())
                && taskEntity != null
                && TaskConstant.SOURCE_CHARGE.equals(taskEntity.getSource())) {
            Optional.ofNullable(vehiclePoolService.getVehicleChargeMarker(vehicleCode))
                    .map(Marker::getCode)
                    .ifPresent(currentMarkerCodes::add);
        }
        return currentMarkerCodes;
    }

    public static Set<String> getMarkerCodesFromPaths(List<Path> paths) {
        Set<String> markerCodes = new HashSet<>();
        for (Path path : paths) {
            markerCodes.add(path.getStartMarkerCode());
            markerCodes.add(path.getEndMarkerCode());
        }
        return markerCodes;
    }

    public static boolean inRotatingArea(Long vehicleTypeId, Set<String> markerCodes) {
        return noRotatingAreaResourcePool.inRotatingArea(vehicleTypeId, markerCodes);
    }

    public static boolean inRotatingArea(Long vehicleTypeId, String markerCode) {
        return noRotatingAreaResourcePool.inRotatingArea(vehicleTypeId, markerCode);
    }

    public static Set<String> getInRotatingAreaMarkerCodes(Long vehicleTypeId) {
        return noRotatingAreaResourcePool.getInRotatingAreaMarkerCodes(vehicleTypeId);
    }

    /**
     * 获取机器人当前点位列表, 机器人在路线上则取路径终点的点位, 机器人充电时返回充电点
     * 注意：当机器人在路径上，如果是单向路，则取终点点位；如果是双向路径，则取离得近的点位
     */
    public static String getLastMarkerCode(String vehicleCode) {
        VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicleCode);
        String lastMarkerCode = null;
        if (vehicleLocation != null) {
            if (vehicleLocation.getMarker() != null) {
                lastMarkerCode = vehicleLocation.getMarker().getCode();
            } else if (vehicleLocation.getSidePaths() != null) {
                List<Path> sidePaths = vehicleLocation.getSidePaths();
                if(sidePaths.size() == 1){
                    lastMarkerCode = sidePaths.get(0).getEndMarkerCode();
                }else{
                    Double path1T0 = Optional.ofNullable(sidePaths.get(0).getT0()).orElse(0.0d);
                    Double path2T0 = Optional.ofNullable(sidePaths.get(1).getT0()).orElse(0.0d);
                    lastMarkerCode = path1T0 > path2T0 ? sidePaths.get(0).getEndMarkerCode() : sidePaths.get(1).getEndMarkerCode();
                }
            }
        } else {
            //如果在充电, 添加充电点，如果小车断线了，就不占用充电点
            Vehicle vehicle = VehicleUtils.getVehicle(vehicleCode);
            TaskEntity chargeTask = Optional.ofNullable(vehicle)
                    .map(Vehicle::getTaskNo)
                    .map(RunningTaskPool::get)
                    .filter(t -> TaskConstant.SOURCE_CHARGE.equals(t.getSource()))
                    .orElse(null);
            if (vehicle != null && CONNECT_STATUS.equals(vehicle.getConnectStatus()) && chargeTask != null) {
                lastMarkerCode = Optional.ofNullable(vehiclePoolService.getVehicleChargeMarker(vehicleCode))
                        .map(Marker::getCode)
                        .orElse(null);
            }
        }
        return lastMarkerCode;
    }

    public static void addResource(MapArea mapArea) {
        log.debug("addResource: mapArea={}", mapArea);
        switch (mapArea.getAreaType()) {
            case MAP_AREA_TYPE_SINGLE_VEHICLE: //单机区域
                singleAreaResourcePool.addResource(mapArea);
                break;
            case MAP_AREA_TYPE_CONTROL: //封控区域
                controlAreaResourcePool.addResource(mapArea);
                break;
            case MAP_AREA_TYPE_NO_PARKING: //禁停区域
                noParkingAreaResourcePool.addResource(mapArea);
                break;
            case MAP_AREA_TYPE_NO_ROTATING: //禁旋区域
                noRotatingAreaResourcePool.addResource(mapArea);
                break;
            case MAP_AREA_TYPE_TRAFFIC: //交管区域
                //trafficAreaResourcePool.addResource(mapArea);
                break;
            case MAP_AREA_TYPE_FORBIDDEN: //禁入区域
                forbiddenResourcePool.addResource(mapArea);
                break;
            default:
                log.debug("添加资源类型未知: mapArea={}", mapArea);
                break;
        }
    }

    public static void removeResource(MapArea mapArea) {
        log.debug("removeResource: mapArea={}", mapArea);
        switch (mapArea.getAreaType()) {
            case MAP_AREA_TYPE_SINGLE_VEHICLE: //单机区域
                singleAreaResourcePool.remove(mapArea.getCode());
                break;
            case MAP_AREA_TYPE_CONTROL: //封控区域
                controlAreaResourcePool.remove(mapArea.getCode());
                break;
            case MAP_AREA_TYPE_NO_PARKING: //禁停区域
                noParkingAreaResourcePool.remove(mapArea.getCode());
                break;
            case MAP_AREA_TYPE_NO_ROTATING: //禁旋区域
                noRotatingAreaResourcePool.remove(mapArea.getCode());
                break;
            case MAP_AREA_TYPE_TRAFFIC: //交管区域
                //trafficAreaResourcePool.remove(mapArea.getCode());
                break;
            case MAP_AREA_TYPE_FORBIDDEN: //禁入区域
                forbiddenResourcePool.remove(mapArea.getCode());
                break;
            default:
                log.debug("移除资源类型未知: mapArea={}", mapArea);
                break;
        }
    }

}
