package com.youibot.vehicle.scheduler.modules.tms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;

/**
 * 作业统计查询参数
 * 类名称：TaskStatisticsQueryDTO
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@EqualsAndHashCode
@Builder
@ApiModel(value = "TaskStatisticsQueryDTO", description = "作业统计查询参数")
public class TaskStatisticsQueryDTO implements Serializable {

    @ApiModelProperty(value = "类型：枚举类型包含：\n" +
            "NONE：统计一段时间内作业的全量汇总数据\n" +
            "HOUR：统计一段时间内作业按小时的汇总数据\n" +
            "DAY：统计一段时间内作业按天的汇总数据\n" +
            "MONTH： 统计一段时间内作业按月的汇总数据", position = 1)
    private String type;

    @ApiModelProperty(value = "机器人编号，传入查单个，否则查询所有", position = 2)
    private String agvCode;

    @ApiModelProperty(value = "开始时间，不传则默认为当前时间向前一年。 格式：yyyy-MM-dd HH:mm:ss", position = 3)
    private String startTime;

    @ApiModelProperty(value = "结束时间，不传则默认为当前时间。格式：yyyy-MM-dd HH:mm:ss", position = 4)
    private String endTime;

}
