package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "MapPauseDTO", description = "地图暂停恢复操作")
public class MapPauseDTO {

    @ApiModelProperty(value = "地图编码")
    private String mapCode;

    @ApiModelProperty(value = "操作类型：暂停：Pause  正常：Normal")
    @NotBlank(message = "操作类型不能为空，值范围：暂停：Pause  正常：Normal")
    private String type;
}
