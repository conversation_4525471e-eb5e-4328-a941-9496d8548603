package com.youibot.vehicle.scheduler.modules.map.service.impl;

import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.dto.EditMapAction;
import com.youibot.vehicle.scheduler.modules.map.entity.MapAreaDraft;
import com.youibot.vehicle.scheduler.modules.map.entity.MarkerDraft;
import com.youibot.vehicle.scheduler.modules.map.entity.PathDraft;
import com.youibot.vehicle.scheduler.modules.map.pool.EditMapActionPool;
import com.youibot.vehicle.scheduler.modules.map.service.UndoManageService;
import com.youibot.vehicle.scheduler.modules.map.service.VehicleMapService;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import com.youibot.vehicle.scheduler.modules.map.webSocket.module.SocketMessageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 撤销和恢复
 */
@Service
public class UndoManageServiceImpl implements UndoManageService {
    @Autowired
    private EditMapActionPool editMapActionPool;
    @Autowired
    private VehicleMapService vehicleMapService;
    @Override
    public void undo(String mapCode) {
        //获取undo队列的第一条记录
        EditMapAction preEditMapAction = editMapActionPool.getUndoAction(mapCode);
        if (null == preEditMapAction) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.reset.error", mapCode);
            throw new FleetException(message);
        }
        //根据第一条记录，处理undo的内容
        vehicleMapService.processUndo(mapCode, preEditMapAction);
        //从undo队列中移除第一条记录
        editMapActionPool.removeUndo(mapCode);
        //将移除的记录加入到redo队列
        editMapActionPool.addRedo(mapCode, preEditMapAction);
        //推送是否可撤销恢复状态
        SocketMessageModel messageModel = new SocketMessageModel().checkUnReDo();
        messageModel.setIsUndo(canUndo(mapCode) ? 1 : 0);
        messageModel.setIsRedo(canRedo(mapCode) ? 1 : 0);
        MapUpdateSocketController.sendMessage(mapCode, messageModel);
    }

    @Override
    public void redo(String mapCode) {
        EditMapAction preEditMapAction = editMapActionPool.getRedoAction(mapCode);
        if (null == preEditMapAction) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.recover.error", mapCode);
            throw new FleetException(message);
        }
        vehicleMapService.processRedo(mapCode, preEditMapAction);
        editMapActionPool.removeRedo(mapCode);
        editMapActionPool.addUndo(mapCode, preEditMapAction);
        //推送是否可撤销恢复状态
        SocketMessageModel messageModel = new SocketMessageModel().checkUnReDo();
        messageModel.setIsUndo(canUndo(mapCode) ? 1 : 0);
        messageModel.setIsRedo(canRedo(mapCode) ? 1 : 0);
        MapUpdateSocketController.sendMessage(mapCode, messageModel);
    }

    @Override
    public boolean canUndo(String mapCode) {
        return editMapActionPool.canUndo(mapCode);
    }

    @Override
    public boolean canRedo(String mapCode) {
        return editMapActionPool.canRedo(mapCode);
    }

    @Override
    public void clear(String mapCode) {
        editMapActionPool.clear(mapCode);
    }

    /**
     * 将对地图元素操作的记录存储到undo的队列池中,
     * 每一次都插入到队列的第一位，
     * 在每次有地图操作时，清空redo队列
     */
    @Override
    public void pushToUndoPool(String mapCode, Integer actionType, List<MarkerDraft> oriMarkers, List<PathDraft> oriPaths, List<MapAreaDraft> oriMapAreas,
                               List<MarkerDraft> preMarkers, List<PathDraft> prePaths, List<MapAreaDraft> preMapAreas) {
        EditMapAction editMapAction = new EditMapAction();
        editMapAction.setActionType(actionType);
        Optional.ofNullable(oriMarkers).ifPresent(editMapAction::setOriMarkers);
        Optional.ofNullable(oriPaths).ifPresent(editMapAction::setOriPaths);
        Optional.ofNullable(oriMapAreas).ifPresent(editMapAction::setOriMapAreas);
        Optional.ofNullable(preMarkers).ifPresent(editMapAction::setPreMarkers);
        Optional.ofNullable(prePaths).ifPresent(editMapAction::setPrePaths);
        Optional.ofNullable(preMapAreas).ifPresent(editMapAction::setPreMapAreas);
        editMapActionPool.addUndo(mapCode, editMapAction);
        editMapActionPool.clearRedo(mapCode);
        //推送是否可撤销恢复状态
        SocketMessageModel messageModel = new SocketMessageModel().checkUnReDo();
        messageModel.setIsUndo(canUndo(mapCode) ? 1 : 0);
        messageModel.setIsRedo(canRedo(mapCode) ? 1 : 0);
        MapUpdateSocketController.sendMessage(mapCode, messageModel);
    }
}
