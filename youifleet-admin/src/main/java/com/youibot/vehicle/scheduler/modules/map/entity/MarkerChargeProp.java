package com.youibot.vehicle.scheduler.modules.map.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "MarkerChargeProp", description = "点位充电参数")
public class MarkerChargeProp {

    @ApiModelProperty(value = "关联的充电桩编码")
    private String chargeStationCode;

    @ApiModelProperty(value = "充电方式： SmartCharge:对接充电、CommonCharge:导航充电")
    private String chargeType;

    @ApiModelProperty(value = "充电点对接类型，ReflectorPoint:反光条特征对接点, Vpoint:V型特征对接点")
    private String dockingType;

    @ApiModelProperty(value = "对接方式： 1、车头对接  2、车尾对接 3、车左侧 4、车右侧")
    private Integer dockingDirection;

    @ApiModelProperty(value = "作业时充电：开启：1，关闭：0，默认关闭，主要针对无线充电桩")
    private Integer chargeEnable = 0;

}
