package com.youibot.vehicle.scheduler.modules.map.service;

import com.youibot.vehicle.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.vehicle.scheduler.modules.map.dto.AutoDoorDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.AutoDoor;
import com.youibot.vehicle.scheduler.modules.map.entity.Path;

import java.util.List;
import java.util.Map;

public interface AutoDoorService {

    List<AutoDoorDTO> searchAll(Map<String, Object> searchMap, boolean isDraft);

    AutoDoorDTO selectByCode(String vehicleMapCode, String code, boolean isDraft);

    /**
     * 根据自动门编码查询自动门详情(正式数据)
     * @param code
     * @return
     */
    AutoDoorDTO selectByCode(String code);

    List<AutoDoorDTO> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft);

    /**
     * 获取地图正式自动门数据
     * @param vehicleMapCode
     * @return
     */
    List<AutoDoor> selectByVehicleMapCode(String vehicleMapCode);

    List<AutoDoorDTO> getAllAutoDoors(boolean isDraft);

    List<AutoDoorDTO> getAutoDoorsByPaths(List<Path> pathList, boolean isDraft);

    List<AutoDoorDTO> getAutoDoorsByMarkerCode(String startMarkerCode, String endMarkerCode);

    List<AutoDoorDTO> isInAutoDoors(VehicleLocation vehicleLocation, boolean isDraft);

    AutoDoorDTO insert(AutoDoorDTO autoDoorDTO);

    void update(AutoDoorDTO autoDoorDTO);

    void deleteByCode(String vehicleMapCode, String code);

    void open(String code);

    void close(String code);

    void deleteDraftByVehicleMapCode(String mapCode);

    void deleteByAsdCode(String asdCode, boolean isDraft);

    /**
     * 删除草稿和正式数据
     * @param mapCode
     */
    void deleteByVehicleMapCode(String mapCode);

    /**
     * 根据风淋门code获取自动门
     * @param asdCode
     * @return
     */
    List<AutoDoorDTO> selectByAsdCode(String asdCode, boolean isDraft);

}
