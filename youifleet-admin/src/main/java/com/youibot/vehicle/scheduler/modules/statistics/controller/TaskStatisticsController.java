package com.youibot.vehicle.scheduler.modules.statistics.controller;

import cn.hutool.core.date.DateUtil;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.modules.statistics.dto.TaskHeatMapDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.TaskStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.dto.TaskSumStatisticsDTO;
import com.youibot.vehicle.scheduler.modules.statistics.service.TaskTypeStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/statistics/task")
@Api(tags = "统计-任务")
public class TaskStatisticsController {

    @Autowired
    private TaskTypeStatisticsService taskTypeStatisticsService;

    @ApiOperation(value = "任务统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String")
    })
    @GetMapping()
    public Result<TaskStatisticsDTO> statistics(@RequestParam(value = "startTime") String startTime,
                                                @RequestParam(value = "endTime") String endTime) {
        return Result.suc(taskTypeStatisticsService.statistics(DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1)));
    }

    @ApiOperation(value = "任务热力图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图编码", required = true, dataType = "String")
    })
    @GetMapping("/heatMap")
    public Result<List<TaskHeatMapDTO>> heatMap(@RequestParam(value = "startTime") String startTime,
                                                @RequestParam(value = "endTime") String endTime,
                                                @RequestParam(value = "vehicleMapCode") String vehicleMapCode) {
        return Result.suc(taskTypeStatisticsService.heatMap(DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1), vehicleMapCode));
    }

    @ApiOperation(value = "当天的任务数量统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd", required = true, dataType = "String")
    })
    @GetMapping("/sum")
    public Result<TaskSumStatisticsDTO> getTaskSumStatistics(@RequestParam(value = "startTime") String startTime,
                                                          @RequestParam(value = "endTime") String endTime) {
        return Result.suc(taskTypeStatisticsService.getTaskSumStatistics(DateUtil.parseDate(startTime), DateUtil.offsetDay(DateUtil.parseDate(endTime), 1)));
    }

}
