package com.youibot.vehicle.scheduler.modules.map.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.vehicle.scheduler.modules.map.entity.MapElement;
import com.youibot.vehicle.scheduler.modules.map.validator.AreaType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 区域
 * 类名称：EventArea
 * 创建时间：2019年4月2日 下午3:39:02
 */
@Data
@ApiModel(value = "MapAreaDTO", description = "地图区域")
public class MapAreaDTO extends MapElement {

    @ApiModelProperty(value = "区域类型 单机区域:SingleAgvArea 显示区域: ShowArea 封控区域：ControlArea 通道区域：ChannelArea 禁旋区域：NoRotatingArea 禁停区域：NoParkingArea 交管区域：TrafficArea 禁入区域：ForbiddenArea", position = 1)
    @NotBlank(message = "{map.area.areaType.require}")
    @AreaType(message = "{map.area.areaType}")
    private String areaType;

    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    @ApiModelProperty(value = "编码", position = 2)
    private String code;

    @ApiModelProperty(value = "区域坐标列表", position = 4)
    @NotBlank(message = "{map.area.polygon.require}")
    private String polygon;

    @ApiModelProperty(value = "所属的地图编码", position = 5)
    @NotBlank(message = "{map.code.require}")
    private String vehicleMapCode;

    @ApiModelProperty(value = "区域颜色", position = 5)
    private String areaColor;

    @ApiModelProperty(value = "构建区域的方式：1、多边形区域方式构建、2、矩形区域方式构建", position = 5)
    @NotBlank(message = "{map.area.operateType.require}")
    private String operateType;

    @ApiModelProperty(value = "占用的机器人", position = 12)
    private String vehicleCode;

    @ApiModelProperty(value = "绑定的机器人类型ID列表, areaType=NoRotatingArea时可存值, 空数组是表示所有机器人都生效", position = 13)
    private List<Long> vehicleTypeIds;

    @ApiModelProperty(value = "占用方", position = 12)
    private String occupyCode;

    @ApiModelProperty(value = "使用状态 启用:true  禁用:false", position = 14)
    private Boolean enable = true;

    public void setPolygon(String polygon) {
        if (StringUtils.isEmpty(polygon)) {
            return;
        }
        JSONArray jsonArray = JSONObject.parseArray(polygon);
        this.polygon = jsonArray.toJSONString();
    }

    public String getPolygon() {
        return this.polygon;
    }

}
