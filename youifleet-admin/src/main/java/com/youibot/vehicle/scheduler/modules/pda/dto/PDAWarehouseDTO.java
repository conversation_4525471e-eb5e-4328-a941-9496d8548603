package com.youibot.vehicle.scheduler.modules.pda.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PDAWarehouseDTO", description = "PDA库位操作")
public class PDAWarehouseDTO {

    @ApiModelProperty(value = "库位编码")
    private String warehouseCode;

    @ApiModelProperty(value = "容器编码", position = 1)
    private String containerBarcode;

    @ApiModelProperty(value = "物料类型", position = 2)
    private String materialType;

}
