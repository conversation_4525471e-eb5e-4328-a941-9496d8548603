package com.youibot.vehicle.scheduler.modules.map.controller;

import com.youibot.vehicle.scheduler.common.annotation.LogOperation;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.Result;
import com.youibot.vehicle.scheduler.common.validator.ValidatorUtils;
import com.youibot.vehicle.scheduler.engine.pathplan.entity.TrafficResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.entity.SingleAreaResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.entity.ThirdSystemTrafficAreaResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.entity.TrafficAreaResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.SingleAreaResourcePool;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.ThirdSystemTrafficAreaResourcePool;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.TrafficAreaResourcePool;
import com.youibot.vehicle.scheduler.modules.map.dto.MapAreaDTO;
import com.youibot.vehicle.scheduler.modules.map.entity.MapArea;
import com.youibot.vehicle.scheduler.modules.map.service.MapAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.TrafficAreaResourcePool.OCCUPY_TYPE_OWN;

@RestController("mapAreaController")
@RequestMapping(value = "/map/mapAreas", produces = "application/json")
@Api(value = "地图区域", tags = "地图区域", description = "区域管理，用户可以通过接口管理区域信息。")
public class MapAreaController {

    @Autowired
    private MapAreaService mapAreaService;
    @Autowired
    private SingleAreaResourcePool singleAreaResourcePool;
    @Autowired
    private TrafficAreaResourcePool trafficAreaResourcePool;
    @Autowired
    private ThirdSystemTrafficAreaResourcePool thirdSystemTrafficAreaResourcePool;

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<PageData<MapAreaDTO>> page(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap, Boolean isDraft) {
        PageData<MapArea> page = mapAreaService.findPage(searchMap, isDraft);
        List<MapArea> list = page.getList();

        List<MapAreaDTO> listDTOS = ConvertUtils.sourceToTarget(list, MapAreaDTO.class);
        PageData<MapAreaDTO> newPage = new PageData<>(page.getPageNum(), page.getPageSize(), listDTOS, page.getTotal());
        return Result.suc(newPage);
    }

    @ApiOperation(value = "列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isDraft", value = "是否查询草稿数据", required = true, dataType = "boolean", paramType = "query")
    })
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MapAreaDTO>> getAll(@RequestParam(required = false) @ApiIgnore Map<String, Object> searchMap, @RequestParam("isDraft") boolean isDraft) {
        List<MapArea> areas = mapAreaService.searchAll(searchMap, isDraft);
        List<MapAreaDTO> mapAreaDTOS = ConvertUtils.sourceToTarget(areas, MapAreaDTO.class);
        return Result.suc(mapAreaDTOS);
    }

    @LogOperation("log.controller.mapArea.insert")
    @ApiOperation(value = "创建")
    @ApiImplicitParam(name = "mapArea", value = "地图区域", required = true, dataType = "MapAreaDTO")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public Result<MapAreaDTO> save(@RequestBody @Valid MapAreaDTO mapArea) {
        MapArea mapArea1 = ConvertUtils.sourceToTarget(mapArea, MapArea.class);
        MapArea insert = this.mapAreaService.insert(mapArea1);
        MapAreaDTO mapAreaDTO = ConvertUtils.sourceToTarget(insert, MapAreaDTO.class);
        return Result.suc(mapAreaDTO);
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "code", value = "地图区域code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<MapAreaDTO> get(@PathVariable("code") String code, String vehicleMapCode, Boolean isDraft) {
        MapArea mapArea = mapAreaService.selectByCode(vehicleMapCode, code, isDraft);
        MapAreaDTO mapAreaDTO = ConvertUtils.sourceToTarget(mapArea, MapAreaDTO.class);
        SingleAreaResource resource = singleAreaResourcePool.get(code);
        if (Objects.nonNull(mapAreaDTO) && Objects.nonNull(resource)) {
            mapAreaDTO.setVehicleCode(resource.getOccupyVehicleCode());
        }
        TrafficAreaResource trafficAreaResource = trafficAreaResourcePool.get(code);
        if (Objects.nonNull(mapAreaDTO) && Objects.nonNull(trafficAreaResource)) {
            TrafficResource trafficResource = TrafficAreaResourcePool.TRAFFIC_VEHICLE_MANAGE.get(trafficAreaResource.getId());
            if (Objects.nonNull(trafficResource)) {
                mapAreaDTO.setOccupyCode(trafficResource.getOccupySource());
                mapAreaDTO.setVehicleCode(String.join(",", trafficResource.getOccupyVehicleCodes()));
            }
        }
        ThirdSystemTrafficAreaResource thirdSystemTrafficAreaResource = thirdSystemTrafficAreaResourcePool.get(code);
        if (Objects.nonNull(mapAreaDTO) && Objects.nonNull(thirdSystemTrafficAreaResource)) {
            mapAreaDTO.setVehicleCode(thirdSystemTrafficAreaResource.getOccupyVehicleCode());
            if (thirdSystemTrafficAreaResource.getOccupyVehicleCode() != null) {
                mapAreaDTO.setOccupyCode(OCCUPY_TYPE_OWN);
            }
        }
        return Result.suc(mapAreaDTO);
    }

    @ApiOperation(value = "详情集合")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isDraft", value = "是否草稿：true、false", required = true, dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "codes", value = "地图区域code集合", required = true, dataType = "String", allowMultiple = true, paramType = "body"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query")
    })
    @PostMapping(value = "/batchQuery")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MapAreaDTO>> batchQuery(@RequestBody List<String> codes, String vehicleMapCode, Boolean isDraft) {
        List<MapArea> areas = mapAreaService.selectByCodes(vehicleMapCode, codes, isDraft);
        List<MapAreaDTO> mapAreaDTOS = ConvertUtils.sourceToTarget(areas, MapAreaDTO.class);
        return Result.suc(mapAreaDTOS);
    }

    @LogOperation("log.controller.mapArea.update")
    @ApiOperation(value = "更新")
    @ApiImplicitParams({@ApiImplicitParam(name = "code", value = "地图区域code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mapArea", value = "地图区域", required = true, dataType = "MapArea")})
    @PutMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<MapAreaDTO> update(@PathVariable("code") String code, @RequestBody @Valid MapAreaDTO mapArea) {
        mapArea.setCode(code);
        MapArea mapArea1 = ConvertUtils.sourceToTarget(mapArea, MapArea.class);
        MapArea update = mapAreaService.update(mapArea1);
        MapAreaDTO mapAreaDTO = ConvertUtils.sourceToTarget(update, MapAreaDTO.class);
        return Result.suc(mapAreaDTO);
    }

    @LogOperation("log.controller.mapArea.update")
    @ApiOperation(value = "批量更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mapAreas", value = "区域集合", allowMultiple = true, required = true, dataType = "MapArea")
    })
    @PutMapping(value = "/batchUpdate")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<List<MapAreaDTO>> batchUpdate(@RequestBody @Valid List<MapAreaDTO> mapAreas) {
        //效验数据
        if (!CollectionUtils.isEmpty(mapAreas)) {
            mapAreas.forEach(area -> {
                ValidatorUtils.validateEntity(area);
            });
        }
        List<MapArea> areas = ConvertUtils.sourceToTarget(mapAreas, MapArea.class);
        List<MapArea> areas1 = mapAreaService.batchUpdate(areas);
        List<MapAreaDTO> mapAreaDTOS = ConvertUtils.sourceToTarget(areas1, MapAreaDTO.class);
        return Result.suc(mapAreaDTOS);
    }

    @LogOperation("log.controller.mapArea.enable")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "地图区域code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query")
    })
    @PutMapping(value = "/{code}/enable")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Void> enable(@PathVariable("code") String code, String vehicleMapCode) {
        mapAreaService.enable(vehicleMapCode, code);
        return Result.suc();
    }

    @LogOperation("log.controller.mapArea.disable")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "地图区域code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query")
    })
    @PutMapping(value = "/{code}/disable")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Void> disable(@PathVariable("code") String code, String vehicleMapCode) {
        mapAreaService.disable(vehicleMapCode, code);
        return Result.suc();
    }

    @LogOperation("log.controller.mapArea.delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "地图区域code", paramType = "path", required = true, dataType = "String"),
            @ApiImplicitParam(name = "vehicleMapCode", value = "地图code", required = true, dataType = "String", paramType = "query")
    })
    @DeleteMapping(value = "/{code}")
    @ResponseStatus(value = HttpStatus.OK)
    public Result<Boolean> delete(@PathVariable("code") String code, String vehicleMapCode) {
        return Result.suc(this.mapAreaService.deleteByCode(vehicleMapCode, code));
    }

}
