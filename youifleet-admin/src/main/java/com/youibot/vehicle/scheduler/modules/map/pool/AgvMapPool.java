package com.youibot.vehicle.scheduler.modules.map.pool;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class AgvMapPool {

    /**
     * 资源更新锁
     */
    public static final Map<String,String> mapResourceLock = new ConcurrentHashMap<>();

    /**
     * 申请锁，成功后，执行回调
     * @param lock
     * @param consumer
     */
    public static void applyLock(String lock, Runnable consumer) {
        synchronized (lock.intern()){
            consumer.run();
        }
    }

}
