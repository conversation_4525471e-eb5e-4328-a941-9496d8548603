package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("elevator")
public class Elevator {

    /**
     * 电梯编码
     */
    @TableId(value = "code")
    private String code;

    /**
     * ip
     */
    private String ip;

    /**
     * port
     */
    private Integer port;

    /**
     * 门已开:OPEN 门未开:CLOSE 通讯异常:ERROR
     */
    private String currentStatus;

    /**
     * Modbus读功能码（01，02，03，04）
     */
    private String readFunctionCode;

    /**
     * Modbus写功能码（05，06）
     */
    private String writeFunctionCode;

    /**
     * 电梯是否有货的检测地址
     */
    private Integer goodsCheckAddress;

    /**
     * 电梯有货的值
     */
    private Integer goodsHasValue;

    /**
     * 电梯使用场景：AGV_ONLY： 机器人专用，AGV_WITH_PEOPLE：人机共用
     */
    private String usageScene;

    /**
     * 电梯模式控制地址，用于写入enterAgvModeWriteValue、exitAgvModeWriteValue
     */
    private Integer modeControlAddress;

    /**
     * 进入机器人模式写入值，用于写入
     */
    private Integer enterAgvModeWriteValue;

    /**
     * 退出机器人模式写入值，用于写入
     */
    private Integer exitAgvModeWriteValue;

    /**
     * 电梯模式状态地址，用于读取
     */
    private Integer modeStatusAddress;

    /**
     * 电梯处于机器人模式时的值，用于和modeStatusAddress比较是否是AGV模式
     */
    private Integer agvModeValue;
}
