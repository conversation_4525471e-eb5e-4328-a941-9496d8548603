package com.youibot.vehicle.scheduler.modules.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RunningTaskDTO",description = "运行中任务")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RunningTaskDTO {

    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "任务类型ID")
    private Long taskTypeId;

    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "状态 Normal:正常 Abnormal:异常")
    private String status;

    @ApiModelProperty(value = "是否可取消,0:不可取消,1:可取消 默认值1")
    private Integer isCancel;

    @ApiModelProperty(value = "当前节点")
    private String currentNode;

    @ApiModelProperty(value = "机器人")
    private String vehicleCodes;

    @ApiModelProperty(value = "外部编码")
    private String externalTaskNo;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

}
