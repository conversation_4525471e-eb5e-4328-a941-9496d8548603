package com.youibot.vehicle.scheduler.modules.map.validator;

import com.youibot.vehicle.scheduler.modules.map.enums.AreaTypeEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

public class AreaTypeValidator implements ConstraintValidator<AreaType,CharSequence> {

    @Override
    public boolean isValid(CharSequence value, ConstraintValidatorContext constraintValidatorContext) {
        Set<String> types = Arrays.stream(AreaTypeEnum.values()).map(item -> item.value()).collect(Collectors.toSet());
        return types.contains(value);
    }
}
