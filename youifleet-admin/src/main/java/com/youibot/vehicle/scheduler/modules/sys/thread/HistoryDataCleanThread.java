package com.youibot.vehicle.scheduler.modules.sys.thread;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youibot.vehicle.scheduler.common.utils.AGVPropertiesUtils;
import com.youibot.vehicle.scheduler.common.utils.DateUtils;
import com.youibot.vehicle.scheduler.modules.log.dao.OperationLogDao;
import com.youibot.vehicle.scheduler.modules.log.dao.SysLogDao;
import com.youibot.vehicle.scheduler.modules.notice.dao.NoticeRecordDao;
import com.youibot.vehicle.scheduler.modules.statistics.dao.AbnormalStatisticsDao;
import com.youibot.vehicle.scheduler.modules.statistics.dao.TaskTypeStatisticsDao;
import com.youibot.vehicle.scheduler.modules.statistics.dao.VehicleStatisticsDao;
import com.youibot.vehicle.scheduler.modules.statistics.dao.VehicleStatusRecordDao;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemConfigEntity;
import com.youibot.vehicle.scheduler.modules.sys.service.SystemPropertyService;
import com.youibot.vehicle.scheduler.modules.task.dao.TaskDao;
import com.youibot.vehicle.scheduler.modules.task.dao.TaskNodeDao;
import com.youibot.vehicle.scheduler.modules.task.entity.TaskEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 历史数据清理线程
 */
@Component
@Slf4j
public class HistoryDataCleanThread {

    @Autowired
    private SystemPropertyService systemPropertyService;
    @Autowired
    private OperationLogDao operationLogDao;
    @Autowired
    private SysLogDao sysLogDao;
    @Autowired
    private NoticeRecordDao noticeRecordDao;
    @Autowired
    private AbnormalStatisticsDao abnormalStatisticsDao;
    @Autowired
    private TaskTypeStatisticsDao taskTypeStatisticsDao;
    @Autowired
    private VehicleStatisticsDao vehicleStatisticsDao;
    @Autowired
    private TaskDao taskDao;
    @Autowired
    private TaskNodeDao taskNodeDao;
    @Autowired
    private VehicleStatusRecordDao vehicleStatusRecordDao;


    /**
     * 自动删除以下数据， 定时调度每天凌晨2点整
     * <p>
     * 1、操作日志（OperationLog），finishTime为检索字段
     * 2、接口日志（OperationLog），finishTime为检索字段
     * 3、运行日志（SysLog），lastTime为检索字段
     * 4、通知日志（NoticeRecord）只删除已关闭通知，closeTime为检索字段
     * 5、业务数据（Task,TaskNode,Event）只删除已完成任务/时间，endTime，updateDate为检索字段
     * 6、报表数据（VehicleStatistics,TaskTypeStatistics,AbnormalStatistics）,statisticsDate为检索字段
     * 7、机器人状态记录（VehicleStatusRecord配置在配置文件，默认保留7天）,endTime为检索字段
     */
    @Scheduled(cron = "0 0 2 * * ? ")
    public void job() {
        log.debug("历史数据清理开始");
        SystemConfigEntity systemConfig = systemPropertyService.getSystemConfig();
        deleteOperationLog(systemConfig);
        deleteInterfaceLog(systemConfig);
        deleteSysLog(systemConfig);
        deleteNoticeRecord(systemConfig);
        deleteTaskRecord(systemConfig);
        deleteStatistics(systemConfig);
        deleteVehicleStatusRecord(systemConfig);
        log.debug("历史数据清理结束");
    }

    /**
     * 清理操作日志
     * @param configEntity
     */
    private void deleteOperationLog(SystemConfigEntity configEntity) {
        Integer userOptLogExpireTime = Optional.ofNullable(configEntity).map(SystemConfigEntity::getUserOptLogExpireTime).orElse(100);
        Date date = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -userOptLogExpireTime).toJdkDate();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("type","Operate");
        queryWrapper.lt("finish_time",date);
        operationLogDao.delete(queryWrapper);
    }

    public static void main(String[] args) {
        Date date = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -10).toJdkDate();
        System.out.println(DateUtils.format(date,DateUtils.DATE_TIME_PATTERN));
    }

    /**
     * 清理接口日志
     * @param configEntity
     */
    private void deleteInterfaceLog(SystemConfigEntity configEntity) {
        Integer interfaceLogExpireTime = Optional.ofNullable(configEntity).map(SystemConfigEntity::getInterfaceLogExpireTime).orElse(10);
        Date date = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -interfaceLogExpireTime).toJdkDate();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("type","Api");
        queryWrapper.lt("finish_time",date);
        operationLogDao.delete(queryWrapper);
    }

    /**
     * 清理运行日志
     * @param configEntity
     */
    private void deleteSysLog(SystemConfigEntity configEntity) {
        Integer runningLogExpireTime = Optional.ofNullable(configEntity).map(SystemConfigEntity::getRunningLogExpireTime).orElse(10);
        Date date = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -runningLogExpireTime).toJdkDate();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lt("last_time",date);
        sysLogDao.delete(queryWrapper);
    }

    /**
     * 清理通知日志
     * 只删除已关闭通知，closeTime为检索字段
     * @param configEntity
     */
    private void deleteNoticeRecord(SystemConfigEntity configEntity) {
        Integer notificationExpireTime = Optional.ofNullable(configEntity).map(SystemConfigEntity::getNotificationExpireTime).orElse(30);
        Date date = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -notificationExpireTime).toJdkDate();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("status",2);
        queryWrapper.lt("close_time",date);
        noticeRecordDao.delete(queryWrapper);
    }

    /**
     * 清理业务数据
     * 只删除已完成任务/时间，endTime
     * @param configEntity
     */
    private void deleteTaskRecord(SystemConfigEntity configEntity) {
        Integer businessDataExpireTime = Optional.ofNullable(configEntity).map(SystemConfigEntity::getBusinessDataExpireTime).orElse(365);
        Date date = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -businessDataExpireTime).toJdkDate();

        QueryWrapper queryIdListWrapper = new QueryWrapper();
        queryIdListWrapper.select("id").in("status",Arrays.asList("Finished","Cancel"));
        queryIdListWrapper.lt("end_time",date);
        List<TaskEntity> list = taskDao.selectList(queryIdListWrapper);
        if(!CollectionUtils.isEmpty(list)){
            List<Long> ids = list.stream().map(TaskEntity::getId).collect(Collectors.toList());
            taskDao.deleteBatchIds(ids);
            //删除对应的节点
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.in("task_id",ids);
            taskNodeDao.delete(queryWrapper);
        }
    }

    /**
     * 清理报表数据
     * @param configEntity
     */
    private void deleteStatistics(SystemConfigEntity configEntity) {
        Integer reportDataExpireTime = Optional.ofNullable(configEntity).map(SystemConfigEntity::getReportDataExpireTime).orElse(365);
        Date date = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -reportDataExpireTime).toJdkDate();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lt("statistics_date",date);
        abnormalStatisticsDao.delete(queryWrapper);
        taskTypeStatisticsDao.delete(queryWrapper);
        vehicleStatisticsDao.delete(queryWrapper);
    }

    /**
     * 清理机器人状态记录
     * @param configEntity
     */
    private void deleteVehicleStatusRecord(SystemConfigEntity configEntity) {
        int vehicleStatusRecordExpireTime = AGVPropertiesUtils.getInt("vehicle.status-record.expireTime", 7);
        vehicleStatusRecordExpireTime = vehicleStatusRecordExpireTime <= 0 ? 7 : vehicleStatusRecordExpireTime;
        Date date = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -vehicleStatusRecordExpireTime).toJdkDate();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.lt("end_time",date);
        vehicleStatusRecordDao.delete(queryWrapper);
    }

}
