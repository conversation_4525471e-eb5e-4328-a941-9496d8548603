package com.youibot.vehicle.scheduler;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;


@SpringBootApplication(exclude = {
        org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class
})
@EnableRetry
@EnableScheduling
@EnableAsync
@Slf4j
public class AdminApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(AdminApplication.class, args);
        log.info(
                "\n======================================" +
                        "\n|----------YOUI-FLEET启动成功---------|" +
                        "\n======================================");
        Environment env = context.getEnvironment();
        try {
            String ip = InetAddress.getLocalHost().getHostAddress();
            String port = env.getProperty("server.port");
            String path = env.getProperty("server.servlet.context-path") == null ? "" : env.getProperty("server.servlet.context-path");
            log.info("\n----------------------------------------------------------\n\t" +
                    "Application YOUI-FLEET is running! Access URLs:\n\t" +
                    "Local: \t\thttp://localhost:" + port + path + "\n\t" +
                    "External: \thttp://" + ip + ":" + port + path + "\n\t" +
                    "Api-doc: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                    "----------------------------------------------------------");
        } catch (Exception e) {
            log.error("获取本地IP地址异常:===>>{}", Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(AdminApplication.class);
    }
}