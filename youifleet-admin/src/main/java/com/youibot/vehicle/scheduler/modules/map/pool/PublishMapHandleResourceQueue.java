package com.youibot.vehicle.scheduler.modules.map.pool;

import com.youibot.vehicle.scheduler.modules.map.dto.PublishMap;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2023/5/18 10:49
 */
@Component
public class PublishMapHandleResourceQueue {

    /**
     * 发布地图时, 插入数据, 资源占用后进行路径重规划
     */
    private ConcurrentLinkedQueue<PublishMap> mapPublishQueue = new ConcurrentLinkedQueue<>();

    public void add(PublishMap publishMap) {
        mapPublishQueue.add(publishMap);
    }

    public void addAll(List<PublishMap> publishMaps) {
        mapPublishQueue.addAll(publishMaps);
    }

    public PublishMap poll() {
        return mapPublishQueue.poll();
    }

    public List<PublishMap> pollAll() {
        List<PublishMap> list = new LinkedList<>();
        while (mapPublishQueue.iterator().hasNext()) {
            list.add(mapPublishQueue.poll());
        }
        return list;
    }

    public boolean hasNext() {
        return mapPublishQueue.iterator().hasNext();
    }

}
