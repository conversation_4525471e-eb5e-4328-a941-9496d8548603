package com.youibot.vehicle.scheduler.modules.task.dto;

import com.youibot.vehicle.scheduler.modules.task.constant.NodeCategory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "NodeConfigDTO",description = "节点配置")
public class NodeConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "节点类型")
    private String type;

    @ApiModelProperty(value = "节点名称")
    private String name;

    @ApiModelProperty(value = "节点提示")
    private String notice;

    /**
     * 动作分类
     * 流程控制=Process  机器人控制=Vehicle  分配资源=AllocationResource  获取资源=ObtainResource  通讯组件=Communication  其它=Other
     */
    @ApiModelProperty(value = "动作分类 流程控制=Process  机器人控制=Vehicle  分配资源=AllocationResource  获取资源=ObtainResource  通讯组件=Communication  其它=Other")
    private String category;

    @ApiModelProperty(value = "是否常用节点")
    private Boolean isCommon;

    @ApiModelProperty(value = "是否允许跳过")
    private Boolean isAllowSkip;

    @ApiModelProperty(value = "是否允许重试")
    private Boolean isAllowRetry;

    @ApiModelProperty(value = "节点执行异常时，是否允许声光报警，默认不允许")
    private Boolean isAllowAlarm;

    @ApiModelProperty(value = "重试次数")
    private Long retryNum;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "创建者")
    private Long  creator;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "修改人")
    private Long updater;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "节点参数")
    private List<NodeParamConfigDTO> nodeParamConfigDTOList;

    @ApiModelProperty(value = "入参数")
    private Integer enterParamCount = 0;

    @ApiModelProperty(value = "出参数")
    private Integer outParamCount = 0;

    public int getSort() {
        return NodeCategory.getByValue(category).getSort();
    }
}
