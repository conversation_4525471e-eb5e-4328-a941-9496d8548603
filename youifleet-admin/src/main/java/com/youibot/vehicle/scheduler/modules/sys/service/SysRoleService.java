package com.youibot.vehicle.scheduler.modules.sys.service;


import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.service.BaseService;
import com.youibot.vehicle.scheduler.modules.sys.dto.SysRoleDTO;
import com.youibot.vehicle.scheduler.modules.sys.entity.SysRoleEntity;

import java.util.List;
import java.util.Map;


/**
 * 角色
 */
public interface SysRoleService extends BaseService<SysRoleEntity> {

	PageData<SysRoleDTO> page(Map<String, Object> params);

	List<SysRoleDTO> list(Map<String, Object> params);

	SysRoleDTO get(Long id);

	SysRoleDTO save(SysRoleDTO dto);

	void update(SysRoleDTO dto);

	void delete(Long[] ids);

    List<String> getRoleNamesByUserId(Long userId);
}
