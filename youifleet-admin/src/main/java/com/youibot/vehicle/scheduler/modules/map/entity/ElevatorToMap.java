package com.youibot.vehicle.scheduler.modules.map.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.youibot.vehicle.scheduler.common.validator.group.AddGroup;
import com.youibot.vehicle.scheduler.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("elevator_to_map")
public class ElevatorToMap {

    /**
     * 编码
     */
    @TableId(value = "code")
    private String code;

    /**
     * 电梯编码
     */
    private String elevatorCode;

    /**
     * 地图编码
     */
    private String vehicleMapCode;

    /**
     * 点位编码
     */
    private String markerCode;

    /**
     * 角度
     */
    private Double angle;

    /**
     * 电梯外呼开门地址
     */
    private Integer outOperateAddress;

    /**
     * 电梯外呼开门值
     */
    private Integer outOperateOpenValue;

    /**
     * 电梯内呼开门地址
     */
    private Integer innerOperateAddress;

    /**
     * 电梯内呼开门值
     */
    private Integer innerOperateOpenValue;

    /**
     * 电梯门状态地址
     * 门状态：1：开、0：关
     */
    private Integer openAddress;

    /**
     * 电梯开门状态值
     */
    private Integer openStatusValue;

    /**
     * 电梯关门状态值
     */
    private Integer closeStatusValue;

    /**
     * 电梯到达地址
     */
    private Integer destAddress;

    /**
     * 电梯到达状态值
     */
    private Integer destStatusValue;

}
