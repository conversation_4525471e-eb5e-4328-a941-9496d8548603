package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(value = "BatchUpdatePathDTO", description = "用于渲染的精简路径参数")
public class BatchUpdatePathDTO implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchUpdatePathDTO.class);

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "地图编码", position = 2)
    private String vehicleMapCode;

    @ApiModelProperty(value = "定位图编码",position = 3)
    private String locatingCode;

    @ApiModelProperty(value = "开始标记点", position = 4)
    private String startMarkerCode;

    @ApiModelProperty(value = "结束标记点", position = 5)
    private String endMarkerCode;

    @ApiModelProperty(value = "开始点的控制点xy坐标", position = 6)
    private String startControl;

    @ApiModelProperty(value = "结束点的控制点xy坐标", position = 7)
    private String endControl;

    @ApiModelProperty(value = "长度", position = 8)
    private Double length;

    @ApiModelProperty(value = "线类型 1、直线 2、曲线（只要不为1,目前都默认为曲线）", position = 9)
    private Integer lineType = 2;

    @ApiModelProperty(value = "车头朝向: none 自适应,0,90,180,-90,-180", position = 10)
    private Integer agvDirection;

    @ApiModelProperty(value = "双向路径的共同编码", position = 11)
    private String commonPathCode;

}
