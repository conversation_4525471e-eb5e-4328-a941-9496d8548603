package com.youibot.vehicle.scheduler.modules.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "MapInfoUpdateDTO", description = "定位图更新")
public class MapInfoUpdateDTO {

    @ApiModelProperty(value = "地图编码")
    private String vehicleMapCode;

    @ApiModelProperty(value = "定位图编码")
    private String locatingCode;

    @ApiModelProperty(value = "绑定的机器人类型编码,按逗号,分割")
    private String vehicleTypeCodes;

    @ApiModelProperty(value = "旋转角度")
    private Double angle;

}
