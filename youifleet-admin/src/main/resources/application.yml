# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 1000
      min-spare: 30
  port: 8080
  servlet:
    context-path: /fleet
    session:
      cookie:
        http-only: true

spring:
  # 环境 dev|test|prod
  profiles:
    active: prod
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB
      enabled: true
  redis:
    database: 0
    host: *************
    port: 6379
    password: # 密码（默认为空）
    timeout: 6000ms # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接

# 是否开启redis缓存  true开启   false关闭
fleet.redis.open: false

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.youibot.vehicle.scheduler.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ID_WORKER
      update-strategy: ignored
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

vehicle:
  socket-server:
    port: 16646
    timeout: 10000
  #小车状态记录保留时间：单位，天
  status-record:
    expireTime: 7

liteflow:
  #规则文件路径
  rule-source: flow/flow.el.xml
  #-----------------以下非必须-----------------
  #FlowExecutor的execute2Future的线程数，默认为64
  main-executor-works: 5000
  #Fleet 默认主执行器生成器
  main-executor-class: com.youibot.vehicle.scheduler.engine.execute.executor.FleetDefaultMainExecutorBuilder
  #异步线程最长的等待时间秒(只用于when)，默认值为15, 改为864000描述, 即10天
  when-max-wait-seconds: 864000
  #when节点全局异步线程池最大线程数，默认为16
  when-max-workers: 15000
  #when节点全局异步线程池等待队列数，默认为512
  when-queue-limit: 512
  #全局默认节点执行器
  node-executor-class: com.youibot.vehicle.scheduler.engine.execute.executor.FleetDefaultNodeExecutor
  #是否打印
  print-banner: false

#取消等待时间 10秒
task:
  exec-max-num: 200
  cancel-wait-time: 20

#优艾呼叫盒服务端口
callbox:
  port: 58940

AUTO_DOOR:
  #自动门控制线程睡眠时间 毫秒
  CONTROL_THREAD_SLEEP_TIME: 1000
  #风淋门控制线程睡眠时间 毫秒
  AIR_CONTROL_THREAD_SLEEP_TIME: 300

# 系统发布版本号 结构实例：v5.0.6.202407261515.20240617（主版本），YOUIFleet v5.0.4-dgjx.20240617（定制分支）
youifleet:
  system:
      # 运行模式： alone：单机，multi：多机
    mode: multi
    version: v5.0.13.202506271013

charge-station:
  socket-server:
    port: 16649
    timeout: 10000
  # 机器人充电停靠偏离角度，默认小于3度
  angle-offset: 3
exception-report-max:
  conflict-avoidance-count: 20