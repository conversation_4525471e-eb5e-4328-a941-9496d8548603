{"message": {"hello": "ããã«ã¡ã¯", "edit": "ç·¨é", "del": "åé¤", "add": "è¿½å ", "success": "<PERSON><PERSON>", "fail": "å¤±æ", "reasonIs": "çç±ï¼", "update": "æ´æ°", "download": "ãã¦ã³ã­ã¼ã", "delete": "åé¤", "export": "ã¨ã¯ã¹ãã¼ã", "multExport": "ä¸æ¬ã¨ã¯ã¹ãã¼ã", "import": "ã¤ã³ãã¼ã", "multImport": "ä¸æ¬ã¤ã³ãã¼ã", "switchEn": "è±èªã«åãæ¿ã", "switchzh": "ä¸­å½èªã«åãæ¿ã", "options": "æä½", "reset": "ãªã»ãã", "status": "ç¶æ", "statusList": {"0": "ç¡å¹å", "1": "<PERSON><PERSON><PERSON><PERSON>"}, "refresh": "æ´æ°", "nickname": "ã¢ã«ã¦ã³ã", "userName": "ã¦ã¼ã¶ã¼å", "menu": "ã¡ãã¥ã¼", "role": "ã­ã¼ã«", "describe": "èª¬æ", "updateTime": "æ´æ°æé", "createTime": "ä½ææé", "passWord": "ãã¹ã¯ã¼ã", "custom": "ã«ã¹ã¿ãã¤ãº", "requiredTips": "å¥åãã¦ãã ãã", "pleaseSelect": "é¸æãã¦ãã ãã", "enableList": {"0": "ã¯ã", "1": "ããã"}, "addForm": "æ°è¦ä½æ", "editForm": "ç·¨é", "cancel": "ã­ã£ã³ã»ã«", "submit": "éä¿¡", "totalData": "åè¨ {total} ä»¶", "type": "ã¿ã¤ã", "group": "ã°ã«ã¼ã", "range": "ç¯å²", "formRules": {"phoneLen": "æ­£ãã11æ¡ã®é»è©±çªå·ãå¥åãã¦ãã ãã", "port": "æ­£ãããã¼ãçªå·ãå¥åãã¦ãã ãã", "ip": "æ­£ããIPã¢ãã¬ã¹ãå¥åãã¦ãã ãã", "isLength": "é·ãã{max}æå­ãè¶ãã¦ãã¾ã", "isNumber": "å¤ã¯æ°å­ã®ã¿ã§ããå¿è¦ãããã¾ã", "englishFirst_": "è±æå­ï¼æåã®æå­ï¼ãã¢ã³ãã¼ã¹ã³ã¢ãæ°å­ã®ã¿ããµãã¼ã", "englishFirst": "è±æå­ï¼æåã®æå­ï¼ãæ°å­ã®ã¿ããµãã¼ã", "english": "è±æå­ã®ã¿ããµãã¼ã", "isName": "è±æå­ãã¢ã³ãã¼ã¹ã³ã¢ãæ°å­ã®ã¿ããµãã¼ã", "isPureNumber": "ç´ç²ãªæ°å­ã§ã¯ãªããæå¤§20æå­ã«ããå¿è¦ãããã¾ã"}, "ChargingMarker": "åé»ãã¤ã³ã", "ParkingMarker": "é§è»ãã¤ã³ã", "WorkMarker": "ä½æ¥­ãã¤ã³ã", "NavigationMarker": "ããã²ã¼ã·ã§ã³ãã¤ã³ã", "message": "ã¡ãã»ã¼ã¸", "language": "è¨èª", "languageSwitch": "è¨èªåæ¿", "importLanguage": "è¨èªãã¤ã³ãã¼ã", "my": "ãã¤ãã¼ã¸", "delTips": "åé¤ãã¦ãããããã§ããï¼åé¤ãããåå®¹ã¯å¾©åã§ãã¾ããã", "details": "è©³ç´°", "individuation": "åæ§å", "searchSettings": "æ¤ç´¢è¨­å®", "complete": "å®äº", "connectTimeOut": "ãããã¯ã¼ã¯æ¥ç¶ãã¿ã¤ã ã¢ã¦ããã¾ãã", "vehicle": "ã­ããã", "lowBattery": "ããããªã¼æ®éå°ãªã", "highBattery": "ããããªã¼æ®éé«ã", "pleaseSelectOneVehicle": "å°ãªãã¨ã1å°ã®ã­ããããé¸æãã¦ãã ãã", "second": "ç§", "minute": "å", "minutes": "å", "hour": "æé", "hour1": "æ", "day": "æ¥", "angle": "è§åº¦", "speed": "éåº¦", "orientation": "ã­ã¼ã«ã©ã¤ã¼ã¼ã·ã§ã³", "ip": "IP", "online": "ãªã³ã©ã¤ã³", "unconnected": "æªæ¥ç¶", "abnormal": "ç°å¸¸", "inExecution": "å®è¡ä¸­", "encoding": "ID", "join": "æ¥ç¶", "professional": "ä½æ¥­", "controls": "å¶å¾¡", "all": "å¨ã¦", "previous": "åã®ãã¼ã¸", "nextPage": "æ¬¡ã®ãã¼ã¸", "thereIsNoPublishedTaskType": "æå¹åãããã¿ã¹ã¯ãã­ã¼ã¯å­å¨ãã¾ãã", "solution": "è§£æ±ºç­", "dragTheMapFileZipIntoThisArea": "ããããã¡ã¤ã«ããã®ã¨ãªã¢ã«ãã©ãã°ãã¦ãã ãã", "dragLocationMapFileZipIntoThisArea": "ã­ã±ã¼ã·ã§ã³å³ãã¡ã¤ã«ããã®ã¨ãªã¢ã«ãã©ãã°ãã¦ãã ãã", "orClickHereToUpload": "ã¾ãã¯ãããã¯ãªãã¯ãã¦ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã", "fileImport": "ãã¡ã¤ã«ã¤ã³ãã¼ã", "uploadSuccessfully": "ã¢ããã­ã¼ãæå", "confirm": "ç¢ºèª", "listSetting": "ãªã¹ãè¨­å®", "pleaseSelectAtLeastOneItem": "å°ãªãã¨ã1ã¤ã®é ç®ãé¸æãã¦ãã ãã", "clickToUpload": "ã¯ãªãã¯ãã¦ã¢ããã­ã¼ã", "fileUploadFailed": "ãã¡ã¤ã«ã®ã¢ããã­ã¼ãã«å¤±æãã¾ãã", "deleteOrNot": "åé¤ãç¢ºèªãã¾ãã", "messageCannotReturn": "åé¤ãããåå®¹ã¯å¾©åã§ãã¾ãã", "quit": "çµäº", "append": "è¿½å ", "deleteAll": "ãã¹ã¦åé¤", "port": "ãã¼ã", "required": "å¿é ", "variable": "å¤æ°", "defaultValue": "ããã©ã«ãå¤", "maximumValue": "æå¤§å¤", "minimumValue": "æå°å¤", "yes": "ã¯ã", "no": "ããã", "value": "å¤", "typeName": "ãã¼ãå", "batch": "ä¸æ¬", "create": "ä½æ", "batchCreate": "ä¸æ¬ä½æ", "login": "ã­ã°ã¤ã³", "updateAirShower": "ã¨ã¢ã·ã£ã¯ã¼ãã¢æ´æ°", "updateArea": "ã¨ãªã¢æ´æ°", "updateAutodoor": "èªåãã¢æ´æ°", "updateElevator": "ã¨ã¬ãã¼ã¿ã¼æ´æ°", "updateMap": "å°å³æ´æ°", "updateMarker": "ãã¤ã³ãæ´æ°", "updatePath": "çµè·¯æ´æ°", "deletePath": "çµè·¯åé¤", "relocationManual": "åã­ã¼ã«ã©ã¤ã¼ã¼ã·ã§ã³", "scrapDraft": "ä¸æ¸ãå»æ£", "publish": "å¬é", "copy": "ã³ãã¼", "name": "åç§°", "serialNumber": "ã·ãªã¢ã«çªå·", "startTime": "éå§æé", "endTime": "çµäºæé", "createTask": "ã¿ã¹ã¯ä½æ", "noData": "ãã¼ã¿ãªã", "arguments": "ãã©ã¡ã¼ã¿", "priority": "åªååº¦", "selectTheDataYouWantToDelete": "åé¤ãããã¼ã¿ãé¸æãã¦ãã ãã", "selectTheDataThatYouWantToModify": "ç·¨éãããã¼ã¿ãé¸æãã¦ãã ãã", "passwordInconsistencyTip": "ãã¹ã¯ã¼ãã¨ç¢ºèªãã¹ã¯ã¼ããä¸è´ãã¾ãããåå¥åãã¦ãã ããã", "theEnteredPasswordIsInconsistent": "å¥åããããã¹ã¯ã¼ããä¸è´ãã¾ãã", "systemServiceException": "ããã¯ã¨ã³ããµã¼ãã¹ã®å¼ã³åºãã«å¤±æãã¾ãã", "founder": "ä<PERSON><PERSON><PERSON>", "result": "çµæ", "meter": "ã¡ã¼ãã«", "file": "ãã¡ã¤ã«", "creationMode": "ããªã¬ã¼ã¿ã¤ã", "externalCoding": "å¤é¨ã³ã¼ã", "task": "ã¿ã¹ã¯", "default": "ããã©ã«ã", "remark": "<PERSON><PERSON>", "addRemark": "<PERSON><PERSON><PERSON>¿½å ", "form": "ãã©ã¼ã ", "height": "é«ã", "point": "ãã¤ã³ã", "noDataAvailable": "ãã¼ã¿ãããã¾ãã", "cm": "ã»ã³ãã¡ã¼ãã«", "userSetting": {"roleManage": "ã­ã¼ã«ç®¡ç", "accountManage": "ã¢ã«ã¦ã³ãç®¡ç", "auth": "æä½æ¨©é", "addRole": "ã­ã¼ã«è¿½å ", "rename": "ååå¤æ´", "account": "ã¢ã«ã¦ã³ã", "name": "åå", "role": "ã­ã¼ã«", "email": "ã¡ã¼ã«", "phone": "é»è©±çªå·", "enable": "æ<PERSON><PERSON><PERSON>¶æ", "updateDate": "æ´æ°æ¥æ", "automaticLogout": "ã­ã°ã¤ã³æé", "password": "ãã¹ã¯ã¼ã", "surePassword": "ãã¹ã¯ã¼ãç¢ºèª", "enableList": {"1": "ã¯ã", "0": "ããã"}, "roleName": "å¤ã¯20æå­ä»¥å;ä¸­å½èªãè±èªãæ°å­ã®ã¿è¨±å¯", "username": "å¤ã¯20æå­ä»¥å;è±å­ã§å§ããå¿è¦ããããè±å­ãã¢ã³ãã¼ã¹ã³ã¢ãæ°å­ã®ã¿è¨±å¯", "realName": "å¤ã¯20æå­ä»¥å", "autoLogoutTime": "ç¯å²ï¼[0,9999]", "changePassword": "ãã¹ã¯ã¼ãå¤æ´", "logout": "ã­ã°ã¢ã¦ã", "pleaseSelectARole": "ã­ã¼ã«ãé¸æãã¦ãã ãã", "nameTip": "åå®¹ãå¥åãã¦ãã ãã", "theNameCannotExceed20Characters": "20æå­ä»¥åã«å¥åãã¦ãã ãã"}, "changePassword": {"changePassword": "ãã¹ã¯ã¼ãå¤æ´", "resetPassword": "ãã¹ã¯ã¼ããªã»ãã", "oldPassword": "æ§ãã¹ã¯ã¼ã", "loginUserPassword": "ã­ã°ã¤ã³ãã¹ã¯ã¼ã", "newPassword": "æ°ãããã¹ã¯ã¼ã", "confirmNewPassword": "æ°ãããã¹ã¯ã¼ããç¢ºèª"}, "map": {"markerName": "å¤ã¯20æå­ãè¶ãããè±å­ã§å§ããå¿è¦ãããã¾ããè±å­ãã¢ã³ãã¼ã¹ã³ã¢ãæ°å­ã®ã¿è¨±å¯", "type": {"Elevator": "ã¨ã¬ãã¼ã¿ã¼", "AutoDoor": "èªåãã¢", "MapArea": "ã¨ãªã¢", "Marker": "ãã¤ã³ã", "Vehicle": "ã­ããã", "All": "å¨ã¦ã®çµæ", "AirShowerDoor": "ã¨ã¢ã·ã£ã¯ã¼ãã¢"}, "autoDoor": "èªåãã¢", "manualDoorOpening": "<PERSON><PERSON><PERSON><PERSON>", "manualDoorClosing": "æåéãã", "automaticDoorCoding": "èªåãã¢ã³ã¼ã", "autoDoorStatus": {"OPEN": "éãã¦ãã", "CLOSE": "éãã¦ãã", "COMMUNICATION_ERROR": "éä¿¡ã¨ã©ã¼", "OPERATING": "å¶å¾¡ä¸­", "PARAM_ERROR": "ãã©ã¡ã¼ã¿ã¨ã©ã¼"}, "areaTypes": {"ControlArea": "å°éã¨ãªã¢", "SingleAgvArea": "åæ©ã¨ãªã¢", "ShowArea": "ã¦ã¼ã¶ã¼ãã¼ã¯ã¨ãªã¢", "ChannelArea": "éè·¯ã¨ãªã¢", "NoRotatingArea": "åè»¢ç¦æ­¢ã¨ãªã¢", "NoParkingArea": "é§è»ç¦æ­¢ã¨ãªã¢", "ForbiddenArea": "ç«å¥ç¦æ­¢ã¨ãªã¢", "TrafficArea": "äº¤éç®¡çã¨ãªã¢ï¼ãµã¼ãå´ï¼", "ThirdSystemTrafficArea": "äº¤éç®¡çã¨ãªã¢ï¼ã¯ã©ã¤ã¢ã³ãå´ï¼"}, "markerTypes": {"ChargingMarker": "åé»ãã¤ã³ã", "WorkMarker": "ä½æ¥­ãã¤ã³ã", "NavigationMarker": "ããã²ã¼ã·ã§ã³ãã¤ã³ã"}, "AirShowerDoor": "ã¨ã¢ã·ã£ã¯ã¼ãã¢", "showerDoorCode": "ã¨ã¢ã·ã£ã¯ã¼ãã¢ã³ã¼ã", "manualDoorOpening1": "æåéã1", "manualDoorClosing1": "æåéãã1", "manualDoorOpening2": "æåéã2", "manualDoorClosing2": "æåéãã2", "gate1Status": "ãã¢1ç¶æ", "gate2State": "ãã¢2ç¶æ", "areaCoding": "ã¨ãªã¢ã³ã¼ã", "robotCoding": "ã­ãããã³ã¼ã", "offSite": "<PERSON><PERSON> <PERSON>", "pointPosition": "ãã¤ã³ãä½ç½®", "coordinate": "åº§æ¨", "elevator": "ã¨ã¬ãã¼ã¿ã¼", "elevatorCode": "ã¨ã¬ãã¼ã¿ã¼ã³ã¼ã", "searchMapElements": "å°å³è¦ç´ ãæ¤ç´¢", "currentMap": "ç¾å¨ã®å°å³", "map": "å°å³", "currentCoordinate": "ç¾å¨ã®åº§æ¨", "pointType": "ãã¤ã³ãã¿ã¤ã", "customCoding": "ã«ã¹ã¿ã ã³ã¼ã", "pointCoding": "ãã¤ã³ãã³ã¼ã", "batchNew": "ä¸æ¬æ°è¦ä½æ", "lineNumber": "è¡æ°", "lineSpacing": "è¡éé", "numberOfColumns": "åæ°", "spaceBetweenColumns": "<PERSON><PERSON><PERSON>", "element": "è¦ç´ ", "searchElement": "è¦ç´ æ¤ç´¢", "area": "ã¨ãªã¢", "select": "é¸æ", "justificationLeft": "å·¦æã", "justifyRight": "å³æã", "topJustification": "ä¸æã", "alignBottom": "ä¸æã", "horizontalEquidistance": "æ°´å¹³ç­éé", "verticalEquidistance": "<PERSON><PERSON><PERSON><PERSON>­<PERSON><PERSON>", "exportDraft": "ä¸æ¸ãã¨ã¯ã¹ãã¼ã", "publishMap": "å°å³å¬é", "scrapDraft": "ä¸æ¸ãå»æ£", "elementList": "è¦ç´ ãªã¹ã", "unidirectionalPath": "åæ¹åçµè·¯", "bidirectionalPath": "åæ¹åçµè·¯", "quiescentTime": "éæ­¢æé", "networkIp": "ãããã¯ã¼ã¯IP", "door": "ãã¢", "networkPort": "ãããã¯ã¼ã¯ãã¼ã", "openDoorControlAddress": "ãã¢ééå¶å¾¡ã¢ãã¬ã¹", "openDoorControlAddressTip": "ã¨ã¢ã·ã£ã¯ã¼ãã¢ãéãå¶å¾¡ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦æ¸ãè¾¼ã¿ã¾ã", "openAutoDoorControlAddressTip": "èªåãã¢ãéãå¶å¾¡ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦æ¸ãè¾¼ã¿ã¾ã", "doorControlAddress": "ãã¢éããå¶å¾¡ã¢ãã¬ã¹", "doorControlAddressTip": "ã¨ã¢ã·ã£ã¯ã¼ãã¢ãéããå¶å¾¡ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦æ¸ãè¾¼ã¿ã¾ã", "autoDoorControlAddressTip": "èªåãã¢ãéã<PERSON>å¶å¾¡ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦æ¸ãè¾¼ã¿ã¾ã", "openStateAddress": "ãã¢éãç¶æã¢ãã¬ã¹", "openStateAddressTip": "ã¨ã¢ã·ã£ã¯ã¼ãã¢ãå®å¨ã«éããç¶æã®ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦èª­ã¿åãã¾ã", "autoOpenStateAddressTip": "èªåãã¢ãå®å¨ã«éããç¶æã®ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦èª­ã¿åãã¾ã", "closedAddress": "éãã¢ç¶æã¢ãã¬ã¹", "closedAddressTip": "ã¨ã¢ã·ã£ã¯ã¼ãã¢ãå®å¨ã«éããç¶æã®ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦èª­ã¿åãã¾ã", "autoClosedAddressTip": "èªåãã¢ãå®å¨ã«éããç¶æã®ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦èª­ã¿åãã¾ã", "bindingPath": "çµè·¯ã®ãã¤ã³ãã£ã³ã°", "pleaseSelectAPath": "ããã¤ã¹ã®ããçµè·¯ãé¸æãã¦ãã ãã", "noPointsOfIntersectionCanBeAdded": "ã¨ã¢ã·ã£ã¯ã¼ãã¢ã®2ã¤ã®çµè·¯ã«ã¯äº¤å·®ç¹ãå¿è¦ã§ã", "duplicatePathsCannotBeBound": "éè¤ãªçµè·¯ã¯ãã¤ã³ãã£ã³ã°ã§ãã¾ãã", "areaType": "ã¨ãªã¢ã¿ã¤ã", "ControlArea": "å°éã¨ãªã¢", "SingleAgvArea": "åæ©ã¨ãªã¢", "ShowArea": "ã¦ã¼ã¶ã¼ãã¼ã¯ã¨ãªã¢", "ChannelArea": "éè·¯ã¨ãªã¢", "NoRotatingArea": "åè»¢ç¦æ­¢ã¨ãªã¢", "NoParkingArea": "é§è»ç¦æ­¢ã¨ãªã¢", "ForbiddenArea": "ç«å¥ç¦æ­¢ã¨ãªã¢", "TrafficArea": "äº¤éç®¡çã¨ãªã¢ï¼ãµã¼ãå´ï¼", "ThirdSystemTrafficArea": "äº¤éç®¡çã¨ãªã¢ï¼ã¯ã©ã¤ã¢ã³ãå´ï¼", "multipleValues": "è¤æ°å¤", "displayName": "åç§°è¡¨ç¤º", "areaColor": "ã¨ãªã¢ã«ã©ã¼", "mapPoint": "å°å³ãã¤ã³ã", "callAddress": "ã¨ã¬ãã¼ã¿ã¼å¼åºã¢ãã¬ã¹", "callAddressTip": "ã¨ã¬ãã¼ã¿ã¼ãä¸ä¸ç§»åãããå¶å¾¡ã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦æ¸ãè¾¼ã¿ã¾ã", "arrivalStatusAddress": "å°çç¶æã¢ãã¬ã¹", "arrivalStatusAddressTip": "ã¨ã¬ãã¼ã¿ã¼ãæå®ãããå°å³ã«å°çããããç¢ºèªããã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦èª­ã¿åãã¾ã", "statusAddressOfTheLadderDoor": "ã¨ã¬ãã¼ã¿ã¼ãã¢ç¶æã¢ãã¬ã¹", "statusAddressOfTheLadderDoorTip": "ã¨ã¬ãã¼ã¿ã¼ãã¢ãéãã¦ãããç¢ºèªããã¢ãã¬ã¹ãã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã¯æ©è½ã³ã¼ããä½¿ç¨ãã¦èª­ã¿åãã¾ã", "path": "çµè·¯", "mapCoding": "å°å³ã³ã¼ã", "mapName": "å°å³åç§°", "mapType": "å°å³ã¿ã¤ã", "mapResolution": "å°å³è§£ååº¦", "mapSize": "å°å³ãµã¤ãº", "originMigration": "åç¹ãªãã»ãã", "releaseTime": "å¬éæé", "qrCodeMap": "QRã³ã¼ãå°å³", "laserMap": "ã¬ã¼ã¶ã¼å°å³", "normalResolution": "éå¸¸è§£ååº¦", "highResolution": "é«è§£ååº¦", "enableParking": "é§è»æå¹å", "networkType": "çµè·¯ãããã¿ã¤ã", "networkTypeList": {"0": "äº¤å·®çµè·¯ããããã¤ã³ã", "1": "éå¸¸çµè·¯ããããã¤ã³ã"}, "chargingAttribute": "åé»å±æ§", "chargingDirection": "<PERSON><PERSON>»æ¹å", "dockingType": "ããã­ã³ã°ã¿ã¤ã", "buttJoint": "æ­£é¢ããã­ã³ã°", "buttbutt": "å¾é¨ããã­ã³ã°", "leftSideDocking": "å·¦å´æ¥ç¶", "rightSideDocking": "å³å´æ¥ç¶", "reflectiveStripFeaturesContactPoints": "åå°ã¹ããªããç¹å¾´ããã­ã³ã°ãã¤ã³ã", "vTypeFeatureContact": "Våç¹å¾´ããã­ã³ã°ãã¤ã³ã", "pathType": "çµè·¯ã¿ã¤ãç±»å", "stationCode": "ä½æ¥­ã¹ãã¼ã·ã§ã³ã³ã¼ã", "pathWeight": "çµè·¯ã¦ã§ã¤ã", "vehicleTypeRestriction": "è»ä¸¡ã¿ã¤ãå¶é", "lackOfRobotTypes": "ã­ãããã¿ã¤ããä¸è¶³", "movingSpeed": "ç§»åéåº¦", "rotationalSpeed": "åè»¢éåº¦", "movingAcceleration": "ç§»åå éåº¦", "rotationalAcceleration": "åè»¢å éåº¦", "moveObstacleAvoidanceArea": "ç§»åéå®³ç©æ¤åºã¨ãªã¢", "obstacleAvoidanceArea": "éå®³ç©æ¤åºã¨ãªã¢", "rotationObstacleRegion": "åè»¢éå®³ç©æ¤åºã¨ãªã¢", "noseDirection": "è»ä½åã", "potholeDetection": "ç©´æ¤åº", "dObstacleAvoidance": "3Déå®³æ¤åº", "featureNavigation": "ç¹å¾´ããã²ã¼ã·ã§ã³", "navigationPath": "ããã²ã¼ã·ã§ã³çµè·¯", "QR_Down": "QRã³ã¼ãããã­ã³ã°", "LeaveDocking": "ããã­ã³ã°è§£é¤", "Shelflegs": "æ£èããã­ã³ã°", "Symbol_V": "Våç¹å¾´ããã­ã³ã°", "Reflector": "åå°ã·ã¼ã«ããã­ã³ã°", "Pallet": "ãã¬ããããã­ã³ã°", "unsetRegion": "ã¨ãªã¢ãè¨­å®ããªã", "pleaseSelectAPointPosition": "ãã¤ã³ããé¸æãã¦ãã ãã", "selectPoint": "ãã¤ã³ããé¸æãã¦ãã ãã", "aPointBitHasBeenSelected": "é¸ææ¸ã¿ã®ãã¤ã³ã", "clear": "ã¯ãªã¢", "pleaseSelectParkingLocation": "é¸æãã¦ãã ããï¼é§è»ãã¤ã³ã", "displayElement": "è¦ç´ è¡¨ç¤º", "pathDirection": "çµè·¯æ¹å", "displayText": "è¡¨ç¤ºãã­ã¹ã", "areaName": "ã¨ãªã¢åç§°", "elementSize": "è¦ç´ ãµã¤ãº", "forceTheElementSizeToChange": "è¦ç´ ãµã¤ãºãå¼·å¶å¤æ´", "backgroundSettings": "èæ¯è¨­å®", "operationHabit": "æä½ç¿æ£", "doubleClickCreateElement": "ããã«ã¯ãªãã¯ã§è¦ç´ ãä½æ", "backgroundColor": "èæ¯è²", "showBackground": "èæ¯ç»åè¡¨ç¤º", "displaysThePngDiagramBorder": "å°å³å¢çè¡¨ç¤º", "limitTheDistanceBetweenPointAndPoint": "ãã¤ã³ãéã®æç­è·é¢", "limitTheDistanceBetweenPointsAndPaths": "ãã¤ã³ãã¨çµè·¯ã®è·é¢ãå¶é", "displayRobot": "AMRãè¡¨ç¤º", "realTimePointCloud": "ãªã¢ã«ã¿ã¤ã ç¹ç¾¤", "mapEditor": "å°å³ç·¨é", "monitoring": "ã¢ãã¿ãªã³ã°", "editParameter": "ãã©ã¡ã¼ã¿ãç·¨é", "parkingOrNot": "é§è»ãã¾ãã", "noMapYet": "å°å³ã¯ã¾ã ããã¾ãã", "pleaseRepositionTheRobot": "AMRãåéç½®ãã¦ãã ãããEscã§æä½ãã­ã£ã³ã»ã«", "pleaseSelectAnEndpoint": "EndPointãé¸æãã¦ãã ãã", "recordingPoint": "ãã¤ã³ããè¨é²", "pointRecordingSucceeded": "ãã¤ã³ãã®è¨é²ãæåãã¾ãã", "discardDraftTip": "ãã®æä½ã¯åã«æ»ãã¾ãããç ´æ£ãããç¢ºèªãã¦ãã ãã?", "publishMapOrNot": "å°å³ãå¬éãã¾ããï¼", "failedToPublishMap": "å°å³ã®å¬éã«å¤±æãã¾ãã", "publishMapSuccessfullyTip": "å°å³ã®å¬éã«æåãã¾ãããAMRã«èªååæããã¾ã", "exitTheRecordingPoint": "ãã¤ã³ãè¨é²ãçµäº", "recordingPointTip": "ââ â â ââ ç§»åã»åè»¢; âEnterâ ãã¤ã³ãè¨é²", "remoteControlMode": "â â â ââ ç§»åã»åè»¢; âEscâ ãªã¢ã¼ãæä½ã¢ã¼ãçµäº", "thereAreNoBotsOnTheCurrentMap": "ç¾å¨ã®å°å³ã«ã¯ã­ããããå­å¨ãã¾ãããã­ããããªã¹ããã¼ã¸ã§å°å³ãåãæ¿ãã¦ãã ãã", "thePathSelectionOperationIsCancelled": "çµè·¯é¸ææä½ãã­ã£ã³ã»ã«ãã¾ãã", "pleaseSelectRobot": "AMRãé¸æãã¦ãã ãã", "robotIsNotConnectedTip": "AMRãæ¥ç¶ããã¦ããããè¨é²ã«å¤±æãã¾ãã", "haveBeenPublishedTip": "ãã®å°å³ã¯æ¢ã«å¬éããã¦ãã¾ãããªã¹ãã«æ»ã£ã¦ãã ãã", "haveBeenDiscardedTip": "ãã®å°å³ã®ä¸æ¸ãã¯æ¢ã«ç ´æ£ããã¦ãã¾ãããªã¹ãã«æ»ã£ã¦ãã ãã", "thePointIsCreatedSuccessfully": "ãã¤ã³ãä½æããã¾ãã", "outletElevator": "ã¨ã¬ãã¼ã¿ã¼ãã¨ã¯ã¹ãã¼ã", "leadInElevator": "ã¨ã¬ãã¼ã¿ã¼ãã¤ã³ãã¼ã", "transferElevatorFileJson": "ã¨ã¬ãã¼ã¿ã¼ãã¡ã¤ã«ããã®ã¨ãªã¢ã«ãã©ãã°ï¼ãã­ãã", "doubleClickMultiplexing": "ããã«ã¯ãªãã¯ã§åå©ç¨", "parkingSign": "é§è»ãµã¤ã³", "locationMapList": "ã­ã±ã¼ã·ã§ã³å³ãªã¹ã", "locationMap": "ã­ã±ã¼ã·ã§ã³å³", "leadinMap": "ã­ã±ã¼ã·ã§ã³å³ãã¤ã³ãã¼ã", "setAsDefault": "ããã©ã«ããè¨­å®ãã¾ã", "setTheRobotType": "ã­ãããã¿ã¤ããè¨­å®ãã¾ã", "deleteLocationMap": "ã­ã±ã¼ã·ã§ã³å³ãåé¤", "locationMapDeletedTip": "ã­ã±ã¼ã·ã§ã³å³ãåé¤ãã¾ãããããä¸åº¦éãã¦ãã ããã", "currentAngle": "ç¾å¨ã®è§åº¦", "fixedAngle": "åºå®è§åº¦", "areavehicleTypeNameTip": "ã­ãããã¿ã¤ããç©ºã®å ´åããã¹ã¦ã®ã­ãããã¿ã¤ããæå¹ã«ãªãã¾ã", "directionToast": "å¤ã¯-180ãã180ã®éã§ãªããã°ãªãã¾ãã", "offsetX": "ãªãã»ããX", "offsetY": "ãªãã»ããY", "offsetAngle": "ãªãã»ããè§åº¦", "dockingDirection": "ããã­ã³ã°æ¹å", "Head": "æ­£é¢ããã­ã³ã°", "Tail": "å¾é¨ããã­ã³ã°", "revocation": "åãæ¶ã", "renewal": "ããç´ã", "cameraObstacleAvoidance": "ã«ã¡ã©éå®³æ¤åº", "templateNumber": "ãã³ãã¬ã¼ãçªå·", "text1": "ãã­ã¹ã1", "text2": "ãã­ã¹ã2", "text3": "ãã­ã¹ã3", "number1": "æ°å¤1", "number2": "æ°å¤2", "number3": "æ°å¤3", "obstacleAvoidance": "èªå¾éå®³ç©åé¿", "enableAvoidance": "<PERSON><PERSON>", "batchNewMaximumTips": "ä¸æ¬æ°è¦ä½æã®ãã¤ã³ãæ°ã¯æå¤§2000åã¾ã§å¶éããã¦ãã¾ã", "unmapped": "ã­ã±ã¼ã·ã§ã³å³ãã¼ã¿ãããã¾ãã", "key": "ãã£ã¼ã«ã", "value": "å¤", "addExtendParam": "ãã©ã¡ã¼ã¿ãè¿½å ", "fieldDplication": "ãã£ã¼ã«ãã¯éè¤ã§ãã¾ããï¼", "addOrientationAngle": "ããã²ã¼ã·ã§ã³è§åº¦ãè¿½å ", "checkDirectionalNavigation": "çµè·¯æ¤è¨¼ãã¹ã", "navigationAngle": "ããã²ã¼ã·ã§ã³è§åº¦", "operationFailedMandatoryFieldsCannotEmpty": "æä½å¤±æãç»é¢ã®å¿é é ç®ãå¥åãã¦ãã ãã", "readingFunctionCode": "èª­ã¿åãæ©è½ã³ã¼ã", "writingFeatureCode": "æ¸ãè¾¼ã¿æ©è½ã³ã¼ã", "elevatorUsageScenario": "ã¨ã¬ãã¼ã¿ä½¿ç¨ã·ããªãª", "elevatorModeControlAddress": "ã¨ã¬ãã¼ã¿ã¢ã¼ãå¶å¾¡ã¢ãã¬ã¹", "enterRobotModeValue": "ã­ãããã¢ã¼ãé²å¥å¤", "exitRobotModeValue": "ã­ãããã¢ã¼ãéåºå¤", "loadDetectedValue": "è·ç©æ¤åºç¶æå¤", "doorOpenedValue": "ãã¢éæ¾ç¶æå¤", "doorClosedValue": "ãã¢ééç¶æå¤", "arrivalStatusValue": "å°çç¶æå¤", "robotOnly": "ã­ãããå°ç¨", "humanAndRobotShared": "äººéã¨ã­ãããå±ç¨", "modeStatusAddress": "ã¢ã¼ãç¶æã¢ãã¬ã¹", "robotModeStatusValue": "ã­ãããã¢ã¼ãç¶æå¤", "outgoingAddress": "å¤é¨å¼ã³åºãã¢ãã¬ã¹", "internalCallAddress": "åé¨å¼ã³åºãã¢ãã¬ã¹", "outOperateOpenValue": "å¤é¨å¼ã³åºããã¢éãå¤", "innerOperateOpenValue": "åé¨å¼ã³åºããã¢éãå¤", "goodsCheckAddress": "å¨åº«ç¢ºèªã¢ãã¬ã¹", "currentStatus": "éä¿¡ç¶æ", "goodsCheckAddressTip": "é²å¥åã«è£ç½®åã«å¨åº«ããããç¢ºèªãã¾ããå¨åº«ãããå ´åãé²å¥ãã¾ããããã®ã¢ãã¬ã¹ãç©ºã®å ´åãç¢ºèªãè¡ãã¾ãã", "currentStatusObject": {"NORMAL": "éä¿¡æ­£å¸¸", "ERROR": "éä¿¡ç°å¸¸"}, "open": "éã", "close": "éãã", "occupyCode": "å æè", "occupyVehicleCode": "å æã­ãããã®ã³ã¼ã", "manualRelease": "æåè§£æ¾", "manualReleaseTip": "æä½ãåã«æ»ããã¨ã¯ã§ãã¾ãããã¨ãªã¢å æãè§£æ¾ãã¾ããï¼", "doubleClickModel": {"ControlArea": "ããã«ã¯ãªãã¯ã§å°éå¶å¾¡ã¨ãªã¢ãåå©ç¨", "SingleAgvArea": "ããã«ã¯ãªãã¯ã§åæ©ã¨ãªã¢ãåå©ç¨", "ShowArea": "ããã«ã¯ãªãã¯ã§ã¦ã¼ã¶ã¼ãã¼ã¯ã¨ãªã¢ãåå©ç¨", "ChannelArea": "ããã«ã¯ãªãã¯ã§éè·¯ã¨ãªã¢ãåå©ç¨", "NoRotatingArea": "ããã«ã¯ãªãã¯ã§æåç¦æ­¢ã¨ãªã¢ãåå©ç¨", "NoParkingArea": "ããã«ã¯ãªãã¯ã§é§è»ç¦æ­¢ã¨ãªã¢ãåå©ç¨", "ForbiddenArea": "ããã«ã¯ãªãã¯ã§é²å¥ç¦æ­¢ã¨ãªã¢ãåå©ç¨", "ChargingMarker": "ããã«ã¯ãªãã¯ã§åé»ãã¤ã³ããåå©ç¨", "WorkMarker": "ããã«ã¯ãªãã¯ã§ä½æ¥­ãã¤ã³ããåå©ç¨", "NavigationMarker": "ããã«ã¯ãªãã¯ã§ããã²ã¼ã·ã§ã³ãã¤ã³ããåå©ç¨", "unidirectionalPath": "ããã«ã¯ãªãã¯ã§åæ¹åçµè·¯ãåå©ç¨", "bidirectionalPath": "ããã«ã¯ãªãã¯ã§åæ¹åçµè·¯ãåå©ç¨", "TrafficArea": "ããã«ã¯ãªãã¯ã§äº¤éã¨ãªã¢ï¼ãµã¼ãå´ï¼ãåå©ç¨", "ThirdSystemTrafficArea": "ããã«ã¯ãªãã¯ã§äº¤éã¨ãªã¢ï¼ã¯ã©ã¤ã¢ã³ãå´ï¼ãåå©ç¨"}, "configureAreaResourceApplicationAddress": "ã¨ãªã¢ãªã½ã¼ã¹ã®ãªã¯ã¨ã¹ãã¢ãã¬ã¹ãè¨­å®ãã>>", "chargeWhileWorking": "ä½æ¥­ä¸­ã«åé»ãã", "chargingPile": "åé»ã¹ã¿ã³ã", "smartCharge": "åé»ã®æ¥ç¶", "commonCharge": "ããã²ã¼ã·ã§ã³åé»", "on": "éã", "off": "éãã", "chargingMethod": "åçµæ¹å¼(å¾ç¿»è¯)", "dockingStrategy": "å¯¹æ¥ç­ç¥(å¾ç¿»è¯)"}, "mapList": {"releaseStatus": "å¬éç¶æ", "releaseStatusList": {"1": "å¬éæ¸ã¿", "0": "æ<PERSON><PERSON><PERSON><PERSON>"}, "resolution": "è§£ååº¦", "dimension": "ãµã¤ãº", "radioList": {"normalResolution": "éå¸¸è§£ååº¦(0.05)", "highResolution": "é«è§£ååº¦(0.03)"}, "copyMap": "å°å³ãã³ãã¼", "newMapCoding": "æ°ããå°å³ã³ã¼ã", "newMapName": "æ°ããå°å³å", "selectImportMode": "ã¤ã³ãã¼ãæ¹æ³ãé¸æ", "importAll": "ãã¹ã¦ãã¤ã³ãã¼ã", "leadInNetwork": "çµè·¯ããããã¤ã³ãã¼ã", "leadInLaser": "ã¬ã¼ã¶ã¼ãã¤ã³ãã¼ã", "importMap": "å°å³ãã¤ã³ãã¼ã", "importMapTip": "ã¤ã³ãã¼ãã«æåããã¨ãåãIDã®å°å³ãã¼ã¿ãä¸æ¸ãããã¾ã", "fileIsSuccessfullyImportedTip": "å°å³ãã¡ã¤ã«ãæ­£å¸¸ã«ã¤ã³ãã¼ãããã¾ãã"}, "robotManage": {"storageLocation": "ã¹ãã¼ã·ã§ã³ã¨ãªã¢", "vehicle": "è»ä¸¡", "status": "ç¶æ", "vehicleCode": "ã³ã¼ã", "controlMode": "å¶å¾¡ã¢ã¼ã", "controlModeList": {"Manual": "<PERSON><PERSON><PERSON>¶å¾¡", "Auto": "<PERSON><PERSON><PERSON><PERSON>¶å¾¡", "Repair": "ä¿®å¾©ã¢ã¼ã"}, "dispatch": "ã¹ã±ã¸ã¥ã¼ãªã³ã°", "scheduleMode": "ã¹ã±ã¸ã¥ã¼ãªã³ã°ã¢ã¼ã", "scheduleModeList": {"ManualSchedule": "æåã¹ã±ã¸ã¥ã¼ãªã³ã°", "AutoSchedule": "èªåã¹ã±ã¸ã¥ã¼ãªã³ã°"}, "connectStatus": "æ¥ç¶ç¶æ", "connectStatusList": {"Disconnect": "æªæ¥ç¶", "Connect": "æ¥ç¶å®äº"}, "softEmerStopStatus": "å®è¡ç¶æ", "softEmerStopStatusList": {"Close": "å®è¡ä¸­", "Open": "ä¸æåæ­¢"}, "storageState": {"FULL": "ãã", "EMPTY": "ç¡ã", "ERROR": "æ¤åºã¨ã©ã¼"}, "errorState": {"0": "æ­£å¸¸", "1": "ç°å¸¸"}, "softEmerStopStatusListBut": {"Close": "<PERSON><PERSON>", "Open": "ä¸æåæ­¢"}, "abnormalStatus": "ç°å¸¸ç¶æ", "abnormalStatusList": {"Abnormal": "ç°å¸¸", "Normal": "ç°å¸¸ãªã"}, "workbenchAbnormalStatusList": {"Abnormal": "ç°å¸¸", "Normal": "æ­£å¸¸"}, "locatedStatus": "ã­ã¼ã«ã©ã¤ã¼ã¼ã·ã§ã³ç¶æ", "locatedStatusList": {"NotLocated": "æªå®", "Located": "å®äº"}, "workStatus": "ä½æ¥­ç¶æ", "workStatusList": {"Offline": "ãªãã©ã¤ã³", "Work": "ä½æ¥­ä¸­", "Free": "å¾æ©ä¸­"}, "missionName": "ã¿ã¹ã¯", "missionWorkActions": "ã¢ã¯ã·ã§ã³", "rate": "é»é", "vehicleTypeName": "ã­ãããã¿ã¤ã", "vehicleGroupName": "ã­ãããã°ã«ã¼ã", "pilotVersion": "Pilotãã¼ã¸ã§ã³", "mosVersion": "Mosãã¼ã¸ã§ã³", "ip": "IPã¢ãã¬ã¹", "mac": "Macã¢ãã¬ã¹", "pause": "ä¸æåæ­¢", "restore": "<PERSON><PERSON>", "manual": "<PERSON><PERSON>", "semiAutomatic": "åèªå", "automatic": "èªå", "restart": "åèµ·å", "clear": "ã¯ãªã¢", "assignMap": "å°å³ãæå®", "stopTask": "ã¿ã¹ã¯åæ­¢", "viewLog": "ã­ã°ç¢ºèª", "batchOperation": "ä¸æ¬æä½", "setUp": "è¨­å®", "details": "è©³ç´°", "allocationProcess": "å²ãå½ã¦ãã­ã»ã¹", "softEmergencyStopBut": {"openSoftEmergencyStop": "ä¸æåæ­¢", "closeSoftEmergencyStop": "<PERSON><PERSON>"}, "enableCharging": "<PERSON><PERSON>»æå¹å", "enableParking": "é§è»æå¹å", "chargingMarker": "åé»ãã¤ã³ã", "parkingMarker": "é§è»ãã¤ã³ã", "chargingList": {"2": "ããã©ã«ã", "1": "<PERSON><PERSON><PERSON><PERSON>", "0": "ç¡å¹å"}, "relocation": "åã­ã¼ã«ã©ã¤ã¼ã¼ã·ã§ã³", "powerOff": "é»æºãªã", "importRobotGrouping": "ã­ãããã°ã«ã¼ããã¤ã³ãã¼ã", "statistics": "çµ±è¨", "offSite": "<PERSON><PERSON> <PERSON>", "historicalTask": "ã¿ã¹ã¯å±¥æ­´", "setType": "ã¿ã¤ãè¨­å®", "setGroup": "ã°ã«ã¼ãè¨­å®", "pleaseSelectARobotGroup": "ã­ãããã°ã«ã¼ããé¸æãã¦ãã ãã", "pleaseSelectARobotType": "ã­ãããã¿ã¤ããé¸æãã¦ãã ãã", "importedRobotType": "ã­ãããã¿ã¤ããã¤ã³ãã¼ã", "allowedRotation": "åè»¢è¨±å¯", "canRotateList": {"true": "ã¯ã", "false": "ããã"}, "currentLocationMap": "ç¾å¨ã®ã­ã±ã¼ã·ã§ã³å³", "switchMap": "å°å³ãåãæ¿ã", "carryTask": "ã¿ã¹ã¯å®è¡", "turnCharge": "åé»éå§", "openParking": "é§è»éå§", "autoChargeState": {"0": "èªååé»ç¦æ­¢", "1": "èªååé»æå¹å"}, "autoParkState": {"0": "èªåé§è»ç¦æ­¢", "1": "èªåé§è»æå¹å"}, "automaticRelocation": "èªåã­ã¼ã«ã©ã¤ã¼ã¼ã·ã§ã³", "manualRelocation": "æåã­ã¼ã«ã©ã¤ã¼ã¼ã·ã§ã³", "resetting": "ãªã»ãã", "ordinaryCharging": "æ<PERSON><PERSON><PERSON>»", "buttReset": "ããã­ã³ã°ãªã»ãã", "odom": "èµ°è¡è·é¢"}, "actionSetting": {"code": "ã³ã¼ã", "name": "åç§°", "type": "<PERSON><PERSON>", "enterParamCount": "å¥åãã©ã¡ã¼ã¿æ°", "outParamCount": "åºåãã©ã¡ã¼ã¿æ°", "add": "æ°è¦è¿½å ", "export": "ã¨ã¯ã¹ãã¼ã", "import": "ã¤ã³ãã¼ã", "preview": "ãã¬ãã¥ã¼", "notice": "ãã¼ãèª¬æ", "icon": "ã¢ã¤ã³ã³", "parameter": "ãã©ã¡ã¼ã¿", "addInputParameters": "å¥åãã©ã¡ã¼ã¿è¿½å ", "addOutputParameters": "åºåãã©ã¡ã¼ã¿è¿½å ", "parameterType": "ãã©ã¡ã¼ã¿ã¿ã¤ã", "parameterCode": "ãã©ã¡ã¼ã¿ã³ã¼ã", "parameterName": "ãã©ã¡ã¼ã¿å", "iconUploadComplete": "ã¢ã¤ã³ã³ã®ã¢ããã­ã¼ããå®äºãã¾ãã", "isCommonList": {"true": "ã¯ã", "false": "ããã"}, "isAllowSkipList": {"true": "ã¯ã", "false": "ããã"}, "baseTypeList": {"Text": "ãã­ã¹ã", "Number": "æ°å­", "Common": "å±é"}, "componentOptions": {"Default": "ããã©ã«ã", "Json": "JSON", "Bool": "ãã¼ã«", "RadioList": "åä¸é¸æãªã¹ã", "MultiList": "è¤æ°é¸æãªã¹ã", "VehicleMapCode": "åä¸é¸æããã", "VehicleMapCodeList": "è¤æ°é¸æããã", "MarkerCode": "åä¸é¸æãã¤ã³ã", "MarkerCodeList": "è¤æ°é¸æãã¤ã³ã", "VehicleCode": "åä¸é¸æã­ããã", "VehicleCodeList": "è¤æ°é¸æã­ããã", "VehicleTypeCode": "åä¸é¸æã­ãããã¿ã¤ã", "VehicleGroupCode": "åä¸é¸æã­ãããã°ã«ã¼ã", "WarehouseLocationType": "åä¸é¸æã¹ãã¼ã·ã§ã³ã¿ã¤ã", "WarehouseLocation": "åä¸é¸æã¹ãã¼ã·ã§ã³", "WarehouseArea": "åä¸é¸æã¹ãã¼ã·ã§ã³ã¨ãªã¢", "VehicleGroupCodeList": "è¤æ°é¸æã­ãããã°ã«ã¼ã", "VehicleTypeCodeList": "è¤æ°é¸æã­ãããã¿ã¤ã", "TaskType": "åä¸é¸æã¿ã¹ã¯ã¿ã¤ã", "customParameter": "ã«ã¹ã¿ã ãã©ã¡ã¼ã¿", "MultiText": "é·ããã­ã¹ã"}, "nodeSettingsImport": "ãã¼ãè¨­å®ã®ã¤ã³ãã¼ã", "fileFormatJson": "ãã¡ã¤ã«ããã©ãã°ãã¦ãã ãã", "inputBox": "å¥åããã¯ã¹", "skipAllow": "ã¹ã­ãããè¨±å¯ãã", "retryNum": "åè©¦è¡åæ°", "allowRetry": "åè©¦è¡ãè¨±å¯ãã"}, "taskManage": {"code": "ã³ã¼ã", "name": "åç§°", "status": "ç¶æ", "priority": "åªååº¦", "robot": "ã­ããã", "callbackUrl": "ä¸æµã®URL", "source": "ã½ã¼ã¹", "createDate": "ä½ææ¥æ", "startTime": "éå§æé", "endTime": "çµäºæé", "log": "ã­ã°", "taskExecution": "ã¿ã¹ã¯å®è¡", "currentNode": "ç¾å¨ã®ãã¼ã", "executionTime": "å®è¡æé", "runningLog": "å®è¡ã­ã°", "pleaseEnterADescriptionSearch": "èª¬æãå¥åãã¦æ¤ç´¢ãã¦ãã ãã", "statistics": "çµ±è¨", "runningTime": "å®è¡æé", "runTimes": "å®è¡åæ°", "totalHours": "ç·æé", "inputValue": "å¥åå¤", "outputValue": "åºåå¤", "taskInformation": "ã¿ã¹ã¯æå ±", "nodeInformation": "ãã¼ã<PERSON>å ±", "jsonFormatError": "JSONãã©ã¼ãããã¨ã©ã¼", "cancelATask": "ã¿ã¹ã¯ã­ã£ã³ã»ã«", "whetherToCancelATask": "ã¿ã¹ã¯ãã­ã£ã³ã»ã«ãã¾ããï¼", "downloadRecord": "è¨é²ããã¦ã³ã­ã¼ã", "optionsStatus": {"Create": "å¾ã¡", "Running": "å®è¡ä¸­", "Finished": "å®äº", "Cancel": "ã­ã£ã³ã»ã«"}, "optionsSource": {"Api": "ã¤ã³ã¿ã¼ãã§ã¼ã¹", "Manual": "æåçºè¡", "Charge": "åé»ããªã·ã¼", "Park": "é§è»ããªã·ã¼", "Traffic": "äº¤éããªã·ã¼", "Task": "ãã®ä»ã¿ã¹ã¯", "Pda": "PDA"}, "batchCancellation": "ä¸æ¬ã­ã£ã³ã»ã«", "uploadRecord": "ã¢ããã­ã¼ãè¨é²", "importTaskManagement": "ã¿ã¹ã¯ç®¡çã®ã¤ã³ãã¼ã", "uploadLog": "ã­ã°ã®ã¢ããã­ã¼ã", "theCompletedOrCanceledTaskIsSelected": "å®äºã¾ãã¯ã­ã£ã³ã»ã«ãããã¿ã¹ã¯ãé¸æããã¾ãã", "successfullyCancelledTask": "ã¿ã¹ã¯ã®ã­ã£ã³ã»ã«ãæåãã¾ãã", "failedToCancelTheTaskBecause": "ã¿ã¹ã¯ã®ã­ã£ã³ã»ã«ã«å¤±æãã¾ãããçç±ï¼", "failedToCancelTask": "ã¿ã¹ã¯ã®ã­ã£ã³ã»ã«ã«å¤±æãã¾ãã", "successfulOperation": "æ<PERSON>½<PERSON><PERSON>", "operationFailure": "æä½å¤±æ", "noPublishedTaskProcessExists": "æå¹åãããã¿ã¹ã¯ãã­ã»ã¹ãå­å¨ãã¾ãã", "skip": "ã¹ã­ãã", "remainingDistance": "æ®ãè·é¢", "totalDistance": "ç·è·é¢", "retry": "åè©¦è¡", "retryTip": "åè©¦è¡ãã¾ãã", "skipTip": "ãã®ãã¼ããã¹ã­ãããã¦æ¬¡ã®ãã¼ããç´æ¥å®è¡ãã¾ããï¼", "isBreak": "ä¸­æ­å¯è½", "isBreakObject": {"0": "ã¯ã", "1": "ããã"}}, "taskType": {"code": "ã³ã¼ã", "name": "åç§°", "priority": "åªååº¦", "selfCheckStatus": "èªå·±è¨ºæ­ç¶æ", "predictExecTime": "äºæ¸¬å®è¡æé", "layout": "ç·¨æ", "implement": "å®è¡", "clone": "ã¯ã­ã¼ã³", "eventType": "ã¤ãã³ãã¿ã¤ã", "eventTypeList": {"Interface": "ããã©ã«ã", "FixedTime": "å®æã¤ãã³ã", "Button": "ãã¿ã³ã¤ãã³ã", "Plc": "ã¬ã¸ã¹ã¿ã¤ãã³ã", "VehiclePlc": "ã­ãããã¬ã¸ã¹ã¿ã¤ãã³ã", "VehicleAbnormal": "ã­ãããç°å¸¸ã¤ãã³ã", "TaskCancel": "ã¿ã¹ã¯ã­ã£ã³ã»ã«ã¤ãã³ã", "TaskFinished": "ã¿ã¹ã¯å®äºã¤ãã³ã"}, "optionsTemplateType": {"Common": "å±éã¿ã¤ã", "Event": "ã¤ãã³ãã¿ã¤ã"}, "optionsPublishStatus": {"Published": "<PERSON><PERSON><PERSON><PERSON>", "Unpublished": "ç¡å¹å"}, "typeList": {"Common": "å±éã¿ã¤ããæ°è¦ä½æ", "Event": "ã¤ãã³ãã¿ã¤ããæ°è¦ä½æ"}, "jobFlowType": "ä½æ¥­ãã­ã¼ãã­ã»ã¹ã®ã¿ã¤ã", "releaseStatus": "æ<PERSON><PERSON><PERSON>¶æ", "importTaskType": "ã¿ã¹ã¯ã¿ã¤ãã®ã¤ã³ãã¼ã"}, "taskTypeArrangement": {"Business": "å±<PERSON>å¶å¾¡", "Common": "ä¸è¬", "Communication": "éä¿¡ã³ã³ãã¼ãã³ã", "Process": "ãã­ã»ã¹å¶å¾¡", "AllocationResource": "ãªã½ã¼ã¹å²å½", "ObtainResource": "ãªã½ã¼ã¹åå¾", "Other": "ãã®ä»", "Vehicle": "ã­<PERSON><PERSON><PERSON><PERSON>¶å¾¡", "commonNode": "å±éãã¼ã", "import": "å¥å", "export": "åºå", "otherCondition": "ãã®ä»æ¡ä»¶", "parallelBranch": "ä¸¦åãã©ã³ã", "cycleCondition": "ã«ã¼ãæ¡ä»¶", "endLoop": "ã«ã¼ãçµäº", "start": "éå§", "end": "çµäº", "settingsCommonlyUsed": "å±éè¨­å®", "parallel": "ä¸¦å", "parallelNode": "ä¸¦åãã¼ã", "condition": "æ¡ä»¶", "conditionalNode": "æ¡ä»¶ãã¼ã", "circulation": "ã«ã¼ã", "loopNode": "ã«ã¼ããã¼ã", "addCondition": "æ¡ä»¶è¿½å ", "settingCommonNodes": "å±éãã¼ãã®è¨­å®", "addParallel": "ä¸¦åè¿½å ", "displayName": "è¡¨ç¤ºå", "thePropertiesAreNotSavedPressEnterToConfirm": "ãã­ããã£ãä¿å­ããã¦ãã¾ããããEnterãã­ã¼ã§ç¢ºèªãã¦ãã ãã", "cycleTime": "ã«ã¼ãæé", "cycleNumber": "ã«ã¼ãåæ°", "constantValue": "å®æ°å¤", "setOfConditions": "æ¡ä»¶ã»ãã", "and": "ãã¤", "or": "ã¾ãã¯", "addConditionGroup": "æ¡ä»¶ã°ã«ã¼ãè¿½å ", "lt": "æªæº", "ne": "ç­ãããªã", "eq": "ç­ãã", "gt": "ããå¤§ãã", "ge": "ä»¥ä¸", "le": "ä»¥ä¸", "belong": "ã«å±ãã", "contain": "å«ã", "eventType": "ã¤ãã³ãã¿ã¤ã", "ip": "IPã¢ãã¬ã¹", "portNumber": "ãã¼ãçªå·", "functionCode": "æ©è½ã³ã¼ã", "registerAddress": "ã¬ã¸ã¹ã¿ã¢ãã¬ã¹", "registerValue": "ã¬ã¸ã¹ã¿å¤", "section": "ã»ã¯ã·ã§ã³", "effectiveScopeRobot": "ã­ããã", "effectiveScopeTask": "ã¿ã¹ã¯", "taskAttribute": "ã¿ã¹ã¯å±æ§", "conditionalAttribute": "æ¡ä»¶å±æ§", "nodeAttribute": "ãã¼ãå±æ§", "taskVariableInput": "ã¿ã¹ã¯å¤æ°ï¼å¥åï¼", "variableName": "å¤æ°å", "taskVariableOutput": "ã¿ã¹ã¯å¤æ°ï¼åºåï¼", "owningNode": "æå±ãã¼ã", "priorityState": {"5": "æé«", "4": "é«", "3": "ä¸­", "2": "ä½", "1": "æä½"}, "interfaceInputFormType": {"Json": "JSON", "MarkerCode": "ãã¤ã³ã", "VehicleCode": "ã­ããã", "Default": "ããã©ã«ã", "MultiText": "é·æ"}, "variableTypeList": {"Default": "ããã©ã«ã", "Bool": "ãã¼ã«", "RadioList": "åä¸é¸æãªã¹ã", "MultiList": "è¤æ°é¸æãªã¹ã", "VehicleMapCode": "åä¸é¸æããã", "VehicleMapCodeList": "è¤æ°é¸æããã", "MarkerCode": "åä¸é¸æãã¤ã³ã", "MarkerCodeList": "è¤æ°é¸æãã¤ã³ã", "VehicleCode": "åä¸é¸æã­ããã", "VehicleCodeList": "è¤æ°é¸æã­ããã", "VehicleTypeCode": "åä¸é¸æã­ãããã¿ã¤ã", "VehicleTypeCodeList": "è¤æ°é¸æã­ãããã¿ã¤ã", "VehicleGroupCode": "åä¸é¸æã­ãããã°ã«ã¼ã", "VehicleGroupCodeList": "è¤æ°é¸æã­ãããã°ã«ã¼ã", "WarehouseArea": "åä¸é¸æã¹ãã¼ã·ã§ã³ã¨ãªã¢", "WarehouseLocation": "åä¸é¸æã¹ãã¼ã·ã§ã³", "WarehouseLocationType": "åä¸é¸æã¹ãã¼ã·ã§ã³ã¨ãªã¢ã¿ã¤ã", "Json": "JSON", "Object": "ãªãã¸ã§ã¯ã"}, "variableCategoryList": {"Text": "ãã­ã¹ã", "Number": "æ°å­", "Common": "å±é"}, "effectiveScopeTaskState": {"1": "ãã¹ã¦", "2": "ä¸é¨"}, "variableNameDuplication": "å¤æ°åãéè¤ãã¦ãã¾ã", "settlementOfCondition": "æ¡ä»¶è¨­å®", "unpublish": "ç¡å¹å", "haveReleased": "<PERSON><PERSON><PERSON><PERSON>", "publishingFailedEmptyLoopExists": "ã¿ã¹ã¯æå¹åå¤±æï¼ç©ºã«ã¼ãã®ãã¼ããããã¾ã", "publishingFailedTaskMustContainOtherNodes": "ã¿ã¹ã¯æå¹åå¤±æï¼ã­ã£ã³ã»ã«ã¿ã¹ã¯ã«ã¯ä»ã®ãã¼ããå«ã¾ãã¦ããå¿è¦ãããã¾ã", "isParameterMandatoryTip": "ã¿ã¹ã¯æå¹åå¤±æï¼æªè¨­å®ã®ãã©ã¡ã¼ã¿ã¼ãå­å¨ãã¾ã", "eventTypeList": {"FixedTime": "å®æã¤ãã³ã", "Button": "ãã¿ã³ã¤ãã³ã", "Plc": "ã¬ã¸ã¹ã¿ã¤ãã³ã", "VehiclePlc": "AMRã¬ã¸ã¹ã¿ã¤ãã³ã", "VehicleAbnormal": "AMRç°å¸¸ã¤ãã³ã", "TaskCancel": "ã¿ã¹ã¯ã­ã£ã³ã»ã«ã¤ãã³ã", "TaskFinished": "ã¿ã¹ã¯å®äºã¤ãã³ã", "Interface": "ããã©ã«ã"}, "effectiveDate": "æå¹æ¥", "pleaseEnterTheEffectiveStartDate": "æå¹éå§æ¥ãå¥åãã¦ãã ãã", "pleaseEnterTheEffectiveEndDate": "æå¹çµäºæ¥ãå¥åãã¦ãã ãã", "effectiveTime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseEnterTheEffectiveStartTime": "æå¹éå§æéãå¥åãã¦ãã ãã", "pleaseEnterTheEffectiveEndTime": "æå¹çµäºæéãå¥åãã¦ãã ãã", "interval": "<PERSON><PERSON>", "enableOrNot": "æå¹åããã", "callBoxNumber": "ã³ã¼ã«ããã¯ã¹çªå·", "buttonNumber": "ãã¿ã³çªå·", "robotNumber": "ã­ãããçªå·", "robotLocationMap": "ã­ããããä½ç½®ããå°å³", "exceptionCoding": "ç°å¸¸ã³ã¼ã", "anomalyLevel": "ç°å¸¸ã¬ãã«", "exceptionDetails": "ç°å¸¸è©³ç´°", "robotAssembly": "ã­ãããéå", "taskPublishingSucceeded": "ã¿ã¹ã¯æå¹åæå", "searchNode": "ãã¼ãæ¤ç´¢", "quickAccess": "ã·ã§ã¼ãã«ãã", "quickAccessTip": "ãããæå¹åããã¨ãã¢ãã¿ãªã³ã°ã»ã³ã¿ã¼ã®AMRã«ã¼ãã§ã¿ã¹ã¯ãä½æã§ãã¾ã", "inputParameter": "å¥åãã©ã¡ã¼ã¿ã¼", "outputParameter": "åºåãã©ã¡ã¼ã¿ã¼", "missionNumber": "ã¿ã¹ã¯çªå·", "customOutput": "ã«ã¹ã¿ã åºå", "customInput": "ã«ã¹ã¿ã å¥å", "pda": "PDA", "pdaTip": "ãããæå¹åããã¨ãPDAã§ã¿ã¹ã¯ãä½æã§ãã¾ã"}, "notice": {"levelList": {"1": "INFO", "2": "WARING", "3": "ERROR"}, "statusList": {"0": "ã¢ã¯ãã£ã", "1": "ç¡è¦", "2": "Close"}, "ignore": "ç¡è¦", "level": "ã¬ãã«", "source": "ã½ã¼ã¹", "quest": "ã¿ã¹ã¯", "equipment": "è¨­å", "closingTime": "çµäºæé", "intervalTime": "éé<PERSON><PERSON>", "importTheNotificationProfile": "ã¡ãã»ã¼ã¸è¨­å®ãã¡ã¤ã«ã®ã¤ã³ãã¼ã", "isUpload": "ã¢ããã­ã¼ãããã", "isUploadState": {"0": "ããã", "1": "ã¯ã"}}, "exception": {"ignore": "ç¡è¦", "cancelIgnore": "ç¡è¦ã®ã­ã£ã³ã»ã«", "exceptionLevel": "ç°å¸¸ã¬ãã«", "sourceSystem": "ã½ã¼ã¹ã·ã¹ãã ", "exceptionType": "ç°å¸¸ã¿ã¤ã", "solution": "è§£æ±ºç­", "exceptionStatus": "ç°å¸¸ç¶æ", "ignoreStatus": "ç¡è¦ç¶æ", "robot": "ã­ããã", "taskId": "ã¿ã¹ã¯ID", "deviceId": "ããã¤ã¹ID", "mapName": "å°å³å", "info": "éå¸¸", "warning": "è­¦å", "error": "ç°å¸¸", "unread": "æªèª­", "readAll": "ãã¹ã¦æ¢èª­", "read": "æ¢èª­", "closeTime": "çµäºæé", "exceptionStatusList": {"0": "éãã¦ããªã", "1": "éãã¾ãã"}, "ignoreStatusList": {"0": "æªç¡è¦", "1": "ç¡è¦æ¸ã¿"}, "exceptionMessage": "ç°å¸¸ã¡ãã»ã¼ã¸", "exceptionMessageDetails": "ç°å¸¸ã¡ãã»ã¼ã¸ã®è©³ç´°", "source": "ã½ã¼ã¹"}, "chargeConfig": {"dialogInputList": {"lowBattery": "ä½é»éãè¨­å®", "highBattery": "é«é»éãè¨­å®", "minBatteryValue": "æå°åé»éãè¨­å®", "minChargeTime": "æå°åé»æéãè¨­å®", "bindChargeMarkers": "åé»ãã¤ã³ããè¨­å®", "chargeTaskTypeId": "ã¿ã¹ã¯ãè¨­å®"}, "lowBattery": "ä½é»éï¼%ï¼", "highBattery": "é«é»éï¼%ï¼", "minBatteryValue": "æå°åé»éï¼%ï¼", "minChargeTime": "æå°åé»æéï¼åï¼", "createTask": "ã¿ã¹ã¯ãä½æ", "title": "åé»ããªã·ã¼", "describe": "AMRãä½é»éãã¤ã¢ã¤ãã«ç¶æã®ã¨ãã«åé»ã¿ã¹ã¯ãä½æããããªã·ã¼"}, "parkConfig": {"dialogInputList": {"bindParkMarkers": "é§è»ãã¤ã³ãããè¨­å®", "parkTaskTypeId": "ã¿ã¹ã¯ãè¨­å®"}, "createTask": "ã¿ã¹ã¯ãä½æ", "title": "é§è»ããªã·ã¼", "describe": "AMRãã¢ã¤ãã«ç¶æã®ã¨ãã«é§è»ã¿ã¹ã¯ãä½æããããªã·ã¼"}, "trafficConfig": {"title": "äº¤éè¨­å®", "describe": "AMRã®åé¿ãçµè·¯ãªã¯ã¨ã¹ããè¡çªå¦ç", "faultOptions": {"1": "å¾æ©", "2": "<PERSON><PERSON>"}, "banOptions": {"1": "å¾æ©", "2": "<PERSON><PERSON>", "3": "æé¤"}, "collisionOptions": {"1": "ããã²ã¼ã·ã§ã³ãã¤ã³ã", "2": "åé»ãã¤ã³ã", "3": "ä½æ¥­ãã¤ã³ã"}, "highPerformanceMode": "é«æ§è½ã¢ã¼ãã§ã¯ãã¨ãªã¢ãAMRã®åè»¢ç¦æ­¢ãªã©ã®æ©è½ã«å½±é¿ãããã¾ã"}, "errorStatistical": {"vehicleAbnormalPieChart": "AMRç°å¸¸ã®å²å", "abnormalDetailPieChart": "ç°å¸¸åé¡ã®å²å", "avgHandleDurationPieChart": "ç°å¸¸ã®å¹³åå¦çæé", "newCountLineChart": "æ°è¦ç°å¸¸ä»¶æ°", "avgHandleDurationLineChart": "ç°å¸¸ã®å¹³åå¦çæé"}, "taskStatistical": {"taskStatusPieChart": "ã¿ã¹ã¯å®äºã®å²å", "createTaskCountPieChart": "æ°è¦ã¿ã¹ã¯ä»¶æ°", "avgAllocationDurationPieChart": "å¹³åå²ãå½ã¦æé", "avgExecuteDurationPieChart": "å¹³åä½æ¥­æé", "createTaskCountLineChart": "æ°è¦ã¿ã¹ã¯ä»¶æ°", "endTaskCountLineChart": "çµäºã¿ã¹ã¯ä»¶æ°", "avgAllocationDurationLineChart": "å¹³åå²ãå½ã¦æé", "avgExecuteDurationDurationLineChart": "å¹³åä½æ¥­æé"}, "screen": {"agvNumStatistical": "AMRå°æ°çµ±è¨", "taskNumStatistical": "ã¿ã¹ã¯ä»¶æ°çµ±è¨", "agvTotal": "AMRç·å°æ°", "totalSize": "ã¿ã¹ã¯ç·ä»¶æ°", "runningSize": "å®è¡ä¸­ã®ã¿ã¹ã¯ä»¶æ°", "successSize": "ã¿ã¹ã¯æåä»¶æ°", "cancelSize": "ã¿ã¹ã¯å¾æ©ä»¶æ°", "waitSize": "ã¿ã¹ã¯ã­ã£ã³ã»ã«ä»¶æ°", "completionRate": "ã¿ã¹ã¯éæç", "title": "ã¹ã±ã¸ã¥ã¼ãªã³ã°ã·ã¹ãã ã®ã¹ã¯ãªã¼ã³", "visualLargeScreen": "ã¹ã¯ãªã¼ã³"}, "serverMonitoring": {"serverParameter": "ãµã¼ãã¼ä»æ§", "serverUsage": "ãµã¼ãã¼ãªã½ã¼ã¹ã®ä½¿ç¨ç¶æ³", "cpuLineChart": "CPUä½¿ç¨ç", "memLineChart": "ã¡ã¢ãªä½¿ç¨ç", "diskLineChart": "ãã£ã¹ã¯å®¹éã®å¤å", "mysqlLineChart": "MySQLæä½åæ°", "cpuCores": "CPUã³ã¢æ°", "cpuCoresUnit": "å", "totalThreads": "ã¹ã¬ããç·æ°", "thread": "ã¹ã¬ãã", "memory": "ã¡ã¢ãª", "diskCapacity": "ãã£ã¹ã¯å®¹é", "diskUsage": "ãã£ã¹ã¯ä½¿ç¨ç"}, "statisticsRobots": {"statusLineChart": "AMRç¶æã®ãã¬ã³ã", "statusPieChart": "AMRç¶æã®å²å", "utilizeRateLineChart": "AMRç¨¼åç", "text": "AMRçµ±è¨"}, "taskTypeStatistic": {"taskStatusPieChart": "ã¿ã¹ã¯å®äºã®å²å", "taskCountLineChart": "ã¿ã¹ã¯ä»¶æ°", "avgAllocationDurationLineChart": "å¹³åå²ãå½ã¦æé", "avgExecuteDurationLineChart": "å¹³åä½æ¥­æé", "text": "ã¿ã¹ã¯ãã­ã¼çµ±è¨"}, "robotManagementStatistic": {"statusPieChart": "AMRç¶æã®å²å", "workStatusPieChart": "AMRä½æ¥­å²å", "statusLineChart": "AMRç¶æã®ãã¬ã³ã", "utilizeRateLineChart": "AMRç¨¼åç"}, "robotMonitorStatistic": {"taskStatusPieChart": "ã¿ã¹ã¯ç¶æ", "vehicleStatusPieChart": "AMRç¶æ", "vehicleBatteryPieChart": "ããããªã¼æ®é"}, "licence": {"licenseRemaining": "ã©ã¤ã»ã³ã¹ã®æå¹æé", "licenseNotInForce": "ã©ã¤ã»ã³ã¹ãæªæå¹", "licenseHasExpired": "ã©ã¤ã»ã³ã¹ãæéåã", "lackOfLicense": "ã©ã¤ã»ã³ã¹ãä¸è¶³ãã¦ãã¾ã", "renewalOfLicense": "ã©ã¤ã»ã³ã¹ã®æ´æ°", "deleteLicense": "ã©ã¤ã»ã³ã¹ã®åé¤", "renewalAuthorization": "èªè¨¼ã®æ´æ°"}, "logins": {"userLogin": "ã­ã°ã¤ã³", "rememberThePassword": "ãã¹ã¯ã¼ããè¨æ¶ãã", "copyrightShenzhenYouaiZhiheCoLtd": "èä½æ¨© Youibot Robotæ ªå¼ä¼ç¤¾"}, "log": {"causeOfFailure": "å¤±æåå ", "responseTime": "å¿ç­æé", "url": "URL", "requestInformation": "ãªã¯ã¨ã¹ãæå ±", "returnInformation": "ãªã¿ã¼ã³", "successList": {"true": "<PERSON><PERSON>", "false": "å¤±æ"}, "user": "ã¦ã¼ã¶ã¼", "ipAddressOfTheClient": "ã¯ã©ã¤ã¢ã³ãIPã¢ãã¬ã¹", "operatingTime": "æ<PERSON><PERSON><PERSON><PERSON>", "typeList": {"Error": "ERROR", "Running": "INFO", "Warning": "WARNING"}, "category": "ã«ãã´ãª", "data": "ãã¼ã¿", "lastTime": "æçµæ´æ°æé", "downloadDetailsLog": "è©³ç´°ã­ã°ããã¦ã³ã­ã¼ã", "filename": "ãã¡ã¤ã«å", "message": "ã¡ãã»ã¼ã¸"}, "systemConfiguration": {"licenseAllocation": "ã©ã¤ã»ã³ã¹è¨­å®", "licenseAllocationDescribe": "ã©ã¤ã»ã³ã¹æå ±", "storageConfiguration": "ã¹ãã¬ã¼ã¸è¨­å®", "storageConfigurationDescribe": "ã­ã°ãã¼ã¿åé¤ããã¡ã¤ã«ãã¼ã¿åé¤ãæ¥­åãã¼ã¿åé¤", "pushInterfaceConfiguration": "ããã·ã¥ã¤ã³ã¿ã¼ãã§ã¼ã¹è¨­å®", "robotState": "ã­ãããç¶æ"}, "storageLocation": {"reservoirArea": "ã¹ãã¼ã·ã§ã³ã¨ãªã¢", "row": "è¡", "column": "å", "layer": "å±¤", "operatingHeight": "ä½æ¥­é«ã", "jobPoint": "ä½æ¥­ãã¤ã³ã", "occupiedState": "ç¶æ", "containerBarCode": "ã³ã³ãããã¼ã³ã¼ã", "storyHeight": "å±¤ã®é«ã", "occupyStatus": {"Lock": "ã­ãã¯ä¸­", "Free": "ç©ºã", "Store": "å¨åº«ä¸­"}, "setPoint": "ãã¤ã³ãè¨­å®", "importDatabaseLocationArea": "ã¹ãã¼ã·ã§ã³ã¨ãªã¢ãã¤ã³ãã¼ã", "importLibraryType": "ã¹ãã¼ã·ã§ã³ã¿ã¤ããã¤ã³ãã¼ã", "importLocation": "ã¹ãã¼ã·ã§ã³ãã¤ã³ãã¼ã", "usageStatus": {"Disable": "ç¡å¹å", "Enable": "<PERSON><PERSON><PERSON><PERSON>"}, "enabledState": "æ<PERSON><PERSON><PERSON>¶æ"}, "today": "ä»æ¥", "yesterday": "æ¨æ¥", "thisWeek": "ä»é±", "lastWeek": "<PERSON><PERSON>", "thisMonth": "ä»æ", "lastMonth": "<PERSON><PERSON>", "last7Days": "éå»7æ¥é", "last30Days": "éå»30æ¥é", "fullTimeOut": "å¨ä½ä¸æåæ­¢ä¸­ãæ°ããã¿ã¹ã¯ãåãä»ãã¾ããã", "onPause": "å¨ä½ä¸æåæ­¢ä¸­ãæªåæ­¢ã­ãããæ°:", "fullRecoveryUnderway": "å¨ã¦åå¾©ä¸­", "completeTimeout": "å¨ã¦ä¸æåæ­¢æå", "fullRecoverySuccessful": "å¨ã¦åå¾©æå", "OperationIsTooFrequent": "æä½ãé »ç¹ããã¾ã", "save": "ä¿å­", "extendedAttribute1": "æ¡å¼µãã­ããã£1", "extendedAttribute2": "æ¡å¼µãã­ããã£2", "extendedAttribute3": "æ¡å¼µãã­ããã£3", "extendedAttribute4": "æ¡å¼µãã­ããã£4", "extendedAttribute5": "æ¡å¼µãã­ããã£5", "extendedAttribute6": "æ¡å¼µãã­ããã£6", "extendedAttribute7": "æ¡å¼µãã­ããã£7", "extendedAttribute8": "æ¡å¼µãã­ããã£8", "extendedAttribute9": "æ¡å¼µãã­ããã£9", "extendedAttribute10": "æ¡å¼µãã­ããã£10", "containerEncoding": "ã³ã³ããã³ã¼ã", "currentReservoirArea": "ç¾å¨ã®ã¹ãã¼ã·ã§ã³ã¨ãªã¢", "locationType": "ã¹ãã¼ã·ã§ã³ã¿ã¤ã", "warehouse": "ã¹ãã¼ã·ã§ã³", "languageConfiguration": "è¨èªè¨­å®", "languageConfigurationDescribe": "è¨èªããã¯ãè¿½å ã»ãã¦ã³ã­ã¼ã", "storageUpdateTime": "å¨åº«æ´æ°æé", "numberTriggers": "ããªã¬ã¼åæ°", "allowRepetition": "éè¤å¯è½", "exceptionMessage": "ç°å¸¸æå ±", "incomingParameter": "å¥åãã©ã¡ã¼ã¿", "isAllowRepeatState": {"0": "è¨±å¯ããªã", "1": "è¨±å¯ãã"}, "importEvent": "ã¤ã³ãã¼ãã¤ãã³ã", "eventCoding": "ã¤ãã³ã", "beChecking": "æ¤è¨¼ä¸­...", "delLanguageTip": "ãã®è¨èªããã¯ãåé¤ãã¦ãããããã§ããï¼?", "endCancelRange": "çµäºåæ¶ç¯å²", "checkValue": "æ¤è¨¼å¤", "changingName": "åå½å", "taskArrangementNew": {"putAway": "åç´", "unfold": "å±é", "start": "éå§", "end": "çµäº", "while": "ã«ã¼ãæ¡ä»¶", "cancelTask": "ã¿ã¹ã¯ã­ã£ã³ã»ã«", "endWhile": "ã«ã¼ãçµäº", "endCancelTask": "ã­ã£ã³ã»ã«ç¯å²çµäº", "judge": "æ¡ä»¶", "when": "ä¸¦è¡", "otherJudge": "ä»ã®æ¡ä»¶", "cancelWhileTip1": "ã¿ã¹ã¯ã­ã£ã³ã»ã«åã«ã¿ã¹ã¯ã­ã£ã³ã»ã«ããã¹ãã§ãã¾ãã", "cancelWhileTip2": "ç¾å¨ã¯2æ®µéã®ãã¹ãã«ã¼ãã®ã¿ãµãã¼ã"}, "globalPauseExecutingArmScriptIsStop": {"Immediately": "ããã«åæ­¢", "Later": "çµãã£ããæ­¢ã¾ã£ã¦ãã ãã"}, "chargingPile": {"serialNumber": "ã·ãªã¢ã«çªå·", "deviceType": "ããã¤ã¹ã®ç¨®é¡", "equipmentType": "ããã¤ã¹ã®åçª", "networkState": "ãããã¯ã¼ã¯ç¶æ", "runningState": "éè¡ç¶æ", "controlMode": "ã³ã³ãã­ã¼ã«ã¢ã¼ã", "dischargeStatus": "æ¾é»ç¶æ", "resetStatus": "ãªã»ããç¶æ", "occupancyRobot": "å ç¨ã­ããã", "chargeTypes": "åé»ã¿ã¤ã", "setVoltage": "åé»ã­ã£ãªãã¬ã¼ã·ã§ã³é»å§", "voltage": "ç¾å¨ã®é»å§", "setCurrent": "åé»åºæºé»æµ", "current": "ç¾å¨ã®é»æµ", "setPower": "åé»ã­ã£ãªãã¬ã¼ã·ã§ã³é»å", "power": "ãã¤ãããã§ãããã", "dcTemp": "DCã¢ã¸ã¥ã¼ã«æ¸©åº¦", "brushTemp": "ãã©ã·ãããã®æ¸©åº¦", "airTemp": "ç©ºæ°æ¸©åº¦", "durationTimes": "å¥ç¤¾æé", "voltageRange": "æ¯æé»å§ç¯å²", "softwareVer": "ãã¡ã¼ã ã¦ã§ã¢ãã¼ã¸ã§ã³", "terminationDischarge": "å¼ºå¶åæ­¢(å¾ç¿»è¯)", "networkStatusState": {"online": "ãªã³ã©ã¤ã³", "offline": "ãªãã©ã¤ã³"}, "workStatusState": {"normal": "æ­£å¸¸", "abnormal": "ç°å¸¸"}, "dischargeStatusList": {"discharging": "æ¾é»ä¸­", "no_discharge": "æªæ¾é»"}, "resetStatusList": {"pending": "å¾©ä½ãå¾ã£ã¦ãã¾ã", "completed": "ãªã»ããããã¾ãã"}, "controlModeList": {"auto": "èªå", "manual": "<PERSON><PERSON>"}, "whetherToPerformReset": "ãªã»ãããå®è¡ãã¾ããï¼", "WhetherPerformTerminationDischarge": "ç¡®è®¤æ§è¡å¼ºå¶åæ­¢ï¼(å¾ç¿»è¯)", "resetBatchPrompt": "{successLength}åã®åé»ã¹ã¿ã³ãã®æä½ã«æåãã{errorLength}åã®åé»ã¹ã¿ã³ãã®æä½ã«å¤±æãã¾ãã", "chargingPoint": "åçµç¹(å¾ç¿»è¯)"}, "secondaryConfirmationPrompt": "ãã®æä½ãå®è¡ãã¦ãããããã§ããï¼", "followingSystem": "ã·ã¹ãã ã«å¾ã", "customization": "ã«ã¹ã¿ãã¤ãº", "exceptionNotifyTimeTip": "äºä»¶ãçºçãã¦ããå°ãªãã¨ã5åå¾ã«ãããé«ãã¬ãã«ã®éç¥ãçºä¿¡ã§ãã¾ãï¼", "alwaysConnect": "å§ç»å¯¹æ¥(å¾ç¿»è¯)", "doNotConnectUntilTheFinishLine": "éç»ç¹ä¸å¯¹æ¥(å¾ç¿»è¯)", "abnormalAlarm": "å¼å¸¸æ¥è­¦(å¾ç¿»è¯)", "menuList": {"menu": {"monitoringCenter": "ã¢ãã¿ãªã³ã°ã»ã³ã¿ã¼", "PDA": "PDA", "agv": "ã­ããã", "task": "ã¿ã¹ã¯", "equipment": "ããã¤ã¹", "operations": "ç®¡çã»ã³ã¿ã¼", "taskManager": "ã¿ã¹ã¯ç®¡ç", "taskList": "ã¿ã¹ã¯å®è¡ãªã¹ã", "taskType": "ã¿ã¹ã¯ãªã¹ã", "eventList": "ã¤ãã³ããªã¹ã", "robots": "ã­ãããç®¡ç", "robotList": "ã­ããããªã¹ã", "robotType": "ã­ãããã¿ã¤ã", "robotGroup": "ã­ãããã°ã«ã¼ã", "mapList": "å°å³ç®¡ç", "storageLocation": "ã¹ãã¼ã·ã§ã³ç®¡ç", "storageLocationList": "ã¹ãã¼ã·ã§ã³ãªã¹ã", "storageLocationType": "ã¹ãã¼ã·ã§ã³ã¿ã¤ã", "storageLocationArea": "ã¹ãã¼ã·ã§ã³ã¨ãªã¢ãªã¹ã", "notificationManager": "ã¡ãã»ã¼ã¸ç®¡ç", "systemLog": "ã·ã¹ãã ã­ã°", "operationLog": "æä½ã­ã°", "interfaceLog": "ã¤ã³ã¿ã¼ãã§ã¼ã¹ã­ã°", "runningLog": "å®è¡ã­ã°", "setting": "ã·ã¹ãã è¨­å®", "schedulingConfiguration": "ã¹ã±ã¸ã¥ã¼ãªã³ã°è¨­å®", "systemSettings": "ã·ã¹ãã è¨­å®", "userSettings": "ã¢ã«ã¦ã³ãæ¨©é", "notificationSettings": "ã¡ãã»ã¼ã¸ãã³ãã¬ã¼ã", "nodeSettings": "ãã¼ãè¨­å®", "statistical": "çµ±è¨ã¬ãã¼ã", "statisticsRobots": "ã­ãããçµ±è¨", "taskStatistical": "ã¿ã¹ã¯çµ±è¨", "errorStatistical": "ç°å¸¸çµ±è¨", "serverMonitoring": "ãµã¼ãã¼ã¢ãã¿ãªã³ã°", "facility": "è¨­åç®¡ç", "chargingPile": "åé»ã¹ã¿ã³ãç®¡ç"}, "button": {"view": "è¡¨ç¤º", "relocation": "åã­ã¼ã«ã©ã¤ã¼ã¼ã·ã§ã³", "switchMap": "å°å³åãæ¿ã", "controlMode": "å¶å¾¡ã¢ã¼ã", "dispatchingMode": "ã¹ã±ã¸ã¥ã¼ãªã³ã°ã¢ã¼ã", "ordinaryCharging": "æ<PERSON><PERSON><PERSON>»", "autocharge": "èªåå<PERSON>»", "automaticParking": "èªåé§è»", "pause_resume": "ä¸æåæ­¢/åé", "reset": "ãªã»ãã", "buttReset": "ããã­ã³ã°ãªã»ãã", "restart": "åèµ·å", "shutdown": "ã·ã£ãããã¦ã³", "departure": "<PERSON><PERSON> <PERSON>", "newTask": "æ°ããã¿ã¹ã¯", "cancelTask": "ã¿ã¹ã¯ã­ã£ã³ã»ã«", "taskDetails": "ã¿ã¹ã¯è©³ç´°", "execution": "ã¿ã¹ã¯å®è¡", "elevato": "ã¨ã¬ãã¼ã¿ã¼", "autoDoor": "èªåãã¢", "airShower": "ã¨ã¢ã·ã£ã¯ã¼ãã¢", "bulkExport": "ä¸æ¬ã¨ã¯ã¹ãã¼ã", "batchImport": "ä¸æ¬ã¤ã³ãã¼ã", "cancle": "ã­ã£ã³ã»ã«", "batchCancellation": "ä¸æ¬ã­ã£ã³ã»ã«", "uploadRecord": "è¨é²ã¢ããã­ã¼ã", "details": "è©³ç´°", "downloadRecord": "è¨é²ãã¦ã³ã­ã¼ã", "remark": "<PERSON><PERSON>", "add": "è¿½å ", "del": "åé¤", "edit": "ç·¨é", "implement": "å®è¡", "layout": "ã¬ã¤ã¢ã¦ã", "clone": "ã¯ã­ã¼ã³", "statistic": "çµ±è¨", "enabledState": "æå¹/ç¡å¹", "assignMap": "å°å³æå®", "SoftEmergencyStop": "ä¸æåæ­¢/è§£é¤", "schedule": "èªåã¹ã±ã¸ã¥ã¼ã«/æåã¹ã±ã¸ã¥ã¼ã«", "controlModeState": "<PERSON><PERSON><PERSON>¶å¾¡/èª<PERSON><PERSON>¶å¾¡", "historicalTask": "å±¥æ­´ã¿ã¹ã¯", "setType": "ã¿ã¤ãè¨­å®", "setGroup": "ã°ã«ã¼ãè¨­å®", "export": "ã¨ã¯ã¹ãã¼ã", "import": "ã¤ã³ãã¼ã", "ignore": "ç¡è¦", "activation": "ã¢ã¯ãã£ãã¼ã·ã§ã³", "downloadDetailedLog": "è©³ç´°ã­ã°ã®ãã¦ã³ã­ã¼ã", "download": "ãã¦ã³ã­ã¼ã", "viewRoles": "ã­ã¼ã«è¡¨ç¤º", "addRoles": "ã­ã¼ã«è¿½å ", "rename": "ååå¤æ´", "delRoles": "ã­ã¼ã«åé¤", "viewAccount": "ã¢ã«ã¦ã³ãè¡¨ç¤º", "addAccount": "ã¢ã«ã¦ã³ãè¿½å ", "editAccount": "ã¢ã«ã¦ã³ãç·¨é", "delAccount": "ã¢ã«ã¦ã³ãåé¤", "resetPasswords": "ãã¹ã¯ã¼ããªã»ãã", "containerExit": "ã³ã³ããéå ´", "containerEntry": "ã³ã³ããå¥å ´", "taskView": "ã¿ã¹ã¯è¡¨ç¤º", "taskCancel": "ã¿ã¹ã¯ã­ã£ã³ã»ã«", "changingName": "ååå¤æ´", "terminationDischarge": "å¼ºå¶åæ­¢(å¾ç¿»è¯)", "stopAlarm": "åæ­¢æ¥è­¦(å¾ç¿»è¯)"}}}, "yi": {"switchLanguage": "è¨èªåãæ¿ã", "map": "å°å³", "size": "ãµã¤ãº", "deleteModel": "è¦ç´ åé¤", "test": "ãã¹ã", "options": "æä½", "addMarker": "ãã¤ã³ãæ°è¦", "addArea": "ã¨ãªã¢æ°è¦", "canvasReset": "å°å³ç»é¢ãªã»ãã", "canvasRotate": "å°å³ç»é¢åè»¢", "canvasFullScreen": "å°å³å¨ç»é¢è¡¨ç¤º", "displaySetting": "è¡¨ç¤ºè¨­å®", "addOneWayPath": "åæ¹åçµè·¯ä½æ", "addTwoWayPath": "åæ¹åçµè·¯ä½æ", "straightenCarve": "çµè·¯æ´å½¢", "canvasSmallScreen": "çµè·¯æ´å½¢", "addPath": "çµè·¯æ°è¦", "systemSetup": "å°å³è¡¨ç¤ºè¨­å®", "endRecordingPoint": "ãã¤ã³ãè¨é²çµäº", "recordingPoint": "ãã¤ã³ãè¨é²", "mapEditor": "å°å³ç·¨é", "batchNew": "ä¸æ¬æ°è¦", "rangingPath": "è·é¢æ¸¬å®", "selectedPath": "é¸æçµè·¯", "up": "ä¸ã¸", "down": "ä¸ã¸", "aleft": "å·¦ã¸", "right": "å³ã¸", "hideSelectedPath": "çµè·¯éè¡¨ç¤º", "smoothPath": "çµè·¯æ»ãã"}, "pdaLang": {"login": "ã­ã°ã¤ã³", "account": "ã¢ã«ã¦ã³ã", "password": "ãã¹ã¯ã¼ã", "changingServerIPAddress": "ãµã¼ãã¼IPå¤æ´", "serverIPAddressTip": "ãµã¼ãã¼ã¢ãã¬ã¹ãæéã«å¤æ´ãã¦ãã ããï¼!", "serverAddress": "ãµã¼ãã¼ã¢ãã¬ã¹", "version": "ãã¼ã¸ã§ã³æå ±", "messageExceptionAlarm": "ã¡ãã»ã¼ã¸ç°å¸¸ã¢ã©ã¼ã ", "logout": "ã­ã°ã¢ã¦ã", "logoutOrNot": "ã­ã°ã¢ã¦ããã¾ãã?", "pleaseEnterAGVNumber": "AMRçªå·ãå¥åãã¦ãã ãã", "refreshSuccessful": "æ´æ°æå", "message": "ã¡ãã»ã¼ã¸", "ignore": "ç¡è¦", "code": "ã³ã¼ã", "describe": "èª¬æ", "robot": "ã­ããã", "taskID": "ã¿ã¹ã¯ID", "operate": "æä½", "operateSuccessfully": "æ<PERSON>½<PERSON><PERSON>", "task": "ã¿ã¹ã¯", "Abnormal": "ç°å¸¸", "Normal": "æ­£å¸¸", "cancel": "ã­ã£ã³ã»ã«", "taskdetail": "ã¿ã¹ã¯è©³ç´°", "taskEntry": "ã¿ã¹ã¯ãã©ã¡ã¼ã¿", "state": "ç¶æ", "name": "åå", "createDate": "ä½ææ¥æ", "taskDelivery": "ã¿ã¹ã¯éä¿¡", "storageLocation": "ã¹ãã¼ã·ã§ã³", "storageLocationTip": "ã¹ãã¼ã·ã§ã³ãå¥åã¾ãã¯ã¹ã­ã£ã³ãã¦ãã ãã", "materialType": "ææã¿ã¤ã", "materialTypeTip": "ææã¿ã¤ããé¸æãã¦ãã ãã", "containerCode": "ã³ã³ãããã¼ã³ã¼ã", "containerCodeTip": "ãã¼ã³ã¼ããå¥åã¾ãã¯ã¹ã­ã£ã³ãã¦ãã ãã", "OK": "ç¢ºèª", "containerEntry": "ã³ã³ããå¥å ´", "containerExit": "ã³ã³ããéå ´", "scnContainerRepositoryLocation": "ã³ã³ããã¾ãã¯ã­ã±ã¼ã·ã§ã³ãã¹ã­ã£ã³ãã¦ãã ãã", "userInformationHasExpired": "ã¦ã¼ã¶ã¼æå ±ã®æå¹æéåã", "networkPrompt": "æ¥ç¶å¤±æããµã¼ãã¹è¨­å®ãç¢ºèªãã¦ãã ãã", "networkPrompt2": "ããã¯ã¨ã³ããµã¼ãã¹ã«æ¥ç¶ã§ãã¾ãã", "Disconnect": "æªæ¥ç¶", "Connect": "æ¥ç¶ä¸­", "map": "å°å³", "type": "ã¿ã¤ã", "scheduleMode": "ã¹ã±ã¸ã¥ã¼ãªã³ã°", "controlStatus": "å¶å¾¡", "orientation": "ä½ç½®è¨­å®", "softStopSwitch": "ç¨¼å", "currentNode": "ç¾å¨ã®ãã¼ã", "executionTime": "å®è¡æé", "Offline": "ãªãã©ã¤ã³", "Work": "ä½æ¥­", "Free": "ç©ºã", "ManualSchedule": "æåã¹ã±ã¸ã¥ã¼ã«", "AutoSchedule": "èªåã¹ã±ã¸ã¥ã¼ã«", "Manual": "<PERSON><PERSON><PERSON>¶å¾¡", "Auto": "<PERSON><PERSON><PERSON><PERSON>¶å¾¡", "NotLocated": "æªç¥", "Located": "å®äº", "Close": "å®è¡ä¸­", "Open": "ä¸æåæ­¢ä¸­", "mine": "mine", "updating": "æ´æ°ä¸­....", "updatesucceededRestarting": "æ´æ°æåãåèµ·åä¸­", "updateFailure": "æ´æ°å¤±æ", "pleaseEnterOrScan": "å¥åã¾ãã¯ã¹ã­ã£ã³ãã¦ãã ã"}}