#system
system.missing.parameter=ç¼ºå°åæ°
system.data.type.error=æ°æ®ç±»åéè¯¯
system.code.format.error=ç¼ç [%s]æ ¼å¼éè¯¯
system.name.format.error=åç§°[%s]æ ¼å¼éè¯¯
system.code.duplicate.error=[%s]å¼ä¸è½éå¤
system.code.is.empty.error=[%s]å¼ä¸è½ä¸ºç©º
system.operate.timeout=æä½è¶æ¶
system.directory.is.empty=å½åç®å½[%s]ä¸æä»¶ä¸ºç©º
system.directory.is.not.exists=å½åç®å½[%s]ä¸å­å¨
system.account.is.not.exists=è´¦å·ä¸å­å¨
system.account.is.already.exists=è´¦å·å·²å­å¨
system.account.passwd.is.error=è´¦å·ä¸å¯ç ä¸å¹é
system.account.old.passwd.is.error=åå¯ç ä¸æ­£ç¡®
system.account.is.disable=è´¦å·å·²è¢«åç¨
system.account.has.no.permission=è¯¥è´¦æ·æ²¡æä»»ä½é¡µé¢æéï¼è¯·èç³»ç®¡çå
system.menu.config.is.error=ä¸çº§èåä¸è½ä¸ºèªèº«
system.menu.delete.error=åå é¤å­èåææé®
system.account.permission.deny=ç¨æ·æéä¸è¶³
system.account.token.invalid=æªç»å½æç»å½å·²å¤±æ
system.db.record.duplicate.error=æ°æ®åºä¸­å·²å­å¨è¯¥è®°å½
system.no.avaliable.marker=ä¼ å¥ç¹ä½éåä¸å¯ç¨
system.no.avaliable.vehicle=ä¼ å¥æºå¨äººéåä¸å¯ç¨
system.version.is.dismatch=å½åç³»ç»çæ¬ä¸å¹é

#license
license.certificate.failure=è¯ä¹¦æªç¥å¼å¸¸
license.certificate.not.uploaded=è¯ä¹¦æªä¸ä¼ !
license.certificate.validate.failed=å·²ä¸ä¼ çè¯ä¹¦æ°æ®éè¯¯
license.certificate.expired=è¯ä¹¦å·²è¿æ

#excel
excel.export.error=å¯¼åºExcelæä»¶å¼å¸¸
excel.import.error=è§£æExcelæä»¶å¼å¸¸
excel.import.code.empty=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :ç¼ç ä¸ºç©º
excel.import.name.empty=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :åç§°ä¸ºç©º
excel.import.type.empty=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :ç±»åä¸ºç©º
excel.import.row.empty=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :ææ°ä¸ºç©º
excel.import.colum.empty=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :åæ°ä¸ºç©º
excel.import.layer.empty=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :å±æ°ä¸ºç©º
excel.import.workHeight.empty=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :ä½ä¸é«åº¦ä¸ºç©º
excel.import.code.exists=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :ç¼ç [{1}]å·²å­å¨
excel.import.barcode.exists=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :å®¹å¨ç¼ç [{1}]å·²å­å¨
excel.import.usage.status.error=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :å¯ç¨ç¶æéè¯¯æä¸å½åç³»ç»è¯­è¨ä¸å¹é
excel.import.occupy.status.error=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :å ç¨ç¶æéè¯¯æä¸å½åç³»ç»è¯­è¨ä¸å¹é
excel.import.notice.level.error=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :éç¥ç­çº§éè¯¯æä¸å½åç³»ç»è¯­è¨ä¸å¹é
excel.import.event.type.error=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :äºä»¶ç±»åéè¯¯æä¸å½åç³»ç»è¯­è¨ä¸å¹é

excel.import.code.repeat=å¯¼å¥å¤±è´¥, Excelè¡¨ä¸­å­å¨éå¤ç¼ç [{0}]
excel.import.barcode.repeat=å¯¼å¥å¤±è´¥, Excelè¡¨ä¸­å­å¨éå¤å®¹å¨ç¼ç [{0}]
excel.import.data.convert.fail=å¯¼å¥å¤±è´¥, ç¬¬[{0}]è¡æ°æ®éªè¯å¤±è´¥, åå :ç¬¬[{1}]åæ°æ®æ ¼å¼éè¯¯
excel.import.format.error=è¡¨æ ¼æ ¼å¼ä¸æ­£ç¡®ï¼è¯·å¯¼åºæ¨¡æ¿åéæ°å¯¼å¥
excel.import.name.format.error=å¯¼å¥çæä»¶æ ¼å¼ä¸æ­£ç¡®

#language
language.empty=è¯­è¨ä¸å­å¨
language.inuse=è¯¥è¯­è¨æ­£å¨ä½¿ç¨
language.upload.file.error=ä¸ä¼ çæä»¶æ ¼å¼ä¸æ­£ç¡®
language.upload.missing.info=è¯­è¨åç¼ºå°infoæä»¶
language.upload.missing.service=è¯­è¨åç¼ºå°serviceæä»¶
language.upload.missing.web=è¯­è¨åç¼ºå°webæä»¶
language.code.duplicate=ç¼ç [{0}]å·²å­å¨
language.code.nonstandard=ç¼ç [{0}]ä¸ç¬¦åå½éåè¦æ±

#vehicle
vehicle.batchOperation.result={0}ä¸ªæºå¨äººæä½æå, {1}ä¸ªæºå¨äººæä½å¤±è´¥
vehicle.operation.fail=æä½æºå¨äºº[%s]å¤±è´¥ï¼[%s]
vehicle.connect.fail=channelæªè¿æ¥æå·²æ­å¼
vehicle.request.timeout=è¯·æ±è¶æ¶ï¼%s
vehicle.is.not.login.error=æºå¨äººæªç»å½,è¯·åç»å½ï¼
vehicle.network.error=æä½æºå¨äººå¤±è´¥ï¼æºå¨äºº[%s]ç½ç»æªè¿æ¥
vehicle.code.duplicate=ç¼ç [{0}]å·²å­å¨, è¯·éæ°è¾å¥
vehicle.name.duplicate=åç§°[{0}]å·²å­å¨, è¯·éæ°è¾å¥
vehicle.name.pattern=å½åå¼[{0}]éè¦ç±å­æ¯ææ°å­ãä¸åçº¿ç»æ
vehicle.network.anomaly=éè®¯å¤±è´¥, è¯·æ£æ¥æºå¨äººæ¯å¦å·²è¿æ¥
vehicle.navigation.cancel=æºå¨äººè·¯å¾å¯¼èªè¢«åæ¶
vehicle.locationMapCode.empty=æºå¨äººæ ä½¿ç¨ä¸­çå®ä½å¾
vehicle.out.of.trace.error=æºå¨äººè±è½¨ï¼
vehicle.aim.marker.unreachable.error=ç®æ ç¹ä¸å¯è¾¾!
vehicle.controlStatus.repair.error=æºå¨äººå¤äºæ£ä¿®æ¨¡å¼ï¼ä¸åè®¸è¿è¡æ§å¶æ¨¡å¼åæ¢
vehicle.empty=æºå¨äººä¸å­å¨
vehicle.type.empty=æºå¨äººç±»å[%s]ä¸å­å¨
vehicle.type.bind.duplicate=æºå¨äººç±»å[%s]ç»å®éå¤
vehicle.type.excel.head.code=ç¼ç 
vehicle.type.excel.head.name=åç§°
vehicle.type.excel.head.rotatable=åè®¸æè½¬
vehicle.group.excel.head.code=ç¼ç 
vehicle.group.excel.head.name=åç§°
vehicle.wait.reason.marker.occupied=åæ¹ç¹ä½[{0}]è¢«å¶ä»æºå¨äººå ç¨
vehicle.wait.reason.marker.inControlArea=åæ¹ç¹ä½[{0}]å¤äºå°æ§åºå
vehicle.wait.reason.noParkArea.occupied=åæ¹ç¦ååºåè¢«å¶ä»æºå¨äººå ç¨
vehicle.wait.reason.auto.door.closed=åæ¹èªå¨é¨è®¾å¤æªå¼é¨
vehicle.wait.reason.airshower.door.closed=åæ¹é£æ·é¨è®¾å¤æªå¼é¨
vehicle.wait.reason.elevator.door.closed=åæ¹çµæ¢¯è®¾å¤æªå¼é¨
vehicle.wait.reason.marker.inForbiddenArea=åæ¹ç¹ä½[{0}]å¤äºç¦å¥åºå

#warehouse
warehouse.code.empty=åºä½ç¼ç ä¸ºç©º
warehouse.material.type.absent=ç©æç¼ç [{0}]ä¸å­å¨
warehouse.code.duplicate=åºä½ç¼ç [{0}]å·²å­å¨, è¯·éæ°è¾å¥
warehouse.barcode.duplicate=å®¹å¨ç¼ç [{0}]å·²å­å¨, è¯·éæ°è¾å¥
warehouse.start.need.less.than.end=å¼å§æ/å/å±å¿é¡»å°äºç»ææ/å/å±
warehouse.height.number.consistent=å±æ°æ°éä¸ä½ä¸é«åº¦æ°æ®çæ°éè¦ç¸ç­
warehouse.material.type.error=è·åç©æç±»åæ°æ®åºé
warehouse.empty.or.disable=åºä½ä¸å­å¨æå·²ç¦ç¨
warehouse.status.lock=è¯¥åºä½å·²è¢«éå®
warehouse.status.store=è¯¥åºä½ä¸ºå­å¨ç¶æ
warehouse.barcode.inuse=è¯¥å®¹å¨æ¡ç å·²è¢«å¶ä»åºä½ä½¿ç¨
warehouse.area.code.duplicate=åºåºç¼ç [{0}]å·²å­å¨, è¯·éæ°è¾å¥
warehouse.type.code.duplicate=ç±»åç¼ç [{0}]å·²å­å¨, è¯·éæ°è¾å¥
warehouse.area.excel.head.code=ç¼ç 
warehouse.area.excel.head.name=åç§°
warehouse.type.excel.head.code=ç¼ç 
warehouse.type.excel.head.name=åç§°
warehouse.excel.head.code=ç¼ç 
warehouse.excel.head.type.code=ç±»åç¼ç 
warehouse.excel.head.area.code=åºåºç¼ç 
warehouse.excel.head.row=ææ°
warehouse.excel.head.colum=åæ°
warehouse.excel.head.layer=å±æ°
warehouse.excel.head.work.height=ä½ä¸é«åº¦
warehouse.excel.head.work.marker=ä½ä¸ç¹ä½
warehouse.excel.head.occupy.status=å ç¨ç¶æ
warehouse.excel.head.barcode=å®¹å¨æ¡ç 
warehouse.excel.head.usage.code=å¯ç¨ç¶æ
warehouse.excel.head.param1=æ©å±å±æ§1
warehouse.excel.head.param2=æ©å±å±æ§2
warehouse.excel.head.param3=æ©å±å±æ§3
warehouse.excel.head.param4=æ©å±å±æ§4
warehouse.excel.head.param5=æ©å±å±æ§5
warehouse.excel.head.param6=æ©å±å±æ§6
warehouse.excel.head.param7=æ©å±å±æ§7
warehouse.excel.head.param8=æ©å±å±æ§8
warehouse.excel.head.param9=æ©å±å±æ§9
warehouse.excel.head.param10=æ©å±å±æ§10
warehouse.excel.head.status.updatedate=åºå­æ´æ°æ¶é´
warehouse.usage.status.enable=å¯ç¨
warehouse.usage.status.disable=ç¦ç¨
warehouse.occupy.status.lock=éå®
warehouse.occupy.status.store=å­å¨
warehouse.occupy.status.free=ç©ºé²

#statistics
statistics.cannot.gt.today=éæ©çæ¥æä¸è½è¶è¿ä»å¤©
statistics.cannot.lt.one.year.ago=éæ©çæ¥æä¸è½å¨ä¸å¹´ä¹å
statistics.start.cannot.gt.end=å¼å§æ¶é´ä¸è½å¤§äºç»ææ¶é´
statistics.name.avgHandleTime=å¹³åå¤çæ¶é´
statistics.name.number=æ°é
statistics.name.other=å¶ä»
statistics.name.no.map=æ å°å¾
statistics.name.low=ä½
statistics.name.lower=è¾ä½
statistics.name.medium=ä¸­
statistics.name.higher=è¾é«
statistics.name.high=é«
statistics.name.busy=å¿ç¢
statistics.name.free=ç©ºé²
statistics.name.abnormal=å¼å¸¸
statistics.name.charge=åçµ
statistics.name.park=æ³è½¦
statistics.name.work=ä½ä¸
statistics.name.disconnect=æªè¿æ¥
statistics.name.wait=ç­å¾
statistics.name.running=æ§è¡
statistics.name.total.task=æ»ä»»å¡
statistics.name.create=æ°å»º
statistics.name.finished=å®æ
statistics.name.cancel=åæ¶
statistics.name.avgExecuteTime=å¹³åæ§è¡æ¶é´
statistics.name.avgAllocateTime=å¹³åæ§è¡æ¶é´
statistics.name.actual.rate=å®éç¨¼å¨ç
statistics.name.theory.rate=çè®ºç¨¼å¨ç
statistics.name.cpu.rate.total=æ»ä½¿ç¨ç
statistics.name.cpu.rate.java=JAVA
statistics.name.cpu.rate.mysql=MySQL
statistics.name.cpu.rate.other=å¶ä»
statistics.name.memo.rate.total=æ»åå­
statistics.name.memo.rate.java=JAVA
statistics.name.memo.rate.mysql=MySQL
statistics.name.memo.rate.other=å¶ä»
statistics.name.disk.total=æ»å­å¨
statistics.name.disk.used=å·²ä½¿ç¨å®¹é
statistics.name.disk.free=ç©ºé²å®¹é
statistics.name.mysql.select=Select
statistics.name.mysql.delete=Delete
statistics.name.mysql.update=Update
statistics.name.mysql.insert=Insert
statistics.name.mysql.times.select=Select
statistics.name.mysql.times.delete=Delete
statistics.name.mysql.times.update=Update
statistics.name.mysql.times.insert=Insert
statistics.unit.number=ä¸ª
statistics.unit.tai=å°
statistics.unit.time=æ¬¡
statistics.unit.second=ç§
statistics.unit.minute=åé
statistics.unit.hour=å°æ¶
statistics.unit.day=å¤©

#charge.station
charge.station.not.exist.error=åçµæ¡©ä¸å­å¨
charge.station.connect.fail=åçµæ¡©è¿æ¥å¤±è´¥
charge.station.request.timeout=åçµæ¡©è¯·æ±è¶æ¶ï¼è¯·æ£æ¥ç½ç»è¿æ¥
charge.station.reset.error=åçµæ¡©å¤ä½å¼å¸¸
charge.station.break_discharge.error=åçµæ¡©ç»æ­¢æ¾çµå¼å¸¸
charge.station.bind.marker.delete.error=è¯¥åçµæ¡©ä¸åçµç¹æç»å®ï¼è¯·åè§£é¤ç»å®å³ç³»
charge.station.duplicate.bind.marker.error=åçµæ¡©[%s]å·²ç»ç»å®äºç¹ä½[%s]ï¼è¯·éæ°éæ©

#config
config.unit.day=å¤©
config.unit.meter=ç±³
config.unit.second=ç§
config.value.range=éç½®çæ°æ®èå´
config.company.name=æ·±å³ä¼è¾æºåæºå¨äººç§ææéå¬å¸
config.title.systemVersion=çæ¬å·
config.remark.systemVersion=çæ¬å·
config.title.ownCompany=çæææ
config.remark.ownCompany=çæææ
config.title.licenseCompanyName=ææä¿¡æ¯-å¬å¸åç§°
config.remark.licenseCompanyName=ææä¿¡æ¯-å¬å¸åç§°
config.title.licenseValidTimeRange=ææä¿¡æ¯-æææ
config.remark.licenseValidTimeRange=ææä¿¡æ¯-æææ
config.title.userOptLogExpireTime=æä½æ¥å¿
config.remark.userOptLogExpireTime=ç¨æ·æä½æ¥å¿ä¿çæ¶é´
config.title.interfaceLogExpireTime=æ¥å£æ¥å¿
config.remark.interfaceLogExpireTime=ç³»ç»æ¥å£æ¥å¿ä¿çæ¶é´
config.title.runningLogExpireTime=è¿è¡æ¥å¿
config.remark.runningLogExpireTime=ç³»ç»è¿è¡æ¥å¿ä¿çæ¶é´
config.title.notificationExpireTime=éç¥
config.remark.notificationExpireTime=ç³»ç»åè­¦éç¥ä¿çæ¶é´
config.title.businessDataExpireTime=ä¸å¡æ°æ®
config.remark.businessDataExpireTime=ä¸å¡çè¿è¡æ°æ®ä¿çæ¶é´ï¼åå«ä»»å¡åè¡¨ç­ä¸å¡æ°æ®
config.title.reportDataExpireTime=æ¥è¡¨æ°æ®
config.remark.reportDataExpireTime=ç»è®¡å½æ¡£åçæ¥è¡¨æ°æ®ä¿çæ¶é´
config.title.markerSpacingCheck=ç¹ä½é´è·
config.remark.markerSpacingCheck=ç¹ä½é´è·å¤æ­å¼å¯
config.title.markerSpacing=ç¹ä½é´è·
config.remark.markerSpacing=ç¹ä½é´è·ï¼mmï¼
config.title.markerAndPathSpacingCheck=ç¹å°è·¯å¾é´è·
config.remark.markerAndPathSpacingCheck=ç¹å°è·¯å¾é´è·å¤æ­å¼å¯
config.title.markerAndPathSpacing=ç¹å°è·¯å¾é´è·
config.remark.markerAndPathSpacing=ç¹å°è·¯å¾é´è·ï¼mmï¼
config.title.blockCheckEnable=é¿ééè§å
config.remark.blockCheckEnable=æºå¨äººéå°éç¢ç©æ¶ï¼è°åº¦ç³»ç»éæ°è§åè·¯å¾ä½¿æºå¨äººç»è¡
config.title.blockCheckInterval=é¿ééè§å
config.remark.blockCheckInterval=é¿éè§¦åæ¶é¿
config.title.removeBlockInterval=é¿ééè§å
config.remark.removeBlockInterval=éç¢éç½®æ¶é¿
config.title.abnormalVehicleRunPolicy=æéæºå¨äºº
config.remark.abnormalVehicleRunPolicy=åæ¹éå°æéæç¦»çº¿æºå¨äººï¼è®¾å®æºå¨äººçæ§è¡ç­ç¥
config.title.freeVehicleRunPolicy=ç©ºé²æºå¨äºº
config.remark.freeVehicleRunPolicy=åæ¹éå°ç©ºé²æºå¨äººï¼è®¾å®æºå¨äººçæ§è¡ç­ç¥
config.title.workVehicleRunPolicy=å¿ç¢æºå¨äºº
config.remark.workVehicleRunPolicy=åæ¹éå°å¿ç¢æºå¨äººï¼è®¾å®æºå¨äººçæ§è¡ç­ç¥
config.title.avoidMarkerTypes=å²çªé¿è®©ç¹ç±»å
config.remark.avoidMarkerTypes=å¤ä¸ªæºå¨äººè·¯å¾å²çªæ¶ï¼åè®¸æºå¨äººå»é¿è®©çç¹ç±»å
config.title.pathApplyLength=ä¸åè·¯å¾è·ç¦»
config.remark.pathApplyLength=è°åº¦ç³»ç»ä¸åç»æºå¨äººçè·¯å¾é¿åº¦ï¼å½ç½ç»ç¯å¢è¾å·®æ¶å¯å¢å è¯¥å¼
config.title.autoReleaseResource=æ­çº¿éæ¾èµæº
config.remark.autoReleaseResource=æºå¨äººæ­å¼ç½ç»ä¸å®æ¶é´åï¼è°åº¦ç³»ç»éæ¾è¯¥æºå¨äººå ç¨çä½ç½®ååºå
config.title.disconnectionTime=æ­çº¿éæ¾èµæº
config.remark.disconnectionTime=æ­çº¿æ¶é¿
config.title.occupyResourceRange=æºå¨äººèæåå¾
config.remark.occupyResourceRange=å½æºå¨äººä¸å¨ç¹ä½æè·¯å¾æ¶ï¼ä»¥æºå¨äººä¸­å¿ä¸ºåå¿ï¼ä»¥è¯¥å¼ä¸ºåå¾ï¼å ç¨è¯¥ååå«çææç¹ä½
config.title.trackRadius=è½¨éåå¾
config.remark.trackRadius=å½æºå¨äººå°æè¿çç¹ä½æè·¯å¾è·ç¦»è¶è¿è¯¥å¼æ¶, ç³»ç»å¤å®æºå¨äººè±è½¨
config.title.channelAvoidance=ééé¿è®©
config.remark.channelAvoidance=å¯ç¨ééé¿è®©åï¼å¯¹åæºå¨äººå¯å¨ééå¤ä¸»å¨é¿è®©
config.title.autoDoorAdvanceLength=æåå¼å«èªå¨é¨
config.remark.autoDoorAdvanceLength=æºå¨äººå°èªå¨é¨åç¹ä½çè·ç¦»å°äºè¯¥å¼æ¶å¼å«å¼é¨ï¼è¯¥å¼ä¸º0æ¶ä¸ä¼æåå¼å«
config.title.showerDoorAdvanceLength=æåå¼å«é£æ·é¨
config.remark.showerDoorAdvanceLength=æºå¨äººå°é£æ·é¨åç¹ä½çè·ç¦»å°äºè¯¥å¼æ¶å¼å«å¼é¨ï¼è¯¥å¼ä¸º0æ¶ä¸ä¼æåå¼å«
config.title.elevatorAdvanceLength=æåå¼å«çµæ¢¯
config.remark.elevatorAdvanceLength=æºå¨äººå°çµæ¢¯åç¹ä½çè·ç¦»å°äºè¯¥å¼æ¶å¼å«å¼é¨ï¼è¯¥å¼ä¸º0æ¶ä¸ä¼æåå¼å«
config.title.highPerformanceMode=é«æ§è½æ¨¡å¼
config.remark.highPerformanceMode=å½è°åº¦å¤§è§æ¨¡æºå¨äººæ¶ï¼å¯å¯ç¨é«æ§è½æ¨¡å¼æé«è°åº¦æç
config.title.highBattery=é«çµéï¼%ï¼
config.remark.highBattery=é«çµéï¼%ï¼
config.title.lowBattery=ä½çµéï¼%ï¼
config.remark.lowBattery=ä½çµéï¼%ï¼
config.title.chargeTaskTypeId=åå»ºä»»å¡
config.remark.chargeTaskTypeId=åå»ºä»»å¡
config.title.autoCharge=ç¶æ
config.remark.autoCharge=ç¶æ
config.title.parkTaskTypeId=åå»ºä»»å¡
config.remark.parkTaskTypeId=åå»ºä»»å¡
config.title.autoPark=ç¶æ
config.remark.autoPark=ç¶æ
config.title.pushCycle=æ¨éå¨æ
config.remark.pushCycle=å¨è¯¥æ¶é´èå´ååªæ¨éä¸æ¬¡
config.title.vehicleStatusPushUrl=æºå¨äººç¶ææ¥å£å°å
config.remark.vehicleStatusPushUrl=æºå¨äººç¶æåçååæ¶, æ¨éæºå¨äººæ¶æ¯å°è¯¥æ¥å£å°å
config.title.noticePushUrl=æ¶æ¯æ¨éå°å
config.remark.noticePushUrl=éè¯¯ç±»åçæ¶æ¯åºç°ææ¶å¤±æ¶ï¼æ¨éå¼å¸¸æ°æ®å°è¯¥æ¥å£å°å
config.title.pdaVersion=pdaææ°çæ¬
config.remark.pdaVersion=pdaææ°çæ¬
config.title.vehicleStatusPushInterval=çæ§å°æºå¨äººç¶ææ¨éé´é(ms)
config.remark.vehicleStatusPushInterval=çæ§å°æºå¨äººç¶ææ¨éé´é(ms)
config.title.noticePushInterval=çæ§å°å°ééæ¨éé´é(ms)
config.remark.noticePushInterval=çæ§å°å°ééæ¨éé´é(ms)
config.title.mapElementPushInterval=çæ§å°å°å¾åç´ ç¶æåæ´æ¨éé´é(ms)
config.remark.mapElementPushInterval=çæ§å°å°å¾åç´ ç¶æåæ´æ¨éé´é(ms)
config.title.thirdSystemTrafficAreaReqUrl=äº¤ç®¡åºåèµæºç³è¯·å°å
config.remark.thirdSystemTrafficAreaReqUrl=Fleetä½ä¸ºå®¢æ·ç«¯æ¶åæå¡ç«¯ç³è¯·äº¤ç®¡åºåèµæºï¼è®¾å®ç³è¯·æ¥å£å°å
config.title.driveFreeVehicleFreeTime=ç©ºé²æ¶é¿
config.remark.driveFreeVehicleFreeTime=å½é©±èµ¶ç©ºé²æºå¨äººæ¶ï¼ç©ºé²æºå¨äººéè¦è¾¾å°æå®ç©ºé²æ¶é¿
config.property.is.exist.error=ç³»ç»å±æ§å·²å­å¨ï¼
config.property.type.is.duplicate.error=ç³»ç»ç±»ååç±»éå¤ï¼
config.title.globalPauseExecutingArmScriptIsStop=å¨åºæå
config.remark.globalPauseExecutingArmScriptIsStop=å¨åºæåæ¶æ­£å¨æ§è¡æºæ¢°èå¨ä½çæºå¨äºº
config.title.noticePushLanguageType=æ¶æ¯è¯­è¨éç½®
config.remark.noticePushLanguageType=å®ä¹éè¯¯ç±»åçæ¶æ¯æ¨éæ¶çè¯­è¨
config.title.exceptionNotifyTime=éç¥æ¶é´
config.remark.exceptionNotifyTime=æºå¨äººè§¦åæ¥åï¼åé/ç³è¯·ä¸å°ç¹ä½ãåºåèµæºè¾¾å°è®¾å®æ¶é´æ¶åèµ·æ´é«ç­çº§éç¥


#notice
notice.missing.notice.config=ç³»ç»ç¼ºå°è¯¥å¼å¸¸ç éç½®ä¿¡æ¯
notice.level.common=æ®é
notice.level.warning=è­¦å
notice.level.error=éè¯¯
notice.record.status.not.close=æªå³é­
notice.record.status.closed=å·²å³é­
notice.config.excel.head.code=ç¼ç 
notice.config.excel.head.level=ç­çº§
notice.config.excel.head.source=æ¥æº
notice.config.excel.head.invalidTime=é´éæ¶é´
notice.config.excel.head.desc=æè¿°
notice.config.excel.head.solution=æªæ½
notice.record.excel.head.code=ç¼ç 
notice.record.excel.head.level=ç­çº§
notice.record.excel.head.source=æ¥æº
notice.record.excel.head.desc=æè¿°
notice.record.excel.head.solution=æªæ½
notice.record.excel.head.status=ç¶æ
notice.record.excel.head.vehicle=æºå¨äºº
notice.record.excel.head.task=ä»»å¡ID
notice.record.excel.head.device=è®¾å¤ID
notice.record.excel.head.map=å°å¾
notice.record.excel.head.create.date=åå»ºæ¶é´
notice.record.excel.head.update.date=æ´æ°æ¶é´
notice.record.excel.head.close.date=å³é­æ¶é´
notice.record.http.request.param=å¼å¸¸éç¥ç¶æåæ´HTTPæ¨é, è¯·æ±åæ°
notice.record.http.response.param=å¼å¸¸éç¥ç¶æåæ´HTTPæ¨é, ååºåæ°

notice.description.100001=baseå¿è·³è¶æ¶
notice.solution.100001=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100002=æ ¸å¿æ¿å¯å¨è¶æ¶
notice.solution.100002=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100020=ç¶æç¯éè®¯æé
notice.solution.100020=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100021=ä¿¡å·ç¯éè®¯æé
notice.solution.100021=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100030=åç¹éµåé·è¾¾1éè®¯è¶æ¶
notice.solution.100030=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100031=åç¹éµåé·è¾¾1æé
notice.solution.100031=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100032=åç¹éµåé·è¾¾2éè®¯è¶æ¶
notice.solution.100032=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100033=åç¹éµåé·è¾¾2æé
notice.solution.100033=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100034=åç¹éµåé·è¾¾3éè®¯è¶æ¶
notice.solution.100034=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100035=åç¹éµåé·è¾¾3æé
notice.solution.100035=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100036=åç¹éµåé·è¾¾4éè®¯è¶æ¶
notice.solution.100036=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100037=åç¹éµåé·è¾¾4æé
notice.solution.100037=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100050=å®å¨é·è¾¾1éè®¯è¶æ¶
notice.solution.100050=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100051=å®å¨é·è¾¾1æé
notice.solution.100051=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100052=å®å¨é·è¾¾2éè®¯è¶æ¶
notice.solution.100052=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100053=å®å¨é·è¾¾2æé
notice.solution.100053=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100054=å®å¨é·è¾¾3éè®¯è¶æ¶
notice.solution.100054=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100055=å®å¨é·è¾¾3æé
notice.solution.100055=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100056=å®å¨é·è¾¾4éè®¯è¶æ¶
notice.solution.100056=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100057=å®å¨é·è¾¾4æé
notice.solution.100057=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100100=REæ©å±æ¿0éè®¯è¶æ¶
notice.solution.100100=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100101=REæ©å±æ¿1éè®¯è¶æ¶
notice.solution.100101=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100102=REæ©å±æ¿2éè®¯è¶æ¶
notice.solution.100102=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100103=REæ©å±æ¿3éè®¯è¶æ¶
notice.solution.100103=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100104=REæ©å±æ¿4éè®¯è¶æ¶
notice.solution.100104=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100105=REæ©å±æ¿5éè®¯è¶æ¶
notice.solution.100105=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100106=REæ©å±æ¿6éè®¯è¶æ¶
notice.solution.100106=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100107=REæ©å±æ¿7éè®¯è¶æ¶
notice.solution.100107=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100108=REæ©å±æ¿8éè®¯è¶æ¶
notice.solution.100108=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100109=REæ©å±æ¿9éè®¯è¶æ¶
notice.solution.100109=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100110=REæ©å±æ¿10éè®¯è¶æ¶
notice.solution.100110=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100111=REæ©å±æ¿11éè®¯è¶æ¶
notice.solution.100111=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100112=REæ©å±æ¿12éè®¯è¶æ¶
notice.solution.100112=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100113=REæ©å±æ¿13éè®¯è¶æ¶
notice.solution.100113=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100114=REæ©å±æ¿14éè®¯è¶æ¶
notice.solution.100114=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.100115=REæ©å±æ¿15éè®¯è¶æ¶
notice.solution.100115=å¯è½æ¥çº¿æ¾å¨ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105000=å·¦ä¾§é©±å¨å¨CANéè®¯æé
notice.solution.105000=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105001=å·¦ä¾§é©±å¨å¨Canèç¹å¯å¨è¶æ¶
notice.solution.105001=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105002=å·¦é©±å¨å¨ä¸çµå¯å¨è¶æ¶
notice.solution.105002=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105003=å·¦é©±å¨å¨PDOéç½®æä»¶ç¼ºå°
notice.solution.105003=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105009=å·¦é©±å¨å¨æªå®ä¹éè¯¯
notice.solution.105009=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105010=å·¦ä¾§é©±å¨å¨ç¼ç å¨æé
notice.solution.105010=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105011=å·¦ä¾§é©±å¨å¨ççµåè¿é«
notice.solution.105011=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105012=å·¦ä¾§é©±å¨å¨ççµåè¿ä½
notice.solution.105012=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105013=å·¦ä¾§é©±å¨å¨è¿æµ
notice.solution.105013=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105014=å·¦ä¾§é©±å¨å¨æ¸©åº¦è¿é«
notice.solution.105014=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105015=å·¦ä¾§é©±å¨å¨è¿è¡è¯¯å·®è¿å¤§
notice.solution.105015=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105016=å·¦ä¾§é©±å¨å¨é»è¾çµåå¼å¸¸
notice.solution.105016=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105017=å·¦ä¾§é©±å¨å¨çµæºæé
notice.solution.105017=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105018=å·¦ä¾§é©±å¨å¨æé
notice.solution.105018=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105019=å·¦ä¾§é©±å¨å¨çç³»ç»æ°æ®éè¯¯
notice.solution.105019=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105020=å·¦ä¾§é©±å¨å¨è½¯ä»¶è¿è¡æ¥é
notice.solution.105020=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105021=å·¦ä¾§é©±å¨å¨ççµæºéç½®éè¯¯
notice.solution.105021=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105022=å·¦ä¾§é©±å¨å¨çæ­£éä½æ¥é
notice.solution.105022=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105023=å·¦ä¾§é©±å¨å¨çè´éä½æ¥é
notice.solution.105023=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105024=å·¦ä¾§é©±å¨å¨çè¶éæ¥è­¦
notice.solution.105024=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105025=å·¦ä¾§é©±å¨å¨è¿è½½
notice.solution.105025=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105026=å·¦ä¾§é©±å¨å¨CAN BUSæ»çº¿æé
notice.solution.105026=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105027=å·¦ä¾§é©±å¨å¨OpenCanåæ°éè¯¯
notice.solution.105027=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105028=å·¦ä¾§é©±å¨å¨OpenCanéè®¯å¼å¸¸
notice.solution.105028=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105029=å·¦ä¾§é©±å¨å¨æ±é¸å¼å¸¸
notice.solution.105029=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105030=å·¦ä¾§é©±å¨å¨å¼å¸¸åæ­¢
notice.solution.105030=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105031=å·¦ä¾§é©±å¨å¨ç¸çµåå¼å¸¸
notice.solution.105031=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105100=å³ä¾§é©±å¨å¨CANéè®¯æé
notice.solution.105100=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105101=å³ä¾§é©±å¨å¨Canèç¹å¯å¨è¶æ¶
notice.solution.105101=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105102=å³é©±å¨å¨ä¸çµå¯å¨è¶æ¶
notice.solution.105102=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105103=å³é©±å¨å¨PDOéç½®æä»¶ç¼ºå°
notice.solution.105103=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105109=å³é©±å¨å¨æªå®ä¹éè¯¯
notice.solution.105109=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105110=å³ä¾§é©±å¨å¨ç¼ç å¨æé
notice.solution.105110=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105111=å³ä¾§é©±å¨å¨ççµåè¿é«
notice.solution.105111=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105112=å³ä¾§é©±å¨å¨ççµåè¿ä½
notice.solution.105112=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105113=å³ä¾§é©±å¨å¨è¿æµ
notice.solution.105113=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105114=å³ä¾§é©±å¨å¨æ¸©åº¦è¿é«
notice.solution.105114=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105115=å³ä¾§é©±å¨å¨è¿è¡è¯¯å·®è¿å¤§
notice.solution.105115=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105116=å³ä¾§é©±å¨å¨é»è¾çµåå¼å¸¸
notice.solution.105116=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105117=å³ä¾§é©±å¨å¨çµæºæé
notice.solution.105117=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105118=å³ä¾§é©±å¨å¨æé
notice.solution.105118=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105119=å³ä¾§é©±å¨å¨çç³»ç»æ°æ®éè¯¯
notice.solution.105119=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105120=å³ä¾§é©±å¨å¨è½¯ä»¶è¿è¡æ¥é
notice.solution.105120=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105121=å³ä¾§é©±å¨å¨ççµæºéç½®éè¯¯
notice.solution.105121=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105122=å³ä¾§é©±å¨å¨çæ­£éä½æ¥é
notice.solution.105122=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105123=å³ä¾§é©±å¨å¨çè´éä½æ¥é
notice.solution.105123=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105124=å³ä¾§é©±å¨å¨çè¶éæ¥è­¦
notice.solution.105124=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105125=å³ä¾§é©±å¨å¨è¿è½½
notice.solution.105125=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105126=å³ä¾§é©±å¨å¨CAN BUSæ»çº¿æé
notice.solution.105126=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105127=å³ä¾§é©±å¨å¨OpenCanåæ°éè¯¯
notice.solution.105127=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105128=å³ä¾§é©±å¨å¨OpenCanéè®¯å¼å¸¸
notice.solution.105128=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105129=å³ä¾§é©±å¨å¨æ±é¸å¼å¸¸
notice.solution.105129=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105130=å³ä¾§é©±å¨å¨å¼å¸¸åæ­¢
notice.solution.105130=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105131=å³ä¾§é©±å¨å¨ç¸çµåå¼å¸¸
notice.solution.105131=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105200=åéé©±å¨å¨CANéè®¯æé
notice.solution.105200=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105201=åéé©±å¨å¨ Canèç¹å¯å¨è¶æ¶
notice.solution.105201=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105202=åéé©±å¨å¨æ¾åç¹è¶æ¶
notice.solution.105202=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105203=åéé©±å¨å¨ä¸çµå¯å¨è¶æ¶
notice.solution.105203=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105204=åéé©±å¨å¨PDOéç½®åæ°ç¼ºå°
notice.solution.105204=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105209=åéé©±å¨å¨æªå®ä¹éè¯¯
notice.solution.105209=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105210=åéé©±å¨å¨ç¼ç å¨æé
notice.solution.105210=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105211=åéé©±å¨å¨ççµåè¿é«
notice.solution.105211=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105212=åéé©±å¨å¨ççµåè¿ä½
notice.solution.105212=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105213=åéé©±å¨å¨è¿æµ
notice.solution.105213=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105214=åéé©±å¨å¨æ¸©åº¦è¿é«
notice.solution.105214=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105215=åéé©±å¨å¨è¿è¡è¯¯å·®è¿å¤§
notice.solution.105215=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105216=åéé©±å¨å¨é»è¾çµåå¼å¸¸
notice.solution.105216=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105217=åéé©±å¨å¨çµæºæé
notice.solution.105217=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105218=åéé©±å¨å¨æé
notice.solution.105218=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105219=åéé©±å¨å¨çç³»ç»æ°æ®éè¯¯
notice.solution.105219=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105220=åéé©±å¨å¨è½¯ä»¶è¿è¡æ¥é
notice.solution.105220=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105221=åéé©±å¨å¨ççµæºéç½®éè¯¯
notice.solution.105221=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105222=åéé©±å¨å¨çæ­£éä½æ¥é
notice.solution.105222=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105223=åéé©±å¨å¨çè´éä½æ¥é
notice.solution.105223=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105224=åéé©±å¨å¨çè¶éæ¥è­¦
notice.solution.105224=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105225=åéé©±å¨å¨è¿è½½
notice.solution.105225=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105226=åéé©±å¨å¨CAN BUSæ»çº¿æé
notice.solution.105226=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105227=åéé©±å¨å¨OpenCanåæ°éè¯¯
notice.solution.105227=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105228=åéé©±å¨å¨OpenCanéè®¯å¼å¸¸
notice.solution.105228=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105229=åéé©±å¨å¨å¼å¸¸åæ­¢
notice.solution.105229=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105230=åéé©±å¨å¨ç¸çµåå¼å¸¸
notice.solution.105230=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105300=æè½¬é©±å¨å¨CANéè®¯æé
notice.solution.105300=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105301=æè½¬é©±å¨å¨ Canèç¹å¯å¨è¶æ¶
notice.solution.105301=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105302=æè½¬é©±å¨å¨æ¾åç¹è¶æ¶
notice.solution.105302=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105303=æè½¬é©±å¨å¨ä¸çµå¯å¨è¶æ¶
notice.solution.105303=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105304=æè½¬é©±å¨å¨PDOéç½®åæ°ç¼ºå°
notice.solution.105304=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105309=æè½¬é©±å¨å¨æªå®ä¹éè¯¯
notice.solution.105309=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105310=æè½¬é©±å¨å¨ç¼ç å¨æé
notice.solution.105310=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105311=æè½¬é©±å¨å¨ççµåè¿é«
notice.solution.105311=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105312=æè½¬é©±å¨å¨ççµåè¿ä½
notice.solution.105312=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105313=æè½¬é©±å¨å¨è¿æµ
notice.solution.105313=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105314=æè½¬é©±å¨å¨æ¸©åº¦è¿é«
notice.solution.105314=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105315=æè½¬é©±å¨å¨è¿è¡è¯¯å·®è¿å¤§
notice.solution.105315=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105316=æè½¬é©±å¨å¨é»è¾çµåå¼å¸¸
notice.solution.105316=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105317=æè½¬é©±å¨å¨çµæºæé
notice.solution.105317=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105318=æè½¬é©±å¨å¨æé
notice.solution.105318=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105319=æè½¬é©±å¨å¨çç³»ç»æ°æ®éè¯¯
notice.solution.105319=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105320=æè½¬é©±å¨å¨è½¯ä»¶è¿è¡æ¥é
notice.solution.105320=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105321=æè½¬é©±å¨å¨ççµæºéç½®éè¯¯
notice.solution.105321=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105322=æè½¬é©±å¨å¨çæ­£éä½æ¥é
notice.solution.105322=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105323=æè½¬é©±å¨å¨çè´éä½æ¥é
notice.solution.105323=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105324=æè½¬é©±å¨å¨çè¶éæ¥è­¦
notice.solution.105324=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105325=æè½¬é©±å¨å¨è¿è½½
notice.solution.105325=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105326=æè½¬é©±å¨å¨CAN BUSæ»çº¿æé
notice.solution.105326=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105327=æè½¬é©±å¨å¨OpenCanåæ°éè¯¯
notice.solution.105327=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105328=æè½¬é©±å¨å¨OpenCanéè®¯å¼å¸¸
notice.solution.105328=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105329=æè½¬é©±å¨å¨å¼å¸¸åæ­¢
notice.solution.105329=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105330=æè½¬é©±å¨å¨ç¸çµåå¼å¸¸
notice.solution.105330=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105500=BMS éè®¯å¼å¸¸
notice.solution.105500=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105501=BMSæªå®ä¹éè¯¯
notice.solution.105501=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105502=åçµè¿æµå¼å¸¸
notice.solution.105502=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105503=æ¾çµè¿æµå¼å¸¸
notice.solution.105503=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105504=çµè¯æ¬ å
notice.solution.105504=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105505=çµè¯è¿å
notice.solution.105505=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105506=æ»ä½æ¬ å
notice.solution.105506=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105507=æ»ä½è¿å
notice.solution.105507=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105508=åå·®é«äºåçµåè®¸åå·®ä¸é
notice.solution.105508=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105509=çµè¯åå·®è¶è¿ä¸é
notice.solution.105509=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105510=çµè¯æ¸©åº¦è¶è¿åçµæ¸©åº¦ä¸é
notice.solution.105510=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105511=çµè¯æ¸©åº¦è¶è¿æ¾çµæ¸©åº¦ä¸é
notice.solution.105511=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105512=çµè¯æ¸©å·®è¶è¿åçµæ¸©åº¦ä¸é
notice.solution.105512=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105513=çµè¯æ¸©å·®è¶è¿æ¾çµæ¸©åº¦ä¸é
notice.solution.105513=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105514=çµè¯æ¸©åº¦ä½äºåæ¾çµä¸é
notice.solution.105514=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105515=MOSç®¡æ¸©åº¦è¶è¿åçµæ¸©åº¦ä¸é
notice.solution.105515=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105516=MOSç®¡æ¸©å·®è¶è¿æ¾åæ¸©å·®ä¸é
notice.solution.105516=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105551=bmså¼å¸¸ï¼åæ¬ä¿æ¤ãéæ ·æéâ¦â¦ï¼
notice.solution.105551=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105552=BMSåå¼å¸¸(åæ¬è¿åï¼è¿æµï¼é«ä½æ¸©æéç­ä¿¡æ¯)
notice.solution.105552=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105610=PDO1 è¿æµ
notice.solution.105610=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105611=PDO2 è¿æµ
notice.solution.105611=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105612=PDO3 è¿æµ
notice.solution.105612=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105613=PDO4è¿æµ
notice.solution.105613=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105614=PDO5 è¿æµ
notice.solution.105614=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105615=PDO6 è¿æµ
notice.solution.105615=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105616=PDO7 è¿æµ
notice.solution.105616=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105617=æ»PDOçµæµè¿å¤§
notice.solution.105617=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105700=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºæªå®ä¹éè¯¯
notice.solution.105700=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105701=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºè½¯ä»¶éè¯¯
notice.solution.105701=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105702=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºè¿å
notice.solution.105702=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105703=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºä½å
notice.solution.105703=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105704=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºå¯å¨éè¯¯
notice.solution.105704=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105705=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºè¿æµ
notice.solution.105705=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105706=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºç¼ç å¨éè¯¯
notice.solution.105706=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105707=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºæ¸©åº¦è¿é«
notice.solution.105707=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105708=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºçµè·¯æ¿è¿é«
notice.solution.105708=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105709=åè½®åè½¬ï¼å·¦ä¾§è½¬åçµæºéè®¯è¶æ¶
notice.solution.105709=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105800=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºæªå®ä¹éè¯¯
notice.solution.105800=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105801=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºè½¯ä»¶éè¯¯
notice.solution.105801=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105802=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºè¿å
notice.solution.105802=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105803=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºä½å
notice.solution.105803=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105804=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºå¯å¨éè¯¯
notice.solution.105804=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105805=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºè¿æµ
notice.solution.105805=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105806=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºç¼ç å¨éè¯¯
notice.solution.105806=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105807=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºæ¸©åº¦è¿é«
notice.solution.105807=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105808=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºçµè·¯æ¿è¿é«
notice.solution.105808=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105809=åè½®åè½¬ï¼å³ä¾§è½¬åçµæºéè®¯è¶æ¶
notice.solution.105809=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105900=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨CANéè®¯æé
notice.solution.105900=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105901=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨Canèç¹å¯å¨è¶æ¶
notice.solution.105901=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105902=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨ä¸çµå¯å¨è¶æ¶
notice.solution.105902=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105903=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨PDOéç½®åæ°ç¼ºå°
notice.solution.105903=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105909=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨æªå®ä¹éè¯¯
notice.solution.105909=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105910=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨ç¼ç å¨æé
notice.solution.105910=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105911=èµè½®ï¼åè½®è¡èµ°è½®é©±å¨å¨çµåè¿é«
notice.solution.105911=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105912=èµè½®ï¼åè½®è¡èµ°è½®é©±å¨å¨çµåè¿ä½
notice.solution.105912=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105913=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨è¿æµ
notice.solution.105913=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105914=èµè½®ï¼åè½®è¡èµ°çµæºæ¸©åº¦è¿é«
notice.solution.105914=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105915=èµè½®ï¼åè½®è¡èµ°çµæºè¿è¡è¯¯å·®è¿å¤§
notice.solution.105915=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105916=èµè½®ï¼åè½®è¡èµ°çµæºé»è¾çµåå¼å¸¸
notice.solution.105916=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105917=èµè½®ï¼åè½®è¡èµ°çµæºæé
notice.solution.105917=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105918=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨æé
notice.solution.105918=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105919=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨ç³»ç»æ°æ®éè¯¯
notice.solution.105919=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105920=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨è½¯ä»¶è¿è¡æ¥é
notice.solution.105920=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105921=èµè½®ï¼åè½®è¡è½®çµæºéç½®éè¯¯
notice.solution.105921=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105922=èµè½®ï¼åè½®è¡èµ°çµæºè¶éæ¥è­¦
notice.solution.105922=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105923=èµè½®ï¼åè½®è¡èµ°çµæºè¿è½½
notice.solution.105923=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105924=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨CAN BUSæ»çº¿æé
notice.solution.105924=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105925=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨OpenCanåæ°éè¯¯
notice.solution.105925=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105926=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨OpenCanéè®¯å¼å¸¸
notice.solution.105926=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105927=èµè½®ï¼åè½®æ±é¸å¼å¸¸
notice.solution.105927=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105928=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨å¼å¸¸åæ­¢
notice.solution.105928=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.105929=èµè½®ï¼åè½®è¡èµ°çµæºç¸çµåå¼å¸¸
notice.solution.105929=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106000=èµè½®ï¼åè½®è½¬åé©±å¨å¨CANéè®¯æé
notice.solution.106000=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106001=èµè½®ï¼åè½®è½¬åé©±å¨å¨Canèç¹å¯å¨è¶æ¶
notice.solution.106001=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106002=èµè½®ï¼åè½®è½¬åé©±å¨å¨ä¸çµå¯å¨è¶æ¶
notice.solution.106002=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106003=èµè½®ï¼åè½®è½¬åé©±å¨å¨PDOéç½®åæ°ç¼ºå°
notice.solution.106003=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106004=èµè½®ï¼åè½®è½¬åé©±å¨å¨æ¾åç¹è¶æ¶
notice.solution.106004=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106009=èµè½®ï¼åè½®è½¬åé©±å¨å¨æªå®ä¹éè¯¯
notice.solution.106009=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106010=èµè½®ï¼åè½®è½¬åé©±å¨å¨ç¼ç å¨æé
notice.solution.106010=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106011=èµè½®ï¼åè½®è½¬åé©±å¨å¨çµåè¿é«
notice.solution.106011=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106012=èµè½®ï¼åè½®è½¬åé©±å¨å¨çµåè¿ä½
notice.solution.106012=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106013=èµè½®ï¼åè½®è½¬åé©±å¨å¨è¿æµ
notice.solution.106013=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106014=èµè½®ï¼åè½®è½¬åçµæºæ¸©åº¦è¿é«
notice.solution.106014=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106015=èµè½®ï¼åè½®è½¬åçµæºè¿è¡è¯¯å·®è¿å¤§
notice.solution.106015=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106016=èµè½®ï¼åè½®è½¬åçµæºé»è¾çµåå¼å¸¸
notice.solution.106016=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106017=èµè½®ï¼åè½®è½¬åçµæºæé
notice.solution.106017=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106018=èµè½®ï¼åè½®è½¬åé©±å¨å¨æé
notice.solution.106018=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106019=èµè½®ï¼åè½®è½¬åé©±å¨å¨ç³»ç»æ°æ®éè¯¯
notice.solution.106019=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106020=èµè½®ï¼åè½®è½¬åé©±å¨å¨è½¯ä»¶è¿è¡æ¥é
notice.solution.106020=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106021=èµè½®ï¼åè½®è½¬åçµæºéç½®éè¯¯
notice.solution.106021=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106022=èµè½®ï¼åè½®è½¬åé©±å¨å¨æ­£éä½æ¥é
notice.solution.106022=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106023=èµè½®ï¼åè½®è½¬åé©±å¨å¨è´éä½æ¥é
notice.solution.106023=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106024=èµè½®ï¼åè½®è½¬åçµæºè¶éæ¥è­¦
notice.solution.106024=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106025=èµè½®ï¼åè½®è½¬åçµæºè¿è½½
notice.solution.106025=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106026=èµè½®ï¼åè½®è½¬åé©±å¨å¨CAN BUSæ»çº¿æé
notice.solution.106026=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106027=èµè½®ï¼åè½®è½¬åé©±å¨å¨OpenCanåæ°éè¯¯
notice.solution.106027=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106028=èµè½®ï¼åè½®è½¬åé©±å¨å¨OpenCanéè®¯å¼å¸¸
notice.solution.106028=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106029=èµè½®ï¼åè½®è½¬åé©±å¨å¨å¼å¸¸åæ­¢
notice.solution.106029=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106030=èµè½®ï¼åè½®è½¬åçµæºç¸çµåå¼å¸¸
notice.solution.106030=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106100=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨CANéè®¯æé
notice.solution.106100=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106101=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨Canèç¹å¯å¨è¶æ¶
notice.solution.106101=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106102=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨ä¸çµå¯å¨è¶æ¶
notice.solution.106102=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106103=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨PDOéç½®åæ°ç¼ºå°
notice.solution.106103=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106109=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨æªå®ä¹éè¯¯
notice.solution.106109=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106110=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨ç¼ç å¨æé
notice.solution.106110=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106111=èµè½®ï¼åè½®è¡èµ°è½®é©±å¨å¨çµåè¿é«
notice.solution.106111=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106112=èµè½®ï¼åè½®è¡èµ°è½®é©±å¨å¨çµåè¿ä½
notice.solution.106112=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106113=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨è¿æµ
notice.solution.106113=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106114=èµè½®ï¼åè½®è¡èµ°çµæºæ¸©åº¦è¿é«
notice.solution.106114=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106115=èµè½®ï¼åè½®è¡èµ°çµæºè¿è¡è¯¯å·®è¿å¤§
notice.solution.106115=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106116=èµè½®ï¼åè½®è¡èµ°çµæºé»è¾çµåå¼å¸¸
notice.solution.106116=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106117=èµè½®ï¼åè½®è¡èµ°çµæºæé
notice.solution.106117=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106118=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨æé
notice.solution.106118=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106119=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨ç³»ç»æ°æ®éè¯¯
notice.solution.106119=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106120=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨è½¯ä»¶è¿è¡æ¥é
notice.solution.106120=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106121=èµè½®ï¼åè½®è¡è½®çµæºéç½®éè¯¯
notice.solution.106121=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106122=èµè½®ï¼åè½®è¡èµ°çµæºè¶éæ¥è­¦
notice.solution.106122=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106123=èµè½®ï¼åè½®è¡èµ°çµæºè¿è½½
notice.solution.106123=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106124=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨CAN BUSæ»çº¿æé
notice.solution.106124=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106125=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨OpenCanåæ°éè¯¯
notice.solution.106125=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106126=èµè½®ï¼åè½®è¡èµ°é©±å¨å¨OpenCanéè®¯å¼å¸¸
notice.solution.106126=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106127=èµè½®ï¼åè½®æ±é¸å¼å¸¸
notice.solution.106127=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106128=èµè½®ï¼åè½®è¡èµ°å¼å¸¸åæ­¢
notice.solution.106128=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106129=èµè½®ï¼åè½®è¡èµ°çµæºç¸çµåå¼å¸¸
notice.solution.106129=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106200=èµè½®ï¼åè½®è½¬åé©±å¨å¨CANéè®¯æé
notice.solution.106200=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åéè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106201=èµè½®ï¼åè½®è½¬åé©±å¨å¨Canèç¹å¯å¨è¶æ¶
notice.solution.106201=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106202=èµè½®ï¼åè½®è½¬åé©±å¨å¨ä¸çµå¯å¨è¶æ¶
notice.solution.106202=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106203=èµè½®ï¼åè½®è½¬åé©±å¨å¨PDOéç½®åæ°ç¼ºå°
notice.solution.106203=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106204=èµè½®ï¼åè½®è½¬åé©±å¨å¨æ¾åç¹è¶æ¶
notice.solution.106204=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106209=èµè½®ï¼åè½®è½¬åé©±å¨å¨æªå®ä¹éè¯¯
notice.solution.106209=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106210=èµè½®ï¼åè½®è½¬åé©±å¨å¨ç¼ç å¨æé
notice.solution.106210=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106211=èµè½®ï¼åè½®è½¬åé©±å¨å¨çµåè¿é«
notice.solution.106211=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106212=èµè½®ï¼åè½®è½¬åé©±å¨å¨çµåè¿ä½
notice.solution.106212=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106213=èµè½®ï¼åè½®è½¬åé©±å¨å¨è¿æµ
notice.solution.106213=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106214=èµè½®ï¼åè½®è½¬åçµæºæ¸©åº¦è¿é«
notice.solution.106214=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106215=èµè½®ï¼åè½®è½¬åçµæºè¿è¡è¯¯å·®è¿å¤§
notice.solution.106215=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106216=èµè½®ï¼åè½®è½¬åçµæºé»è¾çµåå¼å¸¸
notice.solution.106216=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106217=èµè½®ï¼åè½®è½¬åçµæºæé
notice.solution.106217=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106218=èµè½®ï¼åè½®è½¬åé©±å¨å¨æé
notice.solution.106218=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106219=èµè½®ï¼åè½®è½¬åé©±å¨å¨ç³»ç»æ°æ®éè¯¯
notice.solution.106219=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106220=èµè½®ï¼åè½®è½¬åé©±å¨å¨è½¯ä»¶è¿è¡æ¥é
notice.solution.106220=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106221=èµè½®ï¼åè½®è½¬åçµæºéç½®éè¯¯
notice.solution.106221=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106222=èµè½®ï¼åè½®è½¬åé©±å¨å¨æ­£éä½æ¥é
notice.solution.106222=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106223=èµè½®ï¼åè½®è½¬åé©±å¨å¨è´éä½æ¥é
notice.solution.106223=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106224=èµè½®ï¼åè½®è½¬åçµæºè¶éæ¥è­¦
notice.solution.106224=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106225=èµè½®ï¼åè½®è½¬åçµæºè¿è½½
notice.solution.106225=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106226=èµè½®ï¼åè½®è½¬åé©±å¨å¨CAN BUSæ»çº¿æé
notice.solution.106226=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106227=èµè½®ï¼åè½®è½¬åé©±å¨å¨OpenCanåæ°éè¯¯
notice.solution.106227=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106228=èµè½®ï¼åè½®è½¬åé©±å¨å¨OpenCanéè®¯å¼å¸¸
notice.solution.106228=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106229=èµè½®ï¼åè½®è½¬åé©±å¨å¨å¼å¸¸åæ­¢
notice.solution.106229=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.106230=èµè½®ï¼åè½®è½¬åçµæºç¸çµåå¼å¸¸
notice.solution.106230=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®å
notice.description.110001=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110001=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110002=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110002=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110003=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110003=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110004=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110004=é©±å¨å¨å¼å¸¸ï¼éè¦å³æºå¹¶éç½®ä¸å°æ¶ååä½¿ç¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110005=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110005=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110006=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110006=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110007=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110007=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110008=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110008=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110009=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110009=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110010=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110010=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110011=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110011=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110012=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110012=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110013=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110013=é©±å¨å¨å¼å¸¸ï¼éè¦å³æºå¹¶éç½®ä¸å°æ¶ååä½¿ç¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110014=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110014=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110015=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110015=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110016=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110016=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110017=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110017=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110018=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110018=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110019=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110019=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110020=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110020=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110021=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110021=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110022=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110022=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110023=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110023=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110024=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110024=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110025=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110025=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110026=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110026=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110027=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110027=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110028=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110028=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110029=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110029=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110030=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110030=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110031=å·¦é©±å¨å¨-å¼å¸¸
notice.solution.110031=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110032=å³é©±å¨å¨-å¼å¸¸
notice.solution.110032=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110033=å³é©±å¨å¨-å¼å¸¸
notice.solution.110033=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110034=å³é©±å¨å¨-å¼å¸¸
notice.solution.110034=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110035=å³é©±å¨å¨-å¼å¸¸
notice.solution.110035=é©±å¨å¨å¼å¸¸ï¼éè¦å³æºå¹¶éç½®ä¸å°æ¶ååä½¿ç¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110036=å³é©±å¨å¨-å¼å¸¸
notice.solution.110036=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110037=å³é©±å¨å¨-å¼å¸¸
notice.solution.110037=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110038=å³é©±å¨å¨-å¼å¸¸
notice.solution.110038=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110039=å³é©±å¨å¨-å¼å¸¸
notice.solution.110039=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110040=å³é©±å¨å¨-å¼å¸¸
notice.solution.110040=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110041=å³é©±å¨å¨-å¼å¸¸
notice.solution.110041=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110042=å³é©±å¨å¨-å¼å¸¸
notice.solution.110042=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110043=å³é©±å¨å¨-å¼å¸¸
notice.solution.110043=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110044=å³é©±å¨å¨-å¼å¸¸
notice.solution.110044=é©±å¨å¨å¼å¸¸ï¼éè¦å³æºå¹¶éç½®ä¸å°æ¶ååä½¿ç¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110045=å³é©±å¨å¨-å¼å¸¸
notice.solution.110045=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110046=å³é©±å¨å¨-å¼å¸¸
notice.solution.110046=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110047=å³é©±å¨å¨-å¼å¸¸
notice.solution.110047=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110048=å³é©±å¨å¨-å¼å¸¸
notice.solution.110048=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110049=å³é©±å¨å¨-å¼å¸¸
notice.solution.110049=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110050=å³é©±å¨å¨-å¼å¸¸
notice.solution.110050=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110051=å³é©±å¨å¨-å¼å¸¸
notice.solution.110051=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110052=å³é©±å¨å¨-å¼å¸¸
notice.solution.110052=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110053=å³é©±å¨å¨-å¼å¸¸
notice.solution.110053=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110054=å³é©±å¨å¨-å¼å¸¸
notice.solution.110054=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110055=å³é©±å¨å¨-å¼å¸¸
notice.solution.110055=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110056=å³é©±å¨å¨-å¼å¸¸
notice.solution.110056=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110057=å³é©±å¨å¨-å¼å¸¸
notice.solution.110057=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110058=å³é©±å¨å¨-å¼å¸¸
notice.solution.110058=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110059=å³é©±å¨å¨-å¼å¸¸
notice.solution.110059=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110060=å³é©±å¨å¨-å¼å¸¸
notice.solution.110060=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110061=å³é©±å¨å¨-å¼å¸¸
notice.solution.110061=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110062=å³é©±å¨å¨-å¼å¸¸
notice.solution.110062=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110063=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110063=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110064=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110064=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110065=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110065=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110066=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110066=é©±å¨å¨å¼å¸¸ï¼éè¦å³æºå¹¶éç½®ä¸å°æ¶ååä½¿ç¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110067=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110067=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110068=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110068=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110069=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110069=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110070=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110070=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110071=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110071=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110072=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110072=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110073=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110073=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110074=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110074=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110075=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110075=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110076=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110076=é©±å¨å¨å¼å¸¸ï¼éè¦å³æºå¹¶éç½®ä¸å°æ¶ååä½¿ç¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110077=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110077=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110078=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110078=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110079=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110079=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110080=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110080=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110081=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110081=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110082=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110082=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110083=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110083=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110084=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110084=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110085=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110085=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110086=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110086=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110087=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110087=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110088=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110088=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110089=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110089=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110090=é¡¶åé©±å¨å¨-å¼å¸¸
notice.solution.110090=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110091=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110091=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110092=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110092=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110093=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110093=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110094=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110094=é©±å¨å¨å¼å¸¸ï¼éè¦å³æºå¹¶éç½®ä¸å°æ¶ååä½¿ç¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110095=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110095=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110096=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110096=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110097=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110097=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110098=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110098=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110099=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110099=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110100=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110100=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110101=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110101=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110102=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110102=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110103=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110103=é©±å¨å¨å¼å¸¸ï¼éè¦å³æºå¹¶éç½®ä¸å°æ¶ååä½¿ç¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110104=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110104=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110105=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110105=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110106=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110106=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110107=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110107=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110108=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110108=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110109=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110109=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110110=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110110=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110111=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110111=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110112=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110112=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110113=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110113=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110114=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110114=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110115=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110115=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110116=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110116=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110117=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110117=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110118=æè½¬é©±å¨å¨-å¼å¸¸
notice.solution.110118=é©±å¨å¨å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.110200=çµæºåå§å-å¼å¸¸
notice.solution.110200=è¡èµ°çµæºæ¥æåéå¤±è´¥ï¼å¯è½å ä¸ºè¡èµ°çµæºæªéç½®æ­£ç¡®çåæ°æèæªè¿æ¥ãéå¯è¥æ æ³æ¢å¤è¯·èç³»å®åã
notice.description.110201=çµæºåå§å-å¼å¸¸
notice.solution.110201=åéçµæºæ¥æåéå¤±è´¥ï¼å¯è½å ä¸ºè¡èµ°çµæºæªéç½®æ­£ç¡®çåæ°æèæªè¿æ¥ãéå¯è¥æ æ³æ¢å¤è¯·èç³»å®åã
notice.description.110202=çµæºåå§å-å¼å¸¸
notice.solution.110202=æåçµæºæ¥æåéå¤±è´¥ï¼å¯è½å ä¸ºè¡èµ°çµæºæªéç½®æ­£ç¡®çåæ°æèæªè¿æ¥ãéå¯è¥æ æ³æ¢å¤è¯·èç³»å®åã
notice.description.110203=çµæºåå§å-å¼å¸¸
notice.solution.110203=æè½¬çµæºæ¥æåéå¤±è´¥ï¼å¯è½å ä¸ºè¡èµ°çµæºæªéç½®æ­£ç¡®çåæ°æèæªè¿æ¥ãéå¯è¥æ æ³æ¢å¤è¯·èç³»å®åã
notice.description.110204=çµæºåå§å-å¼å¸¸
notice.solution.110204=å¤¹åçµæºæ¥æåéå¤±è´¥ï¼å¯è½å ä¸ºè¡èµ°çµæºæªéç½®æ­£ç¡®çåæ°æèæªè¿æ¥ãéå¯è¥æ æ³æ¢å¤è¯·èç³»å®åã
notice.description.110205=çµæºåå§å-å¼å¸¸
notice.solution.110205=æ£®ååéçµæºæ¥æåéå¤±è´¥ï¼å¯è½å ä¸ºè¡èµ°çµæºæªéç½®æ­£ç¡®çåæ°æèæªè¿æ¥ãéå¯è¥æ æ³æ¢å¤è¯·èç³»å®åã
notice.description.110206=çµæºåå§å-å¼å¸¸
notice.solution.110206=æ£®åæè½¬çµæºæ¥æåéå¤±è´¥ï¼å¯è½å ä¸ºè¡èµ°çµæºæªéç½®æ­£ç¡®çåæ°æèæªè¿æ¥ãéå¯è¥æ æ³æ¢å¤è¯·èç³»å®åã
notice.description.110207=çµæºåå§å-å¼å¸¸
notice.solution.110207=SRçµæºæ¥æåéå¤±è´¥ï¼å¯è½å ä¸ºè¡èµ°çµæºæªéç½®æ­£ç¡®çåæ°æèæªè¿æ¥ãéå¯è¥æ æ³æ¢å¤è¯·èç³»å®åã
notice.description.110300=çµæºæ§å¶-å¼å¸¸
notice.solution.110300=ä¸åçæè½¬æä»¤è¶è¿èå´ï¼-180~180ãæ¸é¤éè¯¯åéæ°ä¸åå³å¯ã
notice.description.110301=çµæºæ§å¶-å¼å¸¸
notice.solution.110301=ä¸åçæè½¬éåº¦æä»¤è¶è¿èå´ãæ¸é¤éè¯¯åéæ°ä¸åå³å¯ãéåº¦æå¤§8è½¬æ¯åéã
notice.description.110302=çµæºæ§å¶-å¼å¸¸
notice.solution.110302=ä¸åçåéæä»¤è¶è¿èå´ãæ¸é¤éè¯¯åéæ°ä¸åå³å¯ãè¥å¨åçèå´åä¸ç´ä¸è½æåï¼è¯·èç³»å®åã
notice.description.110303=çµæºæ§å¶-å¼å¸¸
notice.solution.110303=ä¸åçåééåº¦æä»¤è¶è¿èå´ãæ¸é¤éè¯¯åéæ°ä¸åå³å¯ãéåº¦10mm/sã
notice.description.110400=çµæºè¿è¡-å¼å¸¸
notice.solution.110400=è§¦åæ¥ååè§£é¤ï¼æèéå¯æºå¨äººåè§å¯æ¯å¦æ¢å¤ï¼å¦åèç³»å®å
notice.description.110401=çµæºè¿è¡-å¼å¸¸
notice.solution.110401=è§¦åæ¥ååè§£é¤ï¼æèéå¯æºå¨äººåè§å¯æ¯å¦æ¢å¤ï¼å¦åèç³»å®å
notice.description.110402=çµæºè¿è¡-å¼å¸¸
notice.solution.110402=è§¦åæ¥ååè§£é¤ï¼æèéå¯æºå¨äººåè§å¯æ¯å¦æ¢å¤ï¼å¦åèç³»å®å
notice.description.110403=çµæºè¿è¡-å¼å¸¸
notice.solution.110403=è§¦åæ¥ååè§£é¤ï¼æèéå¯æºå¨äººåè§å¯æ¯å¦æ¢å¤ï¼å¦åèç³»å®å
notice.description.113001=Canå¡-å¼å¸¸
notice.solution.113001=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113002=Canå¡-å¼å¸¸
notice.solution.113002=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113003=Canå¡-å¼å¸¸
notice.solution.113003=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113004=Canå¡-å¼å¸¸
notice.solution.113004=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113005=Canå¡-å¼å¸¸
notice.solution.113005=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113006=Canå¡-å¼å¸¸
notice.solution.113006=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113007=Canå¡-å¼å¸¸
notice.solution.113007=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113008=Canå¡-å¼å¸¸
notice.solution.113008=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113009=Canå¡-å¼å¸¸
notice.solution.113009=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113010=Canå¡-å¼å¸¸
notice.solution.113010=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113011=Canå¡-å¼å¸¸
notice.solution.113011=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113012=Canå¡-å¼å¸¸
notice.solution.113012=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113013=Canå¡-å¼å¸¸
notice.solution.113013=Canå¡å¼å¸¸ï¼éè¦éå¯æ¢å¤ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.113015=Canå¡-å¼å¸¸
notice.solution.113015=Canå¡å¼å¸¸ï¼å¯è½CANå¡æªè¿æ¥æèè¿æ¥çº¿æ­å¼ãè¥éå¯æ æ³è§£å³ï¼è¯·èç³»å®åã
notice.description.113016=Canå¡-å¼å¸¸
notice.solution.113016=Canå¡å¼å¸¸ï¼å¯è½CANå¡è®¾å¤å¼å¸¸ï¼è¥å¤æ¬¡éå¯æªæ¢å¤ï¼è¯·èç³»å®åã
notice.description.113017=Canå¡-å¼å¸¸
notice.solution.113017=Canå¡å¼å¸¸ï¼å¯è½CANå¡è®¾å¤å¼å¸¸ï¼è¥å¤æ¬¡éå¯æªæ¢å¤ï¼è¯·èç³»å®åã
notice.description.114000=IMU-å¼å¸¸
notice.solution.114000=æ£æ¥ä¸²å£ç«¯å£å·æ¯å¦æ­£ç¡®ã
notice.description.114001=IMU-å¼å¸¸
notice.solution.114001=æ£æ¥ä¸²å£æ¯å¦è¿æ¥æ­£å¸¸ã
notice.description.114002=IMU-å¼å¸¸
notice.solution.114002=æ£æ¥ä¸²å£æ¯å¦è¿æ¥æ­£å¸¸ã
notice.description.114003=IMU-å¼å¸¸
notice.solution.114003=æ£æ¥ä¸²å£æ¯å¦è¿æ¥æ­£å¸¸ï¼ä»¥åå¹²æ°æåµã
notice.description.114004=IMU-å¼å¸¸
notice.solution.114004=æ£æ¥ä¸²å£æ¯å¦è¿æ¥æ­£å¸¸ï¼ä»¥åå¹²æ°æåµã
notice.description.120001=åçµ-å¼å¸¸
notice.solution.120001=å¯è½çµæ± çéä¿¡çº¿è¿æ¥ä¸è¯ï¼æèé´æ­æ§éä¿¡å¤±è´¥ãéæ°æ§è¡ä»»å¡ï¼è¥ä¾æ§åºéè¯·èç³»å®åã
notice.description.120002=åçµ-å¼å¸¸
notice.solution.120002=éå¯éæ°å°è¯åè¥ä¾æ§æ¥éè¯·èç³»å®åã
notice.description.120003=åçµ-å¼å¸¸
notice.solution.120003=éå¯éæ°å°è¯åè¥ä¾æ§æ¥éè¯·èç³»å®åã
notice.description.120004=åçµ-å¼å¸¸
notice.solution.120004=éå¯éæ°å°è¯åè¥ä¾æ§æ¥éè¯·èç³»å®åã
notice.description.120005=åçµ-å¼å¸¸
notice.solution.120005=å¯è½ç¯å¢å ç´ å¯¼è´å¯¹æ¥ä¿¡æ¯ééå¤±è´¥ãè°æ´ç¯å¢è³è¯å¥½æ å¹²æ°çæåµåéæ°å°è¯ä»»å¡ï¼è¥è°æ´åä¾æ§å¤±è´¥è¯·èç³»å®åã
notice.description.120006=åçµ-å¼å¸¸
notice.solution.120006=å¯è½ç¯å¢å ç´ å¯¼è´å¯¹æ¥ä¿¡æ¯ééå¤±è´¥ãè°æ´ç¯å¢è³è¯å¥½æ å¹²æ°çæåµåéæ°å°è¯ä»»å¡ï¼è¥è°æ´åä¾æ§å¤±è´¥è¯·èç³»å®åã
notice.description.120007=åçµ-å¼å¸¸
notice.solution.120007=è¥æ²¡è¯å¥½å¯¹æ¥åçµæ¡©ï¼è¯·è°æ´å¯¹æ¥åæ°ãç¡®ä¿åçµæ¡©å¤äºèªå¨æ¨¡å¼ã
notice.description.120008=åçµ-å¼å¸¸
notice.solution.120008=éä½åæ»¡ç¾åæ¯ï¼é»è®¤ä¸º97%ãè¥éä½å°89%ä¾æ§è¿æé®é¢ï¼è¯·èç³»å®åã
notice.description.120100=bms-å¼å¸¸
notice.solution.120100=æ£æ¥ä¸²å£æ¯å¦æ­£å¸¸
notice.description.120101=bms-å¼å¸¸
notice.solution.120101=æ£æ¥è¯»åæä»¤æ°æ®æ¯å¦æ­£ç¡®ï¼æ£æ¥çµæ± éä¿¡åè®®ã
notice.description.120102=bms-å¼å¸¸
notice.solution.120102=æ£æ¥åå¥æä»¤æ°æ®æ¯å¦æ­£ç¡®ï¼æ£æ¥çµæ± éä¿¡åè®®ã
notice.description.120103=bms-å¼å¸¸
notice.solution.120103=æ£æ¥ä¸²å£æ¯å¦æ­£å¸¸ã
notice.description.120104=bms-å¼å¸¸
notice.solution.120104=æ£æ¥ä¸²å£æ¯å¦æ­£å¸¸ã
notice.description.120106=bms-å¼å¸¸
notice.solution.120106=æ£æ¥ä¸²å£æ°æ®æ¯å¦å¹²æ°ã
notice.description.120107=bms-å¼å¸¸
notice.solution.120107=æ£æ¥çµæ± æ°é
notice.description.120108=bms-å¼å¸¸
notice.solution.120108=æ£æ¥åçµæ± çµå
notice.description.121001=é³é¢-å¼å¸¸
notice.solution.121001=ç¡®è®¤ææå®æ­æ¾çé³é¢åå­å­å¨ä¸AGVåï¼æ³¨æé³é¢åå­æ«å°¾ä¸è¦å åç¼ï¼å¦.mp3ãç¡®ä¿é³é¢ä¸ºmp3æ ¼å¼ã
notice.description.123004=Socket-å¼å¸¸
notice.solution.123004=éæ°ç¡®è®¤APIç«¯å£å·ä¸æ¥å£å·æ¯å¦æ­£ç¡®
notice.description.123005=Socket-å¼å¸¸
notice.solution.123005=éè¦éå¯æ¢å¤ï¼å¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.123006=Socket-å¼å¸¸
notice.solution.123006=æ æ³è·åéç½®ä¿¡æ¯ï¼è¯·èç³»å®å
notice.description.127001=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127001=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶èç³»å®åã
notice.description.127002=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127002=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶ä¸äººå·¥å¤æ­æºå¨äººæ¯å¦è±è½¨ï¼å¦æè±è½¨ï¼è¯·å°æºå¨äººç§»å¨å°è·¯å¾ä¸ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127003=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127003=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶ä¸äººå·¥å¤æ­å®ä½æ°æ®æ¯å¦å­å¨ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127004=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127004=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶ä¸äººå·¥å¤æ­è´§æ¶ä¸æ¹æ è®°ç©æ¯å¦è¯å«æ­£å¸¸ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127005=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127005=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶ä¸å¤æ­æ¯å¦å­å¨é·è¾¾ä»¥åpclç¸å³æ¥éãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127006=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127006=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶ä¸å¤æ­æ¯å¦å­å¨çµæºç¸å³æ¥éãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127007=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127007=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶ä¸å¤æ­æ¯å¦å­å¨å®ä½ç¸å³æ¥éï¼å¦ææ²¡æï¼æ¿åå®ä½æåµä¸è¯·å¤æ­æ¯å¦å­å¨é·è¾¾ç¸å³æ¥éï¼äºç»´ç å®ä½å¤æ­æ¯å¦å­å¨äºç»´ç ä¼ æå¨éè®¯å¼å¸¸ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127008=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127008=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¤æ­æ¿åé·è¾¾æ°æ®æ¯å¦æ­£å¸¸ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127009=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127009=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å½åè·¯å¾å¨é­äººå·¥ç¹å¾æ¯å¦å­å¨è¢«é®æ¡çæåµï¼å¦æå­å¨ï¼è¯·é¿åé®æ¡ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127010=å¯¼èªæ§è¡å¼å¸¸
notice.solution.127010=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶ä¸å¤æ­å½åæ¿åå®ä½æ°æ®æ¯å¦æ­£å¸¸ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.127011=å¯¼èªæ§è¡å¼å¸¸111
notice.solution.127011=å¯¼èªæ§è¡å¼å¸¸ï¼éè¦åæ­¢å½åä»»å¡ï¼å¹¶ä¸å¤æ­å½åæ¿åå®ä½æ°æ®æ¯å¦æ­£å¸¸ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã222
notice.description.127012=å®åæ²çº¿è§åº¦ååèå´è¿å¤§
notice.solution.127012=è¯·åå°è·¯å¾å¼¯æ²ç¨åº¦ï¼ä½¿è·¯å¾æ´å¹³æ»
notice.description.128001=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128001=å¯¹æ¥æ§è¡å¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶ä¸å¤æ­å½åæºå¨äººæ¯å¦å¨æ§è¡å¯¹æ¥ï¼æèæ¯å¦å¨ä¹åæ§è¡å¯¹æ¥æä»¤åï¼æªæ§è¡è±ç¦»å¯¹æ¥ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.128002=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128002=å¯¹æ¥æ§è¡å¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶ä¸å¤æ­å½åæå®çå¯¹æ¥ç®æ æ¯å¦åçãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.128003=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128003=å¯¹æ¥æ§è¡å¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶ä¸å¤æ­ç¹å¾æ£æµæ¨¡åè¿è¡ç¶æãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.128004=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128004=å¯¹æ¥æ§è¡å¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶ä¸å¤æ­ç¹å¾æ£æµæ¨¡åè¿è¡ç¶æãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.128005=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128005=å¯¹æ¥æ§è¡å¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶ä¸å¤æ­ç¹å¾æ£æµæ¨¡åè¿è¡ç¶æãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.128006=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128006=å¯¹æ¥æ§è¡å¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶è¯·èç³»å®åã
notice.description.128007=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128007=å¯¹æ¥æ§è¡å¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶ä¸å¤æ­ç¹å¾æ£æµæ¨¡åè¿è¡ç¶æãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.128008=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128008=å¯¹æ¥æ§è¡å¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶ä¸å¤æ­å½åæºå¨äººæ¯å¦å¨æ§è¡è±ç¦»å¯¹æ¥ï¼æèæ¯å¦å¨ä¹åæªæ§è¡å¯¹æ¥å¨ä½ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.128009=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128009=éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶ä¸å¤æ­æ¯å¦å­å¨å®ä½ç¸å³æ¥éï¼å¦ææ²¡æï¼è¯·å¤æ­æ¯å¦å­å¨é·è¾¾ç¸å³æ¥éãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.128010=å¯¹æ¥æ§è¡å¼å¸¸
notice.solution.128010=å¯¹æ¥æ§è¡å¼å¸¸ï¼éæ¸æ¥éè¯¯ç¶æï¼å¹¶ä¸å¤æ­ç¹å¾æ£æµè¿è¡ç¶æãå¦ææ æ³æ¢å¤æå¼å¸¸åºç°å¤æ¬¡è¯·èç³»å®å
notice.description.128011=äºç»´ç è´§æ¶å¯¹åå¼å¸¸
notice.solution.128011=äºç»´ç è´§æ¶å¯¹åå¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶å¤æ­æ¯å¦å¨äºç»´ç è´§æ¶å¯¹åè¿ç¨ä¸­åæ§è¡äºä¸æ¬¡å¯¹å
notice.description.128012=äºç»´ç è´§æ¶å¯¹åå¼å¸¸
notice.solution.128012=äºç»´ç è´§æ¶å¯¹åå¼å¸¸ï¼æ£æ¥æ¯å¦ç§å°äºç»´ç ï¼è¥ç§å°æ£æ¥æ¯å¦å¼å¯äºç»´ç è¯å«èç¹ï¼ä»¥åèç¹åæ°æ¯å¦éç½®æ­£ç¡®
notice.description.128100=ä¾§é¢å¯¹åå¼å¸¸
notice.solution.128100=ä¾§é¢å¯¹åå¼å¸¸ï¼éè¦æ¸é¤éè¯¯ç¶æï¼å¹¶å¤æ­æ¯å¦å¨ä¾§é¢å¯¹åè¿ç¨ä¸­åæ§è¡äºä¸æ¬¡å¯¹å
notice.description.128101=ä¾§é¢å¯¹åå¼å¸¸
notice.solution.128101=æ£æ¥ä¾§é¢å¯¹æ¥ä¼ æå¨ä¸æºå°çè·ç¦»æ¯å¦å¨éå¼èå´å
notice.description.130001=å®ä½å¼å¸¸
notice.solution.130001=è¯·å°è¯éå®ä½ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.133001=ç¹å¾æ£æµæ§è¡å¼å¸¸
notice.solution.133001=ç¹å¾æ£æµæ§è¡å¼å¸¸ï¼äººå·¥å¤æ­æºå¨äººæ¯å¦å¨å¯¹æ¥èå´åï¼ç¹å¾æ¯å¦ä¸æºå¨äººé·è¾¾é«åº¦ä¸è´ãå¦ææ æ³æ¢å¤æèå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.133002=ç¹å¾æ£æµæ§è¡å¼å¸¸
notice.solution.133002=ç¹å¾æ£æµæ§è¡å¼å¸¸ï¼äººå·¥å¤æ­æ¯å¦æç¸ä¼¼ç¹å¾ï¼è°æ´æºå¨äººå¯¹æ¥è·ç¦»ææ¹åãå¦ææ æ³åå¤æå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.133003=ç¹å¾æ£æµæ§è¡å¼å¸¸
notice.solution.133003=ç¹å¾æ£æµæ§è¡å¼å¸¸ï¼å¤æ­æ¯å¦å­å¨æ¿åé·è¾¾ç¸å³æ¥éãå¦ææ æ³æ¢å¤æå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.133004=ç¹å¾æ£æµæ§è¡å¼å¸¸
notice.solution.133004=ç¹å¾æ£æµæ§è¡å¼å¸¸ï¼å¤æ­æ¯å¦å­å¨å®ä½ç¸å³æ¥éãå¦ææ æ³æ¢å¤æå¼å¸¸åºç°å¤æ¬¡è¯·èç³»å®åã
notice.description.135001=å»ºå¾æ§è¡å¼å¸¸
notice.solution.135001=å»ºå¾æ§è¡å¼å¸¸ï¼å¤æ­æ¯å¦å­å¨æ¿åé·è¾¾ç¸å³æ¥éãå¦ææ æ³æ¢å¤æå¼å¸¸å¤æ¬¡åºç°è¯·èç³»å®åã
notice.description.135002=å»ºå¾æ§è¡å¼å¸¸
notice.solution.135002=å»ºå¾æ§è¡å¼å¸¸ï¼äººå·¥å¤æ­ç»å¾è¿ç¨æ¯å¦å½¢æåç¯ãå¦æä¸ç´æ²¡æåç¯è¯·èç³»å®åã
notice.description.140000=èæ¬å¼å¸¸
notice.solution.140000=èæ¬å¼å¸¸ï¼è¯·æ£æ¥socketæèluaæä»¤çåæ°ï¼ä¾å¦ï¼é¿åº¦ãç±»åç­ã
notice.description.140001=èæ¬å¼å¸¸
notice.solution.140001=èæ¬å¼å¸¸ï¼æå®æ§å¶çèæ¬ä¸å­å¨ï¼è¯·æ£æ¥èæ¬çåå­æèIDã
notice.description.140002=èæ¬å¼å¸¸
notice.solution.140002=èæ¬å¼å¸¸ï¼èæ¬æ­£å¨è¿è¡ä¸­ï¼ä¸è¿è¡ç¨æ·å½åçæ§å¶æä»¤ã
notice.description.140003=èæ¬å¼å¸¸
notice.solution.140003=èæ¬å¼å¸¸ï¼èæ¬å­çº¿ç¨æ²¡ææ­£å¸¸éåºï¼è¿ç§æåµå±äºè´å½BUGï¼è¯·èç³»å¼åäººå
notice.description.140004=èæ¬å¼å¸¸
notice.solution.140004=èæ¬å¼å¸¸ï¼å¯å¨èæ¬è¶æ¶ï¼æ£æµèæ¬å­æ¾è·¯å¾æ¯å¦æ­£ç¡®ãå¦æ£æµæ è¯¯ï¼è¯·èç³»å¼åäººåã
notice.description.140005=èæ¬å¼å¸¸
notice.solution.140005=èæ¬å¼å¸¸ï¼åæ­¢èæ¬è¶æ¶ï¼æ£æµèæ¬æ¯å¦å·²ç»éåºãå¦æ£æµæ è¯¯ï¼è¯·èç³»å¼åäººåã
notice.description.140006=èæ¬å¼å¸¸
notice.solution.140006=èæ¬å¼å¸¸ï¼ä¸åçæ§å¶æä»¤ä¸å­å¨ï¼è¯·æ£æ¥jsonæä»¤çcommandã
notice.description.140007=èæ¬å¼å¸¸
notice.solution.140007=èæ¬å¼å¸¸ï¼æå®çèæ¬åéå°åéè¯¯ï¼è¯·æ£æ¥ä¸åçèæ¬åéçå°åæ¯å¦ä¸å¨æå®èå´åã0ï¼31ãã
notice.description.140008=èæ¬å¼å¸¸
notice.solution.140008=èæ¬å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.140009=èæ¬å¼å¸¸
notice.solution.140009=èæ¬å¼å¸¸ï¼è¯·æ£æ¥luaèæ¬ä¸­æ¯å¦æmainå½æ°
notice.description.140010=èæ¬å¼å¸¸
notice.solution.140010=èæ¬å¼å¸¸ï¼è¯·æ£æ¥luaèæ¬ä¸­æ¯å¦æexceptionå½æ°
notice.description.140011=èæ¬å¼å¸¸
notice.solution.140011=èæ¬å¼å¸¸ï¼è¯·æ£æ¥luaèæ¬ä¸­æ¯å¦æcancelå½æ°
notice.description.140012=èæ¬å¼å¸¸
notice.solution.140012=èæ¬å¼å¸¸ï¼è¿ç§æåµè¦æ£æ¥éç½®æä»¶
notice.description.140013=èæ¬å¼å¸¸
notice.solution.140013=èæ¬å¼å¸¸ï¼ç¨æ·ä¼ è¾çjsonæé®é¢ï¼æ£æ¥ä»¥ä¸jsonæ°æ®å¹¶èç³»å¼åäººå
notice.description.140014=èæ¬å¼å¸¸
notice.solution.140014=èæ¬å¼å¸¸ï¼ç¨æ·ä¼ è¾çjsonæé®é¢ï¼æ£æ¥ä»¥ä¸jsonæ°æ®å¹¶èç³»å¼åäººå
notice.description.140015=èæ¬å¼å¸¸
notice.solution.140015=èæ¬å¼å¸¸ï¼æ ¸å¯¹æ¥å£è¦æ±çåéç±»åã
notice.description.140016=èæ¬å¼å¸¸
notice.solution.140016=èæ¬å¼å¸¸ï¼ 1.æ£æ¥ç®æ ç¹æ¯å¦å­å¨ï¼ 2.æ£æ¥å½åä½ç½®æ¯å¦æè·¯å¾éå¾ç®æ ç¹ï¼ 3.æ£æ¥æºå¨äººå®ä½æ¯å¦å¤±æï¼ è¥ä¸æ¯èç³»å¼åäººåã
notice.description.140017=èæ¬å¼å¸¸
notice.solution.140017=èæ¬å¼å¸¸ï¼ 1.æ£æ¥ç®æ ç¹æ¯å¦å­å¨ï¼ 2.æ£æ¥å½åä½ç½®æ¯å¦æè·¯å¾éå¾ç®æ ç¹ï¼ 3.æ£æ¥æºå¨äººå®ä½æ¯å¦å¤±æï¼ è¥ä¸æ¯èç³»å¼åäººåã
notice.description.140018=èæ¬å¼å¸¸
notice.solution.140018=èæ¬å¼å¸¸ï¼compassä¸åçå­æ®µæé®é¢ï¼è¯·èç³»å¼åäººåã
notice.description.140019=èæ¬å¼å¸¸
notice.solution.140019=èæ¬å¼å¸¸ï¼compassçsocketæå¡æ­å¼è¿æ¥ã
notice.description.140020=èæ¬å¼å¸¸
notice.solution.140020=èæ¬å¼å¸¸ï¼ç´çº¿è¿å¨åºéï¼è¯·èç³»å¼åäººåã
notice.description.140021=èæ¬å¼å¸¸
notice.solution.140021=èæ¬å¼å¸¸ï¼æ£æ¥ç¯å¢æ¯å¦æ»¡è¶³å¯¹æ¥æ¡ä»¶ï¼è¯·èç³»å¼åäººåã
notice.description.140022=èæ¬å¼å¸¸
notice.solution.140022=èæ¬å¼å¸¸ï¼æ£æ¥ç®æ³èç¹æ¯å¦å·²ç»å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.140023=èæ¬å¼å¸¸
notice.solution.140023=èæ¬å¼å¸¸ï¼æ£æ¥èæ¬çæ ¼å¼æ¯å¦æ­£ç¡®ï¼è¯·èç³»å¼åäººåã
notice.description.140024=èæ¬å¼å¸¸
notice.solution.140024=èæ¬å¼å¸¸ï¼èæ¬æ§è¡è¿ç¨ä¸­åç°å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.140025=èæ¬å¼å¸¸
notice.solution.140025=èæ¬å¼å¸¸ï¼èæ¬æ§è¡è·¯å¾å¯¼èªå¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.140026=èæ¬å¼å¸¸
notice.solution.140026=èæ¬å¼å¸¸ï¼callæ°çèæ¬å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.140027=èæ¬å¼å¸¸
notice.solution.140027=èæ¬å¼å¸¸ï¼è¯·æ­£ç¡®ç¼è¾èæ¬çåç¼åä¸ºâ.luaâã
notice.description.140028=èæ¬å¼å¸¸
notice.solution.140028=èæ¬å¼å¸¸ï¼æªç¥å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.145000=èæ¬å¼å¸¸
notice.solution.145000=èæ¬å¼å¸¸ï¼è¿æ¥æºæ¢°èè¶æ¶ï¼è¯·æ£æµæºæ¢°èæ¯å¦å·²ç»ä¸çµãç½çº¿æ¯å¦æ­£å¸¸ã
notice.description.145001=èæ¬å¼å¸¸
notice.solution.145001=èæ¬å¼å¸¸ï¼æºæ¢°èæ²¡æå»ºç«è¿æ¥ï¼è¯·æ£æµæºæ¢°èæ¯å¦å·²ç»ä¸çµãç½çº¿æ¯å¦æ­£å¸¸ã
notice.description.145002=èæ¬å¼å¸¸
notice.solution.145002=èæ¬å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.145003=èæ¬å¼å¸¸
notice.solution.145003=èæ¬å¼å¸¸ï¼æ§å¶æºæ¢°èè¾å¥çåæ°éè¯¯ï¼è¯·æ ¹æ®åè®®æ£æ¥åæ°ã
notice.description.145004=èæ¬å¼å¸¸
notice.solution.145004=èæ¬å¼å¸¸ï¼æºæ¢°èè¿åçæ¶æ¯éè¯¯ï¼è¯·èç³»å¼åäººåã
notice.description.145005=èæ¬å¼å¸¸
notice.solution.145005=èæ¬å¼å¸¸ï¼ä¸åæ§å¶æºæ¢°èçæä»¤éè¯¯ï¼è¯·æ ¹æ®åè®®æ£æ¥ä¸åçæä»¤ã
notice.description.145006=èæ¬å¼å¸¸
notice.solution.145006=èæ¬å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.145007=èæ¬å¼å¸¸
notice.solution.145007=èæ¬å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.146000=èæ¬å¼å¸¸
notice.solution.146000=èæ¬å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.146001=èæ¬å¼å¸¸
notice.solution.146001=èæ¬å¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.147001=å£°åç³»ç»-å¼å¸¸
notice.solution.147001=éå¯åè¥ä¾æ§æ æ³è§£å³ï¼åèç³»å®å
notice.description.147002=åç¹é·è¾¾-å¼å¸¸
notice.solution.147002=éå¯åè¥ä¾æ§æ æ³è§£å³ï¼åèç³»å®å
notice.description.147004=ä¾§é¢åçµå¯¹æ¥-å¼å¸¸
notice.solution.147004=æ£æ¥ç½ç»IPå°åã
notice.description.150000=é·è¾¾å¼å¸¸
notice.solution.150000=æ£æ¥ç½ç»ç«¯å£åipå°åã
notice.description.150002=é·è¾¾å¼å¸¸
notice.solution.150002=æ£æ¥è®¾ç½®é¢çæ¯å¦å¨åçèå´ã
notice.description.150003=é·è¾¾å¼å¸¸
notice.solution.150003=æ£æ¥è®¾ç½®éæ ·çæ¯å¦å¨åçèå´ã
notice.description.150004=é·è¾¾å¼å¸¸
notice.solution.150004=æ£æ¥ç½ç»æ¯å¦æ­£å¸¸ã
notice.description.150005=é·è¾¾å¼å¸¸
notice.solution.150005=æ£æ¥ç½ç»æ¯å¦æ­£å¸¸ã
notice.description.150100=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150100=æ£æ¥ç½ç»ipå°ååç«¯å£å·ã
notice.description.150101=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150101=æ£æ¥ç½ç»ipå°ååç«¯å£å·ã
notice.description.150102=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150102=æ£æ¥ç½ç»æ¯å¦æ­£å¸¸ã
notice.description.150103=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150103=æ£æ¥ç½ç»æ¯å¦æ­£å¸¸ã
notice.description.150104=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150104=æ£æ¥æ¯å¦ææ¥åä¿¡å·è§¦åã
notice.description.150151=å®å¨PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150151=æ£æ¥ç½ç»ipå°ååç«¯å£å·ã
notice.description.150152=å®å¨PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150152=æ£æ¥ç½ç»æ¯å¦æ­£å¸¸ã
notice.description.150153=å®å¨PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150153=æ£æ¥ç½ç»æ¯å¦æ­£å¸¸ã
notice.description.150154=å®å¨PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150154=æ£æ¥æ¯å¦ææ¥åä¿¡å·è§¦åã
notice.description.150155=å®å¨PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.150155=æ£æ¥ç¼ç å¨æ¥è­¦ã
notice.description.150300=äºç»´ç å¼å¸¸
notice.solution.150300=æ£æ¥ä¸²å£ç«¯å£å·æ¯å¦æ­£ç¡®ã
notice.description.150301=äºç»´ç å¼å¸¸
notice.solution.150301=æ£æ¥ä¸²å£æ¯å¦è¿æ¥æ­£å¸¸ã
notice.description.150302=äºç»´ç å¼å¸¸
notice.solution.150302=æ£æ¥ä¸²å£æ¯å¦è¿æ¥æ­£å¸¸ã
notice.description.150303=äºç»´ç å¼å¸¸
notice.solution.150303=æ£æ¥ä¸²å£æ¯å¦è¿æ¥æ­£å¸¸ï¼ä»¥åå¹²æ°æåµã
notice.description.150304=äºç»´ç å¼å¸¸
notice.solution.150304=æ£æ¥ä¸²å£æ¯å¦è¿æ¥æ­£å¸¸ï¼ä»¥åå¹²æ°æåµã
notice.description.150310=äºç»´ç å¼å¸¸
notice.solution.150310=æ£æ¥ç¸æºæ¯å¦è¿æ¥æ­£å¸¸
notice.description.150311=äºç»´ç å¼å¸¸
notice.solution.150311=æ£æ¥ç¸æºæ¯å¦è¿æ¥æ­£å¸¸
notice.description.150312=äºç»´ç å¼å¸¸
notice.solution.150312=æ£æ¥ç¸æºæ¯å¦è¿æ¥æ­£å¸¸
notice.description.150313=äºç»´ç å¼å¸¸
notice.solution.150313=æ£æ¥ç¸æºæ¯å¦è¿æ¥æ­£å¸¸
notice.description.150400=3Dç¸æºå¼å¸¸
notice.solution.150400=æ£æ¥ç¸æºæ¯å¦è¿æ¥æ­£å¸¸ã
notice.description.150401=3Dç¸æºå¼å¸¸
notice.solution.150401=æ£æ¥ç¸æºæ¯å¦éç½®æ­£å¸¸ã
notice.description.150500=è¶å£°æ³¢å¼å¸¸
notice.solution.150500=æ£æ¥è¶å£°æ³¢æ¯å¦è¿æ¥æ­£å¸¸ã
notice.description.150501=è¶å£°æ³¢å¼å¸¸
notice.solution.150501=æ£æ¥è¶å£°æ³¢æ¯å¦éç½®æ­£å¸¸ã
notice.description.170001=ä¸éæå¼å¸¸
notice.solution.170001=ä¸éæå¼å¸¸ï¼è¯·èç³»å¼åäººåã
notice.description.170002=ä¸éæå¼å¸¸
notice.solution.170002=ä¸éææ¥è­¦ï¼è¯·æç§ä¸éææä½æåå¤ä½ã
notice.description.170003=ä¸éæå¼å¸¸
notice.solution.170003=ä¸éæå¼å¸¸ï¼è¯·å¤ä½é©±å¨å¨æ¸é¤æéï¼æèæ­çµéå¯ã
notice.description.170004=ä¸éæå¼å¸¸
notice.solution.170004=ä¸éæå¼å¸¸ï¼è¯·å¤ä½é©±å¨å¨æ¸é¤æéï¼æèæ­çµéå¯ã
notice.description.170005=ä¸éæå¼å¸¸
notice.solution.170005=ä¸éæå¼å¸¸ï¼è¯·å¤ä½é©±å¨å¨æ¸é¤æéï¼æèæ­çµéå¯ã
notice.description.170006=ä¸éæå¼å¸¸
notice.solution.170006=ä¸éæå¼å¸¸ï¼è¯·å¤ä½é©±å¨å¨æ¸é¤æéï¼æèæ­çµéå¯ã
notice.description.170007=ä¸éæå¼å¸¸
notice.solution.170007=ä¸éæå¼å¸¸ï¼è¯·å¤ä½é©±å¨å¨æ¸é¤æéï¼æèæ­çµéå¯ã
notice.description.170008=ä¸éæå¼å¸¸
notice.solution.170008=ä¸éæå¼å¸¸ï¼æ¾å¼æ¥åå¯æ¢å¤
notice.description.171001=æµ·åº·äºå°ï¼ä¸éæï¼å¼å¸¸
notice.solution.171001=æµ·åº·äºå°å¼å¸¸: 1.æ£æ¥ç½çº¿æ¯å¦æ¾å¨ 2.æ£æ¥ç½ç»éä¿¡æ¯å¦æ­£å¸¸
notice.description.171002=å£°çº³ä¼ æå¨ï¼ä¸éæï¼å¼å¸¸
notice.solution.171002=å£°çº³ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.171003=é²è·è½ä¼ æå¨å¼å¸¸
notice.solution.171003=é²è·è½ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.171004=é²è·è½ä¼ æå¨å¼å¸¸
notice.solution.171004=é²è·è½ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.171005=é²è·è½ä¼ æå¨å¼å¸¸
notice.solution.171005=é²è·è½ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.171006=é²è·è½ä¼ æå¨å¼å¸¸
notice.solution.171006=é²è·è½ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.171007=è¶å£°æ³¢ä¼ æå¨ï¼ä¸éæï¼å¼å¸¸
notice.solution.171007=è¶å£°æ³¢ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.171008=è¶å£°æ³¢ä¼ æå¨ï¼ä¸éæï¼å¼å¸¸
notice.solution.171008=è¶å£°æ³¢ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.171009=è¶å£°æ³¢ä¼ æå¨ï¼ä¸éæï¼å¼å¸¸
notice.solution.171009=è¶å£°æ³¢ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.171010=è¶å£°æ³¢ä¼ æå¨ï¼ä¸éæï¼å¼å¸¸
notice.solution.171010=è¶å£°æ³¢ä¼ æå¨å¼å¸¸:è¯·æ£æ¥è®¾å¤è¿æ¥æ¯å¦æ­£å¸¸
notice.description.200017=æ¾ä¸å°å·²ä¸åç»Pilotçæä»¤
notice.solution.200017=è¯·èç³»ææ¯æ¯æäººå
notice.description.200018=æä»¤æ§è¡å¤±è´¥
notice.solution.200018=è¯·èç³»ææ¯æ¯æäººå
notice.description.200101=æé®æ¥å
notice.solution.200101=è¯·æ£æ¥æºå¨äººç¶æ
notice.description.200102=å®å¨è®¾å¤æ¥å
notice.solution.200102=è¯·æ£æ¥æºå¨äººç¶æ
notice.description.200103=ç¢°ææ¥å
notice.solution.200103=è¯·æ£æ¥æºå¨äººç¶æ
notice.description.200104=è·¯å¾å¯¼èªæ¥åå¼å¸¸
notice.solution.200104=è¯·æ£æ¥æºå¨äººç¶æ
notice.description.200105=æºå¨äººåéæ¥å
notice.solution.200105=æºå¨äººåéæ¥å
notice.description.200106=æºå¨äººåééè¯¯
notice.solution.200106=æºå¨äººåééè¯¯
notice.description.200107=æºå¨äººæ»ç­æ¥å
notice.solution.200107=æºå¨äººæ»ç­æ¥å
notice.description.200108=æºå¨äººæ»ç­éè¯¯
notice.solution.200108=æºå¨äººæ»ç­éè¯¯
notice.description.200109=æºå¨äººå¼å¯æå
notice.solution.200109=æºå¨äººå¼å¯æå
notice.description.200110=æºå¨äººæå¨æ§å¶æ¨¡å¼
notice.solution.200110=æºå¨äººæå¨æ§å¶æ¨¡å¼
notice.description.200111=æºå¨äººæªå®ä½
notice.solution.200111=æºå¨äººæªå®ä½
notice.description.200112=è¯¥æºå¨äººçæ§å¶æ¨¡å¼åªæ¯æç¡¬ä»¶æé®æ§å¶ï¼
notice.solution.200112=è¯¥æºå¨äººçæ§å¶æ¨¡å¼åªæ¯æç¡¬ä»¶æé®æ§å¶ï¼
notice.description.200113=æºæ¢°èå¤äºæªå°±ç»ª
notice.solution.200113=æ£æ¥æºå¨èç¶æ
notice.description.200114=æºå¨äººå¼å¸¸
notice.solution.200114=æ£æ¥æºå¨äººç¶æ
notice.description.200120=ä»¿çä¸æ¯æè¯¥æä»¤
notice.solution.200120=æ£æ¥æºå¨äººä¸åçæä»¤
notice.description.300001=ç³»ç»åé¨éè¯¯
notice.solution.300001=è¯·èç³»ææ¯æ¯æäººå
notice.description.300002=ç»è®¡æ¨¡åç¨åºå¼å¸¸
notice.solution.300002=è¯·èç³»ææ¯æ¯æäººå
notice.description.300003=å°å¾æ¨¡åç¨åºå¼å¸¸
notice.solution.300003=è¯·èç³»ææ¯æ¯æäººå
notice.description.300004=æºå¨äººæ¨¡åç¨åºå¼å¸¸
notice.solution.300004=è¯·èç³»ææ¯æ¯æäººå
notice.description.300005=ä»»å¡æ¨¡åç¨åºå¼å¸¸
notice.solution.300005=è¯·èç³»ææ¯æ¯æäººå
notice.description.300006=äº¤ç®¡æ¨¡åç¨åºå¼å¸¸
notice.solution.300006=è¯·èç³»ææ¯æ¯æäººå
notice.description.300007=äºä»¶æ¨¡åç¨åºå¼å¸¸
notice.solution.300007=è¯·èç³»ææ¯æ¯æäººå
notice.description.300008=é£æ·é¨æ¨¡åå¼å¸¸
notice.solution.300008=è¯·èç³»ææ¯æ¯æäººå
notice.description.300009=èªå¨é¨æ¨¡åå¼å¸¸
notice.solution.300009=è¯·èç³»ææ¯æ¯æäººå
notice.description.300010=çµæ¢¯æ¨¡åå¼å¸¸
notice.solution.300010=è¯·èç³»ææ¯æ¯æäººå
notice.description.300011=å¼å«çæ¨¡åå¼å¸¸
notice.solution.300011=è¯·èç³»ææ¯æ¯æäººå
notice.description.300012=å¼å¸¸
notice.solution.300012=è¯·èç³»ææ¯æ¯æäººå
notice.description.300013=CPUèµæºå ç¨è¿é«
notice.solution.300013=è¯·èç³»ææ¯æ¯æäººå
notice.description.300014=åå­èµæºå ç¨è¿é«
notice.solution.300014=è¯·èç³»ææ¯æ¯æäººå
notice.description.300015=ç¡¬çèµæºå ç¨è¿é«
notice.solution.300015=è¯·èç³»ææ¯æ¯æäººå
notice.description.300016=åçµæ¡©ç¶ææ¨¡åç¨åºå¼å¸¸
notice.solution.300016=è¯·èç³»ææ¯æ¯æäººå
notice.description.300101=æºå¨äººå ç¨ç¹ä½å¤±è´¥
notice.solution.300101=æºå¨äººæå¨ç¹ä½å·²è¢«å¶å®æºå¨äººå ç¨ï¼è¯·ç§»å¨æºå¨äººå°è·¯ç½ä¸çº¿
notice.description.300102=æºå¨äººå ç¨åºåå¤±è´¥
notice.solution.300102=æºå¨äººæå¨åæºåºåå·²è¢«å¶å®æºå¨äººå ç¨ï¼è¯·ç§»å¨æºå¨äººå°è·¯ç½ä¸çº¿
notice.description.300103=æºå¨äººå ç¨çµæ¢¯å¤±è´¥
notice.solution.300103=æºå¨äººæå¨çµæ¢¯å·²è¢«å¶å®æºå¨äººå ç¨ï¼è¯·ç§»å¨æºå¨äººå°è·¯ç½ä¸çº¿
notice.description.300104=æºå¨äººå·²æ­çº¿
notice.solution.300104=1ï¼è¯·æ£æ¥æºå¨äººåæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥æºå¨äººççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·æ£æ¥æºå¨äººéç½®çç³»ç»IPåç«¯å£æ¯å¦æ­£ç¡®ï¼
notice.description.300105=æºå¨äººè±ç¦»è½¨é
notice.solution.300105=è¯·æ£æ¥æºå¨äººçå®ä½ç¶ææ¯å¦åç¡®ï¼å¦æåç¡®ï¼è¯·ç§»å¨æºå¨äººå°è·¯ç½ä¸çº¿
notice.description.300106=æºå¨äººæå¨æ§å¶æ¨¡å¼
notice.solution.300106=æºå¨äººå¤äºæå¨æ§å¶æ¨¡å¼ï¼è¯·åæ¢æèªå¨æ§å¶æ¨¡å¼
notice.description.300107=æºå¨äººæ£ä¿®æ§å¶æ¨¡å¼
notice.solution.300107=æºå¨äººå¤äºæ£ä¿®æ§å¶æ¨¡å¼ï¼è¯·åæ¢æèªå¨æ§å¶æ¨¡å¼
notice.description.300201=èªå¨é¨è¿æ¥å¤±è´¥
notice.solution.300201=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾å¤IPãç«¯å£å·éç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300202=é£æ·é¨è¿æ¥å¤±è´¥
notice.solution.300202=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾å¤IPãç«¯å£å·éç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300203=çµæ¢¯è¿æ¥å¤±è´¥
notice.solution.300203=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾å¤IPãç«¯å£å·éç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300204=è¯»åèªå¨é¨æä»¤å¤±è´¥
notice.solution.300204=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾å¤è¯»åå°åéç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300205=è¯»åé£æ·é¨æä»¤å¤±è´¥
notice.solution.300205=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾å¤è¯»åå°åéç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300206=è¯»åçµæ¢¯æä»¤å¤±è´¥
notice.solution.300206=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾å¤è¯»åå°åéç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300207=åå¥èªå¨é¨æä»¤å¤±è´¥
notice.solution.300207=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾åå¥å°åéç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300208=åå¥é£æ·é¨æä»¤å¤±è´¥
notice.solution.300208=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾åå¥å°åéç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300209=åå¥çµæ¢¯æä»¤å¤±è´¥
notice.solution.300209=1ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥è®¾å¤ççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·å¨å°å¾ç¼æçé¢æ£æ¥è®¾åå¥å°åéç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300210=èªå¨é¨æªç»å®è·¯å¾
notice.solution.300210=è¯·æå¼å°å¾ç¼è¾çé¢ï¼ä¸ºè¯¥èªå¨é¨ç»å®è·¯å¾
notice.description.300211=é£æ·é¨æªç»å®è·¯å¾
notice.solution.300211=è¯·æå¼å°å¾ç¼è¾çé¢ï¼ä¸ºè¯¥é£æ·é¨ç»å®è·¯å¾
notice.description.300212=çµæ¢¯æªç»å®ç¹ä½
notice.solution.300212=è¯·æå¼å°å¾ç¼è¾çé¢ï¼ä¸ºè¯¥çµæ¢¯ç»å®ç¹ä½
notice.description.300213=å¼å«çæªè¿æ¥è°åº¦ç³»ç»
notice.solution.300213=1ï¼è¯·æ£æ¥å¼å«çä¸æå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥å¼å«çççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·ä½¿ç¨å¼å«çéç½®å·¥å·ï¼æ£æ¥å¼å«ççæå¡å¨å°åéç½®æ¯å¦æ­£ç¡®ï¼
notice.description.300214=å¼å«çæªå¨ç³»ç»ä¸­è¿è¡éç½®
notice.solution.300214=è¯·ä¸ºè¯¥å¼å«çéç½®äºä»¶ç±»åçä»»å¡æµç¨
notice.description.300215=å¼å«çå³èçä»»å¡æµç¨æªåå¸
notice.solution.300215=è¯·åå¸è¯¥å¼å«çç»å®çä»»å¡æµç¨
notice.description.300216=å¼å«çç¼å·éå¤
notice.solution.300216=æå¤ä¸ªå¼å«çéç½®äºç¸åçå¼å«çIDï¼è¯·ä½¿ç¨å¼å«çéç½®å·¥å·éæ°éç½®å¼å«ç
notice.description.300217=å¼å«çè½¯ä»¶å¼å¸¸
notice.solution.300217=è¯·ä½¿ç¨å¼å«çéç½®å·¥å·è¿æ¥å¼å«çï¼å¹¶æ£æ¥å¼å«çç¨åºé®é¢
notice.description.300218=å¼å«çç¡¬ä»¶å¼å¸¸
notice.solution.300218=è¯·ä½¿ç¨å¼å«çéç½®å·¥å·è¿æ¥å¼å«çï¼å¹¶æ£æ¥å¼å«çç¡¬ä»¶é®é¢
notice.description.300219=å¼å«çéç½®å¼å¸¸
notice.solution.300219=è¯·ä½¿ç¨å¼å«çéç½®å·¥å·è¿æ¥å¼å«çï¼å¹¶æ£æ¥å¼å«çéç½®é®é¢
notice.description.300220=å¼å«ççµéä½
notice.solution.300220=è¯·æå¨ä¸ºå¼å«çåçµ

notice.description.300222=åçµæ¡©ä¸å­å¨
notice.solution.300222=è¯·å¨è®¾å¤ç®¡ççé¢æ£æ¥åçµæ¡©æ¯å¦å­å¨
notice.description.300223=åçµæ¡©ä¸å¯ç¨
notice.solution.300223=è¯·å¨è®¾å¤ç®¡ççé¢æ£æ¥åçµæ¡©ç¶æ
notice.description.300224=åçµæ¡©å·²æ­çº¿
notice.solution.300224=è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»è¿æ¥æ¯å¦æ­£å¸¸
notice.description.300225=åçµæ¡©ç¶æå¼å¸¸
notice.solution.300225=è¯·å¨è®¾å¤ç®¡ççé¢æ£æ¥åçµæ¡©ç¶æ
notice.description.300226=åçµæ¡©æ¾çµä¸­
notice.solution.300226=è¯·å¨è®¾å¤ç®¡ççé¢æ£æ¥åçµæ¡©ç¶æ
notice.description.300227=åçµç¹æªç»å®åçµæ¡©
notice.solution.300227=è¯·å¨å°å¾ç¼æçé¢æ£æ¥åçµç¹éç½®

notice.description.300301=ä»»å¡å¼å¸¸
notice.solution.300301=è¯·èç³»ææ¯æ¯æäººå
notice.description.300302=ä»»å¡èç¹å¼å¸¸
notice.solution.300302=è¯·èç³»ææ¯æ¯æäººå
notice.description.300303=æºå¨äººç±»åæ æºå¨äºº
notice.solution.300303=è¯·å°æºå¨äººç»å®å°è¯¥æºå¨äººç±»åä¸
notice.description.300304=æºå¨äººç»æ æºå¨äºº
notice.solution.300304=è¯·å°æºå¨äººç»å®å°è¯¥æºå¨äººç»ä¸
notice.description.300305=æºå¨äººä¸å­å¨
notice.solution.300305=è¯·æå¼ä»»å¡è¯¦æå¼¹çªåæºå¨äººåè¡¨çé¢ï¼æ£æ¥åå»ºä»»å¡æ¯å¦ä½¿ç¨äºä¸å­å¨çæºå¨äººã
notice.description.300306=å°å¾æ æºå¨äºº
notice.solution.300306=è¯·å¨è¯¥å°å¾ä¸çº¿å¯ç¨çæºå¨äºº
notice.description.300307=ç¹ä½ä¸å­å¨
notice.solution.300307=è¯·æå¼ä»»å¡è¯¦æå¼¹çªåå°å¾åè¡¨çé¢ï¼æ£æ¥åå»ºä»»å¡æ¯å¦ä½¿ç¨äºä¸å­å¨çç¹ä½ã
notice.description.300308=ç®æ ç¹ä½ä¸å¯è¾¾
notice.solution.300308=1ï¼è¯·æ£æ¥æºå¨äººæå¨ç¹åç®æ ç¹æ¯å¦å¨åä¸ä¸ªå°å¾ä¸ï¼ 2ï¼è¯·æ£æ¥æºå¨äººæå¨ç¹åç®æ ç¹ä¹é´æ¯å¦æå¯ç¨çè·¯å¾ï¼
notice.description.300309=æ§å¶éæ³çæºå¨äºº
notice.solution.300309=1ï¼å½è¯¥èç¹æªæå®è¦æ§å¶çæºå¨äººæ¶ï¼éå¨è¯¥èç¹åæ§è¡æä¸åªæä¸ä¸ªâå¨æåéæºå¨äººâæâæå®åéæºå¨äººâèç¹ï¼ 2ï¼å½è¯¥èç¹å·²æå®è¦æ§å¶çæºå¨äººæ¶ï¼è¯¥èç¹æå®çæºå¨äººéè¦æ¯âå¨æåéæºå¨äººâæâæå®åéæºå¨äººâèç¹çè¾åºåæ°ï¼
notice.description.300310=åéæºå¨äººæä»¤å¤±è´¥
notice.solution.300310=è¯·æ£æ¥æºå¨äººä¸æå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼
notice.description.300311=æºå¨äººæ æ³æ¥æ¶æä»¤
notice.solution.300311=1ï¼è¯·æ£æ¥æºå¨äººæ¯å¦ä¸ºèªå¨æ§å¶æ¨¡å¼ï¼ 2ï¼è¯·æ£æ¥æºå¨äººæ¯å¦è¢«æä¸æ¥åæé®ï¼ 3ï¼è¯·æ£æ¥æºå¨äººæ¯å¦å¤äºå·²è¿æ¥ç¶æï¼ 4ï¼è¯·æ£æ¥æºå¨äººæ¯å¦å¤äºå¼å¸¸ç¶æï¼
notice.description.300312=æºå¨äººæ§è¡æä»¤å¤±è´¥
notice.solution.300312=è¯·èç³»ææ¯æ¯æäººå
notice.description.300313=æ å¯ç¨çç¹ä½
notice.solution.300313=1ï¼è¯¥èç¹æå®äºå°å¾ï¼ä½å¨è¯¥å°å¾ä¸æ¾ä¸å°ç¹ä½ï¼ 2ï¼è¯¥èç¹æå®äºç¹ä½ç±»åï¼ä½æ¾ä¸å°ç¹ä½ï¼
notice.description.300314=æ°æ®æ ¼å¼è½¬æ¢å¤±è´¥
notice.solution.300314=è¯·å¨ä»»å¡è¯¦æçé¢ï¼æ£æ¥èç¹åæ°è¾å¥å¼
notice.description.300315=æ æ³è¿æ¥Plc
notice.solution.300315=1ï¼è¯·æ£æ¥Plcä¸æå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼ 2ï¼è¯·æ£æ¥Plcççµæºæ¯å¦å·²æå¼ï¼ 3ï¼è¯·æ£æ¥èç¹éç½®çPlcå°åç«¯å£æ¯å¦æ­£ç¡®ï¼
notice.description.300316=å¯å­å¨å¼ä¸å¨èå´å
notice.solution.300316=è¯·æå¼ä»»å¡è¯¦æå¼¹çªï¼æ£æ¥åå»ºä»»å¡æ¯å¦ä½¿ç¨äºè¶åºèå´çå¯å­å¨å¼ã
notice.description.300317=è·åç¹ä½çå±æ§ä¸ºç©º
notice.solution.300317=è¯·æå¼å°å¾ç¼è¾é¡µé¢ï¼å¹¶æ£æ¥è¯¥ç¹ä½çå±æ§
notice.description.300318=åºä½ä¸å­å¨ææªå¯ç¨
notice.solution.300318=è¯·æå¼åºä½é¡µé¢, æ£æ¥åå»ºä»»å¡æ¯å¦ä½¿ç¨äºä¸å­å¨ææªå¯ç¨çåºä½
notice.description.300319=åºåºä¸å­å¨
notice.solution.300319=è¯·æå¼åºåºé¡µé¢, æ£æ¥åå»ºä»»å¡æ¯å¦ä½¿ç¨äºä¸å­å¨çåºåº
notice.description.300320=åºä½ç±»åä¸å­å¨
notice.solution.300320=è¯·æå¼åºä½ç±»åé¡µé¢, æ£æ¥åå»ºä»»å¡æ¯å¦ä½¿ç¨äºä¸å­å¨çåºä½ç±»å
notice.description.300321=æ å¯ç¨åºä½
notice.solution.300321=è¯·æå¼åºä½é¡µé¢, æ£æ¥æ¯å¦å­å¨ä»»å¡éæ©çåºä½
notice.description.300322=å®¹å¨æ¡ç ä¸å­å¨
notice.solution.300322=è¯·æå¼åºä½é¡µé¢, æ£æ¥åå»ºä»»å¡æ¯å¦ä½¿ç¨äºä¸å­å¨çå®¹å¨ç¼ç 
notice.description.300323=ç¹ä½çç¸é»ç¹ä½ä¸å¯ä¸
notice.solution.300323=è¯·æ£æ¥åå»ºä»»å¡æéæ©çç¹ä½æ¯å¦æ²¡æç¸é»ç¹ä½æèæå¤ä¸ªç¸é»ç¹ä½
notice.description.300324=å®¹å¨æ¡ç å·²å­å¨
notice.solution.300324=è¯·æå¼åºä½é¡µé¢, æ£æ¥æ¯å¦å·²ç»å­å¨ç¸åçå®¹å¨ç¼ç 
notice.description.300325=åºä½å·²è¢«å ç¨
notice.solution.300325=è¯·æå¼åºä½é¡µé¢, æ£æ¥è¯¥åºä½æ¯å¦å·²ç»è¢«å ç¨
notice.description.300326=Httpè¯·æ±å¼å¸¸
notice.solution.300326=1ï¼è¯·æ£æ¥Httpè¯·æ±å°åéç½®æ¯å¦æ­£ç¡®ï¼ 2ï¼è¯·æ£æ¥è®¾å¤åæå¡å¨çç½ç»æ¯å¦è¿æ¥æ­£å¸¸ï¼
notice.description.300327=è¯¥ç¼å·ä¸ï¼å­å¨å¤ä¸ªç¬¦åæ¡ä»¶çç¹ä½
notice.solution.300327=è¯·æ£æ¥åå»ºä»»å¡æéæ©çç¹ä½æ¯å¦å­å¨å¤ä¸ª
notice.description.300328=æ æ³æ¾å°ç¬¦åè¦æ±çåºä½
notice.solution.300328=è¯·æå¼åºä½é¡µé¢, æ£æ¥æ¯å¦å­å¨ä»»å¡éæ©çåºä½
notice.description.300329=åºåä¸å­å¨
notice.solution.300329=è¯·æå¼ä»»å¡æµç¨è¯¦æé¡µé¢ï¼å¨å¯¹åºèç¹å¡«åå·²å­å¨çç¦å¥åºåç¼ç 
notice.description.300330=è¯¥åºåç¦æ­¢æä½
notice.solution.300330=è¯·æå¼ä»»å¡æµç¨è¯¦æé¡µé¢ï¼å¨å¯¹åºèç¹å¡«åå¯æä½çåºåç¼ç 
notice.description.300331=æºå¨äººåçµåé åç¦»è§åº¦è¿å¤§
notice.solution.300331=è¯·æå¼å°å¾ç¼è¾é¡µé¢ï¼è°æ´å¯¹åºç¹ä½çåç§»è§åº¦
notice.description.300332=ä½ä¸ä»»å¡ä¸è½åæ­¢
notice.solution.300332=è¯·æ£æ¥æºå¨äººæ§è¡çä»»å¡æ¯å¦ä¸ºä½ä¸ä»»å¡
notice.description.300401=æºå¨äººå¯¼èªå²çª, æ å¯ç¨çé¿è®©ç¹
notice.solution.300401=è¯·èç³»ææ¯æ¯æäººå
notice.description.300501=å°å¾æä»¶åå¥ç£çå¤±è´¥
notice.solution.300501=è¯·éæ°éç½®è¯¥ç£çç®å½çè®¿é®æé
notice.description.300502=è¯»åç£çå°å¾æä»¶å¤±è´¥
notice.solution.300502=è¯·éæ°éç½®è¯¥ç£çç®å½çè®¿é®æé
notice.description.570001=æºæ¢°èéç¨å¤±è´¥
notice.solution.570001=è¯·èç³»moså¼åäººåå¤ç
notice.description.570002=æºæ¢°èæ¥å£åæ°éè¯¯
notice.solution.570002=è¯·èç³»moså¼åäººåå¤ç
notice.description.570003=æªå¼å®¹çæä»¤æ¥å£
notice.solution.570003=è¯·èç³»moså¼åäººåå¤ç
notice.description.570004=æºå¨äººè¿æ¥å¤±è´¥
notice.solution.570004=è¯·èç³»moså¼åäººåå¤ç
notice.description.570005=æºæ¢°èsocketéè®¯æ¶æ¯æ¶åå¼å¸¸
notice.solution.570005=è¯·èç³»moså¼åäººåå¤ç
notice.description.570006=Socketæ­å¼è¿æ¥
notice.solution.570006=è¯·èç³»moså¼åäººåå¤ç
notice.description.570007=åå»ºè¯·æ±å¤±è´¥
notice.solution.570007=è¯·èç³»moså¼åäººåå¤ç
notice.description.570008=è¯·æ±ç¸å³çåé¨åéåºé
notice.solution.570008=è¯·èç³»moså¼åäººåå¤ç
notice.description.570009=è¯·æ±è¶æ¶
notice.solution.570009=è¯·èç³»moså¼åäººåå¤ç
notice.description.570010=åéè¯·æ±ä¿¡æ¯å¤±è´¥
notice.solution.570010=è¯·èç³»moså¼åäººåå¤ç
notice.description.570011=ååºä¿¡æ¯ä¸ºç©º
notice.solution.570011=è¯·èç³»moså¼åäººåå¤ç
notice.description.570012=ååºä¿¡æ¯headerä¸ç¬¦
notice.solution.570012=è¯·èç³»moså¼åäººåå¤ç
notice.description.570013=è§£æååºå¤±è´¥
notice.solution.570013=è¯·èç³»moså¼åäººåå¤ç
notice.description.570014=æ­£è§£åºé
notice.solution.570014=ç»æ­¢å½åä»»å¡ï¼åä¸æºæ¢°èææä¸çç©æï¼æå¨å°æºæ¢°èåå°åç¹åï¼æ¢å¤ä»»å¡ã
notice.description.570015=éè§£åºé
notice.solution.570015=ç»æ­¢å½åä»»å¡ï¼åä¸æºæ¢°èææä¸çç©æï¼æå¨å°æºæ¢°èåå°åç¹åï¼æ¢å¤ä»»å¡ã
notice.description.570016=å·¥å·æ å®åºé
notice.solution.570016=è¯·èç³»moså¼åäººåå¤ç
notice.description.570017=å·¥å·æ å®åæ°æé
notice.solution.570017=è¯·èç³»moså¼åäººåå¤ç
notice.description.570018=åæ ç³»æ å®å¤±è´¥
notice.solution.570018=è¯·èç³»moså¼åäººåå¤ç
notice.description.570019=åºåæ ç³»è½¬ç¨æ·åº§æ å¤±è´¥
notice.solution.570019=è¯·èç³»moså¼åäººåå¤ç
notice.description.570020=ç¨æ·åæ ç³»è½¬åºåº§æ å¤±è´¥
notice.solution.570020=è¯·èç³»moså¼åäººåå¤ç
notice.description.570021=æºå¨äººä¸çµå¤±è´¥
notice.solution.570021=è¯·èç³»moså¼åäººåå¤ç
notice.description.570022=æºå¨äººæ­çµå¤±è´¥
notice.solution.570022=è¯·èç³»moså¼åäººåå¤ç
notice.description.570023=æºå¨äººä½¿è½å¤±è´¥
notice.solution.570023=è¯·èç³»moså¼åäººåå¤ç
notice.description.570024=æºå¨äººä¸ä½¿è½å¤±è´¥
notice.solution.570024=è¯·èç³»moså¼åäººåå¤ç
notice.description.570025=æºå¨äººå¤ä½å¤±è´¥
notice.solution.570025=åæ¬¡ç¹å»å¤ä½æé®è¿è¡å¤ä½ï¼å¦æè¿å¤±è´¥ï¼è¯·ååºç¤ºæå¨ï¼æ¸é¤æ¥éã
notice.description.570026=æºå¨äººæåå¤±è´¥
notice.solution.570026=è¯·èç³»moså¼åäººåå¤ç
notice.description.570027=æºå¨äººåæ­¢å¤±è´¥
notice.solution.570027=æä¸æ¥åæé®ï¼åä¸æºæ¢°èææä¸çç©æï¼ä¸çµï¼ä½¿è½ï¼æå¨å°æºæ¢°èåå°åç¹åï¼æ¢å¤ä»»å¡ã
notice.description.570028=æºå¨äººç¶æè·åå¤±è´¥
notice.solution.570028=ç»æ­¢å½åä»»å¡ï¼åä¸æºæ¢°èææä¸çç©æï¼æå¨å°æºæ¢°èåå°åç¹åï¼æ¢å¤ä»»å¡ã
notice.description.570029=æºå¨äººç¼ç å¨ç¶æåæ­¥å¤±è´¥
notice.solution.570029=è¯·èç³»moså¼åäººåå¤ç
notice.description.570030=æºå¨äººæ¨¡å¼ä¸æ­£ç¡®
notice.solution.570030=è¯·èç³»moså¼åäººåå¤ç
notice.description.570031=æºå¨äººJOGè¿å¨å¤±è´¥
notice.solution.570031=è¯·èç³»moså¼åäººåå¤ç
notice.description.570032=æºå¨äººæå¨ç¤ºæè®¾ç½®å¤±è´¥
notice.solution.570032=è¯·èç³»moså¼åäººåå¤ç
notice.description.570033=æºå¨äººéåº¦è®¾ç½®å¤±è´¥
notice.solution.570033=è¯·èç³»moså¼åäººåå¤ç
notice.description.570034=æºå¨äººè·¯ç¹æ¸é¤å¤±è´¥
notice.solution.570034=è¯·èç³»moså¼åäººåå¤ç
notice.description.570035=æºå¨äººå½ååæ ç³»è·åå¤±è´¥
notice.solution.570035=è¯·èç³»moså¼åäººåå¤ç
notice.description.570036=æºå¨äººåæ ç³»è®¾ç½®å¤±è´¥
notice.solution.570036=è¯·èç³»moså¼åäººåå¤ç
notice.description.570037=æºå¨äººéééå¿è®¾ç½®å¤±è´¥
notice.solution.570037=è¯·èç³»moså¼åäººåå¤ç
notice.description.570038=æºå¨äººIOè®¾ç½®å¤±è´¥
notice.solution.570038=è¯·èç³»moså¼åäººåå¤ç
notice.description.570039=æºå¨äººTCPè®¾ç½®å¤±è´¥
notice.solution.570039=è¯·èç³»moså¼åäººåå¤ç
notice.description.570040=æºå¨äººTCPè·åå¤±è´¥
notice.solution.570040=è¯·èç³»moså¼åäººåå¤ç
notice.description.570041=moveæä»¤é»å¡ç­å¾è¶æ¶
notice.solution.570041=ç»æ­¢å½åä»»å¡ï¼åä¸æºæ¢°èææä¸çç©æï¼æå¨å°æºæ¢°èåå°åç¹åï¼æ£æ¥è¶æ¶åå ã1.å¦å ä¸ºè·¯å¾ç¹è½¨è¿¹èååå¾å¤ªå¤§ï¼å¯¼è´è¿å¨ä¸å°ä½ï¼å¯éå½å°å¯¹åºæ­¥éª¤çéåº¦éä½ãç¶ååæºæµè¯è¿è¡è¯¥ä»»å¡ï¼å¦æä¸ä¼åæ¥è¶æ¶ï¼å³å¯æ¢å¤ä»»å¡2.å¦å ä¸ºè§¦åé²æ¤æ§åæ­¢å¯¼è´æºæ¢°èæåæ¶é´è¶è¿1åéï¼å¯ä»¥ç´æ¥æ¢å¤ä»»å¡ã
notice.description.570042=è¿å¨ç¸å³çåé¨åéåºé
notice.solution.570042=è¯·èç³»moså¼åäººåå¤ç
notice.description.570043=è¿å¨è¯·æ±å¤±è´¥
notice.solution.570043=è¯·èç³»moså¼åäººåå¤ç
notice.description.570044=çæè¿å¨è¯·æ±å¤±è´¥
notice.solution.570044=è¯·èç³»moså¼åäººåå¤ç
notice.description.570045=è¿å¨è¢«äºä»¶ä¸­æ­
notice.solution.570045=ç»æ­¢å½åä»»å¡ï¼åä¸æºæ¢°èææä¸çç©æï¼ç¹å»å¤ä½æé®è¿è¡å¤ä½ï¼å¦ææ æ³å¤ä½ï¼è¯·ä½¿ç¨ç¤ºæå¨æ¸é¤éè¯¯ãæ¸éåéæ°ä¸çµãä½¿è½ï¼æå¨å°æºæ¢°èåå°åç¹åï¼æ¢å¤ä»»å¡ã
notice.description.570046=è¿å¨ç¸å³çè·¯ç¹å®¹å¨çé¿åº¦ä¸ç¬¦åè§å®
notice.solution.570046=è¯·èç³»moså¼åäººåå¤ç
notice.description.570047=æå¡å¨ååºè¿åéè¯¯
notice.solution.570047=è¯·èç³»moså¼åäººåå¤ç
notice.description.570048=çå®æºæ¢°èä¸å­å¨
notice.solution.570048=è¯·èç³»moså¼åäººåå¤ç
notice.description.570049=è°ç¨ç¼åæ¥å£å¤±è´¥
notice.solution.570049=è¯·èç³»moså¼åäººåå¤ç
notice.description.570050=è°ç¨æ¥åæ¥å£å¤±è´¥
notice.solution.570050=è¯·èç³»moså¼åäººåå¤ç
notice.description.570051=è°ç¨æåæ¥å£å¤±è´¥
notice.solution.570051=è¯·èç³»moså¼åäººåå¤ç
notice.description.570052=è°ç¨ç»§ç»­æ¥å£å¤±è´¥
notice.solution.570052=è¯·èç³»moså¼åäººåå¤ç
notice.description.570053=è¿å¨è¢«æ¡ä»¶ä¸­æ­
notice.solution.570053=ç»æ­¢å½åä»»å¡ï¼åä¸æºæ¢°èææä¸çç©æï¼æå¨å°æºæ¢°èåå°åç¹åï¼æ£æ¥å¤±ææ¡ä»¶ãå¦ææ¯éä¼ æå¨èªèº«åå¾å¯¼è´çæ¡ä»¶å¤±æï¼è¯·æ¢å¤ä»»å¡ãå¦ææ¯ä¼ æå¨é®é¢ï¼è¯·èç³»å®åäººåè§£å³ã
notice.description.570054=è¿å¨è¢«æå¨ä¸­æ­
notice.solution.570054=è¯·èç³»moså¼åäººåå¤ç
notice.description.571001=å³èè¿å¨å±æ§éç½®éè¯¯
notice.solution.571001=è¯·èç³»moså¼åäººåå¤ç
notice.description.571002=ç´çº¿è¿å¨å±æ§éç½®éè¯¯
notice.solution.571002=è¯·èç³»moså¼åäººåå¤ç
notice.description.571003=è½¨è¿¹è¿å¨å±æ§éç½®éè¯¯
notice.solution.571003=è¯·èç³»moså¼åäººåå¤ç
notice.description.571004=æ æçè¿å¨å±æ§éç½®
notice.solution.571004=è¯·èç³»moså¼åäººåå¤ç
notice.description.571005=ç­å¾æºå¨äººåæ­¢
notice.solution.571005=è¯·èç³»moså¼åäººåå¤ç
notice.description.571006=è¶åºå³èè¿å¨èå´
notice.solution.571006=è¯·èç³»moså¼åäººåå¤ç
notice.description.571007=è¯·æ­£ç¡®è®¾ç½®MODEPç¬¬ä¸ä¸ªè·¯ç¹
notice.solution.571007=è¯·èç³»moså¼åäººåå¤ç
notice.description.571008=ä¼ éå¸¦è·è¸ªéç½®éè¯¯
notice.solution.571008=è¯·èç³»moså¼åäººåå¤ç
notice.description.571009=ä¼ éå¸¦è½¨è¿¹ç±»åéè¯¯
notice.solution.571009=è¯·èç³»moså¼åäººåå¤ç
notice.description.571010=ç¸å¯¹åæ åæ¢éè§£å¤±è´¥
notice.solution.571010=è¯·èç³»moså¼åäººåå¤ç
notice.description.571011=ç¤ºææ¨¡å¼åçç¢°æ
notice.solution.571011=è¯·èç³»moså¼åäººåå¤ç
notice.description.571012=è¿å¨å±æ§éç½®éè¯¯
notice.solution.571012=è¯·èç³»moså¼åäººåå¤ç
notice.description.571101=è½¨è¿¹å¼å¸¸
notice.solution.571101=è¯·èç³»moså¼åäººåå¤ç
notice.description.571102=è½¨è¿¹è§åéè¯¯
notice.solution.571102=è¯·èç³»moså¼åäººåå¤ç
notice.description.571103=äºåå¨çº¿è½¨è¿¹è§åå¤±è´¥
notice.solution.571103=è¯·èç³»moså¼åäººåå¤ç
notice.description.571104=éè§£å¤±è´¥
notice.solution.571104=è¯·èç³»moså¼åäººåå¤ç
notice.description.571105=å¨åå­¦éå¶ä¿æ¤
notice.solution.571105=è¯·èç³»moså¼åäººåå¤ç
notice.description.571106=ä¼ éå¸¦è·è¸ªå¤±è´¥
notice.solution.571106=è¯·èç³»moså¼åäººåå¤ç
notice.description.571107=è¶åºä¼ éå¸¦å·¥ä½èå´
notice.solution.571107=è¯·èç³»moså¼åäººåå¤ç
notice.description.571108=å³èè¶åºèå´
notice.solution.571108=ç»æ­¢å½åä»»å¡ï¼åä¸æºæ¢°èææä¸çç©æãæ£æ¥MOSéè¯¯æ¥å¿ï¼æ¥çæ¯åªä¸ªç¹å¯¼è´çå³èè¶éï¼ä¿®æ¹è¯¥ç¹ä½åï¼å°æºæ¢°èåå°åç¹ï¼éæ°å¼å§ä»»å¡ã
notice.description.571109=å³èè¶é
notice.solution.571109=è¯·èç³»moså¼åäººåå¤ç
notice.description.571110=ç¦»çº¿è½¨è¿¹è§åå¤±è´¥
notice.solution.571110=è¯·èç³»moså¼åäººåå¤ç
notice.description.571200=æ§å¶å¨å¼å¸¸ï¼éè§£å¤±è´¥
notice.solution.571200=è¯·èç³»moså¼åäººåå¤ç
notice.description.571201=æ§å¶å¨å¼å¸¸ï¼ç¶æå¼å¸¸
notice.solution.571201=è¯·èç³»moså¼åäººåå¤ç
notice.description.571300=è¿å¨è¿å¥å°stopé¶æ®µ
notice.solution.571300=è¯·èç³»moså¼åäººåå¤ç
notice.description.571401=æºæ¢°èæªå®ä¹çå¤±è´¥
notice.solution.571401=è¯·èç³»moså¼åäººåå¤ç
notice.description.571501=æºæ¢°èListenNode æªå¯å¨
notice.solution.571501=è¯·èç³»moså¼åäººåå¤ç
notice.description.572100=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572100=è¯·èç³»moså¼åäººåå¤ç
notice.description.572101=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572101=è¯·èç³»moså¼åäººåå¤ç
notice.description.572102=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572102=è¯·èç³»moså¼åäººåå¤ç
notice.description.572103=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572103=è¯·èç³»moså¼åäººåå¤ç
notice.description.572104=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572104=è¯·èç³»moså¼åäººåå¤ç
notice.description.572105=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572105=è¯·èç³»moså¼åäººåå¤ç
notice.description.572106=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572106=è¯·èç³»moså¼åäººåå¤ç
notice.description.572107=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572107=è¯·èç³»moså¼åäººåå¤ç
notice.description.572108=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572108=è¯·èç³»moså¼åäººåå¤ç
notice.description.572109=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572109=è¯·èç³»moså¼åäººåå¤ç
notice.description.572110=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572110=è¯·èç³»moså¼åäººåå¤ç
notice.description.572111=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572111=è¯·èç³»moså¼åäººåå¤ç
notice.description.572112=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572112=è¯·èç³»moså¼åäººåå¤ç
notice.description.572113=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572113=è¯·èç³»moså¼åäººåå¤ç
notice.description.572114=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572114=è¯·èç³»moså¼åäººåå¤ç
notice.description.572115=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572115=è¯·èç³»moså¼åäººåå¤ç
notice.description.572116=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572116=è¯·èç³»moså¼åäººåå¤ç
notice.description.572117=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572117=è¯·èç³»moså¼åäººåå¤ç
notice.description.572118=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572118=è¯·èç³»moså¼åäººåå¤ç
notice.description.572119=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572119=è¯·èç³»moså¼åäººåå¤ç
notice.description.572120=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572120=è¯·èç³»moså¼åäººåå¤ç
notice.description.572121=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572121=è¯·èç³»moså¼åäººåå¤ç
notice.description.572122=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572122=è¯·èç³»moså¼åäººåå¤ç
notice.description.572123=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572123=è¯·èç³»moså¼åäººåå¤ç
notice.description.572124=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572124=è¯·èç³»moså¼åäººåå¤ç
notice.description.572125=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572125=è¯·èç³»moså¼åäººåå¤ç
notice.description.572126=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572126=è¯·èç³»moså¼åäººåå¤ç
notice.description.572127=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572127=è¯·èç³»moså¼åäººåå¤ç
notice.description.572128=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572128=è¯·èç³»moså¼åäººåå¤ç
notice.description.572129=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572129=è¯·èç³»moså¼åäººåå¤ç
notice.description.572130=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572130=è¯·èç³»moså¼åäººåå¤ç
notice.description.572131=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572131=è¯·èç³»moså¼åäººåå¤ç
notice.description.572132=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572132=è¯·èç³»moså¼åäººåå¤ç
notice.description.572133=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572133=è¯·èç³»moså¼åäººåå¤ç
notice.description.572134=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572134=è¯·èç³»moså¼åäººåå¤ç
notice.description.572135=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572135=è¯·èç³»moså¼åäººåå¤ç
notice.description.572136=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572136=è¯·èç³»moså¼åäººåå¤ç
notice.description.572137=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572137=è¯·èç³»moså¼åäººåå¤ç
notice.description.572138=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572138=è¯·èç³»moså¼åäººåå¤ç
notice.description.572139=PLCå®¢æ·ç«¯å¼å¸¸
notice.solution.572139=è¯·èç³»moså¼åäººåå¤ç
notice.description.572150=ç¡¬ä»¶æ§å¶å¨æªåå§å
notice.solution.572150=è¯·èç³»moså¼åäººåå¤ç
notice.description.572200=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572200=è¯·èç³»moså¼åäººåå¤ç
notice.description.572201=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572201=è¯·èç³»moså¼åäººåå¤ç
notice.description.572202=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572202=è¯·èç³»moså¼åäººåå¤ç
notice.description.572203=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572203=è¯·èç³»moså¼åäººåå¤ç
notice.description.572204=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572204=è¯·èç³»moså¼åäººåå¤ç
notice.description.572205=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572205=è¯·èç³»moså¼åäººåå¤ç
notice.description.572206=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572206=è¯·èç³»moså¼åäººåå¤ç
notice.description.572207=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572207=è¯·èç³»moså¼åäººåå¤ç
notice.description.572208=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572208=è¯·èç³»moså¼åäººåå¤ç
notice.description.572209=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572209=è¯·èç³»moså¼åäººåå¤ç
notice.description.572210=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572210=è¯·èç³»moså¼åäººåå¤ç
notice.description.572211=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572211=è¯·èç³»moså¼åäººåå¤ç
notice.description.572212=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572212=è¯·èç³»moså¼åäººåå¤ç
notice.description.572213=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572213=è¯·èç³»moså¼åäººåå¤ç
notice.description.572214=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572214=è¯·èç³»moså¼åäººåå¤ç
notice.description.572215=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572215=è¯·èç³»moså¼åäººåå¤ç
notice.description.572216=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572216=è¯·èç³»moså¼åäººåå¤ç
notice.description.572217=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572217=è¯·èç³»moså¼åäººåå¤ç
notice.description.572218=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572218=è¯·èç³»moså¼åäººåå¤ç
notice.description.572219=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572219=è¯·èç³»moså¼åäººåå¤ç
notice.description.572220=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572220=è¯·èç³»moså¼åäººåå¤ç
notice.description.572221=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572221=è¯·èç³»moså¼åäººåå¤ç
notice.description.572222=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572222=è¯·èç³»moså¼åäººåå¤ç
notice.description.572223=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572223=è¯·èç³»moså¼åäººåå¤ç
notice.description.572224=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572224=è¯·èç³»moså¼åäººåå¤ç
notice.description.572225=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572225=è¯·èç³»moså¼åäººåå¤ç
notice.description.572226=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572226=è¯·èç³»moså¼åäººåå¤ç
notice.description.572227=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572227=è¯·èç³»moså¼åäººåå¤ç
notice.description.572228=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572228=è¯·èç³»moså¼åäººåå¤ç
notice.description.572229=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572229=è¯·èç³»moså¼åäººåå¤ç
notice.description.572230=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572230=è¯·èç³»moså¼åäººåå¤ç
notice.description.572231=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572231=è¯·èç³»moså¼åäººåå¤ç
notice.description.572232=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572232=è¯·èç³»moså¼åäººåå¤ç
notice.description.572233=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572233=è¯·èç³»moså¼åäººåå¤ç
notice.description.572234=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572234=è¯·èç³»moså¼åäººåå¤ç
notice.description.572235=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572235=è¯·èç³»moså¼åäººåå¤ç
notice.description.572236=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572236=è¯·èç³»moså¼åäººåå¤ç
notice.description.572237=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572237=è¯·èç³»moså¼åäººåå¤ç
notice.description.572238=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572238=è¯·èç³»moså¼åäººåå¤ç
notice.description.572239=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572239=è¯·èç³»moså¼åäººåå¤ç
notice.description.572240=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572240=è¯·èç³»moså¼åäººåå¤ç
notice.description.572241=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572241=è¯·èç³»moså¼åäººåå¤ç
notice.description.572242=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572242=è¯·èç³»moså¼åäººåå¤ç
notice.description.572243=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572243=è¯·èç³»moså¼åäººåå¤ç
notice.description.572244=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572244=è¯·èç³»moså¼åäººåå¤ç
notice.description.572245=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572245=è¯·èç³»moså¼åäººåå¤ç
notice.description.572246=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572246=è¯·èç³»moså¼åäººåå¤ç
notice.description.572247=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572247=è¯·èç³»moså¼åäººåå¤ç
notice.description.572248=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572248=è¯·èç³»moså¼åäººåå¤ç
notice.description.572249=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572249=è¯·èç³»moså¼åäººåå¤ç
notice.description.572250=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572250=è¯·èç³»moså¼åäººåå¤ç
notice.description.572251=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572251=è¯·èç³»moså¼åäººåå¤ç
notice.description.572252=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572252=è¯·èç³»moså¼åäººåå¤ç
notice.description.572253=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572253=è¯·èç³»moså¼åäººåå¤ç
notice.description.572254=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572254=è¯·èç³»moså¼åäººåå¤ç
notice.description.572255=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572255=è¯·èç³»moså¼åäººåå¤ç
notice.description.572256=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572256=è¯·èç³»moså¼åäººåå¤ç
notice.description.572257=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572257=è¯·èç³»moså¼åäººåå¤ç
notice.description.572258=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572258=è¯·èç³»moså¼åäººåå¤ç
notice.description.572259=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572259=è¯·èç³»moså¼åäººåå¤ç
notice.description.572260=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572260=è¯·èç³»moså¼åäººåå¤ç
notice.description.572261=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572261=è¯·èç³»moså¼åäººåå¤ç
notice.description.572262=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572262=è¯·èç³»moså¼åäººåå¤ç
notice.description.572263=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572263=è¯·èç³»moså¼åäººåå¤ç
notice.description.572264=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572264=è¯·èç³»moså¼åäººåå¤ç
notice.description.572265=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572265=è¯·èç³»moså¼åäººåå¤ç
notice.description.572266=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572266=è¯·èç³»moså¼åäººåå¤ç
notice.description.572267=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572267=è¯·èç³»moså¼åäººåå¤ç
notice.description.572268=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572268=è¯·èç³»moså¼åäººåå¤ç
notice.description.572269=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572269=è¯·èç³»moså¼åäººåå¤ç
notice.description.572270=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572270=è¯·èç³»moså¼åäººåå¤ç
notice.description.572271=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572271=è¯·èç³»moså¼åäººåå¤ç
notice.description.572272=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572272=è¯·èç³»moså¼åäººåå¤ç
notice.description.572273=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572273=è¯·èç³»moså¼åäººåå¤ç
notice.description.572274=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572274=è¯·èç³»moså¼åäººåå¤ç
notice.description.572275=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572275=è¯·èç³»moså¼åäººåå¤ç
notice.description.572276=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572276=è¯·èç³»moså¼åäººåå¤ç
notice.description.572277=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572277=è¯·èç³»moså¼åäººåå¤ç
notice.description.572278=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572278=è¯·èç³»moså¼åäººåå¤ç
notice.description.572279=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572279=è¯·èç³»moså¼åäººåå¤ç
notice.description.572280=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572280=è¯·èç³»moså¼åäººåå¤ç
notice.description.572281=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572281=è¯·èç³»moså¼åäººåå¤ç
notice.description.572282=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572282=è¯·èç³»moså¼åäººåå¤ç
notice.description.572283=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572283=è¯·èç³»moså¼åäººåå¤ç
notice.description.572284=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572284=è¯·èç³»moså¼åäººåå¤ç
notice.description.572285=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572285=è¯·èç³»moså¼åäººåå¤ç
notice.description.572286=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572286=è¯·èç³»moså¼åäººåå¤ç
notice.description.572287=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572287=è¯·èç³»moså¼åäººåå¤ç
notice.description.572288=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572288=è¯·èç³»moså¼åäººåå¤ç
notice.description.572289=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572289=è¯·èç³»moså¼åäººåå¤ç
notice.description.572290=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572290=è¯·èç³»moså¼åäººåå¤ç
notice.description.572291=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572291=è¯·èç³»moså¼åäººåå¤ç
notice.description.572292=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572292=è¯·èç³»moså¼åäººåå¤ç
notice.description.572293=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572293=è¯·èç³»moså¼åäººåå¤ç
notice.description.572294=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572294=è¯·èç³»moså¼åäººåå¤ç
notice.description.572295=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572295=è¯·èç³»moså¼åäººåå¤ç
notice.description.572296=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572296=è¯·èç³»moså¼åäººåå¤ç
notice.description.572297=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572297=è¯·èç³»moså¼åäººåå¤ç
notice.description.572298=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572298=è¯·èç³»moså¼åäººåå¤ç
notice.description.572299=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572299=è¯·èç³»moså¼åäººåå¤ç
notice.description.572300=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572300=è¯·èç³»moså¼åäººåå¤ç
notice.description.572301=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572301=è¯·èç³»moså¼åäººåå¤ç
notice.description.572302=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572302=è¯·èç³»moså¼åäººåå¤ç
notice.description.572303=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572303=è¯·èç³»moså¼åäººåå¤ç
notice.description.572304=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572304=è¯·èç³»moså¼åäººåå¤ç
notice.description.572305=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572305=è¯·èç³»moså¼åäººåå¤ç
notice.description.572306=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572306=è¯·èç³»moså¼åäººåå¤ç
notice.description.572307=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572307=è¯·èç³»moså¼åäººåå¤ç
notice.description.572308=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572308=è¯·èç³»moså¼åäººåå¤ç
notice.description.572309=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572309=è¯·èç³»moså¼åäººåå¤ç
notice.description.572310=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572310=è¯·èç³»moså¼åäººåå¤ç
notice.description.572311=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572311=è¯·èç³»moså¼åäººåå¤ç
notice.description.572312=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572312=è¯·èç³»moså¼åäººåå¤ç
notice.description.572313=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572313=è¯·èç³»moså¼åäººåå¤ç
notice.description.572314=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572314=è¯·èç³»moså¼åäººåå¤ç
notice.description.572315=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572315=è¯·èç³»moså¼åäººåå¤ç
notice.description.572316=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572316=è¯·èç³»moså¼åäººåå¤ç
notice.description.572317=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572317=è¯·èç³»moså¼åäººåå¤ç
notice.description.572318=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572318=è¯·èç³»moså¼åäººåå¤ç
notice.description.572319=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572319=è¯·èç³»moså¼åäººåå¤ç
notice.description.572320=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572320=è¯·èç³»moså¼åäººåå¤ç
notice.description.572321=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572321=è¯·èç³»moså¼åäººåå¤ç
notice.description.572322=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572322=è¯·èç³»moså¼åäººåå¤ç
notice.description.572323=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572323=è¯·èç³»moså¼åäººåå¤ç
notice.description.572324=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572324=è¯·èç³»moså¼åäººåå¤ç
notice.description.572325=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572325=è¯·èç³»moså¼åäººåå¤ç
notice.description.572326=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572326=è¯·èç³»moså¼åäººåå¤ç
notice.description.572327=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572327=è¯·èç³»moså¼åäººåå¤ç
notice.description.572328=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572328=è¯·èç³»moså¼åäººåå¤ç
notice.description.572329=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572329=è¯·èç³»moså¼åäººåå¤ç
notice.description.572330=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572330=è¯·èç³»moså¼åäººåå¤ç
notice.description.572331=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572331=è¯·èç³»moså¼åäººåå¤ç
notice.description.572332=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572332=è¯·èç³»moså¼åäººåå¤ç
notice.description.572333=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572333=è¯·èç³»moså¼åäººåå¤ç
notice.description.572334=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572334=è¯·èç³»moså¼åäººåå¤ç
notice.description.572335=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572335=è¯·èç³»moså¼åäººåå¤ç
notice.description.572336=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572336=è¯·èç³»moså¼åäººåå¤ç
notice.description.572337=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572337=è¯·èç³»moså¼åäººåå¤ç
notice.description.572338=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572338=è¯·èç³»moså¼åäººåå¤ç
notice.description.572339=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572339=è¯·èç³»moså¼åäººåå¤ç
notice.description.572340=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572340=è¯·èç³»moså¼åäººåå¤ç
notice.description.572341=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572341=è¯·èç³»moså¼åäººåå¤ç
notice.description.572342=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572342=è¯·èç³»moså¼åäººåå¤ç
notice.description.572343=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572343=è¯·èç³»moså¼åäººåå¤ç
notice.description.572344=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572344=è¯·èç³»moså¼åäººåå¤ç
notice.description.572345=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572345=è¯·èç³»moså¼åäººåå¤ç
notice.description.572346=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572346=è¯·èç³»moså¼åäººåå¤ç
notice.description.572347=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572347=è¯·èç³»moså¼åäººåå¤ç
notice.description.572348=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572348=è¯·èç³»moså¼åäººåå¤ç
notice.description.572349=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572349=è¯·èç³»moså¼åäººåå¤ç
notice.description.572350=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572350=è¯·èç³»moså¼åäººåå¤ç
notice.description.572351=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572351=è¯·èç³»moså¼åäººåå¤ç
notice.description.572352=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572352=è¯·èç³»moså¼åäººåå¤ç
notice.description.572353=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572353=è¯·èç³»moså¼åäººåå¤ç
notice.description.572354=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572354=è¯·èç³»moså¼åäººåå¤ç
notice.description.572355=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572355=è¯·èç³»moså¼åäººåå¤ç
notice.description.572356=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572356=è¯·èç³»moså¼åäººåå¤ç
notice.description.572357=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572357=è¯·èç³»moså¼åäººåå¤ç
notice.description.572358=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572358=è¯·èç³»moså¼åäººåå¤ç
notice.description.572359=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572359=è¯·èç³»moså¼åäººåå¤ç
notice.description.572360=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572360=è¯·èç³»moså¼åäººåå¤ç
notice.description.572361=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572361=è¯·èç³»moså¼åäººåå¤ç
notice.description.572362=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572362=è¯·èç³»moså¼åäººåå¤ç
notice.description.572363=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572363=è¯·èç³»moså¼åäººåå¤ç
notice.description.572364=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572364=è¯·èç³»moså¼åäººåå¤ç
notice.description.572365=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572365=è¯·èç³»moså¼åäººåå¤ç
notice.description.572366=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572366=è¯·èç³»moså¼åäººåå¤ç
notice.description.572367=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572367=è¯·èç³»moså¼åäººåå¤ç
notice.description.572368=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572368=è¯·èç³»moså¼åäººåå¤ç
notice.description.572369=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572369=è¯·èç³»moså¼åäººåå¤ç
notice.description.572370=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572370=è¯·èç³»moså¼åäººåå¤ç
notice.description.572371=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572371=è¯·èç³»moså¼åäººåå¤ç
notice.description.572372=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572372=è¯·èç³»moså¼åäººåå¤ç
notice.description.572373=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572373=è¯·èç³»moså¼åäººåå¤ç
notice.description.572374=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572374=è¯·èç³»moså¼åäººåå¤ç
notice.description.572375=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572375=è¯·èç³»moså¼åäººåå¤ç
notice.description.572376=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572376=è¯·èç³»moså¼åäººåå¤ç
notice.description.572377=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572377=è¯·èç³»moså¼åäººåå¤ç
notice.description.572378=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572378=è¯·èç³»moså¼åäººåå¤ç
notice.description.572379=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572379=è¯·èç³»moså¼åäººåå¤ç
notice.description.572380=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572380=è¯·èç³»moså¼åäººåå¤ç
notice.description.572381=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572381=è¯·èç³»moså¼åäººåå¤ç
notice.description.572382=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572382=è¯·èç³»moså¼åäººåå¤ç
notice.description.572383=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572383=è¯·èç³»moså¼åäººåå¤ç
notice.description.572384=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572384=è¯·èç³»moså¼åäººåå¤ç
notice.description.572385=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572385=è¯·èç³»moså¼åäººåå¤ç
notice.description.572386=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572386=è¯·èç³»moså¼åäººåå¤ç
notice.description.572387=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572387=è¯·èç³»moså¼åäººåå¤ç
notice.description.572388=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572388=è¯·èç³»moså¼åäººåå¤ç
notice.description.572389=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572389=è¯·èç³»moså¼åäººåå¤ç
notice.description.572390=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572390=è¯·èç³»moså¼åäººåå¤ç
notice.description.572391=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572391=è¯·èç³»moså¼åäººåå¤ç
notice.description.572392=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572392=è¯·èç³»moså¼åäººåå¤ç
notice.description.572393=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572393=è¯·èç³»moså¼åäººåå¤ç
notice.description.572394=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572394=è¯·èç³»moså¼åäººåå¤ç
notice.description.572395=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572395=è¯·èç³»moså¼åäººåå¤ç
notice.description.572396=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572396=è¯·èç³»moså¼åäººåå¤ç
notice.description.572397=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572397=è¯·èç³»moså¼åäººåå¤ç
notice.description.572398=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572398=è¯·èç³»moså¼åäººåå¤ç
notice.description.572399=PLCç¡¬ä»¶å¼å¸¸
notice.solution.572399=è¯·èç³»moså¼åäººåå¤ç
notice.description.573100=E84 æ¶æ¯ç­å¾è¶æ¶
notice.solution.573100=è¯·èç³»moså¼åäººåå¤ç
notice.description.573101=E84ä¸æ¯æçæä½ç±»å
notice.solution.573101=è¯·èç³»moså¼åäººåå¤ç
notice.description.573102=E84ä¸æ¯æçæºå¨äººä¸ä¸æç¶æ
notice.solution.573102=è¯·èç³»moså¼åäººåå¤ç
notice.description.573103=mqtt_clientå½æ°è°ç¨åºé
notice.solution.573103=è¯·èç³»moså¼åäººåå¤ç
notice.description.573104=mqttè·åæ°æ®è¶æ¶
notice.solution.573104=è¯·èç³»moså¼åäººåå¤ç
notice.description.573105=mqttåéæ°æ®åºé
notice.solution.573105=è¯·èç³»moså¼åäººåå¤ç
notice.description.573106=mqttæ¥æ¶æ°æ®åºé
notice.solution.573106=è¯·èç³»moså¼åäººåå¤ç
notice.description.573107=mqttæ¥æ¶å°æéä¿¡æ¯
notice.solution.573107=è¯·èç³»moså¼åäººåå¤ç
notice.description.573108=é·è¾¾åºååæ¢å¤±è´¥
notice.solution.573108=è¯·èç³»moså¼åäººåå¤ç
notice.description.574100=å¤¹çªæå¼å¤±è´¥
notice.solution.574100=è¯·èç³»moså¼åäººåå¤ç
notice.description.574101=å¤¹çªå³é­å¤±è´¥
notice.solution.574101=è¯·èç³»moså¼åäººåå¤ç
notice.description.574102=å¤¹çªå¤ä½å¤±è´¥
notice.solution.574102=è¯·èç³»moså¼åäººåå¤ç
notice.description.574103=å¤¹çª485åè®®æ¥æ¶å¤±è´¥
notice.solution.574103=è¯·èç³»moså¼åäººåå¤ç
notice.description.574200=è·ç¦»ä¼ æå¨æ£æµè¶æ¶
notice.solution.574200=è¯·èç³»moså¼åäººåå¤ç
notice.description.574201=ææææ ææ£æµä¼ æå¨æ£æµè¶æ¶
notice.solution.574201=è¯·èç³»moså¼åäººåå¤ç
notice.description.574300=ç¸æºæªè¿æ¥
notice.solution.574300=è¯·èç³»moså¼åäººåå¤ç
notice.description.574301=ç¸æºåé¨éè¯¯
notice.solution.574301=è¯·èç³»moså¼åäººåå¤ç
notice.description.574302=è¶æ¶
notice.solution.574302=è¯·èç³»moså¼åäººåå¤ç
notice.description.574303=ç¸æºæªç¥å½ä»¤
notice.solution.574303=è¯·èç³»moså¼åäººåå¤ç
notice.description.574304=ç´¢å¼è¶åºèå´
notice.solution.574304=è¯·èç³»moså¼åäººåå¤ç
notice.description.574305=èªåéå¤ªå°
notice.solution.574305=è¯·èç³»moså¼åäººåå¤ç
notice.description.574306=æ æèªåéç±»å
notice.solution.574306=è¯·èç³»moså¼åäººåå¤ç
notice.description.574307=æ æèªåé
notice.solution.574307=è¯·èç³»moså¼åäººåå¤ç
notice.description.574308=ä¸åè®¸çå½ä»¤
notice.solution.574308=è¯·èç³»moså¼åäººåå¤ç
notice.description.574309=ä¸åè®¸çç»å
notice.solution.574309=è¯·èç³»moså¼åäººåå¤ç
notice.description.574310=ç¸æºå¿
notice.solution.574310=è¯·èç³»moså¼åäººåå¤ç
notice.description.574311=æªå®å¨å®æ½
notice.solution.574311=è¯·èç³»moså¼åäººåå¤ç
notice.description.574312=ä¸æ¯æ
notice.solution.574312=è¯·èç³»moså¼åäººåå¤ç
notice.description.574313=ç»æå­ç¬¦ä¸²è¿â»
notice.solution.574313=è¯·èç³»moså¼åäººåå¤ç
notice.description.574314=â½æç¸æº ID
notice.solution.574314=è¯·èç³»moså¼åäººåå¤ç
notice.description.574315=â½æç¸æºç¹å¾ ID
notice.solution.574315=è¯·èç³»moså¼åäººåå¤ç
notice.description.574316=ä¸åçéâ½åç§°
notice.solution.574316=è¯·èç³»moså¼åäººåå¤ç
notice.description.574317=ä¸åçæ¬
notice.solution.574317=è¯·èç³»moså¼åäººåå¤ç
notice.description.574318=æ²¡ææ å®
notice.solution.574318=è¯·èç³»moså¼åäººåå¤ç
notice.description.574319=æ å®å¤±è´¥
notice.solution.574319=è¯·èç³»moså¼åäººåå¤ç
notice.description.574320=â½ææ å®æ°æ®
notice.solution.574320=è¯·èç³»moså¼åäººåå¤ç
notice.description.574321=æªè¾¾å°ç»å®çæ å®ä½ç½®
notice.solution.574321=è¯·èç³»moså¼åäººåå¤ç
notice.description.574322=â½å¯å¨å½ä»¤
notice.solution.574322=è¯·èç³»moså¼åäººåå¤ç
notice.description.574323=ç¹å¾æªç»è¿è®­ç»
notice.solution.574323=è¯·èç³»moså¼åäººåå¤ç
notice.description.574324=ç¹å¾æªæ¾å°
notice.solution.574324=è¯·èç³»moså¼åäººåå¤ç
notice.description.574325=ç¹å¾æªæ å°
notice.solution.574325=è¯·èç³»moså¼åäººåå¤ç
notice.description.574326=é¨ä»¶ä½ç½®æªç»è¿è®­ç»
notice.solution.574326=è¯·èç³»moså¼åäººåå¤ç
notice.description.574327=æºå¨â¼ä½ç½®æªç»è¿è®­ç»
notice.solution.574327=è¯·èç³»moså¼åäººåå¤ç
notice.description.574328=â½æé¨ä»¶ ID
notice.solution.574328=è¯·èç³»moså¼åäººåå¤ç
notice.description.574329=æªå®ä½æ­¤é¨ä»¶çææç¹å¾
notice.solution.574329=è¯·èç³»moså¼åäººåå¤ç
notice.description.574330=é¨ä»¶â½ææå¤¹æçº æ­£
notice.solution.574330=è¯·èç³»moså¼åäººåå¤ç
notice.description.574331=é¨ä»¶â½ææå¤¹æçº æ­£
notice.solution.574331=è¯·èç³»moså¼åäººåå¤ç
notice.description.574350=ç¸æºè¯»åsocketéè¯¯
notice.solution.574350=è¯·èç³»moså¼åäººåå¤ç
notice.description.574351=ç¸æºååºä¿¡æ¯headerä¸ç¬¦
notice.solution.574351=è¯·èç³»moså¼åäººåå¤ç
notice.description.574352=è§£æç¸æºååºå¤±è´¥
notice.solution.574352=è¯·èç³»moså¼åäººåå¤ç
notice.description.574360=ç¸æºååæ å®å¤±è´¥
notice.solution.574360=è¯·èç³»moså¼åäººåå¤ç
notice.description.574361=ç¸æºæç¼æ å®å¤±è´¥
notice.solution.574361=è¯·èç³»moså¼åäººåå¤ç
notice.description.574362=æ²¡æååæ å®æ°æ®
notice.solution.574362=è¯·èç³»moså¼åäººåå¤ç
notice.description.574363=æ²¡ææç¼æ å®æ°æ®
notice.solution.574363=è¯·èç³»moså¼åäººåå¤ç
notice.description.574364=ç¸æºæ°æ®è·åå¤±è´¥
notice.solution.574364=è¯·èç³»moså¼åäººåå¤ç
notice.description.574365=ç¸æºæ°æ®å­å¨å¤±è´¥
notice.solution.574365=è¯·èç³»moså¼åäººåå¤ç
notice.description.574366=ç¹å¾ç¹åæ è·åå¤±è´¥
notice.solution.574366=è¯·èç³»moså¼åäººåå¤ç
notice.description.574367=æç§è®¡ç®çå¤¹åä½ä¸ç¤ºæå¤¹åä½ä¹é´åå·®è¿å¤§
notice.solution.574367=è¯·èç³»moså¼åäººåå¤ç
notice.description.574368=åå»ºæ¨¡æ¿å¾åå¤±è´¥
notice.solution.574368=è¯·èç³»moså¼åäººåå¤ç
notice.description.574369=è·åç®æ³åæ°å¤±è´¥
notice.solution.574369=è¯·èç³»moså¼åäººåå¤ç
notice.description.574370=ç¸æºå¾åå¤çå¼å¸¸
notice.solution.574370=è¯·èç³»moså¼åäººåå¤ç
notice.description.574390=ç¸æºæç§å¤±è´¥
notice.solution.574390=è¯·èç³»moså¼åäººåå¤ç
notice.description.574391=ç¸æºéæ¹è®¾ç½®å¤±è´¥
notice.solution.574391=è¯·èç³»moså¼åäººåå¤ç
notice.description.574392=ç¸æºè§¦ååæ°è·åå¤±è´¥
notice.solution.574392=è¯·èç³»moså¼åäººåå¤ç
notice.description.574393=ç¸æºä¸æ¡ä¿å­å¤±è´¥
notice.solution.574393=è¯·èç³»moså¼åäººåå¤ç
notice.description.574394=ç¸æºæªåå§å
notice.solution.574394=è¯·èç³»moså¼åäººåå¤ç
notice.description.574400=åæºè®¾å¤ä¸²å£æå¼å¤±è´¥
notice.solution.574400=è¯·èç³»moså¼åäººåå¤ç
notice.description.574401=åæºè®¾å¤ä¸²å£è¯»åå¼å¸¸
notice.solution.574401=è¯·èç³»moså¼åäººåå¤ç
notice.description.574410=åæºæå¼å¤±è´¥
notice.solution.574410=è¯·èç³»moså¼åäººåå¤ç
notice.description.574411=åæºå³é­å¤±è´¥
notice.solution.574411=è¯·èç³»moså¼åäººåå¤ç
notice.description.574412=åæºäº®åº¦è·åå¤±è´¥
notice.solution.574412=è¯·èç³»moså¼åäººåå¤ç
notice.description.575100=æ æçå¨ä½ID
notice.solution.575100=è¯·èç³»moså¼åäººåå¤ç
notice.description.575101=å¨ä½æ£æµè¶æ¶
notice.solution.575101=è¯·èç³»moså¼åäººåå¤ç
notice.description.575200=å¨ä½ééå®è¶æ¶
notice.solution.575200=è¯·èç³»moså¼åäººåå¤ç
notice.description.575201=å¨ä½éè§£éè¶æ¶
notice.solution.575201=è¯·èç³»moså¼åäººåå¤ç
notice.description.575300=æ ç©æä¿¡æ¯æ£æµsensor
notice.solution.575300=è¯·èç³»moså¼åäººåå¤ç
notice.description.575301=smart tag ä¼ æå¨æªè¿æ¥
notice.solution.575301=è¯·èç³»moså¼åäººåå¤ç
notice.description.575302=smart tag è¯»åå¤±è´¥
notice.solution.575302=è¯·èç³»moså¼åäººåå¤ç
notice.description.575303=smart tag è¯»åè¶æ¶
notice.solution.575303=è¯·èç³»moså¼åäººåå¤ç
notice.description.575304=smart tag æ°æ®æ æ
notice.solution.575304=è¯·èç³»moså¼åäººåå¤ç
notice.description.575401=RFID ä¼ æå¨æªè¿æ¥
notice.solution.575401=è¯·èç³»moså¼åäººåå¤ç
notice.description.575402=RFID è¯»åå¤±è´¥
notice.solution.575402=è¯·èç³»moså¼åäººåå¤ç
notice.description.575403=RFID è¯»åè¶æ¶
notice.solution.575403=è¯·èç³»moså¼åäººåå¤ç
notice.description.575404=RFID æ°æ®æ æ
notice.solution.575404=è¯·èç³»moså¼åäººåå¤ç
notice.description.575405=RFID è¯·æ±æ°æ®éè¯¯
notice.solution.575405=è¯·èç³»moså¼åäººåå¤ç
notice.description.575900=smart tag sensor æé
notice.solution.575900=è¯·èç³»moså¼åäººåå¤ç
notice.description.575901=RFID sensor æé
notice.solution.575901=è¯·èç³»moså¼åäººåå¤ç
notice.description.576100=åéæ±è¶åºè¿å¨èå´
notice.solution.576100=è¯·èç³»moså¼åäººåå¤ç
notice.description.576101=æ æçæ§å¶æä»¤
notice.solution.576101=è¯·èç³»moså¼åäººåå¤ç
notice.description.576102=æ æçå¤è½´æ§å¶æ¨¡å¼
notice.solution.576102=è¯·èç³»moså¼åäººåå¤ç
notice.description.576103=å¤è½´è®¾å¤æªå°±ç»ªï¼æåºç°å¼å¸¸
notice.solution.576103=è¯·èç³»moså¼åäººåå¤ç
notice.description.576104=Canå¡æªè¿æ¥
notice.solution.576104=è¯·èç³»moså¼åäººåå¤ç
notice.description.576105=æ­¥ç§è½´è®¾å¤è¶åºè¡ç¨
notice.solution.576105=è¯·èç³»moså¼åäººåå¤ç
notice.description.576106=å¤è½´è®¾å¤æ æ³è·åå½åä½ç½®
notice.solution.576106=è¯·èç³»moså¼åäººåå¤ç
notice.description.576107=å¤è½´è®¾å¤ç§»å¨å¤±è´¥
notice.solution.576107=è¯·èç³»moså¼åäººåå¤ç
notice.description.576108=å¤è½´è®¾å¤è¿å¨è¢«æ¡ä»¶ä¸­æ­
notice.solution.576108=è¯·èç³»moså¼åäººåå¤ç
notice.description.576109=å¤è½´è®¾å¤ç®æ ç¹ä½è¶åºè®¾ç½®çéä½
notice.solution.576109=è¯·èç³»moså¼åäººåå¤ç
notice.description.576110=å¤è½´è®¾å¤è¿å¨è¢«æå¨ä¸­æ­
notice.solution.576110=è¯·èç³»moså¼åäººåå¤ç
notice.description.576111=åè½´è®¾å¤æä»¤é»å¡ç­å¾è¶æ¶,è¶è¿600sæªåæ­¢
notice.solution.576111=è¯·èç³»moså¼åäººåå¤ç
notice.description.576201=æ æçå¤è½´åæ°
notice.solution.576201=è¯·èç³»moså¼åäººåå¤ç
notice.description.576202=å¤è½´ç®¡çåå§åå¤±è´¥
notice.solution.576202=è¯·èç³»moså¼åäººåå¤ç
notice.description.577100=äºå°ä»»å¡JSONè§£æå¤±è´¥
notice.solution.577100=è¯·èç³»moså¼åäººåå¤ç
notice.description.577101=äºå°æ§è¡å¨ä½ç±»åéè¯¯
notice.solution.577101=è¯·èç³»moså¼åäººåå¤ç
notice.description.577102=äºå°éééè¯¯
notice.solution.577102=è¯·èç³»moså¼åäººåå¤ç
notice.description.577103=äºå°ééç±»åéè¯¯
notice.solution.577103=è¯·èç³»moså¼åäººåå¤ç
notice.description.577104=äºå°ç¸æºåå­éè¯¯
notice.solution.577104=è¯·èç³»moså¼åäººåå¤ç
notice.description.577105=äºå°æ§è¡æç§å¤±è´¥
notice.solution.577105=è¯·èç³»moså¼åäººåå¤ç
notice.description.577106=äºå°æ§è¡å½åå¤±è´¥
notice.solution.577106=è¯·èç³»moså¼åäººåå¤ç
notice.description.577107=äºå°æ§è¡åæ°è®¾ç½®å¤±è´¥
notice.solution.577107=è¯·èç³»moså¼åäººåå¤ç
notice.description.577108=äºå°æ§è¡æ®éæµæ¸©å¤±è´¥
notice.solution.577108=è¯·èç³»moså¼åäººåå¤ç
notice.description.577109=äºå°å¾åè¿ç¨æ·è´å¤±è´¥
notice.solution.577109=è¯·èç³»moså¼åäººåå¤ç
notice.description.577110=äºå°è·åSGCåæ°å¤±è´¥ï¼æ£æ¥SGCä¸åçç¸æºåæ¯å¦å¯¹åº
notice.solution.577110=è¯·èç³»moså¼åäººåå¤ç
notice.description.578100=ä¼ æå¨JSONè§£æå¤±è´¥
notice.solution.578100=è¯·èç³»moså¼åäººåå¤ç
notice.description.578101=ä¼ æå¨åå­ä¸å­å¨
notice.solution.578101=è¯·èç³»moså¼åäººåå¤ç
notice.description.578102=ä¼ å¥çG300M4çæ¨¡å¼ä¸å¯¹
notice.solution.578102=è¯·èç³»moså¼åäººåå¤ç
notice.description.578201=XSLABå£°çº¹ä¼ æå¨åå§åå¤±è´¥
notice.solution.578201=è¯·èç³»moså¼åäººåå¤ç
notice.description.578202=G300M4å±æ¾ä¼ æå¨åå§åå¤±è´¥
notice.solution.578202=è¯·èç³»moså¼åäººåå¤ç
notice.description.578203=FS00802ç¦ç³ä¼ æå¨åå§åå¤±è´¥
notice.solution.578203=è¯·èç³»moså¼åäººåå¤ç
notice.description.578204=æ¥æ¶G300M4æ°æ®å¤±è´¥
notice.solution.578204=è¯·èç³»moså¼åäººåå¤ç
notice.description.578205=G300M4å·¥ä½æ¨¡å¼éè¯¯
notice.solution.578205=è¯·èç³»moså¼åäººåå¤ç
notice.description.578206=ä¼ æå¨åå§åå¤±è´¥æèè¿è¡éè¯¯ï¼è¯·æ£æ¥éç½®
notice.solution.578206=è¯·èç³»moså¼åäººåå¤ç
notice.description.579000=ç³»ç»æªåå§å
notice.solution.579000=è¯·èç³»moså¼åäººåå¤ç
notice.description.579100=åæ¶ä»»å¡å¤±è´¥
notice.solution.579100=è¯·èç³»moså¼åäººåå¤ç
notice.description.579101=æåä»»å¡å¤±è´¥
notice.solution.579101=è¯·èç³»moså¼åäººåå¤ç
notice.description.579102=æ¢å¤ä»»å¡å¤±è´¥
notice.solution.579102=è¯·èç³»moså¼åäººåå¤ç
notice.description.579103=bufferè§£æéè¯¯
notice.solution.579103=è¯·èç³»moså¼åäººåå¤ç
notice.description.579104=æªæ¾å°ä»»å¡
notice.solution.579104=è¯·èç³»moså¼åäººåå¤ç
notice.description.579105=ä»»å¡åè¡¨æªæ´æ°
notice.solution.579105=è¯·èç³»moså¼åäººåå¤ç
notice.description.579106=å­å¨æªå®æçä»»å¡
notice.solution.579106=è¯·èç³»moså¼åäººåå¤ç
notice.description.579107=ä»»å¡è¢«æå¨ä¸­æ­
notice.solution.579107=è¯·èç³»moså¼åäººåå¤ç
notice.description.579201=æ ææ­¥éª¤ç±»å
notice.solution.579201=è¯·èç³»moså¼åäººåå¤ç
notice.description.579202=æªæ¾å°pose value
notice.solution.579202=è¯·èç³»moså¼åäººåå¤ç
notice.description.579203=æªæ¾å°joint value
notice.solution.579203=è¯·èç³»moså¼åäººåå¤ç
notice.description.579204=æªæ¾å°åç§»é
notice.solution.579204=è¯·èç³»moså¼åäººåå¤ç
notice.description.579205=æ æçfeature ID
notice.solution.579205=è¯·èç³»moså¼åäººåå¤ç
notice.description.579206=æ æçæ¡ä»¶ç±»å
notice.solution.579206=è¯·èç³»moså¼åäººåå¤ç
notice.description.579207=æ æçæ¡ä»¶åæ°
notice.solution.579207=è¯·èç³»moså¼åäººåå¤ç
notice.description.579208=å¨ä½åè¡¨è·åå¤±è´¥
notice.solution.579208=è¯·èç³»moså¼åäººåå¤ç
notice.description.579209=æºæ¢°èä¸å¨åç¹
notice.solution.579209=è¯·èç³»moså¼åäººåå¤ç
notice.description.579210=éæéä½,åºçæ­£å¨è¿å¨
notice.solution.579210=è¯·èç³»moså¼åäººåå¤ç
notice.description.579211=Socketåè®®è§£æå¤±è´¥
notice.solution.579211=è¯·èç³»moså¼åäººåå¤ç

notice.description.620001=åçµæºçè¾åºçµåé«äºé¢è®¾å¼
notice.solution.620001=è¯·æ£æ¥çµåè®¾ç½®
notice.description.620002=åçµæ¡©çæ¸©åº¦é«äºé¢è®¾å¼
notice.solution.620002=è¯·æ£æ¥å·å
notice.description.620004=åçµæ¡©çè¾å¥çµåé«äºæä½äºè¾å¥çµåèå´
notice.solution.620004=è¯·æ£æ¥è¾å¥æ¥çº¿
notice.description.620005=åçµæ¡©çè¾åºç­è·¯
notice.solution.620005=è¯·æ£æ¥è¾åºç«¯
notice.description.620006=åçµæ¡©çé£ææé
notice.solution.620006=è¯·æ£æ¥æ¨¡åé£æ
notice.description.620007=åçµæ¡©çè¾åºçµæµé«äºé¢è®¾å¼
notice.solution.620007=è¯·æ£æ¥çµæµè®¾ç½®
notice.description.620008=å·åæ¸©åº¦è¿é«
notice.solution.620008=è¯·æ£æ¥æ£ç­ç³»ç»

notice.description.610001=åå°ç«¯éåçµæµè¶é
notice.solution.610001=å°è·ç¦»æ§å¶å°åçèå´åæ£æ¥çº¿å
notice.description.610002=åå°ç«¯éåçµæµçªå
notice.solution.610002=å¦æå¨æåºç°ï¼éåè¿ä¿®
notice.description.610003=åå°ç«¯æ çº¿éä¿¡æçº¿
notice.solution.610003=å¦æå¨æåºç°æèé¢ç¹åºç°åæ£æ¥å¤©çº¿æ¯å¦æ¾å¨
notice.description.610004=åå°ç«¯è¾å¥æ¯çº¿çµåè¿é«
notice.solution.610004=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610005=åå°ç«¯è¾å¥æ¯çº¿çµåè¿ä½
notice.solution.610005=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610006=åå°ç«¯ FAULT ä¿æ¤
notice.solution.610006=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610007=åå°ç«¯æ¸©åº¦è¿é«
notice.solution.610007=æ£æ¥é£ææ¯å¦æ­£å¸¸è½¬å¨
notice.description.610008=åå°ç«¯å¤é¨å½ä»¤åæ­¢ä½¿è½
notice.solution.610008=æ 
notice.description.610009=åå°ç«¯å¤æ­çµåè¾¾å°æ çµæµ
notice.solution.610009=æ 
notice.description.610010=åå°ç«¯å¤å®åæ»¡åæº
notice.solution.610010=æ 
notice.description.610011=åå°ç«¯çµæºæ¨¡åæé
notice.solution.610011=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610012=åå°ç«¯è¦ååº¦æµè¯ä¸åæ ¼
notice.solution.610012=æ 
notice.description.610013=åå°ç«¯æ¯çº¿çµæµç¡¬ä»¶ä¿æ¤
notice.solution.610013=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610014=åå°ç«¯æ¯çº¿çµåç¡¬ä»¶ä¿æ¤
notice.solution.610014=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610015=åå°ç«¯äº¤æµæ¥è§¦å¨å¼å¸¸åæ­¢
notice.solution.610015=æ 
notice.description.610016=åå°ç«¯çº¿å/å¯¼è½¨çµæµå¼å¸¸
notice.solution.610016=æ£æ¥çº¿åé´è·ç¦»æ¯å¦å¨è§å®èå´å
notice.description.610017=TXå¯¼è½¨/çº¿åçµæµè¿æµ
notice.solution.610017=æ£æ¥çº¿åé´è·ç¦»æ¯å¦å¨è§å®èå´å
notice.description.610018=åå°ç«¯åçµè¶æ¶
notice.solution.610018=æ 
notice.description.610019=åå°ç«¯FAULTä¿æ¤1
notice.solution.610019=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610020=åå°ç«¯FAULTä¿æ¤2
notice.solution.610020=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610021=åå°ç«¯FAULTä¿æ¤3
notice.solution.610021=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610022=åå°ç«¯FAULTä¿æ¤4
notice.solution.610022=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610023=åå°ç«¯FAULTä¿æ¤5
notice.solution.610023=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610024=åå°ç«¯FAULTä¿æ¤6
notice.solution.610024=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610025=åå°ç«¯æ¸©åº¦ä¿æ¤1
notice.solution.610025=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610026=åå°ç«¯æ¸©åº¦ä¿æ¤2
notice.solution.610026=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610027=åå°ç«¯æ¸©åº¦ä¿æ¤3
notice.solution.610027=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610028=åå°ç«¯æ¸©åº¦ä¿æ¤4
notice.solution.610028=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610029=åå°ç«¯æ¸©åº¦ä¿æ¤5
notice.solution.610029=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610030=åå°ç«¯æ¸©åº¦ä¿æ¤6
notice.solution.610030=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610031=TXå¯¼è½¨/çº¿åçµæµçµæµè¿ä½
notice.solution.610031=æ 
notice.description.610032=è°åº¦ç³»ç»å½ä»¤åæ­¢
notice.solution.610032=è¯·ç¡®è®¤è°åº¦ç³»ç»ç»æ­¢æ¾çµåå 
notice.description.610101=æ¥æ¶ç«¯è¾åºè¿å
notice.solution.610101=æ£æ¥åçµæºæ¥æ¶ç«¯æ¯å¦ç©ºè½½
notice.description.610102=æ¥æ¶ç«¯è¾åºè¿æµ
notice.solution.610102=æ´æ¢çµæ± éè¯ï¼ææ¥çµæ± é®é¢ï¼æ¥çä¸ä½æºè®¾ç½®å¼
notice.description.610103=æ¥æ¶ç«¯ç­è·¯ä¿æ¤
notice.solution.610103=ä¸ç¨è¡¨æµéè¾åºä¸¤ç«¯æ¯å¦ç­è·¯
notice.description.610104=æ¥æ¶ç«¯å¤æ­åæ»¡åæ­¢
notice.solution.610104=æ 
notice.description.610105=æ¥æ¶ç«¯æ¸©åº¦è¿é«
notice.solution.610105=æ¥çé£ææ¯å¦æ­£å¸¸è½¬å¨
notice.description.610106=æ¥æ¶ç«¯è¾å¥çµåè¿ä½
notice.solution.610106=å¦æé¢ç¹åºç°ï¼éåè¿ä¿®
notice.description.610107=æ¥æ¶ç«¯å¤é¨å½ä»¤åæ­¢
notice.solution.610107=æ 
notice.description.610108=æ¥æ¶ç«¯çµæ± æéåæ­¢
notice.solution.610108=è®¾ç½®å¼å¯åçµ
notice.description.610109=æ¥æ¶ç«¯ç¡¬ä»¶è¾åºè¿å
notice.solution.610109=æ£æ¥æ¯å¦çªç¶æ­è½½
notice.description.610110=æ¥æ¶ç«¯ç¡¬ä»¶è¾åºè¿æµ
notice.solution.610110=æ£æ¥çµæ± 
notice.description.610111=æ¥æ¶ç«¯ç¡¬ä»¶ç­è·¯ä¿æ¤
notice.solution.610111=ä¸ç¨è¡¨éåæ¯å¦æ­è·¯
notice.description.610112=æ¥æ¶ç«¯ BMSæªä½¿è½
notice.solution.610112=è®¾ç½®å¼å¯åçµ
notice.description.610113=æ¥æ¶ç«¯é£ææé
notice.solution.610113=æ£æ¥é£æ

notice.description.300108=æ¥åè§¦åæ¶é´è¿é¿
notice.solution.300108=è¯·æ£æ¥æºå¨äººç¶æ
notice.description.300402=é¿è®©è®¡ç®æ¶é´è¿é¿
notice.solution.300402=è¯·æ£æ¥ç¹ä½éç½®
notice.description.300109=åéåçµç¹/æ³è½¦ç¹æ¶é´è¿é¿
notice.solution.300109=è¯·æ£æ¥åçµç¹/æ³è½¦ç¹éç½®
notice.description.300110=ç³è¯·ç¹ä½/åºåæ¶é´è¿é¿
notice.solution.300110=è¯·æ£æ¥ç¹ä½/åºåéç½®
notice.description.300403=é¿è®©æ¬¡æ°å¼å¸¸
notice.solution.300403=è¯·èç³»Fleetå¼åäººåå¤ç


#log
log.export.interfaceLog.excelName=æ¥å£æ¥å¿
log.export.operationLog.excelName=æä½æ¥å¿
log.operation.description.success=æå
log.operation.description.fail=å¤±è´¥
log.third.system.operator=ä¸æ¸¸ç³»ç»

log.controller.api.task.create=[API]æ°å»ºä»»å¡
log.controller.api.task.cancel=[API]åæ¶ä»»å¡
log.controller.api.task.overNode=[API]è·³è¿ä»»å¡èç¹
log.controller.api.traffic.occupy=[API]ç³è¯·äº¤ç®¡åºå
log.controller.api.traffic.release=[API]éæ¾äº¤ç®¡åºå
log.controller.api.vehicle.operation=[API]æä½æºå¨äºº
log.controller.api.vehicle.globalPause=[API]å¨åºæå
log.controller.api.vehicle.globalResume=[API]å¨åºæ¢å¤
log.controller.language.delete=å é¤è¯­è¨
log.controller.language.import=å¯¼å¥è¯­è¨
log.controller.language.export=å¯¼åºè¯­è¨
log.controller.language.switch=åæ¢è¯­è¨
log.controller.license.upload=ä¸ä¼ è¯ä¹¦
log.controller.license.delete=å é¤è¯ä¹¦
log.controller.operationLog.delete=å é¤æä½æ¥å¿
log.controller.sysLog.delete=å é¤ç³»ç»æ¥å¿
log.controller.airShowerDoor.insert=æ°å¢é£æ·é¨
log.controller.airShowerDoor.update=ä¿®æ¹é£æ·é¨
log.controller.airShowerDoor.delete=å é¤é£æ·é¨
log.controller.airShowerDoor.open=æå¼é£æ·é¨
log.controller.airShowerDoor.close=å³é­é£æ·é¨
log.controller.autoDoor.insert=æ°å¢èªå¨é¨
log.controller.autoDoor.update=ä¿®æ¹èªå¨é¨
log.controller.autoDoor.delete=å é¤èªå¨é¨
log.controller.autoDoor.open=æå¼èªå¨é¨
log.controller.autoDoor.close=å³é­èªå¨é¨
log.controller.elevator.insert=æ°å¢çµæ¢¯
log.controller.elevator.update=ä¿®æ¹çµæ¢¯
log.controller.elevator.delete=å é¤çµæ¢¯
log.controller.elevator.import=å¯¼å¥çµæ¢¯
log.controller.elevator.export=å¯¼åºçµæ¢¯
log.controller.elevator.open=æå¼çµæ¢¯é¨
log.controller.elevator.close=å³é­çµæ¢¯é¨
log.controller.mapArea.insert=æ°å¢åºå
log.controller.mapArea.enable=å¯ç¨åºå
log.controller.mapArea.disable=ç¦ç¨åºå
log.controller.mapArea.update=ä¿®æ¹åç´ 
log.controller.mapArea.delete=å é¤åç´ 
log.controller.marker.insert=æ°å¢ç¹ä½
log.controller.marker.transcribe=å½å¶ç¹ä½
log.controller.marker.update=ä¿®æ¹åç´ 
log.controller.marker.delete=å é¤åç´ 
log.controller.path.insert=æ°å¢è·¯å¾
log.controller.path.update=ä¿®æ¹åç´ 
log.controller.path.delete=å é¤åç´ 
log.controller.vehicleMap.insert=æ°å¢å°å¾
log.controller.vehicleMap.update=ä¿®æ¹å°å¾
log.controller.vehicleMap.delete=å é¤å°å¾
log.controller.vehicleMap.batchDelete=æ¹éå é¤å°å¾
log.controller.vehicleMap.deleteDraft=å é¤èç¨¿æ°æ®
log.controller.vehicleMap.batchGenerateElement=æ¹éçæç¹ä½åè·¯å¾
log.controller.vehicleMap.batchUpdateElement=ä¿®æ¹åç´ 
log.controller.vehicleMap.batchDeleteElement=å é¤åç´ 
log.controller.vehicleMap.import=å¯¼å¥å°å¾
log.controller.vehicleMap.export=å¯¼åºå°å¾
log.controller.vehicleMap.copy=å¤å¶å°å¾
log.controller.vehicleMap.pause=å°å¾å¨åºæå
log.controller.vehicleMap.recover=å°å¾å¨åºæ¢å¤
log.controller.vehicleMap.publish=åå¸å°å¾
log.controller.vehicleMap.locatingMap.update=ä¿®æ¹å®ä½å¾
log.controller.vehicleMap.locatingMap.import=å¯¼å¥å®ä½å¾
log.controller.vehicleMap.locatingMap.changeDefault=åæ¢é»è®¤å®ä½å¾
log.controller.vehicleMap.locatingMap.delete=å é¤å®ä½å¾
log.controller.noticeConfig.insert=æ°å¢éç¥æ¨¡æ¿
log.controller.noticeConfig.update=ä¿®æ¹éç¥æ¨¡æ¿
log.controller.noticeConfig.delete=å é¤éç¥æ¨¡æ¿
log.controller.noticeConfig.export=å¯¼åºéç¥æ¨¡æ¿
log.controller.noticeConfig.import=å¯¼å¥éç¥æ¨¡æ¿
log.controller.noticeRecord.insert=åå»ºå¼å¸¸è®°å½
log.controller.noticeRecord.update=æ´æ°å¼å¸¸è®°å½
log.controller.noticeRecord.delete=å é¤å¼å¸¸è®°å½
log.controller.noticeRecord.activation=æ¿æ´»éç¥
log.controller.noticeRecord.ignore=å¿½ç¥éç¥
log.controller.noticeRecord.ignoreVehicle=å¿½ç¥æºå¨äººéç¥
log.controller.noticeRecord.export=å¯¼åºéç¥åè¡¨
log.controller.pda.config=(PDA)éç½®çæ¬ä¿¡æ¯
log.controller.pda.containerEnter=(PDA)å®¹å¨è¿åº
log.controller.pda.containerExit=(PDA)å®¹å¨åºåº
log.controller.pda.execute=(PDA)åå»ºä»»å¡
log.controller.security.login=ç»å½ç³»ç»
log.controller.security.logout=éåºç»å½ç³»ç»
log.controller.sys.menu.insert=æ°å¢èå
log.controller.sys.menu.update=ä¿®æ¹èå
log.controller.sys.menu.delete=å é¤èå
log.controller.sys.role.insert=æ°å¢è§è²
log.controller.sys.role.update=ä¿®æ¹è§è²
log.controller.sys.role.delete=å é¤è§è²
log.controller.sys.property.batchUpdate=æ¹éä¿®æ¹ç³»ç»éç½®
log.controller.sys.property.insert=å¢å ç³»ç»å±æ§
log.controller.sys.property.update=ä¿®æ¹ç³»ç»éç½®
log.controller.sys.property.delete=å é¤ç³»ç»å±æ§
log.controller.sys.user.password=ä¿®æ¹å¯ç 
log.controller.sys.user.password.reset=éç½®å¯ç 
log.controller.sys.user.insert=æ°å¢ç¨æ·
log.controller.sys.user.update=ä¿®æ¹ç¨æ·
log.controller.sys.user.delete=å é¤ç¨æ·
log.controller.task.nodeConfig.insert=æ°å¢èç¹ç±»å
log.controller.task.nodeConfig.update=ä¿®æ¹èç¹ç±»å
log.controller.task.nodeConfig.delete=å é¤èç¹ç±»å
log.controller.task.nodeConfig.batchCommon=è®¾ç½®èç¹å¸¸ç¨
log.controller.task.nodeConfig.export=å¯¼åºèç¹ç±»å
log.controller.task.nodeConfig.import=å¯¼å¥èç¹ç±»å
log.controller.task.insert=æ°å¢ä»»å¡
log.controller.task.cancel=åæ¶ä»»å¡
log.controller.task.delete=å é¤ä»»å¡
log.controller.task.skip=è·³è¿èç¹
log.controller.task.retry=éè¯èç¹
log.controller.task.batchCancel=æ¹éåæ¶ä»»å¡
log.controller.task.cancelAll=ä¸é®åæ¶ä»»å¡
log.controller.task.export=ä¸è½½è®°å½
log.controller.task.import=ä¸ä¼ è®°å½
log.controller.task.remark=æ·»å ä»»å¡å¤æ³¨
log.controller.task.type.insert=æ°å¢ä»»å¡æµç¨
log.controller.task.type.update=ä¿®æ¹ä»»å¡æµç¨
log.controller.task.type.copy=å¤å¶ä»»å¡æµç¨
log.controller.task.type.delete=å é¤ä»»å¡æµç¨
log.controller.task.type.enable=å¯ç¨ä»»å¡æµç¨
log.controller.task.type.disable=ç¦ç¨ä»»å¡æµç¨
log.controller.task.type.export=å¯¼åºä»»å¡æµç¨
log.controller.task.type.import=å¯¼å¥ä»»å¡æµç¨
log.controller.vehicle.stop.open=å¼å¯æå
log.controller.vehicle.stop.close=å³é­æå
log.controller.vehicle.delete=å é¤æºå¨äºº
log.controller.vehicle.restart=éå¯æºå¨äºº
log.controller.vehicle.shutdown=å³é­æºå¨äºº
log.controller.vehicle.controls.manualMode=åæ¢ä¸ºæå¨æ§å¶æ¨¡å¼
log.controller.vehicle.controls.autoMode=åæ¢ä¸ºèªå¨æ§å¶æ¨¡å¼
log.controller.vehicle.scheduler.manualMode=åæ¢ä¸ºæå¨è°åº¦æ¨¡å¼
log.controller.vehicle.scheduler.autoMode=åæ¢ä¸ºèªå¨è°åº¦æ¨¡å¼
log.controller.vehicle.update=ä¿®æ¹æºå¨äººéç½®
log.controller.vehicle.updateBatch=æ¹éä¿®æ¹æºå¨äººéç½®
log.controller.vehicle.updateGroupBatch=æ¹éä¿®æ¹æºå¨äººåç»
log.controller.vehicle.updateTypeBatch=æ¹éä¿®æ¹æºå¨äººç±»å
log.controller.vehicle.resource.clear=ç¦»åºæºå¨äºº
log.controller.vehicle.reset=å¤ä½æºå¨äºº
log.controller.vehicle.dockingReset=å¯¹æ¥å¤ä½
log.controller.vehicle.closeSoundLightAlarm=å³é­å£°åæ¥è­¦
log.controller.vehicle.charge=æ®éåçµ
log.controller.vehicle.group.insert=æ°å¢æºå¨äººç»
log.controller.vehicle.group.update=ä¿®æ¹æºå¨äººç»
log.controller.vehicle.group.delete=å é¤æºå¨äººç»
log.controller.vehicle.group.export=å¯¼åºæºå¨äººç»
log.controller.vehicle.group.import=å¯¼å¥æºå¨äººç»
log.controller.vehicle.type.insert=æ°å¢æºå¨äººç±»å
log.controller.vehicle.type.update=ä¿®æ¹æºå¨äººç±»å
log.controller.vehicle.type.delete=å é¤æºå¨äººç±»å
log.controller.vehicle.type.export=å¯¼åºæºå¨äººç±»å
log.controller.vehicle.type.import=å¯¼å¥æºå¨äººç±»å
log.controller.vehicle.map.appoint=æå®å°å¾
log.controller.vehicle.map.relocation=éå®ä½æºå¨äºº
log.controller.warehouse.area.insert=æ°å¢åºä½åºå
log.controller.warehouse.area.update=ä¿®æ¹åºä½åºå
log.controller.warehouse.area.delete=å é¤åºä½åºå
log.controller.warehouse.area.export=å¯¼åºåºä½åºå
log.controller.warehouse.area.import=å¯¼å¥åºä½åºå
log.controller.warehouse.type.insert=æ°å¢åºä½ç±»å
log.controller.warehouse.type.update=ä¿®æ¹åºä½ç±»å
log.controller.warehouse.type.delete=å é¤åºä½ç±»å
log.controller.warehouse.type.export=å¯¼åºåºä½ç±»å
log.controller.warehouse.type.import=å¯¼å¥åºä½ç±»å
log.controller.warehouse.insert=æ°å¢åºä½
log.controller.warehouse.batchInsert=æ¹éæ°å¢åºä½
log.controller.warehouse.update=ä¿®æ¹åºä½
log.controller.warehouse.delete=å é¤åºä½
log.controller.warehouse.enable=å¯ç¨åºä½
log.controller.warehouse.disable=ç¦ç¨åºä½
log.controller.warehouse.export=å¯¼åºåºä½
log.controller.warehouse.import=å¯¼å¥åºä½
log.controller.event.insert=æ°å¢äºä»¶
log.controller.event.update=ä¿®æ¹äºä»¶
log.controller.event.copy=å¤å¶äºä»¶
log.controller.event.delete=å é¤äºä»¶
log.controller.event.enable=å¯ç¨äºä»¶
log.controller.event.disable=ç¦ç¨äºä»¶
log.controller.event.export=å¯¼åºäºä»¶
log.controller.event.import=å¯¼å¥äºä»¶
log.controller.charge.station.update=ä¿®æ¹åçµæ¡©
log.controller.charge.station.delete=å é¤åçµæ¡©
log.controller.charge.station.enable=å¯ç¨åçµæ¡©
log.controller.charge.station.disable=ç¦ç¨åçµæ¡©
log.controller.charge.station.reset=åçµæ¡©å¤ä½
log.controller.charge.station.stopCharge=åçµæ¡©ç»æ­¢æ¾çµ

log.operation.excel.head.operator=ç¨æ·
log.operation.excel.head.description=æä½
log.operation.excel.head.success=ç»æ
log.operation.excel.head.errorMsg=å¤±è´¥åå 
log.operation.excel.head.wasteTime=ååºæ¶é¿
log.operation.excel.head.ip=å®¢æ·ç«¯ipå°å
log.operation.excel.head.paramsIn=è¯·æ±ä¿¡æ¯
log.operation.excel.head.paramsOut=è¿åä¿¡æ¯
log.operation.excel.head.operationTime=æä½æ¶é´

log.interface.excel.head.description=è¯¦æ
log.interface.excel.head.success=ç»æ
log.interface.excel.head.errorMsg=å¤±è´¥åå 
log.interface.excel.head.wasteTime=ååºæ¶é¿
log.interface.excel.head.url=URL
log.interface.excel.head.paramsIn=è¯·æ±ä¿¡æ¯
log.interface.excel.head.paramsOut=è¿åä¿¡æ¯
log.interface.excel.head.operationTime=åå»ºæ¶é´

log.system.module.task=ä»»å¡
log.system.module.task.allocation=æºå¨äººè°åº¦
log.system.module.charge.allocation=åçµè°åº¦
log.system.module.park.allocation=æ³è½¦è°åº¦
log.system.module.resource.apply=äº¤éèµæº
log.system.module.traffic.avoid=äº¤éé¿è®©
log.system.module.vehicle=æºå¨äºº
log.system.module.event=äºä»¶
log.system.module.system=ç³»ç»
log.system.module.autodoor=èªå¨é¨
log.system.module.airshowerdoor=é£æ·é¨
log.system.module.elevator=çµæ¢¯
log.system.module.charge.station=åçµæ¡©

log.system.type.Running=è¿è¡æ¥å¿
log.system.type.Warning=åè­¦æ¥å¿
log.system.type.Error=éè¯¯æ¥å¿

log.system.system.start=è°åº¦ç³»ç»å¯å¨æå
log.system.path.plan.is.unreachable=è·¯å¾è§åå¤±è´¥, ç®æ ç¹ä¸å¯è¾¾
log.system.instruction.status.upload=æ¶å°æºå¨äººæä»¤ç¶æåé¦
log.system.resource.elevator.apply.success=çµæ¢¯å°è¾¾ç³è¯·æ¥¼å±å¹¶å·²å¼é¨
log.system.resource.elevator.ride.success=çµæ¢¯å°è¾¾ç®æ æ¥¼å±å¹¶å·²å¼é¨
log.system.resource.vehicle.clear.resource=æºå¨äººç½ç»è¿æ¥è¶æ¶ï¼èªå¨æ¸çæºå¨äººå ç¨çèµæº

log.system.vehicle.connect=æºå¨äººè¿å¥è°åº¦ç³»ç»
log.system.vehicle.disconnect=æºå¨äººæ­å¼è¿æ¥è°åº¦ç³»ç»
log.system.vehicle.out.of.trace=å·²è±è½¨
log.system.vehicle.on.trace=æªè±è½¨
log.system.vehicle.pause.close=å³é­æå
log.system.vehicle.pause.open=å¼å¯æå
log.system.vehicle.close.stop=æºå¨äººå·²è¢«æ¢å¤æ¥åæé®
log.system.vehicle.open.stop=æºå¨äººå·²è¢«æä¸æ¥åæé®
log.system.vehicle.manual.control=æºå¨äººå·²è¢«åæ¢ä¸ºæå¨æ§å¶æ¨¡å¼
log.system.vehicle.auto.control=æºå¨äººå·²è¢«åæ¢ä¸ºèªå¨æ§å¶æ¨¡å¼
log.system.vehicle.repair.control=æºå¨äººå·²è¢«åæ¢ä¸ºæ£ä¿®æ§å¶æ¨¡å¼
log.system.vehicle.work.status.work=å·¥ä½
log.system.vehicle.work.status.free=ç©ºé²
log.system.vehicle.connect.status.connect=å·²è¿æ¥
log.system.vehicle.connect.status.disconnect=å·²æ­å¼
log.system.vehicle.control.status.manual=æå¨
log.system.vehicle.control.status.auto=èªå¨
log.system.vehicle.control.status.repair=æ£ä¿®
log.system.vehicle.abnormal.status.abnormal=å¼å¸¸
log.system.vehicle.abnormal.status.normal=æ å¼å¸¸
log.system.vehicle.position.status.notLocated=æªå®ä½
log.system.vehicle.position.status.located=å·²å®ä½

log.system.charge.scheduler.error=æºå¨äººåçµåéå¼å¸¸
log.system.charge.create.task.success=æºå¨äººåå»ºåçµä»»å¡æå
log.system.charge.create.task.fail=æºå¨äººåå»ºåçµä»»å¡å¤±è´¥
log.system.charge.vehicle.disable=æºå¨äººæªå¯ç¨èªå¨åçµ
log.system.charge.battery.value.is.null=æºå¨äººå½åçµéæ¯ç©ºå¼
log.system.charge.no.usable.charge.marker=æºå¨äººæ å¯ç¨åç¹çµ
log.system.charge.get.other.charge.marker=æºå¨äººçµéè¿ä½ï¼æ¢å å¶ä»æºå¨äººçåçµç¹
log.system.park.scheduler.error=æºå¨äººæ³è½¦åéå¼å¸¸
log.system.park.vehicle.disable=æºå¨äººæªå¯ç¨èªå¨æ³è½¦
log.system.park.no.usable.park.marker=æºå¨äººæ å¯ç¨æ³è½¦ç¹
log.system.park.create.task.success=æºå¨äººåå»ºæ³è½¦ä»»å¡æå
log.system.park.create.task.fail=æºå¨äººåå»ºæ³è½¦ä»»å¡å¤±è´¥

log.system.traffic.marker.is.not.avaliable.error=æºå¨äººæ æ³åå¾é¿è®©ç¹ä½ï¼éæ°è§åè·¯å¾
log.system.traffic.resource.conflict=å¤ä¸ªæºå¨äººä¹é´è·¯å¾å²çªï¼æºå¨äººéæ°è§åè·¯å¾å°ç¹ä½
log.system.traffic.detect.obstacle=æºå¨äººæ£æµå°éç¢ç©ï¼å¼å§éæ°è§åè·¯å¾
log.system.traffic.detect.vehicle=æºå¨äººæ£æµå°åæ¹ææºå¨äººé»æ¡, å¼å§éæ°è§åè·¯å¾
log.system.traffic.detect.control.area=æºå¨äººéå°å°æ§åºåï¼å¼å§éæ°è§åè·¯å¾
log.system.traffic.detect.map.publish=æºå¨äººéæ°è§åè·¯å¾ï¼ç¨æ·æå¨åå¸äºå°å¾
log.system.traffic.detect.vehicle.error=ç³»ç»æ£æµå°æºå¨äººåæ¹è¢«é»æ¡ï¼ç»è·¯å¤±è´¥
log.system.traffic.detect.vehicle.drive=æºå¨äººæ£æµå°åæ¹ææºå¨äººé»æ¡, é©±èµ¶æºå¨äºº, é©±èµ¶å°ç¹ä½
log.system.traffic.detect.vehicle.drive.error=ç³»ç»æ£æµå°æºå¨äººåæ¹è¢«é»æ¡ï¼é©±èµ¶å¤±è´¥

log.system.auto.door.thread.error=èªå¨é¨ç¨åºå¼å¸¸
log.system.auto.door.connect.error=èªå¨é¨è¿æ¥å¤±è´¥
log.system.auto.door.no.bind.path.error=èªå¨é¨æªç»å®è·¯å¾
log.system.auto.door.read.error=èªå¨é¨è¯»åæä»¤å¤±è´¥
log.system.auto.door.write.error=èªå¨é¨åå¥æä»¤å¤±è´¥
log.system.auto.door.open.ok=èªå¨é¨å¼é¨æå
log.system.auto.door.close.ok=èªå¨é¨å³é¨æå

log.system.air.shower.thread.error=é£æ·é¨ç¨åºå¼å¸¸
log.system.air.shower.no.bind.path.error=é£æ·é¨æªç»å®è·¯å¾
log.system.air.shower.connect.error=é£æ·é¨è¿æ¥å¤±è´¥
log.system.air.shower.read.error=é£æ·é¨è¯»åæä»¤å¤±è´¥
log.system.air.shower.write.error=é£æ·é¨åå¥æä»¤å¤±è´¥
log.system.air.shower.open.ok=é£æ·é¨å¼é¨æå
log.system.air.shower.close.ok=é£æ·é¨å³é¨æå

log.system.elevator.thread.error=çµæ¢¯ç¨åºå¼å¸¸
log.system.elevator.no.bind.path.error=çµæ¢¯æªç»å®ç¹ä½
log.system.elevator.vehicle.leave=æºå¨äººç¦»å¼çµæ¢¯æå
log.system.elevator.vehicle.apply.run=æºå¨äººç³è¯·çµæ¢¯åé
log.system.elevator.connect.error=çµæ¢¯è¿æ¥å¤±è´¥
log.system.elevator.read.error=çµæ¢¯è¯»åæä»¤å¤±è´¥
log.system.elevator.write.error=çµæ¢¯åå¥æä»¤å¤±è´¥

log.system.task.allocation.error=æºå¨äººä»»å¡åéå¼å¸¸
log.system.task.allocation.cancel.old.task.success=åä»»å¡åæ¶å®æ, æ°ä»»å¡ç»å®æºå¨äºº
log.system.task.allocation.interrupt.current.task=ä»»å¡åéæºå¨äºº, åæ¶æºå¨äººæ­£å¨æ§è¡çä»»å¡
log.system.task.allocation.success=ä»»å¡åéæåï¼æºå¨äºº
log.system.task.allocation.fail.vehicle.no.exist=æå®æºå¨äººåéå¤±è´¥ï¼æºå¨äººä¸å­å¨
log.system.task.allocation.fail.vehicle.state.error=æå®æºå¨äººåéå¤±è´¥ï¼æºå¨äººç¶æä¸ç¬¦å
log.system.task.allocation.fail.vehicle.locked=æå®æºå¨äººåéå¤±è´¥ï¼æºå¨äººå·²è¢«ä»»å¡å ç¨
log.system.task.start.run=ä»»å¡å¼å§æ§è¡
log.system.task.run.error=ä»»å¡æ§è¡å¼å¸¸
log.system.task.run.finish=ä»»å¡æ§è¡å®æ
log.system.task.cancel.run=ä»»å¡åæ¶æ§è¡
log.system.task.status.change.callback.request.param=ä»»å¡ç¶æåæ´ï¼è§¦åHTTPåè°
log.system.task.status.change.callback.response.param=ä»»å¡ç¶æåæ´ï¼HTTPåè°å®æ

log.system.node.start=èç¹å¼å§æ§è¡
log.system.node.end=èç¹å®ææ§è¡
log.system.node.cancel=èç¹åæ¶æ§è¡
log.system.node.error=èç¹æ§è¡å¼å¸¸
log.system.node.no.available.marker=åéç¹ä½å¤±è´¥ï¼æ å¯ç¨çç¹ä½
log.system.node.start.send=èç¹æ§è¡ï¼æºå¨äººå¼å§ä¸åæä»¤
log.system.node.send.succeed=èç¹æ§è¡ï¼æºå¨äººä¸åæä»¤æå
log.system.node.send.error=èç¹æ§è¡ï¼æºå¨äººä¸åæä»¤å¤±è´¥
log.system.node.run.succeed=èç¹æ§è¡ï¼æºå¨äººå®ææä»¤
log.system.node.run.error=èç¹æ§è¡ï¼æºå¨äººæ§è¡æä»¤å¤±è´¥
log.system.node.start.send.cancel=èç¹æ§è¡ï¼æºå¨äººå¼å§ä¸ååæ­¢æä»¤
log.system.node.send.cancel.succeed=èç¹æ§è¡ï¼æºå¨äººä¸ååæ­¢æä»¤æå
log.system.node.send.cancel.error=èç¹æ§è¡ï¼æºå¨äººä¸ååæ­¢æä»¤å¤±è´¥
log.system.node.stop.succeed=èç¹æ§è¡ï¼æºå¨äººåæ­¢æä»¤æå
log.system.node.stop.error=èç¹æ§è¡ï¼æºå¨äººåæ­¢æä»¤å¤±è´¥
log.system.node.cancel.timeout=èç¹æ§è¡ï¼æºå¨äººåæ­¢æä»¤è¶æ¶ï¼ç³»ç»å¼ºå¶åæ­¢ä»»å¡
log.system.node.button.release.succeed=æ¾è¡æé®èç¹ï¼è®¾å¤æ¾è¡æå
log.system.node.button.release.error=æ¾è¡æé®èç¹å¼å¸¸ï¼æ æ³è¿æ¥è®¾å¤
log.system.node.button.reset.succeed=éç½®æé®èç¹æ§è¡æå
log.system.node.button.reset.error=éç½®æé®èç¹æ§è¡å¼å¸¸ï¼æ æ³è¿æ¥è®¾å¤
log.system.node.vehicle.no.assign.error=èç¹æ§è¡ï¼æªéå®æºå¨äºº
log.system.node.vehicle.no.exist.error=èç¹æ§è¡ï¼æºå¨äººä¸å­å¨
log.system.node.vehicle.disconnect.error=æºå¨äººç§»å¨èç¹ï¼ æºå¨äººç¶ææ£éªå¤±è´¥, æºå¨äººæ­å¼è¿æ¥
log.system.node.vehicle.abnormal.error=æºå¨äººç§»å¨èç¹ï¼ æºå¨äººç¶ææ£éªå¤±è´¥, æºå¨äººå­å¨å¼å¸¸
log.system.node.vehicle.state.error=èç¹æ§è¡ï¼æºå¨äººç¶æä¸ç¬¦å
log.system.node.vehicle.move.state.change.re.pathplan=æºå¨äººç§»å¨èç¹ï¼ æºå¨äººç¶æåçååï¼éæ°è§åè·¯å¾
log.system.node.vehicle.move.send.instruction=æºå¨äººç§»å¨èç¹ï¼ æºå¨äººä¸åç§»å¨æä»¤
log.system.node.vehicle.move.finish=æºå¨äººç§»å¨èç¹ï¼ æºå¨äººç§»å¨æä»¤å®æ
log.system.node.vehicle.move.error=æºå¨äººç§»å¨èç¹ï¼ æºå¨äººç§»å¨æä»¤æ§è¡å¤±è´¥
log.system.node.vehicle.move.send.cancel=æºå¨äººç§»å¨èç¹ï¼ åéåæ¶ç§»å¨æä»¤
log.system.node.vehicle.move.send.cancel.fail=æºå¨äººç§»å¨èç¹ï¼ åéåæ¶ç§»å¨æä»¤å¤±è´¥
log.system.node.vehicle.move.cancel.success=æºå¨äººç§»å¨èç¹ï¼ ç§»å¨æä»¤åæ¶æå
log.system.node.vehicle.move.cancel.fail=æºå¨äººç§»å¨èç¹ï¼ ç§»å¨æä»¤åæ¶å¤±è´¥
log.system.node.vehicle.move.no.position=æºå¨äººç§»å¨èç¹ï¼ æºå¨äººæ²¡æä½ç½®æ°æ®
log.system.node.vehicle.move.stay.tartget.marker=æºå¨äººç§»å¨èç¹ï¼ æºå¨äººå·²å¨ç®æ ç¹
log.system.node.vehicle.move.pathplan.start=æºå¨äººç§»å¨èç¹ï¼æºå¨äººå¼å§è·¯å¾è§å
log.system.node.vehicle.move.pathplan.success=æºå¨äººç§»å¨èç¹ï¼æºå¨äººè·¯å¾è§åæå
log.system.node.vehicle.move.pathplan.message=æºå¨äººç§»å¨èç¹ï¼çæè·¯å¾è§åæ°æ®
log.system.node.vehicle.move.pathplan.cancel=æºå¨äººç§»å¨èç¹ï¼æ£æµå°è·¯å¾å¯¼èªæ¤é
log.system.node.vehicle.move.re.pathplan=æºå¨äººç§»å¨èç¹ï¼æºå¨äººä¸å¨è§åçè·¯å¾ä¸ï¼éæ°è§åè·¯å¾
log.system.node.vehicle.move.stop.button=æºå¨äººç§»å¨èç¹ï¼æºå¨äººå·²è¢«æä¸æ¥åæé®
log.system.node.set.variable.param.is.empty=è®¾å®åéèç¹, æ¾ä¸å°è®¾å®æ°å­åéçåæ°
log.system.node.set.variable.param.change.error=è®¾å®åéèç¹, åæ°ä¸è½è½¬ä¸ºæ°å­åé
log.system.node.set.variable.param.source.is.unknown=è®¾å®åéèç¹, æ¾ä¸å°åæ°æ¥æº
log.system.node.set.variable.param.is.change=è®¾å®åéèç¹, è®¾å®åéèç¹å°åéåç±è®¾ç½®ä¸º
log.system.node.finish.request.param=ä»»å¡ç»æHTTPåè°ååºè¯·æ±
log.system.node.finish.response.param=ä»»å¡ç»æHTTPåè°æ¥æ¶ååº
log.system.node.http.request.param=æ§è¡Httpè¯·æ±èç¹, ååºè¯·æ±
log.system.node.http.response.param=æ§è¡Httpè¯·æ±èç¹, æ¥æ¶ååº
log.system.node.http.check.param=HTTPæ ¡éªèç¹ï¼ç¼ºå°æ ¡éªå¼
log.system.node.http.check.path=HTTPåæ°æ ¡éªå¤±è´¥ï¼æ²¡ææ¾å°å¯¹åºçå­æ®µ
log.system.node.http.check.fail=HTTPåæ°æ ¡éªå¤±è´¥

log.system.trigger.callbox.success=è§¦åå¼å«çæé®äºä»¶ï¼åå»ºä»»å¡æå
log.system.trigger.callbox.fail=è§¦åå¼å«çæé®äºä»¶ï¼åå»ºä»»å¡å¤±è´¥
log.system.trigger.fix.success=è§¦åå®æ¶äºä»¶ï¼åå»ºä»»å¡æå
log.system.trigger.fix.fail=è§¦åå®æ¶äºä»¶ï¼åå»ºä»»å¡å¤±è´¥
log.system.trigger.plc.success=è§¦åå¯å­å¨äºä»¶ï¼åå»ºä»»å¡æå
log.system.trigger.plc.fail=è§¦åå¯å­å¨äºä»¶ï¼åå»ºä»»å¡å¤±è´¥
log.system.trigger.task.cancel.success=è§¦åä»»å¡åæ¶äºä»¶ï¼åå»ºä»»å¡æå
log.system.trigger.task.cancel.fail=è§¦åä»»å¡åæ¶äºä»¶ï¼åå»ºä»»å¡å¤±è´¥
log.system.trigger.task.finish.success=è§¦åä»»å¡å®æäºä»¶ï¼åå»ºä»»å¡æå
log.system.trigger.task.finish.fail=è§¦åä»»å¡å®æäºä»¶ï¼åå»ºä»»å¡å¤±è´¥
log.system.trigger.vehicle.abnormal.success=è§¦åæºå¨äººå¼å¸¸äºä»¶ï¼åå»ºä»»å¡æå
log.system.trigger.vehicle.abnormal.fail=è§¦åæºå¨äººå¼å¸¸äºä»¶ï¼åå»ºä»»å¡å¤±è´¥
log.system.trigger.vehicle.plc.success=è§¦åæºå¨äººå¯å­å¨äºä»¶ï¼åå»ºä»»å¡æå
log.system.trigger.vehicle.plc.fail=è§¦åæºå¨äººå¯å­å¨äºä»¶ï¼åå»ºä»»å¡å¤±è´¥

log.system.export.error=åæ¬¡å¯¼åºæ°éä¸è½è¶è¿10000æ¡ï¼è¯·éæ°ç­é
log.system.export.name=è¿è¡æ¥å¿
log.system.download.file.not.exist=æ¾ä¸å°æå®æä»¶ï¼æ æ³ä¸è½½
log.system.download.file.error=æä»¶ä¸è½½å¤±è´¥

log.system.excel.head.module=ç±»å«
log.system.excel.head.type=ç­çº§
log.system.excel.head.content=æè¿°
log.system.excel.head.data=æ°æ®
log.system.excel.head.message=æ¥æ
log.system.excel.head.vehicleCodes=æºå¨äºº
log.system.excel.head.taskNos=ä»»å¡
log.system.excel.head.createDate=åå»ºæ¶é´
log.system.excel.head.lastTime=æåæ´æ°æ¶é´

log.system.charge.station.connect=åçµæ¡©è¿æ¥æå
log.system.charge.station.disconnect=åçµæ¡©æ­å¼è¿æ¥
log.system.charge.station.operate=åçµæ¡©æä½ç»æ
log.system.charge.station.cancel.charge.task=ç±äºåçµè¢«å¼ºå¶åæ­¢ï¼èç¹åæ¶æ§è¡

#validation
validation.id.require=IDä¸è½ä¸ºç©º
validation.id.null=IDå¿é¡»ä¸ºç©º
validation.pid.require=ä¸çº§IDï¼ä¸è½ä¸ºç©º
validation.sort.number=æåºå¼ä¸è½å°äº0
validation.sysparams.paramcode.require=åæ°ç¼ç ä¸è½ä¸ºç©º
validation.sysparams.paramvalue.require=åæ°å¼ä¸è½ä¸ºç©º
validation.sysuser.username.require=ç¨æ·åä¸è½ä¸ºç©º
validation.sysuser.password.require=å¯ç ä¸è½ä¸ºç©º
validation.sysuser.realname.require=å§åä¸è½ä¸ºç©º
validation.sysuser.email.require=é®ç®±ä¸è½ä¸ºç©º
validation.sysuser.email.error=é®ç®±æ ¼å¼ä¸æ­£ç¡®
validation.sysuser.mobile.require=ææºå·ä¸è½ä¸ºç©º
validation.sysuser.superadmin.range=è¶çº§ç®¡çååå¼èå´0~1
validation.sysuser.status.range=ç¶æåå¼èå´0~1
validation.sysmenu.pid.require=è¯·éæ©ä¸çº§èå
validation.sysmenu.name.require=èååç§°ä¸è½ä¸ºç©º
validation.sysmenu.type.range=èåç±»ååå¼èå´0~1
validation.sysrole.name.require=è§è²åç§°ä¸è½ä¸ºç©º
validation.schedule.status.range=ç¶æåå¼èå´0~1
validation.schedule.cron.require=cronè¡¨è¾¾å¼ä¸è½ä¸ºç©º
validation.schedule.bean.require=beanåç§°ä¸è½ä¸ºç©º
validation.news.title.require=æ é¢ä¸è½ä¸ºç©º
validation.news.content.require=åå®¹ä¸è½ä¸ºç©º
validation.news.pubdate.require=åå¸æ¶é´ä¸è½ä¸ºç©º
validation.map.marker.name=ç¹ä½åç§°å½åè§åï¼ä¸ºç©ºï¼æèå­æ¯ææ°å­ãä¸åçº¿ï¼å­æ¯å¼å¤´ï¼é¿åº¦ä¸è¶è¿20ä¸ªå­ç¬¦
validation.map.marker.type.require=ç¹ä½ç±»åä¸è½ä¸ºç©º
validation.map.marker.code.require=ç¹ä½ç¼å·ä¸è½ä¸ºç©º
validation.map.marker.type=ç¹ä½ç±»åå¿é¡»æ¯å¦ä¸å¶ä¸­ä¹ä¸ï¼ChargingMarker,NavigationMarker,WorkMarker
validation.map.marker.x.require=xåæ ä¸è½ä¸ºç©º
validation.map.marker.y.require=yåæ ä¸è½ä¸ºç©º
validation.map.path.type.require=è·¯å¾ç±»åä¸è½ä¸ºç©º
validation.map.path.type=è·¯å¾ç±»åå¿é¡»æ¯å¦ä¸å¶ä¸­ä¹ä¸ï¼Commonãæ®éè·¯å¾ï¼QR_Downãäºç»´ç å¯¹æ¥è·¯å¾ï¼Shelflegsãè´§æ¶è¿å¯¹æ¥ï¼Symbol_VãVåæ¿å¯¹æ¥ï¼Reflectorãååæ¿å¯¹æ¥ï¼LeaveDockingãè±ç¦»å¯¹æ¥ï¼Palletãæçå¯¹æ¥
validation.map.path.startMarkerCode.require=å¼å§æ è®°ç¹ä¸è½ä¸ºç©º
validation.map.path.endMarkerCode.require=ç»ææ è®°ç¹ä¸è½ä¸ºç©º
validation.map.path.weightRatio.require=è·¯å¾æéå¿é¡»æ¯æ­£æ°
validation.map.area.areaType.require=åºåç±»åä¸è½ä¸ºç©ºï¼æä¸¾å¼ï¼ åæºåºå:SingleAgvArea æ¾ç¤ºåºå: ShowArea å°æ§åºåï¼ControlArea ééåºåï¼ChannelArea ç¦æåºåï¼NoRotatingArea ç¦ååºåï¼NoParkingArea
validation.map.area.areaType=åºåç±»åå¿é¡»æ¯å¦ä¸å¶ä¸­ä¹ä¸ï¼SingleAgvArea,ShowArea,ControlArea,ChannelArea,NoRotatingArea,NoParkingArea
validation.map.area.polygon.require=åºååæ åè¡¨ä¸è½ä¸ºç©º
validation.map.area.operateType.require=æå»ºåºåçæ¹å¼ä¸è½ä¸ºç©ºï¼æä¸¾å¼ï¼1ãå¤è¾¹å½¢åºåæ¹å¼æå»ºã2ãç©å½¢åºåæ¹å¼æå»º
validation.map.type.require=å°å¾ç±»åä¸è½ä¸ºç©º
validation.map.code.require=å°å¾ç¼ç ä¸è½ä¸ºç©º
validation.map.name.require=å°å¾åç§°ä¸è½ä¸ºç©º
validation.map.originX.require=å°å¾ä¸­å¿xåæ ä¸è½ä¸ºç©º
validation.map.originY.require=å°å¾ä¸­å¿yåæ ä¸è½ä¸ºç©º
validation.map.resolution.require=å°å¾åè¾¨çä¸è½ä¸ºç©º
validation.map.height.require=å°å¾åç´ é«åº¦ä¸è½ä¸ºç©º
validation.map.width.require=å°å¾åç´ å®½åº¦ä¸è½ä¸ºç©º
validation.door.code.require=èªå¨é¨ç¼ç ä¸è½ä¸ºç©º
validation.door.ip.require=èªå¨é¨ipä¸è½ä¸ºç©º
validation.door.port.require=èªå¨é¨portä¸è½ä¸ºç©º
validation.door.openAddress.require=èªå¨é¨å¼é¨å°åä¸è½ä¸ºç©º
validation.door.openStatusAddress.require=èªå¨é¨å¼é¨ç¶æå°åä¸è½ä¸ºç©º
validation.door.closeAddress.require=èªå¨é¨å³é¨å°åä¸è½ä¸ºç©º
validation.door.closeStatusAddress.require=èªå¨é¨å³é¨ç¶æå°åä¸è½ä¸ºç©º
validation.door.pathCodes.require=èªå¨é¨å³èçè·¯å¾ç¼ç ä¸è½ä¸ºç©º
validation.elevator.code.require=çµæ¢¯ç¼ç ä¸è½ä¸ºç©º
validation.elevator.ip.require=çµæ¢¯ipä¸è½ä¸ºç©º
validation.elevator.port.require=çµæ¢¯portä¸è½ä¸ºç©º
validation.elevator.controlAddress.require=çµæ¢¯æ§å¶å°åä¸è½ä¸ºç©º
validation.elevator.destAddress.require=çµæ¢¯ç®çå°åä¸è½ä¸ºç©º
validation.elevator.openAddress.require=çµæ¢¯å¼é¨å°åä¸è½ä¸ºç©º
validation.elevator.readFunctionCode.require=çµæ¢¯Modbusè¯»åè½ç ä¸è½ä¸ºç©º
validation.elevator.writeFunctionCode.require=çµæ¢¯Modbusååè½ç ä¸è½ä¸ºç©º
validation.property.type.require=ç³»ç»å±æ§çç±»åä¸è½ä¸ºç©º
validation.property.category.require=ç³»ç»å±æ§çåç±»ä¸è½ä¸ºç©º
validation.property.propertyKey.require=ç³»ç»å±æ§çé®å¼ä¸è½ä¸ºç©º
validation.property.valueType.require=ç³»ç»å±æ§çå¼ç±»åä¸è½ä¸ºç©º


#vehicleMap
vehicleMap.airShowerDoor.not.exist.error=æä½å¤±è´¥ï¼é£æ·é¨[%s]ä¸å­å¨
vehicleMap.airShowerDoor.add.error=æ°å¢é£æ·é¨å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.airShowerDoor.update.error=ä¿®æ¹é£æ·é¨[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.airShowerDoor.delete.error=å é¤é£æ·é¨[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.airShowerDoor.bind.path.error=é£æ·é¨[%s]ç»å®è·¯å¾å¼å¸¸ï¼ç»å®éå¤è·¯å¾

vehicleMap.autoDoor.not.exist.error=æä½å¤±è´¥ï¼èªå¨é¨[%s]ä¸å­å¨
vehicleMap.autoDoor.already.bind.other.device.error=æä½å¤±è´¥ï¼è·¯å¾[%s]å·²ç»å®è®¾å¤
vehicleMap.autoDoor.add.error=æ°å¢èªå¨é¨å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.autoDoor.update.error=ä¿®æ¹èªå¨é¨[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.autoDoor.delete.error=å é¤èªå¨é¨[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿

vehicleMap.elevator.add.error=æ°å¢çµæ¢¯å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.elevator.update.error=ä¿®æ¹çµæ¢¯[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.elevator.delete.error=å é¤çµæ¢¯[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.elevator.not.exist.error=æä½å¤±è´¥ï¼çµæ¢¯[%s]ä¸å­å¨
vehicleMap.elevator.file.format.error=çµæ¢¯æä»¶æ ¼å¼éè¯¯
vehicleMap.elevator.import.already.exist.error=çµæ¢¯å¯¼å¥å¼å¸¸ï¼çµæ¢¯[%s]å·²å­å¨
vehicleMap.elevator.import.error=çµæ¢¯å¯¼å¥å¼å¸¸ï¼åå ï¼%s
vehicleMap.elevator.export.error=çµæ¢¯å¯¼åºå¼å¸¸ï¼åå ï¼%s
vehicleMap.elevator.publish.check.error=å°å¾åå¸æ£æ¥ï¼å³èçµæ¢¯æ­£å¨è¢«ä½¿ç¨ï¼æ¯å¦å¼ºå¶åå¸
vehicleMap.elevator.import.bind.map.error=çµæ¢¯[%s]å¯¼å¥å¤±è´¥ï¼è¯·åå¯¼å¥çµæ¢¯ç»å®çææå°å¾
vehicleMap.elevator.bind.multi.marker.error=çµæ¢¯ç»å®å¼å¸¸ï¼çµæ¢¯ä¸è½ç»å®åä¸å°å¾çå¤ä¸ªç¹ä½

vehicleMap.mapArea.not.exist.error=æä½å¤±è´¥ï¼åºå[%s]ä¸å­å¨
vehicleMap.mapArea.add.error=æ°å¢åºåå¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.mapArea.update.error=ä¿®æ¹åºå[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.mapArea.update.occupied.error=ä¿®æ¹åºå[%s]å¤±è´¥ï¼å½åèµæºæ­£å¨è¢«ä½¿ç¨
vehicleMap.mapArea.delete.error=å é¤åºå[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿

vehicleMap.marker.add.error=æ°å¢ç¹ä½å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.marker.update.error=ä¿®æ¹ç¹ä½[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.marker.delete.error=å é¤ç¹ä½[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.marker.not.exist.error=æä½å¤±è´¥ï¼ç¹ä½[%s]ä¸å­å¨
vehicleMap.marker.already.bind.other.device.error=æä½å¤±è´¥ï¼ç¹ä½[%s]å·²ç»å®è®¾å¤
vehicleMap.marker.spacing.error=ç¹ä½é´è·å°äºè®¾å®å¼[%s]

vehicleMap.path.bind.marker.no.exist.error=æä½å¤±è´¥ï¼è·¯å¾ç»å®çç¹ä½[%s]ä¸å­å¨
vehicleMap.path.already.bind.device.error=æä½å¤±è´¥ï¼è·¯å¾[%s]å·²ç»å®è®¾å¤
vehicleMap.path.already.exist.error=æä½å¤±è´¥ï¼è·¯å¾[%s]å·²å­å¨
vehicleMap.path.not.exist.error=æä½å¤±è´¥ï¼è·¯å¾[%s]ä¸å­å¨
vehicleMap.path.add.error=æ°å¢è·¯å¾å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.path.update.error=ä¿®æ¹è·¯å¾[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.path.delete.error=å é¤è·¯å¾[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿

vehicleMap.map.operating.duplicate.error=æ§è¡ä¸­ï¼è¯·å¿éå¤æä½
vehicleMap.map.not.exist.error=å°å¾[%s]ä¸å­å¨ï¼è¯·éåºå°å¾ç¼è¾é¡µé¢
vehicleMap.map.file.format.error=å¯¼å¥çæä»¶[%s]æ ¼å¼ä¸æ­£ç¡®
vehicleMap.map.add.error=æ°å¢å°å¾å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.map.update.error=ä¿®æ¹å°å¾[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.map.delete.error=å é¤å°å¾[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.map.roadnet.update.error=ä¿®æ¹å°å¾[%s]è·¯ç½åç´ å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.map.roadnet.delete.error=å é¤å°å¾[%s]è·¯ç½åç´ å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.map.draft.delete.error=å é¤å°å¾[%s]è·¯ç½èç¨¿å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.map.is.not.publish.error=å°å¾[%s]æ²¡ææ­£å¼çæ¬
vehicleMap.map.import.error=å°å¾å¯¼å¥å¼å¸¸ï¼åå ï¼%s
vehicleMap.map.import.structure.error=å¯¼å¥çå°å¾æä»¶æ ¼å¼ä¸æ­£ç¡®
vehicleMap.map.import.in.edit.page.error=è¯·å¨å°å¾ç¼è¾é¡µé¢ä¸­å¯¼å¥å®ä½å¾
vehicleMap.map.import.missing.info.error=å¯¼å¥çå°å¾ç¼ºå¤±infoæä»¶
vehicleMap.map.import.missing.png.error=å¯¼å¥çå°å¾ç¼ºå¤±pngæä»¶
vehicleMap.map.import.missing.locating.error=å¯¼å¥çå°å¾æä»¶æ ¼å¼ä¸æ­£ç¡®ï¼è·¯ç½æä»¶æ²¡æå®ä½å¾ä¿¡æ¯
vehicleMap.map.import.appoint.default.error=å¯¼å¥çå°å¾æä»¶æ ¼å¼ä¸æ­£ç¡®ï¼è·¯ç½æä»¶æ²¡ææå®é»è®¤å®ä½å¾
vehicleMap.map.export.error=å°å¾å¯¼åºå¼å¸¸ï¼åå ï¼%s
vehicleMap.map.copy.error=å°å¾å¤å¶å¼å¸¸ï¼åå ï¼%s
vehicleMap.map.reset.error=å½åå°å¾[%s]æ²¡æå¯æ¤éçæä½
vehicleMap.map.recover.error=å½åå°å¾[%s]æ²¡æå¯æ¢å¤çæä½
vehicleMap.map.global.recover.error=æºå¨äººæ¢å¤å¤±è´¥ï¼%s
vehicleMap.map.publish.occupied.error=åå¸å°å¾å¤±è´¥ï¼å½åèµæº[%s]æ­£å¨è¢«ä½¿ç¨

vehicleMap.map.locatingmap.is.empty.error=å°å¾[%s]å®ä½å¾æ°æ®ä¸ºç©º!
vehicleMap.map.locatingmap.code.is.empty.error=å°å¾[%s]å®ä½å¾ç¼ç ä¸ºç©º!
vehicleMap.map.locatingmap.not.exist.error=å®ä½å¾[%s]ä¸å­å¨ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.map.locatingmap.update.error=ä¿®æ¹å®ä½å¾[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
vehicleMap.map.locatingmap.default.error=ä¸è½å é¤[%s]çé»è®¤å®ä½å¾[%s]
vehicleMap.map.locatingmap.import.structure.error=å¯¼å¥çå®ä½å¾æä»¶ç®å½ç»æå¼å¸¸
vehicleMap.map.locatingmap.import.file.is.empty.error=å¯¼å¥çå®ä½å¾æä»¶åè¡¨ä¸ºç©º
vehicleMap.map.locatingmap.import.file.is.missing.error=å¯¼å¥çå®ä½å¾[%s]æä»¶ç¼ºå¤±
vehicleMap.map.locatingmap.import.file.is.duplicate.error=è¯¥å®ä½å¾%så·²å­å¨äºå°å¾%sä¸­ï¼è¯·æ£æ¥ååæä½
vehicleMap.map.locatingmap.import.file.is.forbidden.error=è¯¥ç¼ç å®ä½å¾%sä¸åè®¸å¯¼å¥ï¼è¯·æ£æ¥ååæä½
vehicleMap.map.locatingmap.export.error=å¯¼åºå®ä½å¾[%s]å¼å¸¸ï¼åå ï¼%s

#device
device.connect.error=æä½è®¾å¤å¤±è´¥ï¼è®¾å¤[%s]ç½ç»æªè¿æ¥
device.open.error=å¼å¯è®¾å¤[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
device.close.error=å³é­è®¾å¤[%s]å¼å¸¸ï¼è¯·æ¥çæä½æ¥å¿
device.is.in.use.error=æä½è®¾å¤å¤±è´¥ï¼è®¾å¤[%s]æ­£å¨ä½¿ç¨ä¸­

#task
task.node.config.export.file.name=èç¹è®¾ç½®
task.node.config.export.error=èç¹è®¾ç½®å¯¼åºå¼å¸¸ï¼åå ï¼%s
task.node.config.import.error=èç¹è®¾ç½®å¯¼å¥å¼å¸¸ï¼åå ï¼%s
task.node.is.not.exist.error=ä»»å¡èç¹[%s]ä¸å­å¨
task.node.is.not.allow.retry.error=ä»»å¡èç¹[%s]ä¸åè®¸éè¯
task.node.is.not.allow.skip.error=ä»»å¡èç¹[%s]ä¸åè®¸è¢«è·³è¿
task.type.is.not.published.error=æ°å¢ä»»å¡å¤±è´¥ï¼ä½¿ç¨äºæªåå¸çä»»å¡æµç¨[%s]
task.type.is.not.exist.error=ä»»å¡æµç¨[%s]ä¸å­å¨
task.type.export.error=ä»»å¡æµç¨å¯¼åºå¼å¸¸ï¼åå ï¼%s
task.type.import.error=ä»»å¡æµç¨å¯¼å¥å¼å¸¸ï¼åå ï¼%s
task.type.node.is.empty.error=å¯ç¨ä»»å¡å¤±è´¥ï¼ä»»å¡æµç¨[%s]ç¼ºå°å¯ç¨çèç¹
task.type.enable.el.parse.error=å¯ç¨å¤±è´¥ï¼åå ä¸ºï¼%s
task.type.enable.param.is.null.error=å­å¨æªè®¾å®åæ°çèç¹ï¼[%s]
task.type.node.while.is.empty.error=å­å¨ç©ºå¾ªç¯çèç¹ï¼[%s]
task.type.prefix.name=ä»»å¡æµç¨
task.is.not.exist.error=ä»»å¡[%s]ä¸å­å¨
task.delete.running.error=æ æ³å é¤æ§è¡ä¸­çä»»å¡
task.cancel.running.error=åæ¶å¤±è´¥ï¼è¯¥ä»»å¡ç¦æ­¢åæ¶
task.export.file.name=ä»»å¡
task.export.error=ä»»å¡å¯¼åºå¼å¸¸ï¼åå ï¼%s
task.import.error=ä»»å¡å¯¼å¥å¼å¸¸ï¼åå ï¼%s
task.import.code.duplicate.error=ä¸ä¼ ä»»å¡è®°å½å¤±è´¥ï¼éå¤çä»»å¡ç¼ç [%s]
task.cancel.timeout.error=åæ¶æºå¨äººæä»¤è¶æ¶ï¼è¯·éå¯æºå¨äºº[%s]æ¸çæä»¤
task.insert.vehicle.not.exist.error=æ°å¢ä»»å¡å¤±è´¥ï¼è¾å¥çæºå¨äºº[%s]ä¸å­å¨
task.insert.marker.not.exist.error=æ°å¢ä»»å¡å¤±è´¥ï¼è¾å¥çç¹ä½[%s]ä¸å­å¨
task.insert.marker.lock.error=è¯¥ç¹ä½[%s]å·²è¢«å¶å®ä»»å¡å ç¨
task.insert.map.not.exist.error=æ°å¢ä»»å¡å¤±è´¥ï¼è¾å¥çå°å¾[%s]ä¸å­å¨
task.insert.dynamic.param.format.error=ä¼ å¥çå¨æåæ°[%s]æ ¼å¼[%s]ä¸æ­£ç¡®
task.excel.head.taskNo=ç¼å·
task.excel.head.externalTaskNo=å¤é¨ç¼ç 
task.excel.head.name=åç§°
task.excel.head.status=ç¶æ
task.excel.head.priority=ä¼åçº§
task.excel.head.vehicleCodes=æºå¨äºº
task.excel.head.source=åå»ºæ¹å¼
task.excel.head.createDate=åå»ºæ¶é´
task.excel.head.startTime=å¼å§æ¶é´
task.excel.head.endTime=ç»ææ¶é´
task.excel.head.remark=å¤æ³¨
task.excel.head.callbackUrl=ä¸æ¸¸åè°å°å

task.event.not.exist.error=äºä»¶[%s]ä¸å­å¨
task.event.bound.taskType.is.null.error=äºä»¶[%s]æªå³èä»»å¡æµç¨
task.event.running.duplicate.error=å½åäºä»¶ä¸åè®¸éå¤ï¼ä¸æè¿è¡ä¸­çä»»å¡
task.event.plc.condition.check.fail.error=å¯å­å¨è§¦åæ¡ä»¶æ ¡éªä¸éè¿ï¼è¯·æ£æ¥
task.event.vehicle.condition.check.fail.error=æºå¨äººå¯å­å¨è§¦åæ¡ä»¶æ ¡éªä¸éè¿ï¼è¯·æ£æ¥
task.event.fix.interval.time.error=é´éæ¶é´è®¾ç½®æè¯¯ï¼è¯·æ£æ¥
task.event.relate.task.contain.cancel.node.error=åæ¶ä»»å¡èç¹å³èçä»»å¡[%s]ä¸­ä¸è½åå«åæ¶ä»»å¡èç¹
task.event.relate.task.create.error=åæ¶ä»»å¡èç¹å³èçä»»å¡[%s]åå»ºå¤±è´¥ï¼åå ï¼[%s]

task.event.type.fixedTime=å®æ¶äºä»¶
task.event.type.button=æé®äºä»¶
task.event.type.plc=å¯å­å¨äºä»¶
task.event.type.vehiclePlc=æºå¨äººå¯å­å¨äºä»¶
task.event.type.vehicleAbnormal=æºå¨äººå¼å¸¸äºä»¶
task.event.type.taskCancel=ä»»å¡åæ¶äºä»¶
task.event.type.taskFinished=ä»»å¡å®æäºä»¶
task.event.status.enable=å¯ç¨
task.event.status.disable=ç¦ç¨
task.event.repeat.allow=åè®¸
task.event.repeat.disallow=ç¦æ­¢
task.event.export.file.name=äºä»¶

task.event.excel.head.code=äºä»¶ç¼ç 
task.event.excel.head.name=äºä»¶åç§°
task.event.excel.head.type=äºä»¶ç±»å
task.event.excel.head.isAllowRepeat=æ¯å¦åè®¸éå¤
task.event.excel.head.taskTypeId=ä»»å¡ç±»å
task.event.excel.head.status=å¯ç¨ç¶æ
task.event.excel.head.param=äºä»¶åæ°
task.event.excel.head.taskParam=ä»»å¡åæ°

task.node.name.Wait=ç­å¾
task.node.name.DynamicAllocationVehicle=å¨æåéæºå¨äºº
task.node.name.AllocationMarker=éæºåéç¹ä½
task.node.name.ReleaseVehicle=éæ¾æºå¨äºº
task.node.name.VehicleRotation=æºå¨äººæè½¬
task.node.name.TrayRotation=æçæè½¬
task.node.name.TrayLifting=æçåé
task.node.name.ReadPlc=è¯»å¯å­å¨
task.node.name.WritePlc=åå¯å­å¨
task.node.name.AssignAllocationVehicle=æå®åéæºå¨äºº
task.node.name.VehicleMove=æºå¨äººç§»å¨
task.node.name.GetMarkerAttribute=è·åç¹ä½å±æ§
task.node.name.DockingCharge=å¯¹æ¥åçµ
task.node.name.ButtonRelease=æé®æ¾è¡
task.node.name.ButtonReset=æé®éç½®
task.node.name.Alarm=å£°åæ¥è­¦
task.node.name.ReadVehiclePlc=è¯»æºå¨äººå¯å­å¨
task.node.name.WriteVehiclePlc=åæºå¨äººå¯å­å¨
task.node.name.Rotation=ç»åæè½¬
task.node.name.RobotArmScript=æºæ¢°èæ§å¶
task.node.name.CheckPlc=æ ¡éªå¯å­å¨
task.node.name.PlayAudio=æ­æ¾é³é¢
task.node.name.SwitchSpeedArea=åæ¢éåº¦åºå
task.node.name.SwitchDockingArea=åæ¢å¯¹æ¥åºå
task.node.name.TrayFollowControl=æçæ¨¡å¼åæ¢
task.node.name.CheckVehiclePlc=æ ¡éªæºå¨äººå¯å­å¨
task.node.name.ForkArmLifting=åèåé
task.node.name.GetTaskAttribute=è·åä»»å¡å±æ§
task.node.name.FinishTask=ç»æä»»å¡
task.node.name.HttpRequest=Http è¯·æ±
task.node.name.NobodyForkCharge=åè½¦åçµ
task.node.name.DockingNavigation=å¯¹æ¥
task.node.name.SwitchScheduleMode=åæ¢è°åº¦æ¨¡å¼
task.node.name.GetVehicleAttribute=è·åæºå¨äººå±æ§
task.node.name.AllocationWarehouse=åéåºä½
task.node.name.UpdateWarehouse=æ´æ°åºä½
task.node.name.GetWarehouseAttribute=è·ååºä½å±æ§
task.node.name.GetBarcodeAttribute=è·åæ¡ç å±æ§
task.node.name.GetAdjacentMarker=è·åç¸é»ç¹ä½
task.node.name.Ajust=å¾®è°
task.node.name.LeaveDocking=è±ç¦»å¯¹æ¥
task.node.name.StopAudio=åæ­¢æ­æ¾é³é¢
task.node.name.ReadQrCode=è¯»åäºç»´ç 
task.node.name.CheckSensorObstacleStatus=åè½¦æ¾è´§æ£æµ
task.node.name.SetVariable=è®¾å®åé
task.node.name.Notice=éç¥æ¥è­¦
task.node.name.CancelTask=åæ¶ä»»å¡
task.node.name.ForbidCancelTask=ç¦æ­¢åæ¶ä»»å¡
task.node.name.OperateMapArea=æä½å°å¾åºå
task.node.name.HttpCheck=Httpæ ¡éª
task.node.name.JavaScript=Javaèæ¬
task.node.name.ReadPLCStr=è¯»å¯å­å¨-å­ç¬¦ä¸²
task.node.name.WritePLCStr=åå¯å­å¨-å­ç¬¦ä¸²
task.node.name.LockMarker=éå®ç¹ä½
task.node.name.StopCharge=åæ­¢åçµ

task.node.notice.Wait=ç­å¾ä¸æ®µæ¶é´ååæ§è¡åç»­èç¹
task.node.notice.DynamicAllocationVehicle=éæºåéå¹¶å ç¨æºå¨äººï¼å ç¨æååæ§è¡åç»­èç¹
task.node.notice.AllocationMarker=éæºåéä¸ä¸ªçç¹ä½
task.node.notice.ReleaseVehicle=ä»»å¡éæ¾å¯¹è¯¥æºå¨äººçå ç¨ï¼è¢«éæ¾çæºå¨äººå¯ä»¥ç«å³è¢«å¶å®ä»»å¡å ç¨
task.node.notice.VehicleRotation=æ§å¶æºå¨äººæè½¬
task.node.notice.TrayRotation=æ§å¶æºå¨äººçæçæè½¬
task.node.notice.TrayLifting=æ§å¶æºå¨äººçæçåé
task.node.notice.ReadPlc=è¯»è¿ç¨å¯å­å¨çå¼
task.node.notice.WritePlc=åå¤é¨å¯å­å¨åå¥å¼
task.node.notice.AssignAllocationVehicle=ä»»å¡å ç¨æå®çæºå¨äººï¼å ç¨æååæ§è¡åç»­èç¹
task.node.notice.VehicleMove=ä¸ºæºå¨äººè§åè·¯å¾å¹¶æ§å¶æºå¨äººåå¾ç®æ ç¹
task.node.notice.GetMarkerAttribute=è·åå¹¶è¾åºç¹ä½å±æ§ç»å¶å®èç¹ä½¿ç¨
task.node.notice.DockingCharge=æ§å¶æºå¨äººèªå¨å¯¹æ¥åçµæ¡©
task.node.notice.ButtonRelease=æä¸ä¼è¾å¼å«çæé®åï¼æ§è¡åç»­èç¹
task.node.notice.ButtonReset=å¯éç½®å¼å«çæé®ï¼è¢«éç½®çæé®å¯éæ°è§¦å
task.node.notice.Alarm=æ§å¶å£°åæ¥è­¦å¨ååºæ¥è­¦
task.node.notice.ReadVehiclePlc=è¯»åæºå¨äººåé¨çå¯å­å¨å¼
task.node.notice.WriteVehiclePlc=åæºå¨äººåé¨çå¯å­å¨åå¥å¼
task.node.notice.Rotation=æ§å¶æºå¨äººåæçä¸èµ·æè½¬
task.node.notice.RobotArmScript=ä½¿ç¨Jsonèæ¬æ§å¶æºæ¢°èè¿å¨
task.node.notice.CheckPlc=æ ¡éªå¯å­å¨
task.node.notice.PlayAudio=æ­æ¾é³é¢
task.node.notice.SwitchSpeedArea=åæ¢éåº¦åºå
task.node.notice.SwitchDockingArea=åæ¢å¯¹æ¥åºå
task.node.notice.TrayFollowControl=æçæ¨¡å¼åæ¢
task.node.notice.CheckVehiclePlc=æ ¡éªæºå¨äººå¯å­å¨
task.node.notice.ForkArmLifting=æ§å¶æ äººåè½¦çåèåé
task.node.notice.GetTaskAttribute=è·åä»»å¡å±æ§
task.node.notice.FinishTask=ç»æä»»å¡
task.node.notice.HttpRequest=åå¤é¨ç³»ç»åéè¯·æ±ï¼å¹¶ç­å¾æ§è¡ç»æ
task.node.notice.NobodyForkCharge=æ äººåè½¦èç¹
task.node.notice.DockingNavigation=å¯¹æ¥
task.node.notice.SwitchScheduleMode=åæ¢è°åº¦æ¨¡å¼
task.node.notice.GetVehicleAttribute=è·åæºå¨äººå±æ§
task.node.notice.AllocationWarehouse=åéåºä½
task.node.notice.UpdateWarehouse=æ´æ°åºä½
task.node.notice.GetWarehouseAttribute=è·ååºä½å±æ§
task.node.notice.GetBarcodeAttribute=è·åæ¡ç å±æ§
task.node.notice.GetAdjacentMarker=è·åç¸é»ç¹ä½
task.node.notice.Ajust=å¾®è°
task.node.notice.LeaveDocking=è±ç¦»å¯¹æ¥
task.node.notice.StopAudio=åæ­¢æ­æ¾é³é¢
task.node.notice.ReadQrCode=è¯»åäºç»´ç 
task.node.notice.CheckSensorObstacleStatus=åè½¦æå°ä¼ æå¨æ¯å¦è§¦åé¿é,æ¯:è¡¨ç¤ºè§¦åé¿é, false è¡¨ç¤º æªè§¦åé¿é
task.node.notice.SetVariable=è¯¥èç¹å¯ä»¥éæ°è®¾ç½®åéçå¼
task.node.notice.Notice=éç¥æ¥è­¦
task.node.notice.CancelTask=åæ¶ä»»å¡äºä»¶
task.node.notice.ForbidCancelTask=ç¦æ­¢åæ¶ä»»å¡
task.node.notice.OperateMapArea=æä½å°å¾åºå
task.node.notice.HttpCheck=HTTPï¼POSTè¯·æ±ï¼ç»ææ ¡éª
task.node.notice.JavaScript=Javaèæ¬èç¹
task.node.notice.ReadPLCStr=è¯»åPLCçASCIIè½¬ä¸ºå­ç¬¦ä¸²
task.node.notice.WritePLCStr=åå¥PLC ASCIIç 
task.node.notice.LockMarker=éå®ç¹ä½
task.node.notice.StopCharge=åæ­¢åçµ

task.node.param.name.1718202092436357121.vehicleCode.In=æºå¨äºº
task.node.param.name.1718202092436357121.position.In=ç®æ é«åº¦
task.node.param.name.1718202092436357121.offsetHeight.In=åå·®é«åº¦
task.node.param.name.1718202092436357121.speed.In=éåº¦
task.node.param.name.1738448135527288834.vehicleCode.In=æºå¨äºº
task.node.param.name.1738448135527288834.markerCode.In=åçµç¹
task.node.param.name.1738448135527288834.chargeTime.In=åçµæ¶é´
task.node.param.name.1738448135527288834.batteryCharge.In=åçµçµé
task.node.param.name.1738443040093851650.finishType.In=ç»æç±»å
task.node.param.name.1738443040093851650.noticeMsg.In=ç»ææé
task.node.param.name.1646764086215663617.vehicleCode.In=æºå¨äºº
task.node.param.name.1646764086215663617.vehicleAngle1.In=æºå¨äººè§åº¦1
task.node.param.name.1646764086215663617.vehicleAngle2.In=æºå¨äººè§åº¦2
task.node.param.name.1646764086215663617.trayRotationSpeed.In=æçæè½¬éåº¦
task.node.param.name.1646764086215663617.trayAngle1.In=æçè§åº¦1
task.node.param.name.1646764086215663617.trayAngle2.In=æçè§åº¦2
task.node.param.name.1715183824889581570.vehicleCode.In=æºå¨äºº
task.node.param.name.1715183824889581570.ladarSwitch.In=éåº¦åºå
task.node.param.name.1715184972354686978.vehicleCode.In=æºå¨äºº
task.node.param.name.1715184972354686978.obstacleArea.In=é¿éèå´
task.node.param.name.1738467719873515521.vehicleCode.In=æºå¨äºº
task.node.param.name.1738467719873515521.scheduleMode.In=è°åº¦æ¨¡å¼
task.node.param.name.1715183168871075842.vehicleCode.In=æºå¨äºº
task.node.param.name.1715183168871075842.audioName.In=é³é¢åç§°
task.node.param.name.1715183168871075842.audioVolume.In=é³é
task.node.param.name.1715183168871075842.playCount.In=æ­æ¾æ¬¡æ°
task.node.param.name.1630863227598438401.markerCode.In=ç¹ä½
task.node.param.name.1630863227598438401.vehicleGroupCode.In=æºå¨äººç»
task.node.param.name.1630863227598438401.vehicleTypeCode.In=æºå¨äººç±»å
task.node.param.name.1630863227598438401.vehicleMapCodeList.In=å°å¾
task.node.param.name.1630863227598438401.limitBattery.In=çµé
task.node.param.name.1630863227598438401.outVehicleCode.Out=æºå¨äºº
task.node.param.name.1630863227745239041.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227745239041.outVehicleCode.Out=æºå¨äºº
task.node.param.name.1630863227644575745.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227623604225.vehicleMapCodeList.In=å°å¾
task.node.param.name.1630863227623604225.markerType.In=ç¹ä½ç±»å
task.node.param.name.1630863227623604225.outMarkerCode.Out=éä¸­ç¹ä½
task.node.param.name.1630863227623604225.vehicleCode.In=æºå¨äºº
task.node.param.name.1738440272671100929.taskNo.Out=ä»»å¡ç¼ç 
task.node.param.name.1738440272671100929.externalTaskNo.Out=å¤é¨ä»»å¡ç¼ç 
task.node.param.name.1738440272671100929.priority.Out=ä¼åçº§
task.node.param.name.1630863227707490306.ip.In=IP
task.node.param.name.1630863227707490306.port.In=ç«¯å£
task.node.param.name.1630863227707490306.code.In=åè½ç 
task.node.param.name.1630863227707490306.slaveId.In=ä»ç«ID
task.node.param.name.1630863227707490306.address.In=è¯»åå°å
task.node.param.name.1630863227707490306.executeMode.In=æ§è¡æ¹å¼
task.node.param.name.1630863227707490306.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227707490306.outValue.Out=è¾åºå¼
task.node.param.name.1645676364679905282.vehicleCode.In=æºå¨äºº
task.node.param.name.1645676364679905282.code.In=åè½ç 
task.node.param.name.1645676364679905282.slaveId.In=ä»ç«ID
task.node.param.name.1645676364679905282.address.In=è¯»åå°å
task.node.param.name.1645676364679905282.outValue.Out=å¯å­å¨å¼
task.node.param.name.1630863227724267521.ip.In=IP
task.node.param.name.1630863227724267521.port.In=ç«¯å£
task.node.param.name.1630863227724267521.code.In=åè½ç 
task.node.param.name.1630863227724267521.slaveId.In=ä»ç«ID
task.node.param.name.1630863227724267521.address.In=åå¥å°å
task.node.param.name.1630863227724267521.value.In=åå¥å¼
task.node.param.name.1630863227724267521.executeMode.In=æ§è¡æ¹å¼
task.node.param.name.1630863227724267521.vehicleCode.In=æºå¨äºº
task.node.param.name.1645678201743114241.vehicleCode.In=æºå¨äºº
task.node.param.name.1645678201743114241.code.In=åè½ç 
task.node.param.name.1645678201743114241.slaveId.In=ä»ç«ID
task.node.param.name.1645678201743114241.address.In=åå¥å°å
task.node.param.name.1645678201743114241.value.In=åå¥å¼
task.node.param.name.1715188504537468930.vehicleCode.In=æºå¨äºº
task.node.param.name.1715188504537468930.code.In=åè½ç 
task.node.param.name.1715188504537468930.slaveId.In=ä»ç«ID
task.node.param.name.1715188504537468930.address.In=å¯å­å¨å°å
task.node.param.name.1715188504537468930.successVal.In=æ­£å¸¸éåº
task.node.param.name.1715188504537468930.failVal.In=å¤±è´¥éåº
task.node.param.name.1715188504537468930.timeout.In=è¶æ¶æ¶é´
task.node.param.name.1715188504537468930.outValue.Out=è¾åºå¼
task.node.param.name.1641376178617024513.callBoxCode.In=å¼å«çç¼å·
task.node.param.name.1641376178617024513.buttonCode.In=æé®ç¼å·
task.node.param.name.1641376178617024513.timeout.In=è¶æ¶æ¾è¡ï¼ç§ï¼
task.node.param.name.1641376553134817282.callBoxCode.In=å¼å«çç¼å·
task.node.param.name.1641376553134817282.buttonCode.In=æé®ç¼å·
task.node.param.name.1641377688272863233.ip.In=IP
task.node.param.name.1641377688272863233.port.In=ç«¯å£
task.node.param.name.1641377688272863233.type.In=æ¥è­¦ç±»å
task.node.param.name.1641377688272863233.time.In=æç»­æ¶é´ï¼ç§ï¼
task.node.param.name.1742437277025456130.warehouseCode.In=åºä½
task.node.param.name.1742437277025456130.containerBarcode.In=å®¹å¨
task.node.param.name.1742437277025456130.dispatchPolicy.In=åéç­ç¥
task.node.param.name.1742437277025456130.occupyStatus.In=åºä½ç¶æ
task.node.param.name.1742437277025456130.warehouseTypeCode.In=åºä½ç±»å
task.node.param.name.1742437277025456130.warehouseAreaCode.In=åºåº
task.node.param.name.1742437277025456130.extendParam1.In=æ©å±å±æ§1
task.node.param.name.1742437277025456130.extendParam2.In=æ©å±å±æ§2
task.node.param.name.1742437277025456130.extendParam3.In=æ©å±å±æ§3
task.node.param.name.1742437277025456130.extendParam4.In=æ©å±å±æ§4
task.node.param.name.1742437277025456130.extendParam5.In=æ©å±å±æ§5
task.node.param.name.1742437277025456130.extendParam6.In=æ©å±å±æ§6
task.node.param.name.1742437277025456130.extendParam7.In=æ©å±å±æ§7
task.node.param.name.1742437277025456130.extendParam8.In=æ©å±å±æ§8
task.node.param.name.1742437277025456130.extendParam9.In=æ©å±å±æ§9
task.node.param.name.1742437277025456130.extendParam10.In=æ©å±å±æ§10
task.node.param.name.1742437277025456130.warehouseCode.Out=åºä½ç¼ç 
task.node.param.name.1742441148997193730.containerBarCode.In=å®¹å¨æ¡ç 
task.node.param.name.1742441148997193730.warehouseCode.Out=åºåºç¼ç 
task.node.param.name.1742441148997193730.workMarkerCode.Out=ä½ä¸ç¹ä½ç¼ç 
task.node.param.name.1742441148997193730.workHeight.Out=ä½ä¸é«åº¦
task.node.param.name.1742441148997193730.warehouseTypeCode.Out=åºä½ç±»åç¼ç 
task.node.param.name.1742441148997193730.warehouseAreaCode.Out=åºä½åºåç¼ç 
task.node.param.name.1742440444115046401.warehouseCode.In=åºä½
task.node.param.name.1742440444115046401.containerBarcode.Out=å®¹å¨æ¡ç 
task.node.param.name.1742440444115046401.workMarkerCode.Out=ä½ä¸ç¹ä½ç¼ç 
task.node.param.name.1742440444115046401.workHeight.Out=ä½ä¸é«åº¦
task.node.param.name.1742440444115046401.warehouseTypeCode.Out=åºä½ç±»åç¼ç 
task.node.param.name.1742440444115046401.warehouseAreaCode.Out=åºä½åºåç¼ç 
task.node.param.name.1742441529047273474.markerCode.In=ç¹ä½
task.node.param.name.1742441529047273474.markerCode.Out=ç¸é»ç¹ä½ç¼ç 
task.node.param.name.1630863227652964354.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227652964354.angle.In=æè½¬è§åº¦
task.node.param.name.1630863227652964354.rateType.In=æè½¬ç±»å
task.node.param.name.1630863227673935874.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227673935874.angle1.In=æè½¬è§åº¦1
task.node.param.name.1630863227673935874.angle2.In=æè½¬è§åº¦2
task.node.param.name.1630863227673935874.speed.In=æè½¬éåº¦
task.node.param.name.1630863227673935874.rateType.In=æè½¬ç±»å
task.node.param.name.1750822156822622210.vehicleCode.In=æºå¨äºº
task.node.param.name.1750822156822622210.offsetX.In=åå·® X
task.node.param.name.1750822156822622210.offsetY.In=åå·® Y
task.node.param.name.1750822156822622210.offsetAngle.In=åå·®è§åº¦
task.node.param.name.1750822156822622210.rotationAngleRange.In=æè½¬èå´
task.node.param.name.1750822156822622210.moveDistanceRange.In=ç§»å¨èå´
task.node.param.name.1750822156822622210.obstacleRegion.In=é¿éåºå
task.node.param.name.1738450017482133505.vehicleCode.In=æºå¨äºº
task.node.param.name.1738450017482133505.dockingType.In=ç±»å
task.node.param.name.1738450017482133505.startX.In=èµ·ç¹ X åæ 
task.node.param.name.1738450017482133505.startY.In=èµ·ç¹ Y åæ 
task.node.param.name.1738450017482133505.startAngle.In=èµ·ç¹è§åº¦
task.node.param.name.1738450017482133505.endX.In=ç»ç¹ X åæ 
task.node.param.name.1738450017482133505.endY.In=ç»ç¹ Y åæ 
task.node.param.name.1738450017482133505.endAngle.In=ç»ç¹è§åº¦
task.node.param.name.1738450017482133505.offsetX.In=åå·® X
task.node.param.name.1738450017482133505.offsetY.In=åå·® Y
task.node.param.name.1738450017482133505.offsetAngle.In=åå·®è§åº¦
task.node.param.name.1738450017482133505.workStationCode.In=å·¥ä½ç¼ç 
task.node.param.name.1738450017482133505.templateNo.In=æ¨¡æ¿ç¼å·
task.node.param.name.1738450017482133505.cameraObstacle.In=ç¸æºé¿é
task.node.param.name.1738450017482133505.obstacleRegion.In=é¿éåºå
task.node.param.name.1751081370463637506.vehicleCode.In=æºå¨äºº
task.node.param.name.1738450017482133505.dockingDirection.In=å¯¹æ¥æ¹å
task.node.param.name.1715186203621986306.vehicleCode.In=æºå¨äºº
task.node.param.name.1715186203621986306.type.In=æè½¬æ¨¡å¼
task.node.param.name.1630863227694907394.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227694907394.speed.In=åééåº¦
task.node.param.name.1630863227694907394.targetTicks.In=åéé«åº¦
task.node.param.name.1751878094551769090.vehicleCode.In=æºå¨äºº
task.node.param.name.1751878094551769090.outValue.Out=äºç»´ç ä¿¡æ¯
task.node.param.name.1751825844022235138.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227787182081.markerCode.In=ç¹ä½
task.node.param.name.1630863227787182081.markerName.In=èªå®ä¹ç¼ç 
task.node.param.name.1630863227787182081.code.Out=ç¹ä½ç¼ç 
task.node.param.name.1630863227787182081.name.Out=èªå®ä¹ç¼ç 
task.node.param.name.1630863227787182081.type.Out=ç¹ä½ç±»å
task.node.param.name.1630863227787182081.isPark.Out=æ¯å¦æ³è½¦
task.node.param.name.1630863227787182081.angle.Out=ç¹ä½è§åº¦
task.node.param.name.1630863227787182081.chargeEnable.Out=æ¯å¦åè®¸å·¥ä½æ¶åçµ
task.node.param.name.1790203373283213314.oriValue.In=åéå
task.node.param.name.1790203373283213314.newValue.In=ææçåéå¼
task.node.param.name.1738444988633272322.url.In=Url å°å
task.node.param.name.1738444988633272322.customParams.In=èªå®ä¹å¥å
task.node.param.name.1738444988633272322.customValues.Out=èªå®ä¹åºå
task.node.param.name.1630863227757821953.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227757821953.markerCode.In=ç®æ ç¹
task.node.param.name.1630863227757821953.dockingMove.In=å¯¹æ¥å¯¼èª
task.node.param.name.1630863227757821953.accurate.In=ç²¾å®ä½
task.node.param.name.1630863227757821953.fallPrevent.In=é²è·è½
task.node.param.name.1630863227757821953.safety3D.In=3Dé¿é
task.node.param.name.1630863227757821953.featureFusion.In=èåç¹å¾
task.node.param.name.1630863227757821953.movingSpeed.In=ç§»å¨éåº¦
task.node.param.name.1630863227757821953.rotationSpeed.In=æè½¬éåº¦
task.node.param.name.1630863227757821953.moveObstacleRegion.In=ç§»å¨é¿éåºå
task.node.param.name.1630863227757821953.rotationObstacleRegion.In=æè½¬é¿éåºå
task.node.param.name.1630863227757821953.obstacleAvoidance.In=èªä¸»ç»é
task.node.param.name.1630863227757821953.agvDirection.In=è½¦å¤´è§åº¦
task.node.param.name.1630863227757821953.extendParams.In=æ©å±åæ°
task.node.param.name.1630863227577466882.time.In=ç­å¾æ¶é´ï¼ç§ï¼
task.node.param.name.1714957947186581506.ip.In=IP
task.node.param.name.1714957947186581506.port.In=ç«¯å£
task.node.param.name.1714957947186581506.code.In=åè½ç 
task.node.param.name.1714957947186581506.slaveId.In=ä»ç«ID
task.node.param.name.1714957947186581506.address.In=å¯å­å¨å°å
task.node.param.name.1714957947186581506.successVal.In=æ­£å¸¸éåº
task.node.param.name.1714957947186581506.failVal.In=å¤±è´¥éåº
task.node.param.name.1714957947186581506.timeout.In=è¶æ¶æ¶é´
task.node.param.name.1714957947186581506.executeMode.In=æ§è¡æ¹å¼
task.node.param.name.1714957947186581506.vehicleCode.In=æºå¨äºº
task.node.param.name.1714957947186581506.outValue.Out=è¾åºå¼
task.node.param.name.1788855369901137921.result_code.Out=è¿åç 
task.node.param.name.1788855369901137921.error_message.Out=è¿åæ¶æ¯
task.node.param.name.1630863227787182081.extendParam1.Out=æ©å±å±æ§1
task.node.param.name.1630863227787182081.extendParam2.Out=æ©å±å±æ§2
task.node.param.name.1630863227787182081.extendParam3.Out=æ©å±å±æ§3
task.node.param.name.1630863227787182081.extendParam4.Out=æ©å±å±æ§4
task.node.param.name.1630863227787182081.extendParam5.Out=æ©å±å±æ§5
task.node.param.name.1630863227787182081.extendParam6.Out=æ©å±å±æ§6
task.node.param.name.1630863227787182081.extendParam7.Out=æ©å±å±æ§7
task.node.param.name.1630863227787182081.extendParam8.Out=æ©å±å±æ§8
task.node.param.name.1630863227787182081.extendParam9.Out=æ©å±å±æ§9
task.node.param.name.1630863227787182081.extendParam10.Out=æ©å±å±æ§10
task.node.param.name.1742440444115046401.extendParam1.Out=æ©å±å±æ§1
task.node.param.name.1742440444115046401.extendParam2.Out=æ©å±å±æ§2
task.node.param.name.1742440444115046401.extendParam3.Out=æ©å±å±æ§3
task.node.param.name.1742440444115046401.extendParam4.Out=æ©å±å±æ§4
task.node.param.name.1742440444115046401.extendParam5.Out=æ©å±å±æ§5
task.node.param.name.1742440444115046401.extendParam6.Out=æ©å±å±æ§6
task.node.param.name.1742440444115046401.extendParam7.Out=æ©å±å±æ§7
task.node.param.name.1742440444115046401.extendParam8.Out=æ©å±å±æ§8
task.node.param.name.1742440444115046401.extendParam9.Out=æ©å±å±æ§9
task.node.param.name.1742440444115046401.extendParam10.Out=æ©å±å±æ§10
task.node.param.name.1630863227799764993.vehicleCode.In=æºå¨äºº
task.node.param.name.1630863227799764993.markerCode.In=åçµç¹
task.node.param.name.1630863227799764993.chargeTime.In=åçµæ¶é¿ï¼åï¼
task.node.param.name.1630863227799764993.batteryCharge.In=åçµçµé
task.node.param.name.1630863227799764993.correctChargeCycle.In=æ ¡æ­£åçµå¨æï¼å¤©ï¼
task.node.param.name.1821375525306884097.message.In=æ¥éä¿¡æ¯
task.node.param.name.1742439427101184002.warehouseCode.In=åºä½
task.node.param.name.1742439427101184002.occupyStatus.In=å ç¨ç¶æ
task.node.param.name.1742439427101184002.containerBarcode.In=å®¹å¨æ¡ç 
task.node.param.name.1742439427101184002.extendParam1.In=æ©å±å±æ§1
task.node.param.name.1742439427101184002.extendParam2.In=æ©å±å±æ§2
task.node.param.name.1742439427101184002.extendParam3.In=æ©å±å±æ§3
task.node.param.name.1742439427101184002.extendParam4.In=æ©å±å±æ§4
task.node.param.name.1742439427101184002.extendParam5.In=æ©å±å±æ§5
task.node.param.name.1742439427101184002.extendParam6.In=æ©å±å±æ§6
task.node.param.name.1742439427101184002.extendParam7.In=æ©å±å±æ§7
task.node.param.name.1742439427101184002.extendParam8.In=æ©å±å±æ§8
task.node.param.name.1742439427101184002.extendParam9.In=æ©å±å±æ§9
task.node.param.name.1742439427101184002.extendParam10.In=æ©å±å±æ§10
task.node.param.name.1831609346757263362.taskTypeId.In=æ§è¡ä»»å¡
task.node.param.name.1831620038075908097.taskTypeId.In=ä»»å¡ç±»å
task.node.param.name.1831620038075908097.executionMode.In=æ§è¡æ¨¡å¼
task.node.param.name.1831620038075908097.outValue.Out=ä»»å¡ç¼å·
task.node.param.name.1750822156822622210.QR_dock_id.In=å·¥ä½ID
task.node.param.name.1750822156822622210.dockingType.In=ç±»å
task.node.param.name.1852196093673111553.url.In=URLå°å
task.node.param.name.1852196093673111553.request.In=èªå®ä¹è¯·æ±åæ°
task.node.param.name.1852196093673111553.response.Out=è¾åºç»æ
task.node.param.name.1852196093673111553.checkParam.Out=æ ¡éªåæ°
task.node.param.name.1738470004770951170.vehicleCode.In=æºå¨äºº
task.node.param.name.1738470004770951170.vehicleCode.Out=æºå¨äººç¼ç 
task.node.param.name.1738470004770951170.batteryValue.Out=çµé
task.node.param.name.1738470004770951170.vehicleTypeCode.Out=æºå¨äººç±»å
task.node.param.name.1738470004770951170.vehicleGroupCode.Out=æºå¨äººç»
task.node.param.name.1738470004770951170.vehicleMapCode.Out=å½åå°å¾
task.node.param.name.1738470004770951170.markerCode.Out=å½åç¹ä½
task.node.param.name.1738470004770951170.storageInfoList.Out=å¨ä½ä¿¡æ¯
task.node.param.name.1654731794802663426.vehicleCode.In=æºå¨äºº
task.node.param.name.1654731794802663426.scriptData.In=æ§å¶èæ¬
task.node.param.name.1654731794802663426.HAND_VISION.Out=æ«æç»æ
task.node.param.name.1654731794802663426.OPERATE.Out=æä½æ°æ®
task.node.param.name.1654731794802663426.STATUS.Out=æ§è¡ç»æ
task.node.param.name.1856960932295598081.ip.In=IP
task.node.param.name.1856960932295598081.port.In=ç«¯å£
task.node.param.name.1856960932295598081.slaveId.In=ä»ç«ID
task.node.param.name.1856960932295598081.address.In=å¼å§å°å
task.node.param.name.1856960932295598081.value.In=åå¥å¼
task.node.param.name.1856959739322294274.ip.In=IP
task.node.param.name.1856959739322294274.port.In=ç«¯å£
task.node.param.name.1856959739322294274.slaveId.In=ä»ç«ID
task.node.param.name.1856959739322294274.address.In=å¼å§å°å
task.node.param.name.1856959739322294274.length.In=è¯»åé¿åº¦
task.node.param.name.1856959739322294274.result.Out=è¾åºå¼
task.node.param.name.1851551331579158530.mapAreaCode.In=åºåç¼ç 
task.node.param.name.1851551331579158530.operation.In=æä½
task.node.param.name.1856960932295598081.executeMode.In=æ§è¡æ¹å¼
task.node.param.name.1856960932295598081.vehicleCode.In=æºå¨äºº
task.node.param.name.1856959739322294274.executeMode.In=æ§è¡æ¹å¼
task.node.param.name.1856959739322294274.vehicleCode.In=æºå¨äºº
task.node.param.name.1856959739322294274.code.In=åè½ç 
task.node.param.name.1856960932295598081.code.In=åè½ç 
task.node.param.name.1856613384389165058.script.In=èæ¬
task.node.param.name.1856613384389165058.param1.In=åæ°1
task.node.param.name.1856613384389165058.param2.In=åæ°2
task.node.param.name.1856613384389165058.param3.In=åæ°3
task.node.param.name.1856613384389165058.param4.In=åæ°4
task.node.param.name.1856613384389165058.param5.In=åæ°5
task.node.param.name.1856613384389165058.param1.Out=åæ°1
task.node.param.name.1856613384389165058.param2.Out=åæ°2
task.node.param.name.1856613384389165058.param3.Out=åæ°3
task.node.param.name.1856613384389165058.param4.Out=åæ°4
task.node.param.name.1856613384389165058.param5.Out=åæ°5
task.node.param.name.1912414958361030657.markerCode.In=ç¹ä½
task.node.param.name.1912414958361030657.vehicleCode.In=æºå¨äºº
task.node.param.name.1912415217493520385.vehicleCode.In=æºå¨äºº

task.node.param.notice.1718202092436357121.vehicleCode.In=æºå¨äºº
task.node.param.notice.1718202092436357121.position.In=åä½ä¸ºæ¯«ç±³
task.node.param.notice.1718202092436357121.offsetHeight.In=åä½ä¸ºæ¯«ç±³
task.node.param.notice.1718202092436357121.speed.In=åä½ä¸ºæ¯«ç±³/ç§
task.node.param.notice.1738448135527288834.vehicleCode.In=æºå¨äºº
task.node.param.notice.1738448135527288834.markerCode.In=åçµç¹
task.node.param.notice.1738448135527288834.chargeTime.In=å¤§äºè¯¥åçµæ¶é´ï¼åéï¼å¯ææ­åçµå¹¶æ§è¡ä½ä¸
task.node.param.notice.1738448135527288834.batteryCharge.In=è¶è¿è¯¥çµéå¯ææ­åçµå¹¶æ§è¡ä½ä¸
task.node.param.notice.1738443040093851650.finishType.In=ç»æç±»å
task.node.param.notice.1738443040093851650.noticeMsg.In=ä»»å¡ç»ææ¶ï¼ä¸æ¥è¯¥æéæ¶æ¯å°ä¸æ¸¸ç³»ç»
task.node.param.notice.1646764086215663617.vehicleCode.In=å¼ä¸ºç©ºæ¶é»è®¤ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1646764086215663617.vehicleAngle1.In=æºå¨äººç¸å¯¹å°å¾çç®æ è§åº¦
task.node.param.notice.1646764086215663617.vehicleAngle2.In=æºå¨äººç¸å¯¹å°å¾çç®æ è§åº¦
task.node.param.notice.1646764086215663617.trayRotationSpeed.In=æçæè½¬éåº¦
task.node.param.notice.1646764086215663617.trayAngle1.In=æçç¸å¯¹å°å¾çç®æ è§åº¦
task.node.param.notice.1646764086215663617.trayAngle2.In=æçç¸å¯¹å°å¾çç®æ è§åº¦
task.node.param.notice.1715183824889581570.vehicleCode.In=æºå¨äºº
task.node.param.notice.1715183824889581570.ladarSwitch.In=éåº¦åºå
task.node.param.notice.1715184972354686978.vehicleCode.In=æºå¨äºº
task.node.param.notice.1715184972354686978.obstacleArea.In=é¿éèå´
task.node.param.notice.1738467719873515521.vehicleCode.In=æºå¨äºº
task.node.param.notice.1738467719873515521.scheduleMode.In=è°åº¦æ¨¡å¼
task.node.param.notice.1715183168871075842.vehicleCode.In=æºå¨äºº
task.node.param.notice.1715183168871075842.audioName.In=é³é¢åç§°
task.node.param.notice.1715183168871075842.audioVolume.In=é³é
task.node.param.notice.1715183168871075842.playCount.In=æ­æ¾æ¬¡æ°
task.node.param.notice.1630863227598438401.markerCode.In=åéç¹ä½éè¿çæºå¨äººï¼è¯¥å¼ä¸ºç©ºæ¶éæºåéæºå¨äºº
task.node.param.notice.1630863227598438401.vehicleGroupCode.In=ä»åéå±äºè¯¥æºå¨äººç»çæºå¨äºº
task.node.param.notice.1630863227598438401.vehicleTypeCode.In=ä»åéå±äºè¯¥æºå¨äººç±»åçæºå¨äºº
task.node.param.notice.1630863227598438401.vehicleMapCodeList.In=ä»åéè¯¥å°å¾åçæºå¨äºº
task.node.param.notice.1630863227598438401.limitBattery.In=ä»åéé«äºè¯¥çµéãé«äºåçµç­ç¥çµéçæºå¨äºº
task.node.param.notice.1630863227598438401.outVehicleCode.Out=æºå¨äºº
task.node.param.notice.1630863227745239041.vehicleCode.In=æºå¨äºº
task.node.param.notice.1630863227745239041.outVehicleCode.Out=æºå¨äºº
task.node.param.notice.1630863227644575745.vehicleCode.In=æºå¨äºº
task.node.param.notice.1630863227623604225.vehicleMapCodeList.In=ä»åéè¯¥å°å¾åçç¹ä½
task.node.param.notice.1630863227623604225.markerType.In=ä»åéè¯¥ç±»åçç¹ä½
task.node.param.notice.1630863227623604225.outMarkerCode.Out=éä¸­ç¹ä½
task.node.param.notice.1630863227623604225.vehicleCode.In=æºå¨äºº
task.node.param.notice.1738440272671100929.taskNo.Out=è¯¥ä»»å¡çå¯ä¸ç¼ç 
task.node.param.notice.1738440272671100929.externalTaskNo.Out=è¯¥ä»»å¡çå¤é¨ä»»å¡ç¼ç 
task.node.param.notice.1738440272671100929.priority.Out=è¯¥ä»»å¡çä¼åçº§
task.node.param.notice.1630863227707490306.ip.In=PLCçIPå°å(ä¸æ¬ç³»ç»è½å¤è¿è¡ç½ç»éè®¯çIP)
task.node.param.notice.1630863227707490306.port.In=PLCçç«¯å£å°å
task.node.param.notice.1630863227707490306.code.In=åè½ç 
task.node.param.notice.1630863227707490306.slaveId.In=ä»ç«ID
task.node.param.notice.1630863227707490306.address.In=è¯»åçå¯å­å¨å°å
task.node.param.notice.1630863227707490306.executeMode.In=æ§è¡æ¹å¼
task.node.param.notice.1630863227707490306.vehicleCode.In=æºå¨äºº
task.node.param.notice.1630863227707490306.outValue.Out=è¾åºå¼
task.node.param.notice.1645676364679905282.vehicleCode.In=å¼ä¸ºç©ºæ¶é»è®¤ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1645676364679905282.code.In=è¯»çº¿åå¯å­å¨ï¼01ï¼ï¼å¼èå´[0-1] ãè¯»ä¿æå¯å­å¨ï¼03ï¼ï¼å¼èå´[0-30000]
task.node.param.notice.1645676364679905282.slaveId.In=ä»ç«ID
task.node.param.notice.1645676364679905282.address.In=è¯»æºå¨äººå¯å­å¨çå°å
task.node.param.notice.1645676364679905282.outValue.Out=è¾åºå¼
task.node.param.notice.1630863227724267521.ip.In=PLCçIPå°å(ä¸æ¬ç³»ç»è½å¤è¿è¡ç½ç»éè®¯çIP)
task.node.param.notice.1630863227724267521.port.In=PLCçç«¯å£å·
task.node.param.notice.1630863227724267521.code.In=åè½ç 
task.node.param.notice.1630863227724267521.slaveId.In=ä»ç«ID
task.node.param.notice.1630863227724267521.address.In=åå¥çå¯å­å¨å°å
task.node.param.notice.1630863227724267521.value.In=åå¥å¯å­å¨çå¼
task.node.param.notice.1630863227724267521.executeMode.In=æ§è¡æ¹å¼
task.node.param.notice.1630863227724267521.vehicleCode.In=æºå¨äºº
task.node.param.notice.1645678201743114241.vehicleCode.In=å¼ä¸ºç©ºæ¶ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1645678201743114241.code.In= ååä¸ªçº¿åå¯å­å¨ï¼05ï¼ï¼å¼èå´[0-1] ååä¸ªä¿æå¯å­å¨ï¼06ï¼ï¼å¼èå´[0-30000]
task.node.param.notice.1645678201743114241.slaveId.In=ä»ç«ID
task.node.param.notice.1645678201743114241.address.In=è¦åå¥çå¯å­å¨å°å
task.node.param.notice.1645678201743114241.value.In=åå¥å¯å­å¨çå¼
task.node.param.notice.1715188504537468930.vehicleCode.In=æºå¨äºº
task.node.param.notice.1715188504537468930.code.In=åè½ç 
task.node.param.notice.1715188504537468930.slaveId.In=ä»ç«ID
task.node.param.notice.1715188504537468930.address.In=å¯å­å¨å°å
task.node.param.notice.1715188504537468930.successVal.In=æ­£å¸¸éåº
task.node.param.notice.1715188504537468930.failVal.In=å¤±è´¥éåº
task.node.param.notice.1715188504537468930.timeout.In=è¶æ¶æ¶é´
task.node.param.notice.1715188504537468930.outValue.Out=è¾åºå¼
task.node.param.notice.1641376178617024513.callBoxCode.In=å¼å«ççåºåç¼å·ï¼å¯ä½¿ç¨å¼å«çéç½®å·¥å·æ¥ç
task.node.param.notice.1641376178617024513.buttonCode.In=å¼å«çæé®çç¼å·
task.node.param.notice.1641376178617024513.timeout.In=è¶è¿è¯¥å¼åï¼èç¹èªå¨è¢«å®æ
task.node.param.notice.1641376553134817282.callBoxCode.In=å¼å«ççåºåç¼å·ï¼å¯ä½¿ç¨å¼å«çéç½®å·¥å·æ¥ç
task.node.param.notice.1641376553134817282.buttonCode.In=å¼å«çæé®çç¼å·
task.node.param.notice.1641377688272863233.ip.In=å£°åæ¥è­¦å¨çIPå°å
task.node.param.notice.1641377688272863233.port.In=å£°åæ¥è­¦å¨çç«¯å£å·
task.node.param.notice.1641377688272863233.type.In=æ¥è­¦ç±»å
task.node.param.notice.1641377688272863233.time.In=æ¥è­¦å¨æç»­æ¥è­¦æ¶é´
task.node.param.notice.1742437277025456130.warehouseCode.In=åºä½
task.node.param.notice.1742437277025456130.containerBarcode.In=å®¹å¨
task.node.param.notice.1742437277025456130.dispatchPolicy.In=åéç­ç¥ï¼éæºåéRANDOMãåè¿ååºFIFO
task.node.param.notice.1742437277025456130.occupyStatus.In=åºä½ç¶æ
task.node.param.notice.1742437277025456130.warehouseTypeCode.In=åºä½ç±»å
task.node.param.notice.1742437277025456130.warehouseAreaCode.In=åºåº
task.node.param.notice.1742437277025456130.extendParam1.In=æ©å±å±æ§1
task.node.param.notice.1742437277025456130.extendParam2.In=æ©å±å±æ§2
task.node.param.notice.1742437277025456130.extendParam3.In=æ©å±å±æ§3
task.node.param.notice.1742437277025456130.extendParam4.In=æ©å±å±æ§4
task.node.param.notice.1742437277025456130.extendParam5.In=æ©å±å±æ§5
task.node.param.notice.1742437277025456130.extendParam6.In=æ©å±å±æ§6
task.node.param.notice.1742437277025456130.extendParam7.In=æ©å±å±æ§7
task.node.param.notice.1742437277025456130.extendParam8.In=æ©å±å±æ§8
task.node.param.notice.1742437277025456130.extendParam9.In=æ©å±å±æ§9
task.node.param.notice.1742437277025456130.extendParam10.In=æ©å±å±æ§10
task.node.param.notice.1742437277025456130.warehouseCode.Out=åºä½ç¼ç 
task.node.param.notice.1742441148997193730.containerBarCode.In=å®¹å¨æ¡ç 
task.node.param.notice.1742441148997193730.warehouseCode.Out=åºåºç¼ç 
task.node.param.notice.1742441148997193730.workMarkerCode.Out=ä½ä¸ç¹ä½ç¼ç 
task.node.param.notice.1742441148997193730.workHeight.Out=ä½ä¸é«åº¦
task.node.param.notice.1742441148997193730.warehouseTypeCode.Out=åºä½ç±»åç¼ç 
task.node.param.notice.1742441148997193730.warehouseAreaCode.Out=åºä½åºåç¼ç 
task.node.param.notice.1742440444115046401.warehouseCode.In=åºä½
task.node.param.notice.1742440444115046401.containerBarcode.Out=å®¹å¨æ¡ç 
task.node.param.notice.1742440444115046401.workMarkerCode.Out=ä½ä¸ç¹ä½ç¼ç 
task.node.param.notice.1742440444115046401.workHeight.Out=ä½ä¸é«åº¦
task.node.param.notice.1742440444115046401.warehouseTypeCode.Out=åºä½ç±»åç¼ç 
task.node.param.notice.1742440444115046401.warehouseAreaCode.Out=åºä½åºåç¼ç 
task.node.param.notice.1742441529047273474.markerCode.In=ç¹ä½
task.node.param.notice.1742441529047273474.markerCode.Out=ç¸é»ç¹ä½ç¼ç 
task.node.param.notice.1630863227652964354.vehicleCode.In=å¼ä¸ºç©ºæ¶é»è®¤ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1630863227652964354.angle.In=æºå¨äººç¸å¯¹å°å¾çç®æ è§åº¦
task.node.param.notice.1630863227652964354.rateType.In=æè½¬ç±»å
task.node.param.notice.1630863227673935874.vehicleCode.In=å¼ä¸ºç©ºæ¶é»è®¤ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1630863227673935874.angle1.In=æçç¸å¯¹å°å¾çç®æ è§åº¦
task.node.param.notice.1630863227673935874.angle2.In=æçç¸å¯¹å°å¾çç®æ è§åº¦
task.node.param.notice.1630863227673935874.speed.In=æè½¬éåº¦
task.node.param.notice.1630863227673935874.rateType.In=æè½¬ç±»å
task.node.param.notice.1750822156822622210.vehicleCode.In=æºå¨äºº
task.node.param.notice.1750822156822622210.offsetX.In=åä½ä¸ºç±³
task.node.param.notice.1750822156822622210.offsetY.In=åä½ä¸ºç±³
task.node.param.notice.1750822156822622210.offsetAngle.In=åå·®è§åº¦
task.node.param.notice.1750822156822622210.rotationAngleRange.In=å¼ä¸º 0 æ¶ä½¿ç¨åºçé»è®¤éç½®
task.node.param.notice.1750822156822622210.moveDistanceRange.In=å¼ä¸º 0 æ¶ä½¿ç¨åºçé»è®¤éç½®
task.node.param.notice.1750822156822622210.obstacleRegion.In=é¿éåºå
task.node.param.notice.1738450017482133505.vehicleCode.In=æºå¨äºº
task.node.param.notice.1738450017482133505.dockingType.In=ç±»å
task.node.param.notice.1738450017482133505.startX.In=åä½ä¸ºç±³
task.node.param.notice.1738450017482133505.startY.In=åä½ä¸ºç±³
task.node.param.notice.1738450017482133505.startAngle.In=èµ·ç¹è§åº¦
task.node.param.notice.1738450017482133505.endX.In=åä½ä¸ºç±³
task.node.param.notice.1738450017482133505.endY.In=åä½ä¸ºç±³
task.node.param.notice.1738450017482133505.endAngle.In=ç»ç¹è§åº¦
task.node.param.notice.1738450017482133505.offsetX.In=åå·® X
task.node.param.notice.1738450017482133505.offsetY.In=åå·® Y
task.node.param.notice.1738450017482133505.offsetAngle.In=åå·®è§åº¦
task.node.param.notice.1738450017482133505.workStationCode.In=åªæå¨äºç»´ç å¯¹æ¥æ¶æç¨
task.node.param.notice.1738450017482133505.templateNo.In=æ¨¡æ¿ç¼å·
task.node.param.notice.1738450017482133505.cameraObstacle.In=ç¸æºé¿é
task.node.param.notice.1738450017482133505.obstacleRegion.In=é¿éåºå
task.node.param.notice.1751081370463637506.vehicleCode.In=æºå¨äºº
task.node.param.notice.1738450017482133505.dockingDirection.In=å¯¹æ¥æ¹å
task.node.param.notice.1715186203621986306.vehicleCode.In=æºå¨äºº
task.node.param.notice.1715186203621986306.type.In=1ãç¸å¯¹æºå¨äººéæ­¢ï¼æçè·éåºçä¸èµ·å¨ 2ãç¸å¯¹å°å¾éæ­¢ï¼åºçå¨æ¶æçä¸å¨
task.node.param.notice.1630863227694907394.vehicleCode.In=å¼ä¸ºç©ºæ¶é»è®¤ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1630863227694907394.speed.In=åééåº¦
task.node.param.notice.1630863227694907394.targetTicks.In=åéé«åº¦
task.node.param.notice.1751878094551769090.vehicleCode.In=æºå¨äºº
task.node.param.notice.1751878094551769090.outValue.Out=äºç»´ç ä¿¡æ¯
task.node.param.notice.1751825844022235138.vehicleCode.In=æºå¨äºº
task.node.param.notice.1630863227787182081.markerCode.In=ç¹ä½
task.node.param.notice.1630863227787182081.markerName.In=èªå®ä¹ç¼ç 
task.node.param.notice.1630863227787182081.code.Out=ç¹ä½å¯ä¸ç¼ç 
task.node.param.notice.1630863227787182081.name.Out=ç¨æ·èªå®ä¹çç¹ä½ç¼ç ï¼å¯ä¸ºç©ºå¼
task.node.param.notice.1630863227787182081.type.Out=æä¸¾å¼ï¼ ChargingMarker:åçµç¹, NavigationMarker:å¯¼èªç¹, WorkMarker:å·¥ä½ç¹
task.node.param.notice.1630863227787182081.isPark.Out=æä¸¾å¼ï¼trueãfalse
task.node.param.notice.1630863227787182081.angle.Out=ç¹ä½è§åº¦
task.node.param.notice.1630863227787182081.chargeEnable.Out=æ¯å¦åè®¸å·¥ä½æ¶åçµ
task.node.param.notice.1790203373283213314.oriValue.In=åéå
task.node.param.notice.1790203373283213314.newValue.In=ææçåéå¼
task.node.param.notice.1738444988633272322.url.In=è¦è¯·æ±ç Url å°å
task.node.param.notice.1738444988633272322.customParams.In=èªå®ä¹å¥å
task.node.param.notice.1738444988633272322.customValues.Out=èªå®ä¹åºå
task.node.param.notice.1630863227757821953.vehicleCode.In=å¼ä¸ºç©ºæ¶é»è®¤ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1630863227757821953.markerCode.In=ç®æ ç¹
task.node.param.notice.1630863227757821953.dockingMove.In=å³é­è¯¥é¡¹éç½®ï¼å¯¹æ¥ç±»åè·¯å¾ä¸åçæ
task.node.param.notice.1630863227757821953.accurate.In=ç²¾å®ä½
task.node.param.notice.1630863227757821953.fallPrevent.In=é²è·è½
task.node.param.notice.1630863227757821953.safety3D.In=3Dé¿é
task.node.param.notice.1630863227757821953.featureFusion.In=èåç¹å¾
task.node.param.notice.1630863227757821953.movingSpeed.In=ç§»å¨éåº¦
task.node.param.notice.1630863227757821953.rotationSpeed.In=æè½¬éåº¦
task.node.param.notice.1630863227757821953.moveObstacleRegion.In=ç§»å¨é¿éåºå
task.node.param.notice.1630863227757821953.rotationObstacleRegion.In=æè½¬é¿éåºå
task.node.param.notice.1630863227757821953.obstacleAvoidance.In=è®¾ç½®æ¯å¦å¼å¯èªä¸»ç»é
task.node.param.notice.1630863227757821953.agvDirection.In=è½¦å¤´è§åº¦
task.node.param.notice.1630863227757821953.extendParams.In=æ©å±åæ°
task.node.param.notice.1630863227577466882.time.In=ç­å¾æ¶é´ï¼ç§ï¼
task.node.param.notice.1714957947186581506.ip.In=PLCçIPå°å(ä¸æ¬ç³»ç»è½å¤è¿è¡ç½ç»éè®¯çIP)
task.node.param.notice.1714957947186581506.port.In=PLCçç«¯å£å°å
task.node.param.notice.1714957947186581506.code.In=åè½ç 
task.node.param.notice.1714957947186581506.slaveId.In=ä»ç«ID
task.node.param.notice.1714957947186581506.address.In=è¯»åçå¯å­å¨å°å
task.node.param.notice.1714957947186581506.successVal.In=æ­£å¸¸éåº
task.node.param.notice.1714957947186581506.failVal.In=å¤±è´¥éåº
task.node.param.notice.1714957947186581506.timeout.In=è¶æ¶æ¶é´
task.node.param.notice.1714957947186581506.executeMode.In=æ§è¡æ¹å¼
task.node.param.notice.1714957947186581506.vehicleCode.In=æºå¨äºº
task.node.param.notice.1714957947186581506.outValue.Out=è¾åºå¼
task.node.param.notice.1788855369901137921.result_code.Out=1001: æªè§¦åé¿é, 1002 :è§¦åé¿é
task.node.param.notice.1788855369901137921.error_message.Out=è¿åä¼ æå¨çé¿éåºå(æ¥å,åæ­¢ åé æªè§¦å)
task.node.param.notice.1630863227787182081.extendParam1.Out=æ©å±å±æ§1
task.node.param.notice.1630863227787182081.extendParam2.Out=æ©å±å±æ§2
task.node.param.notice.1630863227787182081.extendParam3.Out=æ©å±å±æ§3
task.node.param.notice.1630863227787182081.extendParam4.Out=æ©å±å±æ§4
task.node.param.notice.1630863227787182081.extendParam5.Out=æ©å±å±æ§5
task.node.param.notice.1630863227787182081.extendParam6.Out=æ©å±å±æ§6
task.node.param.notice.1630863227787182081.extendParam7.Out=æ©å±å±æ§7
task.node.param.notice.1630863227787182081.extendParam8.Out=æ©å±å±æ§8
task.node.param.notice.1630863227787182081.extendParam9.Out=æ©å±å±æ§9
task.node.param.notice.1630863227787182081.extendParam10.Out=æ©å±å±æ§10
task.node.param.notice.1742440444115046401.extendParam1.Out=æ©å±å±æ§1
task.node.param.notice.1742440444115046401.extendParam2.Out=æ©å±å±æ§2
task.node.param.notice.1742440444115046401.extendParam3.Out=æ©å±å±æ§3
task.node.param.notice.1742440444115046401.extendParam4.Out=æ©å±å±æ§4
task.node.param.notice.1742440444115046401.extendParam5.Out=æ©å±å±æ§5
task.node.param.notice.1742440444115046401.extendParam6.Out=æ©å±å±æ§6
task.node.param.notice.1742440444115046401.extendParam7.Out=æ©å±å±æ§7
task.node.param.notice.1742440444115046401.extendParam8.Out=æ©å±å±æ§8
task.node.param.notice.1742440444115046401.extendParam9.Out=æ©å±å±æ§9
task.node.param.notice.1742440444115046401.extendParam10.Out=æ©å±å±æ§10
task.node.param.notice.1630863227799764993.vehicleCode.In=å¼ä¸ºç©ºæ¶é»è®¤ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1630863227799764993.markerCode.In=åçµç¹
task.node.param.notice.1630863227799764993.chargeTime.In=æºå¨äººå·²åçµæ¶é¿è¶è¿è¯¥å¼åæå¯ä»¥è¢«ä¸­æ­
task.node.param.notice.1630863227799764993.batteryCharge.In=æºå¨äººå½åçµéè¶è¿è¯¥å¼æ¶æå¯ä»¥è¢«ä¸­æ­
task.node.param.notice.1630863227799764993.correctChargeCycle.In=æºå¨äººå¤äºç«æ­£åçµæ¶ä¸å¯ä»¥è¢«ä¸­æ­
task.node.param.notice.1821375525306884097.message.In=æ¥éä¿¡æ¯
task.node.param.notice.1742439427101184002.warehouseCode.In=åºä½
task.node.param.notice.1742439427101184002.occupyStatus.In=å ç¨ç¶æ
task.node.param.notice.1742439427101184002.containerBarcode.In=å®¹å¨æ¡ç 
task.node.param.notice.1742439427101184002.extendParam1.In=æ©å±å±æ§1
task.node.param.notice.1742439427101184002.extendParam2.In=æ©å±å±æ§2
task.node.param.notice.1742439427101184002.extendParam3.In=æ©å±å±æ§3
task.node.param.notice.1742439427101184002.extendParam4.In=æ©å±å±æ§4
task.node.param.notice.1742439427101184002.extendParam5.In=æ©å±å±æ§5
task.node.param.notice.1742439427101184002.extendParam6.In=æ©å±å±æ§6
task.node.param.notice.1742439427101184002.extendParam7.In=æ©å±å±æ§7
task.node.param.notice.1742439427101184002.extendParam8.In=æ©å±å±æ§8
task.node.param.notice.1742439427101184002.extendParam9.In=æ©å±å±æ§9
task.node.param.notice.1742439427101184002.extendParam10.In=æ©å±å±æ§10
task.node.param.notice.1831609346757263362.taskTypeId.In=æ§è¡ä»»å¡
task.node.param.notice.1831620038075908097.taskTypeId.In=ä»»å¡ç±»å
task.node.param.notice.1831620038075908097.executionMode.In=æ§è¡æ¨¡å¼
task.node.param.notice.1831620038075908097.outValue.Out=ä»»å¡ç¼å·
task.node.param.notice.1750822156822622210.QR_dock_id.In=å·¥ä½id
task.node.param.notice.1750822156822622210.dockingType.In=ç±»å
task.node.param.notice.1852196093673111553.url.In=è¯·æ±çå°å
task.node.param.notice.1852196093673111553.request.In=èªå®ä¹è¯·æ±åæ°
task.node.param.notice.1852196093673111553.response.Out=è¾åºç»æ
task.node.param.notice.1852196093673111553.checkParam.Out=æ ¡éªåæ°
task.node.param.notice.1738470004770951170.vehicleCode.In=æºå¨äºº
task.node.param.notice.1738470004770951170.vehicleCode.Out=æºå¨äºº
task.node.param.notice.1738470004770951170.batteryValue.Out=çµé
task.node.param.notice.1738470004770951170.vehicleTypeCode.Out=æºå¨äººç±»å
task.node.param.notice.1738470004770951170.vehicleGroupCode.Out=æºå¨äººç»
task.node.param.notice.1738470004770951170.vehicleMapCode.Out=å½åå°å¾
task.node.param.notice.1738470004770951170.markerCode.Out=å½åç¹ä½
task.node.param.notice.1738470004770951170.storageInfoList.Out=å¨ä½ä¿¡æ¯
task.node.param.notice.1654731794802663426.vehicleCode.In=å¼ä¸ºç©ºæ¶é»è®¤ä½¿ç¨ä»»å¡å ç¨çæºå¨äºº
task.node.param.notice.1654731794802663426.scriptData.In=Mosç³»ç»å¯æ§è¡çJsonèæ¬
task.node.param.notice.1654731794802663426.HAND_VISION.Out=æ«æç»æ
task.node.param.notice.1654731794802663426.OPERATE.Out=æä½æ°æ®
task.node.param.notice.1654731794802663426.STATUS.Out=1:æ§è¡ä¸­ ; 2:æ§è¡å¼å¸¸(æªåå); 3:æ§è¡å®æ; 4:æ§è¡å¼å¸¸(å·²åå)
task.node.param.notice.1856960932295598081.ip.In=PLCçIPå°å(ä¸æ¬ç³»ç»è½å¤è¿è¡ç½ç»éè®¯çIP)
task.node.param.notice.1856960932295598081.port.In=ç«¯å£
task.node.param.notice.1856960932295598081.slaveId.In=ä»ç«ID
task.node.param.notice.1856960932295598081.address.In=å¼å§å°å
task.node.param.notice.1856960932295598081.value.In=åå¥å¼
task.node.param.notice.1856959739322294274.ip.In=PLCçIPå°å(ä¸æ¬ç³»ç»è½å¤è¿è¡ç½ç»éè®¯çIP)
task.node.param.notice.1856959739322294274.port.In=ç«¯å£
task.node.param.notice.1856959739322294274.slaveId.In=ä»ç«ID
task.node.param.notice.1856959739322294274.address.In=å¼å§å°å
task.node.param.notice.1856959739322294274.length.In=è¯»åé¿åº¦
task.node.param.notice.1856959739322294274.result.Out=è¾åºå¼
task.node.param.notice.1851551331579158530.mapAreaCode.In=åºåç¼ç 
task.node.param.notice.1851551331579158530.operation.In=æä½
task.node.param.notice.1856960932295598081.executeMode.In=æ§è¡æ¹å¼
task.node.param.notice.1856960932295598081.vehicleCode.In=æºå¨äºº
task.node.param.notice.1856959739322294274.executeMode.In=æ§è¡æ¹å¼
task.node.param.notice.1856959739322294274.vehicleCode.In=æºå¨äºº
task.node.param.notice.1856959739322294274.code.In=åè½ç 
task.node.param.notice.1856960932295598081.code.In=åè½ç 
task.node.param.notice.1856613384389165058.script.In=èæ¬
task.node.param.notice.1856613384389165058.param1.In=åæ°1
task.node.param.notice.1856613384389165058.param2.In=åæ°2
task.node.param.notice.1856613384389165058.param3.In=åæ°3
task.node.param.notice.1856613384389165058.param4.In=åæ°4
task.node.param.notice.1856613384389165058.param5.In=åæ°5
task.node.param.notice.1856613384389165058.param1.Out=åæ°1
task.node.param.notice.1856613384389165058.param2.Out=åæ°2
task.node.param.notice.1856613384389165058.param3.Out=åæ°3
task.node.param.notice.1856613384389165058.param4.Out=åæ°4
task.node.param.notice.1856613384389165058.param5.Out=åæ°5
task.node.param.notice.1912414958361030657.markerCode.In=ç¹ä½
task.node.param.notice.1912414958361030657.vehicleCode.In=æºå¨äºº
task.node.param.notice.1912415217493520385.vehicleCode.In=æºå¨äºº

task.node.param.value.desc.1738443040093851650.finishType.In.Finished=å®æä»»å¡
task.node.param.value.desc.1738443040093851650.finishType.In.Cancel=åæ¶ä»»å¡
task.node.param.value.desc.1715183824889581570.ladarSwitch.In.0=åºå0
task.node.param.value.desc.1715183824889581570.ladarSwitch.In.1=åºå1
task.node.param.value.desc.1715184972354686978.obstacleArea.In.1=é¿éåºå 1
task.node.param.value.desc.1715184972354686978.obstacleArea.In.2=é¿éåºå 2
task.node.param.value.desc.1715184972354686978.obstacleArea.In.3=é¿éåºå 3
task.node.param.value.desc.1715184972354686978.obstacleArea.In.4=é¿éåºå 4
task.node.param.value.desc.1715184972354686978.obstacleArea.In.5=é¿éåºå 5
task.node.param.value.desc.1715184972354686978.obstacleArea.In.6=é¿éåºå 6
task.node.param.value.desc.1715184972354686978.obstacleArea.In.7=é¿éåºå 7
task.node.param.value.desc.1715184972354686978.obstacleArea.In.8=é¿éåºå 8
task.node.param.value.desc.1715184972354686978.obstacleArea.In.9=é¿éåºå 9
task.node.param.value.desc.1715184972354686978.obstacleArea.In.10=é¿éåºå 10
task.node.param.value.desc.1715184972354686978.obstacleArea.In.11=é¿éåºå 11
task.node.param.value.desc.1715184972354686978.obstacleArea.In.12=é¿éåºå 12
task.node.param.value.desc.1715184972354686978.obstacleArea.In.13=é¿éåºå 13
task.node.param.value.desc.1715184972354686978.obstacleArea.In.14=é¿éåºå 14
task.node.param.value.desc.1715184972354686978.obstacleArea.In.15=é¿éåºå 15
task.node.param.value.desc.1715184972354686978.obstacleArea.In.16=é¿éåºå 16
task.node.param.value.desc.1738467719873515521.scheduleMode.In.AutoSchedule=èªå¨è°åº¦
task.node.param.value.desc.1738467719873515521.scheduleMode.In.ManualSchedule=æå¨è°åº¦
task.node.param.value.desc.1630863227623604225.markerType.In.NavigationMarker=å¯¼èªç¹
task.node.param.value.desc.1630863227623604225.markerType.In.WorkMarker=å·¥ä½ç¹
task.node.param.value.desc.1630863227623604225.markerType.In.ChargingMarker=åçµç¹
task.node.param.value.desc.1630863227707490306.code.In.01=è¯»çº¿åå¯å­å¨ï¼01ï¼
task.node.param.value.desc.1630863227707490306.code.In.02=è¯»ç¦»æ£è¾å¥å¯å­å¨ï¼02ï¼
task.node.param.value.desc.1630863227707490306.code.In.03=è¯»ä¿æå¯å­å¨ï¼03ï¼
task.node.param.value.desc.1630863227707490306.code.In.04=è¯»è¾å¥å¯å­å¨ï¼04ï¼
task.node.param.value.desc.1630863227707490306.executeMode.In.Server=æå¡å¨
task.node.param.value.desc.1630863227707490306.executeMode.In.Vehicle=æºå¨äºº
task.node.param.value.desc.1645676364679905282.code.In.01=è¯»çº¿åå¯å­å¨ï¼01ï¼
task.node.param.value.desc.1645676364679905282.code.In.03=è¯»ä¿æå¯å­å¨ï¼03ï¼
task.node.param.value.desc.1630863227724267521.code.In.05=ååä¸ªçº¿åå¯å­å¨ï¼05ï¼
task.node.param.value.desc.1630863227724267521.code.In.06=ååä¸ªç¦»æ£è¾å¥å¯å­å¨ï¼06ï¼
task.node.param.value.desc.1630863227724267521.code.In.15=åå¤ä¸ªçº¿åå¯å­å¨ï¼15ï¼
task.node.param.value.desc.1630863227724267521.code.In.16=åå¤ä¸ªç¦»æ£è¾å¥å¯å­å¨ï¼16ï¼
task.node.param.value.desc.1630863227724267521.executeMode.In.Server=æå¡å¨
task.node.param.value.desc.1630863227724267521.executeMode.In.Vehicle=æºå¨äºº
task.node.param.value.desc.1645678201743114241.code.In.05=ååä¸ªçº¿åå¯å­å¨ï¼05ï¼
task.node.param.value.desc.1645678201743114241.code.In.06=ååä¸ªä¿æå¯å­å¨ï¼06ï¼
task.node.param.value.desc.1715188504537468930.code.In.03=è¯»ä¿æå¯å­å¨ï¼03ï¼
task.node.param.value.desc.1641377688272863233.type.In.SoundAndLight=å£°å
task.node.param.value.desc.1641377688272863233.type.In.Sound=å£°
task.node.param.value.desc.1641377688272863233.type.In.Light=å
task.node.param.value.desc.1742437277025456130.dispatchPolicy.In.FIFO=åè¿ååº
task.node.param.value.desc.1742437277025456130.dispatchPolicy.In.RANDOM=éæºåé
task.node.param.value.desc.1742437277025456130.occupyStatus.In.Store=å­å¨
task.node.param.value.desc.1742437277025456130.occupyStatus.In.Free=ç©ºé²
task.node.param.value.desc.1630863227652964354.rateType.In.Relative=ç¸å¯¹æºå¨äººæè½¬
task.node.param.value.desc.1630863227652964354.rateType.In.Absolute=ç¸å¯¹å°å¾æè½¬
task.node.param.value.desc.1630863227673935874.rateType.In.Relative=ç¸å¯¹æºå¨äºº
task.node.param.value.desc.1630863227673935874.rateType.In.Absolute=ç¸å¯¹å°å¾
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.1=é¿éåºå 1
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.2=é¿éåºå 2
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.3=é¿éåºå 3
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.4=é¿éåºå 4
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.5=é¿éåºå 5
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.6=é¿éåºå 6
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.7=é¿éåºå 7
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.8=é¿éåºå 8
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.9=é¿éåºå 9
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.10=é¿éåºå 10
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.11=é¿éåºå 11
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.12=é¿éåºå 12
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.13=é¿éåºå 13
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.14=é¿éåºå 14
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.15=é¿éåºå 15
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.16=é¿éåºå 16
task.node.param.value.desc.1738450017482133505.dockingType.In.QR_Down=äºç»´ç å¯¹æ¥
task.node.param.value.desc.1738450017482133505.dockingType.In.Reflector=ååæ¿å¯¹æ¥
task.node.param.value.desc.1738450017482133505.dockingType.In.Symbol_V=V åæ¿å¯¹æ¥
task.node.param.value.desc.1738450017482133505.dockingType.In.Shelflegs=è´§æ¶è¿å¯¹æ¥
task.node.param.value.desc.1738450017482133505.dockingType.In.Pallet=æçå¯¹æ¥
task.node.param.value.desc.1738450017482133505.dockingType.In.LeaveDocking=è±ç¦»å¯¹æ¥
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.1=é¿éåºå 1
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.2=é¿éåºå 2
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.3=é¿éåºå 3
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.4=é¿éåºå 4
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.5=é¿éåºå 5
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.6=é¿éåºå 6
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.7=é¿éåºå 7
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.8=é¿éåºå 8
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.9=é¿éåºå 9
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.10=é¿éåºå 10
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.11=é¿éåºå 11
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.12=é¿éåºå 12
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.13=é¿éåºå 13
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.14=é¿éåºå 14
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.15=é¿éåºå 15
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.16=é¿éåºå 16
task.node.param.value.desc.1738450017482133505.dockingDirection.In.Head=è½¦å¤´
task.node.param.value.desc.1738450017482133505.dockingDirection.In.Tail=è½¦å°¾
task.node.param.value.desc.1715186203621986306.type.In.Open=ç¸å¯¹æºå¨äººéæ­¢
task.node.param.value.desc.1715186203621986306.type.In.Close=ç¸å¯¹å°å¾éæ­¢
task.node.param.value.desc.1630863227757821953.accurate.In.=é»è®¤
task.node.param.value.desc.1630863227757821953.accurate.In.true=å¼å¯
task.node.param.value.desc.1630863227757821953.accurate.In.false=å³é­
task.node.param.value.desc.1630863227757821953.fallPrevent.In.=é»è®¤
task.node.param.value.desc.1630863227757821953.fallPrevent.In.true=å¼å¯
task.node.param.value.desc.1630863227757821953.fallPrevent.In.false=å³é­
task.node.param.value.desc.1630863227757821953.safety3D.In.=é»è®¤
task.node.param.value.desc.1630863227757821953.safety3D.In.true=å¼å¯
task.node.param.value.desc.1630863227757821953.safety3D.In.false=å³é­
task.node.param.value.desc.1630863227757821953.featureFusion.In.=é»è®¤
task.node.param.value.desc.1630863227757821953.featureFusion.In.true=å¼å¯
task.node.param.value.desc.1630863227757821953.featureFusion.In.false=å³é­
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.=é»è®¤
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.1=é¿éåºå1
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.2=é¿éåºå2
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.3=é¿éåºå3
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.4=é¿éåºå4
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.5=é¿éåºå5
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.6=é¿éåºå6
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.7=é¿éåºå7
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.8=é¿éåºå8
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.9=é¿éåºå9
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.10=é¿éåºå10
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.11=é¿éåºå11
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.12=é¿éåºå12
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.13=é¿éåºå13
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.14=é¿éåºå14
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.15=é¿éåºå15
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.16=é¿éåºå16
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.=é»è®¤
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.1=é¿éåºå1
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.2=é¿éåºå2
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.3=é¿éåºå3
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.4=é¿éåºå4
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.5=é¿éåºå5
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.6=é¿éåºå6
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.7=é¿éåºå7
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.8=é¿éåºå8
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.9=é¿éåºå9
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.10=é¿éåºå10
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.11=é¿éåºå11
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.12=é¿éåºå12
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.13=é¿éåºå13
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.14=é¿éåºå14
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.15=é¿éåºå15
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.16=é¿éåºå16
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.=é»è®¤
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.true=å¼å¯
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.false=å³é­
task.node.param.value.desc.1630863227757821953.agvDirection.In.0=0
task.node.param.value.desc.1630863227757821953.agvDirection.In.90=90
task.node.param.value.desc.1630863227757821953.agvDirection.In.-90=-90
task.node.param.value.desc.1630863227757821953.agvDirection.In.180=180
task.node.param.value.desc.1714957947186581506.code.In.01=è¯»çº¿åå¯å­å¨ï¼01ï¼
task.node.param.value.desc.1714957947186581506.code.In.02=è¯»ç¦»æ£è¾å¥å¯å­å¨ï¼02ï¼
task.node.param.value.desc.1714957947186581506.code.In.03=è¯»ä¿æå¯å­å¨ï¼03ï¼
task.node.param.value.desc.1714957947186581506.code.In.04=è¯»è¾å¥å¯å­å¨ï¼04ï¼
task.node.param.value.desc.1714957947186581506.executeMode.In.Server=æå¡å¨
task.node.param.value.desc.1714957947186581506.executeMode.In.Vehicle=æºå¨äºº
task.node.param.value.desc.1742439427101184002.occupyStatus.In.Free=ç©ºé²
task.node.param.value.desc.1742439427101184002.occupyStatus.In.Store=å­å¨
task.node.param.value.desc.1831620038075908097.executionMode.In.Independent=ç¬ç«æ§è¡
task.node.param.value.desc.1831620038075908097.executionMode.In.Embedded=åµå¥æ§è¡
task.node.param.value.desc.1750822156822622210.dockingType.In.UP=ä¸äºç»´ç ï¼ä½éï¼
task.node.param.value.desc.1750822156822622210.dockingType.In.QR_Up=ä¸äºç»´ç ï¼é«éï¼
task.node.param.value.desc.1750822156822622210.dockingType.In.DOWN=ä¸äºç»´ç 
task.node.param.value.desc.1750822156822622210.dockingType.In.LEFT=å·¦äºç»´ç 
task.node.param.value.desc.1750822156822622210.dockingType.In.RIGHT=å³äºç»´ç 
task.node.param.value.desc.1750822156822622210.dockingType.In.Laser_Side=ä¾§é¢æ¿å
task.node.param.value.desc.1750822156822622210.dockingType.In.Line_Straight=åæ¬è
task.node.param.value.desc.1750822156822622210.dockingType.In.Reflector_Adjust=ååè´´ç²¾è°
task.node.param.value.desc.1851551331579158530.operation.In.Enable=å¯ç¨
task.node.param.value.desc.1851551331579158530.operation.In.Disable=ç¦ç¨
task.node.param.value.desc.1856960932295598081.executeMode.In.Server=æå¡å¨
task.node.param.value.desc.1856960932295598081.executeMode.In.Vehicle=æºå¨äºº
task.node.param.value.desc.1856959739322294274.executeMode.In.Server=æå¡å¨
task.node.param.value.desc.1856959739322294274.executeMode.In.Vehicle=æºå¨äºº
task.node.param.value.desc.1856959739322294274.code.In.03=è¯»ä¿æå¯å­å¨ï¼03ï¼
task.node.param.value.desc.1856960932295598081.code.In.16=åå¤ä¸ªå¯å­å¨ï¼16ï¼