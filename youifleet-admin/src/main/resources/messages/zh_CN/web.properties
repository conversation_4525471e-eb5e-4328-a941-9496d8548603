{"message": {"hello": "ä½ å¥½", "edit": "ç¼è¾", "del": "å é¤", "add": "æ°å¢", "success": "<PERSON><PERSON>", "fail": "å¤±è´¥", "reasonIs": "åå æ¯", "update": "æ´æ°", "download": "ä¸è½½", "delete": "å é¤", "export": "å¯¼åº", "multExport": "æ¹éå¯¼åº", "import": "å¯¼å¥", "multImport": "æ¹éå¯¼å¥", "switchEn": "åæ¢è³è±æ", "switchzh": "åæ¢è³ä¸­æ", "options": "æä½", "reset": "éç½®", "status": "ç¶æ", "statusList": {"0": "ç¦ç¨", "1": "å¯ç¨"}, "refresh": "å·æ°", "nickname": "è´¦å·", "userName": "ç¨æ·åç§°", "menu": "èå", "role": "è§è²", "describe": "æ<PERSON>¿°", "updateTime": "æ´æ°æ¶é´", "createTime": "åå»ºæ¶é´", "passWord": "å¯ç ", "custom": "èªå®ä¹", "requiredTips": "è¯·è¾å¥", "pleaseSelect": "è¯·éæ©", "enableList": {"0": "æ¯", "1": "å¦"}, "addForm": "æ°å¢", "editForm": "ç¼è¾", "cancel": "<PERSON><PERSON>¶", "submit": "æäº¤", "totalData": "å± {total} æ¡", "type": "ç±»å", "group": "åç»", "range": "èå´", "formRules": {"phoneLen": "è¯·è¾å¥æ­£ç¡®ç11ä½çµè¯å·ç ", "port": "è¯·è¾å¥æ­£ç¡®çç«¯å£å·", "ip": "è¯·è¾å¥æ­£ç¡®çIPå°å", "isLength": "é¿åº¦è¶è¿{max}å­ç¬¦", "isNumber": "å¼åªè½å¿é¡»ä¸ºæ°å­", "englishFirst_": "ä»æ¯æè±æï¼é¦å­ç¬¦ï¼ãä¸åçº¿ãæ°å­ç»æçå­ç¬¦ä¸²", "englishFirst": "ä»æ¯æè±æï¼é¦å­æ¯ï¼ãæ°å­ç»æçå­ç¬¦ä¸²", "english": "ä»æ¯æè±æçå­ç¬¦ä¸²", "isName": "ä»æ¯æè±æãä¸åçº¿ãæ°å­ç»æçå­ç¬¦ä¸²", "isPureNumber": "ä¸è½æ¯çº¯æ°å­ä¸é¿åº¦æå¤§ä¸º20"}, "ChargingMarker": "åçµç¹", "ParkingMarker": "æ³è½¦ç¹", "WorkMarker": "å·¥ä½ç¹", "NavigationMarker": "å¯¼èªç¹", "message": "æ¶æ¯", "language": "è¯­è¨", "languageSwitch": "è¯­è¨åæ¢", "importLanguage": "è¯­è¨å¯¼å¥", "my": "æç", "delTips": "ç¡®è®¤å é¤ï¼å é¤çåå®¹ä¸å¯æ¢å¤ã", "details": "è¯¦æ", "individuation": "ä¸ªæ§å", "searchSettings": "æç´¢è®¾ç½®", "complete": "å®æ", "connectTimeOut": "ç½ç»è¿æ¥å·²è¶æ¶", "vehicle": "æºå¨äºº", "lowBattery": "ä½çµé", "highBattery": "é«çµé", "pleaseSelectOneVehicle": "è¯·è³å°éæ©ä¸ä¸ªæºå¨äºº", "second": "ç§", "minute": "å", "minutes": "<PERSON><PERSON>", "hour": "å°æ¶", "hour1": "æ¶", "day": "å¤©", "angle": "è§åº¦", "speed": "éåº¦", "orientation": "å®ä½", "ip": "IP", "online": "å¨çº¿", "unconnected": "æªè¿æ¥", "abnormal": "å¼å¸¸", "inExecution": "æ§è¡ä¸­", "encoding": "ç¼ç ", "join": "è¿æ¥", "professional": "å·¥ä½", "controls": "æ§å¶", "all": "å¨é¨", "previous": "ä¸ä¸é¡µ", "nextPage": "ä¸ä¸é¡µ", "thereIsNoPublishedTaskType": "ä¸å­å¨å·²å¯ç¨çä»»å¡æµç¨", "solution": "è§£å³æ¹æ¡", "dragTheMapFileZipIntoThisArea": "å°å°å¾æä»¶ææ½å¨æ­¤åºåå", "dragLocationMapFileZipIntoThisArea": "å°å®ä½å¾æä»¶ææ½å¨æ­¤åºåå", "orClickHereToUpload": "æç¹å»åéæ©æä»¶ä¸ä¼ ", "fileImport": "æä»¶å¯¼å¥", "uploadSuccessfully": "ä¸ä¼ æå", "confirm": "ç¡®å®", "listSetting": "åè¡¨è®¾ç½®", "pleaseSelectAtLeastOneItem": "è¯·è³å°éæ©ä¸é¡¹å±æ§", "clickToUpload": "ç¹å»ä¸ä¼ ", "fileUploadFailed": "æä»¶ä¸ä¼ å¤±è´¥", "deleteOrNot": "ç¡®è®¤å é¤", "messageCannotReturn": "å é¤çåå®¹ä¸å¯æ¢å¤", "quit": "éåº", "append": "æ·»å ", "deleteAll": "å¨é¨å é¤", "port": "ç«¯å£", "required": "å¿å¡«", "variable": "<PERSON><PERSON>", "defaultValue": "é»è®¤å¼", "maximumValue": "æå¤§å¼", "minimumValue": "æå°å¼", "yes": "æ¯", "no": "å¦", "value": "å¼", "typeName": "ç±»ååç§°", "batch": "æ¹é", "create": "åå»º", "batchCreate": "æ¹éåå»º", "login": "ç»å½", "updateAirShower": "æ´æ°é£æ·é¨", "updateArea": "æ´æ°åºå", "updateAutodoor": "æ´æ°èªå¨é¨", "updateElevator": "æ´æ°çµæ¢¯", "updateMap": "æ´æ°å°å¾", "updateMarker": "æ´æ°ç¹ä½", "updatePath": "æ´æ°è·¯å¾", "deletePath": "å é¤è·¯å¾", "relocationManual": "éå®ä½", "scrapDraft": "åºå¼èç¨¿", "publish": "åå¸", "copy": "å¤å¶", "name": "åç§°", "serialNumber": "ç¼å·", "startTime": "å¼å§æ¶é´", "endTime": "ç»ææ¶é´", "createTask": "åå»ºä»»å¡", "noData": "æ æ°æ®", "arguments": "åæ°", "priority": "ä¼åçº§", "selectTheDataYouWantToDelete": "è¯·éæ©æ°æ®", "selectTheDataThatYouWantToModify": "è¯·éæ©éè¦ä¿®æ¹çæ°æ®", "passwordInconsistencyTip": "å¯ç åç¡®è®¤å¯ç ä¸ä¸è´ï¼è¯·éæ°è¾å¥", "theEnteredPasswordIsInconsistent": "å¯ç è¾å¥ä¸ä¸è´", "systemServiceException": "åç«¯æå¡è°ç¨å¤±è´¥", "founder": "åå»ºäºº", "result": "ç»æ", "meter": "ç±³", "file": "æä»¶", "creationMode": "å<PERSON>»ºæ¹å¼", "externalCoding": "å¤é¨ç¼ç ", "task": "ä»»å¡", "default": "é»è®¤", "remark": "å¤æ³¨", "addRemark": "æ·»å å¤æ³¨", "form": "è¡¨å", "height": "é«åº¦", "point": "ç¹ä½", "noDataAvailable": "ææ æ°æ®", "cm": "åç±³", "userSetting": {"roleManage": "è§è²ç®¡ç", "accountManage": "è´¦å·ç®¡ç", "auth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addRole": "æ°å¢è§è²", "rename": "éå½å", "account": "è´¦å·", "name": "å§å", "role": "è§è²", "email": "é®ç®±", "phone": "çµè¯", "enable": "å¯ç¨ç¶æ", "updateDate": "æ´æ°æ¶é´", "automaticLogout": "ç»å½è¿ææ¶é´", "password": "å¯ç ", "surePassword": "ç¡®è®¤å¯ç ", "enableList": {"1": "æ¯", "0": "å¦"}, "roleName": "å¼ä¸åè®¸è¶è¿20ä¸ªå­ç¬¦;ä»æ¯æä¸­æãè±æãæ°å­", "username": "å¼ä¸åè®¸è¶è¿20ä¸ªå­ç¬¦;è±æå¼å¤´;ä»æ¯ææ¯æè±æãä¸åçº¿ãæ°å­", "realName": "å¼ä¸åè®¸è¶è¿20ä¸ªå­ç¬¦", "autoLogoutTime": "èå´ä¸º[0,9999]", "changePassword": "ä¿®æ¹å¯ç ", "logout": "éåºç»å½", "pleaseSelectARole": "è¯·éæ©è§è²", "nameTip": "å¼ä¸åè®¸ä¸ºç©º", "theNameCannotExceed20Characters": "å¼ä¸åè®¸è¶è¿20ä¸ªå­ç¬¦"}, "changePassword": {"changePassword": "ä¿®æ¹å¯ç ", "resetPassword": "éç½®å¯ç ", "oldPassword": "åå¯ç ", "loginUserPassword": "ç»å½ç¨æ·å¯ç ", "newPassword": "æ°å¯ç ", "confirmNewPassword": "ç¡®è®¤æ°å¯ç "}, "map": {"markerName": "å¼ä¸åè®¸è¶è¿20ä¸ªå­ç¬¦;è±æå¼å¤´;ä»æ¯æè±æãä¸åçº¿ãæ°å­", "type": {"Elevator": "çµæ¢¯", "AutoDoor": "èªå¨é¨", "MapArea": "åºå", "Marker": "ç¹ä½", "Vehicle": "æºå¨äºº", "All": "ææç»æ", "AirShowerDoor": "é£æ·é¨"}, "autoDoor": "èªå¨é¨", "manualDoorOpening": "æ<PERSON><PERSON><PERSON>¼é¨", "manualDoorClosing": "æ<PERSON><PERSON><PERSON>³é¨", "automaticDoorCoding": "èªå¨é¨ç¼ç ", "autoDoorStatus": {"OPEN": "å·²å¼å¯", "CLOSE": "å·²å³é­", "COMMUNICATION_ERROR": "éè®¯å¼å¸¸", "OPERATING": "æ§å¶ä¸­", "PARAM_ERROR": "åæ°å¼å¸¸"}, "areaTypes": {"ControlArea": "å°æ§åºå", "SingleAgvArea": "åæºåºå", "ShowArea": "æ¾ç¤ºåºå", "ChannelArea": "ééåºå", "NoRotatingArea": "ç¦æåºå", "NoParkingArea": "ç¦ååºå", "ForbiddenArea": "ç¦å¥åºå", "TrafficArea": "äº¤ç®¡åºåï¼æå¡ç«¯ï¼", "ThirdSystemTrafficArea": "äº¤ç®¡åºåï¼å®¢æ·ç«¯ï¼"}, "markerTypes": {"ChargingMarker": "åçµç¹", "WorkMarker": "å·¥ä½ç¹", "NavigationMarker": "å¯¼èªç¹"}, "AirShowerDoor": "é£æ·é¨", "showerDoorCode": "é£æ·é¨ç¼ç ", "manualDoorOpening1": "æå¨å¼é¨1", "manualDoorClosing1": "æå¨å³é¨1", "manualDoorOpening2": "æå¨å¼é¨2", "manualDoorClosing2": "æå¨å³é¨2", "gate1Status": "é¨1ç¶æ", "gate2State": "é¨2ç¶æ", "areaCoding": "åºåç¼ç ", "robotCoding": "æºå¨äººç¼ç ", "offSite": "ç¦»åº", "pointPosition": "ç¹ä½", "coordinate": "<PERSON><PERSON> ", "elevator": "çµæ¢¯", "elevatorCode": "çµæ¢¯ç¼ç ", "searchMapElements": "æç´¢å°å¾åç´ ", "currentMap": "å½åå°å¾", "map": "å°å¾", "currentCoordinate": "å½ååæ ", "pointType": "ç¹ä½ç±»å", "customCoding": "èªå®ä¹ç¼ç ", "pointCoding": "ç¹ä½ç¼ç ", "batchNew": "æ¹éæ°å»º", "lineNumber": "è¡æ°", "lineSpacing": "è¡é´è·", "numberOfColumns": "åæ°", "spaceBetweenColumns": "<PERSON><PERSON>·", "element": "åç´ ", "searchElement": "æç´¢åç´ ", "area": "åºå", "select": "éä¸­", "justificationLeft": "å·¦å¯¹é½", "justifyRight": "å³å¯¹é½", "topJustification": "ä¸å¯¹é½", "alignBottom": "ä¸å¯¹é½", "horizontalEquidistance": "æ°´å¹³ç­é´è·", "verticalEquidistance": "åç´ç­é´è·", "exportDraft": "å¯¼åºèç¨¿", "publishMap": "åå¸å°å¾", "scrapDraft": "åºå¼èç¨¿", "elementList": "åç´ åè¡¨", "unidirectionalPath": "ååè·¯å¾", "bidirectionalPath": "ååè·¯å¾", "quiescentTime": "éæ­¢æ¶é´", "networkIp": "ç½ç»IP", "door": "é¨", "networkPort": "ç½ç»ç«¯å£", "openDoorControlAddress": "å¼é¨æ§å¶å°å", "openDoorControlAddressTip": "æ§å¶é£æ·é¨å¼é¨çå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç åå¥è¯¥å°å", "openAutoDoorControlAddressTip": "æ§å¶èªå¨é¨å¼é¨çå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç åå¥è¯¥å°å", "doorControlAddress": "å³é¨æ§å¶å°å", "doorControlAddressTip": "æ§å¶é£æ·é¨å³é¨çå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç åå¥è¯¥å°å", "autoDoorControlAddressTip": "æ§å¶èªå¨é¨å³é¨çå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç åå¥è¯¥å°å", "openStateAddress": "å¼é¨ç¶æå°å", "openStateAddressTip": "é£æ·é¨å¼é¨å®æçå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç è¯»åè¯¥å°å", "autoOpenStateAddressTip": "èªå¨é¨å¼é¨å®æçå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç è¯»åè¯¥å°å", "closedAddress": "å³é¨ç¶æå°å", "closedAddressTip": "é£æ·é¨å³é¨å®æçå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç è¯»åè¯¥å°å", "autoClosedAddressTip": "èªå¨é¨å³é¨å®æçå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç è¯»åè¯¥å°å", "bindingPath": "ç»å®è·¯å¾", "pleaseSelectAPath": "è¯·éæ©è®¾å¤æå¨çè·¯å¾", "noPointsOfIntersectionCanBeAdded": "é£æ·é¨çä¸¤æ¡è·¯å¾å¿é¡»æäº¤ç¹", "duplicatePathsCannotBeBound": "ä¸è½ç»å®éå¤è·¯å¾", "areaType": "åºåç±»å", "ControlArea": "å°æ§åºå", "SingleAgvArea": "åæºåºå", "ShowArea": "æ¾ç¤ºåºå", "ChannelArea": "ééåºå", "NoRotatingArea": "ç¦æåºå", "NoParkingArea": "ç¦ååºå", "ForbiddenArea": "ç¦å¥åºå", "TrafficArea": "äº¤ç®¡åºåï¼æå¡ç«¯ï¼", "ThirdSystemTrafficArea": "äº¤ç®¡åºåï¼å®¢æ·ç«¯ï¼", "multipleValues": "å¤ä¸ªå¼", "displayName": "æ¾ç¤ºåç§°", "areaColor": "åºåé¢è²", "mapPoint": "å°å¾ç¹ä½", "callAddress": "å¼æ¢¯å°å", "callAddressTip": "æ§å¶çµæ¢¯åéçå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç åå¥è¯¥å°å", "arrivalStatusAddress": "å°è¾¾ç¶æå°å", "arrivalStatusAddressTip": "å¤æ­çµæ¢¯æ¯å¦å°è¾¾è¯¥å°å¾çå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç è¯»åè¯¥å°å", "statusAddressOfTheLadderDoor": "æ¢¯é¨ç¶æå°å", "statusAddressOfTheLadderDoorTip": "å¤æ­æ¯å¦å·²æå¼çµæ¢¯é¨çå°åï¼è°åº¦ç³»ç»ä½¿ç¨åè½ç è¯»åè¯¥å°å", "path": "è·¯å¾", "mapCoding": "å°å¾ç¼ç ", "mapName": "å°å¾åç§°", "mapType": "å°å¾ç±»å", "mapResolution": "å°å¾åè¾¨ç", "mapSize": "å°å¾å°ºå¯¸", "originMigration": "åç¹åç§»", "releaseTime": "åå¸æ¶é´", "qrCodeMap": "äºç»´ç å°å¾", "laserMap": "æ¿åå°å¾", "normalResolution": "æ­£å¸¸åè¾¨ç", "highResolution": "é«åè¾¨ç", "enableParking": "å¯ç¨æ³è½¦", "networkType": "è·¯ç½ç¹ç±»å", "networkTypeList": {"0": "äº¤åè·¯ç½ç¹", "1": "æ®éè·¯ç½ç¹"}, "chargingAttribute": "åçµå±æ§", "chargingDirection": "åçµæ¹å", "dockingType": "å¯¹æ¥æ¹å¼", "buttJoint": "è½¦å¤´å¯¹æ¥", "buttbutt": "è½¦å°¾å¯¹æ¥", "leftSideDocking": "å·¦ä¾§å¯¹æ¥", "rightSideDocking": "å³ä¾§å¯¹æ¥", "reflectiveStripFeaturesContactPoints": "Vååè´´è¯å«", "vTypeFeatureContact": "V<PERSON>æ¿è¯å«", "pathType": "è·¯å¾ç±»å", "stationCode": "å·¥ä½ç¼ç ", "pathWeight": "è·¯å¾æé", "vehicleTypeRestriction": "è½¦åéå¶", "lackOfRobotTypes": "ç¼ºå°æºå¨äººç±»å", "movingSpeed": "ç§»å¨éåº¦", "rotationalSpeed": "æè½¬éåº¦", "movingAcceleration": "ç§»å¨å éåº¦", "rotationalAcceleration": "æè½¬å éåº¦", "moveObstacleAvoidanceArea": "ç§»å¨é¿éåºå", "obstacleAvoidanceArea": "é¿éåºå", "rotationObstacleRegion": "æè½¬é¿éåºå", "noseDirection": "è½¦å¤´æå", "potholeDetection": "åæ´æ£æµ", "dObstacleAvoidance": "<PERSON><PERSON>", "featureNavigation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navigationPath": "å¯¼èªè·¯å¾", "QR_Down": "äºç»´ç å¯¹æ¥", "LeaveDocking": "è±ç¦»å¯¹æ¥", "Shelflegs": "è´§æ¶è¿å¯¹æ¥", "Symbol_V": "Våæ¿å¯¹æ¥", "Reflector": "ååæ¿å¯¹æ¥", "Pallet": "æçå¯¹æ¥", "unsetRegion": "ä¸è®¾ç½®åºå", "pleaseSelectAPointPosition": "è¯·éæ©ç¹ä½", "selectPoint": "éæ©ç¹ä½", "aPointBitHasBeenSelected": "å·²éç¹ä½", "clear": "æ¸ç©º", "pleaseSelectParkingLocation": "è¯·éæ©ï¼æ³è½¦ç¹", "displayElement": "æ¾ç¤ºåç´ ", "pathDirection": "è·¯å¾æ¹å", "displayText": "æ¾ç¤ºæå­", "areaName": "åºååç§°", "elementSize": "åç´ å¤§å°", "forceTheElementSizeToChange": "å¼ºå¶ä¿®æ¹åç´ å¤§å°", "backgroundSettings": "èæ¯è®¾ç½®", "operationHabit": "æä½ä¹ æ¯", "doubleClickCreateElement": "åå»åå»ºåç´ ", "backgroundColor": "èæ¯é¢è²", "showBackground": "æ¾ç¤ºèæ¯å¾", "displaysThePngDiagramBorder": "æ¾ç¤ºå°å¾è¾¹ç", "limitTheDistanceBetweenPointAndPoint": "éå¶ç¹ä½ä¸ç¹ä½çè·ç¦»", "limitTheDistanceBetweenPointsAndPaths": "éå¶ç¹ä½ä¸è·¯å¾çè·ç¦»", "displayRobot": "æ¾ç¤ºæºå¨äºº", "realTimePointCloud": "å®æ¶ç¹äº", "mapEditor": "å°å¾ç¼è¾", "monitoring": "çæ§", "editParameter": "ç¼è¾åæ°", "parkingOrNot": "æ¯å¦æ³è½¦", "noMapYet": "ææ å°å¾", "pleaseRepositionTheRobot": "è¯·éå®ä½æºå¨äººï¼Escåæ¶æä½", "pleaseSelectAnEndpoint": "è¯·éæ©EndPointç¹ä½", "recordingPoint": "å½<PERSON>¶ç¹ä½", "pointRecordingSucceeded": "ç¹ä½å½å¶æå", "discardDraftTip": "è¯¥æä½ä¸å¯åéï¼è¯·ç¡®è®¤æ¯å¦ä¸¢å¼?", "publishMapOrNot": "æ¯å¦åå¸å°å¾ï¼", "failedToPublishMap": "åå¸å°å¾å¤±è´¥", "publishMapSuccessfullyTip": "åå¸å°å¾æåï¼å°èªå¨åæ­¥å°å¾å°æºå¨äºº", "exitTheRecordingPoint": "<PERSON><PERSON><PERSON><PERSON>½<PERSON>¶ç¹ä½", "recordingPointTip": "ââ â â ââ ç§»å¨æè½¬; âEnterâ å½å¶ç¹ä½", "remoteControlMode": "â â â ââ ç§»å¨æè½¬; âEscâ éåºé¥æ§æ¨¡å¼", "thereAreNoBotsOnTheCurrentMap": "å½åå°å¾ä¸å­å¨æºå¨äººï¼è¯·åå¨æºå¨äººåè¡¨é¡µåæ¢æºå¨äººå°è¯¥å°å¾", "thePathSelectionOperationIsCancelled": "åæ¶éæ©è·¯å¾æä½", "pleaseSelectRobot": "è¯·éæ©æºå¨äºº", "robotIsNotConnectedTip": "æºå¨äººæªè¿æ¥ï¼å½å¶å¤±è´¥", "haveBeenPublishedTip": "è¯¥å°å¾å·²è¢«åå¸,è¯·éååè¡¨", "haveBeenDiscardedTip": "è¯¥å°å¾èç¨¿å·²è¢«ä¸¢å¼,è¯·éååè¡¨", "thePointIsCreatedSuccessfully": "ç¹ä½åå»ºæå", "outletElevator": "å¯¼åºçµæ¢¯", "leadInElevator": "å¯¼å¥çµæ¢¯", "transferElevatorFileJson": "å°çµæ¢¯æä»¶ææ½å¨æ­¤åºåå", "parkingSign": "æ³è½¦æ è¯", "locationMapList": "å®ä½å¾åè¡¨", "locationMap": "å®ä½å¾", "leadinMap": "å¯¼å¥å®ä½å¾", "setAsDefault": "è®¾ä¸ºé»è®¤", "setTheRobotType": "è®¾å®æºå¨äººç±»å", "deleteLocationMap": "å é¤å®ä½å¾", "locationMapDeletedTip": "å®ä½å¾å·²è¢«å é¤ï¼è¯·éæ°æå¼", "currentAngle": "å½åè§åº¦", "fixedAngle": "åºå®è§åº¦", "areavehicleTypeNameTip": "æºå¨äººç±»åä¸ºç©ºæ¶ï¼æææºå¨äººç±»åçæ", "directionToast": "å¼åªè½å¨-180å°180ä¹é´", "offsetX": "åå·®X", "offsetY": "åå·®Y", "offsetAngle": "åå·®è§åº¦", "dockingDirection": "å¯¹æ¥æ¹å", "Head": "è½¦å¤´å¯¹æ¥", "Tail": "è½¦å°¾å¯¹æ¥", "revocation": "æ¤é", "renewal": "éå", "cameraObstacleAvoidance": "ç¸æºé¿é", "templateNumber": "æ¨¡æ¿ç¼å·", "text1": "ææ¬1", "text2": "ææ¬2", "text3": "ææ¬3", "number1": "æ°å­1", "number2": "æ°å­2", "number3": "æ°å­3", "obstacleAvoidance": "èªä¸»ç»é", "enableAvoidance": "å¯ç¨é¿è®©", "batchNewMaximumTips": "æ¹éæ°å»ºç¹ä½æ°éæå¤§éå¶ä¸º2000ä¸ª", "unmapped": "æ å®ä½å¾æ°æ®", "key": "å­æ®µ", "value": "å¼", "addExtendParam": "æ·»å åæ°", "fieldDplication": "å­æ®µä¸åè®¸éå¤ï¼", "addOrientationAngle": "å¢å å¯¼èªè§åº¦", "checkDirectionalNavigation": "æ ¡éªå®åå¯¼èª", "navigationAngle": "å¯¼èªè§åº¦", "operationFailedMandatoryFieldsCannotEmpty": "æä½å¤±è´¥ï¼çé¢å¿å¡«é¡¹ä¸å¯ä¸ºç©º", "readingFunctionCode": "è¯»åè½ç ", "writingFeatureCode": "å<PERSON><PERSON><PERSON> ", "elevatorUsageScenario": "çµæ¢¯ä½¿ç¨åºæ¯", "elevatorModeControlAddress": "çµæ¢¯æ¨¡å¼æ§å¶å°å", "enterRobotModeValue": "è¿å¥æºå¨äººæ¨¡å¼å¼", "exitRobotModeValue": "éåºæºå¨äººæ¨¡å¼å¼", "loadDetectedValue": "æè´§ç¶æå¼", "doorOpenedValue": "æ¢¯é¨æå¼ç¶æå¼", "doorClosedValue": "æ¢¯é¨å³é­ç¶æå¼", "arrivalStatusValue": "å°è¾¾ç¶æå¼", "robotOnly": "æºå¨äººä¸ç¨", "humanAndRobotShared": "äººæºå±ç¨", "modeStatusAddress": "æ¨¡å¼ç¶æå°å", "robotModeStatusValue": "æºå¨äººæ¨¡å¼ç¶æå¼", "outgoingAddress": "å¤å¼å°å", "internalCallAddress": "åå¼å°å", "outOperateOpenValue": "å¤å¼å¼é¨å¼", "innerOperateOpenValue": "<PERSON><PERSON>¼å¼é¨å¼", "goodsCheckAddress": "æè´§ç¶æå°å", "currentStatus": "éè®¯ç¶æ", "goodsCheckAddressTip": "è¿å¥åæ ¡éªè®¾å¤åæ¯å¦æè´§ï¼å¦ææè´§åä¸è¿å¥ãè¯¥å°åä¸ºç©ºæ¶ä¸æ ¡éª", "currentStatusObject": {"NORMAL": "éè®¯æ­£å¸¸", "ERROR": "éè®¯å¼å¸¸"}, "open": "å¼é¨", "close": "å³é¨", "occupyCode": "å ç¨æ¹", "occupyVehicleCode": "å ç¨æºå¨äººç¼ç ", "manualRelease": "æ<PERSON><PERSON><PERSON><PERSON>¾", "manualReleaseTip": "æä½ä¸å¯åéï¼ç¡®è®¤æ¯å¦éæ¾åºåå ç¨ï¼", "doubleClickModel": {"ControlArea": "åå»å¤ç¨å°æ§åºå", "SingleAgvArea": "åå»å¤ç¨åæºåºå", "ShowArea": "åå»å¤ç¨æ¾ç¤ºåºå", "ChannelArea": "åå»å¤ç¨ééåºå", "NoRotatingArea": "åå»å¤ç¨ç¦æåºå", "NoParkingArea": "åå»å¤ç¨ç¦ååºå", "ForbiddenArea": "åå»å¤ç¨ç¦å¥åºå", "ChargingMarker": "åå»å¤ç¨åçµç¹", "WorkMarker": "åå»å¤ç¨å·¥ä½ç¹", "NavigationMarker": "åå»å¤ç¨å¯¼èªç¹", "unidirectionalPath": "åå»å¤ç¨ååè·¯å¾", "bidirectionalPath": "åå»å¤ç¨ååè·¯å¾", "TrafficArea": "åå»å¤ç¨äº¤ç®¡åºåï¼æå¡ç«¯ï¼", "ThirdSystemTrafficArea": "åå»å¤ç¨äº¤ç®¡åºåï¼å®¢æ·ç«¯ï¼"}, "configureAreaResourceApplicationAddress": "éç½®åºåèµæºç³è¯·å°å>>", "chargeWhileWorking": "ä½ä¸æ¶åçµ", "chargingPile": "åçµæ¡©", "smartCharge": "å¯¹æ¥åçµ", "commonCharge": "å¯¼èªåçµ", "on": "å¼å¯", "off": "å³é­", "chargingMethod": "åçµæ¹å¼", "dockingStrategy": "å¯¹æ¥ç­ç¥"}, "mapList": {"releaseStatus": "åå¸ç¶æ", "releaseStatusList": {"1": "å·²åå¸", "0": "æªåå¸"}, "resolution": "<PERSON><PERSON>¾<PERSON>", "dimension": "å°ºå¯¸", "radioList": {"normalResolution": "æ­£å¸¸åè¾¨ç(0.05)", "highResolution": "é«åè¾¨ç(0.03)"}, "copyMap": "å¤å¶å°å¾", "newMapCoding": "æ°å°å¾ç¼ç ", "newMapName": "æ°å°å¾åç§°", "selectImportMode": "éæ©å¯¼å¥æ¹å¼", "importAll": "å¯¼å¥å¨é¨", "leadInNetwork": "å¯¼å¥è·¯ç½", "leadInLaser": "å¯¼å¥æ¿å", "importMap": "å¯¼å¥å°å¾", "importMapTip": "å¯¼å¥æååï¼IDç¸åçå°å¾æ°æ®ä¼è¢«è¦çæ", "fileIsSuccessfullyImportedTip": "å°å¾æä»¶å¯¼å¥æå"}, "robotManage": {"storageLocation": "å¨ä½", "vehicle": "è½½å·", "status": "ç¶æ", "vehicleCode": "ç¼å·", "controlMode": "æ§å¶æ¨¡å¼", "controlModeList": {"Manual": "æå¨æ§å¶", "Auto": "èªå¨æ§å¶", "Repair": "æ£ä¿®æ¨¡å¼"}, "dispatch": "è°åº¦", "scheduleMode": "è°åº¦æ¨¡å¼", "scheduleModeList": {"ManualSchedule": "æå¨è°åº¦", "AutoSchedule": "èªå¨è°åº¦"}, "connectStatus": "è¿æ¥ç¶æ", "connectStatusList": {"Disconnect": "æªè¿æ¥", "Connect": "å·²è¿æ¥"}, "softEmerStopStatus": "è¿è¡", "softEmerStopStatusList": {"Close": "è¿è¡ä¸­", "Open": "æåä¸­"}, "storageState": {"FULL": "ææ", "EMPTY": "æ æ", "ERROR": "æ£æµéè¯¯"}, "errorState": {"0": "æ­£å¸¸", "1": "å¼å¸¸"}, "softEmerStopStatusListBut": {"Close": "æ¢å¤", "Open": "<PERSON><PERSON>"}, "abnormalStatus": "å¼å¸¸ç¶æ", "abnormalStatusList": {"Abnormal": "å¼å¸¸", "Normal": "æ å¼å¸¸"}, "workbenchAbnormalStatusList": {"Abnormal": "å¼å¸¸", "Normal": "æ­£å¸¸"}, "locatedStatus": "å®ä½ç¶æ", "locatedStatusList": {"NotLocated": "æªå®ä½", "Located": "å·²å®ä½"}, "workStatus": "å·¥ä½ç¶æ", "workStatusList": {"Offline": "ç¦»çº¿", "Work": "å¿ç¢", "Free": "ç©ºé²"}, "missionName": "ä»»å¡", "missionWorkActions": "å¨ä½", "rate": "çµé", "vehicleTypeName": "æºå¨äººç±»å", "vehicleGroupName": "æºå¨äººåç»", "pilotVersion": "Pilotçæ¬", "mosVersion": "Mosçæ¬", "ip": "IPå°å", "mac": "Macå°å", "pause": "<PERSON><PERSON>", "restore": "æ¢å¤", "manual": "æ<PERSON>¨", "semiAutomatic": "<PERSON><PERSON><PERSON><PERSON>", "automatic": "èªå¨", "restart": "éå¯", "clear": "æ¸ç", "assignMap": "æå®å°å¾", "stopTask": "åæ­¢ä»»å¡", "viewLog": "æ¥çæ¥å¿", "batchOperation": "æ¹éæä½", "setUp": "è®¾å®", "details": "è¯¦æ", "allocationProcess": "<PERSON><PERSON><PERSON>¿ç¨", "softEmergencyStopBut": {"openSoftEmergencyStop": "<PERSON><PERSON>", "closeSoftEmergencyStop": "æ¢å¤"}, "enableCharging": "å¯ç¨åçµ", "enableParking": "å¯ç¨æ³è½¦", "chargingMarker": "åçµç¹", "parkingMarker": "æ³è½¦ç¹", "chargingList": {"2": "é»è®¤", "1": "å¯ç¨", "0": "ç¦ç¨"}, "relocation": "éå®ä½", "powerOff": "å³æº", "importRobotGrouping": "å¯¼å¥æºå¨äººåç»", "statistics": "ç»è®¡", "offSite": "ç¦»åº", "historicalTask": "åå²ä»»å¡", "setType": "è®¾å®ç±»å", "setGroup": "è®¾å®åç»", "pleaseSelectARobotGroup": "è¯·éæ©æºå¨äººåç»", "pleaseSelectARobotType": "è¯·éæ©æºå¨äººç±»å", "importedRobotType": "å¯¼å¥æºå¨äººç±»å", "allowedRotation": "åè®¸æè½¬", "canRotateList": {"true": "æ¯", "false": "å¦"}, "currentLocationMap": "å½åå®ä½å¾", "switchMap": "åæ¢å°å¾", "carryTask": "æ§è¡ä»»å¡", "turnCharge": "å¼å¯åçµ", "openParking": "å¼å¯æ³è½¦", "autoChargeState": {"0": "ç¦æ­¢èªå¨åçµ", "1": "å¯ç¨èªå¨åçµ"}, "autoParkState": {"0": "ç¦æ­¢èªå¨æ³è½¦", "1": "å¯ç¨èªå¨æ³è½¦"}, "automaticRelocation": "èªå¨éå®ä½", "manualRelocation": "æå¨éå®ä½", "resetting": "å¤ä½", "ordinaryCharging": "æ®éåçµ", "buttReset": "å¯¹æ¥å¤ä½", "odom": "éç¨æ°"}, "actionSetting": {"code": "ç¼ç ", "name": "åç§°", "type": "åç±»", "enterParamCount": "å¥åæ°é", "outParamCount": "åºåæ°é", "add": "æ°å¢", "export": "å¯¼åº", "import": "å¯¼å¥", "preview": "é¢è§", "notice": "æç¤º", "icon": "å¾æ ", "parameter": "åæ°", "addInputParameters": "æ°å¢è¾å¥åæ°", "addOutputParameters": "æ°å¢è¾åºåæ°", "parameterType": "åæ°ç±»å", "parameterCode": "åæ°ç¼ç ", "parameterName": "åæ°åç§°", "category": "ç±»å«", "iconUploadComplete": "å¾æ ä¸ä¼ å®æ", "isCommonList": {"true": "æ¯", "false": "å¦"}, "isAllowSkipList": {"true": "æ¯", "false": "å¦"}, "baseTypeList": {"Text": "ææ¬", "Number": "æ°å­", "Common": "éç¨"}, "componentOptions": {"Default": "é»è®¤", "Json": "JSON", "Bool": "å¸å°", "RadioList": "å<PERSON>åè¡¨", "MultiList": "å¤éåè¡¨", "VehicleMapCode": "åéå°å¾", "VehicleMapCodeList": "å¤éå°å¾", "MarkerCode": "åéç¹ä½", "MarkerCodeList": "å¤éç¹ä½", "VehicleCode": "åéæºå¨äºº", "VehicleCodeList": "å¤éæºå¨äºº", "VehicleTypeCode": "åéæºå¨äººç±»å", "VehicleGroupCode": "åéæºå¨äººç»", "WarehouseLocationType": "åéåºä½ç±»å", "WarehouseLocation": "åéåºä½", "WarehouseArea": "åéåºåº", "VehicleGroupCodeList": "å¤éæºå¨äººç»", "VehicleTypeCodeList": "å¤éæºå¨äººç±»å", "TaskType": "åéä»»å¡ç±»å", "customParameter": "èªå®ä¹åæ°", "MultiText": "é¿ææ¬"}, "nodeSettingsImport": "èç¹è®¾ç½®å¯¼å¥", "fileFormatJson": "ææ½æä»¶", "inputBox": "è¾å¥æ¡", "skipAllow": "åè®¸è·³è¿", "retryNum": "éè¯æ¬¡æ°", "allowRetry": "åè®¸éè¯"}, "taskManage": {"code": "ç¼ç ", "name": "åç§°", "status": "ç¶æ", "priority": "ä¼åçº§", "robot": "æºå¨äºº", "callbackUrl": "ä¸æ¸¸å°å", "source": "æ¥æº", "createDate": "åå»ºæ¶é´", "startTime": "å¼å§æ¶é´", "endTime": "ç»ææ¶é´", "log": "æ¥å¿", "taskExecution": "ä»»å¡æ§è¡", "currentNode": "å½åèç¹", "executionTime": "æ§è¡æ¶é¿", "runningLog": "è¿è¡æ¥å¿", "pleaseEnterADescriptionSearch": "è¯·è¾å¥æè¿°æç´¢", "statistics": "ç»è®¡", "runningTime": "è¿è¡æ¶é¿", "runTimes": "è¿è¡æ¬¡æ°", "totalHours": "æ»æ¶é¿", "inputValue": "è¾å¥å¼", "outputValue": "è¾åºå¼", "taskInformation": "ä»»å¡ä¿¡æ¯", "nodeInformation": "èç¹ä¿¡æ¯", "jsonFormatError": "JSONæ ¼å¼éè¯¯", "cancelATask": "åæ¶ä»»å¡", "whetherToCancelATask": "æ¯å¦åæ¶ä»»å¡ï¼", "downloadRecord": "ä¸è½½è®°å½", "optionsStatus": {"Create": "ç­å¾", "Running": "æ§è¡ä¸­", "Finished": "å·²å®æ", "Cancel": "<PERSON><PERSON>¶"}, "optionsSource": {"Api": "æ¥å£", "Manual": "æå¨ä¸å", "Charge": "åçµç­ç¥", "Park": "æ³è½¦ç­ç¥", "Traffic": "äº¤ç®¡ç­ç¥", "Task": "å¶ä»ä»»å¡", "Pda": "PDA"}, "batchCancellation": "æ<PERSON><PERSON><PERSON><PERSON>¶", "uploadRecord": "ä¸ä¼ è®°å½", "importTaskManagement": "å¯¼å¥ä»»å¡ç®¡ç", "uploadLog": "ä¸ä¼ æ¥å¿", "theCompletedOrCanceledTaskIsSelected": "éä¸­äºå·²å®ææå·²åæ¶çä»»å¡", "successfullyCancelledTask": "åæ¶ä»»å¡æå", "failedToCancelTheTaskBecause": "åæ¶ä»»å¡å¤±è´¥,åå ä¸º", "failedToCancelTask": "åæ¶ä»»å¡å¤±è´¥", "successfulOperation": "æ<PERSON>½<PERSON><PERSON>", "operationFailure": "æä½å¤±è´¥", "noPublishedTaskProcessExists": "ä¸å­å¨å·²å¯ç¨çä»»å¡æµç¨", "skip": "è·³è¿", "remainingDistance": "å©ä½è·ç¦»", "totalDistance": "æ»è·ç¦»", "retry": "éè¯", "retryTip": "æ¯å¦éè¯", "skipTip": "ç¡®è®¤è·³è¿è¯¥èç¹ï¼ç´æ¥æ§è¡åç»­èç¹ï¼", "isBreak": "å¯ä¸­æ­", "isBreakObject": {"0": "æ¯", "1": "å¦"}}, "taskType": {"code": "ç¼ç ", "name": "åç§°", "priority": "ä¼åçº§", "selfCheckStatus": "èªæ£ç¶æ", "predictExecTime": "é¢è®¡æ§è¡æ¶é´", "layout": "ç¼æ", "implement": "æ§è¡", "clone": "<PERSON><PERSON>", "eventType": "äºä»¶ç±»å", "eventTypeList": {"Interface": "é»è®¤", "FixedTime": "å®æ¶äºä»¶", "Button": "æé®äºä»¶", "Plc": "å¯å­å¨äºä»¶", "VehiclePlc": "æºå¨äººå¯å­å¨äºä»¶", "VehicleAbnormal": "æºå¨äººå¼å¸¸äºä»¶", "TaskCancel": "ä»»å¡åæ¶äºä»¶", "TaskFinished": "ä»»å¡å®æäºä»¶"}, "optionsTemplateType": {"Common": "éç¨ç±»å", "Event": "äºä»¶ç±»å"}, "optionsPublishStatus": {"Published": "å¯ç¨", "Unpublished": "ç¦ç¨"}, "typeList": {"Common": "æ°å»ºéç¨ç±»å", "Event": "æ°å»ºäºä»¶ç±»å"}, "jobFlowType": "ä½ä¸æµç¨ç±»å", "releaseStatus": "å¯ç¨ç¶æ", "importTaskType": "å¯¼å¥ä»»å¡ç±»å"}, "taskTypeArrangement": {"Business": "éç¨æ§å¶", "Common": "å¸¸ç¨", "Communication": "éè®¯ç»ä»¶", "Process": "æµç¨æ§å¶", "AllocationResource": "åéè<PERSON>", "ObtainResource": "è·åèµæº", "Other": "å¶ä»", "Vehicle": "æºå¨äººæ§å¶", "commonNode": "å¸¸ç¨èç¹", "import": "è¾å¥", "export": "è¾åº", "otherCondition": "å¶ä»æ¡ä»¶", "parallelBranch": "å¹¶è¡åæ¯", "cycleCondition": "å¾ªç¯æ¡ä»¶", "endLoop": "ç»æå¾ªç¯", "start": "å¼å§", "end": "ç»æ", "settingsCommonlyUsed": "è®¾ç½®å¸¸ç¨", "parallel": "å¹¶è¡", "parallelNode": "å¹¶è¡èç¹", "condition": "æ¡ä»¶", "conditionalNode": "æ¡ä»¶èç¹", "circulation": "å¾ªç¯", "loopNode": "å¾ªç¯èç¹", "addCondition": "æ·»å æ¡ä»¶", "settingCommonNodes": "è®¾ç½®å¸¸ç¨èç¹", "addParallel": "æ·»å å¹¶è¡", "displayName": "æ¾ç¤ºåç§°", "thePropertiesAreNotSavedPressEnterToConfirm": "å±æ§æªä¿å­ï¼è¯·ä½¿ç¨âEnterâé®ç¡®è®¤", "cycleTime": "å¾ªç¯æ¶é´", "cycleNumber": "å¾ªç¯æ¬¡æ°", "constantValue": "å®å¼", "setOfConditions": "æ¡ä»¶ç»", "and": "ä¸", "or": "æ", "addConditionGroup": "æ·»å æ¡ä»¶ç»", "lt": "å°äº", "ne": "ä¸ç­äº", "eq": "ç­äº", "gt": "å¤§äº", "ge": "å¤§äºç­äº", "le": "å°äºç­äº", "belong": "å±äº", "contain": "<PERSON><PERSON>«", "eventType": "äºä»¶ç±»å", "ip": "IPå°å", "portNumber": "ç«¯å£å·", "functionCode": "åè<PERSON><PERSON> ", "registerAddress": "å¯å­å¨å°å", "registerValue": "å¯å­å¨å¼", "section": "é¨å", "effectiveScopeRobot": "æºå¨äºº", "effectiveScopeTask": "ä»»å¡", "taskAttribute": "ä»»å¡å±æ§", "conditionalAttribute": "æ¡ä»¶å±æ§", "nodeAttribute": "èç¹å±æ§", "taskVariableInput": "ä»»å¡åéï¼è¾å¥ï¼", "variableName": "åéå", "taskVariableOutput": "ä»»å¡åéï¼è¾åºï¼", "owningNode": "èç¹", "priorityState": {"5": "æé«", "4": "é«", "3": "ä¸­", "2": "ä½", "1": "æä½"}, "interfaceInputFormType": {"Json": "JSON", "MarkerCode": "ç¹ä½", "VehicleCode": "æºå¨äºº", "Default": "é»è®¤", "MultiText": "é¿ææ¬"}, "variableTypeList": {"Default": "é»è®¤", "Bool": "å¸å°", "RadioList": "å<PERSON>åè¡¨", "MultiList": "å¤éåè¡¨", "VehicleMapCode": "åéå°å¾", "VehicleMapCodeList": "å¤éå°å¾", "MarkerCode": "åéç¹ä½", "MarkerCodeList": "å¤éç¹ä½", "VehicleCode": "åéæºå¨äºº", "VehicleCodeList": "å¤éæºå¨äºº", "VehicleTypeCode": "åéæºå¨äººç±»å", "VehicleTypeCodeList": "å¤éæºå¨äººç±»å", "VehicleGroupCode": "åéæºå¨äººç»", "VehicleGroupCodeList": "å¤éæºå¨äººç»", "WarehouseArea": "åéåºåº", "WarehouseLocation": "åéåºä½", "WarehouseLocationType": "åéåºä½ç±»å", "Json": "JSON", "Object": "å¯¹è±¡"}, "variableCategoryList": {"Text": "ææ¬", "Number": "æ°å­", "Common": "éç¨"}, "effectiveScopeTaskState": {"1": "å¨é¨", "2": "é¨å"}, "variableNameDuplication": "åéåéå¤", "settlementOfCondition": "æ¡ä»¶è®¾ç½®", "unpublish": "ç¦ç¨", "haveReleased": "å¯ç¨", "publishingFailedEmptyLoopExists": "å¯ç¨ä»»å¡å¤±è´¥ï¼å­å¨ç©ºå¾ªç¯çèç¹", "publishingFailedTaskMustContainOtherNodes": "å¯ç¨ä»»å¡å¤±è´¥ï¼åæ¶ä»»å¡ä¸­å¿é¡»åå«å¶ä»èç¹", "isParameterMandatoryTip": "å¯ç¨ä»»å¡å¤±è´¥ï¼å­å¨æªè®¾å®åæ°çèç¹", "eventTypeList": {"FixedTime": "å®æ¶äºä»¶", "Button": "æé®äºä»¶", "Plc": "å¯å­å¨äºä»¶", "VehiclePlc": "æºå¨äººå¯å­å¨äºä»¶", "VehicleAbnormal": "æºå¨äººå¼å¸¸äºä»¶", "TaskCancel": "ä»»å¡åæ¶äºä»¶", "TaskFinished": "ä»»å¡å®æäºä»¶", "Interface": "é»è®¤"}, "effectiveDate": "çææ¥æ", "pleaseEnterTheEffectiveStartDate": "è¯·è¾å¥çæå¼å§æ¥æ", "pleaseEnterTheEffectiveEndDate": "è¯·è¾å¥çæç»ææ¥æ", "effectiveTime": "çææ¶é´", "pleaseEnterTheEffectiveStartTime": "è¯·è¾å¥çæå¼å§æ¶é´", "pleaseEnterTheEffectiveEndTime": "è¯·è¾å¥çæç»ææ¶é´", "interval": "é´é", "enableOrNot": "æ¯å¦å¯ç¨", "callBoxNumber": "å¼å«çç¼å·", "buttonNumber": "æé®ç¼å·", "robotNumber": "æºå¨äººç¼å·", "robotLocationMap": "æºå¨äººæå¨å°å¾", "exceptionCoding": "å¼å¸¸ç¼ç ", "anomalyLevel": "å¼å¸¸ç­çº§", "exceptionDetails": "å¼å¸¸è¯¦æ", "robotAssembly": "æºå¨äººéå", "taskPublishingSucceeded": "ä»»å¡å¯ç¨æå", "searchNode": "æç´¢èç¹", "quickAccess": "å¿«æ·æ¹å¼", "quickAccessTip": "å¯ç¨æ­¤é¡¹å°åè®¸å¨çæ§ä¸­å¿çæºå¨äººå¡çåå»ºä»»å¡", "inputParameter": "è¾å¥åæ°", "outputParameter": "è¾åºåæ°", "missionNumber": "ä»»å¡ç¼å·", "customOutput": "èªå®ä¹è¾åº", "customInput": "èªå®ä¹è¾å¥", "pda": "PDA", "pdaTip": "å¯ç¨åå¨ PDA å¯åå»ºæ­¤ä»»å¡"}, "notice": {"levelList": {"1": "æ®é", "2": "è­¦å", "3": "éè¯¯"}, "statusList": {"0": "æ¿æ´»", "1": "å¿½ç¥", "2": "å³é­"}, "ignore": "å¿½ç¥", "level": "ç­çº§", "source": "æ¥æº", "quest": "ä»»å¡", "equipment": "è®¾å¤", "closingTime": "å³é­æ¶é´", "intervalTime": "é´éæ¶é´", "importTheNotificationProfile": "å¯¼å¥éç¥éç½®æä»¶", "isUpload": "æ¯å¦ä¸æ¥", "isUploadState": {"0": "å¦", "1": "æ¯"}}, "exception": {"ignore": "å¿½ç¥", "cancelIgnore": "åæ¶å¿½ç¥", "exceptionLevel": "å¼å¸¸ç­çº§", "sourceSystem": "æ¥æºç³»ç»", "exceptionType": "å¼å¸¸ç±»å", "solution": "è§£å³æªæ½", "exceptionStatus": "å¼å¸¸ç¶æ", "ignoreStatus": "å¿½ç¥ç¶æ", "robot": "æºå¨äºº", "taskId": "ä»»å¡ID", "deviceId": "è®¾å¤ID", "mapName": "å°å¾åç§°", "info": "æ®é", "warning": "è­¦å", "error": "å¼å¸¸", "unread": "æªè¯»", "readAll": "å¨é¨å·²è¯»", "read": "å·²è¯»", "closeTime": "å³é­æ¶é´", "exceptionStatusList": {"0": "æªå³é­", "1": "å·²å³é­"}, "ignoreStatusList": {"0": "æªå¿½ç¥", "1": "å·²å¿½ç¥"}, "exceptionMessage": "å¼å¸¸æ¶æ¯", "exceptionMessageDetails": "å¼å¸¸æ¶æ¯è¯¦æ", "source": "æ¥æº"}, "chargeConfig": {"dialogInputList": {"lowBattery": "è®¾ç½®ä½çµé", "highBattery": "è®¾ç½®é«çµé", "minBatteryValue": "è®¾ç½®æå°åçµçµé", "minChargeTime": "è®¾ç½®æå°åçµæ¶é¿", "bindChargeMarkers": "è®¾ç½®åçµç¹", "chargeTaskTypeId": "è®¾ç½®ä»»å¡"}, "lowBattery": "ä½çµéï¼%ï¼", "highBattery": "é«çµéï¼%ï¼", "minBatteryValue": "æå°åçµçµéï¼%ï¼", "minChargeTime": "æå°åçµæ¶é¿ï¼åï¼", "createTask": "åå»ºä»»å¡", "title": "åçµç­ç¥", "describe": "æºå¨äººä½çµéãç©ºé²æ¶åå»ºåçµä»»å¡ç­ç¥"}, "parkConfig": {"dialogInputList": {"bindParkMarkers": "è®¾ç½®æ³è½¦ç¹", "parkTaskTypeId": "è®¾ç½®ä»»å¡"}, "createTask": "åå»ºä»»å¡", "title": "æ³è½¦ç­ç¥", "describe": "æºå¨äººç©ºé²æ¶åå»ºæ³è½¦ä»»å¡ç­ç¥"}, "trafficConfig": {"title": "äº¤ééç½®", "describe": "æºå¨äººç»è¡ãè·¯å¾ç³è¯·ãå²çªå¤ç", "faultOptions": {"1": "ç­å¾", "2": "ç»è¡"}, "banOptions": {"1": "ç­å¾", "2": "ç»è¡", "3": "é©±èµ¶"}, "collisionOptions": {"1": "å¯¼èªç¹", "2": "åçµç¹", "3": "å·¥ä½ç¹"}, "highPerformanceMode": "é«æ§è½æ¨¡å¼ä¸åºååæºå¨äººç¦æç­åè½å°ä¼åå½±å"}, "errorStatistical": {"vehicleAbnormalPieChart": "æºå¨äººå¼å¸¸æ¯ä¾", "abnormalDetailPieChart": "å¼å¸¸åç±»æ¯ä¾", "avgHandleDurationPieChart": "å¼å¸¸å¹³åå¤çæ¶é´", "newCountLineChart": "æ°å¢å¼å¸¸æ°é", "avgHandleDurationLineChart": "å¼å¸¸å¹³åå¤çæ¶é´"}, "taskStatistical": {"taskStatusPieChart": "ä»»å¡å®ææ¯ä¾", "createTaskCountPieChart": "æ°å»ºä»»å¡æ°é", "avgAllocationDurationPieChart": "å¹³åå<PERSON>æ¶é¿", "avgExecuteDurationPieChart": "å¹³åå·¥ä½æ¶é¿", "createTaskCountLineChart": "æ°å¢ä»»å¡æ°é", "endTaskCountLineChart": "ç»æä»»å¡æ°é", "avgAllocationDurationLineChart": "å¹³åå<PERSON>æ¶é¿", "avgExecuteDurationDurationLineChart": "å¹³åå·¥ä½æ¶é¿"}, "screen": {"agvNumStatistical": "æºå¨äººæ°éç»è®¡", "taskNumStatistical": "ä»»å¡æ°éç»è®¡", "agvTotal": "æºå¨äººæ»æ°é", "totalSize": "ä»»å¡æ»æ°é", "runningSize": "æ§è¡ä¸­ä»»å¡æ°é", "successSize": "ä»»å¡æåæ°é", "cancelSize": "ä»»å¡åæ¶æ°é", "waitSize": "ä»»å¡ç­å¾æ°é", "completionRate": "ä»»å¡è¾¾æç", "title": "è°åº¦ç³»ç»å¯è§åå¤§å±", "visualLargeScreen": "å¯è§åå¤§å±"}, "serverMonitoring": {"serverParameter": "æå¡å¨è§æ ¼", "serverUsage": "æå¡å¨èµæºä½¿ç¨", "cpuLineChart": "CPUä½¿ç¨ç", "memLineChart": "åå­ä½¿ç¨ç", "diskLineChart": "ç¡¬çå®¹éåå", "mysqlLineChart": "MySQLæä½æ¬¡æ°", "cpuCores": "CPUæ ¸æ°", "cpuCoresUnit": "ä¸ª", "totalThreads": "çº¿ç¨æ»æ°", "thread": "çº¿ç¨", "memory": "åå­", "diskCapacity": "ç¡¬çå®¹é", "diskUsage": "ç¡¬çä½¿ç¨ç"}, "statisticsRobots": {"statusLineChart": "æºå¨äººç¶æè¶å¿", "statusPieChart": "æºå¨äººç¶ææ¯ä¾", "utilizeRateLineChart": "æºå¨äººç¨¼å¨ç", "text": "æºå¨äººç»è®¡"}, "taskTypeStatistic": {"taskStatusPieChart": "ä»»å¡å®ææ¯ä¾", "taskCountLineChart": "ä»»å¡æ°é", "avgAllocationDurationLineChart": "å¹³åå<PERSON>æ¶é¿", "avgExecuteDurationLineChart": "å¹³åå·¥ä½æ¶é¿", "text": "ä»»å¡æµç¨ç»è®¡"}, "robotManagementStatistic": {"statusPieChart": "æºå¨äººç¶ææ¯ä¾", "workStatusPieChart": "æºå¨äººå·¥ä½æ¯ä¾", "statusLineChart": "æºå¨äººç¶æè¶å¿", "utilizeRateLineChart": "æºå¨äººç¨¼å¨ç"}, "robotMonitorStatistic": {"taskStatusPieChart": "ä»»å¡ç¶æ", "vehicleStatusPieChart": "æºå¨äººç¶æ", "vehicleBatteryPieChart": "æºå¨äººçµé"}, "licence": {"licenseRemaining": "è®¸å¯è¯æææ", "licenseNotInForce": "è®¸å¯è¯æªçæ", "licenseHasExpired": "è®¸å¯è¯å·²è¿æ", "lackOfLicense": "ç¼ºå°è®¸å¯è¯", "renewalOfLicense": "æ´æ°è®¸å¯è¯", "deleteLicense": "å é¤è®¸å¯è¯", "renewalAuthorization": "æ´æ°ææ"}, "logins": {"userLogin": "ç¨æ·ç»å½", "rememberThePassword": "è®°ä½å¯ç ", "copyrightShenzhenYouaiZhiheCoLtd": "çæææ æ·±å³ä¼è¾æºåæºå¨äººç§ææéå¬å¸"}, "log": {"causeOfFailure": "å¤±è´¥åå ", "responseTime": "ååºæ¶é¿", "url": "URL", "requestInformation": "è¯·æ±ä¿¡æ¯", "returnInformation": "è¿åä¿¡æ¯", "successList": {"true": "<PERSON><PERSON>", "false": "å¤±è´¥"}, "user": "ç¨æ·", "ipAddressOfTheClient": "å®¢æ·ç«¯ipå°å", "operatingTime": "æä½æ¶é´", "typeList": {"Error": "éè¯¯", "Running": "ä¿¡æ¯", "Warning": "è­¦å"}, "category": "ç±»å«", "data": "æ°æ®", "lastTime": "æåæ´æ°æ¶é´", "downloadDetailsLog": "ä¸è½½è¯¦ç»æ¥å¿", "filename": "æä»¶å", "message": "æ¥æ"}, "systemConfiguration": {"licenseAllocation": "è®¸å¯è¯éç½®", "licenseAllocationDescribe": "è®¸å¯è¯ä¿¡æ¯", "storageConfiguration": "å­å¨éç½®", "storageConfigurationDescribe": "æ¥å¿æ°æ®æ¸çãæä»¶æ°æ®æ¸çãä¸å¡æ°æ®æ¸ç", "pushInterfaceConfiguration": "æ¨éæ¥å£éç½®", "robotState": "æºå¨äººç¶æ"}, "storageLocation": {"reservoirArea": "åºåº", "row": "æ", "column": "å", "layer": "å±", "operatingHeight": "ä½ä¸é«åº¦", "jobPoint": "ä½ä¸ç¹ä½", "occupiedState": "ç¶æ", "containerBarCode": "å®¹å¨æ¡ç ", "storyHeight": "å±é«åº¦", "occupyStatus": {"Lock": "éå®", "Free": "ç©ºé²", "Store": "å­å¨"}, "setPoint": "è®¾å®ç¹ä½", "importDatabaseLocationArea": "å¯¼å¥åºåº", "importLibraryType": "å¯¼å¥åºä½ç±»å", "importLocation": "å¯¼å¥åºä½", "usageStatus": {"Disable": "ç¦ç¨", "Enable": "å¯ç¨"}, "enabledState": "å¯ç¨ç¶æ"}, "today": "ä»å¤©", "yesterday": "æ¨å¤©", "thisWeek": "æ¬å¨", "lastWeek": "ä¸å¨", "thisMonth": "æ¬æ", "lastMonth": "ä¸æ", "last7Days": "è¿å»7å¤©", "last30Days": "è¿å»30å¤©", "fullTimeOut": "å¨åºå·²æåï¼æºå¨äººä¸åæ¥æ¶æ°ä»»å¡", "onPause": "å¨åºæåä¸­ï¼å©ä½æªæåæºå¨äººï¼", "fullRecoveryUnderway": "å¨åºæ¢å¤ä¸­", "completeTimeout": "å¨åºæåæå", "fullRecoverySuccessful": "å¨åºæ¢å¤æå", "OperationIsTooFrequent": "æä½å¤ªé¢ç¹äº", "save": "ä¿å­", "extendedAttribute1": "æ©å±å±æ§1", "extendedAttribute2": "æ©å±å±æ§2", "extendedAttribute3": "æ©å±å±æ§3", "extendedAttribute4": "æ©å±å±æ§4", "extendedAttribute5": "æ©å±å±æ§5", "extendedAttribute6": "æ©å±å±æ§6", "extendedAttribute7": "æ©å±å±æ§7", "extendedAttribute8": "æ©å±å±æ§8", "extendedAttribute9": "æ©å±å±æ§9", "extendedAttribute10": "æ©å±å±æ§10", "containerEncoding": "å®¹å¨ç¼ç ", "currentReservoirArea": "å½ååºåº", "locationType": "åºä½ç±»å", "warehouse": "åºä½", "languageConfiguration": "è¯­è¨éç½®", "languageConfigurationDescribe": "æ·»å ï¼ä¸è½½è¯­è¨å", "storageUpdateTime": "åºå­æ´æ°æ¶é´", "numberTriggers": "è§¦åæ¬¡æ°", "allowRepetition": "åè®¸éå¤", "exceptionMessage": "å¼å¸¸ä¿¡æ¯", "incomingParameter": "ä¼ å¥åæ°", "isAllowRepeatState": {"0": "ä¸åè®¸", "1": "åè®¸"}, "importEvent": "å¯¼å¥äºä»¶", "eventCoding": "äºä»¶ç¼ç ", "beChecking": "æ ¡éªä¸­...", "delLanguageTip": "ç¡®å®è¦å é¤è¯¥è¯­è¨åå?", "endCancelRange": "ç»æåæ¶èå´", "checkValue": "æ ¡éªå¼", "changingName": "ä¿®æ¹åç§°", "taskArrangementNew": {"putAway": "æ¶èµ·", "unfold": "å±å¼", "start": "å¼å§", "end": "ç»æ", "while": "å¾ªç¯æ¡ä»¶", "cancelTask": "åæ¶ä»»å¡", "endWhile": "ç»æå¾ªç¯", "endCancelTask": "ç»æåæ¶èå´", "judge": "æ¡ä»¶", "when": "å¹¶è¡", "otherJudge": "å¶ä»æ¡ä»¶", "cancelWhileTip1": "åæ¶ä»»å¡éé¢ä¸è½åµå¥åæ¶ä»»å¡", "cancelWhileTip2": "å½ååªæ¯æ2å±åµå¥å¾ªç¯"}, "globalPauseExecutingArmScriptIsStop": {"Immediately": "ç«å³åæ­¢", "Later": "å®æååæ­¢"}, "chargingPile": {"serialNumber": "åºåå·", "deviceType": "è®¾å¤ç±»å", "equipmentType": "è®¾å¤åå·", "networkState": "ç½ç»ç¶æ", "runningState": "è¿è¡ç¶æ", "controlMode": "æ§å¶æ¨¡å¼", "dischargeStatus": "æ¾çµç¶æ", "resetStatus": "å¤ä½ç¶æ", "occupancyRobot": "å ç¨æºå¨äºº", "chargeTypes": "åçµç±»å", "setVoltage": "åçµæ å®çµå", "voltage": "å½åçµå", "setCurrent": "åçµæ å®çµæµ", "current": "å½åçµæµ", "setPower": "åçµæ å®åç", "power": "å®æ¶åç", "dcTemp": "DCæ¨¡åæ¸©åº¦", "brushTemp": "å·å¤´æ¸©åº¦", "airTemp": "ç©ºæ°æ¸©åº¦", "durationTimes": "å¥åæ¶é´", "voltageRange": "æ¯æçµåèå´", "softwareVer": "åºä»¶çæ¬", "terminationDischarge": "å¼ºå¶åæ­¢", "networkStatusState": {"online": "å¨çº¿", "offline": "ç¦»çº¿"}, "workStatusState": {"normal": "æ­£å¸¸", "abnormal": "å¼å¸¸"}, "dischargeStatusList": {"discharging": "æ¾çµä¸­", "no_discharge": "æªæ¾çµ"}, "resetStatusList": {"pending": "å¾å¤ä½", "completed": "å·²å¤ä½"}, "controlModeList": {"auto": "èªå¨", "manual": "æ<PERSON>¨"}, "whetherToPerformReset": "ç¡®è®¤æ§è¡å¤ä½ï¼", "WhetherPerformTerminationDischarge": "ç¡®è®¤æ§è¡å¼ºå¶åæ­¢ï¼", "resetBatchPrompt": "{successLength}ä¸ªåçµæ¡©æä½æå, {errorLength}ä¸ªåçµæ¡©æä½å¤±è´¥", "chargingPoint": "åçµç¹"}, "secondaryConfirmationPrompt": "ç¡®è®¤è¦æ§è¡è¯¥æä½åï¼", "followingSystem": "è·éç³»ç»", "customization": "èªå®ä¹", "exceptionNotifyTimeTip": "äºä»¶è§¦åè³å°5åéåæè½åèµ·æ´é«ç­çº§éç¥ï¼", "alwaysConnect": "å§ç»å¯¹æ¥", "doNotConnectUntilTheFinishLine": "éç»ç¹ä¸å¯¹æ¥", "abnormalAlarm": "å¼å¸¸æ¥è­¦", "menuList": {"menu": {"monitoringCenter": "çæ§ä¸­å¿", "PDA": "PDA", "agv": "æºå¨äºº", "task": "ä»»å¡", "equipment": "è®¾å¤", "operations": "è¿ç»´éç½®", "taskManager": "ä»»å¡ç®¡ç", "taskList": "ä»»å¡åè¡¨", "taskType": "ä»»å¡æµç¨", "eventList": "äºä»¶åè¡¨", "robots": "æºå¨äººç®¡ç", "robotList": "æºå¨äººåè¡¨", "robotType": "æºå¨äººç±»å", "robotGroup": "æºå¨äººç»", "mapList": "å°å¾ç®¡ç", "storageLocation": "åºä½ç®¡ç", "storageLocationList": "åºä½åè¡¨", "storageLocationType": "åºä½ç±»å", "storageLocationArea": "åºåºåè¡¨", "notificationManager": "éç¥ç®¡ç", "systemLog": "ç³»ç»æ¥å¿", "operationLog": "æä½æ<PERSON>å¿", "interfaceLog": "æ¥å£æ¥å¿", "runningLog": "è¿è¡æ¥å¿", "setting": "ç³»ç»è®¾ç½®", "schedulingConfiguration": "è°åº¦éç½®", "systemSettings": "ç³»ç»éç½®", "userSettings": "è´¦æ·æé", "notificationSettings": "éç¥æ¨¡æ¿", "nodeSettings": "èç¹è®¾ç½®", "statistical": "ç»è®¡æ¥è¡¨", "statisticsRobots": "æºå¨äººç»è®¡", "taskStatistical": "ä»»å¡ç»è®¡", "errorStatistical": "å¼å¸¸ç»è®¡", "serverMonitoring": "æå¡å¨çæ§", "facility": "è®¾å¤ç®¡ç", "chargingPile": "åçµæ¡©ç®¡ç"}, "button": {"view": "æ¥ç", "relocation": "éå®ä½", "switchMap": "åæ¢å°å¾", "controlMode": "æ§å¶æ¨¡å¼", "dispatchingMode": "è°åº¦æ¨¡å¼", "ordinaryCharging": "æ®éåçµ", "autocharge": "èªå¨åçµ", "automaticParking": "èªå¨æ³è½¦", "pause_resume": "æå/æ¢å¤", "reset": "å¤ä½", "buttReset": "å¯¹æ¥å¤ä½", "restart": "éå¯", "shutdown": "å³æº", "departure": "ç¦»åº", "newTask": "æ°å¢ä»»å¡", "cancelTask": "åæ¶ä»»å¡", "taskDetails": "ä»»å¡è¯¦æ", "execution": "æ§è¡ä»»å¡", "elevato": "çµæ¢¯", "autoDoor": "èªå¨é¨", "airShower": "é£æ·é¨", "bulkExport": "æ¹éå¯¼åº", "batchImport": "æ¹éå¯¼å¥", "cancle": "<PERSON><PERSON>¶", "batchCancellation": "æ<PERSON><PERSON><PERSON><PERSON>¶", "uploadRecord": "ä¸ä¼ è®°å½", "details": "è¯¦æ", "downloadRecord": "ä¸è½½è®°å½", "remark": "å¤æ³¨", "add": "æ°å¢", "del": "å é¤", "edit": "ç¼è¾", "implement": "æ§è¡", "layout": "ç¼æ", "clone": "å¤å¶", "statistic": "ç»è®¡", "enabledState": "å¯ç¨/ç¦ç¨", "assignMap": "æå®å°å¾", "SoftEmergencyStop": "å¼å¯æå/å³é­æå", "schedule": "èªå¨è°åº¦/æå¨è°åº¦", "controlModeState": "æå¨æ§å¶/èªå¨æ§å¶", "historicalTask": "åå²ä»»å¡", "setType": "è®¾å®ç±»å", "setGroup": "è®¾å®åç»", "export": "å¯¼åº", "import": "å¯¼å¥", "ignore": "å¿½ç¥", "activation": "æ¿æ´»", "downloadDetailedLog": "ä¸è½½è¯¦ç»æ¥å¿", "download": "ä¸è½½", "viewRoles": "æ¥çè§è²", "addRoles": "æ°å¢è§è²", "rename": "éå½å", "delRoles": "å é¤è§è²", "viewAccount": "æ¥çè´¦å·", "addAccount": "æ°å¢è´¦å·", "editAccount": "ç¼è¾è´¦å·", "delAccount": "å é¤è´¦å·", "resetPasswords": "éç½®å¯ç ", "containerExit": "å®¹å¨åºåº", "containerEntry": "å®¹å¨å¥åº", "taskView": "ä»»å¡æ¥ç", "taskCancel": "ä»»å¡åæ¶", "changingName": "ä¿®æ¹åç§°", "terminationDischarge": "å¼ºå¶åæ­¢", "stopAlarm": "åæ­¢æ¥è­¦"}}}, "yi": {"switchLanguage": "åæ¢è¯­è¨", "map": "å°å¾", "size": "å°ºå¯¸", "deleteModel": "å é¤åç´ ", "test": "æµè¯", "options": "æä½", "addMarker": "æ°å»ºç¹ä½", "addArea": "æ°å»ºåºå", "canvasReset": "ç»å¸éç½®", "canvasRotate": "ç»å¸æè½¬", "canvasFullScreen": "ç»å¸å¨å±", "displaySetting": "æ¾ç¤ºè®¾ç½®", "addOneWayPath": "æ°å»ºååè·¯å¾", "addTwoWayPath": "æ°å»ºååè·¯å¾", "straightenCarve": "æç´è·¯å¾", "canvasSmallScreen": "ç»å¸å½ä½", "addPath": "æ°å»ºè·¯å¾", "systemSetup": "ä¸ªæ§åè®¾ç½®", "endRecordingPoint": "ç»æå½å¶ç¹ä½", "recordingPoint": "å½<PERSON>¶ç¹ä½", "mapEditor": "å°å¾ç¼è¾", "batchNew": "æ¹éæ°å»º", "rangingPath": "æµéè·ç¦»", "selectedPath": "éä¸­è·¯å¾", "up": "åä¸", "down": "åä¸", "aleft": "åå·¦", "right": "åå³", "hideSelectedPath": "éèè·¯å¾", "smoothPath": "è·¯å¾å¹³æ»"}, "pdaLang": {"login": "ç»å½", "account": "è´¦æ·", "password": "å¯ç ", "changingServerIPAddress": "ä¿®æ¹æå¡å¨IP", "serverIPAddressTip": "æå¡å¨å°å,è¯·è°¨æä¿®æ¹!", "serverAddress": "æå¡å¨å°å", "version": "çæ¬ä¿¡æ¯", "messageExceptionAlarm": "æ¶æ¯å¼å¸¸æ¥è­¦", "logout": "éåºç»å½", "logoutOrNot": "æ¯å¦éåºç»å½?", "pleaseEnterAGVNumber": "è¯·è¾å¥AGVç¼å·", "refreshSuccessful": "å·æ°æå", "message": "æ¶æ¯", "ignore": "å¿½ç¥", "code": "ç¼ç ", "describe": "æ<PERSON>¿°", "robot": "æºå¨äºº", "taskID": "ä»»å¡ID", "operate": "æä½", "operateSuccessfully": "æ<PERSON>½<PERSON><PERSON>", "task": "ä»»å¡", "Abnormal": "å¼å¸¸", "Normal": "æ­£å¸¸", "cancel": "<PERSON><PERSON>¶", "taskdetail": "ä»»å¡è¯¦æ", "taskEntry": "ä»»å¡å¥å", "state": "ç¶æ", "name": "åç§°", "createDate": "åå»ºæ¶é´", "taskDelivery": "ä»»å¡ä¸å", "storageLocation": "åºä½", "storageLocationTip": "è¯·è¾å¥æèæ«æåºä½", "materialType": "ç©æç±»å", "materialTypeTip": "è¯·éæ©ç©æç±»å", "containerCode": "å®¹å¨æ¡ç ", "containerCodeTip": "è¯·è¾å¥æèæ«æå®¹å¨æ¡ç ", "OK": "ç¡®å®", "containerEntry": "å®¹å¨å¥åº", "containerExit": "å®¹å¨åºåº", "scnContainerRepositoryLocation": "è¯·æ«æå®¹å¨æåºä½", "userInformationHasExpired": "ç¨æ·ä¿¡æ¯å·²è¿æ", "networkPrompt": "è¿æ¥å¤±è´¥ï¼è¯·æ£æ¥è¿æ¥æå¡æ¯å¦æ­£ç¡®", "networkPrompt2": "æ æ³è¿æ¥åç«¯æå¡", "Disconnect": "æªè¿æ¥", "Connect": "å·²è¿æ¥", "map": "å°å¾", "type": "ç±»å", "scheduleMode": "è°åº¦", "controlStatus": "æ§å¶", "orientation": "å®ä½", "softStopSwitch": "è¿è¡", "currentNode": "å½åèç¹", "executionTime": "æ§è¡æ¶é¿", "Offline": "ç¦»çº¿", "Work": "å¿ç¢", "Free": "ç©ºé²", "ManualSchedule": "æå¨è°åº¦", "AutoSchedule": "èªå¨è°åº¦", "Manual": "æå¨æ§å¶", "Auto": "èªå¨æ§å¶", "NotLocated": "æªå®ä½", "Located": "å·²å®ä½", "Close": "è¿è¡ä¸­", "Open": "æåä¸­", "mine": "æç", "updating": "æ´æ°ä¸­....", "updatesucceededRestarting": "æ´æ°æåï¼éå¯ä¸­", "updateFailure": "æ´æ°å¤±è´¥", "pleaseEnterOrScan": "è¯·è¾å¥ææ«æ"}}