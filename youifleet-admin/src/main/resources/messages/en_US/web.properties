{"message": {"hello": "Hello", "edit": "Edit", "del": "Delete", "add": "Add", "success": "Success", "fail": "Fail", "reasonIs": "ReasonIs", "update": "Update", "download": "Download", "delete": "Delete", "export": "Export", "multExport": "MultExport", "import": "Import", "multImport": "MultImport", "switchEn": "Switch to EN", "switchzh": "Switch to CN", "options": "Options", "reset": "Reset", "status": "Status", "statusList": {"0": "Disable", "1": "Enable"}, "refresh": "Refresh", "nickname": "Account", "userName": "User Name", "menu": "<PERSON><PERSON>", "role": "Role", "describe": "Description", "updateTime": "Update Time", "createTime": "Create Time", "passWord": "Password", "custom": "User Defined", "requiredTips": "Please Enter", "pleaseSelect": "Please Select", "enableList": {"0": "Yes", "1": "No"}, "addForm": "Add", "editForm": "Edit", "cancel": "Cancel", "submit": "Submit", "totalData": "Total{total} ", "type": "Type", "group": "Group", "range": "Range", "formRules": {"phoneLen": "Enter the correct 11-digit phone number", "port": "Enter the correct port number", "ip": "Enter correct IP address", "isLength": "Longer than {max}characters", "isNumber": "The value must be a number only", "englishFirst_": "English (first character), underscore, and numeric strings only", "englishFirst": "English (initials) and numeric strings only", "english": "English strings only", "isName": "English, underscore, and numeric strings only", "isPureNumber": "Cannot be a pure number and has a maximum length of 20"}, "ChargingMarker": "Charging <PERSON>", "ParkingMarker": "Parking <PERSON><PERSON>", "WorkMarker": "Work Marker", "NavigationMarker": "Navigation Marker", "message": "Message", "language": "Language", "languageSwitch": "Language Change", "importLanguage": "Import Language", "my": "Me", "delTips": "Confirm deletion? Deleted content is not recoverable", "details": "Details", "individuation": "Individuation", "searchSettings": "Search Settings", "complete": "Complete", "connectTimeOut": "Connection timed out", "vehicle": "Robot", "lowBattery": "Low Battery", "highBattery": "High Battery", "pleaseSelectOneVehicle": "Please select at least one robot", "second": "S", "minute": "Min", "minutes": "<PERSON>s", "hour": "Hr", "hour1": "Hr", "day": "Day", "angle": "<PERSON><PERSON>", "speed": "Speed", "orientation": "Orientation", "ip": "IP", "online": "Online", "unconnected": "Offline", "abnormal": "Error", "inExecution": "In Execution", "encoding": "Encoding", "join": "Connect", "professional": "Operate", "controls": "Control", "all": "All", "previous": "Prev Page", "nextPage": "Next Page", "thereIsNoPublishedTaskType": "No enabled task flow exists", "solution": "Solution", "dragTheMapFileZipIntoThisArea": "Drag and drop the map file in this area", "dragLocationMapFileZipIntoThisArea": "Drag and drop the bitmap file in this area", "orClickHereToUpload": "Or click here to upload a file", "fileImport": "File Import", "uploadSuccessfully": "Uploaded Successfully", "confirm": "Confirm", "listSetting": "List Setting", "pleaseSelectAtLeastOneItem": "Please select at least one item", "clickToUpload": "Click To Upload", "fileUploadFailed": "File Upload Failed", "deleteOrNot": "Confirm Delete", "messageCannotReturn": "Deleted Message Unrecoverable", "quit": "Quit", "append": "Add", "deleteAll": "Delete All", "port": "Port", "required": "Required", "variable": "Variable", "defaultValue": "Default Value", "maximumValue": "Max Value", "minimumValue": "Min Value", "yes": "Yes", "no": "No", "value": "Value", "typeName": "Type Name", "batch": "<PERSON><PERSON>", "create": "Create", "batchCreate": "Create in Batches", "login": "<PERSON><PERSON>", "updateAirShower": "Update Air Shower Door", "updateArea": "Update Area", "updateAutodoor": "Update Auto door", "updateElevator": "Update Elevator", "updateMap": "Update Map", "updateMarker": "Update Marker", "updatePath": "Update Path", "deletePath": "Delete Path", "relocationManual": "Relocate", "scrapDraft": "Discard the Draft", "publish": "Publish", "copy": "Copy", "name": "Name", "serialNumber": "Serial Number", "startTime": "Start Time", "endTime": "End Time", "createTask": "Create Task", "noData": "No Data", "arguments": "Parameters", "priority": "Priority", "selectTheDataYouWantToDelete": "Select Data", "selectTheDataThatYouWantToModify": "Select the data to be modified", "passwordInconsistencyTip": "The password does not match the orginal password, please re-enter it.", "theEnteredPasswordIsInconsistent": "The entered password is inconsistent.", "systemServiceException": "The backend service call has failed.", "founder": "Founder", "result": "Result", "meter": "M", "file": "File", "creationMode": "Creation Mode", "externalCoding": "External Coding", "task": "Task", "default": "<PERSON><PERSON><PERSON>", "remark": "Remark", "addRemark": "Add Remark", "form": "Form", "height": "Height", "point": "Point", "noDataAvailable": "No Data Available", "cm": "CM", "userSetting": {"roleManage": "Role Manage", "accountManage": "Account <PERSON>", "auth": "Authority", "addRole": "Add Role", "rename": "<PERSON><PERSON>", "account": "Account", "name": "Name", "role": "Role", "email": "Email", "phone": "Phone", "enable": "Enable Status", "updateDate": "Update Date", "automaticLogout": "Automatic Logout", "password": "Password", "surePassword": "Confirm Password", "enableList": {"1": "Yes", "0": "No"}, "roleName": "Values are limited to 20 characters; Only Chinese, English and numbers", "username": "Values are limited to 20 characters;English Beginning;Only English, underscores, numbers", "realName": "Values are limited to 20 characters;", "autoLogoutTime": "Range[0,9999]", "changePassword": "Change Password", "logout": "Logout", "pleaseSelectARole": "Please Select A Role", "nameTip": "Not allowed to be NULL", "theNameCannotExceed20Characters": "No more than 20 characters are allowed"}, "changePassword": {"changePassword": "Change Password", "resetPassword": "Reset Password", "oldPassword": "Original Password", "loginUserPassword": "Login User Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password"}, "map": {"markerName": "No more than 20 characters are allowed;Must start with English;Only support English, underscore, and numbers", "type": {"Elevator": "Elevator", "AutoDoor": "Automatic Door", "MapArea": "Area", "Marker": "<PERSON><PERSON>", "Vehicle": "Robot", "All": "All Results", "AirShowerDoor": "Air Shower Door"}, "autoDoor": "Automatic Door", "manualDoorOpening": "Open the door manually", "manualDoorClosing": "Close the door manually", "automaticDoorCoding": "Automatic Door Coding", "autoDoorStatus": {"OPEN": "Opened", "CLOSE": "Closed", "COMMUNICATION_ERROR": "Communication Error", "OPERATING": "Operating", "PARAM_ERROR": "Parameter Error"}, "areaTypes": {"ControlArea": "Closure Area", "SingleAgvArea": "Single Robot Area", "ShowArea": "Remark Area", "ChannelArea": "Aisle Area", "NoRotatingArea": "No-Rotating Area", "NoParkingArea": "No-Parking Area", "ForbiddenArea": "Drive-away Area", "TrafficArea": "Traffic Control Area (Server)", "ThirdSystemTrafficArea": "Traffic Control Area (Client)"}, "markerTypes": {"ChargingMarker": "Charging <PERSON>", "WorkMarker": "Work Marker", "NavigationMarker": "Navigation Marker"}, "AirShowerDoor": "Air Shower Door", "showerDoorCode": "Air Shower Door Code", "manualDoorOpening1": "Door Open Manually 1", "manualDoorClosing1": "Door Close Manually 1", "manualDoorOpening2": "Door Open Manually 2", "manualDoorClosing2": "Door Close Manually 2", "gate1Status": "Gate 1 Status", "gate2State": "Gate 2 Status", "areaCoding": "Area Code", "robotCoding": "Robot Code", "offSite": "Off-Site", "pointPosition": "<PERSON>er Position", "coordinate": "Coordinate", "elevator": "Elevator", "elevatorCode": "Elevator Code", "searchMapElements": "Search Map Elements", "currentMap": "Current Map", "map": "Map", "currentCoordinate": "Current Coordinate", "pointType": "Marker Type", "customCoding": "User-define Coding", "pointCoding": "<PERSON>er Coding", "batchNew": "Add in Batches", "lineNumber": "Number of Lines", "lineSpacing": "Line Spacing", "numberOfColumns": "Number of Columns", "spaceBetweenColumns": "Column Spacing", "element": "Element", "searchElement": "Search Element", "area": "Area", "select": "Select", "justificationLeft": "Align Left", "justifyRight": "Align Right", "topJustification": "Align Top", "alignBottom": "Align Below", "horizontalEquidistance": "Horizontal Equal Spacing", "verticalEquidistance": "Vertical Equal Spacing", "exportDraft": "Export Draft", "publishMap": "Publish Map", "scrapDraft": "Discard the Draft", "elementList": "Element List", "unidirectionalPath": "Unidirectional Path", "bidirectionalPath": "Bidirectional Path", "quiescentTime": "Quiescent Time", "networkIp": "Network IP", "door": "Door", "networkPort": "Network Port", "openDoorControlAddress": "Open Door Control Address", "openDoorControlAddressTip": "Address to control the opening of air shower doors. The dispatching system uses function code to write to this address.", "openAutoDoorControlAddressTip": "Address to control the opening of automatic doors. The dispatching system uses function code to write to this address.", "doorControlAddress": "Cloesd Door Control Address", "doorControlAddressTip": "Address to control the closing of air shower doors. The dispatching system uses function code to write to this address.", "autoDoorControlAddressTip": "Address to control the closing of automatic doors. The dispatching system uses function code to write to this address.", "openStateAddress": "Open Door State Address", "openStateAddressTip": "Address indicating the completion of opening air shower doors. The dispatching system uses function code to read from this address.", "autoOpenStateAddressTip": "Address indicating the completion of opening automatic doors. The dispatching system uses function code to read from this address.", "closedAddress": "Close Door State Address", "closedAddressTip": "Address indicating the completion of closing air shower doors. The dispatching system uses function code to read from this address.", "autoClosedAddressTip": "Address indicating the completion of closing automatic doors. The dispatching system uses function code to read from this address.", "bindingPath": "Binding Path", "pleaseSelectAPath": "Please select the path where the device is located", "noPointsOfIntersectionCanBeAdded": "The two paths of the air shower door must have intersections", "duplicatePathsCannotBeBound": "Duplicate paths cannot be bound", "areaType": "Area Type", "ControlArea": "Closure Area", "SingleAgvArea": "Single Robot Area", "ShowArea": "Remark Area", "ChannelArea": "Aisle Area", "NoRotatingArea": "No-Rotating Area", "NoParkingArea": "No-Parking Area", "ForbiddenArea": "Drive-away Area", "TrafficArea": "Traffic Control Area (Server)", "ThirdSystemTrafficArea": "Traffic Control Area (Client)", "multipleValues": "Multiple Values", "displayName": "Display Name", "areaColor": "Area Color", "mapPoint": "Map Marker", "callAddress": "Elevator Call Address", "callAddressTip": "Address to control elevator ascent and descent. The dispatching system uses function code to write to this address.", "arrivalStatusAddress": "Arrival Status Address", "arrivalStatusAddressTip": "Address to determine whether the elevator has arrived at the map. The dispatching system uses function code to read from this address.", "statusAddressOfTheLadderDoor": "Elevator Door Status Address", "statusAddressOfTheLadderDoorTip": "Address to determine whether the elevator door has been opened. The dispatching system uses function code to read from this address.", "path": "Path", "mapCoding": "Map Coding", "mapName": "Map Name", "mapType": "Map Type", "mapResolution": "Map Resolution", "mapSize": "Map Size", "originMigration": "Origin Migration", "releaseTime": "Publish Time", "qrCodeMap": "QR Code Map", "laserMap": "Laser Map", "normalResolution": "Normal Resolution", "highResolution": "High Resolution", "enableParking": "Enable Parking", "networkType": "Network Type", "networkTypeList": {"0": "Intersection Network Point", "1": "Normal Network Point"}, "chargingProperty": "Charging Property", "chargingAttribute": "Charging Attribute", "chargingDirection": "Charging Direction", "dockingType": "Docking Type", "buttJoint": "Front Docking", "buttbutt": "Rear Docking", "leftSideDocking": "Left Side Docking", "rightSideDocking": "Right Side Docking", "reflectiveStripFeaturesContactPoints": "Reflector Docking Point", "vTypeFeatureContact": "V-shaped Docking Point", "pathType": "Path Type", "stationCode": "Station Code", "pathWeight": "Path Weight", "vehicleTypeRestriction": "Vehicle Type Restriction", "lackOfRobotTypes": "Lack of Robot Types", "movingSpeed": "Moving Speed", "rotationalSpeed": "Rotating Speed", "movingAcceleration": "Moving Acceleration", "rotationalAcceleration": "Rotating Acceleration", "moveObstacleAvoidanceArea": "Move Obstacle Avoidance Scope", "obstacleAvoidanceArea": "Obstacle Avoidance Scope", "rotationObstacleRegion": "Rotation Obstacle <PERSON>", "noseDirection": "Head Direction", "potholeDetection": "Pothole Detection", "dObstacleAvoidance": "3D Obstacle Avoidance", "featureNavigation": "Fusion Feature", "navigationPath": "Navigation Path", "QR_Down": "QR Code Docking", "LeaveDocking": "Undocking", "Shelflegs": "<PERSON><PERSON>", "Symbol_V": "V-shaped Plate Docking", "Reflector": "Reflector Docking", "Pallet": "<PERSON><PERSON><PERSON>", "unsetRegion": "Unset Region", "pleaseSelectAPointPosition": "Please select a point", "selectPoint": "Select Points", "aPointBitHasBeenSelected": "Points Selected", "clear": "Clear", "pleaseSelectParkingLocation": "Please select: <PERSON><PERSON>", "displayElement": "Display Element", "pathDirection": "Path Direction", "displayText": "Display Text", "areaName": "Area Name", "elementSize": "Element Size", "forceTheElementSizeToChange": "Force to Change Element Size", "backgroundSettings": "Background Setting", "operationHabit": "Operation Habit", "doubleClickCreateElement": "Double Click to Create Element", "backgroundColor": "Background Color", "showBackground": "Show Background Image", "displaysThePngDiagramBorder": "Show Map Border", "limitTheDistanceBetweenPointAndPoint": "Limited Distance Between Point and Point", "limitTheDistanceBetweenPointsAndPaths": "Limited Distance Between Point and Path", "displayRobot": "Display Robot", "realTimePointCloud": "Real-Time Point Cloud", "mapEditor": "Map Edit", "monitoring": "Monitor", "editParameter": "Edit Parameter", "parkingOrNot": "Parking or Not", "noMapYet": "No Map", "pleaseRepositionTheRobot": "Please relocate the robot. Press Esc to cancel.", "pleaseSelectAnEndpoint": "Please select an EndPoint marker.", "recordingPoint": "Record Marker", "pointRecordingSucceeded": "<PERSON><PERSON> recorded successfully.", "discardDraftTip": "This operation cannot roll back. Are you sure you want to discard?", "publishMapOrNot": "Publish Map or Not", "failedToPublishMap": "Failed to publish map.", "publishMapSuccessfullyTip": "Map published successfully and then sync to the robots automatically.", "exitTheRecordingPoint": "Exit marker recording", "recordingPointTip": "Use 'â â â â' to move and rotate; press 'Enter' to record the marker.", "remoteControlMode": "Use 'â â â â' to move and rotate; press 'Esc' to exit remote control mode.", "thereAreNoBotsOnTheCurrentMap": "No robots binding on this map. Please select some robots binding to this map on the Robot List module.", "thePathSelectionOperationIsCancelled": "Path selection operation cancelled.", "pleaseSelectRobot": "Please select a robot.", "robotIsNotConnectedTip": "Robot is disconnected. Recording failed.", "haveBeenPublishedTip": "This map has already been published. Please return to the list.", "haveBeenDiscardedTip": "This map draft has been discarded. Please return to the list.", "thePointIsCreatedSuccessfully": "<PERSON><PERSON> created successfully.", "outletElevator": "Export Elevator", "leadInElevator": "Import Elevator", "transferElevatorFileJson": "Drag and drop elevator file here", "doubleClickMultiplexing": "Double-click to multiplex", "parkingSign": "Parking Sign", "locationMapList": "Location Map List", "locationMap": "Location Map", "leadinMap": "Import Location Map", "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "setTheRobotType": "Set Robot Type", "deleteLocationMap": "Delete Location Map", "locationMapDeletedTip": "Location map has been deleted. Please reopen.", "currentAngle": "Current <PERSON><PERSON>", "fixedAngle": "Fixed Angle", "areavehicleTypeNameTip": "When robot type is empty, all robot types are effective.", "directionToast": "Value must be between -180 and 180.", "offsetX": "Offset X", "offsetY": "Offset Y", "offsetAngle": "Offset Angle", "dockingDirection": "Docking Direction", "Head": "Head Docking", "Tail": "Rear Docking", "revocation": "Undo", "renewal": "Redo", "cameraObstacleAvoidance": "Camera Obstacle Avoidance", "templateNumber": "Template Number", "text1": "Text 1", "text2": "Text 2", "text3": "Text 3", "number1": "Number 1", "number2": "Number 2", "number3": "Number 3", "obstacleAvoidance": "Autonomous Obstacle Avoidance", "enableAvoidance": "Enable Avoidance", "batchNewMaximumTips": "No more than 2000 new markers adding in batches.", "unmapped": "No positioning map data", "key": "Field", "value": "Value", "addExtendParam": "Add Parameter", "fieldDplication": "Repeated fields are not allowed!", "addOrientationAngle": "Add Navigation Angle", "checkDirectionalNavigation": "Check Directional Navigation", "navigationAngle": "Navigation Angle", "operationFailedMandatoryFieldsCannotEmpty": "The operation failed. Reruired fields cannot be empty", "readingFunctionCode": "Read Function Code", "writingFeatureCode": "Write Function Code", "elevatorUsageScenario": "Elevator <PERSON><PERSON>", "elevatorModeControlAddress": "Elevator Mode Control Address", "enterRobotModeValue": "Value to Enter Robot Mode", "exitRobotModeValue": "Value to Exit Robot Mode", "loadDetectedValue": "Load Detected Value", "doorOpenedValue": "Elevator Door Opened Value", "doorClosedValue": "Elevator Door Closed Value", "arrivalStatusValue": "Arrival Status Value", "robotOnly": "Robot Only", "humanAndRobotShared": "Human and Robot Shared", "modeStatusAddress": "Mode Status Address", "robotModeStatusValue": "Robot Mode Status Value", "outgoingAddress": "External Call Address", "internalCallAddress": "Internal Call Address", "outOperateOpenValue": "External Call Open-door Value", "innerOperateOpenValue": "Internal Call Open-door Value", "goodsCheckAddress": "Stock Status Address", "currentStatus": "Communication Status", "goodsCheckAddressTip": "Check whether there is stock in the device before entering. If there is stock, do not enter. If the address is empty, no verification will be performed.", "currentStatusObject": {"NORMAL": "Communicating", "ERROR": "Communicating Error"}, "open": "Open Door", "close": "Close Door", "occupyCode": "Occupier", "occupyVehicleCode": "Occupied Robot Code", "manualRelease": "Release Manually", "manualReleaseTip": "The operation cannot roll back. Do you want to release the area usage?", "doubleClickModel": {"ControlArea": "Double click to reuse the closure area", "SingleAgvArea": "Double click to reuse the single robot area", "ShowArea": "Double click to reuse the remark area", "ChannelArea": "Double click to reuse the aisle area", "NoRotatingArea": "Double click to reuse the no-rotating area", "NoParkingArea": "Double click to reuse the no-parking area", "ForbiddenArea": "Double click to reuse the drive-away area", "ChargingMarker": "Double click to reuse the charging marker", "WorkMarker": "Double click to reuse the work marker", "NavigationMarker": "Double click to reuse the navigation marker", "unidirectionalPath": "Double click to reuse unidirectional path", "bidirectionalPath": "Double click to reuse bidirectional path", "TrafficArea": "Double click to reuse traffic control area (server)", "ThirdSystemTrafficArea": "Double click to reuse traffic control area (client)"}, "configureAreaResourceApplicationAddress": "Config Address for Area Resources Application >>", "chargeWhileWorking": "Charge during operation", "chargingPile": "Charging pile", "smartCharge": "Connect and charge", "commonCharge": "Navigation charging", "on": "On", "off": "Off", "chargingMethod": "åçµæ¹å¼(å¾ç¿»è¯)", "dockingStrategy": "å¯¹æ¥ç­ç¥(å¾ç¿»è¯)"}, "mapList": {"releaseStatus": "Publishment Status", "releaseStatusList": {"1": "Published", "0": "Unpublished"}, "resolution": "Resolution", "dimension": "Dimension", "radioList": {"normalResolution": "Normal Resolution (0.05)", "highResolution": "High Resolution (0.03)"}, "copyMap": "Copy Map", "newMapCoding": "New Map Code", "newMapName": "New Map Name", "selectImportMode": "Select Import Mode", "importAll": "Import All", "leadInNetwork": "Import Road Network", "leadInLaser": "Import Laser", "importMap": "Import Map", "importMapTip": "After successful import, map data with the same ID will be overwritten.", "fileIsSuccessfullyImportedTip": "Map file imported successfully"}, "robotManage": {"storageLocation": "Storage Location", "vehicle": "Robot", "status": "Status", "vehicleCode": "Robot Number", "controlMode": "Control Mode", "controlModeList": {"Manual": "Manual Control", "Auto": "Auto Control", "Repair": "Repair Mode"}, "dispatch": "Schedule", "scheduleMode": "Schedule Mode", "scheduleModeList": {"ManualSchedule": "Manual Schedule", "AutoSchedule": "Auto Schedule"}, "connectStatus": "Connection Status", "connectStatusList": {"Disconnect": "Disconnected", "Connect": "Connected"}, "softEmerStopStatus": "Running Status", "softEmerStopStatusList": {"Close": "Running", "Open": "Paused"}, "storageState": {"FULL": "Loaded", "EMPTY": "Empty", "ERROR": "Detection Error"}, "errorState": {"0": "Normal", "1": "Error"}, "softEmerStopStatusListBut": {"Close": "Resume", "Open": "Pause"}, "abnormalStatus": "Error Status", "abnormalStatusList": {"Abnormal": "Error", "Normal": "Normal"}, "workbenchAbnormalStatusList": {"Abnormal": "Error", "Normal": "Normal"}, "locatedStatus": "Location Status", "locatedStatusList": {"NotLocated": "Not Located", "Located": "Located"}, "workStatus": "Work Status", "workStatusList": {"Offline": "Offline", "Work": "Busy", "Free": "Idle"}, "missionName": "Task", "missionWorkActions": "Action", "rate": "Battery Level", "vehicleTypeName": "Robot Type", "vehicleGroupName": "Robot Group", "pilotVersion": "Pilot Version", "mosVersion": "Mos Version", "ip": "IP Address", "mac": "MAC Address", "pause": "Pause", "restore": "Resume", "manual": "Manual", "semiAutomatic": "Semi-Auto", "automatic": "Auto", "restart": "<PERSON><PERSON>", "clear": "Clear", "assignMap": "Assign Map", "stopTask": "Stop Task", "viewLog": "View Log", "batchOperation": "Operate in Batches", "setUp": "Set Up", "details": "Details", "allocationProcess": "Allocation Process", "softEmergencyStopBut": {"openSoftEmergencyStop": "Pause", "closeSoftEmergencyStop": "Resume"}, "enableCharging": "Enable Charging", "enableParking": "Enable Parking", "chargingMarker": "Charging <PERSON>", "parkingMarker": "Parking <PERSON><PERSON>", "chargingList": {"2": "<PERSON><PERSON><PERSON>", "1": "Enable", "0": "Disable"}, "relocation": "Relocation", "powerOff": "Power Off", "importRobotGrouping": "Import Robot Group", "statistics": "Statistics", "offSite": "Off-Site", "historicalTask": "Historical Tasks", "setType": "Set Type", "setGroup": "Set Group", "pleaseSelectARobotGroup": "Please select a robot group", "pleaseSelectARobotType": "Please select a robot type", "importedRobotType": "Import Robot Type", "allowedRotation": "Allow Rotation", "canRotateList": {"true": "Yes", "false": "No"}, "currentLocationMap": "Current Location Map", "switchMap": "Change Map", "carryTask": "Execute Task", "turnCharge": "Enable Charging", "openParking": "Enable Parking", "autoChargeState": {"0": "Disable Auto Charging", "1": "Enable Auto Charging"}, "autoParkState": {"0": "Disable Auto Parking", "1": "Enable Auto Parking"}, "automaticRelocation": "Auto Relocation", "manualRelocation": "Manual Relocation", "resetting": "Reset", "ordinaryCharging": "Ordinary Charging", "buttReset": "Reset Docking", "odom": "Mileage"}, "actionSetting": {"code": "Code", "name": "Name", "type": "Type", "enterParamCount": "Input-Parameter Count", "outParamCount": "Output-Parameter Count", "add": "Add", "export": "Export", "import": "Import", "preview": "Preview", "notice": "Notice", "icon": "Icon", "parameter": "Parameter", "addInputParameters": "Add Input-Parameter", "addOutputParameters": "Add Output-Parameter", "parameterType": "Parameter Type", "parameterCode": "Parameter Code", "parameterName": "Parameter Name", "category": "Category", "iconUploadComplete": "Icon Upload Complete", "isCommonList": {"true": "Yes", "false": "No"}, "isAllowSkipList": {"true": "Yes", "false": "No"}, "baseTypeList": {"Text": "Text", "Number": "Number", "Common": "Common"}, "componentOptions": {"Default": "<PERSON><PERSON><PERSON>", "Json": "JSON", "Bool": "Bool", "RadioList": "Single Choice List", "MultiList": "Multi Choice List", "VehicleMapCode": "Single Choice Map", "VehicleMapCodeList": "Multi Choice Maps", "MarkerCode": "Single Choice Marker", "MarkerCodeList": "Multi Choice Markers", "VehicleCode": "Single Choice Robot", "VehicleCodeList": "Multi Choice Robots", "VehicleTypeCode": "Single Choice Robot Type", "VehicleGroupCode": "Single Choice Robot Group", "WarehouseLocationType": "Single Choice Storage Location Type", "WarehouseLocation": "Single Choice Storage Location", "WarehouseArea": "Single Choice Storage Zone", "VehicleGroupCodeList": "Multi Choice Robot Groups", "VehicleTypeCodeList": "Multi Choice Robot Types", "TaskType": "Single Choice Task type", "customParameter": "User-Defined Parameter", "MultiText": "Long Text"}, "nodeSettingsImport": "Import Node Settings", "fileFormatJson": "Drag and Drop File", "inputBox": "Input Box", "skipAllow": "<PERSON><PERSON>", "retryNum": "Retry Times", "allowRetry": "<PERSON><PERSON>ow"}, "taskManage": {"code": "Code", "name": "Name", "status": "Status", "priority": "Priority", "robot": "Robot", "callbackUrl": "Upstream URL", "source": "Source", "createDate": "Creation Date", "startTime": "Start Time", "endTime": "End Time", "log": "Log", "taskExecution": "Task Execution", "currentNode": "Current Node", "executionTime": "Execution Time", "runningLog": "Running Log", "pleaseEnterADescriptionSearch": "Please enter a description to search", "statistics": "Statistics", "runningTime": "Running Time", "runTimes": "Run Times", "totalHours": "Total Hours", "inputValue": "Input Value", "outputValue": "Output Value", "taskInformation": "Task Information", "nodeInformation": "Node Information", "jsonFormatError": "JSON Format Error", "cancelATask": "Cancel Task", "whetherToCancelATask": "Cancel the task or not", "downloadRecord": "Download Record", "optionsStatus": {"Create": "Pending", "Running": "Running", "Finished": "Finished", "Cancel": "Cancelled"}, "optionsSource": {"Api": "API", "Manual": "Manual Dispatch", "Charge": "Charging Strategy", "Park": "Parking Strategy", "Traffic": "Traffic Management Strategy", "Task": "Other Tasks", "Pda": "PDA"}, "batchCancellation": "Cancell in Batches", "uploadRecord": "Upload Record", "importTaskManagement": "Import Task Management", "uploadLog": "Upload Log", "theCompletedOrCanceledTaskIsSelected": "A completed or canceled task is selected", "successfullyCancelledTask": "Successfully Cancelled Task", "failedToCancelTheTaskBecause": "Failed to cancel the task due to", "failedToCancelTask": "Failed to Cancel Task", "successfulOperation": "Operation Successful", "operationFailure": "Operation Failed", "noPublishedTaskProcessExists": "No published task process exists", "skip": "<PERSON><PERSON>", "remainingDistance": "Remaining Distance", "totalDistance": "Total Distance", "retry": "Retry", "retryTip": "Retry or not", "skipTip": "Are you sure to skip this node and directly execute subsequent nodes?", "isBreak": "Interruptible", "isBreakObject": {"0": "Yes", "1": "No"}}, "taskType": {"code": "Code", "name": "Name", "priority": "Priority", "selfCheckStatus": "Self-Check Status", "predictExecTime": "Predicted Execution Time", "layout": "Layout", "implement": "Implement", "clone": "<PERSON><PERSON>", "eventType": "Event Type", "eventTypeList": {"Interface": "<PERSON><PERSON><PERSON>", "FixedTime": "Clock Event", "Button": "Button Event", "Plc": "Register Event", "VehiclePlc": "Robot Register Event", "VehicleAbnormal": "Robot Error Event", "TaskCancel": "Task Cancel Event", "TaskFinished": "Task Finished Event"}, "optionsTemplateType": {"Common": "Common Type", "Event": "Event Type"}, "optionsPublishStatus": {"Published": "Enabled", "Unpublished": "Disable"}, "typeList": {"Common": "Create Common Type", "Event": "Create Event Type"}, "jobFlowType": "Workflow Type", "releaseStatus": "Enabled Status", "importTaskType": "Import Task Type"}, "taskTypeArrangement": {"Business": "General Control", "Common": "Common", "Communication": "Communication Component", "Process": "Process Control", "AllocationResource": "Resource Allocation", "ObtainResource": "Resource Acquisition", "Other": "Other", "Vehicle": "Robot Control", "commonNode": "Common Node", "import": "Input", "export": "Output", "otherCondition": "Other Condition", "parallelBranch": "Parallel Branch", "cycleCondition": "Cycle Condition", "endLoop": "End Loop", "start": "Start", "end": "End", "settingsCommonlyUsed": "Set Commonly Used", "parallel": "<PERSON><PERSON><PERSON>", "parallelNode": "<PERSON>lle<PERSON>", "condition": "Condition", "conditionalNode": "Conditional Node", "circulation": "Loop", "loopNode": "Loop Node", "addCondition": "Add Condition", "settingCommonNodes": "Set Common Nodes", "addParallel": "Add <PERSON>", "displayName": "Display Name", "thePropertiesAreNotSavedPressEnterToConfirm": "Properties are not saved, press 'Enter' to confirm", "cycleTime": "Cycle Time", "cycleNumber": "Number of Cycles", "constantValue": "Constant Value", "setOfConditions": "Condition Group", "and": "And", "or": "Or", "addConditionGroup": "Add Condition Group", "lt": "Less Than", "ne": "Not Equal to", "eq": "Equal to", "gt": "Greater Than", "ge": "Greater Than or Equal to", "le": "Less Than or Equal to", "belong": "Belong", "contain": "Contain", "eventType": "Event Type", "ip": "IP Address", "portNumber": "Port Number", "functionCode": "Function Code", "registerAddress": "Register Address", "registerValue": "Register Value", "section": "Section", "effectiveScopeRobot": "Robot", "effectiveScopeTask": "Task", "taskAttribute": "Task Attribute", "conditionalAttribute": "Conditional Attribute", "nodeAttribute": "Node Attribute", "taskProperty": "Task Properties", "conditionalProperty": "Conditional Properties", "nodeProperty": "Node Properties", "taskVariableInput": "Task Variable (Input)", "variableName": "Variable Name", "taskVariableOutput": "Task Variable (Output)", "owningNode": "Belonging Node", "priorityState": {"5": "Highest", "4": "High", "3": "Medium", "2": "Low", "1": "Lowest"}, "interfaceInputFormType": {"Json": "JSON", "MarkerCode": "<PERSON><PERSON>", "VehicleCode": "Robot", "Default": "<PERSON><PERSON><PERSON>", "MultiText": "Long Text"}, "variableTypeList": {"Default": "<PERSON><PERSON><PERSON>", "Bool": "Bool", "RadioList": "Single Choice List", "MultiList": "Multi Choice List", "VehicleMapCode": "Single Choice Map", "VehicleMapCodeList": "Multi Choice Maps", "MarkerCode": "Single Choice Marker", "MarkerCodeList": "Multi Choice Markers", "VehicleCode": "Single Choice Robot", "VehicleCodeList": "Multi Choice Robots", "VehicleTypeCode": "Single Choice Robot Type", "VehicleTypeCodeList": "Multi Choice Robot Types", "VehicleGroupCode": "Single Choice Robot Group", "VehicleGroupCodeList": "Multi Choice Robot Groups", "WarehouseArea": "Single Choice Storage Location Zone", "WarehouseLocation": "Single Choice Storage Location Location", "WarehouseLocationType": "Single Choice Storage Location Location Type", "Json": "JSON", "Object": "Object"}, "variableCategoryList": {"Text": "Text", "Number": "Number", "Common": "Common"}, "effectiveScopeTaskState": {"1": "All", "2": "Partial"}, "variableNameDuplication": "Variable Name Duplicated", "settlementOfCondition": "Condition Setting", "unpublish": "Disable", "haveReleased": "Enable", "publishingFailedEmptyLoopExists": "Failed to enable task, there is an empty loop in a node", "publishingFailedTaskMustContainOtherNodes": "Failed to enable the task. The Cancel Task must contain other nodes", "isParameterMandatoryTip": "Failed to enable task, there are nodes with unset parameters", "eventTypeList": {"FixedTime": "Clock Event", "Button": "Button Event", "Plc": "Register Event", "VehiclePlc": "Robot Register Event", "VehicleAbnormal": "Robot Error Event", "TaskCancel": "Task Cancel Event", "TaskFinished": "Task Finished Event", "Interface": "<PERSON><PERSON><PERSON>"}, "effectiveDate": "Effective Date", "pleaseEnterTheEffectiveStartDate": "Enter the effective start date", "pleaseEnterTheEffectiveEndDate": "Enter the effective end date", "effectiveTime": "Effective Time", "pleaseEnterTheEffectiveStartTime": "Enter the effective start time", "pleaseEnterTheEffectiveEndTime": "Enter the effective end time", "interval": "Interval", "enableOrNot": "Enable or Not", "callBoxNumber": "Call Box Number", "buttonNumber": "Button Number", "robotNumber": "Robot Number", "robotLocationMap": "Robot Located Map", "exceptionCoding": "Error Code", "anomalyLevel": "Error Level", "exceptionDetails": "<PERSON><PERSON><PERSON>", "robotAssembly": "Robot Set", "taskPublishingSucceeded": "Task Enable Successfully", "searchNode": "Search Node", "quickAccess": "Quick Access", "quickAccessTip": "Enable to create tasks via Robot Cards in the Monitoring Center", "inputParameter": "Input Parameter", "outputParameter": "Output Parameter", "missionNumber": "Task Number", "customOutput": "User-define Output", "customInput": "User-define Input", "pda": "PDA", "pdaTip": "Enable to create this task in PDA"}, "notice": {"levelList": {"1": "Normal", "2": "Warning", "3": "Error"}, "statusList": {"0": "Active", "1": "Ignore", "2": "Close"}, "ignore": "Ignore", "level": "Level", "source": "Source", "quest": "Task", "equipment": "Equipment", "closingTime": "Close Time", "intervalTime": "Interval Time", "importTheNotificationProfile": "Import Notice Config", "isUpload": "Whether to Report", "isUploadState": {"0": "No", "1": "Yes"}}, "exception": {"ignore": "Ignore", "cancelIgnore": "Cancel <PERSON>", "exceptionLevel": "Error Level", "sourceSystem": "Source System", "exceptionType": "Error Type", "solution": "Solution", "exceptionStatus": "Error Status", "ignoreStatus": "Ignore Status", "robot": "Robot", "taskId": "Task ID", "deviceId": "Device ID", "mapName": "Map Name", "info": "Info", "warning": "Warning", "error": "Error", "unread": "Unread", "readAll": "<PERSON> as <PERSON>", "read": "Read", "closeTime": "Close Time", "exceptionStatusList": {"0": "Open", "1": "Closed"}, "ignoreStatusList": {"0": "Not Ignored", "1": "Ignored"}, "exceptionMessage": "Error Message", "exceptionMessageDetails": "Error Message Details", "source": "Source"}, "chargeConfig": {"dialogInputList": {"lowBattery": "Low Battery Setting", "highBattery": "High Battery Setting", "minBatteryValue": "Minimum Charge Battery Level", "minChargeTime": "Minimum Charge Time", "bindChargeMarkers": "Bind Charging Markers", "chargeTaskTypeId": "Charge Task Type"}, "lowBattery": "Low Battery (%)", "highBattery": "High Battery (%)", "minBatteryValue": "Minimum Charge Battery Level (%)", "minChargeTime": "Minimum Charge Time (min)", "createTask": "Create Task", "title": "Charging Strategy", "describe": "Strategy to create charging tasks when robot is low on battery and idle"}, "parkConfig": {"dialogInputList": {"bindParkMarkers": "Bind Parking Points", "parkTaskTypeId": "Set Task"}, "createTask": "Create Task", "title": "Parking Strategy", "describe": "Strategy to create parking tasks when robot is idle"}, "trafficConfig": {"title": "Traffic Config", "describe": "Robot detour, path request, and conflict handling", "faultOptions": {"1": "Wait", "2": "Detour"}, "banOptions": {"1": "Wait", "2": "Detour", "3": "Drive-away"}, "collisionOptions": {"1": "Navigation Marker", "2": "Charging <PERSON>", "3": "Work Marker"}, "highPerformanceMode": "In high performance mode, functions such as zone and robot rotation will be affected"}, "errorStatistical": {"vehicleAbnormalPieChart": "Robot Error <PERSON>", "abnormalDetailPieChart": "Error Type Ratio", "avgHandleDurationPieChart": "Average Error Handling Time", "newCountLineChart": "New Error Count", "avgHandleDurationLineChart": "Average Error Handling Time"}, "taskStatistical": {"taskStatusPieChart": "Task Completion Ratio", "createTaskCountPieChart": "New Task Count", "avgAllocationDurationPieChart": "Average Allocation Duration", "avgExecuteDurationPieChart": "Average Execution Duration", "createTaskCountLineChart": "New Task Count", "endTaskCountLineChart": "End Task Count", "avgAllocationDurationLineChart": "Average Allocation Duration", "avgExecuteDurationDurationLineChart": "Average Execution Duration"}, "screen": {"agvNumStatistical": "Robot Quantity Statistics", "taskNumStatistical": "Task Quantity Statistics", "agvTotal": "Total Robot Quantity", "totalSize": "Total Task Quantity", "runningSize": "Running Task Quantity", "successSize": "Successful Task Quantity", "cancelSize": "Cancelled Task Quantity", "waitSize": "Pending Task Quantity", "completionRate": "Task Completion Ratio", "title": "Scheduling System Visual Dashboard", "visualLargeScreen": "Visual Dashboard"}, "serverMonitoring": {"serverParameter": "Server Specifications", "serverUsage": "Server Resource Usage", "cpuLineChart": "CPU Usage Ratio", "memLineChart": "Memory Usage Ratio", "diskLineChart": "Disk Capacity Changes", "mysqlLineChart": "MySQL Operation Count", "cpuCores": "CPU Cores", "cpuCoresUnit": "Unit", "totalThreads": "Total Threads", "thread": "<PERSON><PERSON><PERSON>", "memory": "Memory", "diskCapacity": "Disk Capacity", "diskUsage": "Disk Usage Ratio"}, "statisticsRobots": {"statusLineChart": "Robot Status Trend", "statusPieChart": "Robot Status Ratio", "utilizeRateLineChart": "Robot Utilization Rate", "text": "Robot Statistics"}, "taskTypeStatistic": {"taskStatusPieChart": "Task Completion Distribution", "taskCountLineChart": "Task Quantity", "avgAllocationDurationLineChart": "Average Allocation Duration", "avgExecuteDurationLineChart": "Average Execution Duration", "text": "Task Workflow Statistics"}, "robotManagementStatistic": {"statusPieChart": "Robot Status Ratio", "workStatusPieChart": "Robot Work Ratio", "statusLineChart": "Robot Status Trend", "utilizeRateLineChart": "Robot Utilization Rate"}, "robotMonitorStatistic": {"taskStatusPieChart": "Task Status", "vehicleStatusPieChart": "Robot Status", "vehicleBatteryPieChart": "Robot Battery"}, "licence": {"licenseRemaining": "License Validity", "licenseNotInForce": "License Invalid", "licenseHasExpired": "License Overdue", "lackOfLicense": "Lack of License", "renewalOfLicense": "Update License", "deleteLicense": "Delete License", "renewalAuthorization": "Update Authority"}, "logins": {"userLogin": "User Login", "rememberThePassword": "Remember the Password", "copyrightShenzhenYouaiZhiheCoLtd": "Copyright @ Shenzhen Youibot Robotics Co.,Ltd."}, "log": {"causeOfFailure": "Failure Cause", "responseTime": "Response Time", "url": "URL", "requestInformation": "Request Information", "returnInformation": "Return Information", "successList": {"true": "Success", "false": "Fail"}, "user": "User", "ipAddressOfTheClient": "Client IP Address", "operatingTime": "Operating Time", "typeList": {"Error": "ERROR", "Running": "INFO", "Warning": "WARNING"}, "category": "Category", "data": "Data", "lastTime": "Last Updated Time", "downloadDetailsLog": "Download Detailed Log", "filename": "File Name", "message": "Message"}, "systemConfiguration": {"licenseAllocation": "License Config", "licenseAllocationDescribe": "License Information", "storageConfiguration": "Storage Config", "storageConfigurationDescribe": "Log Data Cleanup, File Data Cleanup, Business Data Cleanup", "pushInterfaceConfiguration": "Push Interface Config", "robotState": "Robot State"}, "storageLocation": {"reservoirArea": "Storage Zone", "row": "Row", "column": "Column", "layer": "Layer", "operatingHeight": "Operating Height", "jobPoint": "Operating Point", "occupiedState": "State", "containerBarCode": "Container Barcode", "storyHeight": "Layer Height", "occupyStatus": {"Lock": "Lock", "Free": "Free", "Store": "Store"}, "setPoint": "Set Point", "importDatabaseLocationArea": "Import Storage Zone", "importLibraryType": "Import Storage Location Type", "importLocation": "Import Storage Location", "usageStatus": {"Disable": "Disable", "Enable": "Enable"}, "enabledState": "Enabled State"}, "today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last month", "last7Days": "Last 7 days", "last30Days": "Last 30 days", "fullTimeOut": "All robots in the field paused and no longer accept new tasks", "onPause": "All robots in the field are pausing. Number of remaining robots:", "fullRecoveryUnderway": "All robots in the field are restoring", "completeTimeout": "All Pause Complete", "fullRecoverySuccessful": "All Restore Complete", "OperationIsTooFrequent": "The operation is too frequent", "save": "Save", "extendedAttribute1": "Extended Attribute 1", "extendedAttribute2": "Extended Attribute 2", "extendedAttribute3": "Extended Attribute 3", "extendedAttribute4": "Extended Attribute 4", "extendedAttribute5": "Extended Attribute 5", "extendedAttribute6": "Extended Attribute 6", "extendedAttribute7": "Extended Attribute 7", "extendedAttribute8": "Extended Attribute 8", "extendedAttribute9": "Extended Attribute 9", "extendedAttribute10": "Extended Attribute 10", "extendedProperty1": "Extended Property 1", "extendedProperty2": "Extended Property 2", "extendedProperty3": "Extended Property 3", "extendedProperty4": "Extended Property 4", "extendedProperty5": "Extended Property 5", "containerEncoding": "Container Code", "currentReservoirArea": "Current Storage Zone", "locationType": "Storage Location Type", "warehouse": "Storage Location", "languageConfiguration": "Language Config", "languageConfigurationDescribe": "Add/Download Language Package", "storageUpdateTime": "Storage Update Time", "numberTriggers": "Number of Triggers", "allowRepetition": "Allow Repetition", "exceptionMessage": "Error Information", "incomingParameter": "Input Parameter", "isAllowRepeatState": {"0": "Not Allow", "1": "Allow"}, "importEvent": "Import Event", "eventCoding": "Event Code", "beChecking": "Verifying...", "delLanguageTip": "Ensure to delete this language package?", "endCancelRange": "End Cancel Range", "checkValue": "Verify Value", "changingName": "<PERSON><PERSON>", "taskArrangementNew": {"putAway": "Fold", "unfold": "Unfold", "start": "Start", "end": "End", "while": "While", "cancelTask": "Cancel Task", "endWhile": "End While", "endCancelTask": "End Cancel Task", "judge": "Condition", "when": "When", "otherJudge": "Other Condition", "cancelWhileTip1": "Cancel tasks can not be nested inside cancel tasks", "cancelWhileTip2": "Currently only 2 layers of nested loops are supported"}, "globalPauseExecutingArmScriptIsStop": {"Immediately": "Stop Immediately", "Later": "Stop After Done"}, "chargingPile": {"serialNumber": "Serial number", "deviceType": "Device type", "equipmentType": "Equipment type", "networkState": "Network state", "runningState": "Running state", "controlMode": "Control mode", "dischargeStatus": "Discharge state", "resetStatus": "Reset state", "occupancyRobot": "Occupancy robot", "chargeTypes": "Charging type", "setVoltage": "Charge calibration voltage", "voltage": "Current voltage", "setCurrent": "Charge calibration current", "current": "current", "setPower": "Charge rated power", "power": "Real-time power", "dcTemp": "DC module temperature", "brushTemp": "Brush head temperature", "airTemp": "Air temperature", "durationTimes": "Entry Time", "voltageRange": "Support voltage range", "softwareVer": "Firmware version", "terminationDischarge": "å¼ºå¶åæ­¢(å¾ç¿»è¯)", "networkStatusState": {"online": "online", "offline": "offline"}, "workStatusState": {"normal": "normal", "abnormal": "abnormal"}, "dischargeStatusList": {"discharging": "In discharge", "no_discharge": "undischarge"}, "resetStatusList": {"pending": "To be reset", "completed": "Reset"}, "controlModeList": {"auto": "Auto", "manual": "Manual"}, "whetherToPerformReset": "Are you sure to perform a reset?", "WhetherPerformTerminationDischarge": "ç¡®è®¤æ§è¡å¼ºå¶åæ­¢ï¼(å¾ç¿»è¯)", "resetBatchPrompt": "{successLength} charging piles operated successfully, {errorLength} charging piles failed to operate", "chargingPoint": "åçµç¹(å¾ç¿»è¯)"}, "secondaryConfirmationPrompt": "Are you sure you want to perform this action?", "followingSystem": "Follow the system", "customization": "Customize", "exceptionNotifyTimeTip": "It takes at least 5 minutes for the event to trigger a higher-level notification!", "alwaysConnect": "å§ç»å¯¹æ¥(å¾ç¿»è¯)", "doNotConnectUntilTheFinishLine": "éç»ç¹ä¸å¯¹æ¥(å¾ç¿»è¯)", "abnormalAlarm": "å¼å¸¸æ¥è­¦(å¾ç¿»è¯)", "menuList": {"menu": {"monitoringCenter": "Monitoring Center", "PDA": "PDA", "agv": "Robot", "task": "Task", "equipment": "Equipment", "operations": "Operation Config", "taskManager": "Task Management", "taskList": "Task List", "taskType": "Task Flow", "eventList": "Event List", "robots": "Robot Management", "robotList": "Robot List", "robotType": "Robot Type", "robotGroup": "Robot Group", "mapList": "Map Management", "storageLocation": "Storage Location Management", "storageLocationList": "Storage Location List", "storageLocationType": "Storage Location Type", "storageLocationArea": "Storage Zone List", "notificationManager": "Notice Management", "systemLog": "System Log", "operationLog": "Operation Log", "interfaceLog": "Interface Log", "runningLog": "Running Log", "setting": "System Setting", "schedulingConfiguration": "Scheduling Config", "systemSettings": "System Config", "userSettings": "Account Authority", "notificationSettings": "Notice Template", "nodeSettings": "Node Setting", "statistical": "Statistical Statement", "statisticsRobots": "Robot Statistics", "taskStatistical": "Task Statistics", "errorStatistical": "Exception Statistics", "serverMonitoring": "Server Monitoring", "facility": "Equipment management", "chargingPile": "Charging pile management"}, "button": {"view": "View", "relocation": "Relocation", "switchMap": "Change Map", "controlMode": "Control Mode", "dispatchingMode": "Scheduling Mode", "ordinaryCharging": "Ordinary Charging", "autocharge": "Auto Charging", "automaticParking": "Auto Parking", "pause_resume": "Pause/Resume", "reset": "Reset", "buttReset": "Docking Reset", "restart": "<PERSON><PERSON>", "shutdown": "Power Off", "departure": "Off-Site", "newTask": "Add Task", "cancelTask": "Cancel Task", "taskDetails": "Task Details", "execution": "Execution Task", "elevato": "Elevator", "autoDoor": "Auto Door", "airShower": "Air Shower Door", "bulkExport": "Export in Batches", "batchImport": "Import in Batches", "cancle": "Cancel", "batchCancellation": "Cancel in batches", "uploadRecord": "Upload Log", "details": "Details", "downloadRecord": "Download Log", "remark": "Remark", "add": "Add", "del": "Delete", "edit": "Edit", "implement": "Implement", "layout": "Layout", "clone": "Copy", "statistic": "statistics", "enabledState": "Enable/Disable", "assignMap": "Assigned Map", "SoftEmergencyStop": "Enable/Disable Pause", "schedule": "Auto/Manual Schedule", "controlModeState": "Manual/Auto control", "historicalTask": "Historical Tasks", "setType": "Type Set", "setGroup": "Group Set", "export": "Export", "import": "Import", "ignore": "Ignore", "activation": "Activate", "downloadDetailedLog": "Download Detail Logs", "download": "Download", "viewRoles": "Role View", "addRoles": "Add Role", "rename": "<PERSON><PERSON>", "delRoles": "Delete Role", "viewAccount": "Account View", "addAccount": "Add Account", "editAccount": "Edit Account", "delAccount": "Delete Account", "resetPasswords": "Reset Password", "containerExit": "Container Exit", "containerEntry": "Container Entry", "taskView": "Task View", "taskCancel": "Cancel Task", "changingName": "<PERSON><PERSON>", "terminationDischarge": "å¼ºå¶åæ­¢(å¾ç¿»è¯)", "stopAlarm": "åæ­¢æ¥è­¦(å¾ç¿»è¯)"}}}, "yi": {"switchLanguage": "Change Language", "map": "Map", "size": "Size", "deleteModel": "Delete Elements", "test": "Test", "options": "Operation", "addMarker": "<PERSON><PERSON>", "addArea": "Add Area", "canvasReset": "<PERSON><PERSON>", "canvasRotate": "Rotate <PERSON>", "canvasFullScreen": "Full Screen Canvas", "displaySetting": "Display Settings", "addOneWayPath": "Add Unidirectional Path", "addTwoWayPath": "Add Bidirectional Path", "straightenCarve": "Straighten Path", "canvasSmallScreen": "<PERSON><PERSON>", "addPath": "Add Path", "systemSetup": "User-define Setting", "endRecordingPoint": "End Recording Marker", "recordingPoint": "Record Marker", "mapEditor": "Map Edit", "batchNew": "Add in Batches", "rangingPath": "Measure Distance", "selectedPath": "Select Path", "up": "Up", "down": "Down", "aleft": "Left", "right": "Right", "hideSelectedPath": "Hidden Path", "smoothPath": "Smooth Path"}, "pdaLang": {"login": "<PERSON><PERSON>", "account": "Account", "password": "Password", "changingServerIPAddress": "Changing Server IP Address", "serverIPAddressTip": "The server address must be changed with caution!", "serverAddress": "Server Address", "version": "Version Infomation", "messageExceptionAlarm": "Message Exception Alarm", "logout": "Logout", "logoutOrNot": "Logout or not?", "pleaseEnterAGVNumber": "Please enter the AMR number", "refreshSuccessful": "Success", "message": "Message", "ignore": "Ignore", "code": "Code", "describe": "Description", "robot": "Robot", "taskID": "Task ID", "operate": "Operate", "operateSuccessfully": "Success", "task": "Task", "Abnormal": "Abnormal", "Normal": "Normal", "cancel": "Cancel", "taskdetail": "Task Details", "taskEntry": "Task Entry", "state": "Status", "name": "Name", "createDate": "Creation Time", "taskDelivery": "Task Delivery", "storageLocation": "Storage Location", "storageLocationTip": "Please enter or scan the storage location", "materialType": "Material Type", "materialTypeTip": "Please select the material type", "containerCode": "Container Barcode", "containerCodeTip": "Please enter or scan the container barcode", "OK": "OK", "containerEntry": "Container Entry", "containerExit": "Container Exit", "scnContainerRepositoryLocation": "Scan the container or storage location", "userInformationHasExpired": "User information has expired", "networkPrompt": "The connection failed. Please check whether the connection service is correct", "networkPrompt2": "Unable to connect to back-end service", "Disconnect": "Unconnected", "Connect": "Connected", "map": "Map", "type": "Type", "scheduleMode": "Schedule", "controlStatus": "Control", "orientation": "Location", "softStopSwitch": "Running", "currentNode": "Current Node", "executionTime": "Execution Time", "Offline": "Offline", "Work": "Busy", "Free": "Idle", "ManualSchedule": "Manual Schedule", "AutoSchedule": "Auto Schedule", "Manual": "Manual Control", "Auto": "Auto Control", "NotLocated": "Unlocated", "Located": "Located", "Close": "Running", "Open": "Pausing", "mine": "Mine", "updating": "Updating....", "updatesucceededRestarting": "Update Successfully. Restartingâ¦", "updateFailure": "Update Failed", "pleaseEnterOrScan": "Please enter or scan"}}