spring:
  resources:
    static-locations:
      - file:D:\public\fleet\5.0.10\
  datasource:
    druid:
      #MySQL
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***********************************************************************************************************************************************
      username: root
      password: youibot2017
#      #Oracle
#      driver-class-name: oracle.jdbc.OracleDriver
#      url: ***********************************
#      username: root
#      password: root
#      #SQLServer
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      url: ****************************************************************
#      username: sa
#      password: 123456
#      #postgresql
#      driver-class-name: org.postgresql.Driver
#      url: ****************************************************
#      username: postgres
#      password: 123456
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

LICENSE:
  LICENSE_TYPE: YOUIFleet
  SUBJECT: license
  STOREPASS: eXBsMTIz
  PUBLICKEYSSTOREPATH: /secure/publicCerts.store
  PUBLICALIAS: publicCert

# 地图相关配置
VEHICLE_MAP:
  #地图文件存储路径
  FILE_PATH_LOCATING_DRAFT: /home/<USER>/youibot_map/draft/
  FILE_PATH_LOCATING_CURRENT: /home/<USER>/youibot_map/current/
  FILE_PATH_LOCATING_PUBLISH: /home/<USER>/youibot_map/publish/
  # webSocket 消息推送间隔 （单位： 毫秒） 默认：100ms
  WS_MSG_SEND_INTERVAL: 100
  # 地图编辑撤销回退最大步数
  EDIT_UNDO_POOL_SIZE: 30

PATH_PLAN:
  #是否打印修改路径权重的日志
  PRINT_LOG_OF_MODIFY_PATH_WEIGHT: false
  #修改日志文件路径
  LOG_FILE_OF_MODIFY_PATH_WEIGHT: /server/ads/logs/modify_sidePath_weight.txt
  #是否打印冲突处理的日志
  PRINT_LOG_OF_CONFLICT_PROCESS: false
  #修改冲突处理日志文件路径
  LOG_FILE_OF_CONFLICT_PROCESS: /server/ads/logs/conflict_process.txt
  #是否打印冲突处理的日志
  PRINT_LOG_OF_SEND_SIDE_PATH: false
  #修改冲突处理日志文件路径
  LOG_FILE_OF_SEND_SIDE_PATH: /server/ads/logs/send_side_path.txt
  #纯路径跟随AGV的安全检测距离(单位m)
  AGV_SAFE_DISTANCE_PURE_PURSUIT: 0.5
  #AGV运行中的路径阈值，以时间代价表示(单位s)
  #AGV_SAFE_TIME_PURE_PURSUIT: 3.0
  #路径规划AGV的定位增加的权重(临时)
  LOCATION_PLUS_AUTO_WEIGHT: 0.0
  #路径规划已规划路径增加权重值(正向)
  PLANNED_PLUS_AUTO_WEIGHT_OBVERSE: 0.0
  #路径规划已规划路径增加权重值(反向)
  PLANNED_PLUS_AUTO_WEIGHT_REVERSE: 20.0
  #路径规划运行中的路径增加权重值(正向)
  RUNNING_PLUS_AUTO_WEIGHT_OBVERSE: 0.0
  #路径规划运行中的路径增加权重值(反向)
  RUNNING_PLUS_AUTO_WEIGHT_REVERSE: 12.0
  #路径规划AGV占用的单机区域加权重值
  SINGLE_AREA_PLUS_AUTO_WEIGHT: 0.0
  #机器人报错添加的权重
  AGV_ABNORMAL_STATUS_USER_WEIGHT: 2000.0
  #发送的最短路径长度
  SEND_SHORTEST_SIDE_PATH: 0.01
  #机器人处于避让点路径规划时, 检测前N段路径上是否存在机器人
  CHECK_FRONT_PATH_BY_AVOID_MARKER_PLAN: 3
  #开启路径导航阻挡绕路功能（被静止的机器人阻挡）
  #OPEN_OBSTRUCT_DETOUR: false
  OPEN_OBSTRUCT_DETOUR: false
  #机器人处于避让点路径规划时,在10s内有机器人能到达的点不计算
  AGV_COMING_TIME: 10.0
  #机器人处于避让点路径规划时,过滤以下类型的点不作为避让点，英文逗号隔开
  NO_USED_AVOID_MARKER_TYPE: WORK_MARKER
  #机器人处于避让点路径规划时,计算获取可到达点的次数
  AGV_AVOID_REACHABLE_MARKER_CALCULATE_TIMES: 5
  #机器人异常路径附加权重
  ABNORMAL_VEHICLE_ADDITIONAL_PATH_COST: 1000.0
  #封控区域路径附加权重
  CONTROL_AREA_ADDITIONAL_PATH_COST: 2000.0
  #障碍物路径附加权重
  OBSTACLE_ADDITIONAL_PATH_COST: 1000.0
  #反向路径附加权重Fleet
  REVERSE_PATH_ADDITIONAL_PATH_COST: 10.0
  #自动门路径避让附加权重
  AUTO_DOOR_ADDITIONAL_PATH_COST: 10.0
  #根据传上来的坐标计算定位范围,单位m
  LOCATION_CALCULATE_WIDTH: 5.0
  #禁旋角度
  NO_ROTATING_ANGLE: 10

#调度评分策略系数配置
SCHEDULER_CONFIG:
  #空闲充电点评分
  FREE_CHARGE_SCOPE: 500.0
  #距离评分系数
  DISTANCE_RATIO: -1.0
  #电量评分系数
  BATTERY_VALUE_RATIO: 1.0
  #触发充电任务需要的空闲时长，单位s
  FREE_TIME_TO_CHARGE: 10
  #触发充电任务需要的空闲时长，单位s
  FREE_TIME_TO_PARK: 10

#统计
STATISTICS:
  #机器人
  VEHICLE:
    #统计cron表达式
    CRON: 0 0,15,30,45 * * * ?
  #任务
  TASK:
    #统计cron表达式
    CRON: 0 0,15,30,45 * * * ?
  #异常
  ABNORMAL:
    #统计cron表达式
    CRON: 0 0,15,30,45 * * * ?
  #服务器
  SERVER:
    #统计cron表达式
    CRON: 0 0,15,30,45 * * * ?

AGV:
    #机器人移动速度
    MOVE_SPEED: 0.5

PDA:
  # 库位的物料类型文件位置
  MATERIAL_TYPE_CONFIG_FILE_PATH: /server/ads/material_type.txt

#系统配置
SYSTEM:
  #时区
  TIMEZONE: GMT+5.5

#多语言
language:
  #存储路径
  path: ./messages/
