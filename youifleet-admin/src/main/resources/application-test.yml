spring:
  datasource:
    druid:
      #MySQL
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************************************************************
      username: root
      password: youibot2017
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

LICENSE:
  LICENSE_TYPE: YOUIFleet
  SUBJECT: license
  STOREPASS: eXBsMTIz
  PUBLICKEYSSTOREPATH: /secure/publicCerts.store
  PUBLICALIAS: publicCert

#ftp服务器配置
FTP:
  URL: 172.17.0.1
  PORT: 21
  USERNAME: ftp
  PASSWORD: youibot

# 地图文件路径
MAP_FILE_PATH:
  MAP_INFO_FILE: /home/<USER>/youibot_map/
  PATH_FILE: /path/current/
  LOCATION_FILE: /locating/current/
  DRAFT_FILE: /path/draft/
  PATH_TMP_FILE: /path/tmp
  LOCATION_TMP_FILE: /locating/tmp
  ELEVATOR_PATH: /home/<USER>/youibot_map/elevator.json

# 地图更新配置
MAP_UPDATE:
  # 本地 current 更新到 内存  时间间隔（单位： 秒）：5s
  CURRENT_2_MEMORY_INTERVAL: 5
  # 远程ftp tmp 更新到 current 时间间隔（单位： 秒）：5s
  TMP_2_CURRENT_INTERVAL: 5
  # 队列循环 时间间隔（单位： 秒）：1s
  QUEUE_LOOP_INTERVAL: 1
  # 删除10天前的备份数据（单位： 天）：10
  DELETE_BACK_ZIP_FILE: 10
  # 队列循环 时间间隔（单位： 秒）：1s
  DATA_SYNC_INTERVAL: 1
  # webSocket 消息发送间隔 （单位： 毫秒） 默认：100ms
  WS_MSG_SEND_INTERVAL: 100


PATH_PLAN:
  #是否打印修改路径权重的日志
  PRINT_LOG_OF_MODIFY_PATH_WEIGHT: false
  #修改日志文件路径
  LOG_FILE_OF_MODIFY_PATH_WEIGHT: /server/ads/logs/modify_sidePath_weight.txt
  #是否打印冲突处理的日志
  PRINT_LOG_OF_CONFLICT_PROCESS: false
  #修改冲突处理日志文件路径
  LOG_FILE_OF_CONFLICT_PROCESS: /server/ads/logs/conflict_process.txt
  #是否打印冲突处理的日志
  PRINT_LOG_OF_SEND_SIDE_PATH: false
  #修改冲突处理日志文件路径
  LOG_FILE_OF_SEND_SIDE_PATH: /server/ads/logs/send_side_path.txt
  #纯路径跟随AGV的安全检测距离(单位m)
  AGV_SAFE_DISTANCE_PURE_PURSUIT: 0.5
  #AGV运行中的路径阈值，以时间代价表示(单位s)
  #AGV_SAFE_TIME_PURE_PURSUIT: 3.0
  #路径规划AGV的定位增加的权重(临时)
  LOCATION_PLUS_AUTO_WEIGHT: 0.0
  #路径规划已规划路径增加权重值(正向)
  PLANNED_PLUS_AUTO_WEIGHT_OBVERSE: 0.0
  #路径规划已规划路径增加权重值(反向)
  PLANNED_PLUS_AUTO_WEIGHT_REVERSE: 20.0
  #路径规划运行中的路径增加权重值(正向)
  RUNNING_PLUS_AUTO_WEIGHT_OBVERSE: 0.0
  #路径规划运行中的路径增加权重值(反向)
  RUNNING_PLUS_AUTO_WEIGHT_REVERSE: 12.0
  #路径规划AGV占用的单机区域加权重值
  SINGLE_AREA_PLUS_AUTO_WEIGHT: 0.0
  #机器人报错添加的权重
  AGV_ABNORMAL_STATUS_USER_WEIGHT: 2000.0
  #发送的最短路径长度
  SEND_SHORTEST_SIDE_PATH: 0.01
  #机器人处于避让点路径规划时, 检测前N段路径上是否存在机器人
  CHECK_FRONT_PATH_BY_AVOID_MARKER_PLAN: 3
  #开启路径导航阻挡绕路功能（被静止的机器人阻挡）
  #OPEN_OBSTRUCT_DETOUR: false
  OPEN_OBSTRUCT_DETOUR: false
  #机器人处于避让点路径规划时,在10s内有机器人能到达的点不计算
  AGV_COMING_TIME: 10.0
  #机器人处于避让点路径规划时,过滤以下类型的点不作为避让点，英文逗号隔开
  NO_USED_AVOID_MARKER_TYPE: WORK_MARKER
  #机器人处于避让点路径规划时,计算获取可到达点的次数
  AGV_AVOID_REACHABLE_MARKER_CALCULATE_TIMES: 6
  #机器人异常路径附加权重
  ABNORMAL_VEHICLE_ADDITIONAL_PATH_COST: 1000.0
  #封控区域路径附加权重
  CONTROL_AREA_ADDITIONAL_PATH_COST: 2000.0
  #障碍物路径附加权重
  OBSTACLE_ADDITIONAL_PATH_COST: 1000.0
  #反向路径附加权重
  REVERSE_PATH_ADDITIONAL_PATH_COST: 10.0
  #自动门路径避让附加权重
  AUTO_DOOR_ADDITIONAL_PATH_COST: 10.0
  #禁旋角度
  NO_ROTATING_ANGLE: 10

#统计
STATISTICS:
  #机器人
  VEHICLE:
    #统计cron表达式
    CRON: 0 0,15,30,45 * * * ?
  #任务
  TASK:
    #统计cron表达式
    CRON: 0 0,15,30,45 * * * ?
  #异常
  ABNORMAL:
    #统计cron表达式
    CRON: 0 0,15,30,45 * * * ?

AGV:
  #机器人移动速度
  MOVE_SPEED: 0.5

#多语言
language:
  #存储路径
  path: ./messages/