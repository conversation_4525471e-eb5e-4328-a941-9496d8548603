<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">

    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>
    <logger name="com" level="info"/>
    <logger name="org" level="info"/>
    <logger name="io" level="info"/>
    <logger name="com.youibot.vehicle.scheduler.modules.statistics.service.impl" level="info"/>
    <logger name="springfox.documentation" level="error"/>
    <logger name="com.youibot.vehicle.scheduler" level="debug"/>
    <logger name="org.apache.shiro" level="error"/>
    <logger name="oshi" level="error"/>

    <!-- 定义参数 -->
    <property name="App" value="Fleet"/>
    <property name="log.lever" value="debug"/>
    <property name="log.maxHistory" value="30"/>
    <property name="log.filePath" value="logs"/>
    <property name="log.maxSize" value="30MB"/>
    <property name="log.cleanHistoryOnStart" value="true"/>
    <property name="log.totalSize" value="10GB"/>
    <!-- 	如果这里不想写死C盘，那么上面的配置，系统会自动在项目所在的盘符创建文件夹 -->
    <!--	<property name="log.filePath" value="C:/{spring.application.name}_log"></property>-->
    <!-- 	<property name="log.pattern" value="%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) |-%-5level [%thread] %c [%L] -| %msg%n" /> -->
    <property name="log.pattern" value="%-12(%d{MM-dd HH:mm:ss}) %c [%L] | %msg%n"/>

    <!-- 控制台设置 -->
    <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!--格式化输出：%d:表示日期    %thread:表示线程名     %-5level:级别从左显示5个字符宽度  %msg:日志消息    %n:是换行符-->
            <pattern>%red(%d{yyyy-MM-dd HH:mm:ss.SSS}) %green([%thread]) %highlight(%-5level) %yellow(%logger) %L %M - %cyan(%msg%n)</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- DEBUG -->
    <appender name="debugAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.filePath}/debug/${App}-debug.log</file>
        <rollingPolicy class="com.youibot.vehicle.scheduler.modules.log.policy.MySizeAndTimeBasedRollingPolicy">
            <!-- 文件名称 -->
            <fileNamePattern>${log.filePath}/debug/${App}-debug.%d{yyyy-MM-dd}.#time#.%i.log.gz</fileNamePattern>
            <!-- 文件最大保存历史数量 -->
            <MaxHistory>${log.maxHistory}</MaxHistory>
            <maxFileSize>${log.maxSize}</maxFileSize>
            <cleanHistoryOnStart>${log.cleanHistoryOnStart}</cleanHistoryOnStart>
            <totalSizeCap>${log.totalSize}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} %L %M - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!--<filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>-->
    </appender>

    <!-- INFO -->
    <appender name="infoAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.filePath}/info/${App}-info.log</file>
        <rollingPolicy class="com.youibot.vehicle.scheduler.modules.log.policy.MySizeAndTimeBasedRollingPolicy">
            <!-- 文件名称 -->
            <fileNamePattern>${log.filePath}/info/${App}-info.%d{yyyy-MM-dd}.#time#.%i.log.gz</fileNamePattern>
            <!-- 文件最大保存历史数量 -->
            <MaxHistory>${log.maxHistory}</MaxHistory>
            <maxFileSize>${log.maxSize}</maxFileSize>
            <cleanHistoryOnStart>${log.cleanHistoryOnStart}</cleanHistoryOnStart>
            <totalSizeCap>${log.totalSize}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} %L %M - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- ERROR -->
    <appender name="errorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件路径 -->
        <file>${log.filePath}/error/${App}-error.log</file>
        <rollingPolicy class="com.youibot.vehicle.scheduler.modules.log.policy.MySizeAndTimeBasedRollingPolicy">
            <!-- 文件名称 -->
            <fileNamePattern>${log.filePath}/error/${App}-error.%d{yyyy-MM-dd}.#time#.%i.log.gz</fileNamePattern>
            <!-- 文件最大保存历史数量 -->
            <MaxHistory>${log.maxHistory}</MaxHistory>
            <maxFileSize>${log.maxSize}</maxFileSize>
            <cleanHistoryOnStart>${log.cleanHistoryOnStart}</cleanHistoryOnStart>
            <totalSizeCap>${log.totalSize}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} %L %M - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- 	上线后如果要查看错误日志，可以把level=info改为level=debug -->
    <root level="debug">
        <appender-ref ref="consoleAppender"/>
        <appender-ref ref="debugAppender"/>
        <appender-ref ref="infoAppender"/>
        <appender-ref ref="errorAppender"/>
    </root>
</configuration>
