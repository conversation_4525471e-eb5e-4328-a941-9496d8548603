<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <!--<chain name="chain1">
        THEN(Test.data('{"code":"N001","name":"路径导航","type":"PathNavigation","category":"","params":[{"name":"vehicleCode","type":"Int","source":"Fixed","value":"1","nodeCode":"","variable":""}]}'));
    </chain>-->
    <chain name="chain4">
        THEN(Test.data('{"code":"N001","name":"托盘升降","type":"PalletLift","category":"Vehicle","params":[{"name":"vehicleCode","type":"Int","source":"Fixed","value":"1","nodeCode":"","variable":""}]}'));
    </chain>

    <!--<chain name="chain2">
        THEN(VehicleMoving.data('{"code":"N001","name":"路径导航1","type":"VehicleMoving","category":"Vehicle","params":[{"name":"pointSelectJson","type":"PointSelect","source":"Fixed","value":"{\\"vehicleMapCode\\":\\"test001\\",\\"markerCode\\":\\"7\\"}","nodeCode":"","variable":""},{"name":"vehicleSelectJson","type":"VehicleSelect","source":"Fixed","value":"{\\"vehicleCode\\":\\"1\\"}","nodeCode":"","variable":""},{"name":"accurate","type":"Bool","source":"Fixed","value":"true","nodeCode":"","variable":""}]}'));
    </chain>

    <chain name="chain3">
        THEN(VehicleMoving.data('{"code":"N001","name":"路径导航1","type":"VehicleMoving","category":"Vehicle","params":[{"name":"pointSelectJson","type":"PointSelect","source":"Fixed","value":"{\\"vehicleMapCode\\":\\"test001\\",\\"markerCode\\":\\"2\\"}","nodeCode":"","variable":""},{"name":"vehicleSelectJson","type":"VehicleSelect","source":"Fixed","value":"{\\"vehicleCode\\":\\"2\\"}","nodeCode":"","variable":""},{"name":"accurate","type":"Bool","source":"Fixed","value":"true","nodeCode":"","variable":""}]}'));
    </chain>-->
</flow>