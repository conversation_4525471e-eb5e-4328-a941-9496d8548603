<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youibot.vehicle.scheduler.modules.vehicle.dao.VehicleLockDao">

    <select id="selectUsedVehicleLock" resultType="com.youibot.vehicle.scheduler.modules.vehicle.entity.VehicleLockEntity">
        select * from vehicle_lock
          where task_code is not null
    </select>

    <select id="selectVehicleLockByVehicleCode" resultType="com.youibot.vehicle.scheduler.modules.vehicle.entity.VehicleLockEntity">
        select * from vehicle_lock where vehicle_code = #{vehicleCode}
    </select>

    <select id="selectVehicleLockByTaskCode" resultType="com.youibot.vehicle.scheduler.modules.vehicle.entity.VehicleLockEntity">
        select * from vehicle_lock where task_code = #{taskCode}
    </select>

</mapper>

