<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.youibot.vehicle.scheduler.modules.job.dao.ScheduleJobDao">
	
	<!-- 批量更新状态 -->
	<update id="updateBatch"> 
		update schedule_job set status = #{status} where id in
		<foreach item="id" collection="ids"  open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

</mapper>