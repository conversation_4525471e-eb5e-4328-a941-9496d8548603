<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.youibot.vehicle.scheduler.modules.task.dao.TaskNodeDao">

    <insert id="insertBatch" parameterType="java.util.List">
        insert into task_node(`id`,`task_id`,`node_config_id`,`code`,`name`,`type`,`category`,`vehicle_code`,`status`,`param_in`,`param_out`,`start_time`,`end_time`,`creator`,`create_date`,`updater`,`update_date`)
        values
        <foreach collection="list" item="t" separator=",">
            (#{t.id},#{t.taskId},#{t.nodeConfigId},#{t.code},#{t.name},#{t.type},#{t.category},#{t.vehicleCode},#{t.status},#{t.paramIn},#{t.paramOut},#{t.startTime},#{t.endTime},#{t.creator},#{t.createDate},#{t.updater},#{t.updateDate})
        </foreach>
    </insert>
</mapper>