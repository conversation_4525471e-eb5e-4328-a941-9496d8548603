<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.youibot.vehicle.scheduler.modules.security.dao.SysUserTokenDao">

	<select id="getByToken" resultType="com.youibot.vehicle.scheduler.modules.security.entity.SysUserTokenEntity">
		select * from sys_user_token where token = #{value}
	</select>

	<select id="getByUserId" resultType="com.youibot.vehicle.scheduler.modules.security.entity.SysUserTokenEntity">
		select * from sys_user_token where user_id = #{value}
	</select>

	<update id="updateToken">
		update sys_user_token set token = #{token} where user_id = #{userId}
	</update>
</mapper>