<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.youibot.vehicle.scheduler.modules.sys.dao.SysRoleUserDao">

    <delete id="deleteByRoleIds">
        delete from sys_role_user where role_id in
        <foreach item="roleId" collection="array" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <delete id="deleteByUserIds">
        delete from sys_role_user where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="getRoleIdList" resultType="long">
        select role_id from sys_role_user where user_id = #{value}
    </select>

</mapper>