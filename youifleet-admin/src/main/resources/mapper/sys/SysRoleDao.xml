<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.youibot.vehicle.scheduler.modules.sys.dao.SysRoleDao">

    <select id="getRoleNamesByUserId" resultType="java.lang.String">
        select t2.name from sys_role_user t1 left join sys_role t2 on t1.role_id=t2.id
        where t1.user_id = #{userId}
    </select>
</mapper>