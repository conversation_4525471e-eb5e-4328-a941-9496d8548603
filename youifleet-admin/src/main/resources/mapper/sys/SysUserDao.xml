<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.youibot.vehicle.scheduler.modules.sys.dao.SysUserDao">

	<select id="getList" resultType="com.youibot.vehicle.scheduler.modules.sys.entity.SysUserEntity">
		select t1.* from sys_user t1
		where t1.super_admin = 0
		<if test="username != null and username.trim() != ''">
			and t1.username like #{username}
		</if>
		<if test="realName != null and realName.trim() != ''">
			and t1.real_name like #{realName}
		</if>
		<if test="email != null and email.trim() != ''">
			and t1.email like #{email}
		</if>
		<if test="mobile != null and mobile.trim() != ''">
			and t1.mobile like #{mobile}
		</if>
		<if test="status != null">
			and t1.status = #{status}
		</if>
		<if test="startUpdateDate != null">
			and t1.update_date >= #{startUpdateDate}
		</if>
		<if test="endUpdateDate != null">
			and t1.update_date &lt; #{endUpdateDate}
		</if>
		<if test="roleId != null">
			and 0 &lt; (select count(1) from sys_role_user t2 where t2.role_id=#{roleId} and t2.user_id=t1.id)
		</if>
	</select>

	<select id="getById" resultType="com.youibot.vehicle.scheduler.modules.sys.entity.SysUserEntity">
		select t1.* from sys_user t1
			where t1.id = #{value}
	</select>

	<select id="getByUsername" resultType="com.youibot.vehicle.scheduler.modules.sys.entity.SysUserEntity">
		select * from sys_user where username = #{value}
	</select>

    <select id="realNamesByRoleId" resultType="java.lang.String">
		select distinct t1.real_name from sys_role_user t2 left join sys_user t1 on t2.user_id=t1.id
		where t2.role_id=#{roleId}
	</select>
    <select id="getUserIdsByRealName" resultType="java.lang.Long">
		select id from sys_user where real_name like #{realName}
	</select>

    <update id="updatePassword">
		update sys_user set password = #{newPassword} where id = #{id}
	</update>
</mapper>