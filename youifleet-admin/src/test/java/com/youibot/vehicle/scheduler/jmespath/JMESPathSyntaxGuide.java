package com.youibot.vehicle.scheduler.jmespath;

import io.burt.jmespath.Expression;
import io.burt.jmespath.JmesPath;
import io.burt.jmespath.jackson.JacksonRuntime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * JMESPath 语法指南和限制说明
 * 
 * 本类展示JMESPath的正确语法和常见错误，帮助开发者避免语法陷阱
 */
public class JMESPathSyntaxGuide {
    
    private static final JmesPath<JsonNode> jmesPath = new JacksonRuntime();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) throws Exception {
        System.out.println("=== JMESPath 语法指南和限制说明 ===\n");
        
        // 1. 数值比较语法
        numericComparisonSyntax();
        
        // 2. 条件表达式限制
        conditionalExpressionLimitations();
        
        // 3. 字符串处理
        stringHandling();
        
        // 4. 数组操作
        arrayOperations();
        
        // 5. 投影语法
        projectionSyntax();
        
        // 6. 函数使用
        functionUsage();
        
        // 7. 常见错误和解决方案
        commonErrorsAndSolutions();
    }
    
    /**
     * 1. 数值比较语法
     */
    public static void numericComparisonSyntax() throws Exception {
        System.out.println("=== 1. 数值比较语法 ===");
        
        String jsonData = "{\n" +
            "    \"products\": [\n" +
            "        {\"name\": \"Laptop\", \"price\": 1000},\n" +
            "        {\"name\": \"Book\", \"price\": 20},\n" +
            "        {\"name\": \"Phone\", \"price\": 800}\n" +
            "    ]\n" +
            "}";
        
        System.out.println("✓ 正确语法 - 数值需要反引号包围:");
        String correct1 = executeJMESPath(jsonData, "products[?price > `500`].name");
        System.out.println("products[?price > `500`].name -> " + correct1);
        
        System.out.println("\n✗ 错误语法 - 数值不能直接使用:");
        System.out.println("products[?price > 500].name -> 语法错误");
        
        System.out.println("\n✓ 正确语法 - 多条件比较:");
        String correct2 = executeJMESPath(jsonData, "products[?price >= `100` && price <= `900`].name");
        System.out.println("products[?price >= `100` && price <= `900`].name -> " + correct2);
        
        System.out.println();
    }
    
    /**
     * 2. 条件表达式限制
     */
    public static void conditionalExpressionLimitations() throws Exception {
        System.out.println("=== 2. 条件表达式限制 ===");
        
        String jsonData = "{\n" +
            "    \"employees\": [\n" +
            "        {\"name\": \"Alice\", \"salary\": 5000, \"level\": \"Senior\"},\n" +
            "        {\"name\": \"Bob\", \"salary\": 3000, \"level\": \"Junior\"}\n" +
            "    ]\n" +
            "}";
        
        System.out.println("✗ JMESPath不支持三元运算符:");
        System.out.println("salary > `4000` ? 'High' : 'Low' -> 语法错误");
        
        System.out.println("\n✓ 替代方案1 - 使用布尔值:");
        String solution1 = executeJMESPath(jsonData, "employees[*].{name: name, isHighSalary: salary > `4000`}");
        System.out.println("employees[*].{name: name, isHighSalary: salary > `4000`} -> " + solution1);
        
        System.out.println("\n✓ 替代方案2 - 分别查询:");
        String highSalary = executeJMESPath(jsonData, "employees[?salary > `4000`].{name: name, level: 'High'}");
        String lowSalary = executeJMESPath(jsonData, "employees[?salary <= `4000`].{name: name, level: 'Low'}");
        System.out.println("高薪员工: " + highSalary);
        System.out.println("低薪员工: " + lowSalary);
        
        System.out.println();
    }
    
    /**
     * 3. 字符串处理
     */
    public static void stringHandling() throws Exception {
        System.out.println("=== 3. 字符串处理 ===");
        
        String jsonData = "{\n" +
            "    \"users\": [\n" +
            "        {\"name\": \"Alice Smith\", \"email\": \"<EMAIL>\"},\n" +
            "        {\"name\": \"Bob Jones\", \"email\": \"<EMAIL>\"}\n" +
            "    ]\n" +
            "}";
        
        System.out.println("✓ 字符串比较 - 使用单引号:");
        String result1 = executeJMESPath(jsonData, "users[?name == 'Alice Smith'].email");
        System.out.println("users[?name == 'Alice Smith'].email -> " + result1);
        
        System.out.println("\n✓ 字符串包含 - 使用contains函数:");
        String result2 = executeJMESPath(jsonData, "users[?contains(email, 'example')].name");
        System.out.println("users[?contains(email, 'example')].name -> " + result2);
        
        System.out.println("\n✓ 字符串开头 - 使用starts_with函数:");
        String result3 = executeJMESPath(jsonData, "users[?starts_with(name, 'Alice')].name");
        System.out.println("users[?starts_with(name, 'Alice')].name -> " + result3);
        
        System.out.println("\n✗ 不支持正则表达式:");
        System.out.println("users[?name =~ /^Alice/] -> 不支持");
        
        System.out.println();
    }
    
    /**
     * 4. 数组操作
     */
    public static void arrayOperations() throws Exception {
        System.out.println("=== 4. 数组操作 ===");
        
        String jsonData = "{\n" +
            "    \"numbers\": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],\n" +
            "    \"items\": [\n" +
            "        {\"id\": 1, \"value\": 100},\n" +
            "        {\"id\": 2, \"value\": 200},\n" +
            "        {\"id\": 3, \"value\": 300}\n" +
            "    ]\n" +
            "}";
        
        System.out.println("✓ 数组切片:");
        String slice1 = executeJMESPath(jsonData, "numbers[2:5]");
        System.out.println("numbers[2:5] -> " + slice1);
        
        System.out.println("\n✓ 数组前N个元素:");
        String first3 = executeJMESPath(jsonData, "numbers[:3]");
        System.out.println("numbers[:3] -> " + first3);
        
        System.out.println("\n✓ 数组后N个元素:");
        String last3 = executeJMESPath(jsonData, "numbers[-3:]");
        System.out.println("numbers[-3:] -> " + last3);
        
        System.out.println("\n✓ 数组长度:");
        String length = executeJMESPath(jsonData, "length(numbers)");
        System.out.println("length(numbers) -> " + length);
        
        System.out.println("\n✓ 数组求和:");
        String sum = executeJMESPath(jsonData, "sum(items[*].value)");
        System.out.println("sum(items[*].value) -> " + sum);
        
        System.out.println();
    }
    
    /**
     * 5. 投影语法
     */
    public static void projectionSyntax() throws Exception {
        System.out.println("=== 5. 投影语法 ===");
        
        String jsonData = "{\n" +
            "    \"orders\": [\n" +
            "        {\"id\": 1, \"customer\": \"Alice\", \"amount\": 100, \"items\": [\"A\", \"B\"]},\n" +
            "        {\"id\": 2, \"customer\": \"Bob\", \"amount\": 200, \"items\": [\"C\", \"D\", \"E\"]}\n" +
            "    ]\n" +
            "}";
        
        System.out.println("✓ 对象投影 - 创建新结构:");
        String projection1 = executeJMESPath(jsonData, 
            "orders[*].{orderId: id, customerName: customer, total: amount}");
        System.out.println("orders[*].{orderId: id, customerName: customer, total: amount}");
        System.out.println("结果: " + projection1);
        
        System.out.println("\n✓ 数组投影 - 选择多个字段:");
        String projection2 = executeJMESPath(jsonData, "orders[*].[id, customer, amount]");
        System.out.println("orders[*].[id, customer, amount] -> " + projection2);
        
        System.out.println("\n✓ 嵌套投影 - 包含计算:");
        String projection3 = executeJMESPath(jsonData, 
            "orders[*].{id: id, customer: customer, itemCount: length(items)}");
        System.out.println("orders[*].{id: id, customer: customer, itemCount: length(items)}");
        System.out.println("结果: " + projection3);
        
        System.out.println();
    }
    
    /**
     * 6. 函数使用
     */
    public static void functionUsage() throws Exception {
        System.out.println("=== 6. 函数使用 ===");
        
        String jsonData = "{\n" +
            "    \"scores\": [85, 92, 78, 96, 88],\n" +
            "    \"names\": [\"alice\", \"bob\", \"charlie\"],\n" +
            "    \"data\": [\n" +
            "        {\"name\": \"Alice\", \"score\": 85},\n" +
            "        {\"name\": \"Bob\", \"score\": 92},\n" +
            "        {\"name\": \"Charlie\", \"score\": 78}\n" +
            "    ]\n" +
            "}";
        
        System.out.println("✓ 数学函数:");
        System.out.println("sum(scores) -> " + executeJMESPath(jsonData, "sum(scores)"));
        System.out.println("avg(scores) -> " + executeJMESPath(jsonData, "avg(scores)"));
        System.out.println("max(scores) -> " + executeJMESPath(jsonData, "max(scores)"));
        System.out.println("min(scores) -> " + executeJMESPath(jsonData, "min(scores)"));
        
        System.out.println("\n✓ 数组函数:");
        System.out.println("length(scores) -> " + executeJMESPath(jsonData, "length(scores)"));
        System.out.println("reverse(scores) -> " + executeJMESPath(jsonData, "reverse(scores)"));
        
        System.out.println("\n✓ 排序函数:");
        System.out.println("sort(scores) -> " + executeJMESPath(jsonData, "sort(scores)"));
        System.out.println("sort_by(data, &score) -> " + executeJMESPath(jsonData, "sort_by(data, &score)"));
        
        System.out.println("\n✓ 字符串函数:");
        System.out.println("join(', ', names) -> " + executeJMESPath(jsonData, "join(', ', names)"));
        
        System.out.println();
    }
    
    /**
     * 7. 常见错误和解决方案
     */
    public static void commonErrorsAndSolutions() {
        System.out.println("=== 7. 常见错误和解决方案 ===");
        
        System.out.println("❌ 错误1: 数值不用反引号");
        System.out.println("   错误: products[?price > 100]");
        System.out.println("   正确: products[?price > `100`]");
        
        System.out.println("\n❌ 错误2: 使用三元运算符");
        System.out.println("   错误: {level: salary > 5000 ? 'High' : 'Low'}");
        System.out.println("   正确: {isHigh: salary > `5000`}");
        
        System.out.println("\n❌ 错误3: 字符串使用双引号");
        System.out.println("   错误: users[?name == \"Alice\"]");
        System.out.println("   正确: users[?name == 'Alice']");
        
        System.out.println("\n❌ 错误4: 使用let表达式");
        System.out.println("   错误: let $var = value in expression");
        System.out.println("   正确: 使用变量替换机制");
        
        System.out.println("\n❌ 错误5: 正则表达式");
        System.out.println("   错误: users[?name =~ /pattern/]");
        System.out.println("   正确: users[?contains(name, 'pattern')]");
        
        System.out.println("\n✅ 最佳实践:");
        System.out.println("1. 数值比较总是使用反引号: `123`");
        System.out.println("2. 字符串比较使用单引号: 'text'");
        System.out.println("3. 复杂条件拆分为多个简单查询");
        System.out.println("4. 使用内置函数而不是自定义逻辑");
        System.out.println("5. 缓存编译后的表达式提高性能");
        
        System.out.println();
    }
    
    // 工具方法
    private static String executeJMESPath(String jsonData, String expression) throws Exception {
        JsonNode data = objectMapper.readTree(jsonData);
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        return result.toString();
    }
}
