{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "E:/work/idea/wk1"], "env": {"NODE_ENV": "production"}}, "codebase-retrieval": {"command": "augment-mcp-server", "args": ["--workspace", "E:/work/idea/wk1"], "env": {"AUGMENT_API_KEY": "${AUGMENT_API_KEY}"}}, "web-tools": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-web-search"], "env": {"GOOGLE_API_KEY": "${GOOGLE_API_KEY}", "GOOGLE_CSE_ID": "${GOOGLE_CSE_ID}"}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "mermaid": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-mermaid"]}, "process-manager": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-process"], "env": {"ALLOWED_COMMANDS": "git,npm,yarn,pnpm,node,python,java,mvn,gradle,docker,kubectl,helm"}}}, "globalShortcuts": {"commandPalette": "Ctrl+Shift+P", "quickChat": "Ctrl+L"}, "appearance": {"theme": "auto"}, "experimental": {"mcpEnabled": true, "advancedCodeAnalysis": true}}