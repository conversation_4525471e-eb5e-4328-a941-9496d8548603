package com.youibot.vehicle.scheduler.jmespath;

import io.burt.jmespath.Expression;
import io.burt.jmespath.JmesPath;
import io.burt.jmespath.jackson.JacksonRuntime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * JMESPath 变量绑定实例 (JDK8兼容版本)
 * 演示如何在JMESPath表达式中使用变量替换功能
 * 注意：JMESPath本身不支持变量，这里通过字符串替换实现变量功能
 */
public class JMESPathVariableBindingExamples {

    private static final JmesPath<JsonNode> jmesPath = new JacksonRuntime();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    public static void main(String[] args) throws Exception {
        // 示例1: 基本变量绑定
        basicVariableBinding();

        // 示例2: 复杂对象变量绑定
        complexObjectVariableBinding();

        // 示例3: 数组索引变量绑定
        arrayIndexVariableBinding();

        // 示例4: 条件过滤变量绑定
        conditionalFilterVariableBinding();

        // 示例5: 多变量组合使用
        multipleVariableBinding();

        // 示例6: 动态路径变量绑定
        dynamicPathVariableBinding();
    }
    
    /**
     * 示例1: 基本变量绑定
     * 使用变量替换实现动态表达式
     */
    public static void basicVariableBinding() throws Exception {
        System.out.println("=== 示例1: 基本变量绑定 ===");

        String jsonData = "{\n" +
            "    \"users\": [\n" +
            "        {\"name\": \"Alice\", \"age\": 30, \"department\": \"Engineering\"},\n" +
            "        {\"name\": \"Bob\", \"age\": 25, \"department\": \"Marketing\"},\n" +
            "        {\"name\": \"Charlie\", \"age\": 35, \"department\": \"Engineering\"}\n" +
            "    ]\n" +
            "}";

        JsonNode data = objectMapper.readTree(jsonData);

        // 使用变量替换实现动态表达式
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("targetDept", "Engineering");

        String expressionTemplate = "users[?department == '${targetDept}'].{name: name, age: age}";
        String expression = replaceVariables(expressionTemplate, variables);

        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);

        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例2: 复杂对象变量绑定
     * 使用多个变量进行复杂过滤
     */
    public static void complexObjectVariableBinding() throws Exception {
        System.out.println("=== 示例2: 复杂对象变量绑定 ===");

        String jsonData = "{\n" +
            "    \"products\": [\n" +
            "        {\"id\": 1, \"name\": \"Laptop\", \"price\": 1000, \"category\": \"Electronics\"},\n" +
            "        {\"id\": 2, \"name\": \"Book\", \"price\": 20, \"category\": \"Education\"},\n" +
            "        {\"id\": 3, \"name\": \"Phone\", \"price\": 800, \"category\": \"Electronics\"}\n" +
            "    ],\n" +
            "    \"filters\": {\n" +
            "        \"minPrice\": 500,\n" +
            "        \"category\": \"Electronics\"\n" +
            "    }\n" +
            "}";

        JsonNode data = objectMapper.readTree(jsonData);

        // 使用多个变量进行复杂过滤
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("minPrice", 500);
        variables.put("category", "Electronics");

        String expressionTemplate = "products[?price >= `${minPrice}` && category == '${category}'].{" +
            "id: id, name: name, price: price}";
        String expression = replaceVariables(expressionTemplate, variables);

        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);

        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例3: 数组索引变量绑定
     * 使用变量作为数组索引
     */
    public static void arrayIndexVariableBinding() throws Exception {
        System.out.println("=== 示例3: 数组索引变量绑定 ===");

        String jsonData = "{\n" +
            "    \"data\": [\"first\", \"second\", \"third\", \"fourth\", \"fifth\"],\n" +
            "    \"config\": {\n" +
            "        \"startIndex\": 1,\n" +
            "        \"endIndex\": 3\n" +
            "    }\n" +
            "}";

        JsonNode data = objectMapper.readTree(jsonData);

        // 使用变量作为数组切片索引
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("start", 1);
        variables.put("end", 3);

        String expressionTemplate = "data[${start}:${end}]";
        String expression = replaceVariables(expressionTemplate, variables);

        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);

        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例4: 条件过滤变量绑定
     * 在过滤条件中使用变量
     */
    public static void conditionalFilterVariableBinding() throws Exception {
        System.out.println("=== 示例4: 条件过滤变量绑定 ===");

        String jsonData = "{\n" +
            "    \"orders\": [\n" +
            "        {\"id\": 1, \"amount\": 100, \"status\": \"completed\", \"date\": \"2024-01-15\"},\n" +
            "        {\"id\": 2, \"amount\": 250, \"status\": \"pending\", \"date\": \"2024-01-16\"},\n" +
            "        {\"id\": 3, \"amount\": 300, \"status\": \"completed\", \"date\": \"2024-01-17\"},\n" +
            "        {\"id\": 4, \"amount\": 150, \"status\": \"cancelled\", \"date\": \"2024-01-18\"}\n" +
            "    ],\n" +
            "    \"criteria\": {\n" +
            "        \"minAmount\": 200,\n" +
            "        \"targetStatus\": \"completed\"\n" +
            "    }\n" +
            "}";

        JsonNode data = objectMapper.readTree(jsonData);

        // 使用多个变量进行复杂过滤
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("minAmount", 200);
        variables.put("status", "completed");

        String expressionTemplate = "orders[?amount >= `${minAmount}` && status == '${status}'].{" +
            "orderId: id, orderAmount: amount, orderDate: date}";
        String expression = replaceVariables(expressionTemplate, variables);

        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);

        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例5: 多变量组合使用
     * 演示多个变量的嵌套和组合使用
     */
    public static void multipleVariableBinding() throws Exception {
        System.out.println("=== 示例5: 多变量组合使用 ===");

        String jsonData = "{\n" +
            "    \"employees\": [\n" +
            "        {\"name\": \"Alice\", \"salary\": 5000, \"department\": \"IT\", \"experience\": 5},\n" +
            "        {\"name\": \"Bob\", \"salary\": 4000, \"department\": \"HR\", \"experience\": 3},\n" +
            "        {\"name\": \"Charlie\", \"salary\": 6000, \"department\": \"IT\", \"experience\": 7},\n" +
            "        {\"name\": \"Diana\", \"salary\": 4500, \"department\": \"Finance\", \"experience\": 4}\n" +
            "    ],\n" +
            "    \"promotion\": {\n" +
            "        \"targetDepartment\": \"IT\",\n" +
            "        \"minSalary\": 4500,\n" +
            "        \"minExperience\": 5,\n" +
            "        \"bonus\": 1000\n" +
            "    }\n" +
            "}";

        JsonNode data = objectMapper.readTree(jsonData);

        // 使用多个变量计算晋升候选人
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("dept", "IT");
        variables.put("minSal", 4500);
        variables.put("minExp", 5);

        String expressionTemplate = "employees[?department == '${dept}' && salary >= `${minSal}` && experience >= `${minExp}`].{" +
            "name: name, currentSalary: salary, experience: experience}";
        String expression = replaceVariables(expressionTemplate, variables);

        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);

        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例6: 动态路径变量绑定
     * 使用变量构建动态的JSON路径
     */
    public static void dynamicPathVariableBinding() throws Exception {
        System.out.println("=== 示例6: 动态路径变量绑定 ===");

        String jsonData = "{\n" +
            "    \"config\": {\n" +
            "        \"dataSource\": \"sales\",\n" +
            "        \"field\": \"revenue\"\n" +
            "    },\n" +
            "    \"sales\": {\n" +
            "        \"revenue\": [1000, 1500, 2000, 1800, 2200],\n" +
            "        \"units\": [10, 15, 20, 18, 22]\n" +
            "    },\n" +
            "    \"marketing\": {\n" +
            "        \"revenue\": [500, 800, 1200, 900, 1100],\n" +
            "        \"units\": [5, 8, 12, 9, 11]\n" +
            "    }\n" +
            "}";

        JsonNode data = objectMapper.readTree(jsonData);

        // 注意：JMESPath不支持完全动态的路径构建
        // 这里展示一种变通的方法，使用固定的表达式
        String expression = "{source: config.dataSource, field: config.field, " +
            "salesRevenue: sales.revenue, marketingRevenue: marketing.revenue}";

        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);

        System.out.println("表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 工具方法：创建带有变量的JMESPath表达式执行器
     */
    public static class VariableJMESPathExecutor {
        private final JmesPath<JsonNode> jmesPath;
        private final ObjectMapper objectMapper;
        
        public VariableJMESPathExecutor() {
            this.jmesPath = new JacksonRuntime();
            this.objectMapper = new ObjectMapper();
        }
        
        /**
         * 执行带变量的JMESPath表达式
         * @param data JSON数据
         * @param expression JMESPath表达式模板
         * @param variables 变量映射
         * @return 执行结果
         */
        public JsonNode executeWithVariables(JsonNode data, String expression, Map<String, Object> variables) throws Exception {
            // 将变量注入到数据中
            ObjectNode dataWithVars = data.deepCopy();
            ObjectNode varsNode = objectMapper.createObjectNode();
            
            for (Map.Entry<String, Object> entry : variables.entrySet()) {
                JsonNode varValue = objectMapper.valueToTree(entry.getValue());
                varsNode.set(entry.getKey(), varValue);
            }
            dataWithVars.set("$vars", varsNode);
            
            // 修改表达式以使用注入的变量
            String modifiedExpression = expression.replaceAll("\\$([a-zA-Z_][a-zA-Z0-9_]*)", "\\$vars.$1");
            
            Expression<JsonNode> compiledExpression = jmesPath.compile(modifiedExpression);
            return compiledExpression.search(dataWithVars);
        }
    }
    
    /**
     * 演示变量执行器的使用
     */
    public static void demonstrateVariableExecutor() throws Exception {
        System.out.println("=== 变量执行器演示 ===");
        
        String jsonData = "   {\n" +
                "                \"products\": [\n" +
                "                    {\"name\": \"Laptop\", \"price\": 1000, \"category\": \"Electronics\"},\n" +
                "                    {\"name\": \"Book\", \"price\": 20, \"category\": \"Education\"},\n" +
                "                    {\"name\": \"Phone\", \"price\": 800, \"category\": \"Electronics\"}\n" +
                "                ]\n" +
                "            }";
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 定义变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("minPrice", 500);
        variables.put("targetCategory", "Electronics");
        
        // 使用变量的表达式
        String expression = "products[?price >= $minPrice && category == $targetCategory].name";
        
        VariableJMESPathExecutor executor = new VariableJMESPathExecutor();
        JsonNode result = executor.executeWithVariables(data, expression, variables);
        
        System.out.println("表达式: " + expression);
        System.out.println("变量: " + variables);
        System.out.println("结果: " + result.toPrettyString());
    }

    /**
     * 变量替换工具方法
     */
    public static String replaceVariables(String template, Map<String, Object> variables) {
        if (template == null || variables == null || variables.isEmpty()) {
            return template;
        }

        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = variables.get(variableName);
            String replacement = value != null ? value.toString() : "";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }
}
