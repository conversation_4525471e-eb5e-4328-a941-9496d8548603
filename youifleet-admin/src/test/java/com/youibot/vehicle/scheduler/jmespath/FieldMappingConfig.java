package com.youibot.vehicle.scheduler.jmespath;

import com.fasterxml.jackson.databind.JsonNode;
import java.util.*;
import java.util.function.Function;

/**
 * 字段映射配置类
 * 支持复杂的字段映射规则和数据转换
 */
public class FieldMappingConfig {
    
    private List<FieldMapping> mappings = new ArrayList<>();
    private Map<String, DataTransformation> globalTransformations = new HashMap<>();
    
    /**
     * 添加字段映射
     */
    public FieldMappingConfig addMapping(String sourcePath, String targetPath) {
        mappings.add(new FieldMapping(sourcePath, targetPath));
        return this;
    }
    
    /**
     * 添加带默认值的字段映射
     */
    public FieldMappingConfig addMapping(String sourcePath, String targetPath, Object defaultValue) {
        mappings.add(new FieldMapping(sourcePath, targetPath, defaultValue));
        return this;
    }
    
    /**
     * 添加带转换的字段映射
     */
    public FieldMappingConfig addMapping(String sourcePath, String targetPath, DataTransformation transformation) {
        mappings.add(new FieldMapping(sourcePath, targetPath, transformation));
        return this;
    }
    
    /**
     * 添加条件映射
     */
    public FieldMappingConfig addConditionalMapping(String sourcePath, String targetPath, String condition, Object trueValue, Object falseValue) {
        DataTransformation transformation = DataTransformation.conditional()
            .addCondition(condition, trueValue)
            .defaultValue(falseValue);
        mappings.add(new FieldMapping(sourcePath, targetPath, transformation));
        return this;
    }
    
    /**
     * 添加数组映射
     */
    public FieldMappingConfig addArrayMapping(String sourcePath, String targetPath, FieldMapping itemMapping) {
        FieldMapping arrayMapping = new FieldMapping(sourcePath, targetPath);
        arrayMapping.setArrayItemMapping(itemMapping);
        mappings.add(arrayMapping);
        return this;
    }
    
    /**
     * 添加聚合映射
     */
    public FieldMappingConfig addAggregateMapping(String sourcePath, String targetPath, AggregateFunction function) {
        DataTransformation transformation = DataTransformation.aggregate(function);
        mappings.add(new FieldMapping(sourcePath, targetPath, transformation));
        return this;
    }
    
    public List<FieldMapping> getMappings() {
        return mappings;
    }
}

/**
 * 字段映射定义
 */
class FieldMapping {
    private String sourceExpression;
    private String targetPath;
    private Object defaultValue;
    private DataTransformation transformation;
    private FieldMapping arrayItemMapping;
    private boolean isConditional = false;
    private String condition;
    
    public FieldMapping(String sourceExpression, String targetPath) {
        this.sourceExpression = sourceExpression;
        this.targetPath = targetPath;
    }
    
    public FieldMapping(String sourceExpression, String targetPath, Object defaultValue) {
        this(sourceExpression, targetPath);
        this.defaultValue = defaultValue;
    }
    
    public FieldMapping(String sourceExpression, String targetPath, DataTransformation transformation) {
        this(sourceExpression, targetPath);
        this.transformation = transformation;
    }
    
    // Getters and Setters
    public String getSourceExpression() { return sourceExpression; }
    public void setSourceExpression(String sourceExpression) { this.sourceExpression = sourceExpression; }
    
    public String getTargetPath() { return targetPath; }
    public void setTargetPath(String targetPath) { this.targetPath = targetPath; }
    
    public Object getDefaultValue() { return defaultValue; }
    public void setDefaultValue(Object defaultValue) { this.defaultValue = defaultValue; }
    
    public DataTransformation getTransformation() { return transformation; }
    public void setTransformation(DataTransformation transformation) { this.transformation = transformation; }
    
    public FieldMapping getArrayItemMapping() { return arrayItemMapping; }
    public void setArrayItemMapping(FieldMapping arrayItemMapping) { this.arrayItemMapping = arrayItemMapping; }
    
    public boolean isConditional() { return isConditional; }
    public void setConditional(boolean conditional) { isConditional = conditional; }
    
    public String getCondition() { return condition; }
    public void setCondition(String condition) { this.condition = condition; }
}

/**
 * 数据转换定义
 */
class DataTransformation {
    private TransformationType type;
    private String format;
    private String calculation;
    private String dateFormat;
    private List<ConditionalRule> conditions = new ArrayList<>();
    private Function<JsonNode, Object> customFunction;
    private AggregateFunction aggregateFunction;
    
    public static DataTransformation stringFormat(String format) {
        DataTransformation transformation = new DataTransformation();
        transformation.type = TransformationType.STRING_FORMAT;
        transformation.format = format;
        return transformation;
    }
    
    public static DataTransformation numberCalculation(String calculation) {
        DataTransformation transformation = new DataTransformation();
        transformation.type = TransformationType.NUMBER_CALCULATION;
        transformation.calculation = calculation;
        return transformation;
    }
    
    public static DataTransformation dateFormat(String dateFormat) {
        DataTransformation transformation = new DataTransformation();
        transformation.type = TransformationType.DATE_FORMAT;
        transformation.dateFormat = dateFormat;
        return transformation;
    }
    
    public static DataTransformation conditional() {
        DataTransformation transformation = new DataTransformation();
        transformation.type = TransformationType.CONDITIONAL;
        return transformation;
    }
    
    public static DataTransformation custom(Function<JsonNode, Object> function) {
        DataTransformation transformation = new DataTransformation();
        transformation.type = TransformationType.CUSTOM_FUNCTION;
        transformation.customFunction = function;
        return transformation;
    }
    
    public static DataTransformation aggregate(AggregateFunction function) {
        DataTransformation transformation = new DataTransformation();
        transformation.type = TransformationType.AGGREGATE;
        transformation.aggregateFunction = function;
        return transformation;
    }
    
    public DataTransformation addCondition(String condition, Object value) {
        conditions.add(new ConditionalRule(condition, value));
        return this;
    }
    
    public DataTransformation defaultValue(Object value) {
        conditions.add(new ConditionalRule("default", value));
        return this;
    }
    
    // Getters
    public TransformationType getType() { return type; }
    public String getFormat() { return format; }
    public String getCalculation() { return calculation; }
    public String getDateFormat() { return dateFormat; }
    public List<ConditionalRule> getConditions() { return conditions; }
    public Function<JsonNode, Object> getCustomFunction() { return customFunction; }
    public AggregateFunction getAggregateFunction() { return aggregateFunction; }
}

/**
 * 转换类型枚举
 */
enum TransformationType {
    STRING_FORMAT,
    NUMBER_CALCULATION,
    DATE_FORMAT,
    CONDITIONAL,
    CUSTOM_FUNCTION,
    AGGREGATE
}

/**
 * 条件规则
 */
class ConditionalRule {
    private String condition;
    private Object value;
    
    public ConditionalRule(String condition, Object value) {
        this.condition = condition;
        this.value = value;
    }
    
    public String getCondition() { return condition; }
    public Object getValue() { return value; }
}

/**
 * 聚合函数枚举
 */
enum AggregateFunction {
    SUM, AVG, COUNT, MIN, MAX, FIRST, LAST, CONCAT
}

/**
 * 转换配置
 */
class TransformConfig {
    private Map<String, Object> variables = new HashMap<>();
    private List<FieldMapping> fieldMappings = new ArrayList<>();
    private StructureTemplate structureTemplate;
    private List<PostProcessingRule> postProcessingRules = new ArrayList<>();
    
    public static TransformConfig create() {
        return new TransformConfig();
    }
    
    public TransformConfig variables(Map<String, Object> variables) {
        this.variables = variables;
        return this;
    }
    
    public TransformConfig variable(String name, Object value) {
        this.variables.put(name, value);
        return this;
    }
    
    public TransformConfig fieldMappings(List<FieldMapping> fieldMappings) {
        this.fieldMappings = fieldMappings;
        return this;
    }
    
    public TransformConfig addFieldMapping(FieldMapping mapping) {
        this.fieldMappings.add(mapping);
        return this;
    }
    
    public TransformConfig structureTemplate(StructureTemplate template) {
        this.structureTemplate = template;
        return this;
    }
    
    public TransformConfig postProcessingRules(List<PostProcessingRule> rules) {
        this.postProcessingRules = rules;
        return this;
    }
    
    public TransformConfig addPostProcessingRule(PostProcessingRule rule) {
        this.postProcessingRules.add(rule);
        return this;
    }
    
    // Getters
    public Map<String, Object> getVariables() { return variables; }
    public List<FieldMapping> getFieldMappings() { return fieldMappings; }
    public StructureTemplate getStructureTemplate() { return structureTemplate; }
    public List<PostProcessingRule> getPostProcessingRules() { return postProcessingRules; }
}

/**
 * 结构模板
 */
class StructureTemplate {
    String templateExpression;
    Map<String, Object> templateVariables = new HashMap<>();
    boolean isArray = false;
    String arrayItemTemplate;
    
    public static StructureTemplate create(String templateExpression) {
        StructureTemplate template = new StructureTemplate();
        template.templateExpression = templateExpression;
        return template;
    }
    
    public static StructureTemplate array(String itemTemplate) {
        StructureTemplate template = new StructureTemplate();
        template.isArray = true;
        template.arrayItemTemplate = itemTemplate;
        return template;
    }
    
    public StructureTemplate variable(String name, Object value) {
        templateVariables.put(name, value);
        return this;
    }
    
    // Getters
    public String getTemplateExpression() { return templateExpression; }
    public Map<String, Object> getTemplateVariables() { return templateVariables; }
    public boolean isArray() { return isArray; }
    public String getArrayItemTemplate() { return arrayItemTemplate; }
}

/**
 * 后处理规则
 */
class PostProcessingRule {
    private PostProcessingType type;
    private String filterExpression;
    private String sortExpression;
    private String groupExpression;
    private String aggregateExpression;
    
    public static PostProcessingRule filter(String expression) {
        PostProcessingRule rule = new PostProcessingRule();
        rule.type = PostProcessingType.FILTER;
        rule.filterExpression = expression;
        return rule;
    }
    
    public static PostProcessingRule sort(String expression) {
        PostProcessingRule rule = new PostProcessingRule();
        rule.type = PostProcessingType.SORT;
        rule.sortExpression = expression;
        return rule;
    }
    
    public static PostProcessingRule group(String expression) {
        PostProcessingRule rule = new PostProcessingRule();
        rule.type = PostProcessingType.GROUP;
        rule.groupExpression = expression;
        return rule;
    }
    
    public static PostProcessingRule aggregate(String expression) {
        PostProcessingRule rule = new PostProcessingRule();
        rule.type = PostProcessingType.AGGREGATE;
        rule.aggregateExpression = expression;
        return rule;
    }
    
    // Getters
    public PostProcessingType getType() { return type; }
    public String getFilterExpression() { return filterExpression; }
    public String getSortExpression() { return sortExpression; }
    public String getGroupExpression() { return groupExpression; }
    public String getAggregateExpression() { return aggregateExpression; }
}

/**
 * 后处理类型枚举
 */
enum PostProcessingType {
    FILTER, SORT, GROUP, AGGREGATE
}
