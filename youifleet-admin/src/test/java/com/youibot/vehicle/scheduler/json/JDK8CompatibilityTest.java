package com.youibot.vehicle.scheduler.json;

import java.util.*;

/**
 * JDK8兼容性测试
 * 验证所有JSON处理相关代码在JDK8环境下的兼容性
 */
public class JDK8CompatibilityTest {
    
    public static void main(String[] args) {
        System.out.println("=== JDK8兼容性测试 ===\n");
        
        try {
            // 测试1: 字符串重复功能
            testStringRepeat();
            
            // 测试2: Map初始化
            testMapInitialization();
            
            // 测试3: 流式处理
            testStreamProcessing();
            
            // 测试4: 技术选型工具核心功能
            testTechnologySelectionCore();
            
            System.out.println("\n✅ 所有JDK8兼容性测试通过！");
            
        } catch (Exception e) {
            System.err.println("\n❌ JDK8兼容性测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试字符串重复功能
     */
    private static void testStringRepeat() {
        System.out.println("1. 测试字符串重复功能...");
        
        String repeated = repeatString("=", 10);
        System.out.println("重复结果: " + repeated);
        
        if (repeated.length() == 10 && repeated.equals("==========")) {
            System.out.println("   ✓ 字符串重复功能正常");
        } else {
            throw new RuntimeException("字符串重复功能异常");
        }
    }
    
    /**
     * 测试Map初始化
     */
    private static void testMapInitialization() {
        System.out.println("\n2. 测试Map初始化...");
        
        // JDK8兼容的Map初始化
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("name", "Alice");
        variables.put("age", 30);
        variables.put("active", true);
        
        System.out.println("Map内容: " + variables);
        
        if (variables.size() == 3 && "Alice".equals(variables.get("name"))) {
            System.out.println("   ✓ Map初始化功能正常");
        } else {
            throw new RuntimeException("Map初始化功能异常");
        }
    }
    
    /**
     * 测试流式处理
     */
    private static void testStreamProcessing() {
        System.out.println("\n3. 测试流式处理...");
        
        List<String> names = Arrays.asList("Alice", "Bob", "Charlie", "Diana");
        
        // JDK8流式处理
        List<String> filteredNames = new ArrayList<String>();
        for (String name : names) {
            if (name.length() > 3) {
                filteredNames.add(name.toUpperCase());
            }
        }
        
        System.out.println("过滤结果: " + filteredNames);
        
        if (filteredNames.size() == 3 && filteredNames.contains("ALICE")) {
            System.out.println("   ✓ 流式处理功能正常");
        } else {
            throw new RuntimeException("流式处理功能异常");
        }
    }
    
    /**
     * 测试技术选型工具核心功能
     */
    private static void testTechnologySelectionCore() {
        System.out.println("\n4. 测试技术选型工具核心功能...");
        
        // 模拟项目需求
        ProjectRequirements req = new ProjectRequirements();
        req.projectType = 1; // 新项目
        req.complexity = 3; // 复杂转换
        req.performance = 3; // 较高性能要求
        req.teamSkill = 3; // 高级团队
        req.maintenancePeriod = 3; // 长期维护
        req.dataVolume = 2; // 中等数据
        req.realTime = false;
        req.existingJsonPath = 1; // 无现有JsonPath
        req.useCase = 3; // 数据转换
        
        // 计算评分
        int jmesScore = calculateJMESPathScore(req);
        int jsonPathScore = calculateJsonPathScore(req);
        int jacksonScore = calculateJacksonScore(req);
        
        System.out.println("JMESPath评分: " + jmesScore);
        System.out.println("JsonPath评分: " + jsonPathScore);
        System.out.println("Jackson评分: " + jacksonScore);
        
        if (jmesScore > 0 && jsonPathScore > 0 && jacksonScore > 0) {
            System.out.println("   ✓ 技术选型评分功能正常");
        } else {
            throw new RuntimeException("技术选型评分功能异常");
        }
    }
    
    /**
     * JDK8兼容的字符串重复方法
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * JMESPath评分计算 (简化版)
     */
    private static int calculateJMESPathScore(ProjectRequirements req) {
        int score = 60; // 基础分
        
        // 复杂度加分
        if (req.complexity >= 2) score += 15;
        if (req.complexity >= 3) score += 10;
        
        // 新项目加分
        if (req.projectType == 1) score += 10;
        
        // 团队技能
        if (req.teamSkill >= 3) score += 10;
        else if (req.teamSkill <= 2) score -= 5;
        
        // 维护周期
        if (req.maintenancePeriod >= 3) score += 8;
        
        // 使用场景
        if (req.useCase == 3 || req.useCase == 5) score += 15; // 数据转换或规则引擎
        if (req.useCase == 4) score += 10; // 报表生成
        
        // 现有JsonPath使用情况
        if (req.existingJsonPath == 3) score -= 10; // 大量使用JsonPath时迁移成本高
        
        return Math.min(100, Math.max(0, score));
    }
    
    /**
     * JsonPath评分计算 (简化版)
     */
    private static int calculateJsonPathScore(ProjectRequirements req) {
        int score = 70; // 基础分
        
        // 简单查询加分
        if (req.complexity == 1) score += 20;
        if (req.complexity >= 3) score -= 15;
        
        // 现有使用情况
        if (req.existingJsonPath >= 2) score += 20;
        
        // 团队技能
        if (req.teamSkill <= 2) score += 10; // 学习成本低
        
        // 项目类型
        if (req.projectType == 2) score += 10; // 现有项目扩展
        
        // 使用场景
        if (req.useCase == 1 || req.useCase == 2) score += 10; // API或配置处理
        
        return Math.min(100, Math.max(0, score));
    }
    
    /**
     * Jackson评分计算 (简化版)
     */
    private static int calculateJacksonScore(ProjectRequirements req) {
        int score = 65; // 基础分
        
        // 性能要求
        if (req.performance >= 3) score += 20;
        if (req.performance == 4) score += 10;
        
        // 数据量
        if (req.dataVolume >= 3) score += 15;
        
        // 实时处理
        if (req.realTime) score += 15;
        
        // 维护周期
        if (req.maintenancePeriod >= 3) score += 10;
        
        // 团队技能
        if (req.teamSkill >= 3) score += 10;
        else if (req.teamSkill <= 2) score -= 10;
        
        // 复杂度
        if (req.complexity >= 3) score -= 5; // 代码会比较冗长
        
        return Math.min(100, Math.max(0, score));
    }
    
    // 简化的项目需求类
    static class ProjectRequirements {
        int projectType;
        int complexity;
        int performance;
        int teamSkill;
        int maintenancePeriod;
        int dataVolume;
        boolean realTime;
        int existingJsonPath;
        int useCase;
    }
}
