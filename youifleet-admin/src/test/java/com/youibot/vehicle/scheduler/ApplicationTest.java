package com.youibot.vehicle.scheduler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.youibot.vehicle.scheduler.modules.language.service.I18nMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ApplicationTest {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private I18nMessageService i18nMessageService;

    @Test
    public void test() throws IOException {
        String format = DateUtil.format(DateUtil.date(), "HH:mm");
        LOGGER.debug(format);
    }

}
