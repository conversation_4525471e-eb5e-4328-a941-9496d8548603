<?xml version="1.0" encoding="UTF-8"?>
<!-- 
JMESPath 变量绑定项目依赖配置
将以下依赖添加到你的 pom.xml 文件中
-->

<dependencies>
    <!-- JMESPath Java 实现 -->
    <dependency>
        <groupId>io.burt</groupId>
        <artifactId>jmespath-jackson</artifactId>
        <version>0.6.0</version>
    </dependency>
    
    <!-- <PERSON> JSON 处理库 -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>2.15.2</version>
    </dependency>
    
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.15.2</version>
    </dependency>
    
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>2.15.2</version>
    </dependency>
    
    <!-- JUnit 5 测试框架 (用于测试) -->
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>5.9.3</version>
        <scope>test</scope>
    </dependency>
    
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>5.9.3</version>
        <scope>test</scope>
    </dependency>
    
    <!-- 如果你的项目已经使用了 Alibaba FastJSON，可以添加这个适配器 -->
    <!-- 
    <dependency>
        <groupId>io.burt</groupId>
        <artifactId>jmespath-core</artifactId>
        <version>0.6.0</version>
    </dependency>
    -->
</dependencies>

<!-- 
使用说明：

1. 基本用法：
   JMESPathVariableUtils.executeWithVariables(jsonData, expression, variables)

2. 构建器模式：
   JMESPathVariableUtils.builder()
       .expression("users[?department == ${dept}].name")
       .variable("dept", "Engineering")
       .execute(jsonData)

3. 变量提取：
   Map<String, String> paths = Map.of("dept", "config.department");
   Map<String, Object> vars = JMESPathVariableUtils.extractVariables(jsonData, paths);

4. 简单模板替换：
   String result = JMESPathVariableUtils.simpleVariableReplace(
       "Hello ${name}", Map.of("name", "World")
   );

5. 表达式验证：
   boolean valid = JMESPathVariableUtils.isValidExpression("users[0].name");

变量绑定语法：
- 在表达式中使用 ${variableName} 格式
- 支持字符串、数字、布尔值和复杂对象变量
- 自动转换为 JMESPath 的 let 表达式语法

示例表达式：
- "users[?age >= ${minAge}].name"
- "products[?price >= ${minPrice} && category == ${category}]"
- "orders[?status == ${status}].{id: id, amount: amount}"

注意事项：
1. 变量名必须是有效的标识符（字母、数字、下划线）
2. 字符串变量会自动添加引号并转义
3. 复杂对象会序列化为JSON格式
4. 表达式语法必须符合JMESPath规范
-->
