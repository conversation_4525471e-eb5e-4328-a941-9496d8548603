package com.youibot.vehicle.scheduler.jmespath;

import io.burt.jmespath.Expression;
import io.burt.jmespath.JmesPath;
import io.burt.jmespath.jackson.JacksonRuntime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.Option;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JMESPath vs JsonPath 性能基准测试
 * 
 * 测试场景：
 * 1. 简单路径查询
 * 2. 复杂过滤查询
 * 3. 数据投影
 * 4. 聚合计算
 * 5. 重复查询缓存效果
 */
public class PerformanceBenchmark {
    
    private static final JmesPath<JsonNode> jmesPath = new JacksonRuntime();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Configuration jsonPathConfig = Configuration.defaultConfiguration()
        .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL);
    
    // 表达式缓存
    private static final Map<String, Expression<JsonNode>> jmesExpressionCache = new ConcurrentHashMap<>();
    
    public static void main(String[] args) throws Exception {
        System.out.println("=== JMESPath vs JsonPath 性能基准测试 ===\n");
        
        // 生成测试数据
        String testData = generateTestData(10000);
        System.out.println("测试数据: 10,000条记录\n");
        
        // 预热JVM
        warmUp(testData);
        
        // 1. 简单路径查询测试
        testSimplePathQuery(testData);
        
        // 2. 过滤查询测试
        testFilterQuery(testData);
        
        // 3. 数据投影测试
        testProjectionQuery(testData);
        
        // 4. 聚合计算测试
        testAggregationQuery(testData);
        
        // 5. 缓存效果测试
        testCacheEffect(testData);
        
        // 6. 内存使用测试
        testMemoryUsage(testData);
    }
    
    /**
     * 生成测试数据
     */
    private static String generateTestData(int count) {
        StringBuilder json = new StringBuilder();
        json.append("{\"employees\": [");
        
        for (int i = 0; i < count; i++) {
            if (i > 0) json.append(",");
            json.append("{")
                .append("\"id\": ").append(i).append(",")
                .append("\"name\": \"Employee").append(i).append("\",")
                .append("\"age\": ").append(20 + (i % 40)).append(",")
                .append("\"salary\": ").append(3000 + (i % 5000)).append(",")
                .append("\"department\": \"").append(getDepartment(i)).append("\",")
                .append("\"performance\": ").append(1 + (i % 5)).append(",")
                .append("\"active\": ").append(i % 10 != 0 ? "true" : "false")
                .append("}");
        }
        
        json.append("]}");
        return json.toString();
    }
    
    private static String getDepartment(int i) {
        String[] depts = {"IT", "HR", "Finance", "Marketing", "Operations"};
        return depts[i % depts.length];
    }
    
    /**
     * JVM预热
     */
    private static void warmUp(String testData) throws Exception {
        System.out.println("JVM预热中...");
        for (int i = 0; i < 100; i++) {
            // JMESPath预热
            executeJMESPath(testData, "employees[0].name");
            // JsonPath预热
            JsonPath.read(testData, "$.employees[0].name");
        }
        System.out.println("预热完成\n");
    }
    
    /**
     * 1. 简单路径查询测试
     */
    private static void testSimplePathQuery(String testData) throws Exception {
        System.out.println("=== 1. 简单路径查询测试 ===");
        
        int iterations = 1000;
        
        // JMESPath测试
        long jmesStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeJMESPath(testData, "employees[0].name");
        }
        long jmesTime = System.nanoTime() - jmesStart;
        
        // JsonPath测试
        long jsonStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            JsonPath.read(testData, "$.employees[0].name");
        }
        long jsonTime = System.nanoTime() - jsonStart;
        
        System.out.println("简单路径查询 (执行" + iterations + "次):");
        System.out.println("JMESPath: " + (jmesTime / 1_000_000) + "ms");
        System.out.println("JsonPath:  " + (jsonTime / 1_000_000) + "ms");
        System.out.println("性能比: " + String.format("%.2f", (double) jsonTime / jmesTime) + ":1\n");
    }
    
    /**
     * 2. 过滤查询测试
     */
    private static void testFilterQuery(String testData) throws Exception {
        System.out.println("=== 2. 过滤查询测试 ===");
        
        int iterations = 500;
        
        // JMESPath测试
        long jmesStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeJMESPath(testData, "employees[?age > `30` && department == 'IT'].name");
        }
        long jmesTime = System.nanoTime() - jmesStart;
        
        // JsonPath测试
        long jsonStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            JsonPath.read(testData, "$.employees[?(@.age > 30 && @.department == 'IT')].name");
        }
        long jsonTime = System.nanoTime() - jsonStart;
        
        System.out.println("过滤查询 (执行" + iterations + "次):");
        System.out.println("JMESPath: " + (jmesTime / 1_000_000) + "ms");
        System.out.println("JsonPath:  " + (jsonTime / 1_000_000) + "ms");
        System.out.println("性能比: " + String.format("%.2f", (double) jsonTime / jmesTime) + ":1\n");
    }
    
    /**
     * 3. 数据投影测试
     */
    private static void testProjectionQuery(String testData) throws Exception {
        System.out.println("=== 3. 数据投影测试 ===");
        
        int iterations = 200;
        
        // JMESPath测试 - 复杂投影
        long jmesStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeJMESPath(testData,
                "employees[?department == 'IT'].{name: name, salary: salary, isSenior: salary > `5000`}");
        }
        long jmesTime = System.nanoTime() - jmesStart;
        
        // JsonPath测试 - 基本字段选择
        long jsonStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            List<Map<String, Object>> employees = JsonPath.read(testData, "$.employees[?(@.department == 'IT')]");
            // 注意：JsonPath需要额外的Java代码来实现复杂投影
        }
        long jsonTime = System.nanoTime() - jsonStart;
        
        System.out.println("数据投影 (执行" + iterations + "次):");
        System.out.println("JMESPath: " + (jmesTime / 1_000_000) + "ms (包含复杂投影)");
        System.out.println("JsonPath:  " + (jsonTime / 1_000_000) + "ms (仅基本查询，不含投影处理)");
        System.out.println("注意: JsonPath需要额外Java代码实现复杂投影\n");
    }
    
    /**
     * 4. 聚合计算测试
     */
    private static void testAggregationQuery(String testData) throws Exception {
        System.out.println("=== 4. 聚合计算测试 ===");
        
        int iterations = 100;
        
        // JMESPath测试 - 内置聚合
        long jmesStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeJMESPath(testData, "sum(employees[?department == 'IT'].salary)");
        }
        long jmesTime = System.nanoTime() - jmesStart;
        
        // JsonPath测试 - 需要Java代码计算
        long jsonStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            List<Integer> salaries = JsonPath.read(testData, "$.employees[?(@.department == 'IT')].salary");
            // Java代码计算总和
            int sum = salaries.stream().mapToInt(Integer::intValue).sum();
        }
        long jsonTime = System.nanoTime() - jsonStart;
        
        System.out.println("聚合计算 (执行" + iterations + "次):");
        System.out.println("JMESPath: " + (jmesTime / 1_000_000) + "ms (内置sum函数)");
        System.out.println("JsonPath:  " + (jsonTime / 1_000_000) + "ms (查询+Java计算)");
        System.out.println("性能比: " + String.format("%.2f", (double) jsonTime / jmesTime) + ":1\n");
    }
    
    /**
     * 5. 缓存效果测试
     */
    private static void testCacheEffect(String testData) throws Exception {
        System.out.println("=== 5. 缓存效果测试 ===");
        
        int iterations = 1000;
        String expression = "employees[?age > `30` && salary > `5000`].{name: name, department: department}";
        
        // JMESPath - 无缓存
        long jmesNoCacheStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeJMESPath(testData, expression);
        }
        long jmesNoCacheTime = System.nanoTime() - jmesNoCacheStart;
        
        // JMESPath - 有缓存
        long jmesCacheStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeJMESPathCached(testData, expression);
        }
        long jmesCacheTime = System.nanoTime() - jmesCacheStart;
        
        // JsonPath - 每次解析
        long jsonStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            JsonPath.read(testData, "$.employees[?(@.age > 30 && @.salary > 5000)]");
        }
        long jsonTime = System.nanoTime() - jsonStart;
        
        System.out.println("缓存效果测试 (执行" + iterations + "次):");
        System.out.println("JMESPath (无缓存): " + (jmesNoCacheTime / 1_000_000) + "ms");
        System.out.println("JMESPath (有缓存): " + (jmesCacheTime / 1_000_000) + "ms");
        System.out.println("JsonPath (解释型): " + (jsonTime / 1_000_000) + "ms");
        System.out.println("缓存提升: " + String.format("%.2f", (double) jmesNoCacheTime / jmesCacheTime) + "倍\n");
    }
    
    /**
     * 6. 内存使用测试
     */
    private static void testMemoryUsage(String testData) throws Exception {
        System.out.println("=== 6. 内存使用测试 ===");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 测试前内存
        System.gc();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 编译大量JMESPath表达式
        for (int i = 0; i < 1000; i++) {
            String expr = "employees[?age > `" + (20 + i % 40) + "`].name";
            jmesExpressionCache.put(expr, jmesPath.compile(expr));
        }
        
        // 测试后内存
        System.gc();
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        
        System.out.println("编译1000个JMESPath表达式:");
        System.out.println("内存增加: " + ((afterMemory - beforeMemory) / 1024 / 1024) + "MB");
        System.out.println("平均每个表达式: " + ((afterMemory - beforeMemory) / 1000 / 1024) + "KB");
        System.out.println("缓存大小: " + jmesExpressionCache.size() + "个表达式\n");
    }
    
    // 工具方法
    private static JsonNode executeJMESPath(String jsonData, String expression) throws Exception {
        JsonNode data = objectMapper.readTree(jsonData);
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        return compiledExpression.search(data);
    }
    
    private static JsonNode executeJMESPathCached(String jsonData, String expression) throws Exception {
        JsonNode data = objectMapper.readTree(jsonData);
        Expression<JsonNode> compiledExpression = jmesExpressionCache.computeIfAbsent(
            expression, jmesPath::compile);
        return compiledExpression.search(data);
    }
}
