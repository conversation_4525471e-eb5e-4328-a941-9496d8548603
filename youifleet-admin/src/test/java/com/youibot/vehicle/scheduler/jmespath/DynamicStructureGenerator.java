package com.youibot.vehicle.scheduler.jmespath;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 动态数据结构生成器
 * 支持基于模板的复杂数据结构生成，包括循环、条件和递归结构
 */
public class DynamicStructureGenerator {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final AdvancedJMESPathEngine engine;
    
    // 模板指令模式
    private final Pattern forEachPattern = Pattern.compile("\\$forEach\\{([^}]+)\\}");
    private final Pattern ifPattern = Pattern.compile("\\$if\\{([^}]+)\\}");
    private final Pattern includePattern = Pattern.compile("\\$include\\{([^}]+)\\}");
    
    public DynamicStructureGenerator(AdvancedJMESPathEngine engine) {
        this.engine = engine;
    }
    
    /**
     * 生成动态数据结构
     * 
     * @param sourceData 源数据
     * @param template 结构模板
     * @return 生成的数据结构
     */
    public JsonNode generate(JsonNode sourceData, StructureTemplate template) {
        if (template == null) {
            return sourceData;
        }
        
        try {
            // 设置模板变量到上下文
            VariableContext context = engine.getVariableContext();
            context.pushScope(template.getTemplateVariables());
            
            JsonNode result;
            if (template.isArray()) {
                result = generateArrayStructure(sourceData, template);
            } else {
                result = generateObjectStructure(sourceData, template);
            }
            
            context.popScope();
            return result;
            
        } catch (Exception e) {
            throw new RuntimeException("动态结构生成失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成对象结构
     */
    private JsonNode generateObjectStructure(JsonNode sourceData, StructureTemplate template) throws Exception {
        String templateExpression = template.getTemplateExpression();
        
        // 解析模板表达式
        String resolvedTemplate = engine.getVariableContext().resolveVariables(templateExpression);
        
        // 处理模板指令
        resolvedTemplate = processTemplateDirectives(sourceData, resolvedTemplate);
        
        // 解析为JSON结构
        return objectMapper.readTree(resolvedTemplate);
    }
    
    /**
     * 生成数组结构
     */
    private JsonNode generateArrayStructure(JsonNode sourceData, StructureTemplate template) throws Exception {
        String itemTemplate = template.getArrayItemTemplate();
        ArrayNode result = objectMapper.createArrayNode();
        
        // 如果源数据是数组，为每个元素生成结构
        if (sourceData.isArray()) {
            for (JsonNode item : sourceData) {
                JsonNode generatedItem = generateItemStructure(item, itemTemplate);
                if (generatedItem != null) {
                    result.add(generatedItem);
                }
            }
        } else {
            // 单个对象也生成为数组
            JsonNode generatedItem = generateItemStructure(sourceData, itemTemplate);
            if (generatedItem != null) {
                result.add(generatedItem);
            }
        }
        
        return result;
    }
    
    /**
     * 生成单个项目结构
     */
    private JsonNode generateItemStructure(JsonNode itemData, String itemTemplate) throws Exception {
        // 为当前项目创建新的变量作用域
        VariableContext context = engine.getVariableContext();
        context.pushScope();
        context.setVariable("item", itemData);
        context.setVariable("index", getCurrentIndex());
        
        try {
            String resolvedTemplate = context.resolveVariables(itemTemplate);
            resolvedTemplate = processTemplateDirectives(itemData, resolvedTemplate);
            return objectMapper.readTree(resolvedTemplate);
        } finally {
            context.popScope();
        }
    }
    
    /**
     * 处理模板指令
     */
    private String processTemplateDirectives(JsonNode sourceData, String template) {
        String result = template;
        
        // 处理forEach指令
        result = processForEachDirectives(sourceData, result);
        
        // 处理if指令
        result = processIfDirectives(sourceData, result);
        
        // 处理include指令
        result = processIncludeDirectives(sourceData, result);
        
        return result;
    }
    
    /**
     * 处理forEach指令
     */
    private String processForEachDirectives(JsonNode sourceData, String template) {
        Matcher matcher = forEachPattern.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String forEachExpression = matcher.group(1);
            String replacement = executeForEach(sourceData, forEachExpression);
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 执行forEach循环
     */
    private String executeForEach(JsonNode sourceData, String expression) {
        try {
            // 解析forEach表达式: array as item with template
            String[] parts = expression.split(" as ");
            if (parts.length != 2) {
                return "";
            }
            
            String arrayExpression = parts[0].trim();
            String[] itemParts = parts[1].split(" with ");
            if (itemParts.length != 2) {
                return "";
            }
            
            String itemVariable = itemParts[0].trim();
            String itemTemplate = itemParts[1].trim();
            
            // 获取数组数据
            JsonNode arrayData = executeJMESPathExpression(sourceData, arrayExpression);
            if (!arrayData.isArray()) {
                return "";
            }
            
            // 生成每个项目
            List<String> items = new ArrayList<>();
            VariableContext context = engine.getVariableContext();
            
            for (int i = 0; i < arrayData.size(); i++) {
                JsonNode item = arrayData.get(i);
                context.pushScope();
                context.setVariable(itemVariable, item);
                context.setVariable("index", i);
                context.setVariable("isFirst", i == 0);
                context.setVariable("isLast", i == arrayData.size() - 1);
                
                try {
                    String resolvedItemTemplate = context.resolveVariables(itemTemplate);
                    items.add(resolvedItemTemplate);
                } finally {
                    context.popScope();
                }
            }
            
            return String.join(",", items);
            
        } catch (Exception e) {
            return "";
        }
    }
    
    /**
     * 处理if指令
     */
    private String processIfDirectives(JsonNode sourceData, String template) {
        Matcher matcher = ifPattern.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String ifExpression = matcher.group(1);
            String replacement = executeIf(sourceData, ifExpression);
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 执行if条件
     */
    private String executeIf(JsonNode sourceData, String expression) {
        try {
            // 解析if表达式: condition then trueValue else falseValue
            String[] thenParts = expression.split(" then ");
            if (thenParts.length != 2) {
                return "";
            }
            
            String condition = thenParts[0].trim();
            String[] elseParts = thenParts[1].split(" else ");
            String trueValue = elseParts[0].trim();
            String falseValue = elseParts.length > 1 ? elseParts[1].trim() : "";
            
            // 评估条件
            boolean conditionResult = evaluateCondition(sourceData, condition);
            
            // 返回相应的值
            String selectedValue = conditionResult ? trueValue : falseValue;
            return engine.getVariableContext().resolveVariables(selectedValue);
            
        } catch (Exception e) {
            return "";
        }
    }
    
    /**
     * 处理include指令
     */
    private String processIncludeDirectives(JsonNode sourceData, String template) {
        Matcher matcher = includePattern.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String includeExpression = matcher.group(1);
            String replacement = executeInclude(sourceData, includeExpression);
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 执行include指令
     */
    private String executeInclude(JsonNode sourceData, String expression) {
        try {
            // include指令可以包含其他模板或执行JMESPath表达式
            JsonNode includeResult = executeJMESPathExpression(sourceData, expression);
            return includeResult.toString();
        } catch (Exception e) {
            return "";
        }
    }
    
    /**
     * 执行JMESPath表达式
     */
    private JsonNode executeJMESPathExpression(JsonNode sourceData, String expression) throws Exception {
        String resolvedExpression = engine.getVariableContext().resolveVariables(expression);
        return engine.getOrCompileExpression(resolvedExpression).search(sourceData);
    }
    
    /**
     * 评估条件表达式
     */
    private boolean evaluateCondition(JsonNode sourceData, String condition) {
        try {
            // 简单的条件评估
            if (condition.contains("==")) {
                String[] parts = condition.split("==");
                if (parts.length == 2) {
                    String left = engine.getVariableContext().resolveVariables(parts[0].trim());
                    String right = engine.getVariableContext().resolveVariables(parts[1].trim());
                    return Objects.equals(left, right);
                }
            } else if (condition.contains("!=")) {
                String[] parts = condition.split("!=");
                if (parts.length == 2) {
                    String left = engine.getVariableContext().resolveVariables(parts[0].trim());
                    String right = engine.getVariableContext().resolveVariables(parts[1].trim());
                    return !Objects.equals(left, right);
                }
            } else if (condition.contains(">")) {
                String[] parts = condition.split(">");
                if (parts.length == 2) {
                    double left = Double.parseDouble(engine.getVariableContext().resolveVariables(parts[0].trim()));
                    double right = Double.parseDouble(engine.getVariableContext().resolveVariables(parts[1].trim()));
                    return left > right;
                }
            } else if (condition.contains("<")) {
                String[] parts = condition.split("<");
                if (parts.length == 2) {
                    double left = Double.parseDouble(engine.getVariableContext().resolveVariables(parts[0].trim()));
                    double right = Double.parseDouble(engine.getVariableContext().resolveVariables(parts[1].trim()));
                    return left < right;
                }
            } else {
                // 简单的布尔值或存在性检查
                String resolvedCondition = engine.getVariableContext().resolveVariables(condition);
                if ("true".equalsIgnoreCase(resolvedCondition)) return true;
                if ("false".equalsIgnoreCase(resolvedCondition)) return false;
                
                // 检查JMESPath表达式结果
                JsonNode result = executeJMESPathExpression(sourceData, condition);
                return result != null && !result.isNull() && 
                       (!result.isTextual() || !result.asText().isEmpty()) &&
                       (!result.isArray() || result.size() > 0);
            }
        } catch (Exception e) {
            // 条件评估失败时返回false
        }
        
        return false;
    }
    
    /**
     * 获取当前索引（用于数组生成）
     */
    private int getCurrentIndex() {
        Object index = engine.getVariableContext().getVariable("index");
        return index instanceof Integer ? (Integer) index : 0;
    }
    
    /**
     * 创建结构模板构建器
     */
    public static StructureTemplateBuilder builder() {
        return new StructureTemplateBuilder();
    }
    
    /**
     * 结构模板构建器
     */
    public static class StructureTemplateBuilder {
        private String templateExpression;
        private Map<String, Object> templateVariables = new HashMap<>();
        private boolean isArray = false;
        private String arrayItemTemplate;
        
        public StructureTemplateBuilder object(String templateExpression) {
            this.templateExpression = templateExpression;
            this.isArray = false;
            return this;
        }
        
        public StructureTemplateBuilder array(String itemTemplate) {
            this.arrayItemTemplate = itemTemplate;
            this.isArray = true;
            return this;
        }
        
        public StructureTemplateBuilder variable(String name, Object value) {
            this.templateVariables.put(name, value);
            return this;
        }
        
        public StructureTemplateBuilder variables(Map<String, Object> variables) {
            this.templateVariables.putAll(variables);
            return this;
        }
        
        public StructureTemplate build() {
            StructureTemplate template = new StructureTemplate();
            template.templateExpression = this.templateExpression;
            template.templateVariables = this.templateVariables;
            template.isArray = this.isArray;
            template.arrayItemTemplate = this.arrayItemTemplate;
            return template;
        }
    }
}
