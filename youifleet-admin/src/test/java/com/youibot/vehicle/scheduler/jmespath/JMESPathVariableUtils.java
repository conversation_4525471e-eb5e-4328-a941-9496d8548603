package com.youibot.vehicle.scheduler.jmespath;

import io.burt.jmespath.Expression;
import io.burt.jmespath.JmesPath;
import io.burt.jmespath.jackson.JacksonRuntime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import java.util.Map;
import java.util.HashMap;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * JMESPath 变量绑定工具类
 * 提供便捷的变量绑定和表达式执行功能
 */
public class JMESPathVariableUtils {
    
    private static final JmesPath<JsonNode> jmesPath = new JacksonRuntime();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 变量占位符正则表达式 ${variableName}
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    
    /**
     * 执行带变量绑定的JMESPath表达式
     * 
     * @param jsonData JSON数据字符串
     * @param expression JMESPath表达式，支持 ${variableName} 格式的变量
     * @param variables 变量映射
     * @return 执行结果的JSON字符串
     */
    public static String executeWithVariables(String jsonData, String expression, Map<String, Object> variables) {
        try {
            JsonNode data = objectMapper.readTree(jsonData);
            JsonNode result = executeWithVariables(data, expression, variables);
            return result.toString();
        } catch (Exception e) {
            throw new RuntimeException("执行JMESPath表达式失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行带变量绑定的JMESPath表达式
     *
     * @param data JSON数据节点
     * @param expression JMESPath表达式，支持 ${variableName} 格式的变量
     * @param variables 变量映射
     * @return 执行结果的JSON节点
     */
    public static JsonNode executeWithVariables(JsonNode data, String expression, Map<String, Object> variables) {
        try {
            // 使用变量替换方法（JMESPath不支持let表达式）
            String resolvedExpression = replaceVariables(expression, variables);
            Expression<JsonNode> compiledExpression = jmesPath.compile(resolvedExpression);
            return compiledExpression.search(data);
        } catch (Exception e) {
            throw new RuntimeException("执行JMESPath表达式失败: " + e.getMessage(), e);
        }
    }

    /**
     * 替换表达式中的变量
     *
     * @param expression 原始表达式
     * @param variables 变量映射
     * @return 替换后的表达式
     */
    private static String replaceVariables(String expression, Map<String, Object> variables) {
        if (expression == null || variables == null || variables.isEmpty()) {
            return expression;
        }

        String result = expression;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            Object value = entry.getValue();
            String replacement;

            if (value instanceof String) {
                replacement = "'" + value.toString().replace("'", "\\'") + "'";
            } else if (value instanceof Number) {
                replacement = "`" + value.toString() + "`";
            } else if (value instanceof Boolean) {
                replacement = "`" + value.toString() + "`";
            } else {
                replacement = "'" + (value != null ? value.toString() : "") + "'";
            }

            result = result.replace(placeholder, replacement);
        }

        return result;
    }
    
    /**
     * 创建变量映射的便捷方法
     * 
     * @return 新的变量映射
     */
    public static Map<String, Object> createVariables() {
        return new HashMap<>();
    }
    
    /**
     * 向变量映射中添加变量
     * 
     * @param variables 变量映射
     * @param name 变量名
     * @param value 变量值
     * @return 变量映射（支持链式调用）
     */
    public static Map<String, Object> addVariable(Map<String, Object> variables, String name, Object value) {
        variables.put(name, value);
        return variables;
    }
    
    /**
     * 执行简单的变量替换（不使用JMESPath，直接字符串替换）
     * 适用于简单的模板替换场景
     * 
     * @param template 模板字符串，包含 ${variableName} 占位符
     * @param variables 变量映射
     * @return 替换后的字符串
     */
    public static String simpleVariableReplace(String template, Map<String, Object> variables) {
        if (variables == null || variables.isEmpty()) {
            return template;
        }
        
        String result = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    /**
     * 验证JMESPath表达式语法
     * 
     * @param expression JMESPath表达式
     * @return 是否有效
     */
    public static boolean isValidExpression(String expression) {
        try {
            jmesPath.compile(expression);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从JSON数据中提取变量值
     * 
     * @param jsonData JSON数据
     * @param variablePaths 变量路径映射 (变量名 -> JMESPath路径)
     * @return 提取的变量映射
     */
    public static Map<String, Object> extractVariables(String jsonData, Map<String, String> variablePaths) {
        try {
            JsonNode data = objectMapper.readTree(jsonData);
            return extractVariables(data, variablePaths);
        } catch (Exception e) {
            throw new RuntimeException("提取变量失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从JSON数据中提取变量值
     * 
     * @param data JSON数据节点
     * @param variablePaths 变量路径映射 (变量名 -> JMESPath路径)
     * @return 提取的变量映射
     */
    public static Map<String, Object> extractVariables(JsonNode data, Map<String, String> variablePaths) {
        Map<String, Object> variables = new HashMap<>();
        
        for (Map.Entry<String, String> entry : variablePaths.entrySet()) {
            String varName = entry.getKey();
            String path = entry.getValue();
            
            try {
                Expression<JsonNode> expression = jmesPath.compile(path);
                JsonNode result = expression.search(data);
                
                if (result != null && !result.isNull()) {
                    if (result.isTextual()) {
                        variables.put(varName, result.asText());
                    } else if (result.isNumber()) {
                        if (result.isInt()) {
                            variables.put(varName, result.asInt());
                        } else {
                            variables.put(varName, result.asDouble());
                        }
                    } else if (result.isBoolean()) {
                        variables.put(varName, result.asBoolean());
                    } else {
                        variables.put(varName, result);
                    }
                }
            } catch (Exception e) {
                // 忽略无效路径，继续处理其他变量
                System.err.println("提取变量 " + varName + " 失败: " + e.getMessage());
            }
        }
        
        return variables;
    }
    
    /**
     * 构建器模式的变量绑定执行器
     */
    public static class VariableExpressionBuilder {
        private String expression;
        private Map<String, Object> variables = new HashMap<>();
        
        public VariableExpressionBuilder expression(String expression) {
            this.expression = expression;
            return this;
        }
        
        public VariableExpressionBuilder variable(String name, Object value) {
            this.variables.put(name, value);
            return this;
        }
        
        public VariableExpressionBuilder variables(Map<String, Object> variables) {
            this.variables.putAll(variables);
            return this;
        }
        
        public JsonNode execute(JsonNode data) {
            return JMESPathVariableUtils.executeWithVariables(data, expression, variables);
        }
        
        public String execute(String jsonData) {
            return JMESPathVariableUtils.executeWithVariables(jsonData, expression, variables);
        }
    }
    
    /**
     * 创建变量表达式构建器
     * 
     * @return 构建器实例
     */
    public static VariableExpressionBuilder builder() {
        return new VariableExpressionBuilder();
    }
}
