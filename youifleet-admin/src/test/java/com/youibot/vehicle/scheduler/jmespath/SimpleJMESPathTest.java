package com.youibot.vehicle.scheduler.jmespath;

import java.util.HashMap;
import java.util.Map;

/**
 * 简化的JMESPath测试
 * 用于验证JDK8兼容性修复
 */
public class SimpleJMESPathTest {
    
    public static void main(String[] args) {
        try {
            System.out.println("=== JMESPath JDK8兼容性测试 ===\n");
            
            // 测试1: 基本变量替换
            testBasicVariableReplacement();
            
            // 测试2: 数值变量
            testNumericVariables();
            
            // 测试3: 表达式验证
            testExpressionValidation();
            
            System.out.println("\n✓ 所有测试通过！JMESPath JDK8兼容性修复成功！");
            
        } catch (Exception e) {
            System.err.println("\n✗ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试基本变量替换
     */
    public static void testBasicVariableReplacement() throws Exception {
        System.out.println("1. 测试基本变量替换...");
        
        String jsonData = "{\n" +
            "    \"users\": [\n" +
            "        {\"name\": \"Alice\", \"department\": \"Engineering\"},\n" +
            "        {\"name\": \"Bob\", \"department\": \"Marketing\"}\n" +
            "    ]\n" +
            "}";
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("targetDept", "Engineering");
        
        String result = JMESPathVariableUtils.executeWithVariables(
            jsonData, 
            "users[?department == ${targetDept}].name", 
            variables
        );
        
        System.out.println("   结果: " + result);
        
        if (result.contains("Alice") && !result.contains("Bob")) {
            System.out.println("   ✓ 基本变量替换测试通过");
        } else {
            throw new RuntimeException("基本变量替换测试失败");
        }
    }
    
    /**
     * 测试数值变量
     */
    public static void testNumericVariables() throws Exception {
        System.out.println("\n2. 测试数值变量...");
        
        String jsonData = "{\n" +
            "    \"products\": [\n" +
            "        {\"name\": \"Laptop\", \"price\": 1000},\n" +
            "        {\"name\": \"Book\", \"price\": 20}\n" +
            "    ]\n" +
            "}";
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("minPrice", 500);
        
        String result = JMESPathVariableUtils.executeWithVariables(
            jsonData, 
            "products[?price >= ${minPrice}].name", 
            variables
        );
        
        System.out.println("   结果: " + result);
        
        if (result.contains("Laptop") && !result.contains("Book")) {
            System.out.println("   ✓ 数值变量测试通过");
        } else {
            throw new RuntimeException("数值变量测试失败");
        }
    }
    
    /**
     * 测试表达式验证
     */
    public static void testExpressionValidation() {
        System.out.println("\n3. 测试表达式验证...");
        
        // 测试有效表达式
        boolean valid1 = JMESPathVariableUtils.isValidExpression("users[0].name");
        boolean valid2 = JMESPathVariableUtils.isValidExpression("users[?age > `25`].name");
        boolean invalid = JMESPathVariableUtils.isValidExpression("users[invalid syntax");
        
        System.out.println("   users[0].name: " + (valid1 ? "有效" : "无效"));
        System.out.println("   users[?age > `25`].name: " + (valid2 ? "有效" : "无效"));
        System.out.println("   users[invalid syntax: " + (invalid ? "有效" : "无效"));
        
        if (valid1 && valid2 && !invalid) {
            System.out.println("   ✓ 表达式验证测试通过");
        } else {
            throw new RuntimeException("表达式验证测试失败");
        }
    }
}
