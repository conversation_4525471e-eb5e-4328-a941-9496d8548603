package com.youibot.vehicle.scheduler.jmespath;

import io.burt.jmespath.Expression;
import io.burt.jmespath.JmesPath;
import io.burt.jmespath.jackson.JacksonRuntime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 高级JMESPath引擎
 * 支持多字段映射、变量绑定和动态数据结构生成
 */
public class AdvancedJMESPathEngine {
    
    private static final JmesPath<JsonNode> jmesPath = new JacksonRuntime();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 表达式缓存
    private final Map<String, Expression<JsonNode>> expressionCache = new ConcurrentHashMap<>();
    
    // 变量上下文
    private final VariableContext variableContext;
    
    // 字段映射配置
    private final FieldMappingConfig fieldMappingConfig;
    
    // 动态结构生成器
    private final DynamicStructureGenerator structureGenerator;
    
    public AdvancedJMESPathEngine() {
        this.variableContext = new VariableContext();
        this.fieldMappingConfig = new FieldMappingConfig();
        this.structureGenerator = new DynamicStructureGenerator(this);
    }
    
    /**
     * 执行高级数据转换
     * 
     * @param sourceData 源数据
     * @param transformConfig 转换配置
     * @return 转换后的数据
     */
    public JsonNode transform(JsonNode sourceData, TransformConfig transformConfig) {
        try {
            // 1. 设置变量上下文
            variableContext.setVariables(transformConfig.getVariables());
            variableContext.setSourceData(sourceData);
            
            // 2. 执行字段映射
            JsonNode mappedData = executeFieldMapping(sourceData, transformConfig.getFieldMappings());
            
            // 3. 生成动态结构
            JsonNode structuredData = structureGenerator.generate(mappedData, transformConfig.getStructureTemplate());
            
            // 4. 应用后处理规则
            return applyPostProcessing(structuredData, transformConfig.getPostProcessingRules());
            
        } catch (Exception e) {
            throw new RuntimeException("数据转换失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行字段映射
     */
    private JsonNode executeFieldMapping(JsonNode sourceData, List<FieldMapping> fieldMappings) throws Exception {
        ObjectNode result = objectMapper.createObjectNode();
        
        for (FieldMapping mapping : fieldMappings) {
            Object mappedValue = executeSingleFieldMapping(sourceData, mapping);
            if (mappedValue != null) {
                setNestedValue(result, mapping.getTargetPath(), mappedValue);
            }
        }
        
        return result;
    }
    
    /**
     * 执行单个字段映射
     */
    private Object executeSingleFieldMapping(JsonNode sourceData, FieldMapping mapping) throws Exception {
        // 1. 解析源表达式中的变量
        String resolvedExpression = variableContext.resolveVariables(mapping.getSourceExpression());
        
        // 2. 执行JMESPath表达式
        Expression<JsonNode> expression = getOrCompileExpression(resolvedExpression);
        JsonNode result = expression.search(sourceData);
        
        if (result == null || result.isNull()) {
            return mapping.getDefaultValue();
        }
        
        // 3. 应用数据转换
        return applyDataTransformation(result, mapping.getTransformation());
    }
    
    /**
     * 应用数据转换
     */
    private Object applyDataTransformation(JsonNode value, DataTransformation transformation) {
        if (transformation == null) {
            return convertJsonNodeToObject(value);
        }
        
        switch (transformation.getType()) {
            case STRING_FORMAT:
                return String.format(transformation.getFormat(), convertJsonNodeToObject(value));
            case NUMBER_CALCULATION:
                return calculateNumber(value, transformation.getCalculation());
            case DATE_FORMAT:
                return formatDate(value, transformation.getDateFormat());
            case CONDITIONAL:
                return applyConditionalTransformation(value, transformation.getConditions());
            case CUSTOM_FUNCTION:
                return transformation.getCustomFunction().apply(value);
            default:
                return convertJsonNodeToObject(value);
        }
    }
    
    /**
     * 设置嵌套值
     */
    private void setNestedValue(ObjectNode target, String path, Object value) {
        String[] pathParts = path.split("\\.");
        ObjectNode current = target;
        
        for (int i = 0; i < pathParts.length - 1; i++) {
            String part = pathParts[i];
            if (!current.has(part)) {
                current.set(part, objectMapper.createObjectNode());
            }
            current = (ObjectNode) current.get(part);
        }
        
        String finalKey = pathParts[pathParts.length - 1];
        current.set(finalKey, objectMapper.valueToTree(value));
    }
    
    /**
     * 获取或编译表达式（带缓存）
     */
    Expression<JsonNode> getOrCompileExpression(String expression) {
        return expressionCache.computeIfAbsent(expression, jmesPath::compile);
    }
    
    /**
     * 应用后处理规则
     */
    private JsonNode applyPostProcessing(JsonNode data, List<PostProcessingRule> rules) {
        JsonNode result = data;
        
        for (PostProcessingRule rule : rules) {
            result = applyPostProcessingRule(result, rule);
        }
        
        return result;
    }
    
    /**
     * 应用单个后处理规则
     */
    private JsonNode applyPostProcessingRule(JsonNode data, PostProcessingRule rule) {
        switch (rule.getType()) {
            case FILTER:
                return filterData(data, rule.getFilterExpression());
            case SORT:
                return sortData(data, rule.getSortExpression());
            case GROUP:
                return groupData(data, rule.getGroupExpression());
            case AGGREGATE:
                return aggregateData(data, rule.getAggregateExpression());
            default:
                return data;
        }
    }
    
    // 辅助方法
    private Object convertJsonNodeToObject(JsonNode node) {
        if (node.isTextual()) return node.asText();
        if (node.isNumber()) return node.isInt() ? node.asInt() : node.asDouble();
        if (node.isBoolean()) return node.asBoolean();
        if (node.isArray() || node.isObject()) return node;
        return null;
    }
    
    private Object calculateNumber(JsonNode value, String calculation) {
        // 实现数值计算逻辑
        double numValue = value.asDouble();
        // 这里可以实现复杂的计算表达式解析
        return numValue;
    }
    
    private String formatDate(JsonNode value, String dateFormat) {
        // 实现日期格式化逻辑
        return value.asText();
    }
    
    private Object applyConditionalTransformation(JsonNode value, List<ConditionalRule> conditions) {
        // 实现条件转换逻辑
        for (ConditionalRule condition : conditions) {
            if (evaluateCondition(value, condition.getCondition())) {
                return condition.getValue();
            }
        }
        return convertJsonNodeToObject(value);
    }
    
    private boolean evaluateCondition(JsonNode value, String condition) {
        // 实现条件评估逻辑
        return true;
    }
    
    private JsonNode filterData(JsonNode data, String filterExpression) {
        try {
            String resolvedExpression = variableContext.resolveVariables(filterExpression);
            Expression<JsonNode> expression = getOrCompileExpression(resolvedExpression);
            return expression.search(data);
        } catch (Exception e) {
            return data;
        }
    }
    
    private JsonNode sortData(JsonNode data, String sortExpression) {
        // 实现排序逻辑
        return data;
    }
    
    private JsonNode groupData(JsonNode data, String groupExpression) {
        // 实现分组逻辑
        return data;
    }
    
    private JsonNode aggregateData(JsonNode data, String aggregateExpression) {
        // 实现聚合逻辑
        return data;
    }
    
    // Getter方法
    public VariableContext getVariableContext() {
        return variableContext;
    }
    
    public FieldMappingConfig getFieldMappingConfig() {
        return fieldMappingConfig;
    }
    
    public DynamicStructureGenerator getStructureGenerator() {
        return structureGenerator;
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        expressionCache.clear();
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", expressionCache.size());
        stats.put("cachedExpressions", new ArrayList<>(expressionCache.keySet()));
        return stats;
    }
}
