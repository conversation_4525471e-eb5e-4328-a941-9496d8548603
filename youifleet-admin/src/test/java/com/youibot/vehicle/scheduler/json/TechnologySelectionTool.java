package com.youibot.vehicle.scheduler.json;

import java.util.*;

/**
 * JSON处理技术选型决策工具
 * 
 * 通过回答一系列问题，帮助选择最适合的JSON处理技术
 */
public class TechnologySelectionTool {
    
    private static final Scanner scanner = new Scanner(System.in);
    
    public static void main(String[] args) {
        System.out.println("=== JSON处理技术选型决策工具 ===\n");
        System.out.println("请回答以下问题，我将为您推荐最适合的技术方案\n");
        
        // 收集需求信息
        ProjectRequirements requirements = collectRequirements();
        
        // 分析并推荐
        List<TechnologyRecommendation> recommendations = analyzeAndRecommend(requirements);
        
        // 输出推荐结果
        outputRecommendations(recommendations, requirements);
    }
    
    /**
     * 收集项目需求信息
     */
    private static ProjectRequirements collectRequirements() {
        ProjectRequirements req = new ProjectRequirements();
        
        System.out.println("1. 项目类型 (1-新项目, 2-现有项目扩展, 3-重构现有功能):");
        req.projectType = readInt(1, 3);
        
        System.out.println("\n2. 数据处理复杂度 (1-简单查询, 2-中等复杂, 3-复杂转换, 4-大数据分析):");
        req.complexity = readInt(1, 4);
        
        System.out.println("\n3. 性能要求 (1-不敏感, 2-一般, 3-较高, 4-极高):");
        req.performance = readInt(1, 4);
        
        System.out.println("\n4. 团队技能水平 (1-初级, 2-中级, 3-高级, 4-专家):");
        req.teamSkill = readInt(1, 4);
        
        System.out.println("\n5. 维护周期 (1-短期项目, 2-中期项目, 3-长期维护, 4-企业级系统):");
        req.maintenancePeriod = readInt(1, 4);
        
        System.out.println("\n6. 数据量级 (1-小数据, 2-中等数据, 3-大数据, 4-海量数据):");
        req.dataVolume = readInt(1, 4);
        
        System.out.println("\n7. 是否需要实时处理 (1-否, 2-是):");
        req.realTime = readInt(1, 2) == 2;
        
        System.out.println("\n8. 是否已使用JsonPath (1-否, 2-少量使用, 3-大量使用):");
        req.existingJsonPath = readInt(1, 3);
        
        System.out.println("\n9. 主要使用场景 (1-API数据处理, 2-配置文件处理, 3-数据转换, 4-报表生成, 5-规则引擎):");
        req.useCase = readInt(1, 5);
        
        return req;
    }
    
    /**
     * 分析需求并生成推荐
     */
    private static List<TechnologyRecommendation> analyzeAndRecommend(ProjectRequirements req) {
        List<TechnologyRecommendation> recommendations = new ArrayList<>();
        
        // JMESPath评分
        int jmesScore = calculateJMESPathScore(req);
        recommendations.add(new TechnologyRecommendation("JMESPath", jmesScore, getJMESPathReason(req)));
        
        // JsonPath评分
        int jsonPathScore = calculateJsonPathScore(req);
        recommendations.add(new TechnologyRecommendation("JsonPath", jsonPathScore, getJsonPathReason(req)));
        
        // Jackson评分
        int jacksonScore = calculateJacksonScore(req);
        recommendations.add(new TechnologyRecommendation("Jackson + Stream API", jacksonScore, getJacksonReason(req)));
        
        // JOLT评分
        int joltScore = calculateJOLTScore(req);
        recommendations.add(new TechnologyRecommendation("JOLT", joltScore, getJOLTReason(req)));
        
        // GraphQL评分
        int graphqlScore = calculateGraphQLScore(req);
        recommendations.add(new TechnologyRecommendation("GraphQL", graphqlScore, getGraphQLReason(req)));
        
        // 排序推荐
        recommendations.sort((a, b) -> Integer.compare(b.score, a.score));
        
        return recommendations;
    }
    
    /**
     * JMESPath评分计算
     */
    private static int calculateJMESPathScore(ProjectRequirements req) {
        int score = 60; // 基础分
        
        // 复杂度加分
        if (req.complexity >= 2) score += 15;
        if (req.complexity >= 3) score += 10;
        
        // 新项目加分
        if (req.projectType == 1) score += 10;
        
        // 团队技能
        if (req.teamSkill >= 3) score += 10;
        else if (req.teamSkill <= 2) score -= 5;
        
        // 维护周期
        if (req.maintenancePeriod >= 3) score += 8;
        
        // 使用场景
        if (req.useCase == 3 || req.useCase == 5) score += 15; // 数据转换或规则引擎
        if (req.useCase == 4) score += 10; // 报表生成
        
        // 现有JsonPath使用情况
        if (req.existingJsonPath == 3) score -= 10; // 大量使用JsonPath时迁移成本高
        
        return Math.min(100, Math.max(0, score));
    }
    
    /**
     * JsonPath评分计算
     */
    private static int calculateJsonPathScore(ProjectRequirements req) {
        int score = 70; // 基础分
        
        // 简单查询加分
        if (req.complexity == 1) score += 20;
        if (req.complexity >= 3) score -= 15;
        
        // 现有使用情况
        if (req.existingJsonPath >= 2) score += 20;
        
        // 团队技能
        if (req.teamSkill <= 2) score += 10; // 学习成本低
        
        // 项目类型
        if (req.projectType == 2) score += 10; // 现有项目扩展
        
        // 使用场景
        if (req.useCase == 1 || req.useCase == 2) score += 10; // API或配置处理
        
        return Math.min(100, Math.max(0, score));
    }
    
    /**
     * Jackson评分计算
     */
    private static int calculateJacksonScore(ProjectRequirements req) {
        int score = 65; // 基础分
        
        // 性能要求
        if (req.performance >= 3) score += 20;
        if (req.performance == 4) score += 10;
        
        // 数据量
        if (req.dataVolume >= 3) score += 15;
        
        // 实时处理
        if (req.realTime) score += 15;
        
        // 维护周期
        if (req.maintenancePeriod >= 3) score += 10;
        
        // 团队技能
        if (req.teamSkill >= 3) score += 10;
        else if (req.teamSkill <= 2) score -= 10;
        
        // 复杂度
        if (req.complexity >= 3) score -= 5; // 代码会比较冗长
        
        return Math.min(100, Math.max(0, score));
    }
    
    /**
     * JOLT评分计算
     */
    private static int calculateJOLTScore(ProjectRequirements req) {
        int score = 40; // 基础分
        
        // 数据转换场景
        if (req.useCase == 3) score += 30;
        
        // 复杂度
        if (req.complexity == 3) score += 20;
        if (req.complexity == 4) score += 10;
        
        // 团队技能
        if (req.teamSkill >= 3) score += 15;
        else score -= 10;
        
        // 维护周期
        if (req.maintenancePeriod >= 3) score += 10;
        
        return Math.min(100, Math.max(0, score));
    }
    
    /**
     * GraphQL评分计算
     */
    private static int calculateGraphQLScore(ProjectRequirements req) {
        int score = 30; // 基础分
        
        // API场景
        if (req.useCase == 1) score += 40;
        
        // 新项目
        if (req.projectType == 1) score += 15;
        
        // 团队技能
        if (req.teamSkill >= 3) score += 15;
        
        // 复杂度
        if (req.complexity >= 2) score += 10;
        
        return Math.min(100, Math.max(0, score));
    }
    
    /**
     * 输出推荐结果
     */
    private static void outputRecommendations(List<TechnologyRecommendation> recommendations, ProjectRequirements req) {
        System.out.println("\n" + repeatString("=", 60));
        System.out.println("📊 技术选型推荐结果");
        System.out.println(repeatString("=", 60));
        
        for (int i = 0; i < recommendations.size(); i++) {
            TechnologyRecommendation rec = recommendations.get(i);
            String rank = (i == 0) ? "🥇" : (i == 1) ? "🥈" : (i == 2) ? "🥉" : "📍";
            
            System.out.println(String.format("\n%s 第%d名: %s (评分: %d/100)", 
                rank, i + 1, rec.technology, rec.score));
            System.out.println("推荐理由: " + rec.reason);
            
            if (i == 0) {
                System.out.println("⭐ 强烈推荐使用此技术！");
            } else if (i == 1) {
                System.out.println("✅ 备选方案，也是不错的选择");
            }
        }
        
        // 输出具体建议
        outputSpecificAdvice(recommendations.get(0), req);
    }
    
    /**
     * 输出具体实施建议
     */
    private static void outputSpecificAdvice(TechnologyRecommendation topChoice, ProjectRequirements req) {
        System.out.println("\n" + repeatString("=", 60));
        System.out.println("💡 实施建议");
        System.out.println(repeatString("=", 60));
        
        String technology = topChoice.technology;
        
        if (technology.equals("JMESPath")) {
            System.out.println("🎯 JMESPath实施建议:");
            System.out.println("1. 先学习JMESPath基础语法");
            System.out.println("2. 建立表达式缓存机制");
            System.out.println("3. 制定编码规范和最佳实践");
            if (req.existingJsonPath >= 2) {
                System.out.println("4. 渐进式迁移，保持现有JsonPath代码");
                System.out.println("5. 新功能优先使用JMESPath");
            }
        } else if (technology.equals("JsonPath")) {
            System.out.println("🎯 JsonPath实施建议:");
            System.out.println("1. 继续使用现有JsonPath技术栈");
            System.out.println("2. 优化查询表达式性能");
            System.out.println("3. 建立统一的查询工具类");
            if (req.complexity >= 3) {
                System.out.println("4. 复杂场景考虑引入JMESPath补充");
            }
        } else if (technology.contains("Jackson")) {
            System.out.println("🎯 Jackson + Stream API实施建议:");
            System.out.println("1. 建立统一的JSON处理工具类");
            System.out.println("2. 使用Stream API进行函数式处理");
            System.out.println("3. 注意异常处理和类型安全");
            System.out.println("4. 考虑使用Jackson的树模型API");
        }
        
        System.out.println("\n📋 通用建议:");
        System.out.println("• 建立性能基准测试");
        System.out.println("• 制定代码审查标准");
        System.out.println("• 培训团队成员新技术");
        System.out.println("• 建立错误处理机制");
    }
    
    // 工具方法和数据类
    private static int readInt(int min, int max) {
        while (true) {
            try {
                System.out.print("请输入 (" + min + "-" + max + "): ");
                int value = Integer.parseInt(scanner.nextLine().trim());
                if (value >= min && value <= max) {
                    return value;
                }
                System.out.println("输入超出范围，请重新输入");
            } catch (NumberFormatException e) {
                System.out.println("输入格式错误，请输入数字");
            }
        }
    }
    
    // 获取推荐理由的方法
    private static String getJMESPathReason(ProjectRequirements req) {
        return "语法简洁，功能强大，适合复杂查询和数据转换";
    }
    
    private static String getJsonPathReason(ProjectRequirements req) {
        return "生态成熟，学习成本低，适合简单查询场景";
    }
    
    private static String getJacksonReason(ProjectRequirements req) {
        return "性能最佳，类型安全，适合高性能和企业级应用";
    }
    
    private static String getJOLTReason(ProjectRequirements req) {
        return "专业数据转换引擎，声明式配置，适合复杂ETL场景";
    }
    
    private static String getGraphQLReason(ProjectRequirements req) {
        return "现代API查询语言，客户端驱动，适合API数据聚合";
    }

    /**
     * JDK8兼容的字符串重复方法
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    // 数据类
    static class ProjectRequirements {
        int projectType;
        int complexity;
        int performance;
        int teamSkill;
        int maintenancePeriod;
        int dataVolume;
        boolean realTime;
        int existingJsonPath;
        int useCase;
    }
    
    static class TechnologyRecommendation {
        String technology;
        int score;
        String reason;
        
        TechnologyRecommendation(String technology, int score, String reason) {
            this.technology = technology;
            this.score = score;
            this.reason = reason;
        }
    }
}
