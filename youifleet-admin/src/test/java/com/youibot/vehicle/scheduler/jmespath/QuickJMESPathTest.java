package com.youibot.vehicle.scheduler.jmespath;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;

/**
 * 快速JMESPath测试
 * 验证修复后的JMESPath功能是否正常工作
 */
public class QuickJMESPathTest {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) {
        try {
            testBasicVariableReplacement();
            testNumericVariables();
            testBuilderPattern();
            System.out.println("\n✓ 所有测试通过！JMESPath JDK8兼容性修复成功！");
        } catch (Exception e) {
            System.err.println("✗ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试基本变量替换
     */
    public static void testBasicVariableReplacement() throws Exception {
        System.out.println("=== 测试基本变量替换 ===");
        
        String jsonData = "{\n" +
            "    \"users\": [\n" +
            "        {\"name\": \"Alice\", \"age\": 30, \"department\": \"Engineering\"},\n" +
            "        {\"name\": \"Bob\", \"age\": 25, \"department\": \"Marketing\"},\n" +
            "        {\"name\": \"Charlie\", \"age\": 35, \"department\": \"Engineering\"}\n" +
            "    ]\n" +
            "}";
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("targetDept", "Engineering");
        
        String result = JMESPathVariableUtils.executeWithVariables(
            jsonData, 
            "users[?department == ${targetDept}].name", 
            variables
        );
        
        System.out.println("结果: " + result);
        
        if (result.contains("Alice") && result.contains("Charlie") && !result.contains("Bob")) {
            System.out.println("✓ 基本变量替换测试通过");
        } else {
            throw new RuntimeException("基本变量替换测试失败");
        }
    }
    
    /**
     * 测试数值变量
     */
    public static void testNumericVariables() throws Exception {
        System.out.println("\n=== 测试数值变量 ===");
        
        String jsonData = "{\n" +
            "    \"products\": [\n" +
            "        {\"name\": \"Laptop\", \"price\": 1000},\n" +
            "        {\"name\": \"Book\", \"price\": 20},\n" +
            "        {\"name\": \"Phone\", \"price\": 800}\n" +
            "    ]\n" +
            "}";
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("minPrice", 500);
        
        String result = JMESPathVariableUtils.executeWithVariables(
            jsonData, 
            "products[?price >= ${minPrice}].name", 
            variables
        );
        
        System.out.println("结果: " + result);
        
        if (result.contains("Laptop") && result.contains("Phone") && !result.contains("Book")) {
            System.out.println("✓ 数值变量测试通过");
        } else {
            throw new RuntimeException("数值变量测试失败");
        }
    }
    
    /**
     * 测试构建器模式
     */
    public static void testBuilderPattern() throws Exception {
        System.out.println("\n=== 测试构建器模式 ===");
        
        String jsonData = "{\n" +
            "    \"employees\": [\n" +
            "        {\"name\": \"Alice\", \"salary\": 5000, \"department\": \"IT\"},\n" +
            "        {\"name\": \"Bob\", \"salary\": 4000, \"department\": \"HR\"},\n" +
            "        {\"name\": \"Charlie\", \"salary\": 6000, \"department\": \"IT\"}\n" +
            "    ]\n" +
            "}";
        
        String result = JMESPathVariableUtils.builder()
            .expression("employees[?department == ${dept} && salary >= ${minSalary}].name")
            .variable("dept", "IT")
            .variable("minSalary", 5000)
            .execute(jsonData);
        
        System.out.println("结果: " + result);
        
        if (result.contains("Alice") && result.contains("Charlie") && !result.contains("Bob")) {
            System.out.println("✓ 构建器模式测试通过");
        } else {
            throw new RuntimeException("构建器模式测试失败");
        }
    }
}
