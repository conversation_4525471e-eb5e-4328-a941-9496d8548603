package com.youibot.vehicle.scheduler.node;

import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2023/2/10 14:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class NodeTest {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Resource
    private FlowExecutor flowExecutor;

    @Test
    public void test() throws InterruptedException {
        MyThread myThread = new MyThread();
        myThread.start();
        /*Thread.sleep(5000);
        myThread.interrupt();
        while (myThread.isInterrupted() || !Thread.State.TERMINATED.equals(myThread.getState())) {
            //检测对应线程是否已经读取到中断,若线程中已经调用Thread.interrupted()或sleep、wait等方法, thread.isInterrupted()会重新置换为false
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                LOGGER.error("thread sleep error,");
            }
            LOGGER.debug("线程未中断完成......");
        }
        LOGGER.debug("线程已中断完成......");*/
        Thread.sleep(100000);
    }

    class MyThread extends Thread{

        @Override
        public void run() {
            LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("chain2", "{}");
            if (!liteflowResponse.isSuccess()) {
                LOGGER.error("任务执行失败, ", liteflowResponse.getCause());
            } else {
                LOGGER.debug("任务执行成功");
            }
        }
    }

}
