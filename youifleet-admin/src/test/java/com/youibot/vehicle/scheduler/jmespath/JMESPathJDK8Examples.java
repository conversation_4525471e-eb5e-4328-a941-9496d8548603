package com.youibot.vehicle.scheduler.jmespath;

import io.burt.jmespath.Expression;
import io.burt.jmespath.JmesPath;
import io.burt.jmespath.jackson.JacksonRuntime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * JMESPath JDK8兼容示例
 * 演示如何在JDK8环境下使用JMESPath进行变量绑定和动态查询
 */
public class JMESPathJDK8Examples {
    
    private static final JmesPath<JsonNode> jmesPath = new JacksonRuntime();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    
    public static void main(String[] args) throws Exception {
        // 示例1: 基本变量替换
        basicVariableReplacement();
        
        // 示例2: 数值变量过滤
        numericVariableFiltering();
        
        // 示例3: 复杂对象查询
        complexObjectQuery();
        
        // 示例4: 数组操作
        arrayOperations();
        
        // 示例5: 多条件组合查询
        multiConditionQuery();
    }
    
    /**
     * 示例1: 基本变量替换
     */
    public static void basicVariableReplacement() throws Exception {
        System.out.println("=== 示例1: 基本变量替换 ===");
        
        String jsonData = "{\n" +
            "    \"users\": [\n" +
            "        {\"name\": \"Alice\", \"age\": 30, \"department\": \"Engineering\"},\n" +
            "        {\"name\": \"Bob\", \"age\": 25, \"department\": \"Marketing\"},\n" +
            "        {\"name\": \"Charlie\", \"age\": 35, \"department\": \"Engineering\"}\n" +
            "    ]\n" +
            "}";
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        // 使用变量替换
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("targetDept", "Engineering");
        
        String expressionTemplate = "users[?department == '${targetDept}'].{name: name, age: age}";
        String expression = replaceVariables(expressionTemplate, variables);
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例2: 数值变量过滤
     */
    public static void numericVariableFiltering() throws Exception {
        System.out.println("=== 示例2: 数值变量过滤 ===");
        
        String jsonData = "{\n" +
            "    \"products\": [\n" +
            "        {\"name\": \"Laptop\", \"price\": 1000, \"category\": \"Electronics\"},\n" +
            "        {\"name\": \"Book\", \"price\": 20, \"category\": \"Education\"},\n" +
            "        {\"name\": \"Phone\", \"price\": 800, \"category\": \"Electronics\"}\n" +
            "    ]\n" +
            "}";
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("minPrice", 500);
        variables.put("category", "Electronics");
        
        String expressionTemplate = "products[?price >= `${minPrice}` && category == '${category}'].{name: name, price: price}";
        String expression = replaceVariables(expressionTemplate, variables);
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例3: 复杂对象查询
     */
    public static void complexObjectQuery() throws Exception {
        System.out.println("=== 示例3: 复杂对象查询 ===");
        
        String jsonData = "{\n" +
            "    \"orders\": [\n" +
            "        {\"id\": 1, \"amount\": 100, \"status\": \"completed\", \"customer\": {\"name\": \"Alice\", \"vip\": true}},\n" +
            "        {\"id\": 2, \"amount\": 250, \"status\": \"pending\", \"customer\": {\"name\": \"Bob\", \"vip\": false}},\n" +
            "        {\"id\": 3, \"amount\": 300, \"status\": \"completed\", \"customer\": {\"name\": \"Charlie\", \"vip\": true}}\n" +
            "    ]\n" +
            "}";
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("minAmount", 200);
        variables.put("status", "completed");
        
        String expressionTemplate = "orders[?amount >= `${minAmount}` && status == '${status}'].{" +
            "orderId: id, amount: amount, customerName: customer.name, isVip: customer.vip}";
        String expression = replaceVariables(expressionTemplate, variables);
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例4: 数组操作
     */
    public static void arrayOperations() throws Exception {
        System.out.println("=== 示例4: 数组操作 ===");
        
        String jsonData = "{\n" +
            "    \"data\": [\"first\", \"second\", \"third\", \"fourth\", \"fifth\"],\n" +
            "    \"config\": {\"startIndex\": 1, \"endIndex\": 4}\n" +
            "}";
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("start", 1);
        variables.put("end", 4);
        
        String expressionTemplate = "data[${start}:${end}]";
        String expression = replaceVariables(expressionTemplate, variables);
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 示例5: 多条件组合查询
     */
    public static void multiConditionQuery() throws Exception {
        System.out.println("=== 示例5: 多条件组合查询 ===");
        
        String jsonData = "{\n" +
            "    \"employees\": [\n" +
            "        {\"name\": \"Alice\", \"salary\": 5000, \"department\": \"IT\", \"experience\": 5},\n" +
            "        {\"name\": \"Bob\", \"salary\": 4000, \"department\": \"HR\", \"experience\": 3},\n" +
            "        {\"name\": \"Charlie\", \"salary\": 6000, \"department\": \"IT\", \"experience\": 7},\n" +
            "        {\"name\": \"Diana\", \"salary\": 4500, \"department\": \"Finance\", \"experience\": 4}\n" +
            "    ]\n" +
            "}";
        
        JsonNode data = objectMapper.readTree(jsonData);
        
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("dept", "IT");
        variables.put("minSalary", 4500);
        variables.put("minExp", 5);
        
        String expressionTemplate = "employees[?department == '${dept}' && salary >= `${minSalary}` && experience >= `${minExp}`].{" +
            "name: name, salary: salary, experience: experience}";
        String expression = replaceVariables(expressionTemplate, variables);
        
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        
        System.out.println("表达式模板: " + expressionTemplate);
        System.out.println("实际表达式: " + expression);
        System.out.println("结果: " + result.toPrettyString());
        System.out.println();
    }
    
    /**
     * 变量替换工具方法
     */
    public static String replaceVariables(String template, Map<String, Object> variables) {
        if (template == null || variables == null || variables.isEmpty()) {
            return template;
        }
        
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = variables.get(variableName);
            String replacement = value != null ? value.toString() : "";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 高级变量替换工具类
     */
    public static class JMESPathVariableExecutor {
        private final JmesPath<JsonNode> jmesPath;
        private final ObjectMapper objectMapper;
        
        public JMESPathVariableExecutor() {
            this.jmesPath = new JacksonRuntime();
            this.objectMapper = new ObjectMapper();
        }
        
        /**
         * 执行带变量的JMESPath表达式
         */
        public JsonNode executeWithVariables(String jsonData, String expressionTemplate, Map<String, Object> variables) throws Exception {
            JsonNode data = objectMapper.readTree(jsonData);
            String expression = replaceVariables(expressionTemplate, variables);
            Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
            return compiledExpression.search(data);
        }
        
        /**
         * 构建器模式
         */
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private String expressionTemplate;
            private Map<String, Object> variables = new HashMap<String, Object>();
            
            public Builder expression(String expressionTemplate) {
                this.expressionTemplate = expressionTemplate;
                return this;
            }
            
            public Builder variable(String name, Object value) {
                this.variables.put(name, value);
                return this;
            }
            
            public JsonNode execute(String jsonData) throws Exception {
                JMESPathVariableExecutor executor = new JMESPathVariableExecutor();
                return executor.executeWithVariables(jsonData, expressionTemplate, variables);
            }
        }
    }
    
    /**
     * 演示构建器模式的使用
     */
    public static void demonstrateBuilder() throws Exception {
        System.out.println("=== 构建器模式演示 ===");
        
        String jsonData = "{\n" +
            "    \"products\": [\n" +
            "        {\"name\": \"Laptop\", \"price\": 1000, \"category\": \"Electronics\"},\n" +
            "        {\"name\": \"Book\", \"price\": 20, \"category\": \"Education\"},\n" +
            "        {\"name\": \"Phone\", \"price\": 800, \"category\": \"Electronics\"}\n" +
            "    ]\n" +
            "}";
        
        JsonNode result = JMESPathVariableExecutor.builder()
            .expression("products[?price >= `${minPrice}` && category == '${category}'].name")
            .variable("minPrice", 500)
            .variable("category", "Electronics")
            .execute(jsonData);
        
        System.out.println("构建器模式结果: " + result.toPrettyString());
    }
}
