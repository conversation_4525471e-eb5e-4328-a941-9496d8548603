package com.youibot.vehicle.scheduler.jmespath;

import io.burt.jmespath.Expression;
import io.burt.jmespath.JmesPath;
import io.burt.jmespath.jackson.JacksonRuntime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.Option;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * JMESPath vs JsonPath 深度对比分析
 * 
 * 本类通过实际代码示例对比两种JSON查询语言的：
 * 1. 语法差异
 * 2. 功能特性
 * 3. 性能表现
 * 4. 使用场景
 * 5. 优缺点分析
 */
public class JMESPathVsJsonPathComparison {
    
    private static final JmesPath<JsonNode> jmesPath = new JacksonRuntime();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Configuration jsonPathConfig = Configuration.defaultConfiguration()
        .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL);
    
    public static void main(String[] args) throws Exception {
        System.out.println("=== JMESPath vs JsonPath 深度对比分析 ===\n");
        
        // 1. 基本语法对比
        basicSyntaxComparison();
        
        // 2. 过滤查询对比
        filteringComparison();
        
        // 3. 数据投影对比
        projectionComparison();
        
        // 4. 聚合函数对比
        aggregationComparison();
        
        // 5. 复杂查询对比
        complexQueryComparison();
        
        // 6. 性能对比
        performanceComparison();
        
        // 7. 总结对比
        summaryComparison();
    }
    
    /**
     * 1. 基本语法对比
     */
    public static void basicSyntaxComparison() throws Exception {
        System.out.println("=== 1. 基本语法对比 ===");
        
        String jsonData = "{\n" +
            "    \"users\": [\n" +
            "        {\"name\": \"Alice\", \"age\": 30, \"department\": \"Engineering\"},\n" +
            "        {\"name\": \"Bob\", \"age\": 25, \"department\": \"Marketing\"}\n" +
            "    ],\n" +
            "    \"company\": {\"name\": \"TechCorp\", \"location\": \"Beijing\"}\n" +
            "}";
        
        System.out.println("测试数据: " + jsonData + "\n");
        
        // 获取第一个用户名
        System.out.println("获取第一个用户名:");
        
        // JMESPath
        String jmesResult1 = executeJMESPath(jsonData, "users[0].name");
        System.out.println("JMESPath: users[0].name -> " + jmesResult1);
        
        // JsonPath
        String jsonResult1 = JsonPath.read(jsonData, "$.users[0].name");
        System.out.println("JsonPath:  $.users[0].name -> " + jsonResult1);
        
        // 获取所有用户名
        System.out.println("\n获取所有用户名:");
        
        // JMESPath
        String jmesResult2 = executeJMESPath(jsonData, "users[*].name");
        System.out.println("JMESPath: users[*].name -> " + jmesResult2);
        
        // JsonPath
        List<String> jsonResult2 = JsonPath.read(jsonData, "$.users[*].name");
        System.out.println("JsonPath:  $.users[*].name -> " + jsonResult2);
        
        // 获取嵌套属性
        System.out.println("\n获取嵌套属性:");
        
        // JMESPath
        String jmesResult3 = executeJMESPath(jsonData, "company.name");
        System.out.println("JMESPath: company.name -> " + jmesResult3);
        
        // JsonPath
        String jsonResult3 = JsonPath.read(jsonData, "$.company.name");
        System.out.println("JsonPath:  $.company.name -> " + jsonResult3);
        
        System.out.println("\n语法特点:");
        System.out.println("- JMESPath: 简洁，类似对象属性访问");
        System.out.println("- JsonPath:  以$开头，更接近XPath语法");
        System.out.println();
    }
    
    /**
     * 2. 过滤查询对比
     */
    public static void filteringComparison() throws Exception {
        System.out.println("=== 2. 过滤查询对比 ===");
        
        String jsonData = "{\n" +
            "    \"employees\": [\n" +
            "        {\"name\": \"Alice\", \"age\": 30, \"salary\": 5000, \"department\": \"IT\"},\n" +
            "        {\"name\": \"Bob\", \"age\": 25, \"salary\": 4000, \"department\": \"HR\"},\n" +
            "        {\"name\": \"Charlie\", \"age\": 35, \"salary\": 6000, \"department\": \"IT\"},\n" +
            "        {\"name\": \"Diana\", \"age\": 28, \"salary\": 4500, \"department\": \"Finance\"}\n" +
            "    ]\n" +
            "}";
        
        // 年龄大于28的员工
        System.out.println("年龄大于28的员工:");
        
        // JMESPath
        String jmesResult1 = executeJMESPath(jsonData, "employees[?age > `28`].name");
        System.out.println("JMESPath: employees[?age > `28`].name -> " + jmesResult1);
        
        // JsonPath
        List<String> jsonResult1 = JsonPath.read(jsonData, "$.employees[?(@.age > 28)].name");
        System.out.println("JsonPath:  $.employees[?(@.age > 28)].name -> " + jsonResult1);
        
        // IT部门且薪资大于4500的员工
        System.out.println("\nIT部门且薪资大于4500的员工:");
        
        // JMESPath
        String jmesResult2 = executeJMESPath(jsonData, 
            "employees[?department == 'IT' && salary > `4500`].{name: name, salary: salary}");
        System.out.println("JMESPath: employees[?department == 'IT' && salary > `4500`].{name: name, salary: salary}");
        System.out.println("结果: " + jmesResult2);
        
        // JsonPath
        List<Map<String, Object>> jsonResult2 = JsonPath.read(jsonData, 
            "$.employees[?(@.department == 'IT' && @.salary > 4500)][['name', 'salary']]");
        System.out.println("JsonPath:  $.employees[?(@.department == 'IT' && @.salary > 4500)][['name', 'salary']]");
        System.out.println("结果: " + jsonResult2);
        
        System.out.println("\n过滤语法特点:");
        System.out.println("- JMESPath: [?condition] 语法，数值需要反引号");
        System.out.println("- JsonPath:  [?(@.field condition)] 语法，@表示当前元素");
        System.out.println();
    }
    
    /**
     * 3. 数据投影对比
     */
    public static void projectionComparison() throws Exception {
        System.out.println("=== 3. 数据投影对比 ===");
        
        String jsonData = "{\n" +
            "    \"products\": [\n" +
            "        {\"id\": 1, \"name\": \"Laptop\", \"price\": 1000, \"category\": \"Electronics\", \"stock\": 50},\n" +
            "        {\"id\": 2, \"name\": \"Book\", \"price\": 20, \"category\": \"Education\", \"stock\": 100},\n" +
            "        {\"id\": 3, \"name\": \"Phone\", \"price\": 800, \"category\": \"Electronics\", \"stock\": 30}\n" +
            "    ]\n" +
            "}";
        
        // 创建自定义结构
        System.out.println("创建自定义结构:");
        
        // JMESPath - 强大的投影功能
        String jmesResult = executeJMESPath(jsonData,
            "products[*].{productName: name, cost: price, inStock: stock > `40`}");
        System.out.println("JMESPath投影:");
        System.out.println("products[*].{productName: name, cost: price, inStock: stock > `40`}");
        System.out.println("结果: " + jmesResult);
        
        // JsonPath - 需要多步操作
        System.out.println("\nJsonPath投影 (需要后处理):");
        List<Map<String, Object>> products = JsonPath.read(jsonData, "$.products[*]");
        System.out.println("$.products[*] -> 需要在Java代码中进行数据转换");
        System.out.println("原始结果: " + products.get(0));
        
        // 多字段选择
        System.out.println("\n多字段选择:");
        
        // JMESPath
        String jmesMulti = executeJMESPath(jsonData, "products[*].[name, price]");
        System.out.println("JMESPath: products[*].[name, price] -> " + jmesMulti);
        
        // JsonPath
        List<List<Object>> jsonMulti = JsonPath.read(jsonData, "$.products[*]['name', 'price']");
        System.out.println("JsonPath:  $.products[*]['name', 'price'] -> " + jsonMulti);
        
        System.out.println("\n投影功能对比:");
        System.out.println("- JMESPath: 内置强大的投影语法，可创建复杂结构");
        System.out.println("- JsonPath:  基本的字段选择，复杂投影需要后处理");
        System.out.println();
    }
    
    /**
     * 4. 聚合函数对比
     */
    public static void aggregationComparison() throws Exception {
        System.out.println("=== 4. 聚合函数对比 ===");
        
        String jsonData = "{\n" +
            "    \"sales\": [\n" +
            "        {\"amount\": 1000, \"region\": \"North\"},\n" +
            "        {\"amount\": 1500, \"region\": \"South\"},\n" +
            "        {\"amount\": 1200, \"region\": \"North\"},\n" +
            "        {\"amount\": 800, \"region\": \"East\"}\n" +
            "    ]\n" +
            "}";
        
        // 数组长度
        System.out.println("数组长度:");
        String jmesLength = executeJMESPath(jsonData, "length(sales)");
        System.out.println("JMESPath: length(sales) -> " + jmesLength);
        System.out.println("JsonPath:  $.sales.length() -> " + JsonPath.read(jsonData, "$.sales.length()"));
        
        // 求和
        System.out.println("\n求和:");
        String jmesSum = executeJMESPath(jsonData, "sum(sales[*].amount)");
        System.out.println("JMESPath: sum(sales[*].amount) -> " + jmesSum);
        System.out.println("JsonPath:  需要Java代码计算 -> " + 
            JsonPath.read(jsonData, "$.sales[*].amount").toString());
        
        // 最大值
        System.out.println("\n最大值:");
        String jmesMax = executeJMESPath(jsonData, "max(sales[*].amount)");
        System.out.println("JMESPath: max(sales[*].amount) -> " + jmesMax);
        System.out.println("JsonPath:  需要Java代码计算");
        
        // 排序
        System.out.println("\n排序:");
        String jmesSort = executeJMESPath(jsonData, "sort_by(sales, &amount)[*].amount");
        System.out.println("JMESPath: sort_by(sales, &amount)[*].amount -> " + jmesSort);
        System.out.println("JsonPath:  需要Java代码排序");
        
        System.out.println("\n聚合功能对比:");
        System.out.println("- JMESPath: 内置丰富的聚合函数 (sum, max, min, avg, sort_by等)");
        System.out.println("- JsonPath:  基本聚合有限，复杂计算需要Java代码");
        System.out.println();
    }
    
    /**
     * 5. 复杂查询对比
     */
    public static void complexQueryComparison() throws Exception {
        System.out.println("=== 5. 复杂查询对比 ===");
        
        String jsonData = "{\n" +
            "    \"orders\": [\n" +
            "        {\"id\": 1, \"customer\": {\"name\": \"Alice\", \"vip\": true}, \"items\": [{\"name\": \"Laptop\", \"price\": 1000}], \"total\": 1000},\n" +
            "        {\"id\": 2, \"customer\": {\"name\": \"Bob\", \"vip\": false}, \"items\": [{\"name\": \"Book\", \"price\": 20}, {\"name\": \"Pen\", \"price\": 5}], \"total\": 25},\n" +
            "        {\"id\": 3, \"customer\": {\"name\": \"Charlie\", \"vip\": true}, \"items\": [{\"name\": \"Phone\", \"price\": 800}], \"total\": 800}\n" +
            "    ]\n" +
            "}";
        
        // 复杂条件查询：VIP客户的高价值订单
        System.out.println("VIP客户的高价值订单(>500):");
        
        // JMESPath - 一行搞定
        String jmesComplex = executeJMESPath(jsonData, 
            "orders[?customer.vip == `true` && total > `500`].{orderId: id, customerName: customer.name, orderTotal: total, itemCount: length(items)}");
        System.out.println("JMESPath (一行解决):");
        System.out.println("orders[?customer.vip == `true` && total > `500`].{orderId: id, customerName: customer.name, orderTotal: total, itemCount: length(items)}");
        System.out.println("结果: " + jmesComplex);
        
        // JsonPath - 需要多步
        System.out.println("\nJsonPath (需要多步):");
        List<Map<String, Object>> jsonComplex = JsonPath.read(jsonData, 
            "$.orders[?(@.customer.vip == true && @.total > 500)]");
        System.out.println("$.orders[?(@.customer.vip == true && @.total > 500)]");
        System.out.println("结果: " + jsonComplex);
        System.out.println("注意: JsonPath需要额外的Java代码来重构数据格式");
        
        System.out.println("\n复杂查询对比:");
        System.out.println("- JMESPath: 一次查询完成复杂的过滤、投影、计算");
        System.out.println("- JsonPath:  需要多步操作和Java代码后处理");
        System.out.println();
    }
    
    /**
     * 6. 性能对比
     */
    public static void performanceComparison() throws Exception {
        System.out.println("=== 6. 性能对比 ===");
        
        // 生成大量测试数据
        StringBuilder largeJsonBuilder = new StringBuilder();
        largeJsonBuilder.append("{\"data\": [");
        for (int i = 0; i < 10000; i++) {
            if (i > 0) largeJsonBuilder.append(",");
            largeJsonBuilder.append("{\"id\": ").append(i)
                .append(", \"value\": ").append(i * 10)
                .append(", \"category\": \"").append(i % 3 == 0 ? "A" : "B").append("\"}");
        }
        largeJsonBuilder.append("]}");
        String largeJson = largeJsonBuilder.toString();
        
        System.out.println("测试数据: 10,000条记录");
        
        // JMESPath性能测试
        long jmesStart = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            executeJMESPath(largeJson, "data[?value > `50000`].id");
        }
        long jmesTime = System.currentTimeMillis() - jmesStart;
        
        // JsonPath性能测试
        long jsonStart = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            JsonPath.read(largeJson, "$.data[?(@.value > 50000)].id");
        }
        long jsonTime = System.currentTimeMillis() - jsonStart;
        
        System.out.println("执行100次过滤查询:");
        System.out.println("JMESPath: " + jmesTime + "ms");
        System.out.println("JsonPath:  " + jsonTime + "ms");
        
        System.out.println("\n性能特点:");
        System.out.println("- JMESPath: 编译型查询，首次编译后性能较好");
        System.out.println("- JsonPath:  解释型查询，每次都需要解析表达式");
        System.out.println("- 建议: 对于重复查询，缓存编译后的表达式");
        System.out.println();
    }
    
    /**
     * 7. 总结对比
     */
    public static void summaryComparison() {
        System.out.println("=== 7. 总结对比 ===");
        
        System.out.println("┌─────────────────┬─────────────────────┬─────────────────────┐");
        System.out.println("│     特性        │      JMESPath       │      JsonPath       │");
        System.out.println("├─────────────────┼─────────────────────┼─────────────────────┤");
        System.out.println("│ 语法简洁性      │ ★★★★★ 非常简洁    │ ★★★☆☆ 较复杂      │");
        System.out.println("│ 学习曲线        │ ★★★★☆ 容易学习    │ ★★★☆☆ 中等难度    │");
        System.out.println("│ 功能丰富性      │ ★★★★★ 功能强大    │ ★★★☆☆ 功能基础    │");
        System.out.println("│ 数据投影        │ ★★★★★ 内置支持    │ ★★☆☆☆ 需要后处理  │");
        System.out.println("│ 聚合函数        │ ★★★★★ 丰富内置    │ ★★☆☆☆ 基础支持    │");
        System.out.println("│ 性能表现        │ ★★★★☆ 编译型好    │ ★★★☆☆ 解释型一般  │");
        System.out.println("│ 生态系统        │ ★★★☆☆ 相对较新    │ ★★★★★ 成熟生态    │");
        System.out.println("│ 社区支持        │ ★★★☆☆ 社区较小    │ ★★★★★ 社区活跃    │");
        System.out.println("└─────────────────┴─────────────────────┴─────────────────────┘");
        
        System.out.println("\n使用建议:");
        System.out.println("选择 JMESPath 当:");
        System.out.println("- 需要复杂的数据转换和投影");
        System.out.println("- 大量使用聚合函数和计算");
        System.out.println("- 希望查询表达式简洁易读");
        System.out.println("- 性能要求较高的重复查询");
        
        System.out.println("\n选择 JsonPath 当:");
        System.out.println("- 项目已大量使用JsonPath");
        System.out.println("- 需要与现有JsonPath生态集成");
        System.out.println("- 简单的路径查询和过滤");
        System.out.println("- 团队对JsonPath更熟悉");
        
        System.out.println("\n结论:");
        System.out.println("JMESPath在功能性和表达能力上更强，适合复杂的JSON数据处理；");
        System.out.println("JsonPath在生态成熟度上更好，适合简单查询和现有项目集成。");
    }
    
    // 工具方法
    private static String executeJMESPath(String jsonData, String expression) throws Exception {
        JsonNode data = objectMapper.readTree(jsonData);
        Expression<JsonNode> compiledExpression = jmesPath.compile(expression);
        JsonNode result = compiledExpression.search(data);
        return result.toString();
    }
}
