package com.youibot.vehicle.scheduler;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Test {
    public static void main(String[] args) {
        List<String> markerCodes = Lists.newArrayList("a","a","a","a","b","b","b","c");
        String maxNumMarkerCode = null;
        int maxNum = 0;
        Map<String, Integer> count = new HashMap<>();
        for (String markerCode : markerCodes) {
            count.put(markerCode, count.getOrDefault(markerCode, 0) + 1);
            int currentNum = count.get(markerCode);
            if(currentNum > maxNum){
                maxNumMarkerCode = markerCode;
                maxNum = currentNum;
            }
        }
        System.out.println(maxNumMarkerCode);
    }

    @org.junit.jupiter.api.Test
    public  void test12(){
        String value = "1_2_3_4_5_6_7_8_9_10";
        String[] sValue = org.apache.commons.lang3.StringUtils.split(value, "_");
        Double[] array = Arrays.stream(sValue).map(Double::parseDouble).toArray(Double[]::new);   ;
        System.out.println(Arrays.toString(array));
    }
}
