package com.youibot.vehicle.scheduler.charge;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.youibot.vehicle.scheduler.modules.device.netty.message.ChargeStationBaseRequest;
import com.youibot.vehicle.scheduler.modules.device.netty.message.ChargeStationRegisterRequest;
import com.youibot.vehicle.scheduler.modules.device.netty.message.ChargeStationSocketMessage;
import com.youibot.vehicle.scheduler.modules.device.netty.message.ChargeStationStatusReportRequest;
import com.youibot.vehicle.scheduler.modules.device.netty.utils.ChargeStationSocketUtils;
import org.apache.commons.lang3.RandomUtils;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.UUID;

public class Test {

    @org.junit.jupiter.api.Test
    public void test1() throws IOException {
        Socket socket = new Socket();
        socket.connect(new InetSocketAddress("*************", 9999));
        //socket.connect(new InetSocketAddress("127.0.0.1", 16649));

        // 1、发送注册信息
        String device_id = "1234567890";
        ChargeStationSocketMessage response = sendRegister(socket, device_id, "register");
        String deviceCode = response.getData().getString("device_code");

        // 2、异步循环发送状态
        sendStatus(socket, deviceCode, "status_report");

        // 3、循环发送心跳
        sendHeartbeat(socket, deviceCode, "heartbeat");
    }

    private ChargeStationSocketMessage sendRegister(Socket socket, String device_id, String messageType) throws IOException { // 发送状态
        ChargeStationRegisterRequest request = new ChargeStationRegisterRequest();
        request.setMsg_id(UUID.randomUUID().toString());
        request.setDevice_id(device_id);
        request.setIp("127.0.0.1");
        request.setDevice_type("1234567890");
        request.setDevice_mode("CS");
        request.setVendor("YOUI");
        request.setHardware_ver("1.0.0");
        request.setSoftware_ver("1.0.0");
        request.setMax_power(100.0);
        request.setVoltage_range("200V~500V");
        request.setCharge_types("AC, DC");
        request.setOnline_time(System.currentTimeMillis());
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(request));
        byte[] registerBytes = ChargeStationSocketUtils.convertToBytes(messageType, jsonObject);
        socket.getOutputStream().write(registerBytes);
        socket.getOutputStream().flush();

        byte[] responseBytes = new byte[1024];
        socket.getInputStream().read(responseBytes);
        ChargeStationSocketMessage response = ChargeStationSocketUtils.convertMessage(responseBytes);
        System.out.println("收到响应：" + response);
        return response;
    }

    private void sendStatus(Socket socket, String deviceCode, String messageType) { // 发送状态
        new Thread(() -> {
            while (true) {
                try {
                    ChargeStationStatusReportRequest request = new ChargeStationStatusReportRequest();
                    request.setDevice_code(deviceCode);
                    request.setMsg_id(UUID.randomUUID().toString());
                    request.setNetwork_status("online");
                    request.setControl_mode("auto");
                    request.setWork_status("normal");
                    request.setDischarge_status("no_discharge");
                    request.setReset_status("completed");
                    request.setSet_voltage(200.0);
                    request.setSet_current(16.0);
                    request.setVoltage(220.0);
                    request.setCurrent(NumberUtil.round(RandomUtils.nextDouble(0.0, 16.0), 2).doubleValue());
                    request.setPower(300.0);
                    request.setDc_temp(30.0);
                    request.setBrush_temp(35.0);
                    request.setAir_temp(25.0);
                    request.setErrorInfos(null);
                    request.setExpand_data(null);
                    JSONObject status = JSONObject.parseObject(JSONObject.toJSONString(request));
                    byte[] statusBytes = ChargeStationSocketUtils.convertToBytes(messageType, status);
                    socket.getOutputStream().write(statusBytes);
                    socket.getOutputStream().flush();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                ThreadUtil.sleep(1000);
            }
        }).start();
    }

    private void sendHeartbeat(Socket socket, String deviceCode, String messageType) throws IOException {
        while (true) {
            ThreadUtil.sleep(1000);
            ChargeStationBaseRequest request1 = new ChargeStationBaseRequest();
            request1.setDevice_code(deviceCode);
            request1.setMsg_id(UUID.randomUUID().toString());
            JSONObject heartbeat = JSONObject.parseObject(JSONObject.toJSONString(request1));
            byte[] heartbeatBytes = ChargeStationSocketUtils.convertToBytes(messageType, heartbeat);
            socket.getOutputStream().write(heartbeatBytes);
            socket.getOutputStream().flush();

            byte[] responseBytes = new byte[1024];
            socket.getInputStream().read(responseBytes);
            ChargeStationSocketMessage response = ChargeStationSocketUtils.convertMessage(responseBytes);
            System.out.println("收到响应：" + response);
        }
    }
}
