@primary-color: #ee742a;
@layout-body-background: #fff;
@layout-header-background: #fff;
@error-color: #c81207;
@warning-color: #de8807;
@success-color: #0a9b55;
@menu-item-color: #000000;
@table-header-bg: #f0f0f0;
@table-row-hover-bg: #eaeaea;
@table-selected-row-bg: #fef2ea;
@menu-item-active-bg: #f6eae2;
@table-border-color: #e7e7e7;
@tag-default-color: #9f9f9f;
html {-webkit-tap-highlight-color: rgba(0, 0, 0, 0);}
body {color: rgba(0, 0, 0, 0.85);background-color: #fff;}
h1,
h2,
h3,
h4,
h5,
h6 {color: rgba(0, 0, 0, 0.85);}
abbr[title],
abbr[data-original-title] {border-bottom: 0;}
a {color: @primary-color;background-color: transparent;}
a:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
a:active {color: color(~`colorPalette("@{primary-color}", 7)`);}
a[disabled] {color: rgba(0, 0, 0, 0.25);}
img {border-style: none;}
table {border-collapse: collapse;}
caption {color: rgba(0, 0, 0, 0.45);}
input,
button,
select,
optgroup,
textarea {color: inherit;}
button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {border-style: none;}
fieldset {border: 0;}
legend {color: inherit;}
mark {background-color: #feffe6;}
::selection {color: #fff;background: @primary-color;}
.anticon {color: inherit;}
.ant-fade-enter, .ant-fade-appear {animation-fill-mode: both;}
.ant-fade-leave {animation-fill-mode: both;}
.fade-enter, .fade-appear {animation-fill-mode: both;}
.fade-leave {animation-fill-mode: both;}
.ant-move-up-enter, .ant-move-up-appear {animation-fill-mode: both;}
.ant-move-up-leave {animation-fill-mode: both;}
.move-up-enter, .move-up-appear {animation-fill-mode: both;}
.move-up-leave {animation-fill-mode: both;}
.ant-move-down-enter, .ant-move-down-appear {animation-fill-mode: both;}
.ant-move-down-leave {animation-fill-mode: both;}
.move-down-enter, .move-down-appear {animation-fill-mode: both;}
.move-down-leave {animation-fill-mode: both;}
.ant-move-left-enter, .ant-move-left-appear {animation-fill-mode: both;}
.ant-move-left-leave {animation-fill-mode: both;}
.move-left-enter, .move-left-appear {animation-fill-mode: both;}
.move-left-leave {animation-fill-mode: both;}
.ant-move-right-enter, .ant-move-right-appear {animation-fill-mode: both;}
.ant-move-right-leave {animation-fill-mode: both;}
.move-right-enter, .move-right-appear {animation-fill-mode: both;}
.move-right-leave {animation-fill-mode: both;}
html {--antd-wave-shadow-color: @primary-color;}
[ant-click-animating-without-extra-node='true']::after, .ant-click-animating-node {border-radius: inherit;box-shadow: 0 0 0 0 @primary-color;box-shadow: 0 0 0 0 var(--antd-wave-shadow-color);animation-fill-mode: forwards;}
.slide-up-enter, .slide-up-appear {animation-fill-mode: both;}
.slide-up-leave {animation-fill-mode: both;}
.slide-down-enter, .slide-down-appear {animation-fill-mode: both;}
.slide-down-leave {animation-fill-mode: both;}
.slide-left-enter, .slide-left-appear {animation-fill-mode: both;}
.slide-left-leave {animation-fill-mode: both;}
.slide-right-enter, .slide-right-appear {animation-fill-mode: both;}
.slide-right-leave {animation-fill-mode: both;}
.ant-slide-up-enter, .ant-slide-up-appear {animation-fill-mode: both;}
.ant-slide-up-leave {animation-fill-mode: both;}
.ant-slide-down-enter, .ant-slide-down-appear {animation-fill-mode: both;}
.ant-slide-down-leave {animation-fill-mode: both;}
.ant-slide-left-enter, .ant-slide-left-appear {animation-fill-mode: both;}
.ant-slide-left-leave {animation-fill-mode: both;}
.ant-slide-right-enter, .ant-slide-right-appear {animation-fill-mode: both;}
.ant-slide-right-leave {animation-fill-mode: both;}
.ant-zoom-enter, .ant-zoom-appear {animation-fill-mode: both;}
.ant-zoom-leave {animation-fill-mode: both;}
.zoom-enter, .zoom-appear {animation-fill-mode: both;}
.zoom-leave {animation-fill-mode: both;}
.ant-zoom-big-enter, .ant-zoom-big-appear {animation-fill-mode: both;}
.ant-zoom-big-leave {animation-fill-mode: both;}
.zoom-big-enter, .zoom-big-appear {animation-fill-mode: both;}
.zoom-big-leave {animation-fill-mode: both;}
.ant-zoom-big-fast-enter, .ant-zoom-big-fast-appear {animation-fill-mode: both;}
.ant-zoom-big-fast-leave {animation-fill-mode: both;}
.zoom-big-fast-enter, .zoom-big-fast-appear {animation-fill-mode: both;}
.zoom-big-fast-leave {animation-fill-mode: both;}
.ant-zoom-up-enter, .ant-zoom-up-appear {animation-fill-mode: both;}
.ant-zoom-up-leave {animation-fill-mode: both;}
.zoom-up-enter, .zoom-up-appear {animation-fill-mode: both;}
.zoom-up-leave {animation-fill-mode: both;}
.ant-zoom-down-enter, .ant-zoom-down-appear {animation-fill-mode: both;}
.ant-zoom-down-leave {animation-fill-mode: both;}
.zoom-down-enter, .zoom-down-appear {animation-fill-mode: both;}
.zoom-down-leave {animation-fill-mode: both;}
.ant-zoom-left-enter, .ant-zoom-left-appear {animation-fill-mode: both;}
.ant-zoom-left-leave {animation-fill-mode: both;}
.zoom-left-enter, .zoom-left-appear {animation-fill-mode: both;}
.zoom-left-leave {animation-fill-mode: both;}
.ant-zoom-right-enter, .ant-zoom-right-appear {animation-fill-mode: both;}
.ant-zoom-right-leave {animation-fill-mode: both;}
.zoom-right-enter, .zoom-right-appear {animation-fill-mode: both;}
.zoom-right-leave {animation-fill-mode: both;}
.ant-alert {color: rgba(0, 0, 0, 0.85);border-radius: 2px;}
.ant-alert-success {background-color: color(~`colorPalette("@{success-color}", 1)`);border: 1px solid color(~`colorPalette("@{success-color}", 1)`);}
.ant-alert-success .ant-alert-icon {color: @success-color;}
.ant-alert-info {background-color: color(~`colorPalette("@{primary-color}", 1)`);border: 1px solid color(~`colorPalette("@{primary-color}", 3)`);}
.ant-alert-info .ant-alert-icon {color: @primary-color;}
.ant-alert-warning {background-color: color(~`colorPalette("@{warning-color}", 1)`);border: 1px solid color(~`colorPalette("@{warning-color}", 3)`);}
.ant-alert-warning .ant-alert-icon {color: @warning-color;}
.ant-alert-error {background-color: color(~`colorPalette("@{error-color}", 1)`);border: 1px solid color(~`colorPalette("@{error-color}", 3)`);}
.ant-alert-error .ant-alert-icon {color: @error-color;}
.ant-alert-close-icon {background-color: transparent;border: none;}
.ant-alert-close-icon .anticon-close {color: rgba(0, 0, 0, 0.45);}
.ant-alert-close-icon .anticon-close:hover {color: rgba(0, 0, 0, 0.75);}
.ant-alert-close-text {color: rgba(0, 0, 0, 0.45);}
.ant-alert-close-text:hover {color: rgba(0, 0, 0, 0.75);}
.ant-alert-with-description .ant-alert-message {color: rgba(0, 0, 0, 0.85);}
.ant-alert-message {color: rgba(0, 0, 0, 0.85);}
.ant-alert-banner {border: 0;border-radius: 0;}
.ant-anchor {color: rgba(0, 0, 0, 0.85);}
.ant-anchor-wrapper {background-color: transparent;}
.ant-anchor-ink::before {background-color: #f0f0f0;}
.ant-anchor-ink-ball {background-color: #fff;border: 2px solid @primary-color;border-radius: 8px;}
.ant-anchor-link-title {color: rgba(0, 0, 0, 0.85);}
.ant-anchor-link-active > .ant-anchor-link-title {color: @primary-color;}
.ant-select-auto-complete {color: rgba(0, 0, 0, 0.85);}
.ant-avatar {color: rgba(0, 0, 0, 0.85);color: #fff;background: #ccc;border-radius: 50%;}
.ant-avatar-image {background: transparent;}
.ant-avatar-lg {border-radius: 50%;}
.ant-avatar-sm {border-radius: 50%;}
.ant-avatar-square {border-radius: 2px;}
.ant-avatar-group .ant-avatar {border: 1px solid #fff;}
.ant-back-top {color: rgba(0, 0, 0, 0.85);}
.ant-back-top-content {color: #fff;background-color: rgba(0, 0, 0, 0.45);border-radius: 20px;}
.ant-back-top-content:hover {background-color: rgba(0, 0, 0, 0.85);}
.ant-badge {color: rgba(0, 0, 0, 0.85);}
.ant-badge-count {color: #fff;background: #ff4d4f;border-radius: 10px;box-shadow: 0 0 0 1px #fff;}
.ant-badge-count a, .ant-badge-count a:hover {color: #fff;}
.ant-badge-count-sm {border-radius: 7px;}
.ant-badge-dot {background: #ff4d4f;border-radius: 100%;box-shadow: 0 0 0 1px #fff;}
.ant-badge-status-dot {border-radius: 50%;}
.ant-badge-status-success {background-color: @success-color;}
.ant-badge-status-processing {background-color: #1890ff;}
.ant-badge-status-processing::after {border: 1px solid #1890ff;border-radius: 50%;}
.ant-badge-status-default {background-color: #d9d9d9;}
.ant-badge-status-error {background-color: @error-color;}
.ant-badge-status-warning {background-color: @warning-color;}
.ant-badge-status-pink {background: #eb2f96;}
.ant-badge-status-magenta {background: #eb2f96;}
.ant-badge-status-red {background: #f5222d;}
.ant-badge-status-volcano {background: #fa541c;}
.ant-badge-status-orange {background: #fa8c16;}
.ant-badge-status-yellow {background: #fadb14;}
.ant-badge-status-gold {background: #faad14;}
.ant-badge-status-cyan {background: #13c2c2;}
.ant-badge-status-lime {background: #a0d911;}
.ant-badge-status-green {background: #52c41a;}
.ant-badge-status-blue {background: #1890ff;}
.ant-badge-status-geekblue {background: #2f54eb;}
.ant-badge-status-purple {background: #722ed1;}
.ant-badge-status-text {color: rgba(0, 0, 0, 0.85);}
.ant-badge-zoom-appear, .ant-badge-zoom-enter {animation-fill-mode: both;}
.ant-badge-zoom-leave {animation-fill-mode: both;}
.ant-ribbon {color: rgba(0, 0, 0, 0.85);color: #fff;background-color: @primary-color;border-radius: 2px;}
.ant-ribbon-text {color: #fff;}
.ant-ribbon-corner {color: currentcolor;border: 4px solid;}
.ant-ribbon-corner::after {color: rgba(0, 0, 0, 0.25);border: inherit;}
.ant-ribbon-color-pink {color: #eb2f96;background: #eb2f96;}
.ant-ribbon-color-magenta {color: #eb2f96;background: #eb2f96;}
.ant-ribbon-color-red {color: #f5222d;background: #f5222d;}
.ant-ribbon-color-volcano {color: #fa541c;background: #fa541c;}
.ant-ribbon-color-orange {color: #fa8c16;background: #fa8c16;}
.ant-ribbon-color-yellow {color: #fadb14;background: #fadb14;}
.ant-ribbon-color-gold {color: #faad14;background: #faad14;}
.ant-ribbon-color-cyan {color: #13c2c2;background: #13c2c2;}
.ant-ribbon-color-lime {color: #a0d911;background: #a0d911;}
.ant-ribbon-color-green {color: #52c41a;background: #52c41a;}
.ant-ribbon-color-blue {color: #1890ff;background: #1890ff;}
.ant-ribbon-color-geekblue {color: #2f54eb;background: #2f54eb;}
.ant-ribbon-color-purple {color: #722ed1;background: #722ed1;}
.ant-ribbon.ant-ribbon-placement-end {border-bottom-right-radius: 0;}
.ant-ribbon.ant-ribbon-placement-end .ant-ribbon-corner {border-color: currentcolor transparent transparent currentcolor;}
.ant-ribbon.ant-ribbon-placement-start {border-bottom-left-radius: 0;}
.ant-ribbon.ant-ribbon-placement-start .ant-ribbon-corner {border-color: currentcolor currentcolor transparent transparent;}
.ant-ribbon-rtl.ant-ribbon-placement-end {border-bottom-right-radius: 2px;border-bottom-left-radius: 0;}
.ant-ribbon-rtl.ant-ribbon-placement-end .ant-ribbon-corner {border-color: currentcolor currentcolor transparent transparent;}
.ant-ribbon-rtl.ant-ribbon-placement-end .ant-ribbon-corner::after {border-color: currentcolor currentcolor transparent transparent;}
.ant-ribbon-rtl.ant-ribbon-placement-start {border-bottom-right-radius: 0;border-bottom-left-radius: 2px;}
.ant-ribbon-rtl.ant-ribbon-placement-start .ant-ribbon-corner {border-color: currentcolor transparent transparent currentcolor;}
.ant-ribbon-rtl.ant-ribbon-placement-start .ant-ribbon-corner::after {border-color: currentcolor transparent transparent currentcolor;}
.ant-breadcrumb {color: rgba(0, 0, 0, 0.85);color: rgba(0, 0, 0, 0.45);}
.ant-breadcrumb a {color: rgba(0, 0, 0, 0.45);}
.ant-breadcrumb a:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-breadcrumb > span:last-child {color: rgba(0, 0, 0, 0.85);}
.ant-breadcrumb > span:last-child a {color: rgba(0, 0, 0, 0.85);}
.ant-breadcrumb-separator {color: rgba(0, 0, 0, 0.45);}
.ant-btn {background-image: none;border: 1px solid transparent;box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);border-radius: 2px;color: rgba(0, 0, 0, 0.85);border-color: #d9d9d9;background: #fff;}
.ant-btn:not([disabled]):active {box-shadow: none;}
.ant-btn-lg {border-radius: 2px;}
.ant-btn-sm {border-radius: 2px;}
.ant-btn > a:only-child {color: currentcolor;}
.ant-btn > a:only-child::after {background: transparent;}
.ant-btn:hover, .ant-btn:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);background: #fff;}
.ant-btn:hover > a:only-child, .ant-btn:focus > a:only-child {color: currentcolor;}
.ant-btn:hover > a:only-child::after, .ant-btn:focus > a:only-child::after {background: transparent;}
.ant-btn:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);background: #fff;}
.ant-btn:active > a:only-child {color: currentcolor;}
.ant-btn:active > a:only-child::after {background: transparent;}
.ant-btn[disabled], .ant-btn[disabled]:hover, .ant-btn[disabled]:focus, .ant-btn[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn[disabled] > a:only-child, .ant-btn[disabled]:hover > a:only-child, .ant-btn[disabled]:focus > a:only-child, .ant-btn[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn[disabled] > a:only-child::after, .ant-btn[disabled]:hover > a:only-child::after, .ant-btn[disabled]:focus > a:only-child::after, .ant-btn[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn:hover, .ant-btn:focus, .ant-btn:active {background: #fff;}
.ant-btn-primary {color: #fff;border-color: @primary-color;background: @primary-color;box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);}
.ant-btn-primary > a:only-child {color: currentcolor;}
.ant-btn-primary > a:only-child::after {background: transparent;}
.ant-btn-primary:hover, .ant-btn-primary:focus {color: #fff;border-color: color(~`colorPalette("@{primary-color}", 5)`);background: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-btn-primary:hover > a:only-child, .ant-btn-primary:focus > a:only-child {color: currentcolor;}
.ant-btn-primary:hover > a:only-child::after, .ant-btn-primary:focus > a:only-child::after {background: transparent;}
.ant-btn-primary:active {color: #fff;border-color: color(~`colorPalette("@{primary-color}", 7)`);background: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-btn-primary:active > a:only-child {color: currentcolor;}
.ant-btn-primary:active > a:only-child::after {background: transparent;}
.ant-btn-primary[disabled], .ant-btn-primary[disabled]:hover, .ant-btn-primary[disabled]:focus, .ant-btn-primary[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-primary[disabled] > a:only-child, .ant-btn-primary[disabled]:hover > a:only-child, .ant-btn-primary[disabled]:focus > a:only-child, .ant-btn-primary[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-primary[disabled] > a:only-child::after, .ant-btn-primary[disabled]:hover > a:only-child::after, .ant-btn-primary[disabled]:focus > a:only-child::after, .ant-btn-primary[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child) {border-right-color: color(~`colorPalette("@{primary-color}", 5)`);border-left-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child):disabled {border-color: #d9d9d9;}
.ant-btn-group .ant-btn-primary:first-child:not(:last-child) {border-right-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-btn-group .ant-btn-primary:first-child:not(:last-child)[disabled] {border-right-color: #d9d9d9;}
.ant-btn-group .ant-btn-primary:last-child:not(:first-child), .ant-btn-group .ant-btn-primary + .ant-btn-primary {border-left-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-btn-group .ant-btn-primary:last-child:not(:first-child)[disabled], .ant-btn-group .ant-btn-primary + .ant-btn-primary[disabled] {border-left-color: #d9d9d9;}
.ant-btn-ghost {color: rgba(0, 0, 0, 0.85);border-color: #d9d9d9;background: transparent;}
.ant-btn-ghost > a:only-child {color: currentcolor;}
.ant-btn-ghost > a:only-child::after {background: transparent;}
.ant-btn-ghost:hover, .ant-btn-ghost:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);background: transparent;}
.ant-btn-ghost:hover > a:only-child, .ant-btn-ghost:focus > a:only-child {color: currentcolor;}
.ant-btn-ghost:hover > a:only-child::after, .ant-btn-ghost:focus > a:only-child::after {background: transparent;}
.ant-btn-ghost:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);background: transparent;}
.ant-btn-ghost:active > a:only-child {color: currentcolor;}
.ant-btn-ghost:active > a:only-child::after {background: transparent;}
.ant-btn-ghost[disabled], .ant-btn-ghost[disabled]:hover, .ant-btn-ghost[disabled]:focus, .ant-btn-ghost[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-ghost[disabled] > a:only-child, .ant-btn-ghost[disabled]:hover > a:only-child, .ant-btn-ghost[disabled]:focus > a:only-child, .ant-btn-ghost[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-ghost[disabled] > a:only-child::after, .ant-btn-ghost[disabled]:hover > a:only-child::after, .ant-btn-ghost[disabled]:focus > a:only-child::after, .ant-btn-ghost[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-dashed {color: rgba(0, 0, 0, 0.85);border-color: #d9d9d9;background: #fff;border-style: dashed;}
.ant-btn-dashed > a:only-child {color: currentcolor;}
.ant-btn-dashed > a:only-child::after {background: transparent;}
.ant-btn-dashed:hover, .ant-btn-dashed:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);background: #fff;}
.ant-btn-dashed:hover > a:only-child, .ant-btn-dashed:focus > a:only-child {color: currentcolor;}
.ant-btn-dashed:hover > a:only-child::after, .ant-btn-dashed:focus > a:only-child::after {background: transparent;}
.ant-btn-dashed:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);background: #fff;}
.ant-btn-dashed:active > a:only-child {color: currentcolor;}
.ant-btn-dashed:active > a:only-child::after {background: transparent;}
.ant-btn-dashed[disabled], .ant-btn-dashed[disabled]:hover, .ant-btn-dashed[disabled]:focus, .ant-btn-dashed[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-dashed[disabled] > a:only-child, .ant-btn-dashed[disabled]:hover > a:only-child, .ant-btn-dashed[disabled]:focus > a:only-child, .ant-btn-dashed[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-dashed[disabled] > a:only-child::after, .ant-btn-dashed[disabled]:hover > a:only-child::after, .ant-btn-dashed[disabled]:focus > a:only-child::after, .ant-btn-dashed[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-danger {color: #fff;border-color: @error-color;background: @error-color;box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);}
.ant-btn-danger > a:only-child {color: currentcolor;}
.ant-btn-danger > a:only-child::after {background: transparent;}
.ant-btn-danger:hover, .ant-btn-danger:focus {color: #fff;border-color: color(~`colorPalette("@{error-color}", 5)`);background: color(~`colorPalette("@{error-color}", 5)`);}
.ant-btn-danger:hover > a:only-child, .ant-btn-danger:focus > a:only-child {color: currentcolor;}
.ant-btn-danger:hover > a:only-child::after, .ant-btn-danger:focus > a:only-child::after {background: transparent;}
.ant-btn-danger:active {color: #fff;border-color: color(~`colorPalette("@{error-color}", 7)`);background: color(~`colorPalette("@{error-color}", 7)`);}
.ant-btn-danger:active > a:only-child {color: currentcolor;}
.ant-btn-danger:active > a:only-child::after {background: transparent;}
.ant-btn-danger[disabled], .ant-btn-danger[disabled]:hover, .ant-btn-danger[disabled]:focus, .ant-btn-danger[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-danger[disabled] > a:only-child, .ant-btn-danger[disabled]:hover > a:only-child, .ant-btn-danger[disabled]:focus > a:only-child, .ant-btn-danger[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-danger[disabled] > a:only-child::after, .ant-btn-danger[disabled]:hover > a:only-child::after, .ant-btn-danger[disabled]:focus > a:only-child::after, .ant-btn-danger[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-link {color: @primary-color;border-color: transparent;background: transparent;box-shadow: none;}
.ant-btn-link > a:only-child {color: currentcolor;}
.ant-btn-link > a:only-child::after {background: transparent;}
.ant-btn-link:hover, .ant-btn-link:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);background: transparent;}
.ant-btn-link:hover > a:only-child, .ant-btn-link:focus > a:only-child {color: currentcolor;}
.ant-btn-link:hover > a:only-child::after, .ant-btn-link:focus > a:only-child::after {background: transparent;}
.ant-btn-link:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);background: transparent;}
.ant-btn-link:active > a:only-child {color: currentcolor;}
.ant-btn-link:active > a:only-child::after {background: transparent;}
.ant-btn-link[disabled], .ant-btn-link[disabled]:hover, .ant-btn-link[disabled]:focus, .ant-btn-link[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-link[disabled] > a:only-child, .ant-btn-link[disabled]:hover > a:only-child, .ant-btn-link[disabled]:focus > a:only-child, .ant-btn-link[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-link[disabled] > a:only-child::after, .ant-btn-link[disabled]:hover > a:only-child::after, .ant-btn-link[disabled]:focus > a:only-child::after, .ant-btn-link[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-link:hover {background: transparent;}
.ant-btn-link:hover, .ant-btn-link:focus, .ant-btn-link:active {border-color: transparent;}
.ant-btn-link[disabled], .ant-btn-link[disabled]:hover, .ant-btn-link[disabled]:focus, .ant-btn-link[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: transparent;background: transparent;box-shadow: none;}
.ant-btn-link[disabled] > a:only-child, .ant-btn-link[disabled]:hover > a:only-child, .ant-btn-link[disabled]:focus > a:only-child, .ant-btn-link[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-link[disabled] > a:only-child::after, .ant-btn-link[disabled]:hover > a:only-child::after, .ant-btn-link[disabled]:focus > a:only-child::after, .ant-btn-link[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-text {color: rgba(0, 0, 0, 0.85);border-color: transparent;background: transparent;box-shadow: none;}
.ant-btn-text > a:only-child {color: currentcolor;}
.ant-btn-text > a:only-child::after {background: transparent;}
.ant-btn-text:hover, .ant-btn-text:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);background: transparent;}
.ant-btn-text:hover > a:only-child, .ant-btn-text:focus > a:only-child {color: currentcolor;}
.ant-btn-text:hover > a:only-child::after, .ant-btn-text:focus > a:only-child::after {background: transparent;}
.ant-btn-text:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);background: transparent;}
.ant-btn-text:active > a:only-child {color: currentcolor;}
.ant-btn-text:active > a:only-child::after {background: transparent;}
.ant-btn-text[disabled], .ant-btn-text[disabled]:hover, .ant-btn-text[disabled]:focus, .ant-btn-text[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-text[disabled] > a:only-child, .ant-btn-text[disabled]:hover > a:only-child, .ant-btn-text[disabled]:focus > a:only-child, .ant-btn-text[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-text[disabled] > a:only-child::after, .ant-btn-text[disabled]:hover > a:only-child::after, .ant-btn-text[disabled]:focus > a:only-child::after, .ant-btn-text[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-text:hover, .ant-btn-text:focus {color: rgba(0, 0, 0, 0.85);background: rgba(0, 0, 0, 0.018);border-color: transparent;}
.ant-btn-text:active {color: rgba(0, 0, 0, 0.85);background: fadein(@btn-text-hover-bg, 1%);border-color: transparent;}
.ant-btn-text[disabled], .ant-btn-text[disabled]:hover, .ant-btn-text[disabled]:focus, .ant-btn-text[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: transparent;background: transparent;box-shadow: none;}
.ant-btn-text[disabled] > a:only-child, .ant-btn-text[disabled]:hover > a:only-child, .ant-btn-text[disabled]:focus > a:only-child, .ant-btn-text[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-text[disabled] > a:only-child::after, .ant-btn-text[disabled]:hover > a:only-child::after, .ant-btn-text[disabled]:focus > a:only-child::after, .ant-btn-text[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous {color: @error-color;border-color: @error-color;background: #fff;}
.ant-btn-dangerous > a:only-child {color: currentcolor;}
.ant-btn-dangerous > a:only-child::after {background: transparent;}
.ant-btn-dangerous:hover, .ant-btn-dangerous:focus {color: color(~`colorPalette("@{error-color}", 5)`);border-color: color(~`colorPalette("@{error-color}", 5)`);background: #fff;}
.ant-btn-dangerous:hover > a:only-child, .ant-btn-dangerous:focus > a:only-child {color: currentcolor;}
.ant-btn-dangerous:hover > a:only-child::after, .ant-btn-dangerous:focus > a:only-child::after {background: transparent;}
.ant-btn-dangerous:active {color: color(~`colorPalette("@{error-color}", 7)`);border-color: color(~`colorPalette("@{error-color}", 7)`);background: #fff;}
.ant-btn-dangerous:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous[disabled], .ant-btn-dangerous[disabled]:hover, .ant-btn-dangerous[disabled]:focus, .ant-btn-dangerous[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-dangerous[disabled] > a:only-child, .ant-btn-dangerous[disabled]:hover > a:only-child, .ant-btn-dangerous[disabled]:focus > a:only-child, .ant-btn-dangerous[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous[disabled] > a:only-child::after, .ant-btn-dangerous[disabled]:hover > a:only-child::after, .ant-btn-dangerous[disabled]:focus > a:only-child::after, .ant-btn-dangerous[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-primary {color: #fff;border-color: @error-color;background: @error-color;box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);}
.ant-btn-dangerous.ant-btn-primary > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-primary > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-primary:hover, .ant-btn-dangerous.ant-btn-primary:focus {color: #fff;border-color: color(~`colorPalette("@{error-color}", 5)`);background: color(~`colorPalette("@{error-color}", 5)`);}
.ant-btn-dangerous.ant-btn-primary:hover > a:only-child, .ant-btn-dangerous.ant-btn-primary:focus > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-primary:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-primary:focus > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-primary:active {color: #fff;border-color: color(~`colorPalette("@{error-color}", 7)`);background: color(~`colorPalette("@{error-color}", 7)`);}
.ant-btn-dangerous.ant-btn-primary:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-primary:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-primary[disabled], .ant-btn-dangerous.ant-btn-primary[disabled]:hover, .ant-btn-dangerous.ant-btn-primary[disabled]:focus, .ant-btn-dangerous.ant-btn-primary[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-dangerous.ant-btn-primary[disabled] > a:only-child, .ant-btn-dangerous.ant-btn-primary[disabled]:hover > a:only-child, .ant-btn-dangerous.ant-btn-primary[disabled]:focus > a:only-child, .ant-btn-dangerous.ant-btn-primary[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-primary[disabled] > a:only-child::after, .ant-btn-dangerous.ant-btn-primary[disabled]:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-primary[disabled]:focus > a:only-child::after, .ant-btn-dangerous.ant-btn-primary[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-link {color: @error-color;border-color: transparent;background: transparent;box-shadow: none;}
.ant-btn-dangerous.ant-btn-link > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-link > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-link:hover, .ant-btn-dangerous.ant-btn-link:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);background: transparent;}
.ant-btn-dangerous.ant-btn-link:hover > a:only-child, .ant-btn-dangerous.ant-btn-link:focus > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-link:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-link:focus > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-link:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);background: transparent;}
.ant-btn-dangerous.ant-btn-link:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-link:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-link[disabled], .ant-btn-dangerous.ant-btn-link[disabled]:hover, .ant-btn-dangerous.ant-btn-link[disabled]:focus, .ant-btn-dangerous.ant-btn-link[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-dangerous.ant-btn-link[disabled] > a:only-child, .ant-btn-dangerous.ant-btn-link[disabled]:hover > a:only-child, .ant-btn-dangerous.ant-btn-link[disabled]:focus > a:only-child, .ant-btn-dangerous.ant-btn-link[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-link[disabled] > a:only-child::after, .ant-btn-dangerous.ant-btn-link[disabled]:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-link[disabled]:focus > a:only-child::after, .ant-btn-dangerous.ant-btn-link[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-link:hover, .ant-btn-dangerous.ant-btn-link:focus {color: color(~`colorPalette("@{error-color}", 5)`);border-color: transparent;background: transparent;}
.ant-btn-dangerous.ant-btn-link:hover > a:only-child, .ant-btn-dangerous.ant-btn-link:focus > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-link:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-link:focus > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-link:active {color: color(~`colorPalette("@{error-color}", 7)`);border-color: transparent;background: transparent;}
.ant-btn-dangerous.ant-btn-link:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-link:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-link[disabled], .ant-btn-dangerous.ant-btn-link[disabled]:hover, .ant-btn-dangerous.ant-btn-link[disabled]:focus, .ant-btn-dangerous.ant-btn-link[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: transparent;background: transparent;box-shadow: none;}
.ant-btn-dangerous.ant-btn-link[disabled] > a:only-child, .ant-btn-dangerous.ant-btn-link[disabled]:hover > a:only-child, .ant-btn-dangerous.ant-btn-link[disabled]:focus > a:only-child, .ant-btn-dangerous.ant-btn-link[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-link[disabled] > a:only-child::after, .ant-btn-dangerous.ant-btn-link[disabled]:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-link[disabled]:focus > a:only-child::after, .ant-btn-dangerous.ant-btn-link[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-text {color: @error-color;border-color: transparent;background: transparent;box-shadow: none;}
.ant-btn-dangerous.ant-btn-text > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-text > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-text:hover, .ant-btn-dangerous.ant-btn-text:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);background: transparent;}
.ant-btn-dangerous.ant-btn-text:hover > a:only-child, .ant-btn-dangerous.ant-btn-text:focus > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-text:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-text:focus > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-text:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);background: transparent;}
.ant-btn-dangerous.ant-btn-text:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-text:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-text[disabled], .ant-btn-dangerous.ant-btn-text[disabled]:hover, .ant-btn-dangerous.ant-btn-text[disabled]:focus, .ant-btn-dangerous.ant-btn-text[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-dangerous.ant-btn-text[disabled] > a:only-child, .ant-btn-dangerous.ant-btn-text[disabled]:hover > a:only-child, .ant-btn-dangerous.ant-btn-text[disabled]:focus > a:only-child, .ant-btn-dangerous.ant-btn-text[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-text[disabled] > a:only-child::after, .ant-btn-dangerous.ant-btn-text[disabled]:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-text[disabled]:focus > a:only-child::after, .ant-btn-dangerous.ant-btn-text[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-text:hover, .ant-btn-dangerous.ant-btn-text:focus {color: color(~`colorPalette("@{error-color}", 5)`);border-color: transparent;background: rgba(0, 0, 0, 0.018);}
.ant-btn-dangerous.ant-btn-text:hover > a:only-child, .ant-btn-dangerous.ant-btn-text:focus > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-text:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-text:focus > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-text:active {color: color(~`colorPalette("@{error-color}", 7)`);border-color: transparent;background: fadein(@btn-text-hover-bg, 1%);}
.ant-btn-dangerous.ant-btn-text:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-text:active > a:only-child::after {background: transparent;}
.ant-btn-dangerous.ant-btn-text[disabled], .ant-btn-dangerous.ant-btn-text[disabled]:hover, .ant-btn-dangerous.ant-btn-text[disabled]:focus, .ant-btn-dangerous.ant-btn-text[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: transparent;background: transparent;box-shadow: none;}
.ant-btn-dangerous.ant-btn-text[disabled] > a:only-child, .ant-btn-dangerous.ant-btn-text[disabled]:hover > a:only-child, .ant-btn-dangerous.ant-btn-text[disabled]:focus > a:only-child, .ant-btn-dangerous.ant-btn-text[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-dangerous.ant-btn-text[disabled] > a:only-child::after, .ant-btn-dangerous.ant-btn-text[disabled]:hover > a:only-child::after, .ant-btn-dangerous.ant-btn-text[disabled]:focus > a:only-child::after, .ant-btn-dangerous.ant-btn-text[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-icon-only {border-radius: 2px;}
.ant-btn-icon-only.ant-btn-lg {border-radius: 2px;}
.ant-btn-icon-only.ant-btn-sm {border-radius: 2px;}
.ant-btn-round {border-radius: 32px;}
.ant-btn-round.ant-btn-lg {border-radius: 40px;}
.ant-btn-round.ant-btn-sm {border-radius: 24px;}
.ant-btn-circle {border-radius: 50%;}
.ant-btn-circle.ant-btn-lg {border-radius: 50%;}
.ant-btn-circle.ant-btn-sm {border-radius: 50%;}
.ant-btn::before {background: #fff;border-radius: inherit;}
.ant-btn-group-lg > .ant-btn, .ant-btn-group-lg > span > .ant-btn {border-radius: 0;}
.ant-btn-group-sm > .ant-btn, .ant-btn-group-sm > span > .ant-btn {border-radius: 0;}
.ant-btn-group .ant-btn-primary + .ant-btn:not(.ant-btn-primary):not([disabled]) {border-left-color: transparent;}
.ant-btn-group .ant-btn {border-radius: 0;}
.ant-btn-group > .ant-btn:only-child {border-radius: 2px;}
.ant-btn-group > span:only-child > .ant-btn {border-radius: 2px;}
.ant-btn-group > .ant-btn:first-child:not(:last-child), .ant-btn-group > span:first-child:not(:last-child) > .ant-btn {border-top-left-radius: 2px;border-bottom-left-radius: 2px;}
.ant-btn-group > .ant-btn:last-child:not(:first-child), .ant-btn-group > span:last-child:not(:first-child) > .ant-btn {border-top-right-radius: 2px;border-bottom-right-radius: 2px;}
.ant-btn-group-sm > .ant-btn:only-child {border-radius: 2px;}
.ant-btn-group-sm > span:only-child > .ant-btn {border-radius: 2px;}
.ant-btn-group-sm > .ant-btn:first-child:not(:last-child), .ant-btn-group-sm > span:first-child:not(:last-child) > .ant-btn {border-top-left-radius: 2px;border-bottom-left-radius: 2px;}
.ant-btn-group-sm > .ant-btn:last-child:not(:first-child), .ant-btn-group-sm > span:last-child:not(:first-child) > .ant-btn {border-top-right-radius: 2px;border-bottom-right-radius: 2px;}
.ant-btn-group > .ant-btn-group:not(:first-child):not(:last-child) > .ant-btn {border-radius: 0;}
.ant-btn-group > .ant-btn-group:first-child:not(:last-child) > .ant-btn:last-child {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-btn-group > .ant-btn-group:last-child:not(:first-child) > .ant-btn:first-child {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-btn-group-rtl.ant-btn-group > .ant-btn:first-child:not(:last-child), .ant-btn-group-rtl.ant-btn-group > span:first-child:not(:last-child) > .ant-btn {border-radius: 0 2px 2px 0;}
.ant-btn-group-rtl.ant-btn-group > .ant-btn:last-child:not(:first-child), .ant-btn-group-rtl.ant-btn-group > span:last-child:not(:first-child) > .ant-btn {border-radius: 2px 0 0 2px;}
.ant-btn-group-rtl.ant-btn-group-sm > .ant-btn:first-child:not(:last-child), .ant-btn-group-rtl.ant-btn-group-sm > span:first-child:not(:last-child) > .ant-btn {border-radius: 0 2px 2px 0;}
.ant-btn-group-rtl.ant-btn-group-sm > .ant-btn:last-child:not(:first-child), .ant-btn-group-rtl.ant-btn-group-sm > span:last-child:not(:first-child) > .ant-btn {border-radius: 2px 0 0 2px;}
.ant-btn.ant-btn-background-ghost {color: #fff;border-color: #fff;}
.ant-btn.ant-btn-background-ghost, .ant-btn.ant-btn-background-ghost:hover, .ant-btn.ant-btn-background-ghost:active, .ant-btn.ant-btn-background-ghost:focus {background: transparent;}
.ant-btn.ant-btn-background-ghost:hover, .ant-btn.ant-btn-background-ghost:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-btn.ant-btn-background-ghost:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-btn.ant-btn-background-ghost[disabled] {color: rgba(0, 0, 0, 0.25);background: transparent;border-color: #d9d9d9;}
.ant-btn-background-ghost.ant-btn-primary {color: @primary-color;border-color: @primary-color;}
.ant-btn-background-ghost.ant-btn-primary > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-primary > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-primary:hover, .ant-btn-background-ghost.ant-btn-primary:focus {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-btn-background-ghost.ant-btn-primary:hover > a:only-child, .ant-btn-background-ghost.ant-btn-primary:focus > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-primary:hover > a:only-child::after, .ant-btn-background-ghost.ant-btn-primary:focus > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-primary:active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-btn-background-ghost.ant-btn-primary:active > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-primary:active > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-primary[disabled], .ant-btn-background-ghost.ant-btn-primary[disabled]:hover, .ant-btn-background-ghost.ant-btn-primary[disabled]:focus, .ant-btn-background-ghost.ant-btn-primary[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-background-ghost.ant-btn-primary[disabled] > a:only-child, .ant-btn-background-ghost.ant-btn-primary[disabled]:hover > a:only-child, .ant-btn-background-ghost.ant-btn-primary[disabled]:focus > a:only-child, .ant-btn-background-ghost.ant-btn-primary[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-primary[disabled] > a:only-child::after, .ant-btn-background-ghost.ant-btn-primary[disabled]:hover > a:only-child::after, .ant-btn-background-ghost.ant-btn-primary[disabled]:focus > a:only-child::after, .ant-btn-background-ghost.ant-btn-primary[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-danger {color: @error-color;border-color: @error-color;}
.ant-btn-background-ghost.ant-btn-danger > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-danger > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-danger:hover, .ant-btn-background-ghost.ant-btn-danger:focus {color: color(~`colorPalette("@{error-color}", 5)`);border-color: color(~`colorPalette("@{error-color}", 5)`);}
.ant-btn-background-ghost.ant-btn-danger:hover > a:only-child, .ant-btn-background-ghost.ant-btn-danger:focus > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-danger:hover > a:only-child::after, .ant-btn-background-ghost.ant-btn-danger:focus > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-danger:active {color: color(~`colorPalette("@{error-color}", 7)`);border-color: color(~`colorPalette("@{error-color}", 7)`);}
.ant-btn-background-ghost.ant-btn-danger:active > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-danger:active > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-danger[disabled], .ant-btn-background-ghost.ant-btn-danger[disabled]:hover, .ant-btn-background-ghost.ant-btn-danger[disabled]:focus, .ant-btn-background-ghost.ant-btn-danger[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-background-ghost.ant-btn-danger[disabled] > a:only-child, .ant-btn-background-ghost.ant-btn-danger[disabled]:hover > a:only-child, .ant-btn-background-ghost.ant-btn-danger[disabled]:focus > a:only-child, .ant-btn-background-ghost.ant-btn-danger[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-danger[disabled] > a:only-child::after, .ant-btn-background-ghost.ant-btn-danger[disabled]:hover > a:only-child::after, .ant-btn-background-ghost.ant-btn-danger[disabled]:focus > a:only-child::after, .ant-btn-background-ghost.ant-btn-danger[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous {color: @error-color;border-color: @error-color;}
.ant-btn-background-ghost.ant-btn-dangerous > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-dangerous > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous:hover, .ant-btn-background-ghost.ant-btn-dangerous:focus {color: color(~`colorPalette("@{error-color}", 5)`);border-color: color(~`colorPalette("@{error-color}", 5)`);}
.ant-btn-background-ghost.ant-btn-dangerous:hover > a:only-child, .ant-btn-background-ghost.ant-btn-dangerous:focus > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-dangerous:hover > a:only-child::after, .ant-btn-background-ghost.ant-btn-dangerous:focus > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous:active {color: color(~`colorPalette("@{error-color}", 7)`);border-color: color(~`colorPalette("@{error-color}", 7)`);}
.ant-btn-background-ghost.ant-btn-dangerous:active > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-dangerous:active > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous[disabled], .ant-btn-background-ghost.ant-btn-dangerous[disabled]:hover, .ant-btn-background-ghost.ant-btn-dangerous[disabled]:focus, .ant-btn-background-ghost.ant-btn-dangerous[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-background-ghost.ant-btn-dangerous[disabled] > a:only-child, .ant-btn-background-ghost.ant-btn-dangerous[disabled]:hover > a:only-child, .ant-btn-background-ghost.ant-btn-dangerous[disabled]:focus > a:only-child, .ant-btn-background-ghost.ant-btn-dangerous[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-dangerous[disabled] > a:only-child::after, .ant-btn-background-ghost.ant-btn-dangerous[disabled]:hover > a:only-child::after, .ant-btn-background-ghost.ant-btn-dangerous[disabled]:focus > a:only-child::after, .ant-btn-background-ghost.ant-btn-dangerous[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link {color: @error-color;border-color: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus {color: color(~`colorPalette("@{error-color}", 5)`);border-color: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover > a:only-child, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover > a:only-child::after, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active {color: color(~`colorPalette("@{error-color}", 7)`);border-color: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active > a:only-child::after {background: transparent;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled], .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:hover, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:focus, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:active {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;background: #f5f5f5;box-shadow: none;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled] > a:only-child, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:hover > a:only-child, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:focus > a:only-child, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:active > a:only-child {color: currentcolor;}
.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled] > a:only-child::after, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:hover > a:only-child::after, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:focus > a:only-child::after, .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:active > a:only-child::after {background: transparent;}
.ant-btn-group-rtl.ant-btn-group .ant-btn-primary:last-child:not(:first-child), .ant-btn-group-rtl.ant-btn-group .ant-btn-primary + .ant-btn-primary {border-right-color: color(~`colorPalette("@{primary-color}", 5)`);border-left-color: #d9d9d9;}
.ant-btn-group-rtl.ant-btn-group .ant-btn-primary:last-child:not(:first-child)[disabled], .ant-btn-group-rtl.ant-btn-group .ant-btn-primary + .ant-btn-primary[disabled] {border-right-color: #d9d9d9;border-left-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-picker-calendar {color: rgba(0, 0, 0, 0.85);background: #fff;}
.ant-picker-calendar .ant-picker-panel {background: #fff;border: 0;border-top: 1px solid #f0f0f0;border-radius: 0;}
.ant-picker-calendar-mini {border-radius: 2px;}
.ant-picker-calendar-mini .ant-picker-panel {border-radius: 0 0 2px 2px;}
.ant-picker-calendar-full .ant-picker-panel {background: #fff;border: 0;}
.ant-picker-calendar-full .ant-picker-panel .ant-picker-cell:hover .ant-picker-calendar-date {background: #f5f5f5;}
.ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected .ant-picker-calendar-date, .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected:hover .ant-picker-calendar-date, .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected .ant-picker-calendar-date-today, .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected:hover .ant-picker-calendar-date-today {background: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected .ant-picker-calendar-date .ant-picker-calendar-date-value, .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected:hover .ant-picker-calendar-date .ant-picker-calendar-date-value, .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected .ant-picker-calendar-date-today .ant-picker-calendar-date-value, .ant-picker-calendar-full .ant-picker-panel .ant-picker-cell-selected:hover .ant-picker-calendar-date-today .ant-picker-calendar-date-value {color: @primary-color;}
.ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date {border: 0;border-top: 2px solid #f0f0f0;border-radius: 0;}
.ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-content {color: rgba(0, 0, 0, 0.85);}
.ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-today {border-color: @primary-color;}
.ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-today .ant-picker-calendar-date-value {color: rgba(0, 0, 0, 0.85);}
.ant-card {color: rgba(0, 0, 0, 0.85);background: #fff;border-radius: 2px;}
.ant-card-hoverable:hover {border-color: transparent;box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);}
.ant-card-bordered {border: 1px solid #f0f0f0;}
.ant-card-head {color: rgba(0, 0, 0, 0.85);background: transparent;border-bottom: 1px solid #f0f0f0;border-radius: 2px 2px 0 0;}
.ant-card-head .ant-tabs-top {color: rgba(0, 0, 0, 0.85);}
.ant-card-head .ant-tabs-top-bar {border-bottom: 1px solid #f0f0f0;}
.ant-card-extra {color: rgba(0, 0, 0, 0.85);}
.ant-card-grid {border: 0;border-radius: 0;box-shadow: 1px 0 0 0 #f0f0f0, 0 1px 0 0 #f0f0f0, 1px 1px 0 0 #f0f0f0, 1px 0 0 0 #f0f0f0 inset, 0 1px 0 0 #f0f0f0 inset;}
.ant-card-grid-hoverable:hover {box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);}
.ant-card-cover img {border-radius: 2px 2px 0 0;}
.ant-card-actions {background: #fff;border-top: 1px solid #f0f0f0;}
.ant-card-actions > li {color: rgba(0, 0, 0, 0.45);}
.ant-card-actions > li > span:hover {color: @primary-color;}
.ant-card-actions > li > span a:not(.ant-btn), .ant-card-actions > li > span > .anticon {color: rgba(0, 0, 0, 0.45);}
.ant-card-actions > li > span a:not(.ant-btn):hover, .ant-card-actions > li > span > .anticon:hover {color: @primary-color;}
.ant-card-actions > li:not(:last-child) {border-right: 1px solid #f0f0f0;}
.ant-card-rtl .ant-card-actions > li:not(:last-child) {border-right: none;border-left: 1px solid #f0f0f0;}
.ant-card-type-inner .ant-card-head {background: #fafafa;}
.ant-card-meta-title {color: rgba(0, 0, 0, 0.85);}
.ant-card-meta-description {color: rgba(0, 0, 0, 0.45);}
.ant-card-loading-block {background: linear-gradient(90deg, fade(@card-skeleton-bg, 20%), fade(@card-skeleton-bg, 40%), fade(@card-skeleton-bg, 20%));background-size: 600% 600%;border-radius: 2px;}
.ant-carousel {color: rgba(0, 0, 0, 0.85);}
.ant-carousel .slick-slider {-webkit-tap-highlight-color: transparent;}
.ant-carousel .slick-prev, .ant-carousel .slick-next {color: transparent;background: transparent;border: 0;}
.ant-carousel .slick-prev:hover, .ant-carousel .slick-next:hover, .ant-carousel .slick-prev:focus, .ant-carousel .slick-next:focus {color: transparent;background: transparent;}
.ant-carousel .slick-dots li button {color: transparent;background: #fff;border: 0;border-radius: 1px;}
.ant-carousel .slick-dots li.slick-active button {background: #fff;}
.ant-cascader-checkbox {color: rgba(0, 0, 0, 0.85);}
.ant-cascader-checkbox-wrapper:hover .ant-cascader-checkbox-inner, .ant-cascader-checkbox:hover .ant-cascader-checkbox-inner, .ant-cascader-checkbox-input:focus + .ant-cascader-checkbox-inner {border-color: @primary-color;}
.ant-cascader-checkbox-checked::after {border: 1px solid @primary-color;border-radius: 2px;animation-fill-mode: backwards;}
.ant-cascader-checkbox-inner {background-color: #fff;border: 1px solid #d9d9d9;border-radius: 2px;border-collapse: separate;}
.ant-cascader-checkbox-inner::after {border: 2px solid #fff;border-top: 0;border-left: 0;}
.ant-cascader-checkbox-checked .ant-cascader-checkbox-inner::after {border: 2px solid #fff;border-top: 0;border-left: 0;}
.ant-cascader-checkbox-checked .ant-cascader-checkbox-inner {background-color: @primary-color;border-color: @primary-color;}
.ant-cascader-checkbox-disabled.ant-cascader-checkbox-checked .ant-cascader-checkbox-inner::after {border-color: rgba(0, 0, 0, 0.25);}
.ant-cascader-checkbox-disabled .ant-cascader-checkbox-inner {background-color: #f5f5f5;border-color: #d9d9d9 !important;}
.ant-cascader-checkbox-disabled .ant-cascader-checkbox-inner::after {border-color: #f5f5f5;border-collapse: separate;}
.ant-cascader-checkbox-disabled + span {color: rgba(0, 0, 0, 0.25);}
.ant-cascader-checkbox-wrapper {color: rgba(0, 0, 0, 0.85);}
.ant-cascader-checkbox-group {color: rgba(0, 0, 0, 0.85);}
.ant-cascader-checkbox-indeterminate .ant-cascader-checkbox-inner {background-color: #fff;border-color: #d9d9d9;}
.ant-cascader-checkbox-indeterminate .ant-cascader-checkbox-inner::after {background-color: @primary-color;border: 0;}
.ant-cascader-checkbox-indeterminate.ant-cascader-checkbox-disabled .ant-cascader-checkbox-inner::after {background-color: rgba(0, 0, 0, 0.25);border-color: rgba(0, 0, 0, 0.25);}
.ant-cascader-menu {border-right: 1px solid #f0f0f0;}
.ant-cascader-menu-item:hover {background: #f5f5f5;}
.ant-cascader-menu-item-disabled {color: rgba(0, 0, 0, 0.25);}
.ant-cascader-menu-item-disabled:hover {background: transparent;}
.ant-cascader-menu-empty .ant-cascader-menu-item {color: rgba(0, 0, 0, 0.25);}
.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled), .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {background-color: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon, .ant-cascader-menu-item-loading-icon {color: rgba(0, 0, 0, 0.45);}
.ant-cascader-menu-item-disabled.ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon, .ant-cascader-menu-item-disabled.ant-cascader-menu-item-loading-icon {color: rgba(0, 0, 0, 0.25);}
.ant-cascader-menu-item-keyword {color: #ff4d4f;}
.ant-checkbox {color: rgba(0, 0, 0, 0.85);}
.ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover .ant-checkbox-inner, .ant-checkbox-input:focus + .ant-checkbox-inner {border-color: @primary-color;}
.ant-checkbox-checked::after {border: 1px solid @primary-color;border-radius: 2px;animation-fill-mode: backwards;}
.ant-checkbox-inner {background-color: #fff;border: 1px solid #d9d9d9;border-radius: 2px;border-collapse: separate;}
.ant-checkbox-inner::after {border: 2px solid #fff;border-top: 0;border-left: 0;}
.ant-checkbox-checked .ant-checkbox-inner::after {border: 2px solid #fff;border-top: 0;border-left: 0;}
.ant-checkbox-checked .ant-checkbox-inner {background-color: @primary-color;border-color: @primary-color;}
.ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {border-color: rgba(0, 0, 0, 0.25);}
.ant-checkbox-disabled .ant-checkbox-inner {background-color: #f5f5f5;border-color: #d9d9d9 !important;}
.ant-checkbox-disabled .ant-checkbox-inner::after {border-color: #f5f5f5;border-collapse: separate;}
.ant-checkbox-disabled + span {color: rgba(0, 0, 0, 0.25);}
.ant-checkbox-wrapper {color: rgba(0, 0, 0, 0.85);}
.ant-checkbox-group {color: rgba(0, 0, 0, 0.85);}
.ant-checkbox-indeterminate .ant-checkbox-inner {background-color: #fff;border-color: #d9d9d9;}
.ant-checkbox-indeterminate .ant-checkbox-inner::after {background-color: @primary-color;border: 0;}
.ant-checkbox-indeterminate.ant-checkbox-disabled .ant-checkbox-inner::after {background-color: rgba(0, 0, 0, 0.25);border-color: rgba(0, 0, 0, 0.25);}
.ant-collapse {color: rgba(0, 0, 0, 0.85);background-color: #fafafa;border: 1px solid #d9d9d9;border-bottom: 0;border-radius: 2px;}
.ant-collapse > .ant-collapse-item {border-bottom: 1px solid #d9d9d9;}
.ant-collapse > .ant-collapse-item:last-child, .ant-collapse > .ant-collapse-item:last-child > .ant-collapse-header {border-radius: 0 0 2px 2px;}
.ant-collapse > .ant-collapse-item > .ant-collapse-header {color: rgba(0, 0, 0, 0.85);}
.ant-collapse-content {color: rgba(0, 0, 0, 0.85);background-color: #fff;border-top: 1px solid #d9d9d9;}
.ant-collapse-item:last-child > .ant-collapse-content {border-radius: 0 0 2px 2px;}
.ant-collapse-borderless {background-color: #fafafa;border: 0;}
.ant-collapse-borderless > .ant-collapse-item {border-bottom: 1px solid #d9d9d9;}
.ant-collapse-borderless > .ant-collapse-item:last-child, .ant-collapse-borderless > .ant-collapse-item:last-child .ant-collapse-header {border-radius: 0;}
.ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content {background-color: transparent;border-top: 0;}
.ant-collapse-ghost {background-color: transparent;border: 0;}
.ant-collapse-ghost > .ant-collapse-item {border-bottom: 0;}
.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content {background-color: transparent;border-top: 0;}
.ant-collapse .ant-collapse-item-disabled > .ant-collapse-header, .ant-collapse .ant-collapse-item-disabled > .ant-collapse-header > .arrow {color: rgba(0, 0, 0, 0.25);}
.ant-comment {background-color: inherit;}
.ant-comment-avatar img {border-radius: 50%;}
.ant-comment-content-author-name {color: rgba(0, 0, 0, 0.45);}
.ant-comment-content-author-name > * {color: rgba(0, 0, 0, 0.45);}
.ant-comment-content-author-name > *:hover {color: rgba(0, 0, 0, 0.45);}
.ant-comment-content-author-time {color: #ccc;}
.ant-comment-actions > li {color: rgba(0, 0, 0, 0.45);}
.ant-comment-actions > li > span {color: rgba(0, 0, 0, 0.45);}
.ant-comment-actions > li > span:hover {color: #595959;}
.ant-picker {color: rgba(0, 0, 0, 0.85);background: #fff;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-picker:hover, .ant-picker-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-picker:hover, .ant-input-rtl .ant-picker-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-picker-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-picker-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-picker.ant-picker-disabled {background: #f5f5f5;border-color: #d9d9d9;}
.ant-picker.ant-picker-disabled .ant-picker-suffix {color: rgba(0, 0, 0, 0.25);}
.ant-picker.ant-picker-borderless {background-color: transparent !important;border-color: transparent !important;box-shadow: none !important;}
.ant-picker-input > input {color: rgba(0, 0, 0, 0.85);background-color: #fff;background-image: none;border: 1px solid #d9d9d9;border-radius: 2px;background: transparent;border: 0;}
.ant-picker-input > input::placeholder {color: #bfbfbf;}
.ant-picker-input > input:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-picker-input > input:hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-picker-input > input:focus, .ant-picker-input > input-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-picker-input > input:focus, .ant-input-rtl .ant-picker-input > input-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-picker-input > input-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-picker-input > input-disabled:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-picker-input > input[disabled] {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-picker-input > input[disabled]:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-picker-input > input-borderless, .ant-picker-input > input-borderless:hover, .ant-picker-input > input-borderless:focus, .ant-picker-input > input-borderless-focused, .ant-picker-input > input-borderless-disabled, .ant-picker-input > input-borderless[disabled] {background-color: transparent;border: none;box-shadow: none;}
.ant-picker-input > input:focus {box-shadow: none;}
.ant-picker-input > input[disabled] {background: transparent;}
.ant-picker-input-placeholder > input {color: #bfbfbf;}
.ant-picker-suffix {color: rgba(0, 0, 0, 0.25);}
.ant-picker-clear {color: rgba(0, 0, 0, 0.25);background: #fff;}
.ant-picker-clear:hover {color: rgba(0, 0, 0, 0.45);}
.ant-picker-separator {color: rgba(0, 0, 0, 0.25);}
.ant-picker-focused .ant-picker-separator {color: rgba(0, 0, 0, 0.45);}
.ant-picker-range .ant-picker-active-bar {background: @primary-color;}
.ant-picker-dropdown {color: rgba(0, 0, 0, 0.85);}
.ant-picker-ranges .ant-picker-preset > .ant-tag-blue {color: @primary-color;background: color(~`colorPalette("@{primary-color}", 1)`);border-color: color(~`colorPalette("@{primary-color}", 3)`);}
.ant-picker-range-arrow {box-shadow: 2px -2px 6px rgba(0, 0, 0, 0.06);}
.ant-picker-range-arrow::after {border: 5px solid #f0f0f0;border-color: #fff #fff transparent transparent;}
.ant-picker-panel-container {background: #fff;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-picker-panel-container .ant-picker-panel {background: transparent;border-width: 0 0 1px 0;border-radius: 0;}
.ant-picker-panel-container .ant-picker-panel-focused {border-color: #f0f0f0;}
.ant-picker-panel {background: #fff;border: 1px solid #f0f0f0;border-radius: 2px;}
.ant-picker-panel-focused {border-color: @primary-color;}
.ant-picker-header {color: rgba(0, 0, 0, 0.85);border-bottom: 1px solid #f0f0f0;}
.ant-picker-header button {color: rgba(0, 0, 0, 0.25);background: transparent;border: 0;}
.ant-picker-header > button:hover {color: rgba(0, 0, 0, 0.85);}
.ant-picker-header-view button {color: inherit;}
.ant-picker-header-view button:hover {color: @primary-color;}
.ant-picker-prev-icon::before, .ant-picker-next-icon::before, .ant-picker-super-prev-icon::before, .ant-picker-super-next-icon::before {border: 0 solid currentcolor;border-width: 1.5px 0 0 1.5px;}
.ant-picker-super-prev-icon::after, .ant-picker-super-next-icon::after {border: 0 solid currentcolor;border-width: 1.5px 0 0 1.5px;}
.ant-picker-content {border-collapse: collapse;}
.ant-picker-content th {color: rgba(0, 0, 0, 0.85);}
.ant-picker-cell {color: rgba(0, 0, 0, 0.25);}
.ant-picker-cell-in-view {color: rgba(0, 0, 0, 0.85);}
.ant-picker-cell .ant-picker-cell-inner {border-radius: 2px;}
.ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner, .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end) .ant-picker-cell-inner {background: #f5f5f5;}
.ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {border: 1px solid @primary-color;border-radius: 2px;}
.ant-picker-cell-in-view.ant-picker-cell-in-range::before {background: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner, .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner, .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {color: #fff;background: @primary-color;}
.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before, .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {background: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start-single::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-end-near-hover::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-start-near-hover::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-end-single::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-in-range)::after {border-top: 1px dashed lighten(@primary-color, 20%);border-bottom: 1px dashed lighten(@primary-color, 20%);}
.ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover::before, .ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-hover::before, .ant-picker-cell-in-view.ant-picker-cell-range-end.ant-picker-cell-range-hover::before, .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single).ant-picker-cell-range-hover-start::before, .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single).ant-picker-cell-range-hover-end::before, .ant-picker-panel > :not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start::before, .ant-picker-panel > :not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end::before {background: lighten(@primary-color, 35%);}
.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):not(.ant-picker-cell-range-end) .ant-picker-cell-inner {border-radius: 2px 0 0 2px;}
.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):not(.ant-picker-cell-range-start) .ant-picker-cell-inner {border-radius: 0 2px 2px 0;}
.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner::after, .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner::after {background: lighten(@primary-color, 35%);}
tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:first-child::after,
tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child::after, .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range)::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-start::after {border-left: 1px dashed lighten(@primary-color, 20%);border-top-left-radius: 2px;border-bottom-left-radius: 2px;}
tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:last-child::after,
tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after, .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range)::after, .ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after {border-right: 1px dashed lighten(@primary-color, 20%);border-top-right-radius: 2px;border-bottom-right-radius: 2px;}
.ant-picker-cell-disabled {color: rgba(0, 0, 0, 0.25);}
.ant-picker-cell-disabled .ant-picker-cell-inner {background: transparent;}
.ant-picker-cell-disabled::before {background: rgba(0, 0, 0, 0.04);}
.ant-picker-cell-disabled.ant-picker-cell-today .ant-picker-cell-inner::before {border-color: rgba(0, 0, 0, 0.25);}
.ant-picker-footer {border-bottom: 1px solid transparent;}
.ant-picker-panel .ant-picker-footer {border-top: 1px solid #f0f0f0;}
.ant-picker-footer-extra:not(:last-child) {border-bottom: 1px solid #f0f0f0;}
.ant-picker-today-btn {color: @primary-color;}
.ant-picker-today-btn:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-picker-today-btn:active {color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-picker-today-btn.ant-picker-today-btn-disabled {color: rgba(0, 0, 0, 0.25);}
.ant-picker-year-panel .ant-picker-cell-range-hover-start::after, .ant-picker-quarter-panel .ant-picker-cell-range-hover-start::after, .ant-picker-month-panel .ant-picker-cell-range-hover-start::after {border-left: 1px dashed lighten(@primary-color, 20%);border-radius: 2px 0 0 2px;}
.ant-picker-panel-rtl .ant-picker-year-panel .ant-picker-cell-range-hover-start::after, .ant-picker-panel-rtl .ant-picker-quarter-panel .ant-picker-cell-range-hover-start::after, .ant-picker-panel-rtl .ant-picker-month-panel .ant-picker-cell-range-hover-start::after {border-right: 1px dashed lighten(@primary-color, 20%);border-radius: 0 2px 2px 0;}
.ant-picker-year-panel .ant-picker-cell-range-hover-end::after, .ant-picker-quarter-panel .ant-picker-cell-range-hover-end::after, .ant-picker-month-panel .ant-picker-cell-range-hover-end::after {border-right: 1px dashed lighten(@primary-color, 20%);border-radius: 0 2px 2px 0;}
.ant-picker-panel-rtl .ant-picker-year-panel .ant-picker-cell-range-hover-end::after, .ant-picker-panel-rtl .ant-picker-quarter-panel .ant-picker-cell-range-hover-end::after, .ant-picker-panel-rtl .ant-picker-month-panel .ant-picker-cell-range-hover-end::after {border-left: 1px dashed lighten(@primary-color, 20%);border-radius: 2px 0 0 2px;}
.ant-picker-week-panel .ant-picker-cell:hover .ant-picker-cell-inner, .ant-picker-week-panel .ant-picker-cell-selected .ant-picker-cell-inner, .ant-picker-week-panel .ant-picker-cell .ant-picker-cell-inner {background: transparent !important;}
.ant-picker-week-panel-row:hover td {background: #f5f5f5;}
.ant-picker-week-panel-row-selected td, .ant-picker-week-panel-row-selected:hover td {background: @primary-color;}
.ant-picker-week-panel-row-selected td.ant-picker-cell-week, .ant-picker-week-panel-row-selected:hover td.ant-picker-cell-week {color: rgba(255, 255, 255, 0.5);}
.ant-picker-week-panel-row-selected td.ant-picker-cell-today .ant-picker-cell-inner::before, .ant-picker-week-panel-row-selected:hover td.ant-picker-cell-today .ant-picker-cell-inner::before {border-color: #fff;}
.ant-picker-week-panel-row-selected td .ant-picker-cell-inner, .ant-picker-week-panel-row-selected:hover td .ant-picker-cell-inner {color: #fff;}
.ant-picker-datetime-panel .ant-picker-time-panel {border-left: 1px solid #f0f0f0;}
.ant-picker-time-panel-column:not(:first-child) {border-left: 1px solid #f0f0f0;}
.ant-picker-time-panel-column-active {background: fade(@calendar-item-active-bg, 20%);}
.ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {color: rgba(0, 0, 0, 0.85);border-radius: 0;}
.ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner:hover {background: #f5f5f5;}
.ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {background: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-picker-time-panel-column > li.ant-picker-time-panel-cell-disabled .ant-picker-time-panel-cell-inner {color: rgba(0, 0, 0, 0.25);background: transparent;}
.ant-picker-cell .ant-picker-cell-inner {border-radius: 2px;}
.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):not(.ant-picker-cell-range-end) .ant-picker-cell-inner {border-radius: 0 2px 2px 0;}
.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):not(.ant-picker-cell-range-start) .ant-picker-cell-inner {border-radius: 2px 0 0 2px;}
.ant-picker-panel-rtl tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-selected):first-child::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range)::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-start::after {border-right: 1px dashed lighten(@primary-color, 20%);border-left: none;border-radius: 0 2px 2px 0;}
.ant-picker-panel-rtl tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-selected):last-child::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range)::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after {border-right: none;border-left: 1px dashed lighten(@primary-color, 20%);border-radius: 2px 0 0 2px;}
.ant-picker-panel-rtl tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after, .ant-picker-panel-rtl tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover)::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-end.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover)::after, .ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-start.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover)::after, .ant-picker-panel-rtl tr > .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-start:last-child::after, .ant-picker-panel-rtl tr > .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-end:first-child::after {border-right: 1px dashed lighten(@primary-color, 20%);border-left: 1px dashed lighten(@primary-color, 20%);border-radius: 2px;}
.ant-descriptions-title {color: rgba(0, 0, 0, 0.85);}
.ant-descriptions-extra {color: rgba(0, 0, 0, 0.85);}
.ant-descriptions-view {border-radius: 2px;}
.ant-descriptions-row:last-child {border-bottom: none;}
.ant-descriptions-item-label {color: rgba(0, 0, 0, 0.85);}
.ant-descriptions-item-content {color: rgba(0, 0, 0, 0.85);}
.ant-descriptions-bordered .ant-descriptions-view {border: 1px solid #f0f0f0;}
.ant-descriptions-bordered .ant-descriptions-view > table {border-collapse: collapse;}
.ant-descriptions-bordered .ant-descriptions-item-label, .ant-descriptions-bordered .ant-descriptions-item-content {border-right: 1px solid #f0f0f0;}
.ant-descriptions-bordered .ant-descriptions-item-label:last-child, .ant-descriptions-bordered .ant-descriptions-item-content:last-child {border-right: none;}
.ant-descriptions-bordered .ant-descriptions-item-label {background-color: #fafafa;}
.ant-descriptions-bordered .ant-descriptions-row {border-bottom: 1px solid #f0f0f0;}
.ant-descriptions-bordered .ant-descriptions-row:last-child {border-bottom: none;}
.ant-descriptions-rtl.ant-descriptions-bordered .ant-descriptions-item-label, .ant-descriptions-rtl.ant-descriptions-bordered .ant-descriptions-item-content {border-right: none;border-left: 1px solid #f0f0f0;}
.ant-descriptions-rtl.ant-descriptions-bordered .ant-descriptions-item-label:last-child, .ant-descriptions-rtl.ant-descriptions-bordered .ant-descriptions-item-content:last-child {border-left: none;}
.ant-divider {color: rgba(0, 0, 0, 0.85);border-top: 1px solid rgba(0, 0, 0, 0.06);}
.ant-divider-vertical {border-top: 0;border-left: 1px solid rgba(0, 0, 0, 0.06);}
.ant-divider-horizontal.ant-divider-with-text {color: rgba(0, 0, 0, 0.85);border-top: 0;border-top-color: rgba(0, 0, 0, 0.06);}
.ant-divider-horizontal.ant-divider-with-text::before, .ant-divider-horizontal.ant-divider-with-text::after {border-top: 1px solid transparent;border-top-color: inherit;border-bottom: 0;}
.ant-divider-dashed {background: none;border-color: rgba(0, 0, 0, 0.06);border-style: dashed;border-width: 1px 0 0;}
.ant-divider-horizontal.ant-divider-with-text.ant-divider-dashed::before, .ant-divider-horizontal.ant-divider-with-text.ant-divider-dashed::after {border-style: dashed none none;}
.ant-divider-vertical.ant-divider-dashed {border-width: 0 0 0 1px;}
.ant-divider-plain.ant-divider-with-text {color: rgba(0, 0, 0, 0.85);}
.ant-drawer-left.ant-drawer-open .ant-drawer-content-wrapper {box-shadow: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);}
.ant-drawer-right.ant-drawer-open .ant-drawer-content-wrapper {box-shadow: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);}
.ant-drawer-top.ant-drawer-open .ant-drawer-content-wrapper {box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);}
.ant-drawer-bottom.ant-drawer-open .ant-drawer-content-wrapper {box-shadow: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);}
.ant-drawer-title {color: rgba(0, 0, 0, 0.85);}
.ant-drawer-content {background-color: #fff;background-clip: padding-box;border: 0;}
.ant-drawer-close {color: rgba(0, 0, 0, 0.45);background: transparent;border: 0;}
.ant-drawer-close:focus, .ant-drawer-close:hover {color: rgba(0, 0, 0, 0.75);}
.ant-drawer-header {color: rgba(0, 0, 0, 0.85);background: #fff;border-bottom: 1px solid #f0f0f0;border-radius: 2px 2px 0 0;}
.ant-drawer-header-close-only {border: none;}
.ant-drawer-footer {border-top: 1px solid #f0f0f0;}
.ant-drawer-mask {background-color: rgba(0, 0, 0, 0.45);}
.ant-drawer .ant-picker-clear {background: #fff;}
.ant-dropdown-menu-item.ant-dropdown-menu-item-danger {color: @error-color;}
.ant-dropdown-menu-item.ant-dropdown-menu-item-danger:hover {color: #fff;background-color: @error-color;}
.ant-dropdown {color: rgba(0, 0, 0, 0.85);}
.ant-dropdown-arrow {background: transparent;border-style: solid;border-width: 4.24264069px;}
.ant-dropdown-placement-topCenter > .ant-dropdown-arrow, .ant-dropdown-placement-topLeft > .ant-dropdown-arrow, .ant-dropdown-placement-topRight > .ant-dropdown-arrow {border-color: transparent #fff #fff transparent;box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);}
.ant-dropdown-placement-bottomCenter > .ant-dropdown-arrow, .ant-dropdown-placement-bottomLeft > .ant-dropdown-arrow, .ant-dropdown-placement-bottomRight > .ant-dropdown-arrow {border-color: #fff transparent transparent #fff;box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);}
.ant-dropdown-menu {background-color: #fff;background-clip: padding-box;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-dropdown-menu-item-group-title {color: rgba(0, 0, 0, 0.45);}
.ant-dropdown-menu-submenu-popup {background: transparent;box-shadow: none;}
.ant-dropdown-menu-title-content > a {color: inherit;}
.ant-dropdown-menu-title-content > a:hover {color: inherit;}
.ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title {color: rgba(0, 0, 0, 0.85);}
.ant-dropdown-menu-item-selected, .ant-dropdown-menu-submenu-title-selected {color: @primary-color;background-color: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-dropdown-menu-item:hover, .ant-dropdown-menu-submenu-title:hover {background-color: #f5f5f5;}
.ant-dropdown-menu-item-disabled, .ant-dropdown-menu-submenu-title-disabled {color: rgba(0, 0, 0, 0.25);}
.ant-dropdown-menu-item-disabled:hover, .ant-dropdown-menu-submenu-title-disabled:hover {color: rgba(0, 0, 0, 0.25);background-color: #fff;}
.ant-dropdown-menu-item-divider, .ant-dropdown-menu-submenu-title-divider {background-color: #f0f0f0;}
.ant-dropdown-menu-item .ant-dropdown-menu-submenu-expand-icon .ant-dropdown-menu-submenu-arrow-icon, .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-expand-icon .ant-dropdown-menu-submenu-arrow-icon {color: rgba(0, 0, 0, 0.45);}
.ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title, .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon {color: rgba(0, 0, 0, 0.25);background-color: #fff;}
.ant-dropdown-menu-submenu-selected .ant-dropdown-menu-submenu-title {color: @primary-color;}
.ant-dropdown-menu-dark, .ant-dropdown-menu-dark .ant-dropdown-menu {background: @layout-header-background;}
.ant-dropdown-menu-dark .ant-dropdown-menu-item, .ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title, .ant-dropdown-menu-dark .ant-dropdown-menu-item > a, .ant-dropdown-menu-dark .ant-dropdown-menu-item > .anticon + span > a {color: rgba(255, 255, 255, 0.65);}
.ant-dropdown-menu-dark .ant-dropdown-menu-item .ant-dropdown-menu-submenu-arrow::after, .ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow::after, .ant-dropdown-menu-dark .ant-dropdown-menu-item > a .ant-dropdown-menu-submenu-arrow::after, .ant-dropdown-menu-dark .ant-dropdown-menu-item > .anticon + span > a .ant-dropdown-menu-submenu-arrow::after {color: rgba(255, 255, 255, 0.65);}
.ant-dropdown-menu-dark .ant-dropdown-menu-item:hover, .ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title:hover, .ant-dropdown-menu-dark .ant-dropdown-menu-item > a:hover, .ant-dropdown-menu-dark .ant-dropdown-menu-item > .anticon + span > a:hover {color: #fff;background: transparent;}
.ant-dropdown-menu-dark .ant-dropdown-menu-item-selected, .ant-dropdown-menu-dark .ant-dropdown-menu-item-selected:hover, .ant-dropdown-menu-dark .ant-dropdown-menu-item-selected > a {color: #fff;background: @primary-color;}
.ant-empty-normal {color: rgba(0, 0, 0, 0.25);}
.ant-empty-small {color: rgba(0, 0, 0, 0.25);}
.ant-empty-img-default-ellipse {fill: #f5f5f5;fill-opacity: 0.8;}
.ant-empty-img-default-path-1 {fill: #aeb8c2;}
.ant-empty-img-default-path-3 {fill: #f5f5f7;}
.ant-empty-img-default-path-4 {fill: #dce0e6;}
.ant-empty-img-default-path-5 {fill: #dce0e6;}
.ant-empty-img-default-g {fill: #fff;}
.ant-empty-img-simple-ellipse {fill: #f5f5f5;}
.ant-empty-img-simple-g {stroke: #d9d9d9;}
.ant-empty-img-simple-path {fill: #fafafa;}
.ant-form-item .ant-upload {background: transparent;}
.ant-form-item .ant-upload.ant-upload-drag {background: #fafafa;}
.ant-form-item-explain-error {color: @error-color;}
.ant-form-item-explain-warning {color: @warning-color;}
.ant-form-item-has-success.ant-form-item-has-feedback .ant-form-item-children-icon {color: @success-color;}
.ant-form-item-has-warning .ant-form-item-split {color: @warning-color;}
.ant-form-item-has-warning :not(.ant-input-disabled):not(.ant-input-borderless).ant-input, .ant-form-item-has-warning :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper, .ant-form-item-has-warning :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper, .ant-form-item-has-warning :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover, .ant-form-item-has-warning :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:hover, .ant-form-item-has-warning :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper:hover {background-color: #fff;border-color: @warning-color;}
.ant-form-item-has-warning :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:focus, .ant-form-item-has-warning :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:focus, .ant-form-item-has-warning :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper:focus, .ant-form-item-has-warning :not(.ant-input-disabled):not(.ant-input-borderless).ant-input-focused, .ant-form-item-has-warning :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper-focused, .ant-form-item-has-warning :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper-focused {border-color: color(~`colorPalette("@{warning-color}", 5)`);box-shadow: 0 0 0 2px rgba(229, 32, 102, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-warning .ant-calendar-picker-open .ant-calendar-picker-input {border-color: color(~`colorPalette("@{warning-color}", 5)`);box-shadow: 0 0 0 2px rgba(229, 32, 102, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-warning .ant-input-prefix, .ant-form-item-has-warning .ant-input-number-prefix {color: @warning-color;}
.ant-form-item-has-warning .ant-input-group-addon, .ant-form-item-has-warning .ant-input-number-group-addon {color: @warning-color;border-color: @warning-color;}
.ant-form-item-has-warning .has-feedback {color: @warning-color;}
.ant-form-item-has-warning.ant-form-item-has-feedback .ant-form-item-children-icon {color: @warning-color;}
.ant-form-item-has-warning .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input) .ant-select-selector {background-color: #fff;border-color: @warning-color !important;}
.ant-form-item-has-warning .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-open .ant-select-selector, .ant-form-item-has-warning .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused .ant-select-selector {border-color: color(~`colorPalette("@{warning-color}", 5)`);box-shadow: 0 0 0 2px rgba(229, 32, 102, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-warning .ant-input-number, .ant-form-item-has-warning .ant-picker {background-color: #fff;border-color: @warning-color;}
.ant-form-item-has-warning .ant-input-number-focused, .ant-form-item-has-warning .ant-picker-focused, .ant-form-item-has-warning .ant-input-number:focus, .ant-form-item-has-warning .ant-picker:focus {border-color: color(~`colorPalette("@{warning-color}", 5)`);box-shadow: 0 0 0 2px rgba(229, 32, 102, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-warning .ant-input-number:not([disabled]):hover, .ant-form-item-has-warning .ant-picker:not([disabled]):hover {background-color: #fff;border-color: @warning-color;}
.ant-form-item-has-warning .ant-cascader-picker:focus .ant-cascader-input {border-color: color(~`colorPalette("@{warning-color}", 5)`);box-shadow: 0 0 0 2px rgba(229, 32, 102, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-error .ant-form-item-split {color: @error-color;}
.ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input, .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper, .ant-form-item-has-error :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper, .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover, .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:hover, .ant-form-item-has-error :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper:hover {background-color: #fff;border-color: @error-color;}
.ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:focus, .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:focus, .ant-form-item-has-error :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper:focus, .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input-focused, .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper-focused, .ant-form-item-has-error :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper-focused {border-color: color(~`colorPalette("@{error-color}", 5)`);box-shadow: 0 0 0 2px rgba(198, 203, 20, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-error .ant-calendar-picker-open .ant-calendar-picker-input {border-color: color(~`colorPalette("@{error-color}", 5)`);box-shadow: 0 0 0 2px rgba(198, 203, 20, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-error .ant-input-prefix, .ant-form-item-has-error .ant-input-number-prefix {color: @error-color;}
.ant-form-item-has-error .ant-input-group-addon, .ant-form-item-has-error .ant-input-number-group-addon {color: @error-color;border-color: @error-color;}
.ant-form-item-has-error .has-feedback {color: @error-color;}
.ant-form-item-has-error.ant-form-item-has-feedback .ant-form-item-children-icon {color: @error-color;}
.ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input) .ant-select-selector {background-color: #fff;border-color: @error-color !important;}
.ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-open .ant-select-selector, .ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused .ant-select-selector {border-color: color(~`colorPalette("@{error-color}", 5)`);box-shadow: 0 0 0 2px rgba(198, 203, 20, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-error .ant-input-group-addon .ant-select.ant-select-single:not(.ant-select-customize-input) .ant-select-selector, .ant-form-item-has-error .ant-input-number-group-addon .ant-select.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {background-color: inherit;border: 0;box-shadow: none;}
.ant-form-item-has-error .ant-select.ant-select-auto-complete .ant-input:focus {border-color: @error-color;}
.ant-form-item-has-error .ant-input-number, .ant-form-item-has-error .ant-picker {background-color: #fff;border-color: @error-color;}
.ant-form-item-has-error .ant-input-number-focused, .ant-form-item-has-error .ant-picker-focused, .ant-form-item-has-error .ant-input-number:focus, .ant-form-item-has-error .ant-picker:focus {border-color: color(~`colorPalette("@{error-color}", 5)`);box-shadow: 0 0 0 2px rgba(198, 203, 20, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-error .ant-input-number:not([disabled]):hover, .ant-form-item-has-error .ant-picker:not([disabled]):hover {background-color: #fff;border-color: @error-color;}
.ant-form-item-has-error .ant-mention-wrapper .ant-mention-editor, .ant-form-item-has-error .ant-mention-wrapper .ant-mention-editor:not([disabled]):hover {background-color: #fff;border-color: @error-color;}
.ant-form-item-has-error .ant-mention-wrapper.ant-mention-active:not([disabled]) .ant-mention-editor, .ant-form-item-has-error .ant-mention-wrapper .ant-mention-editor:not([disabled]):focus {border-color: color(~`colorPalette("@{error-color}", 5)`);box-shadow: 0 0 0 2px rgba(198, 203, 20, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-error .ant-cascader-picker:hover .ant-cascader-picker-label:hover + .ant-cascader-input.ant-input {border-color: @error-color;}
.ant-form-item-has-error .ant-cascader-picker:focus .ant-cascader-input {background-color: #fff;border-color: color(~`colorPalette("@{error-color}", 5)`);box-shadow: 0 0 0 2px rgba(198, 203, 20, 0.2);border-right-width: 1px !important;}
.ant-form-item-has-error .ant-transfer-list {border-color: @error-color;}
.ant-form-item-has-error .ant-transfer-list-search:not([disabled]) {border-color: #d9d9d9;}
.ant-form-item-has-error .ant-transfer-list-search:not([disabled]):hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-form-item-has-error .ant-transfer-list-search:not([disabled]):hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-form-item-has-error .ant-transfer-list-search:not([disabled]):focus {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-form-item-has-error .ant-transfer-list-search:not([disabled]):focus {border-right-width: 0;border-left-width: 1px !important;}
.ant-form-item-has-error .ant-radio-button-wrapper {border-color: @error-color !important;}
.ant-form-item-has-error .ant-radio-button-wrapper:not(:first-child)::before {background-color: @error-color;}
.ant-form-item-has-error .ant-mentions {border-color: @error-color !important;}
.ant-form-item-has-error .ant-mentions-focused, .ant-form-item-has-error .ant-mentions:focus {border-color: color(~`colorPalette("@{error-color}", 5)`);box-shadow: 0 0 0 2px rgba(198, 203, 20, 0.2);border-right-width: 1px !important;}
.ant-form-item-is-validating.ant-form-item-has-feedback .ant-form-item-children-icon {color: @primary-color;}
.ant-form {color: rgba(0, 0, 0, 0.85);}
.ant-form legend {color: rgba(0, 0, 0, 0.45);border: 0;border-bottom: 1px solid #d9d9d9;}
.ant-form output {color: rgba(0, 0, 0, 0.85);}
.ant-form-item {color: rgba(0, 0, 0, 0.85);}
.ant-form-item-label > label {color: rgba(0, 0, 0, 0.85);}
.ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {color: #ff4d4f;}
.ant-form-item-label > label .ant-form-item-optional {color: rgba(0, 0, 0, 0.45);}
.ant-form-item-label > label .ant-form-item-tooltip {color: rgba(0, 0, 0, 0.45);}
.ant-form-item-explain, .ant-form-item-extra {color: rgba(0, 0, 0, 0.45);}
.ant-image-img-placeholder {background-color: #f5f5f5;background-repeat: no-repeat;background-position: center center;background-size: 30%;}
.ant-image-mask {color: #fff;background: rgba(0, 0, 0, 0.5);}
.ant-image-preview-mask {background-color: rgba(0, 0, 0, 0.45);}
.ant-image-preview-operations {color: rgba(0, 0, 0, 0.85);color: rgba(255, 255, 255, 0.85);background: fade(@modal-mask-bg, 10%);}
.ant-image-preview-operations-operation-disabled {color: fade(@image-preview-operation-color, 25%);}
.ant-image-preview-switch-left, .ant-image-preview-switch-right {color: rgba(255, 255, 255, 0.85);background: fade(@modal-mask-bg, 10%);border-radius: 50%;}
.ant-image-preview-switch-left-disabled, .ant-image-preview-switch-right-disabled {color: fade(@image-preview-operation-color, 25%);}
.ant-input-affix-wrapper {color: rgba(0, 0, 0, 0.85);background-color: #fff;background-image: none;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-input-affix-wrapper::placeholder {color: #bfbfbf;}
.ant-input-affix-wrapper:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-affix-wrapper:hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-affix-wrapper:focus, .ant-input-affix-wrapper-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-affix-wrapper:focus, .ant-input-rtl .ant-input-affix-wrapper-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-affix-wrapper-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input-affix-wrapper-disabled:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input-affix-wrapper[disabled] {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input-affix-wrapper[disabled]:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input-affix-wrapper-borderless, .ant-input-affix-wrapper-borderless:hover, .ant-input-affix-wrapper-borderless:focus, .ant-input-affix-wrapper-borderless-focused, .ant-input-affix-wrapper-borderless-disabled, .ant-input-affix-wrapper-borderless[disabled] {background-color: transparent;border: none;box-shadow: none;}
.ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-affix-wrapper-disabled .ant-input[disabled] {background: transparent;}
.ant-input-affix-wrapper > input.ant-input {border: none;}
.ant-input-affix-wrapper > input.ant-input:focus {box-shadow: none !important;}
.ant-input-show-count-suffix {color: rgba(0, 0, 0, 0.45);}
.anticon.ant-input-clear-icon {color: rgba(0, 0, 0, 0.25);}
.anticon.ant-input-clear-icon:hover {color: rgba(0, 0, 0, 0.45);}
.anticon.ant-input-clear-icon:active {color: rgba(0, 0, 0, 0.85);}
.ant-input-affix-wrapper-textarea-with-clear-btn {border: 0 !important;}
.ant-input {color: rgba(0, 0, 0, 0.85);background-color: #fff;background-image: none;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-input::placeholder {color: #bfbfbf;}
.ant-input:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-input:hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-input:focus, .ant-input-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-input:focus, .ant-input-rtl .ant-input-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input-disabled:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input[disabled] {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input[disabled]:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input-borderless, .ant-input-borderless:hover, .ant-input-borderless:focus, .ant-input-borderless-focused, .ant-input-borderless-disabled, .ant-input-borderless[disabled] {background-color: transparent;border: none;box-shadow: none;}
.ant-input-group {color: rgba(0, 0, 0, 0.85);border-collapse: separate;border-spacing: 0;}
.ant-input-group-addon:not(:first-child):not(:last-child), .ant-input-group-wrap:not(:first-child):not(:last-child), .ant-input-group > .ant-input:not(:first-child):not(:last-child) {border-radius: 0;}
.ant-input-group .ant-input:focus {border-right-width: 1px;}
.ant-input-group .ant-input:hover {border-right-width: 1px;}
.ant-input-group-addon {color: rgba(0, 0, 0, 0.85);background-color: #fafafa;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-input-group-addon .ant-select.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {background-color: inherit;border: 1px solid transparent;box-shadow: none;}
.ant-input-group-addon .ant-select-open .ant-select-selector, .ant-input-group-addon .ant-select-focused .ant-select-selector {color: @primary-color;}
.ant-input-group-addon .ant-cascader-picker {background-color: transparent;}
.ant-input-group-addon .ant-cascader-picker .ant-cascader-input {border: 0;box-shadow: none;}
.ant-input-group > .ant-input:first-child, .ant-input-group-addon:first-child {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-input-group > .ant-input:first-child .ant-select .ant-select-selector, .ant-input-group-addon:first-child .ant-select .ant-select-selector {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-input-group > .ant-input-affix-wrapper:not(:first-child) .ant-input {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-input-group > .ant-input-affix-wrapper:not(:last-child) .ant-input {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-input-group-addon:first-child {border-right: 0;}
.ant-input-group-addon:last-child {border-left: 0;}
.ant-input-group > .ant-input:last-child, .ant-input-group-addon:last-child {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-input-group > .ant-input:last-child .ant-select .ant-select-selector, .ant-input-group-addon:last-child .ant-select .ant-select-selector {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-input-group .ant-input-affix-wrapper:not(:last-child) {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-input-search .ant-input-group .ant-input-affix-wrapper:not(:last-child) {border-top-left-radius: 2px;border-bottom-left-radius: 2px;}
.ant-input-group .ant-input-affix-wrapper:not(:first-child), .ant-input-search .ant-input-group .ant-input-affix-wrapper:not(:first-child) {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child), .ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child), .ant-input-group.ant-input-group-compact > .ant-input:not(:first-child):not(:last-child) {border-right-width: 1px;}
.ant-input-group.ant-input-group-compact > * {border-radius: 0;}
.ant-input-group.ant-input-group-compact > *:not(:last-child) {border-right-width: 1px;}
.ant-input-group.ant-input-group-compact > .ant-select > .ant-select-selector, .ant-input-group.ant-input-group-compact > .ant-select-auto-complete .ant-input, .ant-input-group.ant-input-group-compact > .ant-cascader-picker .ant-input, .ant-input-group.ant-input-group-compact > .ant-input-group-wrapper .ant-input {border-right-width: 1px;border-radius: 0;}
.ant-input-group.ant-input-group-compact > *:first-child, .ant-input-group.ant-input-group-compact > .ant-select:first-child > .ant-select-selector, .ant-input-group.ant-input-group-compact > .ant-select-auto-complete:first-child .ant-input, .ant-input-group.ant-input-group-compact > .ant-cascader-picker:first-child .ant-input {border-top-left-radius: 2px;border-bottom-left-radius: 2px;}
.ant-input-group.ant-input-group-compact > *:last-child, .ant-input-group.ant-input-group-compact > .ant-select:last-child > .ant-select-selector, .ant-input-group.ant-input-group-compact > .ant-cascader-picker:last-child .ant-input, .ant-input-group.ant-input-group-compact > .ant-cascader-picker-focused:last-child .ant-input {border-right-width: 1px;border-top-right-radius: 2px;border-bottom-right-radius: 2px;}
.ant-input-group.ant-input-group-compact .ant-input-group-wrapper + .ant-input-group-wrapper .ant-input-affix-wrapper {border-radius: 0;}
.ant-input-group.ant-input-group-compact .ant-input-group-wrapper:not(:last-child).ant-input-search > .ant-input-group > .ant-input-group-addon > .ant-input-search-button {border-radius: 0;}
.ant-input-group.ant-input-group-compact .ant-input-group-wrapper:not(:last-child).ant-input-search > .ant-input-group > .ant-input {border-radius: 2px 0 0 2px;}
.ant-input-group > .ant-input-rtl:first-child, .ant-input-group-rtl .ant-input-group-addon:first-child {border-radius: 0 2px 2px 0;}
.ant-input-group-rtl .ant-input-group-addon:first-child {border-right: 1px solid #d9d9d9;border-left: 0;}
.ant-input-group-rtl .ant-input-group-addon:last-child {border-right: 0;border-left: 1px solid #d9d9d9;}
.ant-input-group-rtl.ant-input-group > .ant-input:last-child, .ant-input-group-rtl.ant-input-group-addon:last-child {border-radius: 2px 0 0 2px;}
.ant-input-group-rtl.ant-input-group .ant-input-affix-wrapper:not(:first-child) {border-radius: 2px 0 0 2px;}
.ant-input-group-rtl.ant-input-group .ant-input-affix-wrapper:not(:last-child) {border-radius: 0 2px 2px 0;}
.ant-input-group-rtl.ant-input-group.ant-input-group-compact > *:not(:last-child) {border-left-width: 1px;}
.ant-input-group-rtl.ant-input-group.ant-input-group-compact > *:first-child, .ant-input-group-rtl.ant-input-group.ant-input-group-compact > .ant-select:first-child > .ant-select-selector, .ant-input-group-rtl.ant-input-group.ant-input-group-compact > .ant-select-auto-complete:first-child .ant-input, .ant-input-group-rtl.ant-input-group.ant-input-group-compact > .ant-cascader-picker:first-child .ant-input {border-radius: 0 2px 2px 0;}
.ant-input-group-rtl.ant-input-group.ant-input-group-compact > *:last-child, .ant-input-group-rtl.ant-input-group.ant-input-group-compact > .ant-select:last-child > .ant-select-selector, .ant-input-group-rtl.ant-input-group.ant-input-group-compact > .ant-select-auto-complete:last-child .ant-input, .ant-input-group-rtl.ant-input-group.ant-input-group-compact > .ant-cascader-picker:last-child .ant-input, .ant-input-group-rtl.ant-input-group.ant-input-group-compact > .ant-cascader-picker-focused:last-child .ant-input {border-left-width: 1px;border-radius: 2px 0 0 2px;}
.ant-input-group.ant-input-group-compact .ant-input-group-wrapper-rtl:not(:last-child).ant-input-search > .ant-input-group > .ant-input {border-radius: 0 2px 2px 0;}
.ant-input-password-icon {color: rgba(0, 0, 0, 0.45);}
.ant-input-password-icon:hover {color: rgba(0, 0, 0, 0.85);}
.ant-input-textarea-show-count::after {color: rgba(0, 0, 0, 0.45);}
.ant-input-search .ant-input:hover, .ant-input-search .ant-input:focus {border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-input-search .ant-input:hover + .ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary), .ant-input-search .ant-input:focus + .ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary) {border-left-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-input-search .ant-input-affix-wrapper {border-radius: 0;}
.ant-input-search > .ant-input-group > .ant-input-group-addon:last-child {border: 0;}
.ant-input-search > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button {border-radius: 0 2px 2px 0;}
.ant-input-search > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary) {color: rgba(0, 0, 0, 0.45);}
.ant-input-affix-wrapper.ant-input-affix-wrapper-rtl > input.ant-input {border: none;}
.ant-input-search-rtl .ant-input:hover + .ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary), .ant-input-search-rtl .ant-input:focus + .ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary) {border-right-color: color(~`colorPalette("@{primary-color}", 5)`);border-left-color: #d9d9d9;}
.ant-input-search-rtl > .ant-input-group > .ant-input-affix-wrapper:hover, .ant-input-search-rtl > .ant-input-group > .ant-input-affix-wrapper-focused {border-right-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-input-search-rtl > .ant-input-group > .ant-input-group-addon .ant-input-search-button {border-radius: 2px 0 0 2px;}
.ant-input-number-affix-wrapper {color: rgba(0, 0, 0, 0.85);background-color: #fff;background-image: none;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-input-number-affix-wrapper::placeholder {color: #bfbfbf;}
.ant-input-number-affix-wrapper:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-number-affix-wrapper:hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-number-affix-wrapper:focus, .ant-input-number-affix-wrapper-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-number-affix-wrapper:focus, .ant-input-rtl .ant-input-number-affix-wrapper-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-number-affix-wrapper-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input-number-affix-wrapper-disabled:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input-number-affix-wrapper[disabled] {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input-number-affix-wrapper[disabled]:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input-number-affix-wrapper-borderless, .ant-input-number-affix-wrapper-borderless:hover, .ant-input-number-affix-wrapper-borderless:focus, .ant-input-number-affix-wrapper-borderless-focused, .ant-input-number-affix-wrapper-borderless-disabled, .ant-input-number-affix-wrapper-borderless[disabled] {background-color: transparent;border: none;box-shadow: none;}
.ant-input-number-affix-wrapper:not(.ant-input-number-affix-wrapper-disabled):hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-number-affix-wrapper:not(.ant-input-number-affix-wrapper-disabled):hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-number-affix-wrapper-disabled .ant-input-number[disabled] {background: transparent;}
.ant-input-number-affix-wrapper > div.ant-input-number {border: none;}
.ant-input-number-affix-wrapper > div.ant-input-number.ant-input-number-focused {box-shadow: none !important;}
.ant-input-number {color: rgba(0, 0, 0, 0.85);background-color: #fff;background-image: none;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-input-number::placeholder {color: #bfbfbf;}
.ant-input-number:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-number:hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-number:focus, .ant-input-number-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-number:focus, .ant-input-rtl .ant-input-number-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-number-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input-number-disabled:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input-number[disabled] {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input-number[disabled]:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input-number-borderless, .ant-input-number-borderless:hover, .ant-input-number-borderless:focus, .ant-input-number-borderless-focused, .ant-input-number-borderless-disabled, .ant-input-number-borderless[disabled] {background-color: transparent;border: none;box-shadow: none;}
.ant-input-number-group {color: rgba(0, 0, 0, 0.85);border-collapse: separate;border-spacing: 0;}
.ant-input-number-group-addon:not(:first-child):not(:last-child), .ant-input-number-group-wrap:not(:first-child):not(:last-child), .ant-input-number-group > .ant-input-number:not(:first-child):not(:last-child) {border-radius: 0;}
.ant-input-number-group .ant-input-number:focus {border-right-width: 1px;}
.ant-input-number-group .ant-input-number:hover {border-right-width: 1px;}
.ant-input-number-group-addon {color: rgba(0, 0, 0, 0.85);background-color: #fafafa;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-input-number-group-addon .ant-select.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {background-color: inherit;border: 1px solid transparent;box-shadow: none;}
.ant-input-number-group-addon .ant-select-open .ant-select-selector, .ant-input-number-group-addon .ant-select-focused .ant-select-selector {color: @primary-color;}
.ant-input-number-group-addon .ant-cascader-picker {background-color: transparent;}
.ant-input-number-group-addon .ant-cascader-picker .ant-cascader-input {border: 0;box-shadow: none;}
.ant-input-number-group > .ant-input-number:first-child, .ant-input-number-group-addon:first-child {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-input-number-group > .ant-input-number:first-child .ant-select .ant-select-selector, .ant-input-number-group-addon:first-child .ant-select .ant-select-selector {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-input-number-group > .ant-input-number-affix-wrapper:not(:first-child) .ant-input-number {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-input-number-group > .ant-input-number-affix-wrapper:not(:last-child) .ant-input-number {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-input-number-group-addon:first-child {border-right: 0;}
.ant-input-number-group-addon:last-child {border-left: 0;}
.ant-input-number-group > .ant-input-number:last-child, .ant-input-number-group-addon:last-child {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-input-number-group > .ant-input-number:last-child .ant-select .ant-select-selector, .ant-input-number-group-addon:last-child .ant-select .ant-select-selector {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-input-number-group .ant-input-number-affix-wrapper:not(:last-child) {border-top-right-radius: 0;border-bottom-right-radius: 0;}
.ant-input-search .ant-input-number-group .ant-input-number-affix-wrapper:not(:last-child) {border-top-left-radius: 2px;border-bottom-left-radius: 2px;}
.ant-input-number-group .ant-input-number-affix-wrapper:not(:first-child), .ant-input-search .ant-input-number-group .ant-input-number-affix-wrapper:not(:first-child) {border-top-left-radius: 0;border-bottom-left-radius: 0;}
.ant-input-number-group.ant-input-number-group-compact-addon:not(:first-child):not(:last-child), .ant-input-number-group.ant-input-number-group-compact-wrap:not(:first-child):not(:last-child), .ant-input-number-group.ant-input-number-group-compact > .ant-input-number:not(:first-child):not(:last-child) {border-right-width: 1px;}
.ant-input-number-group.ant-input-number-group-compact > * {border-radius: 0;}
.ant-input-number-group.ant-input-number-group-compact > *:not(:last-child) {border-right-width: 1px;}
.ant-input-number-group.ant-input-number-group-compact > .ant-select > .ant-select-selector, .ant-input-number-group.ant-input-number-group-compact > .ant-select-auto-complete .ant-input, .ant-input-number-group.ant-input-number-group-compact > .ant-cascader-picker .ant-input, .ant-input-number-group.ant-input-number-group-compact > .ant-input-group-wrapper .ant-input {border-right-width: 1px;border-radius: 0;}
.ant-input-number-group.ant-input-number-group-compact > *:first-child, .ant-input-number-group.ant-input-number-group-compact > .ant-select:first-child > .ant-select-selector, .ant-input-number-group.ant-input-number-group-compact > .ant-select-auto-complete:first-child .ant-input, .ant-input-number-group.ant-input-number-group-compact > .ant-cascader-picker:first-child .ant-input {border-top-left-radius: 2px;border-bottom-left-radius: 2px;}
.ant-input-number-group.ant-input-number-group-compact > *:last-child, .ant-input-number-group.ant-input-number-group-compact > .ant-select:last-child > .ant-select-selector, .ant-input-number-group.ant-input-number-group-compact > .ant-cascader-picker:last-child .ant-input, .ant-input-number-group.ant-input-number-group-compact > .ant-cascader-picker-focused:last-child .ant-input {border-right-width: 1px;border-top-right-radius: 2px;border-bottom-right-radius: 2px;}
.ant-input-number-group.ant-input-number-group-compact .ant-input-group-wrapper + .ant-input-group-wrapper .ant-input-affix-wrapper {border-radius: 0;}
.ant-input-number-group.ant-input-number-group-compact .ant-input-group-wrapper:not(:last-child).ant-input-search > .ant-input-group > .ant-input-group-addon > .ant-input-search-button {border-radius: 0;}
.ant-input-number-group.ant-input-number-group-compact .ant-input-group-wrapper:not(:last-child).ant-input-search > .ant-input-group > .ant-input {border-radius: 2px 0 0 2px;}
.ant-input-number-group > .ant-input-number-rtl:first-child, .ant-input-number-group-rtl .ant-input-number-group-addon:first-child {border-radius: 0 2px 2px 0;}
.ant-input-number-group-rtl .ant-input-number-group-addon:first-child {border-right: 1px solid #d9d9d9;border-left: 0;}
.ant-input-number-group-rtl .ant-input-number-group-addon:last-child {border-right: 0;border-left: 1px solid #d9d9d9;}
.ant-input-number-group-rtl.ant-input-number-group > .ant-input-number:last-child, .ant-input-number-group-rtl.ant-input-number-group-addon:last-child {border-radius: 2px 0 0 2px;}
.ant-input-number-group-rtl.ant-input-number-group .ant-input-number-affix-wrapper:not(:first-child) {border-radius: 2px 0 0 2px;}
.ant-input-number-group-rtl.ant-input-number-group .ant-input-number-affix-wrapper:not(:last-child) {border-radius: 0 2px 2px 0;}
.ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > *:not(:last-child) {border-left-width: 1px;}
.ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > *:first-child, .ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > .ant-select:first-child > .ant-select-selector, .ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > .ant-select-auto-complete:first-child .ant-input, .ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > .ant-cascader-picker:first-child .ant-input {border-radius: 0 2px 2px 0;}
.ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > *:last-child, .ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > .ant-select:last-child > .ant-select-selector, .ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > .ant-select-auto-complete:last-child .ant-input, .ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > .ant-cascader-picker:last-child .ant-input, .ant-input-number-group-rtl.ant-input-number-group.ant-input-number-group-compact > .ant-cascader-picker-focused:last-child .ant-input {border-left-width: 1px;border-radius: 2px 0 0 2px;}
.ant-input-number-group.ant-input-number-group-compact .ant-input-group-wrapper-rtl:not(:last-child).ant-input-search > .ant-input-group > .ant-input {border-radius: 0 2px 2px 0;}
.ant-input-number-handler {color: rgba(0, 0, 0, 0.45);border-left: 1px solid #d9d9d9;}
.ant-input-number-handler:active {background: #f4f4f4;}
.ant-input-number-handler:hover .ant-input-number-handler-up-inner, .ant-input-number-handler:hover .ant-input-number-handler-down-inner {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-input-number-handler-up-inner, .ant-input-number-handler-down-inner {color: inherit;color: rgba(0, 0, 0, 0.45);}
.ant-input-number:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-number-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-input-number-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-input-number-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-input-number-disabled:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-input-number-input {background-color: transparent;border: 0;border-radius: 2px;}
.ant-input-number-input::placeholder {color: #bfbfbf;}
.ant-input-number-handler-wrap {background: #fff;border-radius: 0 2px 2px 0;}
.ant-input-number-borderless .ant-input-number-handler-wrap {border-left-width: 0;}
.ant-input-number-handler-up {border-top-right-radius: 2px;}
.ant-input-number-handler-down {border-top: 1px solid #d9d9d9;border-bottom-right-radius: 2px;}
.ant-input-number-borderless .ant-input-number-handler-down {border-top-width: 0;}
.ant-input-number-handler-up-disabled:hover .ant-input-number-handler-up-inner, .ant-input-number-handler-down-disabled:hover .ant-input-number-handler-down-inner {color: rgba(0, 0, 0, 0.25);}
.ant-input-number-borderless {box-shadow: none;}
.ant-input-number-out-of-range input {color: @error-color;}
.ant-input-number-rtl .ant-input-number-handler {border-right: 1px solid #d9d9d9;border-left: 0;}
.ant-input-number-rtl.ant-input-number-borderless .ant-input-number-handler-wrap {border-right-width: 0;}
.ant-input-number-rtl .ant-input-number-handler-up {border-top-right-radius: 0;}
.ant-input-number-rtl .ant-input-number-handler-down {border-bottom-right-radius: 0;}
.ant-layout {background: @layout-body-background;}
.ant-layout-header {color: rgba(0, 0, 0, 0.85);background: @layout-header-background;}
.ant-layout-footer {color: rgba(0, 0, 0, 0.85);background: @layout-body-background;}
.ant-layout-sider {background: @layout-header-background;}
.ant-layout-sider-trigger {color: #fff;background: #002140;}
.ant-layout-sider-zero-width-trigger {color: #fff;background: @layout-header-background;border-radius: 0 2px 2px 0;}
.ant-layout-sider-zero-width-trigger::after {background: transparent;}
.ant-layout-sider-zero-width-trigger:hover::after {background: rgba(255, 255, 255, 0.1);}
.ant-layout-sider-zero-width-trigger-right {border-radius: 2px 0 0 2px;}
.ant-layout-sider-light {background: #fff;}
.ant-layout-sider-light .ant-layout-sider-trigger {color: rgba(0, 0, 0, 0.85);background: #fff;}
.ant-layout-sider-light .ant-layout-sider-zero-width-trigger {color: rgba(0, 0, 0, 0.85);background: #fff;}
.ant-list {color: rgba(0, 0, 0, 0.85);}
.ant-list-empty-text {color: rgba(0, 0, 0, 0.25);}
.ant-list-item {color: rgba(0, 0, 0, 0.85);}
.ant-list-item-meta-content {color: rgba(0, 0, 0, 0.85);}
.ant-list-item-meta-title {color: rgba(0, 0, 0, 0.85);}
.ant-list-item-meta-title > a {color: rgba(0, 0, 0, 0.85);}
.ant-list-item-meta-title > a:hover {color: @primary-color;}
.ant-list-item-meta-description {color: rgba(0, 0, 0, 0.45);}
.ant-list-item-action > li {color: rgba(0, 0, 0, 0.45);}
.ant-list-item-action-split {background-color: #f0f0f0;}
.ant-list-header {background: transparent;}
.ant-list-footer {background: transparent;}
.ant-list-empty {color: rgba(0, 0, 0, 0.45);}
.ant-list-split .ant-list-item {border-bottom: 1px solid #f0f0f0;}
.ant-list-split .ant-list-item:last-child {border-bottom: none;}
.ant-list-split .ant-list-header {border-bottom: 1px solid #f0f0f0;}
.ant-list-split.ant-list-empty .ant-list-footer {border-top: 1px solid #f0f0f0;}
.ant-list-split.ant-list-something-after-last-item .ant-spin-container > .ant-list-items > .ant-list-item:last-child {border-bottom: 1px solid #f0f0f0;}
.ant-list-vertical .ant-list-item-meta-title {color: rgba(0, 0, 0, 0.85);}
.ant-list-grid .ant-col > .ant-list-item {border-bottom: none;}
.ant-list-bordered {border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-mentions {color: rgba(0, 0, 0, 0.85);background-color: #fff;background-image: none;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-mentions::placeholder {color: #bfbfbf;}
.ant-mentions:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-mentions:hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-mentions:focus, .ant-mentions-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-mentions:focus, .ant-input-rtl .ant-mentions-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-mentions-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-mentions-disabled:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-mentions[disabled] {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-mentions[disabled]:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-mentions-borderless, .ant-mentions-borderless:hover, .ant-mentions-borderless:focus, .ant-mentions-borderless-focused, .ant-mentions-borderless-disabled, .ant-mentions-borderless[disabled] {background-color: transparent;border: none;box-shadow: none;}
.ant-mentions-disabled > textarea {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-mentions-disabled > textarea:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-mentions-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-mentions-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-mentions > textarea {border: none;}
.ant-mentions > textarea::placeholder {color: #bfbfbf;}
.ant-mentions-measure {color: transparent;}
.ant-mentions-dropdown {color: rgba(0, 0, 0, 0.85);background-color: #fff;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-mentions-dropdown-menu-item {color: rgba(0, 0, 0, 0.85);}
.ant-mentions-dropdown-menu-item:hover {background-color: #f5f5f5;}
.ant-mentions-dropdown-menu-item:first-child {border-radius: 2px 2px 0 0;}
.ant-mentions-dropdown-menu-item:last-child {border-radius: 0 0 2px 2px;}
.ant-mentions-dropdown-menu-item-disabled {color: rgba(0, 0, 0, 0.25);}
.ant-mentions-dropdown-menu-item-disabled:hover {color: rgba(0, 0, 0, 0.25);background-color: #fff;}
.ant-mentions-dropdown-menu-item-selected {color: rgba(0, 0, 0, 0.85);background-color: #fafafa;}
.ant-mentions-dropdown-menu-item-active {background-color: #f5f5f5;}
.ant-menu-item-danger.ant-menu-item {color: @error-color;}
.ant-menu-item-danger.ant-menu-item:hover, .ant-menu-item-danger.ant-menu-item-active {color: @error-color;}
.ant-menu-item-danger.ant-menu-item:active {background: #fff1f0;}
.ant-menu-item-danger.ant-menu-item-selected {color: @error-color;}
.ant-menu-item-danger.ant-menu-item-selected > a, .ant-menu-item-danger.ant-menu-item-selected > a:hover {color: @error-color;}
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-danger.ant-menu-item-selected {background-color: #fff1f0;}
.ant-menu-inline .ant-menu-item-danger.ant-menu-item::after {border-right-color: @error-color;}
.ant-menu-dark .ant-menu-item-danger.ant-menu-item, .ant-menu-dark .ant-menu-item-danger.ant-menu-item:hover, .ant-menu-dark .ant-menu-item-danger.ant-menu-item > a {color: @error-color;}
.ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-danger.ant-menu-item-selected {color: #fff;background-color: @error-color;}
.ant-menu {color: rgba(0, 0, 0, 0.85);color: @menu-item-color;background: #fff;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-menu.ant-menu-root:focus-visible {box-shadow: 0 0 0 2px color(~`colorPalette("@{primary-color}", 2)`);}
.ant-menu-item-group-title {color: rgba(0, 0, 0, 0.45);}
.ant-menu-submenu-selected {color: @primary-color;}
.ant-menu-item:active, .ant-menu-submenu-title:active {background: @menu-item-active-bg;}
.ant-menu-item a {color: @menu-item-color;}
.ant-menu-item a:hover {color: @primary-color;}
.ant-menu-item a::before {background-color: transparent;}
.ant-menu-item > .ant-badge a {color: @menu-item-color;}
.ant-menu-item > .ant-badge a:hover {color: @primary-color;}
.ant-menu-item-divider {border-color: #f0f0f0;border-style: solid;border-width: 1px 0 0;}
.ant-menu-item-divider-dashed {border-style: dashed;}
.ant-menu-horizontal > .ant-menu-item:hover, .ant-menu-horizontal > .ant-menu-item-active, .ant-menu-horizontal > .ant-menu-submenu .ant-menu-submenu-title:hover {background-color: transparent;}
.ant-menu-item-selected {color: @primary-color;}
.ant-menu-item-selected a, .ant-menu-item-selected a:hover {color: @primary-color;}
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {background-color: @menu-item-active-bg;}
.ant-menu-inline, .ant-menu-vertical, .ant-menu-vertical-left {border-right: 1px solid #f0f0f0;}
.ant-menu-vertical-right {border-left: 1px solid #f0f0f0;}
.ant-menu-vertical.ant-menu-sub, .ant-menu-vertical-left.ant-menu-sub, .ant-menu-vertical-right.ant-menu-sub {border-right: 0;}
.ant-menu-vertical.ant-menu-sub .ant-menu-item, .ant-menu-vertical-left.ant-menu-sub .ant-menu-item, .ant-menu-vertical-right.ant-menu-sub .ant-menu-item {border-right: 0;}
.ant-menu-vertical.ant-menu-sub .ant-menu-item::after, .ant-menu-vertical-left.ant-menu-sub .ant-menu-item::after, .ant-menu-vertical-right.ant-menu-sub .ant-menu-item::after {border-right: 0;}
.ant-menu-item:focus-visible, .ant-menu-submenu-title:focus-visible {box-shadow: 0 0 0 2px color(~`colorPalette("@{primary-color}", 2)`);}
.ant-menu-submenu-popup {background: transparent;border-radius: 2px;box-shadow: none;}
.ant-menu-submenu > .ant-menu {background-color: #fff;border-radius: 2px;}
.ant-menu-submenu-popup > .ant-menu {background-color: #fff;}
.ant-menu-submenu-expand-icon, .ant-menu-submenu-arrow {color: @menu-item-color;}
.ant-menu-submenu-arrow::before, .ant-menu-submenu-arrow::after {background-color: currentcolor;border-radius: 2px;}
.ant-menu-submenu:hover > .ant-menu-submenu-title > .ant-menu-submenu-expand-icon, .ant-menu-submenu:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow {color: @primary-color;}
.ant-menu-vertical .ant-menu-submenu-selected, .ant-menu-vertical-left .ant-menu-submenu-selected, .ant-menu-vertical-right .ant-menu-submenu-selected {color: @primary-color;}
.ant-menu-horizontal {border: 0;border-bottom: 1px solid #f0f0f0;box-shadow: none;}
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item:hover, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu:hover, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-active, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-active, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-open, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-open, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-selected, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-selected {color: @primary-color;}
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item:hover::after, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu:hover::after, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-active::after, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-active::after, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-open::after, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-open::after, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-selected::after, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-selected::after {border-bottom: 2px solid @primary-color;}
.ant-menu-horizontal > .ant-menu-item::after, .ant-menu-horizontal > .ant-menu-submenu::after {border-bottom: 2px solid transparent;}
.ant-menu-horizontal > .ant-menu-item a {color: @menu-item-color;}
.ant-menu-horizontal > .ant-menu-item a:hover {color: @primary-color;}
.ant-menu-horizontal > .ant-menu-item-selected a {color: @primary-color;}
.ant-menu-vertical .ant-menu-item::after, .ant-menu-vertical-left .ant-menu-item::after, .ant-menu-vertical-right .ant-menu-item::after, .ant-menu-inline .ant-menu-item::after {border-right: 3px solid @primary-color;}
.ant-menu.ant-menu-inline-collapsed-tooltip a {color: rgba(255, 255, 255, 0.85);}
.ant-menu-root.ant-menu-vertical, .ant-menu-root.ant-menu-vertical-left, .ant-menu-root.ant-menu-vertical-right, .ant-menu-root.ant-menu-inline {box-shadow: none;}
.ant-menu-sub.ant-menu-inline {background: #fafafa;border: 0;border-radius: 0;box-shadow: none;}
.ant-menu-item-disabled, .ant-menu-submenu-disabled {color: rgba(0, 0, 0, 0.25) !important;background: none;}
.ant-menu-item-disabled::after, .ant-menu-submenu-disabled::after {border-color: transparent !important;}
.ant-menu-item-disabled a, .ant-menu-submenu-disabled a {color: rgba(0, 0, 0, 0.25) !important;}
.ant-menu-item-disabled > .ant-menu-submenu-title, .ant-menu-submenu-disabled > .ant-menu-submenu-title {color: rgba(0, 0, 0, 0.25) !important;}
.ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after, .ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after {background: rgba(0, 0, 0, 0.25) !important;}
.ant-menu-inline-collapsed-tooltip a, .ant-menu-inline-collapsed-tooltip a:hover {color: #fff;}
.ant-menu-light .ant-menu-item:hover, .ant-menu-light .ant-menu-item-active, .ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open, .ant-menu-light .ant-menu-submenu-active, .ant-menu-light .ant-menu-submenu-title:hover {color: @primary-color;}
.ant-menu.ant-menu-root:focus-visible {box-shadow: 0 0 0 2px color(~`colorPalette("@{primary-color}", 7)`);}
.ant-menu-dark .ant-menu-item:focus-visible, .ant-menu-dark .ant-menu-submenu-title:focus-visible {box-shadow: 0 0 0 2px color(~`colorPalette("@{primary-color}", 7)`);}
.ant-menu.ant-menu-dark, .ant-menu-dark .ant-menu-sub, .ant-menu.ant-menu-dark .ant-menu-sub {color: rgba(255, 255, 255, 0.65);background: @layout-header-background;}
.ant-menu.ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow::after, .ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow::after, .ant-menu.ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow::after, .ant-menu.ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow::before, .ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow::before, .ant-menu.ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow::before {background: #fff;}
.ant-menu-dark.ant-menu-submenu-popup {background: transparent;}
.ant-menu-dark .ant-menu-inline.ant-menu-sub {background: #000c17;}
.ant-menu-dark.ant-menu-horizontal {border-bottom: 0;}
.ant-menu-dark.ant-menu-horizontal > .ant-menu-item, .ant-menu-dark.ant-menu-horizontal > .ant-menu-submenu {border-color: @layout-header-background;border-bottom: 0;}
.ant-menu-dark.ant-menu-horizontal > .ant-menu-item:hover {background-color: @primary-color;}
.ant-menu-dark .ant-menu-item, .ant-menu-dark .ant-menu-item-group-title, .ant-menu-dark .ant-menu-item > a, .ant-menu-dark .ant-menu-item > span > a {color: rgba(255, 255, 255, 0.65);}
.ant-menu-dark.ant-menu-inline, .ant-menu-dark.ant-menu-vertical, .ant-menu-dark.ant-menu-vertical-left, .ant-menu-dark.ant-menu-vertical-right {border-right: 0;}
.ant-menu-dark.ant-menu-inline .ant-menu-item, .ant-menu-dark.ant-menu-vertical .ant-menu-item, .ant-menu-dark.ant-menu-vertical-left .ant-menu-item, .ant-menu-dark.ant-menu-vertical-right .ant-menu-item {border-right: 0;}
.ant-menu-dark.ant-menu-inline .ant-menu-item::after, .ant-menu-dark.ant-menu-vertical .ant-menu-item::after, .ant-menu-dark.ant-menu-vertical-left .ant-menu-item::after, .ant-menu-dark.ant-menu-vertical-right .ant-menu-item::after {border-right: 0;}
.ant-menu-dark .ant-menu-item:hover, .ant-menu-dark .ant-menu-item-active, .ant-menu-dark .ant-menu-submenu-active, .ant-menu-dark .ant-menu-submenu-open, .ant-menu-dark .ant-menu-submenu-selected, .ant-menu-dark .ant-menu-submenu-title:hover {color: #fff;background-color: transparent;}
.ant-menu-dark .ant-menu-item:hover > a, .ant-menu-dark .ant-menu-item-active > a, .ant-menu-dark .ant-menu-submenu-active > a, .ant-menu-dark .ant-menu-submenu-open > a, .ant-menu-dark .ant-menu-submenu-selected > a, .ant-menu-dark .ant-menu-submenu-title:hover > a, .ant-menu-dark .ant-menu-item:hover > span > a, .ant-menu-dark .ant-menu-item-active > span > a, .ant-menu-dark .ant-menu-submenu-active > span > a, .ant-menu-dark .ant-menu-submenu-open > span > a, .ant-menu-dark .ant-menu-submenu-selected > span > a, .ant-menu-dark .ant-menu-submenu-title:hover > span > a {color: #fff;}
.ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after, .ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after, .ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after, .ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after, .ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after, .ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after, .ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before {background: #fff;}
.ant-menu-dark .ant-menu-item:hover {background-color: transparent;}
.ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {background-color: @primary-color;}
.ant-menu-dark .ant-menu-item-selected {color: #fff;border-right: 0;}
.ant-menu-dark .ant-menu-item-selected::after {border-right: 0;}
.ant-menu-dark .ant-menu-item-selected > a, .ant-menu-dark .ant-menu-item-selected > span > a, .ant-menu-dark .ant-menu-item-selected > a:hover, .ant-menu-dark .ant-menu-item-selected > span > a:hover {color: #fff;}
.ant-menu-dark .ant-menu-item-selected .ant-menu-item-icon, .ant-menu-dark .ant-menu-item-selected .anticon {color: #fff;}
.ant-menu-dark .ant-menu-item-selected .ant-menu-item-icon + span, .ant-menu-dark .ant-menu-item-selected .anticon + span {color: #fff;}
.ant-menu.ant-menu-dark .ant-menu-item-selected, .ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {background-color: @primary-color;}
.ant-menu-dark .ant-menu-item-disabled, .ant-menu-dark .ant-menu-submenu-disabled, .ant-menu-dark .ant-menu-item-disabled > a, .ant-menu-dark .ant-menu-submenu-disabled > a, .ant-menu-dark .ant-menu-item-disabled > span > a, .ant-menu-dark .ant-menu-submenu-disabled > span > a {color: rgba(255, 255, 255, 0.35) !important;}
.ant-menu-dark .ant-menu-item-disabled > .ant-menu-submenu-title, .ant-menu-dark .ant-menu-submenu-disabled > .ant-menu-submenu-title {color: rgba(255, 255, 255, 0.35) !important;}
.ant-menu-dark .ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-dark .ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow::before, .ant-menu-dark .ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after, .ant-menu-dark .ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow::after {background: rgba(255, 255, 255, 0.35) !important;}
.ant-menu-rtl.ant-menu-inline, .ant-menu-rtl.ant-menu-vertical {border-right: none;border-left: 1px solid #f0f0f0;}
.ant-menu-rtl.ant-menu-dark.ant-menu-inline, .ant-menu-rtl.ant-menu-dark.ant-menu-vertical {border-left: none;}
.ant-menu-sub.ant-menu-inline {border: 0;}
.ant-message {color: rgba(0, 0, 0, 0.85);}
.ant-message-notice-content {background: #fff;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-message-success .anticon {color: @success-color;}
.ant-message-error .anticon {color: @error-color;}
.ant-message-warning .anticon {color: @warning-color;}
.ant-message-info .anticon, .ant-message-loading .anticon {color: @primary-color;}
.ant-modal {color: rgba(0, 0, 0, 0.85);}
.ant-modal-mask {background-color: rgba(0, 0, 0, 0.45);}
.ant-modal-title {color: rgba(0, 0, 0, 0.85);}
.ant-modal-content {background-color: #fff;background-clip: padding-box;border: 0;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-modal-close {color: rgba(0, 0, 0, 0.45);background: transparent;border: 0;}
.ant-modal-close:focus, .ant-modal-close:hover {color: rgba(0, 0, 0, 0.75);}
.ant-modal-header {color: rgba(0, 0, 0, 0.85);background: #fff;border-bottom: 1px solid #f0f0f0;border-radius: 2px 2px 0 0;}
.ant-modal-footer {background: transparent;border-top: 1px solid #f0f0f0;border-radius: 0 0 2px 2px;}
.ant-modal-confirm-body .ant-modal-confirm-title {color: rgba(0, 0, 0, 0.85);}
.ant-modal-confirm-body .ant-modal-confirm-content {color: rgba(0, 0, 0, 0.85);}
.ant-modal-confirm-error .ant-modal-confirm-body > .anticon {color: @error-color;}
.ant-modal-confirm-warning .ant-modal-confirm-body > .anticon, .ant-modal-confirm-confirm .ant-modal-confirm-body > .anticon {color: @warning-color;}
.ant-modal-confirm-info .ant-modal-confirm-body > .anticon {color: @primary-color;}
.ant-modal-confirm-success .ant-modal-confirm-body > .anticon {color: @success-color;}
.ant-notification {color: rgba(0, 0, 0, 0.85);}
.ant-notification-notice {background: #fff;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-notification-notice-message {color: rgba(0, 0, 0, 0.85);}
.ant-notification-notice-message-single-line-auto-margin {background-color: transparent;}
.anticon.ant-notification-notice-icon-success {color: @success-color;}
.anticon.ant-notification-notice-icon-info {color: @primary-color;}
.anticon.ant-notification-notice-icon-warning {color: @warning-color;}
.anticon.ant-notification-notice-icon-error {color: @error-color;}
.ant-notification-notice-close {color: rgba(0, 0, 0, 0.45);}
.ant-notification-notice-close:hover {color: shade(@text-color-secondary, 40%);}
.ant-notification .notification-fade-effect {animation-fill-mode: both;}
.ant-notification-fade-enter, .ant-notification-fade-appear {animation-fill-mode: both;}
.ant-notification-fade-leave {animation-fill-mode: both;}
.ant-page-header {color: rgba(0, 0, 0, 0.85);background-color: #fff;}
.ant-page-header-ghost {background-color: inherit;}
.ant-page-header-back-button {color: @primary-color;color: #000;}
.ant-page-header-back-button:focus, .ant-page-header-back-button:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-page-header-back-button:active {color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-page-header-heading-title {color: rgba(0, 0, 0, 0.85);}
.ant-page-header-heading-sub-title {color: rgba(0, 0, 0, 0.45);}
.ant-page-header-footer .ant-tabs > .ant-tabs-nav::before {border: none;}
.ant-pagination {color: rgba(0, 0, 0, 0.85);}
.ant-pagination-item {background-color: #fff;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-pagination-item a {color: rgba(0, 0, 0, 0.85);}
.ant-pagination-item:hover {border-color: @primary-color;}
.ant-pagination-item:hover a {color: @primary-color;}
.ant-pagination-item:focus-visible {border-color: @primary-color;}
.ant-pagination-item:focus-visible a {color: @primary-color;}
.ant-pagination-item-active {background: #fff;border-color: @primary-color;}
.ant-pagination-item-active a {color: @primary-color;}
.ant-pagination-item-active:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-pagination-item-active:focus-visible {border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-pagination-item-active:hover a {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-pagination-item-active:focus-visible a {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon, .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon {color: @primary-color;}
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis, .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {color: rgba(0, 0, 0, 0.25);}
.ant-pagination-prev, .ant-pagination-next, .ant-pagination-jump-prev, .ant-pagination-jump-next {color: rgba(0, 0, 0, 0.85);border-radius: 2px;}
.ant-pagination-prev button, .ant-pagination-next button {color: rgba(0, 0, 0, 0.85);}
.ant-pagination-prev:hover button, .ant-pagination-next:hover button {border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-pagination-prev .ant-pagination-item-link, .ant-pagination-next .ant-pagination-item-link {background-color: #fff;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-pagination-prev:focus-visible .ant-pagination-item-link, .ant-pagination-next:focus-visible .ant-pagination-item-link {color: @primary-color;border-color: @primary-color;}
.ant-pagination-prev:hover .ant-pagination-item-link, .ant-pagination-next:hover .ant-pagination-item-link {color: @primary-color;border-color: @primary-color;}
.ant-pagination-disabled .ant-pagination-item-link, .ant-pagination-disabled:hover .ant-pagination-item-link {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;}
.ant-pagination-disabled:focus-visible .ant-pagination-item-link {color: rgba(0, 0, 0, 0.25);border-color: #d9d9d9;}
.ant-pagination-options-quick-jumper input {color: rgba(0, 0, 0, 0.85);background-color: #fff;background-image: none;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-pagination-options-quick-jumper input::placeholder {color: #bfbfbf;}
.ant-pagination-options-quick-jumper input:hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-pagination-options-quick-jumper input:hover {border-right-width: 0;border-left-width: 1px !important;}
.ant-pagination-options-quick-jumper input:focus, .ant-pagination-options-quick-jumper input-focused {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-pagination-options-quick-jumper input:focus, .ant-input-rtl .ant-pagination-options-quick-jumper input-focused {border-right-width: 0;border-left-width: 1px !important;}
.ant-pagination-options-quick-jumper input-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-pagination-options-quick-jumper input-disabled:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-pagination-options-quick-jumper input[disabled] {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;box-shadow: none;}
.ant-pagination-options-quick-jumper input[disabled]:hover {border-color: #d9d9d9;border-right-width: 1px !important;}
.ant-pagination-options-quick-jumper input-borderless, .ant-pagination-options-quick-jumper input-borderless:hover, .ant-pagination-options-quick-jumper input-borderless:focus, .ant-pagination-options-quick-jumper input-borderless-focused, .ant-pagination-options-quick-jumper input-borderless-disabled, .ant-pagination-options-quick-jumper input-borderless[disabled] {background-color: transparent;border: none;box-shadow: none;}
.ant-pagination-simple .ant-pagination-prev .ant-pagination-item-link, .ant-pagination-simple .ant-pagination-next .ant-pagination-item-link {background-color: transparent;border: 0;}
.ant-pagination-simple .ant-pagination-simple-pager input {background-color: #fff;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-pagination-simple .ant-pagination-simple-pager input:hover {border-color: @primary-color;}
.ant-pagination-simple .ant-pagination-simple-pager input:focus {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);}
.ant-pagination-simple .ant-pagination-simple-pager input[disabled] {color: rgba(0, 0, 0, 0.25);background: #f5f5f5;border-color: #d9d9d9;}
.ant-pagination.mini .ant-pagination-item:not(.ant-pagination-item-active) {background: transparent;border-color: transparent;}
.ant-pagination.mini .ant-pagination-prev .ant-pagination-item-link, .ant-pagination.mini .ant-pagination-next .ant-pagination-item-link {background: transparent;border-color: transparent;}
.ant-pagination.ant-pagination-disabled .ant-pagination-item {background: #f5f5f5;border-color: #d9d9d9;}
.ant-pagination.ant-pagination-disabled .ant-pagination-item a {color: rgba(0, 0, 0, 0.25);background: transparent;border: none;}
.ant-pagination.ant-pagination-disabled .ant-pagination-item-active {background: #e6e6e6;}
.ant-pagination.ant-pagination-disabled .ant-pagination-item-active a {color: rgba(0, 0, 0, 0.25);}
.ant-pagination.ant-pagination-disabled .ant-pagination-item-link {color: rgba(0, 0, 0, 0.25);background: #f5f5f5;border-color: #d9d9d9;}
.ant-pagination-simple.ant-pagination.ant-pagination-disabled .ant-pagination-item-link {background: transparent;}
.ant-pagination.ant-pagination-disabled .ant-pagination-simple-pager {color: rgba(0, 0, 0, 0.25);}
.ant-popover {color: rgba(0, 0, 0, 0.85);}
.ant-popover::after {background: rgba(255, 255, 255, 0.01);}
.ant-popover-inner {background-color: #fff;background-clip: padding-box;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);box-shadow: 0 0 8px rgba(0, 0, 0, 0.15) ;}
.ant-popover-title {color: rgba(0, 0, 0, 0.85);border-bottom: 1px solid #f0f0f0;}
.ant-popover-inner-content {color: rgba(0, 0, 0, 0.85);}
.ant-popover-message {color: rgba(0, 0, 0, 0.85);}
.ant-popover-message > .anticon {color: @warning-color;}
.ant-popover-arrow {background: transparent;}
.ant-popover-arrow-content {background-color: #fff;}
.ant-popover-placement-top .ant-popover-arrow-content, .ant-popover-placement-topLeft .ant-popover-arrow-content, .ant-popover-placement-topRight .ant-popover-arrow-content {box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);}
.ant-popover-placement-right .ant-popover-arrow-content, .ant-popover-placement-rightTop .ant-popover-arrow-content, .ant-popover-placement-rightBottom .ant-popover-arrow-content {box-shadow: -3px 3px 7px rgba(0, 0, 0, 0.07);}
.ant-popover-placement-bottom .ant-popover-arrow-content, .ant-popover-placement-bottomLeft .ant-popover-arrow-content, .ant-popover-placement-bottomRight .ant-popover-arrow-content {box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);}
.ant-popover-placement-left .ant-popover-arrow-content, .ant-popover-placement-leftTop .ant-popover-arrow-content, .ant-popover-placement-leftBottom .ant-popover-arrow-content {box-shadow: 3px -3px 7px rgba(0, 0, 0, 0.07);}
.ant-popover-pink .ant-popover-inner {background-color: #eb2f96;}
.ant-popover-pink .ant-popover-arrow-content {background-color: #eb2f96;}
.ant-popover-magenta .ant-popover-inner {background-color: #eb2f96;}
.ant-popover-magenta .ant-popover-arrow-content {background-color: #eb2f96;}
.ant-popover-red .ant-popover-inner {background-color: #f5222d;}
.ant-popover-red .ant-popover-arrow-content {background-color: #f5222d;}
.ant-popover-volcano .ant-popover-inner {background-color: #fa541c;}
.ant-popover-volcano .ant-popover-arrow-content {background-color: #fa541c;}
.ant-popover-orange .ant-popover-inner {background-color: #fa8c16;}
.ant-popover-orange .ant-popover-arrow-content {background-color: #fa8c16;}
.ant-popover-yellow .ant-popover-inner {background-color: #fadb14;}
.ant-popover-yellow .ant-popover-arrow-content {background-color: #fadb14;}
.ant-popover-gold .ant-popover-inner {background-color: #faad14;}
.ant-popover-gold .ant-popover-arrow-content {background-color: #faad14;}
.ant-popover-cyan .ant-popover-inner {background-color: #13c2c2;}
.ant-popover-cyan .ant-popover-arrow-content {background-color: #13c2c2;}
.ant-popover-lime .ant-popover-inner {background-color: #a0d911;}
.ant-popover-lime .ant-popover-arrow-content {background-color: #a0d911;}
.ant-popover-green .ant-popover-inner {background-color: #52c41a;}
.ant-popover-green .ant-popover-arrow-content {background-color: #52c41a;}
.ant-popover-blue .ant-popover-inner {background-color: #1890ff;}
.ant-popover-blue .ant-popover-arrow-content {background-color: #1890ff;}
.ant-popover-geekblue .ant-popover-inner {background-color: #2f54eb;}
.ant-popover-geekblue .ant-popover-arrow-content {background-color: #2f54eb;}
.ant-popover-purple .ant-popover-inner {background-color: #722ed1;}
.ant-popover-purple .ant-popover-arrow-content {background-color: #722ed1;}
.ant-progress {color: rgba(0, 0, 0, 0.85);}
.ant-progress-steps-item {background: #f3f3f3;}
.ant-progress-steps-item-active {background: #1890ff;}
.ant-progress-inner {background-color: #f5f5f5;border-radius: 100px;}
.ant-progress-circle-trail {stroke: #f5f5f5;}
.ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {stroke: #1890ff;}
.ant-progress-success-bg, .ant-progress-bg {background-color: #1890ff;border-radius: 100px;}
.ant-progress-success-bg {background-color: @success-color;}
.ant-progress-text {color: rgba(0, 0, 0, 0.85);}
.ant-progress-status-active .ant-progress-bg::before {background: #fff;border-radius: 10px;}
.ant-progress-status-exception .ant-progress-bg {background-color: @error-color;}
.ant-progress-status-exception .ant-progress-text {color: @error-color;}
.ant-progress-status-exception .ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {stroke: @error-color;}
.ant-progress-status-success .ant-progress-bg {background-color: @success-color;}
.ant-progress-status-success .ant-progress-text {color: @success-color;}
.ant-progress-status-success .ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {stroke: @success-color;}
.ant-progress-circle .ant-progress-inner {background-color: transparent;}
.ant-progress-circle .ant-progress-text {color: rgba(0, 0, 0, 0.85);}
.ant-progress-circle.ant-progress-status-exception .ant-progress-text {color: @error-color;}
.ant-progress-circle.ant-progress-status-success .ant-progress-text {color: @success-color;}
.ant-radio-group {color: rgba(0, 0, 0, 0.85);}
.ant-radio-group > .ant-badge:not(:first-child) > .ant-radio-button-wrapper {border-left: none;}
.ant-radio-wrapper {color: rgba(0, 0, 0, 0.85);}
.ant-radio {color: rgba(0, 0, 0, 0.85);}
.ant-radio-wrapper:hover .ant-radio, .ant-radio:hover .ant-radio-inner, .ant-radio-input:focus + .ant-radio-inner {border-color: @primary-color;}
.ant-radio-input:focus + .ant-radio-inner {box-shadow: 0 0 0 3px color(~`colorPalette("@{primary-color}", 1)`);}
.ant-radio-checked::after {border: 1px solid @primary-color;border-radius: 50%;animation-fill-mode: both;}
.ant-radio-inner {background-color: #fff;border-color: #d9d9d9;border-style: solid;border-width: 1px;border-radius: 50%;}
.ant-radio-inner::after {background-color: @primary-color;border-top: 0;border-left: 0;border-radius: 16px;}
.ant-radio-checked .ant-radio-inner {border-color: @primary-color;}
.ant-radio-disabled .ant-radio-inner {background-color: #f5f5f5;border-color: #d9d9d9 !important;}
.ant-radio-disabled .ant-radio-inner::after {background-color: rgba(0, 0, 0, 0.2);}
.ant-radio-disabled + span {color: rgba(0, 0, 0, 0.25);}
.ant-radio-button-wrapper {color: rgba(0, 0, 0, 0.85);background: #fff;border: 1px solid #d9d9d9;border-top-width: 1.02px;border-left-width: 0;}
.ant-radio-button-wrapper a {color: rgba(0, 0, 0, 0.85);}
.ant-radio-button-wrapper:not(:first-child)::before {background-color: #d9d9d9;}
.ant-radio-button-wrapper:first-child {border-left: 1px solid #d9d9d9;border-radius: 2px 0 0 2px;}
.ant-radio-button-wrapper:last-child {border-radius: 0 2px 2px 0;}
.ant-radio-button-wrapper:first-child:last-child {border-radius: 2px;}
.ant-radio-button-wrapper:hover {color: @primary-color;}
.ant-radio-button-wrapper:focus-within {box-shadow: 0 0 0 3px color(~`colorPalette("@{primary-color}", 1)`);}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {color: @primary-color;background: #fff;border-color: @primary-color;}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {background-color: @primary-color;}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child {border-color: @primary-color;}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {color: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover::before {background-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {color: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active::before {background-color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {box-shadow: 0 0 0 3px color(~`colorPalette("@{primary-color}", 1)`);}
.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {color: #fff;background: @primary-color;border-color: @primary-color;}
.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {color: #fff;background: color(~`colorPalette("@{primary-color}", 5)`);border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {color: #fff;background: color(~`colorPalette("@{primary-color}", 7)`);border-color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {box-shadow: 0 0 0 3px color(~`colorPalette("@{primary-color}", 1)`);}
.ant-radio-button-wrapper-disabled {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;}
.ant-radio-button-wrapper-disabled:first-child, .ant-radio-button-wrapper-disabled:hover {color: rgba(0, 0, 0, 0.25);background-color: #f5f5f5;border-color: #d9d9d9;}
.ant-radio-button-wrapper-disabled:first-child {border-left-color: #d9d9d9;}
.ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {color: rgba(0, 0, 0, 0.25);background-color: #e6e6e6;border-color: #d9d9d9;box-shadow: none;}
.ant-radio-button-wrapper.ant-radio-button-wrapper-rtl {border-right-width: 0;border-left-width: 1px;}
.ant-radio-button-wrapper.ant-radio-button-wrapper-rtl.ant-radio-button-wrapper:first-child {border-right: 1px solid #d9d9d9;border-radius: 0 2px 2px 0;}
.ant-radio-button-wrapper-checked:not([class*=' ant-radio-button-wrapper-disabled']).ant-radio-button-wrapper:first-child {border-right-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-radio-button-wrapper.ant-radio-button-wrapper-rtl.ant-radio-button-wrapper:last-child {border-radius: 2px 0 0 2px;}
.ant-radio-button-wrapper.ant-radio-button-wrapper-rtl.ant-radio-button-wrapper-disabled:first-child {border-right-color: #d9d9d9;}
.ant-rate {color: rgba(0, 0, 0, 0.85);color: #fadb14;}
.ant-rate-star {color: inherit;}
.ant-rate-star-first, .ant-rate-star-second {color: #f0f0f0;}
.ant-rate-star-half .ant-rate-star-first, .ant-rate-star-full .ant-rate-star-second {color: inherit;}
.ant-result-success .ant-result-icon > .anticon {color: @success-color;}
.ant-result-error .ant-result-icon > .anticon {color: @error-color;}
.ant-result-info .ant-result-icon > .anticon {color: @primary-color;}
.ant-result-warning .ant-result-icon > .anticon {color: @warning-color;}
.ant-result-title {color: rgba(0, 0, 0, 0.85);}
.ant-result-subtitle {color: rgba(0, 0, 0, 0.45);}
.ant-result-content {background-color: #fafafa;}
.ant-select-single.ant-select-open .ant-select-selection-item {color: #bfbfbf;}
.ant-select-disabled.ant-select-multiple .ant-select-selector {background: #f5f5f5;}
.ant-select-multiple .ant-select-selection-item {background: #f5f5f5;border: 1px solid #f0f0f0;border-radius: 2px;}
.ant-select-disabled.ant-select-multiple .ant-select-selection-item {color: #bfbfbf;border-color: #d9d9d9;}
.ant-select-multiple .ant-select-selection-item-remove {color: inherit;color: rgba(0, 0, 0, 0.45);}
.ant-select-multiple .ant-select-selection-item-remove:hover {color: rgba(0, 0, 0, 0.75);}
.ant-select {color: rgba(0, 0, 0, 0.85);}
.ant-select:not(.ant-select-customize-input) .ant-select-selector {background-color: #fff;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {border-color: color(~`colorPalette("@{primary-color}", 5)`);box-shadow: 0 0 0 2px fade(@primary-color, 20%);border-right-width: 1px !important;}
.ant-input-rtl .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {border-right-width: 0;border-left-width: 1px !important;}
.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {color: rgba(0, 0, 0, 0.25);background: #f5f5f5;}
.ant-select-multiple.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {background: #f5f5f5;}
.ant-select:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {background: transparent;border: none;}
.ant-select:not(.ant-select-disabled):hover .ant-select-selector {border-color: color(~`colorPalette("@{primary-color}", 5)`);border-right-width: 1px !important;}
.ant-input-rtl .ant-select:not(.ant-select-disabled):hover .ant-select-selector {border-right-width: 0;border-left-width: 1px !important;}
.ant-select-selection-placeholder {color: #bfbfbf;}
.ant-select-arrow {color: inherit;color: rgba(0, 0, 0, 0.25);}
.ant-select-clear {color: rgba(0, 0, 0, 0.25);background: #fff;}
.ant-select-clear:hover {color: rgba(0, 0, 0, 0.45);}
.ant-select-dropdown {color: rgba(0, 0, 0, 0.85);background-color: #fff;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-select-dropdown-empty {color: rgba(0, 0, 0, 0.25);}
.ant-select-item-empty {color: rgba(0, 0, 0, 0.85);color: rgba(0, 0, 0, 0.25);}
.ant-select-item {color: rgba(0, 0, 0, 0.85);}
.ant-select-item-group {color: rgba(0, 0, 0, 0.45);}
.ant-select-item-option-active:not(.ant-select-item-option-disabled) {background-color: #f5f5f5;}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {color: rgba(0, 0, 0, 0.85);background-color: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) .ant-select-item-option-state {color: @primary-color;}
.ant-select-item-option-disabled {color: rgba(0, 0, 0, 0.25);}
.ant-select-item-option-disabled.ant-select-item-option-selected {background-color: #f5f5f5;}
.ant-select-borderless .ant-select-selector {background-color: transparent !important;border-color: transparent !important;box-shadow: none !important;}
.ant-skeleton-header .ant-skeleton-avatar {background: rgba(190, 190, 190, 0.2);}
.ant-skeleton-header .ant-skeleton-avatar.ant-skeleton-avatar-circle {border-radius: 50%;}
.ant-skeleton-header .ant-skeleton-avatar-lg.ant-skeleton-avatar-circle {border-radius: 50%;}
.ant-skeleton-header .ant-skeleton-avatar-sm.ant-skeleton-avatar-circle {border-radius: 50%;}
.ant-skeleton-content .ant-skeleton-title {background: rgba(190, 190, 190, 0.2);border-radius: 4px;}
.ant-skeleton-content .ant-skeleton-paragraph > li {background: rgba(190, 190, 190, 0.2);border-radius: 4px;}
.ant-skeleton-round .ant-skeleton-content .ant-skeleton-title, .ant-skeleton-round .ant-skeleton-content .ant-skeleton-paragraph > li {border-radius: 100px;}
.ant-skeleton.ant-skeleton-active .ant-skeleton-content .ant-skeleton-title, .ant-skeleton.ant-skeleton-active .ant-skeleton-content .ant-skeleton-paragraph > li {background: linear-gradient(90deg, rgba(190, 190, 190, 0.2) 25%, shade(@skeleton-color, 5%) 37%, rgba(190, 190, 190, 0.2) 63%);background-size: 400% 100%;}
.ant-skeleton.ant-skeleton-active .ant-skeleton-avatar {background: linear-gradient(90deg, rgba(190, 190, 190, 0.2) 25%, shade(@skeleton-color, 5%) 37%, rgba(190, 190, 190, 0.2) 63%);background-size: 400% 100%;}
.ant-skeleton.ant-skeleton-active .ant-skeleton-button {background: linear-gradient(90deg, rgba(190, 190, 190, 0.2) 25%, shade(@skeleton-color, 5%) 37%, rgba(190, 190, 190, 0.2) 63%);background-size: 400% 100%;}
.ant-skeleton.ant-skeleton-active .ant-skeleton-input {background: linear-gradient(90deg, rgba(190, 190, 190, 0.2) 25%, shade(@skeleton-color, 5%) 37%, rgba(190, 190, 190, 0.2) 63%);background-size: 400% 100%;}
.ant-skeleton.ant-skeleton-active .ant-skeleton-image {background: linear-gradient(90deg, rgba(190, 190, 190, 0.2) 25%, shade(@skeleton-color, 5%) 37%, rgba(190, 190, 190, 0.2) 63%);background-size: 400% 100%;}
.ant-skeleton-element .ant-skeleton-button {background: rgba(190, 190, 190, 0.2);border-radius: 2px;}
.ant-skeleton-element .ant-skeleton-button.ant-skeleton-button-circle {border-radius: 50%;}
.ant-skeleton-element .ant-skeleton-button.ant-skeleton-button-round {border-radius: 32px;}
.ant-skeleton-element .ant-skeleton-button-lg.ant-skeleton-button-circle {border-radius: 50%;}
.ant-skeleton-element .ant-skeleton-button-lg.ant-skeleton-button-round {border-radius: 40px;}
.ant-skeleton-element .ant-skeleton-button-sm.ant-skeleton-button-circle {border-radius: 50%;}
.ant-skeleton-element .ant-skeleton-button-sm.ant-skeleton-button-round {border-radius: 24px;}
.ant-skeleton-element .ant-skeleton-avatar {background: rgba(190, 190, 190, 0.2);}
.ant-skeleton-element .ant-skeleton-avatar.ant-skeleton-avatar-circle {border-radius: 50%;}
.ant-skeleton-element .ant-skeleton-avatar-lg.ant-skeleton-avatar-circle {border-radius: 50%;}
.ant-skeleton-element .ant-skeleton-avatar-sm.ant-skeleton-avatar-circle {border-radius: 50%;}
.ant-skeleton-element .ant-skeleton-input {background: rgba(190, 190, 190, 0.2);}
.ant-skeleton-element .ant-skeleton-image {background: rgba(190, 190, 190, 0.2);}
.ant-skeleton-element .ant-skeleton-image.ant-skeleton-image-circle {border-radius: 50%;}
.ant-skeleton-element .ant-skeleton-image-path {fill: #bfbfbf;}
.ant-skeleton-element .ant-skeleton-image-svg.ant-skeleton-image-circle {border-radius: 50%;}
.ant-slider {color: rgba(0, 0, 0, 0.85);}
.ant-slider-rail {background-color: #f5f5f5;border-radius: 2px;}
.ant-slider-track {background-color: color(~`colorPalette("@{primary-color}", 3)`);border-radius: 2px;}
.ant-slider-handle {background-color: #fff;border: solid 2px color(~`colorPalette("@{primary-color}", 3)`);border-radius: 50%;box-shadow: 0;}
.ant-slider-handle-dragging.ant-slider-handle-dragging.ant-slider-handle-dragging {border-color: tint(@primary-color, 20%);box-shadow: 0 0 0 5px fade(@primary-color, 12%);}
.ant-slider-handle:focus {border-color: tint(@primary-color, 20%);box-shadow: 0 0 0 5px fade(@primary-color, 12%);}
.ant-slider-handle.ant-tooltip-open {border-color: @primary-color;}
.ant-slider:hover .ant-slider-rail {background-color: #e1e1e1;}
.ant-slider:hover .ant-slider-track {background-color: color(~`colorPalette("@{primary-color}", 4)`);}
.ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {border-color: color(~`colorPalette("@{primary-color}", 4)`);}
.ant-slider-mark-text {color: rgba(0, 0, 0, 0.45);}
.ant-slider-mark-text-active {color: rgba(0, 0, 0, 0.85);}
.ant-slider-step {background: transparent;}
.ant-slider-dot {background-color: #fff;border: 2px solid #f0f0f0;border-radius: 50%;}
.ant-slider-dot-active {border-color: tint(@primary-color, 50%);}
.ant-slider-disabled .ant-slider-rail {background-color: #f5f5f5 !important;}
.ant-slider-disabled .ant-slider-track {background-color: rgba(0, 0, 0, 0.25) !important;}
.ant-slider-disabled .ant-slider-handle, .ant-slider-disabled .ant-slider-dot {background-color: #fff;border-color: rgba(0, 0, 0, 0.25) !important;box-shadow: none;}
.ant-spin {color: rgba(0, 0, 0, 0.85);color: @primary-color;}
.ant-spin-container::after {background: #fff;}
.ant-spin-tip {color: rgba(0, 0, 0, 0.45);}
.ant-spin-dot-item {background-color: @primary-color;border-radius: 100%;}
.ant-statistic {color: rgba(0, 0, 0, 0.85);}
.ant-statistic-title {color: rgba(0, 0, 0, 0.45);}
.ant-statistic-content {color: rgba(0, 0, 0, 0.85);}
.ant-steps {color: rgba(0, 0, 0, 0.85);}
.ant-steps-item-icon {border: 1px solid rgba(0, 0, 0, 0.25);border-radius: 32px;}
.ant-steps-item-icon .ant-steps-icon {color: @primary-color;}
.ant-steps-item-tail::after {background: #f0f0f0;border-radius: 1px;}
.ant-steps-item-title {color: rgba(0, 0, 0, 0.85);}
.ant-steps-item-title::after {background: #f0f0f0;}
.ant-steps-item-subtitle {color: rgba(0, 0, 0, 0.45);}
.ant-steps-item-description {color: rgba(0, 0, 0, 0.45);}
.ant-steps-item-wait .ant-steps-item-icon {background-color: #fff;border-color: rgba(0, 0, 0, 0.25);}
.ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {color: rgba(0, 0, 0, 0.25);}
.ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {background: rgba(0, 0, 0, 0.25);}
.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title {color: rgba(0, 0, 0, 0.45);}
.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {background-color: #f0f0f0;}
.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {color: rgba(0, 0, 0, 0.45);}
.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-tail::after {background-color: #f0f0f0;}
.ant-steps-item-process .ant-steps-item-icon {background-color: #fff;border-color: @primary-color;}
.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {color: @primary-color;}
.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {background: @primary-color;}
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title {color: rgba(0, 0, 0, 0.85);}
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {background-color: #f0f0f0;}
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {color: rgba(0, 0, 0, 0.85);}
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after {background-color: #f0f0f0;}
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-icon {background: @primary-color;}
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-icon .ant-steps-icon {color: #fff;}
.ant-steps-item-finish .ant-steps-item-icon {background-color: #fff;border-color: @primary-color;}
.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {color: @primary-color;}
.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {background: @primary-color;}
.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title {color: rgba(0, 0, 0, 0.85);}
.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {background-color: @primary-color;}
.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {color: rgba(0, 0, 0, 0.45);}
.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after {background-color: @primary-color;}
.ant-steps-item-error .ant-steps-item-icon {background-color: #fff;border-color: @error-color;}
.ant-steps-item-error .ant-steps-item-icon > .ant-steps-icon {color: @error-color;}
.ant-steps-item-error .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {background: @error-color;}
.ant-steps-item-error > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title {color: @error-color;}
.ant-steps-item-error > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {background-color: #f0f0f0;}
.ant-steps-item-error > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {color: @error-color;}
.ant-steps-item-error > .ant-steps-item-container > .ant-steps-item-tail::after {background-color: #f0f0f0;}
.ant-steps-item.ant-steps-next-error .ant-steps-item-title::after {background: @error-color;}
.ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-title, .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-subtitle, .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button']:hover .ant-steps-item-description {color: @primary-color;}
.ant-steps .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process) > .ant-steps-item-container[role='button']:hover .ant-steps-item-icon {border-color: @primary-color;}
.ant-steps .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process) > .ant-steps-item-container[role='button']:hover .ant-steps-item-icon .ant-steps-icon {color: @primary-color;}
.ant-steps-item-custom > .ant-steps-item-container > .ant-steps-item-icon {background: none;border: 0;}
.ant-steps-item-custom.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {color: @primary-color;}
.ant-steps:not(.ant-steps-vertical) .ant-steps-item-custom .ant-steps-item-icon {background: none;}
.ant-steps-small .ant-steps-item-icon {border-radius: 24px;}
.ant-steps-small .ant-steps-item-description {color: rgba(0, 0, 0, 0.45);}
.ant-steps-small .ant-steps-item-custom .ant-steps-item-icon {background: none;border: 0;border-radius: 0;}
.ant-steps-dot .ant-steps-item-icon, .ant-steps-dot.ant-steps-small .ant-steps-item-icon {background: transparent;border: 0;}
.ant-steps-dot .ant-steps-item-icon .ant-steps-icon-dot, .ant-steps-dot.ant-steps-small .ant-steps-item-icon .ant-steps-icon-dot {border-radius: 100px;}
.ant-steps-dot .ant-steps-item-icon .ant-steps-icon-dot::after, .ant-steps-dot.ant-steps-small .ant-steps-item-icon .ant-steps-icon-dot::after {background: rgba(0, 0, 0, 0.001);}
.ant-steps-dot .ant-steps-item-process .ant-steps-item-icon, .ant-steps-dot.ant-steps-small .ant-steps-item-process .ant-steps-item-icon {background: none;}
.ant-steps-vertical.ant-steps-dot .ant-steps-item-icon {background: none;}
.ant-steps-navigation .ant-steps-item::after {border: 1px solid rgba(0, 0, 0, 0.25);border-bottom: none;border-left: none;}
.ant-steps-navigation .ant-steps-item::before {background-color: @primary-color;}
.ant-switch {color: rgba(0, 0, 0, 0.85);background-color: rgba(0, 0, 0, 0.25);border: 0;border-radius: 100px;}
.ant-switch:focus {box-shadow: 0 0 0 2px fade(@disabled-color, 10%);}
.ant-switch-checked:focus {box-shadow: 0 0 0 2px color(~`colorPalette("@{primary-color}", 1)`);}
.ant-switch:focus:hover {box-shadow: none;}
.ant-switch-checked {background-color: @primary-color;}
.ant-switch-loading *, .ant-switch-disabled * {box-shadow: none;}
.ant-switch-inner {color: #fff;}
.ant-switch-handle::before {background-color: #fff;border-radius: 9px;box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);}
.ant-switch-loading-icon.anticon {color: rgba(0, 0, 0, 0.65);}
.ant-switch-checked .ant-switch-loading-icon {color: @primary-color;}
.ant-table-small .ant-table-thead > tr > th {background-color: @table-header-bg;}
.ant-table.ant-table-bordered > .ant-table-title {border: 1px solid @table-border-color;border-bottom: 0;}
.ant-table.ant-table-bordered > .ant-table-container {border-left: 1px solid @table-border-color;}
.ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > thead > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > thead > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > td {border-right: 1px solid @table-border-color;}
.ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr:not(:last-child) > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr:not(:last-child) > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > thead > tr:not(:last-child) > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > thead > tr:not(:last-child) > th {border-bottom: 1px solid @table-border-color;}
.ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > th::before, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > th::before, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > thead > tr > th::before, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > thead > tr > th::before {background-color: transparent !important;}
.ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > thead > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > thead > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > .ant-table-cell-fix-right-first::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > .ant-table-cell-fix-right-first::after {border-right: 1px solid @table-border-color;}
.ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td > .ant-table-expanded-row-fixed::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td > .ant-table-expanded-row-fixed::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td > .ant-table-expanded-row-fixed::after, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > td > .ant-table-expanded-row-fixed::after {border-right: 1px solid @table-border-color;}
.ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table {border-top: 1px solid @table-border-color;}
.ant-table.ant-table-bordered.ant-table-scroll-horizontal > .ant-table-container > .ant-table-body > table > tbody > tr.ant-table-expanded-row > td, .ant-table.ant-table-bordered.ant-table-scroll-horizontal > .ant-table-container > .ant-table-body > table > tbody > tr.ant-table-placeholder > td {border-right: 0;}
.ant-table.ant-table-bordered > .ant-table-footer {border: 1px solid @table-border-color;border-top: 0;}
.ant-table-cell .ant-table-container:first-child {border-top: 0;}
.ant-table-cell-scrollbar {box-shadow: 0 1px 0 1px @table-header-bg;}
.ant-table-resize-handle-line {background-color: @primary-color;}
.ant-table {color: rgba(0, 0, 0, 0.85);background: #fff;border-radius: 2px;}
.ant-table table {border-radius: 2px 2px 0 0;border-collapse: separate;border-spacing: 0;}
.ant-table-footer {color: rgba(0, 0, 0, 0.85);background: #fafafa;}
.ant-table-thead > tr > th {color: rgba(0, 0, 0, 0.85);background: @table-header-bg;border-bottom: 1px solid @table-border-color;}
.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {background-color: rgba(0, 0, 0, 0.06);}
.ant-table-thead > tr:not(:last-child) > th[colspan] {border-bottom: 0;}
.ant-table-tbody > tr > td {border-bottom: 1px solid @table-border-color;}
.ant-table-tbody > tr > td > .ant-table-wrapper:only-child .ant-table-tbody > tr:last-child > td, .ant-table-tbody > tr > td > .ant-table-expanded-row-fixed > .ant-table-wrapper:only-child .ant-table-tbody > tr:last-child > td {border-bottom: 0;}
.ant-table-tbody > tr > td > .ant-table-wrapper:only-child .ant-table-tbody > tr:last-child > td:first-child, .ant-table-tbody > tr > td > .ant-table-expanded-row-fixed > .ant-table-wrapper:only-child .ant-table-tbody > tr:last-child > td:first-child, .ant-table-tbody > tr > td > .ant-table-wrapper:only-child .ant-table-tbody > tr:last-child > td:last-child, .ant-table-tbody > tr > td > .ant-table-expanded-row-fixed > .ant-table-wrapper:only-child .ant-table-tbody > tr:last-child > td:last-child {border-radius: 0;}
.ant-table-tbody > tr.ant-table-row:hover > td, .ant-table-tbody > tr > td.ant-table-cell-row-hover {background: @table-row-hover-bg;}
.ant-table-tbody > tr.ant-table-row-selected > td {background: @table-selected-row-bg;border-color: rgba(0, 0, 0, 0.03);}
.ant-table-tbody > tr.ant-table-row-selected:hover > td {background: darken(@table-selected-row-bg, 2%);}
.ant-table-summary {background: #fff;}
div.ant-table-summary {box-shadow: 0 -1px 0 @table-border-color;}
.ant-table-summary > tr > th, .ant-table-summary > tr > td {border-bottom: 1px solid @table-border-color;}
.ant-table-thead th.ant-table-column-has-sorters:hover {background: rgba(0, 0, 0, 0.04);}
.ant-table-thead th.ant-table-column-has-sorters:hover::before {background-color: transparent !important;}
.ant-table-thead th.ant-table-column-has-sorters.ant-table-cell-fix-left:hover, .ant-table-thead th.ant-table-column-has-sorters.ant-table-cell-fix-right:hover {background: #f5f5f5;}
.ant-table-thead th.ant-table-column-sort {background: #f5f5f5;}
.ant-table-thead th.ant-table-column-sort::before {background-color: transparent !important;}
td.ant-table-column-sort {background: #fafafa;}
.ant-table-column-sorter {color: #bfbfbf;}
.ant-table-column-sorter-up.active, .ant-table-column-sorter-down.active {color: @primary-color;}
.ant-table-column-sorters:hover .ant-table-column-sorter {color: #a6a6a6;}
.ant-table-filter-trigger {color: #bfbfbf;border-radius: 2px;}
.ant-table-filter-trigger:hover {color: rgba(0, 0, 0, 0.45);background: rgba(0, 0, 0, 0.04);}
.ant-table-filter-trigger.active {color: @primary-color;}
.ant-table-filter-dropdown {color: rgba(0, 0, 0, 0.85);background-color: #fff;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-table-filter-dropdown .ant-dropdown-menu {border: 0;box-shadow: none;}
.ant-table-filter-dropdown .ant-dropdown-menu:empty::after {color: rgba(0, 0, 0, 0.25);}
.ant-table-filter-dropdown-tree .ant-tree-treenode .ant-tree-node-content-wrapper:hover {background-color: #f5f5f5;}
.ant-table-filter-dropdown-tree .ant-tree-treenode-checkbox-checked .ant-tree-node-content-wrapper, .ant-table-filter-dropdown-tree .ant-tree-treenode-checkbox-checked .ant-tree-node-content-wrapper:hover {background-color: color(~`colorPalette("@{primary-color}", 2)`);}
.ant-table-filter-dropdown-search {border-bottom: 1px #f0f0f0 solid;}
.ant-table-filter-dropdown-search-input .anticon {color: rgba(0, 0, 0, 0.25);}
.ant-table-filter-dropdown-btns {background-color: inherit;border-top: 1px solid @table-border-color;}
table tr th.ant-table-selection-column::after {background-color: transparent !important;}
.ant-table-selection-extra .anticon {color: #bfbfbf;}
.ant-table-selection-extra .anticon:hover {color: #a6a6a6;}
.ant-table-row-expand-icon {color: @primary-color;color: inherit;background: #fff;border: 1px solid @table-border-color;border-radius: 2px;}
.ant-table-row-expand-icon:focus, .ant-table-row-expand-icon:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-table-row-expand-icon:active {color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-table-row-expand-icon:focus, .ant-table-row-expand-icon:hover, .ant-table-row-expand-icon:active {border-color: currentcolor;}
.ant-table-row-expand-icon::before, .ant-table-row-expand-icon::after {background: currentcolor;}
.ant-table-row-expand-icon-spaced {background: transparent;border: 0;}
tr.ant-table-expanded-row > td,
tr.ant-table-expanded-row:hover > td {background: #fbfbfb;}
.ant-table-empty .ant-table-tbody > tr.ant-table-placeholder {color: rgba(0, 0, 0, 0.25);}
.ant-table-tbody > tr.ant-table-placeholder:hover > td {background: #fff;}
.ant-table-cell-fix-left, .ant-table-cell-fix-right {background: #fff;}
.ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-container::before {box-shadow: inset 10px 0 8px -8px darken(@shadow-color, 5%);}
.ant-table-ping-left .ant-table-cell-fix-left-first::after, .ant-table-ping-left .ant-table-cell-fix-left-last::after {box-shadow: inset 10px 0 8px -8px darken(@shadow-color, 5%);}
.ant-table-ping-left .ant-table-cell-fix-left-last::before {background-color: transparent !important;}
.ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container::after {box-shadow: inset -10px 0 8px -8px darken(@shadow-color, 5%);}
.ant-table-ping-right .ant-table-cell-fix-right-first::after, .ant-table-ping-right .ant-table-cell-fix-right-last::after {box-shadow: inset -10px 0 8px -8px darken(@shadow-color, 5%);}
.ant-table-sticky-holder {background: #fff;}
.ant-table-sticky-scroll {background: lighten(@table-border-color, 80%);border-top: 1px solid @table-border-color;}
.ant-table-sticky-scroll-bar {background-color: rgba(0, 0, 0, 0.35);border-radius: 4px;}
.ant-table-sticky-scroll-bar:hover {background-color: fade(@table-sticky-scroll-bar-bg, 80%);}
.ant-table-sticky-scroll-bar-active {background-color: fade(@table-sticky-scroll-bar-bg, 80%);}
.ant-table-title {border-radius: 2px 2px 0 0;}
.ant-table-title + .ant-table-container {border-top-left-radius: 0;border-top-right-radius: 0;}
.ant-table-title + .ant-table-container table > thead > tr:first-child th:first-child {border-radius: 0;}
.ant-table-title + .ant-table-container table > thead > tr:first-child th:last-child {border-radius: 0;}
.ant-table-container {border-top-left-radius: 2px;border-top-right-radius: 2px;}
.ant-table-container table > thead > tr:first-child th:first-child {border-top-left-radius: 2px;}
.ant-table-container table > thead > tr:first-child th:last-child {border-top-right-radius: 2px;}
.ant-table-footer {border-radius: 0 0 2px 2px;}
.ant-tabs-top > .ant-tabs-nav::before, .ant-tabs-bottom > .ant-tabs-nav::before, .ant-tabs-top > div > .ant-tabs-nav::before, .ant-tabs-bottom > div > .ant-tabs-nav::before {border-bottom: 1px solid #f0f0f0;}
.ant-tabs-top > .ant-tabs-nav .ant-tabs-nav-wrap::before, .ant-tabs-bottom > .ant-tabs-nav .ant-tabs-nav-wrap::before, .ant-tabs-top > div > .ant-tabs-nav .ant-tabs-nav-wrap::before, .ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-nav-wrap::before {box-shadow: inset 10px 0 8px -8px fade(@shadow-color, 8%);}
.ant-tabs-top > .ant-tabs-nav .ant-tabs-nav-wrap::after, .ant-tabs-bottom > .ant-tabs-nav .ant-tabs-nav-wrap::after, .ant-tabs-top > div > .ant-tabs-nav .ant-tabs-nav-wrap::after, .ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-nav-wrap::after {box-shadow: inset -10px 0 8px -8px fade(@shadow-color, 8%);}
.ant-tabs-left > .ant-tabs-nav .ant-tabs-nav-wrap::before, .ant-tabs-right > .ant-tabs-nav .ant-tabs-nav-wrap::before, .ant-tabs-left > div > .ant-tabs-nav .ant-tabs-nav-wrap::before, .ant-tabs-right > div > .ant-tabs-nav .ant-tabs-nav-wrap::before {box-shadow: inset 0 10px 8px -8px fade(@shadow-color, 8%);}
.ant-tabs-left > .ant-tabs-nav .ant-tabs-nav-wrap::after, .ant-tabs-right > .ant-tabs-nav .ant-tabs-nav-wrap::after, .ant-tabs-left > div > .ant-tabs-nav .ant-tabs-nav-wrap::after, .ant-tabs-right > div > .ant-tabs-nav .ant-tabs-nav-wrap::after {box-shadow: inset 0 -10px 8px -8px fade(@shadow-color, 8%);}
.ant-tabs-left > .ant-tabs-content-holder, .ant-tabs-left > div > .ant-tabs-content-holder {border-left: 1px solid #f0f0f0;}
.ant-tabs-right > .ant-tabs-content-holder, .ant-tabs-right > div > .ant-tabs-content-holder {border-right: 1px solid #f0f0f0;}
.ant-tabs-dropdown {color: rgba(0, 0, 0, 0.85);}
.ant-tabs-dropdown-menu {background-color: #fff;background-clip: padding-box;border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-tabs-dropdown-menu-item {color: rgba(0, 0, 0, 0.85);}
.ant-tabs-dropdown-menu-item-remove {color: rgba(0, 0, 0, 0.45);background: transparent;border: 0;}
.ant-tabs-dropdown-menu-item-remove:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-tabs-dropdown-menu-item:hover {background: #f5f5f5;}
.ant-tabs-dropdown-menu-item-disabled, .ant-tabs-dropdown-menu-item-disabled:hover {color: rgba(0, 0, 0, 0.25);background: transparent;}
.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab, .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {background: #fafafa;border: 1px solid #f0f0f0;}
.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active, .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab-active {color: @primary-color;background: #fff;}
.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab, .ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab {border-radius: 2px 2px 0 0;}
.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active, .ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab-active {border-bottom-color: #fff;}
.ant-tabs-card.ant-tabs-bottom > .ant-tabs-nav .ant-tabs-tab, .ant-tabs-card.ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-tab {border-radius: 0 0 2px 2px;}
.ant-tabs-card.ant-tabs-bottom > .ant-tabs-nav .ant-tabs-tab-active, .ant-tabs-card.ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-tab-active {border-top-color: #fff;}
.ant-tabs-card.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab, .ant-tabs-card.ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab {border-radius: 2px 0 0 2px;}
.ant-tabs-card.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab-active, .ant-tabs-card.ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab-active {border-right-color: #fff;}
.ant-tabs-card.ant-tabs-right > .ant-tabs-nav .ant-tabs-tab, .ant-tabs-card.ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab {border-radius: 0 2px 2px 0;}
.ant-tabs-card.ant-tabs-right > .ant-tabs-nav .ant-tabs-tab-active, .ant-tabs-card.ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab-active {border-left-color: #fff;}
.ant-tabs {color: rgba(0, 0, 0, 0.85);}
.ant-tabs > .ant-tabs-nav .ant-tabs-nav-more, .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-more {background: transparent;border: 0;}
.ant-tabs > .ant-tabs-nav .ant-tabs-nav-add, .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-add {background: #fafafa;border: 1px solid #f0f0f0;border-radius: 2px 2px 0 0;}
.ant-tabs > .ant-tabs-nav .ant-tabs-nav-add:hover, .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-add:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-tabs > .ant-tabs-nav .ant-tabs-nav-add:active, .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-add:active, .ant-tabs > .ant-tabs-nav .ant-tabs-nav-add:focus, .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-add:focus {color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-tabs-ink-bar {background: @primary-color;}
.ant-tabs-tab {background: transparent;border: 0;}
.ant-tabs-tab-btn:focus, .ant-tabs-tab-remove:focus, .ant-tabs-tab-btn:active, .ant-tabs-tab-remove:active {color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-tabs-tab-remove {color: rgba(0, 0, 0, 0.45);background: transparent;border: none;}
.ant-tabs-tab-remove:hover {color: rgba(0, 0, 0, 0.85);}
.ant-tabs-tab:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {color: @primary-color;}
.ant-tabs-tab.ant-tabs-tab-disabled {color: rgba(0, 0, 0, 0.25);}
.ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-btn:focus, .ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-remove:focus, .ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-btn:active, .ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-remove:active {color: rgba(0, 0, 0, 0.25);}
.ant-tag {color: rgba(0, 0, 0, 0.85);background: #fafafa;border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-tag, .ant-tag a, .ant-tag a:hover {color: @tag-default-color;}
.ant-tag-close-icon {color: rgba(0, 0, 0, 0.45);}
.ant-tag-close-icon:hover {color: rgba(0, 0, 0, 0.85);}
.ant-tag-has-color {border-color: transparent;}
.ant-tag-has-color, .ant-tag-has-color a, .ant-tag-has-color a:hover, .ant-tag-has-color .anticon-close, .ant-tag-has-color .anticon-close:hover {color: #fff;}
.ant-tag-checkable {background-color: transparent;border-color: transparent;}
.ant-tag-checkable:not(.ant-tag-checkable-checked):hover {color: @primary-color;}
.ant-tag-checkable:active, .ant-tag-checkable-checked {color: #fff;}
.ant-tag-checkable-checked {background-color: @primary-color;}
.ant-tag-checkable:active {background-color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-tag-pink {color: #c41d7f;background: #fff0f6;border-color: #ffadd2;}
.ant-tag-pink-inverse {color: #fff;background: #eb2f96;border-color: #eb2f96;}
.ant-tag-magenta {color: #c41d7f;background: #fff0f6;border-color: #ffadd2;}
.ant-tag-magenta-inverse {color: #fff;background: #eb2f96;border-color: #eb2f96;}
.ant-tag-red {color: #cf1322;background: #fff1f0;border-color: #ffa39e;}
.ant-tag-red-inverse {color: #fff;background: #f5222d;border-color: #f5222d;}
.ant-tag-volcano {color: #d4380d;background: #fff2e8;border-color: #ffbb96;}
.ant-tag-volcano-inverse {color: #fff;background: #fa541c;border-color: #fa541c;}
.ant-tag-orange {color: #d46b08;background: #fff7e6;border-color: #ffd591;}
.ant-tag-orange-inverse {color: #fff;background: #fa8c16;border-color: #fa8c16;}
.ant-tag-yellow {color: #d4b106;background: #feffe6;border-color: #fffb8f;}
.ant-tag-yellow-inverse {color: #fff;background: #fadb14;border-color: #fadb14;}
.ant-tag-gold {color: #d48806;background: #fffbe6;border-color: #ffe58f;}
.ant-tag-gold-inverse {color: #fff;background: #faad14;border-color: #faad14;}
.ant-tag-cyan {color: #08979c;background: #e6fffb;border-color: #87e8de;}
.ant-tag-cyan-inverse {color: #fff;background: #13c2c2;border-color: #13c2c2;}
.ant-tag-lime {color: #7cb305;background: #fcffe6;border-color: #eaff8f;}
.ant-tag-lime-inverse {color: #fff;background: #a0d911;border-color: #a0d911;}
.ant-tag-green {color: #389e0d;background: #f6ffed;border-color: #b7eb8f;}
.ant-tag-green-inverse {color: #fff;background: #52c41a;border-color: #52c41a;}
.ant-tag-blue {color: #096dd9;background: #e6f7ff;border-color: #91d5ff;}
.ant-tag-blue-inverse {color: #fff;background: #1890ff;border-color: #1890ff;}
.ant-tag-geekblue {color: #1d39c4;background: #f0f5ff;border-color: #adc6ff;}
.ant-tag-geekblue-inverse {color: #fff;background: #2f54eb;border-color: #2f54eb;}
.ant-tag-purple {color: #531dab;background: #f9f0ff;border-color: #d3adf7;}
.ant-tag-purple-inverse {color: #fff;background: #722ed1;border-color: #722ed1;}
.ant-tag-success {color: @success-color;background: color(~`colorPalette("@{success-color}", 1)`);border-color: color(~`colorPalette("@{success-color}", 1)`);}
.ant-tag-processing {color: @primary-color;background: color(~`colorPalette("@{primary-color}", 1)`);border-color: color(~`colorPalette("@{primary-color}", 3)`);}
.ant-tag-error {color: @error-color;background: color(~`colorPalette("@{error-color}", 1)`);border-color: color(~`colorPalette("@{error-color}", 3)`);}
.ant-tag-warning {color: @warning-color;background: color(~`colorPalette("@{warning-color}", 1)`);border-color: color(~`colorPalette("@{warning-color}", 3)`);}
.ant-timeline {color: rgba(0, 0, 0, 0.85);}
.ant-timeline-item-tail {border-left: 2px solid #f0f0f0;}
.ant-timeline-item-pending .ant-timeline-item-head {background-color: transparent;}
.ant-timeline-item-head {background-color: #fff;border: 2px solid transparent;border-radius: 100px;}
.ant-timeline-item-head-blue {color: @primary-color;border-color: @primary-color;}
.ant-timeline-item-head-red {color: @error-color;border-color: @error-color;}
.ant-timeline-item-head-green {color: @success-color;border-color: @success-color;}
.ant-timeline-item-head-gray {color: rgba(0, 0, 0, 0.25);border-color: rgba(0, 0, 0, 0.25);}
.ant-timeline-item-head-custom {border: 0;border-radius: 0;}
.ant-timeline.ant-timeline-pending .ant-timeline-item-last .ant-timeline-item-tail {border-left: 2px dotted #f0f0f0;}
.ant-timeline.ant-timeline-reverse .ant-timeline-item-pending .ant-timeline-item-tail {border-left: 2px dotted #f0f0f0;}
.ant-timeline-rtl .ant-timeline-item-tail {border-right: 2px solid #f0f0f0;border-left: none;}
.ant-timeline-rtl.ant-timeline.ant-timeline-pending .ant-timeline-item-last .ant-timeline-item-tail {border-right: 2px dotted #f0f0f0;border-left: none;}
.ant-timeline-rtl.ant-timeline.ant-timeline-reverse .ant-timeline-item-pending .ant-timeline-item-tail {border-right: 2px dotted #f0f0f0;border-left: none;}
.ant-tooltip {color: rgba(0, 0, 0, 0.85);}
.ant-tooltip-inner {color: #fff;background-color: rgba(0, 0, 0, 0.75);border-radius: 2px;box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);}
.ant-tooltip-arrow {background: transparent;}
.ant-tooltip-arrow-content {background-color: rgba(0, 0, 0, 0.75);}
.ant-tooltip-placement-top .ant-tooltip-arrow-content, .ant-tooltip-placement-topLeft .ant-tooltip-arrow-content, .ant-tooltip-placement-topRight .ant-tooltip-arrow-content {box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);}
.ant-tooltip-placement-right .ant-tooltip-arrow-content, .ant-tooltip-placement-rightTop .ant-tooltip-arrow-content, .ant-tooltip-placement-rightBottom .ant-tooltip-arrow-content {box-shadow: -3px 3px 7px rgba(0, 0, 0, 0.07);}
.ant-tooltip-placement-left .ant-tooltip-arrow-content, .ant-tooltip-placement-leftTop .ant-tooltip-arrow-content, .ant-tooltip-placement-leftBottom .ant-tooltip-arrow-content {box-shadow: 3px -3px 7px rgba(0, 0, 0, 0.07);}
.ant-tooltip-placement-bottom .ant-tooltip-arrow-content, .ant-tooltip-placement-bottomLeft .ant-tooltip-arrow-content, .ant-tooltip-placement-bottomRight .ant-tooltip-arrow-content {box-shadow: -3px -3px 7px rgba(0, 0, 0, 0.07);}
.ant-tooltip-pink .ant-tooltip-inner {background-color: #eb2f96;}
.ant-tooltip-pink .ant-tooltip-arrow-content {background-color: #eb2f96;}
.ant-tooltip-magenta .ant-tooltip-inner {background-color: #eb2f96;}
.ant-tooltip-magenta .ant-tooltip-arrow-content {background-color: #eb2f96;}
.ant-tooltip-red .ant-tooltip-inner {background-color: #f5222d;}
.ant-tooltip-red .ant-tooltip-arrow-content {background-color: #f5222d;}
.ant-tooltip-volcano .ant-tooltip-inner {background-color: #fa541c;}
.ant-tooltip-volcano .ant-tooltip-arrow-content {background-color: #fa541c;}
.ant-tooltip-orange .ant-tooltip-inner {background-color: #fa8c16;}
.ant-tooltip-orange .ant-tooltip-arrow-content {background-color: #fa8c16;}
.ant-tooltip-yellow .ant-tooltip-inner {background-color: #fadb14;}
.ant-tooltip-yellow .ant-tooltip-arrow-content {background-color: #fadb14;}
.ant-tooltip-gold .ant-tooltip-inner {background-color: #faad14;}
.ant-tooltip-gold .ant-tooltip-arrow-content {background-color: #faad14;}
.ant-tooltip-cyan .ant-tooltip-inner {background-color: #13c2c2;}
.ant-tooltip-cyan .ant-tooltip-arrow-content {background-color: #13c2c2;}
.ant-tooltip-lime .ant-tooltip-inner {background-color: #a0d911;}
.ant-tooltip-lime .ant-tooltip-arrow-content {background-color: #a0d911;}
.ant-tooltip-green .ant-tooltip-inner {background-color: #52c41a;}
.ant-tooltip-green .ant-tooltip-arrow-content {background-color: #52c41a;}
.ant-tooltip-blue .ant-tooltip-inner {background-color: #1890ff;}
.ant-tooltip-blue .ant-tooltip-arrow-content {background-color: #1890ff;}
.ant-tooltip-geekblue .ant-tooltip-inner {background-color: #2f54eb;}
.ant-tooltip-geekblue .ant-tooltip-arrow-content {background-color: #2f54eb;}
.ant-tooltip-purple .ant-tooltip-inner {background-color: #722ed1;}
.ant-tooltip-purple .ant-tooltip-arrow-content {background-color: #722ed1;}
.ant-transfer-customize-list .ant-table-wrapper .ant-table-small {border: 0;border-radius: 0;}
.ant-transfer-customize-list .ant-table-wrapper .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th {background: @table-header-bg;}
.ant-transfer-customize-list .ant-table-wrapper .ant-table-small > .ant-table-content .ant-table-row:last-child td {border-bottom: 1px solid #f0f0f0;}
.ant-transfer-customize-list .ant-input[disabled] {background-color: transparent;}
.ant-transfer {color: rgba(0, 0, 0, 0.85);}
.ant-transfer-disabled .ant-transfer-list {background: #f5f5f5;}
.ant-transfer-list {border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-transfer-list-search .anticon-search {color: rgba(0, 0, 0, 0.25);}
.ant-transfer-list-header {color: rgba(0, 0, 0, 0.85);background: #fff;border-bottom: 1px solid #f0f0f0;border-radius: 2px 2px 0 0;}
.ant-transfer-list-content-item-remove {color: @primary-color;color: #d9d9d9;}
.ant-transfer-list-content-item-remove:focus, .ant-transfer-list-content-item-remove:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-transfer-list-content-item-remove:active {color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-transfer-list-content-item-remove:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {background-color: #f5f5f5;}
.ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled).ant-transfer-list-content-item-checked:hover {background-color: darken(@item-active-bg, 2%);}
.ant-transfer-list-content-show-remove .ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {background: transparent;}
.ant-transfer-list-content-item-checked {background-color: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-transfer-list-content-item-disabled {color: rgba(0, 0, 0, 0.25);}
.ant-transfer-list-pagination {border-top: 1px solid #f0f0f0;}
.ant-transfer-list-body-not-found {color: rgba(0, 0, 0, 0.25);}
.ant-transfer-list-footer {border-top: 1px solid #f0f0f0;}
.ant-tree.ant-tree-directory .ant-tree-treenode:hover::before {background: #f5f5f5;}
.ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper {border-radius: 0;}
.ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper:hover {background: transparent;}
.ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected {color: #fff;background: transparent;}
.ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover::before, .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {background: @primary-color;}
.ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher {color: #fff;}
.ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper {color: #fff;background: transparent;}
.ant-tree-checkbox {color: rgba(0, 0, 0, 0.85);}
.ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner, .ant-tree-checkbox:hover .ant-tree-checkbox-inner, .ant-tree-checkbox-input:focus + .ant-tree-checkbox-inner {border-color: @primary-color;}
.ant-tree-checkbox-checked::after {border: 1px solid @primary-color;border-radius: 2px;animation-fill-mode: backwards;}
.ant-tree-checkbox-inner {background-color: #fff;border: 1px solid #d9d9d9;border-radius: 2px;border-collapse: separate;}
.ant-tree-checkbox-inner::after {border: 2px solid #fff;border-top: 0;border-left: 0;}
.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {border: 2px solid #fff;border-top: 0;border-left: 0;}
.ant-tree-checkbox-checked .ant-tree-checkbox-inner {background-color: @primary-color;border-color: @primary-color;}
.ant-tree-checkbox-disabled.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {border-color: rgba(0, 0, 0, 0.25);}
.ant-tree-checkbox-disabled .ant-tree-checkbox-inner {background-color: #f5f5f5;border-color: #d9d9d9 !important;}
.ant-tree-checkbox-disabled .ant-tree-checkbox-inner::after {border-color: #f5f5f5;border-collapse: separate;}
.ant-tree-checkbox-disabled + span {color: rgba(0, 0, 0, 0.25);}
.ant-tree-checkbox-wrapper {color: rgba(0, 0, 0, 0.85);}
.ant-tree-checkbox-group {color: rgba(0, 0, 0, 0.85);}
.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner {background-color: #fff;border-color: #d9d9d9;}
.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {background-color: @primary-color;border: 0;}
.ant-tree-checkbox-indeterminate.ant-tree-checkbox-disabled .ant-tree-checkbox-inner::after {background-color: rgba(0, 0, 0, 0.25);border-color: rgba(0, 0, 0, 0.25);}
.ant-tree {color: rgba(0, 0, 0, 0.85);background: #fff;border-radius: 2px;}
.ant-tree-focused:not(:hover):not(.ant-tree-active-focused) {background: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-treenode.dragging::after {border: 1px solid @primary-color;animation-fill-mode: forwards;}
.ant-tree .ant-tree-treenode-disabled .ant-tree-node-content-wrapper {color: rgba(0, 0, 0, 0.25);}
.ant-tree .ant-tree-treenode-disabled .ant-tree-node-content-wrapper:hover {background: transparent;}
.ant-tree .ant-tree-treenode-active .ant-tree-node-content-wrapper {background: #f5f5f5;}
.ant-tree .ant-tree-treenode:not(.ant-tree .ant-tree-treenode-disabled).filter-node .ant-tree-title {color: inherit;}
.ant-tree-switcher-loading-icon {color: @primary-color;}
.ant-tree-switcher-leaf-line::before {border-right: 1px solid #d9d9d9;}
.ant-tree-switcher-leaf-line::after {border-bottom: 1px solid #d9d9d9;}
.ant-tree .ant-tree-node-content-wrapper {color: inherit;background: transparent;border-radius: 2px;}
.ant-tree .ant-tree-node-content-wrapper:hover {background-color: #f5f5f5;}
.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {background-color: color(~`colorPalette("@{primary-color}", 2)`);}
.ant-tree-unselectable .ant-tree-node-content-wrapper:hover {background-color: transparent;}
.ant-tree-node-content-wrapper .ant-tree-drop-indicator {background-color: @primary-color;border-radius: 1px;}
.ant-tree-node-content-wrapper .ant-tree-drop-indicator::after {background-color: transparent;border: 2px solid @primary-color;border-radius: 50%;}
.ant-tree .ant-tree-treenode.drop-container > [draggable] {box-shadow: 0 0 0 2px @primary-color;}
.ant-tree-show-line .ant-tree-indent-unit::before {border-right: 1px solid #d9d9d9;}
.ant-tree-show-line .ant-tree-switcher {background: #fff;}
.ant-tree-rtl.ant-tree-show-line .ant-tree-indent-unit::before {border-right: none;border-left: 1px solid #d9d9d9;}
.ant-select-tree-checkbox {color: rgba(0, 0, 0, 0.85);}
.ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox-inner, .ant-select-tree-checkbox:hover .ant-select-tree-checkbox-inner, .ant-select-tree-checkbox-input:focus + .ant-select-tree-checkbox-inner {border-color: @primary-color;}
.ant-select-tree-checkbox-checked::after {border: 1px solid @primary-color;border-radius: 2px;animation-fill-mode: backwards;}
.ant-select-tree-checkbox-inner {background-color: #fff;border: 1px solid #d9d9d9;border-radius: 2px;border-collapse: separate;}
.ant-select-tree-checkbox-inner::after {border: 2px solid #fff;border-top: 0;border-left: 0;}
.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner::after {border: 2px solid #fff;border-top: 0;border-left: 0;}
.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner {background-color: @primary-color;border-color: @primary-color;}
.ant-select-tree-checkbox-disabled.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner::after {border-color: rgba(0, 0, 0, 0.25);}
.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner {background-color: #f5f5f5;border-color: #d9d9d9 !important;}
.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner::after {border-color: #f5f5f5;border-collapse: separate;}
.ant-select-tree-checkbox-disabled + span {color: rgba(0, 0, 0, 0.25);}
.ant-select-tree-checkbox-wrapper {color: rgba(0, 0, 0, 0.85);}
.ant-select-tree-checkbox-group {color: rgba(0, 0, 0, 0.85);}
.ant-select-tree-checkbox-indeterminate .ant-select-tree-checkbox-inner {background-color: #fff;border-color: #d9d9d9;}
.ant-select-tree-checkbox-indeterminate .ant-select-tree-checkbox-inner::after {background-color: @primary-color;border: 0;}
.ant-select-tree-checkbox-indeterminate.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner::after {background-color: rgba(0, 0, 0, 0.25);border-color: rgba(0, 0, 0, 0.25);}
.ant-tree-select-dropdown .ant-select-tree {border-radius: 0;}
.ant-select-tree {color: rgba(0, 0, 0, 0.85);background: #fff;border-radius: 2px;}
.ant-select-tree-focused:not(:hover):not(.ant-select-tree-active-focused) {background: color(~`colorPalette("@{primary-color}", 1)`);}
.ant-select-tree.ant-select-tree-block-node .ant-select-tree-list-holder-inner .ant-select-tree-treenode.dragging::after {border: 1px solid @primary-color;animation-fill-mode: forwards;}
.ant-select-tree .ant-select-tree-treenode-disabled .ant-select-tree-node-content-wrapper {color: rgba(0, 0, 0, 0.25);}
.ant-select-tree .ant-select-tree-treenode-disabled .ant-select-tree-node-content-wrapper:hover {background: transparent;}
.ant-select-tree .ant-select-tree-treenode-active .ant-select-tree-node-content-wrapper {background: #f5f5f5;}
.ant-select-tree .ant-select-tree-treenode:not(.ant-select-tree .ant-select-tree-treenode-disabled).filter-node .ant-select-tree-title {color: inherit;}
.ant-select-tree-switcher-loading-icon {color: @primary-color;}
.ant-select-tree-switcher-leaf-line::before {border-right: 1px solid #d9d9d9;}
.ant-select-tree-switcher-leaf-line::after {border-bottom: 1px solid #d9d9d9;}
.ant-select-tree .ant-select-tree-node-content-wrapper {color: inherit;background: transparent;border-radius: 2px;}
.ant-select-tree .ant-select-tree-node-content-wrapper:hover {background-color: #f5f5f5;}
.ant-select-tree .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {background-color: color(~`colorPalette("@{primary-color}", 2)`);}
.ant-select-tree-unselectable .ant-select-tree-node-content-wrapper:hover {background-color: transparent;}
.ant-select-tree-node-content-wrapper .ant-tree-drop-indicator {background-color: @primary-color;border-radius: 1px;}
.ant-select-tree-node-content-wrapper .ant-tree-drop-indicator::after {background-color: transparent;border: 2px solid @primary-color;border-radius: 50%;}
.ant-select-tree .ant-select-tree-treenode.drop-container > [draggable] {box-shadow: 0 0 0 2px @primary-color;}
.ant-select-tree-show-line .ant-select-tree-indent-unit::before {border-right: 1px solid #d9d9d9;}
.ant-select-tree-show-line .ant-select-tree-switcher {background: #fff;}
.ant-typography {color: rgba(0, 0, 0, 0.85);}
.ant-typography.ant-typography-secondary {color: rgba(0, 0, 0, 0.45);}
.ant-typography.ant-typography-success {color: @success-color;}
.ant-typography.ant-typography-warning {color: @warning-color;}
.ant-typography.ant-typography-danger {color: @error-color;}
a.ant-typography.ant-typography-danger:active,
a.ant-typography.ant-typography-danger:focus,
a.ant-typography.ant-typography-danger:hover {color: color(~`colorPalette("@{error-color}", 5)`);}
.ant-typography.ant-typography-disabled {color: rgba(0, 0, 0, 0.25);}
h1.ant-typography, .ant-typography h1 {color: rgba(0, 0, 0, 0.85);}
h2.ant-typography, .ant-typography h2 {color: rgba(0, 0, 0, 0.85);}
h3.ant-typography, .ant-typography h3 {color: rgba(0, 0, 0, 0.85);}
h4.ant-typography, .ant-typography h4 {color: rgba(0, 0, 0, 0.85);}
h5.ant-typography, .ant-typography h5 {color: rgba(0, 0, 0, 0.85);}
a.ant-typography, .ant-typography a {color: @primary-color;}
a.ant-typography:focus, .ant-typography a:focus,
a.ant-typography:hover, .ant-typography a:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
a.ant-typography:active, .ant-typography a:active {color: color(~`colorPalette("@{primary-color}", 7)`);}
a.ant-typography[disabled], .ant-typography a[disabled],
a.ant-typography.ant-typography-disabled, .ant-typography a.ant-typography-disabled {color: rgba(0, 0, 0, 0.25);}
a.ant-typography[disabled]:active, .ant-typography a[disabled]:active,
a.ant-typography.ant-typography-disabled:active, .ant-typography a.ant-typography-disabled:active,
a.ant-typography[disabled]:hover, .ant-typography a[disabled]:hover,
a.ant-typography.ant-typography-disabled:hover, .ant-typography a.ant-typography-disabled:hover {color: rgba(0, 0, 0, 0.25);}
.ant-typography code {background: rgba(150, 150, 150, 0.1);border: 1px solid rgba(100, 100, 100, 0.2);border-radius: 3px;}
.ant-typography kbd {background: rgba(150, 150, 150, 0.06);border: 1px solid rgba(100, 100, 100, 0.2);border-bottom-width: 2px;border-radius: 3px;}
.ant-typography mark {background-color: #ffe58f;}
.ant-typography-expand, .ant-typography-edit, .ant-typography-copy {color: @primary-color;}
.ant-typography-expand:focus, .ant-typography-edit:focus, .ant-typography-copy:focus, .ant-typography-expand:hover, .ant-typography-edit:hover, .ant-typography-copy:hover {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-typography-expand:active, .ant-typography-edit:active, .ant-typography-copy:active {color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-typography-copy-success, .ant-typography-copy-success:hover, .ant-typography-copy-success:focus {color: @success-color;}
.ant-typography-edit-content-confirm {color: rgba(0, 0, 0, 0.45);}
.ant-typography pre {background: rgba(150, 150, 150, 0.1);border: 1px solid rgba(100, 100, 100, 0.2);border-radius: 3px;}
.ant-typography pre code {background: transparent;border: 0;}
.ant-typography blockquote {border-left: 4px solid rgba(100, 100, 100, 0.2);}
.ant-upload {color: rgba(0, 0, 0, 0.85);}
.ant-upload.ant-upload-select-picture-card {background-color: #fafafa;border: 1px dashed #d9d9d9;border-radius: 2px;}
.ant-upload.ant-upload-select-picture-card:hover {border-color: @primary-color;}
.ant-upload-disabled.ant-upload.ant-upload-select-picture-card:hover {border-color: #d9d9d9;}
.ant-upload.ant-upload-drag {background: #fafafa;border: 1px dashed #d9d9d9;border-radius: 2px;}
.ant-upload.ant-upload-drag.ant-upload-drag-hover:not(.ant-upload-disabled) {border-color: color(~`colorPalette("@{primary-color}", 7)`);}
.ant-upload.ant-upload-drag:not(.ant-upload-disabled):hover {border-color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-upload.ant-upload-drag p.ant-upload-drag-icon .anticon {color: color(~`colorPalette("@{primary-color}", 5)`);}
.ant-upload.ant-upload-drag p.ant-upload-text {color: rgba(0, 0, 0, 0.85);}
.ant-upload.ant-upload-drag p.ant-upload-hint {color: rgba(0, 0, 0, 0.45);}
.ant-upload.ant-upload-drag .anticon-plus {color: rgba(0, 0, 0, 0.25);}
.ant-upload.ant-upload-drag .anticon-plus:hover {color: rgba(0, 0, 0, 0.45);}
.ant-upload.ant-upload-drag:hover .anticon-plus {color: rgba(0, 0, 0, 0.45);}
.ant-upload-list {color: rgba(0, 0, 0, 0.85);}
.ant-upload-list-item-card-actions .anticon {color: rgba(0, 0, 0, 0.45);}
.ant-upload-list-item-info .anticon-loading .anticon, .ant-upload-list-item-info .ant-upload-text-icon .anticon {color: rgba(0, 0, 0, 0.45);}
.ant-upload-list-item .anticon-close {color: rgba(0, 0, 0, 0.45);}
.ant-upload-list-item .anticon-close:hover {color: rgba(0, 0, 0, 0.85);}
.ant-upload-list-item:hover .ant-upload-list-item-info {background-color: #f5f5f5;}
.ant-upload-list-item-error, .ant-upload-list-item-error .ant-upload-text-icon > .anticon, .ant-upload-list-item-error .ant-upload-list-item-name {color: @error-color;}
.ant-upload-list-item-error .ant-upload-list-item-card-actions .anticon {color: @error-color;}
.ant-upload-list-picture .ant-upload-list-item, .ant-upload-list-picture-card .ant-upload-list-item {border: 1px solid #d9d9d9;border-radius: 2px;}
.ant-upload-list-picture .ant-upload-list-item:hover, .ant-upload-list-picture-card .ant-upload-list-item:hover {background: transparent;}
.ant-upload-list-picture .ant-upload-list-item-error, .ant-upload-list-picture-card .ant-upload-list-item-error {border-color: @error-color;}
.ant-upload-list-picture .ant-upload-list-item:hover .ant-upload-list-item-info, .ant-upload-list-picture-card .ant-upload-list-item:hover .ant-upload-list-item-info {background: transparent;}
.ant-upload-list-picture .ant-upload-list-item-uploading, .ant-upload-list-picture-card .ant-upload-list-item-uploading {border-style: dashed;}
.ant-upload-list-picture .ant-upload-list-item-error .ant-upload-list-item-thumbnail .anticon svg path[fill='#e6f7ff'], .ant-upload-list-picture-card .ant-upload-list-item-error .ant-upload-list-item-thumbnail .anticon svg path[fill='#e6f7ff'] {fill: color(~`colorPalette("@{error-color}", 1)`);}
.ant-upload-list-picture .ant-upload-list-item-error .ant-upload-list-item-thumbnail .anticon svg path[fill='#1890ff'], .ant-upload-list-picture-card .ant-upload-list-item-error .ant-upload-list-item-thumbnail .anticon svg path[fill='#1890ff'] {fill: @error-color;}
.ant-upload-list-picture-card .ant-upload-list-item-info::before {background-color: rgba(0, 0, 0, 0.5);}
.ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye, .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-download, .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete {color: rgba(255, 255, 255, 0.85);}
.ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye:hover, .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-download:hover, .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete:hover {color: #fff;}
.ant-upload-list-picture-card .ant-upload-list-item-uploading.ant-upload-list-item {background-color: #fafafa;}
.ant-upload-list .ant-upload-animate-inline-appear, .ant-upload-list .ant-upload-animate-inline-enter, .ant-upload-list .ant-upload-animate-inline-leave {animation-fill-mode: cubic-bezier(0.78, 0.14, 0.15, 0.86);}
.bezierEasingMixin() {
@functions: ~`(function() {var NEWTON_ITERATIONS = 4;var NEWTON_MIN_SLOPE = 0.001;var SUBDIVISION_PRECISION = 0.0000001;var SUBDIVISION_MAX_ITERATIONS = 10;var kSplineTableSize = 11;var kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);var float32ArraySupported = typeof Float32Array === 'function';function A (aA1, aA2) { return 1.0 - 3.0 * aA2 + 3.0 * aA1; }
  function B (aA1, aA2) { return 3.0 * aA2 - 6.0 * aA1; }
  function C (aA1)      { return 3.0 * aA1; }
  function calcBezier (aT, aA1, aA2) { return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT; }
  function getSlope (aT, aA1, aA2) { return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1); }
  function binarySubdivide (aX, aA, aB, mX1, mX2) {var currentX, currentT, i = 0;do {currentT = aA + (aB - aA) / 2.0;currentX = calcBezier(currentT, mX1, mX2) - aX;if (currentX > 0.0) {aB = currentT;} else {aA = currentT;}
    } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);return currentT;}
  function newtonRaphsonIterate (aX, aGuessT, mX1, mX2) {for (var i = 0; i < NEWTON_ITERATIONS; ++i) {var currentSlope = getSlope(aGuessT, mX1, mX2);if (currentSlope === 0.0) {return aGuessT;}
     var currentX = calcBezier(aGuessT, mX1, mX2) - aX;aGuessT -= currentX / currentSlope;}
   return aGuessT;}
  var BezierEasing = function (mX1, mY1, mX2, mY2) {if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) {throw new Error('bezier x values must be in [0, 1] range');}
    var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);if (mX1 !== mY1 || mX2 !== mY2) {for (var i = 0; i < kSplineTableSize; ++i) {sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);}
    }
    function getTForX (aX) {var intervalStart = 0.0;var currentSample = 1;var lastSample = kSplineTableSize - 1;for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {intervalStart += kSampleStepSize;}
      --currentSample;var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);var guessForT = intervalStart + dist * kSampleStepSize;var initialSlope = getSlope(guessForT, mX1, mX2);if (initialSlope >= NEWTON_MIN_SLOPE) {return newtonRaphsonIterate(aX, guessForT, mX1, mX2);} else if (initialSlope === 0.0) {return guessForT;} else {return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);}
    }
    return function BezierEasing (x) {if (mX1 === mY1 && mX2 === mY2) {return x; 
      }
      if (x === 0) {return 0;}
      if (x === 1) {return 1;}
      return calcBezier(getTForX(x), mY1, mY2);};};this.colorEasing = BezierEasing(0.26, 0.09, 0.37, 0.18);return '';})()`;}
.bezierEasingMixin();
.tinyColorMixin() {
@functions: ~`(function() {
var trimLeft = /^\s+/,
    trimRight = /\s+$/,
    tinyCounter = 0,
    mathRound = Math.round,
    mathMin = Math.min,
    mathMax = Math.max,
    mathRandom = Math.random;
function tinycolor (color, opts) {color = (color) ? color : '';opts = opts || { };if (color instanceof tinycolor) {return color;}
    if (!(this instanceof tinycolor)) {return new tinycolor(color, opts);}
    var rgb = inputToRGB(color);this._originalInput = color,
    this._r = rgb.r,
    this._g = rgb.g,
    this._b = rgb.b,
    this._a = rgb.a,
    this._roundA = mathRound(100*this._a) / 100,
    this._format = opts.format || rgb.format;this._gradientType = opts.gradientType;if (this._r < 1) { this._r = mathRound(this._r); }
    if (this._g < 1) { this._g = mathRound(this._g); }
    if (this._b < 1) { this._b = mathRound(this._b); }
    this._ok = rgb.ok;this._tc_id = tinyCounter++;}
tinycolor.prototype = {isDark: function() {return this.getBrightness() < 128;},
    isLight: function() {return !this.isDark();},
    isValid: function() {return this._ok;},
    getOriginalInput: function() {return this._originalInput;},
    getFormat: function() {return this._format;},
    getAlpha: function() {return this._a;},
    getBrightness: function() {var rgb = this.toRgb();return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;},
    getLuminance: function() {var rgb = this.toRgb();var RsRGB, GsRGB, BsRGB, R, G, B;RsRGB = rgb.r/255;GsRGB = rgb.g/255;BsRGB = rgb.b/255;if (RsRGB <= 0.03928) {R = RsRGB / 12.92;} else {R = Math.pow(((RsRGB + 0.055) / 1.055), 2.4);}
        if (GsRGB <= 0.03928) {G = GsRGB / 12.92;} else {G = Math.pow(((GsRGB + 0.055) / 1.055), 2.4);}
        if (BsRGB <= 0.03928) {B = BsRGB / 12.92;} else {B = Math.pow(((BsRGB + 0.055) / 1.055), 2.4);}
        return (0.2126 * R) + (0.7152 * G) + (0.0722 * B);},
    setAlpha: function(value) {this._a = boundAlpha(value);this._roundA = mathRound(100*this._a) / 100;return this;},
    toHsv: function() {var hsv = rgbToHsv(this._r, this._g, this._b);return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this._a };},
    toHsvString: function() {var hsv = rgbToHsv(this._r, this._g, this._b);var h = mathRound(hsv.h * 360), s = mathRound(hsv.s * 100), v = mathRound(hsv.v * 100);return (this._a == 1) ?
          "hsv("  + h + ", " + s + "%, " + v + "%)" :
          "hsva(" + h + ", " + s + "%, " + v + "%, "+ this._roundA + ")";},
    toHsl: function() {var hsl = rgbToHsl(this._r, this._g, this._b);return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this._a };},
    toHslString: function() {var hsl = rgbToHsl(this._r, this._g, this._b);var h = mathRound(hsl.h * 360), s = mathRound(hsl.s * 100), l = mathRound(hsl.l * 100);return (this._a == 1) ?
          "hsl("  + h + ", " + s + "%, " + l + "%)" :
          "hsla(" + h + ", " + s + "%, " + l + "%, "+ this._roundA + ")";},
    toHex: function(allow3Char) {return rgbToHex(this._r, this._g, this._b, allow3Char);},
    toHexString: function(allow3Char) {return '#' + this.toHex(allow3Char);},
    toHex8: function(allow4Char) {return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);},
    toHex8String: function(allow4Char) {return '#' + this.toHex8(allow4Char);},
    toRgb: function() {return { r: mathRound(this._r), g: mathRound(this._g), b: mathRound(this._b), a: this._a };},
    toRgbString: function() {return (this._a == 1) ?
          "rgb("  + mathRound(this._r) + ", " + mathRound(this._g) + ", " + mathRound(this._b) + ")" :
          "rgba(" + mathRound(this._r) + ", " + mathRound(this._g) + ", " + mathRound(this._b) + ", " + this._roundA + ")";},
    toPercentageRgb: function() {return { r: mathRound(bound01(this._r, 255) * 100) + "%", g: mathRound(bound01(this._g, 255) * 100) + "%", b: mathRound(bound01(this._b, 255) * 100) + "%", a: this._a };},
    toPercentageRgbString: function() {return (this._a == 1) ?
          "rgb("  + mathRound(bound01(this._r, 255) * 100) + "%, " + mathRound(bound01(this._g, 255) * 100) + "%, " + mathRound(bound01(this._b, 255) * 100) + "%)" :
          "rgba(" + mathRound(bound01(this._r, 255) * 100) + "%, " + mathRound(bound01(this._g, 255) * 100) + "%, " + mathRound(bound01(this._b, 255) * 100) + "%, " + this._roundA + ")";},
    toName: function() {if (this._a === 0) {return "transparent";}
        if (this._a < 1) {return false;}
        return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;},
    toFilter: function(secondColor) {var hex8String = '#' + rgbaToArgbHex(this._r, this._g, this._b, this._a);var secondHex8String = hex8String;var gradientType = this._gradientType ? "GradientType = 1, " : "";if (secondColor) {var s = tinycolor(secondColor);secondHex8String = '#' + rgbaToArgbHex(s._r, s._g, s._b, s._a);}
        return "progid:DXImageTransform.Microsoft.gradient("+gradientType+"startColorstr="+hex8String+",endColorstr="+secondHex8String+")";},
    toString: function(format) {var formatSet = !!format;format = format || this._format;var formattedString = false;var hasAlpha = this._a < 1 && this._a >= 0;var needsAlphaFormat = !formatSet && hasAlpha && (format === "hex" || format === "hex6" || format === "hex3" || format === "hex4" || format === "hex8" || format === "name");if (needsAlphaFormat) {if (format === "name" && this._a === 0) {return this.toName();}
            return this.toRgbString();}
        if (format === "rgb") {formattedString = this.toRgbString();}
        if (format === "prgb") {formattedString = this.toPercentageRgbString();}
        if (format === "hex" || format === "hex6") {formattedString = this.toHexString();}
        if (format === "hex3") {formattedString = this.toHexString(true);}
        if (format === "hex4") {formattedString = this.toHex8String(true);}
        if (format === "hex8") {formattedString = this.toHex8String();}
        if (format === "name") {formattedString = this.toName();}
        if (format === "hsl") {formattedString = this.toHslString();}
        if (format === "hsv") {formattedString = this.toHsvString();}
        return formattedString || this.toHexString();},
    clone: function() {return tinycolor(this.toString());},
    _applyModification: function(fn, args) {var color = fn.apply(null, [this].concat([].slice.call(args)));this._r = color._r;this._g = color._g;this._b = color._b;this.setAlpha(color._a);return this;},
    lighten: function() {return this._applyModification(lighten, arguments);},
    brighten: function() {return this._applyModification(brighten, arguments);},
    darken: function() {return this._applyModification(darken, arguments);},
    desaturate: function() {return this._applyModification(desaturate, arguments);},
    saturate: function() {return this._applyModification(saturate, arguments);},
    greyscale: function() {return this._applyModification(greyscale, arguments);},
    spin: function() {return this._applyModification(spin, arguments);},
    _applyCombination: function(fn, args) {return fn.apply(null, [this].concat([].slice.call(args)));},
    analogous: function() {return this._applyCombination(analogous, arguments);},
    complement: function() {return this._applyCombination(complement, arguments);},
    monochromatic: function() {return this._applyCombination(monochromatic, arguments);},
    splitcomplement: function() {return this._applyCombination(splitcomplement, arguments);},
    triad: function() {return this._applyCombination(triad, arguments);},
    tetrad: function() {return this._applyCombination(tetrad, arguments);}
};
tinycolor.fromRatio = function(color, opts) {if (typeof color == "object") {var newColor = {};for (var i in color) {if (color.hasOwnProperty(i)) {if (i === "a") {newColor[i] = color[i];}
                else {newColor[i] = convertToPercentage(color[i]);}
            }
        }
        color = newColor;}
    return tinycolor(color, opts);};
function inputToRGB(color) {var rgb = { r: 0, g: 0, b: 0 };var a = 1;var s = null;var v = null;var l = null;var ok = false;var format = false;if (typeof color == "string") {color = stringInputToObject(color);}
    if (typeof color == "object") {if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {rgb = rgbToRgb(color.r, color.g, color.b);ok = true;format = String(color.r).substr(-1) === "%" ? "prgb" : "rgb";}
        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {s = convertToPercentage(color.s);v = convertToPercentage(color.v);rgb = hsvToRgb(color.h, s, v);ok = true;format = "hsv";}
        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {s = convertToPercentage(color.s);l = convertToPercentage(color.l);rgb = hslToRgb(color.h, s, l);ok = true;format = "hsl";}
        if (color.hasOwnProperty("a")) {a = color.a;}
    }
    a = boundAlpha(a);return {ok: ok,
        format: color.format || format,
        r: mathMin(255, mathMax(rgb.r, 0)),
        g: mathMin(255, mathMax(rgb.g, 0)),
        b: mathMin(255, mathMax(rgb.b, 0)),
        a: a
    };}
function rgbToRgb(r, g, b){return {r: bound01(r, 255) * 255,
        g: bound01(g, 255) * 255,
        b: bound01(b, 255) * 255
    };}
function rgbToHsl(r, g, b) {r = bound01(r, 255);g = bound01(g, 255);b = bound01(b, 255);var max = mathMax(r, g, b), min = mathMin(r, g, b);var h, s, l = (max + min) / 2;if(max == min) {h = s = 0; 
    }
    else {var d = max - min;s = l > 0.5 ? d / (2 - max - min) : d / (max + min);switch(max) {case r: h = (g - b) / d + (g < b ? 6 : 0); break;case g: h = (b - r) / d + 2; break;case b: h = (r - g) / d + 4; break;}
        h /= 6;}
    return { h: h, s: s, l: l };}
function hslToRgb(h, s, l) {var r, g, b;h = bound01(h, 360);s = bound01(s, 100);l = bound01(l, 100);function hue2rgb(p, q, t) {if(t < 0) t += 1;if(t > 1) t -= 1;if(t < 1/6) return p + (q - p) * 6 * t;if(t < 1/2) return q;if(t < 2/3) return p + (q - p) * (2/3 - t) * 6;return p;}
    if(s === 0) {r = g = b = l; 
    }
    else {var q = l < 0.5 ? l * (1 + s) : l + s - l * s;var p = 2 * l - q;r = hue2rgb(p, q, h + 1/3);g = hue2rgb(p, q, h);b = hue2rgb(p, q, h - 1/3);}
    return { r: r * 255, g: g * 255, b: b * 255 };}
function rgbToHsv(r, g, b) {r = bound01(r, 255);g = bound01(g, 255);b = bound01(b, 255);var max = mathMax(r, g, b), min = mathMin(r, g, b);var h, s, v = max;var d = max - min;s = max === 0 ? 0 : d / max;if(max == min) {h = 0; 
    }
    else {switch(max) {case r: h = (g - b) / d + (g < b ? 6 : 0); break;case g: h = (b - r) / d + 2; break;case b: h = (r - g) / d + 4; break;}
        h /= 6;}
    return { h: h, s: s, v: v };}
 function hsvToRgb(h, s, v) {h = bound01(h, 360) * 6;s = bound01(s, 100);v = bound01(v, 100);var i = Math.floor(h),
        f = h - i,
        p = v * (1 - s),
        q = v * (1 - f * s),
        t = v * (1 - (1 - f) * s),
        mod = i % 6,
        r = [v, q, p, p, t, v][mod],
        g = [t, v, v, q, p, p][mod],
        b = [p, p, t, v, v, q][mod];return { r: r * 255, g: g * 255, b: b * 255 };}
function rgbToHex(r, g, b, allow3Char) {var hex = [
        pad2(mathRound(r).toString(16)),
        pad2(mathRound(g).toString(16)),
        pad2(mathRound(b).toString(16))
    ];if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);}
    return hex.join("");}
function rgbaToHex(r, g, b, a, allow4Char) {var hex = [
        pad2(mathRound(r).toString(16)),
        pad2(mathRound(g).toString(16)),
        pad2(mathRound(b).toString(16)),
        pad2(convertDecimalToHex(a))
    ];if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);}
    return hex.join("");}
function rgbaToArgbHex(r, g, b, a) {var hex = [
        pad2(convertDecimalToHex(a)),
        pad2(mathRound(r).toString(16)),
        pad2(mathRound(g).toString(16)),
        pad2(mathRound(b).toString(16))
    ];return hex.join("");}
tinycolor.equals = function (color1, color2) {if (!color1 || !color2) { return false; }
    return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();};
tinycolor.random = function() {return tinycolor.fromRatio({r: mathRandom(),
        g: mathRandom(),
        b: mathRandom()
    });};
function desaturate(color, amount) {amount = (amount === 0) ? 0 : (amount || 10);var hsl = tinycolor(color).toHsl();hsl.s -= amount / 100;hsl.s = clamp01(hsl.s);return tinycolor(hsl);}
function saturate(color, amount) {amount = (amount === 0) ? 0 : (amount || 10);var hsl = tinycolor(color).toHsl();hsl.s += amount / 100;hsl.s = clamp01(hsl.s);return tinycolor(hsl);}
function greyscale(color) {return tinycolor(color).desaturate(100);}
function lighten (color, amount) {amount = (amount === 0) ? 0 : (amount || 10);var hsl = tinycolor(color).toHsl();hsl.l += amount / 100;hsl.l = clamp01(hsl.l);return tinycolor(hsl);}
function brighten(color, amount) {amount = (amount === 0) ? 0 : (amount || 10);var rgb = tinycolor(color).toRgb();rgb.r = mathMax(0, mathMin(255, rgb.r - mathRound(255 * - (amount / 100))));rgb.g = mathMax(0, mathMin(255, rgb.g - mathRound(255 * - (amount / 100))));rgb.b = mathMax(0, mathMin(255, rgb.b - mathRound(255 * - (amount / 100))));return tinycolor(rgb);}
function darken (color, amount) {amount = (amount === 0) ? 0 : (amount || 10);var hsl = tinycolor(color).toHsl();hsl.l -= amount / 100;hsl.l = clamp01(hsl.l);return tinycolor(hsl);}
function spin(color, amount) {var hsl = tinycolor(color).toHsl();var hue = (hsl.h + amount) % 360;hsl.h = hue < 0 ? 360 + hue : hue;return tinycolor(hsl);}
function complement(color) {var hsl = tinycolor(color).toHsl();hsl.h = (hsl.h + 180) % 360;return tinycolor(hsl);}
function triad(color) {var hsl = tinycolor(color).toHsl();var h = hsl.h;return [
        tinycolor(color),
        tinycolor({ h: (h + 120) % 360, s: hsl.s, l: hsl.l }),
        tinycolor({ h: (h + 240) % 360, s: hsl.s, l: hsl.l })
    ];}
function tetrad(color) {var hsl = tinycolor(color).toHsl();var h = hsl.h;return [
        tinycolor(color),
        tinycolor({ h: (h + 90) % 360, s: hsl.s, l: hsl.l }),
        tinycolor({ h: (h + 180) % 360, s: hsl.s, l: hsl.l }),
        tinycolor({ h: (h + 270) % 360, s: hsl.s, l: hsl.l })
    ];}
function splitcomplement(color) {var hsl = tinycolor(color).toHsl();var h = hsl.h;return [
        tinycolor(color),
        tinycolor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l}),
        tinycolor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l})
    ];}
function analogous(color, results, slices) {results = results || 6;slices = slices || 30;var hsl = tinycolor(color).toHsl();var part = 360 / slices;var ret = [tinycolor(color)];for (hsl.h = ((hsl.h - (part * results >> 1)) + 720) % 360; --results; ) {hsl.h = (hsl.h + part) % 360;ret.push(tinycolor(hsl));}
    return ret;}
function monochromatic(color, results) {results = results || 6;var hsv = tinycolor(color).toHsv();var h = hsv.h, s = hsv.s, v = hsv.v;var ret = [];var modification = 1 / results;while (results--) {ret.push(tinycolor({ h: h, s: s, v: v}));v = (v + modification) % 1;}
    return ret;}
tinycolor.mix = function(color1, color2, amount) {amount = (amount === 0) ? 0 : (amount || 50);var rgb1 = tinycolor(color1).toRgb();var rgb2 = tinycolor(color2).toRgb();var p = amount / 100;var rgba = {r: ((rgb2.r - rgb1.r) * p) + rgb1.r,
        g: ((rgb2.g - rgb1.g) * p) + rgb1.g,
        b: ((rgb2.b - rgb1.b) * p) + rgb1.b,
        a: ((rgb2.a - rgb1.a) * p) + rgb1.a
    };return tinycolor(rgba);};
tinycolor.readability = function(color1, color2) {var c1 = tinycolor(color1);var c2 = tinycolor(color2);return (Math.max(c1.getLuminance(),c2.getLuminance())+0.05) / (Math.min(c1.getLuminance(),c2.getLuminance())+0.05);};
tinycolor.isReadable = function(color1, color2, wcag2) {var readability = tinycolor.readability(color1, color2);var wcag2Parms, out;out = false;wcag2Parms = validateWCAG2Parms(wcag2);switch (wcag2Parms.level + wcag2Parms.size) {case "AAsmall":
        case "AAAlarge":
            out = readability >= 4.5;break;case "AAlarge":
            out = readability >= 3;break;case "AAAsmall":
            out = readability >= 7;break;}
    return out;};
tinycolor.mostReadable = function(baseColor, colorList, args) {var bestColor = null;var bestScore = 0;var readability;var includeFallbackColors, level, size ;args = args || {};includeFallbackColors = args.includeFallbackColors ;level = args.level;size = args.size;for (var i= 0; i < colorList.length ; i++) {readability = tinycolor.readability(baseColor, colorList[i]);if (readability > bestScore) {bestScore = readability;bestColor = tinycolor(colorList[i]);}
    }
    if (tinycolor.isReadable(baseColor, bestColor, {"level":level,"size":size}) || !includeFallbackColors) {return bestColor;}
    else {args.includeFallbackColors=false;return tinycolor.mostReadable(baseColor,["#fff", "#000"],args);}
};
var names = tinycolor.names = {aliceblue: "f0f8ff",
    antiquewhite: "faebd7",
    aqua: "0ff",
    aquamarine: "7fffd4",
    azure: "f0ffff",
    beige: "f5f5dc",
    bisque: "ffe4c4",
    black: "000",
    blanchedalmond: "ffebcd",
    blue: "00f",
    blueviolet: "8a2be2",
    brown: "a52a2a",
    burlywood: "deb887",
    burntsienna: "ea7e5d",
    cadetblue: "5f9ea0",
    chartreuse: "7fff00",
    chocolate: "d2691e",
    coral: "ff7f50",
    cornflowerblue: "6495ed",
    cornsilk: "fff8dc",
    crimson: "dc143c",
    cyan: "0ff",
    darkblue: "00008b",
    darkcyan: "008b8b",
    darkgoldenrod: "b8860b",
    darkgray: "a9a9a9",
    darkgreen: "006400",
    darkgrey: "a9a9a9",
    darkkhaki: "bdb76b",
    darkmagenta: "8b008b",
    darkolivegreen: "556b2f",
    darkorange: "ff8c00",
    darkorchid: "9932cc",
    darkred: "8b0000",
    darksalmon: "e9967a",
    darkseagreen: "8fbc8f",
    darkslateblue: "483d8b",
    darkslategray: "2f4f4f",
    darkslategrey: "2f4f4f",
    darkturquoise: "00ced1",
    darkviolet: "9400d3",
    deeppink: "ff1493",
    deepskyblue: "00bfff",
    dimgray: "696969",
    dimgrey: "696969",
    dodgerblue: "1e90ff",
    firebrick: "b22222",
    floralwhite: "fffaf0",
    forestgreen: "228b22",
    fuchsia: "f0f",
    gainsboro: "dcdcdc",
    ghostwhite: "f8f8ff",
    gold: "ffd700",
    goldenrod: "daa520",
    gray: "808080",
    green: "008000",
    greenyellow: "adff2f",
    grey: "808080",
    honeydew: "f0fff0",
    hotpink: "ff69b4",
    indianred: "cd5c5c",
    indigo: "4b0082",
    ivory: "fffff0",
    khaki: "f0e68c",
    lavender: "e6e6fa",
    lavenderblush: "fff0f5",
    lawngreen: "7cfc00",
    lemonchiffon: "fffacd",
    lightblue: "add8e6",
    lightcoral: "f08080",
    lightcyan: "e0ffff",
    lightgoldenrodyellow: "fafad2",
    lightgray: "d3d3d3",
    lightgreen: "90ee90",
    lightgrey: "d3d3d3",
    lightpink: "ffb6c1",
    lightsalmon: "ffa07a",
    lightseagreen: "20b2aa",
    lightskyblue: "87cefa",
    lightslategray: "789",
    lightslategrey: "789",
    lightsteelblue: "b0c4de",
    lightyellow: "ffffe0",
    lime: "0f0",
    limegreen: "32cd32",
    linen: "faf0e6",
    magenta: "f0f",
    maroon: "800000",
    mediumaquamarine: "66cdaa",
    mediumblue: "0000cd",
    mediumorchid: "ba55d3",
    mediumpurple: "9370db",
    mediumseagreen: "3cb371",
    mediumslateblue: "7b68ee",
    mediumspringgreen: "00fa9a",
    mediumturquoise: "48d1cc",
    mediumvioletred: "c71585",
    midnightblue: "191970",
    mintcream: "f5fffa",
    mistyrose: "ffe4e1",
    moccasin: "ffe4b5",
    navajowhite: "ffdead",
    navy: "000080",
    oldlace: "fdf5e6",
    olive: "808000",
    olivedrab: "6b8e23",
    orange: "ffa500",
    orangered: "ff4500",
    orchid: "da70d6",
    palegoldenrod: "eee8aa",
    palegreen: "98fb98",
    paleturquoise: "afeeee",
    palevioletred: "db7093",
    papayawhip: "ffefd5",
    peachpuff: "ffdab9",
    peru: "cd853f",
    pink: "ffc0cb",
    plum: "dda0dd",
    powderblue: "b0e0e6",
    purple: "800080",
    rebeccapurple: "663399",
    red: "f00",
    rosybrown: "bc8f8f",
    royalblue: "4169e1",
    saddlebrown: "8b4513",
    salmon: "fa8072",
    sandybrown: "f4a460",
    seagreen: "2e8b57",
    seashell: "fff5ee",
    sienna: "a0522d",
    silver: "c0c0c0",
    skyblue: "87ceeb",
    slateblue: "6a5acd",
    slategray: "708090",
    slategrey: "708090",
    snow: "fffafa",
    springgreen: "00ff7f",
    steelblue: "4682b4",
    tan: "d2b48c",
    teal: "008080",
    thistle: "d8bfd8",
    tomato: "ff6347",
    turquoise: "40e0d0",
    violet: "ee82ee",
    wheat: "f5deb3",
    white: "fff",
    whitesmoke: "f5f5f5",
    yellow: "ff0",
    yellowgreen: "9acd32"
};
var hexNames = tinycolor.hexNames = flip(names);
function flip(o) {var flipped = { };for (var i in o) {if (o.hasOwnProperty(i)) {flipped[o[i]] = i;}
    }
    return flipped;}
function boundAlpha(a) {a = parseFloat(a);if (isNaN(a) || a < 0 || a > 1) {a = 1;}
    return a;}
function bound01(n, max) {if (isOnePointZero(n)) { n = "100%"; }
    var processPercent = isPercentage(n);n = mathMin(max, mathMax(0, parseFloat(n)));if (processPercent) {n = parseInt(n * max, 10) / 100;}
    if ((Math.abs(n - max) < 0.000001)) {return 1;}
    return (n % max) / parseFloat(max);}
function clamp01(val) {return mathMin(1, mathMax(0, val));}
function parseIntFromHex(val) {return parseInt(val, 16);}
function isOnePointZero(n) {return typeof n == "string" && n.indexOf('.') != -1 && parseFloat(n) === 1;}
function isPercentage(n) {return typeof n === "string" && n.indexOf('%') != -1;}
function pad2(c) {return c.length == 1 ? '0' + c : '' + c;}
function convertToPercentage(n) {if (n <= 1) {n = (n * 100) + "%";}
    return n;}
function convertDecimalToHex(d) {return Math.round(parseFloat(d) * 255).toString(16);}
function convertHexToDecimal(h) {return (parseIntFromHex(h) / 255);}
var matchers = (function() {var CSS_INTEGER = "[-\\+]?\\d+%?";var CSS_NUMBER = "[-\\+]?\\d*\\.\\d+%?";var CSS_UNIT = "(?:" + CSS_NUMBER + ")|(?:" + CSS_INTEGER + ")";var PERMISSIVE_MATCH3 = "[\\s|\\(]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")\\s*\\)?";var PERMISSIVE_MATCH4 = "[\\s|\\(]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")\\s*\\)?";return {CSS_UNIT: new RegExp(CSS_UNIT),
        rgb: new RegExp("rgb" + PERMISSIVE_MATCH3),
        rgba: new RegExp("rgba" + PERMISSIVE_MATCH4),
        hsl: new RegExp("hsl" + PERMISSIVE_MATCH3),
        hsla: new RegExp("hsla" + PERMISSIVE_MATCH4),
        hsv: new RegExp("hsv" + PERMISSIVE_MATCH3),
        hsva: new RegExp("hsva" + PERMISSIVE_MATCH4),
        hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
        hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,
        hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
        hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/
    };})();
function isValidCSSUnit(color) {return !!matchers.CSS_UNIT.exec(color);}
function stringInputToObject(color) {color = color.replace(trimLeft, '').replace(trimRight, '').toLowerCase();var named = false;if (names[color]) {color = names[color];named = true;}
    else if (color == 'transparent') {return { r: 0, g: 0, b: 0, a: 0, format: "name" };}
    var match;if ((match = matchers.rgb.exec(color))) {return { r: match[1], g: match[2], b: match[3] };}
    if ((match = matchers.rgba.exec(color))) {return { r: match[1], g: match[2], b: match[3], a: match[4] };}
    if ((match = matchers.hsl.exec(color))) {return { h: match[1], s: match[2], l: match[3] };}
    if ((match = matchers.hsla.exec(color))) {return { h: match[1], s: match[2], l: match[3], a: match[4] };}
    if ((match = matchers.hsv.exec(color))) {return { h: match[1], s: match[2], v: match[3] };}
    if ((match = matchers.hsva.exec(color))) {return { h: match[1], s: match[2], v: match[3], a: match[4] };}
    if ((match = matchers.hex8.exec(color))) {return {r: parseIntFromHex(match[1]),
            g: parseIntFromHex(match[2]),
            b: parseIntFromHex(match[3]),
            a: convertHexToDecimal(match[4]),
            format: named ? "name" : "hex8"
        };}
    if ((match = matchers.hex6.exec(color))) {return {r: parseIntFromHex(match[1]),
            g: parseIntFromHex(match[2]),
            b: parseIntFromHex(match[3]),
            format: named ? "name" : "hex"
        };}
    if ((match = matchers.hex4.exec(color))) {return {r: parseIntFromHex(match[1] + '' + match[1]),
            g: parseIntFromHex(match[2] + '' + match[2]),
            b: parseIntFromHex(match[3] + '' + match[3]),
            a: convertHexToDecimal(match[4] + '' + match[4]),
            format: named ? "name" : "hex8"
        };}
    if ((match = matchers.hex3.exec(color))) {return {r: parseIntFromHex(match[1] + '' + match[1]),
            g: parseIntFromHex(match[2] + '' + match[2]),
            b: parseIntFromHex(match[3] + '' + match[3]),
            format: named ? "name" : "hex"
        };}
    return false;}
function validateWCAG2Parms(parms) {var level, size;parms = parms || {"level":"AA", "size":"small"};level = (parms.level || "AA").toUpperCase();size = (parms.size || "small").toLowerCase();if (level !== "AA" && level !== "AAA") {level = "AA";}
    if (size !== "small" && size !== "large") {size = "small";}
    return {"level":level, "size":size};}
this.tinycolor = tinycolor;})()`;}
.tinyColorMixin();
.colorPaletteMixin() {
@functions: ~`(function() {var hueStep = 2;var saturationStep = 0.16;var saturationStep2 = 0.05;var brightnessStep1 = 0.05;var brightnessStep2 = 0.15;var lightColorCount = 5;var darkColorCount = 4;var getHue = function(hsv, i, isLight) {var hue;if (hsv.h >= 60 && hsv.h <= 240) {hue = isLight ? hsv.h - hueStep * i : hsv.h + hueStep * i;} else {hue = isLight ? hsv.h + hueStep * i : hsv.h - hueStep * i;}
    if (hue < 0) {hue += 360;} else if (hue >= 360) {hue -= 360;}
    return Math.round(hue);};var getSaturation = function(hsv, i, isLight) {var saturation;if (isLight) {saturation = hsv.s - saturationStep * i;} else if (i === darkColorCount) {saturation = hsv.s + saturationStep;} else {saturation = hsv.s + saturationStep2 * i;}
    if (saturation > 1) {saturation = 1;}
    if (isLight && i === lightColorCount && saturation > 0.1) {saturation = 0.1;}
    if (saturation < 0.06) {saturation = 0.06;}
    return Number(saturation.toFixed(2));};var getValue = function(hsv, i, isLight) {var value;if (isLight) {value = hsv.v + brightnessStep1 * i;}else{value = hsv.v - brightnessStep2 * i
    }
    if (value > 1) {value = 1;}
    return Number(value.toFixed(2))
  };this.colorPalette = function(color, index) {var isLight = index <= 6;var hsv = tinycolor(color).toHsv();var i = isLight ? lightColorCount + 1 - index : index - lightColorCount - 1;return tinycolor({h: getHue(hsv, i, isLight),
      s: getSaturation(hsv, i, isLight),
      v: getValue(hsv, i, isLight),
    }).toHexString();};})()`;}
.colorPaletteMixin();
@blue-base: #1890ff;
@blue-1: color(~`colorPalette('@{blue-6}', 1) `);
@blue-2: color(~`colorPalette('@{blue-6}', 2) `);
@blue-3: color(~`colorPalette('@{blue-6}', 3) `);
@blue-4: color(~`colorPalette('@{blue-6}', 4) `);
@blue-5: color(~`colorPalette('@{blue-6}', 5) `);
@blue-6: @blue-base;
@blue-7: color(~`colorPalette('@{blue-6}', 7) `);
@blue-8: color(~`colorPalette('@{blue-6}', 8) `);
@blue-9: color(~`colorPalette('@{blue-6}', 9) `);
@blue-10: color(~`colorPalette('@{blue-6}', 10) `);
@purple-base: #722ed1;
@purple-1: color(~`colorPalette('@{purple-6}', 1) `);
@purple-2: color(~`colorPalette('@{purple-6}', 2) `);
@purple-3: color(~`colorPalette('@{purple-6}', 3) `);
@purple-4: color(~`colorPalette('@{purple-6}', 4) `);
@purple-5: color(~`colorPalette('@{purple-6}', 5) `);
@purple-6: @purple-base;
@purple-7: color(~`colorPalette('@{purple-6}', 7) `);
@purple-8: color(~`colorPalette('@{purple-6}', 8) `);
@purple-9: color(~`colorPalette('@{purple-6}', 9) `);
@purple-10: color(~`colorPalette('@{purple-6}', 10) `);
@cyan-base: #13c2c2;
@cyan-1: color(~`colorPalette('@{cyan-6}', 1) `);
@cyan-2: color(~`colorPalette('@{cyan-6}', 2) `);
@cyan-3: color(~`colorPalette('@{cyan-6}', 3) `);
@cyan-4: color(~`colorPalette('@{cyan-6}', 4) `);
@cyan-5: color(~`colorPalette('@{cyan-6}', 5) `);
@cyan-6: @cyan-base;
@cyan-7: color(~`colorPalette('@{cyan-6}', 7) `);
@cyan-8: color(~`colorPalette('@{cyan-6}', 8) `);
@cyan-9: color(~`colorPalette('@{cyan-6}', 9) `);
@cyan-10: color(~`colorPalette('@{cyan-6}', 10) `);
@green-base: #52c41a;
@green-1: color(~`colorPalette('@{green-6}', 1) `);
@green-2: color(~`colorPalette('@{green-6}', 2) `);
@green-3: color(~`colorPalette('@{green-6}', 3) `);
@green-4: color(~`colorPalette('@{green-6}', 4) `);
@green-5: color(~`colorPalette('@{green-6}', 5) `);
@green-6: @green-base;
@green-7: color(~`colorPalette('@{green-6}', 7) `);
@green-8: color(~`colorPalette('@{green-6}', 8) `);
@green-9: color(~`colorPalette('@{green-6}', 9) `);
@green-10: color(~`colorPalette('@{green-6}', 10) `);
@magenta-base: #eb2f96;
@magenta-1: color(~`colorPalette('@{magenta-6}', 1) `);
@magenta-2: color(~`colorPalette('@{magenta-6}', 2) `);
@magenta-3: color(~`colorPalette('@{magenta-6}', 3) `);
@magenta-4: color(~`colorPalette('@{magenta-6}', 4) `);
@magenta-5: color(~`colorPalette('@{magenta-6}', 5) `);
@magenta-6: @magenta-base;
@magenta-7: color(~`colorPalette('@{magenta-6}', 7) `);
@magenta-8: color(~`colorPalette('@{magenta-6}', 8) `);
@magenta-9: color(~`colorPalette('@{magenta-6}', 9) `);
@magenta-10: color(~`colorPalette('@{magenta-6}', 10) `);
@pink-base: #eb2f96;
@pink-1: color(~`colorPalette('@{pink-6}', 1) `);
@pink-2: color(~`colorPalette('@{pink-6}', 2) `);
@pink-3: color(~`colorPalette('@{pink-6}', 3) `);
@pink-4: color(~`colorPalette('@{pink-6}', 4) `);
@pink-5: color(~`colorPalette('@{pink-6}', 5) `);
@pink-6: @pink-base;
@pink-7: color(~`colorPalette('@{pink-6}', 7) `);
@pink-8: color(~`colorPalette('@{pink-6}', 8) `);
@pink-9: color(~`colorPalette('@{pink-6}', 9) `);
@pink-10: color(~`colorPalette('@{pink-6}', 10) `);
@red-base: #f5222d;
@red-1: color(~`colorPalette('@{red-6}', 1) `);
@red-2: color(~`colorPalette('@{red-6}', 2) `);
@red-3: color(~`colorPalette('@{red-6}', 3) `);
@red-4: color(~`colorPalette('@{red-6}', 4) `);
@red-5: color(~`colorPalette('@{red-6}', 5) `);
@red-6: @red-base;
@red-7: color(~`colorPalette('@{red-6}', 7) `);
@red-8: color(~`colorPalette('@{red-6}', 8) `);
@red-9: color(~`colorPalette('@{red-6}', 9) `);
@red-10: color(~`colorPalette('@{red-6}', 10) `);
@orange-base: #fa8c16;
@orange-1: color(~`colorPalette('@{orange-6}', 1) `);
@orange-2: color(~`colorPalette('@{orange-6}', 2) `);
@orange-3: color(~`colorPalette('@{orange-6}', 3) `);
@orange-4: color(~`colorPalette('@{orange-6}', 4) `);
@orange-5: color(~`colorPalette('@{orange-6}', 5) `);
@orange-6: @orange-base;
@orange-7: color(~`colorPalette('@{orange-6}', 7) `);
@orange-8: color(~`colorPalette('@{orange-6}', 8) `);
@orange-9: color(~`colorPalette('@{orange-6}', 9) `);
@orange-10: color(~`colorPalette('@{orange-6}', 10) `);
@yellow-base: #fadb14;
@yellow-1: color(~`colorPalette('@{yellow-6}', 1) `);
@yellow-2: color(~`colorPalette('@{yellow-6}', 2) `);
@yellow-3: color(~`colorPalette('@{yellow-6}', 3) `);
@yellow-4: color(~`colorPalette('@{yellow-6}', 4) `);
@yellow-5: color(~`colorPalette('@{yellow-6}', 5) `);
@yellow-6: @yellow-base;
@yellow-7: color(~`colorPalette('@{yellow-6}', 7) `);
@yellow-8: color(~`colorPalette('@{yellow-6}', 8) `);
@yellow-9: color(~`colorPalette('@{yellow-6}', 9) `);
@yellow-10: color(~`colorPalette('@{yellow-6}', 10) `);
@volcano-base: #fa541c;
@volcano-1: color(~`colorPalette('@{volcano-6}', 1) `);
@volcano-2: color(~`colorPalette('@{volcano-6}', 2) `);
@volcano-3: color(~`colorPalette('@{volcano-6}', 3) `);
@volcano-4: color(~`colorPalette('@{volcano-6}', 4) `);
@volcano-5: color(~`colorPalette('@{volcano-6}', 5) `);
@volcano-6: @volcano-base;
@volcano-7: color(~`colorPalette('@{volcano-6}', 7) `);
@volcano-8: color(~`colorPalette('@{volcano-6}', 8) `);
@volcano-9: color(~`colorPalette('@{volcano-6}', 9) `);
@volcano-10: color(~`colorPalette('@{volcano-6}', 10) `);
@geekblue-base: #2f54eb;
@geekblue-1: color(~`colorPalette('@{geekblue-6}', 1) `);
@geekblue-2: color(~`colorPalette('@{geekblue-6}', 2) `);
@geekblue-3: color(~`colorPalette('@{geekblue-6}', 3) `);
@geekblue-4: color(~`colorPalette('@{geekblue-6}', 4) `);
@geekblue-5: color(~`colorPalette('@{geekblue-6}', 5) `);
@geekblue-6: @geekblue-base;
@geekblue-7: color(~`colorPalette('@{geekblue-6}', 7) `);
@geekblue-8: color(~`colorPalette('@{geekblue-6}', 8) `);
@geekblue-9: color(~`colorPalette('@{geekblue-6}', 9) `);
@geekblue-10: color(~`colorPalette('@{geekblue-6}', 10) `);
@lime-base: #a0d911;
@lime-1: color(~`colorPalette('@{lime-6}', 1) `);
@lime-2: color(~`colorPalette('@{lime-6}', 2) `);
@lime-3: color(~`colorPalette('@{lime-6}', 3) `);
@lime-4: color(~`colorPalette('@{lime-6}', 4) `);
@lime-5: color(~`colorPalette('@{lime-6}', 5) `);
@lime-6: @lime-base;
@lime-7: color(~`colorPalette('@{lime-6}', 7) `);
@lime-8: color(~`colorPalette('@{lime-6}', 8) `);
@lime-9: color(~`colorPalette('@{lime-6}', 9) `);
@lime-10: color(~`colorPalette('@{lime-6}', 10) `);
@gold-base: #faad14;
@gold-1: color(~`colorPalette('@{gold-6}', 1) `);
@gold-2: color(~`colorPalette('@{gold-6}', 2) `);
@gold-3: color(~`colorPalette('@{gold-6}', 3) `);
@gold-4: color(~`colorPalette('@{gold-6}', 4) `);
@gold-5: color(~`colorPalette('@{gold-6}', 5) `);
@gold-6: @gold-base;
@gold-7: color(~`colorPalette('@{gold-6}', 7) `);
@gold-8: color(~`colorPalette('@{gold-6}', 8) `);
@gold-9: color(~`colorPalette('@{gold-6}', 9) `);
@gold-10: color(~`colorPalette('@{gold-6}', 10) `);
@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,
  purple;
@theme: default;
@ant-prefix: ant;
@html-selector: html;
@primary-color-hover: color(~`colorPalette('@{primary-color}', 5) `);
@primary-color-active: color(~`colorPalette('@{primary-color}', 7) `);
@primary-color-outline: fade(@primary-color, @outline-fade);
@processing-color: @blue-6;
@info-color: @primary-color;
@info-color-deprecated-bg: color(~`colorPalette('@{info-color}', 1) `);
@info-color-deprecated-border: color(~`colorPalette('@{info-color}', 3) `);
@success-color-hover: color(~`colorPalette('@{success-color}', 5) `);
@success-color-active: color(~`colorPalette('@{success-color}', 7) `);
@success-color-outline: fade(@success-color, @outline-fade);
@success-color-deprecated-bg: color(~`colorPalette('@{success-color}', 1) `);
@success-color-deprecated-border: color(~`colorPalette('@{success-color}', 3) `);
@warning-color-hover: color(~`colorPalette('@{warning-color}', 5) `);
@warning-color-active: color(~`colorPalette('@{warning-color}', 7) `);
@warning-color-outline: fade(@warning-color, @outline-fade);
@warning-color-deprecated-bg: color(~`colorPalette('@{warning-color}', 1) `);
@warning-color-deprecated-border: color(~`colorPalette('@{warning-color}', 3) `);
@error-color-hover: color(~`colorPalette('@{error-color}', 5) `);
@error-color-active: color(~`colorPalette('@{error-color}', 7) `);
@error-color-outline: fade(@error-color, @outline-fade);
@error-color-deprecated-bg: color(~`colorPalette('@{error-color}', 1) `);
@error-color-deprecated-border: color(~`colorPalette('@{error-color}', 3) `);
@highlight-color: @red-5;
@normal-color: #d9d9d9;
@white: #fff;
@black: #000;
@primary-1: color(~`colorPalette('@{primary-color}', 1) `); 
@primary-2: color(~`colorPalette('@{primary-color}', 2) `); 
@primary-3: color(~`colorPalette('@{primary-color}', 3) `); 
@primary-4: color(~`colorPalette('@{primary-color}', 4) `); 
@primary-5: color(
  ~`colorPalette('@{primary-color}', 5) `
); 
@primary-6: @primary-color; 
@primary-7: color(~`colorPalette('@{primary-color}', 7) `); 
@primary-8: color(~`colorPalette('@{primary-color}', 8) `); 
@primary-9: color(~`colorPalette('@{primary-color}', 9) `); 
@primary-10: color(~`colorPalette('@{primary-color}', 10) `); 
@body-background: #fff;
@component-background: #fff;
@popover-background: @component-background;
@popover-customize-border-color: @border-color-split;
@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
@text-color: fade(@black, 85%);
@text-color-secondary: fade(@black, 45%);
@text-color-inverse: @white;
@icon-color: inherit;
@icon-color-hover: fade(@black, 75%);
@heading-color: fade(@black, 85%);
@text-color-dark: fade(@white, 85%);
@text-color-secondary-dark: fade(@white, 65%);
@text-selection-bg: @primary-color;
@font-variant-base: tabular-nums;
@font-feature-settings-base: 'tnum';
@font-size-base: 14px;
@font-size-lg: @font-size-base + 2px;
@font-size-sm: 12px;
@heading-1-size: ceil(@font-size-base * 2.71);
@heading-2-size: ceil(@font-size-base * 2.14);
@heading-3-size: ceil(@font-size-base * 1.71);
@heading-4-size: ceil(@font-size-base * 1.42);
@heading-5-size: ceil(@font-size-base * 1.14);
@line-height-base: 1.5715;
@border-radius-base: 2px;
@border-radius-sm: @border-radius-base;
@padding-lg: 24px; 
@padding-md: 16px; 
@padding-sm: 12px; 
@padding-xs: 8px; 
@padding-xss: 4px; 
@control-padding-horizontal: @padding-sm;
@control-padding-horizontal-sm: @padding-xs;
@margin-lg: 24px; 
@margin-md: 16px; 
@margin-sm: 12px; 
@margin-xs: 8px; 
@margin-xss: 4px; 
@height-base: 32px;
@height-lg: 40px;
@height-sm: 24px;
@item-active-bg: @primary-1;
@item-hover-bg: #f5f5f5;
@iconfont-css-prefix: anticon;
@link-color: @primary-color;
@link-hover-color: color(~`colorPalette('@{link-color}', 5) `);
@link-active-color: color(~`colorPalette('@{link-color}', 7) `);
@link-decoration: none;
@link-hover-decoration: none;
@link-focus-decoration: none;
@link-focus-outline: 0;
@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);
@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);
@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);
@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);
@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);
@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);
@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);
@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);
@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);
@border-color-base: hsv(0, 0, 85%); 
@border-color-split: hsv(0, 0, 94%); 
@border-color-inverse: @white;
@border-width-base: 1px; 
@border-style-base: solid; 
@outline-blur-size: 0;
@outline-width: 2px;
@outline-color: @primary-color; 
@outline-fade: 20%;
@background-color-light: hsv(0, 0, 98%); 
@background-color-base: hsv(0, 0, 96%); 
@disabled-color: fade(#000, 25%);
@disabled-bg: @background-color-base;
@disabled-active-bg: tint(@black, 90%);
@disabled-color-dark: fade(#fff, 35%);
@shadow-color: rgba(0, 0, 0, 0.15);
@shadow-color-inverse: @component-background;
@box-shadow-base: @shadow-2;
@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05),
  0 -12px 48px 16px rgba(0, 0, 0, 0.03);
@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05),
  0 12px 48px 16px rgba(0, 0, 0, 0.03);
@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05),
  -12px 0 48px 16px rgba(0, 0, 0, 0.03);
@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05),
  12px 0 48px 16px rgba(0, 0, 0, 0.03);
@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 9px 28px 8px rgba(0, 0, 0, 0.05);
@btn-font-weight: 400;
@btn-border-radius-base: @border-radius-base;
@btn-border-radius-sm: @border-radius-base;
@btn-border-width: @border-width-base;
@btn-border-style: @border-style-base;
@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
@btn-primary-color: #fff;
@btn-primary-bg: @primary-color;
@btn-default-color: @text-color;
@btn-default-bg: @component-background;
@btn-default-border: @border-color-base;
@btn-danger-color: #fff;
@btn-danger-bg: @error-color;
@btn-danger-border: @error-color;
@btn-disable-color: @disabled-color;
@btn-disable-bg: @disabled-bg;
@btn-disable-border: @border-color-base;
@btn-default-ghost-color: @component-background;
@btn-default-ghost-bg: transparent;
@btn-default-ghost-border: @component-background;
@btn-font-size-lg: @font-size-lg;
@btn-font-size-sm: @font-size-base;
@btn-padding-horizontal-base: @padding-md - 1px;
@btn-padding-horizontal-lg: @btn-padding-horizontal-base;
@btn-padding-horizontal-sm: @padding-xs - 1px;
@btn-height-base: @height-base;
@btn-height-lg: @height-lg;
@btn-height-sm: @height-sm;
@btn-line-height: @line-height-base;
@btn-circle-size: @btn-height-base;
@btn-circle-size-lg: @btn-height-lg;
@btn-circle-size-sm: @btn-height-sm;
@btn-square-size: @btn-height-base;
@btn-square-size-lg: @btn-height-lg;
@btn-square-size-sm: @btn-height-sm;
@btn-square-only-icon-size: @font-size-base + 2px;
@btn-square-only-icon-size-sm: @font-size-base;
@btn-square-only-icon-size-lg: @btn-font-size-lg + 2px;
@btn-group-border: @primary-5;
@btn-link-hover-bg: transparent;
@btn-text-hover-bg: rgba(0, 0, 0, 0.018);
@checkbox-size: 16px;
@checkbox-color: @primary-color;
@checkbox-check-color: #fff;
@checkbox-check-bg: @checkbox-check-color;
@checkbox-border-width: @border-width-base;
@checkbox-border-radius: @border-radius-base;
@checkbox-group-item-margin-right: 8px;
@descriptions-bg: #fafafa;
@descriptions-title-margin-bottom: 20px;
@descriptions-default-padding: @padding-md @padding-lg;
@descriptions-middle-padding: @padding-sm @padding-lg;
@descriptions-small-padding: @padding-xs @padding-md;
@descriptions-item-padding-bottom: @padding-md;
@descriptions-item-trailing-colon: true;
@descriptions-item-label-colon-margin-right: 8px;
@descriptions-item-label-colon-margin-left: 2px;
@descriptions-extra-color: @text-color;
@divider-text-padding: 1em;
@divider-orientation-margin: 5%;
@divider-color: rgba(0, 0, 0, 6%);
@divider-vertical-gutter: 8px;
@dropdown-selected-color: @primary-color;
@dropdown-menu-submenu-disabled-bg: @component-background;
@dropdown-selected-bg: @item-active-bg;
@empty-font-size: @font-size-base;
@radio-size: 16px;
@radio-top: 0.2em;
@radio-border-width: 1px;
@radio-dot-size: @radio-size - 8px;
@radio-dot-color: @primary-color;
@radio-dot-disabled-color: fade(@black, 20%);
@radio-solid-checked-color: @component-background;
@radio-button-bg: @btn-default-bg;
@radio-button-checked-bg: @btn-default-bg;
@radio-button-color: @btn-default-color;
@radio-button-hover-color: @primary-5;
@radio-button-active-color: @primary-7;
@radio-button-padding-horizontal: @padding-md - 1px;
@radio-disabled-button-checked-bg: @disabled-active-bg;
@radio-disabled-button-checked-color: @disabled-color;
@radio-wrapper-margin-right: 8px;
@screen-xs: 480px;
@screen-xs-min: @screen-xs;
@screen-sm: 576px;
@screen-sm-min: @screen-sm;
@screen-md: 768px;
@screen-md-min: @screen-md;
@screen-lg: 992px;
@screen-lg-min: @screen-lg;
@screen-xl: 1200px;
@screen-xl-min: @screen-xl;
@screen-xxl: 1600px;
@screen-xxl-min: @screen-xxl;
@screen-xxxl: 2000px;
@screen-xxxl-min: @screen-xxxl;
@screen-xs-max: (@screen-sm-min - 1px);
@screen-sm-max: (@screen-md-min - 1px);
@screen-md-max: (@screen-lg-min - 1px);
@screen-lg-max: (@screen-xl-min - 1px);
@screen-xl-max: (@screen-xxl-min - 1px);
@screen-xxl-max: (@screen-xxxl-min - 1px);
@grid-columns: 24;
@layout-header-height: 64px;
@layout-header-padding: 0 50px;
@layout-header-color: @text-color;
@layout-footer-padding: 24px 50px;
@layout-footer-background: @layout-body-background;
@layout-sider-background: @layout-header-background;
@layout-trigger-height: 48px;
@layout-trigger-background: #002140;
@layout-trigger-color: #fff;
@layout-zero-trigger-width: 36px;
@layout-zero-trigger-height: 42px;
@layout-sider-background-light: #fff;
@layout-trigger-background-light: #fff;
@layout-trigger-color-light: @text-color;
@zindex-badge: auto;
@zindex-table-fixed: 2;
@zindex-affix: 10;
@zindex-back-top: 10;
@zindex-picker-panel: 10;
@zindex-popup-close: 10;
@zindex-modal: 1000;
@zindex-modal-mask: 1000;
@zindex-message: 1010;
@zindex-notification: 1010;
@zindex-popover: 1030;
@zindex-dropdown: 1050;
@zindex-picker: 1050;
@zindex-popoconfirm: 1060;
@zindex-tooltip: 1070;
@zindex-image: 1080;
@animation-duration-slow: 0.3s; 
@animation-duration-base: 0.2s;
@animation-duration-fast: 0.1s; 
@collapse-panel-border-radius: @border-radius-base;
@dropdown-menu-bg: @component-background;
@dropdown-vertical-padding: 5px;
@dropdown-edge-child-vertical-padding: 4px;
@dropdown-font-size: @font-size-base;
@dropdown-line-height: 22px;
@label-required-color: @highlight-color;
@label-color: @heading-color;
@form-warning-input-bg: @input-bg;
@form-item-margin-bottom: 24px;
@form-item-trailing-colon: true;
@form-vertical-label-padding: 0 0 8px;
@form-vertical-label-margin: 0;
@form-item-label-font-size: @font-size-base;
@form-item-label-height: @input-height-base;
@form-item-label-colon-margin-right: 8px;
@form-item-label-colon-margin-left: 2px;
@form-error-input-bg: @input-bg;
@input-height-base: @height-base;
@input-height-lg: @height-lg;
@input-height-sm: @height-sm;
@input-padding-horizontal: @control-padding-horizontal - 1px;
@input-padding-horizontal-base: @input-padding-horizontal;
@input-padding-horizontal-sm: @control-padding-horizontal-sm - 1px;
@input-padding-horizontal-lg: @input-padding-horizontal;
@input-padding-vertical-base: max(
  (round(((@input-height-base - @font-size-base * @line-height-base) / 2) * 10) / 10) -
    @border-width-base,
  3px
);
@input-padding-vertical-sm: max(
  (round(((@input-height-sm - @font-size-base * @line-height-base) / 2) * 10) / 10) -
    @border-width-base,
  0
);
@input-padding-vertical-lg: (
    ceil(((@input-height-lg - @font-size-lg * @line-height-base) / 2) * 10) / 10
  ) - @border-width-base;
@input-placeholder-color: hsv(0, 0, 75%);
@input-color: @text-color;
@input-icon-color: @input-color;
@input-border-color: @border-color-base;
@input-bg: @component-background;
@input-number-hover-border-color: @input-hover-border-color;
@input-number-handler-active-bg: #f4f4f4;
@input-number-handler-hover-bg: @primary-5;
@input-number-handler-bg: @component-background;
@input-number-handler-border-color: @border-color-base;
@input-addon-bg: @background-color-light;
@input-hover-border-color: @primary-5;
@input-disabled-bg: @disabled-bg;
@input-outline-offset: 0 0;
@input-icon-hover-color: fade(@black, 85%);
@input-disabled-color: @disabled-color;
@mentions-dropdown-bg: @component-background;
@mentions-dropdown-menu-item-hover-bg: @mentions-dropdown-bg;
@select-border-color: @border-color-base;
@select-item-selected-color: @text-color;
@select-item-selected-font-weight: 600;
@select-dropdown-bg: @component-background;
@select-item-selected-bg: @primary-1;
@select-item-active-bg: @item-hover-bg;
@select-dropdown-vertical-padding: @dropdown-vertical-padding;
@select-dropdown-font-size: @dropdown-font-size;
@select-dropdown-line-height: @dropdown-line-height;
@select-dropdown-height: 32px;
@select-background: @component-background;
@select-clear-background: @select-background;
@select-selection-item-bg: @background-color-base;
@select-selection-item-border-color: @border-color-split;
@select-single-item-height-lg: 40px;
@select-multiple-item-height: @input-height-base - @input-padding-vertical-base * 2; 
@select-multiple-item-height-lg: 32px;
@select-multiple-item-spacing-half: ceil((@input-padding-vertical-base / 2));
@select-multiple-disabled-background: @input-disabled-bg;
@select-multiple-item-disabled-color: #bfbfbf;
@select-multiple-item-disabled-border-color: @select-border-color;
@cascader-bg: @component-background;
@cascader-item-selected-bg: @primary-1;
@cascader-menu-bg: @component-background;
@cascader-menu-border-color-split: @border-color-split;
@cascader-dropdown-vertical-padding: @dropdown-vertical-padding;
@cascader-dropdown-edge-child-vertical-padding: @dropdown-edge-child-vertical-padding;
@cascader-dropdown-font-size: @dropdown-font-size;
@cascader-dropdown-line-height: @dropdown-line-height;
@anchor-bg: transparent;
@anchor-border-color: @border-color-split;
@anchor-link-top: 7px;
@anchor-link-left: 16px;
@anchor-link-padding: @anchor-link-top 0 @anchor-link-top @anchor-link-left;
@tooltip-max-width: 250px;
@tooltip-color: #fff;
@tooltip-bg: rgba(0, 0, 0, 0.75);
@tooltip-arrow-width: 5px;
@tooltip-distance: @tooltip-arrow-width - 1px + 4px;
@tooltip-arrow-color: @tooltip-bg;
@popover-bg: @component-background;
@popover-color: @text-color;
@popover-min-width: 177px;
@popover-min-height: 32px;
@popover-arrow-width: 6px;
@popover-arrow-color: @popover-bg;
@popover-arrow-outer-color: @popover-bg;
@popover-distance: @popover-arrow-width + 4px;
@popover-padding-horizontal: @padding-md;
@modal-header-padding-vertical: @padding-md;
@modal-header-padding-horizontal: @padding-lg;
@modal-body-padding: @padding-lg;
@modal-header-bg: @component-background;
@modal-header-padding: @modal-header-padding-vertical @modal-header-padding-horizontal;
@modal-header-border-width: @border-width-base;
@modal-header-border-style: @border-style-base;
@modal-header-title-line-height: 22px;
@modal-header-title-font-size: @font-size-lg;
@modal-header-border-color-split: @border-color-split;
@modal-header-close-size: 56px;
@modal-content-bg: @component-background;
@modal-heading-color: @heading-color;
@modal-close-color: @text-color-secondary;
@modal-footer-bg: transparent;
@modal-footer-border-color-split: @border-color-split;
@modal-footer-border-style: @border-style-base;
@modal-footer-padding-vertical: 10px;
@modal-footer-padding-horizontal: 16px;
@modal-footer-border-width: @border-width-base;
@modal-mask-bg: fade(@black, 45%);
@modal-confirm-body-padding: 32px 32px 24px;
@modal-confirm-title-font-size: @font-size-lg;
@progress-default-color: @processing-color;
@progress-remaining-color: @background-color-base;
@progress-info-text-color: @progress-text-color;
@progress-radius: 100px;
@progress-steps-item-bg: #f3f3f3;
@progress-text-font-size: 1em;
@progress-text-color: @text-color; 
@progress-circle-text-font-size: 1em;
@menu-inline-toplevel-item-height: 40px;
@menu-item-height: 40px;
@menu-item-group-height: @line-height-base;
@menu-collapsed-width: 80px;
@menu-bg: @component-background;
@menu-popup-bg: @component-background;
@menu-inline-submenu-bg: @background-color-light;
@menu-highlight-color: @primary-color;
@menu-highlight-danger-color: @error-color;
@menu-item-active-danger-bg: @red-1;
@menu-item-active-border-width: 3px;
@menu-item-group-title-color: @text-color-secondary;
@menu-item-vertical-margin: 4px;
@menu-item-font-size: @font-size-base;
@menu-item-boundary-margin: 8px;
@menu-item-padding-horizontal: 20px;
@menu-item-padding: 0 @menu-item-padding-horizontal;
@menu-horizontal-line-height: 46px;
@menu-icon-margin-right: 10px;
@menu-icon-size: @menu-item-font-size;
@menu-icon-size-lg: @font-size-lg;
@menu-item-group-title-font-size: @menu-item-font-size;
@menu-dark-color: @text-color-secondary-dark;
@menu-dark-danger-color: @error-color;
@menu-dark-bg: @layout-header-background;
@menu-dark-arrow-color: #fff;
@menu-dark-inline-submenu-bg: #000c17;
@menu-dark-highlight-color: #fff;
@menu-dark-item-active-bg: @primary-color;
@menu-dark-item-active-danger-bg: @error-color;
@menu-dark-selected-item-icon-color: @white;
@menu-dark-selected-item-text-color: @white;
@menu-dark-item-hover-bg: transparent;
@spin-dot-size-sm: 14px;
@spin-dot-size: 20px;
@spin-dot-size-lg: 32px;
@table-bg: @component-background;
@table-header-color: @heading-color;
@table-header-sort-bg: @background-color-base;
@table-body-sort-bg: #fafafa;
@table-selected-row-color: inherit;
@table-body-selected-sort-bg: @table-selected-row-bg;
@table-selected-row-hover-bg: darken(@table-selected-row-bg, 2%);
@table-expanded-row-bg: #fbfbfb;
@table-padding-vertical: 16px;
@table-padding-horizontal: 16px;
@table-padding-vertical-md: (@table-padding-vertical * 3 / 4);
@table-padding-horizontal-md: (@table-padding-horizontal / 2);
@table-padding-vertical-sm: (@table-padding-vertical / 2);
@table-padding-horizontal-sm: (@table-padding-horizontal / 2);
@table-border-radius-base: @border-radius-base;
@table-footer-bg: @background-color-light;
@table-footer-color: @heading-color;
@table-header-bg-sm: @table-header-bg;
@table-font-size: @font-size-base;
@table-font-size-md: @table-font-size;
@table-font-size-sm: @table-font-size;
@table-header-cell-split-color: rgba(0, 0, 0, 0.06);
@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);
@table-fixed-header-sort-active-bg: hsv(0, 0, 96%);
@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);
@table-filter-btns-bg: inherit;
@table-filter-dropdown-bg: @component-background;
@table-expand-icon-bg: @component-background;
@table-selection-column-width: 32px;
@table-sticky-scroll-bar-bg: fade(#000, 35%);
@table-sticky-scroll-bar-radius: 4px;
@tag-default-bg: @background-color-light;
@tag-font-size: @font-size-sm;
@tag-line-height: 20px;
@picker-bg: @component-background;
@picker-basic-cell-hover-color: @item-hover-bg;
@picker-basic-cell-active-with-range-color: @primary-1;
@picker-basic-cell-hover-with-range-color: lighten(@primary-color, 35%);
@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);
@picker-border-color: @border-color-split;
@picker-date-hover-range-border-color: lighten(@primary-color, 20%);
@picker-date-hover-range-color: @picker-basic-cell-hover-with-range-color;
@picker-time-panel-column-width: 56px;
@picker-time-panel-column-height: 224px;
@picker-time-panel-cell-height: 28px;
@picker-panel-cell-height: 24px;
@picker-panel-cell-width: 36px;
@picker-text-height: 40px;
@picker-panel-without-time-cell-height: 66px;
@calendar-bg: @component-background;
@calendar-input-bg: @input-bg;
@calendar-border-color: @border-color-inverse;
@calendar-item-active-bg: @item-active-bg;
@calendar-column-active-bg: fade(@calendar-item-active-bg, 20%);
@calendar-full-bg: @calendar-bg;
@calendar-full-panel-bg: @calendar-full-bg;
@carousel-dot-width: 16px;
@carousel-dot-height: 3px;
@carousel-dot-active-width: 24px;
@badge-height: 20px;
@badge-height-sm: 14px;
@badge-dot-size: 6px;
@badge-font-size: @font-size-sm;
@badge-font-size-sm: @font-size-sm;
@badge-font-weight: normal;
@badge-status-size: 6px;
@badge-text-color: @component-background;
@badge-color: @highlight-color;
@rate-star-color: @yellow-6;
@rate-star-bg: @border-color-split;
@rate-star-size: 20px;
@rate-star-hover-scale: scale(1.1);
@card-head-color: @heading-color;
@card-head-background: transparent;
@card-head-font-size: @font-size-lg;
@card-head-font-size-sm: @font-size-base;
@card-head-padding: 16px;
@card-head-padding-sm: (@card-head-padding / 2);
@card-head-height: 48px;
@card-head-height-sm: 36px;
@card-inner-head-padding: 12px;
@card-padding-base: 24px;
@card-padding-base-sm: (@card-padding-base / 2);
@card-actions-background: @component-background;
@card-actions-li-margin: 12px 0;
@card-skeleton-bg: #cfd8dc;
@card-background: @component-background;
@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12),
  0 5px 12px 4px rgba(0, 0, 0, 0.09);
@card-radius: @border-radius-base;
@card-head-tabs-margin-bottom: -17px;
@card-head-extra-color: @text-color;
@comment-bg: inherit;
@comment-padding-base: @padding-md 0;
@comment-nest-indent: 44px;
@comment-font-size-base: @font-size-base;
@comment-font-size-sm: @font-size-sm;
@comment-author-name-color: @text-color-secondary;
@comment-author-time-color: #ccc;
@comment-action-color: @text-color-secondary;
@comment-action-hover-color: #595959;
@comment-actions-margin-bottom: inherit;
@comment-actions-margin-top: @margin-sm;
@comment-content-detail-p-margin-bottom: inherit;
@tabs-card-head-background: @background-color-light;
@tabs-card-height: 40px;
@tabs-card-active-color: @primary-color;
@tabs-card-horizontal-padding: (
    (@tabs-card-height - floor(@font-size-base * @line-height-base)) / 2
  ) - @border-width-base @padding-md;
@tabs-card-horizontal-padding-sm: 6px @padding-md;
@tabs-card-horizontal-padding-lg: 7px @padding-md 6px;
@tabs-title-font-size: @font-size-base;
@tabs-title-font-size-lg: @font-size-lg;
@tabs-title-font-size-sm: @font-size-base;
@tabs-ink-bar-color: @primary-color;
@tabs-bar-margin: 0 0 @margin-md 0;
@tabs-horizontal-gutter: 32px;
@tabs-horizontal-margin: 0 0 0 @tabs-horizontal-gutter;
@tabs-horizontal-margin-rtl: 0 0 0 32px;
@tabs-horizontal-padding: @padding-sm 0;
@tabs-horizontal-padding-lg: @padding-md 0;
@tabs-horizontal-padding-sm: @padding-xs 0;
@tabs-vertical-padding: @padding-xs @padding-lg;
@tabs-vertical-margin: @margin-md 0 0 0;
@tabs-scrolling-size: 32px;
@tabs-highlight-color: @primary-color;
@tabs-hover-color: @primary-5;
@tabs-active-color: @primary-7;
@tabs-card-gutter: 2px;
@tabs-card-tab-active-border-top: 2px solid transparent;
@back-top-color: #fff;
@back-top-bg: @text-color-secondary;
@back-top-hover-bg: @text-color;
@avatar-size-base: 32px;
@avatar-size-lg: 40px;
@avatar-size-sm: 24px;
@avatar-font-size-base: 18px;
@avatar-font-size-lg: 24px;
@avatar-font-size-sm: 14px;
@avatar-bg: #ccc;
@avatar-color: #fff;
@avatar-border-radius: @border-radius-base;
@avatar-group-overlapping: -8px;
@avatar-group-space: 3px;
@avatar-group-border-color: #fff;
@switch-height: 22px;
@switch-sm-height: 16px;
@switch-min-width: 44px;
@switch-sm-min-width: 28px;
@switch-disabled-opacity: 0.4;
@switch-color: @primary-color;
@switch-bg: @component-background;
@switch-shadow-color: fade(#00230b, 20%);
@switch-padding: 2px;
@switch-inner-margin-min: ceil(@switch-height * 0.3);
@switch-inner-margin-max: ceil(@switch-height * 1.1);
@switch-sm-inner-margin-min: ceil(@switch-sm-height * 0.3);
@switch-sm-inner-margin-max: ceil(@switch-sm-height * 1.1);
@pagination-item-bg: @component-background;
@pagination-item-size: @height-base;
@pagination-item-size-sm: 24px;
@pagination-font-family: @font-family;
@pagination-font-weight-active: 500;
@pagination-item-bg-active: @component-background;
@pagination-item-link-bg: @component-background;
@pagination-item-disabled-color-active: @disabled-color;
@pagination-item-disabled-bg-active: @disabled-active-bg;
@pagination-item-input-bg: @component-background;
@pagination-mini-options-size-changer-top: 0px;
@page-header-padding: @padding-lg;
@page-header-padding-vertical: @padding-md;
@page-header-padding-breadcrumb: @padding-sm;
@page-header-content-padding-vertical: @padding-sm;
@page-header-back-color: #000;
@page-header-ghost-bg: inherit;
@page-header-heading-title: @heading-4-size;
@page-header-heading-sub-title: 14px;
@page-header-tabs-tab-font-size: 16px;
@breadcrumb-base-color: @text-color-secondary;
@breadcrumb-last-item-color: @text-color;
@breadcrumb-font-size: @font-size-base;
@breadcrumb-icon-font-size: @font-size-base;
@breadcrumb-link-color: @text-color-secondary;
@breadcrumb-link-color-hover: @primary-5;
@breadcrumb-separator-color: @text-color-secondary;
@breadcrumb-separator-margin: 0 @padding-xs;
@slider-margin: 10px 6px 10px;
@slider-rail-background-color: @background-color-base;
@slider-rail-background-color-hover: #e1e1e1;
@slider-track-background-color: @primary-3;
@slider-track-background-color-hover: @primary-4;
@slider-handle-border-width: 2px;
@slider-handle-background-color: @component-background;
@slider-handle-color: @primary-3;
@slider-handle-color-hover: @primary-4;
@slider-handle-color-focus: tint(@primary-color, 20%);
@slider-handle-color-focus-shadow: fade(@primary-color, 12%);
@slider-handle-color-tooltip-open: @primary-color;
@slider-handle-size: 14px;
@slider-handle-margin-top: -5px;
@slider-handle-shadow: 0;
@slider-dot-border-color: @border-color-split;
@slider-dot-border-color-active: tint(@primary-color, 50%);
@slider-disabled-color: @disabled-color;
@slider-disabled-background-color: @component-background;
@tree-bg: @component-background;
@tree-title-height: 24px;
@tree-child-padding: 18px;
@tree-directory-selected-color: #fff;
@tree-directory-selected-bg: @primary-color;
@tree-node-hover-bg: @item-hover-bg;
@tree-node-selected-bg: @primary-2;
@collapse-header-padding: @padding-sm @padding-md;
@collapse-header-padding-extra: 40px;
@collapse-header-bg: @background-color-light;
@collapse-content-padding: @padding-md;
@collapse-content-bg: @component-background;
@collapse-header-arrow-left: 16px;
@skeleton-color: rgba(190, 190, 190, 0.2);
@skeleton-to-color: shade(@skeleton-color, 5%);
@skeleton-paragraph-margin-top: 28px;
@skeleton-paragraph-li-margin-top: @margin-md;
@skeleton-paragraph-li-height: 16px;
@skeleton-title-height: 16px;
@skeleton-title-paragraph-margin-top: @margin-lg;
@transfer-header-height: 40px;
@transfer-item-height: @height-base;
@transfer-disabled-bg: @disabled-bg;
@transfer-list-height: 200px;
@transfer-item-hover-bg: @item-hover-bg;
@transfer-item-selected-hover-bg: darken(@item-active-bg, 2%);
@transfer-item-padding-vertical: 6px;
@transfer-list-search-icon-top: 12px;
@message-notice-content-padding: 10px 16px;
@message-notice-content-bg: @component-background;
@wave-animation-width: 6px;
@alert-success-border-color: ~`colorPalette('@{success-color}', 3) `;
@alert-success-bg-color: ~`colorPalette('@{success-color}', 1) `;
@alert-success-icon-color: @success-color;
@alert-info-border-color: ~`colorPalette('@{info-color}', 3) `;
@alert-info-bg-color: ~`colorPalette('@{info-color}', 1) `;
@alert-info-icon-color: @info-color;
@alert-warning-border-color: ~`colorPalette('@{warning-color}', 3) `;
@alert-warning-bg-color: ~`colorPalette('@{warning-color}', 1) `;
@alert-warning-icon-color: @warning-color;
@alert-error-border-color: ~`colorPalette('@{error-color}', 3) `;
@alert-error-bg-color: ~`colorPalette('@{error-color}', 1) `;
@alert-error-icon-color: @error-color;
@alert-message-color: @heading-color;
@alert-text-color: @text-color;
@alert-close-color: @text-color-secondary;
@alert-close-hover-color: @icon-color-hover;
@alert-no-icon-padding-vertical: @padding-xs;
@alert-with-description-no-icon-padding-vertical: @padding-md - 1px;
@alert-with-description-padding-vertical: @padding-md - 1px;
@alert-with-description-padding: @alert-with-description-padding-vertical 15px
  @alert-with-description-no-icon-padding-vertical @alert-with-description-icon-size;
@alert-icon-top: 8px + @font-size-base * (@line-height-base / 2) - (@font-size-base / 2);
@alert-with-description-icon-size: 24px;
@list-header-background: transparent;
@list-footer-background: transparent;
@list-empty-text-padding: @padding-md;
@list-item-padding: @padding-sm 0;
@list-item-padding-sm: @padding-xs @padding-md;
@list-item-padding-lg: 16px 24px;
@list-item-meta-margin-bottom: @padding-md;
@list-item-meta-avatar-margin-right: @padding-md;
@list-item-meta-title-margin-bottom: @padding-sm;
@list-customize-card-bg: @component-background;
@list-item-meta-description-font-size: @font-size-base;
@statistic-title-font-size: @font-size-base;
@statistic-content-font-size: 24px;
@statistic-unit-font-size: 24px;
@statistic-font-family: @font-family;
@drawer-header-padding: @padding-md @padding-lg;
@drawer-body-padding: @padding-lg;
@drawer-bg: @component-background;
@drawer-footer-padding-vertical: @modal-footer-padding-vertical;
@drawer-footer-padding-horizontal: @modal-footer-padding-horizontal;
@drawer-header-close-size: 56px;
@drawer-title-font-size: @font-size-lg;
@drawer-title-line-height: 22px;
@timeline-width: 2px;
@timeline-color: @border-color-split;
@timeline-dot-border-width: 2px;
@timeline-dot-color: @primary-color;
@timeline-dot-bg: @component-background;
@timeline-item-padding-bottom: 20px;
@typography-title-font-weight: 600;
@typography-title-margin-top: 1.2em;
@typography-title-margin-bottom: 0.5em;
@upload-actions-color: @text-color-secondary;
@process-tail-color: @border-color-split;
@steps-nav-arrow-color: fade(@black, 25%);
@steps-background: @component-background;
@steps-icon-size: 32px;
@steps-icon-custom-size: @steps-icon-size;
@steps-icon-custom-top: 0px;
@steps-icon-custom-font-size: 24px;
@steps-icon-top: -0.5px;
@steps-icon-font-size: @font-size-lg;
@steps-icon-margin: 0 8px 0 0;
@steps-title-line-height: @height-base;
@steps-small-icon-size: 24px;
@steps-small-icon-margin: 0 8px 0 0;
@steps-dot-size: 8px;
@steps-dot-top: 2px;
@steps-current-dot-size: 10px;
@steps-description-max-width: 140px;
@steps-nav-content-max-width: auto;
@steps-vertical-icon-width: 16px;
@steps-vertical-tail-width: 16px;
@steps-vertical-tail-width-sm: 12px;
@notification-bg: @component-background;
@notification-padding-vertical: 16px;
@notification-padding-horizontal: 24px;
@result-title-font-size: 24px;
@result-subtitle-font-size: @font-size-base;
@result-icon-font-size: 72px;
@result-extra-margin: 24px 0 0 0;
@image-size-base: 48px;
@image-font-size-base: 24px;
@image-bg: #f5f5f5;
@image-color: #fff;
@image-mask-font-size: 16px;
@image-preview-operation-size: 18px;
@image-preview-operation-color: @text-color-dark;
@image-preview-operation-disabled-color: fade(@image-preview-operation-color, 25%);
