import { TranslatorOption, useLocale } from 'youibot-plus'
import type { IFormRules } from 'youibot-plus'

const COMMONMESSAGE = 'message.userSetting.nameTip'
const POSITIVEINTEGER = '请输入正整数'
const translateMsg = (msg: string, args?: TranslatorOption) => {
  const { t } = useLocale()
  return t(msg, args)
}

export const isRequire = (message?: string) => {
  return {
    required: true,
    message: message ? translateMsg(message) : translateMsg(COMMONMESSAGE)
  }
}
export const isPositiveInteger = (message?: string) => {
  return {
    required: true,
    pattern: /^[0-9]+$/,
    message: message ? translateMsg(message) : translateMsg(POSITIVEINTEGER)
  }
}
//只能为数字及中英文
export const isName = (message?: string) => {
  return {
    pattern: /^[A-Za-z_]*(?:(?:\d+(\.\d+)?|-?\d+)[A-Za-z_]*)?$/,
    message: message ? translateMsg(message) : translateMsg('message.formRules.isName')
  }
}

// 中文、英文、数字 20
export const isUserName = (message?: string) => {
  return {
    required: true,
    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{0,20}$/,
    message: message ? translateMsg(message) : translateMsg('message.userSetting.roleName')
  }
}

export const isIp = (message?: string) => {
  return {
    required: true,
    pattern:
      /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)$/,
    message: message ? translateMsg(message) : translateMsg('message.formRules.ip')
  }
}

export const isNotSpecial = (message?: string) => {
  return {
    required: true,
    pattern: "[^`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]",
    message: message ? translateMsg(message) : translateMsg(COMMONMESSAGE)
  }
}

export const isPhone = (message?: string) => {
  return {
    pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
    message: message ? translateMsg(message) : translateMsg('message.formRules.phoneLen')
  }
}

export const isEmail = (message?: string) => {
  return {
    required: true,
    pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
    message: message ? translateMsg(message) : translateMsg(COMMONMESSAGE)
  }
}

export const isLength = (min?: number, max?: number, message?: string) => {
  return {
    min: min ? min : 0,
    max: max ? max : 1,
    message: message ? translateMsg(message) : translateMsg('message.formRules.isLength', { max: max ? max : 20 })
  }
}

// 端口
export const isPort = (message?: string) => {
  return {
    pattern: /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
    message: message ? translateMsg(message) : translateMsg('message.formRules.port')
  }
}
//`仅支持英文（首字符）、下划线、数字
export const englishFirst = (message?: string) => {
  return {
    pattern: /^[a-zA-Z]([a-zA-Z0-9_]+)?$/,
    message: message ? translateMsg(message) : translateMsg('message.formRules.englishFirst_')
  }
}
//`仅支持英文（首字符）、数字
export const englishNumbers = (message?: string) => {
  return {
    pattern: /^[a-zA-Z]([a-zA-Z0-9_]+)?$/,
    message: message ? translateMsg(message) : translateMsg('message.formRules.englishFirst')
  }
}
//`仅支持英文
export const english = (message?: string) => {
  return {
    pattern: /^[a-zA-Z]{0,}$/,
    message: message ? translateMsg(message) : translateMsg('message.formRules.english')
  }
}
