<template>
  <a-layout-header class="nav-view">
    <!-- <a-layout-sider :class="{ collapsed: isCollapsed }" v-model="isCollapsed" collapsible :trigger="null"> -->
    <div class="header aside-view" :class="{ collapsed: isCollapsed }">
      <div class="header-background"><img class="header-logo" :width="20" :src="logo" /></div>

      <span v-show="!isCollapsed" class="header-title">{{ sysName }}</span>
    </div>
    <!-- </a-layout-sider> -->
    <ul class="header-middle">
      <template v-for="item in routeList" :key="item.path">
        <li :class="{ 'is-active': isCurrentRoute(item) }" @click="handleMenu(item)">
          <i :class="`header-middle-icon iconfont ${item.meta.icon}`"></i>
          <span class="header-middle-name">{{ t(item.meta.title) }}</span>
        </li>
      </template>
    </ul>
    <div v-if="srviceExceptionTip" class="err">{{ t('message.systemServiceException') }}</div>

    <section class="header-right">
      <div v-if="isLicense()" class="licence">{{ isLicense() }}</div>
      <!-- <div>
        <i class="iconfont icon-a-xiaoxitixing_huaban1fuben3"></i>
        <span>{{ t('message.message') }}</span>
      </div> -->
      <!-- <a-popover placement="bottom" :zIndex="2005" overlayClassName="pop-header-right">
        <template #content>
          <span>{{ t('message.individuation') }}</span>
        </template>
        <i class="iconfont icon-shezhi1" @click="settings.setVisible(true)"></i>
      </a-popover> -->
      <UserSetting popDropClass="pop-header-right-drop"></UserSetting>
    </section>
  </a-layout-header>
</template>

<script setup lang="ts">
import logo from '@/assets/images/logo.png'
import { useLocale } from 'youibot-plus'
import useStore from '@/stores'
import { routeList, RouterType, getRoute, toNext, beforeRouteUpdate } from '@/router'
import UserSetting from './user-setting.vue'

const { t } = useLocale()

const sysName = computed(() => {
  return 'YOUISIMULATION'
})
const { navStore, settings, srviceExceptionTipStore } = useStore()
const licenseData = ref()
let isInquire = ref(false)
let isCollapsed = ref(navStore.getIsCollapsed)
navStore.$subscribe((mutation, state) => {
  isCollapsed.value = state.isCollapsed
})
const srviceExceptionTip = ref(false)
let timr: NodeJS.Timeout | null = null
srviceExceptionTipStore.$subscribe((mutation, state) => {
  if (!state.show && !timr) {
    timr = setTimeout(() => {
      timr = null
      srviceExceptionTip.value = state.show
    }, 5000)
  } else if (state.show) {
    srviceExceptionTip.value = state.show
  }
})

const isLicense = () => {
  if (!isInquire.value) {
    return ''
  }
  if (licenseData.value?.type === 1) {
    if (licenseData.value?.remainingDays <= 30) {
      // 许可证有效期天数
      return `${t('message.licence.licenseRemaining')}${licenseData.value?.remainingDays}${t('message.day')}`
    }
    return ''
  } else if (licenseData.value?.type === 2) {
    // 许可证未生效
    return t('message.licence.licenseNotInForce')
  } else if (licenseData.value?.type === 0) {
    // 许可证已过期
    return t('message.licence.licenseHasExpired')
  } else {
    // 缺少许可证
    return t('message.licence.lackOfLicense')
  }
}
const isCurrentRoute = (menu: RouterType): boolean => {
  const fullPath = getRoute().fullPath
  if (menu.path === '/Index' && fullPath.indexOf('/monitoring/agv-map') > -1) {
    return true
  }
  return fullPath.indexOf(menu.path) > -1
}
const handleMenu = (menu: RouterType): void => {
  toNext({ path: menu.path })
}
beforeRouteUpdate(to => {})
onMounted(() => {
  isInquire.value = false
})
</script>

<style lang="less" scoped>
#components-layout-demo-custom-trigger .trigger {
  transform: scaleX(0.5);
  transform-origin: left;
  cursor: pointer;
}

.nav-view {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: @head_height;
  color: @head-color;
  background-color: @head-background;

  // 顶部左侧模块
  .header {
    position: relative;
    display: flex;
    align-items: center;
    width: @aside-width;
    height: @head_height;
    overflow: hidden;
    background-color: @head-background;
    transition: width 0.3s;

    &-title {
      display: inline-block;
      box-sizing: border-box;
      width: 150px;
      height: 100%;
      margin-left: 4px;
      overflow: hidden;
      color: @head-color;
      font-weight: 700;
      font-size: @head-logo-fontSize;
      line-height: @head_height;
      white-space: nowrap;
      text-align: center;
      text-overflow: ellipsis;
      font-feature-settings: 'tnum' on, 'lnum' on;
    }

    &-background {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      margin-left: 20px;

      &-logo {
        width: 24px;
      }
    }

    &.collapsed {
      padding-right: 16px;
    }
  }

  // 顶部中模块
  .header-middle {
    z-index: 10px;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 48px;
    margin-bottom: 0;
    text-align: center;

    &-icon {
      font-size: @head-icon-fontSize;
    }

    &-name {
      padding-left: 8px;
      color: @head-color;
      font-size: @head-menu-fontSize;
      vertical-align: top;
    }

    &-border {
      display: none;
    }

    li {
      height: @head_height;
      // margin-left: @head-menu-item-margin-right;
      padding: 0 34px;
      line-height: @head_height;
      white-space: nowrap;

      &.is-active {
        position: relative;
        background-color: @font-white6;
        background-color: @primary-color;
        // color: @primary-color;
        cursor: pointer;

        .header-middle-name,
        .header-middle-icon {
          color: @head-color !important;
        }
      }

      &:hover {
        cursor: pointer;

        .header-middle-name,
        .header-middle-icon {
          color: @primary-color;
        }
      }
    }
  }

  // 顶部右侧
  .header-right {
    z-index: 15;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    height: 48px;
    margin-right: 20px;
    line-height: @head_height;

    .licence {
      color: #ee742a;
      cursor: inherit !important;
    }

    div {
      display: flex;
      align-items: center;
      margin-right: 14px;

      i {
        margin-right: 4px;
        font-size: 20px;
      }

      span {
        font-size: 14px;
      }

      &:hover {
        color: #ee742a;
        cursor: pointer;
      }
    }

    .icon-shezhi1 {
      margin-left: 14px;
      padding-right: 4px;
      font-size: 18px;

      &:hover {
        color: #ee742a;
      }
    }

    div:last-child {
      margin-right: 0;
    }
  }
}

.err {
  flex: 0.6;
  color: #ee742a;
}

.ant-layout-header {
  padding: 0;
}

&.collapsed {
  width: @collapsed_width !important;
}
</style>
